﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using DevExpress.XtraEditors.Controls;
using MasterCom.Util;
using System.Xml;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class DIYInjectionGridQueryMultiMapSettingDlg : BaseDialog
    {
        string streetTableName;
        Dictionary<string, CheckedListBoxItem> checkedListBoxItemDic = new Dictionary<string, CheckedListBoxItem>();
        protected string layerSettingErrMsg = "请选择渗透图层。";

        public DIYInjectionGridQueryMultiMapSettingDlg()
        {
            InitializeComponent();
            mainModel = MainModel.GetInstance();
            loadConfig();
            init();
            mainModel.DistrictChanged += new EventHandler(districtChanged);
            Disposed += new EventHandler(disposed);
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                Dictionary<string, List<object>> streetInjectTables = new Dictionary<string, List<object>>();
                foreach (int district in mainModel.StreetInjectTablesDic.Keys)
                {
                    List<object> streetList = new List<object>();
                    foreach (StreetInjectTableInfo streetInfo in mainModel.StreetInjectTablesDic[district])
                    {
                        streetList.Add(streetInfo.Param);
                    }
                    streetInjectTables[district.ToString()] = streetList;
                }
                param["StreetInjectTables"] = streetInjectTables;
                return param;
            }
            set
            {
                mainModel.StreetInjectTablesDic.Clear();
                try
                {
                    Dictionary<string, object> streetInjectTables = (Dictionary<string, object>)value["StreetInjectTables"];
                    foreach (string sDistrict in streetInjectTables.Keys)
                    {
                        int district = int.Parse(sDistrict);
                        List<object> streets = (List<object>)streetInjectTables[sDistrict];
                        foreach (object objStreet in streets)
                        {
                            StreetInjectTableInfo streetInfo = new StreetInjectTableInfo();
                            streetInfo.Param = (Dictionary<string, object>)objStreet;
                            if (!mainModel.StreetInjectTablesDic.ContainsKey(district))
                            {
                                List<StreetInjectTableInfo> streetInfoList = new List<StreetInjectTableInfo>();
                                mainModel.StreetInjectTablesDic[district] = streetInfoList;
                            }
                            mainModel.StreetInjectTablesDic[district].Add(streetInfo);
                        }
                    }
                }
                catch
                {
                    //continue
                }
            }
        }

        private void districtChanged(object sender, EventArgs e)
        {
            init();
        }

        private void disposed(object sender, EventArgs e)
        {
            mainModel.DistrictChanged -= new EventHandler(districtChanged);
        }

        private void init()
        {
            checkedListBoxItemDic.Clear();
            clbMap.Items.Clear();
            if (mainModel.StreetInjectTablesDic.ContainsKey(MainModel.GetInstance().DistrictID))
            {
                List<StreetInjectTableInfo> streetList = mainModel.StreetInjectTablesDic[MainModel.GetInstance().DistrictID];
                foreach (StreetInjectTableInfo streetInfo in streetList)
                {
                    if (checkMap(streetInfo.FilePath))
                    {
                        CheckedListBoxItem item = new CheckedListBoxItem();
                        item.Description = streetInfo.FileName;
                        item.CheckState = CheckState.Checked;
                        item.Value = streetInfo;
                        clbMap.Items.Add(item);
                        checkedListBoxItemDic[streetInfo.FileName] = item;
                    }
                }
            }
        }

        private void clbMap_Enter(object sender, EventArgs e)
        {
            btnDelete.Enabled = clbMap.SelectedIndex >= 0;
        }

        private void clbMap_Leave(object sender, EventArgs e)
        {
            btnDelete.Enabled = clbMap.SelectedIndex >= 0;
        }

        private void edtMap_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Multiselect = false;
            openFileDialog.CheckFileExists = true;
            openFileDialog.DefaultExt = "SHP";
            openFileDialog.Filter = FilterHelper.Shp;
            openFileDialog.InitialDirectory = Application.StartupPath + @"\GEOGRAPHIC";
            if (openFileDialog.ShowDialog(this) == DialogResult.OK)
            {
                string typeName = null;
                MapWinGIS.Shapefile table = new MapWinGIS.Shapefile();
                try
                {
                    System.IO.FileInfo fileInfo = new System.IO.FileInfo(openFileDialog.FileName);
                    typeName = fileInfo.Name;
                    if (typeName.IndexOf('.') >= 0)
                    {
                        typeName = typeName.Substring(0, typeName.IndexOf('.'));
                    }

                    if (table.Open(openFileDialog.FileName, null))
                    {
                        cbxColumn.Properties.Items.Clear();
                        int numFields = table.NumFields;
                        for (int x = 0; x < numFields; x++)
                        {
                            MapWinGIS.Field field = table.get_Field(x);
                            cbxColumn.Properties.Items.Add(field.Name);
                        }
                        cbxColumn.SelectedIndex = 0;
                    }
                    this.edtMap.Text = openFileDialog.FileName;
                    streetTableName = typeName;
                }
                catch
                {
                    //continue
                }
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (checkMap(edtMap.Text.Trim()))
            {
                StreetInjectTableInfo streetInfo = new StreetInjectTableInfo();
                streetInfo.FilePath = edtMap.Text.Trim();
                streetInfo.ColumnName = cbxColumn.Text;
                if (checkedListBoxItemDic.ContainsKey(streetTableName))
                {
                    int index = clbMap.FindString(streetTableName);
                    clbMap.SetItemValue(streetInfo, index);
                    checkedListBoxItemDic[streetTableName].Value = streetInfo;
                }
                else
                {
                    CheckedListBoxItem item = new CheckedListBoxItem();
                    item.Description = streetTableName;
                    item.CheckState = CheckState.Checked;
                    item.Value = streetInfo;
                    clbMap.Items.Add(item);
                    checkedListBoxItemDic[streetTableName] = item;
                }
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (clbMap.SelectedIndex >= 0)
            {
                checkedListBoxItemDic.Remove(clbMap.Items[clbMap.SelectedIndex].ToString());
                clbMap.Items.RemoveAt(clbMap.SelectedIndex);
            }
        }

        private bool checkMap(string filePath)
        {
            if (File.Exists(filePath))
            {
                return true;
            }
            return false;
        }

        private void btnSelectAll_Click(object sender, EventArgs e)
        {
            clbMap.CheckAll();
        }

        private void btnSelectNone_Click(object sender, EventArgs e)
        {
            clbMap.UnCheckAll();
        }

        private void saveConfig()
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement element = configFile.AddConfig("StreetInject");
                configFile.AddItem(element, "DistrictStreets", Param);
                configFile.Save(string.Format(Application.StartupPath + @"/config/StreetInject.xml"));
            }
            catch
            {
                //continue
            }
        }

        private void loadConfig()
        {
            string configFileName = Application.StartupPath + @"\config\StreetInject.xml";
            if (File.Exists(configFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(configFileName);
                try
                {
                    object streetsParam = configFile.GetItemValue("StreetInject", "DistrictStreets");
                    Param = (Dictionary<string, object>)streetsParam;
                }
                catch
                {
                    //continue
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (clbMap.Items.Count <= 0)
            {
                MessageBox.Show(layerSettingErrMsg, "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.Retry;
                return;
            }
            if (clbMap.CheckedIndices.Count <= 0)
            {
                MessageBox.Show(layerSettingErrMsg, "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.Retry;
                return;
            }
            if (mainModel.StreetInjectTablesDic.ContainsKey(mainModel.DistrictID))
            {
                mainModel.StreetInjectTablesDic[mainModel.DistrictID].Clear();
            }
            else
            {
                List<StreetInjectTableInfo> streetList = new List<StreetInjectTableInfo>();
                mainModel.StreetInjectTablesDic[mainModel.DistrictID] = streetList;
            }
            foreach (CheckedListBoxItem item in clbMap.Items)
            {
                mainModel.StreetInjectTablesDic[mainModel.DistrictID].Add((StreetInjectTableInfo)item.Value);
            }
            mainModel.StreetInjectTablesList.Clear();
            foreach (CheckedListBoxItem item in clbMap.CheckedItems)
            {
                mainModel.StreetInjectTablesList.Add((StreetInjectTableInfo)item.Value);
            }
            saveConfig();
            DialogResult = DialogResult.OK;
        }
    }
}
