﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc.ZTLTESINR
{
    public class SINRCondition
    {
        public SINRCondition(int oper, float sinr)
        {
            SINRType = oper;
            SINR = sinr;
            conditions = new List<SINRConditionItem>();
        }
        public SINRCondition()
        {
            conditions = new List<SINRConditionItem>();
        }

        public int SINRType { get; set; }
        public float SINR { get; set; }
        public string KPIName { get; set; }
        private readonly List<SINRConditionItem> conditions = null;

        public List<SINRConditionItem> Conditions
        {
            get { return conditions; }
        }
        public string ConName
        {
            get
            {
                return "SINR " + getType(SINRType) + SINR;
            }
        }
       
        private string getType(int type)
        {
            switch (type)
            {
                case 0:
                    return "<";
                case 1:
                    return ">";
                default:
                    return "";
            }
        }

        public bool IsEligibility(float sinrValue)
        {
            switch(SINRType)
            {
                case 0:
                    if (sinrValue < SINR)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                case 1:
                    if (sinrValue > SINR)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                case 2:
                    if (sinrValue <= SINR)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                case 3:
                    if (sinrValue >= SINR)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                default:
                    return false;

            }
        }     
    }

    
    public class SINRConditionItem
    {
        public SINRConditionItem()
        {
        }

        public SINRConditionItem(int type, float first, float second, string kpiName)
        {
            OperType = type;
            ExtremumFirst = first;
            ExtremumSecond = second;
            KPIName = kpiName;
        }

        public int OperType { get; set; }
        public float ExtremumFirst { get; set; }
        public float ExtremumSecond { get; set; }
        public string KPIName { get; set; }

        public string Name
        {
            get
            {
                if (OperType == (int)EnumOperatorsType.GTAndLT)
                {
                    return ExtremumFirst + " < " + KPIName + " ≤ " + ExtremumSecond;
                }
                else
                {
                    return KPIName + getType() + ExtremumFirst;
                }
            }
        }

        public string getType()
        {
            switch (OperType)
            {
                case 0:
                    return "<";
                case 1:
                    return ">";
                case 3:
                    return "≤";
                default:
                    return "";
            }
        }
    }
    public class SaveCondition
    {
        public SaveCondition()
        {
            SINRCond = new SINRCondition();
        }
        public string Name { get; set; }
        public override string ToString()
        {
            return Name;
        }
        public SINRCondition SINRCond { get; set; }
    }


    enum EnumOperatorsType
    {
        LessThan = 0,
        GreaterThan = 1,
        GTAndLT = 2,
        EqualOrLessThan = 3,
    }
}
