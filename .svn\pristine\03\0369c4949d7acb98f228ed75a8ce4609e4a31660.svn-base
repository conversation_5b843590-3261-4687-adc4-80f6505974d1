﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.JiLinFocusSet
{
    public class EventItem : Event
    {
        internal static EventItem Create(Net.Content content)
        {
            EventItem item = new EventItem();
            FileInfo fi = new FileInfo();
            item.ApplyHeader(fi);
            fi.DistrictID = content.GetParamInt();
            item.SetOrderType = content.GetParamString();
            item.SetID = content.GetParamInt();
            item.ItemID = content.GetParamInt();
            item.Status = content.GetParamInt();
            item.CreateDate = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(content.GetParamInt() * 1000L);
            item.CloseDate = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(content.GetParamInt() * 1000L);
            fi.ID = content.GetParamInt();
            fi.ProjectID = content.GetParamInt();
            fi.ServiceType = content.GetParamInt();
            fi.LogTable = content.GetParamString();
            item.SN = content.GetParamInt();
            item.Time = content.GetParamInt();
            item.Millisecond = content.GetParamShort();
            item.MS = content.GetParamByte();
            item.Longitude = content.GetParamDouble();
            item.Latitude = content.GetParamDouble();
            item.ID = content.GetParamInt();
            item["LAC"] = content.GetParamInt();
            item["CI"] = content.GetParamInt();
            item.AnaDate = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(content.GetParamInt() * 1000L);
            item.PreTypeDesc = content.GetParamString();
            item.ReasonDesc = content.GetParamString();
            item.SolutionDesc = content.GetParamString();
            fi.Name = content.GetParamString();
            item.BeginTime = DateTime.Parse(content.GetParamString());
            item.EndTime = DateTime.Parse(content.GetParamString());
            item.AvgSpeed = content.GetParamFloat();
            item.AvgRSRP = content.GetParamFloat();
            item.AvgSINR = content.GetParamFloat();

            return item;
        }

        public int SetID { get; set; }

        public int ItemID { get; set; }

        public int Status { get; set; }

        public DateTime CreateDate { get; set; }

        public DateTime CloseDate { get; set; }

        public DateTime AnaDate { get; set; }

        public int ESStatus { get; set; }

        public string PreTypeDesc { get; set; }

        public string ReasonDesc { get; set; }

        public string SolutionDesc { get; set; }

        public DateTime EndTime { get; set; }

        public DateTime BeginTime { get; set; }

        public float AvgSpeed { get; set; }

        public float AvgRSRP { get; set; }

        public float AvgSINR { get; set; }

        public string SetOrderType { get; set; }

        public string OrderKey
        {
            get { return string.Format("{0}.{1}.{2}", DistrictID, SetOrderType, SetID); }
        }
    }
}
