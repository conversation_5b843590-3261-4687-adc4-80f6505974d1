﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCoverPnl : UserControl
    {
        public NRCoverPnl()
        {
            InitializeComponent();
        }

        public void LinkCondition(NRLowSpeedCauseCondition cond)
        {
            NRCoverCause cr = null;
            foreach (NRLowSpeedCauseBase r in cond.Causes)
            {
                if (r is NRCoverCause)
                {
                    cr = r as NRCoverCause;
                    break;
                }
            }

            if (cr == null)
            {
                return;
            }
            foreach (NRLowSpeedCauseBase r in cr.SubCauses)
            {
                if (r is NRWeakCoverCause)
                {
                    nrWeakCoverPnl1.LinkCondition(r as NRWeakCoverCause);
                }
                else if (r is NROverCoverCause)
                {
                    nrOverCoverPnl1.LinkCondition(r as NROverCoverCause);
                }
                else if (r is NRWrongCoverCause)
                {
                    nrWrongCoverPnl1.LinkCondition(r as NRWrongCoverCause);
                }
                else if (r is NRRepeatCoverCause)
                {
                    nrRepeatCoverPnl1.LinkCondition(r as NRRepeatCoverCause);
                }
            }
        }
    }
}
