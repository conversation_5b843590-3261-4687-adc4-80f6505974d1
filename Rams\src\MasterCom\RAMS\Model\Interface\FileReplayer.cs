﻿using System;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.Interface
{
    public static class FileReplayer
    {
        private static TimePeriod getReplayPeriod(DTData data)
        {
            DateTime sT = DateTime.MinValue;
            DateTime eT = data.DateTime.AddMinutes(4);
            bool showWholeChk = false;
            if (data is Event)
            {
                Event evt = data as Event;
                showWholeChk = evt.TryGetCallAttemptTimeFromValue7(out sT);
            }
            PreNextMinutesForm timeForm = new PreNextMinutesForm(showWholeChk);
            if (timeForm.ShowDialog() == DialogResult.OK)
            {
                if (!timeForm.ByWholeProc)
                {
                    int pre = timeForm.Pre;
                    int next = timeForm.Next;
                    sT = data.DateTime.AddMinutes(-pre);
                    eT = data.DateTime.AddMinutes(next);
                }
                else if (sT > eT)
                {
                    sT = data.DateTime.AddMinutes(-3);
                    eT = data.DateTime.AddMinutes(3);
                }
            }
            else
            {
                return null;
            }
            TimePeriod p = new TimePeriod(sT, eT);
            return p;
        }

        /// <summary>
        /// 获取文件回放条件
        /// </summary>
        /// <param name="data">文件内的某个数据</param>
        /// <param name="specificPeriod">true：回放指定时间段内的数据。false时则回放整个文件</param>
        /// <returns></returns>
        private static QueryCondition getQueryCondition(DTData data, bool specificPeriod)
        {
            QueryCondition condition = new QueryCondition();
            if (specificPeriod)
            {
                TimePeriod period = getReplayPeriod(data);
                if (period == null)
                {
                    return null;
                }
                condition.Periods.Add(period);
            }

            if (data is Event)
            {
                condition.DistrictID = (data as Event).DistrictID;
            }
            FileInfo fileInfo = new FileInfo();
            fileInfo.Name = data.FileName;
            fileInfo.ProjectID = data.ProjectType;
            fileInfo.ID = data.FileID;
            fileInfo.LogTable = data.LogTable;
            fileInfo.ServiceType = data.ServiceType;
            fileInfo.SampleTbName = data.SampleTbName;
            condition.FileInfos.Add(fileInfo);
            return condition;
        }

        /// <summary>
        /// 通过文件的某个测试数据回放文件
        /// </summary>
        /// <param name="data"></param>
        /// <param name="specificPeriod">回放指定时间段内的数据。false时则回放整个文件</param>
        public static void Replay(DTData data, bool specificPeriod)
        {
            if (specificPeriod)
            {
                ReplayOnePart(data);
            }
            else
            {
                ReplayWhole(data);
            }
            
        }
        public static void HadReplayWithIMSI(DTData data,long imsi)
        {
            QueryCondition condition = getQueryCondition(data, true);
            if (condition == null)
            {
                //
            }
        }
        public static void ReplayWithIMSI(DTData data,bool specificPeriod,long imsi)
        {
            if (specificPeriod)
            {
                ReplayOnePartWithIMSI(data,imsi);
            }
            else
            {
                ReplayWholeWithIMSI(data,imsi);
            }
        }

        internal static void Replay(FileInfo fi, TimePeriod timePeriod)
        {
            QueryCondition condition = new QueryCondition();
            condition.Periods.Add(timePeriod);
            condition.FileInfos.Add(fi);
            DIYReplayFileWithinPeriodQuery query = new DIYReplayFileWithinPeriodQuery(MainModel.GetInstance());
            replay(query, condition);
        }

        /// <summary>
        /// 回放某段时间的文件
        /// </summary>
        /// <param name="data"></param>
        public static void ReplayOnePart(DTData data)
        {
            QueryCondition condition = getQueryCondition(data, true);
            if (condition==null)
            {
                return;
            }
            DIYReplayFileWithinPeriodQuery query = new DIYReplayFileWithinPeriodQuery(MainModel.GetInstance());
            replay(query,condition);
            selectedReplayData(data);
        }

        public static void ReplayOnePartWithIMSI(DTData data,long imsi)
        {
            QueryCondition condition = getQueryCondition(data, true);
            if (condition == null)
            {
                return;
            }
            DIYReplayFileWithinPeriodQueryWithOneIMSI query = new DIYReplayFileWithinPeriodQueryWithOneIMSI(MainModel.GetInstance());
            //DIYReplayFileQuery query = new DIYReplayFileQuery(MainModel.GetInstance());
            if (data is SignalTestPoint)
            {
                query.setWithinGeometry((data as SignalTestPoint).queryGeometry);
            }
            else if(data is Event)
            {
                query.setWithinGeometry((data as Event).Tag as SearchGeometrys);
            }
            query.SetCurrentIMSI(imsi);
            //DIYReplayFileWithinPeriodQuery query = new DIYReplayFileWithinPeriodQuery(MainModel.GetInstance());
            replay(query, condition);
            //selectedReplayData(data);
        }

        public static void ReplayOnePart(DTData data, QueryBase query, bool specificPeriod)
        {
            QueryCondition condition = getQueryCondition(data, specificPeriod);
            if (condition == null)
            {
                return;
            }

            replay(query, condition);
            selectedReplayData(data);
        }

        public static void ReplayOnePart(DTData data,TimePeriod period)
        {
            QueryCondition condition = getQueryCondition(data, false);
            if (condition == null)
            {
                return;
            }
            condition.Periods.Add(period);
            DIYReplayFileWithinPeriodQuery query = new DIYReplayFileWithinPeriodQuery(MainModel.GetInstance());
            query.NeedAskDlg = false;
            replay(query, condition);
            selectedReplayData(data);
        }

        public static void ReplayOnePart(FileInfo fi, DateTime referTime)
        {
            PreNextMinutesForm timeForm = new PreNextMinutesForm(false);
            if (timeForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            int pre = timeForm.Pre;
            int next = timeForm.Next;
            QueryCondition condition = new QueryCondition();
            TimePeriod p = new TimePeriod(referTime.AddMinutes(-pre), referTime.AddMinutes(next));
            condition.Periods.Add(p);
            condition.FileInfos.Add(fi);
            DIYReplayFileWithinPeriodQuery query = new DIYReplayFileWithinPeriodQuery(MainModel.GetInstance());
            replay(query, condition);
        }

        /// <summary>
        /// 回放某段时间的主被叫文件
        /// </summary>
        /// <param name="data"></param>
        public static void ReplayOnePartBothSides(DTData data)
        {
            QueryCondition condition = getQueryCondition(data, true);
            if (condition == null)
            {
                return;
            }
            condition.isCompareMode = true;
            ReplayFileWithinCompare query = new ReplayFileWithinCompare(MainModel.GetInstance());
            replay(query, condition);
            selectedReplayData(data);
        }

        public static void ReplayWhole(DTData data)
        {
            QueryCondition condition = getQueryCondition(data, false);
            if (condition == null)
            {
                return;
            }
            DIYReplayFileQuery query = new DIYReplayFileQuery(MainModel.GetInstance());
            replay(query, condition);
        }

        public static void ReplayWholeWithIMSI(DTData data,long imsi)
        {
            QueryCondition condition = getQueryCondition(data, false);
            if (condition == null)
            {
                return;
            }
            ReplayFileByImsiQueryWithOneIMSI query = new ReplayFileByImsiQueryWithOneIMSI(MainModel.GetInstance());
            if (data is SignalTestPoint)
            {
                query.setWithinGeometry((data as SignalTestPoint).queryGeometry);
            }
            else if (data is Event)
            {
                query.setWithinGeometry((data as Event).Tag as SearchGeometrys);
            }
            //DIYReplayFileQuery query = new DIYReplayFileQuery(MainModel.GetInstance());
            query.SetCurrentIMSI(imsi);
            replay(query, condition);
        }
        public static void ReplayWhole(FileInfo fi)
        {
            QueryCondition condition = new QueryCondition();
            condition.FileInfos.Add(fi);
            DIYReplayFileQuery query = new DIYReplayFileQuery(MainModel.GetInstance());
            replay(query, condition);
        }

        private static void selectedReplayData(DTData data)
        {
            bool hasGotData = false;
            foreach (DTFileDataManager file in MainModel.GetInstance().DTDataManager.FileDataManagers)
            {
                if (file.FileID == data.FileID)
                {
                    MainModel.GetInstance().SelectedEvents.Clear();
                    MainModel.GetInstance().SelectedTestPoints.Clear();
                    hasGotData = judgeHasGotData(data, file);
                    if (hasGotData)
                    {
                        return;
                    }
                }
            }
        }

        private static bool judgeHasGotData(DTData data, DTFileDataManager file)
        {
            foreach (DTData item in file.DTDatas)
            {
                if (item.SN == data.SN)
                {
                    item.Selected = true;
                    if (item is Event)
                    {
                        MainModel.GetInstance().SelectedEvents.Add(item as Event);
                    }
                    else if (item is TestPoint)
                    {
                        MainModel.GetInstance().SelectedTestPoints.Add(item as TestPoint);
                    }
                    return true;
                }
            }

            return false;
        }

        private static void replay(QueryBase query, QueryCondition cond)
        {
            query.SetQueryCondition(cond);
            query.Query();
            MainModel.GetInstance().MainForm.ChangeWorkSpace();
        }

        internal static void ReplayOnePart(FileInfo fi, TimePeriod period)
        {
            QueryCondition condition = new QueryCondition();
            condition.DistrictID = fi.DistrictID;
            condition.FileInfos.Add(fi);
            condition.Periods.Add(period);
            DIYReplayFileWithinPeriodQuery query = new DIYReplayFileWithinPeriodQuery(MainModel.GetInstance());
            query.SetQueryCondition(condition);
            replay(query, condition);
        }
    }

}
