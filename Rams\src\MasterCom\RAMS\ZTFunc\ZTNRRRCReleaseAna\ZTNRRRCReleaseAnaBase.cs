﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTNRRRCReleaseAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public List<ZTNRRRCReleaseAnaItem> resultList { get; set; } = new List<ZTNRRRCReleaseAnaItem>();
        
        public ZTNRRRCReleaseAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = false;
            this.IncludeMessage = true;
        }

        protected override void getReadyBeforeQuery()
        {
            ServiceTypes.Clear();

            ServiceTypes.Add(ServiceType.NR_NSA_TDD_IDLE);
            ServiceTypes.Add(ServiceType.NR_NSA_TDD_DATA);
            ServiceTypes.Add(ServiceType.NR_NSA_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.NR_SA_TDD_IDLE);
            ServiceTypes.Add(ServiceType.NR_SA_TDD_DATA);
            ServiceTypes.Add(ServiceType.NR_SA_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.NR_DM_TDD_IDLE);
            ServiceTypes.Add(ServiceType.NR_DM_TDD_DATA);
            ServiceTypes.Add(ServiceType.NR_DM_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_NR_SA_TDD_EPSFB);
            ServiceTypes.Add(ServiceType.SER_NR_DM_TDD_EPSFB);
            ServiceTypes.Add(ServiceType.SER_NR_NSA_TDD_MULTI);
            ServiceTypes.Add(ServiceType.SER_NR_SA_TDD_MULTI);
            ServiceTypes.Add(ServiceType.SER_NR_DM_TDD_MULTI);
            ServiceTypes.Add(ServiceType.NR_SA_TDD_VONR);
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<ZTNRRRCReleaseAnaItem>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<DTData> dtDataList = new List<DTData>();

                foreach (TestPoint tp in fileMng.TestPoints)
                {
                    dtDataList.Add(tp);
                }

                foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
                {
                    if (msg.ID == (int)MessageManager.NR_RRC_RRCRelease ||
                        msg.ID == (int)MessageManager.NR_RRC_MeasurementReport)
                    {
                        dtDataList.Add(msg);
                    }
                }

                dtDataList.Sort(comparer);

                TestPoint lastTp = null;
                DTData lastMR = null;

                for (int i = 0; i < dtDataList.Count; i++)
                {
                    if (dtDataList[i] is TestPoint)
                    {
                        lastTp = dtDataList[i] as TestPoint;
                    }
                    else if (dtDataList[i] is MasterCom.RAMS.Model.Message)
                    {
                        lastMR = dealMsg(dtDataList, lastTp, lastMR, i);
                    }
                }
            }
        }

        private DTData dealMsg(List<DTData> dtDataList, TestPoint lastTp, DTData lastMR, int i)
        {
            if ((dtDataList[i] as Message).ID == (int)MessageManager.LTE_RRC_RRC_Connection_Release)
            {
                int utranArfcn = getEARFCNFromRRCConnRelease(dtDataList[i]);

                if (utranArfcn > 0)
                {
                    int lastMRRsrp = 0;
                    int lastMRPCI = 0;

                    if (getEutranRSRPAndPCIFromMR(lastMR, dtDataList[i], ref lastMRRsrp, ref lastMRPCI))
                    {
                        var item = new ZTNRRRCReleaseAnaItem(lastTp, utranArfcn, lastMRRsrp, lastMRPCI);
                        item.Analyse();
                        item.SN = resultList.Count + 1;
                        resultList.Add(item);
                    }
                }
            }
            else if ((dtDataList[i] as Message).ID == (int)MessageManager.NR_RRC_RRCRelease)  // NR Release信令无重定向相关参数
            {
                int utranArfcn = getARFCNFromRRCConnRelease(dtDataList[i]);

                if (utranArfcn > 0)
                {
                    int lastMRRsrp = 0;
                    int lastMRPCI = 0;

                    if (getUtranRSRPAndPCIFromMR(lastMR, dtDataList[i], ref lastMRRsrp, ref lastMRPCI))
                    {
                        var item = new ZTNRRRCReleaseAnaItem(lastTp, utranArfcn, lastMRRsrp, lastMRPCI);
                        item.Analyse();
                        item.SN = resultList.Count + 1;
                        resultList.Add(item);
                    }
                }
            }
            else if ((dtDataList[i] as Message).ID == (int)MessageManager.LTE_RRC_Measurement_Report)
            {
                lastMR = dtDataList[i];
            }
            else if ((dtDataList[i] as Message).ID == (int)MessageManager.NR_RRC_MeasurementReport)
            {
                lastMR = dtDataList[i];
            }

            return lastMR;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dtData"></param>
        /// <returns></returns>
        private bool getEutranRSRPAndPCIFromMR(DTData lastMR, DTData RRCConnRelease, ref int rsrp, ref int pci)
        {
            if (lastMR == null)
            {
                return false;
            }

            TimeSpan timeSpan = RRCConnRelease.DateTime - lastMR.DateTime;
            if(timeSpan.Milliseconds > 3000) //如果两个信令时间距离超过3秒
            {
                return false;
            }

            MessageWithSource msg = (MessageWithSource)lastMR;
            MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Source.Length, msg.ID);

            int nbCellsCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measResultListEUTRA", ref nbCellsCount))
            {
                int[] arrRSRPs = new int[nbCellsCount + 1]; //包括主服的，所以+1
                int[] arrPCIs = new int[nbCellsCount];

                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.rsrpResult", ref arrRSRPs, nbCellsCount + 1)
                    && MessageDecodeHelper.GetMultiSInt("lte-rrc.physCellId", ref arrPCIs, nbCellsCount)
                    && (arrRSRPs.Length > 1) && (arrPCIs.Length > 0))
                {
                    rsrp = arrRSRPs[1] - 141; //跳过主服
                    pci = arrPCIs[0];

                    return true;
                }
            }

            return false;
        }


        /// <summary>
        /// 分析原理，当 RRC Connection Release 中出现 eutran 的频段，就认为重定向异常
        /// </summary>
        /// <param name="dtData"></param>
        /// <returns></returns>
        private int getEARFCNFromRRCConnRelease(DTData dtData)
        {
            MessageWithSource msg = ((MessageWithSource)dtData);
            MessageDecodeHelper.StartDissect(msg.Direction,msg.Source, msg.Source.Length, msg.ID);

            uint carrier = 0;
            uint earfcn = 0;

            if(!MessageDecodeHelper.GetSingleUInt("lte-rrc.redirectedCarrierInfo", ref carrier))
            {
                return 0;
            }

            if (carrier != (int)ECarrierInfo.eutra && carrier != (int)ECarrierInfo.nr_r15)
            {
                return 0;
            }

            if (!MessageDecodeHelper.GetSingleUInt("lte-rrc.eutra", ref earfcn) &&
                !MessageDecodeHelper.GetSingleUInt("lte-rrc.carrierFreq_r15", ref earfcn))
            {
                return 0;
            }

            return (int)earfcn;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dtData"></param>
        /// <returns></returns>
        private bool getUtranRSRPAndPCIFromMR(DTData lastMR, DTData RRCConnRelease, ref int rsrp, ref int pci)
        {
            if (lastMR == null)
            {
                return false;
            }

            TimeSpan timeSpan = RRCConnRelease.DateTime - lastMR.DateTime;
            if (timeSpan.Milliseconds > 3000) //如果两个信令时间距离超过3秒
            {
                return false;
            }

            MessageWithSource msg = (MessageWithSource)lastMR;
            MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Source.Length, msg.ID);

            int nbCellsCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("", ref nbCellsCount))
            {
                int[] arrRSRPs = new int[nbCellsCount + 1]; //包括主服的，所以+1
                int[] arrPCIs = new int[nbCellsCount];

                if (MessageDecodeHelper.GetMultiSInt("", ref arrRSRPs, nbCellsCount + 1)
                    && MessageDecodeHelper.GetMultiSInt("", ref arrPCIs, nbCellsCount)
                    && (arrRSRPs.Length > 1) && (arrPCIs.Length > 0))
                {
                    rsrp = arrRSRPs[1] - 141; //跳过主服
                    pci = arrPCIs[0];

                    return true;
                }
            }

            return false;
        }


        /// <summary>
        /// 分析原理，当 NR RRC Connection Release 中出现 utran 的频段，就认为重定向异常
        /// </summary>
        /// <param name="dtData"></param>
        /// <returns></returns>
        private int getARFCNFromRRCConnRelease(DTData dtData)
        {
            MessageWithSource msg = ((MessageWithSource)dtData);
            MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Source.Length, msg.ID);

            uint carrier = 0;
            uint earfcn = 0;

            if (!MessageDecodeHelper.GetSingleUInt("lte-rrc.redirectedCarrierInfo", ref carrier))
            {
                return 0;
            }

            if (carrier != (int)ECarrierInfo.eutra)
            {
                return 0;
            }

            if (!MessageDecodeHelper.GetSingleUInt("lte-rrc.eutra", ref earfcn))
            {
                return 0;
            }

            return (int)earfcn;
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            ZTNRRRCReleaseAnaListForm frm = mainModel.CreateResultForm(
                typeof(ZTNRRRCReleaseAnaListForm)) as ZTNRRRCReleaseAnaListForm;
            frm.FillData(resultList);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class ZTNRRRCReleaseAnaItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }

        public NRCell ServCell { get; set; }
        public string CellName { get; set; }
        public int TAC { get; set; }
        public long NCI { get; set; }
        public int CellID { get; set; }
        public int ARFCN { get; set; }
        public int PCI { get; set; }

        public TestPoint Tp { get; set; }
        public string TpTime { get; set; }
        public double TpLongitude { get; set; }
        public double TpLatitude { get; set; }
        public float TpRSRP { get; set; }
        public float TpSINR { get; set; }

        public int EutranARFCN { get; set; }  //RRC Conn Release信令中记录的频点
        public int LastMRPCI { get; set; }  // 距离RRC Conn Release信令时间最近的MR报告中的PCI信息，MR在RRCConnectionRelease之前
        public int LastMRRSRP { get; set; }  // 距离RRC Conn Release信令时间最近的MR报告中的邻区的RSRP信息，MR在RRCConnectionRelease之前

        public string ReDirectCellName { get; set; } //重定向小区名，根据 EutranEARFCN 和 LastMRPCI 进行工参匹配
        public string CellDistance { get; set; }     //重定向小区与之前小区的距离

        public ZTNRRRCReleaseAnaItem(TestPoint tp, int utranARFCN, int lastMRRsrp, int lastMRPCI)
        {
            Tp = tp;
            EutranARFCN = utranARFCN;
            LastMRRSRP = lastMRRsrp;
            LastMRPCI = lastMRPCI;
        }

        public virtual void Analyse()
        {
            //Cell Info
            int? tac = GetTAC(Tp);
            long? nci = GetNCI(Tp);
            int? arfcn = GetARFCN(Tp);
            int? pci = GetPCI(Tp);

            NRCell servCell = CellManager.GetInstance().GetNearestNRCell(Tp.DateTime, tac, nci, arfcn, pci, Tp.Longitude, Tp.Latitude);

            if (servCell != null)    //没有匹配到主服工参
            {
                ServCell = servCell;
                CellName = servCell.Name;
                TAC = servCell.TAC;
                NCI = servCell.NCI;
                ARFCN = servCell.ARFCN;
                PCI = servCell.PCI;
                CellID = servCell.CellID;
            }
            else
            {
                CellName = "";
                if (tac != null)
                {
                    TAC = (int)tac;
                    NCI = (long)nci;
                    ARFCN = (int)arfcn;
                    PCI = (int)pci;
                }
            }

            //重定向小区
            NRCell redirectCell = CellManager.GetInstance().GetNearestNRCell(Tp.DateTime, null, null, EutranARFCN, LastMRPCI, Tp.Longitude, Tp.Latitude);
            if (redirectCell != null)
            {
                ReDirectCellName = redirectCell.Name;

                if (servCell != null)
                {
                    CellDistance = Math.Round(redirectCell.GetDistance(servCell.Longitude, servCell.Latitude), 2).ToString();
                }
            }

            //TestPoint Info
            FileName = Tp.FileName;
            TpTime = Tp.DateTimeStringWithMillisecond;
            TpLongitude = Tp.Longitude;
            TpLatitude = Tp.Latitude;

            float? rsrp = GetRSRP(Tp);
            if (rsrp != null)
            {
                TpRSRP = (float)rsrp;
            }

            float? sinr = GetSINR(Tp);
            if (sinr != null)
            {
                TpSINR = (float)sinr;
            }
        }

        protected virtual int? GetTAC(TestPoint tp)
        {
            return (int?)tp["NR_TAC"];
        }
        protected virtual long? GetNCI(TestPoint tp)
        {
            return (int?)tp["NR_NCI"];
        }
        protected virtual int? GetARFCN(TestPoint tp)
        {
            return (int?)tp["NR_SSB_ARFCN"];
        }
        protected virtual int? GetPCI(TestPoint tp)
        {
            return (int?)tp["NR_PCI"];
        }
        protected virtual float? GetRSRP(TestPoint tp)
        {
            return (float?)tp["NR_SS_RSRP"];
        }
        protected virtual float? GetSINR(TestPoint tp)
        {
            return (float?)tp["NR_SS_SINR"];
        }
    } 
}
