﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using MasterCom.RAMS.ZTQuery;

namespace MasterCom.RAMS.Net
{
    public class DIYInjectionGridQueryMultiMap : DIYInjectionGridQuery
    {
        public DIYInjectionGridQueryMultiMap(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "道路测试渗透率查询"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18006, this.Name);
        }

        DIYInjectionGridQueryMultiMapSettingDlg mapSettingDlg = null;
        protected override void query()
        {
            if (mapSettingDlg == null)
            {
                mapSettingDlg = new DIYInjectionGridQueryMultiMapSettingDlg();
            }
            if (mapSettingDlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            ClientProxy clientProxy = new ClientProxy();
            injectGridMatrix = null;
            try
            {
                if (MainModel.User.DBID == -1)  //如果是省库
                {
                    foreach (int DistrictID in condition.DistrictIDs)
                    {
                        clientProxy = new ClientProxy();
                        if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                        {
                            ErrorInfo = "连接服务器失败!";
                            continue;
                        }
                        queryOneDistrict(clientProxy);
                    }
                }
                else
                {
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        return;
                    }
                    queryOneDistrict(clientProxy);
                }

                MainModel.CurStreetInjectMatrix = injectGridMatrix;
                MainModel.TotalStreetInjInfoResultList = totalInfoResultList;
                MainModel.FireStreetInjectQueried(this);
                MainModel.RefreshLegend();
                injectGridMatrix = null;
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void queryOneDistrict(ClientProxy clientProxy)
        {
            WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            MainModel.StreetInjectMultiTables = true;
            MainModel.LastSearchGeometry = Condition.Geometorys.Region;
            if (MainModel.MultiGeometrys && Condition.Geometorys.SelectedResvRegions != null && condition.Geometorys.SelectedResvRegions.Count > 0)//
            {
                MainModel.StreetInjectResvRegions = condition.Geometorys.SelectedResvRegions;
            }
            else
            {
                MainModel.StreetInjectResvRegions.Clear();
                ResvRegion resvRegion = new ResvRegion();
                resvRegion.RegionName = "当前选择区域";
                resvRegion.Shape = Condition.Geometorys.Region;
                MainModel.StreetInjectResvRegions.Add(resvRegion);
            }
        }
    }
}
