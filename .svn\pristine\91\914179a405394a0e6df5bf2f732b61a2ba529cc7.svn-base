﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTFileCompareDatetimeOptionDlg : BaseFormStyle
    {
        public ZTFileCompareDatetimeOptionDlg(DateTime dtBegin, DateTime dtEnd)
        {
            InitializeComponent();
            dtpEnd.Value = dtpEnd.MaxDate = dtEnd.Date.AddDays(1).AddMilliseconds(-1);//23:59:59
            //dtpBegin.MaxDate = dtpEnd.MaxDate.AddDays(-1);
            dtpBegin.Value = dtBegin.Date;
        }

        /// <summary>
        /// 获取时间段
        /// </summary>
        /// <param name="beginTime">开始时间，为所选日期的0点</param>
        /// <param name="endTime">结束时间，为所选日期的23:59:59秒</param>
        public void GetSettingTime(out DateTime beginTime, out DateTime endTime)
        {
            beginTime = dtpBegin.Value.Date;
            endTime = dtpEnd.Value.Date.AddDays(1).AddMilliseconds(-1);
        }
    }
}
