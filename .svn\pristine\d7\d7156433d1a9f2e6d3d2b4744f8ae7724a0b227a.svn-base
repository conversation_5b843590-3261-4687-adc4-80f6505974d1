﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CellWrongDirSettingDlg_CP
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.spinEditAngle = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditDis = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.spinEditRSCP = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditPer = new DevExpress.XtraEditors.SpinEdit();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.gbxFirstBatch = new System.Windows.Forms.GroupBox();
            this.dtPickerFirstStart = new System.Windows.Forms.DateTimePicker();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.dtPickerFirstEnd = new System.Windows.Forms.DateTimePicker();
            this.gbxSecondBatch = new System.Windows.Forms.GroupBox();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.dtPickerSecondStart = new System.Windows.Forms.DateTimePicker();
            this.dtPickerSecondEnd = new System.Windows.Forms.DateTimePicker();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.chkTime = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAngle.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDis.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRSCP.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPer.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            this.gbxFirstBatch.SuspendLayout();
            this.gbxSecondBatch.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(73, 43);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(36, 12);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "场强≥";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(28, 50);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(96, 12);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "与主服小区距离≥";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(28, 79);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(96, 12);
            this.labelControl3.TabIndex = 0;
            this.labelControl3.Text = "与主服小区夹角≥";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.spinEditAngle);
            this.groupControl1.Controls.Add(this.spinEditDis);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.labelControl7);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(267, 117);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "覆盖不符条件";
            // 
            // spinEditAngle
            // 
            this.spinEditAngle.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditAngle.Location = new System.Drawing.Point(130, 76);
            this.spinEditAngle.Name = "spinEditAngle";
            this.spinEditAngle.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditAngle.Properties.Appearance.Options.UseFont = true;
            this.spinEditAngle.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditAngle.Properties.IsFloatValue = false;
            this.spinEditAngle.Properties.Mask.EditMask = "N00";
            this.spinEditAngle.Properties.MaxValue = new decimal(new int[] {
            180,
            0,
            0,
            0});
            this.spinEditAngle.Size = new System.Drawing.Size(90, 20);
            this.spinEditAngle.TabIndex = 1;
            // 
            // spinEditDis
            // 
            this.spinEditDis.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditDis.Location = new System.Drawing.Point(130, 44);
            this.spinEditDis.Name = "spinEditDis";
            this.spinEditDis.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDis.Properties.Appearance.Options.UseFont = true;
            this.spinEditDis.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDis.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditDis.Properties.IsFloatValue = false;
            this.spinEditDis.Properties.Mask.EditMask = "N00";
            this.spinEditDis.Size = new System.Drawing.Size(90, 20);
            this.spinEditDis.TabIndex = 0;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(226, 81);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 12);
            this.labelControl7.TabIndex = 0;
            this.labelControl7.Text = "度";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(226, 49);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 12);
            this.labelControl5.TabIndex = 0;
            this.labelControl5.Text = "米";
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(346, 268);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(441, 268);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // spinEditRSCP
            // 
            this.spinEditRSCP.EditValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditRSCP.Location = new System.Drawing.Point(115, 40);
            this.spinEditRSCP.Name = "spinEditRSCP";
            this.spinEditRSCP.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRSCP.Properties.Appearance.Options.UseFont = true;
            this.spinEditRSCP.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRSCP.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditRSCP.Properties.IsFloatValue = false;
            this.spinEditRSCP.Properties.Mask.EditMask = "N00";
            this.spinEditRSCP.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditRSCP.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditRSCP.Size = new System.Drawing.Size(90, 20);
            this.spinEditRSCP.TabIndex = 0;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(37, 79);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(72, 12);
            this.labelControl4.TabIndex = 0;
            this.labelControl4.Text = "不符合比例≥";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(211, 79);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(6, 12);
            this.labelControl6.TabIndex = 0;
            this.labelControl6.Text = "%";
            // 
            // spinEditPer
            // 
            this.spinEditPer.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditPer.Location = new System.Drawing.Point(115, 76);
            this.spinEditPer.Name = "spinEditPer";
            this.spinEditPer.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPer.Properties.Appearance.Options.UseFont = true;
            this.spinEditPer.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPer.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditPer.Properties.IsFloatValue = false;
            this.spinEditPer.Properties.Mask.EditMask = "N00";
            this.spinEditPer.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditPer.Size = new System.Drawing.Size(90, 20);
            this.spinEditPer.TabIndex = 1;
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.labelControl8);
            this.groupControl2.Controls.Add(this.spinEditRSCP);
            this.groupControl2.Controls.Add(this.labelControl1);
            this.groupControl2.Controls.Add(this.spinEditPer);
            this.groupControl2.Controls.Add(this.labelControl4);
            this.groupControl2.Controls.Add(this.labelControl6);
            this.groupControl2.Location = new System.Drawing.Point(273, 1);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(267, 116);
            this.groupControl2.TabIndex = 3;
            this.groupControl2.Text = "过滤设置";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(211, 43);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(18, 12);
            this.labelControl8.TabIndex = 2;
            this.labelControl8.Text = "dBm";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.chkTime);
            this.groupControl3.Controls.Add(this.labelControl14);
            this.groupControl3.Controls.Add(this.gbxFirstBatch);
            this.groupControl3.Controls.Add(this.gbxSecondBatch);
            this.groupControl3.Controls.Add(this.labelControl10);
            this.groupControl3.Location = new System.Drawing.Point(0, 123);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(540, 138);
            this.groupControl3.TabIndex = 4;
            this.groupControl3.Text = "时间对比(竞对)";
            // 
            // gbxFirstBatch
            // 
            this.gbxFirstBatch.Controls.Add(this.dtPickerFirstStart);
            this.gbxFirstBatch.Controls.Add(this.labelControl12);
            this.gbxFirstBatch.Controls.Add(this.labelControl13);
            this.gbxFirstBatch.Controls.Add(this.dtPickerFirstEnd);
            this.gbxFirstBatch.Enabled = false;
            this.gbxFirstBatch.Location = new System.Drawing.Point(14, 42);
            this.gbxFirstBatch.Name = "gbxFirstBatch";
            this.gbxFirstBatch.Size = new System.Drawing.Size(247, 86);
            this.gbxFirstBatch.TabIndex = 8;
            this.gbxFirstBatch.TabStop = false;
            // 
            // dtPickerFirstStart
            // 
            this.dtPickerFirstStart.Location = new System.Drawing.Point(77, 16);
            this.dtPickerFirstStart.Name = "dtPickerFirstStart";
            this.dtPickerFirstStart.Size = new System.Drawing.Size(151, 21);
            this.dtPickerFirstStart.TabIndex = 1;
            this.dtPickerFirstStart.ValueChanged += new System.EventHandler(this.dtPickerFirstStart_ValueChanged);
            // 
            // labelControl12
            // 
            this.labelControl12.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl12.Appearance.Options.UseFont = true;
            this.labelControl12.Location = new System.Drawing.Point(15, 58);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(60, 12);
            this.labelControl12.TabIndex = 3;
            this.labelControl12.Text = "结束时间：";
            // 
            // labelControl13
            // 
            this.labelControl13.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl13.Appearance.Options.UseFont = true;
            this.labelControl13.Location = new System.Drawing.Point(15, 20);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(60, 12);
            this.labelControl13.TabIndex = 3;
            this.labelControl13.Text = "开始时间：";
            // 
            // dtPickerFirstEnd
            // 
            this.dtPickerFirstEnd.Location = new System.Drawing.Point(77, 54);
            this.dtPickerFirstEnd.Name = "dtPickerFirstEnd";
            this.dtPickerFirstEnd.Size = new System.Drawing.Size(151, 21);
            this.dtPickerFirstEnd.TabIndex = 5;
            this.dtPickerFirstEnd.ValueChanged += new System.EventHandler(this.dtPickerFirstEnd_ValueChanged);
            // 
            // gbxSecondBatch
            // 
            this.gbxSecondBatch.Controls.Add(this.labelControl11);
            this.gbxSecondBatch.Controls.Add(this.dtPickerSecondStart);
            this.gbxSecondBatch.Controls.Add(this.dtPickerSecondEnd);
            this.gbxSecondBatch.Controls.Add(this.labelControl9);
            this.gbxSecondBatch.Enabled = false;
            this.gbxSecondBatch.Location = new System.Drawing.Point(278, 42);
            this.gbxSecondBatch.Name = "gbxSecondBatch";
            this.gbxSecondBatch.Size = new System.Drawing.Size(247, 86);
            this.gbxSecondBatch.TabIndex = 7;
            this.gbxSecondBatch.TabStop = false;
            // 
            // labelControl11
            // 
            this.labelControl11.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl11.Appearance.Options.UseFont = true;
            this.labelControl11.Location = new System.Drawing.Point(17, 20);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(60, 12);
            this.labelControl11.TabIndex = 3;
            this.labelControl11.Text = "开始时间：";
            // 
            // dtPickerSecondStart
            // 
            this.dtPickerSecondStart.Location = new System.Drawing.Point(79, 16);
            this.dtPickerSecondStart.Name = "dtPickerSecondStart";
            this.dtPickerSecondStart.Size = new System.Drawing.Size(151, 21);
            this.dtPickerSecondStart.TabIndex = 1;
            this.dtPickerSecondStart.ValueChanged += new System.EventHandler(this.dtPickerSecondStart_ValueChanged);
            // 
            // dtPickerSecondEnd
            // 
            this.dtPickerSecondEnd.Location = new System.Drawing.Point(79, 54);
            this.dtPickerSecondEnd.Name = "dtPickerSecondEnd";
            this.dtPickerSecondEnd.Size = new System.Drawing.Size(151, 21);
            this.dtPickerSecondEnd.TabIndex = 4;
            this.dtPickerSecondEnd.ValueChanged += new System.EventHandler(this.dtPickerSecondEnd_ValueChanged);
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(17, 58);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(60, 12);
            this.labelControl9.TabIndex = 3;
            this.labelControl9.Text = "结束时间：";
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.Options.UseFont = true;
            this.labelControl10.Location = new System.Drawing.Point(24, 30);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(48, 12);
            this.labelControl10.TabIndex = 0;
            this.labelControl10.Text = "第一轮：";
            // 
            // labelControl14
            // 
            this.labelControl14.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl14.Appearance.Options.UseFont = true;
            this.labelControl14.Location = new System.Drawing.Point(283, 29);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(48, 12);
            this.labelControl14.TabIndex = 9;
            this.labelControl14.Text = "第二轮：";
            // 
            // chkTime
            // 
            this.chkTime.AutoSize = true;
            this.chkTime.Location = new System.Drawing.Point(100, 4);
            this.chkTime.Name = "chkTime";
            this.chkTime.Size = new System.Drawing.Size(15, 14);
            this.chkTime.TabIndex = 10;
            this.chkTime.UseVisualStyleBackColor = true;
            this.chkTime.CheckedChanged += new System.EventHandler(this.chkTime_CheckedChanged);
            // 
            // CellWrongDirSettingDlg_CP
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(544, 298);
            this.Controls.Add(this.groupControl3);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupControl1);
            this.Name = "CellWrongDirSettingDlg_CP";
            this.Text = "条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAngle.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDis.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRSCP.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPer.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            this.gbxFirstBatch.ResumeLayout(false);
            this.gbxFirstBatch.PerformLayout();
            this.gbxSecondBatch.ResumeLayout(false);
            this.gbxSecondBatch.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SpinEdit spinEditAngle;
        private DevExpress.XtraEditors.SpinEdit spinEditDis;
        private DevExpress.XtraEditors.SpinEdit spinEditRSCP;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SpinEdit spinEditPer;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private System.Windows.Forms.DateTimePicker dtPickerSecondEnd;
        private System.Windows.Forms.DateTimePicker dtPickerFirstEnd;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private System.Windows.Forms.DateTimePicker dtPickerSecondStart;
        private System.Windows.Forms.DateTimePicker dtPickerFirstStart;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private System.Windows.Forms.GroupBox gbxFirstBatch;
        private System.Windows.Forms.GroupBox gbxSecondBatch;
        private System.Windows.Forms.CheckBox chkTime;
        private DevExpress.XtraEditors.LabelControl labelControl14;
    }
}