﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteURLAnaForm : MinCloseForm
    {
        public LteURLAnaForm()
        {
            InitializeComponent();
            DisposeWhenClose = true;

            gvBroURL.RowClick += gvBroURL_RowClick;
            gvDowURL.RowClick += gvDowURL_RowClick;
            gvVideoURL.RowClick += gvVideoURL_RowClick;

            gvBroFile.RowClick += gvBroFile_RowClick;
            gvDowFile.RowClick += gvDowFile_RowClick;
            gvVideoFile.RowClick += gvVideoFile_RowClick;

            miExportExcel.Click += MiExportExcel_Click;

        }

        void gvVideoURL_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();
            LteURLVideoURL Video = v as LteURLVideoURL;
            if (Video != null)
            {
                gcVideoDet.DataSource = Video.Events;
                gcVideoDet.RefreshDataSource();
            }
        }

        void gvVideoFile_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();
            if (isByRegion)
            {
                LteURLRegion Video = v as LteURLRegion;
                if (Video != null)
                {
                    List<LteURLEvent> evtList = new List<LteURLEvent>();
                    foreach (LteURLVideoURL videoURL in Video.Videos)
                    {
                        evtList.AddRange(videoURL.Events);
                    }
                    gcVideoDet.DataSource = evtList;
                    gcVideoDet.RefreshDataSource();
                }
            }
            else
            {
                LteURLModel Video = v as LteURLModel;
                if (Video != null)
                {
                    List<LteURLEvent> evtList = new List<LteURLEvent>();
                    foreach (LteURLVideoURL videoURL in Video.Videos)
                    {
                        evtList.AddRange(videoURL.Events);
                    }
                    gcVideoDet.DataSource = evtList;
                    gcVideoDet.RefreshDataSource();
                }
            }
        }

        void gvDowURL_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();
            LteURLDowURL Dow = v as LteURLDowURL;
            if (Dow != null)
            {
                gcDowDet.DataSource = Dow.Events;
                gcDowDet.RefreshDataSource();
            }
        }

        void gvDowFile_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();
            if (isByRegion)
            {
                LteURLRegion Down = v as LteURLRegion;
                if (Down != null)
                {
                    List<LteURLEvent> evtList = new List<LteURLEvent>();
                    foreach (LteURLDowURL downURL in Down.Downs)
                    {
                        evtList.AddRange(downURL.Events);
                    }
                    gcDowDet.DataSource = evtList;
                    gcDowDet.RefreshDataSource();
                }
            }
            else
            {
                LteURLModel Down = v as LteURLModel;
                if (Down != null)
                {
                    List<LteURLEvent> evtList = new List<LteURLEvent>();
                    foreach (LteURLDowURL downURL in Down.Downs)
                    {
                        evtList.AddRange(downURL.Events);
                    }
                    gcDowDet.DataSource = evtList;
                    gcDowDet.RefreshDataSource();
                }
            }
        }

        void gvBroURL_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();
            LteURLBroURL Bro = v as LteURLBroURL;
            if (Bro != null)
            {
                gcBroDetail.DataSource = Bro.Events;
                gcBroDetail.RefreshDataSource();
            }
        }

        void gvBroFile_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();
            if (isByRegion)
            {
                LteURLRegion Bro = v as LteURLRegion;
                if (Bro != null)
                {
                    List<LteURLEvent> evtList = new List<LteURLEvent>();
                    foreach (LteURLBroURL broURL in Bro.Bros)
                    {
                        evtList.AddRange(broURL.Events);
                    }
                    gcBroDetail.DataSource = evtList;
                    gcBroDetail.RefreshDataSource();
                }
            }
            else
            {
                LteURLModel Bro = v as LteURLModel;
                if (Bro != null)
                {
                    List<LteURLEvent> evtList = new List<LteURLEvent>();
                    foreach (LteURLBroURL broURL in Bro.Bros)
                    {
                        evtList.AddRange(broURL.Events);
                    }
                    gcBroDetail.DataSource = evtList;
                    gcBroDetail.RefreshDataSource();
                }
            }
        }


        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (resultFiles == null && resultFilesRegion == null)
            {
                return;
            }
            if (resultFiles != null)
            {
                LteURLSummarize summmarize = new LteURLSummarize();
                SummarizeModel result = summmarize.Summarize(resultFiles);


                #region 浏览
                List<NPOIRow> summBroTables = new List<NPOIRow>();
                NPOIRow summBroTitleRow = new NPOIRow();
                summBroTitleRow.AddCellValue("地市");
                summBroTitleRow.AddCellValue("URL");
                summBroTitleRow.AddCellValue("HTTP登陆尝试次数");
                summBroTitleRow.AddCellValue("HTTP登陆成功次数");
                summBroTitleRow.AddCellValue("HTTP完全加载次数");
                summBroTitleRow.AddCellValue("HTTP登陆成功率");
                summBroTitleRow.AddCellValue("HTTP登陆时延(S)");
                summBroTitleRow.AddCellValue("HTTP浏览成功率");
                summBroTitleRow.AddCellValue("HTTP浏览时长(S)");
                summBroTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                summBroTables.Add(summBroTitleRow);
                foreach (SummarizeBro summBro in result.Bros)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(summBro.DistrictName);
                    row.AddCellValue(summBro.URL);
                    row.AddCellValue(summBro.DisplayCount);
                    row.AddCellValue(summBro.DisSucCount);
                    row.AddCellValue(summBro.CompleteCount);
                    row.AddCellValue(summBro.DisplayRate);
                    row.AddCellValue(summBro.DisplayDelay);
                    row.AddCellValue(summBro.CompleteRate);
                    row.AddCellValue(summBro.Time);
                    row.AddCellValue(summBro.Speed);
                    summBroTables.Add(row);
                }

                List<NPOIRow> broTables = new List<NPOIRow>();
                NPOIRow broTitleRow = new NPOIRow();
                broTitleRow.AddCellValue("地市");
                broTitleRow.AddCellValue("文件名");
                broTitleRow.AddCellValue("URL");
                broTitleRow.AddCellValue("HTTP登陆尝试次数");
                broTitleRow.AddCellValue("HTTP登陆成功次数");
                broTitleRow.AddCellValue("HTTP完全加载次数");
                broTitleRow.AddCellValue("HTTP登陆成功率");
                broTitleRow.AddCellValue("HTTP登陆时延(S)");
                broTitleRow.AddCellValue("HTTP浏览成功率");
                broTitleRow.AddCellValue("HTTP浏览时长(S)");
                broTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                broTables.Add(broTitleRow);
                foreach (LteURLModel dtlModel in resultFiles)
                {
                    List<object> fileValues = new List<object>();
                    fileValues.Add(dtlModel.DistrictName);
                    fileValues.Add(dtlModel.FileName);

                    foreach (LteURLBroURL dtlBro in dtlModel.Bros)
                    {
                        List<object> broValues = new List<object>(fileValues);
                        broValues.Add(dtlBro.URL);
                        broValues.Add(dtlBro.DisCount);
                        broValues.Add(dtlBro.DisSuc);
                        broValues.Add(dtlBro.Complete);
                        broValues.Add(dtlBro.DisSucRate * 100);
                        broValues.Add(dtlBro.DisDelay);
                        broValues.Add(dtlBro.SucRate * 100);
                        broValues.Add(dtlBro.Time);
                        broValues.Add(dtlBro.Speed);
                        NPOIRow broRow = new NPOIRow();
                        broRow.cellValues.AddRange(broValues);
                        broTables.Add(broRow);
                    }
                }
                List<NPOIRow> broEvtTables = new List<NPOIRow>();
                NPOIRow broEvtTitleRow = new NPOIRow();
                broEvtTitleRow.AddCellValue("文件名");
                broEvtTitleRow.AddCellValue("URL");
                broEvtTitleRow.AddCellValue("序号");
                broEvtTitleRow.AddCellValue("是否失败");
                broEvtTitleRow.AddCellValue("浏览时长(S)");
                broEvtTitleRow.AddCellValue("开始时间");
                broEvtTitleRow.AddCellValue("结束时间");
                broEvtTitleRow.AddCellValue("开始事件名称");
                broEvtTitleRow.AddCellValue("结束事件名称");
                broEvtTitleRow.AddCellValue("失败原因");
                broEvtTitleRow.AddCellValue("业务测试字节数");
                broEvtTables.Add(broEvtTitleRow);
                foreach (LteURLModel dtlModel in resultFiles)
                {
                    List<object> fileValues2 = new List<object>();
                    fileValues2.Add(dtlModel.FileName);
                    foreach (LteURLBroURL dtlBro in dtlModel.Bros)
                    {
                        foreach (LteURLEvent evt in dtlBro.Events)
                        {
                            List<object> evtValues = new List<object>(fileValues2);
                            evtValues.Add(evt.URL);
                            evtValues.Add(evt.SN);
                            evtValues.Add(evt.IsFail);
                            evtValues.Add(evt.TimeSpan);
                            evtValues.Add(evt.StartTime);
                            evtValues.Add(evt.EndTime);
                            evtValues.Add(evt.StartName);
                            evtValues.Add(evt.EvtEndName);
                            evtValues.Add(evt.FailReason);
                            evtValues.Add(evt.Bytes);
                            NPOIRow evtRow = new NPOIRow();
                            evtRow.cellValues.AddRange(evtValues);
                            broEvtTables.Add(evtRow);
                        }
                    }
                }
                #endregion
                #region 下载
                List<NPOIRow> summDownTables = new List<NPOIRow>();
                NPOIRow summDownTitleRow = new NPOIRow();
                summDownTitleRow.AddCellValue("地市");
                summDownTitleRow.AddCellValue("URL");
                summDownTitleRow.AddCellValue("HTTP下载尝试次数");
                summDownTitleRow.AddCellValue("HTTP下载成功次数");
                summDownTitleRow.AddCellValue("HTTP下载成功率");
                summDownTitleRow.AddCellValue("HTTP下载掉线次数");
                summDownTitleRow.AddCellValue("HTTP下载掉线率");
                summDownTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                summDownTitleRow.AddCellValue("应用层下载速率(含掉线)(kbps)");
                summDownTables.Add(summDownTitleRow);
                foreach (SummarizeDown summDown in result.Downs)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(summDown.DistrictName);
                    row.AddCellValue(summDown.URL);
                    row.AddCellValue(summDown.Dowcount);
                    row.AddCellValue(summDown.DowSuc);
                    row.AddCellValue(summDown.DowSucRate);
                    row.AddCellValue(summDown.DowFail);
                    row.AddCellValue(summDown.DowFaiRate);
                    row.AddCellValue(summDown.SucSpeed);
                    row.AddCellValue(summDown.Speed);
                    summDownTables.Add(row);
                }

                List<NPOIRow> downTables = new List<NPOIRow>();
                NPOIRow downTitleRow = new NPOIRow();
                downTitleRow.AddCellValue("地市");
                downTitleRow.AddCellValue("文件名");
                downTitleRow.AddCellValue("URL");
                downTitleRow.AddCellValue("HTTP下载尝试次数");
                downTitleRow.AddCellValue("HTTP下载成功次数");
                downTitleRow.AddCellValue("HTTP下载成功率");
                downTitleRow.AddCellValue("HTTP下载掉线次数");
                downTitleRow.AddCellValue("HTTP下载掉线率");
                downTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                downTitleRow.AddCellValue("应用层下载速率(含掉线)(kbps)");
                downTables.Add(downTitleRow);
                foreach (LteURLModel dtlModel in resultFiles)
                {
                    List<object> fileValues1 = new List<object>();
                    fileValues1.Add(dtlModel.DistrictName);
                    fileValues1.Add(dtlModel.FileName);
                    foreach (LteURLDowURL dtlDown in dtlModel.Downs)
                    {
                        List<object> downValues = new List<object>(fileValues1);
                        downValues.Add(dtlDown.URL);
                        downValues.Add(dtlDown.Dowcount);
                        downValues.Add(dtlDown.DowSuc);
                        downValues.Add(dtlDown.DowSucRate * 100);
                        downValues.Add(dtlDown.DowFail);
                        downValues.Add(dtlDown.DowFaiRate * 100);
                        downValues.Add(dtlDown.SucSpeed);
                        downValues.Add(dtlDown.Speed);

                        NPOIRow downRow = new NPOIRow();
                        downRow.cellValues.AddRange(downValues);
                        downTables.Add(downRow);

                    }
                }

                List<NPOIRow> downEvtTables = new List<NPOIRow>();
                NPOIRow downEvtTitleRow = new NPOIRow();
                downEvtTitleRow.AddCellValue("文件名");
                downEvtTitleRow.AddCellValue("URL");
                downEvtTitleRow.AddCellValue("序号");
                downEvtTitleRow.AddCellValue("是否失败");
                downEvtTitleRow.AddCellValue("下载时长(S)");
                downEvtTitleRow.AddCellValue("开始时间");
                downEvtTitleRow.AddCellValue("结束时间");
                downEvtTitleRow.AddCellValue("开始事件名称");
                downEvtTitleRow.AddCellValue("结束事件名称");
                downEvtTitleRow.AddCellValue("失败原因");
                downEvtTitleRow.AddCellValue("业务测试字节数");
                downEvtTables.Add(downEvtTitleRow);
                foreach (LteURLModel dtlModel in resultFiles)
                {
                    List<object> fileValues4 = new List<object>();
                    fileValues4.Add(dtlModel.FileName);
                    foreach (LteURLDowURL dtlDown in dtlModel.Downs)
                    {
                        foreach (LteURLEvent evt in dtlDown.Events)
                        {
                            List<object> evtValues = new List<object>(fileValues4);
                            evtValues.Add(evt.URL);
                            evtValues.Add(evt.SN);
                            evtValues.Add(evt.IsFail);
                            evtValues.Add(evt.TimeSpan);
                            evtValues.Add(evt.StartTime);
                            evtValues.Add(evt.EndTime);
                            evtValues.Add(evt.StartName);
                            evtValues.Add(evt.EvtEndName);
                            evtValues.Add(evt.FailReason);
                            evtValues.Add(evt.Bytes);
                            NPOIRow evtRow = new NPOIRow();
                            evtRow.cellValues.AddRange(evtValues);
                            downEvtTables.Add(evtRow);
                        }
                    }
                }
                #endregion
                #region 流媒体
                List<NPOIRow> summVideoTables = new List<NPOIRow>();
                NPOIRow summVideoTitleRow = new NPOIRow();
                summVideoTitleRow.AddCellValue("地市");
                summVideoTitleRow.AddCellValue("URL");
                summVideoTitleRow.AddCellValue("流媒体业务发起次数");
                summVideoTitleRow.AddCellValue("流媒体业务成功次数");
                summVideoTitleRow.AddCellValue("流媒体业务成功率");
                summVideoTitleRow.AddCellValue("流媒体加载时延(S)");
                summVideoTitleRow.AddCellValue("流媒体时长（s）");
                summVideoTitleRow.AddCellValue("流媒体卡顿时长(S)");
                summVideoTitleRow.AddCellValue("流媒体播放总时长（s）");
                summVideoTitleRow.AddCellValue("流媒体播放卡顿次数");
                summVideoTitleRow.AddCellValue("流媒体播放超时比例");
                summVideoTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                summVideoTitleRow.AddCellValue("流媒体加载速率(kbps)");
                summVideoTables.Add(summVideoTitleRow);
                foreach (SummarizeVideo summVideo in result.Videos)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(summVideo.DistrictName);
                    row.AddCellValue(summVideo.URL);
                    row.AddCellValue(summVideo.ReqCount);
                    row.AddCellValue(summVideo.SucCount);
                    row.AddCellValue(summVideo.SucRate);
                    row.AddCellValue(summVideo.Delay);
                    row.AddCellValue(summVideo.Time);
                    row.AddCellValue(summVideo.RebufferTime);
                    row.AddCellValue(summVideo.PlayTime);
                    row.AddCellValue(summVideo.RebufferCount);
                    row.AddCellValue(summVideo.TimeoutRate);
                    row.AddCellValue(summVideo.DownSpeed);
                    row.AddCellValue(summVideo.LoadSpeed);
                    summVideoTables.Add(row);
                }

                List<NPOIRow> videoTables = new List<NPOIRow>();
                NPOIRow videoTitleRow = new NPOIRow();
                videoTitleRow.AddCellValue("地市");
                videoTitleRow.AddCellValue("文件名");
                videoTitleRow.AddCellValue("URL");
                videoTitleRow.AddCellValue("流媒体业务发起次数");
                videoTitleRow.AddCellValue("流媒体业务成功次数");
                videoTitleRow.AddCellValue("流媒体业务成功率");
                videoTitleRow.AddCellValue("流媒体加载时延(S)");
                videoTitleRow.AddCellValue("流媒体时长（s）");
                videoTitleRow.AddCellValue("流媒体卡顿时长(S)");
                videoTitleRow.AddCellValue("流媒体播放总时长（s）");
                videoTitleRow.AddCellValue("流媒体播放卡顿次数");
                videoTitleRow.AddCellValue("流媒体播放超时比例");
                videoTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                videoTitleRow.AddCellValue("流媒体加载速率(kbps)");
                videoTables.Add(videoTitleRow);
                foreach (LteURLModel dtlModel in resultFiles)
                {
                    List<object> fileValues5 = new List<object>();
                    fileValues5.Add(dtlModel.DistrictName);
                    fileValues5.Add(dtlModel.FileName);
                    foreach (LteURLVideoURL dtlVideo in dtlModel.Videos)
                    {
                        List<object> videoValues = new List<object>(fileValues5);
                        videoValues.Add(dtlVideo.URL);
                        videoValues.Add(dtlVideo.ReqCount);
                        videoValues.Add(dtlVideo.SucCount);
                        videoValues.Add(dtlVideo.SucRate * 100);
                        videoValues.Add(dtlVideo.Delay);
                        videoValues.Add(dtlVideo.Time);
                        videoValues.Add(dtlVideo.RebufferTime);
                        videoValues.Add(dtlVideo.PlayTime);
                        videoValues.Add(dtlVideo.RebufferCount);
                        videoValues.Add(dtlVideo.TimeoutRate * 100);
                        videoValues.Add(dtlVideo.DownSpeed);
                        videoValues.Add(dtlVideo.LoadSpeed);

                        NPOIRow videoRow = new NPOIRow();
                        videoRow.cellValues.AddRange(videoValues);
                        videoTables.Add(videoRow);

                    }
                }

                List<NPOIRow> videoEvtTables = new List<NPOIRow>();
                NPOIRow videoEvtTitleRow = new NPOIRow();
                videoEvtTitleRow.AddCellValue("文件名");
                videoEvtTitleRow.AddCellValue("URL");
                videoEvtTitleRow.AddCellValue("序号");
                videoEvtTitleRow.AddCellValue("是否失败");
                videoEvtTitleRow.AddCellValue("时长(S)");
                videoEvtTitleRow.AddCellValue("开始时间");
                videoEvtTitleRow.AddCellValue("结束时间");
                videoEvtTitleRow.AddCellValue("开始事件名称");
                videoEvtTitleRow.AddCellValue("结束事件名称");
                videoEvtTitleRow.AddCellValue("失败原因");
                videoEvtTitleRow.AddCellValue("业务测试字节数");
                videoEvtTables.Add(videoEvtTitleRow);
                foreach (LteURLModel dtlModel in resultFiles)
                {
                    List<object> fileValues6 = new List<object>();
                    fileValues6.Add(dtlModel.FileName);
                    foreach (LteURLVideoURL dtlVideo in dtlModel.Videos)
                    {
                        foreach (LteURLEvent evt in dtlVideo.Events)
                        {
                            List<object> evtValues = new List<object>(fileValues6);
                            evtValues.Add(evt.URL);
                            evtValues.Add(evt.SN);
                            evtValues.Add(evt.IsFail);
                            evtValues.Add(evt.TimeSpan);
                            evtValues.Add(evt.StartTime);
                            evtValues.Add(evt.EndTime);
                            evtValues.Add(evt.StartName);
                            evtValues.Add(evt.EvtEndName);
                            evtValues.Add(evt.FailReason);
                            evtValues.Add(evt.Bytes);
                            NPOIRow evtRow = new NPOIRow();
                            evtRow.cellValues.AddRange(evtValues);
                            videoEvtTables.Add(evtRow);
                        }
                    }
                }
                #endregion
                ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() { summBroTables, summDownTables, summVideoTables, broTables, broEvtTables, downTables, downEvtTables, videoTables, videoEvtTables },
                    new List<string>() { "http浏览汇总", "http下载汇总", "流媒体汇总", "http浏览统计", "http浏览详情", "http下载统计", "http下载详情", "流媒体统计", "流媒体详情" });
            }
            else if (resultFilesRegion != null)
            {
                #region
                List<NPOIRow> broTables = new List<NPOIRow>();
                NPOIRow broTitleRow = new NPOIRow();
                broTitleRow.AddCellValue("图层类型");
                broTitleRow.AddCellValue("网格信息");
                broTitleRow.AddCellValue("URL");
                broTitleRow.AddCellValue("HTTP登陆尝试次数");
                broTitleRow.AddCellValue("HTTP登陆成功次数");
                broTitleRow.AddCellValue("HTTP完全加载次数");
                broTitleRow.AddCellValue("HTTP登陆成功率");
                broTitleRow.AddCellValue("HTTP登陆时延(S)");
                broTitleRow.AddCellValue("HTTP浏览成功率");
                broTitleRow.AddCellValue("HTTP浏览时长(S)");
                broTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                broTables.Add(broTitleRow);
                foreach (LteURLRegion dtlModel in resultFilesRegion)
                {
                    List<object> fileValues = new List<object>();
                    fileValues.Add(dtlModel.RegionName);
                    fileValues.Add(dtlModel.GridName);
                    foreach (LteURLBroURL dtlBro in dtlModel.Bros)
                    {
                        List<object> broValues = new List<object>(fileValues);
                        broValues.Add(dtlBro.URL);
                        broValues.Add(dtlBro.DisCount);
                        broValues.Add(dtlBro.DisSuc);
                        broValues.Add(dtlBro.Complete);
                        broValues.Add(dtlBro.DisSucRate * 100);
                        broValues.Add(dtlBro.DisDelay);
                        broValues.Add(dtlBro.SucRate * 100);
                        broValues.Add(dtlBro.Time);
                        broValues.Add(dtlBro.Speed);
                        NPOIRow broRow = new NPOIRow();
                        broRow.cellValues.AddRange(broValues);
                        broTables.Add(broRow);
                    }
                }

                List<NPOIRow> broEvtTables = new List<NPOIRow>();
                NPOIRow broEvtTitleRow = new NPOIRow();
                broEvtTitleRow.AddCellValue("图层类型");
                broEvtTitleRow.AddCellValue("网格信息");
                broEvtTitleRow.AddCellValue("文件名");
                broEvtTitleRow.AddCellValue("URL");
                broEvtTitleRow.AddCellValue("序号");
                broEvtTitleRow.AddCellValue("是否失败");
                broEvtTitleRow.AddCellValue("浏览时长(S)");
                broEvtTitleRow.AddCellValue("开始时间");
                broEvtTitleRow.AddCellValue("结束时间");
                broEvtTitleRow.AddCellValue("开始事件名称");
                broEvtTitleRow.AddCellValue("结束事件名称");
                broEvtTitleRow.AddCellValue("失败原因");
                broEvtTitleRow.AddCellValue("业务测试字节数");
                broEvtTables.Add(broEvtTitleRow);
                foreach (LteURLRegion dtlModel in resultFilesRegion)
                {
                    List<object> fileValues2 = new List<object>();
                    fileValues2.Add(dtlModel.RegionName);
                    fileValues2.Add(dtlModel.GridName);
                    foreach (LteURLBroURL dtlBro in dtlModel.Bros)
                    {
                        foreach (LteURLEvent evt in dtlBro.Events)
                        {
                            List<object> evtValues = new List<object>(fileValues2);
                            evtValues.Add(evt.FileName);
                            evtValues.Add(evt.URL);
                            evtValues.Add(evt.SN);
                            evtValues.Add(evt.IsFail);
                            evtValues.Add(evt.TimeSpan);
                            evtValues.Add(evt.StartTime);
                            evtValues.Add(evt.EndTime);
                            evtValues.Add(evt.StartName);
                            evtValues.Add(evt.EvtEndName);
                            evtValues.Add(evt.FailReason);
                            evtValues.Add(evt.Bytes);
                            NPOIRow evtRow = new NPOIRow();
                            evtRow.cellValues.AddRange(evtValues);
                            broEvtTables.Add(evtRow);
                        }
                    }
                }
                #endregion
                #region
                List<NPOIRow> downTables = new List<NPOIRow>();
                NPOIRow downTitleRow = new NPOIRow();
                downTitleRow.AddCellValue("图层类型");
                downTitleRow.AddCellValue("网格信息");
                downTitleRow.AddCellValue("URL");
                downTitleRow.AddCellValue("HTTP下载尝试次数");
                downTitleRow.AddCellValue("HTTP下载成功次数");
                downTitleRow.AddCellValue("HTTP下载成功率");
                downTitleRow.AddCellValue("HTTP下载掉线次数");
                downTitleRow.AddCellValue("HTTP下载掉线率");
                downTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                downTitleRow.AddCellValue("应用层下载速率(含掉线)(kbps)");
                downTables.Add(downTitleRow);
                foreach (LteURLRegion dtlModel in resultFilesRegion)
                {
                    List<object> fileValues1 = new List<object>();
                    fileValues1.Add(dtlModel.RegionName);
                    fileValues1.Add(dtlModel.GridName);
                    foreach (LteURLDowURL dtlDown in dtlModel.Downs)
                    {
                        List<object> downValues = new List<object>(fileValues1);
                        downValues.Add(dtlDown.URL);
                        downValues.Add(dtlDown.Dowcount);
                        downValues.Add(dtlDown.DowSuc);
                        downValues.Add(dtlDown.DowSucRate * 100);
                        downValues.Add(dtlDown.DowFail);
                        downValues.Add(dtlDown.DowFaiRate * 100);
                        downValues.Add(dtlDown.SucSpeed);
                        downValues.Add(dtlDown.Speed);

                        NPOIRow downRow = new NPOIRow();
                        downRow.cellValues.AddRange(downValues);
                        downTables.Add(downRow);
                    }
                }

                List<NPOIRow> downEvtTables = new List<NPOIRow>();
                NPOIRow downEvtTitleRow = new NPOIRow();
                downEvtTitleRow.AddCellValue("图层类型");
                downEvtTitleRow.AddCellValue("网格信息");
                downEvtTitleRow.AddCellValue("文件名");
                downEvtTitleRow.AddCellValue("URL");
                downEvtTitleRow.AddCellValue("序号");
                downEvtTitleRow.AddCellValue("是否失败");
                downEvtTitleRow.AddCellValue("下载时长(S)");
                downEvtTitleRow.AddCellValue("开始时间");
                downEvtTitleRow.AddCellValue("结束时间");
                downEvtTitleRow.AddCellValue("开始事件名称");
                downEvtTitleRow.AddCellValue("结束事件名称");
                downEvtTitleRow.AddCellValue("失败原因");
                downEvtTitleRow.AddCellValue("业务测试字节数");
                downEvtTables.Add(downEvtTitleRow);
                foreach (LteURLRegion dtlModel in resultFilesRegion)
                {
                    List<object> fileValues4 = new List<object>();
                    fileValues4.Add(dtlModel.RegionName);
                    fileValues4.Add(dtlModel.GridName);
                    foreach (LteURLDowURL dtlDown in dtlModel.Downs)
                    {
                        foreach (LteURLEvent evt in dtlDown.Events)
                        {
                            List<object> evtValues = new List<object>(fileValues4);
                            evtValues.Add(evt.FileName);
                            evtValues.Add(evt.URL);
                            evtValues.Add(evt.SN);
                            evtValues.Add(evt.IsFail);
                            evtValues.Add(evt.TimeSpan);
                            evtValues.Add(evt.StartTime);
                            evtValues.Add(evt.EndTime);
                            evtValues.Add(evt.StartName);
                            evtValues.Add(evt.EvtEndName);
                            evtValues.Add(evt.FailReason);
                            evtValues.Add(evt.Bytes);
                            NPOIRow evtRow = new NPOIRow();
                            evtRow.cellValues.AddRange(evtValues);
                            downEvtTables.Add(evtRow);
                        }
                    }
                }
                #endregion
                #region
                List<NPOIRow> videoTables = new List<NPOIRow>();
                NPOIRow videoTitleRow = new NPOIRow();
                videoTitleRow.AddCellValue("图层类型");
                videoTitleRow.AddCellValue("网格信息");
                videoTitleRow.AddCellValue("URL");
                videoTitleRow.AddCellValue("流媒体业务发起次数");
                videoTitleRow.AddCellValue("流媒体业务成功次数");
                videoTitleRow.AddCellValue("流媒体业务成功率");
                videoTitleRow.AddCellValue("流媒体加载时延(S)");
                videoTitleRow.AddCellValue("流媒体时长(S)");
                videoTitleRow.AddCellValue("流媒体卡顿时长(S)");
                videoTitleRow.AddCellValue("流媒体播放总时长(S)");
                videoTitleRow.AddCellValue("流媒体播放卡顿次数");
                videoTitleRow.AddCellValue("流媒体播放超时比例");
                videoTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                videoTitleRow.AddCellValue("流媒体加载速率(kbps)");
                videoTables.Add(videoTitleRow);
                foreach (LteURLRegion dtlModel in resultFilesRegion)
                {
                    List<object> fileValues5 = new List<object>();
                    fileValues5.Add(dtlModel.RegionName);
                    fileValues5.Add(dtlModel.GridName);
                    foreach (LteURLVideoURL dtlVideo in dtlModel.Videos)
                    {
                        List<object> videoValues = new List<object>(fileValues5);
                        videoValues.Add(dtlVideo.URL);
                        videoValues.Add(dtlVideo.ReqCount);
                        videoValues.Add(dtlVideo.SucCount);
                        videoValues.Add(dtlVideo.SucRate * 100);
                        videoValues.Add(dtlVideo.Delay);
                        videoValues.Add(dtlVideo.Time);
                        videoValues.Add(dtlVideo.RebufferTime);
                        videoValues.Add(dtlVideo.PlayTime);
                        videoValues.Add(dtlVideo.RebufferCount);
                        videoValues.Add(dtlVideo.TimeoutRate * 100);
                        videoValues.Add(dtlVideo.DownSpeed);
                        videoValues.Add(dtlVideo.LoadSpeed);

                        NPOIRow videoRow = new NPOIRow();
                        videoRow.cellValues.AddRange(videoValues);
                        videoTables.Add(videoRow);

                    }
                }

                List<NPOIRow> videoEvtTables = new List<NPOIRow>();
                NPOIRow videoEvtTitleRow = new NPOIRow();
                videoEvtTitleRow.AddCellValue("图层类型");
                videoEvtTitleRow.AddCellValue("网格信息");
                videoEvtTitleRow.AddCellValue("文件名");
                videoEvtTitleRow.AddCellValue("URL");
                videoEvtTitleRow.AddCellValue("序号");
                videoEvtTitleRow.AddCellValue("是否失败");
                videoEvtTitleRow.AddCellValue("时长(S)");
                videoEvtTitleRow.AddCellValue("开始时间");
                videoEvtTitleRow.AddCellValue("结束时间");
                videoEvtTitleRow.AddCellValue("开始事件名称");
                videoEvtTitleRow.AddCellValue("结束事件名称");
                videoEvtTitleRow.AddCellValue("失败原因");
                videoEvtTitleRow.AddCellValue("业务测试字节数");
                videoEvtTables.Add(videoEvtTitleRow);
                foreach (LteURLRegion dtlModel in resultFilesRegion)
                {
                    List<object> fileValues6 = new List<object>();
                    fileValues6.Add(dtlModel.RegionName);
                    fileValues6.Add(dtlModel.GridName);
                    foreach (LteURLVideoURL dtlVideo in dtlModel.Videos)
                    {
                        foreach (LteURLEvent evt in dtlVideo.Events)
                        {
                            List<object> evtValues = new List<object>(fileValues6);
                            evtValues.Add(evt.FileName);
                            evtValues.Add(evt.URL);
                            evtValues.Add(evt.SN);
                            evtValues.Add(evt.IsFail);
                            evtValues.Add(evt.TimeSpan);
                            evtValues.Add(evt.StartTime);
                            evtValues.Add(evt.EndTime);
                            evtValues.Add(evt.StartName);
                            evtValues.Add(evt.EvtEndName);
                            evtValues.Add(evt.FailReason);
                            evtValues.Add(evt.Bytes);
                            NPOIRow evtRow = new NPOIRow();
                            evtRow.cellValues.AddRange(evtValues);
                            videoEvtTables.Add(evtRow);
                        }
                    }
                }
                #endregion

                ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() { broTables, broEvtTables, downTables, downEvtTables, videoTables, videoEvtTables },
                    new List<string>() { "http浏览统计", "http浏览详情", "http下载统计", "http下载详情", "流媒体统计", "流媒体详情" });
            }
        }
     
        private List<LteURLModel> resultFiles = null;
        private List<LteURLRegion> resultFilesRegion = null;
        private bool isByRegion = false;

        public void FillData(object result,bool byRegion)
        {
            isByRegion = byRegion;
            if (isByRegion)
            {
                resultFilesRegion = result as List<LteURLRegion>;
                resultFiles = null;
                gvBroFile.Columns[3].Visible = false;
                gvBroFile.Columns[4].Visible = false;
                gvDowFile.Columns[3].Visible = false;
                gvDowFile.Columns[4].Visible = false;
                gvVideoFile.Columns[3].Visible = false;
                gvVideoFile.Columns[4].Visible = false;
                gcBrowse.DataSource = resultFilesRegion;
                gcBrowse.RefreshDataSource();
                gcDownload.DataSource = resultFilesRegion;
                gcDownload.RefreshDataSource();
                gcVideo.DataSource = resultFilesRegion;
                gcVideo.RefreshDataSource();
            }
            else
            {
                gvBroFile.Columns[1].Visible = false;
                gvBroFile.Columns[2].Visible = false;
                gvDowFile.Columns[1].Visible = false;
                gvDowFile.Columns[2].Visible = false;
                gvVideoFile.Columns[1].Visible = false;
                gvVideoFile.Columns[2].Visible = false;
                
                resultFiles = result as List<LteURLModel>;
                resultFilesRegion = null;
                gcBrowse.DataSource = resultFiles;
                gcBrowse.RefreshDataSource();
                gcDownload.DataSource = resultFiles;
                gcDownload.RefreshDataSource();
                gcVideo.DataSource = resultFiles;
                gcVideo.RefreshDataSource();
            }            
        }

        private void miReplayFile_Click(object sender, EventArgs e)
        {
            LteURLEvent evt = null;
            if (gvBroDet.IsFocusedView)
            {
                evt = gvBroDet.GetFocusedRow() as LteURLEvent;
            }
            else if (gvDowDet.IsFocusedView)
            {
                evt = gvDowDet.GetFocusedRow() as LteURLEvent;
            }
            else if (gvVideoDet.IsFocusedView)
            {
                evt = gvVideoDet.GetFocusedRow() as LteURLEvent;
            }
            if (evt != null)
            {
                DateTime begin = DateTime.Parse(evt.StartTime);
                DateTime end = DateTime.Parse(evt.EndTime);
                FileReplayer.ReplayOnePart(evt.EventStart, new TimePeriod(begin, end));
            }
        }

    }
}
