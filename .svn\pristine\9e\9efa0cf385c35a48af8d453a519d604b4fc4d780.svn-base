﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc.ZTNRDominantAreaAna
{
    public static class NRDominantAreaConfig
    {
        private static string configPath = Application.StartupPath + @"\config\NRDominantAreaConfig.xml";

        public static NRDominantAreaAnaCondition LoadConfig()
        {
            NRDominantAreaAnaCondition condition = new NRDominantAreaAnaCondition();

            try
            {
                if (!File.Exists(configPath))
                {
                    SaveConfig(condition);
                    return condition;
                }

                var xcfg = new XmlConfigFile(configPath);
                if (xcfg.Load())
                {
                    XmlElement config = xcfg.GetConfig("Configs");
                    condition.SceneExcelPath = getValidPath(xcfg, config, "SceneExcelPath");
                    condition.ShapePath = getValidPath(xcfg, config, "ShapePath");
                    condition.RoadPath = getValidData(xcfg, config, "RoadPath", "");

                    DateTime sTime = getValidData(xcfg, config, "StartTime", DateTime.Now.AddMonths(-1));
                    DateTime eTime = getValidData(xcfg, config, "EndTime", DateTime.Now);
                    int timeThreshold = getValidData(xcfg, config, "TimeThreshold", 90);
                    condition.SetCondition(sTime, eTime, timeThreshold);

                    List<object> list = xcfg.GetItemValue(config, "Districts") as List<object>;
                    if (list != null)
                    {
                        foreach (object o in list)
                        {
                            condition.DistrictIDs.Add((int)o);
                        }
                    }
                    condition.TestPointRadio = getValidData(xcfg, config, "TestPointRadio", 90);
                }
                return condition;
            }
            catch
            {
                return new NRDominantAreaAnaCondition();
            }
        }

        private static T getValidData<T>(XmlConfigFile xcfg, XmlElement config, string name, T defaultData)
        {
            object obj = xcfg.GetItemValue(config, name);
            if (obj != null)
            {
                return (T)obj;
            }
            return defaultData;
        }

        private static string getValidPath(XmlConfigFile xcfg, XmlElement config, string name)
        {
            object obj = xcfg.GetItemValue(config, name);
            if (obj != null)
            {
                string path = obj.ToString();
                if (File.Exists(path))
                {
                    return path;
                }
            }
            return "";
        }

        public static void SaveConfig(NRDominantAreaAnaCondition condition)
        {
            var newConfig = new XmlConfigFile();
            XmlElement cfg = newConfig.AddConfig("Configs");
            newConfig.AddItem(cfg, "SceneExcelPath", condition.SceneExcelPath);
            newConfig.AddItem(cfg, "ShapePath", condition.ShapePath);
            newConfig.AddItem(cfg, "RoadPath", condition.RoadPath);
            newConfig.AddItem(cfg, "StartTime", condition.FilePeriod.BeginTime);
            newConfig.AddItem(cfg, "EndTime", condition.FilePeriod.EndTime);
            newConfig.AddItem(cfg, "TestPointRadio", condition.TestPointRadio);
            newConfig.AddItem(cfg, "Districts", condition.DistrictIDs);
            newConfig.AddItem(cfg, "TimeThreshold", condition.TimeThreshold);
            newConfig.Save(configPath);
        }

        static string curLogPath = "";
        static string curDate = "";
        public static void WriteLog(string info, string type = "Info")
        {
            string path = getLogPath();
            if (!File.Exists(path))
            {
                File.Create(path).Close();
            }
            using (StreamWriter sw = File.AppendText(path))
            {
                sw.Write($"[{DateTime.Now}][{type}][{info}]\r\n");
                sw.Flush();
                sw.Close();
            }
        }

        private static string getLogPath()
        {
            string strDate = DateTime.Now.ToString("yyyyMMdd");
            if (curDate == strDate)
            {
                return curLogPath;
            }
            curDate = strDate;
            StringBuilder sb = new StringBuilder(Application.StartupPath);
            sb.Append(@"\BackGroundLog\NRDominantArea\");
            string directory = sb.ToString();
            sb.Append(strDate);
            sb.Append("-5G优势区域分析.txt");
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            curLogPath = sb.ToString();
            return curLogPath.ToString();
        }
    }
}
