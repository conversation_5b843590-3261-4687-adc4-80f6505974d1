﻿using Adrian.PhotoX.Lib;
using MapWinGIS;
using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Func.GridQueryHistogram;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.Serialization;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Xml;

namespace MasterCom.RAMS.Func
{
    [Serializable()]
    public class MapGridLayer : LayerBase, ISerializable, IKMLExport
    {
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public MapGridLayer(MapOperation mp, string name)
            : base(name)
        {
        }
        public bool IsGridModeChanged { get; set; } = true;

        public Pen PenSelected { get; set; } = Pens.Red;
        public bool SpreadBlank { get; set; } = true;
        public static int OpaqueValue { get; set; } = 255;//0全透明   255 不透明
        public static int GaussRadius { get; set; } = 10;
        public bool SmoothGrid { get; set; } = false;
        public List<GridColorModeItem> GridColorModes { get; set; } = new List<GridColorModeItem>();
        public int CurModeIndex
        {
            get
            {
                if (CurUsingColorMode == null)
                {
                    return -1;
                }
                else
                {
                    for (int i = 0; i < GridColorModes.Count; i++)
                    {
                        if (GridColorModes[i] == CurUsingColorMode)
                        {
                            return i;
                        }
                    }
                    return -1;
                }
            }
            set
            {
                if (value >= 0 && value < GridColorModes.Count)
                {
                    CurUsingColorMode = GridColorModes[value];
                }
            }
        }
        public GridColorModeItem CurUsingColorMode { get; set; }
        public List<GridColorModeItem> CurUsingColorModeList { get; set; }
        public List<GridColorModeItem> ContrastColorModes { get; set; } = new List<GridColorModeItem>();
        public GridColorFixed CurUsingGridColorFixed { get; set; }

        /// <summary>
        /// 栅格汇聚
        /// </summary>
        public bool CombinedGridUnit { get; set; } = false;
        public bool CombinedShowResult { get; set; } = false;
        public bool ShowStreetInfo { get; set; } = true;
        public List<CombinedColorUnit> CombinedColorUnitList { get; private set; } = new List<CombinedColorUnit>();
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
            info.AddValue("SmoothGrid", SmoothGrid);
            info.AddValue("SpreadBlank", SpreadBlank);
            info.AddValue("ColorModes", GridColorModes);
            info.AddValue("CurSelColorMode", CurUsingColorMode);
            info.AddValue("GridSizeFactor", GridSizeFactor);
            info.AddValue("IsGridModeChanged", IsGridModeChanged);
            info.AddValue("ContrastColorModes", ContrastColorModes);
            info.AddValue("DrawGridBorder", DrawGridBorder);
        }
        public static bool NeedFreshFullImg { get; set; } = false;//是否数据发生了变化需要更新整个栅格
        public void SetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (MainModel == null || ((MainModel.CurGridCoverData == null || MainModel.CurGridCoverData.Count == 0) && (MainModel.CurGridColorUnitMatrix == null)))
            {
                SetInitData(info);
            }
            else
            {
                setChangedData(info);
            }

            IsGridModeChanged = (bool)info.GetValue("IsGridModeChanged", typeof(bool));
            ContrastColorModes = (List<GridColorModeItem>)info.GetValue("ContrastColorModes", typeof(List<GridColorModeItem>));

        }

        private void setChangedData(SerializationInfo info)
        {
            bool changed = false;
            bool smoothGrid_T = (bool)info.GetValue("SmoothGrid", typeof(bool));
            if (smoothGrid_T != SmoothGrid)
            {
                SmoothGrid = smoothGrid_T;
                changed = true;
            }
            int gridSize_T = (int)info.GetValue("GridSizeFactor", typeof(int));
            if (gridSize_T != gridSizeFactor)
            {
                gridSizeFactor = gridSize_T;
                changed = true;
            }

            bool spreadBlack_T = (bool)info.GetValue("SpreadBlank", typeof(bool));
            if (spreadBlack_T != SpreadBlank)
            {
                SpreadBlank = spreadBlack_T;
                changed = true;
            }
            try
            {
                bool drawBorder_T = (bool)info.GetValue("DrawGridBorder", typeof(bool));
                if (drawBorder_T != DrawGridBorder)
                {
                    DrawGridBorder = drawBorder_T;
                    changed = true;
                }
            }
            catch
            {
                //continue
            }
            GridColorModes = (List<GridColorModeItem>)info.GetValue("ColorModes", typeof(List<GridColorModeItem>));
            GridColorModeItem mode_T = (GridColorModeItem)info.GetValue("CurSelColorMode", typeof(GridColorModeItem));
            if (mode_T != null && CurUsingColorMode != null && !CurUsingColorMode.Name.Equals(mode_T.Name))
            {
                changed = true;
            }
            CurUsingColorMode = mode_T;
            NeedFreshFullImg = changed;
        }

        private void SetInitData(SerializationInfo info)
        {
            NeedFreshFullImg = false;
            SmoothGrid = (bool)info.GetValue("SmoothGrid", typeof(bool));
            SpreadBlank = (bool)info.GetValue("SpreadBlank", typeof(bool));
            GridSizeFactor = (int)info.GetValue("GridSizeFactor", typeof(int));
            try
            {
                DrawGridBorder = (bool)info.GetValue("DrawGridBorder", typeof(bool));
            }
            catch
            {
                //continue
            }
            GridColorModes = (List<GridColorModeItem>)info.GetValue("ColorModes", typeof(List<GridColorModeItem>));
            CurUsingColorMode = (GridColorModeItem)info.GetValue("CurSelColorMode", typeof(GridColorModeItem));
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> colorModesParams = new List<object>();
                param["ColorModes"] = colorModesParams;
                foreach (GridColorModeItem modeInfo in GridColorModes)
                {
                    colorModesParams.Add(modeInfo.Param);
                }
                param["CurModeIndex"] = CurModeIndex;
                param["SmoothGrid"] = SmoothGrid;
                param["SpreadBlank"] = SpreadBlank;
                param["OpaqueValue"] = OpaqueValue;
                param["SmoothRadius"] = GaussRadius;
                param["GridSizeFactor"] = GridSizeFactor;
                colorModesParams = new List<object>();
                foreach (GridColorModeItem modeInfo in ContrastColorModes)
                {
                    colorModesParams.Add(modeInfo.Param);
                }
                param["ContrastColorModes"] = colorModesParams;
                return param;
            }
            set
            {
                List<object> colorModesParams = new List<object>();
                if (GridColorManager.Instance.LoadCfg())
                {
                    foreach (GridColorModeItem item in GridColorManager.Instance.GridColorItems)
                    {
                        colorModesParams.Add(item.Param);
                    }
                }
                else
                {
                    colorModesParams = (List<object>)value["ColorModes"];
                }

                GridColorModes.Clear();
                foreach (object o in colorModesParams)
                {
                    Dictionary<string, object> modeInfoParam = (Dictionary<string, object>)o;
                    GridColorModeItem modeInfo = new GridColorModeItem();
                    modeInfo.Param = modeInfoParam;
                    GridColorModes.Add(modeInfo);
                }
                int idx = (int)value["CurModeIndex"];
                CurModeIndex = idx;
                if (CurModeIndex < 0)
                {
                    CurModeIndex = 0;
                }
                SmoothGrid = (bool)value["SmoothGrid"];
                SpreadBlank = (bool)value["SpreadBlank"];
                OpaqueValue = (int)value["OpaqueValue"];
                GaussRadius = (int)value["SmoothRadius"];
                GridSizeFactor = (int)value["GridSizeFactor"];
                if (value.ContainsKey("ContrastColorModes"))
                {
                    colorModesParams = (List<object>)value["ContrastColorModes"];
                    ContrastColorModes.Clear();
                    foreach (object o in colorModesParams)
                    {
                        Dictionary<string, object> modeInfoParam = (Dictionary<string, object>)o;
                        GridColorModeItem modeInfo = new GridColorModeItem();
                        modeInfo.Param = modeInfoParam;
                        ContrastColorModes.Add(modeInfo);
                    }
                }
            }
        }

        #region 生成图像
        public Bitmap GetBitmap()
        {
            return img;
        }
        Bitmap img = null;
        public void prepareImage()
        {
            try
            {
                if (img != null)
                {
                    img.Dispose();
                    img = null;
                }
                if (ColorMatrix == null || ColorMatrix.RowCount == 0 || ColorMatrix.ColCount == 0)
                {
                    return;
                }
                if (ColorMatrix.RowCount > 1000 || ColorMatrix.ColCount > 1000)
                {//栅格矩阵太大，创建Bitmap容易内存溢出，此时逐个GridUnit画图
                    img = null;
                    foreach (ColorUnit cu in ColorMatrix)
                    {
                        if (cu != null && cu.Status != 0)
                        {
                            getGridUnitColor(cu);
                        }
                    }
                }
                else
                {
                    drawImage();
                }
            }
            catch (Exception e)
            {
                log.Error("Prepare Image Error :" + e.Message);
            }
        }

        private void drawImage()
        {
            img = new Bitmap(ColorMatrix.ColCount * xGapPixel, yGapPixel * ColorMatrix.RowCount, System.Drawing.Imaging.PixelFormat.Format32bppArgb);
            Graphics graphics = Graphics.FromImage(img);
            foreach (ColorUnit cu in ColorMatrix)
            {
                if (cu == null || cu.Status == 0)
                {
                    continue;
                }
                int c = cu.ColIdx - ColorMatrix.MinColIdx;
                int r = ColorMatrix.MaxRowIdx - cu.RowIdx;
                drawGridColorUnitToImg(cu, graphics, c * xGapPixel, r * yGapPixel, xGapPixel, yGapPixel);
            }
            if (SmoothGrid)
            {
                GaussianBlur blur = new GaussianBlur(GaussRadius);
                Bitmap nBluredImg = blur.ProcessImage(img);
                img.Dispose();
                img = nBluredImg;
            }
            graphics.Save();
            graphics.Dispose();
        }
        #endregion

        internal int MakeShpFile(string fileName, int coverRadius, bool fixColor, Color colorSelected, bool compareBTS)
        {
            if (ColorMatrix == null || ColorMatrix.RowCount == 0 || ColorMatrix.ColCount == 0)
            {
                return 0;
            }
            if (btsLonLatList.Count > 0)
            {
                foreach (ColorUnit cu in ColorMatrix)
                {
                    setColorUnitValid(coverRadius, cu);
                }
            }
            return MakeShpFile(fileName, fixColor, colorSelected, compareBTS);
        }

        private void setColorUnitValid(int coverRadius, ColorUnit cu)
        {
            if (cu != null)
            {
                cu.invalid = false;
                foreach (BTSLonLat bts in btsLonLatList)
                {
                    if (MathFuncs.GetDistance(bts.longitude, bts.latitude, cu.CenterLng, cu.CenterLat) <= coverRadius)
                    {
                        cu.invalid = true;
                        break;
                    }
                }
            }
        }

        internal int MakeShpFile(string fileName, bool fixColor, Color colorSelected, bool compareBTS)
        {
            try
            {
                Shapefile shpFile = new Shapefile();
                bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }
                int idIdx = 0;
                int fiColor = idIdx++;
                int fiLongitude = idIdx++;
                int fiLatitude = idIdx++;
                int fiValue = idIdx;
                ShapeHelper.InsertNewField(shpFile, "Color", FieldType.INTEGER_FIELD, 10, 30, ref fiColor);
                ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLongitude);
                ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLatitude);
                ShapeHelper.InsertNewField(shpFile, "Value", FieldType.DOUBLE_FIELD, 10, 30, ref fiValue);
                if (ColorMatrix == null || ColorMatrix.RowCount == 0 || ColorMatrix.ColCount == 0)
                {
                    return 0;
                }
                int numShp = 0;
                foreach (ColorUnit cu in ColorMatrix)
                {
                    if (cu == null || cu.Status == 0 || cu.color == Color.Empty)
                    {
                        continue;
                    }
                    if (compareBTS && cu.invalid)
                    {
                        continue;
                    }
                    Color color = cu.color;
                    if (fixColor)
                    {
                        color = colorSelected;
                    }
                    numShp++;
                    shpFile.EditInsertShape(ShapeHelper.CreateRectShape(cu.LTLng, cu.LTLat, cu.BRLng, cu.BRLat), ref numShp);
                    shpFile.EditCellValue(fiColor, numShp, ColorTranslator.ToOle(color));
                    shpFile.EditCellValue(fiLongitude, numShp, cu.CenterLng);
                    shpFile.EditCellValue(fiLatitude, numShp, cu.CenterLat);
                    shpFile.EditCellValue(fiValue, numShp, (double)cu.retv);
                }
                ShapeHelper.DeleteShpFile(fileName);
                if (!shpFile.SaveAs(fileName, null))
                {
                    MessageBox.Show("保存文件失败！" + shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    shpFile.Close();
                    return -1;
                }
                shpFile.Close();
                return 1;
            }
            catch
            {
                return -1;
            }
        }

        public class BTSLonLat
        {
            public BTSLonLat(string name, double longitude, double latitude)
            {
                this.name = name;
                this.longitude = longitude;
                this.latitude = latitude;
            }
            public string name { get; set; }
            public double longitude { get; set; }
            public double latitude { get; set; }
        }
        private List<BTSLonLat> btsLonLatList = new List<BTSLonLat>();
        public List<BTSLonLat> BTSLonLatList
        {
            get { return btsLonLatList; }
        }

        private int xGapPixel = 3;
        private int yGapPixel = 3;
        public int XGapPixel
        {
            get
            {
                return xGapPixel;
            }
        }
        public int YGapPixel
        {
            get
            {
                return yGapPixel;
            }
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            try
            {
                doDrawCoverImg(updateRect, graphics);
                if (curMapType == LayerMapType.MTGis)
                {
                    if (mainModel.CurStatGridPoints != null)
                    {
                        drawStatGird(graphics);//小区KPI，覆盖显示
                    }
                    if (mainModel.ScanInterfereResults != null && mainModel.ScanInterfereResults.Count > 0)
                    {
                        drawScanInterfereGird(graphics);//扫频同频干扰栅格
                    }
                    if (mainModel.ScanShearResults != null && mainModel.ScanShearResults.Count > 0)
                    {
                        drawScanShearGird(graphics);//扫频同频错切栅格
                    }
                    if (mainModel.CurCellWeakCoverByGrid != null)
                    {
                        drawCellWeakCoverByGrid(graphics);//弱覆盖小区
                    }
                    drawSelGrid(graphics);
                }
            }
            catch (Exception e)
            {
                log.Error("Draw Grid Cover Error" + e.Message);
            }
        }

        private void drawSelGrid(Graphics graphics)
        {
            if (selColorUnit == null)
            {
                return;
            }
            PointF ltPntF;
            PointF brPntF;
            gisAdapter.ToDisplay(new DbPoint(selColorUnit.LTLng, selColorUnit.LTLat), out ltPntF);
            gisAdapter.ToDisplay(new DbPoint(selColorUnit.BRLng, selColorUnit.BRLat), out brPntF);
            graphics.DrawRectangle(Pens.Red, ltPntF.X, ltPntF.Y, brPntF.X - ltPntF.X, brPntF.Y - ltPntF.Y);
        }

        private void drawStatGird(Graphics graphics)
        {
            foreach (DbPoint dp in mainModel.CurStatGridPoints)
            {
                PointF point;
                gisAdapter.ToDisplay(dp, out point);
                graphics.TranslateTransform(point.X, point.Y);
                graphics.ScaleTransform((float)(10000 / mapScale), (float)(10000 / mapScale));
                graphics.DrawRectangle(Pens.Black, 0, 0, 15, 15);
                graphics.ResetTransform();
            }
        }

        private void drawScanInterfereGird(Graphics graphics)
        {
            foreach (ScanInterfereResult result in mainModel.ScanInterfereResults)
            {
                foreach (MasterCom.RAMS.Net.GridItem grid in result.gridsOne)
                {
                    DbPoint dp = new DbPoint(grid.LTLng, grid.LTLat);
                    PointF point;
                    gisAdapter.ToDisplay(dp, out point);
                    graphics.TranslateTransform(point.X, point.Y);
                    graphics.ScaleTransform((float)(10000 / mapScale), (float)(10000 / mapScale));
                    graphics.DrawRectangle(Pens.Aqua, 0, 0, 15, 15);
                    graphics.ResetTransform();
                }
                foreach (MasterCom.RAMS.Net.GridItem grid in result.gridsTwo)
                {
                    DbPoint dp = new DbPoint(grid.LTLng, grid.LTLat);
                    PointF point;
                    gisAdapter.ToDisplay(dp, out point);
                    graphics.TranslateTransform(point.X, point.Y);
                    graphics.ScaleTransform((float)(10000 / mapScale), (float)(10000 / mapScale));
                    graphics.DrawRectangle(Pens.Aqua, 0, 0, 15, 15);
                    graphics.ResetTransform();
                }
            }
        }

        private void drawScanShearGird(Graphics graphics)
        {
            foreach (ScanShearResult result in mainModel.ScanShearResults)
            {
                foreach (MasterCom.RAMS.Net.GridItem grid in result.grids)
                {
                    DbPoint dp = new DbPoint(grid.LTLng, grid.LTLat);
                    PointF point;
                    gisAdapter.ToDisplay(dp, out point);
                    graphics.TranslateTransform(point.X, point.Y);
                    graphics.ScaleTransform((float)(10000 / mapScale), (float)(10000 / mapScale));
                    graphics.DrawRectangle(Pens.Aqua, 0, 0, 15, 15);
                    graphics.ResetTransform();
                }
            }
        }

        private void drawCellWeakCoverByGrid(Graphics graphics)
        {
            float width = 0;
            float heigh = 0;
            foreach (var grid in MainModel.CurCellWeakCoverByGrid.GridList)
            {
                DbPoint dp = new DbPoint(grid.longitude, grid.latitude);
                PointF point;
                gisAdapter.ToDisplay(dp, out point);
                if (width == 0 || heigh == 0)
                {
                    dp = new DbPoint(grid.longitude + CD.ATOM_SPAN_LONG, grid.latitude + CD.ATOM_SPAN_LAT);
                    PointF point2;
                    gisAdapter.ToDisplay(dp, out point2);
                    width = point2.X - point.X;
                    heigh = point.Y - point2.Y;
                }
                graphics.DrawRectangle(Pens.Aqua, point.X, point.Y, width, heigh);
            }
        }

        private void doDrawCoverImg(Rectangle updateRect, Graphics graphics)
        {
            if (ColorMatrix == null || ColorMatrix.RowCount == 0 || ColorMatrix.ColCount == 0)
            {
                return;
            }
            DbRect viewRect;
            gisAdapter.FromDisplay(updateRect, out viewRect);
            if (img != null)
            {
                DbRect imgDRect = ColorMatrix.GetBounds();
                DbRect vImgRect = MapOperation.IntersectRect(imgDRect, viewRect);//需要显示的图像经纬度跨度

                DbPoint ltPointD = new DbPoint(vImgRect.x1, vImgRect.y2);
                PointF ltPoint;
                gisAdapter.ToDisplay(ltPointD, out ltPoint);
                DbPoint brPointD = new DbPoint(vImgRect.x2, vImgRect.y1);
                PointF brPoint;
                gisAdapter.ToDisplay(brPointD, out brPoint);

                PointF ulCorner = new PointF(ltPoint.X, ltPoint.Y);
                PointF urCorner = new PointF(brPoint.X, ltPoint.Y);
                PointF llCorner = new PointF(ltPoint.X, brPoint.Y);
                PointF[] destPara = { ulCorner, urCorner, llCorner };

                int imgXSize = ColorMatrix.ColCount * xGapPixel;
                int imgYSize = ColorMatrix.RowCount * yGapPixel;

                float x0 = (float)((vImgRect.x1 - imgDRect.x1) * imgXSize / imgDRect.Width());
                float y0 = (float)((imgDRect.y2 - vImgRect.y2) * imgYSize / imgDRect.Height());

                float w0 = (float)(vImgRect.Width() * imgXSize / imgDRect.Width());
                float h0 = (float)(vImgRect.Height() * imgYSize / imgDRect.Height());

                RectangleF srcRect = new RectangleF(x0, y0, w0, h0);
                GraphicsUnit units = GraphicsUnit.Pixel;

                float opqF = (float)OpaqueValue / 255;
                float[][] ptsArray ={ 
                        new float[] {1, 0, 0, 0, 0},
                        new float[] {0, 1, 0, 0, 0},
                        new float[] {0, 0, 1, 0, 0},
                    new float[] {0, 0, 0, opqF, 0}, //注意：此处为0.5f，图像为半透明
                    new float[] {0, 0, 0, 0, 1}};
                ColorMatrix clrMatrix = new ColorMatrix(ptsArray);
                ImageAttributes imgAttributes = new ImageAttributes();
                //设置图像的颜色属性
                imgAttributes.SetColorMatrix(clrMatrix, ColorMatrixFlag.Default,
                ColorAdjustType.Bitmap);
                graphics.DrawImage(img, destPara, srcRect, units, imgAttributes);
            }
            else if (ColorMatrix != null)
            {
                foreach (ColorUnit cu in ColorMatrix)
                {
                    if (cu.Within(viewRect))
                    {
                        DbPoint ltPoint = new DbPoint(cu.LTLng, cu.LTLat);
                        PointF pointLt;
                        this.gisAdapter.ToDisplay(ltPoint, out pointLt);
                        DbPoint brPoint = new DbPoint(cu.BRLng, cu.BRLat);
                        PointF pointBr;
                        this.gisAdapter.ToDisplay(brPoint, out pointBr);
                        Brush brush = new SolidBrush(cu.color);
                        graphics.FillRectangle(brush, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);

                    }
                }
            }

            if (CombinedGridUnit && CombinedColorUnitList.Count > 0)
            {
                drawCombinedGridUnit(updateRect, graphics);
            }
        }

        private void drawCombinedGridUnit(Rectangle updateRect, Graphics graphics)
        {
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);
            foreach (CombinedColorUnit item in CombinedColorUnitList)
            {
                foreach (ColorUnit cu in item.Matrix)
                {
                    DbPoint ltPoint = new DbPoint(cu.LTLng, cu.LTLat);
                    PointF pointLt;
                    this.gisAdapter.ToDisplay(ltPoint, out pointLt);
                    DbPoint brPoint = new DbPoint(cu.BRLng, cu.BRLat);
                    PointF pointBr;
                    this.gisAdapter.ToDisplay(brPoint, out pointBr);
                    DbPoint rtPoint = new DbPoint(cu.BRLng, cu.LTLat);
                    PointF pointRt;
                    this.gisAdapter.ToDisplay(rtPoint, out pointRt);
                    DbPoint blPoint = new DbPoint(cu.LTLng, cu.BRLat);
                    PointF pointBl;
                    this.gisAdapter.ToDisplay(blPoint, out pointBl);
                    Pen pen = new Pen(cu.combineColorRange.color, 2);
                    drawCombinedGridUnitLine(graphics, cu.top, pointLt, pointRt, pen);
                    drawCombinedGridUnitLine(graphics, cu.bottom, pointBl, pointBr, pen);
                    drawCombinedGridUnitLine(graphics, cu.right, pointBr, pointRt, pen);
                    drawCombinedGridUnitLine(graphics, cu.left, pointLt, pointBl, pen);
                }
            }
            MainModel.MainForm.FireCombineGridUnitForm(CombinedShowResult);
            if (CombinedShowResult)
            {
                CombinedShowResult = false;
            }
        }

        private void drawCombinedGridUnitLine(Graphics graphics, bool isDraw, PointF pointStart, PointF pointEnd, Pen pen)
        {
            if (isDraw)
            {
                graphics.DrawLine(pen, pointStart, pointEnd);
            }
        }

        private bool outputCoverFullImg(string filename, ref double ltLong, ref double brLong, ref double ltLat, ref double brLat)
        {
            if (ColorMatrix == null || ColorMatrix.RowCount == 0 || ColorMatrix.ColCount == 0)
            {
                return false;
            }
            if (img != null)
            {
                try
                {
                    DbRect bounds = ColorMatrix.GetBounds();
                    if (bounds == null)
                    {
                        return false;
                    }
                    ltLong = bounds.x1;
                    ltLat = bounds.y2;
                    brLong = bounds.x2;
                    brLat = bounds.y1;
                    img.Save(filename);
                    return true;
                }
                catch (Exception e)
                {
                    log.Warn("Output Img Error" + e.Message);
                    return false;
                }
            }
            return false;
        }

        private void getGridUnitColor(ColorUnit cu)
        {
            if (!NeedFreshFullImg)
            {//重新获取颜色
                if (CurUsingColorMode.rangeMode)
                {
                    cu.color = CurUsingColorMode.GetColor(cu.retv);
                }
                else
                {
                    cu.color = CurUsingColorMode.GetDicColor(cu.retv);
                }
            }
        }

        private void drawGridColorUnitToImg(ColorUnit cu, Graphics graphics, int left, int top, int width, int height)
        {
            getGridUnitColor(cu);
            graphics.FillRectangle(new SolidBrush(cu.color), left, top, width, height);
            if (DrawGridBorder && xGapPixel > 10)
            {
                graphics.DrawRectangle(Pens.Black, left, top, width, height);
            }
        }
        private ColorUnit selColorUnit = null;
        private int gridSizeFactor = 1;

        public static double _GRID_SPAN_LONG_EACH { get; set; } = CD.ATOM_SPAN_LONG;//底层2*20米的精度跨度大小 2*0.0001951
        public static double _GRID_SPAN_LAT_EACH { get; set; } = CD.ATOM_SPAN_LAT;//底层2*20米的纬度跨度大小 2*0.00017986
        public double GRID_SPAN_LONG { get; set; } = CD.ATOM_SPAN_LONG;//当前运算中使用的值
        public double GRID_SPAN_LAT { get; set; } = CD.ATOM_SPAN_LAT;//当前运算中使用的值
        public GridMatrix<ColorUnit> ColorMatrix { get; private set; }

        public int GridSizeFactor
        {
            get
            {
                return gridSizeFactor;
            }
            set
            {
                gridSizeFactor = value;
                GRID_SPAN_LONG = _GRID_SPAN_LONG_EACH * value;
                GRID_SPAN_LAT = _GRID_SPAN_LAT_EACH * value;
            }
        }

        public bool DrawGridBorder { get; set; } = false;

        private bool useWaitBox = false;

        private void doPrepareColorMatrixThread()
        {
            if (!NeedFreshFullImg)
            {
                return;
            }
            try
            {
                DbRect bounds = new DbRect();
                bool first = true;
                List<GridPartParam> gridPartsList = MainModel.CurGridCoverData;
                if (gridPartsList.Count == 0)
                {
                    return;
                }
                if (CurUsingColorMode != null)
                {
                    CurUsingColorMode.ResetDic();
                }
                else
                {
                    MessageBox.Show("当前没有设置好的栅格显示样式，请在栅格显示设置中添加!");
                    return;
                }

                if (useWaitBox)
                {
                    WaitBox.Text = "正在判断数据边界...";
                }

                getBoundsByGridPartParam(ref bounds, ref first, gridPartsList);
                //glau
                int columnCount = (int)(bounds.Width() / GRID_SPAN_LONG + 0.51);
                int rowCount = (int)(bounds.Height() / GRID_SPAN_LAT + 0.51);
                xGapPixel = yGapPixel = getGridSpanPixel(rowCount, columnCount);
                ColorMatrix = new GridMatrix<ColorUnit>();
                if (useWaitBox)
                {
                    WaitBox.Text = "正在生成栅格渲染结构...";
                }
                if (useWaitBox)
                {
                    WaitBox.Text = "正在合并栅格数据...";
                }

                combineGridPartParam(gridPartsList);
                //针对整个矩阵，生成颜色
                if (useWaitBox)
                {
                    WaitBox.Text = "正在生成颜色...";
                }

                dealColorMatrix();

                doSpreadBlank();
                prepareImage();
                NeedFreshFullImg = false;
            }
            catch
            {
                //continue
            }
            finally
            {
                try
                {
                    if (useWaitBox)
                    {
                        Thread.Sleep(2000);
                        WaitBox.Close();
                    }
                }
                catch
                {
                    //continue
                }

            }
        }

        private void getBoundsByGridPartParam(ref DbRect bounds, ref bool first, List<GridPartParam> gridPartsList)
        {
            foreach (GridPartParam u in gridPartsList)
            {
                if (first)
                {
                    bounds = u.Bounds;
                    first = false;
                }
                else
                {
                    setBounds(bounds, u);
                }
            }
        }

        private void setBounds(DbRect bounds, GridPartParam u)
        {
            if (bounds.x1 > u.LTLng)
            {
                bounds.x1 = u.LTLng;
            }
            if (bounds.x2 < u.BRLng)
            {
                bounds.x2 = u.BRLng;
            }
            if (bounds.y1 > u.BRLat)
            {
                bounds.y1 = u.BRLat;
            }
            if (bounds.y2 < u.LTLat)
            {
                bounds.y2 = u.LTLat;
            }
        }

        private void combineGridPartParam(List<GridPartParam> gridPartsList)
        {
            //合并GridPartParam为GridUnit
            foreach (GridPartParam pu in gridPartsList)
            {
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(pu.CenterLng, pu.CenterLat, out rAt, out cAt);
                ColorUnit cu = ColorMatrix[rAt, cAt];
                if (cu == null)
                {
                    double ltLng, ltLat;
                    GridHelper.GetLeftTopByCustomSizeGridIndex(1, rAt, cAt, out ltLng, out ltLat);
                    cu = new ColorUnit();
                    cu.LTLng = ltLng;
                    cu.LTLat = ltLat;
                    ColorMatrix[rAt, cAt] = cu;
                }
                /*Need2BePerfect_Qiujianwei
                * 待优化
                */
                //decideSimpleShow(cu, pu)
            }
        }

        private void doPrepareColorMatrixThread_ByMatrix()
        {
            if (!NeedFreshFullImg)
            {
                return;
            }
            try
            {
                ColorMatrix = GridMatrix<ColorUnit>.RefactorMatrixBySize(gridSizeFactor, MainModel.CurGridColorUnitMatrix);
                if (ColorMatrix.RowCount == 0)
                {
                    NeedFreshFullImg = false;
                    img = null;
                    return;
                }
                if (CurUsingColorMode != null)
                {
                    CurUsingColorMode.ResetDic();
                }
                else
                {
                    MessageBox.Show("当前没有设置好的栅格显示样式，请在栅格显示设置中添加!");
                    return;
                }
                xGapPixel = yGapPixel = getGridSpanPixel(ColorMatrix.RowCount, ColorMatrix.ColCount);

                if (useWaitBox)
                {
                    WaitBox.Text = "正在生成栅格渲染结构...";
                }

                dealColorMatrix();

                doSpreadBlank();
                prepareImage();
                if (CombinedGridUnit)
                {
                    anaAutoLocation_combine2(ColorMatrix);
                }
                NeedFreshFullImg = false;
            }
            catch
            {
                //continue
            }
            finally
            {
                try
                {
                    if (useWaitBox)
                    {
                        Thread.Sleep(200);
                        WaitBox.Close();
                    }
                }
                catch
                {
                    //continue
                }
            }
        }

        private void dealColorMatrix()
        {
            foreach (ColorUnit cu in ColorMatrix)
            {
                if (cu != null && cu.DataHub != null)
                {
                    anaColor(cu);
                    if (CombinedGridUnit)
                    {
                        InitColorUnitForCombine(cu);
                        anaCombinedColor(cu);
                    }
                }
            }
        }

        private int getGridSpanPixel(int rowCnt, int colCnt)
        {
            int max = Math.Max(rowCnt, colCnt);
            if (max < 400)
            {
                return 16;
            }
            else if (max < 800)
            {
                return 8;
            }
            else if (max < 1200)
            {
                return 4;
            }
            else if (max < 1600)
            {
                return 3;
            }
            else if (max < 3000)
            {
                return 2;
            }
            else
            {
                return 1;
            }
        }

        private void doPrepareColorMatrixThread_ByMatrixWithoutGridPartParam()  //只显示栅格颜色，不经过指标计算
        {
            if (!NeedFreshFullImg)
            {
                return;
            }
            try
            {
                GridMatrix<ColorUnit> colorMatrix = GridMatrix<ColorUnit>.RefactorMatrixBySize(gridSizeFactor, MainModel.CurGridColorUnitMatrix);
                if (CurUsingColorMode != null)
                {
                    CurUsingColorMode.ResetDic();
                }
                else
                {
                    MessageBox.Show("当前没有设置好的栅格显示样式，请在栅格显示设置中添加!");
                    return;
                }

                xGapPixel = yGapPixel = getGridSpanPixel(colorMatrix.RowCount, colorMatrix.ColCount);
                if (useWaitBox)
                {
                    WaitBox.Text = "正在生成栅格渲染结构...";
                }
                foreach (ColorUnit cu in colorMatrix)
                {
                    if (cu != null && cu.DataHub != null && CombinedGridUnit)
                    {
                        InitColorUnitForCombine(cu);
                        anaCombinedColor(cu);
                    }
                }
                doSpreadBlank();
                this.ColorMatrix = colorMatrix;
                prepareImage();
                if (CombinedGridUnit)
                {
                    anaAutoLocation_combine2(colorMatrix);
                }
                NeedFreshFullImg = false;
            }
            finally
            {
                if (useWaitBox)
                {
                    Thread.Sleep(2000);
                    WaitBox.Close();
                }
            }
        }

        private void doSpreadBlank()
        {
            if (SpreadBlank)
            {
                DialogResult ret = MessageBox.Show("插值渲染将耗费较多的时间，是否现在进行插值渲染？", "渲染", MessageBoxButtons.OKCancel);
                if (ret == DialogResult.OK)
                {
                    if (useWaitBox)
                    {
                        WaitBox.Text = "正在对无数据区域进行插值渲染...";
                    }
                    decideSpreadShow();
                }
            }
        }

        private void InitColorUnitForCombine(ColorUnit colorUnit)
        {
            colorUnit.combined = false;
            colorUnit.top = false;
            colorUnit.bottom = false;
            colorUnit.left = false;
            colorUnit.right = false;
            colorUnit.combineColorRange = null;
            colorUnit.streetInfo = "";
        }

        private void anaAutoLocation_combine2(GridMatrix<ColorUnit> matrix)
        {
            CombinedColorUnitList.Clear();
            List<CombinedColorUnit> combinedList = new List<CombinedColorUnit>();
            List<CombinedColorUnit> tmpList = new List<CombinedColorUnit>();
            foreach (ColorUnit cu in matrix)
            {
                if (cu != null)
                {
                    if (cu.Status == 0 || cu.combineColorRange == null)
                    {
                        continue;//already Combined
                    }
                    int r, c;
                    GridHelper.GetIndexOfCustomSizeGrid(gridSizeFactor, cu.CenterLng, cu.CenterLat, out r, out c);
                    cu.r = r;
                    cu.c = c;
                    dealTmpList(combinedList, tmpList, cu);
                    dealCombinedList(combinedList, tmpList, cu);
                }
            }

            foreach (CombinedColorUnit item in combinedList)
            {
                if (item.Matrix.Grids.Count >= CurUsingColorMode.combineValidCount && !CombinedColorUnitList.Contains(item))
                {
                    item.AnaBorder();
                    CombinedColorUnitList.Add(item);
                }
            }

            if (ShowStreetInfo)
            {
                GetStreetInfo(matrix);
            }
        }

        private void dealTmpList(List<CombinedColorUnit> combinedList, List<CombinedColorUnit> tmpList, ColorUnit cu)
        {
            tmpList.Clear();
            foreach (CombinedColorUnit combinedColorUnit in combinedList)
            {
                if (combinedColorUnit.Intersect(cu))
                {
                    tmpList.Add(combinedColorUnit);
                }
            }
        }

        private void dealCombinedList(List<CombinedColorUnit> combinedList, List<CombinedColorUnit> tmpList, ColorUnit cu)
        {
            if (tmpList.Count == 0)
            {
                combinedList.Add(new CombinedColorUnit(cu));
            }
            else
            {
                CombinedColorUnit combinTmp = null;
                foreach (CombinedColorUnit item in tmpList)
                {
                    if (combinTmp == null)
                    {
                        combinTmp = item;
                        combinTmp.Matrix[cu.r, cu.c] = cu;
                    }
                    else
                    {
                        combinTmp.Join(item);
                        combinedList.Remove(item);
                    }
                }
            }
        }

        private void GetStreetInfo(GridMatrix<ColorUnit> matrix)
        {
            if (useWaitBox)
            {
                WaitBox.Text = "正在获取道路信息...";
            }
            int index = 0;
            foreach (ColorUnit colorUnit in matrix)
            {
                if (colorUnit == null || colorUnit.Status == 0 || colorUnit.combineColorRange == null)
                {
                    continue;
                }
                colorUnit.streetInfo = GISManager.GetInstance().GetRoadPlaceDesc(colorUnit.CenterLng, colorUnit.CenterLat);
                index++;
                if (useWaitBox)
                {
                    WaitBox.ProgressPercent = 80 + (int)(((double)index / matrix.Length) * 20);
                }
            }
        }

        public void prepareColorMatrix()
        {
            if (MainModel != null && (MainModel.CurGridCoverData != null || MainModel.CurGridColorUnitMatrix != null))
            {
                if (!NeedFreshFullImg)
                {
                    return;
                }
                doPrepareColor();

                this.Invalidate();
            }
            else if (MainModel != null && MainModel.CurGridCoverData == null && MainModel.CurGridColorUnitMatrix == null)
            {
                useWaitBox = false;
                doPrepareColorMatrixThread_ByMatrix();
            }
        }

        private void doPrepareColor()
        {
            if (MainModel.CurGridCoverData != null)
            {
                if (MainModel.CurGridCoverData.Count < 300)
                {
                    useWaitBox = false;
                    doPrepareColorMatrixThread();
                }
                else
                {
                    useWaitBox = true;
                    WaitBox.Show(doPrepareColorMatrixThread);
                }
            }
            else if (MainModel.CurGridColorUnitMatrix != null)
            {
                useWaitBox = true;
                if (MainModel.IsPrepareWithoutGridPartParam)
                {
                    WaitBox.Show(doPrepareColorMatrixThread_ByMatrixWithoutGridPartParam);
                }
                else
                {
                    WaitBox.Show(doPrepareColorMatrixThread_ByMatrix);
                }
            }
        }

        private void anaColor(ColorUnit cu)
        {
            if (CurUsingColorMode != null)
            {
                CurUsingColorMode.CalcColor(cu);
            }
        }

        private void anaCombinedColor(ColorUnit cu)
        {
            if (CurUsingColorMode == null)
            {
                cu.combineColorRange = null;
            }
            else
            {
                CurUsingColorMode.CalcCombinedColorRange(cu);
            }
        }

        /// <summary>
        /// 扩展没有数据的区域
        /// </summary>
        /// <param name="cu"></param>
        /// <param name="xAt"></param>
        /// <param name="yAt"></param>
        /// <param name="u"></param>
        private void decideSpreadShow()
        {
            foreach (ColorUnit cu in ColorMatrix)
            {
                if (cu == null)
                {
                    continue;
                }
                if (cu.Status == 0)
                {//空的，需要填充
                    int r, c;
                    GridHelper.GetIndexOfCustomSizeGrid(gridSizeFactor, cu.CenterLng, cu.CenterLat, out r, out c);
                    if (CurUsingColorMode.rangeMode)
                    {
                        affectNearColorUnitColor(cu, r, c);
                    }
                    else
                    {
                        decideNearDicColorUnitColor(cu, r, c);
                    }
                }
            }
        }

        private void decideNearDicColorUnitColor(ColorUnit cu, int row, int col)
        {
            List<AtPos> related = new List<AtPos>();
            int offset = 1;
            while (offset < 100 && !getByOffset(related, offset++, row, col))
            {
                //cycling
            }
            if (related.Count == 0)
            {
                return;
            }
            int minDistance = 999999;
            AtPos minAtPos = null;
            foreach (AtPos ap in related)
            {
                if (ap.distance < minDistance)
                {
                    minDistance = ap.distance;
                    minAtPos = ap;
                }
            }
            if (minAtPos != null)
            {
                cu.color = CurUsingColorMode.GetDicColor(minAtPos.cu.retv);
                cu.Status = 2;//assigned
                cu.DataHub = minAtPos.cu.DataHub;
            }

        }

        private void affectNearColorUnitColor(ColorUnit cu, int row, int col)
        {
            List<AtPos> related = new List<AtPos>();
            int offset = 1;
            while (offset < 100 && !getByOffset(related, offset++, row, col))
            {
                //cycling
            }
            if (related.Count == 0)
            {
                return;
            }
            int minDistance = 999999;
            AtPos minAtPos = null;
            foreach (AtPos ap in related)
            {
                if (ap.distance < minDistance)
                {
                    minDistance = ap.distance;
                    minAtPos = ap;
                }
            }
            while (offset < 100 && !getByOffset(related, offset++, row, col)
                && !getByOffset(related, offset++, row, col)
                && !getByOffset(related, offset++, row, col)
                 )
            {
                //cycling
            }
            float totleV = 0.0f;
            float totleW = 0.0f;
            foreach (AtPos ap in related)
            {
                float w = 1.0f / ap.distance;
                totleV += ap.cu.retv * w;
                totleW += w;
            }
            float finalv = totleV / totleW;
            cu.color = CurUsingColorMode.GetColor(finalv);
            cu.Status = 2;//assigned
            if (minAtPos != null)
            {
                cu.DataHub = minAtPos.cu.DataHub;
            }
        }

        private bool getByOffset(List<AtPos> related, int offset, int centerRow, int centerCol)
        {
            int minRowIdx = ColorMatrix.MinRowIdx;
            int maxRowIdx = ColorMatrix.MaxRowIdx;
            int minColIdx = ColorMatrix.MinColIdx;
            int maxColIdx = ColorMatrix.MaxColIdx;

            int smallRowIdx = centerRow - offset;
            int largeRowIdx = centerRow + offset;
            int smallColIdx = centerCol - offset;
            int largeColIdx = centerCol + offset;
            if (smallRowIdx < minRowIdx && largeRowIdx > maxRowIdx
             && smallColIdx < minColIdx && largeColIdx > maxColIdx)
            {
                return true;
            }

            bool found = false;
            for (int r = smallRowIdx; r <= largeRowIdx; r++)
            {
                found = setColAtPos(related, centerRow, centerCol, smallColIdx, largeColIdx, found, r);
            }

            for (int c = smallColIdx + 1; c <= largeColIdx; c++)
            {
                found = setRowAtPos(related, centerRow, centerCol, smallRowIdx, largeRowIdx, found, c);
            }
            return found;
        }

        private bool setColAtPos(List<AtPos> related, int centerRow, int centerCol, int smallColIdx, int largeColIdx, bool found, int r)
        {
            ColorUnit cu = ColorMatrix[r, smallColIdx];
            if (cu != null && cu.Status == 1)
            {
                AtPos ap = new AtPos();
                ap.rowAt = r;
                ap.colAt = smallColIdx;
                ap.cu = cu;
                ap.distance = (r - centerRow) * (r - centerRow) + (smallColIdx - centerCol) * (smallColIdx - centerCol);
                related.Add(ap);
                found = true;
            }
            cu = ColorMatrix[r, largeColIdx];
            if (cu != null && cu.Status == 1)
            {
                AtPos ap = new AtPos();
                ap.rowAt = r;
                ap.colAt = largeColIdx;
                ap.cu = cu;
                ap.distance = (r - centerRow) * (r - centerRow) + (largeColIdx - centerCol) * (largeColIdx - centerCol);
                related.Add(ap);
                found = true;
            }

            return found;
        }

        private bool setRowAtPos(List<AtPos> related, int centerRow, int centerCol, int smallRowIdx, int largeRowIdx, bool found, int c)
        {
            ColorUnit cu = ColorMatrix[smallRowIdx, c];
            if (cu != null && cu.Status == 1)
            {
                AtPos ap = new AtPos();
                ap.rowAt = smallRowIdx;
                ap.colAt = c;
                ap.cu = cu;
                ap.distance = (smallRowIdx - centerRow) * (smallRowIdx - centerRow) + (c - centerCol) * (c - centerCol);
                related.Add(ap);
                found = true;
            }
            cu = ColorMatrix[largeRowIdx, c];
            if (cu != null && cu.Status == 1)
            {
                AtPos ap = new AtPos();
                ap.rowAt = largeRowIdx;
                ap.colAt = c;
                ap.cu = cu;
                ap.distance = (largeRowIdx - centerRow) * (largeRowIdx - centerRow) + (c - centerCol) * (c - centerCol);
                related.Add(ap);
                found = true;
            }

            return found;
        }

        internal void doFireApplyAction()
        {
            prepareColorMatrix();
            CombinedShowResult = CombinedGridUnit;
            if (GridQueryHistogramForm.NeedFreshImg && GridQueryHistogramForm.IsLoad)
            {
                mainModel.FireGridQueryChanged(this);
            }
        }

        internal int Select(MapOperation2 mop2)
        {
            ColorUnit cu = null;
            Select(mop2, ref cu);
            selColorUnit = cu;
            return cu == null ? 0 : 1;
        }

        public void Select(MapOperation2 mop2, ref ColorUnit colorUnit)
        {
            if (IsVisible)
            {
                if (ColorMatrix == null)
                {
                    colorUnit = null;
                    return;
                }
                colorUnit = null;
                DbPoint clickCenter = mop2.GetRegion().Bounds.Center();
                int row, col;
                GridHelper.GetIndexOfCustomSizeGrid(gridSizeFactor, clickCenter.x, clickCenter.y, out row, out col);
                colorUnit = ColorMatrix[row, col];
            }
        }

        public void ExportKml(KMLExporter exporter, XmlElement parentElem)
        {
            XmlElement layerElem = exporter.CreateFolder(name, false);
            parentElem.AppendChild(layerElem);
            string filename = Application.StartupPath + @"\cover.png";
            double ltLong = 0;
            double brLong = 0;
            double ltLat = 0;
            double brLat = 0;
            if (outputCoverFullImg(filename, ref ltLong, ref brLong, ref ltLat, ref brLat))
            {
                XmlElement elem = exporter.CreateGroundOverlay("栅格KPI渲染", filename, OpaqueValue, ltLong, ltLat, brLong, brLat);
                layerElem.AppendChild(elem);
            }
            else
            {
                XmlElement elem = exporter.CreateFolder("栅格KPI渲染", false);
                layerElem.AppendChild(elem);
                foreach (ColorUnit cu in ColorMatrix)
                {
                    elem.AppendChild(exporter.CreateRectangle(cu.color, cu.LTLng, cu.LTLat, cu.BRLng, cu.BRLat));
                }
            }
        }
    }

    class AtPos
    {
        public int distance;
        public int rowAt;
        public int colAt;
        public ColorUnit cu;
    }

    public class ColorUnit : GridUnitBase
    {
        public float retv { get; set; }
        public Color color { get; set; }
        public StatDataHubBase DataHub { get; set; } = new StatDataHubBase();
        public bool combined { get; set; } = false;
        public bool top { get; set; } = false;
        public bool left { get; set; } = false;
        public bool right { get; set; } = false;
        public bool bottom { get; set; } = false;
        public int r { get; set; }
        public int c { get; set; }
        public ColorRange combineColorRange { get; set; }
        public bool invalid { get; set; } = false;
        public string streetInfo { get; set; } = "";

        public string PositionInfo
        {
            get
            {
                StringBuilder sb = new StringBuilder();
                sb.Append("栅格左上经度:").Append(LTLng.ToString());
                sb.Append("\r\n栅格左上纬度:").Append(LTLat.ToString());
                sb.Append("\r\n栅格右下经度:").Append(BRLng.ToString());
                sb.Append("\r\n栅格右下纬度:").Append(BRLat.ToString());
                return sb.ToString();
            }
        }

        public override void Merge(GridUnitBase grid2Merge)
        {
            ColorUnit unit = grid2Merge as ColorUnit;
            if (unit != null)
            {
                DataHub.Merge(unit.DataHub);
            }
        }
    }

    /// <summary>
    /// 一个栅格汇聚
    /// </summary>
    public class CombinedColorUnit
    {
        readonly GridMatrix<ColorUnit> matrix = new GridMatrix<ColorUnit>();
        public GridMatrix<ColorUnit> Matrix
        {
            get { return matrix; }
        }
        public CombinedColorUnit(ColorUnit cu)
        {
            matrix[cu.r, cu.c] = cu;
        }

        public ColorRange colorRange
        {
            get
            {
                foreach (ColorUnit unit in matrix)
                {
                    if (unit != null)
                    {
                        return unit.combineColorRange;
                    }
                }
                return null;
            }
        }
        public double retvAvg
        {
            get
            {
                double sum = 0;
                foreach (ColorUnit item in matrix)
                {
                    sum += (double)item.retv;
                }
                return Math.Round(sum / matrix.Grids.Count, 2);
            }
        }
        public string StreetInfo
        {
            get
            {
                string streetInfo = string.Empty;
                foreach (ColorUnit cu in matrix)
                {
                    streetInfo = getStreetInfo(streetInfo, cu);
                }
                return streetInfo;
            }
        }

        private string getStreetInfo(string streetInfo, ColorUnit cu)
        {
            if (!string.IsNullOrEmpty(cu.streetInfo))
            {
                StringBuilder sb = new StringBuilder(streetInfo);
                string[] sArr = cu.streetInfo.Split('|');
                for (int i = 0; i < sArr.Length; i++)
                {
                    if (string.IsNullOrEmpty(streetInfo))
                    {
                        sb.Append(sArr[i]);
                    }
                    else if (!streetInfo.Contains(sArr[i]))
                    {
                        sb.Append("|" + sArr[i]);
                    }
                    streetInfo = sb.ToString();
                }
            }
            return streetInfo;
        }

        public bool invalid { get; set; } = false;

        public bool Intersect(ColorUnit cu)
        {
            if (cu.combineColorRange == colorRange)
            {
                for (int r = cu.r - 1; r <= cu.r + 1; r++)
                {
                    for (int c = cu.c - 1; c <= cu.c + 1; c++)
                    {
                        if (matrix[r, c] != null)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        public void Join(CombinedColorUnit combinCU)
        {
            matrix.Merge(combinCU.matrix);
        }

        internal void AnaBorder()
        {
            foreach (ColorUnit cu in matrix)
            {
                for (int r = cu.r - 1; r <= cu.r + 1; r++)
                {
                    for (int c = cu.c - 1; c <= cu.c + 1; c++)
                    {
                        setColorUnit(cu, r, c);
                    }
                }
            }
            if (matrix.Length == 1)
            {
                matrix.Grids[0].bottom = true;
                matrix.Grids[0].top = true;
                matrix.Grids[0].left = true;
                matrix.Grids[0].right = true;
            }
        }

        private void setColorUnit(ColorUnit cu, int r, int c)
        {
            if (r == cu.r || c == cu.c)
            {
                ColorUnit item = matrix[r, c];
                if (item == null)
                {
                    if (r == cu.r + 1 && c == cu.c) //上
                    {
                        cu.top = true;
                    }
                    else if (r == cu.r - 1 && c == cu.c) //下
                    {
                        cu.bottom = true;
                    }
                    else if (c == cu.c - 1 && r == cu.r) //左
                    {
                        cu.left = true;
                    }
                    else if (c == cu.c + 1 && r == cu.r) //右
                    {
                        cu.right = true;
                    }
                }
            }
        }
    }
}
