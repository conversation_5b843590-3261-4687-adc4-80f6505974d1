﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MapWinGIS;

namespace MasterCom.MTGis
{
    public static class RegionAreaCalculator
    {
        private static MainModel mainModel = MainModel.GetInstance();
        private static MapForm mapForm;
        private static bool isActivated;

        static RegionAreaCalculator()
        {
            Activate();
        }

        public static bool IsActivated
        {
            get { return isActivated; }
        }

        public static void Activate()
        {
            mainModel.SearchGeometrysChanged -= CalculateRegionArea;
            mainModel.SearchGeometrysChanged += CalculateRegionArea;
            isActivated = true;
        }

        public static void Deactivate()
        {
            mainModel.SearchGeometrysChanged -= CalculateRegionArea;
            isActivated = false;
        }

        private static void CalculateRegionArea(object sender, EventArgs e)
        {   
            if (mapForm == null)
            {
                mapForm = mainModel.MainForm.GetMapForm();
                if (mapForm == null)
                {
                    return;
                }
            }

            mapForm.SetStatusLabelZoom("");

            if (!isActivated)
            {
                return;
            }

            MapWinGIS.Shape region = mainModel.SearchGeometrys.Region;
            if (region == null || !mainModel.SearchGeometrys.IsSelectRegion())
            {
                return;
            }

            double area = CalculateArea(region);
            mapForm.SetStatusLabelZoom(string.Format("{0:F3} sq km", area));
        }

        public static double CalculateArea(Shape region)
        {
            if (region==null)
            {
                return 0;
            }
            //double area = region.Area * Equator * Meridian * Math.Cos(region.Center.y * Math.PI / 180);
            double area = region.Area * DistanceTranslator.AreaPerLongLat(region.Center.y) / 1000000;
            return area;
        }
    }
}
