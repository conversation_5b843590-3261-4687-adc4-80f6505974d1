﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class HandoverFailureAnaForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listView = new System.Windows.Forms.ListView();
            this.columnHeaderSN = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTime = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderFilename = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderLAC = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCI = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderBCCH = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderBSIC = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCell = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTarLAC = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTargetCI = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTarBCCH = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTarBSIC = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTarCell = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderLongitude = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderLatitude = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderInfo = new System.Windows.Forms.ColumnHeader();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // listView
            // 
            this.listView.AllowColumnReorder = true;
            this.listView.AutoArrange = false;
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSN,
            this.columnHeaderTime,
            this.columnHeaderFilename,
            this.columnHeaderLAC,
            this.columnHeaderCI,
            this.columnHeaderBCCH,
            this.columnHeaderBSIC,
            this.columnHeaderCell,
            this.columnHeaderTarLAC,
            this.columnHeaderTargetCI,
            this.columnHeaderTarBCCH,
            this.columnHeaderTarBSIC,
            this.columnHeaderTarCell,
            this.columnHeaderLongitude,
            this.columnHeaderLatitude,
            this.columnHeaderInfo});
            this.listView.ContextMenuStrip = this.ctxMenu;
            this.listView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView.FullRowSelect = true;
            this.listView.GridLines = true;
            this.listView.HideSelection = false;
            this.listView.LabelWrap = false;
            this.listView.Location = new System.Drawing.Point(0, 0);
            this.listView.Name = "listView";
            this.listView.ShowGroups = false;
            this.listView.Size = new System.Drawing.Size(1166, 408);
            this.listView.TabIndex = 1;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            this.listView.SelectedIndexChanged += new System.EventHandler(this.listView_SelectedIndexChanged);
            // 
            // columnHeaderSN
            // 
            this.columnHeaderSN.Text = "序号";
            this.columnHeaderSN.Width = 62;
            // 
            // columnHeaderTime
            // 
            this.columnHeaderTime.Text = "时间";
            this.columnHeaderTime.Width = 82;
            // 
            // columnHeaderFilename
            // 
            this.columnHeaderFilename.Text = "FileName";
            this.columnHeaderFilename.Width = 103;
            // 
            // columnHeaderLAC
            // 
            this.columnHeaderLAC.Text = "LAC";
            this.columnHeaderLAC.Width = 57;
            // 
            // columnHeaderCI
            // 
            this.columnHeaderCI.Text = "CI";
            this.columnHeaderCI.Width = 54;
            // 
            // columnHeaderBCCH
            // 
            this.columnHeaderBCCH.Text = "BCCH";
            // 
            // columnHeaderBSIC
            // 
            this.columnHeaderBSIC.Text = "BSIC";
            // 
            // columnHeaderCell
            // 
            this.columnHeaderCell.Text = "Cell";
            // 
            // columnHeaderTarLAC
            // 
            this.columnHeaderTarLAC.Text = "TargetLAC";
            this.columnHeaderTarLAC.Width = 72;
            // 
            // columnHeaderTargetCI
            // 
            this.columnHeaderTargetCI.Text = "TargetCI";
            this.columnHeaderTargetCI.Width = 65;
            // 
            // columnHeaderTarBCCH
            // 
            this.columnHeaderTarBCCH.Text = "TargetBCCH";
            this.columnHeaderTarBCCH.Width = 75;
            // 
            // columnHeaderTarBSIC
            // 
            this.columnHeaderTarBSIC.Text = "TargetBSIC";
            this.columnHeaderTarBSIC.Width = 79;
            // 
            // columnHeaderTarCell
            // 
            this.columnHeaderTarCell.Text = "TargetCell";
            this.columnHeaderTarCell.Width = 81;
            // 
            // columnHeaderLongitude
            // 
            this.columnHeaderLongitude.Text = "Longitude";
            this.columnHeaderLongitude.Width = 99;
            // 
            // columnHeaderLatitude
            // 
            this.columnHeaderLatitude.Text = "Latitude";
            this.columnHeaderLatitude.Width = 91;
            // 
            // columnHeaderInfo
            // 
            this.columnHeaderInfo.Text = "Info";
            this.columnHeaderInfo.Width = 119;
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(143, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(142, 22);
            this.miExportExcel.Text = "导出Excel...";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // HandoverFailureAnaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1166, 408);
            this.Controls.Add(this.listView);
            this.Name = "HandoverFailureAnaForm";
            this.Text = "切换失败分析";
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ListView listView;
        private System.Windows.Forms.ColumnHeader columnHeaderSN;
        private System.Windows.Forms.ColumnHeader columnHeaderFilename;
        private System.Windows.Forms.ColumnHeader columnHeaderTime;
        private System.Windows.Forms.ColumnHeader columnHeaderLAC;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ColumnHeader columnHeaderCI;
        private System.Windows.Forms.ColumnHeader columnHeaderTarLAC;
        private System.Windows.Forms.ColumnHeader columnHeaderTargetCI;
        private System.Windows.Forms.ColumnHeader columnHeaderLongitude;
        private System.Windows.Forms.ColumnHeader columnHeaderLatitude;
        private System.Windows.Forms.ColumnHeader columnHeaderInfo;
        private System.Windows.Forms.ColumnHeader columnHeaderBCCH;
        private System.Windows.Forms.ColumnHeader columnHeaderBSIC;
        private System.Windows.Forms.ColumnHeader columnHeaderTarBCCH;
        private System.Windows.Forms.ColumnHeader columnHeaderTarBSIC;
        private System.Windows.Forms.ColumnHeader columnHeaderCell;
        internal System.Windows.Forms.ColumnHeader columnHeaderTarCell;
    }
}