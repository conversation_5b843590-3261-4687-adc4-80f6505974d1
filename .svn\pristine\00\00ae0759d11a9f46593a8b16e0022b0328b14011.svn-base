﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsRsrpRangeSetting : LteMgrsConditionControlBase
    {
        public LteMgrsRsrpRangeSetting()
        {
            InitializeComponent();
            initCbxFreqType();
        }

        public override string Title
        {
            get { return "场强分布"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            invalidReason = null;
            return (LteMgrsRsrpBandType)EnumDescriptionAttribute.Parse(typeof(LteMgrsRsrpBandType), cbxFreqType.SelectedItem as string);
        }

        private void initCbxFreqType()
        {
            cbxFreqType.Items.Clear();
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.Top));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.SingleD));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.SingleF));
            XmlConfigFile configFile = new MyXmlConfigFile(LteMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                XmlElement configRSRPRange = configFile.GetConfig("RSRPRange");
                object obj = configFile.GetItemValue(configRSRPRange, "FreqType");
                if (obj != null)
                {
                    int index = cbxFreqType.Items.IndexOf(obj.ToString());
                    if (index >= 0)
                    {
                        cbxFreqType.SelectedIndex = index;
                        return;
                    }
                }
            }
            cbxFreqType.SelectedIndex = 0;
        }

        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            XmlElement configRSRPRange = xcfg.AddConfig("RSRPRange");
            xcfg.AddItem(configRSRPRange, "FreqType", cbxFreqType.Text);
        }
    }
}
