﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    public class ImsiFile
    {
        public int FileID
        {
            get;
            set;
        }

        public string FileName
        {
            get;
            set;
        }

        public List<string> ImsiList
        {
            get;
            set;
        }

        public ImsiFile Clone()
        {
            ImsiFile iFile = new ImsiFile();
            iFile.FileID = FileID;
            iFile.FileName = FileName;
            iFile.ImsiList = ImsiList == null ? null : new List<string>(ImsiList);
            return iFile;
        }
    }

    public class ImsiFileQuery
    {
        public List<ImsiFile> QueryFilesForEach(List<FileInfo> fileInfos)
        {
            List<ImsiFile> retList = new List<ImsiFile>();
            foreach (FileInfo fInfo in fileInfos)
            {
                if (string.IsNullOrEmpty(fInfo.SampleTbName2))
                {
                    continue;
                }

                DIYSqlImsi diySql = new DIYSqlImsi(fInfo, MainModel.GetInstance());
                diySql.Query();
                if (diySql.ImsiList.Count == 0)
                {
                    continue;
                }

                ImsiFile imsiFile = new ImsiFile();
                imsiFile.FileID = fInfo.ID;
                imsiFile.FileName = fInfo.Name;
                imsiFile.ImsiList = new List<string>();
                foreach (long imsi in diySql.ImsiList)
                {
                    imsiFile.ImsiList.Add(imsi.ToString());
                }

                retList.Add(imsiFile);
            }
            return retList;
        }

        public List<ImsiFile> QueryFilesBatch(List<FileInfo> fileInfos)
        {
            DIYSqlMultiFileImsi diySql = new DIYSqlMultiFileImsi(MainModel.GetInstance());
            return diySql.QueryImsiFiles(fileInfos);
        }

        private class DIYSqlImsi : DIYSQLBase
        {
            public DIYSqlImsi(FileInfo fileInfo, MainModel mainModel)
                : base(mainModel)
            {
                curFileInfo = fileInfo;
                ImsiList = new List<long>();
            }

            public List<long> ImsiList
            {
                get;
                private set;
            }

            protected override string getSqlTextString()
            {
                return string.Format("select IMSI from {0} where ifileid = {1}", curFileInfo.SampleTbName2, curFileInfo.ID);
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[1];
                rType[0] = E_VType.E_Int64;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                while (true)
                {
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        long imsi = package.Content.GetParamInt64();
                        ImsiList.Add(imsi);
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                }
            }

            public override string Name
            {
                get { return "DIYSqlImsi"; }
            }

            private readonly FileInfo curFileInfo;
        }

        private class DIYSqlMultiFileImsi : DIYSQLBase
        {
            public DIYSqlMultiFileImsi(MainModel mainModel)
                : base(mainModel)
            {
            }

            public override string Name
            {
                get { return "DIYSqlImsi"; }
            }

            public List<ImsiFile> QueryImsiFiles(List<FileInfo> fileInfos)
            {
                // 按imsi表分类
                Dictionary<string, List<int>> imsiTableDic = new Dictionary<string, List<int>>();
                foreach (FileInfo fInfo in fileInfos)
                {
                    List<int> fileIDs = null;
                    if (!imsiTableDic.TryGetValue(fInfo.SampleTbName2, out fileIDs))
                    {
                        fileIDs = new List<int>();
                        imsiTableDic.Add(fInfo.SampleTbName2, fileIDs);
                    }
                    fileIDs.Add(fInfo.ID);
                }

                // 对个imsi表查询文件
                imsiFileDic = new Dictionary<int, ImsiFile>();
                foreach (KeyValuePair<string, List<int>> kvp in imsiTableDic)
                {
                    int fileCnt = 0;
                    while (fileCnt < kvp.Value.Count)
                    {
                        List<int> subFileIds = kvp.Value.GetRange(fileCnt, Math.Min(100, kvp.Value.Count - fileCnt));
                        InitSqlText(kvp.Key, subFileIds);
                        this.Query();
                        fileCnt += subFileIds.Count;
                    }
                }

                List<ImsiFile> retList = new List<ImsiFile>(this.imsiFileDic.Values);
                this.imsiFileDic.Clear();
                return retList;
            }

            protected override string getSqlTextString()
            {
                return sqlText;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[2];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int64;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                while (true)
                {
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        int fileid = package.Content.GetParamInt();
                        long imsi = package.Content.GetParamInt64();

                        ImsiFile imsiFile = null;
                        if (!imsiFileDic.TryGetValue(fileid, out imsiFile))
                        {
                            imsiFile = new ImsiFile();
                            imsiFile.FileID = fileid;
                            imsiFile.ImsiList = new List<string>();
                            imsiFileDic.Add(fileid, imsiFile);
                        }
                        imsiFile.ImsiList.Add(imsi.ToString());
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                }
            }

            private void InitSqlText(string imsiTable, List<int> fileIDs)
            {
                StringBuilder sb = new StringBuilder();
                foreach (int id in fileIDs)
                {
                    sb.Append(id + ",");
                }
                sb.Remove(sb.Length - 1, 1);

                sqlText = string.Format("select ifileid, IMSI from {0} where ifileid in ({1})", imsiTable, sb.ToString());
            }

            private string sqlText;

            private Dictionary<int, ImsiFile> imsiFileDic;
        }
    }
}
