﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryGoodRsrpPoorSinr_NB : QueryGoodRsrpPoorSinr
    {
        public QueryGoodRsrpPoorSinr_NB()
            : base()
        {
        }

        public override string Name
        {
            get { return "强信号SINR质差_NBIOT扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33017, this.Name);
        }
    }
}
