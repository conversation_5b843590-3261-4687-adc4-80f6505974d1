using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Collections.ObjectModel;

namespace MasterCom.RAMS.Model
{
    public class WRNC
    {
        public WRNC(string name)
        {
            Name = name;
        }

        public string Name { get; set; }

        public ReadOnlyCollection<WNodeB> NodeBs
        {
            get { return NodeBsForConfig.AsReadOnly(); }
        }

        public List<WNodeB> NodeBsForConfig { get; set; } = new List<WNodeB>();

        public void AddNodeB(WNodeB nodeB)
        {
            NodeBsForConfig.Add(nodeB);
            nodeB.BelongRNC = this;
        }

        public WMGW BelongMGW { get; set; }

        public static IComparer<WRNC> GetCompareByName()
        {
            if (comparerByName == null)
            {
                comparerByName = new ComparerByName();
            }
            return comparerByName;
        }

        private static IComparer<WRNC> comparerByName;

        public class ComparerByName : IComparer<WRNC>
        {
            public int Compare(WRNC x, WRNC y)
            {
                return x.Name.CompareTo(y.Name);
            }
        }
    }
}
