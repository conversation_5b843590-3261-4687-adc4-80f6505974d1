﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FileProjectChangeSetForm : BaseDialog
    {
        public FileProjectChangeSetForm()
        {
            InitializeComponent();

            CategoryEnumItem[] items = ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items;
            this.cbxType.Items.AddRange(items);
            if (cbxType.Items.Count > 0)
            {
                cbxType.SelectedIndex = 0;
            }

            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        public FileProjectChangeCondition GetCondition()
        {
            FileProjectChangeCondition cond = new FileProjectChangeCondition();
            cond.ProjectID = (cbxType.SelectedItem as CategoryEnumItem).ID;
            cond.Person = txtPerson.Text;
            cond.Reason = txtReason.Text;
            return cond;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            string errMsg = null;
            if (String.IsNullOrEmpty(txtPerson.Text))
            {
                errMsg = "请填写操作人名字";
            }
            else if (string.IsNullOrEmpty(txtReason.Text))
            {
                errMsg = "请填写操作原因";
            }
            else if (cbxType.SelectedItem == null)
            {
                errMsg = "请选择目标项目类型";
            }

            if (errMsg != null)
            {
                MessageBox.Show(errMsg, this.Text, MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = System.Windows.Forms.DialogResult.None;
                return;
            }
            DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }
    }

    public class FileProjectChangeCondition
    {
        public int ProjectID
        {
            get;
            set;
        }

        public string Person
        {
            get;
            set;
        }

        public string Reason
        {
            get;
            set;
        }
    }
}
