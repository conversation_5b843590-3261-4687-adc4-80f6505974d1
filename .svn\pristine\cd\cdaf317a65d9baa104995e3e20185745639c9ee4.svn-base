﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Model
{
    public class QueryDataBaseInfo
    {
        private readonly string dbConnStr;
        public QueryDataBaseInfo(string dbConnStr)
        {
            this.dbConnStr = dbConnStr;
        }

        public List<string> Query()
        {
            List<string> dbNames = new List<string>();
            string queryString = "select name from sys.databases where database_id > 4";
            using (SqlConnection connection = new SqlConnection(dbConnStr))
            {
                try
                {
                    SqlCommand command = new SqlCommand(queryString, connection);
                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        dbNames.Add(reader[0].ToString());
                    }
                    reader.Close();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.ToString());
                }
            }
            return dbNames;
        }
    }
}
