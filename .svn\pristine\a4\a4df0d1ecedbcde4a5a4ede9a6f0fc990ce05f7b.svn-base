﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using DevExpress.XtraEditors.Controls;
using MasterCom.Util;
using System.Xml;
using MasterCom.RAMS.Model;
using DevExpress.XtraEditors;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CityRoadMapSetDlg : BaseDialog
    {
        public CityRoadMapCfg SelectMapCfg
        {
            get { return listBoxAllSettings.SelectedItem as CityRoadMapCfg; }
        }

        public CityRoadMapSetDlg(CityRoadMapCfg selectMapCfg)
        {
            InitializeComponent();
            mainModel = MainModel.GetInstance();
            fillMapSet(selectMapCfg);
        }

        private void fillMapSet(CityRoadMapCfg selectMapCfg)
        {
            listBoxAllSettings.SelectedIndexChanged -= listBoxAllSettings_SelectedIndexChanged;
            listBoxAllSettings.Items.Clear();
            listBoxAllSettings.SelectedIndexChanged += listBoxAllSettings_SelectedIndexChanged;
            foreach (CityRoadMapCfg rpt in CityRoadMapManager.GetInstance().CityRoadMapList)
            {
                listBoxAllSettings.Items.Add(rpt);
            }
            if (listBoxAllSettings.Items.Count > 0)
            {
                if (selectMapCfg != null)
                {
                    listBoxAllSettings.SelectedItem = selectMapCfg;
                }
                else
                {
                    listBoxAllSettings.SelectedIndex = 0;
                }
            }
        }
        private void listBoxAllSettings_DrawItem(object sender, DrawItemEventArgs e)
        {
            e.DrawBackground();
            e.DrawFocusRectangle();
            if (listBoxAllSettings.Items.Count > 0)
            {
                e.Graphics.DrawString(listBoxAllSettings.Items[e.Index].ToString(), e.Font, new SolidBrush(e.ForeColor), e.Bounds);
            }
        }

        CityRoadMapCfg curMapCfg = null;
        void listBoxAllSettings_SelectedIndexChanged(object sender, EventArgs e)
        {
            curMapCfg = listBoxAllSettings.SelectedItem as CityRoadMapCfg;
            btnRemoveSetting.Enabled = this.splitContainerControl1.Panel2.Enabled = curMapCfg != null;
            if (curMapCfg != null)
            {
                txtMapsName.Text = curMapCfg.Name;
                edtGridMap.Text = curMapCfg.GridMapPath;
                cbxGridColumn.Text = curMapCfg.GridNameColumn;

                listBoxRoadMap.Items.Clear();
                foreach (StreetInjectTableInfo streetInfo in curMapCfg.StreetInjectTablesList)
                {
                    if (checkRoadMap(streetInfo.FilePath))
                    {
                        listBoxRoadMap.Items.Add(streetInfo);
                    }
                }
                if (listBoxRoadMap.Items.Count > 0)
                {
                    listBoxRoadMap.SelectedIndex = 0;
                }
                else
                {
                    edtRoadMap.Text = cbxRoadNameColumn.Text = cbxRoadIdColumn.Text = "";
                }
            }
        }

        #region 道路图层设置

        private void listBoxRoadMap_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnRoadMapDelete.Enabled = listBoxRoadMap.SelectedIndex >= 0;
            StreetInjectTableInfo streetInfo = listBoxRoadMap.SelectedItem as StreetInjectTableInfo;
            if (streetInfo != null)
            {
                edtRoadMap.Text = streetInfo.FilePath;
                cbxRoadNameColumn.Text = streetInfo.ColumnName;
                cbxRoadIdColumn.Text = streetInfo.ColumnId;
            }
        }
        private void listBoxRoadMap_DrawItem(object sender, DrawItemEventArgs e)
        {
            e.DrawBackground();
            e.DrawFocusRectangle();
            if (listBoxRoadMap.Items.Count > 0)
            {
                e.Graphics.DrawString(listBoxRoadMap.Items[e.Index].ToString(), e.Font, new SolidBrush(e.ForeColor), e.Bounds);
            }
        }

        private void edtRoadMap_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            edtMapButtonClick(edtRoadMap, cbxRoadNameColumn, cbxRoadIdColumn);
        }

        private void btnRoadMapAdd_Click(object sender, EventArgs e)
        {
            if (checkRoadMap(edtRoadMap.Text.Trim()))
            {
                StreetInjectTableInfo streetInfo = new StreetInjectTableInfo();
                streetInfo.FilePath = edtRoadMap.Text.Trim();
                streetInfo.ColumnName = cbxRoadNameColumn.Text;
                streetInfo.ColumnId = cbxRoadIdColumn.Text;

                foreach (object item in listBoxRoadMap.Items)
                {
                    if (streetInfo.FilePath == ((StreetInjectTableInfo)item).FilePath)
                    {
                        return;
                    }
                }

                listBoxRoadMap.Items.Add(streetInfo);
            }
        }

        private void btnRoadMapDelete_Click(object sender, EventArgs e)
        {
            if (listBoxRoadMap.SelectedIndex >= 0)
            {
                listBoxRoadMap.Items.RemoveAt(listBoxRoadMap.SelectedIndex);
            }
        }

        private bool checkRoadMap(string filePath)
        {
            if (File.Exists(filePath))
            {
                return true;
            }
            return false;
        }
        private void btnRoadMapSelectNone_Click(object sender, EventArgs e)
        {
            listBoxRoadMap.Items.Clear();
        }
        #endregion

        private void edtGridMap_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            edtMapButtonClick(edtGridMap, cbxGridColumn ,null);
        }

        private void edtMapButtonClick(ButtonEdit mapBtn, ComboBoxEdit cbxColumn1, ComboBoxEdit cbxColumn2)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Multiselect = false;
            openFileDialog.CheckFileExists = true;
            openFileDialog.DefaultExt = "SHP";
            openFileDialog.Filter = FilterHelper.Shp;
            openFileDialog.InitialDirectory = Application.StartupPath + @"\GEOGRAPHIC";
            if (openFileDialog.ShowDialog(this) == DialogResult.OK)
            {
                MapWinGIS.Shapefile table = new MapWinGIS.Shapefile();
                try
                {
                    if (table.Open(openFileDialog.FileName, null))
                    {
                        fillComboBox(cbxColumn1, table);
                        fillComboBox(cbxColumn2, table);
                    }
                    mapBtn.Text = openFileDialog.FileName;
                }
                catch
                {
                    //continue;
                }
            }
        }
        private void fillComboBox(ComboBoxEdit cbx, MapWinGIS.Shapefile table)
        {
            if (cbx == null || table == null)
            {
                return;
            }

            cbx.Properties.Items.Clear();
            int numFields = table.NumFields;
            for (int x = 0; x < numFields; x++)
            {
                MapWinGIS.Field field = table.get_Field(x);
                cbx.Properties.Items.Add(field.Name);
            }
            cbx.SelectedIndex = 0;
        }

        private void btnNewSetting_Click(object sender, EventArgs e)
        {
            TextInputBox box = new TextInputBox("新建图层集", "图层集名称", "未命名图层集");
            if (box.ShowDialog() == DialogResult.OK)
            {
                CityRoadMapCfg mapCfg = new CityRoadMapCfg(box.TextInput);
                CityRoadMapManager.GetInstance().CityRoadMapList.Add(mapCfg);
                fillMapSet(mapCfg);
            }
        }

        private void btnRemoveSetting_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(this, "确定删除该设置？删除后将不能恢复！", "确认", MessageBoxButtons.OKCancel) == DialogResult.OK)
            {
                CityRoadMapManager.GetInstance().CityRoadMapList.Remove(curMapCfg);
                fillMapSet(null);
            }
        }

        private void btnSaveSetting_Click(object sender, EventArgs e)
        {
            curMapCfg.Name = txtMapsName.Text;
            curMapCfg.GridMapPath = edtGridMap.Text;
            curMapCfg.GridNameColumn = cbxGridColumn.Text;
            curMapCfg.StreetInjectTablesList.Clear();
            foreach (object item in listBoxRoadMap.Items)
            {
                curMapCfg.StreetInjectTablesList.Add((StreetInjectTableInfo)item);
            }

            CityRoadMapManager.GetInstance().Save();
        }
    }
}
