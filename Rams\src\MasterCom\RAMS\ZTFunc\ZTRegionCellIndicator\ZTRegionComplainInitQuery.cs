﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 网格投诉量统计界面初始化数据查询
    /// </summary>
    public class ZTRegionComplainInitSQLQuery : QueryBase
    {
        public ZTRegionComplainInitSQLQuery(MainModel mainModel)
            : base(mainModel)
        { 
            
        }

        public override string Name
        {
            get { return "网格投诉量统计界面初始化数据"; }
        }


        public override string IconName
        {
            get { return ""; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        private readonly List<string> gsmMenuList = new List<string>();
        public List<string> GSMMenuItems
        {
            get { return gsmMenuList; }
        }

        private readonly List<string> tdMenuList = new List<string>();
        public List<string> TDMenuList
        {
            get { return tdMenuList; }
        }

        private List<string> networks = new List<string>();
        public List<string> Networks
        {
            get { return networks; }
        }

        protected override void query()
        {
            ZTRegionInitMenuSQLQuery menuQuery = new ZTRegionInitMenuSQLQuery(MainModel);
            menuQuery.ReportName = "GSM区域评估报表";
            menuQuery.Query();
            this.gsmMenuList.Clear();
            this.gsmMenuList.AddRange(menuQuery.MenuItems);
            menuQuery.ReportName = "TD区域评估报表";
            menuQuery.Query();
            this.tdMenuList.Clear();
            this.tdMenuList.AddRange(menuQuery.MenuItems);
            ZTRegionComplainInitNetworkSQLQuery networkQuery = new ZTRegionComplainInitNetworkSQLQuery(MainModel);
            networkQuery.Query();
            this.networks = networkQuery.Networks;
        }
    }
}
