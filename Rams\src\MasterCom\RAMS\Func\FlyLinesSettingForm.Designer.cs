﻿namespace MasterCom.RAMS.Func
{
    partial class FlyLinesSettingForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.Label LabelOpacity;
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnParam = new System.Windows.Forms.Button();
            this.txtParam = new System.Windows.Forms.TextBox();
            this.rbtnByServeCell = new System.Windows.Forms.RadioButton();
            this.rbtnBySerial = new System.Windows.Forms.RadioButton();
            this.btnApply = new System.Windows.Forms.Button();
            this.btnClear = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.rdByRxlev = new System.Windows.Forms.RadioButton();
            this.rdTop1Only = new System.Windows.Forms.RadioButton();
            this.numRxLevFlyLine = new System.Windows.Forms.NumericUpDown();
            this.cbxLimiteDistance = new System.Windows.Forms.CheckBox();
            this.numDistanceLimit = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxForEvent = new System.Windows.Forms.CheckBox();
            this.cbxForMR = new System.Windows.Forms.CheckBox();
            this.TrackBarOpacity = new System.Windows.Forms.TrackBar();
            LabelOpacity = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevFlyLine)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceLimit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.btnParam);
            this.groupBox1.Controls.Add(this.txtParam);
            this.groupBox1.Controls.Add(this.rbtnByServeCell);
            this.groupBox1.Controls.Add(this.rbtnBySerial);
            this.groupBox1.Location = new System.Drawing.Point(13, 13);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(278, 68);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "着色方式";
            // 
            // btnParam
            // 
            this.btnParam.Location = new System.Drawing.Point(195, 17);
            this.btnParam.Name = "btnParam";
            this.btnParam.Size = new System.Drawing.Size(77, 23);
            this.btnParam.TabIndex = 4;
            this.btnParam.Text = "选择";
            this.btnParam.UseVisualStyleBackColor = true;
            this.btnParam.Click += new System.EventHandler(this.btnParam_Click);
            // 
            // txtParam
            // 
            this.txtParam.Location = new System.Drawing.Point(82, 19);
            this.txtParam.Name = "txtParam";
            this.txtParam.ReadOnly = true;
            this.txtParam.Size = new System.Drawing.Size(107, 21);
            this.txtParam.TabIndex = 3;
            // 
            // rbtnByServeCell
            // 
            this.rbtnByServeCell.AutoSize = true;
            this.rbtnByServeCell.Location = new System.Drawing.Point(17, 46);
            this.rbtnByServeCell.Name = "rbtnByServeCell";
            this.rbtnByServeCell.Size = new System.Drawing.Size(95, 16);
            this.rbtnByServeCell.TabIndex = 2;
            this.rbtnByServeCell.TabStop = true;
            this.rbtnByServeCell.Text = "按主服务小区";
            this.rbtnByServeCell.UseVisualStyleBackColor = true;
            // 
            // rbtnBySerial
            // 
            this.rbtnBySerial.AutoSize = true;
            this.rbtnBySerial.Checked = true;
            this.rbtnBySerial.Location = new System.Drawing.Point(17, 21);
            this.rbtnBySerial.Name = "rbtnBySerial";
            this.rbtnBySerial.Size = new System.Drawing.Size(59, 16);
            this.rbtnBySerial.TabIndex = 1;
            this.rbtnBySerial.TabStop = true;
            this.rbtnBySerial.Text = "按指标";
            this.rbtnBySerial.UseVisualStyleBackColor = true;
            this.rbtnBySerial.CheckedChanged += new System.EventHandler(this.rbtnBySerial_CheckedChanged);
            // 
            // btnApply
            // 
            this.btnApply.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnApply.Location = new System.Drawing.Point(130, 285);
            this.btnApply.Name = "btnApply";
            this.btnApply.Size = new System.Drawing.Size(75, 23);
            this.btnApply.TabIndex = 1;
            this.btnApply.Text = "应用修改";
            this.btnApply.UseVisualStyleBackColor = true;
            this.btnApply.Click += new System.EventHandler(this.btnApply_Click);
            // 
            // btnClear
            // 
            this.btnClear.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnClear.Location = new System.Drawing.Point(219, 285);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(75, 23);
            this.btnClear.TabIndex = 2;
            this.btnClear.Text = "清除飞线";
            this.btnClear.UseVisualStyleBackColor = true;
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.rdByRxlev);
            this.groupBox2.Controls.Add(this.rdTop1Only);
            this.groupBox2.Controls.Add(this.numRxLevFlyLine);
            this.groupBox2.Location = new System.Drawing.Point(13, 99);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(278, 77);
            this.groupBox2.TabIndex = 3;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "扫频着色方式";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(182, 47);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(23, 12);
            this.label1.TabIndex = 10;
            this.label1.Text = "dBm";
            // 
            // rdByRxlev
            // 
            this.rdByRxlev.AutoSize = true;
            this.rdByRxlev.Location = new System.Drawing.Point(17, 46);
            this.rdByRxlev.Name = "rdByRxlev";
            this.rdByRxlev.Size = new System.Drawing.Size(95, 16);
            this.rdByRxlev.TabIndex = 9;
            this.rdByRxlev.TabStop = true;
            this.rdByRxlev.Text = "按信号强度≥";
            this.rdByRxlev.UseVisualStyleBackColor = true;
            this.rdByRxlev.CheckedChanged += new System.EventHandler(this.rdByRxlev_CheckedChanged);
            // 
            // rdTop1Only
            // 
            this.rdTop1Only.AutoSize = true;
            this.rdTop1Only.Location = new System.Drawing.Point(17, 20);
            this.rdTop1Only.Name = "rdTop1Only";
            this.rdTop1Only.Size = new System.Drawing.Size(95, 16);
            this.rdTop1Only.TabIndex = 8;
            this.rdTop1Only.TabStop = true;
            this.rdTop1Only.Text = "只显示第一强";
            this.rdTop1Only.UseVisualStyleBackColor = true;
            // 
            // numRxLevFlyLine
            // 
            this.numRxLevFlyLine.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numRxLevFlyLine.Location = new System.Drawing.Point(114, 42);
            this.numRxLevFlyLine.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxLevFlyLine.Minimum = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numRxLevFlyLine.Name = "numRxLevFlyLine";
            this.numRxLevFlyLine.Size = new System.Drawing.Size(66, 21);
            this.numRxLevFlyLine.TabIndex = 6;
            this.numRxLevFlyLine.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxLevFlyLine.Value = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            // 
            // cbxLimiteDistance
            // 
            this.cbxLimiteDistance.AutoSize = true;
            this.cbxLimiteDistance.Checked = true;
            this.cbxLimiteDistance.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxLimiteDistance.Location = new System.Drawing.Point(30, 189);
            this.cbxLimiteDistance.Name = "cbxLimiteDistance";
            this.cbxLimiteDistance.Size = new System.Drawing.Size(96, 16);
            this.cbxLimiteDistance.TabIndex = 4;
            this.cbxLimiteDistance.Text = "连线距离限制";
            this.cbxLimiteDistance.UseVisualStyleBackColor = true;
            // 
            // numDistanceLimit
            // 
            this.numDistanceLimit.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numDistanceLimit.Location = new System.Drawing.Point(128, 186);
            this.numDistanceLimit.Maximum = new decimal(new int[] {
            640000,
            0,
            0,
            0});
            this.numDistanceLimit.Minimum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numDistanceLimit.Name = "numDistanceLimit";
            this.numDistanceLimit.Size = new System.Drawing.Size(66, 21);
            this.numDistanceLimit.TabIndex = 7;
            this.numDistanceLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistanceLimit.Value = new decimal(new int[] {
            3000,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(198, 190);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 11;
            this.label2.Text = "米";
            // 
            // cbxForEvent
            // 
            this.cbxForEvent.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxForEvent.AutoSize = true;
            this.cbxForEvent.Location = new System.Drawing.Point(76, 287);
            this.cbxForEvent.Name = "cbxForEvent";
            this.cbxForEvent.Size = new System.Drawing.Size(48, 16);
            this.cbxForEvent.TabIndex = 12;
            this.cbxForEvent.Text = "事件";
            this.cbxForEvent.UseVisualStyleBackColor = true;
            this.cbxForEvent.Visible = false;
            // 
            // cbxForMR
            // 
            this.cbxForMR.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxForMR.AutoSize = true;
            this.cbxForMR.Location = new System.Drawing.Point(10, 287);
            this.cbxForMR.Name = "cbxForMR";
            this.cbxForMR.Size = new System.Drawing.Size(60, 16);
            this.cbxForMR.TabIndex = 13;
            this.cbxForMR.Text = "采样点";
            this.cbxForMR.UseVisualStyleBackColor = true;
            this.cbxForMR.Visible = false;
            // 
            // TrackBarOpacity
            // 
            this.TrackBarOpacity.LargeChange = 32;
            this.TrackBarOpacity.Location = new System.Drawing.Point(95, 223);
            this.TrackBarOpacity.Maximum = 255;
            this.TrackBarOpacity.Name = "TrackBarOpacity";
            this.TrackBarOpacity.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.TrackBarOpacity.Size = new System.Drawing.Size(145, 45);
            this.TrackBarOpacity.TabIndex = 54;
            this.TrackBarOpacity.TickFrequency = 32;
            this.TrackBarOpacity.Value = 255;
            this.TrackBarOpacity.Scroll += new System.EventHandler(this.TrackBarOpacity_Scroll);
            // 
            // LabelOpacity
            // 
            LabelOpacity.Location = new System.Drawing.Point(28, 236);
            LabelOpacity.Name = "LabelOpacity";
            LabelOpacity.Size = new System.Drawing.Size(86, 16);
            LabelOpacity.TabIndex = 53;
            LabelOpacity.Text = "透明度: ";
            // 
            // FlyLinesSettingForm
            // 
            this.AcceptButton = this.btnApply;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(310, 315);
            this.Controls.Add(this.TrackBarOpacity);
            this.Controls.Add(LabelOpacity);
            this.Controls.Add(this.cbxForMR);
            this.Controls.Add(this.cbxForEvent);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.numDistanceLimit);
            this.Controls.Add(this.cbxLimiteDistance);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.btnClear);
            this.Controls.Add(this.btnApply);
            this.Controls.Add(this.groupBox1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FlyLinesSettingForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "飞线设置";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevFlyLine)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceLimit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Button btnApply;
        private System.Windows.Forms.Button btnClear;
        private System.Windows.Forms.RadioButton rbtnByServeCell;
        private System.Windows.Forms.RadioButton rbtnBySerial;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.NumericUpDown numRxLevFlyLine;
        private System.Windows.Forms.RadioButton rdByRxlev;
        private System.Windows.Forms.RadioButton rdTop1Only;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txtParam;
        private System.Windows.Forms.Button btnParam;
        private System.Windows.Forms.CheckBox cbxLimiteDistance;
        private System.Windows.Forms.NumericUpDown numDistanceLimit;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox cbxForEvent;
        private System.Windows.Forms.CheckBox cbxForMR;
        private System.Windows.Forms.TrackBar TrackBarOpacity;
    }
}