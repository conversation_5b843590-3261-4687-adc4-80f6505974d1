﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc.ZTReportEventMng
{
    public partial class InportForm : BaseForm
    {
        public InportForm()
        {
            InitializeComponent();
        }
        List<EventLogTD> tdLogSet = null;
        List<EventLogGSM> gsmLogSet = null;
        List<EventLogTD> tdErrorSet = null;
        List<EventLogGSM> gsmErrorSet = null;
        private void miOpen_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Multiselect = false;
            dlg.Filter = "Excel 工作簿(*.xlsx)|*.xlsx|97-2003 Excel 工作簿(*.xls)|*.xls";
            dlg.FilterIndex = 1;

            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            tdLogSet = new List<EventLogTD>();
            tdErrorSet = new List<EventLogTD>();
            gsmLogSet = new List<EventLogGSM>();
            gsmErrorSet = new List<EventLogGSM>();
            Text = dlg.FileName;
            DataSet ds = ExcelNPOIManager.ImportFromExcel(dlg.FileName);
            foreach (DataTable tb in ds.Tables)
            {
                if (tb.TableName.IndexOf("TD") != -1)
                {
                    tdLogSet = getTDLogSet(tb);
                }
                else if (tb.TableName.IndexOf("GSM") != -1)
                {
                    gsmLogSet = getGSMLogSet(tb);
                }
            }
            showRecords(true);
        }

        private List<EventLogTD> getTDLogSet(DataTable tb)
        {
            List<EventLogTD> set = new List<EventLogTD>();
            string info;
            foreach (DataRow row in tb.Rows)
            {
                EventLogTD log = new EventLogTD();
                log.FillFrom(row);
                set.Add(log);
                if (!log.CheckData(out info))
                {
                    tdErrorSet.Add(log);
                }
            }
            return set;
        }

        private List<EventLogGSM> getGSMLogSet(DataTable tb)
        {
            List<EventLogGSM> set = new List<EventLogGSM>();
            foreach (DataRow row in tb.Rows)
            {
                EventLogGSM log = new EventLogGSM();
                log.FillFrom(row);
                set.Add(log);
                string info;
                if (!log.CheckData(out info))
                {
                    gsmErrorSet.Add(log);
                }
            }
            return set;
        }

        private void gridViewTD_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            EventLogTD log = gridViewTD.GetRow(e.RowHandle) as EventLogTD;
            if (log == null)
            {
                return;
            }
            string info;
            if (log.CheckData(out info))
            {
                tdErrorSet.Remove(log);
            }
            else if (!tdErrorSet.Contains(log))
            {
                tdErrorSet.Add(log);
            }
            gridControlTD.RefreshDataSource();
            showStatusInfo();
        }

        private void gridViewGSM_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            EventLogGSM log = gridViewGSM.GetRow(e.RowHandle) as EventLogGSM;
            if (log == null)
            {
                return;
            }
            if (log.CheckData())
            {
                gsmErrorSet.Remove(log);
            }
            else if (!gsmErrorSet.Contains(log))
            {
                gsmErrorSet.Add(log);
            }
            gridControlGSM.RefreshDataSource();
            showStatusInfo();
        }

        private void showRecords(bool showAll)
        {
            List<EventLogGSM> gsm = null;
            List<EventLogTD> td = null;
            if (showAll)
            {
                gsm = gsmLogSet;
                td = tdLogSet;
            }
            else
            {
                gsm = gsmErrorSet;
                td = tdErrorSet;
            }
            gridControlTD.DataSource = td;
            gridControlTD.RefreshDataSource();
            gridControlGSM.DataSource = gsm;
            gridControlGSM.RefreshDataSource();
            showStatusInfo();
        }

        private void showStatusInfo()
        {
            statusInfo.Text = string.Format("TD错误记录{0}条   GSM错误记录{1}条", tdErrorSet.Count, gsmErrorSet.Count);
        }
        private void btnImportAll_Click(object sender, EventArgs e)
        {
            if (gsmErrorSet==null||tdErrorSet==null)
            {
                MessageBox.Show("请加载Excel！");
                return;
            }
            if (tdErrorSet.Count > 0)
            {
                MessageBox.Show("TD测试事件，存在错误记录！");
                return;
            }
            if (gsmErrorSet.Count>0)
            {
                MessageBox.Show("GSM测试事件，存在错误记录！");
                return;
            }
            WaitTextBox.Show("正在往数据库插入数据...",insertThread);
        }

        private void insertThread()
        {
            InsertRecordsIntoDB insert = new InsertRecordsIntoDB(gsmLogSet, tdLogSet);
            try
            {
                insert.Query();
            }
            catch (Exception e)
            {
                MessageBox.Show("插入数据出现错误。" + e.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitTextBox.Close();
            }
        }

        private void gridViewTD_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (!e.Info.IsRowIndicator)
            {
                return;
            }
            EventLogTD log = gridViewTD.GetRow(e.RowHandle) as EventLogTD;
            if (log==null)
            {
                return;
            }
            if (!tdErrorSet.Contains(log))
            {
                return;
            }
            e.Painter.DrawObject(e.Info);
            e.Graphics.FillRectangle(Brushes.Red, e.Bounds.X + 1, e.Bounds.Y + 1, e.Bounds.Width - 3, e.Bounds.Height - 3);
            e.Handled = true;
        }

        private void gridViewTD_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            GridView gv = sender as GridView;
            if (gv==null)
            {
                return;
            }
            EventLog log = gv.GetRow(e.RowHandle) as EventLog;
            if (log == null)
            {
                return;
            }
            string info = string.Empty;
            if (!log.CheckField(e.Column.FieldName, ref info))
            {
                e.Appearance.BackColor = Color.Red;
                e.Appearance.BackColor2 = Color.Red;
            }
        }

        private void gridViewTD_CustomDrawColumnHeader(object sender, DevExpress.XtraGrid.Views.Grid.ColumnHeaderCustomDrawEventArgs e)
        {
            if (e.Column == null)
            {
                return;
            }
            if (e.Column.Caption.Contains("*"))
            {
                e.Painter.DrawObject(e.Info);
                e.Graphics.FillRectangle(new SolidBrush(Color.FromArgb(150, Color.Yellow)), e.Bounds.X + 1, e.Bounds.Y + 1, e.Bounds.Width - 3, e.Bounds.Height - 3);
                e.Handled = true;
            }
        }

        private void toolTipController_GetActiveObjectInfo(object sender, DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs e)
        {
            GridControl gridControl = e.SelectedControl as GridControl;
            if (gridControl == null)
            {
                return;
            }
            ToolTipControlInfo info = null;
            try
            {
                GridView view = gridControl.GetViewAt(e.ControlMousePosition) as GridView;
                if (view == null)
                {
                    return;
                }
                GridHitInfo hi = view.CalcHitInfo(e.ControlMousePosition);
                if (hi.InRowCell)
                {
                    info = new ToolTipControlInfo(new CellToolTipInfo(hi.RowHandle, hi.Column, "cell"), getCellHintText(view, hi.RowHandle, hi.Column));
                    return;
                }
                if (hi.Column != null)
                {
                    info = new ToolTipControlInfo(hi.Column, getColumnHintText(hi.Column));
                    return;
                }

                if (hi.HitTest == GridHitTest.RowIndicator)
                {
                    info = new ToolTipControlInfo(GridHitTest.RowIndicator.ToString() + hi.RowHandle.ToString(), getRowHintText(view, hi.RowHandle));
                    return;
                }
            }
            finally
            {
                e.Info = info;
            }
        }

        private string getColumnHintText(DevExpress.XtraGrid.Columns.GridColumn gridColumn)
        {
            if (gridColumn.Caption.Contains("*"))
            {
                return "黄色列为必填列";
            }
            return string.Empty;
        }

        private string getCellHintText(GridView view, int rowHandle, DevExpress.XtraGrid.Columns.GridColumn gridColumn)
        {
            EventLog log = view.GetRow(rowHandle) as EventLog;
            if (log == null)
            {
                return string.Empty;
            }
            string tips = string.Empty;
            log.CheckField(gridColumn.FieldName, ref tips);
            return tips;
        }

        private string getRowHintText(GridView view, int rowHandle)
        {
            EventLog log = view.GetRow(rowHandle) as EventLog;
            if (log == null)
            {
                return string.Empty;
            }
            string tips = null;
            log.CheckData(out tips);
            return tips;
        }

        private void gridViewGSM_CustomDrawRowIndicator(object sender, RowIndicatorCustomDrawEventArgs e)
        {
            if (!e.Info.IsRowIndicator)
            {
                return;
            }
            EventLogGSM log = gridViewGSM.GetRow(e.RowHandle) as EventLogGSM;
            if (log == null)
            {
                return;
            }
            if (!gsmErrorSet.Contains(log))
            {
                return;
            }
            e.Painter.DrawObject(e.Info);
            e.Graphics.FillRectangle(Brushes.Red, e.Bounds.X + 1, e.Bounds.Y + 1, e.Bounds.Width - 3, e.Bounds.Height - 3);
            e.Handled = true;
        }

        private void radioShowAll_CheckedChanged(object sender, EventArgs e)
        {
            showRecords(radioShowAll.Checked);
        }

       

        
    }
}
