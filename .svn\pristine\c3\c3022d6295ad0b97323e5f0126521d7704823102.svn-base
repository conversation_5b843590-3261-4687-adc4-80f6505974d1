﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTTestPointBlock.Competition
{
    public partial class CustomReporForm : BaseDialog
    {
        public CustomReporForm()
        {
            InitializeComponent();
            splitContainerControl1.Panel2.Enabled = false;
            initBaseData();
        }
        TestPointBlockReportManager reportMng = null;
        public CustomReporForm(TestPointBlockReportManager mng, TestPointBlockReport report)
            : this()
        {
            reportMng = mng;
            fillReportList(report);
        }
        private void fillReportList(TestPointBlockReport selReport)
        {
            listAllReport.Items.Clear();
            foreach (TestPointBlockReport report in reportMng.Reports)
            {
                listAllReport.Items.Add(report);
            }
            if (listAllReport.Items.Count>0)
            {
                if (selReport!=null)
                {
                    listAllReport.SelectedItem = selReport;
                }
                else
                {
                    listAllReport.SelectedIndex = 0;
                }
            }
        }

        private void initBaseData()
        {
            CategoryEnumItem[] svcItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
            fillCategoryView(lvServiceHost, SelReport != null ? SelReport.HostServiceTypeList : null, svcItems);
            fillCategoryView(lvServiceGuest, SelReport != null ? SelReport.GuestServiceTypeList : null, svcItems);
            lblSvcCnt.Text = "[" + lvServiceHost.Items.Count.ToString() + "]";
            lblSvcCntGuest.Text = "[" + lvServiceGuest.Items.Count.ToString() + "]";

            MapFormItemSelection itemSelection =mainModel.MainForm.GetMapForm().ItemSelection;
            ItemSelectionPanel servPanelHost = new ItemSelectionPanel(dropDownSvcHost, lvServiceHost, lblSvcCnt, itemSelection, "ServiceType", true);
            ItemSelectionPanel servPanelGuest = new ItemSelectionPanel(dropDownSvcGuest, lvServiceGuest, lblSvcCntGuest, itemSelection, "ServiceType", true);
            servPanelHost.FreshItems();
            servPanelGuest.FreshItems();
            dropDownSvcHost.Items.Add(new ToolStripControlHost(servPanelHost));
            dropDownSvcGuest.Items.Add(new ToolStripControlHost(servPanelGuest));

            cbxHostLogicalType.Properties.Items.Clear();
            cbxGuestLogicalType.Properties.Items.Clear();
            foreach (string name in Enum.GetNames(typeof(ELogicalType)))
            {
                cbxHostLogicalType.Properties.Items.Add(name); 
                cbxGuestLogicalType.Properties.Items.Add(name); 
            }
        }

        private void fillCategoryView(ListView lv, List<int> projIDs, CategoryEnumItem[] projItems)
        {
            lv.Items.Clear();
            if (projIDs == null)
            {
                foreach (CategoryEnumItem item in projItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.Name;
                    lvi.Tag = item.ID;
                    lv.Items.Add(lvi);
                }
            }
            else
            {
                foreach (int id in projIDs)
                {
                    foreach (CategoryEnumItem item in projItems)
                    {
                        if (id == item.ID)
                        {
                            ListViewItem lvi = new ListViewItem();
                            lvi.Text = item.Description;
                            lvi.Tag = id;
                            lv.Items.Add(lvi);
                        }
                    }
                }
            }
        }

        public TestPointBlockReport SelReport
        {
            get { return listAllReport.SelectedItem as TestPointBlockReport; }
        }

        TestPointOptionDlg optionDlg = null;
        private void btnHostSetting_Click(object sender, EventArgs e)
        {
            btn_TestPointCondition(sender);
        }

        private void btnGuestSetting_Click(object sender, EventArgs e)
        {
            btn_TestPointCondition(sender);
        }

        private void btn_TestPointCondition(object btn)
        {
            TestPointConvergedCondition condition = null;
            List<TPBlockDisplayColumn> cols = null;
            ListView tpView = null;
            ListView colView = null;
            if (btn == btnHostSetting)
            {
                condition = SelReport.HostTestPointCondition;
                cols = SelReport.HostColumns;
                tpView = lvHostTP;
                colView = lvHostColumns;
            }
            else if (btn == btnGuestSetting)
            {
                condition = SelReport.GuestTestPointCondition;
                cols = SelReport.GuestColumns;
                tpView = lvGuestTP;
                colView = lvGuestColumns;
            }
            else
            {
                return;
            }
            if (optionDlg == null || optionDlg.IsDisposed)
            {
                optionDlg = new TestPointOptionDlg();
            }
            optionDlg.SetOptions(condition, cols);
            optionDlg.ShowDialog();
            fillTPConditionView(tpView, condition);
            fillColumnView(colView, cols);
        }


        private void fillTPConditionView(ListView lv, TestPointConvergedCondition tpCondition)
        {
            lv.Items.Clear();
            if (tpCondition == null)
            {
                return;
            }
            foreach (TPConvergedDetailItem item in tpCondition.DetailConditionSet)
            {
                ListViewItem lvItem = new ListViewItem(item.ToString());
                lv.Items.Add(lvItem);
            }
        }

        private void fillColumnView(ListView lv, List<TPBlockDisplayColumn> columns)
        {
            lv.Items.Clear();
            if (columns == null)
            {
                return;
            }
            foreach (TPBlockDisplayColumn col in columns)
            {
                ListViewItem lvI = new ListViewItem(col.ToString());
                lv.Items.Add(lvI);
            }
        }

        private void btnSelSvcHost_Click(object sender, EventArgs e)
        {
            dropDownSvcHost.Closed -= dropDownSvcHost_Closed;
            System.Drawing.Point pt = new System.Drawing.Point(btnSelSvcHost.Width, btnSelSvcHost.Height);
            dropDownSvcHost.Show(btnSelSvcHost, pt, ToolStripDropDownDirection.BelowLeft);
            dropDownSvcHost.Closed += dropDownSvcHost_Closed;
        }

        void dropDownSvcHost_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            if (SelReport != null)
            {
                SelReport.HostServiceTypeList.Clear();
                foreach (ListViewItem item in lvServiceHost.Items)
                {
                    SelReport.HostServiceTypeList.Add((int)item.Tag);
                }
            }
        }

        private void btnSelSvcGuest_Click(object sender, EventArgs e)
        {
            dropDownSvcGuest.Closed -= dropDownSvcGuest_Closed;
            System.Drawing.Point pt = new System.Drawing.Point(btnSelSvcGuest.Width, btnSelSvcGuest.Height);
            dropDownSvcGuest.Show(btnSelSvcGuest, pt, ToolStripDropDownDirection.BelowLeft);
            dropDownSvcGuest.Closed += dropDownSvcGuest_Closed;
        }

        void dropDownSvcGuest_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            if (SelReport != null)
            {
                SelReport.GuestServiceTypeList.Clear();
                foreach (ListViewItem item in lvServiceGuest.Items)
                {
                    SelReport.GuestServiceTypeList.Add((int)item.Tag);
                }
            }
        }

        private void listAllReport_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listAllReport.SelectedItem is TestPointBlockReport)
            {
                btnRemoveReport.Enabled = true;
                splitContainerControl1.Panel2.Enabled = true;
                grpPointInfo.Enabled = true;
                visualizeReport();
            }
            else
            {
                btnRemoveReport.Enabled = false;
                splitContainerControl1.Panel2.Enabled = false;
            }
        }

        private void visualizeReport()
        {
            if (SelReport == null)
            {
                return;
            }
            radioCarrierIDHost.SelectedIndex = SelReport.HostCarrierType - 1;
            radioCarrierIDGuest.SelectedIndex = SelReport.GuestCarrierType - 1;
            numMinTPCnt.Value = (decimal)SelReport.MinTestPointCnt;
            numRadius.Value = (decimal)SelReport.BlockRadius;
            fillServiceView(lvServiceHost, SelReport.HostServiceTypeList);
            lblSvcCnt.Text = "[" + lvServiceHost.Items.Count.ToString() + "]";
            fillServiceView(lvServiceGuest, SelReport.GuestServiceTypeList);
            lblSvcCntGuest.Text = "[" + lvServiceGuest.Items.Count.ToString() + "]";
            fillTPConditionView(lvHostTP, SelReport.HostTestPointCondition);
            fillTPConditionView(lvGuestTP, SelReport.GuestTestPointCondition);
            fillColumnView(lvHostColumns, SelReport.HostColumns);
            fillColumnView(lvGuestColumns, SelReport.GuestColumns);
            cbxGuestLogicalType.SelectedItem = SelReport.GuestTestPointCondition.LogicalType.ToString();
            cbxHostLogicalType.SelectedItem = SelReport.HostTestPointCondition.LogicalType.ToString();
        }

        private void fillServiceView(ListView lv, List<int> ids)
        {
            lv.Items.Clear();
            CategoryEnumItem[] cat = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
            foreach (int id in ids)
            {
                foreach (CategoryEnumItem item in cat)
                {
                    if (id == item.ID)
                    {
                        ListViewItem lvItem = new ListViewItem(item.Description);
                        lvItem.Tag = id;
                        lv.Items.Add(lvItem);
                        break;
                    }
                }
            }
        }

        private void btnRemoveReport_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(this, "确定删除该报表？删除后将不能恢复！", "确认", MessageBoxButtons.OKCancel) == DialogResult.OK)
            {
                reportMng.Reports.Remove(SelReport);
                fillReportList(null);
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            reportMng.Save();
        }

        private void btnNewReport_Click(object sender, EventArgs e)
        {
            TextInputBox box = new TextInputBox("新建报表名称", "名称", "新建报表");
            if (box.ShowDialog() == DialogResult.OK)
            {
                TestPointBlockReport report = new TestPointBlockReport();
                report.Name = box.TextInput;
                reportMng.Reports.Add(report);
                fillReportList(report);
            }
        }

        private void listAllReport_DoubleClick(object sender, EventArgs e)
        {
            if (listAllReport.SelectedItem is TestPointBlockReport)
            {
                TestPointBlockReport rpt = listAllReport.SelectedItem as TestPointBlockReport;
                TextInputBox box = new TextInputBox("更改报表名称", "名称", rpt.Name);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    rpt.Name = box.TextInput;
                    listAllReport.Invalidate();
                }
            }
        }

        private void listAllReport_DrawItem(object sender, DrawItemEventArgs e)
        {
            e.DrawBackground();
            e.DrawFocusRectangle();
            e.Graphics.DrawString(listAllReport.Items[e.Index].ToString(), e.Font, new SolidBrush(e.ForeColor), e.Bounds);
        }

        private void radioCarrierIDHost_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (SelReport!=null)
            {
                SelReport.HostCarrierType = radioCarrierIDHost.SelectedIndex + 1;
            }
        }

        private void radioCarrierIDGuest_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (SelReport != null)
            {
                SelReport.GuestCarrierType = radioCarrierIDGuest.SelectedIndex + 1;
            }
        }

        private void numRadius_ValueChanged(object sender, EventArgs e)
        {
            if (SelReport!=null)
            {
                SelReport.BlockRadius = (double)numRadius.Value;
            }
        }

        private void numMinTPCnt_ValueChanged(object sender, EventArgs e)
        {
            if (SelReport != null)
            {
                SelReport.MinTestPointCnt = (int)numMinTPCnt.Value;
            }
        }

        private void cbxHostLogicalType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (SelReport!=null)
            {
                SelReport.HostTestPointCondition.LogicalType = (ELogicalType)Enum.Parse(typeof(ELogicalType), cbxHostLogicalType.SelectedItem.ToString());
            }
        }

        private void cbxGuestLogicalType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (SelReport != null)
            {
                SelReport.GuestTestPointCondition.LogicalType = (ELogicalType)Enum.Parse(typeof(ELogicalType), cbxGuestLogicalType.SelectedItem.ToString());
            }
        }

    }
}
