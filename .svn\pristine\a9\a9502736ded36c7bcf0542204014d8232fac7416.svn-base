﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYCellAbnormalLongAndLatInfoForm : MinCloseForm
    {
        private List<AbnormalLongLatInfo> abnormalLongLatInfoList;

        public ZTDIYCellAbnormalLongAndLatInfoForm(MainModel mainModel)
        {
            abnormalLongLatInfoList = new List<AbnormalLongLatInfo>();
            InitializeComponent();
        }

        public void FillData(List<AbnormalLongLatInfo> abnormalLongLatInfoList)
        {
            this.abnormalLongLatInfoList = abnormalLongLatInfoList;
            gcShow.DataSource = abnormalLongLatInfoList;

            if (abnormalLongLatInfoList.Count != 0)
            {
                if (abnormalLongLatInfoList[0].GCTDCell != null)
                {
                    colMeanVar.Caption = "平均BLER";
                    colMaxVar.Caption = "最大BLER";
                    colMinVar.Caption = "最小BLER";
                    colVar.Caption = "BLER";
                }
                else if (abnormalLongLatInfoList[0].GCWCell != null)
                {
                    colMeanVar.Visible = false;
                    colMaxVar.Visible = false;
                    colMinVar.Visible = false;
                }
            }
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable table = new DataTable();
                table.Columns.Add("小区名称");
                table.Columns.Add("LAC");
                table.Columns.Add("CI");
                table.Columns.Add("经度");
                table.Columns.Add("纬度");
                table.Columns.Add("场强");
                table.Columns.Add("距离");
                if (abnormalLongLatInfoList.Count != 0 && abnormalLongLatInfoList[0].GCCell != null)
                {
                    table.Columns.Add("平均质量");
                    table.Columns.Add("最大质量");
                    table.Columns.Add("最小质量");
                }
                else if (abnormalLongLatInfoList.Count != 0 && abnormalLongLatInfoList[0].GCTDCell != null)
                {
                    table.Columns.Add("平均BLER");
                    table.Columns.Add("最大BLER");
                    table.Columns.Add("最小BLER");
                }
                else if (abnormalLongLatInfoList.Count != 0 && abnormalLongLatInfoList[0].GCLTECell != null)
                {
                    table.Columns.Add("平均SINR");
                    table.Columns.Add("最大SINR");
                    table.Columns.Add("最小SINR");
                }
                table.Columns.Add("异常采样点数量");
                table.Columns.Add("正常采样点数量");
                table.Columns.Add("异常采样点占比");
                table.Columns.Add("文件名");
                fillRowData(table);
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(table);
            }
            catch
            {
            	//continue
            }
        }

        private void fillRowData(DataTable table)
        {
            foreach (AbnormalLongLatInfo llInfo in abnormalLongLatInfoList)
            {
                DataRow row = table.NewRow();
                row[0] = llInfo.CellName;
                row[1] = llInfo.LAC;
                row[2] = llInfo.CI;
                row[3] = llInfo.Longitude;
                row[4] = llInfo.Latitude;
                row[5] = llInfo.MeanRxlev;
                row[6] = llInfo.MeanDistance;
                if (llInfo.GCCell != null || llInfo.GCTDCell != null || llInfo.GCLTECell != null)
                {
                    row[7] = llInfo.MeanVar;
                    row[8] = llInfo.MaxVar;
                    row[9] = llInfo.MinVar;
                    row[10] = llInfo.PointNum;
                    row[11] = llInfo.NormalPointNum;
                    row[12] = llInfo.AbnormalRate;
                    row[13] = llInfo.FileName;
                }
                else
                {
                    row[7] = llInfo.PointNum;
                    row[8] = llInfo.NormalPointNum;
                    row[9] = llInfo.AbnormalRate;
                    row[10] = llInfo.FileName;
                }
                table.Rows.Add(row);
                foreach (AbnormalLongLatPoint pointInfo in llInfo.PointList)
                {
                    row = table.NewRow();
                    row[0] = "";
                    row[1] = "";
                    row[2] = "";
                    row[3] = pointInfo.Longitude;
                    row[4] = pointInfo.Latitude;
                    row[5] = pointInfo.Rxlev;
                    row[6] = pointInfo.Distance;
                    row[7] = pointInfo.Var == null ? "" : Math.Round((double)pointInfo.Var, 2).ToString();
                    table.Rows.Add(row);
                }
            }
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            int[] hRow = gridView1.GetSelectedRows();
            if (hRow.Length == 0)
            {
                return;
            }
            AbnormalLongLatInfo info = (AbnormalLongLatInfo)gridView1.GetRow(hRow[0]);
            if (info == null)
            {
                return;
            }
            MainModel.GetInstance().ClearDTData();
            foreach (AbnormalLongLatPoint p in info.PointList)
            {
                MainModel.GetInstance().DTDataManager.Add(p.TPoint);
            }
            setSelectedCell(info.GCCell, info.GCTDCell, info.GCWCell,info.GCLTECell, info.GCNRCell);
            MainModel.GetInstance().MainForm.GetMapForm().GoToView(info.Longitude, info.Latitude, 500);
            MainModel.GetInstance().FireDTDataChanged(this);
        }

        private void gridView2_DoubleClick(object sender, EventArgs e)
        {
            int[] hRow = ((DevExpress.XtraGrid.Views.Grid.GridView)sender).GetSelectedRows();
            if (hRow.Length == 0)
            {
                return;
            }
            AbnormalLongLatPoint info = (AbnormalLongLatPoint)((DevExpress.XtraGrid.Views.Grid.GridView)sender).GetRow(hRow[0]);
            if (info == null)
            {
                return;
            }
            string serialInfoName;
            getSerialInfoName(info, out serialInfoName);
            setSelectedCell(info.GCCell, info.GCTDCell, info.GCWCell,info.GCLTECell, info.GCNRCell);
            MainModel.GetInstance().ClearDTData();
            MainModel.GetInstance().DTDataManager.Add(info.TPoint);
            foreach (MapSerialInfo serialInfo in MainModel.GetInstance().MainForm.GetMapForm().GetDTLayer().SerialInfos)
            {
                if (serialInfo.Name.Equals(serialInfoName))
                {
                    MainModel.GetInstance().MainForm.GetMapForm().GetDTLayer().CurFlyLinesSerialInfo = serialInfo;
                }
            }
            MainModel.GetInstance().DrawFlyLines = true;
            MainModel.GetInstance().FireDTDataChanged(this);
            goToBounds(info.GCCell, info.GCTDCell, info.GCWCell, info.GCLTECell, info.GCNRCell, info.TPoint);
        }

        private void getSerialInfoName(AbnormalLongLatPoint info, out string serialInfoName)
        {
            if (info.TPoint is TestPointDetail)
            {
                serialInfoName = "GSM RxLevSub";
            }
            else if (info.TPoint is TDTestPointDetail)
            {
                serialInfoName = "TD_PCCPCH_RSCP";
            }
            else if (info.TPoint is WCDMATestPointDetail)
            {
                serialInfoName = "W_Reference_RSCP";
            }
            else
            {
                serialInfoName = "";
            }
        }

        private void setSelectedCell(Cell GCCell, TDCell GCTDCell, WCell GCWCell, LTECell GCLTECell, NRCell nrCell)
        {
            if (GCCell != null)
            {
                MainModel.GetInstance().SelectedCell = GCCell;
            }
            else if (GCTDCell != null)
            {
                MainModel.GetInstance().SelectedTDCell = GCTDCell;
            }
            else if (GCWCell != null)
            {
                MainModel.GetInstance().SelectedWCell = GCWCell;
            }
            else if (GCLTECell != null)
            {
                MainModel.GetInstance().SelectedLTECell = GCLTECell;
            }
            else if (nrCell != null)
            {
                MainModel.GetInstance().SetSelectedNRCell(nrCell);
            }
        }

        private void goToBounds(Cell GCCell, TDCell GCTDCell, WCell GCWCell, LTECell GCLTECell, NRCell nrCell,TestPoint tp)
        {
            double maxLongitude = 0;
            double minLongitude;
            double maxLatitude = 0;
            double minLatitude;
            double cellLongitude = 0;
            double cellLatitude = 0;
            if (GCCell != null)
            {
                cellLongitude = GCCell.Longitude;
                cellLatitude = GCCell.Latitude;
            }
            else if (GCTDCell != null)
            {
                cellLongitude = GCTDCell.Longitude;
                cellLatitude = GCTDCell.Latitude;
            }
            else if (GCWCell != null)
            {
                cellLongitude = GCWCell.Longitude;
                cellLatitude = GCWCell.Latitude;
            }
            else if (GCLTECell != null)
            {
                cellLongitude = GCLTECell.Longitude;
                cellLatitude = GCLTECell.Latitude;
            }
            else if (nrCell != null)
            {
                cellLongitude = nrCell.Longitude;
                cellLatitude = nrCell.Latitude;
            }
            maxLongitude = cellLongitude > tp.Longitude ? cellLongitude : tp.Longitude;
            minLongitude = cellLongitude < tp.Longitude ? cellLongitude : tp.Longitude;
            maxLatitude = cellLatitude > tp.Latitude ? cellLatitude : tp.Latitude;
            minLatitude = cellLatitude < tp.Latitude ? cellLatitude : tp.Latitude;

            MasterCom.MTGis.DbRect rect = new MasterCom.MTGis.DbRect(minLongitude, minLatitude, maxLongitude, maxLatitude);
            MainModel.GetInstance().MainForm.GetMapForm().GoToViewCellsBounds(rect);
        }
    }
}
