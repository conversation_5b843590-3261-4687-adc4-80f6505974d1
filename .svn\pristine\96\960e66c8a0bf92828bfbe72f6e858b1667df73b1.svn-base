using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using BrightIdeasSoftware;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Func
{
    public partial class LteCellServiceConditionForm : MinCloseForm
    {
        public LteCellServiceConditionForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            olvColumnRegion.AspectGetter = delegate (object row)
            {
                if (row is RegionLteCellServiceCondition)
                {
                    RegionLteCellServiceCondition item = row as RegionLteCellServiceCondition;
                    return item.regionName;
                }
                return "";
            };
            setLteCellServiceCondition();
            tlvCellsInfo.CanExpandGetter = delegate (object row)
            {
                if (row is RegionLteCellServiceCondition)
                {
                    return true;
                }
                return false;
            };
            tlvCellsInfo.ChildrenGetter = delegate (object row)
            {
                RegionLteCellServiceCondition item = row as RegionLteCellServiceCondition;
                return item.lteCellServiceConditions;
            };
        }

        private void setLteCellServiceCondition()
        {
            olvColumnCellName.AspectGetter = delegate (object row)
            {
                if (row is LteCellServiceCondition)
                {
                    LteCellServiceCondition item = row as LteCellServiceCondition;
                    if (item.cell == null)
                    {
                        return item.cellName;
                    }
                    return item.cell.Name;
                }
                return "";
            };
            olvColumnCellCode.AspectGetter = delegate (object row)
            {
                if (row is LteCellServiceCondition)
                {
                    LteCellServiceCondition item = row as LteCellServiceCondition;
                    if (item.cell == null)
                    {
                        return "";
                    }
                    return item.cell.Code;
                }
                return "";
            };

            olvColumnCellLAC.AspectGetter = delegate (object row)
            {
                if (row is LteCellServiceCondition)
                {
                    LteCellServiceCondition item = row as LteCellServiceCondition;
                    if (item.cell == null)
                    {
                        return item.lac;
                    }
                    return item.cell.TAC;
                }
                return "";
            };
            olvColumnCellCI.AspectGetter = delegate (object row)
            {
                if (row is LteCellServiceCondition)
                {
                    LteCellServiceCondition item = row as LteCellServiceCondition;
                    if (item.cell == null)
                    {
                        return item.ci;
                    }
                    return item.cell.ECI;
                }
                return "";
            };
            olvColumnServiceSeconds.AspectGetter = delegate (object row)
            {
                if (row is LteCellServiceCondition)
                {
                    LteCellServiceCondition item = row as LteCellServiceCondition;
                    return item.ServiceSeconds;
                }
                return "";
            };
            olvColumnServiceTimes.AspectGetter = delegate (object row)
            {
                if (row is LteCellServiceCondition)
                {
                    LteCellServiceCondition item = row as LteCellServiceCondition;
                    return item.serviceTimes;
                }
                return "";
            };
            olvColumnCategory.AspectGetter = delegate (object row)
            {
                if (row is LteCellServiceCondition)
                {
                    LteCellServiceCondition item = row as LteCellServiceCondition;
                    return item.Category;
                }
                return "";
            };
        }

        public void FillData(List<RegionLteCellServiceCondition> lteCellServiceConditions)
        {
            tlvCellsInfo.ClearObjects();
            tlvCellsInfo.SetObjects(lteCellServiceConditions);
            if (lteCellServiceConditions.Count == 1)
            {
                tlvCellsInfo.ExpandAll();
            }
        }

        private void tlvCellsInfo_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (tlvCellsInfo.SelectedObject is LteCellServiceCondition)
            {
                LteCellServiceCondition cellServiceCondition = tlvCellsInfo.SelectedObject as LteCellServiceCondition;
                MainModel.MainForm.GetMapForm().GoToView(cellServiceCondition.cell.Longitude, cellServiceCondition.cell.Latitude);
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            tlvCellsInfo.ExpandAll();
        }

        private void miCloseAll_Click(object sender, EventArgs e)
        {
            tlvCellsInfo.CollapseAll();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(tlvCellsInfo);
        }
    }
}