﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using System.Runtime.Serialization;
using System.Drawing;

namespace MasterCom.RAMS.CQT
{
    public class CQTPointMapLayer : CustomDrawLayer, IKMLExport
    {
        readonly Font fontLabel = new Font(new FontFamily("宋体"), 8, FontStyle.Bold);
        public CQTPointMapLayer(MapOperation mp, string name)
            : base(mp, name)
        {
            this.VisibleScaleEnabled = true;
            this.VisibleScale = new VisibleScale(0, 100000);
            CQTPoints2Show = new List<CQTPoint>();
        }
        
        public List<CQTPoint> CQTPoints2Show { get; set; }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (CQTPoints2Show == null || CQTPoints2Show.Count == 0)
            {
                return;
            }
            updateRect.Inflate((int)(40 * 10000 / Map.Scale), (int)(40 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            drawCQTPoints(graphics, dRect);
        }

        private void drawCQTPoints(Graphics graphics, DbRect dRect)
        {
            if (CQTPoints2Show==null)
            {
                return;
            }
            CQTKPIDataManager kpiDataMng=CQTKPIDataManager.GetInstance();
            List<System.Drawing.Rectangle> labelRectangles = new List<System.Drawing.Rectangle>();
            float radius = (float)(2000 / Map.Scale);
            foreach (CQTPoint pnt in CQTPoints2Show)
            {
                if (dRect.IsPointInThisRect(pnt.LTLongitude, pnt.LTLatitude))
                {
                    DbPoint dPoint = new DbPoint(pnt.LTLongitude, pnt.LTLatitude);
                    PointF point;
                    this.Map.ToDisplay(dPoint, out point);
                    RectangleF rect = new RectangleF(point.X - radius * 128, point.Y - radius * 256, radius * 256, radius * 256);
                    Image img = null;
                    Color color;
                    kpiDataMng.GetCQTPointImageByCurShowColorColumn(pnt, out img, out color);
                    if (img == null)
                    {
                        img = Properties.Resources.cqtDefault;
                        color = Color.Gray;
                    }
                    graphics.DrawImage(img, rect);
                    if (MainModel.SelCQTPoint == pnt)
                    {
                        graphics.DrawRectangle(new Pen(Brushes.Red, 2), rect.Left, rect.Top, rect.Width, rect.Height);
                    }
                    graphics.DrawRectangle(new Pen(color, 2), point.X - 2.0f, point.Y - 2.0f, 2.0f, 2.0f);

                    SizeF size = graphics.MeasureString(pnt.Name, fontLabel);
                    size.Height *= 0.8f;
                    System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
                    drawRegionPoint(graphics, labelRectangles, pnt, point, size, rectangle);
                }
            }
        }

        private void drawRegionPoint(Graphics graphics, List<Rectangle> labelRectangles, CQTPoint pnt, PointF point, SizeF size, Rectangle rectangle)
        {
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in labelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.TranslateTransform(point.X, point.Y);
                graphics.FillRectangle(Brushes.Wheat, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(pnt.Name, fontLabel, Brushes.Black, 3, -size.Height / 2);
                labelRectangles.Add(rectangle);
                graphics.ResetTransform();
            }
        }

        protected override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            mainModel.SelCQTPoint = null;
            MapForm.MapEventArgs ea = e as MapForm.MapEventArgs;
            if (ea == null || CQTPoints2Show == null || CQTPoints2Show.Count == 0)
                return;
            MapOperation2 mop2 = ea.MapOp2;
            selectPoint(mop2);
        }

        CQTPointInfoDlg infoDlg = new CQTPointInfoDlg();
        protected void selectPoint(MapOperation2 mop2)
        {
            if (!IsVisible)
                return;
            PointF selPoint;
            DbRect gbound = mop2.GetRegion().Bounds;
            this.Map.ToDisplay(new DbPoint(gbound.x1, gbound.y1), out selPoint);
            float radius = (float)(2000 / Map.Scale);
            for (int i = CQTPoints2Show.Count - 1; i >= 0; i--)
            {
                CQTPoint addr = CQTPoints2Show[i];
                DbPoint dPoint = new DbPoint(addr.LTLongitude, addr.LTLatitude);
                PointF point;
                this.Map.ToDisplay(dPoint, out point);
                if (point.X < 0 || point.Y < 0)
                {
                    continue;
                }
                RectangleF rect = new RectangleF(point.X - radius * 128, point.Y - radius * 256, radius * 256, radius * 256);
                if (rect.Contains(selPoint.X, selPoint.Y))
                {
                    mainModel.SelCQTPoint = addr;
                    if (infoDlg == null || infoDlg.IsDisposed)
                    {
                        infoDlg = new CQTPointInfoDlg();
                    }
                    infoDlg.FillData(addr);
                    infoDlg.Location = new Point((int)(rect.Right + rect.Width), (int)(rect.Bottom + rect.Height));
                    infoDlg.WindowState = System.Windows.Forms.FormWindowState.Normal;
                    infoDlg.Visible = true;
                    infoDlg.BringToFront();
                    return;
                }
            }
        }

        public override void LayerDispose()
        {
            if (infoDlg!=null)
            {
                infoDlg.Dispose();
            }
            CQTPoints2Show = null;
            base.LayerDispose();
        }

        #region IKMLExport Members

        public void ExportKml(KMLExporter exporter, System.Xml.XmlElement parentElem)
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}
