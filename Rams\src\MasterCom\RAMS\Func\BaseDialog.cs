using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class BaseDialog : BaseForm
    {
        public BaseDialog()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        private void BaseDialog_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (DialogResult == DialogResult.Retry)
            {
                e.Cancel = true;
            }
        }
        protected override void setCurFuncLogItem(object sender, EventArgs e)
        {
            //
        }
        protected override void clearCurFuncLogItem(object sender, EventArgs e)
        {
            //
        }
    }
}