﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment_NR
{
    public class CoverLapCell_NR : IProblemData_NR
    {
        public override string ToString()
        {
            return "过覆盖";
        }
        public NRCell Cell
        {
            get;
            set;
        }

        int totalPntCnt = 0;
        public void AddTestPoint(TestPoint tp, bool coverLap)
        {
            totalPntCnt++;
            if (coverLap)
            {
                coverLapPoints.Add(tp);
            }
        }

        private readonly List<TestPoint> coverLapPoints = new List<TestPoint>();
        public double CellCvrDisMax
        {
            get;
            private set;
        }
        public CoverLapCell_NR(NRCell nrCell, CoverLapCondition_NR coverLapCondition)
        {
            double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(nrCell
               , coverLapCondition.CoverSiteNum);
            CellCvrDisMax = radiusOfCell * coverLapCondition.CvrDisFactorMax;
        }

        public bool Filter(double coverLapRateMin)
        {
            double rate = 100.0 * coverLapPoints.Count / totalPntCnt;
            return rate < coverLapRateMin;
        }


        #region IRelatedData_NR 成员

        public List<TestPoint> TestPoints
        {
            get { return coverLapPoints; }
        }

        public List<Event> Events
        {
            get { return new List<Event>(); }
        }

        public List<string> Cells
        {
            get
            {
                List<string> names = new List<string>();
                names.Add(this.Cell.Name);
                return names;
            }
        }

        public string roadDesc { get; set; }
        public string RoadDesc
        {
            get
            {
                getRoadDesc();
                return roadDesc;
            }
        }

        public void MakeSummary()
        {
            getRoadDesc();
        }

        private void getRoadDesc()
        {
            if (roadDesc != null)
            {
                return;
            }

            List<double> lng = new List<double>();
            List<double> lat = new List<double>();
            if (TestPoints != null)
            {
                float rsrpMin = float.MaxValue;
                float rsrpMax = float.MinValue;
                float rsrpSum = 0;
                int rsrpNum = 0;
                float sinrMin = float.MaxValue;
                float sinrMax = float.MinValue;
                float sinrSum = 0;
                int sinrNum = 0;
                foreach (TestPoint tp in TestPoints)
                {
                    lng.Add(tp.Longitude);
                    lat.Add(tp.Latitude);
                    float? rsrpObj = null;
                    float? sinrObj = null;
                    if (tp is TestPoint_NR)
                    {
                        rsrpObj = (float?)tp["NR_SS_RSRP"];
                        sinrObj = (float?)tp["NR_SS_SINR"];
                    }

                    addRsrpInfo(ref rsrpMin, ref rsrpMax, ref rsrpSum, ref rsrpNum, rsrpObj);
                    addSinrInfo(ref sinrMin, ref sinrMax, ref sinrSum, ref sinrNum, sinrObj);
                }
                setRsrpInfo(rsrpMin, rsrpMax, rsrpSum, rsrpNum);
                setSinrInfo(sinrMin, sinrMax, sinrSum, sinrNum);
            }

            if (Events != null)
            {
                foreach (Event evt in Events)
                {
                    lng.Add(evt.Longitude);
                    lat.Add(evt.Latitude);
                }
            }
            roadDesc = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(lng, lat);
        }

        private static void addRsrpInfo(ref float rsrpMin, ref float rsrpMax, ref float rsrpSum, ref int rsrpNum, float? rsrpObj)
        {
            if (rsrpObj != null && -141 <= rsrpObj && rsrpObj <= 25)
            {
                rsrpMin = Math.Min(rsrpMin, (float)rsrpObj);
                rsrpMax = Math.Max(rsrpMax, (float)rsrpObj);
                rsrpSum += (float)rsrpObj;
                rsrpNum++;
            }
        }

        private static void addSinrInfo(ref float sinrMin, ref float sinrMax, ref float sinrSum, ref int sinrNum, float? sinrObj)
        {
            if (sinrObj != null && -50 <= sinrObj && sinrObj <= 50)
            {
                sinrMin = Math.Min(sinrMin, (float)sinrObj);
                sinrMax = Math.Max(sinrMax, (float)sinrObj);
                sinrSum += (float)sinrObj;
                sinrNum++;
            }
        }

        private void setRsrpInfo(float rsrpMin, float rsrpMax, float rsrpSum, int rsrpNum)
        {
            if (float.MinValue != rsrpMax)
            {
                this.RSRPMax = rsrpMax;
            }
            if (float.MaxValue != rsrpMin)
            {
                this.RSRPMin = rsrpMin;
            }
            if (rsrpNum != 0)
            {
                this.RSRPAvg = (float)Math.Round(1.0 * rsrpSum / rsrpNum, 2);
            }
        }

        private void setSinrInfo(float sinrMin, float sinrMax, float sinrSum, int sinrNum)
        {
            if (float.MinValue != sinrMax)
            {
                this.SINRMax = sinrMax;
            }
            if (float.MaxValue != sinrMin)
            {
                this.SINRMin = sinrMin;
            }
            if (sinrNum != 0)
            {
                this.SINRAvg = (float)Math.Round(1.0 * sinrSum / sinrNum, 2);
            }
        }

        #endregion

        #region IProblemData_NR 成员

        public float? RSRPMin
        {
            get;
            private set;
        }

        public float? RSRPMax
        {
            get;
            private set;
        }

        public float? RSRPAvg
        {
            get;
            private set;
        }

        public float? SINRMin
        {
            get;
            private set;
        }

        public float? SINRMax
        {
            get;
            private set;
        }

        public float? SINRAvg
        {
            get;
            private set;
        }

        #endregion
    }
}
