﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Serialization;

namespace MasterCom.RAMS.ZTFunc
{ 
    /// <summary>
    /// GSM性能指标门限
    /// </summary>
    [XmlRoot("GSM性能指标门限")]
    public class GSMPerformanceThreadholdInf
    {
        
        [XmlArray("items"), XmlArrayItem("item")]
        private IndicatorThresholdInf[] thresholdInf;

        public IndicatorThresholdInf[] ThresholdInf
        {
            get { return thresholdInf; }
            set { thresholdInf = value; }
        }
        public GSMPerformanceThreadholdInf()
        {
            thresholdInf = new IndicatorThresholdInf[13];
            thresholdInf[0] = new IndicatorThresholdInf("信令信道分配成功率");
            thresholdInf[1] = new IndicatorThresholdInf("话音信道分配成功率(不含切)");
            thresholdInf[2] = new IndicatorThresholdInf("无线接通率");

            thresholdInf[3] = new IndicatorThresholdInf("信令信道拥塞率");
            thresholdInf[4] = new IndicatorThresholdInf("话音信道拥塞率(不含切)");
            thresholdInf[5] = new IndicatorThresholdInf("话音信道掉话率(不含切)");

            thresholdInf[6] = new IndicatorThresholdInf("非PBGT切换占比");
            thresholdInf[7] = new IndicatorThresholdInf("下行话音质量");
            thresholdInf[8] = new IndicatorThresholdInf("切换成功率");

            thresholdInf[9] = new IndicatorThresholdInf("上行TBF建立成功率");
            thresholdInf[10] = new IndicatorThresholdInf("下行TBF建立成功率");
            thresholdInf[11] = new IndicatorThresholdInf("下行TBF掉线率");

            thresholdInf[12] = new IndicatorThresholdInf("PDCH分配成功率");
        }
    }
}
