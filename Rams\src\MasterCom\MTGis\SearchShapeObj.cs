﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.MTGis
{
    public class SearchShapeObj:IComparable
    {
        public string Name { get; set; }
        public SfLayerInfo sfLayerInfo { get; set; }
        public int shpIndex { get; set; }
        public override string ToString()
        {
            return Name;
        }

        #region IComparable 成员

        public int CompareTo(object obj)
        {
            SearchShapeObj other = obj as SearchShapeObj;
            if (obj != null)
            {
                return string.Compare(Name, other.Name);
            }
            return -1;
        }

        #endregion
    }
}
