﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.Problem
{
    public partial class ProblemOptionDlg : BaseDialog
    {
        public ProblemOptionDlg()
        {
            InitializeComponent();
            this.cmbWeakCvr.SelectedIndex = 0;
            this.cmbPoorQual.SelectedIndex = 0;
            this.numPoorQual.Maximum = this.numWeakCvr.Maximum = decimal.MaxValue;
            this.numPoorQual.Minimum = this.numWeakCvr.Minimum = decimal.MinValue;
            gridCtrlEvt.DataSource = events;
        }
        private List<EventInfo> events = new List<EventInfo>();

        private void txtExp_DoubleClick(object sender, EventArgs e)
        {
            TextBox txt = sender as TextBox;
            FormulaEditor formulaEditor = new FormulaEditor();
            formulaEditor.Formula = txt.Text;
            if (formulaEditor.ShowDialog() == DialogResult.OK)
            {
                txt.Text = formulaEditor.Formula;
            }
        }

        private ProblemCondition cond;
        public ProblemCondition Condition
        {
            get
            {
                return cond;
            }
            set
            {
                if (value==null)
                {
                    return;
                }
                txtQualName.Text = value.PoorQualName;
                txtIdxNameCM.Text = value.WeakCvrNameCM;
                txtIdxNameCT.Text = value.WeakCvrNameCT;
                txtIdxNameCU.Text = value.WeakCvrNameCU;
                txtPoorQualExp.Text = value.PoorQualExp;
                txtWeakCvrExpCM.Text = value.WeakCvrExpCM;
                txtWeakCvrExpCT.Text = value.WeakCvrExpCT;
                txtWeakCvrExpCU.Text = value.WeakCvrExpCU;
                numPoorQual.Value = (decimal)value.PoorQualValue;
                numWeakCvr.Value = (decimal)value.WeakCvrValue;
                cmbPoorQual.SelectedIndex = (int)value.PoorQualOperator;
                cmbWeakCvr.SelectedIndex = (int)value.WeakCvrOperator;
                this.events.Clear();
                this.events.AddRange(value.Events);
                gridCtrlEvt.RefreshDataSource();
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (txtIdxNameCM.Text.Trim().Length == 0)
            {
                MessageBox.Show("请设置移动覆盖指标名称");
                return;
            }
            if (txtWeakCvrExpCM.Text.Trim().Length == 0)
            {
                MessageBox.Show("请设置移动弱覆盖公式！");
                return;
            }

            if (txtIdxNameCU.Text.Trim().Length == 0)
            {
                MessageBox.Show("请设置联通覆盖指标名称");
                return;
            }
            if (txtWeakCvrExpCU.Text.Trim().Length == 0)
            {
                MessageBox.Show("请设置联通弱覆盖公式！");
                return;
            }

            if (txtIdxNameCT.Text.Trim().Length == 0)
            {
                MessageBox.Show("请设置电信覆盖指标名称");
                return;
            }
            if (txtWeakCvrExpCT.Text.Trim().Length == 0)
            {
                MessageBox.Show("请设置电信弱覆盖公式！");
                return;
            }

            if (txtQualName.Text.Trim().Length == 0)
            {
                MessageBox.Show("请设置质差名称！");
                return;
            }
            if (txtPoorQualExp.Text.Trim().Length == 0)
            {
                MessageBox.Show("请设置质差公式！");
                return;
            }

            if (events.Count == 0)
            {
                MessageBox.Show("请设置异常事件！");
                return;
            }
            cond = new ProblemCondition();
            cond.Events = events;
            cond.PoorQualExp = txtPoorQualExp.Text;
            cond.PoorQualOperator = (MasterCom.Util.RelationalOperator)cmbPoorQual.SelectedIndex;
            cond.PoorQualValue = (double)numPoorQual.Value;
            cond.WeakCvrNameCM = txtIdxNameCM.Text;
            cond.WeakCvrNameCT = txtIdxNameCT.Text;
            cond.WeakCvrNameCU = txtIdxNameCU.Text;
            cond.WeakCvrExpCM = txtWeakCvrExpCM.Text;
            cond.WeakCvrExpCT = txtWeakCvrExpCT.Text;
            cond.WeakCvrExpCU = txtWeakCvrExpCU.Text;
            cond.WeakCvrOperator = (MasterCom.Util.RelationalOperator)cmbWeakCvr.SelectedIndex;
            cond.WeakCvrValue = (double)numWeakCvr.Value;
            DialogResult = DialogResult.OK;
        }

        private void btnAddEvent_Click(object sender, EventArgs e)
        {
            EventChooserForm eventChooser = EventChooserForm.GetInstance(MainModel);
            if (eventChooser.ShowDialog() == DialogResult.OK)
            {
                foreach (int id in eventChooser.SelectedEventIDs)
                {
                    EventInfo info = EventInfoManager.GetInstance()[id];
                    if (!events.Contains(info))
                    {
                        events.Add(info);
                    }
                }
                gridCtrlEvt.RefreshDataSource();
            }
        }

        private void btnRemoveEvent_Click(object sender, EventArgs e)
        {
            int[] rows = gvEvt.GetSelectedRows();
            for (int i = 0; i < rows.Length; i++)
            {
                rows[i] = gvEvt.GetDataSourceRowIndex(rows[i]);
            }
            foreach (int row in rows)
            {
                events.RemoveAt(row);
            }
            gridCtrlEvt.RefreshDataSource();
        }

        private void btnEmptyEvent_Click(object sender, EventArgs e)
        {
            events.Clear();
            gridCtrlEvt.RefreshDataSource();
        }

    }

}
