using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.UserMng;

namespace MasterCom.RAMS.Model
{
    public class FunctionRole 
    {
        public int ID { get; set; }

        public string Description { get; set; }

        public List<int> Permissions { get; set; } = new List<int>();

        readonly Dictionary<int, bool> permissionModifyDic = new Dictionary<int, bool>();
        public Dictionary<int, bool> PermissionModifyDic
        {
            get { return permissionModifyDic; }
        }
        
        public Dictionary<int, FuncExportPermit> FuncExportPermitDic { get; set; } = new Dictionary<int, FuncExportPermit>();

        readonly Dictionary<int, FuncExportPermit> funcExportPermitModifyDic = new Dictionary<int, FuncExportPermit>();
        public Dictionary<int, FuncExportPermit> FuncExportPermitModifyDic
        {
            get { return funcExportPermitModifyDic; }
        }

        public void UpdatePermission(int subFuncID, bool has)
        {
            if (has)
            {
                if (!Permissions.Contains(subFuncID))
                {
                    Permissions.Add(subFuncID);
                }
                permissionModifyDic[subFuncID] = true;
            }
            else
            {
                Permissions.Remove(subFuncID);
                permissionModifyDic[subFuncID] = false;
            }
        }
        public void UpdateExportPermit(FuncExportPermit funcPermit, bool has)
        {
            if (funcPermit == null)
            {
                return;
            }
            if (has)
            {
                FuncExportPermitDic[funcPermit.SubFuncID] = funcPermit;
                funcExportPermitModifyDic[funcPermit.SubFuncID] = funcPermit;
            }
            else
            {
                FuncExportPermitDic.Remove(funcPermit.SubFuncID);
                funcExportPermitModifyDic[funcPermit.SubFuncID] = null;
            }
        }
        public override string ToString()
        {
            return Name;
        }

        public bool HasRight(int rightID)
        {
            if (Permissions != null)
            {
                return Permissions.Contains(rightID);
            }
            return false;
        }

        public string Name
        {
            get;
            set;
        }

        public static FunctionRole Fill(Content content)
        {
            FunctionRole role = new FunctionRole();
            role.ID = content.GetParamInt();
            role.Name = content.GetParamString();
            role.Description = content.GetParamString();
            return role;
        }
    }
}
