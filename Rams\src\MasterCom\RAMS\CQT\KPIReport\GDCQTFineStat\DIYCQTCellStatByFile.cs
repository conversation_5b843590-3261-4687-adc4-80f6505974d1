﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.CQT;
using MasterCom.MTGis;
using DBDataViewer;

namespace MasterCom.RAMS.Net
{
    public class DIYCQTCellStatByFile : DIYCellStatByFile
    {
        public DIYCQTCellStatByFile(MainModel mm)
            : base(mm)
        {
            this.needFindCell = false;
        }
        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11148, this.Name);
        }
        protected override void query()
        {
            loadReportFromFile();
            if (rptStyleList.Count == 0)
            {
                return;
            }
            curSelStyle = rptStyleList[0];
            curSelStyleContentAll = true;
            //==
            cqtStatCellKPIInfoDic = new Dictionary<CQTWYKey, List<CQTStatCellKPIInfo>>();
            MainModel.CurChinaMobileStatReportData = null;
            MainModel.CurChinaUnicomStatReportData = null;
            MainModel.CurChinaTelecomStatReportData = null;
            MainModel.CurChinaMobileStatReportDataList.Clear();
            MainModel.CurChinaUnicomStatReportDataList.Clear();
            MainModel.CurChinaTelecomStatReportDataList.Clear();
            multiGeometrys = false;
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            if (MainModel.MultiGeometrys && condition.Geometorys.SelectedResvRegions != null && condition.Geometorys.SelectedResvRegions.Count > 0)
            {
                multiGeometrys = true;
            }

            if (!WaitBox.CancelRequest)
            {
                if (Condition.CarrierTypes.Contains(1))
                {
                    queryByCarrier(queryChinaMobileInThread);
                }
                if (Condition.CarrierTypes.Contains(2))
                {
                    queryByCarrier(queryChinaUnicomInThread);
                }
                if (Condition.CarrierTypes.Contains(3))
                {
                    queryByCarrier(queryChinaTelecomInThread);
                }
            }
            validCellDic = new Dictionary<string, object>();
        }

        public Dictionary<CQTWYKey, List<CQTStatCellKPIInfo>> cqtStatCellKPIInfoDic { get; set; }
        Dictionary<string, CQTKPIFinePoint> cqtPointStatDic = null;

        public void FillSubAreaFileByList(List<CQTFileKPIFineData> fileDataList
            , Dictionary<string, CQTKPIFinePoint> cqtPointStatDic)
        {
            this.cqtPointStatDic = cqtPointStatDic;
            condition = CQTPointKPIFineData.qCondition;
            condition.FileInfos.Clear();
            foreach (CQTFileKPIFineData fileInfo in fileDataList)
            {
                condition.FileInfos.Add(fileInfo.DataHeader);
                if (fileInfo.DataHeader.DistrictID > 0)
                {
                    condition.DistrictID = fileInfo.DataHeader.DistrictID;
                }
                if (!condition.DistrictIDs.Contains(fileInfo.DataHeader.DistrictID))
                {
                    condition.DistrictIDs.Add(fileInfo.DataHeader.DistrictID);
                }
            }
        }

        public Dictionary<CQTCellKPIKey, DataUnitAreaKPIQuery> cmDataUnitKPIQueryDic { get; set; }
        public Dictionary<CQTCellKPIKey, DataUnitAreaKPIQuery> cuDataUnitKPIQueryDic { get; set; }
        public Dictionary<CQTCellKPIKey, DataUnitAreaKPIQuery> ctDataUnitKPIQueryDic { get; set; }
        public Dictionary<CQTCellKPIKey, List<DTDataHeader>> cmDataUnitCellFileDic { get; set; }
        public Dictionary<CQTCellKPIKey, List<DTDataHeader>> cuDataUnitCellFileDic { get; set; }
        public Dictionary<CQTCellKPIKey, List<DTDataHeader>> ctDataUnitCellFileDic { get; set; }
        protected void saveDataByRegion(CQTCellKPIKey cqtCellKey, ICell cell, DbPoint dp
            , DataUnitAreaKPIQuery data, int carrierId, DTDataHeader header,bool is频点)
        {
            cqtCellKey.CellName = cell.Name;
            setCellData(cqtCellKey, cell, is频点);
            if (multiGeometrys)
            {
                StringBuilder sb = new StringBuilder(cqtCellKey.CellName);
                foreach (ResvRegion resvRegion in Condition.Geometorys.SelectedResvRegions)
                {
                    if (!resvRegion.GeoOp.CheckPointInRegion(dp.x, dp.y))
                        continue;
                    sb.Append(" " + resvRegion.RegionName);
                    break;
                }
                cqtCellKey.CellName = sb.ToString();
            }
            if (carrierId == 1)//移动
            {
                cmDataUnitKPIQueryDic = dealUnitKPIQuery(cmDataUnitKPIQueryDic, cqtCellKey, data);
                cmDataUnitCellFileDic = dealUnitCellFile(cmDataUnitCellFileDic, cqtCellKey, header);
            }
            else if (carrierId == 2)//联通
            {
                cuDataUnitKPIQueryDic = dealUnitKPIQuery(cuDataUnitKPIQueryDic, cqtCellKey, data);
                cuDataUnitCellFileDic = dealUnitCellFile(cuDataUnitCellFileDic, cqtCellKey, header);
            }
            else if (carrierId == 3)//电信
            {
                ctDataUnitKPIQueryDic = dealUnitKPIQuery(ctDataUnitKPIQueryDic, cqtCellKey, data);
                ctDataUnitCellFileDic = dealUnitCellFile(ctDataUnitCellFileDic, cqtCellKey, header);
            }
        }

        private Dictionary<CQTCellKPIKey, DataUnitAreaKPIQuery> dealUnitKPIQuery(Dictionary<CQTCellKPIKey, DataUnitAreaKPIQuery> dataUnitKPIQueryDic, CQTCellKPIKey cqtCellKey, DataUnitAreaKPIQuery data)
        {
            if (dataUnitKPIQueryDic == null)
            {
                dataUnitKPIQueryDic = new Dictionary<CQTCellKPIKey, DataUnitAreaKPIQuery>();
            }
            DataUnitAreaKPIQuery value;
            if (dataUnitKPIQueryDic.TryGetValue(cqtCellKey, out value))
            {
                value.addStatData(data);
            }
            else
            {
                dataUnitKPIQueryDic[cqtCellKey] = data;
            }

            return dataUnitKPIQueryDic;
        }

        private Dictionary<CQTCellKPIKey, List<DTDataHeader>> dealUnitCellFile(Dictionary<CQTCellKPIKey, List<DTDataHeader>> dataUnitCellFileDic, CQTCellKPIKey cqtCellKey, DTDataHeader header)
        {
            if (dataUnitCellFileDic == null)
            {
                dataUnitCellFileDic = new Dictionary<CQTCellKPIKey, List<DTDataHeader>>();
            }
            if (!dataUnitCellFileDic.ContainsKey(cqtCellKey))
            {
                dataUnitCellFileDic[cqtCellKey] = new List<DTDataHeader>();
            }
            dataUnitCellFileDic[cqtCellKey].Add(header);
            return dataUnitCellFileDic;
        }

        private void setCellData(CQTCellKPIKey cqtCellKey, ICell cell, bool is频点)
        {
            if (cell is Cell)
            {
                Cell gcell = cell as Cell;
                cqtCellKey.LAC = gcell.LAC;
                cqtCellKey.CI = gcell.CI;
                cqtCellKey.Str频点 = gcell.BCCH.ToString();
                cqtCellKey.Str扰码 = gcell.BSIC.ToString();
                cqtCellKey.CellLng = gcell.Longitude;
                cqtCellKey.CellLat = gcell.Latitude;
                cqtCellKey.CellNetType = "GSM";
                cqtCellKey.CellCoverType = gcell.BelongBTS.TypeDescription;
                cqtCellKey.CellAddress = gcell.BelongBTS.Description;
            }
            else if (cell is TDCell)
            {
                TDCell tcell = cell as TDCell;
                cqtCellKey.LAC = tcell.LAC;
                cqtCellKey.CI = tcell.CI;
                cqtCellKey.Str频点 = tcell.FREQ.ToString();
                cqtCellKey.Str扰码 = tcell.CPI.ToString();
                cqtCellKey.CellLng = tcell.Longitude;
                cqtCellKey.CellLat = tcell.Latitude;
                cqtCellKey.CellNetType = "TD";
                cqtCellKey.CellCoverType = tcell.BelongBTS.TypeStringDesc;
                cqtCellKey.CellAddress = tcell.BelongBTS.Description;
            }
            else if (cell is LTECell)
            {
                LTECell lcell = cell as LTECell;
                cqtCellKey.LAC = lcell.TAC;
                cqtCellKey.CI = lcell.ECI;
                cqtCellKey.Str频点 = lcell.EARFCN.ToString();
                cqtCellKey.Str扰码 = lcell.PCI.ToString();
                cqtCellKey.CellLng = lcell.Longitude;
                cqtCellKey.CellLat = lcell.Latitude;
                cqtCellKey.CellNetType = "LTE";
                cqtCellKey.CellCoverType = lcell.BelongBTS.TypeStringDesc;
                cqtCellKey.CellAddress = lcell.BelongBTS.Description;
            }
            else if (cell is UnknowCell)
            {
                UnknowCell uCell = cell as UnknowCell;
                if (!is频点)
                {
                    cqtCellKey.LAC = Convert.ToInt32(uCell.Token.Split('_')[0]);
                    cqtCellKey.CI = Convert.ToInt32(uCell.Token.Split('_')[1]);
                }
                else
                {
                    cqtCellKey.LAC = -99999;
                    cqtCellKey.CI = -99999;
                    cqtCellKey.Str频点 = uCell.Token.Split('_')[0];
                    cqtCellKey.Str扰码 = uCell.Token.Split('_')[1];
                }
            }
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, List<DataUnitAreaKPIQuery> paraRetList, int carrierId, TimePeriod period)
        {
            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curImgColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurImgColumnDef(idpairs, curImgColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_GSM ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_GPRS)
                {
                    int lac = package.Content.GetParamInt();
                    int ci = package.Content.GetParamInt();
                    DataGSM_NewImg newImg = new DataGSM_NewImg();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            bool isValid  = isValidCell(lac, ci);
                            if (isValid)
                            {
                                ICell cell = MainModel.CellManager.GetCurrentCell(lac, ci);
                                if (cell == null)
                                {
                                    cell = new UnknowCell(string.Format("{0}_{1}", lac, ci));
                                }
                                DbPoint dp = new DbPoint(cell.Longitude, cell.Latitude);
                                DataUnitAreaKPIQuery result = new DataUnitAreaKPIQuery();
                                result.addStatData(newImg);
                                CQTCellKPIKey cqtCellKey = new CQTCellKPIKey();
                                InitCQTCellKey(cqtCellKey, header);
                                saveDataByRegion(cqtCellKey,cell, dp, result, carrierId,header,false);
                                retResult.addStatData(newImg);
                            }
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.KPI_LTE_AMR)
                {
                    int lac = package.Content.GetParamInt();
                    int ci = package.Content.GetParamInt();

                    DataLTE newImg = new DataLTE();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            bool isValid = isValidCell(lac, ci);
                            if (isValid)
                            {
                                string strPointName = header.Name.Split('_')[2];
                                double dLng = 0;
                                double dLat = 0;
                                if (cqtPointStatDic.ContainsKey(strPointName))
                                {
                                    dLng = cqtPointStatDic[strPointName].LTLongitude;
                                    dLat = cqtPointStatDic[strPointName].LTLatitude;
                                }
                                ICell cell = getLTECellByMutMethods(header.BeginTime, lac, ci, dLng, dLat);
                                
                                DbPoint dp = new DbPoint(cell.Longitude, cell.Latitude);
                                DataUnitAreaKPIQuery result = new DataUnitAreaKPIQuery();
                                result.addStatData(newImg);
                                CQTCellKPIKey cqtCellKey = new CQTCellKPIKey();
                                InitCQTCellKey(cqtCellKey, header);
                                saveDataByRegion(cqtCellKey, cell, dp, result, carrierId, header, lac > 60000);
                                retResult.addStatData(newImg);
                            }
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_AMR ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_PS ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_VP)
                {
                    int lac = package.Content.GetParamInt();
                    int ci = package.Content.GetParamInt();

                    DataTDSCDMA_NewImg newImg = new DataTDSCDMA_NewImg();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            bool isValid = isValidCell(lac, ci);
                            if (isValid)
                            {
                                ICell tdCell = MainModel.CellManager.GetCurrentTDCell(lac, ci);
                                if (tdCell != null)
                                {
                                    DbPoint dp = new DbPoint(tdCell.Longitude, tdCell.Latitude);
                                    DataUnitAreaKPIQuery result = new DataUnitAreaKPIQuery();
                                    result.addStatData(newImg);
                                    CQTCellKPIKey cqtCellKey = new CQTCellKPIKey();
                                    InitCQTCellKey(cqtCellKey, header);
                                    saveDataByRegion(cqtCellKey, tdCell, dp, result, carrierId, header, false);
                                    retResult.addStatData(newImg);
                                }
                                else
                                {
                                    ICell cell = MainModel.CellManager.GetCurrentCell(lac, ci);
                                    if (cell == null)
                                    {
                                        cell = new UnknowCell(string.Format("{0}_{1}", lac, ci));
                                    }
                                    DbPoint dp = new DbPoint(cell.Longitude, cell.Latitude);
                                    DataUnitAreaKPIQuery result = new DataUnitAreaKPIQuery();
                                    result.addStatData(newImg);
                                    CQTCellKPIKey cqtCellKey = new CQTCellKPIKey();
                                    InitCQTCellKey(cqtCellKey, header);
                                    saveDataByRegion(cqtCellKey, cell, dp, result, carrierId, header, false);
                                    retResult.addStatData(newImg);
                                }
                            }
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_AMR ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PS ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_VP ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PSHS)
                {
                    int lac = package.Content.GetParamInt();
                    int ci = package.Content.GetParamInt();

                    DataWCDMA_AMR newImg = new DataWCDMA_AMR();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            bool isValid = isValidCell(lac, ci);
                            if (isValid)
                            {
                                ICell cell = MainModel.CellManager.GetCurrentWCell(lac, ci);
                                if (cell == null)
                                {
                                    cell = new UnknowCell(string.Format("{0}_{1}", lac, ci));
                                }
                                DbPoint dp = new DbPoint(cell.Longitude, cell.Latitude);
                                DataUnitAreaKPIQuery result = new DataUnitAreaKPIQuery();
                                result.addStatData(newImg);
                                CQTCellKPIKey cqtCellKey = new CQTCellKPIKey();
                                InitCQTCellKey(cqtCellKey, header);
                                saveDataByRegion(cqtCellKey, cell, dp, result, carrierId, header, false);
                                retResult.addStatData(newImg);
                            }
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA_V ||
                         package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA_D ||
                         package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA2000_D)
                {
                    int lac = package.Content.GetParamInt();
                    int ci = package.Content.GetParamInt();

                    DataCDMA_Voice newImg = new DataCDMA_Voice();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            bool isValid = isValidCell(lac, ci);
                            if (isValid)
                            {
                                ICell cell = MainModel.CellManager.GetCurrentCDCell(lac, ci);
                                if (cell == null)
                                {
                                    cell = new UnknowCell(string.Format("{0}_{1}", lac, ci));
                                }
                                DbPoint dp = new DbPoint(cell.Longitude, cell.Latitude);
                                DataUnitAreaKPIQuery result = new DataUnitAreaKPIQuery();
                                result.addStatData(newImg);
                                CQTCellKPIKey cqtCellKey = new CQTCellKPIKey();
                                InitCQTCellKey(cqtCellKey, header);
                                saveDataByRegion(cqtCellKey, cell, dp, result, carrierId, header, false);
                                retResult.addStatData(newImg);
                            }
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                #endregion

                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
            paraRetList.Add(retResult);
        }

        private ICell getLTECellByMutMethods(int paramTime, int paramTac, int paramEci, double paramLng, double paramLat)
        {
            ICell cell = null;
            DateTime dTime = JavaDate.GetDateTimeFromMilliseconds(paramTime * 1000L);
            if (paramTac == 0)
            {
                cell = MainModel.CellManager.GetLTECellByECI(dTime, (int?)paramEci);
            }
            else if (paramTac > 0 && paramTac < 60000)
            {
                cell = MainModel.CellManager.GetCurrentLTECell(paramTac, paramEci);
            }
            else
            {
                cell = MainModel.CellManager.GetNearestLTECellByEARFCNPCI(dTime, (int?)paramTac - 60000, (int?)paramEci, paramLng, paramLat);
            }
            if (cell == null)
            {
                if (paramTac >= 60000)
                {
                    paramTac = paramTac - 60000;
                }
                cell = new UnknowCell(string.Format("{0}_{1}", paramTac, paramEci));
            }
            return cell;
        }

        private void InitCQTCellKey(CQTCellKPIKey cqtCellKey, DTDataHeader header)
        {
            string[] strTmp = header.Name.Split('_');
            cqtCellKey.WYName = strTmp[2];
            cqtCellKey.ChildName = strTmp[3];
            cqtCellKey.StateTime = JavaDate.GetDateTimeFromMilliseconds(header.BeginTime * 1000L).ToString("yyyyMMdd");
        }

        protected override void queryChinaMobileInThread(object o) //查询中国移动KPI统计
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                List<DataUnitAreaKPIQuery> paraPeriodList = new List<DataUnitAreaKPIQuery>();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, paraPeriodList, 1, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "正在统计移动小区的指标...";
                        queryPeriodInfo(clientProxy, package, paraPeriodList, 1, period, false);
                    }
                }
                if (cmDataUnitKPIQueryDic != null)
                {
                    foreach (CQTCellKPIKey cellName in cmDataUnitKPIQueryDic.Keys)
                    {
                        CQTStatCellKPIInfo cqtInfo = CQTStatCellKPIInfo.Fill(cellName);
                        cqtInfo.fileList = cmDataUnitCellFileDic[cellName];
                        cqtInfo.DataUnitAreaKPIQuery =  cmDataUnitKPIQueryDic[cellName];
                        CQTWYKey cqtkey = new CQTWYKey();
                        cqtkey.WYName = cqtInfo.WYName;
                        cqtkey.ChildName = cqtInfo.ChildName;
                        if (!cqtStatCellKPIInfoDic.ContainsKey(cqtkey))
                        {
                            cqtStatCellKPIInfoDic[cqtkey] = new List<CQTStatCellKPIInfo>();
                        }
                        cqtStatCellKPIInfoDic[cqtkey].Add(cqtInfo);
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected override void queryChinaUnicomInThread(object o) //查询中国联通KPI统计
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                List<DataUnitAreaKPIQuery> paraPeriodList = new List<DataUnitAreaKPIQuery>();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, paraPeriodList, 2, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "正在统计联通小区指标...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, paraPeriodList, 2, period, false);
                    }
                }
                if (cuDataUnitAreaKPIQueryDic != null)
                {
                    foreach (CQTCellKPIKey cellName in cuDataUnitKPIQueryDic.Keys)
                    {
                        CQTStatCellKPIInfo cqtInfo = CQTStatCellKPIInfo.Fill(cellName);
                        cqtInfo.fileList = cuDataUnitCellFileDic[cellName];
                        cqtInfo.DataUnitAreaKPIQuery = cuDataUnitKPIQueryDic[cellName];
                        CQTWYKey cqtkey = new CQTWYKey();
                        cqtkey.WYName = cqtInfo.WYName;
                        cqtkey.ChildName = cqtInfo.ChildName;
                        if (!cqtStatCellKPIInfoDic.ContainsKey(cqtkey))
                        {
                            cqtStatCellKPIInfoDic[cqtkey] = new List<CQTStatCellKPIInfo>();
                        }
                        cqtStatCellKPIInfoDic[cqtkey].Add(cqtInfo);
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }

        }

        protected override void queryChinaTelecomInThread(object o) //查询中国电信KPI统计
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                List<DataUnitAreaKPIQuery> paraPeriodList = new List<DataUnitAreaKPIQuery>();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, paraPeriodList, 3, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "正在统计电信小区指标...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, paraPeriodList, 3, period, false);
                    }
                }
                if (ctDataUnitAreaKPIQueryDic != null)
                {
                    foreach (CQTCellKPIKey cellName in ctDataUnitKPIQueryDic.Keys)
                    {
                        CQTStatCellKPIInfo cqtInfo = CQTStatCellKPIInfo.Fill(cellName);
                        cqtInfo.fileList = ctDataUnitCellFileDic[cellName];
                        cqtInfo.DataUnitAreaKPIQuery = ctDataUnitKPIQueryDic[cellName];
                        CQTWYKey cqtkey = new CQTWYKey();
                        cqtkey.WYName = cqtInfo.WYName;
                        cqtkey.ChildName = cqtInfo.ChildName;
                        if (!cqtStatCellKPIInfoDic.ContainsKey(cqtkey))
                        {
                            cqtStatCellKPIInfoDic[cqtkey] = new List<CQTStatCellKPIInfo>();
                        }
                        cqtStatCellKPIInfoDic[cqtkey].Add(cqtInfo);
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }
    }
    public class CQTStatCellKPIInfo
    {
        public string WYName { get; set; }
        public string ChildName { get; set; }
        public string StrCellName { get; set; }
        public string StrLac { get; set; }
        public string StrCi { get; set; }
        public string Str频点 { get; set; }
        public string Str扰码 { get; set; }
        public string StrCGI
        {
            get
            {
                string strCGI = "";
                int dValue = 0;
                if (int.TryParse(StrCi, out dValue))
                {
                    strCGI = string.Format("460-00-{0}-{1}", dValue / 256, dValue % 256);
                }
                return strCGI;
            }
        }
        public string StateTime { get; set; }
        public string CellCoverType { get; set; }
        public string CellAddress { get; set; }
        public DataUnitAreaKPIQuery DataUnitAreaKPIQuery { get; set; }
        public CQTStatCellKPIInfo()
        {
            StrCellName = "";
            DataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
        }
        public double CellLng { get; set; }
        public double CellLat { get; set; }
        public string Distance { get; set; }
        public string NetType { get; set; }
        public List<DTDataHeader> fileList { get; set; } = new List<DTDataHeader>();

        public string CellFileNames
        {
            get
            {
                StringBuilder fileNames = new StringBuilder();
                for (int i = 0; i < fileList.Count; i++)
                {
                    if (i > 0)
                    {
                        fileNames.Append( ",");
                    }
                    if (!fileNames.ToString().Contains(fileList[i].Name))
                    {
                        fileNames.Append(fileList[i].Name);
                    }
                }
              
                return fileNames.ToString();
            }
        }
        public static CQTStatCellKPIInfo Fill(CQTCellKPIKey cqtCellKey)
        {
            CQTStatCellKPIInfo cqt = new CQTStatCellKPIInfo();
            cqt.StateTime = cqtCellKey.StateTime;
            cqt.ChildName = cqtCellKey.ChildName;
            cqt.StrCellName = cqtCellKey.CellName;
            cqt.StrCi = cqtCellKey.CI == -99999 ? "-" : cqtCellKey.CI.ToString();
            cqt.StrLac = cqtCellKey.LAC == -99999 ? "-" : cqtCellKey.LAC.ToString();
            cqt.CellCoverType = cqtCellKey.CellCoverType;
            cqt.CellAddress = cqtCellKey.CellAddress;
            cqt.WYName = cqtCellKey.WYName;
            cqt.CellLat = cqtCellKey.CellLat;
            cqt.CellLng = cqtCellKey.CellLng;
            cqt.NetType = cqtCellKey.CellNetType;
            cqt.Str频点 = cqtCellKey.Str频点;
            cqt.Str扰码 = cqtCellKey.Str扰码;
            return cqt;
        }

        public Dictionary<CQTKPIReportColumn, CQTKPIFineStatResult> ColumnKPIFineResultDic { get; set; } = new Dictionary<CQTKPIReportColumn, CQTKPIFineStatResult>();
    }

    public class CQTCellKPIKey
    {
        public string WYName { get; set; }
        public string ChildName { get; set; }
        public string CellName { get; set; }
        public int LAC { get; set; }
        public int CI { get; set; }
        public string Str频点 { get; set; }
        public string Str扰码 { get; set; }
        public string StateTime { get; set; }
        public string CellCoverType { get; set; }
        public string CellAddress { get; set; }
        public double CellLng { get; set; }
        public double CellLat { get; set; }
        public string CellNetType { get; set; }
        public override bool Equals(object obj)
        {
            CQTCellKPIKey other = obj as CQTCellKPIKey;

            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.CellName.Equals(other.CellName)
                    && this.ChildName.Equals(other.ChildName)
                    && this.StateTime.Equals(other.StateTime)
                    && this.WYName.Equals(other.WYName));
        }
        public override int GetHashCode()
        {
            return (this.CellName + "," + this.ChildName + "," + this.WYName).GetHashCode();
        }
    }

    public class CQTWYKey
    {
        public string WYName { get; set; }
        public string ChildName { get; set; }

        public override bool Equals(object obj)
        {
            CQTWYKey other = obj as CQTWYKey;

            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.ChildName.Equals(other.ChildName)
                    && this.WYName.Equals(other.WYName));
        }
        public override int GetHashCode()
        {
            return (this.ChildName + "," + this.WYName).GetHashCode();
        }
    }
}
