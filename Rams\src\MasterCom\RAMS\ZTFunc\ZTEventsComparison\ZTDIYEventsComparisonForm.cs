﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Collections;
using BrightIdeasSoftware;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYEventsComparisonForm : MinCloseForm
    {
        public ZTDIYEventsComparisonForm()
            :base()
        {
            InitializeComponent();

            eventNowHasReduplicateEvents = MainModel.EventsResultForReduplicateEvents.eventNowHasReduplicateEvents;
            eventNowList = MainModel.EventsResultForReduplicateEvents.eventNowList;
            DiySqlGetOrderId diySqlGetOrderId = null;

            if (!MainModel.EventsResultForReduplicateEvents.ShowOrderId)
            {
                this.treeListView.Columns.Remove(olvColumnOrd_id);
            }
            else
            {
                foreach (EventNowHasReduplicateEvents evt in eventNowHasReduplicateEvents)
                {
                    diySqlGetOrderId = new DiySqlGetOrderId(MainModel, evt.dbid);
                    diySqlGetOrderId.file_id = evt.FileID;
                    diySqlGetOrderId.event_id = evt.ID;
                    diySqlGetOrderId.timevalue = evt.Time;
                    diySqlGetOrderId.Query();
                    evt.orderId = diySqlGetOrderId.order_id;
                }
            }
        }

        List<EventNowHasReduplicateEvents> eventNowHasReduplicateEvents { get; set; }
        List<EventNowHasReduplicateEvents> eventNowList { get; set; }
        bool showReduplicateEventsDetail;  //控制窗体是否显示重复问题点的信息
        List<EventNowHasReduplicateEvents> eventNowHasZeroReduplicateEvent = new List<EventNowHasReduplicateEvents>();
        public void showDuplicateEventForm()
        {
            if (MainModel.EventsResultForReduplicateEvents.ShowReduplicateEventsDetail)
                showReduplicateEventsDetail = true;
            else
                showReduplicateEventsDetail = false;

            getValidReduplicateEvt();

            dealReason();

            LoadAllEventsNow();

            LoadEvents(showReduplicateEventsDetail);
        }

        private void getValidReduplicateEvt()
        {
            if (MainModel.EventsResultForReduplicateEvents.OnlyShowInLacCiReason)
            {
                foreach (EventNowHasReduplicateEvents evtNow in eventNowHasReduplicateEvents)
                {
                    List<ReduplicateEvent> deleteReduplicateEvents = getDeleteReduplicateEvt(evtNow);
                    addZeroReduplicateEvt(evtNow, deleteReduplicateEvents);
                }

                foreach (EventNowHasReduplicateEvents evtNowZero in eventNowHasZeroReduplicateEvent)
                {
                    eventNowHasReduplicateEvents.Remove(evtNowZero);
                }
            }
        }

        private List<ReduplicateEvent> getDeleteReduplicateEvt(EventNowHasReduplicateEvents evtNow)
        {
            List<ReduplicateEvent> deleteReduplicateEvents = new List<ReduplicateEvent>();
            foreach (ReduplicateEvent evtOld in evtNow.reduplicateEvents)
            {
                if (!evtOld.reduplicateReason.Contains("距离在") || !evtOld.reduplicateReason.Contains("LAC,CI相同"))
                {
                    deleteReduplicateEvents.Add(evtOld);
                }
            }

            return deleteReduplicateEvents;
        }

        private void addZeroReduplicateEvt(EventNowHasReduplicateEvents evtNow, List<ReduplicateEvent> deleteReduplicateEvents)
        {
            foreach (ReduplicateEvent e in deleteReduplicateEvents)
            {
                evtNow.reduplicateEvents.Remove(e);
                if (evtNow.reduplicateEvents.Count == 0) //若重复事件通过只显示临界距离内并且LAC,CI相同的条件的筛选后，数目为0个，则去掉
                {
                    eventNowHasZeroReduplicateEvent.Add(evtNow);
                }
            }
        }

        private void dealReason()
        {
            if (!MainModel.EventsResultForReduplicateEvents.ShowInLacCiReason)
            {
                foreach (EventNowHasReduplicateEvents evtNow in eventNowHasReduplicateEvents)
                {
                    foreach (ReduplicateEvent evtOld in evtNow.reduplicateEvents)
                    {
                        setReason(evtOld);
                    }
                }
            }
        }

        private void setReason(ReduplicateEvent evtOld)
        {
            if (evtOld.reduplicateReason.Contains("距离在"))
            {
                int index = evtOld.reduplicateReason.IndexOf("内");
                if (index + 1 < evtOld.reduplicateReason.Length)
                {
                    string reason = evtOld.reduplicateReason.Remove(index + 1);
                    evtOld.reduplicateReason = reason;
                }
            }
        }

        private void LoadEvents(bool showReduplicateEventsDetail)
        {

            EventsResultForReduplicateEvents eventsResultForReduplicateEvents = MainModel.EventsResultForReduplicateEvents;
            if (eventsResultForReduplicateEvents == null)
            {
                return;
            }

            this.olvColumnCity.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.City;
                }
                else if (row is ReduplicateEvent)
                {
                    ReduplicateEvent eventOld = row as ReduplicateEvent;
                    return eventOld.City;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnReduplicateEventsCount.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.reduplicateEvents.Count;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnFileName.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.FileName;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnFIleId.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.FileID;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnProj.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.projDesc;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnRoadName.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.RoadName;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnDate.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.date;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnLongitude.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.Longitude;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnLatitude.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.Latitude;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnLAC.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.lac;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnCI.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.ci;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnEventName.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.EventName;
                }
                else
                {
                    return "";
                }
            };

            this.olvColumnEventId.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.ID;
                }
                else
                {
                    return "";
                }
            };

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateFileName.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.FileName;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateFileId.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.FileID;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateProj.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.projDesc;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateRoadName.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.RoadName;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateDate.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.reduplicateDate;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateLongitude.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.Longitude;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateLatitude.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.Latitude;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateLAC.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.reduplicateLac;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateCI.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.reduplicateCi;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateEventName.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.reduplicateEventName;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateEventId.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.ID;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.olvColumnReduplicateReason.AspectGetter = delegate(object row)
                {
                    if (row is ReduplicateEvent)
                    {
                        ReduplicateEvent eventOld = row as ReduplicateEvent;
                        return eventOld.reduplicateReason;
                    }
                    else
                    {
                        return "";
                    }
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.treeListView.CanExpandGetter = delegate(object x)
                {
                    return x is EventNowHasReduplicateEvents;
                };
            }

            if (showReduplicateEventsDetail)
            {
                this.treeListView.ChildrenGetter = delegate(object x)
                {
                    if (x is EventNowHasReduplicateEvents)
                    {
                        EventNowHasReduplicateEvents evt = x as EventNowHasReduplicateEvents;
                        return evt.reduplicateEvents;
                    }
                    else
                    {
                        return new ArrayList();
                    }
                }; 
            }


            this.treeListView.ClearObjects();
            this.treeListView.SetObjects(this.eventNowHasReduplicateEvents);        //加载重复事件点信息
            if (!showReduplicateEventsDetail) //不显示重复问题点信息，只显示重复问题点的个数
            {
                this.treeListView.Columns.Remove(olvColumnReduplicateReason);
                this.treeListView.Columns.Remove(olvColumnReduplicateFileName);
                this.treeListView.Columns.Remove(olvColumnReduplicateDate);
                this.treeListView.Columns.Remove(olvColumnReduplicateRoadName);
                this.treeListView.Columns.Remove(olvColumnReduplicateLongitude);
                this.treeListView.Columns.Remove(olvColumnReduplicateLatitude);
                this.treeListView.Columns.Remove(olvColumnReduplicateLAC);
                this.treeListView.Columns.Remove(olvColumnReduplicateCI);
                this.treeListView.Columns.Remove(olvColumnReduplicateEventName);
            }

            this.textBoxEventsNowCount.Text = MainModel.EventsResultForReduplicateEvents.eventNowHasReduplicateEvents.Count.ToString();

            double EventNowPct = (double)(MainModel.EventsResultForReduplicateEvents.eventNowHasReduplicateEvents.Count)
                / (MainModel.EventsResultForReduplicateEvents.eventNowList.Count);

            this.textBoxEventNowAllCount.Text = MainModel.EventsResultForReduplicateEvents.eventNowList.Count.ToString();

            if (EventNowPct != 0)
                this.textBoxEventNowHasProblemPct.Text = EventNowPct.ToString("P");
        }

        private void LoadAllEventsNow()
        {
            if (eventNowList == null)
            {
                return;
            }

            this.olvCityName.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.City;
                }
                return "";
            };

            this.olvFileName.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.FileName;
                }
                return "";
            };

            this.olvRoadName.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.RoadName;
                }
                return "";
            };

            this.olvTime.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.date;
                }
                return "";
            };

            this.olvLongitude.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.Longitude;
                }
                return "";
            };

            this.olvLatitude.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.Latitude;
                }
                return "";
            };

            this.olvLac.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.lac;
                }
                return "";
            };

            this.olvCi.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.ci;
                }
                return "";
            };

            this.olvEventName.AspectGetter = delegate(object row)
            {
                if (row is EventNowHasReduplicateEvents)
                {
                    EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                    return eventNow.EventName;
                }
                return "";
            };

            if (MainModel.EventsResultForReduplicateEvents.ShowOrderId)
            {
                this.olvColumnOrd_id.AspectGetter = delegate(object row)
                {
                    if (row is EventNowHasReduplicateEvents)
                    {
                        EventNowHasReduplicateEvents eventNow = row as EventNowHasReduplicateEvents;
                        return eventNow.orderId;
                    }
                    return "";
                };
            }

            this.treeListViewAllEventsNow.ClearObjects();
            this.treeListViewAllEventsNow.SetObjects(this.eventNowList); //加载所有当前事件点信息
        }

        public class EventsResultForReduplicateEvents
        {
            public List<EventNowHasReduplicateEvents> eventNowHasReduplicateEvents { get; set; } = new List<EventNowHasReduplicateEvents>();
            public bool ShowReduplicateEventsDetail { get; set; }
            public bool OnlyShowInLacCiReason { get; set; }
            public bool ShowInLacCiReason { get; set; }
            public bool ShowOrderId { get; set; }
            public List<EventNowHasReduplicateEvents> eventNowList { get; set; } = new List<EventNowHasReduplicateEvents>();
        }

        public class EventNowHasReduplicateEvents : Event
        {
            public string City { get; set; }
            public DateTime date { get; set; }
            public int lac { get; set; }
            public int ci { get; set; }
            public string EventName { get; set; }
            public string RoadName { get; set; }
            public string orderId { get; set; }
            public string projDesc { get; set; }
            public int dbid { get; set; }
            public List<ReduplicateEvent> reduplicateEvents { get; set; } = new List<ReduplicateEvent>();
        }

        public class ReduplicateEvent : Event
        {
            public string City { get; set; }
            public string reduplicateReason { get; set; }
            public DateTime reduplicateDate { get; set; }
            public int reduplicateLac { get; set; }
            public int reduplicateCi { get; set; }
            public string RoadName { get; set; }
            public string projDesc { get; set; }
            public string reduplicateEventName { get; set; }
        }

        public class DiySqlGetOrderId : DIYSQLBase
        {
            public int file_id { get; set; }
            public int event_id { get; set; }
            public int timevalue { get; set; }

            public string order_id { get; set; }

            public DiySqlGetOrderId(MainModel mainModel,int dbid)
                : base(mainModel,dbid)
            {
            }
            protected override string getSqlTextString()
            {
                return "exec sp_gd_problem_block_orderseq_qry " + file_id + "," + event_id + "," + timevalue;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[1];
                rType[0] = E_VType.E_String;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        order_id = package.Content.GetParamString();
                        //do your code here
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            public override string Name
            {
                get { return "DIYSqlGetOrderId"; }
            }
        };

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            treeListView.ExpandAll();
            MasterCom.RAMS.Util.ExcelOperator.ExportObjectListViewExcel((ObjectListView)treeListView, true);
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            this.treeListView.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            this.treeListView.CollapseAll();
        }

        private void miAllEventsNowExportExcel_Click(object sender, EventArgs e)
        {
            MasterCom.RAMS.Util.ExcelOperator.ExportObjectListViewExcel((ObjectListView)treeListViewAllEventsNow, true);
        }
    }
}
