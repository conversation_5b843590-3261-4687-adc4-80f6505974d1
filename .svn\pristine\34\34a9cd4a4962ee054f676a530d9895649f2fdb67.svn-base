﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.CQT;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTCsfbCallStat;
using MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause;
using MasterCom.Util;
using NPOI.HSSF.Record;
using Message = MasterCom.RAMS.Model.Message;

namespace MasterCom.RAMS.ZTFunc
{
    public class CsfbCallStatQuery : DIYAnalyseByFileBackgroundBase
    {
        protected List<int> MoCallAttemptEvtIdList = new List<int> { 877, 1021, 1041 };
        protected List<int> MtCallAttemptEvtIdList = new List<int> { 885, 1022, 1042 };

        protected static readonly object lockObj = new object();
        private static CsfbCallStatQuery instance = null;
        public static CsfbCallStatQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new CsfbCallStatQuery();
                    }
                }
            }
            return instance;
        }

        protected CsfbCallStatQuery()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            this.Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_gsm_SC_LAC");
            Columns.Add("lte_gsm_SC_CI");
            Columns.Add("lte_td_SC_LAC");
            Columns.Add("lte_td_SC_CellID");
            Columns.Add("lte_gsm_DM_RxLevBCCH");
            Columns.Add("lte_gsm_DM_RxLevSub");
            Columns.Add("lte_td_DM_PCCPCH_RSCP");
        }


        public override string Name
        {
            get
            {
                return "CSFB呼叫时延统计(区域文件)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22046, this.Name);
        }

        protected bool checkDelay = true;
        protected int maxDelaySec = 180;

        protected override bool getCondition()
        {
            CallConditionDlg dlg = new CallConditionDlg();
            dlg.SetCondition(checkDelay, maxDelaySec);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out checkDelay, out maxDelaySec);
            callStatList = new List<SingleCallStatInfo>();
            return callStatList != null;
        }

        protected override void fireShowForm()
        {
            if (callStatList == null || callStatList.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }
            CsfbCallStatListForm frm = MainModel.GetObjectFromBlackboard(typeof(CsfbCallStatListForm)) as CsfbCallStatListForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new CsfbCallStatListForm();
            }
            frm.FillData(callStatList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            callStatList = null;
        }

        /// <summary>
        /// 先进行主被叫关联
        /// </summary>
        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = getMoMtPair();

            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }
                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private Dictionary<FileInfo, FileInfo> getMoMtPair()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            Dictionary<FileInfo, bool> fileAdded = new Dictionary<FileInfo, bool>();
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                   && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                if (fileInfo.Momt == (int)MoMtFile.MoFlag)
                {//主叫关联被叫
                    FileInfo mtFile = MainModel.FileInfos.Find(delegate (FileInfo x)
                    {
                        return x.ID == fileInfo.EventCount;
                    });
                    moMtPair[fileInfo] = mtFile;
                    if (mtFile != null)
                    {
                        fileAdded[mtFile] = true;
                    }
                }
            }

            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (fileInfo.Momt == (int)MoMtFile.MtFlag && !fileAdded.ContainsKey(fileInfo))
                {
                    moMtPair[fileInfo] = null;
                }
            }

            return moMtPair;
        }

        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    moFile = file;
                }
                else
                {
                    mtFile = file;
                }
            }

            int lastMoTpIdx = 0;
            int lastMoMsgIdx = 0;
            int lastMtEventIdx = 0;
            int lastMtTpIdx = 0;
            int lastMtMsgIdx = 0;
            if (moFile != null)
            {
                dealMoFile(moFile, mtFile, ref lastMoTpIdx, ref lastMoMsgIdx, ref lastMtEventIdx, ref lastMtTpIdx, ref lastMtMsgIdx);
            }
            else if (mtFile != null)
            {
                dealMtFile(mtFile, ref lastMtTpIdx, ref lastMtMsgIdx);
            }
        }

        private void dealMoFile(DTFileDataManager moFile, DTFileDataManager mtFile, ref int lastMoTpIdx, ref int lastMoMsgIdx, 
            ref int lastMtEventIdx, ref int lastMtTpIdx, ref int lastMtMsgIdx)
        {
            try
            {
                CallInfo singleCall = null;
                for (int i = 0; i < moFile.Events.Count; i++)
                {
                    Event evt = moFile.Events[i];
                    if (MoCallAttemptEvtIdList.Contains(evt.ID))
                    {
                        /*MO CSFB request
                        *GSM MO Call Attempt
                        *TD MO Call Attempt
                        *一次呼叫开始信息
                        */
                        singleCall = new CallInfo(moFile.FileName);
                        singleCall.AddEvent(evt);
                    }
                    else if (singleCall != null && singleCall.AddEvent(evt))
                    {//保存singlecall的事件

                        addTpMsgInfo2Call(singleCall, moFile, ref lastMoTpIdx, ref lastMoMsgIdx);
                        CallInfo mtCall = getMtCallInfo(singleCall, mtFile, ref lastMtEventIdx, ref lastMtTpIdx, ref lastMtMsgIdx);
                        saveCallInfo(singleCall, mtCall);
                        singleCall = null;//清空
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
        }

        private void dealMtFile(DTFileDataManager mtFile, ref int lastMtTpIdx, ref int lastMtMsgIdx)
        {
            CallInfo singleCall = null;
            for (int i = 0; i < mtFile.Events.Count; i++)
            {
                Event evt = mtFile.Events[i];
                if (MtCallAttemptEvtIdList.Contains(evt.ID))
                {//MT CSFB request
                 //gsm mt call attempt
                 //td mt call attempt
                    singleCall = new CallInfo(mtFile.FileName);
                    singleCall.AddEvent(evt);
                }
                else if (singleCall != null && singleCall.AddEvent(evt))
                {//保存singlecall的事件
                    addTpMsgInfo2Call(singleCall, mtFile, ref lastMtTpIdx, ref lastMtMsgIdx);
                    saveCallInfo(null, singleCall);
                    singleCall = null;//清空
                }
            }
        }

        protected List<SingleCallStatInfo> callStatList = null;
        private void saveCallInfo(CallInfo moCall, CallInfo mtCall)
        {
            SingleCallStatInfo info = new SingleCallStatInfo(moCall, mtCall);
            info.Sn = callStatList.Count + 1;
            callStatList.Add(info);
        }

        private void addTpMsgInfo2Call(CallInfo call, DTFileDataManager file
            , ref int lastTpIdx, ref int lastMsgIdx)
        {
            getCellInfoBeforeCSFB(call, file, ref lastTpIdx);

            bool began = false;
            Event beginEvent = call.Events[0];
            Event endEvent = call.Events[call.Events.Count - 1];
            int beginIdx = lastMsgIdx;
            for (; beginIdx >= 0; beginIdx++)
            {
                Message msg = file.Messages[beginIdx];
                if ((beginEvent.DateTime - msg.DateTime).TotalSeconds <= 10)
                {//取call attempt前10秒内的信令
                    break;
                }
            }
            if (beginIdx < 0)
            {
                beginIdx = 0;
            }

            for (int i = beginIdx; i < file.Messages.Count; i++)
            {
                Message msg = file.Messages[i];
                if (msg.SN > endEvent.SN)
                {
                    lastMsgIdx = i;
                    break;
                }
                if (msg.DateTime >= beginEvent.DateTime)
                {
                    began = true;
                }
                began = setBeganMsg(call, began, msg);
            }

            setCallInfo(call);
        }

        private bool setBeganMsg(CallInfo call, bool began, Message msg)
        {
            if (msg.ID == 0x416b074c)
            {//Extended service request
             //mo
                began = true;
                call.MsgExtendedServicerequest = msg;
            }
            else if (msg.ID == 0x412F6600)
            {//LTE RRC Paging
             //mt
                began = true;
                call.MsgMtPaging = msg;
                call.MsgMtPagingList.Add(msg);
            }
            else if (msg.ID == 1097533284)
            {//CS service notification
             //mt
                began = true;
                call.MsgServiceNotification = msg;
            }
            else if (msg.ID == 1575 || msg.ID == 1093599234)
            {// gsm mt call attempt       td mt call attempt
                began = true;
                call.MsgMtPagingResponse = msg;
            }
            else
            {
                setMsg(call, began, msg);
            }

            return began;
        }

        private void setMsg(CallInfo call, bool began, Message msg)
        {
            if (began)
            {
                if (call.MsgFirstSysInfo == null && call.EvtRelease != null
                    && msg.SN > call.EvtRelease.SN
                    && ((msg.ID >= 1536 && msg.ID <= 1542)
                        || (msg.ID >= 1560 && msg.ID <= 1567)
                        || (msg.ID >= 0x412F0401 && msg.ID <= 0x412F041E)
                        || msg.ID == 0x412F0420 || msg.ID == 0x412F0421))
                {
                    //gsm td sys info
                    call.MsgFirstSysInfo = msg;
                }
                else
                {
                    setCallInfoMsg(call, msg);
                }
            }
        }

        private void setCallInfoMsg(CallInfo call, Message msg)
        {
            switch (msg.ID)
            {
                case 769:
                case 1899627265:
                    call.MsgAlerting = msg;
                    break;
                case (int)MessageManager.LTE_RRC_RRC_Connection_Release:
                    call.MsgRrcConnectionRelease = msg;
                    break;
                case 1316:
                case 1899627812:
                    call.MsgCmServiceRequest = msg;
                    break;
                case 1313:
                case 1899627809:
                    call.MsgCmServiceAccept = msg;
                    break;
                case 773:
                case 1899627269:
                    call.MsgSetup = msg;
                    break;
                case 770:
                case 1899627266:
                    call.MsgCallProceeding = msg;
                    break;
                case 1582:
                case 1093600015:
                    call.MsgRrAssignmentCommand = msg;
                    break;
                case 1577:
                case 1093599504:
                    call.MsgRrAssignmentComplete = msg;
                    break;
                case 1093625344:
                    call.MsgMtPaging = msg;
                    call.MsgMtPagingList.Add(msg);
                    break;
                case 1097533284:
                    call.MsgServiceNotification = msg;
                    break;
                case 776:
                    call.MsgMtCallConfirmed = msg;
                    break;
                case 1552:
                    call.MsgChannelModeModify = msg;
                    break;
                case 1559:
                    call.MsgChannelModeModifyAcknowledge = msg;
                    break;
                case 1298:
                case 1899627794:
                case 1097533266:
                    call.MsgAuthenticationRequest = msg;
                    call.MsgMtAuthenticationRequest = msg;
                    if (call.MsgCmServiceRequest == null)
                    {
                        call.MsgAuthenticationRequest = null;
                    }
                    if (call.MsgMtPagingResponse == null)
                    {
                        call.MsgMtAuthenticationRequest = null;
                    }
                    break;
                case 1300:
                case 1899627796:
                case 1097533267:
                    call.MsgAuthenticationResponse = msg;
                    call.MsgMtAuthenticationResponse = msg;
                    if (call.MsgAuthenticationRequest == null)
                    {
                        call.MsgAuthenticationResponse = null;
                    }
                    if (call.MsgMtAuthenticationRequest == null)
                    {
                        call.MsgMtAuthenticationResponse = null;
                    }
                    break;
                default:
                    break;
            }
        }

        private void setCallInfo(CallInfo call)
        {
            if (call.MsgServiceNotification == null && call.MsgMtPaging != null && call.MsgMtPagingList.Count > 0 && call.MsgExtendedServicerequest != null)
            {
                uint[] pagTmsiList = VolteStatDelayAnaByRegion.getPagingTmsiList(call.MsgMtPaging);
                uint extendedTmsi = MessageDecodeHelper.GetMsgSingleUInt(call.MsgExtendedServicerequest, "gsm_a.tmsi");
                bool isValuePaging = false;
                foreach (uint tmsi in pagTmsiList)
                {
                    if (tmsi == extendedTmsi)
                    {
                        isValuePaging = true;
                        break;
                    }
                }
                if (!isValuePaging)
                {
                    setMsgMtPaging(call, extendedTmsi);
                }
            }
        }

        private void setMsgMtPaging(CallInfo call, uint extendedTmsi)
        {
            call.MsgMtPaging = null;
            call.MsgMtPagingList.Remove(call.MsgMtPaging);
            foreach (Message msg in call.MsgMtPagingList)
            {
                uint[] pagTmsiList = VolteStatDelayAnaByRegion.getPagingTmsiList(msg);
                foreach (uint tmsi in pagTmsiList)
                {
                    if (tmsi == extendedTmsi)
                    {
                        call.MsgMtPaging = msg;
                        break;
                    }
                }
            }
        }

        protected virtual void getCellInfoBeforeCSFB(CallInfo call, DTFileDataManager file, ref int lastTpIdx)
        {
            for (int i = lastTpIdx; i < file.TestPoints.Count; i++)
            {
                TestPoint tp = file.TestPoints[i];
                searchCsfbPrevInfo(call, file, i, tp);

                bool isAttempt = searchCsfbAttemptInfo(call, i, tp, ref lastTpIdx);
                if (isAttempt)
                {
                    break;
                }
            }
        }

        private void searchCsfbPrevInfo(CallInfo call, DTFileDataManager file, int i, TestPoint tp)
        {
            if (call.EvtCsfbCallRequest != null
                && (call.Rsrp == null || call.Tac == null || call.Eci == null)
                && tp.SN > call.EvtCsfbCallRequest.SN)
            {//回落情况
                setCsfbPrevInfo(call, file, i);
            }
        }

        private void setCsfbPrevInfo(CallInfo call, DTFileDataManager file, int i)
        {
            for (int preIdx = i - 1; preIdx >= 0; preIdx--)
            {//往前找
                TestPoint prePoint = file.TestPoints[preIdx];
                if (call.Rsrp == null)
                {
                    float? rsrp = (float?)prePoint["lte_RSRP"];
                    if (rsrp != null && rsrp >= -141 && rsrp <= 25)
                    {
                        call.Rsrp = rsrp;
                    }
                }
                int? tac = (int?)(ushort?)prePoint["lte_TAC"];
                int? eci = (int?)prePoint["lte_ECI"];
                if (tac != null || eci != null)
                {
                    call.Tac = tac;
                    call.Eci = eci;
                    break;
                }
            }
        }

        private bool searchCsfbAttemptInfo(CallInfo call, int i, TestPoint tp, ref int lastTpIdx)
        {
            if (call.EvtCallAttempt != null
                && (call.RxLev_Rscp == null || call.Lac == null || call.Ci == null)
                && tp.SN > call.EvtCallAttempt.SN)
            {
                 return setCsfbAttemptInfo(call, i, tp, ref lastTpIdx);
            }
            return false;
        }

        private bool setCsfbAttemptInfo(CallInfo call, int i, TestPoint tp, ref int lastTpIdx)
        {
            int? lev = (int?)(short?)tp["lte_gsm_DM_RxLevSub"];
            int? lac = (int?)tp["lte_gsm_SC_LAC"];
            int? ci = (int?)tp["lte_gsm_SC_CI"];
            if (lev != null && lev >= -120 && lev <= -10)
            {
                call.RxLev_Rscp = lev;
                call.CallNetType = "GSM";
                call.Lac = lac;
                call.Ci = ci;
                if (call.Lac != null || call.Ci != null)
                {
                    lastTpIdx = i;
                    return true;
                }
            }
            else
            {
                lev = (int?)tp["lte_td_DM_PCCPCH_RSCP"];
                lac = (int?)tp["lte_td_SC_LAC"];
                ci = (int?)tp["lte_td_SC_CellID"];
                if (lev != null && lev >= -120 && lev <= -10)
                {
                    call.RxLev_Rscp = lev;
                    call.CallNetType = "TD";
                    call.Lac = lac;
                    call.Ci = ci;
                }
                if (call.Lac != null || call.Ci != null)
                {
                    lastTpIdx = i;
                    return true;
                }
            }
            return false;
        }

        private CallInfo getMtCallInfo(CallInfo moCall, DTFileDataManager mtFile
            , ref int lastMtEentIdx, ref int lastMtTpIdx, ref int lastMtMsgIdx)
        {
            if (mtFile == null)
            {
                return null;
            }
            CallInfo mtCall = null;
            if (moCall != null && moCall.BackResult == "Fail")
            {
                return null;
            }
            if (lastMtEentIdx < 0)
            {
                lastMtEentIdx = 0;
            }
            for (int i = lastMtEentIdx; i < mtFile.Events.Count; i++)
            {
                bool isAdded = addCallInfo(moCall, mtFile, ref lastMtEentIdx, ref lastMtTpIdx, ref lastMtMsgIdx, ref mtCall, i);
                if (isAdded)
                {
                    break;
                }
            }
            return mtCall;
        }

        private bool addCallInfo(CallInfo moCall, DTFileDataManager mtFile, ref int lastMtEentIdx,
            ref int lastMtTpIdx, ref int lastMtMsgIdx, ref CallInfo mtCall, int i)
        {
            Event evt = mtFile.Events[i];
            if (moCall == null
                || evt.DateTime >= moCall.EvtCallAttempt.DateTime)
            {//mt call attempt事件只会在mo call attempt 后发生
                if (MtCallAttemptEvtIdList.Contains(evt.ID))
                {//MT CSFB request
                 //gsm mt call attempt
                 //td mt call attempt
                    if (mtCall != null)
                    {
                        lastMtEentIdx = i - 2;
                        addTpMsgInfo2Call(mtCall, mtFile, ref lastMtTpIdx, ref lastMtMsgIdx);
                        return true;
                    }
                    mtCall = new CallInfo(mtFile.FileName);
                    mtCall.AddEvent(evt);
                }
                else if (mtCall != null)
                {
                    bool isAdded = mtCall.AddEvent(evt);
                    if (isAdded)
                    {
                        lastMtEentIdx = i - 2;
                        addTpMsgInfo2Call(mtCall, mtFile, ref lastMtTpIdx, ref lastMtMsgIdx);
                        return true;
                    }
                }
            }

            if (moCall != null
                && checkDelay
                && (evt.DateTime - moCall.Events[moCall.Events.Count - 1].DateTime).TotalSeconds > maxDelaySec)
            {
                if (mtCall != null)
                {
                    lastMtEentIdx = i - 2;
                    addTpMsgInfo2Call(mtCall, mtFile, ref lastMtTpIdx, ref lastMtMsgIdx);
                }
                return true;
            }
            return false;
        }
    }
}
