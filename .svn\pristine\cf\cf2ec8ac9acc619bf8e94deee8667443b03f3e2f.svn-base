﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.ZTFunc.ZTTestPointOverMuchGrid;

namespace MasterCom.RAMS.ZTFunc
{
    public class TestPointOverMuchGridQuery : QueryKPIStatBase
    {
        private Dictionary<string, KPIStatDataBase> fileGridDataDic;
        private List<TestPointOverMuchFileInfo> fileInfoList;

        public TestPointOverMuchGridQuery(MainModel mm)
        {
        }

        public override string Name
        {
            get { return "停车测试"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18026, this.Name);
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.grid;
        }

        protected override void fireShowResult()
        {
            TestPointOverMuchResultForm form = MainModel.CreateResultForm(typeof(TestPointOverMuchResultForm)) as TestPointOverMuchResultForm;
            form.FillData(fileInfoList);
            form.Visible = true;
            form.BringToFront();
            form.ShowAll();
        }

        protected override bool isValidCondition()
        {
            if (condition.FileInfos == null || condition.FileInfos.Count == 0)
            {
                return false;
            }
            DateTime bTime = DateTime.MaxValue;
            DateTime eTime = DateTime.MinValue;
            StringBuilder sb = new StringBuilder();
            foreach (FileInfo fi in condition.FileInfos)
            {
                DateTime fBTime = JavaDate.GetDateTimeFromMilliseconds(fi.BeginTime * 1000L);
                DateTime fETime = JavaDate.GetDateTimeFromMilliseconds(fi.EndTime * 1000L);
                if (fBTime < bTime)
                {
                    bTime = fBTime;
                }
                if (fETime > eTime)
                {
                    eTime = fETime;
                }
                if (!condition.CarrierTypes.Contains(fi.CarrierType))
                {
                    condition.CarrierTypes.Add(fi.CarrierType);
                }
                if (!condition.ServiceTypes.Contains(fi.ServiceType))
                {
                    condition.ServiceTypes.Add(fi.ServiceType);
                }
                if (!condition.Projects.Contains(fi.ProjectID))
                {
                    condition.Projects.Add(fi.ProjectID);
                }
                sb.Append(fi.ID);
                sb.Append(",");
            }
            if (sb.Length > 0)
            {
                sb = sb.Remove(sb.Length - 1, 1);
            }
            TimePeriod p = new TimePeriod(bTime, eTime);
            condition.Periods.Add(p);
            condition.FileName = sb.ToString();
            condition.NameFilterType = FileFilterType.ByMark_ID;
            return true;
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            Dictionary<int, TestPointOverMuchFileInfo> idInfoDic = new Dictionary<int, TestPointOverMuchFileInfo>();
            foreach (string key in fileGridDataDic.Keys)
            {
                string[] tmpStrs = key.Split('_');
                int fileId = Convert.ToInt32(tmpStrs[0]);
                KPIStatDataBase kpiData = fileGridDataDic[key];

                TestPointOverMuchGridInfo gridInfo = new TestPointOverMuchGridInfo(kpiData);
                if (!idInfoDic.ContainsKey(fileId))
                {
                    idInfoDic.Add(fileId, new TestPointOverMuchFileInfo(fileId));
                }
                idInfoDic[fileId].AddGridInfo(gridInfo);
            }
            fileInfoList.AddRange(idInfoDic.Values);
        }

        protected override bool getConditionBeforeQuery()
        {
            fileGridDataDic = new Dictionary<string, KPIStatDataBase>();
            fileInfoList = new List<TestPointOverMuchFileInfo>();
            return true;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            formulaSet.Add("5A010201");
            formulaSet.Add("61210301");
            formulaSet.Add("612D0301");
            formulaSet.Add("5C04030A");
            formulaSet.Add(StatDataSCAN_GSM.RxlevSampleCountKey);
            formulaSet.Add(StatDataSCAN_LTE.PSSRPSampleNumKey);
            formulaSet.Add(StatDataSCAN_TD.PCCPCHRSCPSampleNumKey);
            formulaSet.Add(StatDataSCAN_WCDMA.RSCPSampleNumKey);
            formulaSet.Add(StatDataNR.RSRPSampleNumKey);
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        protected override void queryPeriodInfo(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            isQueringEvent = false;
            preparePackageBasicContent(clientProxy.Package, period);
            preparePackageNeededInfo_StatImg(clientProxy.Package, reservedParams[0]);
            clientProxy.Send();
            recieveInfo_ImgGrid(clientProxy);
            afterRecieveOnePeriodData();
        }

        protected override void AddGeographicFilter(Package package)
        {
            //
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                KPIStatDataBase singleStatData = null;
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        fileInfo.DistrictID = clientProxy.DbID;
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(curImgColumnDef, package, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    System.Diagnostics.Debug.Assert(false, package.Content.Type.ToString());
                    break;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

        private void fillData(List<StatImgDefItem> curImgColumnDef, Package package, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            fillStatData(package, curImgColumnDef, singleStatData);

            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            GridUnitBase grid = new GridUnitBase(lng, lat);
            string key = string.Format("{0}_{1}_{2}", fileID, grid.RowIdx, grid.ColIdx);
            if (fileGridDataDic.ContainsKey(key))
            {
                fileGridDataDic[key].GatherStatData(singleStatData);
            }
            else
            {
                fileGridDataDic[key] = singleStatData;
            }
        }
    }

    public class TestPointOverMuchGridInfo : GridUnitBase
    {
        public int SerialNm { set; get; }

        public int TestPointCount
        {
            get;
            private set;
        }

        public double Duration_Sec
        {
            get;
            private set;
        }

        public double Distance
        {
            get;
            private set;
        }

        public TestPointOverMuchGridInfo(KPIStatDataBase statData)
        {
            string key = GetPointCountKey(statData);
            double count = statData[key, -1];
            if (!double.IsNaN(count))
            {
                TestPointCount = (int)count;
            }
            setDistanceAndDuration(statData);
             LTLng = statData.LTLng;
            LTLat = statData.LTLat;
        }

        private void setDistanceAndDuration(KPIStatDataBase statData)
        {
            if (statData is StatDataNR)
            {
                double count = statData[NRDURATION_KEY, -1];
                if (!double.IsNaN(count))
                {
                    Duration_Sec = count / 1000;
                }
                count = statData[NRDISTANCE_KEY, -1];
                if (!double.IsNaN(count))
                {
                    Distance = count;
                }
            }
            else
            {
                double count = statData[DURATION_KEY, -1];
                if (!double.IsNaN(count))
                {
                    Duration_Sec = count / 1000;
                }
                count = statData[DISTANCE_KEY, -1];
                if (!double.IsNaN(count))
                {
                    Distance = count;
                }
            }
        }

        private string GetPointCountKey(KPIStatDataBase statData)
        {
            if (statData is StatDataGSM)
            {
                return "5A010201";
            }
            else if (statData is StatDataLTE)
            {
                return "61210301";
            }
            else if (statData is StatDataLTE_FDD)
            {
                return "612D0301";
            }
            else if (statData is StatDataTD)
            {
                return "5C04030A";
            }
            else if (statData is StatDataWCDMA)
            {
                return null;
            }
            else if (statData is StatDataCDMA_Voice)
            {
                return null;
            }
            else if (statData is StatDataCDMA_EVDO)
            {
                return null;
            }
            else if (statData is StatDataSCAN_GSM)
            {
                return StatDataSCAN_GSM.RxlevSampleCountKey;
            }
            else if (statData is StatDataSCAN_LTE)
            {
                return StatDataSCAN_LTE.PSSRPSampleNumKey;
            }
            else if (statData is StatDataSCAN_TD)
            {
                return StatDataSCAN_TD.PCCPCHRSCPSampleNumKey;
            }
            else if (statData is StatDataSCAN_WCDMA)
            {
                return StatDataSCAN_WCDMA.RSCPSampleNumKey;
            }
            else if (statData is StatDataSCAN_CDMA)
            {
                return null;
            }
            else if (statData is StatDataGSM_MTR)
            {
                return null;
            }
            else if (statData is StatDataNR)
            {
                return StatDataNR.RSRPSampleNumKey;
            }
            return null;
        }

        private const string NRDURATION_KEY = "80040005";
        private const string NRDISTANCE_KEY = "80040006";
        private const string DURATION_KEY = "0805";
        private const string DISTANCE_KEY = "0806";
    }

    public class TestPointOverMuchFileInfo
    {
        public int FileID
        {
            get;
            private set;
        }

        public string FileName
        {
            get { return fileInfo == null ? "" : fileInfo.Name; }
        }

        public string ServiceTypeDescription
        {
            get { return fileInfo == null ? "" : fileInfo.ServiceTypeDescription; }
        }

        public string BeginTimeString
        {
            get { return fileInfo == null ? "" : fileInfo.BeginTimeString; }
        }

        public string EndTimeString
        {
            get { return fileInfo == null ? "" : fileInfo.EndTimeString; }
        }

        public int TotalGridCount
        {
            get { return totalGridList.Count; }
        }

        public int FilterGridCount
        {
            get { return filterGridList.Count; }
        }

        public double GridRate
        {
            get { return totalGridList.Count == 0 ? 0 : 1.0 * filterGridList.Count / totalGridList.Count; }
        }

        public ReadOnlyCollection<TestPointOverMuchGridInfo> GridList
        {
            get { return totalGridList.AsReadOnly(); }
        }

        public ReadOnlyCollection<TestPointOverMuchGridInfo> FilterGridList
        {
            get { return filterGridList.AsReadOnly(); }
        }

        public TestPointOverMuchFileInfo(int fileId)
        {
            FileID = fileId;
            fileInfo = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileId);
            totalGridList = new List<TestPointOverMuchGridInfo>();
            filterGridList = new List<TestPointOverMuchGridInfo>();
        }

        public void AddGridInfo(TestPointOverMuchGridInfo gridInfo)
        {
            totalGridList.Add(gridInfo);
        }

        public void SetFilter(int minimunPoint, int durationMin,double distanceMin)
        {
            filterGridList.Clear();
            foreach (TestPointOverMuchGridInfo grid in totalGridList)
            {
                if (grid.TestPointCount >= minimunPoint
                    && grid.Duration_Sec >= durationMin
                    && grid.Distance >= distanceMin)
                {
                    filterGridList.Add(grid);
                }
            }
        }

        private readonly List<TestPointOverMuchGridInfo> totalGridList;
        private readonly List<TestPointOverMuchGridInfo> filterGridList;
        private readonly FileInfo fileInfo;
        public FileInfo FileInfo
        { get { return fileInfo; } }
    }
}
