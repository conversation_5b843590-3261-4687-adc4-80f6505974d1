﻿using System;
using System.Collections.Generic;
using System.Text;
using DevExpress.XtraEditors;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRegionAreaCalculator: QueryBase
    {
        public ZTRegionAreaCalculator(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "计算预存区域面积"; }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        //protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        //{
        //    return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19022, "GSM仿真渲染");
        //}

        Dictionary<string, double> regionAreaDic = null;
        protected override void query()
        {
            regionAreaDic = new Dictionary<string, double>();
            List<ResvRegion> resvRegions = MainModel.GetInstance().SearchGeometrys.SelectedResvRegions;//预存区域
            if (resvRegions!=null)
            {
                foreach (ResvRegion region in resvRegions)
                {
                    double area= RegionAreaCalculator.CalculateArea(region.Shape);
                    regionAreaDic.Add(region.RegionName, area);
                }

                fireShowResultForm();
            }
            else
            {
                XtraMessageBox.Show("没有预存区域！");
            }
        }

        private void fireShowResultForm()
        {
            AreasInfoForm frm = null;
            frm = MainModel.GetObjectFromBlackboard(typeof(AreasInfoForm).FullName) as AreasInfoForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new AreasInfoForm(MainModel);
            }
            frm.FillData(regionAreaDic);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
        }
    }
}
