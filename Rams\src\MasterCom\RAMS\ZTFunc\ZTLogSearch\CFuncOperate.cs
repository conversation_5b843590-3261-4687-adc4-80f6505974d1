using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class CFuncOperate
    {
        public CFuncOperate()
        {
            OperateList = new List<CLogOperate>();
        }
        public string FuncName { get; set; }
        public List<CLogOperate> OperateList { get; set; }
        public int NumOfOperation
        {
            get { return OperateList.Count; }
        }

        public void Combine(CFuncOperate func)
        {
            if (func.OperateList.Count > 0)
                OperateList.AddRange(func.OperateList);
        }

        public static CFuncOperate FillFrom(MasterCom.RAMS.Net.Content content)
        {
            CFuncOperate func = new CFuncOperate();
            CLogOperate log = new CLogOperate();
            log.UserName = content.GetParamString();
            log.EventTime = JavaDate.GetDateTimeFromMilliseconds(content.GetParamInt() * 1000L);
            log.FuncName = content.GetParamString();
            log.SubfuncName = content.GetParamString();
            log.City = content.GetParamString();
            log.UserIp = content.GetParamString();
            log.EventDes = content.GetParamString();
            func.FuncName = log.FuncName;
            func.OperateList.Add(log);
            return func;
        }
    }

    public class CLogOperate
    {
        public string UserName { get; set; }
        public DateTime EventTime { get; set; }
        public string FuncName { get; set; }
        public string SubfuncName { get; set; }
        public string City { get; set; }
        public string UserIp { get; set; }
        public string EventDes { get; set; }
    }
}
