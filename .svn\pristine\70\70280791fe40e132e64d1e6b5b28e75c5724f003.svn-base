﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MapWinGIS;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class ImportAreaForm : BaseDialog
    {
        public ImportAreaForm()
            : base()
        {
            InitializeComponent();
            dataGridView.AutoGenerateColumns = false;
        }

        private void btnAddShpFile_Click(object sender, EventArgs e)
        {
            addShpFile(null);
        }

        private List<LayerOption> layers = new List<LayerOption>();

        private void addShpFile(string filePath)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Shp;
            dlg.Multiselect = filePath == null;
            dlg.FileName = filePath;
            List<LayerOption> layerOptions = new List<LayerOption>();
            StringBuilder invalidFiles = new StringBuilder();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                foreach (string path in dlg.FileNames)
                {
                    LayerOption old = this.layers.Find(delegate(LayerOption x) { return x.FilePath == path; });
                    if (old != null)
                    {
                        continue;
                    }
                    List<string> fields;
                    if (isPolygonShpFile(path, out fields))
                    {
                        LayerOption layer = new LayerOption();
                        layer.FilePath = path;
                        layer.Fields = fields;
                        layerOptions.Add(layer);
                    }
                    else
                    {
                        invalidFiles.Append(path + Environment.NewLine);
                    }
                }
            }

            if (!string.IsNullOrEmpty(invalidFiles.ToString()))
            {
                MessageBox.Show("非多边形图层文件：" + Environment.NewLine + invalidFiles.ToString());
            }
            if (layerOptions.Count > 0)
            {
                this.layers.AddRange(layerOptions);
            }
            if (this.layers.Count > 0)
            {
                dataGridView.DataSource = this.layers;
                dataGridView.CurrentCell = dataGridView.Rows[0].Cells[1];
            }
            dataGridView.Invalidate();
        }

        private bool isPolygonShpFile(string fileName, out List<string> fields)
        {
            fields = new List<string>();
            Shapefile shp = new Shapefile();
            if (!shp.Open(fileName, null)
                || shp.ShapefileType != ShpfileType.SHP_POLYGON)
            {
                return false;
            }

            for (int i = 0; i < shp.NumFields; ++i)
            {
                Field field = shp.get_Field(i);
                fields.Add(field.Name);
            }
            shp.Close();
            return true;
        }

        private void dataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex != colShpFile.Index)
            {
                return;
            }

            string fileName = null;
            DataGridViewRow row = dataGridView.Rows[e.RowIndex];
            if (row != null)
            {
                LayerOption layer = row.DataBoundItem as LayerOption;
                if (layer != null)
                {
                    fileName = layer.FilePath;
                }
            }
            addShpFile(fileName);



        }

        private void dataGridView_DataSourceChanged(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                LayerOption layer = row.DataBoundItem as LayerOption;
                if (layer != null)
                {
                    DataGridViewComboBoxCell cbxCell = ((DataGridViewComboBoxCell)row.Cells["colFieldName"]);
                    cbxCell.Items.Clear();
                    foreach (string fName in layer.Fields)
                    {
                        cbxCell.Items.Add(fName);
                    }
                    if (cbxCell.Items.Count > 0)
                    {
                        cbxCell.Value = cbxCell.Items[0];
                    }
                }
            }
        }

        private void btnRemoveShpFile_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                foreach (DataGridViewRow row in dataGridView.SelectedRows)
                {
                    LayerOption layer = row.DataBoundItem as LayerOption;
                    layers.Remove(layer);
                }
                dataGridView.DataSource = null;
                if (this.layers.Count > 0)
                {
                    dataGridView.DataSource = this.layers;
                    dataGridView.CurrentCell = dataGridView.Rows[0].Cells[1];
                }
                dataGridView.Invalidate();
            }
            else
            {
                MessageBox.Show("请选择要移除的图层文件！");
            }
        }

        private void btnEditPath_Properties_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            dlg.FilterIndex = 2;
            dlg.Multiselect = false;
            dlg.FileName = this.btnEditPath.Text;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            this.btnEditPath.Text = dlg.FileName;
            loadXls(dlg.FileName);
        }

        private void loadXls(string filePath)
        {
            DataSet ds = ExcelNPOIManager.ImportFromExcel(filePath);
            DataTable table = ds.Tables[0];
            this.gridCtrlXls.DataSource = ds.Tables[0];
            this.gridCtrlXls.RefreshDataSource();

            cbxSnCol.Items.Clear();
            foreach (DataColumn col in table.Columns)
            {
                cbxSnCol.Items.Add(col);
            }
            if (cbxSnCol.Items.Count > 0)
            {
                cbxSnCol.SelectedIndex = 0;
            }

            this.cbxCol.Items.AddRange(table.Columns);

            List<ColumnAreaRankOption> colRankOption = new List<ColumnAreaRankOption>();
            foreach (AreaRank rank in ZTAreaManager.Instance.Ranks)
            {
                ColumnAreaRankOption option = new ColumnAreaRankOption();
                option.Rank = rank;
                foreach (DataColumn col in table.Columns)
                {
                    if (col.ColumnName == rank.Name
                        || (rank.Name.Contains("村") && col.ColumnName.Contains("村")))
                    {
                        option.Column = col;
                        break;
                    }
                }
                colRankOption.Add(option);
            }
            this.gridCtrlRankSetting.DataSource = colRankOption;
        }

        private void import()
        {
            if (layers.Count == 0)
            {
                MessageBox.Show("请添加区域图层shp文件!");
                return;
            }

            DataTable table = gridCtrlXls.DataSource as DataTable;
            if (table == null)
            {
                MessageBox.Show("请添加区域层级信息Excel文件!");
                return;
            }

            DataColumn xlsSnCol = cbxSnCol.SelectedItem as DataColumn;
            if (xlsSnCol == null)
            {
                MessageBox.Show("请指定区域编号列!");
                return;
            }
            snShapeDic.Clear();

            List<ColumnAreaRankOption> rankOptions = gridCtrlRankSetting.DataSource as List<ColumnAreaRankOption>;
            Dictionary<string, string> colDic = new Dictionary<string, string>();
            foreach (ColumnAreaRankOption item in rankOptions)
            {
                if (colDic.ContainsKey(item.Column.ColumnName))
                {
                    MessageBox.Show(string.Format("Excel列重复指定。【{0}】指定为“{1}”，与已指定为“{2}”冲突。"
                        , item.Column.ColumnName, item.Rank, colDic[item.Column.ColumnName]));
                    return;
                }
                colDic[item.Column.ColumnName] = item.Rank.Name;
            }

            List<string> withoutShape = new List<string>();
            foreach (DataRow row in table.Rows)
            {
                string sn = row[0].ToString();
                MapWinGIS.Shape shape = getShape(sn);
                if (shape == null && !withoutShape.Contains(sn))
                {
                    withoutShape.Add(sn);
                }
                addArea(rankOptions, row);
            }

        }

        private void addArea(List<ColumnAreaRankOption> rankOptions, DataRow row)
        {
            Dictionary<AreaRank, AreaBase> rankAreaDic = new Dictionary<AreaRank, AreaBase>();

            foreach (ColumnAreaRankOption rankOption in rankOptions)
            {
                string areaName = row[rankOption.Column].ToString();
                AreaBase area = ZTAreaManager.Instance.GetArea(rankOption.Rank, areaName);
                if (area == null)
                {
                    area = new AreaBase(rankOption.Rank, areaName);
                    if (!area.Rank.HasChildren)
                    {
                        //area.Shape = areaShp;
                    }
                    ZTAreaManager.Instance.AddArea(area);
                }

                rankAreaDic[area.Rank] = area;
                if (rankOption.Rank.ParentRank != null)
                {
                    AreaBase parentArea = rankAreaDic[rankOption.Rank.ParentRank];
                    parentArea.AddArea(area);
                }
            }
        }

        private Dictionary<string, MapWinGIS.Shape> snShapeDic = new Dictionary<string, MapWinGIS.Shape>();
        private MapWinGIS.Shape getShape(string sn)
        {
            if (snShapeDic.Count == 0)
            {
                snShapeDic[string.Empty] = null;
                foreach (LayerOption option in layers)
                {
                    Shapefile file = new Shapefile();
                    if (!file.Open(option.FilePath, null))
                    {
                        MessageBox.Show(string.Format("打开图层文件{0}失败！", option.FilePath));
                        continue;
                    }
                    int fieldIdx = file.Table.get_FieldIndexByName(option.ShapeSnFieldName);
                    for (int i = 0; i < file.NumShapes; i++)
                    {
                        MapWinGIS.Shape shape = file.get_Shape(i);
                        string snStr = file.Table.get_CellValue(fieldIdx, i).ToString();
                        snShapeDic[snStr] = shape;
                    }
                }
            }
            MapWinGIS.Shape ret = null;
            snShapeDic.TryGetValue(sn, out ret);
            return ret;
        }

        #region inner class
        public class LayerOption
        {
            public string Name
            {
                get
                {
                    if (string.IsNullOrEmpty(FilePath))
                    {
                        return null;
                    }
                    return System.IO.Path.GetFileName(FilePath);
                }
            }
            public string FilePath
            {
                get;
                set;
            }
            public string ShapeSnFieldName
            {
                get;
                set;
            }

            public IEnumerable<string> Fields { get; set; }
        }

        public class ColumnAreaRankOption
        {
            public DataColumn Column
            {
                get;
                set;
            }

            public AreaRank Rank
            {
                get;
                set;
            }
        }

        #endregion

        private void btnImport_Click(object sender, EventArgs e)
        {
            import();
        }

    }

   


}
