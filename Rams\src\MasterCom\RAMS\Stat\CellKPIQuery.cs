﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using Mobius.Utility;

namespace MasterCom.RAMS.Stat
{
    public class CellKPIQuery : ReportStatQueryBase
    {
        private readonly Dictionary<string, bool> selectedCellDic;
        private Model.NetworkType curNetwork { get; set; }
        public Dictionary<string, CellStatInfoItem> CellKeyDataDic { get; set; }

        protected override Model.Interface.StatTbToken getTableNameToken()
        {
            return Model.Interface.StatTbToken.cell;
        }
        public CellKPIQuery(ReporterTemplate template, Dictionary<string, bool> selectedCellDic, Model.NetworkType curNetwork)
            : base()
        {
            this.rptTemplate = template;
            this.selectedCellDic = selectedCellDic;
            this.curNetwork = curNetwork;
            this.IsShowResultForm = false;
        }

        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.KPI_CELL;
                package.Content.PrepareAddParam();
            }
        }

        /// <summary>
        /// RequestType.KPI_CELL，需要一个结束标记。
        /// </summary>
        /// <param name="package"></param>
        /// <param name="reservedParams"></param>
        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            if (!isQueringEvent)
            {
                AddDIYEndOpFlag(package);
            }
        }

        protected override bool getConditionBeforeQuery()
        {
            CellKeyDataDic = new Dictionary<string, CellStatInfoItem>();
            return true;
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            foreach (CellStatInfoItem item in CellKeyDataDic.Values)
            {
                item.KPIData.FinalMtMoGroup();
            }
        }

        protected override void recieveAndHandleSpecificStatData(Package package
            , List<StatImgDefItem> curImgColumnDef
            , KPIStatDataBase singleStatData)
        {
            int lac = package.Content.GetParamInt();
            int ci = package.Content.GetParamInt();
            fillStatData(package, curImgColumnDef, singleStatData);

            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);
            CellStatInfoItem tmp = new CellStatInfoItem();
            tmp.LAC = lac;
            tmp.CI = ci;
            tmp.FileHeader = fi;
            tmp.KPIData.AddStatData(fi, singleStatData, false);
            string cellKey = GetKeyUnionString(this.rptTemplate, tmp);
            CellStatInfoItem fsi = null;
            if (this.CellKeyDataDic.TryGetValue(cellKey, out fsi))
            {
                fsi.KPIData.AddStatData(fi, singleStatData, false);
            }
            else
            {
                fsi = tmp;
                this.CellKeyDataDic[cellKey] = fsi;
            }
        }


        protected override void handleStatEvent(Event evt)
        {
            int lac = (int)evt["LAC"];
            int ci = (int)evt["CI"];
            string key = lac.ToString() + "_" + ci.ToString();
            if (selectedCellDic != null && selectedCellDic.Count > 0 && !selectedCellDic.ContainsKey(key))
            {
                return;
            }

            StatDataEvent singleStatData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            CellStatInfoItem tmp = new CellStatInfoItem();
            tmp.LAC = lac;
            tmp.CI = ci;
            tmp.FileHeader = fi;
            tmp.KPIData.AddStatData(fi, singleStatData, false);
            string cellKey = GetKeyUnionString(this.rptTemplate, tmp);
            CellStatInfoItem fsi = null;
            if (this.CellKeyDataDic.TryGetValue(cellKey, out fsi))
            {
                fsi.KPIData.AddStatData(fi, singleStatData, false);
            }
            else
            {
                fsi = tmp;
                this.CellKeyDataDic[cellKey] = fsi;
            }
        }

        protected override string GetKeyValue(string keyFieldRet, ReporterTemplate tpl, StatInfoBase statInfo)
        {
            string val = "";
            CellStatInfoItem info = statInfo as CellStatInfoItem;
            if (keyFieldRet == "kLAC")
            {
                val = "" + info.LAC;
            }
            else if (keyFieldRet == "kCI")
            {
                val = "" + info.CI;
            }
            else if (info.FileHeader != null)
            {
                return getCellName(keyFieldRet, tpl, ref val, info);
            }
            else
            {
                val = "-";
            }

            return val;
        }

        private string getCellName(string keyFieldRet, ReporterTemplate tpl, ref string val, CellStatInfoItem info)
        {
            if (keyFieldRet == "kCellName")
            {
                return getGsmCellName(info);
            }
            else if (keyFieldRet == "kTDCellName")
            {
                return getTDCellName(info);
            }
            else if (keyFieldRet == "kWCellName")
            {
                return getWCellName(info);
            }
            else if (keyFieldRet == "kLTECellName")
            {
                return getLTECellName(info);
            }
            else
            {
                val = GetKeyValueBase(keyFieldRet, tpl
                    , info.FileHeader.AreaTypeID, info.FileHeader.AreaID, info.FileHeader.DistrictID, info);
            }
            return val;
        }

        private string getGsmCellName(CellStatInfoItem info)
        {
            Cell cell = CellManager.GetInstance().GetCell(
                JavaDate.GetDateTimeFromMilliseconds(1000L * info.FileHeader.BeginTime), (ushort)info.LAC, (ushort)info.CI);
            if (cell != null)
            {
                return cell.Name;
            }
            else
            {
                return info.LAC + "-" + info.CI;
            }
        }

        private string getTDCellName(CellStatInfoItem info)
        {
            TDCell cell = CellManager.GetInstance().GetTDCell(
                JavaDate.GetDateTimeFromMilliseconds(1000L * info.FileHeader.BeginTime), info.LAC, info.CI);
            if (cell != null)
            {
                return cell.Name;
            }
            else
            {
                return info.LAC + "-" + info.CI;
            }
        }

        private string getWCellName(CellStatInfoItem info)
        {
            WCell cell = CellManager.GetInstance().GetWCell(
                JavaDate.GetDateTimeFromMilliseconds(1000L * info.FileHeader.BeginTime), info.LAC, info.CI);
            if (cell != null)
            {
                return cell.Name;
            }
            else
            {
                return info.LAC + "-" + info.CI;
            }
        }

        private string getLTECellName(CellStatInfoItem info)
        {
            LTECell cell = CellManager.GetInstance().GetLTECell(
                JavaDate.GetDateTimeFromMilliseconds(1000L * info.FileHeader.BeginTime), info.LAC, info.CI);
            if (cell != null)
            {
                return cell.Name;
            }
            else
            {
                return info.LAC + "-" + info.CI;
            }
        }
    }
    public class CellStatInfoItem : StatInfoBase, IComparable<CellStatInfoItem>
    {
        public CellStatInfoItem()
        {
            this.KPIData = new KPIDataGroup(null);
        }
        public int LAC { get; set; }
        public int CI { get; set; }

        #region IComparable<CellStatInfoItem> 成员

        public int CompareTo(CellStatInfoItem other)
        {
            if (this.IsSum != other.IsSum)
            {
                return this.IsSum.CompareTo(other.IsSum);
            }
            else
            {
                return this.DistrictID.CompareTo(other.DistrictID);
            }
        }

        #endregion
    }
}
