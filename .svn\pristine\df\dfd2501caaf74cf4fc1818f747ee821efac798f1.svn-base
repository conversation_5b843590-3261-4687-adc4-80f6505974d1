﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class UpdateUserCityRight : DIYSQLBase
   {
        private readonly List<User> users;
        public UpdateUserCityRight(List<User> users)
            : base(Model.MainModel.GetInstance())
        {
            this.users = users;
            MainDB = true;
        }

        //insert语句可能过长，需分包处理
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }

                E_VType[] retArrDef = getSqlRetTypeArr();
                string strsql = getSqlTextString();
                string[] strArr = strsql.Split(';');
                int curIdx = 0;
                while (curIdx < strArr.Length)
                {
                    curIdx = addParam(package, retArrDef, strArr, curIdx);
                    clientProxy.Send();
                    System.Threading.Thread.Sleep(300);
                }
                receiveRetData(clientProxy);
            }
            catch
            {
                //continue
            }
        }

        private int addParam(Package package, E_VType[] retArrDef, string[] strArr, int curIdx)
        {
            StringBuilder txt = new StringBuilder();
            for (; curIdx < strArr.Length; curIdx++)
            {
                txt.Append(strArr[curIdx] + ";");
                if (txt.Length > 6000)
                {
                    break;
                }
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(txt.ToString());
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    if (i < retArrDef.Length - 1)
                    {
                        sb.Append(",");
                    }
                }
            }
            package.Content.AddParam(sb.ToString());
            return curIdx;
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            string tableName = "tb_cfg_static_user_city_id";
            foreach(User user in users)
            {
                sb.Append(string.Format("delete from {0} where user_id={1};" , tableName , user.ID)); //原先记录
                foreach(int cityID in user.CityIDs)
                {
                    sb.Append(string.Format("INSERT INTO {0} (user_id, city_id) VALUES({1},{2});"
                        , tableName , user.ID , cityID));
                }
            }
            return sb.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "更新用户地市权限设置"; }
        }
    }
}
