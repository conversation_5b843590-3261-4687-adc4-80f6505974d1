﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.NOP
{
    public partial class OrderAnalyzerForm : MinCloseForm
    {
        public OrderAnalyzerForm()
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            DisposeWhenClose = true;
            //init();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (results == null)
            {
                return;
            }
           


                #region
                List<NPOIRow> summBroTables = new List<NPOIRow>();
                NPOIRow summBroTitleRow = new NPOIRow();
                summBroTitleRow.AddCellValue("地市");
                summBroTitleRow.AddCellValue("T1准确派单数");
                summBroTitleRow.AddCellValue("T1预处理工单数");
                summBroTitleRow.AddCellValue("T1派单准确性");
                summBroTitleRow.AddCellValue("T1预处理工单数");
                summBroTitleRow.AddCellValue("生成工单总数");
                summBroTitleRow.AddCellValue("T1预处理完整性");
                summBroTitleRow.AddCellValue("T1未超时工单数");
                summBroTitleRow.AddCellValue("生成工单总数");
                summBroTitleRow.AddCellValue("T1预处理及时性");
                summBroTitleRow.AddCellValue("T2指标验证失败数");
                summBroTitleRow.AddCellValue("T2派单T0工单数");
                summBroTitleRow.AddCellValue("T2方案准确性");
                summBroTitleRow.AddCellValue("T0可执行工单数");
                summBroTitleRow.AddCellValue("T1/T2派发T0工单数");
                summBroTitleRow.AddCellValue("方案可执行性");
                summBroTitleRow.AddCellValue("T2未超时工单数");
                summBroTitleRow.AddCellValue("T2分析工单数");
                summBroTitleRow.AddCellValue("T2方案制定及时性");
                summBroTitleRow.AddCellValue("T0未超时工单数");
                summBroTitleRow.AddCellValue("派发T0工单数");
                summBroTitleRow.AddCellValue("T0落实及时性");
                summBroTitleRow.AddCellValue("T0验证失败工单数");
                summBroTitleRow.AddCellValue("T0回复工单数");
                summBroTitleRow.AddCellValue("T0落实准确性");
                summBroTitleRow.AddCellValue("质检未超时工单数");
                summBroTitleRow.AddCellValue("质检总工单");
                summBroTitleRow.AddCellValue("质检及时性");
                summBroTitleRow.AddCellValue("T1质检未超时工单数");
                summBroTitleRow.AddCellValue("T1质检总工单");
                summBroTitleRow.AddCellValue("T1质检及时性");
                summBroTitleRow.AddCellValue("T2质检未超时工单数");
                summBroTitleRow.AddCellValue("T2质检总工单");
                summBroTitleRow.AddCellValue("T2质检及时性");
                summBroTitleRow.AddCellValue("工程督办解决工单数");
                summBroTitleRow.AddCellValue("工程督办工单数");
                summBroTitleRow.AddCellValue("督办挂起解决率");
                

                summBroTables.Add(summBroTitleRow);
                if (results != null)
                {
                    foreach (OrderAnalyzerResult res in results)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(res.City);
                    row.AddCellValue(res.T1ExactOrders);
                    row.AddCellValue(res.T1ProDealOrders);
                    row.AddCellValue(res.T1Exactly);
                    row.AddCellValue(res.T1ProDealOrders);
                    row.AddCellValue(res.TotalOrders);
                    row.AddCellValue(res.ProDealRate);
                    row.AddCellValue(res.T1UnTimeoutOrders);
                    row.AddCellValue(res.TotalOrders);
                    row.AddCellValue(res.T1ProDealTimely);
                    row.AddCellValue(res.T2CheckFaliedOrders);
                    row.AddCellValue(res.T2ToT0Orders);
                    row.AddCellValue(res.T2Exactly);
                    row.AddCellValue(res.DealOrders);
                    row.AddCellValue(res.ToT0Orders);
                    row.AddCellValue(res.OrderPerformability);
                    row.AddCellValue(res.T2UnTimeoutOrders);
                    row.AddCellValue(res.T2DealOrders);
                    row.AddCellValue(res.T2Timely);
                    row.AddCellValue(res.T0UnTimeoutOrders);
                    row.AddCellValue(res.T0Orders);
                    row.AddCellValue(res.T0Timely);
                    row.AddCellValue(res.T0CheckFaliedOrders);
                    row.AddCellValue(res.T0ReplyOrders);
                    row.AddCellValue(res.T0Exactly);
                    row.AddCellValue(res.CheckUnTimeout);
                    row.AddCellValue(res.CheckOrders);
                    row.AddCellValue(res.CheckTimely);
                    row.AddCellValue(res.T1CheckUnTimeout);
                    row.AddCellValue(res.T1CheckOrders);
                    row.AddCellValue(res.T1CheckTimely);
                    row.AddCellValue(res.T2CheckUnTimeout);
                    row.AddCellValue(res.T2CheckOrders);
                    row.AddCellValue(res.T2CheckTimely);
                    row.AddCellValue(res.DuBanDealOrders);
                    row.AddCellValue(res.DuBanOrders);
                    row.AddCellValue(res.DuBanDealRate);

                        

                   summBroTables.Add(row);
                }
            }

                #endregion


                ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() {  summBroTables },
                new List<string>() { "工单分析详情" });
        }


        List<OrderAnalyzerResult> results = null;
        public void FillData(object result)
        {
            results = result as List<OrderAnalyzerResult>;
            gcTask.DataSource = results;
            gcTask.RefreshDataSource();
            

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }
    }
}
