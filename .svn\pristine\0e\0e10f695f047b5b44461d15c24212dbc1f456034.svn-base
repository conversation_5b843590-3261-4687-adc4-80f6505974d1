﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKpiPkByRegion : QueryKPIStatByRegion
    {
        Dictionary<PkGridDataGroup, List<PkGridDataCombine>> gridCombinedDic = null;
        public override string Name
        {
            get
            {
                return "区域KPI竞比";
            }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 11000, 11019, this.Name);
        }

        PKCondition pkCond = null;
        protected override bool getConditionBeforeQuery()
        {
            if (pkCond == null)
            {
                pkCond = new PKCondition();
            }
            PKBaseSettingDlg dlg = new PKBaseSettingDlg();
            dlg.IsStatLastOptionVisible = false;
            dlg.SetCondition(pkCond);

            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            pkCond = dlg.GetCondition();
            regDataDic = new Dictionary<ResvRegion, PkGridDataGroup>();
            condition = pkCond.ConditionCM;
            return true;
        }

        CarrierType curCarrierType;
        string[] areaSqlArray;
        protected override void queryDistrictData(int districtID)
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port
                , MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                areaSqlArray = getDIYAreaTypeAndID(condition.Areas);

                curCarrierType = CarrierType.ChinaMobile;
                condition = pkCond.ConditionCM;
                condition.Geometorys = MainModel.SearchGeometrys;
                WaitBox.Show("正在统计移动数据...", queryInThread, clientProxy);
                System.Threading.Thread.Sleep(100);

                curCarrierType = CarrierType.ChinaUnicom;
                condition = pkCond.ConditionCU;
                condition.Geometorys = MainModel.SearchGeometrys;
                WaitBox.Show("正在统计联通数据...", queryInThread, clientProxy);
                System.Threading.Thread.Sleep(100);

                curCarrierType = CarrierType.ChinaTelecom;
                condition = pkCond.ConditionCT;
                condition.Geometorys = MainModel.SearchGeometrys;
                WaitBox.Show("正在统计电信数据...", queryInThread, clientProxy);
                System.Threading.Thread.Sleep(100);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        /// <summary>
        /// 查询某时间段内的数据
        /// </summary>
        /// <param name="period">当该参数为null时，视为按轮查询</param>
        /// <param name="clientProxy"></param>
        /// <param name="package"></param>
        protected override void queryPeriodInfo(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            sendImgGrid(period, clientProxy, reservedParams);
            sendEvent(period, clientProxy, reservedParams);
            afterRecieveOnePeriodData(period);
        }

        protected virtual void sendImgGrid(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            isQueringEvent = false;
            int curIdx = 0;
            if (areaSqlArray.Length == 0)
            {
                queryImg(period, clientProxy, reservedParams, "");
                return;
            }

            while (curIdx < areaSqlArray.Length)//分批次发送包
            {
                string sqlTxt = getSqlTxt(ref curIdx);
                queryImg(period, clientProxy, reservedParams, sqlTxt);
            }
        }

        private void queryImg(TimePeriod period, ClientProxy clientProxy, object[] reservedParams, string sqlTxt)
        {
            preparePackageCommand(clientProxy.Package);//添加命令告诉服务器查询类型
            preparePackageCondition(sqlTxt, clientProxy.Package, period, reservedParams);//添加查询消息
            AddDIYEndOpFlag(clientProxy.Package);//添加最后标志

            preparePackageNeededInfo_StatImg(clientProxy.Package, reservedParams);
            clientProxy.Send();
            recieveInfo_ImgGrid(clientProxy);
        }

        protected virtual void sendEvent(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            if (isQueryAllParams || this.evtIDSvrIDDic.Count > 0)
            {
                //event
                isQueringEvent = true;
                int curIdx = 0;
                if (areaSqlArray.Length == 0)
                {
                    queryEvt(period, clientProxy, reservedParams, "");
                    return;
                }

                while (curIdx < areaSqlArray.Length)//分批次发送包
                {
                    string sqlTxt = getSqlTxt(ref curIdx);
                    queryEvt(period, clientProxy, reservedParams, sqlTxt);
                }
            }
        }

        private void queryEvt(TimePeriod period, ClientProxy clientProxy, object[] reservedParams, string sqlTxt)
        {
            preparePackageCommand(clientProxy.Package);//添加命令告诉服务器查询类型
            preparePackageCondition(sqlTxt, clientProxy.Package, period, reservedParams);//添加查询消息
            AddDIYEndOpFlag(clientProxy.Package);//添加最后标志

            preparePackageNeededInfo_Event(clientProxy.Package);
            clientProxy.Send();
            recieveInfo_Event(clientProxy);
        }

        protected virtual string getSqlTxt(ref int curIdx)
        {
            StringBuilder strb = new StringBuilder();
            for (; curIdx < areaSqlArray.Length; curIdx++)
            {
                string curStr = areaSqlArray[curIdx];
                if (strb.Length + curStr.Length > 5000)
                {
                    break;
                }
                strb.Append(curStr + ") or ");
            }
            string sqlTxt = strb.ToString().Remove(strb.Length - 4);
            return sqlTxt;
        }

        protected virtual void preparePackageCondition(string sqlTxt, Package package, TimePeriod period, params object[] reservedParams)
        {
            if (period == null)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);//轮次以一个日期为周期
            }
            else
            {
                AddDIYPeriod(package, period);//添加日期
            }
            AddDIYProject(package, condition.Projects);//添加界面选择数据来源栏信息
            AddDIYService(package, condition.ServiceTypes);//添加界面选择业务类型栏信息
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);//添加界面选择代维栏信息
                MainModel.FileAgentFilter = condition.AgentIds;
            }
            else
            {
                MainModel.FileAgentFilter.Clear();
            }
            AddDIYCarrierType(package, condition.CarrierTypes);//添加界面选择运营商栏信息
            AddDIYFileFilter(package, condition);//向包中添加文件筛选信息
            if (condition.DeviceTypes.Count != 0)
            {
                AddDIYDeviceType(package, condition.DeviceTypes);//设备类型
            }
            AddDIYStatStatus(package);//统计状态
            AddDIYAreaTypeAndID(sqlTxt, package);//向包中添加区域类型和ID
            AddGeographicFilter(package);//增加地理过滤
            AddExtraCondition(package, reservedParams);//添加额外条件
        }

        protected virtual void AddDIYAreaTypeAndID(string sqlTxt, Package package)
        {
            if (!string.IsNullOrEmpty(sqlTxt))
            {
                package.Content.AddParam((byte)OpOptionDef.DIYSql);
                package.Content.AddParam(sqlTxt);
            }
        }

        protected virtual string[] getDIYAreaTypeAndID(Dictionary<int, List<int>> areas)
        {
            string[] sqlTxtArray = new string[0];
            if (areas.Count > 0)
            {
                StringBuilder sb = new StringBuilder();
                List<int> onlyAreaType = new List<int>();
                foreach (int areaType in areas.Keys)
                {
                    List<int> areaIDList = areas[areaType];
                    if (areaIDList == null || areaIDList.Count == 0)
                    {
                        onlyAreaType.Add(areaType);
                    }
                    else
                    {
                        sb.Append(makeAreaSQL(areaType, areaIDList).ToString());
                    }
                }
                if (onlyAreaType.Count > 0)
                {
                    sb.Append("(a.iareatype in(" + GetStrAreaID(onlyAreaType) + ")) or ");
                }
                string sqlTxt = sb.ToString();
                sqlTxtArray = sqlTxt.Split(new string[] { ") or " }, StringSplitOptions.RemoveEmptyEntries);
            }
            return sqlTxtArray;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            string triadIDSet = string.Empty;
            List<string> formulaSet = new List<string>();
            if (curCarrierType == CarrierType.ChinaMobile)
            {
                formulaSet.Add(pkCond.SelTemplate.CMHub.PkBase.FormulaExp);
            }
            else if (curCarrierType == CarrierType.ChinaUnicom)
            {
                formulaSet.Add(pkCond.SelTemplate.CUHub.PkBase.FormulaExp);
            }
            else if (curCarrierType == CarrierType.ChinaTelecom)
            {
                formulaSet.Add(pkCond.SelTemplate.CTHub.PkBase.FormulaExp);
            }
            triadIDSet = getTriadIDIgnoreServiceType(formulaSet);
            return triadIDSet;
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!MainModel.SearchGeometrys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
            {
                return;
            }
            fillStatData(package, curImgColumnDef, singleStatData);
            List<ResvRegion> regs = getStatImgIntersectRegions(lng, lat);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
            foreach (ResvRegion reg in regs)
            {
                saveRegionData(reg, fi, singleStatData);
            }
            mergeRootNodeData(grid.CenterLng, grid.CenterLat, fi, singleStatData);
        }

        protected override void handleStatEvent(Event evt)
        {
            if (!MainModel.SearchGeometrys.GeoOp.Contains(evt.Longitude, evt.Latitude))
            {
                return;
            }
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            List<ResvRegion> regs = getEventInRegions(evt);
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            foreach (ResvRegion reg in regs)
            {
                saveRegionData(reg, fi, eventData);
            }
            mergeRootNodeData(evt.Longitude, evt.Latitude, fi, eventData);
        }

        private Dictionary<ResvRegion, PkGridDataGroup> regDataDic = null;
        private void saveRegionData(ResvRegion reg, FileInfo fi, KPIStatDataBase data)
        {
            PkGridDataGroup dataGrp = null;
            if (!regDataDic.TryGetValue(reg, out dataGrp))
            {
                dataGrp = new PkGridDataGroup(reg);
                regDataDic.Add(reg, dataGrp);
            }
            dataGrp.AddStatData(fi, data);
        }

        readonly Dictionary<string, ResvRegion> resvRegionDic = new Dictionary<string, ResvRegion>();
        protected override void mergeRootNodeData(double longitude, double latitude, FileInfo fi, KPIStatDataBase kpiData)
        {
            if (MainModel.RootNodeGeometrys && Condition.Geometorys.SelectedResvRegionDic != null
                && Condition.Geometorys.SelectedResvRegionDic.Count > 0)
            {
                getResvRegion(longitude, latitude);
                foreach (var varPair in resvRegionDic)
                {
                    saveRegionData(varPair.Value, fi, kpiData);
                }
            }
        }

        protected void getResvRegion(double longitude, double latitude)
        {
            foreach (var varPair in MainModel.SearchGeometrys.SelectedResvRegionDic)
            {
                foreach (ResvRegion res in varPair.Value)
                {
                    if (res.GeoOp.CheckPointInRegion(longitude, latitude) && !resvRegionDic.ContainsKey(varPair.Key))
                    {
                        ResvRegion reg = new ResvRegion();
                        reg.RegionName = varPair.Key;
                        resvRegionDic.Add(varPair.Key, reg);
                        break;
                    }
                }
            }
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            WaitBox.Show("正在汇聚连续栅格信息...", doSomethingAfterQuery, reservedParams);
        }
        private void doSomethingAfterQuery(object obj)
        {
            try
            {
                gridCombinedDic = new Dictionary<PkGridDataGroup, List<PkGridDataCombine>>();
                foreach (PkGridDataGroup dataGrp in regDataDic.Values)
                {
                    getFileGridData(dataGrp);

                    dataGrp.FinalMtMoGroup();
                    WaitBox.ProgressPercent = 10;
                    List<PkGridDataCombine> combineGridList = combineGrid(dataGrp);
                    calculateCombineGridList(combineGridList);
                    gridCombinedDic.Add(dataGrp, combineGridList);
                }
                base.afterRecieveAllData(obj);
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitBox.Close();
            }
        }

        private void getFileGridData(PkGridDataGroup dataGrp)
        {
            foreach (PkGridDataHub grid in dataGrp.Matrix)
            {
                foreach (PkGridDataHub fileGrid in grid.FileGridDic.Values)
                {
                    double dcm = 0;
                    dcm = fileGrid.CalcFormula(CarrierType.ChinaMobile, -1, pkCond.SelTemplate.CMHub.PkBase.FormulaExp);
                    fileGrid.CMFormulaValue = dcm;
                }
                grid.SortFileGrid();
                grid.SetLowestGridLogName(2);
            }
        }

        private List<PkGridDataCombine> combineGrid(PkGridDataGroup dataGrp)
        {
            int index = 0;
            int progress = 0;
            List<PkGridDataCombine> combinedList = new List<PkGridDataCombine>();

            foreach (PkGridDataHub grid in dataGrp.Matrix)
            {
                if (grid != null)
                {
                    getGridData(grid);
                    combineMethod(combinedList, grid);
                }
                progressChange(ref index, ref progress);
            }
            return combinedList;
        }

        private void getGridData(PkGridDataHub grid)
        {
            double dcm = 0, dcu = 0, dct = 0;
            dcm = grid.CalcFormula(CarrierType.ChinaMobile, -1, pkCond.SelTemplate.CMHub.PkBase.FormulaExp);
            dcu = grid.CalcFormula(CarrierType.ChinaUnicom, -1, pkCond.SelTemplate.CUHub.PkBase.FormulaExp);
            dct = grid.CalcFormula(CarrierType.ChinaTelecom, -1, pkCond.SelTemplate.CTHub.PkBase.FormulaExp);
            Alghirithm althm = pkCond.GetAlghirithm(dcm, dcu, dct);
            grid.RangName = althm.Name;
            //整个栅格的值
            grid.CMFormulaValue = dcm;
        }

        private void combineMethod(List<PkGridDataCombine> combinedList, PkGridDataHub cu)
        {
            List<PkGridDataCombine> tmpList = new List<PkGridDataCombine>();
            foreach (PkGridDataCombine PkGridDataCombine in combinedList)
            {
                if (PkGridDataCombine.Intersect(cu))
                {
                    tmpList.Add(PkGridDataCombine);
                }
            }
            if (tmpList.Count == 0)
            {
                combinedList.Add(new PkGridDataCombine(cu));
            }
            else
            {
                PkGridDataCombine combinTmp = null;
                foreach (PkGridDataCombine item in tmpList)
                {
                    if (combinTmp == null)
                    {
                        combinTmp = item;
                        combinTmp.Matrix[cu.RowIdx, cu.ColIdx] = cu;
                    }
                    else
                    {
                        combinTmp.Join(item);
                        combinedList.Remove(item);
                    }
                }
            }
        }

        private void progressChange(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (10) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        private void calculateCombineGridList(List<PkGridDataCombine> combineGridList)
        {
            foreach (PkGridDataCombine combineGrid in combineGridList)
            {
                foreach (PkGridDataHub grid in combineGrid.Matrix.Grids)
                {
                    foreach (PkGridDataHub fileGrid in grid.FileGridDic.Values)
                    {
                        combineGrid.AllFileGridList.Add(fileGrid);
                    }
                }
                combineGrid.SortFileGrid();
                combineGrid.SetLowestGridLogName(2);

                combineGrid.CalculateRoadName();
            }
        }

        protected override void fireShowResult()
        {
            if (regDataDic.Count == 0)
            {
                MessageBox.Show("无栅格统计数据！");
                return;
            }
            KpiPkResultForm frm = MainModel.CreateResultForm(typeof(KpiPkResultForm)) as KpiPkResultForm;
            frm.FillData(pkCond, regDataDic.Values, gridCombinedDic);
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
