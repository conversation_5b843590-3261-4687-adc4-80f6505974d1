﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class EmulateCovSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.chkDrawPoints = new DevExpress.XtraEditors.CheckEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.numUDDistance = new System.Windows.Forms.NumericUpDown();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton2 = new DevExpress.XtraEditors.SimpleButton();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numUDPt = new System.Windows.Forms.NumericUpDown();
            this.numUDPr = new System.Windows.Forms.NumericUpDown();
            this.cbxWay = new System.Windows.Forms.ComboBox();
            this.label5 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.chkDrawPoints.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numUDDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numUDPt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numUDPr)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(32, 23);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "覆盖距离≤";
            // 
            // chkDrawPoints
            // 
            this.chkDrawPoints.Location = new System.Drawing.Point(32, 131);
            this.chkDrawPoints.Name = "chkDrawPoints";
            this.chkDrawPoints.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkDrawPoints.Properties.Appearance.Options.UseFont = true;
            this.chkDrawPoints.Properties.Caption = "绘制小区覆盖边界";
            this.chkDrawPoints.Size = new System.Drawing.Size(164, 19);
            this.chkDrawPoints.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(209, 20);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(161, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "（判断区域以外计算的小区）";
            // 
            // numUDDistance
            // 
            this.numUDDistance.Location = new System.Drawing.Point(103, 18);
            this.numUDDistance.Maximum = new decimal(new int[] {
            300000,
            0,
            0,
            0});
            this.numUDDistance.Name = "numUDDistance";
            this.numUDDistance.Size = new System.Drawing.Size(95, 21);
            this.numUDDistance.TabIndex = 5;
            this.numUDDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numUDDistance.Value = new decimal(new int[] {
            3000,
            0,
            0,
            0});
            // 
            // simpleButton1
            // 
            this.simpleButton1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton1.Appearance.Options.UseFont = true;
            this.simpleButton1.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.simpleButton1.Location = new System.Drawing.Point(211, 176);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(75, 23);
            this.simpleButton1.TabIndex = 6;
            this.simpleButton1.Text = "确定";
            // 
            // simpleButton2
            // 
            this.simpleButton2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton2.Appearance.Options.UseFont = true;
            this.simpleButton2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.simpleButton2.Location = new System.Drawing.Point(308, 176);
            this.simpleButton2.Name = "simpleButton2";
            this.simpleButton2.Size = new System.Drawing.Size(75, 23);
            this.simpleButton2.TabIndex = 7;
            this.simpleButton2.Text = "取消";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(32, 52);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 8;
            this.label3.Text = "发射功率≥";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(219, 53);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 9;
            this.label4.Text = "接收功率≥";
            // 
            // numUDPt
            // 
            this.numUDPt.Location = new System.Drawing.Point(103, 50);
            this.numUDPt.Minimum = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numUDPt.Name = "numUDPt";
            this.numUDPt.Size = new System.Drawing.Size(95, 21);
            this.numUDPt.TabIndex = 10;
            this.numUDPt.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numUDPt.Value = new decimal(new int[] {
            60,
            0,
            0,
            -2147483648});
            // 
            // numUDPr
            // 
            this.numUDPr.Location = new System.Drawing.Point(288, 48);
            this.numUDPr.Minimum = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numUDPr.Name = "numUDPr";
            this.numUDPr.Size = new System.Drawing.Size(95, 21);
            this.numUDPr.TabIndex = 11;
            this.numUDPr.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numUDPr.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // cbxWay
            // 
            this.cbxWay.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxWay.FormattingEnabled = true;
            this.cbxWay.Location = new System.Drawing.Point(103, 92);
            this.cbxWay.Name = "cbxWay";
            this.cbxWay.Size = new System.Drawing.Size(121, 20);
            this.cbxWay.TabIndex = 12;
            this.cbxWay.SelectedIndexChanged += new System.EventHandler(this.cbxWay_SelectedIndexChanged);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(32, 95);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 13;
            this.label5.Text = "仿真图类型";
            // 
            // EmulateCovSettingForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(414, 218);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.cbxWay);
            this.Controls.Add(this.numUDPr);
            this.Controls.Add(this.numUDPt);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.simpleButton2);
            this.Controls.Add(this.simpleButton1);
            this.Controls.Add(this.numUDDistance);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.chkDrawPoints);
            this.Controls.Add(this.label1);
            this.Name = "EmulateCovSettingForm";
            this.Text = "查询设置";
            ((System.ComponentModel.ISupportInitialize)(this.chkDrawPoints.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numUDDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numUDPt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numUDPr)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.CheckEdit chkDrawPoints;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numUDDistance;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraEditors.SimpleButton simpleButton2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numUDPt;
        private System.Windows.Forms.NumericUpDown numUDPr;
        private System.Windows.Forms.ComboBox cbxWay;
        private System.Windows.Forms.Label label5;
    }
}