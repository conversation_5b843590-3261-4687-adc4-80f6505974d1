﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.KPIReport
{
    public class CoverGridQuery : DIYQueryCoverGridByRegion
    {
        private readonly List<AreaBase> areas;
        public CoverGridQuery(List<AreaBase> areas)
            : base(MainModel.GetInstance())
        {
            this.areas = areas;
        }

        protected override void AddGeographicFilter(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            MasterCom.MTGis.DbRect rect = null;

            foreach (AreaBase item in areas)
            {
                if (rect == null)
                {
                    rect = item.Bounds.Clone();
                }
                else
                {
                    rect.MergeRects(item.Bounds);
                }
            }

            if (rect != null)
            {
                package.Content.AddParam(rect.x1);
                package.Content.AddParam(rect.y2);
                package.Content.AddParam(rect.x2);
                package.Content.AddParam(rect.y1);
            }
            this.AddDIYRegion_Intersect(package);
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (areas.Count == 0)
            {
                return false;
            }
            else
            {
                foreach (AreaBase item in areas)
                {
                    if (item.MapOper.CheckPointInRegion(grid.CenterLng, grid.CenterLat))
                    {
                        return true;
                    }
                }
            }
            return false;
        }
    }
}
