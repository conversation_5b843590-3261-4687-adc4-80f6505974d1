﻿namespace MasterCom.RAMS.Func
{
    partial class NRCellInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.GroupBox groupBox1;
            System.Windows.Forms.Label label12;
            System.Windows.Forms.Label label9;
            System.Windows.Forms.Label label8;
            System.Windows.Forms.Label label6;
            System.Windows.Forms.Label label7;
            System.Windows.Forms.Label label5;
            System.Windows.Forms.Label label4;
            System.Windows.Forms.Label label2;
            System.Windows.Forms.Label label1;
            System.Windows.Forms.Label label3;
            System.Windows.Forms.Label labelCI;
            System.Windows.Forms.Label label11;
            System.Windows.Forms.Label labelID;
            this.btnBTSInfo = new DevExpress.XtraEditors.SimpleButton();
            this.txtCode = new System.Windows.Forms.TextBox();
            this.txtName = new System.Windows.Forms.TextBox();
            this.txtBTS = new System.Windows.Forms.TextBox();
            this.txtDesc = new System.Windows.Forms.TextBox();
            this.txtDirection = new System.Windows.Forms.TextBox();
            this.txtPCI = new System.Windows.Forms.TextBox();
            this.txtTAC = new System.Windows.Forms.TextBox();
            this.txtSectorID = new System.Windows.Forms.TextBox();
            this.txtEarfcn = new System.Windows.Forms.TextBox();
            this.txtECI = new System.Windows.Forms.TextBox();
            this.txtLat = new System.Windows.Forms.TextBox();
            this.txtLng = new System.Windows.Forms.TextBox();
            this.txtID = new System.Windows.Forms.TextBox();
            groupBox1 = new System.Windows.Forms.GroupBox();
            label12 = new System.Windows.Forms.Label();
            label9 = new System.Windows.Forms.Label();
            label8 = new System.Windows.Forms.Label();
            label6 = new System.Windows.Forms.Label();
            label7 = new System.Windows.Forms.Label();
            label5 = new System.Windows.Forms.Label();
            label4 = new System.Windows.Forms.Label();
            label2 = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            label3 = new System.Windows.Forms.Label();
            labelCI = new System.Windows.Forms.Label();
            label11 = new System.Windows.Forms.Label();
            labelID = new System.Windows.Forms.Label();
            groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(this.btnBTSInfo);
            groupBox1.Controls.Add(label12);
            groupBox1.Controls.Add(label9);
            groupBox1.Controls.Add(label8);
            groupBox1.Controls.Add(this.txtCode);
            groupBox1.Controls.Add(this.txtName);
            groupBox1.Controls.Add(this.txtBTS);
            groupBox1.Controls.Add(this.txtDesc);
            groupBox1.Controls.Add(this.txtDirection);
            groupBox1.Controls.Add(this.txtPCI);
            groupBox1.Controls.Add(this.txtTAC);
            groupBox1.Controls.Add(label6);
            groupBox1.Controls.Add(label7);
            groupBox1.Controls.Add(label5);
            groupBox1.Controls.Add(label4);
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(label1);
            groupBox1.Controls.Add(this.txtSectorID);
            groupBox1.Controls.Add(this.txtEarfcn);
            groupBox1.Controls.Add(this.txtECI);
            groupBox1.Controls.Add(label3);
            groupBox1.Controls.Add(labelCI);
            groupBox1.Controls.Add(this.txtLat);
            groupBox1.Controls.Add(this.txtLng);
            groupBox1.Controls.Add(this.txtID);
            groupBox1.Controls.Add(label11);
            groupBox1.Controls.Add(labelID);
            groupBox1.Location = new System.Drawing.Point(14, 14);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new System.Drawing.Size(355, 286);
            groupBox1.TabIndex = 1;
            groupBox1.TabStop = false;
            groupBox1.Text = "Info";
            // 
            // btnBTSInfo
            // 
            this.btnBTSInfo.Location = new System.Drawing.Point(190, 242);
            this.btnBTSInfo.Name = "btnBTSInfo";
            this.btnBTSInfo.Size = new System.Drawing.Size(52, 27);
            this.btnBTSInfo.TabIndex = 10;
            this.btnBTSInfo.Text = "&Info";
            this.btnBTSInfo.Click += new System.EventHandler(this.btnBTSInfo_Click);
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new System.Drawing.Point(202, 92);
            label12.Name = "label12";
            label12.Size = new System.Drawing.Size(35, 12);
            label12.TabIndex = 4;
            label12.Text = "&纬度:";
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new System.Drawing.Point(202, 57);
            label9.Name = "label9";
            label9.Size = new System.Drawing.Size(35, 12);
            label9.TabIndex = 4;
            label9.Text = "&Code:";
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new System.Drawing.Point(34, 26);
            label8.Name = "label8";
            label8.Size = new System.Drawing.Size(35, 12);
            label8.TabIndex = 2;
            label8.Text = "&Name:";
            // 
            // txtCode
            // 
            this.txtCode.AcceptsReturn = true;
            this.txtCode.BackColor = System.Drawing.SystemColors.Window;
            this.txtCode.Location = new System.Drawing.Point(246, 54);
            this.txtCode.Name = "txtCode";
            this.txtCode.ReadOnly = true;
            this.txtCode.Size = new System.Drawing.Size(93, 21);
            this.txtCode.TabIndex = 5;
            this.txtCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // txtName
            // 
            this.txtName.BackColor = System.Drawing.SystemColors.Window;
            this.txtName.Location = new System.Drawing.Point(82, 22);
            this.txtName.Name = "txtName";
            this.txtName.ReadOnly = true;
            this.txtName.Size = new System.Drawing.Size(256, 21);
            this.txtName.TabIndex = 3;
            this.txtName.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // txtBTS
            // 
            this.txtBTS.BackColor = System.Drawing.SystemColors.Window;
            this.txtBTS.Location = new System.Drawing.Point(82, 243);
            this.txtBTS.Name = "txtBTS";
            this.txtBTS.ReadOnly = true;
            this.txtBTS.Size = new System.Drawing.Size(93, 21);
            this.txtBTS.TabIndex = 7;
            // 
            // txtDesc
            // 
            this.txtDesc.BackColor = System.Drawing.SystemColors.Window;
            this.txtDesc.Location = new System.Drawing.Point(82, 211);
            this.txtDesc.Name = "txtDesc";
            this.txtDesc.ReadOnly = true;
            this.txtDesc.Size = new System.Drawing.Size(257, 21);
            this.txtDesc.TabIndex = 7;
            // 
            // txtDirection
            // 
            this.txtDirection.BackColor = System.Drawing.SystemColors.Window;
            this.txtDirection.Location = new System.Drawing.Point(82, 180);
            this.txtDirection.Name = "txtDirection";
            this.txtDirection.ReadOnly = true;
            this.txtDirection.Size = new System.Drawing.Size(93, 21);
            this.txtDirection.TabIndex = 7;
            // 
            // txtPCI
            // 
            this.txtPCI.BackColor = System.Drawing.SystemColors.Window;
            this.txtPCI.Location = new System.Drawing.Point(82, 148);
            this.txtPCI.Name = "txtPCI";
            this.txtPCI.ReadOnly = true;
            this.txtPCI.Size = new System.Drawing.Size(93, 21);
            this.txtPCI.TabIndex = 7;
            // 
            // txtTAC
            // 
            this.txtTAC.BackColor = System.Drawing.SystemColors.Window;
            this.txtTAC.Location = new System.Drawing.Point(82, 117);
            this.txtTAC.Name = "txtTAC";
            this.txtTAC.ReadOnly = true;
            this.txtTAC.Size = new System.Drawing.Size(93, 21);
            this.txtTAC.TabIndex = 7;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new System.Drawing.Point(40, 247);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(29, 12);
            label6.TabIndex = 6;
            label6.Text = "&BTS:";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new System.Drawing.Point(34, 215);
            label7.Name = "label7";
            label7.Size = new System.Drawing.Size(35, 12);
            label7.TabIndex = 6;
            label7.Text = "&描述:";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new System.Drawing.Point(22, 183);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(47, 12);
            label5.TabIndex = 6;
            label5.Text = "&方向角:";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new System.Drawing.Point(190, 183);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(47, 12);
            label4.TabIndex = 6;
            label4.Text = "&扇区号:";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new System.Drawing.Point(40, 152);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(29, 12);
            label2.TabIndex = 6;
            label2.Text = "&PCI:";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(40, 120);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(29, 12);
            label1.TabIndex = 6;
            label1.Text = "&TAC:";
            // 
            // txtSectorID
            // 
            this.txtSectorID.BackColor = System.Drawing.SystemColors.Window;
            this.txtSectorID.Location = new System.Drawing.Point(246, 180);
            this.txtSectorID.Name = "txtSectorID";
            this.txtSectorID.ReadOnly = true;
            this.txtSectorID.Size = new System.Drawing.Size(93, 21);
            this.txtSectorID.TabIndex = 9;
            // 
            // txtEarfcn
            // 
            this.txtEarfcn.BackColor = System.Drawing.SystemColors.Window;
            this.txtEarfcn.Location = new System.Drawing.Point(246, 148);
            this.txtEarfcn.Name = "txtEarfcn";
            this.txtEarfcn.ReadOnly = true;
            this.txtEarfcn.Size = new System.Drawing.Size(93, 21);
            this.txtEarfcn.TabIndex = 9;
            // 
            // txtECI
            // 
            this.txtECI.BackColor = System.Drawing.SystemColors.Window;
            this.txtECI.Location = new System.Drawing.Point(246, 117);
            this.txtECI.Name = "txtECI";
            this.txtECI.ReadOnly = true;
            this.txtECI.Size = new System.Drawing.Size(93, 21);
            this.txtECI.TabIndex = 9;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new System.Drawing.Point(196, 151);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(41, 12);
            label3.TabIndex = 8;
            label3.Text = "&ARFCN:";
            // 
            // labelCI
            // 
            labelCI.AutoSize = true;
            labelCI.Location = new System.Drawing.Point(208, 120);
            labelCI.Name = "labelCI";
            labelCI.Size = new System.Drawing.Size(29, 12);
            labelCI.TabIndex = 8;
            labelCI.Text = "&NCI:";
            // 
            // txtLat
            // 
            this.txtLat.BackColor = System.Drawing.SystemColors.Window;
            this.txtLat.Location = new System.Drawing.Point(246, 85);
            this.txtLat.Name = "txtLat";
            this.txtLat.ReadOnly = true;
            this.txtLat.Size = new System.Drawing.Size(93, 21);
            this.txtLat.TabIndex = 1;
            // 
            // txtLng
            // 
            this.txtLng.BackColor = System.Drawing.SystemColors.Window;
            this.txtLng.Location = new System.Drawing.Point(82, 85);
            this.txtLng.Name = "txtLng";
            this.txtLng.ReadOnly = true;
            this.txtLng.Size = new System.Drawing.Size(93, 21);
            this.txtLng.TabIndex = 1;
            // 
            // txtID
            // 
            this.txtID.BackColor = System.Drawing.SystemColors.Window;
            this.txtID.Location = new System.Drawing.Point(82, 54);
            this.txtID.Name = "txtID";
            this.txtID.ReadOnly = true;
            this.txtID.Size = new System.Drawing.Size(93, 21);
            this.txtID.TabIndex = 1;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new System.Drawing.Point(34, 92);
            label11.Name = "label11";
            label11.Size = new System.Drawing.Size(35, 12);
            label11.TabIndex = 0;
            label11.Text = "&经度:";
            // 
            // labelID
            // 
            labelID.AutoSize = true;
            labelID.Location = new System.Drawing.Point(46, 57);
            labelID.Name = "labelID";
            labelID.Size = new System.Drawing.Size(23, 12);
            labelID.TabIndex = 0;
            labelID.Text = "&ID:";
            // 
            // NRCellInfoForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(385, 307);
            this.Controls.Add(groupBox1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.Name = "NRCellInfoForm";
            this.Text = "NR小区信息";
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TextBox txtCode;
        private System.Windows.Forms.TextBox txtName;
        private System.Windows.Forms.TextBox txtTAC;
        private System.Windows.Forms.TextBox txtECI;
        private System.Windows.Forms.TextBox txtID;
        private System.Windows.Forms.TextBox txtPCI;
        private System.Windows.Forms.TextBox txtSectorID;
        private System.Windows.Forms.TextBox txtEarfcn;
        private System.Windows.Forms.TextBox txtDirection;
        private DevExpress.XtraEditors.SimpleButton btnBTSInfo;
        private System.Windows.Forms.TextBox txtBTS;
        private System.Windows.Forms.TextBox txtDesc;
        private System.Windows.Forms.TextBox txtLat;
        private System.Windows.Forms.TextBox txtLng;
    }
}