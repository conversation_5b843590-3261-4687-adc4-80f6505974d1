﻿using DevExpress.XtraGrid.Views.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTSINR.LastWeakRoad.GridCompare;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRWeakSinrRoadGridForm : MinCloseForm
    {
        public NRWeakSinrRoadGridForm()
                : base(Model.MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        NRWeakSinrRoadGridLayer layer = null;
        public void FillData(List<NRWeakSinrRoadGrid> period1Grids, List<NRWeakSinrRoadGrid> period2Grids, List<NRWeakSinrRoadGrid> repeatGrids)
        {
            gridControlP1Grid.DataSource = period1Grids;
            gridControlP1Grid.RefreshDataSource();
            gridControlRepeatGrid.DataSource = repeatGrids;
            gridControlRepeatGrid.RefreshDataSource();
            gridControlP2Grid.DataSource = period2Grids;
            gridControlP2Grid.RefreshDataSource();

            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf == null)
            {
                return;
            }
            MainModel.MainForm.RefreshLegend();
            MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(NRWeakSinrRoadGridLayer));
            layer = clayer as NRWeakSinrRoadGridLayer;
            layer.Peroid1WeakGrids = period1Grids;
            layer.Peroid2Grids = period2Grids;
            layer.Invalidate();
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = sender as GridView;
            if (gv == null)
            {
                return;
            }
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gv.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
            if (mf == null)
            {
                return;
            }
            NRWeakSinrRoadGrid grid = gv.GetRow(info.RowHandle) as NRWeakSinrRoadGrid;
            if (grid == null)
            {
                return;
            }
            MainModel.GetInstance().ClearDTData();
            foreach (TestPoint tp in grid.TestPoints)
            {
                MainModel.GetInstance().DTDataManager.Add(tp);
            }
            if (grid.IntersectSegs != null)
            {
                foreach (NRWeakSinrRoadGrid interGrid in grid.IntersectSegs)
                {
                    foreach (TestPoint testPoint in interGrid.TestPoints)
                    {
                        MainModel.GetInstance().DTDataManager.Add(testPoint);
                    }
                }
            }
            MainModel.GetInstance().FireDTDataChanged(this);
            MainModel.FireSetDefaultMapSerialTheme("NR:SS_SINR");
        }

        private void checkPeriod1_CheckedChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.ShowPeriod1Grid = checkPeriod1.Checked;
                layer.Invalidate();
            }
        }

        private void checkNew_CheckedChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.ShowPeriod2NewGrid = checkNew.Checked;
                layer.Invalidate();
            }
        }

        private void checkRepeat_CheckedChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.ShowPeriod2RepeatGrid = checkRepeat.Checked;
                layer.Invalidate();
            }
        }

        private void colorPeriod1_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period1GridColor = colorPeriod1.Color;
                layer.Invalidate();
            }
        }

        private void colorNew_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period2NewGridColor = colorNew.Color;
                layer.Invalidate();
            }
        }

        private void colorRepeat_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period2RepeatGridColor = colorRepeat.Color;
                layer.Invalidate();
            }
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView2);
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView3);
        }
    }
}
