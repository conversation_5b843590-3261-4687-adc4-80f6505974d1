﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakSinrSampleRoadForm : MinCloseForm
    {
        public string themeName { get; set; } = "";
        public WeakSinrSampleRoadForm()
        {
            InitializeComponent();
            this.gridView1.DoubleClick += gv_DoubleClick;
            this.miExpandAll.Click += MiExpandAll_Click;
            this.miCollapseAll.Click += MiCollapseAll_Click;
        }

        public void FillData(List<WeakSinrSampleRoad> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            gv.BestFitColumns();
            MainModel.ClearDTData();
            foreach (WeakSINRRoad road in list)
            {
                foreach (TestPoint tp in road.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            themeName = "TD_LTE_SINR";
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            MainModel.FireDTDataChanged(this);
        }

        public void FillData(List<WeakSinrSampleRoad_LteFdd> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            gv.BestFitColumns();
            MainModel.ClearDTData();
            foreach (WeakSINRRoad_LteFdd road in list)
            {
                foreach (TestPoint tp in road.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            themeName = "LTE_FDD:SINR";
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            MainModel.FireDTDataChanged(this);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            if (themeName == "TD_LTE_SINR")
            {
                setLteClick(sender);
            }
            else
            {
                //***************************LteFdd******************
                setLteFddClick(sender);
            }
        }

        private void setLteClick(object sender)
        {
            DevExpress.XtraGrid.Views.Grid.GridView curGv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object row = curGv.GetRow(curGv.GetSelectedRows()[0]);
            if (row is WeakSinrSampleRoad)
            {
                WeakSinrSampleRoad road = row as WeakSinrSampleRoad;
                if (road != null)
                {
                    MainModel.DTDataManager.Clear();
                    foreach (TestPoint tp in road.TestPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    MainModel.FireDTDataChanged(this);
                    TestPoint midTp = road.TestPoints[road.TestPoints.Count / 2];
                    MainModel.MainForm.GetMapForm().GoToView(midTp.Longitude, midTp.Latitude, 6000);
                }
            }
            else if (row is WeakSinrSampleInfo)
            {
                WeakSinrSampleInfo sample = row as WeakSinrSampleInfo;
                if (sample != null)
                {
                    MainModel.SelectedTestPoints.Clear();
                    MainModel.SelectedTestPoints.Add(sample.TestPoint);
                    MainModel.FireSelectedTestPointsChanged(this);
                }
            }
        }

        private void setLteFddClick(object sender)
        {
            DevExpress.XtraGrid.Views.Grid.GridView curGv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object row = curGv.GetRow(curGv.GetSelectedRows()[0]);
            if (row is WeakSinrSampleRoad_LteFdd)
            {
                WeakSinrSampleRoad_LteFdd road = row as WeakSinrSampleRoad_LteFdd;
                if (road != null)
                {
                    MainModel.DTDataManager.Clear();
                    foreach (TestPoint tp in road.TestPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    MainModel.FireDTDataChanged(this);
                    TestPoint midTp = road.TestPoints[road.TestPoints.Count / 2];
                    MainModel.MainForm.GetMapForm().GoToView(midTp.Longitude, midTp.Latitude, 6000);
                }
            }
            else if (row is WeakSinrSampleInfo_LteFdd)
            {
                WeakSinrSampleInfo_LteFdd sample = row as WeakSinrSampleInfo_LteFdd;
                if (sample != null)
                {
                    MainModel.SelectedTestPoints.Clear();
                    MainModel.SelectedTestPoints.Add(sample.TestPoint);
                    MainModel.FireSelectedTestPointsChanged(this);
                }
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            addHeader(rows);
            if (themeName == "TD_LTE_SINR")
            {
                addExcelContent(rows);
            }
            else
            {
                addFddExcelContent(rows);
            }
           
            ExcelNPOIManager.ExportToExcel(rows);
        }
   
        private void addHeader(List<NPOIRow> rows)
        {
            NPOIRow row = new NPOIRow();
            row.AddCellValue("序号");
            row.AddCellValue("道路");
            row.AddCellValue("持续距离（米）");
            row.AddCellValue("持续时间（秒）");
            row.AddCellValue("采样点个数");
            row.AddCellValue("最大SINR");
            row.AddCellValue("最小SINR");
            row.AddCellValue("平均SINR");
            row.AddCellValue("最大RSRP");
            row.AddCellValue("最小RSRP");
            row.AddCellValue("平均RSRP");
            row.AddCellValue("中心经度");
            row.AddCellValue("中心纬度");
            row.AddCellValue("文件名");

            row.AddCellValue("Time");
            row.AddCellValue("Longitude");
            row.AddCellValue("Latitude");
            row.AddCellValue("SCell ID");
            row.AddCellValue("SCell Name");
            row.AddCellValue("TAC");
            row.AddCellValue("ECI");
            row.AddCellValue("EARFCN");
            row.AddCellValue("PCI");
            row.AddCellValue("RSRP");
            row.AddCellValue("RSRQ");
            row.AddCellValue("SINR");
            row.AddCellValue("Pathloss");
            row.AddCellValue("物理层吞吐率");
            row.AddCellValue("MAC层吞吐率");
            row.AddCellValue("PDCP层吞吐率");
            row.AddCellValue("应用层速率");
            row.AddCellValue("CQI");
            row.AddCellValue("天线模式");
            row.AddCellValue("编码方式");
            row.AddCellValue("每帧调度PRB个数");
            row.AddCellValue("每秒调度帧数");
            row.AddCellValue("NCell EARFCN");
            row.AddCellValue("NCell PCI");
            row.AddCellValue("NCell RSRP");
            rows.Add(row);
        }

        private void addExcelContent(List<NPOIRow> rows)
        {
            List<WeakSinrSampleRoad> roadList = gridControl.DataSource as List<WeakSinrSampleRoad>;
            foreach (WeakSinrSampleRoad road in roadList)
            {
                NPOIRow row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(road.SN);
                row.AddCellValue(road.RoadName);
                row.AddCellValue(road.Distance);
                row.AddCellValue(road.Second);
                row.AddCellValue(road.TestPointCount);
                row.AddCellValue(road.MaxSINR);
                row.AddCellValue(road.MinSINR);
                row.AddCellValue(road.AvgSINR);
                row.AddCellValue(road.MaxRSRP);
                row.AddCellValue(road.MinRSRP);
                row.AddCellValue(road.AvgRSRP);
                row.AddCellValue(road.MidLng);
                row.AddCellValue(road.MidLat);
                row.AddCellValue(road.FileName);

                foreach (WeakSinrSampleInfo sample in road.SampleInfos)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(sample.TimeString);
                    subRow.AddCellValue(sample.Longitude);
                    subRow.AddCellValue(sample.Latitude);
                    subRow.AddCellValue(sample.CellID);
                    subRow.AddCellValue(sample.CellName);
                    subRow.AddCellValue(sample.Tac);
                    subRow.AddCellValue(sample.Eci);
                    subRow.AddCellValue(sample.Earfcn);
                    subRow.AddCellValue(sample.Pci);
                    subRow.AddCellValue(sample.Rsrp);
                    subRow.AddCellValue(sample.Rsrq);
                    subRow.AddCellValue(sample.Sinr);
                    subRow.AddCellValue(sample.Pathloss);
                    subRow.AddCellValue(sample.PhyThroughput);
                    subRow.AddCellValue(sample.MacThroughput);
                    subRow.AddCellValue(sample.PdcpThroughput);
                    subRow.AddCellValue(sample.FtpDlRate);
                    subRow.AddCellValue(sample.Cqi);
                    subRow.AddCellValue(sample.AntennaPattern);
                    subRow.AddCellValue(sample.CodingSchemeDesc);
                    subRow.AddCellValue(sample.PrbPerFrame);
                    subRow.AddCellValue(sample.FramePerSecond);
                    subRow.AddCellValue(sample.NEarfcnDesc);
                    subRow.AddCellValue(sample.NPciDesc);
                    subRow.AddCellValue(sample.NRsrpDesc);
                }
            }
        }

        private void addFddExcelContent(List<NPOIRow> rows)
        {
            List<WeakSinrSampleRoad_LteFdd> roadList = gridControl.DataSource as List<WeakSinrSampleRoad_LteFdd>;
            foreach (WeakSinrSampleRoad_LteFdd road in roadList)
            {
                NPOIRow row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(road.SN);
                row.AddCellValue(road.RoadName);
                row.AddCellValue(road.Distance);
                row.AddCellValue(road.Second);
                row.AddCellValue(road.TestPointCount);
                row.AddCellValue(road.MaxSINR);
                row.AddCellValue(road.MinSINR);
                row.AddCellValue(road.AvgSINR);
                row.AddCellValue(road.MaxRSRP);
                row.AddCellValue(road.MinRSRP);
                row.AddCellValue(road.AvgRSRP);
                row.AddCellValue(road.MidLng);
                row.AddCellValue(road.MidLat);
                row.AddCellValue(road.FileName);

                foreach (WeakSinrSampleInfo_LteFdd sample in road.SampleInfos)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(sample.TimeString);
                    subRow.AddCellValue(sample.Longitude);
                    subRow.AddCellValue(sample.Latitude);
                    subRow.AddCellValue(sample.CellID);
                    subRow.AddCellValue(sample.CellName);
                    subRow.AddCellValue(sample.Tac);
                    subRow.AddCellValue(sample.Eci);
                    subRow.AddCellValue(sample.Earfcn);
                    subRow.AddCellValue(sample.Pci);
                    subRow.AddCellValue(sample.Rsrp);
                    subRow.AddCellValue(sample.Rsrq);
                    subRow.AddCellValue(sample.Sinr);
                    subRow.AddCellValue(sample.Pathloss);
                    subRow.AddCellValue(sample.PhyThroughput);
                    subRow.AddCellValue(sample.MacThroughput);
                    subRow.AddCellValue(sample.PdcpThroughput);
                    subRow.AddCellValue(sample.FtpDlRate);
                    subRow.AddCellValue(sample.Cqi);
                    subRow.AddCellValue(sample.AntennaPattern);
                    subRow.AddCellValue(sample.CodingSchemeDesc);
                    subRow.AddCellValue(sample.PrbPerFrame);
                    subRow.AddCellValue(sample.FramePerSecond);
                    subRow.AddCellValue(sample.NEarfcnDesc);
                    subRow.AddCellValue(sample.NPciDesc);
                    subRow.AddCellValue(sample.NRsrpDesc);
                }
            }
        }

        private void MiExpandAll_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gv.RowCount; ++i)
            {
                gv.ExpandMasterRow(i);
            }
        }

        private void MiCollapseAll_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gv.RowCount; ++i)
            {
                gv.CollapseMasterRow(i);
            }
        }
    }
}
