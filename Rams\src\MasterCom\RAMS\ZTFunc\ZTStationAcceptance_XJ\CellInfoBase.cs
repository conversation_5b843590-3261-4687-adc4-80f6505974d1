﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class CellInfoBase
    {
        public CellParameters CellBaseInfo { get; set; }

        public ICell Cell { get; set; }

        public CellInfoBase(ICell cell)
        {
            Cell = cell;
        }

        public virtual void Calculate()
        {
        }

        public virtual void AddTp(TestPoint tp)
        { 
        
        }
    }

    public class CellParameters
    {

    }
}
