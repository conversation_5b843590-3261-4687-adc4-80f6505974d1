﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.Util.GDI.LineChart;

namespace MasterCom.Util.GDI
{
    public static class GDIHelper
    {
        static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// 基于采样点绘制线形图,纵坐标是double类型采样点指标,横坐标是时间
        /// </summary>
        public static string DrawTestPointChartLine(DTFileDataManager fileManager, ChartLineCondition cond, Dictionary<DateTime, string> xDic, Dictionary<double, string> yDic)
        {
            var xValue = new DateTimeValue(SataType.ByTotal
                , DateTimeValue.DateTimeType.Second, cond.Width, cond.XMaxFieldCount, xDic);
            xValue.Init();
            var XLine = new CoordinateAxis<DateTime>(xValue, cond.XUnit);
            XLine.IsDrawOigin = true;

            var yValue = new DoubleValue(SataType.ByTotal, cond.Height
                , cond.YMaxFieldCount, yDic);
            yValue.Init();
            var YLine = new CoordinateAxis<double?>(yValue, cond.YUnit);

            var LineDataInfoList = new List<LineDataInfo<DateTime, double?>>();
            var info = new LineDataInfo<DateTime, double?>();
            LineDataInfoList.Add(info);
            List<string> datalist = new List<string>();

            try
            {
                foreach (var tp in fileManager.TestPoints)
                {
                    var data = (double?)tp[cond.ParamName];
                    if (data != null && !datalist.Contains(data.ToString()))
                    {
                        info.ValueDic[tp.DateTime] = data;
                        datalist.Add(data.ToString());
                    }
                }

                var line = new ChartLine<DateTime, double?>(XLine, YLine, LineDataInfoList);

                line.SetDrawParaDesc(true, cond.ParamName);
                line.DrawLineChart(cond.FilePath);
                return cond.FilePath;
            }
            catch (Exception e)
            {
                log.Error(e.Message + e.StackTrace);
                return "";
            }
        }

        public class ChartLineCondition
        {
            public ChartLineCondition(string paramName, string filePath, string xUnit, string yUnit
                , int xMaxFieldCount, int yMaxFieldCount)
            {
                ParamName = paramName;
                FilePath = filePath;
                XUnit = xUnit;
                YUnit = yUnit;
                XMaxFieldCount = xMaxFieldCount;
                YMaxFieldCount = yMaxFieldCount;
            }

            public string ParamName { get; protected set; }
            public string FilePath { get; protected set; }
            public string XUnit { get; protected set; }
            public string YUnit { get; protected set; }
            public int XMaxFieldCount { get; protected set; }
            public int YMaxFieldCount { get; protected set; }
            public int Height { get; set; } = 500;
            public int Width { get; set; } = 700;
        }
    }
}
