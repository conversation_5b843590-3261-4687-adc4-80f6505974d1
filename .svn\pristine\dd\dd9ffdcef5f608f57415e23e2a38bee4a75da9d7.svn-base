using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.Model
{
    public enum DTParameterValueType
    {
        Byte = 1,
        Short = 2,
        UShort = 3,
        Int = 4,
        Float = 5,
        Double = 6,
        String = 7,
        Long = 8,
        Int64 = 9,
        UInt64
    }

    public class DTParameterInfo
    {
        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(DTParameterInfo).Name))
            {
                DTParameterInfo info = new DTParameterInfo();
                info.FillItemValue(configFile, item);
                return info;
            }
            return null;
        }

        public static XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is DTParameterInfo)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                (value as DTParameterInfo).FillItem(configFile, item);
                return item;
            }
            return null;
        }

        private DTParameterInfo()
        {
        }

        public DTParameterInfo(int key, String name, int arrayBounds, DTParameterValueType valueType)
        {
            //Key = key;
            Name = name;
            ArrayBounds = arrayBounds > 1 ? arrayBounds : 1;
            ValueType = valueType;
        }

        private void FillItemValue(XmlConfigFile configFile, XmlElement item)
        {
            if (item != null)
            {
                object value = null;
                //value = configFile.GetItemValue(item, "Key");
                //if (value != null)
                //{
                //    Key = (int)value;
                //}
                Name = configFile.GetItemValue(item, "Name") as string;
                value = configFile.GetItemValue(item, "ValueType");
                if (value != null)
                {
                    ValueType = (DTParameterValueType)(int)value;
                }
                value = configFile.GetItemValue(item, "ArrayBounds");
                if (value != null)
                {
                    ArrayBounds = (int)value;
                }
            }
        }

        private void FillItem(XmlConfigFile configFile, XmlElement item)
        {
            configFile.AddItem(item, "Key", key);
            configFile.AddItem(item, "Name", Name);
            configFile.AddItem(item, "ValueType", (int)ValueType);
            configFile.AddItem(item, "ArrayBounds", ArrayBounds);
        }
       
        //public int Key
        //{
        //    get { return key; }
        //    set { key = value; }
        //}

        public String Name { get; set; }

        public int ArrayBounds
        {
            get { return parameters.Count; }
            set
            {
                if (value > ArrayBounds)
                {
                    for (int index = ArrayBounds; index < value; index++)
                    {
                        parameters.Add(new DTParameter(this, index));
                    }
                }
            }
        }

        public DTParameterValueType ValueType { get; set; }

        public DTParameter this[int index]
        {
            get
            {
                if (index >= ArrayBounds)
                {
                    index = ArrayBounds - 1;
                }
                else if (index < 0)
                {
                    index = 0;
                }
                return parameters[index];
            }
        }

        private int key { get; set; } = 0;

        private readonly List<DTParameter> parameters = new List<DTParameter>();
    }
}
