﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    class ReasonsBackCover : ReasonBase
    {
        public ReasonsBackCover()
        {
            this.Name = "背向覆盖";
        }
        
        public double DirectionDif { get; set; } = 60;
        public double Cell2TpDis { get; set; } = 100;

        public override bool IsValid(Model.TestPoint tp, params object[] resvParams)
        {
            LTECell mainCell = null;
            if (tp is ScanTestPoint_NBIOT)
            {
                mainCell = tp.GetCell_LTEScan(0);
            }
            else
            {
                mainCell = tp.GetMainLTECell_TdOrFdd();
            }
            if (mainCell == null || mainCell.Direction > 360)
            {
                return false;
            }
            if (!isValidAngle(mainCell, tp.Longitude, tp.Latitude))
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 采样点到小区的夹角是否属于正常角度
        /// </summary>
        private bool isValidAngle(LTECell cell, double longitude, double latitude)
        {
            double angleDiff = 0;
            double distance = cell.GetDistance(longitude, latitude);
            if (distance >= Cell2TpDis)
            {
                ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
                double angle;
                double ygap = cell.GetDistance(cell.Longitude, latitude);
                double angleV = Math.Acos(ygap / distance);
                if (longitude >= cell.Longitude && latitude >= cell.Latitude)//1象限
                {
                    angle = angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude >= cell.Latitude)//2象限
                {
                    angle = 360 - angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude <= cell.Latitude)//3象限
                {
                    angle = 180 + angleV * 180 / Math.PI;
                }
                else//4象限
                {
                    angle = 180 - angleV * 180 / Math.PI;
                }

                angleDiff = Math.Abs(angle - cell.Direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
                if (angleDiff >= DirectionDif)
                {
                    return false;
                }
            }
            return true;
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                param["DirectionDif"] = this.DirectionDif;
                param["Cell2TpDis"] = this.Cell2TpDis;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("Enable"))
                {
                    this.Enable = (bool)param["Enable"];
                }
                if (param.ContainsKey("DirectionDif"))
                {
                    this.DirectionDif = (double)param["DirectionDif"];
                }
                if (param.ContainsKey("Cell2TpDis"))
                {
                    this.Cell2TpDis = (double)param["Cell2TpDis"];
                }
            }
        }
    }
}
