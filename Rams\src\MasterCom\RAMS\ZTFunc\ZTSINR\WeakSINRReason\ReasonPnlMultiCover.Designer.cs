﻿namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    partial class ReasonPnlMultiCover
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.numMinNum = new DevExpress.XtraEditors.SpinEdit();
            this.numRSRPMin = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numDiffMax = new DevExpress.XtraEditors.SpinEdit();
            this.label4 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.grp)).BeginInit();
            this.grp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffMax.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grp
            // 
            this.grp.Controls.Add(this.numRSRPMin);
            this.grp.Controls.Add(this.numDiffMax);
            this.grp.Controls.Add(this.numMinNum);
            this.grp.Controls.Add(this.label5);
            this.grp.Controls.Add(this.label2);
            this.grp.Controls.Add(this.label4);
            this.grp.Controls.Add(this.label3);
            this.grp.Controls.Add(this.label1);
            this.grp.Size = new System.Drawing.Size(660, 70);
            this.grp.Text = "重叠覆盖";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(36, 38);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "RSRP≥";
            // 
            // numMinNum
            // 
            this.numMinNum.EditValue = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numMinNum.Location = new System.Drawing.Point(513, 33);
            this.numMinNum.Name = "numMinNum";
            this.numMinNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinNum.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numMinNum.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMinNum.Size = new System.Drawing.Size(75, 21);
            this.numMinNum.TabIndex = 1;
            // 
            // numRSRPMin
            // 
            this.numRSRPMin.EditValue = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            this.numRSRPMin.Location = new System.Drawing.Point(79, 33);
            this.numRSRPMin.Name = "numRSRPMin";
            this.numRSRPMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRSRPMin.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRSRPMin.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numRSRPMin.Size = new System.Drawing.Size(75, 21);
            this.numRSRPMin.TabIndex = 1;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(160, 38);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(29, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "并且";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(195, 38);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(137, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "最强RSRP与各RSRP差值≤";
            // 
            // numDiffMax
            // 
            this.numDiffMax.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numDiffMax.Location = new System.Drawing.Point(338, 33);
            this.numDiffMax.Name = "numDiffMax";
            this.numDiffMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDiffMax.Properties.MaxValue = new decimal(new int[] {
            166,
            0,
            0,
            0});
            this.numDiffMax.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numDiffMax.Size = new System.Drawing.Size(75, 21);
            this.numDiffMax.TabIndex = 1;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(419, 38);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(89, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "dB的小区数量≥";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(594, 38);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "个";
            // 
            // ReasonPnlMultiCover
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Name = "ReasonPnlMultiCover";
            this.Size = new System.Drawing.Size(660, 70);
            ((System.ComponentModel.ISupportInitialize)(this.grp)).EndInit();
            this.grp.ResumeLayout(false);
            this.grp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffMax.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit numMinNum;
        private DevExpress.XtraEditors.SpinEdit numRSRPMin;
        private DevExpress.XtraEditors.SpinEdit numDiffMax;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label5;
    }
}
