﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTTestPointBlock.Competition
{
    public class TestPointBlockReportManager
    {
        private static TestPointBlockReportManager instance = null;

        private TestPointBlockReportManager() { }

        public static TestPointBlockReportManager GetInstance()
        {
            if (instance == null)
            {
                instance = new TestPointBlockReportManager();
            }
            return instance;
        }

        private readonly string cfgFileName = string.Format(Application.StartupPath + @"/config/reports.tpb");//TestPoint Block
        public bool HasLoaded
        {
            get;
            set;
        }
        public void Load()
        {
            HasLoaded = true;
            Reports.Clear();
            if (!System.IO.File.Exists(cfgFileName))
            {
                return;
            }
            XmlConfigFile configFile = new XmlConfigFile(cfgFileName);
            configFile.GetItemValue("Main", "ReportMng", loadConfig);
        }

        private object loadConfig(XmlConfigFile configFile, XmlElement item, string itemName)
        {
            if (itemName == this.GetType().Name)
            {
                List<object> list = configFile.GetItemValue(item, "Reports", loadConfig) as List<object>;
                foreach (object value in list)
                {
                    if (value != null && value is TestPointBlockReport)
                    {
                        Reports.Add((TestPointBlockReport)value);
                    }
                }
                return this;
            }
            else if (itemName == typeof(TestPointBlockReport).Name)
            {
                TestPointBlockReport rpt = new TestPointBlockReport();
                rpt.LoadConfig(configFile, item, itemName);
                return rpt;
            }
            return null;
        }
       

        public void Save()
        {
            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement configMain = configFile.AddConfig("Main");
            configFile.AddItem(configMain, "ReportMng", this, saveConfig);
            configFile.Save(cfgFileName);
        }

        private XmlElement saveConfig(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is TestPointBlockReportManager)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Reports", Reports, saveConfig);
                return item;
            }
            else if (value is TestPointBlockReport)
            {
                return (value as TestPointBlockReport).SaveConfig(configFile, config, typeof(TestPointBlockReport).Name, value);
            }
            return null;

        }
        
        public List<TestPointBlockReport> Reports { get; set; } = new List<TestPointBlockReport>();
    }

    public class TestPointBlockReport
    {
        public string Name { get; set; } = "未命名";
        public override string ToString()
        {
            return Name;
        }
        public string BlockDesc
        {
            get { return string.Format("汇聚半径：{0}(米) 汇聚块采样点个数≥{1}", BlockRadius, MinTestPointCnt); }
        }
        public double BlockRadius { get; set; } = 100;
        public int MinTestPointCnt { get; set; } = 10;
        public DateTime HostBeginTime { get; set; } = DateTime.Now;
        public DateTime HostEndTime { get; set; } = DateTime.Now;
        public DateTime GuestBeginTime { get; set; } = DateTime.Now;
        public DateTime GuestEndTime { get; set; } = DateTime.Now;

        public int HostCarrierType 
        {get;set;}
        public int GuestCarrierType 
        {get;set;}
        public List<int> HostServiceTypeList { get; set; } = new List<int>();
        public List<int> GuestServiceTypeList { get; set; } = new List<int>();
        public List<int> HostProjIDList { get; set; } = new List<int>();
        public List<int> GuestProjIDList { get; set; } = new List<int>();
        public string HostDescription
        {
            get {
                string str = string.Format("运营商：{0}     ", Enum.GetName(typeof(CarrierType), HostCarrierType));
                str += HostTestPointCondition.Description;
                return str;
            }
        }
        public string GuestDescription
        {
            get
            {
                string str = string.Format("运营商：{0}", Enum.GetName(typeof(CarrierType), GuestCarrierType));
                str += GuestTestPointCondition.Description;
                return str;
            }
        }
        public TestPointConvergedCondition HostTestPointCondition { get; set; } = new TestPointConvergedCondition();
        public TestPointConvergedCondition GuestTestPointCondition { get; set; } = new TestPointConvergedCondition();
        public List<TPBlockDisplayColumn> HostColumns { get; set; } = new List<TPBlockDisplayColumn>();
        public List<TPBlockDisplayColumn> GuestColumns { get; set; } = new List<TPBlockDisplayColumn>();

        public List<DTParameter> GetNeedParam(bool isHost)
        {
            if (isHost)
            {
                return getNeedParams(HostTestPointCondition, HostColumns);
            }
            else
            {
                return getNeedParams(GuestTestPointCondition, GuestColumns);
            }
        }

        private List<DTParameter> getNeedParams(TestPointConvergedCondition codition, List<TPBlockDisplayColumn> cols)
        {
            List<DTParameter> paramSet = new List<DTParameter>();
            DTParameter p = null;
            foreach (TPConvergedDetailItem item in codition.DetailConditionSet)
            {
                p = DTDisplayParameterManager.GetInstance()[item.SysName, item.ParamName, item.ParamArrayIndex].Parameter;
                if (p != null && !paramSet.Contains(p))
                {
                    paramSet.Add(p);
                }
            }
            foreach (TPBlockDisplayColumn col in cols)
            {
                p = DTParameterManager.GetInstance().GetParameter(col.DisplayParam.ParamInfo.Name);
                if (p != null && !paramSet.Contains(p))
                {
                    paramSet.Add(p);
                }
            }
            return paramSet;
        }

        internal object LoadConfig(XmlConfigFile configFile, XmlElement item, string itemName)
        {
            if (itemName == this.GetType().Name)
            {
                return getCurConfig(configFile, item);
            }
            else if (itemName == typeof(TestPointConvergedCondition).Name)
            {
                TestPointConvergedCondition condition = new TestPointConvergedCondition();
                condition.LoadConfig(configFile, item, itemName);
                return condition;
            }
            else if (itemName == typeof(TPBlockDisplayColumn).Name)
            {
                TPBlockDisplayColumn col = new TPBlockDisplayColumn();
                col.LoadConfig(configFile, item, itemName);
                return col;
            }
            return this;
        }

        private object getCurConfig(XmlConfigFile configFile, XmlElement item)
        {
            this.Name = configFile.GetItemValue(item, "Name") as string;
            this.HostCarrierType = (int)configFile.GetItemValue(item, "HostCarrier");
            this.GuestCarrierType = (int)configFile.GetItemValue(item, "GuestCarrier");
            this.BlockRadius = (double)configFile.GetItemValue(item, "BlockRadius");
            this.MinTestPointCnt = (int)configFile.GetItemValue(item, "MinTestPointCount");
            List<object> list = configFile.GetItemValue(item, "HostServiceIDList") as List<object>;
            foreach (object value in list)
            {
                if (value != null)
                {
                    HostServiceTypeList.Add((int)value);
                }
            }
            list = configFile.GetItemValue(item, "GuestServiceIDList") as List<object>;
            foreach (object value in list)
            {
                if (value != null)
                {
                    this.GuestServiceTypeList.Add((int)value);
                }
            }
            this.HostTestPointCondition = configFile.GetItemValue(item, "HostConvergedCondition", LoadConfig) as TestPointConvergedCondition;
            this.GuestTestPointCondition = configFile.GetItemValue(item, "GuestConvergedCondition", LoadConfig) as TestPointConvergedCondition;
            list = configFile.GetItemValue(item, "HostDispalyCols", LoadConfig) as List<object>;
            foreach (object value in list)
            {
                if (value != null && value is TPBlockDisplayColumn)
                {
                    HostColumns.Add((TPBlockDisplayColumn)value);
                }
            }
            list = configFile.GetItemValue(item, "GuestDispalyCols", LoadConfig) as List<object>;
            foreach (object value in list)
            {
                if (value != null && value is TPBlockDisplayColumn)
                {
                    GuestColumns.Add((TPBlockDisplayColumn)value);
                }
            }
            return this;
        }

        internal XmlElement SaveConfig(XmlConfigFile configFile, XmlElement config, string p, object value)
        {
            if (value is TestPointBlockReport)
            {
                XmlElement item = configFile.AddItem(config, p, value.GetType());
                configFile.AddItem(item, "Name", this.Name);
                configFile.AddItem(item, "HostCarrier", this.HostCarrierType);
                configFile.AddItem(item, "GuestCarrier", this.GuestCarrierType);
                configFile.AddItem(item, "BlockRadius", this.BlockRadius);
                configFile.AddItem(item, "MinTestPointCount", this.MinTestPointCnt);
                configFile.AddItem(item, "HostServiceIDList", this.HostServiceTypeList);
                configFile.AddItem(item, "GuestServiceIDList", this.GuestServiceTypeList);
                configFile.AddItem(item, "HostConvergedCondition", this.HostTestPointCondition, SaveConfig);
                configFile.AddItem(item, "GuestConvergedCondition", this.GuestTestPointCondition, SaveConfig);
                configFile.AddItem(item, "HostDispalyCols", this.HostColumns, SaveConfig);
                configFile.AddItem(item, "GuestDispalyCols", this.GuestColumns, SaveConfig);
                return item;
            }
            else if (value is TestPointConvergedCondition)
            {
                return (value as TestPointConvergedCondition).SaveConfig(configFile, config, p, value);
            }
            else if (value is TPBlockDisplayColumn)
            {
                return (value as TPBlockDisplayColumn).SaveConfig(configFile, config, typeof(TPBlockDisplayColumn).Name, value);
            }
            return null;
        }

    }

}
