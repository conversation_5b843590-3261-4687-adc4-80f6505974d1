﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSQLThirdList_QH : DIYSQLBase
    {
        readonly string thirdName;
        public ZTSQLThirdList_QH(MainModel mainModel, string thirdName)
            : base(mainModel)
        {
            this.thirdName = thirdName;
        }

        private readonly List<string> thirdNameList = new List<string>();

        public List<string> GetReportEventThirdNameList()
        {
            return thirdNameList;
        }

        protected override string getSqlTextString()
        {
            return "exec mc_sp_qinghai_report_event_thirdlist_get '" + this.thirdName + "'";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int index = 0;
            E_VType[] rType = new E_VType[1];
            rType[index] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string name = "";
                    name = package.Content.GetParamString();
                    thirdNameList.Add(name);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
    }
}
