﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTLTEClusterAna
{
     public class ClusterResult
    {
        public ClusterResult(int row, int ClusterSN, double distance)
        {
            ColumnA = "簇编号";
            ColumnB = ClusterSN.ToString();
            ColumnC = "簇内平均站间距(M)";
            ColumnD = distance.ToString();
            ColumnE = "摸底测试";
            ColumnF = "优化后";
        }
        public ClusterResult(int row, double num1, double num2, bool ClusterType)
        {
            RowNum = row;
            if (ClusterType)
            {
                ColumnA = "密集城区";
            }
            else
            {
                ColumnA = "特殊场景";
            }

            switch(row)
            {
                case 2:
                    ColumnB = "网络覆盖指标";
                    ColumnC = "网络覆盖指标";
                    ColumnD = "网络覆盖指标";
                    ColumnE = "网络覆盖指标";
                    ColumnF = "网络覆盖指标";
                    break;
                case 3:
                    ColumnB = "空扰";
                    ColumnC = getColumnData(ClusterType, "RSRP>-95dBm", "RSRP>-103dBm");
                    ColumnD = ">90%";
                    ColumnE = num1.ToString() + "%";
                    ColumnF = num2.ToString() + "%";
                    break;
                case 4:
                    ColumnB = "空扰";
                    ColumnC = getColumnData(ClusterType, "SINR>8dB", "SINR>6dB");
                    ColumnD = ">90%";
                    ColumnE = num1.ToString() + "%";
                    ColumnF = num2.ToString() + "%";
                    break;
                case 5:
                    ColumnB = "空扰";
                    ColumnD = getColumnData(ClusterType, ">=15dB", ">=12dB");
                    ColumnC = "SINR均值(dB)";
                    ColumnE = num1.ToString();
                    ColumnF = num2.ToString();
                    break;
                case 6:
                    ColumnB = "加扰50%";
                    ColumnC = getColumnData(ClusterType, "RSRP>-95dBm", "RSRP>-103dBm");
                    ColumnD = ">90%";
                    ColumnE = num1.ToString() + "%";
                    ColumnF = num2.ToString() + "%";
                    break;
                case 7:
                    ColumnB = "加扰50%";
                    ColumnC = getColumnData(ClusterType, "SINR>1.5dB", "SINR>3dB");
                    ColumnD = ">90%";
                    ColumnE = num1.ToString() + "%";
                    ColumnF = num2.ToString() + "%";
                    break;
                case 8:
                    ColumnB = "加扰50%";
                    ColumnC = "SINR均值(dB)";
                    ColumnD = ">=10";
                    ColumnE = num1.ToString();
                    ColumnF = num2.ToString();
                    break;
                //case 9:
                //    ColumnB = "加扰50%";
                //    ColumnC = "道路重叠覆盖度";
                //    ColumnD = ">=10%";
                //    ColumnE = num1.ToString() + "%";
                //    ColumnF = num2.ToString() + "%";
                //    break;
                //case 10:
                //   ColumnB = "加扰50%";
                //   ColumnC = "LTE连续质差里程占比";
                //    ColumnD = "<3%";
                //    ColumnE = num1.ToString() + "%";
                //    ColumnF = num2.ToString() + "%";
                //    break;
                case 9:
                    ColumnB = "网络性能指标";
                    ColumnC = "网络性能指标";
                    ColumnD = "网络性能指标";
                    ColumnE = "网络性能指标";
                    ColumnF = "网络性能指标";
                    break;
                case 10:
                    ColumnB = "空扰";
                    ColumnC = "连接建立成功率";
                    ColumnD = ">=95%";
                    ColumnE = num1.ToString() + "%";
                    ColumnF = num2.ToString() + "%";
                    break;
                case 11:
                    ColumnB = "空扰";
                    ColumnC = "连接建立时延(ms)";
                    ColumnD = "<100ms";
                    ColumnE = num1.ToString();
                    ColumnF = num2.ToString();
                    break;
                case 12:
                    ColumnB = "空扰";
                    ColumnC = "掉线率";
                    ColumnD = "≤4%";
                    ColumnE = num1.ToString() + "%";
                    ColumnF = num2.ToString() + "%";
                    break;
                case 13:
                    ColumnB = "加扰50%";
                    ColumnC = "切换成功率";
                    ColumnD = "≥95%";
                    ColumnE = num1.ToString() + "%";
                    ColumnF = num2.ToString() + "%";
                    break;
                case 14:
                    ColumnB = "加扰50%";
                    ColumnC = "控制面切换时延(ms)";
                    ColumnD = "<100ms";
                    ColumnE = num1.ToString();
                    ColumnF = num2.ToString();
                    break;
                case 15:
                    ColumnB = "加扰50%";
                    ColumnC = "用户面切换时延(ms)";
                    ColumnD = "<85ms";
                    ColumnE = num1.ToString();
                    ColumnF = num2.ToString();
                    break;
                case 16:
                    ColumnB = "加扰50%";
                    ColumnC = "L2平均下行吞吐量(Mbps)";
                    ColumnD = ">30M";
                    ColumnE = num1.ToString();
                    ColumnF = num2.ToString();
                    break;
                case 17:
                    ColumnB = "加扰50%";
                    ColumnC = "L2平均上行吞吐量(Mbps)";
                    ColumnD = ">6M";
                    ColumnE = num1.ToString();
                    ColumnF = num2.ToString();
                    break;
                case 18:
                    ColumnB = "CSFB测试";
                    ColumnC = "CSFB回落成功率";
                    ColumnD = "93%";
                    ColumnE = num1.ToString() + "%";
                    ColumnF = num2.ToString() + "%";
                    break;
                case 19:
                    ColumnB = "CSFB测试";
                    ColumnC = "CSFB时延";
                    ColumnD = "12S";
                    ColumnE = num1.ToString();
                    ColumnF = num2.ToString();
                    break;


            }
        }

        private string getColumnData(bool ClusterType, string clusterStr, string otherStr)
        {
            if (ClusterType)
            {
                return clusterStr;
            }
            else
            {
                return otherStr;
            }
        }

        public ClusterResult(int row, string first, string second)
        {
            ColumnA = "场景";
            ColumnB = "加扰方式";
            ColumnC = "指标";
            ColumnD = "达标值";
            if (!string.IsNullOrEmpty(first) && first.Length == 8)
            {
                ColumnE = first.Substring(0, 4) + "年" + first.Substring(4,2) + "月" + first.Substring(6, 2) + "日";
            }
            if (!string.IsNullOrEmpty(second) && second.Length == 8)
            {
                ColumnF = second.Substring(0, 4) + "年" + second.Substring(4, 2) + "月" + second.Substring(6, 2) + "日";
            }
        }



        public string ColumnA
        { get; set; }
        public string ColumnB
        { get; set; }
        public string ColumnC
        { get; set; }
        public string ColumnD
        { get; set; }
        public string ColumnE
        { get; set; }
        public string ColumnF
        { get; set; }
        public int RowNum
        { get; set; }
    }
}
