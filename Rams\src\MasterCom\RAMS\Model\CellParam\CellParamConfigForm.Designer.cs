﻿namespace MasterCom.RAMS.Model
{
    partial class CellParamConfigForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel1 = new System.Windows.Forms.Panel();
            this.btnQuery = new System.Windows.Forms.Button();
            this.tbxTitle = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.tbxName = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.tbxCategory = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxConfigType = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnID = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnConfigType = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCategory = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTitle = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnUnit = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMinValue = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMaxValue = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDefaultValue = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRationalMinValue = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRationalMaxValue = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSuggestedMinValue = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSuggestedMaxValue = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnComment = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.btnQuery);
            this.panel1.Controls.Add(this.tbxTitle);
            this.panel1.Controls.Add(this.label4);
            this.panel1.Controls.Add(this.tbxName);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Controls.Add(this.tbxCategory);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Controls.Add(this.cbxConfigType);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(767, 44);
            this.panel1.TabIndex = 0;
            // 
            // btnQuery
            // 
            this.btnQuery.Location = new System.Drawing.Point(681, 11);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(75, 23);
            this.btnQuery.TabIndex = 8;
            this.btnQuery.Text = "查询";
            this.btnQuery.UseVisualStyleBackColor = true;
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // tbxTitle
            // 
            this.tbxTitle.Location = new System.Drawing.Point(535, 12);
            this.tbxTitle.Name = "tbxTitle";
            this.tbxTitle.Size = new System.Drawing.Size(111, 21);
            this.tbxTitle.TabIndex = 7;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(488, 16);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(41, 12);
            this.label4.TabIndex = 6;
            this.label4.Text = "中文名";
            // 
            // tbxName
            // 
            this.tbxName.Location = new System.Drawing.Point(369, 12);
            this.tbxName.Name = "tbxName";
            this.tbxName.Size = new System.Drawing.Size(111, 21);
            this.tbxName.TabIndex = 5;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(334, 16);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(29, 12);
            this.label3.TabIndex = 4;
            this.label3.Text = "名称";
            // 
            // tbxCategory
            // 
            this.tbxCategory.Location = new System.Drawing.Point(205, 12);
            this.tbxCategory.Name = "tbxCategory";
            this.tbxCategory.Size = new System.Drawing.Size(111, 21);
            this.tbxCategory.TabIndex = 3;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(170, 16);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(29, 12);
            this.label2.TabIndex = 2;
            this.label2.Text = "类别";
            // 
            // cbxConfigType
            // 
            this.cbxConfigType.FormattingEnabled = true;
            this.cbxConfigType.Location = new System.Drawing.Point(46, 12);
            this.cbxConfigType.Name = "cbxConfigType";
            this.cbxConfigType.Size = new System.Drawing.Size(111, 20);
            this.cbxConfigType.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(8, 16);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(29, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "类型";
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnID);
            this.objectListView.AllColumns.Add(this.olvColumnConfigType);
            this.objectListView.AllColumns.Add(this.olvColumnCategory);
            this.objectListView.AllColumns.Add(this.olvColumnName);
            this.objectListView.AllColumns.Add(this.olvColumnTitle);
            this.objectListView.AllColumns.Add(this.olvColumnUnit);
            this.objectListView.AllColumns.Add(this.olvColumnMinValue);
            this.objectListView.AllColumns.Add(this.olvColumnMaxValue);
            this.objectListView.AllColumns.Add(this.olvColumnDefaultValue);
            this.objectListView.AllColumns.Add(this.olvColumnRationalMinValue);
            this.objectListView.AllColumns.Add(this.olvColumnRationalMaxValue);
            this.objectListView.AllColumns.Add(this.olvColumnSuggestedMinValue);
            this.objectListView.AllColumns.Add(this.olvColumnSuggestedMaxValue);
            this.objectListView.AllColumns.Add(this.olvColumnComment);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnID,
            this.olvColumnConfigType,
            this.olvColumnCategory,
            this.olvColumnName,
            this.olvColumnTitle,
            this.olvColumnUnit,
            this.olvColumnMinValue,
            this.olvColumnMaxValue,
            this.olvColumnDefaultValue,
            this.olvColumnRationalMinValue,
            this.olvColumnRationalMaxValue,
            this.olvColumnSuggestedMinValue,
            this.olvColumnSuggestedMaxValue,
            this.olvColumnComment});
            this.objectListView.ContextMenuStrip = this.contextMenuStrip;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.HeaderWordWrap = true;
            this.objectListView.Location = new System.Drawing.Point(0, 44);
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(767, 408);
            this.objectListView.TabIndex = 1;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnID
            // 
            this.olvColumnID.HeaderFont = null;
            this.olvColumnID.Text = "序号";
            this.olvColumnID.Width = 40;
            // 
            // olvColumnConfigType
            // 
            this.olvColumnConfigType.HeaderFont = null;
            this.olvColumnConfigType.Text = "类型";
            this.olvColumnConfigType.Width = 80;
            // 
            // olvColumnCategory
            // 
            this.olvColumnCategory.HeaderFont = null;
            this.olvColumnCategory.Text = "类别";
            // 
            // olvColumnName
            // 
            this.olvColumnName.HeaderFont = null;
            this.olvColumnName.Text = "名称";
            // 
            // olvColumnTitle
            // 
            this.olvColumnTitle.HeaderFont = null;
            this.olvColumnTitle.Text = "中文名";
            // 
            // olvColumnUnit
            // 
            this.olvColumnUnit.HeaderFont = null;
            this.olvColumnUnit.Text = "单位";
            // 
            // olvColumnMinValue
            // 
            this.olvColumnMinValue.HeaderFont = null;
            this.olvColumnMinValue.Text = "取值范围_最小值";
            // 
            // olvColumnMaxValue
            // 
            this.olvColumnMaxValue.HeaderFont = null;
            this.olvColumnMaxValue.Text = "取值范围_最大值";
            // 
            // olvColumnDefaultValue
            // 
            this.olvColumnDefaultValue.HeaderFont = null;
            this.olvColumnDefaultValue.Text = "默认值";
            // 
            // olvColumnRationalMinValue
            // 
            this.olvColumnRationalMinValue.HeaderFont = null;
            this.olvColumnRationalMinValue.Text = "合理值范围_最小值";
            // 
            // olvColumnRationalMaxValue
            // 
            this.olvColumnRationalMaxValue.HeaderFont = null;
            this.olvColumnRationalMaxValue.Text = "合理值范围_最大值";
            // 
            // olvColumnSuggestedMinValue
            // 
            this.olvColumnSuggestedMinValue.HeaderFont = null;
            this.olvColumnSuggestedMinValue.Text = "建议值范围_最小值";
            // 
            // olvColumnSuggestedMaxValue
            // 
            this.olvColumnSuggestedMaxValue.HeaderFont = null;
            this.olvColumnSuggestedMaxValue.Text = "建议值范围_最大值";
            // 
            // olvColumnComment
            // 
            this.olvColumnComment.HeaderFont = null;
            this.olvColumnComment.Text = "备注";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(151, 26);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(150, 22);
            this.miExportToExcel.Text = "导出到Excel...";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // CellParamConfigForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(767, 452);
            this.Controls.Add(this.objectListView);
            this.Controls.Add(this.panel1);
            this.Name = "CellParamConfigForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "参数配置查询";
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.TextBox tbxTitle;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox tbxName;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox tbxCategory;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cbxConfigType;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnQuery;
        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnID;
        private BrightIdeasSoftware.OLVColumn olvColumnConfigType;
        private BrightIdeasSoftware.OLVColumn olvColumnCategory;
        private BrightIdeasSoftware.OLVColumn olvColumnName;
        private BrightIdeasSoftware.OLVColumn olvColumnTitle;
        private BrightIdeasSoftware.OLVColumn olvColumnUnit;
        private BrightIdeasSoftware.OLVColumn olvColumnMinValue;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxValue;
        private BrightIdeasSoftware.OLVColumn olvColumnDefaultValue;
        private BrightIdeasSoftware.OLVColumn olvColumnRationalMinValue;
        private BrightIdeasSoftware.OLVColumn olvColumnRationalMaxValue;
        private BrightIdeasSoftware.OLVColumn olvColumnSuggestedMinValue;
        private BrightIdeasSoftware.OLVColumn olvColumnSuggestedMaxValue;
        private BrightIdeasSoftware.OLVColumn olvColumnComment;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
    }
}