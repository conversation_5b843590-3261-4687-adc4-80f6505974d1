﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakCoverRoadLTEHighRailWayForm : MinCloseForm
    {
        public WeakCoverRoadLTEHighRailWayForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
        }

        public WeakCoverRoadCondition_LTE weakCondition { get; set; }
        public void FillData(List<WeakCoverRoadLTE> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            MainModel.ClearDTData();
            foreach (WeakCoverRoadLTE item in list)
            {
                foreach (TestPoint tp in item.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);   
                }
            }
            MainModel.FireDTDataChanged(this);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            WeakCoverRoadLTE weakCover = gv.GetFocusedRow() as WeakCoverRoadLTE;
            if (weakCover != null)
            {
                MainModel.ClearSelectedTestPoints();
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in weakCover.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(MainModel.MainForm);
                OutlineOfRoad outRoad = new OutlineOfRoad();
                outRoad.SetPoints(weakCover.TestPoints);
                TempLayer.Instance.Draw(outRoad.Drawer);
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            List<List<object>> exportList = GridViewTransfer.Transfer(this.gridControl);
            ExcelNPOIManager.ExportToExcel(exportList);
        }

    }
}
