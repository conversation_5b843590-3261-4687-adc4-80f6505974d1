﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public partial class OrderAreaTypeFilterDlg : BaseDialog
    {
        public OrderAreaTypeFilterDlg()
        {
            InitializeComponent();
            tvAreaType.Nodes.Clear();
            foreach (CategoryEnumItem item in mainModel.AreaManager.AreaTypes.Items)
            {
                if (item.ID < 1000)
                {
                    continue;
                }

                TreeNode node = new TreeNode(item.Name);
                node.Checked = item.ID != 1001 && item.ID != 1002 
                    && item.ID != 1003 && item.ID != 1004 && item.ID != 1009;
                node.Tag = item;
                tvAreaType.Nodes.Add(node);
            }
        }

        public List<CategoryEnumItem> AreaTypes
        {
            get {
                List<CategoryEnumItem> list = new List<CategoryEnumItem>();
                foreach (TreeNode node in tvAreaType.Nodes)
                {
                    if (node.Checked)
                    {
                        list.Add(node.Tag as CategoryEnumItem);
                    }
                }
                return list;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                foreach (TreeNode node in tvAreaType.Nodes)
                {
                    bool chk = false;
                    foreach (CategoryEnumItem item in value)
                    {
                        if (item.Name == node.Text)
                        {
                            chk = true;
                            break;
                        }
                    }
                    node.Checked = chk;
                }
            }
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            List<CategoryEnumItem> list = this.AreaTypes;
            if (list.Count == 0)
            {
                MessageBox.Show("请至少选一种区域类型进行统计！");
                return;
            }
            DialogResult = DialogResult.OK;
        }


    }
}
