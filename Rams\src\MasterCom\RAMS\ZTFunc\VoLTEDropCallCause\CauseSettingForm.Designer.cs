﻿namespace MasterCom.RAMS.ZTFunc.VoLTEDropCallCause
{
    partial class CauseSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.numRsrpSec = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.numRsrp = new System.Windows.Forms.NumericUpDown();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.numSinrSec = new System.Windows.Forms.NumericUpDown();
            this.numSinr = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label9 = new System.Windows.Forms.Label();
            this.numHoSec = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.numHoNum = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label13 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.numMultiPer = new System.Windows.Forms.NumericUpDown();
            this.numMultiSec = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.numMultiValue = new System.Windows.Forms.NumericUpDown();
            this.numMultiBand = new System.Windows.Forms.NumericUpDown();
            this.label12 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpSec)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSinrSec)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSinr)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numHoSec)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHoNum)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiPer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiSec)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiBand)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.numRsrpSec);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numRsrp);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(424, 52);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "弱覆盖";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(367, 24);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(23, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "dBm";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(176, 24);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(101, 12);
            this.label2.TabIndex = 2;
            this.label2.Text = "秒内，平均RSRP≤";
            // 
            // numRsrpSec
            // 
            this.numRsrpSec.Location = new System.Drawing.Point(95, 20);
            this.numRsrpSec.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numRsrpSec.Name = "numRsrpSec";
            this.numRsrpSec.Size = new System.Drawing.Size(75, 21);
            this.numRsrpSec.TabIndex = 0;
            this.numRsrpSec.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(46, 24);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "掉话前";
            // 
            // numRsrp
            // 
            this.numRsrp.Location = new System.Drawing.Point(286, 20);
            this.numRsrp.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRsrp.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numRsrp.Name = "numRsrp";
            this.numRsrp.Size = new System.Drawing.Size(75, 21);
            this.numRsrp.TabIndex = 1;
            this.numRsrp.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.numSinrSec);
            this.groupBox2.Controls.Add(this.numSinr);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Location = new System.Drawing.Point(12, 70);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(424, 52);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "质差";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(367, 25);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(17, 12);
            this.label6.TabIndex = 2;
            this.label6.Text = "dB";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(176, 25);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(101, 12);
            this.label5.TabIndex = 2;
            this.label5.Text = "秒内，平均SINR≤";
            // 
            // numSinrSec
            // 
            this.numSinrSec.Location = new System.Drawing.Point(95, 21);
            this.numSinrSec.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numSinrSec.Name = "numSinrSec";
            this.numSinrSec.Size = new System.Drawing.Size(75, 21);
            this.numSinrSec.TabIndex = 0;
            this.numSinrSec.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // numSinr
            // 
            this.numSinr.Location = new System.Drawing.Point(286, 21);
            this.numSinr.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numSinr.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numSinr.Name = "numSinr";
            this.numSinr.Size = new System.Drawing.Size(75, 21);
            this.numSinr.TabIndex = 1;
            this.numSinr.Value = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(46, 25);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(41, 12);
            this.label4.TabIndex = 2;
            this.label4.Text = "掉话前";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.numHoSec);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.numHoNum);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Location = new System.Drawing.Point(12, 212);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(424, 52);
            this.groupBox3.TabIndex = 3;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "频繁切换";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(367, 26);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(17, 12);
            this.label9.TabIndex = 2;
            this.label9.Text = "次";
            // 
            // numHoSec
            // 
            this.numHoSec.Location = new System.Drawing.Point(95, 18);
            this.numHoSec.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numHoSec.Name = "numHoSec";
            this.numHoSec.Size = new System.Drawing.Size(75, 21);
            this.numHoSec.TabIndex = 0;
            this.numHoSec.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(176, 22);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(101, 12);
            this.label8.TabIndex = 2;
            this.label8.Text = "秒内，切换次数≥";
            // 
            // numHoNum
            // 
            this.numHoNum.Location = new System.Drawing.Point(286, 20);
            this.numHoNum.Name = "numHoNum";
            this.numHoNum.Size = new System.Drawing.Size(75, 21);
            this.numHoNum.TabIndex = 1;
            this.numHoNum.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(46, 22);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(41, 12);
            this.label7.TabIndex = 2;
            this.label7.Text = "掉话前";
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(280, 280);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(361, 280);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.label13);
            this.groupBox4.Controls.Add(this.label14);
            this.groupBox4.Controls.Add(this.label10);
            this.groupBox4.Controls.Add(this.numMultiPer);
            this.groupBox4.Controls.Add(this.numMultiSec);
            this.groupBox4.Controls.Add(this.label11);
            this.groupBox4.Controls.Add(this.numMultiValue);
            this.groupBox4.Controls.Add(this.numMultiBand);
            this.groupBox4.Controls.Add(this.label12);
            this.groupBox4.Location = new System.Drawing.Point(12, 128);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(424, 78);
            this.groupBox4.TabIndex = 2;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "高重叠覆盖";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(178, 50);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(101, 12);
            this.label13.TabIndex = 2;
            this.label13.Text = "% 采样点覆盖度≥";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(367, 23);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(29, 12);
            this.label10.TabIndex = 2;
            this.label10.Text = "dB内";
            // 
            // numMultiPer
            // 
            this.numMultiPer.Location = new System.Drawing.Point(95, 45);
            this.numMultiPer.Name = "numMultiPer";
            this.numMultiPer.Size = new System.Drawing.Size(75, 21);
            this.numMultiPer.TabIndex = 2;
            this.numMultiPer.Value = new decimal(new int[] {
            70,
            0,
            0,
            0});
            // 
            // numMultiSec
            // 
            this.numMultiSec.Location = new System.Drawing.Point(95, 18);
            this.numMultiSec.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numMultiSec.Name = "numMultiSec";
            this.numMultiSec.Size = new System.Drawing.Size(75, 21);
            this.numMultiSec.TabIndex = 0;
            this.numMultiSec.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(189, 24);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(77, 12);
            this.label11.TabIndex = 2;
            this.label11.Text = "秒内，覆盖带";
            // 
            // numMultiValue
            // 
            this.numMultiValue.Location = new System.Drawing.Point(286, 45);
            this.numMultiValue.Name = "numMultiValue";
            this.numMultiValue.Size = new System.Drawing.Size(75, 21);
            this.numMultiValue.TabIndex = 3;
            this.numMultiValue.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // numMultiBand
            // 
            this.numMultiBand.Location = new System.Drawing.Point(286, 18);
            this.numMultiBand.Name = "numMultiBand";
            this.numMultiBand.Size = new System.Drawing.Size(75, 21);
            this.numMultiBand.TabIndex = 1;
            this.numMultiBand.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(46, 22);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(41, 12);
            this.label12.TabIndex = 2;
            this.label12.Text = "掉话前";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(58, 50);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(29, 12);
            this.label14.TabIndex = 2;
            this.label14.Text = "至少";
            // 
            // CauseSettingForm
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(448, 315);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Name = "CauseSettingForm";
            this.Text = "掉话原因分析设置";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpSec)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSinrSec)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSinr)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numHoSec)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHoNum)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiPer)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiSec)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiBand)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.NumericUpDown numRsrpSec;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numRsrp;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numSinrSec;
        private System.Windows.Forms.NumericUpDown numSinr;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown numHoSec;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numHoNum;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.NumericUpDown numMultiPer;
        private System.Windows.Forms.NumericUpDown numMultiSec;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown numMultiValue;
        private System.Windows.Forms.NumericUpDown numMultiBand;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label14;
    }
}