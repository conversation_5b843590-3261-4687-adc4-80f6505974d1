﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.ZTFunc.ZTLteTestAcceptance;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.BackgroundFunc
{
    class ExportOutdoorBtsReportHelper_QH : ExportOutdoorBtsReportBase
    {
        public static readonly string WorkDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/BtsAcceptTemplet_QH");
        protected static string getTargetFile(string btsName, int cellCount, string saveFolder)
        {
            if (cellCount > 6)
            {
                throw (new Exception(string.Format("基站{0}小区数超过6个，不支持报告导出", btsName)));
            }

            string templateFile = cellCount <= 3 ? "LTE新站验收模板_三扇区.xlsx" : "LTE新站验收模板_六扇区.xlsx";
            templateFile = Path.Combine(WorkDir, templateFile);

            string targetFile = string.Format("LTE新站验收_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile);
            return targetFile;
        }
        public static bool ExportReports(OutDoorBtsAcceptInfo_QH btsInfo, BtsWorkParam_QH btsWorkParamInfo)
        {
            if (MainModel.GetInstance().BackgroundStopRequest)
            {
                return false;
            }

            Excel.Application xlApp = null;
            try
            {
                StationAcceptAutoSet_QH funcSet = StationAcceptAna_QH.GetInstance().FuncSet;
                string folderPath = Path.Combine(funcSet.ReportSavePath, btsWorkParamInfo.DateDes);
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }
                string targetFile = getTargetFile(btsInfo.BtsName, btsInfo.LteBts.Cells.Count, folderPath);
                reportInfo(string.Format("开始导出 {0} 站点的单验报告", btsInfo.BtsName));

                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile);

                fillHomePage(eBook, btsWorkParamInfo, btsInfo);
                fillKpiPage(eBook, btsInfo);
                fillCoverPicPage(eBook, btsInfo);
                fillBtsPic(eBook, btsInfo, funcSet.OutdoorBtsPicFolderPath);

                BtsFusionInfo_QH btsFusionInfo = btsInfo.FusionInfo as BtsFusionInfo_QH;
                fillFusionKpiPage(eBook, btsFusionInfo);

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);

                StationAcceptAna_QH.GetInstance().ReportFilePaths.Add(targetFile);
                reportInfo(string.Format("成功导出 {0} 站点的单验报告。", btsInfo.BtsName));
                return true;
            }
            catch (Exception ex)
            {
                reportError(ex);
                return false;
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
                //GC.Collect();
            }
        }

        //填充报告首页-宏站验收记录单
        protected static void fillHomePage(Excel.Workbook eBook, BtsWorkParam_QH btsWorkParamInfo
            , OutDoorBtsAcceptInfo_QH btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.LteBts;
            Excel.Worksheet homePageSheet = (Excel.Worksheet)eBook.Sheets[1];
            string districtName = DistrictManager.GetInstance().getDistrictName(MainModel.GetInstance().DistrictID);

            #region 基站描述及基站参数
            homePageSheet.get_Range("e3").set_Value(Type.Missing, srcLteBts.Name);
            homePageSheet.get_Range("e5").set_Value(Type.Missing, srcLteBts.BTSID);
            homePageSheet.get_Range("z3").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));
            homePageSheet.get_Range("z5").set_Value(Type.Missing, districtName);
            homePageSheet.get_Range("z7").set_Value(Type.Missing, "室外");
            homePageSheet.get_Range("h13").set_Value(Type.Missing, srcLteBts.Longitude);
            homePageSheet.get_Range("h14").set_Value(Type.Missing, srcLteBts.Latitude);
            if (btsInfo.PhoneTestInfo != null)
            {
                homePageSheet.get_Range("n13").set_Value(Type.Missing, btsInfo.PhoneTestInfo.Longitude);
                homePageSheet.get_Range("n14").set_Value(Type.Missing, btsInfo.PhoneTestInfo.Latitude);
            }
            #endregion

            int cellIndex = 0;
            foreach (LTECell icell in srcLteBts.Cells)
            {
                #region 小区参数
                int cellParamColIndex = 8 + (cellIndex * 8);//小区参数列：首个小区所在列号为8，其他小区列号以8递增
                homePageSheet.Cells[20, cellParamColIndex] = icell.TAC;
                homePageSheet.Cells[21, cellParamColIndex] = icell.PCI;
                homePageSheet.Cells[22, cellParamColIndex] = icell.EARFCN;
                homePageSheet.Cells[20, cellParamColIndex + 3] = icell.TAC;
                homePageSheet.Cells[21, cellParamColIndex + 3] = icell.PCI;
                homePageSheet.Cells[22, cellParamColIndex + 3] = icell.EARFCN;
                homePageSheet.Cells[26, cellParamColIndex] = icell.Altitude;
                homePageSheet.Cells[27, cellParamColIndex] = icell.Direction;
                homePageSheet.Cells[28, cellParamColIndex] = icell.Downward;
                #endregion

                 OutDoorCellAcceptInfo_QH cellInfo;
                 if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfo))
                 {
                     #region 小区实测信息

                     if (cellInfo.PhoneTestInfo != null)
                     {
                         homePageSheet.Cells[26, cellParamColIndex + 3] = cellInfo.PhoneTestInfo.Altitude;
                         homePageSheet.Cells[27, cellParamColIndex + 3] = cellInfo.PhoneTestInfo.Direction;
                         homePageSheet.Cells[28, cellParamColIndex + 3] = cellInfo.PhoneTestInfo.Downward;
                     }
                     #endregion

                     #region 小区指标
                     int colIndex = 12 + (cellIndex * 5);
                     int kpiRowIndex = 34;
                     homePageSheet.Cells[kpiRowIndex++, colIndex] = cellInfo.FtpDlInfo.IsAccordDes;
                     homePageSheet.Cells[kpiRowIndex++, colIndex] = cellInfo.FtpUlInfo.IsAccordDes;
                     homePageSheet.Cells[kpiRowIndex++, colIndex] = cellInfo.RrcInfo.IsAccordDes;
                     homePageSheet.Cells[kpiRowIndex++, colIndex] = cellInfo.ErabInfo.IsAccordDes;
                     homePageSheet.Cells[kpiRowIndex, colIndex] = cellInfo.AccessInfo.IsAccordDes;
                     #endregion
                 }

                cellIndex++;
            }

            homePageSheet.get_Range("n41").set_Value(Type.Missing, btsInfo.HandOverInfo.IsAccordDes);
            homePageSheet.get_Range("n42").set_Value(Type.Missing, btsInfo.Is34G_ReselectAccordDes);
            homePageSheet.get_Range("n43").set_Value(Type.Missing, btsInfo.Is24G_ReselectAccordDes);
            homePageSheet.get_Range("n44").set_Value(Type.Missing, btsInfo.IsCsfbAccordDes);
            homePageSheet.get_Range("n45").set_Value(Type.Missing, btsInfo.IsSrvccAccordDes);
            homePageSheet.get_Range("n46").set_Value(Type.Missing, btsInfo.IsVolteAudioAccordDes);
            homePageSheet.get_Range("n47").set_Value(Type.Missing, btsInfo.IsVolteVideoAccordDes);

            homePageSheet.get_Range("n50").set_Value(Type.Missing, btsInfo.UltraSiteInfo.IsAccordDes);

            homePageSheet.get_Range("h62").set_Value(Type.Missing, btsInfo.IsAccordAcceptStr);//验收结论
            homePageSheet.get_Range("a65").set_Value(Type.Missing, btsInfo.NotAccordKpiDes);//未通过验收的原因
        }

        //填充报告第二页-性能验收测试表格
        protected static void fillKpiPage(Excel.Workbook eBook, OutDoorBtsAcceptInfo_QH btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.LteBts;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[2];
            kpiPageSheet.get_Range("c2").set_Value(Type.Missing, srcLteBts.Name);
            kpiPageSheet.get_Range("m2").set_Value(Type.Missing, srcLteBts.BTSID);

            int cellIndex = 0;
            int firstColIndex = 16;//尝试次数或FTP相关指标所在列
            int secondColIndex = 23;//成功次数所在列
            foreach (LTECell icell in srcLteBts.Cells)
            {
                OutDoorCellAcceptInfo_QH cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfo))
                {
                    int rowIndex = 5 + (cellIndex * 20);//小区RRC指标所在行：首个小区所在行号为5，其他小区行号以20递增
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.RrcInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.RrcInfo.ValidCount;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.ErabInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.ErabInfo.ValidCount;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.AccessInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.AccessInfo.ValidCount;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.Reselect34Info.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.Reselect34Info.ValidCount;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.Reselect24Info.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.Reselect24Info.ValidCount;
                    rowIndex+=2;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.CsfbInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.CsfbInfo.ValidCount;
                    kpiPageSheet.Cells[rowIndex, 44] = cellInfo.ReturnToLteTimeDelayAvg;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.CsfbReturnToLteInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.CsfbReturnToLteInfo.ValidCount;

                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.SrvccInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.SrvccInfo.ValidCount;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.VolteAudioInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.VolteAudioInfo.ValidCount;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.VolteVideoInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.VolteVideoInfo.ValidCount;

                    rowIndex += 2;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.FtpDlInfo.RsrpAvg;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.FtpDlInfo_Bad.RsrpAvg;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.FtpDlInfo.SinrAvg;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.FtpDlInfo_Bad.SinrAvg;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.FtpDlInfo.SpeedAvg;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.FtpDlInfo_Bad.SpeedAvg;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.FtpUlInfo.RsrpAvg;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.FtpUlInfo_Bad.RsrpAvg;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.FtpUlInfo.SinrAvg;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.FtpUlInfo_Bad.SinrAvg;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.FtpUlInfo.SpeedAvg;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.FtpUlInfo_Bad.SpeedAvg;
                }
                cellIndex++;
            }

            int handOverRowIndex;
            if (srcLteBts.Cells.Count > 3)
            {
                handOverRowIndex = 123;
            }
            else
            {
                handOverRowIndex = 64;
            }
            kpiPageSheet.Cells[handOverRowIndex, firstColIndex] = btsInfo.HandOverInfo.TotalCount;
            kpiPageSheet.Cells[handOverRowIndex, secondColIndex] = btsInfo.HandOverInfo.ValidCount;
        }

        //填充报告第三页-性能验收覆盖效果图
        protected static void fillCoverPicPage(Excel.Workbook eBook, OutDoorBtsAcceptInfo_QH btsInfo)
        {
            int rowIndex = 10;
            insertCoverPic(eBook, btsInfo.CoverPicPath_Handover, ref rowIndex);

            int cellIndex = 0;
            foreach (LTECell cell in btsInfo.LteBts.Cells)
            {
                OutDoorCellAcceptInfo_QH cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    rowIndex = 34 + cellIndex * 92;
                    insertCoverPic(eBook, cellInfo.CoverPicPath_Rsrp, ref rowIndex);
                    insertCoverPic(eBook, cellInfo.CoverPicPath_Sinr, ref rowIndex);
                    insertCoverPic(eBook, cellInfo.CoverPicPath_PdcpDL, ref rowIndex);
                    insertCoverPic(eBook, cellInfo.CoverPicPath_PdcpUL, ref rowIndex);
                }
                cellIndex++;
            }
        }
        protected static void insertCoverPic(Excel.Workbook eBook, string coverPicPath, ref int rowIndex)
        {
            if (File.Exists(coverPicPath))
            {
                string picCell = "a" + rowIndex;
                AcpAutoCoverPicture.InsertExcelPicture(eBook, picCell, coverPicPath);
            }

            rowIndex += 23;
        }

        //填充报告第四页-站点验收天面勘测报告
        protected static void fillBtsPic(Excel.Workbook eBook, OutDoorBtsAcceptInfo_QH btsInfo, string picFolderPath)
        {
            if (string.IsNullOrEmpty(picFolderPath))
            {
                return;
            }

            Excel.Worksheet btsPicPageSheet = (Excel.Worksheet)eBook.Sheets[4];
            if (btsInfo.PhoneTestInfo != null)
            {
                string picFolder_AllView = Path.Combine(picFolderPath, btsInfo.PhoneTestInfo.AllViewPic_FolderName);
                if (Directory.Exists(picFolder_AllView))
                {
                    insertBtsPicture(btsPicPageSheet, picFolder_AllView, 1, 4, btsInfo.PhoneTestInfo.AllViewPic_Buiding);
                    insertBtsPicture(btsPicPageSheet, picFolder_AllView, 3, 4, btsInfo.PhoneTestInfo.AllViewPic_Entry);
                    insertBtsPicture(btsPicPageSheet, picFolder_AllView, 1, 6, btsInfo.PhoneTestInfo.AllViewPic_Housetop);
                }
                string picFolder_Around = Path.Combine(picFolderPath, btsInfo.PhoneTestInfo.AllViewPic_FolderName);
                if (Directory.Exists(picFolder_Around))
                {
                    insertBtsPicture(btsPicPageSheet, picFolder_Around, 1, 9, btsInfo.PhoneTestInfo.AroundPic_0);
                    insertBtsPicture(btsPicPageSheet, picFolder_Around, 3, 9, btsInfo.PhoneTestInfo.AroundPic_45);
                    insertBtsPicture(btsPicPageSheet, picFolder_Around, 1, 11, btsInfo.PhoneTestInfo.AroundPic_90);
                    insertBtsPicture(btsPicPageSheet, picFolder_Around, 3, 11, btsInfo.PhoneTestInfo.AroundPic_135);
                    insertBtsPicture(btsPicPageSheet, picFolder_Around, 1, 13, btsInfo.PhoneTestInfo.AroundPic_180);
                    insertBtsPicture(btsPicPageSheet, picFolder_Around, 3, 13, btsInfo.PhoneTestInfo.AroundPic_225);
                    insertBtsPicture(btsPicPageSheet, picFolder_Around, 1, 15, btsInfo.PhoneTestInfo.AroundPic_270);
                    insertBtsPicture(btsPicPageSheet, picFolder_Around, 3, 15, btsInfo.PhoneTestInfo.AroundPic_315);
                }
            }

            int cellIndex = 0;
            int cellPicRowIndex = 18;
            int cellPicColIndex = 1;
            foreach (LTECell lteCell in btsInfo.LteBts.Cells)
            {
                if (cellIndex == 3)
                {
                    cellPicRowIndex = 20;
                    cellPicColIndex = 1;
                }
                OutDoorCellAcceptInfo_QH cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(lteCell.CellID, out cellInfo))
                {
                    setPicInfo(picFolderPath, btsPicPageSheet, cellPicRowIndex, cellPicColIndex, cellInfo);
                }
                cellPicColIndex += 2;
                cellIndex++;
            }
        }

        private static void setPicInfo(string picFolderPath, Excel.Worksheet btsPicPageSheet, int cellPicRowIndex, int cellPicColIndex, OutDoorCellAcceptInfo_QH cellInfo)
        {
            if (cellInfo.PhoneTestInfo != null)
            {
                string picFolder_CellTopPanel = Path.Combine(picFolderPath, cellInfo.PhoneTestInfo.TopPanel_FolderName);
                if (Directory.Exists(picFolder_CellTopPanel))
                {
                    insertBtsPicture(btsPicPageSheet, picFolder_CellTopPanel, cellPicColIndex
                        , cellPicRowIndex, cellInfo.PhoneTestInfo.TopPanel_PicName);
                }
            }
        }

        //填充报告第五页-站点后台指标监控
        protected static void fillFusionKpiPage(Excel.Workbook eBook, BtsFusionInfo_QH btsFusionInfo)
        {
            if (btsFusionInfo == null)
            {
                return;
            }
            Excel.Worksheet bgKpiPageSheet = (Excel.Worksheet)eBook.Sheets[5];

            int dateIndex = 0;
            int increaseRowCount = btsFusionInfo.BtsWorkParamInfo.CellWorkParams.Count > 3 ? 6 : 3;
            DateTime curDate = btsFusionInfo.BeginTime.Date;
            while (curDate <= btsFusionInfo.EndTime.Date)
            {
                string dateDes = BtsFusionInfoBase.GetDateKeyDes(curDate);
                int cellIndex = 0;
                foreach (CellWorkParamBase cellWorkParam in btsFusionInfo.BtsWorkParamInfo.CellWorkParams)
                {
                    int perfRowIndex = 4 + (increaseRowCount * dateIndex) + cellIndex;
                    bgKpiPageSheet.Cells[perfRowIndex, 1] = dateDes;
                    bgKpiPageSheet.Cells[perfRowIndex, 2] = cellWorkParam.CellName;

                    #region 性能数据
                    setFusionData(btsFusionInfo, bgKpiPageSheet, dateDes, cellWorkParam, perfRowIndex);
                    #endregion

                    #region MR数据
                    setMRData(btsFusionInfo, bgKpiPageSheet, dateDes, cellWorkParam, perfRowIndex);
                    #endregion

                    cellIndex++;
                }
                dateIndex++;
                curDate = curDate.AddDays(1);
            }

            #region 告警数据
            setAlarmData(btsFusionInfo, bgKpiPageSheet);
            #endregion
        }

        private static void setFusionData(BtsFusionInfo_QH btsFusionInfo, Excel.Worksheet bgKpiPageSheet, string dateDes, CellWorkParamBase cellWorkParam, int perfRowIndex)
        {
            Dictionary<string, CellPerfDataBase> date_cellPerfDataDic;
            if (btsFusionInfo.CellPerfInfoDic.TryGetValue(cellWorkParam.CGI, out date_cellPerfDataDic))
            {
                CellPerfDataBase cellPerfBase;
                if (date_cellPerfDataDic.TryGetValue(dateDes, out cellPerfBase))
                {
                    CellPerfData_QH cellPerfInfo = cellPerfBase as CellPerfData_QH;
                    if (cellPerfInfo != null)
                    {
                        int perfColIndex = 3;//列序号
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.RrcConnectTryCount);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.RrcSetupSuccessRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabConnectTryCount);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabSetupSuccessRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessConnectRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessDropRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabDropRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.InnerHandoverSuccessRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.PdcpThroughput_UL);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.PdcpThroughput_DL);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.PdcpThroughputSum);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessConnectRate_QCI1);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabDropRate_QCI1);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex, cellPerfInfo.EsrvccHandoverSuccessRate);
                    }
                }
            }
        }

        private static void setMRData(BtsFusionInfo_QH btsFusionInfo, Excel.Worksheet bgKpiPageSheet, string dateDes, CellWorkParamBase cellWorkParam, int perfRowIndex)
        {
            Dictionary<string, CellMRDataBase> date_CellMRDataDic;
            if (btsFusionInfo.CellMRInfoDic.TryGetValue(cellWorkParam.CGI, out date_CellMRDataDic))
            {
                CellMRDataBase cellMrInfo;
                if (date_CellMRDataDic.TryGetValue(dateDes, out cellMrInfo))
                {
                    setFloatValue(bgKpiPageSheet, perfRowIndex, 17, cellMrInfo.MrCoverRate);
                }
            }
        }

        private static void setAlarmData(BtsFusionInfo_QH btsFusionInfo, Excel.Worksheet bgKpiPageSheet)
        {
            int alarmRowIndex = 26;
            if (btsFusionInfo.BtsWorkParamInfo.CellWorkParams.Count > 3)
            {
                alarmRowIndex = 47;
            }
            if (btsFusionInfo.BtsAlarmInfoDic != null && btsFusionInfo.BtsAlarmInfoDic.Count > 0)
            {
                bgKpiPageSheet.Cells[alarmRowIndex, 2] = "有";
                alarmRowIndex += 2;
                foreach (BtsAlarmDataBase alarmData in btsFusionInfo.BtsAlarmInfoDic.Values)
                {
                    bgKpiPageSheet.Cells[alarmRowIndex, 1] = alarmData.AlarmTitle;
                    bgKpiPageSheet.Cells[alarmRowIndex, 2] = alarmData.BeginTimeStr;
                    bgKpiPageSheet.Cells[alarmRowIndex, 3] = alarmData.EndTimeStr;
                    bgKpiPageSheet.Cells[alarmRowIndex, 4] = alarmData.Desc;
                    alarmRowIndex++;
                }
            }
            else
            {
                bgKpiPageSheet.Cells[alarmRowIndex, 2] = "无";
            }
        }
    }

    class ExportIndoorBtsReportHelper_QH : ExportIndoorBtsReportBase
    {
        protected static string getTargetFile(string btsName, int sectorCount, string saveFolder)
        {
            if (sectorCount >= 9)
            {
                throw (new Exception(string.Format("基站{0}PCI小区超过9个，不支持报告导出", btsName)));
            }

            string templateFile = "LTE室分单站验证模板表.xlsx";
            templateFile = Path.Combine(ExportOutdoorBtsReportHelper_QH.WorkDir, templateFile);

            string targetFile = string.Format("LTE室分单站验证_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile);
            return targetFile;
        }
        public static bool ExportReports(InDoorBtsAcceptInfo_QH btsInfo, BtsWorkParam_QH btsWorkParamInfo)
        {
            if (MainModel.GetInstance().BackgroundStopRequest)
            {
                return false;
            }

            Excel.Application xlApp = null;
            try
            {
                StationAcceptAutoSet_QH funcSet = StationAcceptAna_QH.GetInstance().FuncSet;

                string folderPath = Path.Combine(funcSet.ReportSavePath, btsWorkParamInfo.DateDes);
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }
                string targetFile = getTargetFile(btsInfo.BtsName, btsInfo.LteBts.Cells.Count, folderPath);
                reportInfo(string.Format("开始导出 {0} 站点的单验报告", btsInfo.BtsName));

                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile);

                fillHomePage(eBook, btsInfo, btsWorkParamInfo, btsInfo.NotAccordKpiDes);
                fillKpiPage(eBook, btsInfo);
                fillLevelKpiPage(eBook, btsInfo);
                fillCoverPicPage(eBook, btsInfo, funcSet.IndoorCoverPicFolderPath);

                BtsFusionInfo_QH btsFusionInfo = btsInfo.FusionInfo as BtsFusionInfo_QH;
                fillFusionKpiPage(eBook, btsFusionInfo);

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);

                StationAcceptAna_QH.GetInstance().ReportFilePaths.Add(targetFile);
                reportInfo(string.Format("成功导出 {0} 站点的单验报告。", btsInfo.BtsName));
                return true;
            }
            catch (Exception ex)
            {
                reportError(ex);
                return false;
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
                //GC.Collect();
            }
        }

        //填充报告首页-室分记录单
        protected static void fillHomePage(Excel.Workbook eBook, InDoorBtsAcceptInfo_QH btsInfo
            , BtsWorkParam_QH btsWorkParamInfo, string strNotAccordDes)
        {
            LTEBTS srcLteBts = btsInfo.LteBts;
            Excel.Worksheet homePageSheet = (Excel.Worksheet)eBook.Sheets[1];
            //string districtName = DistrictManager.GetInstance().getDistrictName(MainModel.GetInstance().DistrictID);

            #region 基站描述及基站参数
            homePageSheet.get_Range("e3").set_Value(Type.Missing, srcLteBts.Name);
            homePageSheet.get_Range("e5").set_Value(Type.Missing, srcLteBts.BTSID);
            homePageSheet.get_Range("z3").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));
            homePageSheet.get_Range("z5").set_Value(Type.Missing, btsWorkParamInfo.CoverScene);
            homePageSheet.get_Range("z9").set_Value(Type.Missing, btsInfo.CoveredFloors);
            homePageSheet.get_Range("h13").set_Value(Type.Missing, srcLteBts.Longitude);
            homePageSheet.get_Range("h14").set_Value(Type.Missing, srcLteBts.Latitude);
            if (btsInfo.PhoneTestInfo != null)
            {
                homePageSheet.get_Range("n13").set_Value(Type.Missing, btsInfo.PhoneTestInfo.Longitude);
                homePageSheet.get_Range("n14").set_Value(Type.Missing, btsInfo.PhoneTestInfo.Latitude);
            }
            #endregion

            int cellIndex = 0;
            int cellParamColIndex = 8;
            int cellParamRowIndex = 18;

            int cellKpiColIndex = 12;
            int cellKpiRowIndex = 41;
            foreach (LTECell icell in srcLteBts.Cells)
            {
                if (cellIndex == 3)
                {
                    cellParamColIndex = 8;
                    cellParamRowIndex = 28;

                    cellKpiColIndex = 12;
                    cellKpiRowIndex = 52;
                }
                else if (cellIndex == 6)
                {
                    cellKpiColIndex = 12;
                    cellKpiRowIndex = 63;
                }

                #region 小区参数
                homePageSheet.Cells[cellParamRowIndex, cellParamColIndex] = string.Format("Cell-{0}(PCI:{1})", cellIndex + 1, icell.PCI);
                homePageSheet.Cells[cellParamRowIndex + 2, cellParamColIndex] = icell.TAC;
                homePageSheet.Cells[cellParamRowIndex + 2, cellParamColIndex + 3] = icell.TAC;//TAC暂无手机实测信息，用工参TAC填充
                homePageSheet.Cells[cellParamRowIndex + 3, cellParamColIndex] = icell.PCI;
                homePageSheet.Cells[cellParamRowIndex + 4, cellParamColIndex] = icell.EARFCN;
                //homePageSheet.Cells[cellParamRowIndex + 8, cellParamColIndex] = "";//合路方式
                InDoorCellAcceptInfo_QH cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfo))
                {
                    homePageSheet.Cells[cellParamRowIndex + 9, cellParamColIndex + 3] = cellInfo.RoadTypeDes;//单双路
                }
                #endregion

                if (cellInfo != null)
                {
                    #region 小区实测信息

                    if (cellInfo.PhoneTestInfo != null)
                    {
                        homePageSheet.Cells[cellParamRowIndex + 3, cellParamColIndex + 3] = cellInfo.PhoneTestInfo.PCI;
                        homePageSheet.Cells[cellParamRowIndex + 4, cellParamColIndex + 3] = cellInfo.PhoneTestInfo.EARFCN;
                    }
                    #endregion

                    #region 小区指标
                    homePageSheet.Cells[cellKpiRowIndex, cellKpiColIndex] = cellInfo.RrcInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 1, cellKpiColIndex] = cellInfo.ErabInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 2, cellKpiColIndex] = cellInfo.AccessInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 3, cellKpiColIndex] = cellInfo.IsCsfbAccord ? "是" : "否";
                    homePageSheet.Cells[cellKpiRowIndex + 4, cellKpiColIndex] = cellInfo.SrvccInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 5, cellKpiColIndex] = cellInfo.VolteAudioInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 6, cellKpiColIndex] = cellInfo.VolteVideoInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 7, cellKpiColIndex] = cellInfo.IsFtpDlAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 8, cellKpiColIndex] = cellInfo.IsFtpUlAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 9, cellKpiColIndex] = cellInfo.DlCoverRate.IsAccordDes;
                    #endregion
                }

                cellIndex++;
                cellParamColIndex += 8;
                cellKpiColIndex += 5;
            }

            homePageSheet.get_Range("n75").set_Value(Type.Missing, btsInfo.IsHandoverAccordDes);//系统内切换
            homePageSheet.get_Range("h92").set_Value(Type.Missing, btsInfo.IsAccordAcceptStr);//验收结论
            homePageSheet.get_Range("a95").set_Value(Type.Missing, strNotAccordDes);//未通过验收的原因
        }

        //填充报告第二页-性能验收测试表格
        protected static void fillKpiPage(Excel.Workbook eBook, InDoorBtsAcceptInfo_QH btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.LteBts;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[2];
            kpiPageSheet.get_Range("c2").set_Value(Type.Missing, srcLteBts.Name);
            kpiPageSheet.get_Range("o2").set_Value(Type.Missing, srcLteBts.BTSID);

            int cellIndex = 0;
            int firstColIndex = 16;//尝试次数或FTP相关指标所在列
            int secondColIndex = 23;//成功次数所在列
            foreach (LTECell icell in srcLteBts.Cells)
            {
                InDoorCellAcceptInfo_QH cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfo))
                {
                    int rowIndex = 7 + (cellIndex * 15);//小区RRC指标所在行：首个小区所在行号为5，其他小区行号以15递增
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.RrcInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.RrcInfo.ValidCount;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.ErabInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.ErabInfo.ValidCount;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.AccessInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.AccessInfo.ValidCount;
                    rowIndex += 2;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.CsfbInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.CsfbInfo.ValidCount;
                    kpiPageSheet.Cells[rowIndex, 44] = cellInfo.ReturnToLteTimeDelayAvg;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.CsfbReturnToLteInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.CsfbReturnToLteInfo.ValidCount;

                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.SrvccInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.SrvccInfo.ValidCount;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.VolteAudioInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.VolteAudioInfo.ValidCount;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.VolteVideoInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.VolteVideoInfo.ValidCount;

                    rowIndex++;
                    setRateValue(kpiPageSheet, ++rowIndex, firstColIndex, cellInfo.DlCoverRate.Rate);
                    kpiPageSheet.Cells[++rowIndex, firstColIndex] = cellInfo.FtpRsrpInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[++rowIndex, firstColIndex] = cellInfo.FtpSinrInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[++rowIndex, firstColIndex] = cellInfo.FtpDlSpeedInfo.KpiAvgValueDes;

                    kpiPageSheet.Cells[++rowIndex, secondColIndex] = cellInfo.FtpUlSpeedInfo.KpiAvgValueDes;
                    setDoubleValue(kpiPageSheet, rowIndex, secondColIndex + 12, cellInfo.FtpUlSpeedMax);
                    rowIndex += 2;
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.HandOverInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.HandOverInfo.ValidCount;
                }
                cellIndex++;
            }
        }

        //填充报告第三页-平层测试指标
        protected static void fillLevelKpiPage(Excel.Workbook eBook, InDoorBtsAcceptInfo_QH btsInfo)
        {
            int rowIndex = 2;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[3];
            foreach (InDoorCellAcceptInfo_QH cellAcceptInfo in btsInfo.CellsAcceptList)
            {
                fillCellLevelKpiPage(kpiPageSheet, cellAcceptInfo, ref rowIndex);
            }

            kpiPageSheet.Cells[rowIndex + 2, 1] = btsInfo.CoveredFloorsDes_Lack;
        }

        //填充报告第四页-性能覆盖效果图
        protected static void fillCoverPicPage(Excel.Workbook eBook, InDoorBtsAcceptInfo_QH btsInfo
            , string picFolderPath)
        {
            if (string.IsNullOrEmpty(picFolderPath))
            {
                return;
            }

            Excel.Worksheet coverPicPageSheet = (Excel.Worksheet)eBook.Sheets[4];

            List<InBtsFloorCoverPicInfo> coverPicList = new List<InBtsFloorCoverPicInfo>(btsInfo.FloorCoverPicInfoDic.Values);
            coverPicList.Sort();
            int rowIndex = 17;
            foreach (InBtsFloorCoverPicInfo coverInfo in coverPicList)
            {
                string coverPicFolderPath = Path.Combine(picFolderPath, coverInfo.FolderName);
                reportInfo(string.Format("开始填充室分性能覆盖图：ENodeBID：{0}，楼层：{1}，图片路径{2}"
                    , coverInfo.ENodeBID, coverInfo.FloorName, coverPicFolderPath));

                if (Directory.Exists(coverPicFolderPath))
                {
                    insertCoverPicture(coverPicPageSheet, coverPicFolderPath, 1, rowIndex, "RSRP-" + coverInfo.FloorName + "F", coverInfo.CoverPic_Rsrp);
                    insertCoverPicture(coverPicPageSheet, coverPicFolderPath, 10, rowIndex, "SINR-" + coverInfo.FloorName + "F", coverInfo.CoverPic_Sinr);
                    insertCoverPicture(coverPicPageSheet, coverPicFolderPath, 19, rowIndex, "FTP下载-" + coverInfo.FloorName + "F", coverInfo.CoverPic_FtpDl);
                    insertCoverPicture(coverPicPageSheet, coverPicFolderPath, 28, rowIndex, "FTP上传-" + coverInfo.FloorName + "F", coverInfo.CoverPic_FtpUl);
                    insertCoverPicture(coverPicPageSheet, coverPicFolderPath, 37, rowIndex, "切换PCI-" + coverInfo.FloorName + "F", coverInfo.CoverPic_Pci);

                    rowIndex += 22;
                }
            }
        }
        protected static void insertCoverPicture(Excel.Worksheet eSheet, string coverPicFolderPath
            , int colIndex, int rowIndex, string picDes, string picFileName)
        {
            string picPath = Path.Combine(coverPicFolderPath, picFileName);
            if (File.Exists(picPath))
            {
                reportInfo(string.Format("开始填充图片：{0}", picPath));

                Excel.Range rng = eSheet.get_Range(eSheet.Cells[rowIndex, colIndex], eSheet.Cells[rowIndex, colIndex]);
                rng.set_Value(Type.Missing, picDes);

                rowIndex++;
                rng = eSheet.get_Range(eSheet.Cells[rowIndex, colIndex], eSheet.Cells[rowIndex + 19, colIndex + 8]);
                eSheet.Shapes.AddPicture(picPath, Microsoft.Office.Core.MsoTriState.msoFalse
                    , Microsoft.Office.Core.MsoTriState.msoCTrue, (float)(double)rng.Left
                   , (float)(double)rng.Top, (float)(double)rng.Width, (float)(double)rng.Height);
            }
        }

        //填充报告第五页-站点后台指标监控
        protected static void fillFusionKpiPage(Excel.Workbook eBook, BtsFusionInfo_QH btsFusionInfo)
        {
            if (btsFusionInfo == null)
            {
                return;
            }
            Excel.Worksheet bgKpiPageSheet = (Excel.Worksheet)eBook.Sheets[5];

            DateTime curDate = btsFusionInfo.BeginTime.Date;
            int cellIndex = 0;
            foreach (CellWorkParamBase cellWorkParam in btsFusionInfo.BtsWorkParamInfo.CellWorkParams)
            {
                int perfRowIndex = 4 + (7 * cellIndex);
                bgKpiPageSheet.Cells[perfRowIndex, 1] = cellWorkParam.CellName;

                Dictionary<string, CellPerfDataBase> date_cellPerfDataDic;
                btsFusionInfo.CellPerfInfoDic.TryGetValue(cellWorkParam.CGI, out date_cellPerfDataDic);
                while (curDate <= btsFusionInfo.EndTime.Date)
                {
                    string dateDes = BtsFusionInfoBase.GetDateKeyDes(curDate);
                    bgKpiPageSheet.Cells[perfRowIndex, 2] = dateDes;

                    #region 性能数据
                    setFusionData(bgKpiPageSheet, perfRowIndex, date_cellPerfDataDic, dateDes);
                    #endregion

                    #region MR数据
                    setMRData(btsFusionInfo, bgKpiPageSheet, cellWorkParam, perfRowIndex, dateDes);
                    #endregion

                    perfRowIndex++;
                    curDate = curDate.AddDays(1);
                }
                cellIndex++;
            }


            #region 告警数据
            setAlarmData(btsFusionInfo, bgKpiPageSheet);
            #endregion
        }

        private static void setFusionData(Excel.Worksheet bgKpiPageSheet, int perfRowIndex, Dictionary<string, CellPerfDataBase> date_cellPerfDataDic, string dateDes)
        {
            CellPerfDataBase cellPerfBase;
            if (date_cellPerfDataDic != null && date_cellPerfDataDic.TryGetValue(dateDes, out cellPerfBase))
            {
                CellPerfData_QH cellPerfInfo = cellPerfBase as CellPerfData_QH;
                if (cellPerfInfo != null)
                {
                    int perfColIndex = 3;//列序号
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.RrcConnectTryCount);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.RrcSetupSuccessRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabConnectTryCount);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabSetupSuccessRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessConnectRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessDropRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabDropRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.InnerHandoverSuccessRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.PdcpThroughput_UL);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.PdcpThroughput_DL);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.PdcpThroughputSum);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessConnectRate_QCI1);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabDropRate_QCI1);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex, cellPerfInfo.EsrvccHandoverSuccessRate);
                }
            }
        }

        private static void setMRData(BtsFusionInfo_QH btsFusionInfo, Excel.Worksheet bgKpiPageSheet, CellWorkParamBase cellWorkParam, int perfRowIndex, string dateDes)
        {
            Dictionary<string, CellMRDataBase> date_CellMRDataDic;
            if (btsFusionInfo.CellMRInfoDic.TryGetValue(cellWorkParam.CGI, out date_CellMRDataDic))
            {
                CellMRDataBase cellMrInfo;
                if (date_CellMRDataDic.TryGetValue(dateDes, out cellMrInfo))
                {
                    setFloatValue(bgKpiPageSheet, perfRowIndex, 17, cellMrInfo.MrCoverRate);
                }
            }
        }

        private static void setAlarmData(BtsFusionInfo_QH btsFusionInfo, Excel.Worksheet bgKpiPageSheet)
        {
            int alarmRowIndex = 47;
            if (btsFusionInfo.BtsAlarmInfoDic != null && btsFusionInfo.BtsAlarmInfoDic.Count > 0)
            {
                bgKpiPageSheet.Cells[alarmRowIndex, 2] = "有";
                alarmRowIndex += 2;
                foreach (BtsAlarmDataBase alarmData in btsFusionInfo.BtsAlarmInfoDic.Values)
                {
                    bgKpiPageSheet.Cells[alarmRowIndex, 1] = alarmData.AlarmTitle;
                    bgKpiPageSheet.Cells[alarmRowIndex, 2] = alarmData.BeginTimeStr;
                    bgKpiPageSheet.Cells[alarmRowIndex, 3] = alarmData.EndTimeStr;
                    bgKpiPageSheet.Cells[alarmRowIndex, 4] = alarmData.Desc;
                    alarmRowIndex++;
                }
            }
            else
            {
                bgKpiPageSheet.Cells[alarmRowIndex, 2] = "无";
            }
        }
    }
}
