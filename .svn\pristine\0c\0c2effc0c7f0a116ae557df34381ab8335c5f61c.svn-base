﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public interface IStationAcceptManager
    {
        void SetAcceptCond(LteStationAcceptCondition cond);

        void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager);

        void DoWorkAfterAnalyze();

        string ErrMsg
        {
            get;
        }

        string HasExportedFiles
        {
            get;
        }
    }
}
