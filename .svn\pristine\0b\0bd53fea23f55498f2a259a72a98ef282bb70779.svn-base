﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTCsfbCallStat;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class EpsfbFRCallStatQuery : DIYAnalyseByFileBackgroundBase
    {
        //主叫
        protected List<int> MoCallAttemptEvtIdList = new List<int> { };
        //被叫
        protected List<int> MtCallAttemptEvtIdList = new List<int> { };

        protected static readonly object lockObj = new object();
        private static EpsfbFRCallStatQuery instance = null;
        public static EpsfbFRCallStatQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new EpsfbFRCallStatQuery();
                    }
                }
            }
            return instance;
        }

        protected EpsfbFRCallStatQuery()
           : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            this.Columns = new List<string>();
        }

        public override string Name
        {
            get
            {
                return "EPSFB返回时延统计(按文件)";
            }
        }

        protected bool checkDelay = true;
        protected int maxDelaySec = 180;

        protected override bool getCondition()
        {
            var dlg = new CallConditionDlg();
            dlg.SetCondition(checkDelay, maxDelaySec);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out checkDelay, out maxDelaySec);
            callStatList = new List<SingleCallStatInfo>();
            return callStatList != null;
        }

        protected List<SingleCallStatInfo> callStatList = null;
        protected override void fireShowForm()
        {
            if (callStatList == null || callStatList.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }
            EpsfbCallStatListForm frm = MainModel.GetObjectFromBlackboard(typeof(EpsfbCallStatListForm)) as EpsfbCallStatListForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new EpsfbCallStatListForm();
            }
            frm.FillData(callStatList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            callStatList = null;
        }

        /// <summary>
        /// 先进行主被叫关联
        /// </summary>
        protected override void analyseFiles()
        {


        }


        protected override void doStatWithQuery()
        {




        }


    }
}
