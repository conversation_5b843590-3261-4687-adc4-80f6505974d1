﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason;
using MasterCom.Util;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTWeakSINRReason : DIYAnalyseByCellBackgroundBaseByFile
    {
        public static List<Event> condEvents { get; set; } = new List<Event>();
        public static List<Event> handOverTooMuchEvents { get; set; } = new List<Event>();
        protected static readonly object lockObj = new object();
        private static ZTWeakSINRReason intance = null;
        public static ZTWeakSINRReason GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTWeakSINRReason();
                    }
                }
            }
            return intance;
        }

        protected ZTWeakSINRReason()
            : base(MainModel.GetInstance())
        {
            if (intance != null)
            {
                return;
            }
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
            IncludeMessage = false;
        }

        public ZTWeakSINRReason(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "质差原因分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22025, this.Name);
        }

        protected override void fireShowForm()
        {
            if (groupSet.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            RusultListForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(RusultListForm)) as RusultListForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new RusultListForm();
            }
            frm.FillData(groupSet);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        protected FuncCondition funcCond = new FuncCondition();
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            return showFuncCondSetDlg();
        }
        protected bool showFuncCondSetDlg()
        {
            ReasonOptionDlg dlg = new ReasonOptionDlg(funcCond);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                return true;
            }
            return false;
        }


        protected override void clearDataBeforeAnalyseFiles()
        {
            groupSet = new List<WeakSINRPointGroup>();
            groupDic = new Dictionary<object, WeakSINRPointGroup>();
        }

        protected virtual void saveTestPointInfo(FileInfo fi, TestPoint tp, WeakSINRPoint wp, params object[] resvParam)
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion reg in resvRegions)
                {
                    addGroupInfo(reg, wp, reg);
                }
            }
            else if (condition.Geometorys.RegionInfo != null)
            {
                addGroupInfo(condition.Geometorys.RegionInfo, wp, condition.Geometorys.RegionInfo);
            }
            else
            {
                addGroupInfo(fi, wp, condition.Geometorys.RegionInfo);
            }
        }

        private void addGroupInfo(object item, WeakSINRPoint wp, object grpItem)
        {
            WeakSINRPointGroup grp;
            if (!groupDic.TryGetValue(item, out grp))
            {
                grp = new WeakSINRPointGroup(grpItem, funcCond.Reasons);
                groupDic.Add(item, grp);
            }
            grp.AddWeakSINRPoint(wp);
        }

        protected virtual bool filter(TestPoint tp)
        {
            if (condition.Geometorys.Region != null)
            {
                return !condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
            }
            return false;
        }

        /// <summary>
        /// key可为FileInfo，或者是RegionInfo
        /// </summary>
        private Dictionary<object, WeakSINRPointGroup> groupDic;
        private List<WeakSINRPointGroup> groupSet;
        //用于判断是否为切换不及时
        public static bool isHandOverUnTimely { get; set; } = false;

        //用于判断是否为切换不合理
        public static bool isHandOverProblem { get; set; } = false;

        //用于判断是否为突然质差
        public static bool isSuddenWeak { get; set; } = false;

        //切换过频繁条件
        public static int timeLimit { get; set; } = 15;
        public static int distanceLimit { get; set; } = 250;
        public static int handoverCount { get; set; } = 2;
        protected override void doStatWithQuery()
        {
            if (MainModel.DTDataManager.FileDataManagers.Count <= 0)
            {
                return;
            }

            foreach (Event evt in MainModel.DTDataManager.FileDataManagers[0].Events)
            {
                if ((evt.ID == 1309 || evt.ID == 851 || evt.ID == 899))
                {
                    //切换成功事件
                    condEvents.Add(evt);
                }
            }

            try
            {
                //切换过频繁
                MainModel.DTDataManager.FileDataManagers[0].Events = condEvents;
                HandoverAndReselectionManager.GetHandoverTooMuchResult(MainModel.DTDataManager.FileDataManagers[0],
                timeLimit, distanceLimit, handoverCount, handOverTooMuchEvents);

                List<TestPoint> testPointList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
                FileInfo fi = MainModel.DTDataManager.FileDataManagers[0].GetFileInfo();
                LTEHandoverBehindTime info = null;
                for (int i = 0; i < testPointList.Count; i++)
                {
                    TestPoint tp = testPointList[i];
                    if (!filter(tp))
                    {
                        WeakSINRPoint pnt = null;
                        if (funcCond.IsValid(tp))
                        {
                            isHandOverProblem = isHandOverProblemFun(testPointList, i);
                            isHandOverUnTimely = isHandOverUnTimelyFun(testPointList, i, ref info);
                            isSuddenWeak = isSuddenWeakFun(testPointList, i);
                            ReasonBase reason = funcCond.JudgeReason(tp);
                            pnt = new WeakSINRPoint(fi, tp, reason);
                        }
                        saveTestPointInfo(fi, tp, pnt);
                    }
                }
                condEvents.Clear();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString() + Environment.NewLine + ex.StackTrace);
            }
        }

        public static int timeBeforeWeakSinr { get; set; } = 6;
        public static int timePersist { get; set; } = 3;
        //切换后2秒的电平均值比前2秒均值差的
        private static int timeAround = 2;
        //用于判断是否为切换不合理
        protected bool isHandOverProblemFun(List<TestPoint> testPointList, int index)
        {
            if (condEvents.Count == 0)
            {
                return false;
            }
            TestPoint tp = testPointList[index];
            List<Event> tempEvent = new List<Event>();
            foreach (Event evt in condEvents)
            {
                if ((tp.Time - evt.Time <= timeBeforeWeakSinr)
                    && (tp.Time - evt.Time >= 0))
                {
                    tempEvent.Add(evt);
                }
            }

            if (tempEvent.Count == 0)
            {
                return false;
            }

            return (handOverProblemFun1(testPointList, index, tempEvent)
                    || handOverProblemFun2(testPointList, index, tempEvent));
        }

        //判断切换不合理的方法一:切换后2秒的电平均值比前2秒均值差的
        private bool handOverProblemFun1(List<TestPoint> testPointList, int index, List<Event> tempEvent)
        {
            foreach (Event evt in tempEvent)
            {
                List<TestPoint> before2second = new List<TestPoint>();
                List<TestPoint> after2second = new List<TestPoint>();
                int tempBefore = index;
                int tempAfter = index;

                //取得前2秒的采样点
                getBeforeTps(testPointList, evt, before2second, tempBefore);

                //取得后2秒的采样点
                getAfterTps(testPointList, evt, after2second, tempAfter);

                if ((before2second.Count == 0)
                    || (after2second.Count == 0))
                {
                    continue;
                }

                float beforeSumRsrp = 0;
                float afterSumRsrp = 0;
                getSumRsrp(before2second, after2second, ref beforeSumRsrp, ref afterSumRsrp);

                if ((beforeSumRsrp / before2second.Count) - (afterSumRsrp / after2second.Count) > 0)
                {
                    return true;
                }
            }
            return false;
        }

        private void getBeforeTps(List<TestPoint> testPointList, Event evt, List<TestPoint> before2second, int tempBefore)
        {
            while (true)
            {
                --tempBefore;
                if (tempBefore < 0)
                {
                    break;
                }
                if ((evt.Time - testPointList[tempBefore].Time > 0)
                    && (evt.Time - testPointList[tempBefore].Time <= timeAround))
                {
                    before2second.Add(testPointList[tempBefore]);
                }
                if (evt.Time - testPointList[tempBefore].Time > timeAround)
                {
                    break;
                }
            }
        }

        private void getAfterTps(List<TestPoint> testPointList, Event evt, List<TestPoint> after2second, int tempAfter)
        {
            while (true)
            {
                if ((testPointList[tempAfter].Time - evt.Time > 0)
                    && (testPointList[tempAfter].Time - evt.Time <= timeAround))
                {
                    after2second.Add(testPointList[tempAfter]);
                }
                if (testPointList[tempAfter].Time - evt.Time > timeAround)
                {
                    break;
                }
                tempAfter++;
                if (tempAfter == testPointList.Count)
                {
                    break;
                }
            }
        }

        private void getSumRsrp(List<TestPoint> before2second, List<TestPoint> after2second, ref float beforeSumRsrp, ref float afterSumRsrp)
        {
            foreach (TestPoint btp in before2second)
            {
                object value = GetSCellRelev(btp);
                if (value != null)
                {
                    beforeSumRsrp += (float)value;
                }
            }

            foreach (TestPoint atp in after2second)
            {
                object value = GetSCellRelev(atp);
                if (value != null)
                {
                    afterSumRsrp += (float)value;
                }
            }
        }

        //判断切换不合理的方法二:切换后主服小区电 平小于邻小区电平+3dBm持续 N1 秒
        private bool handOverProblemFun2(List<TestPoint> testPointList, int index, List<Event> tempEvent)
        {
            foreach (Event evt in tempEvent)
            {
                List<TestPoint> tempTPs = getTempTPs(testPointList, index, evt);

                if (tempTPs.Count == 0)
                {
                    continue;
                }

                bool isEnd = judgeProblemTP(tempTPs);
                if (isEnd)
                {
                    return true;
                }
            }
            return false;
        }

        private bool judgeProblemTP(List<TestPoint> tempTPs)
        {
            foreach (TestPoint tp in tempTPs)
            {
                object mRsrp = GetSCellRelev(tp);
                if (mRsrp == null)
                {
                    continue;
                }

                float maxNRsrp = getMaxNRsrp(tp);
                if (maxNRsrp == -1000000)
                {
                    continue;
                }

                if ((float)mRsrp >= maxNRsrp + 3)
                {
                    break;
                }

                if (tp == tempTPs[tempTPs.Count - 1])
                {
                    return true;
                }
            }
            return false;
        }

        private List<TestPoint> getTempTPs(List<TestPoint> testPointList, int index, Event evt)
        {
            int beforeIndex = index;
            int afterIndex = index + 1;
            List<TestPoint> tempTPs = new List<TestPoint>();
            while ((beforeIndex >= 0)
                    && (testPointList[beforeIndex].Time - evt.Time >= 0))
            {
                if (testPointList[beforeIndex].Time - evt.Time <= timePersist)
                {
                    tempTPs.Add(testPointList[beforeIndex]);
                }
                beforeIndex--;
            }

            while ((afterIndex < testPointList.Count)
                    && (testPointList[afterIndex].Time - evt.Time <= timePersist))
            {
                tempTPs.Add(testPointList[afterIndex]);
                afterIndex++;
            }

            return tempTPs;
        }

        private float getMaxNRsrp(TestPoint tp)
        {
            float maxNRsrp = -1000000;
            for (int i = 0; i < 10; i++)
            {
                object value = GetNCellRelev(tp, i);
                if (value == null)
                {
                    continue;
                }
                if (maxNRsrp < (float)value)
                {
                    maxNRsrp = (float)value;
                }
            }

            return maxNRsrp;
        }

        //几秒前
        public static int beforeTime { get; set; } = 5;
        //主小区与邻小区的差值限制
        public static float rsrpDiffer { get; set; } = 1;
        //持续时间
        public static int stayTime { get; set; } = 3;

        //用于判断是否为切换不及时
        protected bool isHandOverUnTimelyFun(List<TestPoint> testPointList, int index, ref LTEHandoverBehindTime info)
        {
            bool isUnTimely = false;
            TestPoint weakTp = testPointList[index];
            for (; index >= 0; index--)
            {
                TestPoint tp = testPointList[index];
                double seconds = (weakTp.DateTime - tp.DateTime).TotalSeconds;
                if (seconds > beforeTime)
                {
                    break;
                }

                if (!isValidTestPoint(tp))//非区域内点
                {
                    isUnTimely = saveAndResetOneResult(ref info);
                }
                else
                {
                    bool isEnd = judgeTimely(testPointList, ref info, ref isUnTimely, tp);
                    if (isEnd)
                    {
                        break;
                    }
                }

                if (isUnTimely)
                {
                    return true;
                }
            }
            return isUnTimely;
        }

        private bool judgeTimely(List<TestPoint> testPointList, ref LTEHandoverBehindTime info, ref bool isUnTimely, TestPoint tp)
        {
            float? pccpchValue = this.GetSCellRelev(tp); //(float?)tp["lte_RSRP"];
            float maxNCellPccpch = float.MinValue;
            for (int i = 0; i < 10; i++)
            {
                float? nCellPccpch = this.GetNCellRelev(tp, i); //(float?)tp["lte_NCell_RSRP", i];
                if (nCellPccpch != null)
                {
                    maxNCellPccpch = Math.Max((float)nCellPccpch, maxNCellPccpch);
                }
            }
            if ((maxNCellPccpch - pccpchValue) >= rsrpDiffer)
            {
                if (info == null)
                {
                    info = new LTEHandoverBehindTime();
                }
                info.AddTestPoint(tp, (float)pccpchValue);
                if (tp.Equals(testPointList[0]))
                {//文件最后一点，需要把前面的信息保存起来
                    isUnTimely = saveAndResetOneResult(ref info);
                    return true;
                }
            }
            else
            {
                isUnTimely = saveAndResetOneResult(ref info);
            }
            return false;
        }

        //质量毛刺质量平均值
        public static float suddenWeakAvg { get; set; } = 8;
        //质量毛刺时间限制
        public static int suddenWeakTime { get; set; } = 2;

        protected bool isSuddenWeakFun(List<TestPoint> testPointList, int index)
        {
            TestPoint weakTp = testPointList[index];
            int tempBefore = index - 1;
            int tempAfter = index + 1;
            int coutSum = 0;
            float sinrSunm = 0;

            for (; tempBefore >= 0; tempBefore--)//前几秒
            {
                TestPoint tp = testPointList[tempBefore];
                double seconds = (weakTp.DateTime - tp.DateTime).TotalSeconds;
                if (seconds > suddenWeakTime)
                {
                    break;
                }
                float? sinr = GetSINR(tp);
                if (sinr != null)
                {
                    coutSum++;
                    sinrSunm += (float)sinr;
                }
            }

            for (; tempAfter < testPointList.Count; tempAfter++)//后几秒
            {
                TestPoint tp = testPointList[tempAfter];
                double seconds = (tp.DateTime - weakTp.DateTime).TotalSeconds;
                if (seconds > suddenWeakTime)
                {
                    break;
                }
                float? sinr = GetSINR(tp);
                if (sinr != null)
                {
                    coutSum++;
                    sinrSunm += (float)sinr;
                }
            }

            if (coutSum > 0)
            {
                return sinrSunm / coutSum >= suddenWeakAvg;
            }
            return false;
        }

        protected virtual float? GetSCellRelev(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }
        protected virtual float? GetSINR(TestPoint tp)
        {
            return (float?)tp["lte_SINR"];
        }

        protected virtual float? GetNCellRelev(TestPoint tp, int index)
        {
            return (float?)tp["lte_NCell_RSRP", index];
        }

        private bool saveAndResetOneResult(ref LTEHandoverBehindTime info)
        {
            if (info == null)
            {
                return false;
            }
            if (Math.Abs(info.StaySeconds) >= stayTime)//持续时间判断
            {
                info = null;//重置
                return true;
            }
            info = null;//重置
            return false;
        }

        protected override void getResultsAfterQuery()
        {
            if (groupDic.Count > 0)
            {
                WeakSINRPointGroup summaryGrp = new WeakSINRPointGroup("汇总", funcCond.Reasons);
                foreach (KeyValuePair<object, WeakSINRPointGroup> kvp in groupDic)
                {
                    groupSet.Add(kvp.Value);
                    if (groupDic.Count > 1)
                    {//大于一组，需要进行汇总处理
                        summaryGrp.Gather(kvp.Value);
                    }
                }
                if (groupDic.Count > 1)
                {
                    groupSet.Add(summaryGrp);
                }
            }
        }


        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.质量; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["MaxSINR"] = funcCond.MaxSINR;
                param["ReasonsParam"] = funcCond.ReasonsParam;
                param["timeLimit"] = timeLimit;
                param["distanceLimit"] = distanceLimit;
                param["handoverCount"] = handoverCount;
                param["timePersist"] = timePersist;
                param["timeBeforeWeakSinr"] = timeBeforeWeakSinr;
                param["stayTime"] = stayTime;
                param["rsrpDiffer"] = rsrpDiffer;
                param["beforeTime"] = beforeTime;
                param["suddenWeakTime"] = suddenWeakTime;
                param["suddenWeakAvg"] = suddenWeakAvg;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("MaxSINR"))
                {
                    funcCond.MaxSINR = (float)param["MaxSINR"];
                }
                if (param.ContainsKey("ReasonsParam"))
                {
                    funcCond.ReasonsParam = (Dictionary<string, object>)param["ReasonsParam"];
                }
                if (param.ContainsKey("timeLimit"))
                {
                    timeLimit = (int)param["timeLimit"];
                }
                if (param.ContainsKey("distanceLimit"))
                {
                    distanceLimit = (int)param["distanceLimit"];
                }
                if (param.ContainsKey("handoverCount"))
                {
                    handoverCount = (int)param["handoverCount"];
                }
                if (param.ContainsKey("timePersist"))
                {
                    timePersist = (int)param["timePersist"];
                }
                if (param.ContainsKey("timeBeforeWeakSinr"))
                {
                    timeBeforeWeakSinr = (int)param["timeBeforeWeakSinr"];
                }
                if (param.ContainsKey("stayTime"))
                {
                    stayTime = (int)param["stayTime"];
                }
                if (param.ContainsKey("rsrpDiffer"))
                {
                    rsrpDiffer = (float)param["rsrpDiffer"];
                }
                if (param.ContainsKey("beforeTime"))
                {
                    beforeTime = (int)param["beforeTime"];
                }
                if (param.ContainsKey("suddenWeakTime"))
                {
                    suddenWeakTime = (int)param["suddenWeakTime"];
                }
                if (param.ContainsKey("suddenWeakAvg"))
                {
                    suddenWeakAvg = (float)param["suddenWeakAvg"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CommonSimpleProperties(this, showFuncCondSetDlg);
            }
        }

        protected override void saveBackgroundData()
        {
            getResultsAfterQuery();
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (WeakSINRPointGroup item in groupSet)
            {
                foreach (WeakSINRPoint weakSinrPoint in item.Points)
                {
                    BackgroundResult result = weakSinrPoint.ConvertToBackgroundResult();
                    result.SubFuncID = GetSubFuncID();
                    result.ProjectString = MasterCom.RAMS.BackgroundFunc.BackgroundFuncBaseSetting.GetInstance().projectType;
                    bgResultList.Add(result);
                }
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, bgResultList);

            groupSet.Clear();
            groupDic.Clear();
        }
        protected override void initBackgroundImageDesc()
        {
            Dictionary<string, List<BackgroundResult>> bgResultDic = new Dictionary<string, List<BackgroundResult>>();
            Dictionary<string, int> reasonDic = new Dictionary<string, int>();
            foreach (ReasonBase reason in funcCond.Reasons)
            {
                reasonDic[reason.Name] = 0;
            }

            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                string cellKey = bgResult.LAC + "_" + bgResult.CI;
                List<BackgroundResult> bgResultList;
                if (!bgResultDic.TryGetValue(cellKey, out bgResultList))
                {
                    bgResultList = new List<BackgroundResult>();
                    bgResultDic[cellKey] = bgResultList;
                }
                bgResultList.Add(bgResult);

                if (reasonDic.ContainsKey(bgResult.StrDesc))
                {
                    reasonDic[bgResult.StrDesc]++;
                }
                else
                {
                    reasonDic[bgResult.StrDesc] = 1;
                }
            }

            this.BackgroundNPOIRowResultDic.Clear();
            this.BackgroundNPOIRowResultDic["详细"] = getDetailNPOIRows(bgResultDic);
            this.BackgroundNPOIRowResultDic["概要"] = getMainNPOIRows(reasonDic, BackgroundResultList.Count);


        }

        private List<NPOIRow> getDetailNPOIRows(Dictionary<string, List<BackgroundResult>> bgResultDic)
        {
            List<NPOIRow> detailNPOIRowList = new List<NPOIRow>();
            NPOIRow rowTitle = new NPOIRow();
            rowTitle.AddCellValue("序号");
            rowTitle.AddCellValue("小区名");
            rowTitle.AddCellValue("TAC");
            rowTitle.AddCellValue("ECI");
            rowTitle.AddCellValue("CellID");
            rowTitle.AddCellValue("采样点序号");
            rowTitle.AddCellValue("SINR");
            rowTitle.AddCellValue("RSRP");
            rowTitle.AddCellValue("原因");
            rowTitle.AddCellValue("距离（米）");
            rowTitle.AddCellValue("采样点经度");
            rowTitle.AddCellValue("采样点纬度");
            rowTitle.AddCellValue("文件名");
            detailNPOIRowList.Add(rowTitle);

            int cellIndex = 0;
            foreach (List<BackgroundResult> bgResultList in bgResultDic.Values)
            {
                cellIndex++;
                NPOIRow rowGather = null;
                int tpIndex = 0;
                foreach (BackgroundResult bgResult in bgResultList)
                {
                    tpIndex++;
                    string distance = bgResult.GetImageValueString();
                    if (rowGather == null)
                    {
                        string cellName = bgResult.GetImageValueString();
                        rowGather = new NPOIRow();
                        rowGather.AddCellValue(cellIndex);
                        rowGather.AddCellValue(cellName);
                        rowGather.AddCellValue(bgResult.LAC);
                        rowGather.AddCellValue(bgResult.CI);
                        rowGather.AddCellValue(bgResult.CellIDDesc);
                    }

                    NPOIRow rowDetail = new NPOIRow();
                    rowDetail.AddCellValue(tpIndex);
                    rowDetail.AddCellValue(bgResult.RxQualMean);
                    rowDetail.AddCellValue(bgResult.RxLevMean);
                    rowDetail.AddCellValue(bgResult.StrDesc);
                    rowDetail.AddCellValue(distance);
                    rowDetail.AddCellValue(bgResult.LongitudeMid);
                    rowDetail.AddCellValue(bgResult.LatitudeMid);
                    rowDetail.AddCellValue(bgResult.FileName);
                    rowGather.AddSubRow(rowDetail);
                }
                detailNPOIRowList.Add(rowGather);
            }
            return detailNPOIRowList;
        }
        private List<NPOIRow> getMainNPOIRows(Dictionary<string, int> reasonDic, int totaWeaklCount)
        {
            List<NPOIRow> mainNPOIRowList = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("质差点总个数");
            row.AddCellValue("原因");
            row.AddCellValue("原因占比(%)");
            row.AddCellValue("个数");
            mainNPOIRowList.Add(row);

            foreach (var varKeyValue in reasonDic)
            {
                row = new NPOIRow();
                row.AddCellValue(totaWeaklCount);
                row.AddCellValue(varKeyValue.Key);
                row.AddCellValue(Math.Round(100.0 * varKeyValue.Value / totaWeaklCount, 2));
                row.AddCellValue(varKeyValue.Value);
                mainNPOIRowList.Add(row);
            }
            return mainNPOIRowList;
        }
        #endregion
    }

    public class ZTWeakSINRReason_FDD : ZTWeakSINRReason
    {
        private static ZTWeakSINRReason_FDD instances = null;
        public new static ZTWeakSINRReason_FDD GetInstance()
        {
            if (instances == null)
            {
                lock (lockObj)
                {
                    if (instances == null)
                    {
                        instances = new ZTWeakSINRReason_FDD();
                    }
                }
            }
            return instances;
        }
        public ZTWeakSINRReason_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
            IncludeMessage = false;
        }

        public override string Name
        {
            get { return "质差原因分析LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26023, this.Name);
        }

        protected override bool getCondition()
        {
            funcCond = new FuncCondition_FDD();
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            ReasonOptionDlg dlg = new ReasonOptionDlg(funcCond);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                return true;
            }
            return false;
        }
        protected override void doStatWithQuery()
        {
            foreach (Event evt in MainModel.DTDataManager.FileDataManagers[0].Events)
            {
                if ((evt.ID == 3309 || evt.ID == 3156 || evt.ID == 3159))
                {
                    //切换成功事件
                    condEvents.Add(evt);
                }
            }

            try
            {
                //切换过频繁
                MainModel.DTDataManager.FileDataManagers[0].Events = condEvents;
                HandoverAndReselectionManager.GetHandoverTooMuchResult(MainModel.DTDataManager.FileDataManagers[0],
                timeLimit, distanceLimit, handoverCount, handOverTooMuchEvents);

                List<TestPoint> testPointList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
                FileInfo fi = MainModel.DTDataManager.FileDataManagers[0].GetFileInfo();
                LTEHandoverBehindTime info = null;
                for (int i = 0; i < testPointList.Count; i++)
                {
                    TestPoint tp = testPointList[i];
                    if (!filter(tp))
                    {
                        WeakSINRPoint pnt = null;
                        if (funcCond.IsValid(tp))
                        {
                            isHandOverProblem = isHandOverProblemFun(testPointList, i);
                            isHandOverUnTimely = isHandOverUnTimelyFun(testPointList, i, ref info);
                            isSuddenWeak = isSuddenWeakFun(testPointList, i);
                            ReasonBase reason = funcCond.JudgeReason(tp);
                            pnt = new WeakSINRPoint_FDD(fi, tp, reason);
                        }
                        saveTestPointInfo(fi, tp, pnt);
                    }
                }
                condEvents.Clear();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString() + Environment.NewLine + ex.StackTrace);
            }
        }
        protected override float? GetSCellRelev(TestPoint tp)
        {
            return (float?)tp["lte_fdd_RSRP"];
        }
        protected override float? GetSINR(TestPoint tp)
        {
            return (float?)tp["lte_fdd_SINR"];
        }

        protected override float? GetNCellRelev(TestPoint tp, int index)
        {
            return (float?)tp["lte_fdd_NCell_RSRP", index];
        }
    }

    public class ZTWeakSINRReason_FDD_VOLTE : ZTWeakSINRReason_FDD
    {
        private static ZTWeakSINRReason_FDD_VOLTE instance = null;
        public static new ZTWeakSINRReason_FDD_VOLTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTWeakSINRReason_FDD_VOLTE();
                    }
                }
            }
            return instance;
        }
        public ZTWeakSINRReason_FDD_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "质差原因分析LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30015, this.Name);
        }
    }
}
