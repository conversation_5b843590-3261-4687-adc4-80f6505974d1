﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NBModRoadQueryByFile : LTEModRoadQueryByFile
    {
        public NBModRoadQueryByFile(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "模三干扰_NB(按文件)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34012, this.Name);
        }
    }

    public class NBModRoadQueryByRegion : LTEModRoadQueryByRegion
    {
        public NBModRoadQueryByRegion(ServiceName serviceName, MainModel mainModel)
            : base(serviceName, mainModel)
        {
        }

        public override string Name
        {
            get { return "模三干扰_NB(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34013, this.Name);
        }
    }
}
