﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTAtuLogKPIGroup
{
    public partial class ATUFileGroupForm : MinCloseForm
    {
        public ATUFileGroupForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
            initFixCol();
        }

        private void initFixCol()
        {
            lv.CanExpandGetter += delegate(object row)
            {
                return row is DeviceKPIInfo;
            };

            lv.ChildrenGetter += delegate(object row)
            {
                if (row is DeviceKPIInfo)
                {
                    DeviceKPIInfo info = row as DeviceKPIInfo;
                    return info.Files;
                }
                return null;
            };

            colFileNum.AspectGetter += delegate(object row)
            {
                if (row is DeviceKPIInfo)
                {
                    DeviceKPIInfo info = row as DeviceKPIInfo;
                    return info.Files.Count;
                }
                return null;
            };

            colSatisifiedNum.AspectGetter += delegate(object row)
            {
                if (row is DeviceKPIInfo)
                {
                    DeviceKPIInfo info = row as DeviceKPIInfo;
                    return info.SatisifiedFileNum;
                }
                return null;
            };

            colSatisifiedPer.AspectGetter += delegate(object row)
            {
                if (row is DeviceKPIInfo)
                {
                    DeviceKPIInfo info = row as DeviceKPIInfo;
                    return info.SatisifiedPer;
                }
                return null;
            };

            colSatisifiedPer.RendererDelegate = delegate(EventArgs e
                , Graphics g, Rectangle r, Object rowObject)
            {
                if (template != null && rowObject is DeviceKPIInfo)
                {
                    Color clr = getColor(rowObject);
                    if (clr == Color.Empty)
                    {
                        return false;
                    }

                    Rectangle rect = r;
                    rect.Inflate(-2, -2);
                    g.FillRectangle(new SolidBrush(clr), rect);
                    g.DrawString(((DeviceKPIInfo)rowObject).SatisifiedPer.ToString(), lv.Font,
                        new SolidBrush(lv.ForeColor), r.X, r.Y);
                    return true;
                }
                return false;
            };
        }

        private Color getColor(object rowObject)
        {
            Color clr = Color.Empty;
            double percent = ((DeviceKPIInfo)rowObject).SatisifiedPer;
            foreach (DTParameterRangeColor item in template.RangeColorSet)
            {
                if (item.Max == 100 && percent == 100)
                {
                    clr = item.Value;
                    break;
                }
                if ((item.MinIncluded ? percent >= item.Min : percent > item.Min)
                    && (item.MaxIncluded ? percent <= item.Max : percent < item.Max))
                {
                    clr = item.Value;
                    break;
                }
            }

            return clr;
        }

        GroupTemplate template = null;
        List<DeviceKPIInfo> deviceSet = null;
        public void FillData(GroupTemplate template, List<DeviceKPIInfo> deviceSet)
        {
            lv.ClearObjects();
            this.deviceSet = deviceSet;
            this.template = template;
            addCol2ListView(template.Options);
            lv.SetObjects(deviceSet);
        }

        List<OLVColumn> addtionalDisCols = new List<OLVColumn>();
        private void addCol2ListView(List<GroupIndicatorOption> list)
        {
            foreach (OLVColumn oldCol in addtionalDisCols)
            {
                lv.AllColumns.Remove(oldCol);
                lv.Columns.Remove(oldCol);
                oldCol.Dispose();
            }
            addtionalDisCols.Clear();
            foreach (GroupIndicatorOption colInfo in list)
            {
                OLVColumn col = createCol(colInfo.KPIName);
                lv.AllColumns.Add(col);
                lv.Columns.AddRange(new ColumnHeader[] { col });
                addtionalDisCols.Add(col);
                if (colInfo.StatPercent)
                {
                    col = createCol(colInfo.FileNumToken);
                    lv.AllColumns.Add(col);
                    lv.Columns.AddRange(new ColumnHeader[] { col });
                    addtionalDisCols.Add(col);

                    col = createCol(colInfo.FilePerToken);
                    lv.AllColumns.Add(col);
                    lv.Columns.AddRange(new ColumnHeader[] { col });
                    addtionalDisCols.Add(col);
                }
            }
            lv.RebuildColumns();
        }

        private OLVColumn createCol(string caption)
        {
            OLVColumn col = new OLVColumn();
            col.Text = caption;
            col.AspectGetter += delegate(object row)
            {
                if (row is KPIInfoBase)
                {
                    KPIInfoBase kpi = row as KPIInfoBase;
                    object value;
                    if (!kpi.KeyValueDic.TryGetValue(col.Text, out value))
                    {
                        return null;
                    }
                    else if (value != null)
                    {
                        double dVal;
                        if (double.TryParse(value.ToString(), out dVal))
                        {
                            if (double.IsNaN(dVal))
                            {
                                return "-";
                            }
                            return dVal;
                        }
                    }
                }
                return null;
            };
            return col;
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            lv.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            lv.CollapseAll();
        }

        private void miExport_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow titleRow = addTitleRow(rows);

            addRowValue(rows, titleRow);
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(rows);
        }

        private NPOIRow addTitleRow(List<NPOIRow> rows)
        {
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("设备号");
            titleRow.AddCellValue("文件总数");
            titleRow.AddCellValue("符合条件文件个数");
            titleRow.AddCellValue("符合条件文件占比（%）");
            foreach (OLVColumn col in addtionalDisCols)
            {
                titleRow.AddCellValue(col.Text);
            }
            titleRow.AddCellValue("文件");
            rows.Add(titleRow);
            return titleRow;
        }

        private void addRowValue(List<NPOIRow> rows, NPOIRow titleRow)
        {
            foreach (DeviceKPIInfo dev in deviceSet)
            {
                NPOIRow devRow = new NPOIRow();
                devRow.AddCellValue(dev.Name);
                devRow.AddCellValue(dev.FileNum);
                devRow.AddCellValue(dev.SatisifiedFileNum);
                devRow.AddCellValue(dev.SatisifiedPer);
                foreach (OLVColumn col in addtionalDisCols)
                {
                    double v;
                    double.TryParse(dev.KeyValueDic[col.Text].ToString(), out v);
                    if (double.IsNaN(v))
                    {
                        devRow.AddCellValue("-");
                    }
                    else
                    {
                        devRow.AddCellValue(v.ToString());
                    }
                }

                titleRow = addSubRowValue(titleRow, dev, devRow);
                rows.Add(devRow);
            }
        }

        private NPOIRow addSubRowValue(NPOIRow titleRow, DeviceKPIInfo dev, NPOIRow devRow)
        {
            foreach (FileKPIInfo file in dev.Files)
            {
                NPOIRow fiRow = new NPOIRow();
                devRow.subRows.Add(fiRow);
                fiRow.AddCellValue(file.Name);
                foreach (string str in file.KeyValueDic.Keys)
                {
                    if (titleRow != null)
                    {
                        titleRow.AddCellValue(str);
                    }

                    double v;
                    double.TryParse(file.KeyValueDic[str].ToString(), out v);
                    if (double.IsNaN(v))
                    {
                        fiRow.AddCellValue("-");
                    }
                    else
                    {
                        fiRow.AddCellValue(v.ToString());
                    }
                }
                titleRow = null;
            }

            return titleRow;
        }

        private void miExportLevelDev_Click(object sender, EventArgs e)
        {
            lv.CollapseAll();
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(lv);
        }

    }
}
