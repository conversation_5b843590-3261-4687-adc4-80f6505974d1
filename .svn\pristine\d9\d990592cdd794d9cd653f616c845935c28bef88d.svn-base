﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment_NR;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment
{
    public partial class ConditionDlg : BaseDialog
    {
        Form fileDescDlg = null;
        public ConditionDlg()
            : base()
        {
            InitializeComponent();
            gvFileDesc.Rows.Add("小区A", "超高");
            gvFileDesc.Rows.Add("小区A", "超远");
            gvFileDesc.Rows.Add("小区B", "超近");
            gvFileDesc.Rows.Add("...", "...");
            gvFileDesc.Visible = true;
            fileDescDlg = new Form();
            fileDescDlg.StartPosition = FormStartPosition.Manual;
            fileDescDlg.Owner = this;
            fileDescDlg.ShowInTaskbar = false;
            fileDescDlg.FormBorderStyle = FormBorderStyle.None;
            fileDescDlg.Controls.Add(gvFileDesc);
            gvFileDesc.Dock = DockStyle.Fill;
            fileDescDlg.Height = 150;
        }

        public void SetCondition(FuncCondition cond)
        {
            if (cond == null)
            {
                return;
            }
            radioByFile.Checked = cond.UltraCellByFile;
            radioByCell.Checked = !radioByFile.Checked;
            tbxGroupResult.Text = cond.FileName;

            numSiteDistance.Value = (decimal)cond.UltraSiteCondition.NearDistanceMin;
            numAltitude.Value = (decimal)cond.UltraSiteCondition.AltitudeMax;
            numDirRange.Value = (decimal)cond.UltraSiteCondition.AvgSitesDistanceAngle;
            numAvgDistance.Value = (decimal)cond.UltraSiteCondition.AvgSiteDistanceMax;
            numDiffBandDis.Value = (decimal)cond.UltraSiteCondition.DiffBandDistanceMin;

            numWeakCoverRSRP.Value = (decimal)cond.WeakCover.RSRP;
            numWeakCoverDis.Value = (decimal)cond.WeakCover.LastDistance;

            numOvrLapRSRP.Value = (decimal)cond.CoverLap.CoverlapRSRPMin;
            numCoverLapPer.Value = (decimal)cond.CoverLap.Rate;
            numSiteCnt.Value = (decimal)cond.CoverLap.CoverSiteNum;
            numCvrDisFactor.Value = (decimal)cond.CoverLap.CvrDisFactorMax;

            numMultiCvrRSRP.Value = (decimal)cond.MultiCover.RSRP;
            numMultiDistance.Value = (decimal)cond.MultiCover.LastDistance;
            numMultiGrade.Value = (decimal)cond.MultiCover.MultiGrade;
            numMultiRSRPDiff.Value = (decimal)cond.MultiCover.RSRPDiff;
            SameARFCN.Checked = cond.MultiCover.SameARFCN;

            numHoCnt.Value = (decimal)cond.OverHandover.HoCount;
            numHoDistance.Value = (decimal)cond.OverHandover.Distance;
            numHoSecond.Value = (decimal)cond.OverHandover.Second;

            numMod3Distance.Value = (decimal)cond.PoorSINR.LastDistance;
            numMod3RSRP.Value = (decimal)cond.PoorSINR.RSRP;
            numMod3SINR.Value = (decimal)cond.PoorSINR.SINR;

            numFarDistance.Value = (decimal)cond.FarCover.Distance;
            numFarRSRP.Value = (decimal)cond.FarCover.RSRP;
            numFarTpNum.Value = (decimal)cond.FarCover.TestPointNum;
        }
        public void SetCondition(FuncCondition_NR cond)
        {
            if (cond == null)
            {
                return;
            }
            radioByFile.Checked = cond.UltraCellByFile;
            radioByCell.Checked = !radioByFile.Checked;
            tbxGroupResult.Text = cond.FileName;

            numSiteDistance.Value = (decimal)cond.UltraSiteCondition.NearDistanceMin;
            numAltitude.Value = (decimal)cond.UltraSiteCondition.AltitudeMax;
            numDirRange.Value = (decimal)cond.UltraSiteCondition.AvgSitesDistanceAngle;
            numAvgDistance.Value = (decimal)cond.UltraSiteCondition.AvgSiteDistanceMax;
            numDiffBandDis.Value = (decimal)cond.UltraSiteCondition.DiffBandDistanceMin;

            numWeakCoverRSRP.Value = (decimal)cond.WeakCover.RSRP;
            numWeakCoverDis.Value = (decimal)cond.WeakCover.LastDistance;

            numOvrLapRSRP.Value = (decimal)cond.CoverLap.CoverlapRSRPMin;
            numCoverLapPer.Value = (decimal)cond.CoverLap.Rate;
            numSiteCnt.Value = (decimal)cond.CoverLap.CoverSiteNum;
            numCvrDisFactor.Value = (decimal)cond.CoverLap.CvrDisFactorMax;

            numMultiCvrRSRP.Value = (decimal)cond.MultiCover.RSRP;
            numMultiDistance.Value = (decimal)cond.MultiCover.LastDistance;
            numMultiGrade.Value = (decimal)cond.MultiCover.MultiGrade;
            numMultiRSRPDiff.Value = (decimal)cond.MultiCover.RSRPDiff;
            SameARFCN.Checked = cond.MultiCover.SameARFCN;

            numHoCnt.Value = (decimal)cond.OverHandover.HoCount;
            numHoDistance.Value = (decimal)cond.OverHandover.Distance;
            numHoSecond.Value = (decimal)cond.OverHandover.Second;

            numMod3Distance.Value = (decimal)cond.PoorSINR.LastDistance;
            numMod3RSRP.Value = (decimal)cond.PoorSINR.RSRP;
            numMod3SINR.Value = (decimal)cond.PoorSINR.SINR;

            numFarDistance.Value = (decimal)cond.FarCover.Distance;
            numFarRSRP.Value = (decimal)cond.FarCover.RSRP;
            numFarTpNum.Value = (decimal)cond.FarCover.TestPointNum;
        }

        public FuncCondition GetCondition()
        {
            FuncCondition cond = new FuncCondition();

            cond.UltraCellByFile = radioByFile.Checked;
            cond.FileName = tbxGroupResult.Text;

            UltraSiteCondition uCond = new UltraSiteCondition();
            uCond.NearDistanceMin = (double)numSiteDistance.Value;
            uCond.AltitudeMax = (double)numAltitude.Value;
            uCond.AvgSitesDistanceAngle = (int)numDirRange.Value;
            uCond.AvgSiteDistanceMax = (double)numAvgDistance.Value;
            uCond.DiffBandDistanceMin = (double)numDiffBandDis.Value;
            cond.UltraSiteCondition = uCond;

            cond.WeakCover.RSRP = (float)numWeakCoverRSRP.Value;
            cond.WeakCover.LastDistance = (double)numWeakCoverDis.Value;

            cond.CoverLap.CoverlapRSRPMin = (float)numOvrLapRSRP.Value;
            cond.CoverLap.Rate = (double)numCoverLapPer.Value;
            cond.CoverLap.CoverSiteNum = (int)numSiteCnt.Value;
            cond.CoverLap.CvrDisFactorMax = (double)numCvrDisFactor.Value;

            cond.FarCover.Distance = (double)numFarDistance.Value;
            cond.FarCover.RSRP = (float)numFarRSRP.Value;
            cond.FarCover.TestPointNum = (int)numFarTpNum.Value;

            cond.MultiCover.RSRP = (float)numMultiCvrRSRP.Value;
            cond.MultiCover.LastDistance = (double)numMultiDistance.Value;
            cond.MultiCover.MultiGrade = (int)numMultiGrade.Value;
            cond.MultiCover.RSRPDiff = (float)numMultiRSRPDiff.Value;
            cond.MultiCover.SameARFCN = SameARFCN.Checked;

            cond.OverHandover.HoCount = (int)numHoCnt.Value;
            cond.OverHandover.Distance = (double)numHoDistance.Value;
            cond.OverHandover.Second = (int)numHoSecond.Value;

            cond.PoorSINR.LastDistance = (double)numMod3Distance.Value;
            cond.PoorSINR.RSRP = (float)numMod3RSRP.Value;
            cond.PoorSINR.SINR = (float)numMod3SINR.Value;

            return cond;
        }
        public FuncCondition_NR GetCondition_NR()
        {
            FuncCondition_NR cond = new FuncCondition_NR();

            cond.UltraCellByFile = radioByFile.Checked;
            cond.FileName = tbxGroupResult.Text;

            UltraSiteCondition uCond = new UltraSiteCondition();
            uCond.NearDistanceMin = (double)numSiteDistance.Value;
            uCond.AltitudeMax = (double)numAltitude.Value;
            uCond.AvgSitesDistanceAngle = (int)numDirRange.Value;
            uCond.AvgSiteDistanceMax = (double)numAvgDistance.Value;
            uCond.DiffBandDistanceMin = (double)numDiffBandDis.Value;
            cond.UltraSiteCondition = uCond;

            cond.WeakCover.RSRP = (float)numWeakCoverRSRP.Value;
            cond.WeakCover.LastDistance = (double)numWeakCoverDis.Value;

            cond.CoverLap.CoverlapRSRPMin = (float)numOvrLapRSRP.Value;
            cond.CoverLap.Rate = (double)numCoverLapPer.Value;
            cond.CoverLap.CoverSiteNum = (int)numSiteCnt.Value;
            cond.CoverLap.CvrDisFactorMax = (double)numCvrDisFactor.Value;

            cond.FarCover.Distance = (double)numFarDistance.Value;
            cond.FarCover.RSRP = (float)numFarRSRP.Value;
            cond.FarCover.TestPointNum = (int)numFarTpNum.Value;

            cond.MultiCover.RSRP = (float)numMultiCvrRSRP.Value;
            cond.MultiCover.LastDistance = (double)numMultiDistance.Value;
            cond.MultiCover.MultiGrade = (int)numMultiGrade.Value;
            cond.MultiCover.RSRPDiff = (float)numMultiRSRPDiff.Value;
            cond.MultiCover.SameARFCN = SameARFCN.Checked;

            cond.OverHandover.HoCount = (int)numHoCnt.Value;
            cond.OverHandover.Distance = (double)numHoDistance.Value;
            cond.OverHandover.Second = (int)numHoSecond.Value;

            cond.PoorSINR.LastDistance = (double)numMod3Distance.Value;
            cond.PoorSINR.RSRP = (float)numMod3RSRP.Value;
            cond.PoorSINR.SINR = (float)numMod3SINR.Value;

            return cond;
        }

        private void btnBrs_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            dlg.Multiselect = false;
            dlg.RestoreDirectory = true;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                tbxGroupResult.Text = dlg.FileName;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (radioByFile.Checked)
            {
                if (string.IsNullOrEmpty(tbxGroupResult.Text))
                {
                    MessageBox.Show("请选取文件！");
                    return;
                }
                else if (!System.IO.File.Exists(tbxGroupResult.Text))
                {
                    MessageBox.Show("不存在文件：" + Environment.NewLine + tbxGroupResult.Text);
                    return;
                }
            }
            DialogResult = DialogResult.OK;
        }

        private void radioByFile_CheckedChanged(object sender, EventArgs e)
        {
            if (radioByFile.Checked)
            {
                grpFileCheck.Enabled = true;
                grpFar.Enabled = grpHigh.Enabled = grpNear.Enabled = false;
            }
            else
            {
                grpFileCheck.Enabled = false;
                grpFar.Enabled = grpHigh.Enabled = grpNear.Enabled = true;
            }
        }

        private void linkFileDesc_MouseClick(object sender, MouseEventArgs e)
        {
            Point location = linkFileDesc.PointToScreen(e.Location);
            fileDescDlg.Location = location;
            fileDescDlg.Visible = true;
        }

        private void gvFileDesc_MouseLeave(object sender, EventArgs e)
        {
            fileDescDlg.Visible = false;
        }


    }
}
