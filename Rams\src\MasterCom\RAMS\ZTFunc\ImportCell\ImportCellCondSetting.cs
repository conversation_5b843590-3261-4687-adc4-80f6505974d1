﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using System.IO;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ImportCells
{
    public partial class ImportCellCondSetting : BaseDialog
    {
        private string fileName = "";
        public ImportCellCondSetting()
        {
            InitializeComponent();
        }

        private void buttonGetModel_Click(object sender, EventArgs e)
        {
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = FilterHelper.Xlsx;
            if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK) return;
            string name = sfd.FileName;
            List<NPOIRow> listRow = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue(ColumnNames.CellName);
            row.AddCellValue(ColumnNames.Lon);
            row.AddCellValue(ColumnNames.Lat);
            row.AddCellValue(ColumnNames.Direction);
            listRow.Add(row);

            ExcelNPOIManager.ExportToExcel(listRow, name, "Sheet1");

            MessageBox.Show("导出成功!");
        }

        private void buttonGetFile_Click(object sender, EventArgs e)
        {
            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel |*.xls;*.xlsx"; 
            if (ofd.ShowDialog() != System.Windows.Forms.DialogResult.OK) return;
            this.textBoxFileName.Text = ofd.FileName;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.fileName = this.textBoxFileName.Text;
            if (!File.Exists(fileName))
            {
                MessageBox.Show("文件不存在，请填写有效的文件路径名!");
                return;
            }
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        public string GetResult()
        {
            return this.fileName;
        }
    }

    static class ColumnNames
    {
        public const string CellName = "物理小区名称";
        public const string Lon = "经度";
        public const string Lat = "纬度";
        public const string Direction = "方位角";
    }

    public class Cell_Import
    {
        public string CellName { get; set; }
        public double Lon { get; set; }
        public double Lat { get; set; }
        public double Direction { get; set; }
        public bool Within(double x1, double y1, double x2, double y2)
        {
            if (Lon < x1 || Lon > x2 || Lat < y1 || Lat > y2)
            {
                return false;
            }
            return true;
        }
    }
}
