﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Net;
using System.Text;
using MasterCom.RAMS.BackgroundFunc;
using EvtEngineLib;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTECauseValueAnaBase : DIYAnalyseByCellBackgroundBaseByFile
    {
        public List<ZTLTECauseValueAnaItem> resultList { get; set; } = new List<ZTLTECauseValueAnaItem>();    //保存结果
        protected bool isVoLTE = false;
        public ZTLTECauseValueAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = false;
            this.IncludeMessage = true;
#if AllRtpMsg
            this.IncludeAllRtpMessage = true;
#endif
            this.Columns = new List<string>();
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_PUSCH_Power");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_PUSCH_Power");
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList.Clear();
        }
        protected override void getReadyBeforeQuery()
        {
            if (mainModel.IsBackground)
            {
                ServiceTypes.Clear();
                ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
            }
            else
            {
                ServiceTypes.Clear();
                if (isVoLTE)
                {
                    ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                    ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                    ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                    ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
                }
                else
                {
                    ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
                    ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
                    ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
                    ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
                    ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
                    ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
                    ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
                    ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
                    ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);

                    ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
                    ServiceTypes.Add(ServiceType.LTE_SCAN_CW);
                    ServiceTypes.Add(ServiceType.LTE扫频_频谱分析);
                    ServiceTypes.Add(ServiceType.LTE_SIGNAL);
                }
            }
        }
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                if (mainModel.IsBackground && fileMng.TestPoints.Count <= 0)
                {
                    return;
                }

                List<DTData> dtDataList = new List<DTData>();
                addDataList(fileMng, dtDataList);

                TestPoint lastTp = null;
                ZTLTECauseValueAnaItem curItem = null;
                for (int i = 0; i < dtDataList.Count; i++)
                {
                    getValidItem(dtDataList, ref lastTp, ref curItem, i);
                }

                addToResultList(curItem, dtDataList);   //最后一个
            }
        }

        private void getValidItem(List<DTData> dtDataList, ref TestPoint lastTp, ref ZTLTECauseValueAnaItem curItem, int i)
        {
            if (dtDataList[i] is TestPoint)
            {
                lastTp = dtDataList[i] as TestPoint; //用最近的采样点经纬度来填充
            }
            else if (dtDataList[i] is Message)
            {
                Message dtMsg = dtDataList[i] as Message;
                if (dtMsg.ID == (int)EnumLteNBCheckMsg.ConnectionRestablishmentRequest)
                {
                    addToResultList(curItem, dtDataList);
                    curItem = new ZTLTECauseValueAnaItem(dtMsg, i, lastTp);
                }
                else if (dtMsg.ID == (int)EnumLteNBCheckMsg.RRCConnectionReestablishmentComplete
                        || dtMsg.ID == (int)EnumLteNBCheckMsg.RRCConnectionReestablishmentReject)
                {
                    setValidCurItem(dtDataList, curItem, i, dtMsg);
                }
            }
        }

        private void setValidCurItem(List<DTData> dtDataList, ZTLTECauseValueAnaItem curItem, int i, Message dtMsg)
        {
            if (curItem != null && curItem.RequestMsg != null)
            {
                curItem.ResultMsg = dtMsg;
                curItem.ResultMsgIndex = i;
                curItem.NextTp = getNextTp(dtDataList, i);
            }
            //没有request，不处理
        }

        private void addDataList(DTFileDataManager fileMng, List<DTData> dtDataList)
        {
            foreach (TestPoint tp in fileMng.TestPoints)
            {
                dtDataList.Add((DTData)tp);
            }

            foreach (Message msg in fileMng.Messages)
            {
                if (msg.ID == (int)EnumLteNBCheckMsg.ConnectionRestablishmentRequest
                    || msg.ID == (int)EnumLteNBCheckMsg.RRCConnectionReestablishmentComplete
                    || msg.ID == (int)EnumLteNBCheckMsg.RRCConnectionReestablishmentReject
                    || (msg.ID == 2147426825 && msg.Direction == 1))//IMS_RTP_SN_And_Payload下行信令
                {
                    dtDataList.Add((DTData)msg);
                }
            }

            dtDataList.Sort(comparer);
        }

        private TestPoint getNextTp(List<DTData> dtDataList, int curIndex)
        {
            TestPoint nextTp = null;
            if (curIndex < dtDataList.Count - 1)
            {
                DateTime curTime = dtDataList[curIndex].DateTime;
                for (int j = curIndex + 1; j < dtDataList.Count; j++)
                {
                    if ((dtDataList[j].DateTime - curTime).TotalSeconds >= 10)
                    {
                        break;
                    }
                    if (dtDataList[j] is TestPoint)
                    {
                        TestPoint tp = dtDataList[j] as TestPoint;
                        LTECell lteCell = tp.GetMainCell_LTE();
                        if (lteCell != null)
                        {
                            nextTp = tp;
                            break;
                        }
                    }
                }
            }
            return nextTp;
        }
        private void addToResultList(ZTLTECauseValueAnaItem item, List<DTData> listData)
        {
            if (item == null)
            {
                return;
            }

            //分析Request中的信息
            int causeValue = 0;
            string causeReason = "";
            int pciValue = 0;

            if (getCauseAndPCIFromConnReEstablishRequest(item.RequestMsg, ref causeValue, ref causeReason, ref pciValue))
            {
                item.setCauseAndPCI(causeValue, causeReason, pciValue);
            }

            //设置采样点及相关信息
            item.SetTpInfo(listData);

            //没有结果
            string result = "成功";
            if (item.ResultMsg == null)
            {
                result = "重建超时失败";
            }
            else
            {
                TimeSpan timeSpan = item.ResultMsg.DateTime - item.RequestMsg.DateTime;
                if (timeSpan.Milliseconds > 5000) //如果两个信令时间距离超过5秒，认为无效
                {
                    result = "开始与结束时间过长";
                }
                else
                {
                    if (item.ResultMsg.ID == (int)EnumLteNBCheckMsg.RRCConnectionReestablishmentReject)
                    {
                        result = "失败";
                    }
                }
            }

            item.Result = result;

            item.SN = resultList.Count + 1;
            resultList.Add(item);
        }

        private bool getCauseAndPCIFromConnReEstablishRequest(DTData lastRequest, ref int causeValue, ref string causeReason, ref int pciValue)
        {
            MessageWithSource msg = ((MessageWithSource)lastRequest);
            MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Source.Length, msg.ID);

            uint cause = 0;
            uint pci = 0;

            if (MessageDecodeHelper.GetSingleUInt("lte-rrc.reestablishmentCause", ref cause)
                && MessageDecodeHelper.GetSingleUInt("lte-rrc.physCellId", ref pci))
            {
                if ((int)cause == 0)
                {
                    causeValue = (int)cause;
                    pciValue = (int)pci;
                    causeReason = "reconfigurationtionFailure";
                }
                else if ((int)cause == 1)
                {
                    causeValue = (int)cause;
                    pciValue = (int)pci;
                    causeReason = "handoverFailure";
                }
                else if ((int)cause == 2)
                {
                    causeValue = (int)cause;
                    pciValue = (int)pci;
                    causeReason = "otherFailure";
                }
                else
                {
                    causeValue = (int)cause;
                    pciValue = (int)pci;
                    causeReason = "未解析原因值";
                }

                return true;
            }

            return false;
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            ZTLTECauseValueAnaListForm frm = MainModel.CreateResultForm(typeof(ZTLTECauseValueAnaListForm)) as ZTLTECauseValueAnaListForm;
            frm.FillData(resultList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            resultList.Clear();
        }

        private readonly Comparer comparer = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.切换; }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Simple; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CommonNoCondProperties(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (ZTLTECauseValueAnaItem item in resultList)
            {
                BackgroundResult result = item.ConvertToBackgroundResult(curAnaFileInfo);
                result.SubFuncID = GetSubFuncID();
                result.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, bgResultList);
            resultList.Clear();
        }
        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                bgResult.StatType = BackgroundStatType.None;//导出专题结果时只需要导出附加信息，这里StatType设为None

                string srcCellid = bgResult.GetImageValueString();
                string srcPUSCHPower = bgResult.GetImageValueString();
                string nextTAC = bgResult.GetImageValueString();
                string nextECI = bgResult.GetImageValueString();
                string nextCellID = bgResult.GetImageValueString();
                string nextEARFCN = bgResult.GetImageValueString();
                string nextPCI = bgResult.GetImageValueString();
                string nextTime = bgResult.GetImageValueString();
                float nextLongitude = bgResult.GetImageValueFloat();
                float nextLatitude = bgResult.GetImageValueFloat();
                float nextRSRP = bgResult.GetImageValueFloat();
                float nextSINR = bgResult.GetImageValueFloat();
                string nextPUSCHPower = bgResult.GetImageValueString();
                string resultDes = bgResult.GetImageValueString();
                int causeValue = bgResult.GetImageValueInt();
                string causeReason = bgResult.GetImageValueString();
                int rrcPCI = bgResult.GetImageValueInt();

                string beginTime;
                try
                {
                    beginTime = bgResult.GetImageValueString();
                }
                catch
                {
                    beginTime = bgResult.DateTimeBeginString;
                }

                StringBuilder strb = new StringBuilder();
                strb.Append("文件名称：" + bgResult.FileName + "\r\n");
                strb.Append("重建前小区名称：" + bgResult.CellName + "\r\n");
                strb.Append("重建前TAC：" + bgResult.LAC + "\r\n");
                strb.Append("重建前ECI：" + bgResult.CI + "\r\n");
                strb.Append("重建前CellID：" + srcCellid + "\r\n");
                strb.Append("重建前EARFCN：" + bgResult.BCCH + "\r\n");
                strb.Append("重建前PCI：" + bgResult.BSIC + "\r\n");
                strb.Append("重建前时间：" + beginTime + "\r\n");
                strb.Append("重建前小区经度：" + bgResult.LongitudeMid + "\r\n");
                strb.Append("重建前小区纬度：" + bgResult.LatitudeMid + "\r\n");
                strb.Append("重建前小区RSRP：" + bgResult.RxLevMean + "\r\n");
                strb.Append("重建前小区SINR：" + bgResult.RxQualMean + "\r\n");
                strb.Append("重建前PUSCHPower：" + srcPUSCHPower + "\r\n");
                strb.Append("重建后小区名称：" + bgResult.CellIDDesc + "\r\n");
                strb.Append("重建后TAC：" + nextTAC + "\r\n");
                strb.Append("重建后ECI：" + nextECI + "\r\n");
                strb.Append("重建后CellID：" + nextCellID + "\r\n");
                strb.Append("重建后EARFCN：" + nextEARFCN + "\r\n");
                strb.Append("重建后PCI：" + nextPCI + "\r\n");
                strb.Append("重建后时间：" + nextTime + "\r\n");
                strb.Append("重建后小区经度：" + nextLongitude + "\r\n");
                strb.Append("重建后小区纬度：" + nextLatitude + "\r\n");
                strb.Append("重建后RSRP：" + nextRSRP + "\r\n");
                strb.Append("重建后SINR：" + nextSINR + "\r\n");
                strb.Append("重建后PUSCHPower：" + nextPUSCHPower + "\r\n");
                strb.Append("结果：" + resultDes + "\r\n");
                strb.Append("Cause值：" + causeValue + "\r\n");
                strb.Append("Cause原因：" + causeReason + "\r\n");
                strb.Append("重建信令中PCI：" + rrcPCI);
                bgResult.ImageDesc = strb.ToString();
            }
        }
        #endregion
    }

    public class ZTLTECauseValueAnaItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }

        public LTECell ServCell { get; set; }
        public LTECell NextServCell { get; set; }
        public string CellName { get; set; }
        public string NextCellName { get; set; }
        public string TAC { get; set; }
        public string NextTAC { get; set; }
        public string ECI { get; set; }
        public string NextECI { get; set; }
        public string CellID { get; set; }
        public string NextCellID { get; set; }
        public string EARFCN { get; set; }
        public string NextEARFCN { get; set; }
        public string PCI { get; set; }
        public string NextPCI { get; set; }
        public string PuschPower { get; set; }
        public string NPuschPower { get; set; }

        public MasterCom.RAMS.Model.Message RequestMsg { get; set; }
        public MasterCom.RAMS.Model.Message ResultMsg { get; set; } //complete or reject or null

        public TestPoint Tp { get; set; }
        public TestPoint NextTp { get; set; }
        public string TpTime { get; set; }
        public string NextTpTime { get; set; }
        public double TpLongitude { get; set; }
        public double NextTpLongitude { get; set; }
        public double TpLatitude { get; set; }
        public double NextTpLatitude { get; set; }
        public float TpRSRP { get; set; }
        public float NextTpRSRP { get; set; }
        public float TpSINR { get; set; }
        public float NextTpSINR { get; set; }

        public string Result { get; set; }
        public int CauseValue { get; set; }
        public string CauseReason { get; set; }
        public int PCIValue { get; set; }

        #region 重建前1秒
        private int RequestMsgIndex { get; set; }
        public short? PuschPowerBef1s { get; set; }
        public float? TpRSRPBef1s { get; set; }
        public float? TpSINRBef1s { get; set; }
        #endregion

        public int ResultMsgIndex { get; set; }

        public ZTLTECauseValueAnaItem(MasterCom.RAMS.Model.Message requestMsg, int requestMsgIndex, TestPoint tp)
        {
            RequestMsgIndex = requestMsgIndex;
            RequestMsg = requestMsg;
            Tp = tp;
        }

        public void setCauseAndPCI(int causeValue, string causeReason, int pciValue)
        {
            CauseValue = causeValue;
            CauseReason = causeReason;
            PCIValue = pciValue;
        }

        public void SetTpInfo(List<DTData> listData)
        {
            setBeforeRebuildInfo(listData);
            setAfterRebuildInfo();
            setBef1SecInfo(listData);
        }

        private void setBef1SecInfo(List<DTData> listData)
        {
            #region 重建前1秒
            DateTime timeRequest = RequestMsg.DateTime;
            for (int i = RequestMsgIndex - 1; i > 0; i--)
            {
                if (listData[i] is TestPoint)
                {
                    DateTime time = listData[i].DateTime;

                    if ((timeRequest - time).TotalMilliseconds >= 1000)
                    {
                        TestPoint testPoint = listData[i] as TestPoint;
                        PuschPowerBef1s = getPower(testPoint);
                        TpRSRPBef1s = getRSRP(testPoint);
                        TpSINRBef1s = getSINR(testPoint);
                        break;
                    }
                }
            }
            #endregion
        }

        private void setAfterRebuildInfo()
        {
            #region 重建后采样点信息
            if (NextTp != null)
            {
                setNextServCell();

                short? npusch_power = getPower(NextTp);
                NextTpTime = NextTp.DateTimeStringWithMillisecond;
                NextTpLongitude = NextTp.Longitude;
                NextTpLatitude = NextTp.Latitude;
                float? nrsrp = getRSRP(NextTp);
                if (nrsrp != null)
                {
                    NextTpRSRP = (float)nrsrp;
                }
                float? nsinr = getSINR(NextTp);
                if (nsinr != null)
                {
                    NextTpSINR = (float)nsinr;
                }
                if (npusch_power != null)
                {
                    NPuschPower = npusch_power.ToString();
                }
            }
            #endregion
        }

        private void setNextServCell()
        {
            int? ntac = getTAC(NextTp);
            int? neci = getECI(NextTp);
            int? nearfcn = getEARFCN(NextTp);
            int? npci = getPCI(NextTp);
            LTECell nServCell = CellManager.GetInstance().GetNearestLTECell(NextTp.DateTime, ntac, neci, nearfcn, npci, NextTp.Longitude, NextTp.Latitude);

            if (nServCell != null)
            {
                NextServCell = nServCell;
                NextCellName = nServCell.Name;
                NextTAC = nServCell.TAC.ToString();
                NextECI = nServCell.ECI.ToString();
                NextEARFCN = nServCell.EARFCN.ToString();
                NextPCI = nServCell.PCI.ToString();
                NextCellID = nServCell.CellID.ToString();
            }
            else
            {
                NextCellName = "";
                if (ntac != null && ntac != 65535)
                {
                    NextTAC = ntac.ToString();
                }
                if (neci != null)
                {
                    NextECI = neci.ToString();
                }
                if (nearfcn != null)
                {
                    NextEARFCN = nearfcn.ToString();
                }
                if (npci != null)
                {
                    NextPCI = npci.ToString();
                }
            }
        }

        private void setBeforeRebuildInfo(List<DTData> listData)
        {
            #region 重建前采样点信息
            if (Tp != null)
            {
                setServCell();

                //TestPoint Info
                short? pusch_power = getPower(Tp);
                FileName = Tp.FileName;
                TpTime = Tp.DateTimeStringWithMillisecond;
                TpLongitude = Tp.Longitude;
                TpLatitude = Tp.Latitude;
                float? rsrp = getRSRP(Tp);
                if (rsrp != null)
                {
                    TpRSRP = (float)rsrp;
                }
                float? sinr = getSINR(Tp);
                if (sinr != null)
                {
                    TpSINR = (float)sinr;
                }
                if (pusch_power != null)
                {
                    PuschPower = pusch_power.ToString();
                }
            }
#if AllRtpMsg
            SetUpAndDownLostPacketNum(RequestMsgIndex, listData);//添加上行或者下行丢包数
#endif
#if DEBUG
            Console.Write(listData);
#endif
            #endregion
        }

        private void setServCell()
        {
            //Cell Info
            int? tac = getTAC(Tp);
            int? eci = getECI(Tp);
            int? earfcn = getEARFCN(Tp);
            int? pci = getPCI(Tp);

            LTECell servCell = CellManager.GetInstance().GetNearestLTECell(Tp.DateTime, tac, eci, earfcn, pci, Tp.Longitude, Tp.Latitude);

            if (servCell != null)    //没有匹配到主服工参
            {
                ServCell = servCell;
                CellName = servCell.Name;
                TAC = servCell.TAC.ToString();
                ECI = servCell.ECI.ToString();
                EARFCN = servCell.EARFCN.ToString();
                PCI = servCell.PCI.ToString();
                CellID = servCell.CellID.ToString();
            }
            else
            {
                CellName = "";
                if (tac != null && tac != 65535)
                {
                    TAC = tac.ToString();
                }
                if (eci != null)
                {
                    ECI = eci.ToString();
                }
                if (earfcn != null)
                {
                    EARFCN = earfcn.ToString();
                }
                if (pci != null)
                {
                    PCI = pci.ToString();
                }
            }
        }

        private long downLostPacketNum =0;
        public string DownLostPacketNum
        {
            get
            {
                return downLostPacketNum.ToString();
            }
        }
        public void SetUpAndDownLostPacketNum(int msgIndex, List<DTData> listData)
        {
            Message preMsg = null;//包含方向判断的前一个信令
            Message nextMsg = null;//后一个信令
            int msgIdPayload = 2147426825;           //IMS_RTP_SN_AND_Payload信令的数字

            for (int i = msgIndex; i < listData.Count; i++)
            {
                if (listData[i] is Message && ((Message)listData[i]).ID == msgIdPayload)
                {
                    nextMsg = (Message)listData[i];
                    break;
                }
            }
            if (nextMsg != null)
            {
                for (int i = msgIndex - 1; i > 0; i--)
                {
                    if (listData[i] is Message && ((Message)listData[i]).ID == msgIdPayload 
                        && ((Message)listData[i]).Direction == nextMsg.Direction)
                    {
                        preMsg = (Message)listData[i];
                        break;
                    }
                }
            }
            if (preMsg != null)
            {
                downLostPacketNum = GetLossNum(preMsg, nextMsg);//下行丢包数
            }
        }
        private readonly string signRTPNumber = "eSam_VOLTE_RTP_Packets_Lost_Num";
        private long GetLossNum(Model.Message msgStart, Model.Message msgEnd)
        {
            long result = 0;
            long lossNumStart = 0;
            long lossNumEnd = 0;
            MessageWithSource msgStartWithSource = msgStart as MessageWithSource;
            MessageWithSource msgEndWithSource = msgEnd as MessageWithSource;

            OwnMsgDecode.StartDissect(msgStartWithSource.Source, msgStartWithSource.ID);
            OwnMsgDecode.GetIntValue(signRTPNumber, ref lossNumStart);
            OwnMsgDecode.StartDissect(msgEndWithSource.Source, msgEndWithSource.ID);
            OwnMsgDecode.GetIntValue(signRTPNumber, ref lossNumEnd);

            result = lossNumEnd - lossNumStart;
            return result;
        }

        protected float? getRSRP(TestPoint tp)
        {
            return tp == null ? null : tp.GetRxlev();
        }
        protected float? getSINR(TestPoint tp)
        {
            if (tp == null)
            {
                return null;
            }
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_SINR"];
            }
            return (float?)tp["lte_SINR"];
        }
        protected int? getTAC(TestPoint tp)
        {
            return tp == null ? null : tp.GetLAC();
        }
        protected int? getECI(TestPoint tp)
        {
            return tp == null ? null : tp.GetCI();
        }
        protected int? getEARFCN(TestPoint tp)
        {
            return tp == null ? null : tp.GetBCCH();
        }
        protected int? getPCI(TestPoint tp)
        {
            return tp == null ? null : tp.GetBSIC();
        }
        protected short? getPower(TestPoint tp)
        {
            if (tp == null)
            {
                return null;
            }
            if (tp is LTEFddTestPoint)
            {
                return (short?)tp["lte_fdd_PUSCH_Power"];
            }
            return (short?)tp["lte_PUSCH_Power"];
        }

        public BackgroundResult ConvertToBackgroundResult(FileInfo file)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.CellType = BackgroundCellType.LTE;
            if (file != null)
            {
                bgResult.FileID = file.ID;
                bgResult.FileName = file.Name;
            }
            bgResult.ISTime = Tp == null ? RequestMsg.Time : Tp.Time;
            bgResult.IETime = NextTp == null ? RequestMsg.Time : NextTp.Time;
            string beginTime = Tp == null ? RequestMsg.DateTimeStringWithMillisecond : Tp.DateTimeStringWithMillisecond;

            if (ServCell != null)
            {
                bgResult.LAC = ServCell.TAC;
                bgResult.CI = ServCell.ECI;
                bgResult.BCCH = ServCell.EARFCN;
                bgResult.BSIC = ServCell.PCI;
                bgResult.LongitudeMid = ServCell.Longitude;
                bgResult.LatitudeMid = ServCell.Latitude;
            }
            bgResult.RxLevMean = TpRSRP;
            bgResult.RxQualMean = TpSINR;
            bgResult.CellIDDesc = NextCellName;

            bgResult.AddImageValue(CellID);
            bgResult.AddImageValue(PuschPower);
            bgResult.AddImageValue(NextTAC);
            bgResult.AddImageValue(NextECI);
            bgResult.AddImageValue(NextCellID);
            bgResult.AddImageValue(NextEARFCN);
            bgResult.AddImageValue(NextPCI);
            bgResult.AddImageValue(NextTpTime);
            bgResult.AddImageValue((float)NextTpLongitude);
            bgResult.AddImageValue((float)NextTpLatitude);
            bgResult.AddImageValue(NextTpRSRP);
            bgResult.AddImageValue(NextTpSINR);
            bgResult.AddImageValue(NPuschPower);
            bgResult.AddImageValue(Result);
            bgResult.AddImageValue(CauseValue);
            bgResult.AddImageValue(CauseReason);
            bgResult.AddImageValue(PCIValue);
            bgResult.AddImageValue(beginTime);

            return bgResult;
        }
    }
}
