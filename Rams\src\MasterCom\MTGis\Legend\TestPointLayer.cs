﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.MTGis
{
    public class TestPointLayer : LayerWithLegendBase<TestPoint>
    {
        public TestPointLayer(MapOperation mapOp, string name)
            : base(mapOp, name)
        {
        }
        public override object Tag
        {
            get
            {
                return base.Tag;
            }
            set
            {
                base.Tag = value;
                if (Legends != null)
                {
                    foreach (LegendGroup<TestPoint> item in Legends)
                    {
                        TestPointLegend legend = item as TestPointLegend;
                        legend.Tag = this.Tag;
                    }
                }
            }
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (Entitys2Draw == null || Entitys2Draw.Count == 0
            || Legends == null || Legends.Count == 0)
            {
                return;
            }

            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(Map.Scale);
            updateRect.Inflate((int)(64 * ratio), (int)(64 * ratio));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            
            foreach (TestPoint tp in Entitys2Draw)
            {
                if (tp.Within(dRect))
                {
                    PointF location = getDisplayLocation(tp);
                    foreach (LegendGroup<TestPoint> item in Legends)
                    {
                        TestPointLegend legend = item as TestPointLegend;
                        legend.DrawOnLayer(Map, graphics, location, tp);
                    }
                }
            }
        }

        private PointF getDisplayLocation(TestPoint tp)
        {
            PointF pntF;
            Map.ToDisplay(new DbPoint(tp.Longitude, tp.Latitude), out pntF);
            return pntF;
        }

    }

}
