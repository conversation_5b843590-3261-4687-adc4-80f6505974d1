﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTAntCfgForm : BaseDialog
    {
        bool isDtFunc;
        public ZTAntCfgForm(bool isDtFunc)
        {
            InitializeComponent();
            this.isDtFunc = isDtFunc;
            init();
        }

        public bool BDtNbInfo { get; set; }
        public bool BSecAna { get; set; }
        public bool BSecDataExport { get; set; }
        public bool BSampleShow { get; set; }
        public bool BDTCellName { get; set; }
        public List<String> cellNameList { get; set; }

        /// <summary>
        /// 初始化
        /// </summary>
        private void init()
        {
            chbDtNb.Enabled = isDtFunc;
            chbByCellName.Enabled = isDtFunc;
            
            tbPath.Text = Application.StartupPath + "\\userData";
            chbCsvFile.Enabled = false;
            tbPath.Enabled = false;
            btnBrowse.Enabled = false; 
            tBCell.Enabled = false;

            BDtNbInfo = false;//是否加载邻区信息
            BSecAna = false;//二维数据分析
            BSecDataExport = false;//二维数据导出
            BSampleShow = true;
            BDTCellName = false;//是否按小区查询
            cellNameList = new List<string>();//用于指定查询小区的列表
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (chbByCellName.Checked && tBCell.Text != "" && isDtFunc)
            {
                BDTCellName = true;
                cellNameList.Clear();
                string strCellNames = tBCell.Text;//小区名称信息
                char[] c = { ',', ';', '，', '；' };
                string[] cellNames = strCellNames.Split(c);
                foreach (string cellName in cellNames)
                {
                    cellNameList.Add(cellName);
                }
            }
            else
            {
                BDTCellName = false;
            }
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void chbSecAna_CheckedChanged(object sender, EventArgs e)
        {
            BSecAna = chbSecAna.Checked;
            chbCsvFile.Enabled = BSecAna;
        }

        private void chbCsvFile_CheckedChanged(object sender, EventArgs e)
        {
            BSecDataExport = chbCsvFile.Checked;
            tbPath.Enabled = BSecDataExport;
            btnBrowse.Enabled = BSecDataExport;
        }

        private void chbSample_CheckedChanged(object sender, EventArgs e)
        {
            BSampleShow = chbSample.Checked;
        }

        private void chbDtNb_CheckedChanged(object sender, EventArgs e)
        {
            BDtNbInfo = chbDtNb.Checked;
        }

        private void chbByCellName_CheckedChanged(object sender, EventArgs e)
        {
            tBCell.Enabled = chbByCellName.Checked;
            if (chbByCellName.Checked)
                BDTCellName = true;
            else
                BDTCellName = false;
        }
    }
}
