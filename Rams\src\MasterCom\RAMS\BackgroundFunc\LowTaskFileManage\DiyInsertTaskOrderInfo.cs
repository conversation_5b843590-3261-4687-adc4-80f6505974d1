﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class DiyInsertTaskOrderInfo : DiySqlMultiNonQuery
    {
        private readonly Dictionary<string, TaskOrderManageInfo> orderFileDic = null;
        public DiyInsertTaskOrderInfo(Dictionary<string, TaskOrderManageInfo> orderFileDic)
        {
            MainDB = true;
            this.orderFileDic = orderFileDic;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            catch (Exception ee)
            {
                ErrorInfo = "插入数据时发生异常";
                log.Error(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (TaskOrderManageInfo taskOrder in orderFileDic.Values)
            {
                strb.AppendFormat(@"IF EXISTS(SELECT 1 FROM [tb_lowtask_file] where [工单号] = '{0}') begin DELETE FROM [tb_lowtask_file] where [工单号] = '{0}' DELETE FROM [tb_lowtask_result] where [工单号] = '{0}' end ", 
                    taskOrder.OrderID);
                foreach (var orderFile in taskOrder.OrderFileList)
                {
                    strb.AppendFormat(@"insert into [tb_lowtask_file] ([地市],[工单号],[开始时间],[结束时间],[文件名],[导入时间]) values ('{0}','{1}','{2}','{3}','{4}','{5}');", 
                        orderFile.Area, taskOrder.OrderID, taskOrder.StartDateTime, taskOrder.EndDateTime,orderFile.FileName, taskOrder.ImportDataTime);
                }
            }
            return strb.ToString();
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    ErrorInfo = "入库失败";
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    ErrorInfo = "入库异常";
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "导入工单信息表"; }
        }
    }
}
