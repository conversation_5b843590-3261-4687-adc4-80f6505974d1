﻿namespace MasterCom.RAMS.Func.PopShow
{
    partial class WirelessNetworkMonitoringInfoPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.panel1 = new System.Windows.Forms.Panel();
            this.btnExportAll = new System.Windows.Forms.Button();
            this.btnExportThisPage = new System.Windows.Forms.Button();
            this.lblTitle = new System.Windows.Forms.Label();
            this.panel2 = new System.Windows.Forms.Panel();
            this.textboxProject = new System.Windows.Forms.TextBox();
            this.radioButtonByBoth = new System.Windows.Forms.RadioButton();
            this.radioButtonByGather = new System.Windows.Forms.RadioButton();
            this.radioButtonByEquipment = new System.Windows.Forms.RadioButton();
            this.btnForward = new System.Windows.Forms.Button();
            this.btnBack = new System.Windows.Forms.Button();
            this.btnFresh = new System.Windows.Forms.Button();
            this.checkedCbxDate = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.cbxShowType = new System.Windows.Forms.ComboBox();
            this.label6 = new System.Windows.Forms.Label();
            this.btnShowCityNames = new System.Windows.Forms.Button();
            this.labelDate = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedCbxDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.BackColor = System.Drawing.Color.Silver;
            this.panel1.Controls.Add(this.btnExportAll);
            this.panel1.Controls.Add(this.btnExportThisPage);
            this.panel1.Controls.Add(this.lblTitle);
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(990, 30);
            this.panel1.TabIndex = 1;
            // 
            // btnExportAll
            // 
            this.btnExportAll.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExportAll.Location = new System.Drawing.Point(912, 3);
            this.btnExportAll.Name = "btnExportAll";
            this.btnExportAll.Size = new System.Drawing.Size(75, 23);
            this.btnExportAll.TabIndex = 1;
            this.btnExportAll.Text = "导出所有页面";
            this.btnExportAll.UseVisualStyleBackColor = true;
            this.btnExportAll.Click += new System.EventHandler(this.btnExportAll_Click);
            // 
            // btnExportThisPage
            // 
            this.btnExportThisPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExportThisPage.Location = new System.Drawing.Point(831, 3);
            this.btnExportThisPage.Name = "btnExportThisPage";
            this.btnExportThisPage.Size = new System.Drawing.Size(75, 23);
            this.btnExportThisPage.TabIndex = 2;
            this.btnExportThisPage.Text = "导出本页";
            this.btnExportThisPage.UseVisualStyleBackColor = true;
            this.btnExportThisPage.Click += new System.EventHandler(this.btnExportThisPage_Click);
            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblTitle.ForeColor = System.Drawing.Color.White;
            this.lblTitle.Location = new System.Drawing.Point(14, 8);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(61, 14);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "KPI指标";
            // 
            // panel2
            // 
            this.panel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel2.Controls.Add(this.textboxProject);
            this.panel2.Controls.Add(this.radioButtonByBoth);
            this.panel2.Controls.Add(this.radioButtonByGather);
            this.panel2.Controls.Add(this.radioButtonByEquipment);
            this.panel2.Controls.Add(this.btnForward);
            this.panel2.Controls.Add(this.btnBack);
            this.panel2.Controls.Add(this.btnFresh);
            this.panel2.Controls.Add(this.checkedCbxDate);
            this.panel2.Controls.Add(this.cbxShowType);
            this.panel2.Controls.Add(this.label6);
            this.panel2.Controls.Add(this.btnShowCityNames);
            this.panel2.Controls.Add(this.labelDate);
            this.panel2.Controls.Add(this.label4);
            this.panel2.Controls.Add(this.label3);
            this.panel2.Controls.Add(this.label2);
            this.panel2.Location = new System.Drawing.Point(11, 36);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(971, 59);
            this.panel2.TabIndex = 6;
            // 
            // textboxProject
            // 
            this.textboxProject.Location = new System.Drawing.Point(79, 4);
            this.textboxProject.Name = "textboxProject";
            this.textboxProject.ReadOnly = true;
            this.textboxProject.Size = new System.Drawing.Size(215, 21);
            this.textboxProject.TabIndex = 26;
            // 
            // radioButtonByBoth
            // 
            this.radioButtonByBoth.AutoSize = true;
            this.radioButtonByBoth.Checked = true;
            this.radioButtonByBoth.Location = new System.Drawing.Point(763, 6);
            this.radioButtonByBoth.Name = "radioButtonByBoth";
            this.radioButtonByBoth.Size = new System.Drawing.Size(95, 16);
            this.radioButtonByBoth.TabIndex = 24;
            this.radioButtonByBoth.TabStop = true;
            this.radioButtonByBoth.Text = "按设备和汇总";
            this.radioButtonByBoth.UseVisualStyleBackColor = true;
            // 
            // radioButtonByGather
            // 
            this.radioButtonByGather.AutoSize = true;
            this.radioButtonByGather.Location = new System.Drawing.Point(698, 6);
            this.radioButtonByGather.Name = "radioButtonByGather";
            this.radioButtonByGather.Size = new System.Drawing.Size(59, 16);
            this.radioButtonByGather.TabIndex = 23;
            this.radioButtonByGather.Text = "按汇总";
            this.radioButtonByGather.UseVisualStyleBackColor = true;
            // 
            // radioButtonByEquipment
            // 
            this.radioButtonByEquipment.AutoSize = true;
            this.radioButtonByEquipment.Location = new System.Drawing.Point(633, 6);
            this.radioButtonByEquipment.Name = "radioButtonByEquipment";
            this.radioButtonByEquipment.Size = new System.Drawing.Size(59, 16);
            this.radioButtonByEquipment.TabIndex = 22;
            this.radioButtonByEquipment.Text = "按设备";
            this.radioButtonByEquipment.UseVisualStyleBackColor = true;
            // 
            // btnForward
            // 
            this.btnForward.Location = new System.Drawing.Point(724, 28);
            this.btnForward.Name = "btnForward";
            this.btnForward.Size = new System.Drawing.Size(43, 23);
            this.btnForward.TabIndex = 16;
            this.btnForward.Text = "前进";
            this.btnForward.UseVisualStyleBackColor = true;
            this.btnForward.Click += new System.EventHandler(this.btnForward_Click);
            // 
            // btnBack
            // 
            this.btnBack.Location = new System.Drawing.Point(675, 28);
            this.btnBack.Name = "btnBack";
            this.btnBack.Size = new System.Drawing.Size(43, 23);
            this.btnBack.TabIndex = 13;
            this.btnBack.Text = "后退";
            this.btnBack.UseVisualStyleBackColor = true;
            this.btnBack.Click += new System.EventHandler(this.btnBack_Click);
            // 
            // btnFresh
            // 
            this.btnFresh.Location = new System.Drawing.Point(572, 28);
            this.btnFresh.Name = "btnFresh";
            this.btnFresh.Size = new System.Drawing.Size(97, 23);
            this.btnFresh.TabIndex = 13;
            this.btnFresh.Text = "刷新";
            this.btnFresh.UseVisualStyleBackColor = true;
            this.btnFresh.Click += new System.EventHandler(this.btnFresh_Click);
            // 
            // checkedCbxDate
            // 
            this.checkedCbxDate.Location = new System.Drawing.Point(353, 28);
            this.checkedCbxDate.Name = "checkedCbxDate";
            this.checkedCbxDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.checkedCbxDate.Size = new System.Drawing.Size(211, 21);
            this.checkedCbxDate.TabIndex = 9;
            // 
            // cbxShowType
            // 
            this.cbxShowType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxShowType.FormattingEnabled = true;
            this.cbxShowType.Location = new System.Drawing.Point(79, 30);
            this.cbxShowType.Name = "cbxShowType";
            this.cbxShowType.Size = new System.Drawing.Size(215, 20);
            this.cbxShowType.TabIndex = 4;
            this.cbxShowType.SelectedIndexChanged += new System.EventHandler(this.cbxShowType_SelectedIndexChanged);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(570, 8);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(65, 12);
            this.label6.TabIndex = 21;
            this.label6.Text = "筛选方式：";
            // 
            // btnShowCityNames
            // 
            this.btnShowCityNames.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnShowCityNames.Location = new System.Drawing.Point(353, 2);
            this.btnShowCityNames.Name = "btnShowCityNames";
            this.btnShowCityNames.Size = new System.Drawing.Size(211, 23);
            this.btnShowCityNames.TabIndex = 20;
            this.btnShowCityNames.Text = "请选择地市↓";
            this.btnShowCityNames.UseVisualStyleBackColor = true;
            this.btnShowCityNames.Click += new System.EventHandler(this.btnShowCityNames_Click);
            // 
            // labelDate
            // 
            this.labelDate.AutoSize = true;
            this.labelDate.Location = new System.Drawing.Point(314, 33);
            this.labelDate.Name = "labelDate";
            this.labelDate.Size = new System.Drawing.Size(41, 12);
            this.labelDate.TabIndex = 16;
            this.labelDate.Text = "日期：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(314, 8);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(41, 12);
            this.label4.TabIndex = 15;
            this.label4.Text = "地市：";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(12, 33);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 1;
            this.label3.Text = "周期时间：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(12, 8);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "项目来源：";
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridView.BackgroundColor = System.Drawing.Color.White;
            this.dataGridView.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Location = new System.Drawing.Point(11, 101);
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.ReadOnly = true;
            this.dataGridView.RowHeadersVisible = false;
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.Size = new System.Drawing.Size(971, 246);
            this.dataGridView.TabIndex = 3;
            // 
            // WirelessNetworkMonitoringInfoPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.Transparent;
            this.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.Controls.Add(this.dataGridView);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Location = new System.Drawing.Point(75, 23);
            this.Name = "WirelessNetworkMonitoringInfoPanel";
            this.Size = new System.Drawing.Size(994, 387);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedCbxDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Button btnExportThisPage;
        private System.Windows.Forms.Button btnExportAll;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label labelDate;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnShowCityNames;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.ComboBox cbxShowType;
        private DevExpress.XtraEditors.CheckedComboBoxEdit checkedCbxDate;
        private System.Windows.Forms.Button btnFresh;
        private System.Windows.Forms.Button btnForward;
        private System.Windows.Forms.Button btnBack;
        private System.Windows.Forms.DataGridView dataGridView;
        private System.Windows.Forms.RadioButton radioButtonByBoth;
        private System.Windows.Forms.RadioButton radioButtonByGather;
        private System.Windows.Forms.RadioButton radioButtonByEquipment;
        private System.Windows.Forms.TextBox textboxProject;
    }
}
