﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Net
{
    public partial class ZTDIYCellWrongDirSettingForm : BaseForm
    {
        public ZTDIYCellWrongDirSettingForm()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        public void GetCondition(out double rxLevThreshold, out double distanceThreshold,
            out double angleThreshold, out double rateThreshold)
        {
            rxLevThreshold = (double)editMeanRxLev.Value;
            distanceThreshold = (double)editDistance.Value;
            angleThreshold = (double)editAngle.Value;
            rateThreshold = (double)editRate.Value / 100;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
