﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsBaseDataControl : LteMgrsConditionControlBase
    {
        public LteMgrsBaseDataControl()
        {
            InitializeComponent();
        }

        public override string Title
        {
            get { return "基准库设置"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            LteMgrsBaseSettingManager.Instance.BaseDataEnable = chkEnable.Checked;
            if (!chkEnable.Checked)
            {
                invalidReason = null;
                return null;
            }

            if (dtStart.Value.Date > dtEnd.Value.Date)
            {
                invalidReason = "开始时间不能大于结束时间";
                return null;
            }

            QueryCondition baseQueryCond = MainModel.GetInstance().MainForm.getQueryConditionBySettings();
            baseQueryCond.MultiTime = false;
            baseQueryCond.Periods.Clear();
            baseQueryCond.Periods.Add(new TimePeriod(this.dtStart.Value.Date, this.dtEnd.Value.Date.AddDays(1).AddSeconds(-1)));
            LteMgrsBaseSettingManager.Instance.BaseQueryCondition = baseQueryCond;

            invalidReason = null;
            return null;
        }
    }
}
