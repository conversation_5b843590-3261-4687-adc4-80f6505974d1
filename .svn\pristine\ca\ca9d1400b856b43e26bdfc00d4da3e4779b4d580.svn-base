﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRMobileServiceAnaFileItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }
        public List<NRMobileServiceAnaItem> AnaList { get; set; } = new List<NRMobileServiceAnaItem>();

        public NRMobileServiceAnaFileItem(string fileName)
        {
            FileName = fileName;
        }
    }

    public class NRMobileServiceAnaItem
    {
        public int SN { get; set; }
        public string TypeName { get; set; }
        public string URL { get; set; }
        public string Result { get; set; }
        public string FailReason { get; set; }      //失败原因

        public string SignalFirstName { get; set; }
        public string SignalFirstTime { get; set; }
        public string SignalSecondName { get; set; }
        public string SignalSecondTime { get; set; }
        public string SignalLastName { get; set; }
        public string SignalLastTime { get; set; }
        public string SignalSpanFirst2Second { get; set; }     //第一和第二信令间的时间差
        public string SignalSpanSecond2Last { get; set; }      //第二和最后信令间的时间差

        public float VideoReBufferCount { get; set; }    //视频卡顿次数
        public float VideoReBufferTime { get; set; }     //视频卡顿时长
        public float VideoLoadByte { get; set; }         //视频加载字节数
        public float VideoLoadTime { get; set; }         //视频加载时长，用于计算加载速率

        public string AppTotalByte { get; set; }      //传输字节
        public string AppTotalTime { get; set; }      //传输时间

        public float Rsrp { get; set; }
        public float RsrpCount { get; set; }
        public float Sinr { get; set; }
        public float SinrCount { get; set; }

        private double? appSpeedMax = null;
        public string AppSpeedMax
        {
            get
            {
                if (Result != "无结论" && appSpeedMax != null)
                {
                    return Math.Round((double)appSpeedMax, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public double AppSpeed { get; set; }
        public float AppSpeedCount { get; set; }

        public Event RequestEvt { get; set; }
        public Event SecondEvt { get; set; }     //记录，用于计算最后事件与第二事件间的时间差


        public bool IsGotBegin { get; set; }
        public bool IsGotEnd { get; set; }

        public Dictionary<string, int> cellDic { get; set; }
        private NRMobileServiceEvent mEvents;

        public NRMobileServiceAnaItem(NRMobileServiceEvent mEvents)
        {
            IsGotBegin = false;
            IsGotEnd = false;
            cellDic = new Dictionary<string, int>();
            this.mEvents = mEvents;
        }

        public void AddFirstEvtInfo(Event requestEvt, string url)
        {
            RequestEvt = requestEvt;
            IsGotBegin = true;

            if (requestEvt.ID == mEvents.structEvents.FTPDownloadBegan)
            {
                TypeName = "FTP";
                SignalFirstName = "FTPDownloadBegan";
            }
            else if (requestEvt.ID == mEvents.structEvents.HTTPPageRequest)
            {
                TypeName = "HTTP浏览";
                SignalFirstName = "HTTPPageRequest";
            }
            else if (requestEvt.ID == mEvents.structEvents.HTTPDownloadBegan)
            {
                TypeName = "HTTP下载";
                SignalFirstName = "HTTPDownloadBegan";

                //由于HTTP下载没有firstData，使用Began填充FirstData，重要！
                SignalSecondName = "HTTPDownloadFirstData";
                SignalSecondTime = requestEvt.DateTimeStringWithMillisecond;
                SignalSpanFirst2Second = "0";
                SecondEvt = requestEvt;
            }
            else if (requestEvt.ID == mEvents.structEvents.VIDEOPlayRequest)
            {
                TypeName = "VIDEO";
                SignalFirstName = "VIDEOPlayRequest";
            }
            URL = url;
            SignalFirstTime = requestEvt.DateTimeStringWithMillisecond;
        }

        public void AddOtherEvtInfo(Event otherEvt)
        {
            if (!IsGotBegin)
            {
                return;
            }

            if (RequestEvt.ID == mEvents.structEvents.FTPDownloadBegan)
            {
                DealWithFTP(otherEvt);
            }
            else if (RequestEvt.ID == mEvents.structEvents.HTTPPageRequest)
            {
                DealWithHTTPPage(otherEvt);
            }
            else if (RequestEvt.ID == mEvents.structEvents.HTTPDownloadBegan)
            {
                DealWithHTTPDownLoad(otherEvt);
            }
            else if (RequestEvt.ID == mEvents.structEvents.VIDEOPlayRequest)
            {
                DealWithVIDEO(otherEvt);
            }
        }

        public void DealWithFTP(Event otherEvt)
        {
            if (otherEvt.ID == mEvents.structEvents.FTPDownloadSuccess)
            {
                Result = "成功";
                SignalLastName = "FTPDownloadSuccess";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value4"].ToString();
                AppTotalByte = otherEvt["Value10"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.FTPDownloadDrop)
            {
                Result = "失败";
                SignalLastName = "FTPDownloadDrop";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value4"].ToString();
                AppTotalByte = otherEvt["Value10"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.FTPDownloadUnFinished)  //一般为文件末尾
            {
                Result = "未完成";
                SignalLastName = "FTPDownloadUnFinished";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value4"].ToString();
                AppTotalByte = otherEvt["Value10"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.FTPDownloadFirstData)
            {
                SignalSecondName = "FTPDownloadFirstData";
                SignalSecondTime = otherEvt.DateTimeStringWithMillisecond;
                SecondEvt = otherEvt;
                SignalSpanFirst2Second = GetTwoEvtIntervalDes(otherEvt, RequestEvt);
            }
        }

        public void DealWithHTTPPage(Event otherEvt)
        {
            if (otherEvt.ID == mEvents.structEvents.HTTPPageComplete)
            {
                Result = "成功";
                SignalLastName = "HTTPPageComplete";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPPageIncomplete)
            {
                Result = "失败";
                SignalLastName = "HTTPPageIncomplete";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPPageFail)
            {
                if (IsGotEnd)   //之前出现 HTTPPageDisplayFailure 
                {
                    AppTotalTime = otherEvt["Value1"].ToString();
                    AppTotalByte = otherEvt["Value2"].ToString();
                    FailReason = getHttpPageFailReason((long)otherEvt["Value3"]);
                }
                else
                {
                    Result = "失败";
                    SignalLastName = "HTTPPageFail";
                    SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                    SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                    IsGotEnd = true;
                }
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPPageDisplaySuccess)
            {
                SignalSecondName = "HTTPPageDisplaySuccess";
                SignalSecondTime = otherEvt.DateTimeStringWithMillisecond;
                SecondEvt = otherEvt;
                SignalSpanFirst2Second = GetTwoEvtIntervalDes(otherEvt, RequestEvt);
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPPageDisplayFailure)
            {
                Result = "失败";
                SignalSecondName = "HTTPPageDisplayFailure";
                SignalSecondTime = otherEvt.DateTimeStringWithMillisecond;
                SecondEvt = otherEvt;
                SignalSpanFirst2Second = GetTwoEvtIntervalDes(otherEvt, RequestEvt);
                IsGotEnd = true;
            }
        }

        private string getHttpPageFailReason(long evtValue)
        {
            switch (evtValue)
            {
                case 0:
                    return "APPNotDefined";
                case 5:
                    return "APPServiceReject";
                case 6:
                    return "APPServiceTimeout";
                case 7:
                    return "APPServiceFailure";
                case 8:
                    return "APPServiceDrop";
                default:
                    return evtValue.ToString();
            }
        }

        public void DealWithHTTPDownLoad(Event otherEvt)
        {
            if (otherEvt.ID == mEvents.structEvents.HTTPDownloadSuccess)
            {
                Result = "成功";
                SignalLastName = "HTTPDownloadSuccess";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPDownloadDrop)
            {
                Result = "失败";
                SignalLastName = "FTPDownloadDrop";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPDownloadFail)
            {

                Result = "失败";
                SignalLastName = "HTTPDownloadFail";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }

        }

        public void DealWithVIDEO(Event otherEvt)
        {
            if (otherEvt.ID == mEvents.structEvents.VIDEOPlayLastData)
            {
                Result = "成功";
                SignalLastName = "VIDEOPlayLastData";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                VideoLoadByte = (float)(long)otherEvt["Value7"];
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.VIDEOPlayDrop)
            {
                Result = "失败";
                SignalLastName = "VIDEOPlayDrop";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.VIDEOPlayReproductionStartFailure)
            {
                Result = "失败";
                SignalLastName = "VIDEOPlayReproductionStartFailure";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.VIDEOPlayFirstData)
            {
                SignalSecondName = "VIDEOPlayFirstData";
                SignalSecondTime = otherEvt.DateTimeStringWithMillisecond;
                SecondEvt = otherEvt;
                SignalSpanFirst2Second = GetTwoEvtIntervalDes(otherEvt, RequestEvt);
            }
            else if (otherEvt.ID == mEvents.structEvents.VIDEOPlayRebufferStart)
            {
                VideoReBufferCount++;
            }
            else if (otherEvt.ID == mEvents.structEvents.VIDEOPlayRebufferEnd)
            {
                VideoReBufferTime += (float)(long)otherEvt["Value2"];
            }
            else if (otherEvt.ID == mEvents.structEvents.VIDEOPlayReproductionStart)
            {
                VideoLoadTime = (float)(long)otherEvt["Value1"];
            }
        }

        public static string GetTwoEvtIntervalDes(Event curEvt, Event lastEvt)
        {
            if (curEvt != null && lastEvt != null)
            {
                return (curEvt.lTimeWithMillsecond - lastEvt.lTimeWithMillsecond).ToString();
            }
            return "";
        }

        public void AddTpInfo(TestPoint tp)
        {
            float? rsrp = GetRSRP(tp);
            if (rsrp != null)
            {
                Rsrp += (float)rsrp;
                RsrpCount++;
            }

            float? sinr = GetSINR(tp);
            if (sinr != null)
            {
                Sinr += (float)sinr;
                SinrCount++;
            }

            double? appSpeedMb = GetAppSpeed(tp);
            if (appSpeedMb != null)
            {
                appSpeedMax = appSpeedMax > appSpeedMb ? appSpeedMax : appSpeedMb;
                AppSpeed += (double)appSpeedMb;
                AppSpeedCount++;
            }

            //分析小区信息
            string cellName = getCellName(tp);

            if (cellDic.ContainsKey(cellName))
            {
                cellDic[cellName]++;
            }
            else
            {
                cellDic.Add(cellName, 1);
            }
        }

        private string getCellName(TestPoint tp)
        {
            NRCell cell = tp.GetMainCell_NR();
            string cellName = "";
            if (cell != null)
            {
                cellName = cell.Name;
            }
            else
            {
                if (GetTAC(tp) != null && GetNCI(tp) != null)
                {
                    int tac = (int)GetTAC(tp);
                    long nci = (long)GetNCI(tp);
                    cellName = tac.ToString() + "_" + nci.ToString();
                }
                else
                {
                    cellName = "未知小区";
                }
            }

            return cellName;
        }

        protected float? GetRSRP(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
        }
        protected float? GetSINR(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetSCellSinr(tp, true);
        }
        protected double? GetAppSpeed(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetAppSpeedMb(tp);
        }
        protected object GetTAC(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetTAC(tp);
        }
        protected object GetNCI(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetNCI(tp);
        }

        #region 预处理
        public string AppSpeedAvg
        {
            get
            {
                if (Result == "无结论")
                {
                    return "";
                }
                else
                {
                    if (AppSpeedCount > 0)
                    {
                        return Math.Round(AppSpeed / AppSpeedCount, 2).ToString();
                    }
                    else
                    {
                        return "";
                    }
                }
            }
        }
        public string RsrpAvg
        {
            get
            {
                if (Result == "无结论")
                {
                    return "";
                }
                else
                {
                    if (RsrpCount > 0)
                    {
                        return Math.Round(Rsrp / RsrpCount, 2).ToString();
                    }
                    else
                    {
                        return "";
                    }
                }
            }
        }

        public string SinrAvg
        {
            get
            {
                if (Result == "无结论")
                {
                    return "";
                }
                else
                {
                    if (SinrCount > 0)
                    {
                        return Math.Round(Sinr / SinrCount, 2).ToString();
                    }
                    else
                    {
                        return "";
                    }
                }
            }
        }

        public string SampleCount
        {
            get
            {
                if (Result == "无结论")
                {
                    return "";
                }
                else
                {
                    return RsrpCount.ToString();
                }
            }
        }

        public string CellInfos { get; private set; }

        public string ReBufferCount { get; private set; }

        public string ReBufferTime { get; private set; }

        public string LoadSpeed { get; private set; }

        public void Calculate()
        {
            CellInfos = getValidData(Result, cellDic.Count, getCellInfos, new object[] { cellDic });
            ReBufferCount = getValidData(VideoReBufferCount);
            ReBufferTime = getValidData(VideoReBufferTime);
            if (VideoLoadTime > 0)
            {
                LoadSpeed = Math.Round((VideoLoadByte * 8 * 1000) / (VideoLoadTime * 1024 * 1024), 2).ToString();
            }
        }

        string getCellInfos(params object[] objs)
        {
            Dictionary<string, int> cellDicTmp = objs[0] as Dictionary<string, int>;

            StringBuilder cellInfo = new StringBuilder();
            foreach (string cellName in cellDicTmp.Keys)
            {
                //小区A(5) | 小区B(11)
                cellInfo.Append(cellName + "(" + cellDicTmp[cellName].ToString() + ")" + " | ");
            }
            return cellInfo.ToString();
        }


        private string getValidData(float data)
        {
            if (data > 0)
            {
                return data.ToString();
            }
            return "";
        }

        private delegate string Func(params object[] objs);

        private string getValidData(string result, int count, Func func, params object[] objs)
        {
            if (result == "无结论")
            {
                return "";
            }
            else
            {
                if (count > 0)
                {
                    return func(objs);
                }
                else
                {
                    return "";
                }
            }
        }

        #endregion
    }

    public class NRMobileServiceEvent
    {
        public StructNRMobileServiceEvent structEvents { get; set; } = new StructNRMobileServiceEvent();
        public NRMobileServiceEvent()
        {
            structEvents.FTPDownloadBegan = (int)NREventManager.FTPDownloadBegan;
            structEvents.FTPDownloadFirstData = (int)NREventManager.FTPDownloadFirstData;
            structEvents.FTPDownloadSuccess = (int)NREventManager.FTPDownloadSuccess;
            structEvents.FTPDownloadDrop = (int)NREventManager.FTPDownloadDrop;
            structEvents.FTPDownloadUnFinished = (int)NREventManager.FTPDownloadUnFinished;

            structEvents.HTTPDownloadBegan = (int)NREventManager.DownRequest;
            structEvents.HTTPDownloadSuccess = (int)NREventManager.DownSuccess;
            structEvents.HTTPDownloadDrop = (int)NREventManager.DownDrop;
            structEvents.HTTPDownloadFail = (int)NREventManager.DownloadFail;

            structEvents.HTTPPageRequest = (int)NREventManager.HttpRequest;
            structEvents.HTTPPageDisplaySuccess = (int)NREventManager.HttpSuccess;
            structEvents.HTTPPageDisplayFailure = (int)NREventManager.HttpDisFail;
            structEvents.HTTPPageComplete = (int)NREventManager.HttpComplete;
            structEvents.HTTPPageIncomplete = (int)NREventManager.HttpIncomplete;
            structEvents.HTTPPageFail = (int)NREventManager.HttpFail;

            structEvents.VIDEOPlayRequest = (int)NREventManager.VideoRequest;
            structEvents.VIDEOPlayFirstData = (int)NREventManager.VideoFirstData;
            structEvents.VIDEOPlayRebufferStart = (int)NREventManager.VideoRebufferStart;
            structEvents.VIDEOPlayRebufferEnd = (int)NREventManager.VideoRebufferEnd;
            structEvents.VIDEOPlayLastData = (int)NREventManager.VideoLastData;  //相当于success
            structEvents.VIDEOPlayDrop = (int)NREventManager.VideoDrop;
            structEvents.VIDEOPlayReproductionStart = (int)NREventManager.VideoReproductionStart;
            structEvents.VIDEOPlayReproductionStartFailure = (int)NREventManager.VideoFail;
        }

        public class StructNRMobileServiceEvent
        {
            public int FTPDownloadBegan { get; set; }
            public int FTPDownloadFirstData { get; set; }
            public int FTPDownloadSuccess { get; set; }
            public int FTPDownloadDrop { get; set; }
            public int FTPDownloadUnFinished { get; set; }

            public int HTTPDownloadBegan { get; set; }
            public int HTTPDownloadSuccess { get; set; }
            public int HTTPDownloadDrop { get; set; }
            public int HTTPDownloadFail { get; set; }

            public int HTTPPageRequest { get; set; }
            public int HTTPPageDisplaySuccess { get; set; }
            public int HTTPPageDisplayFailure { get; set; }
            public int HTTPPageComplete { get; set; }
            public int HTTPPageIncomplete { get; set; }
            public int HTTPPageFail { get; set; }

            public int VIDEOPlayRequest { get; set; }
            public int VIDEOPlayFirstData { get; set; }
            public int VIDEOPlayRebufferStart { get; set; }
            public int VIDEOPlayRebufferEnd { get; set; }
            public int VIDEOPlayLastData { get; set; }   //相当于success
            public int VIDEOPlayDrop { get; set; }
            public int VIDEOPlayReproductionStart { get; set; }
            public int VIDEOPlayReproductionStartFailure { get; set; }
        }
    }
}
