<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolTip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsN
        DhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQU
        FBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCACKAOMDASIAAhEBAxEB/8QA
        HwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIh
        MUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVW
        V1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXG
        x8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQF
        BgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAV
        YnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOE
        hYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq
        8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9U6KTPOKQNnPFADqKaGyKXIoAWikBzS0AFFFFABRRRQAUUUUA
        FFFFABRRRQAUUUUAFFFFABRSE4ppkAOMUAOwKMCozcKMZ7jNAuFJAzyelK6HZktFNRt4z0p1MQUUUUAF
        FFFADCMimZKn68U8njpVW5nWCNnYhQBkk+gouJK8kupMJF4Gfp70Fwo/X3r5tv8A4zar8RfhZqV7pOpf
        8I/dxeI4NPg1GGyeYvb3E8TWE8aC4Q8x3Vm7+YSpxKjQkHZWZp/xp1HxH4v+HEdn8QvDOm6pquh3E09v
        fW7CF5Lh7KS0hNmL3KzmORtuZXJAk28Pxj7SN/U9L6hWSbe6v+Vz6pj6GnE4qK3J8sZqUgVsedtuLRRR
        QAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAxutZHiKKW40XUIobqaxmkhdEubcIZISQQHUOrLuHUblYcc
        gjitgjms7V9KTWNPurGZpUiuI3hdreZ4ZArAg7XQhkPPDKQQcEEEUmVBpSTZ8g2HjLxH46+EQj8Sa3rk
        cTWuiatc3OLKC7iiuZWWW7SSOF4orSLb9ojbC3CG3kLvGNjVcs/HS+IfGPgA3fiPxhot9BpT6fqWqppc
        csltf376dLBYzytY/ZwTuUZEaEFEDFS2G930z9n7wfpehJosVneNpwMStFNqd1KZYI1ZY7WRnkJktgHf
        /R2Ji+dvk5OceH9lnwbYai93pp1HSIzqttq6WGm3r29jHLAYSqi1TEJBMCklkZssxDA7dvI4T0se9HFY
        ZxlGSa3tY9ghXCDnPvU1MiUouDj8KfXYfP3uFFFFABRRRQA01l6zpz6rpl5Zrcy2b3ETRLc24UyQkjAd
        d6suR1G5WGRyCOK1CCaYUzQJNxakuh4bp/7JXhXQ7P7FpeoalpWmXOmQaTqlhZpapHq0UYkBe5byC/mS
        CVw7xsjHIwQQDVa2/ZaWx1O6ntPGWsxWMuuWWsDTrnZdRuLc2xKSSzBp3Zvs3DCVQuVGwhSG98xyKCpN
        Y+yh2O9Y/EpP3nqESbFPuc1JTVBA5p1apWVjhCims4UgGuS8dfFfwj8MhZN4q8Q2Hh+O73tHNqEwiiVE
        2h5JHPyxRhpIo/McqnmTwR7t80asxXOvorzz4G/HXwh+0P8ADvTvGfgvUDe6VdZjlhlAS4sp1AMlvOgJ
        2SJkZGSCGVlLIysfQd4xnFAx1FIDkUtABRRRQAUUUUAFFFFABRRRQAUUU1nCkA96ABnC9ePehWDjIr4r
        +H//AAVW+EfxJ+N1j4B0/T/EEFpql7FpeleIri1UW93dPLJGAYgxljici3EbsuSZj5iQiMsftFCCDjpm
        gdiWikBzS0CCiiigAooooAKKKKAEpaKKACiiigCGRMkV+TP7b3/BPr4m/En9pHX/ABho7z69pviKdJ47
        mWOEeSiW8KLbZj27XRYrjBlSOLy4YAbmW4m2V+tJ65qF497Zzxjnkj/P+fSlrbQqElCSk1ddu58H/wDB
        L/wnqPwv0DxP4a8QHwzpGoxpDDBpNtY/Y9fcR3F3K9zqIeNZJQyXlssRDuiRhVAR9+77wBUDb6/gTXI+
        O/hR4Z+I8VqNc0qG4uLR1ltb6MtFdWrh1cNFMhWSM7kQkowztweK4GHSvif8JWhjsbtfif4ZTYgt9SdL
        fWYFHkp8s+FhuMBZnIkETElQZGPXNOUdJanZKFLENul7r7P9Ge4xncuRyKkrzDwZ8e/CnizUrbR5J7vw
        /wCIJ7dLhdD8QWklhesrBz8kcqr5u3y5Axj3qNh5xgn0oTKyhux6GrUk9jlnTnT+JWJaKZ5gx7+lLu46
        GnczHUUgOaWmAUUUUAFFFFABWdrOkWfiDS7zTNSs7fUNNvYXtrqzu4llhnidSro6MCrKQSCCCCCQa0Cc
        UySUIMnp60Afmf8ADz/glf4X8O/tAW+lah4tn1/wt4buLfxKul3ulxM17DNNMsNnOxcxsq/Yk8xvKAlW
        R1CxcEfpbnAI7+tea6HJ9q+PXi+5gBltotF0qyklQZVJ1lvpGiJ6BxHPC5U8hZUYjDAn0vJwCOPXNTDq
        b13flb6JEinOfanU1fel3VRgLRSA5paACiiigAooooAKKKKACiiigAooooAYxx0qN13HqcY5HY1LgHpR
        gfjR6i9TlPG3w78O/EjRZNI8S6Pa6vYuDiO6j3FCVK7kbqjgMwDKQwzkGvObrwX8RfhUZrrwbrJ8baKo
        eVvD3ie5b7Yn+tfZbX2CTlmiULOr8J/rFFe37DTWi3Y7Y9DWTgnqtGdNOvKC5Xquz1R5d4a/aD8Karqf
        9j6xNceDvEW9Yl0fxIotLiYmR4kMJJKXAdozhoWcHK85IFemiRXXerAg9DnisjxL4U0bxRp0tjrem2mq
        WUoBkt72FJUbBDDKsCDggEehAryu0+FXin4ceS/w08TC60WIIp8MeJppLm1jiHkpi2ueZoMRxvgN5qZf
        hFFF5Reupt7OlVV4Plfnt9/+Z7lGcg9acTjtXlXgz452GoPBpni/T5vAXiR5IrcaZrckaJdTOqHbZzBi
        lyAzhfkO8HG5ELAV6gtwjKGU7lIyCvIxVxkpK6OWdOdN2kiaimhwc8Um7Jp7GVx9FIDmgnFMYhPNRMu7
        JGR9DUhGeabjgik2Jbo80+E6F/FXxTzg/wDFRxjn/sF2FelZC+v415v8JSP+Es+Kf/YyR/8Apr0+vRpG
        AHcj2/xqVdx1N6/xv5D964zzinDkZzXlGhfE/XtR+KF74SuPDUNrFZS3Mk2ofbnZRaqkBtplUwDLTNNK
        m0sFU2twFeTyyK9UjbII96cWnsTODp2UupKvSlpF6UtUZIKKKKBhRRRQAUUUUAFFFFABRRRQAUUUUAFF
        FFAGZrOl2ms6dd6fewR3VpdRNDNbzIHjlRgQysp4YEZBHfvXiv7J2jeHLfwhrd/oV7bapI2qS6a+oQSR
        ytJBZAWdoGdB82baGKbkkFp3dQquqj3iSPzDz07+9RwWqQKyqoAJycd6hx1ujaNRxhKHexS1TRrLXbC4
        stRs7e+sp42hmtriMSRyIy4ZGUjDAgkEHjFcK/w51jwcTN4I1Y29uDu/4R/VmafT9o/ggb/WWvARFCFo
        Y0BxAxNembOtNMeR1/Pmm4pkxqSjpujy9/jppfhma0svHdpP4Kvp5PIS4vgW06dwjNmO9UeUA3lybFlM
        UpCgmNdwB9Jt7+G7XfDIJF6ZWq2raHY65ptxp+o2sN9ZXCNFPb3EYkjlRgQyMrZDAgkEHrk+teWX/wCz
        5H4Zv31T4cavL4Fv5JFlm0+1iEuk3RzEG82zJCglIgu+IxvySWNRaS21Ruo0Kis3yy/A9kj6Hr1p+c14
        fp/x11XwK8dj8VNBPh4kpH/wkumFrnRpWPkpl5Noe1zLKygTqF+Q/vGr1/Sdasdc0+3vrC7hvbO4RZYb
        i3kDxyIwBVlYcEEEEEdc1aknojKpQnS1a07rY0SMYpp6mkMy8+3WgsCCffvTfUw3seb/AAlXPi34qen/
        AAkkf/prsK9HKFgSMLXmvwdP2288faxGdttqXiS48lW++v2aKGxk3DpzLZysME5VkJwSVHpX0pRty2Oi
        t/EdvL8jyvQfhV4g0v4o3Xi2fxLDcx3ctyk9iLKRQbRkhW1hVvPIDQtFI+4qyk3VwVSMyE16qq7SeMU5
        RRg80JJLQznN1GnLoOHSlpBxRmqIFooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigBv8A
        F+FMdNzDkACngkt7Uc57UbCavoyrNZxTIVdQyntXkup/s3aRp4urnwLquo/DvU5JGuFGhzEWDTGHygZL
        F827DAQnCKxKA7gea9iK5pSlRKKkbQrVKfws8Pm+LXiz4Xyyx/Efw61xpMYZ18U+F4ZLm1SMec264tfm
        mt8JGmWXzUy/LqOne3HxP8Mr4SbxNb6rb6honT7XYv8AaI2+fZ8pTOcNwcdD16V1zRLKCMAqRgg9K8Q+
        Kn7M2keJ3uNU8MH/AIRTxDPL511Lp4CQXx2TqDNCQYmkzO5EzRtIp5B4GOHEutSoydHV2066hiKkJ0pS
        irSS0a1V/Qv/ALNXivTvEng3VXsrgGX+39XnktpMCWFZdRuJE3DPdWHIyM5GeDXrynaWIBx71498CvhP
        rXgmK9vtalto7m5eZrfT7MFIbSOW4luGjKg7WKvO4HXAGA2GNdD4m1TxJBrFzFaajbWGnLPBEzS2gEgS
        XCh4pWk8tnEhI2suR12tlBJz4bE1o4aM8RB8z3Wl/XVnkzxk1TVWrF3enS/rZvQ9FXng/lS89z+leX+J
        PFGreEvAOu38d5f6jLCzxQ394lvEYzt2hwmEBQOMDKlmPQMpUnE1D4neJbbxpDpAbSrVrqSORLO41BRN
        EAE/csREwVpNyHHznHmbW5QrVTM6NFqM7p6dL7uy123OOtnGGoSUKiabt0vu2lt5o9qHzAZOad19q878
        ffEEWttd6bos12uuW9xbpK0OmTXKRKWjZwSq7T+7YkgHODwQcVp+AdcutcivmuNVi1HyXWIoumS2Lwvt
        DEMsrEnIZCOB17546Y4unOt7GLv56W63W+6tqdccdSnX9hDV9XdWvrpve6trodpRRRXcekFFFFABRRRQ
        AUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABUciliCMcetP70hBJ9qAIkXYgVVCgeleQeMvg
        6niia5upNFsYJWvoikdmwV5ozOplmlk2od3ll/kGQCWOXYrt9jA/GkbqOlcWJwlLGQ9nWV12PPxmCoY6
        n7Kurx7HkupfCtovBviDSLfRrGSZoD9lntSIHvJAjhPNjCqgKlhg5K7vmATAAvXnw61hfFmkTWWt3drp
        1vaXEaNDFbAWgLw7IkUxcqQp6hiNg5GTu9NIzweaT+LGOPWsP7Oo9LrbZ22d0cv9lYbomttnbZ3S09f6
        Z5hqPhnxO2v6lfaZPd6dHqEzsyJNbEBkiVI5G3wuVV/Lx8pYjKEr8zBN7wDo99pUuqPfx3jT3skd011d
        vCWdjEqFCsQVVKeWAQAVOQQzEkL2JA70L6VrTwkYT9opPe9m9Ls6KeBp0qntFJvVuzemo+ilorvPSCii
        igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKSlooAKKKKACkxS0UAFFFFAH
        /9k=
</value>
  </data>
  <data name="ScanOverlapSettingDlg.Appearance.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAP///yZLgqGwxqfH6leX4EN4ukyFyv39/aW0y1iW4FaU3VeT3Pr6+uj1
        +Jmvyuz2+pywye32+leS2Pz8/JGsytzw+tzw+5Ksydzv+gAAAFeR2FeQ1tnr/Nnv/GeNvXup3gAAAFiR
        1liQ1I2qyo6qytXr+9Xs+wAAAFiP1FiP09Dq+4upyoypytLq+3mo3nio3liO0wAAAAAAAAAAAAAAAFeM
        0FuJwGCNwpK34szm+wAAAFiNz1GAvvX6/meUy8rl+1eMzvz+/2GQyFSFxU98t058twAAAKi20AAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIiQABCBxIsKDBgwYLGFi4cMABhggK
        DDSQoKLFiwkWDGSAsSODgQ0cgBhJEoZJBw0GUqhwoSUGlxouOOAwsEOFkjBC6AxRYcRAEglOCB16AgWK
        BCoGrkgQo6lTpwlkDLyRYIfVq1cF9Bjo44fJr08FCCHoYwgRAWjToi1S0MgRI3Djwk2CsK5dgwEBADs=
</value>
  </data>
</root>