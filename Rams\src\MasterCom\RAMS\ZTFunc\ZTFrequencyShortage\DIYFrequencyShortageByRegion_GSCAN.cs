﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYFrequencyShortageByRegion_GSCAN : DIYAnalyseFilesOneByOneByRegion
    {
        protected BandTypeFilter bandType = BandTypeFilter.All;
        protected int rxLevDValue = 8;
        protected int secondLast = 10;
        protected int rxLevDValueOther = 12;
        protected int freqCountRateThreshold = 50;
        public DIYFrequencyShortageByRegion_GSCAN(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_SCAN);
        }

        public override string Name
        {
            get { return "频率资源不足"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15027, this.Name);
        }

        protected override void fireShowForm()
        {
            if (MainModel.FrequencyShortageList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的数据。");
                return;
            }
            MainModel.MainForm.FireShowFrequencyShortageForm();
            MainModel.FireSetDefaultMapSerialTheme("GSM_SCAN_RxLev");
        }

        readonly FrequencyShortageDlg_GSCAN conditionDlg = new FrequencyShortageDlg_GSCAN();
        protected override bool getCondition()
        {
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                int band = 0;
                conditionDlg.GetFilterCondition(out band, out rxLevDValue, out secondLast, out rxLevDValueOther, out freqCountRateThreshold);
                bandType = (BandTypeFilter)band;
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            MainModel.ClearFrequencyShortage();
        }

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (FileInfo fileInfo in files)
                {
                    WaitBox.Text = "正在分析文件(" + (++iloop) + "/" + files.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
                    if (bandType == BandTypeFilter.GSM900 && fileInfo.Name.EndsWith("1800)"))
                    {
                        continue;
                    }
                    if (bandType == BandTypeFilter.DSC1800 && fileInfo.Name.EndsWith("900)"))
                    {
                        continue;
                    }
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        /// <summary>
        /// 小区持续队列
        /// </summary>
        protected Dictionary<Cell, CellLast> cellLastDic = new Dictionary<Cell, CellLast>();
        /// <summary>
        /// 对象小区队列，即满足持续条件的小区列表，采样点是判断点
        /// </summary>
        protected Dictionary<int, Dictionary<Cell, CellLast>> tpCellValidDic = new Dictionary<int, Dictionary<Cell, CellLast>>();
        /// <summary>
        /// 采样点小区信息列表
        /// </summary>
        protected Dictionary<int, Dictionary<Cell, float>> tpCellDic = new Dictionary<int, Dictionary<Cell, float>>();

        /// <summary>
        /// 清除中间变量
        /// </summary>
        protected void clearIntermediateVariable()
        {
            cellLastDic.Clear();
            tpCellValidDic.Clear();
            tpCellDic.Clear();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    clearIntermediateVariable();
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        List<int> idxList = GetIdxList(testPoint, this.bandType);

                        if (idxList.Count > 0 && isValidTestPoint(testPoint))
                        {
                            Dictionary<Cell, float> cellRxLevDic = getCellRxLevDic(testPointList, i, testPoint, idxList);

                            judgeTestPoint(testPointList, i, cellRxLevDic);
                        }
                        else
                        {
                            clearIntermediateVariable();
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private Dictionary<Cell, float> getCellRxLevDic(List<TestPoint> testPointList, int i, TestPoint testPoint, List<int> idxList)
        {
            Dictionary<Cell, float> cellRxLevDic = new Dictionary<Cell, float>();
            float? rxLevMax = (float?)testPoint["GSCAN_RxLev", idxList[0]];
            foreach (int idx in idxList)
            {
                int j = idx;
                float? rxLev = (float?)testPoint["GSCAN_RxLev", j];
                if (rxLev == null || rxLev > -10 || rxLev < -120)
                {
                    break;
                }
                int? bcch = (int?)testPoint["GSCAN_BCCH", j];
                int? bsic = (int?)testPoint["GSCAN_BSIC", j];
                Cell cell = CellManager.GetInstance().GetNearestCell(testPoint.DateTime, (short)bcch, (byte)bsic, testPoint.Longitude, testPoint.Latitude);
                if (cell != null)
                {
                    cellRxLevDic[cell] = (float)rxLev;
                    judgeCell(rxLevMax, rxLev, cell, testPointList, i);
                }
            }

            return cellRxLevDic;
        }

        protected List<int> GetIdxList(TestPoint tp, BandTypeFilter bandType)
        {
            return tp.GetGSMScanIdxSpecify((GSMFreqBandType)bandType);
        }

        /// <summary>
        /// 确定是否对象小区（与一强相差8db内并持续10秒的小区），做四件事：
        /// 1.如果与一强相差8db，放入小区持续队列；
        /// 2.如果与一强相差8db，小区持续满10秒，放入对象小区队列，从小区持续队列中移除；
        /// 3.如果与一强相差8db，在对象小区队列中存在前一个采样点，并且前一个采样点(前一个才有持续性)的对象小区中有该小区，更新该小区到当前采样点的对象小区，并从前一个采样点的对象小区中移除。
        /// 4.如果与一强相差超过8db，从小区持续队列移除。
        /// </summary>
        /// <param name="rxLevMax">一强小区电平</param>
        /// <param name="rxLev">当前小区电平</param>
        /// <param name="cell">当前小区</param>
        /// <param name="testPointList">采样点列表</param>
        /// <param name="curIndex">当前采样点序号</param>
        protected virtual void judgeCell(float? rxLevMax, float? rxLev, Cell cell, List<TestPoint> testPointList, int curIndex)
        {
            TestPoint testPoint = testPointList[curIndex];
            if (rxLevMax - rxLev <= rxLevDValue)    //与一强相差8db内
            {
                if (!cellLastDic.ContainsKey(cell))
                {
                    CellLast cellLast = new CellLast();
                    cellLastDic[cell] = cellLast;
                }
                cellLastDic[cell].Add(testPoint, (float)rxLev);
                if (cellLastDic[cell].SecondLast >= secondLast) //持续10秒，满足前提条件
                {
                    if (!tpCellValidDic.ContainsKey(curIndex))
                    {
                        Dictionary<Cell, CellLast> cellValidDic = new Dictionary<Cell, CellLast>();
                        tpCellValidDic[curIndex] = cellValidDic;
                    }
                    tpCellValidDic[curIndex][cell] = cellLastDic[cell];
                    cellLastDic.Remove(cell);
                }

                //更新已经满10秒，未形成频率资源不足的点，延续到下个点判断(只判断当前点的前一个点才有延续性)
                List<int> tpList = new List<int>(tpCellValidDic.Keys);
                foreach (int index in tpList)
                {
                    if (index == curIndex - 1)
                    {
                        dealSameTP(rxLev, cell, curIndex, testPoint, index);
                    }
                }
                return;
            }

            //未满10秒移除
            if (cellLastDic.ContainsKey(cell))
            {
                cellLastDic.Remove(cell);
            }
        }

        private void dealSameTP(float? rxLev, Cell cell, int curIndex, TestPoint testPoint, int index)
        {
            List<Cell> cellList = new List<Cell>(tpCellValidDic[index].Keys);
            foreach (Cell cellTP in cellList)
            {
                if (cell == cellTP)
                {
                    //对象小区加入当前采样点
                    if (!tpCellValidDic.ContainsKey(curIndex))
                    {
                        Dictionary<Cell, CellLast> cellValidDic = new Dictionary<Cell, CellLast>();
                        tpCellValidDic[curIndex] = cellValidDic;
                    }
                    if (!tpCellValidDic[curIndex].ContainsKey(cell))
                    {
                        tpCellValidDic[curIndex][cell] = tpCellValidDic[index][cell];
                    }
                    tpCellValidDic[curIndex][cell].Add(testPoint, (float)rxLev);

                    //对象小区从前一个采样点移除，如果前一个采样点没有对象小区，移除
                    tpCellValidDic[index].Remove(cell);
                    if (tpCellValidDic[index].Count == 0)
                    {
                        tpCellValidDic.Remove(index);
                    }
                }
            }
        }

        /// <summary>
        /// 在每个采样点判断，做三件事：
        /// 1.小区持续列表中移除不连续的小区，即之前连续，在当前采样点中未出现；
        /// 2.采样点对象小区列表中，未形成频率资源不足的移除(如果不是当前采样点，说明在之前判断未形成频率资源不足，如果形成，已经移除，有延续性的小区也已经移到当前采样点)；
        /// 3.判断采样点对象小区列表中对象小区是否形成频率资源不足（列表中只有当前采样点，不是当前采样点的第二步已经移除）。
        /// </summary>
        /// <param name="testPointList">采样点列表</param>
        /// <param name="i">当前采样点序号</param>
        /// <param name="cellRxLevDic">当前采样点小区列表</param>
        protected virtual void judgeTestPoint(List<TestPoint> testPointList, int i, Dictionary<Cell, float> cellRxLevDic)
        {
            //不连续的小区移除
            List<Cell> removeCellList = new List<Cell>(cellLastDic.Keys);
            foreach (Cell cell in removeCellList)
            {
                if (!cellRxLevDic.ContainsKey(cell))
                {
                    cellLastDic.Remove(cell);
                }
            }

            //满足前提条件小区，未形成频率资源不足的移除
            List<int> removeTPList = new List<int>(tpCellValidDic.Keys);
            foreach (int index in removeTPList)
            {
                if (i != index)
                {
                    tpCellValidDic.Remove(index);
                }
            }

            //判断是否形成频率资源不足
            removeTPList = new List<int>(tpCellValidDic.Keys);
            foreach (int index in removeTPList)
            {
                //满10秒的取5秒处的点判断
                int k = i - 1;
                for (; k >= 0; k--)
                {
                    if (testPointList[index].Time - testPointList[k].Time >= 5)
                    {
                        break;
                    }
                }
                Dictionary<Cell, CellLast> cellValidDic = tpCellValidDic[index];
                Dictionary<Cell, float> cellDic = tpCellDic[k];
                FrequencyShortage freqShortage = getFrequencyShortage(testPointList, k, cellValidDic, cellDic);
                if (freqShortage != null)
                {
                    tpCellValidDic.Remove(index);
                }
            }
            tpCellDic[i] = cellRxLevDic;
        }

        private FrequencyShortage getFrequencyShortage(List<TestPoint> testPointList, int k, 
            Dictionary<Cell, CellLast> cellValidDic, Dictionary<Cell, float> cellDic)
        {
            FrequencyShortage freqShortage = null;
            foreach (Cell cell in cellValidDic.Keys)    //对每个前提对象进行判断，有一个小区满足条件就满足
            {
                if (!cellDic.ContainsKey(cell))    //小区信号中断，忽略
                {
                    continue;
                }
                //同频段相差12db内小区的频点数占频段可用频点数百分比达到门限形成频率资源不足
                double freqCountRate = getFreqCountRate(cell, cellDic);
                if (100.0 * freqCountRate >= freqCountRateThreshold)
                {
                    if (freqShortage == null)
                    {
                        freqShortage = new FrequencyShortage(testPointList[k]);
                        //freqShortage.RoadDesc = GISManager.GetInstance().GetRoadPlaceDesc(freqShortage.Longitude, freqShortage.Latitude);
                        MainModel.FrequencyShortageList.Add(freqShortage);
                    }
                    freqShortage.AddCell(cell.Name, freqCountRate);
                }
            }

            return freqShortage;
        }

        /// <summary>
        /// 获取对象小区在某一点的频点占比(同频段相差12db内小区的频点数占频段可用频点数百分比)
        /// </summary>
        /// <param name="cell">对象小区</param>
        /// <param name="cellDic">采样点小区列表</param>
        protected double getFreqCountRate(Cell cell, Dictionary<Cell, float> cellDic)
        {
            List<int> freqList = new List<int>();
            foreach (Cell otherCell in cellDic.Keys)
            {
                if (cell == otherCell || (cell.BandType == otherCell.BandType && cellDic[otherCell] - cellDic[cell] >= (-1 * rxLevDValueOther)))
                {
                    addValidFreq(freqList, otherCell);
                }
                else if (cellDic[otherCell] - cellDic[cell] < (-1 * rxLevDValueOther))
                {
                    break;
                }
            }
            return cell.BandType == BTSBandType.GSM900 ? 1.0 * freqList.Count / 94 : 1.0 * freqList.Count / 238;
        }

        private static void addValidFreq(List<int> freqList, Cell otherCell)
        {
            if (!freqList.Contains(otherCell.BCCH))
            {
                freqList.Add(otherCell.BCCH);
            }
            foreach (short tch in otherCell.TCH)
            {
                if (!freqList.Contains(tch))
                {
                    freqList.Add(tch);
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            clearIntermediateVariable();
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
                {
                    return true;
                }
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        protected enum BandTypeFilter
        {
            All,
            GSM900,
            DSC1800
        }

        protected class CellLast
        {
            public CellLast()
            {
            }
            private readonly List<TestPoint> testPointList = new List<TestPoint>();
            private readonly List<float> rxLevList = new List<float>();
            /// <summary>
            /// 持续时间
            /// </summary>
            public int SecondLast
            {
                get
                {
                    if (testPointList.Count > 1)
                    {
                        return testPointList[testPointList.Count - 1].Time - testPointList[0].Time;
                    }
                    else
                    {
                        return 0;
                    }
                }
            }

            /// <summary>
            /// 持续满10秒内的平均场强
            /// </summary>
            public float RxLevLastAvg
            {
                get
                {
                    float sum = 0;
                    foreach (float rxLev in rxLevList)
                    {
                        sum += rxLev;
                    }
                    return sum / rxLevList.Count;
                }
            }

            /// <summary>
            /// 持续满10秒时的时间
            /// </summary>
            public float TimeLast
            {
                get
                {
                    if (testPointList.Count > 0)
                    {
                        return testPointList[testPointList.Count - 1].Time;
                    }
                    return 0;
                }
            }

            public string FileName
            {
                get { return testPointList[0].FileName; }
            }

            public double LongitudeMid
            {
                get { return testPointList[(testPointList.Count / 2)].Longitude; }
            }

            public double LatitudeMid
            {
                get { return testPointList[(testPointList.Count / 2)].Latitude; }
            }

            public void Add(TestPoint tp, float rxLev)
            {
                testPointList.Add(tp);
                rxLevList.Add(rxLev);
            }
        }
    }

    /// <summary>
    /// 频率资源不足
    /// </summary>
    public class FrequencyShortage
    {
        public FrequencyShortage(TestPoint tp)
        {
            this.TestPoint = tp;
        }

        public TestPoint TestPoint { get; set; }

        public List<string> cellList { get; set; } = new List<string>();
        public string Cells
        {
            get
            {
                StringBuilder cellString = new StringBuilder();
                foreach (string cell in cellList)
                {
                    if (cellString.Length > 0)
                    {
                        cellString.Append(" | ");
                    }
                    cellString.Append(cell);
                }
                return cellString.ToString();
            }
        }

        public int CellCount
        {
            get { return cellList.Count; }
        }

        public void AddCell(string cell, double freqCountRate)
        {
            if (!cellList.Contains(cell))
            {
                cellList.Add(cell);
                freqCountRateList.Add(freqCountRate);
            }
        }

        public string FileName
        {
            get { return TestPoint.FileName; }
        }

        public int Time
        {
            get { return TestPoint.Time; }
        }

        public short Millisecond
        {
            get { return TestPoint.Millisecond; }
        }

        public DateTime DateTime
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(Time * 1000L); }
        }

        public string DateTimeStringWithMillisecond
        {
            get
            {
                return String.Format("{0}.{1:d3}", DateTime.ToString("yy-MM-dd HH:mm:ss"), Millisecond);
            }
        }

        public double Longitude
        {
            get { return TestPoint.Longitude; }
        }

        public double Latitude
        {
            get { return TestPoint.Latitude; }
        }

        public List<double> freqCountRateList { get; set; } = new List<double>();
        public string FreqCountRate
        {
            get 
            {
                StringBuilder refString = new StringBuilder();
                foreach (double rate in freqCountRateList)
                {
                    if (refString.Length > 0)
                    {
                        refString.Append(" | ");
                    }
                    refString.Append(Math.Round(rate, 4));
                }
                return refString.ToString();
            }
        }
        
        public string RoadDesc { get; set; } = "";

        public bool WithIn(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }
    }

    public class GridFrequencyShortage : GridBase
    {
        public GridFrequencyShortage(double ltLongitude, double ltLatitude)
            : base(ltLongitude, ltLatitude)
        {
        }

        public void AddFrequencyShortage(FrequencyShortage fs)
        {
            for (int i = 0; i < fs.CellCount; i++)
            {
                string cell = fs.cellList[i];
                if (!cellDic.ContainsKey(cell))
                {
                    List<double> rateList = new List<double>();
                    cellDic[cell] = rateList;
                }
                cellDic[cell].Add(fs.freqCountRateList[i]);
            }
        }

        public Dictionary<string, List<double>> cellDic { get; set; } = new Dictionary<string, List<double>>();
        public string Cells
        {
            get
            {
                StringBuilder cellString = new StringBuilder();
                foreach (string cell in cellDic.Keys)
                {
                    if (cellString.Length > 0)
                    {
                        cellString.Append(" | ");
                    }
                    cellString.Append(cell);
                }
                return cellString.ToString();
            }
        }

        public double FreqCountRate
        {
            get 
            {
                double sum = 0;
                int count = 0;
                foreach (string cell in cellDic.Keys)
                {
                    foreach (double rate in cellDic[cell])
                    {
                        sum += rate;
                        count++;
                    }
                }
                return Math.Round(sum / count, 4); 
            }
        }
    }
}
