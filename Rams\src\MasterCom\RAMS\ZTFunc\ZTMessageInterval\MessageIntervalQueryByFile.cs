﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class MessageIntervalQueryByFile : DIYReplayFileMessage
    {
        public MessageIntervalQueryByFile(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "查询信令间隔"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20038, this.Name);
        }

        protected override bool isValidCondition()
        {
            if (setForm == null)
            {
                setForm = new MessageIntervalSettingForm();
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            setForm.GetCondition(out this.startMessageID, out this.endMessageID);
            return true;
        }

        protected override void prepareStatPackage_Message_MessageFilter(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,6,45");//msgid
            package.Content.AddParam(string.Format("{0},{1}", startMessageID, endMessageID));
            AddDIYEndOpFlag(package);
        }

        protected override void DoSomethingBeforeQuery()
        {
            messageList.Clear();
        }

        protected override void GetResultAfterQuery()
        {
            Message startMsg = null;
            Message endMsg = null;
            foreach (Message msg in messageList)
            {
                if (msg.ID == startMessageID)
                {
                    startMsg = msg;
                }
                else if (msg.ID == endMessageID)
                {
                    endMsg = msg;
                }

                if (startMsg == null || endMsg == null)
                {
                    continue;
                }
                else if (startMsg.FileID != endMsg.FileID)
                {
                    startMsg = endMsg = null;
                    continue;
                }

                MessageIntervalFile iFile = null;
                if (!fileDic.TryGetValue(startMsg.FileID, out iFile))
                {
                    DTDataHeader fileHeader = DTDataHeaderManager.GetInstance().GetHeaderByFileID(startMsg.FileID);
                    iFile = new MessageIntervalFile(fileHeader);
                    fileDic.Add(startMsg.FileID, iFile);
                }
                iFile.IntervalPairList.Add(new MessageIntervalPair(startMsg, endMsg));
                startMsg = endMsg = null;
            }
        }

        protected override void fireShowResult()
        {
            MainModel.FireDTDataChanged(this);

            MessageIntervalResultForm resultForm = MainModel.GetObjectFromBlackboard(typeof(MessageIntervalResultForm).FullName) as MessageIntervalResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new MessageIntervalResultForm(MainModel);
            }
            resultForm.FillData(new List<MessageIntervalFile>(fileDic.Values));
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }

            messageList.Clear();
            fileDic.Clear();
        }

        private MessageIntervalSettingForm setForm;
        private int startMessageID;
        private int endMessageID;
        private readonly Dictionary<int, MessageIntervalFile> fileDic = new Dictionary<int, MessageIntervalFile>();
    }

    public class MessageIntervalPair
    {
        public MessageIntervalPair(Message startMsg, Message endMsg)
        {
            StartMessageName = MessageInfoManager.GetInstance()[startMsg.ID].Name;
            StartMessageTime = startMsg.DateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
            EndMessageName = MessageInfoManager.GetInstance()[endMsg.ID].Name;
            EndMessageTime = endMsg.DateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
            MessageInterval = (endMsg.Time - startMsg.Time) * 1000 + endMsg.Millisecond - startMsg.Millisecond;
        }

        public string StartMessageName
        {
            get;
            private set;
        }

        public string StartMessageTime
        {
            get;
            private set;
        }

        public string EndMessageName
        {
            get;
            private set;
        }

        public string EndMessageTime
        {
            get;
            private set;
        }

        public int MessageInterval
        {
            get;
            private set;
        }
    }

    public class MessageIntervalFile
    {
        public MessageIntervalFile(DTDataHeader fileHeader)
        {
            FileName = fileHeader.Name;
            IntervalPairList = new List<MessageIntervalPair>();
            StartTime = fileHeader.BeginTimeString;
            EndTime = fileHeader.EndTimeString;
        }

        public string FileName
        {
            get;
            private set;
        }

        public int PairCount
        {
            get { return IntervalPairList.Count; }
        }

        public string StartTime
        {
            get;
            private set;
        }

        public string EndTime
        {
            get;
            private set;
        }

        public List<MessageIntervalPair> IntervalPairList
        {
            get;
            private set;
        }
    }
}
