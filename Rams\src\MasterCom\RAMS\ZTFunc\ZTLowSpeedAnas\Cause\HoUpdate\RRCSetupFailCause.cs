﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class RRCSetupFailCause : CauseBase
    {
        public override string Name
        {
            get { return "RRC建立失败"; }
        }
        
        public int Second { get; set; } = 10;
        public override string Desc
        {
            get { return string.Format("判断低速率发生前{0}秒是否有发生RRCSetup Fail事件", Second); }
        }

        public override string Suggestion
        {
            get { return null; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            //循环低速率采样点
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }

                int bTime = pnt.Time - Second;
                foreach (Event evt in evts)
                {
                    //RRCSetup Fail事件,事件发生时间在低速率的前n秒内
                    if ((evt.ID == 857) && bTime <= evt.Time && evt.Time <= pnt.Time)
                    {
                        RRCSetupFailCause cln = this.Clone() as RRCSetupFailCause;
                        segItem.SetReason(new LowSpeedPointDetail(pnt, cln));
                        break;
                    }
                }
            }
        }


        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["second"] = this.Second;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.Second = (int)value["second"];
            }
        }
    }
}
