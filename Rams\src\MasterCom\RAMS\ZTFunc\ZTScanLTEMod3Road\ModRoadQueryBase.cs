﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class ModRoadQueryByFile : DIYReplayFileQuery
    {
        protected ModRoadQueryByFile(MainModel mm, ModRoadQueryBase queryer)
            : base(mm)
        {
            IsAddSampleToDTDataManager = false;
            IsAddMessageToDTDataManager = false;
            isAutoLoadCQTPicture = false;
            this.queryer = queryer;
        }

        public override string Name
        {
            get
            {
                return queryer.Name;
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override bool isValidCondition()
        {
            return queryer.isValidCondition();
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            List<ColumnDefItem> columns = null;
            foreach (string item in queryer.Columns)
            {
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName(item);
                option.SampleColumns.AddRange(columns);
            }

            return option;
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            queryer.StartFileStat(fileInfo);
            base.queryReplayInfo(clientProxy, package, fileInfo);
        }

        protected override void doPostReplayAction()
        {
            queryer.FinishedAllStat();
        }

        protected override void fireShowResult()
        {
            queryer.FireShowForm();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            queryer.DoWithTestPoint(tp);
        }

        protected ModRoadQueryBase queryer;
    }

    public abstract class ModRoadQueryByRegion : DIYAnalyseByCellBackgroundBaseByFile
    {
        protected ModRoadQueryBase queryer;
        protected ModRoadQueryByRegion(MainModel mm, ModRoadQueryBase queryer)
            : base(mm)
        {
            IncludeEvent = false;
            IncludeMessage = false;
            FilterEventByRegion = false;
            FilterSampleByRegion = true;
            this.queryer = queryer;
            this.Columns = queryer.Columns;
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override void fireShowForm()
        {
            queryer.FireShowForm();
        }
        protected override void doSomethingAfterAnalyseFiles()
        {
            getResultsAfterQuery();
        }
        protected override void getResultsAfterQuery()
        {
            queryer.FinishedAllStat();
        }

        protected override bool isValidCondition()
        {
            return queryer.isValidCondition();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                queryer.StartFileStat(fileDataManager.GetFileInfo());

                List<TestPoint> testPointList = fileDataManager.TestPoints;
                foreach (TestPoint tp in testPointList)
                {
                    queryer.DoWithTestPoint(tp);
                }
            }
        }
    }

    public abstract class ModRoadQueryBase
    {
        protected ModRoadQueryBase(MainModel mainModel)
        {
            this.mainModel = mainModel;
        }

        public abstract string Name
        {
            get;
        }

        public abstract List<string> Columns
        {
            get;
        }

        public abstract ModRoadStaterBase Stater
        {
            get;
        }

        public virtual string IconName
        {
            get { return string.Empty; }
        }

        public abstract MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem();
        public abstract bool isValidCondition();
        public abstract void FireShowForm();
        protected abstract ModRoadItemBase CreateNewRoad(TestPoint firstPoint, string fileName);

        public virtual void StartFileStat(FileInfo fInfo)
        {
            if (curRoad != null)
            {
                curRoad.CloseRoad();
                Stater.AddRoad(curRoad);
            }
            curRoad = null;
            curFileInfo = fInfo;
        }

        public virtual void DoWithTestPoint(TestPoint tp)
        {
            if (curRoad == null)
            {
                curRoad = CreateNewRoad(tp, curFileInfo.Name);
            }
            else if (!curRoad.AddTestPoint(tp))
            {
                curRoad.CloseRoad();
                Stater.AddRoad(curRoad);
                curRoad = CreateNewRoad(tp, curFileInfo.Name);
            }
        }

        public virtual void FinishedAllStat()
        {
            if (curRoad != null)
            {
                curRoad.CloseRoad();
                Stater.AddRoad(curRoad);
                curRoad = null;
            }
        }

        protected ModRoadItemBase curRoad;
        protected MainModel mainModel;
        protected FileInfo curFileInfo;
    }
}
