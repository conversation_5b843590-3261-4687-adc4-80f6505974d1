﻿using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 测试里程统计功能（山东应付审计用，只简单呈现指定表数据）
    /// </summary>
    public class GridTestMileageInfoQuery : DIYSQLBase
    {
        protected TestMileageType testType = TestMileageType.GridTest;
        protected int year = 2017;
        protected string testTypeDes = "";
        readonly List<TestMileageInfo> resultList = new List<TestMileageInfo>();
        public GridTestMileageInfoQuery()
            : base(MainModel.GetInstance())
        {
        }
        public override string Name
        {
            get { return "网格测试里程信息查询"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18043, this.Name);
        }
        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }
        TestMileageConditionDlg dlg;
        protected override bool isValidCondition()
        {
            if (dlg == null)
            {
                dlg = new TestMileageConditionDlg(testType);
            }
            dlg.SetCondition(year, testTypeDes);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out year, out testTypeDes);
            return true;
        }
        protected override void query()
        {
            resultList.Clear();
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("正在查询测试里程信息...", queryInThread, clientProxy);
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
            showResultForm();
        }
        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            System.Threading.Thread.Sleep(200);
            WaitBox.Close();
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"SELECT [testYear],[testMonth],[testDate],[project],[districtName],[boxID]
,[testPlanName],[testMileage] FROM [tb_testMileageInfo_grid] 
where testYear in('{0}年') and districtName like '%{1}%' ", year, testTypeDes);
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[8];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    TestMileageInfo info = new TestMileageInfo(testType);
                    info.Year = package.Content.GetParamString();
                    info.Month = package.Content.GetParamString();
                    info.Date = package.Content.GetParamString();
                    info.Project = package.Content.GetParamString();
                    info.TestTypeDes = package.Content.GetParamString();
                    info.BoxID = package.Content.GetParamString();
                    info.TestPlanName = package.Content.GetParamString();
                    info.TestMileage = package.Content.GetParamString();
                    info.SN = resultList.Count + 1;
                    resultList.Add(info);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        protected void showResultForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            TestMileageInfoForm frm = MainModel.CreateResultForm(typeof(TestMileageInfoForm)) as TestMileageInfoForm;
            frm.FillData(testType, resultList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class MainLineTestMileageInfoQuery : GridTestMileageInfoQuery
    {
        public MainLineTestMileageInfoQuery()
        {
            this.testType = TestMileageType.MainLineTest; 
        }
        public override string Name
        {
            get { return "交通干线测试里程信息查询"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18044, this.Name);
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"SELECT [testYear],[testMonth],[testDate],[project],[mainLineName],[boxID]
,[testPlanName],[testMileage] FROM [tb_testMileageInfo_mainLine] 
where testYear in('{0}年') and mainLineName like '%{1}%' ", year, testTypeDes);
        }
    }
}
