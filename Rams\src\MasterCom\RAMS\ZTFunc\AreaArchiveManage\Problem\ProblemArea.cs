﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.Problem
{
    public class ProblemArea
    {
        public ProblemArea(AreaBase area)
        {
            this.Area = area;
            AbnormalEventsDesc = string.Empty;
        }

        public AreaBase Area
        {
            get;
            private set;
        }

        public double CoverCM
        { get; set; }

        public double CoverCU
        {
            get;
            set;
        }

        public double CoverCT
        {
            get;
            set;
        }

        public List<EventInfo> AbnormalEvents
        {
            get;
            set;
        }

        public string AbnormalEventsDesc
        {
            get;
            private set;
        }


        internal void AddAbnormlEvent(EventInfo evtInfo, double cnt)
        {
            AbnormalEventsDesc += string.Format("{0}{1}[{2}个]", AbnormalEventsDesc.Length == 0 ? "" : ";"
                , evtInfo.Name, cnt);
            AbnormalEventNum += (int)cnt;
        }
        public ProblemType ProbType
        {
            get;
            set;
        }

        public int AbnormalEventNum { get; private set; }

        public double PoorQual { get; set; }

        public object ProbNum { get; set; }
    }

    [Flags]
    public enum ProblemType
    {
        None = 0,
        弱覆盖且差于竞争对手 = 1,
        弱覆盖且语音质差 = 2,
        仅弱覆盖 = 4,
        异常事件 = 8
    }

}
