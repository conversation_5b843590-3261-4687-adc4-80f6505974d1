﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.KPI_Statistics;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTExportVillageTestShp : QueryBase
    {
        public ZTExportVillageTestShp(MainModel mainModel)
           : base(mainModel)
        {

        }

        public override string Name
        {
            get { return "农村测试输出图层"; }
        }

        //protected override UserMng.LogInfoItem getRecLogItem()
        //{
        //    return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20028, this.Name);
        //}

        public override string IconName
        {
            get { return "images/其它专题/簇优化.png"; }
        }

        protected string curDistrictName;
        protected string districtPath;
        protected string exportShpPath;
        private ZTExportVillageTestShpDlg setForm = null;
        protected override bool isValidCondition()
        {
            if (setForm == null)
            {
                setForm = new ZTExportVillageTestShpDlg();
            }
            if (setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                districtPath = setForm.DistrictPath;
                exportShpPath = setForm.ExportShpPath;
                return true;
            }
            return false;
        }

        ////区县乡镇信息
        //Dictionary<string, Dictionary<string, ZTVillageTestTownInfo>> county_TownRegionDic;
        ////区县支局信息
        //Dictionary<string, Dictionary<string, ZTVillageTestBranchInfo>> county_BranchRegionDic;

        protected override void query()
        {
            dealWithData();
            //WaitBox.Show("开始统计数据...", dealWithData);
        }

        Dictionary<string, Dictionary<string, ResvRegion>> curDistrictBranch;
         Dictionary<string, ResvRegion> curBranchDic;
        List<ResvRegion> curDistrictList;
        List<ResvRegion> curCountyList;
        List<ResvRegion> curTownList;

        private void dealWithData()
        {
            try
            {
                //按地市区县进行处理
                string[] districtNames = DistrictManager.GetInstance().DistrictNames;
                WaitBox.Text = "正在加载支局图层...";
                //加载支局图层
                //Dictionary<string, Dictionary<string, ResvRegion>> districtBranch = loadDistrictBranchMap();

                WaitBox.Show("正在加载支局图层...", loadDistrictBranchMap);

                for (int i = 0; i < districtNames.Length; i++)
                {
                    //county_TownRegionDic = new Dictionary<string, Dictionary<string, ZTVillageTestTownInfo>>();
                    //county_BranchRegionDic = new Dictionary<string, Dictionary<string, ZTVillageTestBranchInfo>>();

                    curDistrictName = districtNames[i];
                    if (!string.IsNullOrEmpty(curDistrictName))
                    {
                        WaitBox.Text = "开始处理地市【" + curDistrictName + "】的数据...";

                        WaitBox.Show("正在加载其他图层...", loadMapInfo);
                        ////加载地市图层
                        //List<ResvRegion> districtList = loadDistrictMap(curDistrictName, "地区界_region");

                        ////加载区县图层
                        //List<ResvRegion> countyList = loadDistrictMap(curDistrictName, "县界_region");

                        ////加载乡镇图层
                        //List<ResvRegion> townList = loadDistrictMap(curDistrictName, "乡镇界");

                        bool isValidBranch = judgeValidBranch(curDistrictBranch, curDistrictName, out curBranchDic);
                        bool isValid = judgeValidMapInfo(isValidBranch, curDistrictList, curCountyList, curTownList);

                        //dealMapInfo(districtBranch, districtName, countyList, townList);

                        if (isValid)
                        {
                            //初始化地市小区信息
                            loadDistrictCellInfo(mainModel, i, curDistrictName);

                            WaitBox.Show("正在查询分析栅格数据...", dealGridInfo);
                            //dealGridInfo(branchDic);
                        }
                    }

                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MessageBox.Show("农村测试栅格图层导出完成!");
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void dealGridInfo()
        {
            //按地市区域查询对应的小区栅格信息
            List<ZTVillageTestCellGrid> cellGridList = queryCellGridInfos(curDistrictList);

            //将小区栅格按栅格纬度汇聚
            List<ZTVillageTestGrid> gridList = coverageCellGrids(cellGridList);

            setDistrictCountyGrids(curDistrictName, gridList, curCountyList);

            dealTownGrids(gridList, curTownList);

            dealBranchGrids(gridList, curDistrictName, curBranchDic);

            WaitBox.Close();
        }

        private bool judgeValidMapInfo(bool isValidBranch, List<ResvRegion> districtList, List<ResvRegion> countyList, List<ResvRegion> townList)
        {
            bool isValid = false;

            if (districtList.Count > 0 && countyList.Count > 0 && isValidBranch || townList.Count > 0)
            {
                isValid = true;
            }

            return isValid;
        }

        private bool judgeValidBranch(Dictionary<string, Dictionary<string, ResvRegion>> districtBranch, string districtName, 
            out Dictionary<string, ResvRegion> branchDic)
        {
            bool isValidBranch = false;
            branchDic = new Dictionary<string, ResvRegion>();
            foreach (var district in districtBranch.Keys)
            {
                if (districtName.Contains(district))
                {
                    isValidBranch = true;
                    branchDic = districtBranch[district];
                }
            }

            return isValidBranch;
        }

        private List<ZTVillageTestCellGrid> queryCellGridInfos(List<ResvRegion> districtList)
        {
            log.Info("开始查询对应的小区栅格信息...");
            ZTVillageTestQueryCellGrid query = new ZTVillageTestQueryCellGrid();
            QueryCondition curCondition = new QueryCondition();
            curCondition.Periods = Condition.Periods;
            curCondition.CarrierTypes.Add(1);
            List<ServiceType> serviceTypes = ServiceTypeManager.GetLteAllServiceTypes();
            foreach (var st in serviceTypes)
            {
                curCondition.ServiceTypes.Add((int)st);
            }
            foreach (var st in Condition.Projects)
            {
                curCondition.Projects.Add(st);
            }
            curCondition.Geometorys = new SearchGeometrys();
            curCondition.Geometorys.Region = districtList[0].Shape;

            query.SetQueryCondition(curCondition);
            query.Query();
            List<ZTVillageTestCellGrid> cellGridList = query.CellGridList;
            return cellGridList;
        }

        #region 关联地市,区县,乡镇,支局
        //private void dealMapInfo(Dictionary<string, Dictionary<string, ResvRegion>> districtBranch, string districtName,
        //    List<ResvRegion> countyList, List<ResvRegion> townList)
        //{
        //    foreach (var county in countyList)
        //    {
        //        dealTownMapInfo(districtName, county, townList);

        //        Dictionary<string, ResvRegion> branchDic;
        //        if (districtBranch.TryGetValue(districtName, out branchDic))
        //        {
        //            dealBranchMapInfo(districtName, county, branchDic);
        //        }
        //    }
        //}

        //private void dealTownMapInfo(string districtName, ResvRegion county, List<ResvRegion> townList)
        //{
        //    foreach (var town in townList)
        //    {
        //        if (county.GeoOp.CheckRectCenterInRegion(town.GeoOp.Bounds))
        //        {
        //            Dictionary<string, ZTVillageTestTownInfo> townRegionDic;
        //            if (!county_TownRegionDic.TryGetValue(county.RegionName, out townRegionDic))
        //            {
        //                townRegionDic = new Dictionary<string, ZTVillageTestTownInfo>();
        //                county_TownRegionDic.Add(county.RegionName, townRegionDic);
        //            }

        //            ZTVillageTestTownInfo townInfo;
        //            if (!townRegionDic.TryGetValue(town.RegionName, out townInfo))
        //            {
        //                townInfo = new ZTVillageTestTownInfo(districtName, county.RegionName, town.RegionName, town);
        //                townRegionDic.Add(town.RegionName, townInfo);
        //            }
        //        }
        //    }
        //}

        //private void dealBranchMapInfo(string districtName, ResvRegion county, Dictionary<string, ResvRegion> branchDic)
        //{
        //    foreach (var branch in branchDic.Values)
        //    {
        //        if (county.GeoOp.CheckRectCenterInRegion(branch.GeoOp.Bounds))
        //        {
        //            Dictionary<string, ZTVillageTestBranchInfo> branchRegionDic;
        //            if (!county_BranchRegionDic.TryGetValue(county.RegionName, out branchRegionDic))
        //            {
        //                branchRegionDic = new Dictionary<string, ZTVillageTestBranchInfo>();
        //                county_BranchRegionDic.Add(county.RegionName, branchRegionDic);
        //            }

        //            ZTVillageTestBranchInfo branchInfo;
        //            if (!branchRegionDic.TryGetValue(branch.RegionName, out branchInfo))
        //            {
        //                branchInfo = new ZTVillageTestBranchInfo(districtName, county.RegionName, branch.RegionName, branch);
        //                branchRegionDic.Add(branch.RegionName, branchInfo);
        //            }
        //        }
        //    }
        //}
        #endregion

        #region 加载图层,工参
        private void loadDistrictCellInfo(MainModel mainModel, int i, string districtName)
        {
            if (mainModel.DistrictID != i)
            {
                mainModel.DistrictID = i;
                GISManager.GetInstance().ResetMap(this, null);

                log.Info("开始读取地市【" + districtName + "】的小区信息...");
                mainModel.ModelConfig.Init(false);
            }
        }

        private void loadDistrictBranchMap()
        {
            curDistrictBranch = new Dictionary<string, Dictionary<string, ResvRegion>>();
            if (Directory.Exists(districtPath))
            {
                log.Info("开始读取支局图层信息...");
                string branchLayerPath = getLayerFilePath(districtPath, "四川支局图层_region.shp");
                if (!string.IsNullOrEmpty(branchLayerPath))
                {
                    curDistrictBranch = ZTExportVillageTestShpMapInfo.getBranchMapInfo(branchLayerPath);
                }
                else
                {
                    log.Info("不存在支局图层信息!");
                }
            }
            else
            {
                log.Info("不存在支局图层信息!");
            }

            WaitBox.Close();
        }

        private void loadMapInfo()
        {
            //加载地市图层
            curDistrictList = loadDistrictMap(curDistrictName, "地区界_region");

            //加载区县图层
            curCountyList = loadDistrictMap(curDistrictName, "县界_region");

            //加载乡镇图层
            curTownList = loadDistrictMap(curDistrictName, "乡镇界");
            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        private List<ResvRegion> loadDistrictMap(string districtName, string mapType)
        {
            List<ResvRegion> regionList = new List<ResvRegion>();
            string path = districtPath + Path.DirectorySeparatorChar + districtName;
            if (Directory.Exists(path))
            {
                log.Info("开始读取地市【" + districtName + "】的图层信息...");
                string countyRegionLayerPath = getLayerFilePath(path, mapType + ".shp");
                if (!string.IsNullOrEmpty(countyRegionLayerPath))
                {
                    regionList = ZTExportVillageTestShpMapInfo.GetRegionList(countyRegionLayerPath);
                }
                else
                {
                    log.Info("不存在地市【" + districtName + "】的区县图层信息!");
                }
            }
            else
            {
                log.Info("不存在地市【" + districtName + "】的图层信息!");
            }
            return regionList;
        }

        private string getLayerFilePath(string path, string mapName)
        {
            string strLayerPath = string.Format("{0}" + Path.DirectorySeparatorChar + "{1}", path, mapName);
            if (File.Exists(strLayerPath))
            {
                return strLayerPath;
            }
            return string.Empty;
        }
        #endregion

        private List<ZTVillageTestGrid> coverageCellGrids(List<ZTVillageTestCellGrid> cellGridList)
        {
            WaitBox.Text = "正在汇聚地市【" + curDistrictName + "】的栅格数据!";
            Dictionary<string, ZTVillageTestGrid> gridDic = new Dictionary<string, ZTVillageTestGrid>(); 
            foreach (var cellGrid in cellGridList)
            {
                ZTVillageTestGrid grid;
                if (!gridDic.TryGetValue(cellGrid.GridToken, out grid))
                {
                    grid = new ZTVillageTestGrid();
                    grid.CenterLongutidue = cellGrid.CenterLongutidue;
                    grid.CenterLatitude = cellGrid.CenterLatitude;
                    gridDic.Add(cellGrid.GridToken, grid);
                }
                grid.Merge(cellGrid);
            }

            foreach (var item in gridDic.Values)
            {
                item.DealFinalResult();
            }

            return new List<ZTVillageTestGrid>(gridDic.Values);
        }

        private void setDistrictCountyGrids(string districtName, List<ZTVillageTestGrid> gridList, List<ResvRegion> countyList)
        {
            for (int i = gridList.Count - 1; i >= 0; i--)
            {
                var grid = gridList[i];
                foreach (var county in countyList)
                {
                    if (county.GeoOp.CheckPointInRegion(grid.CenterLongutidue, grid.CenterLatitude))
                    {
                        grid.DistrictName = districtName;
                        grid.CountyName = county.RegionName;
                        break;
                    }
                }
                if (string.IsNullOrEmpty(grid.DistrictName))
                {
                    gridList.Remove(grid);
                }
            }
        }

        private void dealTownGrids(List<ZTVillageTestGrid> gridList, List<ResvRegion> townList)
        {
            WaitBox.Text = "开始处理地市【" + curDistrictName + "】的乡镇数据...";
            Dictionary<string, List<ZTVillageTestGrid>> townGridDic = coverageTownGrids(gridList, townList);

            foreach (var item in townGridDic)
            {
                string path = getExportFilePath(item.Key, "乡镇", item.Value[0]);
                ZTExportVillageTestShpMapInfo.MakeShpFile(path, item.Value);
            }
        }

        private Dictionary<string, List<ZTVillageTestGrid>> coverageTownGrids(List<ZTVillageTestGrid> gridList, List<ResvRegion> townList)
        {
            Dictionary<string, List<ZTVillageTestGrid>> townGridDic = new Dictionary<string, List<ZTVillageTestGrid>>();
            foreach (var grid in gridList)
            {
                foreach (var town in townList)
                {
                    if (town.GeoOp.CheckPointInRegion(grid.CenterLongutidue, grid.CenterLatitude))
                    {
                        List<ZTVillageTestGrid> townGridList;
                        if (!townGridDic.TryGetValue(town.RegionName,out townGridList))
                        {
                            townGridList = new List<ZTVillageTestGrid>();
                            townGridDic.Add(town.RegionName, townGridList);
                        }
                        townGridList.Add(grid);
                        break;
                    }
                }
            }

            return townGridDic;
        }

        private void dealBranchGrids(List<ZTVillageTestGrid> gridList, string districtName, Dictionary<string, ResvRegion> branchDic)
        {
            WaitBox.Text = "开始处理地市【" + districtName + "】的支局数据...";
            Dictionary<string, List<ZTVillageTestGrid>> branchGridDic = coverageBranchGrids(gridList, branchDic);

            foreach (var item in branchGridDic)
            {
                string path = getExportFilePath(item.Key, "支局", item.Value[0]);
                ZTExportVillageTestShpMapInfo.MakeShpFile(path, item.Value);
            }
        }

        private string getExportFilePath(string name, string typeName,ZTVillageTestGrid grid)
        {
            string directory = string.Format("{0}{1}农村测试{1}{2}{1}{3}{1}移动{4}",
                exportShpPath, Path.DirectorySeparatorChar, grid.DistrictName, grid.CountyName, typeName);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            string path = string.Format("{0}{1}{2}.shp",
                directory, Path.DirectorySeparatorChar, name);
            return path;
        }

        private Dictionary<string, List<ZTVillageTestGrid>> coverageBranchGrids(List<ZTVillageTestGrid> gridList, Dictionary<string, ResvRegion> branchDic)
        {
            Dictionary<string, List<ZTVillageTestGrid>> branchGridDic = new Dictionary<string, List<ZTVillageTestGrid>>();
            foreach (var grid in gridList)
            {
                foreach (var branch in branchDic.Values)
                {
                    if (branch.GeoOp.CheckPointInRegion(grid.CenterLongutidue, grid.CenterLatitude))
                    {
                        List<ZTVillageTestGrid> branchGridList;
                        if (!branchGridDic.TryGetValue(branch.RegionName, out branchGridList))
                        {
                            branchGridList = new List<ZTVillageTestGrid>();
                            branchGridDic.Add(branch.RegionName, branchGridList);
                        }
                        branchGridList.Add(grid);
                        break;
                    }
                }
            }

            return branchGridDic;
        }

    }
}
