﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTCellWrongDir;
using MasterCom.Util;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellWrongDirQuery_LScan: DIYAnalyseByCellBackgroundBaseByPeriod
    {
        FileInfo curAnaFileInfo = new FileInfo();
        Dictionary<ICell, CellWrongDirItem_Scan> cellWrongDirDic = null;
        BackgroundFuncConfigManager bgCfgManager = null;

        private static ZTCellWrongDirQuery_LScan instance = null;
        protected static readonly object lockObj = new object();
        public static ZTCellWrongDirQuery_LScan GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellWrongDirQuery_LScan();
                    }
                }
            }
            return instance;
        }

        protected ZTCellWrongDirQuery_LScan()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_CW);
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
            carrierID = CarrierType.ChinaMobile;
        }

        public ZTCellWrongDirQuery_LScan(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "覆盖方向异常_LTE扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23006, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_SCAN");
            tmpDic.Add("themeName", (object)"TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                bgCfgManager = BackgroundFuncConfigManager.GetInstance();
                return true;
            }
            FuncCond = FuncCond ?? new CellWrongDirCondition(-120, 30, 60, 0, false, new TimePeriod(MainModel.PPStartTime, MainModel.PPEndTime), new TimePeriod(MainModel.PPStartTime, MainModel.PPEndTime));
            CellWrongDirSettingDlg_TD dlg=new CellWrongDirSettingDlg_TD();
            dlg.SetCondition(FuncCond);
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            FuncCond = dlg.GetConditon();
            this.cellWrongDirDic = new Dictionary<ICell, CellWrongDirItem_Scan>();
            this.finalResultList = new List<CellWrongDirItem_Scan>();
            return true;
        }


        protected override void FireShowFormAfterQuery()
        {
            CellWrongDirForm_Scan scanCellWrongDirForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(CellWrongDirForm_Scan)) as CellWrongDirForm_Scan;
            if (scanCellWrongDirForm == null || scanCellWrongDirForm.IsDisposed)
            {
                scanCellWrongDirForm = new CellWrongDirForm_Scan();
            }
            scanCellWrongDirForm.FillData(finalResultList);
            scanCellWrongDirForm.Owner = MainModel.MainForm;
            scanCellWrongDirForm.Visible = true;
            scanCellWrongDirForm.BringToFront();
            finalResultList = null;
        }

        protected virtual float? getRxLev(TestPoint tp, int idx)
        {
            return (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", idx];
        }

        public CellWrongDirCondition FuncCond { get; set; }
        protected override void doWithDTData(TestPoint tp)
        {
            for (int i = 0; i < 16; i++)
            {
                ICell cell = tp.GetCell_LTEScan(i);
                if (cell == null)
                {
                    continue;
                }
                float? rxLev = getRxLev(tp, i);
                if (!FuncCond.FilterByValue(rxLev))
                {
                    bool inRange = FuncCond.FilterByDistance(tp.Distance2(cell.Longitude, cell.Latitude));
                    if (!inRange)
                    {
                        bool wrongDir = FuncCond.IsWrongDir(cell, tp);
                        CellWrongDirItem_Scan wrongItem = null;
                        if (!cellWrongDirDic.TryGetValue(cell, out wrongItem))
                        {
                            wrongItem = new CellWrongDirItem_Scan(cell);
                            cellWrongDirDic[cell] = wrongItem;
                        }
                        wrongItem.AddPoint(tp, (float)rxLev, wrongDir, i == 0);
                    }
                }
            }
        }

        private List<CellWrongDirItem_Scan> finalResultList = null;
        protected override void getResultAfterQuery()
        {
            if (cellWrongDirDic == null)
            {
                return;
            }
            foreach (CellWrongDirItem_Scan item in cellWrongDirDic.Values)
            {
                if (item.WrongPntCnt == 0 || FuncCond.FilterByRate(item.WrongRate))
                {
                    continue;
                }
                finalResultList.Add(item);
            }
            cellWrongDirDic = null;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE扫频专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["DistanceMin"] = FuncCond.DistanceMin;
                param["AngleMin"] = FuncCond.AngleMin;
                param["RxLevMin"] = FuncCond.RxLevMin;
                param["WrongRateMin"] = FuncCond.WrongRateMin;
                param["SaveFilePath"] = FuncCond.SaveFilePath;
                param["IsExportNow"] = FuncCond.IsExportNow;
                param["ExportDateDiy"] = FuncCond.ExportDateDiy;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (FuncCond == null)
                {
                    FuncCond = new CellWrongDirCondition();
                }
                if (param.ContainsKey("DistanceMin"))
                {
                    FuncCond.DistanceMin = (double)param["DistanceMin"];
                }
                if (param.ContainsKey("AngleMin"))
                {
                    FuncCond.AngleMin = (int)param["AngleMin"];
                }
                if (param.ContainsKey("RxLevMin"))
                {
                    FuncCond.RxLevMin = (float)param["RxLevMin"];
                }
                if (param.ContainsKey("WrongRateMin"))
                {
                    FuncCond.WrongRateMin = (double)param["WrongRateMin"];
                }
                if (param.ContainsKey("SaveFilePath"))
                {
                    FuncCond.SaveFilePath = (string)param["SaveFilePath"];
                }
                if (param.ContainsKey("IsExportNow"))
                {
                    FuncCond.IsExportNow = (bool)param["IsExportNow"];
                }
                if (param.ContainsKey("ExportDateDiy"))
                {
                    FuncCond.ExportDateDiy = (int)param["ExportDateDiy"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CellWrongDirProperties_LTESCAN(this);
            }
        }
        protected override void doBackgroundStatByPeriod(ClientProxy clientProxy)
        {
            QueryCondition cond = new QueryCondition();
            cond.Geometorys = new SearchGeometrys();
            cond.Geometorys.Region = bgCfgManager.RegionBorder;
            SetQueryCondition(cond);
            getFilesForAnalyse();
            analyseFiles();
            exportResultInfo();
        }
        protected void getFilesForAnalyse()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
            BackgroundFuncQueryManager.GetInstance().GetFile_Cell(GetSubFuncID(), ServiceTypeString, ((int)carrierID).ToString());
        }
        protected void analyseFiles()
        {
            try
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取待分析文件" + MainModel.FileInfos.Count + "个...");
                
                int iloop = 0;
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (MainModel.BackgroundStopRequest)
                    {
                        break;
                    }

                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在分析 " + FuncType.ToString() +
                              SubFuncType.ToString() + " 类 " + Name + "，当前文件 " + (++iloop) + "/" + MainModel.FileInfos.Count +
                              "个...文件名：" + fileInfo.Name);

                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                    replay(fileInfo);
                }
                MainModel.ClearDTData();
            }
            catch (Exception e)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(e);
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
            }
        }
        protected void replay(FileInfo fileInfo)
        {
            curAnaFileInfo = fileInfo;
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.Add(fileInfo);
            condition.Geometorys = Condition.Geometorys;
            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
            query.Columns = new List<string>();
            query.Columns.Add("LTESCAN_TopN_CELL_Specific_RSRP");
            query.Columns.Add("LTESCAN_TopN_EARFCN");
            query.Columns.Add("LTESCAN_TopN_PCI");

            query.FilterSampleByRegion = true;
            query.IncludeTestPoint = true;
            query.IncludeEvent = false;
            query.IncludeMessage = false;
            query.SetQueryCondition(condition);
            query.Query();
            doStatWithQuery();
            if (MainModel.IsBackground)
            {
                saveBackgroundData();
            }
            MainModel.ClearDTData();
        }
        protected void doStatWithQuery()
        {
            this.cellWrongDirDic = new Dictionary<ICell, CellWrongDirItem_Scan>();
            this.finalResultList = new List<CellWrongDirItem_Scan>();
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint tp in fileDataManager.TestPoints)
                {
                    doWithDTData(tp);
                }
            }
            getResultAfterQuery();
        }
        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();

            if (finalResultList != null)
            {
                foreach (CellWrongDirItem_Scan item in finalResultList)
                {
                    BackgroundResult result = item.ConvertToBackgroundResult();
                    result.SubFuncID = GetSubFuncID();
                    result.ProjectString = bgCfgManager.ProjectType;
                    result.FileID = curAnaFileInfo.ID;
                    result.FileName = curAnaFileInfo.Name;
                    bgResultList.Add(result);
                }
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, bgResultList);
            finalResultList = new List<CellWrongDirItem_Scan>();
            curAnaFileInfo = null;
            //GC.Collect();
        }
        protected void exportResultInfo()
        {
            if (FuncCond.IsExportNow || DateTime.Now.Day == FuncCond.ExportDateDiy)
            {
                string timeDes = "";
                DateTime startTime = bgCfgManager.StartTime;
                DateTime endTime = bgCfgManager.EndTime;
                BackgroundFuncBaseSetting baseSetting = BackgroundFuncBaseSetting.GetInstance();
                if (baseSetting.queryTimeType != 0)
                {
                    while (startTime <= endTime)
                    {
                        DateTime nextTime = startTime.AddMonths(1);
                        timeDes = startTime.ToString("yyyyMM");
                        int iSTime = JavaDate.GetMonthFirstTime(startTime);
                        int iETime = JavaDate.GetMonthFirstTime(nextTime);

                        BackgroundFuncManager.GetInstance().ReportBackgroundInfo("开始查询"
                            + startTime.ToString("yyyy-MM-dd") + " 至 " + nextTime.ToString("yyyy-MM-dd") + "时段内已分析的记录...");

                        exportByPeriod(iSTime, iETime, timeDes);
                        startTime = nextTime;
                    }
                }
                else
                {
                    string strStime = startTime.ToString("yyyyMMdd");
                    string strEtime = endTime.ToString("yyyyMMdd");
                    if (strStime == strEtime)
                    {
                        timeDes = strStime;
                    }
                    else
                    {
                        timeDes = strStime + "-" + strEtime;
                    }

                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("开始查询"
                     + startTime.ToString("yyyy-MM-dd") + " 至 " + endTime.ToString("yyyy-MM-dd") + "时段内已分析的记录...");

                    exportByPeriod(bgCfgManager.ISTime, bgCfgManager.IETime, timeDes);
                }

            }
        }
        private void exportByPeriod(int iSTime, int iETime, string timeDes)
        {
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Cell(iSTime, iETime
                       , GetSubFuncID(), Name, StatType, bgCfgManager.ProjectType);

            if (BackgroundResultList == null || string.IsNullOrEmpty(FuncCond.SaveFilePath))
            {
                return;
            }
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取到已分析记录" + BackgroundResultList.Count + "条");
            if (BackgroundResultList.Count <= 0)
            {
                return;
            }
            string districtName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            string fileName = string.Format("{0}\\{1}覆盖不符结果{2}.csv"
                , FuncCond.SaveFilePath, districtName, timeDes);

            if (System.IO.File.Exists(fileName))
            {
                System.IO.File.Delete(fileName);
            }

            System.IO.FileStream fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.CreateNew
                , System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);

            try
            {
                streamWriter.WriteLine(CellWrongDirItem_Scan.GetResultTitle());
                foreach (BackgroundResult bgResult in BackgroundResultList)
                {
                    DateTime time = JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
                    LTECell cell = CellManager.GetInstance().GetLTECell(time, bgResult.LAC, bgResult.CI);
                    if (cell == null)
                    {
                        continue;
                    }
                    CellWrongDirItem_Scan info = new CellWrongDirItem_Scan(cell);
                    info.AddInfoFromBackgroundResult(bgResult);
                    info.GetMeanAndDiffWrongDir();

                    streamWriter.WriteLine(info.GetResultDesString());
                }
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("成功导出本时段内信息...");
            }
            catch (Exception ee)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(ee);
            }
            finally
            {
                BackgroundResultList = null;
                if (streamWriter != null)
                {
                    streamWriter.Close();
                }
                if (fileStream != null)
                {
                    fileStream.Close();
                }
                //GC.Collect();
            }
        }

        protected override void getBackgroundData()
        {
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Cell(bgCfgManager.ISTime, bgCfgManager.IETime
                , GetSubFuncID(), Name, StatType, bgCfgManager.ProjectType);
        }
        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                bgResult.GetImageValueInt();//wrongDirMax
                bgResult.GetImageValueInt();//wrongDirMin
                bgResult.GetImageValueInt();//totalPntCnt
                int wrongMCellCnt = bgResult.GetImageValueInt();
                bgResult.GetImageValueFloat();//longitudeMid
                bgResult.GetImageValueFloat();//latitudeMid

                StringBuilder sb = new StringBuilder();
                sb.Append("主强异常点个数：" + wrongMCellCnt);
                sb.AppendLine();
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }

    public class CellWrongDirCondition
    {
        public CellWrongDirCondition()
        { 
        }
        public CellWrongDirCondition(float rxLevMin, double distanceMin, int angleMin, double wrongRateMin, 
            bool twiceBatch, TimePeriod periodFirst, TimePeriod periodSecond)
        {
            this.RxLevMin = rxLevMin;
            this.DistanceMin = distanceMin;
            this.AngleMin = angleMin;
            this.WrongRateMin = wrongRateMin;

            this.IsTwiceBatch = twiceBatch;
            this.PeriodFirstBatch = periodFirst;
            this.PeriodSecondBatch = periodSecond;
        }

        public CellWrongDirCondition(float rxLevMin, double distanceMin, int angleMin, double wrongRateMin, int wrongSampleNum,
            bool twiceBatch, TimePeriod periodFirst, TimePeriod periodSecond)
        {
            this.RxLevMin = rxLevMin;
            this.DistanceMin = distanceMin;
            this.AngleMin = angleMin;
            this.WrongRateMin = wrongRateMin;
            this.WrongSampleNum = wrongSampleNum;

            this.IsTwiceBatch = twiceBatch;
            this.PeriodFirstBatch = periodFirst;
            this.PeriodSecondBatch = periodSecond;
        }

        public void SetThreShold(float rxLevMin, double distanceMin, int angleMin, double wrongRateMin)
        {
            this.RxLevMin = rxLevMin;
            this.DistanceMin = distanceMin;
            this.AngleMin = angleMin;
            this.WrongRateMin = wrongRateMin;
        }

        public void SetThreShold(float rxLevMin, double distanceMin, int angleMin, double wrongRateMin, int wrongSampleNum)
        {
            this.RxLevMin = rxLevMin;
            this.DistanceMin = distanceMin;
            this.AngleMin = angleMin;
            this.WrongRateMin = wrongRateMin;
            this.WrongSampleNum = wrongSampleNum;
        }

        public string SaveFilePath { get; set; }
        public bool IsExportNow { get; set; }
        
        public int ExportDateDiy { get; set; } = 1;

        public float RxLevMin { get;  set; }

        public double DistanceMin { get;  set; }

        public double WrongRateMin { get;  set; }

        public int WrongSampleNum { get; set; }

        public int AngleMin { get;  set; }

        public bool IsTwiceBatch { get; set; }

        public TimePeriod PeriodFirstBatch { get; set; }

        public TimePeriod PeriodSecondBatch { get; set; }

        public List<TimePeriod> GetTimePeriods
        {
            get
            {
                List<TimePeriod> timePeriods = new List<TimePeriod>(new TimePeriod[] { PeriodFirstBatch, PeriodSecondBatch });
                return timePeriods;
            }
        }

        public bool FilterByValue(float? rxLev)
        {
            return rxLev == null || rxLev < RxLevMin;
        }

        public bool FilterByDistance(double distance)
        {
            return distance < DistanceMin;
        }

        public bool IsWrongDir(ICell cell, TestPoint tp)
        {
            //采样点相对于小区的正北方向角度
            int angleN = MathFuncs.getAngleFromPointToPoint(cell.Longitude, cell.Latitude, tp.Longitude, tp.Latitude);
            //采样点与小区的最小夹角
            int angle_Cell_TestPoint = Math.Min(360 - Math.Abs(angleN - cell.Direction), Math.Abs(angleN - cell.Direction));
            return angle_Cell_TestPoint >= AngleMin;
        }

        public bool FilterByRate(double wrongRate)
        {
            return wrongRate < WrongRateMin;
        }

        public bool FilterByNum(int wrongNum)
        {
            return wrongNum < WrongSampleNum;
        }

    }

}
