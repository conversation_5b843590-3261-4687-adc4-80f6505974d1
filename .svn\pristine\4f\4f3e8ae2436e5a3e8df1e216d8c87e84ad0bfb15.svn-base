﻿namespace MasterCom.RAMS.AnyStat
{
    partial class AnyStatResultForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.menu_cms = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.expand_tsmi = new System.Windows.Forms.ToolStripMenuItem();
            this.collapse_tsmi = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.excep_tsmi = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.menu_cms.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.ContextMenuStrip = this.menu_cms;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.Location = new System.Drawing.Point(0, 0);
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(603, 353);
            this.treeListView.TabIndex = 0;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            // 
            // menu_cms
            // 
            this.menu_cms.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.expand_tsmi,
            this.collapse_tsmi,
            this.toolStripSeparator1,
            this.toolStripMenuItem2,
            this.toolStripSeparator3});
            this.menu_cms.Name = "menu_cms";
            this.menu_cms.Size = new System.Drawing.Size(153, 104);
            // 
            // expand_tsmi
            // 
            this.expand_tsmi.Name = "expand_tsmi";
            this.expand_tsmi.Size = new System.Drawing.Size(152, 22);
            this.expand_tsmi.Text = "展开所有";
            this.expand_tsmi.Click += new System.EventHandler(this.expand_tsmi_Click);
            // 
            // collapse_tsmi
            // 
            this.collapse_tsmi.Name = "collapse_tsmi";
            this.collapse_tsmi.Size = new System.Drawing.Size(152, 22);
            this.collapse_tsmi.Text = "折叠所有";
            this.collapse_tsmi.Click += new System.EventHandler(this.collapse_tsmi_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(149, 6);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.excep_tsmi});
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(152, 22);
            this.toolStripMenuItem2.Text = "导出";
            // 
            // excep_tsmi
            // 
            this.excep_tsmi.Name = "excep_tsmi";
            this.excep_tsmi.Size = new System.Drawing.Size(152, 22);
            this.excep_tsmi.Text = "Excel";
            this.excep_tsmi.Click += new System.EventHandler(this.excep_tsmi_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(149, 6);
            // 
            // AnyStatResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(603, 353);
            this.Controls.Add(this.treeListView);
            this.Name = "AnyStatResultForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "AnyStat统计结果表";
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.menu_cms.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private System.Windows.Forms.ContextMenuStrip menu_cms;
        private System.Windows.Forms.ToolStripMenuItem expand_tsmi;
        private System.Windows.Forms.ToolStripMenuItem collapse_tsmi;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem excep_tsmi;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
    }
}