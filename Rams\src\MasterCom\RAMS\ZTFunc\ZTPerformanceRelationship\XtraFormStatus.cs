﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc.ZTPerformRelated
{
    public partial class XtraFormStatus : DevExpress.XtraEditors.XtraForm
    {
        public XtraFormStatus()
        {
            InitializeComponent();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (!checkBox1.Checked && !checkBox2.Checked && !checkBox3.Checked && !checkBox4.Checked && !checkBox5.Checked)
            {
                this.DialogResult = DialogResult.Cancel;
            }
            else
            {
                this.DialogResult = DialogResult.OK;
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }


        public void getStatusSelect(out List<string> cb)
        {
           
                cb = new List<string>();
            if(checkBox1.Checked)
                cb.Add(checkBox1.Name);
            if (checkBox2.Checked)
                cb.Add(checkBox2.Name);
            if (checkBox3.Checked)
                cb.Add(checkBox3.Name);
            if (checkBox4.Checked)
                cb.Add(checkBox4.Name);
            if (checkBox5.Checked)
                cb.Add(checkBox5.Name);
            
        }
    }
}