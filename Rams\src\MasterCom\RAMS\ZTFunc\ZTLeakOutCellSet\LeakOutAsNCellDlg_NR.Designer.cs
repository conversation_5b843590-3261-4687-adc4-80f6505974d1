﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LeakOutAsNCellDlg_NR
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(LeakOutAsNCellDlg_NR));
            this.chkLteCell = new System.Windows.Forms.CheckBox();
            this.label5 = new System.Windows.Forms.Label();
            this.chkGetRoadDesc = new System.Windows.Forms.CheckBox();
            this.chkNBCell = new System.Windows.Forms.CheckBox();
            this.chkMainCell = new System.Windows.Forms.CheckBox();
            this.label3 = new System.Windows.Forms.Label();
            this.numRxLevDValue = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.numRxLev = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLev)).BeginInit();
            this.SuspendLayout();
            // 
            // chkLteCell
            // 
            this.chkLteCell.AutoSize = true;
            this.chkLteCell.Location = new System.Drawing.Point(43, 231);
            this.chkLteCell.Margin = new System.Windows.Forms.Padding(4, 6, 4, 6);
            this.chkLteCell.Name = "chkLteCell";
            this.chkLteCell.Size = new System.Drawing.Size(169, 22);
            this.chkLteCell.TabIndex = 26;
            this.chkLteCell.Text = "分析锚点LTE小区";
            this.chkLteCell.UseVisualStyleBackColor = true;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.label5.Location = new System.Drawing.Point(210, 189);
            this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(98, 18);
            this.label5.TabIndex = 25;
            this.label5.Text = "(速度较慢)";
            // 
            // chkGetRoadDesc
            // 
            this.chkGetRoadDesc.AutoSize = true;
            this.chkGetRoadDesc.Location = new System.Drawing.Point(43, 185);
            this.chkGetRoadDesc.Margin = new System.Windows.Forms.Padding(4, 6, 4, 6);
            this.chkGetRoadDesc.Name = "chkGetRoadDesc";
            this.chkGetRoadDesc.Size = new System.Drawing.Size(178, 22);
            this.chkGetRoadDesc.TabIndex = 24;
            this.chkGetRoadDesc.Text = "获取覆盖道路信息";
            this.chkGetRoadDesc.UseVisualStyleBackColor = true;
            // 
            // chkNBCell
            // 
            this.chkNBCell.AutoSize = true;
            this.chkNBCell.Location = new System.Drawing.Point(249, 22);
            this.chkNBCell.Margin = new System.Windows.Forms.Padding(4, 6, 4, 6);
            this.chkNBCell.Name = "chkNBCell";
            this.chkNBCell.Size = new System.Drawing.Size(142, 22);
            this.chkNBCell.TabIndex = 23;
            this.chkNBCell.Text = "同时计算邻区";
            this.chkNBCell.UseVisualStyleBackColor = true;
            this.chkNBCell.CheckedChanged += new System.EventHandler(this.chkNBCell_CheckedChanged);
            // 
            // chkMainCell
            // 
            this.chkMainCell.AutoSize = true;
            this.chkMainCell.Checked = true;
            this.chkMainCell.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkMainCell.Location = new System.Drawing.Point(43, 22);
            this.chkMainCell.Margin = new System.Windows.Forms.Padding(4, 6, 4, 6);
            this.chkMainCell.Name = "chkMainCell";
            this.chkMainCell.Size = new System.Drawing.Size(160, 22);
            this.chkMainCell.TabIndex = 22;
            this.chkMainCell.Text = "只计算主服小区";
            this.chkMainCell.UseVisualStyleBackColor = true;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(351, 134);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(35, 18);
            this.label3.TabIndex = 21;
            this.label3.Text = "dBm";
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numRxLevDValue.Location = new System.Drawing.Point(236, 127);
            this.numRxLevDValue.Margin = new System.Windows.Forms.Padding(4, 6, 4, 6);
            this.numRxLevDValue.Maximum = new decimal(new int[] {
            120,
            0,
            0,
            0});
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Size = new System.Drawing.Size(104, 28);
            this.numRxLevDValue.TabIndex = 20;
            this.numRxLevDValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxLevDValue.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(61, 134);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(170, 18);
            this.label4.TabIndex = 19;
            this.label4.Text = "邻区与主服电平差≤";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(351, 85);
            this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(35, 18);
            this.label2.TabIndex = 18;
            this.label2.Text = "dBm";
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(311, 266);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(4, 6, 4, 6);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(107, 36);
            this.btnCancel.TabIndex = 17;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(196, 266);
            this.btnOK.Margin = new System.Windows.Forms.Padding(4, 6, 4, 6);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(107, 36);
            this.btnOK.TabIndex = 16;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // numRxLev
            // 
            this.numRxLev.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numRxLev.Location = new System.Drawing.Point(236, 79);
            this.numRxLev.Margin = new System.Windows.Forms.Padding(4, 6, 4, 6);
            this.numRxLev.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxLev.Minimum = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numRxLev.Name = "numRxLev";
            this.numRxLev.Size = new System.Drawing.Size(104, 28);
            this.numRxLev.TabIndex = 15;
            this.numRxLev.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxLev.Value = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(130, 85);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(98, 18);
            this.label1.TabIndex = 14;
            this.label1.Text = "邻区电平≥";
            // 
            // LeakOutAsNCellDlg_NR
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 22F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(429, 314);
            this.Controls.Add(this.chkLteCell);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.chkGetRoadDesc);
            this.Controls.Add(this.chkNBCell);
            this.Controls.Add(this.chkMainCell);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.numRxLevDValue);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.numRxLev);
            this.Controls.Add(this.label1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(8, 11, 8, 11);
            this.Name = "LeakOutAsNCellDlg_NR";
            this.Text = "覆盖外泄条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLev)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.CheckBox chkLteCell;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.CheckBox chkGetRoadDesc;
        private System.Windows.Forms.CheckBox chkNBCell;
        private System.Windows.Forms.CheckBox chkMainCell;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numRxLevDValue;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.NumericUpDown numRxLev;
        private System.Windows.Forms.Label label1;
    }
}