<?xml version="1.0"?>
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<Configs>
	<Config name="StatParamCfg">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item key="Name" typeName="String">GSM MTR参数</Item>
				<Item key="FName" typeName="String"/>
				<Item key="FDesc" typeName="String"></Item>
				<Item key="children" typeName="IList">
					<Item typeName="IDictionary">
						<Item key="Name" typeName="String">基本参数</Item>
						<Item key="FName" typeName="String"/>
						<Item key="FDesc" typeName="String"></Item>
						<Item key="children" typeName="IList">
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Fileid</Item>
								<Item key="FName" typeName="String">Ux_0801</Item>
								<Item key="FDesc" typeName="String">文件ID</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">TestType</Item>
								<Item key="FName" typeName="String">Ux_0802</Item>
								<Item key="FDesc" typeName="String">测试类型</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">MS</Item>
								<Item key="FName" typeName="String">Ux_0803</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">FileTime</Item>
								<Item key="FName" typeName="String">Ux_0804</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Duration</Item>
								<Item key="FName" typeName="String">Ux_0805</Item>
								<Item key="FDesc" typeName="String">总时长</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Distance</Item>
								<Item key="FName" typeName="String">Ux_0806</Item>
								<Item key="FDesc" typeName="String">总里程</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">SampleNum</Item>
								<Item key="FName" typeName="String">Ux_0807</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">TLLongitude</Item>
								<Item key="FName" typeName="String">Ux_0808</Item>
								<Item key="FDesc" typeName="String">左上角经度</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">TLLatitude</Item>
								<Item key="FName" typeName="String">Ux_0809</Item>
								<Item key="FDesc" typeName="String">左上角纬度</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BRLongitude</Item>
								<Item key="FName" typeName="String">Ux_080A</Item>
								<Item key="FDesc" typeName="String">右下角经度</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BRLatitude</Item>
								<Item key="FName" typeName="String">Ux_080B</Item>
								<Item key="FDesc" typeName="String">右下角纬度</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">LAC</Item>
								<Item key="FName" typeName="String">Ux_080C</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">RAC</Item>
								<Item key="FName" typeName="String">Ux_080D</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">CI</Item>
								<Item key="FName" typeName="String">Ux_080E</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Duration_GPRS(毫秒)</Item>
								<Item key="FName" typeName="String">Ux_081B</Item>
								<Item key="FDesc" typeName="String">总时长(GPRS业务)</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Distance_GPRS(米)</Item>
								<Item key="FName" typeName="String">Ux_081F</Item>
								<Item key="FDesc" typeName="String">总里程(GPRS业务)</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Duration_EDGE(毫秒)</Item>
								<Item key="FName" typeName="String">Ux_081C</Item>
								<Item key="FDesc" typeName="String">总时长(EDGE业务)</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Distance_EDGE(米)</Item>
								<Item key="FName" typeName="String">Ux_0820</Item>
								<Item key="FDesc" typeName="String">总里程(EDGE业务)</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Duration_GSM_VOICE(毫秒)</Item>
								<Item key="FName" typeName="String">Ux_0823</Item>
								<Item key="FDesc" typeName="String">总时长(GSM语音业务)</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Distance_GSM_VOICE(米)</Item>
								<Item key="FName" typeName="String">Ux_0824</Item>
								<Item key="FDesc" typeName="String">总里程(GSM语音业务)</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Duration_GSM_VOICE_IDLE(毫秒)</Item>
								<Item key="FName" typeName="String">Ux_0835</Item>
								<Item key="FDesc" typeName="String">总时长(GSM空闲业务)</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Distance_GSM_VOICE_IDLE(米)</Item>
								<Item key="FName" typeName="String">Ux_0836</Item>
								<Item key="FDesc" typeName="String">总里程(GSM空闲业务)</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Duration_GSM_VOICE_DEDICATED(毫秒)</Item>
								<Item key="FName" typeName="String">Ux_0837</Item>
								<Item key="FDesc" typeName="String">通话时长</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Distance_GSM_VOICE_DEDICATED(米)</Item>
								<Item key="FName" typeName="String">Ux_0838</Item>
								<Item key="FDesc" typeName="String">通话里程</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Duration_Gis(毫秒)</Item>
								<Item key="FName" typeName="String">Ux_084C</Item>
								<Item key="FDesc" typeName="String">有经纬度时长</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Duration_NoGis(毫秒)</Item>
								<Item key="FName" typeName="String">Ux_084D</Item>
								<Item key="FDesc" typeName="String">无经纬度时长</Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item key="Name" typeName="String">GSM MTR业务参数</Item>
						<Item key="FName" typeName="String"/>
						<Item key="FDesc" typeName="String"></Item>
						<Item key="children" typeName="IList">
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">TA</Item>
								<Item key="FName" typeName="String"/>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="children" typeName="IList">
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_Sample</Item>
										<Item key="FName" typeName="String">Ux_67180C</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_Max</Item>
										<Item key="FName" typeName="String">Ux_671809</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_Min</Item>
										<Item key="FName" typeName="String">Ux_67180A</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_Mean</Item>
										<Item key="FName" typeName="String">Ux_67180B</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_0</Item>
										<Item key="FName" typeName="String">Ux_671801</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_1</Item>
										<Item key="FName" typeName="String">Ux_67180D</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_2</Item>
										<Item key="FName" typeName="String">Ux_671802</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_3</Item>
										<Item key="FName" typeName="String">Ux_67180E</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_4</Item>
										<Item key="FName" typeName="String">Ux_67180F</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_5</Item>
										<Item key="FName" typeName="String">Ux_671803</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_6_8</Item>
										<Item key="FName" typeName="String">Ux_671804</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_8_10</Item>
										<Item key="FName" typeName="String">Ux_671805</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_10_20</Item>
										<Item key="FName" typeName="String">Ux_671806</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_20_63</Item>
										<Item key="FName" typeName="String">Ux_671807</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
									<Item typeName="IDictionary">
										<Item key="Name" typeName="String">GSM_UPLINK_TA_63</Item>
										<Item key="FName" typeName="String">Ux_671808</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item key="FTag" typeName="Int32">-1</Item>
										<Item key="children" typeName="IList"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Rxlev_DL</Item>
								<Item key="FName" typeName="String"/>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="children" typeName="IList">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_SampleNum</Item>
										<Item typeName="String" key="FName">Ux_5A180F01</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Max</Item>
										<Item typeName="String" key="FName">Ux_5A180F03</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Min</Item>
										<Item typeName="String" key="FName">Ux_5A180F04</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Mean</Item>
										<Item typeName="String" key="FName">Ux_5A180F02</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Sample_120</Item>
										<Item typeName="String" key="FName">Ux_5A180F05</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Sample_120_94</Item>
										<Item typeName="String" key="FName">Ux_5A180F06</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Sample_94_90</Item>
										<Item typeName="String" key="FName">Ux_5A180F07</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Sample_90_85</Item>
										<Item typeName="String" key="FName">Ux_5A180F08</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Sample_85_80</Item>
										<Item typeName="String" key="FName">Ux_5A180F09</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Sample_80_75</Item>
										<Item typeName="String" key="FName">Ux_5A180F0A</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Sample_75</Item>
										<Item typeName="String" key="FName">Ux_5A180F17</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Duration_120</Item>
										<Item typeName="String" key="FName">Ux_5A180F0B</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Duration_120_94</Item>
										<Item typeName="String" key="FName">Ux_5A180F0C</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Duration_94_90</Item>
										<Item typeName="String" key="FName">Ux_5A180F0D</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Duration_90_85</Item>
										<Item typeName="String" key="FName">Ux_5A180F0E</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Duration_85_80</Item>
										<Item typeName="String" key="FName">Ux_5A180F0F</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Duration_80_75</Item>
										<Item typeName="String" key="FName">Ux_5A180F10</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Duration_75</Item>
										<Item typeName="String" key="FName">Ux_5A180F18</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Distance_120</Item>
										<Item typeName="String" key="FName">Ux_5A180F11</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Distance_120_94</Item>
										<Item typeName="String" key="FName">Ux_5A180F12</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Distance_94_90</Item>
										<Item typeName="String" key="FName">Ux_5A180F13</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Distance_90_85</Item>
										<Item typeName="String" key="FName">Ux_5A180F14</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Distance_85_80</Item>
										<Item typeName="String" key="FName">Ux_5A180F15</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Distance_80_75</Item>
										<Item typeName="String" key="FName">Ux_5A180F16</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_DL_Rxlev_Distance_75</Item>
										<Item typeName="String" key="FName">Ux_5A180F19</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Rxqual_DL</Item>
								<Item key="FName" typeName="String"/>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="children" typeName="IList">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_SampleNum</Item>
										<Item typeName="String" key="FName">Ux_5A18110C</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_RXQUAL_Max</Item>
										<Item typeName="String" key="FName">Ux_5A181109</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_RXQUAL_Min</Item>
										<Item typeName="String" key="FName">Ux_5A18110A</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_RXQUAL_Mean</Item>
										<Item typeName="String" key="FName">Ux_5A18110B</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_RXQUAL_0</Item>
										<Item typeName="String" key="FName">Ux_5A181101</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_RXQUAL_1</Item>
										<Item typeName="String" key="FName">Ux_5A181102</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_RXQUAL_2</Item>
										<Item typeName="String" key="FName">Ux_5A181103</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_RXQUAL_3</Item>
										<Item typeName="String" key="FName">Ux_5A181104</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_RXQUAL_4</Item>
										<Item typeName="String" key="FName">Ux_5A181105</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_RXQUAL_5</Item>
										<Item typeName="String" key="FName">Ux_5A181106</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_RXQUAL_6</Item>
										<Item typeName="String" key="FName">Ux_5A181107</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_DL_RXQUAL_7</Item>
										<Item typeName="String" key="FName">Ux_5A181108</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Rxlev_UL</Item>
								<Item key="FName" typeName="String"/>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="children" typeName="IList">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_SampleNum</Item>
										<Item typeName="String" key="FName">Ux_5A180E01</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Max</Item>
										<Item typeName="String" key="FName">Ux_5A180E03</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Min</Item>
										<Item typeName="String" key="FName">Ux_5A180E04</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Mean</Item>
										<Item typeName="String" key="FName">Ux_5A180E02</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Sample_120</Item>
										<Item typeName="String" key="FName">Ux_5A180E05</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Sample_120_94</Item>
										<Item typeName="String" key="FName">Ux_5A180E06</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Sample_94_90</Item>
										<Item typeName="String" key="FName">Ux_5A180E07</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Sample_90_85</Item>
										<Item typeName="String" key="FName">Ux_5A180E08</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Sample_85_80</Item>
										<Item typeName="String" key="FName">Ux_5A180E09</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Sample_80_75</Item>
										<Item typeName="String" key="FName">Ux_5A180E0A</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Sample_75</Item>
										<Item typeName="String" key="FName">Ux_5A180E17</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Duration_120</Item>
										<Item typeName="String" key="FName">Ux_5A180E0B</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Duration_120_94</Item>
										<Item typeName="String" key="FName">Ux_5A180E0C</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Duration_94_90</Item>
										<Item typeName="String" key="FName">Ux_5A180E0D</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Duration_90_85</Item>
										<Item typeName="String" key="FName">Ux_5A180E0E</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Duration_85_80</Item>
										<Item typeName="String" key="FName">Ux_5A180E0F</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Duration_80_75</Item>
										<Item typeName="String" key="FName">Ux_5A180E10</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Duration_75</Item>
										<Item typeName="String" key="FName">Ux_5A180E18</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Distance_120</Item>
										<Item typeName="String" key="FName">Ux_5A180E11</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Distance_120_94</Item>
										<Item typeName="String" key="FName">Ux_5A180E12</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Distance_94_90</Item>
										<Item typeName="String" key="FName">Ux_5A180E13</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Distance_90_85</Item>
										<Item typeName="String" key="FName">Ux_5A180E14</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Distance_85_80</Item>
										<Item typeName="String" key="FName">Ux_5A180E15</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Distance_80_75</Item>
										<Item typeName="String" key="FName">Ux_5A180E16</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxlev_UL_Rxlev_Distance_75</Item>
										<Item typeName="String" key="FName">Ux_5A180E19</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">Rxqual_UL</Item>
								<Item key="FName" typeName="String"/>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="children" typeName="IList">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_SampleNum</Item>
										<Item typeName="String" key="FName">Ux_5A18100C</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_RXQUAL_Max</Item>
										<Item typeName="String" key="FName">Ux_5A181009</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_RXQUAL_Min</Item>
										<Item typeName="String" key="FName">Ux_5A18100A</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_RXQUAL_Mean</Item>
										<Item typeName="String" key="FName">Ux_5A18100B</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_RXQUAL_0</Item>
										<Item typeName="String" key="FName">Ux_5A181001</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_RXQUAL_1</Item>
										<Item typeName="String" key="FName">Ux_5A181002</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_RXQUAL_2</Item>
										<Item typeName="String" key="FName">Ux_5A181003</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_RXQUAL_3</Item>
										<Item typeName="String" key="FName">Ux_5A181004</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_RXQUAL_4</Item>
										<Item typeName="String" key="FName">Ux_5A181005</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_RXQUAL_5</Item>
										<Item typeName="String" key="FName">Ux_5A181006</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_RXQUAL_6</Item>
										<Item typeName="String" key="FName">Ux_5A181007</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_UPLINK_Rxqual_UL_RXQUAL_7</Item>
										<Item typeName="String" key="FName">Ux_5A181008</Item>
										<Item key="FDesc" typeName="String"></Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item key="Name" typeName="String">GSM MTR消息</Item>
						<Item key="FName" typeName="String"/>
						<Item key="FDesc" typeName="String"></Item>
						<Item key="children" typeName="IList">
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">呼叫 Setup</Item>
								<Item key="FName" typeName="String">Mt_773</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">振铃 Alerting</Item>
								<Item key="FName" typeName="String">Mt_769</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">接通 Connect</Item>
								<Item key="FName" typeName="String">Mt_775</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">结束 Disconnect</Item>
								<Item key="FName" typeName="String">Mt_805</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">掉话 Release indication not received, timer T3109 expired</Item>
								<Item key="FName" typeName="String">Mt_168493336</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC内切换申请 Intra BSC hand over</Item>
								<Item key="FName" typeName="String">Mt_168493315</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC内切换申请 Intra BSC inter cell hand over during assignment</Item>
								<Item key="FName" typeName="String">Mt_168493355</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC内切换申请 Intra BSC intra cell hand over assignment command</Item>
								<Item key="FName" typeName="String">Mt_168493362</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC内切换成功 Intra BSC inter cell hand over complete</Item>
								<Item key="FName" typeName="String">Mt_168493316</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC内切换成功 Intra BSC inter cell hand over complete during assignment</Item>
								<Item key="FName" typeName="String">Mt_168493356</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC内切换成功 Intra BSC intra cell hand over assignment</Item>
								<Item key="FName" typeName="String">Mt_168493363</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC内切换失败 Intra BSC inter cell hand over failure</Item>
								<Item key="FName" typeName="String">Mt_168493317</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC内切换失败 Intra BSC inter cell hand over failure during assignment</Item>
								<Item key="FName" typeName="String">Mt_168493357</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC内切换失败 Intra BSC intra cell hand over assignment failure</Item>
								<Item key="FName" typeName="String">Mt_168493364</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC间切换申请 Inter BSC hand over required</Item>
								<Item key="FName" typeName="String">Mt_168493318</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC间切换申请 Inter BSC hand over required during assignment</Item>
								<Item key="FName" typeName="String">Mt_168493358</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC间切换成功 Inter BSC hand over complete</Item>
								<Item key="FName" typeName="String">Mt_168493320</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC间切换成功 Inter BSC hand over complete during assignment</Item>
								<Item key="FName" typeName="String">Mt_168493360</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC间切换失败 Inter BSC hand over failure</Item>
								<Item key="FName" typeName="String">Mt_168493321</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">BSC间切换失败 Inter BSC hand over failure during assignment</Item>
								<Item key="FName" typeName="String">Mt_168493361</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">G - T切换申请 Inter system hand over required</Item>
								<Item key="FName" typeName="String">Mt_168493387</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
							<Item typeName="IDictionary">
								<Item key="Name" typeName="String">G - T切换失败 Inter system hand over failure</Item>
								<Item key="FName" typeName="String">Mt_168493389</Item>
								<Item key="FDesc" typeName="String"></Item>
								<Item key="FTag" typeName="Int32">-1</Item>
								<Item key="children" typeName="IList"/>
							</Item>
						</Item>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
</Configs>
