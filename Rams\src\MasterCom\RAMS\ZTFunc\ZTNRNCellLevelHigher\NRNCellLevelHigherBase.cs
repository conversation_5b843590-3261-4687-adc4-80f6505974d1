﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRNCellLevelHigherBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();

        public NRNCellLevelHigherBase() : base(MainModel.GetInstance())
        {

        }

        NRNCellLevelHigherCond cond = new NRNCellLevelHigherCond();
        List<NRNCellLevelHigherResultInfo> resList;

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35032, this.Name);
        }

        protected override void getReadyBeforeQuery()
        {
            resList = new List<NRNCellLevelHigherResultInfo>();

            Columns = NRTpHelper.InitBaseReplayParamBackground(true, true);

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            NRNCellLevelHigherDlg dlg = new NRNCellLevelHigherDlg();
            dlg.SetCondtion(cond);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                cond = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void fireShowForm()
        {
            NRNCellLevelHigherForm lteCellSetForm = MainModel.CreateResultForm(typeof(NRNCellLevelHigherForm)) as NRNCellLevelHigherForm;
            lteCellSetForm.FillData(resList);
            lteCellSetForm.Visible = true;
            lteCellSetForm.BringToFront();
            mainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (WaitBox.CancelRequest)
                {
                    return;
                }

                CurCondtionRes curRes = new CurCondtionRes(cond);
                for (int i = 0; i < file.TestPoints.Count; i++)
                {
                    TestPoint tp = file.TestPoints[i];
                    RsrpInfo info = new RsrpInfo();
                    bool isNCellHigherPoint = judgeNCellHigherPoint(tp, info);
                    if (isNCellHigherPoint)
                    {
                        curRes.Add(tp, true, info);
                    }
                    else
                    {
                        curRes = dealNotNCellHigherPoint(tp, curRes, info);
                    }
                }
            }
        }

        private CurCondtionRes dealNotNCellHigherPoint(TestPoint tp, CurCondtionRes curRes, RsrpInfo info)
        {
            //1.判断添加该点后是否满足条件
            bool isValidRes = curRes.JudgeValidRes(curRes);
            if (isValidRes)
            {
                //1.1.满足则继续添加
                curRes.Add(tp, false, info);
            }
            else
            {
                //1.2.不满足则判断添加前是否满足条件
                isValidRes = curRes.JudgeValidRes();
                if (isValidRes)
                {
                    //1.2.1.满足则保存结果
                    saveRes(curRes);
                }
                else
                {
                    //1.2.2.不满足舍弃
                }
                curRes = new CurCondtionRes(cond);
            }
            return curRes;
        }

        protected bool judgeNCellHigherPoint(TestPoint tp, RsrpInfo info)
        {
            float? fRsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            if (fRsrp == null)
            {
                return false;
            }

            int? arfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            float maxNRsrp = -1000;
            int idx = 0;//最强邻区序号,用于最后获取最强邻区的其他数据
            for (int i = 0; i < 14; i++)
            {
                bool isValid = getValidNCell(tp, i, arfcn);
                if (isValid)
                {
                    float? fNRsrp = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                    if (fNRsrp == null)
                    {
                        break;
                    }
                    if (fNRsrp > maxNRsrp)
                    {
                        maxNRsrp = (float)fNRsrp;
                        idx = i;
                    }
                }
            }

            float diff = maxNRsrp - (float)fRsrp;
            info.RSRP = (float)fRsrp;
            info.NRSRP = maxNRsrp;
            info.NIdx = idx;
            info.RSRPDiff = diff;
            if (diff >= cond.RsrpDiff)
            {
                return true;
            }

            return false;
        }

        private bool getValidNCell(TestPoint tp, int i, int? earfcn)
        {
            NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(tp, i);
            if (type == NRTpHelper.NRNCellType.NCELL)
            {
                if (cond.CoFreqOnly)
                {
                    int? nEarfcn = (int?)NRTpHelper.NrTpManager.GetNEARFCN(tp, i);
                    if (earfcn != null && earfcn == nEarfcn)
                    {
                        return true;
                    }
                    return false;
                }
                return true;
            }
            return false;
        }

        class CurCondtionRes
        {
            public CurCondtionRes(NRNCellLevelHigherCond cond)
            {
                this.cond = cond;
            }
            readonly NRNCellLevelHigherCond cond;

            public double Distance { get; set; } = 0;
            public double Duration { get; set; } = 0;
            public double Rate { get; set; } = 0;

            public TestPoint LastTP { get; set; } = null;
            public DateTime StartTime { get; private set; }

            public int TotalTPCount { get; private set; }
            public int NCellLevelHigherTPCount { get; private set; }
            public Dictionary<TestPoint, RsrpInfo> TPRsrpDic { get; set; } = new Dictionary<TestPoint, RsrpInfo>();

            public void Add(TestPoint tp, bool isNCellLevelHigherTP, RsrpInfo info)
            {
                info.IsHighRsrpPoint = isNCellLevelHigherTP;
                if (isNCellLevelHigherTP)
                {
                    NCellLevelHigherTPCount++;
                }
                TotalTPCount++;
                TPRsrpDic[tp] = info;

                if (LastTP == null)
                {
                    LastTP = tp;
                    StartTime = tp.DateTime;
                    return;
                }

                double tmpDistance = tp.Distance2(LastTP);
                Distance += tmpDistance;

                TimeSpan ts = tp.DateTime.Subtract(LastTP.DateTime);
                double tmpDuration = ts.TotalSeconds;
                Duration += tmpDuration;

                LastTP = tp;
            }

            public bool JudgeValidRes()
            {
                Rate = Math.Round(NCellLevelHigherTPCount * 100d / TotalTPCount, 2);
                if (Rate >= cond.Rate)
                {
                    //if (!cond.CheckDistance || Distance >= cond.Distance)
                    //{
                    //    if (!cond.CheckDuration || Duration >= cond.Duration)
                    //    {
                    //        return true;
                    //    }
                    //    return false;
                    //}
                    //return false;
                    return true;
                }
                return false;
            }

            public bool JudgeValidRes(CurCondtionRes res)
            {
                if (LastTP == null)
                {
                    return false;
                }

                int count = res.NCellLevelHigherTPCount;
                int total = res.TotalTPCount + 1;

                double rate = Math.Round(count * 100d / total, 2);
                if (rate >= cond.Rate)
                {
                    return true;
                }
                return false;
            }
        }

        public class RsrpInfo
        {
            public float RSRP { get; set; } = -1000;
            public float RSRPDiff { get; set; } = -1000;
            public float NRSRP { get; set; } = -1000;
            public int NIdx { get; set; } = 0;
            public bool IsHighRsrpPoint { get; set; }
        }

        private void saveRes(CurCondtionRes curRes)
        {
            if ((!cond.CheckDistance || curRes.Distance >= cond.Distance)
                && (!cond.CheckDuration || curRes.Duration >= cond.Duration))
            {
                NRNCellLevelHigherResultInfo res = new NRNCellLevelHigherResultInfo();
                res.Distance = Math.Round(curRes.Distance, 2);
                res.Duration = Math.Round(curRes.Duration, 2);
                res.Rate = curRes.Rate.ToString() + "%";
                res.ErrorStartTime = curRes.StartTime;
                res.FileName = curRes.LastTP.FileName;
                res.TPCount = curRes.NCellLevelHigherTPCount;
                res.TPResList = new List<NRNCellLevelHigherInfo>();
                foreach (var tpRsrp in curRes.TPRsrpDic)
                {
                    addTPOtherInfo(res.TPResList, tpRsrp.Key, tpRsrp.Value);
                }
                res.Calculate();
                resList.Add(res);
            }
        }

        protected void addTPOtherInfo(List<NRNCellLevelHigherInfo> tpResList, TestPoint tp, RsrpInfo rsrpInfo)
        {
            NRNCellLevelHigherInfo info = new NRNCellLevelHigherInfo();
            setMainCellInfo(tp, info);

            setNCellInfo(tp, rsrpInfo, info);

            info.IsHighRsrpPoint = rsrpInfo.IsHighRsrpPoint;
            info.Longitude = tp.Longitude;
            info.Latitude = tp.Latitude;
            info.RSRP = getValidRsrp(rsrpInfo.RSRP);
            info.NRSRP = getValidRsrp(rsrpInfo.NRSRP);
            info.RSRPDiff = getValidRsrp(rsrpInfo.RSRPDiff);
            info.TestPoint = tp;
            tpResList.Add(info);
        }

        private static void setMainCellInfo(TestPoint tp, NRNCellLevelHigherInfo info)
        {
            NRCell cell = tp.GetMainCell_NR();
            if (cell == null)
            {
                int? arfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
                if (arfcn != null)
                {
                    info.EARFCN = (int)arfcn;
                }
                int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
                if (pci != null)
                {
                    info.PCI = (int)pci;
                }
                int? tac = (int?)NRTpHelper.NrTpManager.GetTAC(tp);
                if (tac != null)
                {
                    info.TAC = (int)tac;
                }
                long? nci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
                if (nci != null)
                {
                    info.NCI = (long)nci;
                }

                info.CellName = arfcn + "_" + pci;
            }
            else
            {
                info.EARFCN = cell.SSBARFCN;
                info.PCI = cell.PCI;
                info.TAC = cell.TAC;
                info.NCI = cell.NCI;
                info.CellName = cell.Name;
            }
        }

        private void setNCellInfo(TestPoint tp, RsrpInfo rsrpInfo, NRNCellLevelHigherInfo info)
        {
            NRCell nCell = tp.GetNBCell_NR(rsrpInfo.NIdx);
            if (nCell == null)
            {
                int? arfcn = (int?)NRTpHelper.NrTpManager.GetNEARFCN(tp, rsrpInfo.NIdx);
                if (arfcn != null)
                {
                    info.NEARFCN = (int)arfcn;
                }
                int? pci = (int?)NRTpHelper.NrTpManager.GetNPCI(tp, rsrpInfo.NIdx);
                if (pci != null)
                {
                    info.NPCI = (int)pci;
                }
            }
            else
            {
                info.NEARFCN = nCell.SSBARFCN;
                info.NPCI = nCell.PCI;
                info.NTAC = nCell.TAC;
                info.NNCI = nCell.NCI;
            }
        }

        private string getValidRsrp(float rsrp)
        {
            if (rsrp > 100 || rsrp < -200)
            {
                return "";
            }
            return Math.Round(rsrp, 2).ToString();
        }
    }

    public class NRNCellLevelHigherByRegion : NRNCellLevelHigherBase
    {
        private static NRNCellLevelHigherByRegion instance = null;
        public static NRNCellLevelHigherByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRNCellLevelHigherByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "邻区强覆盖(按区域)"; }
        }
    }

    public class NRNCellLevelHigherByFile : NRNCellLevelHigherBase
    {
        private static NRNCellLevelHigherByFile intance = null;
        public static NRNCellLevelHigherByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRNCellLevelHigherByFile();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "邻区强覆盖(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
