﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRoadQualAnaByGrid : QueryBase
    {
        Dictionary<int, RoadPartAreaBaseInfo> roadPartAreaBaseInfoDic;//小路段基础信息
        Dictionary<int, RoadPartAnaInfo> roadPartAnaInfoDic;//小路段指标信息
        Dictionary<int, RoadLabelBaseInfo> roadLabelBaseInfoDic;//路段基础信息
        List<RoadLabelEvtAnaInfo> roadEvtAnaInfoList;//测试异常概率信息（根据异常事件计算）
        protected bool isOnlyReLoadExitCellInfo = false;

        RoadQualAnaInfoSum resultSumInfo;
        RoadQualAnaCond roadAnaCond;

        protected static readonly object lockObj = new object();
        private static ZTRoadQualAnaByGrid instance = null;
        public static ZTRoadQualAnaByGrid GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTRoadQualAnaByGrid();
                    }
                }
            }
            return instance;
        }

        protected ZTRoadQualAnaByGrid()
            : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "道路质量分析"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18048, this.Name);
        }
        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }
        protected override bool isValidCondition()
        {
            RoadQualAnaDlg dlg = new RoadQualAnaDlg();
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                roadAnaCond = RoadQualAnaCfgManager.GetInstance().RoadQualAnaSetting;
                return true;
            }
            return false;
        }
        public void ClearData()
        {
            if (!isOnlyReLoadExitCellInfo)
            {
                if (roadPartAreaBaseInfoDic != null)
                {
                    roadPartAreaBaseInfoDic.Clear();
                }
                if (roadLabelBaseInfoDic != null)
                {
                    roadLabelBaseInfoDic.Clear();
                }
                if (roadEvtAnaInfoList != null)
                {
                    roadEvtAnaInfoList.Clear();
                }
                if (roadPartAnaInfoDic != null)
                {
                    roadPartAnaInfoDic.Clear();
                }
            }
            if (resultSumInfo != null)
            {
                resultSumInfo.ClearData();
            }
            RoadQualAnaLayer roadQualLayer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(RoadQualAnaLayer)) as RoadQualAnaLayer;
            if (roadQualLayer != null)
            {
                roadQualLayer.ClearData();
            }
            resultSumInfo = null;
            //GC.Collect();
        }

        public void ReLoadExitCell()
        {
            isOnlyReLoadExitCellInfo = true;

            this.query();

            isOnlyReLoadExitCellInfo = false;
        }
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }

            try
            {
                ClearData();

                WaitBox.Show("开始统计分析数据...", queryInThread, clientProxy);

                fireShowForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + ex.Source + ex.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }
        protected void fireShowForm()
        {
            RoadQuaAnaInfoForm frm = MainModel.CreateResultForm(typeof(RoadQuaAnaInfoForm)) as RoadQuaAnaInfoForm;
            frm.FillData(resultSumInfo);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
        protected void initRoadBaseInfoDic(string dbName)
        {
            //查询出主要路段信息
            MainRoadBaseInfoQuery mainRoadQuery = new MainRoadBaseInfoQuery(dbName);
            mainRoadQuery.Query();
            roadLabelBaseInfoDic = mainRoadQuery.RoadBaseInfoDic;

            //查询出小路段图层信息
            RoadPartAreaInfoQuery roadPartQuery = new RoadPartAreaInfoQuery(dbName, roadLabelBaseInfoDic);
            roadPartQuery.Query();
            roadPartAreaBaseInfoDic = roadPartQuery.RoadPartAreaBaseInfoDic;
        }
        protected void queryInThread(object o)
        {
            try
            {
                string dbName = RoadQualAnaCfgManager.GetInstance().RoadAnaBaseDBName;

                if (!isOnlyReLoadExitCellInfo)
                {
                    WaitBox.Text = "开始初始化道路信息...";
                    initRoadBaseInfoDic(dbName);

                    WaitBox.Text = "开始查询道路测试异常概率信息...";
                    RoadEvtAnaInfoQuery evtAnaQuery = new RoadEvtAnaInfoQuery(dbName);
                    evtAnaQuery.Query();
                    roadEvtAnaInfoList = evtAnaQuery.RoadEvtAnaInfoList;

                    WaitBox.CanCancel = true;
                    WaitBox.Text = "开始查询小路段指标信息...";
                    roadPartAnaInfoDic = new Dictionary<int, RoadPartAnaInfo>();
                    if (roadAnaCond.IsCheck4G)
                    {
                        RoadPartLteKpiQuery lteKpiQuery = new RoadPartLteKpiQuery(roadPartAreaBaseInfoDic, roadPartAnaInfoDic);
                        lteKpiQuery.Query();
                    }
                    if (roadAnaCond.IsCheck2G)
                    {
                        RoadPartGsmKpiQuery gsmKpiQuery = new RoadPartGsmKpiQuery(roadPartAreaBaseInfoDic,roadPartAnaInfoDic);
                        gsmKpiQuery.Query();
                    }
                }

                WaitBox.CanCancel = false;
                WaitBox.Text = "开始查询退服小区信息";
                CellExitInfoQuery exitInfoQuery = new CellExitInfoQuery(dbName, roadAnaCond.TimePeriod_ExitCell);
                exitInfoQuery.Query();

                WaitBox.Text = "开始统计道路指标信息...";
                calculateSummInfo(exitInfoQuery.CellExitInfoDic);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + ex.Source + ex.StackTrace);
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitBox.Close();
            }
        }
        protected void calculateSummInfo(Dictionary<string, CellExitInfo> cellExitInfoDic)
        {
            WaitBox.ProgressPercent = 0;

            resultSumInfo = new RoadQualAnaInfoSum(roadLabelBaseInfoDic);
            resultSumInfo.AddEvtAnaInfos(roadEvtAnaInfoList);
            foreach (RoadPartAnaInfo roadPart in roadPartAnaInfoDic.Values)
            {
                roadPart.Calculate(roadAnaCond, cellExitInfoDic);
                resultSumInfo.AddRoadPartAnaInfo(roadPart);
            }
            resultSumInfo.Calculate(roadAnaCond);
        }
    }
}
