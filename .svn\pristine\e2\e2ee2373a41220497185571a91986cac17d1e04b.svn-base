﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTGSMCellReselectAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTGSMCellReselectAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewCellReselectAna = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnGridName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBTSName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBeforeRxlev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBeforeRxqual = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBeforeC2I = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDestCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDestBTSName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDestLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDestCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAfterRxlev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAfterRxqual = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAfterC2I = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDestDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnIsAttempt = new BrightIdeasSoftware.OLVColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCellReselectAna)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 48);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(129, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // ListViewCellReselectAna
            // 
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnFileName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnGridName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnTime);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnCellName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnBTSName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnCellLAC);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnCellCI);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnCellDistance);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnBeforeRxlev);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnBeforeRxqual);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnBeforeC2I);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnDestCellName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnDestBTSName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnDestLAC);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnDestCI);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnAfterRxlev);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnAfterRxqual);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnAfterC2I);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnDestDistance);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnIsAttempt);
            this.ListViewCellReselectAna.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewCellReselectAna.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnGridName,
            this.olvColumnTime,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnCellName,
            this.olvColumnBTSName,
            this.olvColumnCellLAC,
            this.olvColumnCellCI,
            this.olvColumnCellDistance,
            this.olvColumnBeforeRxlev,
            this.olvColumnBeforeRxqual,
            this.olvColumnBeforeC2I,
            this.olvColumnDestCellName,
            this.olvColumnDestBTSName,
            this.olvColumnDestLAC,
            this.olvColumnDestCI,
            this.olvColumnAfterRxlev,
            this.olvColumnAfterRxqual,
            this.olvColumnAfterC2I,
            this.olvColumnDestDistance,
            this.olvColumnIsAttempt});
            this.ListViewCellReselectAna.ContextMenuStrip = this.ctxMenu;
            this.ListViewCellReselectAna.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCellReselectAna.FullRowSelect = true;
            this.ListViewCellReselectAna.GridLines = true;
            this.ListViewCellReselectAna.HeaderWordWrap = true;
            this.ListViewCellReselectAna.IsNeedShowOverlay = false;
            this.ListViewCellReselectAna.Location = new System.Drawing.Point(1, 1);
            this.ListViewCellReselectAna.Name = "ListViewCellReselectAna";
            this.ListViewCellReselectAna.OwnerDraw = true;
            this.ListViewCellReselectAna.ShowGroups = false;
            this.ListViewCellReselectAna.Size = new System.Drawing.Size(1150, 501);
            this.ListViewCellReselectAna.TabIndex = 7;
            this.ListViewCellReselectAna.UseCompatibleStateImageBehavior = false;
            this.ListViewCellReselectAna.View = System.Windows.Forms.View.Details;
            this.ListViewCellReselectAna.VirtualMode = true;
            this.ListViewCellReselectAna.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewCellReselectAna_MouseDoubleClick);
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 40;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 120;
            // 
            // olvColumnGridName
            // 
            this.olvColumnGridName.HeaderFont = null;
            this.olvColumnGridName.Text = "网格名称";
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "时间";
            this.olvColumnTime.Width = 80;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "源小区名称";
            // 
            // olvColumnBTSName
            // 
            this.olvColumnBTSName.HeaderFont = null;
            this.olvColumnBTSName.Text = "源基站名称";
            // 
            // olvColumnCellLAC
            // 
            this.olvColumnCellLAC.HeaderFont = null;
            this.olvColumnCellLAC.Text = "源LAC";
            // 
            // olvColumnCellCI
            // 
            this.olvColumnCellCI.HeaderFont = null;
            this.olvColumnCellCI.Text = "源CI";
            // 
            // olvColumnCellDistance
            // 
            this.olvColumnCellDistance.HeaderFont = null;
            this.olvColumnCellDistance.Text = "源小区与事件距离";
            // 
            // olvColumnBeforeRxlev
            // 
            this.olvColumnBeforeRxlev.HeaderFont = null;
            this.olvColumnBeforeRxlev.Text = "重选前场强";
            // 
            // olvColumnBeforeRxqual
            // 
            this.olvColumnBeforeRxqual.HeaderFont = null;
            this.olvColumnBeforeRxqual.Text = "重选前质量";
            // 
            // olvColumnBeforeC2I
            // 
            this.olvColumnBeforeC2I.HeaderFont = null;
            this.olvColumnBeforeC2I.Text = "重选前C/I";
            // 
            // olvColumnDestCellName
            // 
            this.olvColumnDestCellName.HeaderFont = null;
            this.olvColumnDestCellName.Text = "目的小区名称";
            // 
            // olvColumnDestBTSName
            // 
            this.olvColumnDestBTSName.HeaderFont = null;
            this.olvColumnDestBTSName.Text = "目的基站名称";
            // 
            // olvColumnDestLAC
            // 
            this.olvColumnDestLAC.HeaderFont = null;
            this.olvColumnDestLAC.Text = "目的LAC";
            // 
            // olvColumnDestCI
            // 
            this.olvColumnDestCI.HeaderFont = null;
            this.olvColumnDestCI.Text = "目的CI";
            // 
            // olvColumnAfterRxlev
            // 
            this.olvColumnAfterRxlev.HeaderFont = null;
            this.olvColumnAfterRxlev.Text = "重选后场强";
            // 
            // olvColumnAfterRxqual
            // 
            this.olvColumnAfterRxqual.HeaderFont = null;
            this.olvColumnAfterRxqual.Text = "重选后质量";
            // 
            // olvColumnAfterC2I
            // 
            this.olvColumnAfterC2I.HeaderFont = null;
            this.olvColumnAfterC2I.Text = "重选后C/I";
            // 
            // olvColumnDestDistance
            // 
            this.olvColumnDestDistance.HeaderFont = null;
            this.olvColumnDestDistance.Text = "目的小区与事件距离";
            // 
            // olvColumnIsAttempt
            // 
            this.olvColumnIsAttempt.HeaderFont = null;
            this.olvColumnIsAttempt.Text = "是否有起呼";
            // 
            // ZTGSMCellReselectAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1152, 502);
            this.Controls.Add(this.ListViewCellReselectAna);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTGSMCellReselectAnaListForm";
            this.Text = "重选前后指标分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCellReselectAna)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private BrightIdeasSoftware.TreeListView ListViewCellReselectAna;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSName;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeRxqual;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnDestCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnDestBTSName;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCI;
        private BrightIdeasSoftware.OLVColumn olvColumnDestLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnDestCI;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterRxqual;
        private BrightIdeasSoftware.OLVColumn olvColumnCellDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnDestDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnIsAttempt;
        private BrightIdeasSoftware.OLVColumn olvColumnGridName;

    }
}