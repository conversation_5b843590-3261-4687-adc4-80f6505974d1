﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Serialization;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// TD性能指标门限
    /// </summary>
    [XmlRoot("TD性能指标门限")]
    public class TDPerformanceThreadholdInf
    {       
        [XmlArray("items"), XmlArrayItem("item")]
        private IndicatorThresholdInf[] thresholdInf;

        public IndicatorThresholdInf[] ThresholdInf
        {
            get { return thresholdInf; }
            set { thresholdInf = value; }
        }
        public TDPerformanceThreadholdInf()
        {
            thresholdInf = new IndicatorThresholdInf[10];
            thresholdInf[0] = new IndicatorThresholdInf("CS域RAB拥塞率");
            thresholdInf[1] = new IndicatorThresholdInf("CS域无线接通率");
            thresholdInf[2] = new IndicatorThresholdInf("CS域误块率");

            thresholdInf[3] = new IndicatorThresholdInf("语音业务无线掉话率");
            thresholdInf[4] = new IndicatorThresholdInf("PS域RAB拥塞率");
            thresholdInf[5] = new IndicatorThresholdInf("PS域RAB建立成功率");

            thresholdInf[6] = new IndicatorThresholdInf("PS域误块率");
            thresholdInf[7] = new IndicatorThresholdInf("PS域无线掉线率");
            thresholdInf[8] = new IndicatorThresholdInf("接力切换成功率");

            thresholdInf[9] = new IndicatorThresholdInf("码资源利用率");
        }
    }
}
