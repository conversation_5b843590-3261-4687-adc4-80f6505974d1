﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.NewBlackBlock
{
    public class QueryFusionAlarmData : DIYSQLBase
    {
        protected override string getSqlTextString()
        {
            MainDB = true;
            return string.Format(@"SELECT [lac],[ci],[stime],[etime],[name],[typename],[extend] FROM {0}.[dbo].[tb_alarm_cell_mm_{1}]
 where ({2}) and cityid={3} and ((datediff(day,stime,'{4}')>= 0 and datediff(day,etime,'{4}')<= 0) 
or (datediff(day,stime,'{4}')= 0 and etime is null))"
                , CellAlarmData.FusionDB
                , this.Time.ToString("yyMM")
                , CellCondition
                , this.DistrictID
                , this.Time.ToString("yyyyMMdd"));
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[7];
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_VARYBIN;
            return arr;
        }

        public Event Event
        {
            get;
            set;
        }

        private DateTime time;
        public DateTime Time
        { 
            get
            {
                if (Event != null)
                {
                    return Event.DateTime;
                }
                return time;
            }
            set
            {
                time = value;
            }
        }

        private int districtID;
        public int DistrictID
        {
            get
            {
                if (Event != null)
                {
                    return Event.DistrictID;
                }
                return districtID;
            }
            set
            {
                districtID = value;
            }
        }

        public string CellCondition
        {
            get;
            set;
        }

        public Dictionary<string, LTECell> CellDic
        {
            get;
            set;
        }

        public List<CellAlarmData> CellAlarmSet
        {
            get;
            set;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            if (CellAlarmSet==null)
            {
                CellAlarmSet = new List<CellAlarmData>();
            }
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellAlarmData cellAlarm = new CellAlarmData();
                    int tac = package.Content.GetParamInt();
                    int eci = package.Content.GetParamInt();
                    cellAlarm.Cell = CellDic[string.Format("{0}_{1}", tac, eci)];
                    cellAlarm.BeginTime = DateTime.Parse(package.Content.GetParamString());
                    string endTimeStr = package.Content.GetParamString();
                    DateTime endTime;
                    if (DateTime.TryParse(endTimeStr, out endTime))//结束时间有可能为空
                    {
                        cellAlarm.EndTime = endTime;
                    }
                    cellAlarm.Desc = package.Content.GetParamString();
                    cellAlarm.TypeName = package.Content.GetParamString();
                    cellAlarm.FillImg(package.Content.GetParamBytes());
                    cellAlarm.DistrictId = this.DistrictID;
                    CellAlarmSet.Add(cellAlarm);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }
}
