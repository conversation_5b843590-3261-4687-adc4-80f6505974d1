﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LowSpeedCellForm : MinCloseForm
    {
        public LowSpeedCellForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        DataType dataType = DataType.GSM;
        List<LowSpeedCell> lowSpeedCellList;
        List<LowSpeedCell> lowSpeedCellList_WeakCover;
        List<LowSpeedCell> lowSpeedCellList_MultiCover;
        public void FillData(List<LowSpeedCell> lowSpeedCellList, List<LowSpeedCell> lowSpeedCellList_WeakCover,
            List<LowSpeedCell> lowSpeedCellList_MultiCover, DataType dataType)
        {
            this.lowSpeedCellList = lowSpeedCellList;
            this.lowSpeedCellList_WeakCover = lowSpeedCellList_WeakCover;
            this.lowSpeedCellList_MultiCover = lowSpeedCellList_MultiCover;
            this.dataType = dataType;
            initGridColumns();
            BindingSource source = new BindingSource();
            if (dataType == DataType.TD)
            {
                List<LowSpeedCell_TD> resultList = new List<LowSpeedCell_TD>();
                foreach (LowSpeedCell item in lowSpeedCellList)
                {
                    resultList.Add((LowSpeedCell_TD)item);
                }
                source.DataSource = resultList;
            }
            else
            {
                source.DataSource = lowSpeedCellList;
            }
            gridControl.DataSource = source;
            gridControl.RefreshDataSource();
        }

        private void initGridColumns()
        {
            gridView.Columns.Clear();
            List<GridColumn> gridColumnList = new List<GridColumn>();
            GridColumn gridColumn = new GridColumn();
            gridColumn.Caption = "小区名";
            gridColumn.FieldName = "CellName";
            gridColumn.Name = "gridColumnCellName";
            gridColumn.Width = 102;
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "LAC";
            gridColumn.FieldName = "LAC";
            gridColumn.Name = "gridColumnLAC";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "CI";
            gridColumn.FieldName = "CI";
            gridColumn.Name = "gridColumnCI";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "平均下载速率(Kb/s)";
            gridColumn.FieldName = "SpeedAvgString";
            gridColumn.Name = "gridColumnSpeedAvg";
            gridColumn.Width = 140;
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "平均电平";
            gridColumn.FieldName = "RxLevAvgString";
            gridColumn.Name = "gridColumnRxLevAvg";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "低速率采样点数";
            gridColumn.FieldName = "SpeedSample";
            gridColumn.Name = "gridColumnSpeedSample";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "小区总采样点数";
            gridColumn.FieldName = "AllSampleCount";
            gridColumn.Name = "gridColumnAllSampleCount";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "低速率采样占比(%)";
            gridColumn.FieldName = "SamplePercent";
            gridColumn.Name = "gridColumnSamplePercent";
            gridColumnList.Add(gridColumn);

            if (dataType == DataType.TD)
            {
                gridColumn = new GridColumn();
                gridColumn.Caption = "500K采样点数";
                gridColumn.FieldName = "Speed500KSample";
                gridColumn.Name = "gridColumnSpeed500KSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "500K占比(%)";
                gridColumn.FieldName = "Speed500KRateString";
                gridColumn.Name = "gridColumnSpeed500KRateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "0K采样点数";
                gridColumn.FieldName = "Speed0KSample";
                gridColumn.Name = "gridColumnSpeed0KSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "0K占比(%)";
                gridColumn.FieldName = "Speed0KRateString";
                gridColumn.Name = "gridColumnSpeed0KRateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "PCCPCH_RSCP采样点数";
                gridColumn.FieldName = "PccpchRscpSample";
                gridColumn.Name = "gridColumnPccpchRscpSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "PCCPCH_RSCP>=85采样点数";
                gridColumn.FieldName = "PccpchRscpF85Sample";
                gridColumn.Name = "gridColumnPccpchRscpF85Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "PCCPCH_RSCP>=85占比(%)";
                gridColumn.FieldName = "PccpchRscpF85RateString";
                gridColumn.Name = "gridColumnPccpchRscpF85RateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "PCCPCH_RSCP均值";
                gridColumn.FieldName = "RxLevAvgString";
                gridColumn.Name = "gridColumnRxLevAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "PCCPCH_C/I采样点数";
                gridColumn.FieldName = "PccpchC2iSample";
                gridColumn.Name = "gridColumnPccpchC2iSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "PCCPCH_C/I>=-3采样点数";
                gridColumn.FieldName = "PccpchC2iF3Sample";
                gridColumn.Name = "gridColumnPccpchC2iF3Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "PCCPCH_C/I>=-3占比(%)";
                gridColumn.FieldName = "PccpchCIF3RateString";
                gridColumn.Name = "gridColumnPccpchCIF3RateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "PCCPCH_C/I均值";
                gridColumn.FieldName = "PCCPCH_C2IAvgString";
                gridColumn.Name = "gridColumnPCCPCH_C2IAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "DPCH_RSCP采样点数";
                gridColumn.FieldName = "DpchRscpSample";
                gridColumn.Name = "gridColumnDpchRscpSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "DPCH_RSCP>=85采样点数";
                gridColumn.FieldName = "DpchRscpF85Sample";
                gridColumn.Name = "gridColumnDpchRscpF85Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "DPCH_RSCP>=85占比(%)";
                gridColumn.FieldName = "DpchRscpF85RateString";
                gridColumn.Name = "gridColumnDpchRscpF85RateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "DPCH_RSCP均值";
                gridColumn.FieldName = "DpchRscpAvgString";
                gridColumn.Name = "gridColumnDpchRscpAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "DPCH_C/I采样点数";
                gridColumn.FieldName = "DpchC2iSample";
                gridColumn.Name = "gridColumnDpchC2iSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "DPCH_C/I>=-3采样点数";
                gridColumn.FieldName = "DpchC2iF3Sample";
                gridColumn.Name = "gridColumnDpchC2iF3Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "DPCH_C/I>=-3占比(%)";
                gridColumn.FieldName = "DpchCIF3RateString";
                gridColumn.Name = "gridColumnDpchCIF3RateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "DPCH_C/I均值";
                gridColumn.FieldName = "DPCH_C2IAvgString";
                gridColumn.Name = "gridColumnDPCH_C2IAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSSCCH_RSCP均值";
                gridColumn.FieldName = "HSScchRSCPAvgString";
                gridColumn.Name = "gridColumnHSScchRSCPAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSSCCH_ISCP均值";
                gridColumn.FieldName = "HSScchISCPAvgString";
                gridColumn.Name = "gridColumnHSScchISCPAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSSCCH_C/I采样点数";
                gridColumn.FieldName = "HSScchC2iSample";
                gridColumn.Name = "gridColumnHSScchC2iSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSSCCH_C/I>=-3采样点数";
                gridColumn.FieldName = "HSScchC2iF3Sample";
                gridColumn.Name = "gridColumnHSScchC2iF3Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSSCCH_C/I>=-3占比(%)";
                gridColumn.FieldName = "HSScchCIF3RateString";
                gridColumn.Name = "gridColumnHSScchCIF3RateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSSCCH_C/I>=5采样点数";
                gridColumn.FieldName = "HSScchC2i5Sample";
                gridColumn.Name = "gridColumnHSScchC2i5Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSSCCH_C/I>=5占比(%)";
                gridColumn.FieldName = "HSScchCI5RateString";
                gridColumn.Name = "gridColumnHSScchCI5RateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSSCCH_C/I均值";
                gridColumn.FieldName = "HSScchCIAvgString";
                gridColumn.Name = "gridColumnHSScchCIAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPDSCH_RSCP均值";
                gridColumn.FieldName = "HSPdschRSCPAvgString";
                gridColumn.Name = "gridColumnHSPdschRSCPAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPDSCH_C/I采样点数";
                gridColumn.FieldName = "HSPdschC2iSample";
                gridColumn.Name = "gridColumnHSPdschC2iSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPDSCH_C/I>=-3采样点数";
                gridColumn.FieldName = "HSPdschC2iF3Sample";
                gridColumn.Name = "gridColumnHSPdschC2iF3Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPDSCH_C/I>=-3占比(%)";
                gridColumn.FieldName = "HSPdschCIF3RateString";
                gridColumn.Name = "gridColumnHSPdschCIF3RateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPDSCH_C/I>=5采样点数";
                gridColumn.FieldName = "HSPdschC2i5Sample";
                gridColumn.Name = "gridColumnHSPdschC2i5Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPDSCH_C/I>=5占比(%)";
                gridColumn.FieldName = "HSPdschCI5RateString";
                gridColumn.Name = "gridColumnHSPdschCI5RateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPDSCH_C/I均值";
                gridColumn.FieldName = "HSPdschCIAvgString";
                gridColumn.Name = "gridColumnHSPdschCIAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "Bler采样点数";
                gridColumn.FieldName = "BlerSample";
                gridColumn.Name = "gridColumnBlerSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "Bler>=5采样点数";
                gridColumn.FieldName = "Bler5Sample";
                gridColumn.Name = "gridColumnBler5Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "Bler>=5占比(%)";
                gridColumn.FieldName = "Bler5RateString";
                gridColumn.Name = "gridColumnBler5RateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "Bler均值";
                gridColumn.FieldName = "BlerAvgString";
                gridColumn.Name = "gridColumnBlerAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPDCSH_BLER采样点数";
                gridColumn.FieldName = "HSPdcshBlerSample";
                gridColumn.Name = "gridColumnHSPdcshBlerSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPDCSH_BLER>=5采样点数";
                gridColumn.FieldName = "HSPdcshBler5Sample";
                gridColumn.Name = "gridColumnHSPdcshBler5Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPDCSH_BLER>=5占比(%)";
                gridColumn.FieldName = "HSPdschBler5RateString";
                gridColumn.Name = "gridColumnHSPdschBler5RateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPDCSH_BLER均值";
                gridColumn.FieldName = "HSPdschBlerAvgString";
                gridColumn.Name = "gridColumnHSPdschBlerAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "TxPower采样点数";
                gridColumn.FieldName = "TxPowerSample";
                gridColumn.Name = "gridColumnTxPowerSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "TxPower>=24采样点数";
                gridColumn.FieldName = "TxPower24Sample";
                gridColumn.Name = "gridColumnTxPower24Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "TxPower>=24占比(%)";
                gridColumn.FieldName = "TxPower24RateString";
                gridColumn.Name = "gridColumnTxPower24RateString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "TxPower均值";
                gridColumn.FieldName = "TxPowerAvgString";
                gridColumn.Name = "gridColumnTxPowerAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSCQI最大值的均值";
                gridColumn.FieldName = "HSCqiMaxAvgString";
                gridColumn.Name = "gridColumnHSCqiMaxAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSCQI最小值的均值";
                gridColumn.FieldName = "HSCqiMinAvgString";
                gridColumn.Name = "gridColumnHSCqiMinAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSCQI平均值的均值";
                gridColumn.FieldName = "HSCqiAvgAvgString";
                gridColumn.Name = "gridColumnHSCqiAvgAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "16QAM比例平均值";
                gridColumn.FieldName = "HS16QAMRateAvgString";
                gridColumn.Name = "gridColumnHS16QAMRateAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "QPSK比例平均值";
                gridColumn.FieldName = "HSQPSKRateAvgString";
                gridColumn.Name = "gridColumnHSQPSKRateAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HS占用时隙平均值";
                gridColumn.FieldName = "TimeSlotUsedAvgString";
                gridColumn.Name = "gridColumnTimeSlotUsedAvgString";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "HSPA采样点数";
                gridColumn.FieldName = "HSPASample";
                gridColumn.Name = "gridColumnHSPASample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "R4采样点数";
                gridColumn.FieldName = "R4Sample";
                gridColumn.Name = "gridColumnR4Sample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "GSM采样点数";
                gridColumn.FieldName = "GSMSample";
                gridColumn.Name = "gridColumnGSMSample";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "导频污染采样点数";
                gridColumn.FieldName = "PollutSample";
                gridColumn.Name = "gridColumnPollutSample";
                gridColumnList.Add(gridColumn);
            }

            for (int i = 0; i < gridColumnList.Count; i++)
            {
                gridColumnList[i].VisibleIndex = i;
            }
            gridView.Columns.AddRange(gridColumnList.ToArray());
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            exportToExcel(lowSpeedCellList);
        }

        private void exportToExcel(List<LowSpeedCell> cellList)
        {
            List<NPOIRow> rowList = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("小区名");
            row.AddCellValue("LAC");
            row.AddCellValue("CI");
            row.AddCellValue("平均下载速率(Kbps)");
            row.AddCellValue("平均电平");
            row.AddCellValue("低速率采样点数");
            row.AddCellValue("小区总采样点数");
            row.AddCellValue("低速率采样占比(%)");
            if (dataType == DataType.TD)
            {
                row.AddCellValue("500K采样点数");
                row.AddCellValue("500K占比(%)");
                row.AddCellValue("0K采样点数");
                row.AddCellValue("0K占比(%)");
                row.AddCellValue("PCCPCH_RSCP采样点数");
                row.AddCellValue("PCCPCH_RSCP>=85采样点数");
                row.AddCellValue("PCCPCH_RSCP>=85占比(%)");
                row.AddCellValue("PCCPCH_RSCP均值");
                row.AddCellValue("PCCPCH_C/I采样点数");
                row.AddCellValue("PCCPCH_C/I>=-3采样点数");
                row.AddCellValue("PCCPCH_C/I>=-3占比(%)");
                row.AddCellValue("PCCPCH_C/I均值");
                row.AddCellValue("DPCH_RSCP采样点数");
                row.AddCellValue("DPCH_RSCP>=85采样点数");
                row.AddCellValue("DPCH_RSCP>=85占比(%)");
                row.AddCellValue("DPCH_RSCP均值");
                row.AddCellValue("DPCH_C/I采样点数");
                row.AddCellValue("DPCH_C/I>=-3采样点数");
                row.AddCellValue("DPCH_C/I>=-3占比(%)");
                row.AddCellValue("DPCH_C/I均值");
                row.AddCellValue("HSSCCH_RSCP均值");
                row.AddCellValue("HSSCCH_ISCP均值");
                row.AddCellValue("HSSCCH_C/I采样点数");
                row.AddCellValue("HSSCCH_C/I>=-3采样点数");
                row.AddCellValue("HSSCCH_C/I>=-3占比(%)");
                row.AddCellValue("HSSCCH_C/I>=5采样点数");
                row.AddCellValue("HSSCCH_C/I>=5占比(%)");
                row.AddCellValue("HSSCCH_C/I均值");
                row.AddCellValue("HSPDSCH_RSCP均值");
                row.AddCellValue("HSPDSCH_C/I采样点数");
                row.AddCellValue("HSPDSCH_C/I>=-3采样点数");
                row.AddCellValue("HSPDSCH_C/I>=-3占比(%)");
                row.AddCellValue("HSPDSCH_C/I>=5采样点数");
                row.AddCellValue("HSPDSCH_C/I>=5占比(%)");
                row.AddCellValue("HSPDSCH_C/I均值");
                row.AddCellValue("Bler采样点数");
                row.AddCellValue("Bler>=5采样点数");
                row.AddCellValue("Bler>=5占比(%)");
                row.AddCellValue("Bler均值");
                row.AddCellValue("HSPDCSH_BLER采样点数");
                row.AddCellValue("HSPDCSH_BLER>=5采样点数");
                row.AddCellValue("HSPDCSH_BLER>=5占比(%)");
                row.AddCellValue("HSPDCSH_BLER均值");
                row.AddCellValue("TxPower采样点数");
                row.AddCellValue("TxPower>=24采样点数");
                row.AddCellValue("TxPower>=24占比(%)");
                row.AddCellValue("TxPower均值");
                row.AddCellValue("HSCQI最大值的均值");
                row.AddCellValue("HSCQI最小值的均值");
                row.AddCellValue("HSCQI平均值的均值");
                row.AddCellValue("16QAM比例平均值");
                row.AddCellValue("QPSK比例平均值");
                row.AddCellValue("HS占用时隙平均值");
                row.AddCellValue("HSPA采样点数");
                row.AddCellValue("R4采样点数");
                row.AddCellValue("GSM采样点数");
                row.AddCellValue("导频污染采样点数");
            }
            rowList.Add(row);
            foreach (LowSpeedCell item in cellList)
            {
                row = new NPOIRow();
                row.AddCellValue(item.CellName);
                row.AddCellValue(item.LAC);
                row.AddCellValue(item.CI);
                row.AddCellValue(item.SpeedAvgString);
                row.AddCellValue(item.RxLevAvgString);
                row.AddCellValue(item.SpeedSample);
                row.AddCellValue(item.AllSampleCount);
                row.AddCellValue(item.SamplePercent);
                if (dataType == DataType.TD)
                {
                    LowSpeedCell_TD item_TD = (LowSpeedCell_TD)item;
                    row.AddCellValue(item_TD.Speed500KSample);
                    row.AddCellValue(item_TD.Speed500KRateString);
                    row.AddCellValue(item_TD.Speed0KSample);
                    row.AddCellValue(item_TD.Speed0KRateString);
                    row.AddCellValue(item_TD.PccpchRscpSample);
                    row.AddCellValue(item_TD.PccpchRscpF85Sample);
                    row.AddCellValue(item_TD.PccpchRscpF85RateString);
                    row.AddCellValue(item_TD.RxLevAvgString);
                    row.AddCellValue(item_TD.PccpchC2iSample);
                    row.AddCellValue(item_TD.PccpchC2iF3Sample);
                    row.AddCellValue(item_TD.PccpchCIF3RateString);
                    row.AddCellValue(item_TD.PCCPCH_C2IAvgString);
                    row.AddCellValue(item_TD.DpchRscpSample);
                    row.AddCellValue(item_TD.DpchRscpF85Sample);
                    row.AddCellValue(item_TD.DpchRscpF85RateString);
                    row.AddCellValue(item_TD.DpchRscpAvgString);
                    row.AddCellValue(item_TD.DpchC2iSample);
                    row.AddCellValue(item_TD.DpchC2iF3Sample);
                    row.AddCellValue(item_TD.DpchCIF3RateString);
                    row.AddCellValue(item_TD.DPCH_C2IAvgString);
                    row.AddCellValue(item_TD.HSScchRSCPAvgString);
                    row.AddCellValue(item_TD.HSScchISCPAvgString);
                    row.AddCellValue(item_TD.HSScchC2iSample);
                    row.AddCellValue(item_TD.HSScchC2iF3Sample);
                    row.AddCellValue(item_TD.HSScchCIF3RateString);
                    row.AddCellValue(item_TD.HSScchC2i5Sample);
                    row.AddCellValue(item_TD.HSScchCI5RateString);
                    row.AddCellValue(item_TD.HSScchCIAvgString);
                    row.AddCellValue(item_TD.HSPdschRSCPAvgString);
                    row.AddCellValue(item_TD.HSPdschC2iSample);
                    row.AddCellValue(item_TD.HSPdschC2iF3Sample);
                    row.AddCellValue(item_TD.HSPdschCIF3RateString);
                    row.AddCellValue(item_TD.HSPdschC2i5Sample);
                    row.AddCellValue(item_TD.HSPdschCI5RateString);
                    row.AddCellValue(item_TD.HSPdschCIAvgString);
                    row.AddCellValue(item_TD.BlerSample);
                    row.AddCellValue(item_TD.Bler5Sample);
                    row.AddCellValue(item_TD.Bler5RateString);
                    row.AddCellValue(item_TD.BlerAvgString);
                    row.AddCellValue(item_TD.HSPdcshBlerSample);
                    row.AddCellValue(item_TD.HSPdcshBler5Sample);
                    row.AddCellValue(item_TD.HSPdschBler5RateString);
                    row.AddCellValue(item_TD.HSPdschBlerAvgString);
                    row.AddCellValue(item_TD.TxPowerSample);
                    row.AddCellValue(item_TD.TxPower24Sample);
                    row.AddCellValue(item_TD.TxPower24RateString);
                    row.AddCellValue(item_TD.TxPowerAvgString);
                    row.AddCellValue(item_TD.HSCqiMaxAvgString);
                    row.AddCellValue(item_TD.HSCqiMinAvgString);
                    row.AddCellValue(item_TD.HSCqiAvgAvgString);
                    row.AddCellValue(item_TD.HS16QAMRateAvgString);
                    row.AddCellValue(item_TD.HSQPSKRateAvgString);
                    row.AddCellValue(item_TD.TimeSlotUsedAvgString);
                    row.AddCellValue(item_TD.HSPASample);
                    row.AddCellValue(item_TD.R4Sample);
                    row.AddCellValue(item_TD.GSMSample);
                    row.AddCellValue(item_TD.PollutSample);
                }
                rowList.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rowList);
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                LowSpeedCell item = gridView.GetRow(gridView.GetSelectedRows()[0]) as LowSpeedCell;
                if (item.TestPointList.Count == 0)
                {
                    return;
                }

                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in item.TestPointList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
            }
        }

        //private void GoToView(List<TestPoint> tpList)
        //{
        //    double ltLong = 100000;
        //    double ltLat = -100000;
        //    double brLong = -100000;
        //    double brLat = 100000;

        //    foreach (TestPoint tp in tpList)
        //    {
        //        if (tp.Longitude < ltLong)
        //        {
        //            ltLong = tp.Longitude;
        //        }
        //        if (tp.Longitude > brLong)
        //        {
        //            brLong = tp.Longitude;
        //        }
        //        if (tp.Latitude < brLat)
        //        {
        //            brLat = tp.Latitude;
        //        }
        //        if (tp.Latitude > ltLat)
        //        {
        //            ltLat = tp.Latitude;
        //        }
        //    }
        //    if (MainModel.MainForm.GetMapForm() != null)
        //    {
        //        MainModel.MainForm.GetMapForm().GoToView(new DbRect(ltLong, ltLat, brLong, brLat));
        //    }
        //}

        private void miExportToExcelWeakGrid_Click(object sender, EventArgs e)
        {
            exportToExcel(lowSpeedCellList_WeakCover);
        }

        private void miExportToExcelMultiCoverGrid_Click(object sender, EventArgs e)
        {
            exportToExcel(lowSpeedCellList_MultiCover);
        }

        private void contextMenuStrip_Opening(object sender, CancelEventArgs e)
        {
            miExportToExcelWeakGrid.Enabled = lowSpeedCellList_WeakCover.Count > 0;
            miExportToExcelMultiCoverGrid.Enabled = lowSpeedCellList_MultiCover.Count > 0;
        }
    }
}
