﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class DiyQueryFddDataBase : DIYSQLBase
    {
        protected int btsID = 0;
        /// <summary>
        /// 由于存在共站,所以站存在逻辑站名和实际站名
        /// 实际站名:工参中的站名(多个站共站只有一个实际站名,比如工参中站名为NB后缀,FDD的共站也为NB后缀的站名)
        /// 逻辑站名:逻辑上对应的站名(比如FDD站有FDD后缀,NB站有NB后缀)
        /// 这里的btsName为逻辑站名
        /// </summary>
        protected string btsName = "";
        protected LTEBTSType btstype = LTEBTSType.Outdoor;
        protected DiyQueryFddDataBase()
            : base(MainModel.GetInstance())
        {
            MainDB = true;
        }

        public override string Name
        {
            get
            {
                return "";
            }
        }

        public virtual void SetCondition(int btsID, string btsName, LTEBTSType btstype)
        {
            this.btsID = btsID;
            this.btsName = btsName;
            this.btstype = btstype;
        }

        protected override string getSqlTextString()
        {
            string selectSQL = "";
            return selectSQL;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[0];
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            initData();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    dealReceiveData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        protected virtual void initData()
        {
        }

        protected virtual void dealReceiveData(Package package)
        {
        }
    }

    public class BtsCellData
    {
        #region 基站信息
        public string BtsName { get; protected set; }
        public int TAC { get; protected set; }
        public int ENodeBID { get; protected set; }

        protected double longitude;
        public double Longitude
        {
            get { return longitude / 10000000d; }
        }

        protected double latitude;
        public double Latitude
        {
            get { return latitude / 10000000d; }
        }
        #endregion
      
        public virtual void FillData(Package package)
        {
        }
    }

}
