﻿using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable()]
    public class NRScanRoadMultiCoverageLayer : ScanRoadMultiCoverageLayerBase
    {
        public NRScanRoadMultiCoverageLayer()
            : base("NR道路重叠覆盖度图层")
        {
            ColorRanges = NRScanRoadMultiCoverageColorRanges.Instance.ColorRanges;
        }

        //public static NRScanRoadMultiCoverageColorRanges NRMultiCoverageTpColorRange { get; set; } = new NRScanRoadMultiCoverageColorRanges();

        protected override RoadMultiCoverageInfoBase getValidData(RoadMultiCoverageInfoBase point)
        {
            RoadMultiCoverageInfoBase data = null;
            if (point is NRScanRoadMultiCoverageInfo)
            {
                data = point as NRScanRoadMultiCoverageInfo;
            }
            return data;
        }

        protected override GridRoadMultiCoverageInfoBase getValidGrid(GridRoadMultiCoverageInfoBase point)
        {
            GridRoadMultiCoverageInfoBase grid = null;
            if (point is NRScanGridRoadMultiCoverageInfo)
            {
                grid = point as NRScanGridRoadMultiCoverageInfo;
            }
            return grid;
        }
    }
}
