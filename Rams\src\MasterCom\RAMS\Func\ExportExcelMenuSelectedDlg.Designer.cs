﻿namespace MasterCom.RAMS.Func
{
    partial class ExportExcelMenuSelectedDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.cbxAllNone = new System.Windows.Forms.CheckBox();
            this.chbMenus = new System.Windows.Forms.CheckedListBox();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // cbxAllNone
            // 
            this.cbxAllNone.AutoSize = true;
            this.cbxAllNone.Location = new System.Drawing.Point(27, 12);
            this.cbxAllNone.Name = "cbxAllNone";
            this.cbxAllNone.Size = new System.Drawing.Size(90, 16);
            this.cbxAllNone.TabIndex = 11;
            this.cbxAllNone.Text = "全选/全不选";
            this.cbxAllNone.UseVisualStyleBackColor = true;
            this.cbxAllNone.CheckedChanged += new System.EventHandler(this.cbxAllNone_Click);
            // 
            // chbMenus
            // 
            this.chbMenus.CheckOnClick = true;
            this.chbMenus.FormattingEnabled = true;
            this.chbMenus.Location = new System.Drawing.Point(27, 42);
            this.chbMenus.Name = "chbMenus";
            this.chbMenus.Size = new System.Drawing.Size(310, 164);
            this.chbMenus.TabIndex = 10;
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(143, 227);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 9;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(27, 227);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 8;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // ExportExcelMenuSelectedDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(360, 258);
            this.Controls.Add(this.cbxAllNone);
            this.Controls.Add(this.chbMenus);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ExportExcelMenuSelectedDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "批量导出菜单项选择";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.CheckBox cbxAllNone;
        private System.Windows.Forms.CheckedListBox chbMenus;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
    }
}