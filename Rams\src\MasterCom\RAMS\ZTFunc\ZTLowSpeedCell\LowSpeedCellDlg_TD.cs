﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LowSpeedCellDlg_TD : BaseDialog
    {
        public LowSpeedCellDlg_TD()
        {
            InitializeComponent();
        }

        public LowSpeedCellCond_TD GetCondition()
        {
            LowSpeedCellCond_TD cond = new LowSpeedCellCond_TD();
            cond.MinRxLev = (int)numMinRxLev.Value;
            cond.MaxRxLev = (int)numMaxRxLev.Value;
            cond.MinSpeed = (int)numMinSpeed.Value;
            cond.MaxSpeed = (int)numMaxSpeed.Value;
            cond.MinPCCPCH_C2I = (int)numMinPCCPCH_C2I.Value;
            cond.MaxPCCPCH_C2I = (int)numMaxPCCPCH_C2I.Value;
            cond.MinDPCH_C2I = (int)numMinDPCH_C2I.Value;
            cond.MaxDPCH_C2I = (int)numMaxDPCH_C2I.Value;
            cond.SaveTestPoint = chkSaveTestPoint.Checked;
            cond.WeakCoverMap = edtWeakCover.Text.Trim();
            cond.MultiCoverMap = edtMultiCover.Text.Trim();
            return cond;
        }

        private void btnWeakCover_Click(object sender, EventArgs e)
        {
            OpenFileDialog dialog = new OpenFileDialog();
            dialog.RestoreDirectory = false;
            dialog.Filter = "SHP (*.SHP) | *.SHP";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                edtWeakCover.Text = dialog.FileName;
            }
        }

        private void btnMultiCover_Click(object sender, EventArgs e)
        {
            OpenFileDialog dialog = new OpenFileDialog();
            dialog.RestoreDirectory = false;
            dialog.Filter = "SHP (*.SHP) | *.SHP";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                edtMultiCover.Text = dialog.FileName;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!edtWeakCover.Text.Trim().Equals("") && !System.IO.File.Exists(edtWeakCover.Text.Trim()))
            {
                XtraMessageBox.Show("弱覆盖栅格图层不存在，如需过滤弱覆盖栅格，请重新设置，否则设置为空。");
                DialogResult = DialogResult.Retry;
            }
            else if (!edtMultiCover.Text.Trim().Equals("") && !System.IO.File.Exists(edtMultiCover.Text.Trim()))
            {
                XtraMessageBox.Show("重叠覆盖栅格图层不存在，如需过滤重叠覆盖栅格，请重新设置，否则设置为空。");
                DialogResult = DialogResult.Retry;
            }
            else
            {
                DialogResult = DialogResult.OK;
            }
        }
    }

    public class LowSpeedCellCond_TD
    {
        public int MinRxLev { get; set; }
        public int MaxRxLev { get; set; }
        public int MinSpeed { get; set; }
        public int MaxSpeed { get; set; }
        public int MinPCCPCH_C2I { get; set; }
        public int MaxPCCPCH_C2I { get; set; }
        public int MinDPCH_C2I { get; set; }
        public int MaxDPCH_C2I { get; set; }
        public bool SaveTestPoint { get; set; }
        public string WeakCoverMap { get; set; }
        public string MultiCoverMap { get; set; }
    }
}
