﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTQueryHandoverFailInfo_LTE : DIYEventByRegion
   {
        protected static readonly object lockObj = new object();
        private static ZTQueryHandoverFailInfo_LTE instance = null;
        public static ZTQueryHandoverFailInfo_LTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTQueryHandoverFailInfo_LTE(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        protected ZTQueryHandoverFailInfo_LTE(MainModel mainModel)
            : base(mainModel)
        {
            this.IsCanExportResultMapToWord = true;
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        public override string Name
        {
            get { return "切换失败分析"; }
        }
        public override string IconName
        {
            get { return "Images/event/handover.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22017, this.Name);
        }
        protected override bool prepareAskWhatEvent()
        {
            List<int> selectedEventIDs = new List<int>();
            selectedEventIDs.Add(870);//IntraFreqHO Fail
            selectedEventIDs.Add(1100);//Inter Handover Failure
            Condition.EventIDs = selectedEventIDs;
            return true;
        }

        protected override void fireShowFormAfterQuery()
        {
            HandoverFailInfoForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(HandoverFailInfoForm).FullName) as HandoverFailInfoForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new HandoverFailInfoForm(MainModel);
            }
            frm.Stat();
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.切换; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CommonNoCondProperties(this);
            }
        }

        protected override void saveBackgroundData()
        {
            //BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), null, null);
        }
        protected override void initBackgroundImageDesc()
        {
            MainModel.DTDataManager.Sort();
            int sn = 1;
            BackgroundResultList.Clear();
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (Event evt in file.Events)
                {
                    evt.Selected = true;
                    HandoverFailInfo info = new HandoverFailInfo(sn++, evt);
                    BackgroundResult bgResult = info.ConvertToBackgroundResult();
                    bgResult.SubFuncID = GetSubFuncID();
                    bgResult.SubFuncName = this.Name;
                    BackgroundResultList.Add(bgResult);
                }
            }
        }
        #endregion
    }

    public class ZTQueryHandoverFailInfo_LTE_FDD : ZTQueryHandoverFailInfo_LTE
    {
        public ZTQueryHandoverFailInfo_LTE_FDD(MainModel mainModel)
            : base(mainModel)
        {
            carrierID = CarrierType.ChinaUnicom;
        }

        public override string Name
        {
            get { return "切换切换失败分析LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26036, this.Name);
        }

        protected override bool prepareAskWhatEvent()
        {
            List<int> selectedEventIDs = new List<int>();
            selectedEventIDs.Add(3157);//IntraFreqHO Fail
            selectedEventIDs.Add(3160);//Inter Handover Failure
            Condition.EventIDs = selectedEventIDs;
            return true;
        }
    }
}
