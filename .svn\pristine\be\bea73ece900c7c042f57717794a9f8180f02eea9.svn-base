﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class WeakRoadAutoQuery : ZTWeakCoverRoadQueryModel
    {
        readonly WeakRoadReport curRpt;
        public WeakRoadAutoQuery(WeakRoadReport rpt)
        {
            curRpt = rpt;
            tpStr = "lte_NCell_RSRP";
            tpRSRP = "lte_RSRP";
            tpSINR = "lte_SINR";
            tpTac = "lte_TAC";
            tpSpeed = "lte_APP_Speed_Mb";
            themeName = "TD_LTE_RSRP";
            tpNCell_EARFCN = "lte_NCell_EARFCN";
            tpNCell_PCI = "lte_NCell_PCI";
        }
        public override string Name
        {
            get { return "弱覆盖路段自动统计"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11052, this.Name);
        }
        protected override bool getCondition()
        {
            if (curRpt != null)
            {
                weakCondition.CheckMinDistance = true;
                weakCondition.CheckMaxDistance = true;
                weakCondition.CheckMaxTPDistance = false;
                weakCondition.CheckNbMaxRSRP = false;
                weakCondition.CheckMinDuration = false;
                weakCondition.CheckSINR = false;
                weakCondition.MaxRSRP = (float)curRpt.RsrpMax;
                weakCondition.MinCoverRoadDistance = (float)curRpt.DisLastMin;
                weakCondition.MaxCoverRoadDistance = (float)curRpt.DisLastMax;
                weakCondition.MinWeakPointPercent = (float)curRpt.WeakPerMin;
                weakCondition.MaxSampleCellDistance = (int)curRpt.TpCellDisMax;
                weakCondition.MaxSampleCellAngle = (int)curRpt.TpCellAngleMax;
                setRoadCond();
                saveTestPoints = false;
                return true;
            }
            return false;
        }

        protected override void query()
        {
            if (MainModel.IsBackground && !MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getCondition())
            {
                return;
            }
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;

            if (MainModel.IsBackground)
            {
                FilterEventByRegion = false;
                QueryCondition cond = new QueryCondition();
                DateTime time = DateTime.Now.Date;
                cond.Periods.Add(new TimePeriod(new DateTime(time.Year, time.Month, 1).AddMonths(1 - 1 * curRpt.RecentMonths), DateTime.Now));
                string[] sArray = curRpt.Projects.Split(',');
                foreach (string str in sArray)
                {
                    int project = 0;
                    if (int.TryParse(str, out project))
                    {
                        cond.Projects.Add(project);
                    }
                }
                ServiceTypes.Clear();
                curRpt.ServiceIDSet.ForEach(service => ServiceTypes.Add((ServiceType)service));
                SetQueryCondition(cond);
            }

            int id = MainModel.DistrictID;
            foreach (int districtID in curRpt.DistrictIDSet)
            {
                if (MainModel.BackgroundStopRequest)
                {
                    break;
                }
                condition.DistrictID = MainModel.DistrictID = districtID;
                queryDistrictData(districtID);
            }
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;
            MainModel.DistrictID = id;
        }

        private void queryDistrictData(int districtID)
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
            }
            catch
            {
                //continue
            }

            if (!MainModel.IsBackground)
            {
                if (MainModel.QueryFromBackground)
                {
                    getBackgroundData();
                }
            }
            else
            {
                string disName = DistrictManager.GetInstance().getDistrictName(districtID);
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("开始分析" + disName + "库文件...");
                doBackgroundStatByFile(clientProxy);
            }
        }

        protected override void fireShowForm()
        {
            // Method intentionally left empty.
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            bool ignore = false;
            try
            {
                ignore = isIgnoreTestPoint(testPoint);
            }
            catch
            {
                //continue
            }
            return !ignore;
        }
        protected override void saveBackgroundData()
        {
            try
            {
                int ISnIndex = 1;
                doSomethingAfterAna();
                List<BackgroundResult> bgResultList = new List<BackgroundResult>();
                foreach (WeakCoverRoadLTE item in weakCoverList)
                {
                    BackgroundResult result = item.ConvertToBackgroundResult(curRpt.Projects);
                    result.SN = ISnIndex++;
                    result.SubFuncID = curRpt.FuncId;
                    bgResultList.Add(result);
                }
                WeakRoadAutoFuncManager.GetInstance().SaveResult_Road(curRpt.FuncId, curRpt.Projects, curAnaFileInfo, bgResultList);
                weakCoverList.Clear();
            }
            catch
            {
                //continue
            }
        }

        protected override void doBackgroundStatByFile(ClientProxy clientProxy)
        {
            getFilesForAnalyse();
            analyseFiles();
        }
        protected override void getFilesForAnalyse()
        {
            int iSTime = (int)(JavaDate.GetMilliseconds(Condition.Periods[0].BeginTime) / 1000);
            int iETime = (int)(JavaDate.GetMilliseconds(Condition.Periods[0].EndTime) / 1000);

            MasterCom.RAMS.Net.BackgroundFuncQueryManager.DIYSQLBackground_Road_GetFile roadGetFileQuery = MasterCom.RAMS.Net.BackgroundFuncQueryManager.DIYSQLBackground_Road_GetFile.GetInstanceRoadGetFile();
            roadGetFileQuery.SetCondition(iSTime, iETime, curRpt.FuncId, curRpt.Projects, ServiceTypeString, ((int)carrierID).ToString());
            roadGetFileQuery.Query();
        }

        protected override void getBackgroundData()
        {
            List<BackgroundResult> resultList = WeakRoadAutoFuncManager.GetInstance().GetResult_Road(Condition.Periods[0].BeginTime
                , Condition.Periods[0].EndTime, curRpt.FuncId, curRpt.Name, StatType, curRpt.Projects, MainModel.DistrictID);
            BackgroundResultList.AddRange(resultList);
        }
    }
}
