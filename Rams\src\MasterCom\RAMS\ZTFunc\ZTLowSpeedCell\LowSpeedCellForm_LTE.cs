﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LowSpeedCellForm_LTE : MinCloseForm
    {
        public LowSpeedCellForm_LTE(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;
            tsMenuExportExcel.Click += ExportExcel_Click;
            gridControl.DoubleClick += gridControl_DoubleClick;
        }

        public void FillData(List<LowSpeedCell_LTE> lowSpeedCellList)
        {
            gridControl.DataSource = lowSpeedCellList;
            gridControl.RefreshDataSource();
        }

        private void ExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                LowSpeedCell_LTE item = gridView.GetRow(gridView.GetSelectedRows()[0]) as LowSpeedCell_LTE;
                MainModel.MainForm.GetMapForm().GoToView(item.Longitude, item.Latitude);
                if (item.TestPoints.Count == 0)
                {
                    return;
                }

                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in item.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
            }
        }
    }
}
