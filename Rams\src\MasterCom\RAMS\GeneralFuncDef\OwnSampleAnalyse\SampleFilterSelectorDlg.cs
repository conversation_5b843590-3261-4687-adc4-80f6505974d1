﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.GeneralFuncDef.OwnSampleAnalyse;
using MasterCom.RAMS.GeneralFuncDef;

namespace MasterCom.RAMS.Func.OwnSampleAnalyse
{
    public partial class SampleFilterSelectorDlg : Form
    {
        List<CommonAnalyserCommander> settings;
        public SampleFilterSelectorDlg()
        {
            InitializeComponent();
            settings = loadOptionSettings();
            FreshSetting();
#if DEBUG
            cbxDebugMode.Visible = true;
#endif
        }

        internal BaseSubAnalyser GetSelectedAnalyser()
        {
            if(tabControlX.SelectedIndex ==0)//采样点
            {
                if (cbxDebugMode.Checked)
                {
                    SampleAnalyserDef def  = new SampleAnalyserDef();
                    def.debugMode = true;
                    return def; 
                }
                else
                {
                    SampleAnalyserDef def = new SampleAnalyserDef();
                    def.commander= cbxOwnFunc.SelectedItem as CommonAnalyserCommander;
                    return def;
                }
            }
            return null;
        }

        internal void FreshSetting()
        {
            cbxOwnFunc.Items.Clear();
            foreach(CommonAnalyserCommander cmder in settings)
            {
                cbxOwnFunc.Items.Add(cmder);
            }
            if(cbxOwnFunc.Items.Count>0)
            {
                cbxOwnFunc.SelectedIndex = 0;
            }
        }

        private List<CommonAnalyserCommander> loadOptionSettings()
        {
            List<CommonAnalyserCommander> optionList = new List<CommonAnalyserCommander>();
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(string.Format(Application.StartupPath + "/config/samplefilter.xml"));
                List<Object> list = configFile.GetItemValue("SampleAnaCommanderOptions", "options") as List<Object>;
                if (list != null)
                {
                    foreach (object value in list)
                    {
                        CommonAnalyserCommander option = new CommonAnalyserCommander();
                        option.Param = value as Dictionary<string, object>;
                        optionList.Add(option);
                    }
                }
            }
            catch
            {
                //continue
            }
            return optionList;
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            SampleFilterOwnFuncEditorDlg dlg = new SampleFilterOwnFuncEditorDlg();
            dlg.FillSetting(settings);
            dlg.ShowDialog();
            settings = dlg.GetSettingList();
            FreshSetting();

        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (cbxOwnFunc.SelectedItem == null
                && !cbxDebugMode.Checked)
            {
                MessageBox.Show("请选择扩展操作！");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}