﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class EndToEndSettingForm : BaseDialog
    {
        public EndToEndSettingForm()
        {
            InitializeComponent();
        }

        public void GetCondition(out EndToEndCondition condition)
        {
            condition = new EndToEndCondition();
            condition.Imssipinvite2Activatededicatedepsrequest = (int)numImssip2activaterequest.Value;
            condition.Activateddedicatedepsaccept2Deactivateepsrequest = (int)numActivateaccept2Deactivaterequest.Value;
            condition.Imssipinvite2Activatededicatedepsaccept = (int)numImssipinvite2Activateaccept.Value;
            condition.Activate2Sessionprogress183 = (int)numActivate2Session183.Value;
            condition.Detachrequest2DetachAccept = (int)numDetachrequest2accept.Value;
            condition.MoImssipbye2MtImssipbye = (int)numMoISB2MtISB.Value;
            condition.ISISesseionprogress1832ISCancel = (int)numISISession1832ISCancel.Value;
            condition.Imssipbye2Requestterminated487 = (int)numISBOK2002ISBRequest487.Value;
            condition.Imssipinvitetrying1002ISIServiceunavailable503 = (int)numTrying1002Unavailable503.Value;
            condition.Imssipinvitetrying1002DErequest2ISIServiceunavailable503 = (int)numTrying1002Dearequest2Unavailable503.Value;
            condition.Imssipinvite2Paging = (int)numImssipinvite2Paging.Value;
            condition.Imssipupdate2ISUServerinternalerror5002Cancel = (int)numISU2Error5002ISC.Value;
        }
    }
}
