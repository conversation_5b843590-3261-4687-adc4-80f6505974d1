﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment_NR
{
    public abstract class LastSegmentConditionBase_NR
    {
        public abstract string Name
        {
            get;
        }
        public override string ToString()
        {
            return Name;
        }
        protected double lastDistance = 0;
        public double LastDistance
        {
            get { return lastDistance; }
            set
            {
                if (value >= 0)
                {
                    lastDistance = value;
                }
            }
        }
        public abstract bool IsValid(TestPoint tp);
        public virtual bool IsValidSegment(RoadSegment_NR segment)
        {
            if (segment == null)
            {
                return false;
            }
            return segment.LastDistance >= lastDistance;
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is TestPoint_NR)
            {
                return tp["NR_SS_RSRP"];
            }
            return tp["NR_lte_RSRP"];
        }
        protected object GetARFCN(TestPoint tp)
        {
            if (tp is TestPoint_NR)
            {
                return tp["NR_SSB_ARFCN"];
            }
            return tp["NR_lte_EARFCN"];
        }
        protected object GetSINR(TestPoint tp)
        {
            if (tp is TestPoint_NR)
            {
                return tp["NR_SS_SINR"];
            }
            return tp["NR_lte_SINR"];
        }
        protected object GetNRSRP(TestPoint tp, int index)
        {
            if (tp is TestPoint_NR)
            {
                return tp["NR_NCell_RSRP", index];
            }
            return null;
        }
        protected object GetNARFCN(TestPoint tp, int index)
        {
            if (tp is TestPoint_NR)
            {
                return tp["NR_NCell_ARFCN", index];
            }
            return null;
        }
    }

    public class WeakCoverCondition_NR : LastSegmentConditionBase_NR
    {
        public WeakCoverCondition_NR()
        {
            RSRP = -105;
            lastDistance = 50;
        }
        public override string Name
        {
            get { return "弱覆盖"; }
        }
        public float RSRP
        {
            get;
            set;
        }
        public override bool IsValid(TestPoint tp)
        {
            float? rsrp = (float?)GetRSRP(tp);
            return rsrp <= this.RSRP;
        }
    }

    public class MultiCoverCondition_NR : LastSegmentConditionBase_NR
    {
        public MultiCoverCondition_NR()
        {
            RSRP = -105;
            RSRPDiff = 6;
            MultiGrade = 4;
            lastDistance = 20;
            SameARFCN = false;
        }
        public override string Name
        {
            get { return "重叠覆盖"; }
        }

        public float RSRP
        {
            get;
            set;
        }
        public float RSRPDiff
        {
            get;
            set;
        }
        public int MultiGrade
        {
            get;
            set;
        }
        public bool SameARFCN
        {
            get;
            set;
        }

        public override bool IsValid(TestPoint tp)
        {
            List<float> list = new List<float>();
            float? rsrp = (float?)GetRSRP(tp);
            if (rsrp >= RSRP)
            {
                list.Add((float)rsrp);
            }
            var arfcn = (int?)GetARFCN(tp);

            for (int i = 0; i < 10; i++)
            {
                float? nRsrp = (float?)GetNRSRP(tp, i);
                var nArfcn = (int?)GetNARFCN(tp, i);

                if (SameARFCN && arfcn != null)
                {
                    if (nRsrp >= RSRP && arfcn == nArfcn)
                    {
                        list.Add((float)nRsrp);
                    }
                }
                else
                {
                    if (nRsrp >= RSRP)
                    {
                        list.Add((float)nRsrp);
                    }
                }
            }

            if (list.Count < MultiGrade)
            {
                return false;
            }

            list.Sort();
            float max = list[list.Count - 1];
            if (max < RSRP)
            {
                return false;
            }

            float limit = list[list.Count - MultiGrade];
            return max - limit <= RSRPDiff;
        }
    }

    public class PoorSINRCondition_NR : LastSegmentConditionBase_NR
    {
        public PoorSINRCondition_NR()
        {
            RSRP = -105;
            SINR = 0;
            lastDistance = 20;
        }
        public override string Name
        {
            get { return "质差路段"; }
        }

        public float RSRP
        {
            get;
            set;
        }
        public float SINR
        {
            get;
            set;
        }

        public override bool IsValid(TestPoint tp)
        {
            float? sinr = (float?)GetSINR(tp);
            if (sinr > SINR)
            {
                return false;
            }
            float? rsrp = (float?)GetRSRP(tp);
            if (rsrp <= RSRP)
            {
                return false;
            }
            return true;
        }
    }
}
