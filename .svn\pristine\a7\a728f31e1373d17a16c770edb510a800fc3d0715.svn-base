﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.Model.Interface
{
    public partial class SelectGridColorModeDlg : Form
    {
        public SelectGridColorModeDlg()
        {
            InitializeComponent();
        }

        internal void FillCurrentReports(List<ReportStyle> rptStyleList)
        {
            cbxReportSel.Items.Clear();
            foreach(ReportStyle rs in rptStyleList)
            {
                cbxReportSel.Items.Add(rs);
            }
        }

        internal ReportStyle GetSelectedReport()
        {
            return cbxReportSel.SelectedItem as ReportStyle;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if(GetSelectedReport()!=null)
            {
                this.DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show(this, "请选择报表！", "选择");
            }
        }
    }
}