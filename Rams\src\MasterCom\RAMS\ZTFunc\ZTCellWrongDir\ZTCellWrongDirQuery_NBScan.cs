﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTCellWrongDirQuery_NBScan : ZTCellWrongDirQuery_LScan
    {
        public ZTCellWrongDirQuery_NBScan(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "覆盖方向异常_NBIOT扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33008, this.Name);
        }
    }
}
