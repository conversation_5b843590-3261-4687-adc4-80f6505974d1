﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTNotHoTopLevCell;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryNotHoTopLevCellByFiles_TD : QueryNotHoTopLevCell_TD
    {
        private static QueryNotHoTopLevCellByFiles_TD instance = null;
        public new static QueryNotHoTopLevCellByFiles_TD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QueryNotHoTopLevCellByFiles_TD();
                    }
                }
            }
            return instance;
        }

        protected QueryNotHoTopLevCellByFiles_TD()
            : base()
        {
        }

        public override string Name
        {
            get
            {
                return "未切换到最强邻区统计(按文件)";
            }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isDataInRegion(double lng, double lat)
        {
            return true;
        }

    }
}
