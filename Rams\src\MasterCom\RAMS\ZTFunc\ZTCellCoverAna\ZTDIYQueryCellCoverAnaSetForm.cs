﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYQueryCellCoverAnaSetForm : Form
    {
        public ZTDIYQueryCellCoverAnaSetForm()
        {
            InitializeComponent();
            comGridSize.SelectedIndex = 0;
        }
        public int iGrid
        {
            get
            {
                return int.Parse(comGridSize.SelectedItem.ToString().Split('*')[0]);
            }
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
