﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public class DIYSQLQueryCQTPoint : DIYSQLBase
    {
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 21000, 21001, this.Name);
        }
        /// <summary>
        /// 是否显示
        /// </summary>
        public DIYSQLQueryCQTPoint(MainModel mm)
            : base(mm)
        {
            MainDB = false;
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override void query()
        {
            CQTCfgManager.GetInstance();
            base.query();
        }

        protected override string getSqlTextString()
        {
            return "select pointID,pointName,pointAddr,pointDesc,tlLongitude,tlLatitude,brLongitude,brLatitude"
                    + ",altitude,pointType,densityType,aliasName,spaceType,coverType,networkType,otherType1"
                    + ",otherType2,otherType3,otherType4,belongArea,belongArea2"
                    + " from tb_cqt_point";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[21];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_Int;
            rType[11] = E_VType.E_String;
            rType[12] = E_VType.E_Int;
            rType[13] = E_VType.E_Int;
            rType[14] = E_VType.E_String;
            rType[15] = E_VType.E_Int;
            rType[16] = E_VType.E_Int;
            rType[17] = E_VType.E_Int;
            rType[18] = E_VType.E_Int;
            rType[19] = E_VType.E_String;
            rType[20] = E_VType.E_String;
            return rType;
        }

        /// <summary>
        /// 获取CQT点，获取顺序要与getSqlRetTypeArr方法的E_VType[]类型一致
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        private CQTPoint fillFrom(Content content)
        {
            CQTPoint pnt = new CQTPoint();
            pnt.ID = content.GetParamInt();
            pnt.Name = content.GetParamString();
            pnt.AddrAt = content.GetParamString();
            pnt.Desc = content.GetParamString();
            pnt.LTLongitude = content.GetParamDouble();
            pnt.LTLatitude = content.GetParamDouble();
            pnt.BRLongitude = content.GetParamDouble();
            pnt.BRLatitude = content.GetParamDouble();
            pnt.Altitude = content.GetParamInt();
            pnt.PointType = CQTCfgManager.GetInstance().GetCQTPointType(content.GetParamInt());
            pnt.DenstityType = CQTCfgManager.GetInstance().GetCQTDensityType(content.GetParamInt());
            pnt.AliasName = content.GetParamString();
            pnt.SpaceType = CQTCfgManager.GetInstance().GetCQTSpaceType(content.GetParamInt());
            pnt.CoverType =CQTCfgManager.GetInstance().GetCQTCoverType( content.GetParamInt());
            string[] netArr = content.GetParamString().Split(';');
            if (netArr != null)
            {
                List<int> netList = new List<int>();
                foreach (string s in netArr)
                {
                    netList.Add(int.Parse(s));
                }
                pnt.NetworkType = CQTCfgManager.GetInstance().GetCQTNetworkTypes(netList);
            }
            pnt.otherType1 = content.GetParamInt();
            pnt.otherType2 = content.GetParamInt();
            pnt.otherType3 = content.GetParamInt();
            pnt.otherType4 = content.GetParamInt();
            pnt.BelongArea = content.GetParamString();
            pnt.BelongArea2 = content.GetParamString();
            return pnt;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            CQTPointManager pointMng = CQTPointManager.GetInstance();
            pointMng.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CQTPoint pnt = fillFrom(package.Content);
                    pointMng.AddPoint(pnt);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "查询CQT地点"; }
        }

    }
}
