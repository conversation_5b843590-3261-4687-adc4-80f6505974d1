﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.IO;
using MasterCom.Util;
using System.Xml;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLowSpeedCauseCondition
    {
        public static readonly string CfgFileName = string.Format(System.Windows.Forms.Application.StartupPath + "/config/ztfunc/NR低速率原因分析.xml");

        public NRLowSpeedCauseCondition()
        {
            Causes = new List<NRLowSpeedCauseBase>();
            Causes.Add(new NRCoverCause());
            Causes.Add(new NRQualCause());
        }

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["sampleCntMin"] = this.TestPointCountMin;
                paramDic["distanceMin"] = this.DistanceMin;
                paramDic["secondMin"] = this.SecondMin;
                paramDic["checkFTP"] = this.CheckFTP;
                paramDic["ftpRateMax"] = this.FTPRateMax;
                paramDic["checkHTTP"] = this.CheckHTTP;
                paramDic["httpRateMax"] = this.HTTPRateMax;
                paramDic["checkEmail"] = this.CheckEmail;
                paramDic["emailRateMax"] = this.EmailRateMax;

                List<object> list = new List<object>();
                foreach (NRLowSpeedCauseBase cause in Causes)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["CauseSet"] = list;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.TestPointCountMin = (int)value["sampleCntMin"];
                this.DistanceMin = (double)value["distanceMin"];
                this.SecondMin = (double)value["secondMin"];
                this.CheckFTP = (bool)value["checkFTP"];
                this.FTPRateMax = (double)value["ftpRateMax"];
                this.CheckHTTP = (bool)value["checkHTTP"];
                this.HTTPRateMax = (double)value["httpRateMax"];
                this.CheckEmail = (bool)value["checkEmail"];
                this.EmailRateMax = (double)value["emailRateMax"];

                Causes = new List<NRLowSpeedCauseBase>();
                List<object> list = value["CauseSet"] as List<object>;
                foreach (object item in list)
                {
                    Dictionary<string, object> dic = item as Dictionary<string, object>;
                    string typeName = dic["TypeName"].ToString();
                    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                    NRLowSpeedCauseBase cause = (NRLowSpeedCauseBase)assembly.CreateInstance(typeName);
                    cause.CfgParam = dic;
                    Causes.Add(cause);
                }
            }
        }
        
        public int TestPointCountMin { get; set; } = 5;
        public double DistanceMin { get; set; } = 50;
        public double SecondMin { get; set; } = 10;
        public List<NRLowSpeedCauseBase> Causes { get; set; }
        public bool CheckFTP { get; set; } = true;
        public double FTPRateMax { get; set; } = 5;
        public bool CheckHTTP { get; set; } = true;
        public double HTTPRateMax { get; set; } = 5;
        public bool CheckEmail { get; set; } = true;
        public double EmailRateMax { get; set; } = 2;

        internal bool IsValidSpeed(Model.TestPoint testPoint)
        {
            short? type = NRTpHelper.NrTpManager.GetAppType(testPoint);
            if (type == null)
            {
                return false;
            }

            double? speed = NRTpHelper.NrTpManager.GetAppSpeedMb(testPoint);
            if (speed == null || speed <= 0)
            {
                return false;
            }
            if (type == (int)AppType.FTP_Download && CheckFTP)
            {
                return speed < FTPRateMax;
            }
            if (type == (int)AppType.Http_Download && CheckHTTP)
            {
                return speed < HTTPRateMax;
            }
            if (type == (int)AppType.Email_SMTP && CheckEmail)
            {
                return speed < EmailRateMax;
            }
            return false;
        }
    
        internal void Judge(NRLowSpeedSeg segItem, List<Model.Event> evts, List<TestPoint> allTP, NRTpManagerBase nRCond)
        {
            foreach (NRLowSpeedCauseBase item in Causes)
            {
                if (!segItem.NeedJudge)
                {
                    return;
                }
                item.Judge(segItem, evts, allTP, nRCond);
            }
            if (segItem.NeedJudge)
            {
                segItem.SetUnknowReason(nRCond);
            }
        }

        internal bool IsValidSegment(NRLowSpeedSeg seg)
        {
            return seg.SampleCount >= this.TestPointCountMin
                && seg.Distance >= DistanceMin && seg.Second >= SecondMin;
        }

        public void LoadCfg()
        {
            if (File.Exists(CfgFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(CfgFileName);
                CfgParam = configFile.GetItemValue("ConditionCfg", "Condition") as Dictionary<string, object>;
            }
        }

        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("ConditionCfg");
            xmlFile.AddItem(cfgE, "Condition", this.CfgParam);
            xmlFile.Save(CfgFileName);
        }
    }
}
