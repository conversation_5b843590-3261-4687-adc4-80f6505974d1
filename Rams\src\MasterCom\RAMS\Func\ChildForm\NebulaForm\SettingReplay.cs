﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.NebulaForm
{
    public partial class SettingReplay : MinCloseForm
    {
        public SettingReplay()
        {
            InitializeComponent();

            this.comboBoxSystem.SelectedIndexChanged += comboBoxSystem_SelectedIndexChanged;

        }

        List<MaxMin> li = new List<MaxMin>();
        void comboBoxSystem_SelectedIndexChanged(object sender, EventArgs e)
        {
            li.Clear();
            this.comboBoxX.Items.Clear();
            this.comboBoxY.Items.Clear();
            string systemName = (string)this.comboBoxSystem.SelectedItem;
            foreach (DTDisplayParameterInfo paramInfo in DTDisplayParameterManager.GetInstance()[systemName].DisplayParamInfos)
            {
                if ((paramInfo.Type & (int)DTDisplayParameterInfoType.Range) != 0)
                {
                    MaxMin maxmin = new MaxMin();
                    maxmin.max = paramInfo.ValueMax;
                    maxmin.min = paramInfo.ValueMin;
                    li.Add(maxmin);

                    this.comboBoxX.Items.Add(paramInfo.Name);
                    this.comboBoxY.Items.Add(paramInfo.Name);
                }
            }
            if (this.comboBoxX.Items.Contains(XYData.TargetX))
            {
                this.comboBoxX.SelectedItem = XYData.TargetX;
            }
            else if (this.comboBoxX.Items.Count > 0)
            {
                this.comboBoxX.SelectedIndex = 0;
            }

            if (this.comboBoxY.Items.Contains(XYData.TargetY))
            {
                this.comboBoxY.SelectedItem = XYData.TargetY;
            }
            else if (this.comboBoxY.Items.Count > 0)
            {
                this.comboBoxY.SelectedIndex = 0;
            }
        }

        private void SettingReplay_Load(object sender, EventArgs e)
        {
            this.loadSystem();
        }

        private void loadSystem()
        {
            this.comboBoxSystem.Items.Clear();
            foreach (DTDisplayParameterSystem system in DTDisplayParameterManager.GetInstance().Systems)
            {
                this.comboBoxSystem.Items.Add(system.Name);
            }
            if (this.comboBoxSystem.Items.Contains(XYData.Sys))
            {
                this.comboBoxSystem.SelectedItem = XYData.Sys;
            }
            else if (this.comboBoxSystem.Items.Count > 0)
            {
                this.comboBoxSystem.SelectedIndex = 0;
            } 
        }
        private void updateXYData()
        {
            string sys = (string)this.comboBoxSystem.SelectedItem;
            string itemX = (string)this.comboBoxX.SelectedItem;
            string itemY = (string)this.comboBoxY.SelectedItem;
            if (XYData.Sys == sys && XYData.TargetX == itemX && XYData.TargetY == itemY)
            {
                XYData.XsmallChanged = true;
                return;
            }

            int iX = this.comboBoxX.SelectedIndex;
            int iY = this.comboBoxY.SelectedIndex;
            XYData.Xsmall = li[iX].min;
            XYData.Xlarge = li[iX].max;
            XYData.Ysmall = li[iY].min;
            XYData.Ylarge = li[iY].max;
            XYData.Xcenter = (XYData.Xsmall + XYData.Xlarge) / 2;
            XYData.Ycenter = (XYData.Ysmall + XYData.Ylarge) / 2;

            XYData.Sys = sys;
            XYData.TargetX = itemX;
            XYData.TargetY = itemY;
            XYData.XsmallChanged = true;

            XYData.InitScaleItem(XYData.ListScaleItemX, XYData.Xsmall, XYData.Xlarge);
            XYData.InitScaleItem(XYData.ListScaleItemY, XYData.Ysmall, XYData.Ylarge);
        }
        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.updateXYData();
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }
    }
}
