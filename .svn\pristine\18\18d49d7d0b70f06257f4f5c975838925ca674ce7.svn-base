﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Collections.ObjectModel;
using System.Drawing.Drawing2D;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteAntMR : ZTAntennaBase
    {
        public ZTLteAntMR(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "LTE MR天线分析"; }
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.None; }
        }
        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 28000, 28007, this.Name);
        }

        string strCityName = "";
        protected AntTimeCfg timeCfg = new AntTimeCfg();
        Dictionary<int, CellParaData> cellParaDataDic { get; set; } = new Dictionary<int, CellParaData>();

        //小区MR数据
        protected Dictionary<int, LteMrItem> lteMREciDic = new Dictionary<int, LteMrItem>();
        protected Dictionary<int, LteMrItem> lteMREciSDic = new Dictionary<int, LteMrItem>();

        //小区性能数据
        protected Dictionary<int, CellPara> cellParaEciDic = new Dictionary<int, CellPara>();
        protected Dictionary<int, CellPara> cellParaEciSDic = new Dictionary<int, CellPara>();//Sector取一位数

        //状态库天线数据
        public Dictionary<int, AntCfgSub> antCfgParaDic { get; set; } = new Dictionary<int, AntCfgSub>();
        public Dictionary<int, AntCfgSub> antCfgParaSDic { get; set; } = new Dictionary<int, AntCfgSub>();

        //天线权值数据
        Dictionary<int, AntennaPara> antParaEciDic = new Dictionary<int, AntennaPara>();
        Dictionary<string, AntennaPara> antParaCellNameDic = new Dictionary<string, AntennaPara>();

        #region MR分类数据
        protected Dictionary<int, ZTAntMRBaseItem> lteMRTimeDiffEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        protected Dictionary<int, ZTAntMRBaseItem> lteMRTimeDiffEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        protected Dictionary<int, ZTAntMRBaseItem> lteMRPowerHeadRoomEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        protected Dictionary<int, ZTAntMRBaseItem> lteMRPowerHeadRoomEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        protected Dictionary<int, ZTAntMRBaseItem> lteMRRsrpEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        protected Dictionary<int, ZTAntMRBaseItem> lteMRRsrpEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        protected Dictionary<int, ZTAntMRBaseItem> lteMRAoaEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        protected Dictionary<int, ZTAntMRBaseItem> lteMRAoaEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        protected Dictionary<int, ZTAntMRBaseItem> lteMRSinrUlEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        protected Dictionary<int, ZTAntMRBaseItem> lteMRSinrUlEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        protected Dictionary<int, ZTAntMRBaseItem> lteMRTAEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        protected Dictionary<int, ZTAntMRBaseItem> lteMRTAEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        protected Dictionary<int, ZTAntMRBaseItem> lteMRTAAoaEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        protected Dictionary<int, ZTAntMRBaseItem> lteMRTAAoaEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        protected Dictionary<int, LteCoverItem> lteMRCoverEciDic = new Dictionary<int, LteCoverItem>();
        protected Dictionary<int, LteCoverItem> lteMRCoverEciSDic = new Dictionary<int, LteCoverItem>();

        #endregion

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                if (condition.IsByRound)
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 00:00:00", condition.ByRoundYear, condition.ByRoundRound))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 23:59:59", condition.ByRoundYear, condition.ByRoundRound)).AddMonths(1).AddDays(-1)) / (1000L));
                }
                else
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 00:00:00", condition.Periods[0].BeginTime))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 23:59:59", condition.Periods[0].EndTime.AddDays(-1)))) / (1000L));
                }

                MainModel.ClearDTData();
                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";
                strCityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                WaitBox.Show("读取MR数据处理...", doWithCellMRData);
                WaitBox.Show("分类获取MR数据...", doWithCellMRByTypeData);
                WaitBox.Show("读取小区性能数据...", doWithCellParaData);
                WaitBox.Show("读取状态库天线信息...", doWithAntCfgData);
                WaitBox.Show("联合数据处理...", AnaCellAngleData);
                dealMainUtranCellSample();
            }
            catch
            {
                clientProxy.Close();
            }
            finally
            {
                clearData();
                ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic = null;
            }
        }

        protected void clearData()
        {
            lteMREciDic.Clear();
            lteMREciSDic.Clear();
            cellParaEciDic.Clear();
            cellParaEciSDic.Clear();
            antCfgParaDic.Clear();
            antCfgParaSDic.Clear();

            lteMRTimeDiffEciDic.Clear();
            lteMRTimeDiffEciSDic.Clear();
            lteMRPowerHeadRoomEciDic.Clear();
            lteMRPowerHeadRoomEciSDic.Clear();
            lteMRRsrpEciDic.Clear();
            lteMRRsrpEciSDic.Clear();
            lteMRAoaEciDic.Clear();
            lteMRAoaEciSDic.Clear();
            lteMRSinrUlEciDic.Clear();
            lteMRSinrUlEciSDic.Clear();
            lteMRTAEciDic.Clear();
            lteMRTAEciSDic.Clear();
            lteMRTAAoaEciDic.Clear();
            lteMRTAAoaEciSDic.Clear();

            lteMRCoverEciDic.Clear();
            lteMRCoverEciSDic.Clear();
        }

        #region 数据库查询类
        /// <summary>
        /// 查询小区性能参数数据
        /// </summary>
        protected void doWithCellParaData()
        {
            WaitBox.CanCancel = true;
            DiyCellPara cellPara = new DiyCellPara(MainModel, timeCfg);
            cellPara.SetQueryCondition(condition);
            cellPara.Query();
            cellParaEciDic = cellPara.cellParaEciDic;
            cellParaEciSDic = cellPara.cellParaEciSDic;
            WaitBox.Close();
        }
        
        /// <summary>
        /// 小区MR数据
        /// </summary>
        protected void doWithCellMRData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            DiyLTECellMR lteCelMR = new DiyLTECellMR(MainModel, timeCfg);
            lteCelMR.SetQueryCondition(condition);
            lteCelMR.Query();
            lteMREciDic = lteCelMR.lteMREciDic;
            lteMREciSDic = lteCelMR.lteMREciSDic;

            WaitBox.ProgressPercent = 60;
            DiyLTEMRCover lteCover = new DiyLTEMRCover(MainModel, timeCfg);
            lteCover.SetQueryCondition(condition);
            lteCover.Query();
            lteMRCoverEciDic = lteCover.lteMREciDic;
            lteMRCoverEciSDic = lteCover.lteMREciSDic;

            WaitBox.ProgressPercent = 90;

            WaitBox.Close();
        }

        /// <summary>
        /// 查询状态库天线数据
        /// </summary>
        protected void doWithAntCfgData()
        {
            WaitBox.CanCancel = true;
            DiyCfgPara cellPara = new DiyCfgPara(MainModel);
            cellPara.Query();
            antCfgParaDic = cellPara.antCfgParaDic;
            antCfgParaSDic = cellPara.antCfgParaSDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 分类获取MR数据
        /// </summary>
        protected void doWithCellMRByTypeData()
        {
            WaitBox.ProgressPercent = 10;
            doWithMRPowerHeadRoomData();
            WaitBox.ProgressPercent = 20;
            doWithMRRsrpData();
            WaitBox.ProgressPercent = 40;
            doWithMRAoaData();
            WaitBox.ProgressPercent = 60;
            doWithMRSinrUlData();
            WaitBox.ProgressPercent = 80;
            doWithMRTAData();
            WaitBox.ProgressPercent = 90;
            doWithMRRttdAoaData();
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        /// <summary>
        /// UE发射功率余量
        /// </summary>
        protected void doWithMRPowerHeadRoomData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 2, 64, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRPowerHeadRoomEciDic = lteMRData.lteMREciDic;
            lteMRPowerHeadRoomEciSDic = lteMRData.lteMREciSDic;
        }

        /// <summary>
        /// 参考信号接收功率
        /// </summary>
        protected void doWithMRRsrpData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 3, 48, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRRsrpEciDic = lteMRData.lteMREciDic;
            lteMRRsrpEciSDic = lteMRData.lteMREciSDic;
        }

        /// <summary>
        /// ENB天线到达角
        /// </summary>
        protected void doWithMRAoaData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 4, 72, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRAoaEciDic = lteMRData.lteMREciDic;
            lteMRAoaEciSDic = lteMRData.lteMREciSDic;
        }

        /// <summary>
        /// 上行信噪比
        /// </summary>
        protected void doWithMRSinrUlData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 5, 37, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRSinrUlEciDic = lteMRData.lteMREciDic;
            lteMRSinrUlEciSDic = lteMRData.lteMREciSDic;
        }

        /// <summary>
        /// 时间提前量
        /// </summary>
        protected void doWithMRTAData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 6, 44, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRTAEciDic = lteMRData.lteMREciDic;
            lteMRTAEciSDic = lteMRData.lteMREciSDic;
        }

        /// <summary>
        /// UE收发时间差与eNB天线到达角
        /// </summary>
        protected void doWithMRRttdAoaData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 7, 132, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRTAAoaEciDic = lteMRData.lteMREciDic;
            lteMRTAAoaEciSDic = lteMRData.lteMREciSDic;
        }

        /// <summary>
        /// 查询小区参数数据
        /// </summary>
        protected void doWithParaData()
        {
            WaitBox.CanCancel = true;
            DiyAntennaPara antPara = new DiyAntennaPara(MainModel, timeCfg);
            antPara.SetQueryCondition(condition);
            antPara.Query();
            antParaEciDic = antPara.antParaEciDic;
            antParaCellNameDic = antPara.antParaCellNameDic;
            WaitBox.Close();
        }
        #endregion

        /// <summary>
        /// 分析小区的角度数组，进行平滑化处理
        /// </summary>
        private void AnaCellAngleData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 0;
            int iCount = antCfgParaSDic.Count;
            int iNum = 0;

            foreach (int ieci in antCfgParaSDic.Keys)
            {
                if (WaitBox.CancelRequest)
                    break;
                WaitBox.ProgressPercent = (int)(100 * ((iNum * 1.0) / iCount));
                try
                {
                    AntCfgSub antCfgSub = antCfgParaSDic[ieci];
                    CellParaData cellPara = new CellParaData();
                    cellPara.iEci = ieci;
                    cellPara.index = iNum + 1;
                    cellPara.strcityname = strCityName;
                    cellPara.cellname = antCfgSub.strCellName;
                    cellPara.cellNameEn = antCfgSub.strCellNameEn;
                    cellPara.iTac = antCfgSub.iTAC;
                    cellPara.strMatch = "是";
                    cellPara.iangle_dir = (int)antCfgSub.方向角;

                    LTECell lteMainCell = CellManager.GetInstance().GetNearestLTECellByTACCI(DateTime.Now, antCfgSub.iTAC, ieci, antCfgSub.fLongitude, antCfgSub.fLatitude);
                    if (lteMainCell != null)
                    {
                        cellPara.dLongitude = lteMainCell.Longitude;
                        cellPara.dLatitude = lteMainCell.Latitude;
                    }
                    else
                    {
                        cellPara.dLongitude = antCfgSub.fLongitude;
                        cellPara.dLatitude = antCfgSub.fLatitude;
                    }

                    int iNewEci = (cellPara.iEci / 256) * 256 + ((cellPara.iEci % 256) % 10);
                    AnaCellMrData(ref cellPara, iNewEci);
                    ZTLteAntMRAna.AnaRttdAoaArray ary = new ZTLteAntMRAna.AnaRttdAoaArray();
                    ary.AnaRttdAoa90 = cellPara.cellMrData.AnaRttdAoa;
                    Dictionary<int, int> tmpDirSampleDic = ZTAntFuncHelper.GetDirSample(ary);
                    cellPara.dirSampleDic = ZTAntFuncHelper.GetNewDirSample(tmpDirSampleDic);
                    cellPara.iAntMaxDir = ZTAntFuncHelper.GetAntMaxDir(tmpDirSampleDic);

                    LongLat ll = new LongLat();
                    ll.fLongitude = (float)(cellPara.dLongitude);
                    ll.fLatitude = (float)(cellPara.dLatitude);
                    double[] aoaArray = ConvertArray(cellPara.dirSampleDic);
                    int iMaxValue = -50;
                    int iMinValue = 50;
                    ZTAntFuncHelper.getMaxAndMinValue(aoaArray, ref iMaxValue, ref iMinValue);
                    cellPara.mrAoaList = ZTAntFuncHelper.getCellMrCover(ll, aoaArray, iMaxValue, iMinValue, 0);

                    setStrAntResult(cellPara);

                    if (!cellParaDataDic.ContainsKey(ieci))
                    {
                        cellParaDataDic.Add(ieci, cellPara);
                    }
                    iNum++;
                }
                catch (Exception)
                {
                    //continue
                }
            }
            WaitBox.Close();
        }

        private static void setStrAntResult(CellParaData cellPara)
        {
            if (cellPara.iAntMaxDir != -1)
            {
                cellPara.strIsMrCell = "是";
                cellPara.iDirDiff = ZTAntFuncHelper.CalcAntDir(cellPara.iAntMaxDir, cellPara.iangle_dir);
                if (cellPara.iDirDiff >= 150)
                {
                    cellPara.strAntResult = "背瓣覆盖;";
                }
                else if (cellPara.iDirDiff >= 75)
                {
                    cellPara.strAntResult = "旁瓣泄露;";
                }

                if (cellPara.strAntResult == "")
                {
                    return;
                }

                if (cellPara.cellMrData.lteMRItem.dAvgSinr < -3)
                {
                    cellPara.strAntResult += "严重干扰";
                }
                else if (cellPara.cellMrData.lteMRItem.dAvgSinr < 0)
                {
                    cellPara.strAntResult += "一般干扰";
                }

                if (cellPara.cellMrData.lteMRItem.dAvgRsrp <= -110 && cellPara.cellMrData.lteMRItem.dRate95Rsrp < 20 && cellPara.cellMrData.lteMRItem.dRate110Rsrp < 60)
                {
                    cellPara.strAntResult += "严重弱覆盖";
                }
                else if (cellPara.cellMrData.lteMRItem.dAvgRsrp <= -110 || cellPara.cellMrData.lteMRItem.dRate95Rsrp < 20 || cellPara.cellMrData.lteMRItem.dRate110Rsrp < 60)
                {
                    cellPara.strAntResult += "弱覆盖";
                }
            }
        }

        /// <summary>
        /// 转换为数组
        /// </summary>
        protected double[] ConvertArray(Dictionary<int, int> tmpDirSampleDic)
        {
            double[] tmpArray = new double[72];
            for (int i = 0; i < 72; i++)
            {
                if (tmpDirSampleDic.ContainsKey(i))
                {
                    tmpArray[i] = tmpDirSampleDic[i];
                }
            }
            return tmpArray;
        }

        protected void AnaCellMrData(ref CellParaData cellPara, int iNewEci)
        {
            //小区性能数据
            CellPara cPara;
            if (!cellParaEciDic.TryGetValue(iNewEci, out cPara)
                && !cellParaEciSDic.TryGetValue(iNewEci, out cPara))
            {
                cPara = new CellPara();
            }
            cellPara.cellPara = cPara;

            //天线权值信息
            AntennaPara antPara;
            if (!antParaEciDic.TryGetValue(iNewEci, out antPara)
                && !antParaCellNameDic.TryGetValue(cellPara.cellname, out antPara))
            {
                antPara = new AntennaPara();
            }
            cellPara.antPara = antPara;

            //状态库天线数据
            AntCfgSub antCfg;
            if (!antCfgParaDic.TryGetValue(iNewEci, out antCfg)
                && !antCfgParaSDic.TryGetValue(iNewEci, out antCfg))
            {
                antCfg = new AntCfgSub();
            }
            cellPara.antCfg = antCfg;

            //小区MR数据
            LteMrItem lteMRItem;
            if (!lteMREciDic.TryGetValue(iNewEci, out lteMRItem)
                && !lteMREciSDic.TryGetValue(iNewEci, out lteMRItem))
            {
                lteMRItem = new LteMrItem();
            }
            cellPara.cellMrData.lteMRItem = lteMRItem;

            //小区MR覆盖指数
            LteCoverItem lteMRCoverItem;
            if (!lteMRCoverEciDic.TryGetValue(iNewEci, out lteMRCoverItem)
                && !lteMRCoverEciSDic.TryGetValue(iNewEci, out lteMRCoverItem))
            {
                lteMRCoverItem = new LteCoverItem();
            }
            cellPara.cellMrData.lteMRCoverItem = lteMRCoverItem;

            #region 分类MR数据赋值
            cellPara.cellMrData.lteMRPowerHeadRoomItem = getMRItem(lteMRPowerHeadRoomEciDic, lteMRPowerHeadRoomEciSDic, iNewEci);
            cellPara.cellMrData.lteMRRsrpItem = getMRItem(lteMRRsrpEciDic, lteMRRsrpEciSDic, iNewEci);
            cellPara.cellMrData.lteMRAoaItem = getMRItem(lteMRAoaEciDic, lteMRAoaEciSDic, iNewEci);
            cellPara.cellMrData.lteMRSinrUlItem = getMRItem(lteMRSinrUlEciDic, lteMRSinrUlEciSDic, iNewEci);
            cellPara.cellMrData.lteMRRttdAoaItem = getMRItem(lteMRTAAoaEciDic, lteMRTAAoaEciSDic, iNewEci);
            cellPara.cellMrData.lteMRTaItem = getMRItem(lteMRTAEciDic, lteMRTAEciSDic, iNewEci);
            #endregion
        }


        private ZTAntMRBaseItem getMRItem(Dictionary<int, ZTAntMRBaseItem> eciDic, Dictionary<int, ZTAntMRBaseItem> eciSDic, int eci)
        {
            ZTAntMRBaseItem item;
            if (!eciDic.TryGetValue(eci, out item)
                && !eciSDic.TryGetValue(eci, out item))
            {
                item = new ZTAntMRBaseItem();
            }
            return item;
        }

        /// <summary>
        /// 设置需要的字段
        /// </summary>
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LteAntenna");
            tmpDic.Add("themeName", (object)"lte_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        ///<summary>
        ///显示结果窗体
        ///</summary>
        private void FireShowResultForm(List<List<NPOIRow>> nrDatasList, List<string> sheetNames)
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteAntMRForm).FullName);
            LteAntMRForm form = obj == null ? null : obj as LteAntMRForm;
            if (form == null || form.IsDisposed)
            {
                form = new LteAntMRForm(MainModel);
            }
            form.nrDatasList = nrDatasList;
            form.sheetNames = sheetNames;

            form.FillData(MainModel.ListCellLteMRData);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        #region 数据整理及导出
        /// <summary>
        /// 按主服导出数据
        /// </summary>
        private void dealMainUtranCellSample()
        {
            List<NPOIRow> data8s = new List<NPOIRow>();
            NPOIRow nr8 = new NPOIRow();
            List<object> col8s = new List<object>();

            #region 字段定义
            col8s.Add("序号");
            col8s.Add("地市");
            col8s.Add("小区英文名");
            col8s.Add("小区名");
            col8s.Add("主设备厂家");
            col8s.Add("是否区配工参");
            col8s.Add("CGI");
            col8s.Add("覆盖类型");
            col8s.Add("小区频段");

            col8s.Add("方位角");
            col8s.Add("预置下倾角");
            col8s.Add("机械下倾角");
            col8s.Add("电调下倾角");
            col8s.Add("挂高");

            col8s.Add("上行吞吐量");
            col8s.Add("下行吞吐量");
            col8s.Add("无线接通率");
            col8s.Add("无线掉线率");
            col8s.Add("切换成功率");
            col8s.Add("ERAB建立成功率");
            col8s.Add("ERAB掉线率");
            col8s.Add("RSRP均值");
            col8s.Add("SINR均值");
            col8s.Add("95覆盖率");
            col8s.Add("110覆盖率");

            col8s.Add("MRO总采样点数");
            col8s.Add("重叠覆盖条件采样点数");
            col8s.Add("重叠覆盖指数");
            col8s.Add("过覆盖影响小区数");
            col8s.Add("高重叠覆盖小区");
            col8s.Add("过覆盖小区");

            col8s.Add("MR主覆盖角");
            col8s.Add("与天线工参偏差值");
            col8s.Add("是否可MR波形重构");
            #endregion

            nr8.cellValues = col8s;
            data8s.Add(nr8);

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            List<CellParaData> listCellParaData = new List<CellParaData>();
            foreach (int ieci in cellParaDataDic.Keys)
            {
                try
                {
                    NPOIRow nr9 = new NPOIRow();
                    nr9.cellValues = FillCellValue(cellParaDataDic[ieci]);
                    data8s.Add(nr9);
                    listCellParaData.Add(cellParaDataDic[ieci]);
                }
                catch (Exception)
                {
                    //continue
                }
            }
            nrDatasList.Add(data8s);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("MR联合天线分析");
            MainModel.ListCellLteMRData = listCellParaData;
            FireShowResultForm(nrDatasList, sheetNames);
        }

        /// <summary>
        /// 小区级赋值
        /// </summary>
        private List<object> FillCellValue(CellParaData data)
        {
            List<object> objs8 = new List<object>();
            objs8.Add(data.index);//序号
            objs8.Add(data.strcityname);//地市
            objs8.Add(data.cellNameEn);//小区英文名
            objs8.Add(data.cellname);//小区名
            objs8.Add(data.antCfg.strVender);//主设备厂家

            objs8.Add(data.strMatch);//是否区配工参
            objs8.Add("460-00-" + data.antCfg.iEnodebID.ToString() + "-" + data.antCfg.iSectorID.ToString());//CGI
            objs8.Add(data.antCfg.strType);//覆盖类型
            objs8.Add(data.antCfg.strBtsType);//小区频段

            objs8.Add(Math.Round(data.antCfg.方向角, 2));
            objs8.Add(Math.Round(data.antCfg.预置下倾角, 2));
            objs8.Add(Math.Round(data.antCfg.机械下倾角, 2));
            objs8.Add(Math.Round(data.antCfg.电调下倾角, 2));
            objs8.Add(Math.Round(data.antCfg.挂高, 2));

            objs8.Add(Math.Round(data.cellPara.F上行吞吐量, 2));
            objs8.Add(Math.Round(data.cellPara.F下行吞吐量, 2));
            objs8.Add(Math.Round(data.cellPara.F无线接通率, 2));
            objs8.Add(Math.Round(data.cellPara.F无线掉线率, 2));
            objs8.Add(Math.Round(data.cellPara.F切换成功率, 2));
            objs8.Add(Math.Round(data.cellPara.fERAB建立成功率, 2));
            objs8.Add(Math.Round(data.cellPara.fERAB掉线率, 2));

            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dAvgRsrp, 2));//RSRP均值
            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dAvgSinr, 2));//SINR均值
            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dRate95Rsrp, 2));//95覆盖率
            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dRate110Rsrp, 2));//110覆盖率

            objs8.Add(data.cellMrData.lteMRCoverItem.IMRO总采样点数);//MRO总采样点数
            objs8.Add(data.cellMrData.lteMRCoverItem.I重叠覆盖条件采样点数);//重叠覆盖条件采样点数
            objs8.Add(Math.Round(data.cellMrData.lteMRCoverItem.F重叠覆盖指数, 2) + "%");//重叠覆盖指数
            objs8.Add(data.cellMrData.lteMRCoverItem.I过覆盖影响小区数);//过覆盖影响小区数
            objs8.Add(data.cellMrData.lteMRCoverItem.S高重叠覆盖小区);//高重叠覆盖小区
            objs8.Add(data.cellMrData.lteMRCoverItem.S过覆盖小区);//过覆盖小区

            objs8.Add(data.iAntMaxDir);//MR主覆盖角
            objs8.Add(data.iDirDiff);//与天线工参偏差值
            objs8.Add(data.strIsMrCell);//天线分析

            objs8.AddRange(data.antObject);
            return objs8;
        }

        #endregion

        public class CellParaData
        {
            //小区
            public int index { get; set; }
            public string strcityname { get; set; }
            public string cellname { get; set; }
            public string cellNameEn { get; set; }
            public int iTac { get; set; }
            public int iEci { get; set; }
            public string strMatch { get; set; }
            public int iangle_dir { get; set; }
            public double dLongitude { get; set; }
            public double dLatitude { get; set; }

            public AntennaPara antPara { get; set; }
            public CellPara cellPara { get; set; }//小区性能数据
            public AntCfgSub antCfg { get; set; }//天线配置信息
            public List<object> antObject { get; set; } = new List<object>();

            public int iDirDiff { get; set; }
            public int iAntMaxDir { get; set; }
            public string strAntResult { get; set; }//原因分析
            public string strProbType { get; set; }//告警状态
            public string strIsMrCell { get; set; }
            public Dictionary<int, int> dirSampleDic { get; set; } = new Dictionary<int, int>();
            public List<LongLat> mrAoaList { get; set; } = new List<LongLat>();//MR数据角度级采样点
            public CellMrData cellMrData { get; set; }

            public CellParaData()
            {
                index = 0;
                strcityname = "";
                cellname = "";
                strMatch = "是";
                iTac = 0;
                iEci = 0;
                iangle_dir = 0;
                dLongitude = 0;
                dLatitude = 0;

                iDirDiff = 0;
                iAntMaxDir = -1;
                strAntResult = "";
                strProbType = "";
                strIsMrCell = "否";

                cellPara = new CellPara();
                antCfg = new AntCfgSub();
                antPara = new AntennaPara();
                cellMrData = new CellMrData();
            }
        }
    }
}
