﻿namespace MasterCom.RAMS.KPI_Statistics
{
    partial class KPIReportFormSheet
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            this.groupBox = new System.Windows.Forms.GroupBox();
            this.dataView = new System.Windows.Forms.DataGridView();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miInsertRow = new System.Windows.Forms.ToolStripMenuItem();
            this.miInsertCol = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miRemoveRows = new System.Windows.Forms.ToolStripMenuItem();
            this.miRemoveCols = new System.Windows.Forms.ToolStripMenuItem();
            this.miExport = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcelSimple = new System.Windows.Forms.ToolStripMenuItem();
            this.exportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miCellOption = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripSeparator();
            this.miInsertRowSuf = new System.Windows.Forms.ToolStripMenuItem();
            this.miInsertColSuf = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataView)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox
            // 
            this.groupBox.BackColor = System.Drawing.SystemColors.GradientInactiveCaption;
            this.groupBox.Controls.Add(this.dataView);
            this.groupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox.Location = new System.Drawing.Point(0, 0);
            this.groupBox.Name = "groupBox";
            this.groupBox.Size = new System.Drawing.Size(741, 391);
            this.groupBox.TabIndex = 0;
            this.groupBox.TabStop = false;
            this.groupBox.Text = "miniTitile";
            // 
            // dataView
            // 
            this.dataView.AllowUserToAddRows = false;
            this.dataView.AllowUserToDeleteRows = false;
            this.dataView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataView.ColumnHeadersVisible = false;
            this.dataView.ContextMenuStrip = this.ctxMenu;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.GradientInactiveCaption;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.InfoText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataView.DefaultCellStyle = dataGridViewCellStyle1;
            this.dataView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataView.GridColor = System.Drawing.Color.SteelBlue;
            this.dataView.Location = new System.Drawing.Point(3, 17);
            this.dataView.Name = "dataView";
            this.dataView.ReadOnly = true;
            this.dataView.RowHeadersVisible = false;
            this.dataView.RowHeadersWidth = 30;
            this.dataView.RowTemplate.Height = 18;
            this.dataView.ShowCellErrors = false;
            this.dataView.ShowCellToolTips = false;
            this.dataView.ShowEditingIcon = false;
            this.dataView.ShowRowErrors = false;
            this.dataView.Size = new System.Drawing.Size(735, 371);
            this.dataView.TabIndex = 11;
            this.dataView.CellMouseUp += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.dataView_CellMouseUp);
            this.dataView.DoubleClick += new System.EventHandler(this.dataView_DoubleClick);
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miInsertRow,
            this.miInsertRowSuf,
            this.miInsertCol,
            this.miInsertColSuf,
            this.toolStripMenuItem1,
            this.miRemoveRows,
            this.miRemoveCols,
            this.toolStripSeparator1,
            this.miExport,
            this.toolStripMenuItem2,
            this.miCellOption});
            this.ctxMenu.Name = "contextMenuStrip1";
            this.ctxMenu.Size = new System.Drawing.Size(162, 220);
            // 
            // miInsertRow
            // 
            this.miInsertRow.Name = "miInsertRow";
            this.miInsertRow.Size = new System.Drawing.Size(161, 22);
            this.miInsertRow.Text = "前插入行      —";
            this.miInsertRow.Click += new System.EventHandler(this.miInsertRow_Click);
            // 
            // miInsertCol
            // 
            this.miInsertCol.Name = "miInsertCol";
            this.miInsertCol.Size = new System.Drawing.Size(161, 22);
            this.miInsertCol.Text = "前插入列      |";
            this.miInsertCol.Click += new System.EventHandler(this.miInsertCol_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(158, 6);
            // 
            // miRemoveRows
            // 
            this.miRemoveRows.Name = "miRemoveRows";
            this.miRemoveRows.Size = new System.Drawing.Size(161, 22);
            this.miRemoveRows.Text = "删除所在行   —";
            this.miRemoveRows.Click += new System.EventHandler(this.miRemoveRows_Click);
            // 
            // miRemoveCols
            // 
            this.miRemoveCols.Name = "miRemoveCols";
            this.miRemoveCols.Size = new System.Drawing.Size(161, 22);
            this.miRemoveCols.Text = "删除所在列   |";
            this.miRemoveCols.Click += new System.EventHandler(this.miRemoveCols_Click);
            // 
            // miExport
            // 
            this.miExport.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportToExcelSimple,
            this.exportExcel});
            this.miExport.Name = "miExport";
            this.miExport.Size = new System.Drawing.Size(161, 22);
            this.miExport.Text = "导出为";
            // 
            // miExportToExcelSimple
            // 
            this.miExportToExcelSimple.Name = "miExportToExcelSimple";
            this.miExportToExcelSimple.Size = new System.Drawing.Size(185, 22);
            this.miExportToExcelSimple.Text = "Excel文件(不带样式)";
            this.miExportToExcelSimple.Click += new System.EventHandler(this.miExportToExcelSimple_Click);
            // 
            // exportExcel
            // 
            this.exportExcel.Name = "exportExcel";
            this.exportExcel.Size = new System.Drawing.Size(185, 22);
            this.exportExcel.Text = "Excel文件(详细)";
            this.exportExcel.Click += new System.EventHandler(this.exportExcel_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(158, 6);
            // 
            // miCellOption
            // 
            this.miCellOption.Name = "miCellOption";
            this.miCellOption.Size = new System.Drawing.Size(161, 22);
            this.miCellOption.Text = "单元格设置...";
            this.miCellOption.Click += new System.EventHandler(this.miCellOption_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(158, 6);
            // 
            // miInsertRowSuf
            // 
            this.miInsertRowSuf.Name = "miInsertRowSuf";
            this.miInsertRowSuf.Size = new System.Drawing.Size(161, 22);
            this.miInsertRowSuf.Text = "后插入行      —";
            this.miInsertRowSuf.Click += new System.EventHandler(this.miInsertRowSuf_Click);
            // 
            // miInsertColSuf
            // 
            this.miInsertColSuf.Name = "miInsertColSuf";
            this.miInsertColSuf.Size = new System.Drawing.Size(161, 22);
            this.miInsertColSuf.Text = "后插入列      |";
            this.miInsertColSuf.Click += new System.EventHandler(this.miInsertColSuf_Click);
            // 
            // KPIReportFormSheet
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(741, 391);
            this.Controls.Add(this.groupBox);
            this.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "KPIReportFormSheet";
            this.Text = "KPIReportFormSheet";
            this.groupBox.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataView)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox;
        private System.Windows.Forms.DataGridView dataView;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miInsertRow;
        private System.Windows.Forms.ToolStripMenuItem miInsertCol;
        private System.Windows.Forms.ToolStripMenuItem miRemoveRows;
        private System.Windows.Forms.ToolStripMenuItem miRemoveCols;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExport;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcelSimple;
        private System.Windows.Forms.ToolStripMenuItem exportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miCellOption;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem miInsertRowSuf;
        private System.Windows.Forms.ToolStripMenuItem miInsertColSuf;
    }
}