﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDCopareWeakCoverRoadSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.numDpchC_I = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numMaxDistance = new System.Windows.Forms.NumericUpDown();
            this.numPccpchC_I = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numDistance = new System.Windows.Forms.NumericUpDown();
            this.numPccpchRscpThreshold = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.endTime1 = new System.Windows.Forms.DateTimePicker();
            this.endTime2 = new System.Windows.Forms.DateTimePicker();
            this.beginTime2 = new System.Windows.Forms.DateTimePicker();
            this.beginTime1 = new System.Windows.Forms.DateTimePicker();
            this.label10 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.条件门限设置 = new DevExpress.XtraEditors.GroupControl();
            this.checkDpchC2I = new System.Windows.Forms.CheckBox();
            this.checkPccpchC2I = new System.Windows.Forms.CheckBox();
            this.label16 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.numRoadRadius = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.numDpchC_I)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchC_I)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchRscpThreshold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.条件门限设置)).BeginInit();
            this.条件门限设置.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRoadRadius)).BeginInit();
            this.SuspendLayout();
            // 
            // numDpchC_I
            // 
            this.numDpchC_I.Enabled = false;
            this.numDpchC_I.Location = new System.Drawing.Point(222, 87);
            this.numDpchC_I.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numDpchC_I.Minimum = new decimal(new int[] {
            20,
            0,
            0,
            -2147483648});
            this.numDpchC_I.Name = "numDpchC_I";
            this.numDpchC_I.Size = new System.Drawing.Size(80, 21);
            this.numDpchC_I.TabIndex = 41;
            this.numDpchC_I.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDpchC_I.Value = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(309, 148);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 39;
            this.label7.Text = "米";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(112, 148);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 38;
            this.label6.Text = "相邻采样点距离≤";
            // 
            // numMaxDistance
            // 
            this.numMaxDistance.Location = new System.Drawing.Point(222, 143);
            this.numMaxDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxDistance.Name = "numMaxDistance";
            this.numMaxDistance.Size = new System.Drawing.Size(80, 21);
            this.numMaxDistance.TabIndex = 37;
            this.numMaxDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // numPccpchC_I
            // 
            this.numPccpchC_I.Enabled = false;
            this.numPccpchC_I.Location = new System.Drawing.Point(222, 59);
            this.numPccpchC_I.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numPccpchC_I.Minimum = new decimal(new int[] {
            20,
            0,
            0,
            -2147483648});
            this.numPccpchC_I.Name = "numPccpchC_I";
            this.numPccpchC_I.Size = new System.Drawing.Size(80, 21);
            this.numPccpchC_I.TabIndex = 36;
            this.numPccpchC_I.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numPccpchC_I.Value = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(309, 120);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 32;
            this.label4.Text = "米";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(309, 35);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(23, 12);
            this.label3.TabIndex = 31;
            this.label3.Text = "dBm";
            // 
            // numDistance
            // 
            this.numDistance.Location = new System.Drawing.Point(222, 115);
            this.numDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDistance.Name = "numDistance";
            this.numDistance.Size = new System.Drawing.Size(80, 21);
            this.numDistance.TabIndex = 30;
            this.numDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // numPccpchRscpThreshold
            // 
            this.numPccpchRscpThreshold.Location = new System.Drawing.Point(222, 30);
            this.numPccpchRscpThreshold.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numPccpchRscpThreshold.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numPccpchRscpThreshold.Name = "numPccpchRscpThreshold";
            this.numPccpchRscpThreshold.Size = new System.Drawing.Size(80, 21);
            this.numPccpchRscpThreshold.TabIndex = 29;
            this.numPccpchRscpThreshold.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numPccpchRscpThreshold.Value = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(148, 120);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 28;
            this.label2.Text = "持续距离≥";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(112, 37);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(101, 12);
            this.label1.TabIndex = 27;
            this.label1.Text = "TD_PCCPCH_RSCP≤";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.endTime1);
            this.groupControl1.Controls.Add(this.endTime2);
            this.groupControl1.Controls.Add(this.beginTime2);
            this.groupControl1.Controls.Add(this.beginTime1);
            this.groupControl1.Controls.Add(this.label10);
            this.groupControl1.Controls.Add(this.label14);
            this.groupControl1.Controls.Add(this.label13);
            this.groupControl1.Controls.Add(this.label9);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(472, 93);
            this.groupControl1.TabIndex = 42;
            this.groupControl1.Text = "对比时间段设置";
            // 
            // endTime1
            // 
            this.endTime1.Location = new System.Drawing.Point(299, 30);
            this.endTime1.Name = "endTime1";
            this.endTime1.Size = new System.Drawing.Size(153, 21);
            this.endTime1.TabIndex = 28;
            // 
            // endTime2
            // 
            this.endTime2.Location = new System.Drawing.Point(299, 57);
            this.endTime2.Name = "endTime2";
            this.endTime2.Size = new System.Drawing.Size(153, 21);
            this.endTime2.TabIndex = 28;
            // 
            // beginTime2
            // 
            this.beginTime2.Location = new System.Drawing.Point(112, 57);
            this.beginTime2.Name = "beginTime2";
            this.beginTime2.Size = new System.Drawing.Size(152, 21);
            this.beginTime2.TabIndex = 28;
            // 
            // beginTime1
            // 
            this.beginTime1.Location = new System.Drawing.Point(111, 30);
            this.beginTime1.Name = "beginTime1";
            this.beginTime1.Size = new System.Drawing.Size(153, 21);
            this.beginTime1.TabIndex = 28;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(22, 64);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(71, 12);
            this.label10.TabIndex = 27;
            this.label10.Text = "时间段2：从";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(272, 64);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 12);
            this.label14.TabIndex = 27;
            this.label14.Text = "到";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(272, 37);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 27;
            this.label13.Text = "到";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(22, 37);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(71, 12);
            this.label9.TabIndex = 27;
            this.label9.Text = "时间段1：从";
            // 
            // 条件门限设置
            // 
            this.条件门限设置.Controls.Add(this.checkDpchC2I);
            this.条件门限设置.Controls.Add(this.checkPccpchC2I);
            this.条件门限设置.Controls.Add(this.numPccpchRscpThreshold);
            this.条件门限设置.Controls.Add(this.label1);
            this.条件门限设置.Controls.Add(this.numDpchC_I);
            this.条件门限设置.Controls.Add(this.label2);
            this.条件门限设置.Controls.Add(this.numDistance);
            this.条件门限设置.Controls.Add(this.label16);
            this.条件门限设置.Controls.Add(this.label7);
            this.条件门限设置.Controls.Add(this.label3);
            this.条件门限设置.Controls.Add(this.label15);
            this.条件门限设置.Controls.Add(this.label6);
            this.条件门限设置.Controls.Add(this.label4);
            this.条件门限设置.Controls.Add(this.numRoadRadius);
            this.条件门限设置.Controls.Add(this.numMaxDistance);
            this.条件门限设置.Controls.Add(this.numPccpchC_I);
            this.条件门限设置.Dock = System.Windows.Forms.DockStyle.Top;
            this.条件门限设置.Location = new System.Drawing.Point(0, 93);
            this.条件门限设置.Name = "条件门限设置";
            this.条件门限设置.Size = new System.Drawing.Size(472, 206);
            this.条件门限设置.TabIndex = 43;
            this.条件门限设置.Text = "条件门限设置";
            // 
            // checkDpchC2I
            // 
            this.checkDpchC2I.AutoSize = true;
            this.checkDpchC2I.Location = new System.Drawing.Point(111, 91);
            this.checkDpchC2I.Name = "checkDpchC2I";
            this.checkDpchC2I.Size = new System.Drawing.Size(102, 16);
            this.checkDpchC2I.TabIndex = 42;
            this.checkDpchC2I.Text = "TD_DPCH_C/I≤";
            this.checkDpchC2I.UseVisualStyleBackColor = true;
            this.checkDpchC2I.CheckedChanged += new System.EventHandler(this.checkDpchC2I_CheckedChanged);
            // 
            // checkPccpchC2I
            // 
            this.checkPccpchC2I.AutoSize = true;
            this.checkPccpchC2I.Location = new System.Drawing.Point(99, 63);
            this.checkPccpchC2I.Name = "checkPccpchC2I";
            this.checkPccpchC2I.Size = new System.Drawing.Size(114, 16);
            this.checkPccpchC2I.TabIndex = 42;
            this.checkPccpchC2I.Text = "TD_PCCPCH_C/I≤";
            this.checkPccpchC2I.UseVisualStyleBackColor = true;
            this.checkPccpchC2I.CheckedChanged += new System.EventHandler(this.checkPccpchC2I_CheckedChanged);
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(309, 178);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(17, 12);
            this.label16.TabIndex = 39;
            this.label16.Text = "米";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(58, 178);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(155, 12);
            this.label15.TabIndex = 38;
            this.label15.Text = "时间段1测试路线栅格化大小";
            // 
            // numRoadRadius
            // 
            this.numRoadRadius.Location = new System.Drawing.Point(222, 173);
            this.numRoadRadius.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numRoadRadius.Name = "numRoadRadius";
            this.numRoadRadius.Size = new System.Drawing.Size(80, 21);
            this.numRoadRadius.TabIndex = 37;
            this.numRoadRadius.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRoadRadius.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(274, 321);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 45;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(368, 321);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 45;
            this.btnCancel.Text = "取消";
            // 
            // TDCopareWeakCoverRoadSettingDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(472, 356);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.条件门限设置);
            this.Controls.Add(this.groupControl1);
            this.Name = "TDCopareWeakCoverRoadSettingDlg";
            this.Text = "条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numDpchC_I)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchC_I)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchRscpThreshold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.条件门限设置)).EndInit();
            this.条件门限设置.ResumeLayout(false);
            this.条件门限设置.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRoadRadius)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.NumericUpDown numDpchC_I;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numMaxDistance;
        private System.Windows.Forms.NumericUpDown numPccpchC_I;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numDistance;
        private System.Windows.Forms.NumericUpDown numPccpchRscpThreshold;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.DateTimePicker endTime1;
        private System.Windows.Forms.DateTimePicker endTime2;
        private System.Windows.Forms.DateTimePicker beginTime2;
        private System.Windows.Forms.DateTimePicker beginTime1;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label9;
        private DevExpress.XtraEditors.GroupControl 条件门限设置;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.NumericUpDown numRoadRadius;
        private System.Windows.Forms.CheckBox checkDpchC2I;
        private System.Windows.Forms.CheckBox checkPccpchC2I;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
    }
}