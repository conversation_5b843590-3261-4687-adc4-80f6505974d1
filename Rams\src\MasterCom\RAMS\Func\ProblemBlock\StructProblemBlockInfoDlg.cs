using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Func.ProblemBlock
{
    public partial class StructProblemBlockInfoDlg : Form
    {
        MainModel mModel;
        ProblemBlockItem bb;
        public StructProblemBlockInfoDlg(MainModel mModel)
        {
            InitializeComponent();
            this.mModel = mModel;
        }
        public void FillProblemBlockInfo(ProblemBlockItem bb)
        {
            listViewEvents.Items.Clear();
            this.bb = bb;
            //info
            tbxBlockID.Text = bb.blockId.ToString();
            tbxStatus.Text = bb.StructStatusDes;
            tbxAbDays.Text = bb.abnormal_days.ToString();
            tbxNormalDays.Text = bb.normal_days.ToString();
            tbxAbEventCount.Text = bb.EventCount.ToString();
            tbxCreateDate.Text = bb.CreateDateString;
            tbxCloseDate.Text = bb.ClosedDateString;
            tbxLastTest.Text = bb.Last_test_dateString;
            tbxFirstAbDate.Text = bb.First_abnormal_dateString;
            tbxLastAbEvent.Text = bb.Last_abnormal_dateString;
            tbxPlaceDesc.Text = bb.PlaceDesc;
            tbxCellDesc.Text = bb.cell_names;
            tbxEventDes.Text = bb.EventsDes;
            tbCloseReason.Text = bb.order_seq;
            tbEvaluateDate.Text = bb.Evaluate_DateString;
            tbSettingDate.Text = bb.SetBlackspots_dateString;
            tbEvaluateInfo.Text = bb.evaluate_result;

            foreach (ProblemBlockEventItem eventItem in bb.AbEvents)
            {
                ListViewItem lvi = new ListViewItem();
                int idx=bb.AbEvents.IndexOf(eventItem) + 1;
                lvi.Text = idx.ToString();
                lvi.SubItems[0].Tag = idx;
                lvi.SubItems.Add(eventItem.EventDes);
                lvi.SubItems[1].Tag = eventItem.EventDes;
                DateTime dt = JavaDate.GetDateTimeFromMilliseconds(eventItem.itimevalue * 1000L);
                lvi.SubItems.Add(dt.ToString("yyyy-MM-dd HH:mm:ss"));
                lvi.SubItems[2].Tag = dt;
                double longitude = eventItem.ilongitude;
                double latitude = eventItem.ilatitude;
                lvi.SubItems.Add(longitude.ToString());
                lvi.SubItems[3].Tag = longitude;
                lvi.SubItems.Add(latitude.ToString());
                lvi.SubItems[4].Tag = latitude;
                string projName;
                if (((CategoryEnum)CategoryManager.GetInstance()["Project"])[eventItem.iprojectid] != null)
                {
                    projName = ((CategoryEnum)CategoryManager.GetInstance()["Project"])[eventItem.iprojectid].Name;
                }
                else
                {
                    projName = "-";
                }
                lvi.SubItems.Add(projName);
                lvi.SubItems[5].Tag = projName;
                lvi.SubItems.Add(eventItem.filename);
                lvi.SubItems[6].Tag = eventItem.filename;
                lvi.Tag = eventItem;
                listViewEvents.Items.Add(lvi);
            }
        }

        private void miReplayEvent_Click(object sender, EventArgs e)
        {
            mModel.MainForm.NeedChangeWorkSpace(false);
            if (listViewEvents.SelectedItems.Count > 0)
            {
                ProblemBlockEventItem be = listViewEvents.SelectedItems[0].Tag as ProblemBlockEventItem;
                Event evt = be.ConvertToEvent();
                MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(evt);
            }
        }

    }
}