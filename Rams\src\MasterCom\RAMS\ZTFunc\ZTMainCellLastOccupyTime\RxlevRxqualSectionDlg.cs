﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class RxlevRxqualSectionDlg : BaseFormStyle
    {
        public RangeSet RangeSetRxlev { get; set; }
        public RangeSet RangeSetRxqual { get; set; }

        public RxlevRxqualSectionDlg(RangeSet RangeSetRxlev, RangeSet RangeSetRxqual)
        {
            InitializeComponent();
            this.RangeSetRxlev = RangeSetRxlev;
            this.RangeSetRxqual = RangeSetRxqual;
            refreshRxlev();
            refreshRxqual();
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void buttonEditRxlev_Click(object sender, EventArgs e)
        {
            FormRangeSetSetting dialog = new FormRangeSetSetting();
            dialog.RangeSetSetting.RangeAll = new Range(double.MinValue, true, double.MaxValue, false);
            dialog.RangeSetSetting.DecimalPlaces = 0;
            dialog.RangeSetSetting.RangeSet = RangeSetRxlev;
            if (dialog.ShowDialog(this) == DialogResult.OK)
            {
                RangeSetRxlev.Clear();
                RangeSetRxlev.AddRange(dialog.RangeSetSetting.RangeSet.Values);
            }
            refreshRxlev();
        }

        private void buttonEditRxqual_Click(object sender, EventArgs e)
        {
            FormRangeSetSetting dialog = new FormRangeSetSetting();
            dialog.RangeSetSetting.RangeAll = new Range(double.MinValue, true, double.MaxValue, false);
            dialog.RangeSetSetting.DecimalPlaces = 0;
            dialog.RangeSetSetting.RangeSet = RangeSetRxqual;
            if (dialog.ShowDialog(this) == DialogResult.OK)
            {
                RangeSetRxqual.Clear();
                RangeSetRxqual.AddRange(dialog.RangeSetSetting.RangeSet.Values);
            }
            refreshRxqual();
        }

        private void refreshRxlev()
        {
            listViewRxlev.Items.Clear();
            foreach (Range rge in RangeSetRxlev.Values)
            {
                ListViewItem lvi = new ListViewItem(rge.ToString());
                lvi.Tag = rge;
                listViewRxlev.Items.Add(lvi);
            }
        }

        private void refreshRxqual()
        {
            listViewRxqual.Items.Clear();
            foreach (Range rge in RangeSetRxqual.Values)
            {
                ListViewItem lvi = new ListViewItem(rge.ToString());
                lvi.Tag = rge;
                listViewRxqual.Items.Add(lvi);
            }
        }
    }
}
