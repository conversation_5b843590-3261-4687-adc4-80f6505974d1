﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.Interface
{
    public static class ESReplayOptionHelper
    {
        internal static  DIYReplayContentOption getESDIYReplayOption(string sampletbname)
        {
            DIYReplayContentOption option = null;
            if (sampletbname.IndexOf("gsm") != -1)//GSM
            {
                option = dealGsmReplayOption(sampletbname, option);
            }
            else if (sampletbname.IndexOf("tdscdma") != -1)//TD
            {
                option = dealTdscdmaReplayOption(sampletbname, option);
            }
            else if (sampletbname.IndexOf("wcdma") != -1)//W
            {
                option = dealWcdmaReplayOption();
            }
            else if (sampletbname.IndexOf("cdma") != -1)//W
            {
                option = dealCdmaReplayOption();
            }
            return option;
        }

        private static DIYReplayContentOption dealGsmReplayOption(string sampletbname, DIYReplayContentOption option)
        {
            if (sampletbname.IndexOf("sample6_") != -1)//new
            {
                option = new DIYReplayContentOption();
                option.MessageInclude = true;
                option.MessageL3HexCode = true;
                option.EventInclude = true;
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 1, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 2, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 3, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 4, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 5, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 6, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 7, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 8, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 9, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 10, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 11, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 12, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 13, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 14, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 15, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 16, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 17, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 18, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 19, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 20, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 21, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 22, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 23, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 24, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 25, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 26, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(1, 1, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(1, 2, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(1, 3, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(1, 4, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(1, 5, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(1, 6, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(1, 7, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(1, 8, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(1, 9, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(5, 6, 23));//MAIO
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(2, 1, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(2, 2, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(2, 3, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(2, 4, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(2, 6, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(2, 7, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(2, 8, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(3, 1001, 23));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(6, 1001, 23));
            }
            else if (sampletbname.IndexOf("sample_") != -1)//old
            {
                option = new DIYReplayContentOption();
                option.MessageInclude = true;
                option.MessageL3HexCode = true;
                option.EventInclude = true;
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 1, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 2, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 3, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 4, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 5, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 6, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 7, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 8, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 9, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 10, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 11, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 12, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 13, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 16, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 17, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 18, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 19, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 20, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 21, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 22, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 23, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 24, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 25, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 26, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 27, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 30, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 31, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 32, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 35, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 36, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 37, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 40, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 41, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 42, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 45, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 46, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 47, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 50, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 51, 27));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 52, 27));
                for (int i = 55; i <= 113; i++)
                {
                    option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, i, 27));
                }
            }

            return option;
        }

        private static DIYReplayContentOption dealTdscdmaReplayOption(string sampletbname, DIYReplayContentOption option)
        {
            if (sampletbname.IndexOf("sample6_") != -1)//new
            {
                option = new DIYReplayContentOption();
                option.MessageInclude = true;
                option.MessageL3HexCode = true;
                option.EventInclude = true;
                for (int i = 1; i <= 23; i++)
                {
                    option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, i, 37));
                }
                for (int i = 1; i <= 7; i++)
                {
                    option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(1, i, 37));
                }
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(2, 1001, 37));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(3, 1001, 37));
                for (int i = 1; i <= 8; i++)
                {
                    option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(4, i, 37));
                }
                for (int i = 1; i <= 3; i++)
                {
                    option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(6, i, 37));
                }
                for (int i = 1; i <= 10; i++)
                {
                    option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(8, i, 37));
                }
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(8, 12, 37));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(8, 15, 37));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(9, 3, 37));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(9, 4, 37));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(9, 7, 37));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(9, 8, 37));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(9, 9, 37));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(9, 10, 37));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(13, 1, 37));
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(13, 4, 37));
            }
            else if (sampletbname.IndexOf("sample_") != -1)//old
            {
                option = new DIYReplayContentOption();
                option.MessageInclude = true;
                option.MessageL3HexCode = true;
                option.EventInclude = true;
                for (int i = 1; i <= 161; i++)
                {
                    option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, i, 35));
                }
            }

            return option;
        }

        private static DIYReplayContentOption dealWcdmaReplayOption()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.MessageInclude = true;
            option.MessageL3HexCode = true;
            option.EventInclude = true;
            for (int i = 1; i <= 34; i++)
            {
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, i, 41));
            }
            for (int i = 1; i <= 14; i++)
            {
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(1, i, 41));
            }
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(2, 1001, 41));
            for (int i = 1; i <= 3; i++)
            {
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(4, i, 41));
            }
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(5, 1001, 41));
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(8, 1001, 41));
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(9, 1001, 41));
            for (int i = 1; i <= 9; i++)
            {
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(11, i, 41));
            }
            for (int i = 1; i <= 8; i++)
            {
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(12, i, 41));
            }

            return option;
        }

        private static DIYReplayContentOption dealCdmaReplayOption()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.MessageInclude = true;
            option.MessageL3HexCode = true;
            option.EventInclude = true;
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 1, 12));
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 2, 12));
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 3, 12));
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 4, 12));
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 6, 12));
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, 7, 12));
            for (int i = 11; i <= 22; i++)
            {
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, i, 12));
            }
            for (int i = 24; i <= 28; i++)
            {
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(0, i, 12));
            }
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(2, 1001, 12));
            for (int i = 1; i <= 16; i++)
            {
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(3, i, 12));
            }
            option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(6, 1001, 12));
            for (int i = 1; i <= 3; i++)
            {
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(18, i, 12));
            }
            for (int i = 1; i <= 2; i++)
            {
                option.SampleColumns.Add(InterfaceManager.GetInstance().GetColumnDef(19, i, 12));
            }

            return option;
        }
    }
}
