﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class KPIReportForm : Form
    {
        public KPIReportForm()
        {
            InitializeComponent();
            init();
        }
        private BindingSource kpiBrindingSource;
        private BindingSource kqiBrindingSource;
        private void init()
        {
            kpiBrindingSource = new BindingSource();
            kpiBrindingSource.DataSource = typeof(RegionKPIItem);
            kqiBrindingSource = new BindingSource();
            kqiBrindingSource.DataSource = typeof(RegionKQIItem);
        }

        public void FillData(string objname, string timepick, List<RegionKPIItem> kpiitems, List<RegionKQIItem> kqiitems)
        {
            this.labelGridName.Text = objname;
            this.labelDatePick.Text = timepick;
            kpiBrindingSource.Clear();
            foreach (RegionKPIItem kpiitem in kpiitems)
            {
                kpiBrindingSource.Add(kpiitem);
            }
            this.gvGridKPI.DataSource = kpiBrindingSource;
            this.kqiBrindingSource.Clear();
            foreach (RegionKQIItem kqiitem in kqiitems)
            {
                kqiBrindingSource.Add(kqiitem);
            }
            this.gvGridKQI.DataSource = kqiBrindingSource;
        }
    }
}
