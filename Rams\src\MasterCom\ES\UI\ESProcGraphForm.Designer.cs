﻿namespace MasterCom.ES.UI
{
    partial class ESProcGraphForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ESProcGraphForm));
            this.splitContainerMain = new System.Windows.Forms.SplitContainer();
            this.panelLeft = new System.Windows.Forms.Panel();
            this.splitTaskPane = new System.Windows.Forms.SplitContainer();
            this.treeViewRoutines = new System.Windows.Forms.TreeView();
            this.propGrid = new System.Windows.Forms.PropertyGrid();
            this.splitMain = new System.Windows.Forms.SplitContainer();
            this.mainGraphPanel = new System.Windows.Forms.Panel();
            this.panelDirty = new System.Windows.Forms.Panel();
            this.btnMakeNotDirty = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.btnHideRight = new System.Windows.Forms.Button();
            this.panelRight = new System.Windows.Forms.Panel();
            this.splitResv = new System.Windows.Forms.SplitContainer();
            this.panel1 = new System.Windows.Forms.Panel();
            this.treeViewRsvValues = new System.Windows.Forms.TreeView();
            this.label1 = new System.Windows.Forms.Label();
            this.panel2 = new System.Windows.Forms.Panel();
            this.treeViewInProcResv = new System.Windows.Forms.TreeView();
            this.label2 = new System.Windows.Forms.Label();
            this.mainToolBar = new System.Windows.Forms.ToolStrip();
            this.btnSave = new System.Windows.Forms.ToolStripButton();
            this.btnSaveDirtyTo = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.btnModeAddConn = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.btnLarge = new System.Windows.Forms.ToolStripButton();
            this.btnSmall = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.btnParamSetting = new System.Windows.Forms.ToolStripButton();
            this.btnL3ParamSetting = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.btnContinueRun = new System.Windows.Forms.ToolStripButton();
            this.miNextCursor = new System.Windows.Forms.ToolStripButton();
            this.btnStepOver = new System.Windows.Forms.ToolStripButton();
            this.bbtnStepInto = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
            this.btnClearBrk = new System.Windows.Forms.ToolStripButton();
            this.btnFindText = new System.Windows.Forms.ToolStripButton();
            this.ctxMenuNode = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.设置流程ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miTypeCondition = new System.Windows.Forms.ToolStripMenuItem();
            this.miTypeOperation = new System.Windows.Forms.ToolStripMenuItem();
            this.miTypePreType = new System.Windows.Forms.ToolStripMenuItem();
            this.miTypeReason = new System.Windows.Forms.ToolStripMenuItem();
            this.miTypeSolved = new System.Windows.Forms.ToolStripMenuItem();
            this.miTypeOtherProc = new System.Windows.Forms.ToolStripMenuItem();
            this.插入下级流程节点ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miInsCondition = new System.Windows.Forms.ToolStripMenuItem();
            this.miInsOperation = new System.Windows.Forms.ToolStripMenuItem();
            this.miInsPreType = new System.Windows.Forms.ToolStripMenuItem();
            this.miInsReason = new System.Windows.Forms.ToolStripMenuItem();
            this.miInsSolved = new System.Windows.Forms.ToolStripMenuItem();
            this.miInsOtherProc = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripSeparator();
            this.miDelPath2Child = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripSeparator();
            this.miNodeCut = new System.Windows.Forms.ToolStripMenuItem();
            this.miNodeCopy = new System.Windows.Forms.ToolStripMenuItem();
            this.miNodePaste = new System.Windows.Forms.ToolStripMenuItem();
            this.miNodeDelete = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripSeparator();
            this.miBreakAt = new System.Windows.Forms.ToolStripMenuItem();
            this.ctxMenuResv = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miAddResvItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miDelResvItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ctxMenuRoutine = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miAddRoutine = new System.Windows.Forms.ToolStripMenuItem();
            this.miDelRoutine = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miAddGroup = new System.Windows.Forms.ToolStripMenuItem();
            this.miDelGroup = new System.Windows.Forms.ToolStripMenuItem();
            this.btnPackToZip = new System.Windows.Forms.ToolStripButton();
            this.splitContainerMain.Panel1.SuspendLayout();
            this.splitContainerMain.Panel2.SuspendLayout();
            this.splitContainerMain.SuspendLayout();
            this.panelLeft.SuspendLayout();
            this.splitTaskPane.Panel1.SuspendLayout();
            this.splitTaskPane.Panel2.SuspendLayout();
            this.splitTaskPane.SuspendLayout();
            this.splitMain.Panel1.SuspendLayout();
            this.splitMain.Panel2.SuspendLayout();
            this.splitMain.SuspendLayout();
            this.mainGraphPanel.SuspendLayout();
            this.panelDirty.SuspendLayout();
            this.panelRight.SuspendLayout();
            this.splitResv.Panel1.SuspendLayout();
            this.splitResv.Panel2.SuspendLayout();
            this.splitResv.SuspendLayout();
            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.mainToolBar.SuspendLayout();
            this.ctxMenuNode.SuspendLayout();
            this.ctxMenuResv.SuspendLayout();
            this.ctxMenuRoutine.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainerMain
            // 
            this.splitContainerMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerMain.Location = new System.Drawing.Point(0, 25);
            this.splitContainerMain.Name = "splitContainerMain";
            // 
            // splitContainerMain.Panel1
            // 
            this.splitContainerMain.Panel1.Controls.Add(this.panelLeft);
            // 
            // splitContainerMain.Panel2
            // 
            this.splitContainerMain.Panel2.Controls.Add(this.splitMain);
            this.splitContainerMain.Size = new System.Drawing.Size(830, 402);
            this.splitContainerMain.SplitterDistance = 135;
            this.splitContainerMain.TabIndex = 0;
            // 
            // panelLeft
            // 
            this.panelLeft.Controls.Add(this.splitTaskPane);
            this.panelLeft.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelLeft.Location = new System.Drawing.Point(0, 0);
            this.panelLeft.Name = "panelLeft";
            this.panelLeft.Size = new System.Drawing.Size(135, 402);
            this.panelLeft.TabIndex = 0;
            // 
            // splitTaskPane
            // 
            this.splitTaskPane.BackColor = System.Drawing.SystemColors.GradientInactiveCaption;
            this.splitTaskPane.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitTaskPane.Location = new System.Drawing.Point(0, 0);
            this.splitTaskPane.Name = "splitTaskPane";
            this.splitTaskPane.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitTaskPane.Panel1
            // 
            this.splitTaskPane.Panel1.Controls.Add(this.treeViewRoutines);
            // 
            // splitTaskPane.Panel2
            // 
            this.splitTaskPane.Panel2.Controls.Add(this.propGrid);
            this.splitTaskPane.Size = new System.Drawing.Size(135, 402);
            this.splitTaskPane.SplitterDistance = 207;
            this.splitTaskPane.TabIndex = 0;
            // 
            // treeViewRoutines
            // 
            this.treeViewRoutines.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewRoutines.Location = new System.Drawing.Point(0, 0);
            this.treeViewRoutines.Name = "treeViewRoutines";
            this.treeViewRoutines.Size = new System.Drawing.Size(135, 207);
            this.treeViewRoutines.TabIndex = 0;
            this.treeViewRoutines.NodeMouseClick += new System.Windows.Forms.TreeNodeMouseClickEventHandler(this.treeView_NodeMouseClick);
            // 
            // propGrid
            // 
            this.propGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.propGrid.Location = new System.Drawing.Point(0, 0);
            this.propGrid.Name = "propGrid";
            this.propGrid.Size = new System.Drawing.Size(135, 191);
            this.propGrid.TabIndex = 0;
            // 
            // splitMain
            // 
            this.splitMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitMain.Location = new System.Drawing.Point(0, 0);
            this.splitMain.Name = "splitMain";
            // 
            // splitMain.Panel1
            // 
            this.splitMain.Panel1.Controls.Add(this.mainGraphPanel);
            // 
            // splitMain.Panel2
            // 
            this.splitMain.Panel2.Controls.Add(this.panelRight);
            this.splitMain.Size = new System.Drawing.Size(691, 402);
            this.splitMain.SplitterDistance = 536;
            this.splitMain.TabIndex = 2;
            this.splitMain.MouseClick += new System.Windows.Forms.MouseEventHandler(this.splitMain_MouseClick);
            // 
            // mainGraphPanel
            // 
            this.mainGraphPanel.AutoScroll = true;
            this.mainGraphPanel.BackColor = System.Drawing.Color.White;
            this.mainGraphPanel.Controls.Add(this.panelDirty);
            this.mainGraphPanel.Controls.Add(this.btnHideRight);
            this.mainGraphPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.mainGraphPanel.Location = new System.Drawing.Point(0, 0);
            this.mainGraphPanel.Name = "mainGraphPanel";
            this.mainGraphPanel.Size = new System.Drawing.Size(536, 402);
            this.mainGraphPanel.TabIndex = 1;
            this.mainGraphPanel.Paint += new System.Windows.Forms.PaintEventHandler(this.mainGraphPanel_Paint);
            this.mainGraphPanel.MouseMove += new System.Windows.Forms.MouseEventHandler(this.mainGraphPanel_MouseMove);
            this.mainGraphPanel.MouseClick += new System.Windows.Forms.MouseEventHandler(this.mainGraphPanel_MouseClick);
            this.mainGraphPanel.MouseDown += new System.Windows.Forms.MouseEventHandler(this.mainGraphPanel_MouseDown);
            this.mainGraphPanel.MouseUp += new System.Windows.Forms.MouseEventHandler(this.mainGraphPanel_MouseUp);
            // 
            // panelDirty
            // 
            this.panelDirty.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.panelDirty.BackColor = System.Drawing.Color.PaleTurquoise;
            this.panelDirty.Controls.Add(this.btnMakeNotDirty);
            this.panelDirty.Controls.Add(this.label3);
            this.panelDirty.Location = new System.Drawing.Point(307, 328);
            this.panelDirty.Name = "panelDirty";
            this.panelDirty.Size = new System.Drawing.Size(200, 25);
            this.panelDirty.TabIndex = 1;
            this.panelDirty.Visible = false;
            // 
            // btnMakeNotDirty
            // 
            this.btnMakeNotDirty.Location = new System.Drawing.Point(118, 1);
            this.btnMakeNotDirty.Name = "btnMakeNotDirty";
            this.btnMakeNotDirty.Size = new System.Drawing.Size(79, 23);
            this.btnMakeNotDirty.TabIndex = 1;
            this.btnMakeNotDirty.Text = "我未修改";
            this.btnMakeNotDirty.UseVisualStyleBackColor = true;
            this.btnMakeNotDirty.Click += new System.EventHandler(this.btnMakeNotDirty_Click);
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label3.ForeColor = System.Drawing.Color.Red;
            this.label3.Location = new System.Drawing.Point(14, 6);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(98, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "<<检测到修改>>";
            // 
            // btnHideRight
            // 
            this.btnHideRight.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnHideRight.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnHideRight.Location = new System.Drawing.Point(517, 0);
            this.btnHideRight.Name = "btnHideRight";
            this.btnHideRight.Size = new System.Drawing.Size(26, 21);
            this.btnHideRight.TabIndex = 0;
            this.btnHideRight.Text = "<<";
            this.btnHideRight.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnHideRight.UseVisualStyleBackColor = true;
            this.btnHideRight.Visible = false;
            this.btnHideRight.Click += new System.EventHandler(this.btnHideRight_Click);
            // 
            // panelRight
            // 
            this.panelRight.Controls.Add(this.splitResv);
            this.panelRight.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelRight.Location = new System.Drawing.Point(0, 0);
            this.panelRight.Name = "panelRight";
            this.panelRight.Size = new System.Drawing.Size(151, 402);
            this.panelRight.TabIndex = 2;
            // 
            // splitResv
            // 
            this.splitResv.BackColor = System.Drawing.SystemColors.GradientInactiveCaption;
            this.splitResv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitResv.Location = new System.Drawing.Point(0, 0);
            this.splitResv.Name = "splitResv";
            this.splitResv.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitResv.Panel1
            // 
            this.splitResv.Panel1.Controls.Add(this.panel1);
            // 
            // splitResv.Panel2
            // 
            this.splitResv.Panel2.Controls.Add(this.panel2);
            this.splitResv.Size = new System.Drawing.Size(151, 402);
            this.splitResv.SplitterDistance = 204;
            this.splitResv.TabIndex = 3;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.treeViewRsvValues);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(151, 204);
            this.panel1.TabIndex = 0;
            // 
            // treeViewRsvValues
            // 
            this.treeViewRsvValues.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewRsvValues.Location = new System.Drawing.Point(0, 15);
            this.treeViewRsvValues.Name = "treeViewRsvValues";
            this.treeViewRsvValues.ShowNodeToolTips = true;
            this.treeViewRsvValues.Size = new System.Drawing.Size(151, 189);
            this.treeViewRsvValues.TabIndex = 1;
            this.treeViewRsvValues.NodeMouseClick += new System.Windows.Forms.TreeNodeMouseClickEventHandler(this.treeViewRsvValues_NodeMouseClick);
            // 
            // label1
            // 
            this.label1.BackColor = System.Drawing.Color.LightCyan;
            this.label1.Dock = System.Windows.Forms.DockStyle.Top;
            this.label1.Location = new System.Drawing.Point(0, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(151, 15);
            this.label1.TabIndex = 2;
            this.label1.Text = "全局预存值列表：";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.treeViewInProcResv);
            this.panel2.Controls.Add(this.label2);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(0, 0);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(151, 194);
            this.panel2.TabIndex = 1;
            // 
            // treeViewInProcResv
            // 
            this.treeViewInProcResv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewInProcResv.Location = new System.Drawing.Point(0, 15);
            this.treeViewInProcResv.Name = "treeViewInProcResv";
            this.treeViewInProcResv.ShowNodeToolTips = true;
            this.treeViewInProcResv.Size = new System.Drawing.Size(151, 179);
            this.treeViewInProcResv.TabIndex = 1;
            this.treeViewInProcResv.NodeMouseClick += new System.Windows.Forms.TreeNodeMouseClickEventHandler(this.treeViewInProcResv_NodeMouseClick);
            // 
            // label2
            // 
            this.label2.BackColor = System.Drawing.Color.LightCyan;
            this.label2.Dock = System.Windows.Forms.DockStyle.Top;
            this.label2.Location = new System.Drawing.Point(0, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(151, 15);
            this.label2.TabIndex = 2;
            this.label2.Text = "流程预存值列表：";
            this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // mainToolBar
            // 
            this.mainToolBar.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.btnSave,
            this.btnSaveDirtyTo,
            this.btnPackToZip,
            this.toolStripSeparator1,
            this.btnModeAddConn,
            this.toolStripSeparator3,
            this.btnLarge,
            this.btnSmall,
            this.toolStripSeparator2,
            this.btnParamSetting,
            this.btnL3ParamSetting,
            this.toolStripSeparator4,
            this.btnContinueRun,
            this.miNextCursor,
            this.btnStepOver,
            this.bbtnStepInto,
            this.toolStripSeparator5,
            this.btnClearBrk,
            this.btnFindText});
            this.mainToolBar.Location = new System.Drawing.Point(0, 0);
            this.mainToolBar.Name = "mainToolBar";
            this.mainToolBar.RenderMode = System.Windows.Forms.ToolStripRenderMode.System;
            this.mainToolBar.Size = new System.Drawing.Size(830, 25);
            this.mainToolBar.TabIndex = 0;
            this.mainToolBar.Text = "toolStrip1";
            // 
            // btnSave
            // 
            this.btnSave.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnSave.Image = global::MasterCom.RAMS.Properties.Resources.save;
            this.btnSave.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(23, 22);
            this.btnSave.Text = "保存配置";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnSaveDirtyTo
            // 
            this.btnSaveDirtyTo.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnSaveDirtyTo.Image = global::MasterCom.RAMS.Properties.Resources.addrpt;
            this.btnSaveDirtyTo.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnSaveDirtyTo.Name = "btnSaveDirtyTo";
            this.btnSaveDirtyTo.Size = new System.Drawing.Size(23, 22);
            this.btnSaveDirtyTo.Text = "保存所有修改到...";
            this.btnSaveDirtyTo.Click += new System.EventHandler(this.btnSaveDirtyTo_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(6, 25);
            // 
            // btnModeAddConn
            // 
            this.btnModeAddConn.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnModeAddConn.Image = ((System.Drawing.Image)(resources.GetObject("btnModeAddConn.Image")));
            this.btnModeAddConn.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnModeAddConn.Name = "btnModeAddConn";
            this.btnModeAddConn.Size = new System.Drawing.Size(23, 22);
            this.btnModeAddConn.Text = "添加逻辑连线模式";
            this.btnModeAddConn.Click += new System.EventHandler(this.btnModeAddConn_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(6, 25);
            // 
            // btnLarge
            // 
            this.btnLarge.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnLarge.Image = ((System.Drawing.Image)(resources.GetObject("btnLarge.Image")));
            this.btnLarge.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnLarge.Name = "btnLarge";
            this.btnLarge.Size = new System.Drawing.Size(23, 22);
            this.btnLarge.Text = "放大";
            this.btnLarge.Click += new System.EventHandler(this.btnLarge_Click);
            // 
            // btnSmall
            // 
            this.btnSmall.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnSmall.Image = ((System.Drawing.Image)(resources.GetObject("btnSmall.Image")));
            this.btnSmall.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnSmall.Name = "btnSmall";
            this.btnSmall.Size = new System.Drawing.Size(23, 22);
            this.btnSmall.Text = "缩小";
            this.btnSmall.Click += new System.EventHandler(this.btnSmall_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(6, 25);
            // 
            // btnParamSetting
            // 
            this.btnParamSetting.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnParamSetting.Image = ((System.Drawing.Image)(resources.GetObject("btnParamSetting.Image")));
            this.btnParamSetting.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnParamSetting.Name = "btnParamSetting";
            this.btnParamSetting.Size = new System.Drawing.Size(23, 22);
            this.btnParamSetting.Text = "指标参数设置";
            this.btnParamSetting.Click += new System.EventHandler(this.btnParamSetting_Click);
            // 
            // btnL3ParamSetting
            // 
            this.btnL3ParamSetting.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnL3ParamSetting.Image = ((System.Drawing.Image)(resources.GetObject("btnL3ParamSetting.Image")));
            this.btnL3ParamSetting.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnL3ParamSetting.Name = "btnL3ParamSetting";
            this.btnL3ParamSetting.Size = new System.Drawing.Size(23, 22);
            this.btnL3ParamSetting.Text = "层三解码设置";
            this.btnL3ParamSetting.Click += new System.EventHandler(this.btnL3ParamSetting_Click);
            // 
            // toolStripSeparator4
            // 
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            this.toolStripSeparator4.Size = new System.Drawing.Size(6, 25);
            // 
            // btnContinueRun
            // 
            this.btnContinueRun.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnContinueRun.Image = ((System.Drawing.Image)(resources.GetObject("btnContinueRun.Image")));
            this.btnContinueRun.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnContinueRun.Name = "btnContinueRun";
            this.btnContinueRun.Size = new System.Drawing.Size(23, 22);
            this.btnContinueRun.Text = "运行到下一断点";
            this.btnContinueRun.Click += new System.EventHandler(this.btnContinueRun_Click);
            // 
            // miNextCursor
            // 
            this.miNextCursor.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.miNextCursor.Image = ((System.Drawing.Image)(resources.GetObject("miNextCursor.Image")));
            this.miNextCursor.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.miNextCursor.Name = "miNextCursor";
            this.miNextCursor.Size = new System.Drawing.Size(23, 22);
            this.miNextCursor.Text = "下一运行节点";
            this.miNextCursor.Click += new System.EventHandler(this.miNextCursor_Click);
            // 
            // btnStepOver
            // 
            this.btnStepOver.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnStepOver.Image = ((System.Drawing.Image)(resources.GetObject("btnStepOver.Image")));
            this.btnStepOver.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnStepOver.Name = "btnStepOver";
            this.btnStepOver.Size = new System.Drawing.Size(23, 22);
            this.btnStepOver.Text = "跳过(Step Over)";
            this.btnStepOver.Click += new System.EventHandler(this.btnStepOver_Click);
            // 
            // bbtnStepInto
            // 
            this.bbtnStepInto.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.bbtnStepInto.Image = ((System.Drawing.Image)(resources.GetObject("bbtnStepInto.Image")));
            this.bbtnStepInto.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.bbtnStepInto.Name = "bbtnStepInto";
            this.bbtnStepInto.Size = new System.Drawing.Size(23, 22);
            this.bbtnStepInto.Text = "单步执行(Step In)";
            this.bbtnStepInto.Click += new System.EventHandler(this.bbtnStepInto_Click);
            // 
            // toolStripSeparator5
            // 
            this.toolStripSeparator5.Name = "toolStripSeparator5";
            this.toolStripSeparator5.Size = new System.Drawing.Size(6, 25);
            // 
            // btnClearBrk
            // 
            this.btnClearBrk.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnClearBrk.Image = ((System.Drawing.Image)(resources.GetObject("btnClearBrk.Image")));
            this.btnClearBrk.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnClearBrk.Name = "btnClearBrk";
            this.btnClearBrk.Size = new System.Drawing.Size(23, 22);
            this.btnClearBrk.Text = "清除所有断点";
            this.btnClearBrk.Click += new System.EventHandler(this.btnClearBrk_Click);
            // 
            // btnFindText
            // 
            this.btnFindText.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnFindText.Image = ((System.Drawing.Image)(resources.GetObject("btnFindText.Image")));
            this.btnFindText.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnFindText.Name = "btnFindText";
            this.btnFindText.Size = new System.Drawing.Size(23, 22);
            this.btnFindText.Text = "查找关键字";
            this.btnFindText.Click += new System.EventHandler(this.btnFindText_Click);
            // 
            // ctxMenuNode
            // 
            this.ctxMenuNode.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.设置流程ToolStripMenuItem,
            this.插入下级流程节点ToolStripMenuItem,
            this.toolStripMenuItem2,
            this.miDelPath2Child,
            this.toolStripMenuItem3,
            this.miNodeCut,
            this.miNodeCopy,
            this.miNodePaste,
            this.miNodeDelete,
            this.toolStripMenuItem4,
            this.miBreakAt});
            this.ctxMenuNode.Name = "ctxMenuNode";
            this.ctxMenuNode.Size = new System.Drawing.Size(227, 198);
            // 
            // 设置流程ToolStripMenuItem
            // 
            this.设置流程ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miTypeCondition,
            this.miTypeOperation,
            this.miTypePreType,
            this.miTypeReason,
            this.miTypeSolved,
            this.miTypeOtherProc});
            this.设置流程ToolStripMenuItem.Name = "设置流程ToolStripMenuItem";
            this.设置流程ToolStripMenuItem.Size = new System.Drawing.Size(226, 22);
            this.设置流程ToolStripMenuItem.Text = "添加下层流程节点";
            // 
            // miTypeCondition
            // 
            this.miTypeCondition.Name = "miTypeCondition";
            this.miTypeCondition.Size = new System.Drawing.Size(142, 22);
            this.miTypeCondition.Text = "判定节点";
            this.miTypeCondition.Click += new System.EventHandler(this.miTypeCondition_Click);
            // 
            // miTypeOperation
            // 
            this.miTypeOperation.Name = "miTypeOperation";
            this.miTypeOperation.Size = new System.Drawing.Size(142, 22);
            this.miTypeOperation.Text = "操作节点";
            this.miTypeOperation.Click += new System.EventHandler(this.miTypeOperation_Click);
            // 
            // miTypePreType
            // 
            this.miTypePreType.Name = "miTypePreType";
            this.miTypePreType.Size = new System.Drawing.Size(142, 22);
            this.miTypePreType.Text = "预判定类型";
            this.miTypePreType.Click += new System.EventHandler(this.miTypePreType_Click);
            // 
            // miTypeReason
            // 
            this.miTypeReason.Name = "miTypeReason";
            this.miTypeReason.Size = new System.Drawing.Size(142, 22);
            this.miTypeReason.Text = "问题原因描述";
            this.miTypeReason.Click += new System.EventHandler(this.miTypeReason_Click);
            // 
            // miTypeSolved
            // 
            this.miTypeSolved.Name = "miTypeSolved";
            this.miTypeSolved.Size = new System.Drawing.Size(142, 22);
            this.miTypeSolved.Text = "解决方案描述";
            this.miTypeSolved.Click += new System.EventHandler(this.miTypeSolved_Click);
            // 
            // miTypeOtherProc
            // 
            this.miTypeOtherProc.Name = "miTypeOtherProc";
            this.miTypeOtherProc.Size = new System.Drawing.Size(142, 22);
            this.miTypeOtherProc.Text = "其它流程模块";
            this.miTypeOtherProc.Click += new System.EventHandler(this.miTypeOtherProc_Click);
            // 
            // 插入下级流程节点ToolStripMenuItem
            // 
            this.插入下级流程节点ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miInsCondition,
            this.miInsOperation,
            this.miInsPreType,
            this.miInsReason,
            this.miInsSolved,
            this.miInsOtherProc});
            this.插入下级流程节点ToolStripMenuItem.Name = "插入下级流程节点ToolStripMenuItem";
            this.插入下级流程节点ToolStripMenuItem.Size = new System.Drawing.Size(226, 22);
            this.插入下级流程节点ToolStripMenuItem.Text = "插入下级流程节点";
            // 
            // miInsCondition
            // 
            this.miInsCondition.Name = "miInsCondition";
            this.miInsCondition.Size = new System.Drawing.Size(142, 22);
            this.miInsCondition.Text = "判定节点";
            this.miInsCondition.Click += new System.EventHandler(this.miInsCondition_Click);
            // 
            // miInsOperation
            // 
            this.miInsOperation.Name = "miInsOperation";
            this.miInsOperation.Size = new System.Drawing.Size(142, 22);
            this.miInsOperation.Text = "操作节点";
            this.miInsOperation.Click += new System.EventHandler(this.miInsOperation_Click);
            // 
            // miInsPreType
            // 
            this.miInsPreType.Name = "miInsPreType";
            this.miInsPreType.Size = new System.Drawing.Size(142, 22);
            this.miInsPreType.Text = "预判定类型";
            this.miInsPreType.Click += new System.EventHandler(this.miInsPreType_Click);
            // 
            // miInsReason
            // 
            this.miInsReason.Name = "miInsReason";
            this.miInsReason.Size = new System.Drawing.Size(142, 22);
            this.miInsReason.Text = "问题原因描述";
            this.miInsReason.Click += new System.EventHandler(this.miInsReason_Click);
            // 
            // miInsSolved
            // 
            this.miInsSolved.Name = "miInsSolved";
            this.miInsSolved.Size = new System.Drawing.Size(142, 22);
            this.miInsSolved.Text = "解决方案描述";
            this.miInsSolved.Click += new System.EventHandler(this.miInsSolved_Click);
            // 
            // miInsOtherProc
            // 
            this.miInsOtherProc.Name = "miInsOtherProc";
            this.miInsOtherProc.Size = new System.Drawing.Size(142, 22);
            this.miInsOtherProc.Text = "其它流程模块";
            this.miInsOtherProc.Click += new System.EventHandler(this.miInsOtherProc_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(223, 6);
            // 
            // miDelPath2Child
            // 
            this.miDelPath2Child.Name = "miDelPath2Child";
            this.miDelPath2Child.Size = new System.Drawing.Size(226, 22);
            this.miDelPath2Child.Text = "删除该节点与其子结点的连接";
            this.miDelPath2Child.Click += new System.EventHandler(this.miDelPath2Child_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(223, 6);
            // 
            // miNodeCut
            // 
            this.miNodeCut.Name = "miNodeCut";
            this.miNodeCut.Size = new System.Drawing.Size(226, 22);
            this.miNodeCut.Text = "剪切";
            this.miNodeCut.Click += new System.EventHandler(this.miNodeCut_Click);
            // 
            // miNodeCopy
            // 
            this.miNodeCopy.Name = "miNodeCopy";
            this.miNodeCopy.Size = new System.Drawing.Size(226, 22);
            this.miNodeCopy.Text = "复制";
            this.miNodeCopy.Click += new System.EventHandler(this.miNodeCopy_Click);
            // 
            // miNodePaste
            // 
            this.miNodePaste.Name = "miNodePaste";
            this.miNodePaste.Size = new System.Drawing.Size(226, 22);
            this.miNodePaste.Text = "粘贴";
            this.miNodePaste.Click += new System.EventHandler(this.miNodePaste_Click);
            // 
            // miNodeDelete
            // 
            this.miNodeDelete.Name = "miNodeDelete";
            this.miNodeDelete.Size = new System.Drawing.Size(226, 22);
            this.miNodeDelete.Text = "删除";
            this.miNodeDelete.Click += new System.EventHandler(this.miNodeDelete_Click);
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(223, 6);
            // 
            // miBreakAt
            // 
            this.miBreakAt.Name = "miBreakAt";
            this.miBreakAt.Size = new System.Drawing.Size(226, 22);
            this.miBreakAt.Text = "设置/取消断点";
            this.miBreakAt.Click += new System.EventHandler(this.miBreakAt_Click);
            // 
            // ctxMenuResv
            // 
            this.ctxMenuResv.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miAddResvItem,
            this.miDelResvItem});
            this.ctxMenuResv.Name = "ctxMenuResv";
            this.ctxMenuResv.Size = new System.Drawing.Size(131, 48);
            // 
            // miAddResvItem
            // 
            this.miAddResvItem.Name = "miAddResvItem";
            this.miAddResvItem.Size = new System.Drawing.Size(130, 22);
            this.miAddResvItem.Text = "添加预存值";
            this.miAddResvItem.Click += new System.EventHandler(this.miAddResvItem_Click);
            // 
            // miDelResvItem
            // 
            this.miDelResvItem.Name = "miDelResvItem";
            this.miDelResvItem.Size = new System.Drawing.Size(130, 22);
            this.miDelResvItem.Text = "删除预存值";
            this.miDelResvItem.Click += new System.EventHandler(this.miDelResvItem_Click);
            // 
            // ctxMenuRoutine
            // 
            this.ctxMenuRoutine.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miAddRoutine,
            this.miDelRoutine,
            this.toolStripMenuItem1,
            this.miAddGroup,
            this.miDelGroup});
            this.ctxMenuRoutine.Name = "ctxMenuRoutine";
            this.ctxMenuRoutine.Size = new System.Drawing.Size(131, 98);
            // 
            // miAddRoutine
            // 
            this.miAddRoutine.Name = "miAddRoutine";
            this.miAddRoutine.Size = new System.Drawing.Size(130, 22);
            this.miAddRoutine.Text = "添加新流程";
            this.miAddRoutine.Click += new System.EventHandler(this.miAddRoutine_Click);
            // 
            // miDelRoutine
            // 
            this.miDelRoutine.Name = "miDelRoutine";
            this.miDelRoutine.Size = new System.Drawing.Size(130, 22);
            this.miDelRoutine.Text = "删除流程";
            this.miDelRoutine.Click += new System.EventHandler(this.miDelRoutine_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(127, 6);
            // 
            // miAddGroup
            // 
            this.miAddGroup.Name = "miAddGroup";
            this.miAddGroup.Size = new System.Drawing.Size(130, 22);
            this.miAddGroup.Text = "添加组";
            this.miAddGroup.Click += new System.EventHandler(this.miAddGroup_Click);
            // 
            // miDelGroup
            // 
            this.miDelGroup.Name = "miDelGroup";
            this.miDelGroup.Size = new System.Drawing.Size(130, 22);
            this.miDelGroup.Text = "删除组";
            this.miDelGroup.Click += new System.EventHandler(this.miDelGroup_Click);
            // 
            // btnPackToZip
            // 
            this.btnPackToZip.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnPackToZip.Image = ((System.Drawing.Image)(resources.GetObject("btnPackToZip.Image")));
            this.btnPackToZip.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnPackToZip.Name = "btnPackToZip";
            this.btnPackToZip.Size = new System.Drawing.Size(23, 22);
            this.btnPackToZip.Text = "打包至压缩文件";
            this.btnPackToZip.Click += new System.EventHandler(this.btnPackToZip_Click);
            // 
            // ESProcGraphForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(830, 427);
            this.Controls.Add(this.splitContainerMain);
            this.Controls.Add(this.mainToolBar);
            this.Name = "ESProcGraphForm";
            this.Text = "自动分析逻辑设置";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.Shown += new System.EventHandler(this.ESProcGraphForm_Shown);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.ESProcGraphForm_FormClosing);
            this.splitContainerMain.Panel1.ResumeLayout(false);
            this.splitContainerMain.Panel2.ResumeLayout(false);
            this.splitContainerMain.ResumeLayout(false);
            this.panelLeft.ResumeLayout(false);
            this.splitTaskPane.Panel1.ResumeLayout(false);
            this.splitTaskPane.Panel2.ResumeLayout(false);
            this.splitTaskPane.ResumeLayout(false);
            this.splitMain.Panel1.ResumeLayout(false);
            this.splitMain.Panel2.ResumeLayout(false);
            this.splitMain.ResumeLayout(false);
            this.mainGraphPanel.ResumeLayout(false);
            this.panelDirty.ResumeLayout(false);
            this.panelDirty.PerformLayout();
            this.panelRight.ResumeLayout(false);
            this.splitResv.Panel1.ResumeLayout(false);
            this.splitResv.Panel2.ResumeLayout(false);
            this.splitResv.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.mainToolBar.ResumeLayout(false);
            this.mainToolBar.PerformLayout();
            this.ctxMenuNode.ResumeLayout(false);
            this.ctxMenuResv.ResumeLayout(false);
            this.ctxMenuRoutine.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.SplitContainer splitContainerMain;
        private System.Windows.Forms.Panel panelLeft;
        private System.Windows.Forms.ToolStrip mainToolBar;
        private System.Windows.Forms.TreeView treeViewRoutines;
        private System.Windows.Forms.Panel mainGraphPanel;
        private System.Windows.Forms.ToolStripButton btnSave;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ContextMenuStrip ctxMenuNode;
        private System.Windows.Forms.ToolStripMenuItem 设置流程ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem miTypeCondition;
        private System.Windows.Forms.ToolStripMenuItem miTypeOperation;
        private System.Windows.Forms.ToolStripMenuItem miTypePreType;
        private System.Windows.Forms.ToolStripMenuItem miTypeReason;
        private System.Windows.Forms.ToolStripMenuItem miTypeSolved;
        private System.Windows.Forms.SplitContainer splitTaskPane;
        private System.Windows.Forms.PropertyGrid propGrid;
        private System.Windows.Forms.ToolStripMenuItem miNodeDelete;
        private System.Windows.Forms.ToolStripButton btnModeAddConn;
        private System.Windows.Forms.SplitContainer splitMain;
        private System.Windows.Forms.TreeView treeViewRsvValues;
        private System.Windows.Forms.Panel panelRight;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ContextMenuStrip ctxMenuResv;
        private System.Windows.Forms.ToolStripMenuItem miAddResvItem;
        private System.Windows.Forms.ToolStripMenuItem miDelResvItem;
        private System.Windows.Forms.ContextMenuStrip ctxMenuRoutine;
        private System.Windows.Forms.ToolStripMenuItem miDelGroup;
        private System.Windows.Forms.ToolStripMenuItem miAddGroup;
        private System.Windows.Forms.ToolStripMenuItem miAddRoutine;
        private System.Windows.Forms.ToolStripMenuItem miDelRoutine;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem miNodeCut;
        private System.Windows.Forms.ToolStripMenuItem miNodeCopy;
        private System.Windows.Forms.ToolStripMenuItem miNodePaste;
        private System.Windows.Forms.ToolStripMenuItem 插入下级流程节点ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem miInsCondition;
        private System.Windows.Forms.ToolStripMenuItem miInsOperation;
        private System.Windows.Forms.ToolStripMenuItem miInsPreType;
        private System.Windows.Forms.ToolStripMenuItem miInsReason;
        private System.Windows.Forms.ToolStripMenuItem miInsSolved;
        private System.Windows.Forms.ToolStripMenuItem miDelPath2Child;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem3;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripButton btnParamSetting;
        private System.Windows.Forms.ToolStripButton btnL3ParamSetting;
        private System.Windows.Forms.SplitContainer splitResv;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TreeView treeViewInProcResv;
        private System.Windows.Forms.Button btnHideRight;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripButton btnLarge;
        private System.Windows.Forms.ToolStripButton btnSmall;
        private System.Windows.Forms.ToolStripMenuItem miTypeOtherProc;
        private System.Windows.Forms.ToolStripMenuItem miInsOtherProc;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem4;
        private System.Windows.Forms.ToolStripMenuItem miBreakAt;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator4;
        private System.Windows.Forms.ToolStripButton btnContinueRun;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator5;
        private System.Windows.Forms.ToolStripButton miNextCursor;
        private System.Windows.Forms.ToolStripButton btnStepOver;
        private System.Windows.Forms.ToolStripButton bbtnStepInto;
        private System.Windows.Forms.ToolStripButton btnClearBrk;
        private System.Windows.Forms.ToolStripButton btnFindText;
        private System.Windows.Forms.Panel panelDirty;
        private System.Windows.Forms.Button btnMakeNotDirty;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ToolStripButton btnSaveDirtyTo;
        private System.Windows.Forms.ToolStripButton btnPackToZip;
    }
}