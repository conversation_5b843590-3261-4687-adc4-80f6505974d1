﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CellMultiCoverageForm_TD
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CellMultiCoverageForm_TD));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportBrief = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelControlType = new DevExpress.XtraEditors.LabelControl();
            this.btnColorSetting = new DevExpress.XtraEditors.SimpleButton();
            this.radioGroupType = new DevExpress.XtraEditors.RadioGroup();
            this.listViewTotal_TD = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCPI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbsLevel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbsConditionSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRelLevel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRelConditionSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherCellSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherCellRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherCellDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMulLevel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMulConditionSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceCov = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal_TD)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportBrief,
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(178, 120);
            // 
            // miExportBrief
            // 
            this.miExportBrief.Name = "miExportBrief";
            this.miExportBrief.Size = new System.Drawing.Size(177, 22);
            this.miExportBrief.Text = "导出简要信息Excel";
            this.miExportBrief.Click += new System.EventHandler(this.miExportBrief_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(177, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(174, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(177, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(177, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.groupControl1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1163, 73);
            this.panel1.TabIndex = 1;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.labelControlType);
            this.groupControl1.Controls.Add(this.btnColorSetting);
            this.groupControl1.Controls.Add(this.radioGroupType);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1163, 73);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "GIS显示设置";
            // 
            // labelControlType
            // 
            this.labelControlType.Location = new System.Drawing.Point(14, 37);
            this.labelControlType.Name = "labelControlType";
            this.labelControlType.Size = new System.Drawing.Size(72, 14);
            this.labelControlType.TabIndex = 2;
            this.labelControlType.Text = "覆盖度类别：";
            // 
            // btnColorSetting
            // 
            this.btnColorSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnColorSetting.Location = new System.Drawing.Point(1070, 34);
            this.btnColorSetting.Name = "btnColorSetting";
            this.btnColorSetting.Size = new System.Drawing.Size(87, 27);
            this.btnColorSetting.TabIndex = 1;
            this.btnColorSetting.Text = "着色设置...";
            this.btnColorSetting.Click += new System.EventHandler(this.btnColorSetting_Click);
            // 
            // radioGroupType
            // 
            this.radioGroupType.EditValue = false;
            this.radioGroupType.Location = new System.Drawing.Point(92, 28);
            this.radioGroupType.Name = "radioGroupType";
            this.radioGroupType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "绝对覆盖度( > -80dBm)"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "相对覆盖度(-12dB内)"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "综合覆盖度( -12dB内 并且 > -80dBm)")});
            this.radioGroupType.Size = new System.Drawing.Size(707, 33);
            this.radioGroupType.TabIndex = 1;
            this.radioGroupType.SelectedIndexChanged += new System.EventHandler(this.radioGroupType_SelectedIndexChanged);
            // 
            // listViewTotal_TD
            // 
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnSN);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnCellName);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnLAC);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnCI);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnARFCN);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnCPI);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnSampleCount);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnAbsLevel);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnAbsConditionSampleCount);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnRelLevel);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnRelConditionSampleCount);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnOtherCellName);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnOtherCellSample);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnOtherCellRscp);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnOtherCellDistance);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnMulLevel);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnMulConditionSampleCount);
            this.listViewTotal_TD.AllColumns.Add(this.olvColumnDistanceCov);
            this.listViewTotal_TD.BackColor = System.Drawing.SystemColors.Window;
            this.listViewTotal_TD.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCellName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnARFCN,
            this.olvColumnCPI,
            this.olvColumnSampleCount,
            this.olvColumnAbsLevel,
            this.olvColumnAbsConditionSampleCount,
            this.olvColumnRelLevel,
            this.olvColumnRelConditionSampleCount,
            this.olvColumnOtherCellName,
            this.olvColumnOtherCellSample,
            this.olvColumnOtherCellRscp,
            this.olvColumnOtherCellDistance,
            this.olvColumnMulLevel,
            this.olvColumnMulConditionSampleCount,
            this.olvColumnDistanceCov});
            this.listViewTotal_TD.ContextMenuStrip = this.ctxMenu;
            this.listViewTotal_TD.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal_TD.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal_TD.FullRowSelect = true;
            this.listViewTotal_TD.GridLines = true;
            this.listViewTotal_TD.HeaderWordWrap = true;
            this.listViewTotal_TD.IsNeedShowOverlay = false;
            this.listViewTotal_TD.Location = new System.Drawing.Point(0, 73);
            this.listViewTotal_TD.Name = "listViewTotal_TD";
            this.listViewTotal_TD.OwnerDraw = true;
            this.listViewTotal_TD.ShowGroups = false;
            this.listViewTotal_TD.Size = new System.Drawing.Size(1163, 429);
            this.listViewTotal_TD.TabIndex = 5;
            this.listViewTotal_TD.UseCompatibleStateImageBehavior = false;
            this.listViewTotal_TD.View = System.Windows.Forms.View.Details;
            this.listViewTotal_TD.VirtualMode = true;
            this.listViewTotal_TD.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 50;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 80;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "小区LAC";
            this.olvColumnLAC.Width = 80;
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "小区CI";
            this.olvColumnCI.Width = 80;
            // 
            // olvColumnARFCN
            // 
            this.olvColumnARFCN.HeaderFont = null;
            this.olvColumnARFCN.Text = "小区ARFCN";
            // 
            // olvColumnCPI
            // 
            this.olvColumnCPI.HeaderFont = null;
            this.olvColumnCPI.Text = "小区CPI";
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "最强采样点数";
            // 
            // olvColumnAbsLevel
            // 
            this.olvColumnAbsLevel.HeaderFont = null;
            this.olvColumnAbsLevel.Text = "绝对重叠覆盖度";
            // 
            // olvColumnAbsConditionSampleCount
            // 
            this.olvColumnAbsConditionSampleCount.HeaderFont = null;
            this.olvColumnAbsConditionSampleCount.Text = "绝对条件样本点数";
            // 
            // olvColumnRelLevel
            // 
            this.olvColumnRelLevel.HeaderFont = null;
            this.olvColumnRelLevel.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnRelLevel.Text = "相对重叠覆盖度";
            // 
            // olvColumnRelConditionSampleCount
            // 
            this.olvColumnRelConditionSampleCount.HeaderFont = null;
            this.olvColumnRelConditionSampleCount.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnRelConditionSampleCount.Text = "相对条件样本点数";
            // 
            // olvColumnOtherCellName
            // 
            this.olvColumnOtherCellName.HeaderFont = null;
            this.olvColumnOtherCellName.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnOtherCellName.Text = "相对覆盖带内小区名称";
            this.olvColumnOtherCellName.Width = 100;
            // 
            // olvColumnOtherCellSample
            // 
            this.olvColumnOtherCellSample.HeaderFont = null;
            this.olvColumnOtherCellSample.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnOtherCellSample.Text = "进入覆盖带采样点数";
            // 
            // olvColumnOtherCellRscp
            // 
            this.olvColumnOtherCellRscp.HeaderFont = null;
            this.olvColumnOtherCellRscp.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnOtherCellRscp.Text = "平均信号强度";
            // 
            // olvColumnOtherCellDistance
            // 
            this.olvColumnOtherCellDistance.HeaderFont = null;
            this.olvColumnOtherCellDistance.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnOtherCellDistance.Text = "与主服小区距离(米)";
            this.olvColumnOtherCellDistance.Width = 80;
            // 
            // olvColumnMulLevel
            // 
            this.olvColumnMulLevel.HeaderFont = null;
            this.olvColumnMulLevel.Text = "综合重叠覆盖度";
            // 
            // olvColumnMulConditionSampleCount
            // 
            this.olvColumnMulConditionSampleCount.HeaderFont = null;
            this.olvColumnMulConditionSampleCount.Text = "综合条件样本点数";
            // 
            // olvColumnDistanceCov
            // 
            this.olvColumnDistanceCov.HeaderFont = null;
            this.olvColumnDistanceCov.Text = "覆盖距离(米)";
            this.olvColumnDistanceCov.Width = 80;
            // 
            // CellMultiCoverageForm_TD
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1163, 502);
            this.Controls.Add(this.listViewTotal_TD);
            this.Controls.Add(this.panel1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "CellMultiCoverageForm_TD";
            this.Text = "TD小区重叠覆盖";
            this.ctxMenu.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal_TD)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private System.Windows.Forms.Panel panel1;
        private BrightIdeasSoftware.TreeListView listViewTotal_TD;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceCov;
        private BrightIdeasSoftware.OLVColumn olvColumnRelLevel;
        private BrightIdeasSoftware.OLVColumn olvColumnAbsLevel;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LabelControl labelControlType;
        private DevExpress.XtraEditors.SimpleButton btnColorSetting;
        private DevExpress.XtraEditors.RadioGroup radioGroupType;
        private BrightIdeasSoftware.OLVColumn olvColumnMulLevel;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnCPI;
        private BrightIdeasSoftware.OLVColumn olvColumnAbsConditionSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnRelConditionSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnMulConditionSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherCellSample;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherCellRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherCellDistance;
        private System.Windows.Forms.ToolStripMenuItem miExportBrief;

    }
}