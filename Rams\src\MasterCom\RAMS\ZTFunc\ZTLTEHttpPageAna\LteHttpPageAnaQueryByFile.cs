﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    class LteHttpPageAnaQueryByFile : LteHttpPageAnaBase
    {
        public LteHttpPageAnaQueryByFile(MainModel mm)
            : base(mm)
        {

        }
        private static LteHttpPageAnaQueryByFile instance = null;
        protected static readonly object lockObj = new object();
        public static LteHttpPageAnaQueryByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteHttpPageAnaQueryByFile(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "Http浏览分析(按文件)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22103, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidPeriod(List<TestPoint> tpsList)
        {
            return true;
        }
    }

    class LteFddHttpPageAnaQueryByFile : LteHttpPageAnaQueryByFile
    {
        public LteFddHttpPageAnaQueryByFile(MainModel mainModel)
            : base(mainModel)
        {

        }

        protected override void setParmAndServiceType()
        {
            this.RsrpStr = "lte_fdd_RSRP";
            this.SinrStr = "lte_fdd_SINR";
            this.NCellRsrpStr = "lte_fdd_NCell_RSRP";
            this.AppSpeedStr = "lte_fdd_APP_Speed_Mb";
            //this.AppSpeedStr = "lte_fdd_APP_ThroughputDL_Mb";
            this.TacStr = "lte_fdd_TAC";

            this.Columns = new List<string>();
            Columns.Add(RsrpStr);
            Columns.Add(SinrStr);
            Columns.Add(TacStr);
            Columns.Add(NCellRsrpStr);
            Columns.Add(AppSpeedStr);
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_APP_Speed");
            Columns.Add("lte_fdd_APP_type");
            Columns.Add("lte_fdd_Transmission_Mode");
            Columns.Add("lte_fdd_PDSCH_BLER");
            Columns.Add("lte_fdd_Rank_Indicator");
            Columns.Add("lte_fdd_PDCCH_DL_Grant_Count");
            Columns.Add("lte_fdd_PDSCH_PRb_Num_slot");
            Columns.Add("lte_fdd_NCell_SINR");

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
        }

        private static LteFddHttpPageAnaQueryByFile instance = null;
        public new static LteFddHttpPageAnaQueryByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteFddHttpPageAnaQueryByFile(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public override string Name
        {
            get { return "Http浏览LTE_FDD分析(按文件)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26077, this.Name);
        }
    }
}

