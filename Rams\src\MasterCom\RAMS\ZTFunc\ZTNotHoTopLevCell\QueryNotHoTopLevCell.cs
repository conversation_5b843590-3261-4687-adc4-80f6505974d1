﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTNotHoTopLevCell;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryNotHoTopLevCell : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static QueryNotHoTopLevCell instance = null;
        public static QueryNotHoTopLevCell GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QueryNotHoTopLevCell();
                    }
                }
            }
            return instance;
        }

        protected QueryNotHoTopLevCell()
            : base(MainModel.GetInstance())
        {
            this.IncludeEvent = true;
        }

        public override string Name
        {
            get
            {
                return "未切换到最强邻区统计(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12084, this.Name);
        }

        protected override void fireShowForm()
        {
            NotHoTopLevCellListForm form = this.MainModel.GetObjectFromBlackboard(typeof(NotHoTopLevCellListForm)) as NotHoTopLevCellListForm;
            if (form == null||form.IsDisposed)
            {
                form = new NotHoTopLevCellListForm();
                form.Owner = MainModel.MainForm;
            }
            form.FillData(resultSet);
            form.Visible = true;
            form.BringToFront();
            resultSet = null;
        }

        protected override bool getCondition()
        {
            resultSet = new List<NotHoTopLevCellInfo>();
            return true;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                dealDTDatas(file);
            }
        }

        private void dealDTDatas(DTFileDataManager file)
        {
            for (int i = 0; i < file.DTDatas.Count; i++)
            {
                Event evt = file.DTDatas[i] as Event;
                if (evt == null || evt.ID != 17 || !isDataInRegion(evt.Longitude, evt.Latitude))
                {
                    continue;
                }
                TestPoint preTp = getPreTp(file, i, evt);
                if (preTp == null)
                {
                    continue;
                }
                TestPoint sufTp = getSufTp(file, i, evt);
                if (sufTp == null)
                {
                    continue;
                }

                ICell orgCell = preTp.GetMainCell();
                float? orgLev = getMainSignalLev(preTp);
                if (orgCell == null)
                {
                    continue;
                }
                ICell tarCell = sufTp.GetMainCell();
                float? tarLev = getMainSignalLev(sufTp);
                if (tarCell == null || tarCell.Name == orgCell.Name)
                {
                    continue;
                }
                dealTop(evt, preTp, sufTp, orgCell, orgLev, tarCell, tarLev);
            }
        }

        private TestPoint getPreTp(DTFileDataManager file, int i, Event evt)
        {
            TestPoint preTp = null;
            for (int j = i - 1; j >= 0; j--)
            {//获取切换前采样点
                TestPoint tp = file.DTDatas[j] as TestPoint;
                if (tp == null || tp.Time > evt.Time)
                {
                    continue;
                }
                preTp = tp;
                break;
            }

            return preTp;
        }

        private TestPoint getSufTp(DTFileDataManager file, int i, Event evt)
        {
            TestPoint sufTp = null;
            for (int j = i + 1; j < file.DTDatas.Count; j++)
            {//获取切换后采样点
                TestPoint tp = file.DTDatas[j] as TestPoint;
                if (tp == null || tp.Time < evt.Time)
                {
                    continue;
                }
                sufTp = tp;
                break;
            }

            return sufTp;
        }

        private void dealTop(Event evt, TestPoint preTp, TestPoint sufTp, ICell orgCell, float? orgLev, ICell tarCell, float? tarLev)
        {
            float topLev = float.MinValue;
            int topIdx = -1;
            for (int x = 0; x < 10; x++)
            {
                float? nRxLev = getNbSignalLev(preTp, x);
                if (nRxLev == null)
                {
                    break;
                }
                if (nRxLev > topLev)
                {
                    topLev = (float)nRxLev;
                    topIdx = x;
                }
            }

            if (topIdx != -1)
            {
                ICell topNCell = preTp.GetNBCell(topIdx);
                if (topNCell != null && topNCell.Name != tarCell.Name)
                {
                    NotHoTopLevCellInfo info = new NotHoTopLevCellInfo(orgCell, (float)orgLev
                        , tarCell, (float)tarLev, topNCell, topLev);
                    info.HoSuccessEvt = evt;
                    info.TestPoints.Add(preTp);
                    info.TestPoints.Add(sufTp);
                    resultSet.Add(info);
                }
            }
        }

        protected virtual float? getMainSignalLev(TestPoint tp)
        {
            return (float?)(short?)tp["RxLevSub"];
        }

        protected virtual float? getNbSignalLev(TestPoint tp, int idx)
        {
            return (float?)(short?)tp["N_RxLev", idx];
        }

        protected List<NotHoTopLevCellInfo> resultSet = null;

        protected virtual bool isDataInRegion(double lng, double lat)
        {
            if (Condition.Geometorys == null
                || Condition.Geometorys.GeoOp.Contains(lng, lat))
            {
                return true;
            }
            return false;
        }

    }
}
