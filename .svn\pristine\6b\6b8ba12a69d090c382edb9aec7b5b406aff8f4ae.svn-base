﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class NRUrlAnalyzerByRegion : ZTUrlAnalyzer
    {
        public List<NRUrlAnaInfo> ResultsRegion = null;
        Dictionary<string, List<ResvRegion>> resvRegionsDic = null;
        protected Dictionary<string, List<Event>> dicBroRegion = null;
        protected Dictionary<string, List<Event>> dicDownRegion = null;
        protected Dictionary<string, List<Event>> dicVideoRegion = null;

        public void Analyze(List<DTFileDataManager> fileManagers)
        {
            ResultsRegion = new List<NRUrlAnaInfo>();
            dicBroRegion = new Dictionary<string, List<Event>>();
            dicDownRegion = new Dictionary<string, List<Event>>();
            dicVideoRegion = new Dictionary<string, List<Event>>();
            foreach (DTFileDataManager dtFile in fileManagers)
            {
                analyze(dtFile);
            }

            foreach (var kvp in resvRegionsDic)
            {
                foreach (var res in kvp.Value)
                {
                    NRUrlAnaInfoByRegion dtlRegion = new NRUrlAnaInfoByRegion(kvp.Key, res.RegionName);
                    Dictionary<string, List<Event>> dicBroTemp = dealRegion(kvp.Key, res, dicBroRegion);
                    var dtlBros = HttpAnalyze(dicBroTemp);
                    dtlRegion.Bros = dtlBros;

                    Dictionary<string, List<Event>> dicDownTemp = dealRegion(kvp.Key, res, dicDownRegion);
                    var dtlDowns = DownAnalyze(dicDownTemp);
                    dtlRegion.Downs = dtlDowns;

                    Dictionary<string, List<Event>> dicVideoTemp = dealRegion(kvp.Key, res, dicVideoRegion);
                    var dtlVideos = VideoAnalyze(dicVideoTemp);
                    dtlRegion.Videos = dtlVideos;

                    ResultsRegion.Add(dtlRegion);
                }
            }
            setSN(ResultsRegion);
        }

        public Dictionary<string, List<Event>> dealRegion(string kvpKey, ResvRegion res, Dictionary<string, List<Event>> dicRegion)
        {
            Dictionary<string, List<Event>> dicTemp = new Dictionary<string, List<Event>>();
            foreach (string key in dicRegion.Keys)
            {
                string regionName = getRegion(key);
                string gridName = getGrid(key);
                string URL = getURL(key);
                if (kvpKey.Equals(regionName) && res.RegionName.Equals(gridName))
                {
                    if (dicTemp.ContainsKey(URL))
                    {
                        dicTemp[URL].AddRange(dicRegion[key]);
                    }
                    else
                    {
                        dicTemp.Add(URL, dicRegion[key]);
                    }
                }
            }
            return dicTemp;
        }

        protected string getRegion(string regionAndgrid)
        {
            if (regionAndgrid == "")
            {
                return "";
            }
            int index = regionAndgrid.IndexOf("@^&");
            string s = regionAndgrid;
            return s.Remove(index);
        }

        protected string getGrid(string regionAndgrid)
        {
            if (regionAndgrid == "")
            {
                return "";
            }
            int indexStart = regionAndgrid.IndexOf("@^&") + 3;
            int indexEnd = regionAndgrid.IndexOf("&^@");
            int length = indexEnd - indexStart;
            string s = regionAndgrid.Substring(indexStart, length);

            return s;
        }

        protected string getURL(string regionAndgrid)
        {
            if (regionAndgrid == "")
            {
                return "";
            }
            int index = regionAndgrid.IndexOf("&^@") + 3;
            string s = regionAndgrid.Substring(index);
            return s;
        }

        protected void analyze(DTFileDataManager dtFile)
        {
            anaRegionEvts(dtFile, (int)NREventManager.HttpRequest, HttpIDList, dicBroRegion);
            anaRegionEvts(dtFile, (int)NREventManager.DownRequest, DownloadIDList, dicDownRegion);
            anaRegionEvts(dtFile, (int)NREventManager.VideoRequest, VideoIDList, dicVideoRegion);
        }

        private void anaRegionEvts(DTFileDataManager dtFile, int requestEvtID, List<int> evtList, Dictionary<string, List<Event>> dicRegion)
        {
            string url = "";
            Event evtBeforRequest = null;
            List<Event> evtTemps = new List<Event>();
            foreach (Event evt in dtFile.Events)
            {
                if (evtList.Contains(evt.ID))
                {
                    dealRegionEvt(evt, dtFile, ref url, ref evtBeforRequest, ref evtTemps, requestEvtID, dicRegion);
                }
            }

            addRegionEvt(evtBeforRequest, url, evtTemps, requestEvtID, dicRegion);
        }

        private void dealRegionEvt(Event evt, DTFileDataManager dtFile, ref string url, ref Event tarEvt, ref List<Event> evtTemps, int requestEvtID, Dictionary<string, List<Event>> dicRegion)
        {
            if (evt.ID != requestEvtID)
            {
                evtTemps.Add(evt);
                tarEvt = evt;
                return;
            }

            addRegionEvt(tarEvt, url, evtTemps, requestEvtID, dicRegion);
            evtTemps = new List<Event>();
            url = GetURL(evt.SN, dtFile.Messages);
            evtTemps.Add(evt);
        }

        private void addRegionEvt(Event tarEvt, string url, List<Event> evtTemps, int requestEvtID, Dictionary<string, List<Event>> dicRegion)
        {
            if (tarEvt == null)
            {
                return;
            }
            string resvRegions = CheckEvtInRegion(tarEvt);
            if (resvRegions != null)
            {
                resvRegions = $"{resvRegions}&^@{url}";
                if (evtTemps[0].ID == requestEvtID)
                {
                    if (dicRegion.ContainsKey(resvRegions))
                    {
                        dicRegion[resvRegions].AddRange(evtTemps);
                    }
                    else
                    {
                        dicRegion.Add(resvRegions, evtTemps);
                    }
                }
            }
        }

        protected string CheckEvtInRegion(Event evt)
        {
            double x = evt.Longitude;
            double y = evt.Latitude;
            foreach (KeyValuePair<string, List<ResvRegion>> kvp in resvRegionsDic)
            {
                foreach (ResvRegion res in kvp.Value)
                {
                    if (res.GeoOp.CheckPointInRegion(x, y))
                    {
                        string sRegionAndGrid = kvp.Key + "@^&" + res.RegionName;
                        return sRegionAndGrid;
                    }
                }
            }
            return null;
        }

        public void SetResvRegion(Dictionary<string, List<ResvRegion>> resv)
        {
            this.resvRegionsDic = new Dictionary<string, List<ResvRegion>>();
            this.resvRegionsDic = resv;
        }
    }
}
