﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRSinrQueryBase : DIYAnalyseFilesOneByOneByRegion
    {
        protected static readonly object lockObj = new object();
        protected NRSinrAnalyzer analyzer;
        protected List<DTFileDataManager> fileManagers = null;
        protected NRSinrConditionDlg setForm = null;

        public NRSinrQueryBase()
          : base(MainModel.GetInstance())
        {
            this.IncludeEvent = false;
            analyzer = new NRSinrAnalyzer();
        }

        protected override void getReadyBeforeQuery()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);

            analyzer.Refreshs();
            if (fileManagers == null)
            {
                fileManagers = new List<DTFileDataManager>();
            }
            else
            {
                fileManagers.Clear();
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35033, this.Name);
        }

        protected override bool getCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new NRSinrConditionDlg();
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }

            analyzer.SinrCond = setForm.GetCondition();
            return true;
        }

        protected override void doStatWithQuery()
        {
            if (MainModel.DTDataManager.FileDataManagers.Count > 0)
            {
                DTFileDataManager f = MainModel.DTDataManager.FileDataManagers[0];
                List<TestPoint> tps = new List<TestPoint>();
                foreach (TestPoint tp in f.TestPoints)
                {
                    float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
                    if (sinr != null)
                    {
                        tps.Add(tp);
                    }
                }
                analyzer.Analyze(tps, f.CarrierType);
            }
        }

        protected override void getResultsAfterQuery()
        {
            //analyzer.Analyze(fileManagers);
        }

        protected override void fireShowForm()
        {
            NRSinrResultForm resultForm = MainModel.CreateResultForm(typeof(NRSinrResultForm)) as NRSinrResultForm;
            resultForm.FillData(analyzer.GetResult());
            resultForm.Visible = true;
            resultForm.BringToFront();
        }
    }

    public class NRSinrQueryByFile : NRSinrQueryBase
    {
        private static NRSinrQueryByFile instance = null;
        public static NRSinrQueryByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRSinrQueryByFile();
                    }
                }
            }
            return instance;
        }

        public NRSinrQueryByFile()
            : base()
        {
            this.IncludeEvent = false;
            analyzer = new NRSinrAnalyzer();
        }

        public override string Name
        {
            get { return "SINR与RSRP关联(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
    }

    public class NRSinrQueryByRegion : NRSinrQueryBase
    {
        private static NRSinrQueryByRegion instance = null;
        public static NRSinrQueryByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRSinrQueryByRegion();
                    }
                }
            }
            return instance;
        }

        public NRSinrQueryByRegion()
            : base()
        {
            this.FilterEventByRegion = false;
            this.IncludeEvent = false;
            analyzer = new NRSinrAnalyzer();
        }

        public override string Name
        {
            get { return "SINR与RSRP关联(按区域)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.Region;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is TestPoint_NR)
                {
                    return Condition.Geometorys.GeoOp.CheckPointInRegion(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
