﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.CQT.KPIReport
{
    public partial class CQTCoverImg : UserControl
    {
        public CQTCoverImg()
        {
            InitializeComponent();
        }
        
        string mainPntName;
        string subName;
        string floorName;
        public void Draw(DTFileDataManager fileMng, MapSerialInfo serial)
        {
            CQTKPIDataManager.GetPointName(fileMng.FileName
                 , out mainPntName, out  subName, out floorName);
            label.Text = string.Format("{0} {1}", subName, floorName);
            pictureEdit.Image = createImg(fileMng, serial);
        }

        private int imgWidth = 600;
        private int maxColCnt = 200;
        private Image createImg(DTFileDataManager file, MapSerialInfo serial)
        {
            int count = file.TestPoints.Count;
            //求平方根，得到矩阵的列数，加上外框空隙
            int colNum = (int)Math.Ceiling(Math.Sqrt(count)) + 2;
            if (colNum > maxColCnt)
            {
                colNum = maxColCnt;
            }
            int colWidth = imgWidth / colNum;
            float center = colWidth / 2.0f;
            float pntWidth = colWidth * 0.8f;
            float location = center - pntWidth / 2;
           

            Bitmap img = new Bitmap(imgWidth, imgWidth);
            Graphics g = Graphics.FromImage(img);
            for (int i = 0; i < count; i++)
            {
                int rowIdx = i / (colNum - 2);
                int colIdx = i % (colNum - 2);
                RectangleF rect = new RectangleF(location + (colIdx + 1) * colWidth, location + (rowIdx + 1) * colWidth, pntWidth, pntWidth);
                TestPoint pnt = file.TestPoints[i];

                Color? color = serial.ColorDisplayParam.Info.GetColor(getValue(pnt, serial.ColorDisplayParam));
                if (color != null)
                {
                    g.FillEllipse(new SolidBrush((Color)color), rect);
                }
            }
            g.Save();
            g.Dispose();
            return img;
        }

        public Image Image
        {
            get { return pictureEdit.Image; }
        }
        public string Title
        {
            get { return label.Text; }
        }

        private float? getValue(TestPoint tp, DTDisplayParameter p)
        {
            object value = tp[p.Parameter];
            if (value == null)
            {
                return null;
            }
            float ret = 0;
            if (!float.TryParse(value.ToString(), out ret))
            {
                return null;
            }
            return ret;
        }

        public event EventHandler Img_DoubleClick;
        private void pictureEdit_DoubleClick(object sender, EventArgs e)
        {
            if (Img_DoubleClick != null)
            {
                this.Img_DoubleClick(this, e);
            }
        }
        
    }
}
