﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class KPIQueryAndManage
    {

    }

    
    /// <summary>
    /// 查询测试名称、类型、模板
    /// </summary>
    public class DIYQueryKPIcfgRecordInfo : DIYSQLBase
    {
        public DIYQueryKPIcfgRecordInfo(MainModel mm)
            : base(mm)
        {
            mainModel = mm;
            MainDB = true;
        }
        public override string Name
        {
            get { return "查询指标"; }
        }

        protected override string getSqlTextString()
        {
            string Sql = "select * from tb_kpimng_cfg_recordInfo";
            return Sql;
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;

            return rType;
        }

        //类型-测试名-详细信息（类）
        public Dictionary<string, Dictionary<string, TestNameInfo>> testTypeDic { get; set; } = new Dictionary<string, Dictionary<string, TestNameInfo>>();
        Dictionary<string, TestNameInfo> testNameInfoDic = new Dictionary<string, TestNameInfo>();
        public TestNameInfo testNameInfo { get; set; } = new TestNameInfo();

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            testTypeDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string testName = package.Content.GetParamString();
                    string testType = package.Content.GetParamString();
                    string templateName = package.Content.GetParamString();

                    if(!testTypeDic.ContainsKey(testType))//网络类型与测试名等信息
                    {
                        testNameInfoDic = new Dictionary<string, TestNameInfo>();
                        testTypeDic.Add(testType, testNameInfoDic);
                    }
                    else
                    {
                        testTypeDic.TryGetValue(testType, out testNameInfoDic);
                    }

                    if(!testNameInfoDic.ContainsKey(testName))//测试名与对应信息
                    {
                        testNameInfo = new TestNameInfo(testName);
                        testNameInfo.TestType = testType;
                        testNameInfo.TemplateName = templateName;
                        testNameInfoDic.Add(testName, testNameInfo);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }


    /// <summary>
    /// 查询模板、类型、表名、列名
    /// </summary>
    public class DIYQueryKPIcfgTemplateInfo : DIYSQLBase
    {
        public DIYQueryKPIcfgTemplateInfo(MainModel mm)
            : base(mm)
        {
            mainModel = mm;
            MainDB = true;
        }
        public override string Name
        {
            get { return "查询指标"; }
        }
        protected override string getSqlTextString()
        {
            string Sql = "select * from tb_kpimng_cfg_templateInfo";
            return Sql;
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;

            return rType;
        }

        //类型-模板名-模板信息（类）
        public Dictionary<string, Dictionary<string, TemplateInfo>> testTypeDic { get; set; } = new Dictionary<string, Dictionary<string, TemplateInfo>>();
        Dictionary<string, TemplateInfo> templateDic = new Dictionary<string, TemplateInfo>();
        TemplateInfo templateInfo { get; set; } = new TemplateInfo();

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            testTypeDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            string templateName = package.Content.GetParamString();
            string testType = package.Content.GetParamString();
            string tableName = package.Content.GetParamString();
            string colName = package.Content.GetParamString();
            package.Content.GetParamString();//templateDesc

            if (!testTypeDic.ContainsKey(testType))
            {
                //类型与模板
                templateDic = new Dictionary<string, TemplateInfo>();
                testTypeDic.Add(testType, templateDic);
            }
            else
            {
                testTypeDic.TryGetValue(testType, out templateDic);
            }

            if (!templateDic.ContainsKey(templateName))
            {
                //模板与(表名，列名)
                templateInfo = new TemplateInfo(templateName);
                templateInfo.TestType = testType;
                templateInfo.TableName = tableName;
                templateInfo.ColName = colName;
                templateInfo.init();
                templateDic.Add(templateName, templateInfo);
            }
        }
    }

    /// <summary>
    /// 查询对应模板的所有列名
    /// </summary>
    public class DIYQueryKPItemplateTableInfo : DIYSQLBase
    {
        readonly string[] testNameArray;//测试名称
        readonly string tableName;//模板名

        public DIYQueryKPItemplateTableInfo(MainModel mm, string[] testNameArray, string tableName)
            : base(mm)
        {
            mainModel = mm;
            MainDB = true;
            this.testNameArray = testNameArray;
            this.tableName = tableName;
        }
        public override string Name
        {
            get { return "查询指标"; }
        }
        protected override string getSqlTextString()
        {
            StringBuilder sb;

            sb = new StringBuilder("select * from " + tableName + " where 测试名称 = '" + testNameArray[0] + "'");

            for (int i = 1; i < testNameArray.Length; i++)//选中多个测试名称查询
            {
                sb.Append(" or 测试名称 = '" + testNameArray[i] + "'");
            }
            sb.Append(";");

            string Sql = sb.ToString();
            return Sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int colCount = NetTypeName.getInstance().ColItems.Count;

            E_VType[] rType = new E_VType[colCount];
            for (int i = 0; i < colCount; i++)
            {
                rType[i] = E_VType.E_String;
            }
            return rType;

        }

        //列名与对应的数据
        public Dictionary<string, List<string>> paramDic { get; set; } = new Dictionary<string, List<string>>();
        List<string> strList = null;

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            paramDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    foreach (string item in NetTypeName.getInstance().ColItems)
                    {
                        if (!paramDic.ContainsKey(item))
                        {
                            strList = new List<string>();
                            paramDic.Add(item, strList);
                        }
                        else
                        {
                            paramDic.TryGetValue(item, out strList);
                        }
                        strList.Add(package.Content.GetParamString());
                    }

                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

    }
}
