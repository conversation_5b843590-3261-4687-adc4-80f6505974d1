﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraTab;
using DevExpress.XtraGrid.Views.Grid;
using MasterCom.RAMS.Model.PerformanceParam;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.CellPerformanceData
{
    public partial class CellPerformanceDataForm : DevExpress.XtraEditors.XtraForm
    {
        public DataTable[] dtInfo { get; set; }
        public MainModel mainmodel { get; set; }
        public CellPerformanceDataForm(MainModel Mainmodel)
        {
            InitializeComponent();
            mainmodel = Mainmodel;
            
        }

        public CellPerformanceDataForm(Dictionary<PerformanceTable, List<PerformanceField>> tableFieldDic,MainModel Mainmodel)
        {
            InitializeComponent();
            dtInfo = new DataTable[tableFieldDic.Count];
            xtraTabControl.TabPages.Clear();
            int tableIndex = 0;
            mainmodel = Mainmodel;
            foreach (MasterCom.RAMS.Model.PerformanceParam.PerformanceTable tb in tableFieldDic.Keys)
            {
                dtInfo[tableIndex] = new DataTable();
                dtInfo[tableIndex].Columns.Add("时间");
                dtInfo[tableIndex].Columns.Add("小区名称");
                dtInfo[tableIndex].Columns.Add("LAC");
                dtInfo[tableIndex].Columns.Add("CI");
                foreach (PerformanceField field in tableFieldDic[tb])
                {
                    dtInfo[tableIndex].Columns.Add(field.ShowName);
                } 

                GridView gv = new GridView();
                gv.OptionsView.ShowGroupPanel = false; //隐藏分组面板
                gv.OptionsView.ColumnAutoWidth = false; //自动列宽关闭,显示横向滚动条
                gv.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;  //列头自动换行
                gv.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;  //dev的bug  必须设置trimming为None
                
                gv.ColumnPanelRowHeight = 50;       //必须设置列头高度 否则不会换行
                GridControl gc = new GridControl();
                gc.Dock = DockStyle.Fill;
                gc.ContextMenuStrip = this.contextMenuStrip;
                gc.MainView = gv;
                gc.DataSource = dtInfo[tableIndex];
                gc.Enabled = false;
                XtraTabPage page = new XtraTabPage();
                page.Text = tb.ShowName;
                page.Controls.Add(gc);
                xtraTabControl.TabPages.Add(page);
                tableIndex++;
            }
        }

        public void FillData(List<DataTable> tbs)
        {
            int i = 0;
            foreach (XtraTabPage page in xtraTabControl.TabPages)
            {
                foreach (Control ctrl in page.Controls)
                {
                    if (ctrl is GridControl)
                    {
                        GridControl gridcontrol = ctrl as GridControl;
                        gridcontrol.DataSource = tbs[i];
                        i++;
                    }
                }
            }
        }

        public void EndData()
        {
            foreach (XtraTabPage page in xtraTabControl.TabPages)
            {
                foreach (Control ctrl in page.Controls)
                {
                    if (ctrl is GridControl)
                    {
                        (ctrl as GridControl).Enabled = true;
                    }
                }
            }
            tsslStatus.Text = "就绪";
        }

        public void FillDataTest(List<DataTable> tbs)
        {
            xtraTabControl.TabPages.Clear();
            foreach (DataTable tb in tbs)
            {
                GridView gv = new GridView();
                gv.OptionsView.ShowGroupPanel = false; //隐藏分组面板
                gv.OptionsView.ColumnAutoWidth = false; //自动列宽关闭,显示横向滚动条
                gv.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;  //列头自动换行
                gv.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;  //dev的bug  必须设置trimming为None
                gv.ColumnPanelRowHeight = 50;       //必须设置列头高度 否则不会换行
                GridControl gc = new GridControl();
                gc.Dock = DockStyle.Fill;
                gc.DataSource = tb;
                gc.ContextMenuStrip = this.contextMenuStrip;
                gc.MainView = gv;

                XtraTabPage page = new XtraTabPage();
                page.Text = tb.TableName;
                page.Controls.Add(gc);
                xtraTabControl.TabPages.Add(page);
            }
        }
        
        private void tsmiExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelHelper.XtraTabControlToExcel(xtraTabControl, "FieldName");
        }

        public DataTable[] GetDataTable()
        {
            return dtInfo;
        }

        //隐藏窗口至右下角
        private void CellPerformanceDataForm_Deactivate(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Minimized)
            {
                this.Visible = false;
                mainmodel.AddQuickWindowItem(this.GetType().Name, this.Text, "images\\cellquery.gif");
            }
        }
    }
}