﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FreqValueSettingBox : BaseFormStyle
    {
        public FreqValueSettingBox()
        {
            InitializeComponent();
        }

        public void AddFreqBand(List<string> freqBands)
        {
            foreach (var freqBand in freqBands)
            {
                cmbParentFreqBand.Items.Add(freqBand);
            }
            FreqValue = new FreqInfo();
            radioBtnValue_CheckedChanged(null, null);
        }

        public void AddFreq(string freqBandName, string parentFreqBandName)
        {
            FreqValue = new FreqInfo();
            cmbParentFreqBand.Enabled = false;
            if (!string.IsNullOrEmpty(parentFreqBandName))
            {
                cmbParentFreqBand.Items.Add(parentFreqBandName);
                cmbParentFreqBand.SelectedItem = parentFreqBandName;
            }
            textBoxBandName.Enabled = false;
            textBoxBandName.Text = freqBandName;
            radioBtnValue_CheckedChanged(null, null);
        }

        public void EditFreqBand(string freqBandName, string parentFreqBandName, List<string> freqBands)
        {
            //1.修改后原父频段是否有与该频段相重合部分?
            //2.该频段的子频段是否有与原父频段重合部分?(目前限制了只有1层子频段,不会有这种情况了)
            //3.新父频段的子频段是否与该频段有重合部分?

            //修改过于麻烦,暂时禁止修改父频段
            //foreach (var freqBand in freqBands)
            //{
            //    if (freqBand != freqBandName)
            //    {
            //        cmbParentFreqBand.Items.Add(freqBand);
            //    }
            //}
            //if (!string.IsNullOrEmpty(parentFreqBandName))
            //{
            //    cmbParentFreqBand.SelectedItem = parentFreqBandName;
            //}
            cmbParentFreqBand.Enabled = false;
            if (!string.IsNullOrEmpty(parentFreqBandName))
            {
                cmbParentFreqBand.Items.Add(parentFreqBandName);
                cmbParentFreqBand.SelectedItem = parentFreqBandName;
            }
            FreqValue = new FreqInfo();
            textBoxBandName.Text = freqBandName;

            radioBtnValue.Enabled = false;
            radioBtnRange.Enabled = false;
            textBoxValue.Enabled = false;
            textBoxMaxValue.Enabled = false;
            textBoxMinValue.Enabled = false;
        }

        public void EditFreq(string freqBandName, string parentFreqBandName, FreqInfo freqValue)
        {
            FreqValue = freqValue;
            cmbParentFreqBand.Enabled = false;
            if (!string.IsNullOrEmpty(parentFreqBandName))
            {
                cmbParentFreqBand.Items.Add(parentFreqBandName);
                cmbParentFreqBand.SelectedItem = parentFreqBandName;
            }
            textBoxBandName.Enabled = false;
            textBoxBandName.Text = freqBandName;
           
            if (FreqValue.Type == 0)
            {
                radioBtnValue.Checked = true;
                textBoxValue.Text = FreqValue.Value.ToString();
            }
            else
            {
                radioBtnRange.Checked = true;
                textBoxMaxValue.Text = FreqValue.MaxRangeValue.ToString();
                textBoxMinValue.Text = FreqValue.MinRangeValue.ToString();
            }

            radioBtnValue_CheckedChanged(null, null);
        }

        public FreqInfo FreqValue { get; private set; } 
        public string FreqBandName { get; private set; }
        public string ParentFreqBandName { get; private set; }

        protected bool checkValue()
        {
            if (!radioBtnRange.Checked)
            {
                return true;
            }
            if (string.IsNullOrEmpty(textBoxMinValue.Text) || string.IsNullOrEmpty(textBoxMaxValue.Text))
            {
                errorProvider.SetError(textBoxMaxValue, "不能为空!");
                return false;
            }
            if (Convert.ToInt32(textBoxMinValue.Text) <= Convert.ToInt32(textBoxMaxValue.Text))
            {
                errorProvider.Clear();
                return true;
            }
            else
            {
                errorProvider.SetError(textBoxMaxValue, "上限值应该大于下限值!");
                return false;
            }
        }

        protected void applySetting()
        {
            FreqValue.Type = radioBtnValue.Checked ? 0 : 1;
            if (radioBtnValue.Checked)
            {
                if (!string.IsNullOrEmpty(textBoxValue.Text))
                {
                    FreqValue.Value = Convert.ToInt32(textBoxValue.Text);
                }
            }
            else if(!string.IsNullOrEmpty(textBoxMaxValue.Text) && !string.IsNullOrEmpty(textBoxMinValue.Text))
            {
                FreqValue.MaxRangeValue = Convert.ToInt32(textBoxMaxValue.Text);
                FreqValue.MinRangeValue = Convert.ToInt32(textBoxMinValue.Text);
            }
            if (cmbParentFreqBand.SelectedItem != null)
            {
                ParentFreqBandName = cmbParentFreqBand.SelectedItem.ToString();
            }
            FreqBandName = textBoxBandName.Text;
            FreqValue.FreqBandName = FreqBandName;
            FreqValue.Calculate();
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            if (checkValue())
            {
                applySetting();
                DialogResult = DialogResult.OK;
            }
        }

        private void textBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            //文本框只能输入数字和退格键
            if (e.KeyChar != 8 && !Char.IsDigit(e.KeyChar))
            {
                e.Handled = true;
            }
        }

        private void textBoxMinValue_TextChanged(object sender, EventArgs e)
        {
            checkValue();
        }

        private void textBoxMaxValue_TextChanged(object sender, EventArgs e)
        {
            checkValue();
        }

        private void radioBtnValue_CheckedChanged(object sender, EventArgs e)
        {
            if (radioBtnValue.Checked)
            {
                textBoxValue.Enabled = true;
                textBoxMaxValue.Enabled = false;
                textBoxMinValue.Enabled = false;
            }
            else
            {
                textBoxValue.Enabled = false;
                textBoxMaxValue.Enabled = true;
                textBoxMinValue.Enabled = true;
            }
        }
    }
}
