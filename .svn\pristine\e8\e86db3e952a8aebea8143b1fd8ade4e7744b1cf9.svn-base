﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class IndicatorShowFrm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.splitContainer5 = new System.Windows.Forms.SplitContainer();
            this.perCompShow = new MasterCom.RAMS.ZTFunc.IndicatorsCompareShow();
            this.perScoreShow = new MasterCom.RAMS.ZTFunc.EleShowForScoreQueryControl();
            this.labelControlScoreCom1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControlScoreCom2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControlScorePer = new DevExpress.XtraEditors.LabelControl();
            this.btnPerDetail = new DevExpress.XtraEditors.SimpleButton();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.splitContainer8 = new System.Windows.Forms.SplitContainer();
            this.testCompShow = new MasterCom.RAMS.ZTFunc.IndicatorsCompareShow();
            this.testScoreShow = new MasterCom.RAMS.ZTFunc.EleShowForScoreQueryControl();
            this.labelControlScoreCom3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControlScoreCom4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControlScoreTest = new DevExpress.XtraEditors.LabelControl();
            this.btnTestDetail = new DevExpress.XtraEditors.SimpleButton();
            this.dTPickerIniStart = new System.Windows.Forms.DateTimePicker();
            this.dTPickerIniEnd = new System.Windows.Forms.DateTimePicker();
            this.dTPickerCompelStart = new System.Windows.Forms.DateTimePicker();
            this.dTPickerCompelEnd = new System.Windows.Forms.DateTimePicker();
            this.checkEditCompel = new DevExpress.XtraEditors.CheckEdit();
            this.splitContainer2 = new System.Windows.Forms.SplitContainer();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.textBoxAdvise = new System.Windows.Forms.TextBox();
            this.splitContainer9 = new System.Windows.Forms.SplitContainer();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.labelControlScoreCom6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControlScoreCom5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControlScoreSum = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnQuery = new DevExpress.XtraEditors.SimpleButton();
            this.label2 = new System.Windows.Forms.Label();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.splitContainer5.Panel1.SuspendLayout();
            this.splitContainer5.Panel2.SuspendLayout();
            this.splitContainer5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            this.splitContainer8.Panel1.SuspendLayout();
            this.splitContainer8.Panel2.SuspendLayout();
            this.splitContainer8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditCompel.Properties)).BeginInit();
            this.splitContainer2.Panel1.SuspendLayout();
            this.splitContainer2.Panel2.SuspendLayout();
            this.splitContainer2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            this.splitContainer9.Panel1.SuspendLayout();
            this.splitContainer9.Panel2.SuspendLayout();
            this.splitContainer9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.groupControl1);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.groupControl2);
            this.splitContainer1.Size = new System.Drawing.Size(799, 459);
            this.splitContainer1.SplitterDistance = 228;
            this.splitContainer1.TabIndex = 1;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.splitContainer5);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(799, 228);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "性能";
            // 
            // splitContainer5
            // 
            this.splitContainer5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer5.FixedPanel = System.Windows.Forms.FixedPanel.Panel2;
            this.splitContainer5.Location = new System.Drawing.Point(2, 23);
            this.splitContainer5.Name = "splitContainer5";
            // 
            // splitContainer5.Panel1
            // 
            this.splitContainer5.Panel1.Controls.Add(this.perCompShow);
            this.splitContainer5.Panel1.Controls.Add(this.perScoreShow);
            // 
            // splitContainer5.Panel2
            // 
            this.splitContainer5.Panel2.Controls.Add(this.labelControlScoreCom1);
            this.splitContainer5.Panel2.Controls.Add(this.labelControlScoreCom2);
            this.splitContainer5.Panel2.Controls.Add(this.labelControlScorePer);
            this.splitContainer5.Panel2.Controls.Add(this.btnPerDetail);
            this.splitContainer5.Size = new System.Drawing.Size(795, 203);
            this.splitContainer5.SplitterDistance = 667;
            this.splitContainer5.TabIndex = 0;
            // 
            // perCompShow
            // 
            this.perCompShow.Dock = System.Windows.Forms.DockStyle.Fill;
            this.perCompShow.Location = new System.Drawing.Point(0, 0);
            this.perCompShow.Name = "perCompShow";
            this.perCompShow.Size = new System.Drawing.Size(667, 203);
            this.perCompShow.TabIndex = 1;
            // 
            // perScoreShow
            // 
            this.perScoreShow.Dock = System.Windows.Forms.DockStyle.Fill;
            this.perScoreShow.Location = new System.Drawing.Point(0, 0);
            this.perScoreShow.Name = "perScoreShow";
            this.perScoreShow.Size = new System.Drawing.Size(667, 203);
            this.perScoreShow.TabIndex = 0;
            // 
            // labelControlScoreCom1
            // 
            this.labelControlScoreCom1.Location = new System.Drawing.Point(20, 50);
            this.labelControlScoreCom1.Name = "labelControlScoreCom1";
            this.labelControlScoreCom1.Size = new System.Drawing.Size(84, 14);
            this.labelControlScoreCom1.TabIndex = 8;
            this.labelControlScoreCom1.Text = "对比时间分数：";
            // 
            // labelControlScoreCom2
            // 
            this.labelControlScoreCom2.Appearance.Font = new System.Drawing.Font("华文楷体", 26.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControlScoreCom2.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.labelControlScoreCom2.Appearance.Options.UseFont = true;
            this.labelControlScoreCom2.Appearance.Options.UseForeColor = true;
            this.labelControlScoreCom2.Location = new System.Drawing.Point(15, 70);
            this.labelControlScoreCom2.Name = "labelControlScoreCom2";
            this.labelControlScoreCom2.Size = new System.Drawing.Size(94, 39);
            this.labelControlScoreCom2.TabIndex = 7;
            this.labelControlScoreCom2.Text = "100.00";
            // 
            // labelControlScorePer
            // 
            this.labelControlScorePer.Appearance.Font = new System.Drawing.Font("华文楷体", 26.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControlScorePer.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.labelControlScorePer.Appearance.Options.UseFont = true;
            this.labelControlScorePer.Appearance.Options.UseForeColor = true;
            this.labelControlScorePer.Location = new System.Drawing.Point(15, 10);
            this.labelControlScorePer.Name = "labelControlScorePer";
            this.labelControlScorePer.Size = new System.Drawing.Size(94, 39);
            this.labelControlScorePer.TabIndex = 1;
            this.labelControlScorePer.Text = "100.00";
            // 
            // btnPerDetail
            // 
            this.btnPerDetail.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.btnPerDetail.Location = new System.Drawing.Point(0, 143);
            this.btnPerDetail.Name = "btnPerDetail";
            this.btnPerDetail.Size = new System.Drawing.Size(124, 60);
            this.btnPerDetail.TabIndex = 0;
            this.btnPerDetail.Text = "性能钻取";
            this.btnPerDetail.Click += new System.EventHandler(this.btnPerDetail_Click);
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.splitContainer8);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(799, 227);
            this.groupControl2.TabIndex = 1;
            this.groupControl2.Text = "测试";
            // 
            // splitContainer8
            // 
            this.splitContainer8.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer8.FixedPanel = System.Windows.Forms.FixedPanel.Panel2;
            this.splitContainer8.Location = new System.Drawing.Point(2, 23);
            this.splitContainer8.Name = "splitContainer8";
            // 
            // splitContainer8.Panel1
            // 
            this.splitContainer8.Panel1.Controls.Add(this.testCompShow);
            this.splitContainer8.Panel1.Controls.Add(this.testScoreShow);
            // 
            // splitContainer8.Panel2
            // 
            this.splitContainer8.Panel2.Controls.Add(this.labelControlScoreCom3);
            this.splitContainer8.Panel2.Controls.Add(this.labelControlScoreCom4);
            this.splitContainer8.Panel2.Controls.Add(this.labelControlScoreTest);
            this.splitContainer8.Panel2.Controls.Add(this.btnTestDetail);
            this.splitContainer8.Size = new System.Drawing.Size(795, 202);
            this.splitContainer8.SplitterDistance = 667;
            this.splitContainer8.TabIndex = 0;
            // 
            // testCompShow
            // 
            this.testCompShow.Dock = System.Windows.Forms.DockStyle.Fill;
            this.testCompShow.Location = new System.Drawing.Point(0, 0);
            this.testCompShow.Name = "testCompShow";
            this.testCompShow.Size = new System.Drawing.Size(667, 202);
            this.testCompShow.TabIndex = 1;
            // 
            // testScoreShow
            // 
            this.testScoreShow.Dock = System.Windows.Forms.DockStyle.Fill;
            this.testScoreShow.Location = new System.Drawing.Point(0, 0);
            this.testScoreShow.Name = "testScoreShow";
            this.testScoreShow.Size = new System.Drawing.Size(667, 202);
            this.testScoreShow.TabIndex = 0;
            // 
            // labelControlScoreCom3
            // 
            this.labelControlScoreCom3.Location = new System.Drawing.Point(20, 50);
            this.labelControlScoreCom3.Name = "labelControlScoreCom3";
            this.labelControlScoreCom3.Size = new System.Drawing.Size(84, 14);
            this.labelControlScoreCom3.TabIndex = 8;
            this.labelControlScoreCom3.Text = "对比时间分数：";
            // 
            // labelControlScoreCom4
            // 
            this.labelControlScoreCom4.Appearance.Font = new System.Drawing.Font("华文楷体", 26.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControlScoreCom4.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.labelControlScoreCom4.Appearance.Options.UseFont = true;
            this.labelControlScoreCom4.Appearance.Options.UseForeColor = true;
            this.labelControlScoreCom4.Location = new System.Drawing.Point(15, 70);
            this.labelControlScoreCom4.Name = "labelControlScoreCom4";
            this.labelControlScoreCom4.Size = new System.Drawing.Size(94, 39);
            this.labelControlScoreCom4.TabIndex = 9;
            this.labelControlScoreCom4.Text = "100.00";
            // 
            // labelControlScoreTest
            // 
            this.labelControlScoreTest.Appearance.Font = new System.Drawing.Font("华文楷体", 26.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControlScoreTest.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.labelControlScoreTest.Appearance.Options.UseFont = true;
            this.labelControlScoreTest.Appearance.Options.UseForeColor = true;
            this.labelControlScoreTest.Location = new System.Drawing.Point(15, 10);
            this.labelControlScoreTest.Name = "labelControlScoreTest";
            this.labelControlScoreTest.Size = new System.Drawing.Size(94, 39);
            this.labelControlScoreTest.TabIndex = 2;
            this.labelControlScoreTest.Text = "100.00";
            // 
            // btnTestDetail
            // 
            this.btnTestDetail.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.btnTestDetail.Location = new System.Drawing.Point(0, 142);
            this.btnTestDetail.Name = "btnTestDetail";
            this.btnTestDetail.Size = new System.Drawing.Size(124, 60);
            this.btnTestDetail.TabIndex = 0;
            this.btnTestDetail.Text = "事件信息";
            this.btnTestDetail.Click += new System.EventHandler(this.btnTestDetail_Click);
            // 
            // dTPickerIniStart
            // 
            this.dTPickerIniStart.Location = new System.Drawing.Point(12, 49);
            this.dTPickerIniStart.Name = "dTPickerIniStart";
            this.dTPickerIniStart.Size = new System.Drawing.Size(129, 22);
            this.dTPickerIniStart.TabIndex = 0;
            // 
            // dTPickerIniEnd
            // 
            this.dTPickerIniEnd.Location = new System.Drawing.Point(12, 77);
            this.dTPickerIniEnd.Name = "dTPickerIniEnd";
            this.dTPickerIniEnd.Size = new System.Drawing.Size(129, 22);
            this.dTPickerIniEnd.TabIndex = 1;
            // 
            // dTPickerCompelStart
            // 
            this.dTPickerCompelStart.Location = new System.Drawing.Point(12, 130);
            this.dTPickerCompelStart.Name = "dTPickerCompelStart";
            this.dTPickerCompelStart.Size = new System.Drawing.Size(129, 22);
            this.dTPickerCompelStart.TabIndex = 3;
            // 
            // dTPickerCompelEnd
            // 
            this.dTPickerCompelEnd.Location = new System.Drawing.Point(12, 158);
            this.dTPickerCompelEnd.Name = "dTPickerCompelEnd";
            this.dTPickerCompelEnd.Size = new System.Drawing.Size(129, 22);
            this.dTPickerCompelEnd.TabIndex = 4;
            // 
            // checkEditCompel
            // 
            this.checkEditCompel.Location = new System.Drawing.Point(12, 105);
            this.checkEditCompel.Name = "checkEditCompel";
            this.checkEditCompel.Properties.Caption = "双时段对比";
            this.checkEditCompel.Size = new System.Drawing.Size(90, 19);
            this.checkEditCompel.TabIndex = 2;
            this.checkEditCompel.CheckedChanged += new System.EventHandler(this.checkEditPerCompel_CheckedChanged);
            // 
            // splitContainer2
            // 
            this.splitContainer2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer2.FixedPanel = System.Windows.Forms.FixedPanel.Panel2;
            this.splitContainer2.Location = new System.Drawing.Point(0, 0);
            this.splitContainer2.Name = "splitContainer2";
            this.splitContainer2.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer2.Panel1
            // 
            this.splitContainer2.Panel1.Controls.Add(this.splitContainer1);
            // 
            // splitContainer2.Panel2
            // 
            this.splitContainer2.Panel2.Controls.Add(this.groupControl3);
            this.splitContainer2.Size = new System.Drawing.Size(799, 540);
            this.splitContainer2.SplitterDistance = 459;
            this.splitContainer2.TabIndex = 2;
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.btnOK);
            this.groupControl3.Controls.Add(this.textBoxAdvise);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(799, 77);
            this.groupControl3.TabIndex = 0;
            this.groupControl3.Text = "建议";
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(677, 26);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(122, 46);
            this.btnOK.TabIndex = 1;
            this.btnOK.Text = "提交";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // textBoxAdvise
            // 
            this.textBoxAdvise.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxAdvise.BackColor = System.Drawing.Color.White;
            this.textBoxAdvise.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBoxAdvise.Location = new System.Drawing.Point(2, 23);
            this.textBoxAdvise.Multiline = true;
            this.textBoxAdvise.Name = "textBoxAdvise";
            this.textBoxAdvise.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.textBoxAdvise.Size = new System.Drawing.Size(671, 52);
            this.textBoxAdvise.TabIndex = 0;
            // 
            // splitContainer9
            // 
            this.splitContainer9.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer9.Location = new System.Drawing.Point(0, 0);
            this.splitContainer9.Name = "splitContainer9";
            // 
            // splitContainer9.Panel1
            // 
            this.splitContainer9.Panel1.Controls.Add(this.groupControl4);
            // 
            // splitContainer9.Panel2
            // 
            this.splitContainer9.Panel2.Controls.Add(this.splitContainer2);
            this.splitContainer9.Size = new System.Drawing.Size(957, 540);
            this.splitContainer9.SplitterDistance = 154;
            this.splitContainer9.TabIndex = 3;
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.labelControlScoreCom6);
            this.groupControl4.Controls.Add(this.labelControlScoreCom5);
            this.groupControl4.Controls.Add(this.labelControlScoreSum);
            this.groupControl4.Controls.Add(this.labelControl1);
            this.groupControl4.Controls.Add(this.btnQuery);
            this.groupControl4.Controls.Add(this.dTPickerCompelEnd);
            this.groupControl4.Controls.Add(this.checkEditCompel);
            this.groupControl4.Controls.Add(this.label2);
            this.groupControl4.Controls.Add(this.dTPickerCompelStart);
            this.groupControl4.Controls.Add(this.dTPickerIniStart);
            this.groupControl4.Controls.Add(this.dTPickerIniEnd);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(154, 540);
            this.groupControl4.TabIndex = 0;
            this.groupControl4.Text = "双时段对比";
            // 
            // labelControlScoreCom6
            // 
            this.labelControlScoreCom6.Appearance.Font = new System.Drawing.Font("华文楷体", 36F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControlScoreCom6.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.labelControlScoreCom6.Appearance.Options.UseFont = true;
            this.labelControlScoreCom6.Appearance.Options.UseForeColor = true;
            this.labelControlScoreCom6.Location = new System.Drawing.Point(6, 463);
            this.labelControlScoreCom6.Name = "labelControlScoreCom6";
            this.labelControlScoreCom6.Size = new System.Drawing.Size(132, 53);
            this.labelControlScoreCom6.TabIndex = 10;
            this.labelControlScoreCom6.Text = "100.00";
            // 
            // labelControlScoreCom5
            // 
            this.labelControlScoreCom5.Location = new System.Drawing.Point(14, 437);
            this.labelControlScoreCom5.Name = "labelControlScoreCom5";
            this.labelControlScoreCom5.Size = new System.Drawing.Size(84, 14);
            this.labelControlScoreCom5.TabIndex = 9;
            this.labelControlScoreCom5.Text = "对比时间分数：";
            // 
            // labelControlScoreSum
            // 
            this.labelControlScoreSum.Appearance.Font = new System.Drawing.Font("华文楷体", 36F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControlScoreSum.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.labelControlScoreSum.Appearance.Options.UseFont = true;
            this.labelControlScoreSum.Appearance.Options.UseForeColor = true;
            this.labelControlScoreSum.Location = new System.Drawing.Point(6, 378);
            this.labelControlScoreSum.Name = "labelControlScoreSum";
            this.labelControlScoreSum.Size = new System.Drawing.Size(132, 53);
            this.labelControlScoreSum.TabIndex = 8;
            this.labelControlScoreSum.Text = "100.00";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 36F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(14, 314);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(96, 58);
            this.labelControl1.TabIndex = 7;
            this.labelControl1.Text = "总分";
            // 
            // btnQuery
            // 
            this.btnQuery.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.btnQuery.Location = new System.Drawing.Point(16, 130);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(125, 40);
            this.btnQuery.TabIndex = 6;
            this.btnQuery.Text = "查询";
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(20, 32);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(91, 14);
            this.label2.TabIndex = 5;
            this.label2.Text = "当前时间范围：";
            // 
            // IndicatorShowFrm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(957, 540);
            this.Controls.Add(this.splitContainer9);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "IndicatorShowFrm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "网评分表";
            this.Load += new System.EventHandler(this.IndicatorShowFrm_Load);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.splitContainer5.Panel1.ResumeLayout(false);
            this.splitContainer5.Panel2.ResumeLayout(false);
            this.splitContainer5.Panel2.PerformLayout();
            this.splitContainer5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.splitContainer8.Panel1.ResumeLayout(false);
            this.splitContainer8.Panel2.ResumeLayout(false);
            this.splitContainer8.Panel2.PerformLayout();
            this.splitContainer8.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.checkEditCompel.Properties)).EndInit();
            this.splitContainer2.Panel1.ResumeLayout(false);
            this.splitContainer2.Panel2.ResumeLayout(false);
            this.splitContainer2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            this.splitContainer9.Panel1.ResumeLayout(false);
            this.splitContainer9.Panel2.ResumeLayout(false);
            this.splitContainer9.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            this.groupControl4.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.SplitContainer splitContainer1;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.SplitContainer splitContainer2;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private System.Windows.Forms.TextBox textBoxAdvise;
        private EleShowForScoreQueryControl perScoreShow;
        private DevExpress.XtraEditors.CheckEdit checkEditCompel;
        private System.Windows.Forms.DateTimePicker dTPickerIniEnd;
        private System.Windows.Forms.DateTimePicker dTPickerIniStart;
        private System.Windows.Forms.DateTimePicker dTPickerCompelStart;
        private System.Windows.Forms.DateTimePicker dTPickerCompelEnd;
        private System.Windows.Forms.SplitContainer splitContainer5;
        private DevExpress.XtraEditors.SimpleButton btnPerDetail;
        private System.Windows.Forms.SplitContainer splitContainer8;
        private EleShowForScoreQueryControl testScoreShow;
        private DevExpress.XtraEditors.SimpleButton btnTestDetail;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private System.Windows.Forms.SplitContainer splitContainer9;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private DevExpress.XtraEditors.SimpleButton btnQuery;
        private IndicatorsCompareShow perCompShow;
        private IndicatorsCompareShow testCompShow;
        private DevExpress.XtraEditors.LabelControl labelControlScorePer;
        private DevExpress.XtraEditors.LabelControl labelControlScoreTest;
        private DevExpress.XtraEditors.LabelControl labelControlScoreSum;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControlScoreCom1;
        private DevExpress.XtraEditors.LabelControl labelControlScoreCom2;
        private DevExpress.XtraEditors.LabelControl labelControlScoreCom3;
        private DevExpress.XtraEditors.LabelControl labelControlScoreCom4;
        private DevExpress.XtraEditors.LabelControl labelControlScoreCom5;
        private DevExpress.XtraEditors.LabelControl labelControlScoreCom6;
    }
}