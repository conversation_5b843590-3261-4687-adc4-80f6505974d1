﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    public partial class MapNRCellLayerCellProperties : MTLayerPropUserControl
    {
        public MapNRCellLayerCellProperties()
        {
            InitializeComponent();
        }

        private MapNRCellLayer layer = null;
        public override void Setup(object obj)
        {
            layer = obj as MapNRCellLayer;
            if (layer == null)
            {
                return;
            }
            Text = "小区";
            checkBoxDisplay.Checked = layer.DrawCell;
            colorCell.Color = layer.ColorCell;

            TrackBarOpacity.Value = layer.ColorCell.A;
            cbxDrawCellLabel.Checked = layer.DrawCellLabel;
            cbxCellNCI.Checked = layer.DrawCellNCI;
            this.cbxCellCode.Checked = layer.DrawCellCode;
            this.cbxCellPCI.Checked = layer.DrawCellPCI;
            this.cbxCellDes.Checked = layer.DrawCellDes;
            this.cbxCellFreq.Checked = layer.DrawCellARFCN;
            this.cbxCellFreqList.Checked = layer.DrawCellFreqs;
            this.cbxCellLAC.Checked = layer.DrawCellTAC;
            this.cbxCellName.Checked = layer.DrawCellName;

            checkBoxDisplay.CheckedChanged += new EventHandler(checkBoxDisplay_CheckedChanged);
            colorCell.ColorChanged += new EventHandler(colorCell_ColorChanged);
            colorFrame.ColorChanged += new EventHandler(colorFrame_ColorChanged);
            TrackBarOpacity.ValueChanged += new EventHandler(TrackBarOpacity_ValueChanged);
            cbxDrawCellLabel.CheckedChanged += new EventHandler(cbxDrawCellLabel_CheckedChanged);
            cbxCellNCI.CheckedChanged += new EventHandler(cbxCellCI_CheckedChanged);
            cbxCellCode.CheckedChanged += new EventHandler(cbxCellCode_CheckedChanged);
            cbxCellPCI.CheckedChanged += new EventHandler(cbxCellCPI_CheckedChanged);
            cbxCellDes.CheckedChanged += new EventHandler(cbxCellDes_CheckedChanged);
            cbxCellFreq.CheckedChanged += new EventHandler(cbxCellFreq_CheckedChanged);
            cbxCellLAC.CheckedChanged += new EventHandler(cbxCellLAC_CheckedChanged);
            cbxCellFreqList.CheckedChanged += new EventHandler(cbxCellFreqList_CheckedChanged);
            cbxCellName.CheckedChanged += new EventHandler(cbxCellName_CheckedChanged);
            btnFont.Click += new EventHandler(btnFont_Click);
        }

        FontDialog fontDialog = new FontDialog();
        void btnFont_Click(object sender, EventArgs e)
        {
            fontDialog.Font = layer.FontCellLabel;
            if (fontDialog.ShowDialog(this) == DialogResult.OK)
            {
                layer.FontCellLabel = fontDialog.Font;
            }
        }

        void cbxCellFreqList_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellFreqs = cbxCellFreqList.Checked;
        }

        void cbxCellName_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellName = cbxCellName.Checked;
        }

        void cbxCellLAC_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellTAC = cbxCellLAC.Checked;
        }

        void cbxCellFreq_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellARFCN = cbxCellFreq.Checked;
        }

        void cbxCellDes_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellDes = cbxCellDes.Checked;
        }

        void cbxCellCPI_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellPCI = cbxCellPCI.Checked;
        }

        void cbxCellCode_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellCode = cbxCellCode.Checked;
        }

        void cbxCellCI_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellNCI = cbxCellNCI.Checked;
        }

        void cbxDrawCellLabel_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellLabel = cbxDrawCellLabel.Checked;
        }

        void TrackBarOpacity_ValueChanged(object sender, EventArgs e)
        {
            layer.ColorCell = Color.FromArgb(TrackBarOpacity.Value, layer.ColorCell);
            layer.ColorFrameCell = Color.FromArgb(TrackBarOpacity.Value, layer.ColorFrameCell);
        }

        void colorCell_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCell = Color.FromArgb(TrackBarOpacity.Value, colorCell.Color);
        }

        void colorFrame_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorFrameCell = Color.FromArgb(TrackBarOpacity.Value, colorFrame.Color);
        }

        void checkBoxDisplay_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCell = checkBoxDisplay.Checked;
        }
    }
}
