﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class ReportTemplateMng
    {
        private static ReportTemplateMng instance = null;
        private ReportTemplateMng()
        {
            Load();
        }
        public static ReportTemplateMng Instance
        {
            get {
                if (instance==null)
                {
                    instance = new ReportTemplateMng();
                }
                return instance;
            }
        }
        public List<ReportTemplate> Templates
        {
            get;
            private set;
        }

        private readonly string path = string.Format(Application.StartupPath + "/config/homepage/templates.xml");

        private List<object> cfgParam
        {
            get
            {
                List<object> rpts = new List<object>();
                foreach (ReportTemplate rpt in Templates)
                {
                    rpts.Add(rpt.Param);
                }
                return rpts;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                Templates.Clear();
                foreach (object obj in value)
                {
                    ReportTemplate rpt = new ReportTemplate();
                    rpt.Param = obj as Dictionary<string, object>;
                    Templates.Add(rpt);
                }
            }
        }

        public void Load()
        {
            Templates = new List<ReportTemplate>();
            if (System.IO.File.Exists(path))
            {
                XmlConfigFile configFile = new XmlConfigFile(path);
                cfgParam = configFile.GetItemValue("Cfg", "Templates") as List<object>;
            }
        }

        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("Cfg");
            xmlFile.AddItem(cfgE, "Templates", this.cfgParam);
            xmlFile.Save(path);
        }

    }
}
