﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS
{
    public class QueryCPUnitDirectByGrid2 : QueryBase
    {
        public MapFormItemSelection ItemSelection { get; set; }

        private CPModeDlgLsatCondition2 curCPModeCondition;

        public QueryCPUnitDirectByGrid2(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20007, this.Name);
        }

        public override string IconName
        {
            get { return "IconCP"; }
        }

        public override string Name
        {
            get { return "波动趋势跟踪分析"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            CPModeTimeProjSettingDlg2 dlg = new CPModeTimeProjSettingDlg2(mainModel, ItemSelection, curCPModeCondition);
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }

            curCPModeCondition = dlg.CurCPModeCondition;

            CPUnitShowForm2 form = null;
            form = MainModel.GetObjectFromBlackboard(typeof(CPUnitShowForm2).FullName) as CPUnitShowForm2;

            if (form == null || form.IsDisposed)
            {
                form = new CPUnitShowForm2(MainModel, ItemSelection, condition, curCPModeCondition.CurConditionHost, 
                    curCPModeCondition.CurConditionGuest, curCPModeCondition.CurConditionGuest2);
                form.StartAnalysisData(curCPModeCondition.CurCompareTemplate, curCPModeCondition.IsChkRoad);
            }

            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
    }
}
