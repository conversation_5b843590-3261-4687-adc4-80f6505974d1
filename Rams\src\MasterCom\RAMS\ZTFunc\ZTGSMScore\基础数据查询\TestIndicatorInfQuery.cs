﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Globalization;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 测试指标数据查询
    /// </summary>
    class TestIndicatorInfQuery: DIYSQLBase
    {
        private readonly bool IsTDIn = false;
        public DateTime TimeStart { get; set; }
        public DateTime TimeEnd { get; set; }
        public TestIndicatorInfQuery(MainModel mainModel, bool IsTDIn)
            : base(mainModel)
        {
            this.IsTDIn = IsTDIn;
            TimeStart = DateTime.MaxValue;
            TimeEnd = DateTime.MaxValue;
        }

        protected override string getSqlTextString()
        {
            //this.SetQueryCondition(
            if (TimeStart == DateTime.MaxValue && TimeEnd == DateTime.MaxValue)
            {
                TimePeriod timePeriod = condition.Periods[0];
                TimeStart = timePeriod.BeginTime;
                TimeEnd = timePeriod.EndTime;
            }
            string cityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            if (cityName == null)
            {
                return "";
            }
            List<string> cityNames = new List<string>(new string[] {"西安","咸阳","宝鸡","商洛","汉中","渭南","安康","铜川","榆林","延安"});
            cityName = cityName.Replace("市", "");
            if (!cityNames.Contains(cityName))
            {
                return "";
            }
            //MainModel.
            string startTimeStr = TimeStart.ToString("yyyy-MM-dd 00:00:00");
            string endTimeStr = TimeEnd.AddDays(1).ToString("yyyy-MM-dd 00:00:00");
            if (IsTDIn)
            {
                string statement = TDSqlStetement.Replace("#cityname", cityName);
                statement = statement.Replace("#stime", startTimeStr);
                statement = statement.Replace("#etime", endTimeStr);
                return statement;                
            }
            else
            {
               
                string statement = GSMSqlStetement.Replace("#cityname", cityName);
                statement = statement.Replace("#stime", startTimeStr);
                statement = statement.Replace("#etime", endTimeStr);
                return statement;
                //return 
            }
        }
        
        public List<Indicator> ScoreList { get; set; } = new List<Indicator>();

        protected override E_VType[] getSqlRetTypeArr()
        {
            if (IsTDIn)
            {
                E_VType[] rType = new E_VType[16];
                rType[0] = E_VType.E_Float;
                rType[1] = E_VType.E_Float;
                rType[2] = E_VType.E_Float;
                rType[3] = E_VType.E_Float;
                rType[4] = E_VType.E_Float;
                rType[5] = E_VType.E_Float;
                rType[6] = E_VType.E_Float;
                rType[7] = E_VType.E_Float;
                rType[8] = E_VType.E_Float;
                rType[9] = E_VType.E_Float; 
                rType[10] = E_VType.E_Float;
                rType[11] = E_VType.E_Float;
                rType[12] = E_VType.E_Float;
                rType[13] = E_VType.E_Float;
                rType[14] = E_VType.E_Float;
                rType[15] = E_VType.E_Float;
                return rType;
            }
            else
            {
                E_VType[] rType = new E_VType[14];
                rType[0] = E_VType.E_Float;
                rType[1] = E_VType.E_Float;
                rType[2] = E_VType.E_Float;
                rType[3] = E_VType.E_Float;
                rType[4] = E_VType.E_Float;
                rType[5] = E_VType.E_Float;
                rType[6] = E_VType.E_Float;
                rType[7] = E_VType.E_Float;
                rType[8] = E_VType.E_Float;
                rType[9] = E_VType.E_Float;
                rType[10] = E_VType.E_Float;
                rType[11] = E_VType.E_Float;
                rType[12] = E_VType.E_Float;
                rType[13] = E_VType.E_Float;
                return rType;
            }
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            //根据TD和GSM初始化指标项
            IniIndicatorList();
            
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    setScoreListKPI(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
            GetIndicatorThresholdInf();
        }

        private void setScoreListKPI(Package package)
        {
            try
            {
                if (IsTDIn)
                {
                    ScoreList[0].KPI = package.Content.GetParamFloat();
                    ScoreList[1].KPI = package.Content.GetParamFloat();
                    ScoreList[2].KPI = package.Content.GetParamFloat();
                    ScoreList[3].KPI = package.Content.GetParamFloat();
                    ScoreList[4].KPI = package.Content.GetParamFloat();
                    ScoreList[5].KPI = package.Content.GetParamFloat();
                    ScoreList[6].KPI = package.Content.GetParamFloat();
                    ScoreList[7].KPI = package.Content.GetParamFloat();
                    ScoreList[8].KPI = package.Content.GetParamFloat();
                    ScoreList[9].KPI = package.Content.GetParamFloat();
                    ScoreList[10].KPI = package.Content.GetParamFloat();
                    ScoreList[11].KPI = package.Content.GetParamFloat();
                    ScoreList[12].KPI = package.Content.GetParamFloat();
                    ScoreList[13].KPI = package.Content.GetParamFloat();
                    ScoreList[14].KPI = package.Content.GetParamFloat();
                    ScoreList[15].KPI = package.Content.GetParamFloat();
                }
                else
                {
                    ScoreList[0].KPI = package.Content.GetParamFloat();
                    ScoreList[1].KPI = package.Content.GetParamFloat();
                    ScoreList[2].KPI = package.Content.GetParamFloat();
                    ScoreList[3].KPI = package.Content.GetParamFloat();
                    ScoreList[4].KPI = package.Content.GetParamFloat();
                    ScoreList[5].KPI = package.Content.GetParamFloat();
                    ScoreList[6].KPI = package.Content.GetParamFloat();
                    ScoreList[7].KPI = package.Content.GetParamFloat();
                    ScoreList[8].KPI = package.Content.GetParamFloat();
                    ScoreList[9].KPI = package.Content.GetParamFloat();
                    ScoreList[10].KPI = package.Content.GetParamFloat();
                    ScoreList[11].KPI = package.Content.GetParamFloat();
                    ScoreList[12].KPI = package.Content.GetParamFloat();
                    ScoreList[13].KPI = package.Content.GetParamFloat();
                }
            }
            catch
            {
                //continue
            }
        }

        private void GetIndicatorThresholdInf()
        {
            if (ScoreList != null)
            {
                IndicatorThresholdInfProcessor thresholdProcessor = IndicatorThresholdInfProcessor.Ini();
                for (int i = 0; i < ScoreList.Count; i++)
                {
                    setScoreList(thresholdProcessor, i);
                }
            }
        }

        private void setScoreList(IndicatorThresholdInfProcessor thresholdProcessor, int i)
        {
            if (IsTDIn)
            {
                for (int j = 0; j < thresholdProcessor.TDTestThreadholdInf.ThresholdInf.Length; j++)
                {
                    if (ScoreList[i].Name == thresholdProcessor.TDTestThreadholdInf.ThresholdInf[j].Name)
                    {
                        ScoreList[i].GetScore(thresholdProcessor.TDTestThreadholdInf.ThresholdInf[j]);
                        break;
                    }
                }
            }
            else
            {
                for (int j = 0; j < thresholdProcessor.GSMTestThreadholdInf.ThresholdInf.Length; j++)
                {
                    if (ScoreList[i].Name == thresholdProcessor.GSMTestThreadholdInf.ThresholdInf[j].Name)
                    {
                        ScoreList[i].GetScore(thresholdProcessor.GSMTestThreadholdInf.ThresholdInf[j]);
                        break;
                    }
                }
            }
        }

        private void IniIndicatorList()
        {
            if (IsTDIn)
            {
                ScoreList = new List<Indicator>();
                ScoreList.Add(new Indicator("平均车速"));
                ScoreList.Add(new Indicator("接通率"));
                ScoreList.Add(new Indicator("掉话率"));
                ScoreList.Add(new Indicator("切换成功率"));

                ScoreList.Add(new Indicator("里程互操作比"));
                ScoreList.Add(new Indicator("85覆盖率"));
                ScoreList.Add(new Indicator("85覆盖率(C/I>-3)"));
                ScoreList.Add(new Indicator("DPCH覆盖率"));

                ScoreList.Add(new Indicator("导频污染比"));
                ScoreList.Add(new Indicator("语音BLER"));
                ScoreList.Add(new Indicator("MOS2.8占比"));
                ScoreList.Add(new Indicator("每呼切换数"));

                ScoreList.Add(new Indicator("TD时长占比"));
                ScoreList.Add(new Indicator("HSPA时长占比"));
                ScoreList.Add(new Indicator("FTP平均速率"));
                ScoreList.Add(new Indicator("FTP下载速率占比"));
            }
            else
            {
                ScoreList = new List<Indicator>();
                ScoreList.Add(new Indicator("平均车速"));
                ScoreList.Add(new Indicator("接通率"));
                ScoreList.Add(new Indicator("掉话率"));
                ScoreList.Add(new Indicator("切换成功率"));

                ScoreList.Add(new Indicator("90覆盖率"));
                ScoreList.Add(new Indicator("质量0-4级占比"));
                ScoreList.Add(new Indicator("MOS2.8以上占比"));
                ScoreList.Add(new Indicator("半速率占比"));

                ScoreList.Add(new Indicator("GSM语音质量"));
                ScoreList.Add(new Indicator("质差路段占比"));
                ScoreList.Add(new Indicator("每呼切换次数"));
                ScoreList.Add(new Indicator("FTP下载速率"));

                ScoreList.Add(new Indicator("高编码占比"));
                ScoreList.Add(new Indicator("BLER占比"));
            }
        }

        public override string Name
        {
            get { return "ScoreQuery"; }
        }

        private static string GSMSqlStetement = @"     
            select        
            case when sum(duration) = 0 then -10000 else sum(distance)/((convert(float,sum(duration))/3600)*1000) end as '平均车速',
            case when sum(attcallnum) = 0 then -10000 else convert(float,sum(attcallnum - disconnnum))/sum(attcallnum) end as '接通率',
            case when sum(attcallnum) = 0 then -10000 else convert(float,sum(dropcallnum))/(2*sum(attcallnum)) end as '掉话率',
            case when sum(succhandover+failhandover) = 0 then -10000 else convert(float,sum(succhandover))/sum(succhandover+failhandover) end as '切换成功率',
            case when sum(covernum) = 0 then -10000 else convert(float,sum([90covernum]))/sum(covernum) end as '90覆盖率',
            case when sum(rxqualnum) = 0 then -10000 else convert(float,sum([4rxqualnum]))/sum(rxqualnum) end as '质量0-4级占比',
            case when sum(mosnum) = 0 then -10000 else convert(float,sum(mos28num))/sum(mosnum) end as 'MOS2.8以上占比',
            case when sum(hrsample+frsample) = 0 then -10000 else convert(float,sum(hrsample))/sum(hrsample+frsample) end as '半速率占比',
            case when sum(rxqualsample) = 0 or sum(rxqualnum) = 0 then -10000 else (convert(float,sum(rxqualmos))/sum(rxqualsample))*(convert(float,sum([5rxqualnum]))/sum(rxqualnum)) end as 'GSM语音质量',
            case when sum(distance) = 0 then -10000 else convert(float,sum(weakqualdis))/sum(distance) end as '质差路段占比',
            case when sum(attcallnum-disconnnum) = 0 then -10000 else convert(float,sum(succhandover))/(2*sum(attcallnum-disconnnum)) end as '每呼切换次数',
            case when sum(ftptime) = 0 then -10000 else convert(float,sum(ftpsize))/sum(ftptime) end as 'FTP下载速率',
            case when sum(speedcode) = 0 then -10000 else convert(float,sum(highspeedcode))/sum(speedcode) end as '高编码占比',
            case when sum(blernum) = 0 then -10000 else convert(float,sum(blernum))/sum(blersample) end as 'BLER占比'
            from [DTASYSTEM].[dbo].[tb_popkpi_gsm_score] as a
            join [DTASYSTEM].[dbo].[tb_cfg_static_dbsetting] as b on a.dbid = b.id
            where distance > 0 and cityname = '#cityname'
            and 
            dateadd(ss,(stime),'1970-1-1 8:00:00') >= '#stime' 
            and dateadd(ss,(etime),'1970-1-1 8:00:00') < '#etime'
            group by cityname
            order by cityname 
        ";
        private static string TDSqlStetement = @"
            --TD汇总查询   
            select           
            case when sum(duration) = 0 then -10000 else sum(distance)/((convert(float,sum(duration))/3600)*1000) end as '平均车速',
            case when sum(attcallnum) = 0 then  -10000 else convert(float,sum(attcallnum - disconnnum))/sum(attcallnum) end as '接通率',
            case when sum(attcallnum) = 0 then  -10000 else convert(float,sum(dropcallnum))/(2*sum(attcallnum)) end as '掉话率',
            case when sum(succhandover+failhandover) = 0 then  -10000 else convert(float,sum(succhandover))/sum(succhandover+failhandover) end as '切换成功率',
            case when sum(tgevent) = 0 then  1000 else convert(float,sum(distance)/1000000)/sum(tgevent) end as '里程互操作比',
            case when sum(covernum) = 0 then  -10000 else convert(float,sum([85covernum]))/sum(covernum) end as '85覆盖率',
            case when sum(covernum) = 0 then  -10000 else convert(float,sum([85-3covernum]))/sum(covernum) end as '85覆盖率(C/I>-3)',
            case when sum(dpchsample) = 0 then  -10000 else convert(float,sum(dpch0sample))/sum(dpchsample) end as 'DPCH覆盖率',
            case when sum(pccpchnum) = 0 then  -10000 else convert(float,sum(pccpchpronum))/sum(pccpchnum) end as '导频污染比',
            case when sum(blersample) = 0 then  -10000 else convert(float,sum(highblersample))/sum(blersample) end as '语音BLER',
            case when sum(mossample) = 0 then  -10000 else convert(float,sum(mos28sample))/sum(mossample) end as 'MOS2.8占比',
            case when sum(attcallnum-disconnnum) = 0 then  -10000 else convert(float,sum(succhandover))/(2*sum(attcallnum-disconnnum)) end as '每呼切换数',
            case when sum(duration) = 0 then  -10000 else convert(float,sum(tdduration))/sum(duration) end as 'TD时长占比',
            case when sum(dataduraution) = 0 then  -10000 else convert(float,sum(hspaduraution))/sum(dataduraution) end as 'HSPA时长占比',
            case when sum(ftptime) = 0 then  -10000 else convert(float,sum(ftpsize))/sum(ftptime) end as 'FTP平均速率',
            case when sum(ftpsample) = 0 then  -10000 else convert(float,sum(ftp500sample))/sum(ftpsample) end as 'FTP下载速率占比'
            from [DTASYSTEM].[dbo].[tb_popkpi_td_score] as a
            join [DTASYSTEM].[dbo].[tb_cfg_static_dbsetting] as b on a.dbid = b.id
            where distance > 0 and cityname = '#cityname' and 
            dateadd(ss,(stime),'1970-1-1 8:00:00') >= '#stime' 
            and dateadd(ss,(etime),'1970-1-1 8:00:00') < '#etime'
            group by cityname
            order by cityname
        ";
    }
}
