﻿namespace MasterCom.RAMS.Func.PopShow
{
    partial class kpiColorCfgForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(kpiColorCfgForm));
            this.comboBoxTable = new System.Windows.Forms.ComboBox();
            this.listColumn = new System.Windows.Forms.ListView();
            this.columnIdx = new System.Windows.Forms.ColumnHeader();
            this.columnColName = new System.Windows.Forms.ColumnHeader();
            this.columnType = new System.Windows.Forms.ColumnHeader();
            this.columnDesc = new System.Windows.Forms.ColumnHeader();
            this.columnEXP = new System.Windows.Forms.ColumnHeader();
            this.btnModify = new System.Windows.Forms.Button();
            this.txtMaxR = new System.Windows.Forms.TextBox();
            this.txtMinR = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.colorRangeScreen1 = new MasterCom.MControls.ColorRangeScreen();
            this.label1 = new System.Windows.Forms.Label();
            this.btnSaveToDB = new System.Windows.Forms.Button();
            this.btnCompetition = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // comboBoxTable
            // 
            this.comboBoxTable.FormattingEnabled = true;
            this.comboBoxTable.Location = new System.Drawing.Point(132, 10);
            this.comboBoxTable.Name = "comboBoxTable";
            this.comboBoxTable.Size = new System.Drawing.Size(248, 20);
            this.comboBoxTable.TabIndex = 0;
            this.comboBoxTable.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // listColumn
            // 
            this.listColumn.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnIdx,
            this.columnColName,
            this.columnType,
            this.columnDesc});
            this.listColumn.FullRowSelect = true;
            this.listColumn.HideSelection = false;
            this.listColumn.Location = new System.Drawing.Point(13, 39);
            this.listColumn.MultiSelect = false;
            this.listColumn.Name = "listColumn";
            this.listColumn.Size = new System.Drawing.Size(466, 226);
            this.listColumn.TabIndex = 7;
            this.listColumn.UseCompatibleStateImageBehavior = false;
            this.listColumn.View = System.Windows.Forms.View.Details;
            this.listColumn.SelectedIndexChanged += new System.EventHandler(this.listColumn_SelectedIndexChanged);
            // 
            // columnIdx
            // 
            this.columnIdx.Text = "";
            this.columnIdx.Width = 26;
            // 
            // columnColName
            // 
            this.columnColName.Text = "字段名";
            this.columnColName.Width = 123;
            // 
            // columnType
            // 
            this.columnType.DisplayIndex = 3;
            this.columnType.Text = "报表名称";
            this.columnType.Width = 166;
            // 
            // columnDesc
            // 
            this.columnDesc.DisplayIndex = 2;
            this.columnDesc.Text = "描述";
            this.columnDesc.Width = 140;
            // 
            // columnEXP
            // 
            this.columnEXP.Text = "公式";
            this.columnEXP.Width = 178;
            // 
            // btnModify
            // 
            this.btnModify.Location = new System.Drawing.Point(386, 271);
            this.btnModify.Name = "btnModify";
            this.btnModify.Size = new System.Drawing.Size(92, 23);
            this.btnModify.TabIndex = 26;
            this.btnModify.Text = "修改";
            this.btnModify.UseVisualStyleBackColor = true;
            this.btnModify.Click += new System.EventHandler(this.button1_Click);
            // 
            // txtMaxR
            // 
            this.txtMaxR.Location = new System.Drawing.Point(174, 304);
            this.txtMaxR.Name = "txtMaxR";
            this.txtMaxR.Size = new System.Drawing.Size(65, 21);
            this.txtMaxR.TabIndex = 31;
            // 
            // txtMinR
            // 
            this.txtMinR.Location = new System.Drawing.Point(84, 304);
            this.txtMinR.Name = "txtMinR";
            this.txtMinR.Size = new System.Drawing.Size(65, 21);
            this.txtMinR.TabIndex = 32;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(153, 308);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(17, 12);
            this.label9.TabIndex = 30;
            this.label9.Text = "至";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(25, 309);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 12);
            this.label6.TabIndex = 29;
            this.label6.Text = "KPI值阈:";
            // 
            // colorRangeScreen1
            // 
            this.colorRangeScreen1.ColorRanges = ((System.Collections.Generic.List<MasterCom.MControls.ColorRange>)(resources.GetObject("colorRangeScreen1.ColorRanges")));
            this.colorRangeScreen1.Location = new System.Drawing.Point(13, 271);
            this.colorRangeScreen1.Name = "colorRangeScreen1";
            this.colorRangeScreen1.RangeMode = true;
            this.colorRangeScreen1.Size = new System.Drawing.Size(367, 23);
            this.colorRangeScreen1.TabIndex = 33;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(13, 13);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(113, 12);
            this.label1.TabIndex = 34;
            this.label1.Text = "请选择要设置的报表";
            // 
            // btnSaveToDB
            // 
            this.btnSaveToDB.Location = new System.Drawing.Point(386, 304);
            this.btnSaveToDB.Name = "btnSaveToDB";
            this.btnSaveToDB.Size = new System.Drawing.Size(92, 23);
            this.btnSaveToDB.TabIndex = 35;
            this.btnSaveToDB.Text = "保存到数据库";
            this.btnSaveToDB.UseVisualStyleBackColor = true;
            this.btnSaveToDB.Click += new System.EventHandler(this.saveToDB_Click);
            // 
            // btnCompetition
            // 
            this.btnCompetition.Location = new System.Drawing.Point(271, 304);
            this.btnCompetition.Name = "btnCompetition";
            this.btnCompetition.Size = new System.Drawing.Size(109, 23);
            this.btnCompetition.TabIndex = 36;
            this.btnCompetition.Text = "三网对比颜色设置";
            this.btnCompetition.UseVisualStyleBackColor = true;
            this.btnCompetition.Visible = false;
            this.btnCompetition.Click += new System.EventHandler(this.btnCompetition_Click);
            // 
            // kpiColorCfgForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(490, 337);
            this.Controls.Add(this.btnCompetition);
            this.Controls.Add(this.btnSaveToDB);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.colorRangeScreen1);
            this.Controls.Add(this.txtMaxR);
            this.Controls.Add(this.txtMinR);
            this.Controls.Add(this.label9);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.btnModify);
            this.Controls.Add(this.listColumn);
            this.Controls.Add(this.comboBoxTable);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "kpiColorCfgForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "KPI颜色分段设置";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ComboBox comboBoxTable;
        private System.Windows.Forms.ListView listColumn;
        private System.Windows.Forms.ColumnHeader columnIdx;
        private System.Windows.Forms.ColumnHeader columnColName;
        private System.Windows.Forms.ColumnHeader columnType;
        private System.Windows.Forms.ColumnHeader columnDesc;
        private System.Windows.Forms.ColumnHeader columnEXP;
        private System.Windows.Forms.Button btnModify;
        private System.Windows.Forms.TextBox txtMaxR;
        private System.Windows.Forms.TextBox txtMinR;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label6;
        private MasterCom.MControls.ColorRangeScreen colorRangeScreen1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnSaveToDB;
        private System.Windows.Forms.Button btnCompetition;
        //private MasterCom.MControls.ColorRangeScreen colorRangeScreen1;
    }
}