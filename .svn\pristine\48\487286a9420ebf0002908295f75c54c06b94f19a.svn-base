﻿using System;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLteNBCellCheckAnaSetForm : BaseDialog
    {
        bool isLteFdd = false;
        public ZTLteNBCellCheckAnaSetForm(bool isLteFdd)
        {
            this.isLteFdd = isLteFdd;
            InitializeComponent();
            if (isLteFdd)
            {
                groupBox1.Text = "CSFB下WCDMA邻区检测设置";
                checkEditShowGSMNbCell.Text = "只显示WCDMA邻区";
            }
        }

        public ZTLteNBCellCheckCondition GetCondition()
        {
            ZTLteNBCellCheckCondition condition = new ZTLteNBCellCheckCondition();
            condition.SampleCount = (int)numSampleCount.Value;
            condition.RSRP = (int)numRSRP.Value;
            condition.Distance = (int)numDistance.Value;
            condition.TimeSpan = (int)numTimeSpan.Value;
            condition.IsShowGSMNbCellOnly = checkEditShowGSMNbCell.Checked;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
