using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.Func
{
    public partial class TMSettingDlg : BaseForm
    {
        public TMSettingDlg(TMCondition condition)
        {
            tmCondition = condition;
            InitializeComponent();
            rangeSetSetting1.RangeAll = new Range(-50, true, 50, true);
            rangeSetSetting1.DecimalPlaces = 2;
            if (tmCondition == null)
            {
                tmCondition = new TMCondition();
                tmCondition.RangeValues = getAutoRangeSet();
            }
            rangeSetSetting1.RangeSet = tmCondition.RangeValues;
            rangeSetSetting1.AutoRangeSet = getAutoRangeSet();
            setCondition(tmCondition);
        }

        TMCondition tmCondition;
        private void setCondition(TMCondition condition)
        {
            if (condition != null)
            {
                foreach (CheckBox chk in groupBoxTM.Controls)
                {
                    foreach (string str in condition.TMList)
                    {
                        if (chk.Text == str)
                        {
                            chk.Checked = true;
                        }
                    }
                }
                this.Invalidate();

            }
        }

        public TMCondition GetCondition()
        {
            tmCondition = new TMCondition();
            tmCondition.RangeValues = rangeSetSetting1.RangeSet;

            foreach (CheckBox chk in groupBoxTM.Controls)
            {
                if (chk.Checked)
                {
                    tmCondition.TMList.Add(chk.Text);
                }
            }
            tmCondition.TMList.Sort();
            return tmCondition;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.Dispose();
        }
        private RangeSet getAutoRangeSet()
        {
            RangeSet set = new RangeSet();
            set.Add(new Range(-50.00, true, -3.00, false));
            set.Add(new Range(-3.00, true, 3.00, false));
            set.Add(new Range(3.00, true, 50.00, true));
            return set;
        }
    }


}