﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using System.IO;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRLowSpeedCauseDlg : BaseDialog
    {
        public NRLowSpeedCauseDlg()
        {
            InitializeComponent();
        }

        public NRLowSpeedCauseCondition Condition
        {
            get;
            private set;
        }

        public NRLowSpeedCauseDlg(NRLowSpeedCauseCondition cond)
            : this()
        {
            if (cond == null)
            {
                cond = new NRLowSpeedCauseCondition();
                cond.LoadCfg();
            }
            this.Condition = cond;
            init(cond);
        }

        private void init(NRLowSpeedCauseCondition cond)
        {
            numFTPMax.Value = (decimal)cond.FTPRateMax;
            numFTPMax.ValueChanged += numSpeedMax_ValueChanged;
            chkFTP.Checked = cond.CheckFTP;
            chkFTP.CheckedChanged += chkFTP_CheckedChanged;

            numHTTPMax.Value = (decimal)cond.HTTPRateMax;
            numHTTPMax.ValueChanged += numHTTPMax_ValueChanged;
            chkHttp.Checked = cond.CheckHTTP;
            chkHttp.CheckedChanged += chkHttp_CheckedChanged;

            numEmailMax.Value = (decimal)cond.EmailRateMax;
            numEmailMax.ValueChanged += numEmailMax_ValueChanged;
            chkEmail.Checked = cond.CheckEmail;
            chkEmail.CheckedChanged += chkEmail_CheckedChanged;

            numSampleCnt.Value = (decimal)cond.TestPointCountMin;
            numSampleCnt.ValueChanged += numSampleCnt_ValueChanged;

            numDistance.Value = (decimal)cond.DistanceMin;
            numDistance.ValueChanged += numDistance_ValueChanged;

            numSecond.Value = (decimal)cond.SecondMin;
            numSecond.ValueChanged += numSecond_ValueChanged;

            initNaviTree(cond);

            coverPnl1.LinkCondition(cond);
            qualPnl.LinkCondition(cond);
        }


        void numSecond_ValueChanged(object sender, EventArgs e)
        {
            Condition.SecondMin = (double)numSecond.Value;
        }

        void numDistance_ValueChanged(object sender, EventArgs e)
        {
            Condition.DistanceMin = (double)numDistance.Value;
        }

        void chkEmail_CheckedChanged(object sender, EventArgs e)
        {
            Condition.CheckEmail = chkEmail.Checked;
        }

        void numEmailMax_ValueChanged(object sender, EventArgs e)
        {
            Condition.EmailRateMax = (double)numEmailMax.Value;
        }

        void chkHttp_CheckedChanged(object sender, EventArgs e)
        {
            Condition.CheckHTTP = chkHttp.Checked;
        }

        void numHTTPMax_ValueChanged(object sender, EventArgs e)
        {
            Condition.HTTPRateMax = (double)numHTTPMax.Value;
        }

        void chkFTP_CheckedChanged(object sender, EventArgs e)
        {
            Condition.CheckFTP = chkFTP.Checked;
        }

        void numSampleCnt_ValueChanged(object sender, EventArgs e)
        {
            Condition.TestPointCountMin = (int)numSampleCnt.Value;
        }

        void numSpeedMax_ValueChanged(object sender, EventArgs e)
        {
            Condition.FTPRateMax = (float)numFTPMax.Value;
        }

        private void initNaviTree(NRLowSpeedCauseCondition cond)
        {
            tvReason.Nodes.Clear();
            foreach (NRLowSpeedCauseBase r in cond.Causes)
            {
                TreeNode node = createNode(r);
                if (node != null)
                {
                    tvReason.Nodes.Add(node);
                }
            }
        }

        private TreeNode createNode(NRLowSpeedCauseBase r)
        {
            if (r == null)
            {
                return null;
            }
            TreeNode node = new TreeNode(r.Name);
            node.Tag = r;
            if (r.SubCauses != null)
            {
                foreach (NRLowSpeedCauseBase subR in r.SubCauses)
                {
                    TreeNode subNode = createNode(subR);
                    if (subNode != null)
                    {
                        node.Nodes.Add(subNode);
                    }
                }
            }
            return node;
        }

        private void tvReason_AfterSelect(object sender, TreeViewEventArgs e)
        {
            checkBtnStatus();
        }

        private void checkBtnStatus()
        {
            TreeNode node = tvReason.SelectedNode;
            if (node == null || node.Tag == null)
            {
                btnUp.Enabled = btnDown.Enabled = false;
            }
            else
            {
                btnUp.Enabled = node.PrevNode != null;
                btnDown.Enabled = node.NextNode != null;
            }
        }

        private void btnUp_Click(object sender, EventArgs e)
        {
            moveNode(tvReason.SelectedNode, true);
        }

        private void btnDown_Click(object sender, EventArgs e)
        {
            moveNode(tvReason.SelectedNode, false);
        }

        private void moveNode(TreeNode node, bool isUp)
        {
            if (node == null)
            {
                MessageBox.Show("请选择需要调整位置的节点");
                return;
            }
            else if (node.Tag == null)
            {
                MessageBox.Show("该节点不支持调整位置");
                return;
            }

            int newIdx = isUp ? node.PrevNode.Index : node.NextNode.Index;
            TreeNodeCollection parent = node.Parent != null ? node.Parent.Nodes : tvReason.Nodes;
            node.Remove();
            parent.Insert(newIdx, node);
            tvReason.SelectedNode = node;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            Condition.Causes.Clear();
            foreach (TreeNode node in tvReason.Nodes)
            {
                Condition.Causes.Add(getCauseBase(node));
            }
            DialogResult = DialogResult.OK;
            Condition.Save();
        }

        private NRLowSpeedCauseBase getCauseBase(TreeNode node)
        {
            NRLowSpeedCauseBase cause = node.Tag as NRLowSpeedCauseBase;
            if (cause.SubCauses != null)
            {
                cause.SubCauses.Clear();
            }
            foreach (TreeNode subNode in node.Nodes)
            {
                cause.SubCauses.Add(getCauseBase(subNode));
            }
            return cause;
        }
    }
}
