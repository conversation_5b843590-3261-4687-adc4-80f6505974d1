﻿using MasterCom.MTGis;
using System.Collections.Generic;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsSampleRateStater : NbIotMgrsStaterBase
    {
        protected NbIotMgrsFuncItem tmpFuncItem;

        public override void DoStat(NbIotMgrsFuncItem curFuncItem)
        {
            this.tmpFuncItem = curFuncItem;
        }

        public virtual List<CarrierSampleRate> GetViews()
        {
            resultList = new Dictionary<string, string>();
            List<CarrierSampleRate> carrierList = new List<CarrierSampleRate>();
            CarrierSampleRate ct = new CarrierSampleRate();
            ct.Name = "中国移动";
            ct.AreaRateViews = dealWithData(cmCarrierAreaResult.GridList, MasterCom.RAMS.Model.CarrierType.ChinaTelecom);
            carrierList.Add(ct);

            CarrierSampleRate cu = new CarrierSampleRate();
            cu.Name = "中国联通";
            cu.AreaRateViews = dealWithData(cuCarrierAreaResult.GridList, MasterCom.RAMS.Model.CarrierType.ChinaUnicom);
            carrierList.Add(cu);

            CarrierSampleRate cm = new CarrierSampleRate();
            cm.Name = "中国电信";
            cm.AreaRateViews = dealWithData(ctCarrierAreaResult.GridList, MasterCom.RAMS.Model.CarrierType.ChinaMobile);
            carrierList.Add(cm);

            return carrierList;
        }

        protected virtual List<AreaSampleRate> dealWithData(Dictionary<string, List<ScanGridInfo>> gridList, MasterCom.RAMS.Model.CarrierType carrierType)
        {
            //对栅格中的频点汇聚
            Dictionary<string, Dictionary<int, List<ScanGridInfo>>> areaGridList = new Dictionary<string, Dictionary<int, List<ScanGridInfo>>>();
            foreach (List<ScanGridInfo> grid in gridList.Values)
            {
                //先判断栅格所属区域
                string polygonName = "";
                addAreaGrid(areaGridList, grid, ref polygonName);

                //再判断栅格中的频点
                addAreaGridEARFCN(areaGridList, grid, polygonName);
            }

            //统计结果
            List<AreaSampleRate> areaGridDataList = new List<AreaSampleRate>();
            countAreaData(areaGridList, areaGridDataList);

            return areaGridDataList;
        }

        private void countAreaData(Dictionary<string, Dictionary<int, List<ScanGridInfo>>> areaGridList, List<AreaSampleRate> areaGridDataList)
        {
            foreach (var area in areaGridList)
            {
                List<NbIotMgrsSampleRateView> earfcnList = new List<NbIotMgrsSampleRateView>();
                AreaSampleRate areaGridData = new AreaSampleRate();
                areaGridData.Name = area.Key;
                int areaTotalCount = 0;
                foreach (var item in area.Value)
                {
                    areaTotalCount += item.Value.Count;
                }
                foreach (var item in area.Value)
                {
                    NbIotMgrsSampleRateView earfcnGrid = new NbIotMgrsSampleRateView(item.Key);
                    earfcnGrid.CellGrid = item.Value;
                    earfcnGrid.TotalGridCount = areaTotalCount;
                    earfcnList.Add(earfcnGrid);
                }
                areaGridData.SampleRateViews = earfcnList;
                areaGridDataList.Add(areaGridData);
            }
        }

        private void addAreaGridEARFCN(Dictionary<string, Dictionary<int, List<ScanGridInfo>>> areaGridList, List<ScanGridInfo> grid, string polygonName)
        {
            foreach (ScanGridInfo item in grid)
            {
                if (!areaGridList[polygonName].ContainsKey(item.EARFCN))
                {
                    //目前只保存了栅格序号,如果还需其他参数可以替换类型
                    List<ScanGridInfo> list = new List<ScanGridInfo>();
                    list.Add(item);
                    areaGridList[polygonName].Add(item.EARFCN, list);
                }
                else
                {
                    areaGridList[polygonName][item.EARFCN].Add(item);
                }
            }
        }

        private void addAreaGrid(Dictionary<string, Dictionary<int, List<ScanGridInfo>>> areaGridList, List<ScanGridInfo> grid, ref string polygonName)
        {
            foreach (MTPolygon polygon in selectPolygons)
            {
                if (polygon.CheckPointInRegion(grid[0].CentLng, grid[0].CentLat))
                {
                    polygonName = polygon.Name;
                    if (!areaGridList.ContainsKey(polygonName))
                    {
                        areaGridList[polygonName] = new Dictionary<int, List<ScanGridInfo>>();
                    }
                    break;
                }
            }
        }

        public override void SetResultControl()
        {
            NbIotMgrsSampleRateResult resultControl = new NbIotMgrsSampleRateResult();
            resultControl.FillData(tmpFuncItem);
            resultControlList = new List<NbIotMgrsResultControlBase>() { resultControl };
        }
    }

    public class CarrierSampleRate
    {
        public string Name
        {
            get;
            set;
        }

        public int TotalEarfcnCount
        {
            get
            {
                int result = 0;
                List<int> earfcn = new List<int>();
                foreach (AreaSampleRate area in AreaRateViews)
                {
                    foreach (var sample in area.SampleRateViews)
                    {
                        if (!earfcn.Contains(sample.Earfcn))
                        {
                            result += 1;
                            earfcn.Add(sample.Earfcn);
                        }
                    }
                }
                return result;
            }
        }

        public List<AreaSampleRate> AreaRateViews
        {
            get;
            set;
        }
    }

    public class AreaSampleRate
    {
        public string Name
        {
            get;
            set;
        }

        public int EarfcnCount
        {
            get
            {
                return SampleRateViews.Count;
            }
        }

        public List<NbIotMgrsSampleRateView> SampleRateViews
        {
            get;
            set;
        }
    }

    public class NbIotMgrsSampleRateView
    {
        public NbIotMgrsSampleRateView(int earfcn)
        {
            Earfcn = earfcn;
        }

        public int Earfcn
        {
            get;
            private set;
        }

        public int GridCount
        {
            get { return CellGrid.Count; }
        }

        public double GridRate
        {
            get { return TotalGridCount == 0 ? 0 : 1d * GridCount / TotalGridCount; }
        }

        public int TotalGridCount
        {
            get;
            set;
        }

        public List<ScanGridInfo> CellGrid
        {
            get;
            set;
        }

        public double CentLng
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].CentLng;
                }
                return 0;
            }
        }

        public double CentLat
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].CentLat;
                }
                return 0;
            }
        }
    }
}
