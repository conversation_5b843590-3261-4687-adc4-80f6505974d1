﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptAna_GZ : DIYAnalyseByCellBackgroundBaseByFile
    {
        public StationAcceptAutoSet_GZ FuncSet { get; set; } = new StationAcceptAutoSet_GZ();
        string fileNameKeyStr = "";
        string curDistrictName = "";
        CellFileAnaInfoBase_GZ fileAnaInfo;//当前文件的分析信息

        //当前站点的Volte文件
        readonly List<Model.FileInfo> curBtsVoLteFiles = new List<Model.FileInfo>();
        QueryCondition volteQueryCond;
        ReporterTemplate volteTpl;

        // Dictionary<地市, Dictionary<enodebid, BtsWorkParam_GZ>> 
        Dictionary<string, Dictionary<int, BtsWorkParam_GZ>> workParamSumDic = null;

        protected static readonly object lockObj = new object();
        private static StationAcceptAna_GZ instance = null;
        public static StationAcceptAna_GZ GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new StationAcceptAna_GZ();
                    }
                }
            }
            return instance;
        }
        protected StationAcceptAna_GZ()
            : base(MainModel.GetInstance())
        {
            funcVersion = 1.1;
            this.isIgnoreExport = true;
        }
        public override string Name
        {
            get { return "贵州单站验收"; }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22115, "查询");
        }

        public override void DealBeforeBackgroundQueryByCity()
        {
            workParamSumDic = GetWorkParamsHelper_GZ.GetWorkParamsInfo(FuncSet);

            volteTpl = ReporterTemplateManager.LoadSingleReportFromFile(StationAcceptPropertiesGZ_LTE.StrRptTitleHead);
            volteQueryCond = new QueryCondition();
            DateTime startTime = BackgroundFuncConfigManager.GetInstance().StartTime;
            DateTime endTime = BackgroundFuncConfigManager.GetInstance().EndTime;
            volteQueryCond.Periods.Add(new TimePeriod(startTime, endTime));
            volteQueryCond.Projects = BackgroundFuncConfigManager.GetInstance().ProjectTypeList;

            Columns = new List<string>();
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_APP_type");
            Columns.Add("lte_APP_Speed_Mb");
            Columns.Add("lte_PDCP_DL_Mb");
            Columns.Add("lte_PDCP_UL_Mb");
        }
        protected override bool getCondition()
        {
            curDistrictName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID).Replace("市", "");
            if (workParamSumDic == null || workParamSumDic.Count <= 0
                || !workParamSumDic.ContainsKey(curDistrictName))
            {
                reportBackgroundInfo("未读取到" + curDistrictName + "的待评估对象数据");
                return false;
            }
            MainModel.MainForm.GetMapForm().updateMap();
            volteQueryCond.DistrictID = MainModel.DistrictID;
            volteQueryCond.DistrictIDs.Clear();
            volteQueryCond.DistrictIDs.Add(MainModel.DistrictID);

            return base.getCondition();
        }
        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            ClientProxy clientProxy = new ClientProxy();

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            clientProxy.Close();

            Dictionary<int, BtsWorkParam_GZ> curDistrictWorkParam = workParamSumDic[curDistrictName.Replace("市", "")];
            if (curDistrictWorkParam != null)
            {
                reportBackgroundInfo("读取到" + curDistrictWorkParam.Count + "个站点的工参信息");

                foreach (BtsWorkParam_GZ btsInfo in curDistrictWorkParam.Values)
                {
                    if (MainModel.BackgroundStopRequest)
                    {
                        return;
                    }
                    dealBtsInfo(clientProxy, btsInfo);
                }
            }
        }

        private void dealBtsInfo(ClientProxy clientProxy, BtsWorkParam_GZ btsInfo)
        {
            LTEBTS bts = new LTEBTS();
            addCellInfoToCellManager(btsInfo, ref bts);

            reportBackgroundInfo(string.Format("开始读取基站 {0} 的待分析文件...", btsInfo.BtsName));
            StringBuilder strbFilter = new StringBuilder();
            foreach (CellWorkParam_GZ cellInfo in btsInfo.CellWorkParamDic.Values)
            {
                strbFilter.Append(string.Format("{0} or ", cellInfo.CellName));
            }
            if (strbFilter.Length > 4)
            {
                fileNameKeyStr = strbFilter.Remove(strbFilter.Length - 4, 4).ToString();

                doBackgroundStatByFile(clientProxy);

                reportBackgroundInfo(string.Format("开始读取基站 {0} 的预处理信息...", btsInfo.BtsName));
                doWithBtsInfo(btsInfo);//读取并导出站点指标

                foreach (LTECell cell in bts.Cells)
                {
                    MainModel.CellManager.Remove(cell);
                    foreach (LTEAntenna ant in cell.Antennas)
                    {
                        MainModel.CellManager.Remove(ant);
                    }
                }
                MainModel.CellManager.Remove(bts);
            }
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            curBtsVoLteFiles.Clear();
        }
        protected override bool filterFile(Model.FileInfo fileInfo)
        {
            string fileName = fileInfo.Name.Trim().ToUpper();
            if (fileName.Contains("VOLTE"))//VOLTE文件特殊处理
            {
                curBtsVoLteFiles.Add(fileInfo);
                return true;
            }
            else if (fileName.Contains("CQT") || fileName.Contains("_DT"))
            {
                return false;
            }
            return true;
        }
        protected override void doStatWithQuery()
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            foreach (DTFileDataManager dtFile in MainModel.DTDataManager.FileDataManagers)
            {
                StationAcceptFileAnaManager_GZ manager = new StationAcceptFileAnaManager_GZ();
                fileAnaInfo = manager.AnalyzeFile(dtFile);
            }
            MainModel.DTDataManager.Clear();
        }
        protected void doWithBtsInfo(BtsWorkParam_GZ btsInfo)
        {
            try
            {
                getBackgroundData();
                reportBackgroundInfo(string.Format("共读取到{0} 条预处理信息", BackgroundResultList.Count));
                BackgroundResultList.Sort(BackgroundResult.ComparerByISTimeDesc);

                BtsResultInfo btsResultInfo = new BtsResultInfo(btsInfo);
                foreach (BackgroundResult bgResult in BackgroundResultList)
                {
                    btsResultInfo.AddResult(bgResult);
                }

                List<CellKpiInfo_VOLTE> cellVolteKpiInfos = new List<CellKpiInfo_VOLTE>();
                List<NPOIRow> volteRows = new List<NPOIRow>();
                List<NPOIRow> cqtRows = new List<NPOIRow>();
                List<NPOIRow> dtRows = new List<NPOIRow>();

                reportBackgroundInfo(string.Format("查询到站点{0} 有{1}个Volte文件，开始统计其Volte指标信息", btsInfo.BtsName, curBtsVoLteFiles.Count));
                curBtsVoLteFiles.Sort(Model.FileInfo.GetCompareByBeginTimeDesc());//将该站的volte文件按时间倒叙排列
                foreach (CellResultInfo cellResult in btsResultInfo.CellResultInfoDic.Values)
                {
                    //统计各小区Volte指标信息
                    cellResult.KpiInfo_VOLTE.KpiInfo = statCellVolteKpi(cellResult.CellName, curBtsVoLteFiles);
                    cellVolteKpiInfos.Add(cellResult.KpiInfo_VOLTE);

                    cqtRows.Add(cellResult.GetNPOIRow_CQT());
                    dtRows.Add(cellResult.GetNPOIRow_DT());
                }
                QueryVolteFileKpi_GZ queryVolte = new QueryVolteFileKpi_GZ(volteTpl, null);
                volteRows.AddRange(queryVolte.CreateReport(volteTpl, cellVolteKpiInfos));

                exportBtsResult(btsInfo.BtsName, volteRows, cqtRows, dtRows);
            }
            catch (Exception ex)
            {
                reportBackgroundError(ex);
            }
            finally
            {
                curBtsVoLteFiles.Clear();
            }
        }
        protected void exportBtsResult(string btsName, List<NPOIRow> volteRows, List<NPOIRow> cqtRows
            , List<NPOIRow> dtRows)
        {
            reportBackgroundInfo(string.Format("开始导出站点{0} 的指标信息", btsName));
            string saveFolder = Path.Combine(this.FuncSet.ReportSavPath, btsName);
            if (Directory.Exists(saveFolder))
            {
                Directory.Delete(saveFolder, true);
                System.Threading.Thread.Sleep(1000);
            }
            Directory.CreateDirectory(saveFolder);

            CSVWriter.ExportToCsv(cqtRows, Path.Combine(saveFolder, string.Format("CQT_{0}.csv.temp", btsName)));
            CSVWriter.ExportToCsv(dtRows, Path.Combine(saveFolder, string.Format("DT_{0}.csv.temp", btsName)));
            CSVWriter.ExportToCsv(volteRows, Path.Combine(saveFolder, string.Format("VOLTE_{0}.csv.temp", btsName)));

            int tryCount = 0;
            bool uploadSuccess = ftpUploadFiles(btsName, saveFolder);
            while (tryCount < 3 && !uploadSuccess)
            {
                System.Threading.Thread.Sleep(2000);
                uploadSuccess = ftpUploadFiles(btsName, saveFolder);
                tryCount++;
            }
        }
        private bool ftpUploadFiles(string btsName, string saveFolder)
        {
            try
            {
                reportBackgroundInfo(string.Format("开始上传站点{0} 的指标信息", btsName));
                FtpHelper ftpHelper = new FtpHelper(FuncSet.FtpUserName, FuncSet.FtpUserPwd, FuncSet.FtpServerPath);
                if (!ftpHelper.CreatNewFtpFolder(btsName))
                {
                    return false;
                }

                List<string> tempFileNames = new List<string>();
                foreach (string localFilePath in Directory.GetFiles(saveFolder))//先上传带.temp后缀的文件
                {
                    string fileName = System.IO.Path.GetFileName(localFilePath);
                    ftpHelper.UpLoadFile(localFilePath, btsName);
                    tempFileNames.Add(fileName);
                }

                foreach (string tempFileName in tempFileNames)//再将.temp后缀去掉
                {
                    ftpHelper.RenameFtpFile(tempFileName, tempFileName.Replace(".temp", ""), btsName, false);
                }

                writeUploadLog(btsName);
            }
            catch (Exception ex)
            {
                reportBackgroundError(ex);
                return false;
            }
            return true;
        }
        private void writeUploadLog(string btsName)
        {
            try
            {
                string logPath = Path.Combine(this.FuncSet.ReportSavPath, "uploadLog.txt");
                if (!File.Exists(logPath))
                {
                    File.Create(logPath).Close();
                }
                using (StreamWriter sw = File.AppendText(logPath))
                {
                    sw.Write(string.Format("{0}   【{1}】【{2}】的指标报告已上传 \r\n", DateTime.Now.ToString()
                        , curDistrictName, btsName));
                    sw.Flush();
                    sw.Close();
                }
            }
            catch (Exception ex)
            {
                reportBackgroundError(ex);
            }
        }

        private StatInfoBase statCellVolteKpi(string cellName, List<Model.FileInfo> btsVoLteFiles)
        {
            Model.FileInfo fileNewest = null;

            #region 查询小区最新的一个volte文件
            foreach (Model.FileInfo file in btsVoLteFiles)
            {
                string fileName = file.Name.ToUpper();
                if (fileName.Contains(cellName.ToUpper()))
                {
                    fileNewest = file;
                }
            }
            if (fileNewest == null)
            {
                reportBackgroundInfo(string.Format("未查询到{0}小区的Volte文件！", cellName));
                return null;
            }
            #endregion

            reportBackgroundInfo(string.Format("开始统计小区{0}的Volte指标,统计文件为：{1}", cellName, fileNewest.Name));
            volteQueryCond.NameFilterType = FileFilterType.ByMark_ID;
            volteQueryCond.FileName = fileNewest.ID.ToString();

            Dictionary<string, StatInfoBase> fileKeyDataDic = new Dictionary<string, StatInfoBase>();
            QueryVolteFileKpi_GZ queryVolte = new QueryVolteFileKpi_GZ(volteTpl, fileKeyDataDic);
            queryVolte.SetQueryCondition(volteQueryCond);
            queryVolte.Query();

            StatInfoBase statInfo;
            if (fileKeyDataDic.TryGetValue(fileNewest.ID.ToString(), out statInfo))
            {
                statInfo.KPIData.FinalMtMoGroup();
                return statInfo;
            }
            else
            {
                reportBackgroundInfo(string.Format("未统计到Volte文件{0}的指标信息！", fileNewest.Name));
                return null;
            }
        }

        public override void DealAfterBackgroundQueryByCity()
        {
            if (workParamSumDic != null)
            {
                workParamSumDic.Clear();
            }
        }
        private void addCellInfoToCellManager(BtsWorkParam_GZ btsParam, ref LTEBTS bts)
        {
            int snapShotId = -1;

            #region 暂时动态添加工参到CellManager，稍后移除

            bts.Fill(snapShotId, 0, 2147483647);
            bts.Name = btsParam.BtsName;
            bts.BTSID = btsParam.ENodeBID;
            bts.Type = btsParam.IsOutDoor ? LTEBTSType.Outdoor : LTEBTSType.Indoor;

            foreach (CellWorkParam_GZ cellParamInfo in btsParam.CellWorkParamDic.Values)
            {
                bts.Longitude = cellParamInfo.Longitude;
                bts.Latitude = cellParamInfo.Latitude;

                snapShotId--;
                LTECell cell = new LTECell();
                cell.Fill(snapShotId, 0, 2147483647);
                cell.BelongBTS = bts;
                cell.Name = cellParamInfo.CellName;
                cell.TAC = cellParamInfo.Tac;
                cell.ECI = cellParamInfo.ECI;
                cell.CellID = cellParamInfo.CellID;
                cell.PCI = cellParamInfo.PCI;
                cell.EARFCN = cellParamInfo.EARFCN;
                bts.AddCell(cell);
                MainModel.CellManager.Add(cell);

                LTEAntenna antenna = new LTEAntenna();
                snapShotId--;
                antenna.Fill(snapShotId, 0, 2147483647);
                antenna.Cell = cell;
                antenna.Longitude = cellParamInfo.Longitude;
                antenna.Latitude = cellParamInfo.Latitude;
                antenna.Direction = (short)cellParamInfo.Direction;
            }
            MainModel.CellManager.Add(bts);
            #endregion
        }

        #region Background
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }
        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.单站验收; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                ignoreParamKeys.Clear();
                ignoreParamKeys.Add("ExportReportSet");

                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["ExportReportSet"] = FuncSet.Params;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("ExportReportSet"))
                {
                    FuncSet.Params = param["ExportReportSet"] as Dictionary<string, object>;
                }
            }
        }
        public override PropertiesControl Properties
        {
            get
            {
                return new StationAcceptPropertiesGZ_LTE(this);
            }
        }
        protected override void getFilesForAnalyse()
        {
            BackgroundFuncQueryManager.GetInstance().GetFilterFile_CellAccept(this.GetSubFuncID(), ServiceTypeString
                            , ((int)carrierID).ToString(), "strfilename", fileNameKeyStr);
        }
        protected override void saveBackgroundData()
        {
            List<BackgroundResult> resultList = new List<BackgroundResult>();
            int subFuncId = this.GetSubFuncID();
            if (fileAnaInfo != null && fileAnaInfo.LteCell != null)
            {
                BackgroundResult result = fileAnaInfo.ConvertToBackgroundResult();
                result.SubFuncID = subFuncId;
                result.ProjectString = BackgroundFuncConfigManager.GetInstance().ProjectType;

                resultList.Add(result);
                fileAnaInfo = null;

                //未匹配到目标小区或未获取到指标信息的文件信息暂不保留
                //（有可能是未更新工参信息导致的,或者DT上传下载文件是截取覆盖图用的，每次出报告都要重新查询回放）
                BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(subFuncId, curAnaFileInfo, resultList);
            }
        }

        protected override void getBackgroundData()
        {
            BackgroundFuncConfigManager bgConfigManager = BackgroundFuncConfigManager.GetInstance();
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetFilterResult_CellAccept(new BackgroundFuncQueryManager.CellAcceptCondition(bgConfigManager.ISTime, bgConfigManager.IETime, GetSubFuncID(), bgConfigManager.ProjectType, "fileName", fileNameKeyStr), Name, StatType);
        }
        protected override void initBackgroundImageDesc()
        {
            //
        }
        #endregion
    }
    public class StationAcceptAutoSet_GZ
    {
        public StationAcceptAutoSet_GZ()
        {
            IsAnaSpecifyBts = false;
            WorkParamBeforeDays_Begin = 2;
            WorkParamBeforeDays_End = 0;
        }

        /// <summary>
        /// 工参选择从excel导入时的地址
        /// </summary>
        public string CellParamFolderPath { get; set; }

        public string ReportSavPath
        {
            get
            {
                return Path.Combine(System.Windows.Forms.Application.StartupPath, "btsAccept");
            }
        }

        /// <summary>
        /// Ftp服务器上传路径
        /// </summary>
        public string FtpServerPath { get; set; }
        public string FtpUserName { get; set; }
        public string FtpUserPwd { get; set; }

        public bool IsAnaSpecifyBts { get; set; }//是否只分析指定的站点

        private string specifyBtsEnodeBids = "";//要指定分析的站点enodebid，多个enodebid用“,”隔开
        public string SpecifyBtsEnodeBids
        {
            get 
            {
                if (!string.IsNullOrEmpty(specifyBtsEnodeBids))
                {
                    return specifyBtsEnodeBids.Trim().Replace("，", ","); 
                }
                return specifyBtsEnodeBids;
            }
            set 
            { 
                specifyBtsEnodeBids = value;
            }
        }
        public int WorkParamBeforeDays_Begin { get; set; }
        public int WorkParamBeforeDays_End { get; set; }
        public DateTime WorkParamTime_Begin
        {
            get
            {
                return DateTime.Now.Date.AddDays(-1 * (WorkParamBeforeDays_Begin));
            }
        }
        public DateTime WorkParamTime_End
        {
            get
            {
                return DateTime.Now.Date.AddDays(-1 * (WorkParamBeforeDays_End - 1));
            }
        }

        public Dictionary<string, object> Params
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["CellParamFolderPath"] = this.CellParamFolderPath;
                param["FtpServerPath"] = this.FtpServerPath;
                param["IsAnaSpecifyBts"] = this.IsAnaSpecifyBts;
                param["SpecifyBtsEnodeBids"] = this.SpecifyBtsEnodeBids;
                param["WorkParamBeforeDays_Begin"] = this.WorkParamBeforeDays_Begin;
                param["WorkParamBeforeDays_End"] = this.WorkParamBeforeDays_End;
                param["FtpUserName"] = this.FtpUserName;
                param["FtpUserPwd"] = this.FtpUserPwd;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("CellParamFolderPath"))
                {
                    this.CellParamFolderPath = (string)param["CellParamFolderPath"];
                }
                if (param.ContainsKey("FtpServerPath"))
                {
                    this.FtpServerPath = (string)param["FtpServerPath"];
                }
                if (param.ContainsKey("IsAnaSpecifyBts"))
                {
                    this.IsAnaSpecifyBts = (bool)param["IsAnaSpecifyBts"];
                }
                if (param.ContainsKey("SpecifyBtsEnodeBids"))
                {
                    this.SpecifyBtsEnodeBids = (string)param["SpecifyBtsEnodeBids"];
                }
                if (param.ContainsKey("WorkParamBeforeDays_Begin"))
                {
                    this.WorkParamBeforeDays_Begin = (int)param["WorkParamBeforeDays_Begin"];
                }
                if (param.ContainsKey("WorkParamBeforeDays_End"))
                {
                    this.WorkParamBeforeDays_End = (int)param["WorkParamBeforeDays_End"];
                }
                if (param.ContainsKey("FtpUserName"))
                {
                    this.FtpUserName = (string)param["FtpUserName"];
                }
                if (param.ContainsKey("FtpUserPwd"))
                {
                    this.FtpUserPwd = (string)param["FtpUserPwd"];
                }
            }
        }
    }
}
