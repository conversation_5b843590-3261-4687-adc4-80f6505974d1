﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NROverCoverPnl : UserControl
    {
        public NROverCoverPnl()
        {
            InitializeComponent();
        }

        NROverCoverCause mainReason = null;
        public void LinkCondition(NROverCoverCause reason)
        {
            if (reason == null)
            {
                return;
            }
            this.mainReason = reason;

            numRSRPMin.Value = (decimal)mainReason.RSRPMin;
            numRSRPMin.ValueChanged += numRSRPMin_ValueChanged;
        }

        void numRSRPMin_ValueChanged(object sender, EventArgs e)
        {
            mainReason.RSRPMin = (float)numRSRPMin.Value;
        }
    }
}
