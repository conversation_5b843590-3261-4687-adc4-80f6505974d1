﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using System.Xml;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Model.Interface
{
    public partial class DIYReplayContentSettingDlg : BaseDialog
    {
        List<ColumnDefItem> curServiceAllParamList = new List<ColumnDefItem>();
        public DIYReplayContentSettingDlg()
        {
            InitializeComponent();
            initPrepare();

#if AllRtpMsg
            cbxAllRtpMsgInclude.Visible = true;
#endif
            txtFilterAvailable.TextChanged += txtFilterAvailable_TextChanged;
            txtFilterSelected.TextChanged += txtFilterSelected_TextChanged;
        }

        private void initPrepare()
        {
            cbxSvType.Items.Clear();
            //////////////////////////////////////////////////////////////////////////
            //注意!!!!!!!!!!!!!!!!!!!!!!!!!：如更改如下，注意更改switchShowServiceType代码
            //////////////////////////////////////////////////////////////////////////
            cbxSvType.Items.Add(new ServiceSampleTable("GSM语音", "tb_dtgsm_sample6", 1));
            cbxSvType.Items.Add(new ServiceSampleTable("GSM数据", "tb_dtgsm_sample5", 2));
            cbxSvType.Items.Add(new ServiceSampleTable("TDSCDMA语音及视频", "tb_dttdscdma_sample6", 4));
            cbxSvType.Items.Add(new ServiceSampleTable("TDSCDMA数据", "tb_dttdscdma_sample5", 5));
            cbxSvType.Items.Add(new ServiceSampleTable("WCDMA语音及视频", "tb_dtwcdma_sample", 10));
            cbxSvType.Items.Add(new ServiceSampleTable("WCDMA数据", "tb_dtwcdma_sample5", 11));
            cbxSvType.Items.Add(new ServiceSampleTable("CDMA 2G语音", "tb_dtcdma_sample", 6));
            cbxSvType.Items.Add(new ServiceSampleTable("CDMA 2G数据", "tb_dtcdma_sample5", 7));
            cbxSvType.Items.Add(new ServiceSampleTable("EVDO数据", "tb_dtevdo_sample5", 9));
            cbxSvType.Items.Add(new ServiceSampleTable("MTR", "tb_ulgsm_sample", 24));
            cbxSvType.Items.Add(new ServiceSampleTable("GSM扫频", "tb_scan_sample2", 12));
            cbxSvType.Items.Add(new ServiceSampleTable("TD扫频", "tb_scan_td_sample", 19));
            cbxSvType.Items.Add(new ServiceSampleTable("WCDMA扫频", "tb_scan_w_sample", 21));
            cbxSvType.Items.Add(new ServiceSampleTable("WLAN", "tb_dtwlan_sample", 31));
            cbxSvType.Items.Add(new ServiceSampleTable("LTE-TDD", "tb_dtlte_sample", 33));
            cbxSvType.Items.Add(new ServiceSampleTable("LTE-FDD", "tb_dtlte_fdd_sample", 45));
            cbxSvType.Items.Add(new ServiceSampleTable("LTE扫频TopN", "tb_scan_lte_sample", 35));
            cbxSvType.Items.Add(new ServiceSampleTable("LTE感知", "tb_lte_uep_sample", 44));
            cbxSvType.Items.Add(new ServiceSampleTable("扫频CW测量模式", "tb_scan_cw_measure_sample", 36));
            cbxSvType.Items.Add(new ServiceSampleTable("扫频频谱模式", "tb_scan_FreqSpectrum_sample", 37));
            cbxSvType.Items.Add(new ServiceSampleTable("LTE-Signal", "tb_dtsignal_sample", 50));
            cbxSvType.Items.Add(new ServiceSampleTable("NB-IoT扫频_TopN", "tb_scan_nbiot_topn_sample", 55));
            cbxSvType.Items.Add(new ServiceSampleTable("NR数据", "tb_cqtnr_sample", 57));
            cbxSvType.Items.Add(new ServiceSampleTable("NR扫频", "tb_scan_nr_sample", 71));
            //////////////////////////////////////////////////////////////////////////
            //注意!!!!!!!!!!!!!!!!!!!!!!!!!：如更改如下，注意更改switchShowServiceType代码
            //////////////////////////////////////////////////////////////////////////
            cbxSvType.SelectedIndex = 0;
        }


        internal void FillAllReplayOptions(List<DIYReplayContentOption> curOptionList, string curDescFilter)
        {
            listAllOptions.Items.Clear();
            foreach(DIYReplayContentOption option in curOptionList)
            {
                listAllOptions.Items.Add(option);
            }
            if (listAllOptions.Items.Count > 0)
            {
                listAllOptions.SelectedIndex = 0;
            }
            btnApply.Visible = curDescFilter == "";
            btnSave.Visible = curDescFilter == "";
        }
        internal void FillAllReplayOptions(List<DIYReplayContentOption> curOptionList, string curDescFilter, DIYReplayContentOption curSelect)
        {
            FillAllReplayOptions(curOptionList, curDescFilter);
            listAllOptions.SelectedItem = curSelect;
        }

        private void listAllOptions_SelectedIndexChanged(object sender, EventArgs e)
        {
            txtFilterAvailable.Text = string.Empty;
            txtFilterSelected.Text = string.Empty;

            DIYReplayContentOption option = listAllOptions.SelectedItem as DIYReplayContentOption;
            btnDelOption.Enabled = option != null;
            if (option != null)
            {
                tbxName.Text = option.Name;
                tbxDesc.Text = option.Desc;
                cbxEventInclude.Checked = option.EventInclude;
                cbxMsgInclude.Checked = option.MessageInclude;
                cbxAllRtpMsgInclude.Checked = option.MessageIncludeAllRtp;
                cbxL3DecodeInclude.Checked = option.MessageL3HexCode;
                cbxFusionInclude.Checked = option.FusionInclude;

                listSelected.Items.Clear();
                foreach (ColumnDefItem colDef in option.SampleColumns)
                {
                    listSelected.Items.Add(colDef);
                }

                switchShowServiceType(option);

                txtSerial.Text = "";
                if (!string.IsNullOrEmpty(option.DefaultSerialThemeName))
                {
                    txtSerial.Text = option.DefaultSerialThemeName;
                }
            }
        }

        private void switchShowServiceType(DIYReplayContentOption option)
        {
            cbxSvType.SelectedIndex = -1;
            for (int i = 0; i < cbxSvType.Items.Count; i++)
            {
                ServiceSampleTable tbCfg = cbxSvType.Items[i] as ServiceSampleTable;
                if (tbCfg.ServiceTypeV==option.SampleServiceType)
                {
                    cbxSvType.SelectedIndex = i;
                    break;
                }
            }
        }

        private void cbxSvType_SelectedIndexChanged(object sender, EventArgs e)
        {
            ServiceSampleTable svTable = cbxSvType.SelectedItem as ServiceSampleTable;
            if(svTable == null)
            {
                return;
            }
            listAvailable.Items.Clear();
            curServiceAllParamList.Clear();

            foreach (ColumnDefItem col in InterfaceManager.GetInstance().TridIDColDefDic.Values)
            {
                if (col.tableName.ToLower() == svTable.TableModelName.ToLower())
                {
                    curServiceAllParamList.Add(col);

                    if (listSelected.Items.IndexOf(col) == -1)
                    {
                        listAvailable.Items.Add(col);
                    }
                }
            }
        }
        private void txtFilterAvailable_TextChanged(object sender, EventArgs e)
        {
            listAvailable.Items.Clear();
            foreach (ColumnDefItem col in curServiceAllParamList)
            {
                if (col.showName.ToLower().Contains(txtFilterAvailable.Text.ToLower())
                    && listSelected.Items.IndexOf(col) == -1)
                {
                    listAvailable.Items.Add(col);
                }
            }
        }
        private void txtFilterSelected_TextChanged(object sender, EventArgs e)
        {
            listSelected.Items.Clear();
            foreach (ColumnDefItem col in curServiceAllParamList)
            {
                if (col.showName.ToLower().Contains(txtFilterSelected.Text.ToLower())
                    && listAvailable.Items.IndexOf(col) == -1)
                {
                    listSelected.Items.Add(col);
                }
            }
        }
        private void btnColAdd_Click(object sender, EventArgs e)
        {
            List<ColumnDefItem> colList = new List<ColumnDefItem>();
            foreach(ColumnDefItem col in listAvailable.SelectedItems)
            {
                colList.Add(col);
            }
            foreach(ColumnDefItem col in colList)
            {
                listAvailable.Items.Remove(col);
                listSelected.Items.Add(col);
            }
        }

        private void btnColDel_Click(object sender, EventArgs e)
        {
            List<ColumnDefItem> colList = new List<ColumnDefItem>();
            foreach (ColumnDefItem col in listSelected.SelectedItems)
            {
                colList.Add(col);
            }
            foreach (ColumnDefItem col in colList)
            {
                listSelected.Items.Remove(col);
                listAvailable.Items.Add(col);
            }
        }

        private void btnColAddAll_Click(object sender, EventArgs e)
        {
            List<ColumnDefItem> colList = new List<ColumnDefItem>();
            foreach (ColumnDefItem col in listAvailable.Items)
            {
                colList.Add(col);
            }
            foreach (ColumnDefItem col in colList)
            {
                listAvailable.Items.Remove(col);
                listSelected.Items.Add(col);
            }
        }

        private void btnColClear_Click(object sender, EventArgs e)
        {
            List<ColumnDefItem> colList = new List<ColumnDefItem>();
            foreach (ColumnDefItem col in listSelected.Items)
            {
                colList.Add(col);
            }
            foreach (ColumnDefItem col in colList)
            {
                listSelected.Items.Remove(col);
                listAvailable.Items.Add(col);
            }
        }

        private void btnDelOption_Click(object sender, EventArgs e)
        {
            DIYReplayContentOption option = listAllOptions.SelectedItem as DIYReplayContentOption;
            if (option == null)
            {
                return;
            }
            if(DialogResult.OK == MessageBox.Show(this,"确定要删除所选回放方式定义："+option+"？","删除",MessageBoxButtons.OKCancel))
            {
                listAllOptions.Items.Remove(option);
            }

        }

        private void btnNewOption_Click(object sender, EventArgs e)
        {
            TextInputBox input = new TextInputBox("新增回放方式定义", "输入回放方式名称", "新建");
            if (DialogResult.OK == input.ShowDialog(this))
            {
                string textInput = input.TextInput;
                DIYReplayContentOption option = new DIYReplayContentOption();
                option.Name = textInput;
                listAllOptions.Items.Add(option);
                listAllOptions.SelectedItem = option;
            }
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            string name = tbxName.Text.Trim();
            if (name.Length == 0)
            {
                MessageBox.Show(this, "名称不能为空!");
                return;
            }
            if (string.IsNullOrEmpty(txtSerial.Text))
            {
                MessageBox.Show(this, "请选择要显示的指标序列！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            DIYReplayContentOption option  = listAllOptions.SelectedItem as DIYReplayContentOption;
            if(option==null)
            {
                if(DialogResult.OK == MessageBox.Show(this, "无选定名称，是否创建新的回放定义？","创建",MessageBoxButtons.OKCancel, MessageBoxIcon.Question))
                {
                    option = new DIYReplayContentOption();
                    option.Name = tbxName.Text.Trim();
                    option.Desc = tbxDesc.Text.Trim();
                    option.DefaultSerialThemeName = txtSerial.Text;//cbxSerials.SelectedItem.ToString();
                    listAllOptions.Items.Add(option);
                    listAllOptions.SelectedItem = option;
                }
                else
                {
                    return;
                }
            }
            option.Name = name;
            option.Desc = tbxDesc.Text.Trim();
            option.DefaultSerialThemeName = txtSerial.Text;//cbxSerials.SelectedItem.ToString();
            option.MessageInclude = cbxMsgInclude.Checked;
            option.MessageIncludeAllRtp = cbxAllRtpMsgInclude.Checked;
            if (cbxL3DecodeInclude.Checked && (option.MessageInclude || option.MessageIncludeAllRtp))
            {
                option.MessageL3HexCode = cbxL3DecodeInclude.Checked;
            }
            else
            {
                option.MessageL3HexCode = false;
            }
            option.FusionInclude = cbxFusionInclude.Checked;
            option.EventInclude = cbxEventInclude.Checked;
            option.SampleColumns.Clear();
            foreach(ColumnDefItem col in listSelected.Items)
            {
                option.SampleColumns.Add(col);
            }
            ServiceSampleTable svTable = cbxSvType.SelectedItem as ServiceSampleTable;
            option.SampleServiceType = svTable.ServiceTypeV;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            saveCondSettings();
        }

        private void BtnSerial_Click(object sender, EventArgs e)
        {
            PopSelectSerialsForm selectForm = new PopSelectSerialsForm();
            if (selectForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            txtSerial.Text = "";
            if (selectForm.SelectedSerials.Count > 0)
            {
                txtSerial.Text = DTLayerSerialManager.Instance.GetNameBySerial(selectForm.SelectedSerials[0]);
            }
        }

        private void saveCondSettings()
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement cfg = configFile.AddConfig("DIYReplayOptions");
                List<object> styles = new List<object>();
                foreach (DIYReplayContentOption rpt in listAllOptions.Items)
                {
                    styles.Add(rpt.Param);
                }
                configFile.AddItem(cfg, "options", styles);
                configFile.Save(string.Format(Application.StartupPath + "/config/diyreplayoption.xml"));
            }
            catch (Exception e)
            {
                MessageBox.Show("保存失败!" + e.Message);
            }
        }

        private void listAvailable_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            ColumnDefItem selCol = listAvailable.SelectedItem as ColumnDefItem;
            if (selCol != null)
            {
                listAvailable.Items.Remove(selCol);
                listSelected.Items.Add(selCol);
            }
        }

        private void listSelected_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            ColumnDefItem selCol = listSelected.SelectedItem as ColumnDefItem;
            if (selCol != null)
            {
                listSelected.Items.Remove(selCol);
                listAvailable.Items.Add(selCol);
            }
        }

        public List<DIYReplayContentOption> AllOptionList
        {
            get
            {
                List<DIYReplayContentOption> list = new List<DIYReplayContentOption>();
                foreach (DIYReplayContentOption item in listAllOptions.Items)
                {
                    list.Add(item);
                }
                return list;
            }
        }


    }
}