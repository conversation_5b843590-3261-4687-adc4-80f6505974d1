﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteRRCAnaForm : MinCloseForm
    {
        public LteRRCAnaForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        public void FillData(Dictionary<string, LteRRCInfo> cellRRCInfoDic)
        {
            List<LteRRCInfo> rrcInfoList = new List<LteRRCInfo>();
            foreach (string strKey in cellRRCInfoDic.Keys)
            {
                cellRRCInfoDic[strKey].ISn = rrcInfoList.Count + 1;
                rrcInfoList.Add(cellRRCInfoDic[strKey]);
            }
            BindingSource source = new BindingSource();
            source.DataSource = rrcInfoList;
            gridData.DataSource = source;
            gridData.RefreshDataSource();
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }
    }
}
