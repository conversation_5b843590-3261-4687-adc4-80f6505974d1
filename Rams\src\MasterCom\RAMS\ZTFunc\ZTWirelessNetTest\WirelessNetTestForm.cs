﻿using DevExpress.XtraGrid.Views.Grid;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    public partial class WirelessNetTestForm : Form
    {
        public WirelessNetTestForm()
        {
            InitializeComponent();
        }

        public void FillData(WirelessNetTestResult totalResult)
        {
            init(totalResult);
        }

        protected void init(WirelessNetTestResult totalResult)
        {
            var district = new WirelessNetTestKpiDistrict();
            var districtDT = district.GetTableColumns();
            district.AddTableRows(districtDT, totalResult.DistrictResList);
            gcDistrict.DataSource = districtDT;
            gcDistrict.RefreshDataSource();

            var county = new WirelessNetTestKpiCounty();
            var countyDT = county.GetTableColumns();
            county.AddTableRows(countyDT, totalResult.CountyResList);
            gcCounty.DataSource = countyDT;
            gcCounty.RefreshDataSource();

            var scene = new WirelessNetTestKpiScene();
            var sceneDT = scene.GetTableColumns();
            scene.AddTableRows(sceneDT, totalResult.SceneResList);
            gcScene.DataSource = sceneDT;
            gcScene.RefreshDataSource();

            var subScenet = new WirelessNetTestKpiSubScene();
            var subScenetDT = subScenet.GetTableColumns();
            subScenet.AddTableRows(subScenetDT, totalResult.SubSceneResList);
            gcSubScene.DataSource = subScenetDT;
            gcSubScene.RefreshDataSource();
        }

        private void miExport_Click(object sender, EventArgs e)
        {
            List<GridView> gvs = new List<GridView>()
            { 
                gvDistrict,
                gvCounty,
                gvScene,
                gvSubScene
            };
            List<string> sheetNames = new List<string>()
            {
                WirelessNetTestStatType.地市级.ToString(),
                WirelessNetTestStatType.区县级.ToString(),
                WirelessNetTestStatType.场景级.ToString(),
                WirelessNetTestStatType.子场景级.ToString(),
            };

            ExcelNPOIManager.ExportToExcel(gvs, sheetNames);
        }
    }
}
