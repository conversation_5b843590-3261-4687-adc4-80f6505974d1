﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using GMap.NET;
using MasterCom.MTGis;
using MasterCom.RAMS.ExMap;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class EventBlockGMapLayer : ExMapDrawBaseLayer
    {
        public override string Alias
        {
            get { return "事件汇聚"; }
        }

        readonly MainModel mainModel = null;
        readonly MapFormEventBlockLayer layer = null;
        public EventBlockGMapLayer(MTExGMap exMap)
            : base(exMap)
        {
            mainModel = MainModel.GetInstance();
            layer = mainModel.MainForm.GetMapForm().GetCustomLayer(typeof(MapFormEventBlockLayer)) as MapFormEventBlockLayer;
        }

        public override void Draw(System.Drawing.Graphics g, PointLatLng ltPt, PointLatLng brPt)
        {
            if (!layer.IsVisible)
            {
                return;
            }
            DbRect dRect = new DbRect(ltPt.Lng, brPt.Lat, brPt.Lng, ltPt.Lat);
            //底层20米的精度跨度大小 0.0001951;
            double geoGap = (0.0001951 / 20) * MasterCom.RAMS.Net.DIYQueryEventBlockRegion.EventBlockRadius;
            PointLatLng tmpPnt = new PointLatLng(ltPt.Lat + geoGap, ltPt.Lng + geoGap);
            GPoint gPnt1 = exMap.FromLatLngToLocalAdaptered(ltPt);
            GPoint gPnt2 = exMap.FromLatLngToLocalAdaptered(tmpPnt);
            int displayGap = ((gPnt2.X - gPnt1.X) / 2) + 1;
            drawBlock(mainModel.CurEventBlockList, dRect, displayGap, g);
        }

        private void drawBlock(List<MasterCom.RAMS.Func.EventBlock.EventBlock> eventBlockList, DbRect dRect, int rGap, Graphics graphics)
        {
            foreach (EventBlock.EventBlock block in eventBlockList)
            {
                SolidBrush brush = new SolidBrush(layer.GetColorFrom(block));
                if (block.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
                {
                    List<Event> evtList = block.AbnormalEvents;
                    Region blockReg = getRegionBlock(rGap, evtList);
                    graphics.FillRegion(brush, blockReg);
                    if (layer.DrawSn && blockReg != null)
                    {
                        RectangleF rectF = blockReg.GetBounds(graphics);
                        PointF p = new PointF(rectF.X + rectF.Width / 2, rectF.Y + rectF.Height / 2);
                        graphics.DrawString(block.WeightSn.ToString(), layer.snFont, Brushes.Lime, p);
                    }
                }
            }
        }

        private Region getRegionBlock(int rGap, List<Event> evtList)
        {
            Region blockReg = null;
            foreach (Event evt in evtList)
            {
                GPoint point = exMap.FromLatLngToLocalAdaptered(evt.Longitude, evt.Latitude);
                float radius = rGap > 4 ? rGap : 4;
                RectangleF rect = new RectangleF(point.X - radius, point.Y - radius, radius * 2, radius * 2);
                GraphicsPath gp = new GraphicsPath();
                gp.AddEllipse(rect);
                if (blockReg == null)
                {
                    blockReg = new Region(gp);
                }
                else
                {
                    blockReg.Union(gp);
                }
            }

            return blockReg;
        }
    }
}
