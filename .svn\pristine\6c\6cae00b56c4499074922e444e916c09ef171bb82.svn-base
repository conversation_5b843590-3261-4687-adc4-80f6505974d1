﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTNRHandOverDelay;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRHandOverDelayQuery : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static NRHandOverDelayQuery instance = null;
        public static NRHandOverDelayQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRHandOverDelayQuery();
                    }
                }
            }
            return instance;
        }

        protected NRHandOverDelayQuery()
           : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            this.Columns = new List<string>();
        }

        public override string Name
        {
            get
            {
                return "NR切换延统计(按文件)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22046, this.Name);
        }

        protected bool checkDelay = true;
        protected int maxDelaySec = 10;

        protected List<SingleHandOverDelayInfo> callStatList = null;

        protected override bool getCondition()
        {
            //var dlg = new CallConditionDlg();
            //dlg.SetCondition(checkDelay, maxDelaySec);
            //if (dlg.ShowDialog() != DialogResult.OK)
            //{
            //    return false;
            //}

            //dlg.GetCondition(out checkDelay, out maxDelaySec);

            callStatList = new List<SingleHandOverDelayInfo>();

            return callStatList != null;
        }


        protected override void fireShowForm()
        {
            if (callStatList == null || callStatList.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }

            var frm = MainModel.GetObjectFromBlackboard(typeof(HandOverDelayForm)) as HandOverDelayForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new HandOverDelayForm();
            }
            frm.FillData(callStatList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            callStatList = null;
        }


        protected override void analyseFiles()
        {
            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;

                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                       && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }

                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + MainModel.FileInfos.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / MainModel.FileInfos.Count);

                    condition.FileInfos.Add(fileInfo);

                    replay();
                    condition.FileInfos.Clear();

                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        protected override void doStatWithQuery()
        {
            foreach (var file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file != null)
                {
                    dealHandOverFile(file);
                }
            }
        }

        // 9295: NR SA Inter Handover Request  NR站间切换请求
        // 9296: NR SA Inter Handover Success  NR站间切换成功
        // 9298: NR SA Intra Handover Request  NR站内切换请求
        // 9299: NR SA Intra Handover Success  NR站内切换成功
        // 9540: IRAT NR_LTE HO Request  NR切换到LTE请求
        // 9541: IRAT NR_LTE HO Request  NR切换到LTE完成

        protected readonly Dictionary<int, int> HandOverEvtIdPair = new Dictionary<int, int>() { 
            { 9295, 9296 }, { 9298, 9299 }, { 9540, 9541 } };

        protected readonly Dictionary<int, string> HandOverEvtStrPair = new Dictionary<int, string>() {
            { 9295, "NR站间切换" }, { 9298, "NR站内切换" }, { 9540, "NR切换到LTE" } };

        private void dealHandOverFile(DTFileDataManager hoFile)
        {
            try
            {
                SingleHandOverDelayInfo singleHandOver = null;
                bool haveHOSuccEvent = false;

                for (int i = 0; i < hoFile.Events.Count; i++)
                {
                    var evt = hoFile.Events[i];

                    if (HandOverEvtIdPair.ContainsKey(evt.ID))
                    {
                        singleHandOver = new SingleHandOverDelayInfo();
                        singleHandOver.ReqEvent = evt;
                        haveHOSuccEvent = false;
                    }

                    if ((singleHandOver != null && HandOverEvtIdPair.ContainsValue(evt.ID)) || haveHOSuccEvent)
                    {
                        singleHandOver.RealSuccEvent = evt;
                        var hoSucc = dealHandOverSuccess(singleHandOver, ref haveHOSuccEvent);

                        if (hoSucc)
                        {
                            callStatList.Add(singleHandOver);
                            singleHandOver = null;
                            haveHOSuccEvent = false;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
        }

        private bool dealHandOverSuccess(SingleHandOverDelayInfo singleHandOver, ref bool haveHOSuccEvent)
        {
            if (haveHOSuccEvent)
            {
                singleHandOver.SuccessNrTac = Convert.ToInt32(singleHandOver.RealSuccEvent.LAC);
                singleHandOver.SuccessNrNci = Convert.ToInt64(singleHandOver.RealSuccEvent.CI);
                
                if ((singleHandOver.RequestNrTac == singleHandOver.SuccessNrTac &&
                    singleHandOver.RequestNrNci == singleHandOver.SuccessNrNci) ||
                    singleHandOver.SuccessNrNci == 0 || singleHandOver.SuccessNrNci == -1)
                {
                    return false;
                }

                singleHandOver.HandOverNciChangeSuccessTime = singleHandOver.RealSuccEvent.DateTime;

                if (singleHandOver.HandOverRequestTime != null && singleHandOver.HandOverNciChangeSuccessTime != null)
                {
                    singleHandOver.HandOverNciChangeDelayTime = (int)(singleHandOver.HandOverNciChangeSuccessTime - singleHandOver.HandOverRequestTime).TotalMilliseconds;
                }

                return true;
            }

            singleHandOver.SuccEvent = singleHandOver.RealSuccEvent;

            singleHandOver.Sn = callStatList.Count + 1;

            singleHandOver.FileName = singleHandOver.ReqEvent.FileName;

            singleHandOver.HandOverRequestTime = singleHandOver.ReqEvent.DateTime;

            singleHandOver.HandOverSuccessTime = singleHandOver.SuccEvent.DateTime;

            singleHandOver.HandOverNciChangeSuccessTime = singleHandOver.SuccEvent.DateTime;

            singleHandOver.RequestLng = singleHandOver.ReqEvent.Longitude;

            singleHandOver.RequestLat = singleHandOver.ReqEvent.Latitude;

            singleHandOver.RequestNrTac = Convert.ToInt32(singleHandOver.ReqEvent.LAC);

            singleHandOver.RequestNrNci = Convert.ToInt64(singleHandOver.ReqEvent.CI);


            singleHandOver.SuccessLng = singleHandOver.SuccEvent.Longitude;

            singleHandOver.SuccessLat = singleHandOver.SuccEvent.Latitude;

            singleHandOver.SuccessNrTac = Convert.ToInt32(singleHandOver.SuccEvent.LAC);

            singleHandOver.SuccessNrNci = Convert.ToInt64(singleHandOver.SuccEvent.CI);

            if (singleHandOver.HandOverRequestTime != null && singleHandOver.HandOverSuccessTime != null)
            {
                singleHandOver.HandOverDelayTime = (int)(singleHandOver.HandOverSuccessTime - singleHandOver.HandOverRequestTime).TotalMilliseconds;
                singleHandOver.HandOverNciChangeDelayTime = (int)(singleHandOver.HandOverSuccessTime - singleHandOver.HandOverRequestTime).TotalMilliseconds;
            }

            if (HandOverEvtStrPair.ContainsKey(singleHandOver.ReqEvent.ID))
            {
                singleHandOver.HandOverType = HandOverEvtStrPair[singleHandOver.ReqEvent.ID];
            }

            haveHOSuccEvent = true;

            if ((singleHandOver.RequestNrTac == singleHandOver.SuccessNrTac && 
                singleHandOver.RequestNrNci == singleHandOver.SuccessNrNci) ||
                singleHandOver.SuccessNrNci == 0 || singleHandOver.SuccessNrNci == -1)
            {
                return false;
            }
            else
            {
                return true;
            }
        }
    }


    public class SingleHandOverDelayInfo
    {
        public SingleHandOverDelayInfo()
        {

        }

        public int Sn { get; set; }
        public string FileName { get; set; }
        public Model.Event ReqEvent { get; set; }
        public Model.Event SuccEvent { get; set; }
        public Model.Event RealSuccEvent { get; set; }

        public string HandOverType { get; set; }    // 切换类型（NR站内切换、NR站间切换、NR_LTE切换、LTE站内切换、LTE站间切换）
        public int? HandOverDelayTime { get; set; }    // 切换时延(ms)
        public int? HandOverNciChangeDelayTime { get; set; }    // NCI切换时延(ms)

        public DateTime HandOverRequestTime { get; set; }    // 切换请求时间

        public DateTime HandOverSuccessTime { get; set; }    // 切换完成时间

        public DateTime HandOverNciChangeSuccessTime { get; set; }    // NCI变化时间

        public double? RequestLng { get; set; }
        public double? RequestLat { get; set; }

        public int? RequestNrTac { get; set; }
        public long? RequestNrNci { get; set; }

        public double? SuccessLng { get; set; }
        public double? SuccessLat { get; set; }

        public int? SuccessNrTac { get; set; }
        public long? SuccessNrNci { get; set; }
    }

}
