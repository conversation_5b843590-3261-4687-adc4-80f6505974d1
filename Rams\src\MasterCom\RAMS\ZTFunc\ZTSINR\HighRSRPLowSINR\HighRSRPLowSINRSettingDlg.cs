﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HighRSRPLowSINRSettingDlg : BaseDialog
    {
        public HighRSRPLowSINRSettingDlg(HighRSRPLowSINRCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(HighRSRPLowSINRCondition condition)
        {
            if (condition==null)
            {
                return;
            }
            numMaxSINR.Value = (decimal)condition.MaxSinr;
            numMinRsrp.Value = (decimal)condition.MinRsrp;
            chk2TPDistance.Checked = condition.Check2TPDistance;
            numMaxTPDistance.Value=(decimal)condition.Max2TPDistance;
            chkDistance.Checked = condition.CheckDistance;
            numMinDistance.Value = (decimal)condition.MinStayDistance;
            chkTime.Checked = condition.CheckTime;
            numTime.Value = (decimal)condition.MinStaySecond;
            numPer.Value = (decimal)condition.Percentage;
        }

        public HighRSRPLowSINRCondition GetCondition()
        {
            HighRSRPLowSINRCondition condition = new HighRSRPLowSINRCondition();
            condition.MaxSinr = (float)numMaxSINR.Value;
            condition.MinRsrp = (float)numMinRsrp.Value;
            condition.Check2TPDistance = chk2TPDistance.Checked;
            condition.Max2TPDistance = (double)numMaxTPDistance.Value;
            condition.CheckDistance = chkDistance.Checked;
            condition.MinStayDistance = (double)numMinDistance.Value;
            condition.CheckTime = chkTime.Checked;
            condition.MinStaySecond = (double)numTime.Value;
            condition.Percentage = (double)numPer.Value;
            return condition;
        }

        private void chkDistance_CheckedChanged(object sender, EventArgs e)
        {
            numMinDistance.Enabled = chkDistance.Checked;
        }

        private void chk2TPDistance_CheckedChanged(object sender, EventArgs e)
        {
            numMaxTPDistance.Enabled = chk2TPDistance.Checked;
        }

        private void chkTime_CheckedChanged(object sender, EventArgs e)
        {
            numTime.Enabled = chkTime.Checked;
        }
    }
}
