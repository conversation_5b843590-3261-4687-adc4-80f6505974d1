﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.GeneralFuncDef;
using MasterCom.RAMS.GeneralFuncDef.OwnSampleAnalyse;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.OwnSampleAnalyse;
using MasterCom.Util;
using System.Xml;
using MasterCom.RAMS.GeneralFuncDef.OwnResultDef;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.GeneralFuncDef
{
    public partial class GeneralFuncSettingForm : BaseFormStyle
    {
        GeneralRoutine routine = null;
        public GeneralFuncSettingForm()
        {
            InitializeComponent();
            routine = new GeneralRoutine();
        }
        int EachRowHeight = 50;
        int curSelAnalyserInx = -1;
        bool resultNodeSelected = false;
        ResultDefine resultDefineItem = new ResultDefine();
        private void panelGraph_Paint(object sender, PaintEventArgs e)
        {
            if(routine!=null)
            {
                EachRowHeight = 80;
                Graphics g = e.Graphics;
                int xPosOffset = 80;
                int yPosOffset = 50;
                int widthEachAnalyser = 100;
                int widthEachArrow = 50;

                if(!routine.periodCompareMode)//非时间对比模式
                {
                    drawAnalysers(g, ref xPosOffset, yPosOffset, widthEachAnalyser, widthEachArrow);
                    //结果节点
                    drawResult(g, xPosOffset, yPosOffset, widthEachAnalyser); 
                }
                else//两时段对比模式
                {
                    //第一时段
                    drawAnalysers(g, ref xPosOffset, yPosOffset, widthEachAnalyser, widthEachArrow);
                    //第二时段
                    xPosOffset = 80;
                    yPosOffset = 50 + (int)(EachRowHeight * 1.3f);
                    drawAnalysers(g, ref xPosOffset, yPosOffset, widthEachAnalyser, widthEachArrow);
                    //结果节点
                    drawResult(g, xPosOffset, 50 + (int)(EachRowHeight * 0.65f), widthEachAnalyser);
                }

            }
        }

        private void drawAnalysers(Graphics g, ref int xPosOffset, int yPosOffset, int widthEachAnalyser, int widthEachArrow)
        {
            for (int i = 0; i < routine.analysersList.Count; i++)
            {
                BaseSubAnalyser analyser = routine.analysersList[i];
                drawAnalyser(g, analyser, xPosOffset, yPosOffset, widthEachAnalyser, i == curSelAnalyserInx);
                if (i < routine.analysersList.Count - 1)
                {
                    drawStepArrow(g, xPosOffset + widthEachAnalyser, yPosOffset, widthEachArrow);
                }
                xPosOffset += (widthEachAnalyser + widthEachArrow);
            }
        }

        private bool hitDrawResultNode(MouseEventArgs e, int xPosOffset, int yPosOffset, int width)
        {
            xPosOffset = xPosOffset +routine.analysersList.Count * (150);
            if (e.X >= xPosOffset && e.X <= xPosOffset + width && e.Y >= yPosOffset && e.Y <= yPosOffset + width)
            {
                return true;
            }
            return false;
        }

        private void drawResult(Graphics g, int xPosOffset, int yPosOffset, int width)
        {
            g.FillRectangle(Brushes.DarkGray, xPosOffset, yPosOffset, width, EachRowHeight);
            g.FillRectangle(resultNodeSelected?Brushes.Red:Brushes.Blue, xPosOffset + 4, yPosOffset + 3, width, EachRowHeight);
            g.FillRectangle(Brushes.LightYellow, xPosOffset + 9, yPosOffset + 8, width - 10, EachRowHeight - 10);

            Font fontText = new Font(new FontFamily("宋体"), 10, FontStyle.Bold);
            SizeF size = g.MeasureString("结果输出", fontText);
            g.DrawString("结果输出", fontText, Brushes.DarkBlue, xPosOffset - 0.5f * size.Width + width * 0.5f, yPosOffset - 0.5f * size.Height + EachRowHeight * 0.3f);

            Font fontText2 = new Font(new FontFamily("宋体"), 10, FontStyle.Bold);
            SizeF size2 = g.MeasureString(resultDefineItem.desc, fontText2);
            g.DrawString(resultDefineItem.desc, fontText2, Brushes.DarkBlue, xPosOffset - 0.5f * size2.Width + width * 0.5f, yPosOffset - 0.5f * size2.Height + EachRowHeight * 0.3f + size.Height + 5);


        }

        private void drawStepArrow(Graphics g, int xPosOffset, int yPosOffset, int widthEachArrow)
        {
            Pen penGray = new Pen(Brushes.DarkGray, 3);
            Point[] pts = new Point[3];
            pts[0] = new Point(xPosOffset + (int)(widthEachArrow * 0.2f)-2, (int)(yPosOffset + 0.2f * EachRowHeight-2));
            pts[1] = new Point(xPosOffset + (int)(widthEachArrow * 0.8f)-2, (int)(yPosOffset + 0.5f * EachRowHeight-2));
            pts[2] = new Point(xPosOffset + (int)(widthEachArrow * 0.2f)-2, (int)(yPosOffset + 0.8f * EachRowHeight-2));
            g.DrawLines(penGray, pts);
            Pen pen = new Pen(Brushes.DarkRed, 3);
            Point[] pts1 = new Point[3];
            pts1[0] = new Point(xPosOffset + (int)(widthEachArrow * 0.2f), (int)(yPosOffset + 0.2f * EachRowHeight));
            pts1[1] = new Point(xPosOffset + (int)(widthEachArrow * 0.8f), (int)(yPosOffset + 0.5f * EachRowHeight));
            pts1[2] = new Point(xPosOffset + (int)(widthEachArrow * 0.2f), (int)(yPosOffset + 0.8f * EachRowHeight));
            g.DrawLines(pen, pts1);
        }


        private void drawAnalyser(Graphics g, BaseSubAnalyser analyser, int xPosOffset, int yPosOffset,int width,bool selected)
        {
            g.FillRectangle(Brushes.DarkGray, xPosOffset, yPosOffset, width, EachRowHeight);
            g.FillRectangle(selected?Brushes.Red:Brushes.Blue, xPosOffset + 4, yPosOffset + 3, width, EachRowHeight);
            g.FillRectangle(Brushes.WhiteSmoke, xPosOffset + 9, yPosOffset + 8, width - 10, EachRowHeight - 10);

            Font fontText = new Font(new FontFamily("宋体"), 10, FontStyle.Bold);
            SizeF size = g.MeasureString(analyser.name, fontText);
            g.DrawString(analyser.name, fontText, Brushes.DarkBlue, xPosOffset - 0.5f * size.Width + width * 0.5f, yPosOffset - 0.5f * size.Height + EachRowHeight * 0.3f);

            string descShow = analyser.GetDescShow();
            Font fontTextDesc = new Font(new FontFamily("宋体"), 8, FontStyle.Regular);
            SizeF sizeDesc = g.MeasureString(descShow, fontTextDesc);
            g.DrawString(descShow, fontTextDesc, Brushes.Purple, xPosOffset - 0.5f * sizeDesc.Width + width * 0.5f, yPosOffset - 0.5f * sizeDesc.Height + EachRowHeight * 0.3f + size.Height + 2);


        }
        private bool hitTestAnalyser(MouseEventArgs e, int xPosOffset, int yPosOffset, int width)
        {
            if(e.X>=xPosOffset && e.X<=xPosOffset+width && e.Y>=yPosOffset && e.Y<=yPosOffset+EachRowHeight)
            {
                return true;
            }
            return false;
        }

        private void cbxDifferentTime_CheckedChanged(object sender, EventArgs e)
        {
            if(routine!=null)
            {
                routine.periodCompareMode = cbxDifferentTime.Checked;
                panelGraph.Invalidate();
            }
        }
        private bool hitResultNodeSelected(MouseEventArgs e)
        {
            EachRowHeight = 80;
            int xPosOffset = 80;
            int yPosOffset = 50;
            int widthEachAnalyser = 100;

            if (!routine.periodCompareMode)//非时间对比模式
            {
                //结果节点
                return hitDrawResultNode(e, xPosOffset, yPosOffset, widthEachAnalyser);
            }
            else//两时段对比模式
            {

                //结果节点
                return hitDrawResultNode(e, xPosOffset, 50 + (int)(EachRowHeight * 0.65f), widthEachAnalyser);
            }
        }

        
        private int hitSubAnalyserIndex(MouseEventArgs e)
        {
            EachRowHeight = 80;
            int xPosOffset = 80;
            int yPosOffset = 50;
            int widthEachAnalyser = 100;
            int widthEachArrow = 50;
            if (!routine.periodCompareMode)//非时间对比模式
            {
                int index = getIndex(e, ref xPosOffset, yPosOffset, widthEachAnalyser, widthEachArrow);
                if (index >= 0)
                {
                    return index;
                }
            }
            else//两时段对比模式
            {
                //第一时段
                int index = getIndex(e, ref xPosOffset, yPosOffset, widthEachAnalyser, widthEachArrow);
                if (index >= 0)
                {
                    return index;
                }

                //第二时段
                xPosOffset = 80;
                yPosOffset = 50 + (int)(EachRowHeight * 1.3f);
                index = getIndex(e, ref xPosOffset, yPosOffset, widthEachAnalyser, widthEachArrow);
                if (index >= 0)
                {
                    return index;
                }
            }
            return -1;
        }

        private int getIndex(MouseEventArgs e, ref int xPosOffset, int yPosOffset, int widthEachAnalyser, int widthEachArrow)
        {
            for (int i = 0; i < routine.analysersList.Count; i++)
            {
                if (hitTestAnalyser(e, xPosOffset, yPosOffset, widthEachAnalyser))
                {
                    return i;
                }
                xPosOffset += (widthEachAnalyser + widthEachArrow);
            }
            return -1;
        }

        private void panelGraph_MouseClick(object sender, MouseEventArgs e)
        {
            if(e.Button== MouseButtons.Right)
            {
                if (curSelAnalyserInx != -1)
                {
                    miModify.Enabled = true;
                    miAppend.Enabled = true;
                    miDelete.Enabled = true;
                    ctxGraphPopMenu.Show(panelGraph, e.X, e.Y);
                }
                else if(resultNodeSelected)
                {
                    miModify.Enabled = true;
                    miAppend.Enabled = false;
                    miDelete.Enabled = false;
                    ctxGraphPopMenu.Show(panelGraph, e.X, e.Y);
                }
                else
                {
                    if(routine!=null && routine.analysersList.Count==0)
                    {
                        miModify.Enabled = false;
                        miAppend.Enabled = true;
                        miDelete.Enabled = false;
                        ctxGraphPopMenu.Show(panelGraph, e.X, e.Y);
                    }
                }
            }
        }
        SampleFilterSelectorDlg dlgFilter = null;
        private void miModify_Click(object sender, EventArgs e)
        {
            if(curSelAnalyserInx>=0 && curSelAnalyserInx<routine.analysersList.Count)
            {
                if (dlgFilter == null)
                {
                    dlgFilter = new SampleFilterSelectorDlg();
                }
                if (dlgFilter.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                BaseSubAnalyser selAna = dlgFilter.GetSelectedAnalyser();
                routine.analysersList[curSelAnalyserInx] = selAna;
            }
            else if(resultNodeSelected)
            {
                OwnResultFuncEditorDlg dlg = new OwnResultFuncEditorDlg();
                dlg.FillResultSetting(resultDefineItem);
                if(DialogResult.OK == dlg.ShowDialog())
                {
                    resultDefineItem = dlg.GetResultDefineItem();
                }
            }
            panelGraph.Invalidate();
        }

        private void miAppend_Click(object sender, EventArgs e)
        {
            if(routine==null)
            {
                return;
            }
            if (dlgFilter == null)
            {
                dlgFilter = new SampleFilterSelectorDlg();
            }
            if (dlgFilter.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            BaseSubAnalyser selAna = dlgFilter.GetSelectedAnalyser();
            if(curSelAnalyserInx==-1)
            {
                routine.analysersList.Add(selAna);
            }
            else
            {
                routine.analysersList.Insert(curSelAnalyserInx + 1, selAna);
            }
            panelGraph.Invalidate();
        }

        private void miDelete_Click(object sender, EventArgs e)
        {
            if (curSelAnalyserInx >= 0 && curSelAnalyserInx < routine.analysersList.Count)
            {
                BaseSubAnalyser analyser = routine.analysersList[curSelAnalyserInx];
                if(DialogResult.OK == XtraMessageBox.Show(this,"确定要删除分析流程节点"+analyser.name+"?","删除",MessageBoxButtons.OKCancel))
                {
                    routine.analysersList.RemoveAt(curSelAnalyserInx);
                    panelGraph.Invalidate();
                }
            }
           
        }

        private void panelGraph_MouseDown(object sender, MouseEventArgs e)
        {

            curSelAnalyserInx = hitSubAnalyserIndex(e);
            if (curSelAnalyserInx == -1)//是否选择了结果节点
            {
                resultNodeSelected = hitResultNodeSelected(e);
            }
            else
            {
                resultNodeSelected = false;
            }
            panelGraph.Invalidate();

        }

        private void btnSaveConfig_Click(object sender, EventArgs e)
        {
            //generalroutine
            try
            {
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement cfg = configFile.AddConfig("GeneralRoutineOptions");
                List<object> styles = new List<object>();
                foreach (GeneralRoutine rpt in cbxRoutineSet.Items)
                {
                    styles.Add(rpt.Param);
                }
                configFile.AddItem(cfg, "options", styles);
                configFile.Save(string.Format(Application.StartupPath + "/config/generalroutine.xml"));
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("保存失败!" + ex.Message);
            }
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            string name = tbxName.Text.Trim();
            if(name=="")
            {
                XtraMessageBox.Show("请输入流程名称!");
                return;
            }
            routine.name = name;
            int idx = -1;
            for (int i = 0; i < cbxRoutineSet.Items.Count;i++ )
            {
                GeneralRoutine rtine = cbxRoutineSet.Items[i] as GeneralRoutine;
                if (rtine.name == name)
                {
                    idx = i;
                    break;
                }
            }
            if(idx==-1)
            {
                cbxRoutineSet.Items.Add(routine);
            }
            else
            {
                if(DialogResult.OK == XtraMessageBox.Show(this,"名称重复，是否替换?","替换确认",MessageBoxButtons.OKCancel))
                {
                    GeneralRoutine rtine = cbxRoutineSet.Items[idx] as GeneralRoutine;
                    rtine.analysersList.Clear();
                    rtine.analysersList.AddRange(routine.analysersList);
                }
            }
        }

        private void cbxRoutineSet_SelectedIndexChanged(object sender, EventArgs e)
        {
            GeneralRoutine selRoutine = cbxRoutineSet.SelectedItem as GeneralRoutine;
            if (selRoutine != null)
            {
                tbxName.Text = selRoutine.name;
                routine = new GeneralRoutine();
                routine.analysersList.AddRange(selRoutine.analysersList);
            }
            else
            {
                routine = new GeneralRoutine();
            }
            panelGraph.Invalidate();
        }



        internal void FillCurrentSettings(List<GeneralRoutine> settings,GeneralRoutine curSel)
        {
            cbxRoutineSet.Items.Clear();
            foreach (GeneralRoutine gr in settings)
            {
                cbxRoutineSet.Items.Add(gr);
            }
            if(curSel!=null)
            {
                cbxRoutineSet.SelectedItem = curSel;
            }
        }
    }
}