﻿namespace MasterCom.RAMS.ZTFunc.TestDepth
{
    partial class TestDepthResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.btnEditReport = new DevExpress.XtraEditors.SimpleButton();
            this.colorEstimate = new DevExpress.XtraEditors.ColorEdit();
            this.chkEstimate = new DevExpress.XtraEditors.CheckEdit();
            this.chkHistory = new DevExpress.XtraEditors.CheckEdit();
            this.colorHistory = new DevExpress.XtraEditors.ColorEdit();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemCbxClosedLoop = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.repositoryItemCbxHasTrafficLog = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.repositoryItemCbxMoMt = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorEstimate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkEstimate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHistory.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorHistory.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxClosedLoop)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxHasTrafficLog)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxMoMt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl1.Appearance.Options.UseForeColor = true;
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.groupControl2);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl3);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(670, 414);
            this.splitContainerControl1.SplitterPosition = 57;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.btnEditReport);
            this.groupControl2.Controls.Add(this.colorEstimate);
            this.groupControl2.Controls.Add(this.chkEstimate);
            this.groupControl2.Controls.Add(this.chkHistory);
            this.groupControl2.Controls.Add(this.colorHistory);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(670, 57);
            this.groupControl2.TabIndex = 7;
            this.groupControl2.Text = "GIS显示";
            // 
            // btnEditReport
            // 
            this.btnEditReport.Location = new System.Drawing.Point(353, 25);
            this.btnEditReport.Name = "btnEditReport";
            this.btnEditReport.Size = new System.Drawing.Size(70, 23);
            this.btnEditReport.TabIndex = 6;
            this.btnEditReport.Text = "编辑报表";
            this.btnEditReport.Visible = false;
            // 
            // colorEstimate
            // 
            this.colorEstimate.EditValue = System.Drawing.Color.Orange;
            this.colorEstimate.Location = new System.Drawing.Point(281, 27);
            this.colorEstimate.Name = "colorEstimate";
            this.colorEstimate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEstimate.Size = new System.Drawing.Size(51, 21);
            this.colorEstimate.TabIndex = 3;
            this.colorEstimate.EditValueChanged += new System.EventHandler(this.colorEstimate_EditValueChanged);
            // 
            // chkEstimate
            // 
            this.chkEstimate.EditValue = true;
            this.chkEstimate.Location = new System.Drawing.Point(205, 29);
            this.chkEstimate.Name = "chkEstimate";
            this.chkEstimate.Properties.Caption = "新数据：";
            this.chkEstimate.Size = new System.Drawing.Size(70, 19);
            this.chkEstimate.TabIndex = 2;
            this.chkEstimate.CheckedChanged += new System.EventHandler(this.chkEstimate_CheckedChanged);
            // 
            // chkHistory
            // 
            this.chkHistory.EditValue = true;
            this.chkHistory.Location = new System.Drawing.Point(22, 29);
            this.chkHistory.Name = "chkHistory";
            this.chkHistory.Properties.Caption = "基准数据：";
            this.chkHistory.Size = new System.Drawing.Size(84, 19);
            this.chkHistory.TabIndex = 2;
            this.chkHistory.CheckedChanged += new System.EventHandler(this.chkHistory_CheckedChanged);
            // 
            // colorHistory
            // 
            this.colorHistory.EditValue = System.Drawing.Color.Blue;
            this.colorHistory.Location = new System.Drawing.Point(112, 27);
            this.colorHistory.Name = "colorHistory";
            this.colorHistory.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorHistory.Size = new System.Drawing.Size(51, 21);
            this.colorHistory.TabIndex = 3;
            this.colorHistory.EditValueChanged += new System.EventHandler(this.colorHistory_EditValueChanged);
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.gridControl);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(670, 353);
            this.groupControl3.TabIndex = 1;
            this.groupControl3.Text = "详细";
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.ctxMenu;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(2, 23);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemCbxClosedLoop,
            this.repositoryItemCbxHasTrafficLog,
            this.repositoryItemCbxMoMt});
            this.gridControl.Size = new System.Drawing.Size(666, 328);
            this.gridControl.TabIndex = 7;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowAll,
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(145, 48);
            // 
            // miShowAll
            // 
            this.miShowAll.Name = "miShowAll";
            this.miShowAll.Size = new System.Drawing.Size(144, 22);
            this.miShowAll.Text = "GIS显示所有";
            this.miShowAll.Click += new System.EventHandler(this.miShowAll_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(144, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn5,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            this.gridView.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.Default;
            this.gridView.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "区域";
            this.gridColumn1.FieldName = "RegionName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "测试栅格深度";
            this.gridColumn5.DisplayFormat.FormatString = "{0:P2}";
            this.gridColumn5.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;
            this.gridColumn5.FieldName = "TestDepth";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 1;
            this.gridColumn5.Width = 119;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "基准栅格个数";
            this.gridColumn2.FieldName = "HistoryGridCount";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            this.gridColumn2.Width = 97;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "当次栅格个数";
            this.gridColumn3.FieldName = "EstimateGridCount";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            this.gridColumn3.Width = 100;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "新增栅格个数";
            this.gridColumn4.FieldName = "NewGridCount";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 4;
            this.gridColumn4.Width = 88;
            // 
            // repositoryItemCbxClosedLoop
            // 
            this.repositoryItemCbxClosedLoop.AutoHeight = false;
            this.repositoryItemCbxClosedLoop.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemCbxClosedLoop.DropDownRows = 2;
            this.repositoryItemCbxClosedLoop.Items.AddRange(new object[] {
            "是",
            "否"});
            this.repositoryItemCbxClosedLoop.Name = "repositoryItemCbxClosedLoop";
            this.repositoryItemCbxClosedLoop.NullText = "[编辑值为空]";
            this.repositoryItemCbxClosedLoop.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // repositoryItemCbxHasTrafficLog
            // 
            this.repositoryItemCbxHasTrafficLog.AutoHeight = false;
            this.repositoryItemCbxHasTrafficLog.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemCbxHasTrafficLog.Items.AddRange(new object[] {
            "是",
            "否"});
            this.repositoryItemCbxHasTrafficLog.Name = "repositoryItemCbxHasTrafficLog";
            this.repositoryItemCbxHasTrafficLog.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // repositoryItemCbxMoMt
            // 
            this.repositoryItemCbxMoMt.AutoHeight = false;
            this.repositoryItemCbxMoMt.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemCbxMoMt.Items.AddRange(new object[] {
            "主叫",
            "被叫"});
            this.repositoryItemCbxMoMt.Name = "repositoryItemCbxMoMt";
            this.repositoryItemCbxMoMt.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // gridView1
            // 
            this.gridView1.Name = "gridView1";
            // 
            // TestDepthResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(670, 414);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "TestDepthResultForm";
            this.Text = "测试深度";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.colorEstimate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkEstimate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHistory.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorHistory.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxClosedLoop)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxHasTrafficLog)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxMoMt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SimpleButton btnEditReport;
        private DevExpress.XtraEditors.ColorEdit colorEstimate;
        private DevExpress.XtraEditors.CheckEdit chkEstimate;
        private DevExpress.XtraEditors.CheckEdit chkHistory;
        private DevExpress.XtraEditors.ColorEdit colorHistory;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemCbxClosedLoop;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemCbxHasTrafficLog;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemCbxMoMt;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miShowAll;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
    }
}