﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using System.Threading;
using DevExpress.XtraCharts;

namespace MasterCom.RAMS.ZTFunc
{
    public enum EStep { StepOne = 1, StepTwo, StepThree, StepFour, StepTerminal }
    public enum EOptType { Cmd = 1, Background }
    public partial class LtePCIOptimizeNewSettingDlg : BaseFormStyle
    {
        private int step;
        private PCIOptimizeManager manager;

        private List<LTECell> allCells;
        private List<LTECell> showCells;
        private List<LTECell> checkedCells;
        private int progressMax = 100;

        public LtePCIOptimizeNewSettingDlg(PCIOptimizeManager manager)
        {
            allCells = new List<LTECell>();
            showCells = new List<LTECell>();
            checkedCells = new List<LTECell>();
            this.manager = manager;
            step = (int)EStep.StepOne;
            InitializeComponent();
            ShowPanelOne();
            refresh<PERSON>ombox(manager.<PERSON><PERSON>n<PERSON><PERSON><PERSON>, manager.CurCity);
            wyzProgressPSS.ProgressAppearance = MasterCom.RAMS.ZTFunc.WyzProgress.ProgressMode.Win8;
            wyzProgressPSS.ProgressMaximum = progressMax;
            wyzProgressPSS.ProgressMinimum = 0;

            wyzProgressSSS.ProgressAppearance = MasterCom.RAMS.ZTFunc.WyzProgress.ProgressMode.Win8;
            wyzProgressSSS.ProgressMaximum = progressMax;
            wyzProgressSSS.ProgressMinimum = 0;
        }

        private void refreshCombox(List<CityInfo> cityInfoList, CityInfo curCity)
        {
            comboBoxEditDB.Properties.Items.Clear();
            foreach (CityInfo city in cityInfoList)
            {
                comboBoxEditDB.Properties.Items.Add(city);
                if (city.serverid % 100 == mainModel.DistrictID)
                    comboBoxEditDB.SelectedItem = city;
            }
            if (curCity != null)
            {
                comboBoxEditDB.SelectedItem = curCity;
            }
        }

        private void ShowPanelOne()
        {
            this.Text = "PCI优化参数设置";
            step = (int)EStep.StepOne;
            simpleButtonUp.Enabled = false;
            simpleButtonDown.Text = "下一步";
            simpleButtonUp.Enabled = false;
            panelOne.Visible = true;
            panelTwo.Visible = false;
            panelThree.Visible = false;
            panelFour.Visible = false;
            simpleButtonDown.Focus();

            this.spinEditIteration.Value = manager.Iteration;
            this.spinEditPopularSize.Value = manager.PopularSize;
            this.spinEditThreadNum.Value = manager.ThreadNum;
            this.spinEditVarRate.Value = manager.VarRate;
            this.spinEditCrossRate.Value = manager.CrossRate;
        }

        private void ShowPanelTwo()
        {
            this.Text = "PCI优化小区设置";
            step = (int)EStep.StepTwo;
            simpleButtonUp.Enabled = true;
            simpleButtonDown.Text = "下一步";
            checkBoxIncludeIndoor.Checked = false;
            checkBoxAll.Checked = false;
            panelOne.Visible = false;
            panelTwo.Visible = true;
            panelThree.Visible = false;
            panelFour.Visible = false;
            simpleButtonDown.Focus();

            manager.Iteration = (int)this.spinEditIteration.Value;
            manager.PopularSize = (int)this.spinEditPopularSize.Value;
            manager.ThreadNum = (int)this.spinEditThreadNum.Value;
            manager.VarRate = (int)this.spinEditVarRate.Value;
            manager.CrossRate = (int)this.spinEditCrossRate.Value;

            FillCells(manager.RegionCells);
        }

        private void ShowPanelThree()
        {
            this.Text = "PCI优化方式设置";
            step = (int)EStep.StepThree;
            simpleButtonUp.Enabled = true;
            simpleButtonDown.Text = "开始";

            panelOne.Visible = false;
            panelTwo.Visible = false;
            panelThree.Visible = true;
            panelFour.Visible = false;
            simpleButtonDown.Focus();

            manager.CheckedCells = checkedCells;
            manager.CurCity = comboBoxEditDB.SelectedItem as CityInfo;

            radioButtonCmd.Checked = manager.OptType == EOptType.Cmd;
            radioButtonBg.Checked = manager.OptType == EOptType.Background;
        }

        private void ShowPanelFour()
        {
            this.Text = "PCI优化运算...";
            step = (int)EStep.StepFour;
            simpleButtonUp.Enabled = false;
            simpleButtonDown.Text = "终止";
            panelOne.Visible = false;
            panelTwo.Visible = false;
            panelThree.Visible = false;
            panelFour.Visible = true;

            manager.OptType = radioButtonCmd.Checked ? EOptType.Cmd : EOptType.Background;

            if(listBoxTime.SelectedItem != null)
                manager.reporttime = listBoxTime.SelectedItem as string;

            doPCIOptimize();
        }

        public void FillCells(List<LTECell> cells)
        {
            allCells.Clear();
            showCells.Clear();
            checkedCells.Clear();
            allCells.AddRange(cells);
            showCells.AddRange(cells);
            refreshLV(showCells);
        }

        private void refreshLV(List<LTECell> showCellList)
        {
            listViewCell.BeginUpdate();
            listViewCell.Items.Clear();
            ListViewItem[] items = new ListViewItem[showCellList.Count];
            int idx = 0;
            foreach (LTECell cell in showCellList)
            {
                ListViewItem item = new ListViewItem(cell.Name);
                if (addCells.Contains(cell))
                    item.Checked = true;
                items[idx++] = item;
                item.Tag = cell;
            }
            listViewCell.Items.AddRange(items);
            listViewCell.EndUpdate();
        }

        private void listViewCell_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            LTECell cell = e.Item.Tag as LTECell;
            if (cell != null)
            {
                if (e.Item.Checked)
                {
                    if (!checkedCells.Contains(cell))
                    {
                        checkedCells.Add(cell);
                    }
                }
                else
                {
                    if (checkedCells.Contains(cell))
                    {
                        checkedCells.Remove(cell);
                    }
                }
            }
        }

        private void textBoxCellName_TextChanged(object sender, EventArgs e)
        {
            showCells.Clear();
            if (textBoxCellName.Text.Trim() == "")
            {
                showCells.AddRange(allCells);
            }
            else
            {
                foreach (LTECell cell in allCells)
                {
                    if (cell.Name.Contains(textBoxCellName.Text.Trim()))
                    {
                        showCells.Add(cell);
                    }
                }
            }
            refreshLV(showCells);
        }

        private void checkBoxAll_CheckedChanged(object sender, EventArgs e)
        {
            foreach (ListViewItem lvi in listViewCell.Items)
            {
                lvi.Checked = checkBoxAll.Checked;
            }
            if (checkBoxAll.Checked)
            {
                checkedCells.Clear();
                checkedCells.AddRange(showCells);
            }
            else
            {
                checkedCells.Clear();
            }
        }

        private void checkBoxIncludeIndoor_CheckedChanged(object sender, EventArgs e)
        {
            checkedCells.Clear();
            foreach (ListViewItem lvi in listViewCell.Items)
            {
                LTECell cell = lvi.Tag as LTECell;
                if (cell.Type == LTEBTSType.Indoor)
                {
                    lvi.Checked = checkBoxIncludeIndoor.Checked;
                }
                if (lvi.Checked)
                {
                    checkedCells.Add(cell);
                }
            }
        }

        public BackgroundWorker GetbgWorker()
        {
            return this.bgWorkerOptimize;
        }

        private static readonly object padlock = new object();
        private bool started = false;
        private void doPCIOptimize()
        {
            lock (padlock)
            {
                bgWorkerOptimize.WorkerSupportsCancellation = true;
                bgWorkerOptimize.WorkerReportsProgress = true;
                if (!started && !bgWorkerOptimize.IsBusy)
                {
                    bgWorkerOptimize.RunWorkerAsync();
                }
            }
        }

        private void bgWorkerOptimize_DoWork(object sender, DoWorkEventArgs e)
        {
            lock (padlock)
            {
                if (started)
                    return;
                else
                {
                    if (bgWorkerOptimize.IsBusy)
                        started = true;
                }

                if (bgWorkerOptimize.CancellationPending && !bgWorkerOptimize.IsBusy)
                {
                    started = true;
                }
                manager.DoOptimize(this);
            }
        }

        private void bgWorkerOptimize_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            if (e.UserState is string)
            {
                labelDesc.Text = e.UserState as string;
            }
            else if (e.UserState is ReportInfo)
            {
                ReportInfo ri = e.UserState as ReportInfo;
                if (ri == null) return;
                labelDesc.Text = string.Format("计算中...进度：{0}% 平均值：{1} 最优值：{2} 正在进行{3}运算", ri.rate, ri.mean, ri.best, ri.rate < 50 ? "PSS" : "SSS");
            }
        }

        private object objLock = new object();
        public void AddPoint(ReportInfo ri)
        {
            lock (objLock)
            {
                if (ri.rate < 50)
                {
                    if (wyzProgressPSS.ProgressValue >= wyzProgressPSS.ProgressMaximum)
                        wyzProgressPSS.ProgressMaximum += progressMax;
                    wyzProgressPSS.Text = "平均值：" + ri.mean.ToString();
                    wyzProgressPSS.Step((int)ri.mean, (int)ri.best);
                }
                else
                {
                    if (wyzProgressSSS.ProgressValue >= wyzProgressSSS.ProgressMaximum)
                        wyzProgressSSS.ProgressMaximum += progressMax;
                    wyzProgressSSS.Text = "平均值：" + ri.mean.ToString();
                    wyzProgressSSS.Step((int)ri.mean, (int)ri.best);
                }
            }
        }

        private void bgWorkerOptimize_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            if (started)
            {
                started = false;
            }
            this.Close();
            manager.DoOhterAfterOptimize();
            manager.ShowInfoForm();
        }

        private void ThreadTerminal()
        {
            simpleButtonDown.Enabled = false;
            if (bgWorkerOptimize.IsBusy)
                bgWorkerOptimize.CancelAsync();
        }

        private void ShowPanel()
        {
            switch (step)
            {
                case (int)EStep.StepOne:
                    ShowPanelOne();
                    break;
                case (int)EStep.StepTwo:
                    ShowPanelTwo();
                    break;
                case (int)EStep.StepThree:
                    ShowPanelThree();
                    break;
                case (int)EStep.StepFour:
                    ShowPanelFour();
                    break;
                case (int)EStep.StepTerminal:
                    ThreadTerminal();
                    break;
                default:
                    break;
            }
        }

        private void simpleButtonUp_Click(object sender, EventArgs e)
        {
            --step;
            ShowPanel();
        }

        private void simpleButtonDown_Click(object sender, EventArgs e)
        {
            if (step == (int)EStep.StepTwo && checkedCells.Count <= 0)
            {
                MessageBox.Show("请选择小区...", "提示");
                return;
            }
            if (step == (int)EStep.StepThree && !checkStepThree())
            {
                MessageBox.Show("未选择优化方式，直取方式需指定优化时间...", "提示");
                return;
            }
            ++step;
            ShowPanel();
        }

        private bool checkStepThree()
        {
            return radioButtonCmd.Checked || (radioButtonBg.Checked && listBoxTime.SelectedItem != null);
        }

        List<LTECell> addCells = new List<LTECell>();
        private void labelAdd_Click(object sender, EventArgs e)
        {
            addCells.Clear();
            addCells.AddRange(checkedCells);
            LtePCIOptimizeSettingDlg dlg = new LtePCIOptimizeSettingDlg();
            dlg.FillCells(CellManager.GetInstance().GetCurrentLTECells());
            if (dlg.ShowDialog() == DialogResult.Cancel) return;
            foreach (LTECell cell in dlg.GetCheckedCells())
            {
                if (!allCells.Contains(cell))
                {
                    allCells.Add(cell);
                    showCells.Add(cell);
                }
                addCells.Add(cell);
            }
            refreshLV(showCells);
            addCells.Clear();
        }

        private void radioButtonBg_CheckedChanged(object sender, EventArgs e)
        {
            if (radioButtonBg.Checked)
            {
                DiyQueryAllLteOptTimeFixDB query = new DiyQueryAllLteOptTimeFixDB(manager.CurCity);
                query.Query();

                showOptTimes(query.Times);
            }
            else
            {
                listBoxTime.Items.Clear();
            }
        }

        private void showOptTimes(List<string> times)
        {
            listBoxTime.Items.Clear();
            foreach (string time in times)
            {
                listBoxTime.Items.Add(time);
            }
            if (listBoxTime.Items.Count > 0)
            {
                listBoxTime.SelectedIndex = 0;
            }
        }
    }

    public class ReportInfo
    {
        public int time { get; set; }
        public double rate { get; set; }
        public double mean { get; set; }
        public double best { get; set; }

        public ReportInfo(int time, double rate, double mean, double best)
        {
            this.time = time;
            this.rate = rate;
            this.mean = mean;
            this.best = best;
        }
    }
}
