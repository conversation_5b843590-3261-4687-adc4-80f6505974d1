﻿using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class CellSetResultBase
    {
        public Dictionary<string, CellSetResultDetail> ResultDic { get; set; } = new Dictionary<string, CellSetResultDetail>();

        public abstract void DealEarfcnCell(Dictionary<string, Dictionary<ICell, CellStater>> regionCellDic, List<string> selectedRegionNames);
        public abstract void DealCell(Dictionary<string, Dictionary<ICell, CellStater>> regionCellDic,
            Dictionary<string, Dictionary<string, CellStater>> regionUnknowCellDic,
            Dictionary<string, long> regionTestPointCnt,
            List<string> selectedRegionNames, QueryCondition cond);
        public abstract void DealBts(Dictionary<string, Dictionary<ISite, BtsStater>> regionBtsDic,
            Dictionary<string, long> regionBtsCntDic, List<string> selectedRegionNames);
        public abstract void DealNbCell(Dictionary<string, Dictionary<ICell, List<float>>> RegionNbCellDic,
            List<SNCellGatherInfo> SNCellList, List<string> selectedRegionNames);

        protected CellSetResultDetail getResultDetail(Dictionary<string, CellSetResultDetail> resultDic, string regionName)
        {
            CellSetResultDetail nrResult;
            if (!resultDic.TryGetValue(regionName, out nrResult))
            {
                nrResult = new CellSetResultDetail();
                resultDic.Add(regionName, nrResult);
            }

            return nrResult;
        }

        #region DealEarfcnCell
        #endregion

        #region DealCell
        #endregion

        #region DealBts
        #endregion

        #region DealNbCell
        #endregion


        public class SNCellGatherInfo
        {
            public List<ICell> NCells
            {
                get;
                private set;
            }
            public ICell SCell
            {
                get;
                set;
            }
            public SNCellGatherInfo()
            {
                NCells = new List<ICell>();
            }
        }

        protected virtual Dictionary<string, List<ICell>> makeRegionCellDic(List<ICell> csRetDataUnused, QueryCondition cond)
        {
            Dictionary<string, List<ICell>> cellsInRegion = new Dictionary<string, List<ICell>>();
            foreach (ICell cell in csRetDataUnused)
            {
                foreach (var street in cond.Geometorys.SelectedStreetFeatures)
                {
                    MTPolygon polygon = new MTPolygon();
                    polygon.AppendRoadToPolygon(street.geoShape);

                    if (polygon.CheckPointInRegion(cell.Longitude, cell.Latitude))
                    {
                        List<ICell> cells = null;
                        if (!cellsInRegion.TryGetValue(street.name, out cells))
                        {
                            cells = new List<ICell>();
                            cellsInRegion.Add(street.name, cells);
                        }
                        cells.Add(cell);
                    }
                }
            }
            return cellsInRegion;
        }
    }

    public class NRCellSetResultNR : CellSetResultBase
    {
        public override void DealEarfcnCell(Dictionary<string, Dictionary<ICell, CellStater>> regionCellDic, List<string> selectedRegionNames)
        {
            foreach (string regionName in selectedRegionNames)
            {
                CellSetResultDetail result = getResultDetail(ResultDic, regionName);

                Dictionary<int, CellSetResultDetail.EarfcnCellResult> earfcnCellCountDic
                    = new Dictionary<int, CellSetResultDetail.EarfcnCellResult>();
                int cellCountTotal = 0;
                int sampleCountTotal = 0;

                foreach (var regionCell in regionCellDic)
                {
                    setEarfcnCellData(regionCellDic, regionName, earfcnCellCountDic, ref cellCountTotal, ref sampleCountTotal);
                }

                foreach (var earfcnCell in earfcnCellCountDic.Values)
                {
                    earfcnCell.CellTotalCount = cellCountTotal;
                    earfcnCell.TpTotalCount = sampleCountTotal;
                    earfcnCell.Calculate();
                    result.EarfcnCellResultList.Add(earfcnCell);
                }
            }
        }

        private void setEarfcnCellData(Dictionary<string, Dictionary<ICell, CellStater>> regionCellDic,
            string regionName, Dictionary<int, CellSetResultDetail.EarfcnCellResult> earfcnCellCountDic,
            ref int cellCountTotal, ref int sampleCountTotal)
        {
            Dictionary<ICell, CellStater> cellStaterMap;
            if (regionCellDic.TryGetValue(regionName, out cellStaterMap))
            {
                foreach (ICell cell in cellStaterMap.Keys)
                {
                    if (cell is NRCell)

                    {
                        NRCell nrCell = cell as NRCell;
                        if (earfcnCellCountDic.ContainsKey(nrCell.SSBARFCN))
                        {
                            earfcnCellCountDic[nrCell.SSBARFCN].CellCount++;
                            earfcnCellCountDic[nrCell.SSBARFCN].TPCount += cellStaterMap[nrCell].TPCount;
                        }
                        else
                        {
                            CellSetResultDetail.EarfcnCellResult stat = new CellSetResultDetail.EarfcnCellResult();
                            stat.RegionName = regionName;
                            stat.CellType = "NR";
                            stat.EARFCN = nrCell.SSBARFCN;
                            stat.CellCount++;
                            stat.TPCount += cellStaterMap[nrCell].TPCount;
                            earfcnCellCountDic[nrCell.SSBARFCN] = stat;
                        }
                        sampleCountTotal += cellStaterMap[nrCell].TPCount;
                        cellCountTotal++;
                    }
                }
            }
        }

        public override void DealCell(Dictionary<string, Dictionary<ICell, CellStater>> regionCellDic,
            Dictionary<string, Dictionary<string, CellStater>> regionUnknowCellDic,
            Dictionary<string, long> regionTestPointCnt,
            List<string> selectedRegionNames, QueryCondition cond)
        {
            List<ICell> csRetDataUnused = getAllRegionCell();
            Dictionary<string, List<ICell>> cellsInRegion = makeRegionCellDic(csRetDataUnused, cond);

            foreach (string regionName in selectedRegionNames)
            {
                CellSetResultDetail result = getResultDetail(ResultDic, regionName);

                Dictionary<ICell, CellStater> cellStaterMap = null;
                if (!regionCellDic.TryGetValue(regionName, out cellStaterMap))
                {
                    cellStaterMap = new Dictionary<ICell, CellStater>();
                }
                Dictionary<string, CellStater> unknownCellStaterMap = null;
                if (!regionUnknowCellDic.TryGetValue(regionName, out unknownCellStaterMap))
                {
                    unknownCellStaterMap = new Dictionary<string, CellStater>();
                }
                long totalCount;
                regionTestPointCnt.TryGetValue(regionName, out totalCount);

                foreach (var item in cellStaterMap)
                {
                    CellSetResultDetail.CellSetOfRegionResult res = new CellSetResultDetail.CellSetOfRegionResult();
                    res.RegionName = regionName;
                    res.CellType = "NR";
                    res.Stater = item.Value;
                    res.Stater.Cell = item.Key;
                    res.Stater.TPTotalCount = totalCount;
                    res.Stater.Calculate();
                    result.CellSetOfRegionResultList.Add(res);
                }
                foreach (var item in unknownCellStaterMap)
                {
                    CellSetResultDetail.CellSetOfRegionResult res = new CellSetResultDetail.CellSetOfRegionResult();
                    res.RegionName = regionName;
                    res.CellType = "NR";
                    res.Stater = item.Value;
                    res.Stater.CellName = item.Key;
                    res.Stater.TPTotalCount = totalCount;
                    res.Stater.Calculate();
                    result.CellSetOfRegionResultList.Add(res);
                }

                dealUnusedCell(cellsInRegion, regionName, result, cellStaterMap);
            }
        }

        private static void dealUnusedCell(Dictionary<string, List<ICell>> cellsInRegion, string regionName, 
            CellSetResultDetail result, Dictionary<ICell, CellStater> cellStaterMap)
        {
            List<ICell> cells;
            if (cellsInRegion.TryGetValue(regionName, out cells))
            {
                foreach (ICell cell in cellStaterMap.Keys)
                {
                    ICell regCell = cells.Find(x => x.ID == cell.ID);
                    if (regCell != null)
                    {
                        cells.Remove(regCell);
                    }
                }

                foreach (var cell in cells)
                {
                    if (cell is NRCell)
                    {
                        NRCell nrCell = cell as NRCell;
                        CellSetResultDetail.UnusedCellSetOfRegionResult unusedSet = new CellSetResultDetail.UnusedCellSetOfRegionResult();
                        unusedSet.RegionName = regionName;
                        unusedSet.CellName = nrCell.Name;
                        unusedSet.CellType = "NR";
                        unusedSet.EARFCN = nrCell.SSBARFCN;
                        unusedSet.PCI = nrCell.PCI;
                        unusedSet.TAC = nrCell.TAC;
                        unusedSet.NCI = nrCell.NCI;
                        result.UnusedCellSetOfRegionResultList.Add(unusedSet);
                    }
                }
            }
        }

        private List<ICell> getAllRegionCell()
        {
            List<NRCell> cells = MainModel.GetInstance().CellManager.GetNRCells(DateTime.Now);
            List<ICell> regionCells = new List<ICell>();
            foreach (NRCell cell in cells)
            {
                if (MainModel.GetInstance().SearchGeometrys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                {
                    regionCells.Add(cell);
                }
            }

            return regionCells;
        }


        public override void DealBts(Dictionary<string, Dictionary<ISite, BtsStater>> regionBtsDic,
            Dictionary<string, long> regionBtsCntDic, List<string> selectedRegionNames)
        {
            foreach (string regionName in selectedRegionNames)
            {
                CellSetResultDetail result = getResultDetail(ResultDic, regionName);

                Dictionary<ISite, BtsStater> btsStaterMap = new Dictionary<ISite, BtsStater>();
                if (regionBtsDic.ContainsKey(regionName))
                {
                    btsStaterMap = regionBtsDic[regionName];
                }

                long totalBtsCount = 0;
                regionBtsCntDic.TryGetValue(regionName, out totalBtsCount);

                foreach (var item in btsStaterMap)
                {
                    CellSetResultDetail.BtsSetOfRegionResult res = new CellSetResultDetail.BtsSetOfRegionResult();
                    res.RegionName = regionName;
                    res.Type = "NR";
                    res.Stater = item.Value;
                    res.Stater.BTS = item.Key;
                    res.Stater.TPTotalCount = totalBtsCount;
                    res.Stater.Calculate();
                    result.BtsSetOfRegionResultList.Add(res);
                }
            }
        }

        public override void DealNbCell(Dictionary<string, Dictionary<ICell, List<float>>> RegionNbCellDic,
            List<SNCellGatherInfo> SNCellList, List<string> selectedRegionNames)
        {
            foreach (string regionName in selectedRegionNames)
            {
                CellSetResultDetail result = getResultDetail(ResultDic, regionName);

                Dictionary<ICell, List<float>> nbCellRetData = new Dictionary<ICell, List<float>>();
                if (RegionNbCellDic.ContainsKey(regionName))
                {
                    nbCellRetData = RegionNbCellDic[regionName];
                }

                foreach (var item in nbCellRetData)
                {
                    if (item.Key is NRCell)
                    {
                        NRCell nrCell = item.Key as NRCell;
                        CellSetResultDetail.NBCellSetOfRegionResult res = new CellSetResultDetail.NBCellSetOfRegionResult();
                        res.RegionName = regionName;
                        res.CellType = "NR";
                        res.CellName = nrCell.Name;
                        res.EARFCN = nrCell.SSBARFCN;
                        res.PCI = nrCell.PCI;
                        res.TAC = nrCell.TAC;
                        res.NCI = nrCell.NCI;
                        res.Count = item.Value.Count;
                        double rsrpTotal = 0;
                        foreach (var rsrp in item.Value)
                        {
                            rsrpTotal += rsrp;
                        }
                        res.RsrpAvg = Math.Round(rsrpTotal / res.Count, 2);
                        result.NBCellSetOfRegionResultList.Add(res);
                    }
                }
                doClasify(SNCellList, regionName, result);
            }
        }

        //最强邻区个数
        readonly int neighborCellCnt = 5;
        private void doClasify(List<SNCellGatherInfo> SNCellList, string regionName, CellSetResultDetail result)
        {
            Dictionary<ICell, int[]> cellCountDic = new Dictionary<ICell, int[]>();
            foreach (SNCellGatherInfo snCellInfo in SNCellList)
            {
                addSNCells(snCellInfo.SCell, true, cellCountDic);
                for (int i = 0; i < snCellInfo.NCells.Count && i < neighborCellCnt; ++i)
                {
                    addSNCells(snCellInfo.NCells[i], false, cellCountDic);
                }
            }

            foreach (var item in cellCountDic)
            {
                if (item.Key is NRCell)
                {
                    NRCell nrCell = item.Key as NRCell;
                    CellSetResultDetail.SCellAndNCellResult res = new CellSetResultDetail.SCellAndNCellResult();
                    res.RegionName = regionName;
                    res.CellType = "NR";
                    res.CellName = nrCell.Name;
                    res.EARFCN = nrCell.SSBARFCN;
                    res.PCI = nrCell.PCI;
                    res.TAC = nrCell.TAC;
                    res.NCI = nrCell.NCI;
                    res.SCellCount = item.Value[0];
                    res.NCellCount = item.Value[1];
                    result.SCellAndNCellResultList.Add(res);
                }
            }
        }

        private void addSNCells(ICell cell, bool isSCell, Dictionary<ICell, int[]> cellCountDic)
        {
            if (cell == null)
            {
                return;
            }
            if (!cellCountDic.ContainsKey(cell))
            {
                cellCountDic.Add(cell, new int[2]);
            }
            if (isSCell)
            {
                ++cellCountDic[cell][0];
            }
            else
            {
                ++cellCountDic[cell][1];
            }
        }
    }
}
