﻿namespace MasterCom.RAMS.NewBlackBlock
{
    partial class NewBlackBlockCondDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(NewBlackBlockCondDlg));
            this.dateTimePickerSnap = new System.Windows.Forms.DateTimePicker();
            this.label1 = new System.Windows.Forms.Label();
            this.btnQuery = new System.Windows.Forms.Button();
            this.groupBoxOther = new System.Windows.Forms.GroupBox();
            this.tbxBlockID = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.checkBoxSearchBuilding = new System.Windows.Forms.CheckBox();
            this.cbxNoExamBlock = new System.Windows.Forms.CheckBox();
            this.cbxNoPreClose = new System.Windows.Forms.CheckBox();
            this.cbxExamBlock = new System.Windows.Forms.CheckBox();
            this.cbxSetColor = new System.Windows.Forms.CheckBox();
            this.tbxCellName = new System.Windows.Forms.TextBox();
            this.tbxAddress = new System.Windows.Forms.TextBox();
            this.tbxReason = new System.Windows.Forms.TextBox();
            this.tbxName = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxStatus5 = new System.Windows.Forms.CheckBox();
            this.cbxStatus0 = new System.Windows.Forms.CheckBox();
            this.cbxStatus4 = new System.Windows.Forms.CheckBox();
            this.cbxStatus23 = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.tbxMax = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.tbxMin = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.btnAbEventColor = new System.Windows.Forms.Button();
            this.crScreen = new MasterCom.MControls.ColorRangeScreen();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.btnAddEventWeight = new System.Windows.Forms.Button();
            this.btnDelWeight = new System.Windows.Forms.Button();
            this.btnSaveWeightConfig = new System.Windows.Forms.Button();
            this.listViewResult = new System.Windows.Forms.ListView();
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.btnModify = new System.Windows.Forms.Button();
            this.btnAppend = new System.Windows.Forms.Button();
            this.numericUpDownWeight = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.cbboxEventName = new System.Windows.Forms.ComboBox();
            this.chkCompareDate = new System.Windows.Forms.CheckBox();
            this.dtCompareDate = new System.Windows.Forms.DateTimePicker();
            this.cbxBlackBlockType = new System.Windows.Forms.ComboBox();
            this.dateTimePickerAbBeginTime = new System.Windows.Forms.DateTimePicker();
            this.chkAbnormalBeginTime = new System.Windows.Forms.CheckBox();
            this.label12 = new System.Windows.Forms.Label();
            this.dateTimePickerAbEndTime = new System.Windows.Forms.DateTimePicker();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label13 = new System.Windows.Forms.Label();
            this.chkTopNVip = new System.Windows.Forms.CheckBox();
            this.label14 = new System.Windows.Forms.Label();
            this.numUDTopVIPN = new System.Windows.Forms.NumericUpDown();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.groupBoxOther.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownWeight)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numUDTopVIPN)).BeginInit();
            this.groupBox5.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.SuspendLayout();
            // 
            // dateTimePickerSnap
            // 
            this.dateTimePickerSnap.Location = new System.Drawing.Point(121, 20);
            this.dateTimePickerSnap.Name = "dateTimePickerSnap";
            this.dateTimePickerSnap.Size = new System.Drawing.Size(126, 21);
            this.dateTimePickerSnap.TabIndex = 0;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(17, 27);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "创建结束日期";
            // 
            // btnQuery
            // 
            this.btnQuery.Location = new System.Drawing.Point(170, 35);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(71, 23);
            this.btnQuery.TabIndex = 2;
            this.btnQuery.Text = "查询";
            this.btnQuery.UseVisualStyleBackColor = true;
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // groupBoxOther
            // 
            this.groupBoxOther.Controls.Add(this.tbxBlockID);
            this.groupBoxOther.Controls.Add(this.label11);
            this.groupBoxOther.Controls.Add(this.checkBoxSearchBuilding);
            this.groupBoxOther.Controls.Add(this.cbxNoExamBlock);
            this.groupBoxOther.Controls.Add(this.cbxNoPreClose);
            this.groupBoxOther.Controls.Add(this.cbxExamBlock);
            this.groupBoxOther.Controls.Add(this.cbxSetColor);
            this.groupBoxOther.Controls.Add(this.tbxCellName);
            this.groupBoxOther.Controls.Add(this.tbxAddress);
            this.groupBoxOther.Controls.Add(this.tbxReason);
            this.groupBoxOther.Controls.Add(this.tbxName);
            this.groupBoxOther.Controls.Add(this.label2);
            this.groupBoxOther.Controls.Add(this.label6);
            this.groupBoxOther.Controls.Add(this.label5);
            this.groupBoxOther.Controls.Add(this.label4);
            this.groupBoxOther.Location = new System.Drawing.Point(12, 163);
            this.groupBoxOther.Name = "groupBoxOther";
            this.groupBoxOther.Size = new System.Drawing.Size(797, 132);
            this.groupBoxOther.TabIndex = 4;
            this.groupBoxOther.TabStop = false;
            this.groupBoxOther.Text = "其他条件";
            // 
            // tbxBlockID
            // 
            this.tbxBlockID.Location = new System.Drawing.Point(303, 24);
            this.tbxBlockID.Name = "tbxBlockID";
            this.tbxBlockID.Size = new System.Drawing.Size(137, 21);
            this.tbxBlockID.TabIndex = 8;
            this.tbxBlockID.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.tbxBlockID_KeyPress);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(244, 29);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(41, 12);
            this.label11.TabIndex = 7;
            this.label11.Text = "黑点ID";
            // 
            // checkBoxSearchBuilding
            // 
            this.checkBoxSearchBuilding.AutoSize = true;
            this.checkBoxSearchBuilding.Location = new System.Drawing.Point(533, 59);
            this.checkBoxSearchBuilding.Name = "checkBoxSearchBuilding";
            this.checkBoxSearchBuilding.Size = new System.Drawing.Size(108, 16);
            this.checkBoxSearchBuilding.TabIndex = 0;
            this.checkBoxSearchBuilding.Text = "查询周边建筑物";
            this.checkBoxSearchBuilding.UseVisualStyleBackColor = true;
            // 
            // cbxNoExamBlock
            // 
            this.cbxNoExamBlock.AutoSize = true;
            this.cbxNoExamBlock.Checked = true;
            this.cbxNoExamBlock.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxNoExamBlock.Location = new System.Drawing.Point(374, 99);
            this.cbxNoExamBlock.Name = "cbxNoExamBlock";
            this.cbxNoExamBlock.Size = new System.Drawing.Size(84, 16);
            this.cbxNoExamBlock.TabIndex = 0;
            this.cbxNoExamBlock.Text = "不考核黑点";
            this.cbxNoExamBlock.UseVisualStyleBackColor = true;
            this.cbxNoExamBlock.Visible = false;
            // 
            // cbxNoPreClose
            // 
            this.cbxNoPreClose.AutoSize = true;
            this.cbxNoPreClose.Location = new System.Drawing.Point(77, 99);
            this.cbxNoPreClose.Name = "cbxNoPreClose";
            this.cbxNoPreClose.Size = new System.Drawing.Size(120, 16);
            this.cbxNoPreClose.TabIndex = 0;
            this.cbxNoPreClose.Text = "不显示预关闭黑点";
            this.cbxNoPreClose.UseVisualStyleBackColor = true;
            this.cbxNoPreClose.Visible = false;
            // 
            // cbxExamBlock
            // 
            this.cbxExamBlock.AutoSize = true;
            this.cbxExamBlock.Checked = true;
            this.cbxExamBlock.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxExamBlock.Location = new System.Drawing.Point(229, 99);
            this.cbxExamBlock.Name = "cbxExamBlock";
            this.cbxExamBlock.Size = new System.Drawing.Size(96, 16);
            this.cbxExamBlock.TabIndex = 0;
            this.cbxExamBlock.Text = "参与考核黑点";
            this.cbxExamBlock.UseVisualStyleBackColor = true;
            this.cbxExamBlock.Visible = false;
            // 
            // cbxSetColor
            // 
            this.cbxSetColor.AutoSize = true;
            this.cbxSetColor.ForeColor = System.Drawing.SystemColors.ActiveCaption;
            this.cbxSetColor.Location = new System.Drawing.Point(533, 99);
            this.cbxSetColor.Name = "cbxSetColor";
            this.cbxSetColor.Size = new System.Drawing.Size(48, 16);
            this.cbxSetColor.TabIndex = 6;
            this.cbxSetColor.Text = "设置";
            this.cbxSetColor.UseVisualStyleBackColor = true;
            this.cbxSetColor.CheckedChanged += new System.EventHandler(this.cbxSetColor_CheckedChanged);
            // 
            // tbxCellName
            // 
            this.tbxCellName.Location = new System.Drawing.Point(533, 24);
            this.tbxCellName.Name = "tbxCellName";
            this.tbxCellName.Size = new System.Drawing.Size(137, 21);
            this.tbxCellName.TabIndex = 2;
            // 
            // tbxAddress
            // 
            this.tbxAddress.Location = new System.Drawing.Point(303, 57);
            this.tbxAddress.Name = "tbxAddress";
            this.tbxAddress.Size = new System.Drawing.Size(137, 21);
            this.tbxAddress.TabIndex = 2;
            // 
            // tbxReason
            // 
            this.tbxReason.Location = new System.Drawing.Point(77, 57);
            this.tbxReason.Name = "tbxReason";
            this.tbxReason.Size = new System.Drawing.Size(143, 21);
            this.tbxReason.TabIndex = 2;
            // 
            // tbxName
            // 
            this.tbxName.Location = new System.Drawing.Point(77, 24);
            this.tbxName.Name = "tbxName";
            this.tbxName.Size = new System.Drawing.Size(143, 21);
            this.tbxName.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(476, 29);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "小区名";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(243, 60);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 12);
            this.label6.TabIndex = 1;
            this.label6.Text = "地点描述";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(17, 60);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 1;
            this.label5.Text = "原因描述";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(17, 29);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(29, 12);
            this.label4.TabIndex = 1;
            this.label4.Text = "名称";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbxStatus5);
            this.groupBox1.Controls.Add(this.cbxStatus0);
            this.groupBox1.Controls.Add(this.cbxStatus4);
            this.groupBox1.Controls.Add(this.cbxStatus23);
            this.groupBox1.Location = new System.Drawing.Point(12, 106);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(797, 51);
            this.groupBox1.TabIndex = 5;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "状态类型";
            // 
            // cbxStatus5
            // 
            this.cbxStatus5.AutoSize = true;
            this.cbxStatus5.Location = new System.Drawing.Point(533, 20);
            this.cbxStatus5.Name = "cbxStatus5";
            this.cbxStatus5.Size = new System.Drawing.Size(84, 16);
            this.cbxStatus5.TabIndex = 0;
            this.cbxStatus5.Text = "已归并黑点";
            this.cbxStatus5.UseVisualStyleBackColor = true;
            this.cbxStatus5.Visible = false;
            // 
            // cbxStatus0
            // 
            this.cbxStatus0.AutoSize = true;
            this.cbxStatus0.Location = new System.Drawing.Point(381, 20);
            this.cbxStatus0.Name = "cbxStatus0";
            this.cbxStatus0.Size = new System.Drawing.Size(96, 16);
            this.cbxStatus0.TabIndex = 0;
            this.cbxStatus0.Text = "萌芽状态黑点";
            this.cbxStatus0.UseVisualStyleBackColor = true;
            // 
            // cbxStatus4
            // 
            this.cbxStatus4.AutoSize = true;
            this.cbxStatus4.Location = new System.Drawing.Point(229, 20);
            this.cbxStatus4.Name = "cbxStatus4";
            this.cbxStatus4.Size = new System.Drawing.Size(84, 16);
            this.cbxStatus4.TabIndex = 0;
            this.cbxStatus4.Text = "已关闭黑点";
            this.cbxStatus4.UseVisualStyleBackColor = true;
            // 
            // cbxStatus23
            // 
            this.cbxStatus23.AutoSize = true;
            this.cbxStatus23.Location = new System.Drawing.Point(77, 20);
            this.cbxStatus23.Name = "cbxStatus23";
            this.cbxStatus23.Size = new System.Drawing.Size(84, 16);
            this.cbxStatus23.TabIndex = 0;
            this.cbxStatus23.Text = "已创建黑点";
            this.cbxStatus23.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.tbxMax);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.tbxMin);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.btnAbEventColor);
            this.groupBox2.Controls.Add(this.crScreen);
            this.groupBox2.Location = new System.Drawing.Point(12, 316);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(630, 117);
            this.groupBox2.TabIndex = 8;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "按异常事件个数设置颜色";
            this.groupBox2.Visible = false;
            // 
            // tbxMax
            // 
            this.tbxMax.Location = new System.Drawing.Point(329, 78);
            this.tbxMax.Name = "tbxMax";
            this.tbxMax.Size = new System.Drawing.Size(116, 21);
            this.tbxMax.TabIndex = 5;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(250, 82);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(59, 12);
            this.label7.TabIndex = 4;
            this.label7.Text = "最大门限:";
            // 
            // tbxMin
            // 
            this.tbxMin.Location = new System.Drawing.Point(104, 78);
            this.tbxMin.Name = "tbxMin";
            this.tbxMin.Size = new System.Drawing.Size(116, 21);
            this.tbxMin.TabIndex = 3;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(28, 82);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(59, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "最小门限:";
            // 
            // btnAbEventColor
            // 
            this.btnAbEventColor.Location = new System.Drawing.Point(502, 21);
            this.btnAbEventColor.Name = "btnAbEventColor";
            this.btnAbEventColor.Size = new System.Drawing.Size(71, 27);
            this.btnAbEventColor.TabIndex = 1;
            this.btnAbEventColor.Text = "着色设置";
            this.btnAbEventColor.UseVisualStyleBackColor = true;
            this.btnAbEventColor.Click += new System.EventHandler(this.btnAbEventColor_Click);
            // 
            // crScreen
            // 
            this.crScreen.ColorRanges = ((System.Collections.Generic.List<MasterCom.MControls.ColorRange>)(resources.GetObject("crScreen.ColorRanges")));
            this.crScreen.Location = new System.Drawing.Point(29, 23);
            this.crScreen.Name = "crScreen";
            this.crScreen.RangeMode = true;
            this.crScreen.Size = new System.Drawing.Size(465, 24);
            this.crScreen.TabIndex = 0;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.btnAddEventWeight);
            this.groupBox3.Controls.Add(this.btnDelWeight);
            this.groupBox3.Controls.Add(this.btnSaveWeightConfig);
            this.groupBox3.Controls.Add(this.listViewResult);
            this.groupBox3.Controls.Add(this.btnModify);
            this.groupBox3.Controls.Add(this.btnAppend);
            this.groupBox3.Controls.Add(this.numericUpDownWeight);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.cbboxEventName);
            this.groupBox3.Location = new System.Drawing.Point(12, 451);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(793, 180);
            this.groupBox3.TabIndex = 11;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "异常事件权值设置";
            // 
            // btnAddEventWeight
            // 
            this.btnAddEventWeight.Location = new System.Drawing.Point(304, 40);
            this.btnAddEventWeight.Name = "btnAddEventWeight";
            this.btnAddEventWeight.Size = new System.Drawing.Size(64, 27);
            this.btnAddEventWeight.TabIndex = 9;
            this.btnAddEventWeight.Text = "批量...";
            this.btnAddEventWeight.UseVisualStyleBackColor = true;
            this.btnAddEventWeight.Click += new System.EventHandler(this.btnAddEventWeight_Click);
            // 
            // btnDelWeight
            // 
            this.btnDelWeight.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnDelWeight.Image = global::MasterCom.RAMS.Properties.Resources.delete;
            this.btnDelWeight.Location = new System.Drawing.Point(738, 80);
            this.btnDelWeight.Name = "btnDelWeight";
            this.btnDelWeight.Size = new System.Drawing.Size(28, 27);
            this.btnDelWeight.TabIndex = 8;
            this.btnDelWeight.UseVisualStyleBackColor = true;
            this.btnDelWeight.Click += new System.EventHandler(this.btnDelWeight_Click);
            // 
            // btnSaveWeightConfig
            // 
            this.btnSaveWeightConfig.Location = new System.Drawing.Point(231, 132);
            this.btnSaveWeightConfig.Name = "btnSaveWeightConfig";
            this.btnSaveWeightConfig.Size = new System.Drawing.Size(66, 27);
            this.btnSaveWeightConfig.TabIndex = 7;
            this.btnSaveWeightConfig.Text = "保存";
            this.btnSaveWeightConfig.UseVisualStyleBackColor = true;
            this.btnSaveWeightConfig.Click += new System.EventHandler(this.btnSaveWeightConfig_Click);
            // 
            // listViewResult
            // 
            this.listViewResult.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeader2});
            this.listViewResult.FullRowSelect = true;
            this.listViewResult.Location = new System.Drawing.Point(428, 23);
            this.listViewResult.MultiSelect = false;
            this.listViewResult.Name = "listViewResult";
            this.listViewResult.Size = new System.Drawing.Size(303, 135);
            this.listViewResult.TabIndex = 6;
            this.listViewResult.UseCompatibleStateImageBehavior = false;
            this.listViewResult.View = System.Windows.Forms.View.Details;
            this.listViewResult.SelectedIndexChanged += new System.EventHandler(this.listViewResult_SelectedIndexChanged);
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "事件名称";
            this.columnHeader1.Width = 159;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "权值";
            this.columnHeader2.Width = 72;
            // 
            // btnModify
            // 
            this.btnModify.Location = new System.Drawing.Point(157, 132);
            this.btnModify.Name = "btnModify";
            this.btnModify.Size = new System.Drawing.Size(66, 27);
            this.btnModify.TabIndex = 5;
            this.btnModify.Text = "修改";
            this.btnModify.UseVisualStyleBackColor = true;
            this.btnModify.Click += new System.EventHandler(this.btnModify_Click);
            // 
            // btnAppend
            // 
            this.btnAppend.Location = new System.Drawing.Point(83, 132);
            this.btnAppend.Name = "btnAppend";
            this.btnAppend.Size = new System.Drawing.Size(66, 27);
            this.btnAppend.TabIndex = 4;
            this.btnAppend.Text = "添加";
            this.btnAppend.UseVisualStyleBackColor = true;
            this.btnAppend.Click += new System.EventHandler(this.btnAppend_Click);
            // 
            // numericUpDownWeight
            // 
            this.numericUpDownWeight.DecimalPlaces = 1;
            this.numericUpDownWeight.Increment = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.numericUpDownWeight.Location = new System.Drawing.Point(135, 84);
            this.numericUpDownWeight.Name = "numericUpDownWeight";
            this.numericUpDownWeight.Size = new System.Drawing.Size(101, 21);
            this.numericUpDownWeight.TabIndex = 3;
            this.numericUpDownWeight.Value = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(59, 86);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(59, 12);
            this.label9.TabIndex = 2;
            this.label9.Text = "事件权值:";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(59, 45);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(59, 12);
            this.label8.TabIndex = 1;
            this.label8.Text = "事件名称:";
            // 
            // cbboxEventName
            // 
            this.cbboxEventName.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbboxEventName.FormattingEnabled = true;
            this.cbboxEventName.Location = new System.Drawing.Point(135, 42);
            this.cbboxEventName.Name = "cbboxEventName";
            this.cbboxEventName.Size = new System.Drawing.Size(161, 22);
            this.cbboxEventName.TabIndex = 0;
            // 
            // chkCompareDate
            // 
            this.chkCompareDate.AutoSize = true;
            this.chkCompareDate.Location = new System.Drawing.Point(19, 54);
            this.chkCompareDate.Name = "chkCompareDate";
            this.chkCompareDate.Size = new System.Drawing.Size(96, 16);
            this.chkCompareDate.TabIndex = 13;
            this.chkCompareDate.Text = "比较起始日期";
            this.chkCompareDate.UseVisualStyleBackColor = true;
            this.chkCompareDate.CheckedChanged += new System.EventHandler(this.chkBeginDate_CheckedChanged);
            // 
            // dtCompareDate
            // 
            this.dtCompareDate.Enabled = false;
            this.dtCompareDate.Location = new System.Drawing.Point(121, 50);
            this.dtCompareDate.Name = "dtCompareDate";
            this.dtCompareDate.Size = new System.Drawing.Size(126, 21);
            this.dtCompareDate.TabIndex = 14;
            // 
            // cbxBlackBlockType
            // 
            this.cbxBlackBlockType.DisplayMember = "Name";
            this.cbxBlackBlockType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxBlackBlockType.FormattingEnabled = true;
            this.cbxBlackBlockType.Location = new System.Drawing.Point(7, 35);
            this.cbxBlackBlockType.Name = "cbxBlackBlockType";
            this.cbxBlackBlockType.Size = new System.Drawing.Size(157, 22);
            this.cbxBlackBlockType.TabIndex = 15;
            // 
            // dateTimePickerAbBeginTime
            // 
            this.dateTimePickerAbBeginTime.Enabled = false;
            this.dateTimePickerAbBeginTime.Location = new System.Drawing.Point(374, 50);
            this.dateTimePickerAbBeginTime.Name = "dateTimePickerAbBeginTime";
            this.dateTimePickerAbBeginTime.Size = new System.Drawing.Size(126, 21);
            this.dateTimePickerAbBeginTime.TabIndex = 20;
            // 
            // chkAbnormalBeginTime
            // 
            this.chkAbnormalBeginTime.AutoSize = true;
            this.chkAbnormalBeginTime.Location = new System.Drawing.Point(272, 54);
            this.chkAbnormalBeginTime.Name = "chkAbnormalBeginTime";
            this.chkAbnormalBeginTime.Size = new System.Drawing.Size(96, 16);
            this.chkAbnormalBeginTime.TabIndex = 19;
            this.chkAbnormalBeginTime.Text = "异常起始日期";
            this.chkAbnormalBeginTime.UseVisualStyleBackColor = true;
            this.chkAbnormalBeginTime.CheckedChanged += new System.EventHandler(this.chkAbnormalBeginTime_CheckedChanged);
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(270, 27);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(77, 12);
            this.label12.TabIndex = 18;
            this.label12.Text = "异常结束日期";
            // 
            // dateTimePickerAbEndTime
            // 
            this.dateTimePickerAbEndTime.Enabled = false;
            this.dateTimePickerAbEndTime.Location = new System.Drawing.Point(374, 20);
            this.dateTimePickerAbEndTime.Name = "dateTimePickerAbEndTime";
            this.dateTimePickerAbEndTime.Size = new System.Drawing.Size(126, 21);
            this.dateTimePickerAbEndTime.TabIndex = 17;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.label13);
            this.groupBox4.Controls.Add(this.chkTopNVip);
            this.groupBox4.Controls.Add(this.label14);
            this.groupBox4.Controls.Add(this.numUDTopVIPN);
            this.groupBox4.Location = new System.Drawing.Point(648, 316);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(159, 117);
            this.groupBox4.TabIndex = 21;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "权重筛选";
            this.groupBox4.Visible = false;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(23, 66);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 4;
            this.label13.Text = "前";
            // 
            // chkTopNVip
            // 
            this.chkTopNVip.AutoSize = true;
            this.chkTopNVip.Location = new System.Drawing.Point(10, 37);
            this.chkTopNVip.Name = "chkTopNVip";
            this.chkTopNVip.Size = new System.Drawing.Size(108, 16);
            this.chkTopNVip.TabIndex = 3;
            this.chkTopNVip.Text = "显示权重较高的";
            this.chkTopNVip.UseVisualStyleBackColor = true;
            this.chkTopNVip.CheckedChanged += new System.EventHandler(this.chkTopNVip_CheckedChanged);
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(86, 66);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(41, 12);
            this.label14.TabIndex = 2;
            this.label14.Text = "个黑点";
            // 
            // numUDTopVIPN
            // 
            this.numUDTopVIPN.Enabled = false;
            this.numUDTopVIPN.Location = new System.Drawing.Point(44, 62);
            this.numUDTopVIPN.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numUDTopVIPN.Name = "numUDTopVIPN";
            this.numUDTopVIPN.Size = new System.Drawing.Size(42, 21);
            this.numUDTopVIPN.TabIndex = 1;
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.chkCompareDate);
            this.groupBox5.Controls.Add(this.label12);
            this.groupBox5.Controls.Add(this.dateTimePickerAbBeginTime);
            this.groupBox5.Controls.Add(this.dateTimePickerAbEndTime);
            this.groupBox5.Controls.Add(this.dtCompareDate);
            this.groupBox5.Controls.Add(this.chkAbnormalBeginTime);
            this.groupBox5.Controls.Add(this.dateTimePickerSnap);
            this.groupBox5.Controls.Add(this.label1);
            this.groupBox5.Location = new System.Drawing.Point(12, 12);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(543, 88);
            this.groupBox5.TabIndex = 22;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "日期设置";
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.cbxBlackBlockType);
            this.groupBox6.Controls.Add(this.btnQuery);
            this.groupBox6.Location = new System.Drawing.Point(561, 12);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(247, 88);
            this.groupBox6.TabIndex = 23;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "黑点类型";
            // 
            // NewBlackBlockCondDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(818, 643);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.groupBoxOther);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.groupBox6);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "NewBlackBlockCondDlg";
            this.Text = "道路黑点检索条件设置";
            this.groupBoxOther.ResumeLayout(false);
            this.groupBoxOther.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownWeight)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numUDTopVIPN)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DateTimePicker dateTimePickerSnap;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnQuery;
        private System.Windows.Forms.GroupBox groupBoxOther;
        private System.Windows.Forms.TextBox tbxReason;
        private System.Windows.Forms.TextBox tbxName;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox tbxAddress;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox cbxStatus0;
        private System.Windows.Forms.CheckBox cbxStatus4;
        private System.Windows.Forms.CheckBox cbxStatus23;
        private System.Windows.Forms.CheckBox cbxStatus5;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Button btnAbEventColor;
        private MasterCom.MControls.ColorRangeScreen crScreen;
        private System.Windows.Forms.TextBox tbxMax;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox tbxMin;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.CheckBox cbxSetColor;
        private System.Windows.Forms.CheckBox cbxExamBlock;
        private System.Windows.Forms.CheckBox cbxNoExamBlock;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.ComboBox cbboxEventName;
        private System.Windows.Forms.NumericUpDown numericUpDownWeight;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Button btnModify;
        private System.Windows.Forms.Button btnAppend;
        private System.Windows.Forms.ListView listViewResult;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.Button btnSaveWeightConfig;
        private System.Windows.Forms.Button btnDelWeight;
        private System.Windows.Forms.Button btnAddEventWeight;
        private System.Windows.Forms.CheckBox cbxNoPreClose;
        private System.Windows.Forms.CheckBox chkCompareDate;
        private System.Windows.Forms.DateTimePicker dtCompareDate;
        private System.Windows.Forms.TextBox tbxCellName;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cbxBlackBlockType;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox tbxBlockID;
        private System.Windows.Forms.DateTimePicker dateTimePickerAbBeginTime;
        private System.Windows.Forms.CheckBox chkAbnormalBeginTime;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.DateTimePicker dateTimePickerAbEndTime;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.NumericUpDown numUDTopVIPN;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.CheckBox chkTopNVip;
        private System.Windows.Forms.CheckBox checkBoxSearchBuilding;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.GroupBox groupBox6;
    }
}