﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTMainCoverCellChangeTable
{
    public partial class SetDiscrepancyRxlevThresholdForm : Form
    {
        public SetDiscrepancyRxlevThresholdForm()
        {
            InitializeComponent();
        }

        public int DiscrepancyRxlevThreshold
        {
            get { return (int)numUDRxlevThreshold.Value; }
        }
    }
}
