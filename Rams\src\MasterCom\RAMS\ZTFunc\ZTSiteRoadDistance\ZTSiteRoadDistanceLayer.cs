﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using AxMapWinGIS;
using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSiteRoadDistanceLayer : LayerBase
    {
        private double siteLon;
        private double siteLat;
        private double roadHeadLon;
        private double roadHeadLat;
        private double roadTailLon;
        private double roadTailLat;
        public ZTSiteRoadDistanceLayer()
            : base("站道距图层")
        {
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            Pen penLine = new Pen(new SolidBrush(Color.Red), 3);
            SolidBrush bushSiteCircle = new SolidBrush(Color.Green);
            int siteSize = 32;
            Rectangle rectangleSite = new Rectangle(0, 0, siteSize, siteSize);
            //道路线
            DbPoint dbP = new DbPoint(this.roadHeadLon, this.roadHeadLat);
            PointF pointRoadHeadStreem;
            gisAdapter.ToDisplay(dbP, out pointRoadHeadStreem);
            dbP = new DbPoint(this.roadTailLon, this.roadTailLat);
            PointF pointRoadTailStreem;
            gisAdapter.ToDisplay(dbP, out pointRoadTailStreem);
            graphics.DrawLine(penLine, pointRoadHeadStreem, pointRoadTailStreem);
            //基站点
            dbP = new DbPoint(this.siteLon, this.siteLat);
            PointF pointSiteScreem;
            gisAdapter.ToDisplay(dbP, out pointSiteScreem);
            rectangleSite.X = (int)pointSiteScreem.X - siteSize /2;
            rectangleSite.Y = (int)pointSiteScreem.Y - siteSize / 2;
            graphics.FillEllipse(bushSiteCircle, rectangleSite);


            //Pen penLine = new Pen(new SolidBrush(Color.Red), 30);
            //graphics.DrawLine(penLine, new PointF(0, 0), new PointF(200, 200));
            //if (Areas == null || !IsVisible)
            //{
            //    return;
            //}

            //Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            //inflatedRect.Inflate(50, 50);
            //DbRect dRect;
            //gisAdapter.FromDisplay(updateRect, out dRect);

            //foreach (AreaBase area in Areas)
            //{
            //    if (dRect.Within(area.Bounds))
            //    {
            //        paintPolygon(graphics, area);
            //    }
            //}
        }

        public void setDrawingData(ZTSiteRoadDistanceItem item)
        {
            this.siteLon = item.SiteLong;
            this.siteLat = item.SiteLat;
            this.roadHeadLon = double.Parse(item.RoadHeadLong);
            this.roadHeadLat = double.Parse(item.RoadHeadLat);
            this.roadTailLon = double.Parse(item.RoadTailLong);
            this.roadTailLat = double.Parse(item.RoadTailLat);
        }
    }
}
