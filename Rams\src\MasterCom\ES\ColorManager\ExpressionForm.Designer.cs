﻿namespace MasterCom.ES.ColorManager
{
    partial class ExpressionForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.rtxtFormular = new System.Windows.Forms.RichTextBox();
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.panel2 = new System.Windows.Forms.Panel();
            this.panel3 = new System.Windows.Forms.Panel();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.es_olv = new BrightIdeasSoftware.ObjectListView();
            this.olvColumn1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn2 = new BrightIdeasSoftware.OLVColumn();
            this.event_olv = new BrightIdeasSoftware.ObjectListView();
            this.olvColumn3 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn4 = new BrightIdeasSoftware.OLVColumn();
            this.panel4 = new System.Windows.Forms.Panel();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.biacketR_B = new System.Windows.Forms.Button();
            this.or_B = new System.Windows.Forms.Button();
            this.check_B = new System.Windows.Forms.Button();
            this.xor_B = new System.Windows.Forms.Button();
            this.bracketL_B = new System.Windows.Forms.Button();
            this.and_B = new System.Windows.Forms.Button();
            this.clean_B = new System.Windows.Forms.Button();
            this.not_B = new System.Windows.Forms.Button();
            this.backspace_B = new System.Windows.Forms.Button();
            this.panel5 = new System.Windows.Forms.Panel();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.tip_tssl = new System.Windows.Forms.ToolStripStatusLabel();
            this.ok_b = new System.Windows.Forms.Button();
            this.cancel_b = new System.Windows.Forms.Button();
            this.panel1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.panel3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.es_olv)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.event_olv)).BeginInit();
            this.panel4.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.panel5.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // rtxtFormular
            // 
            this.rtxtFormular.Dock = System.Windows.Forms.DockStyle.Fill;
            this.rtxtFormular.Location = new System.Drawing.Point(3, 17);
            this.rtxtFormular.Name = "rtxtFormular";
            this.rtxtFormular.Size = new System.Drawing.Size(653, 27);
            this.rtxtFormular.TabIndex = 0;
            this.rtxtFormular.Text = "";
            this.rtxtFormular.TextChanged += new System.EventHandler(this.rtxtFormular_TextChanged);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.groupBox1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(659, 47);
            this.panel1.TabIndex = 1;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rtxtFormular);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(659, 47);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "表达式";
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.panel3);
            this.panel2.Controls.Add(this.panel4);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(0, 47);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(659, 324);
            this.panel2.TabIndex = 2;
            // 
            // panel3
            // 
            this.panel3.Controls.Add(this.groupBox2);
            this.panel3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel3.Location = new System.Drawing.Point(0, 0);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(542, 324);
            this.panel3.TabIndex = 0;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.splitContainer1);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox2.Location = new System.Drawing.Point(0, 0);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(542, 324);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "所有指标";
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(3, 17);
            this.splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.es_olv);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.event_olv);
            this.splitContainer1.Size = new System.Drawing.Size(536, 304);
            this.splitContainer1.SplitterDistance = 252;
            this.splitContainer1.TabIndex = 0;
            // 
            // es_olv
            // 
            this.es_olv.AllColumns.Add(this.olvColumn1);
            this.es_olv.AllColumns.Add(this.olvColumn2);
            this.es_olv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn1,
            this.olvColumn2});
            this.es_olv.Cursor = System.Windows.Forms.Cursors.Default;
            this.es_olv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.es_olv.FullRowSelect = true;
            this.es_olv.IsNeedShowOverlay = false;
            this.es_olv.Location = new System.Drawing.Point(0, 0);
            this.es_olv.Name = "es_olv";
            this.es_olv.ShowCommandMenuOnRightClick = true;
            this.es_olv.ShowGroups = false;
            this.es_olv.ShowItemToolTips = true;
            this.es_olv.Size = new System.Drawing.Size(252, 304);
            this.es_olv.TabIndex = 0;
            this.es_olv.UseCompatibleStateImageBehavior = false;
            this.es_olv.View = System.Windows.Forms.View.Details;
            this.es_olv.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.es_olv_MouseDoubleClick);
            // 
            // olvColumn1
            // 
            this.olvColumn1.AspectName = "Name";
            this.olvColumn1.HeaderFont = null;
            this.olvColumn1.Text = "Name";
            this.olvColumn1.Width = 117;
            // 
            // olvColumn2
            // 
            this.olvColumn2.AspectName = "Expression";
            this.olvColumn2.HeaderFont = null;
            this.olvColumn2.Text = "Expression";
            this.olvColumn2.Width = 131;
            // 
            // event_olv
            // 
            this.event_olv.AllColumns.Add(this.olvColumn3);
            this.event_olv.AllColumns.Add(this.olvColumn4);
            this.event_olv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn3,
            this.olvColumn4});
            this.event_olv.Cursor = System.Windows.Forms.Cursors.Default;
            this.event_olv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.event_olv.FullRowSelect = true;
            this.event_olv.IsNeedShowOverlay = false;
            this.event_olv.Location = new System.Drawing.Point(0, 0);
            this.event_olv.Name = "event_olv";
            this.event_olv.ShowCommandMenuOnRightClick = true;
            this.event_olv.ShowGroups = false;
            this.event_olv.ShowItemToolTips = true;
            this.event_olv.Size = new System.Drawing.Size(280, 304);
            this.event_olv.TabIndex = 0;
            this.event_olv.UseCompatibleStateImageBehavior = false;
            this.event_olv.View = System.Windows.Forms.View.Details;
            this.event_olv.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.event_olv_MouseDoubleClick);
            // 
            // olvColumn3
            // 
            this.olvColumn3.AspectName = "Name";
            this.olvColumn3.HeaderFont = null;
            this.olvColumn3.Text = "Name";
            this.olvColumn3.Width = 127;
            // 
            // olvColumn4
            // 
            this.olvColumn4.AspectName = "Expression";
            this.olvColumn4.HeaderFont = null;
            this.olvColumn4.Text = "Expression";
            this.olvColumn4.Width = 145;
            // 
            // panel4
            // 
            this.panel4.Controls.Add(this.groupBox3);
            this.panel4.Dock = System.Windows.Forms.DockStyle.Right;
            this.panel4.Location = new System.Drawing.Point(542, 0);
            this.panel4.Name = "panel4";
            this.panel4.Size = new System.Drawing.Size(117, 324);
            this.panel4.TabIndex = 1;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.biacketR_B);
            this.groupBox3.Controls.Add(this.or_B);
            this.groupBox3.Controls.Add(this.check_B);
            this.groupBox3.Controls.Add(this.xor_B);
            this.groupBox3.Controls.Add(this.bracketL_B);
            this.groupBox3.Controls.Add(this.and_B);
            this.groupBox3.Controls.Add(this.clean_B);
            this.groupBox3.Controls.Add(this.not_B);
            this.groupBox3.Controls.Add(this.backspace_B);
            this.groupBox3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox3.Location = new System.Drawing.Point(0, 0);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(117, 324);
            this.groupBox3.TabIndex = 21;
            this.groupBox3.TabStop = false;
            // 
            // biacketR_B
            // 
            this.biacketR_B.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.biacketR_B.Location = new System.Drawing.Point(60, 75);
            this.biacketR_B.Name = "biacketR_B";
            this.biacketR_B.Size = new System.Drawing.Size(51, 25);
            this.biacketR_B.TabIndex = 37;
            this.biacketR_B.TabStop = false;
            this.biacketR_B.Text = ")";
            this.biacketR_B.UseVisualStyleBackColor = true;
            this.biacketR_B.Click += new System.EventHandler(this.biacketR_B_Click);
            // 
            // or_B
            // 
            this.or_B.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.or_B.Location = new System.Drawing.Point(3, 46);
            this.or_B.Name = "or_B";
            this.or_B.Size = new System.Drawing.Size(51, 23);
            this.or_B.TabIndex = 31;
            this.or_B.TabStop = false;
            this.or_B.Text = "|";
            this.or_B.UseVisualStyleBackColor = true;
            this.or_B.Click += new System.EventHandler(this.or_B_Click);
            // 
            // check_B
            // 
            this.check_B.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.check_B.Enabled = false;
            this.check_B.Location = new System.Drawing.Point(6, 135);
            this.check_B.Name = "check_B";
            this.check_B.Size = new System.Drawing.Size(107, 27);
            this.check_B.TabIndex = 17;
            this.check_B.TabStop = false;
            this.check_B.Text = "检测";
            this.check_B.UseVisualStyleBackColor = true;
            this.check_B.Click += new System.EventHandler(this.check_B_Click);
            // 
            // xor_B
            // 
            this.xor_B.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.xor_B.Location = new System.Drawing.Point(60, 46);
            this.xor_B.Name = "xor_B";
            this.xor_B.Size = new System.Drawing.Size(52, 23);
            this.xor_B.TabIndex = 25;
            this.xor_B.TabStop = false;
            this.xor_B.Text = "^";
            this.xor_B.UseVisualStyleBackColor = true;
            this.xor_B.Click += new System.EventHandler(this.xor_B_Click);
            // 
            // bracketL_B
            // 
            this.bracketL_B.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.bracketL_B.Location = new System.Drawing.Point(3, 75);
            this.bracketL_B.Name = "bracketL_B";
            this.bracketL_B.Size = new System.Drawing.Size(51, 25);
            this.bracketL_B.TabIndex = 36;
            this.bracketL_B.TabStop = false;
            this.bracketL_B.Text = "(";
            this.bracketL_B.UseVisualStyleBackColor = true;
            this.bracketL_B.Click += new System.EventHandler(this.bracketL_B_Click);
            // 
            // and_B
            // 
            this.and_B.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.and_B.Location = new System.Drawing.Point(60, 17);
            this.and_B.Name = "and_B";
            this.and_B.Size = new System.Drawing.Size(52, 23);
            this.and_B.TabIndex = 32;
            this.and_B.TabStop = false;
            this.and_B.Text = "&&";
            this.and_B.UseVisualStyleBackColor = true;
            this.and_B.Click += new System.EventHandler(this.and_B_Click);
            // 
            // clean_B
            // 
            this.clean_B.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.clean_B.Location = new System.Drawing.Point(4, 106);
            this.clean_B.Name = "clean_B";
            this.clean_B.Size = new System.Drawing.Size(50, 23);
            this.clean_B.TabIndex = 33;
            this.clean_B.TabStop = false;
            this.clean_B.Text = "Clear";
            this.clean_B.UseVisualStyleBackColor = true;
            this.clean_B.Click += new System.EventHandler(this.clean_B_Click);
            // 
            // not_B
            // 
            this.not_B.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.not_B.Location = new System.Drawing.Point(3, 17);
            this.not_B.Name = "not_B";
            this.not_B.Size = new System.Drawing.Size(51, 23);
            this.not_B.TabIndex = 35;
            this.not_B.TabStop = false;
            this.not_B.Text = "~";
            this.not_B.UseVisualStyleBackColor = true;
            this.not_B.Click += new System.EventHandler(this.not_B_Click);
            // 
            // backspace_B
            // 
            this.backspace_B.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.backspace_B.Location = new System.Drawing.Point(60, 106);
            this.backspace_B.Name = "backspace_B";
            this.backspace_B.Size = new System.Drawing.Size(52, 23);
            this.backspace_B.TabIndex = 34;
            this.backspace_B.TabStop = false;
            this.backspace_B.Text = "Back";
            this.backspace_B.UseVisualStyleBackColor = true;
            this.backspace_B.Click += new System.EventHandler(this.backspace_B_Click);
            // 
            // panel5
            // 
            this.panel5.Controls.Add(this.statusStrip1);
            this.panel5.Controls.Add(this.ok_b);
            this.panel5.Controls.Add(this.cancel_b);
            this.panel5.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel5.Location = new System.Drawing.Point(0, 371);
            this.panel5.Name = "panel5";
            this.panel5.Size = new System.Drawing.Size(659, 25);
            this.panel5.TabIndex = 38;
            // 
            // statusStrip1
            // 
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabel1,
            this.tip_tssl});
            this.statusStrip1.Location = new System.Drawing.Point(0, 3);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new System.Drawing.Size(509, 22);
            this.statusStrip1.TabIndex = 2;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // toolStripStatusLabel1
            // 
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            this.toolStripStatusLabel1.Size = new System.Drawing.Size(41, 17);
            this.toolStripStatusLabel1.Text = "提示：";
            // 
            // tip_tssl
            // 
            this.tip_tssl.Name = "tip_tssl";
            this.tip_tssl.Size = new System.Drawing.Size(453, 17);
            this.tip_tssl.Spring = true;
            this.tip_tssl.Text = "双击指标列表项在表达式中插入相应指标。";
            this.tip_tssl.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // ok_b
            // 
            this.ok_b.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.ok_b.Dock = System.Windows.Forms.DockStyle.Right;
            this.ok_b.Enabled = false;
            this.ok_b.Location = new System.Drawing.Point(509, 0);
            this.ok_b.Name = "ok_b";
            this.ok_b.Size = new System.Drawing.Size(75, 25);
            this.ok_b.TabIndex = 1;
            this.ok_b.Text = "OK";
            this.ok_b.UseVisualStyleBackColor = true;
            this.ok_b.Click += new System.EventHandler(this.ok_b_Click);
            // 
            // cancel_b
            // 
            this.cancel_b.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.cancel_b.Dock = System.Windows.Forms.DockStyle.Right;
            this.cancel_b.Location = new System.Drawing.Point(584, 0);
            this.cancel_b.Name = "cancel_b";
            this.cancel_b.Size = new System.Drawing.Size(75, 25);
            this.cancel_b.TabIndex = 0;
            this.cancel_b.Text = "Cancel";
            this.cancel_b.UseVisualStyleBackColor = true;
            // 
            // ExpressionForm
            // 
            this.AcceptButton = this.cancel_b;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(659, 396);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.panel5);
            this.MinimumSize = new System.Drawing.Size(600, 400);
            this.Name = "ExpressionForm";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "表达式生成";
            this.panel1.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel3.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.es_olv)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.event_olv)).EndInit();
            this.panel4.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.panel5.ResumeLayout(false);
            this.panel5.PerformLayout();
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.RichTextBox rtxtFormular;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.Panel panel4;
        private BrightIdeasSoftware.ObjectListView es_olv;
        private BrightIdeasSoftware.ObjectListView event_olv;
        private BrightIdeasSoftware.OLVColumn olvColumn1;
        private BrightIdeasSoftware.OLVColumn olvColumn2;
        private BrightIdeasSoftware.OLVColumn olvColumn3;
        private BrightIdeasSoftware.OLVColumn olvColumn4;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Button biacketR_B;
        private System.Windows.Forms.Button or_B;
        private System.Windows.Forms.Button check_B;
        private System.Windows.Forms.Button xor_B;
        private System.Windows.Forms.Button bracketL_B;
        private System.Windows.Forms.Button and_B;
        private System.Windows.Forms.Button clean_B;
        private System.Windows.Forms.Button not_B;
        private System.Windows.Forms.Button backspace_B;
        private System.Windows.Forms.Panel panel5;
        private System.Windows.Forms.Button ok_b;
        private System.Windows.Forms.Button cancel_b;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel1;
        private System.Windows.Forms.ToolStripStatusLabel tip_tssl;
    }
}