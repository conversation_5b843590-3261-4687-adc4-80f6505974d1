﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HandoverCountStatResultForm : MinCloseForm
    {
        public HandoverCountStatResultForm(MainModel mm) : base(mm)
        {
            InitializeComponent();
            DisposeWhenClose = true;
            tsMenuExportExcel.Click += tsMenuExportExcel_Click;
            objectListView.DoubleClick += ListView_DoubleClick;
        }

        public void FillData(IEnumerable data)
        {
            objectListView.SetObjects(data);
        }

        private void tsMenuExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(objectListView);
        }

        private void ListView_DoubleClick(object sender, EventArgs e)
        {
            HandoverCountInfo info = objectListView.SelectedObject as HandoverCountInfo;
            if (info == null)
            {
                return;
            }

            MainModel.ClearSelectedEvents();
            MainModel.SelectedEvents.Clear();
            info.Evt.Selected = true;
            MainModel.SelectedEvents.Add(info.Evt);
            MainModel.MainForm.GetMapForm().GoToView(info.Evt.Longitude, info.Evt.Latitude);
        }
    }
}
