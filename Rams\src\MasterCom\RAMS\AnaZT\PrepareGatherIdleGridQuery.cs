﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.AnaZT;
using MasterCom.MTGis;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Net
{
    public class PrepareGatherIdleGridQuery : DIYSampleQuery
    {
        private readonly IdleCellsInGrid[,] idleMatrix;
        private readonly DbRect bounds;
        public PrepareGatherIdleGridQuery(MainModel mainModel,IdleCellsInGrid[,] matrix,DbRect bounds)
            : base(mainModel)
        {
            this.idleMatrix = matrix;
            this.bounds = bounds;
        }
        public override string Name
        {
            get { return "准备栅格化Idle数据"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 1300, 1301, this.Name);
        }
        protected override void prepareStatPackage_Sample_FileFilter(Package package, TimePeriod period, bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (!isPreSetted)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_SAMPLE;
            }
            else
            {
                package.Content.Type = this.preSettedCommandType;
            }
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYRegion_Intersect(package);
            AddDIYProject(package, condition.Projects);
            condition.ServiceTypes.Clear();
            condition.ServiceTypes.Add(22);//IDLE测试
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);

            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            //
            AddDIYEndOpFlag(package);

        }
        protected override void prepareStatPackage_Sample_SampleFilter(Package package, TimePeriod period)
        {
            AddDIYRegion_Sample(package);
            AddDIYEndOpFlag(package);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                if (inRegion && tp is TestPointDetail)
                {
                    IdleCellsInGrid cu = null;
                    int rAt, cAt;
                    getInPlace(tp.Longitude, tp.Latitude, bounds, out rAt, out cAt);
                    if (rAt >= 0 && cAt >= 0 && rAt < idleMatrix.GetLength(0) && cAt < idleMatrix.GetLength(1))
                    {
                        cu = idleMatrix[rAt, cAt];
                        if (cu == null)
                        {
                            cu = new IdleCellsInGrid();
                            idleMatrix[rAt, cAt] = cu;
                            cu.ltLong = bounds.x1 + cAt * IdleLeakNeighboursQuery.GRID_SPAN_LONG;
                            cu.brLong = cu.ltLong + IdleLeakNeighboursQuery.GRID_SPAN_LONG;
                            cu.ltLat = bounds.y2 - rAt * IdleLeakNeighboursQuery.GRID_SPAN_LAT;
                            cu.brLat = cu.ltLat - IdleLeakNeighboursQuery.GRID_SPAN_LAT;
                        }
                        return dealTpCell(tp, cu);
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool dealTpCell(TestPoint tp, IdleCellsInGrid cu)
        {
            Cell servcell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)tp["LAC"], (ushort?)(int?)tp["CI"], (short?)tp["BCCH"], (byte?)tp["BSIC"], tp.Longitude, tp.Latitude);
            if (servcell != null)//主服务小区
            {
                short? rxlevSub = (short?)tp["RxLevSub"];
                if (rxlevSub == null || rxlevSub < curFilterRxlev)
                {
                    return false;
                }
                cu.GatherCell(servcell.Name, (short)rxlevSub);
                //
                return dealTpNbCell(tp, cu, servcell);
            }
            return false;
        }

        private static bool dealTpNbCell(TestPoint tp, IdleCellsInGrid cu, Cell servcell)
        {
            for (int i = 0; i < 16; i++)
            {
                short? bcch = (short?)tp["N_BCCH", i];
                byte? bsic = (byte?)tp["N_BSIC", i];
                if (bcch == null && bsic == null)
                {
                    return false;
                }
                else if (bcch != null && bsic != null)
                {
                    Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, bcch, bsic, servcell.Longitude, servcell.Latitude, (ushort?)(int?)tp["LAC"], (ushort?)(int?)tp["CI"], (short?)tp["BCCH"], (byte?)tp["BSIC"]);
                    if (cell != null && cell.GetDistance(tp.Longitude, tp.Latitude) < CD.MAX_COV_DISTANCE_GSM)
                    {
                        short? tm_rxlev = (short?)tp["N_RxLev", i];
                        if (tm_rxlev != null)
                        {
                            cu.GatherCell(cell.Name, (short)tm_rxlev);
                        }
                    }
                }
            }
            return false;
        }

        private void getInPlace(double longitude, double latitude, DbRect bounds, out int rAt, out int cAt)
        {
            double xDis = longitude - bounds.x1;
            cAt = (int)(Math.Round(xDis / IdleLeakNeighboursQuery.GRID_SPAN_LONG));
            double yDis = bounds.y2 - latitude;
            rAt = (int)(Math.Round(yDis / IdleLeakNeighboursQuery.GRID_SPAN_LAT));
        }
        private readonly int curFilterRxlev = -140;
        protected override void query()
        {
            curSelDIYSampleGroup = makeIdleSampleGroup();

            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private DIYSampleGroup makeIdleSampleGroup()
        {
            DIYSampleGroup group = new DIYSampleGroup();
            group.ColumnsDefSet.Add(new DIYSampleParamDef(DTParameterManager.GetInstance().GetParameter("LAC")));
            group.ColumnsDefSet.Add(new DIYSampleParamDef(DTParameterManager.GetInstance().GetParameter("CI")));
            group.ColumnsDefSet.Add(new DIYSampleParamDef(DTParameterManager.GetInstance().GetParameter("RxLevSub")));
            group.ColumnsDefSet.Add(new DIYSampleParamDef(DTParameterManager.GetInstance().GetParameter("BCCH")));
            group.ColumnsDefSet.Add(new DIYSampleParamDef(DTParameterManager.GetInstance().GetParameter("BSIC")));
            group.ColumnsDefSet.Add(new DIYSampleParamDef(DTParameterManager.GetInstance().GetParameter("N_BCCH")));
            group.ColumnsDefSet.Add(new DIYSampleParamDef(DTParameterManager.GetInstance().GetParameter("N_BSIC")));
            group.ColumnsDefSet.Add(new DIYSampleParamDef(DTParameterManager.GetInstance().GetParameter("N_RxLev")));
            return group;
        }

    }
}
