﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.ES.Core
{
    public class ESModel
    {
        private static ESModel modelInstance = null;
        public static ESModel GetInstance()
        {
            if(modelInstance==null)
            {
                modelInstance = new ESModel();
            }
            return modelInstance;
        }
        public DTDataManager DTDataManager { get; set; } = new DTDataManager(MainModel.GetInstance());
        public DTDataManager DTDataManagerTar { get; set; } = new DTDataManager(MainModel.GetInstance());
    }
}
