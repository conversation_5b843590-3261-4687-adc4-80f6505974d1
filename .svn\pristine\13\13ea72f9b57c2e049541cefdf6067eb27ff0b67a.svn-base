﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRQualPnl : UserControl
    {
        public NRQualPnl()
        {
            InitializeComponent();
        }

        public void LinkCondition(NRLowSpeedCauseCondition cond)
        {
            NRQualCause cr = null;
            foreach (NRLowSpeedCauseBase r in cond.Causes)
            {
                if (r is NRQualCause)
                {
                    cr = r as NRQualCause;
                    break;
                }
            }
            if (cr == null)
            {
                return;
            }
            foreach (NRLowSpeedCauseBase r in cr.SubCauses)
            {
                if (r is NRPoorSINRCause)
                {
                    nrPoorSINRPnl1.LinkCondition(r as NRPoorSINRCause);
                }
            }
        }
    }
}
