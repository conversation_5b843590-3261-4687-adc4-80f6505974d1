﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakQualDetailSettingPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditStru = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditTotal1800 = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditTotal900 = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditRxlevMean = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditStru.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTotal1800.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTotal900.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlevMean.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.groupBox3);
            this.groupBox1.Controls.Add(this.groupBox2);
            this.groupBox1.Location = new System.Drawing.Point(3, 3);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(274, 186);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "质差详细设置";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.labelControl3);
            this.groupBox3.Controls.Add(this.labelControl6);
            this.groupBox3.Controls.Add(this.labelControl5);
            this.groupBox3.Controls.Add(this.spinEditStru);
            this.groupBox3.Controls.Add(this.spinEditTotal1800);
            this.groupBox3.Controls.Add(this.spinEditTotal900);
            this.groupBox3.Location = new System.Drawing.Point(6, 73);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(262, 106);
            this.groupBox3.TabIndex = 1;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "道路结构指数设置";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(32, 80);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(85, 14);
            this.labelControl3.TabIndex = 6;
            this.labelControl3.Text = "道路结构指数 ≥";
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(37, 50);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(80, 14);
            this.labelControl6.TabIndex = 9;
            this.labelControl6.Text = "1800总频点数:";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(44, 20);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(73, 14);
            this.labelControl5.TabIndex = 8;
            this.labelControl5.Text = "900总频点数:";
            // 
            // spinEditStru
            // 
            this.spinEditStru.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            65536});
            this.spinEditStru.Location = new System.Drawing.Point(123, 77);
            this.spinEditStru.Name = "spinEditStru";
            this.spinEditStru.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditStru.Properties.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.spinEditStru.Properties.Mask.EditMask = "f";
            this.spinEditStru.Size = new System.Drawing.Size(100, 21);
            this.spinEditStru.TabIndex = 2;
            // 
            // spinEditTotal1800
            // 
            this.spinEditTotal1800.EditValue = new decimal(new int[] {
            125,
            0,
            0,
            0});
            this.spinEditTotal1800.Location = new System.Drawing.Point(123, 47);
            this.spinEditTotal1800.Name = "spinEditTotal1800";
            this.spinEditTotal1800.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTotal1800.Properties.Mask.EditMask = "f0";
            this.spinEditTotal1800.Size = new System.Drawing.Size(100, 21);
            this.spinEditTotal1800.TabIndex = 5;
            // 
            // spinEditTotal900
            // 
            this.spinEditTotal900.EditValue = new decimal(new int[] {
            95,
            0,
            0,
            0});
            this.spinEditTotal900.Location = new System.Drawing.Point(123, 17);
            this.spinEditTotal900.Name = "spinEditTotal900";
            this.spinEditTotal900.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTotal900.Properties.Mask.EditMask = "f0";
            this.spinEditTotal900.Size = new System.Drawing.Size(100, 21);
            this.spinEditTotal900.TabIndex = 4;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.labelControl1);
            this.groupBox2.Controls.Add(this.labelControl2);
            this.groupBox2.Controls.Add(this.spinEditRxlevMean);
            this.groupBox2.Location = new System.Drawing.Point(6, 20);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(262, 47);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "扫频弱覆盖设置";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(20, 23);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(97, 14);
            this.labelControl1.TabIndex = 3;
            this.labelControl1.Text = "最强与次强均值 <";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(229, 23);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(24, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "dBm";
            // 
            // spinEditRxlevMean
            // 
            this.spinEditRxlevMean.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.spinEditRxlevMean.Location = new System.Drawing.Point(123, 20);
            this.spinEditRxlevMean.Name = "spinEditRxlevMean";
            this.spinEditRxlevMean.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRxlevMean.Properties.Mask.EditMask = "f0";
            this.spinEditRxlevMean.Size = new System.Drawing.Size(100, 21);
            this.spinEditRxlevMean.TabIndex = 2;
            // 
            // WeakQualDetailSettingPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupBox1);
            this.Name = "WeakQualDetailSettingPanel";
            this.Size = new System.Drawing.Size(284, 196);
            this.groupBox1.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditStru.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTotal1800.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTotal900.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlevMean.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit spinEditRxlevMean;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit spinEditStru;
        private DevExpress.XtraEditors.SpinEdit spinEditTotal1800;
        private DevExpress.XtraEditors.SpinEdit spinEditTotal900;
        private DevExpress.XtraEditors.LabelControl labelControl2;
    }
}
