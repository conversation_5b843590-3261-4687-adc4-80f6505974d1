﻿using System;
using System.Collections.Generic;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsSampleRateResult : NbIotMgrsResultControlBase
    {
        NbIotMgrsFuncItem funcItem = null;
        List<CarrierSampleRate> carrierSampleRate = new List<CarrierSampleRate>();

        public NbIotMgrsSampleRateResult()
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            miExportAllExcel.Click += base.MiExportExcelAll_Click;
            miExportAllShp.Click += base.MiExportShpAll_Click;
        }

        public override string Desc
        {
            get { return "频点占比"; }
        }

        public void FillData(NbIotMgrsFuncItem curFuncItem)
        {
            funcItem = curFuncItem;
            RefreshResult();
        }

        private void RefreshResult()
        {
            NbIotMgrsSampleRateStater stater = this.funcItem.Stater as NbIotMgrsSampleRateStater;
            carrierSampleRate = stater.GetViews();
            gridControl1.DataSource = carrierSampleRate;
            gridControl1.RefreshDataSource();
        }

        protected override void ExportAllExcel(string savePath)
        {
            string sheetName = "频点占比";
            string fileName = System.IO.Path.Combine(savePath, sheetName + ".xlsx");
            List<NPOIRow> rowList = getNPOIRow();
            ExcelNPOIManager.ExportToExcel(rowList, fileName, sheetName);
        }


        protected virtual List<NPOIRow> getNPOIRow()
        {
            List<NPOIRow> rowList = new List<NPOIRow>(carrierSampleRate.Count + 1);
            //row为标题
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("运营商名称");
            titleRow.AddCellValue("总频点数");

            titleRow.AddCellValue("区域名称");
            titleRow.AddCellValue("区域频点数");

            titleRow.AddCellValue("频点");
            titleRow.AddCellValue("栅格数");
            titleRow.AddCellValue("栅格总数");
            titleRow.AddCellValue("栅格占比");

            rowList.Add(titleRow);

            int index = 1;
            foreach (CarrierSampleRate item in carrierSampleRate)
            {
                NPOIRow row = new NPOIRow();
                fillRow(ref row, item, index);
                rowList.Add(row);
            }
            return rowList;
        }

        protected virtual void fillRow(ref NPOIRow row, CarrierSampleRate item, int index)
        {
            if (row == null || item == null)
                return;
            //添加一级数据
            row.AddCellValue(item.Name);
            row.AddCellValue(item.TotalEarfcnCount);
            foreach (var area in item.AreaRateViews)
            {
                //添加二级数据
                NPOIRow subRow = new NPOIRow();
                subRow.AddCellValue(area.Name);
                subRow.AddCellValue(area.EarfcnCount);
                foreach (var serialGrid in area.SampleRateViews)
                {
                    //添加二级数据
                    NPOIRow thirdRow = new NPOIRow();
                    thirdRow.AddCellValue(serialGrid.Earfcn);
                    thirdRow.AddCellValue(serialGrid.GridCount);
                    thirdRow.AddCellValue(serialGrid.TotalGridCount);
                    thirdRow.AddCellValue(serialGrid.GridRate);

                    subRow.AddSubRow(thirdRow);
                }
                row.AddSubRow(subRow);
            }
           
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }
    }
}
