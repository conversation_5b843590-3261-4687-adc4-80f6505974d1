﻿using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTRegionGridFilter
{
    public class RadiusGridDataInfo
    {
        public RadiusGridDataInfo()
        {
        }

        protected GridMatrix<GridDataUnit> gridMatrix = new GridMatrix<GridDataUnit>();

        private List<RadiusGridItem> totalGrids = new List<RadiusGridItem>();
        private string cellName = "";

        public int GridNum
        {
            get { return totalGrids.Count; }
        }

        public List<RadiusGridItem> Grids
        {
            get { return totalGrids; }
        }

        public void AddGridData(KPIStatDataBase data, string cellName)
        {
            this.cellName = cellName;
            GridDataUnit grid = new GridDataUnit(data.LTLng, data.LTLat);
            grid.AddStatData(data);
            int rAt, cAt;
            GridHelper.GetIndexOfDefaultSizeGrid(grid.CenterLng, grid.CenterLat, out rAt, out cAt);
            GridDataUnit old = gridMatrix[rAt, cAt];
            if (old == null)
            {
                gridMatrix[rAt, cAt] = grid;
            }
            else
            {
                old.Gather(grid);
            }
        }

        internal void MakeSummary()
        {
            totalGrids = new List<RadiusGridItem>();
            foreach (GridDataUnit grid in gridMatrix)
            {
                double num = grid.DataHub.CalcValueByFormula("{Mx_640101}");
                if((int)num == 0)
                {
                    //场强为0的栅格没有采样点，跳过
                    continue;
                }
                double coverage90 = grid.DataHub.CalcValueByFormula("{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108)/Mx_640101}");
                double coverage94 = (int)grid.DataHub.CalcValueByFormula("{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108+Mx_640107)/Mx_640101}");

                RadiusGridItem item = new RadiusGridItem(grid);
                item.CellName = this.cellName;
                item.TestPointCount = (int)num;
                item.Coverage90 = (float)coverage90;
                item.Coverage94 = (float)coverage94;
                totalGrids.Add(item);
            }
        }
    }

    public class RadiusGridItem : GridUnitBase
    {
        public string CellName { get; set; } = "";
        public int TestPointCount { get; set; }
        public float Coverage90 { get; set; }
        public float Coverage94 { get; set; }
        private GridDataUnit grid { get; set; }

        public RadiusGridItem(GridDataUnit grid)
        {
            this.grid = grid;
            this.LTLng = Math.Round(grid.LTLng, 4);
            this.LTLat = Math.Round(grid.LTLat, 5);
        }
    }
}
