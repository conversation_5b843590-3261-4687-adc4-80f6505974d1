﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsSampleRateStater : LteMgrsStaterBase
    {
        private List<LteMgrsSampleRateView> resultViewList = null;

        public override void DoStat(LteMgrsFuncItem curFuncItem)
        {
            resultViewList = new List<LteMgrsSampleRateView>();
            decimal rsrpMin = (decimal)(double)curFuncItem.FuncCondtion;

            LteMgrsCity city = curFuncItem.CurQueryCitys[curFuncItem.SelectedCityIndex];
            foreach (LteMgrsRegion region in city.RegionDic.Values)
            {
                List<LteMgrsTestPoint> regionSampleList = region.SampleList;
                int totalSampleCnt = 0;
                Dictionary<string, int> totalGridDic = new Dictionary<string, int>();
                Dictionary<int, LteMgrsSampleRateView> freqView = new Dictionary<int, LteMgrsSampleRateView>();

                foreach (LteMgrsTestPoint tp in regionSampleList)
                {
                    if (tp.RsrpList.Count < 1 || tp.RsrpList[0] <= rsrpMin)//this.rsrpMin)
                    {
                        continue;
                    }

                    string mgrsString = tp.MgrsString;
                    int freq = tp.EarfcnList[0];

                    ++totalSampleCnt;
                    if (!totalGridDic.ContainsKey(mgrsString))
                    {
                        totalGridDic.Add(mgrsString, 0);
                    }
                    ++totalGridDic[mgrsString];

                    if (!freqView.ContainsKey(freq))
                    {
                        freqView.Add(freq, new LteMgrsSampleRateView(city.CityName, region.RegionName, freq));
                    }
                    freqView[freq].AddTestPoint(mgrsString);
                }

                foreach (LteMgrsSampleRateView view in freqView.Values)
                {
                    view.TotalSampleCount = totalSampleCnt;
                    view.TotalGridCount += totalGridDic.Count;
                }

                resultViewList.AddRange(freqView.Values);
            }
        }

        public override List<LteMgrsResultControlBase> GetResult()
        {
            LteMgrsSampleRateResult resultControl = new LteMgrsSampleRateResult();
            resultControl.FillData(new List<LteMgrsSampleRateView>(resultViewList));
            return new List<LteMgrsResultControlBase>() { resultControl };
        }

        public override void Clear()
        {
            if (resultViewList != null)
            {
                resultViewList.Clear();
                resultViewList = null;
            }
        }
    }

    public class LteMgrsSampleRateView
    {
        public LteMgrsSampleRateView(string cityName, string regionName, int earfcn)
        {
            CityName = cityName;
            RegionName = regionName;
            Earfcn = earfcn;
            Key = MakeKey(cityName, regionName, earfcn);
        }

        public string CityName
        {
            get;
            private set;
        }

        public string RegionName
        {
            get;
            private set;
        }

        public int Earfcn
        {
            get;
            private set;
        }

        public string Key
        {
            get;
            private set;
        }

        // 网格区域内第一强符合条件阀值，且第一强频点为当前频点的采样点个数
        public int FreqSampleCount
        {
            get;
            private set;
        }

        // 网格区域内第一强符合条件阀值，且第一强频点为当前频点的采样点转换成的栅格总数
        public int FreqGridCount
        {
            get { return gridDic.Count; }
        }

        public double SampleRate
        {
            get { return TotalSampleCount == 0 ? 0 : 1d * FreqSampleCount / TotalSampleCount; }
        }

        public double GridRate
        {
            get { return TotalGridCount == 0 ? 0 : 1d * FreqGridCount / TotalGridCount; }
        }

        // 网格区域内第一强符合条件阀值的采样点总数
        public int TotalSampleCount
        {
            get;
            set;
        }

        // 网格区域内第一强符合条件阀值的采样点转换成的栅格总数
        public int TotalGridCount
        {
            get;
            set;
        }

        public void AddTestPoint(string mgrsString)
        {
            FreqSampleCount += 1;
            if (!gridDic.ContainsKey(mgrsString))
            {
                gridDic.Add(mgrsString, 0);
            }
            ++gridDic[mgrsString];
        }

        private readonly Dictionary<string, int> gridDic = new Dictionary<string, int>();

        public static string MakeKey(string cityName, string regionName, int earfcn)
        {
            return string.Format("{0}:{1}:{2}", cityName, regionName, earfcn);
        }
    }
}
