﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTTestTaskListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode2 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject2 = new DevExpress.Utils.SerializableAppearanceObject();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CQTTestTaskListForm));
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcSubPointName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcSubPointTestedTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayFiles = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcTaskName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcCQTPointName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcCareerName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcServiceType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcAgent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcTarget = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcTestedTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcBeginTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcEndTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcRestDays = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcTaskComment = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcComplianceRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemProgressBar1 = new DevExpress.XtraEditors.Repository.RepositoryItemProgressBar();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcFileDuration = new DevExpress.XtraGrid.Columns.GridColumn();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.cbbxAgent = new DevExpress.XtraEditors.ComboBoxEdit();
            this.popupContainerEditPoint = new DevExpress.XtraEditors.PopupContainerEdit();
            this.popupContainerControl = new DevExpress.XtraEditors.PopupContainerControl();
            this.checkedListBoxPoints = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.checkAllPoints = new DevExpress.XtraEditors.CheckEdit();
            this.buttonEditSearch = new DevExpress.XtraEditors.ButtonEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.dateEditBeginTime = new DevExpress.XtraEditors.DateEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.dateEditEndTime = new DevExpress.XtraEditors.DateEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.textEditTaskName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.textEditComment = new DevExpress.XtraEditors.TextEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.errorProvider = new System.Windows.Forms.ErrorProvider(this.components);
            this.btnNew = new DevExpress.XtraEditors.SimpleButton();
            this.groupControlNewTask = new DevExpress.XtraEditors.GroupControl();
            this.timeSpan = new DevExpress.XtraEditors.TimeEdit();
            this.cbbxCareerID = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbbxServiceType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.btnDeleteTask = new DevExpress.XtraEditors.SimpleButton();
            this.btnQueryTaskResult = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemProgressBar1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbbxAgent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerEditPoint.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerControl)).BeginInit();
            this.popupContainerControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxPoints)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkAllPoints.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.buttonEditSearch.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditBeginTime.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditBeginTime.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditEndTime.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditEndTime.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEditTaskName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEditComment.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.errorProvider)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlNewTask)).BeginInit();
            this.groupControlNewTask.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.timeSpan.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbbxCareerID.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbbxServiceType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            this.SuspendLayout();
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcSubPointName,
            this.gcSubPointTestedTime});
            this.gridView1.GridControl = this.gridControl;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsDetail.ShowDetailTabs = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gcSubPointName
            // 
            this.gcSubPointName.Caption = "楼层名称";
            this.gcSubPointName.FieldName = "Name";
            this.gcSubPointName.Name = "gcSubPointName";
            this.gcSubPointName.Visible = true;
            this.gcSubPointName.VisibleIndex = 0;
            // 
            // gcSubPointTestedTime
            // 
            this.gcSubPointTestedTime.Caption = "测试时长";
            this.gcSubPointTestedTime.FieldName = "TestTimeSpan";
            this.gcSubPointTestedTime.Name = "gcSubPointTestedTime";
            this.gcSubPointTestedTime.Visible = true;
            this.gcSubPointTestedTime.VisibleIndex = 1;
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gridView1;
            gridLevelNode2.LevelTemplate = this.gridView2;
            gridLevelNode2.RelationName = "FileInfoList";
            gridLevelNode1.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode2});
            gridLevelNode1.RelationName = "SubPointsTestInfo";
            this.gridControl.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControl.Location = new System.Drawing.Point(2, 23);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemProgressBar1});
            this.gridControl.Size = new System.Drawing.Size(1173, 461);
            this.gridControl.TabIndex = 0;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView,
            this.gridView2,
            this.gridView1});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayFiles});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(123, 26);
            // 
            // miReplayFiles
            // 
            this.miReplayFiles.Name = "miReplayFiles";
            this.miReplayFiles.Size = new System.Drawing.Size(122, 22);
            this.miReplayFiles.Text = "回放文件";
            this.miReplayFiles.Click += new System.EventHandler(this.miReplayFiles_Click);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcTaskName,
            this.gcCQTPointName,
            this.gcCareerName,
            this.gcServiceType,
            this.gcAgent,
            this.gcTarget,
            this.gcTestedTime,
            this.gcBeginTime,
            this.gcEndTime,
            this.gcRestDays,
            this.gcTaskComment,
            this.gcComplianceRate});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView.OptionsBehavior.AutoPopulateColumns = false;
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsDetail.ShowDetailTabs = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.RowCellStyle += new DevExpress.XtraGrid.Views.Grid.RowCellStyleEventHandler(this.gridView_RowCellStyle);
            // 
            // gcTaskName
            // 
            this.gcTaskName.Caption = "任务名称";
            this.gcTaskName.FieldName = "Name";
            this.gcTaskName.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gcTaskName.Name = "gcTaskName";
            this.gcTaskName.Visible = true;
            this.gcTaskName.VisibleIndex = 0;
            this.gcTaskName.Width = 167;
            // 
            // gcCQTPointName
            // 
            this.gcCQTPointName.Caption = "测试地点";
            this.gcCQTPointName.FieldName = "CQTPoint";
            this.gcCQTPointName.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gcCQTPointName.Name = "gcCQTPointName";
            this.gcCQTPointName.Visible = true;
            this.gcCQTPointName.VisibleIndex = 1;
            this.gcCQTPointName.Width = 119;
            // 
            // gcCareerName
            // 
            this.gcCareerName.Caption = "运营商";
            this.gcCareerName.FieldName = "CareerName";
            this.gcCareerName.Name = "gcCareerName";
            this.gcCareerName.Visible = true;
            this.gcCareerName.VisibleIndex = 2;
            this.gcCareerName.Width = 58;
            // 
            // gcServiceType
            // 
            this.gcServiceType.Caption = "业务类型";
            this.gcServiceType.FieldName = "ServiceName";
            this.gcServiceType.Name = "gcServiceType";
            this.gcServiceType.Visible = true;
            this.gcServiceType.VisibleIndex = 3;
            this.gcServiceType.Width = 93;
            // 
            // gcAgent
            // 
            this.gcAgent.Caption = "承担商";
            this.gcAgent.FieldName = "Agent";
            this.gcAgent.Name = "gcAgent";
            this.gcAgent.Visible = true;
            this.gcAgent.VisibleIndex = 4;
            this.gcAgent.Width = 80;
            // 
            // gcTarget
            // 
            this.gcTarget.Caption = "任务测试时长下限(含等于)";
            this.gcTarget.DisplayFormat.FormatString = "HH:mm:ss";
            this.gcTarget.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;
            this.gcTarget.FieldName = "Target";
            this.gcTarget.Name = "gcTarget";
            this.gcTarget.Visible = true;
            this.gcTarget.VisibleIndex = 8;
            this.gcTarget.Width = 158;
            // 
            // gcTestedTime
            // 
            this.gcTestedTime.Caption = "已测试时长";
            this.gcTestedTime.FieldName = "TestedTimeSpan";
            this.gcTestedTime.Name = "gcTestedTime";
            this.gcTestedTime.Visible = true;
            this.gcTestedTime.VisibleIndex = 9;
            // 
            // gcBeginTime
            // 
            this.gcBeginTime.Caption = "开始时间";
            this.gcBeginTime.FieldName = "BeginTime";
            this.gcBeginTime.Name = "gcBeginTime";
            this.gcBeginTime.Visible = true;
            this.gcBeginTime.VisibleIndex = 5;
            // 
            // gcEndTime
            // 
            this.gcEndTime.Caption = "结束时间";
            this.gcEndTime.FieldName = "EndTime";
            this.gcEndTime.Name = "gcEndTime";
            this.gcEndTime.Visible = true;
            this.gcEndTime.VisibleIndex = 6;
            // 
            // gcRestDays
            // 
            this.gcRestDays.Caption = "剩余天数";
            this.gcRestDays.FieldName = "RestDays";
            this.gcRestDays.Name = "gcRestDays";
            this.gcRestDays.Visible = true;
            this.gcRestDays.VisibleIndex = 7;
            this.gcRestDays.Width = 65;
            // 
            // gcTaskComment
            // 
            this.gcTaskComment.Caption = "任务描述";
            this.gcTaskComment.FieldName = "Comment";
            this.gcTaskComment.Name = "gcTaskComment";
            this.gcTaskComment.Visible = true;
            this.gcTaskComment.VisibleIndex = 11;
            this.gcTaskComment.Width = 89;
            // 
            // gcComplianceRate
            // 
            this.gcComplianceRate.Caption = "测试完成率";
            this.gcComplianceRate.ColumnEdit = this.repositoryItemProgressBar1;
            this.gcComplianceRate.FieldName = "ComplianceRate";
            this.gcComplianceRate.Name = "gcComplianceRate";
            this.gcComplianceRate.Visible = true;
            this.gcComplianceRate.VisibleIndex = 10;
            this.gcComplianceRate.Width = 87;
            // 
            // repositoryItemProgressBar1
            // 
            this.repositoryItemProgressBar1.Appearance.BackColor = System.Drawing.Color.Red;
            this.repositoryItemProgressBar1.Appearance.BackColor2 = System.Drawing.Color.Red;
            this.repositoryItemProgressBar1.Appearance.ForeColor = System.Drawing.Color.Black;
            this.repositoryItemProgressBar1.Appearance.ForeColor2 = System.Drawing.Color.Black;
            this.repositoryItemProgressBar1.AppearanceFocused.BackColor = System.Drawing.Color.Red;
            this.repositoryItemProgressBar1.AppearanceFocused.BackColor2 = System.Drawing.Color.Red;
            this.repositoryItemProgressBar1.AppearanceFocused.ForeColor = System.Drawing.Color.Black;
            this.repositoryItemProgressBar1.AppearanceFocused.ForeColor2 = System.Drawing.Color.Black;
            this.repositoryItemProgressBar1.EndColor = System.Drawing.Color.Lime;
            this.repositoryItemProgressBar1.LookAndFeel.SkinName = "DevExpress Style";
            this.repositoryItemProgressBar1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat;
            this.repositoryItemProgressBar1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.repositoryItemProgressBar1.Name = "repositoryItemProgressBar1";
            this.repositoryItemProgressBar1.ProgressViewStyle = DevExpress.XtraEditors.Controls.ProgressViewStyle.Solid;
            this.repositoryItemProgressBar1.ShowTitle = true;
            this.repositoryItemProgressBar1.StartColor = System.Drawing.Color.Lime;
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcFileName,
            this.gcFileDuration});
            this.gridView2.GridControl = this.gridControl;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsDetail.ShowDetailTabs = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gcFileName
            // 
            this.gcFileName.Caption = "文件名";
            this.gcFileName.FieldName = "Name";
            this.gcFileName.Name = "gcFileName";
            this.gcFileName.Visible = true;
            this.gcFileName.VisibleIndex = 0;
            // 
            // gcFileDuration
            // 
            this.gcFileDuration.Caption = "测试时长";
            this.gcFileDuration.FieldName = "Duration";
            this.gcFileDuration.Name = "gcFileDuration";
            this.gcFileDuration.Visible = true;
            this.gcFileDuration.VisibleIndex = 1;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(232, 90);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 14);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "业务类型：";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(244, 63);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(48, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "承担商：";
            // 
            // cbbxAgent
            // 
            this.cbbxAgent.Location = new System.Drawing.Point(298, 60);
            this.cbbxAgent.Name = "cbbxAgent";
            this.cbbxAgent.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbbxAgent.Size = new System.Drawing.Size(128, 21);
            this.cbbxAgent.TabIndex = 5;
            // 
            // popupContainerEditPoint
            // 
            this.popupContainerEditPoint.Location = new System.Drawing.Point(79, 60);
            this.popupContainerEditPoint.Name = "popupContainerEditPoint";
            this.popupContainerEditPoint.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.popupContainerEditPoint.Properties.PopupControl = this.popupContainerControl;
            this.popupContainerEditPoint.Size = new System.Drawing.Size(128, 21);
            this.popupContainerEditPoint.TabIndex = 1;
            this.popupContainerEditPoint.QueryResultValue += new DevExpress.XtraEditors.Controls.QueryResultValueEventHandler(this.popupContainerEditPoint_QueryResultValue);
            // 
            // popupContainerControl
            // 
            this.popupContainerControl.Controls.Add(this.checkedListBoxPoints);
            this.popupContainerControl.Controls.Add(this.panelControl1);
            this.popupContainerControl.Location = new System.Drawing.Point(305, 127);
            this.popupContainerControl.MinimumSize = new System.Drawing.Size(200, 300);
            this.popupContainerControl.Name = "popupContainerControl";
            this.popupContainerControl.Size = new System.Drawing.Size(339, 300);
            this.popupContainerControl.TabIndex = 3;
            // 
            // checkedListBoxPoints
            // 
            this.checkedListBoxPoints.Dock = System.Windows.Forms.DockStyle.Fill;
            this.checkedListBoxPoints.Location = new System.Drawing.Point(0, 0);
            this.checkedListBoxPoints.Name = "checkedListBoxPoints";
            this.checkedListBoxPoints.Size = new System.Drawing.Size(339, 269);
            this.checkedListBoxPoints.TabIndex = 2;
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.checkAllPoints);
            this.panelControl1.Controls.Add(this.buttonEditSearch);
            this.panelControl1.Controls.Add(this.labelControl4);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelControl1.Location = new System.Drawing.Point(0, 269);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(339, 31);
            this.panelControl1.TabIndex = 1;
            // 
            // checkAllPoints
            // 
            this.checkAllPoints.Location = new System.Drawing.Point(3, 7);
            this.checkAllPoints.Name = "checkAllPoints";
            this.checkAllPoints.Properties.Caption = "全选";
            this.checkAllPoints.Size = new System.Drawing.Size(48, 19);
            this.checkAllPoints.TabIndex = 3;
            this.checkAllPoints.CheckedChanged += new System.EventHandler(this.checkAllPoints_CheckedChanged);
            // 
            // buttonEditSearch
            // 
            this.buttonEditSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonEditSearch.EditValue = "";
            this.buttonEditSearch.Location = new System.Drawing.Point(99, 2);
            this.buttonEditSearch.Name = "buttonEditSearch";
            this.buttonEditSearch.Properties.AutoHeight = false;
            this.buttonEditSearch.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Up, "Prev", -1, true, true, true, DevExpress.XtraEditors.ImageLocation.MiddleCenter, null, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, "", null, null, true),
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Down, "Next", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, null, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject2, "", null, null, true)});
            this.buttonEditSearch.Properties.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.buttonEditSearch_Properties_ButtonClick);
            this.buttonEditSearch.Size = new System.Drawing.Size(238, 27);
            this.buttonEditSearch.TabIndex = 2;
            this.buttonEditSearch.KeyDown += new System.Windows.Forms.KeyEventHandler(this.buttonEditSearch_KeyDown);
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(57, 8);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(36, 14);
            this.labelControl4.TabIndex = 1;
            this.labelControl4.Text = "查找：";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(14, 63);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(59, 14);
            this.labelControl3.TabIndex = 1;
            this.labelControl3.Text = "CQT地点：";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(452, 63);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(60, 14);
            this.labelControl5.TabIndex = 1;
            this.labelControl5.Text = "开始时间：";
            // 
            // dateEditBeginTime
            // 
            this.dateEditBeginTime.EditValue = null;
            this.dateEditBeginTime.Location = new System.Drawing.Point(518, 60);
            this.dateEditBeginTime.Name = "dateEditBeginTime";
            this.dateEditBeginTime.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dateEditBeginTime.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dateEditBeginTime.Size = new System.Drawing.Size(126, 21);
            this.dateEditBeginTime.TabIndex = 3;
            this.dateEditBeginTime.EditValueChanged += new System.EventHandler(this.dateEditBeginTime_EditValueChanged);
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(667, 63);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(60, 14);
            this.labelControl6.TabIndex = 1;
            this.labelControl6.Text = "结束时间：";
            // 
            // dateEditEndTime
            // 
            this.dateEditEndTime.EditValue = null;
            this.dateEditEndTime.Location = new System.Drawing.Point(733, 60);
            this.dateEditEndTime.Name = "dateEditEndTime";
            this.dateEditEndTime.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dateEditEndTime.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dateEditEndTime.Size = new System.Drawing.Size(126, 21);
            this.dateEditEndTime.TabIndex = 4;
            this.dateEditEndTime.EditValueChanged += new System.EventHandler(this.dateEditEndTime_EditValueChanged);
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(14, 36);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(60, 14);
            this.labelControl7.TabIndex = 1;
            this.labelControl7.Text = "任务名称：";
            // 
            // textEditTaskName
            // 
            this.textEditTaskName.Location = new System.Drawing.Point(79, 33);
            this.textEditTaskName.Name = "textEditTaskName";
            this.textEditTaskName.Size = new System.Drawing.Size(347, 21);
            this.textEditTaskName.TabIndex = 5;
            // 
            // labelControl8
            // 
            this.labelControl8.Location = new System.Drawing.Point(452, 36);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(60, 14);
            this.labelControl8.TabIndex = 1;
            this.labelControl8.Text = "任务描述：";
            // 
            // textEditComment
            // 
            this.textEditComment.Location = new System.Drawing.Point(518, 33);
            this.textEditComment.Name = "textEditComment";
            this.textEditComment.Size = new System.Drawing.Size(340, 21);
            this.textEditComment.TabIndex = 0;
            // 
            // labelControl9
            // 
            this.labelControl9.Location = new System.Drawing.Point(446, 90);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(66, 14);
            this.labelControl9.TabIndex = 1;
            this.labelControl9.Text = "测试时长>=";
            // 
            // errorProvider
            // 
            this.errorProvider.ContainerControl = this;
            // 
            // btnNew
            // 
            this.btnNew.Location = new System.Drawing.Point(784, 87);
            this.btnNew.Name = "btnNew";
            this.btnNew.Size = new System.Drawing.Size(75, 23);
            this.btnNew.TabIndex = 7;
            this.btnNew.Text = "创建任务";
            this.btnNew.Click += new System.EventHandler(this.btnNew_Click);
            // 
            // groupControlNewTask
            // 
            this.groupControlNewTask.Controls.Add(this.timeSpan);
            this.groupControlNewTask.Controls.Add(this.btnNew);
            this.groupControlNewTask.Controls.Add(this.labelControl7);
            this.groupControlNewTask.Controls.Add(this.labelControl5);
            this.groupControlNewTask.Controls.Add(this.labelControl6);
            this.groupControlNewTask.Controls.Add(this.labelControl2);
            this.groupControlNewTask.Controls.Add(this.cbbxCareerID);
            this.groupControlNewTask.Controls.Add(this.cbbxServiceType);
            this.groupControlNewTask.Controls.Add(this.labelControl9);
            this.groupControlNewTask.Controls.Add(this.textEditComment);
            this.groupControlNewTask.Controls.Add(this.popupContainerEditPoint);
            this.groupControlNewTask.Controls.Add(this.cbbxAgent);
            this.groupControlNewTask.Controls.Add(this.labelControl8);
            this.groupControlNewTask.Controls.Add(this.textEditTaskName);
            this.groupControlNewTask.Controls.Add(this.dateEditBeginTime);
            this.groupControlNewTask.Controls.Add(this.labelControl11);
            this.groupControlNewTask.Controls.Add(this.labelControl1);
            this.groupControlNewTask.Controls.Add(this.labelControl3);
            this.groupControlNewTask.Controls.Add(this.dateEditEndTime);
            this.groupControlNewTask.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControlNewTask.Location = new System.Drawing.Point(0, 0);
            this.groupControlNewTask.Name = "groupControlNewTask";
            this.groupControlNewTask.Size = new System.Drawing.Size(1177, 120);
            this.groupControlNewTask.TabIndex = 8;
            this.groupControlNewTask.Text = "创建测试任务";
            // 
            // timeSpan
            // 
            this.timeSpan.EditValue = new System.DateTime(2013, 6, 13, 0, 0, 0, 0);
            this.timeSpan.Location = new System.Drawing.Point(518, 87);
            this.timeSpan.Name = "timeSpan";
            this.timeSpan.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.timeSpan.Properties.DisplayFormat.FormatString = "HH:mm:ss";
            this.timeSpan.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;
            this.timeSpan.Properties.EditFormat.FormatString = "\"HH:mm:ss\"";
            this.timeSpan.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Custom;
            this.timeSpan.Size = new System.Drawing.Size(126, 21);
            this.timeSpan.TabIndex = 8;
            // 
            // cbbxCareerID
            // 
            this.cbbxCareerID.Location = new System.Drawing.Point(79, 87);
            this.cbbxCareerID.Name = "cbbxCareerID";
            this.cbbxCareerID.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbbxCareerID.Size = new System.Drawing.Size(128, 21);
            this.cbbxCareerID.TabIndex = 2;
            this.cbbxCareerID.SelectedIndexChanged += new System.EventHandler(this.cbbxCareerID_SelectedIndexChanged);
            // 
            // cbbxServiceType
            // 
            this.cbbxServiceType.Location = new System.Drawing.Point(298, 87);
            this.cbbxServiceType.Name = "cbbxServiceType";
            this.cbbxServiceType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbbxServiceType.Size = new System.Drawing.Size(128, 21);
            this.cbbxServiceType.TabIndex = 2;
            // 
            // labelControl11
            // 
            this.labelControl11.Location = new System.Drawing.Point(26, 90);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(48, 14);
            this.labelControl11.TabIndex = 1;
            this.labelControl11.Text = "运营商：";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.btnDeleteTask);
            this.groupControl2.Controls.Add(this.btnQueryTaskResult);
            this.groupControl2.Controls.Add(this.popupContainerControl);
            this.groupControl2.Controls.Add(this.gridControl);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 120);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(1177, 486);
            this.groupControl2.TabIndex = 9;
            this.groupControl2.Text = "现有测试任务";
            // 
            // btnDeleteTask
            // 
            this.btnDeleteTask.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDeleteTask.Location = new System.Drawing.Point(996, 0);
            this.btnDeleteTask.Name = "btnDeleteTask";
            this.btnDeleteTask.Size = new System.Drawing.Size(85, 23);
            this.btnDeleteTask.TabIndex = 4;
            this.btnDeleteTask.Text = "删除任务";
            this.btnDeleteTask.Click += new System.EventHandler(this.btnDeleteTask_Click);
            // 
            // btnQueryTaskResult
            // 
            this.btnQueryTaskResult.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnQueryTaskResult.Location = new System.Drawing.Point(1087, 0);
            this.btnQueryTaskResult.Name = "btnQueryTaskResult";
            this.btnQueryTaskResult.Size = new System.Drawing.Size(85, 23);
            this.btnQueryTaskResult.TabIndex = 4;
            this.btnQueryTaskResult.Text = "查询任务进度";
            this.btnQueryTaskResult.Click += new System.EventHandler(this.btnQueryTaskResult_Click);
            // 
            // CQTTestTaskListForm
            // 
            this.Appearance.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("CQTTestTaskListForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1177, 606);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControlNewTask);
            this.MinimumSize = new System.Drawing.Size(934, 645);
            this.Name = "CQTTestTaskListForm";
            this.Text = "CQT测试任务";
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemProgressBar1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbbxAgent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerEditPoint.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerControl)).EndInit();
            this.popupContainerControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxPoints)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkAllPoints.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.buttonEditSearch.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditBeginTime.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditBeginTime.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditEndTime.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditEndTime.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEditTaskName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEditComment.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.errorProvider)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlNewTask)).EndInit();
            this.groupControlNewTask.ResumeLayout(false);
            this.groupControlNewTask.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.timeSpan.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbbxCareerID.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbbxServiceType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.ComboBoxEdit cbbxAgent;
        private DevExpress.XtraEditors.PopupContainerEdit popupContainerEditPoint;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.PopupContainerControl popupContainerControl;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.ButtonEdit buttonEditSearch;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.DateEdit dateEditBeginTime;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.DateEdit dateEditEndTime;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.TextEdit textEditTaskName;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.TextEdit textEditComment;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private System.Windows.Forms.ErrorProvider errorProvider;
        private DevExpress.XtraEditors.SimpleButton btnNew;
        private DevExpress.XtraEditors.GroupControl groupControlNewTask;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gcTaskName;
        private DevExpress.XtraGrid.Columns.GridColumn gcCQTPointName;
        private DevExpress.XtraGrid.Columns.GridColumn gcServiceType;
        private DevExpress.XtraGrid.Columns.GridColumn gcAgent;
        private DevExpress.XtraGrid.Columns.GridColumn gcTarget;
        private DevExpress.XtraGrid.Columns.GridColumn gcBeginTime;
        private DevExpress.XtraGrid.Columns.GridColumn gcEndTime;
        private DevExpress.XtraGrid.Columns.GridColumn gcTaskComment;
        private DevExpress.XtraEditors.CheckedListBoxControl checkedListBoxPoints;
        private DevExpress.XtraEditors.CheckEdit checkAllPoints;
        private DevExpress.XtraEditors.SimpleButton btnQueryTaskResult;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.ComboBoxEdit cbbxCareerID;
        private DevExpress.XtraEditors.ComboBoxEdit cbbxServiceType;
        private DevExpress.XtraGrid.Columns.GridColumn gcCareerName;
        private DevExpress.XtraGrid.Columns.GridColumn gcRestDays;
        private DevExpress.XtraGrid.Columns.GridColumn gcComplianceRate;
        private DevExpress.XtraEditors.Repository.RepositoryItemProgressBar repositoryItemProgressBar1;
        private DevExpress.XtraEditors.TimeEdit timeSpan;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gcSubPointName;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gcSubPointTestedTime;
        private DevExpress.XtraGrid.Columns.GridColumn gcFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gcFileDuration;
        private DevExpress.XtraGrid.Columns.GridColumn gcTestedTime;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miReplayFiles;
        private DevExpress.XtraEditors.SimpleButton btnDeleteTask;
    }
}