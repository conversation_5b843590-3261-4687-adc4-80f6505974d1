﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRLTECollaborativeAnaByGridForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gvGrid = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gcDetail = new DevExpress.XtraGrid.GridControl();
            this.gvSerialGrid = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.panel1 = new System.Windows.Forms.Panel();
            this.spEditSerialNum = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.gvGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSerialGrid)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spEditSerialNum.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // gvGrid
            // 
            this.gvGrid.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand2,
            this.gridBand3});
            this.gvGrid.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn5,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22});
            this.gvGrid.GridControl = this.gcDetail;
            this.gvGrid.Name = "gvGrid";
            this.gvGrid.OptionsBehavior.Editable = false;
            this.gvGrid.OptionsView.ShowGroupPanel = false;
            this.gvGrid.DoubleClick += new System.EventHandler(this.gv_DoubleClick);
            // 
            // gridBand1
            // 
            this.gridBand1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand1.Caption = "基础";
            this.gridBand1.Columns.Add(this.gridColumn1);
            this.gridBand1.Columns.Add(this.gridColumn2);
            this.gridBand1.Columns.Add(this.gridColumn23);
            this.gridBand1.Columns.Add(this.gridColumn24);
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 294;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "SN";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Width = 58;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "类型";
            this.gridColumn2.FieldName = "TypeDesc";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.Width = 81;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "经度";
            this.gridColumn23.FieldName = "CentLng";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.Width = 107;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "纬度";
            this.gridColumn24.FieldName = "CentLat";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.Width = 106;
            // 
            // gridBand2
            // 
            this.gridBand2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand2.Caption = "NR";
            this.gridBand2.Columns.Add(this.gridColumn10);
            this.gridBand2.Columns.Add(this.gridColumn11);
            this.gridBand2.Columns.Add(this.gridColumn12);
            this.gridBand2.Columns.Add(this.gridColumn13);
            this.gridBand2.Columns.Add(this.gridColumn14);
            this.gridBand2.Columns.Add(this.gridColumn15);
            this.gridBand2.Columns.Add(this.gridColumn16);
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 263;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "NR采样点数";
            this.gridColumn10.FieldName = "NR.RSRP.Count";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.Width = 87;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "NR最小RSRP";
            this.gridColumn11.FieldName = "NR.RSRP.Min";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Width = 99;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "NR最大RSRP";
            this.gridColumn12.FieldName = "NR.RSRP.Max";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Width = 100;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "NR平均RSRP";
            this.gridColumn13.FieldName = "NR.RSRP.Avg";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.Width = 90;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "NR最小SINR";
            this.gridColumn14.FieldName = "NR.SINR.Min";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Width = 104;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "NR最大SINR";
            this.gridColumn15.FieldName = "NR.SINR.Max";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Width = 91;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "NR平均SINR";
            this.gridColumn16.FieldName = "NR.SINR.Avg";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.Width = 86;
            // 
            // gridBand3
            // 
            this.gridBand3.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand3.Caption = "LTE";
            this.gridBand3.Columns.Add(this.gridColumn5);
            this.gridBand3.Columns.Add(this.gridColumn17);
            this.gridBand3.Columns.Add(this.gridColumn18);
            this.gridBand3.Columns.Add(this.gridColumn19);
            this.gridBand3.Columns.Add(this.gridColumn20);
            this.gridBand3.Columns.Add(this.gridColumn21);
            this.gridBand3.Columns.Add(this.gridColumn22);
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 274;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "LTE采样点数";
            this.gridColumn5.FieldName = "LTE.RSRP.Count";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.Width = 90;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "LTE最小RSRP";
            this.gridColumn17.FieldName = "LTE.RSRP.Min";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Width = 100;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "LTE最大RSRP";
            this.gridColumn18.FieldName = "LTE.RSRP.Max";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Width = 105;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "LTE平均RSRP";
            this.gridColumn19.FieldName = "LTE.RSRP.Avg";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.Width = 92;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "LTE最小SINR";
            this.gridColumn20.FieldName = "LTE.SINR.Min";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Width = 104;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "LTE最大SINR";
            this.gridColumn21.FieldName = "LTE.SINR.Max";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Width = 95;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "LTE平均SINR";
            this.gridColumn22.FieldName = "LTE.SINR.Avg";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.Width = 92;
            // 
            // gcDetail
            // 
            this.gcDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gvGrid;
            gridLevelNode1.RelationName = "ResultList";
            this.gcDetail.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gcDetail.Location = new System.Drawing.Point(0, 53);
            this.gcDetail.MainView = this.gvSerialGrid;
            this.gcDetail.Name = "gcDetail";
            this.gcDetail.Size = new System.Drawing.Size(932, 484);
            this.gcDetail.TabIndex = 10;
            this.gcDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvSerialGrid,
            this.gvGrid});
            // 
            // gvSerialGrid
            // 
            this.gvSerialGrid.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn4,
            this.gridColumn6});
            this.gvSerialGrid.GridControl = this.gcDetail;
            this.gvSerialGrid.Name = "gvSerialGrid";
            this.gvSerialGrid.OptionsBehavior.Editable = false;
            this.gvSerialGrid.OptionsDetail.ShowDetailTabs = false;
            this.gvSerialGrid.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "类型";
            this.gridColumn4.FieldName = "TypeDesc";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 0;
            this.gridColumn4.Width = 121;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "栅格个数";
            this.gridColumn6.FieldName = "GridCount";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 1;
            this.gridColumn6.Width = 100;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.spEditSerialNum);
            this.panel1.Controls.Add(this.labelControl1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(932, 53);
            this.panel1.TabIndex = 11;
            // 
            // spEditSerialNum
            // 
            this.spEditSerialNum.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.spEditSerialNum.Location = new System.Drawing.Point(136, 17);
            this.spEditSerialNum.Name = "spEditSerialNum";
            this.spEditSerialNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spEditSerialNum.Properties.IsFloatValue = false;
            this.spEditSerialNum.Properties.Mask.EditMask = "N00";
            this.spEditSerialNum.Properties.MaxValue = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.spEditSerialNum.Properties.MinValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.spEditSerialNum.Size = new System.Drawing.Size(67, 21);
            this.spEditSerialNum.TabIndex = 3;
            this.spEditSerialNum.EditValueChanged += new System.EventHandler(this.spEditSerialNum_EditValueChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(21, 20);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(109, 14);
            this.labelControl1.TabIndex = 2;
            this.labelControl1.Text = "连续汇聚栅格个数 ≥";
            // 
            // NRLTECollaborativeAnaByGridForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(932, 537);
            this.Controls.Add(this.gcDetail);
            this.Controls.Add(this.panel1);
            this.Name = "NRLTECollaborativeAnaByGridForm";
            this.Text = "4/5G协同分析结果";
            ((System.ComponentModel.ISupportInitialize)(this.gvGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSerialGrid)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spEditSerialNum.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gcDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView gvSerialGrid;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView gvGrid;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraEditors.SpinEdit spEditSerialNum;
        private DevExpress.XtraEditors.LabelControl labelControl1;
    }
}