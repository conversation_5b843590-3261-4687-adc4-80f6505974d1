﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TextOutputForm : BaseDialog
    {
        public TextOutputForm()
        {
            InitializeComponent();
            this.btnClose.Click += BtnClose_Click;
        }

        public string ShowText
        {
            get { return this.txtShow.Text; }
            set { this.txtShow.Text = value; }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
