﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using CQTLibrary.CqtZTFunc;
using CQTLibrary.PublicItem;
using MasterCom.Util;
using MasterCom.RAMS.Model;


namespace MasterCom.RAMS.CQT
{
    public partial class FrmCQTComplainOverview : Form
    {
        public FrmCQTComplainOverview(MainModel mainModel)
        {
            InitializeComponent();
            trackBarControl1.Value = 500;
            mainmodel = mainModel;
        } 
        private MainModel mainmodel;
        int trackBarValue = 0;
        
        private ComplainDetailResult detailResultMap;
        List<ComplainCase> nearCpListMap = new List<ComplainCase>();
        string cqtNameMT = "";

        public Dictionary<string, AreaDetail> InitcqtDetailDic { get; set; }
        public List<TimePeriod> TpList { get; set; }
        public CQTLibrary.RAMS.NET.MainModel CqtModel { get; set; }
        public List<CQTLibrary.PublicItem.ParaColumnItem> Paraitem { get; set; }

        private List<ComplainResult> complainWorthResultList = new List<ComplainResult>();
        public List<ComplainResult> ComplainResultList { get; set; }
        
        float flongitude = 0;
        float flatitude = 0;

        /// <summary>
        /// 双击某一行时
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gridControl1_DoubleClick(object sender, EventArgs e)
        {
            cqtNameMT = "";
            nearCpListMap.Clear();
            MasterCom.RAMS.Func.MapForm mf = mainmodel.MainForm.GetMapForm();
            mf.clearData();
            if (this.bandedGridView1.SelectedRowsCount > 0)
            {
                int row = bandedGridView1.FocusedRowHandle;
                ComplainResult complainResult = bandedGridView1.GetRow(row) as ComplainResult;
                FrmCQTComplainDetail frmDetail = new FrmCQTComplainDetail(TpList);
                frmDetail.ComplainResult = complainResult;
                frmDetail.InitcqtDetailDic = InitcqtDetailDic;
                frmDetail.TpList = TpList;
                frmDetail.Paraitem = Paraitem;
                frmDetail.CqtModel = this.CqtModel;
                frmDetail.Query();
                frmDetail.Show();

                cqtNameMT = complainResult.StrCqtName;
                trackBarValue = trackBarControl1.Value;
                ComplainAna compAna = new ComplainAna();
                detailResultMap = compAna.getCpDetailCase(TpList[0].BeginTime, TpList[0].EndTime, CqtModel, trackBarValue, InitcqtDetailDic[complainResult.StrCqtName]);
                flongitude = float.Parse((InitcqtDetailDic[complainResult.StrCqtName].Ilongitude * 1.0 / Math.Pow(10, 7)).ToString());
                flatitude = float.Parse((InitcqtDetailDic[complainResult.StrCqtName].Ilatitude * 1.0 / Math.Pow(10, 7)).ToString());
                if (isPointInThisRect(flongitude, flatitude))
                {
                    nearCpListMap.AddRange(detailResultMap.nearCpList);
                    FillDataFromCQTPointMngr();
                    mainmodel.MainForm.GetMapForm().GoToView(flongitude, flatitude);
                }
            }
        }

        private bool isPointInThisRect(double longitue, double latitue)
        {
            return longitue > 105.4107 && longitue < 111.3969 && latitue > 31.6920 && latitue < 39.5969;
        }

        private void trackBarControl1_BeforeShowValueToolTip(object sender, DevExpress.XtraEditors.TrackBarValueToolTipEventArgs e)
        {
            e.ShowArgs.ToolTip = string.Format("附近{0}米", trackBarControl1.Value);
        }

        /// <summary>
        /// 刷新
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            trackBarValue = trackBarControl1.Value;
            WaitBox.Text = "准备查询...";
            WaitBox.CanCancel = true;
            WaitBox.Show("开始接收统计数据...", queryData);
            this.gridControl1.DataSource = ComplainResultList;
            this.gridControl1.RefreshDataSource();
            checkResultBox.Checked = false;
        }

        /// <summary>
        /// 查询统计数据
        /// </summary>
        protected void queryData()
        {
            try
            {
                ComplainAna compAna = new ComplainAna();
                WaitBox.Text = "正在查询投诉数据...";
                WaitBox.ProgressPercent = 60;
                Dictionary<string,int> addrnameNearDic = compAna.updateNearCpNum(TpList[0].BeginTime, TpList[0].EndTime, CqtModel, trackBarValue, compAna.intCqtDetailInfo(CqtModel));
                foreach (ComplainResult complainResult in ComplainResultList)
                {
                    if (addrnameNearDic.ContainsKey(complainResult.StrCqtName))
                    {
                        complainResult.INearCpNum = addrnameNearDic[complainResult.StrCqtName];
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void checkResultBox_CheckedChanged(object sender, EventArgs e)
        {
            if (checkResultBox.Checked)
            {
                for (int i = 0; i < ComplainResultList.Count; i++)
                {
                    if (!ComplainResultList[i].StrTest.Equals("未知"))
                    {
                        complainWorthResultList.Add(ComplainResultList[i]);
                    }
                }
                this.gridControl1.DataSource = complainWorthResultList;
                this.gridControl1.RefreshDataSource();
            }
            else
            {
                this.gridControl1.DataSource = ComplainResultList;
                this.gridControl1.RefreshDataSource();
            }
        }

        private void 导出EXCELToolStripMenuItem1_Click(object sender, EventArgs e)
        {
            //SaveFileDialog dlg = new SaveFileDialog();
            //dlg.Title = "投诉概况表 ";
            //dlg.RestoreDirectory = true;
            //dlg.Filter = "Excel文件(*.xls)|*.xls";
            //if (dlg.ShowDialog() == DialogResult.OK)
            //{
            //    string filename = dlg.FileName;
            //    this.gridControl1.ExportToExcelOld(filename);

            //    DevExpress.XtraEditors.XtraMessageBox.Show(" 导出成功！ ", " 提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //}
            List<List<object>> exportList = GridViewTransfer.Transfer(this.gridControl1);
            ExcelNPOIManager.ExportToExcel(exportList);
        }

        /// <summary>
        /// 画图
        /// </summary>
        private void FillDataFromCQTPointMngr()
        {
            MasterCom.RAMS.Func.MapForm mf = mainmodel.MainForm.GetMapForm();
            if (mf != null)
            {
                MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(CQTZTMapPointLayer));
                if (cLayer == null)
                {
                    CQTZTMapPointLayer layer = new CQTZTMapPointLayer(mf.GetMapOperation(), "CQTZT");
                    mf.AddTempCustomLayer(layer);
                    layer.CqtName = cqtNameMT;
                    layer.FLongttude = flongitude;
                    layer.FLatitue = flatitude;
                    layer.CQTPoints2ShowSec = nearCpListMap;
                    layer.Invalidate();
                }
                else
                {
                    CQTZTMapPointLayer layer = cLayer as CQTZTMapPointLayer;
                    layer.CqtName = cqtNameMT;
                    layer.FLongttude = flongitude;
                    layer.FLatitue = flatitude;
                    layer.CQTPoints2ShowSec = nearCpListMap;
                    layer.Invalidate();
                }
            }
        }

    }
}
