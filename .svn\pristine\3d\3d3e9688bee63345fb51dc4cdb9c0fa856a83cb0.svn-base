﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYFastFadingByRegion_LTESCANTOPN : DIYFastFadingByRegion_TDSCAN
    {
        public DIYFastFadingByRegion_LTESCANTOPN(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16019, this.Name);//////
        }

        readonly FastFadingDlg_TDSCAN conditionDlg = new FastFadingDlg_TDSCAN();
        protected override bool getCondition()
        {
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                conditionDlg.GetFilterCondition(out rxLevDValue, out secondLast, out secondFading, out rxLevDValueFading);
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    clearIndermediateVariable();
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        dealTPInfo(testPointList, i);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealTPInfo(List<TestPoint> testPointList, int i)
        {
            TestPoint testPoint = testPointList[i];
            if (isValidTestPoint(testPoint))
            {
                Dictionary<string, float> cellRxLevDic = new Dictionary<string, float>();
                float? rxLevMax = (float?)testPoint["LTESCAN_TopN_PSS_RP", 0];
                for (int j = 0; j < 50; j++)
                {
                    float? rxLev = (float?)testPoint["LTESCAN_TopN_PSS_RP", j];
                    if (rxLev == null || rxLev > 0 || rxLev < -150)
                    {
                        break;
                    }
                    int? tac = (int?)testPoint["LTESCAN_TopN_TAC", j];
                    int? eci = (int?)testPoint["LTESCAN_TopN_ECI", j];
                    int? earfcn = (int?)testPoint["LTESCAN_TopN_EARFCN", j];
                    int? pci = (int?)(short?)testPoint["LTESCAN_TopN_PCI", j];
                    LTECell cell = CellManager.GetInstance().GetNearestLTECell(testPoint.DateTime, (short)tac, (byte)eci, earfcn, pci, testPoint.Longitude, testPoint.Latitude);
                    if (cell != null)
                    {
                        cellRxLevDic[cell.Name] = (float)rxLev;
                        judgeCell(rxLevMax, rxLev, cell.Name, testPointList, i);
                    }
                }

                judgeTestPoint(testPointList, i, cellRxLevDic);
            }
            else
            {
                clearIndermediateVariable();
            }
        }
    }
}
