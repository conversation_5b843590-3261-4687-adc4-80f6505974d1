﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Drawing2D;

namespace MasterCom.RAMS.Func
{
    public class CellCloudPictureLayer : CustomDrawLayer
    {
        public static CellCloudPictureLayer GetInstance()
        {
            if (instance == null)
            {
                MapOperation mp = MainModel.GetInstance().MainForm.GetMapForm().GetMapOperation();
                instance = new CellCloudPictureLayer(mp, "小区云图");
            }
            return instance;
        }

        /// <summary>
        /// 云图绘制数据
        /// </summary>
        public List<CellCloudPictureData> Datas
        {
            get { return datas; }
            set
            {
                datas = value;
                if (datas == null)
                {
                    datas = new List<CellCloudPictureData>();
                }
            }
        }

        /// <summary>
        /// 云图绘制条件设置
        /// </summary>
        public CellCloudPictureConfig Config
        {
            get { return config; }
            set
            {
                config = value;
                if (config == null)
                {
                    config = new CellCloudPictureConfig();
                }
            }
        }

        /// <summary>
        /// 清空云图缓存，准备下次重绘
        /// </summary>
        public void ClearCache()
        {
            lastBitmap = null;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (datas.Count == 0) return;

            UpdateRectangle(ref clientRect, ref updateRect);
            if (updateRect.Width == 0 || updateRect.Height == 0)
            {
                return; // 相交区域为空
            }

            // 判断重绘
            DbPoint mapCenter = Map.GetCenter();
            if (lastCenter == null || lastBitmap == null
                || updateRect != lastRect
                || lastCenter.x != mapCenter.x || lastCenter.y != mapCenter.y)
            {
                lastCenter = mapCenter;
                lastRect = updateRect;
            }
            else
            {
                graphics.DrawImage(lastBitmap, lastRect.X, lastRect.Y);
                return;
            }

            // 注意Bitmap像素格式为默认
            Bitmap bitMap = new Bitmap(updateRect.Width, updateRect.Height, graphics);
            Bitmap tmpMap = new Bitmap(updateRect.Width, updateRect.Height, graphics);
            Rectangle mapRect = new Rectangle();
            byte[] byteMap = new byte[updateRect.Height * updateRect.Width * 4];

            // 初始化
            unsafe
            {
                fixed (byte* bytePtr = byteMap)
                {
                    for (int i = 0; i < updateRect.Height * updateRect.Width; ++i)
                        bytePtr[i * 4] = 0;
                }
            }

            drawCellRegion(updateRect, tmpMap, mapRect, byteMap);

            ColorMapping(bitMap, byteMap); // 颜色映射
            (new AverageBlur(bitMap)).BlurRGBChannel(2); // 模糊处理
            graphics.DrawImage(bitMap, updateRect.X, updateRect.Y);
            tmpMap.Dispose();

            // 保存当前的bitMap，防止相同重绘的重复计算
            if (lastBitmap != null)
            {
                lastBitmap.Dispose();
            }
            lastBitmap = bitMap;
        }

        private void drawCellRegion(Rectangle updateRect, Bitmap tmpMap, Rectangle mapRect, byte[] byteMap)
        {
            // 在tmpMap上绘制每个小区的椭圆
            DbRect geoRect;
            Map.FromDisplay(updateRect, out geoRect);
            foreach (CellCloudPictureData data in datas)
            {
                // 不在当前范围
                if (data.Longitude < geoRect.x1 || data.Longitude > geoRect.x2
                    || data.Latitude < geoRect.y1 || data.Latitude > geoRect.y2)
                {
                    continue;
                }

                if (double.IsNaN(data.Weight) || data.Weight < Config.MinWeight || data.Weight > Config.MaxWeight)
                {
                    continue;
                }

                // 半径长度为0像素的情况
                if (!FillCellRegion(data, tmpMap, ref mapRect) || mapRect.Width < 1 || mapRect.Height < 1)
                {
                    continue;
                }

                // 将小区的椭圆区域的颜色值复制到byteMap数组，并清空刚绘制的tmpMap
                CopyRegionToArray(tmpMap, mapRect, byteMap);
            }
        }

        // 将小区的椭圆范围的颜色值复制到数组，并应用BlendMode.Screen效果，注意像素格式和颜色通道
        private void CopyRegionToArray(Bitmap bitMap, Rectangle mapRect, byte[] byteMap)
        {
            BitmapData bitMapData = bitMap.LockBits(mapRect, ImageLockMode.ReadWrite, bitMap.PixelFormat);
            int bmStride = bitMapData.Stride;
            int srcIx = 0, dstIx = 0;
            int mapRectHeight = mapRect.Height; int mapRectWidth = mapRect.Width;
            int mapRectX = mapRect.X; int mapRectY = mapRect.Y;

            unsafe
            {
                byte* srcPtr = (byte*)bitMapData.Scan0;
                fixed (byte* dstPtr = byteMap)
                {
                    for (int y = 0; y < mapRectHeight; ++y)
                    {
                        for (int x = 0; x < mapRectWidth; ++x)
                        {
                            srcIx = y * bmStride + x * 4;
                            dstIx = (mapRectY + y) * bmStride + (mapRectX + x) * 4;
                            // BlendMode.Screen效果公式的化简
                            // 原式为：Math.Max(Math.Min(255 - ((255 - Src)/255.0f * (255 - Dst)/255.0f) * 255.0f, 255), 0);
                            dstPtr[dstIx] += (byte)(srcPtr[srcIx] - dstPtr[dstIx] * srcPtr[srcIx] / 255);
                            srcPtr[srcIx] = 0;
                        }
                    }
                }
            }
            bitMap.UnlockBits(bitMapData);
        }

        // 将小区的理想覆盖半径转换为像素长度
        private double ConvertRadiusPixel(double radius)
        {
            DbPoint start = new DbPoint(0, 0);
            DbPoint end = new DbPoint(radius, radius);
            PointF startF, endF;
            Map.ToDisplay(start, out startF);
            Map.ToDisplay(end, out endF);
            float ofsX = endF.X - startF.X;
            float ofsY = endF.Y - startF.Y;
            return Math.Sqrt(ofsX * ofsX + ofsY * ofsY);
        }

        // 径向渐变填充小区的椭圆区域
        private bool FillCellRegion(CellCloudPictureData data, Bitmap bitmap,
            ref Rectangle rect)
        {
            // 半径像素
            float radiusPixel = (float)ConvertRadiusPixel(data.Radius);
            if (radiusPixel == 0)
            {
                return false;
            }

            // 小区位置像素（相对框选区域或者可视区域）
            PointF centerPiexl;
            RectangleF cellRectF = new RectangleF();
            Map.ToDisplay(new DbPoint(data.Longitude, data.Latitude), out centerPiexl);
            centerPiexl.X -= lastRect.X;
            centerPiexl.Y -= lastRect.Y;

            // 小区矩形设置
            cellRectF.Width = (float)(radiusPixel * config.MinorSemiAxisRate * 2);
            cellRectF.Height = (float)(radiusPixel * config.MajorSemiAxisRate * 2);
            cellRectF.X = centerPiexl.X - cellRectF.Width / 2;
            cellRectF.Y = centerPiexl.Y;

            // 矩阵旋转后移
            Matrix matrix = new Matrix();
            matrix.RotateAt(270 - (450 - data.Direction) % 360, centerPiexl);
            matrix.Translate(0f, -(float)(cellRectF.Height / 2 * config.CenterOffsetRate));

            // 路径
            GraphicsPath path = new GraphicsPath();
            path.AddEllipse(cellRectF);

            // 画刷
            PathGradientBrush brush = new PathGradientBrush(path);
            brush.CenterPoint = new PointF(centerPiexl.X,
                (float)(centerPiexl.Y + config.GradientOffsetRate * cellRectF.Height / 2));
            double color =
                (Math.Min(Math.Max(data.Weight, config.MinWeight), config.MaxWeight) - config.MinWeight)
                / (config.MaxWeight - config.MinWeight) * 255;
            brush.CenterColor = Color.FromArgb(255, 0, 0, (int)color);
            brush.SurroundColors = new Color[] { Color.FromArgb(255, 0, 0, 0) };

            // 绘制
            Graphics graphics = Graphics.FromImage(bitmap);
            graphics.Transform = matrix;
            graphics.CompositingMode = CompositingMode.SourceOver;
            graphics.FillEllipse(brush, cellRectF);

            // 设置返回,应用变换后的椭圆矩阵
            path.Transform(matrix);
            int x1 = int.MaxValue, x2 = int.MinValue, y1 = int.MaxValue, y2 = int.MinValue;
            PointF[] points = path.PathPoints;
            int length = points.Length, x, y;
            for (int i = 0; i < length; ++i)
            {
                x = (int)points[i].X;
                y = (int)points[i].Y;
                x1 = Math.Min(x1, x);
                x2 = Math.Max(x2, x);
                y1 = Math.Min(y1, y);
                y2 = Math.Max(y2, y);
            }
            rect.X = Math.Max(0, x1); rect.Y = Math.Max(0, y1);
            rect.Width = Math.Min(x2, bitmap.Width) - rect.X;
            rect.Height = Math.Min(y2, bitmap.Height) - rect.Y;

            // 清除
            matrix.Dispose();
            brush.Dispose();
            path.Dispose();
            graphics.Dispose();

            return true;
        }

        // 颜色映射，注意位图的像素格式
        private void ColorMapping(Bitmap bitMap, byte[] byteMap)
        {
            BitmapData bmData = bitMap.LockBits(new Rectangle(0, 0, bitMap.Width, bitMap.Height),
                ImageLockMode.WriteOnly, bitMap.PixelFormat);
            int srcIx = 0;
            int bmDataWidth = bmData.Width; int bmDataHeight = bmData.Height;
            unsafe
            {
                int* dstPtr = (int*)bmData.Scan0;
                fixed (byte* srcPtr = byteMap)
                {
                    for (int y = 0; y < bmDataHeight; ++y)
                        for (int x = 0; x < bmDataWidth; ++x)
                        {
                            srcIx = bmDataWidth * 4 * y + x * 4;
                            dstPtr[bmDataWidth * y + x] = (int)config.ColorArray[srcPtr[srcIx]];
                        }
                }
            }
            bitMap.UnlockBits(bmData);
        }

        // 计算可视区域与框选区域的交集
        private void UpdateRectangle(ref Rectangle clientRect, ref Rectangle updateRect)
        {
            SearchGeometrys sg = MainModel.GetInstance().SearchGeometrys;
            updateRect = clientRect;
            if (!sg.IsSelectRegion())
            {
                return;
            }
            DbRect geoRect = sg.RegionBounds;
            PointF pf1, pf2;
            Map.ToDisplay(new DbPoint(geoRect.x1, geoRect.y2), out pf1);
            Map.ToDisplay(new DbPoint(geoRect.x2, geoRect.y1), out pf2);
            Rectangle rect = new Rectangle((int)pf1.X, (int)pf1.Y, (int)(pf2.X - pf1.X), (int)(pf2.Y - pf1.Y));
            updateRect.Intersect(rect);
        }

        private CellCloudPictureLayer(MapOperation mp, string name)
            : base(mp, name)
        {
            VisibleScaleEnabled = true;
            VisibleScale.ScaleMax = 1000000;
            VisibleScale.ScaleMin = 1;
        }

        private Bitmap lastBitmap;
        private DbPoint lastCenter;
        private Rectangle lastRect;
        private static CellCloudPictureLayer instance;
        private List<CellCloudPictureData> datas = new List<CellCloudPictureData>();
        private CellCloudPictureConfig config = new CellCloudPictureConfig();
    }
}
