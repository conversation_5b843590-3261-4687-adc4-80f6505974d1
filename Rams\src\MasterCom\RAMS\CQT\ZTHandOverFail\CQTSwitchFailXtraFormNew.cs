﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTSwitchFailXtraFormNew : DevExpress.XtraEditors.XtraForm
    {
        public CQTSwitchFailXtraFormNew(MainModel Mainmodel, QueryCondition Condition , string netWoker)
        {
            InitializeComponent();
            mainmodel = Mainmodel;
            conditioncqt = Condition;
            if (netWoker.Equals("GSM"))
                this.Text = "GSM切换失败事件分析";
            else if (netWoker.Equals("TD"))
                this.Text = "TD切换失败事件分析";
        }
        MainModel mainmodel = null;
        QueryCondition conditioncqt = null;
        public void setData(List<CQTHandOverItemFail> result)
        {
            this.gridControl1.DataSource = result;
        }
        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }
        DateTime dtime;
        private void eveToolStripMenuItem_Click(object sender, EventArgs e)
        {
            int[] row = gridView1.GetSelectedRows();
            if (row.Length == 0)
                return;
            object ev = gridView1.GetRow(row[0]);
            CQTHandOverItemFail eve = ev as CQTHandOverItemFail;
            dtime = eve.Dtime;
            if (eve.Ifileid != 0)
            {
                QueryItem queryitem = new QueryItem();
                queryitem.IFileid = eve.Ifileid;
                queryitem.Istime = Convert.ToInt32((int)(JavaDate.GetMilliseconds(eve.Dtime) / 1000));
                queryitem.Ietime = Convert.ToInt32((int)(JavaDate.GetMilliseconds(eve.Dtime) / 1000));
                DiySqlQueryReplaySampleInfoCQT diyreplay = new DiySqlQueryReplaySampleInfoCQT(mainmodel);
                DiySqlQueryReplaySampleInfoCQT.QItem = queryitem;
                DiySqlQueryReplaySampleInfoCQT.mergeSql();
                diyreplay.Query();
                ReplaySampleItem replaysample = diyreplay.RsItem;
                mainmodel.MainForm.NeedChangeWorkSpace(false);
                DIYReplayFile(replaysample);
            }
        }

        private void DIYReplayFile(ReplaySampleItem replaysample)
        {
            FileInfo fileInfo = new FileInfo();
            fileInfo.DistrictID = conditioncqt.DistrictID;
            fileInfo.Name = replaysample.StrFileName;
            fileInfo.ProjectID = replaysample.IProjectType;
            fileInfo.ID = replaysample.IFileid;
            fileInfo.LogTable = replaysample.StrLogFileName;
            fileInfo.ServiceType = replaysample.IServicetType;
            fileInfo.SampleTbName = replaysample.StrSampleName;
            FileReplayer.ReplayOnePart(fileInfo, dtime);
        }

        private void CQTGSMSwitchFailXtraForm_Load(object sender, EventArgs e)
        {
            //
        }
    }
}