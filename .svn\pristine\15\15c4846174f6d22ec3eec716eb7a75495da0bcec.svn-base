﻿using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRMainCellLastOccupyForm : MinCloseForm
    {
        public NRMainCellLastOccupyForm()
        {
            InitializeComponent();
        }

        public void FillData(List<NRMainCellLastOccupyInfo> resultList)
        {
            this.treeList.BeginUpdate();
            this.treeList.Nodes.Clear();
            foreach (NRMainCellLastOccupyInfo res in resultList)
            {
                TreeListNode rootNode = treeList.AppendNode(new object[] { res.FileName }, null);
                rootNode.Tag = res;
                foreach (NRMainCellLastOccupyCellInfo info in res.CellInfoList)
                {
                    TreeListNode pNode = treeList.AppendNode(new object[] { "", info.Time, info.CellName, info.EARFCN
                        , info.PCI, info.TAC, info.NCI, info.Longitude, info.Latitude, info.Duration
                        , info.RsrpInfo.Avg, info.SinrInfo.Avg, info.TestPntList.Count }, rootNode);
                    pNode.Tag = info;
                }
            }
            this.treeList.EndUpdate();
            MasterCom.Util.DevControlManager.TreeListHelper.ThreeStateControl(treeList);
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rowList = new List<NPOIRow>();
            NPOIRow headerRow = new NPOIRow();
            int columnCount = treeList.Columns.Count;
            for (int i = 0; i < columnCount; i++)
            {
                DevExpress.XtraTreeList.Columns.TreeListColumn column = treeList.Columns[i];
                if (column.Visible)
                {
                    headerRow.AddCellValue(column.Caption);
                }
            }
            rowList.Add(headerRow);
            addRowContent(rowList, columnCount, treeList.Nodes);
            ExcelNPOIManager.ExportToExcel(rowList, true);
        }

        private void addRowContent(List<NPOIRow> rowList, int columnCount, TreeListNodes rootNodes)
        {
            foreach (TreeListNode node in rootNodes)
            {
                NPOIRow nodeRow = new NPOIRow();
                for (int i = 0; i < columnCount; i++)
                {
                    if (treeList.Columns[i].Visible)
                    {
                        nodeRow.AddCellValue(node.GetDisplayText(i));
                    }
                }
                rowList.Add(nodeRow);
                if (node.Nodes != null)
                {
                    addRowContent(rowList, columnCount, node.Nodes);
                }
            }
        }

        private void treeList_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            TreeListNode node = (sender as TreeList).CalcHitInfo(new Point(e.X, e.Y)).Node;
            if (node == null)
            {
                treeList.Selection.Clear();
                treeList.FocusedNode = null;
                return;
            }
            if (node.Tag is NRMainCellLastOccupyInfo)
            {
                MainModel.ClearDTData();
                NRMainCellLastOccupyInfo info = node.Tag as NRMainCellLastOccupyInfo;
                foreach (var item in info.CellInfoList)
                {
                    foreach (var tp in item.TestPntList)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                }
            }
            else if (node.Tag is NRMainCellLastOccupyCellInfo)
            {
                MainModel.ClearDTData();
                NRMainCellLastOccupyCellInfo info = node.Tag as NRMainCellLastOccupyCellInfo;
                foreach (var tp in info.TestPntList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.IsFileReplayByCompareMode = false;
            MainModel.FireDTDataChanged(this);
            MainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
        }
    }
}
