﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatByRoad : QueryKPIStatByRegion
    {
        public override string Name
        {
            get { return "GIS选取一道路进行KPI统计"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11020, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Street;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return searchGeometrys.SelectedStreets.Count > 0;
        }

        /// <summary>
        /// 以道路形成的区域作为限定区域
        /// </summary>
        /// <param name="package"></param>
        protected override void AddGeographicFilter(Package package)
        {
            if (isQueringEvent)
            {
                AddDIYEndOpFlag(package);
                this.AddDIYStreets_Sample(package);
            }
            else
            {
                this.AddDIYStreet_Intersect(package);
            }
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
            {
                return;
            }
            fillStatData(package, curImgColumnDef, singleStatData);
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);
            KpiDataManager.AddStatData(string.Empty, string.Empty, fi, singleStatData, this.curReportStyle.HasGridPerCell);
        }

        protected override void handleStatEvent(Event evt)
        {
            if (!condition.Geometorys.GeoOp.Contains(evt.Longitude, evt.Latitude))
            {
                return;
            }
            StatDataEvent eventData = new StatDataEvent(evt, this.needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            KpiDataManager.AddStatData(string.Empty, string.Empty, fi, eventData, this.curReportStyle.HasGridPerCell);
        }


    }


}
