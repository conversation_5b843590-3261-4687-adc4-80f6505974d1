﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.Func
{
    public partial class ItemSelectionPanel : UserControl
    {
        ToolStripDropDown parentDropDown;
        ListView effectListView;
        Label countLabel;
        MapFormItemSelection itemSelection;
        string type;
        public ItemSelectionPanel(ToolStripDropDown dropDown,ListView lv,Label lb,MapFormItemSelection selection,string type,bool canManual)
        {
            InitializeComponent();
            MasterCom.Util.TreeViewCheckHelper.AutoUpdateCheckState(treeViewProject);
            this.parentDropDown = dropDown;
            this.effectListView = lv;
            this.countLabel = lb;
            this.itemSelection = selection;
            this.type = type;
            this.btnManual.Enabled = canManual;
        }

        internal void FreshItems()
        {
            treeViewProject.Nodes.Clear();
            if (type == "Project")
            {
                #region Project
                CategoryEnumItem[] enumItems = ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items;
#if GDProjectOrder
                int[] proIDNew = {7,12,5,18,32,21,46,22,39,44,6,23,43,45,35,1,42,40,4,37,27,28,36,38,
                                     2,25,26,20,24,31,29,19,30,3,41,13,33,34,17,15,301,302};
                int iID = 0;
                List<int> proIDList = new List<int>();
                for (int i = 0; i < proIDNew.Length; i++)
                {
                    foreach (CategoryEnumItem cateEnumItem in ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items)
                    {
                        if (cateEnumItem.ID == proIDNew[i])
                        {
                            enumItems[iID++] = cateEnumItem;
                            proIDList.Add(proIDNew[i]);
                            break;
                        }
                    }
                }
                foreach (CategoryEnumItem cateEnumItem in ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items)
                {
                    if (!proIDList.Contains(cateEnumItem.ID))
                        enumItems[iID++] = cateEnumItem;
                }
#endif
                addItems(enumItems, itemSelection.ProjectGroups);
                #endregion
            }
            else if (type == "ServiceType")
            {
                #region ServiceType
                CategoryEnumItem[] enumItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
                addItems(enumItems, itemSelection.ServiceGroups);
                #endregion
            }
            else if (type == "GridColorModes")
            {
                #region GridColorModes
                CategoryEnumItem[] enumItems = ((CategoryEnum)CategoryManager.GetInstance()["GridColorModes"]).Items;
                addItems(enumItems, itemSelection.ServiceGroups);
                #endregion
            }
            else if (type == "Agent")
            {
                #region Agent
                CategoryEnumItem[] enumItems = ((CategoryEnum)CategoryManager.GetInstance()["Agent"]).Items;
                addItems(enumItems, itemSelection.AgentGroups);
                #endregion
            }
            else if (type == "City")
            {
                #region City
                List<IDNamePair> listPair = MainModel.GetInstance().User.GetAvailableCitys();
                CategoryEnumItem[] enumItems = new CategoryEnumItem[listPair.Count];
                for (int i = 0; i < listPair.Count; i++)
                {
                    IDNamePair p = listPair[i];
                    CategoryEnumItem item = new CategoryEnumItem();
                    item.ID = p.id;
                    item.Description = p.Name;
                    enumItems[i] = item;
                }
                GroupItem g = new GroupItem();
                g.Name = "全部";
                makeTreeView(this.treeViewProject, new List<GroupItem>() { g }, enumItems);
                #endregion
            }
        }

        private void addItems(CategoryEnumItem[] enumItems, List<GroupItem> groups)
        {
            bool isAddAll = false;//是否已含有“全部”节点
            foreach (GroupItem item in groups)
            {
                if (item.Name == "全部")
                {
                    isAddAll = true;
                    break;
                }
            }

            if (!isAddAll)
            {
                GroupItem groupItem = new GroupItem();
                groupItem.Name = "全部";
                foreach (CategoryEnumItem enumItem in enumItems)
                {
                    groupItem.ItemsIDs.Add(enumItem.ID);
                }
                groups.Add(groupItem);
            }
            makeTreeView(treeViewProject, groups, enumItems);
        }

        List<GroupItem>    m_groupItems;
        CategoryEnumItem[] m_enumItems;

        private void makeTreeView(TreeView treeView,List<GroupItem> groupItems,CategoryEnumItem[] enumItems)
        {
            m_groupItems = groupItems;
            m_enumItems = enumItems;
            treeView.Nodes.Clear();
            foreach (GroupItem item in groupItems)
            {
                TreeNode node = new TreeNode();
                node.Text = item.Name;
                node.Tag = item;
                if(item.Name == "全部")
                {
                    addAllNodes(enumItems, node);
                    node.ExpandAll();
                    //node.Collapse();
                }
                else
                {
                    addItemNodes(item, enumItems, node);
                    node.ExpandAll();
                }
                treeView.Nodes.Add(node);
            }
            //treeView.ExpandAll();
        }

        private void addAllNodes(CategoryEnumItem[] enumItems, TreeNode node)
        {
            foreach (CategoryEnumItem enumItem in enumItems)
            {
                addValidNode(enumItem, node);
            }
        }

        private void addItemNodes(GroupItem item, CategoryEnumItem[] enumItems, TreeNode node)
        {
            foreach (int id in item.ItemsIDs)
            {
                foreach (CategoryEnumItem enumItem in enumItems)
                {
                    if (id == enumItem.ID)
                    {
                        addValidNode(enumItem, node);
                        break;
                    }
                }
            }
        }

        private void addValidNode(CategoryEnumItem enumItem, TreeNode node)
        {
            string[] keyword = edtKeyword.Text.Trim().Split(' ');
            bool bShow = false;

            if (edtKeyword.Text.Trim().Length == 0 || keyword.Length == 0)
            {
                bShow = true;
            }
            else
            {
                for (int i = 0; i < keyword.Length; i++)
                {
                    if (enumItem.Description.ToUpper().Contains(keyword[i].ToUpper()))
                    {
                        bShow = true;
                        break;
                    }
                }
            }

            if (bShow)
            {
                TreeNode childNode = new TreeNode();
                childNode.Text = enumItem.Description;
                childNode.Tag = enumItem.ID;
                node.Nodes.Add(childNode);
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            effectListView.Items.Clear();
            Dictionary<int, string> dict = new Dictionary<int, string>();
            foreach (TreeNode node in treeViewProject.Nodes)
            {
                foreach (TreeNode childNode in node.Nodes)
                {
                    if (childNode.Checked)
                    {
                        int id = (int)childNode.Tag;
                        if (!dict.ContainsKey(id))
                        {
                            dict.Add(id, childNode.Text);
                        }
                    }
                }
            }
            foreach (int id in dict.Keys)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Text = dict[id];
                lvi.Tag = id;
                effectListView.Items.Add(lvi);
            }
            countLabel.Text = "[" + dict.Keys.Count + "]";
            parentDropDown.Close();
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            foreach (TreeNode node in treeViewProject.Nodes)
            {
                node.Checked = false;
                foreach (TreeNode childNode in node.Nodes)
                {
                    childNode.Checked = false;
                }
            }
        }

        private void btnManual_Click(object sender, EventArgs e)
        {

            ItemSetChooseForm form = ItemSetChooseForm.GetInstance();
            CategoryEnumItem[] enumItems = null;
            if (type == "Project")
            {
                enumItems = ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items;
                form.freshItems(enumItems, itemSelection.ProjectGroups);
            }
            else if (type == "ServiceType")
            {
                enumItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
                form.freshItems(enumItems, itemSelection.ServiceGroups);
            }
            else if (type == "Agent")
            {
                enumItems = ((CategoryEnum)CategoryManager.GetInstance()["Agent"]).Items;
                form.freshItems(enumItems, itemSelection.AgentGroups);
            }
            else
            {
                return;
            }
            
            if (form.ShowDialog() == DialogResult.OK)
            {
                if (type == "Project")
                {
                    makeTreeView(this.treeViewProject, itemSelection.ProjectGroups, enumItems);
                }
                else if (type == "ServiceType")
                {
                    makeTreeView(this.treeViewProject, itemSelection.ServiceGroups, enumItems);
                }
                else if (type == "Agent")
                {
                    makeTreeView(this.treeViewProject, itemSelection.AgentGroups, enumItems);
                }
            }
        }

        private void edtKeyword_TextChanged(object sender, EventArgs e)
        {
          
            if (m_groupItems != null && m_enumItems!=null)
            {
                makeTreeView(treeViewProject, m_groupItems, m_enumItems);
            }
        }

        public void UpdateNodeState(List<int> checkedIdSet)
        {
            if (checkedIdSet == null || checkedIdSet.Count == 0)
            {
                return;
            }
            foreach (TreeNode node in this.treeViewProject.Nodes)
            {
                updateNodeState(node, checkedIdSet);
            }
        }

        private void updateNodeState(TreeNode node, List<int> checkedIdSet)
        {
            node.Checked = false;
            if (node.Tag is int)
            {
                int id = (int)node.Tag;
                node.Checked = checkedIdSet.Contains(id);
            }
            foreach (TreeNode subNode in node.Nodes)
            {
                updateNodeState(subNode, checkedIdSet);
            }
        }

    }
}
