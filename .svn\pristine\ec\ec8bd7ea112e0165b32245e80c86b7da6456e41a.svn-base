﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using System.Collections.ObjectModel;
using DevExpress.XtraTreeList.Nodes;
using DevExpress.XtraTreeList;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;

namespace MasterCom.RAMS.Func
{
    public partial class ItemSelectionCellPanel : UserControl
    {
        public ItemSelectionCellPanel()
        {
            InitializeComponent();
            if (!this.DesignMode)
            {
                List<ICell> cellsTemp = new List<ICell>();
                foreach (ICell item in CellManager.GetInstance().GetCurrentCells())
                {
                    cellsTemp.Add(item);
                }
                initData(treeListGSM, gvSelected, cellsTemp, cells);

                cellsTemp.Clear();
                foreach (ICell item in CellManager.GetInstance().GetCurrentTDCells())
                {
                    cellsTemp.Add(item);
                }
                initData(treeListTD, gvTD, cellsTemp, cellsTD);

                cellsTemp.Clear();
                foreach (ICell item in CellManager.GetInstance().GetCurrentLTECells())
                {
                    cellsTemp.Add(item);
                }
                initData(treeListLTE, gvLTE, cellsTemp, cellsLTE);
            }
        }



        public ItemSelectionCellPanel(GridControl gridControl, ToolStripDropDown toolStripDropDownCell, Label lbCellCount)
            :this()
        {
            this.outGridCtrl = gridControl;
            this.dropDownCtrl = toolStripDropDownCell;
            this.lbCellCount = lbCellCount;
        }


        private List<ICell> cells = new List<ICell>();
        private List<ICell> cellsTD = new List<ICell>();
        private List<ICell> cellsLTE = new List<ICell>();
        private ToolStripDropDown dropDownCtrl;
        private GridControl outGridCtrl;
        private Label lbCellCount;

        private void initData(TreeList tl, GridView gv, List<ICell> allCells, List<ICell> cells)
        {
            gv.GridControl.DataSource = cells;
            tl.BeginUpdate();
            tl.Nodes.Clear();
            TreeListNode rootNode = tl.AppendNode(new object[] { "全部" }, null);
            rootNode.Checked = true;
            foreach (ICell cell in allCells)
            {
                TreeListNode subNode = tl.AppendNode(new object[] { cell.Name }, rootNode);
                subNode.Tag = cell;
                subNode.Checked = true;
                cells.Add(cell);
            }
            tl.BestFitColumns();
            tl.EndUpdate();
            MasterCom.Util.DevControlManager.TreeListHelper.ThreeStateControl(tl);
            tl.Tag = gv;
            tl.AfterCheckNode -= treeList_AfterCheckNode;
            tl.AfterCheckNode += treeList_AfterCheckNode;
            gv.Tag = tl;
            gv.DoubleClick += gvSelected_DoubleClick;
            gv.GridControl.RefreshDataSource();
        }

        void gvSelected_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = sender as GridView;
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gv.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            ICell cell = gv.GetRow(info.RowHandle) as ICell;
            List<ICell> cellList = gv.DataSource as List<ICell>;
            cellList.Remove(cell);
            TreeList tl = gv.Tag as TreeList;
            foreach (TreeListNode root in tl.Nodes)
            {
                foreach (TreeListNode node in root.Nodes)
                {
                    if (node.Tag == cell)
                    {
                        node.Checked = false;
                        break;
                    }
                }
            }
            gv.GridControl.RefreshDataSource();
        }

        void treeList_AfterCheckNode(object sender, DevExpress.XtraTreeList.NodeEventArgs e)
        {
            TreeList tl = e.Node.TreeList;
            GridView gv = tl.Tag as GridView;
            List<ICell> cellList = gv.DataSource as List<ICell>;
            TreeListNode root = tl.Nodes[0];
            if (root.CheckState == CheckState.Unchecked)
            {
                cellList.Clear();
            }
            else if (root.CheckState == CheckState.Checked)
            {
                cellList.Clear();
                foreach (TreeListNode sub in root.Nodes)
                {
                    cellList.Add(sub.Tag as ICell);
                }
            }
            else
            {
                ICell cell = e.Node.Tag as ICell;
                if (e.Node.Checked)
                {
                    if (!cellList.Contains(cell))
                    {
                        cellList.Add(cell);
                    }
                }
                else
                {
                    cellList.Remove(cell);
                }
            }
            gv.GridControl.RefreshDataSource();
        }

        private void queryBut_Click(object sender, System.EventArgs e)
        {
            string txt = txtKeyword.Text.Trim().ToLower();
            List<ICell> cellList = new List<ICell>();
            TreeList treeList = null;
            if (tabCtrl.SelectedTab == tabPageGSM)
            {
                treeList = this.treeListGSM;
                addGsmCell(txt, cellList);
            }
            else if (tabPageTD == tabCtrl.SelectedTab)
            {
                treeList = this.treeListTD;
                addTdCell(txt, cellList);
            }
            else
            {
                treeList = this.treeListLTE;
                addLteCell(txt, cellList);
            }
            treeList.BeginUpdate();
            treeList.Nodes.Clear();
            TreeListNode rootNode = treeList.AppendNode(new object[] { "全部" }, null);
            rootNode.Checked = false;
            foreach (ICell cell in cellList)
            {
                TreeListNode subNode = treeList.AppendNode(new object[] { cell.Name }, rootNode);
                subNode.Tag = cell;
                subNode.Checked = false;
            }
            treeList.ExpandAll();
            treeList.EndUpdate();
        }

        private void addGsmCell(string txt, List<ICell> cellList)
        {
            List<Cell> cellGSM = CellManager.GetInstance().GetCurrentCells();
            foreach (Cell c in cellGSM)
            {
                if (txt.Length == 0 || c.Name.IndexOf(txt) != -1)
                {
                    cellList.Add(c);
                }
            }
        }

        private void addTdCell(string txt, List<ICell> cellList)
        {
            List<TDCell> cellTD = CellManager.GetInstance().GetCurrentTDCells();
            foreach (TDCell c in cellTD)
            {
                if (txt.Length == 0 || c.Name.IndexOf(txt) != -1)
                {
                    cellList.Add(c);
                }
            }
        }

        private void addLteCell(string txt, List<ICell> cellList)
        {
            List<LTECell> cellLTE = CellManager.GetInstance().GetCurrentLTECells();
            foreach (LTECell c in cellLTE)
            {
                if (txt.Length == 0 || c.Name.IndexOf(txt) != -1)
                {
                    cellList.Add(c);
                }
            }
        }

        private void btnOk_Click(object sender, System.EventArgs e)
        {
            List<ICell> cellList = null;
            bool isAll = false;
            if (tabCtrl.SelectedTab == tabPageGSM)
            {
                cellList = gvSelected.DataSource as List<ICell>;
                isAll = cellList.Count == CellManager.GetInstance().GetCurrentCells().Count;
                lbCellCount.Text = string.Format("GSM小区 [{0}] {1}", cellList.Count, isAll ? "全部" : "");
            }
            else if (tabPageTD == tabCtrl.SelectedTab)
            {
                cellList = gvTD.DataSource as List<ICell>;
                isAll = cellList.Count == CellManager.GetInstance().GetCurrentTDCells().Count;
                lbCellCount.Text = string.Format("TD小区 [{0}] {1}", cellList.Count, isAll ? "全部" : "");
            }
            else
            {
                cellList = gvLTE.DataSource as List<ICell>;
                isAll = cellList.Count == CellManager.GetInstance().GetCurrentLTECells().Count;
                lbCellCount.Text = string.Format("LTE小区 [{0}] {1}", cellList.Count, isAll ? "全部" : "");
            }
            this.outGridCtrl.Tag = isAll;
            this.outGridCtrl.DataSource = new List<ICell>(cellList);
            if (dropDownCtrl != null)
            {
                dropDownCtrl.Close();
            }
        }

        private void btnReverse_Click(object sender, EventArgs e)
        {
            GridView gv = null;
            if (tabCtrl.SelectedTab == tabPageGSM)
            {
                gv = gvSelected;
            }
            else if (tabPageTD == tabCtrl.SelectedTab)
            {
                gv = gvTD;
            }
            else
            {
                gv = gvLTE;
            }
            List<ICell> cellList = gv.DataSource as List<ICell>;
            TreeList treeList = gv.Tag as TreeList;
            cellList.Clear();
            int allNum = 0;
            int checkedNum = 0;
            treeList.BeginUpdate();
            foreach (TreeListNode root in treeList.Nodes)
            {
                allNum = root.Nodes.Count;
                foreach (TreeListNode node in root.Nodes)
                {
                    if (!node.Checked)
                    {
                        cellList.Add(node.Tag as ICell);
                        checkedNum++;
                    }
                    node.Checked = !node.Checked;
                }
                if (checkedNum == 0)
                {
                    root.Checked = false;
                }
                else if (checkedNum == allNum)
                {
                    root.Checked = true;
                }
                else
                {
                    root.CheckState = CheckState.Indeterminate;
                }
            }
            treeList.ExpandAll();
            treeList.EndUpdate();
            gv.GridControl.RefreshDataSource();
        }

    }
}
