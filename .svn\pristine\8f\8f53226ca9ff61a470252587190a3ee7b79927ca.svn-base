﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.MapControlTool;
using AxMapWinGIS;
using MapWinGIS;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 创建自定义点标记对话框
    /// </summary>
    public partial class PointMarkCreateDlg : Form
    {
        private List<SfLayerInfo> sfLayerList;
        private PointMarkManager pmManager;
        private double longitude;
        private double latitude;
        private AxMap map;
        private MapForm mapForm;

        public PointMarkCreateDlg(double longitude, double latitude, AxMap map, MapForm mapForm)
        {
            InitializeComponent();

            this.longitude = longitude;
            this.latitude = latitude;
            this.labeldata.Text = "经度  " + longitude.ToString() + "\n\n纬度  " + latitude.ToString();
            this.map = map;
            this.mapForm = mapForm;
            this.sfLayerList = this.mapForm.AllLayerList;
            this.pmManager = new PointMarkManager(this.sfLayerList);

            List<string> filesList = this.pmManager.GetExistedFiles();
            foreach (string str in filesList)
            {
                this.cbxTable.Items.Add(this.pmManager.ToShowName(str));
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            string showName = this.cbxTable.Text.Trim();
            string fieldValue = this.cbxField.Text.Trim();

            if (string.IsNullOrEmpty(showName))
            {
                MessageBox.Show(this, "图层不能为空！", "提示", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                DialogResult = DialogResult.Retry;
            }
            else if (fieldValue.Trim().Equals(""))
            {
                MessageBox.Show(this, "字段不能为空！", "提示",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                DialogResult = DialogResult.Retry;
            }
            else if (this.pmManager.GetExistedFields(this.pmManager.ToFileName(showName)).Contains(fieldValue))
            {
                if (MessageBox.Show(this, "图层(" + showName + ")中区域(" + fieldValue + ")已存在，是否覆盖？", 
                    "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.OK)
                {
                    DialogResult = DialogResult.Ignore;
                }
                else
                {
                    DialogResult = DialogResult.Retry;
                    cbxField.Focus();
                }
            }
            else
            {
                DialogResult = DialogResult.OK;
            }

            CreatePointMark(showName, fieldValue);
        }

        private void PointMarkCreateDlg_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (DialogResult == DialogResult.Retry)
            {
                e.Cancel = true;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void cbxTable_SelectedIndexChanged(object sender, EventArgs e)
        {
            cbxField.Items.Clear();

            if (cbxTable.Text.Trim().Equals(""))
            {
                return;
            }

            List<string> strList = this.pmManager.GetExistedFields(this.pmManager.ToFileName(cbxTable.Text.Trim()));
            foreach (string str in strList)
            {
                cbxField.Items.Add(str);
            }
        }

        private void CreatePointMark(string showName, string fieldValue)
        {
            if (this.DialogResult != DialogResult.OK && this.DialogResult != DialogResult.Ignore)
            {
                return;
            }

            string fileName = this.pmManager.ToFileName(showName);
            PointMark point = new PointMark(this.longitude, this.latitude, fieldValue);
            MapWinGIS.Shapefile shpFile = this.pmManager.GetOpenedShapefile(fileName);
            if (shpFile == null) // 如果未打开
            {
                shpFile = this.pmManager.GetNewShapefile(fileName);
                if (shpFile == null)
                {
                    return;
                }
            }

            if (this.DialogResult == DialogResult.Ignore)
            {
                point.Update(shpFile, point);
            }
            else
            {
                point.Insert(shpFile);
            }

            if (this.chkOpenLayer.Checked)
            {
                this.pmManager.RefreshLayer(shpFile, this.map, this.mapForm);
            }
            else
            {
                shpFile.Close();
            }
        }

        private void ToolStripMenuItemCopy_Click(object sender, EventArgs e)
        {
            Clipboard.SetDataObject(labeldata.Text);
        }

        private void labeldata_DoubleClick(object sender, EventArgs e)
        {
            Clipboard.SetDataObject(labeldata.Text);
        }
    } // end class
} // end namespace
