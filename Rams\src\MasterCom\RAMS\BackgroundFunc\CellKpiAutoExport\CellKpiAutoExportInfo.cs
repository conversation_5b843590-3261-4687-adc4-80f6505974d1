﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class CellKpiAutoExportInfo
    {
        public CellKpiAutoExportInfo()
        {
            StatInfo = new CellAutoStatInfo();
        }
        public bool HasFoundFile { get; set; }
        public ICell SrcCell { get; set; }
        public string CellName
        {
            get
            {
                if (SrcCell != null)
                {
                    return SrcCell.Name;
                }
                return null;
            }
        }
        public double? Longitude
        {
            get
            {
                if (SrcCell != null)
                {
                    return SrcCell.Longitude;
                }
                return null;
            }
        }
        public double? Latitude
        {
            get
            {
                if (SrcCell != null)
                {
                    return SrcCell.Latitude;
                }
                return null;
            }
        }
        public int? CellID { get; set; }
        public string BtsName { get; set; }
        public int? BtsID { get; set; }
        public int? Earfcn { get; set; }
        public int? PCI { get; set; }
        public string DistrictName { get; set; }
        public string CellToken
        {
            get
            {
                if (StatInfo != null)
                {
                    return StatInfo.LAC + "_" + StatInfo.CI;
                }
                return "";
            }
        }
        public CellAutoStatInfo StatInfo { get; set; }

        public void SetCurCell(ICell iCell)
        {
            this.SrcCell = iCell;
            if (iCell != null)
            {
                if (iCell is LTECell)
                {
                    LTECell lteCell = iCell as LTECell;
                    this.CellID = lteCell.CellID;
                    this.BtsID = lteCell.BelongBTS.BTSID;
                    this.BtsName = lteCell.BTSName;
                    this.Earfcn = lteCell.EARFCN;
                    this.PCI = lteCell.PCI;
                }
                else if (iCell is Cell)
                {
                    Cell cell = iCell as Cell;
                    this.CellID = cell.ID;
                    this.BtsID = cell.BelongBTS.ID;
                    this.BtsName = cell.BelongBTS.Name;
                    this.Earfcn = cell.BCCH;
                    this.PCI = cell.BSIC;
                }
                else if (iCell is TDCell)
                {
                    TDCell tdCell = iCell as TDCell;
                    this.CellID = tdCell.ID;
                    this.BtsID = tdCell.BelongBTS.ID;
                    this.BtsName = tdCell.BelongBTS.Name;
                    this.Earfcn = tdCell.FREQ;
                    this.PCI = tdCell.CPI;
                }
            }
        }
    }
    public class CellKpiAutoSet
    {
        public CellKpiAutoSet()
        {
            RecentDays = 2;
            ResultSaveTypeIndex = 0;
            IsCheckLteCell = true;
        }
        public bool IsCheckRecentDay { get; set; }
        public int RecentDays { get; set; }
        public bool IsCheckLteCell { get; set; }
        public bool IsCheckTdCell { get; set; }
        public bool IsCheckGsmCell { get; set; }
        public bool IsCheckUnKownCell { get; set; }
        public bool IsCheckCellBasicInfo { get; set; }
        public string SelectedTmpName { get; set; }
        public string ResultSavePath { get; set; }
        public int ResultSaveTypeIndex { get; set; }
    }
}
