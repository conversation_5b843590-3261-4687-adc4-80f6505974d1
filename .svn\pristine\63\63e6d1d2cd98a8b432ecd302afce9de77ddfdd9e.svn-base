﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Drawing.Drawing2D;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class CellCloudPictureCommonSetting : UserControl
    {
        public CellCloudPictureConfig Config { get; set; }
        public CellCloudPictureCommonSetting()
        {
            InitializeComponent();  
            if (!IsDesignMode())
            {
                pictureBox1.Image = new Bitmap(pictureBox1.Width, pictureBox1.Height);
                Config = new CellCloudPictureConfig();
                InitWithConfig();
            }
        }

        private void NumMaxWeight_Changed(object sender, EventArgs e)
        {
            Config.MaxWeight = (double)numMaxWeight.Value;
        }

        private void NumMinWeight_Changed(object sender, EventArgs e)
        {
            Config.MinWeight = (double)numMinWeight.Value;
        }

        private void NumMajorRate_Changed(object sender, EventArgs e)
        {
            Config.MajorSemiAxisRate = (double)numMajorRate.Value;
        }

        private void NumMinorRate_Changed(object sender, EventArgs e)
        {
            Config.MinorSemiAxisRate = (double)numMinorRate.Value;
        }

        private void NumOffsetRate_Changed(object sender, EventArgs e)
        {
            Config.CenterOffsetRate = (double)numOffsetRate.Value;
        }

        private void NumGradientRate_Changed(object sender, EventArgs e)
        {
            Config.GradientOffsetRate = (double)numGradientRate.Value;
        }

        private void NumAlpha_Changed(object sender, EventArgs e)
        {
            Config.AlphaValue = (int)numAlpha.Value;
        }

        private void BtnColorZone_Clicked(object sender, EventArgs e)
        {
            ColorDialog colDlg = new ColorDialog();
            colDlg.FullOpen = true;
            colDlg.CustomColors = Config.ColorZone;
            if (colDlg.ShowDialog() == DialogResult.Cancel || colDlg.CustomColors.Length < 2)
            {
                return;
            }
            List<int> customs = new List<int>();
            foreach (int color in colDlg.CustomColors)
            {
                if (color != ColorTranslator.ToOle(Color.White))
                {
                    customs.Add(color);
                }
            }
            Config.ColorZone = customs.ToArray();
            FillColorZone();
        }

        private void InitWithConfig()
        {
            FillColorZone();
            numMaxWeight.Value = (decimal)Config.MaxWeight;
            numMinWeight.Value = (decimal)Config.MinWeight;
            numMajorRate.Value = (decimal)Config.MajorSemiAxisRate;
            numMinorRate.Value = (decimal)Config.MinorSemiAxisRate;
            numOffsetRate.Value = (decimal)Config.CenterOffsetRate;
            numGradientRate.Value = (decimal)Config.GradientOffsetRate;
            numAlpha.Value = (decimal)Config.AlphaValue;

            numMaxWeight.ValueChanged += NumMaxWeight_Changed;
            numMinWeight.ValueChanged += NumMinWeight_Changed;
            numMajorRate.ValueChanged += NumMajorRate_Changed;
            numMinorRate.ValueChanged += NumMinorRate_Changed;
            numOffsetRate.ValueChanged += NumOffsetRate_Changed;
            numGradientRate.ValueChanged += NumGradientRate_Changed;
            numAlpha.ValueChanged += NumAlpha_Changed;
            btnColorZone.Click += BtnColorZone_Clicked;
        }

        private void FillColorZone()
        {
            RectangleF rectf = new RectangleF(0, 0, 
                (float)pictureBox1.Width / Config.ColorArray.Length, pictureBox1.Height);
            Graphics graphics = Graphics.FromImage(pictureBox1.Image);

            for (int i = 0; i < Config.ColorArray.Length; ++i)
            {
                rectf.X = i * rectf.Width;
                SolidBrush brush = new SolidBrush(Color.FromArgb((int)Config.ColorArray[i]));
                graphics.FillRectangle(brush, rectf);
            }

            pictureBox1.Refresh();
            graphics.Dispose();
        }

        public static bool IsDesignMode()
        {
            bool returnFlag = false;
#if DEBUG
            if (LicenseManager.UsageMode == LicenseUsageMode.Designtime)
            {
                returnFlag = true;
            }
            else if (System.Diagnostics.Process.GetCurrentProcess().ProcessName == "devenv")
            {
                returnFlag = true;
            }
#endif
            return returnFlag;
        }
    }
}
