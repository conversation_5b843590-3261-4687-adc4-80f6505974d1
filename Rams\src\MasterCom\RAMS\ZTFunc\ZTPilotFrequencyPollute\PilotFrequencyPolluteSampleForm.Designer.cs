﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class PilotFrequencyPolluteSampleForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLevSub = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFREQ = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCPI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAltitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDownward = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDirection = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTotalSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBadSampleScale = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAreaName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnArea = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAgentName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCCPCH_C2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDPCH_C2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExport2ExcelPaging = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportTab = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripSeparator();
            this.miViewAll = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnSN);
            this.listViewTotal.AllColumns.Add(this.olvColumnRxLevSub);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellName);
            this.listViewTotal.AllColumns.Add(this.colCellID);
            this.listViewTotal.AllColumns.Add(this.olvColumnLAC);
            this.listViewTotal.AllColumns.Add(this.olvColumnCI);
            this.listViewTotal.AllColumns.Add(this.olvColumnFREQ);
            this.listViewTotal.AllColumns.Add(this.olvColumnCPI);
            this.listViewTotal.AllColumns.Add(this.olvColumnAltitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnDownward);
            this.listViewTotal.AllColumns.Add(this.olvColumnDirection);
            this.listViewTotal.AllColumns.Add(this.olvColumnLongitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnLatitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnTotalSampleCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnSampleCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnBadSampleScale);
            this.listViewTotal.AllColumns.Add(this.olvColumnAreaName);
            this.listViewTotal.AllColumns.Add(this.olvColumnArea);
            this.listViewTotal.AllColumns.Add(this.olvColumnAgentName);
            this.listViewTotal.AllColumns.Add(this.olvColumnPCCPCH_C2I);
            this.listViewTotal.AllColumns.Add(this.olvColumnDPCH_C2I);
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnRxLevSub,
            this.olvColumnCellName,
            this.colCellID,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnFREQ,
            this.olvColumnCPI,
            this.olvColumnAltitude,
            this.olvColumnDownward,
            this.olvColumnDirection,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnTotalSampleCount,
            this.olvColumnSampleCount,
            this.olvColumnBadSampleScale,
            this.olvColumnAreaName,
            this.olvColumnArea,
            this.olvColumnAgentName,
            this.olvColumnPCCPCH_C2I,
            this.olvColumnDPCH_C2I});
            this.listViewTotal.ContextMenuStrip = this.ctxMenu;
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.IsNeedShowOverlay = false;
            this.listViewTotal.Location = new System.Drawing.Point(0, 0);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(1126, 459);
            this.listViewTotal.TabIndex = 2;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            this.listViewTotal.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "Index";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnRxLevSub
            // 
            this.olvColumnRxLevSub.HeaderFont = null;
            this.olvColumnRxLevSub.Text = "平均场强";
            this.olvColumnRxLevSub.Width = 70;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名/道路";
            this.olvColumnCellName.Width = 130;
            // 
            // colCellID
            // 
            this.colCellID.HeaderFont = null;
            this.colCellID.Text = "CellID";
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnFREQ
            // 
            this.olvColumnFREQ.HeaderFont = null;
            this.olvColumnFREQ.Text = "频点";
            // 
            // olvColumnCPI
            // 
            this.olvColumnCPI.HeaderFont = null;
            this.olvColumnCPI.Text = "扰码";
            // 
            // olvColumnAltitude
            // 
            this.olvColumnAltitude.HeaderFont = null;
            this.olvColumnAltitude.Text = "站高";
            // 
            // olvColumnDownward
            // 
            this.olvColumnDownward.HeaderFont = null;
            this.olvColumnDownward.Text = "下倾角";
            // 
            // olvColumnDirection
            // 
            this.olvColumnDirection.HeaderFont = null;
            this.olvColumnDirection.Text = "方向角";
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 80;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 80;
            // 
            // olvColumnTotalSampleCount
            // 
            this.olvColumnTotalSampleCount.HeaderFont = null;
            this.olvColumnTotalSampleCount.Text = "总采样点数";
            this.olvColumnTotalSampleCount.Width = 80;
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "异常采样点数";
            this.olvColumnSampleCount.Width = 80;
            // 
            // olvColumnBadSampleScale
            // 
            this.olvColumnBadSampleScale.HeaderFont = null;
            this.olvColumnBadSampleScale.Text = "异常采样点比例(%)";
            this.olvColumnBadSampleScale.Width = 120;
            // 
            // olvColumnAreaName
            // 
            this.olvColumnAreaName.HeaderFont = null;
            this.olvColumnAreaName.Text = "片区";
            // 
            // olvColumnArea
            // 
            this.olvColumnArea.HeaderFont = null;
            this.olvColumnArea.Text = "网格";
            // 
            // olvColumnAgentName
            // 
            this.olvColumnAgentName.HeaderFont = null;
            this.olvColumnAgentName.Text = "代维";
            // 
            // olvColumnPCCPCH_C2I
            // 
            this.olvColumnPCCPCH_C2I.HeaderFont = null;
            this.olvColumnPCCPCH_C2I.Text = "PCCPCH_C/I平均值";
            this.olvColumnPCCPCH_C2I.Width = 110;
            // 
            // olvColumnDPCH_C2I
            // 
            this.olvColumnDPCH_C2I.HeaderFont = null;
            this.olvColumnDPCH_C2I.Text = "DPCH_C/I平均值";
            this.olvColumnDPCH_C2I.Width = 100;
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.miExport2ExcelPaging,
            this.miExportTab,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll,
            this.toolStripMenuItem2,
            this.miViewAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(162, 148);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(161, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miExport2ExcelPaging
            // 
            this.miExport2ExcelPaging.Name = "miExport2ExcelPaging";
            this.miExport2ExcelPaging.Size = new System.Drawing.Size(161, 22);
            this.miExport2ExcelPaging.Text = "导出Excel(分页)";
            this.miExport2ExcelPaging.Click += new System.EventHandler(this.miExport2ExcelPaging_Click);
            // 
            // miExportTab
            // 
            this.miExportTab.Name = "miExportTab";
            this.miExportTab.Size = new System.Drawing.Size(161, 22);
            this.miExportTab.Text = "导出shp图层...";
            this.miExportTab.Click += new System.EventHandler(this.miExportTab_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(158, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(161, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(161, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(158, 6);
            // 
            // miViewAll
            // 
            this.miViewAll.Name = "miViewAll";
            this.miViewAll.Size = new System.Drawing.Size(161, 22);
            this.miViewAll.Text = "查看所有";
            this.miViewAll.Click += new System.EventHandler(this.miViewAll_Click);
            // 
            // PilotFrequencyPolluteSampleForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1126, 459);
            this.Controls.Add(this.listViewTotal);
            this.Name = "PilotFrequencyPolluteSampleForm";
            this.Text = "导频污染小区分析";
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevSub;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miExportTab;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem miViewAll;
        private BrightIdeasSoftware.OLVColumn olvColumnBadSampleScale;
        private BrightIdeasSoftware.OLVColumn olvColumnTotalSampleCount;
        private System.Windows.Forms.ToolStripMenuItem miExport2ExcelPaging;
        private BrightIdeasSoftware.OLVColumn olvColumnArea;
        private BrightIdeasSoftware.OLVColumn olvColumnFREQ;
        private BrightIdeasSoftware.OLVColumn olvColumnCPI;
        private BrightIdeasSoftware.OLVColumn olvColumnPCCPCH_C2I;
        private BrightIdeasSoftware.OLVColumn olvColumnDPCH_C2I;
        private BrightIdeasSoftware.OLVColumn olvColumnAltitude;
        private BrightIdeasSoftware.OLVColumn olvColumnDownward;
        private BrightIdeasSoftware.OLVColumn olvColumnDirection;
        private BrightIdeasSoftware.OLVColumn colCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnAreaName;
        private BrightIdeasSoftware.OLVColumn olvColumnAgentName;
    }
}