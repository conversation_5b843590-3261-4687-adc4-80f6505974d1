<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolTip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="contextMenuStripFile.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>424, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="toolStripMenu_DIYReplay.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIUAAP///wAAAAD/AFF5Ucj/yCY5JvL/8u3/7S1DLRAYEAUHBRwqHBMdEwoPCvv/+xso
        GwwSDBkmGff/9+T/5DlVOQIDAlaBVpDYkEdrRzxZPAkOCcP/w2SWZAcLBzBHMIbJhgAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAgACwAAAAAEAAQ
        AAAInwBBCBS4IIECBQkWDFwI4gGEAQQMGCAwAMIDhgwQHADAseMBBAwGRkDgAACHDR05OkAQQWCDjQAK
        CKAwIeWBBiAWDOgoU0CHARI6DihIgKeAowIYfOBIwKABo0iPUgBg4OBTjj2jeqCKsCjWqBosBG2qE2qF
        DDU5DgXx8msBrxxvChxZEsOFlABWthSYEaZNkAwdQpRI0SLDgQUPJmQYEAA7
</value>
  </data>
  <data name="toolStripMenuItemStat.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAAAAJ6em///0D8/Pp2dm8DAirKysf//y2FhJoaGeLW1fj4+C8rKXjo6LLa2s6+v
        gnl5O0BAB7y8wbe3tbi4gImJOcbGWs7OUIGBSaurUWFhHaenI42NO5eXPeXlbGVlBoSENdzcgpigcJSZ
        f5KWgJycmdPSmMTE0f//yl1dALy8aevrZvT0tuLhqmhnQ/79xv/+xvz7uc/PQpubmnV1S+joX2trO/X1
        2Pn5zeHhsff3u/Pzt/n5zDIyM0dHTysrACEhAEVFJ0BASkJCEVpaIDc3FERETjIyK0ZGK6KiW2ZmCVNT
        Fa+vcTw8HpqaAP7+y8LCjHp6Zra2OWNjSru7Q9HRc8HBMWRkB9fXR2dnLNjYnNvbu9jYtf//x93dovn5
        v///uP//tvz9uvz9wLy8icbGxuPjnXBwdEZGQjExMaGkqNDQp6ytnLCsl6eiovz80pSUavPztYiIhGBg
        VjQ0Nf//xvf3zZyct+fn0Lm5ooeHaTU1Oe/vkdLSzuDg0NLSwuDgkOrq0a6ucZKScv7+w8TEpzMzK///
        wP39xfDwrUNDQ2lpYtPWwt/ev4lydY5wdpFwdmtrf5CChateWqOmbZqamP//0SYmDZaWJ2VkbJiebomN
        kbGxXGVMVpJjaJmeZZmZl+7ui6ZpaO3suoGBn83SwKNlaaCEiiYmA7S0doeHjP//2CsrBaenpfLwvYKC
        nZeXlZgyL5ZXXNHbwoyLj6JSVpZHR/z80JqaYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAC5ACwAAAAAEAAQAAAI7wBzCcyV4ESoPnz8APqzZ6DDO3bw
        6MmD6FCiRYocCjQkKNCgQoTqmClzJo1GNGTAfAkzRkwcOHLoDJwj8M0aNWzctFGValWrgaxQkRpV6pQp
        W7Vo4boFS+MrV7NkxRI1SRIlS5U0RmrEyBGkR546fRIAyiEnTJcybdI0QgQJEwJKCAzRgYMHEB9ctHgR
        AwYLFAEEplihYgODBQ0eOFBgoMABArkQXLCAQUOGChEgSKAwAcoTyFGmSHGiJMmSJky2aOHipUvgXFmq
        ULGC5QqSHDh08NhxY4ZAGTRs1CAypMgRIwOEDPDRw+GPIEA0agwIADs=
</value>
  </data>
  <metadata name="contextMenuStripMarkFile.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>216, 17</value>
  </metadata>
  <data name="tsmiMarkFile.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAA6vAAAOrwFxEUPuAAACG0lE
        QVQ4T4WQzWsTURTF4yAlARFM0YUgWMGNIrgV/RMq7lzYhZvuXKq4EIS6ECsILkQkhVqiMUw6BscQQ5OY
        mKgY0yi0aYM1358khDiN1RZFOL57ZyZjDcUDhzvvzTk/Ls9mKpFIPEgmk9jBv8X/N2LeEXPMqGyX+IGd
        FAwGUSoWkU6nGRiPx88YNUviksP1Wg2VSgWlUgmFfB5f1tYY0O12sZLNIrO4SACPUbMUi8UY0Go2Ua/X
        URWQMkEKBQaQcrkclpeWILJVo2YpGo1yqN1uo9VqoUGQapUhJoBUExuK7E+jZikcDnNg65nE/hE8Au3t
        RTTy71EulxlCjkQiPI2apVAoxIANj7TNfeUQmp9foSIg9JB58S6UNWqWzDX7/T605ip6y2705OP4OiOh
        N+cUm3zE09ePcGLWgdvuqWFAIBBgwOa8hE31AL5l76LXaaDjOYXOPQnKk3M4dt+BS8WTPMcuO84aVV2q
        qjJgfVYaWH0+gaO3HJCnRnhSefLTKE86G1Vdfr+fAVvfNaynpqFMj+DwVb1kzgvv9rHpPLSBoigM+LVy
        BaGFCRyctGN8YS+byuY33d+cuT78Bj6fjwFaag5aRsbLD4+x/7wdp317BqazP+UFZY3aQJIsywz4W9HV
        IJzjdn55mi8y89A0DZSljl7Vtdvlcrm9Xi/+9Y2H17hM07yjLHX0qq5dwnZhp/Dof0wZyoqOzfYH0TUh
        gRvlpPIAAAAASUVORK5CYII=
</value>
  </data>
  <data name="toolStripMenuItemDownload.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIEAAAAAAICAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAACACwAAAAAEAAQ
        AAAISAAFCBxIsGBBAAgTIjQoEECAhxABMBTgEOJDiQwrWsRoUGPEiR4vElRoMaJCiiVTBuAYsiTHhipX
        Zkz58uDGiQMr1gS5E+fEgAA7
</value>
  </data>
  <data name="toolStripMenuItemDelete.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAKUAGOcAGO8AEPcACP8AAP//////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        /yH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIUwABCBxIsGBBAQYRJjSgUKAAAwEO
        Gpio8OHEiA4nahRgUSNGAAE0ivRYMOTIiwZBnvwocSTLgR1HNsx4ciNMlyZtCsyJkWdJiARDvtxpcGjK
        lAEBADs=
</value>
  </data>
  <data name="buttonStat.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAAAAJ6em///0D8/Pp2dm8DAirKysf//y2FhJoaGeLW1fj4+C8rKXjo6LLa2s6+v
        gnl5O0BAB7y8wbe3tbi4gImJOcbGWs7OUIGBSaurUWFhHaenI42NO5eXPeXlbGVlBoSENdzcgpigcJSZ
        f5KWgJycmdPSmMTE0f//yl1dALy8aevrZvT0tuLhqmhnQ/79xv/+xvz7uc/PQpubmnV1S+joX2trO/X1
        2Pn5zeHhsff3u/Pzt/n5zDIyM0dHTysrACEhAEVFJ0BASkJCEVpaIDc3FERETjIyK0ZGK6KiW2ZmCVNT
        Fa+vcTw8HpqaAP7+y8LCjHp6Zra2OWNjSru7Q9HRc8HBMWRkB9fXR2dnLNjYnNvbu9jYtf//x93dovn5
        v///uP//tvz9uvz9wLy8icbGxuPjnXBwdEZGQjExMaGkqNDQp6ytnLCsl6eiovz80pSUavPztYiIhGBg
        VjQ0Nf//xvf3zZyct+fn0Lm5ooeHaTU1Oe/vkdLSzuDg0NLSwuDgkOrq0a6ucZKScv7+w8TEpzMzK///
        wP39xfDwrUNDQ2lpYtPWwt/ev4lydY5wdpFwdmtrf5CChateWqOmbZqamP//0SYmDZaWJ2VkbJiebomN
        kbGxXGVMVpJjaJmeZZmZl+7ui6ZpaO3suoGBn83SwKNlaaCEiiYmA7S0doeHjP//2CsrBaenpfLwvYKC
        nZeXlZgyL5ZXXNHbwoyLj6JSVpZHR/z80JqaYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAC5ACwAAAAAEAAQAAAI7wBzCcyV4ESoPnz8APqzZ6DDO3bw
        6MmD6FCiRYocCjQkKNCgQoTqmClzJo1GNGTAfAkzRkwcOHLoDJwj8M0aNWzctFGValWrgaxQkRpV6pQp
        W7Vo4boFS+MrV7NkxRI1SRIlS5U0RmrEyBGkR546fRIAyiEnTJcybdI0QgQJEwJKCAzRgYMHEB9ctHgR
        AwYLFAEEplihYgODBQ0eOFBgoMABArkQXLCAQUOGChEgSKAwAcoTyFGmSHGiJMmSJky2aOHipUvgXFmq
        ULGC5QqSHDh08NhxY4ZAGTRs1CAypMgRIwOEDPDRw+GPIEA0agwIADs=
</value>
  </data>
  <data name="button_compareReplay.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIUAAP///wAAAAD/AFF5Ucj/yCY5JvL/8u3/7S1DLRAYEAUHBRwqHBMdEwoPCvv/+xso
        GwwSDBkmGff/9+T/5DlVOQIDAlaBVpDYkEdrRzxZPAkOCcP/w2SWZAcLBzBHMIbJhgAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAgACwAAAAAEAAQ
        AAAInwBBCBS4IIECBQkWDFwI4gGEAQQMGCAwAMIDhgwQHADAseMBBAwGRkDgAACHDR05OkAQQWCDjQAK
        CKAwIeWBBiAWDOgoU0CHARI6DihIgKeAowIYfOBIwKABo0iPUgBg4OBTjj2jeqCKsCjWqBosBG2qE2qF
        DDU5DgXx8msBrxxvChxZEsOFlABWthSYEaZNkAwdQpRI0SLDgQUPJmQYEAA7
</value>
  </data>
  <data name="btnDIYReplay.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIUAAP///wAAAAD/AFF5Ucj/yCY5JvL/8u3/7S1DLRAYEAUHBRwqHBMdEwoPCvv/+xso
        GwwSDBkmGff/9+T/5DlVOQIDAlaBVpDYkEdrRzxZPAkOCcP/w2SWZAcLBzBHMIbJhgAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAgACwAAAAAEAAQ
        AAAInwBBCBS4IIECBQkWDFwI4gGEAQQMGCAwAMIDhgwQHADAseMBBAwGRkDgAACHDR05OkAQQWCDjQAK
        CKAwIeWBBiAWDOgoU0CHARI6DihIgKeAowIYfOBIwKABo0iPUgBg4OBTjj2jeqCKsCjWqBosBG2qE2qF
        DDU5DgXx8msBrxxvChxZEsOFlABWthSYEaZNkAwdQpRI0SLDgQUPJmQYEAA7
</value>
  </data>
  <data name="buttonDelete.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAKUAGOcAGO8AEPcACP8AAP//////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        /yH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIUwABCBxIsGBBAQYRJjSgUKAAAwEO
        Gpio8OHEiA4nahRgUSNGAAE0ivRYMOTIiwZBnvwocSTLgR1HNsx4ciNMlyZtCsyJkWdJiARDvtxpcGjK
        lAEBADs=
</value>
  </data>
  <data name="buttonDownload.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIEAAAAAAICAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAACACwAAAAAEAAQ
        AAAISAAFCBxIsGBBAAgTIjQoEECAhxABMBTgEOJDiQwrWsRoUGPEiR4vElRoMaJCiiVTBuAYsiTHhipX
        Zkz58uDGiQMr1gS5E+fEgAA7
</value>
  </data>
  <metadata name="toolStripDropDownRegion.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>602, 17</value>
  </metadata>
  <metadata name="toolTipEx.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>110, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>56</value>
  </metadata>
  <data name="FileInfoForm.Appearance.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAP///yZLgqGwxqfH6leX4EN4ukyFyv39/aW0y1iW4FaU3VeT3Pr6+uj1
        +Jmvyuz2+pywye32+leS2Pz8/JGsytzw+tzw+5Ksydzv+gAAAFeR2FeQ1tnr/Nnv/GeNvXup3gAAAFiR
        1liQ1I2qyo6qytXr+9Xs+wAAAFiP1FiP09Dq+4upyoypytLq+3mo3nio3liO0wAAAAAAAAAAAAAAAFeM
        0FuJwGCNwpK34szm+wAAAFiNz1GAvvX6/meUy8rl+1eMzvz+/2GQyFSFxU98t058twAAAKi20AAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIiQABCBxIsKDBgwYLGFi4cMABhggK
        DDSQoKLFiwkWDGSAsSODgQ0cgBhJEoZJBw0GUqhwoSUGlxouOOAwsEOFkjBC6AxRYcRAEglOCB16AgWK
        BCoGrkgQo6lTpwlkDLyRYIfVq1cF9Bjo44fJr08FCCHoYwgRAWjToi1S0MgRI3Djwk2CsK5dgwEBADs=
</value>
  </data>
</root>