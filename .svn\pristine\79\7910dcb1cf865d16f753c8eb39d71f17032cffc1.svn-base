﻿using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    public class LongitudeLatitude
    {
        public int ILongitude { get; set; }
        public int ILatitude { get; set; }

        public double DLongitude { get; set; }
        public double DLatitude { get; set; }

        public void FillIntData(Package package)
        {
            ILongitude = package.Content.GetParamInt();
            ILatitude = package.Content.GetParamInt();
        }

        public void FillIntDataWithTransfer(Package package, double multiple = 10000000)
        {
            FillIntData(package);
            IntToDouble(multiple);
        }

        public void FillDoubleData(Package package)
        {
            DLongitude = package.Content.GetParamDouble();
            DLatitude = package.Content.GetParamDouble();
        }

        public void FillDoubleDataWithTransfer(Package package, double multiple = 10000000)
        {
            FillDoubleData(package);
            DoubleToInt(multiple);
        }

        public void IntToDouble(double multiple)
        {
            DLongitude = ILongitude / multiple;
            DLatitude = ILatitude / multiple;
        }

        public void DoubleToInt(double multiple)
        {
            ILongitude = (int)(DLongitude * multiple);
            ILatitude = (int)(DLongitude * multiple);
        }

        public double GetDistance(LongitudeLatitude info)
        {
            return MathFuncs.GetDistance(DLongitude, DLatitude, info.DLongitude, info.DLatitude);
        }
    }
}
