﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class GSMPoorRxQualityRoadListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(GSMPoorRxQualityRoadListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewRoad = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDitance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxRxQuality = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinRxQuality = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRxQuality = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFirstTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLastTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCells = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellNames = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGridName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAreaAgentName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoad)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(153, 98);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(149, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(152, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Visible = false;
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(152, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Visible = false;
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewRoad
            // 
            this.ListViewRoad.AllColumns.Add(this.olvColumnSN);
            this.ListViewRoad.AllColumns.Add(this.olvColumnRoadName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnDitance);
            this.ListViewRoad.AllColumns.Add(this.olvColumnSample);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxRxQuality);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinRxQuality);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAvgRxQuality);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxRxlev);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinRxlev);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAvgRxlev);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAvgC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnLongitudeMid);
            this.ListViewRoad.AllColumns.Add(this.olvColumnLatitudeMid);
            this.ListViewRoad.AllColumns.Add(this.olvColumnFileName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnFirstTime);
            this.ListViewRoad.AllColumns.Add(this.olvColumnLastTime);
            this.ListViewRoad.AllColumns.Add(this.olvColumnCells);
            this.ListViewRoad.AllColumns.Add(this.olvColumnCellNames);
            this.ListViewRoad.AllColumns.Add(this.olvColumnGridName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAreaAgentName);
            this.ListViewRoad.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnRoadName,
            this.olvColumnDitance,
            this.olvColumnSample,
            this.olvColumnMaxRxQuality,
            this.olvColumnMinRxQuality,
            this.olvColumnAvgRxQuality,
            this.olvColumnMaxRxlev,
            this.olvColumnMinRxlev,
            this.olvColumnAvgRxlev,
            this.olvColumnMaxC2I,
            this.olvColumnMinC2I,
            this.olvColumnAvgC2I,
            this.olvColumnLongitudeMid,
            this.olvColumnLatitudeMid,
            this.olvColumnFileName,
            this.olvColumnFirstTime,
            this.olvColumnLastTime,
            this.olvColumnCells,
            this.olvColumnCellNames,
            this.olvColumnGridName,
            this.olvColumnAreaAgentName});
            this.ListViewRoad.ContextMenuStrip = this.ctxMenu;
            this.ListViewRoad.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewRoad.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewRoad.FullRowSelect = true;
            this.ListViewRoad.GridLines = true;
            this.ListViewRoad.HeaderWordWrap = true;
            this.ListViewRoad.IsNeedShowOverlay = false;
            this.ListViewRoad.Location = new System.Drawing.Point(0, 0);
            this.ListViewRoad.Name = "ListViewRoad";
            this.ListViewRoad.OwnerDraw = true;
            this.ListViewRoad.ShowGroups = false;
            this.ListViewRoad.Size = new System.Drawing.Size(1114, 502);
            this.ListViewRoad.TabIndex = 5;
            this.ListViewRoad.UseCompatibleStateImageBehavior = false;
            this.ListViewRoad.View = System.Windows.Forms.View.Details;
            this.ListViewRoad.VirtualMode = true;
            this.ListViewRoad.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 40;
            // 
            // olvColumnRoadName
            // 
            this.olvColumnRoadName.HeaderFont = null;
            this.olvColumnRoadName.Text = "道路名称";
            this.olvColumnRoadName.Width = 120;
            // 
            // olvColumnDitance
            // 
            this.olvColumnDitance.HeaderFont = null;
            this.olvColumnDitance.Text = "距离(米)";
            // 
            // olvColumnSample
            // 
            this.olvColumnSample.HeaderFont = null;
            this.olvColumnSample.Text = "采样点数";
            // 
            // olvColumnMaxRxQuality
            // 
            this.olvColumnMaxRxQuality.HeaderFont = null;
            this.olvColumnMaxRxQuality.Text = "最大RxQuality";
            this.olvColumnMaxRxQuality.Width = 80;
            // 
            // olvColumnMinRxQuality
            // 
            this.olvColumnMinRxQuality.HeaderFont = null;
            this.olvColumnMinRxQuality.Text = "最小RxQuality";
            this.olvColumnMinRxQuality.Width = 80;
            // 
            // olvColumnAvgRxQuality
            // 
            this.olvColumnAvgRxQuality.HeaderFont = null;
            this.olvColumnAvgRxQuality.Text = "平均RxQuality";
            this.olvColumnAvgRxQuality.Width = 80;
            // 
            // olvColumnMaxRxlev
            // 
            this.olvColumnMaxRxlev.HeaderFont = null;
            this.olvColumnMaxRxlev.Text = "最大场强";
            this.olvColumnMaxRxlev.Width = 80;
            // 
            // olvColumnMinRxlev
            // 
            this.olvColumnMinRxlev.HeaderFont = null;
            this.olvColumnMinRxlev.Text = "最小场强";
            this.olvColumnMinRxlev.Width = 80;
            // 
            // olvColumnAvgRxlev
            // 
            this.olvColumnAvgRxlev.HeaderFont = null;
            this.olvColumnAvgRxlev.Text = "平均场强";
            this.olvColumnAvgRxlev.Width = 80;
            // 
            // olvColumnMaxC2I
            // 
            this.olvColumnMaxC2I.HeaderFont = null;
            this.olvColumnMaxC2I.Text = "最大C/I";
            this.olvColumnMaxC2I.Width = 80;
            // 
            // olvColumnMinC2I
            // 
            this.olvColumnMinC2I.HeaderFont = null;
            this.olvColumnMinC2I.Text = "最小C/I";
            this.olvColumnMinC2I.Width = 80;
            // 
            // olvColumnAvgC2I
            // 
            this.olvColumnAvgC2I.HeaderFont = null;
            this.olvColumnAvgC2I.Text = "平均C/I";
            this.olvColumnAvgC2I.Width = 80;
            // 
            // olvColumnLongitudeMid
            // 
            this.olvColumnLongitudeMid.HeaderFont = null;
            this.olvColumnLongitudeMid.Text = "中心经度";
            this.olvColumnLongitudeMid.Width = 80;
            // 
            // olvColumnLatitudeMid
            // 
            this.olvColumnLatitudeMid.HeaderFont = null;
            this.olvColumnLatitudeMid.Text = "中心纬度";
            this.olvColumnLatitudeMid.Width = 80;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            // 
            // olvColumnFirstTime
            // 
            this.olvColumnFirstTime.HeaderFont = null;
            this.olvColumnFirstTime.Text = "开始时间";
            this.olvColumnFirstTime.Width = 100;
            // 
            // olvColumnLastTime
            // 
            this.olvColumnLastTime.HeaderFont = null;
            this.olvColumnLastTime.Text = "结束时间";
            this.olvColumnLastTime.Width = 100;
            // 
            // olvColumnCells
            // 
            this.olvColumnCells.HeaderFont = null;
            this.olvColumnCells.Text = "占用小区";
            // 
            // olvColumnCellNames
            // 
            this.olvColumnCellNames.HeaderFont = null;
            this.olvColumnCellNames.Text = "占用小区名";
            // 
            // olvColumnGridName
            // 
            this.olvColumnGridName.HeaderFont = null;
            this.olvColumnGridName.Text = "网格归属";
            // 
            // olvColumnAreaAgentName
            // 
            this.olvColumnAreaAgentName.HeaderFont = null;
            this.olvColumnAreaAgentName.Text = "代维分区";
            // 
            // GSMPoorRxQualityRoadListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1114, 502);
            this.Controls.Add(this.ListViewRoad);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "GSMPoorRxQualityRoadListForm";
            this.Text = "质差路段";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoad)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewRoad;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnDitance;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnSample;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitudeMid;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitudeMid;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnMinC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRxQuality;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRxQuality;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRxQuality;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstTime;
        private BrightIdeasSoftware.OLVColumn olvColumnLastTime;
        private BrightIdeasSoftware.OLVColumn olvColumnCells;
        private BrightIdeasSoftware.OLVColumn olvColumnCellNames;
        private BrightIdeasSoftware.OLVColumn olvColumnGridName;
        private BrightIdeasSoftware.OLVColumn olvColumnAreaAgentName;

    }
}