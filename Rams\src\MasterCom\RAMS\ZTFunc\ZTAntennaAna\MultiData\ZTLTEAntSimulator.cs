﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using System.Threading;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteAntSimulator : ZTAntennaBase
    {
        public ZTLteAntSimulator()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            carrierID = CarrierType.ChinaMobile;
            ServiceTypes.Clear();
            iLastCityID = MainModel.GetInstance().DistrictID;
        }

        public override string Name
        {
            get { return "LTE天线权值模拟器"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 28000, 28013, this.Name);
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            return null;
        }
        protected override bool isValidCondition()
        {
            return true;
        }

        readonly AntTimeCfg timeCfg = new AntTimeCfg();
        private static ZTLteAntSimulator instance = null;
        protected static readonly object lockObj = new object();

        //天线权值数据
        Dictionary<int, AntennaPara> antParaEciDic = new Dictionary<int, AntennaPara>();
        Dictionary<int, AntennaPara> antParaEciSDic = new Dictionary<int, AntennaPara>();
        Dictionary<string, AntennaPara> antParaCellNameDic = new Dictionary<string, AntennaPara>();

        //天线权值评估数据
        Dictionary<int, AntennaPara> antAssessEciDic = new Dictionary<int, AntennaPara>();
        Dictionary<int, AntennaPara> antAssessEciSDic = new Dictionary<int, AntennaPara>();
        Dictionary<string, AntennaPara> antAssessCellNameDic = new Dictionary<string, AntennaPara>();

        //LTE小区权值配置
        readonly Dictionary<LTECell, AntParaArray> lteCellParaDic = new Dictionary<LTECell, AntParaArray>();
        private int iLastCityID;

        public static ZTLteAntSimulator GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLteAntSimulator();
                    }
                }
            }
            return instance;
        }

        private void ClearData()
        {
            antAssessEciDic.Clear();
            antAssessEciSDic.Clear();
            antAssessCellNameDic.Clear();
        }

        protected override void query()
        {
            lteCellParaDic.Clear();
            setVarBeforQuery();
            InitRegionMop2();

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.ClearDTData();

                WaitBox.CanCancel = true;

                timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Now) / 1000L);
                timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Now) / 1000L);

                if (iLastCityID != MainModel.GetInstance().DistrictID)
                {
                    //切换地市重新加载权值
                    antParaEciDic.Clear();
                    antParaEciSDic.Clear();
                    antParaCellNameDic.Clear();
                    iLastCityID = MainModel.GetInstance().DistrictID;
                }
                if (antParaEciDic.Count == 0 || antParaEciSDic.Count == 0 || antParaCellNameDic.Count == 0)
                {
                    WaitBox.Show("加载小区权值数据...", doWithParaData);
                }
                WaitBox.Show("加载小区权值评估数据...", doWithAssessData);
                WaitBox.Show("获取区域内的小区信息...", getCellInfoByRegion);
                MainModel.FireSetDefaultMapSerialTheme("LTE", "RSRP");
            }
            catch
            {
                clientProxy.Close();
            }
            finally
            {
                ClearData();
                MainModel.ClearDTData();
                ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic = null;
                FireShowResultForm();
            }
        }

        /// <summary>
        /// 获取区域内的小区信息
        /// </summary>
        private void getCellInfoByRegion()
        {
            List<LTECell> lteCells = MainModel.CellManager.GetCurrentLTECells();
            WaitBox.ProgressPercent = 50;
            foreach (LTECell lteCell in lteCells)
            {
                if (lteCell.BandType == LTEBandType.E || lteCell.BandType == LTEBandType.A || lteCell.BandType == LTEBandType.Undefined)
                    continue;

                if (lteCell.Type == LTEBTSType.Indoor)
                    continue;

                string strGridTypeName = "";
                isContainPoint(lteCell.Longitude, lteCell.Latitude, ref strGridTypeName);
                if (strGridTypeName != "")
                {
                    AntennaPara antPara, antAssess;
                    getAntennaInfo(lteCell, out antPara, out antAssess);
                    AntParaArray apa = new AntParaArray();
                    apa.antParaOrg = antPara;
                    apa.antParaNew = antAssess;
                    lteCellParaDic.Add(lteCell, apa);
                }
            }
            Thread.Sleep(1000);
            WaitBox.Close();
        }

        private void getAntennaInfo(LTECell lteCell, out AntennaPara antPara, out AntennaPara antAssess)
        {
            if (!antParaEciDic.TryGetValue(lteCell.ECI, out antPara)
                && !antParaCellNameDic.TryGetValue(lteCell.Name, out antPara))
            {
                antPara = new AntennaPara();
            }
            if (!antAssessEciDic.TryGetValue(lteCell.ECI, out antAssess)
                && !antAssessCellNameDic.TryGetValue(lteCell.Name, out antAssess))
            {
                antAssess = new AntennaPara();
            }
        }

        /// <summary>
        /// 查询小区参数数据
        /// </summary>
        private void doWithParaData()
        {
            WaitBox.CanCancel = true;
            try
            {
                WaitBox.ProgressPercent = 20;
                DiyAntennaPara antPara = new DiyAntennaPara(MainModel, timeCfg);
                antPara.Query();
                antParaEciDic = antPara.antParaEciDic;
                antParaEciSDic = antPara.antParaEciSDic;
                antParaCellNameDic = antPara.antParaCellNameDic;
            }
            catch (Exception exp)
            {
                log.Error(exp.Message);
            }
            WaitBox.Close();
        }

        /// <summary>
        /// 查询小区评估数据
        /// </summary>
        private void doWithAssessData()
        {
            WaitBox.CanCancel = true;
            try
            {
                WaitBox.ProgressPercent = 40;
                DiyAntGetSimulatorAssess antPara = new DiyAntGetSimulatorAssess(MainModel);
                antPara.Query();
                antAssessEciDic = antPara.antParaEciDic;
                antAssessEciSDic = antPara.antParaEciSDic;
                antAssessCellNameDic = antPara.antParaCellNameDic;
            }
            catch (Exception exp)
            {
                log.Error(exp.Message);
            }
            WaitBox.Close();
        }
        protected override void fillContentNeeded_Sample(Package package)
        {
            List<ColumnDefItem> colDefList = getNeededColumnDefList(curSelDIYSampleGroup);
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,1,52,0,1,53,");//isampleid
            for (int i = 0; i < colDefList.Count; i++)
            {
                ColumnDefItem sid = colDefList[i];
                sbuilder.Append(sid.GetTriIdStr());
                if (i < colDefList.Count - 1)
                {
                    sbuilder.Append(",");
                }
            }
            package.Content.AddParam(sbuilder.ToString());
        }

        ///<summary>
        ///显示结果窗体
        ///</summary>
        private void FireShowResultForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteAntennaForm).FullName);
            LteAntSimulatorForm form = obj == null ? null : obj as LteAntSimulatorForm;
            if (form == null || form.IsDisposed)
            {
                form = new LteAntSimulatorForm(MainModel);
            }
            form.FillData(lteCellParaDic);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        public class AntParaArray
        {
            public AntParaArray()
            {
                antParaOrg = new AntennaPara();
                antParaNew = new AntennaPara();
                antParaBest = new AntennaPara();
                PicBox = new PictureBox();
                cGroupList = new List<ControlGroupSingle>();
            }

            public AntennaPara antParaOrg { get; set; }
            public AntennaPara antParaNew { get; set; }
            public AntennaPara antParaBest { get; set; }

            public PictureBox PicBox { get; set; }
            public List<ControlGroupSingle> cGroupList { get; set; }
            public void fillControlData(List<ControlGroup> lsCtrll)
            {
                cGroupList.Clear();
                foreach (ControlGroup ctrl in lsCtrll)
                {
                    ControlGroupSingle ctrlSingle = new ControlGroupSingle();
                    ctrlSingle.fillControlData(ctrl);
                    cGroupList.Add(ctrlSingle);
                }
            }
        }

        public class ControlGroupSingle
        {
            public float fRange { get; set; }
            public float fPhase { get; set; }
            public int iSourcePortNum { get; set; }
            public ControlGroupSingle()
            {
                this.fRange = 0;
                this.fPhase = 0;
                this.iSourcePortNum = 0;
            }
            public void fillControlData(ControlGroup ctrl)
            {
                this.fRange = ctrl.fRange;
                this.fPhase = ctrl.fPhase;
                this.iSourcePortNum = ctrl.iSourcePortNum;
            }
        }
        public class CellModelInfo
        {
            public CellModelInfo()
            {
                powerArray = new double[180];
                dDownward = 0;
            }

            public double[] powerArray { get; set; }
            public double dDownward { get; set; }
        }
    }
}
