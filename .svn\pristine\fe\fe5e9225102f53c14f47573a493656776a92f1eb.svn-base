﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryFddPlanData : DiyQueryFddDataBase
    {
        private List<FddPlanBtsCellData> planBtsCellInfo = null;
        public List<FddPlanBtsCellData> PlanBtsCellInfo
        { get { return planBtsCellInfo; } }

        public DiyQueryFddPlanData()
            : base()
        { }

        public override string Name
        {
            get
            {
                return "查询FDD单验规划数据";
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat("SELECT [小区名称],[站名],[经度],[纬度],[总下倾角],[天线挂高],[方位角],[预制电下倾角],[机械下倾角],[天线类型],[天线增益],[TAC],[EnodebID],[小区载波配置],[CellID],[PCI],[频段],[主频点],[小区带宽],[根序列],[覆盖面积],[覆盖范围],[设备类型],[站型],[合路方式],[单双路],[覆盖场景] FROM tb_xinjiang_cellparam_plan where [EnodebID]={0} and [站名]='{1}'", btsID, btsName);
            if (btstype == LTEBTSType.Outdoor)
            {
                selectSQL.Append(" order by [CellID]");
            }
            else
            {
                selectSQL.Append(" order by [PCI]");
            }
            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[27];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void initData()
        {
            planBtsCellInfo = new List<FddPlanBtsCellData>();
        }

        protected override void dealReceiveData(Package package)
        {
            FddPlanBtsCellData btsCell = new FddPlanBtsCellData();
            btsCell.FillData(package);
            planBtsCellInfo.Add(btsCell);
        }
    }

    public class FddPlanBtsCellData : BtsCellData
    {
        #region 小区信息
        public string CellName { get; private set; }
        public int CellID { get; private set; }
        /// <summary>
        /// 载波配置
        /// </summary>
        public string CarrierInfo { get; private set; }
        public int PCI { get; private set; }
        /// <summary>
        /// 频段
        /// </summary>
        public string FrequencyBand { get; private set; }
        /// <summary>
        /// 主频点
        /// </summary>
        public int Earfcn { get; private set; }
        /// <summary>
        /// 小区宽带
        /// </summary>
        public string CellBroadBand { get; private set; }
        /// <summary>
        /// 根序列
        /// </summary>
        public int RootSN { get; private set; }
        /// <summary>
        /// 挂高
        /// </summary>
        public decimal Altitude { get; private set; }
        /// <summary>
        /// 下倾角
        /// </summary>
        public int Downward { get; private set; }
        /// <summary>
        /// 方位角
        /// </summary>
        public int Direction { get; private set; }
        /// <summary>
        /// 电气下倾角
        /// </summary>
        public int Downtilt { get; private set; }
        /// <summary>
        /// 机械下倾角
        /// </summary>
        public int MechanicalTilt { get; private set; }
        /// <summary>
        /// 天线类型
        /// </summary>
        public string AntennaType { get; private set; }
        /// <summary>
        /// 天线增益
        /// </summary>
        public string AntennaGain { get; private set; }
        /// <summary>
        /// 覆盖面积
        /// </summary>
        public int CoverArea { get; private set; }
        /// <summary>
        /// 覆盖范围
        /// </summary>
        public string CoverRange { get; private set; }
        /// <summary>
        /// 设备类型
        /// </summary>
        public string DeviceType { get; private set; }
        /// <summary>
        /// 站型
        /// </summary>
        public string BtsType { get; private set; }
        /// <summary>
        /// 合路方式
        /// </summary>
        public string Combiner { get; private set; }
        /// <summary>
        /// 单双路
        /// </summary>
        public string SingleDouble { get; private set; }
        /// <summary>
        /// 覆盖场景
        /// </summary>
        public string CoverScenes { get; private set; }
        #endregion

        public override void FillData(Package package)
        {
            CellName = package.Content.GetParamString();
            BtsName = package.Content.GetParamString();
            longitude = package.Content.GetParamInt();
            latitude = package.Content.GetParamInt();
            Downward = package.Content.GetParamInt();
            Altitude = (decimal)(package.Content.GetParamInt() / 1000d);
            Direction = package.Content.GetParamInt();
            Downtilt = package.Content.GetParamInt();
            MechanicalTilt = package.Content.GetParamInt();
            AntennaType = package.Content.GetParamString();
            AntennaGain = package.Content.GetParamString();
            TAC = package.Content.GetParamInt();
            ENodeBID = package.Content.GetParamInt();
            CarrierInfo = package.Content.GetParamString();
            CellID = package.Content.GetParamInt();
            PCI = package.Content.GetParamInt();
            FrequencyBand = package.Content.GetParamString();
            Earfcn = package.Content.GetParamInt();
            CellBroadBand = package.Content.GetParamString();
            RootSN = package.Content.GetParamInt();

            CoverArea = package.Content.GetParamInt();
            CoverRange = package.Content.GetParamString();
            DeviceType = package.Content.GetParamString();
            BtsType = package.Content.GetParamString();
            Combiner = package.Content.GetParamString();
            SingleDouble = package.Content.GetParamString();
            CoverScenes = package.Content.GetParamString();
        }
    }
}
