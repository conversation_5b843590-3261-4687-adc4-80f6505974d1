﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.GeneralFuncDef
{
    public class GeneralRoutine
    {
        public List<BaseSubAnalyser> analysersList { get; set; } = new List<BaseSubAnalyser>();
        public bool periodCompareMode { get; set; } = false;
        public string name { get; set; } = "";
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<Dictionary<string,object>> anaListParam = new List<Dictionary<string,object>>();
                foreach(BaseSubAnalyser subAna in analysersList)
                {
                    Dictionary<string, object> anaParam = subAna.Param;
                    anaListParam.Add(anaParam);
                }
                param["AnaNodesList"] = anaListParam;
                param["RoutineName"] = name;
                return param;
            }
            set
            {
                analysersList.Clear();
                List<object> anaListParam = value["AnaNodesList"] as List<object>;
                foreach(Dictionary<string,object> anaParam in anaListParam)
                {
                    int nodeType = (int)anaParam["NodeType"];
                    string commanderName = (string)anaParam["CommanderName"];
                    BaseSubAnalyser ana = BaseSubAnalyserFactory.CreateAnalyser(nodeType, commanderName);
                    analysersList.Add(ana);
                }
                this.name = (string)value["RoutineName"];
            }
        }
        public override string ToString()
        {
            return name;
        }
    }
}
