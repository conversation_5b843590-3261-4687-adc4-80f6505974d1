﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYQueryReselectionTooMuchSetForm : BaseDialog
    {
        public ZTDIYQueryReselectionTooMuchSetForm()
        {
            InitializeComponent();
            
        }
        public static int ISecond { get; set; }

        public void SetCondition(int timeLimit, int distanceLimit, int handoverCount)
        {
            numTimeLimit.Value = (decimal)timeLimit;
            numDistanceLimit.Value = (decimal)distanceLimit;
            numHandoverCount.Value = (decimal)handoverCount;
        }

        public void GetCondition(ref int timeLimit, ref int distanceLimit, ref int handoverCount)
        {
            timeLimit = (int)numTimeLimit.Value;
            distanceLimit = (int)numDistanceLimit.Value;
            handoverCount = (int)numHandoverCount.Value;
            ISecond = timeLimit;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
