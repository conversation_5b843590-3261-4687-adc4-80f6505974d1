﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRPilotFrequencyPolluteBlock
    {
        public NRPilotFrequencyPolluteBlock()
        {

        }

        public NRPilotFrequencyPolluteBlock(int idx)
        {
            ID = idx;
        }

        public int ID { get; set; }

        public Dictionary<string, NRCellOfPilotFrequencyPolluteBlock> CellDic { get; set; } = new Dictionary<string, NRCellOfPilotFrequencyPolluteBlock>();
        public List<NRCellOfPilotFrequencyPolluteBlock> CellList { get; set; } = new List<NRCellOfPilotFrequencyPolluteBlock>();

        public List<TestPoint> TestPoints
        {
            get;
            private set;
        } = new List<TestPoint>();

        public int TestPointCount { get; set; }

        public int GoodTestPointCount { get; set; }

        public int TotalTestPointCount
        {
            get { return TestPointCount + GoodTestPointCount; }
        }

        public double BadSampleScale
        {
            get
            {
                return Math.Round(100.0 * TestPointCount / (TotalTestPointCount), 2);
            }
        }

        private int istime;
        private int ietime;
        public double LongitudeMid { get; set; }
        public double LatitudeMid { get; set; }

        internal void Join(NRPilotFrequencyPolluteBlock tpBlock)
        {
            foreach (TestPoint tp in tpBlock.TestPoints)
            {
                AddTestPoint(tp);
            }
            foreach (NRCellOfPilotFrequencyPolluteBlock item in tpBlock.CellDic.Values)
            {
                AddCellOfPilotFrequencyPolluteBlock(item);
            }
        }

        internal bool Within(double x1, double y1, double x2, double y2)
        {
            if (TestPoints.Count == 0)
            {
                if (LongitudeMid >= x1 && LongitudeMid <= x2 && LatitudeMid >= y1 || LatitudeMid <= y2)
                {
                    return true;
                }
            }
            else
            {
                foreach (TestPoint tp in TestPoints)
                {
                    if (tp.Longitude >= x1 && tp.Longitude <= x2 && tp.Latitude >= y1 || tp.Latitude <= y2)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public void AddTestPoint(TestPoint tp)
        {
            if (!TestPoints.Contains(tp))
            {
                TestPoints.Add(tp);
                if (istime > tp.Time)
                {
                    istime = tp.Time;
                }
                if (ietime < tp.Time)
                {
                    ietime = tp.Time;
                }
            }
        }

        public void AddCellOfPilotFrequencyPolluteBlock(NRCellOfPilotFrequencyPolluteBlock cellOfBlock)
        {
            if (CellDic.ContainsKey(cellOfBlock.NRCell.Name))
            {
                CellDic[cellOfBlock.NRCell.Name].Join(cellOfBlock);
            }
            else
            {
                CellDic[cellOfBlock.NRCell.Name] = cellOfBlock;
            }
        }

        public void AddTestPoint(TestPoint tp, List<NRCellOfPilotFrequencyPolluteBlock> cellList)
        {
            AddTestPoint(tp);
            foreach (NRCellOfPilotFrequencyPolluteBlock cellOfBlock in cellList)
            {
                AddCellOfPilotFrequencyPolluteBlock(cellOfBlock);
            }
        }

        public bool Intersect(double longitude, double latitude, int radius)
        {
            foreach (TestPoint tp in TestPoints)
            {
                if (MathFuncs.GetDistance(longitude, latitude, tp.Longitude, tp.Latitude) <= radius)
                {
                    return true;
                }
            }
            return false;
        }

        private void getCenterPoint()
        {
            DbRect bounds = new DbRect();
            bool first = true;
            foreach (TestPoint item in TestPoints)
            {
                first = setBounds(bounds, first, item);
            }
            LongitudeMid = bounds.Center().x;
            LatitudeMid = bounds.Center().y;
        }

        private bool setBounds(DbRect bounds, bool first, TestPoint item)
        {
            if (first)
            {
                bounds.x1 = item.Longitude;
                bounds.x2 = item.Longitude;
                bounds.y1 = item.Latitude;
                bounds.y2 = item.Latitude;
                first = false;
            }
            else
            {
                if (bounds.x1 > item.Longitude)
                {
                    bounds.x1 = item.Longitude;
                }
                if (bounds.x2 < item.Longitude)
                {
                    bounds.x2 = item.Longitude;
                }
                if (bounds.y1 > item.Latitude)
                {
                    bounds.y1 = item.Latitude;
                }
                if (bounds.y2 < item.Latitude)
                {
                    bounds.y2 = item.Latitude;
                }
            }

            return first;
        }

        public string RoadPlaceDesc { get; set; } = "";

        public string AreaPlaceDesc { get; private set; } = "";
        public string GridDesc { get; private set; } = "";
        public string AreaAgentPlaceDesc { get; private set; } = "";

        public void GetResult()
        {
            getCenterPoint();
            TestPointCount = TestPoints.Count;
            RoadPlaceDesc = GISManager.GetInstance().GetRoadPlaceDesc(LongitudeMid, LatitudeMid);

            CellList = new List<NRCellOfPilotFrequencyPolluteBlock>();
            foreach (var cell in CellDic.Values)
            {
                CellList.Add(cell);
            }

            //string strAreaName = GISManager.GetInstance().GetAreaPlaceDesc(LongitudeMid, LatitudeMid);
            //areaPlaceDesc = setDesc(strAreaName, areaPlaceDesc);

            //string strGridName = GISManager.GetInstance().GetGridDesc(LongitudeMid, LatitudeMid);
            //gridDesc = setDesc(strGridName, gridDesc);

            //string strAreaAgentName = GISManager.GetInstance().GetAreaAgentDesc(LongitudeMid, LatitudeMid);
            //areaAgentPlaceDesc = setDesc(strAreaAgentName, areaAgentPlaceDesc);
        }

        //private string setDesc(string strName, string strDesc)
        //{
        //    if (strName != null)
        //    {
        //        if (strDesc == null || strDesc == "")
        //        {
        //            strDesc = strName;
        //        }
        //        else
        //        {
        //            if (!strDesc.Contains(strName) && strName != "")
        //            {
        //                strDesc += "," + strName;
        //            }
        //        }
        //    }
        //    return strDesc;
        //}
    }
}
