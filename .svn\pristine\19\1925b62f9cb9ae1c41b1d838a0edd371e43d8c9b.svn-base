﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc
{
    class ExportSmallBtsReportHelper_XJ : ExportIndoorBtsReportBase
    {
        public static bool ExportReports(SmallBtsAcceptInfo btsInfo, string saveFolder)
        {
            string targetFile = GetTargetFile(btsInfo.BtsName, saveFolder);
            bool isValid = false;
            if (!string.IsNullOrEmpty(targetFile))
            {
                isValid = exportFile(btsInfo, targetFile);
            }
            saveCurReport(btsInfo);

            return isValid;
        }

        private static bool exportFile(SmallBtsAcceptInfo btsInfo, string targetFile)
        {
            Excel.Application xlApp = null;
            try
            {
                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile);

                fillHomePage(eBook, btsInfo);
                fillLevelCqtCoverPage(eBook, btsInfo);
                fillKpiPage(eBook, btsInfo);
                fillLevelKpiPage(eBook, btsInfo);

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);

                return true;
            }
            catch (Exception ex)
            {
                reportError(ex);
                return false;
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
                //GC.Collect();
            }
        }

        private static void saveCurReport(SmallBtsAcceptInfo btsInfo)
        {
            string path = getPath();
            if (string.IsNullOrEmpty(path))
            {
                return;
            }

            string targetFile = GetTargetFile(btsInfo.BtsName, path);
            if (!string.IsNullOrEmpty(targetFile))
            {
                exportFile(btsInfo, targetFile);
            }
        }

        private static string getPath()
        {
            return Singleton<TddSmallStationAcceptConfigHelper>.Instance.GetCurSavePath();
        }

        public static string GetTargetFile(string btsName, string saveFolder)
        {
            string workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/LteTestAcceptance");
            string templateFile = Path.Combine(workDir, "LTE小站单站验收模板.xlsx");

            string targetFile = string.Format("LTE小站单站验证_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile);
            return targetFile;
        }

        //填充报告第一页-室分记录单
        protected static void fillHomePage(Excel.Workbook eBook, InDoorBtsAcceptInfo btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.LteBts;
            Excel.Worksheet homePageSheet = (Excel.Worksheet)eBook.Sheets[1];

            #region 基站描述及基站参数
            homePageSheet.get_Range("e3").set_Value(Type.Missing, srcLteBts.Name);
            homePageSheet.get_Range("e5").set_Value(Type.Missing, srcLteBts.BTSID);
            homePageSheet.get_Range("z3").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));
            homePageSheet.get_Range("z11").set_Value(Type.Missing, btsInfo.CoveredFloors);
            homePageSheet.get_Range("h15").set_Value(Type.Missing, srcLteBts.Longitude);
            homePageSheet.get_Range("h16").set_Value(Type.Missing, srcLteBts.Latitude);
            #endregion

            int cellIndex = 0;
            int cellParamColIndex = 8;
            int cellParamRowIndex = 20;

            int cellKpiColIndex = 12;
            int cellKpiRowIndex = 51;
            foreach (LTECell icell in srcLteBts.Cells)
            {
                if (cellIndex == 3)
                {
                    cellParamColIndex = 8;
                    cellParamRowIndex = 34;

                    cellKpiColIndex = 12;
                    cellKpiRowIndex = 59;
                }
                else if (cellIndex == 6)
                {
                    cellKpiColIndex = 12;
                    cellKpiRowIndex = 67;
                }

                #region 小区参数
                homePageSheet.Cells[cellParamRowIndex, cellParamColIndex] = string.Format("Cell-{0}(PCI:{1})", cellIndex + 1, icell.PCI);
                homePageSheet.Cells[cellParamRowIndex + 2, cellParamColIndex] = icell.PCI;
                homePageSheet.Cells[cellParamRowIndex + 3, cellParamColIndex] = icell.EARFCN;
                homePageSheet.Cells[cellParamRowIndex + 8, cellParamColIndex] = icell.Altitude;
                homePageSheet.Cells[cellParamRowIndex + 9, cellParamColIndex] = icell.Direction;
                homePageSheet.Cells[cellParamRowIndex + 10, cellParamColIndex] = icell.Downward;
                InDoorCellAcceptInfo cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfo))
                {
                    homePageSheet.Cells[cellParamRowIndex + 13, cellParamColIndex + 3] = cellInfo.RoadTypeDes;//单双路
                }
                #endregion

                #region 小区指标
                if (cellInfo != null)
                {
                    homePageSheet.Cells[cellKpiRowIndex, cellKpiColIndex] = cellInfo.RrcInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 1, cellKpiColIndex] = cellInfo.ErabInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 2, cellKpiColIndex] = cellInfo.AccessInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 3, cellKpiColIndex] = cellInfo.CsfbInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 4, cellKpiColIndex] = cellInfo.IsFtpDlAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 5, cellKpiColIndex] = cellInfo.IsFtpUlAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 6, cellKpiColIndex] = cellInfo.DlCoverRate.IsAccordDes;
                }
                #endregion

                cellIndex++;
                cellParamColIndex += 8;
                cellKpiColIndex += 5;
            }

            homePageSheet.get_Range("n76").set_Value(Type.Missing, btsInfo.IsHandoverAccordDes);//系统内切换
            homePageSheet.get_Range("n77").set_Value(Type.Missing, btsInfo.IsLeakOutCheckAccordDes);//室分信号泄漏
            homePageSheet.get_Range("h92").set_Value(Type.Missing, btsInfo.IsAccordAcceptStr);//验收结论
            homePageSheet.get_Range("a95").set_Value(Type.Missing, btsInfo.NotAccordKpiDes);//未通过验收的原因
        }

        //填充报告第二页-性能测试表格（楼间对打）
        protected static void fillKpiPage(Excel.Workbook eBook, InDoorBtsAcceptInfo btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.LteBts;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[2];
            kpiPageSheet.get_Range("c2").set_Value(Type.Missing, srcLteBts.Name);
            kpiPageSheet.get_Range("p2").set_Value(Type.Missing, srcLteBts.BTSID);

            int cellIndex = 0;
            int firstColIndex = 17;//尝试次数或FTP相关指标所在列
            int secondColIndex = 24;//成功次数所在列
            foreach (LTECell icell in srcLteBts.Cells)
            {
                InDoorCellAcceptInfo cellInfoBase;
                if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfoBase))
                {
                    SmallCellAcceptInfo cellInfo = (SmallCellAcceptInfo)cellInfoBase;
                    if (cellInfo != null)
                    {
                        int rowIndex = 7 + (cellIndex * 23);//小区RRC指标所在行：首个小区所在行号为5，其他小区行号以23递增
                        kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.RrcInfo.TotalCount;
                        kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.RrcInfo.ValidCount;
                        kpiPageSheet.Cells[rowIndex + 1, firstColIndex] = cellInfo.ErabInfo.TotalCount;
                        kpiPageSheet.Cells[rowIndex + 1, secondColIndex] = cellInfo.ErabInfo.ValidCount;
                        kpiPageSheet.Cells[rowIndex + 2, firstColIndex] = cellInfo.AccessInfo.TotalCount;
                        kpiPageSheet.Cells[rowIndex + 2, secondColIndex] = cellInfo.AccessInfo.ValidCount;
                        kpiPageSheet.Cells[rowIndex + 4, firstColIndex] = cellInfo.CsfbInfo.TotalCount;
                        kpiPageSheet.Cells[rowIndex + 4, secondColIndex] = cellInfo.CsfbInfo.ValidCount;
                        kpiPageSheet.Cells[rowIndex + 5, firstColIndex] = cellInfo.MoVolteInfo.TotalCount;
                        kpiPageSheet.Cells[rowIndex + 5, secondColIndex] = cellInfo.MoVolteInfo.ValidCount;
                        kpiPageSheet.Cells[rowIndex + 6, firstColIndex] = cellInfo.MtVolteInfo.TotalCount;
                        kpiPageSheet.Cells[rowIndex + 6, secondColIndex] = cellInfo.MtVolteInfo.ValidCount;
                        kpiPageSheet.Cells[rowIndex + 7, firstColIndex] = cellInfo.FtpGoodDlSpeedInfo.KpiAvgValueDes;
                        kpiPageSheet.Cells[rowIndex + 8, firstColIndex] = cellInfo.FtpGoodUlSpeedInfo.KpiAvgValueDes;

                        setRateValue(kpiPageSheet, rowIndex + 11, firstColIndex, cellInfo.DlCoverRate.Rate);
                        kpiPageSheet.Cells[rowIndex + 12, firstColIndex] = cellInfo.FtpRsrpInfo.KpiAvgValueDes;
                        kpiPageSheet.Cells[rowIndex + 13, firstColIndex] = cellInfo.FtpSinrInfo.KpiAvgValueDes;
                        kpiPageSheet.Cells[rowIndex + 14, firstColIndex] = cellInfo.FtpDlSpeedInfo.KpiAvgValueDes;
                        kpiPageSheet.Cells[rowIndex + 15, firstColIndex] = cellInfo.FtpUlSpeedInfo.KpiAvgValueDes;
                        kpiPageSheet.Cells[rowIndex + 16, firstColIndex] = cellInfo.Rsrp_FaceToPassageway.KpiAvgValueDes;
                        kpiPageSheet.Cells[rowIndex + 17, firstColIndex] = cellInfo.Rsrp_BackFaceToPassageway.KpiAvgValueDes;

                        setRateValue(kpiPageSheet, rowIndex + 19, firstColIndex, cellInfo.LeakoutRate_LockEarfcn);
                        setRateValue(kpiPageSheet, rowIndex + 19, firstColIndex + 13, cellInfo.LeakoutRate_Scan);

                        kpiPageSheet.Cells[rowIndex + 21, firstColIndex] = cellInfo.HandOverInfo.TotalCount;
                        kpiPageSheet.Cells[rowIndex + 21, secondColIndex] = cellInfo.HandOverInfo.ValidCount;
                    }
                }
                cellIndex++;
            }
        }

        //填充报告第四页-平层定点覆盖表格
        protected static void fillLevelCqtCoverPage(Excel.Workbook eBook, InDoorBtsAcceptInfo btsInfo)
        {
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[4];
            int rowIndex = 3;
            foreach (InDoorCellAcceptInfo cellInfoBase in btsInfo.CellsAcceptDic.Values)
            {
                SmallCellAcceptInfo cellInfo = (SmallCellAcceptInfo)cellInfoBase;
                if (cellInfo == null)
                {
                    continue;
                }
                foreach (LevelCqtCoverInfo levelCoverInfo in cellInfo.LevelCqtCoverInfoDic.Values)
                {
                    kpiPageSheet.Cells[rowIndex, 1] = levelCoverInfo.AntennaPosition;
                    kpiPageSheet.Cells[rowIndex, 2] = levelCoverInfo.CoverFloor;
                    kpiPageSheet.Cells[rowIndex, 3] = levelCoverInfo.IndoorMid_RsrpInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[rowIndex, 4] = levelCoverInfo.IndoorMid_SinrInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[rowIndex, 5] = levelCoverInfo.IndoorFaceTo_RsrpInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[rowIndex, 6] = levelCoverInfo.IndoorFaceTo_SinrInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[rowIndex, 7] = levelCoverInfo.IndoorBackFaceTo_RsrpInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[rowIndex, 8] = levelCoverInfo.IndoorBackFaceTo_SinrInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[rowIndex, 9] = levelCoverInfo.PassagewayFaceTo_RsrpInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[rowIndex, 10] = levelCoverInfo.PassagewayBackFaceTo_RsrpInfo.KpiAvgValueDes;

                    rowIndex++;
                }
            }
        }

        //填充报告第五页-平层测试指标
        protected static void fillLevelKpiPage(Excel.Workbook eBook, InDoorBtsAcceptInfo btsInfo)
        {
            int rowIndex = 2;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[5];
            foreach (InDoorCellAcceptInfo cellAcceptInfo in btsInfo.CellsAcceptDic.Values)
            {
                fillCellLevelKpiPage(kpiPageSheet, cellAcceptInfo, ref rowIndex);
            }

            kpiPageSheet.Cells[rowIndex + 2, 1] = btsInfo.CoveredFloorsDes_Lack;
        }
    }
}
