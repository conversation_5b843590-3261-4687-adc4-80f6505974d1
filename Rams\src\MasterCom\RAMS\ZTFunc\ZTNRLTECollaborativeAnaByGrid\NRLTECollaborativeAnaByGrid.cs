﻿using CQTLibrary.CqtZTFunc;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTNRLTECollaborativeAnaByGrid;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class NRLTECollaborativeAnaByGrid : DIYGridQuery
    {
        readonly NRGridImage nrGrid = new NRGridImage();
        readonly NRLTEGridImage nrLteGrid = new NRLTEGridImage();
        readonly LTEGridImage lteGrid = new LTEGridImage();

        //Dictionary<ZTNRLTECollaborativeAnaType, Dictionary<int, List<Result>>> typeSerialResultDic;
        Dictionary<ZTNRLTECollaborativeAnaType, List<Result>> typeResultDic;
        List<SerialResult> serialResultList;

        NRLTECollaborativeAnaByGridDlg dlg;
        NRLTECollaborativeAnaByGridCondition curCondition;

        public NRLTECollaborativeAnaByGrid()
          : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "4/5G协同分析按按栅格"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(3, 35000, 35099, "4/5G协同分析按按栅格");
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            GridUnitBase grid = new GridUnitBase(lng, lat);
            return condition.Geometorys.GeoOp.ContainsRectCenter(grid.Bounds);
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            return getTriadIDIgnoreServiceType(new string[] {
                nrGrid.RsrpAvg, nrGrid.RsrpCount, nrGrid.SinrAvg, nrGrid.SinrCount,
                nrLteGrid.RsrpAvg, nrLteGrid.RsrpCount, nrLteGrid.SinrAvg, nrLteGrid.SinrCount,
                lteGrid.RsrpAvg, lteGrid.RsrpCount, lteGrid.SinrAvg, lteGrid.SinrCount,
            });
        }

        protected override bool getConditionBeforeQuery()
        {
            base.getConditionBeforeQuery();

            if (dlg == null)
            {
                dlg = new NRLTECollaborativeAnaByGridDlg();
            }
            dlg.SetCondition(curCondition);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                serialResultList = new List<SerialResult>();
                typeResultDic = new Dictionary<ZTNRLTECollaborativeAnaType, List<Result>>()
                {
                    { ZTNRLTECollaborativeAnaType.WeakCover4G5G, new List<Result>() },
                    { ZTNRLTECollaborativeAnaType.Better4G, new List<Result>() },
                    { ZTNRLTECollaborativeAnaType.Better5G, new List<Result>() },
                };
                //typeSerialResultDic = new Dictionary<ZTNRLTECollaborativeAnaType, Dictionary<int, List<Result>>>();
                curCondition = dlg.GetCondition();
                return true;
            }

            return false;
        }

        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
            foreach (int districtID in condition.DistrictIDs)
            {
                queryDistrictData(districtID);
                changGridDataFormula();
            }
            afterRecieveAllData();
            fireShowResult();
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.MainForm.GetMapForm().GetGridShowLayer();
                WaitBox.Text = "开始统计查询栅格数据...";
                WaitBox.CanCancel = true;

                string statImgIDSet = this.getStatImgNeededTriadID();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(null, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                }
                else
                {
                    foreach (TimePeriod period in condition.Periods)
                    {
                        queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                    }
                }
                WaitBox.Text = "数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }

        private void changGridDataFormula()
        {
            var gridResultDic = new Dictionary<string, Result>();
            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                string key = cu.RowIdx + "_" + cu.ColIdx;
                Result result;
                if (!gridResultDic.TryGetValue(key, out result))
                {
                    result = new Result();
                    result.Grid = cu;
                    result.Token = key;
                    gridResultDic.Add(key, result);
                }
                //获取栅格数据
                setGridData(cu, result.NR, nrGrid);
                setGridData(cu, result.LTE, lteGrid);
                if (curCondition.ContainsNRLTE)
                {
                    setGridData(cu, result.LTE, nrLteGrid);
                }
            }

            dealResult(gridResultDic);

            //处理栅格连续
            serialResultList = SerialGridHelper.GetSerialGrid(2, typeResultDic);
        }

        #region 设置栅格数据
        private void setGridData(ColorUnit cu, Result.ParamInfo paramInfo, NRLTECollaborativeAnaByGridImage image)
        {
            double rsrpAvg = cu.DataHub.CalcValueByFormula(image.RsrpAvg);
            double rsrpCount = cu.DataHub.CalcValueByFormula(image.RsrpCount);
            double sinrAvg = cu.DataHub.CalcValueByFormula(image.SinrAvg);
            double sinrCount = cu.DataHub.CalcValueByFormula(image.SinrCount);

            paramInfo.RSRP.Sum += getValidData(rsrpAvg * rsrpCount);
            paramInfo.RSRP.Count += getValidData(rsrpCount);
            paramInfo.SINR.Sum += getValidData(sinrAvg * sinrCount);
            paramInfo.SINR.Count += getValidData(sinrCount);
        }

        private double getValidData(double data)
        {
            if (!double.IsNaN(data))
            {
                return data;
            }
            return 0;
        }
        #endregion

        #region 处理结果,判断4/5G协同情况
        private void dealResult(Dictionary<string, Result> gridResultDic)
        {
            //计算栅格平均值,统计4/5G协同情况
            foreach (var item in gridResultDic.Values)
            {
                item.Calculate();

                //bool isNRRsrpValid = !curCondition.ContiansNone || !double.IsNaN(item.NR.RSRP.Avg);
                //bool isLTERsrpValid = !curCondition.ContiansNone || !double.IsNaN(item.LTE.RSRP.Avg);
                bool isNRRsrpValid = !curCondition.ContiansNone || item.NR.RSRP.Avg != null;
                bool isLTERsrpValid = !curCondition.ContiansNone || item.LTE.RSRP.Avg != null;

                if ((!isNRRsrpValid || item.NR.RSRP.Avg < curCondition.Num5GWeakCover)
                    && (!isLTERsrpValid || item.LTE.RSRP.Avg < curCondition.Num4GWeakCover))
                {
                    //覆盖同差
                    setRes(item, ZTNRLTECollaborativeAnaType.WeakCover4G5G);
                }
                else if (isLTERsrpValid && item.LTE.RSRP.Avg >= curCondition.Num4GWeakCover
                    && (!isNRRsrpValid || item.LTE.RSRP.Avg - item.NR.RSRP.Avg >= curCondition.Num4GBetterThan5G))
                {
                    //4G优于5G
                    setRes(item, ZTNRLTECollaborativeAnaType.Better4G);
                }
                else if (isNRRsrpValid && item.NR.RSRP.Avg >= curCondition.Num4GWeakCover
                    && (!isLTERsrpValid || item.NR.RSRP.Avg - item.LTE.RSRP.Avg >= curCondition.Num4GBetterThan5G))
                {
                    //5G优于4G
                    setRes(item, ZTNRLTECollaborativeAnaType.Better5G);
                }
            }
        }

        private void setRes(Result item, ZTNRLTECollaborativeAnaType type)
        {
            item.Type = type;
            item.SetTpeDesc();
            if (typeResultDic.TryGetValue(item.Type, out var resList))
            {
                resList.Add(item);
            }
        }
        #endregion

        protected override void fireShowResult()
        {
            NRLTECollaborativeAnaByGridForm frm = MainModel.CreateResultForm(typeof(NRLTECollaborativeAnaByGridForm)) as NRLTECollaborativeAnaByGridForm;
            frm.FillData(serialResultList, typeResultDic);
            frm.Visible = true;
            frm.BringToFront();
        }
    }



}
