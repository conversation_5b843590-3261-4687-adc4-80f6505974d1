﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.ExMap;
using MasterCom.MTGis;
using System.Drawing;
using MasterCom.RAMS.Func;
using System.Drawing.Drawing2D;
using GMap.NET;
using System.Windows.Forms;

namespace MasterCom.RAMS.MapControlTool
{
    public class ExMapToolMeasureDistance
    {
        private readonly MTExGMap mapControl;
        private List<PointLatLng> measurePoints;//经纬度坐标
        public List<DbPoint> MeasurePoints
        {
            get
            {
                List<DbPoint> ret = new List<DbPoint>();
                foreach (PointLatLng p in measurePoints)
                {
                    DbPoint dp = new DbPoint(p.Lng, p.Lat);
                    ret.Add(dp);
                }
                return ret;
            }
            set
            {
                if (value != null)
                {
                    measurePoints = new List<PointLatLng>();
                    foreach (DbPoint p in value)
                    {
                        PointLatLng gp = new PointLatLng(p.y, p.x);
                        measurePoints.Add(gp);
                    }
                }
                else
                {
                    measurePoints = null;
                }
            }
        }
        private bool isActive = false;
        private readonly Font fontMeasure;
        private readonly Pen linePen;
        private bool measureStarted = false;
        public bool MeasureStarted
        {
            get { return measureStarted; }
            set
            {
                if (value != measureStarted)
                {
                    measureStarted = value;
                    mapForm.MeasureDistanceTool.MeasureStarted = value;
                }
            }
        }
        private bool isShowAngle
        {
            get { return mapForm.MeasureDistanceTool.IsShowAngle; }
        }
        private readonly MapForm mapForm = null;
        private readonly EventHandler handlerMouseDoubleClick;
        public ExMapToolMeasureDistance(MTExGMap map, MapForm mapForm)
        {
            mapControl = map;
            fontMeasure = new Font("宋体", 11, FontStyle.Bold);
            measurePoints = new List<PointLatLng>();
            linePen = new Pen(Color.Green, 2);
            this.mapForm = mapForm;
            MasterCom.RAMS.Model.MainModel.GetInstance().ClearDataEvent += new EventHandler(mapForm_ClearDataEvent);
            this.handlerMouseDoubleClick = new EventHandler(mapControl_DoubleClick);
        }

        void mapForm_ClearDataEvent(object sender, EventArgs e)
        {
            measurePoints.Clear();
        }

        public void Activate()
        {
            if (!isActive)
            {
                mapControl.MouseDown += new System.Windows.Forms.MouseEventHandler(mapControl_MouseDown);
                mapControl.DoubleClick += this.handlerMouseDoubleClick;
            }
            isActive = true;
        }

        void mapControl_DoubleClick(object sender, EventArgs e)
        {
            finishDraw();
        }

        void mapControl_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            mapControl.MouseMove -= new System.Windows.Forms.MouseEventHandler(mapControl_MouseMove);
            if (e.Button != MouseButtons.Left)//非鼠标左键，开始新的测量点
            {
                finishDraw();
            }
            else
            {
                if (!MeasureStarted)
                {
                    measurePoints.Clear();
                    mapForm.MeasureDistanceTool.MeasurePoints.Clear();
                }
                mapControl.MouseMove += mapControl_MouseMove;
                MeasureStarted = true;
                PointLatLng gp = mapControl.FromLocalToLatLngAdaptered(e.X, e.Y);
                if (measurePoints.Count > 0 && measurePoints[measurePoints.Count - 1].Lng == gp.Lng && measurePoints[measurePoints.Count - 1].Lat == gp.Lat)
                {
                    return;
                }
                measurePoints.Add(gp);
                mapForm.MeasureDistanceTool.MeasurePoints = this.MeasurePoints;
            }
        }

        void mapControl_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (measurePoints.Count == 0)
            {
                return;
            }
            GPoint lastPoint = mapControl.FromLatLngToLocalAdaptered(measurePoints[measurePoints.Count - 1]);
            PointLatLng currentPnt = mapControl.FromLocalToLatLngAdaptered(e.X, e.Y);
            GPoint curPoint = mapControl.FromLatLngToLocalAdaptered(currentPnt);
            if (lastPoint.X == curPoint.X && lastPoint.Y == curPoint.Y)
            {//与上一点坐标相同，忽略画线
                return;
            }           
            List<PointLatLng> pnts = new List<PointLatLng>();
            pnts.AddRange(measurePoints);
            pnts.Add(currentPnt);
            Point[] pArr = new Point[pnts.Count];
            for (int i = 0; i < pnts.Count; i++)
            {
                GPoint gP = mapControl.FromLatLngToLocalAdaptered(pnts[i]);
                pArr[i] = new Point(gP.X, gP.Y);
            }
            GraphicsPath path = new GraphicsPath();
            path.AddLines(pArr);
            path.Widen(linePen);
            Region invRegion = new Region(mapControl.ClientRectangle);
            invRegion.Exclude(path);
            mapControl.Invalidate(invRegion);
            mapControl.Update();
            Graphics g = mapControl.CreateGraphics();
            g.SmoothingMode = SmoothingMode.AntiAlias;
            drawMeasureLines(g, pnts);
            g.Dispose();
            mapForm.MeasureDistanceTool.MeasurePoints = MeasurePoints;
        }

        public void Deactivate()
        {
            if (!isActive) return;
            mapControl.MouseDown -= mapControl_MouseDown; mapControl.MouseMove -= mapControl_MouseMove;
            MeasureStarted = false;
            mapControl.DoubleClick -= this.handlerMouseDoubleClick;
            isActive = false;
        }

        void finishDraw()
        {
            mapControl.MouseMove -= mapControl_MouseMove;
            MeasureStarted = false;
            mapControl.UpdateMap();
        }

        public void Draw(Graphics g)
        {
            drawMeasureLines(g, measurePoints);
        }

        readonly SolidBrush solidBrush = new SolidBrush(Color.FromArgb(200, Color.White));
        private void drawMeasureLines(Graphics g, List<PointLatLng> points)
        {
            if (points == null || points.Count < 2)
            {
                return;
            }

            Point[] pArr = new Point[points.Count];
            for (int i = 0; i < points.Count; i++)
            {
                GPoint gP = mapControl.FromLatLngToLocalAdaptered(points[i]);
                pArr[i] = new Point(gP.X, gP.Y);
            }          
            g.DrawLines(linePen, pArr);
            double distanceTotal = 0;

            double distancePre = 0.0;
            double distanceTotalPre = 0.0;
            for (int i = 1; i < points.Count; i++)
            {
                if (isShowAngle)
                {
                    //需要同时刷新2个点的数据
                    double angle = 0.0;
                    double distance = 0.0;

                    distance = MasterCom.Util.MathFuncs.GetDistance(points[i].Lng, points[i].Lat, points[i - 1].Lng, points[i - 1].Lat);
                    distanceTotal += distance;
                    if (i == 1)
                    {
                        angle = getAngle(points[i - 1], points[i]);
                        //绘制起始点
                        string strFir = string.Format("双击鼠标左键可以结束本次测量！\n角度:{0}°", angle);
                        SizeF sizeFir = g.MeasureString(strFir, fontMeasure);
                        g.FillRectangle(solidBrush, pArr[0].X, pArr[0].Y, sizeFir.Width, sizeFir.Height);
                        g.DrawString(strFir, fontMeasure, Brushes.Orange, pArr[0]);
                        //绘制第二个点
                        string strSec = string.Format("{0}m 总长:{1}m", (int)distance, (int)distanceTotal);
                        SizeF sizeSec = g.MeasureString(strSec, fontMeasure);
                        g.FillRectangle(solidBrush, pArr[i].X, pArr[i].Y, sizeSec.Width, sizeSec.Height);
                        g.DrawString(strSec, fontMeasure, Brushes.Orange, pArr[i]);
                    }
                    else
                    {
                        angle = getAngle(points[i - 1], points[i], points[i - 2]);
                        //绘制前一个点
                        string strPre = string.Format("{0}m 总长:{1}m\n角度:{2}°", (int)distancePre, (int)distanceTotalPre, angle);
                        SizeF sizePre = g.MeasureString(strPre, fontMeasure);
                        g.FillRectangle(solidBrush, pArr[i - 1].X, pArr[i - 1].Y, sizePre.Width, sizePre.Height);
                        g.DrawString(strPre, fontMeasure, Brushes.Orange, pArr[i - 1]);
                        //绘制后一个点
                        string strNext = string.Format("{0}m 总长:{1}m", (int)distance, (int)distanceTotal);
                        SizeF sizeNext = g.MeasureString(strNext, fontMeasure);
                        g.FillRectangle(solidBrush, pArr[i].X, pArr[i].Y, sizeNext.Width, sizeNext.Height);
                        g.DrawString(strNext, fontMeasure, Brushes.Orange, pArr[i]);
                    }
                    distancePre = distance;
                    distanceTotalPre = distanceTotal;
                }
                else
                {
                    if (i == 1)
                    {
                        string strFir = "双击鼠标左键可以结束本次测量！";
                        SizeF sizeFir = g.MeasureString(strFir, fontMeasure);
                        g.FillRectangle(solidBrush, pArr[0].X, pArr[0].Y, sizeFir.Width, sizeFir.Height);
                        g.DrawString(strFir, fontMeasure, Brushes.Orange, pArr[0]);
                    }
                    double distance = MasterCom.Util.MathFuncs.GetDistance(points[i].Lng, points[i].Lat, points[i - 1].Lng, points[i - 1].Lat);
                    distanceTotal += distance;
                    string str = string.Empty;

                    str = string.Format("{0}m 总长:{1}m", (int)distance, (int)distanceTotal);
                    SizeF sizeF = g.MeasureString(str, fontMeasure);
                    g.FillRectangle(solidBrush, pArr[i].X, pArr[i].Y, sizeF.Width, sizeF.Height);
                    g.DrawString(str, fontMeasure, Brushes.Orange, pArr[i]);
                }
            }
        }

        /// <summary>
        /// 获取连线的角度
        /// </summary>
        /// <param name="lastPoint">上一个点</param>
        /// <param name="curPoint">当前的点</param>
        /// <param name="pointBeforeLast">上上个点</param>
        /// <returns></returns>
        private double getAngle(PointLatLng lastPoint, PointLatLng curPoint, PointLatLng? pointBeforeLast = null)
        {
            double angle;
            if (pointBeforeLast == null)
            {//如果只连了一条线则计算这条线与正北方向顺时针的夹角
                angle = MasterCom.Util.MathFuncs.getAngleFromPointToPoint_D(lastPoint.Lng, lastPoint.Lat, curPoint.Lng, curPoint.Lat);
            }
            else
            {//计算新连的线与上一次连线的夹角
                PointF o, a, b;
                o = new PointF((float)lastPoint.Lng, (float)lastPoint.Lat);
                a = new PointF((float)((PointLatLng)pointBeforeLast).Lng, (float)((PointLatLng)pointBeforeLast).Lat);
                b = new PointF((float)curPoint.Lng, (float)curPoint.Lat);
                angle = MasterCom.Util.MathFuncs.CalAngle(o, a, b);
            }
            return Math.Round(angle, 2);
        }

    }
}
