﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public static class TestPointFactory
    {
        public static TestPointBaseDeal CreateTestPointDeal(TestPoint testPoint)
        {
            TestPointBaseDeal dealer = null;
            if (testPoint is TDTestPointDetail || testPoint is TDTestPointSummary)
            {
                dealer = new TestPointTDDeal();
            }
            else if (testPoint is WCDMATestPointDetail || testPoint is WCDMATestPointSummary)
            {
                dealer = new TestPointWDeal();
            }
            else if (testPoint is LTETestPointDetail || testPoint is LTEUepTestPoint)
            {
                dealer = getLTETPDeal(testPoint, dealer);
            }
            else if (testPoint is LTEFddTestPoint)
            {
                dealer = getLTEFddTPDeal(testPoint, dealer);
            }
            else if (testPoint is TestPointScan || testPoint is ScanTestPoint_G)
            {
                dealer = new TestPointGsmScanDeal();
            }
            else if (testPoint is ScanTestPoint_TD)
            {
                dealer = new TestPointTDScanDeal();
            }
            else if (testPoint is ScanTestPoint_LTE)
            {
                dealer = new TestPointLteScanDeal();
            }
            else
            {
                dealer = new TestPointGsmDeal();
            }
            return dealer;
        }

        private static TestPointBaseDeal getLTETPDeal(TestPoint testPoint, TestPointBaseDeal dealer)
        {
            switch (testPoint.NetworkType)
            {
                case TestPoint.ECurrNetType.GSM:
                    dealer = new TestPointLteGsmDeal();
                    break;
                case TestPoint.ECurrNetType.TD:
                    dealer = new TestPointLteTDDeal();
                    break;
                case TestPoint.ECurrNetType.LTE:
                case TestPoint.ECurrNetType.Unknow:
                case TestPoint.ECurrNetType.NoService:
                case TestPoint.ECurrNetType.Invalid:
                    dealer = new TestPointLteDeal();
                    break;
                default:
                    break;
            }

            return dealer;
        }

        private static TestPointBaseDeal getLTEFddTPDeal(TestPoint testPoint, TestPointBaseDeal dealer)
        {
            switch (testPoint.NetworkType)
            {
                case TestPoint.ECurrNetType.GSM:
                    dealer = new TestPointLteFddGsmDeal();
                    break;
                case TestPoint.ECurrNetType.WCDMA:
                    dealer = new TestPointLteFddWDeal();
                    break;
                case TestPoint.ECurrNetType.TD:
                    dealer = new TestPointLteTDDeal();
                    break;
                case TestPoint.ECurrNetType.CDMA_EVDO:
                    break;
                case TestPoint.ECurrNetType.LTE:
                case TestPoint.ECurrNetType.Unknow:
                case TestPoint.ECurrNetType.NoService:
                case TestPoint.ECurrNetType.Invalid:
                    dealer = new TestPointLteFddDeal();
                    break;
                default:
                    break;
            }

            return dealer;
        }
    }
}
