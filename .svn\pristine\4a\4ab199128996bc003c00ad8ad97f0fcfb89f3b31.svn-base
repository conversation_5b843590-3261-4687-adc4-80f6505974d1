﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTMainCellLastOccupyBase : DIYAnalyseFilesOneByOneByRegion
    {
        public List<CellLastOccupyFileInfo> resultList { get; set; } = new List<CellLastOccupyFileInfo>();
        public ZTMainCellLastOccupySetCondition hoCondition { get; set; }

        public ZTMainCellLastOccupyBase(MainModel mainModel)
            : base(mainModel)
        {
            this.Columns = new List<string>();
            this.IncludeMessage = true;
            this.Columns.Add("lte_TAC");
            this.Columns.Add("lte_ECI");
            this.Columns.Add("lte_RSRP");
            this.Columns.Add("lte_SINR");
            this.Columns.Add("RxLevSub");
            this.Columns.Add("RxQualSub");
            this.Columns.Add("LAC");
            this.Columns.Add("CI");
        }

        ZTMainCellLastOccupySetLTEConditionForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTMainCellLastOccupySetLTEConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                resultList = new List<CellLastOccupyFileInfo>();
                hoCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                CellLastOccupyCellInfo cellInfo = null;
                CellLastOccupyFileInfo fileInfo = new CellLastOccupyFileInfo(file.GetFileInfo());

                    for (int i = 0; i < file.TestPoints.Count - 1; i++)
                    {
                        #region  lte采样点获取
                        if (file.TestPoints[i] is LTETestPointDetail)
                        {
                            if (file.TestPoints[i]["lte_ECI"] != null)
                            {
                                if (file.TestPoints[i + 1]["lte_ECI"] != null)
                                {
                                    if ((int)file.TestPoints[i]["lte_ECI"] == (int)file.TestPoints[i + 1]["lte_ECI"])
                                    {
                                        if (cellInfo == null)
                                        {
                                            cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                            cellInfo.AddPnt(file.TestPoints[i]);
                                            if (file.TestPoints[i].GetMainCell_LTE() != null)
                                            {
                                                cellInfo.CellName = file.TestPoints[i].GetMainCell_LTE().Name;
                                            }
                                            else
                                            {
                                                cellInfo.CellName = file.TestPoints[i]["lte_ECI"].ToString();
                                            }
                                        }
                                        else
                                        {
                                            cellInfo.AddPnt(file.TestPoints[i]);
                                            if (i == file.TestPoints.Count - 2)
                                            {
                                                cellInfo.AddPnt(file.TestPoints[i + 1]);
                                                cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                                fileInfo.CellInfoList.Add(cellInfo);
                                                cellInfo = null;
                                            }
                                        }
                                    }
                                    else if (cellInfo != null)
                                    {
                                        cellInfo.AddPnt(file.TestPoints[i]);
                                        cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                        fileInfo.CellInfoList.Add(cellInfo);
                                        cellInfo = null;
                                    }
                                    else if (cellInfo == null)
                                    {
                                        cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                        cellInfo.AddPnt(file.TestPoints[i]);
                                        cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                        if (file.TestPoints[i].GetMainCell_LTE() != null)
                                        {
                                            cellInfo.CellName = file.TestPoints[i].GetMainCell_LTE().Name;
                                        }
                                        else
                                        {
                                            cellInfo.CellName = file.TestPoints[i]["lte_ECI"].ToString();
                                        }
                                        fileInfo.CellInfoList.Add(cellInfo);
                                        cellInfo = null;
                                        if (i == file.TestPoints.Count - 2)
                                        {
                                            cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                            cellInfo.AddPnt(file.TestPoints[i + 1]);
                                            cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                            if (file.TestPoints[i + 1].GetMainCell_LTE() != null)
                                            {
                                                cellInfo.CellName = file.TestPoints[i + 1].GetMainCell_LTE().Name;
                                            }
                                            else
                                            {
                                                cellInfo.CellName = file.TestPoints[i + 1]["lte_ECI"].ToString();
                                            }
                                            fileInfo.CellInfoList.Add(cellInfo);
                                            cellInfo = null;
                                        }
                                    }
                                }
                                else if (cellInfo != null)
                                {
                                    cellInfo.AddPnt(file.TestPoints[i]);
                                    cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                    fileInfo.CellInfoList.Add(cellInfo);
                                    cellInfo = null;
                                }
                                else if (cellInfo == null)
                                {
                                    cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                    cellInfo.AddPnt(file.TestPoints[i]);
                                    cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                    if (file.TestPoints[i].GetMainCell_LTE() != null)
                                    {
                                        cellInfo.CellName = file.TestPoints[i].GetMainCell_LTE().Name;
                                    }
                                    else
                                    {
                                        cellInfo.CellName = file.TestPoints[i]["lte_ECI"].ToString();
                                    }
                                    fileInfo.CellInfoList.Add(cellInfo);
                                    cellInfo = null;
                                }
                            }
                            else if (file.TestPoints[i + 1]["lte_ECI"] != null && i == file.TestPoints.Count - 2)
                            {
                                cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                cellInfo.AddPnt(file.TestPoints[i + 1]);
                                cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                if (file.TestPoints[i + 1].GetMainCell_LTE() != null)
                                {
                                    cellInfo.CellName = file.TestPoints[i + 1].GetMainCell_LTE().Name;
                                }
                                else
                                {
                                    cellInfo.CellName = file.TestPoints[i + 1]["lte_ECI"].ToString();
                                }
                                fileInfo.CellInfoList.Add(cellInfo);
                                cellInfo = null;
                            }

                        }
                        #endregion

                        #region   gsm采样点获取
                        else if (file.TestPoints[i] is TestPointDetail)
                        {
                            if (file.TestPoints[i]["CI"] != null && file.TestPoints[i]["LAC"]!=null)
                            {
                                if (file.TestPoints[i + 1]["CI"] != null && file.TestPoints[i+1]["LAC"]!=null)
                                {
                                    if ((int)file.TestPoints[i]["CI"] == (int)file.TestPoints[i + 1]["CI"] && (int)file.TestPoints[i]["LAC"] == (int)file.TestPoints[i + 1]["LAC"])
                                    {
                                        if (cellInfo == null)
                                        {
                                            cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                            cellInfo.AddPnt(file.TestPoints[i]);
                                            if (file.TestPoints[i].GetMainCell_GSM() != null)
                                            {
                                                cellInfo.CellName = file.TestPoints[i].GetMainCell_GSM().Name;
                                            }
                                            else
                                            {
                                                cellInfo.CellName = file.TestPoints[i]["CI"].ToString() + "_" + file.TestPoints[i]["LAC"].ToString();
                                            }
                                        }
                                        else
                                        {
                                            cellInfo.AddPnt(file.TestPoints[i]);
                                            if (i == file.TestPoints.Count - 2)
                                            {
                                                cellInfo.AddPnt(file.TestPoints[i + 1]);
                                                cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                                fileInfo.CellInfoList.Add(cellInfo);
                                                cellInfo = null;
                                            }
                                        }
                                    }
                                    else if (cellInfo != null)
                                    {
                                        cellInfo.AddPnt(file.TestPoints[i]);
                                        cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                        fileInfo.CellInfoList.Add(cellInfo);
                                        cellInfo = null;
                                    }
                                    else if (cellInfo == null)
                                    {
                                        cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                        cellInfo.AddPnt(file.TestPoints[i]);
                                        cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                        if (file.TestPoints[i].GetMainCell_GSM() != null)
                                        {
                                            cellInfo.CellName = file.TestPoints[i].GetMainCell_GSM().Name;
                                        }
                                        else
                                        {
                                            cellInfo.CellName = file.TestPoints[i]["CI"].ToString() + "_" + file.TestPoints[i]["LAC"].ToString();
                                        }
                                        fileInfo.CellInfoList.Add(cellInfo);
                                        cellInfo = null;
                                        if (i == file.TestPoints.Count - 2)
                                        {
                                            cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                            cellInfo.AddPnt(file.TestPoints[i + 1]);
                                            cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                            if (file.TestPoints[i + 1].GetMainCell_GSM() != null)
                                            {
                                                cellInfo.CellName = file.TestPoints[i + 1].GetMainCell_GSM().Name;
                                            }
                                            else
                                            {
                                                cellInfo.CellName = file.TestPoints[i]["CI"].ToString() + "_" + file.TestPoints[i]["LAC"].ToString();
                                            }
                                            fileInfo.CellInfoList.Add(cellInfo);
                                            cellInfo = null;
                                        }
                                    }
                                }
                                else if (cellInfo != null)
                                {
                                    cellInfo.AddPnt(file.TestPoints[i]);
                                    cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                    fileInfo.CellInfoList.Add(cellInfo);
                                    cellInfo = null;
                                }
                                else if (cellInfo == null)
                                {
                                    cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                    cellInfo.AddPnt(file.TestPoints[i]);
                                    cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                    if (file.TestPoints[i].GetMainCell_GSM() != null)
                                    {
                                        cellInfo.CellName = file.TestPoints[i].GetMainCell_GSM().Name;
                                    }
                                    else
                                    {
                                        cellInfo.CellName = file.TestPoints[i]["CI"].ToString() + "_" + file.TestPoints[i]["LAC"].ToString();
                                    }
                                    fileInfo.CellInfoList.Add(cellInfo);
                                    cellInfo = null;
                                }
                            }
                            else if (file.TestPoints[i + 1]["CI"] != null && file.TestPoints[i + 1]["LAC"] != null && i == file.TestPoints.Count - 2)
                            {
                                cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                cellInfo.AddPnt(file.TestPoints[i + 1]);
                                cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                if (file.TestPoints[i + 1].GetMainCell_GSM() != null)
                                {
                                    cellInfo.CellName = file.TestPoints[i + 1].GetMainCell_GSM().Name;
                                }
                                else
                                {
                                    cellInfo.CellName = file.TestPoints[i]["CI"].ToString() + "_" + file.TestPoints[i]["LAC"].ToString();
                                }
                                fileInfo.CellInfoList.Add(cellInfo);
                                cellInfo = null;
                            }
                        }
                        #endregion

                        #region  ltefdd采样点获取
                        else if (file.TestPoints[i] is LTEFddTestPoint)
                        {
                            if (file.TestPoints[i]["lte_fdd_ECI"] != null)
                            {
                                if (file.TestPoints[i + 1]["lte_fdd_ECI"] != null)
                                {
                                    if ((int)file.TestPoints[i]["lte_fdd_ECI"] == (int)file.TestPoints[i + 1]["lte_fdd_ECI"])
                                    {
                                        if (cellInfo == null)
                                        {
                                            cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                            cellInfo.AddPnt(file.TestPoints[i]);
                                            if (file.TestPoints[i].GetMainCell_LTE_FDD() != null)
                                            {
                                                cellInfo.CellName = file.TestPoints[i].GetMainCell_LTE_FDD().Name;
                                            }
                                            else
                                            {
                                                cellInfo.CellName = file.TestPoints[i]["lte_fdd_ECI"].ToString();
                                            }
                                        }
                                        else
                                        {
                                            cellInfo.AddPnt(file.TestPoints[i]);
                                            if (i == file.TestPoints.Count - 2)
                                            {
                                                cellInfo.AddPnt(file.TestPoints[i + 1]);
                                                cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                                fileInfo.CellInfoList.Add(cellInfo);
                                                cellInfo = null;
                                            }
                                        }
                                    }
                                    else if (cellInfo != null)
                                    {
                                        cellInfo.AddPnt(file.TestPoints[i]);
                                        cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                        fileInfo.CellInfoList.Add(cellInfo);
                                        cellInfo = null;
                                    }
                                    else if (cellInfo == null)
                                    {
                                        cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                        cellInfo.AddPnt(file.TestPoints[i]);
                                        cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                        if (file.TestPoints[i].GetMainCell_LTE_FDD() != null)
                                        {
                                            cellInfo.CellName = file.TestPoints[i].GetMainCell_LTE_FDD().Name;
                                        }
                                        else
                                        {
                                            cellInfo.CellName = file.TestPoints[i]["lte_fdd_ECI"].ToString();
                                        }
                                        fileInfo.CellInfoList.Add(cellInfo);
                                        cellInfo = null;
                                        if (i == file.TestPoints.Count - 2)
                                        {
                                            cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                            cellInfo.AddPnt(file.TestPoints[i + 1]);
                                            cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                            if (file.TestPoints[i + 1].GetMainCell_LTE_FDD() != null)
                                            {
                                                cellInfo.CellName = file.TestPoints[i + 1].GetMainCell_LTE_FDD().Name;
                                            }
                                            else
                                            {
                                                cellInfo.CellName = file.TestPoints[i + 1]["lte_fdd_ECI"].ToString();
                                            }
                                            fileInfo.CellInfoList.Add(cellInfo);
                                            cellInfo = null;
                                        }
                                    }
                                }
                                else if (cellInfo != null)
                                {
                                    cellInfo.AddPnt(file.TestPoints[i]);
                                    cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                    fileInfo.CellInfoList.Add(cellInfo);
                                    cellInfo = null;
                                }
                                else if (cellInfo == null)
                                {
                                    cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                    cellInfo.AddPnt(file.TestPoints[i]);
                                    cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                    if (file.TestPoints[i].GetMainCell_LTE_FDD() != null)
                                    {
                                        cellInfo.CellName = file.TestPoints[i].GetMainCell_LTE_FDD().Name;
                                    }
                                    else
                                    {
                                        cellInfo.CellName = file.TestPoints[i]["lte_fdd_ECI"].ToString();
                                    }
                                    fileInfo.CellInfoList.Add(cellInfo);
                                    cellInfo = null;
                                }
                            }
                            else if (file.TestPoints[i + 1]["lte_fdd_ECI"] != null && i == file.TestPoints.Count - 2)
                            {
                                cellInfo = new CellLastOccupyCellInfo(hoCondition);
                                cellInfo.AddPnt(file.TestPoints[i + 1]);
                                cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                                if (file.TestPoints[i + 1].GetMainCell_LTE_FDD() != null)
                                {
                                    cellInfo.CellName = file.TestPoints[i + 1].GetMainCell_LTE_FDD().Name;
                                }
                                else
                                {
                                    cellInfo.CellName = file.TestPoints[i + 1]["lte_fdd_ECI"].ToString();
                                }
                                fileInfo.CellInfoList.Add(cellInfo);
                                cellInfo = null;
                            }

                        }
                        #endregion
                    }
                    fileInfo.Sn = resultList.Count + 1;
                    resultList.Add(fileInfo);
            }
        }

        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息");
                return;
            }

            ZTMainCellLastOccupyResultForm frm = MainModel.CreateResultForm(typeof(ZTMainCellLastOccupyResultForm)) as ZTMainCellLastOccupyResultForm;
            frm.FillData(resultList);
            frm.Visible = true;
            frm.BringToFront();
            resultList = null;
        }
    }

    public class ZTMainCellLastOccupyBase_FDD : ZTMainCellLastOccupyBase
    {
        public ZTMainCellLastOccupyBase_FDD(MainModel mainModel)
            : base(mainModel)
        {
            this.Columns = new List<string>();
            this.IncludeMessage = true;
            this.Columns.Add("lte_fdd_TAC");
            this.Columns.Add("lte_fdd_ECI");
            this.Columns.Add("lte_fdd_RSRP");
            this.Columns.Add("lte_fdd_SINR");
            this.Columns.Add("lte_fdd_EARFCN");
            this.Columns.Add("lte_fdd_PCI");
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26045, this.Name);
        }
    }

    public class ZTMainCellLastOccupyWithTPSetCondition
    {
        public float RSRP { get; set; }
        public float SINR { get; set; }
        public short RxLev { get; set; }
        public byte RxQul { get; set; }
        public ZTMainCellLastOccupyWithTPSetCondition()
        {
            RSRP = -110;
            SINR = -3;
            RxLev = -90;
            RxQul = 5;
        }
    }

    public class CellLastOccupyFileInfo
    {
        public int Sn { get; set; }
        public FileInfo File { get; set; }
        public string FileName
        {
            get { return File.Name; }
        }
        public List<CellLastOccupyCellInfo> CellInfoList { get; set; } = new List<CellLastOccupyCellInfo>();
        public CellLastOccupyFileInfo(FileInfo file)
        {
            this.File = file;
        }
    }

    public class CellLastOccupyCellInfo
    {
        public int Sn { get; set; }

        public DTFileDataManager DTFile { get; set; }
        public double AvgRSRP
        {
            get
            {
                double sum = 0;
                int num = 0;
                foreach (TestPoint tp in TestPntList)
                {
                    getRSRP(ref sum, ref num, tp);
                }
                if (num != 0)
                {
                    return Math.Round(sum / num, 2);
                }
                else
                {
                    return 0;
                }
            }
        }

        private static void getRSRP(ref double sum, ref int num, TestPoint tp)
        {
            if (tp is LTETestPointDetail)
            {
                if (tp["lte_RSRP"] != null)
                {
                    sum += (float)(float?)tp["lte_RSRP"];
                    num++;
                }
            }
            else if (tp is TestPointDetail)
            {
                if (tp["RxLevSub"] != null)
                {
                    sum += (short)(short?)tp["RxLevSub"];
                    num++;
                }
            }
            else if (tp is LTEFddTestPoint)
            {
                if (tp["lte_fdd_RSRP"] != null)
                {
                    sum += (float)(float?)tp["lte_fdd_RSRP"];
                    num++;
                }
            }
            else
            {
                //
            }
        }

        public double AvgSINR
        {
            get
            {
                double sum = 0;
                int num = 0;
                foreach (TestPoint tp in TestPntList)
                {
                    getSINR(ref sum, ref num, tp);
                }
                if (num != 0)
                {
                    return Math.Round(sum / num, 2);
                }
                else
                {
                    return 0;
                }
            }
        }

        private static void getSINR(ref double sum, ref int num, TestPoint tp)
        {
            if (tp is LTETestPointDetail)
            {
                if (tp["lte_SINR"] != null)
                {
                    sum += (float)(float?)tp["lte_SINR"];
                    num++;
                }
            }
            else if (tp is TestPointDetail)
            {
                if (tp["RxQualSub"] != null)
                {
                    sum += (byte)(byte?)tp["RxQualSub"];
                    num++;
                }
            }
            else if (tp is LTEFddTestPoint)
            {
                if (tp["lte_fdd_SINR"] != null)
                {
                    sum += (float)(float?)tp["lte_fdd_SINR"];
                    num++;
                }
            }
            else
            {
                //
            }
        }

        public int SmallRSRPNum
        {
            get
            {
                int num = 0;
                foreach (TestPoint tp in TestPntList)
                {
                    num = getSmallRSRPNum(num, tp);
                }
                return num;
            }
        }

        private int getSmallRSRPNum(int num, TestPoint tp)
        {
            if (tp is LTETestPointDetail)
            {
                if (tp["lte_RSRP"] != null && (float)(float?)tp["lte_RSRP"] <= Condition.RSRP)
                {
                    num++;
                }
            }
            else if (tp is TestPointDetail)
            {
                if (tp["RxLevSub"] != null && (short)(short?)tp["RxLevSub"] <= Condition.RxLev)
                {
                    num++;
                }
            }
            else if (tp is LTEFddTestPoint)
            {
                if (tp["lte_fdd_RSRP"] != null && (float)(float?)tp["lte_fdd_RSRP"] <= Condition.RSRP)
                {
                    num++;
                }
            }
            else
            {
                //
            }

            return num;
        }

        public int SmallSINRNum
        {
            get
            {
                int num = 0;
                foreach (TestPoint tp in TestPntList)
                {
                    num = getSmallSINRNum(num, tp);
                }
                return num;
            }
        }

        private int getSmallSINRNum(int num, TestPoint tp)
        {
            if (tp is LTETestPointDetail)
            {
                if (tp["lte_SINR"] != null && (float)(float?)tp["lte_SINR"] <= Condition.SINR)
                {
                    num++;
                }
            }
            else if (tp is TestPointDetail)
            {
                if (tp["RxQualSub"] != null && (byte)(byte?)tp["RxQualSub"] >= Condition.RxQul)
                {
                    num++;
                }
            }
            else if (tp is LTEFddTestPoint)
            {
                if (tp["lte_fdd_SINR"] != null && (float)(float?)tp["lte_fdd_SINR"] <= Condition.SINR)
                {
                    num++;
                }
            }
            else
            {
                //
            }

            return num;
        }

        public List<TestPoint> TestPntList { get; set; } = new List<TestPoint>();

        public string CellName { get; set; }

        public TestPoint tStart { get { return TestPntList[0]; } }

        public TestPoint tEnd { get { return TestPntList[TestPntList.Count - 1]; } }
        public string Duration { get { return Math.Round(tEnd.DateTime.Subtract(tStart.DateTime).TotalSeconds, 2).ToString(); } }

        public double Longitude { get { return tStart.Longitude; } }

        public double Latitude { get { return tStart.Latitude; } }

        public string Time { get { return tStart.DateTime.ToString(); } }
        public string ECI
        {
            get
            {
                if (tStart is LTETestPointDetail)
                {
                    return tStart["lte_ECI"].ToString();
                }
                else if (tStart is TestPointDetail)
                {
                    return tStart["CI"].ToString();
                }
                else if (tStart is LTEFddTestPoint)
                {
                    return tStart["lte_fdd_ECI"].ToString();
                }
                return "";
            }
        }
        public string TAC
        {
            get
            {
                if (tStart is LTETestPointDetail)
                {
                    return tStart["lte_TAC"].ToString();
                }
                else if (tStart is TestPointDetail)
                {
                    return tStart["LAC"].ToString();
                }
                else if (tStart is LTEFddTestPoint)
                {
                    return tStart["lte_fdd_TAC"].ToString();
                }
                return "";
            }
        }

        public CellLastOccupyCellInfo(ZTMainCellLastOccupySetCondition condition)
        {
            this.Condition = condition;
        }

        public void AddPnt(TestPoint tp)
        {
            TestPntList.Add(tp);
        }

        private ZTMainCellLastOccupySetCondition Condition { get; set; }
    }
}
