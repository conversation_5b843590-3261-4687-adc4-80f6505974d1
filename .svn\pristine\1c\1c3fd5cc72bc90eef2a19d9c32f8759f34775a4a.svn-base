﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public abstract class AreaTestCondition
    {
        public int Permeate { get; set; }
        
        public Color ColorAchieve { get; set; }
        
        public Color ColorUnAchieve { get; set; }

        protected AreaTestCondition()
        {
            this.Permeate = 10;
            this.ColorAchieve = Color.Green;
            this.ColorUnAchieve = Color.Red;
        }

        public void SetContext(int permeate, Color colorAch, Color colorUnAch)
        {
            this.Permeate = permeate;
            this.ColorAchieve = colorAch;
            this.ColorUnAchieve = colorUnAch;
        }

        public Color GetShowColor(bool bAchieve)
        {
            if (bAchieve)
                return ColorAchieve;
            return ColorUnAchieve;
        }

        public abstract void CheckAchieve(CPermeate permeate, bool isVillage);

        public static string GetFormulaCarrier(ECarrier serCar)
        {
            string formula = "";
            switch (serCar)
            {
                case ECarrier.移动:
                    formula = "Mx_0807+Tx_0807+Lte_0807";
                    break;
                case ECarrier.联通:
                    formula = "Mx_0807+Wx_0807+Lf_0807";
                    break;
                case ECarrier.电信:
                    formula = "Cx_0807+Ex_0807+Lf_0807";
                    break;
                default:
                    break;
            }
            return formula;
        }
    }

    //public class CAreaPair
    //{
    //    public int IAreaType { get; set; }
    //    public int IAreaID { get; set; }

    //    public CAreaPair(int type, int id)
    //    {
    //        this.IAreaType = type;
    //        this.IAreaID = id;
    //    }

    //    public override bool Equals(object obj)
    //    {
    //        if (this.GetType() != obj.GetType()) return false;
    //        CAreaPair area = obj as CAreaPair;

    //        return area != null && area.IAreaID == this.IAreaID && area.IAreaType == this.IAreaType;
    //    }

    //    public override int GetHashCode()
    //    {
    //        return string.Format("{0}{1}", IAreaType, IAreaID).GetHashCode();
    //    }
    //}
}
