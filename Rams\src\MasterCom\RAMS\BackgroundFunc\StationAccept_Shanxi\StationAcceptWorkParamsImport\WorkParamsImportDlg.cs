﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public partial class WorkParamsImportDlg : BaseDialog
    {
        public WorkParamsImportCondtion Condtion { get; set; } = new WorkParamsImportCondtion();
        public WorkParamsImportDlg(WorkParamsImportCondtion condtion)
        {
            InitializeComponent();
            if (condtion == null)
            {
                dPSTime.Value = DateTime.Now.Date.AddMonths(-1);
                dPETime.Value = DateTime.Now.Date;
            }
            else
            {
                dPSTime.Value = condtion.StartDate;
                dPETime.Value = condtion.EndDate;
            }
            chbImportType.SelectedIndex = 0;
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            OpenFileDialog openDlg = new OpenFileDialog();
            openDlg.Filter = "Excel|*.xlsx;*.xls";
            openDlg.Title = "请选择单验工参表";
            if (openDlg.ShowDialog() == DialogResult.OK)
            {
                txtBox.Text = openDlg.FileName;
            }
        }

        #region 导出工参模板
        private void btnDownLoad_Click(object sender, EventArgs e)
        {
            List<ExportToExcelModel> lsData = new List<ExportToExcelModel>();
            ExportToExcelModel sheetDevice = null;
            if (curNetType == NetType.LTE)
            {
                sheetDevice = getLteModel();
            }
            else if (curNetType == NetType.NR)
            {
                sheetDevice = getNrModel();
            }
            lsData.Add(sheetDevice);
            ExcelNPOIManager.ExportToExcelMore(lsData);
        }

        private static ExportToExcelModel getLteModel()
        {
            ExportToExcelModel sheetDevice = new ExportToExcelModel();
            sheetDevice.SheetName = "4G单验工参表";
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.AddCellValue("地市");
            nr.AddCellValue("NODE名称");
            nr.AddCellValue("NodeBID");
            nr.AddCellValue("经度");
            nr.AddCellValue("纬度");
            nr.AddCellValue("覆盖类型");
            nr.AddCellValue("小区名称");
            nr.AddCellValue("CellID");
            nr.AddCellValue("SectorID");
            nr.AddCellValue("TAC");
            nr.AddCellValue("PCI");
            nr.AddCellValue("频点");
            nr.AddCellValue("方向角");
            nr.AddCellValue("下倾角");
            nr.AddCellValue("挂高");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("咸阳");
            nr.AddCellValue("咸阳秦都财政局-HLH-XYAO013TND");
            nr.AddCellValue("684156");
            nr.AddCellValue("108.691944");
            nr.AddCellValue("34.344166");
            nr.AddCellValue("室外");
            nr.AddCellValue("咸阳秦都财政局-HLH-XYAO013TND-40");
            nr.AddCellValue("198");
            nr.AddCellValue("198");
            nr.AddCellValue("37348");
            nr.AddCellValue("423");
            nr.AddCellValue("40936");
            nr.AddCellValue("60");
            nr.AddCellValue("5");
            nr.AddCellValue("18");
            rows.Add(nr);

            sheetDevice.Data = rows;
            return sheetDevice;
        }

        private static ExportToExcelModel getNrModel()
        {
            ExportToExcelModel sheetDevice = new ExportToExcelModel();
            sheetDevice.SheetName = "5G单验工参表";
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.AddCellValue("地市");
            nr.AddCellValue("站名");
            nr.AddCellValue("站号");
            nr.AddCellValue("经度");
            nr.AddCellValue("纬度");
            nr.AddCellValue("宏微");
            nr.AddCellValue("小区标识");
            nr.AddCellValue("小区名称");
            nr.AddCellValue("TAC");
            nr.AddCellValue("频点");
            nr.AddCellValue("PCI");
            nr.AddCellValue("SSB频域位置");
            nr.AddCellValue("CGI");
            nr.AddCellValue("方向角");
            nr.AddCellValue("下倾角");
            nr.AddCellValue("挂高");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("安康");
            nr.AddCellValue("安康汉滨锦江商务酒店-HNH-AKAO052NTTD");
            nr.AddCellValue("2394297");
            nr.AddCellValue("109.02277");
            nr.AddCellValue("32.68972");
            nr.AddCellValue("宏站");
            nr.AddCellValue("0");
            nr.AddCellValue("安康汉滨锦江商务酒店-HNH-AKAO052NTTD-0");
            nr.AddCellValue("7473445");
            nr.AddCellValue("513000");
            nr.AddCellValue("3");
            nr.AddCellValue("504990");
            nr.AddCellValue("460-00-2394297-0");
            nr.AddCellValue("60");
            nr.AddCellValue("3");
            nr.AddCellValue("33");
            rows.Add(nr);

            sheetDevice.Data = rows;
            return sheetDevice;
        }
        #endregion

        #region 导入工参
        private bool importSuccess = false;
        private string errorInfo = "";
        private NetType curNetType = NetType.LTE;

        private void chbImportType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (chbImportType.SelectedItem.ToString() == "4G")
            {
                curNetType = NetType.LTE;
            }
            else if (chbImportType.SelectedItem.ToString() == "5G")
            {
                curNetType = NetType.NR;
            }
        }

        private void btnImport_Click(object sender, EventArgs e)
        {
            string file = txtBox.Text;
            if (!File.Exists(file))
            {
                MessageBox.Show(file + "\r\n不存在！", "提示");
                return;
            }

            WaitBox.Show("正在读取并导入单验工参...", ImportWorkParamsInfo, file);

            if (importSuccess)
            {
                MessageBox.Show("工参导入成功！");
            }
            else
            {
                MessageBox.Show(errorInfo);
            }
        }

        private void ImportWorkParamsInfo(object obj)
        {
            string fileName = obj as string;
            var workParams = new Dictionary<string, Dictionary<string, CellAcceptWorkParam_SX>>();
            //读取Excel
            bool isValid = getWorkParamFormExcle(fileName, workParams);
            if (isValid)
            {
                //插入到数据库
                WorkParamsImportQuery query = new WorkParamsImportQuery(workParams, curNetType);
                query.Query();
                importSuccess = true;
            }

            WaitBox.Close();
        }

        private bool getWorkParamFormExcle(string fileName, Dictionary<string, Dictionary<string, CellAcceptWorkParam_SX>> workParams)
        {
            try
            {
                StringBuilder strbErrorInfo = new StringBuilder();
                DataTable tb = getValidDataTable(strbErrorInfo, fileName);
                if (tb == null)
                {
                    return false;
                }

                int index = 0;
                foreach (DataRow row in tb.Rows)
                {
                    index++;
                    try
                    {
                        CellAcceptWorkParam_SX workParam = setWorkParams(row);
                        if (!workParams.TryGetValue(workParam.BtsNameFull, out var workParamDic))
                        {
                            workParamDic = new Dictionary<string, CellAcceptWorkParam_SX>();
                            workParams.Add(workParam.BtsNameFull, workParamDic);
                        }
                        if (!workParamDic.ContainsKey(workParam.CellNameFull))
                        {
                            workParamDic.Add(workParam.CellNameFull, workParam);
                        }
                    }
                    catch (Exception e)
                    {
                        strbErrorInfo.AppendLine("第" + (index + 1) + "行工参信息配置错误!   " + e.Message);
                        break;
                    }
                }
                if (workParams.Count >= 200)
                {
                    errorInfo = "导入待验收站点工参过多，请确认是否都是待验收站点！";
                    return false;
                }
                if (strbErrorInfo.Length > 0)
                {
                    errorInfo = strbErrorInfo.ToString();
                    return false;
                }
            }
            catch (Exception ex)
            {
                errorInfo = ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace;
                return false;
            }
            return true;
        }

        private DataTable getValidDataTable(StringBuilder strbErrorInfo, string fileName)
        {
            int reReadCount = 0;
            while (reReadCount < 3 && FileStatus.FileIsOpen(fileName) == 1)
            {
                System.Threading.Thread.Sleep(5000);
                reReadCount++;
            }

            DataTable tb;
            using (DataSet dataSet = ExcelNPOIManager.ImportFromExcel(fileName))
            {
                if (dataSet == null || dataSet.Tables.Count <= 0)
                {
                    strbErrorInfo.AppendLine("Excel中无数据");
                    return null;
                }
                tb = dataSet.Tables[0];
                if (tb == null || tb.Rows.Count <= 0)
                {
                    strbErrorInfo.AppendLine("表中无数据");
                    return null;
                }

                removeEmpty(tb);
            }

            return tb;
        }

        protected void removeEmpty(DataTable dt)
        {
            List<DataRow> removelist = new List<DataRow>();
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                bool rowdataisnull = true;
                for (int j = 0; j < dt.Columns.Count; j++)
                {
                    if (!string.IsNullOrEmpty(dt.Rows[i][j].ToString().Trim()))
                    {
                        rowdataisnull = false;
                    }
                }
                if (rowdataisnull)
                {
                    removelist.Add(dt.Rows[i]);
                }
            }
            for (int i = 0; i < removelist.Count; i++)
            {
                dt.Rows.Remove(removelist[i]);
            }
        }

        private CellAcceptWorkParam_SX setWorkParams(DataRow row)
        {
            //根据工参模板读取
            CellAcceptWorkParam_SX info;
            if (curNetType == NetType.LTE)
            {
                info = new CellAcceptWorkParam_SX();
            }
            else if (curNetType == NetType.NR)
            {
                info = new CellAcceptWorkParam_SX_NR();
            }
            else
            {
                return null;
            }
               
            info.FillDataByExcel(row);
            if (string.IsNullOrEmpty(info.DistrictName))
            {
                throw (new Exception("地市名为空"));
            }
            return info;
        }
        #endregion

        private void btnOK_Click(object sender, EventArgs e)
        {
            Condtion.StartDate = dPSTime.Value.Date;
            Condtion.EndDate = dPETime.Value.Date;

            Condtion.Type = curNetType;

            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class WorkParamsImportCondtion
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public NetType Type { get; set; } = NetType.LTE;
    }
}
