﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteDriveSpeedWithKpiAnaByRegion : LteDriveSpeedWithKpiAnaBase
    {
        protected LteDriveSpeedWithKpiAnaByRegion()
            : base()
        {
        }

        private static LteDriveSpeedWithKpiAnaByRegion intance = null;
        public static LteDriveSpeedWithKpiAnaByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LteDriveSpeedWithKpiAnaByRegion();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "指标与车速分析(按区域)"; }
        }

    }

    public class LteDriveSpeedWithKpiAnaByRegion_FDD : LteDriveSpeedWithKpiAnaByRegion
    {
        private static LteDriveSpeedWithKpiAnaByRegion_FDD instance = null;
        public static new LteDriveSpeedWithKpiAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteDriveSpeedWithKpiAnaByRegion_FDD();
                    }
                }
            }
            return instance;
        }
        protected LteDriveSpeedWithKpiAnaByRegion_FDD()
            : base()
        {

        }
        public override string Name
        {
            get { return "指标与车速分析LTE_FDD(按区域)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26064, this.Name);
        }
    }
}
