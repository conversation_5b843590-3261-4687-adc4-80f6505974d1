﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class OverCoverCellBase
    {
        public virtual void Init(double altitude, double downward, double idealCoverDis
            , double overFactor, string nearBtsNames)
        {
            IdealCoverDistance = Math.Round(idealCoverDis, 2);
            OverDistance = Math.Round(idealCoverDis * overFactor, 2);
            NearestBtsNames = nearBtsNames;
            if (altitude != 0 && altitude != 999)
            {
                double dir = 90 + downward / 2 - Math.Atan(idealCoverDis / altitude) * 180 / Math.PI;
                CellDownDirSuggest = Math.Round(dir, 1).ToString();
            }
            else
            {
                CellDownDirSuggest = "无挂高信息，无法计算建议下倾角";
            }
        }

        public AvgInfo Rsrp { get; protected set; } = new AvgInfo();
        public AvgInfo Sinr { get; protected set; } = new AvgInfo();
        public AvgInfo Distance { get; protected set; } = new AvgInfo();

        public RateInfo OverCell { get; protected set; } = new RateInfo();

        public double OverDistance { get; protected set; }
        public string NearestBtsNames { get; protected set; }

        public List<TestPoint> OverCoverPoints { get; protected set; } = new List<TestPoint>();

        public double IdealCoverDistance { get; protected set; }

        public int CellID { get; protected set; }
        public string CellName { get; protected set; }
        public int CellAltitude { get; protected set; }
        public short CellDownDir { get; protected set; }
        public string CellDownDirSuggest { get; protected set; }

        public void AddTestPoint(TestPoint tp, int idx, float rsrp, double distance)
        {
            OverCell.TotalCount++;
            //TestPointNum++;
            if (distance >= OverDistance)
            {
                OverCell.Count++;
                OverCoverPoints.Add(tp);

                Distance.Add(distance);
                Rsrp.Add(rsrp);

                float? sinr = NRTpHelper.NrScanTpManager.GetCellSinr(tp, idx, true);
                if (sinr != null)
                {
                    Sinr.Add(sinr);
                }
            }
        }

        public virtual void MakeSummary()
        {
            Distance.Calculate();
            Rsrp.Calculate();
            Sinr.Calculate();

            OverCell.Calculate(true);
        }
    }

    public class LteOverCoverCell : OverCoverCellBase
    {
        public LTECell Cell { get; protected set; }

        public LteOverCoverCell(LTECell cell, double idealCoverDis, double overFactor, string nearBtsNames)
        {
            Cell = cell;
            Init(cell.Altitude, cell.Downward, idealCoverDis, overFactor, nearBtsNames);
        }

        public override void MakeSummary()
        {
            base.MakeSummary();

            CellID = Cell.SCellID;
            CellName = Cell.Name;
            CellAltitude = Cell.Altitude;
            CellDownDir = Cell.Downward;
        }

        public static void RefreashGis(MainModel mainModel, object sender, List<OverCoverCellBase> cells)
        {
            mainModel.ClearDTData();
            mainModel.SelectedLTECells.Clear();
            List<TestPoint> points = new List<TestPoint>();
            foreach (var cell in cells)
            {
                LteOverCoverCell lteCell = cell as LteOverCoverCell;
                mainModel.SelectedLTECells.Add(lteCell.Cell);
                foreach (TestPoint tp in cell.OverCoverPoints)
                {
                    if (!points.Contains(tp))
                    {
                        points.Add(tp);
                    }
                }
            }
            foreach (TestPoint tp in points)
            {
                mainModel.DTDataManager.Add(tp);
            }
            mainModel.FireDTDataChanged(sender);
            mainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
        }
    }

    public class NrOverCoverCell : OverCoverCellBase
    {
        public NRCell Cell { get; protected set; }

        public NrOverCoverCell(NRCell cell, double idealCoverDis, double overFactor, string nearBtsNames)
        {
            Cell = cell;
            Init(cell.Altitude, cell.Downward, idealCoverDis, overFactor, nearBtsNames);
        }

        public override void MakeSummary()
        {
            base.MakeSummary();

            CellID = Cell.CellID;
            CellName = Cell.Name;
            CellAltitude = Cell.Altitude;
            CellDownDir = Cell.Downward;
        }

        public static void RefreashGis(MainModel mainModel, object sender, List<OverCoverCellBase> cells)
        {
            mainModel.ClearDTData();
            mainModel.SelectedNRCells.Clear();
            setFlyLine(mainModel);
            List<TestPoint> points = new List<TestPoint>();
            foreach (var cell in cells)
            {
                NrOverCoverCell nrCell = cell as NrOverCoverCell;
                mainModel.SelectedNRCells.Add(nrCell.Cell);
                foreach (TestPoint tp in cell.OverCoverPoints)
                {
                    if (!points.Contains(tp))
                    {
                        points.Add(tp);
                    }
                }
            }
            foreach (TestPoint tp in points)
            {
                mainModel.DTDataManager.Add(tp);
            }
            mainModel.FireDTDataChanged(sender);
            mainModel.FireSetDefaultMapSerialTheme(NRTpHelper.NrScanTpManager.RsrpFullThemeName);
        }

        private static void setFlyLine(MainModel mainModel)
        {
            string serialInfoName = NRTpHelper.NrScanTpManager.RsrpThemeName;
            var layer = mainModel.MainForm.GetMapForm().GetDTLayer();
            foreach (var serialInfo in layer.SerialInfos)
            {
                if (serialInfo.Name.Equals(serialInfoName))
                {
                    layer.CurFlyLinesSerialInfo = serialInfo;
                }
            }
            mainModel.DrawFlyLines = true;
        }
    }

}
