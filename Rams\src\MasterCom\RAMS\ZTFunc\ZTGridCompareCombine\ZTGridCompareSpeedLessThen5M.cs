﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;

namespace MasterCom.RAMS.Net
{
    public class ZTGridCompareSpeedLessThen5M : ZTGridCompareCombineBase
    {
        public ZTGridCompareSpeedLessThen5M(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "移动低速率路段(按栅格低于5M)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22089, this.Name);
        }

        /// <summary>
        /// 设置查询指标
        /// </summary>
        /// <returns></returns>
        protected override bool getConditionBeforeQuery()
        {
            if (!condition.CarrierTypes.Contains(1) || condition.CarrierTypes.Count > 1)
            {
                MessageBox.Show("运营商选择有误，此功能只能进行移动运营商");
                return false;
            }
            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            this.imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            List<string> formulaSet = new List<string>();
            formulaSet.Add(strTDDDownTime);
            formulaSet.Add(strTDDDownSize);
            statImgIDSet = getTriadIDIgnoreServiceType(formulaSet);
            return true;
        }
        /// <summary>
        /// 重写对比过程
        /// </summary>
        protected override void doCompare()
        {
            gridCountInfo = new GridCountInfo();
            //gridCountInfo.IAllGridCount = MainModel.CurGridColorUnitMatrix.Grids.Count;
            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(cu.CenterLng, cu.CenterLat);
                GridTypeName gridName = strContainDbRect(grid.Bounds);
                if (gridName.strGridType == "" || gridName.strGridName == "")
                {
                    continue;
                }
                gridCountInfo.IAllGridCount++;
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);

                StatDataLTE dataStatTDD = cu.DataHub.GetStatData(typeof(StatDataLTE)) as StatDataLTE;
                if (dataStatTDD == null)
                {
                    cu.DataHub = null;
                    continue;
                }
                gridCountInfo.IHostGridCount++;
                double dHostDownTime = cu.DataHub.CalcValueByFormula(strTDDDownTime);
                double dHostDownSize = cu.DataHub.CalcValueByFormula(strTDDDownSize);
                if (dHostDownTime <= 0)
                {
                    continue;
                }

                gridCountInfo.ICompareGridCount++;

                double dHostDownSpeed = 0;
                if (dHostDownTime > 0 && dHostDownSize >= 0)
                {
                    dHostDownSpeed = dHostDownSize / dHostDownTime;
                }

                if (dHostDownSpeed >= 5)
                {
                    continue;
                }
                GridColorUnit griCu = new GridColorUnit();
                griCu.CuUnit = cu;
                griCu.DHostSpeed = dHostDownSpeed;
                griCu.DHostMo = dHostDownSize;
                griCu.DHostBase = dHostDownTime;
                griCu.CuUnit.DataHub = null;//降低内存
                if (!gridColorUnit.ContainsKey(gridName))
                {
                    gridColorUnit[gridName] = new GridMatrix<GridColorUnit>();
                }
                gridColorUnit[gridName][rAt, cAt] = griCu;
                cu.DataHub = null;
            }
        }
        /// <summary>
        /// 重写转化过程
        /// </summary>
        protected override GridCompareCombineInfo doChangGridCompareResult(GridCompareCombineBlock block)
        {
            if (block.Grids.Count == 0)
            {
                return null;
            }
            GridCompareCombineInfo gridItem = new GridCompareCombineInfo();
            double sumLng = 0;
            double sumLat = 0;
            double sumHostMo = 0;
            double sumHostBase = 0;
            StringBuilder sbLng = new StringBuilder();
            StringBuilder sbLat = new StringBuilder();
            foreach (GridColorUnit gridCU in block.Grids)
            {
                sumLng += gridCU.CuUnit.CenterLng;
                sumLat += gridCU.CuUnit.CenterLat;
                sbLng.Append(gridCU.CuUnit.CenterLng + ";");
                sbLat.Append(gridCU.CuUnit.CenterLat + ";");
                sumHostMo += gridCU.DHostMo;
                sumHostBase += gridCU.DHostBase;

                setStrFileName(gridItem, gridCU);
            }
            gridItem.StrLngList += sbLng.ToString();
            gridItem.StrLatList += sbLat.ToString();

            double dHostMeanVale = -999;
            if (sumHostBase != 0)
            {
                dHostMeanVale = sumHostMo / sumHostBase;
            }

            gridItem.StrCompareInfo = "低于5M";
            gridItem.StrProblemInfo = "移动下载速率：" + ((int)(dHostMeanVale * 100) / 100.0).ToString() + "M，连续栅格个数：" + block.Grids.Count + "个";
            gridItem.DLng = sumLng / block.Grids.Count;
            gridItem.DLat = sumLat / block.Grids.Count;
            gridItem.StrProblemType = "速率问题";
            return gridItem;
        }

        private void setStrFileName(GridCompareCombineInfo gridItem, GridColorUnit gridCU)
        {
            CenterLongLat cll = new CenterLongLat(gridCU.CuUnit.CenterLng, gridCU.CuUnit.CenterLat);
            if (gridFileNameListDic.ContainsKey(cll))
            {
                StringBuilder sbName = new StringBuilder();
                foreach (string strFileName in gridFileNameListDic[cll])
                {
                    if (strFileName.Contains("上传"))
                    {
                        continue;
                    }
                    if (!sbName.ToString().Contains(strFileName))
                    {
                        sbName.Append(strFileName + ";");
                    }
                }
                gridItem.StrFileName = sbName.ToString();
            }
        }
    }
}
