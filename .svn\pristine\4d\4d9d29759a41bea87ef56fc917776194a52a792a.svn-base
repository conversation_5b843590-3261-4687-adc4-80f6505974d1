﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.NOP
{
    public partial class RepeatStatSettingForm : BaseDialog
    {
        public RepeatStatSettingForm()
        {
            InitializeComponent();
            this.btnCancel.Click += BtnCancel_Click;
            this.btnOK.Click += BtnOK_Click;

            DateTime dtNow = DateTime.Now.Date;
            dtSummaryBegin.Value = dtNow.AddDays(1 - dtNow.Day); // 月初
            dtSummaryEnd.Value = dtSummaryBegin.Value.AddMonths(1).AddDays(-1); // 月末
            dtHistoryBegin.Value = dtNow.AddDays(1 - dtNow.Day).AddMonths(1 - dtNow.Month); // 年初
            dtHistoryEnd.Value = dtSummaryBegin.Value.AddDays(-1); // 上月末
        }

        public RepeatStatCondition GetCondition()
        {
            RepeatStatCondition cond = new RepeatStatCondition();
            cond.TpSummary = new TimePeriod(dtSummaryBegin.Value, dtSummaryEnd.Value.AddDays(1).AddSeconds(-1));
            cond.TpHistory = new TimePeriod(dtHistoryBegin.Value, dtHistoryEnd.Value.AddDays(1).AddSeconds(-1));
            cond.GatherRadius = (double)numRadius.Value;
            return cond;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (dtSummaryEnd.Value < dtSummaryBegin.Value
                || dtHistoryEnd.Value < dtHistoryBegin.Value)
            {
                MessageBox.Show("开始时间不能大于结束时间", this.Text, MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = System.Windows.Forms.DialogResult.None;
                return;
            }

            DialogResult = System.Windows.Forms.DialogResult.OK;
        }
    }
}
