﻿using System;
using System.Collections.Generic;
using System.Text;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Func
{
    public class ZTDiyQueryLteEdgeSpeedAnaData : DIYAnalyseByPeriodBackgroundBase_Sample
    {
        protected static readonly object lockObj = new object();
        public ZTDiyQueryLteEdgeSpeedAnaData(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }
        public override string Name
        {
            get { return "边缘下载速率分析_LTE(按采样点)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(3, 13000, 13055, "查询");
        }

        #region 全局变量
        public Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic { get; set; }
        ZTDiyQueryLteEdgeSpeedAnaSetForm ztLteEdgeSpeedAnaForm = null;
        public Dictionary<string, Dictionary<string, List<float>>> cityGridTypeNameEdgeSpeedDic { get; set; }
        public List<LteEdgeSpeedInfo> lteEdgeSpeedInfoList { get; set; }
        protected string strCityName = "";
        protected LteEdgeSpeedCondtion curCondition = new LteEdgeSpeedCondtion();
        protected LteEdgeSpeedAnaHelper helper = new LteEdgeSpeedAnaHelper();
        #endregion

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_APP_Average_Speed";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_APP_ThroughputDL";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_APP_ThroughputDL_Mb";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_APP_type";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LteEdgeSpeed");
            tmpDic.Add("themeName", (object)"lte_APP_ThroughputDL_Mb");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            mutRegionMopDic = EdgeSpeedAnaHelper.InitRegionMop2();
            cityGridTypeNameEdgeSpeedDic = new Dictionary<string, Dictionary<string, List<float>>>();
            lteEdgeSpeedInfoList = new List<LteEdgeSpeedInfo>();
            
            try
            {
                int iCityIdTmp = MainModel.DistrictID;
                foreach (int iCityId in Condition.DistrictIDs)
                {
                    ClientProxy clientProxy = new ClientProxy();
                    try
                    {
                        MainModel.DistrictID = iCityId;
                        strCityName = DistrictManager.GetInstance().getDistrictName(iCityId);
                        if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                        {
                            ErrorInfo = "连接服务器失败!";
                            return;
                        }
                        MainModel.ClearDTData();
                        WaitBox.CanCancel = true;
                        WaitBox.Text = "正在查询...";
                        WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                    }
                    catch
                    {
                        clientProxy.Close();
                    }
                }
                MainModel.DistrictID = iCityIdTmp;

                lteEdgeSpeedInfoList = GetResultAfterAllQuery(cityGridTypeNameEdgeSpeedDic);

                FireShowFormAfterQuery();
            }
            catch
            {
                //continue
            }
            finally
            {
                MainModel.ClearDTData();
            }
        }

        protected override bool getConditionBeforeQuery()
        {
            if (ztLteEdgeSpeedAnaForm == null)
            {
                ztLteEdgeSpeedAnaForm = new ZTDiyQueryLteEdgeSpeedAnaSetForm();
            }
            if (ztLteEdgeSpeedAnaForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curCondition.EdgeSpeedThreshold = ztLteEdgeSpeedAnaForm.FSpeedRate;
            return true;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude) && tp is LTETestPointDetail)
                {
                    dealTestPoint(tp);
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealTestPoint(TestPoint tp)
        {
            object dValue = tp["lte_APP_ThroughputDL_Mb"];

            if (tp["lte_APP_type"] != null && (short?)tp["lte_APP_type"] == 2
                && dValue != null && double.Parse(dValue.ToString()) > -1)
            {
                float dEdgeSpeed = float.Parse(dValue.ToString());
                helper.AddValidData(tp.Longitude, tp.Latitude, dEdgeSpeed, strCityName, mutRegionMopDic, cityGridTypeNameEdgeSpeedDic);
            }
        }

        public List<LteEdgeSpeedInfo> GetResultAfterAllQuery(Dictionary<string, Dictionary<string, List<float>>> cityGridTypeNameEdgeSpeedDicTem)
        {
            List<EdgeSpeedInfo> infoList = helper.GetResultAfterAllQuery(cityGridTypeNameEdgeSpeedDicTem, curCondition.EdgeSpeedThreshold);
            List<LteEdgeSpeedInfo> lteEdgeSpeedInfoListTem = new List<LteEdgeSpeedInfo>();
            foreach (var info in infoList)
            {
                lteEdgeSpeedInfoListTem.Add(info as LteEdgeSpeedInfo);
            }
            return lteEdgeSpeedInfoListTem;
        }

        protected override void FireShowFormAfterQuery()
        {
            ZTDiyQueryLteEdgeSpeedAnaDataForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(ZTDiyQueryLteEdgeSpeedAnaDataForm).FullName);
            showForm = obj == null ? null : obj as ZTDiyQueryLteEdgeSpeedAnaDataForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new ZTDiyQueryLteEdgeSpeedAnaDataForm(MainModel, "有效采样点数");
            }
            showForm.FillData(lteEdgeSpeedInfoList);
            showForm.Show(MainModel.MainForm);
        }       
    }

    public class ZTDiyQueryLteEdgeSpeedAnaData_FDD : ZTDiyQueryLteEdgeSpeedAnaData
    {
        private static ZTDiyQueryLteEdgeSpeedAnaData_FDD instance = null;
        public static ZTDiyQueryLteEdgeSpeedAnaData_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDiyQueryLteEdgeSpeedAnaData_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public ZTDiyQueryLteEdgeSpeedAnaData_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "边缘下载速率分析_LTE_FDD(按采样点)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26059, "查询");
        }
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_APP_Average_Speed";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_APP_ThroughputDL";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_APP_ThroughputDL_Mb";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_APP_type";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LtefddEdgeSpeed");
            tmpDic.Add("themeName", (object)"lte_fdd_APP_ThroughputDL_Mb");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude) && tp is LTEFddTestPoint)
                {
                    dealTestPoint(tp);
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealTestPoint(TestPoint tp)
        {
            object dValue = tp["lte_fdd_APP_ThroughputDL_Mb"];

            if (tp["lte_fdd_APP_type"] != null && (short?)tp["lte_fdd_APP_type"] == 2
                && dValue != null && double.Parse(dValue.ToString()) > -1)
            {
                float dEdgeSpeed = float.Parse(dValue.ToString());
                helper.AddValidData(tp.Longitude, tp.Latitude, dEdgeSpeed, strCityName, mutRegionMopDic, cityGridTypeNameEdgeSpeedDic);
            }
        }
    }
}
