﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.CoverageCheck;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.Util.UiEx;
using MapWinGIS;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Compare_Foreground;
using BrightIdeasSoftware;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.ZTFunc.GridOrderCommon;

namespace MasterCom.RAMS
{
    public partial class CPGridCellShowForm : MinCloseForm
    {
        private QueryCondition condition;
        private QueryCondition conditionTPHost;
        private QueryCondition condtionTPGuest;
        private MainModel mainModel;
        protected MapFormItemSelection ItemSelection;
        private CompareMode compareConfig;
        private string fillLvDeSign = "全部";
        private CorrectCell correctCells = new CorrectCell();                                      //小区集合
        private Dictionary<string, CellGroup> cellDetails = new Dictionary<string, CellGroup>();   //栅格小区集合
        private int countGridCells = 0;                                                            //栅格小区总数
        private List<string> nowColorItems = new List<string>();                                   //当前选择的竟对模式的颜色集合
        public static List<string> chosedWarnings { get; set; } = new List<string>();                            //选择的差异条件
        List<OLVColumn> addtionalDisCols = new List<OLVColumn>();

        public CPGridCellShowForm(MainModel mainModel, MapFormItemSelection ItemSelection, QueryCondition condition,
            QueryCondition conditionTPHost, QueryCondition conditionTPGuest, CompareParam SelectedcompareParam, CompareMode compareModel)
            : base(mainModel)
        {
            InitializeComponent();
            listView.ListViewItemSorter = new ListViewSorter(listView);
            this.mainModel = mainModel;
            this.ItemSelection = ItemSelection;
            this.condition = condition;
            this.conditionTPHost = conditionTPHost;
            this.condtionTPGuest = conditionTPGuest;
            compareConfig = compareModel;

            List<string> list = new List<string>();
            foreach (CPModeColorItem item in SelectedcompareParam.AlgorithmCfg.colorItemList)
            {
                list.Add(item.colorRange.description);
            }
            nowColorItems = list;
            fillDisplayItem(SelectedcompareParam);

            initUI();
            Start(SelectedcompareParam);
        }

        private void initUI()
        {
            addCellGroupInfo();

            addCellDetailInfo();
        }

        private void addCellDetailInfo()
        {
            //小区所在栅格左上角经纬度
            olvGrid.AspectGetter += delegate (object row)
            {
                if (row is CellDetail)
                {
                    CellDetail cellDetail = row as CellDetail;
                    if (cellDetail.Grid != null)
                        return cellDetail.Grid.LTLng.ToString() + " , " + cellDetail.Grid.LTLat.ToString();
                }
                return null;
            };

            addCellInfo();

            //栅格小区详细信息-主队信号强度与质量
            olvCellQualityHost.AspectGetter += delegate (object row)
            {
                if (row is CellDetail)
                {
                    CellDetail cellDetail = row as CellDetail;
                    if (cellDetail.CellDataHost != null)
                        return cellDetail.CellDataHost.GetValue(GridCellDataHub.strFormula);
                }
                return null;
            };

            //栅格小区详细信息-客队信号强度与质量
            olvCellQualityGuest.AspectGetter += delegate (object row)
            {
                if (row is CellDetail)
                {
                    CellDetail cellDetail = row as CellDetail;
                    if (cellDetail.CellDataGuest != null)
                        return cellDetail.CellDataGuest.GetValue(GridCellDataHub.strFormula);
                }
                return null;
            };


            //栅格小区详细信息-主客差值
            olvColHostGuestDiff.AspectGetter += delegate (object row)
            {
                if (row is CellDetail)
                {
                    CellDetail cellDetail = row as CellDetail;
                    if (cellDetail.CellDataHost != null && cellDetail.CellDataGuest != null
                        && cellDetail.CellDataHost.GetValue(GridCellDataHub.strFormula) != 0
                        && cellDetail.CellDataGuest.GetValue(GridCellDataHub.strFormula) != 0)
                        return Math.Round((cellDetail.CellDataHost.GetValue(GridCellDataHub.strFormula)
                            - cellDetail.CellDataGuest.GetValue(GridCellDataHub.strFormula)), 3).ToString();
                }
                return null;
            };
        }

        private void addCellInfo()
        {
            //栅格小区详细信息-小区名
            olvCell.AspectGetter += delegate (object row)
            {
                if (row is CellDetail)
                {
                    CellDetail cellDetail = row as CellDetail;
                    if (cellDetail.Cell != null)
                        return cellDetail.Cell.Name;
                }
                return null;
            };

            //栅格小区详细信息-小区经度
            olvCellLongitude.AspectGetter += delegate (object row)
            {
                if (row is CellDetail)
                {
                    CellDetail cellDetail = row as CellDetail;
                    if (cellDetail.Cell != null && !double.IsNaN(cellDetail.Cell.Longitude))
                        return cellDetail.Cell.Longitude.ToString();
                    else
                        return null;
                }
                return null;
            };

            //栅格小区详细信息-小区纬度
            olvCellLatitude.AspectGetter += delegate (object row)
            {
                if (row is CellDetail)
                {
                    CellDetail cellDetail = row as CellDetail;
                    if (cellDetail.Cell != null && !double.IsNaN(cellDetail.Cell.Latitude))
                        return cellDetail.Cell.Latitude.ToString();
                    else
                        return null;
                }
                return null;
            };
        }

        private void addCellGroupInfo()
        {
            //栅格小区详细信息-项
            olvItemName.AspectGetter += delegate (object row)
            {
                if (row is CellGroup)
                {
                    CellGroup cellGroup = row as CellGroup;
                    return cellGroup.Description + " (" + cellGroup.Cells.Count.ToString() + ")";
                }
                return null;
            };

            //获取折叠类
            lvDetails.CanExpandGetter += delegate (object row)
            {
                return row is CellGroup;
            };

            //获取子类
            lvDetails.ChildrenGetter = delegate (object row)
            {
                if (row is CellGroup)
                {
                    CellGroup cellGroup = row as CellGroup;
                    return cellGroup.Cells;
                }
                else
                {
                    return "";
                }
            };
        }

        CompareParam curCompareTemplate = null;

        /// <summary>
        /// 开始分析按钮触发事件
        /// </summary>
        private void Start(CompareParam SelectedcompareParam)
        {
            CPGridCellChoseWarningForm choseWaringForm = new CPGridCellChoseWarningForm(nowColorItems);
            if (DialogResult.Cancel == choseWaringForm.ShowDialog())
            {
                chosedWarnings = new List<string>();
            }

            curCompareTemplate = SelectedcompareParam;
            if (curCompareTemplate.AlgorithmCfg.colorItemList.Count <= 0 &&
                curCompareTemplate.AlgorithmCfg.bothStandardList.Count <= 0)
            {
                MessageBox.Show("此竞对模式尚未设置竞对算法,请设置", "提示");
            }
            clear();
            doSearch(curCompareTemplate);

            WaitTextBox.Show(doCompare, curCompareTemplate);

            listView.Items.Clear();
            if (correctCells.CellData.Count <= 0)
            {
                MessageBox.Show("主队客队均未发现栅格数据", "提示");
                return;
            }
            ResetLvCells();
            fillLvDetails(fillLvDeSign);
            fillLvCells(null);
            fillListView();
            mainModel.RefreshLegend();
        }

        private void ResetLvCells()
        {
            foreach (OLVColumn oldCol in  addtionalDisCols)
            {
                lvCells.AllColumns.Remove(oldCol);
                lvCells.Columns.Remove(oldCol);
                oldCol.Dispose();
            }
            addtionalDisCols.Clear();

            #region 固定列的生成
            OLVColumn coloumn = new OLVColumn();
            coloumn.Text = "统计全部小区个数";
            lvCells.AllColumns.Add(coloumn);
            lvCells.Columns.AddRange(new ColumnHeader[] { coloumn });
            coloumn.AspectGetter += delegate(object row)
            {
                if (row is CellData)
                {
                    CellData cellData = row as CellData;
                    return cellData.Name;
                }
                return null;
            };
            addtionalDisCols.Add(coloumn);
            coloumn = new OLVColumn();
            coloumn.Text = "占用过共同栅格数";
            lvCells.AllColumns.Add(coloumn);
            lvCells.Columns.AddRange(new ColumnHeader[] { coloumn });
            coloumn.AspectGetter += delegate(object row)
            {
                if (row is CellData)
                {
                    CellData cellData = row as CellData;
                    return cellData.numSharedGrid.ToString();
                }
                return null;
            };
            addtionalDisCols.Add(coloumn);
            coloumn = new OLVColumn();
            coloumn.Text = "占比";
            lvCells.AllColumns.Add(coloumn);
            lvCells.Columns.AddRange(new ColumnHeader[] { coloumn });
            coloumn.AspectGetter += delegate(object row)
            {
                if (row is CellData)
                {
                    CellData cellData = row as CellData;
                    if (cellData.numSharedGrid != 0)
                    {
                        return (Convert.ToDouble(cellData.numDifferent) / Convert.ToDouble(cellData.numSharedGrid)).ToString("0.00%");
                    }
                    return "-";
                }
                return null;
            };
            addtionalDisCols.Add(coloumn); 
            #endregion

            #region 根据不同的竟对模式动态生成列
            foreach (string colInfo in nowColorItems)
            {
                addColumn(colInfo);
            }
            #endregion

            lvCells.RebuildColumns();

        }

        private void addColumn(string colInfo)
        {
            OLVColumn col = new OLVColumn();
            col.Text = colInfo + "栅格数量";
            col.Tag = colInfo;
            lvCells.AllColumns.Add(col);
            lvCells.Columns.AddRange(new ColumnHeader[] { col });
            col.AspectGetter += delegate (object row)
            {
                if (row is CellData)
                {
                    CellData cellData = row as CellData;
                    int countOfColor = 0;
                    foreach (KeyValuePair<GridCellFormula, TextColorRange> color in cellData.GridColors)
                    {
                        if (color.Value.description == colInfo)
                            countOfColor++;
                    }
                    return countOfColor.ToString();
                }
                return null;
            };
            addtionalDisCols.Add(col);
        }

        /// <summary>
        /// 为LvDetails填充数据
        /// </summary>
        /// <param name="group">标志当前填充的数据组别</param>
        private void fillLvDetails(string group)
        {
            lvDetails.Items.Clear();
            if (correctCells == null) return;
            try
            {
                lvDetails.ClearObjects();
            }
            catch
            {
                //continue
            }
            List<CellGroup> listCellGroup = new List<CellGroup>();
            if (group == fillLvDeSign)
            {
                foreach (KeyValuePair<string, CellGroup> cellGroup in cellDetails)
                {
                    listCellGroup.Add(cellGroup.Value);
                }
            }
            else
            {
                foreach (KeyValuePair<string, CellGroup> cellGroup in cellDetails)
                {
                    if (group == cellGroup.Key)
                        listCellGroup.Add(cellGroup.Value);
                }
            }
            lvDetails.SetObjects(listCellGroup);
        }

        private void miShowAll_Click(object sender, EventArgs e)
        {
            fillLvCells(null);
        }

        /// <summary>
        /// 为LvCells填充数据
        /// </summary>
        private void fillLvCells(CellData cellData)
        {
            lvCells.Items.Clear();
            if (cellData == null)
            {
                if (correctCells == null) return;
                try
                {
                    lvCells.ClearObjects();
                }
                catch
                {
                    //continue
                }
                List<CellData> listCellData = new List<CellData>();
                foreach (KeyValuePair<ICell, CellData> correctCell in correctCells.CellData)
                {
                    listCellData.Add(correctCell.Value);
                }
                lvCells.SetObjects(listCellData);
            }
            else
            {
                try
                {
                    lvCells.ClearObjects();
                }
                catch
                {
                    //continue
                }
                lvCells.SetObjects(new List<CellData>() { cellData });
            }

        }

        /// <summary>
        /// 搜索栅格小区数据
        /// </summary>
        /// <param name="bHost">标志搜索主队还是客队</param>
        /// <param name="grids">需要搜索的栅格</param>
        /// <param name="formula">计算信号质量的公式</param>
        /// <Message>
        /// 方法是根据以搜索到的栅格集合再搜索各个栅格对应覆盖的小区集合
        /// <returns>栅格小区的集合</returns>
        private GridMatrix<GridCellFormula> SearchGridCells(bool bHost, List<GridFormula> grids, string formula)
        {
            QueryCondition condHost;
            QueryCondition condGuest;

            getCondition(curCompareTemplate, out condHost, out condGuest);
            QueryCondition searchCond = bHost ? condHost : condGuest;
            QueryGridCellByGrids query = new QueryGridCellByGrids();
            query.curFormula = formula;
            query.SetQueryCondition(searchCond);
            query.IsShowResultForm = false;
            query.FormulaSet = new string[] { bHost ? curCompareTemplate.formula_A : curCompareTemplate.formula_B };

            foreach (GridUnitBase grid in grids)
            {
                query.AddGrid(grid);
            }
            query.Query();
            return query.RtGridCells;
        }

        private void clear()
        {
            cbxEditRegion.Properties.Items.Clear();
            cbxEditRegion.ResetText();
            listView.Items.Clear();
            lvDetails.Items.Clear();
        }

        private void doSearch(CompareParam cpParam)
        {
            QueryCondition conditionHost;
            QueryCondition conditionGuest;
            getCondition(cpParam, out conditionHost, out conditionGuest);
            searchHostFormula(cpParam, conditionHost);
            searchGuestFormula(cpParam, conditionGuest);
        }

        /// <summary>
        /// 主客队的比较
        /// </summary>
        /// <param name="o">竟对模式</param>
        private void doCompare(object o)
        {
            initCellDetails();
            correctCells = new CorrectCell();

            CompareParam cpParam = o as CompareParam;

            Dictionary<string, MapOperation2> resvMopDic = RegionMop.GetResvRegionMop();

            #region 比较主队栅格小区
            foreach (GridCellFormula gridCellHost in hostGridCellMatrix)
            {
                string regionName = RegionMop.GetResvRegionName(resvMopDic, new DbPoint(gridCellHost.CenterLng, gridCellHost.CenterLat));
                if (regionName == null) continue;

                bool isOther = true;

                //GridCellFormula gridCellGuest = guestGridCellMatrix[gridCellHost.RowIdx, gridCellHost.ColIdx];
                GridCellFormula gridCellGuest = null;

                foreach (GridCellFormula gridCellF in guestGridCellMatrix.Grids)
                {
                    //由于主客队分开查询，所以不能直接通过判断引用类型GridCellFormula是否相等来判断是否是同一个小区
                    if (gridCellF.Bounds.x1 == gridCellHost.Bounds.x1
                        && gridCellF.Bounds.x2 == gridCellHost.Bounds.x2
                        && gridCellF.Bounds.y1 == gridCellHost.Bounds.y1
                        && gridCellF.Bounds.y2 == gridCellHost.Bounds.y2)
                    {
                        gridCellGuest = gridCellF;
                        break;
                    }
                }

                if (gridCellGuest != null)
                {
                    double dHost;
                    double dGuest;

                    foreach (KeyValuePair<ICell, GridCellDataHub> gridCellKeyHost in gridCellHost.CellDataHubDic)
                    {
                        isOther = true;

                        foreach (KeyValuePair<ICell, GridCellDataHub> gridCellKeyGuest in gridCellGuest.CellDataHubDic)
                        {
                            if (gridCellKeyHost.Key.Code == gridCellKeyGuest.Key.Code)
                            {
                                isOther = false;

                                dHost = gridCellKeyHost.Value.GetValue(GridCellDataHub.strFormula);
                                dGuest = gridCellKeyGuest.Value.GetValue(GridCellDataHub.strFormula);

                                TextColorRange rtColor = cpParam.GetTextColorRange(ref dHost, ref dGuest);
                                if (rtColor == null)
                                {
                                    tryAddCorrectCell(gridCellKeyHost.Key, gridCellHost, curCompareTemplate.AlgorithmCfg.GetSpecialColor(CPModeEditForm.OTHERS));
                                    addNumSharedGrid(gridCellKeyHost.Key);
                                    continue;
                                }
                                else
                                {
                                    if (rtColor.description == CPModeEditForm.HOSTNULL || rtColor.description == CPModeEditForm.GUESTNULL)
                                        rtColor.description = CPModeEditForm.OTHERS;
                                    tryAddCorrectCell(gridCellKeyHost.Key, gridCellHost, rtColor);
                                    addNumSharedGrid(gridCellKeyHost.Key);
                                }

                                foreach (string item in chosedWarnings)
                                {
                                    if (item == rtColor.description)
                                        addNumDifferent(gridCellKeyHost.Key);
                                }

                                addCellDetail(gridCellKeyHost.Key, gridCellHost, gridCellKeyHost.Value, gridCellKeyGuest.Value, rtColor.description, rtColor.color);
                            }
                        }

                        if (isOther)
                        {
                            TextColorRange rtColor = curCompareTemplate.AlgorithmCfg.GetSpecialColor(CPModeEditForm.OTHERS);
                            tryAddCorrectCell(gridCellKeyHost.Key, gridCellHost, rtColor);
                            addCellDetail(gridCellKeyHost.Key, gridCellHost, gridCellKeyHost.Value, null, rtColor.description, rtColor.color);
                        }
                    }
                }
                else
                {
                    foreach (KeyValuePair<ICell, GridCellDataHub> gridCellKeyHost in gridCellHost.CellDataHubDic)
                    {
                        TextColorRange rtColor = curCompareTemplate.AlgorithmCfg.GetSpecialColor(CPModeEditForm.GUESTNULL);
                        tryAddCorrectCell(gridCellKeyHost.Key, gridCellHost, rtColor);
                        addCellDetail(gridCellKeyHost.Key, gridCellHost, gridCellKeyHost.Value, null, rtColor.description, rtColor.color);
                    }
                }
            }
            #endregion

            #region 比较客队栅格小区
            foreach (GridCellFormula gridCellGuest in guestGridCellMatrix)
            {
                string regionName = RegionMop.GetResvRegionName(resvMopDic, new DbPoint(gridCellGuest.CenterLng, gridCellGuest.CenterLat));
                if (regionName == null) continue;

                bool isOther = true;

                GridCellFormula gridCellHost = null;
                foreach (GridCellFormula gridCellF in hostGridCellMatrix.Grids)
                {
                    //由于主客队分开查询，所以不能直接通过判断引用类型GridCellFormula是否相等来判断是否是同一个小区
                    if (gridCellF.Bounds.x1 == gridCellGuest.Bounds.x1
                        && gridCellF.Bounds.x2 == gridCellGuest.Bounds.x2
                        && gridCellF.Bounds.y1 == gridCellGuest.Bounds.y1
                        && gridCellF.Bounds.y2 == gridCellGuest.Bounds.y2)
                    {
                        gridCellHost = gridCellF;
                        break;
                    }
                }

                if (gridCellHost != null)
                {
                    foreach (KeyValuePair<ICell, GridCellDataHub> gridCellKeyGuest in gridCellGuest.CellDataHubDic)
                    {
                        isOther = true;
                        foreach (KeyValuePair<ICell, GridCellDataHub> gridCellKeyHost in gridCellHost.CellDataHubDic)
                        {
                            if (gridCellKeyHost.Key.Code == gridCellKeyGuest.Key.Code)
                            {
                                isOther = false;
                            }
                        }
                        if (isOther)
                        {
                            TextColorRange rtColor = curCompareTemplate.AlgorithmCfg.GetSpecialColor(CPModeEditForm.OTHERS);
                            tryAddCorrectCell(gridCellKeyGuest.Key, gridCellGuest, rtColor);

                            addCellDetail(gridCellKeyGuest.Key, gridCellGuest, null, gridCellKeyGuest.Value, rtColor.description, rtColor.color);
                        }
                    }
                }
                else
                {
                    foreach (KeyValuePair<ICell, GridCellDataHub> gridCellKeyGuest in gridCellGuest.CellDataHubDic)
                    {
                        TextColorRange rtColor = curCompareTemplate.AlgorithmCfg.GetSpecialColor(CPModeEditForm.HOSTNULL);
                        tryAddCorrectCell(gridCellKeyGuest.Key, gridCellGuest, rtColor);
                        addCellDetail(gridCellKeyGuest.Key, gridCellGuest, null, gridCellKeyGuest.Value, rtColor.description, rtColor.color);
                    }
                }
            }
            #endregion

            System.Threading.Thread.Sleep(100);
            WaitTextBox.Close();
        }

        /// <summary>
        /// 初始化lvDetails所填充的数据
        /// </summary>
        private void initCellDetails()
        {
            if (cellDetails.Count == 0)
            {
                foreach (CPModeColorItem item in curCompareTemplate.AlgorithmCfg.colorItemList)
                {
                    cellDetails.Add(item.colorRange.description, new CellGroup(item.colorRange.description));
                }
                cellDetails.Add(CPModeEditForm.OTHERS, new CellGroup(CPModeEditForm.OTHERS));
                cellDetails.Add(CPModeEditForm.GUESTNULL, new CellGroup(CPModeEditForm.GUESTNULL));
                cellDetails.Add(CPModeEditForm.HOSTNULL, new CellGroup(CPModeEditForm.HOSTNULL));
            }
            else
            {
                cellDetails = new Dictionary<string, CellGroup>();
                initCellDetails();
            }
        }

        private void addCellDetail(ICell cell, GridCellFormula grid, GridCellDataHub cellDataHost, GridCellDataHub cellDataGuest, string description, Color color)
        {
            cellDetails[description].Cells.Add(new CellDetail(cell, grid, cellDataHost, cellDataGuest, description, color));
        }

        /// <summary>
        /// 添加lvCells所填充的数据
        /// </summary>
        /// <param name="cell">小区</param>
        /// <param name="grid">小区对应的栅格</param>
        /// <param name="color">竟对比较后的颜色</param>
        /// <returns>是否成功</returns>
        private void tryAddCorrectCell(ICell cell, GridCellFormula grid, TextColorRange color)
        {
            if (!correctCells.CellData.ContainsKey(cell))
            {
                correctCells.CellData.Add(cell, new CellData(cell.Name, grid, color));
            }
            else
            {
                foreach (GridCellFormula g in correctCells.CellData[cell].Grids)
                {
                    if (g.Bounds.x1 == grid.Bounds.x1
                        && g.Bounds.x2 == grid.Bounds.x2
                        && g.Bounds.y1 == grid.Bounds.y1
                        && g.Bounds.y2 == grid.Bounds.y2)
                        return;
                }
                correctCells.CellData[cell].Grids.Add(grid);
                correctCells.CellData[cell].GridColors.Add(grid, color);
            }
        }

        /// <summary>
        /// 增加小区占用过共同栅格数
        /// </summary>
        /// <param name="cell">小区</param>
        private void addNumSharedGrid(ICell cell)
        {
            if (correctCells.CellData.ContainsKey(cell))
                correctCells.CellData[cell].numSharedGrid += 1;
        }

        /// <summary>
        /// 增加小区差异次数
        /// </summary>
        /// <param name="cell">小区</param>
        private void addNumDifferent(ICell cell)
        {
            if (correctCells.CellData.ContainsKey(cell))
                correctCells.CellData[cell].numDifferent += 1;
        }

        /// <summary>
        /// 为ListView填充数据
        /// </summary>
        private void fillListView()
        {
            listView.Items.Clear();
            countGridCells = 0;
            foreach (KeyValuePair<string, CellGroup> cellGroup in cellDetails)
            {
                countGridCells += cellGroup.Value.Cells.Count;
            }
            foreach (KeyValuePair<string, CellGroup> cellGroup in cellDetails)
            {
                string[] array = new string[3];
                array[0] = cellGroup.Key;
                array[1] = cellGroup.Value.Cells.Count.ToString();
                array[2] = Math.Round(100.0 * cellGroup.Value.Cells.Count / countGridCells, 2).ToString();
                ListViewItem lvi = new ListViewItem(array);
                lvi.Tag = cellGroup.Value;
                listView.Items.Add(lvi);
            }
        }

        private void getCondition(CompareParam param, out QueryCondition conditionHost, out QueryCondition conditionGuest)
        {
            initHostCondition(param, out conditionHost);
            initGuestCondition(param, out conditionGuest);
        }

        private void initHostCondition(CompareParam param, out QueryCondition conditionHost)
        {
            conditionHost = new QueryCondition();
            conditionHost.AgentIds = condition.AgentIds;
            conditionHost.Areas = condition.Areas;
            conditionHost.DistrictID = condition.DistrictID;
            conditionHost.DistrictIDs = condition.DistrictIDs;
            conditionHost.EventIDs = condition.EventIDs;
            conditionHost.FileInfos = condition.FileInfos;
            conditionHost.FileName = conditionTPHost.FileName;
            conditionHost.FileNameOrNum = conditionTPHost.FileNameOrNum;
            conditionHost.NameFilterType = condition.NameFilterType;
            conditionHost.Geometorys = condition.Geometorys;
            conditionHost.IsAllAgent = condition.IsAllAgent;
            conditionHost.QueryType = condition.QueryType;

            conditionHost.Periods.Clear();
            conditionHost.Periods = conditionTPHost.Periods;
            conditionHost.Projects.Clear();
            foreach (int projID in conditionTPHost.Projects)
            {
                conditionHost.Projects.Add(projID);
            }
            conditionHost.ServiceTypes.Clear();
            foreach (int servID in param.serviceList_A)
            {
                conditionHost.ServiceTypes.Add(servID);
            }
            conditionHost.CarrierTypes.Clear();
            conditionHost.CarrierTypes.Add(param.carrier_A);
        }

        private void initGuestCondition(CompareParam param, out QueryCondition conditionGuest)
        {
            conditionGuest = new QueryCondition();
            conditionGuest.AgentIds = condition.AgentIds;
            conditionGuest.Areas = condition.Areas;
            conditionGuest.DistrictID = condition.DistrictID;
            conditionGuest.DistrictIDs = condition.DistrictIDs;
            conditionGuest.EventIDs = condition.EventIDs;
            conditionGuest.FileInfos = condition.FileInfos;
            conditionGuest.FileName = condtionTPGuest.FileName;
            conditionGuest.FileNameOrNum = condtionTPGuest.FileNameOrNum;
            conditionGuest.NameFilterType = condition.NameFilterType;
            conditionGuest.Geometorys = condition.Geometorys;
            conditionGuest.IsAllAgent = condition.IsAllAgent;
            conditionGuest.QueryType = condition.QueryType;

            conditionGuest.Periods.Clear();
            conditionGuest.Periods = condtionTPGuest.Periods;
            conditionGuest.Projects.Clear();
            foreach (int projID in condtionTPGuest.Projects)
            {
                conditionGuest.Projects.Add(projID);
            }
            conditionGuest.ServiceTypes.Clear();
            foreach (int servID in param.serviceList_B)
            {
                conditionGuest.ServiceTypes.Add(servID);
            }
            conditionGuest.CarrierTypes.Clear();
            conditionGuest.CarrierTypes.Add(param.carrier_B);
        }


        GridMatrix<GridCellFormula> hostGridCellMatrix;

        private void searchHostFormula(CompareParam param, QueryCondition conditionHost)
        {
            GridMatrix<GridFormula> hostGridMatrix;
            DIYQueryFormulaInGridByRegion queryHost = new DIYQueryFormulaInGridByRegion(mainModel);
            queryHost.setQueryFormulas(param.displayColumnList_A, param.formula_A);
            queryHost.SetQueryCondition(conditionHost);
            queryHost.Query();
            hostGridMatrix = queryHost.formulaGridMatrix;
            hostGridCellMatrix = SearchGridCells(true, hostGridMatrix.Grids, param.formula_A);
        }

        GridMatrix<GridCellFormula> guestGridCellMatrix;
        private void searchGuestFormula(CompareParam param, QueryCondition conditionGuest)
        {
            GridMatrix<GridFormula> guestGridMatrix;
            DIYQueryFormulaInGridByRegion queryGuest = new DIYQueryFormulaInGridByRegion(mainModel);
            queryGuest.setQueryFormulas(param.displayColumnList_B, param.formula_B);
            queryGuest.SetQueryCondition(conditionGuest);
            queryGuest.Query();
            guestGridMatrix = queryGuest.formulaGridMatrix;
            guestGridCellMatrix = SearchGridCells(false, guestGridMatrix.Grids, param.formula_B);
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            lvDetails.ExpandAll();
            if (lvDetails.Items.Count > 65535)
            {
                TxtExporter.Export(lvDetails, true);
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(lvDetails);
            }
        }

        private void lvDetails_MouseClick(object sender, MouseEventArgs e)
        {

            TreeListView lv = sender as TreeListView;
            OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
            CellDetail cellDetail = null;
            if (info.RowObject is CellDetail)
            {
                cellDetail = info.RowObject as CellDetail;

                foreach (KeyValuePair<ICell, CellData> correctCell in correctCells.CellData)
                {
                    if (correctCell.Key.Code == cellDetail.Cell.Code)
                    {
                        fillLvCells(correctCell.Value);
                        lvCells.SelectedObject = correctCell.Value;
                        return;
                    }
                }
                fillLvCells(null);
            }
        }

        private void lvDetails_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            TreeListView lv = sender as TreeListView;
            OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
            CellDetail cellDetail = null;
            CellGroup cellGroup = null;
            if (info.RowObject is CellDetail)
            {
                cellDetail = info.RowObject as CellDetail;
                makeSureLayerVisible();
                cellDetail.Grid.GridColor = cellDetail.GridColor;
                layer.Grids = new List<GridCellFormula>() { cellDetail.Grid };
            }
            else if (info.RowObject is CellGroup)
            {
                cellGroup = info.RowObject as CellGroup;
                makeSureLayerVisible();
                layer.Grids = new List<GridCellFormula>();
                foreach (CellDetail cell in cellGroup.Cells)
                {
                    cell.Grid.GridColor = cell.GridColor;
                    layer.Grids.Add(cell.Grid);
                }
            }
            else
            {
                return;
            }

            double minLng = double.MaxValue;
            double maxLng = double.MinValue;
            double minLat = double.MaxValue;
            double maxLat = double.MinValue;
            foreach (GridCellFormula g in layer.Grids)
            {
                minLng = Math.Min(minLng, g.CenterLng);
                maxLng = Math.Max(maxLng, g.CenterLng);
                minLat = Math.Min(minLat, g.CenterLat);
                maxLat = Math.Max(maxLat, g.CenterLat);
            }

            MasterCom.MTGis.DbRect rect = new MTGis.DbRect(minLng - 0.002, minLat - 0.002, maxLng + 0.002, maxLat + 0.002);
            MainModel.MainForm.GetMapForm().GoToView(rect);
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            lvDetails.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            lvDetails.CollapseAll();
        }

        private void miExportTxt_Click(object sender, EventArgs e)
        {
            lvDetails.ExpandAll();
            TxtExporter.Export(lvDetails, true);
        }

        private void fillDisplayItem(CompareParam mode)
        {
            cbxDisplayMode.Items.Clear();
            cbxDisplayMode.Items.Add(fillLvDeSign);
            foreach (TextColorRange range in mode.AlgorithmCfg.specialColorList)
            {
                cbxDisplayMode.Items.Add(range);
            }
            foreach (CPModeColorItem item in mode.AlgorithmCfg.colorItemList)
            {
                cbxDisplayMode.Items.Add(item.colorRange);
            }
            foreach (CPModeColorItem item in mode.AlgorithmCfg.bothStandardList)
            {
                cbxDisplayMode.Items.Add(item.colorRange);
            }
            if (cbxDisplayMode.Items.Count > 0)
            {
                cbxDisplayMode.SelectedIndex = 0;
            }
        }

        private void cbxDisplayMode_SelectedIndexChanged(object sender, EventArgs e)
        {

            fillLvDetails(cbxDisplayMode.Text);

        }

        private void miExportCellsExcel_Click(object sender, EventArgs e)
        {
            lvCells.ExpandAll();
            if (lvCells.Items.Count > 65535)
            {
                TxtExporter.Export(lvCells, true);
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(lvCells);
            }
        }

        private void miExportLvExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(listView);
        }

        private void miExportCellsTxt_Click(object sender, EventArgs e)
        {
            lvCells.ExpandAll();
            TxtExporter.Export(lvCells, true);
        }

        private void lvCells_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            TreeListView lv = sender as TreeListView;
            OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
            CellData cellData = null;
            if (info.RowObject is CellData)
            {
                cellData = info.RowObject as CellData;
                makeSureLayerVisible();
                foreach (GridCellFormula grid in cellData.Grids)
                {
                    grid.GridColor = cellData.GridColors[grid].color;
                }
                layer.Grids = cellData.Grids;
            }
            else
            {
                return;
            }

            double minLng = double.MaxValue;
            double maxLng = double.MinValue;
            double minLat = double.MaxValue;
            double maxLat = double.MinValue;
            foreach (GridCellFormula g in layer.Grids)
            {
                minLng = Math.Min(minLng, g.CenterLng);
                maxLng = Math.Max(maxLng, g.CenterLng);
                minLat = Math.Min(minLat, g.CenterLat);
                maxLat = Math.Max(maxLat, g.CenterLat);
            }

            MasterCom.MTGis.DbRect rect = new MTGis.DbRect(minLng - 0.002, minLat - 0.002, maxLng + 0.002, maxLat + 0.002);
            MainModel.MainForm.GetMapForm().GoToView(rect);
        }

        CPGridCellLayer layer = new CPGridCellLayer();
        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            layer = mf.GetLayerBase(typeof(CPGridCellLayer)) as CPGridCellLayer;
        }

        /// <summary>
        /// 栅格小区图层
        /// </summary>
        /// <Message>
        /// 修改子栅格工单图层
        /// <Message>
        public class CPGridCellLayer : LayerBase
        {

            public List<GridCellFormula> Grids { get; set; }

            public CPGridCellLayer()
                : base("栅格工单图层")
            {
                Grids = new List<GridCellFormula>();
            }

            public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
            {
                if (!IsVisible || Grids == null || Grids.Count == 0)
                {
                    return;
                }

                Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
                inflatedRect.Inflate(50, 50);
                DbRect dRect;
                this.gisAdapter.FromDisplay(inflatedRect, out dRect);

                foreach (GridCellFormula grid in Grids)
                {
                    if (dRect.Within(grid.Bounds))
                    {
                        RectangleF rect;
                        this.gisAdapter.ToDisplay(grid.Bounds, out rect);
                        graphics.FillRectangle(new SolidBrush(grid.GridColor), rect);
                    }
                }
            }
        }

        public class CorrectCell
        {

            public Dictionary<ICell, CellData> CellData { get; set; }

            public CorrectCell()
            {
                CellData = new Dictionary<ICell, CellData>();
            }
        }

        public class CellData
        {
            public int numSharedGrid { get; set; }
            public int numDifferent { get; set; }

            public string Name { get; set; }

            public List<GridCellFormula> Grids { get; set; }

            public Dictionary<GridCellFormula, TextColorRange> GridColors { get; set; }

            public CellData(string cellName, GridCellFormula grid, TextColorRange color)
            {
                this.numSharedGrid = 0;
                this.numDifferent = 0;
                this.Name = cellName;
                this.Grids = new List<GridCellFormula>() { grid };
                this.GridColors = new Dictionary<GridCellFormula, TextColorRange>() { { grid, color } };
            }
        }

        public class CellGroup
        {
            public List<CellDetail> Cells { get; set; }

            public List<CellData> CellDatas { get; set; }

            public string Description { get; set; }

            public CellGroup(string description)
            {
                Cells = new List<CellDetail>();
                CellDatas = new List<CellData>();
                Description = description;
            }
        }

        public class CellDetail
        {
            public string Description { get; set; }

            public ICell Cell { get; set; }

            public GridCellFormula Grid { get; set; }

            public Color GridColor { get; set; }

            public GridCellDataHub CellDataHost { get; set; }

            public GridCellDataHub CellDataGuest { get; set; }

            public CellDetail(ICell cell, GridCellFormula grid, GridCellDataHub cellDataHost, GridCellDataHub cellDataGuest, string description, Color color)
            {
                this.GridColor = color;
                this.Cell = cell;
                this.Grid = grid;
                this.CellDataHost = cellDataHost;
                this.CellDataGuest = cellDataGuest;
                this.Description = description;
            }
        }

    }



}
