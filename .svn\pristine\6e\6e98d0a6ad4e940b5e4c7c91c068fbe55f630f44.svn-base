﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Net;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellCoverLapByRegion : DIYAnalyseByPeriodBackgroundBase_Sample
    {
        private static ZTCellCoverLapByRegion intance = null;
        protected static readonly object lockObj = new object();
        public static ZTCellCoverLapByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTCellCoverLapByRegion();
                    }
                }
            }
            return intance;
        }
        protected ZTCellCoverLapByRegion()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_VOICE);
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }

        public override string Name
        {
            get { return "过覆盖分析_GSM"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12002, this.Name);
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }

        protected override void getBackgroundData()
        {
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Cell(Condition.Periods[0].IBeginTime,
                            Condition.Periods[0].IEndTime, GetSubFuncID(), Name, StatType, BackgroundFuncBaseSetting.GetInstance().projectType);
        }

        protected override List<TimePeriod> GetStatedTimePeriod()
        {
            return BackgroundFuncQueryManager.GetInstance().GetStatedTimePeriod_Cell(GetSubFuncID(), BackgroundFuncBaseSetting.GetInstance().projectType);
        }

        public CellCoverLapCondition SettingCondition { get; set; } = new CellCoverLapCondition();
        protected Dictionary<string, CellCoverLap> cellLapRetDic = new Dictionary<string, CellCoverLap>();
        public int curFilterRxlev { get; set; } = -90;
        public int curMinSampleCount { get; set; } = 0;
        public float curMinPercent { get; set; } = 0;
        public int curMinDistance { get; set; } = 0;
        public int curMaxDistance { get; set; } = 1000000;
        public float disFactor { get; set; } = 1.6f;
        public int nearestCellCount { get; set; } = 3;
        public bool isBand { get; set; } = false;
        public bool CheckNearBTS { get; set; } = false;
        public string bandType { get; set; } = "";
        public bool ShowNearBTS { get; set; } = false;

        protected override void doSomethingBeforeQueryInThread()
        {
            queryCurRegionFileId();
            cellLapRetDic.Clear();
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                bool isVailid = false;
                bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                //进行过覆盖算法运算
                if (inRegion && tp is TestPointDetail)
                {
                    //分析主服小区
                    short? rxlevSub = (short?)tp["RxLevSub"];
                    if (rxlevSub == null)
                    {
                        return false;
                    }
                    int? rxQual = (int?)(byte?)tp["RxQualSub"];
                    if (rxQual != null && (rxQual < 0 || rxQual > 7))
                    {//异常值
                        rxQual = null;
                    }
                    if (rxlevSub < curFilterRxlev)
                    {
                        return false;
                    }
                    Cell cell = null;
                    cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)tp["LAC"], (ushort?)(int?)tp["CI"], (short?)tp["BCCH"], (byte?)tp["BSIC"], tp.Longitude, tp.Latitude);
                    isVailid = IsVaildCellTP(cell, tp, rxlevSub, rxQual);

                    //分析邻区
                    isVailid = judgeNCellCoverLap(tp, isVailid);
                }
                return isVailid;
            }
            catch
            {
                return false;
            }
        }

        private bool judgeNCellCoverLap(TestPoint tp, bool isVailid)
        {
            if (CheckNearBTS)
            {
                for (int i = 0; i < 5; i++)
                {
                    short? n_Rxlev = (short?)tp["N_RxLev", i];
                    if (n_Rxlev == null)
                    {
                        continue;
                    }
                    else if (n_Rxlev < curFilterRxlev)
                    {
                        continue;
                    }
                    Cell nCell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)tp["N_LAC", i], (ushort?)(int?)tp["N_CI", i], (short?)tp["N_BCCH", i], (byte?)tp["N_BSIC", i], tp.Longitude, tp.Latitude);

                    bool res = IsVaildCellTP(nCell, tp, n_Rxlev, null);
                    if (!isVailid)
                    {
                        isVailid = res;
                    }
                }
            }

            return isVailid;
        }

        protected bool IsVaildCellTP(Cell cell, TestPoint tp, short? rxlevSub, int? rxQual)
        {
            if (cell != null)
            {
                if (cell.Type != BTSType.Outdoor)
                {
                    return false;
                }
                CellCoverLap covLap = null;
                if (!cellLapRetDic.TryGetValue(cell.Name, out covLap))
                {
                    string realNearestBTSs = "";
                    double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, nearestCellCount, false, out realNearestBTSs);
                    covLap = new CellCoverLap();
                    covLap.RealNearestBTSs = realNearestBTSs;
                    covLap._CellCovRadius = radiusOfCell;
                    covLap.rationalDistance = radiusOfCell * disFactor;
                    covLap.cell = cell;
                    covLap.nearestBTSs = MasterCom.ES.Data.CfgDataProvider.GetNearestBTSs(cell, nearestCellCount);
                    covLap.mnger = new DTDataManager(MainModel.GetInstance());
                    cellLapRetDic[cell.Name] = covLap;
                }
                if (fileIDNameDic.ContainsKey(tp.FileID)
                    && !covLap.strFileID.Contains(fileIDNameDic[tp.FileID] + ""))
                {
                    covLap.strFileID += fileIDNameDic[tp.FileID] + ",";
                }

                double distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, cell.Longitude, cell.Latitude);
                bool isBadCheck = distanceToCell > covLap.rationalDistance;
                if (isBadCheck)
                {
                    covLap.AddBadSample(tp, distanceToCell, (float)rxlevSub, rxQual);
                    return true;
                }
                else
                {
                    covLap.goodSampleCount++;
                    return false;
                }
            }
            return false;
        }

        ZTCellCoverLapSetForm fDlg = null;
        public Dictionary<int, string> fileIDNameDic { get; set; }
        protected override bool getConditionBeforeQuery()
        {
            if (this.GetType().Name.Equals("ZTCellCoverLapByRegion"))
            {
                ShowNearBTS = true;
            }
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (fDlg == null)
            {
                fDlg = new ZTCellCoverLapSetForm(this.GetType());
                fDlg.SetSameNbhNumVisible(false);
            }
            if (fDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            fDlg.GetSettingFilterRet(SettingCondition);
            curFilterRxlev = SettingCondition.CurFilterRxlev;
            curMinSampleCount = SettingCondition.CurMinSampleCount;
            curMinPercent = SettingCondition.CurMinPercent;
            curMinDistance = SettingCondition.CurMinDistance;
            curMaxDistance = SettingCondition.CurMaxDistance;
            nearestCellCount = SettingCondition.NearestCellCount;
            disFactor = SettingCondition.DisFactor;
            isBand = SettingCondition.IsBand;
            bandType = SettingCondition.BandType;
            CheckNearBTS = SettingCondition.CheckNearBTS;

            return true;
        }

        private void queryCurRegionFileId()
        {
            fileIDNameDic = new Dictionary<int, string>();
            if (condition != null)
            {
                DIYQueryFileInfo diyFilie = new DIYQueryFileInfo(MainModel);
                diyFilie.IsShowFileInfoForm = false;
                diyFilie.SetQueryCondition(condition);
                diyFilie.Query();
            }

            foreach (FileInfo file in MainModel.FileInfos)
            {
                if (!fileIDNameDic.ContainsKey(file.ID))
                {
                    fileIDNameDic[file.ID] = file.Name;
                }
            }
            MainModel.FileInfos.Clear();
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "RxQualSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 5; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "N_LAC";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "N_CI";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "N_BCCH";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "N_BSIC";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "N_RxLev";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM");
            tmpDic.Add("themeName", (object)"---");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void getResultAfterQuery()
        {
            curSelDIYSampleGroup.ThemeName = "GSM RxLevSub";
            FilterCellCoverLap();
        }

        protected virtual void FilterCellCoverLap()
        {
            List<string> filterCells = new List<string>();
            foreach (KeyValuePair<string, CellCoverLap> keyValue in cellLapRetDic)
            {
                CellCoverLap cellLap = keyValue.Value;
                cellLap.GetResult();

                if (!(cellLap.badSampleCount > 0 && cellLap._CellCovRadius > 50
                    && cellLap.TotalSampleCount >= curMinSampleCount
                    && cellLap.BadSamplePercent >= curMinPercent
                    && cellLap.MeanBadDistance >= curMinDistance
                    && cellLap.MeanBadDistance <= curMaxDistance))
                {
                    filterCells.Add(keyValue.Key);
                }
            }
            foreach (string key in filterCells)
            {
                cellLapRetDic.Remove(key);
            }
            MainModel.DTDataManager.Clear();
            foreach (CellCoverLap ccl in cellLapRetDic.Values)
            {
                if (ccl.mnger != null)
                {
                    foreach (DTFileDataManager fmnger in ccl.mnger.FileDataManagers)
                    {
                        foreach (TestPoint tp in fmnger.TestPoints)
                        {
                            MainModel.DTDataManager.Add(tp);
                        }
                    }
                }
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            if (cellLapRetDic.Count == 0)
            {
                XtraMessageBox.Show(MainModel.MainForm, "没有符合条件的数据。");
                return;
            }
            ZTCellCoverLapListForm cellCoverLapListForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTCellCoverLapListForm)) as ZTCellCoverLapListForm;
            if (cellCoverLapListForm == null || cellCoverLapListForm.IsDisposed)
            {
                cellCoverLapListForm = new ZTCellCoverLapListForm(MainModel);
            }
            cellCoverLapListForm.showCellSet(cellLapRetDic, curMinSampleCount, curMinPercent, curMinDistance, curMaxDistance, ShowNearBTS);
            cellCoverLapListForm.Owner = MainModel.MainForm;
            cellCoverLapListForm.Visible = true;
            cellCoverLapListForm.BringToFront();
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.GSM业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["FilterRxlev"] = curFilterRxlev;
                param["NearestCellCount"] = nearestCellCount;
                param["DisFactor"] = disFactor;

                param["MinSampleCount"] = curMinSampleCount;
                param["MinPercent"] = curMinPercent;
                param["MinDistance"] = curMinDistance;
                param["MaxDistance"] = curMaxDistance;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("FilterRxlev"))
                {
                    curFilterRxlev = int.Parse(param["FilterRxlev"].ToString());
                }
                if (param.ContainsKey("NearestCellCount"))
                {
                    nearestCellCount = int.Parse(param["NearestCellCount"].ToString());
                }
                if (param.ContainsKey("DisFactor"))
                {
                    disFactor = float.Parse(param["DisFactor"].ToString());
                }

                if (param.ContainsKey("MinSampleCount"))
                {
                    curMinSampleCount = int.Parse(param["MinSampleCount"].ToString());
                }
                if (param.ContainsKey("MinPercent"))
                {
                    curMinPercent = float.Parse(param["MinPercent"].ToString());
                }
                if (param.ContainsKey("MinDistance"))
                {
                    curMinDistance = int.Parse(param["MinDistance"].ToString());
                }
                if (param.ContainsKey("MaxDistance"))
                {
                    curMaxDistance = int.Parse(param["MaxDistance"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CoverLapProperties_GSM(this,false);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (string key in cellLapRetDic.Keys)
            {
                CellCoverLap ccl = cellLapRetDic[key];
                BackgroundResult result = ccl.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                Condition.Periods[0].IEndTime, bgResultList);
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                bgResult.GetImageValueInt();//rxQualTestPointCount
                int badSampleCount = bgResult.GetImageValueInt();
                int goodSampleCount = bgResult.GetImageValueInt();
                float MeanBadDistance = bgResult.GetImageValueFloat();
                float maxBadDistance = bgResult.GetImageValueFloat();
                float minBadDistance = bgResult.GetImageValueFloat();
                bgResult.GetImageValueFloat();//_CellCovRadius
                float rationalDistance = bgResult.GetImageValueFloat();
                string strFileId = bgResult.GetImageValueString();
                string strProject = bgResult.GetImageValueString();
                if (strProject != "" && !BackgroundFuncBaseSetting.GetInstance().projectType.Equals(strProject))
                {
                    continue;
                }
                StringBuilder sb = new StringBuilder();
                sb.Append("总采样点数：");
                sb.Append(badSampleCount + goodSampleCount);
                sb.Append("\r\n");
                sb.Append("异常采样点数：");
                sb.Append(badSampleCount);
                sb.Append("\r\n");
                sb.Append("异常采样点比例：");
                sb.Append(Math.Round(100.0 * badSampleCount / (badSampleCount + goodSampleCount), 2));
                sb.Append("%");
                sb.Append("\r\n");
                sb.Append("平均过覆盖距离：");
                sb.Append(MeanBadDistance);
                sb.Append("\r\n");
                sb.Append("最大过覆盖距离：");
                sb.Append(maxBadDistance);
                sb.Append("\r\n");
                sb.Append("最小过覆盖距离：");
                sb.Append(minBadDistance);
                sb.Append("\r\n");
                sb.Append("合理覆盖距离：");
                sb.Append(rationalDistance);
                sb.Append("\r\n");
                sb.Append("文件ID：");
                sb.Append(strFileId);
                sb.Append("\r\n");
                sb.Append("项目ID：");
                sb.Append(strProject);
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }

    public class CellCoverLap
    {
        public double SumRxLev { get; set; } = 0;
        public int TestPointCount { get; set; } = 0;
        public string nearestBTSs { get; set; }
        public string RealNearestBTSs { get; set; }
        private double avgRxLev;
        public double AvgRxLev
        {
            get { return Math.Round(avgRxLev, 2); }
        }
        public double MaxRxLev { get; set; } = double.MinValue;
        public double MinRxLev { get; set; } = double.MaxValue;

        public double SumRxQual { get; set; } = 0;
        public int RxQualTestPointCount { get; set; } = 0;
        private double avgRxQual;
        public string AvgRxQual
        {
            get { return avgRxQual == 255 ? "" : Math.Round(avgRxQual, 2).ToString(); }
        }
        public double MaxRxQual { get; set; } = double.MinValue;
        public string MaxRxQualString
        {
            get { return MaxRxQual == 255 ? "" : MaxRxQual.ToString("#.##"); }
        }
        public double MinRxQual { get; set; } = double.MaxValue;
        public string MinRxQualString
        {
            get { return MinRxQual == 255 ? "" : MinRxQual.ToString("#.##"); }
        }

        public int TotalSampleCount
        {
            get { return goodSampleCount + badSampleCount; }
        }
        public float BadSamplePercent
        {
            get { return (((float)badSampleCount) / TotalSampleCount); }
        }
        public float MeanBadDistance { get; set; } = 0;
        public bool isMainCell { get; set; } = false;
        public int InterfereCount { get; set; } = 0;
        public string InterfereCoefficient
        {
            get
            {
                if (isMainCell)
                {
                    return string.Format("{0:F2}", (InterfereCount * 1.0 / (1 + cell.TCH.Count)));
                }
                return string.Empty;
            }
        }
        public Cell cell { get; set; }
        public int goodSampleCount { get; set; }
        public int badSampleCount { get; set; }
        public float maxBadDistance { get; set; } = float.MinValue;
        public float minBadDistance { get; set; } = float.MaxValue;
        public List<float> distanceAllBadList { get; set; } = new List<float>();
        /// <summary>
        /// 理想覆盖半径
        /// </summary>
        public double _CellCovRadius { get; set; }
        public double rationalDistance { get; set; }
        public DTDataManager mnger { get; set; }

        public int istime { get; set; } = int.MaxValue;
        public int ietime { get; set; } = int.MinValue;

        public double longitude { get; set; }
        public double latitude { get; set; }

        public string roadDesc { get; set; }

        public string areaName { get; set; }
        public string gridName { get; set; }
        public string areaAgentName { get; set; }
        public string strFileID { get; set; } = "";

        public void AddBadSample(TestPoint tp, double distanceToCell, float rxlevSub, int? rxQual)
        {
            badSampleCount++;
            mnger.Add(tp);
            distanceAllBadList.Add((float)distanceToCell);
            if (distanceToCell > maxBadDistance)
            {
                maxBadDistance = (float)distanceToCell;
            }
            if (distanceToCell < minBadDistance)
            {
                minBadDistance = (float)distanceToCell;
            }
            TestPointCount++;
            SumRxLev += (int)rxlevSub;
            if (rxlevSub > MaxRxLev)
            {
                MaxRxLev = (int)rxlevSub;
            }
            if (rxlevSub < MinRxLev)
            {
                MinRxLev = (int)rxlevSub;
            }
            if (rxQual != null)
            {
                RxQualTestPointCount++;
                SumRxQual += (int)rxQual;
                if (rxQual > MaxRxQual)
                {
                    MaxRxQual = (int)rxQual;
                }
                if (rxQual < MinRxQual)
                {
                    MinRxQual = (int)rxQual;
                }
            }

            if (istime > tp.Time)
            {
                istime = tp.Time;
            }
            if (ietime < tp.Time)
            {
                ietime = tp.Time;
            }
        }

        public void AddBadSample(TestPoint tp, double distanceToCell, float rxLev)
        {
            AddBadSample(tp, distanceToCell, rxLev, null);
        }

        public void GetResult()
        {
            avgRxLev = TestPointCount == 0 ? 0 : SumRxLev / TestPointCount;
            if (RxQualTestPointCount == 0)
            {
                avgRxQual = 255;
                MinRxQual = 255;
                MaxRxQual = 255;
            }
            else
            {
                avgRxQual = SumRxQual / RxQualTestPointCount;
            }
            if (distanceAllBadList.Count > 0)
            {
                double TotalDist = 0;
                foreach (float fv in distanceAllBadList)
                {
                    TotalDist += fv;
                }
                MeanBadDistance = (float)(TotalDist / distanceAllBadList.Count);
            }
            if (mnger.FileDataManagers.Count > 0)
            {
                longitude = mnger.FileDataManagers[0].TestPoints[0].Longitude;
                latitude = mnger.FileDataManagers[0].TestPoints[0].Latitude;
                roadDesc = GISManager.GetInstance().GetRoadPlaceDesc(longitude, latitude);
                string strAreaName = GISManager.GetInstance().GetAreaPlaceDesc(longitude, latitude);
                areaName = addValidName(strAreaName, areaName);
                string strGridName = GISManager.GetInstance().GetGridDesc(longitude, latitude);
                gridName = addValidName(strGridName, gridName);
                string strAreaAgentName = GISManager.GetInstance().GetAreaAgentDesc(longitude, latitude);
                areaAgentName = addValidName(strAreaAgentName, areaAgentName);
            }
        }

        private string addValidName(string addName, string name)
        {
            if (addName != null)
            {
                if (name == null || name == "")
                {
                    name = addName;
                }
                else
                {
                    if (!name.Contains(addName) && addName != "")
                    {
                        name += "," + addName;
                    }
                }
            }
            return name;
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.ISTime = istime;
            bgResult.IETime = ietime;
            bgResult.LongitudeMid = longitude;
            bgResult.LatitudeMid = latitude;
            bgResult.RxLevMean = (float)avgRxLev;
            bgResult.RxLevMax = (float)MaxRxLev;
            bgResult.RxLevMin = (float)MinRxLev;
            bgResult.RxQualMean = (float)avgRxQual;
            bgResult.RxQualMax = (float)MaxRxQual;
            bgResult.RxQualMin = (float)MinRxQual;
            bgResult.RoadDesc = roadDesc;
            bgResult.AreaAgentDesc = areaAgentName;
            bgResult.AreaDesc = areaName;
            bgResult.GridDesc = gridName;
            bgResult.SampleCount = TestPointCount;
            bgResult.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;

            bgResult.AddImageValue(RxQualTestPointCount);
            bgResult.AddImageValue(badSampleCount);
            bgResult.AddImageValue(goodSampleCount);
            bgResult.AddImageValue(MeanBadDistance);
            bgResult.AddImageValue(maxBadDistance);
            bgResult.AddImageValue(minBadDistance);
            bgResult.AddImageValue((float)_CellCovRadius);
            bgResult.AddImageValue((float)rationalDistance);
            bgResult.AddImageValue(strFileID);
            bgResult.AddImageValue(bgResult.ProjectString);
            fillCellInfo(bgResult);
           
            return bgResult;
        }

        protected virtual void fillCellInfo(BackgroundResult bgResult)
        {
            bgResult.CellType = BackgroundCellType.GSM;
            bgResult.LAC = cell.LAC;
            bgResult.CI = cell.CI;
            bgResult.BCCH = cell.BCCH;
            bgResult.BSIC = cell.BSIC;
        }
    }

    public class CellCoverLap_TD : CellCoverLap
    {
        public TDCell tdCell { get; set; }

        protected override void fillCellInfo(BackgroundResult bgResult)
        {
            bgResult.CellType = BackgroundCellType.TD;
            bgResult.LAC = tdCell.LAC;
            bgResult.CI = tdCell.CI;
            bgResult.BCCH = tdCell.FREQ;
            bgResult.BSIC = tdCell.CPI;
        }
    }

    public class CellCoverLap_W : CellCoverLap
    {
        public WCell wCell { get; set; }

        protected override void fillCellInfo(BackgroundResult bgResult)
        {
            bgResult.CellType = BackgroundCellType.WCDMA;
            bgResult.LAC = wCell.LAC;
            bgResult.CI = wCell.CI;
            bgResult.BCCH = wCell.UARFCN;
            bgResult.BSIC = wCell.PSC;
        }
    }

    public class CellCoverLap_LTE : CellCoverLap
    {
        public LTECell lteCell { get; set; }

        public CellCoverLap_LTE(LTECell cell, double idealCoverDis)
        {
            this.lteCell = cell;
            this._CellCovRadius = idealCoverDis;
            if (cell.Altitude != 0 && cell.Altitude != 999)
            {
                double dir = 90 + cell.Downward / 2 - Math.Atan(idealCoverDis / cell.Altitude) * 180 / Math.PI;
                SuggustDownDir = Math.Round(dir, 2).ToString();
            }
            else
            {
                SuggustDownDir = "无挂高信息，无法计算建议下倾角";
            }
        }
        public short CellDownDir
        {
            get { return this.lteCell.Downward; }
        }
        public string SuggustDownDir
        {
            get;
            private set;
        }

        protected override void fillCellInfo(BackgroundResult bgResult)
        {
            bgResult.CellType = BackgroundCellType.LTE;
            bgResult.LAC = lteCell.TAC;
            bgResult.CI = lteCell.ECI;
            bgResult.BCCH = lteCell.EARFCN;
            bgResult.BSIC = lteCell.PCI;

            bgResult.StrDesc = lteCell.BelongBTS.BTSID + "," + lteCell.SCellID;
        }
    }
}
