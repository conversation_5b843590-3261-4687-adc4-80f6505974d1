﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.VoLTEDropCallCause
{
    public class CallInfo
    {
        private CallInfo otherSideCall;
        public CallInfo OtherSideCall
        {
            get { return otherSideCall; }
            set
            {
                otherSideCall = value;
                if (otherSideCall != null)
                {
                    otherSideCall.otherSideCall = this;
                }
            }
        }


        public DateTime EndTime { get; set; }

        private readonly List<Event> events = new List<Event>();
        internal void AddEvent(Model.Event evt)
        {
            events.Add(evt);
        }

        public DateTime BeginTime { get; set; }

        public bool IsDropCall { get; set; }

        public DateTime DropTime { get; set; }

        private readonly List<TestPoint> tps = new List<TestPoint>();
        internal void AddTestPoint(Model.TestPoint tp)
        {
            tps.Add(tp);
        }

        private readonly List<Message> messages = new List<Message>();
        public List<Message> Messages
        {
            get { return messages; }
        }
        public void AddMsg(Message msg)
        {
            messages.Add(msg);
        }

        public string MoMtDesc { get; set; }

        public float? RsrpAvg { get; set; }

        public float? SinrAvg { get; set; }

        public float? MultiCvrValueAvg { get; set; }
        public float MultiCvrPer { get; set; }

        public bool IsMultiCvr
        { get; set; }

        public int HoNum { get; set; }

        internal void Evaluate(DropCallCondition dropCallCond, bool both)
        {
            int rsrpNum = 0;
            float rsrpSum = 0;
            int sinrNum = 0;
            float sinrSum = 0;
            float multiNum = 0;
            float multiMatchNum = 0;
            float multiSum = 0;
            foreach (TestPoint tp in tps)
            {
                addRsrpInfo(dropCallCond, ref rsrpNum, ref rsrpSum, tp);
                addSinrInfo(dropCallCond, ref sinrNum, ref sinrSum, tp);
                addMultiInfo(dropCallCond, ref multiNum, ref multiMatchNum, ref multiSum, tp);
            }

            if (multiNum > 0)
            {
                MultiCvrValueAvg = (float)Math.Round(multiSum / multiNum, 2);
                MultiCvrPer = (float)Math.Round(multiMatchNum * 100.0 / multiNum, 2);
            }

            if (rsrpNum > 0)
            {
                this.RsrpAvg = (float)Math.Round(rsrpSum / rsrpNum, 2);
            }
            if (sinrNum > 0)
            {
                this.SinrAvg = (int)Math.Round(sinrSum / sinrNum, 2);
            }

            addHoNum(dropCallCond);

            bool netProb = false;
            foreach (DropCallCauseBase cause in dropCallCond.CauseSet)
            {
                if (cause.IsSatisfy(this))
                {
                    netProb = true;
                }
            }

            setDropCause(dropCallCond, netProb);

            if (both && this.otherSideCall != null)
            {
                this.otherSideCall.Evaluate(dropCallCond, false);
            }
        }

        private void addRsrpInfo(DropCallCondition dropCallCond, ref int rsrpNum, ref float rsrpSum, TestPoint tp)
        {
            if ((this.DropTime - DateTime.Parse(tp.DateTimeStringWithMillisecond)).TotalSeconds
                <= dropCallCond.WeakRsrpSec)
            {
                float? rsrp = GetRSRP(tp);
                if (rsrp != null)
                {
                    rsrpSum += (float)rsrp;
                    rsrpNum++;
                }
            }
        }

        private void addSinrInfo(DropCallCondition dropCallCond, ref int sinrNum, ref float sinrSum, TestPoint tp)
        {
            if ((this.DropTime - DateTime.Parse(tp.DateTimeStringWithMillisecond)).TotalSeconds
               <= dropCallCond.PoorSinrSec)
            {
                float? sinr = GetSINR(tp);
                if (sinr != null)
                {
                    sinrSum += (float)sinr;
                    sinrNum++;
                }
            }
        }

        private void addMultiInfo(DropCallCondition dropCallCond, ref float multiNum, ref float multiMatchNum, ref float multiSum, TestPoint tp)
        {
            if ((this.DropTime - DateTime.Parse(tp.DateTimeStringWithMillisecond)).TotalSeconds > dropCallCond.MultiSec)
            {
                return;
            }
            float? rsrp = GetRSRP(tp);
            if (rsrp != null)
            {
                multiNum++;
                int num = 1;
                for (int i = 0; i < 10; i++)
                {
                    float? rsrpN = GetNRSRP(tp, i);
                    if (rsrpN == null)
                    {
                        break;
                    }
                    if (Math.Abs((float)(rsrpN - rsrp)) <= dropCallCond.MultiBand)
                    {
                        num++;
                    }
                }
                multiSum += num;
                if (num >= dropCallCond.MultiValue)
                {
                    multiMatchNum++;
                }
            }
        }

        private void addHoNum(DropCallCondition dropCallCond)
        {
            foreach (Event evt in events)
            {
                if (evt.DateTime > this.DropTime)
                {
                    break;
                }
                if (evt.ID == 850 || evt.ID == 898 || evt.ID == 3155 || evt.ID == 3158)
                {//LTE Intra Handover Request & LTE Inter Handover Request
                    double totalSeconds = (DropTime - evt.DateTime).TotalSeconds;
                    if (totalSeconds <= dropCallCond.HoSec)
                    {
                        HoNum++;
                    }
                }
            }
        }

        private void setDropCause(DropCallCondition dropCallCond, bool netProb)
        {
            if (!netProb)
            {
                if (this.RsrpAvg <= dropCallCond.WeakRsrp)
                {
                    DropCause = DropCallCause.弱覆盖;
                }
                else if (this.SinrAvg <= dropCallCond.PoorSinr)
                {
                    DropCause = DropCallCause.质差;
                }
                else if (this.MultiCvrPer >= dropCallCond.MultiPer)
                {
                    DropCause = DropCallCause.高重叠覆盖;
                    IsMultiCvr = true;
                }
                else if (this.HoNum >= dropCallCond.HoNum)
                {
                    DropCause = DropCallCause.频繁切换;
                }
            }
        }

        protected float? GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRP"];
            }
            return (float?)tp["lte_RSRP"];
        }
        protected float? GetSINR(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_SINR"];
            }
            return (float?)tp["lte_SINR"];
        }
        protected float? GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_NCell_RSRP",index];
            }
            return (float?)tp["lte_NCell_RSRP", index];
        }
        
        public DropCallCause DropCause { get; set; } = DropCallCause.其它;

        public string Suggest
        { get; set; }


        public string FileName { get; set; }

        public Event DropEvt { get; set; }

        public List<TestPoint> TestPoints { get { return tps; } }
        public List<Event> Events { get { return events; } }

        public bool IsFilter { get; set; }
    }

    public enum DropCallCause
    {
        VoiceHangup,
        双BYE,
        BYE_Request_Terminated,
        提前释放EPS专用承载,
        弱覆盖,
        质差,
        高重叠覆盖,
        频繁切换,
        其它
    }

}
