﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.ZTFunc.ZTLTESINR;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTESINRQuery : DIYAnalyseFilesOneByOneByRegion
    {


        protected LTESINRAnalyzer analyzer;

        protected List<DTFileDataManager> fileManagers = null;

        protected SetConditionDlg setForm = null;

        

        public LTESINRQuery(MainModel mModel)
            : base(mModel)
        {
            this.IncludeEvent = false;
            analyzer = new LTESINRAnalyzer();
        }

        public override string Name
        {
            get { return "SINR与RSRP关联"; }
        }
       
        public LTESINRAnalyzer Analyzer
        {
            get { return analyzer; }
        }

        
        protected override void getReadyBeforeQuery()
        {
            analyzer.Refreshs();
            if (fileManagers == null)
            {
                fileManagers = new List<DTFileDataManager>();
            }
            else
            {
                fileManagers.Clear();
            }

        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22063, this.Name);
        }

        protected override bool getCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new SetConditionDlg();
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            
            this.Analyzer.Condition = new SaveCondition();
            this.Analyzer.Condition = setForm.GetCondition();
            return true;
        }


        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager f in MainModel.DTDataManager.FileDataManagers)
            {
                DTFileDataManager file = new DTFileDataManager(f.FileID, f.FileName, f.ProjectType, f.TestType,
                    f.CarrierType, f.LogTable, f.SampleTableName, f.ServiceType, f.MoMtFlag);

                foreach (TestPoint tp in f.TestPoints)
                {
                    if (f.CarrierType == 1)
                    {
                        if ((float?)tp["lte_SINR"] == null)
                        {
                            continue;
                        }
                        file.Add(tp);
                    }
                    else
                    {
                        if ((float?)tp["lte_fdd_SINR"] == null)
                        {
                            continue;
                        }
                        file.Add(tp);
                    }
                }
                fileManagers.Add(file);
            }
            analyzer.Analyze(fileManagers);
            //int count = MainModel.DTDataManager.FileDataManagers.Count;
            //for (int i = 0; i < count; i++)
            //{
            //    DTFileDataManager f = MainModel.DTDataManager.FileDataManagers[0];
            //    DTFileDataManager file = new DTFileDataManager(f.FileID, f.FileName, f.ProjectType, f.TestType,
            //        f.CarrierType, f.LogTable, f.SampleTableName, f.ServiceType, f.MoMtFlag);
            //    foreach (TestPoint tp in f.TestPoints)
            //    {
            //        if (f.CarrierType == 1)
            //        {
            //            if ((float?)tp["lte_SINR"] == null)
            //            {
            //                continue;
            //            }
            //            file.Add(tp);
            //        }
            //        else
            //        {
            //            if ((float?)tp["lte_fdd_SINR"] == null)
            //            {
            //                continue;
            //            }
            //            file.Add(tp);
            //        }
            //    }
            //    fileManagers.Add(file);
            //    MainModel.DTDataManager.FileDataManagers.Remove(f);
            //}
          
        }

        protected override void getResultsAfterQuery()
        {
            analyzer.Analyze(fileManagers);
        }

        protected override void fireShowForm()
        {
            LTESINRResult resultForm = MainModel.GetObjectFromBlackboard(
                typeof(LTESINRResult).FullName) as LTESINRResult;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new LTESINRResult(MainModel);
            }
            resultForm.FillData(analyzer.GetResult());
            analyzer.ClearResult();
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }             
   }
}
