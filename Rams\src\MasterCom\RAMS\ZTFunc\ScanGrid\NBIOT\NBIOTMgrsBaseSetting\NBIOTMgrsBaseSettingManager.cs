﻿using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsBaseSettingManager
    {
        private static NbIotMgrsBaseSettingManager instance = null;
        public static NbIotMgrsBaseSettingManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NbIotMgrsBaseSettingManager();
                }
                return instance;
            }
        }

        private NbIotMgrsBaseSettingManager()
        {
            BandValues = new List<NbIotBandRange>();
            BandType = new NBMgrsCoverageBandType();

            XmlConfigFile configFile = new MyXmlConfigFile(ConfigPath);
            if (configFile.Load())
            {
                Dictionary<string, object> newDic = configFile.GetItemValue("BaseSetting", "BaseSettingConfig") as Dictionary<string, object>;
                if (newDic != null)
                {
                    Param = newDic;
                }
                else
                {
                    //加载基础设置失败默认栅格大小50
                    GridSize = 50;
                }
            }
        }

        public readonly string ConfigPath = Application.StartupPath + @"\config\NBIOTMgrsCondition.xml";

        // 栅格大小
        public int GridSize { get; set; }

        public List<NbIotBandRange> BandValues { get; set; }
        
        public NBMgrsCoverageBandType BandType { get; private set; }

        public void SetBandType()
        {
            BandType = new NBMgrsCoverageBandType();
            foreach (var item in BandValues)
            {
                if (!BandType.BandType.ContainsKey(item.BandName))
                {
                    BandType.BandType.Add(item.BandName, item.BandName);
                }
            }
        }

        public bool JudgeInBand(string type, int earfcn)
        {
            foreach (var item in BandValues)
            {
                if (type == item.BandName)
                {
                    if (item.Type == 0 && item.Value == earfcn)
                    {
                        return true;
                    }
                    else if (item.Type == 1 && earfcn <= item.MaxRangeValue && earfcn >= item.MinRangeValue)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public void SaveConfig(XmlConfigFile xcfg)
        {
            XmlElement cfg = xcfg.AddConfig("BaseSetting");
            xcfg.AddItem(cfg, "BaseSettingConfig", this.Param);
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["GridSize"] = this.GridSize;
                List<object> rangeParams = new List<object>();
                param["NBIOTBandRange"] = rangeParams;
                foreach (NbIotBandRange band in BandValues)
                {
                    rangeParams.Add(band.Param);
                }
                return param;
            }
            set
            {
                this.GridSize = (int)value["GridSize"];
                List<object> rangeParams = (List<object>)value["NBIOTBandRange"];
                foreach (object o in rangeParams)
                {
                    Dictionary<string, object> rangeParam = (Dictionary<string, object>)o;
                    NbIotBandRange range = new NbIotBandRange();
                    range.Param = rangeParam;
                    BandValues.Add(range);
                }
            }
        }
    }
}
