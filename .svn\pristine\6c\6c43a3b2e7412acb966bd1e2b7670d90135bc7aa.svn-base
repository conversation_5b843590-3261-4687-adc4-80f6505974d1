﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MapWinGIS;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTGDGridCompareStatForm : MinCloseForm
    {
        public ZTGDGridCompareStatForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            initDetail();
            initDate();
            init();
            DisposeWhenClose = true;
        }

        private void init()
        {
            this.olvColumnIndex.AspectGetter = delegate(object row)
            {
                GridCellTestPointInfo cellInfo = row as GridCellTestPointInfo;
                return cellInfo.ISN;
            };
            this.olvColumnCityName.AspectGetter = delegate(object row)
            {
                GridCellTestPointInfo cellInfo = row as GridCellTestPointInfo;
                return cellInfo.StrCityName;
            };
            this.olvColumnGrid.AspectGetter = delegate(object row)
            {
                GridCellTestPointInfo cellInfo = row as GridCellTestPointInfo;
                return cellInfo.StrGridName;
            };
            this.olvColumnDataSource.AspectGetter = delegate(object row)
            {
                GridCellTestPointInfo cellInfo = row as GridCellTestPointInfo;
                return cellInfo.StrDataSource;
            };
            this.olvColumnTakeUp.AspectGetter = delegate(object row)
            {
                GridCellTestPointInfo cellInfo = row as GridCellTestPointInfo;
                return cellInfo.StrTakeUp;
            };
            this.olvColumnCellName.AspectGetter = delegate(object row)
            {
                GridCellTestPointInfo cellInfo = row as GridCellTestPointInfo;
                return cellInfo.StrCellName;
            };
            this.olvColumnECI.AspectGetter = delegate(object row)
            {
                GridCellTestPointInfo cellInfo = row as GridCellTestPointInfo;
                return cellInfo.IECI;
            };
            this.olvColumnDistance.AspectGetter = delegate(object row)
            {
                GridCellTestPointInfo cellInfo = row as GridCellTestPointInfo;
                return Math.Round(cellInfo.DDistance ,2);
            };
            this.olvColumnTestDate.AspectGetter = delegate(object row)
            {
                GridCellTestPointInfo cellInfo = row as GridCellTestPointInfo;
                return cellInfo.StrTestDate;
            };
        }

        private void initDate()
        {
            this.olvDateSN.AspectGetter = delegate(object row)
            {
                GridCellDateInfo cellInfo = row as GridCellDateInfo;
                return cellInfo.ISN;
            };
            this.olvDateCity.AspectGetter = delegate(object row)
            {
                GridCellDateInfo cellInfo = row as GridCellDateInfo;
                return cellInfo.StrCityName;
            };
            this.olvDateGrid.AspectGetter = delegate(object row)
            {
                GridCellDateInfo cellInfo = row as GridCellDateInfo;
                return cellInfo.StrGridName.Trim(',');
            };
            this.olvDateCellName.AspectGetter = delegate(object row)
            {
                GridCellDateInfo cellInfo = row as GridCellDateInfo;
                return cellInfo.StrCellName;
            };
            this.olvDateECI.AspectGetter = delegate(object row)
            {
                GridCellDateInfo cellInfo = row as GridCellDateInfo;
                return cellInfo.IECI;
            };
            this.olvDateIsAlarm.AspectGetter = delegate(object row)
            {
                GridCellDateInfo cellInfo = row as GridCellDateInfo;
                return cellInfo.StrAlarm;
            };
            this.olvDateAlarmDate.AspectGetter = delegate(object row)
            {
                GridCellDateInfo cellInfo = row as GridCellDateInfo;
                return cellInfo.StrAlarmDate;
            };
        }

        private void initDetail()
        {
            this.olvSN.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.ISN;
            };
            this.olvCity.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.StrCityName;
            };
            this.olvGrid.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.StrGridName.Trim(',');
            };
            this.olvCell.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.StrCellName;
            };
            this.olvECI.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.IECI;
            };
            this.olvTestDate.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.StrTestDate;
            };
            this.olvTimePeriod.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.strTimeSpanDes;
            };
            this.olvTakeUpTime.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.StrTestTakeUpTime;
            };
            this.olvTestGridLngLat.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.StrTestGridLngLat;
            };
            this.olvSampleNum.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.ITestPointNum;
            };
            this.olvDistance.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return Math.Round(cellInfo.DDistance, 2);
            };
            this.olvFileName.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.StrTestFileName;
            };
            this.olvIsAlarm.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.StrAlarm;
            };
            this.olvIsAlarmDate.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.StrAlarmDate;
            };
            this.olvAlarmDate.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrDate;
            };
            this.olvAlarmCity.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrCity;
            };
            this.olvAlarmCellName.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrCellName;
            };
            this.olvAlarmCGI.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrCGI;
            };
            this.olvAlarmBtsName.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrBtsName;
            };
            this.olvAlarmNet.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrNetWork;
            };
            this.olvAlarmCJ.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrManufacturer;
            };
            this.olvAlarmCover.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrCoverType;
            };
            this.olvAlarmSence.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrScene;
            };
            this.olvAlarmProblem.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrProblemType;
            };
            this.olvAlarmStartTime.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrStartTime;
            };
            this.olvAlarmEndTime.AspectGetter = delegate(object row)
            {
                TimeSpandGridCellInfo cellInfo = row as TimeSpandGridCellInfo;
                return cellInfo.CellAlarmInfoNew.StrEndTime;
            };
        }

        private ContextMenuStrip conOutPutExcel;
        private System.ComponentModel.IContainer components;
        private ToolStripMenuItem outPutExcel;
        private DevExpress.XtraTab.XtraTabControl xtraTabData;
        private DevExpress.XtraTab.XtraTabPage xtraTabHZ;
        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnIndex;
        private BrightIdeasSoftware.OLVColumn olvColumnCityName;
        private BrightIdeasSoftware.OLVColumn olvColumnGrid;
        private BrightIdeasSoftware.OLVColumn olvColumnDataSource;
        private BrightIdeasSoftware.OLVColumn olvColumnTakeUp;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnECI;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnTestDate;
        private DevExpress.XtraTab.XtraTabPage xtraTabXQ;
        private BrightIdeasSoftware.ObjectListView objectListViewDetail;
        private BrightIdeasSoftware.OLVColumn olvSN;
        private BrightIdeasSoftware.OLVColumn olvCity;
        private BrightIdeasSoftware.OLVColumn olvGrid;
        private BrightIdeasSoftware.OLVColumn olvCell;
        private BrightIdeasSoftware.OLVColumn olvECI;
        private BrightIdeasSoftware.OLVColumn olvTestDate;
        private BrightIdeasSoftware.OLVColumn olvTimePeriod;
        private BrightIdeasSoftware.OLVColumn olvTakeUpTime;
        private BrightIdeasSoftware.OLVColumn olvSampleNum;
        private BrightIdeasSoftware.OLVColumn olvDistance;
        private BrightIdeasSoftware.OLVColumn olvFileName;
        private BrightIdeasSoftware.OLVColumn olvIsAlarm;
        private BrightIdeasSoftware.OLVColumn olvAlarmDate;
        private BrightIdeasSoftware.OLVColumn olvAlarmCity;
        private BrightIdeasSoftware.OLVColumn olvAlarmCellName;
        private BrightIdeasSoftware.OLVColumn olvAlarmCGI;
        private BrightIdeasSoftware.OLVColumn olvAlarmBtsName;
        private BrightIdeasSoftware.OLVColumn olvAlarmNet;
        private BrightIdeasSoftware.OLVColumn olvAlarmCJ;
        private BrightIdeasSoftware.OLVColumn olvAlarmCover;
        private BrightIdeasSoftware.OLVColumn olvAlarmSence;
        private BrightIdeasSoftware.OLVColumn olvAlarmProblem;
        private BrightIdeasSoftware.OLVColumn olvAlarmStartTime;
        private BrightIdeasSoftware.OLVColumn olvAlarmEndTime;

        readonly List<GridCellTestPointInfo> cellTakeUpList = new List<GridCellTestPointInfo>();
        private ToolStripMenuItem outPutShap;
        readonly List<TimeSpandGridCellInfo> cellTakeUpDetailsList = new List<TimeSpandGridCellInfo>();
        readonly List<GridCellDateInfo> cellDateInfoList = new List<GridCellDateInfo>();
        private BrightIdeasSoftware.OLVColumn olvIsAlarmDate;
        private DevExpress.XtraTab.XtraTabPage xtraTabDateHZ;
        private BrightIdeasSoftware.ObjectListView ListViewDetailDateHZ;
        private BrightIdeasSoftware.OLVColumn olvDateSN;
        private BrightIdeasSoftware.OLVColumn olvDateCity;
        private BrightIdeasSoftware.OLVColumn olvDateGrid;
        private BrightIdeasSoftware.OLVColumn olvDateCellName;
        private BrightIdeasSoftware.OLVColumn olvDateECI;
        private BrightIdeasSoftware.OLVColumn olvDateIsAlarm;
        private BrightIdeasSoftware.OLVColumn olvDateAlarmDate;
        private BrightIdeasSoftware.OLVColumn olvTestGridLngLat;
        Dictionary<string, MapWinGIS.Shape> gridShapStatDic = new Dictionary<string, MapWinGIS.Shape>();
        private ToolStripMenuItem outPutTestShap;
        Dictionary<string, MapWinGIS.Shape> gridTestShapStatDic = new Dictionary<string, MapWinGIS.Shape>();
        public void FillData(Dictionary<GridCellKey, GridCellTestPointInfo> curCellShowDic, Dictionary<GridCellKey, GridCellTestPointInfo> preCellShowDic
                            , Dictionary<TimeGridCellKey, TimeSpandGridCellInfo> timeSpanGridCellInfoDic, Dictionary<string, MapWinGIS.Shape> gridShapDic
                            , List<GridCellDateInfo> cellDateInfoListTmp, Dictionary<string, MapWinGIS.Shape> gridTestShapDic)
        {
            cellTakeUpDetailsList.Clear();
            this.cellTakeUpDetailsList.AddRange(timeSpanGridCellInfoDic.Values);
            objectListViewDetail.ClearObjects();
            objectListViewDetail.SetObjects(this.cellTakeUpDetailsList);

            cellDateInfoList.Clear();
            cellDateInfoList.AddRange(cellDateInfoListTmp);
            ListViewDetailDateHZ.ClearObjects();
            ListViewDetailDateHZ.SetObjects(this.cellDateInfoList);

            cellTakeUpList.Clear();
            cellTakeUpList.AddRange(preCellShowDic.Values);
            cellTakeUpList.AddRange(curCellShowDic.Values);
            objectListView.ClearObjects();
            objectListView.SetObjects(this.cellTakeUpList);

            gridShapStatDic.Clear();
            gridShapStatDic = gridShapDic;
            gridTestShapStatDic.Clear();
            gridTestShapStatDic = gridTestShapDic;
        }

        private void exportToExcel()
        {
            List<NPOIRow> dataDetails = new List<NPOIRow>();
            List<NPOIRow> dataDate = new List<NPOIRow>();
            List<NPOIRow> datas = new List<NPOIRow>();

            #region 表头字段
            NPOIRow nrTitle = new NPOIRow();
            List<object> rowTitle = new List<object>();
            rowTitle.Add("序号");
            rowTitle.Add("地市名称");
            rowTitle.Add("测试网格");
            rowTitle.Add("数据源");
            rowTitle.Add("占用情况");
            rowTitle.Add("小区名称");
            rowTitle.Add("ECI");
            rowTitle.Add("测试里程(M)");
            rowTitle.Add("测试日期");
            nrTitle.cellValues = rowTitle;
            datas.Add(nrTitle);

            nrTitle = new NPOIRow();
            rowTitle = new List<object>();
            rowTitle.Add("序号");
            rowTitle.Add("地市名称");
            rowTitle.Add("测试网格");
            rowTitle.Add("小区名称");
            rowTitle.Add("ECI");
            rowTitle.Add("是否因退服而影响测试");
            rowTitle.Add("是否在亿阳不可用小区表");
            nrTitle.cellValues = rowTitle;
            dataDate.Add(nrTitle);

            nrTitle = new NPOIRow();
            rowTitle = new List<object>();
            rowTitle.Add("序号");
            rowTitle.Add("地市名称");
            rowTitle.Add("测试网格");
            rowTitle.Add("小区名称");
            rowTitle.Add("ECI");
            rowTitle.Add("本次栅格测试日期");
            rowTitle.Add("比对时间粒度");
            rowTitle.Add("本次占用栅格时间段");
            //rowTitle.Add("本次占用栅格左上经纬度")
            rowTitle.Add("本次测试采样点数");
            rowTitle.Add("本次测试里程数(米)");
            rowTitle.Add("文件名");
            rowTitle.Add("是否因退服而影响测试");
            rowTitle.Add("是否在亿阳不可用小区表");
            rowTitle.Add("CGI");
            rowTitle.Add("基站名称");
            rowTitle.Add("网络制式");
            rowTitle.Add("厂家");
            rowTitle.Add("覆盖类型");
            rowTitle.Add("覆盖场景");
            rowTitle.Add("问题类型");
            rowTitle.Add("退服开始时间");
            rowTitle.Add("退服结束时间");
            nrTitle.cellValues = rowTitle;
            dataDetails.Add(nrTitle);
            #endregion

            foreach (GridCellTestPointInfo cellInfo in this.cellTakeUpList)
            {
                NPOIRow nrData = new NPOIRow();
                List<object> row = new List<object>();
                row.Add(cellInfo.ISN);
                row.Add(cellInfo.StrCityName);
                row.Add(cellInfo.StrGridName.Replace(",", "，"));
                row.Add(cellInfo.StrDataSource);
                row.Add(cellInfo.StrTakeUp);
                row.Add(cellInfo.StrCellName);
                row.Add(cellInfo.IECI);
                row.Add(Math.Round(cellInfo.DDistance,2));
                row.Add(cellInfo.StrTestDate.Replace(",","，")) ;
                nrData.cellValues = row;
                datas.Add(nrData);
            }

            foreach (GridCellDateInfo cellInfo in this.cellDateInfoList)
            {
                NPOIRow nrData = new NPOIRow();
                List<object> row = new List<object>();
                row.Add(cellInfo.ISN);
                row.Add(cellInfo.StrCityName);
                row.Add(cellInfo.StrGridName.Replace(",", "，"));
                row.Add(cellInfo.StrCellName);
                row.Add(cellInfo.IECI);
                row.Add(cellInfo.StrAlarm);
                row.Add(cellInfo.StrAlarmDate);
                nrData.cellValues = row;
                dataDate.Add(nrData);
            }

            foreach (TimeSpandGridCellInfo timeSpanCellInfo in this.cellTakeUpDetailsList)
            {
                NPOIRow nrData = new NPOIRow();
                List<object> row = new List<object>();
                row.Add(timeSpanCellInfo.ISN);
                row.Add(timeSpanCellInfo.StrCityName);
                row.Add(timeSpanCellInfo.StrGridName.Replace(",", "，"));
                row.Add(timeSpanCellInfo.StrCellName);
                row.Add(timeSpanCellInfo.IECI);
                row.Add(timeSpanCellInfo.StrTestDate);
                row.Add(timeSpanCellInfo.strTimeSpanDes);
                row.Add(timeSpanCellInfo.StrTestTakeUpTime);
                //row.Add(timeSpanCellInfo.StrTestGridLngLat)
                row.Add(timeSpanCellInfo.ITestPointNum);
                row.Add(Math.Round(timeSpanCellInfo.DDistance, 2));
                row.Add(timeSpanCellInfo.StrTestFileName);
                row.Add(timeSpanCellInfo.StrAlarm);
                row.Add(timeSpanCellInfo.StrAlarmDate);
                row.Add(timeSpanCellInfo.CellAlarmInfoNew.StrCGI);
                row.Add(timeSpanCellInfo.CellAlarmInfoNew.StrBtsName);
                row.Add(timeSpanCellInfo.CellAlarmInfoNew.StrNetWork);
                row.Add(timeSpanCellInfo.CellAlarmInfoNew.StrManufacturer);
                row.Add(timeSpanCellInfo.CellAlarmInfoNew.StrCoverType);
                row.Add(timeSpanCellInfo.CellAlarmInfoNew.StrScene);
                row.Add(timeSpanCellInfo.CellAlarmInfoNew.StrProblemType);
                row.Add(timeSpanCellInfo.CellAlarmInfoNew.StrStartTime);
                row.Add(timeSpanCellInfo.CellAlarmInfoNew.StrEndTime);
                nrData.cellValues = row;
                dataDetails.Add(nrData);
            }
            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            List<string> sheetNames = new List<string>();
            nrDatasList.Add(dataDetails);
            nrDatasList.Add(dataDate);
            nrDatasList.Add(datas);
            sheetNames.Add("重合路段小区占用统计明细");
            sheetNames.Add("重合路段小区占用统计简表");
            sheetNames.Add("测试小区集合");
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.conOutPutExcel = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.outPutExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.outPutTestShap = new System.Windows.Forms.ToolStripMenuItem();
            this.outPutShap = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabData = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabXQ = new DevExpress.XtraTab.XtraTabPage();
            this.objectListViewDetail = new BrightIdeasSoftware.ObjectListView();
            this.olvSN = new BrightIdeasSoftware.OLVColumn();
            this.olvCity = new BrightIdeasSoftware.OLVColumn();
            this.olvGrid = new BrightIdeasSoftware.OLVColumn();
            this.olvCell = new BrightIdeasSoftware.OLVColumn();
            this.olvECI = new BrightIdeasSoftware.OLVColumn();
            this.olvTestDate = new BrightIdeasSoftware.OLVColumn();
            this.olvTimePeriod = new BrightIdeasSoftware.OLVColumn();
            this.olvTakeUpTime = new BrightIdeasSoftware.OLVColumn();
            this.olvTestGridLngLat = new BrightIdeasSoftware.OLVColumn();
            this.olvSampleNum = new BrightIdeasSoftware.OLVColumn();
            this.olvDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvIsAlarm = new BrightIdeasSoftware.OLVColumn();
            this.olvIsAlarmDate = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmDate = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmCity = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmCGI = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmBtsName = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmNet = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmCJ = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmCover = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmSence = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmProblem = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmStartTime = new BrightIdeasSoftware.OLVColumn();
            this.olvAlarmEndTime = new BrightIdeasSoftware.OLVColumn();
            this.xtraTabDateHZ = new DevExpress.XtraTab.XtraTabPage();
            this.ListViewDetailDateHZ = new BrightIdeasSoftware.ObjectListView();
            this.olvDateSN = new BrightIdeasSoftware.OLVColumn();
            this.olvDateCity = new BrightIdeasSoftware.OLVColumn();
            this.olvDateGrid = new BrightIdeasSoftware.OLVColumn();
            this.olvDateCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvDateECI = new BrightIdeasSoftware.OLVColumn();
            this.olvDateIsAlarm = new BrightIdeasSoftware.OLVColumn();
            this.olvDateAlarmDate = new BrightIdeasSoftware.OLVColumn();
            this.xtraTabHZ = new DevExpress.XtraTab.XtraTabPage();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnIndex = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCityName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnGrid = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDataSource = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTakeUp = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnECI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTestDate = new BrightIdeasSoftware.OLVColumn();
            this.conOutPutExcel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabData)).BeginInit();
            this.xtraTabData.SuspendLayout();
            this.xtraTabXQ.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewDetail)).BeginInit();
            this.xtraTabDateHZ.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewDetailDateHZ)).BeginInit();
            this.xtraTabHZ.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.SuspendLayout();
            // 
            // conOutPutExcel
            // 
            this.conOutPutExcel.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.outPutExcel,
            this.outPutTestShap,
            this.outPutShap});
            this.conOutPutExcel.Name = "conOutPutExcel";
            this.conOutPutExcel.Size = new System.Drawing.Size(197, 70);
            this.conOutPutExcel.Text = "导出数据";
            // 
            // outPutExcel
            // 
            this.outPutExcel.Name = "outPutExcel";
            this.outPutExcel.Size = new System.Drawing.Size(196, 22);
            this.outPutExcel.Text = "导出Excel";
            this.outPutExcel.Click += new System.EventHandler(this.outPutExcel_Click);
            // 
            // outPutTestShap
            // 
            this.outPutTestShap.Name = "outPutTestShap";
            this.outPutTestShap.Size = new System.Drawing.Size(196, 22);
            this.outPutTestShap.Text = "导出告警小区覆盖轨迹";
            this.outPutTestShap.Click += new System.EventHandler(this.outPutTestShap_Click);
            // 
            // outPutShap
            // 
            this.outPutShap.Name = "outPutShap";
            this.outPutShap.Size = new System.Drawing.Size(196, 22);
            this.outPutShap.Text = "导出多跑测试轨迹";
            this.outPutShap.Click += new System.EventHandler(this.outPutShap_Click);
            // 
            // xtraTabData
            // 
            this.xtraTabData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabData.Location = new System.Drawing.Point(0, 0);
            this.xtraTabData.Name = "xtraTabData";
            this.xtraTabData.SelectedTabPage = this.xtraTabXQ;
            this.xtraTabData.Size = new System.Drawing.Size(987, 397);
            this.xtraTabData.TabIndex = 6;
            this.xtraTabData.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabXQ,
            this.xtraTabDateHZ,
            this.xtraTabHZ});
            // 
            // xtraTabXQ
            // 
            this.xtraTabXQ.Controls.Add(this.objectListViewDetail);
            this.xtraTabXQ.Name = "xtraTabXQ";
            this.xtraTabXQ.Size = new System.Drawing.Size(980, 367);
            this.xtraTabXQ.Text = "重合路段小区占用统计明细";
            // 
            // objectListViewDetail
            // 
            this.objectListViewDetail.AllColumns.Add(this.olvSN);
            this.objectListViewDetail.AllColumns.Add(this.olvCity);
            this.objectListViewDetail.AllColumns.Add(this.olvGrid);
            this.objectListViewDetail.AllColumns.Add(this.olvCell);
            this.objectListViewDetail.AllColumns.Add(this.olvECI);
            this.objectListViewDetail.AllColumns.Add(this.olvTestDate);
            this.objectListViewDetail.AllColumns.Add(this.olvTimePeriod);
            this.objectListViewDetail.AllColumns.Add(this.olvTakeUpTime);
            this.objectListViewDetail.AllColumns.Add(this.olvTestGridLngLat);
            this.objectListViewDetail.AllColumns.Add(this.olvSampleNum);
            this.objectListViewDetail.AllColumns.Add(this.olvDistance);
            this.objectListViewDetail.AllColumns.Add(this.olvFileName);
            this.objectListViewDetail.AllColumns.Add(this.olvIsAlarm);
            this.objectListViewDetail.AllColumns.Add(this.olvIsAlarmDate);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmDate);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmCity);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmCellName);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmCGI);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmBtsName);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmNet);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmCJ);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmCover);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmSence);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmProblem);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmStartTime);
            this.objectListViewDetail.AllColumns.Add(this.olvAlarmEndTime);
            this.objectListViewDetail.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvSN,
            this.olvCity,
            this.olvGrid,
            this.olvCell,
            this.olvECI,
            this.olvTestDate,
            this.olvTimePeriod,
            this.olvTakeUpTime,
            this.olvSampleNum,
            this.olvDistance,
            this.olvFileName,
            this.olvIsAlarm,
            this.olvIsAlarmDate,
            this.olvAlarmDate,
            this.olvAlarmCity,
            this.olvAlarmCellName,
            this.olvAlarmCGI,
            this.olvAlarmBtsName,
            this.olvAlarmNet,
            this.olvAlarmCJ,
            this.olvAlarmCover,
            this.olvAlarmSence,
            this.olvAlarmProblem,
            this.olvAlarmStartTime,
            this.olvAlarmEndTime});
            this.objectListViewDetail.ContextMenuStrip = this.conOutPutExcel;
            this.objectListViewDetail.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListViewDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListViewDetail.FullRowSelect = true;
            this.objectListViewDetail.GridLines = true;
            this.objectListViewDetail.HeaderWordWrap = true;
            this.objectListViewDetail.Location = new System.Drawing.Point(0, 0);
            this.objectListViewDetail.Name = "objectListViewDetail";
            this.objectListViewDetail.ShowGroups = false;
            this.objectListViewDetail.Size = new System.Drawing.Size(980, 367);
            this.objectListViewDetail.TabIndex = 7;
            this.objectListViewDetail.UseCompatibleStateImageBehavior = false;
            this.objectListViewDetail.View = System.Windows.Forms.View.Details;
            // 
            // olvSN
            // 
            this.olvSN.HeaderFont = null;
            this.olvSN.Text = "序号";
            this.olvSN.Width = 55;
            // 
            // olvCity
            // 
            this.olvCity.HeaderFont = null;
            this.olvCity.Text = "地市名称";
            this.olvCity.Width = 80;
            // 
            // olvGrid
            // 
            this.olvGrid.HeaderFont = null;
            this.olvGrid.Text = "网格";
            this.olvGrid.Width = 53;
            // 
            // olvCell
            // 
            this.olvCell.HeaderFont = null;
            this.olvCell.Text = "小区名称";
            // 
            // olvECI
            // 
            this.olvECI.HeaderFont = null;
            this.olvECI.Text = "ECI";
            this.olvECI.Width = 99;
            // 
            // olvTestDate
            // 
            this.olvTestDate.HeaderFont = null;
            this.olvTestDate.Text = "本次栅格测试日期";
            this.olvTestDate.Width = 112;
            // 
            // olvTimePeriod
            // 
            this.olvTimePeriod.HeaderFont = null;
            this.olvTimePeriod.Text = "比对时间粒度";
            this.olvTimePeriod.Width = 114;
            // 
            // olvTakeUpTime
            // 
            this.olvTakeUpTime.HeaderFont = null;
            this.olvTakeUpTime.Text = "本次占用栅格时间段";
            this.olvTakeUpTime.Width = 118;
            // 
            // olvTestGridLngLat
            // 
            this.olvTestGridLngLat.DisplayIndex = 8;
            this.olvTestGridLngLat.HeaderFont = null;
            this.olvTestGridLngLat.IsVisible = false;
            this.olvTestGridLngLat.Text = "本次测试占用栅格";
            this.olvTestGridLngLat.Width = 108;
            // 
            // olvSampleNum
            // 
            this.olvSampleNum.HeaderFont = null;
            this.olvSampleNum.Text = "本次测试采样点数";
            this.olvSampleNum.Width = 123;
            // 
            // olvDistance
            // 
            this.olvDistance.HeaderFont = null;
            this.olvDistance.Text = "本次测试里程数(米)";
            // 
            // olvFileName
            // 
            this.olvFileName.HeaderFont = null;
            this.olvFileName.Text = "文件名";
            // 
            // olvIsAlarm
            // 
            this.olvIsAlarm.HeaderFont = null;
            this.olvIsAlarm.Text = "是否因退服而影响测试";
            // 
            // olvIsAlarmDate
            // 
            this.olvIsAlarmDate.HeaderFont = null;
            this.olvIsAlarmDate.Text = "是否在亿阳不可用小区表";
            // 
            // olvAlarmDate
            // 
            this.olvAlarmDate.HeaderFont = null;
            this.olvAlarmDate.Text = "日期";
            // 
            // olvAlarmCity
            // 
            this.olvAlarmCity.HeaderFont = null;
            this.olvAlarmCity.Text = "地市";
            // 
            // olvAlarmCellName
            // 
            this.olvAlarmCellName.HeaderFont = null;
            this.olvAlarmCellName.Text = "小区名";
            // 
            // olvAlarmCGI
            // 
            this.olvAlarmCGI.HeaderFont = null;
            this.olvAlarmCGI.Text = "CGI";
            // 
            // olvAlarmBtsName
            // 
            this.olvAlarmBtsName.HeaderFont = null;
            this.olvAlarmBtsName.Text = "基站名称";
            // 
            // olvAlarmNet
            // 
            this.olvAlarmNet.HeaderFont = null;
            this.olvAlarmNet.Text = "网络制式";
            // 
            // olvAlarmCJ
            // 
            this.olvAlarmCJ.HeaderFont = null;
            this.olvAlarmCJ.Text = "厂家";
            // 
            // olvAlarmCover
            // 
            this.olvAlarmCover.HeaderFont = null;
            this.olvAlarmCover.Text = "覆盖类型";
            // 
            // olvAlarmSence
            // 
            this.olvAlarmSence.HeaderFont = null;
            this.olvAlarmSence.Text = "覆盖场景";
            // 
            // olvAlarmProblem
            // 
            this.olvAlarmProblem.HeaderFont = null;
            this.olvAlarmProblem.Text = "问题类型";
            // 
            // olvAlarmStartTime
            // 
            this.olvAlarmStartTime.HeaderFont = null;
            this.olvAlarmStartTime.Text = "退服开始时间";
            // 
            // olvAlarmEndTime
            // 
            this.olvAlarmEndTime.HeaderFont = null;
            this.olvAlarmEndTime.Text = "退服结束时间";
            // 
            // xtraTabDateHZ
            // 
            this.xtraTabDateHZ.Controls.Add(this.ListViewDetailDateHZ);
            this.xtraTabDateHZ.Name = "xtraTabDateHZ";
            this.xtraTabDateHZ.Size = new System.Drawing.Size(980, 367);
            this.xtraTabDateHZ.Text = "重合路段小区占用统计简表";
            // 
            // ListViewDetailDateHZ
            // 
            this.ListViewDetailDateHZ.AllColumns.Add(this.olvDateSN);
            this.ListViewDetailDateHZ.AllColumns.Add(this.olvDateCity);
            this.ListViewDetailDateHZ.AllColumns.Add(this.olvDateGrid);
            this.ListViewDetailDateHZ.AllColumns.Add(this.olvDateCellName);
            this.ListViewDetailDateHZ.AllColumns.Add(this.olvDateECI);
            this.ListViewDetailDateHZ.AllColumns.Add(this.olvDateIsAlarm);
            this.ListViewDetailDateHZ.AllColumns.Add(this.olvDateAlarmDate);
            this.ListViewDetailDateHZ.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvDateSN,
            this.olvDateCity,
            this.olvDateGrid,
            this.olvDateCellName,
            this.olvDateECI,
            this.olvDateIsAlarm,
            this.olvDateAlarmDate});
            this.ListViewDetailDateHZ.ContextMenuStrip = this.conOutPutExcel;
            this.ListViewDetailDateHZ.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewDetailDateHZ.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewDetailDateHZ.FullRowSelect = true;
            this.ListViewDetailDateHZ.GridLines = true;
            this.ListViewDetailDateHZ.HeaderWordWrap = true;
            this.ListViewDetailDateHZ.Location = new System.Drawing.Point(0, 0);
            this.ListViewDetailDateHZ.Name = "ListViewDetailDateHZ";
            this.ListViewDetailDateHZ.ShowGroups = false;
            this.ListViewDetailDateHZ.Size = new System.Drawing.Size(980, 367);
            this.ListViewDetailDateHZ.TabIndex = 8;
            this.ListViewDetailDateHZ.UseCompatibleStateImageBehavior = false;
            this.ListViewDetailDateHZ.View = System.Windows.Forms.View.Details;
            // 
            // olvDateSN
            // 
            this.olvDateSN.HeaderFont = null;
            this.olvDateSN.Text = "序号";
            this.olvDateSN.Width = 55;
            // 
            // olvDateCity
            // 
            this.olvDateCity.HeaderFont = null;
            this.olvDateCity.Text = "地市名称";
            this.olvDateCity.Width = 80;
            // 
            // olvDateGrid
            // 
            this.olvDateGrid.HeaderFont = null;
            this.olvDateGrid.Text = "网格";
            this.olvDateGrid.Width = 64;
            // 
            // olvDateCellName
            // 
            this.olvDateCellName.HeaderFont = null;
            this.olvDateCellName.Text = "小区名称";
            this.olvDateCellName.Width = 129;
            // 
            // olvDateECI
            // 
            this.olvDateECI.HeaderFont = null;
            this.olvDateECI.Text = "ECI";
            this.olvDateECI.Width = 99;
            // 
            // olvDateIsAlarm
            // 
            this.olvDateIsAlarm.HeaderFont = null;
            this.olvDateIsAlarm.Text = "是否因退服而影响测试";
            this.olvDateIsAlarm.Width = 71;
            // 
            // olvDateAlarmDate
            // 
            this.olvDateAlarmDate.HeaderFont = null;
            this.olvDateAlarmDate.Text = "是否在亿阳不可用小区表";
            this.olvDateAlarmDate.Width = 79;
            // 
            // xtraTabHZ
            // 
            this.xtraTabHZ.Controls.Add(this.objectListView);
            this.xtraTabHZ.Name = "xtraTabHZ";
            this.xtraTabHZ.Size = new System.Drawing.Size(980, 367);
            this.xtraTabHZ.Text = "小区占用集合";
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnIndex);
            this.objectListView.AllColumns.Add(this.olvColumnCityName);
            this.objectListView.AllColumns.Add(this.olvColumnGrid);
            this.objectListView.AllColumns.Add(this.olvColumnDataSource);
            this.objectListView.AllColumns.Add(this.olvColumnTakeUp);
            this.objectListView.AllColumns.Add(this.olvColumnCellName);
            this.objectListView.AllColumns.Add(this.olvColumnECI);
            this.objectListView.AllColumns.Add(this.olvColumnDistance);
            this.objectListView.AllColumns.Add(this.olvColumnTestDate);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnIndex,
            this.olvColumnCityName,
            this.olvColumnGrid,
            this.olvColumnDataSource,
            this.olvColumnTakeUp,
            this.olvColumnCellName,
            this.olvColumnECI,
            this.olvColumnDistance,
            this.olvColumnTestDate});
            this.objectListView.ContextMenuStrip = this.conOutPutExcel;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.HeaderWordWrap = true;
            this.objectListView.Location = new System.Drawing.Point(0, 0);
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(980, 367);
            this.objectListView.TabIndex = 6;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnIndex
            // 
            this.olvColumnIndex.HeaderFont = null;
            this.olvColumnIndex.Text = "序号";
            this.olvColumnIndex.Width = 40;
            // 
            // olvColumnCityName
            // 
            this.olvColumnCityName.HeaderFont = null;
            this.olvColumnCityName.Text = "地市名称";
            this.olvColumnCityName.Width = 80;
            // 
            // olvColumnGrid
            // 
            this.olvColumnGrid.HeaderFont = null;
            this.olvColumnGrid.Text = "网格";
            this.olvColumnGrid.Width = 45;
            // 
            // olvColumnDataSource
            // 
            this.olvColumnDataSource.HeaderFont = null;
            this.olvColumnDataSource.Text = "数据源";
            // 
            // olvColumnTakeUp
            // 
            this.olvColumnTakeUp.HeaderFont = null;
            this.olvColumnTakeUp.Text = "占用情况";
            this.olvColumnTakeUp.Width = 99;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名";
            this.olvColumnCellName.Width = 158;
            // 
            // olvColumnECI
            // 
            this.olvColumnECI.HeaderFont = null;
            this.olvColumnECI.Text = "ECI";
            this.olvColumnECI.Width = 114;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "测试里程(米)";
            this.olvColumnDistance.Width = 118;
            // 
            // olvColumnTestDate
            // 
            this.olvColumnTestDate.HeaderFont = null;
            this.olvColumnTestDate.Text = "测试日期";
            this.olvColumnTestDate.Width = 169;
            // 
            // ZTGDGridCompareStatForm
            // 
            this.ClientSize = new System.Drawing.Size(987, 397);
            this.Controls.Add(this.xtraTabData);
            this.Name = "ZTGDGridCompareStatForm";
            this.Text = "对比测试统计结果列表";
            this.conOutPutExcel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabData)).EndInit();
            this.xtraTabData.ResumeLayout(false);
            this.xtraTabXQ.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewDetail)).EndInit();
            this.xtraTabDateHZ.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewDetailDateHZ)).EndInit();
            this.xtraTabHZ.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.ResumeLayout(false);

        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            exportToExcel();
        }

        private void outPutShap_Click(object sender, EventArgs e)
        {
            outPutShapRegion(false, this.gridShapStatDic);
        }

        private void outPutTestShap_Click(object sender, EventArgs e)
        {
            outPutShapRegion(true, this.gridTestShapStatDic);
        }

        private void outPutShapRegion(bool isTest, Dictionary<string, MapWinGIS.Shape> gridShapStatDicOutPut)
        {
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = FilterHelper.Shp;
            if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }
            string fileName = sfd.FileName;
            if (!ShapeHelper.DeleteShpFile(fileName))
            {
                MessageBox.Show("文件：" + fileName + " 已被其他程序占用。");
                return;
            }
            try
            {
                Shapefile shpFile = new Shapefile();
                bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    MessageBox.Show("打开创建SHAP失败!!!");
                }
                int idIdx = 0;
                int fiEci = -1;
                int fiType = -1;
                if (!isTest)
                {
                    fiType = idIdx;
                    ShapeHelper.InsertNewField(shpFile, "Name", FieldType.STRING_FIELD, 10, 30, ref fiType);
                }
                else
                {
                    fiEci = idIdx++;
                    fiType = idIdx;
                    ShapeHelper.InsertNewField(shpFile, "Eci", FieldType.INTEGER_FIELD, 10, 30, ref fiEci);
                    ShapeHelper.InsertNewField(shpFile, "Name", FieldType.STRING_FIELD, 10, 30, ref fiType);
                }

                int numShp = 0;
                foreach (string strType in gridShapStatDicOutPut.Keys)
                {
                    numShp++;
                    shpFile.EditInsertShape(gridShapStatDicOutPut[strType], ref numShp);
                    if (!isTest)
                    {
                        shpFile.EditCellValue(fiType, numShp, strType);
                    }
                    else
                    {
                        shpFile.EditCellValue(fiEci, numShp, Convert.ToInt32(strType.Split('^')[0]));
                        shpFile.EditCellValue(fiType, numShp, strType.Split('^')[1]);
                    }
                }
                if (!shpFile.SaveAs(fileName, null))
                {
                    shpFile.Close();
                    MessageBox.Show("保存SHAP失败!!!");
                    return;
                }
                shpFile.Close();
                MessageBox.Show("导出SHAP成功!!!");
            }
            catch
            {
                MessageBox.Show("导出SHAP失败!!!");
            }
        }

    }
}