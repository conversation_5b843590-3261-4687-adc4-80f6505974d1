﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using DevExpress.XtraCharts;

using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraTab;

namespace MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna
{
    public partial class XtraNewLastWeakPoadForm : DevExpress.XtraEditors.XtraForm
    {
        public XtraNewLastWeakPoadForm(MainModel mainmodel)
        {
            InitializeComponent();
            this.mainmodel = mainmodel;
        }
        MainModel mainmodel;
        public void setGSMData(List<LastWeakStatis> list,List<LastWeakProportion> list2)
        {
            this.Text = "GSM月度差道路概况";
            gridControl1.DataSource = list;
            gridControl2.DataSource = list2;
        }


        public void setTDData(List<LastWeakStatis> list, List<LastWeakProportion> list2)
        {
            this.Text = "TD月度差道路概况";
            gridControl1.DataSource = list;
            gridControl2.DataSource = list2;
        }

     

        //隐藏至路网通右下角
        private void XtraLastWeakPoadForm_Deactivate(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Minimized)
            {
                this.Visible = false;
                mainmodel.AddQuickWindowItem(this.GetType().Name, this.Text, "images\\cellquery.gif");
            }
        }

     
    }
}