﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public partial class DIYCellSetBriefDataCompareTimeForm : MinCloseForm
    {
        public DIYCellSetBriefDataCompareTimeForm()
        {
            InitializeComponent();
            dTimeOldFrom.Value = dTimeOldTo.Value = DateTime.Now.AddDays(-1);
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (dTimeNowFrom.Value.Date > dTimeNowTo.Value.Date || dTimeNowFrom.Value.Date < dTimeOldTo.Value.Date
                || dTimeOldFrom.Value.Date > dTimeOldTo.Value.Date)
            {
                MessageBox.Show("时间设置有误，请检查！");
                return;
            }
            if (!radBtnMaiCell.Checked && !radBtnNearCell.Checked)
            {
                MessageBox.Show("请选择查询的小区类型，至少选择其一！");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
        public void getSelect(ref DateTime compareNowFrom, ref DateTime compareNowTo,ref DateTime compareOldFrom, 
            ref DateTime compareOldTo ,ref bool isMainCell, ref bool isNearCell)
        {
            compareNowFrom = dTimeNowFrom.Value.Date;
            compareNowTo = dTimeNowTo.Value.Date.AddDays(1).AddMilliseconds(-1);
            compareOldFrom = dTimeOldFrom.Value.Date;
            compareOldTo = dTimeOldTo.Value.Date.AddDays(1).AddMilliseconds(-1);

            isMainCell = radBtnMaiCell.Checked;
            isNearCell = radBtnNearCell.Checked;
        }

    }
}
