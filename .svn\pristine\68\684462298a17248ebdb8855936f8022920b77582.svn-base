﻿namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    partial class CoverPnl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.repeatCoverPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.RepeatCoverPnl();
            this.overCoverPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.OverCoverPnl();
            this.wrongCoverPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.WrongCoverPnl();
            this.unstabitilyCoverPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.UnstabitilyCoverPnl();
            this.messCoverPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.MessCoverPnl();
            this.weakCoverPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.WeakCoverPnl();
            this.indoorLeakCoverPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.IndoorLeakCoverPnl();
            this.SuspendLayout();
            // 
            // repeatCoverPnl1
            // 
            this.repeatCoverPnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.repeatCoverPnl1.Location = new System.Drawing.Point(0, 771);
            this.repeatCoverPnl1.Name = "repeatCoverPnl1";
            this.repeatCoverPnl1.Size = new System.Drawing.Size(400, 91);
            this.repeatCoverPnl1.TabIndex = 13;
            // 
            // overCoverPnl1
            // 
            this.overCoverPnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.overCoverPnl1.Location = new System.Drawing.Point(406, 3);
            this.overCoverPnl1.Name = "overCoverPnl1";
            this.overCoverPnl1.Size = new System.Drawing.Size(397, 372);
            this.overCoverPnl1.TabIndex = 7;
            // 
            // wrongCoverPnl1
            // 
            this.wrongCoverPnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.wrongCoverPnl1.Location = new System.Drawing.Point(406, 384);
            this.wrongCoverPnl1.Name = "wrongCoverPnl1";
            this.wrongCoverPnl1.Size = new System.Drawing.Size(397, 59);
            this.wrongCoverPnl1.TabIndex = 10;
            // 
            // unstabitilyCoverPnl1
            // 
            this.unstabitilyCoverPnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.unstabitilyCoverPnl1.Location = new System.Drawing.Point(3, 381);
            this.unstabitilyCoverPnl1.Name = "unstabitilyCoverPnl1";
            this.unstabitilyCoverPnl1.Size = new System.Drawing.Size(397, 62);
            this.unstabitilyCoverPnl1.TabIndex = 9;
            // 
            // messCoverPnl1
            // 
            this.messCoverPnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.messCoverPnl1.Location = new System.Drawing.Point(406, 449);
            this.messCoverPnl1.Name = "messCoverPnl1";
            this.messCoverPnl1.Size = new System.Drawing.Size(397, 317);
            this.messCoverPnl1.TabIndex = 12;
            // 
            // weakCoverPnl1
            // 
            this.weakCoverPnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.weakCoverPnl1.Location = new System.Drawing.Point(3, 3);
            this.weakCoverPnl1.Name = "weakCoverPnl1";
            this.weakCoverPnl1.Size = new System.Drawing.Size(397, 372);
            this.weakCoverPnl1.TabIndex = 8;
            // 
            // indoorLeakCoverPnl1
            // 
            this.indoorLeakCoverPnl1.Location = new System.Drawing.Point(0, 449);
            this.indoorLeakCoverPnl1.Name = "indoorLeakCoverPnl1";
            this.indoorLeakCoverPnl1.Size = new System.Drawing.Size(400, 317);
            this.indoorLeakCoverPnl1.TabIndex = 11;
            // 
            // CoverPnl
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.AutoScroll = true;
            this.Controls.Add(this.repeatCoverPnl1);
            this.Controls.Add(this.overCoverPnl1);
            this.Controls.Add(this.wrongCoverPnl1);
            this.Controls.Add(this.unstabitilyCoverPnl1);
            this.Controls.Add(this.messCoverPnl1);
            this.Controls.Add(this.weakCoverPnl1);
            this.Controls.Add(this.indoorLeakCoverPnl1);
            this.Name = "CoverPnl";
            this.Size = new System.Drawing.Size(662, 338);
            this.ResumeLayout(false);

        }

        #endregion

        private RepeatCoverPnl repeatCoverPnl1;
        private OverCoverPnl overCoverPnl1;
        private WrongCoverPnl wrongCoverPnl1;
        private UnstabitilyCoverPnl unstabitilyCoverPnl1;
        private MessCoverPnl messCoverPnl1;
        private WeakCoverPnl weakCoverPnl1;
        private IndoorLeakCoverPnl indoorLeakCoverPnl1;

    }
}
