﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class ReasonPnlMultiCover : ReasonPanelBase
    {
        public ReasonPnlMultiCover()
        {
            InitializeComponent();
        }

        public override void AttachReason(ReasonBase reason)
        {
            base.AttachReason(reason);
            numDiffMax.ValueChanged -= numDiffMax_ValueChanged;
            numMinNum.ValueChanged -= numMinNum_ValueChanged;
            numRSRPMin.ValueChanged -= numRSRPMin_ValueChanged;
            numDiffMax.Value = (decimal)((ReasonMultiCover)reason).RSRPDiffMax;
            numMinNum.Value = (decimal)((ReasonMultiCover)reason).MultiNumMin;
            numRSRPMin.Value = (decimal)((ReasonMultiCover)reason).RSRSMin;
            numDiffMax.ValueChanged += numDiffMax_ValueChanged;
            numMinNum.ValueChanged += numMinNum_ValueChanged;
            numRSRPMin.ValueChanged += numRSRPMin_ValueChanged;
        }

        void numRSRPMin_ValueChanged(object sender, EventArgs e)
        {
            ((ReasonMultiCover)reason).RSRSMin = (float)numRSRPMin.Value;
        }

        void numMinNum_ValueChanged(object sender, EventArgs e)
        {
            ((ReasonMultiCover)reason).MultiNumMin = (int)numMinNum.Value;
        }

        void numDiffMax_ValueChanged(object sender, EventArgs e)
        {
            ((ReasonMultiCover)reason).RSRPDiffMax = (float)numDiffMax.Value;
        }
    }
}
