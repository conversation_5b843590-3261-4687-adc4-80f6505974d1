﻿using System;
using System.Collections.Generic;
using System.Text;
using DevExpress.XtraGrid.Views.Grid;
using Microsoft.Office.Interop.Excel;
using DevExpress.XtraTab;
using DevExpress.XtraGrid;
using MasterCom.Util;
using System.Windows.Forms;
using System.Data;
using DevExpress.XtraGrid.Columns;
using System.Collections;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace MasterCom.Util
{
    #region 导出excel帮助类
    /// <summary>
    /// 路网通专用ExcelHelper   Developer控件专用     by小雷
    /// </summary>
    public static class ExcelHelper
    {
        /// <summary>
        /// 通用GridView转DataTable    (适用于对象绑定数据源)
        /// </summary>
        /// <param name="view">GridView</param>
        /// <returns>DataTable</returns>
        public static System.Data.DataTable GridViewToDataTable(GridView view)
        {

            System.Data.DataTable dt = new System.Data.DataTable();
            //定义列类型
            for (int i = 0; i < view.Columns.Count; i++)
            {
                dt.Columns.Add(new DataColumn("col" + i, typeof(string)));
            }

            //定义列名
            DataRow dr = dt.NewRow();
            for (int i = 0; i < view.Columns.Count; i++)
            {
                dr[i] = view.Columns[i].Caption.ToString();
            }
            dt.Rows.Add(dr);

            //填充数据
            for (int i = 0; i < view.RowCount; i++)
            {
                DataRow datarow = dt.NewRow();
                foreach (GridColumn column in view.Columns)
                {
                    datarow[i] = view.GetRowCellValue(i, column);
                }
                dt.Rows.Add(datarow);
            }
            return dt;
        }

        /// <summary>
        /// XtraTabControl 转 EXCEL (选项卡对应sheet)   
        /// </summary>
        /// <param name="xtratabcontrol">XtraTabControl</param>
        public static void XtraTabControlToExcel(XtraTabControl xtratabcontrol)
        {
            XtraTabControlToExcel(xtratabcontrol, null);
        }
        
        /// <summary>
        /// XtraTabControl 转 EXCEL (选项卡对应sheet)   
        /// </summary>
        /// <param name="xtratabcontrol"></param>
        /// <param name="typeName">需要导出excel列头时 选择Columns的什么属性  例如: Caption,CustomizationCaption等</param>
        public static void XtraTabControlToExcel(XtraTabControl xtratabcontrol,string typeName)
        {
            Microsoft.Office.Interop.Excel.Application app;
            _Workbook workbook;
            Sheets sheets;
            _Worksheet worksheet;
            initExcel(out app, out workbook, out sheets, out worksheet);

            try
            {
                bool sheettmp = false;   //标识是否为第一个sheet,以便新建

                foreach (XtraTabPage page in xtratabcontrol.TabPages)
                {
                    foreach (Control ctrl in page.Controls)
                    {
                        addGroupControlData(typeName, sheets, ref worksheet, ref sheettmp, page, ctrl);
                    }
                }

                app.Visible = true;  //显示EXCEL
                app.UserControl = true;
            }
            catch (Exception)
            {
                throw (new Exception("导出错误!"));
            }
        }

        private static void addGroupControlData(string typeName, Sheets sheets, ref _Worksheet worksheet, ref bool sheettmp, XtraTabPage page, Control ctrl)
        {
            if (ctrl is GridControl)
            {
                foreach (GridView gridview in (ctrl as GridControl).ViewCollection)
                {
                    int rowCount = gridview.RowCount;       //DataTable行数
                    int colCount = gridview.Columns.Count;  //DataTable列数

                    if (sheettmp)
                    {
                        if (worksheet.Name == page.Text.ToString()) //循环到其他控件 跳过
                            continue;

                        //新建一个sheet(在最后位置新建)
                        //worksheet = (_Worksheet)workbook.Worksheets.Add(Type.Missing, (_Worksheet)workbook.Sheets.get_Item(sheets.Count), Type.Missing, Type.Missing);重构提示去除无用赋值,不确定是否有影响,留作备查
                        //切换到新sheet
                        worksheet = (_Worksheet)sheets.get_Item(sheets.Count);
                    }

                    object[,] arr = setArrayData(typeName, gridview, rowCount, colCount);

                    Range range = (Range)worksheet.Cells[1, 1];
                    range = range.get_Resize(rowCount + 1, colCount + 1);
                    range.Value2 = arr;
                    worksheet.Name = page.Text.ToString();  //获取TabControl名称 用作sheet名
                    for (int i = 1; i <= colCount; i++)
                    {
                        ((Range)worksheet.Cells[1, i]).EntireColumn.AutoFit();
                    }

                    sheettmp = true;// 至少完成了一个sheet,下一个将新建
                }
            }
        }

        private static object[,] setArrayData(string typeName, GridView gridview, int rowCount, int colCount)
        {
            //利用二维数组批量写入
            object[,] arr = new object[rowCount + 1, colCount + 1];
            for (int i = 0; i < colCount; i++)
            {
                if (typeName != null)
                {
                    if (typeName == "CustomizationCaption")
                    {
                        arr[0, i] = gridview.Columns[i].CustomizationCaption.ToString();
                    }
                    else if (typeName == "FieldName")
                    {
                        arr[0, i] = gridview.Columns[i].FieldName.ToString();
                    }
                    else
                    {
                        arr[0, i] = gridview.Columns[i].Caption.ToString();
                    }
                }
                else
                {
                    arr[0, i] = gridview.Columns[i].Caption.ToString();
                }
            }

            for (int j = 0; j < rowCount; j++)
            {
                for (int k = 0; k < colCount; k++)
                {
                    arr[j + 1, k] = gridview.GetRowCellValue(j, gridview.Columns[k]).ToString();// == "" ? "null" : dt.Rows[j][k];  //判断数据为空 填入字符串 "null"
                }
            }

            return arr;
        }







        private static void initExcel(out Microsoft.Office.Interop.Excel.Application app, out _Workbook workbook, out Sheets sheets, out _Worksheet worksheet)
        {
            app = new Microsoft.Office.Interop.Excel.Application();
            app.Visible = false;    //excel不可见
            app.UserControl = false;
            Workbooks workbooks = app.Workbooks;
            workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
            sheets = workbook.Worksheets;
            worksheet = (_Worksheet)sheets.get_Item(1);
            if (worksheet == null)
            {
                throw (new Exception("错误:sheet为空!"));
            }
            //删除多余sheet
            for (int i = 1; i < sheets.Count; i++)
            {
                ((_Worksheet)sheets.get_Item(2)).Delete();
            }
        }

        #region 杀死当前Excel进程
        [DllImport("User32.dll")]
        static extern int GetWindowThreadProcessId(IntPtr hwnd, out int id);
        public static void KillCurrentExcel(Microsoft.Office.Interop.Excel.Application excel)
        {
            try
            {
                IntPtr t = new IntPtr(excel.Hwnd);//得到这个句柄，具体作用是得到这块内存入口 

                int excelId = 0;
                GetWindowThreadProcessId(t, out excelId);//得到当前excel进程ID
                Process.GetProcessById(excelId).Kill();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + ex.StackTrace + ex.Source);
            }
        }
        #endregion

        /// <summary>
        /// 获取Excel第几列对应的列头字母 
        /// 例 : 1-A, 8-H, 16-P, 27-AA, 32-AF
        /// </summary>
        /// <param name="idx"></param>
        /// <returns></returns>
        public static string GetColNameByIndex(int idx)
        {
            if (idx == 0)
            {
                return "";
            }

            return getColNameByIndex(idx, new string[27]);
        }

        private static string getColNameByIndex(int idx, string[] strs)
        {
            string res = "";
            int tmp = idx % 26;
            int remainder = tmp == 0 ? 26 : tmp;
            int divisor = (idx - remainder) / 26;

            if (divisor >= 1)
            {
                res += getColNameByIndex(divisor, strs);
            }

            if (!string.IsNullOrEmpty(strs[remainder]))
            {
                res += strs[remainder];
            }
            else
            {
                char c = (char)(64 + remainder);
                res += c.ToString();
                strs[remainder] = c.ToString();
            }

            return res;
        }



        public delegate void DealDataDel<in T>(T content, DataRow dr);

        public static T ReadExcel<T>(string fileName, DealDataDel<T> func)
             where T : new()
        {
            //WaitBox.ProgressPercent = 10;
            T content = new T();
            DataSet ds = ExcelNPOIManager.ImportFromExcel(fileName);
            if (ds == null || ds.Tables == null || ds.Tables.Count == 0)
            {
                MessageBox.Show("Excel中没有数据或读取失败！");
                return content;
            }

            try
            {
                foreach (System.Data.DataTable dt in ds.Tables)
                {
                    //WaitBox.Text = "正在读取Excel信息..." + dt.TableName;
                    //WaitBox.ProgressPercent += 30;
                    foreach (DataRow dr in dt.Rows)
                    {
                        func(content, dr);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
                return new T();
            }
            return content;
        }
    }
    #endregion
}
