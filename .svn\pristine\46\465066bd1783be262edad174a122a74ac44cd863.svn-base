﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment_NR
{
    public class CellCheckItem_NR : IComparable<CellCheckItem_NR>
    {
        public int SN
        {
            get;
            set;
        }
        public string CellName
        {
            get;
            set;
        }

        public string UltraType
        {
            get;
            set;
        }

        public int? SegProbPointNum
        {
            get;
            set;
        }

        public double? SegProbPer
        {
            get;
            set;
        }

        public int? SegPointNum
        {
            get;
            set;
        }

        public int? CellNum
        { get; set; }

        public int? HoTimes
        { get; set; }
        public double? ProbCellPer
        {
            get;
            set;
        }

        public float? RSRPMin
        {
            get;
            private set;
        }
        public float? RSRPMax
        {
            get;
            private set;
        }
        public float? RSRPAvg
        {
            get;
            private set;
        }

        public float? SINRMin { get; private set; }

        public float? SINRMax { get; private set; }

        public float? SINRAvg { get; private set; }

        public double? Distance
        {
            get;
            private set;
        }
        public string HoDesc
        {
            get;
            private set;
        }

        private void initInfo()
        {
            if (Data is RoadSegment_NR)
            {
                int num = 0;
                foreach (TestPoint tp in Data.TestPoints)
                {
                    NRCell cell = tp.GetMainCell_NR();
                    if (cell != null && cell.Name.Equals(CellName))
                    {
                        num++;
                    }
                }
                SegProbPointNum = num;
                SegPointNum = Data.TestPoints.Count;
                SegProbPer = Math.Round(100.0 * num / Data.TestPoints.Count, 2);
                RoadSegment_NR segment = Data as RoadSegment_NR;
                this.RSRPAvg = segment.RSRPAvg;
                this.RSRPMax = segment.RSRPMax;
                this.RSRPMin = segment.RSRPMin;
                this.SINRAvg = segment.SINRAvg;
                this.SINRMax = segment.SINRMax;
                this.SINRMin = segment.SINRMin;
                this.Distance = segment.Distance;
            }
            else if (Data is OverHandover_NR)
            {
                int num = getNum();
                CellNum = num;
                HoTimes = Data.Events.Count;
                ProbCellPer = Math.Round(100.0 * num / Data.Events.Count, 2);
                HoDesc = (Data as OverHandover_NR).HoDesc;
            }
            else
            {
                this.RSRPAvg = Data.RSRPAvg;
                this.RSRPMax = Data.RSRPMax;
                this.RSRPMin = Data.RSRPMin;
                this.SINRAvg = Data.SINRAvg;
                this.SINRMax = Data.SINRMax;
                this.SINRMin = Data.SINRMin;
            }
        }

        private int getNum()
        {
            int num = 0;
            foreach (Event evt in Data.Events)
            {
                ICell cell = evt.GetTargetCell();
                if (cell != null && cell.Name.Equals(CellName))
                {
                    num++;
                    continue;
                }
                cell = evt.GetSrcCell();
                if (cell != null && cell.Name.Equals(CellName))
                {
                    num++;
                }
            }

            return num;
        }

        public string Prob
        {
            get
            {
                if (Data == null)
                {
                    return "-";
                }
                return Data.ToString();
            }
        }
        public string RoadDesc
        {
            get
            {
                string desc = string.Empty;
                if (probData != null)
                {
                    desc = probData.RoadDesc;
                }
                return desc;
            }
        }
        private IProblemData_NR probData = null;
        public IProblemData_NR Data
        {
            get { return probData; }
            set
            {
                probData = value;
                initInfo();
            }
        }

        public double? MidLng
        {
            get
            {
                double? ret = null;
                if (probData != null)
                {
                    if (probData.TestPoints != null && probData.TestPoints.Count > 0)
                    {
                        ret = probData.TestPoints[probData.TestPoints.Count / 2].Longitude;
                    }
                    else if (probData.Events != null && probData.Events.Count > 0)
                    {
                        ret = probData.Events[probData.Events.Count / 2].Longitude;
                    }
                }
                return ret;
            }
        }

        public double? MidLat
        {
            get
            {
                double? ret = null;
                if (probData != null)
                {
                    if (probData.TestPoints != null && probData.TestPoints.Count > 0)
                    {
                        ret = probData.TestPoints[probData.TestPoints.Count / 2].Latitude;
                    }
                    else if (probData.Events != null && probData.Events.Count > 0)
                    {
                        ret = probData.Events[probData.Events.Count / 2].Latitude;
                    }
                }
                return ret;
            }
        }

        #region IComparable<CellCheckItem> 成员

        public int CompareTo(CellCheckItem_NR other)
        {
            if (other == null)
            {
                throw new ArgumentNullException("other", "对比值为空");
            }
            return this.CellName.CompareTo(other.CellName);
        }

        #endregion
    }

}
