﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GSMComparePoorRxQualityRoadSettingDlg : BaseDialog
    {
        public GSMComparePoorRxQualityRoadSettingDlg(GSMComparePoorRxQualityRoadCondition condition)
        {
            InitializeComponent();
            setUIValue(condition);
        }
        void setUIValue(GSMComparePoorRxQualityRoadCondition condition)
        {
            if (condition==null)
            {
                DateTime now = DateTime.Now.Date;
                beginTime1.Value = now.AddDays(-15);
                endTime1.Value = now.AddDays(-8);
                beginTime2.Value = now.AddDays(-7);
                endTime2.Value = now;
                this.numRxQualMax.Value = 5;
                this.numDistance.Value = 100;
                this.numMaxDistance.Value = 20;
                this.numRoadRadius.Value = 50;
            }
            else
            {
                beginTime1.Value = condition.Period1.BeginTime;
                endTime1.Value = condition.Period1.EndTime.Date.AddDays(-1);
                beginTime2.Value = condition.Period2.BeginTime;
                endTime2.Value = condition.Period2.EndTime.Date.AddDays(-1);
                this.numRxQualMax.Value = condition.RxQual;
                this.numDistance.Value = (decimal)condition.RoadDistance;
                this.numMaxDistance.Value = (decimal)condition.TestPointDistance;
                this.numRoadRadius.Value = (decimal)condition.RoadGridSpan;
            }
        }

        public GSMComparePoorRxQualityRoadCondition GetCondition()
        {
            GSMComparePoorRxQualityRoadCondition condition = new GSMComparePoorRxQualityRoadCondition();
            condition.Period1 = p1;
            condition.Period2 = p2;
            condition.RxQual = (short)this.numRxQualMax.Value;
            condition.RoadDistance = (double)this.numDistance.Value;
            condition.TestPointDistance = (double)this.numMaxDistance.Value;
            condition.RoadGridSpan = (double)this.numRoadRadius.Value;
            return condition;
        }

        private TimePeriod p1 = null;
        private TimePeriod p2 = null;
        private void btnOK_Click(object sender, EventArgs e)
        {
            p1 = new TimePeriod();
            if (!p1.SetPeriod(beginTime1.Value.Date, endTime1.Value.Date.AddDays(1).Date))
            {
                MessageBox.Show("时间段1，开始时间不能大于结束时间！请重新设置条件！");
                return;
            }
            p2 = new TimePeriod();
            if (!p2.SetPeriod(beginTime2.Value.Date, endTime2.Value.Date.AddDays(1).Date))
            {
                MessageBox.Show("时间段2，开始时间不能大于结束时间！请重新设置条件！");
                return;
            }
            bool notIntersect = p1.BeginTime >= p2.EndTime || p1.EndTime <= p2.BeginTime;
            if (!notIntersect)
            {
                MessageBox.Show("时间段2与时间段1有交叉！请重新设置条件！");
                return;
            }
            DialogResult = DialogResult.OK;
        }


    }
}
