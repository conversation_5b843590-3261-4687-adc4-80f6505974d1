﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Stat.Data;
using DBDataViewer;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SetQueryFormRxlevComp : BaseDialog
    {
        private ItemSelectionPanel projPanelDT;
        private ItemSelectionPanel projPanelScan;
        private ItemSelectionPanel servPanelDT;
        private ItemSelectionPanel servPanelScan;
        private QueryCondition CurCondition;
        private MapFormItemSelection ItemSelection;
        public bool isFirstTimeFireShowForm { get; set; } = true;
        private DbRect bounds;
        private double GRID_SPAN_LAT;
        private double GRID_SPAN_LONG;
        Dictionary<TestPoint, tpGridposition> tpGridDicDT = null;
        Dictionary<TestPoint, tpGridposition> tpGridDicScan = null;
        List<markGrid> markGirds = null; 
        List<TestPoint> tpInconnectionDTList = null;
        List<TestPoint> tpInconnectionScanList = null;

        /// <summary>
        /// 路测栅格的最强信号字典
        /// </summary>
        Dictionary<string, ColorUnitContainRxlev> cuContainRxlevDTDic = null;
        /// <summary>
        /// 扫频栅格的最强信号字典
        /// </summary>
        Dictionary<string, ColorUnitContainRxlev> cuContainRxlevScanDic = null;
        /// <summary>
        /// 在同一栅格中，扫频最强信号与路测最强信号的差值字典
        /// </summary>
        Dictionary<string, int> cuContainRxlevDiscrepancy = null;
        /// <summary>
        /// 有信号的行列集合
        /// </summary>
        List<string> rowcolList = null;

        public SetQueryFormRxlevComp(MainModel mModel, MapFormItemSelection itemSelection, QueryCondition condition)
        {
            InitializeComponent();

            mainModel = mModel;
            CurCondition = condition;
            ItemSelection = itemSelection;

            dateTimePickerBeginTimeDT.Value = DateTime.Now.AddDays(-1);
            dateTimePickerBeginTimeScan.Value = DateTime.Now.AddDays(-1);
            dateTimePickerEndTimeDT.Value = DateTime.Now;
            dateTimePickerEndTimeScan.Value = DateTime.Now;

            listViewProjectDT.Items.Clear();
            listViewProjectScan.Items.Clear();
            if (mainModel.CategoryManager["Project"] != null)
            {
                projPanelDT = new ItemSelectionPanel(toolStripDropDownProjectDT, listViewProjectDT, lbProjCountDT, itemSelection, "Project", true);
                projPanelScan = new ItemSelectionPanel(toolStripDropDownProjectScan, listViewProjectScan, lbProjCountScan, itemSelection, "Project", true);
                toolStripDropDownProjectDT.Items.Clear();
                toolStripDropDownProjectScan.Items.Clear();
                projPanelDT.FreshItems();
                projPanelScan.FreshItems();
                toolStripDropDownProjectDT.Items.Add(new ToolStripControlHost(projPanelDT));
                toolStripDropDownProjectScan.Items.Add(new ToolStripControlHost(projPanelScan));
            }

            listViewServiceDT.Items.Clear();
            listViewServiceScan.Items.Clear();
            if (mainModel.CategoryManager["ServiceType"] != null)
            {
                servPanelDT = new ItemSelectionPanel(toolStripDropDownServiceDT, listViewServiceDT, lbSvCountDT, itemSelection, "ServiceType", true);
                servPanelScan = new ItemSelectionPanel(toolStripDropDownServiceScan, listViewServiceScan, lbSvCountScan, itemSelection, "ServiceType", true);
                toolStripDropDownServiceDT.Items.Clear();
                toolStripDropDownServiceScan.Items.Clear();
                servPanelDT.FreshItems();
                servPanelScan.FreshItems();
                toolStripDropDownServiceDT.Items.Add(new ToolStripControlHost(servPanelDT));
                toolStripDropDownServiceScan.Items.Add(new ToolStripControlHost(servPanelScan));
            }

            setDefaultService();

            if (mainModel.ConditionRecorderRxlevComp != null && mainModel.ConditionRecorderRxlevComp.kind == conditionRecorder.Kind.RxlevComp)
            {
                conditionRecorder recorder = mainModel.ConditionRecorderRxlevComp;
                this.dateTimePickerBeginTimeDT.Value = recorder.dateTimePickerBeginTimeDTValue;
                this.dateTimePickerBeginTimeScan.Value = recorder.dateTimePickerBeginTimeScanValue;
                this.dateTimePickerEndTimeDT.Value = recorder.dateTimePickerEndTimeDTValue;
                this.dateTimePickerEndTimeScan.Value = recorder.dateTimePickerEndTimeScanValue;
                this.numUdMakeupRxlev.Value = recorder.numUdMakeupRxlevValue;
                this.listViewProjectDT.Items.Clear();
                this.listViewProjectScan.Items.Clear();
                this.listViewServiceDT.Items.Clear();
                this.listViewServiceScan.Items.Clear();
                foreach (saveItem item in recorder.listViewProjectDTItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewProjectDT.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewProjectScanItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewProjectScan.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewServiceDTItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewServiceDT.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewServiceScanItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewServiceScan.Items.Add(lvi);
                }
                this.trackBarChannel.Value = recorder.trackBarChannelValue;
                this.trackBarChannel_Scroll(null, null);
            }
        }

        private void setDefaultService()
        {
            ListViewItem lviDt = new ListViewItem();
            lviDt.Text = "GSM语音业务";
            lviDt.Tag = 1;
            this.listViewServiceDT.Items.Add(lviDt);

            ListViewItem lviScan = new ListViewItem();
            lviScan.Text = "扫频业务";
            lviScan.Tag = 12;
            this.listViewServiceScan.Items.Add(lviScan);
        }

        private void buttonProjDT_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjDT.Width, buttonProjDT.Height);
            toolStripDropDownProjectDT.Show(buttonProjDT, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonProjScan_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjScan.Width, buttonProjScan.Height);
            toolStripDropDownProjectScan.Show(buttonProjScan, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonServDT_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonServDT.Width, buttonServDT.Height);
            toolStripDropDownServiceDT.Show(buttonServDT, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonServScan_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonServScan.Width, buttonServScan.Height);
            toolStripDropDownServiceScan.Show(buttonServScan, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void trackBarChannel_Scroll(object sender, EventArgs e)
        {
            if (trackBarChannel.Value == 1)
            {
                label900.ForeColor = Color.Red;
                label1800.ForeColor = Color.Black;
                label900and1800.ForeColor = Color.Black;
            }
            else if (trackBarChannel.Value == 2)
            {
                label900.ForeColor = Color.Black;
                label1800.ForeColor = Color.Red;
                label900and1800.ForeColor = Color.Black;
            }
            else if (trackBarChannel.Value == 3)
            {
                label900.ForeColor = Color.Black;
                label1800.ForeColor = Color.Black;
                label900and1800.ForeColor = Color.Red;
            }
        }

        private bool isValidCondition()
        {
            if (dateTimePickerBeginTimeDT.Value > dateTimePickerEndTimeDT.Value)
            {
                MessageBox.Show("查询路测数据，结束时间必须大于开始时间。");
            }
            if (dateTimePickerBeginTimeScan.Value > dateTimePickerEndTimeScan.Value)
            {
                MessageBox.Show("查询扫频数据，结束时间必须大于开始时间。");
            }
            else if (listViewProjectDT.Items.Count <= 0)
            {
                MessageBox.Show("查询路测数据，至少需要选择一个项目。");
            }
            else if (listViewServiceDT.Items.Count <= 0)
            {
                MessageBox.Show("查询路测数据，至少需要选择一个业务。");
            }
            else if (listViewProjectScan.Items.Count <= 0)
            {
                MessageBox.Show("查询扫频数据，至少需要选择一个项目。");
            }
            else if (listViewServiceScan.Items.Count <= 0)
            {
                MessageBox.Show("查询扫频数据，至少需要选择一个业务。");
            }
            else
                return true;
            return false;
        }

        private void buttonQuery_Click(object sender, EventArgs e)
        {
            conditionRecorder recorder = new conditionRecorder();
            recorder.fill(conditionRecorder.Kind.RxlevComp, this.dateTimePickerBeginTimeDT.Value, this.dateTimePickerBeginTimeScan.Value, this.dateTimePickerEndTimeDT.Value
                , this.dateTimePickerEndTimeScan.Value, this.trackBarChannel.Value,(int)this.numUdMakeupRxlev.Value);
            recorder.fillCollection(this.listViewProjectDT.Items, this.listViewProjectScan.Items, this.listViewServiceDT.Items, this.listViewServiceScan.Items);
            mainModel.ConditionRecorderRxlevComp = recorder;

            mainModel.IsPrepareWithoutGridPartParam = true;
            mainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = null;

            tpGridDicDT = queryDTData();     
            if (tpGridDicDT == null)
                return;
            if (tpGridDicDT.Count == 0)
                return;
            tpGridDicScan = queryScanData();
            if (tpGridDicScan==null)
                return;
            if (tpGridDicScan.Count == 0)
                return;

            WaitBox.Show(compareTpGridDicDTToTpGridDicScan);//保留DT和Scan都在同一栅格内的采样点集合

            WaitBox.Show(queryStrongestRxlevGrid);  //查询栅格对应的最强信号差值

            MapGridLayer.NeedFreshFullImg = true;
            mainModel.FireGridCoverQueried(this);

            setLegendType();
            mainModel.RefreshLegend();

            mainModel.IsPrepareWithoutGridPartParam = false; //查询结束，回复原始的布尔值，不影响其他栅格绘图

            clearTempObjects();
        }

        public void compareTpGridDicDTToTpGridDicScan()
        {
            try
            {
                WaitBox.Text = "正在调整频段……";
                WaitBox.ProgressPercent = 30;

                markGirds = new List<markGrid>();
                tpInconnectionDTList = new List<TestPoint>();
                tpInconnectionScanList = new List<TestPoint>();

                List<TestPoint> curBandTestpointDT = new List<TestPoint>();
                List<TestPoint> curBandTestpointScan = new List<TestPoint>();
                addCurBandTestpointDT(curBandTestpointDT);
                addCurBandTestpointScan(curBandTestpointScan);

                WaitBox.ProgressPercent = 50;

                markGrid markGrid = null;
                bool needAdd = false;
                addTP(curBandTestpointDT, ref markGrid, ref needAdd);
                addScanTP(curBandTestpointScan, ref markGrid, ref needAdd);
                foreach (markGrid mg in markGirds)
                {
                    tpInconnectionDTList.AddRange(mg.testPointDtList);
                    tpInconnectionScanList.AddRange(mg.testPointScanList);
                }

                WaitBox.ProgressPercent = 90;
            }
            catch
            {
                //continue
            }
            finally
            {
                //---释放内存--------
                tpGridDicDT = null;
                tpGridDicScan = null;
                //--------------------

                WaitBox.Close();
            }
        }

        private void addCurBandTestpointDT(List<TestPoint> curBandTestpointDT)
        {
            foreach (TestPoint tpDT in tpGridDicDT.Keys)
            {
                if (this.trackBarChannel.Value == 1)   //选择900频段
                {
                    short? bcch = (short?)tpDT["BCCH"];
                    if (bcch != null && bcch < 512)
                    {
                        curBandTestpointDT.Add(tpDT);
                    }
                }
                else if (this.trackBarChannel.Value == 2)  //选择1800频段
                {
                    short? bcch = (short?)tpDT["BCCH"];
                    if (bcch != null && bcch >= 512)
                    {
                        curBandTestpointDT.Add(tpDT);
                    }
                }
                else  //选择900和1800频段
                {
                    curBandTestpointDT.Add(tpDT);
                }
            }
        }

        private void addCurBandTestpointScan(List<TestPoint> curBandTestpointScan)
        {
            foreach (TestPoint tpScan in tpGridDicScan.Keys)
            {
                if (this.trackBarChannel.Value == 1)   //选择900频段
                {
                    short? bcch = (short?)(int?)tpScan["GSCAN_BCCH", 0];
                    if (bcch != null && bcch < 512)
                    {
                        curBandTestpointScan.Add(tpScan);
                    }
                }
                else if (this.trackBarChannel.Value == 2)  //选择1800频段
                {
                    short? bcch = (short?)(int?)tpScan["GSCAN_BCCH", 0];
                    if (bcch != null && bcch >= 512)
                    {
                        curBandTestpointScan.Add(tpScan);
                    }
                }
                else  //选择900和1800频段
                {
                    curBandTestpointScan.Add(tpScan);
                }
            }
        }

        private void addTP(List<TestPoint> curBandTestpointDT, ref markGrid markGrid, ref bool needAdd)
        {
            foreach (TestPoint tpDT in curBandTestpointDT)
            {
                tpGridposition posDT = tpGridDicDT[tpDT];
                if (markGirds.Count == 0)
                {
                    markGrid = new markGrid(posDT.row, posDT.col);
                    markGrid.hasDtSample = true;
                    markGrid.testPointDtList.Add(tpDT);
                    markGirds.Add(markGrid);
                }
                else
                {
                    dealTPMarkGirds(ref markGrid, ref needAdd, tpDT, posDT);
                }
            }
        }

        private void dealTPMarkGirds(ref markGrid markGrid, ref bool needAdd, TestPoint tpDT, tpGridposition posDT)
        {
            int num = markGirds.Count;
            int count = 0;
            foreach (markGrid mg in markGirds)
            {
                if (mg.row == posDT.row && mg.col == posDT.col)
                {
                    mg.testPointDtList.Add(tpDT);  //往栅格加入该采样点
                    needAdd = false;
                }
                else
                {
                    count++;
                    if (count == num)
                    {
                        needAdd = true;
                        markGrid = new markGrid(posDT.row, posDT.col);
                        markGrid.testPointDtList.Add(tpDT);
                        markGrid.hasDtSample = true;
                    }
                }
            }
            if (needAdd)    //将含有路测采样点的栅格收集标记起来
                markGirds.Add(markGrid);
        }

        private void addScanTP(List<TestPoint> curBandTestpointScan, ref markGrid markGrid, ref bool needAdd)
        {
            foreach (TestPoint tpScan in curBandTestpointScan)
            {
                tpGridposition posScan = tpGridDicScan[tpScan];
                dealScanTPMarkGirds(ref markGrid, ref needAdd, tpScan, posScan);
            }
        }

        private void dealScanTPMarkGirds(ref markGrid markGrid, ref bool needAdd, TestPoint tpScan, tpGridposition posScan)
        {
            int num = markGirds.Count;
            int count = 0;
            foreach (markGrid mg in markGirds)
            {
                if (mg.row == posScan.row && mg.col == posScan.col)
                {
                    mg.testPointScanList.Add(tpScan);  //往栅格加入该采样点
                    mg.hasScanSample = true;
                    needAdd = false;
                }
                else
                {
                    count++;
                    if (count == num)
                    {
                        markGrid = new markGrid(posScan.row, posScan.col);
                        markGrid.hasScanSample = true;
                        markGrid.testPointScanList.Add(tpScan);
                        needAdd = true;
                    }
                }
            }
            if (needAdd)    //将含有扫频采样点的栅格收集标记起来
                markGirds.Add(markGrid);
        }

        public void queryStrongestRxlevGrid()
        {
            cuContainRxlevDTDic = new Dictionary<string, ColorUnitContainRxlev>();
            cuContainRxlevScanDic = new Dictionary<string, ColorUnitContainRxlev>();
            cuContainRxlevDiscrepancy = new Dictionary<string, int>();
            rowcolList = new List<string>();

            WaitBox.Text = "正在计算信号差值……";
            WaitBox.ProgressPercent = 30;

            try
            {
                dealTP();
                dealScanTP();
                WaitBox.ProgressPercent = 60;

                setColorUnit();

                WaitBox.ProgressPercent = 90;
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void dealTP()
        {
            foreach (TestPoint tpDt in tpInconnectionDTList)
            {
                int pGridUnitRow = (int)(Math.Round((bounds.y2 - tpDt.Latitude) / GRID_SPAN_LAT));
                int pGridUnitCol = (int)(Math.Round((tpDt.Longitude - bounds.x1) / GRID_SPAN_LONG));

                if (tpDt["RxLevSub"] != null)
                {
                    int rxlevSub = (int)(short)tpDt["RxLevSub"];
                    string rowcol = pGridUnitRow.ToString() + "_" + pGridUnitCol.ToString();
                    if (!cuContainRxlevDTDic.ContainsKey(rowcol))
                    {
                        ColorUnitContainRxlev cuContainRxlev = new ColorUnitContainRxlev(pGridUnitRow, pGridUnitCol, rxlevSub);
                        cuContainRxlevDTDic.Add(rowcol, cuContainRxlev);
                    }
                    else
                    {
                        if (rxlevSub > cuContainRxlevDTDic[rowcol].strongestRxlev)
                            cuContainRxlevDTDic[rowcol].strongestRxlev = rxlevSub;
                    }
                    if (!rowcolList.Contains(rowcol))
                        rowcolList.Add(rowcol);
                }
            }
        }

        private void dealScanTP()
        {
            foreach (TestPoint tpScan in tpInconnectionScanList)
            {
                int pGridUnitRow = (int)(Math.Round((bounds.y2 - tpScan.Latitude) / GRID_SPAN_LAT));
                int pGridUnitCol = (int)(Math.Round((tpScan.Longitude - bounds.x1) / GRID_SPAN_LONG));

                if (tpScan["GSCAN_RxLev", 0] != null)
                {
                    int rxLevSub = (int)(float?)tpScan["GSCAN_RxLev", 0];
                    string rowcol = pGridUnitRow.ToString() + "_" + pGridUnitCol.ToString();
                    if (!cuContainRxlevScanDic.ContainsKey(rowcol))
                    {
                        ColorUnitContainRxlev cuContainRxlev = new ColorUnitContainRxlev(pGridUnitRow, pGridUnitCol, rxLevSub);
                        cuContainRxlevScanDic.Add(rowcol, cuContainRxlev);
                    }
                    else
                    {
                        if (rxLevSub > cuContainRxlevScanDic[rowcol].strongestRxlev)
                            cuContainRxlevScanDic[rowcol].strongestRxlev = rxLevSub;
                    }
                    if (!rowcolList.Contains(rowcol))
                        rowcolList.Add(rowcol);
                }
            }
        }

        private void setColorUnit()
        {
            ColorUnit cu = null;
            foreach (string rowcol in rowcolList)
            {
                int i = rowcol.IndexOf("_");
                int row = int.Parse(rowcol.Substring(0, i));
                int col = int.Parse(rowcol.Substring(i + 1));

                if (mainModel.CurGridColorUnitMatrix[row, col] == null)
                {
                    cu = new ColorUnit();
                    mainModel.CurGridColorUnitMatrix[row, col] = cu;
                    cu.Status = 0;
                }
                else
                {
                    cu = mainModel.CurGridColorUnitMatrix[row, col];
                }

                if (cuContainRxlevDTDic.ContainsKey(rowcol) && cuContainRxlevScanDic.ContainsKey(rowcol))
                {
                    int rxlevDiscrepancy = cuContainRxlevDTDic[rowcol].strongestRxlev - cuContainRxlevScanDic[rowcol].strongestRxlev;
                    int rxlevDiscrepancyAfterMakeup = rxlevDiscrepancy + (int)this.numUdMakeupRxlev.Value;  //对路测数据进行补差，加差值到路测数据上

                    if (rxlevDiscrepancyAfterMakeup == 0)
                        cu.color = Color.Green;

                    setNegativeColor(cu, rxlevDiscrepancyAfterMakeup);

                    setPostiveColor(cu, rxlevDiscrepancyAfterMakeup);
                }
            }
        }

        private void setNegativeColor(ColorUnit cu, int rxlevDiscrepancyAfterMakeup)
        {
            if (-3 <= rxlevDiscrepancyAfterMakeup && rxlevDiscrepancyAfterMakeup < 0)
                cu.color = Color.FromArgb(255, 192, 192);
            else if (-6 <= rxlevDiscrepancyAfterMakeup && rxlevDiscrepancyAfterMakeup < -3)
                cu.color = Color.FromArgb(255, 128, 128);
            else if (-9 <= rxlevDiscrepancyAfterMakeup && rxlevDiscrepancyAfterMakeup < -6)
                cu.color = Color.Red;
            else if (-12 <= rxlevDiscrepancyAfterMakeup && rxlevDiscrepancyAfterMakeup < -9)
                cu.color = Color.FromArgb(192, 0, 0);
            else if (rxlevDiscrepancyAfterMakeup < -12)
                cu.color = Color.Maroon;
        }

        private void setPostiveColor(ColorUnit cu, int rxlevDiscrepancyAfterMakeup)
        {
            if (0 < rxlevDiscrepancyAfterMakeup && rxlevDiscrepancyAfterMakeup <= 3)
                cu.color = Color.FromArgb(192, 192, 255);
            else if (3 < rxlevDiscrepancyAfterMakeup && rxlevDiscrepancyAfterMakeup <= 6)
                cu.color = Color.FromArgb(128, 128, 255);
            else if (6 < rxlevDiscrepancyAfterMakeup && rxlevDiscrepancyAfterMakeup <= 9)
                cu.color = Color.Blue;
            else if (9 < rxlevDiscrepancyAfterMakeup && rxlevDiscrepancyAfterMakeup <= 12)
                cu.color = Color.FromArgb(0, 0, 192);
            else if (rxlevDiscrepancyAfterMakeup > 12)
                cu.color = Color.Black;
        }

        private Dictionary<TestPoint, tpGridposition> queryDTData()
        {
            if (isValidCondition())
            {
                DIYSampleToGridQueryByRegion sampleToGridQueryForDT = new DIYSampleToGridQueryByRegion(mainModel);
                CurCondition.Periods.Clear();
                CurCondition.Periods.Add(new TimePeriod(dateTimePickerBeginTimeDT.Value.Date, dateTimePickerEndTimeDT.Value.Date.AddDays(1).AddMilliseconds(-1)));
                CurCondition.Projects.Clear();
                CurCondition.ServiceTypes.Clear();
                foreach (ListViewItem item in listViewProjectDT.Items)
                {
                    CurCondition.Projects.Add((byte)(int)item.Tag);
                }
                foreach (ListViewItem item in listViewServiceDT.Items)
                {
                    CurCondition.ServiceTypes.Add((byte)(int)item.Tag);
                }
                CurCondition.DistrictIDs.Clear();
                CurCondition.DistrictIDs.Add(mainModel.DistrictID);
                sampleToGridQueryForDT.CurCondition = CurCondition;
                sampleToGridQueryForDT.Query();

                bounds = sampleToGridQueryForDT.bounds;
                GRID_SPAN_LAT = sampleToGridQueryForDT.GRID_SPAN_LAT;
                GRID_SPAN_LONG = sampleToGridQueryForDT.GRID_SPAN_LONG;

                return sampleToGridQueryForDT.tpGridDic;
            }
            return new Dictionary<TestPoint, tpGridposition>();
        }

        private Dictionary<TestPoint, tpGridposition> queryScanData()
        {
            DIYSampleToGridQueryByRegion sampleToGridQueryForScan = new DIYSampleToGridQueryByRegion(mainModel);
            CurCondition.Periods.Clear();
            CurCondition.Projects.Clear();
            CurCondition.ServiceTypes.Clear();
            CurCondition.Periods.Add(new TimePeriod(dateTimePickerBeginTimeScan.Value.Date, dateTimePickerEndTimeScan.Value.Date.AddDays(1).AddMilliseconds(-1)));
            foreach (ListViewItem item in listViewProjectScan.Items)
            {
                CurCondition.Projects.Add((byte)(int)item.Tag);
            }
            foreach (ListViewItem item in listViewServiceScan.Items)
            {
                CurCondition.ServiceTypes.Add((byte)(int)item.Tag);
            }
            CurCondition.DistrictIDs.Clear();
            CurCondition.DistrictIDs.Add(mainModel.DistrictID);
            sampleToGridQueryForScan.CurCondition = CurCondition;
            sampleToGridQueryForScan.Query();

            return sampleToGridQueryForScan.tpGridDic;
        }

        /// <summary>
        /// 清除之前用于计算的对象，减少其对内存的占用
        /// </summary>
        private void clearTempObjects()
        {
            this.markGirds.Clear();
            this.Dispose();
            //GC.Collect();
        }

        private void setLegendType()
        {
            GridColorFixed gridColorFixed = new GridColorFixed();
            gridColorFixed.items = new List<GridColorFixedItem>();
            gridColorFixed.theme = "路测最强电平与扫频最强电平差值栅格";
            GridColorFixedItem item = new GridColorFixedItem();
            item.desc = "差值 < -12";
            item.color = Color.Maroon;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-12 <= 差值 < -9";
            item.color = Color.FromArgb(192, 0, 0);
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-9 <= 差值 < -6";
            item.color = Color.Red;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-6 <= 差值 < -3";
            item.color = Color.FromArgb(255, 128, 128);
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-3 <= 差值 < 0";
            item.color = Color.FromArgb(255, 192, 192);
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "无差值";
            item.color = Color.Green;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "0 < 差值 <= 3";
            item.color = Color.FromArgb(192, 192, 255);
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "3 < 差值 <= 6";
            item.color = Color.FromArgb(128, 128, 255);
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "6 < 差值 <= 9";
            item.color = Color.Blue;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "9 < 差值 <= 12";
            item.color = Color.FromArgb(0, 0, 192);
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "12 < 差值";
            item.color = Color.Black;
            gridColorFixed.items.Add(item);

            mainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = gridColorFixed;
        }
    }

    public class ColorUnitContainRxlev : ColorUnit
    {
        public ColorUnitContainRxlev(int row, int col,int strongestRxlev)
        {
            this.row = row;
            this.col = col;
            this.strongestRxlev = strongestRxlev;
        }
        public int strongestRxlev { get; set; }
        public int row { get; set; }
        public int col { get; set; }
    }

}
