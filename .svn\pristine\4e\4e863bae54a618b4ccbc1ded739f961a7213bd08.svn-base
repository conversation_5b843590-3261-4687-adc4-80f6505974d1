﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using DevExpress.XtraEditors.Controls;
using MasterCom.Util;
using System.Xml;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class DIYStatByStreetsOfRegionSettingDlg : BaseDialog
    {
        public DIYStatByStreetsOfRegionSettingDlg()
            : base()
        {
            InitializeComponent();
        }

        private void edtMap_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Multiselect = false;
            openFileDialog.CheckFileExists = true;
            openFileDialog.DefaultExt = "SHP";
            openFileDialog.Filter = FilterHelper.Shp;
            if (openFileDialog.ShowDialog(this) == DialogResult.OK)
            {
                MapWinGIS.Shapefile layerFile = new MapWinGIS.Shapefile();
                try
                {
                    if (layerFile.Open(openFileDialog.FileName, null))
                    {
                        cbxColumn.Properties.Items.Clear();
                        int numFields = layerFile.NumFields;
                        for (int x = 0; x < numFields; x++)
                        {
                            MapWinGIS.Field field = layerFile.get_Field(x);
                            cbxColumn.Properties.Items.Add(field.Name);
                        }
                        cbxColumn.SelectedIndex = 0;
                    }
                    this.edtMap.Text = openFileDialog.FileName;
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.ToString());
                }
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            string fileName = edtMap.Text.Trim();
            if (checkMap(fileName))
            {
                foreach (object obj in listBoxMap.Items)
                {
                    StreetInjectTableInfo info = obj as StreetInjectTableInfo;
                    if (info != null && info.FilePath == fileName)
                    {
                        MessageBox.Show("已添加该图层！");
                        return;
                    }
                }
                StreetInjectTableInfo streetInfo = new StreetInjectTableInfo();
                streetInfo.FilePath = edtMap.Text.Trim();
                streetInfo.ColumnName = cbxColumn.Text;
                if (!listBoxMap.Items.Contains(streetInfo))
                {
                    listBoxMap.Items.Add(streetInfo);
                }
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (listBoxMap.SelectedIndex >= 0)
            {
                listBoxMap.Items.RemoveAt(listBoxMap.SelectedIndex);
            }
        }

        private bool checkMap(string filePath)
        {
            if (File.Exists(filePath))
            {
                return true;
            }
            return false;
        }

        public List<StreetInjectTableInfo> GetCondition()
        {
            List<StreetInjectTableInfo> refList = new List<StreetInjectTableInfo>();
            foreach (object obj in listBoxMap.Items)
            {
                if (obj is StreetInjectTableInfo)
                {
                    refList.Add(obj as StreetInjectTableInfo);
                }
            }
            return refList;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (listBoxMap.Items.Count == 0)
            {
                MessageBox.Show("请至少添加一个道路图层！");
                return;
            }
            DialogResult = DialogResult.OK;
        }
    }
}
