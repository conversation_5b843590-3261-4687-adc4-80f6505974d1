﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTLteLastRoadScene
{
    public partial class ResultForm : MinCloseForm
    {
        public ResultForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        internal void FillData(List<RoadSceneBase> roadSceneSet)
        {
            gridCtrl.DataSource = roadSceneSet;
            gridCtrl.RefreshDataSource();
            gridView.BestFitColumns();
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridView.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }

            RoadSceneBase scene = gridView.GetRow(info.RowHandle) as RoadSceneBase;
            MainModel.ClearDTData();
            MainModel.SelectedLTECells.Clear();
            bool isScan = scene.TestPoints[0] is ScanTestPoint_LTE;
            foreach (TestPoint tp in scene.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach (Event evt in scene.Events)
            {
                MainModel.DTDataManager.Add(evt);
            }
            foreach (LTECell cell in scene.Cells)
            {
                MainModel.SelectedLTECells.Add(cell);
            }
            if (isScan)
            {
                MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
            }
            else
            {
                MainModel.FireSetDefaultMapSerialTheme("TD_LTE_RSRP","LTE_FDD:RSRP");
            }
            MainModel.FireDTDataChanged(this);
        }
    }
}
