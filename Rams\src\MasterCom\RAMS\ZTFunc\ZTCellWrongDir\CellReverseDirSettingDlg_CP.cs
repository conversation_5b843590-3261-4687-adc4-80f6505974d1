﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellReverseDirSettingDlg_CP : BaseDialog
    {
        public CellReverseDirSettingDlg_CP()
        {
            InitializeComponent();
        }

        public void SetCondition(CellWrongDirCondition cond)
        {
            if (cond == null)
                return;

            spinEditRSCP.Value = (decimal)cond.RxLevMin;
            spinEditDis.Value = (decimal)cond.DistanceMin;
            spinEditAngle.Value = cond.AngleMin;
            spinEditPer.Value = (decimal)cond.WrongRateMin;
            spinWrongSampleNum.Value = cond.WrongSampleNum;
        }

        public CellWrongDirCondition GetConditon()
        {
            CellWrongDirCondition cond = new CellWrongDirCondition((float)spinEditRSCP.Value, (double)spinEditDis.Value
                , (int)spinEditAngle.Value, (double)spinEditPer.Value, (int)spinWrongSampleNum.Value, false, null, null);
            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        //private void cbxSecondBatch_CheckedChanged(object sender, EventArgs e)
        //{
        //    gbxSecondBatch.Enabled = cbxSecondBatch.Checked;
        //}

        private void dtPickerFirstStart_ValueChanged(object sender, EventArgs e)
        {
        }

        private void dtPickerFirstEnd_ValueChanged(object sender, EventArgs e)
        {
        }

        private void dtPickerSecondStart_ValueChanged(object sender, EventArgs e)
        {
        }

        private void dtPickerSecondEnd_ValueChanged(object sender, EventArgs e)
        {
        }

        private void chkTime_CheckedChanged(object sender, EventArgs e)
        {
        }
    }
}
