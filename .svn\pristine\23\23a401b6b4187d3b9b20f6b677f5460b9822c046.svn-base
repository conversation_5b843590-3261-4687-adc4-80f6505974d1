﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTCellWrongDir;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellWrongDirForm_Scan : MinCloseForm
    {
        public CellWrongDirForm_Scan()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        public void FillData(List<CellWrongDirItem_Scan> list)
        {
            foreach (CellWrongDirItem_Scan item in list)
            {
                item.CalcWrongDir();
            }
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
        }

        private void miExport_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = FilterHelper.ExcelX;
            if (dlg.ShowDialog(this)==DialogResult.OK)
            {
                gridControl.ExportToXlsx(dlg.FileName);
            }
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridView.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }

            CellWrongDirItem_Scan item = gridView.GetRow(info.RowHandle) as CellWrongDirItem_Scan;
            MainModel.ClearDTData();
            foreach (TestPoint tp in item.WrongTestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            //TestPointLayer layer = MainModel.MainForm.GetMapForm().TestPointLayer;
            //layer.AddLegend(new CellWrongDirLegend(), true);
            //layer.Tag = item.Cell;
            //layer.Entitys2Draw = item.WrongTestPoints;
            MainModel.SelectedLTECell = item.Cell as LTECell;
            MainModel.RefreshLegend();
            MainModel.FireDTDataChanged(this);
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
        }
    }
}
