using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class WCDMATestPointDetail:TestPoint
    {
        public override void Fill(MasterCom.RAMS.Net.Content content)
        {
            FileID = content.GetParamInt();
            SN = content.GetParamInt();
            Time = content.GetParamInt();
            Millisecond = (short)content.GetParamInt();
            MS = (byte)content.GetParamInt();
            Longitude = content.GetParamDouble();
            Latitude = content.GetParamDouble();
            this["W_Altitude"] = content.GetParamInt();
            this["W_Speed"] = content.GetParamInt();
            this["W_Mode"] = content.GetParamInt();
            this["W_RxPower"] = content.GetParamFloat();
            this["W_TxPower"] = content.GetParamFloat();
            this["W_BLER"] = content.GetParamFloat();
            this["W_TotalEc_Io"] = content.GetParamFloat();
            this["W_MaxEc_Io"] = content.GetParamFloat();
            this["W_Reference_Ec_Io"] = content.GetParamFloat();
            this["W_TotalRSCP"] = content.GetParamFloat();
            this["W_MaxRSCP"] = content.GetParamFloat();
            this["W_Reference_RSCP"] = content.GetParamFloat();
            this["W_MaxEc_Io_PSC"] = content.GetParamInt();
            this["W_Reference_PSC"] = content.GetParamInt();
            this["W_SIR"] = content.GetParamFloat();
            this["W_Target_SIR"] = content.GetParamFloat();
            this["W_frequency"] = content.GetParamInt();
            this["W_Locked"] = content.GetParamInt();
            this["W_SysCellReserved"] = content.GetParamInt();
            this["W_SysCellBar"] = content.GetParamInt();
            this["W_SysMCC"] = content.GetParamInt();
            this["W_SysMNC"] = content.GetParamInt();
            this["W_SysCellID"] = content.GetParamInt();
            this["W_SysURA_ID"] = content.GetParamInt();
            this["W_SysLAI"] = content.GetParamInt();
            this["W_SysRAI"] = content.GetParamInt();
            this["W_SysSerType"] = content.GetParamInt();
        }

        public override DateTime DateTime
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(Time * 1000L + Millisecond); }
        }
        public override ECurrNetType NetworkType
        {
            get
            {
                ECurrNetType eMode = ECurrNetType.Unknow;
                short? mode = (short?)this["mode"];
                if (mode == null || mode == 255)
                    return ECurrNetType.Unknow;

                if (mode == (int)EMODE_W.WCDMA_GSMIDLE || mode == (int)EMODE_W.WCDMA_GSMDEDICATED || mode == (int)EMODE_W.WCDMA_GSMPACKET)
                {
                    eMode = ECurrNetType.GSM;
                }
                else if (mode == (int)EMODE_W.WCDMA_IDLE || mode == (int)EMODE_W.WCDMA_EDICATED || mode == (int)EMODE_W.WCDMA_PACKET)
                {
                    eMode = ECurrNetType.WCDMA;
                }
                return eMode;
            }
        }
        public enum EMODE_W
        {
            WCDMA_IDLE = 1,
            WCDMA_EDICATED = 2,
            WCDMA_GSMIDLE = 3,
            WCDMA_GSMDEDICATED = 4,
            WCDMA_GSMPACKET = 5,
            WCDMA_PACKET = 6,
        }
    }
}
