﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    static class NbIotGetWorkParamsHelper
    {
        public static Dictionary<string, Dictionary<int, NbIotBtsWorkParam>> GetWorkParamsInfo(
            NbIotStationAcceptAutoSet funcSet)
        {
            //Dictionary<地市, Dictionary<基站编号, NBIot_BtsWorkParam>> 
            Dictionary<string, Dictionary<int, NbIotBtsWorkParam>> workParamSumDic = new Dictionary<string, Dictionary<int, NbIotBtsWorkParam>>();
            try
            {
                //读取指定目录下的小区工参文件并将对应数据添加到数据库中(暂时创建和青海相同结构的表)
                readAndUploadWorkParams(funcSet.CellParamFolderPath);

                //查询导入的小区工参信息转为地市对应的基站工参信息 
                NbIotWorkParamsQuery queryParams = new NbIotWorkParamsQuery(funcSet.WorkParamTime_Begin, funcSet.WorkParamTime_End);
                queryParams.Query();
                workParamSumDic = queryParams.WorkParamSumDic;
            }
            catch (Exception ex)
            {
                reportError(ex);
            }
            reportInfo(string.Format("查询到{0}个地市的待评估工参数据", workParamSumDic.Count));
            return workParamSumDic;
        }

        /// <summary>
        /// 将文件夹下的文件备份
        /// </summary>
        /// <param name="srcFolder">需要备份的文件夹</param>
        /// <param name="fileNameList">该文件夹下需要备份的文件</param>
        public static void BcpFiles(string srcFolder, List<string> fileNameList)
        {
            string bcpFolderPath = srcFolder + "\\工参备份\\" + DateTime.Now.ToString("yyMMdd");
            if (!Directory.Exists(bcpFolderPath))
            {
                Directory.CreateDirectory(bcpFolderPath);
            }

            foreach (string fileName in fileNameList)//备份工参文件后删除
            {
                string filePathNew = fileName.Replace(srcFolder, bcpFolderPath);
                string fileExtension = Path.GetExtension(fileName);

                while (File.Exists(filePathNew))
                {
                    System.Threading.Thread.Sleep(1000);
                    string fileTitle = Path.GetFileNameWithoutExtension(filePathNew);
                    filePathNew = filePathNew.Replace(string.Format("{0}{1}", fileTitle, fileExtension)
                        , string.Format("{0}_{1}{2}", fileTitle, DateTime.Now.ToString("HHmmss"), fileExtension));
                }
                File.Copy(fileName, filePathNew, true);
                File.Delete(fileName);
            }
        }

        #region 读取并上传资管推送来的待验收工参信息
        public static void readAndUploadWorkParams(string strCellParamFolderPath)
        {
            try
            {
                reportInfo("开始读取待评估对象数据...");

                List<string> fileNameList = new List<string>();
                if (Directory.Exists(strCellParamFolderPath))
                {
                    DirectoryInfo dinfo = new DirectoryInfo(strCellParamFolderPath);
                    foreach (System.IO.FileInfo file in dinfo.GetFiles("*.csv", SearchOption.TopDirectoryOnly))
                    {
                        fileNameList.Add(file.FullName);
                    }
                }
                if (fileNameList.Count > 0)
                {
                    Dictionary<string, NbIotCellWorkParam> cellParamInfoDic = new Dictionary<string, NbIotCellWorkParam>();

                    foreach (string fileName in fileNameList)//逐个读取待上传工参文件
                    {
                        getWorkParamFormExcle(fileName, ref cellParamInfoDic);
                    }

                    reportInfo("正在上传待评估对象数据...");
                    NbIotUpLoadWorkParams upLoadParams = new NbIotUpLoadWorkParams(cellParamInfoDic);
                    upLoadParams.Query();//上传工参

                    BcpFiles(strCellParamFolderPath, fileNameList);
                }
                else
                {
                    reportInfo("未找到指定目录下的待上传工参文件");
                }
            }
            catch (Exception ex)
            {
                reportError(ex);
            }
        }
        public static bool getWorkParamFormExcle(string fileName
            , ref Dictionary<string, NbIotCellWorkParam> cellParamInfoDic)
        {
            try
            {
                reportInfo(string.Format("正在读取文件 {0} 信息...", fileName));
                int reReadCount = 0;
                while (reReadCount < 6 && FileStatus.FileIsOpen(fileName) == 1)
                {
                    System.Threading.Thread.Sleep(10000);
                    reReadCount++;
                }

                CSVReader reader = new CSVReader(fileName);
                List<string[]> listData = reader.GetAllData();
                if (listData != null && listData.Count > 0)
                {
                    addCellParamInfo(cellParamInfoDic, listData);
                }
            }
            catch (Exception ex)
            {
                reportError(ex);
                return false;
            }
            return true;
        }

        private static void addCellParamInfo(Dictionary<string, NbIotCellWorkParam> cellParamInfoDic, List<string[]> listData)
        {
            int index = 0;
            foreach (string[] rowDataVec in listData)
            {
                if (rowDataVec.Length > 0)
                {
                    try
                    {
                        index++;
                        string firstValue = rowDataVec[0];
                        if (!string.IsNullOrEmpty(firstValue) && firstValue != "小区名")
                        {
                            NbIotCellWorkParam cellParam = new NbIotCellWorkParam();
                            cellParam.FillDataByExcel(rowDataVec);
                            cellParamInfoDic[cellParam.Token] = cellParam;
                        }
                    }
                    catch (Exception ex)
                    {
                        reportInfo(string.Format("第{0}行工参格式有误！：{1}", index, ex.Message));
                    }
                }
            }
        }
        #endregion

        public static void reportInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }

        public static void reportError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }

    public class NbIotBtsWorkParam : BtsWorkParamBase
    {
        public NbIotBtsWorkParam(NbIotCellWorkParam cellParam)
            : base(cellParam)
        {
            this.Longitude = cellParam.Longitude;
            this.Latitude = cellParam.Latitude;
        }
        public void AddCellParamsInfo(NbIotCellWorkParam info)
        {
            CellWorkParamDic[info.CellID] = info;
        }
    }

    public class NbIotCellWorkParam : CellWorkParamBase
    {
        public int Eci { get; set; }
        public int Earfcn { get; set; }
        public int Pci { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string Token { get { return string.Format("{0}-{1}", Tac, Eci); } }
        public string StrDes { get; set; }
        public string AreaType { get; set; }

        /// <summary>
        /// 与正北方向，顺时针的夹角
        /// </summary>
        public int Direction { get; set; }

        public int Downward { get; set; }

        public int Altitude { get; set; }

        public string UpdateTime { get; set; }


        public void FillDataByDB(Package package)
        {
            CellName = package.Content.GetParamString();
            CellID = package.Content.GetParamInt();
            BtsName = package.Content.GetParamString();
            CGI = package.Content.GetParamString();
            Tac = package.Content.GetParamInt();
            Earfcn = package.Content.GetParamInt();
            Pci = package.Content.GetParamInt();
            DistrictName = package.Content.GetParamString();
            CoverTypeDes = package.Content.GetParamString();
            CoverScene = package.Content.GetParamString();
            ENodeBID = package.Content.GetParamInt();
            int iLongitude = package.Content.GetParamInt();
            int iLatitude = package.Content.GetParamInt();
            Longitude = (double)iLongitude / 10000000;
            Latitude = (double)iLatitude / 10000000;
            Direction = package.Content.GetParamInt();
            Downward = package.Content.GetParamInt();
            Altitude = package.Content.GetParamInt();
            UpdateTime = package.Content.GetParamString();
            StrDes = package.Content.GetParamString();
            AreaType = package.Content.GetParamString();
            Eci = ENodeBID * 256 + CellID;
        }

        public void FillDataByExcel(string[] rowDataVec)
        {
            CellName = rowDataVec[0];
            CellID = getIntValue(rowDataVec[1]);
            BtsName = rowDataVec[2];
            CGI = rowDataVec[3];
            Tac = getIntValue(rowDataVec[4]);
            Earfcn = getIntValue(rowDataVec[5]);
            Pci = getIntValue(rowDataVec[6]);
            DistrictName = rowDataVec[7];
            CoverTypeDes = rowDataVec[8];
            CoverScene = rowDataVec[9];
            ENodeBID = getIntValue(rowDataVec[10]);
            Longitude = Convert.ToDouble(rowDataVec[11].Trim());
            Latitude = Convert.ToDouble(rowDataVec[12].Trim());

            Direction = getIntValue(rowDataVec[13]);
            Downward = getIntValue(rowDataVec[14]);
            Altitude = getIntValue(rowDataVec[15]);

            Eci = ENodeBID * 256 + CellID;
            AreaType = rowDataVec[16];
        }

        public static int getIntValue(string strValue)
        {
            if (string.IsNullOrEmpty(strValue))
            {
                return 0;
            }
            if (strValue.Contains("."))
            {
                double dValue;
                if (double.TryParse(strValue.Trim(), out dValue))
                {
                    return (int)dValue;
                }
            }
            else
            {
                int intValue;
                if (int.TryParse(strValue.Trim(), out intValue))
                {
                    return intValue;
                }
            }
            return 0;
        }
    }

    public class NbIotUpLoadWorkParams : DiySqlMultiNonQuery
    {
        readonly Dictionary<string, NbIotCellWorkParam> cellParamInfoDic;
        public NbIotUpLoadWorkParams(Dictionary<string, NbIotCellWorkParam> cellParamInfoDic)
            : base()
        {
            MainDB = true;
            this.cellParamInfoDic = cellParamInfoDic;
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (NbIotCellWorkParam cellInfo in cellParamInfoDic.Values)
            {
                strb.Append(string.Format("delete from tb_nbiot_btscheck_cfg_cell where cellName = '{0}';"
                   , cellInfo.CellName));
                strb.Append(string.Format(@"insert into [tb_nbiot_btscheck_cfg_cell]([cellName],[cellid],[btsName]
,[cgi],[tac],[earfcn],[pci],[districtName],[coverType],[coverScene],[enodebid],[ilongitude],[ilatitude],[iangle_dir]
,[iangle_ob],[ialtitude],[areaType]) values 
('{0}',{1}, '{2}', '{3}', {4}, {5}, {6}, '{7}', '{8}', '{9}', {10}, {11}, {12}, {13}, {14}, {15},'{16}')"
                    , cellInfo.CellName, cellInfo.CellID, cellInfo.BtsName, cellInfo.CGI, cellInfo.Tac
                    , cellInfo.Earfcn, cellInfo.Pci, cellInfo.DistrictName, cellInfo.CoverTypeDes, cellInfo.CoverScene
                    , cellInfo.ENodeBID, cellInfo.Longitude * 10000000, cellInfo.Latitude * 10000000
                    , cellInfo.Direction, cellInfo.Downward, cellInfo.Altitude, cellInfo.AreaType));
            }
            return strb.ToString();
        }
    }

    public class NbIotWorkParamsQuery : DIYSQLBase
    {
        readonly DateTime beginTime;
        readonly DateTime endTime;

        // Dictionary<地市, Dictionary<基站编号, NBIot_BtsWorkParam>> 
        public Dictionary<string, Dictionary<int, NbIotBtsWorkParam>> WorkParamSumDic { get; set; }

        public NbIotWorkParamsQuery(DateTime beginTime, DateTime endTime)
        {
            MainDB = true;
            this.beginTime = beginTime;
            this.endTime = endTime;
            WorkParamSumDic = new Dictionary<string, Dictionary<int, NbIotBtsWorkParam>>();
        }

        protected override string getSqlTextString()
        {
            return string.Format(@"select [cellName],[cellid],[btsName],[cgi],[tac],[earfcn],[pci],[districtName],[coverType],[coverScene],[enodebid],[ilongitude],[ilatitude],[iangle_dir],[iangle_ob],[ialtitude],[updateTime],[strdes],[areaType] from tb_nbiot_btscheck_cfg_cell where updateTime between '{0}' and '{1}'", beginTime.ToString(), endTime.ToString());
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[19];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            WorkParamSumDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    NbIotCellWorkParam cellInfo = new NbIotCellWorkParam();
                    cellInfo.FillDataByDB(package);

                    LTECell nbiotCell = CellManager.GetInstance().GetLTECellLatest(cellInfo.CellName);
                    if (nbiotCell != null)//待验收工参中没有的信息，关联客户端本地工参补全
                    {
                        cellInfo.SectorID = nbiotCell.SectorID;
                    }

                    AddWorkParamSum(cellInfo);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }

        private void AddWorkParamSum(NbIotCellWorkParam cellInfo)
        {
            Dictionary<int, NbIotBtsWorkParam> btsInfoIDic;
            if (!WorkParamSumDic.TryGetValue(cellInfo.DistrictName, out btsInfoIDic))
            {
                btsInfoIDic = new Dictionary<int, NbIotBtsWorkParam>();
                WorkParamSumDic.Add(cellInfo.DistrictName, btsInfoIDic);
            }
            NbIotBtsWorkParam btsInfo;
            if (!btsInfoIDic.TryGetValue(cellInfo.ENodeBID, out btsInfo))
            {
                btsInfo = new NbIotBtsWorkParam(cellInfo);
                btsInfoIDic.Add(cellInfo.ENodeBID, btsInfo);
            }
            btsInfo.DateTimeDes = cellInfo.UpdateTime;
            btsInfo.AddCellParamsInfo(cellInfo);
        }
    }

    public class NbIotUpdateWorkParamDes : DiySqlMultiNonQuery
    {
        readonly NbIotBtsWorkParam btsParam;
        public NbIotUpdateWorkParamDes(NbIotBtsWorkParam btsParam)
        {
            MainDB = true;
            this.btsParam = btsParam;
        }
        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (CellWorkParamBase param in btsParam.CellWorkParamDic.Values)
            {
                NbIotCellWorkParam cellParam = (NbIotCellWorkParam)param;
                strb.Append(string.Format("update tb_nbiot_btscheck_cfg_cell set strdes = '{0}' where cgi = '{1}';"
                    , cellParam.StrDes, cellParam.CGI));
            }
            return strb.ToString();
        }
    }
}
