﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.ES.Core;

namespace MasterCom.ES.Data
{
    /// <summary>
    /// 数据处理对象基类
    /// </summary>
    public abstract class DataProvider
    {
        /// <summary>
        /// 本数据处理对象所能够提供的函数调用接口
        /// </summary>
        protected Dictionary<string, FuncInfo> funcInfoDic = new Dictionary<string, FuncInfo>();
        protected Dictionary<string, VirtualFuncGroup> vFuncGroup = new Dictionary<string, VirtualFuncGroup>();
        public abstract string Name
        {
            get;
        }
        public Dictionary<string, FuncInfo> FuncInfoDic
        {
            get 
            {
                return funcInfoDic;
            }
        }
        public Dictionary<string, VirtualFuncGroup> VFuncGroup
        {
            get 
            {
                return vFuncGroup;
            }
        }
        public override string ToString()
        {
            return Name;
        }
        public abstract void fireDataFill(object objData);
        public void processFunc(string funcName,string paramStr,out object outret)
        {
            FuncInfo finfo;
            if(funcInfoDic.TryGetValue(funcName,out finfo))
            {
                finfo.ExecuteReturnInfo(paramStr,out outret);
            }
            else
            {
                throw (new Exception("Can't find " + funcName));
            }
        }
    }
}
