﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Func.SystemSetting
{
    public partial class TdAntennaProperties_TD : PropertiesControl
    {
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl;
        private DevExpress.XtraEditors.CheckEdit chkBackgroundStat;

        private readonly ZTTdAntenna queryFunc;

        public TdAntennaProperties_TD(ZTTdAntenna queryFunc)
        {
            InitializeComponent();
            this.queryFunc = queryFunc;
        }

        public override string ParentName
        {
            get { return queryFunc.FuncType.ToString(); }
        }

        public override string ParentSubName
        {
            get { return queryFunc.SubFuncType.ToString(); }
        }

        public override string SelfName
        {
            get { return queryFunc.Name; }
        }

        public override string TabPageName
        {
            get { return queryFunc.Name; }
        }

        public override void Flush()
        {
            chkBackgroundStat.Checked = queryFunc.BackgroundStat;
        }

        public override bool IsValid()
        {
            return true;
        }

        public override void Apply()
        {
            queryFunc.BackgroundStat = chkBackgroundStat.Checked;
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl = new DevExpress.XtraEditors.GroupControl();
            this.chkBackgroundStat = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).BeginInit();
            this.groupControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Location = new System.Drawing.Point(3, 62);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(448, 102);
            this.groupControl1.TabIndex = 5;
            this.groupControl1.Text = "条件设置";
            // 
            // groupControl
            // 
            this.groupControl.Controls.Add(this.chkBackgroundStat);
            this.groupControl.Location = new System.Drawing.Point(3, 3);
            this.groupControl.Name = "groupControl";
            this.groupControl.Size = new System.Drawing.Size(448, 53);
            this.groupControl.TabIndex = 4;
            this.groupControl.Text = "开关设置";
            // 
            // chkBackgroundStat
            // 
            this.chkBackgroundStat.Location = new System.Drawing.Point(15, 26);
            this.chkBackgroundStat.Name = "chkBackgroundStat";
            this.chkBackgroundStat.Properties.Caption = "启用";
            this.chkBackgroundStat.Size = new System.Drawing.Size(75, 19);
            this.chkBackgroundStat.TabIndex = 0;
            // 
            // GsmAntennaProperties_GSM
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.groupControl);
            this.Name = "GsmAntennaProperties_GSM";
            this.Size = new System.Drawing.Size(454, 280);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).EndInit();
            this.groupControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
    }
}
