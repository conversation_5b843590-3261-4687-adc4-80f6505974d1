﻿using MasterCom.MTGis;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLowSpeedConvergeLayer : LayerBase
    {
        public NRLowSpeedConvergeLayer()
           : base("NR低速率汇聚图层")
        {
        }

        private List<NRLowSpeedConvergeBlock> badBlockList;
        public void FillData(List<NRLowSpeedConvergeBlock> badBlockList)
        {
            this.badBlockList = badBlockList;
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(MapScale);
            foreach (NRLowSpeedConvergeBlock block in badBlockList)
            {
                DbPoint pntTL = new DbPoint(block.Bounds.x1, block.Bounds.y2);
                PointF pntFTL;
                gisAdapter.ToDisplay(pntTL, out pntFTL);
                DbPoint pntBr = new DbPoint(block.Bounds.x2, block.Bounds.y1);
                PointF pntFBr;
                gisAdapter.ToDisplay(pntBr, out pntFBr);
                //矩形
                RectangleF rect = new RectangleF(pntFTL.X - 2 * ratio * 150, pntFTL.Y - 2 * ratio * 150, (pntFBr.X - pntFTL.X) + 4 * ratio * 150, (pntFBr.Y - pntFTL.Y) + 4 * ratio * 150);
                Pen recPen = new Pen(Color.MediumVioletRed, 3);
                graphics.DrawRectangle(recPen, rect.Left, rect.Top, rect.Width, rect.Height);
                graphics.DrawString(block.ID.ToString(), new Font(new FontFamily("宋体"), 18, FontStyle.Regular), Brushes.Green, pntFTL.X + (pntFBr.X - pntFTL.X) / 2, pntFTL.Y + (pntFBr.Y - pntFTL.Y) / 2);
            }
        }

    }
}
