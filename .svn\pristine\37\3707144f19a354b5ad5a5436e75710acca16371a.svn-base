﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTHiddFailluresKPIData : DIYStatQuery
    {
        public DIYQueryCQTHiddFailluresKPIData(MainModel mainModel, string netWorker)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            netType = netWorker;
        }
        readonly string netType;
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return ""; }
        }
        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            string str = "查询" + netType + "业务隐形故障";
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21022, string.Format("Name[{0}], {1}", this.Name,str));
        }
        /// <summary>
        /// 涉及的全局变量
        /// </summary>
        #region 全局变量
        private readonly List<string> formulaList = new List<string>();
        private readonly List<FileInfo> fileList = new List<FileInfo>();
        private readonly List<string> fileValueNameList = new List<string>();
        private readonly List<string> finalValueNameList = new List<string>();
        readonly Dictionary<string, List<FileInfo>> fileValueList = new Dictionary<string, List<FileInfo>>();
        readonly Dictionary<string, List<Event>> cqtNameEventList = new Dictionary<string, List<Event>>();
        readonly Dictionary<string, List<CQTEventItem>> nameEventDic = new Dictionary<string, List<CQTEventItem>>();
        readonly List<CQTQualityItem> resultList = new List<CQTQualityItem>();
        readonly List<CQTQualityItem> cqtResultList = new List<CQTQualityItem>();
        readonly List<CQTBlerItem> resultListTD = new List<CQTBlerItem>();
        readonly List<CQTBlerItem> cqtResultListTD = new List<CQTBlerItem>(); 
        Dictionary<int, List<Event>> condEventsDic = new Dictionary<int, List<Event>>();
        string cqtName = "";
        private double num0_4;
        private int numganzhi;
        private int numchixu;
        private int CQTNumGanzhi { get; set; }
        private int CQTNumChixu;
        private double numFB10;
        #endregion
        /// <summary>
        /// 清空变量
        /// </summary>
        private void clearVar()
        {
            fileList.Clear();
            SetFormulaList();
            fileValueNameList.Clear();
            finalValueNameList.Clear();
            fileValueList.Clear();
            resultList.Clear();
            cqtResultList.Clear();
            resultListTD.Clear();
            cqtResultListTD.Clear();
            num0_4 = 0;
            numganzhi = 0;
            numchixu = 0;
            CQTNumGanzhi = 0;
            CQTNumChixu = 0;
            numFB10 = 0;
        }
        /// <summary>
        /// 准备查询数据
        /// </summary>
        protected override void query()
        {
            //清空以及重新初始化变量
            clearVar();
            if (netType.Equals("GSM"))
            {
                CQTGSMFailuresQueryForm xtraformstatus = new CQTGSMFailuresQueryForm();
                if (xtraformstatus.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                xtraformstatus.getSelect(out num0_4, out numganzhi, out numchixu);
            }
            else if (netType.Equals("TD"))
            {
                CQTTDFailuresQueryForm xtraformstatus = new CQTTDFailuresQueryForm();
                if (xtraformstatus.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                xtraformstatus.getSelect(out numFB10, out numchixu);
            }
            // 查找全区域文件
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();
            fileList.AddRange(diyFileInfoData.FlieInfoData);
            //找出所有文件中包含的地点名称
            foreach (FileInfo fileIn in fileList)
            {
                string[] name = fileIn.Name.Split('_');
                if (name.Length < 3)
                    continue;
                if (!fileValueNameList.Contains(name[2]))
                {
                    fileValueNameList.Add(name[2]);
                }
            }
            //处理事件
            PrepareEvents();
            List<int> eventId = new List<int>();
            eventId.AddRange(condEventsDic.Keys);
            //每个地点所涉及的文件
            addFileValueList(eventId);
            //各个地点事件
            cqtEventList();
            //根据用户设置的事件数去掉不符合的地点
            addFinalValueNameList();
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, condition.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
            showForm();
        }

        private void addFileValueList(List<int> eventId)
        {
            foreach (string nameL in fileValueNameList)
            {
                List<FileInfo> subFileList = new List<FileInfo>();
                foreach (FileInfo fileIn in fileList)
                {
                    string[] name = fileIn.Name.Split('_');
                    if (name.Length < 3)
                        continue;
                    if (nameL.Equals(name[2]) && eventId.Contains(fileIn.ID))
                    {
                        subFileList.Add(fileIn);
                    }
                }
                fileValueList.Add(nameL, subFileList);
            }
        }

        private void addFinalValueNameList()
        {
            if (netType.Equals("GSM"))
            {
                foreach (string cpn in fileValueNameList)
                {
                    if (nameEventDic[cpn].Count >= (numganzhi + numchixu))
                    {
                        finalValueNameList.Add(cpn);
                    }
                }
            }
            else if (netType.Equals("TD"))
            {
                foreach (string cpn in fileValueNameList)
                {
                    if (nameEventDic[cpn].Count >= numchixu)
                    {
                        finalValueNameList.Add(cpn);
                    }
                }
            }
        }

        private void showForm()
        {
            if (netType.Equals("GSM"))
            {
                CQTGSMFailuresNew cqtform = new CQTGSMFailuresNew(MainModel, condition);
                cqtform.setData(cqtResultList);
                cqtform.Show();
            }
            else if (netType.Equals("TD"))
            {
                CQTTDFailuresNew cqtform = new CQTTDFailuresNew(MainModel, condition);
                cqtform.setData(cqtResultListTD);
                cqtform.Show();
            }
        }

        /// <summary>
        /// 指标的计算公式列表
        /// </summary>
        private void SetFormulaList()
        {
            formulaList.Clear();
            if (netType.Equals("GSM"))
            {
                formulaList.Add("{100*(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505)/Mx_5A01050C }%");//RxqualSub0-4级占比
            }
            else if (netType.Equals("TD"))
            {
                formulaList.Add("{100*(Tx_72040E+Tx_72040F+Tx_720410+Tx_720411+Tx_720412)/(Tx_72040B+Tx_72040C+Tx_72040D+Tx_720402+Tx_720403+Tx_720404+Tx_720405+Tx_72040E+Tx_72040F+Tx_720410+Tx_720411+Tx_720412) }%");//高BLER占比
            }
        }
        /// <summary>
        /// 查询数据
        /// </summary>
        private void queryInThread(object o)
        {
            ClientProxy clientProxy = (ClientProxy)o;
            Package package = clientProxy.Package;
            int idx = 1;
            WaitBox.CanCancel = true;
            WaitBox.Text = "开始查询...";
            foreach (TimePeriod period in condition.Periods)
            {
                int countTotal = finalValueNameList.Count;
                foreach (string cqtPnt in finalValueNameList)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "正在统计( " + idx++ + "/" + countTotal + " ) " + cqtPnt + " 的指标值...";
                    cqtName = cqtPnt;
                    prepareStatPackage_ImgGrid(package, period, (byte)condition.CarrierTypes[0], condition.IsByRound, cqtPnt);
                    fillContentNeeded_ImgGrid(package);
                    clientProxy.Send();
                    recieveInfo_ImgGrid(clientProxy, period);
                }
            }
            addCqtResultList();
            System.Threading.Thread.Sleep(1000);
            WaitBox.Close();
        }

        private void addCqtResultList()
        {
            if (netType.Equals("GSM"))
            {
                foreach (CQTQualityItem item in resultList)
                {
                    if (item.IWeakQualNum >= numganzhi && item.ILastWeakQualNum >= numchixu && item.FQual0_4Rate < num0_4 && item.FQual0_4Rate >= 0)
                    {
                        cqtResultList.Add(item);
                    }
                }
            }
            else if (netType.Equals("TD"))
            {
                foreach (CQTBlerItem item in resultListTD)
                {
                    if (item.IWeakBlerNum >= numchixu && item.FBlerRate >= numFB10)
                    {
                        cqtResultListTD.Add(item);
                    }
                }
            }
        }

        /// <summary>
        /// 查询全区域隐性故障事件
        /// </summary>
        private void PrepareEvents()
        {
            DIYEventByAllRegion queryEvent = new DIYEventByAllRegion(MainModel);
            queryEvent.SetIsAddEventToDTDataManager(false);
            queryEvent.SetSaveAsFileEventsDic(true);
            queryEvent.showEventChooser = false;
            queryEvent.IsQueryAllEvents = false;
            List<int> eventIds = new List<int>();
            if (netType.Equals("GSM"))
            {
                eventIds.Add(43);//GSM感知质差事件
                eventIds.Add(900);//GSM持续质差事件
            }
            else if (netType.Equals("TD"))
            {
                eventIds.Add(230);//TD持续高BLER事件
            }
            condition.EventIDs = eventIds;
            queryEvent.SetQueryCondition(condition);
            queryEvent.Query();
            condEventsDic = queryEvent.fileEventsDic;
        }
        /// <summary>
        /// 获取各个测试地点的隐性故障事件
        /// </summary>
        private void cqtEventList()
        {
            cqtNameEventList.Clear();
            foreach (string cpn in fileValueNameList)
            {
                List<Event> sunEventList = new List<Event>();
                foreach (FileInfo fileinfo in fileValueList[cpn])//每个测试地点所涉及的文件
                {
                    addValidSunEventList(sunEventList, fileinfo);
                }
                cqtNameEventList.Add(cpn, sunEventList);
            }
            getCQTEventList();
        }

        private void addValidSunEventList(List<Event> sunEventList, FileInfo fileinfo)
        {
            foreach (Event eve in condEventsDic[fileinfo.ID])
            {
                if (netType.Equals("GSM"))
                {
                    if (eve.ID == 43 || eve.ID == 900)
                    {
                        sunEventList.Add(eve);
                    }
                }
                else if (netType.Equals("TD"))
                {
                    if (eve.ID == 230)
                    {
                        sunEventList.Add(eve);
                    }
                }
                else
                {
                    //other
                }
            }
        }

        /// <summary>
        /// 处理事件对应关系
        /// </summary>
        private void getCQTEventList()
        {
            nameEventDic.Clear();
            foreach (string cname in cqtNameEventList.Keys)
            {
                List<CQTEventItem> eventList = new List<CQTEventItem>();
                foreach (Event ev in cqtNameEventList[cname])
                {
                    CQTEventItem cqtEventItem = new CQTEventItem();
                    cqtEventItem.Ieventid = ev.ID;
                    cqtEventItem.Strfilename = ev.FileName;
                    cqtEventItem.Dtime = ev.DateTime;
                    cqtEventItem.Strcellname = ev.CellNameSrc;
                    cqtEventItem.Ilac = (int)ev["LAC"];
                    cqtEventItem.Ici = (int)ev["CI"];
                    cqtEventItem.Ifileid = ev.FileID;
                    cqtEventItem.Itime = ev.Time;
                    eventList.Add(cqtEventItem);
                }
                nameEventDic.Add(cname, eventList);
            }
        }
        /// <summary>
        /// 添加接收指标项
        /// </summary>
        protected virtual void fillContentNeeded_ImgGrid(Package package)
        {
            package.Content.AddParam("-1,-1,-1");
        }
        /// <summary>
        /// 设置查询条件
        /// </summary>
        protected void prepareStatPackage_ImgGrid(Package package, TimePeriod period, byte carrierID, bool byRound, string cqtPointName)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = MasterCom.RAMS.Net.RequestType.REQTYPE_DIY_LOG_KPI;
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYFileName(package, cqtPointName);//按CQT地点匹配文件
            
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);

            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYMomt(package, condition.Momt);
            //
            AddDIYEndOpFlag(package);
        }
        /// <summary>
        /// 按照测试地点名称匹配文件
        /// </summary>
        protected void AddDIYFileName(Package package, string fileNameLikeStr)
        {
            fileNameLikeStr = "_" + fileNameLikeStr + "_@";
            package.Content.AddParam((byte)OpOptionDef.StrLike);
            package.Content.AddParam("0,2,1");//filename
            package.Content.AddParam(fileNameLikeStr);
        }
        protected void AddDIYFileNameFilter(Package package, string totalfilterStr, int orNumCount, int nameFilterType, string cqtPointName)
        {
            if (totalfilterStr.Length == 0)
            {
                return;
            }
            package.Content.AddParam((byte)OpOptionDef.StrLike);
            StringBuilder sbFileTrid = new StringBuilder();
            for (int i = 0; i < orNumCount; i++)
            {
                if (nameFilterType == 1)
                {
                    sbFileTrid.Append("0,16,1");//filepath
                }
                else if (nameFilterType == 2)
                {
                    sbFileTrid.Append("0,33,1");//strdesc
                }
                else
                {
                    sbFileTrid.Append("0,2,1");//filename
                }
                if (i < orNumCount - 1)
                {
                    sbFileTrid.Append(",");
                }
            }
            package.Content.AddParam(sbFileTrid.ToString());
            package.Content.AddParam(totalfilterStr);
        }
        /// <summary>
        /// 接收查询结果
        /// </summary>
        protected void recieveInfo_ImgGrid(ClientProxy clientProxy, TimePeriod period)
        {
            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 25;
            WaitBox.ProgressPercent = progress;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.COLUMN_DEFINE)
                {
                    curImgColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurImgColumnDef(idpairs, curImgColumnDef);
                }
                else if (addRetResult(package, curImgColumnDef, retResult))
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgess(ref index, ref progress);
            }
            caculaValueByFormula(retResult);
            WaitBox.ProgressPercent = 95;
        }

        private void setProgess(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        private bool addRetResult(Package package, List<StatImgDefItem> curImgColumnDef, DataUnitAreaKPIQuery retResult)
        {
            if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_GSM
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_GPRS)
            {
                addGSM_NewImgRetResult(package, curImgColumnDef, retResult);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_AMR
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_PS
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_VP)
            {
                addTDSCDMA_NewImgRetResult(package, curImgColumnDef, retResult);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_AMR
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PS
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_VP
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PSHS)
            {
                addWCDMA_AMRRetResult(package, curImgColumnDef, retResult);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_V
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_D)
            {
                addCDMA_VoiceRetResult(package, curImgColumnDef, retResult);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_CDMA2000_D)
            {
                addEVDO_DataRetResult(package, curImgColumnDef, retResult);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_GSM_MTR)
            {
                addMTR_GSMRetResult(package, curImgColumnDef, retResult);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_TD)
            {
                addSCAN_TDRetResult(package, curImgColumnDef, retResult);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_GSM)
            {
                addSCAN_GSMRetResult(package, curImgColumnDef, retResult);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WLAN)
            {
                addWLANRetResult(package, curImgColumnDef, retResult);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_LTE_AMR)
            {
                addLTERetResult(package, curImgColumnDef, retResult);
            }
            else
            {
                return false;
            }
            return true;
        }

        private static void addGSM_NewImgRetResult(Package package, List<StatImgDefItem> curImgColumnDef, DataUnitAreaKPIQuery retResult)
        {
            DataGSM_NewImg newImg = new DataGSM_NewImg();
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            retResult.addStatData(newImg);
        }

        private static void addTDSCDMA_NewImgRetResult(Package package, List<StatImgDefItem> curImgColumnDef, DataUnitAreaKPIQuery retResult)
        {
            DataTDSCDMA_NewImg newImg = new DataTDSCDMA_NewImg();
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            retResult.addStatData(newImg);
        }

        private static void addWCDMA_AMRRetResult(Package package, List<StatImgDefItem> curImgColumnDef, DataUnitAreaKPIQuery retResult)
        {
            try
            {
                DataWCDMA_AMR newImg = new DataWCDMA_AMR();
                foreach (StatImgDefItem cdf in curImgColumnDef)
                {
                    byte[] imgBytes = package.Content.GetParamBytes();
                    Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                    foreach (string str in cellStatInfoDic.Keys)
                    {
                        newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                    }
                }
                retResult.addStatData(newImg);
            }
            catch
            {
                //continue
            }
        }

        private static void addCDMA_VoiceRetResult(Package package, List<StatImgDefItem> curImgColumnDef, DataUnitAreaKPIQuery retResult)
        {
            DataCDMA_Voice newImg = new DataCDMA_Voice();
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            retResult.addStatData(newImg);
        }

        private static void addEVDO_DataRetResult(Package package, List<StatImgDefItem> curImgColumnDef, DataUnitAreaKPIQuery retResult)
        {
            DataEVDO_Data newImg = new DataEVDO_Data();
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            retResult.addStatData(newImg);
        }

        private static void addMTR_GSMRetResult(Package package, List<StatImgDefItem> curImgColumnDef, DataUnitAreaKPIQuery retResult)
        {
            DataMTR_GSM newImg = new DataMTR_GSM();
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            retResult.addStatData(newImg);
        }

        private static void addSCAN_TDRetResult(Package package, List<StatImgDefItem> curImgColumnDef, DataUnitAreaKPIQuery retResult)
        {
            DataScan_TD newImg = new DataScan_TD();
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            retResult.addStatData(newImg);
        }

        private static void addSCAN_GSMRetResult(Package package, List<StatImgDefItem> curImgColumnDef, DataUnitAreaKPIQuery retResult)
        {
            DataScan_GSM newImg = new DataScan_GSM();
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    newImg.WInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            retResult.addStatData(newImg);
        }

        private static void addWLANRetResult(Package package, List<StatImgDefItem> curImgColumnDef, DataUnitAreaKPIQuery retResult)
        {
            DataWLAN newImg = new DataWLAN();
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    newImg.WInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            retResult.addStatData(newImg);
        }

        private static void addLTERetResult(Package package, List<StatImgDefItem> curImgColumnDef, DataUnitAreaKPIQuery retResult)
        {
            DataLTE newImg = new DataLTE();
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<String, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    newImg.WInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            retResult.addStatData(newImg);
        }

        /// <summary>
        /// 按照指标公式计算指标值
        /// </summary>
        private void caculaValueByFormula(DataUnitAreaKPIQuery retResult)
        {
            CQTNumGanzhi = 0;
            CQTNumChixu = 0;
            List<string> strFormu = new List<string>();
            string strSun = "";
            foreach (string sunFom in formulaList)
            {
                strSun = retResult.CalcValueByFormula(sunFom);
                if (!strSun.Equals("-") && strSun != "")
                    strFormu.Add(strSun);
            }
            if (strFormu.Count == formulaList.Count)
            {
                addResultList(strFormu);
            }
            WaitBox.ProgressPercent = 90;
        }

        private void addResultList(List<string> strFormu)
        {
            if (netType.Equals("GSM"))
            {
                CQTQualityItem cqtCoverItem = new CQTQualityItem();
                cqtCoverItem.FQual0_4Rate = float.Parse(strFormu[0].Split('%')[0]);
                cqtCoverItem.StrQual0_4Rate = cqtCoverItem.FQual0_4Rate.ToString("0.00") + "%";
                cqtCoverItem.Strcqtname = cqtName;
                cqtCoverItem.cqtEventList.AddRange(nameEventDic[cqtName]);
                foreach (CQTEventItem e in cqtCoverItem.cqtEventList)
                {
                    if (e.Ieventid == 43)
                    {
                        CQTNumGanzhi++;
                    }
                    else if (e.Ieventid == 900)
                    {
                        CQTNumChixu++;
                    }
                }
                cqtCoverItem.IWeakQualNum = CQTNumGanzhi;
                cqtCoverItem.ILastWeakQualNum = CQTNumChixu;
                resultList.Add(cqtCoverItem);
            }
            else if (netType.Equals("TD"))
            {
                CQTBlerItem cqtCoverItem = new CQTBlerItem();
                cqtCoverItem.FBlerRate = float.Parse(strFormu[0].Split('%')[0]);
                cqtCoverItem.StrBlerRate = cqtCoverItem.FBlerRate.ToString("0.00") + "%";
                cqtCoverItem.Strcqtname = cqtName;
                cqtCoverItem.cqtEventList.AddRange(nameEventDic[cqtName]);
                cqtCoverItem.IWeakBlerNum = cqtCoverItem.cqtEventList.Count;
                resultListTD.Add(cqtCoverItem);
            }
        }
    }

    public class CQTQualityItem
    {
        public List<CQTEventItem> cqtEventList { get; set; } = new List<CQTEventItem>();
        public float FQual0_4Rate { get; set; }
        public int ILastWeakQualNum { get; set; }
        public int IWeakQualNum { get; set; }
        public string Strcqtname { get; set; }
        public string StrQual0_4Rate { get; set; }
    }
    public class CQTBlerItem
    {
        public List<CQTEventItem> cqtEventList { get; set; } = new List<CQTEventItem>();
        public float FBlerRate { get; set; }
        public int IWeakBlerNum { get; set; }
        public string StrBlerRate { get; set; }
        public string Strcqtname { get; set; }
    }
}