﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model.Interface
{
    public class DIYSQLQueryRoundTime : DIYSQLBase
    {
        public DIYSQLQueryRoundTime(MainModel mm)
            :base(mm)
        {
            Rounds = new List<RoundTime>();
        }

        protected override string getSqlTextString()
        {
            return "select iid,strname,strcomment from tb_cfg_static_month where iid>12";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] ret = new E_VType[3];
            ret[0] = E_VType.E_Int;
            ret[1] = E_VType.E_String;
            ret[2] = E_VType.E_String;
            return ret;
        }

        public List<RoundTime> Rounds { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Rounds.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RoundTime round = new RoundTime();
                    round.Round = package.Content.GetParamInt() - 12;
                    round.Name = package.Content.GetParamString();
                    round.Comment = package.Content.GetParamString();
                    Rounds.Add(round);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get {return "查询轮次"; }
        }
    }

    public class RoundTime
    {
        public RoundTime()
        {
            Round = -1;
            Name = "";
            Comment = "";
        }

        public int Round { get; set; }
        public string Name { get; set; }
        public string Comment { get; set; }
    }
}
