﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.CQT;
using MasterCom.Util;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Util
{
    public partial class ScoreColorRangeSettingBox:BaseFormStyle
    {
        protected CQTKPIScoreColorRange rangeValue;
        public CQTKPIScoreColorRange ScoreColorRange
        {
            get { return rangeValue; }
        }
        protected ScoreOrderType scoreType;
        public ScoreColorRangeSettingBox(CQTKPIScoreColorRange range, float rangeMin, float rangeMax, double scoreMin, double scoreMax, ScoreOrderType scoreType)
        {
            InitializeComponent();
            rangeValue = range;
            this.scoreType = scoreType;
            spinEditMin.Properties.MinValue = (decimal)rangeMin;
            spinEditMin.Properties.MaxValue = (decimal)rangeMax;
            spinEditMax.Properties.MinValue = (decimal)rangeMin;
            spinEditMax.Properties.MaxValue = (decimal)rangeMax;
            spinEditScoreLeft.Properties.MinValue = (decimal)scoreMin;
            spinEditScoreLeft.Properties.MaxValue = (decimal)scoreMax;
            spinEditScoreRight.Properties.MinValue = (decimal)scoreMin;
            spinEditScoreRight.Properties.MaxValue = (decimal)scoreMax;
            spinEditMin.Value = (decimal)range.Min;
            spinEditMax.Value = (decimal)range.Max;
            if (scoreType == ScoreOrderType.Positive)
            {
                spinEditScoreLeft.Value = (decimal)range.ScoreRangeMin;
                spinEditScoreRight.Value = (decimal)range.ScoreRangeMax;
            }
            else
            {
                spinEditScoreLeft.Value = (decimal)range.ScoreRangeMax;
                spinEditScoreRight.Value = (decimal)range.ScoreRangeMin;
                lblScoreLeft.Text = ">=";
                lblScoreRight.Text = ">=";
            }
            textEdit.Text = range.DesInfo;
            colorEdit.Color = range.Color;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (spinEditMax.Value < spinEditMin.Value)
            {
                XtraMessageBox.Show("指标最大值不能小于最小值！");
                return;
            }
            if ((scoreType == ScoreOrderType.Positive && spinEditScoreRight.Value < spinEditScoreLeft.Value)
                || (scoreType == ScoreOrderType.Negative && spinEditScoreRight.Value > spinEditScoreLeft.Value))
            {
                XtraMessageBox.Show("分数最大值不能小于最小值！");
                return;
            }
            rangeValue.Color = colorEdit.Color;
            rangeValue.Min = (float)spinEditMin.Value;
            rangeValue.Max = (float)spinEditMax.Value;
            rangeValue.MaxIncluded = spinEditMax.Value == spinEditMax.Properties.MaxValue;
            if (scoreType == ScoreOrderType.Positive)
            {
                rangeValue.ScoreRangeMin = (double)spinEditScoreLeft.Value;
                rangeValue.ScoreRangeMax = (double)spinEditScoreRight.Value;
            }
            else
            {
                rangeValue.ScoreRangeMin = (double)spinEditScoreRight.Value;
                rangeValue.ScoreRangeMax = (double)spinEditScoreLeft.Value;
            }
            rangeValue.DesInfo = textEdit.Text;
            DialogResult = DialogResult.OK;
        }

        private void spinEditMax_EditValueChanged(object sender, EventArgs e)
        {
            if (rangeValue.IsSmoonthScore)
            {
                if (spinEditMax.Value == spinEditMax.Properties.MaxValue)
                {
                    string text = "<=";
                    lblMax.Text = text;
                    if (scoreType == ScoreOrderType.Positive)
                    {
                        lblScoreRight.Text = text;
                    }
                    else
                    {
                        lblScoreRight.Text = ">=";
                    }
                }
                else
                {
                    string text = "<";
                    lblMax.Text = text;
                    if (scoreType == ScoreOrderType.Positive)
                    {
                        lblScoreRight.Text = text;
                    }
                    else
                    {
                        lblScoreRight.Text = ">";
                    }
                }
            }

        }

    }
}
