﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class CoverPnl : UserControl
    {
        public CoverPnl()
        {
            InitializeComponent();
        }

        public void LinkCondition(FunctionCondition cond)
        {
            CoverCause cr = null;
            foreach (CauseBase r in cond.Causes)
            {
                if (r is CoverCause)
                {
                    cr = r as CoverCause;
                    break;
                }
            }

            if (cr == null)
            {
                return;
            }
            foreach (CauseBase r in cr.SubCauses)
            {
                if (r is WeakCoverCause)
                {
                    weakCoverPnl1.LinkCondition(r as WeakCoverCause);
                }
                else if (r is OverCoverCause)
                {
                    overCoverPnl1.LinkCondition(r as OverCoverCause);
                }
                else if (r is UnstabitilyCover)
                {
                    unstabitilyCoverPnl1.LinkCondition(r as UnstabitilyCover);
                }
                else if (r is IndoorLeakCoverCause)
                {
                    indoorLeakCoverPnl1.LinkCondition(r as IndoorLeakCoverCause);
                }
                else if (r is WrongCover)
                {
                    wrongCoverPnl1.LinkCondition(r as WrongCover);
                }
                else if (r is MessCoverCause)
                {
                    messCoverPnl1.LinkCondition(r as MessCoverCause);
                }
                else if (r is RepeatCoverCause)
                {
                    repeatCoverPnl1.LinkCondition(r as RepeatCoverCause);
                }
            }
        }
    }
}
