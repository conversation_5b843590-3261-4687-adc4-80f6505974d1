﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.Net
{
    public class DIYSampleByStreet : DIYSampleQuery
    {
        public DIYSampleByStreet(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "采样点查询(按道路)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11010, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Street;
        }

        protected override void prepareStatPackage_Sample_FileFilter(Package package, TimePeriod period,bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (!isPreSetted)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_SAMPLE;
            }
            else
            {
                package.Content.Type = this.preSettedCommandType;
            }
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYStreet_Intersect(package);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);

            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            //
            AddDIYEndOpFlag(package);
        }
        protected override void prepareStatPackage_Sample_SampleFilter(Package package, TimePeriod period)
        {
            AddDIYStreets_Sample(package);
            AddDIYEndOpFlag(package);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedStreets.Count <= 0)
            {
                return false;
            }
            return true;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            return intersects(tp.Longitude, tp.Latitude);
        }

        private bool intersects(double longitude, double latitude)
        {
              return Condition.Geometorys.GeoOp.Contains(longitude, latitude);
        }

    }
}
