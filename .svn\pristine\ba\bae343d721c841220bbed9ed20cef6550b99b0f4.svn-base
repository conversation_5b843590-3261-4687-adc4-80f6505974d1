﻿using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class QueryTestRound : DIYSQLBase
    {
        private readonly List<TestRound> testRoundList = new List<TestRound>();
        public List<TestRound> TestRoundList
        {
            get { return testRoundList; }
        }

        public QueryTestRound(MainModel mainModel)
            : base(mainModel)
        {
            MainDB = true;
        }
        protected override string getSqlTextString()
        {
            return @"select year,month,begintime,endtime,comment from tb_stat_area_whitelist_TestRound 
where isvalid =1 order by begintime desc";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_String;
            return rType;
        }
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("正在查询测试时段信息...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }

        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    TestRound testRound = new TestRound();
                    testRound.Year = package.Content.GetParamInt();
                    testRound.Month = package.Content.GetParamInt();
                    int beginTime = package.Content.GetParamInt();
                    int endTime = package.Content.GetParamInt();
                    testRound.SetPeriod(beginTime, endTime);
                    testRound.StrDesc = package.Content.GetParamString();
                    testRound.SN = testRoundList.Count + 1;
                    testRoundList.Add(testRound);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
            WaitBox.Close();
        }

        public override string Name
        {
            get { return "查询跨月指标统计功能测试时段"; }
        }
    }
}
