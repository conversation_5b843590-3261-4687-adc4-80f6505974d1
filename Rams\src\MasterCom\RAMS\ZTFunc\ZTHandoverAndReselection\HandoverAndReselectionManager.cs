﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Collections;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Util;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class HandoverAndReselectionManager
    {
        protected HandoverAndReselectionManager()
        {
            //
        }

        #region 乒乓切换分析
        /// <summary>
        /// 计算乒乓切换结果
        /// </summary>
        /// <param name="dataManageBak">文件数据集</param>
        /// <param name="timeLimit">时间限制</param>
        /// <param name="bSpeedLimit">是否限制时速</param>
        /// <param name="speedLow">最低时速</param>
        /// <param name="speedHigh">最高时速</param>
        /// <param name="ppCellList">乒乓切换涉及小区</param>
        /// <returns>乒乓切换结果序列</returns>
        public static List<HandoverFileDataManager> GetHandoverPingPangResult(List<DTFileDataManager> dataManageBak, int timeLimit, bool bSpeedLimit,
            int speedLow, int speedHigh, List<Event> handoverEvents, List<PingPangCell> pingPangCells)
        {
            List<HandoverFileDataManager> refList = new List<HandoverFileDataManager>();
            pingPangCells.Clear();
            handoverEvents.Clear();
            for (int index = 0; index < dataManageBak.Count; index++)
            {
                DTFileDataManager dtFileDataManager = dataManageBak[index];
                List<Event> events = new List<Event>();
                List<PingPangCell> ppCellList = new List<PingPangCell>();
                HandoverFileDataManager handoverFile = GetHandoverPingPangResult(dtFileDataManager, timeLimit,
                    bSpeedLimit, speedLow, speedHigh, events, ppCellList);
                if (handoverFile.HandoverTimes > 0)
                {
                    handoverFile.Index = refList.Count + 1;
                    refList.Add(handoverFile);
                    handoverEvents.AddRange(events);

                    foreach (PingPangCell ppCell in ppCellList)
                    {
                        SavePingPangCell(pingPangCells, ppCell);
                    }
                }
            }
            return refList;
        }

        public static HandoverFileDataManager GetHandoverPingPangResult(DTFileDataManager dtFileDataManager, int timeLimit, bool bSpeedLimit,
            int speedLow, int speedHigh, List<Event> handoverEvents, List<PingPangCell> pingPangCells)
        {
            pingPangCells.Clear();
            handoverEvents.Clear();
            List<Event> events;
            List<List<Event>> eventsList;
            List<PingPangCell> ppCellList = new List<PingPangCell>();

            DTFileDataManager dtFileDataManagerNew = new DTFileDataManager(dtFileDataManager.FileID, dtFileDataManager.FileName,
                    dtFileDataManager.ProjectType, dtFileDataManager.TestType, dtFileDataManager.CarrierType, dtFileDataManager.LogTable,
                    dtFileDataManager.SampleTableName, dtFileDataManager.ServiceType, dtFileDataManager.MoMtFlag);

            HandoverFileDataManager item = new HandoverFileDataManager(dtFileDataManagerNew);
            try
            {
                Limit limit = new Limit(timeLimit, bSpeedLimit, speedLow, speedHigh);
                if (hasPingPang(dtFileDataManager.Events, limit, out events,
                    out eventsList, ppCellList))
                {
                    foreach (PingPangCell ppCell in ppCellList)
                    {
                        SavePingPangCell(pingPangCells, ppCell);
                    }
                    handoverEvents.AddRange(events);

                    foreach (Event e in events)
                    {
                        dtFileDataManagerNew.Add(e);
                    }

                    item.Des = MakeCellUpdateStr(eventsList);
                    item.Events.AddRange(events);
                    item.EventsList = eventsList;
                    item.HandoverTimes = eventsList.Count;
                    item.AddCells(ppCellList);
                }
            }
            catch
            {
                //continue
            }
            return item;
        }

        public class Limit
        {
            public Limit(int secondLimit, bool bSpeedLimit, int speedLimitLow, int speedLimitHigh)
            {
                SecondLimit = secondLimit;
                BSpeedLimit = bSpeedLimit;
                SpeedLimitLow = speedLimitLow;
                SpeedLimitHigh = speedLimitHigh;
            }

            public int SecondLimit { get; set; }
            public bool BSpeedLimit { get; set; }
            public int SpeedLimitLow { get; set; }
            public int SpeedLimitHigh { get; set; }
        }

        /// <summary>
        /// 判断事件序列是否有乒乓切换
        /// </summary>
        /// <param name="events">切换事件序列</param>
        /// <param name="secondLimit">时间限制</param>
        /// <param name="bSpeedLimit">是否限制时速</param>
        /// <param name="speedLimitLow">最低时速</param>
        /// <param name="speedLimitHigh">最高时速</param>
        /// <param name="resultEvents">乒乓切换涉及到的切换事件列表</param>
        /// <param name="times">乒乓切换次数</param>
        /// <param name="eventsList">乒乓切换事件序列列表</param>
        /// <param name="ppCellList">涉及到的小区信息列表</param>
        /// <returns></returns>
        private static bool hasPingPang(List<Event> events, Limit limit,
            out List<Event> resultEvents, out List<List<Event>> eventsList, List<PingPangCell> ppCellList)
        {
            Queue<Event> timeEventQueue = new Queue<Event>();
            int indexBegin = 0;
            int indexEnd = 0;
            List<int> indexBegins = new List<int>();
            List<int> indexEnds = new List<int>();
            foreach (Event e in events)
            {
                int timeLimit = e.Time - limit.SecondLimit;
                while (timeEventQueue.Count > 0 && (timeEventQueue.Peek().Time < timeLimit ||
                    (timeEventQueue.Peek().Time == timeLimit) && timeEventQueue.Peek().Millisecond <= e.Millisecond))
                {
                    timeEventQueue.Dequeue();
                    indexBegin++;
                }
                addIndex(limit, timeEventQueue, indexBegin, indexEnd, indexBegins, indexEnds, e);
                if (timeEventQueue.Count > 0)
                {
                    Event eTmp = timeEventQueue.Peek();
                    if (!((int)e["TargetLAC"] == (int)eTmp["LAC"] && (int)e["TargetCI"] == (int)eTmp["CI"] &&
                            (int)eTmp["TargetLAC"] == (int)e["LAC"] && (int)eTmp["TargetCI"] == (int)e["CI"]
                        ))
                    {
                        timeEventQueue.Dequeue();
                        indexBegin++;
                    }
                }
                timeEventQueue.Enqueue(e);
                indexEnd++;
            }

            addEvtRes(events, out resultEvents, out eventsList, indexBegins, indexEnds);
            calePingPangCells(events, indexBegins, ppCellList);
            return resultEvents.Count > 0;
        }

        private static void addIndex(Limit limit, Queue<Event> timeEventQueue, int indexBegin, int indexEnd, List<int> indexBegins, List<int> indexEnds, Event e)
        {
            foreach (Event e2 in timeEventQueue)
            {
                if ((int)e["TargetLAC"] == (int)e2["LAC"] && (int)e["TargetCI"] == (int)e2["CI"] &&
                        (int)e2["TargetLAC"] == (int)e["LAC"] && (int)e2["TargetCI"] == (int)e["CI"])
                {
                    if (limit.BSpeedLimit)
                    {
                        double curSpeed = MathFuncs.GetDistance(e.Longitude, e.Latitude, e2.Longitude, e2.Latitude) / Math.Abs(e.Time - e2.Time) * 3.6;
                        if ((curSpeed >= limit.SpeedLimitLow) && (curSpeed <= limit.SpeedLimitHigh))
                        {
                            indexBegins.Add(indexBegin);
                            indexEnds.Add(indexEnd);
                            break;
                        }
                    }
                    else
                    {
                        indexBegins.Add(indexBegin);
                        indexEnds.Add(indexEnd);
                        break;
                    }
                }
            }
        }

        private static void addEvtRes(List<Event> events, out List<Event> resultEvents, out List<List<Event>> eventsList, List<int> indexBegins, List<int> indexEnds)
        {
            resultEvents = new List<Event>();
            for (int index = 0; index < events.Count; index++)
            {
                for (int index2 = 0; index2 < indexBegins.Count; index2++)
                {
                    if (indexBegins[index2] <= index && index <= indexEnds[index2])
                    {
                        resultEvents.Add(events[index]);
                        break;
                    }
                }
            }
            eventsList = new List<List<Event>>();
            for (int i = 0; i < indexBegins.Count; i++)
            {
                List<Event> eList = new List<Event>();
                int iBegin = indexBegins[i];
                int iEnd = indexEnds[i];
                for (int j = iBegin; j <= iEnd; j++)
                {
                    eList.Add(events[j]);
                }
                eventsList.Add(eList);
            }
        }

        /// <summary>
        /// 统计乒乓切换涉及到的小区
        /// </summary>
        /// <param name="events">乒乓切换事件序列</param>
        /// <param name="beginIndexs"></param>
        /// <param name="cellObjList"></param>
        /// <param name="ppCellList"></param>
        private static void calePingPangCells(List<Event> events, List<int> beginIndexs, List<PingPangCell> ppCellList)
        {
            foreach (int i in beginIndexs)
            {
                Event e = events[i];

                SavePingPangCell(ppCellList, getEvtSrcCell(e));
                SavePingPangCell(ppCellList, getEvtTargetCell(e));
            }
        }
        protected static ICell getEvtSrcCell(Event evt)
        {
            ICell icell = evt.GetSrcCell();
            if (icell == null)
            {
                int? lac = (int?)evt["LAC"];
                int? ci = (int?)evt["CI"];
                if (lac != null && ci != null)
                {
                    icell = new UnknowCell((int)lac, (int)ci);
                }
            }
            return icell;
        }
        protected static ICell getEvtTargetCell(Event evt)
        {
            ICell icell = evt.GetTargetCell();
            if (icell == null)
            {
                int? lac = (int?)evt["TargetLAC"];
                int? ci = (int?)evt["TargetCI"];
                if (lac != null && ci != null)
                {
                    icell = new UnknowCell((int)lac, (int)ci);
                }
            }
            return icell;
        }

        /// <summary>
        /// 保存乒乓切换涉及小区信息
        /// </summary>
        /// <param name="ppCellList">保存的乒乓切换涉及小区信息列表</param>
        /// <param name="icell">要保存的小区</param>
        public static void SavePingPangCell(List<PingPangCell> ppCellList, ICell icell)
        {
            if (icell == null)
            {
                return;
            }

            foreach (PingPangCell ppCell in ppCellList)
            {
                if (ppCell.EqualCell(icell))
                {
                    ppCell.pingPangTimes++;
                    return;
                }
            }

            PingPangCell ppCellNew = new PingPangCell(ppCellList.Count + 1, icell);
            ppCellList.Add(ppCellNew);
        }
        public static void SavePingPangCell(List<PingPangCell> ppCellList, PingPangCell curPPCell)
        {
            if (curPPCell.iCell == null)
            {
                return;
            }

            foreach (PingPangCell ppCell in ppCellList)
            {
                if (ppCell.EqualCell(curPPCell.iCell))
                {
                    ppCell.pingPangTimes += curPPCell.pingPangTimes;
                    return;
                }
            }

            PingPangCell ppCellNew = new PingPangCell(ppCellList.Count + 1, curPPCell.iCell);
            ppCellNew.pingPangTimes = curPPCell.pingPangTimes;
            ppCellList.Add(ppCellNew);
        }

        /// <summary>
        /// 获取切换重新事件小区变更序列描述
        /// </summary>
        /// <param name="list">切换重新事件列表</param>
        /// <returns></returns>
        public static string MakeCellUpdateStr(List<List<Event>> list)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < list.Count; i++)
            {
                sb.Append(MakeCellUpdateStr(list[i]));
                sb.Append("；\r\n");
            }
            return sb.ToString();
        }

        /// <summary>
        /// 获取切换重新事件小区变更序列描述
        /// </summary>
        /// <param name="eList"></param>
        /// <returns></returns>
        public static string MakeCellUpdateStr(List<Event> eList)
        {
            string lastLacCi = "";
            StringBuilder sb = new StringBuilder();
            for (int j = 0; j < eList.Count; j++)
            {
                Event e = eList[j];
                int eLac1 = (int)e["LAC"];
                int eCi1 = (int)e["CI"];
                int eLac2 = (int)e["TargetLAC"];
                int eCi2 = (int)e["TargetCI"];

                ICell iCellSrc = e.GetSrcCell();
                ICell iCellTar = e.GetTargetCell();
                string cellName = "";
                if (lastLacCi != (eLac1 + "_" + eCi1))
                {
                    addUpdateSign2StringBuilder(ref sb, e);
                    if (iCellSrc != null)
                    {
                        cellName = iCellSrc.Name + "(" + eLac1 + "," + eCi1 + ")";
                    }
                    else if (e.ID == 851) // LTE
                    {
                        cellName = "(TAC:" + eLac1 + ",ECI:" + eCi1 + ")";
                    }
                    else
                    {
                        cellName = "(LAC:" + eLac1 + ",CI:" + eCi1 + ")";
                    }
                    sb.Append(cellName);
                }

                addUpdateSign2StringBuilder(ref sb, e);
                if (iCellTar != null)
                {
                    cellName = iCellTar.Name + "(" + eLac2 + "," + eCi2 + ")";
                }
                else if (e.ID == 851)
                {
                    cellName = "(TAC:" + eLac1 + ",ECI:" + eCi1 + ")";
                }
                else
                {
                    cellName = "(LAC:" + eLac1 + ",CI:" + eCi1 + ")";
                }
                sb.Append(cellName);
                lastLacCi = eLac2 + "_" + eCi2;
            }
            return sb.ToString();
        }

        private static void addUpdateSign2StringBuilder(ref StringBuilder sb, Event e)
        {
            if (e.ID == 40 || e.ID == 137 || e.ID == 139 || e.ID == 179 || e.ID == 537
                    || e.ID == 539 || e.ID == 579)//小区重选
            {
                sb.Append("=>");
            }
            else
            {
                sb.Append("->");
            }
        }

        #endregion

        #region 过频繁分析
        /// <summary>
        /// 获取切换(重选)过频繁结果
        /// </summary>
        /// <param name="dataManageBak">文件列表</param>
        /// <param name="timeLimit">时间限制(多少秒内发生的)</param>
        /// <param name="distanceLimit">距离限制</param>
        /// <param name="handoverCount">切换次数</param>
        /// <param name="handoverEvents">输出的问题点涉及到的事件列表</param>
        /// <returns>形成过频繁问题的文件列表</returns>
        public static List<HandoverFileDataManager> GetHandoverTooMuchResult(List<DTFileDataManager> dataManageBak, int timeLimit, int distanceLimit,
            int handoverCount, List<Event> handoverEvents)
        {
            handoverEvents.Clear();
            List<HandoverFileDataManager> list = new List<HandoverFileDataManager>();
            for (int index = 0; index < dataManageBak.Count; index++)
            {
                DTFileDataManager dtFileDataManager = dataManageBak[index];
                List<Event> events = new List<Event>();
                HandoverFileDataManager handoverFile = GetHandoverTooMuchResult(dtFileDataManager, timeLimit,
                    distanceLimit, handoverCount, events);
                if (handoverFile.HandoverTimes > 0)
                {
                    handoverEvents.AddRange(events);
                    handoverFile.Index = list.Count + 1;
                    list.Add(handoverFile);
                }
            }
            return list;
        }

        public static HandoverFileDataManager GetHandoverTooMuchResult(DTFileDataManager dtFileDataManager, int timeLimit, int distanceLimit,
            int handoverCount, List<Event> handoverEvents)
        {
            handoverEvents.Clear();
            DTFileDataManager dtFileDataManagerNew = new DTFileDataManager(dtFileDataManager.FileID, dtFileDataManager.FileName,
                dtFileDataManager.ProjectType, dtFileDataManager.TestType, dtFileDataManager.CarrierType, dtFileDataManager.LogTable,
                dtFileDataManager.SampleTableName, dtFileDataManager.ServiceType, dtFileDataManager.MoMtFlag);
            HandoverFileDataManager item = new HandoverFileDataManager(dtFileDataManagerNew);
            List<Event> events;
            List<List<Event>> eventsList;
            if (isTooFrequent(dtFileDataManager.Events, timeLimit, distanceLimit, handoverCount, out events, out eventsList))
            {
                handoverEvents.AddRange(events);

                foreach (Event e in events)
                {
                    dtFileDataManagerNew.Add(e);
                }

                item.Des = MakeCellUpdateStr(eventsList);
                item.Events.AddRange(events);
                item.EventsList = eventsList;
                item.HandoverTimes = eventsList.Count;
            }
            return item;
        }

        private static bool isTooFrequent(List<Event> events, int secondLimit, int distanceLimit, int timesLimit, out List<Event> resultEvents,
            out List<List<Event>> eventsList)
        {
            return HandoverTooFrequentItem.isTooFrequent(events, secondLimit, distanceLimit, timesLimit,
                out resultEvents, out eventsList);
        }

        #endregion

        #region 获取问题事件组(如乒乓切换，切换过频繁)的详细指标
        public static void GetHandoverDetails(List<HandoverFileDataManager> handoverFileList)
        {
            try
            {
                foreach (HandoverFileDataManager handoverFile in handoverFileList)
                {
                    GetHandoverDetails(handoverFile);
                }
            }
            catch
            {
                //continue
            }
        }

        //计算切换频繁的详细数据
        public static void GetHandoverToMuchDetails(HandoverFileDataManager handoverFile, bool isNeedReplay)
        {
            if (handoverFile.EventsList.Count <= 0 || handoverFile.fmnger == null)
                return;

            if (isNeedReplay)
            {
                ReplayFileInPeriod(handoverFile);
                foreach (DTFileDataManager dtfdm in MainModel.GetInstance().DTDataManager.FileDataManagers)
                {
                    if (dtfdm.FileID == handoverFile.fmnger.FileID)
                    {
                        handoverFile.fmnger = dtfdm;
                        break;
                    }
                }
            }

            for (int i = 0; i < handoverFile.Events.Count; i++)
            {
                Event ev = handoverFile.Events[i];
                List<HandoverCellItem> hoCellList = statHandoverCells(handoverFile.fmnger, ev);
                HandoverItem handoverItem = getHandoverItem(ev, hoCellList);
                handoverFile.HandoverEventDic[ev] = handoverItem;
            }
            getHandoverItems(handoverFile, handoverFile.fmnger);
            handoverFile.fmnger.TestPoints.Clear();
        }

        private static HandoverItem getHandoverItem(Event ev, List<HandoverCellItem> hoCellList)
        {
            HandoverItem handoverItem = new HandoverItem(ev);
            foreach (HandoverCellItem cellItem in hoCellList)
            {
                if (cellItem == null) continue;
                cellItem.Ev = ev;
                if (cellItem.Type == HandoverCellType.BeforeHandover)
                    handoverItem.CellItemBefore = cellItem;
                else
                    handoverItem.CellItemAfter = cellItem;
            }

            return handoverItem;
        }

        public static void GetHandoverDetails(HandoverFileDataManager handoverFile)
        {
            ReplayFileInPeriod(handoverFile);
            DTFileDataManager fileMngr = null;
            foreach (DTFileDataManager dtfdm in MainModel.GetInstance().DTDataManager.FileDataManagers)
            {
                if (dtfdm.IsEqual(handoverFile.Tag as DTFileDataManager))
                {
                    fileMngr = dtfdm;
                    addHandoverEvents(handoverFile, dtfdm);
                    break;
                }
            }

            getHandoverItems(handoverFile, fileMngr);
            if (fileMngr != null)
            {
                fileMngr.ClearDTDatas();
            }
        }

        private static void addHandoverEvents(HandoverFileDataManager handoverFile, DTFileDataManager dtfdm)
        {
            for (int i = 0; i < handoverFile.Events.Count; i++)
            {
                Event ev = handoverFile.Events[i];
                List<HandoverCellItem> hoCellList = statHandoverCells(dtfdm, ev);
                HandoverItem handoverItem = new HandoverItem(ev);
                foreach (HandoverCellItem cellItem in hoCellList)
                {
                    if (cellItem == null) continue;
                    cellItem.Ev = ev;
                    if (cellItem.Type == HandoverCellType.BeforeHandover)
                    {
                        handoverItem.CellItemBefore = cellItem;
                    }
                    else
                    {
                        handoverItem.CellItemAfter = cellItem;
                    }
                }
                handoverFile.HandoverEventDic[ev] = handoverItem;
            }
        }

        private static void ReplayFileInPeriod(HandoverFileDataManager dtfdmi)
        {
            DIYReplayFileWithNoWaitBox qb = new DIYReplayFileWithNoWaitBox(MainModel.GetInstance());
            qb.FilterByPeriod = true;
            QueryCondition condition = new QueryCondition();
            condition.QueryType = 2;

            DTFileDataManager dtfdm = dtfdmi.Tag as DTFileDataManager;
            FileInfo fileInfo = new FileInfo();
            fileInfo.Name = dtfdm.FileName;
            fileInfo.ProjectID = dtfdm.ProjectType;
            fileInfo.ID = dtfdm.FileID;
            fileInfo.LogTable = dtfdm.LogTable;
            fileInfo.DistrictID = dtfdm.GetFileInfo().DistrictID;
            fileInfo.ServiceType = dtfdm.ServiceType;
            fileInfo.SampleTbName = dtfdm.SampleTableName;

            condition.FileInfos.Add(fileInfo);
            condition.Periods.Add(GetTimePeriod(dtfdmi.Events));

            qb.SetQueryCondition(condition);
            qb.Query();
        }

        private static TimePeriod GetTimePeriod(List<Event> events)
        {
            return new TimePeriod(events[0].DateTime.AddSeconds(-10), events[events.Count - 1].DateTime.AddSeconds(5));
        }

        /// <summary>
        /// 计算事件切换前后小区指标信息
        /// </summary>
        /// <param name="dtfdm"></param>
        /// <param name="e"></param>
        /// <returns></returns>
        private static List<HandoverCellItem> statHandoverCells(DTFileDataManager dtfdm, Event e)
        {
            List<HandoverCellItem> hoCellList = new List<HandoverCellItem>();

            HandoverCellItem tps1 = new HandoverCellItem(e, HandoverCellType.BeforeHandover);  //切换前
            HandoverCellItem tps2 = new HandoverCellItem(e, HandoverCellType.AfterHandover);  //切换后

            hoCellList.Add(tps1);
            hoCellList.Add(tps2);

            int index = getChangeTestpointIndex(dtfdm.TestPoints, e);
            if (index == -1)
            {
                return hoCellList;
            }

            TestPoint tp;

            //根据Index统计相关的点
            for (int i = index; i >= 0; i--)
            {//切换前3秒
                tp = dtfdm.TestPoints[i];

                if (!checkTestPoint(tp, e, HandoverCellType.BeforeHandover))
                {
                    break;
                }
                AddTp2CellItem(tps1, tp);
            }

            #region 查询TAU更新前小区名
            if (e.ID == 853)
            {
                tps2.CellName = e.CellNameSrc;

                int evtIndex = getChangeEventIndex(dtfdm.Events, e);
                for (int i = evtIndex; i >= 0; i--)
                {
                    Event evt = dtfdm.Events[i];
                    if (evt.CellNameSrc != "" && evt.CellNameSrc != tps2.CellName)
                    {
                        tps1.CellName = evt.CellNameSrc;
                        break;
                    }
                }
            }
            #endregion

            for (int i = index + 1; i < dtfdm.TestPoints.Count; i++)
            {
                tp = dtfdm.TestPoints[i];

                if (!checkTestPoint(tp, e, HandoverCellType.AfterHandover))//切换后3秒
                {
                    break;
                }
                AddTp2CellItem(tps2, tp);
            }
            return hoCellList;
        }

        /// <summary>
        /// 采样点算入相应小区信息
        /// </summary>
        /// <param name="cellItem">小区信息对象</param>
        /// <param name="tp">TestPoint</param>
        private static void AddTp2CellItem(HandoverCellItem cellItem, TestPoint tp)
        {
            int? lac = tp.GetLAC();
            int? ci = tp.GetCI();

            if (lac == null || ci == null || lac == 0 || ci == 0)
            {
                return;
            }

            if ((cellItem.Lac == lac && cellItem.Ci == ci) || cellItem.Ev.ID == 853 || cellItem.Ev.ID == 3172)
            {
                if (tp is WCDMATestPointDetail)
                {
                    setWcdmaCellInfo(cellItem, tp);
                }
                else if (tp is TDTestPointDetail || tp is TDTestPointSummary)
                {
                    setTdCellInfo(cellItem, tp);
                }
                else if (tp is LTETestPointDetail)
                {
                    setTddCellInfo(cellItem, tp);
                }
                else if (tp is LTEFddTestPoint)
                {
                    setFddCellInfo(cellItem, tp);
                }
                else
                {
                    setGsmCellInfo(cellItem, tp);
                }
            }
        }

        private static void setWcdmaCellInfo(HandoverCellItem cellItem, TestPoint tp)
        {
            DTDisplayParameterSystem dtd = DTDisplayParameterManager.GetInstance()["WCDMA"];
            int rxlev = (int)(float?)tp["W_Reference_RSCP"];
            int rxqual = (int)(float?)tp["W_Reference_Ec_Io"];
            if (dtd["Reference_RSCP"].ValueMin <= rxlev && rxlev <= dtd["Reference_RSCP"].ValueMax)
            {
                cellItem.Rxlev += rxlev;
                cellItem.RxlevCount++;
            }
            if (dtd["Reference_Ec_Io"].ValueMin <= rxqual && rxqual <= dtd["Reference_Ec_Io"].ValueMax)
            {
                cellItem.Rxqual += rxqual;
                cellItem.RxqualCount++;
            }
        }

        private static void setTdCellInfo(HandoverCellItem cellItem, TestPoint tp)
        {
            DTDisplayParameterSystem dtd = DTDisplayParameterManager.GetInstance()["TD-SCDMA"];
            int ta = int.Parse(tp[MainModel.TD_TA].ToString());
            int rxlev = (int)(float?)tp["TD_PCCPCH_RSCP"];
            if (dtd[MainModel.TD_TA.Substring(3)].ValueMin <= ta && ta <= dtd[MainModel.TD_TA.Substring(3)].ValueMax)
            {
                cellItem.Ta += ta;
                cellItem.TaCount++;
            }
            if (dtd["PCCPCH_RSCP"].ValueMin <= rxlev && rxlev <= dtd["PCCPCH_RSCP"].ValueMax)
            {
                cellItem.Rxlev += rxlev;
                cellItem.RxlevCount++;
            }
        }

        private static void setTddCellInfo(HandoverCellItem cellItem, TestPoint tp)
        {
            float? rxlev = (float?)tp["lte_RSRP"];
            float? rxqual = (float?)tp["lte_SINR"];
            if (-141 <= rxlev && rxlev <= 25)
            {
                cellItem.Rxlev += (int)(float)rxlev;
                cellItem.RxlevCount++;
            }
            if (-50 <= rxqual && rxqual <= 50)
            {
                cellItem.Rxqual += (int)(float)rxqual;
                cellItem.RxqualCount++;
            }
        }

        private static void setFddCellInfo(HandoverCellItem cellItem, TestPoint tp)
        {
            float? rxlev = (float?)tp["lte_fdd_RSRP"];
            float? rxqual = (float?)tp["lte_fdd_SINR"];
            if (-141 <= rxlev && rxlev <= 25)
            {
                cellItem.Rxlev += (int)(float)rxlev;
                cellItem.RxlevCount++;
            }
            if (-50 <= rxqual && rxqual <= 50)
            {
                cellItem.Rxqual += (int)(float)rxqual;
                cellItem.RxqualCount++;
            }
        }

        private static void setGsmCellInfo(HandoverCellItem cellItem, TestPoint tp)
        {
            object obj = tp["TA"];
            if (obj != null)
            {
                int ta = int.Parse(obj.ToString());
                cellItem.Ta += ta;
                cellItem.TaCount++;
            }
            obj = tp["RxLevSub"];
            if (obj != null)
            {
                int rxlev = int.Parse(obj.ToString());
                cellItem.Rxlev += rxlev;
                cellItem.RxlevCount++;
            }
            obj = tp["RxQualSub"];
            if (obj != null)
            {
                int rxqual = int.Parse(obj.ToString());
                cellItem.Rxqual += rxqual;
                cellItem.RxqualCount++;
            }
        }

        /// <summary>
        /// 查找切换点的索引
        /// </summary>
        /// <param name="cellItemAfter">TestPoint 集合</param>
        /// <param name="events">Event  集合</param>
        /// <returns></returns>
        private static int getChangeTestpointIndex(List<TestPoint> tpList, Event e)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > e.SN)
                {
                    index = i - 1;
                    break;
                }
                if (tpList[i].SN == e.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        private static int getChangeEventIndex(List<Event> evtList, Event e)
        {
            int index = -1;
            for (int i = 0; i < evtList.Count; i++)
            {
                if (evtList[i].SN > e.SN)
                {
                    index = i - 1;
                    break;
                }
                if (evtList[i].SN == e.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        /// <summary>
        /// 检测是否属于有效范围内的切换点
        /// 1、切换前后 3 秒
        /// 2、切换前后 LAC、CI比较
        /// </summary>
        /// <param name="tp">切换所在的点</param>
        /// <param name="e">切换事件</param>
        /// <returns>true or false</returns>
        private static bool checkTestPoint(TestPoint tp, Event e, HandoverCellType cellType)
        {
            bool isFlag = false;
            int? lac = tp.GetLAC();
            int? ci = tp.GetCI();
            int? eLac1 = (int?)e["LAC"];
            int? eCi1 = (int?)e["CI"];

            if (lac == null || ci == null)
            {
                return isFlag;
            }

            long timeTestpoint = tp.Time * 1000L + tp.Millisecond;
            long tiemEvent = e.Time * 1000L + e.Millisecond;

            if (((cellType == HandoverCellType.BeforeHandover)
                    && ((timeTestpoint + 3 * 1000) >= tiemEvent))//比较时间范围(小于它的3秒内）
                && (lac == eLac1 && ci == eCi1))
            {
                isFlag = true;
            }
            else if (((cellType == HandoverCellType.AfterHandover)
                        && ((timeTestpoint - 3 * 1000) <= tiemEvent))) //比较时间范围(大于它的3秒内）
            //&& (lac == eLac2 && ci == eCi2)) //shielded by wj 切换后LAC,CI获取太慢，导致匹配不到，因此屏蔽
            {
                isFlag = true;
            }

            return isFlag;
        }

        private static void getHandoverItems(HandoverFileDataManager dtfdmi, DTFileDataManager fileMngr)
        {
            if (dtfdmi.EventsList.Count <= 0)
            {
                return;
            }
            foreach (List<Event> eList in dtfdmi.EventsList)
            {
                if (eList.Count > 1)
                {
                    HandoverProblemItem item = new HandoverProblemItem();
                    item.Index = dtfdmi.HandoverItems.Count + 1;
                    dtfdmi.HandoverItems.Add(item);
                    for (int i = 0; i < eList.Count; i++)
                    {
                        Event e = eList[i];
                        // 同一个e可能存在不同切换组中，导致hoItem被重新修改Index，故加上Clone修正
                        HandoverItem hoItem = dtfdmi.HandoverEventDic[e].Clone();
                        hoItem.Index = i + 1;
                        item.handoverItemList.Add(hoItem);
                    }
                    fillHandoverItem(item);

                    if (fileMngr.ServiceType == (int)ServiceType.LTE_TDD_DATA
                        || fileMngr.ServiceType == (int)ServiceType.LTE_TDD_MULTI
                        || fileMngr.ServiceType == (int)ServiceType.LTE_TDD_VOICE
                        || fileMngr.ServiceType == (int)ServiceType.LTE_FDD_DATA
                        || fileMngr.ServiceType == (int)ServiceType.LTE_FDD_MULTI
                        || fileMngr.ServiceType == (int)ServiceType.LTE_FDD_VOICE)
                    {
                        setHandoverProblemItemInfo(fileMngr, eList, item);
                    }
                }
            }
        }

        private static void setHandoverProblemItemInfo(DTFileDataManager fileMngr, List<Event> eList, HandoverProblemItem item)
        {
            if (eList.Count > 1)
            {
                int bSn = eList[0].SN;
                int eSn = eList[eList.Count - 1].SN;
                int rsrpNum = 0;
                double rsrpSum = 0;
                int sinrNum = 0;
                double sinrSum = 0;
                int speedNum = 0;
                double speedSum = 0;
                int grantNum = 0;
                double grantSum = 0;
                bool isfdd = false;
                int iAppSeepNum = 0;
                double dAppSeepSum = 0;
                foreach (TestPoint tp in fileMngr.TestPoints)
                {
                    if (tp is LTEFddTestPoint)
                    {
                        isfdd = true;
                    }
                    else if (tp is LTETestPointDetail)
                    {
                        isfdd = false;
                    }
                    if (tp.SN < bSn)
                    {
                        continue;
                    }
                    if (tp.SN > eSn)
                    {
                        break;
                    }
                    getRsrp(ref rsrpNum, ref rsrpSum, tp);
                    getSinr(ref sinrNum, ref sinrSum, tp);
                    getSpeed(ref speedNum, ref speedSum, tp);
                    getGrant(ref grantNum, ref grantSum, tp);
                    getAppSeepSum(ref iAppSeepNum, ref dAppSeepSum, tp);
                }

                setRsrpNum(item, rsrpNum, rsrpSum, isfdd);
                setSinrNum(item, sinrNum, sinrSum, isfdd);
                setSpeedNum(item, speedNum, speedSum, isfdd);
                setGrantNum(item, grantNum, grantSum, isfdd);
                setIAppSeepNum(item, isfdd, iAppSeepNum, dAppSeepSum);
            }
        }

        private static void getRsrp(ref int rsrpNum, ref double rsrpSum, TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_RSRP"];
            if (tp is LTEFddTestPoint)
            {
                rsrp = (float?)tp["lte_fdd_RSRP"];
            }
            if (-141 <= rsrp && rsrp <= 25)
            {
                rsrpNum++;
                rsrpSum += (float)rsrp;
            }
        }

        private static void getSinr(ref int sinrNum, ref double sinrSum, TestPoint tp)
        {
            float? sinr = (float?)tp["lte_SINR"];
            if (tp is LTEFddTestPoint)
            {
                sinr = (float?)tp["lte_fdd_SINR"];
            }
            if (-50 <= sinr && sinr <= 50)
            {
                sinrNum++;
                sinrSum += (float)sinr;
            }
        }

        private static void getSpeed(ref int speedNum, ref double speedSum, TestPoint tp)
        {
            double? speed = (double?)tp["lte_PDCP_DL_Mb"];
            if (tp is LTEFddTestPoint)
            {
                speed = (double?)tp["lte_fdd_PDCP_DL_Mb"];
            }
            if (0 < speed)
            {
                speedNum++;
                speedSum += (double)speed;
            }
        }

        private static void getGrant(ref int grantNum, ref double grantSum, TestPoint tp)
        {
            short? grant = (short?)tp["lte_PDCCH_DL_Grant_Count"];
            if (tp is LTEFddTestPoint)
            {
                grant = (short?)tp["lte_fdd_PDCCH_DL_Grant_Count"];
            }
            if (0 <= grant)
            {
                grantNum++;
                grantSum += (short)grant;
            }
        }

        private static void getAppSeepSum(ref int iAppSeepNum, ref double dAppSeepSum, TestPoint tp)
        {
            double? dAppSeep = (double?)(int?)tp["lte_APP_ThroughputDL"];
            if (tp.FileName.Contains("上传"))
            {
                dAppSeep = (double?)(int?)tp["lte_APP_ThroughputUL"];
            }
            if (tp is LTEFddTestPoint)
            {
                dAppSeep = (double?)(int?)tp["lte_fdd_APP_ThroughputDL"];
            }
            if (dAppSeep >= 0)
            {
                iAppSeepNum++;
                dAppSeepSum += (double)dAppSeep;
            }
        }

        private static void setRsrpNum(HandoverProblemItem item, int rsrpNum, double rsrpSum, bool isfdd)
        {
            if (rsrpNum != 0)
            {
                if (isfdd)
                {
                    item.Param["lte_fdd_RSRP_Avg"] = Math.Round(rsrpSum / rsrpNum, 2);
                }
                else
                {
                    item.Param["lte_RSRP_Avg"] = Math.Round(rsrpSum / rsrpNum, 2);
                }
            }
        }

        private static void setSinrNum(HandoverProblemItem item, int sinrNum, double sinrSum, bool isfdd)
        {
            if (sinrNum != 0)
            {
                if (isfdd)
                {
                    item.Param["lte_fdd_SINR_Avg"] = Math.Round(sinrSum / sinrNum, 2);
                }
                else
                {
                    item.Param["lte_SINR_Avg"] = Math.Round(sinrSum / sinrNum, 2);
                }
            }
        }

        private static void setSpeedNum(HandoverProblemItem item, int speedNum, double speedSum, bool isfdd)
        {
            if (speedNum != 0)
            {
                if (isfdd)
                {
                    item.Param["lte_fdd_PDCP_DL_Mb_Avg"] = Math.Round(speedSum / speedNum, 2);
                }
                else
                {
                    item.Param["lte_PDCP_DL_Mb_Avg"] = Math.Round(speedSum / speedNum, 2);
                }
            }
        }

        private static void setGrantNum(HandoverProblemItem item, int grantNum, double grantSum, bool isfdd)
        {
            if (grantNum != 0)
            {
                if (isfdd)
                {
                    item.Param["lte_fdd_PDCCH_DL_Grant_Count_Avg"] = Math.Round(grantSum / grantNum, 2);
                }
                else
                {
                    item.Param["lte_PDCCH_DL_Grant_Count_Avg"] = Math.Round(grantSum / grantNum, 2);
                }
            }
        }

        private static void setIAppSeepNum(HandoverProblemItem item, bool isfdd, int iAppSeepNum, double dAppSeepSum)
        {
            if (iAppSeepNum != 0)
            {
                if (isfdd)
                {
                    item.Param["lte_fdd_APP_ThroughputDL_Num"] = iAppSeepNum;
                    item.Param["lte_fdd_APP_ThroughputDL_Avg"] = Math.Round(dAppSeepSum / iAppSeepNum / 1024.0 / 1024.0, 2);
                }
                else
                {
                    item.Param["lte_APP_ThroughputDL_Num"] = iAppSeepNum;
                    item.Param["lte_APP_ThroughputDL_Avg"] = Math.Round(dAppSeepSum / iAppSeepNum / 1024.0 / 1024.0, 2);
                }
            }
        }

        private static void fillHandoverItem(HandoverProblemItem item)
        {
            List<Event> eList = new List<Event>();
            StringBuilder sb = new StringBuilder();
            foreach (HandoverItem cellItem in item.handoverItemList)
            {
                sb.Append(cellItem.Des + ";\r\n");
                if (!eList.Contains(cellItem.Event))
                {
                    eList.Add(cellItem.Event);
                }
            }
            item.Des += sb.ToString();
            item.Name = HandoverAndReselectionManager.MakeCellUpdateStr(eList);
            item.overPool = isOverPool(eList);
            item.eventList = eList;
        }

        /// <summary>
        /// 是否跨pool
        /// </summary>
        /// <param name="eList"></param>
        /// <returns></returns>
        private static bool isOverPool(List<Event> eList)
        {
            for (int i = 0; i < eList.Count; i++)
            {
                Event e = eList[i];
                Cell cellSrc = e.GetSrcCell() as Cell;
                Cell cellTar = e.GetTargetCell() as Cell;
                if (cellSrc != null && cellTar != null
                    && cellSrc.PoolID != -1 && cellTar.PoolID != -1
                    && cellSrc.PoolID != cellTar.PoolID)
                {
                    return true;
                }
            }
            return false;
        }

        #endregion

    }

    public interface IHandoverTreeItem
    {
        int Index { get; }
        string Name { get; }
        string Des { get; }
        object Tag { get; }
    }

    /// <summary>
    /// 切换或者重选专题有问题的文件
    /// </summary>
    public class HandoverFileDataManager : IHandoverTreeItem
    {
        public DTFileDataManager fmnger { get; set; }
        public HandoverFileDataManager(DTFileDataManager fmnger)
        {
            this.fmnger = fmnger;
        }

        public object Tag { get { return fmnger; } }

        #region IHandoverTreeItem 成员
        
        public int Index { get; set; }

        public string Name
        {
            get { return fmnger.FileName; }
        }
        
        public string Des { get; set; }

        #endregion

        private readonly List<Event> events = new List<Event>();
        public List<Event> Events
        {
            get { return events; }
        }
        
        public Dictionary<Event, HandoverItem> HandoverEventDic { get; set; } = new Dictionary<Event, HandoverItem>();
        public int HandoverTimes { get; set; }
        public List<List<Event>> EventsList { get; set; } = new List<List<Event>>();
        public List<ICell> ICells { get; set; } = new List<ICell>();
        public List<HandoverProblemItem> HandoverItems { get; set; } = new List<HandoverProblemItem>();

        public void AddCells(List<PingPangCell> ppCellList)
        {
            foreach (PingPangCell ppCell in ppCellList)
            {
                ICells.Add(ppCell.iCell);
            }
        }

        public List<BackgroundResult> ConvertToBackgroundResults()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (HandoverProblemItem handoverProblem in HandoverItems)
            {
                int maxItemCount = 20;//每20条转换成一个BackgroundResult，防止image过大
                while (handoverProblem.handoverItemList.Count > maxItemCount)
                {
                    bgResultList.Add(itemsConvertToBackgroundResult(handoverProblem
                        , handoverProblem.handoverItemList.GetRange(0, maxItemCount)));

                    handoverProblem.handoverItemList.RemoveRange(0, maxItemCount);
                }
                if (handoverProblem.handoverItemList.Count > 0)
                {
                    bgResultList.Add(itemsConvertToBackgroundResult(handoverProblem, handoverProblem.handoverItemList));
                }
            }
            return bgResultList;
        }
        private BackgroundResult itemsConvertToBackgroundResult(HandoverProblemItem handoverProblem,
            List<HandoverItem> handoverItemList)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.FileID = fmnger.FileID;
            bgResult.FileName = fmnger.FileName;
            bgResult.LongitudeStart = handoverProblem.LongitudeStart;
            bgResult.LatitudeStart = handoverProblem.LatitudeStart;
            bgResult.LongitudeMid = handoverProblem.LongitudeMid;
            bgResult.LatitudeMid = handoverProblem.LatitudeMid;
            bgResult.LongitudeEnd = handoverProblem.LongitudeEnd;
            bgResult.LatitudeEnd = handoverProblem.LatitudeEnd;
            bgResult.ISTime = handoverProblem.ISTime;
            bgResult.IETime = handoverProblem.IETime;
            bgResult.DistanceLast = handoverProblem.DistanceLast;
            bgResult.AddImageValue(handoverProblem.Index);//切换序号
            bgResult.AddImageValue(handoverItemList.Count);
            foreach (HandoverItem item in handoverItemList)
            {
                bgResult.AddImageValue(item.DateTimeString);
                bgResult.AddImageValue((int)(10000000 * item.Longitude));
                bgResult.AddImageValue((int)(10000000 * item.Latitude));
                bgResult.AddImageValue(item.CellNameSrc);
                bgResult.AddImageValue(item.RxLevAvgStringBefore);
                bgResult.AddImageValue(item.RxQualStringBefore);
                bgResult.AddImageValue(item.CellNameTarget);
                bgResult.AddImageValue(item.RxLevAvgStringAfter);
                bgResult.AddImageValue(item.RxQualStringAfter);
            }
            return bgResult;
        }

        public static List<NPOIRow> BgResultsToNPOIRowList(List<BackgroundResult> backgroundResultList)
        {
            Dictionary<int, Dictionary<string, List<BackgroundResult>>> bgResultDic_All
               = new Dictionary<int, Dictionary<string, List<BackgroundResult>>>();
            foreach (BackgroundResult bgResult in backgroundResultList)
            {
                Dictionary<string, List<BackgroundResult>> bgResultDic_File;
                if (!bgResultDic_All.TryGetValue(bgResult.FileID, out bgResultDic_File))
                {
                    bgResultDic_File = new Dictionary<string, List<BackgroundResult>>();
                    bgResultDic_All[bgResult.FileID] = bgResultDic_File;
                }

                string problemKey = bgResult.ISTime + "-" + bgResult.IETime;
                List<BackgroundResult> bgList;
                if (!bgResultDic_File.TryGetValue(problemKey, out bgList))
                {
                    bgList = new List<BackgroundResult>();
                    bgResultDic_File[problemKey] = bgList;
                }
                bgList.Add(bgResult);
            }

            return bgResultsToNPOIRowList(bgResultDic_All);
        }
        private static List<NPOIRow> bgResultsToNPOIRowList(Dictionary<int, Dictionary<string, List<BackgroundResult>>> bgResultDic_All)
        {
            List<NPOIRow> handInfoNPOIRowList = new List<NPOIRow>();
            NPOIRow rowTitle = new NPOIRow();
            rowTitle.AddCellValue("文件序号");
            rowTitle.AddCellValue("文件名称");
            rowTitle.AddCellValue("切换序号");
            rowTitle.AddCellValue("切换描述");
            rowTitle.AddCellValue("切换事件个数");
            rowTitle.AddCellValue("切换事件序号");
            rowTitle.AddCellValue("切换时间");
            rowTitle.AddCellValue("切换时经度");
            rowTitle.AddCellValue("切换时纬度");
            rowTitle.AddCellValue("切换前小区");
            rowTitle.AddCellValue("切换前场强");
            rowTitle.AddCellValue("切换前质量");
            rowTitle.AddCellValue("切换后小区");
            rowTitle.AddCellValue("切换后场强");
            rowTitle.AddCellValue("切换后质量");
            handInfoNPOIRowList.Add(rowTitle);

            int fileIndex = 0;
            foreach (Dictionary<string, List<BackgroundResult>> bgResultDic_File in bgResultDic_All.Values)
            {
                fileIndex++;
                NPOIRow handDetailRow_1 = null;
                int handIndex = 0;
                foreach (List<BackgroundResult> bgResultList in bgResultDic_File.Values)
                {
                    if (handDetailRow_1 == null)
                    {
                        handDetailRow_1 = new NPOIRow();
                        handInfoNPOIRowList.Add(handDetailRow_1);
                        handDetailRow_1.AddCellValue(fileIndex);
                        handDetailRow_1.AddCellValue(bgResultList[0].FileName);
                    }

                    handDetailRow_1.AddSubRow(sameProblemToNPOIRow(bgResultList, ref handIndex));
                }
            }
            return handInfoNPOIRowList;
        }
        private static NPOIRow sameProblemToNPOIRow(List<BackgroundResult> bgResultList, ref int handIndex)
        {
            handIndex++;
            int eventIndex = 0;
            StringBuilder strbHandoverDirection = new StringBuilder();

            NPOIRow handDetailRow_2 = new NPOIRow();
            handDetailRow_2.AddCellValue(handIndex);
            handDetailRow_2.AddCellValue("");//切换描述 先赋初始值
            handDetailRow_2.AddCellValue(0);//切换事件个数 先赋初始值
            foreach (BackgroundResult bgResult in bgResultList)
            {
                bgResult.GetImageValueInt();//index
                int eventCount = bgResult.GetImageValueInt();

                for (int i = 0; i < eventCount; i++)
                {
                    eventIndex++;
                    string timeString = bgResult.GetImageValueString();
                    float ilongitude = bgResult.GetImageValueInt();
                    float ilatitude = bgResult.GetImageValueInt();
                    string cellNameBefore = bgResult.GetImageValueString();
                    string rxLevAvgStringBefore = bgResult.GetImageValueString();
                    string rxQualStringBefore = bgResult.GetImageValueString();
                    string cellNameAfter = bgResult.GetImageValueString();
                    string rxLevAvgStringAfter = bgResult.GetImageValueString();
                    string rxQualStringAfter = bgResult.GetImageValueString();

                    if (eventIndex == 1)
                    {
                        strbHandoverDirection.Append(cellNameBefore);
                    }
                    strbHandoverDirection.Append("->" + cellNameAfter);

                    NPOIRow handDetailRow_3 = new NPOIRow();
                    handDetailRow_2.AddSubRow(handDetailRow_3);
                    handDetailRow_3.AddCellValue(eventIndex);
                    handDetailRow_3.AddCellValue(timeString);
                    handDetailRow_3.AddCellValue((double)ilongitude / 10000000);
                    handDetailRow_3.AddCellValue((double)ilatitude / 10000000);
                    handDetailRow_3.AddCellValue(cellNameBefore);
                    handDetailRow_3.AddCellValue(rxLevAvgStringBefore);
                    handDetailRow_3.AddCellValue(rxQualStringBefore);
                    handDetailRow_3.AddCellValue(cellNameAfter);
                    handDetailRow_3.AddCellValue(rxLevAvgStringAfter);
                    handDetailRow_3.AddCellValue(rxQualStringAfter);
                }
                handDetailRow_2.cellValues[1] = strbHandoverDirection.ToString();
                handDetailRow_2.cellValues[2] = eventIndex;
            }
            return handDetailRow_2;
        }
    }

    /// <summary>
    /// 切换或者重选的问题类
    /// </summary>
    public class HandoverProblemItem
    {
        public int Index { get; set; } = 0;
        public string Name { get; set; } = "";
        public string Des { get; set; } = "";
        public List<HandoverItem> handoverItemList { get; set; } = new List<HandoverItem>();
        public List<Event> eventList { get; set; } = new List<Event>();
        public bool overPool { get; set; } = false;
        public string OverPoolString
        {
            get { return overPool ? "是" : "否"; }
        }
        
        public Dictionary<string, object> Param { get; set; } = new Dictionary<string, object>();

        public double LongitudeStart
        {
            get { return eventList[0].Longitude; }
        }
        public double LatitudeStart
        {
            get { return eventList[0].Latitude; }
        }
        public double LongitudeMid
        {
            get
            {
                if (eventList.Count > 2)
                {
                    return eventList[eventList.Count / 2].Longitude;
                }
                else
                {
                    return (LongitudeStart + LongitudeEnd) / 2;
                }
            }
        }
        public double LatitudeMid
        {
            get
            {
                if (eventList.Count > 2)
                {
                    return eventList[eventList.Count / 2].Latitude;
                }
                else
                {
                    return (LatitudeStart + LatitudeEnd) / 2;
                }
            }
        }
        public double LongitudeEnd
        {
            get { return eventList[eventList.Count - 1].Longitude; }
        }
        public double LatitudeEnd
        {
            get { return eventList[eventList.Count - 1].Latitude; }
        }

        public int ISTime
        {
            get { return eventList[0].Time; }
        }
        public int IETime
        {
            get { return eventList[eventList.Count - 1].Time; }
        }

        public double DistanceLast
        {
            get
            {
                double distance = 0;
                for (int i = 0; i < eventList.Count - 1; i++)
                {
                    distance += MathFuncs.GetDistance(eventList[i].Longitude, eventList[i].Latitude,
                        eventList[i + 1].Longitude, eventList[i + 1].Latitude);
                }
                return Math.Round(distance, 2);
            }
        }
    }

    /// <summary>
    /// 单个切换或者重选事件类
    /// </summary>
    public class HandoverItem : IHandoverTreeItem
    {
        public HandoverItem(Event e)
        {
            this.Event = e;
        }

        public HandoverItem(Event e, bool isNetReslect)
        {
            this.Event = e;
            this.isLTENetReselect = isNetReslect;
        }
        private readonly bool isLTENetReselect = false;
        public HandoverCellItem CellItemBefore { get; set; }

        public HandoverCellItem CellItemAfter { get; set; }

        public Event Event { get; set; }

        #region IHandoverTreeItem 成员

        public int Index { get; set; }

        public string Name
        {
            get
            {
                return CellItemBefore.CellName + "->" + CellItemAfter.CellName;
            }
        }

        public string OverType
        {
            get
            {
                string type = "";
                switch (Event.ID)
                {
                    case 542:
                    case 3141:
                        type = "W切G-硬切";
                        break;
                    case 545:
                    case 3144:
                        type = "W切W-硬切";
                        break;
                    case 548:
                    case 3147:
                        type = "W切W-软切";
                        break;
                    case 1300:
                    case 3300:
                        type = "LTE->GSM";
                        break;
                    case 1117:
                    case 1302:
                        type = "LTE->TD";
                        break;
                    case 1304:
                        type = "TD->GSM";
                        break;
                    case 1306:
                    case 3306:
                        type = "GSM->LTE";
                        break;
                    case 1120:
                    case 1308:
                    case 3120:
                    case 3308:
                        type = "TD->LTE";
                        break;
                    case 1310:
                    case 3310:
                        type = "GSM->TD";
                        break;
                    default:
                        break;
                }
                return type;
            }
        }

        public string EventRoadName
        {
            get
            {
                return Event.RoadPlaceDesc;
            }
        }

        public string StrGridTypeAndName
        {
            get
            {
                string strGrid = "";
                if (this.isLTENetReselect)
                {
                    strGrid = ZTDIYQueryReselectionTooMuch_LTE.isContainPoint(Event.Longitude, Event.Latitude);
                }
                return strGrid;
            }
        }

        public string StrGridType
        {
            get
            {
                if (StrGridTypeAndName.Split(',').Length > 1)
                    return StrGridTypeAndName.Split(',')[0];
                return "";
            }

        }

        public string StrGridName
        {
            get
            {
                if (StrGridTypeAndName.Split(',').Length > 1)
                    return StrGridTypeAndName.Split(',')[1];
                return "";
            }

        }

        public string Des
        {
            get
            {
                return (Event == null ? "" : (EventDesc + "时间:" + DateTimeString + "," +
                                        EventDesc + "时坐标(" + Longitude + "，" + Latitude + "),")) + DescBefore + "," + DescAfter;
            }
        }

        public string EventDesc
        {
            get
            {
                string sEvent = "切换";
                if (Event.ID == 40 || Event.ID == 137 || Event.ID == 139 || Event.ID == 179 || Event.ID == 537
                || Event.ID == 539 || Event.ID == 579)
                {
                    sEvent = "重选";
                }
                else if (Event.ID == 20)
                {
                    sEvent = "位置更新";
                }
                else if (Event.ID == 28)
                {
                    sEvent = "路由更新";
                }
                return sEvent;
            }
        }

        public string DescBefore
        {
            get
            {
                if (Event.ID == 853 || Event.ID == 3172)
                {
                    return EventDesc + EventDesc + "前RSRP:" + RxLevAvgStringBefore + "," +
                                       EventDesc + "前SINR:" + RxQualStringBefore;
                }
                else if (!this.isLTENetReselect)
                {
                    return EventDesc + "前TA:" + TAStringBefore + "," + EventDesc + "前Rxlev/PCCPCH_RSCP:" + RxLevAvgStringBefore + "," +
                    EventDesc + "前Rxqual:" + RxQualStringBefore;
                }
                else
                {
                    return EventDesc + EventDesc + "前场强值:" + RxLevAvgStringBefore + "," +
                    EventDesc + "前质量值:" + RxQualStringBefore;
                }
            }
        }

        public string DescAfter
        {
            get
            {
                if (Event.ID == 853 || Event.ID == 3172)
                {
                    return EventDesc + EventDesc + "后RSRP:" + RxLevAvgStringAfter + "," +
                       EventDesc + "后SINR:" + RxQualStringAfter;
                }
                else if (!this.isLTENetReselect)
                {
                    return EventDesc + "后TA:" + TAStringAfter + "," + EventDesc + "后Rxlev/PCCPCH_RSCP:" + RxLevAvgStringAfter + "," +
                        EventDesc + "后Rxqual:" + RxQualStringAfter;
                }
                else
                {
                    return EventDesc + EventDesc + "后场强值:" + RxLevAvgStringAfter + "," +
                        EventDesc + "后质量值:" + RxQualStringAfter;
                }
            }
        }

        public object Tag { get { return CellItemAfter; } }

        public string CellNameSrc
        {
            get { return Event.CellNameSrc; }
        }
        public string CellNameTarget
        {
            get { return Event.CellNameTarget; }
        }
        #endregion

        public string DateTimeString
        {
            get { return Event.DateTime.ToString("yyyy-MM-dd HH:mm:ss"); }
        }

        public string TimeString
        {
            get { return Event.DateTime.ToString("HH:mm:ss.fff"); }
        }

        public string HandOverDec
        {
            get
            {
                StringBuilder sbHandover = new StringBuilder();
                sbHandover.Append("【");
                sbHandover.Append(Event.CellNameSrc);
                sbHandover.Append("】 -> 【");
                sbHandover.Append(Event.CellNameTarget);
                sbHandover.Append("】");
                return sbHandover.ToString();
            }
        }

        public double Longitude
        {
            get { return Event.Longitude; }
        }
        public double Latitude
        {
            get { return Event.Latitude; }
        }
        public string RxLevAvgStringBefore
        {
            get { return CellItemBefore.RxlevCount != 0 ? ((double)CellItemBefore.Rxlev / (double)CellItemBefore.RxlevCount).ToString("0.00") : " _  "; }
        }
        public string RxLevAvgStringAfter
        {
            get { return CellItemAfter.RxlevCount != 0 ? ((double)CellItemAfter.Rxlev / (double)CellItemAfter.RxlevCount).ToString("0.00") : " _  "; }
        }
        public string RxQualStringBefore
        {
            get { return CellItemBefore.RxqualCount != 0 ? ((double)CellItemBefore.Rxqual / (double)CellItemBefore.RxqualCount).ToString("0.00") : " _  "; }
        }
        public string RxQualStringAfter
        {
            get { return CellItemAfter.RxqualCount != 0 ? ((double)CellItemAfter.Rxqual / (double)CellItemAfter.RxqualCount).ToString("0.00") : " _  "; }
        }
        public string TAStringBefore
        {
            get { return CellItemBefore.TaCount != 0 ? ((double)CellItemBefore.Ta / (double)CellItemBefore.TaCount).ToString("0.00") : " _  "; }
        }
        public string TAStringAfter
        {
            get { return CellItemAfter.TaCount != 0 ? ((double)CellItemAfter.Ta / (double)CellItemAfter.TaCount).ToString("0.00") : " _  "; }
        }

        public HandoverItem Clone()
        {
            return this.MemberwiseClone() as HandoverItem;
        }
    }

    /// <summary>
    /// 切换或者重选涉及小区信息类
    /// </summary>
    public class HandoverCellItem
    {
        public HandoverCellItem(Event e, HandoverCellType type)
        {
            this.Ev = e;
            this.Type = type;

            if (e != null)
            {
                if (type == HandoverCellType.BeforeHandover)
                {
                    this.Lac = (int)e["LAC"];
                    this.Ci = (int)e["CI"];
                    this.CellName = e.CellNameSrc;
                }
                else
                {
                    this.Lac = (int)e["TargetLAC"];
                    this.Ci = (int)e["TargetCI"];
                    this.CellName = e.CellNameTarget;
                }
            }
        }

        public Event Ev { get; set; }
        public void AddStat(HandoverCellItem tps)
        {
            this.Ta += tps.Ta;
            this.TaCount += tps.TaCount;
            this.Rxlev += tps.Rxlev;
            this.RxlevCount += tps.RxlevCount;
            this.Rxqual += tps.Rxqual;
            this.RxqualCount += tps.RxqualCount;
        }

        public int Lac { get; set; } = 0;
        public int Ci { get; set; } = 0;
        public int Ta { get; set; } = 0;
        public int Rxlev { get; set; } = 0;
        public int Rxqual { get; set; } = 0;
        public int TaCount { get; set; } = 0;
        public int RxlevCount { get; set; } = 0;
        public int RxqualCount { get; set; } = 0;
        public string CellName { get; set; } = "";
        
        /// <summary>
        /// 切换前后类型，切换前或切换后
        /// </summary>
        public HandoverCellType Type { get; set; }
    }

    /// <summary>
    /// 切换小区类型：切换前，切换后
    /// </summary>
    public enum HandoverCellType
    {
        BeforeHandover,
        AfterHandover
    }

    /// <summary>
    /// 问题点类型：乒乓切换，过频繁
    /// </summary>
    public enum HandoverProblemType
    {
        PingPang,
        TooMuch
    }

    /// <summary>
    /// 乒乓切换涉及小区(N个文件乒乓切换涉及到小区次数汇总)
    /// </summary>
    public class PingPangCell
    {
        public PingPangCell(int index, ICell cell)
        {
            this.pingPangTimes = 1;
            this.index = index;
            this.iCell = cell;
        }
        public int index { get; set; }
        public string cellName
        {
            get
            {
                if (iCell != null)
                {
                    return iCell.Name;
                }
                return null;
            }
        }
        public int pingPangTimes { get; set; }
        public ICell iCell { get; set; }
        public int LAC
        {
            get
            {
                if (iCell is Cell)
                {
                    return (iCell as Cell).LAC;
                }
                else if (iCell is LTECell)
                {
                    return (iCell as LTECell).TAC;
                }
                else if (iCell is WCell)
                {
                    return (iCell as WCell).LAC;
                }
                else if (iCell is TDCell)
                {
                    return (iCell as TDCell).LAC;
                }
                else if (iCell is UnknowCell)
                {
                    return (iCell as UnknowCell).LAC;
                }
                return 0;
            }
        }
        public int CI
        {
            get
            {
                if (iCell is Cell)
                {
                    return (iCell as Cell).CI;
                }
                else if (iCell is LTECell)
                {
                    return (iCell as LTECell).ECI;
                }
                else if (iCell is WCell)
                {
                    return (iCell as WCell).CI;
                }
                else if (iCell is TDCell)
                {
                    return (iCell as TDCell).CI;
                }
                else if (iCell is UnknowCell)
                {
                    return (int)(iCell as UnknowCell).CI;
                }
                return 0;
            }
        }

        public string LACCI
        {
            get { return LAC + "|" + CI; }
        }

        public bool EqualCell(ICell icell)
        {
            if (iCell != null)
            {
                return iCell.Equals(icell);
            }
            return false;
        }
    }

    class WindowSplitter
    {
        public int MSecondMax { get; private set; }
        public double DistanceMax { get; private set; }
        public int CountMin { get; set; }
        public List<Event> Events { get; private set; }
        public List<EventWindow> WindowSet { get; private set; }
        public WindowSplitter(int secondMax, int distanceMax, int countMin, List<Event> events)
        {
            this.MSecondMax = secondMax * 1000;
            this.DistanceMax = distanceMax;
            this.CountMin = countMin;
            this.Events = events;
            EventWindow win = null;
            WindowSet = new List<EventWindow>();
            for (int i = 0; i < Events.Count; i++)
            {
                Event evt = events[i];
                if (win == null)
                {//初始化窗口
                    win = new EventWindow(evt);
                    continue;
                }
                if (!win.TryAddEvent(evt, MSecondMax, distanceMax))
                {//窗口未能容纳当前事件，无法推进
                    if (win.Items.Count >= countMin)
                    {
                        WindowSet.Add(win);//当前窗口满足所有条件，保存
                        win = new EventWindow(evt);//保存后，以当前事件新建窗口
                    }
                    else if (win.DequeueHead())
                    {//移除窗口首点，继续尝试推进窗口
                        i--;
                    }
                    else
                    {//窗口仅有一点，无法移除首点。以当前事件新建窗口
                        win = new EventWindow(evt);
                    }
                }
            }
            if (win != null && win.Items.Count >= countMin)
            {
                WindowSet.Add(win);
            }
        }

        public class EventWindow
        {
            public double TotalMilliseconds { get; private set; }
            public double Distance { get; private set; }
            public List<WindowItem> Items
            {
                get;
                private set;
            }
            public List<Event> GetEventSet()
            {
                List<Event> ret = new List<Event>();
                if (Items != null)
                {
                    foreach (WindowItem item in Items)
                    {
                        ret.Add(item.Event);
                    }
                }
                return ret;
            }
            public WindowItem Head { get; private set; }
            public WindowItem End { get; private set; }
            public EventWindow(Event headEvt)
            {
                Items = new List<WindowItem>();
                Head = new WindowItem(headEvt);
                End = Head;
                Items.Add(Head);
            }

            public bool TryAddEvent(Event evt, int msMax, int distanceMax)
            {
                Event lastEvt = End.Event;
                TimeSpan secondGap = evt.DateTime - lastEvt.DateTime;
                if (this.TotalMilliseconds + secondGap.TotalMilliseconds > msMax)
                {
                    return false;
                }

                double distanceGap = MathFuncs.GetDistance(lastEvt.Longitude, lastEvt.Latitude, evt.Longitude, evt.Latitude);
                if (this.Distance + distanceGap > distanceMax)
                {
                    return false;
                }
                End.SecondGapToNext = secondGap;
                End.DistanceGapToNext = distanceGap;
                this.TotalMilliseconds += secondGap.TotalMilliseconds;
                this.Distance += distanceGap;
                WindowItem item = new WindowItem(evt);
                End = item;
                this.Items.Add(item);
                return true;
            }

            internal bool DequeueHead()
            {
                if (Head == End)
                {
                    return false;
                }
                Items.RemoveAt(0);
                TotalMilliseconds -= Head.SecondGapToNext.TotalMilliseconds;
                Distance -= Head.DistanceGapToNext;
                Head = Items[0];
                return true;
            }
        }

        public class WindowItem
        {
            public TimeSpan SecondGapToNext { get; set; }
            public double DistanceGapToNext { get; set; }
            public Event Event { get; private set; }
            public WindowItem(Event evt)
            {
                this.Event = evt;
            }
        }
    }

    class HandoverTooFrequentItem
    {
        public static bool isTooFrequent(List<Event> events, int secondLimit, int distanceLimit, int timesLimit,
            out List<Event> resultEvents, out List<List<Event>> eventsList)
        {
            resultEvents = new List<Event>();
            eventsList = new List<List<Event>>();
            if (events.Count == 0)
            {
                return false;
            }

            WindowSplitter splitter = new WindowSplitter(secondLimit, distanceLimit, timesLimit, events);
            foreach (WindowSplitter.EventWindow win in splitter.WindowSet)
            {
                resultEvents.AddRange(win.GetEventSet());
                eventsList.Add(win.GetEventSet());
            }
            return resultEvents.Count > 0;
        }

        public HandoverTooFrequentItem(Event evt, int secondLimit, int distanceLimit, int timesLimit)
        {
            if (secondLimit <= 0 || distanceLimit <= 0 || timesLimit <= 0)
            {
                throw (new Exception());
            }

            this.secondLimit = secondLimit;
            this.distanceLimit = distanceLimit;
            this.timesLimit = timesLimit;

            this.Events = new List<Event>();
            this.Events.Add(evt);
        }

        public bool TryAddEvent(Event evt)
        {
            Event lastEvt = Events[Events.Count - 1];
            double secondGap = evt.Time - lastEvt.Time + (evt.Millisecond - lastEvt.Millisecond) / 1000.0;
            totalSeconds += secondGap;
            if (totalSeconds > secondLimit)
            {
                return false;
            }

            double distanceGap = MathFuncs.GetDistance(lastEvt.Longitude, lastEvt.Latitude, evt.Longitude, evt.Latitude);
            totalDistance += distanceGap;
            if (totalDistance > distanceLimit)
            {
                return false;
            }

            Events.Add(evt);
            return true;
        }

        public bool IsValid
        {
            get { return Events.Count >= timesLimit; }
        }

        public List<Event> Events
        {
            get;
            private set;
        }

        private readonly int secondLimit;

        private readonly int distanceLimit;

        private readonly int timesLimit;

        private double totalSeconds;

        private double totalDistance;
    }
}
