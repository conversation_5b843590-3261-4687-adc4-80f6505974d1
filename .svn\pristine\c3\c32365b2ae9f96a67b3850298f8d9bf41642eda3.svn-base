﻿using MasterCom.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRScanCellMultiCoverageForm : MinCloseForm
    {
        public NRScanCellMultiCoverageForm()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            DisposeWhenClose = true;
        }

        private MapForm mapForm = null;
        List<NRCellMultiCovInfo> resList;

        public void FillData(ScanMultiCoverageCondition cond, List<NRCellMultiCovInfo> resList)
        {
            this.resList = resList;
            radioGroupType.Properties.Items[0].Description = $"绝对覆盖度(>{cond.AbsoluteValue}dBm)";
            radioGroupType.Properties.Items[1].Description = $"相对覆盖度(-{cond.CoverBandDiff}dB内)";
            radioGroupType.Properties.Items[2].Description = $"综合覆盖度(>{cond.AbsoluteValue}dBm 并且在-{cond.CoverBandDiff}dB内)";

            gridControl.DataSource = resList;
            gridControl.RefreshDataSource();

            MainModel.RefreshLegend();
            if (mapForm != null)
            {
                mapForm.GetTDCellLayer().Invalidate();
            }
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            export2Excel();
        }

        private void export2Excel()
        {
            List<NPOIRow> rowList = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("序号");
            row.AddCellValue("小区名称");
            row.AddCellValue("TAC");
            row.AddCellValue("NCI");
            row.AddCellValue("ARFCN");
            row.AddCellValue("PCI");
            row.AddCellValue("最强采样点数");
            row.AddCellValue("绝对重叠覆盖度");
            row.AddCellValue("绝对条件样本点数");
            row.AddCellValue("相对重叠覆盖度");
            row.AddCellValue("相对条件样本点数");
            row.AddCellValue("综合重叠覆盖度");
            row.AddCellValue("综合条件样本点数");
            row.AddCellValue("覆盖距离(米)");
            row.AddCellValue("平均信号强度");
            row.AddCellValue("相对覆盖度大于等于4的采样点占比(%)");
            row.AddCellValue("相对覆盖带内小区序号");
            row.AddCellValue("相对覆盖带内小区名称");
            row.AddCellValue("进入覆盖带采样点数");
            row.AddCellValue("平均信号强度");
            row.AddCellValue("与主服小区距离(米)");
            row.AddCellValue("与主服小区距离(米)");
            rowList.Add(row);

            foreach (var item in resList)
            {
                row = new NPOIRow();
                row.AddCellValue(item.SN);
                row.AddCellValue(item.Cell.Name);
                row.AddCellValue(item.Cell.TAC);
                row.AddCellValue(item.Cell.NCI);
                row.AddCellValue(item.Cell.SSBARFCN);
                row.AddCellValue(item.Cell.PCI);
                row.AddCellValue(item.Top1Count);
                row.AddCellValue(item.AbsRate);
                row.AddCellValue(item.AbsSampleCount);
                row.AddCellValue(item.RelRate);
                row.AddCellValue(item.RelSampleCount);
                row.AddCellValue(item.MulRate);
                row.AddCellValue(item.MulSampleCount);
                row.AddCellValue(item.Distance);
                row.AddCellValue(item.Rsrp.Avg);
                row.AddCellValue(item.RelFactor4Rate.Rate);

                foreach (var otherCell in item.OtherCellDic.Values)
                {
                    NPOIRow subRow = new NPOIRow();
                    subRow.AddCellValue(otherCell.SN);
                    subRow.AddCellValue(otherCell.CellName);
                    subRow.AddCellValue(otherCell.Rsrp.Count);
                    subRow.AddCellValue(otherCell.Rsrp.Avg);
                    subRow.AddCellValue(otherCell.Distance2ServCell);
                    row.AddSubRow(subRow);
                }

                //foreach (string otherCell in item.OtherCellDic.Keys)
                //{
                //    NPOIRow subRow = new NPOIRow();
                //    subRow.AddCellValue(item.OtherCellDic[otherCell].SN);
                //    subRow.AddCellValue(item.OtherCellDic[otherCell].CellName);
                //    subRow.AddCellValue(item.OtherCellDic[otherCell].Rsrp.Count);
                //    subRow.AddCellValue(item.OtherCellDic[otherCell].Rsrp.Avg);
                //    subRow.AddCellValue(item.OtherCellDic[otherCell].Distance2ServCell);
                //    row.AddSubRow(subRow);
                //}

                rowList.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rowList, true);
        }

        private void btnColorSetting_Click(object sender, EventArgs e)
        {
            ColorRangeMngDlg mngDlg = new ColorRangeMngDlg();
            mngDlg.FixMinMax(0, 50);
            mngDlg.MakeRangeModeOnly();
            mngDlg.FillColorRanges(NRScanCellMultiCoverageColorRanges.Instance.ColorRanges);
            if (DialogResult.OK == mngDlg.ShowDialog(this))
            {
                NRScanCellMultiCoverageColorRanges.Instance.ColorRanges = mngDlg.ColorRanges;
                MainModel.RefreshLegend();
                if (mapForm != null)
                {
                    mapForm.updateMap();
                }
            }
        }

        private void radioGroupType_SelectedIndexChanged(object sender, EventArgs e)
        {
            NRScanCellMultiCoverage.GetInstance().ShowCoverageType = (ShowCoverage)radioGroupType.SelectedIndex;
            if (mapForm != null)
            {
                mapForm.updateMap();
            }
        }

        public override void ReleaseResources()
        {
            NRScanCellMultiCoverage.GetInstance().ClearData();
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            NRCellMultiCovInfo cellInfo = gridView.GetFocusedRow() as NRCellMultiCovInfo;
            if (cellInfo?.Cell != null)
            {
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in cellInfo.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
                MainModel.SetSelectedNRCell(cellInfo.Cell);
                mapForm.GoToView(cellInfo.Cell.Longitude, cellInfo.Cell.Latitude, 6000);
            }
        }
    }
}
