﻿using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTVillageTestTownInfo
    {
        public ZTVillageTestTownInfo(string districtName, string countyName, string townName, ResvRegion regionInfo)
        {
            DistrictName = districtName;
            CountyName = countyName;
            TownName = townName;
            RegionInfo = regionInfo;
        }

        public string DistrictName { get; set; }
        public string CountyName { get; set; }
        public string TownName { get; set; }
        public ResvRegion RegionInfo { get; set; }
    }

    public class ZTVillageTestBranchInfo
    {
        public ZTVillageTestBranchInfo(string districtName, string countyName, string branchName, ResvRegion regionInfo)
        {
            DistrictName = districtName;
            CountyName = countyName;
            BranchName = branchName;
            RegionInfo = regionInfo;
        }

        public string DistrictName { get; set; }
        public string CountyName { get; set; }
        public string BranchName { get; set; }
        public ResvRegion RegionInfo { get; set; }
    }

    public class ZTVillageTestCellGrid
    {
        public ICell Cell { get; set; }
        public FileInfo File { get; set; }

        public double CenterLongutidue { get; set; }
        public double CenterLatitude { get; set; }
        public double RsrpAvg { get; set; }
        public double SinrAvg { get; set; }
        public double RsrpCount { get; set; }
        public double SinrCount { get; set; }
        public string GridToken { get; set; }
        public GridDataUnit Grid { get; set; }

        public void FillData(ICell cell, FileInfo file, KPIStatDataBase singleStatData)
        {
            Cell = cell;
            File = file;

            int rowIndex;
            int colIndex;
            Grid = new GridDataUnit(singleStatData.LTLng, singleStatData.LTLat);
            GridHelper.GetIndexOfDefaultSizeGrid(Grid.CenterLng, Grid.CenterLat, out rowIndex, out colIndex);
            GridToken = rowIndex.ToString() + "_" + colIndex.ToString();

            CenterLongutidue = Grid.CenterLng;
            CenterLatitude = Grid.CenterLat;

            RsrpCount = singleStatData["61210301", -1];
            RsrpAvg = singleStatData["61210309", -1];
            SinrCount = singleStatData["61210401", -1];
            SinrAvg = singleStatData["61210403", -1];
        }
    }

    public class ZTVillageTestGrid
    {
        public GridDataUnit Grid { get; set; }

        public List<string> TestScenesList { get; set; } = new List<string>();
        public List<string> TestDeviceList { get; set; } = new List<string>();
        public List<string> CarrierList { get; set; } = new List<string>();
        public List<string> FileNameList { get; set; } = new List<string>();
        public List<string> TestDateList { get; set; } = new List<string>();

        public List<string> CGIList { get; set; } = new List<string>();
        public List<int> PciList { get; set; } = new List<int>();
        public List<int> EarfcnList { get; set; } = new List<int>();
        public List<int> ENodebIDList { get; set; } = new List<int>();

        public string TestScenesDesc { get; set; }
        public string TestDeviceDesc { get; set; }
        public string CarrierDesc { get; set; }
        public string FileNameDEsc { get; set; }
        public string TestDateDesc { get; set; }

        public string CGIDesc { get; set; }
        public string PciDesc { get; set; }
        public string EarfcnDesc { get; set; }
        public string ENodebIDDesc { get; set; }

        public double CenterLongutidue { get; set; }
        public double CenterLatitude { get; set; }
        public double RsrpAvg { get; set; }
        public double SinrAvg { get; set; }
        public double RsrpSum { get; set; }
        public double SinrSum { get; set; }
        public double RsrpCount { get; set; }
        public double SinrCount { get; set; }

        public string DistrictName { get; set; }
        public string CountyName { get; set; }

        public void Merge(ZTVillageTestCellGrid cellGrid)
        {
            Grid = cellGrid.Grid;
            LTECell lteCell = cellGrid.Cell as LTECell;
            if (lteCell == null)
            {
                UnknowCell cell = cellGrid.Cell as UnknowCell;
                string cgi = getCGI((int)cell.CI);
                addList(CGIList, cgi);
                int EnodebId = (int)cell.CI / 256;
                addList(ENodebIDList, EnodebId);
            }
            else
            {
                string cgi = getCGI(lteCell.ECI);
                addList(CGIList, cgi);
                addList(PciList, lteCell.PCI);
                addList(EarfcnList, lteCell.EARFCN);
                addList(ENodebIDList, lteCell.BelongBTS.BTSID);
            }

            string[] project = cellGrid.File.ProjectDescription.Split('_');
            if (project.Length == 2)
            {
                string scenes = project[1];
                addList(TestScenesList, scenes);
            }
            addList(TestDeviceList, cellGrid.File.DeviceName);
            addList(CarrierList, cellGrid.File.CarrierTypeDescription);
            addList(FileNameList, cellGrid.File.Name);
            string date = JavaDate.GetDateTimeFromMilliseconds(cellGrid.File.BeginTime * 1000L).ToString("yyyy-MM-dd");
            addList(TestDateList, date);

            RsrpCount += cellGrid.RsrpCount;
            SinrCount += cellGrid.SinrCount;
            RsrpSum += cellGrid.RsrpAvg * RsrpCount;
            SinrSum += cellGrid.SinrAvg * SinrCount;
        }

        private string getCGI(int iEci)
        {
            string strCGI;
            int EnodebId = iEci / 256;
            int CellId = iEci - iEci / 256 * 256;
            strCGI = string.Format("460-00-{0}-{1}", EnodebId, CellId);
            return strCGI;
        }

        private void addList<T>(List<T> list, T data)
        {
            if (!list.Contains(data))
            {
                list.Add(data);
            }
        }

        //public void Merge(ZTVillageTestGrid grid)
        //{
        //    addList(CGIList, grid.CGIList);
        //    addList(PciList, grid.PciList);
        //    addList(EarfcnList, grid.EarfcnList);
        //    addList(ENodebIDList, grid.ENodebIDList);

        //    addList(TestScenesList, grid.TestScenesList);
        //    addList(TestDeviceList, grid.TestDeviceList);
        //    addList(CarrierList, grid.CarrierList);
        //    addList(FileNameList, grid.FileNameList);
        //    addList(TestDateList, grid.TestDateList);

        //    RsrpCount += grid.RsrpCount;
        //    SinrCount += grid.SinrCount;
        //    RsrpSum += grid.RsrpAvg * RsrpCount;
        //    SinrSum += grid.SinrAvg * SinrCount;
        //}

        //private void addList<T>(List<T> list, List<T> dataList)
        //{
        //    foreach (var data in dataList)
        //    {
        //        if (!list.Contains(data))
        //        {
        //            list.Add(data);
        //        }
        //    }
        //}

        public void DealFinalResult()
        {
            TestScenesDesc = getDesc(TestScenesList);
            TestDeviceDesc = getDesc(TestDeviceList);
            CarrierDesc = getDesc(CarrierList);
            FileNameDEsc = getDesc(FileNameList);
            TestDateDesc = getDesc(TestDateList);

            CGIDesc = getDesc(CGIList);
            PciDesc = getDesc(PciList);
            EarfcnDesc = getDesc(EarfcnList);
            ENodebIDDesc = getDesc(ENodebIDList);

            RsrpAvg = RsrpSum / RsrpCount;
            SinrAvg = SinrSum / SinrCount;
        }

        private string getDesc<T>(List<T> list)
        {
            StringBuilder desc = new StringBuilder();
            foreach (var item in list)
            {
                desc.Append(item.ToString());
                desc.Append(";");
            }
            return desc.ToString().TrimEnd(';');
        }
    }

    public class ZTVillageTestResult
    {
        public double CenterLongutidue { get; set; }
        public double CenterLatitude { get; set; }
        public string districtName { get; set; }
        public string CountyName { get; set; }
        public string TestScenes { get; set; }
        public string TestDevice { get; set; }
        public int Carrier { get; set; }
        public string CarrierDesc { get; set; }
        public List<string> FileNameList { get; set; } = new List<string>();
        public string FileNameDEsc { get; set; }
        public List<string> TestDateList { get; set; } = new List<string>();
        public string TestDateDesc { get; set; }
        public List<string> CGIList { get; set; } = new List<string>();
        public string CGIDesc { get; set; }
        public List<int> PciList { get; set; } = new List<int>();
        public string PciDesc { get; set; }
        public List<int> EarfcnList { get; set; } = new List<int>();
        public string EarfcnDesc { get; set; }
        public List<int> ENodebIDList { get; set; } = new List<int>();
        public string ENodebIDDesc { get; set; }
        public double RSRP { get; set; }
        public double SINR { get; set; }

        public void dealFinalResult()
        {
            FileNameDEsc = getDesc(FileNameList);
            TestDateDesc = getDesc(TestDateList);
            CGIDesc = getDesc(CGIList);
            PciDesc = getDesc(PciList);
            EarfcnDesc = getDesc(EarfcnList);
            ENodebIDDesc = getDesc(ENodebIDList);
        }

        public string getDesc<T>(List<T> list)
        {
            StringBuilder desc = new StringBuilder();
            foreach (var item in list)
            {
                desc.Append(item.ToString());
                desc.Append(";");
            }
            return desc.ToString().TrimEnd(';');
        }
    }


}
