﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRStationAcceptQuery : StationAcceptQueryBase
    {
        public NRStationAcceptQuery(MainModel mainModel)
           : base(mainModel)
        {
        }

        public override string Name { get { return "NR宏站验收"; } }

        NRStationSettingDlgConfigModel_XJ curCondition = null;
        protected override bool isValidCondition()
        {
            List<FileInfo> nrFiles = new List<FileInfo>();
            foreach (var file in Condition.FileInfos)
            {
                ServiceName name = ServiceTypeManager.getServiceNameFromTypeID(file.ServiceType);
                if (name == ServiceName.NR)
                {
                    nrFiles.Add(file);
                }
            }
            if (nrFiles.Count == 0)
            {
                MessageBox.Show("请选择5G文件进行单验");
                return false;
            }
            Condition.FileInfos = nrFiles;

            curCondition = NRStationSettingDlgConfig_XJ.Instance.LoadConfig();
            NRStationSettingDlg_XJ dlg = new NRStationSettingDlg_XJ();
            dlg.setCondition(curCondition);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }

            NRStationAcceptCondition cond = new NRStationAcceptCondition();
            cond.Init();
            FolderBrowserDialog folderdlg = new FolderBrowserDialog();
            if (folderdlg.ShowDialog() == DialogResult.OK)
            {
                cond.SaveFolder = folderdlg.SelectedPath;
                initManager(cond);
                return true;
            }
            return false;
        }

        protected override void initManager(StationAcceptConditionBase cond)
        {
            manager = new NRStationAcceptManager();
            manager.SetAcceptCond(cond);
        }

        protected override bool judgeValidFile(FileInfo fileInfo)
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return false;
            }
            return true;
        }

        protected override List<string> queryColumns
        {
            get
            {
                return new List<string>()
                {
                     "isampleid",
                     "itime",
                     "ilongitude",
                     "ilatitude",
                     "NR_SSB_ARFCN",
                     "NR_PCI",
                     "NR_SS_RSRP",
                     "NR_SS_SINR",
                     "NR_Throughput_MAC_DL",
                     "NR_Throughput_MAC_UL",
                     "NR_APP_type",
                     "NR_APP_Status",

                     "NR_lte_TAC",
                     "NR_lte_ECI",
                     "NR_lte_EARFCN",
                     "NR_lte_PCI",
                     "NR_lte_RSRP",
                     "NR_lte_RSRQ",
                     "NR_lte_RSSI",
                     "NR_lte_SINR",
                };
            }
        }
    }

    public class NRStationAcceptCondition : StationAcceptConditionBase
    {
        public NRServiceName NRServiceType { get; set; }
        public NRThroughputStandard Standard { get; protected set; }

        public virtual void Init()
        {
            Standard = new NRThroughputStandard();
            Standard.Init();
        }
    }

    public class NRThroughputStandard
    {
        public virtual void Init()
        {
            ThroughputStandardList = new List<ThroughputStandard>()
            {
                new ThroughputStandard(NRServiceName.SA, 60, 4,
                  new ThroughputStandard.DataInfo( 156, 60, 18),
                  new ThroughputStandard.DataInfo(30, 18, 0.5)),
                new ThroughputStandard(NRServiceName.SA, 60, 8,
                  new ThroughputStandard.DataInfo( 240, 90, 27),
                  new ThroughputStandard.DataInfo(36, 21, 0.6)),
                new ThroughputStandard(NRServiceName.SA, 60, 32,
                  new ThroughputStandard.DataInfo( 480, 180, 54),
                  new ThroughputStandard.DataInfo(72, 42, 0.6)),
                new ThroughputStandard(NRServiceName.SA, 60, 64,
                  new ThroughputStandard.DataInfo( 480, 180, 54),
                  new ThroughputStandard.DataInfo(72, 42, 0.6)),
                //new ThroughputStandard(NRServiceName.SA, 60, 64,
                //  new ThroughputStandard.DataInfo( 408, 150, 45),
                //  new ThroughputStandard.DataInfo(108, 63, 0.9)),

                new ThroughputStandard(NRServiceName.SA, 100, 4,
                  new ThroughputStandard.DataInfo( 260, 100, 30),
                  new ThroughputStandard.DataInfo(50, 30, 0.8)),
                new ThroughputStandard(NRServiceName.SA, 100, 8,
                  new ThroughputStandard.DataInfo( 400, 150, 45),
                  new ThroughputStandard.DataInfo(60, 35, 1)),
                new ThroughputStandard(NRServiceName.SA, 100, 32,
                  new ThroughputStandard.DataInfo( 800, 300, 90),
                  new ThroughputStandard.DataInfo(120, 70, 1)),
                new ThroughputStandard(NRServiceName.SA, 100, 64,
                  new ThroughputStandard.DataInfo( 800, 300, 90),
                  new ThroughputStandard.DataInfo(120, 70, 1)),
                //new ThroughputStandard(NRServiceName.SA, 100, 64,
                //  new ThroughputStandard.DataInfo( 680, 250, 75),
                //  new ThroughputStandard.DataInfo(180, 105, 1.5)),

                new ThroughputStandard(NRServiceName.NSA, 60, 4,
                  new ThroughputStandard.DataInfo( 156, 60, 18),
                  new ThroughputStandard.DataInfo(18, 11, 0.5)),
                new ThroughputStandard(NRServiceName.NSA, 60, 8,
                  new ThroughputStandard.DataInfo( 240, 90, 27),
                  new ThroughputStandard.DataInfo(21, 12, 0.6)),
                new ThroughputStandard(NRServiceName.NSA, 60, 32,
                  new ThroughputStandard.DataInfo( 480, 180, 54),
                  new ThroughputStandard.DataInfo(42, 24, 0.6)),
                new ThroughputStandard(NRServiceName.NSA, 60, 64,
                  new ThroughputStandard.DataInfo( 480, 180, 54),
                  new ThroughputStandard.DataInfo(42, 24, 0.6)),
                //new ThroughputStandard(NRServiceName.NSA, 60, 64,
                //  new ThroughputStandard.DataInfo( 408, 150, 45),
                //  new ThroughputStandard.DataInfo(60, 36, 0.9)),

                new ThroughputStandard(NRServiceName.NSA, 100, 4,
                  new ThroughputStandard.DataInfo( 260, 100, 30),
                  new ThroughputStandard.DataInfo(30, 18, 0.8)),
                new ThroughputStandard(NRServiceName.NSA, 100, 8,
                  new ThroughputStandard.DataInfo( 400, 150, 45),
                  new ThroughputStandard.DataInfo(35, 20, 1)),
                new ThroughputStandard(NRServiceName.NSA, 100, 32,
                  new ThroughputStandard.DataInfo( 800, 300, 90),
                  new ThroughputStandard.DataInfo(70, 40, 1)),
                new ThroughputStandard(NRServiceName.NSA, 100, 64,
                  new ThroughputStandard.DataInfo( 800, 300, 90),
                  new ThroughputStandard.DataInfo(70, 40, 1)),
                //new ThroughputStandard(NRServiceName.NSA, 100, 64,
                //  new ThroughputStandard.DataInfo( 680, 250, 75),
                //  new ThroughputStandard.DataInfo(100, 60, 1.5)),
            };
        }

        public List<ThroughputStandard> ThroughputStandardList { get; protected set; }

        public bool JudgeValidThroughput(NRServiceName serviceType, int bandWidth, int channels, bool isDown, ThroughputStandard.PointType pointType, double throughput)
        {
            string token = $"{serviceType}_{bandWidth}_{channels}";
            ThroughputStandard cur = ThroughputStandardList.Find(x => x.Token == token);
            bool isValid = false;
            if (cur != null)
            {
                if (isDown)
                {
                    isValid = cur.DownThroughput.JudgeThroughput(pointType, throughput);
                }
                else
                {
                    isValid = cur.UpThroughput.JudgeThroughput(pointType, throughput);
                }
            }
            return isValid;
        }
    }

    public class ThroughputStandard
    {
        public NRServiceName ServiceType { get; private set; }
        public int BandWidth { get; private set; }
        public int Channels { get; private set; }
        public DataInfo DownThroughput { get; private set; }
        public DataInfo UpThroughput { get; private set; }

        public string Token { get; set; }

        public ThroughputStandard(NRServiceName serviceType, int bandWidth, int channels, DataInfo down, DataInfo up)
        {
            ServiceType = serviceType;
            BandWidth = bandWidth;
            Channels = channels;
            DownThroughput = down;
            UpThroughput = up;

            Token = $"{serviceType}_{bandWidth}_{channels}";
        }

        public class DataInfo
        {
            public double GoodPointThroughput { get; private set; }
            public double MiddlePointThroughput { get; private set; }
            public double BadPointThroughput { get; private set; }

            public DataInfo(double good, double middle, double bad)
            {
                GoodPointThroughput = good;
                MiddlePointThroughput = middle;
                BadPointThroughput = bad;
            }

            public bool JudgeThroughput(PointType pointType, double throughput)
            {
                double standard;
                if (pointType == PointType.Good)
                {
                    standard = GoodPointThroughput;
                }
                else if (pointType == PointType.Middle)
                {
                    standard = MiddlePointThroughput;
                }
                else if (pointType == PointType.Bad)
                {
                    standard = BadPointThroughput;
                }
                else
                {
                    return false;
                }

                if (throughput < standard)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
        }

        public enum PointType
        {
            Good,
            Middle,
            Bad
        }
    }
}
