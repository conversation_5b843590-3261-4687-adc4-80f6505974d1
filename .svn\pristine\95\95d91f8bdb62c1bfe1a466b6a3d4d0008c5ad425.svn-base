﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using System.Threading;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class PCIOptimizeManager
    {
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private readonly int cur_iFRM = 1;
        private readonly int cur_iFlag = 12;      //TD = 1,PCI =12 
        
        private int cur_sessionID = 0;  //当前状态
        private bool startBool = true;  //循环接受数据时，判断是否结束

        public EOptType OptType { get; set; }
        public string reporttime { get; set; }

        private TimePeriod tPeriod;
        private List<CityInfo> cityInfoList = null;
        private readonly List<LTECell_FSO> cellFSOLst;
        private Dictionary<string, LTECell> allNameCellDic;
        private List<Rule> ruleList = null;
        private readonly Dictionary<string, LTECell_FSO> lacciCellDic;
        private Dictionary<string, LTECell_FSO> nameLTECellDIc = null;

        private List<ArrangeResult> retCellList;
        
        public CityInfo CurCity { get; set; }
        public List<LTECell> RegionCells { get; set; }
        public List<LTECell> CheckedCells { get; set; }

        public int Iteration { get; set; }
        public int PopularSize { get; set; }
        public int CrossRate { get; set; }
        public int VarRate { get; set; }
        public int ThreadNum { get; set; }

        public PCIOptimizeManager()
        {
            cellFSOLst = new List<LTECell_FSO>();
            lacciCellDic = new Dictionary<string, LTECell_FSO>();
            allNameCellDic = new Dictionary<string, LTECell>();
            retCellList = new List<ArrangeResult>();

            RegionCells = new List<LTECell>();
            CheckedCells = new List<LTECell>();

            OptType = EOptType.Cmd;
            reporttime = string.Empty;

            Iteration = 200;	// 最大迭代次数，iFlag=1时有效
            PopularSize = 100;	// 种群大小,默认25
            CrossRate = 20;	// 交叉率 1~99,默认20
            VarRate = 10;		// 变异率 1~99,默认10
            ThreadNum = 1;		// 工作线程数 1~64,默认1
        }

        public List<CityInfo> CityInfoList
        {
            get
            {
                if (cityInfoList == null)
                {
                    DiyQueryCityInfoFixDB query = new DiyQueryCityInfoFixDB();
                    query.Query();
                    cityInfoList = query.CityInfoList;
                }
                return cityInfoList;
            }
        }

        public void ClearData()
        {
            retCellList.Clear();
        }

        private List<Rule> getRuleLst()
        {
            DiyQueryRuleFixDB query = new DiyQueryRuleFixDB(CurCity);
            query.Query();
            return query.RuleList;
        }

        private Dictionary<string, LTECell_FSO> getLTECellLst()
        {
            DiyQueryLTECellFixDB query = new DiyQueryLTECellFixDB(CurCity);
            query.Query();
            return query.NameLTECellDic;
        }

        public bool InitCityBaseInfo()
        {
            this.ruleList = getRuleLst();
            this.nameLTECellDIc = getLTECellLst();
            foreach (LTECell_FSO cell in nameLTECellDIc.Values)
            {
                StringBuilder sb;
                if (cell.Check(out sb) && !lacciCellDic.ContainsKey(cell.enodeb_id.ToString() + "_" + cell.cellid.ToString()))
                {
                    lacciCellDic.Add(cell.enodeb_id.ToString() + "_" + cell.cellid.ToString(), cell);
                }
            }
            return ruleList.Count > 0 && nameLTECellDIc.Count > 0;
        }

        public void SetAllNameCellDic(Dictionary<string, LTECell> allNameCellDic)
        {
            this.allNameCellDic = allNameCellDic;
        }

        public void SetTimePeriod(TimePeriod time)
        {
            this.tPeriod = time;
        }

        private bool cmpCell()
        {
            cellFSOLst.Clear();
            foreach (LTECell cell in CheckedCells)
            {
                if (nameLTECellDIc.ContainsKey(cell.Name))
                {
                    StringBuilder sb;
                    if (!nameLTECellDIc[cell.Name].Check(out sb))
                    {
                        log.Info(string.Format("{0} 小区非法,被跳过: {1}", cell.Name, sb.ToString()));
                        continue;
                    }
                    cellFSOLst.Add(nameLTECellDIc[cell.Name]);
                }
            }
            return cellFSOLst.Count > 0;
        }

        public void DoOptimize(LtePCIOptimizeNewSettingDlg settingForm)
        {
            try
            {
                if (!InitCityBaseInfo())
                {
                    MessageBox.Show(string.Format("地市：{0} 规则表为空或者小区表为空,请检查数据库表数据", CurCity.servername));
                    return;
                }
                if (!cmpCell())
                {
                    MessageBox.Show("未匹配到小区");
                    return;
                }

                switch (OptType)
                {
                    case EOptType.Cmd:
                        doOptimizeByCmd(settingForm);
                        break;
                    case EOptType.Background:
                        doOptimizeByBg();
                        break;
                    default:
                        break;
                }
            }
            catch(Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        private void doOptimizeByCmd(LtePCIOptimizeNewSettingDlg settingForm)
        {
            retCellList.Clear();
            settingForm.GetbgWorker().ReportProgress(0, "正在进行干扰评估...");
            List<EvalReqParam> paramList = calcCellFreqEvalOrig(CurCity, ruleList, cellFSOLst, cur_iFRM, tPeriod);
            if (paramList != null && paramList.Count > 0)
            {
                //获取数据填充到dataGridView中
            }
            else
            {
                MessageBox.Show("没有评估结果返回...");
                return;
            }

            settingForm.GetbgWorker().ReportProgress(0, "正在进行PCI优化...");
            FreqArrangeStart freqArrangeStart = QueryAutoFreqArrangeStart(CurCity, ruleList, cellFSOLst, cur_iFRM, tPeriod);
            if (freqArrangeStart != null && freqArrangeStart.Status == 0)//成功
            {
                dealPCIOptimization(settingForm, freqArrangeStart);
                startBool = true;
            }
            else
            {
                MessageBox.Show("排频命令生成失败!");
            }
        }

        private void dealPCIOptimization(LtePCIOptimizeNewSettingDlg settingForm, FreqArrangeStart freqArrangeStart)
        {
            cur_sessionID = freqArrangeStart.Session;
            DateTime dtBegin = DateTime.Now;
            FreqArrange freqArrange = null;
            //增加处理，比如，清掉曲线图等等
            while (startBool)
            {
                if (settingForm.GetbgWorker().CancellationPending)
                {
                    settingForm.GetbgWorker().ReportProgress(100, "正在终止PCI优化...");
                    freqArrange = QueryAutoFreqArrangePause(cur_sessionID, CurCity);
                    setRetCellList(freqArrange);
                    break;
                }
                freqArrange = QueryAutoFreqArrangeContinue(cur_sessionID, CurCity);
                if (freqArrange != null)
                {
                    setSettingFormInfo(settingForm, dtBegin, freqArrange);
                }
                else
                {
                    settingForm.GetbgWorker().ReportProgress(100, "通信异常，正在终止PCI优化...");
                    freqArrange = QueryAutoFreqArrangePause(cur_sessionID, CurCity);
                    setRetCellList(freqArrange);
                    break;
                }
            }
        }

        private void setRetCellList(FreqArrange freqArrange)
        {
            if (freqArrange != null)
            {
                retCellList = freqArrange.RetCellList;
                startBool = false;
            }
        }

        private void setSettingFormInfo(LtePCIOptimizeNewSettingDlg settingForm, DateTime dtBegin, FreqArrange freqArrange)
        {
            if (freqArrange.Rate == 100)
            {
                retCellList = freqArrange.RetCellList;
                startBool = false;
            }
            else
            {
                double pctRate = freqArrange.Rate;
                int pctAveEvalue = freqArrange.AveEvalue;
                int pctBestEvalue = freqArrange.BestEvalue;
                // 可以采用上面的这些参数，做一个动态的曲线图
                double eval = 0.01 * pctAveEvalue;
                double bval = 0.01 * pctBestEvalue;
                if (pctRate > 0 && pctRate <= 100)
                {
                    TimeSpan ts = DateTime.Now.Subtract(dtBegin);
                    ReportInfo ri = new ReportInfo((int)ts.TotalSeconds, pctRate, eval, bval);
                    settingForm.GetbgWorker().ReportProgress((int)pctRate, ri);
                    settingForm.AddPoint(ri);
                }
            }
        }

        private void doOptimizeByBg()
        {
            List<string> cellNameLst = new List<string>();
            foreach (LTECell cell in CheckedCells)
            {
                cellNameLst.Add(cell.Name);
            }
            DiyQuerySINRByTimeFixDB query = new DiyQuerySINRByTimeFixDB(CurCity, reporttime, cellNameLst);
            query.Query();

            retCellList = new List<ArrangeResult>();
            Dictionary<string, Dictionary<string, SINRToPCI>> retCellPntSinrDic = query.CellPntSinrDic;
            SearchFileByFileIDs search = new SearchFileByFileIDs(MainModel.GetInstance(), query.FileIDLst);
            search.Query();

            foreach (string cellName in retCellPntSinrDic.Keys)
            {
                LTECell cell;
                if (!allNameCellDic.TryGetValue(cellName, out cell))
                    continue;
                ArrangeResult ar = new ArrangeResult();
                ar.sectorid = cell.CellID;
                ar.lac = cell.TAC;
                ar.ci = cell.ECI;
                ar.cellname = cell.Name;
                ar.orig_iUtranFreq = cell.EARFCN;
                ar.orig_iScrambleCode = cell.PCI;

                setSINRToPCIInfo(retCellPntSinrDic, search, cellName, cell, ar);
                retCellList.Add(ar);
            }
        }

        private void setSINRToPCIInfo(Dictionary<string, Dictionary<string, SINRToPCI>> retCellPntSinrDic, 
            SearchFileByFileIDs search, string cellName, LTECell cell, ArrangeResult ar)
        {
            bool first = true;
            foreach (SINRToPCI sinr in retCellPntSinrDic[cellName].Values)
            {
                if (first)
                {
                    int newPci;
                    if (int.TryParse(sinr.iScrambleCode, out newPci))
                    {
                        ar.iScrambleCode = newPci;
                        cell.NewPCI = sinr.iScrambleCode;
                    }
                    first = false;
                }
                sinr.FinalDeal(allNameCellDic, ar.orig_iScrambleCode.ToString(), sinr.iScrambleCode);
                ar.SinrList.Add(sinr);
                if (search.IdNameDic.ContainsKey(sinr.fileid))
                    sinr.FileName = search.IdNameDic[sinr.fileid];
                else
                    sinr.FileName = sinr.fileid.ToString();
            }
        }

        public void DoOhterAfterOptimize()
        {
            if (OptType == EOptType.Background)
                return;

            List<string> cellNameLst = new List<string>();
            foreach (ArrangeResult aresult in retCellList)
            {
                aresult.curCity = CurCity;
                if (allNameCellDic.ContainsKey(aresult.cellname))
                {
                    aresult.mainCell = allNameCellDic[aresult.cellname];

                    aresult.mainCell.NewPCI = aresult.iScrambleCode.ToString();
                    if (!MainModel.GetInstance().ShowPCICells.Contains(aresult.mainCell))
                        MainModel.GetInstance().ShowPCICells.Add(aresult.mainCell);
                }
                cellNameLst.Add(aresult.cellname);
            }
            DiyQueryAllLTESINRFixDB query = new DiyQueryAllLTESINRFixDB(CurCity, cellNameLst);
            query.Query();
            Dictionary<string, Dictionary<string, SINRToPCI>> retCellPntSinrDic = query.CellPntSinrDic;
            SearchFileByFileIDs search = new SearchFileByFileIDs(MainModel.GetInstance(), query.FileIDLst);
            search.Query();
            foreach (ArrangeResult aresult in retCellList)
            {
                addArrangeResult(retCellPntSinrDic, search, aresult);
            }
        }

        private void addArrangeResult(Dictionary<string, Dictionary<string, SINRToPCI>> retCellPntSinrDic, SearchFileByFileIDs search, ArrangeResult aresult)
        {
            if (retCellPntSinrDic.ContainsKey(aresult.cellname))
            {
                foreach (KeyValuePair<string, SINRToPCI> pair in retCellPntSinrDic[aresult.cellname])
                {
                    pair.Value.FinalDeal(allNameCellDic, aresult.orig_iScrambleCode.ToString(), aresult.iScrambleCode.ToString());
                    aresult.SinrList.Add(pair.Value);
                }
                foreach (SINRToPCI sinr in aresult.SinrList)
                {
                    if (search.IdNameDic.ContainsKey(sinr.fileid))
                        sinr.FileName = search.IdNameDic[sinr.fileid];
                    else
                        sinr.FileName = sinr.fileid.ToString();
                }
            }
        }

        //做干扰评估
        private List<EvalReqParam> calcCellFreqEvalOrig(CityInfo curCity, List<Rule> ruleLst, List<LTECell_FSO> cellLst, int cur_iFRM, TimePeriod tPeriod)
        {
            Dictionary<string, EvalReqParam> evalDic = getEvalDic(cellLst);

            InnerProcClientProxy clientProxy = new InnerProcClientProxy(curCity.CServerIP, Convert.ToInt32(curCity.serverport));
            if (!clientProxy.connect())
            {
                clientProxy = new InnerProcClientProxy(curCity.serverip, Convert.ToInt32(curCity.serverport));
                if (!clientProxy.connect())
                {
                    MessageBox.Show("连接服务器失败或服务器正在排频...");
                    return new List<EvalReqParam>();
                }
            }
            try
            {
                getSetParam(curCity, ruleLst, cur_iFRM, tPeriod, evalDic, clientProxy);
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    fillData(evalDic, clientProxy);
                    if (clientProxy.Package.EndFlag)
                    {
                        break;
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + "\r\n" + e.StackTrace);
                return new List<EvalReqParam>();
            }
            finally
            {
                clientProxy.Close();
            }
            List<EvalReqParam> paramList = new List<EvalReqParam>();
            foreach (EvalReqParam erp in evalDic.Values)
            {
                paramList.Add(erp);
            }
            return paramList;
        }

        private static Dictionary<string, EvalReqParam> getEvalDic(List<LTECell_FSO> cellLst)
        {
            Dictionary<string, EvalReqParam> evalDic = new Dictionary<string, EvalReqParam>();
            foreach (LTECell_FSO cell in cellLst)
            {
                EvalReqParam eval = new EvalReqParam();
                eval.cellname = cell.strcellname;
                eval.cellid = cell.iid;
                eval.lac = (int)cell.tac;
                eval.ci = (int)cell.cellid;
                eval.freq = (int)cell.ifreq;
                eval.cpi = (int)cell.ipci;//icpi===cpi
                string strfreqlist = "";
                string Strfreqlist = cell.ifreq.ToString() + "," + cell.ipci.ToString();
                if (Strfreqlist != "" && Strfreqlist.Length > 1)
                {
                    string[] temparr = Strfreqlist.Split(',');
                    StringBuilder sb = new StringBuilder();
                    foreach (string freq in temparr)
                    {
                        if (freq != "" && freq.Length > 1 && freq != cell.ifreq.ToString())
                        {
                            sb.Append(freq + ",");
                        }
                    }
                    strfreqlist = sb.ToString();
                }
                if (strfreqlist.Length > 1)
                {
                    strfreqlist = strfreqlist.Substring(0, strfreqlist.Length - 1);
                }
                eval.orig_sAssFreqList = strfreqlist;
                evalDic[cell.enodeb_id + "_" + cell.cellid] = eval;
            }

            return evalDic;
        }

        private static void getSetParam(CityInfo curCity, List<Rule> ruleLst, int cur_iFRM, TimePeriod tPeriod, Dictionary<string, EvalReqParam> evalDic, InnerProcClientProxy clientProxy)
        {
            clientProxy.Package.CmdType = LTECmdTypeDef.Freq_Eval;
            clientProxy.Package.Content.PrepareAddParam();
            clientProxy.Package.Content.AddParam(curCity.GetFiaDBConn(), 256);
            clientProxy.Package.Content.AddParam(tPeriod.IBeginTime);
            clientProxy.Package.Content.AddParam(tPeriod.IEndTime);
            clientProxy.Package.Content.AddParam(0x0700 | cur_iFRM);//扰码相关性
            clientProxy.Package.Content.AddParam(evalDic.Count);
            clientProxy.Package.Content.AddParam(ruleLst.Count);
            foreach (EvalReqParam reqParam in evalDic.Values)
            {
                clientProxy.Package.Content.AddParam(reqParam.lac);
                clientProxy.Package.Content.AddParam(reqParam.ci);
                clientProxy.Package.Content.AddParam(reqParam.freq);
                clientProxy.Package.Content.AddParam(reqParam.cpi);
                clientProxy.Package.Content.AddParam(reqParam.orig_sAssFreqList, 128);//原来辅频
            }
            foreach (Rule ruleParam in ruleLst)
            {
                clientProxy.Package.Content.AddParam(ruleParam.id);
                clientProxy.Package.Content.AddParam(ruleParam.type);
            }
        }

        private static void fillData(Dictionary<string, EvalReqParam> evalDic, InnerProcClientProxy clientProxy)
        {
            uint totalLen = clientProxy.Package.Content.Length;
            if (totalLen != 24 + 128 + 128) //**+orig_sAssFreqList+old_rule
            {
                throw (new Exception(string.Format("Pack Invalid totalLen length not valid ,!=24, recv length = {0}", totalLen)));
            }
            int lac = clientProxy.Package.Content.GetParamInt();
            int ci = clientProxy.Package.Content.GetParamInt();
            int evalvalue = clientProxy.Package.Content.GetParamInt();
            clientProxy.Package.Content.GetParamInt();//iTightEvalue
            clientProxy.Package.Content.GetParamInt();//iRelaEvalue
            clientProxy.Package.Content.GetParamInt();//iAffectCellCount
            string orig_sAssFreqList = clientProxy.Package.Content.GetParamString(128);
            string old_rule = clientProxy.Package.Content.GetParamString(128);//原违反约束

            EvalReqParam evalParam = null;
            if (evalDic.TryGetValue(lac + "_" + ci, out evalParam))
            {
                evalParam.evalresult = evalvalue;
                evalParam.orig_sAssFreqList = orig_sAssFreqList;
                evalParam.old_rule = old_rule;
            }
        }

        //生成自动排频任务
        private FreqArrangeStart QueryAutoFreqArrangeStart(CityInfo curCity, List<Rule> ruleLst, List<LTECell_FSO> cellLst, int cur_iFRM, TimePeriod tPeriod)
        {
            FreqArrangeStart freqArrangestart = new FreqArrangeStart();

            InnerProcClientProxy clientProxy = new InnerProcClientProxy(curCity.CServerIP, Convert.ToInt32(curCity.serverport));
            if (!clientProxy.connect())
            {
                MessageBox.Show("连接服务器失败或服务器正在排频...");
                return null;
            }
            try
            {
                addParam(curCity, ruleLst, cellLst, cur_iFRM, tPeriod, clientProxy);
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    uint totalLen = clientProxy.Package.Content.Length;
                    if (totalLen != 24)
                    {
                        throw (new Exception(string.Format("Pack Invalid totalLen length not valid ,!=24, recv length = {0}", totalLen)));
                    }
                    else
                    {
                        clientProxy.Package.Content.GetParamInt();           // 0/1：穷举/迭代命令响应，3：穷举进度，4：迭代进度retiFlag
                        int retiStatus = clientProxy.Package.Content.GetParamInt();         // 0：成功，1：错误
                        int retiSession = clientProxy.Package.Content.GetParamInt();        // 后台程序分配的此次会话ID，用于标识一次计算任务
                        clientProxy.Package.Content.GetParamInt();           // 进度，范围[0,100]，iFlag=3/4有效retiRate
                        clientProxy.Package.Content.GetParamInt();     // 当前最优值，iFlag=3/4有效retiBestEvalue
                        clientProxy.Package.Content.GetParamInt();      // 当前平均值，iFlag=4有效retiAveEvalue
                        freqArrangestart.Status = retiStatus;
                        freqArrangestart.Session = retiSession;
                        return freqArrangestart;
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + "\r\n" + e.StackTrace);
                return null;
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void addParam(CityInfo curCity, List<Rule> ruleLst, List<LTECell_FSO> cellLst, int cur_iFRM, TimePeriod tPeriod, InnerProcClientProxy clientProxy)
        {
            clientProxy.Package.CmdType = LTECmdTypeDef.Freq_Arrange;
            clientProxy.Package.Content.PrepareAddParam();
            clientProxy.Package.Content.AddParam(curCity.GetFiaDBConn(), 256);
            clientProxy.Package.Content.AddParam(tPeriod.IBeginTime);
            clientProxy.Package.Content.AddParam(tPeriod.IEndTime);
            clientProxy.Package.Content.AddParam(0x0700 | cur_iFRM);
            clientProxy.Package.Content.AddParam(cur_iFlag);
            clientProxy.Package.Content.AddParam(0);//SessionID 
            clientProxy.Package.Content.AddParam(Iteration);
            clientProxy.Package.Content.AddParam(PopularSize);
            clientProxy.Package.Content.AddParam(CrossRate);
            clientProxy.Package.Content.AddParam(VarRate);
            clientProxy.Package.Content.AddParam(ThreadNum);
            clientProxy.Package.Content.AddParam(cellLst.Count);
            clientProxy.Package.Content.AddParam(ruleLst.Count);
            for (int x = 0; x < cellLst.Count; x++)
            {
                LTECell_FSO cell = cellLst[x];
                clientProxy.Package.Content.AddParam(cell.enodeb_id);
                clientProxy.Package.Content.AddParam(cell.cellid);
            }
            foreach (Rule rule in ruleLst)
            {
                clientProxy.Package.Content.AddParam(rule.id);
                clientProxy.Package.Content.AddParam(rule.type);
            }
        }

        public FreqArrange QueryAutoFreqArrangeContinue(int Session, CityInfo curCity)
        {
            int stime = 0;
            int etime = 0;
            int itrCount = 0;
            FreqArrange freqArrange = new FreqArrange();
            InnerProcClientProxy clientProxy = new InnerProcClientProxy(curCity.CServerIP, Convert.ToInt32(curCity.serverport));
            if (!clientProxy.connect())
            {
                MessageBox.Show("连接服务器失败或服务器正在排频...");
                return null;
            }
            try
            {
                clientProxy.Package.CmdType = LTECmdTypeDef.Freq_Arrange;
                clientProxy.Package.Content.PrepareAddParam();
                clientProxy.Package.Content.AddParam(curCity.GetFiaDBConn(), 256);
                clientProxy.Package.Content.AddParam(stime);
                clientProxy.Package.Content.AddParam(etime);
                clientProxy.Package.Content.AddParam(0x0701);//空位的扰码相关性
                clientProxy.Package.Content.AddParam(2);//2：查询进度
                clientProxy.Package.Content.AddParam(Session);//SessionID 
                clientProxy.Package.Content.AddParam(itrCount);
                clientProxy.Package.Content.AddParam(1);
                clientProxy.Package.Content.AddParam(0);
                clientProxy.Package.Content.AddParam(0);
                clientProxy.Package.Content.AddParam(0);//rule
                clientProxy.Send();

                List<ArrangeResult> retArrangeResultList = getRetArrangeResultList(curCity, freqArrange, clientProxy);
                freqArrange.RetCellList = retArrangeResultList;
                return freqArrange;
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + "\r\n" + e.StackTrace);
                return null;
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private List<ArrangeResult> getRetArrangeResultList(CityInfo curCity, FreqArrange freqArrange, InnerProcClientProxy clientProxy)
        {
            List<ArrangeResult> retArrangeResultList = new List<ArrangeResult>();
            while (true)
            {
                clientProxy.Recieve();
                if (clientProxy.Package.CmdType == LTECmdTypeDef.Rcv_Freq_ArrangeProg)
                {
                    int retiRate = getArrangeProg(freqArrange, clientProxy);

                    if (retiRate != 10000)//原来为100，现在*100，能看到小数的进度
                    {
                        break;
                    }
                    else
                    {
                        //继续收
                    }
                }
                else if (clientProxy.Package.CmdType == LTECmdTypeDef.Rcv_Freq_ArrangeResult)//====
                {
                    getArrangeResult(curCity, clientProxy, retArrangeResultList);
                    if (clientProxy.Package.EndFlag)
                    {
                        break;
                    }
                }
            }

            return retArrangeResultList;
        }

        private static int getArrangeProg(FreqArrange freqArrange, InnerProcClientProxy clientProxy)
        {
            uint totalLen = clientProxy.Package.Content.Length;
            if (totalLen != 24)
            {
                throw (new Exception(string.Format("Pack Invalid totalLen length not valid ,!=24, recv length = {0}", totalLen)));
            }
            clientProxy.Package.Content.GetParamInt();           // 0/1：穷举/迭代命令响应，3：穷举进度，4：迭代进度retiFlag
            int retiStatus = clientProxy.Package.Content.GetParamInt();         // 0：成功，1：错误
            int retiSession = clientProxy.Package.Content.GetParamInt();        // 后台程序分配的此次会话ID，用于标识一次计算任务
            int retiRate = clientProxy.Package.Content.GetParamInt();           // 进度，范围[0,100]，iFlag=3/4有效
            int retiBestEvalue = clientProxy.Package.Content.GetParamInt();     // 当前最优值，iFlag=3/4有效
            int retiAveEvalue = clientProxy.Package.Content.GetParamInt();      // 当前平均值，iFlag=4有效

            freqArrange.Status = retiStatus;
            freqArrange.Session = retiSession;
            freqArrange.AveEvalue = retiAveEvalue;
            freqArrange.BestEvalue = retiBestEvalue;//最优值
            freqArrange.Rate = Math.Round(1.0 * retiRate / 100, 2);//比例
            return retiRate;
        }

        private void getArrangeResult(CityInfo curCity, InnerProcClientProxy clientProxy, List<ArrangeResult> retArrangeResultList)
        {
            uint totalLen = clientProxy.Package.Content.Length;
            ushort eachPackCount = 168 + 128 + 128;
            if (totalLen % eachPackCount != 0)
            {
                throw (new Exception(string.Format("Pack Invalid totalLen length not valid ,%!=0, recv length % eachPack length -> {0}%{1} !=0", totalLen, eachPackCount)));
            }
            uint count = totalLen / eachPackCount;
            for (int i = 0; i < count; i++)
            {
                int lac = clientProxy.Package.Content.GetParamInt();
                int ci = clientProxy.Package.Content.GetParamInt();
                int iUtranFreq = clientProxy.Package.Content.GetParamInt();
                int iHFreq = clientProxy.Package.Content.GetParamInt();
                int iR4Freq = clientProxy.Package.Content.GetParamInt();
                int iScrambleCode = clientProxy.Package.Content.GetParamInt();
                int iDisterbEvalue = clientProxy.Package.Content.GetParamInt();
                clientProxy.Package.Content.GetParamInt();//iTightEvalue
                clientProxy.Package.Content.GetParamInt();//iRelaEvalue
                clientProxy.Package.Content.GetParamInt();//iAffectCellCount
                string sAssFreqList = clientProxy.Package.Content.GetParamString(128);//ChangedXXXX
                string sAssFreqList24 = clientProxy.Package.Content.GetParamString(128);//ChangedXXXX
                string sAssRule = clientProxy.Package.Content.GetParamString(128);
                LTECell_FSO cell = null;
                if (lacciCellDic.ContainsKey(lac.ToString() + "_" + ci.ToString()))
                {
                    cell = lacciCellDic[lac.ToString() + "_" + ci.ToString()];
                }
                if (cell != null)
                {
                    ArrangeResult arrResult = new ArrangeResult();
                    arrResult.sectorid = (int)cell.cellid;
                    arrResult.lac = lac;
                    arrResult.ci = ci;
                    arrResult.cellname = cell.strcellname;
                    arrResult.orig_iUtranFreq = (int)cell.ifreq;
                    arrResult.orig_iScrambleCode = (int)cell.ipci;
                    arrResult.iUtranFreq = iUtranFreq;
                    arrResult.iHFreq = iHFreq;
                    arrResult.iR4Freq = iR4Freq;
                    arrResult.iScrambleCode = iScrambleCode;
                    arrResult.iDisterbEvalue = iDisterbEvalue;
                    arrResult.sAssFreqList = sAssFreqList;
                    arrResult.sAssFreqList24 = sAssFreqList24;
                    arrResult.sAssRule = sAssRule;
                    arrResult.curCity = curCity;
                    retArrangeResultList.Add(arrResult);
                }
            }
        }

        //中断/完成任务结束
        public FreqArrange QueryAutoFreqArrangePause(int Session, CityInfo curCity)
        {
            int stime = 0;
            int etime = 0;
            int itrCount = 0;
            FreqArrange freqArrange = new FreqArrange();
            InnerProcClientProxy clientProxy = new InnerProcClientProxy(curCity.CServerIP, Convert.ToInt32(curCity.serverport));
            if (!clientProxy.connect())
            {
                MessageBox.Show("连接服务器失败或服务器正在排频...");
                return null;
            }
            try
            {
                clientProxy.Package.CmdType = LTECmdTypeDef.Freq_Arrange;
                clientProxy.Package.Content.PrepareAddParam();
                clientProxy.Package.Content.AddParam(curCity.GetFiaDBConn(), 256);
                clientProxy.Package.Content.AddParam(stime);
                clientProxy.Package.Content.AddParam(etime);
                clientProxy.Package.Content.AddParam(0x0701);//空值得扰码相关性，不保证为空即可
                clientProxy.Package.Content.AddParam(3);//3：停止
                clientProxy.Package.Content.AddParam(Session);//SessionID 
                clientProxy.Package.Content.AddParam(itrCount);
                clientProxy.Package.Content.AddParam(1);
                clientProxy.Package.Content.AddParam(0);
                clientProxy.Package.Content.AddParam(0);
                clientProxy.Package.Content.AddParam(0);//rule
                clientProxy.Send();
                List<ArrangeResult> retArrangeResultList = new List<ArrangeResult>();
                while (true)
                {
                    clientProxy.Recieve();
                    if (clientProxy.Package.CmdType == TDCmdTypeDef.Rcv_Freq_ArrangeResult)//===
                    {
                        getFreqArrangePauseResult(clientProxy, retArrangeResultList);
                        if (clientProxy.Package.EndFlag)
                        {
                            break;
                        }
                    }
                }
                freqArrange.Status = 0;
                freqArrange.Session = Session;
                freqArrange.AveEvalue = 999;//not used
                freqArrange.BestEvalue = 999;//not used
                freqArrange.Rate = 100;
                freqArrange.RetCellList = retArrangeResultList;
                return freqArrange;
            }
            catch
            {
                return null;
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void getFreqArrangePauseResult(InnerProcClientProxy clientProxy, List<ArrangeResult> retArrangeResultList)
        {
            uint totalLen = clientProxy.Package.Content.Length; //  296
            ushort eachPackCount = 168 + 128 + 128;
            if (totalLen % eachPackCount != 0)
            {
                throw (new Exception("Pack Invalid totalLen length not valid ,%!=0"));
            }
            uint count = totalLen / eachPackCount;
            for (int i = 0; i < count; i++)
            {
                int lac = clientProxy.Package.Content.GetParamInt();
                int ci = clientProxy.Package.Content.GetParamInt();
                int iUtranFreq = clientProxy.Package.Content.GetParamInt();
                int iHFreq = clientProxy.Package.Content.GetParamInt();
                int iR4Freq = clientProxy.Package.Content.GetParamInt();
                int iScrambleCode = clientProxy.Package.Content.GetParamInt();
                int iDisterbEvalue = clientProxy.Package.Content.GetParamInt();
                clientProxy.Package.Content.GetParamInt();//iTightEvalue
                clientProxy.Package.Content.GetParamInt();//iRelaEvalue
                clientProxy.Package.Content.GetParamInt();//iAffectCellCount
                string sAssFreqList = clientProxy.Package.Content.GetParamString(128);//ChangedXXXX
                string sAssFreqList24 = clientProxy.Package.Content.GetParamString(128);//ChangedXXXX
                string sAssRule = clientProxy.Package.Content.GetParamString(128);//ChangedXXXX

                LTECell_FSO cell = null;
                if (lacciCellDic.ContainsKey(lac.ToString() + "_" + ci.ToString()))
                {
                    cell = lacciCellDic[lac.ToString() + "_" + ci.ToString()];
                }
                if (cell != null)
                {
                    ArrangeResult arrResult = new ArrangeResult();
                    arrResult.sectorid = cell.iid;
                    arrResult.lac = lac;
                    arrResult.ci = ci;
                    arrResult.cellname = cell.strcellname;
                    arrResult.orig_iUtranFreq = (int)cell.ifreq;
                    arrResult.orig_iScrambleCode = (int)cell.ipci;
                    arrResult.iUtranFreq = iUtranFreq;
                    arrResult.iHFreq = iHFreq;
                    arrResult.iR4Freq = iR4Freq;
                    arrResult.iScrambleCode = iScrambleCode;
                    arrResult.iDisterbEvalue = iDisterbEvalue;
                    arrResult.sAssFreqList = sAssFreqList;
                    arrResult.sAssFreqList24 = sAssFreqList24;
                    arrResult.sAssRule = sAssRule;
                    retArrangeResultList.Add(arrResult);
                }
            }
        }

        public void ShowInfoForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LtePCIOptimizeInfoForm).FullName);
            LtePCIOptimizeInfoForm form = obj == null ? null : obj as LtePCIOptimizeInfoForm;
            if (form == null || form.IsDisposed)
            {
                form = new LtePCIOptimizeInfoForm(MainModel.GetInstance());
            }

            form.FillData(retCellList, false);
            if (!form.Visible)
            {
                form.Show(MainModel.GetInstance().MainForm);
            }
        }
    }
}
