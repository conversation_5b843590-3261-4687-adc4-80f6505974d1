﻿namespace MasterCom.RAMS.KPI_Statistics
{
    partial class KPIReportMainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            GeneLau.WinFormsUI.Docking.DockPanelSkin dockPanelSkin1 = new GeneLau.WinFormsUI.Docking.DockPanelSkin();
            GeneLau.WinFormsUI.Docking.AutoHideStripSkin autoHideStripSkin1 = new GeneLau.WinFormsUI.Docking.AutoHideStripSkin();
            GeneLau.WinFormsUI.Docking.DockPanelGradient dockPanelGradient1 = new GeneLau.WinFormsUI.Docking.DockPanelGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient1 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.DockPaneStripSkin dockPaneStripSkin1 = new GeneLau.WinFormsUI.Docking.DockPaneStripSkin();
            GeneLau.WinFormsUI.Docking.DockPaneStripGradient dockPaneStripGradient1 = new GeneLau.WinFormsUI.Docking.DockPaneStripGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient2 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.DockPanelGradient dockPanelGradient2 = new GeneLau.WinFormsUI.Docking.DockPanelGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient3 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.DockPaneStripToolWindowGradient dockPaneStripToolWindowGradient1 = new GeneLau.WinFormsUI.Docking.DockPaneStripToolWindowGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient4 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient5 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.DockPanelGradient dockPanelGradient3 = new GeneLau.WinFormsUI.Docking.DockPanelGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient6 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient7 = new GeneLau.WinFormsUI.Docking.TabGradient();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(KPIReportMainForm));
            this.dockPanel = new GeneLau.WinFormsUI.Docking.DockPanel();
            this.toolStrip = new System.Windows.Forms.ToolStrip();
            this.tsBtnNewReport = new System.Windows.Forms.ToolStripButton();
            this.btnSaveStyle = new System.Windows.Forms.ToolStripButton();
            this.btnSaveAs = new System.Windows.Forms.ToolStripButton();
            this.btnDelStyle = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.btnExport2ExcelSimple = new System.Windows.Forms.ToolStripButton();
            this.tsBtnXlsDetails = new System.Windows.Forms.ToolStripButton();
            this.tsBtnXlsFormula = new System.Windows.Forms.ToolStripButton();
            this.tsBtnExcelToXml = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.cascadeToolStripButton = new System.Windows.Forms.ToolStripButton();
            this.tileVerticalToolStripButton = new System.Windows.Forms.ToolStripButton();
            this.tileHorizontalToolStripButton = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.tsBtnAutoWidth = new System.Windows.Forms.ToolStripButton();
            this.toolStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // dockPanel
            // 
            this.dockPanel.ActiveAutoHideContent = null;
            this.dockPanel.AllowEndUserNestedDocking = false;
            this.dockPanel.BackColor = System.Drawing.Color.White;
            this.dockPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dockPanel.DockBackColor = System.Drawing.SystemColors.ControlLightLight;
            this.dockPanel.DockBottomPortion = 150D;
            this.dockPanel.DockLeftPortion = 200D;
            this.dockPanel.DockRightPortion = 200D;
            this.dockPanel.DockTopPortion = 150D;
            this.dockPanel.DocumentStyle = GeneLau.WinFormsUI.Docking.DocumentStyle.DockingSdi;
            this.dockPanel.Location = new System.Drawing.Point(0, 25);
            this.dockPanel.Name = "dockPanel";
            this.dockPanel.RightToLeftLayout = true;
            this.dockPanel.Size = new System.Drawing.Size(827, 351);
            dockPanelGradient1.EndColor = System.Drawing.SystemColors.ControlLight;
            dockPanelGradient1.StartColor = System.Drawing.SystemColors.ControlLight;
            autoHideStripSkin1.DockStripGradient = dockPanelGradient1;
            tabGradient1.EndColor = System.Drawing.SystemColors.Control;
            tabGradient1.StartColor = System.Drawing.SystemColors.Control;
            tabGradient1.TextColor = System.Drawing.SystemColors.ControlDarkDark;
            autoHideStripSkin1.TabGradient = tabGradient1;
            dockPanelSkin1.AutoHideStripSkin = autoHideStripSkin1;
            tabGradient2.EndColor = System.Drawing.SystemColors.ControlLightLight;
            tabGradient2.StartColor = System.Drawing.SystemColors.ControlLightLight;
            tabGradient2.TextColor = System.Drawing.SystemColors.ControlText;
            dockPaneStripGradient1.ActiveTabGradient = tabGradient2;
            dockPanelGradient2.EndColor = System.Drawing.SystemColors.Control;
            dockPanelGradient2.StartColor = System.Drawing.SystemColors.Control;
            dockPaneStripGradient1.DockStripGradient = dockPanelGradient2;
            tabGradient3.EndColor = System.Drawing.SystemColors.ControlLight;
            tabGradient3.StartColor = System.Drawing.SystemColors.ControlLight;
            tabGradient3.TextColor = System.Drawing.SystemColors.ControlText;
            dockPaneStripGradient1.InactiveTabGradient = tabGradient3;
            dockPaneStripSkin1.DocumentGradient = dockPaneStripGradient1;
            tabGradient4.EndColor = System.Drawing.SystemColors.GradientInactiveCaption;
            tabGradient4.LinearGradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            tabGradient4.StartColor = System.Drawing.SystemColors.Control;
            tabGradient4.TextColor = System.Drawing.SystemColors.ActiveCaptionText;
            dockPaneStripToolWindowGradient1.ActiveCaptionGradient = tabGradient4;
            tabGradient5.EndColor = System.Drawing.SystemColors.Control;
            tabGradient5.StartColor = System.Drawing.SystemColors.Control;
            tabGradient5.TextColor = System.Drawing.SystemColors.ControlText;
            dockPaneStripToolWindowGradient1.ActiveTabGradient = tabGradient5;
            dockPanelGradient3.EndColor = System.Drawing.SystemColors.ControlLight;
            dockPanelGradient3.StartColor = System.Drawing.SystemColors.ControlLight;
            dockPaneStripToolWindowGradient1.DockStripGradient = dockPanelGradient3;
            tabGradient6.EndColor = System.Drawing.SystemColors.ControlDarkDark;
            tabGradient6.LinearGradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            tabGradient6.StartColor = System.Drawing.SystemColors.Control;
            tabGradient6.TextColor = System.Drawing.SystemColors.ControlText;
            dockPaneStripToolWindowGradient1.InactiveCaptionGradient = tabGradient6;
            tabGradient7.EndColor = System.Drawing.Color.Transparent;
            tabGradient7.StartColor = System.Drawing.Color.Transparent;
            tabGradient7.TextColor = System.Drawing.SystemColors.ControlDarkDark;
            dockPaneStripToolWindowGradient1.InactiveTabGradient = tabGradient7;
            dockPaneStripSkin1.ToolWindowGradient = dockPaneStripToolWindowGradient1;
            dockPanelSkin1.DockPaneStripSkin = dockPaneStripSkin1;
            this.dockPanel.Skin = dockPanelSkin1;
            this.dockPanel.TabIndex = 11;
            this.dockPanel.ActiveDocumentChanged += new System.EventHandler(this.dockPanel_ActiveDocumentChanged);
            // 
            // toolStrip
            // 
            this.toolStrip.BackColor = System.Drawing.SystemColors.GradientActiveCaption;
            this.toolStrip.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            this.toolStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsBtnNewReport,
            this.btnSaveStyle,
            this.btnSaveAs,
            this.btnDelStyle,
            this.toolStripSeparator2,
            this.btnExport2ExcelSimple,
            this.tsBtnXlsDetails,
            this.tsBtnXlsFormula,
            this.tsBtnExcelToXml,
            this.toolStripSeparator1,
            this.cascadeToolStripButton,
            this.tileVerticalToolStripButton,
            this.tileHorizontalToolStripButton,
            this.toolStripSeparator3,
            this.tsBtnAutoWidth});
            this.toolStrip.Location = new System.Drawing.Point(0, 0);
            this.toolStrip.Name = "toolStrip";
            this.toolStrip.RenderMode = System.Windows.Forms.ToolStripRenderMode.System;
            this.toolStrip.Size = new System.Drawing.Size(827, 25);
            this.toolStrip.TabIndex = 10;
            this.toolStrip.Text = "toolStrip2";
            // 
            // tsBtnNewReport
            // 
            this.tsBtnNewReport.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnNewReport.Image = global::MasterCom.RAMS.Properties.Resources.newDoc;
            this.tsBtnNewReport.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnNewReport.Name = "tsBtnNewReport";
            this.tsBtnNewReport.Size = new System.Drawing.Size(23, 22);
            this.tsBtnNewReport.Text = "新建报表";
            this.tsBtnNewReport.Click += new System.EventHandler(this.tsBtnNewReport_Click);
            // 
            // btnSaveStyle
            // 
            this.btnSaveStyle.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnSaveStyle.Image = global::MasterCom.RAMS.Properties.Resources.save;
            this.btnSaveStyle.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnSaveStyle.Name = "btnSaveStyle";
            this.btnSaveStyle.Size = new System.Drawing.Size(23, 22);
            this.btnSaveStyle.Text = "保存当前报表样式";
            this.btnSaveStyle.Click += new System.EventHandler(this.btnSaveStyle_Click);
            // 
            // btnSaveAs
            // 
            this.btnSaveAs.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnSaveAs.Image = global::MasterCom.RAMS.Properties.Resources.saveas;
            this.btnSaveAs.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnSaveAs.Name = "btnSaveAs";
            this.btnSaveAs.Size = new System.Drawing.Size(23, 22);
            this.btnSaveAs.Text = "另存为";
            this.btnSaveAs.Click += new System.EventHandler(this.btnSaveAs_Click);
            // 
            // btnDelStyle
            // 
            this.btnDelStyle.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnDelStyle.Image = global::MasterCom.RAMS.Properties.Resources.delete;
            this.btnDelStyle.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnDelStyle.Name = "btnDelStyle";
            this.btnDelStyle.Size = new System.Drawing.Size(23, 22);
            this.btnDelStyle.Text = "删除当前报表样式";
            this.btnDelStyle.Visible = false;
            this.btnDelStyle.Click += new System.EventHandler(this.btnDelStyle_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(6, 25);
            // 
            // btnExport2ExcelSimple
            // 
            this.btnExport2ExcelSimple.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnExport2ExcelSimple.Image = global::MasterCom.RAMS.Properties.Resources.xls;
            this.btnExport2ExcelSimple.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnExport2ExcelSimple.Name = "btnExport2ExcelSimple";
            this.btnExport2ExcelSimple.Size = new System.Drawing.Size(23, 22);
            this.btnExport2ExcelSimple.Text = "导出到Excel文件(无样式)";
            this.btnExport2ExcelSimple.ToolTipText = "导出到Excel文件(无样式)";
            this.btnExport2ExcelSimple.Click += new System.EventHandler(this.btnExport2ExcelSimple_Click);
            // 
            // tsBtnXlsDetails
            // 
            this.tsBtnXlsDetails.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnXlsDetails.Image = global::MasterCom.RAMS.Properties.Resources.xls;
            this.tsBtnXlsDetails.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnXlsDetails.Name = "tsBtnXlsDetails";
            this.tsBtnXlsDetails.Size = new System.Drawing.Size(23, 22);
            this.tsBtnXlsDetails.Text = "导出到Excel文件(含样式)";
            this.tsBtnXlsDetails.Click += new System.EventHandler(this.tsBtnXlsDetails_Click);
            // 
            // tsBtnXlsFormula
            // 
            this.tsBtnXlsFormula.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnXlsFormula.Image = global::MasterCom.RAMS.Properties.Resources.xls;
            this.tsBtnXlsFormula.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnXlsFormula.Name = "tsBtnXlsFormula";
            this.tsBtnXlsFormula.Size = new System.Drawing.Size(23, 22);
            this.tsBtnXlsFormula.Text = "导出报表公式到Excel文件";
            this.tsBtnXlsFormula.Click += new System.EventHandler(this.tsBtnXlsFormula_Click);
            // 
            // tsBtnExcelToXml
            // 
            this.tsBtnExcelToXml.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnExcelToXml.Image = global::MasterCom.RAMS.Properties.Resources.open;
            this.tsBtnExcelToXml.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnExcelToXml.Name = "tsBtnExcelToXml";
            this.tsBtnExcelToXml.Size = new System.Drawing.Size(23, 22);
            this.tsBtnExcelToXml.Text = "导入Excel生成报表";
            this.tsBtnExcelToXml.Click += new System.EventHandler(this.tsBtnExcelToXml_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(6, 25);
            // 
            // cascadeToolStripButton
            // 
            this.cascadeToolStripButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.cascadeToolStripButton.Image = ((System.Drawing.Image)(resources.GetObject("cascadeToolStripButton.Image")));
            this.cascadeToolStripButton.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.cascadeToolStripButton.Name = "cascadeToolStripButton";
            this.cascadeToolStripButton.Size = new System.Drawing.Size(23, 22);
            this.cascadeToolStripButton.Text = "层叠窗口";
            this.cascadeToolStripButton.Click += new System.EventHandler(this.cascadeToolStripButton_Click);
            // 
            // tileVerticalToolStripButton
            // 
            this.tileVerticalToolStripButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tileVerticalToolStripButton.Image = ((System.Drawing.Image)(resources.GetObject("tileVerticalToolStripButton.Image")));
            this.tileVerticalToolStripButton.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tileVerticalToolStripButton.Name = "tileVerticalToolStripButton";
            this.tileVerticalToolStripButton.Size = new System.Drawing.Size(23, 22);
            this.tileVerticalToolStripButton.Text = "垂直平铺窗口";
            this.tileVerticalToolStripButton.Click += new System.EventHandler(this.tileVerticalToolStripButton_Click);
            // 
            // tileHorizontalToolStripButton
            // 
            this.tileHorizontalToolStripButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tileHorizontalToolStripButton.Image = ((System.Drawing.Image)(resources.GetObject("tileHorizontalToolStripButton.Image")));
            this.tileHorizontalToolStripButton.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tileHorizontalToolStripButton.Name = "tileHorizontalToolStripButton";
            this.tileHorizontalToolStripButton.Size = new System.Drawing.Size(23, 22);
            this.tileHorizontalToolStripButton.Text = "水平平铺窗口";
            this.tileHorizontalToolStripButton.Click += new System.EventHandler(this.tileHorizontalToolStripButton_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(6, 25);
            // 
            // tsBtnAutoWidth
            // 
            this.tsBtnAutoWidth.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnAutoWidth.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnAutoWidth.Image")));
            this.tsBtnAutoWidth.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnAutoWidth.Name = "tsBtnAutoWidth";
            this.tsBtnAutoWidth.Size = new System.Drawing.Size(23, 22);
            this.tsBtnAutoWidth.Text = "自动列宽";
            this.tsBtnAutoWidth.Click += new System.EventHandler(this.tsBtnAutoWidth_Click);
            // 
            // KPIReportMainForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(827, 376);
            this.Controls.Add(this.dockPanel);
            this.Controls.Add(this.toolStrip);
            this.Name = "KPIReportMainForm";
            this.Text = "KPI统计报表";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.toolStrip.ResumeLayout(false);
            this.toolStrip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private GeneLau.WinFormsUI.Docking.DockPanel dockPanel;
        private System.Windows.Forms.ToolStrip toolStrip;
        private System.Windows.Forms.ToolStripButton btnSaveAs;
        private System.Windows.Forms.ToolStripButton btnDelStyle;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripButton btnExport2ExcelSimple;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripButton cascadeToolStripButton;
        private System.Windows.Forms.ToolStripButton tileVerticalToolStripButton;
        private System.Windows.Forms.ToolStripButton tileHorizontalToolStripButton;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripButton tsBtnAutoWidth;
        private System.Windows.Forms.ToolStripButton tsBtnNewReport;
        private System.Windows.Forms.ToolStripButton btnSaveStyle;
        private System.Windows.Forms.ToolStripButton tsBtnXlsDetails;
        private System.Windows.Forms.ToolStripButton tsBtnExcelToXml;
        private System.Windows.Forms.ToolStripButton tsBtnXlsFormula;
    }
}