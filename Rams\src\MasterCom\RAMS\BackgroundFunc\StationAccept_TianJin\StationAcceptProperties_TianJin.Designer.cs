﻿namespace MasterCom.RAMS.Func.SystemSetting
{
    partial class StationAcceptProperties_TianJin
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.btnEditResultSavePath = new DevExpress.XtraEditors.ButtonEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.comboBoxFileNameType = new System.Windows.Forms.ComboBox();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.btnEditCellParamPath = new DevExpress.XtraEditors.ButtonEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl = new DevExpress.XtraEditors.GroupControl();
            this.chkBackgroundStat = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.btnEditResultSavePath.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.btnEditCellParamPath.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).BeginInit();
            this.groupControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.btnEditResultSavePath);
            this.groupControl3.Controls.Add(this.labelControl2);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl3.Location = new System.Drawing.Point(0, 169);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(461, 67);
            this.groupControl3.TabIndex = 3;
            this.groupControl3.Text = "其他设置";
            // 
            // btnEditResultSavePath
            // 
            this.btnEditResultSavePath.Location = new System.Drawing.Point(134, 33);
            this.btnEditResultSavePath.Name = "btnEditResultSavePath";
            this.btnEditResultSavePath.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.btnEditResultSavePath.Properties.ReadOnly = true;
            this.btnEditResultSavePath.Size = new System.Drawing.Size(299, 21);
            this.btnEditResultSavePath.TabIndex = 86;
            this.btnEditResultSavePath.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.btnEditSelectFolder);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(21, 36);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(104, 14);
            this.labelControl2.TabIndex = 84;
            this.labelControl2.Text = "单验报告保存路径 :";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.comboBoxFileNameType);
            this.groupControl2.Controls.Add(this.labelControl3);
            this.groupControl2.Controls.Add(this.btnEditCellParamPath);
            this.groupControl2.Controls.Add(this.labelControl1);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl2.Location = new System.Drawing.Point(0, 58);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(461, 111);
            this.groupControl2.TabIndex = 2;
            this.groupControl2.Text = "工参设置";
            // 
            // comboBoxFileNameType
            // 
            this.comboBoxFileNameType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxFileNameType.FormattingEnabled = true;
            this.comboBoxFileNameType.Items.AddRange(new object[] {
            "仅对未单验的工参进行单验",
            "仅对单验失败的工参进行单验",
            "对未单验和单验失败的工参进行单验"});
            this.comboBoxFileNameType.Location = new System.Drawing.Point(134, 71);
            this.comboBoxFileNameType.Name = "comboBoxFileNameType";
            this.comboBoxFileNameType.Size = new System.Drawing.Size(299, 22);
            this.comboBoxFileNameType.TabIndex = 87;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(49, 74);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(84, 14);
            this.labelControl3.TabIndex = 86;
            this.labelControl3.Text = "单验工参配置：";
            // 
            // btnEditCellParamPath
            // 
            this.btnEditCellParamPath.Location = new System.Drawing.Point(134, 34);
            this.btnEditCellParamPath.Name = "btnEditCellParamPath";
            this.btnEditCellParamPath.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.btnEditCellParamPath.Properties.ReadOnly = true;
            this.btnEditCellParamPath.Size = new System.Drawing.Size(299, 21);
            this.btnEditCellParamPath.TabIndex = 85;
            this.btnEditCellParamPath.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.btnEditSelectFolder);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(21, 37);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(112, 14);
            this.labelControl1.TabIndex = 83;
            this.labelControl1.Text = "工参Excel导入路径：";
            // 
            // groupControl
            // 
            this.groupControl.Controls.Add(this.chkBackgroundStat);
            this.groupControl.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl.Location = new System.Drawing.Point(0, 0);
            this.groupControl.Name = "groupControl";
            this.groupControl.Size = new System.Drawing.Size(461, 58);
            this.groupControl.TabIndex = 1;
            this.groupControl.Text = "开关设置";
            // 
            // chkBackgroundStat
            // 
            this.chkBackgroundStat.Location = new System.Drawing.Point(17, 30);
            this.chkBackgroundStat.Name = "chkBackgroundStat";
            this.chkBackgroundStat.Properties.Caption = "启用";
            this.chkBackgroundStat.Size = new System.Drawing.Size(87, 19);
            this.chkBackgroundStat.TabIndex = 0;
            // 
            // StationAcceptProperties_TianJin
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl3);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl);
            this.Name = "StationAcceptProperties_TianJin";
            this.Size = new System.Drawing.Size(461, 416);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.btnEditResultSavePath.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.btnEditCellParamPath.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).EndInit();
            this.groupControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.GroupControl groupControl;
        private DevExpress.XtraEditors.CheckEdit chkBackgroundStat;
        private DevExpress.XtraEditors.ButtonEdit btnEditResultSavePath;
        private DevExpress.XtraEditors.ButtonEdit btnEditCellParamPath;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private System.Windows.Forms.ComboBox comboBoxFileNameType;
    }
}
