﻿namespace MasterCom.RAMS.Model
{
    partial class LoginManagerDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label20 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.chkPwdSuperLimit = new System.Windows.Forms.CheckBox();
            this.label24 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numPwdNotSameRecentTimes = new System.Windows.Forms.NumericUpDown();
            this.label22 = new System.Windows.Forms.Label();
            this.numPwdUpdateDays = new System.Windows.Forms.NumericUpDown();
            this.label23 = new System.Windows.Forms.Label();
            this.numPWPromptDays = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.numFailLockTimes = new System.Windows.Forms.NumericUpDown();
            this.numResetHour = new System.Windows.Forms.NumericUpDown();
            this.label12 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.numPwdMinCharKindCount = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.numPwdMinCharCount = new System.Windows.Forms.NumericUpDown();
            this.label18 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.numNoOperateExitMins = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numDelUnLoadDays = new System.Windows.Forms.NumericUpDown();
            this.label16 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.numLockUnUseDays = new System.Windows.Forms.NumericUpDown();
            this.label14 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.numFailLockSeconds = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.label5 = new System.Windows.Forms.Label();
            this.radioBtnLogOut = new System.Windows.Forms.RadioButton();
            this.radioBtnLock = new System.Windows.Forms.RadioButton();
            this.panel1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPwdNotSameRecentTimes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPwdUpdateDays)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPWPromptDays)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFailLockTimes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResetHour)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPwdMinCharKindCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPwdMinCharCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNoOperateExitMins)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDelUnLoadDays)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLockUnUseDays)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFailLockSeconds)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(457, 459);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(65, 27);
            this.btnCancel.TabIndex = 12;
            this.btnCancel.Text = "取消";
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.radioBtnLock);
            this.panel1.Controls.Add(this.radioBtnLogOut);
            this.panel1.Controls.Add(this.groupBox1);
            this.panel1.Controls.Add(this.numNoOperateExitMins);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Controls.Add(this.label15);
            this.panel1.Controls.Add(this.label6);
            this.panel1.Controls.Add(this.numDelUnLoadDays);
            this.panel1.Controls.Add(this.label16);
            this.panel1.Controls.Add(this.label13);
            this.panel1.Controls.Add(this.numLockUnUseDays);
            this.panel1.Controls.Add(this.label14);
            this.panel1.Controls.Add(this.label10);
            this.panel1.Controls.Add(this.numFailLockSeconds);
            this.panel1.Controls.Add(this.btnOK);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.btnCancel);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(534, 500);
            this.panel1.TabIndex = 28;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label20);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.chkPwdSuperLimit);
            this.groupBox1.Controls.Add(this.label24);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.numPwdNotSameRecentTimes);
            this.groupBox1.Controls.Add(this.label22);
            this.groupBox1.Controls.Add(this.numPwdUpdateDays);
            this.groupBox1.Controls.Add(this.label23);
            this.groupBox1.Controls.Add(this.numPWPromptDays);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.numFailLockTimes);
            this.groupBox1.Controls.Add(this.numResetHour);
            this.groupBox1.Controls.Add(this.label12);
            this.groupBox1.Controls.Add(this.label21);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.numPwdMinCharKindCount);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.label19);
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Controls.Add(this.numPwdMinCharCount);
            this.groupBox1.Controls.Add(this.label18);
            this.groupBox1.Controls.Add(this.label17);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(510, 310);
            this.groupBox1.TabIndex = 62;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "密码管理";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(40, 50);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(317, 12);
            this.label20.TabIndex = 62;
            this.label20.Text = "续字符或重复字符、3位以上（含3位）键盘排序连续字符）";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(38, 181);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(77, 12);
            this.label2.TabIndex = 28;
            this.label2.Text = "密码有效期为";
            // 
            // chkPwdSuperLimit
            // 
            this.chkPwdSuperLimit.AutoSize = true;
            this.chkPwdSuperLimit.Location = new System.Drawing.Point(18, 27);
            this.chkPwdSuperLimit.Name = "chkPwdSuperLimit";
            this.chkPwdSuperLimit.Size = new System.Drawing.Size(15, 14);
            this.chkPwdSuperLimit.TabIndex = 60;
            this.chkPwdSuperLimit.UseVisualStyleBackColor = true;
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(39, 27);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(461, 12);
            this.label24.TabIndex = 61;
            this.label24.Text = "启用密码强度超强限制（密码不得包含用户名、年月日、手机号、3位以上（含3位）连";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(14, 246);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(101, 12);
            this.label4.TabIndex = 30;
            this.label4.Text = "密码最多连续输错";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(26, 214);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(89, 12);
            this.label3.TabIndex = 29;
            this.label3.Text = "密码即将过期前";
            // 
            // numPwdNotSameRecentTimes
            // 
            this.numPwdNotSameRecentTimes.Location = new System.Drawing.Point(123, 144);
            this.numPwdNotSameRecentTimes.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numPwdNotSameRecentTimes.Name = "numPwdNotSameRecentTimes";
            this.numPwdNotSameRecentTimes.Size = new System.Drawing.Size(80, 21);
            this.numPwdNotSameRecentTimes.TabIndex = 58;
            this.numPwdNotSameRecentTimes.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(86, 148);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(29, 12);
            this.label22.TabIndex = 57;
            this.label22.Text = "最近";
            // 
            // numPwdUpdateDays
            // 
            this.numPwdUpdateDays.Location = new System.Drawing.Point(123, 177);
            this.numPwdUpdateDays.Maximum = new decimal(new int[] {
            30000,
            0,
            0,
            0});
            this.numPwdUpdateDays.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            -2147483648});
            this.numPwdUpdateDays.Name = "numPwdUpdateDays";
            this.numPwdUpdateDays.Size = new System.Drawing.Size(80, 21);
            this.numPwdUpdateDays.TabIndex = 32;
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(211, 148);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(149, 12);
            this.label23.TabIndex = 59;
            this.label23.Text = "次以内不得设置相同的密码";
            // 
            // numPWPromptDays
            // 
            this.numPWPromptDays.Location = new System.Drawing.Point(123, 210);
            this.numPWPromptDays.Maximum = new decimal(new int[] {
            30000,
            0,
            0,
            0});
            this.numPWPromptDays.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            -2147483648});
            this.numPWPromptDays.Name = "numPWPromptDays";
            this.numPWPromptDays.Size = new System.Drawing.Size(80, 21);
            this.numPWPromptDays.TabIndex = 33;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(211, 278);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(251, 12);
            this.label11.TabIndex = 43;
            this.label11.Text = "时整,将状态正常账号的密码连续输错次数清零";
            // 
            // numFailLockTimes
            // 
            this.numFailLockTimes.Location = new System.Drawing.Point(123, 242);
            this.numFailLockTimes.Maximum = new decimal(new int[] {
            30000,
            0,
            0,
            0});
            this.numFailLockTimes.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numFailLockTimes.Name = "numFailLockTimes";
            this.numFailLockTimes.Size = new System.Drawing.Size(80, 21);
            this.numFailLockTimes.TabIndex = 34;
            this.numFailLockTimes.Value = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            // 
            // numResetHour
            // 
            this.numResetHour.Location = new System.Drawing.Point(123, 274);
            this.numResetHour.Maximum = new decimal(new int[] {
            23,
            0,
            0,
            0});
            this.numResetHour.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            -2147483648});
            this.numResetHour.Name = "numResetHour";
            this.numResetHour.Size = new System.Drawing.Size(80, 21);
            this.numResetHour.TabIndex = 42;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(86, 278);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(29, 12);
            this.label12.TabIndex = 41;
            this.label12.Text = "每天";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(209, 114);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(17, 12);
            this.label21.TabIndex = 56;
            this.label21.Text = "类";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(211, 181);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(119, 12);
            this.label7.TabIndex = 37;
            this.label7.Text = "天,过期后将无法登陆";
            // 
            // numPwdMinCharKindCount
            // 
            this.numPwdMinCharKindCount.Location = new System.Drawing.Point(123, 110);
            this.numPwdMinCharKindCount.Maximum = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numPwdMinCharKindCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numPwdMinCharKindCount.Name = "numPwdMinCharKindCount";
            this.numPwdMinCharKindCount.Size = new System.Drawing.Size(80, 21);
            this.numPwdMinCharKindCount.TabIndex = 54;
            this.numPwdMinCharKindCount.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(211, 214);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(137, 12);
            this.label8.TabIndex = 38;
            this.label8.Text = "天开始提示用户更新密码";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(38, 114);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(71, 12);
            this.label19.TabIndex = 53;
            this.label19.Text = "符号4类中的";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(211, 246);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(107, 12);
            this.label9.TabIndex = 39;
            this.label9.Text = "次,超过将锁定账号";
            // 
            // numPwdMinCharCount
            // 
            this.numPwdMinCharCount.Location = new System.Drawing.Point(123, 83);
            this.numPwdMinCharCount.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numPwdMinCharCount.Minimum = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numPwdMinCharCount.Name = "numPwdMinCharCount";
            this.numPwdMinCharCount.Size = new System.Drawing.Size(80, 21);
            this.numPwdMinCharCount.TabIndex = 51;
            this.numPwdMinCharCount.Value = new decimal(new int[] {
            8,
            0,
            0,
            0});
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(211, 87);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(293, 12);
            this.label18.TabIndex = 52;
            this.label18.Text = "位字符，且至少包含大写字母、小写字母、数字和特殊";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(38, 87);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(77, 12);
            this.label17.TabIndex = 50;
            this.label17.Text = "密码需至少有";
            // 
            // numNoOperateExitMins
            // 
            this.numNoOperateExitMins.Location = new System.Drawing.Point(137, 433);
            this.numNoOperateExitMins.Maximum = new decimal(new int[] {
            30000,
            0,
            0,
            0});
            this.numNoOperateExitMins.Minimum = new decimal(new int[] {
            9,
            0,
            0,
            0});
            this.numNoOperateExitMins.Name = "numNoOperateExitMins";
            this.numNoOperateExitMins.Size = new System.Drawing.Size(80, 21);
            this.numNoOperateExitMins.TabIndex = 6;
            this.numNoOperateExitMins.Value = new decimal(new int[] {
            9,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(76, 437);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "系统超过";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(225, 404);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(89, 12);
            this.label15.TabIndex = 49;
            this.label15.Text = "天未登陆的账号";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(225, 437);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 13;
            this.label6.Text = "分钟无用户操作后";
            // 
            // numDelUnLoadDays
            // 
            this.numDelUnLoadDays.Location = new System.Drawing.Point(137, 400);
            this.numDelUnLoadDays.Maximum = new decimal(new int[] {
            30000,
            0,
            0,
            0});
            this.numDelUnLoadDays.Minimum = new decimal(new int[] {
            29,
            0,
            0,
            0});
            this.numDelUnLoadDays.Name = "numDelUnLoadDays";
            this.numDelUnLoadDays.Size = new System.Drawing.Size(80, 21);
            this.numDelUnLoadDays.TabIndex = 48;
            this.numDelUnLoadDays.Value = new decimal(new int[] {
            29,
            0,
            0,
            0});
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(52, 402);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(77, 12);
            this.label16.TabIndex = 47;
            this.label16.Text = "自动删除超过";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(225, 370);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(89, 12);
            this.label13.TabIndex = 46;
            this.label13.Text = "天未登陆的账号";
            // 
            // numLockUnUseDays
            // 
            this.numLockUnUseDays.Location = new System.Drawing.Point(137, 366);
            this.numLockUnUseDays.Maximum = new decimal(new int[] {
            30000,
            0,
            0,
            0});
            this.numLockUnUseDays.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            -2147483648});
            this.numLockUnUseDays.Name = "numLockUnUseDays";
            this.numLockUnUseDays.Size = new System.Drawing.Size(80, 21);
            this.numLockUnUseDays.TabIndex = 45;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(52, 370);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(77, 12);
            this.label14.TabIndex = 44;
            this.label14.Text = "自动锁定超过";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(223, 336);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(77, 12);
            this.label10.TabIndex = 40;
            this.label10.Text = "秒后自动解锁";
            // 
            // numFailLockSeconds
            // 
            this.numFailLockSeconds.Location = new System.Drawing.Point(135, 332);
            this.numFailLockSeconds.Maximum = new decimal(new int[] {
            30000,
            0,
            0,
            0});
            this.numFailLockSeconds.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            -2147483648});
            this.numFailLockSeconds.Name = "numFailLockSeconds";
            this.numFailLockSeconds.Size = new System.Drawing.Size(80, 21);
            this.numFailLockSeconds.TabIndex = 35;
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(358, 459);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(65, 27);
            this.btnOK.TabIndex = 36;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(40, 336);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(89, 12);
            this.label5.TabIndex = 31;
            this.label5.Text = "已被锁定的账号";
            // 
            // radioBtnLogOut
            // 
            this.radioBtnLogOut.AutoSize = true;
            this.radioBtnLogOut.Checked = true;
            this.radioBtnLogOut.Location = new System.Drawing.Point(332, 435);
            this.radioBtnLogOut.Name = "radioBtnLogOut";
            this.radioBtnLogOut.Size = new System.Drawing.Size(71, 16);
            this.radioBtnLogOut.TabIndex = 63;
            this.radioBtnLogOut.TabStop = true;
            this.radioBtnLogOut.Text = "自动退出";
            this.radioBtnLogOut.UseVisualStyleBackColor = true;
            // 
            // radioBtnLock
            // 
            this.radioBtnLock.AutoSize = true;
            this.radioBtnLock.Location = new System.Drawing.Point(409, 435);
            this.radioBtnLock.Name = "radioBtnLock";
            this.radioBtnLock.Size = new System.Drawing.Size(71, 16);
            this.radioBtnLock.TabIndex = 64;
            this.radioBtnLock.Text = "自动锁定";
            this.radioBtnLock.UseVisualStyleBackColor = true;
            // 
            // LoginManagerDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(534, 500);
            this.Controls.Add(this.panel1);
            this.Name = "LoginManagerDlg";
            this.Text = "登录管理";
            this.Load += new System.EventHandler(this.FuncManager_Load);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPwdNotSameRecentTimes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPwdUpdateDays)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPWPromptDays)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFailLockTimes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResetHour)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPwdMinCharKindCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPwdMinCharCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNoOperateExitMins)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDelUnLoadDays)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLockUnUseDays)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFailLockSeconds)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.NumericUpDown numNoOperateExitMins;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.NumericUpDown numDelUnLoadDays;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.NumericUpDown numLockUnUseDays;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown numResetHour;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numFailLockSeconds;
        private System.Windows.Forms.NumericUpDown numFailLockTimes;
        private System.Windows.Forms.NumericUpDown numPWPromptDays;
        private System.Windows.Forms.NumericUpDown numPwdUpdateDays;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numPwdMinCharCount;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.NumericUpDown numPwdMinCharKindCount;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.NumericUpDown numPwdNotSameRecentTimes;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.CheckBox chkPwdSuperLimit;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.RadioButton radioBtnLock;
        private System.Windows.Forms.RadioButton radioBtnLogOut;
    }
}