﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NBCellGridInfoPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listViewGridInfo = new BrightIdeasSoftware.TreeListView();
            this.olvColumnTLLong = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTLLat = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBRLong = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBRLat = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnOrgCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnOrgCellLac = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnOrgCellCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnOrgCellRxlev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnOrgCellSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnNBCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnNBCellLac = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnNBCellCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnNBCellRxlev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnNBCellSample = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStripListViewGridInfo = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExpendAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemShrinkAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemDownLoadList = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.listViewGridInfo)).BeginInit();
            this.contextMenuStripListViewGridInfo.SuspendLayout();
            this.SuspendLayout();
            // 
            // listViewGridInfo
            // 
            this.listViewGridInfo.AllColumns.Add(this.olvColumnTLLong);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnTLLat);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnBRLong);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnBRLat);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnOrgCellName);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnOrgCellLac);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnOrgCellCI);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnOrgCellRxlev);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnOrgCellSample);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnNBCellName);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnNBCellLac);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnNBCellCI);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnNBCellRxlev);
            this.listViewGridInfo.AllColumns.Add(this.olvColumnNBCellSample);
            this.listViewGridInfo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnTLLong,
            this.olvColumnTLLat,
            this.olvColumnBRLong,
            this.olvColumnBRLat,
            this.olvColumnOrgCellName,
            this.olvColumnOrgCellLac,
            this.olvColumnOrgCellCI,
            this.olvColumnOrgCellRxlev,
            this.olvColumnOrgCellSample,
            this.olvColumnNBCellName,
            this.olvColumnNBCellLac,
            this.olvColumnNBCellCI,
            this.olvColumnNBCellRxlev,
            this.olvColumnNBCellSample});
            this.listViewGridInfo.ContextMenuStrip = this.contextMenuStripListViewGridInfo;
            this.listViewGridInfo.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewGridInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewGridInfo.FullRowSelect = true;
            this.listViewGridInfo.GridLines = true;
            this.listViewGridInfo.HeaderWordWrap = true;
            this.listViewGridInfo.IsNeedShowOverlay = false;
            this.listViewGridInfo.Location = new System.Drawing.Point(0, 0);
            this.listViewGridInfo.Name = "listViewGridInfo";
            this.listViewGridInfo.OwnerDraw = true;
            this.listViewGridInfo.ShowGroups = false;
            this.listViewGridInfo.Size = new System.Drawing.Size(860, 440);
            this.listViewGridInfo.TabIndex = 12;
            this.listViewGridInfo.UseCompatibleStateImageBehavior = false;
            this.listViewGridInfo.View = System.Windows.Forms.View.Details;
            this.listViewGridInfo.VirtualMode = true;
            // 
            // olvColumnTLLong
            // 
            this.olvColumnTLLong.HeaderFont = null;
            this.olvColumnTLLong.Text = "左上角经度";
            this.olvColumnTLLong.Width = 66;
            // 
            // olvColumnTLLat
            // 
            this.olvColumnTLLat.HeaderFont = null;
            this.olvColumnTLLat.Text = "左上角纬度";
            this.olvColumnTLLat.Width = 78;
            // 
            // olvColumnBRLong
            // 
            this.olvColumnBRLong.HeaderFont = null;
            this.olvColumnBRLong.Text = "右下角经度";
            this.olvColumnBRLong.Width = 69;
            // 
            // olvColumnBRLat
            // 
            this.olvColumnBRLat.HeaderFont = null;
            this.olvColumnBRLat.Text = "右下角纬度";
            this.olvColumnBRLat.Width = 70;
            // 
            // olvColumnOrgCellName
            // 
            this.olvColumnOrgCellName.HeaderFont = null;
            this.olvColumnOrgCellName.Text = "参考小区名称";
            // 
            // olvColumnOrgCellLac
            // 
            this.olvColumnOrgCellLac.HeaderFont = null;
            this.olvColumnOrgCellLac.Text = "参考小区LAC";
            this.olvColumnOrgCellLac.Width = 61;
            // 
            // olvColumnOrgCellCI
            // 
            this.olvColumnOrgCellCI.HeaderFont = null;
            this.olvColumnOrgCellCI.Text = "参考小区CI";
            this.olvColumnOrgCellCI.Width = 44;
            // 
            // olvColumnOrgCellRxlev
            // 
            this.olvColumnOrgCellRxlev.HeaderFont = null;
            this.olvColumnOrgCellRxlev.Text = "参考小区场强";
            // 
            // olvColumnOrgCellSample
            // 
            this.olvColumnOrgCellSample.HeaderFont = null;
            this.olvColumnOrgCellSample.Text = "参考小区采样点数";
            // 
            // olvColumnNBCellName
            // 
            this.olvColumnNBCellName.HeaderFont = null;
            this.olvColumnNBCellName.Text = "拟邻区名称";
            this.olvColumnNBCellName.Width = 70;
            // 
            // olvColumnNBCellLac
            // 
            this.olvColumnNBCellLac.AspectName = "";
            this.olvColumnNBCellLac.HeaderFont = null;
            this.olvColumnNBCellLac.Text = "拟邻区LAC";
            this.olvColumnNBCellLac.Width = 62;
            // 
            // olvColumnNBCellCI
            // 
            this.olvColumnNBCellCI.HeaderFont = null;
            this.olvColumnNBCellCI.Text = "拟邻区CI";
            this.olvColumnNBCellCI.Width = 54;
            // 
            // olvColumnNBCellRxlev
            // 
            this.olvColumnNBCellRxlev.HeaderFont = null;
            this.olvColumnNBCellRxlev.Text = "拟邻区场强值";
            // 
            // olvColumnNBCellSample
            // 
            this.olvColumnNBCellSample.HeaderFont = null;
            this.olvColumnNBCellSample.Text = "拟邻区采样点数";
            // 
            // contextMenuStripListViewGridInfo
            // 
            this.contextMenuStripListViewGridInfo.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExpendAll,
            this.ToolStripMenuItemShrinkAll,
            this.toolStripMenuItemDownLoadList});
            this.contextMenuStripListViewGridInfo.Name = "contextMenuStripListViewGridInfo";
            this.contextMenuStripListViewGridInfo.Size = new System.Drawing.Size(153, 92);
            // 
            // ToolStripMenuItemExpendAll
            // 
            this.ToolStripMenuItemExpendAll.Name = "ToolStripMenuItemExpendAll";
            this.ToolStripMenuItemExpendAll.Size = new System.Drawing.Size(152, 22);
            this.ToolStripMenuItemExpendAll.Text = "展开全部";
            this.ToolStripMenuItemExpendAll.Click += new System.EventHandler(this.ToolStripMenuItemExpendAll_Click);
            // 
            // ToolStripMenuItemShrinkAll
            // 
            this.ToolStripMenuItemShrinkAll.Name = "ToolStripMenuItemShrinkAll";
            this.ToolStripMenuItemShrinkAll.Size = new System.Drawing.Size(152, 22);
            this.ToolStripMenuItemShrinkAll.Text = "收缩全部";
            this.ToolStripMenuItemShrinkAll.Click += new System.EventHandler(this.ToolStripMenuItemShrinkAll_Click);
            // 
            // toolStripMenuItemDownLoadList
            // 
            this.toolStripMenuItemDownLoadList.Name = "toolStripMenuItemDownLoadList";
            this.toolStripMenuItemDownLoadList.Size = new System.Drawing.Size(152, 22);
            this.toolStripMenuItemDownLoadList.Text = "导出列表";
            this.toolStripMenuItemDownLoadList.Click += new System.EventHandler(this.toolStripMenuItemDownLoadList_Click);
            // 
            // NBCellGridInfoPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.listViewGridInfo);
            this.Name = "NBCellGridInfoPanel";
            this.Size = new System.Drawing.Size(860, 440);
            ((System.ComponentModel.ISupportInitialize)(this.listViewGridInfo)).EndInit();
            this.contextMenuStripListViewGridInfo.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView listViewGridInfo;
        private BrightIdeasSoftware.OLVColumn olvColumnTLLong;
        private BrightIdeasSoftware.OLVColumn olvColumnTLLat;
        private BrightIdeasSoftware.OLVColumn olvColumnBRLong;
        private BrightIdeasSoftware.OLVColumn olvColumnBRLat;
        private BrightIdeasSoftware.OLVColumn olvColumnOrgCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnOrgCellLac;
        private BrightIdeasSoftware.OLVColumn olvColumnOrgCellCI;
        private BrightIdeasSoftware.OLVColumn olvColumnOrgCellRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnOrgCellSample;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellLac;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellCI;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellSample;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripListViewGridInfo;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpendAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemShrinkAll;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemDownLoadList;
    }
}
