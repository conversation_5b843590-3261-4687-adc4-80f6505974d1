﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsDualFreqSetting : LteMgrsConditionControlBase
    {
        public LteMgrsDualFreqSetting()
        {
            InitializeComponent();
            XmlConfigFile configFile = new MyXmlConfigFile(LteMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                XmlElement configDualFreq = configFile.GetConfig("DualFreq");
                object obj = configFile.GetItemValue(configDualFreq, "RSRP");
                if (obj != null)
                {
                    numRsrp.Value = (decimal)(double)obj;
                }
            }
        }

        public override string Title
        {
            get { return "双频网统计"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            invalidReason = null;
            return (double)numRsrp.Value;
        }

        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            XmlElement configDualFreq = xcfg.AddConfig("DualFreq");
            xcfg.AddItem(configDualFreq, "RSRP", (double)numRsrp.Value);
        }
    }
}
