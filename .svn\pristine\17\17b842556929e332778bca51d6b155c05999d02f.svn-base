﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LeakOutAsNCellDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.numRxLev = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numRxLevDValue = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.chkMainCell = new System.Windows.Forms.CheckBox();
            this.chkNBCell = new System.Windows.Forms.CheckBox();
            this.chkGetRoadDesc = new System.Windows.Forms.CheckBox();
            this.label5 = new System.Windows.Forms.Label();
            this.chkGSMCell = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLev)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(91, 54);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "邻区电平≥";
            // 
            // numRxLev
            // 
            this.numRxLev.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numRxLev.Location = new System.Drawing.Point(165, 50);
            this.numRxLev.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxLev.Minimum = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numRxLev.Name = "numRxLev";
            this.numRxLev.Size = new System.Drawing.Size(73, 21);
            this.numRxLev.TabIndex = 1;
            this.numRxLev.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxLev.Value = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(137, 169);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(218, 169);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(246, 54);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(23, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "dBm";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(246, 85);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(23, 12);
            this.label3.TabIndex = 7;
            this.label3.Text = "dBm";
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numRxLevDValue.Location = new System.Drawing.Point(165, 81);
            this.numRxLevDValue.Maximum = new decimal(new int[] {
            120,
            0,
            0,
            0});
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Size = new System.Drawing.Size(73, 21);
            this.numRxLevDValue.TabIndex = 6;
            this.numRxLevDValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxLevDValue.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(43, 85);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(113, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "邻区与主服电平差≤";
            // 
            // chkMainCell
            // 
            this.chkMainCell.AutoSize = true;
            this.chkMainCell.Checked = true;
            this.chkMainCell.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkMainCell.Location = new System.Drawing.Point(30, 14);
            this.chkMainCell.Name = "chkMainCell";
            this.chkMainCell.Size = new System.Drawing.Size(108, 16);
            this.chkMainCell.TabIndex = 9;
            this.chkMainCell.Text = "只计算主服小区";
            this.chkMainCell.UseVisualStyleBackColor = true;
            // 
            // chkNBCell
            // 
            this.chkNBCell.AutoSize = true;
            this.chkNBCell.Location = new System.Drawing.Point(174, 14);
            this.chkNBCell.Name = "chkNBCell";
            this.chkNBCell.Size = new System.Drawing.Size(96, 16);
            this.chkNBCell.TabIndex = 10;
            this.chkNBCell.Text = "同时计算邻区";
            this.chkNBCell.UseVisualStyleBackColor = true;
            this.chkNBCell.CheckedChanged += new System.EventHandler(this.chkNBCell_CheckedChanged);
            // 
            // chkGetRoadDesc
            // 
            this.chkGetRoadDesc.AutoSize = true;
            this.chkGetRoadDesc.Location = new System.Drawing.Point(30, 118);
            this.chkGetRoadDesc.Name = "chkGetRoadDesc";
            this.chkGetRoadDesc.Size = new System.Drawing.Size(120, 16);
            this.chkGetRoadDesc.TabIndex = 11;
            this.chkGetRoadDesc.Text = "获取覆盖道路信息";
            this.chkGetRoadDesc.UseVisualStyleBackColor = true;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.label5.Location = new System.Drawing.Point(147, 120);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 12;
            this.label5.Text = "(速度较慢)";
            // 
            // chkGSMCell
            // 
            this.chkGSMCell.AutoSize = true;
            this.chkGSMCell.Location = new System.Drawing.Point(30, 147);
            this.chkGSMCell.Name = "chkGSMCell";
            this.chkGSMCell.Size = new System.Drawing.Size(114, 16);
            this.chkGSMCell.TabIndex = 13;
            this.chkGSMCell.Text = "分析回落GSM小区";
            this.chkGSMCell.UseVisualStyleBackColor = true;
            // 
            // LeakOutAsNCellDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(300, 200);
            this.Controls.Add(this.chkGSMCell);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.chkGetRoadDesc);
            this.Controls.Add(this.chkNBCell);
            this.Controls.Add(this.chkMainCell);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.numRxLevDValue);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.numRxLev);
            this.Controls.Add(this.label1);
            this.Name = "LeakOutAsNCellDlg";
            this.Text = "覆盖外泄条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRxLev)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numRxLev;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numRxLevDValue;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.CheckBox chkMainCell;
        private System.Windows.Forms.CheckBox chkNBCell;
        private System.Windows.Forms.CheckBox chkGetRoadDesc;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.CheckBox chkGSMCell;
    }
}