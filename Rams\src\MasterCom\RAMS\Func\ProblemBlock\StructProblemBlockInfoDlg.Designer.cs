﻿namespace MasterCom.RAMS.Func.ProblemBlock
{
    partial class StructProblemBlockInfoDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(StructProblemBlockInfoDlg));
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.tbEvaluateInfo = new System.Windows.Forms.TextBox();
            this.label17 = new System.Windows.Forms.Label();
            this.tbEvaluateDate = new System.Windows.Forms.TextBox();
            this.label18 = new System.Windows.Forms.Label();
            this.tbCloseReason = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.tbSettingDate = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.tbxEventDes = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.tbxLastTest = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.tbxCellDesc = new System.Windows.Forms.TextBox();
            this.tbxPlaceDesc = new System.Windows.Forms.TextBox();
            this.tbxFirstAbDate = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.tbxLastAbEvent = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.tbxCloseDate = new System.Windows.Forms.TextBox();
            this.tbxCreateDate = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.tbxAbEventCount = new System.Windows.Forms.TextBox();
            this.tbxStatus = new System.Windows.Forms.TextBox();
            this.tbxNormalDays = new System.Windows.Forms.TextBox();
            this.tbxAbDays = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.tbxBlockID = new System.Windows.Forms.TextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.listViewEvents = new System.Windows.Forms.ListView();
            this.columnHeaderSN = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventName = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderTime = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderLongitude = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderLatitude = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderProjName = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderFileName = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderInfo = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.ctxStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox3.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.ctxStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.tbEvaluateInfo);
            this.groupBox3.Controls.Add(this.label17);
            this.groupBox3.Controls.Add(this.tbEvaluateDate);
            this.groupBox3.Controls.Add(this.label18);
            this.groupBox3.Controls.Add(this.tbCloseReason);
            this.groupBox3.Controls.Add(this.label16);
            this.groupBox3.Controls.Add(this.tbSettingDate);
            this.groupBox3.Controls.Add(this.label1);
            this.groupBox3.Controls.Add(this.tbxEventDes);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.tbxLastTest);
            this.groupBox3.Controls.Add(this.label14);
            this.groupBox3.Controls.Add(this.label13);
            this.groupBox3.Controls.Add(this.label11);
            this.groupBox3.Controls.Add(this.tbxCellDesc);
            this.groupBox3.Controls.Add(this.tbxPlaceDesc);
            this.groupBox3.Controls.Add(this.tbxFirstAbDate);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Controls.Add(this.tbxLastAbEvent);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.label5);
            this.groupBox3.Controls.Add(this.tbxCloseDate);
            this.groupBox3.Controls.Add(this.tbxCreateDate);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.tbxAbEventCount);
            this.groupBox3.Controls.Add(this.tbxStatus);
            this.groupBox3.Controls.Add(this.tbxNormalDays);
            this.groupBox3.Controls.Add(this.tbxAbDays);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.tbxBlockID);
            this.groupBox3.Location = new System.Drawing.Point(12, 12);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(675, 291);
            this.groupBox3.TabIndex = 3;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "问题点情况";
            // 
            // tbEvaluateInfo
            // 
            this.tbEvaluateInfo.Location = new System.Drawing.Point(414, 155);
            this.tbEvaluateInfo.Name = "tbEvaluateInfo";
            this.tbEvaluateInfo.ReadOnly = true;
            this.tbEvaluateInfo.Size = new System.Drawing.Size(249, 21);
            this.tbEvaluateInfo.TabIndex = 21;
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(355, 158);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(53, 12);
            this.label17.TabIndex = 20;
            this.label17.Text = "评估消息";
            // 
            // tbEvaluateDate
            // 
            this.tbEvaluateDate.Location = new System.Drawing.Point(531, 112);
            this.tbEvaluateDate.Name = "tbEvaluateDate";
            this.tbEvaluateDate.ReadOnly = true;
            this.tbEvaluateDate.Size = new System.Drawing.Size(132, 21);
            this.tbEvaluateDate.TabIndex = 19;
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(470, 115);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(53, 12);
            this.label18.TabIndex = 18;
            this.label18.Text = "评估时间";
            // 
            // tbCloseReason
            // 
            this.tbCloseReason.Location = new System.Drawing.Point(88, 155);
            this.tbCloseReason.Name = "tbCloseReason";
            this.tbCloseReason.ReadOnly = true;
            this.tbCloseReason.Size = new System.Drawing.Size(232, 21);
            this.tbCloseReason.TabIndex = 17;
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(29, 158);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(53, 12);
            this.label16.TabIndex = 16;
            this.label16.Text = "关闭原因";
            // 
            // tbSettingDate
            // 
            this.tbSettingDate.Location = new System.Drawing.Point(305, 79);
            this.tbSettingDate.Name = "tbSettingDate";
            this.tbSettingDate.ReadOnly = true;
            this.tbSettingDate.Size = new System.Drawing.Size(132, 21);
            this.tbSettingDate.TabIndex = 13;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(250, 82);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 12;
            this.label1.Text = "定型时间";
            // 
            // tbxEventDes
            // 
            this.tbxEventDes.Location = new System.Drawing.Point(88, 198);
            this.tbxEventDes.Name = "tbxEventDes";
            this.tbxEventDes.ReadOnly = true;
            this.tbxEventDes.Size = new System.Drawing.Size(232, 21);
            this.tbxEventDes.TabIndex = 11;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(29, 201);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(53, 12);
            this.label15.TabIndex = 10;
            this.label15.Text = "事件详情";
            // 
            // tbxLastTest
            // 
            this.tbxLastTest.Location = new System.Drawing.Point(89, 82);
            this.tbxLastTest.Name = "tbxLastTest";
            this.tbxLastTest.ReadOnly = true;
            this.tbxLastTest.Size = new System.Drawing.Size(132, 21);
            this.tbxLastTest.TabIndex = 5;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(355, 201);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(53, 12);
            this.label14.TabIndex = 8;
            this.label14.Text = "相关小区";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(30, 241);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(53, 12);
            this.label13.TabIndex = 8;
            this.label13.Text = "位置描述";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(450, 55);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(77, 12);
            this.label11.TabIndex = 8;
            this.label11.Text = "首次异常时间";
            // 
            // tbxCellDesc
            // 
            this.tbxCellDesc.Location = new System.Drawing.Point(414, 198);
            this.tbxCellDesc.Name = "tbxCellDesc";
            this.tbxCellDesc.ReadOnly = true;
            this.tbxCellDesc.Size = new System.Drawing.Size(249, 21);
            this.tbxCellDesc.TabIndex = 9;
            // 
            // tbxPlaceDesc
            // 
            this.tbxPlaceDesc.Location = new System.Drawing.Point(88, 238);
            this.tbxPlaceDesc.Name = "tbxPlaceDesc";
            this.tbxPlaceDesc.ReadOnly = true;
            this.tbxPlaceDesc.Size = new System.Drawing.Size(575, 21);
            this.tbxPlaceDesc.TabIndex = 9;
            // 
            // tbxFirstAbDate
            // 
            this.tbxFirstAbDate.Location = new System.Drawing.Point(531, 50);
            this.tbxFirstAbDate.Name = "tbxFirstAbDate";
            this.tbxFirstAbDate.ReadOnly = true;
            this.tbxFirstAbDate.Size = new System.Drawing.Size(132, 21);
            this.tbxFirstAbDate.TabIndex = 9;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(448, 82);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(77, 12);
            this.label10.TabIndex = 6;
            this.label10.Text = "最后异常时间";
            // 
            // tbxLastAbEvent
            // 
            this.tbxLastAbEvent.Location = new System.Drawing.Point(531, 79);
            this.tbxLastAbEvent.Name = "tbxLastAbEvent";
            this.tbxLastAbEvent.ReadOnly = true;
            this.tbxLastAbEvent.Size = new System.Drawing.Size(132, 21);
            this.tbxLastAbEvent.TabIndex = 7;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(10, 85);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(77, 12);
            this.label9.TabIndex = 4;
            this.label9.Text = "最后测试时间";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(246, 115);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(53, 12);
            this.label8.TabIndex = 4;
            this.label8.Text = "关闭时间";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(250, 55);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 4;
            this.label5.Text = "创建时间";
            // 
            // tbxCloseDate
            // 
            this.tbxCloseDate.Location = new System.Drawing.Point(305, 112);
            this.tbxCloseDate.Name = "tbxCloseDate";
            this.tbxCloseDate.ReadOnly = true;
            this.tbxCloseDate.Size = new System.Drawing.Size(132, 21);
            this.tbxCloseDate.TabIndex = 5;
            // 
            // tbxCreateDate
            // 
            this.tbxCreateDate.Location = new System.Drawing.Point(305, 52);
            this.tbxCreateDate.Name = "tbxCreateDate";
            this.tbxCreateDate.ReadOnly = true;
            this.tbxCreateDate.Size = new System.Drawing.Size(132, 21);
            this.tbxCreateDate.TabIndex = 5;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(21, 115);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(65, 12);
            this.label7.TabIndex = 2;
            this.label7.Text = "异常事件数";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(246, 26);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(53, 12);
            this.label12.TabIndex = 2;
            this.label12.Text = "当前状态";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(472, 26);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 12);
            this.label6.TabIndex = 2;
            this.label6.Text = "正常天数";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(33, 55);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 12);
            this.label4.TabIndex = 2;
            this.label4.Text = "问题天数";
            // 
            // tbxAbEventCount
            // 
            this.tbxAbEventCount.Location = new System.Drawing.Point(88, 112);
            this.tbxAbEventCount.Name = "tbxAbEventCount";
            this.tbxAbEventCount.ReadOnly = true;
            this.tbxAbEventCount.Size = new System.Drawing.Size(132, 21);
            this.tbxAbEventCount.TabIndex = 3;
            // 
            // tbxStatus
            // 
            this.tbxStatus.Location = new System.Drawing.Point(305, 20);
            this.tbxStatus.Name = "tbxStatus";
            this.tbxStatus.ReadOnly = true;
            this.tbxStatus.Size = new System.Drawing.Size(132, 21);
            this.tbxStatus.TabIndex = 3;
            // 
            // tbxNormalDays
            // 
            this.tbxNormalDays.Location = new System.Drawing.Point(531, 23);
            this.tbxNormalDays.Name = "tbxNormalDays";
            this.tbxNormalDays.ReadOnly = true;
            this.tbxNormalDays.Size = new System.Drawing.Size(132, 21);
            this.tbxNormalDays.TabIndex = 3;
            // 
            // tbxAbDays
            // 
            this.tbxAbDays.Location = new System.Drawing.Point(88, 50);
            this.tbxAbDays.Name = "tbxAbDays";
            this.tbxAbDays.ReadOnly = true;
            this.tbxAbDays.Size = new System.Drawing.Size(132, 21);
            this.tbxAbDays.TabIndex = 3;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(21, 26);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "问题点编号";
            // 
            // tbxBlockID
            // 
            this.tbxBlockID.Location = new System.Drawing.Point(88, 20);
            this.tbxBlockID.Name = "tbxBlockID";
            this.tbxBlockID.ReadOnly = true;
            this.tbxBlockID.Size = new System.Drawing.Size(132, 21);
            this.tbxBlockID.TabIndex = 1;
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.listViewEvents);
            this.groupBox1.Location = new System.Drawing.Point(12, 309);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(675, 136);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "事件信息";
            // 
            // listViewEvents
            // 
            this.listViewEvents.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewEvents.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSN,
            this.columnHeaderEventName,
            this.columnHeaderTime,
            this.columnHeaderLongitude,
            this.columnHeaderLatitude,
            this.columnHeaderProjName,
            this.columnHeaderFileName,
            this.columnHeaderInfo});
            this.listViewEvents.ContextMenuStrip = this.ctxStrip;
            this.listViewEvents.FullRowSelect = true;
            this.listViewEvents.GridLines = true;
            this.listViewEvents.Location = new System.Drawing.Point(7, 20);
            this.listViewEvents.Name = "listViewEvents";
            this.listViewEvents.Size = new System.Drawing.Size(660, 110);
            this.listViewEvents.TabIndex = 0;
            this.listViewEvents.UseCompatibleStateImageBehavior = false;
            this.listViewEvents.View = System.Windows.Forms.View.Details;
            // 
            // columnHeaderSN
            // 
            this.columnHeaderSN.Text = "序号";
            this.columnHeaderSN.Width = 41;
            // 
            // columnHeaderEventName
            // 
            this.columnHeaderEventName.Text = "事件名称";
            this.columnHeaderEventName.Width = 103;
            // 
            // columnHeaderTime
            // 
            this.columnHeaderTime.Text = "时间";
            this.columnHeaderTime.Width = 157;
            // 
            // columnHeaderLongitude
            // 
            this.columnHeaderLongitude.Text = "经度";
            this.columnHeaderLongitude.Width = 113;
            // 
            // columnHeaderLatitude
            // 
            this.columnHeaderLatitude.Text = "纬度";
            this.columnHeaderLatitude.Width = 122;
            // 
            // columnHeaderProjName
            // 
            this.columnHeaderProjName.Text = "项目名称";
            this.columnHeaderProjName.Width = 170;
            // 
            // columnHeaderFileName
            // 
            this.columnHeaderFileName.Text = "文件名";
            this.columnHeaderFileName.Width = 350;
            // 
            // columnHeaderInfo
            // 
            this.columnHeaderInfo.Text = "信息";
            this.columnHeaderInfo.Width = 239;
            // 
            // ctxStrip
            // 
            this.ctxStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayEvent});
            this.ctxStrip.Name = "ctxStrip";
            this.ctxStrip.Size = new System.Drawing.Size(125, 26);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(124, 22);
            this.miReplayEvent.Text = "回放事件";
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // StructProblemBlockInfoDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(699, 457);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.groupBox3);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "StructProblemBlockInfoDlg";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "问题点信息";
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.ctxStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox tbxBlockID;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox tbxAbDays;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox tbxCreateDate;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox tbxAbEventCount;
        private System.Windows.Forms.TextBox tbxNormalDays;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox tbxCloseDate;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox tbxFirstAbDate;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox tbxLastAbEvent;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox tbxLastTest;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox tbxStatus;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox tbxCellDesc;
        private System.Windows.Forms.TextBox tbxPlaceDesc;
        private System.Windows.Forms.TextBox tbxEventDes;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ListView listViewEvents;
        private System.Windows.Forms.ColumnHeader columnHeaderSN;
        private System.Windows.Forms.ColumnHeader columnHeaderEventName;
        private System.Windows.Forms.ColumnHeader columnHeaderLongitude;
        private System.Windows.Forms.ColumnHeader columnHeaderLatitude;
        private System.Windows.Forms.ColumnHeader columnHeaderInfo;
        private System.Windows.Forms.ColumnHeader columnHeaderTime;
        private System.Windows.Forms.ContextMenuStrip ctxStrip;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvent;
        private System.Windows.Forms.ColumnHeader columnHeaderProjName;
        private System.Windows.Forms.ColumnHeader columnHeaderFileName;
        private System.Windows.Forms.TextBox tbCloseReason;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.TextBox tbSettingDate;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox tbEvaluateInfo;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.TextBox tbEvaluateDate;
        private System.Windows.Forms.Label label18;
    }
}