﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTSCellNCellInfo
{
    public partial class SCellNCellSignalInfoForm : MinCloseForm
    {
        public SCellNCellSignalInfoForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            colCi.AspectGetter = new AspectGetterDelegate(getCi);
            colFileName.AspectGetter = new AspectGetterDelegate(getFileName);
            colLac.AspectGetter = new AspectGetterDelegate(getLac);
            colNAvgLev.AspectGetter = new AspectGetterDelegate(getNAvgLev);
            colNMaxLev.AspectGetter = new AspectGetterDelegate(getNMaxLev);
            colNMinLev.AspectGetter = new AspectGetterDelegate(getNMinLev);

            this.colPointNum.AspectGetter = delegate(object row)
            {
                if (row is NCellInfo)
                {
                    NCellInfo cell = row as NCellInfo;
                    return cell.TestPointNum;
                }
                return null;
            };

            this.colSAvgLev.AspectGetter = delegate(object row)
            {
                if (row is NCellInfo)
                {
                    NCellInfo cell = row as NCellInfo;
                    return cell.SCell.AvgLev;
                }
                return null;
            };

            this.colCell.AspectGetter = delegate(object row)
            {
                if (row is SCellInfo)
                {
                    SCellInfo cell = row as SCellInfo;
                    return cell.Cell.Name;
                }
                if (row is NCellInfo)
                {
                    NCellInfo cell = row as NCellInfo;
                    return cell.Cell.Name;
                }
                return null;
            };

            this.colSMaxLev.AspectGetter = delegate(object row)
            {
                if (row is NCellInfo)
                {
                    NCellInfo cell = row as NCellInfo;
                    return cell.SCell.MaxLev;
                }
                return null;
            };

            this.colSMinLev.AspectGetter = delegate(object row)
            {
                if (row is NCellInfo)
                {
                    NCellInfo cell = row as NCellInfo;
                    return cell.SCell.MinLev;
                }
                return null;
            };

            this.colDistance.AspectGetter = delegate(object row)
            {
                if (row is NCellInfo)
                {
                    NCellInfo cell = row as NCellInfo;
                    return cell.Distance2SCell;
                }
                return null;
            };

            tvCell.CanExpandGetter = delegate(object row)
            {
                return row is SCellInfo;
            };

            tvCell.ChildrenGetter = delegate(object row)
            {
                if (row is SCellInfo)
                {
                    return (row as SCellInfo).NCells;
                }
                return null;
            };
        }

        private static object getNMinLev(object row)
        {
            if (row is NCellInfo)
            {
                NCellInfo cell = row as NCellInfo;
                return cell.MinLev;
            }
            return null;
        }

        private static object getNMaxLev(object row)
        {
            if (row is NCellInfo)
            {
                NCellInfo cell = row as NCellInfo;
                return cell.MaxLev;
            }
            return null;
        }

        private static object getNAvgLev(object row)
        {
            if (row is NCellInfo)
            {
                NCellInfo cell = row as NCellInfo;
                return cell.AvgLev;
            }
            return null;
        }

        private object getCi(object row)
        {
            if (row is SCellInfo)
            {
                ICell cell = (row as SCellInfo).Cell;
                return getCiOrEci(cell);
            }
            else if (row is NCellInfo)
            {
                ICell cell = (row as NCellInfo).Cell;
                return getCiOrEci(cell);
            }
            return null;
        }

        private object getFileName(object row)
        {
            if (row is SCellInfo)
            {
                SCellInfo x = row as SCellInfo;
                return x.FileName;
            }
            return null;
        }

        private object getLac(object row)
        {
            if (row is SCellInfo)
            {
                ICell cell = (row as SCellInfo).Cell;
                return getLacOrTac(cell);
            }
            else if (row is NCellInfo)
            {
                ICell cell = (row as NCellInfo).Cell;
                return getLacOrTac(cell);
            }
            return null;
        }

        private static object getCiOrEci(ICell cell)
        {
            if (cell is Cell)
            {
                return (cell as Cell).CI;
            }
            else if (cell is TDCell)
            {
                return (cell as TDCell).CI;
            }
            else if (cell is LTECell)
            {
                return (cell as LTECell).ECI;
            }
            return null;
        }

        private static object getLacOrTac(ICell cell)
        {
            if (cell is Cell)
            {
                return (cell as Cell).LAC;
            }
            else if (cell is TDCell)
            {
                return (cell as TDCell).LAC;
            }
            else if (cell is LTECell)
            {
                return (cell as LTECell).TAC;
            }
            return null;
        }

        public void FillData(List<SCellInfo> cells)
        {
            tvCell.ClearObjects();
            tvCell.SetObjects(cells);
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            tvCell.ExpandAll();
        }

        private void miCloseAll_Click(object sender, EventArgs e)
        {
            tvCell.CollapseAll();

        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            tvCell.ExpandAll();
            ExcelNPOIManager.ExportToExcel(tvCell);
        }

        private void tvCell_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo info = tvCell.OlvHitTest(e.X, e.Y);
            if (info.RowIndex < 0)
            {
                return;
            }

            object row = info.RowObject;
            MainModel.ClearDTData();
            MainModel.SelectedCells.Clear();
            MainModel.SelectedTDCells.Clear();
            List<TestPoint> tps = new List<TestPoint>();
            if (row is SCellInfo)
            {
                SCellInfo sCell = row as SCellInfo;
                addSCell(sCell.Cell);
                foreach (NCellInfo item in sCell.NCells)
                {
                    addNCell(item);
                    dealTPs(tps, item);
                }
            }
            else if (row is NCellInfo)
            {
                NCellInfo item = row as NCellInfo;
                addNCell(item);
                ICell sCell = item.SCell.Cell;
                addSCell(sCell);
                dealTPs(tps, item);
            }
            foreach (TestPoint tp in tps)
            {
                MainModel.DTDataManager.Add(tp);
            }
            MainModel.FireDTDataChanged(this);
            MainModel.FireSetDefaultMapSerialTheme("GSM RxLevSub");
        }

        private static void dealTPs(List<TestPoint> tps, NCellInfo item)
        {
            foreach (TestPoint tp in item.TestPoints)
            {
                if (!tps.Contains(tp))
                {
                    tps.Add(tp);
                }
            }
        }

        private void addSCell(ICell sCell)
        {
            if (sCell is Cell)
            {
                MainModel.SelectedCells.Add(sCell as Cell);
            }
            else if (sCell is TDCell)
            {
                MainModel.SelectedTDCells.Add(sCell as TDCell);
            }
        }

        private void addNCell(NCellInfo item)
        {
            if (item.Cell is Cell)
            {
                MainModel.SelectedCells.Add(item.Cell as Cell);
            }
            else if (item.Cell is TDCell)
            {
                MainModel.SelectedTDCells.Add(item.Cell as TDCell);
            }
        }
    }
}
