﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class TestAlarmForm : MinCloseForm
    {
        public TestAlarmForm()
        {
            InitializeComponent();
            areaResultPanel.LogItemSrc = this.LogItemSrc;
        }

        public void FillData(AnaDealerBase anaDealer, Dictionary<AreaBase, CAreaSummary> areaSummaryMap)
        {
            areaResultPanel.Init(anaDealer);
            areaResultPanel.FillData(areaSummaryMap);
        }
    }
}
