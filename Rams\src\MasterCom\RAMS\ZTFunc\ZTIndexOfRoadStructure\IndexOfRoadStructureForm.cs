﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Grid;
using MasterCom.MControls;
using MasterCom.Util;

using System.IO;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class IndexOfRoadStructureForm : MinCloseForm
    {
        MapForm mapForm;
        public IndexOfRoadStructureForm(MapForm mapForm)
            : base(mapForm != null ? mapForm.MainModel : null)
        {
            InitializeComponent();
            this.mapForm = mapForm;
        }

        public void FillData()
        {
            List<GridForIndexOfRoadStructure> gridList = new List<GridForIndexOfRoadStructure>();
            if (cbxBandType.SelectedIndex == 0)
            {
                addGridList(gridList, BTSBandType.GSM900);
            }
            else if (cbxBandType.SelectedIndex == 1)
            {
                addGridList(gridList, BTSBandType.DSC1800);
            }
            BindingSource source = new BindingSource();
            source.DataSource = gridList;
            gridControlGrid.DataSource = source;
            gridControlGrid.RefreshDataSource();
            gridViewGrid.SelectRow(0);
            gridViewGrid_FocusedRowChanged(null, null);
        }

        private void addGridList(List<GridForIndexOfRoadStructure> gridList, BTSBandType type)
        {
            if (MainModel.GridForIndexOfRoadStructureDic.ContainsKey(type))
            {
                foreach (GridForIndexOfRoadStructure grid in MainModel.GridForIndexOfRoadStructureDic[type])
                {
                    if (grid.IndexOfStructure >= (double)edtGridStructuralIndex.Value)
                    {
                        gridList.Add(grid);
                    }
                }
            }
        }

        public void SelectGrid(GridForIndexOfRoadStructure gridSel)
        {
            for (int i = 0; i < gridViewGrid.RowCount; i++)
            {
                object row = gridViewGrid.GetRow(i);
                if (row is GridForIndexOfRoadStructure)
                {
                    GridForIndexOfRoadStructure grid = row as GridForIndexOfRoadStructure;
                    if (grid.Equals(gridSel))
                    {
                        gridViewGrid.SelectRow(i);
                        gridViewGrid.FocusedRowHandle = i;
                        break;
                    }
                }
            }
        }

        private void fillSample()
        {
            if (gridViewGrid.SelectedRowsCount > 0)
            {
                List<SampleForIndexOfRoadStructure> sampleList = new List<SampleForIndexOfRoadStructure>();
                object row = gridViewGrid.GetRow(gridViewGrid.GetSelectedRows()[0]);
                if (row is GridForIndexOfRoadStructure)
                {
                    GridForIndexOfRoadStructure grid = row as GridForIndexOfRoadStructure;
                    foreach (SampleForIndexOfRoadStructure sample in grid.SampleList)
                    {
                        if (sample.IndexOfStructure >= (double)edtSampleStructuralIndex.Value)
                        {
                            sampleList.Add(sample);
                        }
                    }
                }
                BindingSource source = new BindingSource();
                source.DataSource = sampleList;
                gridControlSample.DataSource = source;
                gridControlSample.RefreshDataSource();
                gridViewSample.SelectRow(0);
                gridViewSample_FocusedRowChanged(null, null);
            }
        }

        private void fillCell()
        {
            if (gridViewSample.SelectedRowsCount > 0)
            {
                List<CellOfSampleForIndexOfRoadStructure> cellList = new List<CellOfSampleForIndexOfRoadStructure>();
                object row = gridViewSample.GetRow(gridViewSample.GetSelectedRows()[0]);
                if (row is SampleForIndexOfRoadStructure)
                {
                    SampleForIndexOfRoadStructure sample = row as SampleForIndexOfRoadStructure;
                    cellList = sample.CellList;
                }
                BindingSource source = new BindingSource();
                source.DataSource = cellList;
                gridControlCell.DataSource = source;
                gridControlCell.RefreshDataSource();
            }
        }

        private void cbxBandType_SelectedIndexChanged(object sender, EventArgs e)
        {
            MainModel.GridForIndexOfRoadStructureShowBandType = cbxBandType.SelectedIndex == 0 ? BTSBandType.GSM900 : BTSBandType.DSC1800;
            FillData();
        }

        private void edtGridStructuralIndex_ValueChanged(object sender, EventArgs e)
        {
            FillData();
        }

        private void edtSampleStructuralIndex_ValueChanged(object sender, EventArgs e)
        {
            fillSample();
        }

        private void gridViewGrid_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            fillSample();
        }

        private void gridViewSample_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            fillCell();
        }

        private void btnColorSetting_Click(object sender, EventArgs e)
        {
            ColorRangeMngDlg mngDlg = new ColorRangeMngDlg();
            mngDlg.FixMinMax(0, 200);
            mngDlg.MakeRangeModeOnly();
            mngDlg.FillColorRanges(MainModel.GridForIndexOfRoadStructureColorRanges);
            if (DialogResult.OK == mngDlg.ShowDialog(this))
            {
                MainModel.GridForIndexOfRoadStructureColorRanges = mngDlg.ColorRanges;
                MainModel.RefreshLegend();
                MapForm mf = MainModel.MainForm.GetMapForm();
                if (mf != null)
                {
                    mf.gridForIndexOfRoadStructureRanges.GridForIndexOfRoadStructureColorRanges = mngDlg.ColorRanges;
                    mf.GetIndexOfRoadStructureLayer().Invalidate();
                }
            }
        }

        private void gridControlGrid_DoubleClick(object sender, EventArgs e)
        {
            if (gridViewGrid.SelectedRowsCount > 0)
            {
                object row = gridViewGrid.GetRow(gridViewGrid.GetSelectedRows()[0]);
                if (row is GridForIndexOfRoadStructure)
                {
                    GridForIndexOfRoadStructure grid = row as GridForIndexOfRoadStructure;
                    MainModel.curGridForIndexOfRoadStructure = grid;
                    mapForm.GoToView(grid.MidLongitude, grid.MidLatitude);
                }
            }
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            List<List<NPOIRow>> rowsList = new List<List<NPOIRow>>();
            List<string> sheetNames = new List<string>();
            int rowCount = 0;
            bool overFlow = false;
            foreach (BTSBandType bandType in MainModel.GridForIndexOfRoadStructureDic.Keys)
            {
                if (overFlow)
                {
                    break;
                }
                sheetNames.Add(bandType == BTSBandType.GSM900 ? "900" : "1800");
                List<GridForIndexOfRoadStructure> gridList = MainModel.GridForIndexOfRoadStructureDic[bandType];
                List<NPOIRow> rows = new List<NPOIRow>();
                NPOIRow row = new NPOIRow();
                row.AddCellValue("序号");
                row.AddCellValue("栅格结构指数");
                row.AddCellValue("栅格经度");
                row.AddCellValue("栅格纬度");
                for (int index = 0; index <= 7; index++)
                {
                    row.AddCellValue("Rxquality_" + index);
                }
                row.AddCellValue("文件名");
                row.AddCellValue("经度");
                row.AddCellValue("纬度");
                row.AddCellValue("经纬度结构指数");
                for (int i = 0; i < 48; i++)
                {
                    row.AddCellValue("小区名" + (i + 1));
                    row.AddCellValue("BCCH" + (i + 1));
                    row.AddCellValue("BSIC" + (i + 1));
                    row.AddCellValue("场强" + (i + 1));
                    row.AddCellValue("TCH数" + (i + 1));
                }
                rows.Add(row);
                rowCount++;
                addRowContent(ref rowCount, ref overFlow, gridList, rows, ref row);
                rowsList.Add(rows);
            }
            if (overFlow)
            {
                if (XtraMessageBox.Show("数据超过65536行，是否导出到TXT?", "提示", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    exportToTXT();
                }
                return;
            }
            ExcelNPOIManager.ExportToExcel(rowsList, sheetNames);
        }

        private void addRowContent(ref int rowCount, ref bool overFlow, List<GridForIndexOfRoadStructure> gridList, List<NPOIRow> rows, ref NPOIRow row)
        {
            for (int i = 0; i < gridList.Count; i++)
            {
                if (overFlow)
                {
                    break;
                }
                GridForIndexOfRoadStructure grid = gridList[i];
                row = new NPOIRow();
                row.AddCellValue(i + 1);
                row.AddCellValue(grid.IndexOfStructure);
                row.AddCellValue(grid.MidLongitude);
                row.AddCellValue(grid.MidLatitude);
                for (int index = 1; index <= 8; index++)
                {
                    string formula = "Mx_5A01050" + index;
                    row.AddCellValue(grid.rxqualityDic[formula]);
                }
                foreach (SampleForIndexOfRoadStructure sample in grid.SampleList)
                {
                    if (overFlow)
                    {
                        break;
                    }
                    NPOIRow subRow = dealSubRow(ref rowCount, ref overFlow, sample);
                    row.AddSubRow(subRow);
                }
                rows.Add(row);
            }
        }

        private NPOIRow dealSubRow(ref int rowCount, ref bool overFlow, SampleForIndexOfRoadStructure sample)
        {
            NPOIRow subRow = new NPOIRow();
            subRow.AddCellValue(sample.FileName);
            subRow.AddCellValue(sample.Longitude);
            subRow.AddCellValue(sample.Latitude);
            subRow.AddCellValue(sample.IndexOfStructure);
            int index = 0;
            foreach (CellOfSampleForIndexOfRoadStructure cell in sample.CellList)
            {
                if (overFlow)
                {
                    break;
                }
                else if (++index > 48)
                {
                    break;
                }
                subRow.AddCellValue(cell.CellName);
                subRow.AddCellValue(cell.BCCH);
                subRow.AddCellValue(cell.BSIC);
                subRow.AddCellValue(cell.RxLev);
                subRow.AddCellValue(cell.TCHCount);
                if (++rowCount > 65536)
                {
                    overFlow = true;
                }
            }

            return subRow;
        }

        private void miExportToSHP_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveDlg = new SaveFileDialog();
            saveDlg.RestoreDirectory = false;
            saveDlg.Filter = FilterHelper.Shp;
            if (saveDlg.ShowDialog() == DialogResult.OK)
            {
                WaitBox.Show("开始导出到ShapeFile...", mapForm.GetIndexOfRoadStructureLayer().MakeShpFile, saveDlg.FileName);
            }
        }

        private void miExportTXT_Click(object sender, EventArgs e)
        {
            exportToTXT();
        }

        private void exportToTXT()
        {
            SaveFileDialog saveDlg = new SaveFileDialog();
            saveDlg.RestoreDirectory = false;
            saveDlg.Filter = "文本文档 (*.txt)|*.txt";
            if (saveDlg.ShowDialog() == DialogResult.OK)
            {
                WaitBox.Show("开始导出到TXT...", exportToTXT, saveDlg.FileName);
            }
        }

        private void exportToTXT(object obj)
        {
            try
            {
                string fileName = obj.ToString();
                int index = fileName.LastIndexOf(".");
                string firstPart = fileName.Substring(0, index);
                string lastPart = fileName.Substring(index, fileName.Length - index);
                foreach (BTSBandType bandType in MainModel.GridForIndexOfRoadStructureDic.Keys)
                {
                    string band = bandType == BTSBandType.GSM900 ? "900" : "1800";
                    WaitBox.Text = "正在导出[" + band + "]到TXT...";
                    fileName = firstPart + "_" + band + lastPart;
                    exportTxt(fileName, MainModel.GridForIndexOfRoadStructureDic[bandType]);
                }
                XtraMessageBox.Show("导出成功。");
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void exportTxt(string fileName, List<GridForIndexOfRoadStructure> gridList)
        {
            FileStream fStream = new FileStream(fileName, FileMode.Create, FileAccess.Write, FileShare.Read);
            StreamWriter sWriter = new StreamWriter(fStream, Encoding.Default);
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.Append("序号\t");
                sb.Append("栅格结构指数\t");
                sb.Append("栅格经度\t");
                sb.Append("栅格纬度\t");
                for (int index = 0; index <= 7; index++)
                {
                    sb.Append("Rxquality_" + index + "\t");
                }
                sb.Append("文件名\t");
                sb.Append("经度\t");
                sb.Append("纬度\t");
                sb.Append("经纬度结构指数\t");
                for (int i = 0; i < 20; i++)
                {
                    sb.Append("小区名" + (i + 1) + "\t");
                    sb.Append("BCCH" + (i + 1) + "\t");
                    sb.Append("BSIC" + (i + 1) + "\t");
                    sb.Append("场强" + (i + 1) + "\t");
                    sb.Append("TCH数" + (i + 1) + "\t");
                }
                sWriter.WriteLine(sb.ToString());
                sb.Remove(0, sb.Length);

                for (int i = 0; i < gridList.Count; i++)
                {
                    GridForIndexOfRoadStructure grid = gridList[i];

                    addSampleInfo(sWriter, sb, i, grid);

                    WaitBox.ProgressPercent = (int)(100.0 * (i + 1) / gridList.Count);
                }
            }
            finally
            {
                sWriter.Close();
                fStream.Close();
            }
        }

        private static void addSampleInfo(StreamWriter sWriter, StringBuilder sb, int i, GridForIndexOfRoadStructure grid)
        {
            bool firstSample = true;
            foreach (SampleForIndexOfRoadStructure sample in grid.SampleList)
            {
                if (firstSample)
                {
                    sb.Append((i + 1) + "\t");
                    sb.Append(grid.IndexOfStructure + "\t");
                    sb.Append(grid.MidLongitude + "\t");
                    sb.Append(grid.MidLatitude + "\t");
                    for (int index = 1; index <= 8; index++)
                    {
                        string formula = "Mx_5A01050" + index;
                        sb.Append(grid.rxqualityDic[formula] + "\t");
                    }
                }
                else
                {
                    sb.Append("\t");
                    sb.Append("\t");
                    sb.Append("\t");
                    sb.Append("\t");
                    for (int index = 1; index <= 8; index++)
                    {
                        sb.Append("\t");
                    }
                }
                sb.Append(sample.FileName + "\t");
                sb.Append(sample.Longitude + "\t");
                sb.Append(sample.Latitude + "\t");
                sb.Append(sample.IndexOfStructure + "\t");
                foreach (CellOfSampleForIndexOfRoadStructure cell in sample.CellList)
                {
                    sb.Append(cell.CellName + "\t");
                    sb.Append(cell.BCCH + "\t");
                    sb.Append(cell.BSIC + "\t");
                    sb.Append(cell.RxLev + "\t");
                    sb.Append(cell.TCHCount + "\t");
                }
                if (firstSample)
                {
                    firstSample = false;
                }
                sWriter.WriteLine(sb.ToString());
                sb.Remove(0, sb.Length);
            }
        }
    }

    public class GridForIndexOfRoadStructureRanges
    {
        public GridForIndexOfRoadStructureRanges()
        {
            initialize();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> colorParams = new List<object>();
                param["GridForIndexOfRoadStructureRanges"] = colorParams;
                foreach (ColorRange cr in GridForIndexOfRoadStructureColorRanges)
                {
                    colorParams.Add(cr.Param);
                }
                return param;
            }
            set
            {
                GridForIndexOfRoadStructureColorRanges.Clear();
                List<object> colorParams = (List<object>)value["GridForIndexOfRoadStructureRanges"];
                foreach (object o in colorParams)
                {
                    Dictionary<string, object> colorParam = (Dictionary<string, object>)o;
                    ColorRange cr = new ColorRange();
                    cr.Param = colorParam;
                    GridForIndexOfRoadStructureColorRanges.Add(cr);
                }
            }
        }

        private void initialize()
        {
            GridForIndexOfRoadStructureColorRanges.Clear();
            GridForIndexOfRoadStructureColorRanges.Add(new ColorRange(0, 0.1f, Color.FromArgb(0, 255, 255)));
            GridForIndexOfRoadStructureColorRanges.Add(new ColorRange(0.1f, 0.3f, Color.FromArgb(0, 255, 0)));
            GridForIndexOfRoadStructureColorRanges.Add(new ColorRange(0.3f, 0.5f, Color.FromArgb(255, 255, 0)));
            GridForIndexOfRoadStructureColorRanges.Add(new ColorRange(0.5f, 1, Color.FromArgb(255, 128, 0)));
            GridForIndexOfRoadStructureColorRanges.Add(new ColorRange(1, 2, Color.FromArgb(255, 0, 0)));
        }

        public List<ColorRange> GridForIndexOfRoadStructureColorRanges { get; set; } = new List<ColorRange>();
    }
}
