﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;

namespace MasterCom.RAMS.Net
{
    public class ZTQuerryLTEGridCoverAna : DIYGridQuery
    {
        public string themeName { get; set; }
        public ZTQuerryLTEGridCoverAna(MainModel mainModel)
            : base(mainModel)
        {
            themeName = "TD_LTE_RSRP";
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "综合覆盖率_LTE(按栅格)"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22079, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            this.AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            return isContainDbRect(grid.Bounds);
        }
        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            return getTriadIDIgnoreServiceType(new string[] {
            strLTRsrp,strLTSinr,strDXRsrp,strDXSinr
            ,"Lte_61212D0109","Lte_61212C0103"});
        }

        #region 全局变量
        List<int> iCarrList = null;
        string strCarrName = "";
        string strLTRsrp = "";
        string strLTSinr = "";
        string strDXRsrp = "";
        string strDXSinr = "";
        DTDataHeaderManager headerManager = null;
        Dictionary<string, GridMatrix<ColorUnit>> lteGridInfoDic = null;
        Dictionary<string, LTEGridCoverInfo> cityLteCoverRateInfoDic = null;
        public List<LTEGridCoverInfo> lteCoverageRateInfoList { get; set; }
        Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic = null;
        string strCityName = "";
        string strStartTime = "";
        #endregion

        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            strLTRsrp = "Lf_612D0309";
            strLTSinr = "Lf_612D0403";
            strDXRsrp = "Lf_612D0309";
            strDXSinr = "Lf_612D0403";
            if (condition.Periods[0].BeginTime.ToString("yyyy-MM-dd").Contains("2014-"))
            {
                strLTRsrp = "Lte_61212D0209";
                strLTSinr = "Lte_61212C0203";
                strDXRsrp = "Lte_61212D0309";
                strDXSinr = "Lte_61212C0303";
            }
            iCarrList = new List<int>();
            iCarrList.AddRange(condition.CarrierTypes);
            strStartTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            lteGridInfoDic = new Dictionary<string, GridMatrix<ColorUnit>>();
            lteCoverageRateInfoList = new List<LTEGridCoverInfo>();
            cityLteCoverRateInfoDic = new Dictionary<string, LTEGridCoverInfo>();
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            InitRegionMop2();
            foreach (int districtID in condition.DistrictIDs)
            {
                strCityName = DistrictManager.GetInstance().getDistrictName(districtID);
                queryDistrictData(districtID);
                lteCoverageRateInfoList.Add(cityLteCoverRateInfoDic[strCityName]);
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                lteGridInfoDic = new Dictionary<string, GridMatrix<ColorUnit>>();
            }
            afterRecieveAllData();
            fireShowResult();
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;

                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                WaitBox.Text = strCityName + " 开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();

                string statImgIDSet = getStatImgNeededTriadID();
                foreach (int iCarr in iCarrList)
                {
                    if (iCarr == 1)
                    {
                        strCarrName = " 移动 ";
                    }
                    else if (iCarr == 2)
                    {
                        strCarrName = " 联通 ";
                    }
                    else
                    {
                        strCarrName = " 电信 ";
                    }
                    condition.CarrierTypes.Clear();
                    condition.CarrierTypes.Add(iCarr);
                    foreach (TimePeriod period in condition.Periods)
                    {
                        queryPeriodInfo(period, clientProxy, statImgIDSet, lteGridInfoDic);
                    }

                    changGridDataFormula();

                    lteGridInfoDic.Clear();
                    MainModel.DTDataManager.Clear();
                    MainModel.SelectedTestPoints.Clear();
                    MainModel.SelectedEvents.Clear();
                    MainModel.SelectedMessage = null;
                    MainModel.CurGridCoverData = null;
                    MainModel.CurGridColorUnitMatrix = null;
                    MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                }
                WaitBox.Text = strCityName + " 数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            headerManager = new DTDataHeaderManager();
            headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = strCityName + " 正在从服务器接收" + strCarrName + "数据...";
                }
                recved = true;
                KPIStatDataBase singleStatData = null;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader header = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    headerManager.AddDTDataHeader(header);
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(package, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref counter, ref curPercent);
            }
        }

        private void fillData(Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            fillStatData(package, curImgColumnDef, singleStatData);
            string strKey = strCarrierSevervicKey(singleStatData);
            if (strKey.Split(',').Length >= 2)
            {
                ColorUnit cu = new ColorUnit();
                cu.LTLng = lng;
                cu.LTLat = lat;
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);
                if (!lteGridInfoDic.ContainsKey(strKey))
                {
                    lteGridInfoDic.Add(strKey, new GridMatrix<ColorUnit>());
                }
                cu = lteGridInfoDic[strKey][rAt, cAt];

                if (cu == null)
                {
                    if (isValidStatImg(lng, lat))
                    {
                        cu = new ColorUnit();
                        cu.LTLng = lng;
                        cu.LTLat = lat;
                        lteGridInfoDic[strKey][rAt, cAt] = cu;
                        cu.Status = 1;
                        cu.DataHub.AddStatData(singleStatData, false);
                    }
                }
                else
                {
                    cu.Status = 1;
                    cu.DataHub.AddStatData(singleStatData, false);
                }
            }
        }

        private string strCarrierSevervicKey(KPIStatDataBase singleStatDataTmp)
        {
            string strKey = "";
            if (singleStatDataTmp != null)
            {
                bool contians = headerManager.GetHeaderMap().ContainsKey(singleStatDataTmp.FileID);
                if (contians)
                {
                    strKey = setStrKey(singleStatDataTmp, strKey);
                }
            }
            return strKey;
        }

        private string setStrKey(KPIStatDataBase singleStatDataTmp, string strKey)
        {
            DTDataHeader dtFile = headerManager.GetHeaderMap()[singleStatDataTmp.FileID];
            if (dtFile.CarrierType == 1)
            {
                strKey += "移动,";
                if (dtFile.ServiceType == 34)
                {
                    strKey += "数据";
                }
            }
            else if (dtFile.CarrierType == 2)
            {
                strKey += "联通,";
                if (dtFile.ServiceType == 34 || dtFile.ServiceType == 46)
                {
                    strKey += "数据";
                }
            }
            else if (dtFile.CarrierType == 3)
            {
                strKey += "电信,";
                if (dtFile.ServiceType == 34 || dtFile.ServiceType == 46)
                {
                    strKey += "数据";
                }
            }
            if (dtFile.ServiceType == 41 || dtFile.ServiceType == 47)
            {
                strKey += "空闲";
            }

            return strKey;
        }

        private void changGridDataFormula()
        {
            if (!cityLteCoverRateInfoDic.ContainsKey(strCityName))
            {
                LTEGridCoverInfo lteCoverRateInfo = new LTEGridCoverInfo();
                lteCoverRateInfo.StrCityName = strCityName;
                cityLteCoverRateInfoDic.Add(strCityName, lteCoverRateInfo);
            }
            foreach (string strKey in lteGridInfoDic.Keys)
            {
                string[] key = strKey.Split(',');
                foreach (ColorUnit cu in lteGridInfoDic[strKey])
                {
                    StatDataLTE dataStatTDD = cu.DataHub.GetStatData(typeof(StatDataLTE)) as StatDataLTE;
                    StatDataLTE_FDD dataStatFDD = cu.DataHub.GetStatData(typeof(StatDataLTE_FDD)) as StatDataLTE_FDD;
                    if (dataStatTDD == null && dataStatFDD == null)
                    {
                        continue;
                    }
                    CenterLongLat cll = new CenterLongLat(cu.CenterLng, cu.CenterLat);
                    if (key[0].Equals("移动"))
                    {
                        doLTEYDStat(key, cu, cll);
                    }
                    else if (key[0].Equals("联通"))
                    {
                        doLTELTStat(key, cu);
                    }
                    else if (key[0].Equals("电信"))
                    {
                        doLTEDXStat(key, cu);
                    }
                    cu.DataHub = new StatDataHubBase();
                }
            }
            fillDataAndClearCache();
        }

        private void fillDataAndClearCache()
        {
            if (condition.CarrierTypes.Contains(1))
            {
                cityLteCoverRateInfoDic[strCityName].IYdUploadDownloadAll = cityLteCoverRateInfoDic[strCityName].YdUploadDownloadAll.Count;
                cityLteCoverRateInfoDic[strCityName].YdUploadDownloadAll.Clear();
                cityLteCoverRateInfoDic[strCityName].IYdUploadDownloadCover = cityLteCoverRateInfoDic[strCityName].YdUploadDownloadCover.Count;
                cityLteCoverRateInfoDic[strCityName].YdUploadDownloadCover.Clear();
                cityLteCoverRateInfoDic[strCityName].IYdUploadDownloadCover110 = cityLteCoverRateInfoDic[strCityName].YdUploadDownloadCover110.Count;
                cityLteCoverRateInfoDic[strCityName].YdUploadDownloadCover110.Clear();
            }
        }

        private void doLTEYDStat(string[] key, ColorUnit cu, CenterLongLat cll)
        {
            float fRsrp = (float)cu.DataHub.CalcValueByFormula("Lte_61212D0109");
            float fSinr = (float)cu.DataHub.CalcValueByFormula("Lte_61212C0103");
            if (key[1].Equals("空闲"))
            {
                dealIdleType(fRsrp, fSinr);
            }
            if (key[1].Contains("数据"))
            {
                dealDataType(cu, cll, fRsrp, fSinr);
            }
            if (key[1].Contains("上传"))
            {
                cityLteCoverRateInfoDic[strCityName].IYDUploadAllCount++;
                if (judgeRsrp100(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].IYDUploadCoverCount++;
                }
                if (judgeRsrp100(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].IYDUploadCoverCount110++;
                }
            }
            if (key[1].Contains("下载"))
            {
                cityLteCoverRateInfoDic[strCityName].IYDDownloaAllCount++;
                if (judgeRsrp100(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].IYDDownloaCoverCount++;
                }
                if (judgeRsrp110(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].IYDDownloaCoverCount110++;
                }
            }
        }

        private void dealIdleType(float fRsrp, float fSinr)
        {
            cityLteCoverRateInfoDic[strCityName].IYDIDLEAllCount++;
            if (judgeRsrp100(fRsrp, fSinr))
            {
                cityLteCoverRateInfoDic[strCityName].IYDIDLECoverCount++;
            }
            if (judgeRsrp100(fRsrp, fSinr))
            {
                cityLteCoverRateInfoDic[strCityName].IYDIDLECoverCount110++;
            }
        }

        private void dealDataType(ColorUnit cu, CenterLongLat cll, float fRsrp, float fSinr)
        {
            if (!cityLteCoverRateInfoDic[strCityName].YdUploadDownloadAll.Contains(cll))
            {
                cityLteCoverRateInfoDic[strCityName].YdUploadDownloadAll.Add(cll);
            }
            if (judgeRsrp100(fRsrp, fSinr)
                && !cityLteCoverRateInfoDic[strCityName].YdUploadDownloadCover.Contains("" + cu.CenterLng + cu.CenterLat))
            {
                cityLteCoverRateInfoDic[strCityName].YdUploadDownloadCover.Add("" + cu.CenterLng + cu.CenterLat);
            }
            if (judgeRsrp100(fRsrp, fSinr)
                && !cityLteCoverRateInfoDic[strCityName].YdUploadDownloadCover110.Contains("" + cu.CenterLng + cu.CenterLat))
            {
                cityLteCoverRateInfoDic[strCityName].YdUploadDownloadCover110.Add("" + cu.CenterLng + cu.CenterLat);
            }
        }

        private static bool judgeRsrp100(float fRsrp, float fSinr)
        {
            return fRsrp >= -100 && fRsrp < 0 && fSinr >= -3 && fSinr < 40;
        }

        private static bool judgeRsrp110(float fRsrp, float fSinr)
        {
            return fRsrp >= -110 && fRsrp < 0 && fSinr >= -3 && fSinr < 40;
        }

        private void doLTELTStat(string[] key, ColorUnit cu)
        {
            float fRsrp = (float)cu.DataHub.CalcValueByFormula(strLTRsrp);
            float fSinr = (float)cu.DataHub.CalcValueByFormula(strLTSinr);
            if (key[1].Equals("空闲"))
            {
                cityLteCoverRateInfoDic[strCityName].ILTIDLEAllCount++;
                if (judgeRsrp100(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].ILTIDLECoverCount++;
                }
                if (judgeRsrp100(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].ILTIDLECoverCount110++;
                }
            }
            else if (key[1].Equals("数据"))
            {
                cityLteCoverRateInfoDic[strCityName].ILtUploadDownloadAll++;
                if (judgeRsrp100(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].ILtUploadDownloadCover++;
                }
                if (judgeRsrp100(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].ILtUploadDownloadCover110++;
                }
            }
        }

        private void doLTEDXStat(string[] key, ColorUnit cu)
        {
            float fRsrp = (float)cu.DataHub.CalcValueByFormula(strDXRsrp);
            float fSinr = (float)cu.DataHub.CalcValueByFormula(strDXSinr);
            if (key[1].Equals("空闲"))
            {
                cityLteCoverRateInfoDic[strCityName].IDXIDLEAllCount++;
                if (judgeRsrp100(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].IDXIDLECoverCount++;
                }
                if (judgeRsrp100(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].IDXIDLECoverCount110++;
                }
            }
            else if (key[1].Equals("数据"))
            {
                cityLteCoverRateInfoDic[strCityName].IDxUploadDownloadAll++;
                if (judgeRsrp100(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].IDxUploadDownloadCover++;
                }
                if (judgeRsrp100(fRsrp, fSinr))
                {
                    cityLteCoverRateInfoDic[strCityName].IDxUploadDownloadCover110++;
                }
            }
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            // Method intentionally left empty.
        }

        /// <summary>
        /// 获取预存或圈选的区域
        /// </summary>
        private void InitRegionMop2()
        {
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegionMap(resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic = new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private void addRegionMap(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!mutRegionMopDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                mutRegionMopDic.Add(strGridType, regionMop);
            }
        }

        /// <summary>
        /// 定位所在网格
        /// </summary>
        private bool isContainDbRect(DbRect dRect)
        {
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (!(gridType + grid).Contains(strCityName))
                    {
                        continue;
                    }
                    if (mutRegionMopDic[gridType][grid].CheckRectIntersectWithRegion(dRect))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        protected override void fireShowResult()
        {
            ZTQuerryLTEGridCoverAnaForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(ZTQuerryLTEGridCoverAnaForm).FullName);
            showForm = obj == null ? null : obj as ZTQuerryLTEGridCoverAnaForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new ZTQuerryLTEGridCoverAnaForm(MainModel, strStartTime);
            }
            showForm.FillData(lteCoverageRateInfoList);
            showForm.Show(MainModel.MainForm);
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }
    }
    public class LTEGridCoverInfo
    {
        public string StrCityName { get; set; }
        public int IYDIDLECoverCount { get; set; }
        public int IYDIDLECoverCount110 { get; set; }
        public int IYDIDLEAllCount { get; set; }
        public string StrYDIDLERate
        {
            get
            {
                string strRate = "";
                if (IYDIDLEAllCount > 0)
                {
                    strRate = (100.0 * IYDIDLECoverCount / IYDIDLEAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public string StrYDIDLERate110
        {
            get
            {
                string strRate = "";
                if (IYDIDLEAllCount > 0)
                {
                    strRate = (100.0 * IYDIDLECoverCount110 / IYDIDLEAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public int IYDUploadCoverCount { get; set; }
        public int IYDUploadCoverCount110 { get; set; }
        public int IYDUploadAllCount { get; set; }
        public string StrYDUploadRate
        {
            get
            {
                string strRate = "";
                if (IYDUploadAllCount > 0)
                {
                    strRate = (100.0 * IYDUploadCoverCount / IYDUploadAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public string StrYDUploadRate110
        {
            get
            {
                string strRate = "";
                if (IYDUploadAllCount > 0)
                {
                    strRate = (100.0 * IYDUploadCoverCount110 / IYDUploadAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public int IYDDownloaCoverCount { get; set; }
        public int IYDDownloaCoverCount110 { get; set; }
        public int IYDDownloaAllCount { get; set; }
        public string StrYDDownloadRate
        {
            get
            {
                string strRate = "";
                if (IYDDownloaAllCount > 0)
                {
                    strRate = (100.0 * IYDDownloaCoverCount / IYDDownloaAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public string StrYDDownloadRate110
        {
            get
            {
                string strRate = "";
                if (IYDDownloaAllCount > 0)
                {
                    strRate = (100.0 * IYDDownloaCoverCount110 / IYDDownloaAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public List<string> YdUploadDownloadCover { get; set; } = new List<string>();
        public List<string> YdUploadDownloadCover110 { get; set; } = new List<string>();
        public List<CenterLongLat> YdUploadDownloadAll { get; set; } = new List<CenterLongLat>();
        public int IYdUploadDownloadCover { get; set; }
        public int IYdUploadDownloadCover110 { get; set; }
        public int IYdUploadDownloadAll { get; set; }
        public string StrYDUpDownCoverRate
        {
            get
            {
                string strRate = "";
                if (IYdUploadDownloadAll > 0)
                {
                    strRate = (100.0 * IYdUploadDownloadCover / IYdUploadDownloadAll).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public string StrYDUpDownCoverRate110
        {
            get
            {
                string strRate = "";
                if (IYdUploadDownloadAll > 0)
                {
                    strRate = (100.0 * IYdUploadDownloadCover110 / IYdUploadDownloadAll).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public int ILTIDLECoverCount { get; set; }
        public int ILTIDLECoverCount110 { get; set; }
        public int ILTIDLEAllCount { get; set; }
        public string StrLTIDLERate
        {
            get
            {
                string strRate = "";
                if (ILTIDLEAllCount > 0)
                {
                    strRate = (100.0 * ILTIDLECoverCount / ILTIDLEAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public string StrLTIDLERate110
        {
            get
            {
                string strRate = "";
                if (ILTIDLEAllCount > 0)
                {
                    strRate = (100.0 * ILTIDLECoverCount110 / ILTIDLEAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public int ILTUploadCoverCount { get; set; }
        public int ILTUploadAllCount { get; set; }
        public string StrLTUploadRate
        {
            get
            {
                string strRate = "";
                if (ILTUploadAllCount > 0)
                {
                    strRate = (100.0 * ILTUploadCoverCount / ILTUploadAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public int ILTDownloaCoverCount { get; set; }
        public int ILTDownloaAllCount { get; set; }
        public string StrLTDownloadRate
        {
            get
            {
                string strRate = "";
                if (ILTDownloaAllCount > 0)
                {
                    strRate = (100.0 * ILTDownloaCoverCount / ILTDownloaAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public List<CenterLongLat> LtUploadDownloadCover { get; set; } = new List<CenterLongLat>();
        public List<string> LtUploadDownloadCover110 { get; set; } = new List<string>();
        public List<string> LtUploadDownloadAll { get; set; } = new List<string>();
        public int ILtUploadDownloadCover { get; set; }
        public int ILtUploadDownloadCover110 { get; set; }
        public int ILtUploadDownloadAll { get; set; }
        public string StrLTUpDownCoverRate
        {
            get
            {
                string strRate = "";
                if (ILtUploadDownloadAll > 0)
                {
                    strRate = (100.0 * ILtUploadDownloadCover / ILtUploadDownloadAll).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public string StrLTUpDownCoverRate110
        {
            get
            {
                string strRate = "";
                if (ILtUploadDownloadAll > 0)
                {
                    strRate = (100.0 * ILtUploadDownloadCover110 / ILtUploadDownloadAll).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public int IDXIDLECoverCount { get; set; }
        public int IDXIDLECoverCount110 { get; set; }
        public int IDXIDLEAllCount { get; set; }
        public string StrDXIDLERate
        {
            get
            {
                string strRate = "";
                if (IDXIDLEAllCount > 0)
                {
                    strRate = (100.0 * IDXIDLECoverCount / IDXIDLEAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public string StrDXIDLERate110
        {
            get
            {
                string strRate = "";
                if (IDXIDLEAllCount > 0)
                {
                    strRate = (100.0 * IDXIDLECoverCount110 / IDXIDLEAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public int IDXUploadCoverCount { get; set; }
        public int IDXUploadAllCount { get; set; }
        public string StrDXUploadRate
        {
            get
            {
                string strRate = "";
                if (IDXUploadAllCount > 0)
                {
                    strRate = (100.0 * IDXUploadCoverCount / IDXUploadAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public int IDXDownloaCoverCount { get; set; }
        public int IDXDownloaAllCount { get; set; }
        public string StrDXDownloadRate
        {
            get
            {
                string strRate = "";
                if (IDXDownloaAllCount > 0)
                {
                    strRate = (100.0 * IDXDownloaCoverCount / IDXDownloaAllCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }

        public List<CenterLongLat> DxUploadDownloadCover { get; set; } = new List<CenterLongLat>();
        public List<CenterLongLat> DxUploadDownloadCover110 { get; set; } = new List<CenterLongLat>();
        public List<CenterLongLat> DxUploadDownloadAll { get; set; } = new List<CenterLongLat>();
        public int IDxUploadDownloadCover { get; set; }
        public int IDxUploadDownloadCover110 { get; set; }
        public int IDxUploadDownloadAll { get; set; }
        public string StrDXUpDownCoverRate
        {
            get
            {
                string strRate = "";
                if (IDxUploadDownloadAll > 0)
                {
                    strRate = (100.0 * IDxUploadDownloadCover / IDxUploadDownloadAll).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public string StrDXUpDownCoverRate110
        {
            get
            {
                string strRate = "";
                if (IDxUploadDownloadAll > 0)
                {
                    strRate = (100.0 * IDxUploadDownloadCover110 / IDxUploadDownloadAll).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
    }

    //栅格覆盖率统计*********************************************
    public class ZTQuerryLTEFDDGridCoverAna : ZTQuerryLTEGridCoverAna
    {
        public ZTQuerryLTEFDDGridCoverAna(MainModel mainModel)
            : base(mainModel)
        {
            themeName = "LTE_FDD:RSRP";
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "综合覆盖率_LTE_FDD(按栅格)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22080, this.Name);
        }
    }
}
