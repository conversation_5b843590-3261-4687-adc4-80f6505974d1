﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class MoBlockCallResultListForm : MinCloseForm
    {
        public MoBlockCallResultListForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
            mapForm = MainModel.MainForm.GetMapForm();
            init();
        }

        private MapForm mapForm = null;
        List<MoCallInfo> resultList = new List<MoCallInfo>();

        public void FillData(List<MoCallInfo> resultList)
        {
            this.resultList = resultList;
            ListViewMoBlockCall.RebuildColumns();
            ListViewMoBlockCall.ClearObjects();
            ListViewMoBlockCall.SetObjects(resultList);

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void init()
        {
            #region 界面数据绑定

            this.olvColumnStatSN.AspectGetter = delegate(object row)
            {
                if (row is MoCallInfo)
                {
                    MoCallInfo info = row as MoCallInfo;
                    return info.SN;
                }
                return null;
            };

            this.olvColumnMoFileName.AspectGetter = delegate(object row)
            {
                if (row is MoCallInfo)
                {
                    MoCallInfo info = row as MoCallInfo;
                    return info.FileName;
                }
                return null;
            };

            this.olvColumnDate.AspectGetter = delegate(object row)
            {
                if (row is MoCallInfo)
                {
                    MoCallInfo info = row as MoCallInfo;
                    return info.BeginTime.ToShortDateString();
                }
                return null;
            };

            this.olvColumnTime.AspectGetter = delegate(object row)
            {
                if (row is MoCallInfo)
                {
                    MoCallInfo info = row as MoCallInfo;
                    return info.BeginTime.ToString("HH:mm:ss.fff");
                }
                return null;
            };

            this.olvColumnIsContain.AspectGetter = delegate(object row)
            {
                if (row is MoCallInfo)
                {
                    MoCallInfo info = row as MoCallInfo;
                    return info.IsContainISI2SP;
                }
                return null;
            };

            #endregion
        }

        private void ToolStripReplay_Click(object sender, EventArgs e)
        {
            object rows = ListViewMoBlockCall.GetSelectedObjects();
            if (rows == null)
            {
                MessageBox.Show("请选择要回放的文件");
                return;
            }
            MoCallInfo info = ListViewMoBlockCall.GetSelectedObject() as MoCallInfo;
            if (info == null)
            {
                return;
            }

            MainModel.MainForm.NeedChangeWorkSpace(false);
            PreNextMinutesForm preNextMinuteForm = new PreNextMinutesForm(false);
            preNextMinuteForm.Pre = 2;
            preNextMinuteForm.Next = 2;
            if (preNextMinuteForm.ShowDialog() == DialogResult.OK)
            {
                int pre = preNextMinuteForm.Pre;
                int next = preNextMinuteForm.Next;
                DateTime timeStart = info.BeginTime.AddMinutes(-pre);
                DateTime timeEnd = info.BeginTime.AddMinutes(next);
                FileReplayer.Replay(info.File, new MasterCom.Util.TimePeriod(timeStart, timeEnd));
            }
            else
            {
                MainModel.MainForm.CancelChange = true;
            }
            MainModel.MainForm.ChangeWorkSpace();
        }

        private void ToolStripExport_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(ListViewMoBlockCall);
        }
    }
}
