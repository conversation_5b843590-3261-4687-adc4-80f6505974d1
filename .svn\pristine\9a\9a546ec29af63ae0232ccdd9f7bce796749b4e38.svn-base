﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteVideoPlayAnaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportTp2Csv = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportOtherTp2Csv = new System.Windows.Forms.ToolStripMenuItem();
            this.tabVideoPlay = new System.Windows.Forms.TabControl();
            this.pageLoad = new System.Windows.Forms.TabPage();
            this.gcLoad = new DevExpress.XtraGrid.GridControl();
            this.gvLoad = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.pageRebuffer = new System.Windows.Forms.TabPage();
            this.gcRebuffer = new DevExpress.XtraGrid.GridControl();
            this.gvRebuffer = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStrip.SuspendLayout();
            this.tabVideoPlay.SuspendLayout();
            this.pageLoad.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcLoad)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvLoad)).BeginInit();
            this.pageRebuffer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcRebuffer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRebuffer)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miExport2Xls,
            this.miExportTp2Csv,
            this.miExportOtherTp2Csv});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(214, 92);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(213, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(213, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // miExportTp2Csv
            // 
            this.miExportTp2Csv.Name = "miExportTp2Csv";
            this.miExportTp2Csv.Size = new System.Drawing.Size(213, 22);
            this.miExportTp2Csv.Text = "导出缓冲采样点到Csv...";
            this.miExportTp2Csv.ToolTipText = "按选中区域（如果有）";
            this.miExportTp2Csv.Click += new System.EventHandler(this.miExportTp2Csv_Click);
            // 
            // miExportOtherTp2Csv
            // 
            this.miExportOtherTp2Csv.Name = "miExportOtherTp2Csv";
            this.miExportOtherTp2Csv.Size = new System.Drawing.Size(213, 22);
            this.miExportOtherTp2Csv.Text = "导出非缓冲采样点到Csv...";
            this.miExportOtherTp2Csv.ToolTipText = "按选中区域（如果有）";
            this.miExportOtherTp2Csv.Click += new System.EventHandler(this.miExportOtherTp2Csv_Click);
            // 
            // tabVideoPlay
            // 
            this.tabVideoPlay.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabVideoPlay.Controls.Add(this.pageLoad);
            this.tabVideoPlay.Controls.Add(this.pageRebuffer);
            this.tabVideoPlay.Location = new System.Drawing.Point(3, 3);
            this.tabVideoPlay.Name = "tabVideoPlay";
            this.tabVideoPlay.SelectedIndex = 0;
            this.tabVideoPlay.Size = new System.Drawing.Size(1060, 592);
            this.tabVideoPlay.TabIndex = 3;
            this.tabVideoPlay.SelectedIndexChanged += new System.EventHandler(this.tabVideoPlay_TabSelectedIndexChanged);
            // 
            // pageLoad
            // 
            this.pageLoad.Controls.Add(this.gcLoad);
            this.pageLoad.Location = new System.Drawing.Point(4, 23);
            this.pageLoad.Name = "pageLoad";
            this.pageLoad.Padding = new System.Windows.Forms.Padding(3);
            this.pageLoad.Size = new System.Drawing.Size(1052, 565);
            this.pageLoad.TabIndex = 1;
            this.pageLoad.Text = "流媒体缓冲";
            this.pageLoad.UseVisualStyleBackColor = true;
            // 
            // gcLoad
            // 
            this.gcLoad.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gcLoad.ContextMenuStrip = this.contextMenuStrip;
            this.gcLoad.Location = new System.Drawing.Point(3, 3);
            this.gcLoad.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gcLoad.MainView = this.gvLoad;
            this.gcLoad.Name = "gcLoad";
            this.gcLoad.Size = new System.Drawing.Size(1046, 559);
            this.gcLoad.TabIndex = 4;
            this.gcLoad.UseEmbeddedNavigator = true;
            this.gcLoad.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvLoad});
            // 
            // gvLoad
            // 
            this.gvLoad.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gvLoad.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gvLoad.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gvLoad.ColumnPanelRowHeight = 50;
            this.gvLoad.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44});
            this.gvLoad.GridControl = this.gcLoad;
            this.gvLoad.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvLoad.Name = "gvLoad";
            this.gvLoad.OptionsBehavior.Editable = false;
            this.gvLoad.OptionsDetail.EnableMasterViewMode = false;
            this.gvLoad.OptionsDetail.ShowDetailTabs = false;
            this.gvLoad.OptionsView.ColumnAutoWidth = false;
            this.gvLoad.OptionsView.ShowGroupPanel = false;
            this.gvLoad.DoubleClick += new System.EventHandler(this.gv_DoubleClick);
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "序号";
            this.gridColumn20.FieldName = "SN";
            this.gridColumn20.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 0;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "文件名";
            this.gridColumn21.FieldName = "FileName";
            this.gridColumn21.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 1;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "缓冲时长(s)";
            this.gridColumn22.FieldName = "InterTime";
            this.gridColumn22.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 2;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "URL";
            this.gridColumn23.FieldName = "URL";
            this.gridColumn23.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 3;
            this.gridColumn23.Width = 100;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "流媒体缓冲开始时间";
            this.gridColumn24.FieldName = "BeginTimeStr";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 4;
            this.gridColumn24.Width = 125;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "缓冲事件开始经度";
            this.gridColumn25.FieldName = "BeginLongitude";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 5;
            this.gridColumn25.Width = 125;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "缓冲事件开始纬度";
            this.gridColumn28.FieldName = "BeginLatitude";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 6;
            this.gridColumn28.Width = 125;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "流媒体缓冲结束时间";
            this.gridColumn29.FieldName = "EndTimeStr";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 7;
            this.gridColumn29.Width = 125;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "缓冲事件结束经度";
            this.gridColumn30.FieldName = "EndLongitude";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 8;
            this.gridColumn30.Width = 125;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "缓冲事件结束纬度";
            this.gridColumn31.FieldName = "EndLatitude";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 9;
            this.gridColumn31.Width = 125;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "采样点个数";
            this.gridColumn32.FieldName = "TestPointsCount";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 10;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "RSRP最大值";
            this.gridColumn33.FieldName = "RsrpMax";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 11;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "RSRP最小值";
            this.gridColumn34.FieldName = "RsrpMin";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 12;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "RSRP平均值";
            this.gridColumn35.FieldName = "RsrpAvg";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 13;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "最强邻区最大RSRP";
            this.gridColumn36.FieldName = "MaxNbRsrpMax";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 14;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "最强邻区最小RSRP";
            this.gridColumn37.FieldName = "MaxNbRsrpMin";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 15;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "最强邻区平均RSRP";
            this.gridColumn38.FieldName = "MaxNbRsrpAvg";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 16;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "SINR最大值";
            this.gridColumn39.FieldName = "SinrMax";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 17;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "关联小区名";
            this.gridColumn40.FieldName = "CellNameStr";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 22;
            this.gridColumn40.Width = 163;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "关联TACCI";
            this.gridColumn41.FieldName = "TacCIStr";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 21;
            this.gridColumn41.Width = 153;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "SINR最小值";
            this.gridColumn42.FieldName = "SinrMin";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 18;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "SINR平均值";
            this.gridColumn43.FieldName = "SinrAvg";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 19;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "平均下载速率(Mbps)";
            this.gridColumn44.FieldName = "AppSpeedMbAvg";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 20;
            // 
            // pageRebuffer
            // 
            this.pageRebuffer.Controls.Add(this.gcRebuffer);
            this.pageRebuffer.Location = new System.Drawing.Point(4, 23);
            this.pageRebuffer.Name = "pageRebuffer";
            this.pageRebuffer.Padding = new System.Windows.Forms.Padding(3);
            this.pageRebuffer.Size = new System.Drawing.Size(1052, 565);
            this.pageRebuffer.TabIndex = 0;
            this.pageRebuffer.Text = "流媒体卡顿";
            this.pageRebuffer.UseVisualStyleBackColor = true;
            // 
            // gcRebuffer
            // 
            this.gcRebuffer.ContextMenuStrip = this.contextMenuStrip;
            this.gcRebuffer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcRebuffer.Location = new System.Drawing.Point(3, 3);
            this.gcRebuffer.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gcRebuffer.MainView = this.gvRebuffer;
            this.gcRebuffer.Name = "gcRebuffer";
            this.gcRebuffer.Size = new System.Drawing.Size(1046, 559);
            this.gcRebuffer.TabIndex = 3;
            this.gcRebuffer.UseEmbeddedNavigator = true;
            this.gcRebuffer.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvRebuffer});
            // 
            // gvRebuffer
            // 
            this.gvRebuffer.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gvRebuffer.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gvRebuffer.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gvRebuffer.ColumnPanelRowHeight = 50;
            this.gvRebuffer.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnId,
            this.gridColumnFileName,
            this.gridColumn17,
            this.gridColumn1,
            this.gridColumn27,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn15,
            this.gridColumn4,
            this.gridColumn16,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn9,
            this.gridColumn19,
            this.gridColumn18,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn26});
            this.gvRebuffer.GridControl = this.gcRebuffer;
            this.gvRebuffer.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvRebuffer.Name = "gvRebuffer";
            this.gvRebuffer.OptionsBehavior.Editable = false;
            this.gvRebuffer.OptionsDetail.EnableMasterViewMode = false;
            this.gvRebuffer.OptionsDetail.ShowDetailTabs = false;
            this.gvRebuffer.OptionsView.ColumnAutoWidth = false;
            this.gvRebuffer.OptionsView.ShowGroupPanel = false;
            this.gvRebuffer.DoubleClick += new System.EventHandler(this.gv_DoubleClick);
            // 
            // gridColumnId
            // 
            this.gridColumnId.Caption = "序号";
            this.gridColumnId.FieldName = "SN";
            this.gridColumnId.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumnId.Name = "gridColumnId";
            this.gridColumnId.Visible = true;
            this.gridColumnId.VisibleIndex = 0;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 1;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "卡顿时长(s)";
            this.gridColumn17.FieldName = "InterTime";
            this.gridColumn17.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 2;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "URL";
            this.gridColumn1.FieldName = "URL";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 3;
            this.gridColumn1.Width = 100;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "流媒体卡顿开始时间";
            this.gridColumn27.FieldName = "BeginTimeStr";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 4;
            this.gridColumn27.Width = 125;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "卡顿事件开始经度";
            this.gridColumn2.FieldName = "BeginLongitude";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 5;
            this.gridColumn2.Width = 125;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "卡顿事件开始纬度";
            this.gridColumn3.FieldName = "BeginLatitude";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 6;
            this.gridColumn3.Width = 125;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "流媒体卡顿结束时间";
            this.gridColumn15.FieldName = "EndTimeStr";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 7;
            this.gridColumn15.Width = 125;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "卡顿事件结束经度";
            this.gridColumn4.FieldName = "EndLongitude";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 8;
            this.gridColumn4.Width = 125;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "卡顿事件结束纬度";
            this.gridColumn16.FieldName = "EndLatitude";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 9;
            this.gridColumn16.Width = 125;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "采样点个数";
            this.gridColumn5.FieldName = "TestPointsCount";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 10;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "RSRP最大值";
            this.gridColumn6.FieldName = "RsrpMax";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 11;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "RSRP最小值";
            this.gridColumn7.FieldName = "RsrpMin";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 12;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "RSRP平均值";
            this.gridColumn8.FieldName = "RsrpAvg";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 13;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "最强邻区最大RSRP";
            this.gridColumn12.FieldName = "MaxNbRsrpMax";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 14;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "最强邻区最小RSRP";
            this.gridColumn13.FieldName = "MaxNbRsrpMin";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 15;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "最强邻区平均RSRP";
            this.gridColumn14.FieldName = "MaxNbRsrpAvg";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 16;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "SINR最大值";
            this.gridColumn9.FieldName = "SinrMax";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 17;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "关联小区名";
            this.gridColumn19.FieldName = "CellNameStr";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 22;
            this.gridColumn19.Width = 163;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "关联TACCI";
            this.gridColumn18.FieldName = "TacCIStr";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 21;
            this.gridColumn18.Width = 153;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "SINR最小值";
            this.gridColumn10.FieldName = "SinrMin";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 18;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "SINR平均值";
            this.gridColumn11.FieldName = "SinrAvg";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 19;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "平均下载速率(Mbps)";
            this.gridColumn26.FieldName = "AppSpeedMbAvg";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 20;
            // 
            // LteVideoPlayAnaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1062, 598);
            this.Controls.Add(this.tabVideoPlay);
            this.Name = "LteVideoPlayAnaForm";
            this.Text = "流媒体卡顿缓冲分析";
            this.contextMenuStrip.ResumeLayout(false);
            this.tabVideoPlay.ResumeLayout(false);
            this.pageLoad.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcLoad)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvLoad)).EndInit();
            this.pageRebuffer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcRebuffer)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRebuffer)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private System.Windows.Forms.TabControl tabVideoPlay;
        private System.Windows.Forms.TabPage pageRebuffer;
        private System.Windows.Forms.TabPage pageLoad;
        private DevExpress.XtraGrid.GridControl gcRebuffer;
        private DevExpress.XtraGrid.Views.Grid.GridView gvRebuffer;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnId;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.GridControl gcLoad;
        private DevExpress.XtraGrid.Views.Grid.GridView gvLoad;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private System.Windows.Forms.ToolStripMenuItem miExportTp2Csv;
        private System.Windows.Forms.ToolStripMenuItem miExportOtherTp2Csv;
    }
}