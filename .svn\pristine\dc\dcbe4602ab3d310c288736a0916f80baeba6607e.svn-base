﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.NOP
{
    public partial class UnDoneGroupStatForm : MinCloseForm
    {
        public UnDoneGroupStatForm()
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            DisposeWhenClose = true;
            //init();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (resultFiles == null && results == null)
            {
                return;
            }
           


                #region
                List<NPOIRow> summBroTables = new List<NPOIRow>();
                NPOIRow summBroTitleRow = new NPOIRow();
                summBroTitleRow.AddCellValue("工单名称");
                summBroTitleRow.AddCellValue("地市");
                summBroTitleRow.AddCellValue("T1");
                summBroTitleRow.AddCellValue("T21");
                summBroTitleRow.AddCellValue("T22");
                summBroTitleRow.AddCellValue("T23");
                summBroTitleRow.AddCellValue("T24");
                summBroTitleRow.AddCellValue("T25");
                summBroTitleRow.AddCellValue("T3");
                summBroTitleRow.AddCellValue("T0");
                summBroTitleRow.AddCellValue("申诉审核");
                summBroTitleRow.AddCellValue("挂起处理");
                summBroTitleRow.AddCellValue("归档工单");
                summBroTables.Add(summBroTitleRow);
                if (resultFiles != null)
                {
                foreach (TaskStatResult res in resultFiles)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(res.TaskName);
                    row.AddCellValue(res.District);
                    row.AddCellValue(res.T1);
                    row.AddCellValue(res.T21);
                    row.AddCellValue(res.T22);
                    row.AddCellValue(res.T23);
                    row.AddCellValue(res.T24);
                    row.AddCellValue(res.T25);
                    row.AddCellValue(res.T3);
                    row.AddCellValue(res.T0);
                    row.AddCellValue(res.T4);
                    row.AddCellValue(res.T5);
                    row.AddCellValue(res.T6);
                    summBroTables.Add(row);
                }
            }
            

                List<NPOIRow> broTables = new List<NPOIRow>();
                NPOIRow broTitleRow = new NPOIRow();
                broTitleRow.AddCellValue("分类");
                broTitleRow.AddCellValue("鄂州");
                broTitleRow.AddCellValue("恩施");
                broTitleRow.AddCellValue("黄石");
                broTitleRow.AddCellValue("黄冈");
                broTitleRow.AddCellValue("荆门");
                broTitleRow.AddCellValue("荆州");
                broTitleRow.AddCellValue("江汉");
                broTitleRow.AddCellValue("天门");
                broTitleRow.AddCellValue("潜江");
                broTitleRow.AddCellValue("十堰");
                broTitleRow.AddCellValue("随州");
                broTitleRow.AddCellValue("武汉");
                broTitleRow.AddCellValue("襄阳");
                broTitleRow.AddCellValue("咸宁");
                broTitleRow.AddCellValue("孝感");
                broTitleRow.AddCellValue("宜昌");

                broTables.Add(broTitleRow);
                if (results != null)
                {
                foreach (GroupStatResult dtlModel in results)
                {
                    NPOIRow fileValues = new NPOIRow();
                    fileValues.AddCellValue(dtlModel.GroupName);
                    fileValues.AddCellValue(dtlModel.EZhou);
                    fileValues.AddCellValue(dtlModel.EnShi);
                    fileValues.AddCellValue(dtlModel.HuangShi);
                    fileValues.AddCellValue(dtlModel.HuangGang);
                    fileValues.AddCellValue(dtlModel.JingMen);
                    fileValues.AddCellValue(dtlModel.JingZhou);
                    fileValues.AddCellValue(dtlModel.JiangHan);
                    fileValues.AddCellValue(dtlModel.TianMen);
                    fileValues.AddCellValue(dtlModel.QianJiang);
                    fileValues.AddCellValue(dtlModel.ShiYan);
                    fileValues.AddCellValue(dtlModel.SuiZhou);
                    fileValues.AddCellValue(dtlModel.WuHan);
                    fileValues.AddCellValue(dtlModel.XiangYang);
                    fileValues.AddCellValue(dtlModel.XianNing);
                    fileValues.AddCellValue(dtlModel.XiaoGan);
                    fileValues.AddCellValue(dtlModel.YiChang);

                    broTables.Add(fileValues);


                }
            }


                #endregion


                ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() { broTables, summBroTables },
                new List<string>() { "类别-地市", "工单-类别" });
        }

        
        List<TaskStatResult> resultFiles = null;
        List<GroupStatResult> results = null;
        public void FillData(object result, object taskResult)
        {
            resultFiles = taskResult as List<TaskStatResult>;
            results = result as List<GroupStatResult>;
            gcGroup.DataSource = results;
            gcGroup.RefreshDataSource();
            gcTask.DataSource = resultFiles;
            gcTask.RefreshDataSource();
            

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }
    }
}
