﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Net
{
    public class DIYQueryBlackBlock_Point : QueryBase
    {
        public DIYQueryBlackBlock_Point(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        public override string Name
        {
            get { return "重复问题点"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18002, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        BlackBlock_Point_QueryCond curCond = null;
        BlackBlock_PointDlg conditiondlg = null;
        protected override void query()
        {
            if (conditiondlg == null)
            {
                conditiondlg = new BlackBlock_PointDlg();
            }
            if (conditiondlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                curCond = conditiondlg.GetCondition();
            }
            else
            {
                return;
            }
            WaitBox.Show("开始查询重复问题点...", queryInThread);
            MainModel.MainForm.FireBlackBlock_PointForm();
        }

        private void queryInThread()
        {
            try
            {
                MainModel.BlackBlockPointsDic.Clear();
                string sqlBlock = "";
                string sqlEvent = "";
                string sqlDate = "";
                string sqlEventValidate = "";
                if (curCond.blockType == 0)
                {
                    sqlBlock = "exec mc_sp_kh_blackblock_info_get " + curCond.EndTime;
                    sqlEvent = "exec mc_sp_kh_blackblock_event_get " + curCond.EndTime;
                    sqlDate = "exec mc_sp_kh_blackblock_date_get";
                    sqlEventValidate = "exec mc_sp_kh_blackblock_event_validate_get " + curCond.EndTime;
                }
                else if (curCond.blockType == 1)
                {
                    sqlBlock = "exec mc_sp_kh_blackblock_td_info_get " + curCond.EndTime;
                    sqlEvent = "exec mc_sp_kh_blackblock_td_event_get " + curCond.EndTime;
                    sqlDate = "exec mc_sp_kh_blackblock_td_date_get";
                    sqlEventValidate = "exec mc_sp_kh_blackblock_td_event_validate_get " + curCond.EndTime;
                }
                WaitBox.Text = "开始获取重复问题点...";
                DIYSQLBlackBlock_Point blockQuery = new DIYSQLBlackBlock_Point(MainModel, sqlBlock);
                blockQuery.Query();
                Dictionary<int, BlackBlock_Point> blackBlockPointsDic = blockQuery.BlackBlockPointsDic;
                foreach (int blockID in blackBlockPointsDic.Keys)
                {
                    BlackBlock_Point bb = blackBlockPointsDic[blockID];
                    if (filterCondition(bb))
                    {
                        MainModel.BlackBlockPointsDic[blockID] = bb;
                    }
                }
                WaitBox.Text = "开始获取重复问题点事件...";
                DIYSQLBlackBlock_Point_Event eventQuery = new DIYSQLBlackBlock_Point_Event(MainModel, sqlEvent);
                eventQuery.Query();
                WaitBox.Text = "开始获取重复问题点验证情况...";
                DIYSQLBlackBlock_Point_Date dateQuery = new DIYSQLBlackBlock_Point_Date(MainModel, sqlDate);
                dateQuery.Query();
                DIYSQLBlackBlock_Point_Event_Validate eventValidateQuery = new DIYSQLBlackBlock_Point_Event_Validate(MainModel, sqlEventValidate);
                eventValidateQuery.Query();
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private bool filterCondition(BlackBlock_Point bb)
        {
            if (curCond.blockID != -1 && bb.ID != curCond.blockID)
            {
                return false;
            }
            if (!curCond.blockStatusList.Contains(bb.Status))
            {
                return false;
            }
            return true;
        }

        private class DIYSQLBlackBlock_Point : DIYSQLBase
        {
            public Dictionary<int, BlackBlock_Point> BlackBlockPointsDic = new Dictionary<int, BlackBlock_Point>();
            readonly string sql;
            public DIYSQLBlackBlock_Point(MainModel mainModel, string sql)
                : base(mainModel)
            {
                this.sql = sql;
            }
            protected override string getSqlTextString()
            {
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[23];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Int;
                rType[6] = E_VType.E_Int;
                rType[7] = E_VType.E_Int;
                rType[8] = E_VType.E_Int;
                rType[9] = E_VType.E_Int;
                rType[10] = E_VType.E_Int;
                rType[11] = E_VType.E_Int;
                rType[12] = E_VType.E_Int;
                rType[13] = E_VType.E_Int;
                rType[14] = E_VType.E_String;
                rType[15] = E_VType.E_String;
                rType[16] = E_VType.E_String;
                rType[17] = E_VType.E_String;
                rType[18] = E_VType.E_String;
                rType[19] = E_VType.E_String;
                rType[20] = E_VType.E_String;
                rType[21] = E_VType.E_String;
                rType[22] = E_VType.E_String;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        BlackBlock_Point block = new BlackBlock_Point();
                        block.Fill(package.Content);
                        BlackBlockPointsDic[block.ID] = block;
                        //do your code here
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    setProgress(ref index, ref progress);
                }
            }

            private void setProgress(ref int index, ref int progress)
            {
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }

            public override string Name
            {
                get { return "DIYSQLBlackBlock_Point"; }
            }
        }

        private class DIYSQLBlackBlock_Point_Event : DIYSQLBase
        {
            protected string sql;
            public DIYSQLBlackBlock_Point_Event(MainModel mainModel, string sql)
                : base(mainModel)
            {
                this.sql = sql;
            }

            protected override string getSqlTextString()
            {
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[15];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Int;
                rType[6] = E_VType.E_Int;
                rType[7] = E_VType.E_Int;
                rType[8] = E_VType.E_Int;
                rType[9] = E_VType.E_Int;
                rType[10] = E_VType.E_Int;
                rType[11] = E_VType.E_Int;
                rType[12] = E_VType.E_String;
                rType[13] = E_VType.E_Int;
                rType[14] = E_VType.E_String;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        fillData(clientProxy, package);
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    setProgress(ref index, ref progress);
                }
            }

            private void setProgress(ref int index, ref int progress)
            {
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }

            private void fillData(ClientProxy clientProxy, Package package)
            {
                BlackBlock_Point_Event eventItem = new BlackBlock_Point_Event();
                eventItem.Fill(package.Content);
                eventItem.DistrictID = clientProxy.DbID;
                BlackBlock_Point bb;
                if (MainModel.BlackBlockPointsDic.TryGetValue(eventItem.BlockID, out bb))
                {
                    bb.AddEvent(eventItem);
                }
                //do your code here
            }

            public override string Name
            {
                get { return "DIYSQLBlackBlock_Point_Event"; }
            }
        }

        private class DIYSQLBlackBlock_Point_Date : DIYSQLBase
        {
            readonly string sql;
            public DIYSQLBlackBlock_Point_Date(MainModel mainModel, string sql)
                : base(mainModel)
            {
                this.sql = sql;
            }
            protected override string getSqlTextString()
            {
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[4];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Int;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        fillData(package);
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            private void fillData(Package package)
            {
                BlackBlock_Point_Date validateDate = new BlackBlock_Point_Date();
                validateDate.Fill(package.Content);
                BlackBlock_Point bb;
                if (MainModel.BlackBlockPointsDic.TryGetValue(validateDate.BlockID, out bb))
                {
                    bb.AddValidateDate(validateDate);
                }
            }

            public override string Name
            {
                get { return "DIYSQLBlackBlock_Point_Date"; }
            }
        }

        private class DIYSQLBlackBlock_Point_Event_Validate : DIYSQLBlackBlock_Point_Event
        {
            public DIYSQLBlackBlock_Point_Event_Validate(MainModel mainModel, string sql)
                : base(mainModel, sql)
            {
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        fillData(clientProxy, package);
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    setProgress(ref index, ref progress);
                }
            }

            private void setProgress(ref int index, ref int progress)
            {
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }

            private void fillData(ClientProxy clientProxy, Package package)
            {
                BlackBlock_Point_Event eventItem = new BlackBlock_Point_Event();
                eventItem.Fill(package.Content);
                eventItem.DistrictID = clientProxy.DbID;
                BlackBlock_Point bb;
                if (MainModel.BlackBlockPointsDic.TryGetValue(eventItem.BlockID, out bb))
                {
                    bb.AddEventValidate(eventItem);
                }
            }

            public override string Name
            {
                get { return "DIYSQLBlackBlock_Point_Event_Validate"; }
            }
        }
    }
}
