﻿namespace MasterCom.RAMS.Func
{
    partial class FindContainCellsFrm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FindContainCellsFrm));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCloseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExport2XlsWithNoMerge = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.rb5G = new System.Windows.Forms.RadioButton();
            this.rb4G = new System.Windows.Forms.RadioButton();
            this.rb3G = new System.Windows.Forms.RadioButton();
            this.rb2G = new System.Windows.Forms.RadioButton();
            this.rbAll = new System.Windows.Forms.RadioButton();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.pagCell = new System.Windows.Forms.TabPage();
            this.tlvCellsInfo = new BrightIdeasSoftware.TreeListView();
            this.olvColumnRegion = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnArea = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNetType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCode = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBCCH = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMSC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTCH = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDoor = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAngle_Dir = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAngle_Ob = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAltitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSComment = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.pagBTS = new System.Windows.Forms.TabPage();
            this.lvBTS = new BrightIdeasSoftware.TreeListView();
            this.olvColumnRegionBTS = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colArea = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colBTSNetType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSBSC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSMSC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSDoor = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBandType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSAltitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellBtsName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCGI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.pagCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tlvCellsInfo)).BeginInit();
            this.pagBTS.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvBTS)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCloseAll,
            this.toolStripMenuItem1,
            this.miExport2Xls,
            this.miExport2XlsWithNoMerge});
            this.contextMenuStrip.Name = "contextMenuStrip1";
            this.contextMenuStrip.Size = new System.Drawing.Size(174, 98);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(173, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCloseAll
            // 
            this.miCloseAll.Name = "miCloseAll";
            this.miCloseAll.Size = new System.Drawing.Size(173, 22);
            this.miCloseAll.Text = "全部合并";
            this.miCloseAll.Click += new System.EventHandler(this.miCloseAll_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(170, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(173, 22);
            this.miExport2Xls.Text = "导出Excel";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // miExport2XlsWithNoMerge
            // 
            this.miExport2XlsWithNoMerge.Name = "miExport2XlsWithNoMerge";
            this.miExport2XlsWithNoMerge.Size = new System.Drawing.Size(173, 22);
            this.miExport2XlsWithNoMerge.Text = "导出Excel(不合并)";
            this.miExport2XlsWithNoMerge.Click += new System.EventHandler(this.miExport2XlsWithNoMerge_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rb5G);
            this.groupBox1.Controls.Add(this.rb4G);
            this.groupBox1.Controls.Add(this.rb3G);
            this.groupBox1.Controls.Add(this.rb2G);
            this.groupBox1.Controls.Add(this.rbAll);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(898, 41);
            this.groupBox1.TabIndex = 14;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "查看方式";
            // 
            // rb5G
            // 
            this.rb5G.AutoSize = true;
            this.rb5G.Location = new System.Drawing.Point(308, 19);
            this.rb5G.Name = "rb5G";
            this.rb5G.Size = new System.Drawing.Size(59, 16);
            this.rb5G.TabIndex = 1;
            this.rb5G.Text = "5G小区";
            this.rb5G.UseVisualStyleBackColor = true;
            // 
            // rb4G
            // 
            this.rb4G.AutoSize = true;
            this.rb4G.Location = new System.Drawing.Point(243, 19);
            this.rb4G.Name = "rb4G";
            this.rb4G.Size = new System.Drawing.Size(59, 16);
            this.rb4G.TabIndex = 0;
            this.rb4G.Text = "4G小区";
            this.rb4G.UseVisualStyleBackColor = true;
            this.rb4G.CheckedChanged += new System.EventHandler(this.rb_CheckedChanged);
            // 
            // rb3G
            // 
            this.rb3G.AutoSize = true;
            this.rb3G.Location = new System.Drawing.Point(178, 19);
            this.rb3G.Name = "rb3G";
            this.rb3G.Size = new System.Drawing.Size(59, 16);
            this.rb3G.TabIndex = 0;
            this.rb3G.Text = "3G小区";
            this.rb3G.UseVisualStyleBackColor = true;
            this.rb3G.CheckedChanged += new System.EventHandler(this.rb_CheckedChanged);
            // 
            // rb2G
            // 
            this.rb2G.AutoSize = true;
            this.rb2G.Location = new System.Drawing.Point(113, 20);
            this.rb2G.Name = "rb2G";
            this.rb2G.Size = new System.Drawing.Size(59, 16);
            this.rb2G.TabIndex = 0;
            this.rb2G.Text = "2G小区";
            this.rb2G.UseVisualStyleBackColor = true;
            this.rb2G.CheckedChanged += new System.EventHandler(this.rb_CheckedChanged);
            // 
            // rbAll
            // 
            this.rbAll.AutoSize = true;
            this.rbAll.Checked = true;
            this.rbAll.Location = new System.Drawing.Point(60, 19);
            this.rbAll.Name = "rbAll";
            this.rbAll.Size = new System.Drawing.Size(47, 16);
            this.rbAll.TabIndex = 0;
            this.rbAll.TabStop = true;
            this.rbAll.Text = "全部";
            this.rbAll.UseVisualStyleBackColor = true;
            this.rbAll.CheckedChanged += new System.EventHandler(this.rb_CheckedChanged);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.pagCell);
            this.tabControl1.Controls.Add(this.pagBTS);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 41);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(898, 425);
            this.tabControl1.TabIndex = 15;
            // 
            // pagCell
            // 
            this.pagCell.Controls.Add(this.tlvCellsInfo);
            this.pagCell.Location = new System.Drawing.Point(4, 22);
            this.pagCell.Name = "pagCell";
            this.pagCell.Padding = new System.Windows.Forms.Padding(3);
            this.pagCell.Size = new System.Drawing.Size(890, 399);
            this.pagCell.TabIndex = 0;
            this.pagCell.Text = "小区";
            this.pagCell.UseVisualStyleBackColor = true;
            // 
            // tlvCellsInfo
            // 
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnRegion);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnArea);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCellCount);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCellName);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCellBtsName);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnNetType);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCode);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnLAC);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCI);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnBCCH);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnBSIC);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnBSC);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnMSC);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnTCH);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnLongitude);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnLatitude);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnDoor);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnType);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnAngle_Dir);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnAngle_Ob);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnAltitude);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnBTSComment);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCGI);
            this.tlvCellsInfo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnRegion,
            this.olvColumnArea,
            this.olvColumnCellCount,
            this.olvColumnCellName,
            this.olvColumnCellBtsName,
            this.olvColumnNetType,
            this.olvColumnCode,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnBCCH,
            this.olvColumnBSIC,
            this.olvColumnBSC,
            this.olvColumnMSC,
            this.olvColumnTCH,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnDoor,
            this.olvColumnType,
            this.olvColumnAngle_Dir,
            this.olvColumnAngle_Ob,
            this.olvColumnAltitude,
            this.olvColumnBTSComment,
            this.olvColumnCGI});
            this.tlvCellsInfo.ContextMenuStrip = this.contextMenuStrip;
            this.tlvCellsInfo.Cursor = System.Windows.Forms.Cursors.Default;
            this.tlvCellsInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tlvCellsInfo.FullRowSelect = true;
            this.tlvCellsInfo.GridLines = true;
            this.tlvCellsInfo.HeaderWordWrap = true;
            this.tlvCellsInfo.HideSelection = false;
            this.tlvCellsInfo.IsNeedShowOverlay = false;
            this.tlvCellsInfo.Location = new System.Drawing.Point(3, 3);
            this.tlvCellsInfo.Name = "tlvCellsInfo";
            this.tlvCellsInfo.OwnerDraw = true;
            this.tlvCellsInfo.ShowGroups = false;
            this.tlvCellsInfo.Size = new System.Drawing.Size(884, 393);
            this.tlvCellsInfo.TabIndex = 12;
            this.tlvCellsInfo.UseCompatibleStateImageBehavior = false;
            this.tlvCellsInfo.View = System.Windows.Forms.View.Details;
            this.tlvCellsInfo.VirtualMode = true;
            this.tlvCellsInfo.DoubleClick += new System.EventHandler(this.tlvCellsInfo_DoubleClick);
            // 
            // olvColumnRegion
            // 
            this.olvColumnRegion.HeaderFont = null;
            this.olvColumnRegion.Text = "区域";
            this.olvColumnRegion.Width = 100;
            // 
            // olvColumnArea
            // 
            this.olvColumnArea.AspectName = "";
            this.olvColumnArea.HeaderFont = null;
            this.olvColumnArea.Text = "面积(km²)";
            // 
            // olvColumnCellCount
            // 
            this.olvColumnCellCount.HeaderFont = null;
            this.olvColumnCellCount.Text = "小区个数";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.AspectName = "";
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 100;
            // 
            // olvColumnNetType
            // 
            this.olvColumnNetType.HeaderFont = null;
            this.olvColumnNetType.Text = "网络类型";
            // 
            // olvColumnCode
            // 
            this.olvColumnCode.HeaderFont = null;
            this.olvColumnCode.Text = "Code";
            this.olvColumnCode.Width = 80;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC/TAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI/CellID";
            // 
            // olvColumnBCCH
            // 
            this.olvColumnBCCH.HeaderFont = null;
            this.olvColumnBCCH.Text = "频点";
            // 
            // olvColumnBSIC
            // 
            this.olvColumnBSIC.HeaderFont = null;
            this.olvColumnBSIC.Text = "BSIC/CPI/PCI";
            this.olvColumnBSIC.Width = 86;
            // 
            // olvColumnBSC
            // 
            this.olvColumnBSC.HeaderFont = null;
            this.olvColumnBSC.Text = "BSC";
            this.olvColumnBSC.Width = 67;
            // 
            // olvColumnMSC
            // 
            this.olvColumnMSC.HeaderFont = null;
            this.olvColumnMSC.Text = "MSC";
            this.olvColumnMSC.Width = 66;
            // 
            // olvColumnTCH
            // 
            this.olvColumnTCH.HeaderFont = null;
            this.olvColumnTCH.Text = "TCH";
            this.olvColumnTCH.Width = 140;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 80;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 80;
            // 
            // olvColumnDoor
            // 
            this.olvColumnDoor.HeaderFont = null;
            this.olvColumnDoor.Text = "室内/室外";
            // 
            // olvColumnType
            // 
            this.olvColumnType.HeaderFont = null;
            this.olvColumnType.Text = "频带类型";
            // 
            // olvColumnAngle_Dir
            // 
            this.olvColumnAngle_Dir.HeaderFont = null;
            this.olvColumnAngle_Dir.Text = "方向角";
            // 
            // olvColumnAngle_Ob
            // 
            this.olvColumnAngle_Ob.HeaderFont = null;
            this.olvColumnAngle_Ob.Text = "下倾角";
            // 
            // olvColumnAltitude
            // 
            this.olvColumnAltitude.HeaderFont = null;
            this.olvColumnAltitude.Text = "挂高";
            // 
            // olvColumnBTSComment
            // 
            this.olvColumnBTSComment.HeaderFont = null;
            this.olvColumnBTSComment.Text = "基站描述";
            // 
            // pagBTS
            // 
            this.pagBTS.Controls.Add(this.lvBTS);
            this.pagBTS.Location = new System.Drawing.Point(4, 22);
            this.pagBTS.Name = "pagBTS";
            this.pagBTS.Padding = new System.Windows.Forms.Padding(3);
            this.pagBTS.Size = new System.Drawing.Size(890, 399);
            this.pagBTS.TabIndex = 1;
            this.pagBTS.Text = "基站";
            this.pagBTS.UseVisualStyleBackColor = true;
            // 
            // lvBTS
            // 
            this.lvBTS.AllColumns.Add(this.olvColumnRegionBTS);
            this.lvBTS.AllColumns.Add(this.colArea);
            this.lvBTS.AllColumns.Add(this.olvColumnBTSCount);
            this.lvBTS.AllColumns.Add(this.olvColumnBTSName);
            this.lvBTS.AllColumns.Add(this.colBTSNetType);
            this.lvBTS.AllColumns.Add(this.olvColumnBTSBSC);
            this.lvBTS.AllColumns.Add(this.olvColumnBTSMSC);
            this.lvBTS.AllColumns.Add(this.olvColumnBTSLongitude);
            this.lvBTS.AllColumns.Add(this.olvColumnBTSLatitude);
            this.lvBTS.AllColumns.Add(this.olvColumnBTSDoor);
            this.lvBTS.AllColumns.Add(this.olvColumnBandType);
            this.lvBTS.AllColumns.Add(this.olvColumnBTSAltitude);
            this.lvBTS.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnRegionBTS,
            this.colArea,
            this.olvColumnBTSCount,
            this.olvColumnBTSName,
            this.colBTSNetType,
            this.olvColumnBTSBSC,
            this.olvColumnBTSMSC,
            this.olvColumnBTSLongitude,
            this.olvColumnBTSLatitude,
            this.olvColumnBTSDoor,
            this.olvColumnBandType,
            this.olvColumnBTSAltitude});
            this.lvBTS.ContextMenuStrip = this.contextMenuStrip;
            this.lvBTS.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvBTS.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvBTS.FullRowSelect = true;
            this.lvBTS.GridLines = true;
            this.lvBTS.HeaderWordWrap = true;
            this.lvBTS.HideSelection = false;
            this.lvBTS.IsNeedShowOverlay = false;
            this.lvBTS.Location = new System.Drawing.Point(3, 3);
            this.lvBTS.Name = "lvBTS";
            this.lvBTS.OwnerDraw = true;
            this.lvBTS.ShowGroups = false;
            this.lvBTS.Size = new System.Drawing.Size(884, 393);
            this.lvBTS.TabIndex = 16;
            this.lvBTS.UseCompatibleStateImageBehavior = false;
            this.lvBTS.View = System.Windows.Forms.View.Details;
            this.lvBTS.VirtualMode = true;
            // 
            // olvColumnRegionBTS
            // 
            this.olvColumnRegionBTS.HeaderFont = null;
            this.olvColumnRegionBTS.Text = "区域";
            this.olvColumnRegionBTS.Width = 100;
            // 
            // colArea
            // 
            this.colArea.AspectName = "";
            this.colArea.HeaderFont = null;
            this.colArea.Text = "面积(km²)";
            // 
            // olvColumnBTSCount
            // 
            this.olvColumnBTSCount.HeaderFont = null;
            this.olvColumnBTSCount.Text = "基站个数";
            // 
            // olvColumnBTSName
            // 
            this.olvColumnBTSName.AspectName = "";
            this.olvColumnBTSName.HeaderFont = null;
            this.olvColumnBTSName.Text = "基站名称";
            this.olvColumnBTSName.Width = 100;
            // 
            // colBTSNetType
            // 
            this.colBTSNetType.HeaderFont = null;
            this.colBTSNetType.Text = "网络类型";
            // 
            // olvColumnBTSBSC
            // 
            this.olvColumnBTSBSC.HeaderFont = null;
            this.olvColumnBTSBSC.Text = "BSC";
            // 
            // olvColumnBTSMSC
            // 
            this.olvColumnBTSMSC.HeaderFont = null;
            this.olvColumnBTSMSC.Text = "MSC";
            // 
            // olvColumnBTSLongitude
            // 
            this.olvColumnBTSLongitude.HeaderFont = null;
            this.olvColumnBTSLongitude.Text = "经度";
            this.olvColumnBTSLongitude.Width = 100;
            // 
            // olvColumnBTSLatitude
            // 
            this.olvColumnBTSLatitude.HeaderFont = null;
            this.olvColumnBTSLatitude.Text = "纬度";
            this.olvColumnBTSLatitude.Width = 100;
            // 
            // olvColumnBTSDoor
            // 
            this.olvColumnBTSDoor.HeaderFont = null;
            this.olvColumnBTSDoor.Text = "室内/室外";
            // 
            // olvColumnBandType
            // 
            this.olvColumnBandType.HeaderFont = null;
            this.olvColumnBandType.Text = "频段";
            // 
            // olvColumnBTSAltitude
            // 
            this.olvColumnBTSAltitude.HeaderFont = null;
            this.olvColumnBTSAltitude.Text = "基站高度";
            // 
            // olvColumnCellBtsName
            // 
            this.olvColumnCellBtsName.HeaderFont = null;
            this.olvColumnCellBtsName.Text = "基站名称";
            // 
            // olvColumnCGI
            // 
            this.olvColumnCGI.HeaderFont = null;
            this.olvColumnCGI.Text = "CGI";
            // 
            // FindContainCellsFrm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("FindContainCellsFrm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.ClientSize = new System.Drawing.Size(898, 466);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.groupBox1);
            this.Name = "FindContainCellsFrm";
            this.Text = "小区信息";
            this.contextMenuStrip.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabControl1.ResumeLayout(false);
            this.pagCell.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tlvCellsInfo)).EndInit();
            this.pagBTS.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvBTS)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCloseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton rb3G;
        private System.Windows.Forms.RadioButton rb2G;
        private System.Windows.Forms.RadioButton rbAll;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage pagCell;
        private BrightIdeasSoftware.TreeListView tlvCellsInfo;
        private BrightIdeasSoftware.OLVColumn olvColumnRegion;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCount;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnBSC;
        private BrightIdeasSoftware.OLVColumn olvColumnMSC;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private System.Windows.Forms.TabPage pagBTS;
        private BrightIdeasSoftware.TreeListView lvBTS;
        private BrightIdeasSoftware.OLVColumn olvColumnRegionBTS;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSCount;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSName;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSBSC;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSMSC;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnDoor;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSDoor;
        private BrightIdeasSoftware.OLVColumn olvColumnCode;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC;
        private BrightIdeasSoftware.OLVColumn olvColumnTCH;
        private BrightIdeasSoftware.OLVColumn olvColumnBandType;
        private BrightIdeasSoftware.OLVColumn olvColumnType;
        private BrightIdeasSoftware.OLVColumn olvColumnAngle_Dir;
        private BrightIdeasSoftware.OLVColumn olvColumnAngle_Ob;
        private BrightIdeasSoftware.OLVColumn olvColumnAltitude;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSComment;
        private System.Windows.Forms.ToolStripMenuItem miExport2XlsWithNoMerge;
        private System.Windows.Forms.RadioButton rb4G;
        private BrightIdeasSoftware.OLVColumn olvColumnArea;
        private BrightIdeasSoftware.OLVColumn colArea;
        private BrightIdeasSoftware.OLVColumn olvColumnNetType;
        private BrightIdeasSoftware.OLVColumn colBTSNetType;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSAltitude;
        private System.Windows.Forms.RadioButton rb5G;
        private BrightIdeasSoftware.OLVColumn olvColumnCellBtsName;
        private BrightIdeasSoftware.OLVColumn olvColumnCGI;
    }
}
