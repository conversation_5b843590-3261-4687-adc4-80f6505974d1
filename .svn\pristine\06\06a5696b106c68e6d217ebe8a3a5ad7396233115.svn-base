﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class IndoorLeakCoverPnl : UserControl
    {
        public IndoorLeakCoverPnl()
        {
            InitializeComponent();
        }

        IndoorLeakCoverCause mainReason = null;
        LeakCoverOverCover overCover = null;
        LeakCoverHandoverParam hoParam = null;
        LeakCoverWeakOutdoor outdoorWC = null;
        public void LinkCondition(IndoorLeakCoverCause reason)
        {
            this.mainReason = reason;
            foreach (CauseBase item in reason.SubCauses)
            {
                if (item is LeakCoverOverCover)
                {
                    overCover = item as LeakCoverOverCover;
                }
                else if (item is LeakCoverHandoverParam)
                {
                    hoParam = item as LeakCoverHandoverParam;
                }
                else if (item is LeakCoverWeakOutdoor)
                {
                    outdoorWC = item as LeakCoverWeakOutdoor;
                }
            }

            if (overCover != null)
            {
                numDisMin.Value = (decimal)overCover.Distance;
                numRSRPMin.Value = (decimal)overCover.RSRPMin;
                numDisMin.ValueChanged += numDisMin_ValueChanged;
                numRSRPMin.ValueChanged += numRSRPMin_ValueChanged;
            }

            if (hoParam != null)
            {
                numHORSRPDiff.Value = (decimal)hoParam.RSRPMin;
                numHOSecond.Value = (decimal)hoParam.SecondMin;
                numHORSRPDiff.ValueChanged += numHORSRPDiff_ValueChanged;
                numHOSecond.ValueChanged += numHOSecond_ValueChanged;
            }

            if (outdoorWC != null)
            {
                numWCDis.Value = (decimal)outdoorWC.DistanceMin;
                numWCRSRPDiff.Value = (decimal)outdoorWC.RSRPDiffMax;
                numWCDis.ValueChanged += numWCDis_ValueChanged;
                numWCRSRPDiff.ValueChanged += numWCRSRPDiff_ValueChanged;
            }
        }

        void numWCRSRPDiff_ValueChanged(object sender, EventArgs e)
        {
            outdoorWC.RSRPDiffMax = (float)numWCRSRPDiff.Value;
        }

        void numWCDis_ValueChanged(object sender, EventArgs e)
        {
            outdoorWC.DistanceMin = (double)numWCDis.Value;
        }

        void numHOSecond_ValueChanged(object sender, EventArgs e)
        {
            hoParam.SecondMin = (int)numHOSecond.Value;
        }

        void numHORSRPDiff_ValueChanged(object sender, EventArgs e)
        {
            hoParam.RSRPMin = (float)numHORSRPDiff.Value;
        }

        void numRSRPMin_ValueChanged(object sender, EventArgs e)
        {
            overCover.RSRPMin = (float)numRSRPMin.Value;
        }

        void numDisMin_ValueChanged(object sender, EventArgs e)
        {
            overCover.Distance = (double)numDisMin.Value;
        }

    }
}
