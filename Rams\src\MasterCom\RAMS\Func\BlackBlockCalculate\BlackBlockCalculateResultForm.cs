﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraTab;
using System.Reflection;

namespace MasterCom.RAMS.Func
{
    public partial class BlackBlockCalculateResultForm : MinCloseForm
    {
        List<string> sheetName = new List<string>();
        List<GridView> eList = new List<GridView>();
        List<GridView> listView = new List<GridView>();
        public BlackBlockCalculateResultForm()
            : base()
        {
            InitializeComponent();
        }

        public void FillData(List<List<BBDetailItem>> resultList, DataTable dt)
        {
            sheetName = new List<string>();
            eList = new List<GridView>();
            listView = new List<GridView>();
            if (resultList.Count > 0)
            {
                for (int j = 0; j < resultList.Count; j++)
                {
                    addTabData(resultList, j);
                }

                addBand();
                xtraTabCtl.SelectedTabPageIndex = 0;
                bandGC.DataSource = dt;
                eList.Add(bandGV);

                listView.Add(bandGV);
                foreach (GridView gv in eList)
                {
                    if (gv != bandGV)
                    {
                        SetCaption(gv);
                        listView.Add(gv);
                    }
                }
            }
        }

        private void addTabData(List<List<BBDetailItem>> resultList, int j)
        {
            List<BBDetailItem> detailList = new List<BBDetailItem>();
            for (int k = 0; k < resultList[j].Count; k++)
            {
                detailList.Add(resultList[j][k]);
            }
            if (resultList[j].Count > 0)
            {
                if (!sheetName.Contains("汇总"))
                {
                    sheetName.Add("汇总");
                }

                DevExpress.XtraGrid.GridControl gc = new DevExpress.XtraGrid.GridControl();
                GridView gv = new GridView();

                gc.ForceInitialize();
                gc.ViewCollection.Add(gv);
                gc.MainView = gv;
                gc.ShowOnlyPredefinedDetails = true;

                gv.OptionsBehavior.Editable = false;
                gv.OptionsView.ColumnAutoWidth = false;
                gv.OptionsView.ShowGroupPanel = false;
                gv.GridControl = gc;
                gc.DataSource = detailList;
                gc.BringToFront();

                addTabControl(gc, resultList[j][0].GetToken(), resultList[j][0].GetTokenName());

                eList.Add(gv);
                sheetName.Add(resultList[j][0].GetTokenName());
            }
        }

        private void SetCaption(DevExpress.XtraGrid.Views.Grid.GridView gv)
        {
            foreach (DevExpress.XtraGrid.Columns.GridColumn cl in gv.Columns)
            {
                cl.Caption = cl.ToString();
            }
        }

        /// <summary>
        /// 根据数据库内容,动态添加tab
        /// </summary>
        /// <param name="gc"></param>
        /// <param name="name"></param>
        /// <param name="caption"></param>
        private void addTabControl(DevExpress.XtraGrid.GridControl gc, string name, string caption)
        {
            try
            {
                XtraTabPage page = new XtraTabPage();
                page.Name = name;
                page.Text = caption;
                gc.Dock = System.Windows.Forms.DockStyle.Fill;
                page.Controls.Add(gc);
                xtraTabCtl.TabPages.Add(page);
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message);
                throw;
            }
        }

        /// <summary>
        /// 自动绑定汇总列表的控件
        /// </summary>
        private void addBand()
        {
            DevExpress.XtraGrid.Views.BandedGrid.GridBand bandAll = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();

            DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colTotalAll = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colNewAll = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colSolutionsAll = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colRate = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            int index = 0;
            foreach (XtraTabPage page in xtraTabCtl.TabPages)
            {
                DevExpress.XtraGrid.Views.BandedGrid.GridBand band = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
                DevExpress.XtraGrid.Views.BandedGrid.GridBand bandTotal = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
                DevExpress.XtraGrid.Views.BandedGrid.GridBand bandNew = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
                DevExpress.XtraGrid.Views.BandedGrid.GridBand bandSolutions = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
                DevExpress.XtraGrid.Views.BandedGrid.GridBand bandRate = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();

                if (page.Text == "汇总")
                {
                    bandAll.Name = page.Text;
                    bandAll.Caption = page.Text;
                }
                else
                {
                    band.Name = page.Text;
                    band.Caption = page.Text;
                }
                bandTotal.Name = page.Text + "总数";
                bandTotal.Caption = "总数";
                bandNew.Name = page.Text + "新增数";
                bandNew.Caption ="新增数";
                bandSolutions.Name = page.Text + "解决数";
                bandSolutions.Caption = "解决数";
                if (page.Text == "汇总")
                {
                    bandRate.Name = page.Text + "解决率";
                    bandRate.Caption = "解决率";
                }

                DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colTotal = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
                DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colNew = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
                DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colSolutions = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
                
                if (page.Text == "汇总")
                {
                    colTotalAll.Name = page.Text + "总数";
                    colTotalAll.FieldName = page.Text + "总数";
                    colNewAll.Name = page.Text + "新增数";
                    colNewAll.FieldName = page.Text + "新增数";
                    colSolutionsAll.Name = page.Text + "解决数";
                    colSolutionsAll.FieldName = page.Text + "解决数";
                    colRate.Name = page.Text + "解决率";
                    colRate.FieldName = page.Text + "解决率";

                    bandTotal.Columns.Add(colTotalAll);
                    bandNew.Columns.Add(colNewAll);
                    bandSolutions.Columns.Add(colSolutionsAll);
                }
                else
                {
                    colTotal.Name = page.Text + "总数";
                    colTotal.FieldName = page.Name + "总数";
                    colTotal.VisibleIndex = index++;
                    colNew.Name = page.Text + "新增数";
                    colNew.FieldName = page.Name + "新增数";
                    colNew.VisibleIndex = index++;
                    colSolutions.Name = page.Text + "解决数";
                    colSolutions.FieldName = page.Name + "解决数";
                    colSolutions.VisibleIndex = index++;

                    bandTotal.Columns.Add(colTotal);
                    bandNew.Columns.Add(colNew);
                    bandSolutions.Columns.Add(colSolutions);
                }

                if (page.Text == "汇总")
                {
                    bandRate.Columns.Add(colRate);

                    bandAll.Children.Add(bandTotal);
                    bandAll.Children.Add(bandNew);
                    bandAll.Children.Add(bandSolutions);
                    bandAll.Children.Add(bandRate);
                }
                else
                {
                    band.Children.Add(bandTotal);
                    band.Children.Add(bandNew);
                    band.Children.Add(bandSolutions);

                    bandGV.Bands.Add(band);
                    bandGV.Columns.Add(colTotal);
                    bandGV.Columns.Add(colNew);
                    bandGV.Columns.Add(colSolutions);
                }
            }
            bandGV.Bands.Add(bandAll);

            colTotalAll.VisibleIndex = index++;
            colNewAll.VisibleIndex = index++;
            colSolutionsAll.VisibleIndex = index++;
            colRate.VisibleIndex = index;

            bandGV.Columns.Add(colTotalAll);
            bandGV.Columns.Add(colNewAll);
            bandGV.Columns.Add(colSolutionsAll);
            bandGV.Columns.Add(colRate);

            bandGV.Bands.AddBand("得分");
        }

        private void exportToExcel_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(listView, sheetName);
        }
    }
}
