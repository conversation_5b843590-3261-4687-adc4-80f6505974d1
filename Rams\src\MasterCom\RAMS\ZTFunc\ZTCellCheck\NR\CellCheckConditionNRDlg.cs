﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck
{
    public partial class CellCheckConditionNRDlg : BaseForm
    {
        public CellCheckConditionNRDlg()
        {
            InitializeComponent();
        }

        public void SetCondition(FuncConditionNR cond)
        {
            if (cond==null)
            {
                return;
            }
            numSiteDistance.Value = (decimal)cond.UltraSiteCondition.NearDistanceMin;
            numAltitude.Value = (decimal)cond.UltraSiteCondition.AltitudeMax;
            numDirRange.Value = cond.UltraSiteCondition.AvgSitesDistanceAngle;
            numAvgDistance.Value = (decimal)cond.UltraSiteCondition.AvgSiteDistanceMax;
            numDiffBandDis.Value = (decimal)cond.UltraSiteCondition.DiffBandDistanceMin;

            numOvrLapRSRP.Value = (decimal)cond.CoverlapRSRPMin;
            numSiteCnt.Value = cond.CoverSiteNum;
            numCvrDisFactor.Value = (decimal)cond.CvrDisFactorMax;

            numMultiCvrRSRP.Value = (decimal)cond.MultiCoverRSRP;
            numMultiRSRPDiff.Value = (decimal)cond.MultiCoverDiff;

            numMod3RSRP.Value = (decimal)cond.Mod3RSRP;
            numMod3RSRPDiff.Value = (decimal)cond.Mod3Diff;
            numMod4RSRP.Value = (decimal)cond.Mod4RSRP;
            numMod4RSRPDiff.Value = (decimal)cond.Mod4Diff;
            numMod6RSRP.Value = (decimal)cond.Mod6RSRP;
            numMod6RSRPDiff.Value = (decimal)cond.Mod6Diff;

            numWeakCoverRSRP.Value = (decimal)cond.WeakCoverRSRP;
        }

        public FuncConditionNR GetCondition()
        {
            FuncConditionNR cond = new FuncConditionNR();

            UltraSiteCondition uCond = new UltraSiteCondition();
            uCond.NearDistanceMin = (double)numSiteDistance.Value;
            uCond.AltitudeMax = (double)numAltitude.Value;
            uCond.AvgSitesDistanceAngle = (int)numDirRange.Value;
            uCond.AvgSiteDistanceMax = (double)numAvgDistance.Value;
            uCond.DiffBandDistanceMin = (double)numDiffBandDis.Value;
            cond.UltraSiteCondition = uCond;

            cond.CoverlapRSRPMin = (float)numOvrLapRSRP.Value;
            cond.CoverSiteNum = (int)numSiteCnt.Value;
            cond.CvrDisFactorMax = (double)numCvrDisFactor.Value;

            cond.MultiCoverRSRP = (float)numMultiCvrRSRP.Value;
            cond.MultiCoverDiff = (float)numMultiRSRPDiff.Value;

            cond.Mod3RSRP = (float)numMod3RSRP.Value;
            cond.Mod3Diff = (float)numMod3RSRPDiff.Value;
            cond.Mod4RSRP = (float)numMod4RSRP.Value;
            cond.Mod4Diff = (float)numMod4RSRPDiff.Value;
            cond.Mod6RSRP = (float)numMod6RSRP.Value;
            cond.Mod6Diff = (float)numMod6RSRPDiff.Value;

            cond.WeakCoverRSRP = (float)numWeakCoverRSRP.Value;
            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
