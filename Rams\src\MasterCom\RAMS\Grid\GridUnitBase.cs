﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Grid
{
    public class GridUnitBase
    {
        /// <summary>
        /// 0 未设置
        /// 1 已经设置
        /// 2 被插值填充的
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// 栅格大小系数
        /// </summary>
        public int SizeFactor { get; set; } = 1;
        public int RowIdx
        {
            get
            {
                int r, c;
                GridHelper.GetIndexOfCustomSizeGrid(SizeFactor, CenterLng, CenterLat, out r, out c);
                return r;
            }
            set
            {
                double lng;
                double lat;
                GridHelper.GetLeftTopByCustomSizeGridIndex(1, value, 0, out lng, out lat);
                LTLat = lat;
            }
        }
        public int ColIdx
        {
            get
            {
                int r, c;
                GridHelper.GetIndexOfCustomSizeGrid(SizeFactor, CenterLng, CenterLat, out r, out c);
                return c;
            }
            set {
                double lng;
                double lat;
                GridHelper.GetLeftTopByCustomSizeGridIndex(1, 0, value, out lng, out lat);
                LTLng = lng;
            }
        }
        public double LngSpan
        {//double精度问题，先转成decimal计算，再转回double
            get { return (double)((decimal)SizeFactor * (decimal)lngSpan); }
        }
        public double LatSpan
        {//double精度问题，先转成decimal计算，再转回double
            get { return (double)((decimal)SizeFactor * (decimal)latSpan); }
        }
        /// <summary>
        /// 中心经度
        /// </summary>
        public double CenterLng
        {
            get { return (double)((decimal)LTLng + (decimal)LngSpan / 2); }
        }
        /// <summary>
        /// 中心纬度
        /// </summary>
        public double CenterLat
        {
            get { return (double)((decimal)LTLat - (decimal)LatSpan / 2); }
        }
        /// <summary>
        /// 左上角经度
        /// </summary>
        public double LTLng { get; set; }
        /// <summary>
        /// 左上角纬度
        /// </summary>
        public double LTLat { get; set; }
        /// <summary>
        /// 右下角经度
        /// </summary>
        public double BRLng
        {
            get { return LTLng + lngSpan * SizeFactor; }
        }
        /// <summary>
        /// 右下角纬度
        /// </summary>
        public double BRLat
        {
            get { return LTLat - latSpan * SizeFactor; }
        }
        public DbRect Bounds
        {
            get { return new DbRect(LTLng, BRLat, BRLng, LTLat); }
        }
        /// <summary>
        /// 默认为普通栅格大小
        /// </summary>
        protected double lngSpan = CD.ATOM_SPAN_LONG;
        protected double latSpan = CD.ATOM_SPAN_LAT;
        public GridUnitBase(double ltLng,double ltLat)
        {
            this.LTLng = ltLng;
            this.LTLat = ltLat;
        }
        public GridUnitBase()
        {
 
        }
        public virtual void Merge(GridUnitBase grid2Merge)
        {

        }

        public bool Within(double minX, double minY, double maxX, double maxY)
        {
            if (BRLng < minX || LTLng > maxX || LTLat < minY || BRLat > maxY)
            {
                return false;
            }
            return true;
        }
        public bool Within(DbRect rect)
        {
            if (BRLng < rect.x1 || LTLng > rect.x2 || LTLat < rect.y1 || BRLat > rect.y2)
            {
                return false;
            }
            return true;
        }

        public override string ToString()
        {
            return string.Format("{0}_{1}", LTLng, LTLat);
        }
    }
}
