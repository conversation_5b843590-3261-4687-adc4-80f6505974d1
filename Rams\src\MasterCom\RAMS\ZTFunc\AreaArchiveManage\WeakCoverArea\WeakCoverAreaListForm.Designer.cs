﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.WeakCoverArea
{
    partial class WeakCoverAreaListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.lv = new BrightIdeasSoftware.ObjectListView();
            this.colSn = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colIsLackCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colRxLevAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNearestSiteName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNearestSiteDis = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNbWeakAreaNames = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNbWeakAreaNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.lv)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // lv
            // 
            this.lv.AllColumns.Add(this.colSn);
            this.lv.AllColumns.Add(this.colName);
            this.lv.AllColumns.Add(this.colIsLackCell);
            this.lv.AllColumns.Add(this.colRxLevAvg);
            this.lv.AllColumns.Add(this.colNearestSiteName);
            this.lv.AllColumns.Add(this.colNearestSiteDis);
            this.lv.AllColumns.Add(this.colNbWeakAreaNames);
            this.lv.AllColumns.Add(this.colNbWeakAreaNum);
            this.lv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colSn,
            this.colName,
            this.colIsLackCell,
            this.colRxLevAvg,
            this.colNearestSiteName,
            this.colNearestSiteDis,
            this.colNbWeakAreaNames,
            this.colNbWeakAreaNum});
            this.lv.ContextMenuStrip = this.ctxMenu;
            this.lv.Cursor = System.Windows.Forms.Cursors.Default;
            this.lv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lv.FullRowSelect = true;
            this.lv.GridLines = true;
            this.lv.HeaderWordWrap = true;
            this.lv.IsNeedShowOverlay = false;
            this.lv.Location = new System.Drawing.Point(0, 0);
            this.lv.Name = "lv";
            this.lv.OwnerDraw = true;
            this.lv.ShowGroups = false;
            this.lv.Size = new System.Drawing.Size(944, 362);
            this.lv.TabIndex = 6;
            this.lv.UseCompatibleStateImageBehavior = false;
            this.lv.View = System.Windows.Forms.View.Details;
            this.lv.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lv_MouseDoubleClick);
            // 
            // colSn
            // 
            this.colSn.HeaderFont = null;
            this.colSn.Text = "序号";
            this.colSn.Width = 80;
            // 
            // colName
            // 
            this.colName.HeaderFont = null;
            this.colName.Text = "名称";
            this.colName.Width = 180;
            // 
            // colIsLackCell
            // 
            this.colIsLackCell.HeaderFont = null;
            this.colIsLackCell.Text = "需要建站";
            // 
            // colRxLevAvg
            // 
            this.colRxLevAvg.HeaderFont = null;
            this.colRxLevAvg.Text = "平均场强(dBm)";
            // 
            // colNearestSiteName
            // 
            this.colNearestSiteName.HeaderFont = null;
            this.colNearestSiteName.Text = "最近基站";
            this.colNearestSiteName.Width = 120;
            // 
            // colNearestSiteDis
            // 
            this.colNearestSiteDis.HeaderFont = null;
            this.colNearestSiteDis.Text = "最近基站距离(米)";
            // 
            // colNbWeakAreaNames
            // 
            this.colNbWeakAreaNames.HeaderFont = null;
            this.colNbWeakAreaNames.Text = "相邻弱覆盖区域";
            this.colNbWeakAreaNames.Width = 250;
            // 
            // colNbWeakAreaNum
            // 
            this.colNbWeakAreaNum.HeaderFont = null;
            this.colNbWeakAreaNum.Text = "相邻弱覆盖区域个数";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 26);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // WeakCoverAreaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(944, 362);
            this.Controls.Add(this.lv);
            this.Name = "WeakCoverAreaListForm";
            this.Text = "弱覆盖区域列表";
            ((System.ComponentModel.ISupportInitialize)(this.lv)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.ObjectListView lv;
        private BrightIdeasSoftware.OLVColumn colSn;
        private BrightIdeasSoftware.OLVColumn colName;
        private BrightIdeasSoftware.OLVColumn colIsLackCell;
        private BrightIdeasSoftware.OLVColumn colRxLevAvg;
        private BrightIdeasSoftware.OLVColumn colNearestSiteName;
        private BrightIdeasSoftware.OLVColumn colNearestSiteDis;
        private BrightIdeasSoftware.OLVColumn colNbWeakAreaNames;
        private BrightIdeasSoftware.OLVColumn colNbWeakAreaNum;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
    }
}