﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;

namespace MasterCom.RAMS.ZTFunc
{
    public class AchiveBaseSetting : AreaKpiQueryBase
    {
        public AchiveBaseSetting(MainModel mModel)
            : base(mModel) { }

        public override string Name
        {
            get { return "基础设置"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31001, this.Name);
        }
        protected override bool setConditionDlg()
        {
            return false;
        }

        protected override void query()
        {
            ArchiveSettingManager manager = ArchiveSettingManager.GetInstance();
            manager.SetCondition();
        }

        protected override void fireShowForm()
        {
        }
    }
}
