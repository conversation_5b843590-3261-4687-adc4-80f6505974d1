﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRDropCallInfo
    {
        public NRDropCallInfo(int sn, NRDropCallAnaInfo call)
        {
            this.SN = sn;
            this.MoMtCalls = new List<NRDropCallAnaInfo>();
            if (call.MoMtDesc == "主叫")
            {
                MoMtCalls.Add(call);
                if (call.OtherSideCall != null)
                {
                    MoMtCalls.Add(call.OtherSideCall);
                }
            }
            else
            {
                if (call.OtherSideCall != null)
                {
                    MoMtCalls.Add(call.OtherSideCall);
                }
                MoMtCalls.Add(call);
            }
        }

        public int SN { get; private set; }

        public List<NRDropCallAnaInfo> MoMtCalls { get; set; }
    }
}
