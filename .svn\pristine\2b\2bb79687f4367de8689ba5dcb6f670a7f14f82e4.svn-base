﻿namespace MasterCom.ES.UI
{
    partial class MsgIDDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.lstViewMsg = new System.Windows.Forms.ListView();
            this.columnHeader1 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader2 = new System.Windows.Forms.ColumnHeader();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.rbGSMTD = new System.Windows.Forms.RadioButton();
            this.rbCDMA = new System.Windows.Forms.RadioButton();
            this.rbGsmUplink = new System.Windows.Forms.RadioButton();
            this.SuspendLayout();
            // 
            // lstViewMsg
            // 
            this.lstViewMsg.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.lstViewMsg.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeader2});
            this.lstViewMsg.FullRowSelect = true;
            this.lstViewMsg.Location = new System.Drawing.Point(12, 3);
            this.lstViewMsg.MultiSelect = false;
            this.lstViewMsg.Name = "lstViewMsg";
            this.lstViewMsg.Size = new System.Drawing.Size(341, 275);
            this.lstViewMsg.TabIndex = 0;
            this.lstViewMsg.UseCompatibleStateImageBehavior = false;
            this.lstViewMsg.View = System.Windows.Forms.View.Details;
            this.lstViewMsg.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lstViewMsg_MouseDoubleClick);
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "msgid";
            this.columnHeader1.Width = 91;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "Message";
            this.columnHeader2.Width = 236;
            // 
            // btnOK
            // 
            this.btnOK.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnOK.Location = new System.Drawing.Point(88, 310);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 1;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnCancel.Location = new System.Drawing.Point(210, 310);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // rbGSMTD
            // 
            this.rbGSMTD.AutoSize = true;
            this.rbGSMTD.Checked = true;
            this.rbGSMTD.Location = new System.Drawing.Point(12, 284);
            this.rbGSMTD.Name = "rbGSMTD";
            this.rbGSMTD.Size = new System.Drawing.Size(83, 16);
            this.rbGSMTD.TabIndex = 2;
            this.rbGSMTD.TabStop = true;
            this.rbGSMTD.Text = "GSM_TD消息";
            this.rbGSMTD.UseVisualStyleBackColor = true;
            this.rbGSMTD.CheckedChanged += new System.EventHandler(this.rbGSMTD_CheckedChanged);
            // 
            // rbCDMA
            // 
            this.rbCDMA.AutoSize = true;
            this.rbCDMA.Location = new System.Drawing.Point(113, 284);
            this.rbCDMA.Name = "rbCDMA";
            this.rbCDMA.Size = new System.Drawing.Size(71, 16);
            this.rbCDMA.TabIndex = 2;
            this.rbCDMA.Text = "CDMA消息";
            this.rbCDMA.UseVisualStyleBackColor = true;
            this.rbCDMA.CheckedChanged += new System.EventHandler(this.rbCDMA_CheckedChanged);
            // 
            // rbGsmUplink
            // 
            this.rbGsmUplink.AutoSize = true;
            this.rbGsmUplink.Location = new System.Drawing.Point(201, 284);
            this.rbGsmUplink.Name = "rbGsmUplink";
            this.rbGsmUplink.Size = new System.Drawing.Size(83, 16);
            this.rbGsmUplink.TabIndex = 3;
            this.rbGsmUplink.TabStop = true;
            this.rbGsmUplink.Text = "GSM Uplink";
            this.rbGsmUplink.UseVisualStyleBackColor = true;
            this.rbGsmUplink.CheckedChanged += new System.EventHandler(this.rbGsmUplink_CheckedChanged);
            // 
            // MsgIDDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(366, 345);
            this.Controls.Add(this.rbGsmUplink);
            this.Controls.Add(this.rbCDMA);
            this.Controls.Add(this.rbGSMTD);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.lstViewMsg);
            this.Name = "MsgIDDlg";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "MsgIDDlg";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ListView lstViewMsg;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.RadioButton rbGSMTD;
        private System.Windows.Forms.RadioButton rbCDMA;
        private System.Windows.Forms.RadioButton rbGsmUplink;
    }
}