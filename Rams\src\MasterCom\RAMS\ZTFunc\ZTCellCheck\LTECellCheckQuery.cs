﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc.ZTCellCheck;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTECellCheckQuery : DIYAnalyseByFileBackgroundBase
    {
        public LTECellCheckQuery()
            : base(MainModel.GetInstance())
        {

        }

        public override string Name
        {
            get
            {
                return "小区指标体检";
            }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22032, this.Name);
        }

        FuncCondition funcCond = null;
        protected override bool getCondition()
        {
            ConditionDlg dlg = new ConditionDlg();
            dlg.SetCondition(funcCond);
            if (dlg.ShowDialog()!=DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.GetCondition();
            cellCheckDic = new Dictionary<ICell, CellCheckInfo>();
            return true;
        }

        Dictionary<ICell, CellCheckInfo> cellCheckDic = null;
        private CellCheckInfo getCellCheckItem(ICell cell)
        {
            CellCheckInfo item = null;
            if (!cellCheckDic.TryGetValue(cell, out item))
            {
                item = new CellCheckInfo(cell, funcCond);
                cellCheckDic.Add(cell, item);
            }
            return item;
        }

        protected override void fireShowForm()
        {
            UltraSiteQuery qry = new UltraSiteQuery();
            qry.SetQueryCondition(condition);
            qry.ShowSettingDlg = false;
            qry.UltraSiteCondition = funcCond.UltraSiteCondition;
            qry.Query();

            Dictionary<ICell, List<UltraSiteCell>> cellDic = qry.Result;
            Dictionary<string, List<UltraSiteCell>> cellNameDic = new Dictionary<string, List<UltraSiteCell>>();
            foreach (ICell cell in cellDic.Keys)
            {
                cellNameDic[cell.Name]= cellDic[cell];
            }

            foreach (ICell cell in cellCheckDic.Keys)
            {
                CellCheckInfo info = cellCheckDic[cell];
                if (cellNameDic.ContainsKey(cell.Name))
                {
                    foreach (UltraSiteCell item in cellNameDic[cell.Name])
                    {
                        if (item is UltraFarSite)
                        {
                            info.IsUltraFar = true;
                        }
                        else if (item is UltraHighSite)
                        {
                            info.IsUltraHigh = true;
                        }
                        else if (item is UltraNearSite)
                        {
                            info.IsUltraNear = true;
                        }
                    }
                }
                info.MakeSummary();
            }

            List<CellCheckInfo> cells = new List<CellCheckInfo>(cellCheckDic.Values);
            ResultForm form = MainModel.GetObjectFromBlackboard(typeof(ResultForm)) as ResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new ResultForm();
            }
            form.FillData(cells);
            form.Owner = MainModel.MainForm;
            form.Visible = true;
            form.BringToFront();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint tp in file.TestPoints)
                {
                    LTECell cell = tp.GetMainLTECell_TdOrFdd();
                    float? rsrp = GetRSRP(tp);
                    if (cell == null || rsrp == null || rsrp > 25 || rsrp < -141)
                    {
                        continue;
                    }
                    CellCheckInfo cellChk = getCellCheckItem(cell);
                    cellChk.AddTestPoint();
                    int? pss = GetPSS(tp);//PCI mod 3
                    double distance = tp.Distance2(cell.Longitude, cell.Latitude);

                    bool isWeakCover = funcCond.IsWeakCover((float)rsrp);
                    cellChk.AddWeakCoverInfo(isWeakCover, (float)rsrp);

                    if (funcCond.IsCoverLap((float)rsrp, distance, cellChk.CellCvrDisMax))
                    {
                        cellChk.AddCoverLapInfo((float)rsrp, distance);
                    }
                    else
                    {
                        cellChk.AddNoneCoverLapInfo((float)rsrp, distance);
                    }

                    setCellCheckInfo(tp, rsrp, cellChk, pss);
                }
            }
        }

        private void setCellCheckInfo(TestPoint tp, float? rsrp, CellCheckInfo cellChk, int? pss)
        {
            int mod3CellCnt = 0;
            int multiCoverCnt = 1;//自身为1
            for (int i = 0; i < 10; i++)
            {
                float? nRsrp = GetNRSRP(tp, i);
                int? nPss = GetNPSS(tp, i);//PCI mod 3
                if (nRsrp == null)
                {
                    break;
                }
                addCnt(rsrp, pss, ref mod3CellCnt, ref multiCoverCnt, nRsrp, nPss);
            }
            cellChk.AddMultiCoverInfo(multiCoverCnt);
            cellChk.AddMod3Info(mod3CellCnt);
        }

        private void addCnt(float? rsrp, int? pss, ref int mod3CellCnt, ref int multiCoverCnt, float? nRsrp, int? nPss)
        {
            float diff = (float)rsrp - (float)nRsrp;
            if (nRsrp >= rsrp)
            {
                multiCoverCnt++;
                if (pss == nPss)
                {
                    mod3CellCnt++;
                }
            }
            else
            {
                if (diff >= funcCond.MultiCoverDiff)
                {//重叠覆盖
                    multiCoverCnt++;
                }
                if (diff >= funcCond.Mod3Diff && pss == nPss)
                {//模三干扰
                    mod3CellCnt++;
                }
            }
        }

        protected virtual float? GetRSRP(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }
        protected virtual int? GetPSS(TestPoint tp)
        {
            return (int?)tp["lte_SCell_PSS"];
        }
        protected virtual float? GetNRSRP(TestPoint tp,int index)
        {
            return (float?)tp["lte_NCell_RSRP", index];
        }
        protected virtual int? GetNPSS(TestPoint tp,int index)
        {
            return (int?)tp["lte_NCell_PSS", index];
        }

    }

    public class CellCheckInfo
    {
        public ICell Cell
        { get; private set; }

        public double CellCvrDisMax
        {
            get;
            private set;
        }

        public double MultiCoverFactor
        {
            get;
            private set;
        }

        public CellCheckInfo(ICell cell, FuncCondition condition)
        {
            this.Cell = cell;
            double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius((LTECell)cell
                , condition.CoverSiteNum);
            CellCvrDisMax = radiusOfCell * condition.CvrDisFactorMax;
        }

        float coverLapTotalRSRP = 0;
        int coverLapCnt = 0;
        double coverLapTotalDis = 0;
        internal void AddCoverLapInfo(float rsrp, double distance)
        {
            coverLapTotalRSRP += rsrp;
            coverLapTotalDis += distance;
            coverLapCnt++;
        }

        float noneCoverLapTotalRSRP = 0;
        int noneCoverLapCnt = 0;
        double noneCoverLapTotalDis = 0;
        internal void AddNoneCoverLapInfo(float rsrp, double distance)
        {
            noneCoverLapTotalRSRP += rsrp;
            noneCoverLapTotalDis += distance;
            noneCoverLapCnt++;
        }

        int multiCoverTotalCellCnt = 0;
        int multiCovertTotalTpCnt = 0;
        internal void AddMultiCoverInfo(int multiCoverCnt)
        {
            multiCoverTotalCellCnt += multiCoverCnt;
            multiCovertTotalTpCnt++;
        }

        int mod3TotalCellCnt = 0;
        int mod3TotalTpCnt = 0;
        int mod3NoneTpCnt = 0;
        internal void AddMod3Info(int mod3CellCnt)
        {
            mod3TotalTpCnt++;
            if (mod3CellCnt == 0)
            {
                mod3NoneTpCnt++;
            }
            else
            {
                mod3TotalCellCnt += mod3CellCnt;
            }
        }

        public UltraFarSite FarSite
        {
            get;
            set;
        }
        public bool IsUltraFar
        {
            get;
            set;
        }
        public string UltraFarDesc
        {
            get
            {
                if (IsUltraFar)
                {
                    return "是";
                }
                return "否";
            }
        }

        public bool IsUltraHigh
        {
            get;
            set;
        }
        public UltraHighSite HighSite
        {
            get;
            set;
        }
        public string UltraHighDesc
        {
            get
            {
                if (IsUltraHigh)
                {
                    return "是";
                }
                return "否";
            }
        }

        public UltraNearSite NearSite
        {
            get;
            set;
        }
        public string UltraNearDesc
        {
            get
            {
                if (IsUltraNear)
                {
                    return "是";
                }
                return "否";
            }
        }
        public bool IsUltraNear
        {
            get;
            set;
        }

        float weakCoverTotalRSRP = 0;
        int weakCoverCnt = 0;
        float noneWeakCoverTotalRSRP = 0;
        int noneWeakCoverCnt = 0;
        int totalTpCnt = 0;
        public int TotalTpCnt
        {
            get { return totalTpCnt; }
        }
        public void AddTestPoint()
        {
            totalTpCnt++;
        }
        internal void AddWeakCoverInfo(bool isWeakCover, float rsrp)
        {
            if (isWeakCover)
            {
                weakCoverTotalRSRP += rsrp;
                weakCoverCnt++;
            }
            else
            {
                noneWeakCoverTotalRSRP += rsrp;
                noneWeakCoverCnt++;
            }
        }

        public double WeakCoverRate
        {
            get;
            private set;
        }
        public double CoverLapRate
        {
            get;
            private set;
        }
        public double AvgMod3 { get; private set; }
        public double AvgMultiCover
        {
            get;
            private set;
        }
        internal void MakeSummary()
        {
            WeakCoverRate = Math.Round(100.0 * weakCoverCnt / (weakCoverCnt + noneWeakCoverCnt), 2);
            CoverLapRate = Math.Round(100.0 * coverLapCnt / (coverLapCnt + noneCoverLapCnt), 2);
            AvgMod3 = Math.Round(1.0 * mod3TotalCellCnt / mod3TotalTpCnt, 2);
            AvgMultiCover = Math.Round(1.0 * multiCoverTotalCellCnt / multiCovertTotalTpCnt, 2);
        }

    }

    public class LTECellCheckQuery_FDD : LTECellCheckQuery
    {
        public LTECellCheckQuery_FDD()
            : base()
        {

        }

        public override string Name
        {
            get
            {
                return "小区指标体检_LTE_FDD";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 26000, 26028, this.Name);
        }

        protected override float? GetRSRP(TestPoint tp)
        {
            return (float?)tp["lte_fdd_RSRP"];
        }
        protected override int? GetPSS(TestPoint tp)
        {
            return (int?)tp["lte_fdd_SCell_PSS"];
        }
        protected override float? GetNRSRP(TestPoint tp, int index)
        {
            return (float?)tp["lte_fdd_NCell_RSRP", index];
        }
        protected override int? GetNPSS(TestPoint tp, int index)
        {
            return (int?)tp["lte_fdd_NCell_PSS", index];
        }
    }

}
