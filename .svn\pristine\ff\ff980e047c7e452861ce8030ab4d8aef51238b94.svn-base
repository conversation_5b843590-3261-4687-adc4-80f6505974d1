﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.KPI_Statistics
{
    public partial class ReportGridView : UserControl
    {
        public ReportGridView()
        {
            InitializeComponent();
        }

        KPIDataGroup dataGrp = null;
        ReportStyle report = null;
        public void FillData(ReportStyle report,KPIDataGroup data)
        {
            this.report = report;
            this.dataGrp = data;
            refreshView();
        }

        private void refreshView()
        {
            if (dataGrp.IsSummaryGroup)
            {
                tbxGrpInfo.BackColor = Color.Pink;
            }
            else
            {
                tbxGrpInfo.BackColor = Color.YellowGreen;
            }
            tbxGrpInfo.Text = dataGrp.ToString();

            dataGridView.Columns.Clear();
            dataGridView.Rows.Clear();
            for (int i = 0; i < report.ColCount; i++)
            {
                DataGridViewTextBoxColumn col = new DataGridViewTextBoxColumn();
                col.Width = report.rptColWidths[0];
                dataGridView.Columns.Add(col);
            }
            dataGridView.RowCount = report.RowCount;
            foreach (RptCell cell in report.rptCellList)
            {
                if (string.IsNullOrEmpty(cell.exp))
                {
                    continue;
                }
                DataGridViewCell viewCell = dataGridView.Rows[cell.rowAt].Cells[cell.colAt];
                viewCell.Style.BackColor = cell.bkColor;
                viewCell.Style.ForeColor = cell.foreColor;
                setViewCellValue(cell, viewCell);
            }
        }

        private void setViewCellValue(RptCell cell, DataGridViewCell viewCell)
        {
            if (cell.exp.Contains("{") && cell.exp.Contains("}"))
            {
                string exp = cell.exp.Substring(cell.exp.IndexOf("{")
                    , cell.exp.IndexOf("}") + 1);
                double val = dataGrp.CalcFormula((Model.CarrierType)cell.carrierID, cell.momt, exp);
                if (double.IsNaN(val))
                {
                    viewCell.Value = string.Intern("-");
                }
                else
                {
                    if (exp.Equals(cell.exp.Trim()))
                    {
                        viewCell.Value = val;
                    }
                    else
                    {
                        viewCell.Value = cell.exp.Replace(exp, val.ToString());
                    }
                }
            }
            else
            {
                viewCell.Value = cell.exp;
            }
        }

        public int ViewWidth
        {
            get
            {
                int x = 0;
                foreach (DataGridViewColumn col in dataGridView.Columns)
                {
                    x += col.Width;
                }
                return x;
            }
        }

        public int ViewHeight
        {
            get
            {
                int w = 0;
                foreach (DataGridViewRow row in dataGridView.Rows)
                {
                    w += row.Height;
                }
                w += tbxGrpInfo.Height;
                return w;
            }
        }
    }
}
