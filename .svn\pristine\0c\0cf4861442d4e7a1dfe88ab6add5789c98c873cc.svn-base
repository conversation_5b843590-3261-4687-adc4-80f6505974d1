﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class EndToEndSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numImssip2activaterequest = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.numActivateaccept2Deactivaterequest = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numImssipinvite2Activateaccept = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.numDetachrequest2accept = new System.Windows.Forms.NumericUpDown();
            this.label10 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.numMoISB2MtISB = new System.Windows.Forms.NumericUpDown();
            this.label14 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.numISISession1832ISCancel = new System.Windows.Forms.NumericUpDown();
            this.label17 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.numISBOK2002ISBRequest487 = new System.Windows.Forms.NumericUpDown();
            this.label20 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.numTrying1002Unavailable503 = new System.Windows.Forms.NumericUpDown();
            this.label23 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.label25 = new System.Windows.Forms.Label();
            this.numTrying1002Dearequest2Unavailable503 = new System.Windows.Forms.NumericUpDown();
            this.label26 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.label28 = new System.Windows.Forms.Label();
            this.numImssipinvite2Paging = new System.Windows.Forms.NumericUpDown();
            this.label29 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.label31 = new System.Windows.Forms.Label();
            this.numISU2Error5002ISC = new System.Windows.Forms.NumericUpDown();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.label32 = new System.Windows.Forms.Label();
            this.label33 = new System.Windows.Forms.Label();
            this.label34 = new System.Windows.Forms.Label();
            this.label35 = new System.Windows.Forms.Label();
            this.label36 = new System.Windows.Forms.Label();
            this.numActivate2Session183 = new System.Windows.Forms.NumericUpDown();
            this.label37 = new System.Windows.Forms.Label();
            this.label38 = new System.Windows.Forms.Label();
            this.label39 = new System.Windows.Forms.Label();
            this.label40 = new System.Windows.Forms.Label();
            this.label41 = new System.Windows.Forms.Label();
            this.label42 = new System.Windows.Forms.Label();
            this.label43 = new System.Windows.Forms.Label();
            this.label44 = new System.Windows.Forms.Label();
            this.label45 = new System.Windows.Forms.Label();
            this.label46 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numImssip2activaterequest)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numActivateaccept2Deactivaterequest)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numImssipinvite2Activateaccept)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDetachrequest2accept)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMoISB2MtISB)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numISISession1832ISCancel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numISBOK2002ISBRequest487)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTrying1002Unavailable503)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTrying1002Dearequest2Unavailable503)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numImssipinvite2Paging)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numISU2Error5002ISC)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numActivate2Session183)).BeginInit();
            this.SuspendLayout();
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(89, 36);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(389, 12);
            this.label4.TabIndex = 14;
            this.label4.Text = "IMS_SIP_INVITE=>Activate dedicated EPS bearer context request ≥";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(536, 35);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 13;
            this.label3.Text = "秒";
            // 
            // numImssip2activaterequest
            // 
            this.numImssip2activaterequest.Location = new System.Drawing.Point(483, 31);
            this.numImssip2activaterequest.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numImssip2activaterequest.Name = "numImssip2activaterequest";
            this.numImssip2activaterequest.Size = new System.Drawing.Size(47, 21);
            this.numImssip2activaterequest.TabIndex = 12;
            this.numImssip2activaterequest.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.Location = new System.Drawing.Point(89, 60);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(296, 26);
            this.label1.TabIndex = 17;
            this.label1.Text = "Activate dedicated EPS bearer context accept =>  Deactivate EPS bearer context re" +
    "quest ";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(536, 64);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 16;
            this.label2.Text = "秒";
            // 
            // numActivateaccept2Deactivaterequest
            // 
            this.numActivateaccept2Deactivaterequest.Location = new System.Drawing.Point(483, 62);
            this.numActivateaccept2Deactivaterequest.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numActivateaccept2Deactivaterequest.Name = "numActivateaccept2Deactivaterequest";
            this.numActivateaccept2Deactivaterequest.Size = new System.Drawing.Size(47, 21);
            this.numActivateaccept2Deactivaterequest.TabIndex = 15;
            this.numActivateaccept2Deactivaterequest.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(89, 96);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(389, 12);
            this.label5.TabIndex = 20;
            this.label5.Text = "IMS_SIP_INVITE=>Activate dedicated EPS bearer context accept  ≥";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(536, 95);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(17, 12);
            this.label6.TabIndex = 19;
            this.label6.Text = "秒";
            // 
            // numImssipinvite2Activateaccept
            // 
            this.numImssipinvite2Activateaccept.Location = new System.Drawing.Point(483, 91);
            this.numImssipinvite2Activateaccept.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numImssipinvite2Activateaccept.Name = "numImssipinvite2Activateaccept";
            this.numImssipinvite2Activateaccept.Size = new System.Drawing.Size(47, 21);
            this.numImssipinvite2Activateaccept.TabIndex = 18;
            this.numImssipinvite2Activateaccept.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(460, 65);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 21;
            this.label7.Text = "≤";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(89, 156);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(203, 12);
            this.label8.TabIndex = 24;
            this.label8.Text = "Detach request  =>  Detach accept";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(536, 155);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(17, 12);
            this.label9.TabIndex = 23;
            this.label9.Text = "秒";
            // 
            // numDetachrequest2accept
            // 
            this.numDetachrequest2accept.Location = new System.Drawing.Point(483, 151);
            this.numDetachrequest2accept.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numDetachrequest2accept.Name = "numDetachrequest2accept";
            this.numDetachrequest2accept.Size = new System.Drawing.Size(47, 21);
            this.numDetachrequest2accept.TabIndex = 22;
            this.numDetachrequest2accept.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(461, 156);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(17, 12);
            this.label10.TabIndex = 25;
            this.label10.Text = "≤";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(461, 184);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(17, 12);
            this.label11.TabIndex = 29;
            this.label11.Text = "≤";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(89, 184);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(221, 12);
            this.label12.TabIndex = 28;
            this.label12.Text = "MO: IMS_SIP_BYE  =>  MT: IMS_SIP_BYE";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(536, 183);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 27;
            this.label13.Text = "秒";
            // 
            // numMoISB2MtISB
            // 
            this.numMoISB2MtISB.Location = new System.Drawing.Point(483, 179);
            this.numMoISB2MtISB.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numMoISB2MtISB.Name = "numMoISB2MtISB";
            this.numMoISB2MtISB.Size = new System.Drawing.Size(47, 21);
            this.numMoISB2MtISB.TabIndex = 26;
            this.numMoISB2MtISB.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(461, 210);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 12);
            this.label14.TabIndex = 33;
            this.label14.Text = "≥";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(89, 210);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(335, 12);
            this.label15.TabIndex = 32;
            this.label15.Text = "IMS_SIP_INVITE->Session_Progress(183) => IMS_SIP_CANCEL";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(536, 209);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(17, 12);
            this.label16.TabIndex = 31;
            this.label16.Text = "秒";
            // 
            // numISISession1832ISCancel
            // 
            this.numISISession1832ISCancel.Location = new System.Drawing.Point(483, 205);
            this.numISISession1832ISCancel.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numISISession1832ISCancel.Name = "numISISession1832ISCancel";
            this.numISISession1832ISCancel.Size = new System.Drawing.Size(47, 21);
            this.numISISession1832ISCancel.TabIndex = 30;
            this.numISISession1832ISCancel.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(461, 251);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(17, 12);
            this.label17.TabIndex = 37;
            this.label17.Text = "≤";
            // 
            // label18
            // 
            this.label18.Location = new System.Drawing.Point(89, 235);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(262, 40);
            this.label18.TabIndex = 36;
            this.label18.Text = "IMS_SIP_BYE(up) => IMS_SIP_BYE(down) => IMS_SIP_BYE->OK(200) => IMS_SIP_BYE->Requ" +
    "est_Terminated(487)";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(536, 250);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(17, 12);
            this.label19.TabIndex = 35;
            this.label19.Text = "秒";
            // 
            // numISBOK2002ISBRequest487
            // 
            this.numISBOK2002ISBRequest487.Location = new System.Drawing.Point(483, 246);
            this.numISBOK2002ISBRequest487.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numISBOK2002ISBRequest487.Name = "numISBOK2002ISBRequest487";
            this.numISBOK2002ISBRequest487.Size = new System.Drawing.Size(47, 21);
            this.numISBOK2002ISBRequest487.TabIndex = 34;
            this.numISBOK2002ISBRequest487.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(461, 285);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(17, 12);
            this.label20.TabIndex = 41;
            this.label20.Text = "≤";
            // 
            // label21
            // 
            this.label21.Location = new System.Drawing.Point(88, 281);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(248, 31);
            this.label21.TabIndex = 40;
            this.label21.Text = "IMS_SIP_INVITE->Trying(100) => IMS_SIP_INVITE->Service_Unavailable(503)";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(536, 284);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(17, 12);
            this.label22.TabIndex = 39;
            this.label22.Text = "秒";
            // 
            // numTrying1002Unavailable503
            // 
            this.numTrying1002Unavailable503.Location = new System.Drawing.Point(483, 280);
            this.numTrying1002Unavailable503.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numTrying1002Unavailable503.Name = "numTrying1002Unavailable503";
            this.numTrying1002Unavailable503.Size = new System.Drawing.Size(47, 21);
            this.numTrying1002Unavailable503.TabIndex = 38;
            this.numTrying1002Unavailable503.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(461, 329);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(17, 12);
            this.label23.TabIndex = 45;
            this.label23.Text = "≥";
            // 
            // label24
            // 
            this.label24.Location = new System.Drawing.Point(88, 315);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(248, 44);
            this.label24.TabIndex = 44;
            this.label24.Text = "IMS_SIP_INVITE->Trying(100) => Deactivate EPS bearer context request => IMS_SIP_I" +
    "NVITE->Service_Unavailable(503)";
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(536, 328);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(17, 12);
            this.label25.TabIndex = 43;
            this.label25.Text = "秒";
            // 
            // numTrying1002Dearequest2Unavailable503
            // 
            this.numTrying1002Dearequest2Unavailable503.Location = new System.Drawing.Point(483, 324);
            this.numTrying1002Dearequest2Unavailable503.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numTrying1002Dearequest2Unavailable503.Name = "numTrying1002Dearequest2Unavailable503";
            this.numTrying1002Dearequest2Unavailable503.Size = new System.Drawing.Size(47, 21);
            this.numTrying1002Dearequest2Unavailable503.TabIndex = 42;
            this.numTrying1002Dearequest2Unavailable503.Value = new decimal(new int[] {
            7,
            0,
            0,
            0});
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(460, 359);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(17, 12);
            this.label26.TabIndex = 49;
            this.label26.Text = "≤";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(88, 359);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(149, 12);
            this.label27.TabIndex = 48;
            this.label27.Text = "IMS_SIP_INVITE => Paging";
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(535, 358);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(17, 12);
            this.label28.TabIndex = 47;
            this.label28.Text = "秒";
            // 
            // numImssipinvite2Paging
            // 
            this.numImssipinvite2Paging.Location = new System.Drawing.Point(482, 354);
            this.numImssipinvite2Paging.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numImssipinvite2Paging.Name = "numImssipinvite2Paging";
            this.numImssipinvite2Paging.Size = new System.Drawing.Size(47, 21);
            this.numImssipinvite2Paging.TabIndex = 46;
            this.numImssipinvite2Paging.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(460, 398);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(17, 12);
            this.label29.TabIndex = 53;
            this.label29.Text = "≥";
            // 
            // label30
            // 
            this.label30.Location = new System.Drawing.Point(88, 384);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(283, 43);
            this.label30.TabIndex = 52;
            this.label30.Text = "IMS_SIP_UPDATE => IMS_SIP_UPDATE->Server_Internal_Error(500) => IMS_SIP_CANCEL";
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(535, 397);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(17, 12);
            this.label31.TabIndex = 51;
            this.label31.Text = "秒";
            // 
            // numISU2Error5002ISC
            // 
            this.numISU2Error5002ISC.Location = new System.Drawing.Point(482, 393);
            this.numISU2Error5002ISC.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numISU2Error5002ISC.Name = "numISU2Error5002ISC";
            this.numISU2Error5002ISC.Size = new System.Drawing.Size(47, 21);
            this.numISU2Error5002ISC.TabIndex = 50;
            this.numISU2Error5002ISC.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(472, 455);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 55;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(391, 455);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 54;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(65, 36);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(23, 12);
            this.label32.TabIndex = 56;
            this.label32.Text = "1、";
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(65, 65);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(23, 12);
            this.label33.TabIndex = 57;
            this.label33.Text = "2、";
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(460, 125);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(17, 12);
            this.label34.TabIndex = 61;
            this.label34.Text = "≤";
            // 
            // label35
            // 
            this.label35.Location = new System.Drawing.Point(89, 120);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(296, 26);
            this.label35.TabIndex = 60;
            this.label35.Text = "Activate dedicated EPS bearer context accept =>  IMS_SIP_INVITE->Session_Progress" +
    "(183)";
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(536, 124);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(17, 12);
            this.label36.TabIndex = 59;
            this.label36.Text = "秒";
            // 
            // numActivate2Session183
            // 
            this.numActivate2Session183.Location = new System.Drawing.Point(483, 122);
            this.numActivate2Session183.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numActivate2Session183.Name = "numActivate2Session183";
            this.numActivate2Session183.Size = new System.Drawing.Size(47, 21);
            this.numActivate2Session183.TabIndex = 58;
            this.numActivate2Session183.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(65, 96);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(23, 12);
            this.label37.TabIndex = 62;
            this.label37.Text = "3、";
            // 
            // label38
            // 
            this.label38.AutoSize = true;
            this.label38.Location = new System.Drawing.Point(65, 125);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(23, 12);
            this.label38.TabIndex = 63;
            this.label38.Text = "4、";
            // 
            // label39
            // 
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(65, 156);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(23, 12);
            this.label39.TabIndex = 64;
            this.label39.Text = "5、";
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(65, 184);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(23, 12);
            this.label40.TabIndex = 65;
            this.label40.Text = "6、";
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(65, 210);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(23, 12);
            this.label41.TabIndex = 66;
            this.label41.Text = "7、";
            // 
            // label42
            // 
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(65, 246);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(23, 12);
            this.label42.TabIndex = 67;
            this.label42.Text = "8、";
            // 
            // label43
            // 
            this.label43.AutoSize = true;
            this.label43.Location = new System.Drawing.Point(65, 285);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(23, 12);
            this.label43.TabIndex = 68;
            this.label43.Text = "9、";
            // 
            // label44
            // 
            this.label44.AutoSize = true;
            this.label44.Location = new System.Drawing.Point(60, 329);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(29, 12);
            this.label44.TabIndex = 69;
            this.label44.Text = "10、";
            // 
            // label45
            // 
            this.label45.AutoSize = true;
            this.label45.Location = new System.Drawing.Point(60, 359);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(29, 12);
            this.label45.TabIndex = 70;
            this.label45.Text = "11、";
            // 
            // label46
            // 
            this.label46.AutoSize = true;
            this.label46.Location = new System.Drawing.Point(61, 397);
            this.label46.Name = "label46";
            this.label46.Size = new System.Drawing.Size(29, 12);
            this.label46.TabIndex = 71;
            this.label46.Text = "12、";
            // 
            // EndToEndSettingForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(615, 499);
            this.Controls.Add(this.label46);
            this.Controls.Add(this.label45);
            this.Controls.Add(this.label44);
            this.Controls.Add(this.label43);
            this.Controls.Add(this.label42);
            this.Controls.Add(this.label41);
            this.Controls.Add(this.label40);
            this.Controls.Add(this.label39);
            this.Controls.Add(this.label38);
            this.Controls.Add(this.label37);
            this.Controls.Add(this.label34);
            this.Controls.Add(this.label35);
            this.Controls.Add(this.label36);
            this.Controls.Add(this.numActivate2Session183);
            this.Controls.Add(this.label33);
            this.Controls.Add(this.label32);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.label29);
            this.Controls.Add(this.label30);
            this.Controls.Add(this.label31);
            this.Controls.Add(this.numISU2Error5002ISC);
            this.Controls.Add(this.label26);
            this.Controls.Add(this.label27);
            this.Controls.Add(this.label28);
            this.Controls.Add(this.numImssipinvite2Paging);
            this.Controls.Add(this.label23);
            this.Controls.Add(this.label24);
            this.Controls.Add(this.label25);
            this.Controls.Add(this.numTrying1002Dearequest2Unavailable503);
            this.Controls.Add(this.label20);
            this.Controls.Add(this.label21);
            this.Controls.Add(this.label22);
            this.Controls.Add(this.numTrying1002Unavailable503);
            this.Controls.Add(this.label17);
            this.Controls.Add(this.label18);
            this.Controls.Add(this.label19);
            this.Controls.Add(this.numISBOK2002ISBRequest487);
            this.Controls.Add(this.label14);
            this.Controls.Add(this.label15);
            this.Controls.Add(this.label16);
            this.Controls.Add(this.numISISession1832ISCancel);
            this.Controls.Add(this.label11);
            this.Controls.Add(this.label12);
            this.Controls.Add(this.label13);
            this.Controls.Add(this.numMoISB2MtISB);
            this.Controls.Add(this.label10);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.label9);
            this.Controls.Add(this.numDetachrequest2accept);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.numImssipinvite2Activateaccept);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.numActivateaccept2Deactivaterequest);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.numImssip2activaterequest);
            this.Name = "EndToEndSettingForm";
            this.Text = "端对端条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numImssip2activaterequest)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numActivateaccept2Deactivaterequest)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numImssipinvite2Activateaccept)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDetachrequest2accept)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMoISB2MtISB)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numISISession1832ISCancel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numISBOK2002ISBRequest487)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTrying1002Unavailable503)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTrying1002Dearequest2Unavailable503)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numImssipinvite2Paging)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numISU2Error5002ISC)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numActivate2Session183)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numImssip2activaterequest;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numActivateaccept2Deactivaterequest;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numImssipinvite2Activateaccept;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown numDetachrequest2accept;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.NumericUpDown numMoISB2MtISB;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.NumericUpDown numISISession1832ISCancel;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.NumericUpDown numISBOK2002ISBRequest487;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.NumericUpDown numTrying1002Unavailable503;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.NumericUpDown numTrying1002Dearequest2Unavailable503;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.NumericUpDown numImssipinvite2Paging;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.NumericUpDown numISU2Error5002ISC;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.NumericUpDown numActivate2Session183;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.Label label43;
        private System.Windows.Forms.Label label44;
        private System.Windows.Forms.Label label45;
        private System.Windows.Forms.Label label46;
    }
}