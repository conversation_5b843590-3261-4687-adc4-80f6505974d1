﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRLastWeakMosAnaDlg : MinCloseForm
    {
        public NRLastWeakMosAnaDlg()
        {
            InitializeComponent();
            spinEditTPNum.Value = 2;

            comboBoxMOS.Items.Add("3.0");

            spinEditTPNum.ValueChanged += spinEditTPNum_ValueChanged;
        }

        void spinEditTPNum_ValueChanged(object sender, EventArgs e)
        {
            double d = (double)spinEditTPNum.Value;
            if (d > 9999) d = 9999;
            if (d < 0) d = 0;
            d = Math.Round(d, 0);
            spinEditTPNum.Value = (decimal)d;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public void SetValue(NRLastWeakMosCondition curCondtion)
        {
            comboBoxMOS.SelectedIndex = (int)curCondtion.EventType;
            spinEditTPNum.Value = curCondtion.TpNum;
        }

        public void GetResult(NRLastWeakMosCondition curCondtion)
        {
            curCondtion.EventType = (ENRWeakMosEventType)comboBoxMOS.SelectedIndex;
            curCondtion.TpNum = (int)spinEditTPNum.Value;
        }
    }
}
