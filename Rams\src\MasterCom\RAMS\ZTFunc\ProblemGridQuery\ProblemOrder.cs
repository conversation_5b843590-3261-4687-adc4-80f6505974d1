﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ProblemGridQuery
{
    public class ProblemOrder
    {
        public DbRect Bounds
        {
            get
            {
                DbRect bounds = null;
                foreach (ProblemGrid grid in Grids)
                {
                    if (bounds == null)
                    {
                        bounds = grid.Bounds.Clone();
                    }
                    else
                    {
                        bounds.MergeRects(grid.Bounds);
                    }
                }
                return bounds;
            }
        }

        public ProblemOrder(Net.Content content)
        {
            Grids = new List<ProblemGrid>();
            //txt = "select orderID,网格号,栅格号,中心经度,中心纬度,测试日期,测试文件,项目类型,业务类型,问题类型,问题栅格数,涉及问题小区,状态,cityID,projectID,serviceID from [KPIMNG_DB_SHANDONG].[dbo].[tb_order_info]";
            this.ID = content.GetParamInt();
            this.AreaName = content.GetParamString();
            this.GridName = content.GetParamString();
            this.CenterLng = content.GetParamString();
            this.CenterLat = content.GetParamString();
            this.TestDate = content.GetParamString();
            this.FileNames = content.GetParamString();
            this.ProjectName = content.GetParamString();
            this.ServiceName = content.GetParamString();
            this.ProbType = content.GetParamString();
            this.GridCount = content.GetParamInt();
            this.Cells = content.GetParamString();
            this.Status = content.GetParamInt();
            this.CityID = content.GetParamInt();
            this.ProjectType = content.GetParamInt();
            this.ServiceType = content.GetParamInt();
        }
        public List<ProblemGrid> Grids
        {
            get;
            set;
        }

        public int ID
        {
            get;
            set;
        }

        public string AreaName
        {
            get;
            set;
        }

        public string Cells
        { get; set; }

        public int CityID
        { get; set; }

        public string CityName
        {
            get { return DistrictManager.GetInstance().getDistrictName(CityID); }
        }

        public int GridCount
        { get; set; }



        public string ProbType { get; set; }

        public string GridName { get; set; }

        public string CenterLat { get; set; }

        public string CenterLng { get; set; }

        public string TestDate { get; set; }

        public string FileNames { get; set; }

        public string ProjectName { get; set; }

        public string ServiceName { get; set; }

        public int Status { get; set; }

        public int ProjectType { get; set; }

        public int ServiceType { get; set; }

        internal void AddGrid(ProblemGrid grid)
        {
            grid.Order = this;
            this.Grids.Add(grid);
        }
    }
}
