﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    partial class AreaKPIReportForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeList = new DevExpress.XtraTreeList.TreeList();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemGrid = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemSample = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemCellCoverage = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.lbxLegend = new System.Windows.Forms.ListBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.cbxRank = new System.Windows.Forms.ComboBox();
            this.cbxCol = new System.Windows.Forms.ComboBox();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.gridControlFileInfo = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuFile = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miRelayFiles = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewFile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlFileInfo)).BeginInit();
            this.ctxMenuFile.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewFile)).BeginInit();
            this.SuspendLayout();
            // 
            // treeList
            // 
            this.treeList.ContextMenuStrip = this.ctxMenu;
            this.treeList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeList.Location = new System.Drawing.Point(0, 0);
            this.treeList.Name = "treeList";
            this.treeList.OptionsView.AutoWidth = false;
            this.treeList.Size = new System.Drawing.Size(756, 441);
            this.treeList.TabIndex = 0;
            this.treeList.NodeCellStyle += new DevExpress.XtraTreeList.GetCustomNodeCellStyleEventHandler(this.treeList_NodeCellStyle);
            this.treeList.FocusedNodeChanged += new DevExpress.XtraTreeList.FocusedNodeChangedEventHandler(this.treeList_FocusedNodeChanged);
            this.treeList.FocusedColumnChanged += new DevExpress.XtraTreeList.FocusedColumnChangedEventHandler(this.treeList_FocusedColumnChanged);
            this.treeList.BeforeExpand += new DevExpress.XtraTreeList.BeforeExpandEventHandler(this.treeList_BeforeExpand);
            this.treeList.DoubleClick += new System.EventHandler(this.treeList_DoubleClick);
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls,
            this.ToolStripMenuItemGrid,
            this.ToolStripMenuItemSample,
            this.ToolStripMenuItemCellCoverage});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(149, 92);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(148, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // ToolStripMenuItemGrid
            // 
            this.ToolStripMenuItemGrid.Name = "ToolStripMenuItemGrid";
            this.ToolStripMenuItemGrid.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemGrid.Text = "栅格轨迹";
            this.ToolStripMenuItemGrid.Click += new System.EventHandler(this.ToolStripMenuItemGrid_Click);
            // 
            // ToolStripMenuItemSample
            // 
            this.ToolStripMenuItemSample.Name = "ToolStripMenuItemSample";
            this.ToolStripMenuItemSample.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemSample.Text = "采样点轨迹";
            this.ToolStripMenuItemSample.Click += new System.EventHandler(this.ToolStripMenuItemSample_Click);
            // 
            // ToolStripMenuItemCellCoverage
            // 
            this.ToolStripMenuItemCellCoverage.Name = "ToolStripMenuItemCellCoverage";
            this.ToolStripMenuItemCellCoverage.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemCellCoverage.Text = "查看小区覆盖";
            this.ToolStripMenuItemCellCoverage.Click += new System.EventHandler(this.ToolStripMenuItemCellCoverage_Click);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel2;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.Panel2;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.treeList);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl2);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(984, 441);
            this.splitContainerControl1.SplitterPosition = 222;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.lbxLegend);
            this.groupControl2.Controls.Add(this.label2);
            this.groupControl2.Controls.Add(this.label1);
            this.groupControl2.Controls.Add(this.cbxRank);
            this.groupControl2.Controls.Add(this.cbxCol);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(222, 441);
            this.groupControl2.TabIndex = 0;
            this.groupControl2.Text = "图例";
            // 
            // lbxLegend
            // 
            this.lbxLegend.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.lbxLegend.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.lbxLegend.FormattingEnabled = true;
            this.lbxLegend.IntegralHeight = false;
            this.lbxLegend.ItemHeight = 18;
            this.lbxLegend.Location = new System.Drawing.Point(5, 90);
            this.lbxLegend.Name = "lbxLegend";
            this.lbxLegend.SelectionMode = System.Windows.Forms.SelectionMode.None;
            this.lbxLegend.Size = new System.Drawing.Size(214, 346);
            this.lbxLegend.TabIndex = 65;
            this.lbxLegend.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.lbxGis_DrawItem);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(12, 64);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(67, 14);
            this.label2.TabIndex = 1;
            this.label2.Text = "渲染级别：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(5, 35);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(74, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "GIS渲染列：";
            // 
            // cbxRank
            // 
            this.cbxRank.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxRank.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxRank.FormattingEnabled = true;
            this.cbxRank.Location = new System.Drawing.Point(85, 60);
            this.cbxRank.Name = "cbxRank";
            this.cbxRank.Size = new System.Drawing.Size(125, 22);
            this.cbxRank.TabIndex = 0;
            // 
            // cbxCol
            // 
            this.cbxCol.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxCol.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxCol.FormattingEnabled = true;
            this.cbxCol.Location = new System.Drawing.Point(85, 32);
            this.cbxCol.Name = "cbxCol";
            this.cbxCol.Size = new System.Drawing.Size(125, 22);
            this.cbxCol.TabIndex = 0;
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel2;
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.splitContainerControl1);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.groupControl1);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(984, 662);
            this.splitContainerControl2.SplitterPosition = 441;
            this.splitContainerControl2.TabIndex = 0;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.gridControlFileInfo);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(984, 215);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "当前节点测试文件";
            // 
            // gridControlFileInfo
            // 
            this.gridControlFileInfo.ContextMenuStrip = this.ctxMenuFile;
            this.gridControlFileInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlFileInfo.Location = new System.Drawing.Point(2, 23);
            this.gridControlFileInfo.LookAndFeel.SkinName = "Office 2007 Blue";
            this.gridControlFileInfo.MainView = this.gridViewFile;
            this.gridControlFileInfo.Name = "gridControlFileInfo";
            this.gridControlFileInfo.Size = new System.Drawing.Size(980, 190);
            this.gridControlFileInfo.TabIndex = 4;
            this.gridControlFileInfo.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewFile});
            // 
            // ctxMenuFile
            // 
            this.ctxMenuFile.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miRelayFiles});
            this.ctxMenuFile.Name = "ctxMenuFile";
            this.ctxMenuFile.Size = new System.Drawing.Size(125, 26);
            // 
            // miRelayFiles
            // 
            this.miRelayFiles.Name = "miRelayFiles";
            this.miRelayFiles.Size = new System.Drawing.Size(124, 22);
            this.miRelayFiles.Text = "回放文件";
            this.miRelayFiles.Click += new System.EventHandler(this.miRelayFiles_Click);
            // 
            // gridViewFile
            // 
            this.gridViewFile.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.gridViewFile.Appearance.FocusedCell.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.gridViewFile.Appearance.FocusedCell.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridViewFile.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gridViewFile.Appearance.FocusedCell.Options.UseBorderColor = true;
            this.gridViewFile.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewFile.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewFile.Appearance.FocusedRow.BorderColor = System.Drawing.Color.Green;
            this.gridViewFile.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gridViewFile.Appearance.FocusedRow.Options.UseBorderColor = true;
            this.gridViewFile.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewFile.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewFile.Appearance.SelectedRow.BorderColor = System.Drawing.Color.Green;
            this.gridViewFile.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gridViewFile.Appearance.SelectedRow.Options.UseBorderColor = true;
            this.gridViewFile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn19,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn17,
            this.gridColumnStatus,
            this.gridColumn18,
            this.gridColumnDesc});
            this.gridViewFile.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridViewFile.GridControl = this.gridControlFileInfo;
            this.gridViewFile.IndicatorWidth = 20;
            this.gridViewFile.Name = "gridViewFile";
            this.gridViewFile.OptionsBehavior.Editable = false;
            this.gridViewFile.OptionsNavigation.AutoFocusNewRow = true;
            this.gridViewFile.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewFile.OptionsSelection.MultiSelect = true;
            this.gridViewFile.OptionsView.ColumnAutoWidth = false;
            this.gridViewFile.OptionsView.ShowGroupPanel = false;
            this.gridViewFile.MouseMove += new System.Windows.Forms.MouseEventHandler(this.gridViewFile_MouseMove);
            this.gridViewFile.MouseUp += new System.Windows.Forms.MouseEventHandler(this.gridViewFile_MouseUp);
            this.gridViewFile.MouseDown += new System.Windows.Forms.MouseEventHandler(this.gridViewFile_MouseDown);
            this.gridViewFile.DoubleClick += new System.EventHandler(this.gridViewFile_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "序号";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.SortMode = DevExpress.XtraGrid.ColumnSortMode.DisplayText;
            this.gridColumn1.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "地市";
            this.gridColumn2.FieldName = "DistrictName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Width = 55;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "文件名";
            this.gridColumn3.FieldName = "Name";
            this.gridColumn3.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 0;
            this.gridColumn3.Width = 294;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "开始时间";
            this.gridColumn4.FieldName = "BeginTimeString";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 1;
            this.gridColumn4.Width = 131;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "结束时间";
            this.gridColumn5.FieldName = "EndTimeString";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 2;
            this.gridColumn5.Width = 131;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "测试时长";
            this.gridColumn6.FieldName = "Duration";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 3;
            this.gridColumn6.Width = 110;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "测试里程";
            this.gridColumn19.FieldName = "Distance";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 4;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "文件类型";
            this.gridColumn7.FieldName = "FileTypeDescription";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 5;
            this.gridColumn7.Width = 141;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "项目";
            this.gridColumn8.FieldName = "ProjectDescription";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 6;
            this.gridColumn8.Width = 126;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "业务类型";
            this.gridColumn9.FieldName = "ServiceTypeDescription";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 7;
            this.gridColumn9.Width = 73;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "文件大小";
            this.gridColumn10.FieldName = "Size";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 8;
            this.gridColumn10.Width = 71;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "上传单位";
            this.gridColumn11.FieldName = "CompanyName";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 9;
            this.gridColumn11.Width = 63;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "运营商";
            this.gridColumn12.FieldName = "CarrierTypeDescription";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 10;
            this.gridColumn12.Width = 60;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "区域类型";
            this.gridColumn13.FieldName = "AreaTypeString";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 11;
            this.gridColumn13.Width = 60;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "区域";
            this.gridColumn14.FieldName = "AreaString";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 12;
            this.gridColumn14.Width = 60;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "导入时间";
            this.gridColumn15.FieldName = "ImportTimeString";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 13;
            this.gridColumn15.Width = 131;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "设备类型";
            this.gridColumn17.FieldName = "DeviceName";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 17;
            // 
            // gridColumnStatus
            // 
            this.gridColumnStatus.Caption = "状态值";
            this.gridColumnStatus.FieldName = "StatStatusStr";
            this.gridColumnStatus.Name = "gridColumnStatus";
            this.gridColumnStatus.Visible = true;
            this.gridColumnStatus.VisibleIndex = 14;
            this.gridColumnStatus.Width = 60;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "事件处理状态";
            this.gridColumn18.FieldName = "EventStatusDesc";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 15;
            // 
            // gridColumnDesc
            // 
            this.gridColumnDesc.Caption = "文件描述";
            this.gridColumnDesc.FieldName = "StrDesc";
            this.gridColumnDesc.Name = "gridColumnDesc";
            this.gridColumnDesc.Visible = true;
            this.gridColumnDesc.VisibleIndex = 16;
            this.gridColumnDesc.Width = 140;
            // 
            // AreaKPIReportForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(984, 662);
            this.Controls.Add(this.splitContainerControl2);
            this.Name = "AreaKPIReportForm";
            this.Text = "档案报表";
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlFileInfo)).EndInit();
            this.ctxMenuFile.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewFile)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTreeList.TreeList treeList;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraGrid.GridControl gridControlFileInfo;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewFile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnStatus;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDesc;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ComboBox cbxCol;
        private System.Windows.Forms.ListBox lbxLegend;
        private System.Windows.Forms.ContextMenuStrip ctxMenuFile;
        private System.Windows.Forms.ToolStripMenuItem miRelayFiles;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cbxRank;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemGrid;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemSample;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemCellCoverage;
    }
}