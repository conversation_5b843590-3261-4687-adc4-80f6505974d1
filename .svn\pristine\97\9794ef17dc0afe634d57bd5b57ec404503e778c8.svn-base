﻿namespace MasterCom.RAMS.GeneralFuncDef
{
    partial class GeneralFuncSettingForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(GeneralFuncSettingForm));
            this.panelGraph = new System.Windows.Forms.Panel();
            this.cbxRoutineSet = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnApply = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.tbxName = new System.Windows.Forms.TextBox();
            this.cbxDifferentTime = new System.Windows.Forms.CheckBox();
            this.btnSaveConfig = new System.Windows.Forms.Button();
            this.ctxGraphPopMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miModify = new System.Windows.Forms.ToolStripMenuItem();
            this.miAppend = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripSeparator();
            this.miDelete = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox1.SuspendLayout();
            this.ctxGraphPopMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // panelGraph
            // 
            this.panelGraph.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panelGraph.BackColor = System.Drawing.Color.White;
            this.panelGraph.Location = new System.Drawing.Point(8, 32);
            this.panelGraph.Name = "panelGraph";
            this.panelGraph.Size = new System.Drawing.Size(843, 302);
            this.panelGraph.TabIndex = 0;
            this.panelGraph.Paint += new System.Windows.Forms.PaintEventHandler(this.panelGraph_Paint);
            this.panelGraph.MouseClick += new System.Windows.Forms.MouseEventHandler(this.panelGraph_MouseClick);
            this.panelGraph.MouseDown += new System.Windows.Forms.MouseEventHandler(this.panelGraph_MouseDown);
            // 
            // cbxRoutineSet
            // 
            this.cbxRoutineSet.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxRoutineSet.FormattingEnabled = true;
            this.cbxRoutineSet.Location = new System.Drawing.Point(83, 6);
            this.cbxRoutineSet.Name = "cbxRoutineSet";
            this.cbxRoutineSet.Size = new System.Drawing.Size(200, 20);
            this.cbxRoutineSet.TabIndex = 1;
            this.cbxRoutineSet.SelectedIndexChanged += new System.EventHandler(this.cbxRoutineSet_SelectedIndexChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 10);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "分析流程：";
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.btnApply);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.tbxName);
            this.groupBox1.Controls.Add(this.cbxDifferentTime);
            this.groupBox1.Location = new System.Drawing.Point(8, 340);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(843, 48);
            this.groupBox1.TabIndex = 3;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "流程基本属性设置";
            // 
            // btnApply
            // 
            this.btnApply.Location = new System.Drawing.Point(762, 16);
            this.btnApply.Name = "btnApply";
            this.btnApply.Size = new System.Drawing.Size(75, 23);
            this.btnApply.TabIndex = 3;
            this.btnApply.Text = "应用";
            this.btnApply.UseVisualStyleBackColor = true;
            this.btnApply.Click += new System.EventHandler(this.btnApply_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(28, 22);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(29, 12);
            this.label2.TabIndex = 2;
            this.label2.Text = "名称";
            // 
            // tbxName
            // 
            this.tbxName.Location = new System.Drawing.Point(63, 18);
            this.tbxName.Name = "tbxName";
            this.tbxName.Size = new System.Drawing.Size(222, 21);
            this.tbxName.TabIndex = 1;
            // 
            // cbxDifferentTime
            // 
            this.cbxDifferentTime.AutoSize = true;
            this.cbxDifferentTime.Location = new System.Drawing.Point(347, 23);
            this.cbxDifferentTime.Name = "cbxDifferentTime";
            this.cbxDifferentTime.Size = new System.Drawing.Size(108, 16);
            this.cbxDifferentTime.TabIndex = 0;
            this.cbxDifferentTime.Text = "使用两时段对比";
            this.cbxDifferentTime.UseVisualStyleBackColor = true;
            this.cbxDifferentTime.CheckedChanged += new System.EventHandler(this.cbxDifferentTime_CheckedChanged);
            // 
            // btnSaveConfig
            // 
            this.btnSaveConfig.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSaveConfig.Location = new System.Drawing.Point(772, 4);
            this.btnSaveConfig.Name = "btnSaveConfig";
            this.btnSaveConfig.Size = new System.Drawing.Size(75, 23);
            this.btnSaveConfig.TabIndex = 4;
            this.btnSaveConfig.Text = "保存设置";
            this.btnSaveConfig.UseVisualStyleBackColor = true;
            this.btnSaveConfig.Click += new System.EventHandler(this.btnSaveConfig_Click);
            // 
            // ctxGraphPopMenu
            // 
            this.ctxGraphPopMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miModify,
            this.miAppend,
            this.toolStripMenuItem2,
            this.miDelete});
            this.ctxGraphPopMenu.Name = "ctxGraphPopMenu";
            this.ctxGraphPopMenu.Size = new System.Drawing.Size(158, 76);
            // 
            // miModify
            // 
            this.miModify.Name = "miModify";
            this.miModify.Size = new System.Drawing.Size(157, 22);
            this.miModify.Text = "修改流程节点...";
            this.miModify.Click += new System.EventHandler(this.miModify_Click);
            // 
            // miAppend
            // 
            this.miAppend.Name = "miAppend";
            this.miAppend.Size = new System.Drawing.Size(157, 22);
            this.miAppend.Text = "追加流程节点...";
            this.miAppend.Click += new System.EventHandler(this.miAppend_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(154, 6);
            // 
            // miDelete
            // 
            this.miDelete.Name = "miDelete";
            this.miDelete.Size = new System.Drawing.Size(157, 22);
            this.miDelete.Text = "删除流程节点...";
            this.miDelete.Click += new System.EventHandler(this.miDelete_Click);
            // 
            // GeneralFuncSettingForm
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("GeneralFuncSettingForm.Appearance.Image")));
            this.Appearance.Options.UseBackColor = true;
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(859, 394);
            this.Controls.Add(this.btnSaveConfig);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.cbxRoutineSet);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.panelGraph);
            this.Name = "GeneralFuncSettingForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "分析流程定制";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ctxGraphPopMenu.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Panel panelGraph;
        private System.Windows.Forms.ComboBox cbxRoutineSet;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox cbxDifferentTime;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox tbxName;
        private System.Windows.Forms.Button btnApply;
        private System.Windows.Forms.Button btnSaveConfig;
        private System.Windows.Forms.ContextMenuStrip ctxGraphPopMenu;
        private System.Windows.Forms.ToolStripMenuItem miModify;
        private System.Windows.Forms.ToolStripMenuItem miAppend;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem miDelete;
    }
}