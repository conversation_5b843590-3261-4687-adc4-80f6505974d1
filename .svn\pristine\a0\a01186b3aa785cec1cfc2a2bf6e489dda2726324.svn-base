﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public class WeakSINRPoint
    {
        private ICell mainCell = null;
        public ICell Cell
        {
            get
            {
                if (mainCell == null)
                {
                    if (tp is ScanTestPoint_NBIOT)
                    {
                        mainCell = tp.GetCell_LTEScan(0);
                    }
                    else
                    {
                        mainCell = tp.GetMainLTECell_TdOrFdd();
                    }
                    if (mainCell == null)
                    {
                        mainCell = tp.GetMainCell();
                    }
                }
                return mainCell;
            }
        }
        public string CellName
        {
            get { return Cell.Name; }
        }
        public int? TAC
        {
            get
            {
                if (Cell is UnknowCell)
                {
                    UnknowCell cell = Cell as UnknowCell;
                    string tac = cell.Token.Substring(0, cell.Token.IndexOf('_'));
                    if (string.IsNullOrEmpty(tac))
                    {
                        return null;
                    }
                    return int.Parse(tac);
                }
                else
                {
                    return ((LTECell)Cell).TAC;
                }
            }
        }
        public int? ECI
        {
            get
            {
                if (Cell is UnknowCell)
                {
                    UnknowCell cell = Cell as UnknowCell;
                    int idx = cell.Token.IndexOf("_");
                    if (idx < cell.Token.Length - 1)
                    {
                        return int.Parse(cell.Token.Substring(idx + 1));
                    }
                    return null;
                }
                else
                {
                    return ((LTECell)Cell).ECI;
                }
            }
        }
        public int? CellID
        {
            get { return (Cell is UnknowCell) ? null : (int?)(Cell as LTECell).SCellID; }
        }
        public double PntLng
        {
            get { return tp.Longitude; }
        }
        public double PntLat
        {
            get { return tp.Latitude; }
        }
        public string Distance
        {
            get
            {
                if (Cell is UnknowCell)
                {
                    return "-";
                }
                return tp.Distance2(this.Cell.Longitude, this.Cell.Latitude).ToString();
            }
        }
        protected TestPoint tp = null;
        public TestPoint TestPoint
        {
            get { return tp; }
        }
        private readonly ReasonBase reason;
        public ReasonBase Reason
        {
            get { return reason; }
        }
        public string ReasonName
        {
            get { return reason.Name; }
        }
        private readonly FileInfo fi = null;
        public string FileName
        {
            get { return fi.Name; }
        }
        public float SINR
        {
            get
            {
                if (GetSINR(tp) == null)
                {
                    return -1000000;
                }
                return float.Parse(GetSINR(tp).ToString());
            }
        }
        public float RSRP
        {
            get
            {
                if (GetRelev(tp) == null)
                {
                    return -1000000;
                }
                return float.Parse(GetRelev(tp).ToString());
            }
        }
        public WeakSINRPoint(FileInfo fi, TestPoint tp, ReasonBase reason)
        {
            this.fi = fi;
            this.tp = tp;
            this.reason = reason;
        }

        protected virtual float? GetRelev(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }
        protected virtual float? GetSINR(TestPoint tp)
        {
            return (float?)tp["lte_SINR"];
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            if (Cell != null)
            {
                if (Cell is LTECell)
                {
                    bgResult.CellType = BackgroundCellType.LTE;
                }
                else if (Cell is Cell)
                {
                    bgResult.CellType = BackgroundCellType.GSM;
                }
                else if (Cell is TDCell)
                {
                    bgResult.CellType = BackgroundCellType.TD;
                }
            }
            bgResult.ISTime = tp.Time;
            bgResult.IETime = tp.Time;
            bgResult.FileID = tp.FileID;
            bgResult.FileName = FileName;
            bgResult.LAC = TAC == null ? 0 : (int)TAC;
            bgResult.CI = ECI == null ? 0 : (int)ECI;
            bgResult.RxLevMean = RSRP;
            bgResult.RxQualMean = SINR;
            bgResult.LongitudeMid = PntLng;
            bgResult.LatitudeMid = PntLat;
            bgResult.StrDesc = ReasonName;
            bgResult.CellIDDesc = CellID == null ? "" : CellID.ToString();

            bgResult.AddImageValue(Distance);
            bgResult.AddImageValue(CellName);
            return bgResult;
        }
    }

    public class WeakSINRPoint_FDD : WeakSINRPoint
    {
        protected override float? GetRelev(TestPoint tp)
        {
            return (float?)tp["lte_fdd_RSRP"];
        }
        protected override float? GetSINR(TestPoint tp)
        {
            return (float?)tp["lte_fdd_SINR"];
        }
        public WeakSINRPoint_FDD(FileInfo fi, TestPoint tp, ReasonBase reason)
            : base(fi, tp, reason)
        {

        }
    }

    public class WeakSINRPoint_Scan: WeakSINRPoint
    {
        protected override float? GetRelev(TestPoint tp)
        {
            return (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP"];
        }
        protected override float? GetSINR(TestPoint tp)
        {
            return (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR"];
        }
        public WeakSINRPoint_Scan(FileInfo fi, TestPoint tp, ReasonBase reason)
            : base(fi, tp, reason)
        {

        }
    }
}
