﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS
{
    public partial class CPModeTimeProjSettingDlg : BaseDialog
    {
        private MapFormItemSelection itemSelection;
        protected ItemSelectionPanel projPanelHost;
        protected ItemSelectionPanel projPanelGuest;

        //xml中的模板数据
        private CompareMode compareConfig;
        //保存上次查询条件数据
        public CPModeDlgLsatCondition CurCPModeCondition { get; set; }

        public CPModeTimeProjSettingDlg(MainModel mainModel, MapFormItemSelection itemSelection,CPModeDlgLsatCondition curCPModeCondition)
        {
            this.mainModel = mainModel;
            this.itemSelection = itemSelection;
            CurCPModeCondition = new CPModeDlgLsatCondition();
            InitializeComponent();
            initProjs();

            compareConfig = new CompareMode(Application.StartupPath + @"\config\comparemode.xml");
            compareConfig.loadConfig();
            freshCbxCompareMode();
            fillTimeAndProjs(curCPModeCondition);
        }

        /// <summary>
        /// 加载数据来源选择panel
        /// </summary>
        private void initProjs()
        {
            listViewProjectHost.Items.Clear();
            listViewProjectGuest.Items.Clear();
            if (mainModel.CategoryManager["Project"] != null)
            {
                projPanelHost = new ItemSelectionPanel(toolStripDropDownProjectHost, listViewProjectHost, lbProjCountHost, itemSelection, "Project", true);
                projPanelGuest = new ItemSelectionPanel(toolStripDropDownProjectGuest, listViewProjectGuest, lbProjCountGuest, itemSelection, "Project", true);
                toolStripDropDownProjectHost.Items.Clear();
                toolStripDropDownProjectGuest.Items.Clear();
                projPanelHost.FreshItems();
                projPanelGuest.FreshItems();
                toolStripDropDownProjectHost.Items.Add(new ToolStripControlHost(projPanelHost));
                toolStripDropDownProjectGuest.Items.Add(new ToolStripControlHost(projPanelGuest));
            }
        }

        /// <summary>
        /// 加载Dlg界面数据
        /// </summary>
        /// <param name="curCPModeCondition">上次查询条件数据</param>
        private void fillTimeAndProjs(CPModeDlgLsatCondition curCPModeCondition)
        {
            if (curCPModeCondition != null && curCPModeCondition.CurConditionHost != null && curCPModeCondition.CurConditionGuest != null)
            {
                //加载上次查询条件
                QueryCondition conditionHost = curCPModeCondition.CurConditionHost;
                QueryCondition conditionGuest = curCPModeCondition.CurConditionGuest;

                dateTimePickerBeginTimeHost.Value = conditionHost.Periods[0].BeginTime.Date;
                dateTimePickerEndTimeHost.Value = conditionHost.Periods[0].EndTime.Date;
                dateTimePickerBeginTimeGuest.Value = conditionGuest.Periods[0].BeginTime.Date;
                dateTimePickerEndTimeGuest.Value = conditionGuest.Periods[0].EndTime.Date;
                listViewProjectHost.Items.Clear();
                listViewProjectGuest.Items.Clear();
                CategoryEnum projectCate = (CategoryEnum)CategoryManager.GetInstance()["Project"];
                foreach (int projID in conditionHost.Projects)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = projectCate[projID].Name;
                    lvi.Tag = projectCate[projID].ID;
                    listViewProjectHost.Items.Add(lvi);
                }
                foreach (int projID in conditionGuest.Projects)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = projectCate[projID].Name;
                    lvi.Tag = projectCate[projID].ID;
                    listViewProjectGuest.Items.Add(lvi);
                }

                txtHostFileName.Text = conditionHost.FileName;
                txtGuestFileName.Text = conditionGuest.FileName;

                //模板选择
                if (curCPModeCondition.CurCompareTemplate != null)
                {
                    cbxCompareMode.Text = curCPModeCondition.CurCompareTemplate.name;
                }

                //是否查询道路名
                chkRoadName.Checked = curCPModeCondition.IsChkRoad;

                //选择列表赋值
                List<int> idSetHost = new List<int>();
                foreach (ListViewItem item in this.listViewProjectHost.Items)
                {
                    idSetHost.Add((int)item.Tag);
                }
                projPanelHost.UpdateNodeState(idSetHost);
                lbProjCountHost.Text = "[" + listViewProjectHost.Items.Count + "]";

                List<int> idSetGuest = new List<int>();
                foreach (ListViewItem item in this.listViewProjectGuest.Items)
                {
                    idSetGuest.Add((int)item.Tag);
                }
                projPanelGuest.UpdateNodeState(idSetGuest);
                lbProjCountGuest.Text = "[" + listViewProjectGuest.Items.Count + "]";
            }
            else
            {
                dateTimePickerBeginTimeHost.Value = System.DateTime.Now.Date;
                dateTimePickerEndTimeHost.Value = System.DateTime.Now.Date + new TimeSpan(1, 0, 0, 0);
                dateTimePickerBeginTimeGuest.Value = System.DateTime.Now.Date;
                dateTimePickerEndTimeGuest.Value = System.DateTime.Now.Date + new TimeSpan(1, 0, 0, 0);
            }
        }
      
        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            if (check())
            {
                fillCondition();
                this.DialogResult = DialogResult.OK;
            }
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void buttonProjHost_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjHost.Width, buttonProjHost.Height);
            toolStripDropDownProjectHost.Show(buttonProjHost, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonProjGuest_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjGuest.Width, buttonProjGuest.Height);
            toolStripDropDownProjectGuest.Show(buttonProjGuest, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private bool check()
        {
            if (dateTimePickerBeginTimeHost.Value > dateTimePickerEndTimeHost.Value)
            {
                MessageBox.Show("主队初始时间大于结束时间", "提示");
                return false;
            }
            else if (dateTimePickerBeginTimeGuest.Value > dateTimePickerEndTimeGuest.Value)
            {
                MessageBox.Show("客队初始时间大于结束时间", "提示");
                return false;
            }
            else if (listViewProjectHost.Items.Count <= 0)
            {
                MessageBox.Show("主队项目来源不能为空", "提示");
                return false;
            }
            else if (listViewProjectGuest.Items.Count <= 0)
            {
                MessageBox.Show("客队项目来源不能为空", "提示");
                return false;
            }

            if (cbxCompareMode.SelectedItem == null)
            {
                MessageBox.Show("尚未选择竞对模式", "提示");
                return false;
            }
            CompareParam curCompareTemplate = cbxCompareMode.SelectedItem as CompareParam;
            if (curCompareTemplate.AlgorithmCfg.colorItemList.Count <= 0 &&
                curCompareTemplate.AlgorithmCfg.bothStandardList.Count <= 0)
            {
                MessageBox.Show("此竞对模式尚未设置竞对算法,请设置", "提示");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 填充界面数据到public的CurCPModeCondition以供结果显示界面作为查询条件
        /// </summary>
        private void fillCondition()
        {
            CurCPModeCondition.CurConditionHost.Periods.Clear();
            CurCPModeCondition.CurConditionHost.Periods.Add(new TimePeriod(dateTimePickerBeginTimeHost.Value.Date, dateTimePickerEndTimeHost.Value.Date.AddDays(1).AddMilliseconds(-1)));
            CurCPModeCondition.CurConditionHost.Projects.Clear();
            foreach (ListViewItem item in listViewProjectHost.Items)
            {
                CurCPModeCondition.CurConditionHost.Projects.Add((byte)(int)item.Tag);
            }
            if (txtHostFileName.Text.Trim().Length > 0)
            {
                int orNum = 1;
                CurCPModeCondition.CurConditionHost.FileName = QueryCondition.MakeFileFilterString(txtHostFileName.Text, ref orNum);
                CurCPModeCondition.CurConditionHost.FileNameOrNum = orNum;
            }

            CurCPModeCondition.CurConditionGuest.Periods.Clear();
            CurCPModeCondition.CurConditionGuest.Periods.Add(new TimePeriod(dateTimePickerBeginTimeGuest.Value.Date, dateTimePickerEndTimeGuest.Value.Date.AddDays(1).AddMilliseconds(-1)));
            CurCPModeCondition.CurConditionGuest.Projects.Clear();
            foreach (ListViewItem item in listViewProjectGuest.Items)
            {
                CurCPModeCondition.CurConditionGuest.Projects.Add((byte)(int)item.Tag);
            }
            if (txtGuestFileName.Text.Trim().Length > 0)
            {
                int orNum = 1;
                CurCPModeCondition.CurConditionGuest.FileName = QueryCondition.MakeFileFilterString(txtGuestFileName.Text, ref orNum);
                CurCPModeCondition.CurConditionGuest.FileNameOrNum = orNum;
            }

            CurCPModeCondition.IsChkRoad = chkRoadName.Checked;
            CurCPModeCondition.CurCompareTemplate = cbxCompareMode.SelectedItem as CompareParam;
        }

        private void freshCbxCompareMode()
        {
            cbxCompareMode.Items.Clear();
            List<CompareParam> compareParamList = compareConfig.CompareConfigList;
            foreach (CompareParam param in compareParamList)
            {
                cbxCompareMode.Items.Add(param);
            }
            if (cbxCompareMode.Items.Count > 0)
            {
                cbxCompareMode.SelectedIndex = 0;
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            CompareMode compareConfigTemp = new CompareMode(compareConfig.Path);
            compareConfigTemp.loadConfig();

            CPModeEditForm cpModeEditForm = new CPModeEditForm(itemSelection);
            cpModeEditForm.FillData(compareConfigTemp);
            if (DialogResult.OK == cpModeEditForm.ShowDialog())
            {
                compareConfig = cpModeEditForm.compareConfig;
                freshCbxCompareMode();
            }
        }
    }

    /// <summary>
    /// 保存上次查询条件
    /// </summary>
    public class CPModeDlgLsatCondition
    {
        public QueryCondition CurConditionHost { get; set; } = new QueryCondition();
        public QueryCondition CurConditionGuest { get; set; } = new QueryCondition();
        public CompareParam CurCompareTemplate { get; set; } = new CompareParam();
        public bool IsChkRoad { get; set; } = false;
    }
    
}
