﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.BackgroundFunc;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCompareWeakCoverRoad_LTE : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static ZTCompareWeakCoverRoad_LTE instance = null;
        public static ZTCompareWeakCoverRoad_LTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCompareWeakCoverRoad_LTE();
                    }
                }
            }
            return instance;
        }
        protected WeakCoverCondition_TD weakCoverCondition = null;
        protected ZTCompareWeakCoverRoad_LTE()
            : base(MainModel.GetInstance())
        {
        }

        public override string Description
        {
            get
            {
                return "选择2个时间段分析文件，做弱覆盖的对比分析。";
            }
        }

        public override string Name
        {
            get { return "弱覆盖路段对比_LTE"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13034, this.Name);
        }

        /// <summary>
        /// 查询之前初始化各个变量
        /// </summary>
        protected override void getReadyBeforeQuery()
        {
            p1Files = new List<FileInfo>();
            p2Files = new List<FileInfo>();
            weakCoverOfPeriod1 = new List<ZTWeakCoverGrid>();
            weakCoverOfPeriod2 = new List<ZTWeakCoverGrid>();
            repeatGrids = new List<ZTWeakCoverGrid>();
            newGrids = new List<ZTWeakCoverGrid>();
            prepareGridInfo(condition.Geometorys.RegionBounds);
        }

        List<FileInfo> p1Files = null;//时间段1对应的文件
        List<FileInfo> p2Files = null;//时间段2对应的文件
        /// <summary>
        /// 已经获取了2个时间段的所有文件信息，这里要先分析时间段1的文件，再分析时间段2的文件，从而对比计算
        /// </summary>
        protected override void analyseFiles()
        {
            try
            {
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }
                    if (weakCoverCondition.Period1.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.BeginTime * 1000L)) || weakCoverCondition.Period1.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.EndTime * 1000L)))
                    {
                        p1Files.Add(fileInfo);//时间段1的文件
                    }
                    else if (weakCoverCondition.Period2.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.BeginTime * 1000L)) || weakCoverCondition.Period2.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.EndTime * 1000L)))
                    {
                        p2Files.Add(fileInfo);//时间段2的文件
                    }
                }
                clearDataBeforeAnalyseFiles();
                if (MainModel.IsBackground)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取待分析文件" + MainModel.FileInfos.Count + "个...");
                }
                replayFiles(p1Files);//先分析时间段1的文件
                replayFiles(p2Files);
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        /// <summary>
        /// 逐个回放对应的时间段文件
        /// </summary>
        /// <param name="files2replay">要回放的文件</param>
        protected void replayFiles(List<FileInfo> files2replay)
        {
            int iloop = 0;
            foreach (FileInfo fileInfo in files2replay)
            {
                if (MainModel.IsBackground)
                {
                    if (MainModel.BackgroundStopRequest)
                    {
                        break;
                    }
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在分析" + FuncType.ToString() +
                        SubFuncType.ToString() + "类" + Name + "，当前文件" + (++iloop) + "/" + files2replay.Count +
                        "个...文件名：" + fileInfo.Name);
                }
                else
                {
                    WaitBox.Text = "正在分析文件(" + (++iloop) + "/" + files2replay.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files2replay.Count);
                }
                if (filterFile(fileInfo))
                {
                    continue;
                }

                curAnaFileInfo = fileInfo;
                Condition.FileInfos.Clear();
                Condition.FileInfos.Add(fileInfo);
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                replay();//回放
                if (WaitBox.CancelRequest)
                {
                    break;
                }
            }
        }

        /// <summary>
        /// 文件已回放，开始统计该文件
        /// </summary>
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                if (isFileInPeriod(fileDataManager.FileID, p1Files))//时段1的文件分析
                {
                    doWithFileOfPeriod(true, fileDataManager);
                }
                else if (isFileInPeriod(fileDataManager.FileID, p2Files))//时间段2的文件分析
                {
                    doWithFileOfPeriod(false, fileDataManager);
                }
            }
        }

        /// <summary>
        /// 判断某文件是否为某时间段内的文件
        /// </summary>
        /// <param name="fileId">要判断的文件ID</param>
        /// <param name="filesInPeroid">某时间段内的文件</param>
        /// <returns>在时间段内true;不在false</returns>
        protected bool isFileInPeriod(int fileId, List<FileInfo> filesInPeroid)
        {
            foreach (FileInfo fi in filesInPeroid)
            {
                if (fi.ID == fileId)
                {
                    return true;
                }
            }
            return false;
        }

        protected List<ZTWeakCoverGrid> weakCoverOfPeriod1 = null;
        protected List<ZTWeakCoverGrid> weakCoverOfPeriod2 = null;
        protected void doWithFileOfPeriod(bool isPeriod1File, DTFileDataManager fileMng)
        {
            List<TestPoint> testPointList = fileMng.TestPoints;
            ZTWeakCoverGrid info = null;
            TestPoint prePoint = null;//前一点
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                if (isValidTestPoint(testPoint))
                {
                    info = addZTWeakCoverGrid(isPeriod1File, testPointList, info, prePoint, i, testPoint);
                    prePoint = testPoint;
                }
                else//区域外的采样点
                {
                    saveWeakCoverInfo(isPeriod1File, ref info);
                }
            }
            saveWeakCoverInfo(isPeriod1File, ref info);
        }

        private ZTWeakCoverGrid addZTWeakCoverGrid(bool isPeriod1File, List<TestPoint> testPointList, ZTWeakCoverGrid info, TestPoint prePoint, int i, TestPoint testPoint)
        {
            float? rscp = (float?)testPoint["TD_PCCPCH_RSCP"];
            int? pccpchC2i = (int?)testPoint["TD_PCCPCH_C2I"];
            int? dpchC2i = (int?)testPoint["TD_DPCH_C2I"];
            int maxNCellRscp = int.MinValue;
            if (!weakCoverCondition.IsValidate(rscp, pccpchC2i, dpchC2i))//先作指标的判断，后作距离的判断
            {//不符合设置的弱覆盖条件
                saveWeakCoverInfo(isPeriod1File, ref info);
            }
            else//指标符合弱覆盖，还需要进行距离条件判断
            {
                if (info == null)//弱覆盖开始
                {
                    info = new ZTWeakCoverGrid(curGridSpan, curRegionGridBounds);
                    info.Add((float)rscp, pccpchC2i, dpchC2i, 0, testPoint, maxNCellRscp);
                    if (i == testPointList.Count - 1)//最后一采样点
                    {
                        saveWeakCoverInfo(isPeriod1File, ref info);
                    }
                }
                else
                {//上一点为弱覆盖点
                    double dis = MathFuncs.GetDistance(prePoint.Longitude, prePoint.Latitude, testPoint.Longitude, testPoint.Latitude);
                    if (weakCoverCondition.Match2TestpointsMaxDistance(dis))
                    {//符合两采样点之间的距离门限
                        info.Add((float)rscp, pccpchC2i, dpchC2i, dis, testPoint, maxNCellRscp);
                    }
                    else
                    {//两采样点距离不符合，该点开始新的弱覆盖
                        saveWeakCoverInfo(isPeriod1File, ref info);
                        info = new ZTWeakCoverGrid(curGridSpan, curRegionGridBounds);
                        info.Add((float)rscp, pccpchC2i, dpchC2i, dis, testPoint, maxNCellRscp);
                    }
                }
            }

            return info;
        }

        protected List<ZTWeakCoverGrid> repeatGrids = null;
        protected List<ZTWeakCoverGrid> newGrids = null;
        protected override void doSomethingAfterAnalyseFiles()
        {
            foreach (ZTWeakCoverGrid grid in weakCoverOfPeriod2)
            {
                bool repeat = false;
                foreach (ZTWeakCoverGrid gridBase in weakCoverOfPeriod1)
                {
                    if (gridBase.CanMerge(grid))
                    {//有重叠
                        repeatGrids.Add(grid);
                        grid.SN = repeatGrids.Count;
                        repeat = true;
                        break;
                    }
                }
                if (!repeat)
                {//新的弱覆盖路段
                    newGrids.Add(grid);
                    grid.SN = newGrids.Count;
                }
            }
        }

        private void saveWeakCoverInfo(bool isPeriod1File,ref ZTWeakCoverGrid info)
        {
            if (info == null || !weakCoverCondition.MatchWeakCoverMinDistance(info.Distance))
            {//不符合最小持续距离
                info = null;
                return;
            }
            if (isPeriod1File)
            {
                if (!weakCoverOfPeriod1.Contains(info))
                {
                    info.SN = weakCoverOfPeriod1.Count + 1;
                    info.GetResult(saveTestPoints);
                    weakCoverOfPeriod1.Add(info);   
                }
            }
            else
            {
                if (!weakCoverOfPeriod2.Contains(info))
                {
                    info.SN = weakCoverOfPeriod2.Count + 1;
                    info.GetResult(saveTestPoints);
                    weakCoverOfPeriod2.Add(info);
                }
            }
            info = null;
        }

        private DbRect curRegionGridBounds = null;
        private double curGridSpan = double.NaN;

        private void prepareGridInfo(DbRect queryBounds)
        {
            curGridSpan = weakCoverCondition.RoadGridSpan / 10 * 0.0001;
            curRegionGridBounds = new DbRect();
            curRegionGridBounds.x1 = 0.0000001 * ((int)(queryBounds.x1 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000));
            //避免漏掉最右边栅格
            curRegionGridBounds.x2 = 0.0000001 * ((int)(queryBounds.x2 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000)) + curGridSpan;
            //避免漏掉最上面栅格
            curRegionGridBounds.y2 = 0.0000001 * ((int)(queryBounds.y2 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000)) + curGridSpan;
            curRegionGridBounds.y1 = 0.0000001 * ((int)(queryBounds.y1 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000));
        }

        protected string getGridIdx(TestPoint tp)
        {
            double xDis = tp.Longitude - curRegionGridBounds.x1;
            int cAt = (int)(xDis / curGridSpan);
            double yDis = curRegionGridBounds.y2 - tp.Latitude;
            int rAt = (int)(yDis / curGridSpan);
            return rAt.ToString() + "_" + cAt.ToString();
        }

        protected override void fireShowForm()
        {
            TDWeakCoverRoadCompareForm frm = null;
            frm = MainModel.GetObjectFromBlackboard(typeof(TDWeakCoverRoadCompareForm).FullName) as TDWeakCoverRoadCompareForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new TDWeakCoverRoadCompareForm(MainModel);
                frm.Owner = MainModel.MainForm;
            }
            frm.FillData(weakCoverOfPeriod1, repeatGrids, newGrids);
            frm.Visible = true;
            frm.BringToFront();
            weakCoverOfPeriod1 = new List<ZTWeakCoverGrid>();
            repeatGrids = new List<ZTWeakCoverGrid>();
            newGrids = new List<ZTWeakCoverGrid>();
        }
        protected bool saveTestPoints = true;
        /// <summary>
        /// 弹出条件设置窗口，获取条件
        /// </summary>
        /// <returns></returns>
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                saveTestPoints = false;
                return true;
            }
            TDCopareWeakCoverRoadSettingDlg conditionDlg = new TDCopareWeakCoverRoadSettingDlg(weakCoverCondition);
            if (conditionDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            weakCoverCondition = conditionDlg.GetCondition();
            condition.Periods.Clear();
            condition.Periods.Add(weakCoverCondition.Period1);
            condition.Periods.Add(weakCoverCondition.Period2);
            return true;
        }
    }

    public class WeakCoverRoadGrid_LTE : WeakCoverRoadLTE
    {
        private double gridSpan { get; set; }// = 0.0005;
        public WeakCoverRoadGrid_LTE(double gridSpan)
        {
            this.gridSpan = gridSpan;
        }
        public override void MakeSummary()
        {
            //
        }
    }

}
