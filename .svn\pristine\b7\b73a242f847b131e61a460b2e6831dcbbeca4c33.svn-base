﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEModRoadStater : ModRoadStaterBase
    {
        public LTEModRoadStater(MainModel mm, LTEModRoadCondition cond) : base(mm, cond)
        {
            this.lteCond = cond;
        }

        public override object GetStatResult(object param)
        {
            LTEModInterfereCondition interfereCond = param as LTEModInterfereCondition;
            if (interfereCond == null)
            {
                return null;
            }
            List<LTEModRoadItem> resultList = new List<LTEModRoadItem>();
            object[] args = new object[] { interfereCond, resultList };
            if (mainModel.IsBackground)
            {
                GetStatResultInThread(args);
            }
            else
            {
                WaitBox.Show("正在统计道路信息...", GetStatResultInThread, args);
            }
            return resultList;
        }

        private void GetStatResultInThread(object param)
        {
            try
            {
                object[] args = param as object[];
                LTEModInterfereCondition interfereCond = args[0] as LTEModInterfereCondition;
                List<LTEModRoadItem> resultList = args[1] as List<LTEModRoadItem>;

                int iLoop = 0;
                foreach (ModRoadItemBase roadBase in this.roadList)
                {
                    if (!mainModel.IsBackground)
                    {
                        WaitBox.ProgressPercent = (++iLoop) * 100 / this.roadList.Count;
                    }

                    LTEModRoadItem road = roadBase as LTEModRoadItem;
                    road.FilterInterfere(interfereCond);
                    if (road.InterSampleRate >= interfereCond.InterfereRate)
                    {
                        resultList.Add(road);
                        road.SN = resultList.Count;
                    }
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(500);
                if (!mainModel.IsBackground)
                {
                    WaitBox.Close();
                }
            }
        }

        private LTEModRoadCondition lteCond { get; set; }
        public void ClearRoadList()
        {
            if (roadList != null)
            {
                roadList.Clear();
            }
        }
    }
}
