using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Security.Cryptography;

namespace MasterCom.Util
{
    public static class DES
    {
        private static DESCryptoServiceProvider des;
        private static byte[] iv = Encoding.UTF8.GetBytes("12345678");
        private static byte[] key = Encoding.UTF8.GetBytes("Chris   ");

        public static string Decode(string code)
        {
            if (code == null)
            {
                return null;
            }
            MemoryStream stream = new MemoryStream(Convert.FromBase64String(code));
            CryptoStream stream2 = new CryptoStream(stream, getDES().CreateDecryptor(), CryptoStreamMode.Read);
            StreamReader reader = new StreamReader(stream2);
            string str = reader.ReadToEnd();
            reader.Close();
            stream2.Close();
            stream.Close();
            return str;
        }

        public static string Encode(string str)
        {
            if (str == null)
            {
                return null;
            }
            MemoryStream stream = new MemoryStream();
            CryptoStream stream2 = new CryptoStream(stream, getDES().CreateEncryptor(), CryptoStreamMode.Write);
            StreamWriter writer = new StreamWriter(stream2);
            writer.Write(str);
            writer.Close();
            stream2.Close();
            byte[] inArray = stream.ToArray();
            stream.Close();
            return Convert.ToBase64String(inArray);
        }

        private static DESCryptoServiceProvider getDES()
        {
            if (des == null)
            {
                des = new DESCryptoServiceProvider();
                des.Key = key;
                des.IV = iv;
            }
            return des;
        }
    }
}
