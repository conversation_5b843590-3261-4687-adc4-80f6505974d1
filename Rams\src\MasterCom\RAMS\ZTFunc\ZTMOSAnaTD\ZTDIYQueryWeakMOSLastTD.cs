﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYQueryWeakMOSLastTD : ZTDIYQueryWeakMOSLast
    {
        public ZTDIYQueryWeakMOSLastTD(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "MOS差分析"; }
        }

        public override string IconName
        {
            get { return "Images/event/WeakQual.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13045, this.Name);
        }

        protected override bool prepareAskWhatEvent()
        {
            List<int> selectedEventIDs = new List<int>();
            selectedEventIDs.Add(225);
            selectedEventIDs.Add(226);
            Condition.EventIDs = selectedEventIDs;
            return true;
        }
    }
}
