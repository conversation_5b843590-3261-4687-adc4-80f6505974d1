﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.Voronoi;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTUltraSite;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using MasterCom.MTGis;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 查询3超小区（超高、超远、超近）站点or小区，暂支持NR
    /// 默认通过弹窗设置3超条件
    /// 当不弹窗设置条件时，将会以传入的条件，分析全区域的站点小区。
    /// </summary>
    public class UltraSiteQuery_NR : BackgroundQueryBase
    {
        readonly NetworkType netWork = NetworkType.NR;
        Dictionary<ICell, List<UltraSiteCell>> cellDic = null;
        public Dictionary<ICell, List<UltraSiteCell>> Result
        {
            get { return cellDic; }
        }

        public UltraSiteCondition nrUltraSiteCondition { get; set; } = new UltraSiteCondition();
        public bool ShowSettingDlg { get; set; } = true;

        protected static readonly object lockObj = new object();
        private static UltraSiteQuery_NR instance = null;
        public static UltraSiteQuery_NR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new UltraSiteQuery_NR();
                    }
                }
            }
            return instance;
        }
        public UltraSiteQuery_NR()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "三超站点分析"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override bool isValidCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (ShowSettingDlg && !showFuncCondSetDlg())
            {
                return false;
            }
            return nrUltraSiteCondition != null;
        }
        protected bool showFuncCondSetDlg()
        {
            SettingDlg dlg = new SettingDlg();
            dlg.SetCondition(this.nrUltraSiteCondition);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            this.nrUltraSiteCondition = dlg.GetCondition();
            return true;
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19038, this.Name);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override void query()
        {
            if (netWork == NetworkType.NR)
            {
                if (!MainModel.IsBackground)
                {
                    if (!MainModel.QueryFromBackground)
                    {
                        queryFront();
                    }
                    else
                    {
                        getBackgroundData();
                    }
                }
                else
                {
                    if (mainModel.BackgroundStarted)
                    {
                        cellDic = new Dictionary<ICell, List<UltraSiteCell>>();
                        reportBackgroundInfo("正在分析三超站点...");
                        judgeSites();
                        reportBackgroundInfo("三超站点分析结束。");
                    }
                }
            }
        }

        private void queryFront()
        {
            cellDic = new Dictionary<ICell, List<UltraSiteCell>>();
            WaitTextBox.Show("正在分析三超站点...", judgeSites);
            if (ShowSettingDlg && cellDic.Count == 0)
            {
                MessageBox.Show("无符合条件的站点！");
            }
            else if (ShowSettingDlg)
            {
                showResultForm(cellDic);
            }
        }

        protected virtual void showResultForm(Dictionary<ICell, List<UltraSiteCell>> cellDic)
        {
            List<UltraHighSite> highCells = new List<UltraHighSite>();
            List<UltraNearSite> nearSites = new List<UltraNearSite>();
            List<UltraFarSite> farCells = new List<UltraFarSite>();
            foreach (List<UltraSiteCell> uSites in cellDic.Values)
            {
                foreach (UltraSiteCell uSite in uSites)
                {
                    if (uSite is UltraHighSite)
                    {
                        highCells.Add(uSite as UltraHighSite);
                    }
                    else if (uSite is UltraNearSite)
                    {
                        nearSites.Add(uSite as UltraNearSite);
                    }
                    else if (uSite is UltraFarSite)
                    {
                        farCells.Add(uSite as UltraFarSite);
                    }
                }
            }
            UltraSiteCellForm frm = MainModel.CreateResultForm(typeof(UltraSiteCellForm)) as UltraSiteCellForm;
            frm.FillData(farCells, highCells, nearSites);
            frm.Visible = true;
            frm.BringToFront();
        }

        private List<UltraFarSite> getCellAvgDistanceByDir(ISite site, List<ISite> otherSites)
        {
            NRBTS bts = site as NRBTS;
            List<UltraFarSite> farCells = new List<UltraFarSite>();
            foreach (NRCell cell in bts.LatestCells)
            {
                List<ISite> twoKMSites = new List<ISite>();
                List<ISite> tenKMSites = new List<ISite>();
                List<ISite> twtyKMSites = new List<ISite>();
                addSites(otherSites, bts, twoKMSites, tenKMSites, twtyKMSites);
                List<SiteDistance> nearestCells = new List<SiteDistance>();
                addInDirSites(nearestCells, cell, twoKMSites);
                if (nearestCells.Count < 3)
                {
                    addInDirSites(nearestCells, cell, tenKMSites);
                }
                if (nearestCells.Count < 3)
                {
                    addInDirSites(nearestCells, cell, twtyKMSites);
                }
                nearestCells.Sort();
                double disTotal = 0;
                List<SiteDistance> nSites = new List<SiteDistance>();
                for (int i = 0; i < 3 && i < nearestCells.Count; i++)
                {
                    nSites.Add(nearestCells[i]);
                    disTotal += nearestCells[i].Distance;
                }
                double avgDis = Math.Round(disTotal / nSites.Count, 2);
                farCells.Add(new UltraFarSite(cell, avgDis, nSites));
            }
            return farCells;
        }

        private void addSites(List<ISite> otherSites, NRBTS bts, List<ISite> twoKMSites, List<ISite> tenKMSites, List<ISite> twtyKMSites)
        {
            foreach (ISite other in otherSites)
            {
                if (other == bts)
                {
                    continue;
                }

                double lngDis = Math.Abs(bts.Longitude - other.Longitude);
                double latDis = Math.Abs(bts.Latitude - other.Latitude);
                if (lngDis > 0.2 || latDis > 0.2)
                {//约20KM
                }
                else if (lngDis > 0.1 || latDis > 0.1)
                {//约10KM
                    twtyKMSites.Add(other);
                }
                else if (lngDis > 0.02 || latDis > 0.02)
                {//约2KM
                    tenKMSites.Add(other);
                }
                else
                {
                    twoKMSites.Add(other);
                }
            }
        }

        private void addInDirSites(List<SiteDistance> sites, ICell cell, List<ISite> siteSet)
        {
            foreach (ISite site in siteSet)
            {
                if (MathFuncs.JudgePoint(cell.Longitude, cell.Latitude,
                        site.Longitude, site.Latitude, (int)cell.Direction
                        , nrUltraSiteCondition.AvgSitesDistanceAngle / 2))
                {//在扇区范围内
                    double distance = MathFuncs.GetDistance(cell.Longitude, cell.Latitude
                    , site.Longitude, site.Latitude);
                    sites.Add(new SiteDistance(site, distance));
                }
            }
        }

        private Dictionary<ISite, SiteDelaunayTri> getSiteDelaunayTri(List<ISite> allSites
            , List<ISite> filterSites)
        {
            WaitBox.Text = "正在构建delaunay三角网...";
            Dictionary<int, List<ISite>> posSiteDic = new Dictionary<int, List<ISite>>();
            List<Vertex> pnts = new List<Vertex>();
            foreach (ISite site in allSites)
            {
                Vertex pnt = new Vertex(site.Longitude, site.Latitude);
                if (!pnts.Contains(pnt))
                {//过滤掉相同经纬度的点
                    pnts.Add(pnt);
                }
                int posKey = site.Longitude.GetHashCode() ^ site.Latitude.GetHashCode();
                List<ISite> samePosSites = null;
                if (!posSiteDic.TryGetValue(posKey, out samePosSites))
                {
                    samePosSites = new List<ISite>();
                    posSiteDic.Add(posKey, samePosSites);
                }
                samePosSites.Add(site);
            }

            CTriangle cTriangle = new CTriangle();
            List<Vertex> cTriangleResult = null;
            try
            {
                cTriangleResult = cTriangle.Triangulate(pnts);
            }
            catch (Exception e)
            {
                if (e is ArgumentException)
                {
                    throw;
                }
                else
                {
                    ErrorInfo += "缺少CGeometry动态库！";
                }
                return new Dictionary<ISite, SiteDelaunayTri>();
            }
            System.Diagnostics.Debug.Assert(cTriangleResult.Count % 3 == 0, "构建delaunay三角网失败");
            Dictionary<ISite, SiteDelaunayTri> delaunayDic = new Dictionary<ISite, SiteDelaunayTri>();
            //每3个点，为一个delaunay三角
            for (int i = 0; i + 2 < cTriangleResult.Count; i++)
            {
                Vertex pt1 = cTriangleResult[i];
                Vertex pt2 = cTriangleResult[++i];
                Vertex pt3 = cTriangleResult[++i];
                List<ISite> site1 = posSiteDic[pt1.GetHashCode()];
                List<ISite> site2 = posSiteDic[pt2.GetHashCode()];
                List<ISite> site3 = posSiteDic[pt3.GetHashCode()];

                addOtherSite(filterSites, delaunayDic, site1, site2, site3);
                addOtherSite(filterSites, delaunayDic, site2, site1, site3);
                addOtherSite(filterSites, delaunayDic, site3, site1, site2);
            }
            return delaunayDic;
        }

        private void addOtherSite(List<ISite> filterSites, Dictionary<ISite, SiteDelaunayTri> delaunayDic,
            List<ISite> siteA, List<ISite> siteB, List<ISite> siteC)
        {
            foreach (ISite site in siteA)
            {
                if (!filterSites.Contains((NRBTS)site))
                {
                    SiteDelaunayTri dTri = null;
                    if (!delaunayDic.TryGetValue(site, out dTri))
                    {
                        dTri = new SiteDelaunayTri(site);
                        delaunayDic.Add(site, dTri);
                    }
                    foreach (ISite item in siteB)
                    {
                        dTri.AddOtherSite(item);
                    }
                    foreach (ISite item in siteC)
                    {
                        dTri.AddOtherSite(item);
                    }
                }
            }
        }

        private void judgeSites()
        {
            List<NRBTS> tempSites = getCurrentNRBTSs();
            List<ISite> allSites = new List<ISite>(tempSites.Count);
            foreach (NRBTS item in tempSites)
            {
                if (item.Type == NRBTSType.Indoor)
                {
                    continue;
                }
                allSites.Add(item);
            }
            try
            {
                if (allSites.Count < 3)
                {
                    ErrorInfo += "室外站数量小于3个，无法计算站间距！";
                    return;
                }

                List<ISite> inRegionSites = new List<ISite>(allSites);
                List<ISite> outRegionSites = new List<ISite>();

                getRegionSites(inRegionSites, outRegionSites);

                Dictionary<ISite, SiteDelaunayTri> delaunayDic = getSiteDelaunayTri(allSites, outRegionSites);
                //先计算站间距，后面超高，超近小区会有站间距信息。
                Dictionary<ICell, UltraFarSite> cellDisDic = new Dictionary<ICell, UltraFarSite>();
                addCellDic(allSites, inRegionSites, delaunayDic, cellDisDic);

                dealRegionSites(inRegionSites, outRegionSites, cellDisDic);
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        private void getRegionSites(List<ISite> inRegionSites, List<ISite> outRegionSites)
        {
            if (ShowSettingDlg && MainModel.SearchGeometrys != null
                && MainModel.SearchGeometrys.IsSelectRegion())
            {
                for (int i = 0; i < inRegionSites.Count; i++)
                {
                    ISite site = inRegionSites[i];
                    if (!MainModel.SearchGeometrys.GeoOp.Contains(site.Longitude, site.Latitude))
                    {
                        outRegionSites.Add(site);
                        inRegionSites.RemoveAt(i);
                        i--;
                    }
                }
            }
        }

        private void addCellDic(List<ISite> allSites, List<ISite> inRegionSites,
            Dictionary<ISite, SiteDelaunayTri> delaunayDic, Dictionary<ICell, UltraFarSite> cellDisDic)
        {
            foreach (ISite bts in inRegionSites)
            {
                string areaName = GISManager.GetInstance().GetGridDesc(bts.Longitude, bts.Latitude);
                SiteDelaunayTri siteDTri = delaunayDic[bts];
                //超远 （方向角站间距）
                List<UltraFarSite> tempFarSites = getCellAvgDistanceByDir(bts, allSites);
                foreach (UltraFarSite farSite in tempFarSites)
                {
                    farSite.AreaName = areaName;
                    farSite.SetDelaunayInfo(siteDTri);
                    cellDisDic[farSite.Cell] = farSite;
                    if (!nrUltraSiteCondition.IsTooFar(farSite.Distance))
                        continue;
                    List<UltraSiteCell> ultraSet = null;
                    if (!cellDic.TryGetValue(farSite.Cell, out ultraSet))
                    {
                        ultraSet = new List<UltraSiteCell>();
                        cellDic.Add(farSite.Cell, ultraSet);
                    }
                    ultraSet.Add(farSite);
                }
            }
        }

        private void dealRegionSites(List<ISite> inRegionSites, List<ISite> outRegionSites, Dictionary<ICell, UltraFarSite> cellDisDic)
        {
            for (int i = 0; i < inRegionSites.Count; i++)
            {
                NRBTS bts = inRegionSites[i] as NRBTS;

                //超高
                foreach (NRCell cell in bts.LatestCells)
                {
                    if (nrUltraSiteCondition.IsTooHighSite(cell))
                    {
                        addUltraHighSite(cellDisDic, cell);
                    }
                }

                //超近
                dealInRegionSites(inRegionSites, cellDisDic, i, bts);
                dealOutRegionSites(outRegionSites, cellDisDic, bts);
            }
        }

        private void dealInRegionSites(List<ISite> inRegionSites, Dictionary<ICell, UltraFarSite> cellDisDic, int i, NRBTS bts)
        {
            for (int j = i + 1; j < inRegionSites.Count; j++)
            {
                NRBTS other = inRegionSites[j] as NRBTS;
                double dis = 0;
                if (nrUltraSiteCondition.IsTooNearSite(bts, other, out dis))
                {
                    addValidBts(cellDisDic, other, bts, dis);
                    addValidBts(cellDisDic, bts, other, dis);
                }
            }
        }

        private void addValidBts(Dictionary<ICell, UltraFarSite> cellDisDic, NRBTS bts, NRBTS judgedBts, double dis)
        {
            foreach (NRCell cell in judgedBts.LatestCells)
            {
                bool diffBand = false;
                foreach (NRCell tmp in bts.LatestCells)
                {
                    if (tmp.BandType != cell.BandType)
                    {
                        diffBand = true;
                        break;
                    }
                }
                if (diffBand && dis <= nrUltraSiteCondition.DiffBandDistanceMin)
                {//异频，且距离很近有可能是共站情况，过滤。
                    continue;
                }

                addUltraNearSite(cellDisDic, bts, dis, cell);
            }
        }

        private void dealOutRegionSites(List<ISite> outRegionSites, Dictionary<ICell, UltraFarSite> cellDisDic, NRBTS bts)
        {
            foreach (ISite other in outRegionSites)
            {
                double dis = 0;
                if (nrUltraSiteCondition.IsTooNearSite(bts, other, out dis))
                {
                    foreach (NRCell cell in bts.LatestCells)
                    {
                        addUltraNearSite(cellDisDic, other, dis, cell);
                    }
                }
            }
        }

        private void addUltraHighSite(Dictionary<ICell, UltraFarSite> cellDisDic, NRCell cell)
        {
            UltraHighSite hSite = new UltraHighSite(cell);
            hSite.DistanceByDelaunay = cellDisDic[cell].DistanceByDelaunay;
            hSite.DistanceByDir = cellDisDic[cell].DistanceByDir;
            hSite.AreaName = cellDisDic[cell].AreaName;
            List<UltraSiteCell> ultraSet = null;
            if (!cellDic.TryGetValue(cell, out ultraSet))
            {
                ultraSet = new List<UltraSiteCell>();
                cellDic.Add(cell, ultraSet);
            }
            ultraSet.Add(hSite);
        }

        private void addUltraNearSite(Dictionary<ICell, UltraFarSite> cellDisDic, ISite site, double dis, NRCell cell)
        {
            UltraNearSite nSite = new UltraNearSite(cell, site, dis);
            nSite.DistanceByDelaunay = cellDisDic[cell].DistanceByDelaunay;
            nSite.DistanceByDir = cellDisDic[cell].DistanceByDir;
            nSite.AreaName = cellDisDic[cell].AreaName;
            List<UltraSiteCell> ultraSet = null;
            if (!cellDic.TryGetValue(cell, out ultraSet))
            {
                ultraSet = new List<UltraSiteCell>();
                cellDic.Add(cell, ultraSet);
            }
            ultraSet.Add(nSite);
        }

        private List<NRBTS> getCurrentNRBTSs()
        {
            List<NRBTS> validBts = CellManager.GetInstance().GetCurrentNRBTSs();
            if (MainModel.SearchGeometrys != null && MainModel.SearchGeometrys.IsSelectRegion())
            {
                DbRect selectRect = MainModel.SearchGeometrys.RegionBounds;
                DbRect validRect = new DbRect(selectRect.x1 - 0.1, selectRect.y1 - 0.1, selectRect.x2 + 0.1, selectRect.y2 + 0.1);

                for (int i = 0; i < validBts.Count; i++)
                {
                    ISite site = validBts[i];
                    if (!validRect.IsPointInThisRect(site.Longitude, site.Latitude))
                    {
                        validBts.RemoveAt(i);
                        i--;
                    }
                }
            }
            return validBts;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.NR业务专题; }
        }
        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.其他; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                ignoreParamKeys.Add("FuncCond");

                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                if (nrUltraSiteCondition != null)
                {
                    param["FuncCond"] = nrUltraSiteCondition.Param;
                }
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("FuncCond"))
                {
                    if (nrUltraSiteCondition == null)
                    {
                        nrUltraSiteCondition = new UltraSiteCondition();
                    }
                    nrUltraSiteCondition.Param = param["FuncCond"] as Dictionary<string, object>;
                }
            }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.None; }
        }
        public override PropertiesControl Properties
        {
            get
            {
                return new CommonSimpleProperties(this, showFuncCondSetDlg);
            }
        }
        protected override void getBackgroundData()
        {
            if (!BackgroundFuncConfigManager.GetInstance().AutoExportResult
                || cellDic == null || cellDic.Count <= 0)
            {
                return;
            }

            List<NPOIRow> farCellNPOIRows = new List<NPOIRow>();
            List<NPOIRow> highCellNPOIRows = new List<NPOIRow>();
            List<NPOIRow> nearSiteNPOIRows = new List<NPOIRow>();
            foreach (List<UltraSiteCell> uSites in cellDic.Values)
            {
                foreach (UltraSiteCell uSite in uSites)
                {
                    NPOIRow row = convertToNPOIRow(uSite);
                    if (uSite is UltraHighSite)
                    {
                        highCellNPOIRows.Add(row);
                    }
                    else if (uSite is UltraNearSite)
                    {
                        nearSiteNPOIRows.Add(row);
                    }
                    else if (uSite is UltraFarSite)
                    {
                        farCellNPOIRows.Add(row);
                    }
                }
            }

            NPOIRow rowTitle = new NPOIRow();
            rowTitle.AddCellValue("类别");
            rowTitle.AddCellValue("所属区域");
            rowTitle.AddCellValue("站点");
            rowTitle.AddCellValue("小区");
            rowTitle.AddCellValue("TAC");
            rowTitle.AddCellValue("NCI");
            rowTitle.AddCellValue("Delaunay站间距");
            rowTitle.AddCellValue("站间距(方向角)");
            rowTitle.AddCellValue("问题信息");
            rowTitle.AddCellValue("经度");
            rowTitle.AddCellValue("纬度");
            rowTitle.AddCellValue("相关站点");
            rowTitle.AddCellValue("相关小区");

            List<NPOIRow> resultNPOIRowList = new List<NPOIRow>();
            resultNPOIRowList.Add(rowTitle);
            resultNPOIRowList.AddRange(farCellNPOIRows);
            resultNPOIRowList.AddRange(highCellNPOIRows);
            resultNPOIRowList.AddRange(nearSiteNPOIRows);

            this.BackgroundNPOIRowResultDic.Clear();
            this.BackgroundNPOIRowResultDic[this.Name] = resultNPOIRowList;
        }
        protected NPOIRow convertToNPOIRow(UltraSiteCell item)
        {
            NPOIRow row = new NPOIRow();
            row.AddCellValue(item.TypeName);
            row.AddCellValue(item.AreaName);
            row.AddCellValue(item.Cell.Site.Name);
            row.AddCellValue(item.Cell.Name);

            NRCell nrCell = item.Cell as NRCell;
            if (nrCell != null)
            {
                row.AddCellValue(nrCell.TAC);
                row.AddCellValue(nrCell.NCI);
            }
            else
            {
                row.AddCellValue("");
                row.AddCellValue("");
            }
            row.AddCellValue(item.DistanceByDelaunay);
            row.AddCellValue(item.DistanceByDir);

            if (item is UltraFarSite)
            {
                UltraFarSite farSite = item as UltraFarSite;
                StringBuilder btsNames = new StringBuilder();
                StringBuilder cellNames = new StringBuilder();
                foreach (SiteDistance site in farSite.DirSites)
                {
                    btsNames.Append(site.Site.Name + "，");
                    if (site.Site is NRBTS)
                    {
                        foreach (NRCell cell in (site.Site as NRBTS).LatestCells)
                        {
                            cellNames.Append(cell.Name + '，');
                        }
                    }
                }

                row.AddCellValue(farSite.Distance);
                row.AddCellValue(farSite.Cell.Longitude);
                row.AddCellValue(farSite.Cell.Latitude);
                row.AddCellValue(btsNames.ToString().TrimEnd('，'));
                row.AddCellValue(cellNames.ToString().TrimEnd('，'));
            }
            else if (item is UltraHighSite)
            {
                UltraHighSite site = item as UltraHighSite;
                row.AddCellValue(site.Cell.Altitude);
                row.AddCellValue(site.Cell.Longitude);
                row.AddCellValue(site.Cell.Latitude);
                row.AddCellValue("");
                row.AddCellValue("");
            }
            else if (item is UltraNearSite)
            {
                UltraNearSite site = item as UltraNearSite;
                row.AddCellValue(site.Distance);
                row.AddCellValue(site.Site.Longitude);
                row.AddCellValue(site.Site.Latitude);
                row.AddCellValue(site.OtherSite.Name);
                row.AddCellValue(site.OtherSite.Name);
            }
            return row;
        }
        #endregion
    }


    /// <summary>
    /// 查询3超小区（超高、超远、超近）站点or小区，暂支持LTE
    /// 默认通过弹窗设置3超条件
    /// 当不弹窗设置条件时，将会以传入的条件，分析全区域的站点小区。
    /// </summary>
    public class UltraSiteQuery : BackgroundQueryBase
    {
        readonly NetworkType netWork = NetworkType.LTE_TDD;
        Dictionary<ICell, List<UltraSiteCell>> cellDic = null;
        public Dictionary<ICell, List<UltraSiteCell>> Result
        {
            get { return cellDic; }
        }
        
        public UltraSiteCondition UltraSiteCondition { get; set; } = new UltraSiteCondition();
        public bool ShowSettingDlg { get; set; } = true;

        protected static readonly object lockObj = new object();
        private static UltraSiteQuery instance = null;
        public static UltraSiteQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new UltraSiteQuery();
                    }
                }
            }
            return instance;
        }
        public UltraSiteQuery()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "三超站点分析"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override bool isValidCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (ShowSettingDlg && !showFuncCondSetDlg())
            {
                return false;
            }
            return UltraSiteCondition != null;
        }
        protected bool showFuncCondSetDlg()
        {
            SettingDlg dlg = new SettingDlg();
            dlg.SetCondition(this.UltraSiteCondition);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            this.UltraSiteCondition = dlg.GetCondition();
            return true;
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19038, this.Name);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override void query()
        {
            if (netWork == NetworkType.LTE_TDD)
            {
                if (!MainModel.IsBackground)
                {
                    if (!MainModel.QueryFromBackground)
                    {
                        queryFront();
                    }
                    else
                    {
                        getBackgroundData();
                    }
                }
                else
                {
                    if (mainModel.BackgroundStarted)
                    {
                        cellDic = new Dictionary<ICell, List<UltraSiteCell>>();
                        reportBackgroundInfo("正在分析三超站点...");
                        judgeSites();
                        reportBackgroundInfo("三超站点分析结束。");
                    }
                }
            }
        }

        private void queryFront()
        {
            cellDic = new Dictionary<ICell, List<UltraSiteCell>>();
            WaitTextBox.Show("正在分析三超站点...", judgeSites);
            if (ShowSettingDlg && cellDic.Count == 0)
            {
                MessageBox.Show("无符合条件的站点！");
            }
            else if (ShowSettingDlg)
            {
                showResultForm(cellDic);
            }
        }

        protected virtual void showResultForm(Dictionary<ICell, List<UltraSiteCell>> cellDic)
        {
            List<UltraHighSite> highCells = new List<UltraHighSite>();
            List<UltraNearSite> nearSites = new List<UltraNearSite>();
            List<UltraFarSite> farCells = new List<UltraFarSite>();
            foreach (List<UltraSiteCell> uSites in cellDic.Values)
            {
                foreach (UltraSiteCell uSite in uSites)
                {
                    if (uSite is UltraHighSite)
                    {
                        highCells.Add(uSite as UltraHighSite);
                    }
                    else if (uSite is UltraNearSite)
                    {
                        nearSites.Add(uSite as UltraNearSite);
                    }
                    else if (uSite is UltraFarSite)
                    {
                        farCells.Add(uSite as UltraFarSite);
                    }
                }
            }
            UltraSiteCellForm frm = MainModel.CreateResultForm(typeof(UltraSiteCellForm)) as UltraSiteCellForm;
            frm.FillData(farCells, highCells, nearSites);
            frm.Visible = true;
            frm.BringToFront();
        }

        private List<UltraFarSite> getCellAvgDistanceByDir(ISite site, List<ISite> otherSites)
        {
            LTEBTS bts = site as LTEBTS;
            List<UltraFarSite> farCells = new List<UltraFarSite>();
            foreach (LTECell cell in bts.LatestCells)
            {
                List<ISite> twoKMSites = new List<ISite>();
                List<ISite> tenKMSites = new List<ISite>();
                List<ISite> twtyKMSites = new List<ISite>();
                addSites(otherSites, bts, twoKMSites, tenKMSites, twtyKMSites);
                List<SiteDistance> nearestCells = new List<SiteDistance>();
                addInDirSites(nearestCells, cell, twoKMSites);
                if (nearestCells.Count < 3)
                {
                    addInDirSites(nearestCells, cell, tenKMSites);
                }
                if (nearestCells.Count < 3)
                {
                    addInDirSites(nearestCells, cell, twtyKMSites);
                }
                nearestCells.Sort();
                double disTotal = 0;
                List<SiteDistance> nSites = new List<SiteDistance>();
                for (int i = 0; i < 3 && i < nearestCells.Count; i++)
                {
                    nSites.Add(nearestCells[i]);
                    disTotal += nearestCells[i].Distance;
                }
                double avgDis = Math.Round(disTotal / nSites.Count, 2);
                farCells.Add(new UltraFarSite(cell, avgDis, nSites));
            }
            return farCells;
        }

        private void addSites(List<ISite> otherSites, LTEBTS bts, List<ISite> twoKMSites, List<ISite> tenKMSites, List<ISite> twtyKMSites)
        {
            foreach (ISite other in otherSites)
            {
                if (other == bts)
                {
                    continue;
                }
                double lngDis = Math.Abs(bts.Longitude - other.Longitude);
                double latDis = Math.Abs(bts.Latitude - other.Latitude);
                if (lngDis > 0.2 || latDis > 0.2)
                {//约20KM
                }
                else if (lngDis > 0.1 || latDis > 0.1)
                {//约10KM
                    twtyKMSites.Add(other);
                }
                else if (lngDis > 0.02 || latDis > 0.02)
                {//约2KM
                    tenKMSites.Add(other);
                }
                else
                {
                    twoKMSites.Add(other);
                }
            }
        }

        private void addInDirSites(List<SiteDistance> sites, ICell cell, List<ISite> siteSet)
        {
            foreach (ISite site in siteSet)
            {
                if (MathFuncs.JudgePoint(cell.Longitude, cell.Latitude,
                        site.Longitude, site.Latitude, (int)cell.Direction
                        , UltraSiteCondition.AvgSitesDistanceAngle / 2))
                {//在扇区范围内
                    double distance = MathFuncs.GetDistance(cell.Longitude, cell.Latitude
                    , site.Longitude, site.Latitude);
                    sites.Add(new SiteDistance(site, distance));
                }
            }
        }

        private Dictionary<ISite, SiteDelaunayTri> getSiteDelaunayTri(List<ISite> allSites
            , List<ISite> filterSites)
        {
            WaitBox.Text = "正在构建delaunay三角网...";
            Dictionary<int, List<ISite>> posSiteDic = new Dictionary<int, List<ISite>>();
            List<Vertex> pnts = new List<Vertex>();
            foreach (ISite site in allSites)
            {
                Vertex pnt = new Vertex(site.Longitude, site.Latitude);
                if (!pnts.Contains(pnt))
                {//过滤掉相同经纬度的点
                    pnts.Add(pnt);
                }
                int posKey = site.Longitude.GetHashCode() ^ site.Latitude.GetHashCode();
                List<ISite> samePosSites = null;
                if (!posSiteDic.TryGetValue(posKey, out samePosSites))
                {
                    samePosSites = new List<ISite>();
                    posSiteDic.Add(posKey, samePosSites);
                }
                samePosSites.Add(site);
            }

            CTriangle cTriangle = new CTriangle();
            List<Vertex> cTriangleResult = null;
            try
            {
                cTriangleResult = cTriangle.Triangulate(pnts);
            }
            catch (Exception e)
            {
                if (e is ArgumentException)
                {
                    throw;
                }
                else
                {
                    ErrorInfo += "缺少CGeometry动态库！";
                }
                return new Dictionary<ISite, SiteDelaunayTri>();
            }
            System.Diagnostics.Debug.Assert(cTriangleResult.Count % 3 == 0, "构建delaunay三角网失败");
            Dictionary<ISite, SiteDelaunayTri> delaunayDic = new Dictionary<ISite, SiteDelaunayTri>();
            //每3个点，为一个delaunay三角
            for (int i = 0; i + 2 < cTriangleResult.Count; i++)
            {
                Vertex pt1 = cTriangleResult[i];
                Vertex pt2 = cTriangleResult[++i];
                Vertex pt3 = cTriangleResult[++i];
                List<ISite> site1 = posSiteDic[pt1.GetHashCode()];
                List<ISite> site2 = posSiteDic[pt2.GetHashCode()];
                List<ISite> site3 = posSiteDic[pt3.GetHashCode()];

                addOtherSite(filterSites, delaunayDic, site1, site2, site3);
                addOtherSite(filterSites, delaunayDic, site2, site1, site3);
                addOtherSite(filterSites, delaunayDic, site3, site1, site2);
            }
            return delaunayDic;
        }

        private void addOtherSite(List<ISite> filterSites, Dictionary<ISite, SiteDelaunayTri> delaunayDic, 
            List<ISite> siteA, List<ISite> siteB, List<ISite> siteC)
        {
            foreach (ISite site in siteA)
            {
                if (!filterSites.Contains((LTEBTS)site))
                {
                    SiteDelaunayTri dTri = null;
                    if (!delaunayDic.TryGetValue(site, out dTri))
                    {
                        dTri = new SiteDelaunayTri(site);
                        delaunayDic.Add(site, dTri);
                    }
                    foreach (ISite item in siteB)
                    {
                        dTri.AddOtherSite(item);
                    }
                    foreach (ISite item in siteC)
                    {
                        dTri.AddOtherSite(item);
                    }
                }
            }
        }

        private void judgeSites()
        {
            List<LTEBTS> tempSites = getCurrentLTEBTSs();
            List<ISite> allSites = new List<ISite>(tempSites.Count);
            foreach (LTEBTS item in tempSites)
            {
                if (item.Type == LTEBTSType.Indoor)
                {
                    continue;
                }
                allSites.Add(item);
            }
            try
            {
                if (allSites.Count < 3)
                {
                    ErrorInfo += "室外站数量小于3个，无法计算站间距！";
                    return;
                }

                List<ISite> inRegionSites = new List<ISite>(allSites);
                List<ISite> outRegionSites = new List<ISite>();

                getRegionSites(inRegionSites, outRegionSites);

                Dictionary<ISite, SiteDelaunayTri> delaunayDic = getSiteDelaunayTri(allSites, outRegionSites);
                //先计算站间距，后面超高，超近小区会有站间距信息。
                Dictionary<ICell, UltraFarSite> cellDisDic = new Dictionary<ICell, UltraFarSite>();
                addCellDic(allSites, inRegionSites, delaunayDic, cellDisDic);

                dealRegionSites(inRegionSites, outRegionSites, cellDisDic);
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        private void getRegionSites(List<ISite> inRegionSites, List<ISite> outRegionSites)
        {
            if (ShowSettingDlg && MainModel.SearchGeometrys != null
                && MainModel.SearchGeometrys.IsSelectRegion())
            {
                for (int i = 0; i < inRegionSites.Count; i++)
                {
                    ISite site = inRegionSites[i];
                    if (!MainModel.SearchGeometrys.GeoOp.Contains(site.Longitude, site.Latitude))
                    {
                        outRegionSites.Add(site);
                        inRegionSites.RemoveAt(i);
                        i--;
                    }
                }
            }
        }

        private void addCellDic(List<ISite> allSites, List<ISite> inRegionSites, 
            Dictionary<ISite, SiteDelaunayTri> delaunayDic, Dictionary<ICell, UltraFarSite> cellDisDic)
        {
            foreach (ISite bts in inRegionSites)
            {
                string areaName = GISManager.GetInstance().GetGridDesc(bts.Longitude, bts.Latitude);
                SiteDelaunayTri siteDTri = delaunayDic[bts];
                //超远 （方向角站间距）
                List<UltraFarSite> tempFarSites = getCellAvgDistanceByDir(bts, allSites);
                foreach (UltraFarSite farSite in tempFarSites)
                {
                    farSite.AreaName = areaName;
                    farSite.SetDelaunayInfo(siteDTri);
                    cellDisDic[farSite.Cell] = farSite;
                    if (!UltraSiteCondition.IsTooFar(farSite.Distance))
                        continue;
                    List<UltraSiteCell> ultraSet = null;
                    if (!cellDic.TryGetValue(farSite.Cell, out ultraSet))
                    {
                        ultraSet = new List<UltraSiteCell>();
                        cellDic.Add(farSite.Cell, ultraSet);
                    }
                    ultraSet.Add(farSite);
                }
            }
        }

        private void dealRegionSites(List<ISite> inRegionSites, List<ISite> outRegionSites, Dictionary<ICell, UltraFarSite> cellDisDic)
        {
            for (int i = 0; i < inRegionSites.Count; i++)
            {
                LTEBTS bts = inRegionSites[i] as LTEBTS;

                //超高
                foreach (LTECell cell in bts.LatestCells)
                {
                    if (UltraSiteCondition.IsTooHighSite(cell))
                    {
                        addUltraHighSite(cellDisDic, cell);
                    }
                }

                //超近
                dealInRegionSites(inRegionSites, cellDisDic, i, bts);
                dealOutRegionSites(outRegionSites, cellDisDic, bts);
            }
        }

        private void dealInRegionSites(List<ISite> inRegionSites, Dictionary<ICell, UltraFarSite> cellDisDic, int i, LTEBTS bts)
        {
            for (int j = i + 1; j < inRegionSites.Count; j++)
            {
                LTEBTS other = inRegionSites[j] as LTEBTS;
                double dis = 0;
                if (UltraSiteCondition.IsTooNearSite(bts, other, out dis))
                {
                    addValidBts(cellDisDic, other, bts, dis);
                    addValidBts(cellDisDic, bts, other, dis);
                }
            }
        }

        private void addValidBts(Dictionary<ICell, UltraFarSite> cellDisDic, LTEBTS bts, LTEBTS judgedBts, double dis)
        {
            foreach (LTECell cell in judgedBts.LatestCells)
            {
                bool diffBand = false;
                foreach (LTECell tmp in bts.LatestCells)
                {
                    if (tmp.BandType != cell.BandType)
                    {
                        diffBand = true;
                        break;
                    }
                }
                if (diffBand && dis <= UltraSiteCondition.DiffBandDistanceMin)
                {//异频，且距离很近有可能是共站情况，过滤。
                    continue;
                }

                addUltraNearSite(cellDisDic, bts, dis, cell);
            }
        }

        private void dealOutRegionSites(List<ISite> outRegionSites, Dictionary<ICell, UltraFarSite> cellDisDic, LTEBTS bts)
        {
            foreach (ISite other in outRegionSites)
            {
                double dis = 0;
                if (UltraSiteCondition.IsTooNearSite(bts, other, out dis))
                {
                    foreach (LTECell cell in bts.LatestCells)
                    {
                        addUltraNearSite(cellDisDic, other, dis, cell);
                    }
                }
            }
        }

        private void addUltraHighSite(Dictionary<ICell, UltraFarSite> cellDisDic, LTECell cell)
        {
            UltraHighSite hSite = new UltraHighSite(cell);
            hSite.DistanceByDelaunay = cellDisDic[cell].DistanceByDelaunay;
            hSite.DistanceByDir = cellDisDic[cell].DistanceByDir;
            hSite.AreaName = cellDisDic[cell].AreaName;
            List<UltraSiteCell> ultraSet = null;
            if (!cellDic.TryGetValue(cell, out ultraSet))
            {
                ultraSet = new List<UltraSiteCell>();
                cellDic.Add(cell, ultraSet);
            }
            ultraSet.Add(hSite);
        }

        private void addUltraNearSite(Dictionary<ICell, UltraFarSite> cellDisDic, ISite site, double dis, LTECell cell)
        {
            UltraNearSite nSite = new UltraNearSite(cell, site, dis);
            nSite.DistanceByDelaunay = cellDisDic[cell].DistanceByDelaunay;
            nSite.DistanceByDir = cellDisDic[cell].DistanceByDir;
            nSite.AreaName = cellDisDic[cell].AreaName;
            List<UltraSiteCell> ultraSet = null;
            if (!cellDic.TryGetValue(cell, out ultraSet))
            {
                ultraSet = new List<UltraSiteCell>();
                cellDic.Add(cell, ultraSet);
            }
            ultraSet.Add(nSite);
        }

        private List<LTEBTS> getCurrentLTEBTSs()
        {
            List<LTEBTS> validBts = CellManager.GetInstance().GetCurrentLTEBTSs();
            if (MainModel.SearchGeometrys != null && MainModel.SearchGeometrys.IsSelectRegion())
            {
                DbRect selectRect = MainModel.SearchGeometrys.RegionBounds;
                DbRect validRect = new DbRect(selectRect.x1 - 0.1, selectRect.y1 - 0.1, selectRect.x2 + 0.1, selectRect.y2 + 0.1);

                for (int i = 0; i < validBts.Count; i++)
                {
                    ISite site = validBts[i];
                    if (!validRect.IsPointInThisRect(site.Longitude, site.Latitude))
                    {
                        validBts.RemoveAt(i);
                        i--;
                    }
                }
            }
            return validBts;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }
        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.其他; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                ignoreParamKeys.Add("FuncCond");

                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                if (UltraSiteCondition != null)
                {
                    param["FuncCond"] = UltraSiteCondition.Param;
                }
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("FuncCond"))
                {
                    if (UltraSiteCondition == null)
                    {
                        UltraSiteCondition = new UltraSiteCondition();
                    }
                    UltraSiteCondition.Param = param["FuncCond"] as Dictionary<string, object>;
                }
            }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.None; }
        }
        public override PropertiesControl Properties
        {
            get
            {
                return new CommonSimpleProperties(this, showFuncCondSetDlg);
            }
        }
        protected override void getBackgroundData()
        {
            if (!BackgroundFuncConfigManager.GetInstance().AutoExportResult
                || cellDic == null || cellDic.Count <= 0)
            {
                return;
            }

            List<NPOIRow> farCellNPOIRows = new List<NPOIRow>();
            List<NPOIRow> highCellNPOIRows = new List<NPOIRow>();
            List<NPOIRow> nearSiteNPOIRows = new List<NPOIRow>();
            foreach (List<UltraSiteCell> uSites in cellDic.Values)
            {
                foreach (UltraSiteCell uSite in uSites)
                {
                    NPOIRow row = convertToNPOIRow(uSite);
                    if (uSite is UltraHighSite)
                    {
                        highCellNPOIRows.Add(row);
                    }
                    else if (uSite is UltraNearSite)
                    {
                        nearSiteNPOIRows.Add(row);
                    }
                    else if (uSite is UltraFarSite)
                    {
                        farCellNPOIRows.Add(row);
                    }
                }
            }

            NPOIRow rowTitle = new NPOIRow();
            rowTitle.AddCellValue("类别");
            rowTitle.AddCellValue("所属区域");
            rowTitle.AddCellValue("站点");
            rowTitle.AddCellValue("小区");
            rowTitle.AddCellValue("TAC");
            rowTitle.AddCellValue("ECI");
            rowTitle.AddCellValue("Delaunay站间距");
            rowTitle.AddCellValue("站间距(方向角)");
            rowTitle.AddCellValue("问题信息");
            rowTitle.AddCellValue("经度");
            rowTitle.AddCellValue("纬度");
            rowTitle.AddCellValue("相关站点");
            rowTitle.AddCellValue("相关小区");

            List<NPOIRow> resultNPOIRowList = new List<NPOIRow>();
            resultNPOIRowList.Add(rowTitle);
            resultNPOIRowList.AddRange(farCellNPOIRows);
            resultNPOIRowList.AddRange(highCellNPOIRows);
            resultNPOIRowList.AddRange(nearSiteNPOIRows);

            this.BackgroundNPOIRowResultDic.Clear();
            this.BackgroundNPOIRowResultDic[this.Name] = resultNPOIRowList;
        }
        protected NPOIRow convertToNPOIRow(UltraSiteCell item)
        {
            NPOIRow row = new NPOIRow();
            row.AddCellValue(item.TypeName);
            row.AddCellValue(item.AreaName);
            row.AddCellValue(item.Cell.Site.Name);
            row.AddCellValue(item.Cell.Name);

            LTECell lteCell = item.Cell as LTECell;
            if (lteCell != null)
            {
                row.AddCellValue(lteCell.TAC);
                row.AddCellValue(lteCell.ECI);
            }
            else
            {
                row.AddCellValue("");
                row.AddCellValue("");
            }
            row.AddCellValue(item.DistanceByDelaunay);
            row.AddCellValue(item.DistanceByDir);

            if (item is UltraFarSite)
            {
                UltraFarSite farSite = item as UltraFarSite;
                StringBuilder btsNames = new StringBuilder();
                StringBuilder cellNames = new StringBuilder();
                foreach (SiteDistance site in farSite.DirSites)
                {
                    btsNames.Append(site.Site.Name + "，");
                    if (site.Site is LTEBTS)
                    {
                        foreach (LTECell cell in (site.Site as LTEBTS).LatestCells)
                        {
                            cellNames.Append(cell.Name + '，');
                        }
                    }
                }

                row.AddCellValue(farSite.Distance);
                row.AddCellValue(farSite.Cell.Longitude);
                row.AddCellValue(farSite.Cell.Latitude);
                row.AddCellValue(btsNames.ToString().TrimEnd('，'));
                row.AddCellValue(cellNames.ToString().TrimEnd('，'));
            }
            else if (item is UltraHighSite)
            {
                UltraHighSite site = item as UltraHighSite;
                row.AddCellValue(site.Cell.Altitude);
                row.AddCellValue(site.Cell.Longitude);
                row.AddCellValue(site.Cell.Latitude);
                row.AddCellValue("");
                row.AddCellValue("");
            }
            else if (item is UltraNearSite)
            {
                UltraNearSite site = item as UltraNearSite;
                row.AddCellValue(site.Distance);
                row.AddCellValue(site.Site.Longitude);
                row.AddCellValue(site.Site.Latitude);
                row.AddCellValue(site.OtherSite.Name);
                row.AddCellValue(site.OtherSite.Name);
            }
            return row;
        }
        #endregion
    }

    /// <summary>
    /// 基站Delaunay信息
    /// </summary>
    public class SiteDelaunayTri
    {
        public ISite Site
        { get; set; }
        public int VertexHashCode
        {
            get
            {
                return Site.Longitude.GetHashCode() ^ Site.Latitude.GetHashCode();
            }
        }
        public SiteDelaunayTri(ISite site)
        {
            this.Site = site;
        }
        public void AddOtherSite(ISite site)
        {
            if (!others.Contains(site))
            {
                others.Add(site);
            }
        }
        private readonly List<ISite> others = new List<ISite>();
        public List<ISite> Others
        {
            get { return others; }
        }
        public double Distance
        {
            get
            {
                double totalDis = 0;
                int less5KMCnt = 0;
                foreach (ISite other in others)
                {
                    double dis = MathFuncs.GetDistance(Site.Longitude, Site.Latitude
                        , other.Longitude, other.Latitude);
                    if (dis > 5000)
                    {
                        continue;
                    }
                    totalDis += dis;
                    less5KMCnt++;
                }
                return Math.Round(totalDis / less5KMCnt, 2);
            }
        }
    }

    public abstract class UltraSiteCell
    {
        public override string ToString()
        {
            return TypeName;
        }
        public abstract string TypeName
        {
            get;
        }
        public ICell Cell
        {
            get;
            protected set;
        }

        public string AreaName
        {
            get;
            set;
        }

        public double DistanceByDir
        {
            get;
            set;
        }

        public double DistanceByDelaunay
        {
            get;
            set;
        }
    }

    public class UltraNearSite : UltraSiteCell
    {
        public override string TypeName
        {
            get { return "超近站"; }
        }
        public ISite Site
        {
            get;
            private set;
        }
        public ISite OtherSite
        {
            get;
            private set;
        }
        public double Distance { get; private set; }
        public UltraNearSite(ICell cell, ISite otherSite, double distance)
        {
            this.Cell = cell;
            this.Site = cell.Site;
            this.OtherSite = otherSite;
            this.Distance = Math.Round(distance, 2);
        }
    }

    public class UltraHighSite : UltraSiteCell
    {
        public override string TypeName
        {
            get { return "超高站"; }
        }
        public UltraHighSite(ICell cell)
        {
            Cell = cell;
        }
    }

    public class UltraFarSite : UltraSiteCell
    {
        public override string TypeName
        {
            get { return "超远站"; }
        }
        public List<SiteDistance> DirSites
        {
            get;
            private set;
        }
        public List<ISite> DelaunaySites
        {
            get;
            private set;
        }
        public void SetDelaunayInfo(SiteDelaunayTri dSite)
        {
            this.DistanceByDelaunay = dSite.Distance;
            this.DelaunaySites = dSite.Others;
        }
        public UltraFarSite(ICell cell,double avgDis,List<SiteDistance> sites)
        {
            this.Cell = cell;
            this.DistanceByDir = avgDis;
            this.DirSites = sites;
        }

        public double Distance
        {
            get { return Math.Min(DistanceByDelaunay, DistanceByDir); }
        }
    }

    public class UltraSiteCondition
    {
        private double nearDistanceMin = 100;
        public double NearDistanceMin
        {
            get { return nearDistanceMin; }
            set
            {
                nearDistanceMin = value;
                nearDisSpanMin = value * 0.00001;
            }
        }
        
        public double DiffBandDistanceMin { get; set; } = 50;

        /// <summary>
        /// 经纬度跨度，用于减少计算量
        /// </summary>
        private double nearDisSpanMin = 100 * 0.00001;

        public bool IsTooNearSite(ISite site1, ISite site2,out double distance)
        {
            distance = 0;
            if (Math.Abs(site1.Longitude - site2.Longitude) > nearDisSpanMin
                || Math.Abs(site1.Latitude - site2.Latitude) > nearDisSpanMin)
            {
                return false;
            }
            distance = MathFuncs.GetDistance(site1.Longitude, site1.Latitude, site2.Longitude, site2.Latitude);
            return distance < nearDistanceMin;
        }

        public bool IsTooHighSite(ICell cell)
        {
            return cell.Altitude > AltitudeMax;
        }
        
        public double AltitudeMax { get; set; } = 50;
        
        public double AvgSiteDistanceMax { get; set; } = 700;
        
        public int AvgSitesDistanceAngle { get; set; } = 120;

        public List<UltraFarSite> JudgeFarCell(LTEBTS bts, List<LTEBTS> btsSet)
        {
            List<UltraFarSite> farCells = new List<UltraFarSite>();
            foreach (LTECell cell in bts.LatestCells)
            {
                List<LTEBTS> twoKMSites = new List<LTEBTS>();
                List<LTEBTS> tenKMSites = new List<LTEBTS>();
                List<LTEBTS> twtyKMSites = new List<LTEBTS>();
                addSites(bts, btsSet, twoKMSites, tenKMSites, twtyKMSites);
                List<SiteDistance> nearestCells = new List<SiteDistance>();
                addDirSites(nearestCells, cell, twoKMSites);
                if (nearestCells.Count < 3)
                {
                    addDirSites(nearestCells, cell, tenKMSites);
                }
                if (nearestCells.Count < 3)
                {
                    addDirSites(nearestCells, cell, twtyKMSites);
                }
                if (nearestCells.Count == 0)
                {
                    continue;
                }
                nearestCells.Sort();
                double disTotal = 0;
                List<SiteDistance> nSites = new List<SiteDistance>();
                for (int i = 0; i < 3 && i < nearestCells.Count; i++)
                {
                    nSites.Add(nearestCells[i]);
                    disTotal += nearestCells[i].Distance;
                }
                double avgDis = Math.Round(disTotal / nSites.Count, 2);
                if (avgDis > AvgSiteDistanceMax)
                {
                    farCells.Add(new UltraFarSite(cell, avgDis, nSites));
                }
            }
            return farCells;
        }

        private void addSites(LTEBTS bts, List<LTEBTS> btsSet, List<LTEBTS> twoKMSites, List<LTEBTS> tenKMSites, List<LTEBTS> twtyKMSites)
        {
            foreach (LTEBTS site in btsSet)
            {
                if (site != bts)
                {
                    double lngDis = Math.Abs(bts.Longitude - site.Longitude);
                    double latDis = Math.Abs(bts.Latitude - site.Latitude);
                    if (lngDis > 0.2 || latDis > 0.2)
                    {
                        //约20KM
                    }
                    else if (lngDis > 0.1 || latDis > 0.1)
                    {//约10KM
                        twtyKMSites.Add(site);
                    }
                    else if (lngDis > 0.02 || latDis > 0.02)
                    {//约2KM
                        tenKMSites.Add(site);
                    }
                    else
                    {
                        twoKMSites.Add(site);
                    }
                }
            }
        }

        private void addDirSites(List<SiteDistance> sites, ICell cell, List<LTEBTS> btsSet)
        {
            foreach (LTEBTS site in btsSet)
            {
                double distance = MathFuncs.GetDistance(cell.Longitude, cell.Latitude
                    , site.Longitude, site.Latitude);
                if (MathFuncs.JudgePoint(cell.Longitude, cell.Latitude,
                        site.Longitude, site.Latitude, (int)cell.Direction, AvgSitesDistanceAngle / 2))
                {//在扇区范围内
                    sites.Add(new SiteDistance(site, distance));
                }
            }
        }

        public List<UltraFarSite> JudgeFarCell(NRBTS bts, List<NRBTS> btsSet)
        {
            List<UltraFarSite> farCells = new List<UltraFarSite>();
            foreach (NRCell cell in bts.LatestCells)
            {
                List<NRBTS> twoKMSites = new List<NRBTS>();
                List<NRBTS> tenKMSites = new List<NRBTS>();
                List<NRBTS> twtyKMSites = new List<NRBTS>();
                addSites(bts, btsSet, twoKMSites, tenKMSites, twtyKMSites);
                List<SiteDistance> nearestCells = new List<SiteDistance>();
                addDirSites(nearestCells, cell, twoKMSites);
                if (nearestCells.Count < 3)
                {
                    addDirSites(nearestCells, cell, tenKMSites);
                }
                if (nearestCells.Count < 3)
                {
                    addDirSites(nearestCells, cell, twtyKMSites);
                }
                if (nearestCells.Count == 0)
                {
                    continue;
                }
                nearestCells.Sort();
                double disTotal = 0;
                List<SiteDistance> nSites = new List<SiteDistance>();
                for (int i = 0; i < 3 && i < nearestCells.Count; i++)
                {
                    nSites.Add(nearestCells[i]);
                    disTotal += nearestCells[i].Distance;
                }
                double avgDis = Math.Round(disTotal / nSites.Count, 2);
                if (avgDis > AvgSiteDistanceMax)
                {
                    farCells.Add(new UltraFarSite(cell, avgDis, nSites));
                }
            }
            return farCells;
        }

        private void addSites(NRBTS bts, List<NRBTS> btsSet, List<NRBTS> twoKMSites, List<NRBTS> tenKMSites, List<NRBTS> twtyKMSites)
        {
            foreach (NRBTS site in btsSet)
            {
                if (site != bts)
                {
                    double lngDis = Math.Abs(bts.Longitude - site.Longitude);
                    double latDis = Math.Abs(bts.Latitude - site.Latitude);
                    if (lngDis > 0.2 || latDis > 0.2)
                    {
                        //约20KM
                    }
                    else if (lngDis > 0.1 || latDis > 0.1)
                    {//约10KM
                        twtyKMSites.Add(site);
                    }
                    else if (lngDis > 0.02 || latDis > 0.02)
                    {//约2KM
                        tenKMSites.Add(site);
                    }
                    else
                    {
                        twoKMSites.Add(site);
                    }
                }
            }
        }

        private void addDirSites(List<SiteDistance> sites, ICell cell, List<NRBTS> btsSet)
        {
            foreach (NRBTS site in btsSet)
            {
                double distance = MathFuncs.GetDistance(cell.Longitude, cell.Latitude
                    , site.Longitude, site.Latitude);
                if (MathFuncs.JudgePoint(cell.Longitude, cell.Latitude,
                        site.Longitude, site.Latitude, (int)cell.Direction, AvgSitesDistanceAngle / 2))
                {//在扇区范围内
                    sites.Add(new SiteDistance(site, distance));
                }
            }
        }


        internal bool IsTooFar(double distance)
        {
            return distance > this.AvgSiteDistanceMax;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["NearDistanceMin"] = NearDistanceMin;
                param["DiffBandDistanceMin"] = DiffBandDistanceMin;
                param["AltitudeMax"] = AltitudeMax;
                param["AvgSiteDistanceMax"] = AvgSiteDistanceMax;
                param["AvgSitesDistanceAngle"] = AvgSitesDistanceAngle;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("NearDistanceMin"))
                {
                    NearDistanceMin = (double)param["NearDistanceMin"];
                }
                if (param.ContainsKey("DiffBandDistanceMin"))
                {
                    DiffBandDistanceMin = (double)param["DiffBandDistanceMin"];
                }
                if (param.ContainsKey("AltitudeMax"))
                {
                    AltitudeMax = (double)param["AltitudeMax"];
                }
                if (param.ContainsKey("AvgSiteDistanceMax"))
                {
                    AvgSiteDistanceMax = (double)param["AvgSiteDistanceMax"];
                }
                if (param.ContainsKey("AvgSitesDistanceAngle"))
                {
                    AvgSitesDistanceAngle = (int)param["AvgSitesDistanceAngle"];
                }
            }
        }
    }

    public class SiteDistance : IComparable<SiteDistance>
    {
        readonly double dis;
        public double Distance
        {
            get { return dis; }
        }
        public ISite Site
        {
            get;
            private set;
        }
        public SiteDistance(ISite site, double dis)
        {
            this.Site = site;
            this.dis = dis;
        }

        #region IComparable<CellDistance> 成员

        public int CompareTo(SiteDistance other)
        {
            if (other == null)
            {
                return -1;
            }
            return this.dis.CompareTo(other.dis);
        }

        #endregion
    }

    public class UltraSiteQuery_FDD : UltraSiteQuery
    {
        public UltraSiteQuery_FDD()
            : base()
        {

        }

        public override string Name
        {
            get { return "超站点分析_LTE_FDD"; }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 26000, 26027, this.Name);
        }
    }

}
