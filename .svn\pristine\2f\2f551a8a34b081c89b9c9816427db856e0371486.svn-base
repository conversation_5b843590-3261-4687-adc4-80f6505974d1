﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class BtsFusionInfo_XJ : BtsFusionInfoBase
    {
        public BtsFusionInfo_XJ(BtsWorkParamBase btsWorkParamInfo, DateTime beginTime, DateTime endTime)
            : base(btsWorkParamInfo, beginTime, endTime)
        {
        }
    }
    public class BtsAlarmData_XJ : BtsAlarmDataBase
    {
    }
    public class CellPerfData_XJ : CellPerfDataBase
    {
    }
    public class CellMRData_XJ : CellMRDataBase
    {
    }
    public class BtsFusionInfoBase
    {
        public BtsFusionInfoBase(BtsWorkParamBase btsWorkParamInfo, DateTime beginTime, DateTime endTime)
        {
            this.BtsWorkParamInfo = btsWorkParamInfo;
            this.BeginTime = beginTime;
            this.EndTime = endTime;
        }
        public BtsWorkParamBase BtsWorkParamInfo { get; set; }
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }

        //各小区的性能信息 <小区名,Dictionary<日期,CellPerfDataBase>>
        public Dictionary<string, Dictionary<string, CellPerfDataBase>> CellPerfInfoDic { get; set; }

        //各小区的MR信息 <小区名,Dictionary<日期,CellMRDataBase>>
        public Dictionary<string, Dictionary<string, CellMRDataBase>> CellMRInfoDic { get; set; }

        //基站的告警信息 Dictionary<日期,BtsAlarmDataBase>
        public Dictionary<string, BtsAlarmDataBase> BtsAlarmInfoDic { get; set; }

        private bool isAccord = false;
        public bool IsAccord { get { return isAccord; } }

        private string notAccordKpiDes = string.Empty;
        public string NotAccordKpiDes { get { return notAccordKpiDes; } }
        public void CheckIsAccordAccept(StationAcceptAutoSetBase funcSet)
        {
            isAccord = true;
            if (BtsAlarmInfoDic != null && BtsAlarmInfoDic.Count > 0)
            {
                notAccordKpiDes += "本站存在告警;\r\n";
                isAccord = false;
            }

            string districtName = this.BtsWorkParamInfo.DistrictName;
            if (string.IsNullOrEmpty(funcSet.PerfDataCheckDistictNames)
                || !funcSet.PerfDataCheckDistictNames.Contains(districtName))
            {
                return;
            }

            if (CellPerfInfoDic == null || CellPerfInfoDic.Count <= 0)
            {
                notAccordKpiDes += "未查询到性能指标;\r\n";
                isAccord = false;
                return;
            }

            //从数据库中查到的关联数据已按时间降序排练
            StringBuilder sb = new StringBuilder(notAccordKpiDes);
            foreach (CellWorkParamBase cellWorkParam in this.BtsWorkParamInfo.CellWorkParams)
            {
                bool? isCurCellPerfAccord = null;
                Dictionary<string, CellPerfDataBase> date_cellPerfDataDic;
                if (this.CellPerfInfoDic.TryGetValue(cellWorkParam.CGI, out date_cellPerfDataDic))//获取小区的关联指标
                {
                    isCurCellPerfAccord = isCellPerfAccord(date_cellPerfDataDic, funcSet.PerfDataAccordLastDays);
                }

                if (isCurCellPerfAccord != true)
                {
                    sb.Append(cellWorkParam.CellName + "小区性能指标不达标;\r\n");
                    isAccord = false;//有一个小区性能未达标即为不通过
                }
            }
            notAccordKpiDes = sb.ToString();
        }

        //检查小区的性能指标是否通过验收
        protected bool? isCellPerfAccord(Dictionary<string, CellPerfDataBase> date_cellPerfDataDic
            , int fusionAccordLastDays)
        {
            if (date_cellPerfDataDic == null || date_cellPerfDataDic.Count <= 0)
            {
                return null;
            }

            bool? isCellPerfAccord = null;
            int perfAccordLastDays = 0;//本小区有性能指标的天数
            DateTime curDate = this.EndTime.Date;

            while (curDate >= this.BeginTime.Date)//按天倒叙遍历
            {
                string dateDes = GetDateKeyDes(curDate);

                #region 核查有性能指标的最近N天，指标是否均达标
                CellPerfDataBase cellPerfData;
                if (date_cellPerfDataDic.TryGetValue(dateDes, out cellPerfData))
                {
                    perfAccordLastDays++;
                    if (!cellPerfData.IsAccord)
                    {
                        isCellPerfAccord = false;
                        break;//
                    }

                    if (perfAccordLastDays >= fusionAccordLastDays)
                    {
                        isCellPerfAccord = true;
                        break;
                    }
                }
                #endregion

                curDate = curDate.AddDays(-1);
            }
            return isCellPerfAccord;
        }
        public static string GetDateKeyDes(DateTime time)
        {
            return time.Date.ToString("yy/MM/dd");
        }

    }

    /// <summary>
    /// 基站告警
    /// </summary>
    public class BtsAlarmDataBase
    {
        public string BtsName { get; set; }
        public string AlarmTitle { get; set; }
        public string BeginDateDes
        {
            get
            {
                DateTime beginTime;
                if (DateTime.TryParse(BeginTimeStr, out beginTime))//结束时间有可能为null,即告警还未结束
                {
                    return BtsFusionInfoBase.GetDateKeyDes(beginTime);
                }
                return "";
            }
        }
        public string BeginTimeStr { get; set; }
        public string EndTimeStr { get; set; }
        public string Desc { get; set; }
        public string LocationInfo { get; set; }
        public void Fill(MasterCom.RAMS.Net.Content content)
        {
            this.BtsName = content.GetParamString();
            this.BeginTimeStr = content.GetParamString();
            this.EndTimeStr = content.GetParamString();
            this.AlarmTitle = content.GetParamString();
            this.LocationInfo = content.GetParamString();
            this.Desc = content.GetParamString();
        }
    }

    /// <summary>
    /// 小区性能
    /// </summary>
    public class CellPerfDataBase
    {
        public string CGI { get; set; }
        public string CellName { get; set; }
        public DateTime BeginTime { get; set; }
        public string BeginDateDes
        {
            get { return BtsFusionInfoBase.GetDateKeyDes(BeginTime); }
        }
        public float RrcConnectTryCount { get; set; }
        public float RrcSetupSuccessRate { get; set; }
        public float ErabConnectTryCount { get; set; }
        public float ErabSetupSuccessRate { get; set; }
        public float ErabDropRate { get; set; }
        public float WirelessConnectRate { get; set; }//无线接通率
        public float WirelessDropRate { get; set; }//无线掉线率
        public float InnerHandoverSuccessRate { get; set; }
        public float PdcpThroughput_DL { get; set; }
        public float PdcpThroughput_UL { get; set; }
        public virtual void Fill(MasterCom.RAMS.Net.Content content)
        {
            this.CGI = content.GetParamString();
            this.BeginTime = DateTime.Parse(content.GetParamString());
            this.RrcConnectTryCount = content.GetParamFloat();
            this.RrcSetupSuccessRate = content.GetParamFloat() * 100;
            this.ErabConnectTryCount = content.GetParamFloat();
            this.ErabSetupSuccessRate = content.GetParamFloat() * 100;
            this.ErabDropRate = content.GetParamFloat() * 100;
            this.WirelessConnectRate = content.GetParamFloat() * 100;
            this.WirelessDropRate = content.GetParamFloat() * 100;
            this.InnerHandoverSuccessRate = content.GetParamFloat() * 100;
            this.PdcpThroughput_DL = content.GetParamFloat();
            this.PdcpThroughput_UL = content.GetParamFloat();
        }
        public virtual bool IsAccord
        {
            get
            {
                return RrcConnectTryCount > 20 && RrcSetupSuccessRate > 99 && ErabConnectTryCount > 20
                    && ErabSetupSuccessRate > 95 && WirelessConnectRate > 94 && WirelessDropRate <= 4
                    && ErabDropRate <= 4 && InnerHandoverSuccessRate >= 95;
            }
        }
    }

    /// <summary>
    /// 小区MR
    /// </summary>
    public class CellMRDataBase
    {
        public string CGI { get; set; }
        public DateTime BeginTime { get; set; }
        public string BeginDateDes
        {
            get { return BtsFusionInfoBase.GetDateKeyDes(BeginTime); }
        }
        public float MrCoverRate { get; set; }

        public void Fill(MasterCom.RAMS.Net.Content content)
        {
            this.CGI = content.GetParamString();
            this.BeginTime = DateTime.Parse(content.GetParamString());
            float mrCoverRate = content.GetParamFloat() * 100;
            this.MrCoverRate = (float)Math.Round(mrCoverRate, 2);
        }
        public virtual bool IsAccord
        {
            get
            {
                return MrCoverRate > 90;
            }
        }
    }
}
