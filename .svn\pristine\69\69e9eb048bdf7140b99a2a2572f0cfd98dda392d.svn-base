﻿namespace MasterCom.MTGis
{
    partial class PrintHDCutDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnPrint = new System.Windows.Forms.Button();
            this.printDocument = new System.Drawing.Printing.PrintDocument();
            this.label1 = new System.Windows.Forms.Label();
            this.cbxPaintBorder = new System.Windows.Forms.CheckBox();
            this.cbxPaintMark = new System.Windows.Forms.CheckBox();
            this.SuspendLayout();
            // 
            // btnPrint
            // 
            this.btnPrint.Location = new System.Drawing.Point(363, 41);
            this.btnPrint.Name = "btnPrint";
            this.btnPrint.Size = new System.Drawing.Size(62, 49);
            this.btnPrint.TabIndex = 0;
            this.btnPrint.Text = "打印";
            this.btnPrint.UseVisualStyleBackColor = true;
            this.btnPrint.Click += new System.EventHandler(this.btnPrint_Click);
            // 
            // printDocument
            // 
            this.printDocument.BeginPrint += new System.Drawing.Printing.PrintEventHandler(this.printDocument_BeginPrint);
            this.printDocument.PrintPage += new System.Drawing.Printing.PrintPageEventHandler(this.printDocument_PrintPage);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 41);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(311, 84);
            this.label1.TabIndex = 1;
            this.label1.Text = "注意：\r\n\r\n1）此打印功能将会把地图按切块打印到多个页面中，\r\n   从左至右，上至下排列。\r\n\r\n2）对于支持无边距打印的打印机，可设置为无边距打印，\r\n  " +
    " 打印无缝拼接的多幅图。";
            // 
            // cbxPaintBorder
            // 
            this.cbxPaintBorder.AutoSize = true;
            this.cbxPaintBorder.Location = new System.Drawing.Point(197, 12);
            this.cbxPaintBorder.Name = "cbxPaintBorder";
            this.cbxPaintBorder.Size = new System.Drawing.Size(84, 16);
            this.cbxPaintBorder.TabIndex = 2;
            this.cbxPaintBorder.Text = "打印边框线";
            this.cbxPaintBorder.UseVisualStyleBackColor = true;
            // 
            // cbxPaintMark
            // 
            this.cbxPaintMark.AutoSize = true;
            this.cbxPaintMark.Location = new System.Drawing.Point(321, 12);
            this.cbxPaintMark.Name = "cbxPaintMark";
            this.cbxPaintMark.Size = new System.Drawing.Size(84, 16);
            this.cbxPaintMark.TabIndex = 3;
            this.cbxPaintMark.Text = "打印行列号";
            this.cbxPaintMark.UseVisualStyleBackColor = true;
            // 
            // PrintHDCutDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(448, 138);
            this.Controls.Add(this.cbxPaintMark);
            this.Controls.Add(this.cbxPaintBorder);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.btnPrint);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "PrintHDCutDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "打印高清截图";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnPrint;
        private System.Drawing.Printing.PrintDocument printDocument;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox cbxPaintBorder;
        private System.Windows.Forms.CheckBox cbxPaintMark;
    }
}