﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellCheckDiffFreqSpanAnaByBegion : ZTLteNBCellCheckDiffFreqSpanAnaBase
    {
        public ZTLteNBCellCheckDiffFreqSpanAnaByBegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected static readonly object lockObj = new object();
        private static ZTLteNBCellCheckDiffFreqSpanAnaByBegion intance = null;
        public static ZTLteNBCellCheckDiffFreqSpanAnaByBegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLteNBCellCheckDiffFreqSpanAnaByBegion(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "LTE邻区核查(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22045, this.Name);//////
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
    public class LteFddNBCellCheckDiffFreqSpanAnaByBegion : ZTLteNBCellCheckDiffFreqSpanAnaByBegion
    {
        public LteFddNBCellCheckDiffFreqSpanAnaByBegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        private static LteFddNBCellCheckDiffFreqSpanAnaByBegion instance = null;
        public new static LteFddNBCellCheckDiffFreqSpanAnaByBegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteFddNBCellCheckDiffFreqSpanAnaByBegion(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26019, this.Name);
        }
    }
}