﻿
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    partial class EventChooserSimpleForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(EventChooserSimpleForm));
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.treeListEvent = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.checkBoxSelectAll = new System.Windows.Forms.CheckBox();
            this.buttonOK = new System.Windows.Forms.Button();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListEvent)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.treeListEvent);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(721, 386);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "事件列表";
            // 
            // treeListEvent
            // 
            this.treeListEvent.Appearance.Empty.BackColor = System.Drawing.Color.Ivory;
            this.treeListEvent.Appearance.Empty.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListEvent.Appearance.Empty.Options.UseBackColor = true;
            this.treeListEvent.Appearance.Empty.Options.UseForeColor = true;
            this.treeListEvent.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(253)))), ((int)(((byte)(245)))), ((int)(((byte)(230)))));
            this.treeListEvent.Appearance.EvenRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListEvent.Appearance.EvenRow.Options.UseBackColor = true;
            this.treeListEvent.Appearance.EvenRow.Options.UseForeColor = true;
            this.treeListEvent.Appearance.FooterPanel.BackColor = System.Drawing.Color.NavajoWhite;
            this.treeListEvent.Appearance.FooterPanel.BorderColor = System.Drawing.Color.NavajoWhite;
            this.treeListEvent.Appearance.FooterPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListEvent.Appearance.FooterPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListEvent.Appearance.FooterPanel.Options.UseBackColor = true;
            this.treeListEvent.Appearance.FooterPanel.Options.UseBorderColor = true;
            this.treeListEvent.Appearance.FooterPanel.Options.UseFont = true;
            this.treeListEvent.Appearance.FooterPanel.Options.UseForeColor = true;
            this.treeListEvent.Appearance.GroupButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListEvent.Appearance.GroupButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListEvent.Appearance.GroupButton.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListEvent.Appearance.GroupButton.Options.UseBackColor = true;
            this.treeListEvent.Appearance.GroupButton.Options.UseBorderColor = true;
            this.treeListEvent.Appearance.GroupButton.Options.UseForeColor = true;
            this.treeListEvent.Appearance.GroupFooter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListEvent.Appearance.GroupFooter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListEvent.Appearance.GroupFooter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListEvent.Appearance.GroupFooter.Options.UseBackColor = true;
            this.treeListEvent.Appearance.GroupFooter.Options.UseBorderColor = true;
            this.treeListEvent.Appearance.GroupFooter.Options.UseForeColor = true;
            this.treeListEvent.Appearance.HeaderPanel.BackColor = System.Drawing.Color.BurlyWood;
            this.treeListEvent.Appearance.HeaderPanel.BorderColor = System.Drawing.Color.BurlyWood;
            this.treeListEvent.Appearance.HeaderPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListEvent.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListEvent.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.treeListEvent.Appearance.HeaderPanel.Options.UseBorderColor = true;
            this.treeListEvent.Appearance.HeaderPanel.Options.UseFont = true;
            this.treeListEvent.Appearance.HeaderPanel.Options.UseForeColor = true;
            this.treeListEvent.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.White;
            this.treeListEvent.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.treeListEvent.Appearance.HorzLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListEvent.Appearance.HorzLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListEvent.Appearance.HorzLine.Options.UseBackColor = true;
            this.treeListEvent.Appearance.HorzLine.Options.UseForeColor = true;
            this.treeListEvent.Appearance.OddRow.BackColor = System.Drawing.Color.Bisque;
            this.treeListEvent.Appearance.OddRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListEvent.Appearance.OddRow.Options.UseBackColor = true;
            this.treeListEvent.Appearance.OddRow.Options.UseForeColor = true;
            this.treeListEvent.Appearance.Preview.BackColor = System.Drawing.Color.Cornsilk;
            this.treeListEvent.Appearance.Preview.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(255)))));
            this.treeListEvent.Appearance.Preview.Options.UseBackColor = true;
            this.treeListEvent.Appearance.Preview.Options.UseForeColor = true;
            this.treeListEvent.Appearance.Preview.Options.UseTextOptions = true;
            this.treeListEvent.Appearance.Preview.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            this.treeListEvent.Appearance.Row.BackColor = System.Drawing.Color.Ivory;
            this.treeListEvent.Appearance.Row.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(25)))), ((int)(((byte)(25)))), ((int)(((byte)(112)))));
            this.treeListEvent.Appearance.Row.Options.UseBackColor = true;
            this.treeListEvent.Appearance.Row.Options.UseForeColor = true;
            this.treeListEvent.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(138)))));
            this.treeListEvent.Appearance.SelectedRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListEvent.Appearance.SelectedRow.Options.UseBackColor = true;
            this.treeListEvent.Appearance.SelectedRow.Options.UseForeColor = true;
            this.treeListEvent.Appearance.TreeLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeListEvent.Appearance.TreeLine.Options.UseForeColor = true;
            this.treeListEvent.Appearance.VertLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListEvent.Appearance.VertLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListEvent.Appearance.VertLine.Options.UseBackColor = true;
            this.treeListEvent.Appearance.VertLine.Options.UseForeColor = true;
            this.treeListEvent.Appearance.VertLine.Options.UseTextOptions = true;
            this.treeListEvent.Appearance.VertLine.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.treeListEvent.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("treeListEvent.BackgroundImage")));
            this.treeListEvent.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn1});
            this.treeListEvent.Location = new System.Drawing.Point(17, 22);
            this.treeListEvent.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.treeListEvent.Name = "treeListEvent";
            this.treeListEvent.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListEvent.OptionsBehavior.AutoChangeParent = false;
            this.treeListEvent.OptionsBehavior.AutoNodeHeight = false;
            this.treeListEvent.OptionsBehavior.AutoSelectAllInEditor = false;
            this.treeListEvent.OptionsBehavior.CloseEditorOnLostFocus = false;
            this.treeListEvent.OptionsBehavior.Editable = false;
            this.treeListEvent.OptionsBehavior.KeepSelectedOnClick = false;
            this.treeListEvent.OptionsBehavior.ResizeNodes = false;
            this.treeListEvent.OptionsBehavior.SmartMouseHover = false;
            this.treeListEvent.OptionsMenu.EnableFooterMenu = false;
            this.treeListEvent.OptionsPrint.PrintHorzLines = false;
            this.treeListEvent.OptionsPrint.PrintVertLines = false;
            this.treeListEvent.OptionsPrint.UsePrintStyles = true;
            this.treeListEvent.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.treeListEvent.OptionsView.AutoWidth = false;
            this.treeListEvent.OptionsView.ShowCheckBoxes = true;
            this.treeListEvent.OptionsView.ShowColumns = false;
            this.treeListEvent.OptionsView.ShowFocusedFrame = false;
            this.treeListEvent.OptionsView.ShowHorzLines = false;
            this.treeListEvent.OptionsView.ShowIndicator = false;
            this.treeListEvent.OptionsView.ShowVertLines = false;
            this.treeListEvent.Size = new System.Drawing.Size(689, 349);
            this.treeListEvent.TabIndex = 22;
            this.treeListEvent.AfterCheckNode += new DevExpress.XtraTreeList.NodeEventHandler(this.treeListEvent_AfterCheckNode);
            // 
            // treeListColumn1
            // 
            this.treeListColumn1.AllNodesSummary = true;
            this.treeListColumn1.Caption = "Registry Keys";
            this.treeListColumn1.FieldName = "Key";
            this.treeListColumn1.MinWidth = 36;
            this.treeListColumn1.Name = "treeListColumn1";
            this.treeListColumn1.SummaryFooter = DevExpress.XtraTreeList.SummaryItemType.Count;
            this.treeListColumn1.SummaryFooterStrFormat = "Count keys = {0}";
            this.treeListColumn1.Visible = true;
            this.treeListColumn1.VisibleIndex = 0;
            // 
            // checkBoxSelectAll
            // 
            this.checkBoxSelectAll.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.checkBoxSelectAll.AutoSize = true;
            this.checkBoxSelectAll.Location = new System.Drawing.Point(19, 357);
            this.checkBoxSelectAll.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.checkBoxSelectAll.Name = "checkBoxSelectAll";
            this.checkBoxSelectAll.Size = new System.Drawing.Size(60, 22);
            this.checkBoxSelectAll.TabIndex = 5;
            this.checkBoxSelectAll.Text = "全选";
            this.checkBoxSelectAll.UseVisualStyleBackColor = true;
            // 
            // buttonOK
            // 
            this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.buttonOK.Location = new System.Drawing.Point(265, 421);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(87, 27);
            this.buttonOK.TabIndex = 1;
            this.buttonOK.Text = "确定";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // buttonCancel
            // 
            this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.buttonCancel.Location = new System.Drawing.Point(406, 421);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new System.Drawing.Size(87, 27);
            this.buttonCancel.TabIndex = 2;
            this.buttonCancel.Text = "取消";
            this.buttonCancel.UseVisualStyleBackColor = true;
            // 
            // EventChooserSimpleForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(748, 471);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonOK);
            this.Controls.Add(this.groupBox1);
            this.Name = "EventChooserSimpleForm";
            this.Text = "智能预判事件选择";
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListEvent)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraTreeList.TreeList treeListEvent;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn1;
        protected CheckBox checkBoxSelectAll;
        private System.Windows.Forms.Button buttonOK;
        private System.Windows.Forms.Button buttonCancel;
    }
}