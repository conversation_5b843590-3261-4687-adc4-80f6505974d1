﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraTreeList.Columns;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Func.CoverageCheck;
using DevExpress.XtraTreeList;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class PKByPeriodForm : MinCloseForm
    {
        private PKPeriodCondition pkCond;

        private Dictionary<AreaBase, CompareResultVillage> areaCPResultDic;

        private Dictionary<AreaBase, List<AreaBase>> rootLeafDic;

        private ZTAreaArchiveLayer layer
        {
            get
            {
                ZTAreaArchiveLayer layerTmp = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(ZTAreaArchiveLayer)) as ZTAreaArchiveLayer;
                if (layerTmp == null)
                {
                    layerTmp = new ZTAreaArchiveLayer();
                    MainModel.MainForm.GetMapForm().AddLayerBase(layerTmp);
                }
                return layerTmp;
            }
        }

        private AreaGridLayer gridLayer
        {
            get
            {
                AreaGridLayer layerTmp = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(AreaGridLayer)) as AreaGridLayer;
                if (layerTmp == null)
                {
                    layerTmp = new AreaGridLayer();
                    MainModel.MainForm.GetMapForm().AddLayerBase(layerTmp);
                }

                return layerTmp;
            }
        }

        public PKByPeriodForm()
        {
            InitializeComponent();

            areaCPResultDic = new Dictionary<AreaBase, CompareResultVillage>();

            rootLeafDic = new Dictionary<AreaBase, List<AreaBase>>();

            lbxLegend.DrawItem += new DrawItemEventHandler(lbxGis_DrawItem);
        }

        public void FillData(PKPeriodCondition pkCond, Dictionary<AreaBase, CompareResultVillage> regionCPResultDic)
        {
            this.pkCond = pkCond;
            areaCPResultDic.Clear();

            rootLeafDic = ArchiveSettingManager.GetInstance().Condition.VillageCondition.RootLeafDic;

            foreach (AreaBase village in regionCPResultDic.Keys)
            {
                AreaBase area = village;
                do
                {
                    CompareResultVillage result;
                    if (!areaCPResultDic.TryGetValue(area, out result))
                    {
                        result = new CompareResultVillage(area);
                        areaCPResultDic[area] = result;
                    }
                    result.MergeResult(regionCPResultDic[village]);

                    area = area.ParentArea;
                } while (area != null);
            }

            MasterCom.Util.WaitBox.Show("正在填充数据...", visualizeReport);
        }

        private void visualizeReport()
        {
            try
            {
                treeList.BeginUpdate();
                treeList.Nodes.Clear();
                makeReportColumn(treeList);
                foreach (AreaBase area in rootLeafDic.Keys)
                {
                    if (area.SubAreas == null ||
                        !areaCPResultDic.ContainsKey(area))
                    {
                        continue;
                    }
                    layer.Areas.AddRange(rootLeafDic[area]);
                    appendTreeNode(areaCPResultDic[area], null);
                }
                treeList.EndUpdate();

                cbxRank.Items.Clear();
                foreach (AreaRank rank in ZTAreaManager.Instance.Ranks)
                {
                    cbxRank.Items.Add(rank);
                }
                cbxRank.SelectedItem = ZTAreaManager.Instance.LowestRank;

                refreshLegend();
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(1000);
                MasterCom.Util.WaitBox.Close();
            }
        }

        private void lbxGis_DrawItem(object sender, System.Windows.Forms.DrawItemEventArgs e)
        {
            System.Windows.Forms.ListBox listBoxLegend = sender as System.Windows.Forms.ListBox;
            if (e.Index < 0)
            {
                return;
            }
            object item = listBoxLegend.Items[e.Index];
            string text = "";
            if (item is TextColorRange)
            {
                e.Graphics.FillRectangle(new SolidBrush((item as TextColorRange).color), e.Bounds.X, e.Bounds.Y, 16, 16);
                text = (item as TextColorRange).description;
                e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Black, e.Bounds.X + 20, e.Bounds.Y);
            }
            else if (item is string)
            {
                text = item.ToString();
                e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Red, e.Bounds.X, e.Bounds.Y);
            }
        }

        private void makeReportColumn(DevExpress.XtraTreeList.TreeList treeList)
        {
            treeList.Columns.Clear();
            DevExpress.XtraTreeList.Columns.TreeListColumn colFixed = treeList.Columns.Add();
            colFixed.Caption = "区域名称";
            colFixed.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colFixed.OptionsColumn.AllowEdit = false;
            colFixed.OptionsColumn.AllowMoveToCustomizationForm = false;
            colFixed.OptionsColumn.ReadOnly = true;
            colFixed.Visible = true;
            colFixed.Width = 160;
            colFixed.Fixed = FixedStyle.Left;

            colFixed = treeList.Columns.Add();
            colFixed.Caption = "行政级别";
            colFixed.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colFixed.OptionsColumn.AllowEdit = false;
            colFixed.OptionsColumn.AllowMoveToCustomizationForm = false;
            colFixed.OptionsColumn.ReadOnly = true;
            colFixed.Visible = true;
            colFixed.Fixed = FixedStyle.Left;

            DevExpress.XtraTreeList.Columns.TreeListColumn col = treeList.Columns.Add();
            col.Caption = "竞比结果";
            col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            col.OptionsColumn.AllowEdit = false;
            col.OptionsColumn.AllowMoveToCustomizationForm = false;
            col.OptionsColumn.ReadOnly = true;
            col.Visible = true;

            col = treeList.Columns.Add();
            col.Caption = pkCond.HostPeriod.ToString();
            col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            col.OptionsColumn.AllowEdit = false;
            col.OptionsColumn.AllowMoveToCustomizationForm = false;
            col.OptionsColumn.ReadOnly = true;
            col.Visible = true;

            col = treeList.Columns.Add();
            col.Caption = pkCond.GuestPeriod.ToString();
            col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            col.OptionsColumn.AllowEdit = false;
            col.OptionsColumn.AllowMoveToCustomizationForm = false;
            col.OptionsColumn.ReadOnly = true;
            col.Visible = true;
        }

        private void appendTreeNode(CompareResultVillage areaResult, TreeListNode parentNode)
        {
            PKAreaResult result = new PKAreaResult(areaResult);
            result.CalcValue(pkCond);
            TreeListNode node = treeList.AppendNode(
                new object[] { areaResult.Village.Name, areaResult.Village.RankName, result.color.description, getText(result.dHost), getText(result.dGuest) }, 
                parentNode);
            node.Tag = result;

            if (areaResult.Village.SubAreas == null) return;

            foreach (AreaBase sub in areaResult.Village.SubAreas)
            {
                if (areaCPResultDic.ContainsKey(sub))
                {
                    appendTreeNode(areaCPResultDic[sub], node);
                }
            }
        }

        private string getText(double dValue)
        {
            if (double.IsNaN(dValue))
            {
                return "-";
            }
            return Convert.ToString(dValue);
        }

        private void refreshLegend()
        {
            lbxLegend.Items.Clear();
            foreach (CPModeColorItem item in pkCond.ParamSelected.AlgorithmCfg.colorItemList)
            {
                lbxLegend.Items.Add(item.colorRange);
            }
            foreach (CPModeColorItem item in pkCond.ParamSelected.AlgorithmCfg.bothStandardList)
            {
                lbxLegend.Items.Add(item.colorRange);
            }
            foreach (TextColorRange item in pkCond.ParamSelected.AlgorithmCfg.specialColorList)
            {
                lbxLegend.Items.Add(item);
            }

            lbxLegend.Items.Add("\r\n");
            lbxLegend.Items.Add("\r\n");

            if(pkCond.ParamSelected.isLimit_A)
                lbxLegend.Items.Add(string.Format("主队有效范围：{0}", pkCond.ParamSelected.Range_A));
            if (pkCond.ParamSelected.isLimit_B)
                lbxLegend.Items.Add(string.Format("主队有效范围：{0}", pkCond.ParamSelected.Range_B));

            lbxLegend.Invalidate();
        }

        private void refreshGis()
        {
            AreaRank rank = cbxRank.SelectedItem as AreaRank;
            if (rank == null)
            {
                return;
            }
            Dictionary<AreaBase, Color> areaColorDic = new Dictionary<AreaBase, Color>();
            foreach (TreeListNode root in treeList.Nodes)
            {
                Color color = Color.Empty;
                getLeafNodeColor(root, rank, color, areaColorDic);
            }
            layer.AreaColorDic = areaColorDic;
            layer.Invalidate();
        }

        private void getLeafNodeColor(TreeListNode node, AreaRank rank, Color color, Dictionary<AreaBase, Color> areaColorDic)
        {
            PKAreaResult pkData = node.Tag as PKAreaResult;
            if (pkData.result.Village.Rank == rank)
            {
                color = pkData.color.color;
                areaColorDic[pkData.result.Village] = color;
            }
            if (node.HasChildren)
            {
                foreach (TreeListNode subNode in node.Nodes)
                {
                    getLeafNodeColor(subNode, rank, color, areaColorDic);
                }
            }
            else
            {
                areaColorDic[pkData.result.Village] = color;
            }
        }

        private void cbxRank_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshGis();
        }

        private void treeList_NodeCellStyle(object sender, GetCustomNodeCellStyleEventArgs e)
        {
            if (e.Node == null || e.Node.Tag == null)
            {
                return;
            }
            if (e.Column.Caption != "竞比结果")
            {
                return;
            }
            PKAreaResult pkResult = e.Node.Tag as PKAreaResult;
            if (pkResult == null)
            {
                return;
            }
            e.Appearance.BackColor = pkResult.color.color;
        }

        private void treeList_DoubleClick(object sender, EventArgs e)
        {
            System.Windows.Forms.MouseEventArgs me = e as System.Windows.Forms.MouseEventArgs;
            TreeListHitInfo info = treeList.CalcHitInfo(me.Location);

            if (info.Node == null)
            {
                return;
            }
            refreshGrid(info.Node);
            List<AreaBase> selAreas = new List<AreaBase>();
            layer.AreaColorDic.Clear();
            getLeafNode(info.Node, selAreas);
            layer.SelectedAreas = selAreas;
            DbRect bounds;

            double maxX = double.MinValue;
            double maxY = double.MinValue;
            double minX = double.MaxValue;
            double minY = double.MaxValue;
            foreach (AreaBase area in selAreas)
            {
                maxX = Math.Max(maxX, area.Bounds.x2);
                maxY = Math.Max(maxY, area.Bounds.y2);
                minY = Math.Min(minY, area.Bounds.y1);
                minX = Math.Min(minX, area.Bounds.x1);
            }
            bounds = new DbRect(minX, minY, maxX, maxY);
            MainModel.MainForm.GetMapForm().GoToView(bounds);
        }

        private void getLeafNode(TreeListNode node, List<AreaBase> areas)
        {
            if (node.HasChildren)
            {
                foreach (TreeListNode subNode in node.Nodes)
                {
                    getLeafNode(subNode, areas);
                }
            }
            else
            {
                PKAreaResult result = node.Tag as PKAreaResult;
                areas.Add(result.result.Village);
                layer.AreaColorDic[result.result.Village] = result.color.color;
            }
        }

        private void refreshGrid(TreeListNode node)
        {
            if (node == null) return;

            PKAreaResult result = node.Tag as PKAreaResult;
            gridLayer.HostGrids = result.result.HostGirds;
            gridLayer.GuestGrids = result.result.GuestGrids;
        }

        private void spinEditOffsetX_ValueChanged(object sender, EventArgs e)
        {
            gridLayer.XOffset = (int)spinEditOffsetX.Value;
            gridLayer.Invalidate();
        }

        private void spinEditOffsetY_ValueChanged(object sender, EventArgs e)
        {
            gridLayer.YOffset = (int)spinEditOffsetY.Value;
            gridLayer.Invalidate();
        }
    }

    public class PKAreaResult
    {

        public TextColorRange color { get; set; }

        public double dHost { get; set; }

        public double dGuest { get; set; }

        public CompareResultVillage result { get; set; }

        public PKAreaResult(CompareResultVillage result)
        {
            this.result = result;
        }

        public void CalcValue(PKPeriodCondition pkCond)
        {
            dHost = result.HostUnit.DataHub.CalcValueByFormula(pkCond.ParamSelected.formula_A);
            dGuest = result.GuestUnit.DataHub.CalcValueByFormula(pkCond.ParamSelected.formula_B);

            double dh = dHost;
            double dg = dGuest;
            color = pkCond.ParamSelected.GetTextColorRange(ref dh, ref dg);
        }
    }
}
