﻿using System;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.BaseInfo;

namespace MasterCom.RAMS.Model
{
    public partial class LoginManagerDlg : BaseDialog
    {
        public LoginManagerDlg()
        {
            InitializeComponent();

            toolTip.SetToolTip(numPwdUpdateDays, "值小于1时，密码不会过期");
            toolTip.SetToolTip(numPWPromptDays, "值小于1时，不提示更新密码");
            toolTip.SetToolTip(numFailLockSeconds, "值小于1时，不自动解锁");
            toolTip.SetToolTip(numNoOperateExitMins, "值小于10时，不自动退出/锁定");
            toolTip.SetToolTip(numResetHour, "值小于1时，不自动清零");
            toolTip.SetToolTip(numLockUnUseDays, "值小于1时，不自动锁定");
            toolTip.SetToolTip(numDelUnLoadDays, "值小于30时，不自动删除");
        }

        /// <summary>
        /// 更新数据库数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOK_Click(object sender, EventArgs e)
        {
            //更新数据到数据库tb_cfg_static_user_constraint
            LoginValues loginValues = new LoginValues();
            loginValues.PwdSuperLimit = chkPwdSuperLimit.Checked;
            loginValues.PwdMinCharCount = (int)numPwdMinCharCount.Value;
            loginValues.PwdMinCharKindCount = (int)numPwdMinCharKindCount.Value;
            loginValues.PwdNotSameRecentTimes = (int)numPwdNotSameRecentTimes.Value;

            loginValues.StaleDays = (int)numPwdUpdateDays.Value;
            loginValues.TryTimes = (int)numFailLockTimes.Value;
            loginValues.AlarmDays = (int)numPWPromptDays.Value;
            loginValues.LockSeconds = (int)numFailLockSeconds.Value;
            loginValues.ExitMins = (int)numNoOperateExitMins.Value;
            loginValues.NoOperationType = radioBtnLogOut.Checked ? LoginValues.NoOpsType.LogOut : LoginValues.NoOpsType.Lock;
            loginValues.HourOfResetTryTimes = (int)numResetHour.Value;
            loginValues.LockUnUseDays = (int)numLockUnUseDays.Value;
            loginValues.DelUnLoadDays = (int)numDelUnLoadDays.Value;

            LoginManagerDBUpdate fLoginDB = new LoginManagerDBUpdate(loginValues);
            fLoginDB.Query();

            if (fLoginDB.Res == 1)
            {
                mainModel.LoginManageCfg = loginValues;
                MessageBox.Show("修改登录管理设置成功");
            }
            else
            {
                MessageBox.Show("修改登录管理设置失败");
                selectLoginValues();
            }
        }

        private void FuncManager_Load(object sender, EventArgs e)
        {
            selectLoginValues();
        }

        /// <summary>
        /// 设置界面数据为数据库查询结果
        /// </summary>
        private void selectLoginValues()
        {
            try
            {
                LoginValues loginValues = mainModel.LoginManageCfg;
                chkPwdSuperLimit.Checked = loginValues.PwdSuperLimit;
                numPwdMinCharCount.Value = loginValues.PwdMinCharCount;
                numPwdMinCharKindCount.Value = loginValues.PwdMinCharKindCount;
                numPwdNotSameRecentTimes.Value = loginValues.PwdNotSameRecentTimes;

                numPwdUpdateDays.Value = loginValues.StaleDays;
                numFailLockTimes.Value = loginValues.TryTimes;
                numPWPromptDays.Value = loginValues.AlarmDays;
                numFailLockSeconds.Value = loginValues.LockSeconds;
                numNoOperateExitMins.Value = loginValues.ExitMins;
                radioBtnLogOut.Checked = (loginValues.NoOperationType == LoginValues.NoOpsType.LogOut);
                radioBtnLock.Checked = !radioBtnLogOut.Checked;
                numResetHour.Value = loginValues.HourOfResetTryTimes;
                numLockUnUseDays.Value = loginValues.LockUnUseDays;
                numDelUnLoadDays.Value = loginValues.DelUnLoadDays;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }

    /// <summary>
    /// 登录管理相关设置
    /// </summary>
    public class LoginValues
    {
        public LoginValues()
        {
            StaleDays = 90;
            TryTimes = 5;
            AlarmDays = 10;
            LockSeconds = -1;
            ExitMins = 9;
            NoOperationType = NoOpsType.LogOut;
            HourOfResetTryTimes = 0;
            LockUnUseDays = 60;
            DelUnLoadDays = 90;
            PwdMinCharCount = 8;
            PwdMinCharKindCount = 3;
            PwdNotSameRecentTimes = 5;
            PwdSuperLimit = false;
        }

        //密码有效天数
        public int StaleDays { get; set; }
        //密码连续输错最多X次
        public int TryTimes { get; set; }
        //密码即将过期前X天开始提醒
        public int AlarmDays { get; set; }
        //被锁定的账号X秒后自动解锁
        public int LockSeconds { get; set; }
        //系统X分钟无用户操作后自动退出/锁定
        public int ExitMins { get; set; }
        //系统无操作后客户端行为类型
        public NoOpsType NoOperationType { get; set; }
        //每天X时整,将状态正常账号的密码连续输错次数清零
        public int HourOfResetTryTimes { get; set; }
        //自动锁定X天未登陆的账号
        public int LockUnUseDays { get; set; }
        //自动删除X天未登陆的账号
        public int DelUnLoadDays { get; set; }
        //密码需至少有 x 位字符
        public int PwdMinCharCount { get; set; }
        //密码至少包含大写字母、小写字母、数字和特殊符号4类中的 y 类
        public int PwdMinCharKindCount { get; set; }
        //最近x 次以内不得设置相同的密码
        public int PwdNotSameRecentTimes { get; set; }
        //是否启用密码强度超强限制（密码不得包含用户名、年月日、手机号、3位以上（含三位）连续字符或重复字符、3位以上（含3位）键盘排序连续字符）
        public bool PwdSuperLimit { get; set; }

        public enum NoOpsType
        {
            //退出
            LogOut,
            //锁定
            Lock
        };
    }
}
