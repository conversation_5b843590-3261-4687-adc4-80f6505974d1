<?xml version="1.0"?>
<Configs>
  <Config name="FusionColumns">
    <Item name="ColumnType" typeName="IDictionary">
      <Item typeName="IDictionary" key="告警">
        <Item typeName="String" key="Name">告警</Item>
        <Item typeName="IDictionary" key="FusionDataColumns">
          <Item typeName="IDictionary" key="16843009">
            <Item typeName="String" key="ColumnName">网元类型</Item>
            <Item typeName="Int32" key="ColumnId">16843009</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="16843010">
            <Item typeName="String" key="ColumnName">告警源</Item>
            <Item typeName="Int32" key="ColumnId">16843010</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="16843011">
            <Item typeName="String" key="ColumnName">MO对象</Item>
            <Item typeName="Int32" key="ColumnId">16843011</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="16843012">
            <Item typeName="String" key="ColumnName">定位信息</Item>
            <Item typeName="Int32" key="ColumnId">16843012</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="16843013">
            <Item typeName="String" key="ColumnName">确认时间</Item>
            <Item typeName="Int32" key="ColumnId">16843013</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="16843014">
            <Item typeName="String" key="ColumnName">确认用户</Item>
            <Item typeName="Int32" key="ColumnId">16843014</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="16843015">
            <Item typeName="String" key="ColumnName">所属子网</Item>
            <Item typeName="Int32" key="ColumnId">16843015</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="16843016">
            <Item typeName="String" key="ColumnName">网元名称</Item>
            <Item typeName="Int32" key="ColumnId">16843016</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary" key="性能">
        <Item typeName="String" key="Name">性能</Item>
        <Item typeName="IDictionary" key="FusionDataColumns">
          <Item typeName="IDictionary" key="83951873">
            <Item typeName="String" key="ColumnName">空口上行业务字节数</Item>
            <Item typeName="Int32" key="ColumnId">83951873</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="83951874">
            <Item typeName="String" key="ColumnName">空口下行业务字节数</Item>
            <Item typeName="Int32" key="ColumnId">83951874</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="83951875">
            <Item typeName="String" key="ColumnName">RRC连接平均数</Item>
            <Item typeName="Int32" key="ColumnId">83951875</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951876">
            <Item typeName="String" key="ColumnName">RRC连接最大数</Item>
            <Item typeName="Int32" key="ColumnId">83951876</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="83951877">
            <Item typeName="String" key="ColumnName">eNB接收干扰功率平均值</Item>
            <Item typeName="Int32" key="ColumnId">83951877</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951878">
            <Item typeName="String" key="ColumnName">上行PRB平均利用率</Item>
            <Item typeName="Int32" key="ColumnId">83951878</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951879">
            <Item typeName="String" key="ColumnName">下行PRB平均利用率</Item>
            <Item typeName="Int32" key="ColumnId">83951879</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951880">
            <Item typeName="String" key="ColumnName">无线利用率</Item>
            <Item typeName="Int32" key="ColumnId">83951880</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="83951881">
            <Item typeName="String" key="ColumnName">RRC连接建立请求次数</Item>
            <Item typeName="Int32" key="ColumnId">83951881</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951882">
            <Item typeName="String" key="ColumnName">RRC连接建立成功次数</Item>
            <Item typeName="Int32" key="ColumnId">83951882</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951883">
            <Item typeName="String" key="ColumnName">RRC连接建立成功率</Item>
            <Item typeName="Int32" key="ColumnId">83951883</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951884">
            <Item typeName="String" key="ColumnName">E-RAB建立请求数</Item>
            <Item typeName="Int32" key="ColumnId">83951884</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951885">
            <Item typeName="String" key="ColumnName">E-RAB建立成功数</Item>
            <Item typeName="Int32" key="ColumnId">83951885</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951886">
            <Item typeName="String" key="ColumnName">E-RAB建立成功率</Item>
            <Item typeName="Int32" key="ColumnId">83951886</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951887">
            <Item typeName="String" key="ColumnName">无线接通率</Item>
            <Item typeName="Int32" key="ColumnId">83951887</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="83951888">
            <Item typeName="String" key="ColumnName">eNB请求释放上下文数</Item>
            <Item typeName="Int32" key="ColumnId">83951888</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951889">
            <Item typeName="String" key="ColumnName">正常的eNB请求释放上下文数</Item>
            <Item typeName="Int32" key="ColumnId">83951889</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951890">
            <Item typeName="String" key="ColumnName">初始上下文建立成功次数</Item>
            <Item typeName="Int32" key="ColumnId">83951890</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="83951891">
            <Item typeName="String" key="ColumnName">无线掉线率</Item>
            <Item typeName="Int32" key="ColumnId">83951891</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary" key="参数">
        <Item typeName="String" key="Name">参数</Item>
        <Item typeName="IDictionary" key="FusionDataColumns">
          <Item typeName="IDictionary" key="33620225">
            <Item typeName="String" key="ColumnName">参考信号功率</Item>
            <Item typeName="Int32" key="ColumnId">33620225</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="33620226">
            <Item typeName="String" key="ColumnName">PDSCH采用均匀功率分配时的PA值</Item>
            <Item typeName="Int32" key="ColumnId">33620226</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="33620227">
            <Item typeName="String" key="ColumnName">PB</Item>
            <Item typeName="Int32" key="ColumnId">33620227</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="33620228">
            <Item typeName="String" key="ColumnName">小区偏移量</Item>
            <Item typeName="Int32" key="ColumnId">33620228</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="33620229">
            <Item typeName="String" key="ColumnName">小区偏置</Item>
            <Item typeName="Int32" key="ColumnId">33620229</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="33620230">
            <Item typeName="String" key="ColumnName">上下行业务子帧配置</Item>
            <Item typeName="Int32" key="ColumnId">33620230</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="33620231">
            <Item typeName="String" key="ColumnName">特殊子帧配置</Item>
            <Item typeName="Int32" key="ColumnId">33620231</Item>
            <Item typeName="Boolean" key="IsCheck">True</Item>
          </Item>
          <Item typeName="IDictionary" key="33620232">
            <Item typeName="String" key="ColumnName">同频 A3 offset</Item>
            <Item typeName="Int32" key="ColumnId">33620232</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="33620233">
            <Item typeName="String" key="ColumnName">同频 A3 Hysteresis</Item>
            <Item typeName="Int32" key="ColumnId">33620233</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="33620234">
            <Item typeName="String" key="ColumnName">同频 A3 Time-to-trigger</Item>
            <Item typeName="Int32" key="ColumnId">33620234</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
          <Item typeName="IDictionary" key="33620235">
            <Item typeName="String" key="ColumnName">异频A2门限（A2 Threshold)</Item>
            <Item typeName="Int32" key="ColumnId">33620235</Item>
            <Item typeName="Boolean" key="IsCheck">False</Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>