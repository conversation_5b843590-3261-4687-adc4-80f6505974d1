﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Func.EventBlock
{
    public class  SqlGetESEventInfoForBlock: DIYSQLBase
    {
        readonly string sql;
        readonly Dictionary<string, Event> evtDic;
        public SqlGetESEventInfoForBlock(MainModel mainModel, String sql, Dictionary<string, Event> evtDic)
            : base(mainModel)
        {
            this.sql = sql;
            this.evtDic = evtDic;
        }

        protected override string getSqlTextString()
        {
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    int ifileid = package.Content.GetParamInt();
                    int iseqid = package.Content.GetParamInt();
                    int itime = package.Content.GetParamInt();
                    int iEventID = package.Content.GetParamInt();
                    
                    string pretype_desc = package.Content.GetParamString();
                    string reason_desc = package.Content.GetParamString();
                    string solution_desc = package.Content.GetParamString();
                    string evtKey = String.Format("{0}:{1}:{2}:{3}", ifileid, itime, iEventID, iseqid);
                    Event evt;
                    if (evtDic.TryGetValue(evtKey,out evt))
                    {
                        evt.pretype_desc = pretype_desc;
                        evt.reason_desc = reason_desc;
                        evt.solution_desc = solution_desc;
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "SqlGetESEventInfoForBlock"; }
        }
    };
}
