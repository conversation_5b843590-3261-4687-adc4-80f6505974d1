﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.ES.Data;
using MasterCom.ES.Core;
using MasterCom.Util;

namespace MasterCom.ES.UI
{
    public partial class ReservSelectDlg : BaseFormStyle
    {
        public ReservSelectDlg()
        {
            InitializeComponent();
        }
        public void FreshResvTreeShow(ProcRoutine curSelProc)
        {
            DataGatherProxy proxy = DataGatherProxy.GetInstance();
            treeViewResvValue.Nodes.Clear();
            TreeNode pubNode = new TreeNode();
            treeViewResvValue.Nodes.Add(pubNode);
            pubNode.Tag = proxy.PublicResvStore;
            pubNode.Text = "流程内预存值";
            fillRsvItemsToNode(curSelProc.ReservStore, pubNode);

            pubNode = new TreeNode();
            treeViewResvValue.Nodes.Add(pubNode);
            pubNode.Tag = proxy.PublicResvStore;
            pubNode.Text = "公共预存值";
            fillRsvItemsToNode(proxy.PublicResvStore, pubNode);
            foreach (string inProcKey in ESEngine.GetInstance().inProcModuleDic.Keys)
            {
                ResvStore store = ESEngine.GetInstance().inProcModuleDic[inProcKey].ReservStore;
                TreeNode provNode = new TreeNode();
                provNode.Tag = store;
                provNode.Text = inProcKey;
                treeViewResvValue.Nodes.Add(provNode);
                fillRsvItemsToNode(store, provNode);
            }
        }
        private void fillRsvItemsToNode(ResvStore store, TreeNode tnode)
        {
            foreach (string str in store.ResvValueDic.Keys)
            {
                TreeNode nd = new TreeNode();
                nd.Tag = str;
                nd.Text = str;
                tnode.Nodes.Add(nd);
            }
            foreach (string str in store.ResvStringDic.Keys)
            {
                TreeNode nd = new TreeNode();
                nd.Tag = str;
                nd.Text = str + "(字符型)";
                tnode.Nodes.Add(nd);
            }
        }
        public string GetResultResvString()
        {
            if (treeViewResvValue.SelectedNode != null && treeViewResvValue.SelectedNode.Level == 1)
            {
                string parentNodeName = treeViewResvValue.SelectedNode.Parent.Text;
                if (parentNodeName.Equals("流程内预存值"))
                {
                    string key = treeViewResvValue.SelectedNode.Tag as string;
                    return "R[" + key + "]";
                }
                else
                {
                    string key = treeViewResvValue.SelectedNode.Tag as string;
                    return "R[" + parentNodeName + "," + key + "]";
                }
            }
            return null;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void treeViewResvValue_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            string str = GetResultResvString();
            if(str!=null)
            {
                this.DialogResult = DialogResult.OK;
            }
        }
    }
}