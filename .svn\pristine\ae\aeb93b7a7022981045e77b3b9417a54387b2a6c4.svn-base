﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTECellWrongDir : TDCellWrongDir
    {
        public LTECellWrongDir(LTECell cell)
            : base(cell)
        {
        }
        public new LTECell Cell
        {
            get { return (LTECell)cell; }
        }

        public override string CellName
        {
            get
            {
                return Cell.Name;
            }
        }

        public override int Direction
        {
            get
            {
                return Cell.Direction;
            }
        }


        public override double Latitude
        {
            get
            {
                return Cell.Latitude;
            }
        }

        public override double Longitude
        {
            get
            {
                return Cell.Longitude;
            }
        }

        public override int LAC
        {
            get
            {
                return Cell.TAC;
            }
        }

        public override int CI
        {
            get
            {
                return Cell.ECI;
            }
        }
        public override int FREQ
        {
            get { return Cell.EARFCN; }
        }

        public override int CPI
        {
            get { return Cell.PCI; }
        }
        public override TDCellWrongDir Clone()
        {
            TDCellWrongDir cellWrong = new LTECellWrongDir(Cell);
            cellWrong.cellWrongBatch = this.cellWrongBatch;
            cellWrong.resultFirstBatch = this.resultFirstBatch;
            cellWrong.resultSecondBatch = this.resultSecondBatch;

            return cellWrong;
        }

        public BackgroundResult ConvertToBackgroundResult(FileInfo file)
        {
            resultShow.CalcWrongDir();

            BackgroundResult bgResult = new BackgroundResult();
            bgResult.CellType = BackgroundCellType.LTE;
            bgResult.ProjectString = MasterCom.RAMS.BackgroundFunc.BackgroundFuncBaseSetting.GetInstance().projectType;
            if (file != null)
            {
                bgResult.FileID = file.ID;
                bgResult.FileName = file.Name;
            }

            bgResult.LAC = LAC;
            bgResult.CI = CI;
            bgResult.BCCH = FREQ;
            bgResult.BSIC = CPI;
            bgResult.ISTime = resultShow.istime;
            bgResult.IETime = resultShow.ietime;
            bgResult.LongitudeMid = Longitude;
            bgResult.LatitudeMid = Latitude;
            bgResult.SampleCount = WrongTestPointCount;
            bgResult.AddImageValue(resultShow.GoodTestPointCount);
            bgResult.AddImageValue(this.Direction);
            bgResult.AddImageValue(resultShow.WrongDirMax);
            bgResult.AddImageValue(resultShow.WrongDirMin);

            return bgResult;
        }
    }
}
