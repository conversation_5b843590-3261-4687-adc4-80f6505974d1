﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Func
{
    public static class StreetInjectHelper
    {
        //public List<StreetInjectInfo> TotalResultList { get; set; } = new List<StreetInjectInfo>();
        private static GridMatrix<InjectGridUnit> gridMatrix = null;
        private static bool useWaitBox = false;

        public static bool Init(GridMatrix<InjectGridUnit> gridMatrix, bool useWaitBox)
        {
            if (gridMatrix != null)
            {
                StreetInjectHelper.gridMatrix = gridMatrix;
                StreetInjectHelper.useWaitBox = useWaitBox;
                return true;
            }
            return false;
        }

        #region 计算道路渗透率
        public static List<StreetInjectInfo> ParseStreetsOfTable(MapWinGIS.Shapefile shapeFile, MTPolygon mapOp, string nameCol, string idCol, string regionName, ref Dictionary<InjectGridUnit, int> coveredGridDic)
        {
            var totalResultList = new List<StreetInjectInfo>();
            if (shapeFile == null)
            {
                return totalResultList;
            }
            if (coveredGridDic == null)
            {
                coveredGridDic = new Dictionary<InjectGridUnit, int>();
            }
            int streetCount = shapeFile.NumShapes;
            int nmFldIdx = 0;
            int idIndex = 1;
            bool ignoreIdCol = string.IsNullOrEmpty(idCol);
            getIdx(shapeFile, nameCol, idCol, ref nmFldIdx, ref idIndex, ignoreIdCol);

            string shpFileName = System.IO.Path.GetFileNameWithoutExtension(shapeFile.Filename);
            for (int dx = 0; dx < streetCount; dx++)
            {
                if (useWaitBox)
                {
                    WaitBox.ProgressPercent = (int)(100.0 * dx / streetCount);
                }
                MapWinGIS.Shape street = shapeFile.get_Shape(dx);
                if (street == null || !(mapOp.CheckStreetInRegion(street)))
                {
                    continue;
                }
                StreetInjectInfo ijinfo = initStreetInjectInfo(shapeFile, regionName, nmFldIdx, idIndex, ignoreIdCol, shpFileName, dx);

                MapWinGIS.Shape roadMultiCurv = street;
                int coveredCount = 0;
                int uncoveredCount = 0;
                for (int part = 0; part < roadMultiCurv.NumParts; part++)
                {
                    List<DbPoint> pnts = ShapeHelper.GetPartShapePoints(roadMultiCurv, part);
                    if (pnts == null)
                    {
                        continue;
                    }
                    DbPoint[] pts = pnts.ToArray();
                    CovSegmentGenerator csg = new CovSegmentGenerator();

                    #region 计算渗透
                    calculateInject(mapOp, coveredGridDic, ref coveredCount, ref uncoveredCount, pts, csg);
                    #endregion
                    ijinfo.AddToCovSeg(csg.ToCovSegList());
                }
                ijinfo.countCovered = coveredCount;
                ijinfo.countUnCovered = uncoveredCount;
                ijinfo.streetCurv = roadMultiCurv;
                ijinfo.PrepareDoCalc();
                totalResultList.Add(ijinfo);
            }
            return totalResultList;
        }

        private static void getIdx(MapWinGIS.Shapefile shapeFile, string nameCol, string idCol
            , ref int nmFldIdx, ref int idIndex, bool ignoreIdCol)
        {
            int numFields = shapeFile.NumFields;
            for (int x = 0; x < numFields; x++)
            {
                MapWinGIS.Field field = shapeFile.get_Field(x);
                if (field.Name.Equals(nameCol))
                {
                    nmFldIdx = x;
                    if (ignoreIdCol)
                    {
                        break;
                    }
                }
                if (!ignoreIdCol && field.Name.Equals(idCol))
                {
                    idIndex = x;
                }
            }
        }

        private static StreetInjectInfo initStreetInjectInfo(MapWinGIS.Shapefile shapeFile, string regionName
            , int nmFldIdx, int idIndex, bool ignoreIdCol, string shpFileName, int dx)
        {
            string streetName = "未命名";
            object nmObj = shapeFile.get_CellValue(nmFldIdx, dx);
            if (nmObj != null)
            {
                string tempName = nmObj.ToString().Trim();
                streetName = string.IsNullOrEmpty(tempName) ? streetName : tempName;
            }
            object idObj = null;
            if (!ignoreIdCol)
            {
                idObj = shapeFile.get_CellValue(idIndex, dx);
            }

            StreetInjectInfo ijinfo = new StreetInjectInfo();
            ijinfo.StreetName = streetName;
            ijinfo.AreaName = regionName;
            ijinfo.StreetTableName = shpFileName;
            if (idObj != null)
            {
                ijinfo.StreetIdList.Add(int.Parse(idObj.ToString()));
            }

            return ijinfo;
        }

        private static void calculateInject(MTPolygon mapOp, Dictionary<InjectGridUnit, int> coveredGridDic, ref int coveredCount, ref int uncoveredCount, DbPoint[] pts, CovSegmentGenerator csg)
        {
            for (int i = 0; i < pts.Length - 1; i++)
            {
                LineInfo lineInfo = new LineInfo();
                if (!lineInfo.InitLineInfo(i, pts))
                {
                    continue;
                }

                #region X跨度大
                if (lineInfo.xStepNum >= lineInfo.yStepNum)//X的跨度大些
                {//from小经度to大经度
                    dealXStep(mapOp, coveredGridDic, ref coveredCount, ref uncoveredCount, csg, lineInfo);
                }
                #endregion
                #region Y跨度大
                else //Y的跨度大些
                {
                    dealYStep(mapOp, coveredGridDic, ref coveredCount, ref uncoveredCount, csg, lineInfo);
                }
                #endregion
            }
        }

        private static void dealXStep(MTPolygon mapOp, Dictionary<InjectGridUnit, int> coveredGridDic, ref int coveredCount, ref int uncoveredCount, CovSegmentGenerator csg, LineInfo lineInfo)
        {
            InjectParamInfo info = new InjectParamInfo(lineInfo.dirXbY);
            info.InitInjectParamInfo(lineInfo.ptStart, lineInfo.ptEnd, lineInfo.ptStart.x, lineInfo.ptStart.y, lineInfo.ptEnd.x, lineInfo.ptEnd.y);

            bool greater = false;
            for (int p = 0; p < lineInfo.xStepNum + 1; p++)//分析的点数为步长数+1
            {
                if (greater)
                {
                    break;
                }
                info.InitXStep(p, CD.ATOM_SPAN_LONG, lineInfo.xGap, lineInfo.yGap, lineInfo.xStepNum, ref greater);

                dealPoint(mapOp, coveredGridDic, ref coveredCount, ref uncoveredCount, csg, lineInfo.lineTokenId, info);
            }
        }

        private static void dealYStep(MTPolygon mapOp, Dictionary<InjectGridUnit, int> coveredGridDic, ref int coveredCount, ref int uncoveredCount, CovSegmentGenerator csg, LineInfo lineInfo)
        {
            InjectParamInfo info = new InjectParamInfo(lineInfo.dirXbY);
            info.InitInjectParamInfo(lineInfo.ptStart, lineInfo.ptEnd, lineInfo.ptStart.y, lineInfo.ptStart.x, lineInfo.ptEnd.y, lineInfo.ptEnd.x);

            bool greater = false;
            for (int p = 0; p < lineInfo.yStepNum + 1; p++)//分析的点数为步长数+1
            {
                if (greater)
                {
                    break;
                }
                info.InitYStep(p, CD.ATOM_SPAN_LAT, lineInfo.xGap, lineInfo.yGap, lineInfo.yStepNum, ref greater);

                dealPoint(mapOp, coveredGridDic, ref coveredCount, ref uncoveredCount, csg, lineInfo.lineTokenId, info);
            }
        }

        private static void dealPoint(MTPolygon mapOp, Dictionary<InjectGridUnit, int> coveredGridDic, ref int coveredCount, ref int uncoveredCount, CovSegmentGenerator csg, int lineTokenId, InjectParamInfo info)
        {
            if (mapOp.CheckPointInRegion(info.xx, info.yy))
            {
                info.GetIndexOfDefaultSizeGrid();

                InjectGridUnit cu = gridMatrix[info.RAt, info.CAt];
                if (cu != null && cu.repeatCount > 0)
                {
                    coveredCount++;
                    if (!coveredGridDic.ContainsKey(cu))
                    {
                        coveredGridDic.Add(cu, 1);
                    }
                    csg.AddPoint(info.xx, info.yy, true, true, lineTokenId, cu.repeatCount);
                }
                else
                {
                    addOtherGrid(coveredGridDic, ref coveredCount, ref uncoveredCount, csg, lineTokenId, ref cu, info);
                }
            }
            else
            {
                csg.AddPoint(info.xx, info.yy, false, false, lineTokenId, 0);
            }
        }

        private static void addOtherGrid(Dictionary<InjectGridUnit, int> coveredGridDic, ref int coveredCount, ref int uncoveredCount, CovSegmentGenerator csg, int lineTokenId, ref InjectGridUnit cu, InjectParamInfo info)
        {
            int repeatCount = 0;
            if (findNear(info.RAt, info.CAt, ref repeatCount, info.DirXbY, info.Flag, info.RAt_Near, info.CAt_Near))
            {
                coveredCount++;
                if (cu == null)
                {
                    gridMatrix[info.RAt, info.CAt] = new InjectGridUnit();
                    cu = gridMatrix[info.RAt, info.CAt];
                    if (!coveredGridDic.ContainsKey(cu))
                    {
                        coveredGridDic.Add(cu, 1);
                    }
                }
                csg.AddPoint(info.xx, info.yy, true, true, lineTokenId, repeatCount);
            }
            else//周边均没有采样点
            {
                uncoveredCount++;
                csg.AddPoint(info.xx, info.yy, false, true, lineTokenId, 0);
            }
        }

        private static bool findNear(int rAt, int cAt, ref int repeatCountRet, float dirXbY, int yFlag
            , int rAt_Near, int cAt_Near)
        {
            int maxRepeatCt = 0;
            try
            {
                if (dirXbY >= 3)//左右的，栅格上下找（保持列，行加减1)
                {
                    maxRepeatCt = setMaxRepeatCt(rAt + 1, cAt, rAt - 1, cAt, maxRepeatCt);
                }
                else if (dirXbY < 0.33)
                {//上下的，栅格左右找（保持行，列加减1)
                    maxRepeatCt = setMaxRepeatCt(rAt, cAt - 1, rAt, cAt + 1, maxRepeatCt);
                }
                else//对角方向的
                {
                    if (yFlag > 0)
                    {
                        //左下到右上，栅格左上和栅格右下找
                        maxRepeatCt = setMaxRepeatCt(rAt + 1, cAt - 1, rAt - 1, cAt + 1, maxRepeatCt);

                        //避免对角问题
                        //左下，右上
                        maxRepeatCt = setMaxRepeatCt(rAt_Near, cAt, rAt, cAt_Near, maxRepeatCt);
                    }
                    else
                    {
                        //左上到右下，栅格左下和栅格右上找
                        maxRepeatCt = setMaxRepeatCt(rAt - 1, cAt - 1, rAt + 1, cAt + 1, maxRepeatCt);

                        //避免对角问题
                        //左下，右上
                        maxRepeatCt = setMaxRepeatCt(rAt_Near, cAt, rAt, cAt_Near, maxRepeatCt);
                    }
                }
                repeatCountRet = maxRepeatCt;
            }
            catch
            {
                //
            }
            return maxRepeatCt > 0;
        }

        private static int setMaxRepeatCt(int fstRow, int fstCol, int secRow, int secCol, int maxRepeatCt)
        {
            if (gridMatrix[fstRow, fstCol] != null)
            {
                maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[fstRow, fstCol].repeatCount);
            }
            if (gridMatrix[secRow, secCol] != null)
            {
                maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[secRow, secCol].repeatCount);
            }
            return maxRepeatCt;
        }

        class LineInfo
        {
            public int lineTokenId { get; set; }
            public DbPoint ptStart { get; set; }
            public DbPoint ptEnd { get; set; }
            public double xGap { get; set; }
            public double yGap { get; set; }
            public int xStepNum { get; set; }
            public int yStepNum { get; set; }
            public float dirXbY { get; set; }

            public bool InitLineInfo(int i, DbPoint[] pts)
            {
                lineTokenId = i;
                ptStart = pts[i];
                ptEnd = pts[i + 1];
                xGap = Math.Abs(ptEnd.x - ptStart.x);
                yGap = Math.Abs(ptEnd.y - ptStart.y);
                if (xGap == 0 && yGap == 0)
                {
                    return false;
                }

                xStepNum = (int)Math.Ceiling(xGap / CD.ATOM_SPAN_LONG);//跨2.2个栅格应分析3个栅格，向上取整
                yStepNum = (int)Math.Ceiling(yGap / CD.ATOM_SPAN_LAT);//向上取整
                if (xStepNum == 1 && yStepNum == 1)
                {
                    if (xGap >= yGap)
                    {
                        xStepNum = 2;
                    }
                    else
                    {
                        yStepNum = 2;
                    }
                }
                dirXbY = yStepNum > 0 ? (float)(xStepNum) / yStepNum : 9999;

                return true;
            }
        }

        class InjectParamInfo
        {
            public float DirXbY { get; set; }
            public int Flag { get; set; }
            public double yy { get; set; }
            public double xx { get; set; }
            public int RAt { get; set; }
            public int CAt { get; set; }
            public int RAt_Near { get; set; }
            public int CAt_Near { get; set; }

            double fromXX = 0;
            double fromYY = 0;
            double toXX = 0;
            double toYY = 0;

            public InjectParamInfo(float dirXbY)
            {
                DirXbY = dirXbY;
            }

            public void InitInjectParamInfo(DbPoint ptStart, DbPoint ptEnd, double sData1, double sData2, double eData1, double eData2)
            {
                if (sData1 <= eData1)
                {
                    fromXX = ptStart.x;
                    fromYY = ptStart.y;
                    toXX = ptEnd.x;
                    toYY = ptEnd.y;
                    if (sData2 >= eData2)
                    {
                        Flag = -1;
                    }
                    else
                    {
                        Flag = 1;
                    }
                }
                else
                {
                    fromXX = ptEnd.x;
                    fromYY = ptEnd.y;
                    toXX = ptStart.x;
                    toYY = ptStart.y;
                    if (eData2 < sData2)
                    {
                        Flag = 1;
                    }
                    else
                    {
                        Flag = -1;
                    }
                }
            }

            public void InitXStep(int p, double span, double xGap, double yGap, int stepNum, ref bool greater)
            {
                double xxMove = p * span;
                double yyMove = xxMove * yGap / xGap;

                xx = fromXX + xxMove;
                yy = fromYY + Flag * yyMove;

                if (p == stepNum || xx > toXX)
                {
                    xx = toXX;
                    yy = toYY;
                    greater = true;
                }
            }

            public void InitYStep(int p, double span, double xGap, double yGap, int stepNum, ref bool greater)
            {
                double yyMove = p * span;
                double xxMove = yyMove * xGap / yGap;

                yy = fromYY + yyMove;
                xx = fromXX + Flag * xxMove;

                if (p == stepNum || yy > toYY)
                {
                    xx = toXX;
                    yy = toYY;
                    greater = true;
                }
            }

            public void GetIndexOfDefaultSizeGrid()
            {
                int rAt, cAt;
                int rAt_Near, cAt_Near;
                GridHelper.GetIndexOfDefaultSizeGrid(xx, yy, out rAt, out cAt, out rAt_Near, out cAt_Near);
                RAt = rAt;
                CAt = cAt;
                RAt_Near = rAt_Near;
                CAt_Near = cAt_Near;
            }
        }
        #endregion

        public static void DealGridInfo(ResvRegion resvRegion, Dictionary<InjectGridUnit, int> coveredGridDic, Dictionary<string, RegionMileageInfo> regionMileageInfoDic)
        {
            RegionMileageInfo regionMileageInfo = new RegionMileageInfo(resvRegion.RegionName);
            regionMileageInfo.Area = RegionAreaCalculator.CalculateArea(resvRegion.Shape);
            //区域内栅格里程

            foreach (InjectGridUnit grid in gridMatrix)
            {
                if (grid != null && resvRegion.GeoOp.CheckRectCenterInRegion(grid.Bounds))
                {
                    regionMileageInfo.MileageInfo.Total += grid.TestDistance;
                    regionMileageInfo.DurationInfo.Total += grid.TestDuration;
                }
            }

            foreach (InjectGridUnit item in coveredGridDic.Keys)
            {
                regionMileageInfo.MileageInfo.CoveredData += item.TestDistance;
                regionMileageInfo.DurationInfo.CoveredData += item.TestDuration;
            }
            regionMileageInfo.Calculate();
            if (!regionMileageInfoDic.ContainsKey(regionMileageInfo.Name))
            {
                regionMileageInfoDic.Add(regionMileageInfo.Name, regionMileageInfo);
            }
        }

        public static void AddStreetInject(StreetInjectInfo info, Dictionary<string, StreetInjectInfoTotal> tableInjectDic, StreetInjectInfoTotal tableTotal)
        {
            if (!tableInjectDic.TryGetValue(info.StreetTableName, out StreetInjectInfoTotal tableInject))
            {
                tableInject = new StreetInjectInfoTotal();
                tableInjectDic.Add(info.StreetTableName, tableInject);
                tableInject.StreetTableName = info.StreetTableName;
            }
            tableInject.Add(info);
            tableTotal.Add(info);
        }

        public static void AddRegionInject(StreetInjectInfo info, Dictionary<string, StreetInjectInfoTotal> regionInjectDic, StreetInjectInfoTotal regionTotal, Dictionary<string, RegionMileageInfo> regionTestDistance)
        {
            if (!regionInjectDic.TryGetValue(info.AreaName, out StreetInjectInfoTotal regionInject))
            {
                regionInject = new StreetInjectInfoTotal();
                regionInjectDic.Add(info.AreaName, regionInject);
                regionInject.RegionName = info.AreaName;
                if (regionTestDistance.TryGetValue(info.AreaName, out RegionMileageInfo regionMileageInfo))
                {
                    regionInject.TestMileageKM = regionMileageInfo.MileageInfo.Total;
                    regionInject.CoveredMileageKM = regionMileageInfo.MileageInfo.CoveredData;
                    regionInject.NoneMustTestKM = regionMileageInfo.MileageInfo.UnCoveredData;
                    regionInject.MileageCoveredRate = regionMileageInfo.MileageInfo.CoveredRate;
                    regionInject.CoveredSpeed = regionMileageInfo.CoveredSpeed;
                    regionInject.TotalSpeed = regionMileageInfo.TotalSpeed;
                    regionInject.Area = Math.Round(regionMileageInfo.Area, 2);
                    regionTotal.Area += regionInject.Area;
                }
            }
            regionInject.Add(info);
            regionTotal.Add(info);
        }
    }
}
