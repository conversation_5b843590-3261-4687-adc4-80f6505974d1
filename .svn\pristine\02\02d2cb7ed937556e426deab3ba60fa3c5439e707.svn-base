﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using DBDataViewer;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.KPI_Statistics
{
    [Serializable]
    public abstract class KPIStatDataBase
    {
        public const string FileIDKey = "0801";
        public const string FileTimeKey = "0804";
        public const string LTLngKey = "0808";
        public const string LTLatKey = "0809";

        //新版栅格编码
        public const string NewGridFileIDKey = "80040001";
        public const string NewGridFileTimeKey = "80040004";
        public const string NewGridLTLngKey = "80040008";
        public const string NewGridLTLatKey = "80040009";

        /// <summary>
        /// 取配置statinitargs_kpi_xxx.xml 中FName（也即是统计公式中的最小单元）的前缀
        /// 如：FName=Lte_6121050E，则Token为Lte_
        /// 用于公式解析
        /// </summary>
        abstract public string Token { get; }
        protected Dictionary<string, double> fieldValueDic = new Dictionary<string, double>();

        /// <summary>
        /// 文件ID，-1时为错误ID
        /// </summary>
        public virtual int FileID
        {
            get
            {
                double v = this[FileIDKey, NewGridFileIDKey, - 1];
                if (!double.IsNaN(v))
                {
                    return int.Parse(v.ToString());
                }
                return -1;
            }
        }

        public virtual double LTLng
        {
            get
            {
                double v = this[LTLngKey, NewGridLTLngKey, - 1];
                if (!double.IsNaN(v))
                {
                    return v / 10000000;
                }
                return -1;
            }
        }

        public virtual double LTLat
        {
            get
            {
                double v = this[LTLatKey, NewGridLTLatKey, - 1];
                if (!double.IsNaN(v))
                {
                    return v / 10000000;
                }
                return -1;
            }
        }

        protected int imgCount = 1;
        public int ImgCount
        {
            get { return imgCount; }
        }

        public string CreateKeyWithServiceID(string fieldKey, int serviceID)
        {
            return serviceID.ToString() + "#" + fieldKey;
        }

        /// <summary>
        /// 根据业务类型ID、文件名关键字 创建 存储主键（排列组合成多个主键）
        /// </summary>
        /// <param name="fieldKey">指标公式</param>
        /// <param name="serviceID">业务类型ID</param>
        /// <param name="fileNameKeyWord">文件名关键字</param>
        /// <returns></returns>
        public List<string> CreateKeysWithServIdAndFileName(string fieldKey, int serviceID, string fileNameKeyWord)
        {
            List<string> keyList = new List<string>();
            if (serviceID == -1 && string.IsNullOrEmpty(fileNameKeyWord))
            {
                return keyList;
            }

            string mergeKey = getKeyWithServIdAndFileName(fieldKey, serviceID, fileNameKeyWord);
            if (!keyList.Contains(mergeKey))
            {
                keyList.Add(mergeKey);
            }

            if (serviceID != -1 && !string.IsNullOrEmpty(fileNameKeyWord))
            {
                string serviceIdKey = getKeyWithServIdAndFileName(fieldKey, serviceID, "");
                if (!keyList.Contains(serviceIdKey))
                {
                    keyList.Add(serviceIdKey);
                }

                string fileNameKey = getKeyWithServIdAndFileName(fieldKey, -1, fileNameKeyWord);
                if (!keyList.Contains(fileNameKey))
                {
                    keyList.Add(fileNameKey);
                }
            }
            return keyList;
        }
        
        /// <summary>
        /// 根据业务类型ID、文件名关键字 获取 单个存储主键
        /// </summary>
        /// <param name="fieldKey"></param>
        /// <param name="serviceID"></param>
        /// <param name="fileNameKey"></param>
        /// <returns></returns>
        private string getKeyWithServIdAndFileName(string fieldKey, int serviceID, string fileNameKey)
        {
            string strKey = fieldKey;
            if (serviceID != -1)
            {
                strKey = serviceID.ToString() + "#" + fieldKey;
            }
            if (!string.IsNullOrEmpty(fileNameKey))
            {
                return fileNameKey + "$" + strKey;
            }
            return strKey;
        }

        public double GetValue(string fieldKeyWithoutToken, List<int> serviceIDSet, List<string> fileNameKeylist)
        {
            if (serviceIDSet == null || serviceIDSet.Count == 0)
            {
                serviceIDSet = new List<int>() { -1 };
            }
            if (fileNameKeylist == null || fileNameKeylist.Count == 0)
            {
                fileNameKeylist = new List<string>() { "" };
            }

            double ret = double.NaN;
            int haveValueNum = 0;
            double sum = 0;
            StatImgDefItem imgDef = InterfaceManager.GetInstance().GetStatImgDef(fieldKeyWithoutToken);
            if (imgDef != null)
            {
                if (imgDef.gatherMethod == GatherMethod.E_MIN)
                {
                    ret = double.MaxValue;
                }
                else if (imgDef.gatherMethod == GatherMethod.E_MAX)
                {
                    ret = double.MinValue;
                }
            }
            else
            {
                ret = 0;
            }

            foreach (int id in serviceIDSet)
            {
                dealImgData(fieldKeyWithoutToken, fileNameKeylist, ref ret, ref haveValueNum, ref sum, imgDef, id);
            }
            if (haveValueNum != 0 && imgDef != null && imgDef.gatherMethod == GatherMethod.E_MEAN)
            {
                ret = sum / haveValueNum;
            }
            return ret;
        }

        private void dealImgData(string fieldKeyWithoutToken, List<string> fileNameKeylist, ref double ret, ref int haveValueNum, ref double sum, StatImgDefItem imgDef, int id)
        {
            foreach (string fileNameKey in fileNameKeylist)
            {
                double val = this[fieldKeyWithoutToken, id, fileNameKey];
                if (imgDef == null)
                {
                    ret = getValidData(ret, val);
                }
                else
                {
                    addImgData(ref ret, ref haveValueNum, ref sum, imgDef, val);
                }
            }
        }

        private static double getValidData(double ret, double val)
        {
            if (!double.IsNaN(val))
            {
                if (!double.IsNaN(ret))
                {
                    ret += val;
                }
                else
                {
                    ret = val;
                }
            }

            return ret;
        }

        private void addImgData(ref double ret, ref int haveValueNum, ref double sum, StatImgDefItem imgDef, double val)
        {
            switch (imgDef.gatherMethod)
            {
                case GatherMethod.E_SUM:
                    ret = getValidData(ret, val);
                    break;
                case GatherMethod.E_MEAN:
                    if (!double.IsNaN(val))
                    {
                        haveValueNum++;
                        sum += val;
                    }
                    break;
                case GatherMethod.E_MAX:
                    if (double.MinValue == val)
                    {
                        ret = val;
                    }
                    else
                    {
                        ret = Math.Max(ret, val);
                    }
                    break;
                case GatherMethod.E_MIN:
                    if (double.MaxValue == val)
                    {
                        ret = val;
                    }
                    else
                    {
                        ret = Math.Min(ret, val);
                    }
                    break;
                case GatherMethod.E_MEDIAN:
                case GatherMethod.E_Ignore:
                    ret = val;
                    break;
                default:
                    break;
            }
        }

        public double this[string fieldKey, string newGridFieldKey, int serviceID]
        {
            get
            {
                double val = 0;
                if (fieldValueDic.ContainsKey(fieldKey))
                {
                    val = fieldValueDic[fieldKey];
                }
                else if (fieldValueDic.ContainsKey(newGridFieldKey))
                {
                    val = fieldValueDic[newGridFieldKey];
                }
                return val;
            }
        }


        public double this[string fieldKeyWithoutToken, int serviceID]
        {
            get 
            {
                if (serviceID != -1)
                {
                    fieldKeyWithoutToken = CreateKeyWithServiceID(fieldKeyWithoutToken, serviceID);
                }
                double val;
                fieldValueDic.TryGetValue(fieldKeyWithoutToken, out val);
                return val;
            }
            set
            {
                if (string.IsNullOrEmpty(fieldKeyWithoutToken))
                {
                    return;
                }
                fieldValueDic[fieldKeyWithoutToken] = value;
                if (serviceID != -1)
                {
                    string keyWithSvID = CreateKeyWithServiceID(fieldKeyWithoutToken, serviceID);
                    fieldValueDic[keyWithSvID] = value;
                }
            }
        }
        public double this[string fieldKeyWithoutToken, int serviceID, string fileNameKey]
        {
            get
            {
                fieldKeyWithoutToken = getKeyWithServIdAndFileName(fieldKeyWithoutToken, serviceID, fileNameKey);
                double val;
                if (fieldValueDic.TryGetValue(fieldKeyWithoutToken, out val))
                {
                    return val;
                }
                return double.NaN;
            }
            set
            {
                if (string.IsNullOrEmpty(fieldKeyWithoutToken))
                {
                    return;
                }
                fieldValueDic[fieldKeyWithoutToken] = value;

                List<string> keyList = CreateKeysWithServIdAndFileName(fieldKeyWithoutToken, serviceID, fileNameKey);
                foreach (string strKey in keyList)
                {
                    fieldValueDic[strKey] = value; 
                }
            }
        }
        public virtual bool GatherStatData(KPIStatDataBase data)//收集统计数据
        {
            if (this.GetType().Equals(data.GetType()))
            {
                this.imgCount += data.imgCount;
                Dictionary<string, double> dicTemp = new Dictionary<string, double>();
                foreach (string imgCode in this.fieldValueDic.Keys)
                {
                    dicTemp[imgCode] = this.fieldValueDic[imgCode];
                }
                foreach (string imgCode in data.fieldValueDic.Keys)
                {
                    int serviceID = -1;
                    string fileNameKey = "";
                    StatImgDefItem imgDef = getStatImgDefItem(imgCode, ref serviceID, ref fileNameKey);

                    if (imgDef != null)
                    {
                        switch (imgDef.gatherMethod)
                        {
                            case GatherMethod.E_SUM:
                                {
                                    getSumData(data, dicTemp, imgCode);
                                }
                                break;
                            case GatherMethod.E_Ignore:
                                break;
                            case GatherMethod.E_MIN:
                                {
                                    getMinData(data, imgCode);
                                }
                                break;
                            case GatherMethod.E_MAX:
                                {
                                    getMaxData(data, imgCode);
                                }
                                break;
                            case GatherMethod.E_MEAN:
                                {
                                    getMeanData(data, dicTemp, imgCode, serviceID, fileNameKey, imgDef);
                                }
                                break;
                            case GatherMethod.E_MEDIAN:
                                break;
                        }
                    }
                }

                //StatDataConverter.gatherStatImgInfo(data.fieldValueDic, this.fieldValueDic);
                return true;
            }
            return false;
        }

        private void getSumData(KPIStatDataBase data, Dictionary<string, double> dicTemp, string imgCode)
        {
            if (this.fieldValueDic.ContainsKey(imgCode))
            {
                this.fieldValueDic[imgCode] = dicTemp[imgCode] + data.fieldValueDic[imgCode];
            }
            else
            {
                this.fieldValueDic[imgCode] = data.fieldValueDic[imgCode];
                dicTemp[imgCode] = data.fieldValueDic[imgCode];
            }
        }

        private void getMinData(KPIStatDataBase data, string imgCode)
        {
            if (!this.fieldValueDic.ContainsKey(imgCode))
            {
                this.fieldValueDic.Add(imgCode, data.fieldValueDic[imgCode]);
            }
            else
            {
                if (data.fieldValueDic[imgCode] < this.fieldValueDic[imgCode])
                {
                    this.fieldValueDic[imgCode] = data.fieldValueDic[imgCode];
                }
            }
        }

        private void getMaxData(KPIStatDataBase data, string imgCode)
        {
            if (!this.fieldValueDic.ContainsKey(imgCode))
            {
                this.fieldValueDic.Add(imgCode, data.fieldValueDic[imgCode]);
            }
            else
            {
                if (data.fieldValueDic[imgCode] > this.fieldValueDic[imgCode])
                {
                    this.fieldValueDic[imgCode] = data.fieldValueDic[imgCode];
                }
            }
        }

        private void getMeanData(KPIStatDataBase data, Dictionary<string, double> dicTemp, string imgCode, int serviceID, string fileNameKey, StatImgDefItem imgDef)
        {
            string refstr = imgDef.bak;
            if (serviceID != -1 || !string.IsNullOrEmpty(fileNameKey))
            {
                refstr = getKeyWithServIdAndFileName(imgDef.bak, serviceID, fileNameKey);
            }
            if (refstr != null && this.fieldValueDic.ContainsKey(imgCode)
                && this.fieldValueDic.ContainsKey(refstr) && data.fieldValueDic.ContainsKey(refstr))
            {
                double oldMean = dicTemp[imgCode];
                double oldCount = dicTemp[refstr];
                double newMean = data.fieldValueDic[imgCode];
                double newCount = data.fieldValueDic[refstr];
                if (oldCount + newCount > 0)
                {
                    double mean = (oldMean * oldCount + newMean * newCount)
                        / (oldCount + newCount);
                    this.fieldValueDic[imgCode] = mean;
                }
            }
            else
            {
                this.fieldValueDic[imgCode] = data.fieldValueDic[imgCode];
                dicTemp[imgCode] = data.fieldValueDic[imgCode];
            }
        }

        public virtual KPIStatDataBase Clone()
        {
            using (MemoryStream objectStream = new MemoryStream())
            {
                IFormatter formatter = new BinaryFormatter();
                formatter.Serialize(objectStream, this);
                objectStream.Seek(0, SeekOrigin.Begin);
                return formatter.Deserialize(objectStream) as KPIStatDataBase;
            }
        }

        protected StatImgDefItem getStatImgDefItem(string imgCode, ref int serviceID, ref string fileNameKey)
        {
            StatImgDefItem imgDef = null;
            int idx = imgCode.IndexOf("#");
            serviceID = -1;
            fileNameKey = "";
            if (idx == -1)
            {
                imgDef = getImgDef(imgCode,ref fileNameKey);
            }
            else
            {
                int sIdx = imgCode.IndexOf("$") + 1;
                serviceID = int.Parse(imgCode.Substring(sIdx, idx - sIdx));
                string realCode = imgCode.Substring(idx + 1);
                imgDef = getImgDef(realCode, ref fileNameKey);
            }
            return imgDef;
        }

        private StatImgDefItem getImgDef(string imgCode, ref string fileNameKey)
        {
            StatImgDefItem imgDef = null;
            int idx = imgCode.IndexOf("$");
            if (idx == -1)
            {
                imgDef = InterfaceManager.GetInstance().GetStatImgDef(imgCode);
            }
            else
            {
                fileNameKey = imgCode.Substring(0, idx);
                string realCode = imgCode.Substring(idx + 1);
                imgDef = InterfaceManager.GetInstance().GetStatImgDef(realCode);
            }
            return imgDef;
        }
    }
}
