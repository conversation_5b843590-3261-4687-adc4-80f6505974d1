﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDPccpchRscpWeakCoverConditionDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.numPccpchRscpThreshold = new System.Windows.Forms.NumericUpDown();
            this.numDistance = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.numPccpchC_I = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numMaxDistance = new System.Windows.Forms.NumericUpDown();
            this.numDpchC_I = new System.Windows.Forms.NumericUpDown();
            this.checkDpchC2I = new System.Windows.Forms.CheckBox();
            this.checkPccpchC2I = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label12 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.numSampleCellAngle = new System.Windows.Forms.NumericUpDown();
            this.label10 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.numSampleCellDistance = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.chbNCellRSCP = new System.Windows.Forms.CheckBox();
            this.numNCellRSCP = new System.Windows.Forms.NumericUpDown();
            this.label13 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchRscpThreshold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchC_I)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDpchC_I)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCellAngle)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCellDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNCellRSCP)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(98, 21);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(101, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "TD_PCCPCH_RSCP≤";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(134, 153);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "持续距离≥";
            // 
            // numPccpchRscpThreshold
            // 
            this.numPccpchRscpThreshold.Location = new System.Drawing.Point(205, 17);
            this.numPccpchRscpThreshold.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numPccpchRscpThreshold.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numPccpchRscpThreshold.Name = "numPccpchRscpThreshold";
            this.numPccpchRscpThreshold.Size = new System.Drawing.Size(94, 21);
            this.numPccpchRscpThreshold.TabIndex = 2;
            this.numPccpchRscpThreshold.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numPccpchRscpThreshold.Value = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            // 
            // numDistance
            // 
            this.numDistance.Location = new System.Drawing.Point(205, 147);
            this.numDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDistance.Name = "numDistance";
            this.numDistance.Size = new System.Drawing.Size(94, 21);
            this.numDistance.TabIndex = 3;
            this.numDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(305, 21);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(23, 12);
            this.label3.TabIndex = 4;
            this.label3.Text = "dBm";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(305, 156);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "米";
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(175, 408);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 6;
            this.btnOK.Text = "确认";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(268, 408);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // numPccpchC_I
            // 
            this.numPccpchC_I.Enabled = false;
            this.numPccpchC_I.Location = new System.Drawing.Point(205, 82);
            this.numPccpchC_I.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numPccpchC_I.Minimum = new decimal(new int[] {
            20,
            0,
            0,
            -2147483648});
            this.numPccpchC_I.Name = "numPccpchC_I";
            this.numPccpchC_I.Size = new System.Drawing.Size(94, 21);
            this.numPccpchC_I.TabIndex = 9;
            this.numPccpchC_I.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numPccpchC_I.Value = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(305, 184);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 24;
            this.label7.Text = "米";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(98, 184);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 23;
            this.label6.Text = "相邻采样点距离≤";
            // 
            // numMaxDistance
            // 
            this.numMaxDistance.Location = new System.Drawing.Point(205, 180);
            this.numMaxDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxDistance.Name = "numMaxDistance";
            this.numMaxDistance.Size = new System.Drawing.Size(94, 21);
            this.numMaxDistance.TabIndex = 22;
            this.numMaxDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // numDpchC_I
            // 
            this.numDpchC_I.Enabled = false;
            this.numDpchC_I.Location = new System.Drawing.Point(205, 114);
            this.numDpchC_I.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numDpchC_I.Minimum = new decimal(new int[] {
            20,
            0,
            0,
            -2147483648});
            this.numDpchC_I.Name = "numDpchC_I";
            this.numDpchC_I.Size = new System.Drawing.Size(94, 21);
            this.numDpchC_I.TabIndex = 26;
            this.numDpchC_I.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDpchC_I.Value = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            // 
            // checkDpchC2I
            // 
            this.checkDpchC2I.AutoSize = true;
            this.checkDpchC2I.Location = new System.Drawing.Point(97, 119);
            this.checkDpchC2I.Name = "checkDpchC2I";
            this.checkDpchC2I.Size = new System.Drawing.Size(102, 16);
            this.checkDpchC2I.TabIndex = 27;
            this.checkDpchC2I.Text = "TD_DPCH_C/I≤";
            this.checkDpchC2I.UseVisualStyleBackColor = true;
            this.checkDpchC2I.CheckedChanged += new System.EventHandler(this.checkDpchC2I_CheckedChanged);
            // 
            // checkPccpchC2I
            // 
            this.checkPccpchC2I.AutoSize = true;
            this.checkPccpchC2I.Location = new System.Drawing.Point(85, 83);
            this.checkPccpchC2I.Name = "checkPccpchC2I";
            this.checkPccpchC2I.Size = new System.Drawing.Size(114, 16);
            this.checkPccpchC2I.TabIndex = 27;
            this.checkPccpchC2I.Text = "TD_PCCPCH_C/I≤";
            this.checkPccpchC2I.UseVisualStyleBackColor = true;
            this.checkPccpchC2I.CheckedChanged += new System.EventHandler(this.checkPccpchC2I_CheckedChanged);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label12);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.numSampleCellAngle);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Controls.Add(this.numSampleCellDistance);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Location = new System.Drawing.Point(32, 225);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(309, 154);
            this.groupBox1.TabIndex = 28;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "优化问题判断条件";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.ForeColor = System.Drawing.Color.Red;
            this.label12.Location = new System.Drawing.Point(12, 127);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(245, 12);
            this.label12.TabIndex = 29;
            this.label12.Text = "（周边没有符合条件且覆盖到此路段的小区）";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(32, 103);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(209, 12);
            this.label11.TabIndex = 28;
            this.label11.Text = "注：不符合上述条件，归类为规划问题";
            // 
            // numSampleCellAngle
            // 
            this.numSampleCellAngle.Location = new System.Drawing.Point(154, 57);
            this.numSampleCellAngle.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numSampleCellAngle.Name = "numSampleCellAngle";
            this.numSampleCellAngle.Size = new System.Drawing.Size(94, 21);
            this.numSampleCellAngle.TabIndex = 27;
            this.numSampleCellAngle.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSampleCellAngle.Value = new decimal(new int[] {
            60,
            0,
            0,
            0});
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(254, 62);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(17, 12);
            this.label10.TabIndex = 26;
            this.label10.Text = "度";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(29, 62);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(119, 12);
            this.label9.TabIndex = 25;
            this.label9.Text = "采样点与小区夹角 ≤";
            // 
            // numSampleCellDistance
            // 
            this.numSampleCellDistance.Location = new System.Drawing.Point(154, 26);
            this.numSampleCellDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numSampleCellDistance.Name = "numSampleCellDistance";
            this.numSampleCellDistance.Size = new System.Drawing.Size(94, 21);
            this.numSampleCellDistance.TabIndex = 24;
            this.numSampleCellDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSampleCellDistance.Value = new decimal(new int[] {
            300,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(254, 28);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 23;
            this.label8.Text = "米";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(29, 30);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(119, 12);
            this.label5.TabIndex = 23;
            this.label5.Text = "采样点与小区距离 ≤";
            // 
            // chbNCellRSCP
            // 
            this.chbNCellRSCP.AutoSize = true;
            this.chbNCellRSCP.Checked = true;
            this.chbNCellRSCP.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chbNCellRSCP.Location = new System.Drawing.Point(31, 51);
            this.chbNCellRSCP.Name = "chbNCellRSCP";
            this.chbNCellRSCP.Size = new System.Drawing.Size(168, 16);
            this.chbNCellRSCP.TabIndex = 27;
            this.chbNCellRSCP.Text = "最强邻区TD_PCCPCH_RSCP≤";
            this.chbNCellRSCP.UseVisualStyleBackColor = true;
            this.chbNCellRSCP.CheckedChanged += new System.EventHandler(this.chbNCellRSCP_CheckedChanged);
            // 
            // numNCellRSCP
            // 
            this.numNCellRSCP.Location = new System.Drawing.Point(205, 50);
            this.numNCellRSCP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numNCellRSCP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numNCellRSCP.Name = "numNCellRSCP";
            this.numNCellRSCP.Size = new System.Drawing.Size(94, 21);
            this.numNCellRSCP.TabIndex = 2;
            this.numNCellRSCP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numNCellRSCP.Value = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(305, 52);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(23, 12);
            this.label13.TabIndex = 4;
            this.label13.Text = "dBm";
            // 
            // TDPccpchRscpWeakCoverConditionDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(376, 445);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.chbNCellRSCP);
            this.Controls.Add(this.checkPccpchC2I);
            this.Controls.Add(this.checkDpchC2I);
            this.Controls.Add(this.numDpchC_I);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.numMaxDistance);
            this.Controls.Add(this.numPccpchC_I);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label13);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.numDistance);
            this.Controls.Add(this.numNCellRSCP);
            this.Controls.Add(this.numPccpchRscpThreshold);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "TDPccpchRscpWeakCoverConditionDlg";
            this.Text = "TD弱覆盖条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchRscpThreshold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchC_I)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDpchC_I)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCellAngle)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCellDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNCellRSCP)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numPccpchRscpThreshold;
        private System.Windows.Forms.NumericUpDown numDistance;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.NumericUpDown numPccpchC_I;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numMaxDistance;
        private System.Windows.Forms.NumericUpDown numDpchC_I;
        private System.Windows.Forms.CheckBox checkDpchC2I;
        private System.Windows.Forms.CheckBox checkPccpchC2I;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown numSampleCellAngle;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown numSampleCellDistance;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.CheckBox chbNCellRSCP;
        private System.Windows.Forms.NumericUpDown numNCellRSCP;
        private System.Windows.Forms.Label label13;
    }
}