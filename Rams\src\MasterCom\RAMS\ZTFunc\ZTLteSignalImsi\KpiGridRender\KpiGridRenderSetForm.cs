﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Frame;
using MasterCom.Util;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    public partial class KpiGridRenderSetForm : BaseDialog
    {
        public KpiGridRenderSetForm(GridColorModeItem preItem)
        {
            InitializeComponent();
            btnCancel.Click += BtnCancel_Click;
            btnOK.Click += BtnOK_Click;
            btnSetting.Click += BtnSetting_Click;

            Load(preItem);
        }
        public KpiGridRenderSetForm(GridColorModeItem preItem,List<string> availableFormulas)
        {
            InitializeComponent();
            btnCancel.Click += BtnCancel_Click;
            btnOK.Click += BtnOK_Click;
            btnSetting.Click += BtnSetting_Click;

            Load(preItem, availableFormulas);
        }

        public GridColorModeItem GetSelectedItem()
        {
            return cbxKpi.SelectedItem as GridColorModeItem;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (cbxKpi.SelectedItem == null)
            {
                MessageBox.Show("请选择渲染指标", this.Text, MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = System.Windows.Forms.DialogResult.None;
                return;
            }

            DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void BtnSetting_Click(object sender, EventArgs e)
        {
            MapGridLayer layer = MainModel.MainForm.GetMapForm().GetGridShowLayer();
            GridColorMngDlg dlg = new GridColorMngDlg(layer, false);
            dlg.FillColorModelList(layer.GridColorModes, layer.CurUsingColorMode);
            dlg.initType();
            dlg.ShowDialog();

            Load(cbxKpi.SelectedItem as GridColorModeItem);
        }

        private new void Load(GridColorModeItem preItem)
        {
            cbxKpi.Items.Clear();
            GridColorModeItem selectedItem = null;
            foreach (GridColorModeItem item in GridColorManager.Instance.GridColorItems)
            {
                cbxKpi.Items.Add(item);
                if (preItem != null && preItem.Name == item.Name)
                {
                    selectedItem = item;
                }
            }

            if (selectedItem != null)
            {
                cbxKpi.SelectedItem = selectedItem;
            }
            else if (cbxKpi.Items.Count > 0)
            {
                cbxKpi.SelectedIndex = 0;
            }
        }
        private new void Load(GridColorModeItem preItem,List<string> formulasList)
        {
            Dictionary<string, bool> dic = new Dictionary<string, bool>();
            formulasList.ForEach(x => dic[x] = true);
            cbxKpi.Items.Clear();
            GridColorModeItem selectedItem = null;
            foreach (GridColorModeItem item in GridColorManager.Instance.GridColorItems)
            {
                if (!string.IsNullOrEmpty(item.formula) && dic.ContainsKey(item.formula))
                {
                    cbxKpi.Items.Add(item);
                    if (preItem != null && preItem.Name == item.Name)
                    {
                        selectedItem = item;
                    }
                }
            }

            if (selectedItem != null)
            {
                cbxKpi.SelectedItem = selectedItem;
            }
            else if (cbxKpi.Items.Count > 0)
            {
                cbxKpi.SelectedIndex = 0;
            }
        }
    }
}
