﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakCoverAnaCondtion
    {
        public WeakCoverAnaCondtion()
        {
            InventoryDic = new Dictionary<string, bool>();
            InventoryDic.Add("缺少规划站", true);
            InventoryDic.Add("切换不合理", true);
            InventoryDic.Add("覆盖不稳定", true);
            InventoryDic.Add("过覆盖", true);
        }
        
        public float RsrpWeakGate { get; set; } = -105;
        public float LteBtsDisGate { get; set; } = 800;
        public float RsrpMainLowerNear { get; set; } = 5;
        public int MinSecondsMainLowerNear { get; set; } = 10;
        public int LastSecondsBeforeWeak { get; set; } = 10;
        public float RsrpGoodGate { get; set; } = -95;
        public int IdealCoverBtsCount { get; set; } = 3;
        public float IdealCoverRadFactor { get; set; } = 1.6F;
        public Dictionary<string, bool> InventoryDic { get; set; }
    }
}
