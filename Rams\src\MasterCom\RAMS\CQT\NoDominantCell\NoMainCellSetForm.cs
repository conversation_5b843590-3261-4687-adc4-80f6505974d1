﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public partial class NoMainCellSetForm : Form
    {
        public NoMainCellSetForm(string netWoker)
        {
            InitializeComponent();
            if (netWoker.Equals("TD"))
                label8.Text = "最大最小RSCP差";
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (numRxlevMin.Value > numRxLevMax.Value)
            {
                MessageBox.Show("采样点小区场强门限最小值不能大于最大值！");
                this.DialogResult = DialogResult.Retry;
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
        public NoMainCellCondition GetSettingFilterRet()
        {
            NoMainCellCondition condition = new NoMainCellCondition();
            condition.RSCPMin = (int)numRxlevMin.Value;
            condition.RSCPMax = (int)numRxLevMax.Value;
            condition.sampleCountLimit = (int)numSampleCountLimit.Value;
            condition.cellCountLimit = (int)numCellCountLimit.Value;
            condition.rxLevDValue = (int)numRxLevDValue.Value;
            return condition;
        }
    }

    public class NoMainCellCondition
    {
        public int RSCPMin { get; set; }
        public int RSCPMax { get; set; }
        public int sampleCountLimit { get; set; }
        public int cellCountLimit { get; set; }
        public int rxLevDValue { get; set; }
    }
}
