﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsHighCoverageSetting : LteMgrsConditionControlBase
    {
        public LteMgrsHighCoverageSetting()
        {
            InitializeComponent();
            InitCbxFreqType();
            chkOptionalRsrp.CheckedChanged += ChkOptionRsrp_CheckedChanged;
            initValues();
        }

        public override string Title
        {
            get { return "高重叠覆盖区域"; }
        }

        private void initValues()
        {
            XmlConfigFile configFile = new MyXmlConfigFile(LteMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                XmlElement configHighCoverage = configFile.GetConfig("HighCoverage");
                object obj = configFile.GetItemValue(configHighCoverage, "Coverage");
                if (obj != null)
                {
                    this.numCoverage.Value = (decimal)(double)obj;
                }

                obj = configFile.GetItemValue(configHighCoverage, "CheckTwoEarfcn");
                if (obj != null)
                {
                    this.chkTwoEarfcn.Enabled = (bool)obj;
                }

                obj = configFile.GetItemValue(configHighCoverage, "FreqType");
                if (obj != null)
                {
                    int index = cbxFreqType.Items.IndexOf(obj.ToString());
                    if (index >= 0)
                    {
                        cbxFreqType.SelectedIndex = index;
                    }
                }

                setInitValue(configFile, configHighCoverage);
            }
        }

        private void setInitValue(XmlConfigFile configFile, XmlElement configHighCoverage)
        {
            object obj = configFile.GetItemValue(configHighCoverage, "MinRsrp");
            if (obj != null)
            {
                this.numRsrpMin.Value = (decimal)(double)obj;
            }

            obj = configFile.GetItemValue(configHighCoverage, "DiffRsrp");
            if (obj != null)
            {
                this.numRsrpDiff.Value = (decimal)(double)obj;
            }

            obj = configFile.GetItemValue(configHighCoverage, "GridCount");
            if (obj != null)
            {
                this.numGridCount.Value = (decimal)(int)obj;
            }

            obj = configFile.GetItemValue(configHighCoverage, "EnableOptional");
            if (obj != null)
            {
                this.chkOptionalRsrp.Enabled = (bool)obj;
            }

            obj = configFile.GetItemValue(configHighCoverage, "OptionalRsrp");
            if (obj != null)
            {
                this.numOptionalRsrp.Value = (decimal)(double)obj;
            }

            obj = configFile.GetItemValue(configHighCoverage, "EnableOutputSample");
            if (obj != null)
            {
                this.checkEditSampleData.Checked = (bool)obj;
            }

            obj = configFile.GetItemValue(configHighCoverage, "EnableGridNumDist");
            if (obj != null)
            {
                this.chkGridNum.Checked = (bool)obj;
            }

            obj = configFile.GetItemValue(configHighCoverage, "GridNumDist");
            if (obj != null)
            {
                this.numGridNum.Value = (decimal)(int)obj;
            }

            obj = configFile.GetItemValue(configHighCoverage, "CsvPath");
            if (obj != null)
            {
                this.txtCsvPath.Text = obj.ToString();
            }

            obj = configFile.GetItemValue(configHighCoverage, "EnableFBandType");
            if (obj != null)
            {
                this.chkFBandType.Checked = (bool)obj;
            }
        }

        public override object GetCondition(out string invalidReason)
        {
            invalidReason = null;
            LteMgrsHighCoverageCondition cond = new LteMgrsHighCoverageCondition();
            cond.Coverage = (double)numCoverage.Value;
            cond.CheckTwoEarfcn = (bool)chkTwoEarfcn.EditValue;
            cond.FreqType = (LteMgrsCoverageBandType)EnumDescriptionAttribute.Parse(typeof(LteMgrsCoverageBandType), cbxFreqType.SelectedItem as string);
            cond.MinRsrp = (double)numRsrpMin.Value;
            cond.DiffRsrp = (double)numRsrpDiff.Value;
            cond.GridCount = (int)numGridCount.Value;
            cond.EnableOptional = chkOptionalRsrp.Checked;
            cond.OptionalRsrp = (double)numOptionalRsrp.Value;
            cond.EnableOutputSample = checkEditSampleData.Checked;
            cond.EnableGridNumDist = chkGridNum.Checked;
            cond.GridNumDist = (int)numGridNum.Value;
            cond.strCsvPath = txtCsvPath.Text;
            cond.EnableFBandType = chkFBandType.Checked;
            return cond;
        }

        private void ChkOptionRsrp_CheckedChanged(object sender, EventArgs e)
        {
            numOptionalRsrp.Enabled = chkOptionalRsrp.Checked;
        }

        private void chkTwoEarfcn_CheckedChanged(object sender, EventArgs e)
        {
            cbxFreqType.Enabled = !chkTwoEarfcn.Checked;
            chkFBandType.Enabled = chkTwoEarfcn.Checked;
        }

        private void chkGridNum_CheckedChanged(object sender, EventArgs e)
        {
            numGridNum.Enabled = chkGridNum.Checked;
        }

        private void InitCbxFreqType()
        {
            cbxFreqType.Items.Clear();
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.All));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.Top));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD1));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD2));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_38098));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF1));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF2));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.TopEarfcn));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_40936));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_40940));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleE_38950));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleE_39148));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD1_3683));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD1_3692));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1259));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1300));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1309));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1359));
            cbxFreqType.SelectedIndex = 0;
        }

        private void btnFile_Click(object sender, EventArgs e)
        {
            string filePath = txtCsvPath.Text;
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择文件夹";
            dialog.ShowNewFolderButton = true;
            dialog.SelectedPath = filePath;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                txtCsvPath.Text = dialog.SelectedPath;
            }
        }

        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            XmlElement configHighCoverage = xcfg.AddConfig("HighCoverage");
            xcfg.AddItem(configHighCoverage, "Coverage", (double)numCoverage.Value);
            xcfg.AddItem(configHighCoverage, "CheckTwoEarfcn", chkTwoEarfcn.Checked);
            xcfg.AddItem(configHighCoverage, "FreqType", cbxFreqType.Text);
            xcfg.AddItem(configHighCoverage, "MinRsrp", (double)numRsrpMin.Value);
            xcfg.AddItem(configHighCoverage, "DiffRsrp", (double)numRsrpDiff.Value);
            xcfg.AddItem(configHighCoverage, "GridCount", (int)numGridCount.Value);
            xcfg.AddItem(configHighCoverage, "EnableOptional", chkOptionalRsrp.Enabled);
            xcfg.AddItem(configHighCoverage, "OptionalRsrp", (double)numOptionalRsrp.Value);
            xcfg.AddItem(configHighCoverage, "EnableOutputSample", checkEditSampleData.Checked);
            xcfg.AddItem(configHighCoverage, "EnableGridNumDist", chkGridNum.Checked);
            xcfg.AddItem(configHighCoverage, "GridNumDist", (int)numGridNum.Value);
            xcfg.AddItem(configHighCoverage, "CsvPath", txtCsvPath.Text);
            xcfg.AddItem(configHighCoverage, "EnableFBandType", chkFBandType.Enabled);
        }

        private void checkEditSampleData_CheckedChanged(object sender, EventArgs e)
        {
            txtCsvPath.Enabled = checkEditSampleData.Checked;
            btnFile.Enabled = checkEditSampleData.Checked;
            string filePath = Application.StartupPath + "\\userData";
            txtCsvPath.Text = filePath;
        }
    }

    public class LteMgrsHighCoverageCondition : LteMgrsCoverageCondition
    {
        public double Coverage { get; set; }
        public int GridCount { get; set; }
        public bool EnableGridNumDist { get; set; } = false;
        public int GridNumDist { get; set; }
    }
}
