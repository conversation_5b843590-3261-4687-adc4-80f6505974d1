﻿using System;
using System.Collections.Generic;
using System.Text;


using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.Net
{
    public class DIYFileIDByEventByStreet : DIYEventByStreet
    {
        public List<int> fileIDs { get; set; } = new List<int>();

        public DIYFileIDByEventByStreet(MainModel mainModel)
            : base(mainModel)
        {
            isAddEventToDTDataManager = false;
        }
        public override string Name
        {
            get { return "按事件查询文件(按道路)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11005, this.Name);
        }

        protected override void doWithDTData(Event evt)
        {
            if (!fileIDs.Contains(evt.FileID))
            {
                fileIDs.Add(evt.FileID);
            }
        }
    }
}
