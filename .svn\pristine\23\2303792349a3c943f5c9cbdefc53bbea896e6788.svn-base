﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRHnadoverNCellInfoDlg : BaseDialog
    {
        public NRHnadoverNCellInfoDlg()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        public void SetCondition(NRHnadoverNCellInfoCondition condition)
        {
            chkAnaLTE.Checked = condition.IsAnaLTE;
        }

        public void GetCondition(NRHnadoverNCellInfoCondition condition)
        {
            condition.IsAnaLTE = chkAnaLTE.Checked;
        }
    }

    public class NRHnadoverNCellInfoCondition
    {
        public bool IsAnaLTE { get; set; }
    }
}
