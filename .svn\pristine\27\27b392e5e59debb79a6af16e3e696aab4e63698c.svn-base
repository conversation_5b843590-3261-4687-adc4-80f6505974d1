﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.UserMng;

namespace MasterCom.RAMS.Stat
{
    public partial class NoGisBatExpRptForm : BaseForm
    {
        private static NoGisBatExpRptForm instance = null;
        public static string fileNameDIY { get; set; } = string.Empty;

        public static NoGisBatExpRptForm Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NoGisBatExpRptForm();
                    instance.Owner = MainModel.GetInstance().MainForm;
                }
                return instance;
            }
        }

        private NoGisBatExpRptForm()
        {
            InitializeComponent();
            init();
            MasterCom.Util.DevControlManager.TreeListHelper.ThreeStateControl(treeLstArea);
            this.LogItemSrc = new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11045, this.Text);
        }

        private void init()
        {
            cbxfileNumType.SelectedIndex = 0;
            cbxQueryFunc.SelectedIndex = 0;
            chkLstAgent.Items.Clear();
            tBoxFileName.Text = "(区域名)_(报表名)";
            foreach (CategoryEnumItem item in ((CategoryEnum)CategoryManager.GetInstance()["Agent"]).Items)
            {
                chkLstAgent.Items.Add(item, true);
            }

            chkLstDistrict.Items.Clear();
            foreach (IDNamePair item in DistrictManager.GetInstance().GetAvailableDistrict())
            {
                if (mainModel.User.DBID == -1)
                {
                    chkLstDistrict.Items.Add(item, true);
                }
                else if (item.id == mainModel.User.DBID)
                {
                    chkLstDistrict.Items.Add(item, true);
                    break;
                }
            }

            freshBatItemView(null);

            initProject();

            initTemplates();

            initService();

            initArea();
        }

        private void initProject()
        {
            chkLstProj.Items.Clear();
            foreach (CategoryEnumItem item in ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items)
            {
                chkLstProj.Items.Add(item, true);
            }
        }

        private void initTemplates()
        {
            chkLstReport.Items.Clear();
            if (System.IO.Directory.Exists(string.Format(Application.StartupPath + "/config/templates")))
            {
                System.IO.DirectoryInfo dinfo = new System.IO.DirectoryInfo(string.Format(Application.StartupPath + "/config/templates"));
                foreach (System.IO.FileInfo file in dinfo.GetFiles("nogis*", System.IO.SearchOption.TopDirectoryOnly))
                {
                    XmlConfigFile configFile = new XmlConfigFile(file.FullName);
                    List<object> list = configFile.GetItemValue("StatReports", "reports", GetItemValue) as List<object>;
                    if (list != null)
                    {
                        foreach (object o in list)
                        {
                            if (o != null)
                            {
                                ReporterTemplate rpt = o as ReporterTemplate;
                                chkLstReport.Items.Add(rpt);
                            }
                        }
                    }
                }
            }
        }

        private void initService()
        {
            chkLstService.Items.Clear();
            foreach (CategoryEnumItem item in ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items)
            {
                chkLstService.Items.Add(item, true);
            }
        }

        private void initArea()
        {
            treeLstArea.Nodes.Clear();
            foreach (CategoryEnumItem item in ((CategoryEnum)CategoryManager.GetInstance()["AreaType"]).Items)
            {
                List<CategoryEnumItem> project = ProjectManager.GetInstance().GetProjectsByAreaType(item.ID);
                if (project.Count > 0)
                {
                    DevExpress.XtraTreeList.Nodes.TreeListNode mainNode = treeLstArea.AppendNode(new object[] { item.Description }, null, CheckState.Checked);
                    mainNode.Tag = item;
                    List<CategoryEnumItem> subArea = AreaManager.GetInstance()[item.ID];
                    if (subArea != null)
                    {
                        foreach (CategoryEnumItem subItem in subArea)
                        {
                            treeLstArea.AppendNode(new object[] { subItem.Description }, mainNode, CheckState.Checked).Tag = subItem;
                        }
                    }
                }
            }
        }

        private void freshBatItemView(NoGisBatExpItem selItem)
        {
            chkLstBatItem.Items.Clear();
            foreach (NoGisBatExpItem item in NoGisBatExpCfgMngr.Instance.Items)
            {
                chkLstBatItem.Items.Add(item, item.IsCheck);
            }
            if (txtExportPath.TextLength == 0)
            {
                txtExportPath.Text = NoGisBatExpCfgMngr.Instance.ExportFolderPath;
            }
            chkLstBatItem.SelectedItem = selItem;
        }

        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(ReporterTemplate).Name))
            {
                Dictionary<string, object> dic = configFile.GetItemValue(item, "Param") as Dictionary<string, object>;
                if (dic == null)
                {
                    return null;
                }
                ReporterTemplate rpt = new ReporterTemplate();
                rpt.Param = dic;
                return rpt;
            }
            return null;
        }

        private void chkAll_CheckedChanged(object sender, EventArgs e)
        {
            for (int i = 0; i < chkLstBatItem.Items.Count; i++)
            {
                chkLstBatItem.SetItemChecked(i, chkAll.Checked);
            }
        }

        private TimePeriod getPeriod()
        {
            DateTime bTime = dateBegin.Value.Date;

            DateTime eTime = dateEnd.Value.Date.AddDays(1).AddMilliseconds(-1);
            if (bTime > eTime)
            {
                MessageBox.Show("开始时间不能大于结束时间！");
                return null;
            }
            TimePeriod p = new TimePeriod(bTime, eTime);
            return p;
        }
#if PermissionControl_DataExport
        ExportSecurityCondition exportCond;
#endif
        private void btnRun_Click(object sender, EventArgs e)
        {
            TimePeriod period = null;
            if (sender != null)
                period = getPeriod();
            else
                period = new TimePeriod(DateTime.Now.Date.AddDays(-(int)nupDays.Value), DateTime.Now.Date.AddDays(1).AddMilliseconds(-1));

            if (period == null)
            {
                return;
            }
            if (chkLstBatItem.CheckedItems.Count == 0)
            {
                MessageBox.Show("请至少勾选一个导出项！");
                return;
            }
            if (txtExportPath.TextLength == 0)
            {
                MessageBox.Show("请选择导出目录");
                return;
            }
            string timeDesc = string.Format("{0}_{1}", period.BeginTime.Date.ToString("yyMMdd")
                , period.EndTime.Date.ToString("yyMMdd"));
            string filePath = string.Format(this.txtExportPath.Text + "\\" + timeDesc);
#if PermissionControl_DataExport
            if (!ExportResultSecurityHelper.GetExportPermit(this.LogItemSrc, null
                , ref filePath, out exportCond))
            {
                return;
            }
#else
            if (!System.IO.Directory.Exists(filePath))
            {
                System.IO.Directory.CreateDirectory(filePath);
            }
#endif

            List<int> iAreatypeList = new List<int>();
            List<NoGisBatExpItem> items = new List<NoGisBatExpItem>();
            foreach (NoGisBatExpItem item in chkLstBatItem.CheckedItems)
            {
                items.Add(item);
                foreach (int iareatype in item.AreaTypeIDDic.Keys)
                {
                    if (!iAreatypeList.Contains(iareatype))
                    {
                        iAreatypeList.Add(iareatype);
                    }
                }
            }
            initStatPack(iAreatypeList);
            Dictionary<NoGisBatExpItem, List<ReporterTemplate>> dic = bindingReport(items);
            bkWorker.RunWorkerAsync(new object[] { period, dic, filePath });
        }

        private void initStatPack(List<int> iAreatypeList)
        {
            CommonNoGisStatForm.DistrictAllAreaNameDic.Clear();
            CommonNoGisStatForm.AllAreaNameDic.Clear();

            if (MainModel.User.DBID == -1) //选用全省
            {
                CommonNoGisStatForm.usingProvince = true;
                List<int> ignoreIds = new List<int>();//过滤掉省库
#if Guangdong
                ignoreIds.Add(1);//1 为省库
#elif ShanxiJin
                ignoreIds.Add(12);//"山西省",
                ignoreIds.Add(13);//"山西省专项",
#endif
                int count = DistrictManager.GetInstance().getDistrictCount();
                for (int districtId = 1; districtId < count; districtId++) //读取各地市的地点字典
                {
                    if (ignoreIds.Contains(districtId))
                    {
                        continue;
                    }

                    bool isConnected = queryDistrictAreaName(iAreatypeList, districtId);
                    if (!isConnected)
                    {
                        return;
                    }
                }
            }
            else
            {
                queryDistrictAreaName(iAreatypeList, MainModel.DistrictID);
            }
        }

        private bool queryDistrictAreaName(List<int> iAreatypeList, int districtId)
        {
            ClientProxy cp = new ClientProxy();
            try
            {
                if (cp.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, districtId) != ConnectResult.Success)
                {
                    MessageBox.Show("数据库连接失败!");
                    return false;
                }
                addDistrictAllAreaNameDic(iAreatypeList, districtId, cp);
            }
            catch (Exception err)
            {
                MessageBox.Show("查询出错：" + err.Message);
            }
            finally
            {
                try { cp.Close(); }
                catch
                {
                    //continue
                }
            }
            return true;
        }

        private void addDistrictAllAreaNameDic(List<int> iAreatypeList, int districtId, ClientProxy cp)
        {
            Dictionary<int, Dictionary<int, IDNamePair>> allAreaNameDic = new Dictionary<int, Dictionary<int, IDNamePair>>();
            foreach (int areaTypeId in iAreatypeList)
            {
                List<IDNamePair> curAreaNameList = QueryFuncHelper.GetAreaInfoList(cp, areaTypeId);

                Dictionary<int, IDNamePair> dic = new Dictionary<int, IDNamePair>();
                foreach (IDNamePair pair in curAreaNameList)
                {
                    pair.districtId = districtId;
                    dic[pair.id] = pair;
                }
                allAreaNameDic[areaTypeId] = dic;
            }
            CommonNoGisStatForm.DistrictAllAreaNameDic.Add(districtId, allAreaNameDic);
        }

        /// <summary>
        /// 登录账号不同，需要修正导出内容
        /// </summary>
        /// <returns></returns>
        private Dictionary<NoGisBatExpItem, List<ReporterTemplate>> bindingReport(List<NoGisBatExpItem> expItems)
        {
            Dictionary<NoGisBatExpItem, List<ReporterTemplate>> dic = new Dictionary<NoGisBatExpItem, List<ReporterTemplate>>();
            foreach (NoGisBatExpItem item in expItems)
            {
                List<ReporterTemplate> rptSet = new List<ReporterTemplate>();
                dic[item] = rptSet;
                for (int i = 0; i < chkLstReport.Items.Count; i++)
                {
                    ReporterTemplate rpt = chkLstReport.Items[i] as ReporterTemplate;
                    if (item.ReportNames.Contains(rpt.Name))
                    {
                        rptSet.Add(rpt);
                    }
                }
            }
            return dic;
        }

        private void visualizeExportItem(NoGisBatExpItem expItem)
        {
            txtBatItemName.Text = expItem.Name;
            chkLstReport.BeginUpdate();
            for (int i = 0; i < chkLstReport.Items.Count; i++)
            {
                chkLstReport.SetItemChecked(i, expItem.ReportNames.Contains(chkLstReport.Items[i].ToString()));
            }
            chkLstReport.EndUpdate();

            chkLstDistrict.BeginUpdate();
            for (int i = 0; i < chkLstDistrict.Items.Count; i++)
            {
                IDNamePair p = chkLstDistrict.Items[i] as IDNamePair;
                chkLstDistrict.SetItemChecked(i, expItem.DistrictIDs.Contains(p.id));
            }
            chkLstDistrict.EndUpdate();

            chkLstProj.BeginUpdate();
            for (int i = 0; i < chkLstProj.Items.Count; i++)
            {
                CategoryEnumItem x = chkLstProj.Items[i] as CategoryEnumItem;
                chkLstProj.SetItemChecked(i, expItem.ProjectIDs.Contains(x.ID));
            }
            chkLstProj.EndUpdate();

            chkLstService.BeginUpdate();
            for (int i = 0; i < chkLstService.Items.Count; i++)
            {
                CategoryEnumItem x = chkLstService.Items[i] as CategoryEnumItem;
                chkLstService.SetItemChecked(i, expItem.ServiceIDs.Contains(x.ID));
            }
            chkLstService.EndUpdate();

            chkLstAgent.BeginUpdate();
            for (int i = 0; i < chkLstAgent.Items.Count; i++)
            {
                CategoryEnumItem x = chkLstAgent.Items[i] as CategoryEnumItem;
                chkLstAgent.SetItemChecked(i, expItem.AgentIDs.Contains(x.ID));
            }
            chkLstAgent.EndUpdate();

            treeLstArea.BeginUpdate();
            foreach (DevExpress.XtraTreeList.Nodes.TreeListNode mainNode in treeLstArea.Nodes)
            {
                CategoryEnumItem x = mainNode.Tag as CategoryEnumItem;
                mainNode.Checked = expItem.AreaTypeIDDic.ContainsKey(x.ID);
                List<int> subIds = new List<int>();
                if (mainNode.Checked)
                {
                    subIds = expItem.AreaTypeIDDic[x.ID];
                }
                foreach (DevExpress.XtraTreeList.Nodes.TreeListNode subNode in mainNode.Nodes)
                {
                    x = subNode.Tag as CategoryEnumItem;
                    subNode.Checked = subIds.Contains(x.ID);
                }
            }
            treeLstArea.EndUpdate();

            cBoxMergeData.Checked = expItem.IsMergeData;
            cbxQueryFunc.SelectedIndex = expItem.QueryFunc;
            tBoxFileName.Text = expItem.FileName;

            chkFileName.Checked = expItem.CheckFileName;
            textBoxFileName.Text = expItem.FileNameFilter;
        }

        private void btnNoGisPath_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            dlg.Description = "选择存放文件夹";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                txtExportPath.Text = dlg.SelectedPath;
                NoGisBatExpCfgMngr.Instance.ExportFolderPath = dlg.SelectedPath;
            }
        }

        private NoGisBatExpItem createExportItem()
        {
            NoGisBatExpItem item = new NoGisBatExpItem();
            item.Name = txtBatItemName.Text;
            bool isValid = judgeValid();
            if (isValid)
            {
                return null;
            }
            foreach (object chkItem in chkLstReport.CheckedItems)
            {
                item.ReportNames.Add(chkItem.ToString());
            }
            foreach (IDNamePair chkItem in chkLstDistrict.CheckedItems)
            {
                item.DistrictIDs.Add(chkItem.id);
            }
            foreach (CategoryEnumItem chkItem in chkLstAgent.CheckedItems)
            {
                item.AgentIDs.Add(chkItem.ID);
            }
            foreach (CategoryEnumItem chkItem in chkLstProj.CheckedItems)
            {
                item.ProjectIDs.Add(chkItem.ID);
            }
            foreach (CategoryEnumItem chkItem in chkLstService.CheckedItems)
            {
                item.ServiceIDs.Add(chkItem.ID);
            }

            Dictionary<int, List<int>> dic = new Dictionary<int, List<int>>();
            foreach (DevExpress.XtraTreeList.Nodes.TreeListNode mainNode in treeLstArea.Nodes)
            {
                List<int> subs = new List<int>();

                foreach (DevExpress.XtraTreeList.Nodes.TreeListNode subNode in mainNode.Nodes)
                {
                    if (subNode.Checked)
                    {
                        subs.Add(((CategoryEnumItem)(subNode.Tag)).ID);
                    }
                }

                if (subs.Count > 0)
                    dic.Add(((CategoryEnumItem)(mainNode.Tag)).ID, subs);
            }
            item.IsCheck = true;
            item.AreaTypeIDDic = dic;
            item.CheckFileName = chkFileName.Checked;
            item.FileNameFilter = textBoxFileName.Text;
            item.IsMergeData = cBoxMergeData.Checked;
            item.QueryFunc = cbxQueryFunc.SelectedIndex;
            item.FileName = tBoxFileName.Text;

            return item;
        }

        private bool judgeValid()
        {
            if (chkLstReport.CheckedItems.Count == 0)
            {
                MessageBox.Show("请至少勾选一个报表！");
                return false;
            }
            if (chkLstDistrict.CheckedItems.Count == 0)
            {
                MessageBox.Show("请至少勾选一个地市！");
                return false;
            }
            if (chkLstAgent.CheckedItems.Count == 0)
            {
                MessageBox.Show("请至少勾选一个代维！");
                return false;
            }
            if (chkLstProj.CheckedItems.Count == 0)
            {
                MessageBox.Show("请至少勾选一个项目！");
                return false;
            }
            if (chkLstService.CheckedItems.Count == 0)
            {
                MessageBox.Show("请至少勾选一种业务！");
                return false;
            }
            return true;
        }

        private void btnAddExpItem_Click(object sender, EventArgs e)
        {
            string name = txtBatItemName.Text.Trim();
            if (name.Length == 0)
            {
                MessageBox.Show("名称不能空");
                return;
            }
            foreach (object item in chkLstBatItem.Items)
            {
                if (item.ToString() == name)
                {
                    MessageBox.Show("已存在相同名称的导出项！若要覆盖，请点击“保存修改”按钮");
                    return;
                }
            }
            addExportItem();
        }

        private void addExportItem()
        {
            NoGisBatExpItem expItem = createExportItem();
            if (expItem != null)
            {
                NoGisBatExpCfgMngr.Instance.Items.Add(expItem);
                NoGisBatExpCfgMngr.Instance.Save();
                freshBatItemView(expItem);
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            string name = txtBatItemName.Text.Trim();
            if (name.Length == 0)
            {
                MessageBox.Show("名称不能空");
                return;
            }
            int i = 0;
            foreach (NoGisBatExpItem item in chkLstBatItem.Items)
            {
                item.IsCheck = chkLstBatItem.GetItemChecked(i);
                i++;
            }
            NoGisBatExpItem curItem = this.chkLstBatItem.SelectedItem as NoGisBatExpItem;
            if (curItem == null)
            {
                addExportItem();
            }
            else
            {
                NoGisBatExpItem item = createExportItem();
                if (item != null)
                {
                    item.IsCheck = curItem.IsCheck;
                    curItem.Param = item.Param;
                    NoGisBatExpCfgMngr.Instance.Save();
                    freshBatItemView(curItem);
                }
            }
        }

        private void NoGisBatExpRptForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                Visible = false;
            }
        }

        private void chkLstBatItem_SelectedIndexChanged(object sender, EventArgs e)
        {
            NoGisBatExpItem item = chkLstBatItem.SelectedItem as NoGisBatExpItem;
            if (item != null)
            {
                visualizeExportItem(item);
            }
        }

        private void chkAllRpt_CheckedChanged(object sender, EventArgs e)
        {
            for (int i = 0; i < chkLstReport.Items.Count; i++)
            {
                chkLstReport.SetItemChecked(i, chkAllRpt.Checked);
            }
        }

        private void chkAllDistrict_CheckedChanged(object sender, EventArgs e)
        {
            for (int i = 0; i < chkLstDistrict.Items.Count; i++)
            {
                chkLstDistrict.SetItemChecked(i, chkAllDistrict.Checked);
            }
        }

        private void chkAllProj_CheckedChanged(object sender, EventArgs e)
        {
            for (int i = 0; i < chkLstProj.Items.Count; i++)
            {
                chkLstProj.SetItemChecked(i, chkAllProj.Checked);
            }
        }

        private void chkAllService_CheckedChanged(object sender, EventArgs e)
        {
            for (int i = 0; i < chkLstService.Items.Count; i++)
            {
                chkLstService.SetItemChecked(i, chkAllService.Checked);
            }
        }

        private void chkAllAgent_CheckedChanged(object sender, EventArgs e)
        {
            for (int i = 0; i < chkLstAgent.Items.Count; i++)
            {
                chkLstAgent.SetItemChecked(i, chkAllAgent.Checked);
            }
        }

        private void bkWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            btnRun.Enabled = false;
            cbxQueryFunc.Enabled = false;
            object[] args = e.Argument as object[];
            TimePeriod period = args[0] as TimePeriod;
            Dictionary<NoGisBatExpItem, List<ReporterTemplate>> dic = args[1] as Dictionary<NoGisBatExpItem, List<ReporterTemplate>>;
            string filePath = args[2] as string;
            dealNoGisBatExpItem(period, dic, filePath);
#if PermissionControl_DataExport
            ExportResultSecurityHelper.ExportSecurity(this.LogItemSrc, ref filePath, exportCond);
#else 
            ExportResultSecurityHelper.ExportSecurity(this.LogItemSrc, ref filePath, null);
#endif
            bkWorker.ReportProgress(100, "本次导出完毕");
        }

        private void dealNoGisBatExpItem(TimePeriod period, Dictionary<NoGisBatExpItem, List<ReporterTemplate>> dic, string filePath)
        {
            int rptNum = 0;
            int curRptIdx = 0;
            foreach (NoGisBatExpItem item in dic.Keys)
            {
                rptNum += dic[item].Count * item.AreaTypeIDDic.Count;
            }
            bkWorker.ReportProgress(0, string.Format("共{0}个报表待处理", rptNum));
            foreach (NoGisBatExpItem item in dic.Keys)
            {
                string expFilePath = string.Format(filePath + "\\" + item.Name);
                if (!System.IO.Directory.Exists(expFilePath))
                {
                    System.IO.Directory.CreateDirectory(expFilePath);
                }
                dealAreaTypeIDDic(period, dic, rptNum, ref curRptIdx, item, expFilePath);
            }
        }

        private void dealAreaTypeIDDic(TimePeriod period, Dictionary<NoGisBatExpItem, List<ReporterTemplate>> dic, int rptNum, 
            ref int curRptIdx, NoGisBatExpItem item, string expFilePath)
        {
            foreach (int areaType in item.AreaTypeIDDic.Keys)
            {
                string areaName = getAreaName(areaType);
                NoGisStatCond cond;
                QueryCondition condition;
                getCondition(period, item, areaType, out cond, out condition);

                foreach (ReporterTemplate rpt in dic[item])
                {
                    curRptIdx++;
                    condition.CarrierTypes = rpt.CarrierIDs;
                    bkWorker.ReportProgress(curRptIdx * 100 / rptNum
                        , string.Format("{0}/{1} {5} 正在统计区域：{2} 报表：{3} {4}"
                        , curRptIdx, rptNum, areaName, item.Name, rpt.Name, "step 1:"));
                    List<NPOIRow> rows = getRowsByQueryFunc(item, cond, rpt);

                    if (timerExport == null || !timerExport.Enabled || rows.Count != 1)
                    {
                        bkWorker.ReportProgress(curRptIdx * 100 / rptNum
                         , string.Format("{0}/{1} {5} 正在导出区域：{2} 报表：{3} {4}"
                         , curRptIdx, rptNum, areaName, item.Name, rpt.Name, "step 2:"));

                        string fileName = string.Format(expFilePath + "\\" + formatFileName(item.FileName, areaName, rpt.Name));
                        ExcelNPOIManager.AutoExportExcel(rows, fileName);
                    }
                }
            }
        }

        private List<NPOIRow> getRowsByQueryFunc(NoGisBatExpItem item, NoGisStatCond cond, ReporterTemplate rpt)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            if (item.QueryFunc == 1)
            {
                //旧版
                List<FileAreaStatInfo> dataSet = null;
                dataSet = queryOneReport(cond, rpt);
                dataSet.Sort(FileAreaStatInfo.GetCompareByDistrictId());
                rows = StatShowHelper.CreateReport(mainModel, rpt, dataSet, true);
            }
            else if (item.QueryFunc == 0)
            {
                //新版
                List<AreaStatInfo> dataSetNew = null;
                dataSetNew = queryOneReport(cond, rpt, item.IsMergeData);
                dataSetNew.Sort();
                QueryAreaStat query = new QueryAreaStat(rpt, item.IsMergeData, null);
                rows = query.CreateReport(rpt, dataSetNew);
            }
            else if (item.QueryFunc == 2)
            {
                rows = getCheckOrderResult(item, cond, rpt);

                //StringBuilder sb = new StringBuilder();
                ////由于质检文件是直接用CommonNoGisStatForm中的接口,而CommonNoGisStatForm
                ////中是按地市分开处理,而批量报表是全部地市一起处理.所以此处需分开地市处理
                //foreach (int cityID in item.DistrictIDs)
                //{
                //    //添加能查询到的文件ID
                //    addDistrictFileID(cond, dicID2Data, sb, cityID);
                //}
                //if (sb.ToString() != string.Empty)
                //{
                //    cond.queryCondition.FileName = sb.ToString().TrimEnd(',');
                //    cond.queryCondition.NameFilterType = FileFilterType.ByMark_ID;

                //    dataSetQualityCheck = queryOneReport(cond, rpt, item.IsMergeData, CheckFileDic);
                //    dataSetQualityCheck.Sort();
                //    QueryAreaStat query = new QueryAreaStat(rpt, item.IsMergeData, null);
                //    rows = query.CreateReport(rpt, dataSetQualityCheck);
                //}
            }

            return rows;
        }

        private List<NPOIRow> getCheckOrderResult(NoGisBatExpItem item, NoGisStatCond cond, ReporterTemplate rpt)
        {
            List<NPOIRow> rows;
            //质检
            List<AreaStatInfo> dataSetQualityCheck = null;
            Dictionary<int, List<OrderCheckFileItem>> dicID2Data = null;

            //根据工单库存储过程查询未验证工单文件
            QueryOrderCheckFileInfo queryAreaStatFilter = new QueryOrderCheckFileInfo();
            queryAreaStatFilter.Query();
            dicID2Data = queryAreaStatFilter.GetResult();

            Dictionary<string, AreaStatInfo> areaKeyDataDic = new Dictionary<string, AreaStatInfo>();
            foreach (int cityID in item.DistrictIDs)
            {
                List<OrderCheckFileItem> chkFiles;
                if (!dicID2Data.TryGetValue(cityID, out chkFiles))
                {
                    continue;
                }

                cond.queryCondition.FileName = null;
                cond.queryCondition.DistrictID = cityID;
                Dictionary<int, List<OrderCheckFileItem>> checkFileDic = getCheckFiles(cond.queryCondition, chkFiles);
                if (checkFileDic.Count == 0)
                {
                    continue;
                }

                StringBuilder sb = new StringBuilder();
                foreach (int fileID in checkFileDic.Keys)
                {
                    sb.Append(fileID);
                    sb.Append(",");
                }

                cond.queryCondition.FileName = sb.ToString().TrimEnd(',');
                cond.queryCondition.NameFilterType = FileFilterType.ByMark_ID;

                //用于匹配字段 - 统计表区域类型名
                foreach (int iCity in CommonNoGisStatForm.DistrictAllAreaNameDic.Keys)
                {
                    if (cityID == iCity)
                    {
                        CommonNoGisStatForm.AllAreaNameDic = CommonNoGisStatForm.DistrictAllAreaNameDic[cityID];
                    }
                }

                QueryAreaStat curQuery = new QueryAreaStat(rpt, item.IsMergeData, areaKeyDataDic);
                curQuery.CheckFileIDDic = checkFileDic;
                curQuery.SetQueryCondition(cond);
                curQuery.Query();
                areaKeyDataDic = curQuery.AreaKeyDataDic;
                curQuery.ClearDataAfterQuery();
            }

            foreach (AreaStatInfo info in areaKeyDataDic.Values)
            {
                info.KPIData.FinalMtMoGroup();
            }
            dataSetQualityCheck = new List<AreaStatInfo>(areaKeyDataDic.Values);
            dataSetQualityCheck.Sort();
            QueryAreaStat query = new QueryAreaStat(rpt, item.IsMergeData, null);
            rows = query.CreateReport(rpt, dataSetQualityCheck);
            return rows;
        }

        //private void addDistrictFileID(NoGisStatCond cond, Dictionary<int, List<OrderCheckFileItem>> dicID2Data, StringBuilder sb, int cityID)
        //{
        //    List<OrderCheckFileItem> chkFiles;
        //    if (dicID2Data.TryGetValue(cityID, out chkFiles))
        //    {
        //        cond.queryCondition.DistrictID = cityID;
        //        Dictionary<int, List<OrderCheckFileItem>> checkFileDic = getCheckFiles(cond.queryCondition, chkFiles);

        //        if (checkFileDic.Count != 0)
        //        {
        //            foreach (int fileID in checkFileDic.Keys)
        //            {
        //                sb.Append(fileID);
        //                sb.Append(",");
        //            }
        //        }
        //    }
        //}

        private static void getCondition(TimePeriod period, NoGisBatExpItem item, int areaType, out NoGisStatCond cond, out QueryCondition condition)
        {
            cond = new NoGisStatCond();
            cond.method = 2;//大概也许可能2就是代表不是byround查询吧。。。
            cond.areaType = areaType;
            cond.areaList = item.AreaTypeIDDic[areaType];
            cond.yearMonthList.Add(new YearMonthUnit((int)(JavaDate.GetMilliseconds(period.BeginTime.Date) / 1000)
                , (int)(JavaDate.GetMilliseconds(period.EndTime.Date) / 1000)));

            condition = new QueryCondition();
            condition.Areas = new Dictionary<int, List<int>>();
            condition.DistrictIDs = item.DistrictIDs;
            condition.Periods.Add(period);
            condition.Projects = item.ProjectIDs;
            condition.ServiceTypes = item.ServiceIDs;
            condition.IsAllAgent = false;
            condition.AgentIds = item.AgentIDs;
            if (item.CheckFileName)
            {
                int numSp = 0;
                condition.FileName = QueryCondition.MakeFileFilterString(item.FileNameFilter, ref numSp);
            }
            cond.queryCondition = condition;
        }

        private static string getAreaName(int areaType)
        {
            string areaName = "areaType";
            foreach (CategoryEnumItem eItem in ((CategoryEnum)CategoryManager.GetInstance()["AreaType"]).Items)
            {
                if (eItem.ID == areaType)
                {
                    areaName = eItem.Name;
                    break;
                }
            }

            return areaName;
        }

        private string formatFileName(string fileName, string areaName, string rptName)
        {
            string result = checkFileName(fileName).Replace("(区域名)", areaName).Replace("(报表名)", rptName) + ".xlsx";

            int indexFront = -1;
            while ((indexFront = result.IndexOf("(:")) >= 0)
            {
                int indexBack = result.IndexOf(":)");
                if (indexBack - 2 <= indexFront)
                    break;

                string strFront = result.Substring(0, indexFront);
                string strTime = result.Substring(indexFront + 2, indexBack - indexFront - 2);
                string strBack = result.Substring(indexBack + 2, result.Length - indexBack - 2);
                strTime = DateTime.Now.ToString(strTime);
                result = strFront + strTime + strBack;
            }

            return result;
        }

        private string checkFileName(string fileName)
        {
            if (fileName == null || fileName.IndexOf("(区域名)") < 0 || fileName.IndexOf("(报表名)") < 0)
                return "(区域名)_(报表名)";
            else
                return fileName;
        }

        private Dictionary<int, List<OrderCheckFileItem>> getCheckFiles(QueryCondition cond, List<OrderCheckFileItem> chkfiles)
        {
            Dictionary<int, List<OrderCheckFileItem>> dic = new Dictionary<int, List<OrderCheckFileItem>>();
            DIYQueryFileInfo queryAllFiles = new DIYQueryFileInfo(MainModel);
            queryAllFiles.IsShowFileInfoForm = false;
            queryAllFiles.SetQueryCondition(cond);
            queryAllFiles.Query();
            List<MasterCom.RAMS.Model.FileInfo> files = MainModel.FileInfos;
            foreach (OrderCheckFileItem chkFi in chkfiles)
            {
                foreach (MasterCom.RAMS.Model.FileInfo fi in files)
                {
                    if (fi.Name.Contains(chkFi.FileName))
                    {
                        List<OrderCheckFileItem> temp;
                        if (!dic.TryGetValue(fi.ID, out temp))
                        {
                            temp = new List<OrderCheckFileItem>();
                            dic[fi.ID] = temp;
                        }
                        temp.Add(chkFi);
                    }
                }
            }
            return dic;
        }

        private List<FileAreaStatInfo> queryOneReport(NoGisStatCond condition, ReporterTemplate report)
        {
            List<FileAreaStatInfo> results = new List<FileAreaStatInfo>();
            foreach (int id in condition.queryCondition.DistrictIDs)
            {
                foreach (int iCity in CommonNoGisStatForm.DistrictAllAreaNameDic.Keys)
                {
                    if (id == iCity)
                    {
                        CommonNoGisStatForm.AllAreaNameDic = CommonNoGisStatForm.DistrictAllAreaNameDic[id];
                    }
                }
                DIYStatAreaNoGisQuery query = new DIYStatAreaNoGisQuery(MainModel, report);
                query.DistrictId = id;
                query.FillNoGisCondition(condition);
                query.Query();
                results.AddRange(query.ResultStatList);
            }
            return results;
        }
        private List<AreaStatInfo> queryOneReport(NoGisStatCond curCond, ReporterTemplate report, bool IsMergeData)
        {
            Dictionary<string, AreaStatInfo> areaKeyDataDic = new Dictionary<string, AreaStatInfo>();
            foreach (int id in curCond.queryCondition.DistrictIDs)
            {
                foreach (int iCity in CommonNoGisStatForm.DistrictAllAreaNameDic.Keys)
                {
                    if (id == iCity)
                    {
                        CommonNoGisStatForm.AllAreaNameDic = CommonNoGisStatForm.DistrictAllAreaNameDic[id];
                    }
                }
                curCond.queryCondition.DistrictID = id;
                QueryAreaStat curQuery = new QueryAreaStat(report, IsMergeData, areaKeyDataDic);
                curQuery.SetQueryCondition(curCond);
                curQuery.Query();
                areaKeyDataDic = curQuery.AreaKeyDataDic;
                curQuery.ClearDataAfterQuery();
            }
            foreach (AreaStatInfo item in areaKeyDataDic.Values)
            {
                item.KPIData.FinalMtMoGroup();
            }
            return new List<AreaStatInfo>(areaKeyDataDic.Values);
        }

        //按质检文件查询专用
        //查询条件增加质检文件
        //private List<AreaStatInfo> queryOneReport(NoGisStatCond curCond, ReporterTemplate report, bool IsMergeData, Dictionary<int, List<OrderCheckFileItem>> checkFileDic)
        //{
        //    Dictionary<string, AreaStatInfo> areaKeyDataDic = new Dictionary<string, AreaStatInfo>();
        //    foreach (int id in curCond.queryCondition.DistrictIDs)
        //    {
        //        foreach (int iCity in CommonNoGisStatForm.DistrictAllAreaNameDic.Keys)
        //        {
        //            if (id == iCity)
        //            {
        //                CommonNoGisStatForm.AllAreaNameDic = CommonNoGisStatForm.DistrictAllAreaNameDic[id];
        //            }
        //        }
        //        curCond.queryCondition.DistrictID = id;
        //        QueryAreaStat curQuery = new QueryAreaStat(report, IsMergeData, areaKeyDataDic);
        //        curQuery.CheckFileIDDic = checkFileDic;
        //        curQuery.SetQueryCondition(curCond);
        //        curQuery.Query();
        //        areaKeyDataDic = curQuery.AreaKeyDataDic;
        //        curQuery.ClearDataAfterQuery();
        //    }
        //    foreach (AreaStatInfo item in areaKeyDataDic.Values)
        //    {
        //        item.KPIData.FinalMtMoGroup();
        //    }
        //    return new List<AreaStatInfo>(areaKeyDataDic.Values);
        //}

        private void bkWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            if (timerExport == null || !timerExport.Enabled)
            {
                btnRun.Enabled = true;
            }

            cbxQueryFunc.Enabled = true;
            if (!Visible)
            {
                MessageBox.Show(this, "本次批处理导出已完毕。");
            }
            btnTimer_Click(null, null);
        }

        private void bkWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            this.progBar.Text = e.ProgressPercentage.ToString();
            if (e.UserState != null)
            {
                this.txtProgInfo.AppendText(e.UserState.ToString());
                this.txtProgInfo.AppendText(Environment.NewLine);
            }
        }

        private void miClearTxt_Click(object sender, EventArgs e)
        {
            txtProgInfo.Text = string.Empty;
        }

        private void chkAllRegion_CheckedChanged(object sender, EventArgs e)
        {
            treeLstArea.BeginUpdate();
            foreach (TreeListNode node in treeLstArea.Nodes)
            {
                setNodeStat(node, chkAllRegion.Checked);
            }
            treeLstArea.EndUpdate();
        }

        private void setNodeStat(TreeListNode node, bool check)
        {
            node.Checked = check;
            foreach (TreeListNode subNode in node.Nodes)
            {
                setNodeStat(subNode, check);
            }
        }

        private void ccbePort_EditValueChanged(object sender, EventArgs e)
        {
            textBoxFileName.Text = "";
            string[] attr = ccbePort.Text.Split(',');
            if (attr.Length == 1 && attr[0] == "")
            {
                return;
            }
            StringBuilder str = new StringBuilder();
            for (int i = 0; i < attr.Length; i++)
            {
                if (i > 0 || str.Length > 0)
                {
                    str.Append(" or ");
                }
                if (cbxfileNumType.SelectedIndex == 0)
                {
                    str.Append("(" + attr[i].Trim() + ")");
                }
                else if (cbxfileNumType.SelectedIndex == 1)
                {
                    str.Append("ms" + attr[i].Trim());
                }
            }
            textBoxFileName.Text = str.ToString();
        }

        private void btnClearPort_Click(object sender, EventArgs e)
        {
            ccbePort.Reset();
            ccbePort.Text = string.Empty;
            ccbePort.RefreshEditValue();
            this.textBoxFileName.Text = string.Empty;
        }

        private void miRemoveCur_Click(object sender, EventArgs e)
        {
            if (!isSureRemoveItem())
            {
                return;
            }
            NoGisBatExpItem item = chkLstBatItem.SelectedItem as NoGisBatExpItem;
            if (item != null)
            {
                NoGisBatExpCfgMngr.Instance.Items.Remove(item);
                NoGisBatExpCfgMngr.Instance.Save();
                freshBatItemView(null);
            }
        }

        private void miRemoveChk_Click(object sender, EventArgs e)
        {
            if (!isSureRemoveItem())
            {
                return;
            }
            foreach (NoGisBatExpItem item in chkLstBatItem.CheckedItems)
            {
                NoGisBatExpCfgMngr.Instance.Items.Remove(item);
            }
            NoGisBatExpCfgMngr.Instance.Save();
            freshBatItemView(null);
        }
        private bool isSureRemoveItem()
        {
            bool isOK = false;
            if (DialogResult.Yes == MessageBox.Show(this, "确认删除所选项，删除后不可恢复。", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question))
            {
                isOK = true;
            }
            return isOK;
        }

        private void btnSetFileName_Click(object sender, EventArgs e)
        {
            MasterCom.RAMS.ZTFunc.NoGisBatExpRptSetFileNameForm settingForm = new ZTFunc.NoGisBatExpRptSetFileNameForm(tBoxFileName.Text);
            settingForm.ShowDialog();
            if (settingForm.DialogResult == DialogResult.OK)
            {
                tBoxFileName.Text = fileNameDIY;
            }
        }

        private int exportSpan = 0;
        private int residueTime = 0;
        private Timer timerExport = null;
        private void btnTimer_Click(object sender, EventArgs e)
        {
            if (btnTimer.Text.Equals("启动"))
            {
                residueTime = exportSpan = (int)nupTimer.Value;
                gBoxTimer.Text = "已启动定时批量导出 (" + exportSpan.ToString() + "分钟后导出报表)";
                btnTimer.Text = "关闭";

                setTimerExport();
            }
            else
            {
                btnRun.Enabled = true;
                if (timerExport != null)
                    timerExport.Stop();
                gBoxTimer.Text = "未启动定时批量导出";
                btnTimer.Text = "启动";
            }
        }

        private void setTimerExport()
        {
            if (timerExport == null)
            {
                timerExport = new Timer();
                timerExport.Interval = 60 * 1000;
                timerExport.Tick += delegate (object o, EventArgs args)
                {
                    residueTime--;
                    if (residueTime == 0)
                    {
                        //为了真正实现时间间隔为设定值在执行导出时需把timer暂停,
                        //同时也防止timer在执行下一个任务时上一个任务还未完成
                        timerExport.Stop();
                        btnRun_Click(null, null);
                        residueTime = exportSpan;
                        gBoxTimer.Text = "已启动定时批量导出 (" + residueTime.ToString() + "分钟后导出报表)";        
                    }
                    else
                    {
                        gBoxTimer.Text = "已启动定时批量导出 (" + residueTime.ToString() + "分钟后导出报表)";
                    }
                };
            }
            btnRun.Enabled = false;
            timerExport.Start();
        }
    }
}
