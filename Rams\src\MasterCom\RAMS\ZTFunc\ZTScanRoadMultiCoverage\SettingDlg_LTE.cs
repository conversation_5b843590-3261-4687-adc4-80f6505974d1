﻿using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SettingDlg_LTE : BaseDialog
    {
        public SettingDlg_LTE(ServiceName serviceName)
        {
            InitializeComponent();

            if (serviceName == ServiceName.NBIOTSCAN)
            {
                groupBoxRemove.Visible = false;
                groupBoxVaild.Location = new Point(48, 12);
                groupBoxRelative.Location = new Point(48, 117);
                groupBoxAbsolute.Location = new Point(48, 183);
                chkSaveTestPoint.Location = new Point(61, 258);
                labelControl3.Location = new Point(162, 260);
                ClientSize = new System.Drawing.Size(534, 322);
            }
        }

        public void SetCondition(Condition_LTE condition)
        {
            if (condition == null)
            {
                return;
            }
            numAbsValue.Value = (decimal)condition.AbsoluteValue;
            numBandValue.Value = (decimal)condition.CoverBandDiff;
            numMaxValidValue.Value = (decimal)condition.ValidValue;
            checkEditDualBandJT.Checked = condition.CheckDualBandJT;
            freqBandControl1.chkFreqBand.Checked = condition.CheckFreqBandOnly;
            checkEditMultiBandDiffFreq.Checked = condition.CheckBandDiffFreq;
            //设置频点条件
            if (condition.ListFreqPoint != null && condition.ListFreqPoint.Count > 0)
            {
                freqBandControl1.lvFreqBand.Items.Clear();
                foreach (FreqPoint fp in condition.ListFreqPoint)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = fp.Carrier + "_" + fp.FreqPointName;
                    lvi.Tag = fp;
                    freqBandControl1.lvFreqBand.Items.Add(lvi);
                }
            }
            chkSaveTestPoint.Checked = condition.SaveTestPoint;
        }

        public Condition_LTE GetCondition()
        {
            Condition_LTE condition = new Condition_LTE();
            condition.AbsoluteValue = (int)numAbsValue.Value;
            condition.CoverBandDiff = (int)numBandValue.Value;
            condition.ValidValue = (int)numMaxValidValue.Value;
            condition.CheckDualBandJT = checkEditDualBandJT.Checked;
            condition.CheckFreqBandOnly = freqBandControl1.chkFreqBand.Checked;
            condition.CheckBandDiffFreq = checkEditMultiBandDiffFreq.Checked;
            //得到频点集合
            condition.ListFreqPoint = freqBandControl1.GetListViewItems();
            condition.SaveTestPoint = chkSaveTestPoint.Checked;
            return condition;
        }

        private void chkFreqBand_CheckedChanged(object sender, EventArgs e)
        {
            checkEditDualBandJT.Enabled = !freqBandControl1.chkFreqBand.Checked;
            checkEditMultiBandDiffFreq.Enabled = !freqBandControl1.chkFreqBand.Checked;
        }

        private void checkEditTwoEarfcn_CheckedChanged(object sender, EventArgs e)
        {
            freqBandControl1.chkFreqBand.Enabled = !checkEditDualBandJT.Checked;
            checkEditMultiBandDiffFreq.Enabled = !checkEditDualBandJT.Checked;

            if (checkEditDualBandJT.Checked)
            {
                freqBandControl1.chkFreqBand.Checked = false;
                checkEditMultiBandDiffFreq.Checked = false;
            }
        }

        private void checkEditMultiBandDiffFreq_CheckedChanged(object sender, EventArgs e)
        {
            checkEditDualBandJT.Enabled = !checkEditMultiBandDiffFreq.Checked;
            freqBandControl1.chkFreqBand.Enabled = !checkEditMultiBandDiffFreq.Checked;
            if (checkEditMultiBandDiffFreq.Checked)
            {
                checkEditDualBandJT.Checked = false;
                freqBandControl1.chkFreqBand.Checked = false;
            }
        }

        private void chkAllRsrp_CheckedChanged(object sender, EventArgs e)
        {
            numAllRsrp.Enabled = this.chkAllRsrp.Checked;
        }

        /// <summary>
        /// 确认操作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOK_Click(object sender, EventArgs e)
        {
            if (freqBandControl1.chkFreqBand.Checked && freqBandControl1.lvFreqBand.Items.Count <= 0)
            {
                XtraMessageBox.Show("请选择频点", "提示");
                DialogResult = DialogResult.None;
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void SettingDlg_LTENew_Load(object sender, EventArgs e)
        {
            freqBandControl1.ChkFreqBandChange_click += new FreqBandControl.ChkChangeDelegate(chkFreqBand_CheckedChanged);//把事件绑定到自定义的委托上
        }
    }
}
