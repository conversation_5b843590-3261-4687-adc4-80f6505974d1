﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatByRegion : QueryKPIStatBase
    {
        protected bool isStatAsGrid = false;  //是否为以栅格为纬度进行统计
        protected bool isQueryByTimePeriod;         //是否按时间查询
        List<TimePeriod> periodList = null;
        protected string allData = "按时间段总和数据:   ";
        TimePeriod timePeriodTemp=null;
        public QueryKPIStatByRegion():base()
        { }
        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.grid;
        }

        public override string Name
        {
            get
            {
                return "区域KPI统计";
            }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11019, this.Name);
        }
        protected override bool getConditionBeforeQuery()
        {
            ReportPickerDlg dlg = new ReportPickerDlg();
            dlg.SetIsQueryByTimePeriod();  //设置时间选项可选
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curReportStyle = dlg.Report;
            isQueryAllParams = dlg.IsQueryAllParams;
            isQueryByTimePeriod = dlg.IsQueryByTimePeriod;
            periodList = new List<TimePeriod>();
            if (isQueryByTimePeriod)  //用时间查询的话，把所有选择的时间段全部挑选出来
            {
                QueryCondition qCondition = MainModel.GetInstance().MainForm.GetQueryConditionTimePeriods();
                foreach (TimePeriod tp in qCondition.Periods)
                {
                    periodList.Add(tp);
                }
            }
            else
            {
                timePeriodTemp = null;
            }
            KpiDataManager = new KPIDataManager();
            return true;
        }
        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        protected override void AddGeographicFilter(Package package)
        {
            if (isQueringEvent)
            {
                AddDIYEndOpFlag(package);
                this.AddDIYRegion_Sample(package);
            }
            else
            {
                this.AddDIYRegion_Intersect(package);
            }
        }
        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
            {
                return;
            }
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);

            if (isQueryByTimePeriod)
            {
                timePeriodTemp = getTimePeriod(getDateTimeByKPIData(singleStatData));
                if (timePeriodTemp == null) return;//如果找不到时间，则直接返回
            }
            List<ResvRegion> regs = getStatImgIntersectRegions(lng, lat);
            foreach (ResvRegion reg in regs)//为子区域添加指标
            {
                object key = getKeyStringByTime(timePeriodTemp, reg);
                KpiDataManager.AddStatData(reg.RegionName, key, fi, singleStatData
                    , this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
            }
            mergeRootNodeData(grid.CenterLng, grid.CenterLat, fi, singleStatData);
        }

        protected override void handleStatEvent(Event evt)
        {
            if (!condition.Geometorys.GeoOp.Contains(evt.Longitude, evt.Latitude))
            {
                return;
            }
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);

            if (isQueryByTimePeriod)
            {
                timePeriodTemp = getTimePeriod(evt.DateTime);
                if (timePeriodTemp == null) return;//如果找不到时间，则直接返回
            }
            List<ResvRegion> regs = getEventInRegions(evt);
            foreach (ResvRegion reg in regs)//为子区域添加指标
            {
                object key = getKeyStringByTime(timePeriodTemp, reg);
                KpiDataManager.AddStatData(reg.RegionName, key, fi, eventData
                    , this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
            }
            mergeRootNodeData(evt.Longitude, evt.Latitude, fi, eventData);
        }

        protected virtual void mergeRootNodeData(double longitude, double latitude, FileInfo fi, KPIStatDataBase kpiData)
        {
            if (MainModel.RootNodeGeometrys && Condition.Geometorys.SelectedResvRegionDic != null
                && Condition.Geometorys.SelectedResvRegionDic.Count > 0)
            {
                //匹配该点所属的根节点区域信息
                Dictionary<string, List<ResvRegion>> resvRegionDic = getResvRegionDic(longitude, latitude);
                addValidStatData(fi, kpiData, resvRegionDic);
            }
        }

        private Dictionary<string, List<ResvRegion>> getResvRegionDic(double longitude, double latitude)
        {
            Dictionary<string, List<ResvRegion>> resvRegionDic = new Dictionary<string, List<ResvRegion>>();
            foreach (var varPair in MainModel.SearchGeometrys.SelectedResvRegionDic)
            {
                foreach (ResvRegion res in varPair.Value)
                {
                    if (res.GeoOp.CheckPointInRegion(longitude, latitude)
                        && !resvRegionDic.ContainsKey(varPair.Key))
                    {
                        resvRegionDic.Add(varPair.Key, varPair.Value);
                        break;
                    }
                }
            }

            return resvRegionDic;
        }

        private void addValidStatData(FileInfo fi, KPIStatDataBase kpiData, Dictionary<string, List<ResvRegion>> resvRegionDic)
        {
            //为根节点区域添加指标
            bool isSum = isStatAsGrid || MainModel.MultiGeometrys;
            foreach (var varPair in resvRegionDic)
            {
                if (isQueryByTimePeriod)
                {
                    string key = varPair.Key + "   " + timePeriodTemp.ToString();
                    KpiDataManager.AddStatData(varPair.Key, key, isSum, fi, kpiData
                        , this.curReportStyle != null && this.curReportStyle.HasGridPerCell);  //按时间分类
                }
                else
                {
                    KpiDataManager.AddStatData(varPair.Key, varPair, isSum, fi, kpiData
                   , this.curReportStyle != null && this.curReportStyle.HasGridPerCell);  //按总根节点区域
                }
            }
            if (isQueryByTimePeriod)
            {//针对总和数据  进行添加并处理 只有在多时间段时才会处理
                string allDataKey = allData + timePeriodTemp.ToString();
                KpiDataManager.AddStatData(allData, allDataKey, isSum, fi, kpiData, false);
            }
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            //为各区域添加空指标，以确保所有区域的信息都添加到KpiDataManager中
            if (KpiDataManager != null && MainModel.MultiGeometrys
               && Condition.Geometorys.SelectedResvRegions != null
               && Condition.Geometorys.SelectedResvRegions.Count > 0)
            {
                bool isSum = isStatAsGrid;//区域栅格统计中的子区域和根节点区域指标都是以汇总的形式添加
                foreach (ResvRegion resvRegion in Condition.Geometorys.SelectedResvRegions)
                {
                    string areaInfo = "   （区域ID：" + resvRegion.ID.ToString() + "）";
                    addFinalStatData(isSum, resvRegion.RegionName, resvRegion, areaInfo);
                }
            }

            if (KpiDataManager != null && MainModel.RootNodeGeometrys
                && Condition.Geometorys.SelectedResvRegionDic != null
                && Condition.Geometorys.SelectedResvRegionDic.Count > 0)
            {
                bool isSum = isStatAsGrid || MainModel.MultiGeometrys;
                foreach (var varPair in Condition.Geometorys.SelectedResvRegionDic)
                {
                    addFinalStatData(isSum, varPair.Key, varPair, "");
                }
            }
            base.afterRecieveAllData(reservedParams);
        }

        private void addFinalStatData(bool isSum, string name, object region, string areaInfo)
        {
            if (isQueryByTimePeriod)
            {
                foreach (TimePeriod tp in periodList)
                {
                    //添加时间信息
                    string key = name + "   " + tp.ToString() + areaInfo;
                    KpiDataManager.AddStatData(name, key, isSum, null, null, false);
                }
            }
            else
            {
                KpiDataManager.AddStatData(name, region, isSum, null, null, false);
            }
        }

        protected List<ResvRegion> getStatImgIntersectRegions(double ltLng, double ltLat)
        {
            GridUnitBase grid = new GridUnitBase(ltLng, ltLat);
            return getPointInRegions(grid.CenterLng, grid.CenterLat);
        }

        protected List<ResvRegion> getEventInRegions(Model.Event evt)
        {
            return getPointInRegions(evt.Longitude, evt.Latitude);
        }

        //获取点所在的子区域信息
        protected List<ResvRegion> getPointInRegions(double longitude, double latitude)
        {
            List<ResvRegion> regs = new List<ResvRegion>();
            if (MainModel.MultiGeometrys
                && MainModel.SearchGeometrys.SelectedResvRegions != null
                && MainModel.SearchGeometrys.SelectedResvRegions.Count > 0)
            {
                foreach (ResvRegion resvRegion in MainModel.SearchGeometrys.SelectedResvRegions)
                {
                    if (resvRegion.GeoOp.CheckPointInRegion(longitude, latitude))
                    {
                        regs.Add(resvRegion);
                    }
                }
            }
            else if (!MainModel.RootNodeGeometrys
                && MainModel.SearchGeometrys.GeoOp.Contains(longitude, latitude))
            {
                regs.Add(MainModel.SearchGeometrys.RegionInfo);
            }
            return regs;
        }
        private TimePeriod getTimePeriod(DateTime dt)
        {
            TimePeriod timePeriod = null;
            foreach (TimePeriod tp in periodList)
            {
                if (tp.Contains(dt))
                {
                    timePeriod = tp;
                    break;
                }
            }
            return timePeriod;
        }
        private object getKeyStringByTime(TimePeriod timePeriod, ResvRegion reg)
        {
            string unique = "";
            if (timePeriod == null)
            {
                return reg;
            }
            else
            {
                unique = reg.RegionName + "   " + timePeriod.ToString() + "   （区域ID：" + reg.ID.ToString() + "）";
            } 
            return unique;
        }
        private DateTime getDateTimeByKPIData(KPIStatDataBase kpiData)
        {
            return JavaDate.GetDateTimeFromMilliseconds((long)kpiData[KPIStatDataBase.FileTimeKey, KPIStatDataBase.NewGridFileTimeKey, -1] * 1000L);
        }
    }
}
