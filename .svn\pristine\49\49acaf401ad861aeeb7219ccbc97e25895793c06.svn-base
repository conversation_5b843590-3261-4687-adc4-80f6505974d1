﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTTDCellReselectAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public List<ZTTDCellReselectAnaItem> resultList { get; set; } = new List<ZTTDCellReselectAnaItem>();    //保存结果
        public ZTTDCellReselectAnaCondition cellReselCondition { get; set; } = new ZTTDCellReselectAnaCondition();   //查询条件

        public ZTTDCellReselectAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = false;
        } 

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<ZTTDCellReselectAnaItem>();
        }

        ZTTDCellReselectAnaSetForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTTDCellReselectAnaSetForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                cellReselCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileMng.TestPoints;
                List<Event> eventList = fileMng.Events;

                for (int eLoop = 0; eLoop < eventList.Count; ++eLoop)
                {
                    Event e = eventList[eLoop];
                    if (e.ID != 179)
                    {
                        continue;
                    }

                    int index = -1;
                    if ((index = GetNearestTestPointIndex(e.SN, testPointList)) == -1)
                    {
                        continue;
                    }

                    addResultList(testPointList, e, index);
                }
            } 
        }

        private void addResultList(List<TestPoint> testPointList, Event e, int index)
        {
            ZTTDCellReselectAnaItem item = new ZTTDCellReselectAnaItem(e.FileName, e);

            long tpTimeHead = e.Time * 1000L + e.Millisecond - cellReselCondition.BeforeSecond * 1000L;
            long tpTimeTail = e.Time * 1000L + e.Millisecond + cellReselCondition.AfterSecond * 1000L;

            for (int i = index; i >= 0; --i)
            {
                TestPoint tp = testPointList[i];
                if ((tp.Time + cellReselCondition.BeforeSecond) * 1000L + tp.Millisecond < tpTimeHead)
                {
                    break;
                }
                item.AddBeforeTp(tp);
            }
            for (int i = index + 1; i < testPointList.Count; ++i)
            {
                TestPoint tp = testPointList[i];
                if (tpTimeTail < tp.Time * 1000L + tp.Millisecond)
                {
                    break;
                }
                item.AddAfterTp(tp);
            }

            item.SN = resultList.Count + 1;
            resultList.Add(item);
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTTDCellReselectAnaListForm).FullName);
            ZTTDCellReselectAnaListForm cellReselectAnaListForm = obj == null ? null : obj as ZTTDCellReselectAnaListForm;
            if (cellReselectAnaListForm == null || cellReselectAnaListForm.IsDisposed)
            {
                cellReselectAnaListForm = new ZTTDCellReselectAnaListForm(MainModel);
            }

            cellReselectAnaListForm.FillData(resultList);
            if (!cellReselectAnaListForm.Visible)
            {
                cellReselectAnaListForm.Show(MainModel.MainForm);
            }
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        /// <summary>
        /// tpList升序，寻找最大的index使得tpList[index] <= eventSN
        /// </summary>
        /// <param name="eventSN"></param>
        /// <param name="tpList"></param>
        /// <returns></returns>
        protected int GetNearestTestPointIndex(int eventSN, List<TestPoint> tpList)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > eventSN)
                {
                    index = i - 1;
                    break;
                }
            }
            return index;
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class ZTTDCellReselectAnaItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }

        public ZTTDCellReselectAnaCellItem SrcCellItem{ get; set; }
        public ZTTDCellReselectAnaCellItem DestCellItem { get; set; }

        public ZTTDCellReselectAnaTpItem BeforeTpItem{ get; set; }
        public ZTTDCellReselectAnaTpItem AfterTpItem { get; set; }

        public Event ReselEvt { get; set; }

        public ZTTDCellReselectAnaItem(string fileName, Event reselEvt)
        {
            FileName = fileName;
            ReselEvt = reselEvt;
            SrcCellItem = new ZTTDCellReselectAnaCellItem();
            DestCellItem = new ZTTDCellReselectAnaCellItem();
            BeforeTpItem = new ZTTDCellReselectAnaTpItem();
            AfterTpItem = new ZTTDCellReselectAnaTpItem();

            SetCellItem(SrcCellItem, (int)reselEvt["LAC"], (int)reselEvt["CI"], reselEvt);
            SetCellItem(DestCellItem, (int)reselEvt["TargetLAC"], (int)reselEvt["TargetCI"], reselEvt);

        }

        public void AddBeforeTp(TestPoint tp)
        {
            AddTpInfo(this.BeforeTpItem, tp);
        }

        public void AddAfterTp(TestPoint tp)
        {
            AddTpInfo(this.AfterTpItem, tp);
        }

        public void AddTpInfo(ZTTDCellReselectAnaTpItem tpItem, TestPoint tp)
        {
            float? pccpchRscp = (float?)tp["TD_PCCPCH_RSCP"];
            if (pccpchRscp != null && pccpchRscp >= -140 && pccpchRscp <= -10)
            {
                tpItem.PccpchRscp += (float)pccpchRscp;
                tpItem.PccpchRscpCount++;
            }

            int? pccpchC2i = (int?)tp["TD_PCCPCH_C2I"];
            if (pccpchC2i != null && pccpchC2i >= -30 && pccpchC2i <= 40)
            {
                tpItem.PccpchC2I += (float)pccpchC2i;
                tpItem.PccpchC2ICount++;
            }

            float? dpchRscp = (float?)tp["TD_DPCH_RSCP"];
            if (dpchRscp != null && dpchRscp >= -140 && dpchRscp <= -10)
            {
                tpItem.DpchRscp += (float)dpchRscp;
                tpItem.DpchRscpCount++;
            }

            int? dpchC2i = (int?)tp["TD_DPCH_C2I"];
            if (dpchC2i != null && dpchC2i >= -30 && dpchC2i <= 40)
            {
                tpItem.DpchC2I += (float)dpchC2i;
                tpItem.DpchC2ICount++;
            }
        }

        public void SetCellItem(ZTTDCellReselectAnaCellItem cellItem, int lac, int ci, Event reselEvt)
        {
            TDCell cell = CellManager.GetInstance().GetTDCell(reselEvt.DateTime, (ushort)lac, (ushort)ci);

            if (cell != null)
            {
                cellItem.TDCell = cell;
                cellItem.CellName = cell.Name;
                cellItem.RNCName = cell.RNCName;
                cellItem.Distance = Math.Round(cell.GetDistance(reselEvt.Longitude, reselEvt.Latitude),2).ToString();
            }

            cellItem.LAC = lac.ToString();
            cellItem.CI = ci.ToString();
        }

        #region 预处理
        public string BeforePccpchRscpAvg
        {
            get
            {
                if (BeforeTpItem.PccpchRscpCount > 0)
                {
                    return Math.Round(BeforeTpItem.PccpchRscp/BeforeTpItem.PccpchRscpCount,2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforePccpchC2IAvg
        {
            get
            {
                if (BeforeTpItem.PccpchC2ICount > 0)
                {
                    return Math.Round(BeforeTpItem.PccpchC2I / BeforeTpItem.PccpchC2ICount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeDpchRscpAvg
        {
            get
            {
                if (BeforeTpItem.DpchRscpCount > 0)
                {
                    return Math.Round(BeforeTpItem.DpchRscp / BeforeTpItem.DpchRscpCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeDpchC2IAvg
        {
            get
            {
                if (BeforeTpItem.DpchC2ICount > 0)
                {
                    return Math.Round(BeforeTpItem.DpchC2I / BeforeTpItem.DpchC2ICount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string AfterPccpchRscpAvg
        {
            get
            {
                if (AfterTpItem.PccpchRscpCount > 0)
                {
                    return Math.Round(AfterTpItem.PccpchRscp / AfterTpItem.PccpchRscpCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterPccpchC2IAvg
        {
            get
            {
                if (AfterTpItem.PccpchC2ICount > 0)
                {
                    return Math.Round(AfterTpItem.PccpchC2I / AfterTpItem.PccpchC2ICount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterDpchRscpAvg
        {
            get
            {
                if (AfterTpItem.DpchRscpCount > 0)
                {
                    return Math.Round(AfterTpItem.DpchRscp / AfterTpItem.DpchRscpCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterDpchC2IAvg
        {
            get
            {
                if (AfterTpItem.DpchC2ICount > 0)
                {
                    return Math.Round(AfterTpItem.DpchC2I / AfterTpItem.DpchC2ICount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string GridName
        {
            get
            {
                return GISManager.GetInstance().GetGridDesc(ReselEvt.Longitude, ReselEvt.Latitude);
            }
        }
        #endregion
    }

    public class ZTTDCellReselectAnaCellItem
    {
        public TDCell TDCell { get; set; }
        public string CellName { get; set; }
        public string RNCName { get; set; }
        public string LAC { get; set; }
        public string CI { get; set; }
        public string Distance { get; set; }
    }

    public class ZTTDCellReselectAnaTpItem
    {
        public float PccpchRscp { get; set; }
        public float PccpchRscpCount { get; set; }
        public float PccpchC2I { get; set; }
        public float PccpchC2ICount { get; set; }
        public float DpchRscp { get; set; }
        public float DpchRscpCount { get; set; }
        public float DpchC2I { get; set; }
        public float DpchC2ICount { get; set; }
    }

    public class ZTTDCellReselectAnaCondition
    {
        public int BeforeSecond { get; set; }           //重选前时长
        public int AfterSecond { get; set; }            //重选后时长

        public ZTTDCellReselectAnaCondition()
        {
            BeforeSecond = 5;
            AfterSecond = 5;
        }
    }
}
