﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyNBCellSetQueryByFile : ZTDiyLteCellSetQueryByFile
    {
        public ZTDiyNBCellSetQueryByFile(ServiceName serviceName)
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "NB小区集（按文件）"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34014, this.Name);
        }
    }

    public class ZTDiyNBCellSetQueryByRegion : ZTDiyLteCellSetQueryByRegion
    {
        public ZTDiyNBCellSetQueryByRegion(ServiceName serviceName)
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "NB区域小区集"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34015, this.Name);
        }
    }
}
