﻿using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationFilesCountQuery : DIYSQLBase
    {
        public int FileCountStataus { get; set; } = -1;
        public string FileCountStatausDesc
        {
            get
            {
                string strDesc = "";
                switch (FileCountStataus)
                {
                    case 0:
                        strDesc = "路网通入库已解析的该站文件数量＜该站Excel内填写的文件数量";
                        break;
                    case -1:
                        strDesc = "未上传该站的Excel文件";
                        break;
                    case -2:
                    case -3://未查到指定时段内的表
                        strDesc = "未查到路网通已入库解析的该站文件";
                        break;
                    case 1:
                        strDesc = "路网通入库已解析的该站文件数量≥该站Excel内填写的文件数量";
                        break;
                    case 2:
                        strDesc = "该站文件正在解析中";
                        break;
                    case 11:
                        strDesc = "已验收通过";
                        break;
                    default:
                        strDesc = "未知原因";
                        break;
                }
                return strDesc;
            }
        }

        string strSql = "";
        public StationFilesCountQuery(MainModel mainModel)
            : base(mainModel)
        {
        }
        public void SetCondition(int recentDays, int enodeBid, string serviceType, string carrierType)
        {
            BackgroundFuncConfigManager configManager = BackgroundFuncConfigManager.GetInstance();
            strSql = string.Format("exec sp_probchk_singleStation_filecount_check {0},{1},{2},{3},'{4}','{5}','{6}',{7}"
                , recentDays, configManager.ISTime, configManager.IETime, enodeBid, configManager.ProjectType
                , serviceType, carrierType, (int)(JavaDate.GetMilliseconds(DateTime.Now) / 1000));
        }
        protected override string getSqlTextString()
        {
            return strSql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    FileCountStataus = package.Content.GetParamInt();
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "查询新站文件上传数量是否合格"; }
        }
    }
}
