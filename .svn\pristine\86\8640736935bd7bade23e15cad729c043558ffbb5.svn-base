﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteMOSUnionAnaByFile : VolteMOSUnionAnaBase
    {
        public VolteMOSUnionAnaByFile(MainModel mm)
            : base(mm)
        {
            mainModel = mm;
        }

        public override string Name
        {
            get { return "VOLTE-MOS关联分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override void query()
        {
            if (getCondition())
            {
                base.query();
            }
        }
    }

    public class VolteMOSUnionAnaByFile_FDD : VolteMOSUnionAnaBase_FDD
    {
        public VolteMOSUnionAnaByFile_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "VOLTE_FDD-MOS关联分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override void query()
        {
            if (getCondition())
            {
                base.query();
            }
        }
    }
}
