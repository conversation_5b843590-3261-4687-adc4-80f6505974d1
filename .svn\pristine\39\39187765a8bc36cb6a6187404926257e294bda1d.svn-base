﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class PKModelSettingDlg : BaseForm
    {
        private PKModeTemplate curTemplate = null;

        public PKModelSettingDlg()
        {
            InitializeComponent();
        }

        public void FillTemplate(PKModeTemplate selTemplate)
        {
            this.gridCtrlTemplate.DataSource = PKModeTemplateMngr.Instance.Templates;
            this.gridCtrlTemplate.RefreshDataSource();
            if (selTemplate != null)
            {
                gvTemplate.FocusedRowHandle = PKModeTemplateMngr.Instance.Templates.IndexOf(selTemplate);
            }
            else if (PKModeTemplateMngr.Instance.Templates.Count > 0)
            {
                gvTemplate.FocusedRowHandle = 0;
            }
            gvTemplate_FocusedRowChanged(null, null);
        }

        private void gvTemplate_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            visualizeTemplate(gvTemplate.GetFocusedRow() as PKModeTemplate);
        }

        private void visualizeTemplate(PKModeTemplate template)
        {
            curTemplate = template;
            btnDelete.Enabled = curTemplate != null;
            splitContainerControl1.Panel2.Enabled = curTemplate != null;
            if (template == null)
            {
                return;
            }
            fillAttrHub();
            alghirithmVec = new List<Alghirithm>();
            foreach (Alghirithm item in template.AlghirithmVec)
            {
                Alghirithm al = new Alghirithm();
                al.Param = item.Param;
                alghirithmVec.Add(al);
            }
            fillValueDiffView();
        }

        private void fillAttrHub()
        {
            expCM.SetAttribute(curTemplate.CMHub);
            expCU.SetAttribute(curTemplate.CUHub);
            expCT.SetAttribute(curTemplate.CTHub);
        }

        private List<Alghirithm> alghirithmVec { get; set; }
        private void fillValueDiffView()
        {
            dgvColorRange.Rows.Clear();
            if (alghirithmVec == null)
            {
                return;
            }
            dgvColorRange.RowCount = alghirithmVec.Count;
            int iRow = 0, iCol = 0;
            bool bSpecial = true;
            for (int idx = 0; idx < 2; idx++)
            {
                bSpecial = !bSpecial;
                foreach (Alghirithm thm in alghirithmVec)
                {
                    if (thm.IsSpecial == bSpecial)
                    {
                        iCol = 0;
                        DataGridViewRow row = dgvColorRange.Rows[iRow++];
                        row.Cells[iCol++].Value = thm.Name;
                        row.Cells[iCol++].Value = thm.StrDesc;
                        row.Cells[iCol].Style.BackColor = thm.Color;
                        row.Cells[iCol].Style.SelectionBackColor = thm.Color;
                        row.Tag = thm;
                    }
                }
            }
            dgvColorRange.Invalidate();

            colorEidtOther.EditValue = curTemplate.OtherAlghirithm.Color;

            btnColorDel.Enabled = btnColorModify.Enabled = dgvColorRange.SelectedRows.Count > 0;
        }

        private void dgvColorRange_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvColorRange.SelectedRows.Count == 0) return;
            Alghirithm thm = dgvColorRange.SelectedRows[0].Tag as Alghirithm;

            btnColorDel.Enabled = btnColorModify.Enabled = thm != null;
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            MasterCom.Util.TextInputBox box = new MasterCom.Util.TextInputBox("新增竞比模板", "模板名", "请输入模板名");
            if (box.ShowDialog() == DialogResult.OK)
            {
                PKModeTemplate col = new PKModeTemplate();
                col.Name = box.TextInput;
                PKModeTemplateMngr.Instance.Templates.Add(col);
                FillTemplate(col);
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (curTemplate == null)
            {
                return;
            }
            PKModeTemplateMngr.Instance.Delete(curTemplate);
            curTemplate = null;
            FillTemplate(curTemplate);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            foreach (PKModeTemplate tmp in PKModeTemplateMngr.Instance.Templates)
            {
                if (!tmp.IsValid())
                {
                    MessageBox.Show(string.Format("{0}竞比算法设置不完整...", tmp.Name), "提示");
                    return;
                }
            }
            PKModeTemplateMngr.Instance.Save();
        }

        private void btnColorAdd_Click(object sender, EventArgs e)
        {
            PKAlghirithmSettingDlg dlg = new PKAlghirithmSettingDlg();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                alghirithmVec.Add(dlg.GetAttribute());
                fillValueDiffView();
            }
        }

        private void btnColorModify_Click(object sender, EventArgs e)
        {
            if (dgvColorRange.SelectedRows.Count == 0) return;
            Alghirithm thm = dgvColorRange.SelectedRows[0].Tag as Alghirithm;

            PKAlghirithmSettingDlg dlg = new PKAlghirithmSettingDlg();
            dlg.FillData(thm);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                dlg.GetAttribute();
                fillValueDiffView();
            }
        }

        private void btnColorDel_Click(object sender, EventArgs e)
        {
            if (dgvColorRange.SelectedRows.Count == 0) return;
            Alghirithm thm = dgvColorRange.SelectedRows[0].Tag as Alghirithm;
            alghirithmVec.Remove(thm);
            fillValueDiffView();
        }

        private void btnApplyT_Click(object sender, EventArgs e)
        {
            curTemplate.CMHub = expCM.GetAttribute();
            curTemplate.CUHub = expCU.GetAttribute();
            curTemplate.CTHub = expCT.GetAttribute();
            curTemplate.AlghirithmVec = alghirithmVec;
            curTemplate.OtherAlghirithm.Color = (Color)colorEidtOther.EditValue;
        }
    }
}
