﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.FindNearestSiteByPoints
{
    public partial class ResultForm : MinCloseForm
    {
        public ResultForm()
            : base()
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            lv.CanExpandGetter += delegate(object row)
            {
                return row is PointNearestSiteInfo;
            };

            lv.ChildrenGetter += delegate(object row)
            {
                if (row is PointNearestSiteInfo)
                {
                    return (row as PointNearestSiteInfo).NearestSites;
                }
                return null;
            };

            colName.AspectGetter += delegate(object row)
            {
                if (row is PointNearestSiteInfo)
                {
                    return (row as PointNearestSiteInfo).Point.Name;
                }
                else if (row is NearestSiteInfo)
                {
                    return (row as NearestSiteInfo).Site.Name;
                }
                return null;
            };

            colLng.AspectGetter += delegate(object row)
            {
                if (row is PointNearestSiteInfo)
                {
                    return (row as PointNearestSiteInfo).Point.Longitude;
                }
                else if (row is NearestSiteInfo)
                {
                    return (row as NearestSiteInfo).Site.Longitude;
                }
                return null;
            };

            colLat.AspectGetter += delegate(object row)
            {
                if (row is PointNearestSiteInfo)
                {
                    return (row as PointNearestSiteInfo).Point.Latitude;
                }
                else if (row is NearestSiteInfo)
                {
                    return (row as NearestSiteInfo).Site.Latitude;
                }
                return null;
            };

            colDistance.AspectGetter += delegate(object row)
            {
                if (row is NearestSiteInfo)
                {
                    return (row as NearestSiteInfo).Distance;
                }
                return null;
            };

            colNetworkType.AspectGetter += delegate(object row)
            {
                if (row is NearestSiteInfo)
                {
                    MasterCom.RAMS.Model.ISite site = (row as NearestSiteInfo).Site;
                    if (site is BTS)
                    {
                        return "GSM";
                    }
                    else if (site is TDNodeB)
                    {
                        return "TD";
                    }
                    else if (site is LTEBTS)
                    {
                        return "LTE";
                    }
                }
                return null;
            };
        }

        List<PointNearestSiteInfo> points;
        public void FillData(List<PointNearestSiteInfo> points)
        {
            lv.ClearObjects();
            lv.SetObjects(points);
            lv.ExpandAll();
            this.points = points;

            PointNearestSiteLayer layer = MainModel.MainForm.GetMapForm().GetLayerBase(
                typeof(PointNearestSiteLayer)) as PointNearestSiteLayer;
            layer.Points = points;

            layer.SelPoint = points[0];
            MainModel.MainForm.GetMapForm().GoToView(points[0].Bounds);
        }

        private void lv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
            PointNearestSiteInfo p = null;
            if (info.RowObject is PointNearestSiteInfo)
            {
                p = info.RowObject as PointNearestSiteInfo;
            }
            else if (info.RowObject is NearestSiteInfo)
            {
                p = (info.RowObject as NearestSiteInfo).Point;
            }
            if (p == null)
            {
                return;
            }
            MainModel.MainForm.GetMapForm().GoToView(p.Bounds);
            PointNearestSiteLayer layer = MainModel.MainForm.GetMapForm().GetLayerBase(
           typeof(PointNearestSiteLayer)) as PointNearestSiteLayer;
            layer.SelPoint = p;

        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("坐标点");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("网络类型");
            row.AddCellValue("基站");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("距离（米）");
            rows.Add(row);

            foreach (PointNearestSiteInfo pt in points)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(pt.Point.Name);
                row.AddCellValue(pt.Point.Longitude);
                row.AddCellValue(pt.Point.Latitude);

                foreach (NearestSiteInfo site in pt.NearestSites)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    string network = string.Empty;
                    if (site.Site is BTS)
                    {
                        network = "GSM";
                    }
                    else if (site.Site is TDNodeB)
                    {
                        network = "TD";
                    }
                    else if (site.Site is LTEBTS)
                    {
                        network = "LTE";
                    }

                    subRow.AddCellValue(network);
                    subRow.AddCellValue(site.Site.Name);
                    subRow.AddCellValue(site.Site.Longitude);
                    subRow.AddCellValue(site.Site.Latitude);
                    subRow.AddCellValue(site.Distance);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

    }
}
