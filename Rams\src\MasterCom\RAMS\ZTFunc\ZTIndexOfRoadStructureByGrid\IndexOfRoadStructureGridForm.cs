﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class IndexOfRoadStructureGridForm : BaseForm
    {
        private List<IndexOfRoadStructureGridInfo> indexOfRoadStruList;
        public IndexOfRoadStructureGridForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridView1);
            }
            catch
            {
                MessageBox.Show("导出到xls失败");
            }
        }

        public void FillData(List<IndexOfRoadStructureGridInfo> indexOfRoadStruList)
        {
            this.indexOfRoadStruList = indexOfRoadStruList;
            refreshGridView();
        }

        private void refreshGridView()
        {
            foreach (IndexOfRoadStructureGridInfo indexOfStru in indexOfRoadStruList)
            {
                indexOfStru.IndexStruList.Sort();
                for (int idx = 0; idx < indexOfStru.IndexStruList.Count; ++idx)
                {
                    if (indexOfStru.IndexStruList[idx] >= (double)edtSampleStructuralIndex.Value)
                    {
                        indexOfStru.Index = idx;
                        break;
                    }
                }
            }
            gridControl1.DataSource = indexOfRoadStruList;
            gridControl1.RefreshDataSource();
        }

        private void edtSampleStructuralIndex_ValueChanged(object sender, EventArgs e)
        {
            refreshGridView();
        }
    }
}
