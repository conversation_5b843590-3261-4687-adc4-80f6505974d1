﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTNRMCSStatistic
{
    public partial class MCSStatisticForm : MinCloseForm
    {
        public MCSStatisticForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<SingleMCSStatisticInfo> calls)
        {
            gridControl.DataSource = calls;
            gridControl.RefreshDataSource();
        }

        private void miReplay_Click(object sender, EventArgs e)
        {
            int[] rows = bandedGridView.GetSelectedRows();
            if (rows.Length == 0)
            {
                MessageBox.Show("请选择要回放的接入文件！");
                return;
            }
            SingleMCSStatisticInfo call = bandedGridView.GetRow(rows[0]) as SingleMCSStatisticInfo;
            if (call == null)
            {
                return;
            }

            MainModel.MainForm.NeedChangeWorkSpace(true);
            PreNextMinutesForm preNextMinutesForm = new PreNextMinutesForm(false);
            preNextMinutesForm.Pre = 2;
            preNextMinutesForm.Next = 2;
            if (preNextMinutesForm.ShowDialog() == DialogResult.OK)
            {
                QueryCondition condition = new QueryCondition();
                condition.isCompareMode = true;

                var fileInfo = new FileInfo();

                fileInfo.ID = call.nrtp.FileID;
                fileInfo.ProjectID = call.nrtp.ProjectType;
                fileInfo.LogTable = call.nrtp.LogTable;
                fileInfo.ServiceType = call.nrtp.ServiceType;
                fileInfo.SampleTbName = call.nrtp.SampleTbName;
                condition.FileInfos.Add(fileInfo);


                int pre = preNextMinutesForm.Pre;
                int next = preNextMinutesForm.Next;
                DateTime timeStart = call.nrtp.DateTime.AddMinutes(-pre);
                DateTime timeEnd = call.nrtp.DateTime.AddMinutes(next);

                condition.Periods.Add(new TimePeriod(timeStart, timeEnd));
                try
                {
                    ReplayFileWithinCompare query = new ReplayFileWithinCompare(MainModel);

                    condition.DistrictID = call.nrtp.DistrictID;
                    query.SetQueryCondition(condition);
                    query.Query();
                }
                catch
                {
                    MainModel.MainForm.CancelChange = true;
                }
            }
            else
            {
                MainModel.MainForm.CancelChange = true;
            }
            MainModel.MainForm.ChangeWorkSpace();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(bandedGridView);
        }
    }
}
