﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment_NR
{
    public class RoadSegment_NR : IProblemData_NR
    {
        public override string ToString()
        {
            return TypeName;
        }
        public string TypeName
        {
            get;
            set;
        }

        private double lastDistance = 0;
        public RoadSegment_NR(string typeName)
        {
            this.TypeName = typeName;
        }
        public double LastDistance
        {
            get { return lastDistance; }
        }

        private readonly List<TestPoint> pnts = new List<TestPoint>();
        public List<string> Cells
        {
            get
            {
                List<string> ret = new List<string>();
                foreach (TestPoint tp in pnts)
                {
                    NRCell cell = tp.GetMainCell_NR();
                    if (cell != null && !ret.Contains(cell.Name))
                    {
                        ret.Add(cell.Name);
                    }
                }
                return ret;
            }
        }
        internal void AddTestPoint(Model.TestPoint testPoint)
        {
            if (pnts.Count > 0)
            {
                lastDistance += pnts[pnts.Count - 1].Distance2(testPoint);
            }
            pnts.Add(testPoint);
        }

        public double MidLng
        {
            get
            {
                if (pnts.Count > 0)
                {
                    return pnts[pnts.Count / 2].Longitude;
                }
                return double.NaN;
            }
        }

        public double MidLat
        {
            get
            {
                if (pnts.Count > 0)
                {
                    return pnts[pnts.Count / 2].Latitude;
                }
                return double.NaN;
            }
        }

        public List<TestPoint> TestPoints
        {
            get { return pnts; }
        }

        public List<Event> Events
        {
            get { return new List<Event>(); }
        }

        public float? RSRPMin
        {
            get;
            private set;
        }
        public float? RSRPMax
        {
            get;
            private set;
        }
        public float? RSRPAvg
        {
            get;
            private set;
        }

        public float? SINRMin { get; private set; }

        public float? SINRMax { get; private set; }

        public float? SINRAvg { get; private set; }

        public double Distance
        { get; private set; }

        public void MakeSummary()
        {
            getRoadDesc();
        }

        private void getRoadDesc()
        {
            if (roadDesc != null)
            {
                return;
            }
            List<double> lng = new List<double>();
            List<double> lat = new List<double>();
            if (TestPoints != null)
                dealTPs(lng, lat);
            delEvts(lng, lat);
            roadDesc = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(lng, lat);
        }

        private void dealTPs(List<double> lng, List<double> lat)
        {
            float rsrpMin = float.MaxValue;
            float rsrpMax = float.MinValue;
            float rsrpSum = 0;
            int rsrpNum = 0;
            float sinrMin = float.MaxValue;
            float sinrMax = float.MinValue;
            float sinrSum = 0;
            int sinrNum = 0;
            double dis = 0;

            TestPoint lastTp = null;
            foreach (TestPoint tp in TestPoints)
            {
                if (lastTp != null)
                {
                    dis += tp.Distance2(lastTp.Longitude, lastTp.Latitude);
                }
                lng.Add(tp.Longitude);
                lat.Add(tp.Latitude);
                dealRsrp(ref rsrpMin, ref rsrpMax, ref rsrpSum, ref rsrpNum, tp);
                dealSinr(ref sinrMin, ref sinrMax, ref sinrSum, ref sinrNum, tp);
                lastTp = tp;
            }
            this.Distance = Math.Round(dis, 2);
            dealRsrpRes(rsrpMin, rsrpMax, rsrpSum, rsrpNum);
            dealSinrRes(sinrMin, sinrMax, sinrSum, sinrNum);
        }

        private static void dealRsrp(ref float rsrpMin, ref float rsrpMax, ref float rsrpSum, ref int rsrpNum, TestPoint tp)
        {
            float? rsrpObj = null;
            if (tp is TestPoint_NR)
            {
                rsrpObj = (float?)tp["NR_SS_RSRP"];
            }

            if (rsrpObj != null && -141 <= rsrpObj && rsrpObj <= 25)
            {
                rsrpMin = Math.Min(rsrpMin, (float)rsrpObj);
                rsrpMax = Math.Max(rsrpMax, (float)rsrpObj);
                rsrpSum += (float)rsrpObj;
                rsrpNum++;
            }
        }

        private static void dealSinr(ref float sinrMin, ref float sinrMax, ref float sinrSum, ref int sinrNum, TestPoint tp)
        {
            float? sinrObj = null;
            if (tp is TestPoint_NR)
            {
                sinrObj = (float?)tp["NR_SS_SINR"];
            }

            if (sinrObj != null && -50 <= sinrObj && sinrObj <= 50)
            {
                sinrMin = Math.Min(sinrMin, (float)sinrObj);
                sinrMax = Math.Max(sinrMax, (float)sinrObj);
                sinrSum += (float)sinrObj;
                sinrNum++;
            }
        }

        private void dealRsrpRes(float rsrpMin, float rsrpMax, float rsrpSum, int rsrpNum)
        {
            if (float.MinValue != rsrpMax)
            {
                this.RSRPMax = rsrpMax;
            }
            if (float.MaxValue != rsrpMin)
            {
                this.RSRPMin = rsrpMin;
            }
            if (rsrpNum != 0)
            {
                this.RSRPAvg = (float)Math.Round(1.0 * rsrpSum / rsrpNum, 2);
            }
        }

        private void dealSinrRes(float sinrMin, float sinrMax, float sinrSum, int sinrNum)
        {
            if (float.MinValue != sinrMax)
            {
                this.SINRMax = sinrMax;
            }
            if (float.MaxValue != sinrMin)
            {
                this.SINRMin = sinrMin;
            }
            if (sinrNum != 0)
            {
                this.SINRAvg = (float)Math.Round(1.0 * sinrSum / sinrNum, 2);
            }
        }

        private void delEvts(List<double> lng, List<double> lat)
        {
            if (Events != null)
            {
                foreach (Event evt in Events)
                {
                    lng.Add(evt.Longitude);
                    lat.Add(evt.Latitude);
                }
            }
        }

        private string roadDesc = null;
        public string RoadDesc
        {
            get
            {
                getRoadDesc();
                return roadDesc;
            }
        }
    }
}
