﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.KPI_Statistics;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc.ZTLTEClusterAna
{
    public class AnalyzerBase
    {


        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public virtual void AnalyzeFile(FileInfo fileInfo, KPIDataGroup fileManager)
        {
        }

   
        public virtual bool IsValidFile(FileInfo fileInfo)
        {
            return false;
        }
    }

    public class ShortCallAna : AnalyzerBase
    {


        #region 属性
        public int ClusterSN
        {
            get;
            private set;
        }

        public string dateTime
        {
            get;
            private set;
        }
        public double RSRP
        {
            get
            {
                if (RSRPParam2 == 0)
                {
                    return 0;
                }
                return Math.Round(100 * (RSRPParam1 / RSRPParam2),2);
            }
        }

        private double RSRPParam1 = 0;
        private double RSRPParam2 = 0;

        public double SINR
        {
            get
            {
                if (SINRParam2 == 0)
                {
                    return 0;
                }
                return Math.Round(100 * (SINRParam1 / SINRParam2),2);
            }
        }

        private double SINRParam1 = 0;
        private double SINRParam2 = 0;

        public double AveSINR
        {
            get
            {
                if (SINRParam2 == 0)
                {
                    return 0;
                }
                return Math.Round(AveSINRParam1 / SINRParam2, 2);
            }
        }

        private double AveSINRParam1 = 0;

        public double SuccessRate
        {
            get
            {
                if (SuccessParam2 == 0 || SuccessParam4 == 0)
                {
                    return 0;
                }
                return Math.Round(100 * (SuccessParam1 / (SuccessParam2)) * ((SuccessParam3) / (SuccessParam4)),2);
            }
        }

        private double SuccessParam1 = 0;
        private double SuccessParam2 = 0;
        private double SuccessParam3 = 0;
        private double SuccessParam4 = 0;

        public double ConnectDelay
        {
            get
            {
                if (ConnectDelayParam4 == 0 || ConnectDelayParam2 == 0)
                {
                    return 0;
                }
                return Math.Round(1000 * ((ConnectDelayParam1) / ((ConnectDelayParam2) * 1000) + (ConnectDelayParam3) / (ConnectDelayParam4 * 1000)),2);
            }
        }

        private double ConnectDelayParam1 = 0;
        private double ConnectDelayParam2 = 0;
        private double ConnectDelayParam3 = 0;
        private double ConnectDelayParam4 = 0;

        public double DropRate
        {
            get
            {
                if (DropRateParam2 == 0)
                {
                    return 0;
                }
                return Math.Round(100 * (DropRateParam1 / DropRateParam2),2);
            }
        }

        private double DropRateParam1 = 0;
        private double DropRateParam2 = 0;
       
        #endregion

     

        public override void AnalyzeFile(FileInfo fileInfo, KPIDataGroup fileManager)
        {
            string deviceName = getDeviceNameByFileName(fileInfo.Name);
            int index = fileInfo.Name.IndexOf("簇") + 1;
            int length = 0;
            for (int i = index; i < fileInfo.Name.Length; i++)
            {
                if (fileInfo.Name[i] > '9' || fileInfo.Name[i] < '0')
                {
                    length = i - index;
                    break;
                }
            }
            int clusterSN = Convert.ToInt32(fileInfo.Name.Substring(index, length));
            this.dateTime = deviceName;
            this.ClusterSN = clusterSN;

            CarrierType carrierType;

            int j = 0;
            foreach (string item in Options)
            {
                carrierType = (CarrierType)fileInfo.CarrierType;
                double value = fileManager.CalcFormula(carrierType, 0, item);
                if (double.IsNaN(value))
                {
                    continue;
                }
                switch (j++)
                {
                    case 0:
                        RSRPParam1 += value;
                        break;
                    case 1:
                        RSRPParam2 += value;
                        break;
                    case 2:
                        SINRParam1 += Convert.ToDouble((decimal)value);
                        break;
                    case 3:
                        SINRParam2 += Convert.ToDouble((decimal)value);
                        break;
                    case 4:
                        AveSINRParam1 += Convert.ToDouble((decimal)value);
                        break;
                    case 5:
                        SuccessParam1 += value ;
                        break;
                    case 6:
                        SuccessParam2 += value;
                        break;
                    case 7:
                        SuccessParam3 += value;
                        break;
                    case 8:
                        SuccessParam4 += value;
                        break;
                    case 9:
                        ConnectDelayParam1 += value;
                        break;
                    case 10:
                        ConnectDelayParam2 += value;
                        break;
                    case 11:
                        ConnectDelayParam3 += value;
                        break;
                    case 12:
                        ConnectDelayParam4 += value;
                        break;
                    case 13:
                        DropRateParam1 += value;
                        break;
                    case 14:
                        DropRateParam2 += value;
                        break;
                }

            }



        }

        protected string getDeviceNameByFileName(string fileName)
        {
            Regex reg = new Regex(@"\d{4}\d{2}\d{2}");
            Match match = reg.Match(fileName);
            if (match.Success)
            {
                return match.Value;
            }
            return null;
        }

        #region Options
        protected List<string> Options = new List<string>()
        {
            
            "{(Lte_61210380+Lte_61210381+Lte_61210382 +Lte_61210306+Lte_61210307+Lte_61210308) }",
            "{Lte_61210301}",//RSRP>-95dBm
            "{(Lte_61210434+Lte_61210411+Lte_61210408+Lte_61210409+Lte_6121040A+Lte_6121040B) }",
            "{Lte_61210401}",//SINR>8dB
            "{(Lte_61210403)*(Lte_61210401) }",//SINR均值(dB)

            //连接建立成功率
            "{evtIdCount[858]}",
            "{(evtIdCount[858]+evtIdCount[896])}",
            "{evtIdCount[855]+evtIdCount[891]}",
            "{evtIdCount[855]+evtIdCount[856]+evtIdCount[891]+evtIdCount[892]}",

             //连接建立时延(ms)
            "{(value1[855]+value1[891])}",
            "{(evtIdCount[855]+evtIdCount[891])}",
            "{value1[858]}",
            "{evtIdCount[858]}",

            //掉线率
            "{evtIdCount[896]}",
            "{evtIdCount[858]}",
           
        };
        #endregion

    }

    public class SpecialShortCall : ShortCallAna
    {
        public SpecialShortCall()
        {
            #region Options
            Options = new List<string>()
        {
            "{(Lte_61210320+Lte_6121031F+Lte_6121031E+Lte_6121031D+Lte_6121031C+Lte_61210389) }",//RSRP>-103dBm
            "{Lte_61210301}",

            "{(Lte_61210410 +Lte_61210411+Lte_61210408+Lte_61210409+Lte_6121040A+Lte_6121040B) }",//SINR>6dB
            "{Lte_61210401}",

            "{Lte_61210403 * Lte_61210401 }",//SINR均值(dB)
            
            //连接建立成功率
            "{(evtIdCount[858])}",
            "{(evtIdCount[858]+evtIdCount[896])}",
            "{(evtIdCount[855]+evtIdCount[891])}",
            "{(evtIdCount[855]+evtIdCount[856]+evtIdCount[891]+evtIdCount[892])}",

             //连接建立时延(ms)
            "{(value1[855]+value1[891])}",
            "{(evtIdCount[855]+evtIdCount[891])}",
            "{value1[858]}",
            "{evtIdCount[858]}",

            //掉线率
            "{100*evtIdCount[896]}",
            "{evtIdCount[858]}"
        };
            #endregion
        }
       
    }

    public class LongCallAna : AnalyzerBase
    {
        

        #region 属性
        public double RSRP
        {
            get
            {
                if (RSRPParam1 == 0)
                {
                    return 0;
                }
                return Math.Round(100 * (RSRPParam1 / RSRPParam2), 2);
            }
        }

        private double RSRPParam1 = 0;
        private double RSRPParam2 = 0;

        public double SINR
        {
            get
            {
                if (SINRParam2 == 0)
                {
                    return 0;
                }
                return Math.Round(100 * (SINRParam1 / SINRParam2),2);
            }
        }

        private double SINRParam1 = 0;
        private double SINRParam2 = 0;

        public double AveSINR
        {
            get
            {
                if (SINRParam2 == 0)
                {
                    return 0;
                }
                return Math.Round(AveSINRParam1 / SINRParam2,2);
            }
        }

        private double AveSINRParam1 = 0;

      

        public double HandoverRate
        {
            get
            {
                if (HandoverRateParam2 == 0)
                {
                    return 0;
                }
                return Math.Round((HandoverRateParam1 / HandoverRateParam2) * 100,2);
            }
        }

        private double HandoverRateParam1 = 0;
        private double HandoverRateParam2 = 0;

        public double HandoverDelay
        {
            get
            {
                if (HandoverDelayParam2 == 0)
                {
                    return 0;
                }
                return Math.Round((HandoverDelayParam1 / HandoverDelayParam2),2);
            }
        }

        private double HandoverDelayParam1 = 0;
        private double HandoverDelayParam2 = 0;

        public double UserHandoverDelay
        {
            get;
            private set;
        }

        public double L2Down
        {
            get
            {
                if (L2DownParam2 == 0)
                {
                    return 0;
                }
                return Math.Round((L2DownParam1 / L2DownParam2) / (1024 * 1024),2);
            }
        }

        private double L2DownParam1 = 0;
        private double L2DownParam2 = 0;
        public double L2Up
        {
            get
            {
                if (L2UpParam2 == 0)
                {
                    return 0;
                }
                return Math.Round((L2UpParam1 / L2UpParam2) / (1024 * 1024),2);
            }
        }
        private double L2UpParam1 = 0;
        private double L2UpParam2 = 0;
        #endregion


        public override void AnalyzeFile(FileInfo fileInfo, KPIDataGroup fileManager)
        {
            if (fileInfo.Name.Contains("下载"))
            {
                CarrierType carrierType;

                int j = 0;
                foreach (string item in Options)
                {
                    carrierType = (CarrierType)fileInfo.CarrierType;
                    double value = 0;

                    value = fileManager.CalcFormula(carrierType, 0, item);
                    switch (j++)
                    {
                        case 0:
                            RSRPParam1 += value;
                            break;
                        case 1:
                            RSRPParam2 += value;
                            break;
                        case 2:
                            SINRParam1 += value;
                            break;
                        case 3:
                            SINRParam2 += value;
                            break;
                        case 4:
                            AveSINRParam1 += value;
                            break;
                        case 5:
                            HandoverRateParam1 += value;
                            break;
                        case 6:
                            HandoverRateParam2 += value;
                            break;
                        case 7: 
                            HandoverDelayParam1 += value;
                            break;
                        case 8:
                            HandoverDelayParam2 += value;
                            break;
                        case 9:
                            L2DownParam1 += value;
                            break;
                        case 10:
                            L2DownParam2 += value;
                            break;
                    }

                }
            }
            else
            {
                CarrierType carrierType = (CarrierType)fileInfo.CarrierType;
                double value = 0;

                value = fileManager.CalcFormula(carrierType, 0, options[0]);
                L2UpParam1 += value;

                value = fileManager.CalcFormula(carrierType, 0, options[1]);
                L2UpParam2 += value;
            }
         
        }

        #region Options
        //L2平均上行吞吐量(Mbps)
        protected List<string> options = new List<string>() { "{Lte_61211E12 * Lte_61211E01}", "Lte_61211E01" };
        protected List<string> Options = new List<string>()
        {
            "{(Lte_61210380+Lte_61210381+Lte_61210382 +Lte_61210306+Lte_61210307+Lte_61210308) }",//RSRP>-95dBm
            "{Lte_61210301}",
            "{(Lte_61210433+Lte_6121040F+Lte_61210410 +Lte_61210411+Lte_61210412)}",//SINR>1.5dB
            "{Lte_61210401}",

            "{Lte_61210403*Lte_61210401}",//SINR均值(dB)

            //切换成功率
            "{(evtIdCount[850]+evtIdCount[898])}",
            "{(evtIdCount[849]+evtIdCount[897])}",

            //控制面切换时延(ms)
            "{value1[850]}",
            "{evtIdCount[850]}",

             //L2平均下行吞吐量(Mbps)
            "{Lte_61211D12 * Lte_61211D01}",
            "{Lte_61211D01}",
        };
        #endregion
    }

    public class SpecialLongCall : LongCallAna
    {
        public SpecialLongCall()
        {
            #region Options
            //L2平均上行吞吐量(Mbps)
            options = new List<string>() { "{Lte_61211E12 * Lte_61211E01}", "Lte_61211E01" };
            Options = new List<string>()
        {
            "{(Lte_61210320+Lte_6121031F+Lte_6121031E+Lte_6121031D+Lte_6121031C+Lte_61210389) }",//RSRP>-103dBm
            "{Lte_61210301}",

            "{(Lte_6121040F+Lte_61210410 +Lte_61210411+Lte_61210408+Lte_61210409+Lte_6121040A+Lte_6121040B) }",//SINR>3dB
            "{Lte_61210401}"  ,

            "{Lte_61210403 * Lte_61210401}",//SINR均值(dB)

            //切换成功率
            "{(evtIdCount[850]+evtIdCount[898])}",
            "{(evtIdCount[849]+evtIdCount[897])}",

            //控制面切换时延(ms)
            "{value1[850]}",
            "{evtIdCount[850]}",

             //L2平均下行吞吐量(Mbps)
            "{Lte_61211D12 * Lte_61211D01}",
            "{Lte_61211D01}",
        };
            #endregion
        }
        
    }

    public class CSFBAna : AnalyzerBase
    {
       
        #region 属性
        public double CSFBRate
        {
            get
            {
                if (CSFBParam2 == 0)
                {
                    return 0;
                }
                return Math.Round(100 * (CSFBParam1 / CSFBParam2),2);
            }
        }

        private double CSFBParam1 = 0;
        private double CSFBParam2 = 0;

        public double CSFBDelay
        {
            get
            {
                if (CSFBDelayParam2 == 0)
                {
                    return 0;
                }
                return  Math.Round((CSFBDelayParam1 /( CSFBDelayParam2 * 1000)),2);
            }
        }
        private double CSFBDelayParam1 = 0;
        private double CSFBDelayParam2 = 0;
       
        #endregion

      
        public override void AnalyzeFile(FileInfo fileInfo, KPIDataGroup fileManager)
        {
            int j = 0;
            foreach (string item in Options)
            {
                CarrierType carrierType = (CarrierType)fileInfo.CarrierType;
                double value;

                value = fileManager.CalcFormula(carrierType, 0, item);
                switch (j++)
                {
                    case 0:
                        CSFBParam1 += value;
                        break;
                    case 1:
                        CSFBParam2 += value;
                        break;
                    case 2:
                        CSFBDelayParam1 += value;
                        break;
                    case 3:
                        CSFBDelayParam2 += value;
                        break;
                }
            }

        }
        #region Options
        private readonly List<string> Options = new List<string>()
        {
            //CSFB回落成功率
            "{(evtIdCount[879]+value9[879])}",
            "{(evtIdCount[876]+value9[876])}",
            //CSFB时延
            "{value1[1011]}",
            "{evtIdCount[1011]}",

           
        };
        #endregion

    }
}
