﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEDownloadPerformanceForm : MinCloseForm
    {
        public LTEDownloadPerformanceForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        public void FillData(Dictionary<int, LTEDownloadPerformanceItem> lteDownloadPerformanceItemDic)
        {
            BindingSource source = new BindingSource();
            source.DataSource = lteDownloadPerformanceItemDic.Values;
            gridData.DataSource = source;
            gridData.RefreshDataSource();
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }
    }
}
