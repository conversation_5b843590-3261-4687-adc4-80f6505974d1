﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.ES.Core;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Data;


namespace MasterCom.RAMS.Model.Interface
{
    public class DiySqlQueryESResult : DIYSQLBase
    {
        private DateTime startTime;
        private readonly DateTime endTime;
        public DiySqlQueryESResult(MainModel mainModel, DateTime startTime, DateTime endTime)
            : base(mainModel)
        {
            this.startTime = startTime;
            this.endTime = endTime;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                while (startTime <= endTime)
                {
                    queryInThread(clientProxy);
                    startTime = startTime.AddMonths(1);
                }
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            return rType;
        }
        public Dictionary<string, ESResult> ResultDictionary { set; get; } = new Dictionary<string, ESResult>();

        protected override string getSqlTextString()
        {
            return "select ifileid,iseqid,pretype_desc,reason_desc,solution_desc from tb_es_event_result_" + this.startTime.ToString("yyMM");
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ESResult es = new ESResult
                    {
                        FileId = package.Content.GetParamInt(),
                        SeqId = package.Content.GetParamInt(),
                        Type = package.Content.GetParamString(),
                        Analysis = package.Content.GetParamString(),
                        Solution = package.Content.GetParamString()
                    };
                    string dicKey = string.Format("{0}_{1}", es.FileId, es.SeqId);
                    ResultDictionary[dicKey] = es;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }
        public override string Name
        {
            get { return "智能预判信息结果"; }
        }
    }
}
