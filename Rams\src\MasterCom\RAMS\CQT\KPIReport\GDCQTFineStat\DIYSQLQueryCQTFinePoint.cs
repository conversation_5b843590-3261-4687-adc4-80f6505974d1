﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public class DIYSQLQueryCQTFinePoint : DIYSQLQueryCQTPoint
    {
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 21000, 21001, this.Name);
        }
        /// <summary>
        /// 是否显示
        /// </summary>
        public DIYSQLQueryCQTFinePoint(MainModel mm)
            : base(mm)
        {
            MainDB = false;
        }

        protected override string getSqlTextString()
        {
            string sql = @"select a.iareatypeid,a.iareaid,a.strareaname,a.strcomment,a.strareatypename,b.itllongitude,b.itllatitude,b.ibrlongitude,b.ibrlatitude"
                    +" from tb_cfg_static_arealist as a"
                    +" left join tb_cfg_static_point as b on a.iareatypeid = b.iareatypeid and a.iareaid = b.iareaid "
                    +" where (strareatypename like '%CQT%' or strareatypename like '%标准化地点%')";
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[9];
            int index = 0;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index] = E_VType.E_Int;
            return rType;
        }

        /// <summary>
        /// 获取CQT点，获取顺序要与getSqlRetTypeArr方法的E_VType[]类型一致
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        private CQTKPIFinePoint fillFrom(Content content)
        {
            CQTKPIFinePoint pnt = new CQTKPIFinePoint();
            pnt.ID = 0;
            pnt.AreatypeID = content.GetParamInt();
            pnt.AreaID = content.GetParamInt();
            pnt.Name = content.GetParamString();
            pnt.AddrAt = content.GetParamString();
            pnt.NetType = content.GetParamString();
            pnt.LTLongitude = content.GetParamDouble();
            pnt.LTLatitude = content.GetParamDouble();
            pnt.BRLongitude = content.GetParamDouble();
            pnt.BRLatitude = content.GetParamDouble();
            return pnt;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            CQTKPIFineDataManager cqtFineManager = CQTKPIFineDataManager.GetInstance();
            cqtFineManager.AllPoints.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CQTKPIFinePoint pnt = fillFrom(package.Content);
                    if (!cqtFineManager.AllPoints.ContainsKey(pnt.Name))
                    {
                        cqtFineManager.AllPoints[pnt.Name] = new List<CQTKPIFinePoint>();
                    }
                    cqtFineManager.AllPoints[pnt.Name].Add(pnt);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
}
