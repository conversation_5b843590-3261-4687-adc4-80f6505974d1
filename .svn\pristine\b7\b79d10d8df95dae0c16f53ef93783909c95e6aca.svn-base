﻿using System;
using System.Collections.Generic;
using System.Text;


using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Func;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
    /// <summary>
    /// Written by WuJunHong 2012.7.27
    /// </summary>
    public class ZTDIYEventsComparison : QueryBase
    {
        private readonly SearchGeometrys searchGeometrys;
        public ZTDIYEventsComparison(MainModel mainModel)
            : base(mainModel)
        {
            searchGeometrys = mainModel.SearchGeometrys;
        }

        public override string Name
        {
            get { return "重复事件查询"; }
        }
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20005, this.Name);
        }
        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            if (searchGeometrys.Region == null)
            {
                DialogResult res = MessageBox.Show("在当前地图上没有选取区域，是否按全区域模式继续进行查询？", "提示", MessageBoxButtons.YesNo);
                if (res == DialogResult.No)
                    return;
            }
            if (searchGeometrys.Region == null) //地图上无框选区域，按全区域模式查询
            {
                ZTDIYEventsComparisonDlg ztDiyEventsComparisonDlg = new ZTDIYEventsComparisonDlg(MainModel, true);
                ztDiyEventsComparisonDlg.ShowDialog();
            }
            else  //地图上有框选区域，按区域模式查询
            {
                ZTDIYEventsComparisonDlg ztDiyEventsComparisonDlg = new ZTDIYEventsComparisonDlg(MainModel, false);
                ztDiyEventsComparisonDlg.ShowDialog();
            }
        }
    }
}
