﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLteUploadIpGroup
{
    public partial class UploadIpGroupForm : MinCloseForm
    {
        public UploadIpGroupForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gv);
        }

        public void FillData(List<UpLoadInfo> dataSet)
        {
            gridCtrl.DataSource = dataSet;
            gridCtrl.RefreshDataSource();
            gv.BestFitColumns();
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gv.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            UpLoadInfo dl = gv.GetRow(info.RowHandle) as UpLoadInfo;
            if (dl == null)
            {
                return;
            }
            MainModel.ClearDTData();
            foreach (TestPoint tp in dl.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            MainModel.FireDTDataChanged(this);
        }
    }
}
