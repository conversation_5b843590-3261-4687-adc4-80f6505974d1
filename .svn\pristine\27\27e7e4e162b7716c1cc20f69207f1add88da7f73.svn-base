﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTVoNRStatDelayAna
{
    public class VoNRPairsInfo
    {
        public VoNRPairsInfo(VoNRCallInfo moCall, VoNRCallInfo mtCall)
        {
            this.moCall = moCall;
            this.mtCall = mtCall;
        }
        public int Sn { get; set; }
        public string GridName
        {
            get 
            {
                if (MoCall != null)
                {
                    return MoCall.GridName;
                }
                return "";
            }
        }

        private VoNRCallInfo moCall;
        public VoNRCallInfo MoCall
        {
            get
            {
                if (moCall == null)
                {
                    return new VoNRCallInfo("", (int)MoMtFile.MoFlag);
                }
                else
                {
                    return moCall;
                }
            }
            set
            {
                moCall = value;
            }
        }

        private VoNRCallInfo mtCall;
        public VoNRCallInfo MtCall 
        {
            get
            {
                if (mtCall == null)
                {
                    return new VoNRCallInfo("", (int)MoMtFile.MtFlag);
                }
                else
                {
                    return mtCall;
                }
            }
            set
            {
                mtCall = value;
            }
        }
    }

    public class VoNRItemsInfo
    {
        public int SN { get; set; }
        public string GridName { get { return ListVoNrPairsInfo[0].GridName; } }
        public List<VoNRPairsInfo> ListVoNrPairsInfo { get; set; } = new List<VoNRPairsInfo>();
    }
}
