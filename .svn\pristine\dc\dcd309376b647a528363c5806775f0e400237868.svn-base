﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.NewBlackBlock
{
    public class NewBlackBlockStatQuery : QueryNewBlackBlock
    {
        public NewBlackBlockStatQuery()
            : base(MainModel.GetInstance())
        {

        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18023, this.Name);
        }

        protected override void fireShowResultForm()
        {
            BlackBlockStatForm frm = MainModel.CreateResultForm(typeof(BlackBlockStatForm)) as BlackBlockStatForm;
            frm.FillData(MainModel.CurBlackBlockList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
