using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model
{
    public class Office : IUserInfoObject
    {
        public Office()
        {

        }

        public int CompanyID { get; set; }

        public int DepartmentID { get; set; }

        public int ID { get; set; }

        public string LoginName { get; set; }

        public string Description { get; set; }

        public override string ToString()
        {
            return LoginName;
        }

        public static Office Fill(Content content)
        {
            Office office = new Office();
            office.CompanyID = content.GetParamInt();
            office.DepartmentID = content.GetParamInt();
            office.ID = content.GetParamInt();
            office.LoginName = content.GetParamString();
            office.Description = content.GetParamString();
            return office;
        }

        public static IComparer<Office> GetCompareByOfficeID()
        {
            if (compareByOfficeID == null)
            {
                compareByOfficeID = new CompareByOfficeID();
            }
            return compareByOfficeID;
        }

        private static IComparer<Office> compareByOfficeID;

        private class CompareByOfficeID : IComparer<Office>
        {
            public int Compare(Office x, Office y)
            {
                return x.ID.CompareTo(y.ID);
            }
        }
    }
}
