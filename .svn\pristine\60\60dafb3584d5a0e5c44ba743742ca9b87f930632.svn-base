﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanLTEBestRxlevSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.spinEditBandEnd = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditBandStart = new DevExpress.XtraEditors.SpinEdit();
            this.labelFreqBand = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.numBenchRxlev = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxFreqBandType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelBandSel = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBandEnd.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBandStart.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBenchRxlev)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxFreqBandType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Location = new System.Drawing.Point(373, 102);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 16;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Location = new System.Drawing.Point(279, 102);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 17;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(175, 17);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(11, 12);
            this.label3.TabIndex = 32;
            this.label3.Text = "-";
            // 
            // spinEditBandEnd
            // 
            this.spinEditBandEnd.EditValue = new decimal(new int[] {
            1900,
            0,
            0,
            0});
            this.spinEditBandEnd.Location = new System.Drawing.Point(193, 13);
            this.spinEditBandEnd.Name = "spinEditBandEnd";
            this.spinEditBandEnd.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditBandEnd.Properties.Appearance.Options.UseFont = true;
            this.spinEditBandEnd.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditBandEnd.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditBandEnd.Properties.IsFloatValue = false;
            this.spinEditBandEnd.Properties.Mask.EditMask = "N00";
            this.spinEditBandEnd.Size = new System.Drawing.Size(82, 20);
            this.spinEditBandEnd.TabIndex = 31;
            // 
            // spinEditBandStart
            // 
            this.spinEditBandStart.EditValue = new decimal(new int[] {
            1880,
            0,
            0,
            0});
            this.spinEditBandStart.Location = new System.Drawing.Point(85, 13);
            this.spinEditBandStart.Name = "spinEditBandStart";
            this.spinEditBandStart.Properties.Appearance.BackColor = System.Drawing.Color.White;
            this.spinEditBandStart.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditBandStart.Properties.Appearance.Options.UseBackColor = true;
            this.spinEditBandStart.Properties.Appearance.Options.UseFont = true;
            this.spinEditBandStart.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditBandStart.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditBandStart.Properties.IsFloatValue = false;
            this.spinEditBandStart.Properties.Mask.EditMask = "N00";
            this.spinEditBandStart.Size = new System.Drawing.Size(82, 20);
            this.spinEditBandStart.TabIndex = 30;
            // 
            // labelFreqBand
            // 
            this.labelFreqBand.AutoSize = true;
            this.labelFreqBand.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelFreqBand.Location = new System.Drawing.Point(18, 18);
            this.labelFreqBand.Name = "labelFreqBand";
            this.labelFreqBand.Size = new System.Drawing.Size(65, 12);
            this.labelFreqBand.TabIndex = 29;
            this.labelFreqBand.Text = "频段设置：";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label10.Location = new System.Drawing.Point(85, 57);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(65, 12);
            this.label10.TabIndex = 42;
            this.label10.Text = "信号强度≥";
            // 
            // numBenchRxlev
            // 
            this.numBenchRxlev.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numBenchRxlev.Location = new System.Drawing.Point(154, 52);
            this.numBenchRxlev.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numBenchRxlev.Minimum = new decimal(new int[] {
            150,
            0,
            0,
            -2147483648});
            this.numBenchRxlev.Name = "numBenchRxlev";
            this.numBenchRxlev.Size = new System.Drawing.Size(82, 21);
            this.numBenchRxlev.TabIndex = 43;
            this.numBenchRxlev.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numBenchRxlev.Value = new decimal(new int[] {
            54,
            0,
            0,
            -2147483648});
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label11.Location = new System.Drawing.Point(237, 58);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(23, 12);
            this.label11.TabIndex = 44;
            this.label11.Text = "dBm";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(269, 59);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 45;
            this.label1.Text = "的占比";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(40, 56);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 46;
            this.label2.Text = "统计：";
            // 
            // cbxFreqBandType
            // 
            this.cbxFreqBandType.Location = new System.Drawing.Point(379, 13);
            this.cbxFreqBandType.Name = "cbxFreqBandType";
            this.cbxFreqBandType.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbxFreqBandType.Properties.Appearance.Options.UseFont = true;
            this.cbxFreqBandType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxFreqBandType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxFreqBandType.Size = new System.Drawing.Size(70, 20);
            this.cbxFreqBandType.TabIndex = 48;
            this.cbxFreqBandType.SelectedIndexChanged += new System.EventHandler(this.cbxFreqBandType_SelectedIndexChanged);
            // 
            // labelBandSel
            // 
            this.labelBandSel.AutoSize = true;
            this.labelBandSel.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelBandSel.Location = new System.Drawing.Point(308, 16);
            this.labelBandSel.Name = "labelBandSel";
            this.labelBandSel.Size = new System.Drawing.Size(65, 12);
            this.labelBandSel.TabIndex = 47;
            this.labelBandSel.Text = "频段选择：";
            // 
            // ZTScanLTEBestRxlevSetForm
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(469, 148);
            this.Controls.Add(this.cbxFreqBandType);
            this.Controls.Add(this.labelBandSel);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.label11);
            this.Controls.Add(this.numBenchRxlev);
            this.Controls.Add(this.label10);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.spinEditBandEnd);
            this.Controls.Add(this.spinEditBandStart);
            this.Controls.Add(this.labelFreqBand);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ZTScanLTEBestRxlevSetForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "LTE频点最强信号分析";
            this.Load += new System.EventHandler(this.ZTScanLTEInterferenceSetForm_Load);
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBandEnd.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBandStart.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBenchRxlev)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxFreqBandType.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SpinEdit spinEditBandEnd;
        private DevExpress.XtraEditors.SpinEdit spinEditBandStart;
        private System.Windows.Forms.Label labelFreqBand;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.NumericUpDown numBenchRxlev;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.ComboBoxEdit cbxFreqBandType;
        private System.Windows.Forms.Label labelBandSel;

    }
}