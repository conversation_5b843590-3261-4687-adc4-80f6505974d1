<?xml version="1.0"?>
<Configs>
  <Config name="Main">
    <Item name="WorkSpace" typeName="WorkSpace">
      <Item name="WorkSheets" typeName="IList">
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">地图</Item>
          <Item name="ChildFormConfigs" typeName="IList">
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.MapForm</Item>
              <Item name="ImageFilePath" typeName="String">images\maplogo.gif</Item>
              <Item name="Text" typeName="String">MAP地图</Item>
              <Item name="WindowState" typeName="Int32">2</Item>
              <Item name="LocationX" typeName="Int32">-4</Item>
              <Item name="LocationY" typeName="Int32">-30</Item>
              <Item name="SizeWidth" typeName="Int32">1107</Item>
              <Item name="SizeHeight" typeName="Int32">681</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="IList" key="MapSpaceFeatureLayers">
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">1</Item>
                    <Item typeName="String" key="LayerName">选择区域</Item>
                    <Item typeName="String" key="LayerPath" />
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">Region</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">10</Item>
                    <Item typeName="String" key="LayerName">问题黑点</Item>
                    <Item typeName="String" key="LayerPath" />
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">BlackBlock</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">13</Item>
                    <Item typeName="String" key="LayerName">街道</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\街道.TAB</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">街道</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">14</Item>
                    <Item typeName="String" key="LayerName">公路</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\公路.TAB</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">公路</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">15</Item>
                    <Item typeName="String" key="LayerName">道路</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\道路.TAB</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">道路</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">16</Item>
                    <Item typeName="String" key="LayerName">地铁</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\地铁.tab</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">地铁</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">17</Item>
                    <Item typeName="String" key="LayerName">铁路</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\铁路.TAB</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">铁路</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">18</Item>
                    <Item typeName="String" key="LayerName">LAC图层</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\LAC图层.TAB</Item>
                    <Item typeName="Boolean" key="LayerVisible">False</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">LAC图层</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">19</Item>
                    <Item typeName="String" key="LayerName">BSC图层</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\BSC图层.TAB</Item>
                    <Item typeName="Boolean" key="LayerVisible">False</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">BSC图层</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">20</Item>
                    <Item typeName="String" key="LayerName">干道网格</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\干道网格.TAB</Item>
                    <Item typeName="Boolean" key="LayerVisible">False</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">干道网格</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">21</Item>
                    <Item typeName="String" key="LayerName">城区网格</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\城区网格.TAB</Item>
                    <Item typeName="Boolean" key="LayerVisible">False</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">城区网格</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">22</Item>
                    <Item typeName="String" key="LayerName">县道</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\县道.tab</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">县道</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">23</Item>
                    <Item typeName="String" key="LayerName">省道</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\省道.tab</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">省道</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">24</Item>
                    <Item typeName="String" key="LayerName">国道</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\国道.tab</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">国道</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">25</Item>
                    <Item typeName="String" key="LayerName">高速</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\高速.tab</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">高速</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">26</Item>
                    <Item typeName="String" key="LayerName">一般水系</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\一般水系.TAB</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">一般水系</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">27</Item>
                    <Item typeName="String" key="LayerName">郊区水系</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\郊区水系.TAB</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">郊区水系</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">28</Item>
                    <Item typeName="String" key="LayerName">主要水系</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\主要水系.TAB</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">主要水系</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">29</Item>
                    <Item typeName="String" key="LayerName">镇界</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\镇界.tab</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">镇界</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">30</Item>
                    <Item typeName="String" key="LayerName">县界</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\县界.tab</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">县界</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Int32" key="LayerIndex">31</Item>
                    <Item typeName="String" key="LayerName">市界</Item>
                    <Item typeName="String" key="LayerPath">D:\Project\RAMS\Release\Release\GEOGRAPHIC\市界.tab</Item>
                    <Item typeName="Boolean" key="LayerVisible">True</Item>
                    <Item typeName="Int32" key="LayerType">0</Item>
                    <Item typeName="String" key="LayerAlias">市界</Item>
                    <Item typeName="IList" key="ChildNodes" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item name="ActiveChildFormIndex" typeName="Int32">0</Item>
        </Item>
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">GSM参数</Item>
          <Item name="ChildFormConfigs" typeName="IList">
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.LineChartForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_timeline.gif</Item>
              <Item name="Text" typeName="String">GSM时序图</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">280</Item>
              <Item name="LocationY" typeName="Int32">0</Item>
              <Item name="SizeWidth" typeName="Int32">563</Item>
              <Item name="SizeHeight" typeName="Int32">325</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Boolean" key="DisplayInfo">True</Item>
                <Item typeName="Boolean" key="DisplayLegend">True</Item>
                <Item typeName="Int32" key="GraphWidth">470</Item>
                <Item typeName="Int32" key="GraphHeight">190</Item>
                <Item typeName="Int32" key="DisplayChartCount">2</Item>
                <Item typeName="IList" key="ChartHeights">
                  <Item typeName="Int32">99</Item>
                  <Item typeName="Int32">79</Item>
                  <Item typeName="Int32">50</Item>
                </Item>
                <Item typeName="IList" key="ChartInfos">
                  <Item typeName="IDictionary">
                    <Item typeName="Boolean" key="DisplayHorizontalLine">True</Item>
                    <Item typeName="IList" key="SerialInfos">
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="String" key="SystemName">GSM</Item>
                        <Item typeName="String" key="ParamName">RxLevSub</Item>
                        <Item typeName="Int32" key="ArrayIndex">0</Item>
                        <Item typeName="Single" key="Min">-120</Item>
                        <Item typeName="Single" key="Max">-10</Item>
                        <Item typeName="Int32" key="DisplayType">1</Item>
                        <Item typeName="Int32" key="ColorR">0</Item>
                        <Item typeName="Int32" key="ColorG">255</Item>
                        <Item typeName="Int32" key="ColorB">0</Item>
                        <Item typeName="Boolean" key="DisplayAlarmLine">False</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="String" key="SystemName">GSM</Item>
                        <Item typeName="String" key="ParamName">N_RxLev</Item>
                        <Item typeName="Int32" key="ArrayIndex">0</Item>
                        <Item typeName="Single" key="Min">-120</Item>
                        <Item typeName="Single" key="Max">-10</Item>
                        <Item typeName="Int32" key="DisplayType">0</Item>
                        <Item typeName="Int32" key="ColorR">234</Item>
                        <Item typeName="Int32" key="ColorG">228</Item>
                        <Item typeName="Int32" key="ColorB">0</Item>
                        <Item typeName="Int32" key="LineWidth">1</Item>
                        <Item typeName="Boolean" key="DisplayAlarmLine">False</Item>
                      </Item>
                    </Item>
                    <Item typeName="IList" key="EventInfos">
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">6</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">7</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">8</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">9</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">10</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">11</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">18</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">41</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">43</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">82</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">83</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">84</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">85</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">87</Item>
                      </Item>
                    </Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Boolean" key="DisplayHorizontalLine">True</Item>
                    <Item typeName="IList" key="SerialInfos">
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="String" key="SystemName">GSM</Item>
                        <Item typeName="String" key="ParamName">SQI</Item>
                        <Item typeName="Int32" key="ArrayIndex">0</Item>
                        <Item typeName="Single" key="Min">-20</Item>
                        <Item typeName="Single" key="Max">30</Item>
                        <Item typeName="Int32" key="DisplayType">1</Item>
                        <Item typeName="Int32" key="ColorR">128</Item>
                        <Item typeName="Int32" key="ColorG">255</Item>
                        <Item typeName="Int32" key="ColorB">128</Item>
                        <Item typeName="Boolean" key="DisplayAlarmLine">False</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="String" key="SystemName">GSM</Item>
                        <Item typeName="String" key="ParamName">RxQualSub</Item>
                        <Item typeName="Int32" key="ArrayIndex">0</Item>
                        <Item typeName="Single" key="Min">0</Item>
                        <Item typeName="Single" key="Max">7</Item>
                        <Item typeName="Int32" key="DisplayType">1</Item>
                        <Item typeName="Int32" key="ColorR">255</Item>
                        <Item typeName="Int32" key="ColorG">0</Item>
                        <Item typeName="Int32" key="ColorB">0</Item>
                        <Item typeName="Boolean" key="DisplayAlarmLine">False</Item>
                      </Item>
                    </Item>
                    <Item typeName="IList" key="EventInfos">
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">1</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">2</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">3</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">4</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">5</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">6</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">7</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">8</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">9</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">10</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">11</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">12</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">13</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">14</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">15</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">17</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">18</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">40</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">41</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">43</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="Int32" key="EventID">73</Item>
                      </Item>
                    </Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Boolean" key="DisplayHorizontalLine">True</Item>
                    <Item typeName="IList" key="SerialInfos">
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">4</Item>
                        <Item typeName="String" key="SystemName">GSM</Item>
                        <Item typeName="String" key="ParamName">MsTxPower</Item>
                        <Item typeName="Int32" key="ArrayIndex">0</Item>
                        <Item typeName="Single" key="Min">0</Item>
                        <Item typeName="Single" key="Max">63</Item>
                        <Item typeName="Int32" key="DisplayType">0</Item>
                        <Item typeName="Int32" key="ColorR">255</Item>
                        <Item typeName="Int32" key="ColorG">0</Item>
                        <Item typeName="Int32" key="ColorB">0</Item>
                        <Item typeName="Int32" key="LineWidth">1</Item>
                        <Item typeName="Boolean" key="DisplayAlarmLine">False</Item>
                      </Item>
                    </Item>
                    <Item typeName="IList" key="EventInfos" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Boolean" key="DisplayHorizontalLine">True</Item>
                    <Item typeName="IList" key="SerialInfos" />
                    <Item typeName="IList" key="EventInfos" />
                  </Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
                <Item typeName="Boolean" key="IsMTRForm">False</Item>
              </Item>
            </Item>
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.RTPointsDataGridForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">服务信道参数</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">0</Item>
              <Item name="LocationY" typeName="Int32">0</Item>
              <Item name="SizeWidth" typeName="Int32">281</Item>
              <Item name="SizeHeight" typeName="Int32">226</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Int32" key="RowCount">10</Item>
                <Item typeName="Int32" key="ColumnCount">2</Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">127</Item>
                  <Item typeName="Int32">127</Item>
                </Item>
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String">指标项</Item>
                  <Item typeName="String">值</Item>
                </Item>
                <Item typeName="IList" key="CellDefines">
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Time</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">Time</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Cell Name</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">CellName</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">LAC</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">LAC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">CI</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">BCCH ARFCN</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Speech Codec</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">SpeechCodecName</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">BSIC</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Mode</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">ModeName</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">ProjectType</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">Project</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">FileName</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">FileName</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
              </Item>
            </Item>
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.RTPointsDataGridForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">无线参数</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">1</Item>
              <Item name="LocationY" typeName="Int32">224</Item>
              <Item name="SizeWidth" typeName="Int32">280</Item>
              <Item name="SizeHeight" typeName="Int32">172</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Int32" key="RowCount">7</Item>
                <Item typeName="Int32" key="ColumnCount">4</Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">64</Item>
                  <Item typeName="Int32">63</Item>
                  <Item typeName="Int32">63</Item>
                  <Item typeName="Int32">63</Item>
                </Item>
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String">参数项</Item>
                  <Item typeName="String">值</Item>
                  <Item typeName="String">参数项</Item>
                  <Item typeName="String">值</Item>
                </Item>
                <Item typeName="IList" key="CellDefines">
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">RxLev sub</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">RxLevSub</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Rxlev full</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">RxLevFull</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">RxQual sub</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">RxQualSub</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Rxqual full</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">RxQualFull</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">TA</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">TA</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">SQI</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">SQI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">FER sub</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">FERSub</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">FER full</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">FERFull</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">DTX</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">DTX</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Hop</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">HP</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">RLT max</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">RLTMax</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">RLT current</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">RLTCur</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">MS Tx power</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">MsTxPower</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">CellName</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                  </Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
              </Item>
            </Item>
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.RTPointsDataGridForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">干扰-C/I</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">1</Item>
              <Item name="LocationY" typeName="Int32">393</Item>
              <Item name="SizeWidth" typeName="Int32">279</Item>
              <Item name="SizeHeight" typeName="Int32">230</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Int32" key="RowCount">16</Item>
                <Item typeName="Int32" key="ColumnCount">3</Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">84</Item>
                  <Item typeName="Int32">84</Item>
                  <Item typeName="Int32">84</Item>
                </Item>
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String">C/I ARFCN</Item>
                  <Item typeName="String">C/I value</Item>
                  <Item typeName="String">C/I Rxlev</Item>
                </Item>
                <Item typeName="IList" key="CellDefines">
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">6</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">6</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">6</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">7</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">7</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">7</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">8</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">8</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">8</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">9</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">9</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">9</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">10</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">10</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">10</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">11</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">11</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">11</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">12</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">12</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">12</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">13</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">13</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">13</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">14</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">14</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">14</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">15</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I</Item>
                      <Item typeName="Int32" key="ArrayIndex">15</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C_I_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">15</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
              </Item>
            </Item>
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.RTPointsDataGridForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">服务+邻区信道无线参数</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">279</Item>
              <Item name="LocationY" typeName="Int32">325</Item>
              <Item name="SizeWidth" typeName="Int32">564</Item>
              <Item name="SizeHeight" typeName="Int32">298</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Int32" key="RowCount">13</Item>
                <Item typeName="Int32" key="ColumnCount">7</Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">79</Item>
                  <Item typeName="Int32">79</Item>
                  <Item typeName="Int32">79</Item>
                  <Item typeName="Int32">80</Item>
                  <Item typeName="Int32">79</Item>
                  <Item typeName="Int32">79</Item>
                  <Item typeName="Int32">79</Item>
                </Item>
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String">Cell name</Item>
                  <Item typeName="String">ARFCN</Item>
                  <Item typeName="String">BSIC</Item>
                  <Item typeName="String">Rxlev</Item>
                  <Item typeName="String">C1</Item>
                  <Item typeName="String">C2</Item>
                  <Item typeName="String">Distance(m)</Item>
                </Item>
                <Item typeName="IList" key="CellDefines">
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">CellName</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">RxLevSub</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C1</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">C2</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_CellName</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C1</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C2</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_CellName</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C1</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C2</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_CellName</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C1</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C2</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_CellName</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C1</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C2</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_CellName</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C1</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C2</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_CellName</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C1</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_C2</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">6</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">6</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">6</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">7</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">7</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">7</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">8</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">8</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">8</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">9</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">9</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">9</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">10</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">10</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">10</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BCCH</Item>
                      <Item typeName="Int32" key="ArrayIndex">11</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">11</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">GSM</Item>
                      <Item typeName="String" key="ParamName">N_RxLev</Item>
                      <Item typeName="Int32" key="ArrayIndex">11</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
              </Item>
            </Item>
          </Item>
          <Item name="ActiveChildFormIndex" typeName="Int32">1</Item>
        </Item>
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">信令事件</Item>
          <Item name="ChildFormConfigs" typeName="IList">
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.BriefDataGridFormEvent</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_signaling.gif</Item>
              <Item name="Text" typeName="String">事件列表</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">0</Item>
              <Item name="LocationY" typeName="Int32">0</Item>
              <Item name="SizeWidth" typeName="Int32">519</Item>
              <Item name="SizeHeight" typeName="Int32">650</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Boolean" key="EquipmentFilterEnabled">False</Item>
                <Item typeName="IList" key="NeedLoadEquipmentFlags">
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                </Item>
                <Item typeName="Boolean" key="MessageFilterEnabled">False</Item>
                <Item typeName="IList" key="NeedLoadMessageIDs" />
                <Item typeName="Boolean" key="EventFilterEnabled">True</Item>
                <Item typeName="IList" key="NeedLoadEventIDs">
                  <Item typeName="Int32">1</Item>
                  <Item typeName="Int32">3</Item>
                  <Item typeName="Int32">4</Item>
                  <Item typeName="Int32">6</Item>
                  <Item typeName="Int32">8</Item>
                  <Item typeName="Int32">10</Item>
                  <Item typeName="Int32">12</Item>
                  <Item typeName="Int32">14</Item>
                  <Item typeName="Int32">82</Item>
                  <Item typeName="Int32">101</Item>
                  <Item typeName="Int32">102</Item>
                  <Item typeName="Int32">103</Item>
                  <Item typeName="Int32">104</Item>
                  <Item typeName="Int32">105</Item>
                  <Item typeName="Int32">106</Item>
                  <Item typeName="Int32">107</Item>
                  <Item typeName="Int32">108</Item>
                  <Item typeName="Int32">109</Item>
                  <Item typeName="Int32">110</Item>
                  <Item typeName="Int32">111</Item>
                  <Item typeName="Int32">112</Item>
                  <Item typeName="Int32">113</Item>
                  <Item typeName="Int32">114</Item>
                  <Item typeName="Int32">115</Item>
                  <Item typeName="Int32">116</Item>
                  <Item typeName="Int32">117</Item>
                  <Item typeName="Int32">118</Item>
                  <Item typeName="Int32">119</Item>
                  <Item typeName="Int32">120</Item>
                  <Item typeName="Int32">121</Item>
                  <Item typeName="Int32">122</Item>
                  <Item typeName="Int32">123</Item>
                  <Item typeName="Int32">124</Item>
                  <Item typeName="Int32">125</Item>
                  <Item typeName="Int32">126</Item>
                  <Item typeName="Int32">127</Item>
                  <Item typeName="Int32">128</Item>
                  <Item typeName="Int32">129</Item>
                  <Item typeName="Int32">130</Item>
                  <Item typeName="Int32">131</Item>
                  <Item typeName="Int32">132</Item>
                  <Item typeName="Int32">133</Item>
                  <Item typeName="Int32">134</Item>
                  <Item typeName="Int32">135</Item>
                  <Item typeName="Int32">136</Item>
                  <Item typeName="Int32">137</Item>
                  <Item typeName="Int32">138</Item>
                  <Item typeName="Int32">139</Item>
                  <Item typeName="Int32">140</Item>
                  <Item typeName="Int32">141</Item>
                  <Item typeName="Int32">142</Item>
                  <Item typeName="Int32">143</Item>
                  <Item typeName="Int32">144</Item>
                  <Item typeName="Int32">145</Item>
                  <Item typeName="Int32">146</Item>
                  <Item typeName="Int32">147</Item>
                  <Item typeName="Int32">148</Item>
                  <Item typeName="Int32">149</Item>
                  <Item typeName="Int32">150</Item>
                  <Item typeName="Int32">151</Item>
                  <Item typeName="Int32">152</Item>
                  <Item typeName="Int32">153</Item>
                  <Item typeName="Int32">154</Item>
                  <Item typeName="Int32">155</Item>
                  <Item typeName="Int32">156</Item>
                  <Item typeName="Int32">157</Item>
                  <Item typeName="Int32">158</Item>
                  <Item typeName="Int32">159</Item>
                  <Item typeName="Int32">160</Item>
                  <Item typeName="Int32">161</Item>
                  <Item typeName="Int32">162</Item>
                  <Item typeName="Int32">163</Item>
                  <Item typeName="Int32">164</Item>
                  <Item typeName="Int32">165</Item>
                  <Item typeName="Int32">166</Item>
                  <Item typeName="Int32">167</Item>
                  <Item typeName="Int32">168</Item>
                  <Item typeName="Int32">169</Item>
                  <Item typeName="Int32">170</Item>
                  <Item typeName="Int32">171</Item>
                  <Item typeName="Int32">172</Item>
                  <Item typeName="Int32">173</Item>
                  <Item typeName="Int32">174</Item>
                  <Item typeName="Int32">175</Item>
                  <Item typeName="Int32">176</Item>
                  <Item typeName="Int32">177</Item>
                  <Item typeName="Int32">178</Item>
                  <Item typeName="Int32">179</Item>
                  <Item typeName="Int32">180</Item>
                  <Item typeName="Int32">181</Item>
                  <Item typeName="Int32">182</Item>
                  <Item typeName="Int32">183</Item>
                  <Item typeName="Int32">184</Item>
                  <Item typeName="Int32">185</Item>
                  <Item typeName="Int32">186</Item>
                  <Item typeName="Int32">187</Item>
                  <Item typeName="Int32">188</Item>
                  <Item typeName="Int32">189</Item>
                  <Item typeName="Int32">190</Item>
                  <Item typeName="Int32">191</Item>
                  <Item typeName="Int32">192</Item>
                  <Item typeName="Int32">193</Item>
                  <Item typeName="Int32">194</Item>
                  <Item typeName="Int32">195</Item>
                  <Item typeName="Int32">196</Item>
                  <Item typeName="Int32">197</Item>
                  <Item typeName="Int32">198</Item>
                  <Item typeName="Int32">199</Item>
                  <Item typeName="Int32">200</Item>
                  <Item typeName="Int32">201</Item>
                  <Item typeName="Int32">202</Item>
                  <Item typeName="Int32">301</Item>
                  <Item typeName="Int32">302</Item>
                  <Item typeName="Int32">303</Item>
                  <Item typeName="Int32">304</Item>
                  <Item typeName="Int32">305</Item>
                  <Item typeName="Int32">306</Item>
                  <Item typeName="Int32">307</Item>
                  <Item typeName="Int32">308</Item>
                  <Item typeName="Int32">309</Item>
                  <Item typeName="Int32">310</Item>
                  <Item typeName="Int32">311</Item>
                  <Item typeName="Int32">312</Item>
                  <Item typeName="Int32">313</Item>
                  <Item typeName="Int32">314</Item>
                  <Item typeName="Int32">315</Item>
                  <Item typeName="Int32">316</Item>
                  <Item typeName="Int32">317</Item>
                  <Item typeName="Int32">318</Item>
                  <Item typeName="Int32">319</Item>
                  <Item typeName="Int32">320</Item>
                  <Item typeName="Int32">321</Item>
                  <Item typeName="Int32">323</Item>
                  <Item typeName="Int32">324</Item>
                  <Item typeName="Int32">325</Item>
                  <Item typeName="Int32">501</Item>
                  <Item typeName="Int32">502</Item>
                  <Item typeName="Int32">503</Item>
                  <Item typeName="Int32">504</Item>
                  <Item typeName="Int32">505</Item>
                  <Item typeName="Int32">506</Item>
                  <Item typeName="Int32">507</Item>
                  <Item typeName="Int32">508</Item>
                  <Item typeName="Int32">509</Item>
                  <Item typeName="Int32">510</Item>
                  <Item typeName="Int32">511</Item>
                  <Item typeName="Int32">512</Item>
                  <Item typeName="Int32">513</Item>
                  <Item typeName="Int32">514</Item>
                  <Item typeName="Int32">515</Item>
                  <Item typeName="Int32">516</Item>
                  <Item typeName="Int32">517</Item>
                  <Item typeName="Int32">518</Item>
                  <Item typeName="Int32">519</Item>
                  <Item typeName="Int32">520</Item>
                  <Item typeName="Int32">521</Item>
                  <Item typeName="Int32">522</Item>
                  <Item typeName="Int32">523</Item>
                  <Item typeName="Int32">524</Item>
                  <Item typeName="Int32">525</Item>
                  <Item typeName="Int32">526</Item>
                  <Item typeName="Int32">527</Item>
                  <Item typeName="Int32">528</Item>
                  <Item typeName="Int32">529</Item>
                  <Item typeName="Int32">530</Item>
                  <Item typeName="Int32">531</Item>
                  <Item typeName="Int32">532</Item>
                  <Item typeName="Int32">533</Item>
                  <Item typeName="Int32">534</Item>
                  <Item typeName="Int32">535</Item>
                  <Item typeName="Int32">536</Item>
                  <Item typeName="Int32">537</Item>
                  <Item typeName="Int32">538</Item>
                  <Item typeName="Int32">539</Item>
                  <Item typeName="Int32">540</Item>
                  <Item typeName="Int32">541</Item>
                  <Item typeName="Int32">542</Item>
                  <Item typeName="Int32">543</Item>
                  <Item typeName="Int32">544</Item>
                  <Item typeName="Int32">545</Item>
                  <Item typeName="Int32">546</Item>
                  <Item typeName="Int32">547</Item>
                  <Item typeName="Int32">548</Item>
                  <Item typeName="Int32">549</Item>
                  <Item typeName="Int32">550</Item>
                  <Item typeName="Int32">551</Item>
                  <Item typeName="Int32">552</Item>
                  <Item typeName="Int32">553</Item>
                  <Item typeName="Int32">554</Item>
                  <Item typeName="Int32">555</Item>
                  <Item typeName="Int32">556</Item>
                  <Item typeName="Int32">557</Item>
                  <Item typeName="Int32">558</Item>
                  <Item typeName="Int32">559</Item>
                  <Item typeName="Int32">560</Item>
                  <Item typeName="Int32">561</Item>
                  <Item typeName="Int32">562</Item>
                  <Item typeName="Int32">563</Item>
                  <Item typeName="Int32">564</Item>
                  <Item typeName="Int32">565</Item>
                  <Item typeName="Int32">566</Item>
                  <Item typeName="Int32">567</Item>
                  <Item typeName="Int32">568</Item>
                  <Item typeName="Int32">569</Item>
                  <Item typeName="Int32">570</Item>
                  <Item typeName="Int32">571</Item>
                  <Item typeName="Int32">572</Item>
                  <Item typeName="Int32">573</Item>
                  <Item typeName="Int32">574</Item>
                  <Item typeName="Int32">575</Item>
                  <Item typeName="Int32">576</Item>
                  <Item typeName="Int32">577</Item>
                  <Item typeName="Int32">578</Item>
                  <Item typeName="Int32">579</Item>
                  <Item typeName="Int32">580</Item>
                  <Item typeName="Int32">581</Item>
                  <Item typeName="Int32">582</Item>
                  <Item typeName="Int32">583</Item>
                  <Item typeName="Int32">584</Item>
                  <Item typeName="Int32">585</Item>
                  <Item typeName="Int32">586</Item>
                  <Item typeName="Int32">587</Item>
                  <Item typeName="Int32">588</Item>
                  <Item typeName="Int32">589</Item>
                  <Item typeName="Int32">590</Item>
                  <Item typeName="Int32">591</Item>
                  <Item typeName="Int32">592</Item>
                  <Item typeName="Int32">593</Item>
                  <Item typeName="Int32">594</Item>
                  <Item typeName="Int32">595</Item>
                  <Item typeName="Int32">596</Item>
                  <Item typeName="Int32">597</Item>
                  <Item typeName="Int32">598</Item>
                  <Item typeName="Int32">599</Item>
                  <Item typeName="Int32">600</Item>
                  <Item typeName="Int32">601</Item>
                  <Item typeName="Int32">602</Item>
                  <Item typeName="Int32">603</Item>
                  <Item typeName="Int32">604</Item>
                  <Item typeName="Int32">607</Item>
                  <Item typeName="Int32">613</Item>
                  <Item typeName="Int32">614</Item>
                  <Item typeName="Int32">615</Item>
                  <Item typeName="Int32">616</Item>
                  <Item typeName="Int32">617</Item>
                  <Item typeName="Int32">618</Item>
                </Item>
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String">MS</Item>
                  <Item typeName="String" />
                  <Item typeName="String">Name</Item>
                  <Item typeName="String">Time</Item>
                  <Item typeName="String">File Name</Item>
                  <Item typeName="String">LAC</Item>
                  <Item typeName="String">RAC</Item>
                  <Item typeName="String">CI</Item>
                  <Item typeName="String">Target LAC</Item>
                  <Item typeName="String">Target RAC</Item>
                  <Item typeName="String">Target CI</Item>
                  <Item typeName="String">Longitude</Item>
                  <Item typeName="String">Latitude</Item>
                  <Item typeName="String">Index</Item>
                  <Item typeName="String">Info</Item>
                </Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">79</Item>
                  <Item typeName="Int32">21</Item>
                  <Item typeName="Int32">229</Item>
                  <Item typeName="Int32">181</Item>
                  <Item typeName="Int32">281</Item>
                  <Item typeName="Int32">137</Item>
                  <Item typeName="Int32">74</Item>
                  <Item typeName="Int32">190</Item>
                  <Item typeName="Int32">104</Item>
                  <Item typeName="Int32">130</Item>
                  <Item typeName="Int32">174</Item>
                  <Item typeName="Int32">255</Item>
                  <Item typeName="Int32">526</Item>
                  <Item typeName="Int32">473</Item>
                  <Item typeName="Int32">493</Item>
                </Item>
                <Item typeName="IList" key="VisibleColumnIndexs">
                  <Item typeName="Int32">0</Item>
                  <Item typeName="Int32">1</Item>
                  <Item typeName="Int32">2</Item>
                  <Item typeName="Int32">3</Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
                <Item typeName="Boolean" key="IsMTRForm">False</Item>
              </Item>
            </Item>
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.BriefDataGridFormMessage</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_signaling.gif</Item>
              <Item name="Text" typeName="String">UMLayer3信令</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">518</Item>
              <Item name="LocationY" typeName="Int32">0</Item>
              <Item name="SizeWidth" typeName="Int32">560</Item>
              <Item name="SizeHeight" typeName="Int32">652</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Boolean" key="EquipmentFilterEnabled">False</Item>
                <Item typeName="IList" key="NeedLoadEquipmentFlags">
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                </Item>
                <Item typeName="Boolean" key="MessageFilterEnabled">True</Item>
                <Item typeName="IList" key="NeedLoadMessageIDs">
                  <Item typeName="Int32">769</Item>
                  <Item typeName="Int32">776</Item>
                  <Item typeName="Int32">770</Item>
                  <Item typeName="Int32">825</Item>
                  <Item typeName="Int32">775</Item>
                  <Item typeName="Int32">783</Item>
                  <Item typeName="Int32">805</Item>
                  <Item typeName="Int32">782</Item>
                  <Item typeName="Int32">826</Item>
                  <Item typeName="Int32">792</Item>
                  <Item typeName="Int32">793</Item>
                  <Item typeName="Int32">794</Item>
                  <Item typeName="Int32">791</Item>
                  <Item typeName="Int32">799</Item>
                  <Item typeName="Int32">787</Item>
                  <Item typeName="Int32">831</Item>
                  <Item typeName="Int32">771</Item>
                  <Item typeName="Int32">779</Item>
                  <Item typeName="Int32">813</Item>
                  <Item typeName="Int32">810</Item>
                  <Item typeName="Int32">796</Item>
                  <Item typeName="Int32">797</Item>
                  <Item typeName="Int32">798</Item>
                  <Item typeName="Int32">773</Item>
                  <Item typeName="Int32">777</Item>
                  <Item typeName="Int32">821</Item>
                  <Item typeName="Int32">822</Item>
                  <Item typeName="Int32">823</Item>
                  <Item typeName="Int32">830</Item>
                  <Item typeName="Int32">820</Item>
                  <Item typeName="Int32">817</Item>
                  <Item typeName="Int32">818</Item>
                  <Item typeName="Int32">784</Item>
                  <Item typeName="Int32">1595</Item>
                  <Item typeName="Int32">1582</Item>
                  <Item typeName="Int32">1577</Item>
                  <Item typeName="Int32">1583</Item>
                  <Item typeName="Int32">1552</Item>
                  <Item typeName="Int32">1559</Item>
                  <Item typeName="Int32">1549</Item>
                  <Item typeName="Int32">1791</Item>
                  <Item typeName="Int32">1589</Item>
                  <Item typeName="Int32">1586</Item>
                  <Item typeName="Int32">1558</Item>
                  <Item typeName="Int32">1555</Item>
                  <Item typeName="Int32">1585</Item>
                  <Item typeName="Int32">1584</Item>
                  <Item typeName="Int32">1587</Item>
                  <Item typeName="Int32">1591</Item>
                  <Item typeName="Int32">1590</Item>
                  <Item typeName="Int32">1556</Item>
                  <Item typeName="Int32">1588</Item>
                  <Item typeName="Int32">1789</Item>
                  <Item typeName="Int32">1579</Item>
                  <Item typeName="Int32">1580</Item>
                  <Item typeName="Int32">1576</Item>
                  <Item typeName="Int32">1599</Item>
                  <Item typeName="Int32">1593</Item>
                  <Item typeName="Int32">1594</Item>
                  <Item typeName="Int32">1557</Item>
                  <Item typeName="Int32">1573</Item>
                  <Item typeName="Int32">1568</Item>
                  <Item typeName="Int32">1569</Item>
                  <Item typeName="Int32">1570</Item>
                  <Item typeName="Int32">1572</Item>
                  <Item typeName="Int32">1575</Item>
                  <Item typeName="Int32">1546</Item>
                  <Item typeName="Int32">1551</Item>
                  <Item typeName="Int32">1571</Item>
                  <Item typeName="Int32">1581</Item>
                  <Item typeName="Int32">1574</Item>
                  <Item typeName="Int32">1547</Item>
                  <Item typeName="Int32">1596</Item>
                  <Item typeName="Int32">1554</Item>
                  <Item typeName="Int32">1544</Item>
                  <Item typeName="Int32">1790</Item>
                  <Item typeName="Int32">1561</Item>
                  <Item typeName="Int32">1536</Item>
                  <Item typeName="Int32">1562</Item>
                  <Item typeName="Int32">1543</Item>
                  <Item typeName="Int32">1538</Item>
                  <Item typeName="Int32">1543</Item>
                  <Item typeName="Int32">1539</Item>
                  <Item typeName="Int32">1563</Item>
                  <Item typeName="Int32">1564</Item>
                  <Item typeName="Int32">1565</Item>
                  <Item typeName="Int32">1541</Item>
                  <Item typeName="Int32">1542</Item>
                  <Item typeName="Int32">1566</Item>
                  <Item typeName="Int32">1567</Item>
                  <Item typeName="Int32">1560</Item>
                  <Item typeName="Int32">1540</Item>
                  <Item typeName="Int32">1553</Item>
                  <Item typeName="Int32">1578</Item>
                  <Item typeName="Int32">1548</Item>
                  <Item typeName="Int32">1550</Item>
                  <Item typeName="Int32">1545</Item>
                  <Item typeName="Int32">1321</Item>
                  <Item typeName="Int32">1297</Item>
                  <Item typeName="Int32">1298</Item>
                  <Item typeName="Int32">1300</Item>
                  <Item typeName="Int32">1320</Item>
                  <Item typeName="Int32">1315</Item>
                  <Item typeName="Int32">1313</Item>
                  <Item typeName="Int32">1317</Item>
                  <Item typeName="Int32">1314</Item>
                  <Item typeName="Int32">1316</Item>
                  <Item typeName="Int32">1304</Item>
                  <Item typeName="Int32">1305</Item>
                  <Item typeName="Int32">1281</Item>
                  <Item typeName="Int32">1282</Item>
                  <Item typeName="Int32">1284</Item>
                  <Item typeName="Int32">1288</Item>
                  <Item typeName="Int32">1330</Item>
                  <Item typeName="Int32">1328</Item>
                  <Item typeName="Int32">1329</Item>
                  <Item typeName="Int32">1318</Item>
                  <Item typeName="Int32">1306</Item>
                  <Item typeName="Int32">1307</Item>
                  <Item typeName="Int32">2050</Item>
                  <Item typeName="Int32">2051</Item>
                  <Item typeName="Int32">2052</Item>
                  <Item typeName="Int32">2049</Item>
                  <Item typeName="Int32">2068</Item>
                  <Item typeName="Int32">2066</Item>
                  <Item typeName="Int32">2067</Item>
                  <Item typeName="Int32">2054</Item>
                  <Item typeName="Int32">2053</Item>
                  <Item typeName="Int32">2081</Item>
                  <Item typeName="Int32">2060</Item>
                  <Item typeName="Int32">2080</Item>
                  <Item typeName="Int32">2069</Item>
                  <Item typeName="Int32">2070</Item>
                  <Item typeName="Int32">2064</Item>
                  <Item typeName="Int32">2065</Item>
                  <Item typeName="Int32">2057</Item>
                  <Item typeName="Int32">2058</Item>
                  <Item typeName="Int32">2059</Item>
                  <Item typeName="Int32">2056</Item>
                  <Item typeName="Int32">2641</Item>
                  <Item typeName="Int32">2642</Item>
                  <Item typeName="Int32">2640</Item>
                  <Item typeName="Int32">2626</Item>
                  <Item typeName="Int32">2627</Item>
                  <Item typeName="Int32">2625</Item>
                  <Item typeName="Int32">2644</Item>
                  <Item typeName="Int32">2643</Item>
                  <Item typeName="Int32">2631</Item>
                  <Item typeName="Int32">2630</Item>
                  <Item typeName="Int32">2633</Item>
                  <Item typeName="Int32">2632</Item>
                  <Item typeName="Int32">2628</Item>
                  <Item typeName="Int32">2629</Item>
                  <Item typeName="Int32">2645</Item>
                  <Item typeName="Int32">2308</Item>
                  <Item typeName="Int32">2305</Item>
                  <Item typeName="Int32">2320</Item>
                  <Item typeName="Int32">4360</Item>
                  <Item typeName="Int32">4129</Item>
                  <Item typeName="Int32">4352</Item>
                  <Item typeName="Int32">4097</Item>
                  <Item typeName="Int32">4353</Item>
                  <Item typeName="Int32">4354</Item>
                  <Item typeName="Int32">4098</Item>
                  <Item typeName="Int32">4133</Item>
                  <Item typeName="Int32">4362</Item>
                  <Item typeName="Int32">4099</Item>
                  <Item typeName="Int32">4356</Item>
                  <Item typeName="Int32">4358</Item>
                  <Item typeName="Int32">4130</Item>
                  <Item typeName="Int32">4361</Item>
                  <Item typeName="Int32">4131</Item>
                  <Item typeName="Int32">4100</Item>
                  <Item typeName="Int32">4101</Item>
                  <Item typeName="Int32">4132</Item>
                  <Item typeName="Int32">4359</Item>
                  <Item typeName="Int32">4102</Item>
                  <Item typeName="Int32">4357</Item>
                  <Item typeName="Int32">4104</Item>
                  <Item typeName="Int32">4103</Item>
                  <Item typeName="Int32">4105</Item>
                  <Item typeName="Int32">4106</Item>
                  <Item typeName="Int32">4355</Item>
                  <Item typeName="Int32">4145</Item>
                  <Item typeName="Int32">4151</Item>
                  <Item typeName="Int32">4146</Item>
                  <Item typeName="Int32">4147</Item>
                  <Item typeName="Int32">4148</Item>
                  <Item typeName="Int32">4149</Item>
                  <Item typeName="Int32">4150</Item>
                  <Item typeName="Int32">4144</Item>
                  <Item typeName="Int32">4152</Item>
                  <Item typeName="Int32">4153</Item>
                  <Item typeName="Int32">1899627265</Item>
                  <Item typeName="Int32">1899627272</Item>
                  <Item typeName="Int32">1899627266</Item>
                  <Item typeName="Int32">1899627268</Item>
                  <Item typeName="Int32">1899627270</Item>
                  <Item typeName="Int32">1899627321</Item>
                  <Item typeName="Int32">1899627271</Item>
                  <Item typeName="Int32">1899627279</Item>
                  <Item typeName="Int32">1899627301</Item>
                  <Item typeName="Int32">1899627278</Item>
                  <Item typeName="Int32">1899627322</Item>
                  <Item typeName="Int32">1899627288</Item>
                  <Item typeName="Int32">1899627289</Item>
                  <Item typeName="Int32">1899627290</Item>
                  <Item typeName="Int32">1899627287</Item>
                  <Item typeName="Int32">1899627295</Item>
                  <Item typeName="Int32">1899627283</Item>
                  <Item typeName="Int32">1899627326</Item>
                  <Item typeName="Int32">1899627267</Item>
                  <Item typeName="Int32">1899627275</Item>
                  <Item typeName="Int32">1899627309</Item>
                  <Item typeName="Int32">1899627306</Item>
                  <Item typeName="Int32">1899627292</Item>
                  <Item typeName="Int32">1899627293</Item>
                  <Item typeName="Int32">1899627294</Item>
                  <Item typeName="Int32">1899627269</Item>
                  <Item typeName="Int32">1899627273</Item>
                  <Item typeName="Int32">1899627317</Item>
                  <Item typeName="Int32">1899627318</Item>
                  <Item typeName="Int32">1899627319</Item>
                  <Item typeName="Int32">1899627325</Item>
                  <Item typeName="Int32">1899627316</Item>
                  <Item typeName="Int32">1899627313</Item>
                  <Item typeName="Int32">1899627314</Item>
                  <Item typeName="Int32">1899627280</Item>
                  <Item typeName="Int32">1595</Item>
                  <Item typeName="Int32">1582</Item>
                  <Item typeName="Int32">1577</Item>
                  <Item typeName="Int32">1583</Item>
                  <Item typeName="Int32">1552</Item>
                  <Item typeName="Int32">1559</Item>
                  <Item typeName="Int32">1549</Item>
                  <Item typeName="Int32">1791</Item>
                  <Item typeName="Int32">1589</Item>
                  <Item typeName="Int32">1586</Item>
                  <Item typeName="Int32">1558</Item>
                  <Item typeName="Int32">1555</Item>
                  <Item typeName="Int32">1585</Item>
                  <Item typeName="Int32">1584</Item>
                  <Item typeName="Int32">1587</Item>
                  <Item typeName="Int32">1591</Item>
                  <Item typeName="Int32">1590</Item>
                  <Item typeName="Int32">1556</Item>
                  <Item typeName="Int32">1588</Item>
                  <Item typeName="Int32">1789</Item>
                  <Item typeName="Int32">1579</Item>
                  <Item typeName="Int32">1580</Item>
                  <Item typeName="Int32">1576</Item>
                  <Item typeName="Int32">1599</Item>
                  <Item typeName="Int32">1593</Item>
                  <Item typeName="Int32">1594</Item>
                  <Item typeName="Int32">1557</Item>
                  <Item typeName="Int32">1573</Item>
                  <Item typeName="Int32">1568</Item>
                  <Item typeName="Int32">1569</Item>
                  <Item typeName="Int32">1570</Item>
                  <Item typeName="Int32">1572</Item>
                  <Item typeName="Int32">1575</Item>
                  <Item typeName="Int32">1546</Item>
                  <Item typeName="Int32">1551</Item>
                  <Item typeName="Int32">1571</Item>
                  <Item typeName="Int32">1581</Item>
                  <Item typeName="Int32">1547</Item>
                  <Item typeName="Int32">1574</Item>
                  <Item typeName="Int32">1596</Item>
                  <Item typeName="Int32">1554</Item>
                  <Item typeName="Int32">1544</Item>
                  <Item typeName="Int32">1561</Item>
                  <Item typeName="Int32">1536</Item>
                  <Item typeName="Int32">1562</Item>
                  <Item typeName="Int32">1538</Item>
                  <Item typeName="Int32">1539</Item>
                  <Item typeName="Int32">1563</Item>
                  <Item typeName="Int32">1564</Item>
                  <Item typeName="Int32">1565</Item>
                  <Item typeName="Int32">1541</Item>
                  <Item typeName="Int32">1542</Item>
                  <Item typeName="Int32">1566</Item>
                  <Item typeName="Int32">1567</Item>
                  <Item typeName="Int32">1560</Item>
                  <Item typeName="Int32">1540</Item>
                  <Item typeName="Int32">1553</Item>
                  <Item typeName="Int32">1578</Item>
                  <Item typeName="Int32">1548</Item>
                  <Item typeName="Int32">1550</Item>
                  <Item typeName="Int32">1545</Item>
                  <Item typeName="Int32">1899627817</Item>
                  <Item typeName="Int32">1899627793</Item>
                  <Item typeName="Int32">1899627794</Item>
                  <Item typeName="Int32">1899627796</Item>
                  <Item typeName="Int32">1899627816</Item>
                  <Item typeName="Int32">1899627811</Item>
                  <Item typeName="Int32">1899627809</Item>
                  <Item typeName="Int32">1899627813</Item>
                  <Item typeName="Int32">1899627810</Item>
                  <Item typeName="Int32">1899627812</Item>
                  <Item typeName="Int32">1899627800</Item>
                  <Item typeName="Int32">1899627801</Item>
                  <Item typeName="Int32">1899627777</Item>
                  <Item typeName="Int32">1899627778</Item>
                  <Item typeName="Int32">1899627780</Item>
                  <Item typeName="Int32">1899627784</Item>
                  <Item typeName="Int32">1899627826</Item>
                  <Item typeName="Int32">1899627824</Item>
                  <Item typeName="Int32">1899627825</Item>
                  <Item typeName="Int32">1899627814</Item>
                  <Item typeName="Int32">1899627802</Item>
                  <Item typeName="Int32">1899627803</Item>
                  <Item typeName="Int32">1899628546</Item>
                  <Item typeName="Int32">1899628547</Item>
                  <Item typeName="Int32">1899628548</Item>
                  <Item typeName="Int32">1899628545</Item>
                  <Item typeName="Int32">1899628572</Item>
                  <Item typeName="Int32">1899628564</Item>
                  <Item typeName="Int32">1899628562</Item>
                  <Item typeName="Int32">1899628563</Item>
                  <Item typeName="Int32">1899628550</Item>
                  <Item typeName="Int32">1899628549</Item>
                  <Item typeName="Int32">1899628565</Item>
                  <Item typeName="Int32">1899628566</Item>
                  <Item typeName="Int32">1899628577</Item>
                  <Item typeName="Int32">1899628557</Item>
                  <Item typeName="Int32">1899628558</Item>
                  <Item typeName="Int32">1899628556</Item>
                  <Item typeName="Int32">1899628576</Item>
                  <Item typeName="Int32">1899628560</Item>
                  <Item typeName="Int32">1899628561</Item>
                  <Item typeName="Int32">1899628553</Item>
                  <Item typeName="Int32">1899628554</Item>
                  <Item typeName="Int32">1899628555</Item>
                  <Item typeName="Int32">1899628552</Item>
                  <Item typeName="Int32">1899629137</Item>
                  <Item typeName="Int32">1899629138</Item>
                  <Item typeName="Int32">1899629136</Item>
                  <Item typeName="Int32">1899629122</Item>
                  <Item typeName="Int32">1899629123</Item>
                  <Item typeName="Int32">1899629121</Item>
                  <Item typeName="Int32">1899629134</Item>
                  <Item typeName="Int32">1899629135</Item>
                  <Item typeName="Int32">1899629133</Item>
                  <Item typeName="Int32">1899629140</Item>
                  <Item typeName="Int32">1899629139</Item>
                  <Item typeName="Int32">1899629127</Item>
                  <Item typeName="Int32">1899629126</Item>
                  <Item typeName="Int32">1899629131</Item>
                  <Item typeName="Int32">1899629129</Item>
                  <Item typeName="Int32">1899629132</Item>
                  <Item typeName="Int32">1899629128</Item>
                  <Item typeName="Int32">1899629130</Item>
                  <Item typeName="Int32">1899629124</Item>
                  <Item typeName="Int32">1899629125</Item>
                  <Item typeName="Int32">1899629141</Item>
                  <Item typeName="Int32">1378222346</Item>
                  <Item typeName="Int32">1378222344</Item>
                  <Item typeName="Int32">1378222113</Item>
                  <Item typeName="Int32">1378222336</Item>
                  <Item typeName="Int32">1378222081</Item>
                  <Item typeName="Int32">1378222337</Item>
                  <Item typeName="Int32">1378222338</Item>
                  <Item typeName="Int32">1378222082</Item>
                  <Item typeName="Int32">1378222117</Item>
                  <Item typeName="Int32">1378222347</Item>
                  <Item typeName="Int32">1378222083</Item>
                  <Item typeName="Int32">1378222340</Item>
                  <Item typeName="Int32">1378222342</Item>
                  <Item typeName="Int32">1378222114</Item>
                  <Item typeName="Int32">1378222345</Item>
                  <Item typeName="Int32">1378222115</Item>
                  <Item typeName="Int32">1378222084</Item>
                  <Item typeName="Int32">1378222085</Item>
                  <Item typeName="Int32">1378222116</Item>
                  <Item typeName="Int32">1378222343</Item>
                  <Item typeName="Int32">1378222086</Item>
                  <Item typeName="Int32">1378222341</Item>
                  <Item typeName="Int32">1378222129</Item>
                  <Item typeName="Int32">1378222135</Item>
                  <Item typeName="Int32">1378222130</Item>
                  <Item typeName="Int32">1378222131</Item>
                  <Item typeName="Int32">1378222132</Item>
                  <Item typeName="Int32">1378222133</Item>
                  <Item typeName="Int32">1378222134</Item>
                  <Item typeName="Int32">1378222112</Item>
                  <Item typeName="Int32">1378222136</Item>
                  <Item typeName="Int32">1378222137</Item>
                  <Item typeName="Int32">1378222088</Item>
                  <Item typeName="Int32">1378222087</Item>
                  <Item typeName="Int32">1378222089</Item>
                  <Item typeName="Int32">1378222090</Item>
                  <Item typeName="Int32">1378222339</Item>
                  <Item typeName="Int32">1093599233</Item>
                  <Item typeName="Int32">1093599234</Item>
                  <Item typeName="Int32">1093599235</Item>
                  <Item typeName="Int32">1093599489</Item>
                  <Item typeName="Int32">1093599490</Item>
                  <Item typeName="Int32">1093599491</Item>
                  <Item typeName="Int32">1093599492</Item>
                  <Item typeName="Int32">1093599493</Item>
                  <Item typeName="Int32">1093599494</Item>
                  <Item typeName="Int32">1093599495</Item>
                  <Item typeName="Int32">1093599496</Item>
                  <Item typeName="Int32">1093599497</Item>
                  <Item typeName="Int32">1093599498</Item>
                  <Item typeName="Int32">1093599499</Item>
                  <Item typeName="Int32">1093599500</Item>
                  <Item typeName="Int32">1093599501</Item>
                  <Item typeName="Int32">1093599502</Item>
                  <Item typeName="Int32">1093599503</Item>
                  <Item typeName="Int32">1093599504</Item>
                  <Item typeName="Int32">1093599505</Item>
                  <Item typeName="Int32">1093599506</Item>
                  <Item typeName="Int32">1093599507</Item>
                  <Item typeName="Int32">1093599508</Item>
                  <Item typeName="Int32">1093599509</Item>
                  <Item typeName="Int32">1093599510</Item>
                  <Item typeName="Int32">1093599511</Item>
                  <Item typeName="Int32">1093599512</Item>
                  <Item typeName="Int32">1093599513</Item>
                  <Item typeName="Int32">1093599514</Item>
                  <Item typeName="Int32">1093599515</Item>
                  <Item typeName="Int32">1093599516</Item>
                  <Item typeName="Int32">1093599517</Item>
                  <Item typeName="Int32">1093599518</Item>
                  <Item typeName="Int32">1093599745</Item>
                  <Item typeName="Int32">1093599746</Item>
                  <Item typeName="Int32">1093599747</Item>
                  <Item typeName="Int32">1093599748</Item>
                  <Item typeName="Int32">1093599749</Item>
                  <Item typeName="Int32">1093600001</Item>
                  <Item typeName="Int32">1093600002</Item>
                  <Item typeName="Int32">1093600003</Item>
                  <Item typeName="Int32">1093600004</Item>
                  <Item typeName="Int32">1093600005</Item>
                  <Item typeName="Int32">1093600006</Item>
                  <Item typeName="Int32">1093600007</Item>
                  <Item typeName="Int32">1093600008</Item>
                  <Item typeName="Int32">1093600009</Item>
                  <Item typeName="Int32">1093600010</Item>
                  <Item typeName="Int32">1093600011</Item>
                  <Item typeName="Int32">1093600012</Item>
                  <Item typeName="Int32">1093600013</Item>
                  <Item typeName="Int32">1093600014</Item>
                  <Item typeName="Int32">1093600015</Item>
                  <Item typeName="Int32">1093600016</Item>
                  <Item typeName="Int32">1093600017</Item>
                  <Item typeName="Int32">1093600018</Item>
                  <Item typeName="Int32">1093600019</Item>
                  <Item typeName="Int32">1093600020</Item>
                  <Item typeName="Int32">1093600021</Item>
                  <Item typeName="Int32">1093600022</Item>
                  <Item typeName="Int32">1093600023</Item>
                  <Item typeName="Int32">1093600024</Item>
                  <Item typeName="Int32">1093600025</Item>
                  <Item typeName="Int32">1093600032</Item>
                  <Item typeName="Int32">1093600257</Item>
                  <Item typeName="Int32">1093600258</Item>
                  <Item typeName="Int32">1093600259</Item>
                  <Item typeName="Int32">1093600260</Item>
                  <Item typeName="Int32">1093600261</Item>
                  <Item typeName="Int32">1093600262</Item>
                  <Item typeName="Int32">1093600263</Item>
                  <Item typeName="Int32">1093600264</Item>
                  <Item typeName="Int32">1093600265</Item>
                  <Item typeName="Int32">1093600266</Item>
                  <Item typeName="Int32">1093600267</Item>
                  <Item typeName="Int32">1093600268</Item>
                  <Item typeName="Int32">1093600269</Item>
                  <Item typeName="Int32">1093600270</Item>
                  <Item typeName="Int32">1093600271</Item>
                  <Item typeName="Int32">1093600272</Item>
                  <Item typeName="Int32">1093600273</Item>
                  <Item typeName="Int32">1093600274</Item>
                  <Item typeName="Int32">1093600275</Item>
                  <Item typeName="Int32">1093600276</Item>
                  <Item typeName="Int32">1093600277</Item>
                  <Item typeName="Int32">1093600278</Item>
                  <Item typeName="Int32">1093600279</Item>
                  <Item typeName="Int32">1093600280</Item>
                  <Item typeName="Int32">1093600281</Item>
                  <Item typeName="Int32">1093600282</Item>
                  <Item typeName="Int32">1093600283</Item>
                  <Item typeName="Int32">1093600284</Item>
                  <Item typeName="Int32">1093600285</Item>
                  <Item typeName="Int32">1093600286</Item>
                  <Item typeName="Int32">1093600513</Item>
                  <Item typeName="Int32">1093600514</Item>
                  <Item typeName="Int32">1093600769</Item>
                  <Item typeName="Int32">1093601025</Item>
                  <Item typeName="Int32">1093601281</Item>
                  <Item typeName="Int32">268697856</Item>
                  <Item typeName="Int32">268698120</Item>
                  <Item typeName="Int32">268698124</Item>
                  <Item typeName="Int32">268698125</Item>
                  <Item typeName="Int32">268698176</Item>
                  <Item typeName="Int32">268698196</Item>
                  <Item typeName="Int32">268698232</Item>
                  <Item typeName="Int32">268698236</Item>
                  <Item typeName="Int32">268960768</Item>
                  <Item typeName="Int32">268698624</Item>
                  <Item typeName="Int32">268698880</Item>
                  <Item typeName="Int32">268763648</Item>
                  <Item typeName="Int32">268767232</Item>
                  <Item typeName="Int32">268767488</Item>
                  <Item typeName="Int32">268699904</Item>
                  <Item typeName="Int32">268700160</Item>
                  <Item typeName="Int32">268772352</Item>
                  <Item typeName="Int32">268771072</Item>
                  <Item typeName="Int32">268763400</Item>
                  <Item typeName="Int32">268763404</Item>
                  <Item typeName="Int32">268763405</Item>
                  <Item typeName="Int32">268763412</Item>
                  <Item typeName="Int32">268763436</Item>
                  <Item typeName="Int32">268763437</Item>
                  <Item typeName="Int32">268763438</Item>
                  <Item typeName="Int32">268763456</Item>
                  <Item typeName="Int32">268763468</Item>
                  <Item typeName="Int32">268763472</Item>
                  <Item typeName="Int32">268763476</Item>
                  <Item typeName="Int32">268763484</Item>
                  <Item typeName="Int32">268763486</Item>
                  <Item typeName="Int32">268763488</Item>
                  <Item typeName="Int32">268763492</Item>
                  <Item typeName="Int32">268763493</Item>
                  <Item typeName="Int32">268763508</Item>
                  <Item typeName="Int32">268763512</Item>
                  <Item typeName="Int32">268763516</Item>
                  <Item typeName="Int32">268763648</Item>
                  <Item typeName="Int32">268963328</Item>
                  <Item typeName="Int32">268764160</Item>
                  <Item typeName="Int32">268764416</Item>
                  <Item typeName="Int32">268764672</Item>
                  <Item typeName="Int32">268962048</Item>
                  <Item typeName="Int32">268765184</Item>
                  <Item typeName="Int32">268765440</Item>
                  <Item typeName="Int32">268765696</Item>
                  <Item typeName="Int32">268765952</Item>
                  <Item typeName="Int32">268964352</Item>
                  <Item typeName="Int32">268964608</Item>
                  <Item typeName="Int32">268766720</Item>
                  <Item typeName="Int32">268965120</Item>
                  <Item typeName="Int32">268767232</Item>
                  <Item typeName="Int32">268767488</Item>
                  <Item typeName="Int32">268767744</Item>
                  <Item typeName="Int32">268768000</Item>
                  <Item typeName="Int32">268768256</Item>
                  <Item typeName="Int32">268768512</Item>
                  <Item typeName="Int32">268768768</Item>
                  <Item typeName="Int32">268769024</Item>
                  <Item typeName="Int32">268769280</Item>
                  <Item typeName="Int32">268769792</Item>
                  <Item typeName="Int32">268970496</Item>
                  <Item typeName="Int32">268770304</Item>
                  <Item typeName="Int32">268770560</Item>
                  <Item typeName="Int32">268770816</Item>
                  <Item typeName="Int32">268771072</Item>
                  <Item typeName="Int32">268771328</Item>
                  <Item typeName="Int32">268970240</Item>
                  <Item typeName="Int32">268771840</Item>
                  <Item typeName="Int32">268772096</Item>
                  <Item typeName="Int32">268772352</Item>
                  <Item typeName="Int32">268772608</Item>
                  <Item typeName="Int32">268971008</Item>
                  <Item typeName="Int32">268971776</Item>
                  <Item typeName="Int32">268972288</Item>
                  <Item typeName="Int32">268763137</Item>
                  <Item typeName="Int32">268763393</Item>
                  <Item typeName="Int32">268974096</Item>
                  <Item typeName="Int32">268974352</Item>
                  <Item typeName="Int32">268777985</Item>
                  <Item typeName="Int32">268778241</Item>
                  <Item typeName="Int32">268778497</Item>
                  <Item typeName="Int32">268828928</Item>
                  <Item typeName="Int32">268894464</Item>
                  <Item typeName="Int32">268894720</Item>
                  <Item typeName="Int32">268894976</Item>
                  <Item typeName="Int32">268895232</Item>
                  <Item typeName="Int32">268895488</Item>
                  <Item typeName="Int32">268895744</Item>
                  <Item typeName="Int32">268896004</Item>
                  <Item typeName="Int32">268896008</Item>
                  <Item typeName="Int32">268896016</Item>
                  <Item typeName="Int32">268896024</Item>
                  <Item typeName="Int32">268896036</Item>
                  <Item typeName="Int32">268896064</Item>
                  <Item typeName="Int32">268896073</Item>
                  <Item typeName="Int32">268896074</Item>
                  <Item typeName="Int32">268896075</Item>
                  <Item typeName="Int32">268896084</Item>
                  <Item typeName="Int32">268896108</Item>
                  <Item typeName="Int32">268896109</Item>
                  <Item typeName="Int32">268896110</Item>
                  <Item typeName="Int32">268896120</Item>
                  <Item typeName="Int32">268896124</Item>
                  <Item typeName="Int32">268896128</Item>
                  <Item typeName="Int32">268896256</Item>
                  <Item typeName="Int32">268896512</Item>
                  <Item typeName="Int32">268960256</Item>
                  <Item typeName="Int32">268963072</Item>
                  <Item typeName="Int32">268897280</Item>
                  <Item typeName="Int32">268897536</Item>
                  <Item typeName="Int32">268897792</Item>
                  <Item typeName="Int32">268963840</Item>
                  <Item typeName="Int32">268965632</Item>
                  <Item typeName="Int32">268898560</Item>
                  <Item typeName="Int32">268898816</Item>
                  <Item typeName="Int32">268965376</Item>
                  <Item typeName="Int32">268899328</Item>
                  <Item typeName="Int32">268899584</Item>
                  <Item typeName="Int32">268899840</Item>
                  <Item typeName="Int32">268900096</Item>
                  <Item typeName="Int32">268900352</Item>
                  <Item typeName="Int32">268900608</Item>
                  <Item typeName="Int32">268900864</Item>
                  <Item typeName="Int32">268901120</Item>
                  <Item typeName="Int32">268969216</Item>
                  <Item typeName="Int32">268901632</Item>
                  <Item typeName="Int32">268901888</Item>
                  <Item typeName="Int32">268902144</Item>
                  <Item typeName="Int32">268902400</Item>
                  <Item typeName="Int32">268902656</Item>
                  <Item typeName="Int32">268970752</Item>
                  <Item typeName="Int32">268903168</Item>
                  <Item typeName="Int32">268960008</Item>
                  <Item typeName="Int32">268960012</Item>
                  <Item typeName="Int32">268960020</Item>
                  <Item typeName="Int32">268960024</Item>
                  <Item typeName="Int32">268960040</Item>
                  <Item typeName="Int32">268960064</Item>
                  <Item typeName="Int32">268960068</Item>
                  <Item typeName="Int32">268960069</Item>
                  <Item typeName="Int32">268960073</Item>
                  <Item typeName="Int32">268960074</Item>
                  <Item typeName="Int32">268960076</Item>
                  <Item typeName="Int32">268960080</Item>
                  <Item typeName="Int32">268960084</Item>
                  <Item typeName="Int32">268960088</Item>
                  <Item typeName="Int32">268960092</Item>
                  <Item typeName="Int32">268960100</Item>
                  <Item typeName="Int32">268960101</Item>
                  <Item typeName="Int32">268960104</Item>
                  <Item typeName="Int32">268960116</Item>
                  <Item typeName="Int32">268960120</Item>
                  <Item typeName="Int32">268960128</Item>
                  <Item typeName="Int32">268960256</Item>
                  <Item typeName="Int32">268960512</Item>
                  <Item typeName="Int32">268960768</Item>
                  <Item typeName="Int32">268961024</Item>
                  <Item typeName="Int32">268961280</Item>
                  <Item typeName="Int32">268961536</Item>
                  <Item typeName="Int32">268961792</Item>
                  <Item typeName="Int32">268962048</Item>
                  <Item typeName="Int32">268962304</Item>
                  <Item typeName="Int32">268962560</Item>
                  <Item typeName="Int32">268962816</Item>
                  <Item typeName="Int32">268963072</Item>
                  <Item typeName="Int32">268963328</Item>
                  <Item typeName="Int32">268963584</Item>
                  <Item typeName="Int32">268963840</Item>
                  <Item typeName="Int32">268964096</Item>
                  <Item typeName="Int32">268964352</Item>
                  <Item typeName="Int32">268964608</Item>
                  <Item typeName="Int32">268964864</Item>
                  <Item typeName="Int32">268965120</Item>
                  <Item typeName="Int32">268965376</Item>
                  <Item typeName="Int32">268965632</Item>
                  <Item typeName="Int32">268965888</Item>
                  <Item typeName="Int32">268966144</Item>
                  <Item typeName="Int32">268966400</Item>
                  <Item typeName="Int32">268966656</Item>
                  <Item typeName="Int32">268966912</Item>
                  <Item typeName="Int32">268967168</Item>
                  <Item typeName="Int32">268967424</Item>
                  <Item typeName="Int32">268967680</Item>
                  <Item typeName="Int32">268967936</Item>
                  <Item typeName="Int32">268968192</Item>
                  <Item typeName="Int32">268968448</Item>
                  <Item typeName="Int32">268968704</Item>
                  <Item typeName="Int32">268968960</Item>
                  <Item typeName="Int32">268969216</Item>
                  <Item typeName="Int32">268969472</Item>
                  <Item typeName="Int32">268969728</Item>
                  <Item typeName="Int32">268969984</Item>
                  <Item typeName="Int32">268970240</Item>
                  <Item typeName="Int32">268970496</Item>
                  <Item typeName="Int32">268970752</Item>
                  <Item typeName="Int32">268971008</Item>
                  <Item typeName="Int32">268971264</Item>
                  <Item typeName="Int32">268971520</Item>
                  <Item typeName="Int32">268971776</Item>
                  <Item typeName="Int32">268972032</Item>
                  <Item typeName="Int32">268972288</Item>
                  <Item typeName="Int32">268959760</Item>
                  <Item typeName="Int32">268960016</Item>
                  <Item typeName="Int32">268960272</Item>
                  <Item typeName="Int32">268960528</Item>
                  <Item typeName="Int32">268974096</Item>
                  <Item typeName="Int32">268974352</Item>
                  <Item typeName="Int32">277872640</Item>
                  <Item typeName="Int32">277938176</Item>
                  <Item typeName="Int32">271581184</Item>
                  <Item typeName="Int32">280756224</Item>
                  <Item typeName="Int32">282460160</Item>
                  <Item typeName="Int32">282525696</Item>
                  <Item typeName="Int32">282591232</Item>
                  <Item typeName="Int32">168493312</Item>
                  <Item typeName="Int32">168493313</Item>
                  <Item typeName="Int32">168493314</Item>
                  <Item typeName="Int32">168493315</Item>
                  <Item typeName="Int32">168493316</Item>
                  <Item typeName="Int32">168493317</Item>
                  <Item typeName="Int32">168493318</Item>
                  <Item typeName="Int32">168493319</Item>
                  <Item typeName="Int32">168493320</Item>
                  <Item typeName="Int32">168493321</Item>
                  <Item typeName="Int32">168493322</Item>
                  <Item typeName="Int32">168493323</Item>
                  <Item typeName="Int32">168493324</Item>
                  <Item typeName="Int32">168493325</Item>
                  <Item typeName="Int32">168493326</Item>
                  <Item typeName="Int32">168493327</Item>
                  <Item typeName="Int32">168493328</Item>
                  <Item typeName="Int32">168493329</Item>
                  <Item typeName="Int32">168493330</Item>
                  <Item typeName="Int32">168493331</Item>
                  <Item typeName="Int32">168493332</Item>
                  <Item typeName="Int32">168493333</Item>
                  <Item typeName="Int32">168493334</Item>
                  <Item typeName="Int32">168493335</Item>
                  <Item typeName="Int32">168493336</Item>
                  <Item typeName="Int32">168493339</Item>
                  <Item typeName="Int32">168493340</Item>
                  <Item typeName="Int32">168493341</Item>
                  <Item typeName="Int32">168493342</Item>
                  <Item typeName="Int32">168493343</Item>
                  <Item typeName="Int32">168493344</Item>
                  <Item typeName="Int32">168493345</Item>
                  <Item typeName="Int32">168493346</Item>
                  <Item typeName="Int32">168493347</Item>
                  <Item typeName="Int32">168493348</Item>
                  <Item typeName="Int32">168493349</Item>
                  <Item typeName="Int32">168493350</Item>
                  <Item typeName="Int32">168493351</Item>
                  <Item typeName="Int32">168493352</Item>
                  <Item typeName="Int32">168493353</Item>
                  <Item typeName="Int32">168493355</Item>
                  <Item typeName="Int32">168493356</Item>
                  <Item typeName="Int32">168493357</Item>
                  <Item typeName="Int32">168493358</Item>
                  <Item typeName="Int32">168493359</Item>
                  <Item typeName="Int32">168493360</Item>
                  <Item typeName="Int32">168493361</Item>
                  <Item typeName="Int32">168493362</Item>
                  <Item typeName="Int32">168493363</Item>
                  <Item typeName="Int32">168493364</Item>
                  <Item typeName="Int32">168493365</Item>
                  <Item typeName="Int32">168493366</Item>
                  <Item typeName="Int32">168493367</Item>
                  <Item typeName="Int32">168493368</Item>
                  <Item typeName="Int32">168493369</Item>
                  <Item typeName="Int32">168493370</Item>
                  <Item typeName="Int32">168493371</Item>
                  <Item typeName="Int32">168493372</Item>
                  <Item typeName="Int32">168493373</Item>
                  <Item typeName="Int32">168493374</Item>
                  <Item typeName="Int32">168493375</Item>
                  <Item typeName="Int32">168493376</Item>
                  <Item typeName="Int32">168493377</Item>
                  <Item typeName="Int32">168493378</Item>
                  <Item typeName="Int32">168493379</Item>
                  <Item typeName="Int32">168493380</Item>
                  <Item typeName="Int32">168493381</Item>
                  <Item typeName="Int32">168493382</Item>
                  <Item typeName="Int32">168493383</Item>
                  <Item typeName="Int32">168493384</Item>
                  <Item typeName="Int32">168493385</Item>
                  <Item typeName="Int32">168493386</Item>
                  <Item typeName="Int32">168493387</Item>
                  <Item typeName="Int32">168493388</Item>
                  <Item typeName="Int32">168493389</Item>
                  <Item typeName="Int32">168493056</Item>
                  <Item typeName="Int32">168493057</Item>
                  <Item typeName="Int32">168493568</Item>
                  <Item typeName="Int32">168494080</Item>
                  <Item typeName="Int32">168494336</Item>
                  <Item typeName="Int32">168494592</Item>
                  <Item typeName="Int32">1445134611</Item>
                  <Item typeName="Int32">1445134616</Item>
                  <Item typeName="Int32">1445134617</Item>
                  <Item typeName="Int32">1445134618</Item>
                  <Item typeName="Int32">1445134619</Item>
                  <Item typeName="Int32">1445134622</Item>
                  <Item typeName="Int32">1445134643</Item>
                  <Item typeName="Int32">1445134865</Item>
                  <Item typeName="Int32">1445134872</Item>
                  <Item typeName="Int32">1445134875</Item>
                  <Item typeName="Int32">1445134879</Item>
                  <Item typeName="Int32">1445135233</Item>
                  <Item typeName="Int32">1461911808</Item>
                  <Item typeName="Int32">1461911809</Item>
                  <Item typeName="Int32">1461911814</Item>
                  <Item typeName="Int32">1461911823</Item>
                  <Item typeName="Int32">1461911827</Item>
                  <Item typeName="Int32">1461911828</Item>
                  <Item typeName="Int32">1461912065</Item>
                  <Item typeName="Int32">1461912066</Item>
                  <Item typeName="Int32">1461912070</Item>
                  <Item typeName="Int32">1461912449</Item>
                  <Item typeName="Int32">1461912576</Item>
                  <Item typeName="Int32">2147425087</Item>
                  <Item typeName="Int32">2147425088</Item>
                  <Item typeName="Int32">2147425089</Item>
                  <Item typeName="Int32">2147425346</Item>
                  <Item typeName="Int32">2147425347</Item>
                </Item>
                <Item typeName="Boolean" key="EventFilterEnabled">False</Item>
                <Item typeName="IList" key="NeedLoadEventIDs" />
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String">MS</Item>
                  <Item typeName="String" />
                  <Item typeName="String">Name</Item>
                  <Item typeName="String">Protocal</Item>
                  <Item typeName="String">Time</Item>
                  <Item typeName="String">Source</Item>
                  <Item typeName="String">Index</Item>
                </Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">47</Item>
                  <Item typeName="Int32">21</Item>
                  <Item typeName="Int32">284</Item>
                  <Item typeName="Int32">78</Item>
                  <Item typeName="Int32">121</Item>
                  <Item typeName="Int32">521</Item>
                  <Item typeName="Int32">352</Item>
                </Item>
                <Item typeName="IList" key="VisibleColumnIndexs">
                  <Item typeName="Int32">0</Item>
                  <Item typeName="Int32">1</Item>
                  <Item typeName="Int32">2</Item>
                  <Item typeName="Int32">3</Item>
                  <Item typeName="Int32">4</Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
                <Item typeName="Boolean" key="IsMTRForm">False</Item>
              </Item>
            </Item>
          </Item>
          <Item name="ActiveChildFormIndex" typeName="Int32">1</Item>
        </Item>
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">指标列表</Item>
          <Item name="ChildFormConfigs" typeName="IList">
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">TestPointGridForm.dll</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.TestPointGridForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">指标列表</Item>
              <Item name="WindowState" typeName="Int32">2</Item>
              <Item name="LocationX" typeName="Int32">0</Item>
              <Item name="LocationY" typeName="Int32">0</Item>
              <Item name="SizeWidth" typeName="Int32">530</Item>
              <Item name="SizeHeight" typeName="Int32">643</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="IList" key="Columns">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">GSM</Item>
                    <Item typeName="String" key="ParamName">FileName</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">FileName</Item>
                    <Item typeName="Int32" key="Width">205</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">GSM</Item>
                    <Item typeName="String" key="ParamName">Time</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">Time</Item>
                    <Item typeName="Int32" key="Width">155</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">GSM</Item>
                    <Item typeName="String" key="ParamName">Longitude</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">Longitude</Item>
                    <Item typeName="Int32" key="Width">84</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">GSM</Item>
                    <Item typeName="String" key="ParamName">Latitude</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">Latitude</Item>
                    <Item typeName="Int32" key="Width">87</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">GSM</Item>
                    <Item typeName="String" key="ParamName">LAC</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">LAC</Item>
                    <Item typeName="Int32" key="Width">68</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">GSM</Item>
                    <Item typeName="String" key="ParamName">CI</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">CI</Item>
                    <Item typeName="Int32" key="Width">65</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">GSM</Item>
                    <Item typeName="String" key="ParamName">BCCH</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">BCCH</Item>
                    <Item typeName="Int32" key="Width">52</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">GSM</Item>
                    <Item typeName="String" key="ParamName">BSIC</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">BSIC</Item>
                    <Item typeName="Int32" key="Width">57</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">GSM</Item>
                    <Item typeName="String" key="ParamName">RxLevSub</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">RxLevSub</Item>
                    <Item typeName="Int32" key="Width">76</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">GSM</Item>
                    <Item typeName="String" key="ParamName">RxQualSub</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">RxQualSub</Item>
                    <Item typeName="Int32" key="Width">82</Item>
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item name="ActiveChildFormIndex" typeName="Int32">0</Item>
        </Item>
      </Item>
      <Item name="SelectedIndex" typeName="Int32">0</Item>
    </Item>
  </Config>
</Configs>