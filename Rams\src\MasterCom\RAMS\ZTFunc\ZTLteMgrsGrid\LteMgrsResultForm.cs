﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using DevExpress.XtraTab;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsResultForm : MinCloseForm
    {
        private List<LteMgrsFuncItem> funcItems;

        public LteMgrsResultForm()
            : base()
        {
            InitializeComponent();
            xtraTabControl1.SelectedPageChanged += TabControl_SelectedPageChanged;
            cbxCity.SelectedIndexChanged += CbxCity_SelectedChanged;
            MainModel.MainForm.GetMapForm().FireAddLteMgrsAnaLayer();
        }

        public List<LteMgrsFuncItem> BindingFuncs
        {
            get
            {
                return funcItems;
            }
        }

        public List<LteMgrsResultControlBase> BindingControls
        {
            get
            {
                List<LteMgrsResultControlBase> retList = new List<LteMgrsResultControlBase>();
                foreach (XtraTabPage tp in xtraTabControl1.TabPages)
                {
                    retList.Add(tp.Controls[0] as LteMgrsResultControlBase);
                }
                return retList;
            }
        }

        public LteMgrsResultControlBase GetControlByType(Type t)
        {
            foreach (XtraTabPage tp in xtraTabControl1.TabPages)
            {
                if (tp.Controls[0].GetType() == t)
                {
                    return tp.Controls[0] as LteMgrsResultControlBase;
                }
            }
            return null;
        }

        /// <summary>
        /// 不要在非UI线程调用这个函数
        /// </summary>
        /// <param name="t"></param>
        public void SelectedPageByType(Type t)
        {
            foreach (XtraTabPage tp in xtraTabControl1.TabPages)
            {
                if (tp.Controls[0].GetType() == t)
                {
                    if (xtraTabControl1.SelectedTabPage == tp)
                    {
                        TabControl_SelectedPageChanged(xtraTabControl1, new TabPageChangedEventArgs(tp, tp));
                    }
                    else
                    {
                        xtraTabControl1.SelectedTabPage = tp;
                    }
                }
            }
        }

        public void FillData(List<LteMgrsFuncItem> funcItems)
        {
            this.funcItems = funcItems;
            LteMgrsFuncItem func = this.funcItems[0];
            this.cbxCity.Items.Clear();
            foreach (LteMgrsCity city in func.CurQueryCitys)
            {
                this.cbxCity.Items.Add(city.CityName);
            }
            if (cbxCity.SelectedIndex == 0)
            {
                CbxCity_SelectedChanged(cbxCity, new EventArgs());
            }
            else
            {
                cbxCity.SelectedIndex = cbxCity.Items.Count == 0 ? -1 : 0;
            }
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            foreach (LteMgrsFuncItem fItem in funcItems)
            {
                fItem.Clear();
            }

            foreach (XtraTabPage tabPage in xtraTabControl1.TabPages)
            {
                foreach (Control ctrl in tabPage.Controls)
                {
                    (ctrl as LteMgrsResultControlBase).Clear();
                    ctrl.Dispose();
                }
            }
            xtraTabControl1.TabPages.Clear();

            LteMgrsLayer.DrawList = null;
            LteMgrsLayer.LegendGroup = null;
            MainModel.MainForm.GetMapForm().updateMap();
            MainModel.RefreshLegend();

            base.MinCloseForm_FormClosing(sender, e);
        }

        private void TabControl_SelectedPageChanged(object sender, TabPageChangedEventArgs e)
        {
            XtraTabPage curTp = e.Page;
            if (curTp == null || curTp.Controls.Count == 0)
            {
                return;
            }
            LteMgrsResultControlBase ctrl = curTp.Controls[0] as LteMgrsResultControlBase;
            ctrl.DrawOnLayer();
        }

        private void CbxCity_SelectedChanged(object sender, EventArgs e)
        {
            xtraTabControl1.TabPages.Clear();
            foreach (LteMgrsFuncItem item in funcItems)
            {
                item.SelectCityByName(cbxCity.SelectedItem as string);
                item.Stater.DoStat(item);
                List<LteMgrsResultControlBase> controls = item.Stater.GetResult();
                foreach (LteMgrsResultControlBase ctrl in controls)
                {
                    XtraTabPage tabPage = new XtraTabPage();
                    tabPage.Text = ctrl.Desc;
                    tabPage.Controls.Add(ctrl);
                    ctrl.Dock = DockStyle.Fill;
                    xtraTabControl1.TabPages.Add(tabPage);
                }

                if (item.Stater is LteMgrsCoverageRangeStater &&
                    xtraTabControl1.TabPages.Count > 0)
                {
                    xtraTabControl1.SelectedTabPageIndex = xtraTabControl1.TabPages.Count - 1;
                }
            }
            if (xtraTabControl1.SelectedTabPage == null &&
                xtraTabControl1.TabPages.Count > 0)
            {
                xtraTabControl1.SelectedTabPageIndex = 0;
            }
        }
    }
}
