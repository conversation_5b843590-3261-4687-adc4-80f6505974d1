using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using System.Net;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYCellSetByRegion_W : ZTDIYCellSetByRegion
    {
        public ZTDIYCellSetByRegion_W(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14007, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            return true;
        }
    }
}
