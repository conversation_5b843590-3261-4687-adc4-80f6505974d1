﻿namespace MasterCom.RAMS.Frame
{   
    partial class DissectForm
    {
        /// <summary>
        /// 必需的设计器变量。 
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DissectForm));
            this.treeView = new System.Windows.Forms.TreeView();
            this.toolStrip = new System.Windows.Forms.ToolStrip();
            this.tsBtnFixed = new System.Windows.Forms.ToolStripButton();
            this.tsBtnSaveTxt = new System.Windows.Forms.ToolStripButton();
            this.tsBtnHide = new System.Windows.Forms.ToolStripButton();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.tsTextBoxSearch = new System.Windows.Forms.ToolStripTextBox();
            this.toolStripButtonCopy = new System.Windows.Forms.ToolStripButton();
            this.toolStripButtonPre = new System.Windows.Forms.ToolStripButton();
            this.toolStripButtonNext = new System.Windows.Forms.ToolStripButton();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.srcTextBox = new MasterCom.RAMS.Frame.SrcTextBox();
            this.toolStrip.SuspendLayout();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeView
            // 
            this.treeView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeView.Location = new System.Drawing.Point(0, 0);
            this.treeView.Name = "treeView";
            this.treeView.Size = new System.Drawing.Size(499, 401);
            this.treeView.TabIndex = 0;
            this.treeView.MouseClick += new System.Windows.Forms.MouseEventHandler(this.treeView1_MouseUp);
            // 
            // toolStrip
            // 
            this.toolStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsBtnFixed,
            this.tsBtnSaveTxt,
            this.tsBtnHide,
            this.toolStripLabel1,
            this.tsTextBoxSearch,
            this.toolStripButtonCopy,
            this.toolStripButtonPre,
            this.toolStripButtonNext});
            this.toolStrip.Location = new System.Drawing.Point(0, 0);
            this.toolStrip.Name = "toolStrip";
            this.toolStrip.RenderMode = System.Windows.Forms.ToolStripRenderMode.System;
            this.toolStrip.Size = new System.Drawing.Size(499, 25);
            this.toolStrip.TabIndex = 5;
            this.toolStrip.Text = "toolStrip1";
            // 
            // tsBtnFixed
            // 
            this.tsBtnFixed.CheckOnClick = true;
            this.tsBtnFixed.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnFixed.Image = global::MasterCom.RAMS.Properties.Resources.nonFixed;
            this.tsBtnFixed.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnFixed.Name = "tsBtnFixed";
            this.tsBtnFixed.Size = new System.Drawing.Size(23, 22);
            this.tsBtnFixed.Text = "点击冻结内容";
            this.tsBtnFixed.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.tsBtnFixed.Click += new System.EventHandler(this.tsBtnFixed_Click);
            // 
            // tsBtnSaveTxt
            // 
            this.tsBtnSaveTxt.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnSaveTxt.Image = global::MasterCom.RAMS.Properties.Resources.save_as;
            this.tsBtnSaveTxt.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnSaveTxt.Name = "tsBtnSaveTxt";
            this.tsBtnSaveTxt.Size = new System.Drawing.Size(23, 22);
            this.tsBtnSaveTxt.Text = "保存";
            this.tsBtnSaveTxt.ToolTipText = "保存源码";
            this.tsBtnSaveTxt.Click += new System.EventHandler(this.tsBtnSaveTxt_Click);
            // 
            // tsBtnHide
            // 
            this.tsBtnHide.CheckOnClick = true;
            this.tsBtnHide.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.tsBtnHide.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnHide.Image")));
            this.tsBtnHide.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnHide.Name = "tsBtnHide";
            this.tsBtnHide.Size = new System.Drawing.Size(60, 22);
            this.tsBtnHide.Text = "隐藏源码";
            this.tsBtnHide.Click += new System.EventHandler(this.tsBtnHide_Click);
            // 
            // toolStripLabel1
            // 
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(35, 22);
            this.toolStripLabel1.Text = "查找:";
            // 
            // tsTextBoxSearch
            // 
            this.tsTextBoxSearch.Name = "tsTextBoxSearch";
            this.tsTextBoxSearch.Size = new System.Drawing.Size(100, 25);
            // 
            // toolStripButtonCopy
            // 
            this.toolStripButtonCopy.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.toolStripButtonCopy.Image = global::MasterCom.RAMS.Properties.Resources.copy;
            this.toolStripButtonCopy.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButtonCopy.Name = "toolStripButtonCopy";
            this.toolStripButtonCopy.Size = new System.Drawing.Size(23, 22);
            this.toolStripButtonCopy.Text = "复制当前节点文本到搜索框";
            this.toolStripButtonCopy.ToolTipText = "复制文本";
            this.toolStripButtonCopy.Click += new System.EventHandler(this.toolStripButtonCopy_Click);
            // 
            // toolStripButtonPre
            // 
            this.toolStripButtonPre.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.toolStripButtonPre.Image = ((System.Drawing.Image)(resources.GetObject("toolStripButtonPre.Image")));
            this.toolStripButtonPre.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButtonPre.Name = "toolStripButtonPre";
            this.toolStripButtonPre.Size = new System.Drawing.Size(23, 22);
            this.toolStripButtonPre.Text = "toolStripButton3";
            this.toolStripButtonPre.ToolTipText = "查找前一个";
            this.toolStripButtonPre.Click += new System.EventHandler(this.toolStripButtonPre_Click);
            // 
            // toolStripButtonNext
            // 
            this.toolStripButtonNext.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.toolStripButtonNext.Image = ((System.Drawing.Image)(resources.GetObject("toolStripButtonNext.Image")));
            this.toolStripButtonNext.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButtonNext.Name = "toolStripButtonNext";
            this.toolStripButtonNext.Size = new System.Drawing.Size(23, 22);
            this.toolStripButtonNext.Text = "toolStripButton4";
            this.toolStripButtonNext.ToolTipText = "查找下一个";
            this.toolStripButtonNext.Click += new System.EventHandler(this.toolStripButtonNext_Click);
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 25);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.treeView);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.srcTextBox);
            this.splitContainer1.Size = new System.Drawing.Size(499, 473);
            this.splitContainer1.SplitterDistance = 401;
            this.splitContainer1.SplitterWidth = 5;
            this.splitContainer1.TabIndex = 6;
            // 
            // srcTextBox
            // 
            this.srcTextBox.BackColor = System.Drawing.Color.White;
            this.srcTextBox.Dock = System.Windows.Forms.DockStyle.Fill;
            this.srcTextBox.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.srcTextBox.Location = new System.Drawing.Point(0, 0);
            this.srcTextBox.Name = "srcTextBox";
            this.srcTextBox.ReadOnly = true;
            this.srcTextBox.Size = new System.Drawing.Size(499, 67);
            this.srcTextBox.TabIndex = 2;
            this.srcTextBox.Text = "";
            // 
            // DissectForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(499, 498);
            this.Controls.Add(this.splitContainer1);
            this.Controls.Add(this.toolStrip);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "DissectForm";
            this.Text = "详细解码";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.DissectForm_FormClosing);
            this.SizeChanged += new System.EventHandler(this.Form1_SizeChanged);
            this.toolStrip.ResumeLayout(false);
            this.toolStrip.PerformLayout();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.TreeView treeView;
        private SrcTextBox srcTextBox;
        private System.Windows.Forms.ToolStrip toolStrip;
        private System.Windows.Forms.ToolStripButton tsBtnSaveTxt;
        private System.Windows.Forms.ToolStripButton tsBtnHide;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
        private System.Windows.Forms.ToolStripTextBox tsTextBoxSearch;
        private System.Windows.Forms.ToolStripButton toolStripButtonPre;
        private System.Windows.Forms.ToolStripButton toolStripButtonNext;
        private System.Windows.Forms.ToolStripButton toolStripButtonCopy;
        private System.Windows.Forms.ToolStripButton tsBtnFixed;
    }
}

