﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class AlarmRecordLayerPropertis : MTLayerPropUserControl
    {
        public AlarmRecordLayerPropertis()
        {
            InitializeComponent();

            numWidth.ValueChanged += NumWidth_ValueChanged;
            numHight.ValueChanged += NumHight_ValueChanged;
            numNbCount.ValueChanged += NumNbCount_ValueChanged;
            btnReload.Click += BtnReload_Click;
        }

        private AlarmRecordLayer layer = null;
        public override void Setup(object obj)
        {
            layer = obj as AlarmRecordLayer;
            if (layer == null)
            {
                return;
            }
            Text = "告警设置";

            numWidth.ValueChanged -= NumWidth_ValueChanged;
            numHight.ValueChanged -= NumHight_ValueChanged;
            numNbCount.ValueChanged -= NumNbCount_ValueChanged;

            numHight.Value = layer.ImgSize.Height;
            numWidth.Value = layer.ImgSize.Width;
            numNbCount.Value = layer.NbCellCount;

            numWidth.ValueChanged += NumWidth_ValueChanged;
            numHight.ValueChanged += NumHight_ValueChanged;
            numNbCount.ValueChanged += NumNbCount_ValueChanged;
        }

        private void NumWidth_ValueChanged(object sender, EventArgs e)
        {
            Size size = new Size((int)numWidth.Value, layer.ImgSize.Height);
            layer.ImgSize = size;
        }

        private void NumHight_ValueChanged(object sender, EventArgs e)
        {
            Size size = new Size(layer.ImgSize.Width, (int)numHight.Value);
            layer.ImgSize = size;
        }

        private void NumNbCount_ValueChanged(object sender, EventArgs e)
        {
            layer.NbCellCount = (int)numNbCount.Value;
        }

        private void BtnReload_Click(object sender, EventArgs e)
        {
            layer.ReloadAlarmRecords();
        }
    }
}
