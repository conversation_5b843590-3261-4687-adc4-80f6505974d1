﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryDeviceManageInfo : DIYSQLBase
    {
        private Dictionary<string, DeviceManageInfo> deviceInfoDic = null;
        public Dictionary<string, DeviceManageInfo> DeviceInfoDic
        {
            get { return deviceInfoDic; }
        }

        public List<DeviceManageInfo> DeviceInfoList
        {
            get { return new List<DeviceManageInfo>(deviceInfoDic.Values); }
        }

        public DiyQueryDeviceManageInfo()
            : base()
        {
            MainDB = true;
        }

        public override string Name
        {
            get
            {
                return "查询设备信息表";
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.Append("SELECT [BoxID],[Vendor],[SoftwareVersion],[CurVersion],[Area],[Operation],[Status],[DeviceStatus] FROM tb_testing_device_manage");
            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[8];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            initData();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    dealReceiveData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        protected virtual void initData()
        {
            deviceInfoDic = new Dictionary<string, DeviceManageInfo>();
        }

        protected virtual void dealReceiveData(Package package)
        {
            DeviceManageInfo deviceInfo = new DeviceManageInfo();
            deviceInfo.FillDataBySQL(package);
            deviceInfoDic[deviceInfo.BoxID] = deviceInfo;
        }
    }

    public class DeviceManageInfo
    {
        public DeviceManageInfo()
        {
            DeviceStatus = "正常";
        }

        private string boxID = "";
        /// <summary>
        /// BOX_ID
        /// </summary>
        public string BoxID
        {
            get { return boxID; }
            private set { boxID = value; }
        }
        private string vendor = "";
        /// <summary>
        /// 厂商
        /// </summary>
        public string Vendor
        {
            get { return vendor; }
            private set { vendor = value; }
        }
        private string softwareVersion = "";
        /// <summary>
        /// 软件版本
        /// </summary>
        public string SoftwareVersion
        {
            get { return softwareVersion; }
            private set { softwareVersion = value; }
        }
        private string curVersion = "";
        /// <summary>
        /// 下发版本
        /// </summary>
        public string CurVersion
        {
            get { return curVersion; }
            private set { curVersion = value; }
        }
        private string area = "";
        /// <summary>
        /// 所属域
        /// </summary>
        public string Area
        {
            get { return area; }
            private set { area = value; }
        }
        private string operation = "";
        /// <summary>
        /// 操作
        /// </summary>
        public string Operation
        {
            get { return operation; }
            private set { operation = value; }
        }
        private string status = "";
        /// <summary>
        /// 状态
        /// </summary>
        public string Status
        {
            get { return status; }
            private set { status = value; }
        }
        /// <summary>
        /// 设备状态 - 设备短缺、设备维修、设备报废、设备借调。（初始状态为“正常”）
        /// </summary>
        public string DeviceStatus { get; set; }

        public void FillDataBySQL(Package package)
        {
            boxID = package.Content.GetParamString();
            vendor = package.Content.GetParamString();
            softwareVersion = package.Content.GetParamString();
            curVersion = package.Content.GetParamString();
            area = package.Content.GetParamString();
            operation = package.Content.GetParamString();
            status = package.Content.GetParamString();
            DeviceStatus = package.Content.GetParamString();
        }

        public void FillDataByExcel(DataRow dr)
        {
            boxID = dr["BOX_ID"].ToString().PadLeft(8,'0');
            vendor = dr["厂商"].ToString();
            softwareVersion = dr["软件版本"].ToString();
            curVersion = dr["下发版本"].ToString();
            area = dr["所属域"].ToString();
            operation = dr["操作"].ToString();
            status = dr["状态"].ToString();
        }
    }
}
