﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Frame
{
    public partial class VerifyLockedLoginBox : BaseDialog
    {
        public VerifyLockedLoginBox()
        {
            InitializeComponent();
        }

        public string UserName
        {
            get { return this.textBoxUserName.Text; }
            set { this.textBoxUserName.Text = value; }
        }

        public string Password
        {
            get { return this.textBoxPassword.Text; }
            set { this.textBoxPassword.Text = value; }
        }
    }
}
