﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc.ZTFileCompare;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYFileCompare : DIYSQLBase
    {
        //默认弹出时间选择
        public bool ShowTimePicker { get; set; } = false;
        public bool bShowResult { get; set; } = true;
        public ZTDIYFileCompare(MainModel mm)
            : base(mm)
        {
            MainDB = true;
            //每次查询前，重置时间，结束时间取当前时间的前一天
            DtEnd = DateTime.Now.Date.AddMilliseconds(-1);//登录系统时间的前一天
            DtBegin = DtEnd.AddDays(-8).AddMilliseconds(1);
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18027, this.Name);
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public DateTime DtBegin { get; set; }
        public DateTime DtEnd { get; set; }
        public Dictionary<string, Dictionary<string, Dictionary<string, CityFileCompare>>> dateFileDic { get; set; }
        private List<FileState> files = null;
        private int notExistCount = 0;
        public List<TestPlan_Beijing> Plans { get; set; }
        protected override void query()
        {
            dateFileDic = new Dictionary<string, Dictionary<string, Dictionary<string, CityFileCompare>>>();
            files = new List<FileState>();
            Plans = null;
            notExistCount = 0;
            if (ShowTimePicker)
            {
                ZTFileCompareDatetimeOptionDlg dlg = new ZTFileCompareDatetimeOptionDlg(DtBegin, DtEnd);
                if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                {
                    return;
                }
                DateTime dtBegin;
                DateTime dtEnd;
                dlg.GetSettingTime(out dtBegin, out dtEnd);
                DtBegin = dtBegin;
                DtEnd = dtEnd;
            }
            base.query();

#if TestPlanFileCompare
            ZTDIYTestPlanQuery testPlanQuery = new ZTDIYTestPlanQuery(this.dtBegin, this.dtEnd);
            testPlanQuery.Query();
            Plans = testPlanQuery.Plans;
            if (Plans != null)
            {
                foreach (FileState fi in files)
                {
                    foreach (TestPlan_Beijing p in Plans)
                    {
                        if (p.JudgeFile(fi))
                        {
                            break;
                        }
                    }
                }
            }
#endif
            if (bShowResult)
                showResult();
        }

        protected override string getSqlTextString()
        {
            return @"exec sp_atu_file_compare '" + DtBegin.ToString("yyyy-MM-dd HH:mm:ss") + "','" + DtEnd.ToString("yyyy-MM-dd HH:mm:ss") + "'";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[6];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_Int;
            return rType;
        }

        public override string Name
        {
            get {return "文件对比"; }
        }

       
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            FileState fi = null;
            CityFileCompare dateFileInfo = CityFileCompare.FillFrom(package.Content, out fi);
            files.Add(fi);
            if (!fi.IsExist)
            {
                notExistCount++;
            }
            if (!dateFileDic.ContainsKey(dateFileInfo.DateString))
            {
                dateFileDic[dateFileInfo.DateString] = new Dictionary<string, Dictionary<string, CityFileCompare>>();
            }
            if (!dateFileDic[dateFileInfo.DateString].ContainsKey(dateFileInfo.City))
            {
                dateFileDic[dateFileInfo.DateString][dateFileInfo.City] = new Dictionary<string, CityFileCompare>();
            }
            if (!dateFileDic[dateFileInfo.DateString][dateFileInfo.City].ContainsKey(dateFileInfo.NetType))
            {
                dateFileDic[dateFileInfo.DateString][dateFileInfo.City].Add(dateFileInfo.NetType, dateFileInfo);
            }
            else
            {
                dateFileDic[dateFileInfo.DateString][dateFileInfo.City][dateFileInfo.NetType].Combine(dateFileInfo);
            }
        }

        protected void showResult()
        {
            CityFileStateForm form = MainModel.GetInstance().GetObjectFromBlackboard(typeof(CityFileStateForm)) as CityFileStateForm;
            if (form == null || form.IsDisposed)
            {
                form = new CityFileStateForm(MainModel);
            }
            form.SetTime(DtBegin, DtEnd, files.Count, notExistCount);
            form.FillData(dateFileDic, Plans);
            form.Owner = MainModel.MainForm;
            form.Visible = true;
            form.BringToFront();
            dateFileDic = null;
            files = null;
            Plans = null;
        }
        
    }

}
