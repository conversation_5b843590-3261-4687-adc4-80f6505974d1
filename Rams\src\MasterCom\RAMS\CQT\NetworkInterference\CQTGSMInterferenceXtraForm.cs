﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTGSMInterferenceXtraForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTGSMInterferenceXtraForm(MainModel Mainmodel)
        {
            InitializeComponent();
            mainmodel = Mainmodel;
        }
        MainModel mainmodel = null;
        Dictionary<string, Dictionary<string, int>> cqtCellItemDic = 
            new Dictionary<string, Dictionary<string, int>>();
        Dictionary<string, int> cqtNum = new Dictionary<string, int>();
        List<CQTC2IItemNew> cqtC2IItemNewListAll = new List<CQTC2IItemNew>();
        public void setData(List<CQTC2IItemNew> cqtC2IItemNewList)
        {
            cqtC2IItemNewListAll.Clear();           
            cqtC2IItemNewListAll = cqtC2IItemNewList;
            this.gridControl1.DataSource = cqtC2IItemNewList;
            dealData(cqtC2IItemNewList);           
        }
        private void dealData(List<CQTC2IItemNew> cqtC2IItemNewList)
        {
            cqtCellItemDic.Clear();
            cqtNum.Clear();
            foreach (CQTC2IItemNew cqtTem in cqtC2IItemNewList)
            {
                if (!cqtCellItemDic.ContainsKey(cqtTem.Strcqtname))
                {
                    Dictionary<string, int> cellItemDic = new Dictionary<string, int>();
                    cellItemDic.Add(cqtTem.Strcellname, cqtTem.cqtCellInfo.Count);
                    cqtCellItemDic.Add(cqtTem.Strcqtname,cellItemDic);
                }
                else
                {
                    if (!cqtCellItemDic[cqtTem.Strcqtname].ContainsKey(cqtTem.Strcellname))
                    {
                        cqtCellItemDic[cqtTem.Strcqtname].Add(cqtTem.Strcellname, cqtTem.cqtCellInfo.Count);
                    }
                    else
                    {
                        cqtCellItemDic[cqtTem.Strcqtname][cqtTem.Strcellname] += cqtTem.cqtCellInfo.Count;
                    }
                }
                if (!cqtNum.ContainsKey(cqtTem.Strcqtname))
                {
                    int numTem = cqtTem.cqtCellInfo.Count;
                    cqtNum.Add(cqtTem.Strcqtname, numTem);
                }
                else
                {
                    cqtNum[cqtTem.Strcqtname] += cqtTem.cqtCellInfo.Count;
                }
            }
        }

        //点击查看详情
        private void gridView1_Click(object sender, EventArgs e)
        {
            int[] rows = gridView1.GetSelectedRows();
            if (rows.Length == 0)
                return;
            object o = gridView1.GetRow(rows[0]);
            CQTC2IItemNew evaluate = o as CQTC2IItemNew;

            if (evaluate != null)
            {
                this.gridControl2.DataSource = evaluate.cqtCellInfo;
            }
        }
        private void ExportExcel()
        {
            Microsoft.Office.Interop.Excel.Application app = null;
            try
            {
                app = new Microsoft.Office.Interop.Excel.Application();
                if (app == null)
                {
                    throw (new Exception("ERROR: EXCEL couldn't be started!"));
                }
                app.Visible = false;
                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                Sheets sheets = workbook.Worksheets;
                _Worksheet worksheet = (_Worksheet)sheets.get_Item(1);
                if (worksheet == null)
                {
                    throw (new Exception("ERROR: worksheet == null"));
                }
                worksheet.Name = "GSM覆盖入侵表";
                //==== 列标题
                int idx = 1;
                makeTitle(worksheet, 1, idx++, "CQT地点名称", 12, false);
                makeTitle(worksheet, 1, idx++, "小区名称", 12, false);
                makeTitle(worksheet, 1, idx++, "LAC", 7, false);
                makeTitle(worksheet, 1, idx++, "CI", 7, false);
                makeTitle(worksheet, 1, idx++, "BCCH", 6, false);
                makeTitle(worksheet, 1, idx++, "BSIC", 6, false);
                makeTitle(worksheet, 1, idx++, "采样点总数", 10, false);
                makeTitle(worksheet, 1, idx++, "C/I最大值", 10, false);
                makeTitle(worksheet, 1, idx++, "C/I最小值", 10, false);
                makeTitle(worksheet, 1, idx++, "C/I平均值", 10, false);
                makeTitle(worksheet, 1, idx++, "采样点异常比", 15, false);
                makeTitle(worksheet, 1, idx++, "文件名", 15, false);
                makeTitle(worksheet, 1, idx++, "采样点总数", 10, false);
                makeTitle(worksheet, 1, idx++, "C/I最大值", 10, false);
                makeTitle(worksheet, 1, idx++, "C/I最小值", 10, false);
                makeTitle(worksheet, 1, idx, "C/I平均值", 10, false);


                int rowsId = 2;
                foreach (string cqtN in cqtNum.Keys)
                {
                    worksheet.get_Range(worksheet.Cells[rowsId, 1], worksheet.Cells[rowsId + cqtNum[cqtN] - 1, 1]).MergeCells = true;
                    Range firstGroupRge = worksheet.Cells[rowsId, 1] as Range;
                    firstGroupRge.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    firstGroupRge.Value2 = cqtN;
                    rowsId = rowsId + cqtNum[cqtN];
                }
                
                int ivalue = 0;
                for (int col = 2; col < 12; col++)
                {
                    rowsId = 2;
                    ivalue = 0;
                    foreach (string cqtN in cqtNum.Keys)
                    {
                        foreach (string cellN in cqtCellItemDic[cqtN].Keys)
                        {
                            setRangeValue(worksheet, ref rowsId, ref ivalue, col, cqtN, cellN);
                        }
                    }
                }
                int rowAt = 2;
                foreach (CQTC2IItemNew ts in cqtC2IItemNewListAll)
                {
                    foreach (underCqtC2IItem ws in ts.cqtCellInfo)
                    {
                        int xx = 12;
                        makeItemRow(worksheet, rowAt, xx++, ws.Strufilename);
                        makeItemRow(worksheet, rowAt, xx++, ws.IuNumC2I.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ws.IuMaxC2I.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ws.IuMinC2I.ToString());
                        makeItemRow(worksheet, rowAt, xx, ws.IuMeanC2I.ToString());
                        rowAt++;
                    }
                }
                app.Visible = true;
                app.UserControl = true;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(this, "导出 Excel 出错：" + ex.Message);
            }
        }

        private void setRangeValue(_Worksheet worksheet, ref int rowsId, ref int ivalue, int col, string cqtN, string cellN)
        {
            worksheet.get_Range(worksheet.Cells[rowsId, col], worksheet.Cells[rowsId + cqtCellItemDic[cqtN][cellN] - 1, col]).MergeCells = true;
            Range secondGroupRge = worksheet.Cells[rowsId, col] as Range;
            secondGroupRge.HorizontalAlignment = XlHAlign.xlHAlignCenter;
            if (col == 2)
                secondGroupRge.Value2 = cqtC2IItemNewListAll[ivalue].Strcellname;
            else if (col == 3)
                secondGroupRge.Value2 = cqtC2IItemNewListAll[ivalue].Ilaic;
            else if (col == 4)
                secondGroupRge.Value2 = cqtC2IItemNewListAll[ivalue].Ici;
            else if (col == 5)
                secondGroupRge.Value2 = cqtC2IItemNewListAll[ivalue].Ibcch;
            else if (col == 6)
                secondGroupRge.Value2 = cqtC2IItemNewListAll[ivalue].Ibsic;
            else if (col == 7)
                secondGroupRge.Value2 = cqtC2IItemNewListAll[ivalue].INumC2I;
            else if (col == 8)
                secondGroupRge.Value2 = cqtC2IItemNewListAll[ivalue].IMaxC2I;
            else if (col == 9)
                secondGroupRge.Value2 = cqtC2IItemNewListAll[ivalue].IMinC2I;
            else if (col == 10)
                secondGroupRge.Value2 = cqtC2IItemNewListAll[ivalue].IMeanC2I;
            else if (col == 11)
                secondGroupRge.Value2 = cqtC2IItemNewListAll[ivalue].StrAbnormalRate;
            rowsId = rowsId + cqtCellItemDic[cqtN][cellN];
            ivalue++;
        }

        public void makeTitle(_Worksheet worksheet, int row, int col, string title, int width, bool wraptext)
        {
            Range range = worksheet.Cells[row, col] as Range;
            range.Value2 = title;
            //  range.Font.Bold = true;
            range.ColumnWidth = width;
            range.WrapText = wraptext;
        }

        public void makeItemRow(_Worksheet worksheet, int row, int column, string str)
        {
            Range range = worksheet.Cells[row, column] as Range;
            range.Value2 = str;
        }

        private void EXCELToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExportExcel();
        }
        //回放采样点
        private void replayToolStripMenuItem_Click(object sender, EventArgs e)
        {
            int[] row = gridView2.GetSelectedRows();
            if (row.Length == 0)
                return;
            object o = gridView2.GetRow(row[0]);
            underCqtC2IItem replayFile = o as underCqtC2IItem;
            FileInfo fileInfo = new FileInfo();
            fileInfo.ID = replayFile.ReplayFile.ID;
            fileInfo.ProjectID = replayFile.ReplayFile.ProjectID;
            fileInfo.ServiceType = replayFile.ReplayFile.ServiceType;
            fileInfo.SampleTbName = replayFile.ReplayFile.SampleTbName;
            fileInfo.LogTable = replayFile.ReplayFile.LogTable;

            MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(fileInfo, replayFile.Dtime);

        }

    }

}