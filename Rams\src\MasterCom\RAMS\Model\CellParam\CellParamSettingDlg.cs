﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public partial class CellParamSettingDlg : BaseForm
    {
        public CellParamSettingDlg(List<CellParamColumn> paramCols)
        {
            InitializeComponent();
            tempSelCols.Clear();
            tempSelCols.AddRange(paramCols);
            fillView();
            updateViewCheckState(null);
        }

        private List<CellParamColumn> tempSelCols = new List<CellParamColumn>();
        public List<CellParamColumn> DisplayCellParamCols
        {
            get { return tempSelCols; }
        }
        private void fillView()
        {
            fillParamView();
            fillCustomView();
        }

        private void updateViewCheckState(object sender)
        {
            updateCustomView(sender);
            updateParamView(sender);
            updateSelView(sender);
        }
        private void updateCustomView(object sender)
        {
            if (sender == treeListCustom)
            {
                return;
            }
            setTreeCheckState(treeListCustom);
        }
        private void updateParamView(object sender)
        {
            if (sender == treeListParams)
            {
                return;
            }
            setTreeCheckState(treeListParams);
        }
        private void updateSelView(object sender)
        {
            if (sender == lvColSelected)
            {
                return;
            }
            fillSelView();
        }

        private void setTreeCheckState(TreeList tree)
        {
            foreach (TreeListNode root in tree.Nodes)
            {
                int checkedCnt = 0;
                foreach (TreeListNode node in root.Nodes)
                {
                    CellParamColumn col = node.Tag as CellParamColumn;
                    if (col == null)
                    {
                        continue;
                    }
                    if (tempSelCols.Contains(col))
                    {
                        node.Checked = true;
                        checkedCnt++;
                    }
                    else
                    {
                        node.Checked = false;
                    }
                }
                setCheckState(root, checkedCnt);
            }
        }

        private void setCheckState(TreeListNode root, int checkedCnt)
        {
            if (checkedCnt != 0)
            {
                if (root.Nodes.Count == checkedCnt)
                {
                    root.CheckState = CheckState.Checked;
                }
                else
                {
                    root.CheckState = CheckState.Indeterminate;
                }
            }
            else
            {
                root.CheckState = CheckState.Unchecked;
            }
        }

        private void fillCustomView()
        {
            treeListCustom.BeforeCheckNode -= treeListBeforeCheck;
            treeListCustom.AfterCheckNode -= treeListAfterCheckNode;
            treeListCustom.Nodes.Clear();
            Dictionary<string, List<CellParamColumn>> grpDic = CellParamCfgManager.GetInstance().CustomGrpColDic;
            foreach (KeyValuePair<string, List<CellParamColumn>> kvp in grpDic)
            {
                TreeListNode tableNode = treeListCustom.AppendNode(new object[] { kvp.Key }, null);
                foreach (CellParamColumn col in kvp.Value)
                {
                    TreeListNode subNode = treeListCustom.AppendNode(new object[] { col.Name, col.Description }, tableNode);
                    subNode.Tag = col;
                }
            }
            treeListCustom.ExpandAll();
            treeListCustom.BeforeCheckNode += treeListBeforeCheck;
            treeListCustom.AfterCheckNode += treeListAfterCheckNode;
        }

        private void fillSelView()
        {
            lvColSelected.Items.Clear();
            foreach (CellParamColumn col in tempSelCols)
            {
                ListViewItem item = new ListViewItem();
                item.Text = col.Name;
                item.Tag = col;
                lvColSelected.Items.Add(item);
            }
        }

        private void fillParamView()
        {
            treeListParams.BeforeCheckNode -= treeListBeforeCheck;
            treeListParams.AfterCheckNode -= treeListAfterCheckNode;
            treeListParams.Nodes.Clear();
            treeListParams.BeginUnboundLoad();
            List<CellParamDataBase> dbs = CellParamCfgManager.GetInstance().ParamDataBaseList;
            foreach (CellParamDataBase db in dbs)
            {
                foreach (CellParamTable tb in db.Tables)
                {
                    TreeListNode tableNode = treeListParams.AppendNode(new object[] { tb.Name, tb.Alias, tb.Description }, null);
                    tableNode.Tag = tb;
                    tableNode.HasChildren = tb.Columns.Count > 0;
                    foreach (CellParamColumn col in tb.Columns)
                    {
                        TreeListNode subNode = treeListParams.AppendNode(new object[] { col.Name, col.Alias, col.Description }, tableNode);
                        subNode.Tag = col;
                        subNode.HasChildren = false;
                    }
                }
            }
            treeListParams.EndUnboundLoad();
            treeListParams.Refresh();
            treeListParams.BeforeCheckNode += treeListBeforeCheck;
            treeListParams.AfterCheckNode += treeListAfterCheckNode;
        }

        void treeListBeforeCheck(object sender, DevExpress.XtraTreeList.CheckNodeEventArgs e)
        {
            e.State = (e.PrevState == CheckState.Checked ? CheckState.Unchecked : CheckState.Checked);
        }

        void treeListAfterCheckNode(object sender, DevExpress.XtraTreeList.NodeEventArgs e)
        {
            setCheckedChildNodes(e.Node, e.Node.CheckState);
            setCheckedParentNodes(e.Node, e.Node.CheckState);
            if (e.Node.Nodes.Count > 0)
            {
                foreach (TreeListNode colNode in e.Node.Nodes)
                {
                    CellParamColumn col = colNode.Tag as CellParamColumn;
                    if (col != null)
                    {
                        dealColumn(e, col);
                    }
                }
            }
            else if (e.Node.Tag is CellParamColumn)
            {
                CellParamColumn col = e.Node.Tag as CellParamColumn;
                dealColumn(e, col);
            }
            updateViewCheckState(sender);
        }

        private void dealColumn(NodeEventArgs e, CellParamColumn col)
        {
            if (e.Node.Checked)
            {
                if (!tempSelCols.Contains(col))
                {
                    tempSelCols.Add(col);
                }
            }
            else
            {
                tempSelCols.Remove(col);
            }
        }

        private void setCheckedChildNodes(TreeListNode node, CheckState check)
        {
            for (int i = 0; i < node.Nodes.Count; i++)
            {
                node.Nodes[i].CheckState = check;
                setCheckedChildNodes(node.Nodes[i], check);
            }
        }
        private void setCheckedParentNodes(TreeListNode node, CheckState check)
        {
            if (node.ParentNode != null)
            {
                bool b = false;
                CheckState state;
                for (int i = 0; i < node.ParentNode.Nodes.Count; i++)
                {
                    state = node.ParentNode.Nodes[i].CheckState;
                    if (!check.Equals(state))
                    {
                        b = !b;
                        break;
                    }
                }
                node.ParentNode.CheckState = b ? CheckState.Indeterminate : check;
                setCheckedParentNodes(node.ParentNode, check);
            }
        }

        private void miAddTable_Click(object sender, EventArgs e)
        {
            AddCellParamTableDlg dlg = new AddCellParamTableDlg(tempSelCols);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                fillParamView();
                updateCustomView(null);
            }
        }

        private void buttonColumnUp_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < lvColSelected.SelectedIndices.Count; i++)
            {
                int index = lvColSelected.SelectedIndices[i];
                ListViewItem item = lvColSelected.Items[index];
                lvColSelected.Items.RemoveAt(index);
                lvColSelected.Items.Insert(index - 1, item);
            }
            lvColSelected.Focus();
            checkColumnButtonState();
        }
        private void buttonColumnDown_Click(object sender, EventArgs e)
        {
            for (int i = lvColSelected.SelectedIndices.Count - 1; i >= 0; i--)
            {
                int index = lvColSelected.SelectedIndices[i];
                ListViewItem item = lvColSelected.Items[index];
                lvColSelected.Items.RemoveAt(index);
                lvColSelected.Items.Insert(index + 1, item);
            }
            lvColSelected.Focus();
            checkColumnButtonState();
        }
        private void checkColumnButtonState()
        {
            buttonColumnUp.Enabled = lvColSelected.SelectedIndices.Count > 0 && lvColSelected.SelectedIndices[0] > 0;
            buttonColumnDown.Enabled = lvColSelected.SelectedIndices.Count > 0 && lvColSelected.SelectedIndices[lvColSelected.SelectedIndices.Count - 1] < lvColSelected.Items.Count - 1;
        }

        private void lvColSelected_Click(object sender, EventArgs e)
        {
            checkColumnButtonState();
        }

        private void miRemoveTables_Click(object sender, EventArgs e)
        {
            //
        }

        private void miRemoveCols_Click(object sender, EventArgs e)
        {
            //
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            CellParamCfgManager.GetInstance().Save();
        }

        private void treeListParams_CellValueChanged(object sender, CellValueChangedEventArgs e)
        {
            CellParamTable tb = e.Node.Tag as CellParamTable;
            CellParamColumn col = e.Node.Tag as CellParamColumn;
            if (e.Column.FieldName == "Alias")
            {
                if (tb!=null)
                {
                    tb.Alias = e.Value.ToString();
                }
                else if (col!=null)
                {
                    col.Alias = e.Value.ToString();
                }
            }
            else if (e.Column.FieldName == "Description")
            {
                if (tb != null)
                {
                    tb.Description = e.Value.ToString();
                }
                else if (col != null)
                {
                    col.Description = e.Value.ToString();
                }
            }
          
        }

        private void miAddGrp_Click(object sender, EventArgs e)
        {
            TextInputBox box = new TextInputBox("新建自定义指标组", "组名", "未命名");
            if (box.ShowDialog() == DialogResult.OK)
            {
                List<CellParamColumn> cols = new List<CellParamColumn>(tempSelCols);
                CellParamCfgManager.GetInstance().AddCustomGrp(box.TextInput, cols);
                fillCustomView();
                updateViewCheckState(sender);
            }
        }

        private void miRemoveGrp_Click(object sender, EventArgs e)
        {
            foreach (TreeListNode grpNode in treeListCustom.Nodes)
            {
                if (grpNode.Checked)
                {
                    CellParamCfgManager.GetInstance().CustomGrpColDic.Remove(grpNode.GetDisplayText(colRoleName));
                }
            }
            fillCustomView();
            updateViewCheckState(sender);
        }

        private void cmsCustom_Opening(object sender, CancelEventArgs e)
        {
            miAddGrp.Visible = tempSelCols.Count > 0;
        }
    }
}
