﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class NRBTSInfoForm : BaseForm
    {
        private NRBTS bts = null;
        public NRBTSInfoForm(MainModel mm,NRBTS bts):base(mm)
        {
            InitializeComponent();
            if (bts!=null)
            {
                this.bts = bts;
                fillBTSInfo(bts);
            }
        }

        private void fillBTSInfo(NRBTS bts)
        {
            txtName.Text = bts.Name;
            txtType.Text = bts.TypeStringDesc;
            txtLng.Text = bts.Longitude.ToString("F7");
            txtLat.Text = bts.Latitude.ToString("F7");
            txtBTSDesc.Text = bts.Description;
            txtBtsId.Text = bts.BTSID.ToString();
            List<NRCell> cells = new List<NRCell>();
            foreach (NRCell cell in bts.Cells)
            {
                if (MapNRCellLayer.DrawCurrent)
                {
                    if (cell.ValidPeriod.Contains(DateTime.Now.Date))
                    {
                        cells.Add(cell);
                    }
                }
                else
                {
                    if (cell.ValidPeriod.Contains(MapNRCellLayer.CurShowSnapshotTime))
                    {
                        cells.Add(cell);
                    }
                }
            }
            gridCtrlNR.DataSource = cells;
            gridCtrlNR.RefreshDataSource();
        }

        private void gridViewNR_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            NRCell cell = gridViewNR.GetRow(gridViewNR.FocusedRowHandle) as NRCell;
            fillCellInfo(cell);
        }

        private void fillCellInfo(NRCell cell)
        {
            if (cell == null)
            {
                txtCellName.Text = "";
                txtID.Text = "";
                txtCode.Text = "";
                txtTAC.Text = "";
                txtECI.Text = "";
                txtPCI.Text = "";
                txtEarfcn.Text = "";
                txtDirection.Text = "";
                txtSectorID.Text = "";
                txtDesc.Text = "";
            }
            else
            {
                txtCellName.Text = cell.Name;
                txtID.Text = cell.ID.ToString();
                txtCode.Text = cell.Code;
                txtTAC.Text = cell.TAC.ToString();
                txtECI.Text = cell.NCI.ToString();
                txtPCI.Text = cell.PCI.ToString();
                txtEarfcn.Text = cell.SSBARFCN.ToString();
                txtDirection.Text = cell.Direction.ToString();
                txtSectorID.Text = cell.SectorID.ToString();
                txtDesc.Text = cell.DESC;
            }
        }

        private void gridViewNR_DoubleClick(object sender, EventArgs e)
        {
            locatCell();
        }

        private void btnCellLocation_Click(object sender, EventArgs e)
        {
            locatCell();
        }

        private void locatCell()
        {
            NRCell cell = gridViewNR.GetRow(gridViewNR.FocusedRowHandle) as NRCell;
            if (cell != null)
            {
                MainModel.SetSelectedNRCell(cell);
                MainModel.FireSelectedCellChanged(this);
            }
        }
    }
}
