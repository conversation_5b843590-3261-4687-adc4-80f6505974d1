﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    public class GridOrderToken
    {
        public string Name { get; set; }
        public int ID { get; set; }
        public string Token { get; set; }

        public string OrderTbName
        {
            get { return string.Format("tb_grid_{0}set", Token); }
        }

        public string OrderGridTbName
        {
            get { return string.Format("tb_grid_{0}set_item", Token); }
        }

        public string OrderGridKPITbName
        {
            get { return string.Format("tb_grid_{0}set_item_kpi", Token); }
        }

        public string OrderGridCellTbName
        {
            get { return string.Format("tb_grid_{0}set_cell", Token); }
        }

        public string OrderGridCellKPITbName
        {
            get { return string.Format("tb_grid_{0}set_cell_kpi", Token); }
        }
    }
}
