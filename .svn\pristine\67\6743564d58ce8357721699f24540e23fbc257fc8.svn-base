﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class EleRadioAndLACCI
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.gridControlRes = new DevExpress.XtraGrid.GridControl();
            this.gridViewForCellRadio = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRadio = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewForCellRadio)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControlRes
            // 
            this.gridControlRes.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRes.Location = new System.Drawing.Point(0, 0);
            this.gridControlRes.MainView = this.gridViewForCellRadio;
            this.gridControlRes.Name = "gridControlRes";
            this.gridControlRes.Size = new System.Drawing.Size(268, 385);
            this.gridControlRes.TabIndex = 2;
            this.gridControlRes.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewForCellRadio});
            //this.gridControlRes.Click += new System.EventHandler(this.gridControlRes_Click);
            // 
            // gridViewForCellRadio
            // 
            this.gridViewForCellRadio.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnLAC,
            this.gridColumnCI,
            this.gridColumnRadio});
            this.gridViewForCellRadio.GridControl = this.gridControlRes;
            this.gridViewForCellRadio.Name = "gridViewForCellRadio";
            this.gridViewForCellRadio.OptionsBehavior.Editable = false;
            this.gridViewForCellRadio.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewForCellRadio.OptionsView.ColumnAutoWidth = false;
            this.gridViewForCellRadio.OptionsView.ShowGroupPanel = false;
            this.gridViewForCellRadio.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridViewForCellRadio_FocusedRowChanged);
            //this.gridViewForCellRadio.MouseUp += new System.Windows.Forms.MouseEventHandler(this.gridViewForCellRadio_MouseUp);
            // 
            // gridColumnLAC
            // 
            this.gridColumnLAC.Caption = "LAC";
            this.gridColumnLAC.FieldName = "LAC";
            this.gridColumnLAC.Name = "gridColumnLAC";
            this.gridColumnLAC.Visible = true;
            this.gridColumnLAC.VisibleIndex = 0;
            this.gridColumnLAC.Width = 60;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "CI";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 1;
            this.gridColumnCI.Width = 60;
            // 
            // gridColumnRadio
            // 
            this.gridColumnRadio.Caption = "问题点比率";
            this.gridColumnRadio.FieldName = "Radio";
            this.gridColumnRadio.Name = "gridColumnRadio";
            this.gridColumnRadio.Visible = true;
            this.gridColumnRadio.VisibleIndex = 2;
            this.gridColumnRadio.Width = 109;
            // 
            // EleRadioAndLACCI
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(268, 385);
            this.Controls.Add(this.gridControlRes);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "EleRadioAndLACCI";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Load += new System.EventHandler(this.EleRadioAndLACCI_Load);
            this.LocationChanged += new System.EventHandler(this.EleRadioAndLACCI_LocationChanged);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewForCellRadio)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlRes;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewForCellRadio;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRadio;
    }
}