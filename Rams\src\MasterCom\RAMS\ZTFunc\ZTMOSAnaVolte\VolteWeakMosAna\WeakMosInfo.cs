﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakMosItem
    {
        public WeakMosItem(string strFileName)
        {
            FileName = strFileName;
            WeakMosTpList = new List<WeakMosTPInfo>();
        }
        public int SN { get; set; }
        public string FileName { get; set; }
        public string StrDes
        {
            get
            {
                return WeakMosTpList.Count > 1 ? "持续弱MOS" : "弱MOS";
            }
        }

        public List<WeakMosTPInfo> WeakMosTpList { get; set; }
        public int PointCount { get { return WeakMosTpList.Count; } }

        public float MinMos { get; set; }
        public float MaxMos { get; set; }
        public double AvgMos
        {
            get
            {
                return Math.Round(SumMos / (double)WeakMosTpList.Count, 2);
            }
        }
        public float SumMos { get; set; }

        public List<string> netTypeList { get; set; } = new List<string>();
        public string NetType 
        {
            get
            {
                StringBuilder strb = new StringBuilder();
                foreach(string str in netTypeList)
                {
                    strb.Append(str+",");
                }
                if (strb.Length > 0)
                {
                    strb = strb.Remove(strb.Length - 1, 1);
                }
                return strb.ToString();
            }
        }
        public string FirstStrTime
        {
            get
            {
                if (WeakMosTpList.Count > 0)
                {
                    return WeakMosTpList[0].StrTime;
                }
                return "";
            }
        }
        public int? FirstTac
        {
            get
            {
                if (WeakMosTpList.Count > 0)
                {
                    return WeakMosTpList[0].Tac;
                }
                return null;
            }
        }
        public string FirstCellCode
        {
            get
            {
                if (WeakMosTpList.Count > 0)
                {
                    return WeakMosTpList[0].CellCode;
                }
                return "";
            }
        }
        public double? FirstLongitude
        {
            get
            {
                if (WeakMosTpList.Count > 0)
                {
                    return WeakMosTpList[0].Longitude;
                }
                return null;
            }
        }
        public double? FirstLatitude
        {
            get
            {
                if (WeakMosTpList.Count > 0)
                {
                    return WeakMosTpList[0].Latitude;
                }
                return null;
            }
        }
        public string FirstCellName
        {
            get
            {
                if (WeakMosTpList.Count > 0)
                {
                    return WeakMosTpList[0].CellName;
                }
                return "";
            }
        }

        public string FirstMosPeriodAvgSinr
        {
            get
            {
                if (WeakMosTpList.Count > 0)
                {
                    return WeakMosTpList[0].StrSinrAvg;
                }
                return null;
            }
        }
        public string FirstMosPeriodSinr
        {
            get
            {
                if (WeakMosTpList.Count > 0)
                {
                    return WeakMosTpList[0].StrSinr;
                }
                return "";
            }
        }
        public string FirstMosPeriodAvgRsrp
        {
            get
            {
                if (WeakMosTpList.Count > 0)
                {
                    return WeakMosTpList[0].StrRsrpAvg;
                }
                return null;
            }
        }
        public string FirstMosPeriodRsrp
        {
            get
            {
                if (WeakMosTpList.Count > 0)
                {
                    return WeakMosTpList[0].StrRsrp;
                }
                return "";
            }
        }
        public int FirstMosPeriodHandOverCount
        {
            get
            {
                if (WeakMosTpList.Count > 0)
                {
                    return WeakMosTpList[0].HandOverCount;
                }
                return 0;
            }
        }
        public string EndStrTime
        {
            get
            {
                if (WeakMosTpList.Count > 1)
                {
                    return WeakMosTpList[WeakMosTpList.Count - 1].StrTime;
                }
                return "";
            }
        }
        public int? EndTac
        {
            get
            {
                if (WeakMosTpList.Count > 1)
                {
                    return WeakMosTpList[WeakMosTpList.Count - 1].Tac;
                }
                return null;
            }
        }
        public string EndCellCode
        {
            get
            {
                if (WeakMosTpList.Count > 1)
                {
                    return WeakMosTpList[WeakMosTpList.Count - 1].CellCode;
                }
                return "";
            }
        }
        public double? EndLongitude
        {
            get
            {
                if (WeakMosTpList.Count > 1)
                {
                    return WeakMosTpList[WeakMosTpList.Count - 1].Longitude;
                }
                return null;
            }
        }

        public double? EndLatitude
        {
            get
            {
                if (WeakMosTpList.Count > 1)
                {
                    return WeakMosTpList[WeakMosTpList.Count - 1].Latitude;
                }
                return null;
            }
        }
        public string EndCellName
        {
            get
            {
                if (WeakMosTpList.Count > 1)
                {
                    return WeakMosTpList[WeakMosTpList.Count - 1].CellName;
                }
                return "";
            }
        }

        public string EndMosPeriodAvgSinr
        {
            get
            {
                if (WeakMosTpList.Count > 1)
                {
                    return WeakMosTpList[WeakMosTpList.Count - 1].StrSinrAvg;
                }
                return null;
            }
        }
        public string EndMosPeriodSinr
        {
            get
            {
                if (WeakMosTpList.Count > 1)
                {
                    return WeakMosTpList[WeakMosTpList.Count - 1].StrSinr;
                }
                return "";
            }
        }
        public string EndMosPeriodAvgRsrp
        {
            get
            {
                if (WeakMosTpList.Count > 1)
                {
                    return WeakMosTpList[WeakMosTpList.Count - 1].StrRsrpAvg;
                }
                return null;
            }
        }
        public string EndMosPeriodRsrp
        {
            get
            {
                if (WeakMosTpList.Count > 1)
                {
                    return WeakMosTpList[WeakMosTpList.Count - 1].StrRsrp;
                }
                return "";
            }
        }
        public int EndMosPeriodHandOverCount
        {
            get
            {
                if (WeakMosTpList.Count > 1)
                {
                    return WeakMosTpList[WeakMosTpList.Count - 1].HandOverCount;
                }
                return 0;
            }
        }

        public void AddWeakMosPoint(TestPoint curTp, List<TestPoint> filePonits, List<Event> fileEvts, float mos)
        {
            WeakMosTPInfo info = new WeakMosTPInfo();
            info.SN = this.WeakMosTpList.Count + 1;
            info.AddPoint(curTp, filePonits, fileEvts, mos);
            this.WeakMosTpList.Add(info);

            this.SumMos += mos;
            this.MaxMos = mos > this.MaxMos ? mos : this.MaxMos;
            if (this.MinMos == 0)
            {
                this.MinMos = mos;
            }
            this.MinMos = mos < this.MinMos ? mos : this.MinMos;

            string strNetType = curTp.NetworkType.ToString();
            if (!string.IsNullOrEmpty(strNetType) && !netTypeList.Contains(strNetType))
            {
                netTypeList.Add(strNetType);
            }
        }
    }
    public class WeakMosTPInfo : LteGsmSumInfo
    {
        public WeakMosTPInfo()
        {
            EvtList = new List<Event>();
            PointList = new List<TestPoint>();
        }
        public int SN { get; set; }
        public string StrTime { get; set; }
        public int? Tac { get; set; }
        public string CellCode { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string CellName { get; set; }
        public float MosValue { get; set; }
        public TestPoint TP { get; set; }

        public int HandOverCount { get; set; }
        public List<TestPoint> PointList { get; set; }
        public List<Event> EvtList { get; set; }
        public void AddPoint(TestPoint curTp, List<TestPoint> filePonits, List<Event> fileEvts, float mos)
        {
            this.TP = curTp;
            this.StrTime = curTp.DateTime.ToString();
            this.Longitude = curTp.Longitude;
            this.Latitude = curTp.Latitude;
            this.MosValue = mos;

            this.Tac = (int?)(ushort?)curTp["lte_TAC"];
            int? eci = (int?)curTp["lte_ECI"];
            if (this.Tac == null || eci == null)
            {
                this.Tac = (int?)curTp["lte_gsm_SC_LAC"];
                eci = (int?)curTp["lte_gsm_SC_CI"];
            }
            ICell cell = curTp.GetMainCell();
            if (cell != null)
            {
                if (cell is UnknowCell && this.Tac != null && eci != null)
                {
                    this.CellName = this.CellCode = this.Tac + "_" + eci;
                }
                else
                {
                    this.CellName = cell.Name;
                    this.CellCode = cell.Code;
                }
            }
            //设x为MOS值时间点，采样点分析的时间段为【x-10,x-2】，因为事件分析要包括切换事件所以事件的分析时间段为【x-10,x-1】
            TimePeriod tpPeriod1 = new TimePeriod(curTp.DateTime.AddSeconds(-10), curTp.DateTime.AddSeconds(-2));
            int tpIdx1 = 0;
            PointList = MOSAnaManager.getTestPoinsByPeriod(tpPeriod1, filePonits, ref tpIdx1);

            foreach (TestPoint tp in PointList)
            {
                fillRsrpAndSinr(tp);
            }

            TimePeriod tpPeriod2 = new TimePeriod(curTp.DateTime.AddSeconds(-10), curTp.DateTime.AddSeconds(-1));
            int tpIdx2 = 0;
            List <Event> evts = MOSAnaManager.getEventsByPeriod(tpPeriod2, fileEvts, ref tpIdx2);
            DateTime endTime = tpPeriod2.EndTime.AddSeconds(-1);
            foreach (Event evt in evts)
            {
                if (evt.DateTime > endTime)
                {
                    continue;
                }

                if (evt.ID == 898 || evt.ID == 850 || evt.ID == 1145 || evt.ID == 1038)
                {
                    EvtList.Add(evt);
                    this.HandOverCount++;
                }
            }
        }
    }
}
