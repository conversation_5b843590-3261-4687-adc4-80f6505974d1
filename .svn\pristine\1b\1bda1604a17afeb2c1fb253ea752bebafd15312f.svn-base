﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRUnKnownDisturbAnaByFile : NRUnKnownDisturbAnaByRegion
    {
        private NRUnKnownDisturbAnaByFile()
            : base()
        {
        }

        private static readonly object lockObj = new object();
        private static NRUnKnownDisturbAnaByFile intance = null;
        public static new NRUnKnownDisturbAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRUnKnownDisturbAnaByFile();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "不明干扰(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
