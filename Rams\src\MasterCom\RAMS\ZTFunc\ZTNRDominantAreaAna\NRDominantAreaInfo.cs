﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRDominantAreaAna
{
    /// <summary>
    /// 查询条件
    /// </summary>
    public class NRDominantAreaAnaCondition
    {
        public NRDominantAreaAnaCondition()
        {
            SetCondition(DateTime.Now.AddMonths(-1), DateTime.Now, 12);
            string startPath = System.Windows.Forms.Application.StartupPath;
            SceneExcelPath = startPath + @"\config\NRDominantArea\场景.xlsx";
            ShapePath = startPath + @"\config\NRDominantArea\场景_region.shp";
            RoadPath = startPath + @"\config\NRDominantArea\MAP";
        }

        #region 设置的条件
        //条件窗选择的时段
        public TimePeriod FilePeriod { get; set; }
        //测试时段
        public TimePeriod TestPeriod { get; set; }
        //时间阈值
        public int TimeThreshold { get; set; }

        public string SceneExcelPath { get; set; }
        public string ShapePath { get; set; }
        public string RoadPath { get; set; }

        public int TestPointRadio { get; set; } = 90;

        public List<int> DistrictIDs { get; set; } = new List<int>();
        #endregion

        public Dictionary<string, NRDominantAreaDistrictInfo> DistrictConfigDic { get; set; }

        public void SetCondition(DateTime beginTime, DateTime endTime, int timeThreshold)
        {
            FilePeriod = new TimePeriod(beginTime, endTime);
            TimeThreshold = timeThreshold;
            TestPeriod = new TimePeriod(beginTime.AddMonths(-timeThreshold), endTime.AddMonths(1));
        }
    }

    #region 配置信息
    public class NRDominantAreaDistrictInfo
    {
        public int DistrictID { get; set; }
        public string DistrictName { get; set; } = "";
        public Dictionary<string, MapWinGIS.Shapefile> RoadShpNames { get; set; } 
            = new Dictionary<string, MapWinGIS.Shapefile>();
        public Dictionary<string, NRDominantAreaConfigInfo> SceneConfigDic { get; set; } 
            = new Dictionary<string, NRDominantAreaConfigInfo>();
    }

    public class NRDominantAreaConfigInfo
    { 
        public NRDominantAreaSceneInfo SceneInfo { get; set; }
        public NRDominantAreaCellInfo SceneCell { get; set; }
        public ResvRegion SceneRegion { get; set; }
    }

    public class NRDominantAreaCellInfo
    {
        public Dictionary<string, NRCell> NrCellDic { get; set; } = new Dictionary<string, NRCell>();
        public Dictionary<string, LTECell> LteCellDic { get; set; } = new Dictionary<string, LTECell>();
    }

    public class NRDominantAreaSceneInfo
    {
        public string DistrictName { get; set; }
        public string CountryName { get; set; }
        public string SceneName { get; set; }
        public NRDominantAreaSceneType SceneType { get; set; }

        public void FillDataBySQL(Package package)
        {
            DistrictName = package.Content.GetParamString();
            CountryName = package.Content.GetParamString();
            SceneName = package.Content.GetParamString();
            string sceneDesc = package.Content.GetParamString();
            SceneType = (NRDominantAreaSceneType)Enum.Parse(typeof(NRDominantAreaSceneType), sceneDesc);
        }

        public void FillDataByExcel(DataRow dr)
        {
            DistrictName = dr["地市"].ToString();
            CountryName = dr["区县"].ToString();
            SceneName = dr["场景名称"].ToString();
            string sceneDesc = dr["场景类型"].ToString();
            SceneType = (NRDominantAreaSceneType)Enum.Parse(typeof(NRDominantAreaSceneType), sceneDesc);
        }
    }
    #endregion


    /// <summary>
    /// 文件信息
    /// </summary>
    public class NRDominantAreaFileInfo
    {
        public FileInfo File { get; set; }

        #region 文件名解析出的数据
        /// <summary>
        /// 场景区域名称
        /// </summary>
        public string SceneName { get; set; }
        /// <summary>
        /// 详细字段(暂时无用)
        /// </summary>
        public string DetailDesc { get; set; }
        /// <summary>
        /// 文件名中解析出来的上报时间
        /// </summary>
        public string TimeDesc { get; set; }
        /// <summary>
        /// 运营商，限定“移动NR”“联通NR”“电信NR”，用于区分测试运营商(暂时无用)
        /// </summary>
        public string CarrierDesc { get; set; }
        /// <summary>
        /// 场景类型：限定“室内”“室外”，用于识别测试LOG类型，方便进行指标统计
        /// </summary>
        public string SceneDesc { get; set; }

        public NRDominantAreaSceneType SceneType { get; set; }
        public NRDominantAreaCarrierType CarrierType { get; set; }
        #endregion

        #region 文件有效性信息
        public string FileName { get; set; }
        /// <summary>
        /// 文件名中解析出来的上报时间
        /// </summary>
        public DateTime FileTime { get; set; }
        /// <summary>
        /// 文件实测开始时间
        /// </summary>
        public DateTime TestTime { get; set; }
        public bool IsValid { get; set; } = true;
        public string IsValidDesc { get; set; } = "是";
        public NRDominantAreaInvalidType InvalidType { get; set; } = NRDominantAreaInvalidType.无;
        /// <summary>
        /// 不合格原因
        /// </summary>
        public string InvalidReason { get; set; } = "";
        /// <summary>
        /// 宏站不合格采样点占比
        /// </summary>
        public double Percent { get; set; } = 0;
        #endregion

        /// <summary>
        /// 现场文件命名格式存在多种情况,故添加多种分隔符识别
        /// </summary>
        readonly List<char> separatorList = new List<char>() { '_', '-', '+'};

        public string Init(FileInfo file)
        {
            File = file;
            FileName = file.Name;
            TestTime = DateTime.Parse(file.BeginTimeString);

            string[] strs = new string[0];
            foreach (var separator in separatorList)
            {
                strs = FileName.Split(separator);
                if (strs.Length == 5)
                {
                    break;
                }
            }
            if (strs.Length != 5)
            {
                return "文件名格式不符合[场景名称_详细字段_日期_xxNR_场景类型]规则";
            }

            SceneName = strs[0];
            DetailDesc = strs[1];
            TimeDesc = strs[2];
            CarrierDesc = strs[3];
            SceneDesc = strs[4];
            int suffix = SceneDesc.LastIndexOf('.');
            if (suffix > 0)
            {
                SceneDesc = SceneDesc.Substring(0, suffix);
            }

            DateTime time;
            if (DateTime.TryParseExact(TimeDesc, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out time))
            {
                FileTime = time;
            }
            else
            {
                return $"文件名中[{TimeDesc}]无法转换为时间";
            }

            try
            {
                SceneType = (NRDominantAreaSceneType)Enum.Parse(typeof(NRDominantAreaSceneType), SceneDesc);
                CarrierType = (NRDominantAreaCarrierType)Enum.Parse(typeof(NRDominantAreaCarrierType), CarrierDesc);
            }
            catch
            {
                return $"文件名中运营商类型[{CarrierDesc}]或场景类型[{SceneDesc}]不在限定字段内";
            }
            return "";
        }

        public NRDominantAreaInvalidType JudgeFileTime(int day)
        {
            TimeSpan ts = FileTime.Subtract(TestTime);
            if (ts.TotalDays > day || ts.TotalDays < -30)
            {
                return NRDominantAreaInvalidType.时间不符;
            }
            return NRDominantAreaInvalidType.无;
        }

        public void SetInvalidReason(NRDominantAreaInvalidType type, NRDominantAreaAnaCondition cond)
        {
            IsValid = false;
            IsValidDesc = "否";
            InvalidType = type;
            switch (InvalidType)
            {
                case NRDominantAreaInvalidType.不在区域内:
                    int radio = 100 - cond.TestPointRadio;
                    double percent = Math.Round(100 - Percent, 2);
                    InvalidReason = $"测试点超过{radio}%未落在图层区域内；实际未落在区域内的采样点占比为{percent}";
                    break;
                case NRDominantAreaInvalidType.主服不匹配:
                    InvalidReason = "测试log主服小区与上报主服务小区不匹配；";
                    break;
                case NRDominantAreaInvalidType.时间不符:
                    InvalidReason = "测试log时间不符合条件。";
                    break;
            }
        }

        //public void CalculateResult()
        //{
        //    if (!IsValid)
        //    {
        //        IsValidDesc = "否";
        //        switch (InvalidType)
        //        {
        //            case NRDominantAreaInvalidType.不在区域内:
        //                InvalidReason = "测试点超过 10%未落在图层区域内；";
        //                break;
        //            case NRDominantAreaInvalidType.主服不匹配:
        //                InvalidReason = "测试log主服小区与上报主服务小区不匹配；";
        //                break;
        //            case NRDominantAreaInvalidType.时间不符:
        //                InvalidReason = "测试log时间不符合条件。";
        //                break;
        //        }
        //    }
        //}
    }

    public enum NRDominantAreaSceneType
    {
        室内,
        室外,
        室内外
    }

    public enum NRDominantAreaCarrierType
    {
        移动NR,
        联通NR,
        电信NR
    }

    public enum NRDominantAreaInvalidType
    {
        无,
        不在区域内,
        主服不匹配,
        时间不符
    }


    public class NRDominantAreaCellCount
    {
        public int LteCellTotalCount { get; set; }
        public int NrCellTotalCount { get; set; }

        public Dictionary<string, bool> LteCellDic { get; set; } = new Dictionary<string, bool>();
        public Dictionary<string, bool> NrCellDic { get; set; } = new Dictionary<string, bool>();
        //public Dictionary<string, bool> NrIndoorCellDic { get; set; }

        public int NrRegionIndoorCellTotalCount { get; set; }
    }


    /// <summary>
    /// 分析过程中临时保存的场景指标信息
    /// </summary>
    public class NRDominantAreaSceneIndexInfo
    {
        //基础信息
        public NRDominantAreaSceneInfo SceneInfo { get; set; }
        public ResvRegion Region { get; set; }

        //场景小区数
        public NRDominantAreaCellCount CellCount { get; set; } = new NRDominantAreaCellCount();

        public int ValidFileCount { get; set; }
        public List<NRDominantAreaFileInfo> FileList { get; set; } = new List<NRDominantAreaFileInfo>();


        public NRDominantAreaResult Result { get; set; } = new NRDominantAreaResult();
        public List<NRDominantAreaFileResult> FileResult { get; set; } = new List<NRDominantAreaFileResult>();

        public SceneKpiInfo KpiInfo { get; set; }

        public Func.StreetInjectInfoTotal InjectInfo { get; set; } = new Func.StreetInjectInfoTotal();


        public void CalculateFileResult()
        {
            FileResult = new List<NRDominantAreaFileResult>();
            foreach (var file in FileList)
            {
                NRDominantAreaFileResult fileRes = new NRDominantAreaFileResult();
                fileRes.FileName = file.FileName;
                fileRes.FileTime = file.FileTime.ToString();
                fileRes.TestTime = file.TestTime.ToString();
                fileRes.IsValidDesc = file.IsValidDesc;
                fileRes.InValidReason = file.InvalidReason;
                FileResult.Add(fileRes);
            }
        }

        public void Calculate()
        {
            CalculateFileResult();

            Result = new NRDominantAreaResult();
            Result.CreateDate = DateTime.Now;
            Result.DistrictName = SceneInfo.DistrictName;
            Result.CountryName = SceneInfo.CountryName;
            Result.SceneName = SceneInfo.SceneName;
            Result.SceneTypeDesc = Enum.GetName(typeof(NRDominantAreaSceneType), SceneInfo.SceneType);

            double time = 0;
            double distance = 0;
            DateTime newestTime = new DateTime();
            foreach (var file in FileList)
            {
                if (file.IsValid)
                {
                    time += TimeSpan.FromSeconds(file.File.EndTime - file.File.BeginTime).TotalSeconds;
                    distance += file.File.Distance;

                    if (file.TestTime > newestTime)
                    {
                        Result.TestTime = file.TestTime.ToString();
                    }
                }
            }
            Result.TotalTestTime = time;
            Result.TotalTestDistance = distance / 1000f;

            KpiInfo.Calculate(Result);

            Result.LteCellTotalCount = CellCount.LteCellTotalCount;
            Result.LteCellValidCount = CellCount.LteCellDic.Count;
            Result.NrCellTotalCount = CellCount.NrCellTotalCount;
            Result.NrCellValidCount = CellCount.NrCellDic.Count;

            if (CellCount.NrCellTotalCount > 0)
            {
                Result.NrCellRate = Math.Round(Result.NrCellValidCount * 100d / (Result.NrCellTotalCount + Result.LteCellTotalCount), 2);
                if (SceneInfo.SceneType != NRDominantAreaSceneType.室外)
                {
                    Result.NrIndoorCellRate = Math.Round(CellCount.NrCellDic.Count * 100d / CellCount.NrCellTotalCount, 2);
                }
            }

            Result.RoadTotalDistance = InjectInfo.DistTotalKM;
            Result.RoadDistance = InjectInfo.DistCoveredKM;
            Result.RoadRate = InjectInfo.CoverPercent;

            Result.TotalFileCount = FileList.Count;
            Result.ValidFileCount = ValidFileCount;
            Result.InValidFileCount = Result.TotalFileCount - Result.ValidFileCount;
            if (Result.TotalFileCount == Result.ValidFileCount)
            {
                Result.IsValid = 1;
            }
        }
    }

    public class NRDominantAreaResult
    {
        public DateTime CreateDate { get; set; }
        public string DistrictName { get; set; }
        public string CountryName { get; set; }
        public string SceneName { get; set; }
        public string SceneTypeDesc { get; set; }
        public string TestTime { get; set; }
        public double TotalTestTime { get; set; }
        public double TotalTestDistance { get; set; }

        public double CoverRate { get; set; }
        public double ComprehensiveCoverRate { get; set; }
        public double DLMacSpeed { get; set; }
        public double DLMacBSSpeed { get; set; }

        public double FtpDLSpeed { get; set; }
        public double FtpULSpeed { get; set; }

        public double RSRP { get; set; }
        public double SINR { get; set; }

        public int LteCellValidCount { get; set; }
        public int NrCellValidCount { get; set; }
        public int LteCellTotalCount { get; set; }
        public int NrCellTotalCount { get; set; }
        public double NrCellRate { get; set; }

        public double RoadTotalDistance { get; set; }
        public double RoadDistance { get; set; }
        public double RoadRate { get; set; }

        public double NrIndoorCellRate { get; set; }

        public int TotalFileCount { get; set; }
        public int ValidFileCount { get; set; }
        public int InValidFileCount { get; set; }
        public int IsValid { get; set; }
    }

    public class NRDominantAreaFileResult
    {
        public string FileName { get; set; }
        public string FileTime { get; set; }
        public string TestTime { get; set; }
        public string IsValidDesc { get; set; }
        public string InValidReason { get; set; }
    }
}
