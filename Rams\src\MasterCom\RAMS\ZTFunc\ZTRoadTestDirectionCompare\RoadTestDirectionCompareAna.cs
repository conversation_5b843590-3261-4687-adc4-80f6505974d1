﻿using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class RoadTestDirectionCompareAna : DIYAnalyseByFileBackgroundBase
    {
        string curPeriodDes = "";
        readonly CityRoadCompareSetting comPareCond = new CityRoadCompareSetting(true);
        RoadCompareDlg mapSettingDlg = null;

        Dictionary<int, Dictionary<int, string>> roadNameMatrixBak = new Dictionary<int, Dictionary<int, string>>();
        readonly Dictionary<string, RoadDirectionInfo_File> curFileDifRoadTestDic = new Dictionary<string, RoadDirectionInfo_File>();
        readonly Dictionary<string, Dictionary<string, StreetInjectInfo>> regionRoadDic = new Dictionary<string, Dictionary<string, StreetInjectInfo>>();

        string firstTimeDes;
        //各个月与第一个月对比，方向不一致道路和总道路个数信息
        readonly Dictionary<string, TestRandomInfo> testDirectionCountDic = new Dictionary<string, TestRandomInfo>();

        //<对比时段，对比时段内的道路信息集合> 渲染不同时段的GIS用
        readonly Dictionary<string, List<StreetInjectInfo>> difComPareTimeRoadInfoDic = new Dictionary<string, List<StreetInjectInfo>>();

        //<道路主键,不同时段的道路信息集合>
        readonly Dictionary<string, RoadDirectionCompareItem> roadCompareDic = new Dictionary<string, RoadDirectionCompareItem>();
        readonly List<RoadDirectionCompareItem> resultList = new List<RoadDirectionCompareItem>();

        public RoadTestDirectionCompareAna()
            : base(MainModel.GetInstance())
        {
            Columns = new List<string>();
            IncludeEvent = false;
            FilterSampleByRegion = true;
        }
        public override string Name
        {
            get { return "多时段道路测试方向对比"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20052, this.Name);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool getCondition()
        {
            if (mapSettingDlg == null)
            {
                mapSettingDlg = new RoadCompareDlg(comPareCond);
            }
            if (mapSettingDlg.ShowDialog() == DialogResult.OK)
            {
                return true;
            }
            return false;
        }
        protected override void getReadyBeforeQuery()
        {
            firstTimeDes = null;
            testDirectionCountDic.Clear();
            curFileDifRoadTestDic.Clear();
            difComPareTimeRoadInfoDic.Clear();
            resultList.Clear();
            roadCompareDic.Clear();
            WaitBox.Show("正在应用图层信息...", initStreetInfo, comPareCond.IsMergeSameRoad);
        }

        #region 应用配置的道路图层，栅格化道路
        private void initStreetInfo(object isMergeSameNameRoadObj)
        {
            bool isMergeSameNameRoad = (bool)isMergeSameNameRoadObj;
            regionRoadDic.Clear();
            bakAndClearRoadNameMatrix();

            Shapefile shapeFile = new Shapefile();
            foreach (StreetInjectTableInfo tableInfo in mainModel.StreetInjectTablesList)
            {
                shapeFile.Close();
                shapeFile.Open(tableInfo.FilePath, null);
                #region 获取道路名称、ID所在列
                int nmFldIdx = 0;
                int idIndex = 1;
                int streetCount = shapeFile.NumShapes;
                int numFields = shapeFile.NumFields;
                for (int x = 0; x < numFields; x++)
                {
                    MapWinGIS.Field field = shapeFile.get_Field(x);
                    if (field.Name.Equals(tableInfo.ColumnName))
                    {
                        nmFldIdx = x;
                    }
                    if (field.Name.Equals(tableInfo.ColumnId))
                    {
                        idIndex = x;
                    }
                }
                #endregion

                dealShapeFile(isMergeSameNameRoad, shapeFile, nmFldIdx, idIndex, streetCount);
                shapeFile.Close();
            }

            System.Threading.Thread.Sleep(200);
            WaitBox.Close();
        }

        private void dealShapeFile(bool isMergeSameNameRoad, Shapefile shapeFile, int nmFldIdx, int idIndex, int streetCount)
        {
            string shpFileName = System.IO.Path.GetFileNameWithoutExtension(shapeFile.Filename);
            for (int dx = 0; dx < streetCount; dx++)
            {
                WaitBox.ProgressPercent = dx * 100 / streetCount;

                MapWinGIS.Shape street = shapeFile.get_Shape(dx);
                if (street == null)
                {
                    continue;
                }
                string streetName = "未命名";
                object nmObj = shapeFile.get_CellValue(nmFldIdx, dx);
                if (nmObj != null)
                {
                    string tempName = nmObj.ToString().Trim();
                    streetName = string.IsNullOrEmpty(tempName) ? streetName : tempName;
                }
                object idObj = shapeFile.get_CellValue(idIndex, dx);
                string roadId = idObj == null ? "" : idObj.ToString();

                string roadName;
                if (isMergeSameNameRoad || string.IsNullOrEmpty(roadId))
                {
                    roadName = streetName;
                }
                else
                {
                    roadName = string.Format("{0}_{1}", streetName, roadId);
                }
                GISManager.GetInstance().MakeRoadShapeGrid(street, roadName);

                List<CovSegment> covSegmentList = getStreetCovSegment(street);

                addRegionRoad(shpFileName, street, streetName, roadId, roadName, covSegmentList);
            }
        }

        private void addRegionRoad(string shpFileName, MapWinGIS.Shape street, string streetName, string roadId, string roadName, List<CovSegment> covSegmentList)
        {
            List<ResvRegion> regionList = getRoadInRegions(street);
            foreach (ResvRegion region in regionList)
            {
                StreetInjectInfo ijinfo = new StreetInjectInfo();
                ijinfo.StreetName = streetName;
                ijinfo.AreaName = region.RegionName;
                ijinfo.StreetTableName = shpFileName;
                ijinfo.streetCurv = street;
                ijinfo.AddToCovSeg(covSegmentList);
                //ijinfo.distUnCovered = streetLength;
                if (!string.IsNullOrEmpty(roadId))
                {
                    ijinfo.StreetIdList.Add(int.Parse(roadId));
                }

                Dictionary<string, StreetInjectInfo> roadDic;
                if (!regionRoadDic.TryGetValue(ijinfo.AreaName, out roadDic))
                {
                    roadDic = new Dictionary<string, StreetInjectInfo>();
                    regionRoadDic.Add(ijinfo.AreaName, roadDic);
                }

                StreetInjectInfo roadInfo;
                if (roadDic.TryGetValue(roadName, out roadInfo))
                {
                    roadInfo.AddData(ijinfo);
                }
                else
                {
                    roadInfo = (StreetInjectInfo)ijinfo.Clone();
                    roadInfo.PrepareDoCalc();
                    roadDic.Add(roadName, roadInfo);
                }
            }
        }

        private List<CovSegment> getStreetCovSegment(MapWinGIS.Shape street)
        {
            List<CovSegment> covSegmentList = new List<CovSegment>();
            if (street != null)
            {
                for (int part = 0; part < street.NumParts; part++)
                {
                    List<DbPoint> pnts = ShapeHelper.GetPartShapePoints(street, part);
                    if (pnts == null)
                    {
                        continue;
                    }
                    CovSegmentGenerator csg = new CovSegmentGenerator();
                    DbPoint[] pts = pnts.ToArray();
                    for (int i = 0; i < pts.Length - 1; i++)
                    {
                        int lineTokenId = i;
                        DbPoint ptStart = pts[i];
                        DbPoint ptEnd = pts[i + 1];
                        double xGap = Math.Abs(ptEnd.x - ptStart.x);
                        double yGap = Math.Abs(ptEnd.y - ptStart.y);
                        if (xGap == 0 && yGap == 0)
                        {
                            continue;
                        }

                        int xStepNum = (int)Math.Ceiling(xGap / CD.ATOM_SPAN_LONG);//跨2.2个栅格应分析3个栅格，向上取整
                        int yStepNum = (int)Math.Ceiling(yGap / CD.ATOM_SPAN_LAT);//向上取整
                        if (xStepNum == 1 && yStepNum == 1)
                        {
                            if (xGap >= yGap)
                            {
                                xStepNum = 2;
                            }
                            else
                            {
                                yStepNum = 2;
                            }
                        }

                        #region X跨度大
                        if (xStepNum >= yStepNum)//X的跨度大些
                        {//from小经度to大经度 
                            double fromXX = 0;
                            double fromYY = 0;
                            double toXX = 0;
                            double toYY = 0;
                            int yFlag = 1;

                            if (ptStart.x <= ptEnd.x)
                            {
                                fromXX = ptStart.x;
                                fromYY = ptStart.y;
                                toXX = ptEnd.x;
                                toYY = ptEnd.y;
                                if (ptStart.y >= ptEnd.y)
                                {
                                    yFlag = -1;
                                }
                                else
                                {
                                    yFlag = 1;
                                }
                            }
                            else
                            {
                                fromXX = ptEnd.x;
                                fromYY = ptEnd.y;
                                toXX = ptStart.x;
                                toYY = ptStart.y;
                                if (ptEnd.y < ptStart.y)
                                {
                                    yFlag = 1;
                                }
                                else
                                {
                                    yFlag = -1;
                                }
                            }
                            bool greater = false;
                            for (int p = 0; p < xStepNum + 1; p++)//分析的点数为步长数+1
                            {
                                if (greater)
                                {
                                    break;
                                }
                                double xxMove = p * CD.ATOM_SPAN_LONG;
                                double yyMove = xxMove * yGap / xGap;

                                double xx = fromXX + xxMove;
                                double yy = fromYY + yFlag * yyMove;

                                if (p == xStepNum || xx > toXX)
                                {
                                    xx = toXX;
                                    yy = toYY;
                                    greater = true;
                                }
                                csg.AddPoint(xx, yy, false, true, lineTokenId, 0);
                            }
                        }
                        #endregion
                        #region Y跨度大
                        else //Y的跨度大些
                        {
                            double fromXX = 0;
                            double fromYY = 0;
                            double toXX = 0;
                            double toYY = 0;
                            int xFlag = 1;
                            if (ptStart.y <= ptEnd.y)
                            {
                                fromXX = ptStart.x;
                                fromYY = ptStart.y;
                                toXX = ptEnd.x;
                                toYY = ptEnd.y;
                                if (ptStart.x >= ptEnd.x)
                                {
                                    xFlag = -1;
                                }
                                else
                                {
                                    xFlag = 1;
                                }
                            }
                            else
                            {
                                fromXX = ptEnd.x;
                                fromYY = ptEnd.y;
                                toXX = ptStart.x;
                                toYY = ptStart.y;
                                if (ptEnd.x < ptStart.x)
                                {
                                    xFlag = 1;
                                }
                                else
                                {
                                    xFlag = -1;
                                }
                            }
                            bool greater = false;
                            for (int p = 0; p < yStepNum + 1; p++)//分析的点数为步长数+1
                            {
                                if (greater)
                                {
                                    break;
                                }
                                double yyMove = p * CD.ATOM_SPAN_LAT;
                                double xxMove = yyMove * xGap / yGap;

                                double yy = fromYY + yyMove;
                                double xx = fromXX + xFlag * xxMove;

                                if (p == yStepNum || yy > toYY)
                                {
                                    xx = toXX;
                                    yy = toYY;
                                    greater = true;
                                }
                                csg.AddPoint(xx, yy, false, true, lineTokenId, 0);
                            }
                        }
                        #endregion
                    }
                    covSegmentList.AddRange(csg.ToCovSegList());
                }
            }
            return covSegmentList;
        }

        /// <summary>
        /// 备份后初始化GISManager的栅格道路信息roadNameMatrix
        /// </summary>
        private void bakAndClearRoadNameMatrix()
        {
            roadNameMatrixBak = new Dictionary<int, Dictionary<int, string>>();
            Dictionary<int, Dictionary<int, string>> roadNameMatrix = GISManager.GetInstance().RoadNameMatrix;
            foreach (int rowIdx in roadNameMatrix.Keys)
            {
                Dictionary<int, string> dic = roadNameMatrix[rowIdx];
                Dictionary<int, string> dicBak = new Dictionary<int, string>();
                roadNameMatrixBak.Add(rowIdx, dicBak);

                foreach (int colIdx in dic.Keys)
                {
                    dicBak.Add(colIdx, dic[colIdx]);
                }
            }
            GISManager.GetInstance().ClearGridedRoadInfo();
        }

        /// <summary>
        /// 还原之前的GISManager的栅格道路信息roadNameMatrix
        /// </summary>
        private void restoreRoadNameMatrix()
        {
            GISManager.GetInstance().RoadNameMatrix = roadNameMatrixBak;
        }
        private List<ResvRegion> getRoadInRegions(MapWinGIS.Shape street)
        {
            List<ResvRegion> regionList = new List<ResvRegion>();
            foreach (ResvRegion region in mainModel.SearchGeometrys.SelectedResvRegions)
            {
                if (region.GeoOp.CheckStreetInRegion(street))
                {
                    regionList.Add(region);
                }
            }
            return regionList;
        }
        #endregion

        protected override void query()
        {
            if (MainModel.IsBackground && !MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
            }
            catch
            {
                //continue
            }

            bool multiGeometrys = mainModel.MultiGeometrys;
            mainModel.MultiGeometrys = true;

            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;
            if (!MainModel.IsBackground)
            {
                if (!MainModel.QueryFromBackground)
                {
                    queryFile();
                }
                else
                {
                    getBackgroundData();
                }
                MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;
                if (!MainModel.QueryFromBackground)
                {
                    WaitBox.Show("正在汇总结果信息...", getResultsBeforeShow);
                    MainModel.FireDTDataChanged(this);
                    fireShowForm();
                    fireSetDefaultMapSerialTheme();
                }
                else
                {
                    initBackgroundImageDesc();
                }
            }
            else
            {
                doBackgroundStatByFile(clientProxy);
                clientProxy.Close();
            }

            restoreRoadNameMatrix();
            mainModel.MultiGeometrys = multiGeometrys;
        }

        private void queryFile()
        {
            foreach (TimePeriod timePeriod in comPareCond.PeriodList)
            {
                curPeriodDes = ZTRoadCompareAna.GetTimePeriodDes(timePeriod);
                if (string.IsNullOrEmpty(firstTimeDes))
                {
                    firstTimeDes = curPeriodDes;
                }
                else
                {
                    TestRandomInfo random = new TestRandomInfo(firstTimeDes, curPeriodDes);
                    random.SN = testDirectionCountDic.Count + 1;
                    testDirectionCountDic.Add(curPeriodDes, random);
                    difComPareTimeRoadInfoDic.Add(curPeriodDes, new List<StreetInjectInfo>());
                }
                condition.Periods.Clear();
                condition.Periods.Add(timePeriod);
                queryAndAnalyseFiles();
            }
        }

        protected void queryAndAnalyseFiles()
        {
            queryFileToAnalyse();
            WaitBox.CanCancel = true;
            WaitBox.Show("开始分析文件...", analyseFiles);
            DoWaitBoxAfterGetResults();

        }
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager dtFile in MainModel.DTDataManager.FileDataManagers)
            {
                TestPoint lastTp = null;
                EnumDirection lastDiretion = EnumDirection.None;
                curFileDifRoadTestDic.Clear();
                foreach (TestPoint tp in dtFile.TestPoints)
                {
                    if (WaitBox.CancelRequest)
                    {
                        return;
                    }

                    if (lastTp != null)
                    {
                        lastDiretion = dealLastTP(lastTp, lastDiretion, tp);
                    }
                    else
                    {
                        lastDiretion = EnumDirection.None;
                    }

                    lastTp = tp;
                }

                saveResult(curFileDifRoadTestDic);
            }
        }

        private EnumDirection dealLastTP(TestPoint lastTp, EnumDirection lastDiretion, TestPoint tp)
        {
            double distance = MasterCom.Util.MathFuncs.GetDistance(tp.Longitude, tp.Latitude, lastTp.Longitude, lastTp.Latitude);
            EnumDirection direction = TestDirectionHelper.GetDireciton(lastTp, tp);//测试方向
            bool isOpposite = TestDirectionHelper.IsOppositeDireciton(direction, lastDiretion);//是否折返测试
            if (isOpposite)
            {
                saveResult(curFileDifRoadTestDic);
            }

            StreetInjectInfo street;
            if (getTpLocation(tp, out street))
            {
                string strToken;
                if (comPareCond.IsMergeSameRoad)
                {
                    strToken = string.Format("{0}_{1}_{2}", street.AreaName, street.StreetTableName, street.StreetName);
                }
                else
                {
                    strToken = RoadInjectCompareItem.GetToken(street);
                }
                RoadDirectionInfo_File roadInfo;
                if (!curFileDifRoadTestDic.TryGetValue(strToken, out roadInfo))
                {
                    roadInfo = new RoadDirectionInfo_File(curPeriodDes, street);
                    curFileDifRoadTestDic.Add(strToken, roadInfo);
                }
                roadInfo.AddDirectionInfo(direction, distance);
            }

            if (direction != EnumDirection.None)
            {
                lastDiretion = direction;
            }

            return lastDiretion;
        }

        private void saveResult(Dictionary<string, RoadDirectionInfo_File> fileDifRoadTestDic)
        {
            foreach (string roadToken in fileDifRoadTestDic.Keys)
            {
                RoadDirectionInfo_File roadTestInfo = fileDifRoadTestDic[roadToken];
                if (roadTestInfo.GetMainDirection())
                {
                    string token = RoadDirectionInfoBase.GetToken(roadTestInfo);

                    RoadDirectionCompareItem item;
                    if (!roadCompareDic.TryGetValue(token, out item))
                    {
                        item = new RoadDirectionCompareItem(roadTestInfo.TimeDes, roadTestInfo.StreetInfo);
                        roadCompareDic[token] = item;
                    }
                    item.AddData(roadTestInfo);
                }
            }
            fileDifRoadTestDic.Clear();
        }
        private bool getTpLocation(TestPoint tp, out StreetInjectInfo street)
        {
            street = null;
            string roadName = GISManager.GetInstance().GetRoadPlaceDesc(tp.Longitude, tp.Latitude);
            if (string.IsNullOrEmpty(roadName))
            {
                return false;
            }

            foreach (ResvRegion region in mainModel.SearchGeometrys.SelectedResvRegions)
            {
                if (region.GeoOp.CheckPointInRegion(tp.Longitude, tp.Latitude))
                {
                    Dictionary<string, StreetInjectInfo> roadDic;
                    if (regionRoadDic.TryGetValue(region.RegionName, out roadDic))
                    {
                        StreetInjectInfo roadInfo;
                        if (roadDic.TryGetValue(roadName, out roadInfo))
                        {
                            street = roadInfo;
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        protected void getResultsBeforeShow()
        {
            foreach (RoadDirectionCompareItem item in roadCompareDic.Values)
            {
                item.GetSumInfo(firstTimeDes);

                item.SN = resultList.Count + 1;
                resultList.Add(item);

                foreach (RoadDirectionInfo_Period periodInfo in item.DifTimeRoadDirectionInfoList)
                {
                    addStreetInjectInfo(periodInfo);
                    setTestRandomInfo(periodInfo);
                }
            }
            System.Threading.Thread.Sleep(200);
            WaitBox.Close();
        }

        private void addStreetInjectInfo(RoadDirectionInfo_Period periodInfo)
        {
            #region 记录需要渲染的StreetInjectInfo
            List<StreetInjectInfo> streetList;
            if (difComPareTimeRoadInfoDic.TryGetValue(periodInfo.TimeDes, out streetList)
                && periodInfo.IsSameDirectionWithFirst != null)
            {
                foreach (CovSegment cs in periodInfo.StreetInfo.covSegmentList)
                {
                    cs.Covered = (bool)periodInfo.IsSameDirectionWithFirst;//暂用方向一致代替覆盖（方便GIS渲染用）
                }
                streetList.Add(periodInfo.StreetInfo);
            }
            #endregion
        }

        private void setTestRandomInfo(RoadDirectionInfo_Period periodInfo)
        {
            #region 统计测试随机性
            TestRandomInfo randomInfo;
            if (testDirectionCountDic.TryGetValue(periodInfo.TimeDes, out randomInfo))
            {
                if (periodInfo.IsSameDirectionWithFirst == true)
                {
                    randomInfo.SameDirectionCount++;
                }
                else if (periodInfo.IsSameDirectionWithFirst == false)
                {
                    randomInfo.DifferentDirectionCount++;
                }
            }
            #endregion
        }

        protected override void fireShowForm()
        {
            RoadTestDirectionCompareInfoForm frm = mainModel.CreateResultForm(typeof(RoadTestDirectionCompareInfoForm)) as RoadTestDirectionCompareInfoForm;
            frm.FillDatas(resultList, new List<TestRandomInfo>(testDirectionCountDic.Values), difComPareTimeRoadInfoDic);
            frm.Visible = true;
            frm.BringToFront();
        }
    }
    public enum EnumDirection
    {
        北 = 1,
        东北,
        东,
        东南,
        南,
        西南,
        西,
        西北,
        None,
    }
}
