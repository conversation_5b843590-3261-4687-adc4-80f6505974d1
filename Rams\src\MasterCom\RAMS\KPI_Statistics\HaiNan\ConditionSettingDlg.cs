﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics.HaiNan
{
    public partial class ConditionSettingDlg : BaseDialog
    {
        private ConditionSet condition = null;
        public ConditionSettingDlg()
        {
            InitializeComponent();
            this.condition = new ConditionSet();
        }

        private void buttonSelectFile_Click(object sender, EventArgs e)
        {
            OpenFileDialog openDlg = new OpenFileDialog();
            if (openDlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }
            this.textBoxFileName.Text = openDlg.FileName;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(this.textBoxFileName.Text))
            {
                MessageBox.Show("路径名不可以为空!");
                return;
            }
            this.condition.fileName = this.textBoxFileName.Text;
            this.condition.radius = (float)this.spinEditRadius.Value;
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }
        public void SetCondition(ConditionSet cond)
        {
            this.textBoxFileName.Text = cond.fileName;
            this.spinEditRadius.Value = (decimal)cond.radius;
        }
        public ConditionSet GetCondition()
        {
            return this.condition;
        }
    }

    public class ConditionSet
    {
        public string fileName { get; set; }
        public float radius { get; set; }
    }
}
