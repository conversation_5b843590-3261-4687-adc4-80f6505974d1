﻿namespace MasterCom.RAMS.Func
{
    partial class WeakRxQualTCPForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode2 = new DevExpress.XtraGrid.GridLevelNode();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colRelation = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCellID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBCCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBSIC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colRxLevSub = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.mainDateTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.maiCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainCellID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainBCCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainBSIC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainRxLevSub = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainTCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainRxQual = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainHP = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainLon = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainLat = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainC2IARFCN = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainC2IRxLev = new DevExpress.XtraGrid.Columns.GridColumn();
            this.mainC2I = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colRelation,
            this.colCellName,
            this.colCellID,
            this.colLAC,
            this.colCI,
            this.colBCCH,
            this.colBSIC,
            this.colRxLevSub,
            this.colTCH});
            this.gridView2.GridControl = this.gridControl;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsDetail.ShowDetailTabs = false;
            this.gridView2.OptionsView.ColumnAutoWidth = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.gridView_CustomDrawCell);
            this.gridView2.DoubleClick += new System.EventHandler(this.gridView2_DoubleClick);
            // 
            // colRelation
            // 
            this.colRelation.Caption = "Relation";
            this.colRelation.FieldName = "Relation";
            this.colRelation.Name = "colRelation";
            // 
            // colCellName
            // 
            this.colCellName.Caption = "小区名";
            this.colCellName.FieldName = "CellName";
            this.colCellName.Name = "colCellName";
            this.colCellName.Visible = true;
            this.colCellName.VisibleIndex = 0;
            // 
            // colCellID
            // 
            this.colCellID.Caption = "小区ID";
            this.colCellID.FieldName = "CellID";
            this.colCellID.Name = "colCellID";
            this.colCellID.Visible = true;
            this.colCellID.VisibleIndex = 1;
            // 
            // colLAC
            // 
            this.colLAC.Caption = "LAC";
            this.colLAC.FieldName = "LAC";
            this.colLAC.Name = "colLAC";
            this.colLAC.Visible = true;
            this.colLAC.VisibleIndex = 2;
            // 
            // colCI
            // 
            this.colCI.Caption = "CI";
            this.colCI.FieldName = "CI";
            this.colCI.Name = "colCI";
            this.colCI.Visible = true;
            this.colCI.VisibleIndex = 3;
            // 
            // colBCCH
            // 
            this.colBCCH.Caption = "BCCH";
            this.colBCCH.FieldName = "BCCH";
            this.colBCCH.Name = "colBCCH";
            this.colBCCH.Tag = "ColorBCCH";
            this.colBCCH.Visible = true;
            this.colBCCH.VisibleIndex = 4;
            // 
            // colBSIC
            // 
            this.colBSIC.Caption = "BSIC";
            this.colBSIC.FieldName = "BSIC";
            this.colBSIC.Name = "colBSIC";
            this.colBSIC.Visible = true;
            this.colBSIC.VisibleIndex = 5;
            // 
            // colRxLevSub
            // 
            this.colRxLevSub.Caption = "场强";
            this.colRxLevSub.FieldName = "RxLevSub";
            this.colRxLevSub.Name = "colRxLevSub";
            this.colRxLevSub.Visible = true;
            this.colRxLevSub.VisibleIndex = 6;
            // 
            // colTCH
            // 
            this.colTCH.Caption = "TCH";
            this.colTCH.FieldName = "TCH";
            this.colTCH.Name = "colTCH";
            this.colTCH.Tag = "ColorTCH";
            this.colTCH.Visible = true;
            this.colTCH.VisibleIndex = 7;
            this.colTCH.Width = 100;
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode2.LevelTemplate = this.gridView2;
            gridLevelNode2.RelationName = "NeiborCellList";
            this.gridControl.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode2});
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.ShowOnlyPredefinedDetails = true;
            this.gridControl.Size = new System.Drawing.Size(1284, 440);
            this.gridControl.TabIndex = 2;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView,
            this.gridView2});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(151, 26);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(150, 22);
            this.miExportToExcel.Text = "导出到Excel...";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.mainDateTime,
            this.maiCellName,
            this.mainCellID,
            this.mainLAC,
            this.mainCI,
            this.mainBCCH,
            this.mainBSIC,
            this.mainRxLevSub,
            this.mainTCH,
            this.mainRxQual,
            this.mainHP,
            this.mainLon,
            this.mainLat,
            this.mainFileName,
            this.mainC2IARFCN,
            this.mainC2IRxLev,
            this.mainC2I});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsDetail.ShowDetailTabs = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            this.gridView.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.mainGridView_CustomDrawCell);
            this.gridView.MasterRowGetChildList += new DevExpress.XtraGrid.Views.Grid.MasterRowGetChildListEventHandler(this.gridView_MasterRowGetChildList);
            this.gridView.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // mainDateTime
            // 
            this.mainDateTime.Caption = "时间";
            this.mainDateTime.FieldName = "DateTime";
            this.mainDateTime.Name = "mainDateTime";
            this.mainDateTime.Visible = true;
            this.mainDateTime.VisibleIndex = 0;
            // 
            // maiCellName
            // 
            this.maiCellName.Caption = "小区名";
            this.maiCellName.FieldName = "CellName";
            this.maiCellName.Name = "maiCellName";
            this.maiCellName.Visible = true;
            this.maiCellName.VisibleIndex = 1;
            // 
            // mainCellID
            // 
            this.mainCellID.Caption = "小区ID";
            this.mainCellID.FieldName = "CellID";
            this.mainCellID.Name = "mainCellID";
            this.mainCellID.Visible = true;
            this.mainCellID.VisibleIndex = 2;
            // 
            // mainLAC
            // 
            this.mainLAC.Caption = "LAC";
            this.mainLAC.FieldName = "LAC";
            this.mainLAC.Name = "mainLAC";
            this.mainLAC.Visible = true;
            this.mainLAC.VisibleIndex = 3;
            // 
            // mainCI
            // 
            this.mainCI.Caption = "CI";
            this.mainCI.FieldName = "CI";
            this.mainCI.Name = "mainCI";
            this.mainCI.Visible = true;
            this.mainCI.VisibleIndex = 4;
            // 
            // mainBCCH
            // 
            this.mainBCCH.Caption = "BCCH";
            this.mainBCCH.FieldName = "BCCH";
            this.mainBCCH.Name = "mainBCCH";
            this.mainBCCH.Visible = true;
            this.mainBCCH.VisibleIndex = 5;
            // 
            // mainBSIC
            // 
            this.mainBSIC.Caption = "BSIC";
            this.mainBSIC.FieldName = "BSIC";
            this.mainBSIC.Name = "mainBSIC";
            this.mainBSIC.Visible = true;
            this.mainBSIC.VisibleIndex = 6;
            // 
            // mainRxLevSub
            // 
            this.mainRxLevSub.Caption = "场强";
            this.mainRxLevSub.FieldName = "RxLev";
            this.mainRxLevSub.Name = "mainRxLevSub";
            this.mainRxLevSub.Visible = true;
            this.mainRxLevSub.VisibleIndex = 7;
            // 
            // mainTCH
            // 
            this.mainTCH.Caption = "TCH";
            this.mainTCH.FieldName = "TCH";
            this.mainTCH.Name = "mainTCH";
            this.mainTCH.Visible = true;
            this.mainTCH.VisibleIndex = 8;
            this.mainTCH.Width = 100;
            // 
            // mainRxQual
            // 
            this.mainRxQual.Caption = "质量";
            this.mainRxQual.FieldName = "RxQual";
            this.mainRxQual.Name = "mainRxQual";
            this.mainRxQual.Visible = true;
            this.mainRxQual.VisibleIndex = 9;
            // 
            // mainHP
            // 
            this.mainHP.Caption = "是否跳频";
            this.mainHP.FieldName = "HP";
            this.mainHP.Name = "mainHP";
            this.mainHP.Visible = true;
            this.mainHP.VisibleIndex = 10;
            // 
            // mainLon
            // 
            this.mainLon.Caption = "经度";
            this.mainLon.FieldName = "Longitude";
            this.mainLon.Name = "mainLon";
            this.mainLon.Visible = true;
            this.mainLon.VisibleIndex = 11;
            // 
            // mainLat
            // 
            this.mainLat.Caption = "纬度";
            this.mainLat.FieldName = "Latitude";
            this.mainLat.Name = "mainLat";
            this.mainLat.Visible = true;
            this.mainLat.VisibleIndex = 12;
            // 
            // mainFileName
            // 
            this.mainFileName.Caption = "文件名";
            this.mainFileName.FieldName = "FileName";
            this.mainFileName.Name = "mainFileName";
            this.mainFileName.Visible = true;
            this.mainFileName.VisibleIndex = 13;
            // 
            // mainC2IARFCN
            // 
            this.mainC2IARFCN.Caption = "C2I_ARFCN";
            this.mainC2IARFCN.FieldName = "C2IARFCN";
            this.mainC2IARFCN.Name = "mainC2IARFCN";
            this.mainC2IARFCN.Visible = true;
            this.mainC2IARFCN.VisibleIndex = 14;
            // 
            // mainC2IRxLev
            // 
            this.mainC2IRxLev.Caption = "C2I_RxLev";
            this.mainC2IRxLev.FieldName = "C2IRxLev";
            this.mainC2IRxLev.Name = "mainC2IRxLev";
            this.mainC2IRxLev.Visible = true;
            this.mainC2IRxLev.VisibleIndex = 15;
            // 
            // mainC2I
            // 
            this.mainC2I.Caption = "C2I";
            this.mainC2I.FieldName = "C2I";
            this.mainC2I.Name = "mainC2I";
            this.mainC2I.Visible = true;
            this.mainC2I.VisibleIndex = 16;
            // 
            // WeakRxQualTCPForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1284, 440);
            this.Controls.Add(this.gridControl);
            this.Name = "WeakRxQualTCPForm";
            this.Text = "质差频点查询";
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn colCellName;
        private DevExpress.XtraGrid.Columns.GridColumn colCellID;
        private DevExpress.XtraGrid.Columns.GridColumn colLAC;
        private DevExpress.XtraGrid.Columns.GridColumn colCI;
        private DevExpress.XtraGrid.Columns.GridColumn colBCCH;
        private DevExpress.XtraGrid.Columns.GridColumn colBSIC;
        private DevExpress.XtraGrid.Columns.GridColumn colRxLevSub;
        private DevExpress.XtraGrid.Columns.GridColumn colTCH;
        private DevExpress.XtraGrid.Columns.GridColumn colRelation;
        private DevExpress.XtraGrid.Columns.GridColumn mainDateTime;
        private DevExpress.XtraGrid.Columns.GridColumn maiCellName;
        private DevExpress.XtraGrid.Columns.GridColumn mainCellID;
        private DevExpress.XtraGrid.Columns.GridColumn mainLAC;
        private DevExpress.XtraGrid.Columns.GridColumn mainCI;
        private DevExpress.XtraGrid.Columns.GridColumn mainBCCH;
        private DevExpress.XtraGrid.Columns.GridColumn mainBSIC;
        private DevExpress.XtraGrid.Columns.GridColumn mainTCH;
        private DevExpress.XtraGrid.Columns.GridColumn mainRxQual;
        private DevExpress.XtraGrid.Columns.GridColumn mainRxLevSub;
        private DevExpress.XtraGrid.Columns.GridColumn mainHP;
        private DevExpress.XtraGrid.Columns.GridColumn mainLon;
        private DevExpress.XtraGrid.Columns.GridColumn mainLat;
        private DevExpress.XtraGrid.Columns.GridColumn mainC2IARFCN;
        private DevExpress.XtraGrid.Columns.GridColumn mainC2IRxLev;
        private DevExpress.XtraGrid.Columns.GridColumn mainC2I;
        private DevExpress.XtraGrid.Columns.GridColumn mainFileName;


    }
}