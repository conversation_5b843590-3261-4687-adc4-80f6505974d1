﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class DLSpeedPitSet
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.gcSet = new DevExpress.XtraEditors.GroupControl();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.label8 = new System.Windows.Forms.Label();
            this.dPitAfterLastTime = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.dPitLastSpeedAvg = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.dPitLastTime = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.dPitDropSpeed = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.dPitPreSpeedAvg = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.dPitPreLastTime = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.gcSet)).BeginInit();
            this.gcSet.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dPitAfterLastTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dPitLastSpeedAvg)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dPitLastTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dPitDropSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dPitPreSpeedAvg)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dPitPreLastTime)).BeginInit();
            this.SuspendLayout();
            // 
            // gcSet
            // 
            this.gcSet.Controls.Add(this.btnCancel);
            this.gcSet.Controls.Add(this.btnOK);
            this.gcSet.Controls.Add(this.label8);
            this.gcSet.Controls.Add(this.dPitAfterLastTime);
            this.gcSet.Controls.Add(this.label7);
            this.gcSet.Controls.Add(this.dPitLastSpeedAvg);
            this.gcSet.Controls.Add(this.label6);
            this.gcSet.Controls.Add(this.dPitLastTime);
            this.gcSet.Controls.Add(this.label5);
            this.gcSet.Controls.Add(this.label4);
            this.gcSet.Controls.Add(this.dPitDropSpeed);
            this.gcSet.Controls.Add(this.label2);
            this.gcSet.Controls.Add(this.dPitPreSpeedAvg);
            this.gcSet.Controls.Add(this.label3);
            this.gcSet.Controls.Add(this.dPitPreLastTime);
            this.gcSet.Controls.Add(this.label1);
            this.gcSet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcSet.Location = new System.Drawing.Point(0, 0);
            this.gcSet.Name = "gcSet";
            this.gcSet.Size = new System.Drawing.Size(552, 189);
            this.gcSet.TabIndex = 0;
            this.gcSet.Text = "设置项";
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(372, 132);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 16;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(282, 132);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 15;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(506, 87);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(11, 12);
            this.label8.TabIndex = 14;
            this.label8.Text = "S";
            // 
            // dPitAfterLastTime
            // 
            this.dPitAfterLastTime.DecimalPlaces = 2;
            this.dPitAfterLastTime.Location = new System.Drawing.Point(437, 82);
            this.dPitAfterLastTime.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.dPitAfterLastTime.Name = "dPitAfterLastTime";
            this.dPitAfterLastTime.Size = new System.Drawing.Size(63, 21);
            this.dPitAfterLastTime.TabIndex = 13;
            this.dPitAfterLastTime.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.dPitAfterLastTime.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(327, 88);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(119, 12);
            this.label7.TabIndex = 12;
            this.label7.Text = "M  回升后持续时间：";
            // 
            // dPitLastSpeedAvg
            // 
            this.dPitLastSpeedAvg.DecimalPlaces = 2;
            this.dPitLastSpeedAvg.Location = new System.Drawing.Point(266, 83);
            this.dPitLastSpeedAvg.Name = "dPitLastSpeedAvg";
            this.dPitLastSpeedAvg.Size = new System.Drawing.Size(65, 21);
            this.dPitLastSpeedAvg.TabIndex = 11;
            this.dPitLastSpeedAvg.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.dPitLastSpeedAvg.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(168, 85);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(107, 12);
            this.label6.TabIndex = 10;
            this.label6.Text = "S  在坑平均速率：";
            // 
            // dPitLastTime
            // 
            this.dPitLastTime.DecimalPlaces = 2;
            this.dPitLastTime.Location = new System.Drawing.Point(101, 80);
            this.dPitLastTime.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.dPitLastTime.Name = "dPitLastTime";
            this.dPitLastTime.Size = new System.Drawing.Size(61, 21);
            this.dPitLastTime.TabIndex = 9;
            this.dPitLastTime.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.dPitLastTime.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(15, 85);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(89, 12);
            this.label5.TabIndex = 8;
            this.label5.Text = "在坑持续时间：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(506, 45);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(11, 12);
            this.label4.TabIndex = 7;
            this.label4.Text = "M";
            // 
            // dPitDropSpeed
            // 
            this.dPitDropSpeed.DecimalPlaces = 2;
            this.dPitDropSpeed.Location = new System.Drawing.Point(437, 40);
            this.dPitDropSpeed.Name = "dPitDropSpeed";
            this.dPitDropSpeed.Size = new System.Drawing.Size(63, 21);
            this.dPitDropSpeed.TabIndex = 6;
            this.dPitDropSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.dPitDropSpeed.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(328, 44);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(119, 12);
            this.label2.TabIndex = 5;
            this.label2.Text = "M  落坑点速率降幅：";
            // 
            // dPitPreSpeedAvg
            // 
            this.dPitPreSpeedAvg.DecimalPlaces = 2;
            this.dPitPreSpeedAvg.Location = new System.Drawing.Point(266, 39);
            this.dPitPreSpeedAvg.Name = "dPitPreSpeedAvg";
            this.dPitPreSpeedAvg.Size = new System.Drawing.Size(65, 21);
            this.dPitPreSpeedAvg.TabIndex = 4;
            this.dPitPreSpeedAvg.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.dPitPreSpeedAvg.Value = new decimal(new int[] {
            30,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(168, 44);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(107, 12);
            this.label3.TabIndex = 3;
            this.label3.Text = "S  坑前平均速率：";
            // 
            // dPitPreLastTime
            // 
            this.dPitPreLastTime.DecimalPlaces = 2;
            this.dPitPreLastTime.Location = new System.Drawing.Point(101, 37);
            this.dPitPreLastTime.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.dPitPreLastTime.Name = "dPitPreLastTime";
            this.dPitPreLastTime.Size = new System.Drawing.Size(61, 21);
            this.dPitPreLastTime.TabIndex = 2;
            this.dPitPreLastTime.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.dPitPreLastTime.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(15, 42);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(89, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "坑前持续时间：";
            // 
            // DLSpeedPitSet
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(552, 189);
            this.Controls.Add(this.gcSet);
            this.Name = "DLSpeedPitSet";
            this.Text = "下载速率掉坑设置";
            ((System.ComponentModel.ISupportInitialize)(this.gcSet)).EndInit();
            this.gcSet.ResumeLayout(false);
            this.gcSet.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dPitAfterLastTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dPitLastSpeedAvg)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dPitLastTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dPitDropSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dPitPreSpeedAvg)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dPitPreLastTime)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl gcSet;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown dPitPreLastTime;
        private System.Windows.Forms.NumericUpDown dPitPreSpeedAvg;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown dPitDropSpeed;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown dPitLastTime;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown dPitLastSpeedAvg;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown dPitAfterLastTime;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
    }
}