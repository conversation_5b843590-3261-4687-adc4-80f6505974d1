﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public abstract class ReasonBase
    {
        public string Name { get; set; }
        public override string ToString()
        {
            return Name ?? "";
        }
        public bool Enable { get; set; } = true;
        public abstract bool IsValid(TestPoint tp, params object[] resvParams);

        public virtual Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey(this.Name))
                {
                    this.Enable = (bool)param["Enable"];
                }
            }
        }
    }
}
