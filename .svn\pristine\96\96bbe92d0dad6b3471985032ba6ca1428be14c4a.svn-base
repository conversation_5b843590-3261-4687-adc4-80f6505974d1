﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Xml;
using System.Data;
using System.Data.SqlClient;
using System.Collections.ObjectModel;

using MasterCom;

using MasterCom.Util;
using MasterCom.MControls;

namespace MasterCom.RAMS.ZTFunc
{
    public class CellAssociationKPIItem
    {
        public CellAssociationKPIItem()
        {
            ColorRanges = new List<ColorRange>();
        }

        public string Key
        {
            get { return string.Format("{0}:{1}:{2}:{3}", (int)TimeType, (int)NetType, KPISetEncode, ID); }
            private set
            {
                string[] parts = value.Split(':');
                TimeType = (ECellAssociationKPITimeType)Enum.Parse(typeof(ECellAssociationKPITimeType), parts[0]);
                NetType = (ECellAssociationKPINetType)Enum.Parse(typeof(ECellAssociationKPINetType), parts[1]);
                KPISetEncode = parts[2];
                ID = parts[3];
            }
        }
        
        // Base Attributes
        public string Name
        {
            get;
            private set;
        }

        public string ID
        {
            get;
            private set;
        }

        public string KPISetName
        {
            get;
            private set;
        }

        public string KPISetEncode
        {
            get;
            private set;
        }

        public ECellAssociationKPITimeType TimeType
        {
            get;
            private set;
        }

        public ECellAssociationKPINetType NetType
        {
            get;
            private set;
        }

        public Dictionary<string, object> BaseParams
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("Name", Name);
                dic.Add("ID", ID);
                dic.Add("KPISetName", KPISetName);
                dic.Add("KPISetEncode", KPISetEncode);
                dic.Add("TimeType", (int)TimeType);
                dic.Add("NetType", (int)NetType);
                return dic;
            }
            set
            {
                if (value.ContainsKey("Name") && value["Name"] != null)
                {
                    Name = value["Name"].ToString();
                }
                if (value.ContainsKey("ID") && value["ID"] != null)
                {
                    ID = value["ID"].ToString();
                }
                if (value.ContainsKey("KPISetName") && value["KPISetName"] != null)
                {
                    KPISetName = value["KPISetName"].ToString();
                }
                if (value.ContainsKey("KPISetEncode") && value["KPISetEncode"] != null)
                {
                    KPISetEncode = value["KPISetEncode"].ToString();
                }
                if (value.ContainsKey("TimeType") && value["TimeType"] != null)
                {
                    TimeType = (ECellAssociationKPITimeType)Enum.Parse(typeof(ECellAssociationKPITimeType), value["TimeType"].ToString());
                }
                if (value.ContainsKey("NetType") && value["NetType"] != null)
                {
                    NetType = (ECellAssociationKPINetType)Enum.Parse(typeof(ECellAssociationKPINetType), value["NetType"].ToString());
                }
            }
        }

        // Extra Attributes
        public List<ColorRange> ColorRanges
        {
            get;
            private set;
        }

        public bool HasExtra
        {
            get { return ColorRanges != null && ColorRanges.Count > 0; }
        }

        public Dictionary<string, object> ExtraParams
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("Key", Key);
                if (HasExtra)
                {
                    List<object> colorRanges = new List<object>();
                    foreach (ColorRange cr in ColorRanges)
                    {
                        colorRanges.Add(cr.Param);
                    }
                    dic.Add("ColorRanges", colorRanges);
                }
                return dic;
            }
            set
            {
                if (value.ContainsKey("Key") && value["Key"] != null)
                {
                    Key = value["Key"].ToString();
                }
                if (value.ContainsKey("ColorRanges") && value["ColorRanges"] != null)
                {
                    List<object> colorRanges = value["ColorRanges"] as List<object>;
                    ColorRanges.Clear();
                    foreach (object o in colorRanges)
                    {
                        ColorRange cr = new ColorRange();
                        cr.Param = o as Dictionary<string, object>;
                        ColorRanges.Add(cr);
                    }
                }
            }
        }

        public static float SMinRange { get; set; } = -100000000;
        public static float SMaxRange { get; set; } = 100000000;
    }

    public class CellAssociationKPIManager
    {
        public static CellAssociationKPIManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new CellAssociationKPIManager();
                }
                return instance;
            }
        }

        public List<CellAssociationKPIItem> KPIList
        {
            get;
            private set;
        }

        /// <summary>
        /// 保存指标的自定义属性
        /// </summary>
        public void Save()
        {
            SaveExtraParams();
        }

        /// <summary>
        /// 更新指标集
        /// </summary>
        public void Download()
        {
            LoadFromDb();
            BindingExtraToBase();
        }

        private void Load()
        {
            if (!File.Exists(xmlBaseFile))
            {
                LoadFromDb();
            }
            else
            {
                LoadBaseParams();
            }

            LoadExtraParams();
            BindingExtraToBase();
        }

        private void LoadFromDb()
        {
            string sql =
@"select a.指标名称,a.指标标识,b.指标集编码,b.指标集名称,c.对象类型ID,c.时间类型ID
from [MTNOH_BASIC_CONF].[dbo].[TB_AAA_数据查询配置_指标集指标] a
left join [MTNOH_BASIC_CONF].[dbo].[TB_AAA_数据查询配置_指标集定义] b on a.所属指标集编码 = b.指标集编码
left join [MTNOH_BASIC_CONF].[dbo].[TB_AAA_数据查询配置_数据表] c on c.所属指标集编码 = b.指标集编码
where c.对象类型ID in (100,200,300) and c.时间类型ID in (3,4)
order by c.对象类型ID, b.指标集名称 ";

            KPIList.Clear();
            SqlDataReader reader = SqlHelper.ExecuteReader(CellAssociationKPIConfig.Instance.ConnectionString, CommandType.Text, sql);
            while (reader.Read())
            {
                CellAssociationKPIItem kpiItem = new CellAssociationKPIItem();
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("Name", reader["指标名称"]);
                dic.Add("ID", reader["指标标识"]);
                dic.Add("KPISetEncode", reader["指标集编码"]);
                dic.Add("KPISetName", reader["指标集名称"]);
                dic.Add("NetType", reader["对象类型ID"]);
                dic.Add("TimeType", reader["时间类型ID"]);
                kpiItem.BaseParams = dic;
                KPIList.Add(kpiItem);
            }

            SaveBaseParams();
        }

        private void LoadBaseParams()
        {
            KPIList.Clear();
            if (!File.Exists(xmlBaseFile))
            {
                return;
            }

            XmlConfigFile configFile = new XmlConfigFile(xmlBaseFile);
            List<object> loadList = configFile.GetItemValue("Config", "KPIList") as List<object>;
            foreach (object o in loadList)
            {
                Dictionary<string, object> dic = o as Dictionary<string, object>;
                CellAssociationKPIItem kpiItem = new CellAssociationKPIItem();
                kpiItem.BaseParams = dic;
                KPIList.Add(kpiItem);
            }
        }

        private void LoadExtraParams()
        {
            extraList.Clear();
            if (!File.Exists(xmlExtraFile))
            {
                return;
            }

            XmlConfigFile configFile = new XmlConfigFile(xmlExtraFile);
            List<object> loadList = configFile.GetItemValue("Config", "KPIList") as List<object>;
            foreach (object o in loadList)
            {
                Dictionary<string, object> dic = o as Dictionary<string, object>;
                CellAssociationKPIItem kpiItem = new CellAssociationKPIItem();
                kpiItem.ExtraParams = dic;
                extraList.Add(kpiItem);
            }
        }

        private void BindingExtraToBase()
        {
            Dictionary<string, CellAssociationKPIItem> baseDic = new Dictionary<string, CellAssociationKPIItem>();
            foreach (CellAssociationKPIItem baseItem in KPIList)
            {
                baseDic.Add(baseItem.Key, baseItem);
            }

            foreach (CellAssociationKPIItem extraItem in extraList)
            {
                if (baseDic.ContainsKey(extraItem.Key))
                {
                    baseDic[extraItem.Key].ExtraParams = extraItem.ExtraParams;
                }
            }
        }

        private void SaveBaseParams()
        {
            string dirPath = Path.GetDirectoryName(xmlBaseFile);
            if (!Directory.Exists(dirPath))
            {
                Directory.CreateDirectory(dirPath);
            }

            List<Dictionary<string, object>> saveList = new List<Dictionary<string, object>>();
            foreach (CellAssociationKPIItem kpiItem in KPIList)
            {
                saveList.Add(kpiItem.BaseParams);
            }

            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement config = configFile.AddConfig("Config");
            configFile.AddItem(config, "KPIList", saveList);
            configFile.Save(xmlBaseFile);
        }

        private void SaveExtraParams()
        {
            string dirPath = Path.GetDirectoryName(xmlExtraFile);
            if (!Directory.Exists(dirPath))
            {
                Directory.CreateDirectory(dirPath);
            }

            extraList.Clear();
            List<Dictionary<string, object>> saveList = new List<Dictionary<string, object>>();
            foreach (CellAssociationKPIItem kpiItem in KPIList)
            {
                if (kpiItem.HasExtra)
                {
                    saveList.Add(kpiItem.ExtraParams);
                    extraList.Add(kpiItem);
                }
            }

            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement config = configFile.AddConfig("Config");
            configFile.AddItem(config, "KPIList", saveList);
            configFile.Save(xmlExtraFile);
        }

        private CellAssociationKPIManager()
        {
            KPIList = new List<CellAssociationKPIItem>();
            Load();
        }

        private readonly List<CellAssociationKPIItem> extraList = new List<CellAssociationKPIItem>();
        private static CellAssociationKPIManager instance;
        private static string xmlBaseFile = Path.Combine(System.Windows.Forms.Application.StartupPath, "config/CellAssociationKPI/basekpi.xml");
        private static string xmlExtraFile = Path.Combine(System.Windows.Forms.Application.StartupPath, "config/CellAssociationKPI/extrakpi.xml");
    }

    public class CellAssociationKPIConfig
    {
        public static CellAssociationKPIConfig Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new CellAssociationKPIConfig();
                }
                return instance;
            }
        }

        public string ConnectionString
        {
            get
            {
                return string.Format("Data Source={0},{1};Initial Catalog={2};User Id={3};Password={4}",
                    Server, Port, Database, Username, Password);
            }
        }

        public string Server
        {
            get;
            set;
        }

        public int Port
        {
            get;
            set;
        }

        public string Username
        {
            get;
            set;
        }

        public string Password
        {
            get;
            set;
        }

        public string Database
        {
            get;
            set;
        }

        public Dictionary<string, object> Params
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("Server", Server);
                dic.Add("Port", Port);
                dic.Add("Database", Database);
                dic.Add("Username", Username);
                dic.Add("Password", Password);
                return dic;
            }
            set
            {
                if (value.ContainsKey("Server") && value["Server"] != null)
                {
                    Server = value["Server"].ToString();
                }
                if (value.ContainsKey("Port") && value["Port"] != null)
                {
                    int port = 0;
                    if (int.TryParse(value["Port"].ToString(), out port))
                    {
                        Port = port;
                    }
                }
                if (value.ContainsKey("Database") && value["Database"] != null)
                {
                    Database = value["Database"].ToString();
                }
                if (value.ContainsKey("Username") && value["Username"] != null)
                {
                    Username = value["Username"].ToString();
                }
                if (value.ContainsKey("Password") && value["Password"] != null)
                {
                    Password = value["Password"].ToString();
                }
            }
        }

        public void Load()
        {
            if (!File.Exists(xmlFilePath))
            {
                return;
            }

            XmlConfigFile configFile = new XmlConfigFile(xmlFilePath);
            object value = configFile.GetItemValue("Config", "Connection"); 
            if (value is Dictionary<string, object>)
            {
                Params = value as Dictionary<string, object>;
            }
        }

        public void Save()
        {
            string dirPath = Path.GetDirectoryName(xmlFilePath);
            if (!Directory.Exists(dirPath))
            {
                Directory.CreateDirectory(dirPath);
            }

            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement config = configFile.AddConfig("Config");
            configFile.AddItem(config, "Connection", Params);
            configFile.Save(xmlFilePath);
        }

        private static CellAssociationKPIConfig instance;
        private static string xmlFilePath = Path.Combine(System.Windows.Forms.Application.StartupPath, "config/CellAssociationKPI/Database.xml");

        private CellAssociationKPIConfig()
        {
            Load();
        }
    }

    public enum ECellAssociationKPITimeType
    {
        [EnumDescriptionAttribute("按小时")]
        ByHour = 3,

        [EnumDescriptionAttribute("按天")]
        ByDay = 4,
    }

    public enum ECellAssociationKPINetType
    {
        [EnumDescriptionAttribute("GSM")]
        GSM = 100,

        [EnumDescriptionAttribute("TD")]
        TD = 200,

        [EnumDescriptionAttribute("LTE")]
        LTE = 300,
    }

    public class CellAssociationKPIField
    {
        public string KpiKey
        {
            get;
            set;
        }

        public string KpiName
        {
            get;
            set;
        }

        public string ShowName
        {
            get;
            set;
        }

        public CellAssociationKPIField()
        {
            this.KpiName = "";
            this.ShowName = null;
        }

        public Dictionary<string, object> Params
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("KpiKey", KpiKey);
                dic.Add("KpiName", KpiName);
                if (ShowName != null)
                {
                    dic.Add("ShowName", ShowName);
                }
                return dic;
            }
            set
            {
                KpiKey = value["KpiKey"].ToString();
                KpiName = value["KpiName"].ToString();
                if (value.ContainsKey("ShowName") && value["ShowName"] != null)
                {
                    ShowName = value["ShowName"].ToString();
                }
            }
        }

        public override string ToString()
        {
            if (KpiName == null)
            {
                return "";
            }
            if (ShowName == null)
            {
                return KpiName;
            }
            return string.Format("{0}[{1}]", KpiName, ShowName);
        }
    }

    public class CellAssociationKPIReport
    {
        public CellAssociationKPIReport(string reportName)
        {
            FieldList = new List<CellAssociationKPIField>();
            this.ReportName = reportName;
        }

        public ECellAssociationKPITimeType TimeType
        {
            get;
            set;
        }

        public ECellAssociationKPINetType NetType
        {
            get;
            set;
        }

        public string ReportName
        {
            get;
            set;
        }

        public List<CellAssociationKPIField> FieldList
        {
            get;
            set;
        }

        public bool Save(string filePath)
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement config = configFile.AddConfig("Config");
                configFile.AddItem(config, "Report", this.Params);
                configFile.Save(filePath);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool Load(string filePath)
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(filePath);
                object o = configFile.GetItemValue("Config", "Report");
                this.Params = o as Dictionary<string, object>;
                return true;
            }
            catch
            {
                return false;
            }
        }

        public CellAssociationKPIField AddField(CellAssociationKPIItem kpiItem, out string errMsg)
        {
            errMsg = null;
            CellAssociationKPIField field = null;
            if (FieldList.Count == 0)
            {
                TimeType = kpiItem.TimeType;
                NetType = kpiItem.NetType;
                field = new CellAssociationKPIField();
                field.KpiName = kpiItem.Name;
                field.KpiKey = kpiItem.Key;
                FieldList.Add(field);
                return field;
            }
            else if (TimeType != kpiItem.TimeType || NetType != kpiItem.NetType)
            {
                errMsg = "添加的指标与已存在的指标所属时间类型或者网络类型不一致";
                return null;
            }
            for (int i = 0; i < FieldList.Count; ++i)
            {
                if (FieldList[i].KpiKey == kpiItem.Key)
                {
                    errMsg = "添加的指标在报表中已经存在";
                    return null;
                }
            }

            field = new CellAssociationKPIField();
            field.KpiName = kpiItem.Name;
            field.KpiKey = kpiItem.Key;
            FieldList.Add(field);
            return field;
        }

        public bool DelField(CellAssociationKPIField field)
        {
            if (FieldList.Contains(field))
            {
                FieldList.Remove(field);
            }
            return true;
        }

        public void UpField(CellAssociationKPIField field)
        {
            int curIndex = FieldList.IndexOf(field);
            if (curIndex < 1)
            {
                return;
            }

            FieldList.RemoveAt(curIndex);
            FieldList.Insert(curIndex - 1, field);
        }

        public void DownField(CellAssociationKPIField field)
        {
            int curIndex = FieldList.IndexOf(field);
            if (curIndex >= FieldList.Count - 1)
            {
                return;
            }

            FieldList.RemoveAt(curIndex);
            FieldList.Insert(curIndex + 1, field);
        }

        public Dictionary<string, object> Params
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("TimeType", (int)TimeType);
                dic.Add("NetType", (int)NetType);
                List<Dictionary<string, object>> saveList = new List<Dictionary<string, object>>();
                foreach (CellAssociationKPIField field in FieldList)
                {
                    saveList.Add(field.Params);
                }
                dic.Add("FieldList", saveList);
                return dic;
            }
            set
            {
                if (value.ContainsKey("TimeType") && value["TimeType"] != null)
                {
                    TimeType = (ECellAssociationKPITimeType)Enum.Parse(typeof(ECellAssociationKPITimeType), value["TimeType"].ToString());
                }
                if (value.ContainsKey("NetType") && value["NetType"] != null)
                {
                    NetType = (ECellAssociationKPINetType)Enum.Parse(typeof(ECellAssociationKPINetType), value["NetType"].ToString());
                }
                if (value.ContainsKey("FieldList") && value["FieldList"] != null)
                {
                    List<object> fieldList = value["FieldList"] as List<object>;
                    foreach (object o in fieldList)
                    {
                        CellAssociationKPIField field = new CellAssociationKPIField();
                        field.Params = o as Dictionary<string, object>;
                        FieldList.Add(field);
                    }
                }
            }
        }

        public override string ToString()
        {
            return ReportName;
        }
    }

    public class CellAssociationKPIReportManager
    {
        public static CellAssociationKPIReportManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new CellAssociationKPIReportManager();
                }
                return instance;
            }
        }

        public List<CellAssociationKPIReport> ReportList
        {
            get;
            private set;
        }

        public bool Save(CellAssociationKPIReport report)
        {
            string fileName = Path.Combine(directoryPath, report.ReportName + ".rpt");
            return report.Save(fileName);
        }

        public bool Delete(CellAssociationKPIReport report)
        {
            if (ReportList.IndexOf(report) == -1)
            {
                return true;
            }

            ReportList.Remove(report);
            string filePath = Path.Combine(directoryPath, report.ReportName + ".rpt");
            File.Delete(filePath);

            return true;
        }

        public CellAssociationKPIReport New(string reportName)
        {
            foreach (CellAssociationKPIReport report in ReportList)
            {
                if (report.ReportName == reportName)
                {
                    return report;
                }
            }
            
            CellAssociationKPIReport kpiReport = new CellAssociationKPIReport(reportName);
            ReportList.Add(kpiReport);
            return kpiReport;
        }

        private void Load()
        {
            if (!Directory.Exists(directoryPath))
            {
                return;
            }

            string[] files = Directory.GetFiles(directoryPath);
            foreach (string fName in files)
            {
                if (!fName.EndsWith(".rpt"))
                {
                    continue;
                }

                string filePath = Path.Combine(directoryPath, fName);
                string reportName = Path.GetFileNameWithoutExtension(filePath);
                CellAssociationKPIReport report = new CellAssociationKPIReport(reportName);
                if (!report.Load(filePath))
                {
                    continue;
                }
                ReportList.Add(report);
            }
        }

        private CellAssociationKPIReportManager()
        {
            ReportList = new List<CellAssociationKPIReport>();
            Load();
        }

        private static CellAssociationKPIReportManager instance;
        private static string directoryPath = Path.Combine(System.Windows.Forms.Application.StartupPath, "config/CellAssociationKPI/");
    }
}
