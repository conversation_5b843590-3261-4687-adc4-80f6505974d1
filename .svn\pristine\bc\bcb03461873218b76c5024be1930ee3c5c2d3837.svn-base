﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.src
{
    public class GridCellUnit : ColorUnit
    {
        public Dictionary<int, GridCell> GridCell { get; set; } = new Dictionary<int, GridCell>();

        public List<GridCellFile> GridCellUnitDoData(int sampleNum, float smax)
        {
            List<GridCellFile> listGridCellFile = new List<GridCellFile>();
            foreach (GridCell gridCell in GridCell.Values)
            {
                if (gridCell != null)
                {
                    listGridCellFile = gridCell.DoWithData(sampleNum, smax);
                    break;
                }
            }
            return listGridCellFile;
        }

        public void Initialize(GridCellData gridCellData)
        {
            GridCell gridCell = null;
            if (!GridCell.TryGetValue(gridCellData.Cell.ID, out gridCell))
            {
                gridCell = new GridCell();
                GridCell[gridCellData.Cell.ID] = gridCell;
            }
            gridCell.CellID = gridCellData.Cell.ID;
            gridCell.Initialize(gridCellData, this.LTLng, this.LTLat);
        }

    }

    public class GridCell
    {
        public int CellID { get; set; }
        public List<GridCellFile> GridCellFileList { get; set; } = new List<GridCellFile>();

        public void Initialize(GridCellData gridCellData , double ltLng , double ltLat)
        {
            GridCellFile gridCellFile = new GridCellFile();
            gridCellFile.Cell = gridCellData.Cell;
            gridCellFile.IFileID = gridCellData.IFileID;
            gridCellFile.GridCellRxlev = gridCellData.Rxlev;
            gridCellFile.GridSampleNum = gridCellData.SampleNum;
            gridCellFile.LtLongitude = ltLng;
            gridCellFile.LtLatitude = ltLat;
            GridCellFileList.Add(gridCellFile);
        }

        /// <summary>
        /// 逻辑判断是否符合参加计算的栅格
        /// </summary>
        /// <param name="sampleNum">采样点数</param>
        /// <param name="smax">波动系数</param>
        /// <returns></returns>
        public List<GridCellFile> DoWithData(int sampleNum, float Smax)
        {
            List<GridCellFile> listGridCellFile = new List<GridCellFile>();
            if (GridCellFileList.Count >= 3)
            {
                removeUnValidFile(sampleNum, listGridCellFile);
                if (GridCellFileList.Count >= 3)
                {
                    return AbnormalFile(GridCellFileList, Smax);
                }
                else
                {
                    return new List<GridCellFile>();
                }
            }
            else
            {
                return new List<GridCellFile>();
            }
        }

        private void removeUnValidFile(int sampleNum, List<GridCellFile> listGridCellFile)
        {
            foreach (GridCellFile gridCellFile in GridCellFileList)
            {
                if (gridCellFile.GridSampleNum < sampleNum || gridCellFile.GridCellRxlev > -60 || gridCellFile.GridCellRxlev < -120)
                {
                    listGridCellFile.Add(gridCellFile);//记录不符合条件的文件
                }
            }

            if (listGridCellFile.Count > 0)
            {
                foreach (GridCellFile gridCellFile in listGridCellFile)
                {
                    GridCellFileList.Remove(gridCellFile);//去除不符合条件的文件
                }
                listGridCellFile.Clear();
            }
        }

        /// <summary>
        /// 波动异常算法
        /// </summary>
        /// <param name="gridCellFileList">异常栅格信息列表</param>
        /// <param name="Smax"></param>
        /// <returns></returns>
        private List<GridCellFile> AbnormalFile(List<GridCellFile> gridCellFileList , float Smax)
        {
            float Ssum = 100;
            List<GridCellFile> listGridCellFile = new List<GridCellFile>();
            GridCellFile tmpGridCellFile = null;

            while (Ssum >= Smax)
            {
                float Rsum = 0, R_Ssum = 0, S_max = 0;
                foreach (GridCellFile gridCellFile in gridCellFileList) //计算Rxlev总值及记录最大值
                {
                    Rsum += gridCellFile.GridCellRxlev;
                }

                float R_avg = Rsum / gridCellFileList.Count;

                foreach (GridCellFile gridCellFile in gridCellFileList) //计算数据波动方差
                {
                    float tmpRxlev = (gridCellFile.GridCellRxlev - R_avg) * (gridCellFile.GridCellRxlev - R_avg);
                    if (S_max < tmpRxlev)
                    {
                        S_max = tmpRxlev;
                        tmpGridCellFile = gridCellFile;
                    }
                    R_Ssum += tmpRxlev;
                }
                Ssum = R_Ssum / gridCellFileList.Count;

                if (tmpGridCellFile != null)
                {
                    tmpGridCellFile.GridCellRxlevAvg = R_avg;
                    if (Ssum >= Smax)
                    {
                        listGridCellFile.Add(tmpGridCellFile);
                        gridCellFileList.Remove(tmpGridCellFile);
                    }
                }
                if (gridCellFileList.Count < 3)
                {
                    break;
                }
            }
            return listGridCellFile;
        }
    }

    public class GridCellFile
    {
        public float GridCellRxlev { get; set; }
        public float GridCellRxlevAvg { get; set; }
        public int GridSampleNum { get; set; }
        public int IFileID { get; set; }
        public Cell Cell { get; set; } = new Cell();
        public double LtLongitude { get; set; }
        public double LtLatitude { get; set; }
    }
}
