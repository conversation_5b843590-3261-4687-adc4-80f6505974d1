﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryProblemEvtInfoByRegion : QueryResultEventInfoByRegion
    {
        public QueryProblemEvtInfoByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "网络问题透视(按区域)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20006, "网络问题透视");
        }
        protected override void query()
        {
            Condition.FilterOffValue9 = false;
            //Condition.EventIDs = eventChooser.SelectEvents;
            if (Condition.EventIDs.Count > 0)
            {
                ClientProxy clientProxy = new ClientProxy();
                try
                {
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        return;
                    }
                    WaitBox.Show("开始查询数据...", queryInThread, clientProxy);
                }
                finally
                {
                    clientProxy.Close();
                }
            }
        }
    }
}
