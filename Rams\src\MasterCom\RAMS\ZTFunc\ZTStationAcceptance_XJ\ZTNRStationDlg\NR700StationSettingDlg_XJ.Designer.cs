﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NR700StationSettingDlg_XJ
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label25 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.txtStationBtwSwitchRate = new System.Windows.Forms.NumericUpDown();
            this.label28 = new System.Windows.Forms.Label();
            this.txtStationInSwitch = new System.Windows.Forms.NumericUpDown();
            this.label24 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.txtSmallPing = new System.Windows.Forms.NumericUpDown();
            this.label17 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.txtCallSuccessRate = new System.Windows.Forms.NumericUpDown();
            this.txtFRSuccessRate4G = new System.Windows.Forms.NumericUpDown();
            this.label21 = new System.Windows.Forms.Label();
            this.txtFRSuccessRate = new System.Windows.Forms.NumericUpDown();
            this.label19 = new System.Windows.Forms.Label();
            this.txtCallRate4G = new System.Windows.Forms.NumericUpDown();
            this.label18 = new System.Windows.Forms.Label();
            this.txtUploadThrought = new System.Windows.Forms.NumericUpDown();
            this.label15 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.txtUploadAvgSINR = new System.Windows.Forms.NumericUpDown();
            this.label13 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.txtUploadRSRP = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.txtDownThrought = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.txtDownAvgSINR = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.txtBigPing = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.txtAccessRate = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.txtDownRsrp = new System.Windows.Forms.NumericUpDown();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtStationBtwSwitchRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStationInSwitch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSmallPing)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCallSuccessRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFRSuccessRate4G)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFRSuccessRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCallRate4G)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUploadThrought)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUploadAvgSINR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUploadRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDownThrought)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDownAvgSINR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBigPing)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDownRsrp)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(615, 593);
            this.btnOK.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(107, 36);
            this.btnOK.TabIndex = 5;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(763, 593);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(107, 36);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "取消";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label25);
            this.groupBox3.Controls.Add(this.label29);
            this.groupBox3.Controls.Add(this.label27);
            this.groupBox3.Controls.Add(this.label30);
            this.groupBox3.Controls.Add(this.txtStationBtwSwitchRate);
            this.groupBox3.Controls.Add(this.label28);
            this.groupBox3.Controls.Add(this.txtStationInSwitch);
            this.groupBox3.Controls.Add(this.label24);
            this.groupBox3.Controls.Add(this.label22);
            this.groupBox3.Controls.Add(this.label20);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.label11);
            this.groupBox3.Controls.Add(this.txtSmallPing);
            this.groupBox3.Controls.Add(this.label17);
            this.groupBox3.Controls.Add(this.label23);
            this.groupBox3.Controls.Add(this.label26);
            this.groupBox3.Controls.Add(this.txtCallSuccessRate);
            this.groupBox3.Controls.Add(this.txtFRSuccessRate4G);
            this.groupBox3.Controls.Add(this.label21);
            this.groupBox3.Controls.Add(this.txtFRSuccessRate);
            this.groupBox3.Controls.Add(this.label19);
            this.groupBox3.Controls.Add(this.txtCallRate4G);
            this.groupBox3.Controls.Add(this.label18);
            this.groupBox3.Controls.Add(this.txtUploadThrought);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.label16);
            this.groupBox3.Controls.Add(this.txtUploadAvgSINR);
            this.groupBox3.Controls.Add(this.label13);
            this.groupBox3.Controls.Add(this.label14);
            this.groupBox3.Controls.Add(this.txtUploadRSRP);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.txtDownThrought);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.label5);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.txtDownAvgSINR);
            this.groupBox3.Controls.Add(this.label2);
            this.groupBox3.Controls.Add(this.label1);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Controls.Add(this.txtBigPing);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.txtAccessRate);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.txtDownRsrp);
            this.groupBox3.Location = new System.Drawing.Point(34, 6);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox3.Size = new System.Drawing.Size(836, 562);
            this.groupBox3.TabIndex = 64;
            this.groupBox3.TabStop = false;
            this.groupBox3.Enter += new System.EventHandler(this.groupBox3_Enter);
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(762, 323);
            this.label25.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(17, 18);
            this.label25.TabIndex = 168;
            this.label25.Text = "%";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(331, 500);
            this.label29.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(17, 18);
            this.label29.TabIndex = 167;
            this.label29.Text = "%";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(763, 438);
            this.label27.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(17, 18);
            this.label27.TabIndex = 166;
            this.label27.Text = "%";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(101, 504);
            this.label30.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(98, 18);
            this.label30.TabIndex = 165;
            this.label30.Text = "站间切换≥";
            // 
            // txtStationBtwSwitchRate
            // 
            this.txtStationBtwSwitchRate.Location = new System.Drawing.Point(213, 498);
            this.txtStationBtwSwitchRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtStationBtwSwitchRate.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtStationBtwSwitchRate.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtStationBtwSwitchRate.Name = "txtStationBtwSwitchRate";
            this.txtStationBtwSwitchRate.Size = new System.Drawing.Size(114, 28);
            this.txtStationBtwSwitchRate.TabIndex = 164;
            this.txtStationBtwSwitchRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(534, 440);
            this.label28.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(98, 18);
            this.label28.TabIndex = 162;
            this.label28.Text = "站内切换≥";
            // 
            // txtStationInSwitch
            // 
            this.txtStationInSwitch.Location = new System.Drawing.Point(641, 434);
            this.txtStationInSwitch.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtStationInSwitch.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtStationInSwitch.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtStationInSwitch.Name = "txtStationInSwitch";
            this.txtStationInSwitch.Size = new System.Drawing.Size(114, 28);
            this.txtStationInSwitch.TabIndex = 161;
            this.txtStationInSwitch.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(14, 440);
            this.label24.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(188, 18);
            this.label24.TabIndex = 160;
            this.label24.Text = "FR 成功率（5G-4G）≥";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(444, 387);
            this.label22.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(188, 18);
            this.label22.TabIndex = 159;
            this.label22.Text = "FR 成功率（5G-5G）≥";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(5, 385);
            this.label20.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(197, 18);
            this.label20.TabIndex = 158;
            this.label20.Text = "呼叫成功率（5G-4G）≥";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(335, 131);
            this.label6.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(26, 18);
            this.label6.TabIndex = 157;
            this.label6.Text = "ms";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(71, 131);
            this.label11.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(134, 18);
            this.label11.TabIndex = 156;
            this.label11.Text = "Ping小包时延≥";
            // 
            // txtSmallPing
            // 
            this.txtSmallPing.Location = new System.Drawing.Point(213, 127);
            this.txtSmallPing.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSmallPing.Maximum = new decimal(new int[] {
            20000,
            0,
            0,
            0});
            this.txtSmallPing.Minimum = new decimal(new int[] {
            20000,
            0,
            0,
            -2147483648});
            this.txtSmallPing.Name = "txtSmallPing";
            this.txtSmallPing.Size = new System.Drawing.Size(114, 28);
            this.txtSmallPing.TabIndex = 155;
            this.txtSmallPing.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(335, 323);
            this.label17.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(44, 18);
            this.label17.TabIndex = 154;
            this.label17.Text = "Mbps";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(336, 440);
            this.label23.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(17, 18);
            this.label23.TabIndex = 153;
            this.label23.Text = "%";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(434, 325);
            this.label26.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(197, 18);
            this.label26.TabIndex = 130;
            this.label26.Text = "呼叫成功率（5G-5G）≥";
            // 
            // txtCallSuccessRate
            // 
            this.txtCallSuccessRate.Location = new System.Drawing.Point(641, 321);
            this.txtCallSuccessRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtCallSuccessRate.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtCallSuccessRate.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtCallSuccessRate.Name = "txtCallSuccessRate";
            this.txtCallSuccessRate.Size = new System.Drawing.Size(114, 28);
            this.txtCallSuccessRate.TabIndex = 129;
            this.txtCallSuccessRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // txtFRSuccessRate4G
            // 
            this.txtFRSuccessRate4G.Location = new System.Drawing.Point(214, 436);
            this.txtFRSuccessRate4G.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFRSuccessRate4G.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtFRSuccessRate4G.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtFRSuccessRate4G.Name = "txtFRSuccessRate4G";
            this.txtFRSuccessRate4G.Size = new System.Drawing.Size(114, 28);
            this.txtFRSuccessRate4G.TabIndex = 151;
            this.txtFRSuccessRate4G.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(762, 387);
            this.label21.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(17, 18);
            this.label21.TabIndex = 150;
            this.label21.Text = "%";
            // 
            // txtFRSuccessRate
            // 
            this.txtFRSuccessRate.Location = new System.Drawing.Point(640, 379);
            this.txtFRSuccessRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFRSuccessRate.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtFRSuccessRate.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtFRSuccessRate.Name = "txtFRSuccessRate";
            this.txtFRSuccessRate.Size = new System.Drawing.Size(114, 28);
            this.txtFRSuccessRate.TabIndex = 148;
            this.txtFRSuccessRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(334, 383);
            this.label19.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(17, 18);
            this.label19.TabIndex = 147;
            this.label19.Text = "%";
            // 
            // txtCallRate4G
            // 
            this.txtCallRate4G.Location = new System.Drawing.Point(212, 381);
            this.txtCallRate4G.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtCallRate4G.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtCallRate4G.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtCallRate4G.Name = "txtCallRate4G";
            this.txtCallRate4G.Size = new System.Drawing.Size(114, 28);
            this.txtCallRate4G.TabIndex = 145;
            this.txtCallRate4G.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(89, 329);
            this.label18.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(116, 18);
            this.label18.TabIndex = 143;
            this.label18.Text = "上行吞吐量≥";
            // 
            // txtUploadThrought
            // 
            this.txtUploadThrought.Location = new System.Drawing.Point(213, 319);
            this.txtUploadThrought.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtUploadThrought.Maximum = new decimal(new int[] {
            20000,
            0,
            0,
            0});
            this.txtUploadThrought.Minimum = new decimal(new int[] {
            20000,
            0,
            0,
            -2147483648});
            this.txtUploadThrought.Name = "txtUploadThrought";
            this.txtUploadThrought.Size = new System.Drawing.Size(114, 28);
            this.txtUploadThrought.TabIndex = 142;
            this.txtUploadThrought.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(763, 260);
            this.label15.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(26, 18);
            this.label15.TabIndex = 141;
            this.label15.Text = "dB";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(463, 260);
            this.label16.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(170, 18);
            this.label16.TabIndex = 140;
            this.label16.Text = "上传Average SINR≥";
            // 
            // txtUploadAvgSINR
            // 
            this.txtUploadAvgSINR.Location = new System.Drawing.Point(641, 257);
            this.txtUploadAvgSINR.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtUploadAvgSINR.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtUploadAvgSINR.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtUploadAvgSINR.Name = "txtUploadAvgSINR";
            this.txtUploadAvgSINR.Size = new System.Drawing.Size(114, 28);
            this.txtUploadAvgSINR.TabIndex = 139;
            this.txtUploadAvgSINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(332, 264);
            this.label13.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(35, 18);
            this.label13.TabIndex = 138;
            this.label13.Text = "dBm";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(104, 264);
            this.label14.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(98, 18);
            this.label14.TabIndex = 137;
            this.label14.Text = "上传RSRP≥";
            // 
            // txtUploadRSRP
            // 
            this.txtUploadRSRP.Location = new System.Drawing.Point(210, 257);
            this.txtUploadRSRP.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtUploadRSRP.Name = "txtUploadRSRP";
            this.txtUploadRSRP.Size = new System.Drawing.Size(114, 28);
            this.txtUploadRSRP.TabIndex = 136;
            this.txtUploadRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(763, 195);
            this.label9.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(44, 18);
            this.label9.TabIndex = 135;
            this.label9.Text = "Mbps";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(516, 195);
            this.label12.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(116, 18);
            this.label12.TabIndex = 134;
            this.label12.Text = "下行吞吐量≥";
            // 
            // txtDownThrought
            // 
            this.txtDownThrought.Location = new System.Drawing.Point(640, 189);
            this.txtDownThrought.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDownThrought.Name = "txtDownThrought";
            this.txtDownThrought.Size = new System.Drawing.Size(114, 28);
            this.txtDownThrought.TabIndex = 133;
            this.txtDownThrought.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(763, 123);
            this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(35, 18);
            this.label7.TabIndex = 132;
            this.label7.Text = "dBm";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(331, 199);
            this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(26, 18);
            this.label5.TabIndex = 131;
            this.label5.Text = "dB";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(-1, 203);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(206, 18);
            this.label4.TabIndex = 130;
            this.label4.Text = "下载测试Average SINR≥";
            // 
            // txtDownAvgSINR
            // 
            this.txtDownAvgSINR.Location = new System.Drawing.Point(213, 193);
            this.txtDownAvgSINR.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDownAvgSINR.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtDownAvgSINR.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtDownAvgSINR.Name = "txtDownAvgSINR";
            this.txtDownAvgSINR.Size = new System.Drawing.Size(114, 28);
            this.txtDownAvgSINR.TabIndex = 129;
            this.txtDownAvgSINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(763, 64);
            this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(26, 18);
            this.label2.TabIndex = 128;
            this.label2.Text = "ms";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(498, 64);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(134, 18);
            this.label1.TabIndex = 125;
            this.label1.Text = "Ping大包时延≥";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(8, 64);
            this.label10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(197, 18);
            this.label10.TabIndex = 124;
            this.label10.Text = "Access Success Rate≥";
            // 
            // txtBigPing
            // 
            this.txtBigPing.Location = new System.Drawing.Point(641, 60);
            this.txtBigPing.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtBigPing.Maximum = new decimal(new int[] {
            20000,
            0,
            0,
            0});
            this.txtBigPing.Minimum = new decimal(new int[] {
            20000,
            0,
            0,
            -2147483648});
            this.txtBigPing.Name = "txtBigPing";
            this.txtBigPing.Size = new System.Drawing.Size(114, 28);
            this.txtBigPing.TabIndex = 119;
            this.txtBigPing.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(499, 127);
            this.label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(134, 18);
            this.label8.TabIndex = 121;
            this.label8.Text = "下载测试RSRP≥";
            // 
            // txtAccessRate
            // 
            this.txtAccessRate.Location = new System.Drawing.Point(213, 62);
            this.txtAccessRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAccessRate.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtAccessRate.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtAccessRate.Name = "txtAccessRate";
            this.txtAccessRate.Size = new System.Drawing.Size(114, 28);
            this.txtAccessRate.TabIndex = 118;
            this.txtAccessRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(340, 64);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 18);
            this.label3.TabIndex = 122;
            this.label3.Text = "%";
            // 
            // txtDownRsrp
            // 
            this.txtDownRsrp.Location = new System.Drawing.Point(641, 119);
            this.txtDownRsrp.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDownRsrp.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtDownRsrp.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtDownRsrp.Name = "txtDownRsrp";
            this.txtDownRsrp.Size = new System.Drawing.Size(114, 28);
            this.txtDownRsrp.TabIndex = 120;
            this.txtDownRsrp.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // NR700StationSettingDlg_XJ
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 22F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(898, 656);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Margin = new System.Windows.Forms.Padding(10, 14, 10, 14);
            this.Name = "NR700StationSettingDlg_XJ";
            this.Text = "NR700M单验门限设置";
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtStationBtwSwitchRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStationInSwitch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSmallPing)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCallSuccessRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFRSuccessRate4G)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFRSuccessRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCallRate4G)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUploadThrought)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUploadAvgSINR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUploadRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDownThrought)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDownAvgSINR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBigPing)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDownRsrp)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.NumericUpDown txtFRSuccessRate4G;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.NumericUpDown txtFRSuccessRate;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.NumericUpDown txtCallRate4G;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.NumericUpDown txtUploadThrought;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.NumericUpDown txtUploadAvgSINR;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.NumericUpDown txtUploadRSRP;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.NumericUpDown txtDownThrought;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown txtDownAvgSINR;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.NumericUpDown txtBigPing;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown txtAccessRate;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown txtDownRsrp;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.NumericUpDown txtCallSuccessRate;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown txtSmallPing;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.NumericUpDown txtStationBtwSwitchRate;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.NumericUpDown txtStationInSwitch;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label25;
    }
}