﻿using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.Func
{
    public class MapTDCellLayer : LayerBase, IKMLExport
    {
        public static bool DrawCurrent { get; set; } = true;
        public static DateTime CurShowTimeAt { get; set; } = DateTime.Now;

        private readonly Pen penCellOutLine = Pens.LightSteelBlue;
        static MapTDCellLayer()
        {
            creatGraphicsPath();
        }

        protected static void creatGraphicsPath()
        {
            float radius = 4;
            cellPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 40;
            float x1 = 0.75f;
            float y1 = 0.14f;
            float x2 = 0.85f;
            float y2 = 0.15f;
            float x3 = 0.92f;
            float y3 = 0.14f;
            float x4 = 0.96f;
            float y4 = 0.12f;
            float x5 = 0.99f;
            float y5 = 0.07f;
            cellPoints.Add(new PointF[] 
                {
                    new PointF(0, 0), 
                    new PointF(radius * x1, -radius * y1), 
                    new PointF(radius * x2, -radius * y2), 
                    new PointF(radius * x3, -radius * y3), 
                    new PointF(radius * x4, -radius * y4), 
                    new PointF(radius * x5, -radius * y5), 
                    new PointF(radius, 0),
                    new PointF(radius * x5, radius * y5), 
                    new PointF(radius * x4, radius * y4), 
                    new PointF(radius * x3, radius * y3), 
                    new PointF(radius * x2, radius * y2), 
                    new PointF(radius * x1, radius * y1)
                });
            radius = 2;
            cellPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 22;
            x1 = 0.75f;
            y1 = 0.29f;
            x2 = 0.85f;
            y2 = 0.30f;
            x3 = 0.92f;
            y3 = 0.28f;
            x4 = 0.96f;
            y4 = 0.24f;
            x5 = 0.99f;
            y5 = 0.14f;
            cellPoints.Add(new PointF[] 
                {
                    new PointF(0, 0), 
                    new PointF(radius * x1, -radius * y1), 
                    new PointF(radius * x2, -radius * y2), 
                    new PointF(radius * x3, -radius * y3), 
                    new PointF(radius * x4, -radius * y4), 
                    new PointF(radius * x5, -radius * y5), 
                    new PointF(radius, 0),
                    new PointF(radius * x5, radius * y5), 
                    new PointF(radius * x4, radius * y4), 
                    new PointF(radius * x3, radius * y3), 
                    new PointF(radius * x2, radius * y2), 
                    new PointF(radius * x1, radius * y1)
                });
            GraphicsPath path = new GraphicsPath();
            path.AddEllipse(cellPoints[0][0].X, cellPoints[0][0].Y, cellPoints[0][2].X * 2, cellPoints[0][2].Y * 2);
            cellPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(cellPoints[1]);
            cellPaths.Add(path);
            path = new GraphicsPath();
            path.AddEllipse(cellPoints[2][0].X, cellPoints[2][0].Y, cellPoints[2][2].X * 2, cellPoints[2][2].Y * 2);
            cellPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(cellPoints[3]);
            cellPaths.Add(path);
            foreach (GraphicsPath pathTemp in cellPaths)
            {
                PathGradientBrush pathGradientBrush = new PathGradientBrush(pathTemp);
                pathGradientBrush.CenterPoint = new PointF(0, 0);
                cellPathGradientBrushs.Add(pathGradientBrush);
            }
            radius = 4;
            antennaPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 40;
            antennaPoints.Add(new PointF[] 
                {
                    new PointF(0, -1), 
                    new PointF(radius - 3, -1), 
                    new PointF(radius - 5, -5), 
                    new PointF(radius, 0), 
                    new PointF(radius - 5, 5), 
                    new PointF(radius - 3, 1), 
                    new PointF(0, 1), 
                });
            radius = 2;
            antennaPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 22;
            antennaPoints.Add(new PointF[] 
                {
                    new PointF(0, -3), 
                    new PointF(radius - 6, -3), 
                    new PointF(radius - 6, -6), 
                    new PointF(radius, 0), 
                    new PointF(radius - 6, 6), 
                    new PointF(radius - 6, 3), 
                    new PointF(0, 3), 
                });
            path = new GraphicsPath();
            path.AddEllipse(antennaPoints[0][0].X, antennaPoints[0][0].Y, antennaPoints[0][2].X * 2, antennaPoints[0][2].Y * 2);
            antennaPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaPoints[1]);
            antennaPaths.Add(path);
            path = new GraphicsPath();
            path.AddEllipse(antennaPoints[2][0].X, antennaPoints[2][0].Y, antennaPoints[2][2].X * 2, antennaPoints[2][2].Y * 2);
            antennaPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaPoints[3]);
            antennaPaths.Add(path);

            double radiusGE = 0.00004;
            int part = 36;//将一个圆分为几份
            antennaGEPoints = GEdrawCircle(radiusGE, part, 0);

            radiusGE = 0.00036;
            antennaGEPoints.Add(new LayerPoint(0, -0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00003, -0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00005, -0.00005, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE, 0, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00005, 0.00005, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00003, 0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(0, 0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(0, -0.00001, 1));

            radiusGE = 0.00002;
            part = 36;
            antennaGEPoints.AddRange(GEdrawCircle(radiusGE, part, 2));

            radiusGE = 0.00022;
            antennaGEPoints.Add(new LayerPoint(0, -0.00003, 3));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00006, -0.00003, 3));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00006, -0.00006, 3));
            antennaGEPoints.Add(new LayerPoint(radiusGE, 0, 3));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00006, 0.00006, 3));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00006, 0.00003, 3));
            antennaGEPoints.Add(new LayerPoint(0, 0.00003, 3));
            antennaGEPoints.Add(new LayerPoint(0, -0.00003, 3));
        }

        private static List<PointF[]> cellHighlightPoints = new List<PointF[]>();
        public List<PointF[]> CellHighlightPoints
        {
            get { return cellHighlightPoints; }
        }

        private static List<GraphicsPath> cellHighlightPaths = new List<GraphicsPath>();
        public List<GraphicsPath> CellHighlightPaths
        {
            get { return cellHighlightPaths; }
        }

        private static List<PathGradientBrush> cellHighlightPathGradientBrushs = new List<PathGradientBrush>();
        public List<PathGradientBrush> CellHighlightPathGradientBrushs
        {
            get { return cellHighlightPathGradientBrushs; }
        }

        private static List<PointF[]> antennaHighlightPoints = new List<PointF[]>();
        public List<PointF[]> AntennaHighlightPoints
        {
            get { return antennaHighlightPoints; }
        }
        
        public static List<LayerPoint> AntennaGEHighlightPoints { get; private set; } = new List<LayerPoint>();

        private static List<GraphicsPath> antennaHighlightPaths = new List<GraphicsPath>();
        public List<GraphicsPath> AntennaHighlightPaths
        {
            get { return antennaHighlightPaths; }
        }

        public void InitCellPath()
        {
            cellHighlightPoints.Clear();
            cellHighlightPaths.Clear();
            cellHighlightPathGradientBrushs.Clear();
            antennaHighlightPoints.Clear();
            AntennaGEHighlightPoints.Clear();
            antennaHighlightPaths.Clear();
            float radius = 3 * CD.CellLengthRadio_TD;
            cellHighlightPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 40 * CD.CellLengthRadio_TD;
            float x1 = 0.75f;
            float y1 = 0.14f;
            float x2 = 0.85f;
            float y2 = 0.15f;
            float x3 = 0.92f;
            float y3 = 0.14f;
            float x4 = 0.96f;
            float y4 = 0.12f;
            float x5 = 0.99f;
            float y5 = 0.07f;
            cellHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, 0), 
                    new PointF(radius * x1, -radius * y1), 
                    new PointF(radius * x2, -radius * y2), 
                    new PointF(radius * x3, -radius * y3), 
                    new PointF(radius * x4, -radius * y4), 
                    new PointF(radius * x5, -radius * y5), 
                    new PointF(radius, 0),
                    new PointF(radius * x5, radius * y5), 
                    new PointF(radius * x4, radius * y4), 
                    new PointF(radius * x3, radius * y3), 
                    new PointF(radius * x2, radius * y2), 
                    new PointF(radius * x1, radius * y1)
                });
            radius = 2 * CD.CellLengthRadio_TD;
            cellHighlightPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 22 * CD.CellLengthRadio_TD;
            x1 = 0.75f;
            y1 = 0.29f;
            x2 = 0.85f;
            y2 = 0.30f;
            x3 = 0.92f;
            y3 = 0.28f;
            x4 = 0.96f;
            y4 = 0.24f;
            x5 = 0.99f;
            y5 = 0.14f;
            cellHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, 0), 
                    new PointF(radius * x1, -radius * y1), 
                    new PointF(radius * x2, -radius * y2), 
                    new PointF(radius * x3, -radius * y3), 
                    new PointF(radius * x4, -radius * y4), 
                    new PointF(radius * x5, -radius * y5), 
                    new PointF(radius, 0),
                    new PointF(radius * x5, radius * y5), 
                    new PointF(radius * x4, radius * y4), 
                    new PointF(radius * x3, radius * y3), 
                    new PointF(radius * x2, radius * y2), 
                    new PointF(radius * x1, radius * y1)
                });
            GraphicsPath path = new GraphicsPath();
            path.AddEllipse(cellHighlightPoints[0][0].X, cellHighlightPoints[0][0].Y, cellHighlightPoints[0][2].X * 2, cellHighlightPoints[0][2].Y * 2);
            cellHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(cellHighlightPoints[1]);
            cellHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddEllipse(cellHighlightPoints[2][0].X, cellHighlightPoints[2][0].Y, cellHighlightPoints[2][2].X * 2, cellHighlightPoints[2][2].Y * 2);
            cellHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(cellHighlightPoints[3]);
            cellHighlightPaths.Add(path);
            foreach (GraphicsPath pathTemp in cellHighlightPaths)
            {
                PathGradientBrush pathGradientBrush = new PathGradientBrush(pathTemp);
                pathGradientBrush.CenterPoint = new PointF(0, 0);
                cellHighlightPathGradientBrushs.Add(pathGradientBrush);
            }

            radius = 4 * CD.CellLengthRadio_TD;
            antennaHighlightPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 40 * CD.CellLengthRadio_TD;
            antennaHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, -1), 
                    new PointF(radius - 3, -1), 
                    new PointF(radius - 5, -5), 
                    new PointF(radius, 0), 
                    new PointF(radius - 5, 5), 
                    new PointF(radius - 3, 1), 
                    new PointF(0, 1), 
                });
            radius = 2 * CD.CellLengthRadio_TD;
            antennaHighlightPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 22 * CD.CellLengthRadio_TD;
            antennaHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, -3), 
                    new PointF(radius - 6, -3), 
                    new PointF(radius - 6, -6), 
                    new PointF(radius, 0), 
                    new PointF(radius - 6, 6), 
                    new PointF(radius - 6, 3), 
                    new PointF(0, 3), 
                });
            path = new GraphicsPath();
            path.AddEllipse(antennaHighlightPoints[0][0].X, antennaHighlightPoints[0][0].Y, antennaHighlightPoints[0][2].X * 2, antennaHighlightPoints[0][2].Y * 2);
            antennaHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaHighlightPoints[1]);
            antennaHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddEllipse(antennaHighlightPoints[2][0].X, antennaHighlightPoints[2][0].Y, antennaHighlightPoints[2][2].X * 2, antennaHighlightPoints[2][2].Y * 2);
            antennaHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaHighlightPoints[3]);
            antennaHighlightPaths.Add(path);

            double radiusGE = 0.00004 * CD.CellLengthRadio_TD;
            int part = 36;//将一个圆分为几份
            AntennaGEHighlightPoints = GEdrawCircle(radiusGE, part, 0);

            radiusGE = 0.00036 * CD.CellLengthRadio_TD;
            AntennaGEHighlightPoints.Add(new LayerPoint(0, -0.00001, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00003, -0.00001, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00005, -0.00005, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE, 0, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00005, 0.00005, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00003, 0.00001, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(0, 0.00001, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(0, -0.00001, 1));

            radiusGE = 0.00002 * CD.CellLengthRadio_TD;
            part = 36;
            AntennaGEHighlightPoints.AddRange(GEdrawCircle(radiusGE, part, 2));

            radiusGE = 0.00022 * CD.CellLengthRadio_TD;
            AntennaGEHighlightPoints.Add(new LayerPoint(0, -0.00003, 3));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00006, -0.00003, 3));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00006, -0.00006, 3));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE, 0, 3));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00006, 0.00006, 3));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00006, 0.00003, 3));
            AntennaGEHighlightPoints.Add(new LayerPoint(0, 0.00003, 3));
            AntennaGEHighlightPoints.Add(new LayerPoint(0, -0.00003, 3));
        }

        public MapTDCellLayer(string name)
            : base(name)
        {
            this.VisibleScaleEnabled = true;
            this.VisibleScale = new VisibleScale(0, 80000);
#if Beijing
            ColorCell = Color.GreenYellow;
#endif
        }

        public override MainModel MainModel
        {
            get { return mainModel; }
            set
            {
                mainModel = value;
                mainModel.ServerCellsChanged += serverCellsChanged;
                serverCellsChanged(null, null);
            }
        }

        private List<TDCell> cellsInCurrentView = new List<TDCell>();
        public List<TDCell> CellsInCurrentView
        {
            get { return cellsInCurrentView; }
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            updateRect.Inflate((int)(40 * 10000 / mapScale), (int)(40 * 10000 / mapScale));
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            cellsInCurrentView = null;
            if (DrawCell)
            {
                cellsInCurrentView = new List<TDCell>();
                List<TDCell> cells = null;
                if (DrawCurrent)
                {
                    cells = mainModel.CellManager.GetCurrentTDCells();
                }
                else
                {
                    cells = mainModel.CellManager.GetTDCells(CurShowTimeAt);
                }

                if (cells != null)
                {
                    foreach (TDCell cell in cells)
                    {
                        if (cell.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((cell.Type == TDNodeBType.Outdoor && DrawOutdoor) || (cell.Type == TDNodeBType.Indoor && DrawIndoor)))
                        {
                            cellsInCurrentView.Add(cell);
                            paintCell(cell, graphics, mapScale);
                        }
                    }
                }
                if (DrawServer && (curMapType == LayerMapType.Google || curMapType == LayerMapType.MTGis &&
                    mainModel.ServerTDCells != null && mainModel.ServerTDCells.Count > 0))
                {
                    cells = mainModel.ServerTDCells;
                    foreach (TDCell cell in cells)
                    {
                        if (cell.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((cell.Type == TDNodeBType.Outdoor && DrawOutdoor) || (cell.Type == TDNodeBType.Indoor && DrawIndoor)))
                        {
                            paintCell(cell, graphics, mapScale);
                        }
                    }
                }
            }
            if (DrawSelTDCellPair && curMapType == LayerMapType.MTGis)
            {
                if (mainModel.SelTDCellPairDic.Count > 0)
                {
                    Dictionary<TDCell, List<TestPoint>> selTDCellPairDic = mainModel.SelTDCellPairDic;
                    int i = 0;
                    foreach (TDCell cell in selTDCellPairDic.Keys)
                    {
                        if (i == 0)  //主要的小区设为蓝色
                        {
                            //Brush brush = new SolidBrush(Color.RoyalBlue);
                            paintCellPair(cell, selTDCellPairDic[cell], Color.RoyalBlue, graphics, mapScale);
                        }
                        else  //其它小区设为红色
                        {
                            //Brush brush = new SolidBrush(Color.Red);
                            paintCellPair(cell, selTDCellPairDic[cell], Color.Red, graphics, mapScale);
                        }
                        i++;
                    }
                }
                else if (mainModel.SelDCellPairDic.Count > 0)
                {
                    Dictionary<ZTDIYNonconformity.DefineCell, List<TestPoint>> selDCellPairDic = mainModel.SelDCellPairDic;
                    int i = 0;
                    foreach (ZTDIYNonconformity.DefineCell cell in selDCellPairDic.Keys)
                    {
                        if (i == 0)  //主要的小区设为蓝色
                        {
                            //Brush brush = new SolidBrush(Color.RoyalBlue);
                            paintCellPair(cell, selDCellPairDic[cell], Color.RoyalBlue, graphics, mapScale);
                        }
                        else  //其它小区设为红色
                        {
                            //Brush brush = new SolidBrush(Color.Red);
                            paintCellPair(cell, selDCellPairDic[cell], Color.Red, graphics, mapScale);
                        }
                        i++;
                    }
                }
            }
            if (DrawNonconformityCell && curMapType == LayerMapType.MTGis)
            {
                Brush brush = new SolidBrush(Color.Red);
                foreach (string cellName in HighlightCellList)
                {
                    TDCell tdcell = CellManager.GetInstance().GetTDCellByName(cellName);
                    if (tdcell != null)
                    {
                        paintTDScanNonconformityCell(tdcell, graphics, mapScale, brush);
                    }
                }
            }

            if (DrawAntenna)
            {
                List<TDAntenna> antennas = null;
                if (DrawCurrent)
                {
                    antennas = mainModel.CellManager.GetCurrentTDAntennas();
                }
                else
                {
                    antennas = mainModel.CellManager.GetTDAntennas(CurShowTimeAt);
                }
                if (antennas != null)
                {
                    foreach (TDAntenna antenna in antennas)
                    {
                        if (!MainModel.SystemConfigInfo.isDisplayHisCell && curMapType == LayerMapType.MTGis
                            && !antenna.ValidPeriod.Contains(DateTime.Now))
                        {
                            continue;
                        }

                        if (antenna.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((antenna.Type == TDNodeBType.Outdoor && DrawOutdoor) || (antenna.Type == TDNodeBType.Indoor && DrawIndoor)))
                        {
                            paintAntenna(antenna, graphics, mapScale);
                        }
                    }
                }
                List<TDCell> cells = null;
                if (DrawServer)
                {
                    cells = mainModel.ServerTDCells;
                }
                if (cells != null)
                {
                    foreach (TDCell cell in cells)
                    {
                        TDAntenna antenna = cell.Antenna;
                        if (antenna != null && antenna.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((cell.Type == TDNodeBType.Outdoor && DrawOutdoor) || (cell.Type == TDNodeBType.Indoor && DrawIndoor)))
                        {
                            paintAntenna(antenna, graphics, mapScale);
                        }
                    }
                }
            }
            if (DrawBTS)
            {
                List<TDNodeB> btss = null;
                if (DrawCurrent)
                {
                    btss = mainModel.CellManager.GetCurrentTDBTSs();
                }
                else
                {
                    btss = mainModel.CellManager.GetTDBTSs(CurShowTimeAt);
                }
                if (btss != null)
                {
                    foreach (TDNodeB bts in btss)
                    {
                        if (bts.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((bts.Type == TDNodeBType.Outdoor && DrawOutdoor) || (bts.Type == TDNodeBType.Indoor && DrawIndoor)))
                        {
                            paintBTS(bts, graphics);
                        }
                    }
                }
            }
            if (DrawBTSLabel &&
                (curMapType == LayerMapType.MTGis || curMapType == LayerMapType.Google && mapScale < 50000))
            {
                List<TDNodeB> btss = null;
                if (DrawCurrent)
                {
                    btss = mainModel.CellManager.GetCurrentTDBTSs();
                }
                else
                {
                    btss = mainModel.CellManager.GetTDBTSs(CurShowTimeAt);
                }
                if (btss != null)
                {
                    DrawedBTSLabelRectangles.Clear();
                    foreach (TDNodeB bts in btss)
                    {
                        if (bts.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((bts.Type == TDNodeBType.Outdoor && DrawOutdoor) || (bts.Type == TDNodeBType.Indoor && DrawIndoor)))
                        {
                            paintBTSLabel(bts, graphics);
                        }
                    }
                }
            }
            if (DrawBTSsLine)
            {
                drawLineBetweenBTS(graphics, dRect);
            }
            if (DrawCellLabel && (mapScale < 50000 || 
                (curMapType == LayerMapType.MTGis && (MainModel.CellHighLightAll || (MainModel.CellHighlightList_TD.Count > 0)))))
            {
                List<TDCell> cells = null;
                if (DrawCurrent)
                {
                    cells = mainModel.CellManager.GetCurrentTDCells();
                }
                else
                {
                    cells = mainModel.CellManager.GetTDCells(CurShowTimeAt);
                }
                if (cells != null)
                {
                    DrawedCellLabelRectangles.Clear();
                    foreach (TDCell cell in cells)
                    {
                        if (mapScale >= 50000 && curMapType == LayerMapType.MTGis
                            && !MainModel.CellHighLightAll && MainModel.CellHighlightList.Count > 0 && !MainModel.CellHighlightList_TD.Contains(cell))
                        {
                            continue;
                        }
                        if (cell.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((cell.Type == TDNodeBType.Outdoor && DrawOutdoor) || (cell.Type == TDNodeBType.Indoor && DrawIndoor)))
                        {
                            paintCellLabel(cell, graphics);
                        }
                    }
                }
            }
            if (DrawAntennaLabel && (mapScale < 50000 ||
                (curMapType == LayerMapType.MTGis && (MainModel.CellHighLightAll || (MainModel.CellHighlightList_TD.Count > 0)))))
            {
                List<TDAntenna> antennas = null;
                if (DrawCurrent)
                {
                    antennas = mainModel.CellManager.GetCurrentTDAntennas();
                }
                else
                {
                    antennas = mainModel.CellManager.GetTDAntennas(CurShowTimeAt);
                }
                if (antennas != null)
                {
                    DrawedAntennaLabelRectangles.Clear();
                    foreach (TDAntenna antenna in antennas)
                    {
                        if (mapScale >= 50000 && !MainModel.CellHighLightAll && MainModel.CellHighlightList_TD.Count > 0 
                            && curMapType == LayerMapType.MTGis && !MainModel.CellHighlightList_TD.Contains(antenna.BelongCell))
                        {
                            continue;
                        }
                        if (antenna.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((antenna.Type == TDNodeBType.Outdoor && DrawOutdoor) || (antenna.Type == TDNodeBType.Indoor && DrawIndoor)))
                        {
                            paintAntennaLabel(antenna, graphics);
                        }
                    }
                }
            }
            if ((mapScale < 10000000 && curMapType == LayerMapType.MTGis)
                || (mapScale < 100000 && curMapType == LayerMapType.Google))
            {
                List<TDCell> cells = null;
                if (mainModel.ClusterAnalysisSet.JamTDCellOnGIS != null && mainModel.ClusterAnalysisSet.OrgTDCellsOnGIS.Count > 0)
                {
                    foreach (TDCell orgCell in mainModel.ClusterAnalysisSet.OrgTDCellsOnGIS)
                    {
                        DbPoint jamDPoint = new DbPoint(mainModel.ClusterAnalysisSet.JamTDCellOnGIS.Antenna.EndPointLongitude
                            , mainModel.ClusterAnalysisSet.JamTDCellOnGIS.Antenna.EndPointLatitude);
                        PointF jamPointF;
                        gisAdapter.ToDisplay(jamDPoint, out jamPointF);

                        DbPoint orgDPoint = new DbPoint(orgCell.Antenna.EndPointLongitude, orgCell.Antenna.EndPointLatitude);
                        PointF orgPointF;
                        gisAdapter.ToDisplay(orgDPoint, out orgPointF);

                        graphics.DrawLine(new Pen(brushOrgCells, 2), jamPointF, orgPointF);

                    }
                }

                if (DrawPlanningLabel && curMapType == LayerMapType.MTGis)
                {
                    if (DrawCurrent)
                    {
                        cells = mainModel.CellManager.GetCurrentTDCells();
                    }
                    else
                    {
                        cells = mainModel.CellManager.GetTDCells(CurShowTimeAt);
                    }

                    if (cells != null)
                    {
                        foreach (TDCell cell in cells)
                        {
                            if (cell.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                                && ((cell.Type == TDNodeBType.Outdoor && DrawOutdoor) || (cell.Type == TDNodeBType.Indoor && DrawIndoor)))
                            {
                                paintPlanningLabel(cell, graphics);
                            }
                        }
                    }
                    cells = null;
                    if (DrawServer)
                    {
                        cells = mainModel.ServerTDCells;
                    }
                    if (cells != null)
                    {
                        foreach (TDCell cell in cells)
                        {
                            if (cell.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                                && ((cell.Type == TDNodeBType.Outdoor && DrawOutdoor) || (cell.Type == TDNodeBType.Indoor && DrawIndoor)))
                            {
                                paintPlanningLabel(cell, graphics);
                            }
                        }
                    }
                }

            }
        }
        
        public List<System.Drawing.Rectangle> DrawedCustomCellLabelRectangles { get; set; } = new List<System.Drawing.Rectangle>();
        public List<TDCell> GetCellsByRegion(MTPolygon mapRegion)
        {
            List<TDCell> cellsInRegion = new List<TDCell>();
            List<TDCell> cells = null;
            if (DrawCurrent)
            {
                cells = CellManager.GetInstance().GetCurrentTDCells();
            }
            else
            {
                cells = CellManager.GetInstance().GetTDCells(CurShowTimeAt);
            }
            if (cells == null)
            {
                return new List<TDCell>();
            }
            foreach (TDCell cell in cells)
            {
                if (mapRegion.Contains(cell.Longitude, cell.Latitude))
                {
                    cellsInRegion.Add(cell);
                }
            }
            return cellsInRegion;
        }
        
        private void paintPlanningLabel(TDCell cell, Graphics graphics)
        {
            Brush brush = null;
            if (DrawCoBCCH && mainModel.CoCPITDCells.Contains(cell))
            {
                brush = brushCoBCCH;
            }
            else if (DrawCoTCH && mainModel.CoFreqTDCells.Contains(cell))
            {
                brush = brushCoTCH;
            }
            if (brush == null)
            {
                return;
            }
            DbPoint dPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            
            List<string> descriptions;
            descriptions = mainModel.GetLabel(cell);
            SizeF size = graphics.MeasureString(descriptions[0], FontPlanningLabel);
            graphics.FillRectangle(brush, point.X, point.Y, size.Width, size.Height);
            graphics.DrawString(descriptions[0], FontPlanningLabel, new SolidBrush(Color.Black), point.X, point.Y);
            graphics.ResetTransform();
        }

        private void drawLineBetweenBTS(Graphics graphics, DbRect dRect)
        {
            if (mainModel.SelectedTDCell == null)
            {
                return;
            }
            TDNodeB curBTS = mainModel.SelectedTDCell.BelongBTS;
            List<TDNodeB> BTSs = null;
            if (DrawCurrent)
            {
                BTSs = mainModel.CellManager.GetCurrentTDBTSs();
            }
            else
            {
                BTSs = mainModel.CellManager.GetTDBTSs(CurShowTimeAt);
            }
            if (BTSs != null && curBTS != null)
            {
                Pen pen = new Pen(BtssLineColor, BtssLineWidth);
                Brush brsh = new SolidBrush(BTSLineLblColor);
                System.Drawing.Font fontMeasure = new System.Drawing.Font(new FontFamily("宋体"), 10, FontStyle.Bold);
                DbPoint dPointCurBTS = new DbPoint(curBTS.Longitude, curBTS.Latitude);
                PointF pointCurBTS;
                gisAdapter.ToDisplay(dPointCurBTS, out pointCurBTS);
                List<BTSsDistance> BTSsDistanceLst = getBTSsDistanceLst(dRect, curBTS, BTSs, dPointCurBTS);
                BTSsDistanceLst.Sort(BTSsDistance.SortBTSsDistance);
                List<BTSsDistance> tempLst = new List<BTSsDistance>();
                for (int i = 0; i < BTSsDistanceLst.Count; i++)
                {
                    int distance = BTSsDistanceLst[i].distance;
                    bool isDraw;
                    PointF pointBTS1;
                    judgeNeedDrawPointBTS(pointCurBTS, BTSsDistanceLst, tempLst, i, out isDraw, out pointBTS1);
                    if (isDraw)
                    {
                        tempLst.Add(BTSsDistanceLst[i]);
                        graphics.DrawLine(pen, pointCurBTS, pointBTS1);
                        graphics.DrawString("" + distance + "米", fontMeasure, brsh, (pointBTS1.X + pointCurBTS.X) / 2 - 10, (pointBTS1.Y + pointCurBTS.Y) / 2 - 10);
                    }
                }
            }
        }

        private List<BTSsDistance> getBTSsDistanceLst(DbRect dRect, TDNodeB curBTS, List<TDNodeB> BTSs, DbPoint dPointCurBTS)
        {
            List<BTSsDistance> BTSsDistanceLst = new List<BTSsDistance>();
            foreach (TDNodeB bts in BTSs)
            {
                if (bts.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
                {
                    if (bts == curBTS)
                    {
                        continue;
                    }
                    addValidBTSsDistanceLst(dPointCurBTS, BTSsDistanceLst, bts);
                }
            }

            return BTSsDistanceLst;
        }

        private void addValidBTSsDistanceLst(DbPoint dPointCurBTS, List<BTSsDistance> BTSsDistanceLst, TDNodeB bts)
        {
            if (((bts.Type == TDNodeBType.Outdoor && DrawOutdoor)
                   || (bts.Type == TDNodeBType.Indoor && DrawIndoor)))
            {
                DbPoint dPointBts = new DbPoint(bts.Longitude, bts.Latitude);
                double longitudeDistance = (Math.Sin((90 - dPointBts.y) * 2 * Math.PI / 360) + Math.Sin((90 - dPointCurBTS.y) * 2 * Math.PI / 360)) / 2 * (dPointBts.x - dPointCurBTS.x) / 360 * 40075360;
                double latitudeDistance = (dPointBts.y - dPointCurBTS.y) / 360 * 39940670;
                double distance = Math.Sqrt(longitudeDistance * longitudeDistance + latitudeDistance * latitudeDistance);
                if (0 < distance && distance < BtssDistance)
                {
                    BTSsDistanceLst.Add(new BTSsDistance(bts, (int)distance));
                }
            }
        }

        private void judgeNeedDrawPointBTS(PointF pointCurBTS, List<BTSsDistance> BTSsDistanceLst, List<BTSsDistance> tempLst, int i, out bool isDraw, out PointF pointBTS1)
        {
            isDraw = true;
            TDNodeB bts = BTSsDistanceLst[i].bts;
            DbPoint dPointBTS1 = new DbPoint(bts.Longitude, bts.Latitude);
            gisAdapter.ToDisplay(dPointBTS1, out pointBTS1);
            foreach (BTSsDistance item in tempLst)
            {
                DbPoint dPointBTS2 = new DbPoint(item.bts.Longitude, item.bts.Latitude);
                PointF pointBTS2;
                gisAdapter.ToDisplay(dPointBTS2, out pointBTS2);
                if (MathFuncs.CalAngle(pointCurBTS, pointBTS1, pointBTS2) < BtsLineAngle)
                {
                    isDraw = false;
                }
            }
        }

        internal class BTSsDistance
        {
            public TDNodeB bts;
            public int distance;
            public BTSsDistance(TDNodeB bts, int distance)
            {
                this.bts = bts;
                this.distance = distance;
            }
            public static int SortBTSsDistance(BTSsDistance bts1, BTSsDistance bts2)
            {
                return bts1.distance.CompareTo(bts2.distance);
            }
        }

        public override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            MapForm mf = sender as MapForm;
            Select(((MapForm.MapEventArgs)e).MapOp2, mf.TDNodeBs, mf.TDCells, mf.TDAntennas);
        }

        public void Select(MapOperation2 mop2, List<TDNodeB> selectedBTSs, List<TDCell> selectedCells, List<TDAntenna> selectedAntennas)
        {
            select(mop2, selectedBTSs, selectedCells, selectedAntennas, DrawBTS, DrawAntenna, DrawCell);
        }

        private void select(MapOperation2 mop2, List<TDNodeB> selectedBTSs, List<TDCell> selectedCells, List<TDAntenna> selectedAntennas, bool drawBTS, bool drawAntenna, bool drawCell)
        {
            if (IsVisible)
            {
                DbRect dRect = mop2.GetRegion().Bounds;
                if (drawBTS)
                {
                    List<TDNodeB> btss = null;
                    if (DrawCurrent)
                    {
                        btss = mainModel.CellManager.GetCurrentTDBTSs();
                    }
                    else
                    {
                        btss = mainModel.CellManager.GetTDBTSs(CurShowTimeAt);
                    }
                    if (btss != null)
                    {
                        foreach (TDNodeB bts in btss)
                        {
                            if (bts.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01)
                                && ((bts.Type == TDNodeBType.Outdoor && DrawOutdoor) || (bts.Type == TDNodeBType.Indoor && DrawIndoor)))
                            {
                                TDNodeB selectedBTS = selectBTS(bts, mop2);
                                if (selectedBTS != null)
                                {
                                    selectedBTSs.Add(selectedBTS);
                                }
                            }
                        }
                    }
                }

                if (drawAntenna && mapScale < 100000)
                {
                    List<TDAntenna> antennas = null;
                    List<TDCell> cells = null;
                    if (DrawServer)
                    {
                        cells = mainModel.ServerTDCells;
                    }
                    if (cells != null)
                    {
                        foreach (TDCell cell in cells)
                        {
                            TDAntenna antenna = cell.Antenna;
                            if (antenna != null && antenna.Within(dRect.x1 - 0.002, dRect.y1 - 0.002, dRect.x2 + 0.002, dRect.y2 + 0.002))
                            {
                                TDAntenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                                if (selectedAntenna != null)
                                {
                                    selectedAntennas.Add(selectedAntenna);
                                }
                            }
                        }
                    }
                    if (DrawCurrent)
                    {
                        antennas = mainModel.CellManager.GetCurrentTDAntennas();
                    }
                    else
                    {
                        antennas = mainModel.CellManager.GetTDAntennas(CurShowTimeAt);
                    }
                    if (antennas != null)
                    {
                        foreach (TDAntenna antenna in antennas)
                        {
                            if (antenna.Within(dRect.x1 - 0.002, dRect.y1 - 0.002, dRect.x2 + 0.002, dRect.y2 + 0.002))
                            {
                                TDAntenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                                if (selectedAntenna != null && !selectedAntennas.Contains(selectedAntenna))
                                {
                                    selectedAntennas.Add(selectedAntenna);
                                }
                            }
                        }
                    }
                }
                if (drawCell && mapScale < 100000)
                {
                    List<TDCell> cells = null;
                    if (DrawServer)
                    {
                        cells = mainModel.ServerTDCells;
                    }
                    if (cells != null)
                    {
                        foreach (TDCell cell in cells)
                        {
                            if (cell.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                            {
                                TDCell selectedCell = selectCell(cell, mop2, mapScale);
                                if (selectedCell != null)
                                {
                                    selectedCells.Add(selectedCell);
                                }
                            }
                        }
                    }
                    if (DrawCurrent)
                    {
                        cells = mainModel.CellManager.GetCurrentTDCells();
                    }
                    else
                    {
                        cells = mainModel.CellManager.GetTDCells(CurShowTimeAt);
                    }
                    if (cells != null)
                    {
                        foreach (TDCell cell in cells)
                        {
                            if (cell.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                            {
                                TDCell selectedCell = selectCell(cell, mop2, mapScale);
                                if (selectedCell != null && !selectedCells.Contains(selectedCell))
                                {
                                    selectedCells.Add(selectedCell);
                                }
                            }
                        }
                    }
                }
            }
        }

        public TDCell SelectCell(MapOperation2 mop2)
        {
            if (IsVisible)
            {
                DbRect dRect = mop2.GetRegion().Bounds;
                if (DrawAntenna && mapScale < 100000)
                {
                    List<TDAntenna> antennas = null;
                    List<TDCell> cells = null;
                    if (DrawServer)
                    {
                        cells = mainModel.ServerTDCells;
                    }
                    if (cells != null)
                    {
                        foreach (TDCell cell in cells)
                        {
                            TDAntenna antenna = cell.Antenna;
                            if (antenna != null && antenna.Within(dRect.x1 - 0.002, dRect.y1 - 0.002, dRect.x2 + 0.002, dRect.y2 + 0.002))
                            {
                                TDAntenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                                if (selectedAntenna != null)
                                {
                                    return cell;
                                }
                            }
                        }
                    }
                    if (DrawCurrent)
                    {
                        antennas = mainModel.CellManager.GetCurrentTDAntennas();
                    }
                    else
                    {
                        antennas = mainModel.CellManager.GetTDAntennas(CurShowTimeAt);
                    }
                    if (antennas != null)
                    {
                        foreach (TDAntenna antenna in antennas)
                        {
                            if (antenna.Within(dRect.x1 - 0.002, dRect.y1 - 0.002, dRect.x2 + 0.002, dRect.y2 + 0.002))
                            {
                                TDAntenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                                if (selectedAntenna != null)
                                {
                                    return selectedAntenna.BelongCell;
                                }
                            }
                        }
                    }
                }
                if (DrawCell && mapScale < 100000)
                {
                    List<TDCell> cells = null;
                    if (DrawServer)
                    {
                        cells = mainModel.ServerTDCells;
                    }
                    if (cells != null)
                    {
                        foreach (TDCell cell in cells)
                        {
                            if (cell.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                            {
                                TDCell selectedCell = selectCell(cell, mop2, mapScale);
                                if (selectedCell != null)
                                {
                                    return selectedCell;
                                }
                            }
                        }
                    }
                    if (DrawCurrent)
                    {
                        cells = mainModel.CellManager.GetCurrentTDCells();
                    }
                    else
                    {
                        cells = mainModel.CellManager.GetTDCells(CurShowTimeAt);
                    }
                    if (cells != null)
                    {
                        foreach (TDCell cell in cells)
                        {
                            if (cell.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                            {
                                TDCell selectedCell = selectCell(cell, mop2, mapScale);
                                if (selectedCell != null)
                                {
                                    return selectedCell;
                                }
                            }
                        }
                    }
                }
            }
            return null;
        }

        private void paintCell(TDCell cell, Graphics graphics, double scale)
        {
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            Brush brush = brushCell;
            int index = 0;
            if (cell.DirectionType != TDAntennaDirectionType.Omni)
            {
                index = 1;
            }
            if (DrawServer)
            {
                for (int i = 0; i < mainModel.ServerTDCells.Count; i++)
                {
                    if (mainModel.ServerTDCells[i] == cell)
                    {
                        brush = brushesServer[i];
                        break;
                    }
                }
            }

            if (mainModel.CellMultiCovList_TD.Count > 0)
            {
                Color bClr = Color.Empty;
                List<TDCell> cells = new List<TDCell>();
                if (mainModel.CellMultiCovMCellSubCellDic_TD.Count > 0)
                {
                    foreach (TDCell mCell in mainModel.CellMultiCovMCellSubCellDic_TD.Keys)
                    {
                        cells = mainModel.CellMultiCovMCellSubCellDic_TD[mCell];
                        if (mCell == cell)
                        {
                            bClr = Color.Red;
                            break;
                        }
                    }
                    if (bClr == Color.Empty)
                    {
                        foreach (TDCell subCell in cells)
                        {
                            if (subCell == cell)
                            {
                                bClr = Color.Cyan;
                                break;
                            }
                        }
                    }
                    if (bClr != Color.Empty)
                    {
                        brush = new SolidBrush(bClr);
                    }
                }
                else
                {
                    foreach (TDCellInCovRegionInfo cellMultiCov in mainModel.CellMultiCovList_TD)
                    {
                        if (cell.Equals(cellMultiCov.tdCell))
                        {
                            Color curColor = Color.Empty;
                            if (mainModel.ShowCellCoverAbsLevel)
                            {
                                curColor = MainModel.GetCellMultiCovColor(cellMultiCov.AbsRate);
                            }
                            else if (mainModel.ShowCellCoverRelLevel)
                            {
                                curColor = MainModel.GetCellMultiCovColor(cellMultiCov.RelRate);
                            }
                            else if (mainModel.ShowCellCoverMulLevel)
                            {
                                curColor = MainModel.GetCellMultiCovColor(cellMultiCov.MulRate);
                            }

                            if (curColor != Color.Empty)
                            {
                                brush = new SolidBrush(curColor);
                            }
                            break;
                        }
                    }
                }
            }

            if (mainModel.CellRedundantCovList_TD.Count > 0)
            {
                foreach (TDCellRedundantInfo cellRedundantCov in mainModel.CellRedundantCovList_TD)
                {
                    if (cell.Equals(cellRedundantCov.tdCell))
                    {
                        Color curColor = Color.Empty;
                        if (mainModel.ShowCellRedundantAbsLevel)
                        {
                            double absrate = cellRedundantCov.AbsRate == null ? 0 : (double)cellRedundantCov.AbsRate;
                            curColor = MainModel.GetCellRedundantCovColor(absrate);
                        }
                        else if (mainModel.ShowCellRedundantRelLevel)
                        {
                            double relrate = cellRedundantCov.RelRate == null ? 0 : (double)cellRedundantCov.RelRate;
                            curColor = MainModel.GetCellRedundantCovColor(relrate);
                        }
                        else if (mainModel.ShowCellRedundantMulLevel)
                        {
                            double mulrate = cellRedundantCov.MulRate == null ? 0 : (double)cellRedundantCov.MulRate;
                            curColor = MainModel.GetCellRedundantCovColor(mulrate);
                        }

                        if (curColor != Color.Empty)
                        {
                            brush = new SolidBrush(curColor);
                        }
                        break;
                    }
                }
            }

            if (mainModel.OutServiceInfoManager.isContainSnapShot(MasterCom.RAMS.Func.OutServiceInfo.NetType.TD, cell))
            {
                brush = brushAlarmCell;
            }
            else if (mainModel.TDNeighbourTDCells.Contains(cell))
            {
                brush = brushTDNeighbourTDCell;
            }
            else if (mainModel.LTENeighbourTDCells.Contains(cell))
            {
                Brush tmpBrush = mainModel.MainForm.GetMapForm().GetLTECellLayer().BrushNeighbourTDCell;
                if (tmpBrush != null)
                {
                    brush = tmpBrush;
                }
            }
            
            List<GraphicsPath> paths;
            if (mainModel.CellHighLightAll)
            {
                paths = cellHighlightPaths;
            }
            else
            {
                if (mainModel.CellHighlightList_TD.Contains(cell))
                {
                    paths = cellHighlightPaths;
                }
                else
                {
                    paths = cellPaths;
                }
            }

            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(cell.Direction - 90);
            scale = getDisplayScale(scale);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));
            graphics.FillPath(brush, paths[index]);
            graphics.DrawPath(this.penCellOutLine, paths[index]);
            if (mainModel.SelectedTDCell == cell || mainModel.SelectedTDCells.Contains(cell))
            {
                switch (cell.CellType)
                {
                    case MainOrNBCell.MainCell:
                    case MainOrNBCell.Other:
                        graphics.DrawPath(penSelected, paths[index]);
                        break;
                    case MainOrNBCell.NBCell:
                        graphics.DrawPath(penSelectedNB, paths[index]);
                        break;

                }
            }
            graphics.ResetTransform();
        }

        private void paintTDScanNonconformityCell(TDCell cell, Graphics graphics, double scale, Brush brush)
        {
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            int index = 0;

            if (cell.DirectionType != TDAntennaDirectionType.Omni)
            {
                index = 1;
            }

            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(cell.Direction - 90);
            scale = getDisplayScale(scale);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));
            graphics.FillPath(brush, cellPaths[index]);
            graphics.ResetTransform();
        }

        private void paintCellPair(TDCell cell, List<TestPoint> tpList, Color lineColor, Graphics graphics, double scale)
        {
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            int index = 0;

            if (cell.DirectionType != TDAntennaDirectionType.Omni)
            {
                index = 1;
            }

            List<PathGradientBrush> pathGradientBrushs;
            if (mainModel.CellHighLightAll)
            {
                pathGradientBrushs = cellHighlightPathGradientBrushs;
            }
            else
            {
                if (mainModel.CellHighlightList_TD.Contains(cell))
                {
                    pathGradientBrushs = cellHighlightPathGradientBrushs;
                }
                else
                {
                    pathGradientBrushs = cellPathGradientBrushs;
                }
            }

            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(cell.Direction - 90);
            scale = getDisplayScale(scale);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));
            graphics.FillPath(pathGradientBrushs[index], cellPaths[index]);
            graphics.ResetTransform();

            DbPoint cellDPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
            PointF cellPointF;
            this.gisAdapter.ToDisplay(cellDPoint, out cellPointF);
            if (tpList != null)
            {
                foreach (TestPoint tp in tpList)
                {
                    DbPoint testDPoint = new DbPoint(tp.Longitude, tp.Latitude);
                    PointF testPointF;
                    this.gisAdapter.ToDisplay(testDPoint, out testPointF);
                    graphics.DrawLine(new Pen(lineColor, 1), testPointF, cellPointF);
                }
            }
        }

        private void paintCellPair(ZTDIYNonconformity.DefineCell cell, List<TestPoint> tpList, Color lineColor, Graphics graphics, double scale)
        {
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            int index = 0;
            List<PathGradientBrush> pathGradientBrushs;

            pathGradientBrushs = cellPathGradientBrushs;
            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(cell.Direction - 90);
            scale = getDisplayScale(scale);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));
            graphics.FillPath(pathGradientBrushs[index], cellPaths[index]);
            graphics.ResetTransform();

            DbPoint cellDPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF cellPointF;
            this.gisAdapter.ToDisplay(cellDPoint, out cellPointF);
            if (tpList != null)
            {
                foreach (TestPoint tp in tpList)
                {
                    DbPoint testDPoint = new DbPoint(tp.Longitude, tp.Latitude);
                    PointF testPointF;
                    this.gisAdapter.ToDisplay(testDPoint, out testPointF);
                    graphics.DrawLine(new Pen(lineColor, 1), testPointF, cellPointF);
                }
            }
        }


        private void paintCellLabel(TDCell cell, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string cellDes = getCellLabelDes(cell, 100);
            SizeF size = graphics.MeasureString(cellDes, FontCellLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in DrawedCellLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(cellDes, FontCellLabel, Brushes.Black, 3, -size.Height / 2);
                DrawedCellLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }

        public string getCellLabelDes(TDCell cell, int length)
        {
            string des = "";
            if (DrawCellName)
            {
                des += cell.Name + " ";
            }
            if (DrawCellCode)
            {
                des += cell.Code + " ";
            }
            if (DrawCellLAC)
            {
                des += cell.LAC.ToString() + " ";
            }
            if (DrawCellCI)
            {
                des += cell.CI.ToString() + " ";
            }
            if (DrawCellFreq)
            {
                des += cell.FREQ.ToString() + " ";
            }
            if (DrawCellFreqList)
            {
                des += cell.FreqDesc + " ";
            }
            if (DrawCellDes)
            {
                des += cell.DESC.ToString() + " ";
            }
            if (DrawCellCPI)
            {
                des += cell.CPI.ToString() + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }
        
        public List<System.Drawing.Rectangle> DrawedCellLabelRectangles { get; set; } = new List<System.Drawing.Rectangle>();
        private void paintAntenna(TDAntenna antenna, Graphics graphics, double scale)
        {
            DbPoint dPoint = new DbPoint(antenna.Longitude, antenna.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            Brush brush = brushAntenna;
            Pen pen = penAntenna;
            int index = 1;
            bool isneighbour = false;
            bool isClusterInterference = false;
            if (antenna.DirectionType == TDAntennaDirectionType.Omni)
            {
                index = 0;
            }
            if (DrawServer)
            {
                for (int i = 0; i < mainModel.ServerTDCells.Count; i++)
                {
                    if (antenna.BelongCell == mainModel.ServerTDCells[i])
                    {
                        brush = brushesServer[i];
                        break;
                    }
                }
            }

            if (mainModel.CellMultiCovList_TD.Count > 0)
            {
                Color bClr = Color.Empty;
                List<TDCell> cells = new List<TDCell>();
                if (mainModel.CellMultiCovMCellSubCellDic_TD.Count > 0)
                {
                    foreach (TDCell mCell in mainModel.CellMultiCovMCellSubCellDic_TD.Keys)
                    {
                        cells = mainModel.CellMultiCovMCellSubCellDic_TD[mCell];
                        if (antenna.BelongCell == mCell)
                        {
                            bClr = Color.Red;
                            break;
                        }
                    }
                    if (bClr == Color.Empty)
                    {
                        foreach (TDCell subCell in cells)
                        {
                            if (antenna.BelongCell == subCell)
                            {
                                bClr = Color.Cyan;
                                break;
                            }
                        }
                    }
                    if (bClr != Color.Empty)
                    {
                        brush = new SolidBrush(bClr);
                    }
                }
                else
                {
                    foreach (TDCellInCovRegionInfo cellMultiCov in mainModel.CellMultiCovList_TD)
                    {
                        if (antenna.BelongCell == cellMultiCov.tdCell)
                        {
                            Color curColor = Color.Empty;
                            if (mainModel.ShowCellCoverAbsLevel)
                            {
                                curColor = MainModel.GetCellMultiCovColor(cellMultiCov.AbsRate);
                            }
                            else if (mainModel.ShowCellCoverRelLevel)
                            {
                                curColor = MainModel.GetCellMultiCovColor(cellMultiCov.RelRate);
                            }
                            else if (mainModel.ShowCellCoverMulLevel)
                            {
                                curColor = MainModel.GetCellMultiCovColor(cellMultiCov.MulRate);
                            }

                            if (curColor != Color.Empty)
                            {
                                brush = new SolidBrush(curColor);
                            }
                            break;
                        }
                    }
                }
            }

            foreach (TDCell cell in mainModel.TDNeighbourTDCells)
            {
                if (antenna.BelongCell == cell)
                {
                    brush = brushTDNeighbourTDCell;
                    pen = new Pen(brush, 2);
                    isneighbour = true;
                    break;
                }
            }

            if (mainModel.OutServiceInfoManager.isContainSnapShot(MasterCom.RAMS.Func.OutServiceInfo.NetType.TD, antenna.BelongCell))
            {
                brush = brushAlarmCell;
            }

            if (mainModel.ClusterAnalysisSet.JamTDCellOnGIS != null && antenna.BelongCell.ID == mainModel.ClusterAnalysisSet.JamTDCellOnGIS.ID)
            {
                brush = brushJamCell;
                isClusterInterference = true;
            }

            foreach (TDCell orgCell in mainModel.ClusterAnalysisSet.OrgTDCellsOnGIS)
            {
                if (antenna.BelongCell.ID == orgCell.ID)
                {
                    brush = brushOrgCells;
                    isClusterInterference = true;
                    break;
                }
            }

            List<GraphicsPath> paths;
            if (mainModel.CellHighLightAll)
            {
                paths = antennaHighlightPaths;
            }
            else
            {
                paths = antennaPaths;
                foreach (TDCell cell in mainModel.CellHighlightList_TD)
                {
                    if (cell.Antenna == antenna)
                    {
                        paths = antennaHighlightPaths;
                        break;
                    }
                }
            }

            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(antenna.Direction - 90);
            //scale = getDisplayScale(scale);  //by zlr 不限制TD天线放大倍数
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));
            if (index == 0)
            {
                graphics.DrawPath(pen, paths[index]);
            }
            else
            {
                graphics.FillPath(brush, paths[index]);
            }
            if (isneighbour)
            {
                graphics.DrawPath(new Pen(brushTDNeighbourTDCell, 5), paths[index]);
            }
            if (antenna.BelongCell == mainModel.SelectedTDCell)
            {
                graphics.DrawPath(penSelected, paths[index]);
            }
            foreach (TDCell tdCell in mainModel.SelectedTDCells)
            {
                if (antenna.BelongCell == tdCell)
                {
                    switch (tdCell.CellType)
                    {
                        case MainOrNBCell.MainCell:
                        case MainOrNBCell.Other:
                            graphics.DrawPath(penSelected, paths[index]);
                            break;
                        case MainOrNBCell.NBCell:
                            graphics.DrawPath(penSelectedNB, paths[index]);
                            break;

                    }
                }
            }
            if (isClusterInterference)
            {
                graphics.DrawPath(new Pen(brush, 2), paths[index]);
            }
            graphics.ResetTransform();
        }

        private void paintAntennaLabel(TDAntenna antenna, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(antenna.EndPointLongitude, antenna.EndPointLatitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string antennaDes = getAntennaLabelDes(antenna, 100);
            SizeF size = graphics.MeasureString(antennaDes, FontAntennaLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in DrawedAntennaLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(antennaDes, FontAntennaLabel, Brushes.Black, 3, -size.Height / 2);
                DrawedAntennaLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }

        public string getAntennaLabelDes(TDAntenna antenna, int length)
        {
            string des = "";
            if (DrawAntennaGroup)
            {
                des += antenna.AnttenaGroupSN.ToString() + " ";
            }
            if (DrawAntennaSN)
            {
                des += antenna.SN.ToString() + " ";
            }
            if (DrawAntennaLongitude)
            {
                des += antenna.Longitude.ToString() + " ";
            }
            if (DrawAntennaLatitude)
            {
                des += antenna.Latitude.ToString() + " ";
            }
            if (DrawAntennaDirectionType)
            {
                des += antenna.DirectionType.ToString() + " ";
            }
            if (DrawAntennaDirection)
            {
                des += antenna.Direction.ToString() + " ";
            }
            if (DrawAntennaDownward)
            {
                des += antenna.Downward.ToString() + " ";
            }
            if (DrawAntennaAltitude)
            {
                des += antenna.Altitude.ToString() + " ";
            }
            if (DrawAntennaDescription)
            {
                des += antenna.Description + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }
        
        public List<System.Drawing.Rectangle> DrawedAntennaLabelRectangles { get; set; } = new List<System.Drawing.Rectangle>();
        private void paintBTS(TDNodeB bts, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);

            graphics.DrawRectangle(penBTS, -SizeBTS / 2, -SizeBTS / 2, SizeBTS, SizeBTS);

            graphics.ResetTransform();
        }

        private void paintBTSLabel(TDNodeB bts, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string btsDes = getBTSLabelDes(bts, 100);
            SizeF size = graphics.MeasureString(btsDes, FontBTSLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in DrawedBTSLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(btsDes, FontBTSLabel, Brushes.Black, 3, -size.Height / 2);
                DrawedBTSLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }

        public string getBTSLabelDes(TDNodeB bts, int length)
        {
            string des = "";
            if (DrawBTSName)
            {
                des += bts.Name + " ";
            }
            if (DrawBTSRNC)
            {
                des += bts.BelongBSC.Name + " ";
            }
            if (DrawBTSLongitude)
            {
                des += bts.Longitude.ToString() + " ";
            }
            if (DrawBTSLatitude)
            {
                des += bts.Latitude.ToString() + " ";
            }
            if (DrawBTSType)
            {
                des += bts.TypeStringDesc + " ";
            }
            if (DrawBTSDescription)
            {
                des += bts.Description + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }
        
        public List<System.Drawing.Rectangle> DrawedBTSLabelRectangles { get; set; } = new List<System.Drawing.Rectangle>();
        private TDCell selectCell(TDCell cell, MapOperation2 mop2, double scale)
        {
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            float ratio = (float)(10000 / scale);
            int index = 1;
            bool circle = false;
            if (cell.DirectionType == TDAntennaDirectionType.Omni)
            {
                index = 0;
                circle = true;
            }

            if (circle)
            {
                RectangleF rect = new RectangleF(point.X + cellPoints[index][0].X * ratio, point.Y + cellPoints[index][0].Y * ratio, cellPoints[index][2].X * 2 * ratio, cellPoints[index][2].Y * 2 * ratio);
                DbRect dRect;
                gisAdapter.FromDisplay(rect, out dRect);
                if (mop2.CheckCenterInDRect(dRect)) //Ellipse
                {
                    return cell;
                }
            }
            else
            {
                PointF[] pointsNew = new PointF[cellPoints[index].Length + 1];
                for (int i = 0; i < cellPoints[index].Length + 1; i++)
                {
                    PointF pointNew = cellPoints[index][i % cellPoints[index].Length];
                    if (pointNew.X == 0 && pointNew.Y == 0)
                    {
                        pointNew.X = point.X;
                        pointNew.Y = point.Y;
                    }
                    else
                    {
                        double pointRadius = Math.Sqrt(pointNew.X * ratio * pointNew.X * ratio + pointNew.Y * ratio * pointNew.Y * ratio);
                        double pointDirection = (cell.Direction - 90) / 180.0F * Math.PI + Math.Atan2(pointNew.Y, pointNew.X);
                        pointNew.X = point.X + (float)(pointRadius * Math.Cos(pointDirection));
                        pointNew.Y = point.Y + (float)(pointRadius * Math.Sin(pointDirection));
                    }
                    pointsNew[i] = pointNew;
                }
                DbPoint[] dpoints;
                gisAdapter.FromDisplay(pointsNew, out dpoints);
                if (mop2.CheckCenterInDbPoints(dpoints))
                {
                    return cell;
                }

            }
            return null;
        }

        private TDNodeB selectBTS(TDNodeB bts, MapOperation2 mop2)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            RectangleF rect;

            rect = new RectangleF(point.X - SizeBTS / 2, point.Y - SizeBTS / 2, SizeBTS, SizeBTS);

            DbRect dRect;
            gisAdapter.FromDisplay(rect, out dRect);
            if (mop2.CheckCenterInDRect(dRect))
            {
                return bts;
            }
            return null;
        }

        private TDAntenna selectAntenna(TDAntenna antenna, MapOperation2 mop2, double scale)
        {
            DbPoint dPoint = new DbPoint(antenna.Longitude, antenna.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            float ratio = (float)(10000 / scale);
            int index = 1;
            bool circle = false;
            if (antenna.DirectionType == TDAntennaDirectionType.Omni)
            {
                index = 0;
                circle = true;
            }

            List<PointF[]> points = getAntennaPoints(antenna);

            if (circle)
            {
                RectangleF rect = new RectangleF(point.X + points[index][0].X * ratio, point.Y + points[index][0].Y * ratio, points[index][2].X * 2 * ratio, points[index][2].Y * 2 * ratio);
                DbRect dRect;
                gisAdapter.FromDisplay(rect, out dRect);
                if (mop2.CheckCenterInDRect(dRect)) //Ellipse
                {
                    return antenna;
                }
            }
            else
            {
                PointF[] pointsNew = new PointF[points[index].Length + 1];
                for (int i = 0; i < points[index].Length + 1; i++)
                {
                    point = setPointCoordinate(antenna, point, ratio, index, points, pointsNew, i);
                }
                DbPoint[] dpoints;
                gisAdapter.FromDisplay(pointsNew, out dpoints);
                if (mop2.CheckCenterInDbPoints(dpoints))
                {
                    return antenna;
                }
            }
            return null;
        }

        private PointF setPointCoordinate(TDAntenna antenna, PointF point, float ratio, int index, List<PointF[]> points, PointF[] pointsNew, int i)
        {
            PointF pointNew = points[index][i % points[index].Length];
            if (pointNew.X == 0 && pointNew.Y == 0)
            {
                pointNew.X = point.X;
                pointNew.Y = point.Y;
            }
            else
            {
                double pointRadius = Math.Sqrt(pointNew.X * ratio * pointNew.X * ratio + pointNew.Y * ratio * pointNew.Y * ratio);
                double pointDirection = (antenna.Direction - 90) / 180.0F * Math.PI + Math.Atan2(pointNew.Y, pointNew.X);
                pointNew.X = point.X + (float)(pointRadius * Math.Cos(pointDirection));
                pointNew.Y = point.Y + (float)(pointRadius * Math.Sin(pointDirection));
            }
            pointsNew[i] = pointNew;
            return point;
        }

        private List<PointF[]> getAntennaPoints(TDAntenna antenna)
        {
            List<PointF[]> points;
            if (mainModel.CellHighLightAll)
            {
                points = antennaHighlightPoints;
            }
            else
            {
                points = antennaPoints;
                foreach (TDCell cell in mainModel.CellHighlightList_TD)
                {
                    if (cell.Antenna == antenna)
                    {
                        points = antennaHighlightPoints;
                        break;
                    }
                }
            }

            return points;
        }

        private void serverCellsChanged(object sender, EventArgs e)
        {
            makeBrushes();
            Invalidate();
        }

        public void RefreshBrushes()
        {
            makeBrushes();
        }

        private void makeBrushes()
        {
            if (mainModel == null)
            {
                return;
            }
            brushCell = new SolidBrush(ColorCell);
            brushAntenna = new SolidBrush(ColorAntenna);
            brushCoBCCH = new SolidBrush(ColorCoBCCH);
            brushCoTCH = new SolidBrush(ColorCoTCH);
            brushAdjBCCH = new SolidBrush(ColorAdjBCCH);
            brushAdjTCH = new SolidBrush(ColorAdjTCH);
            brushCoBSIC = new SolidBrush(ColorCoBSIC);
            brushTDNeighbourTDCell = new SolidBrush(ColorNeighbour);
            penSelected = new Pen(ColorSelected, 3);
            penBTS = new Pen(ColorBTS, 1);
            penAntenna = new Pen(ColorAntenna, 2);
            brushesServer.Clear();
            mainModel.ServerCellPens.Clear();
            int count = mainModel.ServerCells.Count + mainModel.ServerTDCells.Count;
            for (int i = 0; i < count; i++)
            {
                float percent = count == 1 ? 0.5F : (float)i / (count - 1);
                Color beginColor;
                Color endColor;
                if (ColorViaEnabled)
                {
                    percent = percent * 2;
                    if (percent < 1)
                    {
                        beginColor = ColorBegin;
                        endColor = ColorVia;
                    }
                    else
                    {
                        percent -= 1;
                        beginColor = ColorVia;
                        endColor = ColorEnd;
                    }
                }
                else
                {
                    beginColor = ColorBegin;
                    endColor = ColorEnd;
                }
                brushesServer.Add(new SolidBrush(Color.FromArgb(
                    (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                    (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                    (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                    )));
                mainModel.ServerCellPens.Add(new Pen(Color.FromArgb(
                    (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                    (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                    (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                    ), 1));
            }
        }

        private XmlElement TransformPoint(TDAntenna antenna, int index, XmlDocument doc, string ColorCode)
        {
            StringBuilder location = new StringBuilder();
            foreach (LayerPoint p in antennaGEPoints)
            {

                if (p.Index == index)
                {
                    if (index == 1 || index == 3)
                    {
                        double angle1 = (90 - antenna.Direction) * Math.PI / 180;
                        double angle2 = Math.Atan(p.Y / p.X);
                        double angle3 = angle1 + angle2;
                        double r = Math.Sqrt(p.X * p.X + p.Y * p.Y);
                        double tarX = r * Math.Cos(angle3) + antenna.Longitude;
                        double tarY = r * Math.Sin(angle3) + antenna.Latitude;
                        location.Append(tarX.ToString() + "," + tarY.ToString() + ",10 ");
                    }
                    else if (index == 0 || index == 2)
                    {
                        double tarX = antenna.Longitude + p.X;
                        double tarY = antenna.Latitude + p.Y;
                        location.Append(tarX.ToString() + "," + tarY.ToString() + ",10 ");

                    }
                }
            }
            Brush brush;
            if (DrawServer)
            {
                for (int i = 0; i < mainModel.ServerTDCells.Count; i++)
                {
                    if (antenna.BelongCell == mainModel.ServerTDCells[i])
                    {
                        brush = brushesServer[i];
                        SolidBrush sbrush = brush as SolidBrush;
                        ColorCode = ChangeColor(sbrush.Color);
                        break;
                    }
                }
            }
            return AddLine(doc, antenna.BelongCell.Name, antenna.Description, location.ToString(), ColorCode);
        }

        private string ChangeColor(Color color)//将Color类型转为string类型
        {
            string alpha = Convert.ToString(color.A, 16);
            string red = Convert.ToString(color.R, 16);
            string green = Convert.ToString(color.G, 16);
            string blue = Convert.ToString(color.B, 16);
            return DealwithColor(alpha) + DealwithColor(blue) + DealwithColor(green) + DealwithColor(red);
        }

        private string DealwithColor(string ColorCode)//对不足两位的颜色值进行补零处理
        {
            if (ColorCode.Length < 2)
            {
                for (int i = 0; i < 2 - ColorCode.Length; i++)
                {
                    ColorCode = "0" + ColorCode;
                }
            }
            return ColorCode;
        }

        private XmlElement AddLine(XmlDocument doc, string Name, string description, string location, string ColorCode)
        {
            XmlElement elemPlacemark = doc.CreateElement("Placemark");
            XmlElement elemName = doc.CreateElement("name");
            elemName.AppendChild(doc.CreateTextNode("小区：" + Name));
            elemPlacemark.AppendChild(elemName);
            XmlElement elemdescription = doc.CreateElement("description");
            elemdescription.AppendChild(doc.CreateTextNode(description));
            elemPlacemark.AppendChild(elemdescription);
            XmlElement elemstyle = doc.CreateElement("Style");
            elemPlacemark.AppendChild(elemstyle);
            XmlElement elemPolyStyle = doc.CreateElement("PolyStyle");
            elemstyle.AppendChild(elemPolyStyle);
            XmlElement elemcolor = doc.CreateElement("color");
            elemcolor.AppendChild(doc.CreateTextNode(ColorCode));
            elemPolyStyle.AppendChild(elemcolor);
            XmlElement elemoutline = doc.CreateElement("outline");
            elemoutline.AppendChild(doc.CreateTextNode("0"));
            elemPolyStyle.AppendChild(elemoutline);
            XmlElement elemPolygon = doc.CreateElement("Polygon");
            elemPlacemark.AppendChild(elemPolygon);
            XmlElement elemextrude = doc.CreateElement("extrude");
            elemextrude.AppendChild(doc.CreateTextNode("1"));
            elemPolygon.AppendChild(elemextrude);
            XmlElement elemaltitudeMode = doc.CreateElement("altitudeMode");
            elemaltitudeMode.AppendChild(doc.CreateTextNode("relativeToGround"));
            elemPolygon.AppendChild(elemaltitudeMode);
            XmlElement elemouterBoundaryIs = doc.CreateElement("outerBoundaryIs");
            elemPolygon.AppendChild(elemouterBoundaryIs);
            XmlElement elemLinearRing = doc.CreateElement("LinearRing");
            elemPolygon.AppendChild(elemLinearRing);
            XmlElement elemCoordinates = doc.CreateElement("coordinates");
            elemCoordinates.AppendChild(doc.CreateTextNode(location));
            elemLinearRing.AppendChild(elemCoordinates);
            elemouterBoundaryIs.AppendChild(elemLinearRing);
            return elemPlacemark;
        }

        private static List<LayerPoint> GEdrawCircle(double radius, int part, int index)
        {
            List<LayerPoint> basePoint = new List<LayerPoint>();
            double angel = 2 * Math.PI / part;
            for (int i = 0; i <= part; i++)
            {
                basePoint.Add(new LayerPoint(radius * Math.Sin(i * angel), radius * Math.Cos(i * angel), index));
            }
            return basePoint;
        }

        internal int MakeShpFile(string filename)
        {
            Shapefile shpFile = new Shapefile();
            try
            {
                int idIdx = 0;
                int fLongId = idIdx++;
                int fLatId = idIdx++;
                int fNameId = idIdx++;
                int fCodeId = idIdx++;
                int fLacId = idIdx++;
                int fCiId = idIdx++;
                int fBcchId = idIdx++;
                int fBsicId = idIdx++;
                int fTchId = idIdx++;
                int fDirectionId = idIdx++;
                int fDownwordId = idIdx++;
                int fBscId = idIdx++;
                int fMscId = idIdx++;
                int fTypeId = idIdx;

                bool result = shpFile.CreateNew("", MapWinGIS.ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }
                ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 7, 0, ref fLongId);
                ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 7, 0, ref fLatId);
                ShapeHelper.InsertNewField(shpFile, "Name", FieldType.STRING_FIELD, 7, 30, ref fNameId);
                ShapeHelper.InsertNewField(shpFile, "Code", FieldType.STRING_FIELD, 7, 30, ref fCodeId);
                ShapeHelper.InsertNewField(shpFile, "LAC", FieldType.INTEGER_FIELD, 7, 0, ref fLacId);
                ShapeHelper.InsertNewField(shpFile, "CI", FieldType.INTEGER_FIELD, 7, 0, ref fCiId);
                ShapeHelper.InsertNewField(shpFile, "ARFCN", FieldType.INTEGER_FIELD, 7, 0, ref fBcchId);
                ShapeHelper.InsertNewField(shpFile, "CPI", FieldType.INTEGER_FIELD, 7, 0, ref fBsicId);
                ShapeHelper.InsertNewField(shpFile, "ARFCN List", FieldType.STRING_FIELD, 7, 30, ref fTchId);
                ShapeHelper.InsertNewField(shpFile, "方向角", FieldType.INTEGER_FIELD, 7, 0, ref fDirectionId);
                ShapeHelper.InsertNewField(shpFile, "下倾角", FieldType.INTEGER_FIELD, 7, 0, ref fDownwordId);
                ShapeHelper.InsertNewField(shpFile, "BSC", FieldType.STRING_FIELD, 7, 30, ref fBscId);
                ShapeHelper.InsertNewField(shpFile, "MSC", FieldType.STRING_FIELD, 7, 30, ref fMscId);
                ShapeHelper.InsertNewField(shpFile, "室内室外", FieldType.STRING_FIELD, 7, 30, ref fTypeId);
                List<TDCell> cells = null;
                if (DrawCurrent)
                {
                    cells = mainModel.CellManager.GetCurrentTDCells();
                }
                else
                {
                    cells = mainModel.CellManager.GetTDCells(CurShowTimeAt);
                }
                double radius = 0.00048775;//大约50米
                foreach (TDCell cell in cells)
                {
                    MapWinGIS.Shape shp = null;
                    if (cell.DirectionType == TDAntennaDirectionType.Omni)
                    {
                        shp = ShapeHelper.CreateCircleShape(cell.Longitude, cell.Latitude, radius);
                    }
                    else if (cell.DirectionType == TDAntennaDirectionType.Beam)
                    {
                        shp = ShapeHelper.CreateOutdoorCellShape(cell.Longitude, cell.Latitude, cell.EndPointLongitude, cell.EndPointLatitude, cell.Direction);
                    }
                    int shpIdx = 0;
                    shpFile.EditInsertShape(shp, ref shpIdx);
                    shpFile.EditCellValue(fLongId, shpIdx, cell.Longitude);
                    shpFile.EditCellValue(fLatId, shpIdx, cell.Latitude);
                    shpFile.EditCellValue(fNameId, shpIdx, cell.Name);
                    shpFile.EditCellValue(fCodeId, shpIdx, cell.Code);
                    shpFile.EditCellValue(fLacId, shpIdx, cell.LAC);
                    shpFile.EditCellValue(fCiId, shpIdx, cell.CI);
                    shpFile.EditCellValue(fBcchId, shpIdx, cell.FREQ);
                    shpFile.EditCellValue(fBsicId, shpIdx, cell.CPI);
                    shpFile.EditCellValue(fTchId, shpIdx, cell.FreqDesc);
                    shpFile.EditCellValue(fDirectionId, shpIdx, cell.Direction);
                    shpFile.EditCellValue(fDownwordId, shpIdx, cell.Downword);
                    shpFile.EditCellValue(fBscId, shpIdx, cell.NodeBName);
                    shpFile.EditCellValue(fMscId, shpIdx, cell.RNCName);
                    shpFile.EditCellValue(fTypeId, shpIdx, cell.Type.ToString());
                }
                ShapeHelper.DeleteShpFile(filename);
                shpFile.SaveAs(filename, null);
                return 1;
            }
            catch
            {
                return -1;
            }
            finally
            {
                shpFile.Close();
            }
        }

        private static List<PointF[]> cellPoints = new List<PointF[]>();
        public List<PointF[]> CellPoints
        {
            get { return cellPoints; }
        }

        private static List<GraphicsPath> cellPaths = new List<GraphicsPath>();
        public List<GraphicsPath> CellPaths
        {
            get { return cellPaths; }
        }

        private static List<PathGradientBrush> cellPathGradientBrushs = new List<PathGradientBrush>();

        private static List<PointF[]> antennaPoints = new List<PointF[]>();
        public List<PointF[]> AntennaPoints
        {
            get { return antennaPoints; }
        }

        private static List<LayerPoint> antennaGEPoints = new List<LayerPoint>();

        private static List<GraphicsPath> antennaPaths = new List<GraphicsPath>();
        public List<GraphicsPath> AntennaPaths
        {
            get { return antennaPaths; }
        }

        #region layer properties
        public bool DrawServer { get; set; } = true;
        public bool DrawSelTDCellPair { get; set; } = false; //是否高亮小区对
        public bool DrawNonconformityCell { get; set; } = false;//是否高亮覆盖不符的小区集
        public List<string> HighlightCellList { get; set; } = new List<string>();
        public Color ColorBegin { get; set; } = Color.Red;
        public bool ColorViaEnabled { get; set; }
        public Color ColorVia { get; set; } = Color.Yellow;
        public Color ColorEnd { get; set; } = Color.Green;
        public bool DrawOutdoor { get; set; } = true;
        public bool DrawIndoor { get; set; } = true;
        public Color ColorSelected { get; set; } = Color.Red;
        public bool DrawBTS { get; set; } = false;
        public Color ColorBTS { get; set; } = Color.Black;
        public int SizeBTS { get; set; } = 6;
        public bool DrawBTSLabel { get; set; } = true;
        public Font FontBTSLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public bool DrawCell { get; set; } = false;
        public Color ColorCell { get; set; } = Color.Purple;
        public bool DrawAntenna { get; set; } = true;
        public Color ColorAntenna { get; set; } = Color.Blue;

        public bool DrawPlanningLabel { get; set; } = true;
        public bool DrawCoBCCH { get; set; } = true;
        public bool DrawCoTCH { get; set; } = true;
        public bool DrawAdjBCCH { get; set; } = true;
        public bool DrawAdjTCH { get; set; } = true;
        public Color ColorCoBCCH { get; set; } = Color.Red;
        public Color ColorCoTCH { get; set; } = Color.Orange;
        public Color ColorAdjBCCH { get; set; } = Color.Yellow;
        public Color ColorAdjTCH { get; set; } = Color.Violet;
        public Color ColorCoBSIC { get; set; } = Color.BurlyWood;
        public Color ColorNeighbour { get; set; } = Color.Cyan;
        public Color ColorNeighbourEachOther { get; set; } = Color.DarkGreen;
        public Color ColorNeighbour2G { get; set; } = Color.Red;
        public Font FontPlanningLabel { get; set; } = new Font(new FontFamily("宋体"), 9);
        public bool DrawRepeater { get; set; } = true;
        public int SizeRepeater { get; set; } = 16;
        public bool DrawLineDonarCell2Repeater { get; set; } = true;

        public override void GetObjectData(System.Runtime.Serialization.SerializationInfo info, System.Runtime.Serialization.StreamingContext context)
        {
            base.GetObjectData(info, context);
            info.AddValue("featrueMaxZoomScale", FeatrueMaxZoomScale);

            info.AddValue("drawCurrent", DrawCurrent);
            info.AddValue("drawServer", DrawServer);
            info.AddValue("colorBegin", ColorBegin);
            info.AddValue("colorViaEnabled", ColorViaEnabled);
            info.AddValue("colorVia", ColorVia);
            info.AddValue("colorEnd", ColorEnd);
            info.AddValue("drawOutdoor", DrawOutdoor);
            info.AddValue("drawIndoor", DrawIndoor);
            info.AddValue("colorSelected", ColorSelected);

            info.AddValue("drawBTS", DrawBTS);
            info.AddValue("colorBTS", ColorBTS);
            info.AddValue("sizeBTS", SizeBTS);
            info.AddValue("drawBTSLabel", DrawBTSLabel);
            info.AddValue("fontBTSLabelFontFamilyName", FontBTSLabel.FontFamily.Name);
            info.AddValue("fontBTSLabelFontSize", FontBTSLabel.Size);
            info.AddValue("fontBTSLabelFontStyle", (int)FontBTSLabel.Style);

            info.AddValue("drawCell", DrawCell);
            info.AddValue("colorCell", ColorCell);
            info.AddValue("drawAntenna", DrawAntenna);
            info.AddValue("colorAntenna", ColorAntenna);

            info.AddValue("drawPlanningLabel", DrawPlanningLabel);
            info.AddValue("drawCoBCCH", DrawCoBCCH);
            info.AddValue("drawCoTCH", DrawCoTCH);
            info.AddValue("drawAdjBCCH", DrawAdjBCCH);
            info.AddValue("drawAdjTCH", DrawAdjTCH);
            info.AddValue("colorCoBCCH", ColorCoBCCH);
            info.AddValue("colorCoTCH", ColorCoTCH);
            info.AddValue("colorAdjBCCH", ColorAdjBCCH);
            info.AddValue("colorAdjTCH", ColorAdjTCH);
            info.AddValue("colorCoBSIC", ColorCoBSIC);
            info.AddValue("colorNeighbour", ColorNeighbour);
            info.AddValue("colorNeighbourEachOther", ColorNeighbourEachOther);
            info.AddValue("colorNeighbour2G", ColorNeighbour2G);
            info.AddValue("fontPlanningLabelFontFamilyName", FontPlanningLabel.FontFamily.Name);
            info.AddValue("fontPlanningLabelFontSize", FontPlanningLabel.Size);
            info.AddValue("fontPlanningLabelFontStyle", (int)FontPlanningLabel.Style);

            info.AddValue("drawRepeater", DrawRepeater);
            info.AddValue("sizeRepeater", SizeRepeater);
            info.AddValue("drawLineDonarCell2Repeater", DrawLineDonarCell2Repeater);

            //BTS Index
            info.AddValue("drawBTSName", DrawBTSName);
            info.AddValue("drawBTSRNC", DrawBTSRNC);
            info.AddValue("drawBTSLongitude", DrawBTSLongitude);
            info.AddValue("drawBTSLatitude", DrawBTSLatitude);
            info.AddValue("drawBTSType", DrawBTSType);
            info.AddValue("drawBTSDescription", DrawBTSDescription);

            //Cell Index
            info.AddValue("drawCellLabel", DrawCellLabel);
            info.AddValue("fontCellLabelFamilyName", FontCellLabel.FontFamily.Name);
            info.AddValue("fontCellLabelFontSize", FontCellLabel.Size);
            info.AddValue("fontCellLabelFontStyle", (int)FontCellLabel.Style);
            info.AddValue("drawCellName", DrawCellName);
            info.AddValue("drawCellCode", DrawCellCode);
            info.AddValue("drawCellLAC", DrawCellLAC);
            info.AddValue("drawCellCI", DrawCellCI);
            info.AddValue("drawCellFreq", DrawCellFreq);
            info.AddValue("drawCellFreqList", DrawCellFreqList);
            info.AddValue("drawCellDes", DrawCellDes);
            info.AddValue("drawCellCPI", DrawCellCPI);

            //Antenna Index
            info.AddValue("drawAntennaLabel", DrawAntennaLabel);
            info.AddValue("fontAntennaLabelFamilyName", FontAntennaLabel.FontFamily.Name);
            info.AddValue("fontAntennaLabelFontSize", FontAntennaLabel.Size);
            info.AddValue("fontAntennaLabelFontStyle", (int)FontAntennaLabel.Style);
            info.AddValue("drawAntennaGroup", DrawAntennaGroup);
            info.AddValue("drawAntennaSN", DrawAntennaSN);
            info.AddValue("drawAntennaLongitude", DrawAntennaLongitude);
            info.AddValue("drawAntennaLatitude", DrawAntennaLatitude);
            info.AddValue("drawAntennaDirectionType", DrawAntennaDirectionType);
            info.AddValue("drawAntennaDirection", DrawAntennaDirection);
            info.AddValue("drawAntennaDownward", DrawAntennaDownward);
            info.AddValue("drawAntennaAltitude", DrawAntennaAltitude);
            info.AddValue("drawAntennaDescription", DrawAntennaDescription);
        }

        public void SetObjectData(System.Runtime.Serialization.SerializationInfo info, System.Runtime.Serialization.StreamingContext context)
        {
            FeatrueMaxZoomScale = info.GetDouble("featrueMaxZoomScale");

            DrawCurrent = info.GetBoolean("drawCurrent");
            DrawServer = info.GetBoolean("drawServer");
            ColorBegin = (Color)info.GetValue("colorBegin", typeof(Color));
            ColorViaEnabled = info.GetBoolean("colorViaEnabled");
            ColorVia = (Color)info.GetValue("colorVia", typeof(Color));
            ColorEnd = (Color)info.GetValue("colorEnd", typeof(Color));
            DrawOutdoor = info.GetBoolean("drawOutdoor");
            DrawIndoor = info.GetBoolean("drawIndoor");
            ColorSelected = (Color)info.GetValue("colorSelected", typeof(Color));

            DrawBTS = info.GetBoolean("drawBTS");
            ColorBTS = (Color)info.GetValue("colorBTS", typeof(Color));
            SizeBTS = info.GetInt32("sizeBTS");
            DrawBTSLabel = info.GetBoolean("drawBTSLabel");
            FontBTSLabel = new Font(new FontFamily(info.GetString("fontBTSLabelFontFamilyName")), info.GetSingle("fontBTSLabelFontSize"), (FontStyle)info.GetInt32("fontBTSLabelFontStyle"));

            DrawCell = info.GetBoolean("drawCell");
            ColorCell = (Color)info.GetValue("colorCell", typeof(Color));

            DrawAntenna = info.GetBoolean("drawAntenna");
            ColorAntenna = (Color)info.GetValue("colorAntenna", typeof(Color));

            DrawPlanningLabel = info.GetBoolean("drawPlanningLabel");
            DrawCoBCCH = info.GetBoolean("drawCoBCCH");
            DrawCoTCH = info.GetBoolean("drawCoTCH");
            DrawAdjBCCH = info.GetBoolean("drawAdjBCCH");
            DrawAdjTCH = info.GetBoolean("drawAdjTCH");
            ColorCoBCCH = (Color)info.GetValue("colorCoBCCH", typeof(Color));
            ColorCoTCH = (Color)info.GetValue("colorCoTCH", typeof(Color));
            ColorAdjBCCH = (Color)info.GetValue("colorAdjBCCH", typeof(Color));
            ColorAdjTCH = (Color)info.GetValue("colorAdjTCH", typeof(Color));
            ColorCoBSIC = (Color)info.GetValue("colorCoBSIC", typeof(Color));
            ColorNeighbour = (Color)info.GetValue("colorNeighbour", typeof(Color));
            ColorNeighbourEachOther = (Color)info.GetValue("colorNeighbourEachOther", typeof(Color));
            ColorNeighbour2G = (Color)info.GetValue("colorNeighbour2G", typeof(Color));
            FontPlanningLabel = new Font(new FontFamily(info.GetString("fontPlanningLabelFontFamilyName")), info.GetSingle("fontPlanningLabelFontSize"), (FontStyle)info.GetInt32("fontPlanningLabelFontStyle"));

            DrawRepeater = info.GetBoolean("drawRepeater");
            SizeRepeater = info.GetInt32("sizeRepeater");
            DrawLineDonarCell2Repeater = info.GetBoolean("drawLineDonarCell2Repeater");

            //BTS Index
            DrawBTSName = info.GetBoolean("drawBTSName");
            DrawBTSRNC = info.GetBoolean("drawBTSRNC");
            DrawBTSLongitude = info.GetBoolean("drawBTSLongitude");
            DrawBTSLatitude = info.GetBoolean("drawBTSLatitude");
            DrawBTSType = info.GetBoolean("drawBTSType");
            DrawBTSDescription = info.GetBoolean("drawBTSDescription");

            //Cell Index
            DrawCellLabel = info.GetBoolean("drawCellLabel");
            FontCellLabel = new Font(new FontFamily(info.GetString("fontCellLabelFamilyName")), info.GetSingle("fontCellLabelFontSize"), (FontStyle)info.GetInt32("fontCellLabelFontStyle"));
            DrawCellName = info.GetBoolean("drawCellName");
            DrawCellCode = info.GetBoolean("drawCellCode");
            DrawCellLAC = info.GetBoolean("drawCellLAC");
            DrawCellCI = info.GetBoolean("drawCellCI");
            DrawCellFreq = info.GetBoolean("drawCellFreq");
            DrawCellFreqList = info.GetBoolean("drawCellFreqList");
            DrawCellDes = info.GetBoolean("drawCellDes");
            DrawCellCPI = info.GetBoolean("drawCellCPI");

            //Antenna Index
            DrawAntennaLabel = info.GetBoolean("drawAntennaLabel");
            FontAntennaLabel = new Font(new FontFamily(info.GetString("fontAntennaLabelFamilyName")), info.GetSingle("fontAntennaLabelFontSize"), (FontStyle)info.GetInt32("fontAntennaLabelFontStyle"));
            DrawAntennaGroup = info.GetBoolean("drawAntennaGroup");
            DrawAntennaSN = info.GetBoolean("drawAntennaSN");
            DrawAntennaLongitude = info.GetBoolean("drawAntennaLongitude");
            DrawAntennaLatitude = info.GetBoolean("drawAntennaLatitude");
            DrawAntennaDirectionType = info.GetBoolean("drawAntennaDirectionType");
            DrawAntennaDirection = info.GetBoolean("drawAntennaDirection");
            DrawAntennaDownward = info.GetBoolean("drawAntennaDownward");
            DrawAntennaAltitude = info.GetBoolean("drawAntennaAltitude");
            DrawAntennaDescription = info.GetBoolean("drawAntennaDescription");

            makeBrushes();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["featrueMaxZoomScale"] = FeatrueMaxZoomScale;
                param["drawServer"] = DrawServer;
                param["colorBegin"] = ColorBegin.ToArgb();
                param["colorViaEnabled"] = ColorViaEnabled;
                param["colorVia"] = ColorVia.ToArgb();
                param["colorEnd"] = ColorEnd.ToArgb();
                param["drawIndoor"] = DrawIndoor;
                param["drawOutdoor"] = DrawOutdoor;
                param["colorSelected"] = ColorSelected.ToArgb();

                //BTS
                param["drawBTS"] = DrawBTS;
                param["colorBTS"] = ColorBTS.ToArgb();
                param["sizeBTS"] = SizeBTS;
                param["drawBTSLabel"] = DrawBTSLabel;
                param["fontBTSLabelFontFamilyName"] = FontBTSLabel.FontFamily.Name;
                param["fontBTSLabelFontSize"] = FontBTSLabel.Size;
                param["fontBTSLabelFontStyle"] = (int)FontBTSLabel.Style;
                param["drawBTSName"] = DrawBTSName;
                param["drawBTSRNC"] = DrawBTSRNC;
                param["drawBTSLongitude"] = DrawBTSLongitude;
                param["drawBTSLatitude"] = DrawBTSLatitude;
                param["drawBTSType"] = DrawBTSType;
                param["drawBTSDescription"] = DrawBTSDescription;

                //cell
                param["drawCell"] = DrawCell;
                param["colorCell"] = ColorCell.ToArgb();
                param["fontCellLabelFamilyName"] = FontCellLabel.FontFamily.Name;
                param["fontCellLabelFontSize"] = FontCellLabel.Size;
                param["fontCellLabelFontStyle"] = (int)FontCellLabel.Style;
                param["drawCellLabel"] = DrawCellLabel;
                param["drawCellName"] = DrawCellName;
                param["drawCellCode"] = DrawCellCode;
                param["drawCellLAC"] = DrawCellLAC;
                param["drawCellCI"] = DrawCellCI;
                param["drawCellFreq"] = DrawCellFreq;
                param["drawCellFreqList"] = DrawCellFreqList;
                param["drawCellDes"] = DrawCellDes;
                param["drawCellCPI"] = DrawCellCPI;

                //Antenna
                param["drawAntenna"] = DrawAntenna;
                param["colorAntenna"] = ColorAntenna.ToArgb();
                param["drawAntennaLabel"] = DrawAntennaLabel;
                param["fontAntennaLabelFamilyName"] = FontAntennaLabel.FontFamily.Name;
                param["fontAntennaLabelFontSize"] = FontAntennaLabel.Size;
                param["fontAntennaLabelFontStyle"] = (int)FontAntennaLabel.Style;
                param["drawAntennaGroup"] = DrawAntennaGroup;
                param["drawAntennaSN"] = DrawAntennaSN;
                param["drawAntennaLongitude"] = DrawAntennaLongitude;
                param["drawAntennaLatitude"] = DrawAntennaLatitude;
                param["drawAntennaDirectionType"] = DrawAntennaDirectionType;
                param["drawAntennaDirection"] = DrawAntennaDirection;
                param["drawAntennaDownward"] = DrawAntennaDownward;
                param["drawAntennaAltitude"] = DrawAntennaAltitude;
                param["drawAntennaDescription"] = DrawAntennaDescription;
                param["colorNeighbour"] = ColorNeighbour.ToArgb();
                param["colorNeighbourEachOther"] = ColorNeighbourEachOther.ToArgb();
                param["colorNeighbour2G"] = ColorNeighbour2G.ToArgb();
                param["isEnabled"] = IsVisible;
                return param;
            }
            set
            {
                try
                {
                    FeatrueMaxZoomScale = (double)value["featrueMaxZoomScale"];
                    DrawServer = (bool)value["drawServer"];
                    ColorBegin = Color.FromArgb((int)value["colorBegin"]);
                    ColorViaEnabled = (bool)value["colorViaEnabled"];
                    ColorVia = Color.FromArgb((int)value["colorVia"]);
                    ColorEnd = Color.FromArgb((int)value["colorEnd"]);
                    DrawIndoor = (bool)value["drawIndoor"];
                    DrawOutdoor = (bool)value["drawOutdoor"];
                    ColorSelected = Color.FromArgb((int)value["colorSelected"]);

                    DrawBTS = (bool)value["drawBTS"];
                    ColorBTS = Color.FromArgb((int)value["colorBTS"]);
                    SizeBTS = (int)value["sizeBTS"];
                    DrawBTSLabel = (bool)value["drawBTSLabel"];
                    FontBTSLabel = new Font(new FontFamily((String)value["fontBTSLabelFontFamilyName"]), (float)value["fontBTSLabelFontSize"], (FontStyle)(int)value["fontBTSLabelFontStyle"]);
                    try
                    {
                        DrawBTSName = (bool)value["drawBTSName"];
                        DrawBTSRNC = (bool)value["drawBTSRNC"];
                        DrawBTSLongitude = (bool)value["drawBTSLongitude"];
                        DrawBTSLatitude = (bool)value["drawBTSLatitude"];
                        DrawBTSType = (bool)value["drawBTSType"];
                        DrawBTSDescription = (bool)value["drawBTSDescription"];

                        DrawCellLabel = (bool)value["drawCellLabel"];
                        DrawCellName = (bool)value["drawCellName"];
                        DrawCellCode = (bool)value["drawCellCode"];
                        DrawCellLAC = (bool)value["drawCellLAC"];
                        DrawCellCI = (bool)value["drawCellCI"];
                        DrawCellFreq = (bool)value["drawCellFreq"];
                        DrawCellFreqList = (bool)value["drawCellFreqList"];
                        DrawCellDes = (bool)value["drawCellDes"];
                        DrawCellCPI = (bool)value["drawCellCPI"];

                        DrawAntennaLabel = (bool)value["drawAntennaLabel"];
                        FontAntennaLabel = new Font(new FontFamily((string)value["fontAntennaLabelFamilyName"]), (float)value["fontAntennaLabelFontSize"], (FontStyle)(int)value["fontAntennaLabelFontStyle"]);
                        DrawAntennaGroup = (bool)value["drawAntennaGroup"];
                        DrawAntennaSN = (bool)value["drawAntennaSN"];
                        DrawAntennaLongitude = (bool)value["drawAntennaLongitude"];
                        DrawAntennaLatitude = (bool)value["drawAntennaLatitude"];
                        DrawAntennaDirectionType = (bool)value["drawAntennaDirectionType"];
                        DrawAntennaDirection = (bool)value["drawAntennaDirection"];
                        DrawAntennaDownward = (bool)value["drawAntennaDownward"];
                        DrawAntennaAltitude = (bool)value["drawAntennaAltitude"];
                        DrawAntennaDescription = (bool)value["drawAntennaDescription"];
                    }
                    catch
                    {
                        //continue
                    }

                    DrawCell = (bool)value["drawCell"];
                    ColorCell = Color.FromArgb((int)value["colorCell"]);
                    DrawCellLabel = (bool)value["drawCellLabel"];

                    DrawAntenna = (bool)value["drawAntenna"];
                    ColorAntenna = Color.FromArgb((int)value["colorAntenna"]);
                    ColorNeighbour = Color.FromArgb((int)value["colorNeighbour"]);
                    ColorNeighbourEachOther = Color.FromArgb((int)value["colorNeighbourEachOther"]);
                    ColorNeighbour2G = Color.FromArgb((int)value["colorNeighbour2G"]);

                    this.Enabled = (bool)value["isEnabled"];
                }
                catch
                {
                    //continue
                }
                makeBrushes();
            }
        }
        
        private readonly List<Brush> brushesServer = new List<Brush>();
        public List<Brush> BrushesServer
        {
            get { return brushesServer; }
        }

        private Pen penSelected = new Pen(Color.Red, 1);
        public Pen PenSelected
        {
            get { return penSelected; }
        }

        private readonly Pen penSelectedNB = new Pen(Color.Pink, 1);
        public Pen PenSelectedNB
        {
            get { return penSelectedNB; }
        }

        private Pen penBTS = new Pen(Color.Black, 1);
        public Pen PenBTS
        {
            get { return penBTS; }
        }

        private Brush brushCell = new SolidBrush(Color.Purple);
        public Brush BrushCell
        {
            get { return brushCell; }
        }

        private Brush brushTDNeighbourTDCell = new SolidBrush(Color.LimeGreen);
        public Brush BrushTDNeighbourTDCell
        {
            get { return brushTDNeighbourTDCell; }
        }

        private readonly Brush brushJamCell = new SolidBrush(Color.Maroon);//干扰小区颜色（簇优化）
        public Brush BrushJamCell
        {
            get { return brushJamCell; }
        }

        private readonly Brush brushAlarmCell = new SolidBrush(Color.Red);
        public Brush BrushAlarmCell
        {
            get { return brushAlarmCell; }
        }

        private readonly Brush brushOrgCells = new SolidBrush(Color.Orange);//原小区（被干扰小区）颜色（簇优化）
        public Brush BrushOrgCells
        {
            get { return brushOrgCells; }
        }

        private Brush brushAntenna = new SolidBrush(Color.Blue);
        public Brush BrushAntenna
        {
            get { return brushAntenna; }
        }
        private Pen penAntenna = new Pen(Brushes.Blue, 2);
        public Pen PenAntenna
        {
            get { return penAntenna; }
        }
        
        private Brush brushCoBCCH = new SolidBrush(Color.Red);
        private Brush brushCoTCH = new SolidBrush(Color.Orange);
        private Brush brushAdjBCCH { get; set; } = new SolidBrush(Color.Yellow);
        private Brush brushAdjTCH { get; set; } = new SolidBrush(Color.Violet);
        private Brush brushCoBSIC { get; set; } = new SolidBrush(Color.BurlyWood);
        //private Brush brushNeighbour = new SolidBrush(Color.LimeGreen)

        //BTS指标显示设置 
        //Hui 2010.07.24
        public bool DrawBTSName { get; set; } = true;
        public bool DrawBTSRNC { get; set; } = false;
        public bool DrawBTSLongitude { get; set; } = false;
        public bool DrawBTSLatitude { get; set; } = false;
        public bool DrawBTSType { get; set; } = false;
        public bool DrawBTSDescription { get; set; } = false;

        //Cell指标显示设置 
        //Hui 2010.07.24
        public bool DrawCellLabel { get; set; } = false;
        public Font FontCellLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public bool DrawCellName { get; set; } = true;
        public bool DrawCellCode { get; set; } = false;
        public bool DrawCellLAC { get; set; } = false;
        public bool DrawCellCI { get; set; } = false;
        public bool DrawCellFreq { get; set; } = false;
        public bool DrawCellFreqList { get; set; } = false;
        public bool DrawCellDes { get; set; } = false;
        public bool DrawCellCPI { get; set; } = false;

        //Antenna指标显示设置 
        //Hui 2010.07.24
        public bool DrawAntennaLabel { get; set; } = false;
        public Font FontAntennaLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public bool DrawAntennaGroup { get; set; } = false;
        public bool DrawAntennaSN { get; set; } = false;
        public bool DrawAntennaLongitude { get; set; } = false;
        public bool DrawAntennaLatitude { get; set; } = false;
        public bool DrawAntennaDirectionType { get; set; } = false;
        public bool DrawAntennaDirection { get; set; } = false;
        public bool DrawAntennaDownward { get; set; } = false;
        public bool DrawAntennaAltitude { get; set; } = false;
        public bool DrawAntennaDescription { get; set; } = false;
        public bool DrawBTSsLine { get; set; } = false;//站间线
        public Color BtssLineColor { get; set; } = Color.Orange;
        public int BtssLineWidth { get; set; } = 1;
        public double BtssDistance { get; set; } = 1000;
        public Color BTSLineLblColor { get; set; }
        public double BtsLineAngle { get; set; }

        #endregion

        #region IKMLExport 成员

        void IKMLExport.ExportKml(KMLExporter exporter, XmlElement parentElem)
        {
            XmlElement layerElement = exporter.CreateFolder("小区信息", false);
            parentElem.AppendChild(layerElement);

            if (DrawAntenna && mapScale < 100000)
            {
                List<TDAntenna> antennas = null;
                if (DrawCurrent)
                {
                    antennas = mainModel.CellManager.GetCurrentTDAntennas();
                }
                else
                {
                    antennas = mainModel.CellManager.GetTDAntennas(CurShowTimeAt);
                }
                if (antennas != null)
                {
                    foreach (TDAntenna antenna in antennas)
                    {
                        int index = 0;
                        string ColorCode;
                        if (antenna.DirectionType == TDAntennaDirectionType.Omni)
                        {
                            index = 0;
                            ColorCode = "FFCC00AA";
                        }
                        else
                        {
                            index = 1;
                            ColorCode = "FFCC00AA";
                        }
                        layerElement.AppendChild(TransformPoint(antenna, index, exporter.getRootNode(), ColorCode));
                    }
                }
            }
        }

        #endregion
    }
}
