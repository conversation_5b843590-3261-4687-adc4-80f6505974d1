﻿using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDeviceManageQueryByRegion : ZTDeviceManageQueryBase
    {
        public ZTDeviceManageQueryByRegion()
            : base()
        {
        }

        #region 基础数据重写
        public override string Name
        {
            get { return "设备信息管理(按区域)"; }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        #endregion

        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }

            //查询区域内文件
            DIYQueryFileInfoByRegion queryFileByReg = new DIYQueryFileInfoByRegion(MainModel);
            queryFileByReg.IsShowFileInfoForm = false;
            queryFileByReg.SetQueryCondition(condition);
            queryFileByReg.Query();
            condition.FileInfos = mainModel.FileInfos;

            foreach (int districtID in condition.DistrictIDs)//根据地市id找文件
            {
                condition.DistrictID = districtID;
                queryDistrictData(districtID);
            }
            afterRecieveAllData();
            if (IsShowResultForm)
            {
                fireShowResult();
            }
        }
    }

    public class ZTDeviceManageQueryByFile : ZTDeviceManageQueryBase
    {
        public ZTDeviceManageQueryByFile()
            : base()
        {
        }

        #region 基础数据重写
        public override string Name
        {
            get { return "设备信息管理(按文件)"; }
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                mainModel.FileInfos = Condition.FileInfos;
                return true;
            }
            return false;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        #endregion
    }

    public class ZTDeviceManageQueryBase : QueryKPIStatBase
    {
        /// <summary>
        /// 数据库中保存的设备信息,key为设备号
        /// </summary>
        protected Dictionary<string, DeviceManageInfo> dbDeviceInfoDic = null;
        /// <summary>
        /// 匹配的设备结果集
        /// </summary>
        protected Dictionary<string, ZTDeviceManageInfo> resDeviceInfoDic = null;
        protected FileInfo curAnalyzedFile = null;

        #region 业务类型分类
        //2G业务类型
        protected ServiceType[] serviceType2G = new ServiceType[] { ServiceType.GSM_VOICE, ServiceType.GPRS_DATA,
            ServiceType.EDGE_DATA, ServiceType.GSM_SCAN, ServiceType.GSM_IDLE, ServiceType.GSM_MOS,
            ServiceType.GSM_MTR, ServiceType.GSM_CALLTRACE, ServiceType.GSM扫频_频谱分析 };
        //3G业务类型
        protected ServiceType[] serviceType3G = new ServiceType[] { ServiceType.TDSCDMA_VOICE, ServiceType.TDSCDMA_DATA,
            ServiceType.CDMA_VOICE, ServiceType.CDMA1X_DATA, ServiceType.CDMA2000_VOICE, ServiceType.CDMA2000_DATA,
            ServiceType.WCDMA_VOICE, ServiceType.WCDMA_DATA, ServiceType.TDSCDMA_VIDEO, ServiceType.WCDMA_VIDEO,
            ServiceType.WCDMA_HSDPA, ServiceType.CDMA2000_VIDEO, ServiceType.TDSCDMA_IDLE, ServiceType.WCDMA_SCAN,
            ServiceType.WCDMA_IDLE, ServiceType.CDMA_IDLE, ServiceType.TDSCDMA_HSUPA, ServiceType.WCDMA_HSUPA,
            ServiceType.CDMA_MOS, ServiceType.TD扫频_频谱分析, ServiceType.CDMA2000_IDLE };
        //4G业务类型
        protected ServiceType[] serviceType4G = new ServiceType[] { ServiceType.LTE_TDD_VOICE, ServiceType.LTE_TDD_DATA,
            ServiceType.LTE_SCAN_TOPN, ServiceType.LTE_SCAN_CW, ServiceType.LTE扫频_频谱分析, ServiceType.LTE_TDD_IDLE,
            ServiceType.LTE_TDD_MULTI, ServiceType.LTE_TDD_VOLTE, ServiceType.LTE_TDD_UEP, ServiceType.LTE_FDD_VOICE,
            ServiceType.LTE_FDD_DATA, ServiceType.LTE_FDD_IDLE, ServiceType.LTE_FDD_MULTI, ServiceType.LTE_FDD_VOLTE,
            ServiceType.LTE_SIGNAL, ServiceType.SER_LTE_TDD_VIDEO_VOLTE, ServiceType.SER_LTE_FDD_VIDEO_VOLTE };
        #endregion

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.log;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18049, this.Name);
        }

        public override string Name
        {
            get { return "设备信息管理"; }
        }

        protected override void fireShowResult()
        {
            ZTDeviceManageResultForm frm = MainModel.GetInstance().CreateResultForm(typeof(ZTDeviceManageResultForm)) as ZTDeviceManageResultForm;
            frm.FillData(new List<ZTDeviceManageInfo>(resDeviceInfoDic.Values));
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override bool getConditionBeforeQuery()
        {
            isQueryAllParams = false;
            KpiDataManager = new KPIDataManager();

            ZTDeviceManageDlg dlg = new ZTDeviceManageDlg();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                dbDeviceInfoDic = dlg.DeviceInfoDic;
                resDeviceInfoDic = new Dictionary<string, ZTDeviceManageInfo>();
                return true;
            }
            return false;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            //由于该功能只需要里程时长,image1对应的里程时长全为0805和0806,故添加一个0805保证查询Image1字段就行
            formulaSet.Add("{Lte_0805 }");
            string triadIDSet = this.getTriadIDIgnoreServiceType(formulaSet);
            return triadIDSet;
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            //
        }

        protected override void queryInThread(object o)
        {
            System.Threading.Thread.Sleep(100);
            WaitBox.Text = "开始查询KPI统计数据...";
            WaitBox.CanCancel = true;
            ClientProxy clientProxy = (ClientProxy)o;
            try
            {
                string imgTriadIDSet = getStatImgNeededTriadID();
                int idx = 1;
                foreach (FileInfo file in condition.FileInfos)
                {
                    if (file.DistrictID != clientProxy.DbID)
                    {
                        continue;
                    }
                    curAnalyzedFile = file;
                    WaitBox.Text = "(" + (idx++) + "/" + condition.FileInfos.Count + ")正在统计文件[" + file.Name + "]...";
                    WaitBox.ProgressPercent += 10;
                    //添加一个初始数据,用于"按文件分开统计"时显示没有数据的文件
                    Event evt = new Event();
                    handleStatEvent(evt);
                    queryPeriodInfo(null, clientProxy, imgTriadIDSet, file);
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    if (WaitBox.ProgressPercent >= 95)
                    {
                        WaitBox.ProgressPercent = 0;
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                curAnalyzedFile = null;
                System.Threading.Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        #region 添加查询条件
        protected override void preparePackageCondition(Package package, TimePeriod period, params object[] reservedParams)
        {
            FileInfo fi = reservedParams[1] as FileInfo;
            TimePeriod logTbPeriod;
            DateTime bTime = getFirstDateFromLogTbName(fi.LogTable);
            DateTime eTime = bTime.AddMonths(1).AddSeconds(-1);
            logTbPeriod = new TimePeriod(bTime, eTime);
            AddDIYPeriod(package, logTbPeriod);
            AddDIYFileID(package, fi.ID);
            AddDIYStatStatus(package);
            AddGeographicFilter(package);
        }

        private DateTime getFirstDateFromLogTbName(string strname)
        {
            string[] vec = strname.Split('_');
            if (vec.Length == 5)
            {
                int year;
                int month;
                int.TryParse(vec[3], out year);
                int.TryParse(vec[4], out month);
                return new DateTime(year, month, 1);
            }
            throw (new Exception("格式错误，时间获取失败！"));
        }

        protected override void preparePackageCommand(Net.Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_LOG_KPI;
                package.Content.PrepareAddParam();
            }
        }

        protected void AddDIYFileID(Package package, int fileID)
        {
            package.Content.AddParam((byte)OpOptionDef.Equal);
            package.Content.AddParam("0,1,1");//fileid
            package.Content.AddParam("" + fileID);
        }
        #endregion

        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            try
            {
                fillStatData(package, curImgColumnDef, singleStatData);
            }
            catch
            {
                //部分异常栅格数据可能导致解码报错
                log.Info(string.Format("[{0}]的栅格文件维度数据解码失败", curAnalyzedFile.Name));
                return;
            }

            string deviceNum = getDeviceNumByFileName(curAnalyzedFile.Name);
            DeviceManageInfo curDevice = null;
            bool hasDeviceInDB = judgeDeviceInDB(deviceNum, out curDevice);
            if (!hasDeviceInDB)
            {
                return;
            }

            double duration = singleStatData.GetValue("0805", null, null);
            double distance = singleStatData.GetValue("0806", null, null);
            setDeviceManageInfo(duration, distance, deviceNum, curDevice);
        }

        private bool judgeDeviceInDB(string deviceNum, out DeviceManageInfo curDevice)
        {
            if (dbDeviceInfoDic.TryGetValue(deviceNum, out curDevice))
            {
                return true;
            }
            return false;
        }

        private void setDeviceManageInfo(double duration, double distance, string deviceNum, DeviceManageInfo curDevice)
        {
            ZTDeviceManageInfo curDeviceManageInfo = null;
            if (!resDeviceInfoDic.TryGetValue(deviceNum, out curDeviceManageInfo))
            {
                curDeviceManageInfo = new ZTDeviceManageInfo();
                resDeviceInfoDic.Add(deviceNum, curDeviceManageInfo);

                #region 匹配设备信息表中的基础信息
                curDeviceManageInfo.BoxID = curDevice.BoxID;
                curDeviceManageInfo.Area = curDevice.Area;
                curDeviceManageInfo.Vendor = curDevice.Vendor;
                curDeviceManageInfo.DeviceStatus = curDevice.DeviceStatus;
                #endregion
            }

            #region 记录设备每天使用的次数
            string date = JavaDate.GetDateTimeFromMilliseconds(curAnalyzedFile.BeginTime * 1000L).ToString("yyyyMMdd");
            int times = 0;
            if (!curDeviceManageInfo.DateFrequency.TryGetValue(date, out times))
            {
                curDeviceManageInfo.DateFrequency.Add(date, 1);
            }
            else
            {
                curDeviceManageInfo.DateFrequency[date] = times + 1;
            }
            #endregion

            setDeviceCarrierInfo(duration, distance, curDeviceManageInfo);
        }

        private void setDeviceCarrierInfo(double duration, double distance, ZTDeviceManageInfo curDeviceManageInfo)
        {
            CarrierType carrierType = (CarrierType)curAnalyzedFile.CarrierType;
            ZTDeviceManageCarrierInfo carrierInfo = null;
            if (!curDeviceManageInfo.ManageCarrierInfoDic.TryGetValue(carrierType, out carrierInfo))
            {
                carrierInfo = new ZTDeviceManageCarrierInfo();
                curDeviceManageInfo.ManageCarrierInfoDic.Add(carrierType, carrierInfo);
            }
            carrierInfo.SetCarrierType(carrierType);
            ServiceType typeEnum = (ServiceType)curAnalyzedFile.ServiceType;
            //根据不同的业务类型添加对应2,3,4G数据,根据现有业务的使用频率,先判断4G,再2G,3G
            if (Array.IndexOf(serviceType4G, typeEnum) >= 0)
            {
                carrierInfo.Duration_4G += setDuration_Hour(duration);
                carrierInfo.Distance_4G += setDistance_KM(distance);
            }
            else if (Array.IndexOf(serviceType2G, typeEnum) >= 0)
            {
                carrierInfo.Duration_2G += setDuration_Hour(duration);
                carrierInfo.Distance_2G += setDistance_KM(distance);
            }
            else if (Array.IndexOf(serviceType3G, typeEnum) >= 0)
            {
                carrierInfo.Duration_3G += setDuration_Hour(duration);
                carrierInfo.Distance_3G += setDistance_KM(distance);
            }
            curDeviceManageInfo.SetResult();
        }

        private double setDuration_Hour(double duration)
        {
            double res = Math.Round(duration / 3600000, 2);
            return res;
        }

        private double setDistance_KM(double distance)
        {
            double res = Math.Round(distance / 1000, 2);
            return res;
        }

        protected override void handleStatEvent(Model.Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            KpiDataManager.AddStatData(string.Empty, curAnalyzedFile, curAnalyzedFile, eventData, false);
        }

        /// <summary>
        /// 判断是否为设备号,8位且不能转为年月日格式的字符串视为设备号
        /// 现有设备号基本以0开头的是ATU设备的,9开头的是苹果手机做CSFB的
        /// </summary>
        /// <param name="devNum"></param>
        /// <returns></returns>
        private bool judgeDeviceNum(string devNum)
        {
            if (devNum.Length == 8)
            {
                DateTime dt;
                //判断是否为年月日,且年以20开头
                bool isDate = devNum.StartsWith("20") && DateTime.TryParse(string.Format("{0}-{1}-{2}", devNum.Substring(0, 4), devNum.Substring(4, 2), devNum.Substring(6, 2)), out dt);
                if (!isDate)
                {
                    return true;
                }
            }
            return false;
        }

        private string getDeviceNumByFileName(string fileName)
        {
            StringBuilder devNum = new StringBuilder();
            for (int i = 0; i < fileName.Length; i++)
            {
                char c = fileName[i];
                if (Char.IsDigit(c))
                {
                    devNum .Append( fileName[i]);
                    bool isDevNum = judgeDeviceNum(devNum.ToString());
                    if (isDevNum)
                    {
                        return devNum.ToString();
                    }
                    if (devNum.Length == 8)
                    {
                        devNum = new StringBuilder();
                    }
                }
                else
                {
                    devNum = new StringBuilder();
                }
            }

            return fileName;
        }
    }

    public class ZTDeviceManageInfo
    {
        public ZTDeviceManageInfo()
        {
            BoxID = "";
            Vendor = "";
            Area = "";
            DeviceStatus = "正常";
            DateFrequency = new Dictionary<string, int>();
            ManageCarrierInfoDic = new Dictionary<CarrierType, ZTDeviceManageCarrierInfo>();
        }

        /// <summary>
        /// BOX_ID
        /// </summary>
        public string BoxID { get; set; }
        /// <summary>
        /// 厂商
        /// </summary>
        public string Vendor { get; set; }
        /// <summary>
        /// 所属域
        /// </summary>
        public string Area { get; set; }
        /// <summary>
        /// 设备状态 - 设备短缺、设备维修、设备报废、设备借调。（初始状态为“正常”）
        /// </summary>
        public string DeviceStatus { get; set; }
        /// <summary>
        /// 使用频次
        /// </summary>
        public int Frequency
        {
            get { return DateFrequency.Count; }
        }

        /// <summary>
        /// 使用频次
        /// </summary>
        public Dictionary<string, int> DateFrequency { get; set; }

        public Dictionary<CarrierType, ZTDeviceManageCarrierInfo> ManageCarrierInfoDic { get; set; }

        private List<ZTDeviceManageCarrierInfo> manageCarrierInfo = null;
        /// <summary>
        /// 设备-运营商信息
        /// </summary>
        public List<ZTDeviceManageCarrierInfo> ManageCarrierInfo
        {
            get { return manageCarrierInfo; }
        }

        public void SetResult()
        {
            manageCarrierInfo = new List<ZTDeviceManageCarrierInfo>(ManageCarrierInfoDic.Values);
            //倒序排序,让移动在第一个
            manageCarrierInfo.Sort((a, b) => b.CarrierStr.CompareTo(a.CarrierStr));
        }
    }

    public class ZTDeviceManageCarrierInfo
    {
        public ZTDeviceManageCarrierInfo()
        {
            Distance_2G = 0;
            Distance_3G = 0;
            Distance_4G = 0;
            Duration_2G = 0;
            Duration_3G = 0;
            Duration_4G = 0;
        }

        public void SetCarrierType(CarrierType carrier)
        {
            switch (carrier)
            {
                case CarrierType.ChinaMobile:
                    carrierStr = "中国移动";
                    break;
                case CarrierType.ChinaUnicom:
                    carrierStr = "中国联通";
                    break;
                case CarrierType.ChinaTelecom:
                    carrierStr = "中国电信";
                    break;
            }
        }

        private string carrierStr;
        /// <summary>
        /// 运营商文字信息
        /// </summary>
        public string CarrierStr
        {
            get { return carrierStr; }
        }
        /// <summary>
        /// 2G里程(公里)
        /// </summary>
        public double Distance_2G { get; set; }
        /// <summary>
        /// 3G里程(公里)
        /// </summary>
        public double Distance_3G { get; set; }
        /// <summary>
        /// 4G里程(公里)
        /// </summary>
        public double Distance_4G { get; set; }
        /// <summary>
        /// 2G时长(小时)
        /// </summary>
        public double Duration_2G { get; set; }
        /// <summary>
        /// 3G时长(小时)
        /// </summary>
        public double Duration_3G { get; set; }
        /// <summary>
        /// 4G时长(小时)
        /// </summary>
        public double Duration_4G { get; set; }
    }
}
