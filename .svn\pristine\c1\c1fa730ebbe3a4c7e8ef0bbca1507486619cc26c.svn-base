﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRHandOverAnaByRegion : NRHandOverAnaBase
    {
        public NRHandOverAnaByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected static readonly object lockObj = new object();
        private static NRHandOverAnaByRegion intance = null;
        public static NRHandOverAnaByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRHandOverAnaByRegion(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "NR切换前后指标分析(按区域)"; }
        }
        
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35025, this.Name);
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is TestPoint_NR)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}