﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public static class DualBandGetter
    {
        public static DualBand GetDualBand(LTECell curCell, DateTime dtValid)
        {
            if (curCell == null)
                return null;

            DualBand dualBand = new DualBand(curCell);

            dualBand.AddRangeCells(NearestCellsGetter.GetNearestLteCells(curCell.Longitude, 
                curCell.Latitude, 
                dtValid, 
                50));

            return dualBand;
        }
    }

    public class DualBand
    {
        public LTECell CurCell { get; set; }

        public List<LTECell> LstNearestCells { get; set; }

        public EDualBand EDualBandType
        {
            get
            {
                EDualBand rtBand = EDualBand.Normal;

                if (LstNearestCells.Count < 5)
                    return rtBand;

                Dictionary<int, bool> dicEarfcn = new Dictionary<int, bool>();
                foreach (LTECell cell in LstNearestCells)
                {
                    dicEarfcn[cell.EARFCN] = true;
                }

                if (dicEarfcn.Count == 2)
                    rtBand = EDualBand.DualBand;
                else if (dicEarfcn.Count > 2)
                    rtBand = EDualBand.MultiBand;

                return rtBand;
            }
        }

        public EDualBand EDualBandTypeJT
        {
            get
            {
                //多层网判断算法：剔除室内小区后，将50米距离内的小区汇聚成1个物理站点
                //如果此物理站点所包含的小区数>=5，则为多层网站点

                EDualBand rtBand = EDualBand.Normal;

                if (LstNearestCells.Count < 5)
                    return rtBand;
                else
                    return EDualBand.MultiBand;
            }
        }

        public DualBand(LTECell cell)
        {
            this.CurCell = cell;
            this.LstNearestCells = new List<LTECell>();
        }

        public void AddRangeCells(List<LTECell> cells)
        {
            foreach (LTECell cell in cells)
            {
                if (cell.Type != LTEBTSType.Indoor)
                {
                    AddCell(cell);
                }
            }
        }

        public void AddCell(LTECell cell)
        {
            if (cell.Type == LTEBTSType.Indoor)
                return;

            LstNearestCells.Add(cell);
        }
    }

    public enum EDualBand
    {
        DualBand = 1,
        MultiBand,
        Normal,
    }
}
