﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Data.OleDb;

using MasterCom.RAMS.Stat;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.NOP
{
    public partial class GroupStatSetDlg : BaseDialog
    {
        public GroupStatSetDlg()
        {
            InitializeComponent();
        }
       
        public List<GroupStatModel> groupStatModels { get; set; }

        public List<GroupStatModel> GetGroupStatModels()
        {
            return groupStatModels;
        }

        public bool DealTime { get; set; } = false;
        public void getCondition(out string strWhere)
        {
            DealTime = rdbDealTime.Checked;
            StringBuilder where = new StringBuilder();
            string key = "b.工单月份";
            if (rdbDealTime.Checked)
            {
                key = "a.time";
            }

            where.Append(" " + key + " >= '");
            where.Append(dtpBegin.Value);
            where.Append("' and " + key + " <= '");
            where.Append(dtpEnd.Value);
            where.Append("' ");
   
            strWhere = where.ToString();
        }
        public void getCondition(out string strWhere,ref DateTime beginTime,ref DateTime endTime)
        {
            StringBuilder where = new StringBuilder();
            string key = "b.工单月份";
            if (rdbDealTime.Checked)
            {
                key = "a.time";
            }
            beginTime = dtpBegin.Value;
            endTime = dtpEnd.Value;
            where.Append(" " + key + " >= '");
            where.Append(dtpBegin.Value);
            where.Append("' and " + key + " <= '");
            where.Append(dtpEnd.Value);
            where.Append("' ");

            strWhere = where.ToString();
        }      
        public void getCondition1(out string strWhere)
        {
            StringBuilder where = new StringBuilder();
            string key = "b.工单月份";
            if (rdbDealTime.Checked)
            {
                key = "b.工单最后更新时间";
            }

            where.Append(" " + key + " >= '");
            where.Append(dtpBegin.Value);
            where.Append("' and " + key + " <= '");
            where.Append(dtpEnd.Value);
            where.Append("' ");

            strWhere = where.ToString();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        

    }
}
