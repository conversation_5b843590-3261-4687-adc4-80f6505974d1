﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteDriveSpeedWithKpiAnaBase : DIYAnalyseByFileBackgroundBase
    {
        protected RoadKpiCondition roadSetCondition = null;
        protected List<DriveSpeedKpiInfo> roadSpeedInfoList = null;
        protected PercentRoadBuilder roadBuilder_Good;
        protected PercentRoadBuilder roadBuilder_Bad;

        protected static readonly object lockObj = new object();
        protected LteDriveSpeedWithKpiAnaBase()
            : base(MainModel.GetInstance())
        {
            init();
        }

        public struct KpiTypeString
        {
            public static string Good { get; set; } = "指标好";
            public static string Bad { get; set; } = "指标差";
        }

        protected void init()
        {
            this.IncludeEvent = false;
            Columns = new List<string>();
            Columns.Add("lte_SINR");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_APP_type");
            Columns.Add("lte_APP_Speed_Mb");
            Columns.Add("lte_APP_ThroughputDL_Mb");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_APP_type");
            Columns.Add("lte_fdd_APP_Speed_Mb");
            Columns.Add("lte_fdd_APP_ThroughputDL_Mb");

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);

            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
        }

        public override string Name
        {
            get { return "指标与车速分析_LTE"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22097, this.Name);
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            roadSpeedInfoList = new List<DriveSpeedKpiInfo>();
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                setRoadCond();
                return true;
            }
            RoadKpiSetDlg dlg = new RoadKpiSetDlg(roadSetCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                roadSetCondition = dlg.GetCondition();
                setRoadCond();
                return true;
            }
            return false;
        }

        public void setRoadCond()
        {
            PercentRoadCondition roadCond = new PercentRoadCondition(roadSetCondition.CommonTpPercentGate / 100, OnOneRoadComplete);
            roadCond.IsCheckDuration = roadSetCondition.CheckMinDuration;
            roadCond.MinDuration = roadSetCondition.MinDuration;
            roadCond.IsCheckMinLength = roadSetCondition.CheckMinDistance;
            roadCond.MinLength = roadSetCondition.MinCoverRoadDistance;
            roadCond.IsCheckDistanceGap = true;
            roadCond.MaxDistanceGap = roadSetCondition.Max2TPDistance;

            this.roadBuilder_Good = new PercentRoadBuilder(roadCond);
            this.roadBuilder_Bad = new PercentRoadBuilder(roadCond);
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint tp in fileDataManager.TestPoints)
                    {
                        if (!isValidTestPoint(tp))
                        {
                            this.roadBuilder_Good.StopRoading(KpiTypeString.Good);
                            this.roadBuilder_Bad.StopRoading(KpiTypeString.Bad);
                        }
                        else
                        {
                            float? sinr = getSinr(tp);
                            float? rsrp = getRsrp(tp);
                            double? speed = getDownLoadSpeed(tp);

                            roadBuilder_Good.AddPoint(tp, roadSetCondition.IsGoodKpiTestPoint(sinr, rsrp, speed), KpiTypeString.Good);
                            roadBuilder_Bad.AddPoint(tp, roadSetCondition.IsBadKpiTestPoint(sinr, rsrp, speed), KpiTypeString.Bad);
                        }
                    }
                    this.roadBuilder_Good.StopRoading(KpiTypeString.Good);
                    this.roadBuilder_Bad.StopRoading(KpiTypeString.Bad);
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }
        protected void OnOneRoadComplete(object sender, PercentRoadItem roadItem)
        {
            if (roadItem.TestPoints.Count == 0 || !roadSetCondition.MatchMinWeakCoverDistance(roadItem.Length)
                || !roadSetCondition.MatchMinWeakCoverDuration(roadItem.Duration)
                || roadSetCondition.CommonTpPercentGate > (roadItem.ValidPercent * 100))
            {
                return;
            }
            saveKpiInfo(roadItem);
        }

        protected void saveKpiInfo(PercentRoadItem roadItem)
        {
            DriveSpeedKpiInfo info = new DriveSpeedKpiInfo();
            foreach (TestPoint testPoint in roadItem.TestPoints)
            {
                float? sinr = getSinr(testPoint);
                float? rsrp = getRsrp(testPoint);
                double? downloadSpeed = getDownLoadSpeed(testPoint);
                info.Add(sinr, rsrp, downloadSpeed, testPoint);
            }

            double driveSpeed = Math.Round(3.6 * roadItem.Length / roadItem.Duration, 2);// m/s换算成km/h
            info.DriveSpeed = driveSpeed;
            info.DriveSpeedType = roadSetCondition.GetDriveSpeedType(driveSpeed);
            info.KpiType = roadItem.NetType;
            info.RoadLength = Math.Round(roadItem.Length, 2);
            info.Duration = Math.Round(roadItem.Duration, 2);
            info.CommonTpPercent = Math.Round(roadItem.ValidPercent * 100, 2);
            info.SN = roadSpeedInfoList.Count + 1;

            roadSpeedInfoList.Add(info);
        }
        protected override void fireShowForm()
        {
            if (roadSpeedInfoList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            LteDriveSpeedWithKpiListForm frm = MainModel.CreateResultForm(typeof(LteDriveSpeedWithKpiListForm)) as LteDriveSpeedWithKpiListForm;
            frm.FillData(roadSpeedInfoList);
            frm.Visible = true;
            frm.BringToFront();
            roadSpeedInfoList = null;
        }



        protected float? getSinr(TestPoint testPoint)
        {
            if (testPoint is LTEFddTestPoint)
            {
                return (float?)testPoint["lte_fdd_SINR"];
            }
            return (float?)testPoint["lte_SINR"];
        }

        protected float? getRsrp(TestPoint testPoint)
        {
            if (testPoint is LTEFddTestPoint)
            {
                return (float?)testPoint["lte_fdd_RSRP"];
            }
            return (float?)testPoint["lte_RSRP"];
        }
        protected double? getDownLoadSpeed(TestPoint testPoint)
        {
            if (testPoint is LTEFddTestPoint)
            {
                short? type = (short?)testPoint["lte_fdd_APP_type"];
                if (type == 2 || type == 6 || type == 12 || type == 25 || type == 26 || type == 32 || type == 33)
                {
                    return (double?)testPoint["lte_fdd_APP_Speed_Mb"];
                }
                return (double?)testPoint["lte_fdd_APP_ThroughputDL_Mb"];
            }
            else
            {
                short? type = (short?)testPoint["lte_APP_type"];
                if (type == 2 || type == 6 || type == 12 || type == 25 || type == 26 || type == 32 || type == 33)
                {
                    return (double?)testPoint["lte_APP_Speed_Mb"];
                }
                return (double?)testPoint["lte_APP_ThroughputDL_Mb"];
            }
        }
    }
}
