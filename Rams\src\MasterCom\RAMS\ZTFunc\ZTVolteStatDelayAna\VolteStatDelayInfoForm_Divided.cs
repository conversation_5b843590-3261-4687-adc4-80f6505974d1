﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class VolteStatDelayInfoForm_Divided : MinCloseForm
    {
        public VolteStatDelayInfoForm_Divided()
        {
            InitializeComponent();

            this.init();

            ToolStripMenuItem itemExportToExcel = new ToolStripMenuItem("导出为Excel");
            itemExportToExcel.Click += itemExportToExcel_Click;
            ContextMenuStrip contextMenu = new ContextMenuStrip();
            contextMenu.Items.Add(itemExportToExcel);
            this.treeListViewResult.ContextMenuStrip = contextMenu;
        }

        void itemExportToExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.treeListViewResult);
        }
        private void init()
        {
            this.col_sn.AspectGetter = delegate (object row)
            {
                if (row is ResultRow)
                {
                    return (row as ResultRow).sn.ToString();
                }
                return "-";
            };

            this.col_beginTime.AspectGetter = delegate (object row)
            {
                if (row is ResultRow)
                {
                    return (row as ResultRow).time_invite.ToString("yyyy-MM-dd HH:mm:ss.fff");
                }
                return "-";
            };

            this.col_endTime.AspectGetter = delegate (object row)
            {
                if (row is ResultRow)
                {
                    return (row as ResultRow).time_180.ToString("yyyy-MM-dd HH:mm:ss.fff");
                }
                return "-";
            };

            this.col_totalTime.AspectGetter = delegate (object row)
            {
                if (row is ResultRow)
                {
                    double re = ((row as ResultRow).time_180 - ((row as ResultRow).time_invite)).TotalSeconds;
                    return Math.Round(re, 3).ToString();
                }
                return "-";
            };

            this.col_invite_100.AspectGetter = delegate (object row)
              {
                  if (row is ResultRow)
                  {
                      double re = ((row as ResultRow).time_100 - (row as ResultRow).time_invite).TotalSeconds;
                      return Math.Round(re, 3).ToString();
                  }
                  return "-";
              };

            this.col_100_183.AspectGetter = delegate (object row)
            {
                if (row is ResultRow)
                {
                    double re = ((row as ResultRow).time_183 - (row as ResultRow).time_100).TotalSeconds;
                    return Math.Round(re, 3).ToString();
                }
                return "-";
            };

            this.col_183_prack.AspectGetter = delegate (object row)
            {
                if (row is ResultRow)
                {
                    double re = ((row as ResultRow).time_prack - ((row as ResultRow).time_183)).TotalSeconds;
                    return Math.Round(re, 3).ToString();
                }
                return "-";
            };

            this.col_prack_200.AspectGetter = delegate (object row)
            {
                if (row is ResultRow)
                {
                    double re = ((row as ResultRow).time_prack200 - ((row as ResultRow).time_prack)).TotalSeconds;
                    return Math.Round(re, 3).ToString();
                }
                return "-";
            };

            this.col_200_update.AspectGetter = delegate (object row)
            {
                if (row is ResultRow)
                {
                    double re = ((row as ResultRow).time_update - ((row as ResultRow).time_prack200)).TotalSeconds;
                    return Math.Round(re, 3).ToString();
                }
                return "-";
            };

            this.col_update_200.AspectGetter = delegate (object row)
            {
                if (row is ResultRow)
                {
                    double re = ((row as ResultRow).time_update200 - ((row as ResultRow).time_update)).TotalSeconds;
                    return Math.Round(re, 3).ToString();
                }
                return "-";
            };

            this.col_200_180.AspectGetter = delegate (object row)
            {
                if (row is ResultRow)
                {
                    double re = ((row as ResultRow).time_180 - ((row as ResultRow).time_update200)).TotalSeconds;
                    return Math.Round(re, 3).ToString();
                }
                return "-";
            };

            this.col_fileName.AspectGetter = delegate (object row)
            {
                if (row is ResultRow)
                {
                    return (row as ResultRow).fileName;
                }
                return "-";
            };
        }

        public void FillData(List<ResultRow> listData)
        {
            for (int i = 0; i < listData.Count; i++)
            {
                ResultRow row = listData[i];
                row.sn = i + 1;
            }
            this.treeListViewResult.SetObjects(listData);
            this.treeListViewResult.Refresh();
        }
    }

    public class ResultRow
    {
        public int sn{ get; set; }
        //单位：秒
        public DateTime time_invite{ get; set; }
        public DateTime time_183{ get; set; }
        public DateTime time_prack{ get; set; }
        public DateTime time_prack200{ get; set; }
        public DateTime time_update{ get; set; }
        public DateTime time_update200{ get; set; }
        public DateTime time_180{ get; set; }
        public DateTime time_100{ get; set; }

        public string fileName{ get; set; }
    }
}
