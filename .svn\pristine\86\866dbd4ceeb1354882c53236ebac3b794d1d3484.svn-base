﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class DIYQueryLteTddWirelessPlanning : DiyQueryDataBase
    {
        public string TableName { get; set; } = "";
        public List<LteTddWirelessPlanningDBInfo> LteTddWirelessPlanningDBInfoList { get; private set; }

        public DIYQueryLteTddWirelessPlanning()
            : base()
        { }

        public override string Name { get { return "查询NR无线规划"; } }

        protected override string getSqlTextString()
        {
            //string name = $"{TableName}_" + DateTime.Now.ToString("yyyyMMdd");
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat(@"SELECT [基站名称],[小区名称],[带宽],[业务IP],[管理IP]
,[RsPower],[PA],[PB] FROM {0} where [基站名称]='{1}' order by 小区名称", TableName, btsName);

            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[8];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void initData()
        {
            LteTddWirelessPlanningDBInfoList = new List<LteTddWirelessPlanningDBInfo>();
        }

        protected override void dealReceiveData(Package package)
        {
            LteTddWirelessPlanningDBInfo info = new LteTddWirelessPlanningDBInfo();
            info.FillData(package);
            LteTddWirelessPlanningDBInfoList.Add(info);
        }
    }

    public class DIYQueryLteTddWirelessPlanningTable : DiyQueryDataBase
    {
        public string TableName { get; private set; } = "";

        public DIYQueryLteTddWirelessPlanningTable()
            : base()
        { }

        public override string Name { get { return "查询最新LteTdd无线规划表名"; } }

        protected override string getSqlTextString()
        {
            string sql = @"select top 1 name from sysobjects where type = 'U' and name like '%tb_xinjiang_LteTddWirelessPlanning%' order by name desc";
            return sql.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[1];
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void dealReceiveData(Package package)
        {
            TableName = package.Content.GetParamString();
        }
    }

    public class LteTddWirelessPlanningDBInfo
    {
        public string BtsName { get; set; }
        public string CellName { get; set; }
        public string Bandwidth { get; set; }
        public string ServiceIP { get; set; }
        public string ManageIP { get; set; }
        public string RsPower { get; set; }
        public string PA { get; set; }
        public string PB { get; set; }

        public void FillData(Package package)
        {
            BtsName = package.Content.GetParamString();
            CellName = package.Content.GetParamString();
            Bandwidth = package.Content.GetParamString();
            ServiceIP = package.Content.GetParamString();
            ManageIP = package.Content.GetParamString();
            RsPower = package.Content.GetParamString();
            PA = package.Content.GetParamString();
            PB = package.Content.GetParamString();
        }
    }
}
