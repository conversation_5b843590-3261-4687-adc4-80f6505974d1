﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellOmitAnaByFile : DIYReplayFileQuery
    {
        public Dictionary<int, List<DTData>> dtDataMap { get; set; } = new Dictionary<int, List<DTData>>();//主键是fileid,值是采样点和层三列表

        public List<ZTLteNBCellOmitFileItem> nbCellOmitFileList { get; set; } = new List<ZTLteNBCellOmitFileItem>();   //计算结果
        public List<ZTLteNBCellOmitStatItem> nbCellOmitStatList { get; set; } = new List<ZTLteNBCellOmitStatItem>();   //统计结果

        public ZTLteNBCellOmitCondition nbCellOmitCondition { get; set; } = new ZTLteNBCellOmitCondition();   //查询条件

        public ZTLteNBCellOmitAnaByFile(MainModel mainModel)
            : base(mainModel)
        {

        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22022, this.Name);
        }

        ZTLteNBCellOmitAnaSetForm setForm = null;
        private bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTLteNBCellOmitAnaSetForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                nbCellOmitCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }


        protected override void query()
        {
            dtDataMap.Clear();
            nbCellOmitFileList = new List<ZTLteNBCellOmitFileItem>();
            nbCellOmitStatList = new List<ZTLteNBCellOmitStatItem>();
            IsAddMessageToDTDataManager = false;
            IsAddSampleToDTDataManager = false;

            if (!getCondition())
            {
                return;
            }

            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = "正在查询...";
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);

            }
            finally
            {
                clientProxy.Close();
            }

            anaSamplesAndMessages();
            getStatResult();
            fireShowForm();
        }

        private void fireShowForm()
        {
            if (nbCellOmitFileList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTLteNBCellOmitAnaListForm).FullName);
            ZTLteNBCellOmitAnaListForm lteHOOmitListForm = obj == null ? null : obj as ZTLteNBCellOmitAnaListForm;
            if (lteHOOmitListForm == null || lteHOOmitListForm.IsDisposed)
            {
                lteHOOmitListForm = new ZTLteNBCellOmitAnaListForm(MainModel);
            }

            lteHOOmitListForm.FillData(nbCellOmitFileList, nbCellOmitStatList);
            if (!lteHOOmitListForm.Visible)
            {
                lteHOOmitListForm.Show(MainModel.MainForm);
            }
        }

        private void getStatResult()
        {
            if (nbCellOmitFileList.Count == 0)
            {
                return;
            }

            Dictionary<string, ZTLteNBCellOmitStatItem> statDic = new Dictionary<string, ZTLteNBCellOmitStatItem>();

            foreach (ZTLteNBCellOmitFileItem fileItem in nbCellOmitFileList)
            {
                foreach (ZTLteNBCellOmitCellItem cellItem in fileItem.CellList)
                {
                    if (cellItem.CurCell == null)    //如果主服未能匹配到工参，则无法计算距离，需要剔除
                    {
                        continue;
                    }
                    ZTLteNBCellOmitStatItem statItem;
                    if (statDic.ContainsKey(cellItem.CellName))
                    {
                        statItem = statDic[cellItem.CellName];
                    }
                    else
                    {
                        statItem = new ZTLteNBCellOmitStatItem(cellItem);
                        statDic.Add(cellItem.CellName, statItem);
                    }

                    statItem.MergeData(cellItem);
                }
            }

            addNbCellOmitStatList(statDic);
        }

        private void addNbCellOmitStatList(Dictionary<string, ZTLteNBCellOmitStatItem> statDic)
        {
            foreach (string cellName in statDic.Keys)
            {
                foreach (string nbCellName in statDic[cellName].NBCellDic.Keys)
                {
                    if (statDic[cellName].NBCellDic[nbCellName].MRCount >= nbCellOmitCondition.MRCount      //数量要高于门限
                        && statDic[cellName].NBCellDic[nbCellName].Distance >= 0                            //剔除邻区不匹配的情况，当未匹配工参时，无法计算出距离    
                        && statDic[cellName].NBCellDic[nbCellName].Distance <= nbCellOmitCondition.Distance) //距离低于门限
                    {
                        statDic[cellName].NBList.Add(statDic[cellName].NBCellDic[nbCellName]);
                    }
                }

                if (statDic[cellName].NBList.Count > 0)      //当前小区存在邻区则添加记录，否则不添加
                {
                    statDic[cellName].SN = nbCellOmitStatList.Count + 1;
                    statDic[cellName].NBList.Sort(ZTLteNBCellOmitNBItem.GetCompareByScore());

                    nbCellOmitStatList.Add(statDic[cellName]);
                }
            }
        }

        /// <summary>
        /// 对每个文件进行分析，其信令特征如下：
        /// RRC Connection Reconfiguration          发切换指令，其中包含目的小区频点和PCI(获取EARFCN,PCI)
        /// RRC Connection Reconfiguration Complete UE反馈切换结束
        /// RRC Connection Reconfiguration          下发邻区测量要求(获取测量邻区列表，邻区的信息有EARFCN和PCI)
        /// RRC Connection Reconfiguration Complete UE反馈收到
        /// System Information Block Type 1         发系统消息，其中包含当前小区的TAC 和 ECI
        /// Measurement Report                      上报测量报告（关注A3事件——邻区信号与主服的差异超过测量要求中的阈值）
        /// 

        /// 算法如下：
        /// 根据切换指令中的目标小区，获取到切换后的小区的频点和PCI
        /// 根据邻区测量要求，获取到切换后小区的邻区频点和PCI列表
        /// 根据系统消息1，获取切换后小区的TAC ECI
        /// 根据测量报告，获取测量报告中上报的PCI与邻区列表进行比较，如果不一致则认为是漏配
        /// 原则上UE只需要上报1次MR，如果出现多次，也可能是异常
        /// </summary>
        private void anaSamplesAndMessages()
        {
            try
            {
                foreach (int fileID in dtDataMap.Keys)
                {
                    dtDataMap[fileID].Sort(comparer);   //将采样点和层三信令按照sampleid排序
                    List<DTData> dtDataList = dtDataMap[fileID];

                    string fileName = "";
                    if (dtDataList.Count > 0)
                    {
                        fileName = dtDataList[0].FileName;
                    }
                    else
                    {
                        continue;
                    }

                    addNbCellOmitFileList(dtDataList, fileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void addNbCellOmitFileList(List<DTData> dtDataList, string fileName)
        {
            int sn = nbCellOmitFileList.Count + 1;
            ZTLteNBCellOmitFileItem fileItem = new ZTLteNBCellOmitFileItem(sn, fileName);
            ZTLteNBCellOmitCellItem hoItem = new ZTLteNBCellOmitCellItem();

            for (int i = 0; i < dtDataList.Count; i++)
            {
                if (dtDataList[i] is TestPoint)
                {
                    if (hoItem.IsGotHOMsg)
                    {
                        setTestPoint(ref hoItem, dtDataList[i]);  //用最近的采样点经纬度来填充
                    }
                }
                else if (dtDataList[i] is MasterCom.RAMS.Model.Message)
                {
                    Model.Message msg = dtDataList[i] as Model.Message;
                    if (msg.ID == (int)EnumLteNBCheckMsg.RRCConnectionReconfiguration)
                    {
                        processRRCConnReconfigMsg(ref hoItem, dtDataList[i], ref fileItem);
                    }
                    else if (msg.ID == (int)EnumLteNBCheckMsg.SystemInformationBlockType1)
                    {
                        processSIB1Msg(ref hoItem, dtDataList[i]);
                    }
                    else if (msg.ID == (int)EnumLteNBCheckMsg.MeasurementReport)
                    {
                        processMRMsg(ref hoItem, dtDataList[i]);
                    }
                }
            }

            nbCellOmitFileList.Add(fileItem);
        }

        /// <summary>
        /// 匹配主服和邻区的小区信息
        /// </summary>
        /// <param name="hoItem"></param>
        private void processCellItem(ref ZTLteNBCellOmitCellItem cellItem)
        {
            LTECell curCell = null;
            if (cellItem.Tp != null)
            {
                curCell = CellManager.GetInstance().GetNearestLTECell(cellItem.Tp.DateTime, cellItem.TAC, cellItem.ECI, cellItem.EARFCN, cellItem.PCI,
                                                                                cellItem.Tp.Longitude, cellItem.Tp.Latitude);
                if (curCell != null)
                {
                    cellItem.CurCell = curCell;
                }
            }

            foreach (int earfcn in cellItem.NBRptDic.Keys)
            {
                if (cellItem.NBConfigDic.ContainsKey(earfcn))    //下发的测量列表中，存在同样频点的PCI列表
                {
                    Dictionary<int, ZTLteNBCellOmitNBItem> rptPCIDic = cellItem.NBRptDic[earfcn];

                    Dictionary<int, int> configPCIDic = cellItem.NBConfigDic[earfcn];

                    foreach (int pci in rptPCIDic.Keys)
                    {
                        setRptPCIDic(cellItem, curCell, earfcn, rptPCIDic, configPCIDic, pci);
                    }
                }
            }
        }

        private static void setRptPCIDic(ZTLteNBCellOmitCellItem cellItem, LTECell curCell, int earfcn, Dictionary<int, ZTLteNBCellOmitNBItem> rptPCIDic, Dictionary<int, int> configPCIDic, int pci)
        {
            if (cellItem.Tp != null)
            {
                LTECell nbCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(cellItem.Tp.DateTime, earfcn, pci, cellItem.Tp.Longitude, cellItem.Tp.Latitude);
                if (nbCell != null)
                {
                    rptPCIDic[pci].NBCell = nbCell;
                    if (curCell != null)
                    {
                        rptPCIDic[pci].Distance = nbCell.GetDistance(curCell.Longitude, curCell.Latitude);
                    }
                }
            }

            if (configPCIDic.ContainsKey(pci)) //如果在下发的配置中存在该PCI
            {
                rptPCIDic[pci].Status = "在配置中";
            }
            else
            {
                rptPCIDic[pci].Status = "漏配";
            }
        }

        /// <summary>
        /// 分析 RRCConnectionReconfig层三信令，如果包含目标EARFCN和PCI，就认为是切换信令
        /// 如果不是，则需进一步判断是否是下发邻区的信令
        /// </summary>
        /// <param name="cellItem"></param>
        /// <param name="dtData"></param>
        /// <param name="fileItem"></param>
        private void processRRCConnReconfigMsg(ref ZTLteNBCellOmitCellItem cellItem, DTData dtData, ref ZTLteNBCellOmitFileItem fileItem)
        {
            MessageWithSource msg = ((MessageWithSource)dtData);
            MessageDecodeHelper.StartDissect(msg.Direction,msg.Source, msg.Source.Length, msg.ID);

            // 如果RRC Connection Reconfiguration中包含mobilityControlInfo，那主要作用就是eNodeB发切换命令给UE执行切换
            // 如果RRC Connection Reconfiguration中包含measConfig，那其主要作用就是进行测量配置

            int earfcn = 0;
            int pci = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.targetPhysCellId", ref pci))  
            {
                if (!MessageDecodeHelper.GetSingleSInt("lte-rrc.dl_CarrierFreq", ref earfcn))   //如果获取不到earfcn，沿用之前的
                {
                    earfcn = cellItem.EARFCN;
                }

                if (cellItem.IsGotHOMsg && cellItem.IsGotNBConfigMsg    //如果一次信息已经收集完整，则添加到结果列表中，否则丢弃
                        && cellItem.IsGotSIB1Msg && cellItem.IsGotMRMsg)
                {
                    processCellItem(ref cellItem);
                    cellItem.SN = fileItem.CellList.Count + 1;
                    fileItem.CellList.Add(cellItem);
                    cellItem = new ZTLteNBCellOmitCellItem();
                }
                else
                {
                    cellItem = new ZTLteNBCellOmitCellItem();     //原有记录丢弃，重新reset
                }

                cellItem.EARFCN = earfcn;
                cellItem.PCI = pci;
                cellItem.IsGotHOMsg = true;
                cellItem.DateTimeHoMsg = dtData.DateTimeStringWithMillisecond;
            }
            else
            {
                processRRCConnReconfigMsg_CfgNBList(ref cellItem, dtData);
            }
        }

        /// <summary>
        /// 找出含有A3事件的RptObjectID
        /// </summary>
        /// <param name="rptObjectID_A3"></param>
        private void setRptObjectID_A3Event(ref int rptObjectID_A3)
        {
            int rptObjectCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.reportConfigToAddModList", ref rptObjectCount))  //包含报告对象
            {
                int[] arrEventIDs = new int[rptObjectCount];
                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.eventId", ref arrEventIDs, rptObjectCount))   //是否有事件ID，如果没有，肯定没有A3事件，则终止
                {
                    int[] arrRptObjectIDs = new int[rptObjectCount];
                    int[] arrTrigTypes = new int[rptObjectCount];
                    int periodCount = 0;                        //记录周期性上报rpt次数，

                    if (MessageDecodeHelper.GetMultiSInt("lte-rrc.reportConfigId", ref arrRptObjectIDs, rptObjectCount)
                        && (MessageDecodeHelper.GetMultiSInt("lte-rrc.triggerType", ref arrTrigTypes, rptObjectCount)))
                    {
                        setRptObjectID_A3(ref rptObjectID_A3, rptObjectCount, arrEventIDs, arrRptObjectIDs, arrTrigTypes, ref periodCount);
                    }
                }
            }
        }

        private void setRptObjectID_A3(ref int rptObjectID_A3, int rptObjectCount, int[] arrEventIDs, 
            int[] arrRptObjectIDs, int[] arrTrigTypes, ref int periodCount)
        {
            for (int i = 0; i < rptObjectCount; i++)
            {
                if (arrTrigTypes[i] == 0)   //event
                {
                    if (arrEventIDs[i - periodCount] == 2)//a3事件
                    {
                        rptObjectID_A3 = arrRptObjectIDs[i];    //记录A3事件的rpt的ID号
                        return; 
                    }
                }
                else
                {
                    periodCount++;
                }
            }
        }

        /// <summary>
        /// 通过RptObjectID来反查measID和measObjectID
        /// </summary>
        /// <param name="rptObjectID_A3"></param>
        /// <param name="measID"></param>
        /// <param name="measObjectID"></param>
        private void setMeasIDAndMeasObjectIDByRptObjectID(int rptObjectID_A3, ref int measID, ref int measObjectID)
        {
            int measIDCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measIdToAddModList", ref measIDCount))
            {
                int[] arrMeasObjectIDs = new int[measIDCount];
                int[] arrMeasIDs = new int[measIDCount];
                int[] arrRptObjectIDs = new int[measIDCount];

                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.measId", ref arrMeasObjectIDs, measIDCount)
                    && MessageDecodeHelper.GetMultiSInt("lte-rrc.measObjectId", ref arrMeasIDs, measIDCount)
                    && MessageDecodeHelper.GetMultiSInt("lte-rrc.reportConfigId", ref arrRptObjectIDs, measIDCount))
                {
                    for (int i = 0; i < measIDCount; i++)
                    {
                        if (arrRptObjectIDs[i] == rptObjectID_A3)
                        {
                            measObjectID = arrMeasObjectIDs[i];
                            measID = arrMeasIDs[i];
                            return;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 通过MeasObjectID来找到需要测量的邻区列表，同时建立MeasID和频点的对应关系（由于MR报告中没有频点，需要用此关系找出频点）
        /// </summary>
        /// <param name="cellItem"></param>
        /// <param name="measID"></param>
        /// <param name="measObjectID"></param>
        private void setNBListCfgByMeasObjectID(ref ZTLteNBCellOmitCellItem cellItem, int measID, int measObjectID)
        {
            int measObjectCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measObjectToAddModList", ref measObjectCount))  //包含测量对象
            {
                int[] arrMeasObjectIDs = new int[measObjectCount];
                int[] arrCarrierFreqs = new int[measObjectCount];
                int[] arrPCICounts = new int[measObjectCount];
                int[] arrPCIHeadIndexs = new int[measObjectCount];

                int index = 0;  //需要获取的测量数据在数组中的位置
                int accuPCIIndex = 0;       //PCI累积的数量,用于确定数组大小，取回所有的PCI

                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.measObjectId", ref arrMeasObjectIDs, measObjectCount)
                    && MessageDecodeHelper.GetMultiSInt("lte-rrc.carrierFreq", ref arrCarrierFreqs, measObjectCount)
                    && MessageDecodeHelper.GetMultiSInt("lte-rrc.cellsToAddModList", ref arrPCICounts, measObjectCount))
                {
                    if (arrPCICounts.Length == 0)   //都没有下发PCI，常见于异频的情况，只下发测量的mbw，不下发具体的PCI
                    {
                        return;
                    }

                    setIndex(measObjectID, measObjectCount, arrMeasObjectIDs, arrPCICounts, arrPCIHeadIndexs, ref index, ref accuPCIIndex);
                }

                int[] arrPCIs = new int[accuPCIIndex];
                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.physCellId", ref arrPCIs, accuPCIIndex))
                {
                    int mrEarfcn = arrCarrierFreqs[index];
                    addPci(cellItem, arrPCICounts, arrPCIHeadIndexs, index, arrPCIs, mrEarfcn);

                    if (!cellItem.NBMeasEarfcnDic.ContainsKey(measID))
                    {
                        cellItem.NBMeasEarfcnDic.Add(measID, mrEarfcn);
                    }
                }
            }
        }

        private static void setIndex(int measObjectID, int measObjectCount, int[] arrMeasObjectIDs, int[] arrPCICounts, int[] arrPCIHeadIndexs, ref int index, ref int accuPCIIndex)
        {
            for (int i = 0; i < measObjectCount; i++)
            {
                arrPCIHeadIndexs[i] = accuPCIIndex;
                accuPCIIndex += arrPCICounts[i];

                if (arrMeasObjectIDs[i] == measObjectID)
                {
                    index = i;
                }
            }
        }

        private void addPci(ZTLteNBCellOmitCellItem cellItem, int[] arrPCICounts, int[] arrPCIHeadIndexs, int index, int[] arrPCIs, int mrEarfcn)
        {
            Dictionary<int, int> pciDic = new Dictionary<int, int>();

            if (cellItem.NBConfigDic.ContainsKey(mrEarfcn))
            {
                pciDic = cellItem.NBConfigDic[mrEarfcn];
            }
            else
            {
                cellItem.NBConfigDic.Add(mrEarfcn, pciDic);
            }

            int startIndex = arrPCIHeadIndexs[index];
            for (int j = 0; j < arrPCICounts[index]; j++)
            {
                if ((mrEarfcn == cellItem.EARFCN) && (arrPCIs[startIndex + j] == cellItem.PCI)) //列表中可能出现主服的EARFCN和PCI，剔除
                {
                    continue;
                }

                if (pciDic.ContainsKey(arrPCIs[startIndex + j]))
                {
                    pciDic[arrPCIs[startIndex + j]]++;
                }
                else
                {
                    pciDic.Add(arrPCIs[startIndex + j], 1);
                }
            }
        }

        /// <summary>
        /// 解析出需要测量的邻区列表
        /// </summary>
        /// <param name="cellItem"></param>
        private void processRRCConnReconfigMsg_CfgNBList(ref ZTLteNBCellOmitCellItem cellItem, DTData dtData)
        {
            if (!cellItem.IsGotHOMsg)  //对于其它信令，如果还没有头信令，不处理
            {
                return;
            }

            //找出A3事件所在的RptObjectID
            int rptObjectID_A3 = 0;
            setRptObjectID_A3Event(ref rptObjectID_A3); 
            if (rptObjectID_A3 == 0)  //没有A3事件
            {
                return;
            }

            //根据A3事件的RptObjectID，找出对应的measObjectID和measID
            int measID = 0;
            int measObjectID = 0;
            setMeasIDAndMeasObjectIDByRptObjectID(rptObjectID_A3, ref measID, ref measObjectID);
            if (measID == 0 || measObjectID == 0)   //没有找到或出错
            {
                return;
            }

            setNBListCfgByMeasObjectID(ref cellItem, measID, measObjectID);
            cellItem.IsGotNBConfigMsg = true;
            cellItem.DateTimeNBConfigMsg = dtData.DateTimeStringWithMillisecond;
        }

        /// <summary>
        /// 解析SIB1,用户获取当前小区的TAC和ECI
        /// </summary>
        /// <param name="cellItem"></param>
        /// <param name="dtData"></param>
        private void processSIB1Msg(ref ZTLteNBCellOmitCellItem cellItem, DTData dtData)
        {
            if (!cellItem.IsGotHOMsg)  //对于其它信令，如果还没有头信令，不处理
            {
                return;
            }

            MessageWithSource msg = ((MessageWithSource)dtData);
            MessageDecodeHelper.StartDissect(msg.Direction,msg.Source, msg.Source.Length, msg.ID);

            uint uTac = 0;
            uint uEci = 0;
            if (MessageDecodeHelper.GetSingleUInt("lte-rrc.trackingAreaCode_TAC", ref uTac)
                && MessageDecodeHelper.GetSingleUInt("lte-rrc.cellIdentity_ECI", ref uEci))
            {
                cellItem.TAC = (int)uTac;
                cellItem.ECI = (int)uEci;
                cellItem.IsGotSIB1Msg = true;
            }
            cellItem.IsGotSIB1Msg = true;
            cellItem.DateTimeSIB1Msg = dtData.DateTimeStringWithMillisecond;
        }

        /// <summary>
        /// 解析MR测量报告，解析时需要判断MeasID是否是之前A3事件设置的ID
        /// </summary>
        /// <param name="cellItem"></param>
        /// <param name="dtData"></param>
        private void processMRMsg(ref ZTLteNBCellOmitCellItem cellItem, DTData dtData)
        {
            if (!cellItem.IsGotHOMsg)  //对于其它信令，如果还没有头信令，不处理
            {
                return;
            }

            MessageWithSource msg = ((MessageWithSource)dtData);
            MessageDecodeHelper.StartDissect(msg.Direction,msg.Source, msg.Source.Length, msg.ID);

            int measID = 0;
            bool isFound = false;
            int earfcn = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measId", ref measID))
            {
                foreach (int nbMeasID in cellItem.NBMeasEarfcnDic.Keys)   
                {
                    if (measID == nbMeasID)
                    {
                        isFound = true;
                        earfcn = cellItem.NBMeasEarfcnDic[nbMeasID];
                        break;
                    }
                }
                if (!isFound)  //不是需要的报告
                {
                    return; 
                }
            }

            int nbCellsCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measResultListEUTRA", ref nbCellsCount))
            {
                int[] arrRSRPs = new int[nbCellsCount + 1]; //包括主服的，所以+1
                int[] arrPCIs = new int[nbCellsCount];
                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.rsrpResult", ref arrRSRPs, nbCellsCount + 1) && MessageDecodeHelper.GetMultiSInt("lte-rrc.physCellId", ref arrPCIs, nbCellsCount))
                {
                    cellItem.MRCount++;
                    cellItem.RSRPTotal += arrRSRPs[0] - 141;

                    setPciDic(cellItem, earfcn, nbCellsCount, arrRSRPs, arrPCIs);

                    cellItem.IsGotMRMsg = true; //信息收集完毕
                    cellItem.DateTimeMRMsg = dtData.DateTimeStringWithMillisecond;
                }
            }
        }

        private void setPciDic(ZTLteNBCellOmitCellItem cellItem, int earfcn, int nbCellsCount, int[] arrRSRPs, int[] arrPCIs)
        {
            Dictionary<int, ZTLteNBCellOmitNBItem> pciDic;
            if (cellItem.NBRptDic.ContainsKey(earfcn))
            {
                pciDic = cellItem.NBRptDic[earfcn];
            }
            else
            {
                pciDic = new Dictionary<int, ZTLteNBCellOmitNBItem>();
                cellItem.NBRptDic.Add(earfcn, pciDic);
            }

            for (int i = 0; i < nbCellsCount; i++)
            {
                if (((float)arrRSRPs[i + 1] - 141) < nbCellOmitCondition.RSRP)  //如果邻区的RSRP低于门限，则跳过
                {
                    continue;
                }

                if (pciDic.ContainsKey(arrPCIs[i]))
                {
                    pciDic[arrPCIs[i]].MRCount++;
                    pciDic[arrPCIs[i]].AccuScore(i);
                    pciDic[arrPCIs[i]].RSRPTotal += ((float)arrRSRPs[i + 1] - 141);  //RSRPResult列表中主服在最前面，因此需要跳过主服
                }
                else
                {
                    ZTLteNBCellOmitNBItem nbItem = new ZTLteNBCellOmitNBItem(earfcn, arrPCIs[i], ((float)arrRSRPs[i + 1] - 141), i);
                    pciDic.Add(arrPCIs[i], nbItem);
                }
            }
        }

        private void setTestPoint(ref ZTLteNBCellOmitCellItem cellItem, DTData dtData)
        {
            cellItem.Tp = (TestPoint)dtData;
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            //sample                         
            prepareStatPackage_Sample_FileFilter(package, fileInfo);
            prepareStatPackage_Sample_SampleFilter(package);
            fillContentNeeded_Sample(package);
            clientProxy.Send();
            recieveInfo_Sample(clientProxy);

            //message
            prepareStatPackage_Message_FileFilter(package, fileInfo);
            prepareStatPackage_Message_MessageFilter(package);
            fillContentNeeded_Message(package, true);
            clientProxy.Send();
            recieveInfo_Message(clientProxy, true);
        }

        protected override void fillContentNeeded_Sample(Package package)
        {
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,13,23,0,11,23,0,1,23,0,3,23");//lac,ci
            sbuilder.Append(",0,6,53,0,7,53");
            package.Content.AddParam(sbuilder.ToString());
        }

        protected override void doWithDTData(MasterCom.RAMS.Model.Message msg)
        {
            if (dtDataMap.ContainsKey(msg.FileID))
            {
                dtDataMap[msg.FileID].Add(msg);
            }
            else
            {
                List<DTData> dtDatas = new List<DTData>();
                dtDatas.Add(msg);
                dtDataMap.Add(msg.FileID, dtDatas);
            }
        }

        protected override void doWithDTData(TestPoint tp)
        {
            if (dtDataMap.ContainsKey(tp.FileID))
            {
                dtDataMap[tp.FileID].Add(tp);
            }
            else
            {
                List<DTData> dtDatas = new List<DTData>();
                dtDatas.Add(tp);
                dtDataMap.Add(tp.FileID, dtDatas);
            }
         }

        protected override void prepareStatPackage_Message_MessageFilter(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,6,45");//msgid
            List<int> msgids = new List<int>();
            msgids.Add((int)EnumLteNBCheckMsg.RRCConnectionReconfigurationComplete);
            msgids.Add((int)EnumLteNBCheckMsg.RRCConnectionReconfiguration); 
            msgids.Add((int)EnumLteNBCheckMsg.SystemInformationBlockType1); 
            msgids.Add((int)EnumLteNBCheckMsg.MeasurementReport); 
            
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < msgids.Count; i++)
            {
                int id = msgids[i];
                sb.Append(id);
                if (i < msgids.Count - 1)
                {
                    sb.Append(",");
                }
            }
            package.Content.AddParam(sb.ToString());
            AddDIYEndOpFlag(package);
        }

        private readonly Comparer comparer = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class ZTLteNBCellOmitFileItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }
        public List<ZTLteNBCellOmitCellItem> CellList { get; set; }

        public ZTLteNBCellOmitFileItem(int SN, string FileName )
        {
            this.SN = SN;
            this.FileName = FileName;
            this.CellList = new List<ZTLteNBCellOmitCellItem>();
        }
    }

    public class ZTLteNBCellOmitCellItem
    {
        public int SN { get; set; }
        public LTECell CurCell { get; set; }
        public int TAC { get; set; }
        public int ECI { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }
         //按照MeasID和频点信息存放邻区的PCI列表
        public Dictionary<int,Dictionary<int,int>> NBConfigDic { get; set; }    //Dictionary<EARFCN,Dictionary<PCI,count>>
        public Dictionary<int, int> NBMeasEarfcnDic { get; set; }               //Dictionary<MeasID, EARFCN>>

        public string DateTimeHoMsg { get; set; }
        public string DateTimeNBConfigMsg { get; set; }
        public string DateTimeSIB1Msg { get; set; }
        public string DateTimeMRMsg { get; set; }

        public bool IsGotHOMsg { get; set; }
        public bool IsGotNBConfigMsg { get; set; }
        public bool IsGotSIB1Msg { get; set; }
        public bool IsGotMRMsg { get; set; }

        public TestPoint Tp { get; set; }
        public int MRCount { get; set; }
        public float RSRPTotal { get; set; }

        public Dictionary<int, Dictionary<int, ZTLteNBCellOmitNBItem>> NBRptDic { get; set; }   //Dictionary<EARFCN, Dictionary<PCI,ZTLteNBCellOmitNBItem>>

        public ZTLteNBCellOmitCellItem()
        {
            SN = 0;
            CurCell = null;
            TAC = 0;
            ECI = 0;
            EARFCN = 0;
            PCI = 0;

            NBConfigDic = new Dictionary<int,Dictionary<int,int>>();
            NBMeasEarfcnDic = new Dictionary<int, int>();

            IsGotHOMsg = false;
            IsGotNBConfigMsg = false;
            IsGotSIB1Msg = false;
            IsGotMRMsg = false;
            DateTimeHoMsg = "";
            DateTimeNBConfigMsg = "";
            DateTimeSIB1Msg = "";
            DateTimeMRMsg = "";

            MRCount = 0;
            RSRPTotal = 0;
            NBRptDic = new Dictionary<int, Dictionary<int, ZTLteNBCellOmitNBItem>>();
        }
 
        #region 预处理
        public string CellName
        {
            get
            {
                if (CurCell == null)
                {
                    return TAC.ToString() + "_" + ECI.ToString();
                }
                else
                {
                    return CurCell.Name;
                }
            }
        }

        public string CfgNBListStr
        {
            get
            {
                StringBuilder sb = new StringBuilder();
                foreach(int earfcn in NBConfigDic.Keys)
                {
                    sb.Append("频点：");
                    sb.Append(earfcn);
                    sb.Append("(");
                    foreach (int pci in NBConfigDic[earfcn].Keys)
                    {
                        sb.Append(pci.ToString());
                        sb.Append("|");
                    }
                    sb.Append(")");
                }

                return sb.ToString();
            }
        }

        public string AvgRSRP
        {
            get
            {
                if (MRCount > 0)
                {
                    return Math.Round((RSRPTotal / (float)MRCount),2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string Longitude
        {
            get
            {
                if (Tp != null)
                {
                    return Tp.Longitude.ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string Latitude
        {
            get
            {
                if (Tp != null)
                {
                    return Tp.Latitude.ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        #endregion
    }

    public class ZTLteNBCellOmitNBItem
    {
        public int EARFCN { get; set; }
        public int PCI { get; set; }
        public LTECell NBCell { get; set; }
        public int MRCount { get; set; }
        public float RSRPTotal { get; set; }
        public int Score { get; set; }
        public string Status { get; set; }
        public double Distance { get; set; }

        public ZTLteNBCellOmitNBItem(int EARFCN, int PCI, float RSRPTotal, int index)
        {
            this.EARFCN = EARFCN;
            this.PCI = PCI;
            NBCell = null;
            this.MRCount = 1;
            this.RSRPTotal = RSRPTotal;
            this.Status = "未知";
            this.Distance = -999;
            this.Score = getScoreByIndex(index);
        }

        public void AccuScore(int index)
        {
            this.Score += getScoreByIndex(index);
        }

        public int getScoreByIndex(int index)
        {
            int score = 0;
            switch (index)
            {
                case 0:
                    score = 6;
                    break;
                case 1:
                    score = 5;
                    break;
                case 2:
                    score = 4;
                    break;
                case 3:
                    score = 3;
                    break;
                case 4:
                    score = 2;
                    break;
                case 5:
                    score = 1;
                    break;
                default:
                    break;
            }

            return score;
        }

        public void MergeData(ZTLteNBCellOmitNBItem nbCellItem)
        {
            MRCount += nbCellItem.MRCount;
            RSRPTotal += nbCellItem.RSRPTotal;
            Score += nbCellItem.Score;
        }

        public static IComparer<ZTLteNBCellOmitNBItem> GetCompareByScore()
        {
            if (comparerByScore == null)
            {
                comparerByScore = new ComparerByScore();
            }
            return comparerByScore;
        }
        public class ComparerByScore : IComparer<ZTLteNBCellOmitNBItem>
        {
            public int Compare(ZTLteNBCellOmitNBItem x, ZTLteNBCellOmitNBItem y)
            {
                return (y.Score - x.Score);
            }
        }
        private static IComparer<ZTLteNBCellOmitNBItem> comparerByScore;

        #region 预处理
        public string CellName
        {
            get
            {
                if (NBCell == null)
                {
                    return EARFCN.ToString() + "_" + PCI.ToString();
                }
                else
                {
                    return NBCell.Name;
                }
            }
        }

        public string TAC
        {
            get
            {
                if (NBCell == null)
                {
                    return "";
                }
                else
                {
                    return NBCell.TAC.ToString();
                }
            }
        }

        public string ECI
        {
            get
            {
                if (NBCell == null)
                {
                    return "";
                }
                else
                {
                    return NBCell.ECI.ToString();
                }
            }
        }

        public string AvgRSRP
        {
            get
            {
                if (MRCount > 0)
                {
                    return Math.Round((RSRPTotal / (float)MRCount),2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string DistanceStr
        {
            get
            {
                if (Distance != -999)
                {
                    return Math.Round(Distance,2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        #endregion
    }

    public class ZTLteNBCellOmitCondition
    {
        public int MRCount { get; set; }
        public int RSRP { get; set; }
        public int Distance { get; set; }

        public ZTLteNBCellOmitCondition()
        {
            MRCount = 15;
            RSRP = -110;
            Distance = 1000;
        }
    }

    public class ZTLteNBCellOmitStatItem
    {
        public int SN { get; set; }
        public LTECell CurCell { get; set; }
        public string CellName { get; set; }
        public int TAC { get; set; }
        public int ECI { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }

        public int MRCount { get; set; }
        public float RSRPTotal { get; set; }

        public Dictionary<string, ZTLteNBCellOmitNBItem> NBCellDic { get; set; }
        public List<ZTLteNBCellOmitNBItem> NBList { get; set; }

        public ZTLteNBCellOmitStatItem(ZTLteNBCellOmitCellItem cellItem)
        {
            CurCell = cellItem.CurCell;
            CellName = cellItem.CellName;
            TAC = cellItem.TAC;
            ECI = cellItem.ECI;
            EARFCN = cellItem.EARFCN;
            PCI = cellItem.PCI;
            NBCellDic = new Dictionary<string, ZTLteNBCellOmitNBItem>();
            NBList = new List<ZTLteNBCellOmitNBItem>();
        }

        public void MergeData(ZTLteNBCellOmitCellItem cellItem)
        {
            MRCount += cellItem.MRCount;
            RSRPTotal += cellItem.RSRPTotal;

            foreach(int earfcn in cellItem.NBRptDic.Keys)
            {
                foreach(int pci in cellItem.NBRptDic[earfcn].Keys)
                {
                    string nbCellName = cellItem.NBRptDic[earfcn][pci].CellName;
                    if (NBCellDic.ContainsKey(nbCellName))
                    {
                        NBCellDic[nbCellName].MergeData(cellItem.NBRptDic[earfcn][pci]);
                    }
                    else
                    {
                        NBCellDic.Add(nbCellName, cellItem.NBRptDic[earfcn][pci]);
                    }
                }
            }
        }

        #region 预处理
        public string AvgRSRP
        {
            get
            {
                if (MRCount > 0)
                {
                    return Math.Round((RSRPTotal / (float)MRCount),2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        #endregion
    }


    /**后续可能使用到，暂时保留
    public class ZTLteHOResultFile
    {
        public string FileName { get; set; }
        public Dictionary<string, ZTLteHOResultCell> cellDic { get; set; }

        public ZTLteHOResultFile()
        {
            cellDic = new Dictionary<string,ZTLteHOResultCell>();
        }
    }

    public class ZTLteHOResultCell
    {
        public LTECell CurCell { get; set; }
        public int TAC { get; set; }
        public int ECI { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }
        public List<ZTLteHOResultCellSnap> SnapList{ get; set; }

        public ZTLteHOResultCell(ZTLteNBCellOmitCellItem item)
        {
            SnapList = new List<ZTLteHOResultCellSnap>();

            LTECell lteCell = CellManager.GetInstance().GetNearestLTECell(item.Tp.DateTime, item.TAC, item.ECI, item.EARFCN, item.PCI, item.Tp.Longitude, item.Tp.Latitude);
            if (lteCell != null)
            {
                CurCell = lteCell;
            }
            TAC = item.TAC;
            ECI = item.ECI;
            EARFCN = item.EARFCN;
            PCI = item.PCI;
        }
    }

    public class ZTLteHOResultCellSnap
    {
        public int MRCount { get; set; }
        public float RxlevTotal { get; set; }
        public TestPoint Tp { get; set; }
        public List<ZTLteNBCellOmitNBItem> NBList { get; set; }

        public ZTLteHOResultCellSnap()
        {
            NBList = new List<ZTLteNBCellOmitNBItem>();
        }
    }

    public class ZTLteHONBItem
    {
        public LTECell NBCell { get; set; }
        public int UARFCN { get; set; }
        public int PCI { get; set; }
        public int RptCount { get; set; }
        public float RxlevTotal { get; set; }
    }
    */

    enum EnumLteNBCheckMsg
    {
        RRCConnectionReconfigurationComplete = 1093626370,  //0x412f6a02, "RRC Connection Reconfiguration Complete"
        RRCConnectionReconfiguration = 1093625860,          //0x412f6804, "RRC Connection Reconfiguration"
        SystemInformationBlockType1 = 1093625089,           //0x412f6501, "System Information Block Type 1"
        MeasurementReport = 1093626369,                     //0x412f6a01, "Measurement Report"

        RRCConnectionRelease = 1093625861,
        ConnectionRestablishmentRequest = 1093626112,       //0x412F6900, "Connection Reestablishment Request"
        RRCConnectionReestablishmentComplete = 0x412f6a03,
        RRCConnectionReestablishmentReject = 0x412f6701,

        GSMServiceRequest = 1316,
        PagingResponse = 1575,      //TD和GSM相同
        GSMSetup = 773,
        TDCMServiceRequest = 1899627812,
        TDSetup = 1899627269,
    }

    enum EnumMRTrigEvent
    {
        Event_A1 = 0,
        Event_A2 = 1,
        Event_A3 = 2,
        Event_A4 = 3,
        Event_A5 = 4,
        Event_A6_r10 = 5,
    }
}

