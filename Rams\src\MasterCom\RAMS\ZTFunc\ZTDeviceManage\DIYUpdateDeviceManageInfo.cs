﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyUpdateDeviceManageInfo : DiySqlMultiNonQuery
    {
        readonly List<DeviceManageInfo> changedDeviceInfolist = null;
        public DiyUpdateDeviceManageInfo(List<DeviceManageInfo> changedDeviceInfolist)
            : base()
        {
            this.changedDeviceInfolist = changedDeviceInfolist;
            MainDB = true;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = "正在向数据库导入数据......";
                queryInThread(clientProxy);
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            WaitBox.Text = "导入完毕.....";
            System.Threading.Thread.Sleep(200);
            WaitBox.Close();
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (DeviceManageInfo info in changedDeviceInfolist)
            {
                strb.AppendFormat(@"update [tb_testing_device_manage] set DeviceStatus='{1}' where [BoxID] = '{0}';",
                  info.BoxID, info.DeviceStatus);
            }
            return strb.ToString();
        }

        public override string Name
        {
            get { return "更新设备信息表"; }
        }
    }
}
