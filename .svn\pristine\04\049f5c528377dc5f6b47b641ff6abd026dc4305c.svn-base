﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class ShowBTSInfoForm : ShowFuncForm
    {
        public ShowBTSInfoForm(MainModel mm)
            : base(mm)
        { }
        FindBTSForm form = null;
        protected override void showForm()
        {
            if (form == null || form.IsDisposed)
            {
                form = new FindBTSForm(MainModel);
            }
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
            form.BringToFront();
        }

        public override string Name
        {
            get { return "呈现基站信息窗口"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19015, this.Name);
        }
    }
}
