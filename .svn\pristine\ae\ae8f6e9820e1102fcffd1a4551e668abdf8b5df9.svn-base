using System;
using System.Collections.Generic;
using System.Text;
using System.Data.OleDb;

namespace MasterCom.RAMS.Func.LoadCellExcel
{
    public class NbCellItem
    {
        public string cellCode { get; set; }
        public List<string> nbList { get; set; } = new List<string>();
        public List<string> nbListAdd { get; set; } = new List<string>();
        public List<string> nbListDel { get; set; } = new List<string>();

        public static NbCellItem FillForm(OleDbDataReader reader, System.ComponentModel.BackgroundWorker report)
        {
            NbCellItem nbCellItem = new NbCellItem();

            nbCellItem.cellCode = Convert.ToString(reader[LoadExcelFile.TITLES[7]].ToString());

            /*************************************** nb_list *********************************************/
            for (int i = 1; i < 56; i++)
            {
                int nbSite =26 + i - 1;
                if (Convert.ToString(reader[nbSite]) != null)
                {
                    string cellcode = Convert.ToString(reader[nbSite]);
                    if (!(nbCellItem.nbList.Contains(cellcode)) && (cellcode != ""))
                    {
                        nbCellItem.nbList.Add(cellcode);
                    }
                }
            }
            return nbCellItem;
        }
    }
}
