﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Func
{
    public abstract class ShowFuncForm : QueryBase
    {
        protected ShowFuncForm(MainModel mm)
            : base(mm)
        { }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        public override string IconName
        {
            get { return string.Empty; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }
        protected override void query()
        {
            showForm();
        }

        protected abstract void showForm();

    }
}