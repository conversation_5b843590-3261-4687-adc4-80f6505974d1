﻿using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public abstract class StationAcceptManagerBase
    {
        protected StationAcceptManagerBase()
        {
            ErrMsg = new StringBuilder();
            BtsInfoDic = new Dictionary<string, BtsInfoBase>();
        }

        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        //单验模板所在目录
        protected string workDir = "";
        //单验设置条件,默认只有保存目录
        protected StationAcceptConditionBase acceptCond = null;
        //指标验证类列表
        protected List<StationAcceptBase> acceptorList = null;
        //支持的最大单验小区数
        protected int MaxCellCount = 9;

        //单验配置文件助手
        //protected abstract StationAcceptConfigHelper configHelper { get; set; }
        protected abstract string templateFileName { get; }
        protected abstract string reportFileName { get; }

        public StringBuilder ErrMsg { get; set; }
        //已经单验报表名集合
        public string HasExportedFiles { get; set; }
        public Dictionary<string, BtsInfoBase> BtsInfoDic { get; set; }

        public abstract void SetAcceptCond(StationAcceptConditionBase cond, string type);

        public abstract void SetAcceptCond(StationAcceptConditionBase cond);
        public abstract void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager);
        protected abstract void fillResult(BtsInfoBase bts, Excel.Workbook eBook);

        public virtual void DoWorkAfterAnalyze()
        {
            try
            {
                calculate();
                verifyResult();
                createFileForBts();
            }
            catch (Exception e)
            {
                string errMsg = $"导出报表异常 : {e.Message}";
                log.Error(string.Format("{0} : {1}", errMsg, e.StackTrace));
                ErrMsg.AppendLine(errMsg);
            }
            finally
            {
                clear();
            }
        }

        protected virtual void calculate()
        {
            foreach (var bts in BtsInfoDic.Values)
            {
                bts.Calculate();
            }
        }

        protected virtual void verifyResult()
        {

        }

        protected virtual void createFileForBts()
        {
            StringBuilder exportedFiles = new StringBuilder();
            foreach (var btsInfo in BtsInfoDic)
            {
                int cellCount = btsInfo.Value.CellInfoList.Count;
                string btsName = btsInfo.Key;
                bool isValid = judgeValidFile(btsName, cellCount);
                if (isValid)
                {
                    string targetFile = GetTargetFile(btsName, cellCount, acceptCond.SaveFolder);
                    if (!string.IsNullOrEmpty(targetFile))
                    {
                        exportFile(exportedFiles, btsInfo.Value, targetFile);
                    }

                    saveCurReport(btsName, cellCount, btsInfo.Value);
                }
            }
            HasExportedFiles = exportedFiles.ToString().TrimEnd(',');
        }

        protected virtual bool judgeValidFile(string btsName, int cellCount)
        {
            if (cellCount > MaxCellCount)
            {
                ErrMsg.AppendLine($"基站{btsName}小区数超过{MaxCellCount}个，不支持报告导出");
                return false;
            }
            return true;
        }

        protected virtual void saveCurReport(string btsName, int cellCount, BtsInfoBase bts)
        {
            string path = getPath();
            if (string.IsNullOrEmpty(path))
            {
                return;
            }

            string targetFile = GetTargetFile(btsName, cellCount, path);
            if (!string.IsNullOrEmpty(targetFile))
            {
                exportFile(new StringBuilder(), bts, targetFile);
            }
        }

        protected virtual string getPath()
        {
            return "";
        }

        public virtual string GetTargetFile(string btsName, int cellCount, string saveFolder)
        {
            string templateFile = "";
            if (cellCount <= MaxCellCount)
            {
                templateFile = templateFileName;
            }
            templateFile = System.IO.Path.Combine(workDir, templateFile);

            if (!System.IO.File.Exists(templateFile))
            {
                ErrMsg.AppendLine($"[{templateFile}]路径下不存在报告模板文件");
                return "";
            }

            string targetFile = $"{reportFileName}_{btsName}.xlsx";
            targetFile = System.IO.Path.Combine(saveFolder, targetFile);
            //if (System.IO.File.Exists(targetFile))
            //{
            //    System.IO.File.Delete(targetFile);
            //}
            System.IO.File.Copy(templateFile, targetFile, true);
            return targetFile;
        }

        protected virtual void exportFile(StringBuilder exportedFiles, BtsInfoBase bts, string targetFile)
        {
            Excel.Application xlApp = null;
            try
            {
                WaitTextBox.Text = "正在导出Excel...";
                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing);

                fillResult(bts, eBook);

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);
                getExportedFiles(exportedFiles, bts);
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
            }
        }

        protected virtual void getExportedFiles(StringBuilder exportedFiles, BtsInfoBase bts)
        {
            exportedFiles.Append(bts.BtsName);
            exportedFiles.Append(",");
        }

        protected void insertExcelPicture(Excel.Worksheet eSheet, string picPath
            , int ltRowIndex, int ltColIndex, double width, double height)
        {
            if (System.IO.File.Exists(picPath))
            {
                Excel.Range rng = eSheet.get_Range(eSheet.Cells[ltRowIndex, ltColIndex], eSheet.Cells[ltRowIndex, ltColIndex]);
                eSheet.Shapes.AddPicture(picPath,
                    Microsoft.Office.Core.MsoTriState.msoFalse,
                    Microsoft.Office.Core.MsoTriState.msoCTrue,
                    (float)(double)rng.Left, (float)(double)rng.Top, (float)width, (float)height);
            }
        }

        protected virtual void clear()
        {

        }

        public Dictionary<string, BtsInfoBase> getBtsInfo()
        {
            return BtsInfoDic;
        }

        protected class ExcelCell
        {
            public ExcelCell(int fstRowIdx, int fstColIdx, int rowInterval, int colInterval)
            {
                FstRowIdx = fstRowIdx;
                FstColIdx = fstColIdx;
                RowInterval = rowInterval;
                ColInterval = colInterval;
            }

            public int FstRowIdx { get; set; }
            public int FstColIdx { get; set; }

            public int RowInterval { get; set; }
            public int ColInterval { get; set; }
        }
    }
}
