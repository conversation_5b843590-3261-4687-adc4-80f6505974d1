﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryFddDBLeveling : DIYSQLBase
    {
        public List<FddIndoorDBLeveling> DataList { get; private set; }
        readonly int eci;
        readonly FddDatabaseSetting setting;
        public DiyQueryFddDBLeveling(int eci, FddDatabaseSetting setting)
            : base()
        {
            MainDB = true;
            this.eci = eci;
            this.setting = setting;
        }

        public override string Name
        {
            get
            {
                return "查询FDD单验平层指标";
            }
        }

        /// <summary>
        /// 查询ECI对应最新一个工单的所有数据
        /// </summary>
        /// <returns></returns>
        protected override string getSqlTextString()
        {
            string tableName = string.Format(@"{0}.{1}.[dbo].[{2}]", setting.ServerIp, setting.DbName, setting.TableNameHead);
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat("select [楼宇名称],[楼层],[业务类型],[PCI],[RSRP(dBm)],[SINR(dB)],[上行吞吐量/kbps],[下行吞吐量/kbps] from {0} where [工单流水号] = (select top 1 [工单流水号] from {0} where ECI = {1} order by [工单流水号] desc)",
                tableName, eci);
            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[8];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx] = E_VType.E_Float;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            DataList = new List<FddIndoorDBLeveling>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    FddIndoorDBLeveling data = new FddIndoorDBLeveling();
                    data.FillData(package, eci);
                    DataList.Add(data);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class FddIndoorDBLeveling
    {
        public string Name { get; set; }
        public string BuildingName { get; set; }
        public string FloorName { get; set; }
        public string ServiceType { get; set; }
        public double PCI { get; set; }
        public double RSRP { get; set; }
        public double SINR { get; set; }
        public double ULThroughput { get; set; }
        public double DLThroughput { get; set; }
        public int ECI { get; set; }

        public void FillData(Package package, int eci)
        {
            ECI = eci;
            BuildingName = package.Content.GetParamString();
            FloorName = package.Content.GetParamString();
            ServiceType = package.Content.GetParamString();
            Name = ECI  + "-" + BuildingName + "-" + FloorName + "-" + ServiceType;
            PCI = package.Content.GetParamInt() / 1000d;
            RSRP = package.Content.GetParamInt() / 1000d;
            SINR = package.Content.GetParamInt() / 1000d;
            ULThroughput = package.Content.GetParamInt() / 1000d;
            DLThroughput = package.Content.GetParamInt() / 1000d;
        }
    }

    public class FddIndoorDBLevelingResult
    {
        public List<FddIndoorDBLeveling> LevelingDataList { get; set; } = new List<FddIndoorDBLeveling>();

        private double sumRsrp;
        private double sumSinr;
        private double sumULSpeed;
        private double sumDLSpeed;

        private int cntRsrpMoreThan85;
        private int cntRsrpMoreThan95;
        private int cntRsrpMoreThan105;
        private int cntSinrMoreThan6;
        private int cntSinrMoreThan9;
        
        public double MaxDLSpeed { get; private set; }
        public double MaxULSpeed { get; private set; }
        public string PCI { get; private set; }
        public int PointCount { get; private set; }
        public double AvgRsrp { get; private set; }
        public double AvgSinr { get; private set; }
        public double AvgULSpeed { get; private set; }
        public double AvgDLSpeed { get; private set; }
        public double SinrMoreThan6Rate { get; private set; }
        public double SinrMoreThan9Rate { get; private set; }
        public double RsrpMoreThan105Rate { get; private set; }
        public double RsrpMoreThan95Rate { get; private set; }
        public double RsrpMoreThan85Rate { get; private set; }

        public void CalculateData()
        {
            foreach (FddIndoorDBLeveling levelingData in LevelingDataList)
            {
                PointCount++;

                addRsrpCnt(levelingData);

                addSinrCnt(levelingData);

                addRsrpSinrCnt(levelingData);

                addSpeed(levelingData);
            }
            CalcResult();
        }

        private void addRsrpCnt(FddIndoorDBLeveling levelingData)
        {
            sumRsrp += levelingData.RSRP;
            if (levelingData.RSRP >= -85)
            {
                cntRsrpMoreThan85++;
            }
        }

        private void addSinrCnt(FddIndoorDBLeveling levelingData)
        {
            sumSinr += levelingData.SINR;
            if (levelingData.SINR >= 6)
            {
                cntSinrMoreThan6++;
            }
            if (levelingData.SINR >= 9)
            {
                cntSinrMoreThan9++;
            }
        }

        private void addRsrpSinrCnt(FddIndoorDBLeveling levelingData)
        {
            if (levelingData.RSRP >= -105 && levelingData.SINR >= 6)
            {
                cntRsrpMoreThan105++;
            }
            if (levelingData.RSRP >= -95 && levelingData.SINR >= 9)
            {
                cntRsrpMoreThan95++;
            }
        }

        private void addSpeed(FddIndoorDBLeveling levelingData)
        {
            sumULSpeed += levelingData.ULThroughput;
            if (levelingData.ULThroughput > MaxULSpeed)
            {
                MaxULSpeed = levelingData.ULThroughput;
            }

            sumDLSpeed += levelingData.DLThroughput;
            if (levelingData.DLThroughput > MaxDLSpeed)
            {
                MaxDLSpeed = levelingData.DLThroughput;
            }
        }

        private void CalcResult()
        {
            AvgRsrp = getDivisionResult(sumRsrp, PointCount);
            AvgSinr = getDivisionResult(sumSinr, PointCount);
            AvgULSpeed = getDivisionResult(sumULSpeed, PointCount);
            AvgDLSpeed = getDivisionResult(sumDLSpeed, PointCount);
            SinrMoreThan6Rate = getDivisionResult(cntSinrMoreThan6, PointCount, 4);
            SinrMoreThan9Rate = getDivisionResult(cntSinrMoreThan9, PointCount, 4);
            RsrpMoreThan105Rate = getDivisionResult(cntRsrpMoreThan105, PointCount, 4);
            RsrpMoreThan95Rate = getDivisionResult(cntRsrpMoreThan95, PointCount, 4);
            RsrpMoreThan85Rate = getDivisionResult(cntRsrpMoreThan85, PointCount, 4);
        }

        private double getDivisionResult(double value, int count, int places = 2)
        {
            if (count == 0)
            {
                return double.MinValue;
            }
            double res = System.Math.Round(value / count, places);
            return res;
        }

    }
}
