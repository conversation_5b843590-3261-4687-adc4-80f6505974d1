﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteVideoPlayAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public LteVideoPlayAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.FilterEventByRegion = false;
            this.IncludeMessage = true;
            this.IncludeTestPoint = true;
            this.IncludeEvent = true;
        }

        protected string RsrpStr = "";
        protected string SinrStr = "";
        protected string NCellRsrpStr = "";
        protected string AppSpeedStr = "";
        protected string TacStr = "";

        protected List<ZTLTEVideoPlayAnaItem> rebufferAnaList = new List<ZTLTEVideoPlayAnaItem>();
        protected List<ZTLTEVideoPlayAnaItem> loadAnaList = new List<ZTLTEVideoPlayAnaItem>();
        protected VideoPlayCondion_LTE videoPlayCondition = new VideoPlayCondion_LTE();
        private readonly LteURLAnalyzer analyzer = new LteURLAnalyzer();

        /// <summary>
        /// 设置参数和业务类型
        /// </summary>
        protected virtual void setParmAndServiceType()
        {
            this.RsrpStr = "lte_RSRP";
            this.SinrStr = "lte_SINR";
            this.NCellRsrpStr = "lte_NCell_RSRP";
            this.AppSpeedStr = "lte_APP_Speed_Mb";
            this.TacStr = "lte_TAC";

            this.Columns = new List<string>();
            Columns.Add(RsrpStr);
            Columns.Add(SinrStr);
            Columns.Add(TacStr);
            Columns.Add(NCellRsrpStr);
            Columns.Add(AppSpeedStr);
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_APP_Speed");
            Columns.Add("lte_APP_type");
            Columns.Add("lte_Transmission_Mode");
            Columns.Add("lte_PDSCH_BLER");
            Columns.Add("lte_Rank_Indicator");
            Columns.Add("lte_PDCCH_DL_Grant_Count");
            Columns.Add("lte_PDSCH_PRb_Num_slot");
            Columns.Add("lte_NCell_SINR");

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
        }

        protected override bool getCondition()
        {
            setParmAndServiceType();

            LteVideoPlaySettingDlg dlg = new LteVideoPlaySettingDlg(videoPlayCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                videoPlayCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            rebufferAnaList = new List<ZTLTEVideoPlayAnaItem>();
            loadAnaList = new List<ZTLTEVideoPlayAnaItem>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<Event> evtsList = fileMng.Events;
                bool noRebuffer = true;//是否是播放开始后的第一次卡顿
                string lastURL = "";    //最近一次流媒体播放URL
                DateTime lastLoadTime = new DateTime(); //最近一次加载完成时间
                DateTime lastRebufferEndTime = new DateTime(); //上一次卡顿结束时间
                ZTLTEVideoPlayAnaItem curRebufferItem = null;   //流媒体卡顿分析单元
                ZTLTEVideoPlayAnaItem curLoadItem = null;   //流媒体缓冲分析单元
                foreach (Event evt in evtsList)
                {
                    if (evt.ID == (int)ELteVideoEvt.VideoPlayRebufferStart ||
                        evt.ID == (int)ELteVideoEvt.VideoPlayRebufferStartFDD)     //卡顿开始事件
                    {
                        setStartInfo(evt, ref curRebufferItem);
                        curRebufferItem.URL = lastURL;
                        if (noRebuffer)
                        {
                            List<TestPoint> tpList = getTestPoinsByTime(lastLoadTime, evt.DateTime,
                                fileMng.TestPoints, true);
                            //获取加载完成到第一次卡顿的采样点
                            if (tpList != null && tpList.Count != 0)
                            {
                                curRebufferItem.OtherTpsList.AddRange(tpList);  //加入到非卡顿采样点
                                curRebufferItem.fillOtherMaxNBIndexList(tpList, NCellRsrpStr);
                            }
                        }
                        else
                        {
                            List<TestPoint> tpList = getTestPoinsByTime(lastRebufferEndTime, evt.DateTime,
                                fileMng.TestPoints, true);
                            //获取第N-1次卡顿结束到第N次卡顿开始的采样点 
                            if (tpList != null && tpList.Count != 0)
                            {
                                curRebufferItem.OtherTpsList.AddRange(tpList);  //加入到非卡顿采样点
                                curRebufferItem.fillOtherMaxNBIndexList(tpList, NCellRsrpStr);
                            }
                        }
                        noRebuffer = false;
                    }
                    else if (evt.ID == (int)ELteVideoEvt.VideoPlayRebufferEnd ||
                        evt.ID == (int)ELteVideoEvt.VideoPlayRebufferEndFDD)  //卡顿结束事件
                    {
                        if (curRebufferItem != null)
                        {
                            curRebufferItem.EndTime = evt.DateTime;
                            curRebufferItem.InterTime = calcInterSeconds(curRebufferItem.BeginTime,
                                curRebufferItem.EndTime); //获取卡顿时长
                            curRebufferItem.TpsList = getTestPoinsByTime(curRebufferItem.BeginTime.AddSeconds(-videoPlayCondition.PreRebufferTime),
                                curRebufferItem.EndTime, fileMng.TestPoints, false);          //获取卡顿开始前几秒到卡顿结束的采样点                          
                            setEndInfo(evt, ref curRebufferItem, rebufferAnaList);
                        }
                        lastRebufferEndTime = evt.DateTime;
                    }
                    else if (evt.ID == (int)ELteVideoEvt.VideoPlayRequest ||
                        evt.ID == (int)ELteVideoEvt.VideoPlayRequestFDD)   //流媒体请求事件（缓冲开始）
                    {
                        setStartInfo(evt, ref curLoadItem);
                        curLoadItem.URL = analyzer.GetURL(evt.SN, fileMng.Messages);
                        lastURL = curLoadItem.URL;
                    }
                    else if (evt.ID == (int)ELteVideoEvt.VideoPlayReproductionStart ||
                        evt.ID == (int)ELteVideoEvt.VideoPlayReproductionStartFDD)  //流媒体加载成功（缓冲结束）
                    {
                        lastLoadTime = evt.DateTime;
                        if (curLoadItem != null)
                        {
                            curLoadItem.EndTime = evt.DateTime;
                            curLoadItem.InterTime = calcInterSeconds(curLoadItem.BeginTime, curLoadItem.EndTime); //获取缓冲时长
                            curLoadItem.TpsList = getTestPoinsByTime(curLoadItem.BeginTime.AddSeconds(-videoPlayCondition.PreLoadTime),
                                curLoadItem.EndTime, fileMng.TestPoints, false);//获取缓冲开始前几秒到缓冲结束的采样点
                            setEndInfo(evt, ref curLoadItem, loadAnaList);
                        }
                    }
                    else if (evt.ID == (int)ELteVideoEvt.VideoPlayLastData
                        || evt.ID == (int)ELteVideoEvt.VideoPlayDrop
                        || evt.ID == (int)ELteVideoEvt.VideoPlayLastDataFDD
                        || evt.ID == (int)ELteVideoEvt.VideoPlayDropFDD)
                    {
                        lastURL = "";   //流媒体播放完成重新获取URL
                        if (rebufferAnaList.Count != 0 && !noRebuffer)
                        {
                            //如果这次播放周期里有卡顿事件
                            int lastIndex = rebufferAnaList.Count - 1;
                            List<TestPoint> tpList = getTestPoinsByTime(rebufferAnaList[lastIndex].EndTime, evt.DateTime,
                                fileMng.TestPoints, true);//截取最后一次卡顿结束到播放结束的采样点
                            if (tpList != null && tpList.Count != 0)
                            {
                                rebufferAnaList[lastIndex].OtherTpsList.AddRange(tpList); //加入非卡顿采样点
                                rebufferAnaList[lastIndex].fillOtherMaxNBIndexList(tpList, NCellRsrpStr);
                            }
                        }
                        if (loadAnaList.Count != 0 && noRebuffer)
                        {
                            //如果这次播放周期里没卡顿事件则有非缓冲周期
                            int lastIndex = loadAnaList.Count - 1;
                            List<TestPoint> tpList = getTestPoinsByTime(loadAnaList[lastIndex].EndTime,
                                evt.DateTime, fileMng.TestPoints, true);//截取缓冲结束到播放开始的采样点
                            if (tpList != null && tpList.Count != 0)
                            {
                                loadAnaList[lastIndex].OtherTpsList.AddRange(tpList); //加入非缓冲采样点
                                loadAnaList[lastIndex].fillOtherMaxNBIndexList(tpList, NCellRsrpStr);
                            }
                        }
                        noRebuffer = true;   //重新开始新的流媒体播放周期
                    }
                    else if (evt.ID == (int)ELteVideoEvt.VideoPlayFirstData ||
                        evt.ID == (int)ELteVideoEvt.VideoPlayFirstDataFDD) 
                    {
                        //流媒体开始播放
                    }
                }
            }
        }

        /// <summary>
        /// 设置开始信息
        /// </summary>
        /// <param name="evt">开始事件</param>
        /// <param name="anaItem">分析单元</param>
        private void setStartInfo(Event evt, ref ZTLTEVideoPlayAnaItem anaItem)
        {
            anaItem = new ZTLTEVideoPlayAnaItem(evt.FileName);
            anaItem.BeginTime = evt.DateTime;
            anaItem.BeginLongitude = evt.Longitude;
            anaItem.BeginLatitude = evt.Latitude;
        }

        protected static double calcInterSeconds(DateTime beginTime, DateTime endTime)
        {
            TimeSpan ts = endTime.Subtract(beginTime);
            return ts.TotalSeconds;
        }

        /// <summary>
        /// 设置结束信息
        /// </summary>
        /// <param name="evt">结束事件</param>
        /// <param name="anaItem">分析单元</param>
        /// <param name="list">结果列表</param>
        private void setEndInfo(Event evt, ref ZTLTEVideoPlayAnaItem anaItem, List<ZTLTEVideoPlayAnaItem> list)
        {
            if (isValidPeriod(anaItem.TpsList))
            {
                anaItem.FillItem(RsrpStr, SinrStr, TacStr, AppSpeedStr, NCellRsrpStr);
                anaItem.SN = list.Count + 1;
                anaItem.EndLatitude = evt.Latitude;
                anaItem.EndLongitude = evt.Longitude;
                anaItem.StatTpsList = getVaildTestPoints(anaItem.TpsList, anaItem.MaxNBIndexList);
                list.Add(anaItem);
            }
            anaItem = null;
        }

        protected override void fireShowForm()
        {
            if (rebufferAnaList.Count == 0 && loadAnaList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            MainModel.FireSetDefaultMapSerialTheme(RsrpStr);
            LteVideoPlayAnaForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteVideoPlayAnaForm)) as LteVideoPlayAnaForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new LteVideoPlayAnaForm(MainModel);
            }
            frm.FillData(rebufferAnaList, loadAnaList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        /// <summary>
        /// 筛选在区域内的采样点
        /// </summary>
        /// <param name="tpList"></param>
        /// <returns></returns>
        protected List<KeyValuePair<TestPoint, int>> getVaildTestPoints(List<TestPoint> tpList, List<int> maxNBIndexList)
        {
            List<KeyValuePair<TestPoint, int>> ret = new List<KeyValuePair<TestPoint, int>>();
            if (tpList.Count == maxNBIndexList.Count)
            {
                for (int i = 0; i < tpList.Count; i++)
                {
                    if (isValidTestPoint(tpList[i]))
                    {
                        ret.Add(new KeyValuePair<TestPoint, int>(tpList[i], maxNBIndexList[i]));
                    }
                }
            }
            return ret;
        }

        /// <summary>
        /// 根据前后时间获取采样点
        /// </summary>
        /// <param name="period"></param>
        /// <param name="tps"></param>
        /// <param name="countInRegion">是否需要判断在区域内</param>
        /// <returns></returns>
        protected List<TestPoint> getTestPoinsByTime(DateTime beginTime, DateTime endTime, List<TestPoint> tps, bool countInRegion)
        {
            TimePeriod period = new TimePeriod();
            List<TestPoint> ret = new List<TestPoint>();
            if (!period.SetPeriod(beginTime, endTime))    //开始时间和结束时间不合逻辑
            {
                return ret;
            }
            for (int index = 0; index < tps.Count; index++)
            {
                TestPoint tp = tps[index];
                if (tp.DateTime >= period.BeginTime && tp.DateTime <= period.EndTime)
                {
                    if (countInRegion && !isValidTestPoint(tp))
                    {
                        continue;
                    }
                    ret.Add(tp);
                }
                else if (tp.DateTime > period.EndTime)
                {
                    break;
                }
            }
            return ret;
        }

        /// <summary>
        /// 判断时间段的采样点存在区域内
        /// </summary>
        /// <param name="tpsList"></param>
        /// <returns></returns>
        protected  virtual bool isValidPeriod(List<TestPoint> tpsList)
        {
            foreach (TestPoint tp in tpsList)
            {
                if (isValidTestPoint(tp))
                {
                    return true;
                }
            }
            return false;
        }
    }
    /// <summary>
    /// 流媒体分析单元
    /// </summary>
    public class ZTLTEVideoPlayAnaItem : LteHttpPageOrVideoPlay.LteCommonParm
    {
        public ZTLTEVideoPlayAnaItem(string fileName)
        {
            this.FileName = fileName;
            this.TpsList = new List<TestPoint>();
            this.OtherTpsList = new List<TestPoint>();
            this.OtherMaxNBIndexList = new List<int>();
        }
        public string URL
        {
            get;
            set;
        }
        public double InterTime
        {
            get;
            set;
        }
        /// <summary>
        /// 非卡顿（缓冲）区间的采样点
        /// </summary>
        public List<TestPoint> OtherTpsList
        {
            get;
            set;
        }
        /// <summary>
        ///  非卡顿（缓冲)区间的最强邻区索引表
        /// </summary>
        public List<int> OtherMaxNBIndexList { get; set; }


        /// <summary>
        /// 根据新增加的非卡顿（缓冲）区间的采样点相应生成最强邻区索引表
        /// </summary>
        /// <param name="tpList"></param>
        /// <param name="nCellRsrpStr"></param>
        public void fillOtherMaxNBIndexList(List<TestPoint> tpList, string nCellRsrpStr)
        {
            if (tpList == null || tpList.Count == 0)
            {
                return;
            }
            List<int> indexList = new List<int>();
            int maxNBIndex = -1;
            foreach (TestPoint tp in tpList)
            {
                float nCellRsrpMax = float.MinValue;
                for (int i = 0; i < 10; i++)
                {
                    float? rsrp = (float?)tp[nCellRsrpStr, i];
                    if (rsrp == null || rsrp < -141 || rsrp > 25)
                    {
                        continue;
                    }
                    if (rsrp > nCellRsrpMax)
                    {
                        nCellRsrpMax = (float)rsrp;
                        maxNBIndex = i;
                    }
                }
                indexList.Add(maxNBIndex);
            }
            this.OtherMaxNBIndexList.AddRange(indexList);
        }
    }
    public class VideoPlayCondion_LTE
    {
        /// <summary>
        /// 流媒体卡顿前几秒
        /// </summary>
        public int PreRebufferTime { get; set; } = 5;
        /// <summary>
        /// 流媒体缓冲前几秒
        /// </summary>
        public int PreLoadTime { get; set; } = 5;
    }
    /// <summary>
    /// 流媒体相关事件
    /// </summary>
    enum ELteVideoEvt
    {
        VideoPlayRequest = 1231,
        VideoPlayFirstData = 1232,
        VideoPlayRebufferStart = 1233,
        VideoPlayRebufferEnd = 1234,
        VideoPlayLastData = 1235,
        VideoPlayDrop = 1237,
        VideoPlayReproductionStart = 1238,
        VideoPlayRequestFDD = 3231,
        VideoPlayFirstDataFDD = 3232,
        VideoPlayRebufferStartFDD = 3233,
        VideoPlayRebufferEndFDD = 3234,
        VideoPlayLastDataFDD = 3235,
        VideoPlayDropFDD = 3237,
        VideoPlayReproductionStartFDD = 3238,
    }
}
