﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model.RoadProtection
{
    public class DIYQueryGSMData : DIYSQLBase
    {
        readonly List<int> gsmIDList = new List<int>();
        public List<int> GsmIDList
        {
            get { return gsmIDList; }
        }

        readonly List<GSMData> gsmList = new List<GSMData>();
        public List<GSMData> GSMDataList
        {
            get { return gsmList; }
        }

        readonly Dictionary<int, GSMData> gsmDic = new Dictionary<int, GSMData>();
        public Dictionary<int, GSMData> GsmDic
        {
            get { return gsmDic; }
        }

        private readonly string _dbname;
        public DIYQueryGSMData(MainModel mainModel, DBSetting db)
            : base(mainModel)
        {
            _dbname = db.Dbname;
        }
        protected override string getSqlTextString()
        {
            return "select distinct top 10 istatnum as INUM,weightvalue as WVAL from " + _dbname + ".dbo.v_autotest_relana_gsmevent order by weightvalue desc,istatnum";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Float;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            gsmList.Clear();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    GSMData gsmdata = new GSMData();
                    gsmdata.Inum = package.Content.GetParamInt();
                    gsmdata.Wval = package.Content.GetParamFloat();

                    gsmList.Add(gsmdata);
                    //do your code here
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "DIYQueryGSMData"; }
        }
    }
}
