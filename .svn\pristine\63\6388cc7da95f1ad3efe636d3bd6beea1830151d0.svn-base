﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTHandOverAndCellReselListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel1 = new System.Windows.Forms.Panel();
            this.chkFileToCell = new System.Windows.Forms.CheckBox();
            this.chkSpeedLimit = new System.Windows.Forms.CheckBox();
            this.numSpeedMin = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.numSpeedMax = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.btnOK = new System.Windows.Forms.Button();
            this.numTimeLimit = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.tlvHandOverInfo = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnHOCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowAllEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.tlvCellInfo = new BrightIdeasSoftware.TreeListView();
            this.olvColumnCellInfoSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellInfoHOCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuCell = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tlvHandOverInfo)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tlvCellInfo)).BeginInit();
            this.contextMenuCell.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.chkFileToCell);
            this.panel1.Controls.Add(this.chkSpeedLimit);
            this.panel1.Controls.Add(this.numSpeedMin);
            this.panel1.Controls.Add(this.label4);
            this.panel1.Controls.Add(this.numSpeedMax);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Controls.Add(this.btnOK);
            this.panel1.Controls.Add(this.numTimeLimit);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1010, 45);
            this.panel1.TabIndex = 0;
            // 
            // chkFileToCell
            // 
            this.chkFileToCell.AutoSize = true;
            this.chkFileToCell.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkFileToCell.Location = new System.Drawing.Point(623, 15);
            this.chkFileToCell.Name = "chkFileToCell";
            this.chkFileToCell.Size = new System.Drawing.Size(108, 16);
            this.chkFileToCell.TabIndex = 16;
            this.chkFileToCell.Text = "小区与文件关联";
            this.chkFileToCell.UseVisualStyleBackColor = true;
            this.chkFileToCell.CheckedChanged += new System.EventHandler(this.chkFileToCell_CheckedChanged);
            // 
            // chkSpeedLimit
            // 
            this.chkSpeedLimit.AutoSize = true;
            this.chkSpeedLimit.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSpeedLimit.Location = new System.Drawing.Point(206, 14);
            this.chkSpeedLimit.Name = "chkSpeedLimit";
            this.chkSpeedLimit.Size = new System.Drawing.Size(96, 16);
            this.chkSpeedLimit.TabIndex = 15;
            this.chkSpeedLimit.Text = "启用车速限制";
            this.chkSpeedLimit.UseVisualStyleBackColor = true;
            this.chkSpeedLimit.CheckedChanged += new System.EventHandler(this.chkSpeedLimit_CheckedChanged);
            // 
            // numSpeedMin
            // 
            this.numSpeedMin.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSpeedMin.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numSpeedMin.Location = new System.Drawing.Point(304, 11);
            this.numSpeedMin.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numSpeedMin.Name = "numSpeedMin";
            this.numSpeedMin.Size = new System.Drawing.Size(75, 21);
            this.numSpeedMin.TabIndex = 13;
            this.numSpeedMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(539, 16);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 12);
            this.label4.TabIndex = 9;
            this.label4.Text = "公里/小时";
            // 
            // numSpeedMax
            // 
            this.numSpeedMax.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSpeedMax.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numSpeedMax.Location = new System.Drawing.Point(458, 11);
            this.numSpeedMax.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numSpeedMax.Name = "numSpeedMax";
            this.numSpeedMax.Size = new System.Drawing.Size(75, 21);
            this.numSpeedMax.TabIndex = 7;
            this.numSpeedMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSpeedMax.Value = new decimal(new int[] {
            180,
            0,
            0,
            0});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(387, 16);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 8;
            this.label5.Text = "≤ 时速 ≤";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(167, 16);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 5;
            this.label3.Text = "秒";
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(911, 7);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 4;
            this.btnOK.Text = "重新统计";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // numTimeLimit
            // 
            this.numTimeLimit.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numTimeLimit.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numTimeLimit.Location = new System.Drawing.Point(89, 11);
            this.numTimeLimit.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numTimeLimit.Name = "numTimeLimit";
            this.numTimeLimit.Size = new System.Drawing.Size(75, 21);
            this.numTimeLimit.TabIndex = 3;
            this.numTimeLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(12, 16);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(71, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "时间间隔 ≤";
            // 
            // tlvHandOverInfo
            // 
            this.tlvHandOverInfo.AllColumns.Add(this.olvColumnSN);
            this.tlvHandOverInfo.AllColumns.Add(this.olvColumnFileName);
            this.tlvHandOverInfo.AllColumns.Add(this.olvColumnHOCount);
            this.tlvHandOverInfo.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tlvHandOverInfo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnFileName,
            this.olvColumnHOCount});
            this.tlvHandOverInfo.ContextMenuStrip = this.contextMenuStrip;
            this.tlvHandOverInfo.Cursor = System.Windows.Forms.Cursors.Default;
            this.tlvHandOverInfo.FullRowSelect = true;
            this.tlvHandOverInfo.GridLines = true;
            this.tlvHandOverInfo.Location = new System.Drawing.Point(12, 51);
            this.tlvHandOverInfo.MultiSelect = false;
            this.tlvHandOverInfo.Name = "tlvHandOverInfo";
            this.tlvHandOverInfo.OwnerDraw = true;
            this.tlvHandOverInfo.ShowGroups = false;
            this.tlvHandOverInfo.Size = new System.Drawing.Size(638, 409);
            this.tlvHandOverInfo.TabIndex = 1;
            this.tlvHandOverInfo.UseCompatibleStateImageBehavior = false;
            this.tlvHandOverInfo.View = System.Windows.Forms.View.Details;
            this.tlvHandOverInfo.VirtualMode = true;
            this.tlvHandOverInfo.SelectedIndexChanged += new System.EventHandler(this.tlvHandOverInfo_SelectedIndexChanged);
            this.tlvHandOverInfo.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.tlvHandOverInfo_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 80;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 450;
            // 
            // olvColumnHOCount
            // 
            this.olvColumnHOCount.HeaderFont = null;
            this.olvColumnHOCount.Text = "乒乓切换组数";
            this.olvColumnHOCount.Width = 100;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportToExcel,
            this.miShowAllEvent});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(173, 92);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(172, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(172, 22);
            this.miCollapseAll.Text = "全部折叠";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(172, 22);
            this.miExportToExcel.Text = "导出Excel";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // miShowAllEvent
            // 
            this.miShowAllEvent.Name = "miShowAllEvent";
            this.miShowAllEvent.Size = new System.Drawing.Size(172, 22);
            this.miShowAllEvent.Text = "显示全部切换事件";
            this.miShowAllEvent.Click += new System.EventHandler(this.miShowAllEvent_Click);
            // 
            // tlvCellInfo
            // 
            this.tlvCellInfo.AllColumns.Add(this.olvColumnCellInfoSN);
            this.tlvCellInfo.AllColumns.Add(this.olvColumnCellName);
            this.tlvCellInfo.AllColumns.Add(this.olvColumnLAC);
            this.tlvCellInfo.AllColumns.Add(this.olvColumnCI);
            this.tlvCellInfo.AllColumns.Add(this.olvColumnCellInfoHOCount);
            this.tlvCellInfo.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tlvCellInfo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnCellInfoSN,
            this.olvColumnCellName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnCellInfoHOCount});
            this.tlvCellInfo.ContextMenuStrip = this.contextMenuCell;
            this.tlvCellInfo.Cursor = System.Windows.Forms.Cursors.Default;
            this.tlvCellInfo.FullRowSelect = true;
            this.tlvCellInfo.GridLines = true;
            this.tlvCellInfo.Location = new System.Drawing.Point(669, 51);
            this.tlvCellInfo.MultiSelect = false;
            this.tlvCellInfo.Name = "tlvCellInfo";
            this.tlvCellInfo.OwnerDraw = true;
            this.tlvCellInfo.ShowGroups = false;
            this.tlvCellInfo.Size = new System.Drawing.Size(329, 409);
            this.tlvCellInfo.TabIndex = 2;
            this.tlvCellInfo.UseCompatibleStateImageBehavior = false;
            this.tlvCellInfo.View = System.Windows.Forms.View.Details;
            this.tlvCellInfo.VirtualMode = true;
            this.tlvCellInfo.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.tlvCellInfo_MouseDoubleClick);
            // 
            // olvColumnCellInfoSN
            // 
            this.olvColumnCellInfoSN.HeaderFont = null;
            this.olvColumnCellInfoSN.Text = "序号";
            this.olvColumnCellInfoSN.Width = 40;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 80;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnCellInfoHOCount
            // 
            this.olvColumnCellInfoHOCount.HeaderFont = null;
            this.olvColumnCellInfoHOCount.Text = "乒乓次数";
            this.olvColumnCellInfoHOCount.Width = 80;
            // 
            // contextMenuCell
            // 
            this.contextMenuCell.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem3});
            this.contextMenuCell.Name = "contextMenuStrip";
            this.contextMenuCell.Size = new System.Drawing.Size(130, 26);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(129, 22);
            this.toolStripMenuItem3.Text = "导出Excel";
            this.toolStripMenuItem3.Click += new System.EventHandler(this.miCellExportToExcel_Click);
            // 
            // ZTHandOverAndCellReselListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1010, 472);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.tlvCellInfo);
            this.Controls.Add(this.tlvHandOverInfo);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.Fixed3D;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ZTHandOverAndCellReselListForm";
            this.Text = "乒乓切换分析";
            this.Load += new System.EventHandler(this.ZTHandOverAndCellReselListForm_Load);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tlvHandOverInfo)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tlvCellInfo)).EndInit();
            this.contextMenuCell.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private BrightIdeasSoftware.TreeListView tlvHandOverInfo;
        private System.Windows.Forms.NumericUpDown numTimeLimit;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numSpeedMax;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numSpeedMin;
        private System.Windows.Forms.CheckBox chkSpeedLimit;
        private System.Windows.Forms.CheckBox chkFileToCell;
        private BrightIdeasSoftware.TreeListView tlvCellInfo;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnHOCount;
        private BrightIdeasSoftware.OLVColumn olvColumnCellInfoSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnCellInfoHOCount;
        private System.Windows.Forms.ContextMenuStrip contextMenuCell;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem3;
        private System.Windows.Forms.ToolStripMenuItem miShowAllEvent;
    }
}