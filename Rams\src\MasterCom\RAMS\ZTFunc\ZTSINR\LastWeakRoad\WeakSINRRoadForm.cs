﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakSINRRoadForm : MinCloseForm
    {
        public string themeName { get; set; } = "";
        public WeakSINRRoadForm()
        {
            InitializeComponent();
        }

        public WeakSINRRoadCondition weakSINRRoadCondition { get; set; }
        public void FillData(List<WeakSINRRoad> list)
        {
            if (weakSINRRoadCondition!=null)
            {
                //显示FDD采样点占比
                gv.Columns["FDDPointPercent"].Visible = weakSINRRoadCondition.IsShowFDDPointFreq;
            }

            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            MainModel.ClearDTData();
            foreach (WeakSINRRoad road in list)
            {
                foreach (TestPoint tp in road.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            themeName = "TD_LTE_SINR";
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            MainModel.FireDTDataChanged(this);
        }

        //LteFdd重载方法
        public void FillData(List<WeakSINRRoad_LteFdd> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            gv.BestFitColumns();
            MainModel.ClearDTData();
            foreach (WeakSINRRoad_LteFdd road in list)
            {
                foreach (TestPoint tp in road.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            themeName = "LTE_FDD:SINR";
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            MainModel.FireDTDataChanged(this);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            if(themeName == "TD_LTE_SINR")
            {
                WeakSINRRoad weakCover = gv.GetFocusedRow() as WeakSINRRoad;
                if (weakCover != null)
                {
                    MainModel.DTDataManager.Clear();
                    foreach (TestPoint tp in weakCover.TestPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    MainModel.FireDTDataChanged(this);
                    TestPoint midTp = weakCover.TestPoints[weakCover.TestPoints.Count / 2];
                    MainModel.MainForm.GetMapForm().GoToView(midTp.Longitude, midTp.Latitude, 6000);
                }
            }
            else
            {
                //************LteFdd**************
                WeakSINRRoad_LteFdd weakCover = gv.GetFocusedRow() as WeakSINRRoad_LteFdd;
                if (weakCover != null)
                {
                    MainModel.DTDataManager.Clear();
                    foreach (TestPoint tp in weakCover.TestPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    MainModel.FireDTDataChanged(this);
                    TestPoint midTp = weakCover.TestPoints[weakCover.TestPoints.Count / 2];
                    MainModel.MainForm.GetMapForm().GoToView(midTp.Longitude, midTp.Latitude, 6000);
                }
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            List<List<object>> exportList = GridViewTransfer.Transfer(this.gridControl);
            ExcelNPOIManager.ExportToExcel(exportList);
        }
    }
}
