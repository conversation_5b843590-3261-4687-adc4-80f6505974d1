﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public enum VoiCoverResult
    {
        Succeed,
        Failed,
        Cancel,
    }

    public class VoiCoverCondition
    {
        public BTSType GsmType { get; set; }
        public TDNodeBType TdType { get; set; }
        public LTEBTSType LteType { get; set; }
        public WNodeBType WType { get; set; }
        public CDNodeBType CType { get; set; }

        public VoiCoverCondition()
        {
            GsmType = BTSType.Outdoor;
            TdType = TDNodeBType.Outdoor;
            LteType = LTEBTSType.Outdoor;
            WType = WNodeBType.Outdoor;
            CType = CDNodeBType.Outdoor;
        }
    }
}
