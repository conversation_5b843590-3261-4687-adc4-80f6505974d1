﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using Excel = Microsoft.Office.Interop.Excel;
using System.Data;
using System.Data.Sql;
using System.Data.SqlClient;

namespace MasterCom.RAMS.ZTFunc.AcceptHistory
{
    class UploaderManager
    {
        public static UploaderManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new UploaderManager();
                }
                return instance;
            }
        }

        public void UploadResult(Excel.Workbook eBook, string btsName, int cellCount)
        {
            DataRow siteRow = null;
            DataRow[] cellRows = null;
            FillByTranslators(eBook, out siteRow, out cellRows, cellCount);

            long identity = JavaDate.GetMilliseconds(DateTime.Now);
            siteRow["id"] = identity;
            DiySqlSite diySite = new DiySqlSite(siteRow);
            diySite.Query();

            foreach (DataRow dr in cellRows)
            {
                dr["sid"] = identity;
                DiySqlCell diyCell = new DiySqlCell(dr);
                diyCell.Query();
            }
        }

        private void FillByTranslators(Excel.Workbook eBook, out DataRow siteRow, out DataRow[] cellRows, int cellCount)
        {
            siteRow = UploadTable.GetSiteRow();
            if (cellCount <= 3)
            {
                cellRows = new DataRow[] {
                UploadTable.GetCellRow(),
                UploadTable.GetCellRow(),
                UploadTable.GetCellRow(),
                };
            }
            else
            {
                cellRows = new DataRow[] {
                UploadTable.GetCellRow(),
                UploadTable.GetCellRow(),
                UploadTable.GetCellRow(),
                UploadTable.GetCellRow(),
                UploadTable.GetCellRow(),
                UploadTable.GetCellRow(),
                };
            }

            foreach (SiteTranslatorBase s in siteTranslators)
            {
                s.Fill(eBook, siteRow, cellRows.Length);
            }

            foreach (CellTranslatorBase c in cellTranslators)
            {
                c.Fill(eBook, cellRows);
            }
        }

        private UploaderManager()
        {
            siteTranslators = new List<SiteTranslatorBase>()
            {
                new SiteTranslatorInfo(),
                new SiteTranslatorInnerHandover(),
            };

            cellTranslators = new List<CellTranslatorBase>()
            {
                //new CellTranslatorAll(),
                new CellTranslatorInfo(),
                new CellTranslatorCountKPI(),
                new CellTranslatorDataKPI(),
            };
        }

        private readonly List<SiteTranslatorBase> siteTranslators;

        private readonly List<CellTranslatorBase> cellTranslators;

        private static UploaderManager instance;
    }

    static class UploadTable
    {
        public static DataRow GetSiteRow()
        {
            return siteTable.NewRow();
        }

        public static DataRow GetCellRow()
        {
            return cellTable.NewRow();
        }

        public static string SiteInsertSql(DataRow dr)
        {
            return string.Format("insert into tb_lte_site_acceptance"
                + "([id],[日期],[地州],[基站名称],[基站类型],[ENodeBID],[验证通过],[系统内切换尝试次数],[系统内切换成功次数]) "
                + "values "
                + "({0}, {1}, '{2}', '{3}', '{4}', {5}, '{6}', {7}, {8})",
                (long)dr["id"], JavaDate.GetMilliseconds(DateTime.Now) / 1000,
                Value(dr, "地州", ""), 
                Value(dr, "基站名称", ""), 
                Value(dr, "基站类型", ""), 
                Value(dr, "ENodeBID"), 
                Value(dr, "验证通过", "否"), 
                Value(dr, "系统内切换尝试次数"), 
                Value(dr, "系统内切换成功次数"));
        }

        public static string CellInsertSql(DataRow dr)
        {
            string fmt = @"insert into tb_lte_cell_acceptance
                        ([sid],[sectorid],[验证通过],
                        [下载SINR好点],[下载SINR中点],[下载SINR差点],[下载RSRP好点],[下载RSRP中点],[下载RSRP差点],
                        [上传SINR好点],[上传SINR中点],[上传SINR差点],[上传RSRP好点],[上传RSRP中点],[上传RSRP差点],
                        [下载速率好点],[下载速率中点],[下载速率差点],[上传速率好点],[上传速率中点],[上传速率差点],
                        [RRC连接尝试次数],[RRC连接成功次数],[ERAB连接尝试次数],[ERAB连接成功次数],[Access尝试次数],[Access成功次数],
                        [被叫CSFB尝试次数],[被叫CSFB成功次数],[34G尝试次数],[34G成功次数])
                        values 
                        ({0}, {1}, '{2}',
                        {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10}, 
                        {11}, {12}, {13}, {14}, {15}, {16}, {17}, {18},
                        {19}, {20}, {21}, {22}, {23}, {24}, {25}, {26},                        
                        {27}, {28}, {29}, {30})";
            return string.Format(fmt, (long)dr["sid"], (int)dr["sectorid"], (string)dr["验证通过"],
                Value(dr, "下载SINR好点"), Value(dr, "下载SINR中点"), Value(dr, "下载SINR差点"),
                Value(dr, "下载RSRP好点"), Value(dr, "下载RSRP中点"), Value(dr, "下载RSRP差点"), 
                Value(dr, "上传SINR好点"), Value(dr, "上传SINR中点"), Value(dr, "上传SINR差点"), 
                Value(dr, "上传RSRP好点"), Value(dr, "上传RSRP中点"), Value(dr, "上传RSRP差点"), 
                Value(dr, "下载速率好点"), Value(dr, "下载速率中点"), Value(dr, "下载速率差点"),
                Value(dr, "上传速率好点"), Value(dr, "上传速率中点"), Value(dr, "上传速率差点"),
                Value(dr, "RRC连接尝试次数"), Value(dr, "RRC连接成功次数"), Value(dr, "ERAB连接尝试次数"),
                Value(dr, "ERAB连接成功次数"), Value(dr, "Access尝试次数"), Value(dr, "Access成功次数"), 
                Value(dr, "被叫CSFB尝试次数"), Value(dr, "被叫CSFB成功次数"), Value(dr, "34G尝试次数"), 
                Value(dr, "34G成功次数"));
        }

        private static string Value(DataRow dr, string fieldName, string defaultValue)
        {
            return Convert.IsDBNull(dr[fieldName]) ? defaultValue : dr[fieldName].ToString();
        }

        private static string Value(DataRow dr, string fieldName)
        {
            return Convert.IsDBNull(dr[fieldName]) ? "-1000000" : dr[fieldName].ToString();
        }

        static DataTable initSiteTable()
        {
            DataTable table = new DataTable();
            table.Columns.Add("id", typeof(long));
            table.Columns.Add("日期", typeof(DateTime));
            table.Columns.Add("地州", typeof(string));
            table.Columns.Add("基站名称", typeof(string));
            table.Columns.Add("基站类型", typeof(string));
            table.Columns.Add("ENodeBID", typeof(int));
            table.Columns.Add("验证通过", typeof(string));
            table.Columns.Add("系统内切换尝试次数", typeof(int));
            table.Columns.Add("系统内切换成功次数", typeof(int));
            return table;
        }

        static DataTable initCelTable()
        {
            DataTable table = new DataTable();
            table.Columns.Add("sid", typeof(long));
            table.Columns.Add("sectorid", typeof(int));
            table.Columns.Add("验证通过", typeof(string));

            table.Columns.Add("下载SINR好点", typeof(double));
            table.Columns.Add("下载SINR中点", typeof(double));
            table.Columns.Add("下载SINR差点", typeof(double));

            table.Columns.Add("下载RSRP好点", typeof(double));
            table.Columns.Add("下载RSRP中点", typeof(double));
            table.Columns.Add("下载RSRP差点", typeof(double));

            table.Columns.Add("上传SINR好点", typeof(double));
            table.Columns.Add("上传SINR中点", typeof(double));
            table.Columns.Add("上传SINR差点", typeof(double));

            table.Columns.Add("上传RSRP好点", typeof(double));
            table.Columns.Add("上传RSRP中点", typeof(double));
            table.Columns.Add("上传RSRP差点", typeof(double));

            table.Columns.Add("下载速率好点", typeof(double));
            table.Columns.Add("下载速率中点", typeof(double));
            table.Columns.Add("下载速率差点", typeof(double));

            table.Columns.Add("上传速率好点", typeof(double));
            table.Columns.Add("上传速率中点", typeof(double));
            table.Columns.Add("上传速率差点", typeof(double));

            table.Columns.Add("RRC连接尝试次数", typeof(int));
            table.Columns.Add("RRC连接成功次数", typeof(int));

            table.Columns.Add("ERAB连接尝试次数", typeof(int));
            table.Columns.Add("ERAB连接成功次数", typeof(int));

            table.Columns.Add("Access尝试次数", typeof(int));
            table.Columns.Add("Access成功次数", typeof(int));

            table.Columns.Add("被叫CSFB尝试次数", typeof(int));
            table.Columns.Add("被叫CSFB成功次数", typeof(int));

            table.Columns.Add("34G尝试次数", typeof(int));
            table.Columns.Add("34G成功次数", typeof(int));
            return table;
        }

        private static DataTable siteTable = initSiteTable();

        private static DataTable cellTable = initCelTable();
    }

    abstract class TranslatorBase
    {
        protected object GetExcelValue(Excel.Workbook eBook, int sheetIndex, string cell)
        {
            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[sheetIndex];
            Excel.Range rng = eSheet.get_Range(cell, Type.Missing);
            return rng.get_Value(Type.Missing);
        }
        protected object GetExcelValue(Excel.Workbook eBook, int sheetIndex, int colIndex, int rowIndex)
        {
            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[sheetIndex];
            object cell = eSheet.Cells[rowIndex, colIndex];
            Excel.Range rng = eSheet.get_Range(cell, cell);
            return rng.get_Value(Type.Missing);
        }

        protected void SetDataRow(DataRow dr, string fieldName, object value, Type vType)
        {
            dr[fieldName] = ConvertValue(value, vType);
        }

        private object ConvertValue(object srcValue, Type targetType)
        {
            if (srcValue == null)
            {
                return DBNull.Value;
            }
            
            if (typeof(int) == targetType)
            {
                return ConvertIntValue(srcValue);
            }
            else if (typeof(double) == targetType)
            {
                return ConvertDoubleValue(srcValue);
            }
            else if (typeof(string) == targetType)
            {
                return srcValue.ToString();
            }
            return DBNull.Value;
        }

        private object ConvertIntValue(object srcValue)
        {
            int retValue = 0;
            if (int.TryParse(srcValue.ToString(), out retValue))
            {
                return retValue;
            }
            return DBNull.Value;
        }

        private object ConvertDoubleValue(object srcValue)
        {
            double retValue = 0;
            if (double.TryParse(srcValue.ToString(), out retValue))
            {
                return retValue;
            }
            return DBNull.Value;
        }
    }

    abstract class SiteTranslatorBase : TranslatorBase
    {
        public abstract void Fill(Excel.Workbook eBook, DataRow dr, int cellCount);
    }

    class SiteTranslatorInfo : SiteTranslatorBase
    {
        public override void Fill(Excel.Workbook eBook, DataRow dr, int cellCount)
        {
            object city = GetExcelValue(eBook, 1, "z5");
            SetDataRow(dr, "地州", city, typeof(string));

            object btsName = GetExcelValue(eBook, 1, "e3");
            SetDataRow(dr, "基站名称", btsName, typeof(string));

            object btsType = GetExcelValue(eBook, 1, "z7");
            SetDataRow(dr, "基站类型", btsType, typeof(string));

            object eNodeBID = GetExcelValue(eBook, 1, "e5");
            SetDataRow(dr, "ENodeBID", eNodeBID, typeof(int));

            object isPass = GetExcelValue(eBook, 1, "h56");
            SetDataRow(dr, "验证通过", isPass == null || isPass.ToString() != "是" ? "否" : "是", typeof(string));
        }
    }

    class SiteTranslatorInnerHandover : SiteTranslatorBase
    {
        public override void Fill(Excel.Workbook eBook, DataRow dr, int cellCount)
        {
            int rowIndex = cellCount <= 3 ? 52 : 99;
            object handoverRequest = GetExcelValue(eBook, 2, "p" + rowIndex);
            SetDataRow(dr, "系统内切换尝试次数", handoverRequest, typeof(int));

            object handoverSuccess = GetExcelValue(eBook, 2, "w" + rowIndex);
            SetDataRow(dr, "系统内切换成功次数", handoverSuccess, typeof(int));
        }
    }

    abstract class CellTranslatorBase : TranslatorBase
    {
        public abstract void Fill(Excel.Workbook eBook, DataRow[] drs);
    }

    class CellTranslatorInfo : CellTranslatorBase
    {
        public override void Fill(Excel.Workbook eBook, DataRow[] drs)
        {
            string sectorField = "sectorid";
            string passField = "验证通过";
            string yes = "是";
            double pct = 1;
            for (int i = 0; i < drs.Length; ++i)
            {
                SetDataRow(drs[i], sectorField, i + 1, typeof(int));

                int col1 = 12 + (5 * i);
                int row2 = 8 + (16 * i);
                SetDataRow(drs[i], passField,
                    IsPassValue(eBook, 1, col1, 31, yes)
                    && IsPassValue(eBook, 1, col1, 32, yes)
                    && IsPassValue(eBook, 1, col1, 33, yes)
                    && IsPassValue(eBook, 1, col1, 34, yes)
                    && IsPassValue(eBook, 1, col1, 35, yes)
                    && IsPassValue(eBook, 2, string.Format("ai{0}", row2), pct)
                    && IsPassValue(eBook, 2, string.Format("ai{0}", row2 + 1), pct)
                    && IsPassValue(eBook, 2, string.Format("ai{0}", row2 + 3), pct)
                    ? "是" : "否", typeof(string));
            }
        }

        private bool IsPassValue(Excel.Workbook eBook, int sheetIndex, int colIndex, int rowIndex, string passValue)
        {
            object excelValue = GetExcelValue(eBook, sheetIndex, colIndex, rowIndex);
            return excelValue != null && excelValue.ToString() == passValue;
        }

        private bool IsPassValue(Excel.Workbook eBook, int sheetIndex, string cell, double passValue)
        {
            object excelValue = GetExcelValue(eBook, sheetIndex, cell);
            double doubleValue = double.NaN;
            return excelValue != null && double.TryParse(excelValue.ToString(), out doubleValue) && doubleValue == passValue;
        }
    }
    class CellTranslatorCountKPI : CellTranslatorBase
    {
        readonly List<string> kpiNames = new List<string> { "RRC连接", "ERAB连接", "Access", "34G", "被叫CSFB" };
        public override void Fill(Excel.Workbook eBook, DataRow[] drs)
        {
            int sheetIndex = 2;
            int rowIndexFirst = -1;

            foreach (string strKpiName in kpiNames)
            {
                switch (strKpiName)
                {
                    case "RRC连接":
                        rowIndexFirst = 5;
                        break;
                    case "ERAB连接":
                        rowIndexFirst = 6;
                        break;
                    case "Access":
                        rowIndexFirst = 7;
                        break;
                    case "34G":
                        rowIndexFirst = 8;
                        break;
                    case "被叫CSFB":
                        rowIndexFirst = 11;
                        break;
                    default:
                        break;
                }

                if (rowIndexFirst > 0)
                {
                    fillKpiInfo(eBook, drs, sheetIndex, strKpiName, rowIndexFirst);
                }
            }
        }
        private void fillKpiInfo(Excel.Workbook eBook, DataRow[] drs, int sheetIndex
            , string strKpiName, int rowIndexFirst)
        {
            string requestField = strKpiName + "尝试次数";
            string successField = strKpiName + "成功次数";
            for (int i = 0; i < drs.Length; ++i)
            {
                int rowIndex = rowIndexFirst + (16 * i);
                SetDataRow(drs[i], requestField, GetExcelValue(eBook, sheetIndex, "p" + rowIndex), typeof(int));
                SetDataRow(drs[i], successField, GetExcelValue(eBook, sheetIndex, "w" + rowIndex), typeof(int));
            }
        }
    }
    class CellTranslatorDataKPI : CellTranslatorBase
    {
        readonly List<string> kpiNames = new List<string> { "下载RSRP", "下载SINR", "下载速率", "上传RSRP", "上传SINR", "上传速率" };
        public override void Fill(Excel.Workbook eBook, DataRow[] drs)
        {
            int sheetIndex = 2;
            int rowIndexFirst = -1;

            foreach (string strKpiName in kpiNames)
            {
                switch (strKpiName)
                {
                    case "下载RSRP":
                        rowIndexFirst = 13;
                        break;
                    case "下载SINR":
                        rowIndexFirst = 14;
                        break;
                    case "下载速率":
                        rowIndexFirst = 15;
                        break;
                    case "上传RSRP":
                        rowIndexFirst = 16;
                        break;
                    case "上传SINR":
                        rowIndexFirst = 17;
                        break;
                    case "上传速率":
                        rowIndexFirst = 18;
                        break;
                    default:
                        break;
                }

                if (rowIndexFirst > 0)
                {
                    fillKpiInfo(eBook, drs, sheetIndex, strKpiName, rowIndexFirst);
                }
            }
        }
        private void fillKpiInfo(Excel.Workbook eBook, DataRow[] drs, int sheetIndex
            , string strKpiName, int rowIndexFirst)
        {
            string goodField = strKpiName + "好点";
            string tallField = strKpiName + "中点";
            string badField = strKpiName + "差点";
            for (int i = 0; i < drs.Length; ++i)
            {
                int rowIndex = rowIndexFirst + (16 * i);
                SetDataRow(drs[i], goodField, GetExcelValue(eBook, sheetIndex, "p" + rowIndex), typeof(double));
                SetDataRow(drs[i], tallField, GetExcelValue(eBook, sheetIndex, "w" + rowIndex), typeof(double));
                SetDataRow(drs[i], badField, GetExcelValue(eBook, sheetIndex, "ac" + rowIndex), typeof(double));
            }
        }
    }

    class DiySqlSite : DIYSQLBase
    {
        public DiySqlSite(DataRow dr) : base(MainModel.GetInstance())
        {
            this.dr = dr;
            MainDB = true;
        }

        public override string Name
        {
            get { return "Upload Site"; }
        }

        protected override string getSqlTextString()
        {
            return UploadTable.SiteInsertSql(this.dr);
        }
        
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private readonly DataRow dr;
    }

    class DiySqlCell : DIYSQLBase
    {
        public DiySqlCell(DataRow dr)
            : base(MainModel.GetInstance())
        {
            this.dr = dr;
            MainDB = true;
        }

        public override string Name
        {
            get { return "Upload Cells"; }
        }

        protected override string getSqlTextString()
        {
            return UploadTable.CellInsertSql(dr);
        }
        
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private readonly DataRow dr;
    }
}
