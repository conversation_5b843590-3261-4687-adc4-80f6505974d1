﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDLTENBAnaSetForm : BaseDialog
    {
        public TDLTENBAnaSetForm()
        {
            InitializeComponent();
        }

        public void GetCondition(out TDLTENBAnaCondition condition)
        {
            condition = new TDLTENBAnaCondition();

            condition.DistanceMin = (int)numDistanceMin.Value;
            condition.DistanceMax = (int)numDistanceMax.Value;
            condition.AngleMin = (int)numAngleMin.Value;
            condition.AngleMax = (int)numAngleMax.Value; 
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if ((int)numDistanceMin.Value > (int)numDistanceMax.Value)
            {
                MessageBox.Show("距离下限超过上限，请核查！");
                DialogResult = DialogResult.Retry;
                return;
            }

            if ((int)numAngleMin.Value > (int)numAngleMax.Value)
            {
                MessageBox.Show("夹角下限超过上限，请核查！");
                DialogResult = DialogResult.Retry;
                return;
            }

            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
     }
}
