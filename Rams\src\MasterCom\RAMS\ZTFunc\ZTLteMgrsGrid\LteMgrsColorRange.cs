﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.MControls;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsColorRange
    {
        public float Minimum { get; set; } = -1000;
        public float Maximum { get; set; } = 1000;
        public Color InvalidColor { get; set; } = Color.Black;
        public string InvalidDesc { get; set; } = "无效栅格";
        public List<ColorRange> ColorRanges { get; set; } = new List<ColorRange>();

        public LteMgrsColorRange()
        {
        }

        public LteMgrsColorRange(string rangeType)
        {
            if (rangeType == "Rsrp")
            {
                ColorRanges.Add(new ColorRange(Minimum, -113, Color.Red, "x < -113"));
                ColorRanges.Add(new ColorRange(-113, -110, Color.DarkOrange, "-113 <= x < -110"));
                ColorRanges.Add(new ColorRange(-110, -105, Color.DarkGoldenrod, "-110 <= x < -105"));
                ColorRanges.Add(new ColorRange(-105, -100, Color.Gold, "-105 <= x < -100"));
                ColorRanges.Add(new ColorRange(-100, -95, Color.GreenYellow, "-100 <= x < -95"));
                ColorRanges.Add(new ColorRange(-95, -90, Color.LightGreen, "-95 <= x < -90"));
                ColorRanges.Add(new ColorRange(-90, -85, Color.LawnGreen, "-90 <= x < -85"));
                ColorRanges.Add(new ColorRange(-85, -80, Color.SeaGreen, "-85 <= x < -80"));
                ColorRanges.Add(new ColorRange(-80, -75, Color.ForestGreen, "-80 <= x < -75"));
                ColorRanges.Add(new ColorRange(-75, -70, Color.DarkSlateBlue, "-75 <= x < -70"));
                ColorRanges.Add(new ColorRange(-70, Maximum, Color.Blue, "70 <= x"));
            }
            else if (rangeType == "Coverage")
            {
                ColorRanges.Add(new ColorRange(Minimum, 1, Color.Red, "x < 1"));
                ColorRanges.Add(new ColorRange(1, 2, Color.Red, "1 <= x < 2"));
                ColorRanges.Add(new ColorRange(2, 3, Color.Red, "2 <= x < 3"));
                ColorRanges.Add(new ColorRange(3, 4, Color.Red, "3 <= x < 4"));
                ColorRanges.Add(new ColorRange(4, 5, Color.Red, "4 <= x < 5"));
                ColorRanges.Add(new ColorRange(5, 6, Color.Red, "5 <= x < 6"));
                ColorRanges.Add(new ColorRange(6, 7, Color.Red, "6 <= x < 7"));
                ColorRanges.Add(new ColorRange(7, 8, Color.Red, "7 <= x < 8"));
                ColorRanges.Add(new ColorRange(8, 9, Color.Red, "8 <= x < 9"));
                ColorRanges.Add(new ColorRange(9, 10, Color.Red, "9 <= x < 10"));
                ColorRanges.Add(new ColorRange(10, Maximum, Color.Red, "10 <= x"));
            }
        }

        /// <summary>
        /// 还原默认图例
        /// </summary>
        public void SetDefault(string rangeType)
        {
            ColorRanges.Clear();
            if (rangeType == "Rsrp")
            {
                ColorRanges.Add(new ColorRange(Minimum, -120, Color.Red, "(-∞,-120)"));
                ColorRanges.Add(new ColorRange(-120, -105, Color.OrangeRed, "[-120,-105)"));
                ColorRanges.Add(new ColorRange(-105, -100, Color.Tomato, "[-105,-100)"));
                ColorRanges.Add(new ColorRange(-100, -95, Color.Orange, "[-100,-95)"));
                ColorRanges.Add(new ColorRange(-95, -90, Color.Yellow, "[-95,-90)"));
                ColorRanges.Add(new ColorRange(-90, -85, Color.GreenYellow, "[-90,-85)"));
                ColorRanges.Add(new ColorRange(-85, -80, Color.LightGreen, "[-85,-80)"));
                ColorRanges.Add(new ColorRange(-80, -75, Color.LawnGreen, "[-80,-75)"));
                ColorRanges.Add(new ColorRange(-75, -70, Color.LimeGreen, "[-75,-70)"));
                ColorRanges.Add(new ColorRange(-70, Maximum, Color.Blue, "[-70,+∞)"));
            }
            else if (rangeType == "Coverage")
            {
                ColorRanges.Add(new ColorRange(Minimum, 1, Color.Blue, "（-∞,1)"));
                ColorRanges.Add(new ColorRange(1, 2, Color.DarkSlateBlue, "[1,2)"));
                ColorRanges.Add(new ColorRange(2, 3, Color.DarkGreen, "[2,3)"));
                ColorRanges.Add(new ColorRange(3, 4, Color.MediumSeaGreen, "[3,4"));
                ColorRanges.Add(new ColorRange(4, 5, Color.Lime, "[4,5)"));
                ColorRanges.Add(new ColorRange(5, 6, Color.LawnGreen, "[5,6)"));
                ColorRanges.Add(new ColorRange(6, 7, Color.GreenYellow, "[6,7)"));
                ColorRanges.Add(new ColorRange(7, 8, Color.Yellow, "[7,8)"));
                ColorRanges.Add(new ColorRange(8, 9, Color.Orange, "[8,9)"));
                ColorRanges.Add(new ColorRange(9, 10, Color.DarkOrange, "[9,10)"));
                ColorRanges.Add(new ColorRange(10, Maximum, Color.Red, "[10,+∞)"));
            }
        }

        public virtual int GetIndex(float value)
        {
            if (value < ColorRanges[0].maxValue)
            {
                return 0;
            }
            if (value >= ColorRanges[ColorRanges.Count - 1].minValue)
            {
                return ColorRanges.Count - 1;
            }
            for (int i = 1; i < ColorRanges.Count - 1; ++i)
            {
                if (value >= ColorRanges[i].minValue && value < ColorRanges[i].maxValue)
                {
                    return i;
                }
            }

            // ColorRanges.Count < 3 go here, let error throw
            return -1;
        }

        public virtual Color GetColor(float value)
        {
            int idx = GetIndex(value);
            return idx < 0 ? InvalidColor : ColorRanges[idx].color;
        }

        public virtual string GetDesc(float value)
        {
            int idx = GetIndex(value);
            return idx < 0 ? InvalidDesc : ColorRanges[idx].desInfo;
        }

        public virtual LteMgrsLegendGroup GetLegend()
        {
            return null;
        }

        public void SaveColorRange(string s)
        {
            XmlConfigFile configFile = new XmlConfigFile(LteMgrsBaseSettingManager.Instance.ConfigPath);
            XmlElement cfgColorRange = configFile.GetConfig(string.Format("{0}ColorRange", s));
            if (cfgColorRange != null)
            {
                cfgColorRange.RemoveAll();
                cfgColorRange.SetAttribute("name", string.Format("{0}ColorRange", s));
            }
            else
            {
                cfgColorRange = configFile.AddConfig(string.Format("{0}ColorRange", s));
            }

            foreach (ColorRange range in this.ColorRanges)
            {
                configFile.AddItem(cfgColorRange, range.desInfo,
                    string.Format("{0},{1},{2}", range.minValue, range.maxValue, range.color.ToArgb()));
            }
            configFile.Save(LteMgrsBaseSettingManager.Instance.ConfigPath);
        }
    }

    public class LteMgrsRsrpColorRange : LteMgrsColorRange
    {
        public LteMgrsRsrpColorRange()
        {
            loadConfig();

            if (ColorRanges.Count == 0)
            {
                ColorRanges.Add(new ColorRange(Minimum, -120, Color.Red, "(-∞,-120)"));
                ColorRanges.Add(new ColorRange(-120, -105, Color.OrangeRed, "[-120,-105)"));
                ColorRanges.Add(new ColorRange(-105, -100, Color.Tomato, "[-105,-100)"));
                ColorRanges.Add(new ColorRange(-100, -95, Color.Orange, "[-100,-95)"));
                ColorRanges.Add(new ColorRange(-95, -90, Color.Yellow, "[-95,-90)"));
                ColorRanges.Add(new ColorRange(-90, -85, Color.GreenYellow, "[-90,-85)"));
                ColorRanges.Add(new ColorRange(-85, -80, Color.LightGreen, "[-85,-80)"));
                ColorRanges.Add(new ColorRange(-80, -75, Color.LawnGreen, "[-80,-75)"));
                ColorRanges.Add(new ColorRange(-75, -70, Color.LimeGreen, "[-75,-70)"));
                ColorRanges.Add(new ColorRange(-70, Maximum, Color.Blue, "[-70,+∞)"));
            }

            legendGroup = new LteMgrsLegendGroup("扫频栅格场强图例");
            foreach (ColorRange c in ColorRanges)
            {
                LteMgrsLegendItem legendItem = new LteMgrsLegendItem(c.color, c.desInfo);
                legendGroup.SubItems.Add(legendItem);
            }
            legendGroup.SubItems.Add(new LteMgrsLegendItem(InvalidColor, InvalidDesc));
        }

        private void loadConfig()
        {
            XmlConfigFile configFile = new MyXmlConfigFile(LteMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                XmlElement cfgRsrpColorRange = configFile.GetConfig("RsrpColorRange");
                if (cfgRsrpColorRange != null)
                {
                    foreach (XmlNode node in cfgRsrpColorRange.ChildNodes)
                    {
                        addColorRanges(node);
                    }
                }
            }
        }

        private void addColorRanges(XmlNode node)
        {
            if (node.Attributes.Count != 0)
            {
                string des = node.Attributes["name"].InnerText;
                string text = node.InnerText;
                string[] s = text.Split(',');
                float min, max;
                int color;
                if (s.Length == 3 && float.TryParse(s[0], out min) && float.TryParse(s[1], out max)
                    && int.TryParse(s[2], out color))
                {
                    ColorRanges.Add(new ColorRange(min, max, Color.FromArgb(color), des));
                }
            }
        }

        public override LteMgrsLegendGroup GetLegend()
        {
            legendGroup.SubItems.Clear();
            foreach (ColorRange c in ColorRanges)
            {
                LteMgrsLegendItem legendItem = new LteMgrsLegendItem(c.color, c.desInfo);
                legendGroup.SubItems.Add(legendItem);
            }
            legendGroup.SubItems.Add(new LteMgrsLegendItem(InvalidColor, InvalidDesc));
            return legendGroup;
        }

        private readonly LteMgrsLegendGroup legendGroup;
    }

    public class LteMgrsCoverageColorRange : LteMgrsColorRange
    {
        public LteMgrsCoverageColorRange()
        {
            loadConfig();

            if (ColorRanges.Count == 0)
            {
                ColorRanges.Add(new ColorRange(Minimum, 1, Color.Blue, "（-∞,1)"));
                ColorRanges.Add(new ColorRange(1, 2, Color.DarkSlateBlue, "[1,2)"));
                ColorRanges.Add(new ColorRange(2, 3, Color.DarkGreen, "[2,3)"));
                ColorRanges.Add(new ColorRange(3, 4, Color.MediumSeaGreen, "[3,4"));
                ColorRanges.Add(new ColorRange(4, 5, Color.Lime, "[4,5)"));
                ColorRanges.Add(new ColorRange(5, 6, Color.LawnGreen, "[5,6)"));
                ColorRanges.Add(new ColorRange(6, 7, Color.GreenYellow, "[6,7)"));
                ColorRanges.Add(new ColorRange(7, 8, Color.Yellow, "[7,8)"));
                ColorRanges.Add(new ColorRange(8, 9, Color.Orange, "[8,9)"));
                ColorRanges.Add(new ColorRange(9, 10, Color.DarkOrange, "[9,10)"));
                ColorRanges.Add(new ColorRange(10, Maximum, Color.Red, "[10,+∞)"));
            }

            legendGroup = new LteMgrsLegendGroup("扫频栅格重叠覆盖度图例");
            foreach (ColorRange c in ColorRanges)
            {
                LteMgrsLegendItem legendItem = new LteMgrsLegendItem(c.color, c.desInfo);
                legendGroup.SubItems.Add(legendItem);
            }
            legendGroup.SubItems.Add(new LteMgrsLegendItem(InvalidColor, InvalidDesc));
        }

        private void loadConfig()
        {
            XmlConfigFile configFile = new MyXmlConfigFile(LteMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                XmlElement cfgCoverageColorRange = configFile.GetConfig("CoverageColorRange");
                if (cfgCoverageColorRange != null)
                {
                    foreach (XmlNode node in cfgCoverageColorRange.ChildNodes)
                    {
                        addColorRanges(node);
                    }
                }
            }
        }

        private void addColorRanges(XmlNode node)
        {
            if (node.Attributes.Count != 0)
            {
                string des = node.Attributes["name"].InnerText;
                string text = node.InnerText;
                string[] s = text.Split(',');
                float min, max;
                int color;
                if (s.Length == 3 && float.TryParse(s[0], out min) && float.TryParse(s[1], out max)
                    && int.TryParse(s[2], out color))
                {
                    ColorRanges.Add(new ColorRange(min, max, Color.FromArgb(color), des));
                }
            }
        }

        public override LteMgrsLegendGroup GetLegend()
        {
            legendGroup.SubItems.Clear();
            foreach (ColorRange c in ColorRanges)
            {
                LteMgrsLegendItem legendItem = new LteMgrsLegendItem(c.color, c.desInfo);
                legendGroup.SubItems.Add(legendItem);
            }
            legendGroup.SubItems.Add(new LteMgrsLegendItem(InvalidColor, InvalidDesc));
            return legendGroup;
        }

        public override Color GetColor(float value)
        {
            int idx = GetIndex(value);
            return idx <= 0 ? InvalidColor : ColorRanges[idx].color;
        }

        private readonly LteMgrsLegendGroup legendGroup;
    }

    public class LteMgrsTestDepthColorRange : LteMgrsColorRange
    {
        public LteMgrsTestDepthColorRange()
        {
            ColorRanges.Add(new ColorRange(1, 2, Color.Blue, "本次测试新增"));
            ColorRanges.Add(new ColorRange(2, 3, Color.Lime, "以外测试本次测试"));
            ColorRanges.Add(new ColorRange(3, 4, Color.Red, "以外测试本次未测试"));

            legendGroup = new LteMgrsLegendGroup("扫频栅格测试深度图例");
            foreach (ColorRange c in ColorRanges)
            {
                LteMgrsLegendItem legendItem = new LteMgrsLegendItem(c.color, c.desInfo);
                legendGroup.SubItems.Add(legendItem);
            }
        }

        public override LteMgrsLegendGroup GetLegend()
        {
            return legendGroup;
        }

        private readonly LteMgrsLegendGroup legendGroup;
    }
}
