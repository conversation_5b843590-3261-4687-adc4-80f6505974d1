﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class OutOfService : CauseBase
    {
        public override string Name
        {
            get { return "脱网"; }
        }
        public override string Desc
        {
            get
            {
                return "无信号强度记录";
            }
        }

        public override string Suggestion
        {
            get
            {
                return "检查周围是否有遮挡";
            }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                float? rsrp = (float?)GetRSRP(pnt);
                if (rsrp == null || rsrp < -141 || rsrp > 25)
                {
                    OutOfService cln = this.Clone() as OutOfService;
                    segItem.SetReason(new LowSpeedPointDetail(pnt, cln));
                }
            }
        }
        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    //
                }
            }
        }
    }

}
