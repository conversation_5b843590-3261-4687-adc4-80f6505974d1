﻿using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.ExMap;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace MasterCom.Util
{
    public class ScreenMapCapture
    {
        private static ScreenMapCapture instance = null;
        public static ScreenMapCapture Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ScreenMapCapture();
                }
                return instance;
            }
        }

        private ScreenMapCapture() { }

        public void OutputHighResolutionMap(MapOperation m_mapOperation, float mapScale
            , bool printIt = false, bool willShowFileDlg = true, string strFilePath = "")
        {
            DbPoint centerPoint = null;
            double oldScale = 0;
            centerPoint = m_mapOperation.GetCenter();
            oldScale = m_mapOperation.Scale;
            WaitTextBox.Show("正在截图...", getHighResolutionMapWinGis
                , new object[] { mapScale, willShowFileDlg, m_mapOperation });

            savaeImg(printIt, willShowFileDlg, strFilePath);
            m_mapOperation.GoToView(centerPoint.x, centerPoint.y, (float)oldScale);
        }

        public void OutputHighResolutionMap(ExMapFromPanel exMapPanel, float mapScale
            , bool printIt = false, bool willShowFileDlg = true, string strFilePath = "")
        {
            DbPoint centerPoint = null;
            double oldScale = 0;
            DbRect bounds = exMapPanel.MapBounds;
            centerPoint = bounds.Center();
            oldScale = exMapPanel.getMainMap().Zoom;
            WaitBoxTopLeftIcon.Show("正在截图...", getHighResolutionMapGoogle
                , new object[] { mapScale, exMapPanel });
            savaeImg(printIt, willShowFileDlg, strFilePath);
            exMapPanel.GoToView(centerPoint.x, centerPoint.y, (float)oldScale);
        }

        #region MapWinGis
        private void getHighResolutionMapWinGis(object obj)
        {
            var mainModel = MainModel.GetInstance();
            var mapForm = mainModel.MainForm.GetMapForm();
            var mapControl = mapForm.GetMapFormControl();
            var dtLayer = mapForm.GetDTLayer();

            highResolutionMapBitmap = null;
            object[] paramsObj = obj as object[];
            float mapScale = (float)paramsObj[0];
            bool willShowTips = (bool)paramsObj[1];
            MapOperation m_mapOperation = (MapOperation)paramsObj[2];
            DbRect rectTotal = m_mapOperation.GetBounds();
            if (mainModel.SearchGeometrys.Region != null)
            {
                rectTotal = mainModel.SearchGeometrys.RegionBounds;
            }
            DbPoint centerPoint = m_mapOperation.GetCenter();
            try
            {
                m_mapOperation.GoToView(centerPoint.x, centerPoint.y, mapScale);
                DbRect rect = m_mapOperation.GetBounds();
                CaptureRegion captureRegion = new CaptureRegion();
                captureRegion.widthUnit = mapControl.ClientRectangle.Width;
                captureRegion.heighUnit = mapControl.ClientRectangle.Height;
                getRegionInfo(rectTotal, rect, captureRegion);
                double mbSize = (captureRegion.xResolution * 1.0000 / 1024) * (captureRegion.yResolution * 1.0000 / 1024);

                bool toLarge = false;
                try
                {
                    highResolutionMapBitmap = new Bitmap(captureRegion.xResolution
                        , captureRegion.yResolution, PixelFormat.Format16bppRgb555);
                }
                catch
                {
                    toLarge = true;
                }

                if (willShowTips)
                {
                    string msg = "";
                    if (toLarge)
                    {
                        msg = $"截图分辨率{mbSize.ToString("0")}M，已超过限制，" +
                           $"请调大截图比例或者调小地图显示比例！";
                        MessageBox.Show(msg, "提示");
                        return;
                    }
                    msg = $"高清截图的分辨率是{captureRegion.xResolution}*{captureRegion.yResolution}" +
                        $",是否继续？";
                    if (MessageBox.Show(msg, "提示", MessageBoxButtons.YesNo) == DialogResult.No)
                    {
                        return;
                    }
                }

                dealMapWinGisImg(mapForm, mapControl, dtLayer, m_mapOperation, captureRegion);
            }
            catch (Exception ex)
            {
                if (willShowTips)
                {
                    MessageBox.Show($"截图文件生成失败！{ex.Message}");
                }
            }
            finally
            {
                dtLayer.DrawScaleBar = true;
                mapControl.ShowRedrawTime = true;
                Thread.Sleep(20);
                WaitTextBox.Close();
            }
        }

        private void dealMapWinGisImg(MapForm mapForm, AxMapWinGIS.AxMap mapControl, MapDTLayer dtLayer, MapOperation m_mapOperation, CaptureRegion captureRegion)
        {
            Graphics g = Graphics.FromImage(highResolutionMapBitmap);
            mapControl.ShowRedrawTime = false;
            int xCount = captureRegion.xCenters.Count;
            int yCount = captureRegion.yCenters.Count;
            for (int j = 0; j < yCount; j++)
            {
                for (int i = 0; i < xCount; i++)
                {
                    dtLayer.DrawScaleBar = false;
                    if (j == yCount - 1 && i == 0)
                    {
                        dtLayer.DrawScaleBar = true;
                    }
                    double x = captureRegion.xCenters[i];
                    double y = captureRegion.yCenters[j];
                    mapForm.SetRedrawBuffFlag();
                    m_mapOperation.GoToView(x, y);
                    while (mapForm.GetRedrawBuffFlag())
                    {
                        Thread.Sleep(500);
                    }
                    MapWinGIS.Image imgTmp = mapControl.SnapShot(mapControl.Extents) as MapWinGIS.Image;
                    string name = Guid.NewGuid().ToString();
                    string fileName = Application.StartupPath + "/" + name;
                    imgTmp.Save(fileName, true, ImageType.BITMAP_FILE, null);
                    System.Drawing.Image img = System.Drawing.Image.FromFile(fileName);
                    g.DrawImage(img, i * captureRegion.widthUnit, j * captureRegion.heighUnit
                        , captureRegion.widthUnit, captureRegion.heighUnit);
                    try
                    {
                        img.Dispose();
                        System.IO.File.Delete(fileName);
                    }
                    catch
                    {
                        //continue
                    }
                }
            }
            g.Save();
            g.Dispose();
        }
        #endregion

        #region Google
        private void getHighResolutionMapGoogle(object obj)
        {
            var mainModel = MainModel.GetInstance();
            var mapForm = mainModel.MainForm.GetMapForm();
            var mapControl = mapForm.GetMapFormControl();
            var dtLayer = mapForm.GetDTLayer();

            highResolutionMapBitmap = null;
            object[] paramsObj = obj as object[];
            float mapScale = (float)paramsObj[0];
            ExMapFromPanel exMapPanel = (ExMapFromPanel)paramsObj[1];
            DbRect rectTotal = exMapPanel.MapBounds;
            if (mainModel.SearchGeometrys.Region != null)
            {
                rectTotal = mainModel.SearchGeometrys.RegionBounds;
            }
            DbPoint centerPoint = rectTotal.Center();
            try
            {
                exMapPanel.GoToView(centerPoint.x, centerPoint.y, mapScale);
                DbRect rect = exMapPanel.MapBounds;
                CaptureRegion captureRegion = new CaptureRegion();
                captureRegion.widthUnit = exMapPanel.ClientRectangle.Width;
                captureRegion.heighUnit = exMapPanel.ClientRectangle.Height;
                getRegionInfo(rectTotal, rect, captureRegion);
                int mbSize = (captureRegion.xResolution * captureRegion.yResolution / 1024) / 1024;

                bool toLarge = false;
                try
                {
                    highResolutionMapBitmap = new Bitmap(captureRegion.xResolution
                        , captureRegion.yResolution, PixelFormat.Format16bppRgb555);
                }
                catch
                {
                    toLarge = true;
                }

                string msg = "";
                if (toLarge)
                {
                    msg = $"截图分辨率{mbSize}M，已超过限制，请调大截图比例或者调小地图显示比例！";
                    MessageBox.Show(msg, "提示");
                    return;
                }
                msg = $"高清截图的分辨率是{captureRegion.xResolution}*{captureRegion.yResolution}" +
                    $",是否继续？";
                if (MessageBox.Show(msg, "提示", MessageBoxButtons.YesNo) == DialogResult.No)
                {
                    return;
                }

                float delayDefaultSecond = 3;
                var box = new NumberInputBox("截屏设置", "设置每屏抓图的等待时延（秒）", 3, 100, 1, 1);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    delayDefaultSecond = (float)box.Value;
                }

                dealGoogleMapImg(mapForm, dtLayer, mapScale, exMapPanel, captureRegion, delayDefaultSecond);
            }
            catch(Exception ex)
            {
                MessageBox.Show($"截图文件生成失败！{ex.Message}");
            }
            finally
            {
                dtLayer.DrawScaleBar = true;
                mapControl.ShowRedrawTime = true;
                Thread.Sleep(20);
                WaitBoxTopLeftIcon.Close();
            }
        }

        private void dealGoogleMapImg(MapForm mapForm, MapDTLayer dtLayer, float mapScale, ExMapFromPanel exMapPanel, CaptureRegion captureRegion, float delayDefaultSecond)
        {
            Graphics g = Graphics.FromImage(highResolutionMapBitmap);
            int xCount = captureRegion.xCenters.Count;
            int yCount = captureRegion.yCenters.Count;
            int totalCountToCatch = xCount * yCount;
            int progAt = 0;
            for (int j = 0; j < yCount; j++)
            {
                for (int i = 0; i < xCount; i++)
                {
                    progAt++;
                    dtLayer.DrawScaleBar = false;
                    if (j == yCount - 1 && i == 0)
                    {
                        dtLayer.DrawScaleBar = true;
                    }
                    double x = captureRegion.xCenters[i];
                    double y = captureRegion.yCenters[j];
                    mapForm.SetRedrawBuffFlag();
                    //mapForm.SetRedrawBuffFlag();
                    mapForm.TileLoaded = false;
                    exMapPanel.GoToView(x, y, mapScale);
                    while ((!mapForm.TileLoaded) && exMapPanel.getMainMap().NeedReDrawBufferImage && (!exMapPanel.getMainMap().RenderAllFinished))
                    {
                        Thread.Sleep(500);
                    }
                    WaitBoxTopLeftIcon.Text = "抓图中" + 100 * progAt / totalCountToCatch + "%...";
                    Thread.Sleep((int)(delayDefaultSecond * 1000));
                    System.Drawing.Image imgTmp = exMapPanel.getMainMap().ToImage();
                    string name = Guid.NewGuid().ToString();
                    string fileName = Application.StartupPath + "/" + name;
                    imgTmp.Save(fileName, ImageFormat.Bmp);
                    System.Drawing.Image img = System.Drawing.Image.FromFile(fileName);
                    g.DrawImage(img, i * captureRegion.widthUnit, j * captureRegion.heighUnit
                        , captureRegion.widthUnit, captureRegion.heighUnit);
                    try
                    {
                        img.Dispose();
                        System.IO.File.Delete(fileName);
                    }
                    catch
                    {
                        //continue
                    }
                }
            }
            g.Save();
            g.Dispose();
        }
        #endregion

        Bitmap highResolutionMapBitmap = null;
        private void savaeImg(bool printIt, bool willShowFileDlg, string strFilePath)
        {
            if (highResolutionMapBitmap != null)
            {
                if (willShowFileDlg)
                {
                    if (printIt)
                    {
                        PrintHDCutDlg dlg = new PrintHDCutDlg();
                        dlg.SetToPrintImage(highResolutionMapBitmap);
                        dlg.ShowDialog();
                    }
                    else
                    {
                        Clipboard.SetImage(highResolutionMapBitmap);
                        SaveFileDialog dialog = new SaveFileDialog();
                        dialog.RestoreDirectory = true;
                        dialog.Filter = "png文件(*.png)|*.png";
                        dialog.Title = "导出高清截图到png文件";
                        if (dialog.ShowDialog() == DialogResult.OK)
                        {
                            highResolutionMapBitmap.Save(dialog.FileName, ImageFormat.Png);
                            MessageBox.Show("截图文件保存成功！");
                        }
                    }
                }
                else
                {
                    highResolutionMapBitmap.Save(strFilePath, ImageFormat.Png);
                }
            }
            if (highResolutionMapBitmap != null)
            {
                highResolutionMapBitmap.Dispose();
                highResolutionMapBitmap = null;
            }
        }

        private static void getRegionInfo(DbRect rectTotal, DbRect rect, CaptureRegion captureRegion)
        {
            double xMin = rectTotal.x1;
            double xMax = rectTotal.x2;
            double yMin = rectTotal.y1;
            double yMax = rectTotal.y2;
            double xUnit = rect.x2 - rect.x1;
            double yUnit = rect.y2 - rect.y1;
            captureRegion.xCenters = new List<double>();
            captureRegion.yCenters = new List<double>();
            double xCenter = xMin + xUnit / 2;
            double yCenter = yMax - yUnit / 2;
            captureRegion.xCenters.Add(xCenter);
            captureRegion.yCenters.Add(yCenter);
            while (xCenter + xUnit / 2 < xMax)
            {
                xCenter += xUnit;
                captureRegion.xCenters.Add(xCenter);
            }
            while (yCenter - yUnit / 2 > yMin)
            {
                yCenter -= yUnit;
                captureRegion.yCenters.Add(yCenter);
            }
            captureRegion.xResolution = captureRegion.xCenters.Count * captureRegion.widthUnit;
            captureRegion.yResolution = captureRegion.yCenters.Count * captureRegion.heighUnit;
        }

        class CaptureRegion
        {
            public List<double> xCenters { get; set; }
            public List<double> yCenters { get; set; }
            public int xResolution { get; set; }
            public int yResolution { get; set; }

            public int widthUnit { get; set; }
            public int heighUnit { get; set; }
        }
    }
}
