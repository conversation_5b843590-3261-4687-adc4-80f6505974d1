﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    public class OrderGridItem:GridUnitBase
    {
        public int SetID { get; set; }

        public int CityID { get; set; }

        public int ItemID { get; set; }

        public int SetTokenID { get; set; }

        public string GridSN { get; set; }

        public GridOrder Order
        {
            get;
            set;
        }

        public List<OrderCellItem> Cells { get; set; }

        public void AddCell(OrderCellItem cell)
        {
            if (Cells==null)
            {
                Cells = new List<OrderCellItem>();
            }
            cell.Grid = this;
            Cells.Add(cell);
        }

        public string OrderKey
        {
            get
            {
                return string.Format("{0}-{1}-{2}", CityID, SetTokenID, SetID);
            }
        }

        public Dictionary<string, double> KPIDic { get; set; } = new Dictionary<string, double>();

    }
}
