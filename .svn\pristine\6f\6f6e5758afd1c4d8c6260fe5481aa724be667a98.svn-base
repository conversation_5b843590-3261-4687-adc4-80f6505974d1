﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using System.Data;
using System.Data.SqlClient;
using System.Data.OleDb;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.Util.UiEx;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ImportCellArgumentFunc : QueryBase
    {
        public ImportCellArgumentFunc(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        public override string Name
        {
            get { return "导入小区关联信息"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 25000, 25003, this.Name);
        }

        protected override bool isValidCondition()
        {
            ImportCellArgumentForm form = new ImportCellArgumentForm();
            if (form.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            this.cond = form.GetCondition();
            form.Dispose();
            return true;
        }

        protected override void query()
        {
            this.error = null;
            CellArgumentImporter importer = new CellArgumentImporter();
            WaitTextBox.Show("正在导入小区关系信息...", ImportInThread, importer);

            if (this.error != null)
            {
                MessageBox.Show(error.Message + Environment.NewLine + error.StackTrace, "导入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string title = string.Format("成功导入{0}记录，", importer.ImportedCount);
            if (importer.ErrorRows.Count == 0)
            {
                title += "未发生错误。";
            }
            else
            {
                title += string.Format("数据文件存在以下{0}行错误:", importer.ErrorRows.Count);
            }
            ImportCellArgumentResultForm resultForm = new ImportCellArgumentResultForm(title, importer.ErrorRows);
            resultForm.ShowDialog();
            resultForm.Dispose();
            this.error = null;

            CellArgumentManager.Instance.FireLoadCellArguments(MainModel.DistrictID, true);
        }

        private void ImportInThread(object o)
        {
            CellArgumentImporter importer = o as CellArgumentImporter;
            try
            {
                importer.Import(CellArgumentManager.Instance.SqlConnectionString, this.cond);
            }
            catch (System.Exception ex)
            {
                this.error = ex;
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        private ImportCellArgumentCondition cond;
        private Exception error;
    }

    public class ImportCellArgumentCondition
    {
        public int CityID { get; set; }

        public bool IsClear { get; set; }

        public string FilePath { get; set; }

        public int CellStartIndex { get; set; }
    }

    public class CellArgumentImporter
    {
        public CellArgumentImporter()
        {
            ErrorRows = new List<string>();
        }

        public List<string> ErrorRows
        {
            get;
            private set;
        }

        public int ImportedCount
        {
            get;
            private set;
        }

        public void Import(string sqlConnStr, ImportCellArgumentCondition cond)
        {
            ErrorRows.Clear();

            DataTable excelTable = null;
            if (string.Equals(System.IO.Path.GetExtension(cond.FilePath), ".csv", StringComparison.CurrentCultureIgnoreCase))
            {
                excelTable = CsvFileReader.ReadTable(cond.FilePath);
            }
            else
            {
                List<DataTable> tables = ExcelOleDbReader.ReadTables(cond.FilePath);
                excelTable = tables[0];
            }
            DataTable importTable = TransferTable(excelTable, cond.CityID, cond.CellStartIndex);

            using (SqlConnection sqlConn = new SqlConnection(sqlConnStr))
            {
                sqlConn.Open();
                if (cond.IsClear)
                {
                    string sql = string.Format("delete from {0} where [地市ID] = {1}", tableName, cond.CityID);
                    using (SqlCommand cmd = new SqlCommand(sql, sqlConn))
                    {
                        cmd.ExecuteNonQuery();
                    }
                }
                SqlBulkCopy bcp = new SqlBulkCopy(sqlConn);
                bcp.DestinationTableName = importTable.TableName;
                bcp.WriteToServer(importTable);
                bcp.Close();

                ImportedCount = importTable.Rows.Count;
            }
        }

        private DataTable CreateImportTable()
        {
            DataTable importTable = new DataTable(tableName);
            importTable.Columns.Add("地市ID", typeof(int));
            importTable.Columns.Add("eNodeB标识", typeof(string));
            importTable.Columns.Add("eNodeB名称", typeof(string));
            importTable.Columns.Add("小区标识", typeof(string));
            importTable.Columns.Add("小区名称", typeof(string));
            importTable.Columns.Add("频点", typeof(int));
            importTable.Columns.Add("物理小区识别码", typeof(int));
            importTable.Columns.Add("参考信号功率", typeof(float));
            importTable.Columns.Add("PA", typeof(float));
            importTable.Columns.Add("PB", typeof(float));
            importTable.Columns.Add("小区偏移量", typeof(int));
            importTable.Columns.Add("小区偏置", typeof(int));
            importTable.Columns.Add("邻区列表", typeof(string));
            return importTable;
        }

        private void ReportErrorRow(int rowIndex, string column, string errMsg)
        {
            if (errMsg == null)
            {
                errMsg = "Invalid";
            }
            ErrorRows.Add(string.Format("Row {0}: [{1}] {2}", rowIndex + 2, column, errMsg));
        }

        private DataTable TransferTable(DataTable excelTable, int cityID, int cellStartIndex)
        {
            Dictionary<string, CellArgumentRecord> recordDic = new Dictionary<string, CellArgumentRecord>();
            for (int i = 0; i < excelTable.Rows.Count; ++i)
            {
                DataRow excelRow = excelTable.Rows[i];
                CellArgumentRecord record = new CellArgumentRecord();

                #region read row value
                record.CityID = cityID;

                string eNodeBID;
                if (!DataRowReader.GetString(excelRow, "eNodeB网络标识符", out eNodeBID) || eNodeBID.Trim() == "")
                {
                    ReportErrorRow(i, "eNodeB网络标识符", null);
                    continue;
                }
                record.ENodeBID = eNodeBID.Trim();

                string eNodeBName;
                if (!DataRowReader.GetString(excelRow, "eNodeB名称", out eNodeBName) || eNodeBName.Trim() == "")
                {
                    ReportErrorRow(i, "eNodeB名称", null);
                    continue;
                }
                record.ENodeBName = eNodeBName.Trim();

                int cellID;
                if (!DataRowReader.GetInt(excelRow, "小区标识", out cellID))
                {
                    ReportErrorRow(i, "小区标识", null);
                }
                cellID -= cellStartIndex;
                record.CellID = eNodeBID.Trim() + "-" + cellID;
                record.CellName = eNodeBName.Trim() + "-" + cellID;

                setEarfcn(excelRow, record);
                setPci(excelRow, record);
                setRefSignalPower(excelRow, record);
                setPa(excelRow, record);
                setPb(excelRow, record);
                setCellOcn(excelRow, record);
                setCellOcs(excelRow, record);
                addNbCell(excelRow, record);
                #endregion

                if (!recordDic.ContainsKey(record.CellID))
                {
                    recordDic.Add(record.CellID, record);
                }
                else
                {
                    recordDic[record.CellID].MergeRecord(record);
                }
            }

            DataTable importTable = dealRecordDic(recordDic);

            return importTable;
        }

        private void setEarfcn(DataRow excelRow, CellArgumentRecord record)
        {
            int earfcn;
            if (DataRowReader.GetInt(excelRow, "下行频点", out earfcn))
            {
                record.Earfcn = earfcn;
            }
        }

        private void setPci(DataRow excelRow, CellArgumentRecord record)
        {
            int pci;
            if (DataRowReader.GetInt(excelRow, "物理小区识别码", out pci))
            {
                record.Pci = pci;
            }
        }

        private void setRefSignalPower(DataRow excelRow, CellArgumentRecord record)
        {
            float refSignalPower;
            if (DataRowReader.GetFloat(excelRow, "参考信号功率", out refSignalPower))
            {
                record.RefSignalPower = refSignalPower;
            }
        }

        private void setPa(DataRow excelRow, CellArgumentRecord record)
        {
            string paString;
            if (DataRowReader.GetString(excelRow, "PDSCH采用均匀功率分配时的PA值", out paString))
            {
                int pa;
                if (paString.StartsWith("DB0")) record.Pa = 0;
                else if (paString.StartsWith("DB"))
                {
                    int firstPos, secondPos;
                    if ((firstPos = paString.IndexOf('_')) != -1 && (secondPos = paString.IndexOf('_', firstPos + 1)) != -1
                        && int.TryParse(paString.Substring(firstPos + 1, secondPos - firstPos - 1), out pa))
                    {
                        record.Pa = -1 * pa;
                    }
                }
                else if (int.TryParse(paString, out pa))
                {
                    record.Pa = pa;
                }
            }
        }

        private void setPb(DataRow excelRow, CellArgumentRecord record)
        {
            float pb;
            if (DataRowReader.GetFloat(excelRow, "PB", out pb))
            {
                record.Pb = pb;
            }
        }

        private void setCellOcn(DataRow excelRow, CellArgumentRecord record)
        {
            string cellOcnString;
            if (DataRowReader.GetString(excelRow, "小区偏移量", out cellOcnString))
            {
                if (cellOcnString.StartsWith("DB"))
                {
                    cellOcnString = cellOcnString.Remove(0, 2);
                }
                int cellOcn;
                if (int.TryParse(cellOcnString, out cellOcn))
                {
                    record.CellOcn = cellOcn;
                }
            }
        }

        private void setCellOcs(DataRow excelRow, CellArgumentRecord record)
        {
            string cellOcsString;
            if (DataRowReader.GetString(excelRow, "小区偏置", out cellOcsString))
            {
                if (cellOcsString.StartsWith("DB"))
                {
                    cellOcsString = cellOcsString.Remove(0, 2);
                }
                int cellOcs;
                if (int.TryParse(cellOcsString, out cellOcs))
                {
                    record.CellOcs = cellOcs;
                }
            }
        }

        private void addNbCell(DataRow excelRow, CellArgumentRecord record)
        {
            string nbCell;
            if (DataRowReader.GetString(excelRow, "目标小区标识", out nbCell) && !record.NbCells.Contains(nbCell.Trim()))
            {
                record.NbCells.Add(nbCell.Trim());
            }
        }

        private DataTable dealRecordDic(Dictionary<string, CellArgumentRecord> recordDic)
        {
            DataTable importTable = CreateImportTable();
            foreach (CellArgumentRecord record in recordDic.Values)
            {
                StringBuilder sb = new StringBuilder();
                foreach (string nbCell in record.NbCells)
                {
                    sb.Append(nbCell + ",");
                }

                object[] values = new object[] {
                    record.CityID, record.ENodeBID, record.ENodeBName, record.CellID, record.CellName,
                    record.Earfcn, record.Pci, record.RefSignalPower, record.Pa, record.Pb, record.CellOcn, record.CellOcs,
                    sb.Length == 0 ? null : sb.Remove(sb.Length - 1, 1).ToString(),
                };
                importTable.Rows.Add(values);
            }

            return importTable;
        }

        private readonly string tableName = "tb_association_argument";
    }
}
