﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GridCompareCombieSetForm : BaseDialog
    {
        public GridCompareCombieSetForm(bool isShowGridDataInfo, bool isInsertGridInfo, bool isMutGrid
            , List<string> gridTypeList)
        {
            InitializeComponent();
            if (isShowGridDataInfo)
            {
                chkShowDataInfo.Visible = true;
            }
            if (isInsertGridInfo)
            {
                 chkInsertDB.Visible = true;
                 chkAddInsert.Visible = true;
            }
            label5.Visible = isMutGrid;
            cbGridType.Visible = isMutGrid;
            cbGridType.Items.Clear();
            foreach (string strGrid in gridTypeList)
            {
                cbGridType.Items.Add(strGrid);
            }
            if (cbGridType.Items.Count > 0)
            {
                cbGridType.SelectedIndex = 0;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public double GetPercent
        {
            get
            {
                double dPercent;
                if (!double.TryParse(numPercent.Value.ToString(), out dPercent))
                {
                    dPercent = 100;
                }
                return dPercent;
            }
        }

        public double GetSpeed
        {
            get
            {
                double dSpeed;
                if (!double.TryParse(numSpeed.Value.ToString(), out dSpeed))
                {
                    dSpeed = 25;
                }
                return dSpeed;
            }
        }

        public bool GetIsShowGridDataInfo
        {
            get { return chkShowDataInfo.Checked; }
        }

        public bool GetIsInsertGridDataInfoToDB
        {
            get { return chkInsertDB.Checked; }
        }

        public bool GetIsAddInsertGridDataInfo
        {
            get { return chkAddInsert.Checked; }
        }

        public string GetGridType
        {
            get { return cbGridType.Items.Count > 0 ? cbGridType.SelectedItem.ToString() : ""; }
        }
    }
}
