﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System.Collections;
using Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.Model.Interface;
using MapWinGIS;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakQualAnaForm : MinCloseForm
    {
        MainModel mainModel;
        List<TableInfo> tableInfoList;
        List<TableInfoForExcel> tableInfoForExcelList;
        public WeakQualAnaForm()
            : base()
        {
            InitializeComponent();
            this.mainModel = MainModel;
        }

        public void FillData()
        {
            #region 原因分析概况
            Dictionary<string, Dictionary<string, ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo>> weakQualReasonInfo = MainModel.WeakQualReasonInfo;
            tableInfoList = new List<TableInfo>();
            List<ZTDIYWeakQualAnaByRegion.QualBurrInfo> tableQualBurr = new List<ZTDIYWeakQualAnaByRegion.QualBurrInfo>();
            tableInfoForExcelList = new List<TableInfoForExcel>();
            foreach (string key in weakQualReasonInfo.Keys)
            {
                TableInfoForExcel curTiExcel = tableInfoForExcelList.Find(
                    delegate (TableInfoForExcel tiExcel)
                    {
                        if (tiExcel.regionType == key)
                        {
                            return true;
                        }
                        else
                        {
                            return false;
                        }
                    });
                if (curTiExcel == null)
                {
                    curTiExcel = new TableInfoForExcel();
                    tableInfoForExcelList.Add(curTiExcel);
                }

                addTableQualBurr(weakQualReasonInfo, tableQualBurr, key, curTiExcel);
            }
            BindingSource bindingSource = new BindingSource();
            bindingSource.DataSource = tableInfoList;
            this.gridControl1.DataSource = bindingSource;
            this.BuildCellManage();
            this.gridControl1.RefreshDataSource();

            bindingSource = new BindingSource();
            bindingSource.DataSource = tableQualBurr;
            this.gridControlQualBurr.DataSource = bindingSource;
            this.BuildCellManage();
            this.gridControlQualBurr.RefreshDataSource();
            #endregion

            #region 问题详情
            #region 小区信息
            Dictionary<MasterCom.RAMS.Net.ZTDIYWeakQualAnaByRegion.CellSub, ZTDIYWeakQualAnaByRegion.WeakQualCellInfo> cellWeakQualCellInfoDic = MainModel.CellWeakQualCellInfoDic;
            List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo> CellInfoList = new List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo>();
            foreach (ZTDIYWeakQualAnaByRegion.WeakQualCellInfo cellInfo in cellWeakQualCellInfoDic.Values)
            {
                CellInfoList.Add(cellInfo);
            }

#if Guangdong
            this.gridBandNetworkModulus.Visible = true;
#else
            this.gridBandNetworkModulus.Visible = false;
#endif
            bindingSource = new BindingSource();
            bindingSource.DataSource = CellInfoList;
            this.gridControlDetail.DataSource = bindingSource;
            this.gridControlDetail.RefreshDataSource();
            #endregion
            #endregion
        }

        private void addTableQualBurr(Dictionary<string, Dictionary<string, ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo>> weakQualReasonInfo, List<ZTDIYWeakQualAnaByRegion.QualBurrInfo> tableQualBurr, string key, TableInfoForExcel curTiExcel)
        {
            int curTpCountSum = 0;   //当前网格的总质差采样点数目
            foreach (ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo rInfo in weakQualReasonInfo[key].Values)
            {
                curTpCountSum += rInfo.TpCount;
            }

            foreach (string _key in weakQualReasonInfo[key].Keys)
            {
                TableInfo ti = new TableInfo();
                ti.RegionType = key; //网格名称
                ti.Reason = weakQualReasonInfo[key][_key].Reason;
                ti.TpAllCount = weakQualReasonInfo[key][_key].TpAllCount;
                ti.TpCount = weakQualReasonInfo[key][_key].TpCount;
                ti.WeakQualTpPct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                ti.AvgQual = weakQualReasonInfo[key][_key].AvgQual;
                ti.qualBurrInfoList = weakQualReasonInfo[key][_key].qualBurrInfoList;
                ti.reasonTestpointList = weakQualReasonInfo[key][_key].reasonTestpointList;

                tableInfoList.Add(ti);

                if (ti.qualBurrInfoList != null && ti.qualBurrInfoList.Count > 0)
                {
                    foreach (ZTDIYWeakQualAnaByRegion.QualBurrInfo qi in ti.qualBurrInfoList)
                    {
                        tableQualBurr.Add(qi);
                    }
                }

                setCurTiExcelData(weakQualReasonInfo, key, curTiExcel, curTpCountSum, _key);
            }
        }

        private static void setCurTiExcelData(Dictionary<string, Dictionary<string, ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo>> weakQualReasonInfo, string key, TableInfoForExcel curTiExcel, int curTpCountSum, string _key)
        {
            #region 网格原因概况导出Excel的储存集
            curTiExcel.regionType = key;
            curTiExcel.CurRegionTpAllCount = weakQualReasonInfo[key][_key].TpAllCount;
            curTiExcel.CurRegionTpCount += weakQualReasonInfo[key][_key].TpCount;
            if (curTiExcel.CurRegionTpCount != 0)
            {
                switch (weakQualReasonInfo[key][_key].Reason)
                {
                    case "室分泄漏":
                        curTiExcel.Indoor = weakQualReasonInfo[key][_key].TpCount;
                        curTiExcel.IndoorPct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                        break;
                    case "占用不合理":
                        curTiExcel.CoverLap = weakQualReasonInfo[key][_key].TpCount;
                        curTiExcel.CoverLapPct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                        break;
                    case "弱覆盖":
                        curTiExcel.WeakCover = weakQualReasonInfo[key][_key].TpCount;
                        curTiExcel.WeakCoverPct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                        break;
                    case "覆盖杂乱":
                        curTiExcel.NoMainCell = weakQualReasonInfo[key][_key].TpCount;
                        curTiExcel.NoMainCellPct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                        break;
                    case "背向覆盖":
                        curTiExcel.BackCover = weakQualReasonInfo[key][_key].TpCount;
                        curTiExcel.BackCoverPct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                        break;
                    case "重选问题":
                        curTiExcel.ReselectProblemPct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                        break;
                    case "切换不及时":
                        curTiExcel.HandoverNotInTime = weakQualReasonInfo[key][_key].TpCount;
                        curTiExcel.HandoverNotInTimePct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                        break;
                    case "切换不合理":
                        curTiExcel.HandoverProblem = weakQualReasonInfo[key][_key].TpCount;
                        curTiExcel.HandoverProblemPct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                        break;
                    case "频率干扰或故障":
                        curTiExcel.Interfere_C_I = weakQualReasonInfo[key][_key].TpCount;
                        curTiExcel.Interfere_C_IPct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                        break;
                    case "质量毛刺":
                        curTiExcel.QualBurr = weakQualReasonInfo[key][_key].TpCount;
                        curTiExcel.QualBurrPct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                        break;
                    case "其它":
                        curTiExcel.Other = weakQualReasonInfo[key][_key].TpCount;
                        curTiExcel.OtherPct = Math.Round(100 * (double)weakQualReasonInfo[key][_key].TpCount / (double)curTpCountSum, 2) + "%";
                        break;
                }
            }
            #endregion
        }

        public void BuildCellManage()
        {
            foreach (DevExpress.XtraGrid.Columns.GridColumn cn in gridView.Columns)
            {
                if (cn.FieldName != "RegionType" && cn.FieldName != "TpAllCount")
                {
                    cn.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
                }
            }

            foreach (DevExpress.XtraGrid.Columns.GridColumn cn in bandedGridView2.Columns)
            {
                if (cn.FieldName != "RegionType")
                {
                    cn.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
                }
            }
        }

        private void miExportExcelReasons_Click(object sender, EventArgs e)
        {
            miExportExcelReasons_Click2();
        }

        private void makeTitle(_Worksheet worksheet, int row, int col, string title, int width)
        {
            Range range = worksheet.Cells[row, col] as Range;
            range.Value2 = title;
            range.Font.Bold = true;
            range.ColumnWidth = width;
        }
        private void makeItemRow(_Worksheet worksheet, int row, int column, string str)
        {
            Range range = worksheet.Cells[row, column] as Range;
            range.Value2 = str;
        }
        private void miExportExcelReasons_Click2()
        {
            Microsoft.Office.Interop.Excel.Application app=null;
            try
            {
                app = new Microsoft.Office.Interop.Excel.Application();
                if (app == null)
                {
                    throw (new Exception("ERROR: EXCEL couldn't be started!"));
                }
                app.Visible = false;
                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                Sheets sheets = workbook.Worksheets;
                _Worksheet worksheet = (_Worksheet)sheets.get_Item(1);
                if (worksheet == null)
                {
                    throw (new Exception("ERROR: worksheet == null"));
                }
                WaitBox.Text = "正在导出网格原因分析...";
                worksheet.Name = "网格";
                //====列标题
                int idx = 1;
                makeTitle(worksheet, 1, idx++, "网格", 10);
                makeTitle(worksheet, 1, idx++, "总采样点", 10);
                makeTitle(worksheet, 1, idx++, "质差采样点", 10);
                makeTitle(worksheet, 1, idx++, "质差占比", 10);
                for (int i = 0; i < 11; i++)
                {
                    makeTitle(worksheet, 1, idx++, "", 10);
                }
                //------合并表头
                worksheet.get_Range(worksheet.Cells[1, 5], worksheet.Cells[1, 15]).MergeCells = true;
                Range firstGroupRge_1 = worksheet.Cells[1, 5] as Range;
                firstGroupRge_1.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                firstGroupRge_1.Value2 = "各类原因采样点";
                for (int i = 0; i < 11; i++)
                {
                    makeTitle(worksheet, 1, idx++, "", 10);
                }
                worksheet.get_Range(worksheet.Cells[1, 16], worksheet.Cells[1, 26]).MergeCells = true;
                Range firstGroupRge_2 = worksheet.Cells[1, 16] as Range;
                firstGroupRge_2.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                firstGroupRge_2.Value2 = "各类原因占比";
                //-------

                idx = 1;
                for (int i = 0; i < 4; i++)
                {
                    makeTitle(worksheet, 2, idx++, "", 10);
                }

                //--------合并表头
                worksheet.get_Range(worksheet.Cells[1, 1], worksheet.Cells[2,1]).MergeCells = true;
                Range firstGroupRge_3 = worksheet.Cells[1, 1] as Range;
                firstGroupRge_3.HorizontalAlignment = XlHAlign.xlHAlignCenter;

                worksheet.get_Range(worksheet.Cells[1, 2], worksheet.Cells[2, 2]).MergeCells = true;
                Range firstGroupRge_4 = worksheet.Cells[1, 2] as Range;
                firstGroupRge_4.HorizontalAlignment = XlHAlign.xlHAlignCenter;

                worksheet.get_Range(worksheet.Cells[1, 3], worksheet.Cells[2, 3]).MergeCells = true;
                Range firstGroupRge_5 = worksheet.Cells[1, 3] as Range;
                firstGroupRge_5.HorizontalAlignment = XlHAlign.xlHAlignCenter;

                worksheet.get_Range(worksheet.Cells[1, 4], worksheet.Cells[2, 4]).MergeCells = true;
                Range firstGroupRge_6 = worksheet.Cells[1,4] as Range;
                firstGroupRge_6.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                //------

                //----各类原因采样点子列
                makeTitle(worksheet, 2, idx++, "室分泄漏", 10);
                makeTitle(worksheet, 2, idx++, "占用不合理", 10);
                makeTitle(worksheet, 2, idx++, "弱覆盖", 10);
                makeTitle(worksheet, 2, idx++, "覆盖杂乱", 10);
                makeTitle(worksheet, 2, idx++, "背向覆盖", 10);
                makeTitle(worksheet, 2, idx++, "重选问题", 10);
                makeTitle(worksheet, 2, idx++, "切换不合理", 10);
                makeTitle(worksheet, 2, idx++, "切换不及时", 10);
                makeTitle(worksheet, 2, idx++, "质量毛刺", 10);
                makeTitle(worksheet, 2, idx++, "频率干扰或故障", 10);
                makeTitle(worksheet, 2, idx++, "其它", 10);
                //----
                //-----各类原因占比子列
                makeTitle(worksheet, 2, idx++, "室分泄漏", 10);
                makeTitle(worksheet, 2, idx++, "占用不合理", 10);
                makeTitle(worksheet, 2, idx++, "弱覆盖", 10);
                makeTitle(worksheet, 2, idx++, "覆盖杂乱", 10);
                makeTitle(worksheet, 2, idx++, "背向覆盖", 10);
                makeTitle(worksheet, 2, idx++, "重选问题", 10);
                makeTitle(worksheet, 2, idx++, "切换不合理", 10);
                makeTitle(worksheet, 2, idx++, "切换不及时", 10);
                makeTitle(worksheet, 2, idx++, "质量毛刺", 10);
                makeTitle(worksheet, 2, idx++, "频率干扰或故障", 10);
                makeTitle(worksheet, 2, idx, "其它", 10);
                //------
                
                int rowAt = 3;
                foreach (TableInfoForExcel ti in tableInfoForExcelList)
                {
                    WaitBox.ProgressPercent = 0;
                    int rowOffset = 0;
                    int xx = 1;
                    WaitBox.ProgressPercent = (int)((rowOffset + 1) * 100.0f / tableInfoList.Count);
                    makeItemRow(worksheet, rowAt, xx++, ti.regionType);
                    makeItemRow(worksheet, rowAt, xx++, ti.CurRegionTpAllCount.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.CurRegionTpCount.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.WeakQualPct);

                    makeItemRow(worksheet, rowAt, xx++, ti.Indoor.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.CoverLap.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.WeakCover.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.NoMainCell.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.BackCover.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.ReselectProblem.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.HandoverProblem.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.HandoverNotInTime.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.QualBurr.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.Interfere_C_I.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.Other.ToString());

                    makeItemRow(worksheet, rowAt, xx++, ti.IndoorPct);
                    makeItemRow(worksheet, rowAt, xx++, ti.CoverLapPct);
                    makeItemRow(worksheet, rowAt, xx++, ti.WeakCoverPct);
                    makeItemRow(worksheet, rowAt, xx++, ti.NoMainCellPct);
                    makeItemRow(worksheet, rowAt, xx++, ti.BackCoverPct);
                    makeItemRow(worksheet, rowAt, xx++, ti.ReselectProblemPct);
                    makeItemRow(worksheet, rowAt, xx++, ti.HandoverProblemPct);
                    makeItemRow(worksheet, rowAt, xx++, ti.HandoverNotInTimePct);
                    makeItemRow(worksheet, rowAt, xx++, ti.QualBurrPct);
                    makeItemRow(worksheet, rowAt, xx++, ti.Interfere_C_IPct);
                    makeItemRow(worksheet, rowAt, xx, ti.OtherPct);

                    rowAt++;
                }
                app.Visible = true;
                app.UserControl = true;
                WaitBox.ProgressPercent = 100;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(this, "导出Excel出错：" + ex.Message);
            }
            finally
            {
                WaitBox.Close();
            }

        }

     
        private void ReplayTestpoint(TestPoint tp)
        {
            FileReplayer.Replay(tp, true);
        }

        Dictionary<string, List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint>> fileTpsDic = null;
        private void bandedGridView1_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            int rowHandle = e.FocusedRowHandle;
            ZTDIYWeakQualAnaByRegion.WeakQualCellInfo selCellInfo = (ZTDIYWeakQualAnaByRegion.WeakQualCellInfo)bandedGridViewCell.GetRow(rowHandle);

            #region 小区下的采样点集信息
            fileTpsDic=new Dictionary<string,List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint>>();//文件与采样信息数目的字典
            foreach (ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint reasonTp in selCellInfo.ReasonTpList)
            {
                if (fileTpsDic.ContainsKey(reasonTp.tp.FileName))
                {
                    List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint> reasonTestpointList = fileTpsDic[reasonTp.tp.FileName];
                    reasonTestpointList.Add(reasonTp);
                }
                else
                {
                    List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint> reasonTestpointList = new List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint>();
                    reasonTestpointList.Add(reasonTp);
                    fileTpsDic.Add(reasonTp.tp.FileName, reasonTestpointList);
                }
            }

            this.CbxFileName.Items.Clear();
            List<string> sortList=new List<string>();
            foreach (string file in fileTpsDic.Keys)
            {
                addSortList(sortList, file);
            }
            foreach (string filename in sortList)
            {
                this.CbxFileName.Items.Add(filename);
            }
            this.CbxFileName.SelectedIndex = 0;
            #endregion
        }

        private void addSortList(List<string> sortList, string file)
        {
            if (sortList.Count == 0)
            {
                sortList.Add(file);
            }
            else
            {
                int c = 0;
                for (int i = 0; i < sortList.Count; i++)
                {
                    if (fileTpsDic[file].Count > fileTpsDic[sortList[i]].Count)
                    {
                        sortList.Insert(i, file); //序列按采样点数目由多到少排列
                        break;
                    }
                    c = i;
                }
                if (c == sortList.Count - 1)
                {
                    sortList.Add(file);
                }
            }
        }

        private void CbxFileName_SelectedIndexChanged(object sender, EventArgs e)
        {
            List<TestPointInfo> tpInfoList = new List<TestPointInfo>();
            int sn = 1;
            foreach (ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint reasonTp in fileTpsDic[CbxFileName.SelectedItem.ToString()])
            {
                TestPointInfo tpInfo = new TestPointInfo(reasonTp);
                sn++;

                tpInfoList.Add(tpInfo);
            }
            BindingSource bindingSource = new BindingSource();
            bindingSource.DataSource = tpInfoList;
            this.gridControlTP.DataSource = bindingSource;
            this.gridControlTP.RefreshDataSource();

            int fileCount = CbxFileName.Items.Count;
            int tpCount = tpInfoList.Count;
            string lblText = "文件共" + fileCount + "个，当前文件" + tpCount + "条质差点记录";
            lblCountDetail.Text = lblText;
        }

        private void miReplayTestpoint_Click(object sender, EventArgs e)
        {
            TestPointInfo tpInfo = (TestPointInfo)gridViewTP.GetFocusedRow();
            if (tpInfo!=null)
            {
                ReplayTestpoint(tpInfo.tp);
            }
        }

        private void miReplayTestpointCompare_Click(object sender, EventArgs e)
        {
            TestPointInfo tpInfo = (TestPointInfo)gridViewTP.GetFocusedRow();
            if (tpInfo != null)
            {
                QueryCondition condition = new QueryCondition();
                condition.isCompareMode = true;
                FileInfo fileInfo = new FileInfo();
                fileInfo.ID = tpInfo.tp.FileID;
                fileInfo.ServiceType = tpInfo.tp.ServiceType;
                fileInfo.ProjectID = tpInfo.tp.ProjectType;
                fileInfo.SampleTbName =tpInfo.tp.SampleTbName;
                fileInfo.LogTable = tpInfo.tp.LogTable;
                condition.FileInfos.Add(fileInfo);
                try
                {
                    DIYReplayFileCompareWithinPeriodQuery query = new DIYReplayFileCompareWithinPeriodQuery(MainModel);
                    query.replayTestpoint = tpInfo.tp;
                    query.SetQueryCondition(condition);
                    query.Query();
                }
                catch
                {
                    mModel.MainForm.CancelChange = true;
                }
            }
        }

        private void miLocateLineChart_Click(object sender, EventArgs e)
        {
            TestPointInfo tpInfo = (TestPointInfo)gridViewTP.GetFocusedRow();
            if (tpInfo != null)
            {
                foreach (WorkSheet workSheet in MainModel.WorkSpace.WorkSheets)
                {
                    foreach (ChildFormConfig childFormConfig in workSheet.ChildFormConfigs)
                    {
                        if (childFormConfig.ChildForm is LineChartForm && childFormConfig.Text.Contains("GSM"))
                        {
                            ((LineChartForm)(childFormConfig.ChildForm)).locateDTData(tpInfo.tp);
                        }
                    }
                }
            }
        }

        private void miExportExcelCell_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(bandedGridViewCell);
        }
        
        private void miExportTp_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridViewTP);
        }

        private void miShowTestPoints_Click(object sender, EventArgs e)
        {
            bandedGridView1_DoubleClick(null, null);
        }

        private void bandedGridView1_DoubleClick(object sender, EventArgs e)
        {
            ZTDIYWeakQualAnaByRegion.WeakQualCellInfo selCellInfo = (ZTDIYWeakQualAnaByRegion.WeakQualCellInfo)bandedGridViewCell.GetFocusedRow();
            if (selCellInfo != null)
            {
                MainModel.WeakQualReasonTpList.Clear();

                addWeakQualReasonTpList(selCellInfo);
                Cell cell = CellManager.GetInstance().GetCellByName(selCellInfo.CellName);
                mainModel.SelectedCell = cell;
            }

            MainModel.MainForm.FireWeakQualReasonQueried();

            mainModel.FireSelectedCellChanged(this);
        }

        private void addWeakQualReasonTpList(ZTDIYWeakQualAnaByRegion.WeakQualCellInfo selCellInfo)
        {
            if (selCellInfo.ReasonTpList.Count > 0)
            {
                Dictionary<string, int> reasonColorIndexDic = new Dictionary<string, int>();
                addReasonColorIndexDic(selCellInfo, reasonColorIndexDic);

                List<Color> reasonColorList = new List<Color>();
                reasonColorList.Add(Color.Green);
                reasonColorList.Add(Color.Red);
                reasonColorList.Add(Color.Blue);
                reasonColorList.Add(Color.Orange);
                reasonColorList.Add(Color.Purple);
                reasonColorList.Add(Color.Tomato);
                reasonColorList.Add(Color.Cyan);
                reasonColorList.Add(Color.Coral);
                reasonColorList.Add(Color.Pink);
                reasonColorList.Add(Color.Violet);
                reasonColorList.Add(Color.Yellow);

                foreach (ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint rtp in selCellInfo.ReasonTpList)
                {
                    rtp.color = reasonColorList[reasonColorIndexDic[rtp.reason]];
                    MainModel.WeakQualReasonTpList.Add(rtp);
                }
            }
        }

        private void addReasonColorIndexDic(ZTDIYWeakQualAnaByRegion.WeakQualCellInfo selCellInfo, Dictionary<string, int> reasonColorIndexDic)
        {
            int colorIndex = 0;
            foreach (ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint rtp in selCellInfo.ReasonTpList)
            {
                if (!reasonColorIndexDic.ContainsKey(rtp.reason))
                {
                    reasonColorIndexDic.Add(rtp.reason, colorIndex);
                    if (colorIndex < 11)  //预设十一种原因类别
                    {
                        colorIndex++;
                    }
                }
            }
        }

        private void miGisShowAllTp_Click(object sender, EventArgs e)
        {
            BindingSource  source = (BindingSource)gridControl1.DataSource;
            List<TableInfo> tbListSrc= (List<TableInfo>)source.DataSource;

            List<TableInfo> tableInfoListTmp = new List<TableInfo>();
            foreach (TableInfo ti in tbListSrc)
            {
                if (ti.RegionType != "全网（汇总）")
                {
                    tableInfoListTmp.Add(ti);
                }
            }
            MainModel.WeakQualReasonTpList.Clear();
            int colorIndex = 0;
            Dictionary<string, int> reasonColorIndexDic = new Dictionary<string, int>();
            foreach (TableInfo ti in tableInfoListTmp)
            {
                colorIndex = addReasonColorIndexDic(colorIndex, reasonColorIndexDic, ti);
            }

            List<Color> reasonColorList = new List<Color>();
            reasonColorList.Add(Color.Green);
            reasonColorList.Add(Color.Red);
            reasonColorList.Add(Color.Blue);
            reasonColorList.Add(Color.Orange);
            reasonColorList.Add(Color.Purple);
            reasonColorList.Add(Color.Tomato);
            reasonColorList.Add(Color.Cyan);
            reasonColorList.Add(Color.RosyBrown);
            reasonColorList.Add(Color.Pink);
            reasonColorList.Add(Color.Violet);
            reasonColorList.Add(Color.Yellow);

            foreach (TableInfo ti in tableInfoListTmp)
            {
                foreach (ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint rtp in ti.reasonTestpointList)
                {
                    rtp.color = reasonColorList[reasonColorIndexDic[rtp.reason]];
                    MainModel.WeakQualReasonTpList.Add(rtp);
                }
            }

            MainModel.MainForm.FireWeakQualReasonQueried();
            MainModel.RefreshLegend();
        }

        private static int addReasonColorIndexDic(int colorIndex, Dictionary<string, int> reasonColorIndexDic, TableInfo ti)
        {
            foreach (ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint rtp in ti.reasonTestpointList)
            {
                if (!reasonColorIndexDic.ContainsKey(rtp.reason))
                {
                    reasonColorIndexDic.Add(rtp.reason, colorIndex);
                    if (colorIndex < 11)  //预设十一种原因类别
                        colorIndex++;
                }
            }

            return colorIndex;
        }

        private class TableInfo : ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo
        {
            public string RegionType { get; set; }
        }

        private class TableInfoForExcel
        {
            public string regionType;

            /// <summary>
            /// 当前网格总采样点
            /// </summary>
            public int CurRegionTpAllCount = 0;
            /// <summary>
            /// 当前网格总的质差采样点
            /// </summary>
            public int CurRegionTpCount = 0;

            /// <summary>
            /// 质差占比
            /// </summary>
            public string WeakQualPct
            {
                get { return Math.Round(CurRegionTpCount * 100f / CurRegionTpAllCount, 2) + "%"; }
            }

            /// <summary>
            /// 室内泄漏数目
            /// </summary>
            public int Indoor;

            /// <summary>
            /// 占用不合理数目
            /// </summary>
            public int CoverLap;

            /// <summary>
            /// 弱覆盖数目
            /// </summary>
            public int WeakCover;

            /// <summary>
            /// 覆盖杂乱数目
            /// </summary>
            public int NoMainCell;

            /// <summary>
            /// 背向覆盖数目
            /// </summary>
            public int BackCover;

            /// <summary>
            /// 重选问题
            /// </summary>
            public int ReselectProblem = 0;

            /// <summary>
            /// 切换不及时数目
            /// </summary>
            public int HandoverNotInTime;

            /// <summary>
            /// 切换不合理数目
            /// </summary>
            public int HandoverProblem;

            /// <summary>
            /// C/I干扰数目
            /// </summary>
            public int Interfere_C_I;

            /// <summary>
            /// 质量毛刺数目
            /// </summary>
            public int QualBurr;

            /// <summary>
            /// 其它数目
            /// </summary>
            public int Other;

            /// <summary>
            /// 室内泄漏数目占比
            /// </summary>
            public string IndoorPct;

            /// <summary>
            /// 占用不合理数目占比
            /// </summary>
            public string CoverLapPct;

            /// <summary>
            /// 弱覆盖数目占比
            /// </summary>
            public string WeakCoverPct;

            /// <summary>
            /// 覆盖杂乱数目占比
            /// </summary>
            public string NoMainCellPct;

            /// <summary>
            /// 背向覆盖数目占比
            /// </summary>
            public string BackCoverPct;

            /// <summary>
            /// 重选问题占比
            /// </summary>
            public string ReselectProblemPct;

            /// <summary>
            /// 切换不及时数目占比
            /// </summary>
            public string HandoverNotInTimePct;

            /// <summary>
            /// 切换不合理数目占比
            /// </summary>
            public string HandoverProblemPct;

            /// <summary>
            /// C/I干扰数目占比
            /// </summary>
            public string Interfere_C_IPct;

            /// <summary>
            /// 质量毛刺数目占比
            /// </summary>
            public string QualBurrPct;

            /// <summary>
            /// 其它数目占比
            /// </summary>
            public string OtherPct;
        }

        private class TestPointInfo
        {
            public TestPointInfo(ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint reasonTestpoint)
            {
                this.tp = reasonTestpoint.tp;
            }
            public TestPoint tp;
        }

        public class DIYReplayFileCompareWithinPeriodQuery : ReplayFileCompare
        {
            public TestPoint replayTestpoint { get; set; }
            public DIYReplayFileCompareWithinPeriodQuery(MainModel mainModel)
                : base(mainModel)
            {
            }

            protected override void replay()
            {
                QueryCondition condition = new QueryCondition();
                condition.QueryType = QueryType.General;
                condition.FileInfos.AddRange(Condition.FileInfos);
                condition.Periods.AddRange(Condition.Periods);
                condition.isCompareMode = Condition.isCompareMode;
                condition.DistrictID = Condition.DistrictID;

                PreNextMinutesForm preNextMinutesForm = new PreNextMinutesForm(false);
                if (preNextMinutesForm.ShowDialog() == DialogResult.OK)
                {
                    int pre = preNextMinutesForm.Pre;
                    int next = preNextMinutesForm.Next;
                    DateTime timeStart = replayTestpoint.DateTime.AddMinutes(-pre);
                    DateTime timeEnd = replayTestpoint.DateTime.AddMinutes(next);
                    condition.Periods.Add(new TimePeriod(timeStart, timeEnd));

                    try
                    {
                        DIYReplayFileWithinPeriodQuery query = new DIYReplayFileWithinPeriodQuery(MainModel);
                        query.SetQueryCondition(condition);
                        query.Query();
                        log.Info("回放完毕。");
                    }
                    catch
                    {
                        MainModel.MainForm.CancelChange = true;
                    }
                }
                else
                {
                    MainModel.MainForm.CancelChange = true;
                }
      
            }
        }


        private void toolStripMenuItem12_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

        private void outputShpFile(string reason)
        {
            try
            {
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = FilterHelper.Shp;
                saveFileDialog.FilterIndex = 1;
                saveFileDialog.Title = "导出shp文件";
                saveFileDialog.RestoreDirectory = true;
                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string filename = saveFileDialog.FileName;
                    Shapefile shpFile = new Shapefile();

                    int fColor = setShpFileValue(reason, shpFile);

                    Dictionary<uint, int> categoryByColor = new Dictionary<uint, int>();
                    addCategoryByColor(fColor, shpFile, categoryByColor);
                    applyCategories(fColor, shpFile, categoryByColor);
                    dealFiles(filename, shpFile);
                    MessageBox.Show("导出完成！");
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private int setShpFileValue(string reason, Shapefile shpFile)
        {
            Dictionary<string, Dictionary<string, ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo>> weakQualReasonInfo = MainModel.WeakQualReasonInfo;

            int idIdx = 0;
            int fColor = idIdx++;
            int fRegionName = idIdx++;
            int fReason = idIdx++;
            int fLongitude = idIdx++;
            int fLatitude = idIdx;

            bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POINT);
            if (!result)
            {
                MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
            }
            shpFile.DefaultDrawingOptions.SetDefaultPointSymbol(tkDefaultPointSymbol.dpsCircle);

            ShapeHelper.InsertNewField(shpFile, "Color", FieldType.INTEGER_FIELD, 10, 30, ref fColor);
            ShapeHelper.InsertNewField(shpFile, "RegionName", FieldType.STRING_FIELD, 10, 0, ref fRegionName);
            ShapeHelper.InsertNewField(shpFile, "Reason", FieldType.STRING_FIELD, 10, 0, ref fReason);
            ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 10, 0, ref fLongitude);
            ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 10, 0, ref fLatitude);

            int shpIdx = 0;
            foreach (string regionName in weakQualReasonInfo.Keys)
            {
                foreach (ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo reasonClassifyInfo in weakQualReasonInfo[regionName].Values)
                {
                    foreach (ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint rtp in reasonClassifyInfo.reasonTestpointList)
                    {
                        if (reason == "全部" || rtp.reason == reason)
                        {
                            MapWinGIS.Shape spBase = new MapWinGIS.Shape();
                            spBase.Create(ShpfileType.SHP_POINT);
                            MapWinGIS.Point pt = new MapWinGIS.Point();
                            pt.x = rtp.tp.Longitude;
                            pt.y = rtp.tp.Latitude;
                            uint oleColor = (uint)ColorTranslator.ToOle(rtp.color);
                            int j = 0;
                            spBase.InsertPoint(pt, ref j);
                            shpFile.EditInsertShape(spBase, ref shpIdx);
                            shpFile.EditCellValue(fColor, shpIdx, (int)oleColor);
                            shpFile.EditCellValue(fRegionName, shpIdx, regionName);
                            shpFile.EditCellValue(fReason, shpIdx, rtp.reason);
                            shpFile.EditCellValue(fLongitude, shpIdx, rtp.tp.Longitude);
                            shpFile.EditCellValue(fLatitude, shpIdx, rtp.tp.Latitude);

                            shpIdx++;
                        }
                    }
                }
            }

            return fColor;
        }

        private static void addCategoryByColor(int fColor, Shapefile shpFile, Dictionary<uint, int> categoryByColor)
        {
            for (int i = 0; i < shpFile.NumShapes; i++)
            {
                object vx = shpFile.get_CellValue(fColor, i);
                uint color = (uint)(int)vx;
                if (!categoryByColor.ContainsKey(color))
                {
                    string name = shpFile.Categories.Count.ToString();
                    MapWinGIS.ShapefileCategory cat = shpFile.Categories.Add(name);
                    if (cat != null)
                    {
                        cat.DrawingOptions.FillColor = color;
                        categoryByColor.Add(color, shpFile.Categories.Count - 1);
                    }
                }
            }
        }

        private static void applyCategories(int fColor, Shapefile shpFile, Dictionary<uint, int> categoryByColor)
        {
            for (int i = 0; i < shpFile.NumShapes; i++)
            {
                object vx = shpFile.get_CellValue(fColor, i);
                uint color = (uint)(int)vx;
                uint clr = color;
                int catIndex = categoryByColor[clr];
                shpFile.set_ShapeCategory(i, catIndex);
            }
        }

        private void dealFiles(string filename, Shapefile shpFile)
        {
            try
            {
                string delPath = System.IO.Path.GetDirectoryName(filename);
                string delFile = System.IO.Path.GetFileNameWithoutExtension(filename);
                System.IO.File.Delete(string.Format(delPath + @"\" + delFile + ".shp"));
                System.IO.File.Delete(string.Format(delPath + @"\" + delFile + ".dbf"));
                System.IO.File.Delete(string.Format(delPath + @"\" + delFile + ".prj"));
                System.IO.File.Delete(string.Format(delPath + @"\" + delFile + ".shx"));
            }
            catch
            {
                //continue
            }

            try
            {
                shpFile.SaveAs(filename, null);
            }
            catch (System.Exception ex)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("保存文件失败！" + ex.Message);
            }
            finally
            {
                shpFile.Close();
            }
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

        private void toolStripMenuItem3_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

        private void toolStripMenuItem4_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

        private void toolStripMenuItem5_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

        private void toolStripMenuItem6_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

        private void toolStripMenuItem7_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

        private void toolStripMenuItem8_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

        private void toolStripMenuItem9_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

        private void toolStripMenuItem10_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

        private void toolStripMenuItem11_Click(object sender, EventArgs e)
        {
            outputShpFile(((ToolStripMenuItem)sender).Text);
        }

    }
}
