using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Collections;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLeakOutCellForm : MinCloseForm
    {
        public ZTLeakOutCellForm(MainModel mModel)
            : base(mModel)
        {
            InitializeComponent();
            init();
            DisposeWhenClose = true;
        }

        private void init()
        {
            olvColumnSn.AspectGetter = delegate (object row)
            {
                if (row is LeakOutCellRoad_TD)
                {
                    return (row as LeakOutCellRoad_TD).SN;
                }
                return null;
            };

            olvColumnCellName.AspectGetter = delegate (object row)
            {
                if (row is LeakOutCellRoad_TD)
                {
                    return (row as LeakOutCellRoad_TD).CellName;
                }
                else if (row is LeakOutSequenceTestPointItem)
                {
                    return ((TDCell)(row as LeakOutSequenceTestPointItem).Cell).Name;
                }
                return null;
            };

            olvColumnLAC.AspectGetter = delegate (object row)
            {
                if (row is LeakOutCellRoad_TD)
                {
                    return (row as LeakOutCellRoad_TD).LAC;
                }
                else if (row is LeakOutSequenceTestPointItem)
                {
                    return ((TDCell)(row as LeakOutSequenceTestPointItem).Cell).LAC;
                }
                return null;
            };

            olvColumnCI.AspectGetter = delegate (object row)
            {
                if (row is LeakOutCellRoad_TD)
                {
                    return (row as LeakOutCellRoad_TD).CI;
                }
                else if (row is LeakOutSequenceTestPointItem)
                {
                    return ((TDCell)(row as LeakOutSequenceTestPointItem).Cell).CI;
                }
                return null;
            };

            olvColumnCPI.AspectGetter = delegate (object row)
            {
                if (row is LeakOutCellRoad_TD)
                {
                    return (row as LeakOutCellRoad_TD).CPI;
                }
                else if (row is LeakOutSequenceTestPointItem)
                {
                    return ((TDCell)(row as LeakOutSequenceTestPointItem).Cell).CPI;
                }
                return null;
            };

            olvColumnUARFCN.AspectGetter = delegate (object row)
            {
                if (row is LeakOutCellRoad_TD)
                {
                    return (row as LeakOutCellRoad_TD).UARFCN;
                }
                else if (row is LeakOutSequenceTestPointItem)
                {
                    return ((TDCell)(row as LeakOutSequenceTestPointItem).Cell).FREQ;
                }
                return null;
            };

            setLeakOutSequenceTestPointItem();

            this.objectListView.CanExpandGetter = delegate (object row)
            {
                return row is LeakOutCellRoad_TD;
            };
            this.objectListView.ChildrenGetter = delegate (object row)
            {
                LeakOutCellRoad_TD cell = row as LeakOutCellRoad_TD;
                return cell.SequenceTestPointItems;
            };
        }

        private void setLeakOutSequenceTestPointItem()
        {
            olvColumnCellSrvType.AspectGetter = delegate (object row)
            {
                if (row is LeakOutSequenceTestPointItem)
                {
                    return (row as LeakOutSequenceTestPointItem).CellSrvType;
                }
                return null;
            };

            olvColumnFileName.AspectGetter = delegate (object row)
            {
                if (row is LeakOutSequenceTestPointItem)
                {
                    return (row as LeakOutSequenceTestPointItem).FileName;
                }
                return null;
            };

            olvColumnTestPointCount.AspectGetter = delegate (object row)
            {
                if (row is LeakOutSequenceTestPointItem)
                {
                    return (row as LeakOutSequenceTestPointItem).TestPointCount;
                }
                return null;
            };

            olvColumnStartTime.AspectGetter = delegate (object row)
            {
                if (row is LeakOutSequenceTestPointItem)
                {
                    return (row as LeakOutSequenceTestPointItem).SequenceStartTime;
                }
                return null;
            };

            olvColumnEndTime.AspectGetter = delegate (object row)
            {
                if (row is LeakOutSequenceTestPointItem)
                {
                    return (row as LeakOutSequenceTestPointItem).SequenceEndTime;
                }
                return null;
            };

            olvColumnDistance.AspectGetter = delegate (object row)
            {
                if (row is LeakOutSequenceTestPointItem)
                {
                    return (row as LeakOutSequenceTestPointItem).Distance;
                }
                return null;
            };

            olvColumnRxLevMin.AspectGetter = delegate (object row)
            {
                if (row is LeakOutSequenceTestPointItem)
                {
                    return (row as LeakOutSequenceTestPointItem).MinRxLev;
                }
                return null;
            };

            olvColumnRxLevMax.AspectGetter = delegate (object row)
            {
                if (row is LeakOutSequenceTestPointItem)
                {
                    return (row as LeakOutSequenceTestPointItem).MaxRxLev;
                }
                return null;
            };
            olvColumnRxLevMean.AspectGetter = delegate (object row)
            {
                if (row is LeakOutSequenceTestPointItem)
                {
                    return (row as LeakOutSequenceTestPointItem).AvgRxLev;
                }
                return null;
            };
        }

        public void ShowCellSet(List<LeakOutCellRoad_TD> cellSet)
        {
            objectListView.ClearObjects();
            objectListView.SetObjects(cellSet);
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            objectListView.ExpandAll();
            ExcelNPOIManager.ExportToExcel(objectListView);
        }

        private void objectListView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            LeakOutCellRoad_TD leakOutCell = objectListView.SelectedObject as LeakOutCellRoad_TD;
            if (leakOutCell != null)
            {
                TDCell cell = (TDCell)leakOutCell.Cell;
                MainModel.SelectedTDCell = cell;
                MainModel.MainForm.GetMapForm().GoToView(cell.Longitude,cell.Latitude);
            }
            else
            {
                LeakOutSequenceTestPointItem item = objectListView.SelectedObject as LeakOutSequenceTestPointItem;
                if (item!=null)
                {
                    TDCell cell = (TDCell)item.Cell;
                    MainModel.SelectedTDCell = cell;
                    MainModel.MainForm.GetMapForm().GoToView(cell.Longitude, cell.Latitude);

                    mModel.DTDataManager.Clear();
                    foreach (TestPoint tp in item.TestPoints)
                    {
                        mModel.DTDataManager.Add(tp);
                    }
                    mModel.FireDTDataChanged(this);
                }
            }

        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            objectListView.ExpandAll();
        }

        private void miColAll_Click(object sender, EventArgs e)
        {
            objectListView.CollapseAll();
        }
    }
}