﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ReadLongLatFromXlsPlatForm : BaseForm
    {
        public ReadLongLatFromXlsPlatForm(bool showRadius)
        {
            InitializeComponent();
            this.labelRadius.Visible = showRadius;
            this.labelUnit.Visible = showRadius;
            this.spinEditRadius.Visible = showRadius;
        }

        public string FilePath
        {
            get { return textBoxFilePath.Text; }
        }

        public int Radius
        {
            get { return (int)spinEditRadius.Value; }
        }

        public void SetFilePath(string filePath)
        {
            if (filePath != null)
            {
                textBoxFilePath.Text = filePath;
            }
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            if (textBoxFilePath.Text.Trim().Equals(""))
                return;
            this.DialogResult = DialogResult.OK;
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void simpleBtnSelect_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Multiselect = false;
            dlg.Filter = "Excel files |*.xls;*.xlsx";
            if (dlg.ShowDialog() != DialogResult.OK) return;
            textBoxFilePath.Text = dlg.FileName;
        }
    }
}
