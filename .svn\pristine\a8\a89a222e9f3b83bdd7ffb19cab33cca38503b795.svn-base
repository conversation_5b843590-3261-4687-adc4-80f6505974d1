﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HandoverBehindTimeListForm_TD : MinCloseForm
    {
        public HandoverBehindTimeListForm_TD(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
        }

        public void FillData(List<HandoverBehindTimeInfo> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            int[] rows = gridView.GetSelectedRows();
            if (rows.Length>0)
            {
                HandoverBehindTimeInfo info = gridView.GetRow(rows[0]) as HandoverBehindTimeInfo;
                if (info!=null)
                {
                    MainModel.ClearDTData();
                    foreach (TestPoint tp in info.TestPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    MainModel.FireDTDataChanged(this);
                    MainModel.FireSetDefaultMapSerialTheme("TD_PCCPCH_RSCP");
                }
            }
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = "Excel2007 (*.xlsx)|*.xlsx";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    gridControl.ExportToXlsx(dlg.FileName);
                    if (DevExpress.XtraEditors.XtraMessageBox.Show("导出完毕，是否打开文件？", "提示", MessageBoxButtons.YesNo) == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start(dlg.FileName);
                    }
                }
                catch
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("导出失败！");
                }
            }
        }

    }
}