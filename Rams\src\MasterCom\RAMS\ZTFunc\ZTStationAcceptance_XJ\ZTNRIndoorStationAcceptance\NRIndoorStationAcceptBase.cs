﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    /// <summary>
    /// NR业务分析基类
    /// </summary>
    public abstract class NRIndoorStationAcceptBase : StationAcceptBase
    {
        protected NRIndoorCellServiceInfo curCellServiceInfo;

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NRIndoorBtsInfo nrBtsInfo = bts as NRIndoorBtsInfo;
            NRIndoorCellInfo nrCellInfo = cell as NRIndoorCellInfo;
            NRIndoorStationAcceptCondition nrCondition = condition as NRIndoorStationAcceptCondition;

            if (nrCondition.NRServiceType == NRServiceName.NSA)
            {
                curCellServiceInfo = nrCellInfo.NSAInfo;
            }
            else if (nrCondition.NRServiceType == NRServiceName.SA)
            {
                curCellServiceInfo = nrCellInfo.SAInfo;
            }
            else
            {
                return;
            }

            bool needAna = judgeNeedAna(fileInfo, fileManager, nrBtsInfo, nrCellInfo, nrCondition);
            if (!needAna)
            {
                return;
            }

            analyzeNRFile(fileInfo, fileManager, nrBtsInfo, nrCellInfo, nrCondition);

            verifyFileResult(fileInfo, fileManager, nrBtsInfo, nrCellInfo, nrCondition);
        }

        protected virtual bool judgeNeedAna(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            return true;
        }

        protected virtual void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {

        }

        protected virtual void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {

        }
    }

    /// <summary>
    /// 分析采样点业务基类
    /// </summary>
    public abstract class NRIndoorStationAcceptSample : NRIndoorStationAcceptBase
    {
        protected NRCell getMainCell(TestPoint tp, ICell nrCell)
        {
            int? arfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            List<NRCell> cellList = StationAcceptCellHelper_XJ.Instance.GetNRCellListByEarfcnPci(tp, arfcn, pci);
            if (cellList == null)
            {
                return null;
            }

            foreach (NRCell cell in cellList)
            {
                if (nrCell.Name == cell.Name)
                {
                    return cell;
                }
            }

            return null;
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                ICell iCell = getMainCell(tp, cell.Cell);
                if (iCell != null)
                {
                    addData(tp, cell, condition);
                }
            }
        }

        /// <summary>
        /// 添加每个采样点的指标数据
        /// </summary>
        protected abstract void addData(TestPoint tp, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition);
    }

    /// <summary>
    /// 分析速率业务基类
    /// </summary>
    public abstract class NRIndoorStationAcceptThroughput : NRIndoorStationAcceptSample
    {
        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            ThroughputStandard standard = getThroughputStandard(fileInfo.Name, condition);
            if (standard == null)
            {
                return;
            }

            verifyThroughput(cell, condition, standard);
        }

        /// <summary>
        /// 根据文件名获取吞吐率判断标准
        /// </summary>
        protected ThroughputStandard getThroughputStandard(string fileName, NRIndoorStationAcceptCondition condition)
        {
            string token = NRStationAcceptFileNameHelper.GetThroughputFileToken(fileName, 8);
            if (string.IsNullOrEmpty(token))
            {
                log.Error($"{fileName}文件没有找到对应的速率标准,请检查文件命名");
                return null;
            }

            ThroughputStandard standard = condition.Standard.ThroughputStandardList.Find(x => x.Token == token);
            return standard;
        }

        /// <summary>
        /// 判断速率是否有效
        /// </summary>
        protected abstract void verifyThroughput(NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition, ThroughputStandard standard);

        protected void dealValidGoodThroughput(FtpPointInfo info, ThroughputStandard.DataInfo standard)
        {
            if (info.Rsrp.Divisor > 0 && info.Rsrp.Data > -80)
            {
                info.Rsrp.IsValid = true;
            }
            if (info.Sinr.Data > 18)
            {
                info.Sinr.IsValid = true;
            }
            dealValidThroughput(info, standard, ThroughputStandard.PointType.Good);
        }

        protected void dealValidBadThroughput(FtpPointInfo info, ThroughputStandard.DataInfo standard)
        {
            info.Rsrp.IsValid = true;
            info.Sinr.IsValid = true;

            dealValidThroughput(info, standard, ThroughputStandard.PointType.Bad);
        }

        private void dealValidThroughput(FtpPointInfo info, ThroughputStandard.DataInfo standard, ThroughputStandard.PointType pointType)
        {
            bool isValid = standard.JudgeThroughput(pointType, info.Throughput.Data);
            if (isValid)
            {
                info.Throughput.IsValid = true;
            }
            info.Judge();
        }
    }

    /// <summary>
    /// 分析事件业务基类
    /// </summary>
    public abstract class NRIndoorStationAcceptEvent : NRIndoorStationAcceptBase
    {
        protected List<int> evtSuccList;
        protected List<int> evtRequList;

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            SuccessRateKpiInfo eventInfo = initEventKpiInfo(cell, condition);
            dealEvtDataList(eventInfo, fileManager.Events);
        }

        protected virtual void dealEvtDataList(SuccessRateKpiInfo eventInfo, List<Event> evtList)
        {
            foreach (Event evt in evtList)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    eventInfo.RequestCnt++;
                }
                else if (evtSuccList.Contains(evt.ID))
                {
                    eventInfo.SucceedCnt++;
                }
            }
            eventInfo.CalculateFailEvt();
        }

        protected abstract SuccessRateKpiInfo initEventKpiInfo(NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition);
    }

    //public abstract class NRIndoorStationAcceptRelatePic : NRIndoorStationAcceptBase
    //{
    //    protected string curBtsPicPath;
    //    protected List<string> picNameList;
    //    protected readonly StationAcceptConfigHelper configHelper;
    //    protected NRIndoorStationAcceptRelatePic(StationAcceptConfigHelper configHelper)
    //    {
    //        this.configHelper = configHelper;
    //    }

    //    protected void downloadPicFile(string btsName, List<string> picNameList)
    //    {
    //        //1.获取当前测试站路径
    //        configHelper.ConfigInfo.ServerCoverPicPath += System.IO.Path.DirectorySeparatorChar + btsName;
    //        curBtsPicPath = configHelper.ConfigInfo.LocalCoverPicPath + System.IO.Path.DirectorySeparatorChar + btsName;
    //        if (!System.IO.Directory.Exists(curBtsPicPath))
    //        {
    //            System.IO.Directory.CreateDirectory(curBtsPicPath);
    //        }

    //        //2.获取服务端图片下载路径
    //        List<string> picServerPathList = initPicPath(picNameList, configHelper.ConfigInfo.ServerCoverPicPath);

    //        //3.下载图片到本地
    //        DownloadStationAcceptPic downloadQuery = new DownloadStationAcceptPic(picServerPathList, curBtsPicPath);
    //        downloadQuery.Query();
    //    }

    //    protected List<string> initPicPath(List<string> coverPicResult, string serverDirectoryName)
    //    {
    //        List<string> picPathList = new List<string>();
    //        foreach (var coverPicData in coverPicResult)
    //        {
    //            string realPath = serverDirectoryName + System.IO.Path.DirectorySeparatorChar + coverPicData;
    //            picPathList.Add(realPath);
    //        }

    //        return picPathList;
    //    }

    //    protected string getValidPic(string picPath, string picName)
    //    {
    //        string pic = $"{picPath}/{picName}";
    //        if (!System.IO.File.Exists(pic))
    //        {
    //            return "";
    //        }
    //        return pic;
    //    }

    //    public override void Clear()
    //    {
    //        System.Threading.Thread.Sleep(100);
    //        if (System.IO.Directory.Exists(curBtsPicPath))
    //        {
    //            System.IO.Directory.Delete(curBtsPicPath, true);
    //        }
    //    }
    //}
}
