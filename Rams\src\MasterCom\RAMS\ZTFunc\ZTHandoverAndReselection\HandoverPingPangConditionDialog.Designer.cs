﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class HandoverPingPangConditionDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numTimeLimit = new DevExpress.XtraEditors.SpinEdit();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.chkSpeedLimit = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numSpeedLimitMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.numSpeedLimitMax = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSpeedLimit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedLimitMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedLimitMax.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(30, 19);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 12);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "时间限制≤";
            // 
            // numTimeLimit
            // 
            this.numTimeLimit.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numTimeLimit.Location = new System.Drawing.Point(96, 16);
            this.numTimeLimit.Name = "numTimeLimit";
            this.numTimeLimit.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numTimeLimit.Properties.Appearance.Options.UseFont = true;
            this.numTimeLimit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numTimeLimit.Properties.IsFloatValue = false;
            this.numTimeLimit.Properties.Mask.EditMask = "N00";
            this.numTimeLimit.Size = new System.Drawing.Size(78, 20);
            this.numTimeLimit.TabIndex = 1;
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(214, 187);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(309, 187);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // chkSpeedLimit
            // 
            this.chkSpeedLimit.Location = new System.Drawing.Point(22, 31);
            this.chkSpeedLimit.Name = "chkSpeedLimit";
            this.chkSpeedLimit.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSpeedLimit.Properties.Appearance.Options.UseFont = true;
            this.chkSpeedLimit.Properties.Caption = "车速限制";
            this.chkSpeedLimit.Size = new System.Drawing.Size(75, 19);
            this.chkSpeedLimit.TabIndex = 4;
            this.chkSpeedLimit.CheckedChanged += new System.EventHandler(this.chkSpeedLimit_CheckedChanged);
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(180, 19);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(12, 12);
            this.labelControl2.TabIndex = 5;
            this.labelControl2.Text = "秒";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(265, 63);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(54, 12);
            this.labelControl3.TabIndex = 8;
            this.labelControl3.Text = "公里/小时";
            // 
            // numSpeedLimitMin
            // 
            this.numSpeedLimitMin.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numSpeedLimitMin.Location = new System.Drawing.Point(24, 61);
            this.numSpeedLimitMin.Name = "numSpeedLimitMin";
            this.numSpeedLimitMin.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSpeedLimitMin.Properties.Appearance.Options.UseFont = true;
            this.numSpeedLimitMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSpeedLimitMin.Properties.IsFloatValue = false;
            this.numSpeedLimitMin.Properties.Mask.EditMask = "N00";
            this.numSpeedLimitMin.Size = new System.Drawing.Size(78, 20);
            this.numSpeedLimitMin.TabIndex = 7;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(104, 64);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(66, 12);
            this.labelControl4.TabIndex = 6;
            this.labelControl4.Text = " ≤ 车速 ≤";
            // 
            // numSpeedLimitMax
            // 
            this.numSpeedLimitMax.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numSpeedLimitMax.Location = new System.Drawing.Point(181, 60);
            this.numSpeedLimitMax.Name = "numSpeedLimitMax";
            this.numSpeedLimitMax.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSpeedLimitMax.Properties.Appearance.Options.UseFont = true;
            this.numSpeedLimitMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSpeedLimitMax.Properties.IsFloatValue = false;
            this.numSpeedLimitMax.Properties.Mask.EditMask = "N00";
            this.numSpeedLimitMax.Size = new System.Drawing.Size(78, 20);
            this.numSpeedLimitMax.TabIndex = 10;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.chkSpeedLimit);
            this.groupBox1.Controls.Add(this.numSpeedLimitMin);
            this.groupBox1.Controls.Add(this.numSpeedLimitMax);
            this.groupBox1.Controls.Add(this.labelControl3);
            this.groupBox1.Controls.Add(this.labelControl4);
            this.groupBox1.Location = new System.Drawing.Point(30, 60);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(354, 102);
            this.groupBox1.TabIndex = 12;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "车速设置";
            // 
            // HandoverPingPangConditionDialog
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(410, 236);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.numTimeLimit);
            this.Controls.Add(this.labelControl1);
            this.Name = "HandoverPingPangConditionDialog";
            this.Text = "乒乓切换条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSpeedLimit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedLimitMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedLimitMax.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit numTimeLimit;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.CheckEdit chkSpeedLimit;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit numSpeedLimitMin;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit numSpeedLimitMax;
        private System.Windows.Forms.GroupBox groupBox1;
    }
}