﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.src;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.Net
{
    public class DIYQueryAbnormalDataGridByRegion : DIYCellGridQueryBase
    {
        public DIYQueryAbnormalDataGridByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get
            {
                return "栅格异常波动分析(按小区)";
            }
        }
        public override string IconName
        {
            get { return null; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11016, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            this.AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            return condition.Geometorys.GeoOp.ContainsRectCenter(grid.Bounds);
        }

        #region 全局变量
        DTDataHeaderManager headerManager = null;
        private int gridSampleNum;
        private double gridRate;
        private float smax;
        readonly Dictionary<int, Dictionary<GridInfo, int>> dicGridFile = new Dictionary<int, Dictionary<GridInfo, int>>();
        List<AbnormalFileInfo> abnormalDataList = null;
        readonly Dictionary<int, List<GridCellFile>> dicGridCellFile = new Dictionary<int, List<GridCellFile>>();
        Dictionary<GridInfo, int> dicGridInfo = null;
        GridMatrix<GridCellUnit> gridCellMatrix = new GridMatrix<GridCellUnit>();
        
        #endregion

        protected override bool getConditionBeforeQuery()
        {
            DIYQueryAbnormalDataGridSettingForm abnormalDataGridSet = new DIYQueryAbnormalDataGridSettingForm();
            if (abnormalDataGridSet.ShowDialog() == DialogResult.OK)
            {
                abnormalDataGridSet.GetAbnormalDataGridSet(out gridSampleNum, out gridRate, out smax);
                return true;
            }
            else
            {
                return false;
            }
        }
  
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                WaitBox.Text = "开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                setFormula();
                string statImgIDSet = "2,1,1044,2,1,1045";
               
                foreach (TimePeriod period in condition.Periods)
                {
                    List<QueryCondUnit> RegionChangedPeriods = parseChangePeriodsOfRegion(period);
                    foreach (QueryCondUnit cond in RegionChangedPeriods)
                    {
                        queryPeriodInfo(cond.period, clientProxy, cond, statImgIDSet, gridCellMatrix);
                    } 
                }

                WaitBox.Text = "数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                log.Error("Error:" + e.Message);
            }
            finally
            {
                System.Threading.Thread.Sleep(1000);
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            List<GridCellFile> listGridCellFile;
            foreach (GridCellUnit gridCellUnit in gridCellMatrix)
            {
                listGridCellFile = gridCellUnit.GridCellUnitDoData(gridSampleNum, smax);
                setDicGridCellFile(listGridCellFile);
            }
            gridCellMatrix = new GridMatrix<GridCellUnit>();//清空栅格
            abnormalDataList = new List<AbnormalFileInfo>();
            foreach (int ifileid in dicGridCellFile.Keys)
            {
                if (dicGridFile.TryGetValue(ifileid, out dicGridInfo) && dicGridCellFile.TryGetValue(ifileid, out listGridCellFile))
                {
                    double rate = Double.Parse(listGridCellFile.Count.ToString()) / Double.Parse(dicGridInfo.Count.ToString());

                    if (rate > gridRate)
                    {
                        addAbnormalDataList(listGridCellFile, ifileid);
                    }
                }
            }
        }

        private void setDicGridCellFile(List<GridCellFile> listGridCellFile)
        {
            if (listGridCellFile != null && listGridCellFile.Count > 0)
            {
                List<GridCellFile> tmpList = null;
                foreach (GridCellFile gridCellFile in listGridCellFile)
                {
                    if (!dicGridCellFile.TryGetValue(gridCellFile.IFileID, out tmpList))
                    {
                        tmpList = new List<GridCellFile>();
                        dicGridCellFile[gridCellFile.IFileID] = tmpList;
                    }
                    tmpList.Add(gridCellFile);
                }
            }
        }

        private void addAbnormalDataList(List<GridCellFile> listGridCellFile, int ifileid)
        {
            AbnormalFileInfo abnormalDataInfo = new AbnormalFileInfo();
            abnormalDataInfo.StriFileID = ifileid;
            abnormalDataInfo.StriFileName = GetiFileName(ifileid);
            abnormalDataInfo.StrAbnormalGrid = listGridCellFile.Count;
            abnormalDataInfo.StrGridTotal = dicGridInfo.Count;

            foreach (GridCellFile gridCellFile in listGridCellFile)
            {
                AbnormalGridCellInfo abnormalCellInfo = new AbnormalGridCellInfo();
                abnormalCellInfo.StrLng = gridCellFile.LtLongitude;
                abnormalCellInfo.StrLat = gridCellFile.LtLatitude;
                abnormalCellInfo.StrCellName = gridCellFile.Cell.Name;
                abnormalCellInfo.StrLAC = gridCellFile.Cell.LAC;
                abnormalCellInfo.StrCI = gridCellFile.Cell.CI;
                abnormalCellInfo.StrRxlev = gridCellFile.GridCellRxlev;
                abnormalCellInfo.StrRxlevAvg = gridCellFile.GridCellRxlevAvg;
                abnormalCellInfo.StrSRxlev = gridCellFile.GridCellRxlev - gridCellFile.GridCellRxlevAvg;
                abnormalDataInfo.AbnormalGridCellInfo.Add(abnormalCellInfo);
            }
            abnormalDataList.Add(abnormalDataInfo);
        }

        private List<QueryCondUnit> parseChangePeriodsOfRegion(TimePeriod period)
        {
            List<QueryCondUnit> condPeriodList = new List<QueryCondUnit>();
            double ltlong = 0, ltlat = 0, brlong = 0, brlat = 0; //定义左上右下经纬度

            QueryCondUnit cond = new QueryCondUnit();
            TimePeriod tpseg = new TimePeriod();
            DbRect dbRect = condition.Geometorys.RegionBounds;
            ltlong = Math.Round(dbRect.x1, 4);
            ltlat = Math.Round(dbRect.y2, 4);

            brlong = dbRect.x2;
            brlat = dbRect.y1;

            tpseg.SetPeriod(period.BeginTime, period.EndTime);
            cond.period = tpseg;
            cond.ltLongitude = ltlong;
            cond.ltLatitude = ltlat;
            cond.brLongitude = brlong;
            cond.brLatitude = brlat;
            condPeriodList.Add(cond);

            return condPeriodList;
        }

        
        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            //GridMatrix<GridCellUnit> gridCellMatrix = paramSet[0] as GridMatrix<GridCellUnit>;
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            headerManager = new DTDataHeaderManager();
            headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = "正在从服务器接收数据...";
                }
                recved = true;
                KPIStatDataBase singleStatData = null;
                try
                {
                    if (isFileHeaderContentType(package.Content.Type))
                    {
                        DTDataHeader header = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                        headerManager.AddDTDataHeader(header);
                    }
                    else if (isImgColDefContent(package, curImgColumnDef))
                    {
                        //
                    }
                    else if (isKPIDataContent(package, out singleStatData))
                    {
                        fillData(package, curImgColumnDef, singleStatData);
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        MessageBox.Show("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    setPercent(ref counter, ref curPercent);
                }
                catch (Exception ex)
                {
                    log.Error(ex.StackTrace);
                }
            }
        }

        private void fillData(Package package, List<StatImgDefItem> curImgColumnDef, 
            KPIStatDataBase singleStatData)
        {
            int lac = package.Content.GetParamInt();
            int ci = package.Content.GetParamInt();

            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            GridCellUnit gridCellUnit = null;
            GridInfo gridInfo = null;
            Dictionary<GridInfo, int> curDicGridInfo = null;
            fillStatData(package, curImgColumnDef, singleStatData);

            gridCellUnit = new GridCellUnit();
            gridCellUnit.LTLng = lng;
            gridCellUnit.LTLat = lat;
            int rAt, cAt;
            GridHelper.GetIndexOfDefaultSizeGrid(gridCellUnit.CenterLng, gridCellUnit.CenterLat, out rAt, out cAt);
            gridCellUnit = gridCellMatrix[rAt, cAt];
            if (gridCellUnit == null)
            {
                gridCellUnit = new GridCellUnit();
                gridCellUnit.LTLng = lng;
                gridCellUnit.LTLat = lat;
                gridCellMatrix[rAt, cAt] = gridCellUnit;
            }
            gridCellUnit.Status = 1;

            if (!dicGridFile.TryGetValue(singleStatData.FileID, out curDicGridInfo))
            {
                curDicGridInfo = new Dictionary<GridInfo, int>();
                dicGridFile[singleStatData.FileID] = curDicGridInfo;
            }
            gridInfo = new GridInfo();
            gridInfo.Itllongitude = gridCellUnit.LTLng;
            gridInfo.Itllatitude = gridCellUnit.LTLat;
            gridInfo.Ibrlongitude = gridCellUnit.BRLng;
            gridInfo.Ibrlatitude = gridCellUnit.BRLat;

            if (!curDicGridInfo.ContainsKey(gridInfo))
            {
                curDicGridInfo[gridInfo] = 1;
            }

            Cell cell = CellManager.GetInstance().GetCurrentCell(lac, ci);
            if (cell != null)
            {
                StatDataHubBase statDataHubBase = new StatDataHubBase();
                statDataHubBase.AddStatData(singleStatData, false);
                float gsmRxlev = (float)statDataHubBase.CalcValueByFormula(strFormulaDic["GSM栅格小区场强"]);
                int gsmSampleNum = (int)statDataHubBase.CalcValueByFormula(strFormulaDic["GSM栅格小区采样点数"]);
                if (!float.IsNaN(gsmRxlev))
                {
                    GridCellData gridCellData = new GridCellData();
                    gridCellData.Cell = cell;
                    gridCellData.IFileID = singleStatData.FileID;
                    gridCellData.Rxlev = gsmRxlev;
                    gridCellData.SampleNum = gsmSampleNum;

                    gridCellUnit.Initialize(gridCellData);
                }
            }
        }

        private void setPercent(ref int counter, ref int curPercent)
        {
            int tmp = (int)(Math.Log(counter++) * 10);
            if (tmp < 95 && tmp > 0 && curPercent != tmp)
            {
                WaitBox.ProgressPercent = tmp;
            }
            else if (tmp > 95)
            {
                curPercent = 5;
                counter = 0;
            }
        }

        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            if (reservedParams != null && reservedParams.Length > 0)
            {
                QueryCondUnit cond = reservedParams[0] as QueryCondUnit;
                package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
                package.Content.AddParam(cond.ltLongitude);
                package.Content.AddParam(cond.ltLatitude);
                package.Content.AddParam(cond.brLongitude);
                package.Content.AddParam(cond.brLatitude);
                AddDIYEndOpFlag(package);
            }
        }

        /// <summary>
        /// 通过ifileid在文件字典中获取文件名
        /// </summary>
        /// <param name="ifileid"></param>
        /// <returns></returns>
        private string GetiFileName(int ifileid)
        {
            DTDataHeader dtDataHeader = null;
            if (headerManager.GetHeaderMap().ContainsKey(ifileid))
            {
                headerManager.GetHeaderMap().TryGetValue(ifileid, out dtDataHeader);
                return dtDataHeader.Name;
            }
            else
            {
                return null;
            }
        }

        protected override void fireShowResult()
        {
            MainModel.RefreshLegend();
            MainModel.IsPrepareWithoutGridPartParam = false;
            showDataForm();
        }
        /// <summary>
        /// 显示结果集
        /// </summary>
        private void showDataForm()
        {
            DIYQueryAbnormalDataShowForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(DIYQueryAbnormalDataShowForm).FullName);
            showForm = obj == null ? null : obj as DIYQueryAbnormalDataShowForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new DIYQueryAbnormalDataShowForm(MainModel);
            }
            showForm.setData(abnormalDataList);
            showForm.Show(MainModel.MainForm);
        }

        Dictionary<string, string> strFormulaDic = null;
        private void setFormula()
        {
            strFormulaDic = new Dictionary<string, string>();
            strFormulaDic.Add("GSM栅格小区场强", "Mx_640102");
            strFormulaDic.Add("GSM栅格小区采样点数", "Mx_640101");
        }
    }
    public class QueryCondUnit
    {
        public TimePeriod period { get; set; }
        public double ltLongitude { get; set; }
        public double ltLatitude { get; set; }
        public double brLongitude { get; set; }
        public double brLatitude { get; set; }
    }

    public class GridCellData
    {
        public GridCellData()
        {
            Cell = new Cell();
        }
        
        public Cell Cell { get; set; }
        public int IFileID { get; set; }
        public double Lng { get; set; }
        public double Lat { get; set; }
        public float Rxlev { get; set; }
        public int SampleNum { get; set; }
    }

    public class GridInfo
    {
        public GridInfo()
        {
            hasCode = -1;
        }
        
        public double Itllongitude { get; set; }
        public double Itllatitude { get; set; }
        public double Ibrlongitude { get; set; }
        public double Ibrlatitude { get; set; }
        public int hasCode { get; set; }

        public override bool Equals(object obj)
        {
            GridInfo other = obj as GridInfo;

            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.Itllongitude.Equals(other.Itllongitude)
                    && (this.Itllatitude.Equals(other.Itllatitude))
                    && (this.Ibrlongitude.Equals(other.Ibrlongitude))
                    && (this.Ibrlatitude.Equals(other.Ibrlatitude)));
        }

        public override int GetHashCode()
        {
            if (hasCode == -1)
            {
                hasCode = (this.Itllongitude + "," + this.Itllatitude).GetHashCode();
            }
            return hasCode;
        }

    }

    public class AbnormalFileInfo
    {
        public AbnormalFileInfo()
        {
            AbnormalGridCellInfo = new List<AbnormalGridCellInfo>();
        }

        public int StriFileID { get; set; }
        public string StriFileName { get; set; }
        public int StrAbnormalGrid { get; set; }
        public int StrGridTotal { get; set; }
        public List<AbnormalGridCellInfo> AbnormalGridCellInfo { get; set; }
    }

    public class  AbnormalGridCellInfo
    {
        public double StrLng { get; set; }
        public double StrLat { get; set; }
        public string StrCellName { get; set; }
        public int StrLAC { get; set; }
        public int StrCI { get; set; }
        public float StrRxlev { get; set; }
        public float StrRxlevAvg { get; set; }
        public float StrSRxlev { get; set; }
    }
}
