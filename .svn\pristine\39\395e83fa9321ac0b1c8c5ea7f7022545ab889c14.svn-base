﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryWeakRoadBySample : DIYQueryFileInfo
    {
        public QueryWeakRoadBySample()
            : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "弱路段分析(按采样点)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22113, this.Name);
        }

        WeakRoadCond weakCond = new WeakRoadCond();
        List<WeakRoadInfo> weakInfoList = new List<WeakRoadInfo>();
        string strParam = "";

        protected bool getCondition()
        {
            GDWeakRoadSetForm conForm = new GDWeakRoadSetForm();
            if (conForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            weakCond = conForm.GetCond();
            if(weakCond.IsWeakCover)
                strParam = "lte_RSRP";
            else
                strParam = "lte_SINR";
            return true;
        }

        protected override void query()
        {
            if (!getCondition())
                return;
            weakInfoList = new List<WeakRoadInfo>();
            WaitBox.CanCancel = true;
            base.query();
            WaitBox.CanCancel = true;
            WaitBox.Show("开始分析文件...", analyseFiles);
            fireShowForm();
        }

        private void analyseFiles()
        {
            try
            {
                int iloop = 0;
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    WaitBox.Text = "正在分析文件(" + (++iloop) + "/" + MainModel.FileInfos.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / MainModel.FileInfos.Count);
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    QueryCondition condition = new QueryCondition();
                    condition.QueryType = Condition.QueryType;
                    condition.FileInfos.AddRange(Condition.FileInfos);
                    condition.Geometorys = Condition.Geometorys;

                    DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
                    query.FilterSampleByRegion = false;
                    query.FilterEventByRegion = false;
                    query.IncludeTestPoint = true;
                    query.IncludeEvent = false;
                    query.IncludeMessage = false;
                    query.SetQueryCondition(condition);
                    query.Query();
                    doStatWithQuery();
                    MainModel.ClearDTData();

                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        protected void doStatWithQuery()
        {
            foreach (DTFileDataManager dtFile in MainModel.DTDataManager.FileDataManagers)
            {
                int iCount = dtFile.TestPoints.Count;
                List<int> calIndex = new List<int>();
                Dictionary<long, int> timeSNDic = new Dictionary<long, int>();
                WeakRoadInfo weakInfo = new WeakRoadInfo();
                for (int i = 0; i < iCount; i++)
                {
                    try
                    {
                        TestPoint tp = dtFile.TestPoints[i];
                        float? param = getValidParam(tp);
                        if (param != null)
                        {
                            float paramValue = (float)param;
                            addWeakInfo(calIndex, timeSNDic, ref weakInfo, ref i, tp, paramValue);
                        }
                    }
                    catch
                    {
                        //continue
                    }
                }
            }
        }

        private float? getValidParam(TestPoint tp)
        {
            object obj = tp[strParam];
            if (obj == null)
            {
                return null;
            }
            float paramValue = float.Parse(obj.ToString());
            if (weakCond.IsWeakCover && (paramValue < -150 || paramValue > -25))
            {
                return null;
            }
            if (!weakCond.IsWeakCover && (paramValue < -50 || paramValue > 50))
            {
                return null;
            }
            return paramValue;
        }

        private void addWeakInfo(List<int> calIndex, Dictionary<long, int> timeSNDic, ref WeakRoadInfo weakInfo, ref int i, TestPoint tp, float paramValue)
        {
            if (!timeSNDic.ContainsKey(tp.Time))
            {
                timeSNDic[tp.lTimeWithMillsecond] = i;
            }
            if (weakInfo.TpParamList.Count < 10)
                weakInfo.AddPoint(tp, paramValue);
            else
            {
                weakInfo.CalWeakPercent(weakCond);
                if (weakInfo.DWeakPercent >= weakCond.DWeakPercent)
                {
                    weakInfo.ISN = weakInfoList.Count + 1;
                    weakInfo.ClearList();
                    weakInfoList.Add(weakInfo);
                }
                else
                {
                    i = timeSNDic[weakInfo.TpList[1].lTimeWithMillsecond];
                    if (calIndex.Contains(i))
                        i++;
                    else
                        calIndex.Add(i);
                }
                weakInfo.ClearList();
                weakInfo = new WeakRoadInfo();
            }
        }

        protected void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(GDWeakRoadInfoForm).FullName);
            GDWeakRoadInfoForm form = obj == null ? null : obj as GDWeakRoadInfoForm;
            if (form == null || form.IsDisposed)
            {
                form = new GDWeakRoadInfoForm(MainModel);
            }
            form.FillData(weakInfoList);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
    }
}
