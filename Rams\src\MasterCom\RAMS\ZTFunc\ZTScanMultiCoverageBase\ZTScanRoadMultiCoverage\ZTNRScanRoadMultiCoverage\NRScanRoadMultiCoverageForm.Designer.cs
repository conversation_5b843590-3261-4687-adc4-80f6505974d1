﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRScanRoadMultiCoverageForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle1 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel4 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel5 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel6 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle2 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram3 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series5 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel7 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series6 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel8 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel9 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle3 = new DevExpress.XtraCharts.ChartTitle();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.cbxEditRegion = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.edtWeakCoverThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.chkFilterWeakCover = new DevExpress.XtraEditors.CheckEdit();
            this.btnConvertToGrid = new DevExpress.XtraEditors.SimpleButton();
            this.labelControlType = new DevExpress.XtraEditors.LabelControl();
            this.btnColorRangeSetting = new DevExpress.XtraEditors.SimpleButton();
            this.radioGroupType = new DevExpress.XtraEditors.RadioGroup();
            this.splitContainerControl5 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitContainerControl3 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlRelative = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlRelative = new DevExpress.XtraCharts.ChartControl();
            this.splitContainerControl4 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlAbs = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlAbs = new DevExpress.XtraCharts.ChartControl();
            this.splitContainerControl6 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlRelORAbs = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlRelORAbs = new DevExpress.XtraCharts.ChartControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmiExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportMapInfo = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToMapInfoGrid = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToMapInfoWeakCoverGrid = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportSample = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxEditRegion.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtWeakCoverThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkFilterWeakCover.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).BeginInit();
            this.splitContainerControl5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).BeginInit();
            this.splitContainerControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRelative)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRelative)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).BeginInit();
            this.splitContainerControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl6)).BeginInit();
            this.splitContainerControl6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRelORAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRelORAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.panelControl1);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.AutoScroll = true;
            this.splitContainerControl2.Panel2.Controls.Add(this.splitContainerControl5);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(1126, 604);
            this.splitContainerControl2.SplitterPosition = 77;
            this.splitContainerControl2.TabIndex = 20;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.groupControl1);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(1126, 77);
            this.panelControl1.TabIndex = 0;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.cbxEditRegion);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Controls.Add(this.edtWeakCoverThreshold);
            this.groupControl1.Controls.Add(this.chkFilterWeakCover);
            this.groupControl1.Controls.Add(this.btnConvertToGrid);
            this.groupControl1.Controls.Add(this.labelControlType);
            this.groupControl1.Controls.Add(this.btnColorRangeSetting);
            this.groupControl1.Controls.Add(this.radioGroupType);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(2, 2);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1122, 73);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "GIS显示设置";
            // 
            // cbxEditRegion
            // 
            this.cbxEditRegion.Location = new System.Drawing.Point(595, 31);
            this.cbxEditRegion.Name = "cbxEditRegion";
            this.cbxEditRegion.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxEditRegion.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxEditRegion.Size = new System.Drawing.Size(100, 21);
            this.cbxEditRegion.TabIndex = 8;
            this.cbxEditRegion.SelectedIndexChanged += new System.EventHandler(this.cbxEditRegion_SelectedIndexChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(913, 34);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(24, 14);
            this.labelControl1.TabIndex = 7;
            this.labelControl1.Text = "dBm";
            // 
            // edtWeakCoverThreshold
            // 
            this.edtWeakCoverThreshold.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            this.edtWeakCoverThreshold.Location = new System.Drawing.Point(860, 31);
            this.edtWeakCoverThreshold.Name = "edtWeakCoverThreshold";
            this.edtWeakCoverThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtWeakCoverThreshold.Properties.IsFloatValue = false;
            this.edtWeakCoverThreshold.Properties.Mask.EditMask = "N00";
            this.edtWeakCoverThreshold.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.edtWeakCoverThreshold.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.edtWeakCoverThreshold.Size = new System.Drawing.Size(47, 21);
            this.edtWeakCoverThreshold.TabIndex = 6;
            // 
            // chkFilterWeakCover
            // 
            this.chkFilterWeakCover.Location = new System.Drawing.Point(718, 31);
            this.chkFilterWeakCover.Name = "chkFilterWeakCover";
            this.chkFilterWeakCover.Properties.Caption = "过滤弱覆盖栅格门限：";
            this.chkFilterWeakCover.Size = new System.Drawing.Size(136, 19);
            this.chkFilterWeakCover.TabIndex = 5;
            this.chkFilterWeakCover.CheckedChanged += new System.EventHandler(this.chkFilterWeakCover_CheckedChanged);
            // 
            // btnConvertToGrid
            // 
            this.btnConvertToGrid.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnConvertToGrid.Location = new System.Drawing.Point(949, 30);
            this.btnConvertToGrid.Name = "btnConvertToGrid";
            this.btnConvertToGrid.Size = new System.Drawing.Size(75, 23);
            this.btnConvertToGrid.TabIndex = 4;
            this.btnConvertToGrid.Text = "栅格化处理";
            this.btnConvertToGrid.Click += new System.EventHandler(this.btnConvertToGrid_Click);
            // 
            // labelControlType
            // 
            this.labelControlType.Location = new System.Drawing.Point(13, 34);
            this.labelControlType.Name = "labelControlType";
            this.labelControlType.Size = new System.Drawing.Size(72, 14);
            this.labelControlType.TabIndex = 2;
            this.labelControlType.Text = "覆盖度类别：";
            // 
            // btnColorRangeSetting
            // 
            this.btnColorRangeSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnColorRangeSetting.Location = new System.Drawing.Point(1030, 30);
            this.btnColorRangeSetting.Name = "btnColorRangeSetting";
            this.btnColorRangeSetting.Size = new System.Drawing.Size(87, 23);
            this.btnColorRangeSetting.TabIndex = 1;
            this.btnColorRangeSetting.Text = "着色设置...";
            this.btnColorRangeSetting.Click += new System.EventHandler(this.btnColorRangeSetting_Click);
            // 
            // radioGroupType
            // 
            this.radioGroupType.EditValue = true;
            this.radioGroupType.Location = new System.Drawing.Point(91, 26);
            this.radioGroupType.Name = "radioGroupType";
            this.radioGroupType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "相对覆盖度(-12dBm内)"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "绝对覆盖度( > -80dBm)"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "综合覆盖度")});
            this.radioGroupType.Size = new System.Drawing.Size(480, 30);
            this.radioGroupType.TabIndex = 0;
            this.radioGroupType.SelectedIndexChanged += new System.EventHandler(this.radioGroupType_SelectedIndexChanged);
            // 
            // splitContainerControl5
            // 
            this.splitContainerControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl5.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl5.Name = "splitContainerControl5";
            this.splitContainerControl5.Panel1.Controls.Add(this.splitContainerControl1);
            this.splitContainerControl5.Panel1.Text = "Panel1";
            this.splitContainerControl5.Panel2.Controls.Add(this.splitContainerControl6);
            this.splitContainerControl5.Panel2.Text = "Panel2";
            this.splitContainerControl5.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.splitContainerControl5.Size = new System.Drawing.Size(1126, 521);
            this.splitContainerControl5.SplitterPosition = 750;
            this.splitContainerControl5.TabIndex = 3;
            this.splitContainerControl5.Text = "splitContainerControl5";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.splitContainerControl3);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl4);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(750, 521);
            this.splitContainerControl1.SplitterPosition = 372;
            this.splitContainerControl1.TabIndex = 2;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // splitContainerControl3
            // 
            this.splitContainerControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl3.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl3.Horizontal = false;
            this.splitContainerControl3.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl3.Name = "splitContainerControl3";
            this.splitContainerControl3.Panel1.Controls.Add(this.gridControlRelative);
            this.splitContainerControl3.Panel1.Text = "Panel1";
            this.splitContainerControl3.Panel2.Controls.Add(this.chartControlRelative);
            this.splitContainerControl3.Panel2.Text = "Panel2";
            this.splitContainerControl3.Size = new System.Drawing.Size(372, 521);
            this.splitContainerControl3.SplitterPosition = 264;
            this.splitContainerControl3.TabIndex = 0;
            this.splitContainerControl3.Text = "splitContainerControl3";
            // 
            // gridControlRelative
            // 
            this.gridControlRelative.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRelative.Location = new System.Drawing.Point(0, 0);
            this.gridControlRelative.MainView = this.gridView1;
            this.gridControlRelative.Name = "gridControlRelative";
            this.gridControlRelative.Size = new System.Drawing.Size(372, 264);
            this.gridControlRelative.TabIndex = 0;
            this.gridControlRelative.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.GridControl = this.gridControlRelative;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlRelative
            // 
            xyDiagram1.AxisX.Label.Staggered = true;
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlRelative.Diagram = xyDiagram1;
            this.chartControlRelative.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlRelative.Legend.Visible = false;
            this.chartControlRelative.Location = new System.Drawing.Point(0, 0);
            this.chartControlRelative.Name = "chartControlRelative";
            sideBySideBarSeriesLabel1.LineVisible = true;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 1";
            sideBySideBarSeriesLabel2.LineVisible = true;
            series2.Label = sideBySideBarSeriesLabel2;
            series2.Name = "Series 2";
            this.chartControlRelative.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1,
        series2};
            sideBySideBarSeriesLabel3.LineVisible = true;
            this.chartControlRelative.SeriesTemplate.Label = sideBySideBarSeriesLabel3;
            this.chartControlRelative.Size = new System.Drawing.Size(372, 251);
            this.chartControlRelative.TabIndex = 3;
            chartTitle1.Font = new System.Drawing.Font("Microsoft Sans Serif", 15F);
            chartTitle1.Text = "相对重叠覆盖度";
            this.chartControlRelative.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle1});
            // 
            // splitContainerControl4
            // 
            this.splitContainerControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl4.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl4.Horizontal = false;
            this.splitContainerControl4.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl4.Name = "splitContainerControl4";
            this.splitContainerControl4.Panel1.Controls.Add(this.gridControlAbs);
            this.splitContainerControl4.Panel1.Text = "Panel1";
            this.splitContainerControl4.Panel2.Controls.Add(this.chartControlAbs);
            this.splitContainerControl4.Panel2.Text = "Panel2";
            this.splitContainerControl4.Size = new System.Drawing.Size(372, 521);
            this.splitContainerControl4.SplitterPosition = 264;
            this.splitContainerControl4.TabIndex = 1;
            this.splitContainerControl4.Text = "splitContainerControl4";
            // 
            // gridControlAbs
            // 
            this.gridControlAbs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlAbs.Location = new System.Drawing.Point(0, 0);
            this.gridControlAbs.MainView = this.gridView2;
            this.gridControlAbs.Name = "gridControlAbs";
            this.gridControlAbs.Size = new System.Drawing.Size(372, 264);
            this.gridControlAbs.TabIndex = 0;
            this.gridControlAbs.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.gridControlAbs;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlAbs
            // 
            xyDiagram2.AxisX.Label.Staggered = true;
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlAbs.Diagram = xyDiagram2;
            this.chartControlAbs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlAbs.Legend.Visible = false;
            this.chartControlAbs.Location = new System.Drawing.Point(0, 0);
            this.chartControlAbs.Name = "chartControlAbs";
            sideBySideBarSeriesLabel4.LineVisible = true;
            series3.Label = sideBySideBarSeriesLabel4;
            series3.Name = "Series 1";
            sideBySideBarSeriesLabel5.LineVisible = true;
            series4.Label = sideBySideBarSeriesLabel5;
            series4.Name = "Series 2";
            this.chartControlAbs.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series3,
        series4};
            sideBySideBarSeriesLabel6.LineVisible = true;
            this.chartControlAbs.SeriesTemplate.Label = sideBySideBarSeriesLabel6;
            this.chartControlAbs.Size = new System.Drawing.Size(372, 251);
            this.chartControlAbs.TabIndex = 3;
            chartTitle2.Font = new System.Drawing.Font("Tahoma", 15F);
            chartTitle2.Text = "绝对重叠覆盖度";
            this.chartControlAbs.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle2});
            // 
            // splitContainerControl6
            // 
            this.splitContainerControl6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl6.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl6.Horizontal = false;
            this.splitContainerControl6.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl6.Name = "splitContainerControl6";
            this.splitContainerControl6.Panel1.Controls.Add(this.gridControlRelORAbs);
            this.splitContainerControl6.Panel1.Text = "Panel1";
            this.splitContainerControl6.Panel2.Controls.Add(this.chartControlRelORAbs);
            this.splitContainerControl6.Panel2.Text = "Panel2";
            this.splitContainerControl6.Size = new System.Drawing.Size(370, 521);
            this.splitContainerControl6.SplitterPosition = 264;
            this.splitContainerControl6.TabIndex = 0;
            this.splitContainerControl6.Text = "splitContainerControl6";
            // 
            // gridControlRelORAbs
            // 
            this.gridControlRelORAbs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRelORAbs.Location = new System.Drawing.Point(0, 0);
            this.gridControlRelORAbs.MainView = this.gridView3;
            this.gridControlRelORAbs.Name = "gridControlRelORAbs";
            this.gridControlRelORAbs.Size = new System.Drawing.Size(370, 264);
            this.gridControlRelORAbs.TabIndex = 1;
            this.gridControlRelORAbs.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // gridView3
            // 
            this.gridView3.GridControl = this.gridControlRelORAbs;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlRelORAbs
            // 
            xyDiagram3.AxisX.Label.Staggered = true;
            xyDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram3.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlRelORAbs.Diagram = xyDiagram3;
            this.chartControlRelORAbs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlRelORAbs.Legend.Visible = false;
            this.chartControlRelORAbs.Location = new System.Drawing.Point(0, 0);
            this.chartControlRelORAbs.Name = "chartControlRelORAbs";
            sideBySideBarSeriesLabel7.LineVisible = true;
            series5.Label = sideBySideBarSeriesLabel7;
            series5.Name = "Series 1";
            sideBySideBarSeriesLabel8.LineVisible = true;
            series6.Label = sideBySideBarSeriesLabel8;
            series6.Name = "Series 2";
            this.chartControlRelORAbs.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series5,
        series6};
            sideBySideBarSeriesLabel9.LineVisible = true;
            this.chartControlRelORAbs.SeriesTemplate.Label = sideBySideBarSeriesLabel9;
            this.chartControlRelORAbs.Size = new System.Drawing.Size(370, 251);
            this.chartControlRelORAbs.TabIndex = 4;
            chartTitle3.Font = new System.Drawing.Font("Tahoma", 15F);
            chartTitle3.Text = "综合重叠覆盖度";
            this.chartControlRelORAbs.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle3});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiExport2Xls,
            this.miExportMapInfo,
            this.miExportToMapInfoGrid,
            this.miExportToMapInfoWeakCoverGrid,
            this.miExportSample});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(239, 114);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // tsmiExport2Xls
            // 
            this.tsmiExport2Xls.Name = "tsmiExport2Xls";
            this.tsmiExport2Xls.Size = new System.Drawing.Size(238, 22);
            this.tsmiExport2Xls.Text = "导出Excel...";
            this.tsmiExport2Xls.Click += new System.EventHandler(this.tsmiExport2Xls_Click);
            // 
            // miExportMapInfo
            // 
            this.miExportMapInfo.Name = "miExportMapInfo";
            this.miExportMapInfo.Size = new System.Drawing.Size(238, 22);
            this.miExportMapInfo.Text = "导出到shp图层...";
            this.miExportMapInfo.Click += new System.EventHandler(this.miExportMapInfo_Click);
            // 
            // miExportToMapInfoGrid
            // 
            this.miExportToMapInfoGrid.Name = "miExportToMapInfoGrid";
            this.miExportToMapInfoGrid.Size = new System.Drawing.Size(238, 22);
            this.miExportToMapInfoGrid.Text = "导出重叠覆盖栅格到shp图层...";
            this.miExportToMapInfoGrid.Click += new System.EventHandler(this.miExportToMapInfoGrid_Click);
            // 
            // miExportToMapInfoWeakCoverGrid
            // 
            this.miExportToMapInfoWeakCoverGrid.Name = "miExportToMapInfoWeakCoverGrid";
            this.miExportToMapInfoWeakCoverGrid.Size = new System.Drawing.Size(238, 22);
            this.miExportToMapInfoWeakCoverGrid.Text = "导出弱覆盖栅格到shp图层...";
            this.miExportToMapInfoWeakCoverGrid.Click += new System.EventHandler(this.miExportToMapInfoWeakCoverGrid_Click);
            // 
            // miExportSample
            // 
            this.miExportSample.Name = "miExportSample";
            this.miExportSample.Size = new System.Drawing.Size(238, 22);
            this.miExportSample.Text = "导出采样点到Excel...";
            this.miExportSample.Click += new System.EventHandler(this.miExportSample_Click);
            // 
            // NRScanRoadMultiCoverageForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1126, 604);
            this.ContextMenuStrip = this.contextMenuStrip;
            this.Controls.Add(this.splitContainerControl2);
            this.Name = "NRScanRoadMultiCoverageForm";
            this.Text = "道路重叠覆盖度";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxEditRegion.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtWeakCoverThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkFilterWeakCover.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).EndInit();
            this.splitContainerControl5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).EndInit();
            this.splitContainerControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRelative)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRelative)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).EndInit();
            this.splitContainerControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlAbs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlAbs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl6)).EndInit();
            this.splitContainerControl6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRelORAbs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRelORAbs)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.ComboBoxEdit cbxEditRegion;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit edtWeakCoverThreshold;
        private DevExpress.XtraEditors.CheckEdit chkFilterWeakCover;
        private DevExpress.XtraEditors.SimpleButton btnConvertToGrid;
        private DevExpress.XtraEditors.LabelControl labelControlType;
        private DevExpress.XtraEditors.SimpleButton btnColorRangeSetting;
        private DevExpress.XtraEditors.RadioGroup radioGroupType;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl5;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl3;
        private DevExpress.XtraGrid.GridControl gridControlRelative;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraCharts.ChartControl chartControlRelative;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl4;
        private DevExpress.XtraGrid.GridControl gridControlAbs;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraCharts.ChartControl chartControlAbs;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl6;
        private DevExpress.XtraGrid.GridControl gridControlRelORAbs;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraCharts.ChartControl chartControlRelORAbs;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem tsmiExport2Xls;
        private System.Windows.Forms.ToolStripMenuItem miExportMapInfo;
        private System.Windows.Forms.ToolStripMenuItem miExportToMapInfoGrid;
        private System.Windows.Forms.ToolStripMenuItem miExportToMapInfoWeakCoverGrid;
        private System.Windows.Forms.ToolStripMenuItem miExportSample;
    }
}