﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptInfo_HB : CellAcceptInfo_HB
    {
        public StationAcceptInfo_HB()
            : base()
        {
            CellAcceptInfoDic = new Dictionary<int, CellAcceptInfo_HB>();
        }
        public bool IsFddBts { get; set; }
        public override bool? IsAntennaOpposite { get; set; }
        public override bool IsPassAccept { get; set; }
        public override bool IsCoverRate1Valid
        {
            get
            {
                if (TpCountOfValidDlRsrpAndSinr > 0)
                {
                    return ((double)CoverRate1Count / TpCountOfValidDlRsrpAndSinr) > 0.95;
                }
                return false;
            }
        }
        public override bool IsPdcpDLAvgValid
        {
            get
            {
                return this.PdcpDLAvg > 30;
            }
        }
        public override bool IsPdcpULAvgValid
        {
            get
            {
                return this.PdcpULAvg > 6;
            }
        }
        public Dictionary<int, CellAcceptInfo_HB> CellAcceptInfoDic { get; set; }
        public CellAcceptInfo_HB CellCircleTestInfo { get; set; }
        public string StrErrorInfo { get; set; }
        public string StrNote { get; set; }

        private List<string> cellParamTacList { get; set; } = new List<string>();
        private List<string> cellParamArfcnList { get; set; } = new List<string>();
        private List<string> cellParamPciList { get; set; } = new List<string>();

        public void JudgeAcceptInfo(StationAcceptAutoSet funcSet)
        {
            this.IsCircleTestHasValidTp = true;
            this.IsAntennaOpposite = false;

            #region 将各小区指标和环测指标汇总到基站
            CallKpiCheck kpiCheck = this.CellParamInfo.IsOutDoor ? funcSet.OutDoorCallCheck : funcSet.InDoorCallCheck;

            bool isAllCellPassAccept = true;
            foreach (var cellAcceptInfo in this.CellAcceptInfoDic)
            {
                CellAcceptInfo_HB info = cellAcceptInfo.Value;
                info.SetCheckConditon(kpiCheck);//设置各小区指标达标的条件

                this.cellParamTacList.Add(info.CellParamInfo.Tac.ToString());
                this.cellParamArfcnList.Add(info.CellParamInfo.ARFCN.ToString());
                this.cellParamPciList.Add(info.CellParamInfo.PCI.ToString());

                if (info.CellParamInfo.IsOutDoor && !info.IsPassAccept)
                {
                    isAllCellPassAccept = false;//检查各小区是否通过验收
                }
                this.addCellAcceptInfo(info);//汇总到基站
            }

            if (this.CellCircleTestInfo != null)
            {
                this.CellCircleTestInfo.SetCheckConditon(kpiCheck);//设置环测指标达标的条件
                this.addCellAcceptInfo(this.CellCircleTestInfo);//将环测指标汇总到基站
            }
            this.SetCheckConditon(kpiCheck);//设置基站指标达标的条件
            #endregion

            #region 单站验收是否通过
            if (this.CellParamInfo.IsOutDoor)
            {
                bool isCirClePassAccept = true;
                if (this.CellCircleTestInfo != null)
                {
                    isCirClePassAccept = this.isKpiRatePassAccept(this);
                }
                else
                {
                    isCirClePassAccept = false;
                }

                this.IsPassAccept = isAllCellPassAccept && isCirClePassAccept && this.IsCircleTestHasValidTp 
                    && (this.IsAntennaOpposite == false);
            }
            else
            {
                this.IsPassAccept = this.isKpiRatePassAccept(this) && this.IsCoverRate1Valid 
                    && this.IsPdcpDLAvgValid && this.IsPdcpULAvgValid;
            }
            #endregion

            this.StrErrorInfo += this.getAcceptErrorInfo();
        }

        private void addCellAcceptInfo(CellAcceptInfo_HB info)
        {
            this.TpListOfValidDlRsrp.AddRange(info.TpListOfValidDlRsrp);
            this.TpCountOfValidDlRsrpAndSinr += info.TpCountOfValidDlRsrpAndSinr;
            this.CoverRate1Count += info.CoverRate1Count;
            this.CoverRate2Count += info.CoverRate2Count;
            this.CoverRate3Count += info.CoverRate3Count;

            this.rsrpCount += info.rsrpCount;
            this.rsrpSum += info.rsrpSum;
            this.RsrpMax = this.RsrpMax > info.RsrpMax ? this.RsrpMax : info.RsrpMax;
            this.sinrCount += info.sinrCount;
            this.sinrSum += info.sinrSum;
            this.SinrMax = this.SinrMax > info.SinrMax ? this.SinrMax : info.SinrMax;

            this.PdcpDLCount += info.PdcpDLCount;
            this.PdcpDLSum += info.PdcpDLSum;
            this.PdcpDLMax = this.PdcpDLMax > info.PdcpDLMax ? this.PdcpDLMax : info.PdcpDLMax;
            this.PdcpULCount += info.PdcpULCount;
            this.PdcpULSum += info.PdcpULSum;
            this.PdcpULMax = this.PdcpULMax > info.PdcpULMax ? this.PdcpULMax : info.PdcpULMax;

            this.CsfbCallInfo.Merge(info.CsfbCallInfo);
            this.VolteVoiceCallInfo.Merge(info.VolteVoiceCallInfo);
            this.VolteVideoCallInfo.Merge(info.VolteVideoCallInfo);
            this.RatioInfoWeakRsrp.Merge(info.RatioInfoWeakRsrp);
            this.RatioInfoWeakSinr.Merge(info.RatioInfoWeakSinr);
            this.RatioInfoLowDl.Merge(info.RatioInfoLowDl);
            this.RatioInfoLowUl.Merge(info.RatioInfoLowUl);
            this.RatioInfoWx.Merge(info.RatioInfoWx);
            this.RatioInfoHandover.Merge(info.RatioInfoHandover);
            this.RatioInfoSRVCC.Merge(info.RatioInfoSRVCC);

            if (info.HasHandoverOutToIn)
            {
                this.HasHandoverOutToIn = true;
            }
            if (info.HasHandoverInToOut)
            {
                this.HasHandoverInToOut = true;
            }
            this.HandOverCellInfos.AddRange(info.HandOverCellInfos);

            if (info.HasFoundFile)
            {
                this.HasFoundFile = true;
            }
            if (info.HasFoundValidSingleTestPosition)
            {
                this.HasFoundValidSingleTestPosition = true;
            }

            judgeCircleTest(info);

            this.TestTimeList.AddRange(info.TestTimeList);
            this.EnodeBidList.AddRange(info.EnodeBidList);
            this.TacList.AddRange(info.TacList);
            this.PciList.AddRange(info.PciList);
            this.EarfcnList.AddRange(info.EarfcnList);
            this.ErrorInfoList.AddRange(info.ErrorInfoList);
        }

        private void judgeCircleTest(CellAcceptInfo_HB info)
        {
            if (!info.IsCircleTest)
            {
                if (!info.IsCircleTestHasValidTp)//单个小区中有一个未通过，即视为未通过
                {
                    this.IsCircleTestHasValidTp = false;
                }

                if (this.CellParamInfo.IsOutDoor && this.CellAcceptInfoDic.Count == 1)
                {//覆盖类型：室外；且只有一个小区的单站验证报告中，不判决天线是否接反
                    this.IsAntennaOpposite = false;
                }
                else
                {
                    if (info.IsAntennaOpposite == true)//有一个小区接反，即视本站为接反
                    {
                        this.IsAntennaOpposite = true;
                    }
                    else if (info.IsAntennaOpposite == null && this.IsAntennaOpposite != true)
                    {//有一个小区未测到且无接反的小区，即视本站为未测到
                        this.IsAntennaOpposite = null;
                    }
                }
            }
        }

        private bool isKpiRatePassAccept(CellAcceptInfo_HB acceptInfo)
        {
            if (this.CellParamInfo.IsOutDoor && this.CellAcceptInfoDic.Count == 1)
            {//室外；且只有一个小区的单站验证报告中，不判决切换成功率
                acceptInfo.IsValidHandOverSucessRatio = this.IsValidHandOverSucessRatio = true;
            }

            return acceptInfo.CsfbCallInfo.IsValidSucessRatioAllCall && acceptInfo.RatioInfoSRVCC.IsValidRatio
                && acceptInfo.VolteVoiceCallInfo.IsValidSucessRatioAllCall && acceptInfo.VolteVoiceCallInfo.IsValidSucessRatioMoCall
                && acceptInfo.VolteVideoCallInfo.IsValidSucessRatioAllCall && acceptInfo.VolteVideoCallInfo.IsValidSucessRatioMoCall 
                && acceptInfo.RatioInfoWeakRsrp.IsValidRatio && acceptInfo.RatioInfoWeakSinr.IsValidRatio //室外站指标
                && acceptInfo.RatioInfoLowDl.IsValidRatio && acceptInfo.RatioInfoLowUl.IsValidRatio //室外站指标
                && acceptInfo.RatioInfoWx.IsValidRatio && acceptInfo.IsValidInAndOutSrc //室内站指标
                && acceptInfo.IsValidHandOverSucessRatio;
        }
        private string getAcceptErrorInfo()
        {
            if (this.IsPassAccept)
            {
                return "已验收通过";
            }

            StringBuilder strb = new StringBuilder();
            if (!this.HasFoundFile)
            {
                if (this.CellParamInfo.IsOutDoor)
                {
                    return "室外站未找到文件;";
                }
                else
                {
                    strb.AppendLine("室内站未找到楼层测试文件;");
                }
            }
            strb.AppendLine("该站KPI指标不达标;");

            if (!this.RatioInfoHandover.HasFoundValidFile)
            {
                strb.AppendLine("无LTE数据或空闲业务含DL的log;");
            }
            if (!this.CsfbCallInfo.HasFoundValidFile)
            {
                strb.AppendLine("无CSFB语音业务log;");
            }
            if (!this.VolteVoiceCallInfo.HasFoundValidFile)
            {
                strb.AppendLine("无VoLTE语音业务log;");
            }
            if (!this.VolteVideoCallInfo.HasFoundValidFile)
            {
                strb.AppendLine("无VoLTE视频业务log;");
            }
            if (!this.CellParamInfo.IsOutDoor && !this.RatioInfoWx.HasFoundValidFile)
            {
                strb.AppendLine("无室分外泄测试log;");
            }

            foreach (string str in this.ErrorInfoList)
            {
                strb.AppendLine(str + ";");
            }
            return strb.ToString();
        }
    }
    
    public class CellAcceptInfo_HB : AcceptInfoBase_HB
    {
        public CellAcceptInfo_HB()
        {
            TestTimeList = new List<DateTime>();
            TestPositionAcceptInfoDic = new Dictionary<string, AcceptInfoBase_HB>();
        }
        public CellAcceptInfo_HB(CellWorkParam cellParamInfo, int fileNameEnodType)
            : base()
        {
            this.CellParamInfo = cellParamInfo;
            FileNameEnodType = fileNameEnodType;
            TestTimeList = new List<DateTime>();
            TestPositionAcceptInfoDic = new Dictionary<string, AcceptInfoBase_HB>();
        }

        public Dictionary<string, AcceptInfoBase_HB> TestPositionAcceptInfoDic { get; set; }
        public CellWorkParam CellParamInfo { get; set; }

        private string testTimeDes = "";
        public string TestTimeDes 
        {
            get
            {
                foreach (DateTime time in TestTimeList)
                {
                    if (time == DateTime.MinValue)
                    {
                        continue;
                    }
                    string curTestDate = time.ToShortDateString();
                    StringBuilder sb = new StringBuilder(testTimeDes);
                    if (!this.testTimeDes.Contains(curTestDate))
                    {
                        if (this.testTimeDes == "")
                        {
                            this.testTimeDes = curTestDate;
                        }
                        else
                        {
                            sb.Append(" | " + curTestDate);
                            this.testTimeDes = sb.ToString();
                        }
                    }
                }
                return testTimeDes;
            }
        }

        public List<DateTime> TestTimeList { get; set; }
    
        /// <summary>
        /// 文件名中ENodeBID进制： 0位10进制，1位16进制
        /// </summary>
        public int FileNameEnodType { get; set; }

        public string StrENodeBID
        {
            get
            {
                string strEnod = "";
                if (FileNameEnodType == 1)
                {
                    strEnod = Convert.ToString(this.CellParamInfo.ENodeBID, 16).ToUpper();
                }
                else
                {
                    strEnod = this.CellParamInfo.ENodeBID.ToString();
                }
                return strEnod;
            }
        }
        public bool IsCircleTest { get; set; }

        /// <summary>
        /// 环测是否有合格采样点
        /// </summary>
        public bool IsCircleTestHasValidTp { get; set; }

        public virtual bool IsPassAccept
        {
            get
            {
                return HasUlGoodTp && HasDlGoodTp && HasUlCommonTp && HasDlCommonTp && HasUlBadTp && HasDlBadTp;
            }
            set
            {
#if DEBUG
                Console.Write(value);
#endif
            }
        }
        public bool HasUlGoodTp { get; set; }
        public bool HasUlCommonTp { get; set; }
        public bool HasUlBadTp { get; set; }
        public bool HasDlGoodTp { get; set; }
        public bool HasDlCommonTp { get; set; }
        public bool HasDlBadTp { get; set; }

        /// <summary>
        /// 天线是否接反
        /// </summary>
        public virtual bool? IsAntennaOpposite
        {
            get
            {
                if (TpCountSrcSum > 0)
                {
                    return ((double)TpCountAntennaOpposite / TpCountSrcSum) > 0.7;
                }
                return null;
            }
            set
            {
#if DEBUG
                Console.Write(value);
#endif
            }
        }

        public int TpCountSrcSum { get; set; }
        public int TpCountAntennaOpposite { get; set; }

        public bool HasHandoverOutToIn { get; set; }
        public bool HasHandoverInToOut { get; set; }
        public bool HasInAndOutSrc
        {
            get
            {
                return HasHandoverOutToIn && HasHandoverInToOut;
            }
        }

        public List<string> HandOverCellInfos { get; set; } = new List<string>();
        public string HandOverCellInfosDes
        {
            get
            {
                StringBuilder strb = new StringBuilder();
                foreach (string str in this.HandOverCellInfos)
                {
                    strb.AppendLine(str + ";");
                }
                return strb.ToString();
            }
        }
        public void AddAndRecordHandOverEvents(List<Event> evts)
        {
            this.RatioInfoHandover.HasFoundValidFile = true;

            foreach (Event evt in evts)
            {
                switch (evt.ID)
                {
                    case 850:
                    case 898:
                    case 3155://fdd
                    case 3158:
                        this.RatioInfoHandover.CountDenominator++;  //HandOver Request
                        break;
                    case 851:
                    case 899:
                    case 3156://fdd
                    case 3159:
                        this.RatioInfoHandover.CountNumerator++;    //HandOver Success

                        bool? isSrcOutDoor;
                        bool? isTargetOutDoor;
                        string strHandOverCellInfo = string.Format("{0}切换到{1}"
                            , getCellDesByTacEci((int?)evt["LAC"], (int?)evt["CI"], evt, out isSrcOutDoor)
                            , getCellDesByTacEci((int?)evt["TargetLAC"], (int?)evt["TargetCI"], evt, out isTargetOutDoor));

                        if (isSrcOutDoor == true && isTargetOutDoor == false)
                        {
                            HasHandoverOutToIn = true;
                        }
                        else if (isSrcOutDoor == false && isTargetOutDoor == true)
                        {
                            HasHandoverInToOut = true;
                        }

                        if (!HandOverCellInfos.Contains(strHandOverCellInfo))
                        {
                            HandOverCellInfos.Add(strHandOverCellInfo);
                        }
                        break;
                }
            }
        }
        private string getCellDesByTacEci(int? tac, int? eci, Event evt, out bool? isOutDoor)
        {
            isOutDoor = null;
            if (tac == -1 && eci == -1)
            {
                return "未知小区";
            }

            CellManager mng = CellManager.GetInstance();
            LTECell lteCell = mng.GetNearestLTECellByTACCI(evt.DateTime, tac, eci, evt.Longitude, evt.Latitude);
            if (lteCell != null)
            {
                isOutDoor = (lteCell.Type == LTEBTSType.Outdoor);
                return string.Format("{0}“{1}”小区", lteCell.BelongBTS.TypeStringDesc, lteCell.Name);
            }
            else
            {
                Cell cell = mng.GetNearestCell(evt.DateTime, (ushort?)tac, (ushort?)eci, evt.Longitude, evt.Latitude);
                if (cell != null)
                {
                    isOutDoor = (cell.Type == BTSType.Outdoor);
                    return string.Format("{0}“{1}”小区", cell.BelongBTS.TypeDescription, cell.Name);
                }
            }

            return string.Format("{0}_{1}", tac != null ? tac.ToString() : string.Empty,
                        eci != null ? eci.ToString() : string.Empty);
        }

        public void AddCircleCallTp(TestPoint tp, bool isValidPositionTp)//仅室外站点绘制环测CSFB、Volte语音、Volte视频轨迹图用到
        {
            if (isValidPositionTp)
            {
                if (tp.ServiceType == (int)ServiceType.LTE_TDD_VOICE 
                    || tp.ServiceType == (int)ServiceType.LTE_FDD_VOICE)
                {
                    this.CsfbCallInfo.AddTpByMoMtFlag(tp);
                }
                else if (tp.ServiceType == (int)ServiceType.LTE_TDD_VOLTE 
                    || tp.ServiceType == (int)ServiceType.LTE_FDD_VOLTE)
                {
                    this.VolteVoiceCallInfo.AddTpByMoMtFlag(tp);
                }
                else if (tp.ServiceType == (int)ServiceType.SER_LTE_TDD_VIDEO_VOLTE
                    || tp.ServiceType == (int)ServiceType.SER_LTE_FDD_VIDEO_VOLTE)
                {
                    this.VolteVideoCallInfo.AddTpByMoMtFlag(tp);
                }
            }
        }

        public bool IsValidInAndOutSrc { get; set; } = true;

        public RatioInfo RatioInfoWx { get; set; } = new RatioInfo();
        public RatioInfo RatioInfoWeakRsrp { get; set; } = new RatioInfo();
        public RatioInfo RatioInfoWeakSinr { get; set; } = new RatioInfo();
        public RatioInfo RatioInfoLowDl { get; set; } = new RatioInfo();
        public RatioInfo RatioInfoLowUl { get; set; } = new RatioInfo();

        /// <summary>
        /// 设置指标通过的条件
        /// </summary>
        /// <param name="kpiCheck"></param>
        public void SetCheckConditon(CallKpiCheck kpiCheck)
        {
            this.CsfbCallInfo.IsCheckAllCall = kpiCheck.IsCheckCsfb;
            this.VolteVoiceCallInfo.IsCheckAllCall = kpiCheck.IsCheckVolteVoiceAllCall;
            this.VolteVoiceCallInfo.IsCheckMoCall = kpiCheck.IsCheckVolteVoiceMoCall;
            this.VolteVideoCallInfo.IsCheckAllCall = kpiCheck.IsCheckVolteVideoAllCall;
            this.VolteVideoCallInfo.IsCheckMoCall = kpiCheck.IsCheckVolteVideoMoCall;

            if (this.CellParamInfo.IsOutDoor && kpiCheck.IsCheckOutDoorOtherSet)
            {
                this.RatioInfoWeakRsrp.SetValidInfo(20, false);
                this.RatioInfoWeakSinr.SetValidInfo(10, false);
                this.RatioInfoLowDl.SetValidInfo(10, false);
                this.RatioInfoLowUl.SetValidInfo(10, false);
            }
            else
            {
                if (kpiCheck.IsCheckLeakOutRatio)
                {
                    this.RatioInfoWx.SetValidInfo(95, true);
                }
                if (kpiCheck.IsCheckInAndOutSrc)
                {
                    this.IsValidInAndOutSrc = this.HasInAndOutSrc;
                }
            }

            if (kpiCheck.IsCheckSRVCCCall)
            {
                this.RatioInfoSRVCC.SetValidInfo(100, true);
            }

            if (kpiCheck.IsCheckHandOver)
            {
                this.RatioInfoHandover.SetValidInfo(100, true);
            }
            this.IsValidHandOverSucessRatio = this.RatioInfoHandover.IsValidRatio;
        }

        public void AddCircleKpiInfo(TestPoint tp, bool isValidDl, bool isValidUl)
        {
            if (!isValidDl && !isValidUl)
            {
                return;
            }
            float? rsrp = tp.GetRxlev();
            addRatioInfoCount(rsrp, RatioInfoWeakRsrp, -110);

            float? sinr = getTpSinr(tp);
            addRatioInfoCount(sinr, RatioInfoWeakSinr, 0);

            if (isValidDl)
            {
                double? speed = getSpeedDl(tp);
                addRatioInfoCount(speed, RatioInfoLowDl, 2);
            }
            if (isValidUl)
            {
                double? speed = getSpeedUl(tp);
                addRatioInfoCount(speed, RatioInfoLowUl, 0.5);
            }
        }

        protected void addRatioInfoCount(double? value,RatioInfo info, double maxValue)
        {
            if (value != null)
            {
                info.CountDenominator++;
                if (value < maxValue)
                {
                    info.CountNumerator++;
                }
            }
        }

        protected override void setHasTpStatus(float? rsrp, float? sinr, double? pdcpSpeed, bool isDl)
        {
            if (this.IsPassAccept || (this.CellParamInfo!= null && (!this.CellParamInfo.IsOutDoor)))
            {
                return;
            }
            if (pdcpSpeed == null) pdcpSpeed = 0;

            if (rsrp != null && sinr != null)
            {
                if (rsrp >= -75)
                {
                    judgeGoodTp(sinr, pdcpSpeed, isDl);
                }
                else if (rsrp > -90 && rsrp < -80 && sinr >= 15)
                {
                    judgeCommonTp(pdcpSpeed, isDl);
                }
                else if (rsrp > -100 && rsrp < -90 && sinr >= 5)
                {
                    judgeBadTp(pdcpSpeed, isDl);
                }
            }
        }

        private void judgeGoodTp(float? sinr, double? pdcpSpeed, bool isDl)
        {
            if (isDl)
            {
                if (sinr >= 22 && pdcpSpeed >= 40)
                {
                    this.HasDlGoodTp = true;
                }
            }
            else
            {
                if (sinr >= 22 && pdcpSpeed >= 6)
                {
                    this.HasUlGoodTp = true;
                }
            }
        }

        private void judgeCommonTp(double? pdcpSpeed, bool isDl)
        {
            if (isDl)
            {
                if (pdcpSpeed >= 20)
                {
                    this.HasDlCommonTp = true;
                }
            }
            else
            {
                if (pdcpSpeed >= 3)
                {
                    this.HasUlCommonTp = true;
                }
            }
        }

        private void judgeBadTp(double? pdcpSpeed, bool isDl)
        {
            if (isDl)
            {
                if (pdcpSpeed >= 5)
                {
                    this.HasDlBadTp = true;
                }
            }
            else
            {
                if (pdcpSpeed >= 1)
                {
                    this.HasUlBadTp = true;
                }
            }
        }

        /// <summary>
        /// 采样点与小区覆盖方向夹角
        /// </summary>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <returns></returns>
        public double GetTp_CellAngle(TestPoint tp)
        {
            double tpLongitude = tp.Longitude;
            double tpLatitude = tp.Latitude;

            double angleDiff = 0;
            double distance = MathFuncs.GetDistance(this.CellParamInfo.Longitude, this.CellParamInfo.Latitude, tpLongitude, tpLatitude);

            ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
            double angle;
            double ygap = MathFuncs.GetDistance(this.CellParamInfo.Longitude, this.CellParamInfo.Latitude, this.CellParamInfo.Longitude, tpLatitude);
            double angleV = Math.Acos(ygap / distance);
            if (tpLongitude >= this.CellParamInfo.Longitude && tpLatitude >= this.CellParamInfo.Latitude)//1象限
            {
                angle = angleV * 180 / Math.PI;
            }
            else if (tpLongitude <= this.CellParamInfo.Longitude && tpLatitude >= this.CellParamInfo.Latitude)//2象限
            {
                angle = 360 - angleV * 180 / Math.PI;
            }
            else if (tpLongitude <= this.CellParamInfo.Longitude && tpLatitude <= this.CellParamInfo.Latitude)//3象限
            {
                angle = 180 + angleV * 180 / Math.PI;
            }
            else//4象限
            {
                angle = 180 - angleV * 180 / Math.PI;
            }

            angleDiff = Math.Abs(angle - this.CellParamInfo.Direction);
            if (angleDiff > 180)
            {
                angleDiff = 360 - angleDiff;
            }
            return angleDiff;
        }
    }

    public class AcceptInfoBase_HB
    {
        public AcceptInfoBase_HB()
        {
            SinrMax = null;
            RsrpMax = null;
            PdcpULMax = null;
            PdcpDLMax = null;
            TpListOfValidDlRsrp = new List<TestPoint>();
            ULTpList = new List<TestPoint>();
            DLTpList = new List<TestPoint>();
        }
        public int SN { get; set; }
        public string TestPosition { get; set; }

        public int sinrCount { get; set; }
        public float sinrSum { get; set; }
        public double? SinrAvg
        {
            get
            {
                if (sinrCount > 0)
                {
                    return Math.Round(sinrSum / sinrCount, 2);
                }
                return null;
            }
        }

        public float? SinrMax { get; set; }

        public int rsrpCount { get; set; }
        public float rsrpSum { get; set; }
        public double? RsrpAvg
        {
            get
            {
                if (rsrpCount > 0)
                {
                    return Math.Round(rsrpSum / rsrpCount, 2);
                }
                return null;
            }
        }
        public float? RsrpMax { get; set; }

        public int PdcpULCount { get; set; }
        public double PdcpULSum { get; set; }
        public virtual bool IsPdcpULAvgValid
        {
            get
            {
                return true;
            }
        }
        public double? PdcpULAvg
        {
            get
            {
                if (PdcpULCount > 0)
                {
                    return Math.Round(PdcpULSum / PdcpULCount, 2);
                }
                return null;
            }
        }
        public double? PdcpULMax { get; set; }

        public int PdcpDLCount { get; set; }
        public double PdcpDLSum { get; set; }

        public virtual bool IsPdcpDLAvgValid
        {
            get
            {
                return true;
            }
        }
        public double? PdcpDLAvg
        {
            get
            {
                if (PdcpDLCount > 0)
                {
                    return Math.Round(PdcpDLSum / PdcpDLCount, 2);
                }
                return null;
            }
        }
        public double? PdcpDLMax { get; set; }

        /// <summary>
        /// LTE数据业务或者是LTE空闲业务的下载类型的有效RSRP的采样点集合（仅室分站用到）
        /// </summary>
        public List<TestPoint> TpListOfValidDlRsrp { get; set; }
        public int TpCountOfValidDlRsrp
        {
            get
            {
                return TpListOfValidDlRsrp.Count;
            }
        }

        public int TpCountOfValidDlRsrpAndSinr { get; set; }

        /// <summary>
        /// 只做上传测试的采样点 仅报告拉线图用
        /// </summary>
        public List<TestPoint> ULTpList { get; set; }
        public bool HasFoundValidPositionUlTp { get; set; }

        /// <summary>
        /// 只做下载测试的采样点 仅报告拉线图用
        /// </summary>
        public List<TestPoint> DLTpList { get; set; }
        public bool HasFoundValidPositionDlTp { get; set; }

        public int CoverRate1Count { get; set; }
        public virtual bool IsCoverRate1Valid
        {
            get
            {
                return true;
            }
        }
        public string CoverRate1Des
        {
            get
            {
                if (TpCountOfValidDlRsrpAndSinr > 0)
                {
                    return Math.Round((double)100 * CoverRate1Count / TpCountOfValidDlRsrpAndSinr, 2) + "%";
                }
                return "";
            }
        }

        public int CoverRate2Count { get; set; }
        public string CoverRate2Des
        {
            get
            {
                if (TpCountOfValidDlRsrpAndSinr > 0)
                {
                    return Math.Round((double)100 * CoverRate2Count / TpCountOfValidDlRsrpAndSinr, 2) + "%";
                }
                return "";
            }
        }

        public int CoverRate3Count { get; set; }
        public string CoverRate3Des
        {
            get
            {
                if (TpCountOfValidDlRsrp > 0)
                {
                    return Math.Round((double)100 * CoverRate3Count / TpCountOfValidDlRsrp, 2) + "%";
                }
                return "";
            }
        }

        public bool IsValidHandOverSucessRatio { get; set; } = true;
        public RatioInfo RatioInfoHandover { get; set; } = new RatioInfo();
        public RatioInfo RatioInfoSRVCC { get; set; } = new RatioInfo();

        public CallSucessInfo CsfbCallInfo { get; set; } = new CallSucessInfo();
        public CallSucessInfo VolteVoiceCallInfo { get; set; } = new CallSucessInfo();
        public CallSucessInfo VolteVideoCallInfo { get; set; } = new CallSucessInfo();

        /// <summary>
        /// 是否找到单测的有经纬度的采样点
        /// </summary>
        public bool HasFoundValidSingleTestPosition { get; set; }
        public bool HasFoundFile { get; set; }

        public string EnodeBids
        {
            get
            {
                return getStringFromList(EnodeBidList);
            }
        }
        public List<string> EnodeBidList { get; set; } = new List<string>();

        public string PCIs
        {
            get
            {
                return getStringFromList(PciList);
            }
        }
        public List<string> PciList { get; set; } = new List<string>();

        public string TACs
        {
            get
            {
                return getStringFromList(TacList);
            }
        }
        public List<string> TacList { get; set; } = new List<string>();

        public string EARFCNs
        {
            get
            {
                return getStringFromList(EarfcnList);
            }
        }
        public List<string> EarfcnList { get; set; } = new List<string>();
        public List<string> ErrorInfoList { get; set; } = new List<string>();
        public void AddSrcCellBaseInfo(int? tpPci, int? tpTac, int? tpENodeBid, int? tpEarfcn)
        {
            if (tpPci != null && !this.PciList.Contains(tpPci.ToString()))
            {
                this.PciList.Add(tpPci.ToString());
            }
            if (tpTac != null && !this.TacList.Contains(tpTac.ToString()))
            {
                this.TacList.Add(tpTac.ToString());
            }
            if (tpENodeBid != null && !this.EnodeBidList.Contains(tpENodeBid.ToString()))
            {
                this.EnodeBidList.Add(tpENodeBid.ToString());
            }
            if (tpEarfcn != null && !this.EarfcnList.Contains(tpEarfcn.ToString()))
            {
                this.EarfcnList.Add(tpEarfcn.ToString());
            }
        }
        protected string getStringFromList(List<string> strList)
        {
            if (strList != null && strList.Count > 0)
            {
                List<string> newList = new List<string>();
                StringBuilder strb = new StringBuilder();
                foreach (string str in strList)
                {
                    if (!newList.Contains(str))
                    {
                        newList.Add(str);
                        strb.Append(str + "、");
                    }
                }
                if (strb.Length > 1)
                {
                    return strb.Remove(strb.Length - 1, 1).ToString();
                }
            }
            return "";
        }
        public void AddTp(TestPoint tp, bool isValidSingleTestPositionTp, bool isValidDl, bool isValidUl)
        {
            if (!this.HasFoundFile)
            {
                this.HasFoundFile = true;
            }
            if (isValidSingleTestPositionTp)
            {
                HasFoundValidSingleTestPosition = true;
            }

            //覆盖类指标都是针对LTE数据业务或者是LTE空闲模式数据,且分上传和下载
            if (isValidDl)
            {
                addValidDlTp(tp);
            }
            if (isValidUl)
            {
                addValidUlTp(tp);
            }
        }
        private void addValidDlTp(TestPoint tp)
        {
            float? rsrp = tp.GetRxlev();
            float? sinr = getTpSinr(tp);
            bool isValidRsrp = false;
            bool isValidSinr = false;
            if (rsrp != null && rsrp >= -141 && rsrp <= 25)
            {
                isValidRsrp = true;
                TpListOfValidDlRsrp.Add(tp);
            }
            if (sinr != null && sinr >= -50 && sinr <= 50)
            {
                isValidSinr = true;
            }
            if (isValidRsrp && isValidSinr)
            {
                TpCountOfValidDlRsrpAndSinr++;
            }

            double? pdcpDl = getSpeedDl(tp);
            if (pdcpDl != null)
            {
                PdcpDLCount++;
                PdcpDLSum += (double)pdcpDl;
                PdcpDLMax = PdcpDLMax > pdcpDl ? PdcpDLMax : pdcpDl;
            }

            setHasTpStatus(rsrp, sinr, pdcpDl, true);
            setGeneralCoverRate(isValidRsrp, isValidSinr, rsrp, sinr);

            if (isValidRsrp)
            {
                rsrpCount++;
                rsrpSum += (float)rsrp;
                RsrpMax = RsrpMax > rsrp ? RsrpMax : rsrp;
            }
            if (isValidSinr)
            {
                sinrCount++;
                sinrSum += (float)sinr;
                SinrMax = SinrMax > sinr ? SinrMax : sinr;
            }
        }
        private void addValidUlTp(TestPoint tp)
        {
            float? rsrp = tp.GetRxlev();
            float? sinr = getTpSinr(tp);
            double? pdcpUl = null;
            pdcpUl = getSpeedUl(tp);
            if (pdcpUl != null)
            {
                PdcpULCount++;
                PdcpULSum += (double)pdcpUl;
                PdcpULMax = PdcpULMax > pdcpUl ? PdcpULMax : pdcpUl;
            }

            setHasTpStatus(rsrp, sinr, pdcpUl, false);
        }
        protected float? getTpSinr(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_SINR"];
            }
            else
            {
                return (float?)tp["lte_SINR"];
            }
        }
        protected double? getSpeedDl(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (double?)tp["lte_fdd_PDCP_DL_Mb"];
            }
            else
            {
                return (double?)tp["lte_PDCP_DL_Mb"];
            }
        }
        protected double? getSpeedUl(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (double?)tp["lte_fdd_PDCP_UL_Mb"];
            }
            else
            {
                return (double?)tp["lte_PDCP_UL_Mb"];
            }
        }

        public void AddEvents(bool isValidDl, List<Event> evts, int serviceType)
        {
            if (isValidDl)
            {
                this.RatioInfoHandover.HasFoundValidFile = true;
                addHandOverEvents(evts);
            }
            else if (serviceType == (int)ServiceType.LTE_TDD_VOICE
                || serviceType == (int)ServiceType.LTE_FDD_VOICE)
            {
                this.CsfbCallInfo.HasFoundValidFile = true;
                addCsfbEvents(evts);
            }
            else if (serviceType == (int)ServiceType.LTE_TDD_VOLTE
                || serviceType == (int)ServiceType.LTE_FDD_VOLTE)
            {
                this.VolteVoiceCallInfo.HasFoundValidFile = true;
                addVolteVoiceCallEvents(evts);
                addSRVCCEvents(evts);
            }
            else if (serviceType == (int)ServiceType.SER_LTE_TDD_VIDEO_VOLTE
                || serviceType == (int)ServiceType.SER_LTE_FDD_VIDEO_VOLTE)
            {
                this.VolteVideoCallInfo.HasFoundValidFile = true;
                addVolteVideoCallEvents(evts);
                addSRVCCEvents(evts);
            }
        }

        private void addHandOverEvents(List<Event> evts)
        {
            foreach (Event evt in evts)
            {
                switch (evt.ID)
                {
                    case 850:
                    case 898:
                    case 3155://fdd
                    case 3158:
                        this.RatioInfoHandover.CountDenominator++;  //HandOver Request
                        break;
                    case 851:
                    case 899:
                    case 3156://fdd
                    case 3159:
                        this.RatioInfoHandover.CountNumerator++;    //HandOver Success
                        break;
                }
            }
        }

        private void addCsfbEvents(List<Event> evts)
        {
            foreach (Event evt in evts)
            {
                //int validCount = 1;
                //object isOutObj = evt["Value9"];//value9: -1 剔除 0保留
                //if (isOutObj != null)
                //{
                //    validCount += int.Parse(isOutObj.ToString());
                //}

                switch (evt.ID)
                {
                    case 870:
                    case 1040:
                    case 1100:
                    case 1147:
                    case 3139://fdd
                    case 3157:
                    case 3160:
                        this.CsfbCallInfo.AddHandFailEvt(evt);
                        break;   
                    case 1006:
                    case 1007:
                    case 1016:
                    case 1017:
                    case 1026:
                    case 1027:
                    case 1036:
                    case 1037:
                    case 1046:
                    case 1047:
                    case 1056:
                    case 1057:
                    case 3006://fdd
                    case 3007:
                    case 3016:
                    case 3017:
                    case 3026:
                    case 3027:
                    case 3036:
                    case 3037:
                    case 3046:
                    case 3047:
                    case 3056:
                    case 3057:
                        this.CsfbCallInfo.AddDropEvt(evt);
                        break;
                    case 1001:
                    case 1021:
                    case 1041:
                    case 3001://fdd
                    case 3021:
                    case 3041:
                        this.CsfbCallInfo.AddMoCallAttemptEvt(evt);
                        break;
                    case 1002:
                    case 1022:
                    case 1042:
                    case 3002://fdd
                    case 3022:
                    case 3042:
                        this.CsfbCallInfo.AddAttemptEvt(evt);
                        break;
                    case 1008:
                    case 1028:
                    case 1048:
                    case 1058:
                    case 3008://fdd
                    case 3028:
                    case 3048:
                    case 3058:
                        this.CsfbCallInfo.AddMoCallBlockEvt(evt);
                        break;
                    case 1009:
                    case 1029:
                    case 1049:
                    case 1059:
                    case 3009://fdd
                    case 3029:
                    case 3049:
                    case 3059:
                        this.CsfbCallInfo.AddBlockEvt(evt);
                        break;

                }
            }
        }
        private void addVolteVoiceCallEvents(List<Event> evts)
        {
            foreach (Event evt in evts)
            {
                //int validCount = 1;
                //object isOutObj = evt["Value9"];//value9: -1 剔除 0保留
                //if (isOutObj != null)
                //{
                //    validCount += int.Parse(isOutObj.ToString());
                //}
                switch (evt.ID)
                {
                    case 870:
                    case 1040:
                    case 1100:
                    case 1147:
                    case 3139://fdd
                    case 3157:
                    case 3160:
                    case 3630:
                        this.VolteVoiceCallInfo.AddHandFailEvt(evt);
                        break;
                    case 1078:
                    case 1079:
                    case 1086:
                    case 1087:
                    case 3613://fdd
                    case 3620:
                    case 3625:
                    case 3629:
                        this.VolteVoiceCallInfo.AddDropEvt(evt);
                        break;
                    case 1070:
                    case 3609://fdd
                        this.VolteVoiceCallInfo.AddMoCallAttemptEvt(evt);
                        break;
                    case 1071:
                    case 3616://fdd
                        this.VolteVoiceCallInfo.AddAttemptEvt(evt);
                        break;
                    case 1080:
                    case 1090:
                    case 3608://fdd
                    case 3622:
                        this.VolteVoiceCallInfo.AddMoCallBlockEvt(evt);
                        break;
                    case 1081:
                    case 1091:
                    case 3615://fdd
                    case 3626:
                        this.VolteVoiceCallInfo.AddBlockEvt(evt);
                        break;
                    default:
                        break;
                }
            }
        }
        private void addVolteVideoCallEvents(List<Event> evts)
        {
            foreach (Event evt in evts)
            {
                //int validCount = 1;
                //object isOutObj = evt["Value9"];//value9: -1 剔除 0保留
                //if (isOutObj != null)
                //{
                //    validCount += int.Parse(isOutObj.ToString());
                //}

                switch (evt.ID)
                {
                    case 870:
                    case 1040:
                    case 1100:
                    case 1147:
                    case 3139://fdd
                    case 3157:
                    case 3160:
                    case 3630:
                        this.VolteVideoCallInfo.AddHandFailEvt(evt);
                        break;   
                    case 1378:
                    case 1379:
                    case 1386:
                    case 1387:
                    case 3657://fdd
                    case 3665:
                    case 3703:
                    case 3649:
                        this.VolteVideoCallInfo.AddDropEvt(evt);
                        break;
                    case 1370:
                    case 3652://fdd
                        this.VolteVideoCallInfo.AddMoCallAttemptEvt(evt);
                        break;
                    case 1371:
                    case 3661://fdd
                        this.VolteVideoCallInfo.AddAttemptEvt(evt);
                        break;
                    case 1380:
                    case 1390:
                    case 3651://fdd
                    case 3643:
                        this.VolteVideoCallInfo.AddMoCallBlockEvt(evt);
                        break;
                    case 1381:
                    case 1391:
                    case 3660://fdd
                    case 3646:
                        this.VolteVideoCallInfo.AddBlockEvt(evt);
                        break;
                }
            }
        }
        private void addSRVCCEvents(List<Event> evts)
        {
            this.RatioInfoSRVCC.HasFoundValidFile = true;

            foreach (Event evt in evts)
            {
                switch (evt.ID)
                {
                    case 1145://SRVCC Requestt
                    case 3631://fdd
                        this.RatioInfoSRVCC.CountDenominator++;
                        break;
                    case 1146://SRVCC Success
                    case 3632://fdd
                        this.RatioInfoSRVCC.CountNumerator++;
                        break;
                }
            }
        }

        /// <summary>
        /// 室外站统计好中差点
        /// </summary>
        /// <param name="rsrp"></param>
        /// <param name="sinr"></param>
        /// <param name="pdcpSpeed"></param>
        /// <param name="pdcpDl"></param>
        protected virtual void setHasTpStatus(float? rsrp, float? sinr, double? pdcpSpeed, bool isDl)
        {
        }

        /// <summary>
        /// 室分站统计综合覆盖率
        /// </summary>
        /// <param name="rsrp"></param>
        /// <param name="sinr"></param>
        protected void setGeneralCoverRate(bool isValidRsrp, bool isValidSinr, float? rsrp, float? sinr)
        {
            if (isValidRsrp)
            {
                if (isValidSinr)
                {
                    if (rsrp >= -105 && sinr >= 6)
                    {
                        CoverRate1Count++;
                    }
                    if (rsrp >= -95 && sinr >= 9)
                    {
                        CoverRate2Count++;
                    }
                }
                if (rsrp >= -85)
                {
                    CoverRate3Count++;
                }
            }
        }
    }
    public class CallSucessInfo
    {
        public List<TestPoint> TestPointMoList { get; set; } = new List<TestPoint>();
        public List<TestPoint> TestPointMtList { get; set; } = new List<TestPoint>();
        public List<Event> ProblemEvtMoList { get; set; } = new List<Event>();
        public List<Event> ProblemEvtMtList { get; set; } = new List<Event>();
        public bool HasFoundValidFile { get; set; }
        public int DropCount { get; set; }
        public int AttemptCount { get; set; }
        public int BlockCount { get; set; }
        public int MoCallAttemptCount { get; set; }
        public int MoCallBlockCount { get; set; }

        /// <summary>
        /// 全程呼叫成功率
        /// </summary>
        public double SucessRatioAllCall
        {
            get
            {
                if ((AttemptCount - BlockCount > 0) && MoCallAttemptCount > 0)
                {
                    return Math.Round(100 * ((1 - ((double)DropCount / (AttemptCount - BlockCount)))
                        * ((double)(MoCallAttemptCount - MoCallBlockCount) / MoCallAttemptCount)), 2);
                }
                return double.MinValue;
            }
        }
        public string SucessRatioAllCallDes
        {
            get
            {
                if (SucessRatioAllCall != double.MinValue)
                {
                    return SucessRatioAllCall + "%";
                }
                else if (!HasFoundValidFile)
                {
                    return "无对应数据";
                }
                return "-";
            }
        }
        public bool IsCheckAllCall { get; set; } = false;
        public bool IsValidSucessRatioAllCall
        {
            get
            {
                if (IsCheckAllCall)
                {
                    return SucessRatioAllCall >= 100;
                }
                return true;
            }
        }

        /// <summary>
        /// 呼叫接通率
        /// </summary>
        public double SucessRatioMoCall
        {
            get
            {
                if (MoCallAttemptCount > 0)
                {
                    return Math.Round(100 * ((double)(MoCallAttemptCount - MoCallBlockCount) / MoCallAttemptCount), 2);
                }
                return double.MinValue;
            }
        }
        public string SucessRatioMoCallDes
        {
            get
            {
                if (SucessRatioMoCall != double.MinValue)
                {
                    return SucessRatioMoCall + "%";
                }
                else if (!HasFoundValidFile)
                {
                    return "无对应数据";
                }
                return "-";
            }
        }
        public bool IsCheckMoCall { get; set; } = false;
        public bool IsValidSucessRatioMoCall
        {
            get
            {
                if (IsCheckMoCall)
                {
                    return SucessRatioMoCall >= 100;
                }
                return true;
            }
        }

        public void Merge(CallSucessInfo info)
        {
            if (info.HasFoundValidFile)
            {
                this.HasFoundValidFile = true;
            }
            this.TestPointMoList.AddRange(info.TestPointMoList);
            this.TestPointMtList.AddRange(info.TestPointMtList);
            this.ProblemEvtMoList.AddRange(info.ProblemEvtMoList);
            this.ProblemEvtMtList.AddRange(info.ProblemEvtMtList);
            this.DropCount += info.DropCount;
            this.AttemptCount += info.AttemptCount;
            this.BlockCount += info.BlockCount;
            this.MoCallAttemptCount += info.MoCallAttemptCount;
            this.MoCallBlockCount += info.MoCallBlockCount;
        }

        public void AddTpByMoMtFlag(TestPoint tp)
        {
            if (tp.MoMtFlag == (int)MoMtFile.MtFlag)
            {
                this.TestPointMtList.Add(tp);
            }
            else
            {
                this.TestPointMoList.Add(tp);
            }
        }
        private void addEvtByMoMtFlag(Event evt)
        {
            if (evt.MoMtFlag == (int)MoMtFile.MtFlag)
            {
                ProblemEvtMtList.Add(evt);
            }
            else
            {
                ProblemEvtMoList.Add(evt);
            }
        }
        public void AddHandFailEvt(Event evt)
        {
            addEvtByMoMtFlag(evt);
        }

        public void AddDropEvt(Event evt)
        {
            this.DropCount += getEvtValidCount(evt);
            addEvtByMoMtFlag(evt);
        }
        public void AddAttemptEvt(Event evt)
        {
            this.AttemptCount += getEvtValidCount(evt);
        }
        public void AddBlockEvt(Event evt)
        {
            this.BlockCount += getEvtValidCount(evt);
            addEvtByMoMtFlag(evt);
        }
        public void AddMoCallAttemptEvt(Event evt)
        {
            int validCount = getEvtValidCount(evt);
            this.MoCallAttemptCount += validCount;
            this.AttemptCount += validCount;
        }
        public void AddMoCallBlockEvt(Event evt)
        {
            int validCount = getEvtValidCount(evt);
            this.MoCallBlockCount += validCount;
            this.BlockCount += validCount;
            addEvtByMoMtFlag(evt);
        }
        private int getEvtValidCount(Event evt)
        {
            int validCount = 1;
            object isOutObj = evt["Value9"];//value9: -1 剔除 0保留
            if (isOutObj != null)
            {
                validCount += int.Parse(isOutObj.ToString());
            }
            return validCount;
        }
    }

    public class RatioInfo
    {
        /// <summary>
        /// 分母
        /// </summary>
        public int CountDenominator { get; set; }

        /// <summary>
        /// 分子
        /// </summary>
        public int CountNumerator { get; set; }

        /// <summary>
        /// 有效点所占比例
        /// </summary>
        public double? Ratio
        {
            get
            {
                if (CountDenominator > 0)
                {
                    return Math.Round((double)100 * CountNumerator / CountDenominator, 2);
                }
                return null;
            }
        }

        public string RatioDes
        {
            get
            {
                if (Ratio != null)
                {
                    return Ratio + "%";
                }
                else if (!HasFoundValidFile)
                {
                    return "无对应数据";
                }
                return "-";
            }
        }

        private bool hasFoundValidFile = false;
        public bool HasFoundValidFile 
        {
            get
            {
                if (CountDenominator > 0 || CountNumerator > 0)
                {
                    return true;
                }
                return hasFoundValidFile;
            }
            set
            {
                hasFoundValidFile = value;
            }
        }

        private bool hasSetValidInfo = false;
        private double validRatio = 100;
        private bool isMaxIncludeThanValidRatio = true;

        public void SetValidInfo(double validRatio, bool isMaxIncludeThanValidRatio)
        {
            this.hasSetValidInfo = true;
            this.validRatio = validRatio;
            this.isMaxIncludeThanValidRatio = isMaxIncludeThanValidRatio;
        }
        public bool IsValidRatio
        {
            get
            {
                if (hasSetValidInfo)
                {
                    if (isMaxIncludeThanValidRatio)
                    {
                        return Ratio >= validRatio;
                    }
                    else
                    {
                        return validRatio > Ratio;
                    }
                }
                return true;
            }
        }

        public void Merge(RatioInfo info)
        {
            this.CountNumerator += info.CountNumerator;
            this.CountDenominator += info.CountDenominator;
            if (info.HasFoundValidFile)
            {
                this.HasFoundValidFile = true;
            }
        }
    }
}
