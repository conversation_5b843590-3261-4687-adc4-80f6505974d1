﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;
using System.Net;

namespace MasterCom.RAMS.CQT
{
    public class DIYQureyCQTTDNoMainCelldData : DIYSampleQuery
    {
        public DIYQureyCQTTDNoMainCelldData(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            isAddSampleToDTDataManager = false;
        }
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return "查询TD业务覆盖杂乱"; }
        }
        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21017, this.Name);
        }

        /// <summary>
        /// 涉及的全局变量
        /// </summary>
        private readonly List<string> fileValueNameList = new List<string>();
        readonly Dictionary<string, List<FileInfo>> fileValueList = new Dictionary<string, List<FileInfo>>();
        readonly Dictionary<string, List<TestPoint>> cqtNameTestPointList = new Dictionary<string, List<TestPoint>>();
        readonly Dictionary<string, Dictionary<TDCell, Dictionary<TestPoint, List<NbSampleInfoTD>>>> cqtMainCellTestPointNbDic
            = new Dictionary<string, Dictionary<TDCell, Dictionary<TestPoint, List<NbSampleInfoTD>>>>();
        readonly Dictionary<TDCell, Dictionary<TestPoint, List<NbSampleInfoTD>>> mainCellTestPointNbDic
            = new Dictionary<TDCell, Dictionary<TestPoint, List<NbSampleInfoTD>>>();
        readonly List<CQTTdNoMainCell> resultData = new List<CQTTdNoMainCell>();
        readonly Dictionary<TDCell, int> cellNoDominTestPointDic = new Dictionary<TDCell, int>();
       
        private int noUserTem { get; set; } = 0;
        public int RSCPMin { get; set; } = -85;
        public int RSCPMax { get; set; } = -10;
        public int sampleCountLimit { get; set; } = 1;
        public int cellCountLimit { get; set; } = 4;
        public int rxLevDValue { get; set; } = 6;

        protected override void query()
        {
            NoMainCellSetForm noMainCellSetForm = new NoMainCellSetForm("TD");
            if (noMainCellSetForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            NoMainCellCondition settingCondition = noMainCellSetForm.GetSettingFilterRet();
            RSCPMin = settingCondition.RSCPMin;
            RSCPMax = settingCondition.RSCPMax;
            sampleCountLimit = settingCondition.sampleCountLimit;
            cellCountLimit = settingCondition.cellCountLimit;
            rxLevDValue = settingCondition.rxLevDValue;
            fileValueNameList.Clear();
            fileValueList.Clear();
            resultData.Clear();
            // 查找全区域文件
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();
            //找出所有文件中包含的地点名称
            foreach (FileInfo fileIn in diyFileInfoData.FlieInfoData)
            {
                string[] name = fileIn.Name.Split('_');
                if (name.Length < 3)
                    continue;
                if (!fileValueNameList.Contains(name[2]))
                {
                    fileValueNameList.Add(name[2]);
                }
            }
            //每个地点所涉及的文件
            foreach (string nameL in fileValueNameList)
            {
                List<FileInfo> subFileList = new List<FileInfo>();
                foreach (FileInfo fileIn in diyFileInfoData.FlieInfoData)
                {
                    string[] name = fileIn.Name.Split('_');
                    if (name.Length < 3)
                        continue;
                    if (nameL.Equals(name[2]))
                    {
                        subFileList.Add(fileIn);
                    }
                }
                fileValueList.Add(nameL, subFileList);
            }
            ClientProxy clientProxy = new ClientProxy();
            WaitBox.Show("开始分析数据...", queryInThread, clientProxy);

            CQTTDNoDominantCellNew xtraformstatus = new CQTTDNoDominantCellNew(MainModel);
            xtraformstatus.setData(resultData);
            xtraformstatus.Show();
        }
        /// <summary>
        /// 开始分析采样点
        /// </summary>
        protected override void queryInThread(object o)
        {
            WaitBox.CanCancel = true;
            int idx = 1;
            foreach (string name in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                cqtNameTestPointList.Clear();
                cellNoDominTestPointDic.Clear();
                cqtMainCellTestPointNbDic.Clear();
                //获取测试地点的采样点
                anyFileAnalysTestPointTD(name,idx++);
                //对每个测试地点涉及的采样点进行电平值的统计分析
                foreach (string cqtName in cqtNameTestPointList.Keys)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    mainCellTestPointNbDic.Clear();
                    dealTPList(cqtName);
                    cqtMainCellTestPointNbDic.Add(cqtName, mainCellTestPointNbDic);
                }
                WaitBox.Text = "分析该测试地点各小区的无主导采样点个数...";
                foreach (string cqtname in cqtMainCellTestPointNbDic.Keys)
                {
                    addCellNoDominTestPointDic(cqtname);
                }
                dealWithDataDic();
            }
            WaitBox.Close();
        }

        private void dealTPList(string cqtName)
        {
            foreach (TestPoint tp in cqtNameTestPointList[cqtName])
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                if (tp["TD_SCell_CI"] == null || tp["TD_PCCPCH_RSCP"] == null)
                    continue;
                int iCi = (int)tp["TD_SCell_CI"];
                int iRSCP = (int)(float?)tp["TD_PCCPCH_RSCP"];
                if (iCi <= 0 || iRSCP > RSCPMax || iRSCP < RSCPMin)
                    continue;
                GetCellByTestPointTD(tp);
            }
        }

        private void addCellNoDominTestPointDic(string cqtname)
        {
            foreach (TDCell cell in cqtMainCellTestPointNbDic[cqtname].Keys)
            {
                int noDominCout = 0;
                foreach (TestPoint tp in cqtMainCellTestPointNbDic[cqtname][cell].Keys)
                {
                    if (IsNoDominTestPoint(tp))
                    {
                        noDominCout++;
                    }
                }
                cellNoDominTestPointDic.Add(cell, noDominCout);
            }
        }

        /// <summary>
        /// 实际获取采样点的过程
        /// </summary>
        private void anyFileAnalysTestPointTD(string name,int idx)
        {
            List<TestPoint> testlist = new List<TestPoint>();
            WaitBox.Text = "分析(" + idx.ToString() + "/" + fileValueNameList.Count.ToString() + ")( " + name + " )的采样点...";
            WaitBox.ProgressPercent = 30;
            QueryCondition condition = new QueryCondition();
            condition.FileInfos.AddRange(fileValueList[name]);
            try
            {
                ReplayFileCQT query = new ReplayFileCQT(MainModel);
                query.SetQueryCondition(condition);
                query.Query();
                testlist.AddRange(query.testPointList);
                WaitBox.ProgressPercent = 95;
            }
            catch
            {
                //continue
            }
            cqtNameTestPointList.Add(name, testlist);
            WaitBox.ProgressPercent = 90;
        }
        /// <summary>
        /// 通过采样点获取小区
        /// </summary>
        private void GetCellByTestPointTD(TestPoint tp)
        {
            Dictionary<TestPoint, List<NbSampleInfoTD>> testPointNbSample = new Dictionary<TestPoint, List<NbSampleInfoTD>>();
            List<NbSampleInfoTD> nbsampleList = new List<NbSampleInfoTD>();
            for (int i = 0; i < 6; i++)
            {
                NbSampleInfoTD nbsampleinfo = new NbSampleInfoTD();
                if (tp["TD_NCell_UARFCN", i] == null || tp["TD_NCell_CPI", i] == null || tp["TD_NCell_PCCPCH_RSCP", i] == null)
                    continue;
                nbsampleinfo.iNbUARFCN = (int)(int?)tp["TD_NCell_UARFCN", i];
                nbsampleinfo.iNbCPI = (int)(int?)tp["TD_NCell_CPI", i];
                nbsampleinfo.iNbRSCP = (int)(int?)tp["TD_NCell_PCCPCH_RSCP", i];
                if (nbsampleinfo.iNbUARFCN <= 0 || nbsampleinfo.iNbCPI <= 0 || nbsampleinfo.iNbRSCP > RSCPMax || nbsampleinfo.iNbRSCP < RSCPMin)
                    continue; 
                nbsampleList.Add(nbsampleinfo);
            }
            TDCell mainCell = tp.GetMainCell_TD_TDCell();
            if (mainCell != null)
            {
                List<TDCell> cellTem = new List<TDCell>();
                cellTem.AddRange(mainCellTestPointNbDic.Keys);
                if (!cellTem.Contains(mainCell))
                {
                    testPointNbSample.Add(tp, nbsampleList);
                    mainCellTestPointNbDic.Add(mainCell, testPointNbSample);
                }
                else
                {
                    mainCellTestPointNbDic[mainCell].Add(tp, nbsampleList);
                }
            } 
        }
        /// <summary>
        /// 判断是否是无主导的采样点
        /// </summary>
        private bool IsNoDominTestPoint(TestPoint tp)
        {
            List<int> relevList = new List<int>();
            float? rSCP = (float?)tp["TD_PCCPCH_RSCP"];
            if (rSCP == null || rSCP < RSCPMin)
                return false;
            else
                relevList.Add((int)(rSCP));
            for (int i = 0; i < 6; i++)
            {
                int? rSCPN = (int?)tp["TD_NCell_PCCPCH_RSCP", i];
                if (rSCPN != null)
                {
                    if (rSCPN >= RSCPMin)
                        relevList.Add((int)(rSCPN));
                }
                else
                    break;
            }
            int maxRelev;
            int icount = 0;
            relevList.Sort();
            if (relevList.Count != 0)
            {
                maxRelev = relevList[relevList.Count - 1];
                icount = getCountLimit(relevList, maxRelev, icount);
            }
            else
                return false;
            if (icount >= cellCountLimit)
                return true;
            else
                return false;

        }

        private int getCountLimit(List<int> relevList, int maxRelev, int icount)
        {
            icount++;
            for (int i = relevList.Count - 2; i >= 0; i--)
            {
                if ((maxRelev - relevList[i]) <= rxLevDValue)
                    icount++;
                else
                    break;
            }

            return icount;
        }

        /// <summary>
        /// 将处理的数据转化为CQTGsmNoMainCell数据，用于输出
        /// </summary>
        private void dealWithDataDic()
        {
            foreach (string cqtName in cqtMainCellTestPointNbDic.Keys)
            {
                foreach (TDCell cell in cqtMainCellTestPointNbDic[cqtName].Keys)
                {
                    CQTTdNoMainCell cqtGsmNoMainCell = new CQTTdNoMainCell();
                    cqtGsmNoMainCell.Strcqtname = cqtName;
                    cqtGsmNoMainCell.Strcellname = cell.Name;
                    cqtGsmNoMainCell.Ilac = cell.LAC;
                    cqtGsmNoMainCell.Ici = cell.CI;
                    List<TestPoint> testPoint = new List<TestPoint>();
                    testPoint.AddRange(cqtMainCellTestPointNbDic[cqtName][cell].Keys);
                    cqtGsmNoMainCell.Iuarfcn = (int)testPoint[0]["TD_SCell_UARFCN"];
                    cqtGsmNoMainCell.Icpi = cell.CPI;
                    string strRxlevSub = GetMaxMinAvgRSCPSub(testPoint, cqtMainCellTestPointNbDic[cqtName][cell]);
                    cqtGsmNoMainCell.Isamplenum = testPoint.Count;
                    cqtGsmNoMainCell.Inomaincellname = cellNoDominTestPointDic[cell];
                    cqtGsmNoMainCell.Strnomaincellrate = (100.0 * cqtGsmNoMainCell.Inomaincellname / cqtGsmNoMainCell.Isamplenum).ToString("0.00") + "%";                    
                    cqtGsmNoMainCell.Imaxpccpchrscp = int.Parse(strRxlevSub.Split('|')[0].Split(',')[0]);
                    cqtGsmNoMainCell.Iminpccpchrscp = int.Parse(strRxlevSub.Split('|')[0].Split(',')[1]);
                    cqtGsmNoMainCell.Iavgpccpchrscp = int.Parse(strRxlevSub.Split('|')[0].Split(',')[2]);
                    cqtGsmNoMainCell.Iavgpccpchc2i = int.Parse(strRxlevSub.Split('|')[2]);
                    cqtGsmNoMainCell.Iavgdpchc2i = int.Parse(strRxlevSub.Split('|')[3]);
                    cqtGsmNoMainCell.Imaxnbpccpchrscp = int.Parse(strRxlevSub.Split('|')[1].Split(',')[0]);
                    cqtGsmNoMainCell.Iminnbpccpchrscp = int.Parse(strRxlevSub.Split('|')[1].Split(',')[1]);
                    cqtGsmNoMainCell.Iavgnbpccpchrscp = int.Parse(strRxlevSub.Split('|')[1].Split(',')[2]);
                    if (cqtGsmNoMainCell.Imaxpccpchrscp == 0 && cqtGsmNoMainCell.Iminpccpchrscp == 0 ||
                        cqtGsmNoMainCell.Inomaincellname < sampleCountLimit || cqtGsmNoMainCell.Iuarfcn == 65535)
                        continue;
                    resultData.Add(cqtGsmNoMainCell);
                }
            }
        }

        private string GetMaxMinAvgRSCPSub(List<TestPoint> testPoint, Dictionary<TestPoint, List<NbSampleInfoTD>> testPointNbSample)
        {
            int MaxRSCP = RSCPMin;
            int MinRSCP = RSCPMax;
            int AvgRSCP = 0;
            int SumRSCP = 0;
            int AvgPCCPH = 0;
            int SumPCCPH = 0;
            int AvgDPCH = 0;
            int SumDPCH = 0;
            int nbMaxRSCP = RSCPMin;
            int nbMinRSCP = RSCPMax;
            int nbAvgRSCP = 0;
            int nbSumRSCP = 0;
            int nbSumSampleCount = 0;
            int noUse = 0;
            foreach (TestPoint tp in testPoint)
            {
                if (tp["TD_PCCPCH_C2I"] == null || tp["TD_PCCPCH_RSCP"] == null || tp["TD_DPCH_C2I"] == null || (int)tp["TD_PCCPCH_C2I"] > 40 || (int)tp["TD_PCCPCH_C2I"] < -40
                    || (int)tp["TD_DPCH_C2I"] > 40 || (int)tp["TD_DPCH_C2I"] < -40 || (int)(float?)tp["TD_PCCPCH_RSCP"] > RSCPMax || (int)(float?)tp["TD_PCCPCH_RSCP"] < RSCPMin)
                {
                    noUse++;
                    continue;
                }
                SumRSCP += (int)(float?)tp["TD_PCCPCH_RSCP"];
                if ((int)(float?)tp["TD_PCCPCH_RSCP"] > MaxRSCP)
                {
                    MaxRSCP = (int)(float?)tp["TD_PCCPCH_RSCP"];
                }
                if ((int)(float?)tp["TD_PCCPCH_RSCP"] < MinRSCP)
                {
                    MinRSCP = (int)(float?)tp["TD_PCCPCH_RSCP"];
                }
                SumPCCPH += (int)tp["TD_PCCPCH_C2I"];
                SumDPCH += (int)tp["TD_DPCH_C2I"];
                getNBTPInfo(testPointNbSample, ref nbMaxRSCP, ref nbMinRSCP, ref nbSumRSCP, ref nbSumSampleCount, tp);
            }
            if (testPoint.Count - noUse != 0)
            {
                AvgRSCP = SumRSCP / (testPoint.Count - noUse);
                AvgPCCPH = SumPCCPH / (testPoint.Count - noUse);
                AvgDPCH = SumDPCH / (testPoint.Count - noUse);
            }
            else
            {
                AvgRSCP = 0;
                AvgPCCPH = 0;
                AvgDPCH = 0;
                MaxRSCP = 0;
                MinRSCP = 0;
            }           
            if (nbSumSampleCount != 0)
                nbAvgRSCP = nbSumRSCP / nbSumSampleCount;
            else
                nbAvgRSCP = 0;
            noUserTem = noUse;
            return MaxRSCP.ToString() + "," + MinRSCP.ToString() + "," + AvgRSCP.ToString() + "|"
                + nbMaxRSCP.ToString() + "," + nbMinRSCP.ToString() + "," + nbAvgRSCP.ToString() + "|"
                 + AvgPCCPH.ToString() + "|" + AvgDPCH.ToString();
        }

        private void getNBTPInfo(Dictionary<TestPoint, List<NbSampleInfoTD>> testPointNbSample, ref int nbMaxRSCP, ref int nbMinRSCP, ref int nbSumRSCP, ref int nbSumSampleCount, TestPoint tp)
        {
            foreach (NbSampleInfoTD nbsampleinfo in testPointNbSample[tp])
            {
                nbSumRSCP += nbsampleinfo.iNbRSCP;
                if (nbsampleinfo.iNbRSCP > nbMaxRSCP)
                {
                    nbMaxRSCP = nbsampleinfo.iNbRSCP;
                }
                if (nbsampleinfo.iNbRSCP < nbMinRSCP)
                {
                    nbMinRSCP = nbsampleinfo.iNbRSCP;
                }
            }
            nbSumSampleCount += testPointNbSample[tp].Count;
        }
    }
    class NbSampleInfoTD
    {
        public int iNbUARFCN;
        public int iNbCPI;
        public int iNbRSCP;

        public NbSampleInfoTD()
        {
            iNbUARFCN = 0;
            iNbCPI = 0;
            iNbRSCP = 0;
        }
    }
    public class CQTTdNoMainCell
    {
        public float Fnomaincellrate { get; set; }
        public float FtotalDpchC2i { get; set; }
        public float FtotalnbRscp { get; set; }
        public float FtotalPccpchC2i { get; set; }
        public float FtotalPccpchRscp { get; set; }
        public int Iareaid { get; set; }
        public int Iareatype { get; set; }
        public int Iavgdpchc2i { get; set; }
        public int Iavgnbpccpchrscp { get; set; }
        public int Iavgpccpchc2i { get; set; }
        public int Iavgpccpchrscp { get; set; }
        public int Ici { get; set; }
        public int Icpi { get; set; }
        public int Idx { get; set; }
        public int Ilac { get; set; }
        public int Imaxnbpccpchrscp { get; set; }
        public int Imaxpccpchrscp { get; set; }
        public int Iminnbpccpchrscp { get; set; }
        public int Iminpccpchrscp { get; set; }
        public int Inomaincellname { get; set; }
        public int InumDpchC2i { get; set; }
        public int InumnbRscp { get; set; }
        public int InumPccpchC2i { get; set; }
        public int Isamplenum { get; set; }
        public int Iuarfcn { get; set; }
        public string Strcellname { get; set; }
        public string Strcqtname { get; set; }
        public string Strnomaincellrate { get; set; }
    }
}
