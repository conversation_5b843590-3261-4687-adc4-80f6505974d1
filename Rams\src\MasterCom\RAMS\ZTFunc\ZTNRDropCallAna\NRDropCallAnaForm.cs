﻿using DevExpress.XtraCharts;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRDropCallAnaForm : MinCloseForm
    {
        //NRDropCallAnaInfo clickedCallInfo = new NRDropCallAnaInfo();
        public NRDropCallAnaForm()
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        private List<NRDropCallInfo> calls = null;
        internal void FillData(List<NRDropCallInfo> calls, NRDropCallAnaCondtion dropCallCond)
        {
            this.calls = calls;
            colSinr.Caption = string.Format("掉话前{0}秒平均SINR", dropCallCond.PoorSinrSec);
            colRsrp.Caption = string.Format("掉话前{0}秒平均RSRP", dropCallCond.WeakRsrpSec);
            colLteSinr.Caption = string.Format("掉话前{0}秒平均Lte SINR", dropCallCond.PoorSinrSec);
            colLteRsrp.Caption = string.Format("掉话前{0}秒平均Lte RSRP", dropCallCond.WeakRsrpSec);
            colHoNum.Caption = string.Format("掉话前{0}秒切换次数", dropCallCond.HoSec);

            makeSummary();
         
            gridControl.DataSource = calls;
            gridControl.RefreshDataSource();
        }

        private void makeSummary()
        {
            Dictionary<string, int> causeDic = new Dictionary<string, int>();
            foreach (string name in Enum.GetNames(typeof(ENRDropCallCause)))
            {
                causeDic[name] = 0;
            }
            int dropNum = 0;
            foreach (NRDropCallInfo dropCall in calls)
            {
                foreach (NRDropCallAnaInfo call in dropCall.MoMtCalls)
                {
                    if (call.IsDropCall)
                    {
                        dropNum++;
                        causeDic[call.DropCause.ToString()]++;
                    }
                }
            }
            DataTable tb = new DataTable();
            tb.Columns.Add("原因", typeof(string));
            tb.Columns.Add("掉话个数", typeof(int));
            tb.Columns.Add("占比(%)", typeof(double));

            Series mainSer = chartMain.Series[0];
            mainSer.Points.Clear();
            foreach (KeyValuePair<string, int> pair in causeDic)
            {
                DataRow row = tb.NewRow();
                row["原因"] = pair.Key;
                row["掉话个数"] = pair.Value;
                double per = Math.Round(100.0 * pair.Value / dropNum, 2);
                row["占比(%)"] = per;
                tb.Rows.Add(row);
                SeriesPoint pnt = new SeriesPoint(pair.Key, per);
                mainSer.Points.Add(pnt);
            }
            DataRow srow = tb.NewRow();
            srow["原因"] = "汇总";
            srow["掉话个数"] = dropNum;
            srow["占比(%)"] = 100;
            tb.Rows.Add(srow);
            gridSummary.DataSource = tb;
            viewSummary.PopulateColumns();
            viewSummary.BestFitColumns();
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            object info = gridView.GetRow(gridView.FocusedRowHandle);
            if (info == null)
            {
                return;
            }
            if (info is NRDropCallInfo)
            {
                MainModel.ClearDTData();
                NRDropCallInfo dc = info as NRDropCallInfo;
                foreach (NRDropCallAnaInfo call in dc.MoMtCalls)
                {
                    addDTData(call);
                }
                MainModel.IsFileReplayByCompareMode = true;
                MainModel.FireDTDataChanged(this);
                MainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
            }
            else if (info is NRDropCallAnaInfo)
            {
                MainModel.ClearDTData();
                NRDropCallAnaInfo call = info as NRDropCallAnaInfo;
                addDTData(call);
                MainModel.IsFileReplayByCompareMode = false;
                MainModel.FireDTDataChanged(this);
                MainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
            }
        }

        private void addDTData(NRDropCallAnaInfo call)
        {
            foreach (TestPoint tp in call.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach (Event evt in call.Events)
            {
                MainModel.DTDataManager.Add(evt);
            }
            foreach (Model.Message msg in call.Messages)
            {
                MainModel.DTDataManager.Add(msg);
            }
        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            rows.Add(row);
            row.AddCellValue("序号");
            row.AddCellValue("文件名");
            row.AddCellValue("主叫/被叫");
            row.AddCellValue("是否掉话");
            row.AddCellValue("掉话原因");
            row.AddCellValue("掉话经度");
            row.AddCellValue("掉话纬度");
            row.AddCellValue("掉话时间");
            row.AddCellValue(colRsrp.Caption);
            row.AddCellValue(colSinr.Caption);
            row.AddCellValue(colLteRsrp.Caption);
            row.AddCellValue(colLteSinr.Caption);
            row.AddCellValue("高重叠覆盖占比(%)");
            row.AddCellValue(colHoNum.Caption);

            foreach (NRDropCallInfo dc in calls)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(dc.SN);
                foreach (NRDropCallAnaInfo call in dc.MoMtCalls)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(call.FileName);
                    subRow.AddCellValue(call.MoMtDesc);
                    subRow.AddCellValue(call.IsDropCall);
                    subRow.AddCellValue(call.DropCause);
                    if (call.IsDropCall)
                    {
                        subRow.AddCellValue(call.DropEvt.Longitude);
                        subRow.AddCellValue(call.DropEvt.Latitude);
                    }
                    else
                    {
                        subRow.AddCellValue("");
                        subRow.AddCellValue("");
                    }
                    subRow.AddCellValue(call.DropTime);

                    if (call.RsrpInfo.Avg == null)
                    {
                        subRow.AddCellValue("");
                    }
                    else
                    {
                        subRow.AddCellValue(call.RsrpInfo.Avg);
                    }

                    if (call.SinrInfo.Avg == null)
                    {
                        subRow.AddCellValue("");
                    }
                    else
                    {
                        subRow.AddCellValue(call.SinrInfo.Avg);
                    }

                    if (call.LteRsrpInfo.Avg == null)
                    {
                        subRow.AddCellValue("");
                    }
                    else
                    {
                        subRow.AddCellValue(call.LteRsrpInfo.Avg);
                    }

                    if (call.LteSinrInfo.Avg == null)
                    {
                        subRow.AddCellValue("");
                    }
                    else
                    {
                        subRow.AddCellValue(call.LteSinrInfo.Avg);
                    }

                    if (call.MultiCvrInfo.Avg == null)
                    {
                        subRow.AddCellValue("");
                    }
                    else
                    {
                        subRow.AddCellValue(call.MultiCvrInfo.Avg);
                    }

                    subRow.AddCellValue(call.HoNum);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void miExportXlsSum_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(viewSummary);
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gridView.RowCount; i++)
            {
                gridView.ExpandMasterRow(i);
            }
        }

        private void toolStripMenuItem3_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gridView.RowCount; i++)
            {
                gridView.CollapseMasterRow(i);
            }
        }
    }
}
