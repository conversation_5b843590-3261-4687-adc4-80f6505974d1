﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    class BtsFusionDataQuery_XJ
    {
        protected BtsFusionDataQuery_XJ()
        {
        }

        public static BtsFusionInfo_XJ GetFusionInfos(BtsWorkParam_XJ btsWorkParamInfo
            , DateTime beginTime, DateTime endTime)
        {
            string cgiConditions = getCgiConditions(btsWorkParamInfo.CellWorkParams);

            QueryFusionPerfData_XJ perfQuery = new QueryFusionPerfData_XJ(beginTime, endTime, cgiConditions);
            perfQuery.Query();

            QueryFusionMRData_XJ mrQuery = new QueryFusionMRData_XJ(beginTime, endTime, cgiConditions);
            mrQuery.Query();

            QueryFusionAlarmData_XJ alarmQuery = new QueryFusionAlarmData_XJ(beginTime, endTime, btsWorkParamInfo.BtsName);
            alarmQuery.Query();

            BtsFusionInfo_XJ btsFusionInfo = new BtsFusionInfo_XJ(btsWorkParamInfo, beginTime, endTime);
            btsFusionInfo.CellPerfInfoDic = perfQuery.PerfInfoDic;
            btsFusionInfo.CellMRInfoDic = mrQuery.MRInfoDic;
            Dictionary<string, BtsAlarmDataBase> btsAlarmInfoDic;
            alarmQuery.AlarmInfoDic.TryGetValue(btsWorkParamInfo.BtsName, out btsAlarmInfoDic);
            btsFusionInfo.BtsAlarmInfoDic = btsAlarmInfoDic;
            return btsFusionInfo;
        }
        protected static string getCgiConditions(List<CellWorkParamBase> cellWorkParamList)
        {
            StringBuilder strb = new StringBuilder();
            string strCgiMidChars = "'',''";
            foreach (CellWorkParamBase cellInfo in cellWorkParamList)
            {
                strb.Append(cellInfo.CGI + strCgiMidChars);
            }
            if (strb.Length > 0)
            {
                strb.Remove(strb.Length - strCgiMidChars.Length, strCgiMidChars.Length);
            }
            return strb.ToString();
        }
    }

    public class QueryFusionPerfData_XJ : DIYSQLBase
    {
        protected DateTime beginTime;
        protected DateTime endTime;
        protected string cgiConditions;

        //<cgi,Dictionary<日期,CellPerfDataBase>>
        public Dictionary<string, Dictionary<string, CellPerfDataBase>> PerfInfoDic { get; set; } = new Dictionary<string, Dictionary<string, CellPerfDataBase>>();
        public QueryFusionPerfData_XJ(DateTime beginTime, DateTime endTime, string cgiConditions)
            : base()
        {
            MainDB = true;
            this.beginTime = beginTime;
            this.endTime = endTime;
            this.cgiConditions = cgiConditions;
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"exec sp_xinjiang_fusionPerf_get '{0}','{1}','{2}'"
                , beginTime.ToString(), endTime.ToString(), cgiConditions);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[12];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i] = E_VType.E_Float;
            return arr;
        }
        protected virtual CellPerfDataBase getInitCellPerfData()
        {
            return new CellPerfData_XJ();
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellPerfDataBase cellPerf = getInitCellPerfData();
                    cellPerf.Fill(package.Content);

                    Dictionary<string, CellPerfDataBase> cellPerfDatas;
                    if (!PerfInfoDic.TryGetValue(cellPerf.CGI, out cellPerfDatas))
                    {
                        cellPerfDatas = new Dictionary<string, CellPerfDataBase>();
                        PerfInfoDic.Add(cellPerf.CGI, cellPerfDatas);
                    }
                    cellPerfDatas[cellPerf.BeginDateDes] = cellPerf;
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }
    public class QueryFusionMRData_XJ : DIYSQLBase
    {
        protected DateTime beginTime;
        protected DateTime endTime;
        protected string cgiConditions;

        //<cgi,Dictionary<日期,CellMRDataBase>>
        public Dictionary<string, Dictionary<string, CellMRDataBase>> MRInfoDic { get; set; } = new Dictionary<string, Dictionary<string, CellMRDataBase>>();
        public QueryFusionMRData_XJ(DateTime beginTime, DateTime endTime, string cgiConditions)
            : base()
        {
            MainDB = true;
            this.beginTime = beginTime;
            this.endTime = endTime;
            this.cgiConditions = cgiConditions;
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"exec sp_xinjiang_fusionMr_get '{0}','{1}','{2}'"
                , beginTime.ToString(), endTime.ToString(), cgiConditions);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[3];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_Float;
            return arr;
        }
        protected virtual CellMRDataBase getInitCellPerfData()
        {
            return new CellMRData_XJ();
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellMRDataBase cellMR = getInitCellPerfData();
                    cellMR.Fill(package.Content);

                    Dictionary<string, CellMRDataBase> cellMrDatas;
                    if (!MRInfoDic.TryGetValue(cellMR.CGI, out cellMrDatas))
                    {
                        cellMrDatas = new Dictionary<string, CellMRDataBase>();
                        MRInfoDic.Add(cellMR.CGI, cellMrDatas);
                    }
                    cellMrDatas[cellMR.BeginDateDes] = cellMR;
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }
    public class QueryFusionAlarmData_XJ : DIYSQLBase
    {
        protected DateTime beginTime;
        protected DateTime endTime;
        protected string btsNameConditions;

        //<基站名,Dictionary<日期,BtsAlarmDataBase>>
        public Dictionary<string, Dictionary<string, BtsAlarmDataBase>> AlarmInfoDic { get; set; } = new Dictionary<string, Dictionary<string, BtsAlarmDataBase>>();
        public QueryFusionAlarmData_XJ(DateTime beginTime, DateTime endTime, string btsNameConditions)
            : base()
        {
            MainDB = true;
            this.beginTime = beginTime;
            this.endTime = endTime;
            this.btsNameConditions = btsNameConditions;
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"exec sp_xinjiang_fusionAlarm_get '{0}','{1}','{2}'"
                , beginTime.ToString(), endTime.ToString(), btsNameConditions);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[6];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }
        protected virtual BtsAlarmDataBase getInitBtsAlarmData()
        {
            return new BtsAlarmData_XJ();
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    BtsAlarmDataBase cellAlarm = getInitBtsAlarmData();
                    cellAlarm.Fill(package.Content);

                    Dictionary<string, BtsAlarmDataBase> cellAlarmDatas;
                    if (!AlarmInfoDic.TryGetValue(cellAlarm.BtsName, out cellAlarmDatas))
                    {
                        cellAlarmDatas = new Dictionary<string, BtsAlarmDataBase>();
                        AlarmInfoDic.Add(cellAlarm.BtsName, cellAlarmDatas);
                    }
                    cellAlarmDatas[cellAlarm.BeginDateDes] = cellAlarm;
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }
}
