﻿using DevExpress.XtraCharts;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTAntFuncHelper
    {
        #region 基础函数
        /// <summary>
        /// 广东地市类别
        /// </summary>
        public static string getCityLevel(string cityName)
        {
            string strCityLeve = "";
            if (cityName.Equals("广州") || cityName.Equals("深圳") || cityName.Equals("东莞") || cityName.Equals("佛山"))
                strCityLeve = "一类";
            else if (cityName.Equals("惠州") || cityName.Equals("珠海") || cityName.Equals("江门") || cityName.Equals("中山")
                || cityName.Equals("汕头") || cityName.Equals("湛江") || cityName.Equals("揭阳") || cityName.Equals("茂名"))
                strCityLeve = "二类";
            else if (cityName.Equals("梅州") || cityName.Equals("清远") || cityName.Equals("汕尾") || cityName.Equals("韶关")
                || cityName.Equals("阳江") || cityName.Equals("云浮") || cityName.Equals("肇庆") || cityName.Equals("河源")
                || cityName.Equals("潮州"))
                strCityLeve = "三类";
            else
                strCityLeve = " ";
            return strCityLeve;
        }

        /// <summary>
        /// 获取距离级别
        /// </summary>
        public static int getDistanceLevel(double dDist)
        {
            int iLevel = 0;
            if (dDist < 500)
            {
                iLevel = (int)(dDist / 5);
            }
            else
            {
                iLevel = (int)(dDist - 500) / 50 + 100;
            }
            return iLevel;
        }

        /// <summary>
        ///GSM频段类型 
        /// </summary>
        public static string getFreqType(int Freq)
        {
            if ((Freq >= 0 && Freq <= 124))
            {
                return "900";
            }
            else if ((Freq >= 512 && Freq <= 975) || (Freq >= 976 && Freq <= 1024))
            {
                return "1800";
            }
            else
            {
                return "未知频段";
            }
        }

        /// <summary>
        /// 获取小区区间
        /// </summary>
        public static string GetAngleSection(int angle)
        {
            if (angle <= 15)
            {
                return "[0,15]";
            }
            if (angle <= 30)
            {
                return "(15,30]";
            }
            else
            {
                int num = (int)Math.Ceiling(angle / 30.0);
                return "(" + (num - 1) * 30 + "," + (num) * 30 + "]";
            }
        }

        /// <summary>
        /// 覆盖朝向
        /// </summary>
        public static string GetCoverSection(int angle)
        {
            if (angle <= 60)
            {
                return "主瓣";
            }
            if (angle <= 150)
            {
                return "旁瓣";
            }
            else
            {
                return "背瓣";
            }
        }

        /// <summary>
        /// 批量导出CSV文件
        /// </summary>
        public static void OutputCsvFile(List<List<NPOIRow>> nrDatasList, List<string> sheetNames)
        {
            string fileName;
            if (!ExportResultSecurityHelper.GetExportPermit(FileSimpleTypeHelper.Csv, out fileName))
            {
                return;
            }
            fileName = fileName.Substring(0, fileName.Length - 4);

            try
            {
                int iSheetNum = nrDatasList.Count;
                for (int i = 0; i < iSheetNum; i++)
                {
                    List<NPOIRow> npoiList = nrDatasList[i];
                    if (sheetNames.Count < i + 1)
                        break;

                    string strFileSubName = fileName + "_" + sheetNames[i] + ".csv";
                    if (System.IO.File.Exists(strFileSubName))
                        System.IO.File.Delete(strFileSubName);

                    System.IO.FileStream fileStream = new System.IO.FileStream(strFileSubName, System.IO.FileMode.CreateNew, System.IO.FileAccess.Write, System.IO.FileShare.Read);
                    System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);
                    addRowInfo(npoiList, streamWriter);

                    streamWriter.Close();
                    fileStream.Close();
                }
                MessageBox.Show("导出完毕");
            }
            catch
            {
                MessageBox.Show("导出有异常");
            }
        }

        private static void addRowInfo(List<NPOIRow> npoiList, System.IO.StreamWriter streamWriter)
        {
            foreach (NPOIRow nrow in npoiList)
            {
                int iCellNum = nrow.cellValues.Count;
                StringBuilder sb = new StringBuilder();
                for (int j = 0; j < iCellNum; j++)
                {
                    object cellValue = nrow.cellValues[j];
                    if (cellValue.ToString().IndexOf(",") >= 0)
                    {
                        cellValue = cellValue.ToString().Replace(",", "，");
                    }

                    sb.Append(cellValue + ",");
                }
                streamWriter.WriteLine(sb.ToString());
            }
        }

        #endregion

        #region 基础计算函数

        /// <summary>
        /// 计算数据均值
        /// </summary>
        public static float CalcValueAvg(float fValue, int iBaseValue)
        {
            try
            {
                if (iBaseValue == 0)
                    return 0;
                else
                    return (float)(Math.Round(fValue / iBaseValue, 2));
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 获取数组中的最大及最小值
        /// </summary>
        public static void getMaxAndMinValue(double[] seriesValues, ref int iMaxValue, ref int iMinValue)
        {
            foreach (double tmpValue in seriesValues)
            {
                if (tmpValue > iMaxValue)
                {
                    iMaxValue = (int)tmpValue + 1;
                }
                if (tmpValue < iMinValue)
                {
                    iMinValue = (int)tmpValue - 1;
                }
            }
        }

        /// <summary>
        /// 获取数组中的最大及最小值
        /// </summary>
        public static int getMaxValue(int[] seriesValues)
        {
            int iMaxValue = 1;
            foreach (double tmpValue in seriesValues)
            {
                if (tmpValue > iMaxValue)
                {
                    iMaxValue = (int)tmpValue + 1;
                }
            }
            return iMaxValue;
        }

        /// <summary>
        /// 获取数组中的最大及最小值
        /// </summary>
        public static int getMaxValue(double[] seriesValues)
        {
            if (seriesValues == null)
                return 0;

            int iMaxValue = 1;
            foreach (double tmpValue in seriesValues)
            {
                if (tmpValue > iMaxValue)
                {
                    iMaxValue = (int)tmpValue + 1;
                }
            }
            return iMaxValue;
        }

        /// <summary>
        /// 获取数组中的最大及最小值
        /// </summary>
        public static int getMaxValue(Dictionary<string, object> dataDic)
        {
            int iMaxValue = 1;
            foreach (string strKey in dataDic.Keys)
            {
                object obj = dataDic[strKey];
                double tmpValue;
                if (double.TryParse(obj.ToString(), out tmpValue) && tmpValue > iMaxValue)
                {
                    iMaxValue = (int)tmpValue + 1;
                }
            }
            return iMaxValue;
        }

        /// <summary>
        /// 计算指标占比
        /// </summary>
        public static float CalcKPIRate(int iValue, int iBaseValue)
        {
            try
            {
                if (iBaseValue == 0)
                    return 0;
                else
                    return (float)(Math.Round(iValue * 100.0 / iBaseValue, 2));
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 取两数组中的大值
        /// </summary>
        public static double[] getMaxPowerArray(double[] power1Array, double[] power2Array)
        {
            double[] powerArray = new double[180];
            for (int i = 0; i < 180; i++)
            {
                double dTmp = power1Array[i] >= power2Array[i] ? power1Array[i] : power2Array[i];
                powerArray[i] = dTmp > -120 ? dTmp : -120;
            }
            return powerArray;
        }

        /// <summary>
        /// 获取归一化RSRP的平均值
        /// </summary>
        public static double getAvgRsrp(double[] rsrpUnifiedArray, int k)
        {
            double dMeanRsrp = 0;
            int iNum = 0;
            double dSumRsrp = 0;
            for (int i = -2; i <= 2; i++)
            {
                int iTmp = (i + k + 360) % 360;
                iNum++;
                dSumRsrp += rsrpUnifiedArray[iTmp];
            }
            dMeanRsrp = dSumRsrp / iNum;
            return dMeanRsrp;
        }

        
        #endregion

        #region 覆盖仿真类函数

        /// <summary>
        /// 小区覆盖仿真采样点
        /// </summary>
        public static List<LongLat> getCellEmulateCover(LongLat btsLongLat, int iangle_dir, int ialtitude, int iangle_ob)
        {
            List<LongLat> cellEmulateList = new List<LongLat>();
            float coverDistance = 0;
            if (iangle_ob > 0 && iangle_ob < 90)
            {
                coverDistance = (float)(ialtitude / Math.Tan(iangle_ob * Math.PI / 180));
            }
            if (coverDistance < 300)
                coverDistance = 300;
            else if (coverDistance > 1000)
                coverDistance = 1000;

            int sDir = iangle_dir - 60;
            int eDir = iangle_dir + 60;

            for (int i = sDir; i <= eDir; i += 5)
            {
                int tDir = (i + 360) % 360;
                LongLat tLongLat = calcPointX(tDir, coverDistance, btsLongLat);
                cellEmulateList.Add(tLongLat);

            }
            return cellEmulateList;
        }

        /// <summary>
        /// 小区测试覆盖仿真
        /// </summary>
        public static List<LongLat> getCellEmulateCoverTest(LongLat btsLongLat, double[] array, double dMax, double dMin, int iangle_dir)
        {
            List<LongLat> cellEmulateList = new List<LongLat>();
            float coverDistance = 500;
            for (int i = 0; i < 360; i++)
            {
                int tDir = (i + iangle_dir + 360) % 360;
                double d = array[i] > dMin ? array[i] - dMin : 0;
                float tmpDistance = (float)(coverDistance * d / (dMax - dMin));
                LongLat tLongLat = calcPointX(tDir, tmpDistance, btsLongLat);
                cellEmulateList.Add(tLongLat);
            }
            return cellEmulateList;
        }

        /// <summary>
        /// 小区覆盖理想覆盖模型
        /// </summary>
        public static List<LongLat> getCellEmulateCoverModel(LongLat btsLongLat, double[] array, double dMax, double dMin, int iangle_dir)
        {
            List<LongLat> cellEmulateList = new List<LongLat>();
            float coverDistance = 500;
            for (int i = 0; i < 180; i++)
            {
                int tDir = (i - 90 + iangle_dir + 360) % 360;
                double d = array[i] > dMin ? array[i] - dMin : 0;
                float tmpDistance = (float)(coverDistance * d / (dMax - dMin));
                LongLat tLongLat = calcPointX(tDir, tmpDistance, btsLongLat);
                cellEmulateList.Add(tLongLat);
            }
            return cellEmulateList;
        }

        /// <summary>
        /// 小区MR覆盖模型
        /// </summary>
        public static List<LongLat> getCellMrCover(LongLat btsLongLat, double[] array, double dMax, double dMin, int iangle_dir)
        {
            List<LongLat> cellEmulateList = new List<LongLat>();
            float coverDistance = 500;
            for (int i = 0; i < 72; i++)
            {
                int j = i * 5;
                int tDir = (j + iangle_dir + 360) % 360;
                double d = array[i] > dMin ? array[i] - dMin : 0;
                float tmpDistance = (float)(coverDistance * d / (dMax - dMin));
                LongLat tLongLat = calcPointX(tDir, tmpDistance, btsLongLat);
                cellEmulateList.Add(tLongLat);
            }
            return cellEmulateList;
        }


        #endregion

        #region 几何计算类函数

        /// <summary>
        /// 计算两天线夹角
        /// </summary>
        public static int CalcAntDir(int iAnt1, int iAnt2)
        {
            int iNewDir = 0;
            int iDir = (iAnt1 - iAnt2 + 360) % 360;
            if (iDir > 180)
                iNewDir = 360 - iDir;
            else
                iNewDir = iDir;
            return iNewDir;
        }

        /// <summary>
        /// 粗略定位另一点信息
        /// </summary>
        /// <param name="iangle">夹角</param>
        public static LongLat calcPointX(int iangle, float idis, LongLat s)
        {
            LongLat e = new LongLat();
            double a = Math.Cos((90 - iangle) * 2 * Math.PI / 360);
            double b = Math.Sin((90 - iangle) * 2 * Math.PI / 360);
            e.fLongitude = s.fLongitude + (float)(a * (idis / 40075360) * 360);//32507969.15
            e.fLatitude = s.fLatitude + (float)(b * (idis / 39940670) * 360); //40172187.93
            return e;
        }

        /// <summary>
        /// 计算两个点间的夹角
        /// </summary>
        public static double calcTwoPointAngle(double longitudeA, double latitudeA, double longitudeB, double latitudeB)
        {
            return MathFuncs.getAngleFromPointToPoint_D(longitudeA, latitudeA, longitudeB, latitudeB);
        }

        /// <summary>
        /// 获取采样点与小区的夹角
        /// </summary>
        /// <param name="itype">0为相对夹角，1为相对夹角(绝对值)</param>
        public int calcSampleAngle(Cell cell, TDCell tdCell, LTECell lteCell, double longitude, double latitude, int itype)
        {
            //所有角度按正北方向算起始，顺时针算夹角，正北为0度
            double angleDiff = 0;
            double distance = 0;
            double angle = 0;
            double ygap = 0;
            double cellLog = 0;
            double cellLat = 0;
            short direction = 0;
            if (cell != null)
            {
                distance = cell.GetDistance(longitude, latitude);
                ygap = cell.GetDistance(cell.Longitude, latitude);
                cellLog = cell.Longitude;
                cellLat = cell.Latitude;
                direction = cell.Direction;
            }
            else if (tdCell != null)
            {
                distance = tdCell.GetDistance(longitude, latitude);
                ygap = tdCell.GetDistance(tdCell.Longitude, latitude);
                cellLog = tdCell.Longitude;
                cellLat = tdCell.Latitude;
                direction = tdCell.Direction;
            }
            else if (lteCell != null)
            {
                distance = lteCell.GetDistance(longitude, latitude);
                ygap = lteCell.GetDistance(lteCell.Longitude, latitude);
                cellLog = lteCell.Longitude;
                cellLat = lteCell.Latitude;
                direction = lteCell.Direction;
            }
            angle = getAngle(longitude, latitude, distance, ygap, cellLog, cellLat);
            if (itype == 0)
            {
                angleDiff = angle - direction;
                if (angleDiff < 0)
                {
                    angleDiff = 360 + angleDiff;
                }
            }
            else
            {
                angleDiff = Math.Abs(angle - direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
            }
            return (int)angleDiff;
        }

        private static double getAngle(double longitude, double latitude, double distance, double ygap, double cellLog, double cellLat)
        {
            double angle;
            double angleV = Math.Acos(ygap / distance);
            if (longitude >= cellLog && latitude >= cellLat)//1象限
            {
                angle = angleV * 180 / Math.PI;
            }
            else if (longitude <= cellLog && latitude >= cellLat)//2象限
            {
                angle = 360 - angleV * 180 / Math.PI;
            }
            else if (longitude <= cellLog)//3象限 && latitude <= cellLat
            {
                angle = 180 + angleV * 180 / Math.PI;
            }
            else//4象限
            {
                angle = 180 - angleV * 180 / Math.PI;
            }

            return angle;
        }

        public class CellCollection
        {
            public Cell Cell { get; set; }
            public TDCell TDCell { get; set; }
            public LTECell LteCell { get; set; }
        }

        /// <summary>
        /// 获取采样点与小区的夹角
        /// </summary>
        /// <param name="angleDiffRel">相对夹角</param>
        /// <param name="angleDiffAbs">绝对夹角</param>
        public static void calcSampleAngle(CellCollection col, double longitude, double latitude,
                                   out double angleDiffRel, out double angleDiffAbs, out double distance)
        {
            //所有角度按正北方向算起始，顺时针算夹角，正北为0度
            distance = 0;
            double angle = 0;
            double ygap = 0;
            double cellLog = 0;
            double cellLat = 0;
            short direction = 0;
            if (col.Cell != null)
            {
                distance = col.Cell.GetDistance(longitude, latitude);
                ygap = GetAntDistance(col.Cell, null, latitude);
                cellLog = col.Cell.Longitude;
                cellLat = col.Cell.Latitude;
                direction = col.Cell.Direction;
            }
            else if (col.TDCell != null)
            {
                distance = col.TDCell.GetDistance(longitude, latitude);
                ygap = col.TDCell.GetDistance(col.TDCell.Longitude, latitude);
                cellLog = col.TDCell.Longitude;
                cellLat = col.TDCell.Latitude;
                direction = col.TDCell.Direction;
            }
            else if (col.LteCell != null)
            {
                distance = col.LteCell.GetDistance(longitude, latitude);
                ygap = GetAntDistance(null, col.LteCell, latitude);
                cellLog = col.LteCell.Longitude;
                cellLat = col.LteCell.Latitude;
                direction = col.LteCell.Direction;
            }
            double angleV = Math.Acos(ygap / distance);
            if (longitude >= cellLog && latitude >= cellLat)//1象限
            {
                angle = angleV * 180 / Math.PI;
            }
            else if (longitude <= cellLog && latitude >= cellLat)//2象限
            {
                angle = 360 - angleV * 180 / Math.PI;
            }
            else if (longitude <= cellLog && latitude <= cellLat)//3象限
            {
                angle = 180 + angleV * 180 / Math.PI;
            }
            else//4象限
            {
                angle = 180 - angleV * 180 / Math.PI;
            }

            direction = direction < 0 ? (short)0 : direction;//发现方位角为-255情况

            angleDiffRel = angle - direction;
            if (angleDiffRel < 0)
            {
                angleDiffRel = 360 + angleDiffRel;
            }
            if (angleDiffRel == 360)
            {
                angleDiffRel = 0;
            }

            angleDiffAbs = Math.Abs(angle - direction);
            if (angleDiffAbs > 180)
            {
                angleDiffAbs = 360 - angleDiffAbs;
            }
        }

        /// <summary>
        /// 获取两个经纬度之间，以正北方向的夹角
        /// </summary>
        /// <param name="longitude_X1">x1 经度</param>
        /// <param name="latitude_Y1">y1 纬度</param>
        /// <param name="longitude_X2">x2 经度</param>
        /// <param name="latitude_Y2">y2 纬度</param>
        /// <param name="direction">x的方向角</param>
        /// <param name="itype">0 相对夹角，1绝对夹角</param>
        public static int calcSampleAngle(double longitude_X1, double latitude_Y1, double longitude_X2, double latitude_Y2
            , short direction, int itype)
        {
            double angle = calcTwoPointAngle(longitude_X1, latitude_Y1, longitude_X2, latitude_Y2);

            double angleDiff = 0;
            if (itype == 0)
            {
                angleDiff = angle - direction;
                if (angleDiff < 0)
                {
                    angleDiff = 360 + angleDiff;
                }
            }
            else
            {
                angleDiff = Math.Abs(angle - direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
            }
            return (int)angleDiff;
        }

        /// <summary>
        /// 获取反向角
        /// </summary>
        public static float getReverseAngle(float iAngle)
        {
            float iDiff = iAngle - 180;
            if (iDiff < 0)
                iDiff = 360 + iDiff;
            return iDiff;
        }

        /// <summary>
        /// 小区天线距离计算
        /// </summary>
        private static double GetAntDistance(Cell cell, LTECell lteCell, double y)
        {
            if (cell != null)
            {
                if (cell.Antennas.Count == 0) //天线异常时用基站经纬度
                {
                    return MathFuncs.GetDistance(cell.Longitude, cell.Latitude, cell.Longitude, y);
                }
                else
                {
                    double dDistance = getAntennasDistance(cell, y);
                    return dDistance;
                }
            }
            else if (lteCell != null)
            {
                if (lteCell.Antennas.Count == 0)
                {
                    return MathFuncs.GetDistance(lteCell.Longitude, lteCell.Latitude, lteCell.Longitude, y);
                }
                else
                {
                    double dDistance = getLteAntennasDistance(lteCell, y);
                    return dDistance;
                }
            }
            else
            {
                return 0;
            }
        }

        private static double getAntennasDistance(Cell cell, double y)
        {
            double dDistance = double.MaxValue;
            foreach (Antenna antenna in cell.Antennas)
            {
                double dDistanceTmp = MathFuncs.GetDistance(antenna.EndPointLongitude, antenna.EndPointLatitude, antenna.EndPointLongitude, y);
                if (dDistanceTmp < dDistance)
                {
                    dDistance = dDistanceTmp;
                }
            }

            return dDistance;
        }

        private static double getLteAntennasDistance(LTECell lteCell, double y)
        {
            double dDistance = double.MaxValue;
            foreach (LTEAntenna antenna in lteCell.Antennas)
            {
                double dDistanceTmp = MathFuncs.GetDistance(antenna.Longitude, antenna.Latitude, antenna.Longitude, y);
                if (dDistanceTmp < dDistance)
                {
                    dDistance = dDistanceTmp;
                }
            }

            return dDistance;
        }


        /// <summary>
        /// 计算垂直面夹角
        /// </summary>
        public static double calcVertAngle(LTECell lteCell, float fDistance, double dDownward)
        {
            if (fDistance <= 0)
                return 90;

            double vertAngle = 0;
            double dHeight = lteCell.Antennas[0].Altitude <= 20 ? 20 : lteCell.Antennas[0].Altitude;

            double angleV = Math.Atan(dHeight / fDistance);
            double angle = angleV * 180 / Math.PI;
            vertAngle = Math.Abs(angle - dDownward);

            return vertAngle;
        }

        #endregion

        #region MR处理算法
        /// <summary>
        /// 计算各个角度的采样点数
        /// </summary>
        public static Dictionary<int, int> GetDirSample(ZTLteAntMRAna.AnaRttdAoaArray ary)
        {
            Dictionary<int, int> tmpDic = new Dictionary<int, int>();
            for (int j = 0; j < 72; j++)
            {
                float fNum = 0;
                for (int i = 0; i < 44; i++)
                {
                    float ia = 1;
                    if (i < 5)
                        ia = 1.5f;
                    else if (i < 10)
                        ia = 1.4f;
                    else if (i < 20)
                        ia = 1.3f;
                    else if (i < 30)
                        ia = 1.2f;
                    else if (i < 40)
                        ia = 1.1f;

                    if (ary.AnaRttdAoa90[i, j] > 0)
                    {
                        fNum += ary.AnaRttdAoa90[i, j] * ia;
                    }
                }
                tmpDic.Add(j, (int)fNum);
            }
            return tmpDic;
        }

        /// <summary>
        /// 平滑处理
        /// </summary>
        public static Dictionary<int, int> GetNewDirSample(Dictionary<int, int> dirSampleDic)
        {
            Dictionary<int, int> newDirSampleDic = new Dictionary<int, int>();
            foreach (int i in dirSampleDic.Keys)
            {
                int b = ((i - 1) + 72) % 72;
                int c = ((i + 1) + 72) % 72;
                int iNum = 0;
                int iSum = 0;
                if (dirSampleDic.ContainsKey(i))
                {
                    iNum++;
                    iSum += dirSampleDic[i];
                }
                if (dirSampleDic.ContainsKey(b))
                {
                    iNum++;
                    iSum += dirSampleDic[b];
                }
                if (dirSampleDic.ContainsKey(c))
                {
                    iNum++;
                    iSum += dirSampleDic[c];
                }
                if (iNum > 0)
                {
                    newDirSampleDic.Add(i, iSum / iNum);
                }
            }
            return newDirSampleDic;
        }

        /// <summary>
        /// 获取MR最大方向角
        /// </summary>
        public static int GetAntMaxDir(Dictionary<int, int> tmpDic)
        {
            if (tmpDic.Count == 0)
                return -1;

            int iMaxDir = 0;
            int[] antDir = new int[72];
            int iZero = 0;
            float fSum = 0;
            foreach (int i in tmpDic.Keys)
            {
                antDir[i] = tmpDic[i];
                fSum += tmpDic[i];
                if (tmpDic[i] == 0)
                    iZero++;
            }

            if (iZero >= 67 || fSum < 1000)
                return -1;

            int iMaxDir1 = 0;
            float fMaxNum1 = 0;
            int iMaxDir2 = 0;
            float fMaxNum2 = 0;
            int iMaxDir3 = 0;
            float fMaxNum3 = 0;

            getMaxInfo(antDir, ref iMaxDir1, ref fMaxNum1, ref iMaxDir2, ref fMaxNum2, ref iMaxDir3, ref fMaxNum3);

            fMaxNum2 = (fMaxNum2 / 3) * 1.2f;
            fMaxNum3 = (fMaxNum3 / 5) * 1.3f;

            if (fMaxNum1 >= fMaxNum2 && fMaxNum1 >= fMaxNum3)
                iMaxDir = iMaxDir1;
            else if (fMaxNum2 >= fMaxNum1 && fMaxNum2 >= fMaxNum3)
                iMaxDir = iMaxDir2;
            else
                iMaxDir = iMaxDir3;

            return iMaxDir * 5;
        }

        private static void getMaxInfo(int[] antDir, ref int iMaxDir1, ref float fMaxNum1, ref int iMaxDir2, 
            ref float fMaxNum2, ref int iMaxDir3, ref float fMaxNum3)
        {
            for (int i = 0; i < 72; i++)
            {
                if (fMaxNum1 < antDir[i])
                {
                    iMaxDir1 = i;
                    fMaxNum1 = antDir[i];
                }

                int a = ((i - 2) + 72) % 72;
                int b = ((i - 1) + 72) % 72;
                int c = ((i + 1) + 72) % 72;
                int d = ((i + 2) + 72) % 72;

                if (fMaxNum2 < antDir[b] + antDir[i] + antDir[c])
                {
                    iMaxDir2 = i;
                    fMaxNum2 = antDir[b] + antDir[i] + antDir[c];
                }

                if (fMaxNum3 < antDir[a] + antDir[b] + antDir[i] + antDir[c] + antDir[d])
                {
                    iMaxDir3 = i;
                    fMaxNum3 = antDir[a] + antDir[b] + antDir[i] + antDir[c] + antDir[d];
                }
            }
        }

        /// <summary>
        /// 按时间差计算距离(LTE)
        /// </summary>
        public static int calcDistByLteMrTa(int iTA)
        {
            if (iTA < 0)
                return 0;

            int iTsNum = 0;
            if (iTA < 12)
            {
                iTsNum = (iTA + 1) * 16 - 1;
            }
            else if (iTA < 38)
            {
                iTsNum = 191 + (iTA - 11) * 32;
            }
            else if (iTA < 42)
            {
                iTsNum = 1023 + (iTA - 37) * 256;
            }
            else if (iTA < 44)
            {
                iTsNum = 2047 + (iTA - 41) * 1024;
            }
            else
            {
                return 4096;
            }

            return (int)(3 * 3.26 / 2 * iTsNum);
        }

        /// <summary>
        /// 按时间差计算距离(TD)
        /// </summary>
        public static int calcDistByTdMrTa(int iTA)
        {
            if (iTA < 0)
                return 0;

            float iChip = 0;
            if (iTA < 8)
            {
                iChip = (iTA + 1) * 0.5f;
            }
            else if (iTA < 36)
            {
                iChip = 4 + (iTA - 7);
            }
            else
            {
                iChip = 32;
            }
            float dDist = (300 / 1.28f) * iChip;
            return (int)dDist;
        }

        /// <summary>
        /// MR数据等级
        /// </summary>
        public static int GetMRDataLevel(int iSampleNum)
        {
            int iLevel = 0;
            if (iSampleNum < 500)
                iLevel = 1;
            else if (iSampleNum < 1000)
                iLevel = 2;
            else if (iSampleNum < 5000)
                iLevel = 3;
            else if (iSampleNum < 10000)
                iLevel = 4;
            else if (iSampleNum < 20000)
                iLevel = 5;
            else if (iSampleNum < 30000)
                iLevel = 6;
            else if (iSampleNum < 50000)
                iLevel = 7;
            else if (iSampleNum < 70000)
                iLevel = 8;
            else if (iSampleNum < 100000)
                iLevel = 9;
            else
                iLevel = 10;
            return iLevel;
        }

        /// <summary>
        /// 判断是否需要呈现的区间
        /// </summary>
        public static bool checkLegendIsVaild(int iColor, string cellPart)
        {
            if (iColor == 2)
            {
                if (cellPart == "(0%,50%]")
                    return false;
            }
            else if (iColor == 3)
            {
                if (cellPart == "(0%,50%]" || cellPart == "(0%,70%]")
                    return false;
            }
            else if (iColor == 4)
            {
                if (cellPart == "(0%,50%]" || cellPart == "(0%,70%]" || cellPart == "(0%,80%]")
                    return false;
            }
            else
            {
                return true;
            }
            return true;
        }

        /// <summary>
        /// 按等级获取图例
        /// </summary>
        public static AntLegend GetMRDataLegend(int iLevel)
        {
            AntLegend legend = new AntLegend();
            if (iLevel == 1)
            {
                legend.strLegend = "[0,500)";
                legend.colorType = Color.LightCyan;
            }
            else if (iLevel == 2)
            {
                legend.strLegend = "[500,1000)";
                legend.colorType = Color.Cyan;
            }
            else if (iLevel == 3)
            {
                legend.strLegend = "[1000,5000)";
                legend.colorType = Color.SkyBlue;
            }
            else if (iLevel == 4)
            {
                legend.strLegend = "[5000,10000)";
                legend.colorType = Color.Blue;
            }
            else if (iLevel == 5)
            {
                legend.strLegend = "[10000,20000)";
                legend.colorType = Color.DarkBlue;
            }
            else if (iLevel == 6)
            {
                legend.strLegend = "[20000,30000)";
                legend.colorType = Color.Orange;
            }
            else if (iLevel == 7)
            {
                legend.strLegend = "[30000,50000)";
                legend.colorType = Color.Tomato;
            }
            else if (iLevel == 8)
            {
                legend.strLegend = "[50000,70000)";
                legend.colorType = Color.Red;
            }
            else if (iLevel == 9)
            {
                legend.strLegend = "[70000,100000)";
                legend.colorType = Color.DarkRed;
            }
            else
            {
                legend.strLegend = "[100000,+∞）";
                legend.colorType = Color.Black;
            }

            return legend;
        }

        /// <summary>
        /// 按等级获取图例
        /// </summary>
        public static AntLegend GetMRDataAnaLegend(int iLevel)
        {
            AntLegend legend = new AntLegend();
            if (iLevel == 1)
            {
                legend.strLegend = "(0%,50%]";
                legend.colorType = Color.Green;
            }
            else if (iLevel == 2)
            {
                legend.strLegend = "(50%,70%]";
                legend.colorType = Color.LightGreen;
            }
            else if (iLevel == 3)
            {
                legend.strLegend = "(70%,80%]";
                legend.colorType = Color.Yellow;
            }
            else if (iLevel == 4)
            {
                legend.strLegend = "(80%,90%]";
                legend.colorType = Color.Red;
            }
            return legend;
        }

        /// <summary>
        /// 获取扫频结构优化图例
        /// </summary>
        public static AntLegend GetScanStructLegend(int iRsrp,int iCover,int iRsrpSplit)
        {
            AntLegend legend = new AntLegend();
            if (iRsrp <= iRsrpSplit && iCover <= 2)//强覆盖Mod3
            {
                legend.colorType = Color.LightGreen;
            }
            else if (iRsrp <= iRsrpSplit && iCover > 2)//强覆盖高重叠
            {
                legend.colorType = Color.Violet;
            }
            else if (iRsrp > iRsrpSplit && iCover <= 2)//弱覆盖低重叠
            {
                legend.colorType = Color.Orange;
            }
            else
            {
                legend.colorType = Color.SkyBlue;
            }
            return legend;
        }

        /// <summary>
        /// 获取小区着色方案
        /// </summary>
        public static Color getCellColor(int iSector)
        {
            if (iSector % 10 == 0)
                return Color.Red;
            else if (iSector % 10 == 1)
                return Color.Blue;
            else if (iSector % 10 == 2)
                return Color.Green;
            else if (iSector % 10 == 3)
                return Color.Orange;
            else if (iSector % 10 == 4)
                return Color.DarkBlue;
            else if (iSector % 10 == 5)
                return Color.DarkGreen;
            else if (iSector % 10 == 6)
                return Color.DarkOrange;
            else
                return Color.Yellow;
        }

        /// <summary>
        /// 绘制雷达图直线
        /// </summary>
        public static Series DrawRadarLine(Color lineColor, string legendText, int iDir, int iMaxValue)
        {
            RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
            lineSeriesView.Color = lineColor;
            lineSeriesView.LineMarkerOptions.Size = 2;

            Series series = new Series();
            series.ShowInLegend = true;
            series.LegendText = legendText;
            series.PointOptions.PointView = PointView.Values;

            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            series.Points.Add(new SeriesPoint(iDir, 0));
            series.Points.Add(new SeriesPoint(iDir, iMaxValue));
            return series;
        }

        #endregion
    }
}
