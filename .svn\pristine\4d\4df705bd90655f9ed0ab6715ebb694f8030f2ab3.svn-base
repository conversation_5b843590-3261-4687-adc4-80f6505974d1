﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class DIYQueryLteTddStationAuditData : DiyQueryDataBase
    {
        public string tableName { get; set; } = "tb_xinjiang_LteTddStationAuditData";
        public List<LteTddStationAuditDataDBInfo> LteTddStationAuditDataDBInfoList { get; private set; }

        public DIYQueryLteTddStationAuditData()
            : base()
        { }

        public override string Name { get { return "查询LteTdd站审数据"; } }

        protected override string getSqlTextString()
        {
            string name = $"{tableName}_" + DateTime.Now.ToString("yyyyMMdd");
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat(@"SELECT [基站名称],[小区名称],[区县],[经度],[纬度],[天线挂高]
,[方位角],[机械下倾角],[电子下倾角] FROM {0} where [基站名称]='{1}'", name, btsName);

            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[9];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx] = E_VType.E_Float;
            return rType;
        }

        protected override void initData()
        {
            LteTddStationAuditDataDBInfoList = new List<LteTddStationAuditDataDBInfo>();
        }

        protected override void dealReceiveData(Package package)
        {
            LteTddStationAuditDataDBInfo info = new LteTddStationAuditDataDBInfo();
            info.FillData(package);
            LteTddStationAuditDataDBInfoList.Add(info);
        }
    }

    public class LteTddStationAuditDataDBInfo
    {
        public string BtsName { get; set; }
        public string CellName { get; set; }
        public string Country { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public double Altitude { get; set; }
        public double Direction { get; set; }
        public double Downtilt { get; set; }
        public double MechanicalTilt { get; set; }
        //public double Downward { get; set; }

        public int BBU { get; set; }
        public int RRU { get; set; }

        public void FillData(Package package)
        {
            BtsName = package.Content.GetParamString();
            CellName = package.Content.GetParamString();
            Country = package.Content.GetParamString();
            Longitude = package.Content.GetParamInt() / 10000000d;
            Latitude = package.Content.GetParamInt() / 10000000d;
            Altitude = package.Content.GetParamFloat();
            Direction = package.Content.GetParamFloat();
            Downtilt = package.Content.GetParamFloat();
            MechanicalTilt = package.Content.GetParamFloat();
            //Downward = Downtilt + MechanicalTilt;
        }
    }
}
