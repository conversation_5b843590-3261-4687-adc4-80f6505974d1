﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class PoorQualityRoadSettingForm : BaseDialog
    {
        public PoorQualityRoadSettingForm()
        {
            InitializeComponent();
        }

        public PoorQualityRoadSettingForm(string qualityLabel, int minBler, int roadDistance, int sampleDistance ) : this()
        {
            label1.Text = qualityLabel;
            numRxQualityThreshold.Value = minBler;
            numDistance.Value = roadDistance;
            numMaxDistance.Value = sampleDistance;
        }

        public void GetSettingFilterRet(out int minBler, out int roadDistance, out int sampleDistance,out bool isChkPercent,out double dPercent)
        {
            minBler = (int)numRxQualityThreshold.Value;
            roadDistance = (int)numDistance.Value;
            sampleDistance = (int)numMaxDistance.Value;
            isChkPercent = chkPercent.Checked;
            dPercent = (double)numPercent.Value;
        }

        public void GetSettingFilterRet(out double minBler, out double roadDistance, out double sampleDistance)
        {
            minBler = (double)numRxQualityThreshold.Value;
            roadDistance = (double)numDistance.Value;
            sampleDistance = (double)numMaxDistance.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void chkPercent_CheckedChanged(object sender, EventArgs e)
        {
            numPercent.Enabled = chkPercent.Checked;
        }
    }
}