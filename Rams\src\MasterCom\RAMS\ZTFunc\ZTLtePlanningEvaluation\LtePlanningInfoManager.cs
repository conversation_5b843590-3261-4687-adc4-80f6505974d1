﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Collections.ObjectModel;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using MasterCom.ES.Data;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 管理Excel文件里面的工参
    /// </summary>
    public class LtePlanningInfoManager
    {
        public static LtePlanningInfoManager Instance
        {
            get { return instance; }
        }

        public ReadOnlyCollection<LTECell> LteCells
        {
            get { return lteCells.AsReadOnly(); }
        }

        public ReadOnlyCollection<LTEBTS> LteBTSs
        {
            get { return lteBTSs.AsReadOnly(); }
        }

        /// <summary>
        /// 从Excel加载工参，发生错误抛出异常
        /// </summary>
        /// <param name="xlsFile"></param>
        /// <returns>如果非空(不是null)则为警告信息</returns>
        public bool LoadFromXls(string xlsFile)
        {
            lteCells.Clear();
            lteBTSs.Clear();
            lteBtsDic.Clear();
            taceciCellMap.Clear();
            earfcnpciCellMap.Clear();
            warnings.Clear();

            ExcelNPOIReader reader = new ExcelNPOIReader(xlsFile);
            ExcelNPOITable table = reader.GetTable(sXlsColumns);
            if (table == null)
            {
                throw (new Exception("未能找到匹配以下列名的工作表:" + Environment.NewLine + sXlsColString));
            }

            // parse rows
            for (int i = 0; i < table.CellValues.Count; ++i)
            {
                object[] values = table.CellValues[i];
                if (values == null)
                {
                    continue;
                }

                int errorIx = ParseXlsRow(i + table.FirstRow, values);
                if (errorIx != -1)
                {
                    warnings.Add(string.Format("Parse Error[{0}, {1}]", i + table.FirstRow, table.ColumnsIndex[errorIx]));
                }
            }

            if (warnings.Count > 0)
            {
                LtePlanningExcelWarningForm warnForm = new LtePlanningExcelWarningForm(
                    string.Format("读取Excel发现{0}处错误，是否继续？", warnings.Count), warnings);
                if (warnForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                {
                    return false;
                }
            }
            return true;
        }

        public LTECell GetLteCell(int? tac, int? eci, int? earfcn, int? pci, double lng, double lat)
        {
            LTECell cell = null;
            cell = GetLteCellByTacEci(tac, eci);
            if (cell == null)
            {
                cell = GetLteCellByEarfcnPci(earfcn, pci, lng, lat);
            }
            return cell;
        }

        public void CalculateRadius()
        {
            WaitBox.Show("正在计算理想覆盖半径...", CalculateRadiusInThread);
        }

        public double GetCellRadius(LTECell cell)
        {
            double radius = 0;
            cellRadiusMap.TryGetValue(cell, out radius);
            return radius;
        }

        #region private members
        private int ParseXlsRow(int rowIndex, object[] values)
        {
            int ix = -1;
            try
            {
                LTECell cell = new LTECell();
                LTEBTS bts = new LTEBTS();
                LTEAntenna ant = new LTEAntenna();

                #region parse cells value in row
                string btsName = values[++ix].ToString();
                if (string.IsNullOrEmpty(btsName))
                {
                    return ix;
                }
                bts.Name = btsName;

                int eNodeBID = 0;
                if (!int.TryParse(values[++ix].ToString(), out eNodeBID))
                {
                    eNodeBID = -1;
                }
                bts.BTSID = eNodeBID;

                double lng = 0;
                if (!double.TryParse(values[++ix].ToString(), out lng))
                {
                    return ix;
                }
                bts.Longitude = lng;

                double lat = 0;
                if (!double.TryParse(values[++ix].ToString(), out lat))
                {
                    return ix;
                }
                bts.Latitude = lat;

                bts.Description = values[++ix] == null ? "" : values[ix].ToString();

                string cellName = values[++ix].ToString();
                if (string.IsNullOrEmpty(cellName))
                {
                    return ix;
                }
                cell.Name = cellName;

                int tac = 0;
                if (!int.TryParse(values[++ix].ToString(), out tac))
                {
                    return ix;
                }
                cell.TAC = tac;

                ++ix; // skip eci; cellID = (ECI / 256) * 10 + sectorID; ECI = (cellID - sectorID) / 10 * 256 + sectorID

                ++ix; // skip cellid; ENodeBID = (cellID - sectorID) / 10; cellID = EnodeBID * 10 + sectorID

                int sectorId = 0;
                if (!int.TryParse(values[++ix].ToString(), out sectorId))
                {
                    return ix;
                }
                cell.SectorID = sectorId;

                int earfcn = 0;
                if (!int.TryParse(values[++ix].ToString(), out earfcn))
                {
                    return ix;
                }
                cell.EARFCN = earfcn;

                int pci = 0;
                if (!int.TryParse(values[++ix].ToString(), out pci))
                {
                    return ix;
                }
                cell.PCI = pci;

                short direction = 0;
                if (!short.TryParse(values[++ix].ToString(), out direction))
                {
                    return ix;
                }
                ant.Direction = direction;

                short downward = 0;
                if (!short.TryParse(values[++ix].ToString(), out downward))
                {
                    return ix;
                }
                ant.Downward = downward;

                ++ix; // 电子下倾角

                int altitude = 0;
                if (!int.TryParse(values[++ix].ToString(), out altitude))
                {
                    return ix;
                }
                ant.Altitude = altitude;
                #endregion

                ant.Longitude = bts.Longitude;
                ant.Latitude = bts.Latitude;
                cell.SCellID = bts.BTSID * 10 + cell.SectorID; 
                cell.ECI = (cell.SCellID - cell.SectorID) / 10 * 256 + cell.SectorID;

                AddToIndexDic(rowIndex, bts, cell, ant);
            }
            catch
            {
                return ix;
            }
            return -1;
        }

        private void AddToIndexDic(int rowIndex, LTEBTS bts, LTECell cell, LTEAntenna ant)
        {
            string taceci = MakeCellKey(cell.TAC, cell.ECI);
            if (taceciCellMap.ContainsKey(taceci))
            {
                warnings.Add(string.Format("Row[{0}] Repeat TAC[{1}] ECI[{2}]", rowIndex, cell.TAC, cell.ECI));
                return;
            }

            string earfcnpci = MakeCellKey(cell.EARFCN, cell.PCI);
            if (!earfcnpciCellMap.ContainsKey(earfcnpci))
            {
                earfcnpciCellMap.Add(earfcnpci, new List<LTECell>());
            }
            ant.Cell = cell;
            if (!lteBtsDic.ContainsKey(bts.BTSID))
            {
                lteBtsDic.Add(bts.BTSID, bts);
                lteBTSs.Add(bts);
            }
            lteBtsDic[bts.BTSID].AddCell(cell);
            earfcnpciCellMap[earfcnpci].Add(cell);
            taceciCellMap.Add(taceci, cell);
            lteCells.Add(cell);
        }

        private string MakeCellKey(int x, int y)
        {
            return string.Format("{0}:{1}", x, y);
        }

        private LTECell GetLteCellByTacEci(int? tac, int? eci)
        {
            if (tac == null || eci == null)
            {
                return null;
            }
            string key = MakeCellKey((int)tac, (int)eci);
            if (!taceciCellMap.ContainsKey(key))
            {
                return null;
            }
            return taceciCellMap[key];
        }

        private LTECell GetLteCellByEarfcnPci(int? earfcn, int? pci, double lng, double lat)
        {
            if (earfcn == null || pci == null)
            {
                return null;
            }
            string key = MakeCellKey((int)earfcn, (int)pci);
            if (!earfcnpciCellMap.ContainsKey(key))
            {
                return null;
            }

            double minDistance = double.MaxValue;
            LTECell retCell = null;
            foreach (LTECell cell in earfcnpciCellMap[key])
            {
                double dis = MathFuncs.GetDistance(lng, lat, cell.Longitude, cell.Latitude);
                if (dis < minDistance)
                {
                    retCell = cell;
                    minDistance = dis;
                }
            }
            return retCell;
        }

        private void CalculateRadiusInThread()
        {
            cellRadiusMap.Clear();
            int cellCount = lteCells.Count;
            for (int i = 0; i < cellCount; ++i)
            {
                WaitBox.ProgressPercent = (i + 1) * 100 / cellCount;
                CalculateCell calCell = new CalculateCell(new CellInfo(lteCells[i]), 0);
                double meanDis = CfgDataProvider.CalculateRadius(lteCells[i], lteBTSs, 3, ref calCell);
                if (!cellRadiusMap.ContainsKey(lteCells[i]))
                {
                    cellRadiusMap.Add(lteCells[i], meanDis);
                }
            }

            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        private LtePlanningInfoManager()
        {
            sXlsColumns = new List<string>();
            sXlsColumns.Add("基站名称");
            sXlsColumns.Add("ENodeBID");
            sXlsColumns.Add("经度");
            sXlsColumns.Add("纬度");
            sXlsColumns.Add("地址");
            sXlsColumns.Add("小区名称");
            sXlsColumns.Add("TAC");
            sXlsColumns.Add("ECI");
            sXlsColumns.Add("CellID");
            sXlsColumns.Add("SectorID");
            sXlsColumns.Add("ARFCN");
            sXlsColumns.Add("PCI");
            sXlsColumns.Add("方向角");
            sXlsColumns.Add("机械下倾角");
            sXlsColumns.Add("电子下倾角");
            sXlsColumns.Add("挂高");

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < sXlsColumns.Count; ++i)
            {
                sb.Append(sXlsColumns[i] + (i < sXlsColumns.Count - 1 ? Environment.NewLine : ""));
            }
            sXlsColString = sb.ToString();

            lteCells = new List<LTECell>();
            lteBTSs = new List<LTEBTS>();
            lteBtsDic = new Dictionary<int, LTEBTS>();
            taceciCellMap = new Dictionary<string, LTECell>();
            earfcnpciCellMap = new Dictionary<string, List<LTECell>>();
            cellRadiusMap = new Dictionary<LTECell, double>();
            warnings = new List<string>();
        }

        private readonly List<string> warnings;
        private readonly List<LTECell> lteCells;
        private readonly List<LTEBTS> lteBTSs;
        private readonly Dictionary<int, LTEBTS> lteBtsDic;

        private readonly Dictionary<string, LTECell> taceciCellMap;
        private readonly Dictionary<string, List<LTECell>> earfcnpciCellMap;

        private readonly Dictionary<LTECell, double> cellRadiusMap;
        private static List<string> sXlsColumns { get; set; }
        private static string sXlsColString { get; set; }
        private static LtePlanningInfoManager instance = new LtePlanningInfoManager();
        #endregion
    }
}
