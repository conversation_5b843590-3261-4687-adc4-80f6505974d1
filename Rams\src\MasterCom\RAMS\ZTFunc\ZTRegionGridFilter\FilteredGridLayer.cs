﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.Serialization;
using System.Text;
using MasterCom.MControls;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc.ZTRegionGridFilter
{
    public class FilteredGridLayer : CustomDrawLayer
    {
        public FilteredGridLayer(MapOperation mp, string name)
            : base(mp, name)
        {
            ColorRange c = new ColorRange(-141, -110, Color.Red);
            ColorRanges.Add(c);
            c = new ColorRange(-110, -105, Color.FromArgb(255,0,255));
            ColorRanges.Add(c);
            c = new ColorRange(-105, -100, Color.FromArgb(255, 255, 0));
            ColorRanges.Add(c);
            c = new ColorRange(-100, -95, Color.FromArgb(128, 255, 255));
            ColorRanges.Add(c);
            c = new ColorRange(-95, -80, Color.FromArgb(0, 0, 255));
            ColorRanges.Add(c);
            c = new ColorRange(-80, -75, Color.FromArgb(0, 255, 0));
            ColorRanges.Add(c);
            c = new ColorRange(-75, -40, Color.FromArgb(0, 128, 0));
            ColorRanges.Add(c);
            c = new ColorRange(-40, 25, Color.FromArgb(0, 128, 0));
            ColorRanges.Add(c);
        }

        public List<ColorRange> ColorRanges { get; set; } = new List<ColorRange>();

        public List<RegionGridDataInfo> RegionDataSet { get; set; } = new List<RegionGridDataInfo>();
        public GridItem CurSelGrid { get; set; }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            if (RegionDataSet == null || RegionDataSet.Count == 0)
            {
                return;
            }
            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate(50, 50);
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);

            RectangleF? size = null;
            foreach (RegionGridDataInfo reg in RegionDataSet)
            {
                foreach (GridItem grid in reg.FilteredGirds)
                {
                    if (grid.Bounds.Within(dRect))
                    {
                        drawGrid(graphics, grid, ref size);
                    }
                }
            }
        }

        private void drawGrid(Graphics graphics, GridItem grid, ref RectangleF? size)
        {
            PointF pointLt;
            this.Map.ToDisplay(new DbPoint(grid.LTLng, grid.LTLat), out pointLt);
            if (size == null)
            {
                DbPoint brPoint = new DbPoint(grid.BRLng, grid.BRLat);
                PointF pointBr;
                this.Map.ToDisplay(brPoint, out pointBr);
                size = new RectangleF(pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
            }
            RectangleF rect = new RectangleF(pointLt.X, pointLt.Y, ((RectangleF)size).Width, ((RectangleF)size).Height);
            graphics.FillRectangle(new SolidBrush(getGridColor(grid)), rect);
            if (grid == CurSelGrid)
            {
                rect.Inflate(1.0f, 1.0f);
                graphics.DrawRectangle(new Pen(Color.Red), rect.X, rect.Y, rect.Width, rect.Height);
            }
        }

        private Color getGridColor(GridItem grid)
        {
            for (int i = 0; i < ColorRanges.Count; i++)
            {
                ColorRange rng = ColorRanges[i];
                if (rng.minValue <= grid.RsrpAvg
                    && (i == ColorRanges.Count - 1 
                    ? grid.RsrpAvg <= rng.maxValue 
                    : grid.RsrpAvg < rng.maxValue))
                {
                    return rng.color;
                }
            }
            return Color.Empty;
        }


    }
}
