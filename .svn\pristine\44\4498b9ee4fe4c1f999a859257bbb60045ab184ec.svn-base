﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRScanMultiCoverageGridDlg : Form
    {
        public NRScanMultiCoverageGridDlg()
        {
            InitializeComponent();
        }

        public NRScanMultiCoverageGridCond Condition { get; set; }

        public void SetCondition(NRScanMultiCoverageGridCond cond)
        {
            if (cond == null)
            {
                return;
            }

            numRsrpMin.Value = cond.AbsoluteValue;
            numRsrpDiff.Value = cond.CoverBandDiff;
            chkOptionalRsrp.Checked = cond.EnableOptional;
            numOptionalRsrp.Value = cond.ValidValue;
        }

        public NRScanMultiCoverageGridCond GetCondition()
        {
            NRScanMultiCoverageGridCond cond = new NRScanMultiCoverageGridCond();
            cond.AbsoluteValue = (int)numRsrpMin.Value;
            cond.CoverBandDiff = (int)numRsrpDiff.Value;
            cond.EnableOptional = chkOptionalRsrp.Checked;
            cond.ValidValue = (int)numOptionalRsrp.Value;

            return cond;
        }

        private void chkOptionalRsrp_CheckedChanged(object sender, EventArgs e)
        {
            numOptionalRsrp.Enabled = chkOptionalRsrp.Checked;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            Condition = GetCondition();
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }

    public class NRScanMultiCoverageGridCond : ScanMultiCoverageCondition
    { 
        public bool EnableOptional { get; set; }
    }
}
