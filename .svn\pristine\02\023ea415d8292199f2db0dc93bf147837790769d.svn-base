﻿namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    partial class OverCoverPnl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.numRSRPDiff = new DevExpress.XtraEditors.SpinEdit();
            this.label12 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.grpLackNC = new System.Windows.Forms.GroupBox();
            this.label6 = new System.Windows.Forms.Label();
            this.numDisMin2 = new DevExpress.XtraEditors.SpinEdit();
            this.lblNCell = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label3 = new System.Windows.Forms.Label();
            this.numDisMin = new DevExpress.XtraEditors.SpinEdit();
            this.lblNoSite = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numRSRPMin = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPDiff.Properties)).BeginInit();
            this.grpLackNC.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMin2.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPMin.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.groupBox3);
            this.groupBox1.Controls.Add(this.grpLackNC);
            this.groupBox1.Controls.Add(this.groupBox2);
            this.groupBox1.Controls.Add(this.numRSRPMin);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label15);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(395, 333);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "过覆盖";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.numRSRPDiff);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.label11);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Location = new System.Drawing.Point(33, 233);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(355, 74);
            this.groupBox3.TabIndex = 4;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "切换参数不合理";
            // 
            // numRSRPDiff
            // 
            this.numRSRPDiff.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numRSRPDiff.Location = new System.Drawing.Point(135, 16);
            this.numRSRPDiff.Name = "numRSRPDiff";
            this.numRSRPDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRSRPDiff.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numRSRPDiff.Size = new System.Drawing.Size(60, 21);
            this.numRSRPDiff.TabIndex = 1;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(16, 48);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(137, 12);
            this.label12.TabIndex = 0;
            this.label12.Text = "并且邻区距离采样点更近";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(16, 21);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(113, 12);
            this.label11.TabIndex = 0;
            this.label11.Text = "邻区与主服信号差≥";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(201, 21);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(29, 12);
            this.label10.TabIndex = 0;
            this.label10.Text = "dB，";
            // 
            // grpLackNC
            // 
            this.grpLackNC.Controls.Add(this.label6);
            this.grpLackNC.Controls.Add(this.numDisMin2);
            this.grpLackNC.Controls.Add(this.lblNCell);
            this.grpLackNC.Controls.Add(this.label8);
            this.grpLackNC.Location = new System.Drawing.Point(33, 153);
            this.grpLackNC.Name = "grpLackNC";
            this.grpLackNC.Size = new System.Drawing.Size(355, 74);
            this.grpLackNC.TabIndex = 3;
            this.grpLackNC.TabStop = false;
            this.grpLackNC.Text = "漏配邻区关系";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(16, 21);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(161, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "主服和邻区与采样点距离都≥";
            // 
            // numDisMin2
            // 
            this.numDisMin2.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numDisMin2.Location = new System.Drawing.Point(183, 16);
            this.numDisMin2.Name = "numDisMin2";
            this.numDisMin2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDisMin2.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDisMin2.Size = new System.Drawing.Size(60, 21);
            this.numDisMin2.TabIndex = 1;
            // 
            // lblNCell
            // 
            this.lblNCell.AutoSize = true;
            this.lblNCell.Location = new System.Drawing.Point(16, 48);
            this.lblNCell.Name = "lblNCell";
            this.lblNCell.Size = new System.Drawing.Size(161, 12);
            this.lblNCell.TabIndex = 0;
            this.lblNCell.Text = "周围{0}米内，有其它LTE基站";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(249, 21);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(29, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "米，";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.numDisMin);
            this.groupBox2.Controls.Add(this.lblNoSite);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Location = new System.Drawing.Point(33, 73);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(355, 74);
            this.groupBox2.TabIndex = 2;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "缺站";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(16, 21);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(161, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "主服和邻区与采样点距离都≥";
            // 
            // numDisMin
            // 
            this.numDisMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numDisMin.Location = new System.Drawing.Point(183, 16);
            this.numDisMin.Name = "numDisMin";
            this.numDisMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDisMin.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDisMin.Size = new System.Drawing.Size(60, 21);
            this.numDisMin.TabIndex = 1;
            // 
            // lblNoSite
            // 
            this.lblNoSite.AutoSize = true;
            this.lblNoSite.Location = new System.Drawing.Point(16, 46);
            this.lblNoSite.Name = "lblNoSite";
            this.lblNoSite.Size = new System.Drawing.Size(173, 12);
            this.lblNoSite.TabIndex = 0;
            this.lblNoSite.Text = "周围{0}米内，没有其它LTE基站";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(249, 21);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(29, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "米，";
            // 
            // numRSRPMin
            // 
            this.numRSRPMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numRSRPMin.Location = new System.Drawing.Point(138, 24);
            this.numRSRPMin.Name = "numRSRPMin";
            this.numRSRPMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRSRPMin.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRSRPMin.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numRSRPMin.Size = new System.Drawing.Size(60, 21);
            this.numRSRPMin.TabIndex = 1;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(204, 29);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(35, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "dBm，";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(31, 48);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(263, 12);
            this.label15.TabIndex = 0;
            this.label15.Text = "与主服的距离超过主服小区理想覆盖半径的1.6倍";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(31, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(101, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "采样点信号强度≥";
            // 
            // OverCoverPnl
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.Controls.Add(this.groupBox1);
            this.Name = "OverCoverPnl";
            this.Size = new System.Drawing.Size(395, 333);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPDiff.Properties)).EndInit();
            this.grpLackNC.ResumeLayout(false);
            this.grpLackNC.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMin2.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPMin.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraEditors.SpinEdit numRSRPDiff;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SpinEdit numDisMin;
        private System.Windows.Forms.Label lblNoSite;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.SpinEdit numRSRPMin;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox grpLackNC;
        private System.Windows.Forms.Label label6;
        private DevExpress.XtraEditors.SpinEdit numDisMin2;
        private System.Windows.Forms.Label lblNCell;
        private System.Windows.Forms.Label label8;
    }
}
