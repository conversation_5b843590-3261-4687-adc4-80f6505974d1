﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteMiMoAntenna : ZTAntennaBase
    {
        public ZTLteMiMoAntenna()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
            carrierID = CarrierType.ChinaMobile;
            mimoCfgInfo = new MiMoCfgInfo();
        }
        private static ZTLteMiMoAntenna instance = null;
        protected static readonly object lockObj = new object();

        public static ZTLteMiMoAntenna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLteMiMoAntenna();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "天线分析_MIMO双流分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23021, this.Name);
        }

        Dictionary<string, CellAngleData> dicUtranCellAngelData = null;
        Dictionary<int, CellAngleData> dicCellAngelDataBG = null;//后台体检数据
        string strCityTypeName = "";
        public MiMoCfgInfo mimoCfgInfo { get; set; }

        protected override void query()
        {
            if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
            {
                setVarBeforQuery();
                InitRegionMop2();
                LTEMIMOSetForm fDlg = new LTEMIMOSetForm();
                if (fDlg.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                mimoCfgInfo = fDlg.GetScanMIMOCfg(mimoCfgInfo);
            }
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                dicUtranCellAngelData = new Dictionary<string, CellAngleData>();
                MainModel.ClearDTData();
                if (!MainModel.IsBackground)
                {
                    if (!MainModel.QueryFromBackground)
                    {
                        WaitBox.CanCancel = true;
                        WaitBox.Text = "正在查询...";
                        strCityTypeName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                        WaitBox.Show("读取数据分析...", queryInThread, clientProxy);
                        dealMainUtranCellSample();
                    }
                    else
                    {
                        getBackgroundData();
                        initBackgroundImageDesc();
                    }
                    MainModel.FireSetDefaultMapSerialTheme("LTE", "RSRP");
                }
                else
                {
                    strCityTypeName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                    doBackgroundStatByPeriod(clientProxy);
                }
            }
            catch
            {
                clientProxy.Close();
            }
            finally
            {
                dicUtranCellAngelData.Clear();
                MainModel.ClearDTData();
            }
        }

        protected override void statData(ClientProxy clientProxy)
        {
            InitRegionMop2();
            setVarBeforQuery();
            queryInThread(clientProxy);
        }

        protected override void queryInThread(object o)
        {
            try
            {
                doSomethingBeforeQueryInThread();
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, period, false);
                    }
                }

                getResultAfterQuery();
            }
            catch (Exception ex)
            {
                log.Error(ex.Message + Environment.NewLine + ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            //LTE SCAN
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSSINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param["param_name"] = "LTESCAN_TopN_RS_RP_Rx1Tx1";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_RS_CINR_Rx1Tx1";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param["param_name"] = "LTESCAN_TopN_RS_RP_Rx1Tx2";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_RS_CINR_Rx1Tx2";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            //LTE DT
            param = new Dictionary<string, object>();
            param["param_name"] = "lte_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP_Rx0";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP_Rx1";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SINR_Rx0";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SINR_Rx1";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_SCAN");
            tmpDic.Add("themeName", (object)"TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void fillContentNeeded_Sample(Package package)
        {
            List<ColumnDefItem> colDefList = getNeededColumnDefList(curSelDIYSampleGroup);
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,1,51,0,2,51,0,4,51,0,5,51,");//isampleid
            for (int i = 0; i < colDefList.Count; i++)
            {
                ColumnDefItem sid = colDefList[i];
                sbuilder.Append(sid.GetTriIdStr());
                if (i < colDefList.Count - 1)
                {
                    sbuilder.Append(",");
                }
            }
            package.Content.AddParam(sbuilder.ToString());
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (mapOp2.CheckPointInRegion(tp.Longitude, tp.Latitude)
                    && (tp is ScanTestPoint_LTE || tp is LTETestPointDetail))
                {
                    List<SampleMimoInfo> sampList = new List<SampleMimoInfo>();
                    if (tp is ScanTestPoint_LTE)
                        sampList = doWithLTEScanData(tp);
                    else if (tp is LTETestPointDetail)
                        sampList = doWithLTEDTData(tp);

                    foreach (SampleMimoInfo sInfo in sampList)
                    {
                        CellAngleData utranCellAngleData;
                        utranCellAngleData = getUtranCellAngleData(tp, sInfo); utranCellAngleData.sampleTotal++;
                        //按小区级、小区角度区间、360度信息统计
                        dealWithCellAndAngleStat(ref utranCellAngleData, sInfo);
                    }

                }
            }
            catch (Exception ee)
            {
                log.Error(ee.Message);
            }
        }

        private CellAngleData getUtranCellAngleData(TestPoint tp, SampleMimoInfo sInfo)
        {
            CellAngleData utranCellAngleData;
            #region 天线信息处理
            if (dicUtranCellAngelData.ContainsKey(sInfo.CellName))
            {
                utranCellAngleData = dicUtranCellAngelData[sInfo.CellName];
            }
            else
            {
                utranCellAngleData = new CellAngleData();
                utranCellAngleData.strbtsname = sInfo.BtsName;
                utranCellAngleData.strTime = tp.DateTime.ToString("yyyy-MM-dd");
                utranCellAngleData.strcityname = strCityTypeName;
                string strGridTypeName = "";
                isContainPoint(tp.Longitude, tp.Latitude, ref strGridTypeName);
                if (strGridTypeName == "")
                    strGridTypeName = "无网格号";

                utranCellAngleData.gridList.Add(strGridTypeName);
                utranCellAngleData.cellname = sInfo.CellName;
                utranCellAngleData.iTac = sInfo.lteCell?.TAC ?? 0;
                utranCellAngleData.iEci = sInfo.ECI;
                utranCellAngleData.strcgi = sInfo.CGI;
                utranCellAngleData.uarfcn = sInfo.Earfcn;
                utranCellAngleData.pci = sInfo.PCI;
                utranCellAngleData.band = LTECell.GetBandTypeByJT(sInfo.Earfcn);
                if (sInfo.lteCell != null)
                {
                    if (sInfo.lteCell.BelongBTS.Type == LTEBTSType.Indoor)
                        utranCellAngleData.strCover = "室内";
                    else
                        utranCellAngleData.strCover = "室外";
                }
                utranCellAngleData.ialtitude = sInfo.lteCell?.Altitude ?? 0;
                utranCellAngleData.iangle_dir = sInfo.lteCell?.Direction ?? 0;
                utranCellAngleData.iangle_ob = sInfo.lteCell?.Downward ?? 0;

                 dicUtranCellAngelData[sInfo.CellName] = utranCellAngleData;
            }
            #endregion
            return utranCellAngleData;
        }

        /// <summary>
        /// 扫频数据处理方法
        /// </summary>
        private List<SampleMimoInfo> doWithLTEScanData(TestPoint tp)
        {
            List<SampleMimoInfo> sampList = new List<SampleMimoInfo>();
            if (tp is ScanTestPoint_LTE)
            {
                Dictionary<string, int> chanelCellDic = new Dictionary<string, int>();
                chanelCellDic.Add("D", 0);
                chanelCellDic.Add("E", 0);
                chanelCellDic.Add("F", 0);
                anaTPData(tp, sampList, chanelCellDic);
            }
            return sampList;
        }

        private void anaTPData(TestPoint tp, List<SampleMimoInfo> sampList, Dictionary<string, int> chanelCellDic)
        {
            for (int i = 0; i < 50; i++)
            {
                float rsrp0, sinr0, rsrp1, sinr1;
                int flag = getValidTPParam(tp, i, out rsrp0, out sinr0, out rsrp1, out sinr1);
                if (flag == -1)
                {
                    break;
                }
                else if (flag == 0)
                {
                    continue;
                }

                LTECell lteCell = tp.GetCell_LTEScan(i);
                if (lteCell == null || lteCell.Direction > 360)
                    continue;

                bool isValid = dealChanelAna(chanelCellDic, lteCell);
                if (!isValid)
                {
                    continue;
                }

                double dAngleDiffRel = 0;
                double dAngLeDiffAbs = 0;
                double cellDistance = 0;
                ZTAntFuncHelper.CellCollection col = new ZTAntFuncHelper.CellCollection()
                {
                    Cell = null,
                    TDCell = null,
                    LteCell = lteCell
                };
                ZTAntFuncHelper.calcSampleAngle(col, tp.Longitude, tp.Latitude, out dAngleDiffRel, out dAngLeDiffAbs, out cellDistance);
                int iAngleDiffRel = (int)dAngleDiffRel;//相对夹角
                int iAngleDiffAbs = (int)dAngLeDiffAbs;//相对夹角(绝对值)

                if (cellDistance > CD.MAX_COV_DISTANCE_LTE || iAngleDiffAbs < 0)
                    continue;

                SampleMimoInfo sInfo = new SampleMimoInfo(rsrp0, sinr0, rsrp1, sinr1, lteCell);
                sInfo.SetOtherInfo(cellDistance, iAngleDiffRel, iAngleDiffAbs);
                sampList.Add(sInfo);
            }
        }

        private int getValidTPParam(TestPoint tp, int i, out float rsrp0, out float sinr0, out float rsrp1, out float sinr1)
        {
            rsrp0 = -1000; sinr0 = -1000; rsrp1 = -1000; sinr1 = -1000;
            if (mimoCfgInfo.bSampleN && i > mimoCfgInfo.iSampleN)
            {
                return -1;//只取前N强小区
            }
            int flag = getValidTP(tp["LTESCAN_TopN_RS_RP_Rx1Tx1", i], tp["LTESCAN_TopN_RS_CINR_Rx1Tx1", i], out rsrp0, out sinr0, mimoCfgInfo.bRSRP0, mimoCfgInfo.fRsrp0Min, mimoCfgInfo.fRsrp0Max);
            if (flag != 1)
            {
                return flag;
            }

            flag = getValidTP(tp["LTESCAN_TopN_RS_RP_Rx1Tx2", i], tp["LTESCAN_TopN_RS_CINR_Rx1Tx2", i], out rsrp1, out sinr1, mimoCfgInfo.bRSRP1, mimoCfgInfo.fRsrp1Min, mimoCfgInfo.fRsrp1Max);
            if (flag != 1)
            {
                return flag;
            }

            return 1;
        }

        private int getValidTP(object value1, object value2, out float rsrp1, out float sinr1, bool bRSRP, float fRsrpMin, float fRsrpMax)
        {
            rsrp1 = -1000; sinr1 = -1000;
            if (value1 == null)
            {
                return -1;
            }
            rsrp1 = float.Parse(value1.ToString());
            if (rsrp1 < -141 || rsrp1 > 25)
            {
                return -1;
            }
            if (bRSRP && (rsrp1 < fRsrpMin || rsrp1 > fRsrpMax))
            {
                return 0;//只取设置区间值的小区
            }
            if (value2 == null)
            {
                return -1;
            }
            sinr1 = float.Parse(value2.ToString());
            if (sinr1 < -50 || sinr1 > 50)
            {
                return -1;
            }

            return 1;
        }

        private bool dealChanelAna(Dictionary<string, int> chanelCellDic, LTECell lteCell)
        {
            if (mimoCfgInfo.bChanelAna)
            {
                LTEBandTypeJT bandType = LTECell.GetBandTypeByJT(lteCell.EARFCN);
                if (bandType == LTEBandTypeJT.D1 || bandType == LTEBandTypeJT.D2 || bandType == LTEBandTypeJT.D3)
                {
                    if (mimoCfgInfo.bChanelD && chanelCellDic["D"] < mimoCfgInfo.iChanelNum)
                    {
                        chanelCellDic["D"]++;
                        return true;
                    }
                }
                else if (bandType == LTEBandTypeJT.E && mimoCfgInfo.bChanelE && chanelCellDic["E"] < mimoCfgInfo.iChanelNum)
                {
                    chanelCellDic["E"]++;
                    return true;
                }
                else if (bandType == LTEBandTypeJT.F && mimoCfgInfo.bChanelF && chanelCellDic["F"] < mimoCfgInfo.iChanelNum)
                {
                    chanelCellDic["F"]++;
                    return true;
                }
                return false;
            }
            return true;
        }

        /// <summary>
        /// 路测数据处理方法
        /// </summary>
        private List<SampleMimoInfo> doWithLTEDTData(TestPoint tp)
        {
            List<SampleMimoInfo> sampList = new List<SampleMimoInfo>();

            int? iTac = (int?)(ushort?)tp["lte_TAC"];
            int? iEci = (int?)tp["lte_ECI"];
            int? iEarfcn = (int?)tp["lte_EARFCN"];
            int? iPCI = (int?)(short?)tp["lte_PCI"];

            if (iTac == null || iEci == null || iEarfcn == null || iPCI == null)
                return sampList;

            LTECell lteMainCell = tp.GetMainCell_LTE();
            if (lteMainCell == null)
            {//tac eci 优先匹配
                lteMainCell = CellManager.GetInstance().GetNearestLTECellByTACCI(JavaDate.GetDateTimeFromMilliseconds(tp.Time * 1000L), iTac, iEci, tp.Longitude, tp.Latitude);
            }

            float? lteRsrpRx0, lteRsrpRx1, lteSinrRx0, lteSinrRx1;
            bool isValid = getValidTPInfo(tp, out lteRsrpRx0, out lteRsrpRx1, out lteSinrRx0, out lteSinrRx1);
            if (!isValid)
            {
                return sampList;
            }

            if (lteMainCell != null)
            {
                double dAngleDiffRel = 0;
                double dAngLeDiffAbs = 0;
                double cellDistance = 0;
                ZTAntFuncHelper.CellCollection col = new ZTAntFuncHelper.CellCollection()
                {
                    Cell = null,
                    TDCell = null,
                    LteCell = lteMainCell
                };
                ZTAntFuncHelper.calcSampleAngle(col, tp.Longitude, tp.Latitude, out dAngleDiffRel, out dAngLeDiffAbs, out cellDistance);
                int iAngleDiffRel = (int)dAngleDiffRel;//相对夹角
                int iAngleDiffAbs = (int)dAngLeDiffAbs;//相对夹角(绝对值)

                if (cellDistance > CD.MAX_COV_DISTANCE_LTE || iAngleDiffAbs < 0)
                    return sampList;

                SampleMimoInfo sInfo = new SampleMimoInfo((float)lteRsrpRx0, (float)lteSinrRx0, (float)lteRsrpRx1, (float)lteSinrRx1, lteMainCell);
                sInfo.SetOtherInfo(cellDistance, iAngleDiffRel, iAngleDiffAbs);
                sampList.Add(sInfo);
            }
            else
            {
                SampleMimoInfo sInfo = new SampleMimoInfo((float)lteRsrpRx0, (float)lteSinrRx0, (float)lteRsrpRx1, (float)lteSinrRx1,
                                                          (int)iEci, (int)iEarfcn, (int)iPCI);
                sampList.Add(sInfo);
            }

            return sampList;
        }

        private bool getValidTPInfo(TestPoint tp, out float? lteRsrpRx0, out float? lteRsrpRx1, out float? lteSinrRx0, out float? lteSinrRx1)
        {
            lteRsrpRx0 = lteRsrpRx1 = lteSinrRx0 = lteSinrRx1 = null;

            if (tp["lte_RSRP_Rx0"] == null || tp["lte_RSRP_Rx1"] == null
                || tp["lte_SINR_Rx0"] == null || tp["lte_SINR_Rx1"] == null)
                return false;

            lteRsrpRx0 = (float?)tp["lte_RSRP_Rx0"];
            if (lteRsrpRx0 > mimoCfgInfo.fRsrp0Max || lteRsrpRx0 < mimoCfgInfo.fRsrp0Min)
                return false;

            lteRsrpRx1 = (float?)tp["lte_RSRP_Rx1"];
            if (lteRsrpRx1 > mimoCfgInfo.fRsrp1Max || lteRsrpRx1 < mimoCfgInfo.fRsrp1Min)
                return false;

            lteSinrRx0 = (float?)tp["lte_SINR_Rx0"];
            if (lteSinrRx0 > 40 || lteSinrRx0 < -25)
                return false;

            lteSinrRx1 = (float?)tp["lte_SINR_Rx1"];
            if (lteSinrRx1 > 40 || lteSinrRx1 < -25)
                return false;

            return true;
        }

        /// <summary>
        /// 按小区角度区间类型统计
        /// </summary>
        private void dealWithCellAndAngleStat(ref CellAngleData utranCellAngleData, SampleMimoInfo sInfo)
        {
            Dictionary<string, AngleData> angleDatas = utranCellAngleData.angleDatas;
            string sectionName = GetAngleSectionAbs(sInfo.iAngleDiffAbs);
            if (!angleDatas.ContainsKey(sectionName))
            {
                angleDatas[sectionName] = new AngleData();
            }
            angleDatas[sectionName].SampleList.Add(sInfo);
            utranCellAngleData.cExt.angleDatasExt[sInfo.iAngleDiffRel].UpdateRsrpAndSinr(sInfo);
            utranCellAngleData.cExt.angleDatasExt[sInfo.iAngleDiffRel].strsection = sectionName;

            angleDatas[sectionName].angleDatasExt[sInfo.iAngleDiffRel].UpdateRsrpAndSinr(sInfo);
            angleDatas[sectionName].angleDatasExt[sInfo.iAngleDiffRel].strsection = sectionName;
            utranCellAngleData.cExt.angleDatas = angleDatas;
        }

        private string GetAngleSectionAbs(int iAngleDiffAbs)
        {
            string strSection = "";
            if (iAngleDiffAbs <= 60)
            {
                strSection = "主瓣[0,60]";
            }
            else if (iAngleDiffAbs <= 150)
            {
                strSection = "旁瓣[60,150]";
            }
            else if (iAngleDiffAbs <= 180)
            {
                strSection = "背瓣[150,180]";
            }
            return strSection;
        }

        /// <summary>
        /// 数据结果集封装
        /// </summary>
        private void dealMainUtranCellSample()
        {
            List<List<NPOIRow>> nrDatasList = null;
            List<string> sheetNames = null;
            nrDatasList = new List<List<NPOIRow>>();
            sheetNames = new List<string>();
            sheetNames.Add("小区级天线双流分析");
            sheetNames.Add("区间级天线双流分析");
            sheetNames.Add("小区360度双流分析");
            dealMainCellInfo(ref nrDatasList);
            dealRegionCellInfo(ref nrDatasList);
            deal360AngleInfo(ref nrDatasList);

            FireShowResultForm(nrDatasList, sheetNames);
        }

        /// <summary>
        /// 小区级列表
        /// </summary>
        private void dealMainCellInfo(ref List<List<NPOIRow>> nrDatasList)
        {
            List<NPOIRow> datasCell = new List<NPOIRow>();
            NPOIRow nrCell = new NPOIRow();
            List<object> colsCell = new List<object>();
            colsCell.Add("序号");
            colsCell.Add("地市");
            colsCell.Add("网格");
            colsCell.Add("小区名称");
            colsCell.Add("小区CGI");
            colsCell.Add("小区TAC");
            colsCell.Add("小区ECI");
            colsCell.Add("小区EARFCN");
            colsCell.Add("小区PCI");
            colsCell.Add("小区频段");
            colsCell.Add("小区室分类型");
            colsCell.Add("小区方向角");
            colsCell.Add("小区下倾角");
            colsCell.Add("小区挂高");
            colsCell.Add("小区采样点数");
            colsCell.Add("小区通讯距离");
            colsCell.Add("分析结果");
            colsCell.Add("告警状态");
            colsCell.Add("360双流功率差>9dB扫描角");
            colsCell.Add("360双流功率差>5dB扫描角");
            colsCell.Add("360双流功率差>3dB扫描角");
            colsCell.Add("主瓣双流功率差>9dB扫描角");
            colsCell.Add("主瓣双流功率差>5dB扫描角");
            colsCell.Add("主瓣双流功率差>3dB扫描角");
            colsCell.Add("|RSRP0-RSPP1|>5dB采样点");
            colsCell.Add("|RSRP0-RSPP1|>5dB占比");
            colsCell.Add("|SINR0-SINR1|>5dB采样点");
            colsCell.Add("|SINR0-SINR1|>5dB占比");
            colsCell.Add("RSRP最大差值");
            colsCell.Add("RSRP最大差值角度");
            colsCell.Add("主瓣RSRP最大差值");
            colsCell.Add("主瓣RSRP最大差值角度");
            colsCell.Add("综合覆盖率");
            colsCell.Add("R0覆盖率");
            colsCell.Add("R1覆盖率");
            colsCell.Add("RSRP0均值");
            colsCell.Add("RSRP1均值");
            colsCell.Add("△RSRP");
            colsCell.Add("RSRP稳定度");
            colsCell.Add("SINR0均值");
            colsCell.Add("SINR1均值");
            colsCell.Add("△SINR");
            colsCell.Add("SINR稳定度");

            nrCell.cellValues = colsCell;
            datasCell.Add(nrCell);
            int idx = 1;
            foreach (string cellName in dicUtranCellAngelData.Keys)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();

                objs.Add(idx++);
                objs.Add(dicUtranCellAngelData[cellName].strcityname);
                objs.Add(dicUtranCellAngelData[cellName].GridName);
                objs.Add(dicUtranCellAngelData[cellName].cellname);
                objs.Add(dicUtranCellAngelData[cellName].strcgi);
                objs.Add(dicUtranCellAngelData[cellName].iTac);
                objs.Add(dicUtranCellAngelData[cellName].iEci);
                objs.Add(dicUtranCellAngelData[cellName].uarfcn);
                objs.Add(dicUtranCellAngelData[cellName].pci);
                objs.Add(dicUtranCellAngelData[cellName].BandType);
                objs.Add(dicUtranCellAngelData[cellName].strCover);
                objs.Add(dicUtranCellAngelData[cellName].iangle_dir);
                objs.Add(dicUtranCellAngelData[cellName].iangle_ob);
                objs.Add(dicUtranCellAngelData[cellName].ialtitude);
                objs.Add(dicUtranCellAngelData[cellName].cExt.sampleTotal);
                objs.Add(dicUtranCellAngelData[cellName].cExt.cellDistanceAvg);
                objs.Add(dicUtranCellAngelData[cellName].strreslut);
                objs.Add(dicUtranCellAngelData[cellName].strwarnstat);
                objs.Add(dicUtranCellAngelData[cellName].cExt.cellDiff9Angle);
                objs.Add(dicUtranCellAngelData[cellName].cExt.cellDiff5Angle);
                objs.Add(dicUtranCellAngelData[cellName].cExt.cellDiff3Angle);
                objs.Add(dicUtranCellAngelData[cellName].cExt.maincellDiff9Angle);
                objs.Add(dicUtranCellAngelData[cellName].cExt.maincellDiff5Angle);
                objs.Add(dicUtranCellAngelData[cellName].cExt.maincellDiff3Angle);
                dicUtranCellAngelData[cellName].cExt.doStatRSPP_SINR();
                objs.Add(dicUtranCellAngelData[cellName].cExt.IRSRP0_1_Sample);
                objs.Add(dicUtranCellAngelData[cellName].cExt.dRSRP0_1_Rate);
                objs.Add(dicUtranCellAngelData[cellName].cExt.ISINR0_1_Sample);
                objs.Add(dicUtranCellAngelData[cellName].cExt.dSINR0_1_Rate);

                List<double> tmp = dicUtranCellAngelData[cellName].cExt.DRSRPDiffMax;
                objs.Add(tmp[0]);
                objs.Add(tmp[1]);
                tmp = dicUtranCellAngelData[cellName].cExt.DRSRPDiffMianMax;
                if (tmp[0] == double.MinValue)
                {
                    objs.AddRange(getDefaultValue(2));
                }
                else
                {
                    objs.Add(tmp[0]);
                    objs.Add(tmp[1]);
                }
                objs.Add(dicUtranCellAngelData[cellName].cExt.StrCoverRate);
                objs.Add(dicUtranCellAngelData[cellName].cExt.StrRSRP0CoverRate);
                objs.Add(dicUtranCellAngelData[cellName].cExt.StrRSRP1CoverRate);
                objs.Add(dicUtranCellAngelData[cellName].cExt.DRSRP0AVG);
                objs.Add(dicUtranCellAngelData[cellName].cExt.DRSRP1AVG);
                objs.Add(dicUtranCellAngelData[cellName].cExt.DRSRPDIFF);
                objs.Add(dicUtranCellAngelData[cellName].cExt.DRSRP);
                objs.Add(dicUtranCellAngelData[cellName].cExt.DSINR0AVG);
                objs.Add(dicUtranCellAngelData[cellName].cExt.DSINR1AVG);
                objs.Add(dicUtranCellAngelData[cellName].cExt.DSINRDIFF);
                objs.Add(dicUtranCellAngelData[cellName].cExt.DSINR);

                nr.cellValues = objs;
                datasCell.Add(nr);
            }
            nrDatasList.Add(datasCell);
        }

        private List<object> getDefaultValue(int index)
        {
            List<object> tmpCols = new List<object>();
            for (int i = 0; i < index; i++)
            {
                tmpCols.Add("-");
            }
            return tmpCols;
        }

        /// <summary>
        /// 角度区间级列表
        /// </summary>
        private void dealRegionCellInfo(ref List<List<NPOIRow>> nrDatasList)
        {
            List<NPOIRow> datasAngle = new List<NPOIRow>();

            NPOIRow nrAngle = new NPOIRow();
            List<object> colsAngle = new List<object>();
            colsAngle.Add("序号");
            colsAngle.Add("地市");
            colsAngle.Add("网格");
            colsAngle.Add("小区名称");
            colsAngle.Add("小区CGI");
            colsAngle.Add("小区TAC");
            colsAngle.Add("小区ECI");
            colsAngle.Add("小区EARFCN");
            colsAngle.Add("小区PCI");
            colsAngle.Add("小区频段");
            colsAngle.Add("小区室分类型");
            colsAngle.Add("小区方向角");
            colsAngle.Add("小区下倾角");
            colsAngle.Add("小区挂高");
            colsAngle.Add("角度区间类型");
            colsAngle.Add("采样点数");
            colsAngle.Add("平均通讯距离");
            colsAngle.Add("双流功率差>5dB扫描角");
            colsAngle.Add("双流功率差>3dB扫描角");
            colsAngle.Add("RSRP最大差值");
            colsAngle.Add("RSRP最大差值角度");
            colsAngle.Add("综合覆盖率");
            colsAngle.Add("R0覆盖率");
            colsAngle.Add("R1覆盖率");
            colsAngle.Add("RSRP0均值");
            colsAngle.Add("RSRP1均值");
            colsAngle.Add("△RSRP");
            colsAngle.Add("RSRP稳定度");
            colsAngle.Add("SINR0均值");
            colsAngle.Add("SINR1均值");
            colsAngle.Add("△SINR");
            colsAngle.Add("SINR稳定度");

            nrAngle.cellValues = colsAngle;
            datasAngle.Add(nrAngle);
            int idx = 1;
            foreach (string cellName in dicUtranCellAngelData.Keys)
            {
                foreach (string section in dicUtranCellAngelData[cellName].angleDatas.Keys)
                {
                    NPOIRow nr = new NPOIRow();
                    List<object> objs = new List<object>();
                    objs.Add(idx++);
                    objs.Add(dicUtranCellAngelData[cellName].strcityname);
                    objs.Add(dicUtranCellAngelData[cellName].GridName);
                    objs.Add(dicUtranCellAngelData[cellName].cellname);
                    objs.Add(dicUtranCellAngelData[cellName].strcgi);
                    objs.Add(dicUtranCellAngelData[cellName].iTac);
                    objs.Add(dicUtranCellAngelData[cellName].iEci);
                    objs.Add(dicUtranCellAngelData[cellName].uarfcn);
                    objs.Add(dicUtranCellAngelData[cellName].pci);
                    objs.Add(dicUtranCellAngelData[cellName].BandType);
                    objs.Add(dicUtranCellAngelData[cellName].strCover);
                    objs.Add(dicUtranCellAngelData[cellName].iangle_dir);
                    objs.Add(dicUtranCellAngelData[cellName].iangle_ob);
                    objs.Add(dicUtranCellAngelData[cellName].ialtitude);
                    objs.Add(section);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].SampleNum);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].cellDistanceAvg);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].cellDiff5Angle(section));
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].cellDiff3Angle(section));
                    List<double> tmp = dicUtranCellAngelData[cellName].angleDatas[section].DRSRPDiffMax(section);
                    objs.Add(tmp[0]);
                    objs.Add(tmp[1]);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].StrCoverRate);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].StrRSRP0CoverRate);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].StrRSRP1CoverRate);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].DRSRP0AVG);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].DRSRP1AVG);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].DRSRPDIFF);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].DRSRP);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].DSINR0AVG);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].DSINR1AVG);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].DSINRDIFF);
                    objs.Add(dicUtranCellAngelData[cellName].angleDatas[section].DSINR);

                    nr.cellValues = objs;
                    datasAngle.Add(nr);
                }
            }
            nrDatasList.Add(datasAngle);
        }

        /// <summary>
        /// 360角度级信息
        /// </summary>
        private void deal360AngleInfo(ref List<List<NPOIRow>> nrDatasList)
        {
            List<NPOIRow> datas360 = new List<NPOIRow>();
            NPOIRow nr360 = new NPOIRow();
            List<object> cols360 = new List<object>();
            cols360.Add("小区名称");
            cols360.Add("指标名称");
            for (int i = 0; i < 360; i++)
            {
                cols360.Add(i.ToString());
            }
            nr360.cellValues = cols360;
            datas360.Add(nr360);

            List<string> kpiName = new List<string> { "RSRP0", "RSRP1", "SINR0", "SINR1" };
            foreach (string cellName in dicUtranCellAngelData.Keys)
            {
                foreach (string name in kpiName)
                {
                    NPOIRow nr = new NPOIRow();
                    List<object> objs = new List<object>();
                    objs.Add(cellName);
                    objs.Add(name);
                    addTPInfo(cellName, name, objs);
                    nr.cellValues = objs;
                    datas360.Add(nr);
                }
            }
            nrDatasList.Add(datas360);
        }

        private void addTPInfo(string cellName, string name, List<object> objs)
        {
            if (name.Equals("RSRP0"))
            {
                for (int i = 0; i < 360; i++)
                {
                    objs.Add(dicUtranCellAngelData[cellName].cExt.angleDatasExt[i].FRSRP0AVG);
                }
            }
            else if (name.Equals("RSRP1"))
            {
                for (int i = 0; i < 360; i++)
                {
                    objs.Add(dicUtranCellAngelData[cellName].cExt.angleDatasExt[i].FRSRP1AVG);
                }
            }
            else if (name.Equals("SINR0"))
            {
                for (int i = 0; i < 360; i++)
                {
                    objs.Add(dicUtranCellAngelData[cellName].cExt.angleDatasExt[i].FSINR0AVG);
                }
            }
            else
            {
                for (int i = 0; i < 360; i++)
                {
                    objs.Add(dicUtranCellAngelData[cellName].cExt.angleDatasExt[i].FSINR1AVG);
                }
            }
        }

        /// <summary>
        /// 数据结果窗赋值
        /// </summary>
        private void FireShowResultForm(List<List<NPOIRow>> nrDatasList, List<string> sheetNames)
        {
            if (dicUtranCellAngelData.Count == 0)
            {
                MessageBox.Show("没有符合条件的结果集！！！");
                return;
            }
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LTEMIMOAntennaForm).FullName);
            LTEMIMOAntennaForm form = obj == null ? null : obj as LTEMIMOAntennaForm;
            if (form == null || form.IsDisposed)
            {
                form = new LTEMIMOAntennaForm(MainModel);
            }
            form.nrDatasList = nrDatasList;
            form.sheetNames = sheetNames;
            form.FillData(dicUtranCellAngelData);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        #region background

        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE扫频专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;

                param["bChanelAna"] = mimoCfgInfo.bChanelAna;
                param["bChanelD"] = mimoCfgInfo.bChanelD;
                param["bChanelE"] = mimoCfgInfo.bChanelE;
                param["bChanelF"] = mimoCfgInfo.bChanelF;
                param["iChanelNum"] = mimoCfgInfo.iChanelNum;
                param["bSampleN"] = mimoCfgInfo.bSampleN;
                param["iSampleN"] = mimoCfgInfo.iSampleN;
                param["bRSRP0"] = mimoCfgInfo.bRSRP0;
                param["fRsrp0Min"] = mimoCfgInfo.fRsrp0Min;
                param["fRsrp0Max"] = mimoCfgInfo.fRsrp0Max;
                param["bRSRP1"] = mimoCfgInfo.bRSRP1;
                param["fRsrp1Min"] = mimoCfgInfo.fRsrp1Min;
                param["fRsrp1Max"] = mimoCfgInfo.fRsrp1Max;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                BackgroundStat = getValidData(param, "BackgroundStat", BackgroundStat);
                mimoCfgInfo.bChanelAna = getValidData(param, "bChanelAna", mimoCfgInfo.bChanelAna);
                mimoCfgInfo.bChanelD = getValidData(param, "bChanelD", mimoCfgInfo.bChanelD);
                mimoCfgInfo.bChanelE = getValidData(param, "bChanelE", mimoCfgInfo.bChanelE);
                mimoCfgInfo.bChanelF = getValidData(param, "bChanelF", mimoCfgInfo.bChanelF);
                mimoCfgInfo.iChanelNum = getValidData(param, "iChanelNum", mimoCfgInfo.iChanelNum);
                mimoCfgInfo.bSampleN = getValidData(param, "bSampleN", mimoCfgInfo.bSampleN);
                mimoCfgInfo.iSampleN = getValidData(param, "iSampleN", mimoCfgInfo.iSampleN);
                mimoCfgInfo.bRSRP0 = getValidData(param, "bRSRP0", mimoCfgInfo.bRSRP0);
                mimoCfgInfo.fRsrp0Min = getValidData(param, "fRsrp0Min", mimoCfgInfo.fRsrp0Min);
                mimoCfgInfo.fRsrp0Max = getValidData(param, "fRsrp0Max", mimoCfgInfo.fRsrp0Max);
                mimoCfgInfo.bRSRP1 = getValidData(param, "bRSRP1", mimoCfgInfo.bRSRP1);
                mimoCfgInfo.fRsrp1Min = getValidData(param, "fRsrp1Min", mimoCfgInfo.fRsrp1Min);
                mimoCfgInfo.fRsrp1Max = getValidData(param, "fRsrp1Max", mimoCfgInfo.fRsrp1Max);
                mimoCfgInfo.bValue = mimoCfgInfo.bChanelAna || mimoCfgInfo.bSampleN || mimoCfgInfo.bRSRP0 || mimoCfgInfo.bRSRP1;
            }
        }

        private T getValidData<T>(Dictionary<string, object> param, string name, T defaultData)
        {
            if (param.ContainsKey(name))
            {
                return (T)param[name];
            }
            return defaultData;
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new LteScanMIMOAntennaProperties_LTE(this);
            }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }

        protected override void getBackgroundData()
        {
            string[] strProject = BackgroundFuncConfigManager.GetInstance().ProjectType.Split('|');
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Cell(Condition.Periods[0].BeginTime.AddSeconds(1),
                Condition.Periods[0].EndTime.AddSeconds(-1), GetSubFuncID(), Name, StatType, strProject.Length == 1 ? strProject[0] : strProject[1]);
        }

        protected override void saveBackgroundData()
        {
            if (dicUtranCellAngelData.Count == 0)
                return;
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (CellAngleData cad in dicUtranCellAngelData.Values)
            {
                BackgroundResult result = cad.ConvertToBackgroundResult();
                result.ProjectString = BackgroundFuncConfigManager.GetInstance().ProjectType;
                result.ISTime = Condition.Periods[0].IBeginTime;
                result.IETime = Condition.Periods[0].IEndTime;
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                    Condition.Periods[0].IEndTime, bgResultList);
        }

        protected override void initBackgroundImageDesc()
        {
            dicCellAngelDataBG = new Dictionary<int, CellAngleData>();
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                CellAngleData cad = new CellAngleData();
                cad.strcityname = bgResult.GetImageValueString();
                cad.strgridname = bgResult.GetImageValueString();
                cad.cellname = bgResult.GetImageValueString();
                cad.strcgi = bgResult.GetImageValueString();
                cad.iTac = bgResult.GetImageValueInt();
                cad.iEci = bgResult.GetImageValueInt();
                cad.uarfcn = bgResult.GetImageValueInt();
                cad.pci = bgResult.GetImageValueInt();
                cad.strband = bgResult.GetImageValueString();
                cad.strCover = bgResult.GetImageValueString();
                cad.iangle_dir = bgResult.GetImageValueInt();
                cad.iangle_ob = bgResult.GetImageValueInt();
                cad.ialtitude = bgResult.GetImageValueInt();
                cad.bgExt.sampleTotal = bgResult.GetImageValueInt();
                string scellDistanceAvg = bgResult.GetImageValueString();
                double cellDistanceAvg = 0;
                if (double.TryParse(scellDistanceAvg, out cellDistanceAvg))
                    cad.bgExt.cellDistanceAvg = cellDistanceAvg;
                cad.strreslut = bgResult.GetImageValueString();
                cad.strwarnstat = bgResult.GetImageValueString();
                cad.bgExt.cellDiff9Angle = bgResult.GetImageValueString();
                cad.bgExt.cellDiff5Angle = bgResult.GetImageValueString();
                cad.bgExt.cellDiff3Angle = bgResult.GetImageValueString();
                cad.bgExt.maincellDiff9Angle = bgResult.GetImageValueString();
                cad.bgExt.maincellDiff5Angle = bgResult.GetImageValueString();
                cad.bgExt.maincellDiff3Angle = bgResult.GetImageValueString();
                cad.bgExt.DRSRPDiffMax = bgResult.GetImageValueString();
                cad.bgExt.DRSRPDiffMaxAnt = bgResult.GetImageValueString();
                cad.bgExt.DRSRPDiffMianMax = bgResult.GetImageValueString();
                cad.bgExt.DRSRPDiffMianMaxAnt = bgResult.GetImageValueString();
                float fCR = 0;
                float fR0CR = 0;
                float fR1CR = 0;
                float.TryParse(bgResult.GetImageValueString().Replace("%", ""), out fCR);
                float.TryParse(bgResult.GetImageValueString().Replace("%", ""), out fR0CR);
                float.TryParse(bgResult.GetImageValueString().Replace("%", ""), out fR1CR);
                cad.bgExt.fCoverRate = fCR;
                cad.bgExt.fRSRP0CoverRate = fR0CR;
                cad.bgExt.fRSRP1CoverRate = fR1CR;
                cad.bgExt.fDRSRP0AVG = Convert.ToSingle(bgResult.GetImageValueString());
                cad.bgExt.fDRSRP1AVG = Convert.ToSingle(bgResult.GetImageValueString());
                cad.bgExt.fDRSRPDIFF = Convert.ToSingle(bgResult.GetImageValueString());
                cad.bgExt.fDRSRP = Convert.ToSingle(bgResult.GetImageValueString());
                cad.bgExt.mainDRSRP0AVG = bgResult.GetImageValueString();
                cad.bgExt.mainDRSRP1AVG = bgResult.GetImageValueString();
                cad.bgExt.mainDRSRPDIFF = bgResult.GetImageValueString();
                cad.bgExt.mainDRSRP = bgResult.GetImageValueString();
                cad.bgExt.fDSINR0AVG = Convert.ToSingle(bgResult.GetImageValueString());
                cad.bgExt.fDSINR1AVG = Convert.ToSingle(bgResult.GetImageValueString());
                cad.bgExt.fDSINRDIFF = Convert.ToSingle(bgResult.GetImageValueString());
                cad.bgExt.fDSINR = Convert.ToSingle(bgResult.GetImageValueString());
                if (!dicCellAngelDataBG.ContainsKey(cad.iEci))
                    dicCellAngelDataBG[cad.iEci] = new CellAngleData();
                dicCellAngelDataBG[cad.iEci] = cad;

                StringBuilder sb = new StringBuilder();
                sb.Append("地市：");
                sb.Append(cad.strcityname);
                sb.Append("\r\n");
                sb.Append("网格：");
                sb.Append(cad.GridName);
                sb.Append("\r\n");
                sb.Append("小区名：");
                sb.Append(cad.cellname);
                sb.Append("\r\n");
                sb.Append("CGI：");
                sb.Append(cad.strcgi);
                sb.Append("\r\n");
                sb.Append("小区LAC：");
                sb.Append(cad.iTac);
                sb.Append("\r\n");
                sb.Append("小区CI：");
                sb.Append(cad.iEci);
                sb.Append("\r\n");
                sb.Append("小区EARFCN：");
                sb.Append(cad.uarfcn);
                sb.Append("\r\n");
                sb.Append("小区PCI：");
                sb.Append(cad.pci);
                sb.Append("\r\n");
                sb.Append("小区频段：");
                sb.Append(cad.BandType);
                sb.Append("\r\n");
                sb.Append("小区室分类型：");
                sb.Append(cad.strCover);
                sb.Append("\r\n");
                sb.Append("小区方向角：");
                sb.Append(cad.iangle_dir);
                sb.Append("\r\n");
                sb.Append("小区下倾角：");
                sb.Append(cad.iangle_ob);
                sb.Append("\r\n");
                sb.Append("小区挂高：");
                sb.Append(cad.ialtitude);
                sb.Append("\r\n");
                sb.Append("小区采样点数：");
                sb.Append(cad.bgExt.sampleTotal);
                sb.Append("\r\n");
                sb.Append("小区通讯距离：");
                sb.Append(cad.bgExt.cellDistanceAvg);
                sb.Append("\r\n");
                sb.Append("分析结果：");
                sb.Append(cad.strreslut);
                sb.Append("\r\n");
                sb.Append("告警状态：");
                sb.Append(cad.strwarnstat);
                sb.Append("\r\n");
                sb.Append("360双流功率差>9dB扫描角：");
                sb.Append(cad.bgExt.cellDiff9Angle);
                sb.Append("\r\n");
                sb.Append("360双流功率差>5dB扫描角：");
                sb.Append(cad.bgExt.cellDiff5Angle);
                sb.Append("\r\n");
                sb.Append("360双流功率差>3dB扫描角：");
                sb.Append(cad.bgExt.cellDiff3Angle);
                sb.Append("\r\n");
                sb.Append("主瓣双流功率差>9dB扫描角：");
                sb.Append(cad.bgExt.maincellDiff9Angle);
                sb.Append("\r\n");
                sb.Append("主瓣双流功率差>5dB扫描角：");
                sb.Append(cad.bgExt.maincellDiff5Angle);
                sb.Append("\r\n");
                sb.Append("主瓣双流功率差>3dB扫描角：");
                sb.Append(cad.bgExt.maincellDiff3Angle);
                sb.Append("\r\n");
                sb.Append("RSRP最大差值：");
                sb.Append(cad.bgExt.DRSRPDiffMax);
                sb.Append("\r\n");
                sb.Append("RSRP最大差值角度：");
                sb.Append(cad.bgExt.DRSRPDiffMaxAnt);
                sb.Append("\r\n");
                sb.Append("主瓣RSRP最大差值：");
                sb.Append(cad.bgExt.DRSRPDiffMianMax);
                sb.Append("\r\n");
                sb.Append("主瓣RSRP最大差值角度：");
                sb.Append(cad.bgExt.DRSRPDiffMianMaxAnt);
                sb.Append("\r\n");
                sb.Append("综合覆盖率：");
                sb.Append(cad.bgExt.fCoverRate);
                sb.Append("\r\n");
                sb.Append("R0覆盖率：");
                sb.Append(cad.bgExt.fRSRP0CoverRate);
                sb.Append("\r\n");
                sb.Append("R1覆盖率");
                sb.Append(cad.bgExt.fRSRP1CoverRate);
                sb.Append("\r\n");
                sb.Append("RSRP0均值：");
                sb.Append(cad.bgExt.fDRSRP0AVG);
                sb.Append("\r\n");
                sb.Append("RSRP1均值：");
                sb.Append(cad.bgExt.fDRSRP1AVG);
                sb.Append("\r\n");
                sb.Append("△RSRP：");
                sb.Append(cad.bgExt.fDRSRPDIFF);
                sb.Append("\r\n");
                sb.Append("RSRP稳定度：");
                sb.Append(cad.bgExt.fDRSRP);
                sb.Append("\r\n");
                sb.Append("主瓣RSRP0均值：");
                sb.Append(cad.bgExt.mainDRSRP0AVG);
                sb.Append("\r\n");
                sb.Append("主瓣RSRP1均值：");
                sb.Append(cad.bgExt.mainDRSRP1AVG);
                sb.Append("\r\n");
                sb.Append("主瓣△RSRP：");
                sb.Append(cad.bgExt.mainDRSRPDIFF);
                sb.Append("\r\n");
                sb.Append("主瓣RSRP稳定度：");
                sb.Append(cad.bgExt.mainDRSRP);
                sb.Append("\r\n");
                sb.Append("SINR0均值：");
                sb.Append(cad.bgExt.fDSINR0AVG);
                sb.Append("\r\n");
                sb.Append("SINR1均值：");
                sb.Append(cad.bgExt.fDSINR1AVG);
                sb.Append("\r\n");
                sb.Append("△SINR：");
                sb.Append(cad.bgExt.fDSINRDIFF);
                sb.Append("\r\n");
                sb.Append("SINR稳定度：");
                sb.Append(cad.bgExt.fDSINR);
                bgResult.ImageDesc = sb.ToString();
            }
        }

        public Dictionary<int, CellAngleData> getBackgroundData(int istime, int ietime, string strPro)
        {
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Cell(istime + 1,
                ietime - 1, GetSubFuncID(), Name, StatType, strPro);//时间退1秒，由于存储过程有缺陷
            initBackgroundImageDesc();
            return dicCellAngelDataBG;
        }
        #endregion

        public class CellAngleData
        {
            //小区
            public int isn { get; set; }
            public string strTime { get; set; }
            public string strcityname { get; set; }
            public string strcgi { get; set; }
            public string strbtsname { get; set; }
            public string cellname { get; set; }
            public int iTac { get; set; }
            public int iEci { get; set; }
            public int uarfcn { get; set; }
            public int pci { get; set; }
            public LTEBandTypeJT band { get; set; }
            public string strreslut { get; set; }
            public string strwarnstat { get; set; }

            public string strgridname { get; set; }
            public string strband { get; set; }

            public string strCover { get; set; }//室分类型
            public int sampleTotal { get; set; }//采样点数
            public int iangle_ob { get; set; }//下倾角
            public int ialtitude { get; set; }//挂高
            public int iangle_dir { get; set; }//方向角

            public CellAngleDataExt cExt { get; set; }//小区级字段
            public CellAngleBGDataExt bgExt { get; set; }
            public Dictionary<string, AngleData> angleDatas { get; set; }//角度区间级字段
            public List<String> gridList { get; set; }

            public string GridName
            {
                get
                {
                    if (strgridname != "")
                        return strgridname;

                    StringBuilder strGrid = new StringBuilder();
                    if (gridList == null)
                        return "";
                    else
                    {
                        gridList.Sort();
                        foreach (string grid in gridList)
                        {
                            strGrid.Append(grid);
                            strGrid.Append(",");
                        }
                    }
                    return strGrid.ToString().TrimEnd(',');
                }
            }

            public string BandType
            {
                get
                {
                    if (strband != "")
                        return strband;

                    string strBandType = "";
                    if (band == LTEBandTypeJT.D1 || band == LTEBandTypeJT.D2 || band == LTEBandTypeJT.D3)
                        strBandType = "D频段";
                    else if (band == LTEBandTypeJT.E)
                        strBandType = "E频段";
                    else if (band == LTEBandTypeJT.F)
                        strBandType = "F频段";

                    return strBandType;
                }
            }

            public CellAngleData()
            {
                isn = 0;
                strTime = "";
                strcityname = "";
                strbtsname = "";
                cellname = "";
                iTac = 0;
                iEci = 0;
                uarfcn = 0;
                band = LTEBandTypeJT.Undefined;
                sampleTotal = 0;
                strreslut = "";
                strwarnstat = "";
                strCover = "";
                iangle_ob = 0;
                ialtitude = 0;
                iangle_dir = 0;
                bgExt = new CellAngleBGDataExt();
                cExt = new CellAngleDataExt();
                angleDatas = new Dictionary<string, AngleData>();
                gridList = new List<string>();

                strgridname = "";
                strband = "";
            }

            public BackgroundResult ConvertToBackgroundResult()
            {
                BackgroundResult bgResult = new BackgroundResult();
                bgResult.AddImageValue(strcityname);
                bgResult.AddImageValue(GridName);
                bgResult.AddImageValue(cellname);
                bgResult.AddImageValue(strcgi);
                bgResult.AddImageValue(iTac);
                bgResult.AddImageValue(iEci);
                bgResult.AddImageValue(uarfcn);
                bgResult.AddImageValue(pci);
                bgResult.AddImageValue(strband);
                bgResult.AddImageValue(strCover);
                bgResult.AddImageValue(iangle_dir);
                bgResult.AddImageValue(iangle_ob);
                bgResult.AddImageValue(ialtitude);
                bgResult.AddImageValue(cExt.sampleTotal);
                bgResult.AddImageValue(cExt.cellDistanceAvg.ToString());
                bgResult.AddImageValue(strreslut);
                bgResult.AddImageValue(strwarnstat);
                bgResult.AddImageValue(cExt.cellDiff9Angle.ToString());
                bgResult.AddImageValue(cExt.cellDiff5Angle.ToString());
                bgResult.AddImageValue(cExt.cellDiff3Angle.ToString());
                bgResult.AddImageValue(cExt.maincellDiff9Angle.ToString());
                bgResult.AddImageValue(cExt.maincellDiff5Angle.ToString());
                bgResult.AddImageValue(cExt.maincellDiff3Angle.ToString());
                List<double> tmp = cExt.DRSRPDiffMax;
                bgResult.AddImageValue(tmp[0].ToString());
                bgResult.AddImageValue(tmp[1].ToString());
                tmp = cExt.DRSRPDiffMianMax;
                if (tmp[0] == double.MinValue)
                {
                    bgResult.AddImageValue("-");
                    bgResult.AddImageValue("-");
                }
                else
                {
                    bgResult.AddImageValue(tmp[0].ToString());
                    bgResult.AddImageValue(tmp[1].ToString());
                }
                bgResult.AddImageValue(cExt.StrCoverRate);
                bgResult.AddImageValue(cExt.StrRSRP0CoverRate);
                bgResult.AddImageValue(cExt.StrRSRP1CoverRate);
                bgResult.AddImageValue(cExt.DRSRP0AVG.ToString());
                bgResult.AddImageValue(cExt.DRSRP1AVG.ToString());
                bgResult.AddImageValue(cExt.DRSRPDIFF.ToString());
                bgResult.AddImageValue(cExt.DRSRP.ToString());
                bgResult.AddImageValue(cExt.mainDRSRP0AVG.ToString());
                bgResult.AddImageValue(cExt.mainDRSRP1AVG.ToString());
                bgResult.AddImageValue(cExt.mainDRSRPDIFF.ToString());
                bgResult.AddImageValue(cExt.mainDRSRP.ToString());
                bgResult.AddImageValue(cExt.DSINR0AVG.ToString());
                bgResult.AddImageValue(cExt.DSINR1AVG.ToString());
                bgResult.AddImageValue(cExt.DSINRDIFF.ToString());
                bgResult.AddImageValue(cExt.DSINR.ToString());
                return bgResult;
            }
        }

        /// <summary>
        /// 后台体检数据专用
        /// </summary>
        public class CellAngleBGDataExt
        {
            public CellAngleBGDataExt()
            {
                sampleTotal = 0;
                cellDistanceAvg = 0;
                fCoverRate = 0;
                fRSRP0CoverRate = 0;
                fRSRP1CoverRate = 0;
                fDRSRP0AVG = 0;
                fDRSRP1AVG = 0;
                fDRSRPDIFF = 0;
                fDRSRP = 0;
                fDSINR0AVG = 0;
                fDSINR1AVG = 0;
                fDSINRDIFF = 0;
                fDSINR = 0;

                DRSRPDiffMax = "";
                DRSRPDiffMaxAnt = "";
                DRSRPDiffMianMax = "";
                DRSRPDiffMianMaxAnt = "";
                cellDiff9Angle = "";
                cellDiff5Angle = "";
                cellDiff3Angle = "";
                maincellDiff9Angle = "";
                maincellDiff5Angle = "";
                maincellDiff3Angle = "";
                mainDRSRP0AVG = "";
                mainDRSRP1AVG = "";
                mainDRSRPDIFF = "";
                mainDRSRP = "";
            }

            /// <summary>
            /// 该小区采样点数
            /// </summary>
            public int sampleTotal { get; set; }
            /// <summary>
            /// 小区平均通讯距离
            /// </summary>
            public double cellDistanceAvg { get; set; }
            /// <summary>
            /// 综合覆盖率
            /// </summary>
            public float fCoverRate { get; set; }
            /// <summary>
            /// R0覆盖率
            /// </summary>
            public float fRSRP0CoverRate { get; set; }
            /// <summary>
            /// R1覆盖率
            /// </summary>
            public float fRSRP1CoverRate { get; set; }
            /// <summary>
            /// RSRP0均值
            /// </summary>
            public float fDRSRP0AVG { get; set; }
            /// <summary>
            /// RSRP1均值
            /// </summary>
            public float fDRSRP1AVG { get; set; }
            /// <summary>
            /// △RSRP
            /// </summary>
            public float fDRSRPDIFF { get; set; }
            /// <summary>
            /// RSRP稳定度
            /// </summary>
            public float fDRSRP { get; set; }
            /// <summary>
            /// SINR0均值
            /// </summary>
            public float fDSINR0AVG { get; set; }
            /// <summary>
            /// SINR1均值
            /// </summary>
            public float fDSINR1AVG { get; set; }
            /// <summary>
            /// △SINR
            /// </summary>
            public float fDSINRDIFF { get; set; }
            /// <summary>
            /// SINR稳定度
            /// </summary>
            public float fDSINR { get; set; }
            /// <summary>
            /// △RSRP最大差值以及区间
            /// </summary>
            public string DRSRPDiffMax { get; set; }//RSRP最大差值
            public string DRSRPDiffMaxAnt { get; set; }//RSRP最大差值角度

            /// <summary>
            /// 主瓣△RSRP最大差值以及区间
            /// </summary>
            public string DRSRPDiffMianMax { get; set; }//主瓣RSRP最大差值
            public string DRSRPDiffMianMaxAnt { get; set; }//主瓣RSRP最大差值角度

            public string cellDiff9Angle { get; set; }//360双流功率差值>9dBm
            public string cellDiff5Angle { get; set; }//360双流功率差值>5dBm
            public string cellDiff3Angle { get; set; }//360双流功率差值>3dBm
            public string maincellDiff9Angle { get; set; }//主瓣双流功率差值>9dBm
            public string maincellDiff5Angle { get; set; }//主瓣双流功率差值>5dBm
            public string maincellDiff3Angle { get; set; }//主瓣双流功率差值>3dBm

            public string mainDRSRP0AVG { get; set; }//主瓣RSRP0均值
            public string mainDRSRP1AVG { get; set; }//主瓣RSRP1均值
            public string mainDRSRPDIFF { get; set; }//主瓣△RSRP
            public string mainDRSRP { get; set; }//主瓣RSRP稳定度
        }

        public class CellAngleDataExt
        {
            public CellAngleDataExt()
            {
                angleDatas = new Dictionary<string, AngleData>();
                angleDatasExt = new Dictionary<int, AngeleDataExt>();

                for (int i = 0; i < 360; i++)
                {
                    angleDatasExt.Add(i, new AngeleDataExt());
                }
            }

            /// <summary>
            /// 各区间采样点信息表(双天线的RSRP,SINR)
            /// </summary>
            public Dictionary<string, AngleData> angleDatas { get; set; }
            /// <summary>
            /// 360度信息
            /// </summary>
            public Dictionary<int, AngeleDataExt> angleDatasExt { get; set; }

            /// <summary>
            /// 该小区采样点数
            /// </summary>
            public int sampleTotal
            {
                get
                {
                    int iCount = 0;
                    foreach (string section in angleDatas.Keys)
                    {
                        iCount += angleDatas[section].SampleList.Count;
                    }
                    return iCount;
                }
            }
            /// <summary>
            /// 小区平均通讯距离
            /// </summary>
            public double cellDistanceAvg
            {
                get
                {
                    double cellDistanceSum = 0;
                    foreach (string section in angleDatas.Keys)
                    {
                        foreach (SampleMimoInfo info in angleDatas[section].SampleList)
                        {
                            cellDistanceSum += info.cellDistance;
                        }
                    }
                    return sampleTotal == 0 ? 0 : Math.Round(cellDistanceSum / sampleTotal, 2);
                }
            }
            /// <summary>
            /// 360双流功率差>9dB扫描角
            /// </summary>
            public double cellDiff9Angle
            {
                get
                {
                    int iCount = 0;
                    foreach (int info in angleDatasExt.Keys)
                    {
                        double rsrpSum = 0;
                        for (int i = info - 2; i <= info + 2; i++)
                        {
                            rsrpSum += angleDatasExt[(i + 360) % 360].FRSRP0AVGDiffView;
                        }
                        if (Math.Abs(rsrpSum / 5) > 9)
                        {
                            iCount++;
                        }
                    }
                    return iCount;
                }
            }
            /// <summary>
            /// 360双流功率差>5dB扫描角
            /// </summary>
            public double cellDiff5Angle
            {
                get
                {
                    int iCount = 0;
                    foreach (int info in angleDatasExt.Keys)
                    {
                        double rsrpSum = 0;
                        for (int i = info - 2; i <= info + 2; i++)
                        {
                            rsrpSum += angleDatasExt[(i + 360) % 360].FRSRP0AVGDiffView;
                        }
                        if (Math.Abs(rsrpSum / 5) > 5)
                        {
                            iCount++;
                        }
                    }
                    return iCount;
                }
            }
            /// <summary>
            /// 360双流功率差>3dB扫描角
            /// </summary>
            public double cellDiff3Angle
            {
                get
                {
                    int iCount = 0;
                    foreach (int info in angleDatasExt.Keys)
                    {
                        double rsrpSum = 0;
                        for (int i = info - 2; i <= info + 2; i++)
                        {
                            rsrpSum += angleDatasExt[(i + 360) % 360].FRSRP0AVGDiffView;
                        }
                        if (Math.Abs(rsrpSum / 5) > 3)
                        {
                            iCount++;
                        }
                    }
                    return iCount;
                }
            }
            /// <summary>
            /// 主瓣双流功率差>9dB扫描角
            /// </summary>
            public double maincellDiff9Angle
            {
                get
                {
                    int iCount = 0;
                    foreach (int info in angleDatasExt.Keys)
                    {
                        if (info > 60 && info < 300)
                        {
                            continue;
                        }
                        double rsrpSum = 0;
                        for (int i = info - 2; i <= info + 2; i++)
                        {
                            rsrpSum += angleDatasExt[(i + 360) % 360].FRSRP0AVGDiffView;
                        }
                        if (Math.Abs(rsrpSum / 5) > 9)
                        {
                            iCount++;
                        }
                    }
                    return iCount;
                }
            }
            /// <summary>
            /// 主瓣双流功率差>5dB扫描角
            /// </summary>
            public double maincellDiff5Angle
            {
                get
                {
                    int iCount = 0;
                    foreach (int info in angleDatasExt.Keys)
                    {
                        if (info > 60 && info < 300)
                        {
                            continue;
                        }
                        double rsrpSum = 0;
                        for (int i = info - 2; i <= info + 2; i++)
                        {
                            rsrpSum += angleDatasExt[(i + 360) % 360].FRSRP0AVGDiffView;
                        }
                        if (Math.Abs(rsrpSum / 5) > 5)
                        {
                            iCount++;
                        }
                    }
                    return iCount;
                }
            }
            /// <summary>
            /// 主瓣双流功率差>3dB扫描角
            /// </summary>
            public double maincellDiff3Angle
            {
                get
                {
                    int iCount = 0;
                    foreach (int info in angleDatasExt.Keys)
                    {
                        if (info > 60 && info < 300)
                        {
                            continue;
                        }
                        double rsrpSum = 0;
                        for (int i = info - 2; i <= info + 2; i++)
                        {
                            rsrpSum += angleDatasExt[(i + 360) % 360].FRSRP0AVGDiffView;
                        }
                        if (Math.Abs(rsrpSum / 5) > 3)
                        {
                            iCount++;
                        }
                    }
                    return iCount;
                }
            }
            /// <summary>
            /// 综合覆盖率
            /// </summary>
            public string StrCoverRate
            {
                get
                {
                    int iCoverNum = 0;
                    foreach (string section in angleDatas.Keys)
                    {
                        foreach (SampleMimoInfo info in angleDatas[section].SampleList)
                        {
                            if (info.fRSRP0 >= -110 && info.fSINR0 >= -3
                                && info.fRSRP1 >= -110 && info.fSINR1 >= -3)
                            {
                                iCoverNum++;
                            }
                        }
                    }
                    return sampleTotal == 0 ? "-" : (100.0 * iCoverNum / sampleTotal).ToString("0.00") + "%";
                }
            }
            /// <summary>
            /// R0覆盖率
            /// </summary>
            public string StrRSRP0CoverRate
            {
                get
                {
                    int iCoverNum = 0;
                    foreach (string section in angleDatas.Keys)
                    {
                        foreach (SampleMimoInfo info in angleDatas[section].SampleList)
                        {
                            if (info.fRSRP0 >= -110 && info.fSINR0 >= -3)
                            {
                                iCoverNum++;
                            }
                        }
                    }
                    return sampleTotal == 0 ? "-" : (100.0 * iCoverNum / sampleTotal).ToString("0.00") + "%";
                }
            }
            /// <summary>
            /// R1覆盖率
            /// </summary>
            public string StrRSRP1CoverRate
            {
                get
                {
                    int iCoverNum = 0;
                    foreach (string section in angleDatas.Keys)
                    {
                        foreach (SampleMimoInfo info in angleDatas[section].SampleList)
                        {
                            if (info.fRSRP1 >= -110 && info.fSINR1 >= -3)
                            {
                                iCoverNum++;
                            }
                        }
                    }
                    return sampleTotal == 0 ? "-" : (100.0 * iCoverNum / sampleTotal).ToString("0.00") + "%";
                }
            }
            /// <summary>
            /// RSRP0均值
            /// </summary>
            public double DRSRP0AVG
            {
                get
                {
                    double dRSRP0Sum = 0;
                    foreach (string section in angleDatas.Keys)
                    {
                        foreach (SampleMimoInfo info in angleDatas[section].SampleList)
                        {
                            dRSRP0Sum += info.fRSRP0;
                        }
                    }
                    return sampleTotal == 0 ? 0 : Math.Round(dRSRP0Sum / sampleTotal, 2);
                }
            }
            /// <summary>
            /// 主瓣RSRP0均值
            /// </summary>
            public double mainDRSRP0AVG
            {
                get
                {
                    double dRSRP0Sum = 0;
                    int iMainSampleNum = 0;
                    if (angleDatas.ContainsKey("主瓣[0,60]"))
                    {
                        foreach (SampleMimoInfo info in angleDatas["主瓣[0,60]"].SampleList)
                        {
                            dRSRP0Sum += info.fRSRP0;
                            iMainSampleNum++;
                        }
                    }
                    return iMainSampleNum == 0 ? 0 : Math.Round(dRSRP0Sum / iMainSampleNum, 2);
                }
            }
            /// <summary>
            /// RSRP1均值
            /// </summary>
            public double DRSRP1AVG
            {
                get
                {
                    double dRSRP1Sum = 0;
                    foreach (string section in angleDatas.Keys)
                    {
                        foreach (SampleMimoInfo info in angleDatas[section].SampleList)
                        {
                            dRSRP1Sum += info.fRSRP1;
                        }
                    }
                    return sampleTotal == 0 ? 0 : Math.Round(dRSRP1Sum / sampleTotal, 2);
                }
            }
            /// <summary>
            /// 主瓣RSRP1均值
            /// </summary>
            public double mainDRSRP1AVG
            {
                get
                {
                    double dRSRP1Sum = 0;
                    int iMainSampleNum = 0;
                    if (angleDatas.ContainsKey("主瓣[0,60]"))
                    {
                        foreach (SampleMimoInfo info in angleDatas["主瓣[0,60]"].SampleList)
                        {
                            dRSRP1Sum += info.fRSRP1;
                            iMainSampleNum++;
                        }
                    }
                    return iMainSampleNum == 0 ? 0 : Math.Round(dRSRP1Sum / iMainSampleNum, 2);
                }
            }
            /// <summary>
            /// △RSRP
            /// </summary>
            public double DRSRPDIFF
            {
                get
                {
                    return Math.Round(Math.Abs(DRSRP0AVG - DRSRP1AVG), 2);
                }
            }
            /// <summary>
            /// maiin△RSRP
            /// </summary>
            public double mainDRSRPDIFF
            {
                get
                {
                    return Math.Round(Math.Abs(mainDRSRP0AVG - mainDRSRP1AVG), 2);
                }
            }
            /// <summary>
            /// RSRP稳定度
            /// </summary>
            public double DRSRP
            {
                get
                {
                    double dRSRPSquare = 0;
                    foreach (string section in angleDatas.Keys)
                    {
                        foreach (SampleMimoInfo info in angleDatas[section].SampleList)
                        {
                            dRSRPSquare += Math.Pow((info.fRSRP0 - info.fRSRP1), 2);
                        }
                    }
                    if (sampleTotal == 0)
                    {
                        return 0;
                    }
                    return Math.Round(Math.Sqrt(dRSRPSquare / sampleTotal), 2);
                }
            }
            /// <summary>
            /// mainRSRP稳定度
            /// </summary>
            public double mainDRSRP
            {
                get
                {
                    double dRSRPSquare = 0;
                    int iMainCount = 0;
                    if (angleDatas.ContainsKey("主瓣[0,60]"))
                    {
                        foreach (SampleMimoInfo info in angleDatas["主瓣[0,60]"].SampleList)
                        {
                            dRSRPSquare += Math.Pow((info.fRSRP0 - info.fRSRP1), 2);
                            iMainCount++;
                        }
                    }
                    return iMainCount == 0 ? 0 : Math.Round(Math.Sqrt(dRSRPSquare / iMainCount), 2);
                }
            }
            /// <summary>
            /// SINR0均值
            /// </summary>
            public double DSINR0AVG
            {
                get
                {
                    double dSINR0Sum = 0;
                    foreach (string section in angleDatas.Keys)
                    {
                        foreach (SampleMimoInfo info in angleDatas[section].SampleList)
                        {
                            dSINR0Sum += info.fSINR0;
                        }
                    }
                    return sampleTotal == 0 ? 0 : Math.Round(dSINR0Sum / sampleTotal, 2);
                }
            }
            /// <summary>
            /// SINR1均值
            /// </summary>
            public double DSINR1AVG
            {
                get
                {
                    double dSINR1Sum = 0;
                    foreach (string section in angleDatas.Keys)
                    {
                        foreach (SampleMimoInfo info in angleDatas[section].SampleList)
                        {
                            dSINR1Sum += info.fSINR1;
                        }
                    }
                    return sampleTotal == 0 ? 0 : Math.Round(dSINR1Sum / sampleTotal, 2);
                }
            }
            /// <summary>
            /// △SINR
            /// </summary>
            public double DSINRDIFF
            {
                get
                {
                    return Math.Round(Math.Abs(DSINR0AVG - DSINR1AVG), 2);
                }
            }
            /// <summary>
            /// SINR稳定度
            /// </summary>
            public double DSINR
            {
                get
                {
                    double dSINRSquare = 0;
                    foreach (string section in angleDatas.Keys)
                    {
                        foreach (SampleMimoInfo info in angleDatas[section].SampleList)
                        {
                            dSINRSquare += Math.Pow((info.fSINR0 - info.fSINR1), 2);
                        }
                    }
                    if (sampleTotal == 0)
                    {
                        return 0;
                    }
                    return Math.Round(Math.Sqrt(dSINRSquare / sampleTotal), 2);
                }
            }
            /// <summary>
            /// △RSRP最大差值以及区间
            /// </summary>
            public List<double> DRSRPDiffMax
            {
                get
                {
                    List<double> dRrspDiffMax = new List<double> { double.MinValue, 0 };
                    foreach (int id in angleDatasExt.Keys)
                    {
                        if (dRrspDiffMax[0] < angleDatasExt[id].FRSRP0AVGDiff
                            && !double.IsNaN(angleDatasExt[id].FRSRP0AVGDiff))
                        {
                            dRrspDiffMax[0] = angleDatasExt[id].FRSRP0AVGDiff;
                            dRrspDiffMax[1] = id;
                        }
                    }
                    return dRrspDiffMax;
                }
            }
            /// <summary>
            /// 主瓣△RSRP最大差值以及区间
            /// </summary>
            public List<double> DRSRPDiffMianMax
            {
                get
                {
                    List<double> dRrspDiffMax = new List<double> { double.MinValue, 0 };
                    foreach (int id in angleDatasExt.Keys)
                    {
                        if (dRrspDiffMax[0] < angleDatasExt[id].FRSRP0AVGDiff
                            && angleDatasExt[id].strsection.Contains("主瓣")
                            && !double.IsNaN(angleDatasExt[id].FRSRP0AVGDiff))
                        {
                            dRrspDiffMax[0] = angleDatasExt[id].FRSRP0AVGDiff;
                            dRrspDiffMax[1] = id;
                        }
                    }
                    return dRrspDiffMax;
                }
            }

            private int iRSRP0_1_Sample = 0;
            /// <summary>
            /// |R0-R1|>5dB采样点
            /// </summary>
            public int IRSRP0_1_Sample
            {
                get { return this.iRSRP0_1_Sample; }
            }
            /// <summary>
            /// |R0-R1|>5dB占比
            /// </summary>
            public string dRSRP0_1_Rate
            {
                get { return (sampleTotal == 0 ? 0 : Math.Round(iRSRP0_1_Sample * 1.0 / sampleTotal * 100, 2)).ToString() + "%"; }
            }

            private int iSINR0_1_Sample = 0;
            /// <summary>
            /// |SINR0-SINR1|>5dB采样点
            /// </summary>
            public int ISINR0_1_Sample
            {
                get { return this.iSINR0_1_Sample; }
            }

            /// <summary>
            /// |SINR0-SINR1|>5dB占比
            /// </summary>
            public string dSINR0_1_Rate
            {
                get { return (sampleTotal == 0 ? 0 : Math.Round(iSINR0_1_Sample * 1.0 / sampleTotal * 100, 2)).ToString() + "%"; }
            }
            public void doStatRSPP_SINR()
            {
                foreach (string section in angleDatas.Keys)
                {
                    foreach (SampleMimoInfo info in angleDatas[section].SampleList)
                    {
                        if (Math.Abs(info.fRSRP0 - info.fRSRP1) > 5)
                            this.iRSRP0_1_Sample++;
                        if (Math.Abs(info.fSINR0 - info.fSINR1) > 5)
                            this.iSINR0_1_Sample++;
                    }
                }
            }
        }

        public class AngleData
        {
            public AngleData()
            {
                ianglesn = 0;
                SampleList = new List<SampleMimoInfo>();
                angleDatasExt = new Dictionary<int, AngeleDataExt>();

                for (int i = 0; i < 360; i++)
                {
                    angleDatasExt.Add(i, new AngeleDataExt());
                }
            }

            /// <summary>
            /// 序号
            /// </summary>
            public int ianglesn { get; set; }
            /// <summary>
            /// 采样点信息表(双天线的RSRP,SINR)
            /// </summary>
            public List<SampleMimoInfo> SampleList { get; set; }
            /// <summary>
            /// 360度情况
            /// </summary>
            public Dictionary<int, AngeleDataExt> angleDatasExt { get; set; }
            /// <summary>
            /// 采样点数
            /// </summary>
            public int SampleNum
            {
                get { return SampleList.Count; }
            }
            /// <summary>
            /// 小区平均通讯距离
            /// </summary>
            public double cellDistanceAvg
            {
                get
                {
                    double cellDistanceSum = 0;
                    foreach (SampleMimoInfo info in SampleList)
                    {
                        cellDistanceSum += info.cellDistance;
                    }
                    return SampleNum == 0 ? 0 : Math.Round(cellDistanceSum / SampleNum, 2);
                }
            }
            /// <summary>
            /// 双流功率差>5dB扫描角
            /// </summary>
            public double cellDiff5Angle(string section)
            {
                int iCount = 0;
                foreach (int info in angleDatasExt.Keys)
                {
                    if ((section.Contains("主瓣") && info > 60 && info < 300)
                        || (section.Contains("旁瓣") && ((info >= 300 || info <= 60) || (info > 150 && info < 210)))
                        || (section.Contains("背瓣") && (info >= 210 || info <= 150)))
                    {
                        continue;
                    }
                    double rsrpSum = 0;
                    for (int i = info - 2; i <= info + 2; i++)
                    {
                        rsrpSum += angleDatasExt[(i + 360) % 360].FRSRP0AVGDiffView;
                    }
                    if (Math.Abs(rsrpSum / 5) > 5)
                    {
                        iCount++;
                    }
                }
                return iCount;
            }
            /// <summary>
            /// 双流功率差>3dB扫描角
            /// </summary>
            public double cellDiff3Angle(string section)
            {
                int iCount = 0;
                foreach (int info in angleDatasExt.Keys)
                {
                    if ((section.Contains("主瓣") && info > 60 && info < 300)
                        || (section.Contains("旁瓣") && ((info >= 300 || info <= 60) || (info > 150 && info < 210)))
                        || (section.Contains("背瓣") && (info > 210 || info < 150)))
                    {
                        continue;
                    }
                    double rsrpSum = 0;
                    for (int i = info - 2; i <= info + 2; i++)
                    {
                        rsrpSum += angleDatasExt[(i + 360) % 360].FRSRP0AVGDiffView;
                    }
                    if (Math.Abs(rsrpSum / 5) > 3)
                    {
                        iCount++;
                    }
                }
                return iCount;
            }
            /// <summary>
            /// 综合覆盖率
            /// </summary>
            public string StrCoverRate
            {
                get
                {
                    int iCoverNum = 0;
                    foreach (SampleMimoInfo info in SampleList)
                    {
                        if (info.fRSRP0 >= -110 && info.fSINR0 >= -3
                            && info.fRSRP1 >= -110 && info.fSINR1 >= -3)
                        {
                            iCoverNum++;
                        }
                    }
                    return SampleList.Count == 0 ? "-" : (100.0 * iCoverNum / SampleList.Count).ToString("0.00") + "%";
                }
            }
            /// <summary>
            /// R0覆盖率
            /// </summary>
            public string StrRSRP0CoverRate
            {
                get
                {
                    int iCoverNum = 0;
                    foreach (SampleMimoInfo info in SampleList)
                    {
                        if (info.fRSRP0 >= -110 && info.fSINR0 >= -3)
                        {
                            iCoverNum++;
                        }
                    }
                    return SampleList.Count == 0 ? "-" : (100.0 * iCoverNum / SampleList.Count).ToString("0.00") + "%";
                }
            }
            /// <summary>
            /// R1覆盖率
            /// </summary>
            public string StrRSRP1CoverRate
            {
                get
                {
                    int iCoverNum = 0;
                    foreach (SampleMimoInfo info in SampleList)
                    {
                        if (info.fRSRP1 >= -110 && info.fSINR1 >= -3)
                        {
                            iCoverNum++;
                        }
                    }
                    return SampleList.Count == 0 ? "-" : (100.0 * iCoverNum / SampleList.Count).ToString("0.00") + "%";
                }
            }
            /// <summary>
            /// RSRP0均值
            /// </summary>
            public double DRSRP0AVG
            {
                get
                {
                    double dRSRP0Sum = 0;
                    foreach (SampleMimoInfo info in SampleList)
                    {
                        dRSRP0Sum += info.fRSRP0;
                    }
                    return SampleList.Count == 0 ? 0 : Math.Round(dRSRP0Sum / SampleList.Count, 2);
                }
            }
            /// <summary>
            /// RSRP1均值
            /// </summary>
            public double DRSRP1AVG
            {
                get
                {
                    double dRSRP1Sum = 0;
                    foreach (SampleMimoInfo info in SampleList)
                    {
                        dRSRP1Sum += info.fRSRP1;
                    }
                    return SampleList.Count == 0 ? 0 : Math.Round(dRSRP1Sum / SampleList.Count, 2);
                }
            }
            /// <summary>
            /// △RSRP
            /// </summary>
            public double DRSRPDIFF
            {
                get
                {
                    return Math.Round(Math.Abs(DRSRP0AVG - DRSRP1AVG), 2);
                }
            }
            /// <summary>
            /// RSRP稳定度
            /// </summary>
            public double DRSRP
            {
                get
                {
                    double dRSRPSquare = 0;
                    foreach (SampleMimoInfo info in SampleList)
                    {
                        dRSRPSquare += Math.Pow((info.fRSRP0 - info.fRSRP1), 2);
                    }
                    if (SampleList.Count == 0)
                    {
                        return 0;
                    }
                    return Math.Round(Math.Sqrt(dRSRPSquare / SampleList.Count), 2);
                }
            }
            /// <summary>
            /// SINR0均值
            /// </summary>
            public double DSINR0AVG
            {
                get
                {
                    double dSINR0Sum = 0;
                    foreach (SampleMimoInfo info in SampleList)
                    {
                        dSINR0Sum += info.fSINR0;
                    }
                    return SampleList.Count == 0 ? 0 : Math.Round(dSINR0Sum / SampleList.Count, 2);
                }
            }
            /// <summary>
            /// SINR1均值
            /// </summary>
            public double DSINR1AVG
            {
                get
                {
                    double dSINR1Sum = 0;
                    foreach (SampleMimoInfo info in SampleList)
                    {
                        dSINR1Sum += info.fSINR1;
                    }
                    return SampleList.Count == 0 ? 0 : Math.Round(dSINR1Sum / SampleList.Count, 2);
                }
            }
            /// <summary>
            /// △SINR
            /// </summary>
            public double DSINRDIFF
            {
                get
                {
                    return Math.Round(Math.Abs(DSINR0AVG - DSINR1AVG), 2);
                }
            }
            /// <summary>
            /// SINR稳定度
            /// </summary>
            public double DSINR
            {
                get
                {
                    double dSINRSquare = 0;
                    foreach (SampleMimoInfo info in SampleList)
                    {
                        dSINRSquare += Math.Pow((info.fSINR0 - info.fSINR1), 2);
                    }
                    if (SampleList.Count == 0)
                    {
                        return 0;
                    }
                    return Math.Round(Math.Sqrt(dSINRSquare / SampleList.Count), 2);
                }
            }
            /// <summary>
            /// △RSRP最大差值以及区间
            /// </summary>
            public List<double> DRSRPDiffMax(string strsection)
            {
                List<double> dRrspDiffMax = new List<double> { double.MinValue, 0 };
                foreach (int id in angleDatasExt.Keys)
                {
                    if (dRrspDiffMax[0] < angleDatasExt[id].FRSRP0AVGDiff
                        && angleDatasExt[id].strsection == strsection
                        && !double.IsNaN(angleDatasExt[id].FRSRP0AVGDiff))
                    {
                        dRrspDiffMax[0] = angleDatasExt[id].FRSRP0AVGDiff;
                        dRrspDiffMax[1] = id;
                    }
                }
                return dRrspDiffMax;
            }
        }

        public class AngeleDataExt
        {
            public AngeleDataExt()
            {
                strsection = "";
                FAngele = 0;
                FRSRP0Sum = 0;
                FRSRP1Sum = 0;
                FSINR0Sum = 0;
                FSINR1Sum = 0;
                FSampleNum = 0;
            }

            public string strsection { get; set; }
            public float FAngele { get; set; }
            public float FRSRP0Sum { get; set; }
            public float FRSRP1Sum { get; set; }
            public float FSINR0Sum { get; set; }
            public float FSINR1Sum { get; set; }
            public float FSampleNum { get; set; }

            public int iDiff5Angle
            {
                get
                {
                    return Math.Abs(FRSRP0AVG - FRSRP1AVG) > 5 ? 1 : 0;
                }
            }
            public int iDiff3Angle
            {
                get
                {
                    return Math.Abs(FRSRP0AVG - FRSRP1AVG) > 3 ? 1 : 0;
                }
            }

            public double FRSRP0AVG
            {
                get
                {
                    if (FSampleNum == 0)
                    {
                        return -140;
                    }
                    else
                    {
                        return Math.Round(FRSRP0Sum / FSampleNum, 2);
                    }
                }
            }
            public double FRSRP1AVG
            {
                get
                {
                    if (FSampleNum == 0)
                    {
                        return -140;
                    }
                    else
                    {
                        return Math.Round(FRSRP1Sum / FSampleNum, 2);
                    }
                }
            }
            public double FRSRP0AVGDiff
            {
                get
                {
                    if (FSampleNum == 0)
                    {
                        return 0;
                    }
                    return Math.Round(Math.Abs(FRSRP0Sum / FSampleNum - FRSRP1Sum / FSampleNum), 2);
                }
            }

            public double FRSRP0AVGDiffView
            {
                get
                {
                    if (FSampleNum == 0)
                    {
                        return 0;
                    }
                    return Math.Round(FRSRP0Sum / FSampleNum - FRSRP1Sum / FSampleNum, 2);
                }
            }
            private double DRSRPDiffPow = 0;
            public double FRSRP
            {
                get
                {
                    if (FSampleNum == 0)
                    {
                        return 0;
                    }
                    return Math.Round(Math.Sqrt(DRSRPDiffPow / FSampleNum), 2);
                }
            }

            public double FSINR0AVG
            {
                get
                {
                    if (FSampleNum == 0)
                    {
                        return -50;
                    }
                    else
                    {
                        return Math.Round(FSINR0Sum / FSampleNum, 2);
                    }
                }
            }
            public double FSINR1AVG
            {
                get
                {
                    if (FSampleNum == 0)
                    {
                        return -50;
                    }
                    else
                    {
                        return Math.Round(FSINR1Sum / FSampleNum, 2);
                    }
                }
            }

            public void UpdateRsrpAndSinr(SampleMimoInfo rsrpsinr)
            {
                this.FRSRP0Sum += rsrpsinr.fRSRP0;
                this.FRSRP1Sum += rsrpsinr.fRSRP1;
                this.FSINR0Sum += rsrpsinr.fSINR0;
                this.FSINR1Sum += rsrpsinr.fSINR1;
                FSampleNum += 1;
                DRSRPDiffPow += Math.Pow(rsrpsinr.fRSRP0 - rsrpsinr.fRSRP1, 2);
            }
        }
    }

    public class SampleMimoInfo
    {
        public float fRSRP0 { get; set; }
        public float fSINR0 { get; set; }
        public float fRSRP1 { get; set; }
        public float fSINR1 { get; set; }
        public double cellDistance { get; set; }
        public int iAngleDiffRel { get; set; }//相对夹角
        public int iAngleDiffAbs { get; set; }//相对夹角(绝对值)
        public LTECell lteCell { get; set; }
        public int iEci { get; set; }
        public int iEarfcn { get; set; }
        public int iPci { get; set; }

        public string CellName
        {
            get
            {
                string strCellName = "";
                if (lteCell != null)
                    strCellName = lteCell.Name;
                else
                    strCellName = string.Format("460-00-{0}-{1}", (iEci / 256), iEci - (iEci / 256) * 256);

                return strCellName;
            }
        }

        public string CGI
        {
            get
            {
                string strCGI = "";
                if (lteCell != null)
                    strCGI = string.Format("460-00-{0}-{1}", lteCell.BelongBTS.BTSID, lteCell.SectorID);
                else
                    strCGI = string.Format("460-00-{0}-{1}", (iEci / 256), iEci - (iEci / 256) * 256);

                return strCGI;
            }
        }

        public string BtsName
        {
            get
            {
                string strBtsName = "";
                if (lteCell != null)
                    strBtsName = lteCell.BelongBTS.Name;
                else
                    strBtsName = string.Format("460-00-{0}", (iEci / 256));

                return strBtsName;
            }
        }

        public int ECI
        {
            get
            {
                if (lteCell != null)
                    return lteCell.ECI;
                else
                    return iEci;
            }
        }

        public int Earfcn
        {
            get
            {
                if (lteCell != null)
                    return lteCell.EARFCN;
                else
                    return iEarfcn;
            }
        }

        public int PCI
        {
            get
            {
                if (lteCell != null)
                    return lteCell.PCI;
                else
                    return iPci;
            }
        }

        public SampleMimoInfo(float fRSRP0, float fSINR0, float fRSRP1, float fSINR1, LTECell lteCell)
        {
            this.fRSRP0 = fRSRP0;
            this.fSINR0 = fSINR0;
            this.fRSRP1 = fRSRP1;
            this.fSINR1 = fSINR1;
            this.lteCell = lteCell;
        }

        public void SetOtherInfo(double cellDistance, int iAngleDiffRel, int iAngleDiffAbs)
        {
            this.cellDistance = cellDistance;
            this.iAngleDiffRel = iAngleDiffRel;
            this.iAngleDiffAbs = iAngleDiffAbs;
        }

        public SampleMimoInfo(float fRSRP0, float fSINR0, float fRSRP1, float fSINR1, int iEci, int iEarfcn, int iPci)
        {
            this.fRSRP0 = fRSRP0;
            this.fSINR0 = fSINR0;
            this.fRSRP1 = fRSRP1;
            this.fSINR1 = fSINR1;
            this.iEci = iEci;
            this.iEarfcn = iEarfcn;
            this.iPci = iPci;
            cellDistance = 0;
            iAngleDiffRel = 0;
            iAngleDiffAbs = 0;
        }
    }

    public class MiMoCfgInfo
    {
        public MiMoCfgInfo()
        {
            bChanelAna = false;
            bChanelD = false;
            bChanelE = false;
            bChanelF = false;
            iChanelNum = 3;

            bSampleN = false;
            iSampleN = 9;

            bRSRP0 = false;
            fRsrp0Min = -140;
            fRsrp0Max = 25;

            bRSRP1 = false;
            fRsrp1Min = -140;
            fRsrp1Max = 25;

            bValue = false;
        }

        public bool bChanelAna { get; set; }
        public bool bChanelD { get; set; }
        public bool bChanelE { get; set; }
        public bool bChanelF { get; set; }
        public int iChanelNum { get; set; }

        public bool bSampleN { get; set; }
        public int iSampleN { get; set; }

        public bool bRSRP0 { get; set; }
        public float fRsrp0Min { get; set; }
        public float fRsrp0Max { get; set; }

        public bool bRSRP1 { get; set; }
        public float fRsrp1Min { get; set; }
        public float fRsrp1Max { get; set; }

        public bool bValue { get; set; }
    }
}
