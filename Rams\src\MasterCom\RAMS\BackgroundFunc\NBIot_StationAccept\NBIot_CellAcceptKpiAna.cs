﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Imaging;
using MasterCom.RAMS.Model;
using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.BackgroundFunc
{
    public enum NBIotKpiKey
    {
        AttachAvgRSRP = 0x04020000,
        AttachAvgSINR = 0x04020001,
        AttachRate = 0x04020002,

        RrcAvgRSRP = 0x04020003,
        RrcAvgSINR = 0x04020004,
        RrcRate = 0x04020005,

        PingAvgRSRP = 0x04020006,
        PingAvgSINR = 0x04020007,
        PingDelay = 0x04020008,

        ULAvgRSRP = 0x04020009,
        ULAvgSINR = 0x0402000A,
        ULThroughputRate = 0x0402000B,

        DLAvgRSRP = 0x0402000C,
        DLAvgSINR = 0x0402000D,
        DLThroughputRate = 0x0402000E,

        CoverRsrpPath = 0x0402000F,
        CoverSinrPath = 0x04020010,
        CoverULPath = 0x04020011,
        CoverAvgRSRP = 0x04020012,
        CoverAvgSINR = 0x04020013,
        CoverAvgSpeed = 0x04020014,
        CoverSpeedUpTen = 0x04020015,
        CoverCoverRate = 0x04020016,
        AntennaOppositeRate = 0x04020017,
    }

    public abstract class NbIotCellAcceptKpiAna
    {
        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public virtual Dictionary<NBIotKpiKey, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            return new Dictionary<NBIotKpiKey, object>();
        }

        public virtual bool IsValidFile(FileInfo fileInfo)
        {
            return false;
        }

        protected LTECell getTpSrcCell(TestPoint tp)
        {
            return NbIotStationAcceptManager.GetTpSrcCell(tp);
        }

        protected void reportInfo(string strInfo)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(strInfo);
            }
            else
            {
                log.Info(strInfo);
            }
        }

        protected void reportInfo(Exception ex)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
            }
            else
            {
                System.Windows.Forms.MessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
            }
        }
    }

    #region 室外站
    class NBIotAcpAutoAttachRate : NbIotCellAcceptKpiAna
    {
        protected List<int> evtSuccList;
        protected List<int> evtFailList;
        protected List<int> evtRequList;

        public NBIotAcpAutoAttachRate()
        {
            evtRequList = new List<int> { 5001 };
            evtSuccList = new List<int> { 5002 };
            evtFailList = new List<int> { 5003 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("ATTACH");
        }

        public override Dictionary<NBIotKpiKey, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpi kpiCell = anaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                reportInfo(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return new Dictionary<NBIotKpiKey, object>();
            }

            if (filterDataINfo(kpiCell, fileInfo))
            {
                return getKpiInfos(kpiCell);
            }
            else
            {
                return new Dictionary<NBIotKpiKey, object>();
            }
        }

        protected virtual NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpi targetKpiCell = new NbIotCellKpi(targetCell.Name);
            judgeEventSuccessRate(fileManager, targetKpiCell);

            //获取平均RSRP,SINR,判断RSRP,SINR是否满足条件
            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);

            return targetKpiCell;
        }

        protected virtual void getTestPointKpi(List<TestPoint> testPoints, LTECell targetCell, NbIotCellKpi targetKpiCell)
        {
            foreach (TestPoint tp in testPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell != null && cell.Token == targetCell.Token)
                {
                    targetKpiCell.AddPoint(tp);
                }
            }
        }

        private void judgeEventSuccessRate(DTFileDataManager fileManager, NbIotCellKpi targetKpiCell)
        {
            //判断事件成功率
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++targetKpiCell.RequestCnt;
                }
                else if (evtSuccList.Contains(evt.ID))
                {
                    ++targetKpiCell.SucceedCnt;
                }
                else if (evtFailList.Contains(evt.ID))
                {
                    ++targetKpiCell.FailedCnt;
                }
            }
        }

        protected virtual bool filterDataINfo(NbIotCellKpi kpiCell, FileInfo fileInfo)
        {
            if (kpiCell.RequestCnt < 5)
            {
                reportInfo(string.Format("文件{0}测试次数不足5次", fileInfo.Name));
                return false;
            }
            if (kpiCell.AvgRsrp < -80 || kpiCell.AvgSinr < 20)
            {
                reportInfo(string.Format("文件{0}rsrp<-80或sinr<20", fileInfo.Name));
                return false;
            }
            return true;
        }

        protected virtual Dictionary<NBIotKpiKey, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NBIotKpiKey, object> kpiInfos = new Dictionary<NBIotKpiKey, object>();
            kpiInfos.Add(NBIotKpiKey.AttachAvgRSRP, kpiCell.AvgRsrp);
            kpiInfos.Add(NBIotKpiKey.AttachAvgSINR, kpiCell.AvgSinr);
            kpiInfos.Add(NBIotKpiKey.AttachRate, kpiCell.SuccessRate);
            return kpiInfos;
        }

        public class NbIotCellKpi
        {
            public NbIotCellKpi(string cellName)
            {
                this.CellName = cellName;
            }
            public string CellName { get; set; }
            public int PointCount { get; protected set; }

            protected double sumSinr;
            protected int cntSinr;

            protected double sumRsrp;
            protected int cntRsrp;

            public double AvgSinr
            {
                get { return cntSinr == 0 ? double.MinValue : Math.Round(sumSinr / cntSinr, 2); }
            }
            public double AvgRsrp
            {
                get { return cntRsrp == 0 ? double.MinValue : Math.Round(sumRsrp / cntRsrp, 2); }
            }

            //事件请求数
            public int RequestCnt { get; set; }
            //事件成功数
            public int SucceedCnt { get; set; }
            //事件失败数
            public int FailedCnt { get; set; }
            //成功率
            public double SuccessRate
            {
                get
                {
                    if (RequestCnt != 0)
                    {
                        return (SucceedCnt * 100d / RequestCnt);
                    }
                    else if (SucceedCnt + FailedCnt != 0)
                    {
                        return (SucceedCnt * 100d / (SucceedCnt + FailedCnt));
                    }
                    else
                    {
                        return 0;
                    }
                }
            }

            public void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                }
            }
        }
    }

    class NBIotAcpAutoRrcRate : NBIotAcpAutoAttachRate
    {
        public NBIotAcpAutoRrcRate()
        {
            evtRequList = new List<int> { 5011 };
            evtSuccList = new List<int> { 5012 };
            evtFailList = new List<int> { 5013 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("RRC");
        }

        protected override NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpi targetKpiCell = new NbIotCellKpi(targetCell.Name);
            Event prevEvt = null;
            bool flag = false;
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    //与上一个请求事件间隔10s以上才为有效事件
                    if (prevEvt == null || prevEvt.DateTime.Subtract(evt.DateTime).TotalSeconds >= 10)
                    {
                        ++targetKpiCell.RequestCnt;
                        flag = true;
                    }
                    prevEvt = evt;
                }
                else if (evtSuccList.Contains(evt.ID) && flag)
                {
                    ++targetKpiCell.SucceedCnt;
                    flag = false;
                }
                else if (evtFailList.Contains(evt.ID) && flag)
                {
                    ++targetKpiCell.FailedCnt;
                    flag = false;
                }
            }

            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);

            return targetKpiCell;
        }

        protected override bool filterDataINfo(NbIotCellKpi kpiCell, FileInfo fileInfo)
        {
            if (kpiCell.RequestCnt < 10)
            {
                reportInfo(string.Format("文件{0}测试次数不足10次", fileInfo.Name));
                return false;
            }
            if (kpiCell.AvgRsrp < -80 || kpiCell.AvgSinr < 20)
            {
                reportInfo(string.Format("文件{0}rsrp<-80或sinr<20", fileInfo.Name));
                return false;
            }
            return true;
        }

        protected override Dictionary<NBIotKpiKey, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NBIotKpiKey, object> kpiInfos = new Dictionary<NBIotKpiKey, object>();
            kpiInfos.Add(NBIotKpiKey.RrcAvgRSRP, kpiCell.AvgRsrp);
            kpiInfos.Add(NBIotKpiKey.RrcAvgSINR, kpiCell.AvgSinr);
            kpiInfos.Add(NBIotKpiKey.RrcRate, kpiCell.SuccessRate);
            return kpiInfos;
        }
    }

    class NBIotAcpAutoPingDelay : NBIotAcpAutoAttachRate
    {
        public NBIotAcpAutoPingDelay()
        {
            evtSuccList = new List<int> { 5006 };
            evtFailList = new List<int> { 5007 };
        }
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("PING");
        }

        protected override NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NBiotCellKpiPingDelay targetKpiCell = new NBiotCellKpiPingDelay(targetCell.Name);
            foreach (Event evt in fileManager.Events)
            {
                if (evtSuccList.Contains(evt.ID))
                {
                    targetKpiCell.TotalEvtList.Add(evt);
                }
                if (evtFailList.Contains(evt.ID))
                {
                    targetKpiCell.TotalEvtList.Add(evt);
                }
            }

            targetKpiCell.CalculatePingDelay();

            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);

            return targetKpiCell;
        }

        protected override bool filterDataINfo(NbIotCellKpi kpiCell, FileInfo fileInfo)
        {
            if (((NBiotCellKpiPingDelay)kpiCell).TestCount < 10)
            {
                reportInfo(string.Format("文件{0}测试次数不足10次", fileInfo.Name));
                return false;
            }
            if (kpiCell.AvgRsrp < -80 || kpiCell.AvgSinr < 20)
            {
                reportInfo(string.Format("文件{0}rsrp<-80或sinr<20", fileInfo.Name));
                return false;
            }
            return true;
        }

        protected override Dictionary<NBIotKpiKey, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NBIotKpiKey, object> kpiInfos = new Dictionary<NBIotKpiKey, object>();
            kpiInfos.Add(NBIotKpiKey.PingAvgRSRP, kpiCell.AvgRsrp);
            kpiInfos.Add(NBIotKpiKey.PingAvgSINR, kpiCell.AvgSinr);
            kpiInfos.Add(NBIotKpiKey.PingDelay, ((NBiotCellKpiPingDelay)kpiCell).AvgDelay / 1000);
            return kpiInfos;
        }

        protected class NBiotCellKpiPingDelay : NbIotCellKpi
        {
            public NBiotCellKpiPingDelay(string cellName)
                : base(cellName)
            {
            }

            public List<Event> TotalEvtList = new List<Event>();

            /// <summary>
            /// 有效的测试次数
            /// </summary>
            public int TestCount { get; private set; }

            /// <summary>
            /// 平均时延(毫秒)
            /// </summary>
            public double AvgDelay { get; private set; }

            protected List<int> evtSuccList = new List<int> { 5006 };
            public void CalculatePingDelay()
            {
                double sumDelay = 0;
                int validEvtNum = 0;
                Event prevEvt = null;
                foreach (Event evt in TotalEvtList)
                {
                    if (evtSuccList.Contains(evt.ID))
                    {
                        //2次ping事件间隔大于2秒
                        if (prevEvt == null || evt.DateTime.Subtract(prevEvt.DateTime).TotalSeconds >= 2)
                        {
                            sumDelay += int.Parse(evt["Value1"].ToString());
                            validEvtNum++;
                        }
                        prevEvt = evt;
                    }
                }
                if (validEvtNum > 0)
                {
                    TestCount = validEvtNum;
                    AvgDelay = sumDelay / validEvtNum;
                }
                else
                {
                    AvgDelay = double.MinValue;
                }
            }
        }
    }

    class NBIotAcpAutoULSpeed : NBIotAcpAutoAttachRate
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上行");
        }

        protected override NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpiUL targetKpiCell = new NbIotCellKpiUL(targetCell.Name);
            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);
            return targetKpiCell;
        }

        protected override bool filterDataINfo(NbIotCellKpi kpiCell, FileInfo fileInfo)
        {
            return true;
        }

        protected override Dictionary<NBIotKpiKey, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NBIotKpiKey, object> kpiInfos = new Dictionary<NBIotKpiKey, object>();
            kpiInfos.Add(NBIotKpiKey.ULAvgRSRP, kpiCell.AvgRsrp);
            kpiInfos.Add(NBIotKpiKey.ULAvgSINR, kpiCell.AvgSinr);
            kpiInfos.Add(NBIotKpiKey.ULThroughputRate, ((NbIotCellKpiUL)kpiCell).AvgMacUL);
            return kpiInfos;
        }

        public class NbIotCellKpiUL : NbIotCellKpi
        {
            public NbIotCellKpiUL(string cellName)
                : base(cellName)
            {
            }

            protected double sumMacUL;
            protected int cntMacUL;
            public double AvgMacUL
            {
                get { return cntMacUL == 0 ? double.MinValue : Math.Round(sumMacUL / cntMacUL, 2); }
            }

            public new void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                }

                int? macUL = (int?)tp["lte_MAC_UL"];
                if (macUL != null)
                {
                    ++cntMacUL;
                    sumMacUL += (float)macUL;
                }
            }
        }
    }

    class NBIotAcpAutoDLSpeed : NBIotAcpAutoAttachRate
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下行");
        }

        protected override NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpiDL targetKpiCell = new NbIotCellKpiDL(targetCell.Name);
            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);
            return targetKpiCell;
        }

        protected override Dictionary<NBIotKpiKey, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NBIotKpiKey, object> kpiInfos = new Dictionary<NBIotKpiKey, object>();
            kpiInfos.Add(NBIotKpiKey.DLAvgRSRP, kpiCell.AvgRsrp);
            kpiInfos.Add(NBIotKpiKey.DLAvgSINR, kpiCell.AvgSinr);
            kpiInfos.Add(NBIotKpiKey.DLThroughputRate, ((NbIotCellKpiDL)kpiCell).AvgMacDL);
            return kpiInfos;
        }

        protected override bool filterDataINfo(NbIotCellKpi kpiCell, FileInfo fileInfo)
        {
            return true;
        }

        public class NbIotCellKpiDL : NbIotCellKpi
        {
            public NbIotCellKpiDL(string cellName)
                : base(cellName)
            {
            }

            protected double sumMacDL;
            protected int cntMacDL;
            public double AvgMacDL
            {
                get { return cntMacDL == 0 ? double.MinValue : Math.Round(sumMacDL / cntMacDL, 2); }
            }

            public new void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                }

                int? macDL = (int?)tp["lte_MAC_DL"];
                if (macDL != null)
                {
                    ++cntMacDL;
                    sumMacDL += (float)macDL;
                }
            }
        }
    }

    class NBIotAcpAutoCoverPicture : NBIotAcpAutoAttachRate
    {
        protected string picFolderPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory,
            "userdata\\NbIotStationAcceptance");

        protected static readonly object lockObj = new object();
        private static NBIotAcpAutoCoverPicture intance = null;
        public static NBIotAcpAutoCoverPicture Instance
        {
            get
            {
                if (intance == null)
                {
                    lock (lockObj)
                    {
                        if (intance == null)
                        {
                            intance = new NBIotAcpAutoCoverPicture();
                        }
                    }
                }
                return intance;
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("覆盖");
        }

        public override Dictionary<NBIotKpiKey, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpi kpiCell = anaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                reportInfo(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return new Dictionary<NBIotKpiKey, object>();
            }

            try
            {
                MTGis.DbRect bounds = GetCoverBounds(fileManager, targetCell);
                double nearestDistance;
                TestPoint nearestTp = GetNearestTp(fileManager.TestPoints, targetCell, out nearestDistance);
                string rsrpPathr = FireMapAndTakePic("RSRP", bounds, nearestTp, targetCell);
                string sinrPathr = FireMapAndTakePic("SINR", bounds, nearestTp, targetCell);
                string macULPathr = FireMapAndTakePic("MAC_UL", bounds, nearestTp, targetCell);

                Dictionary<NBIotKpiKey, object> kpiInfos = new Dictionary<NBIotKpiKey, object>();
                kpiInfos.Add(NBIotKpiKey.CoverRsrpPath, rsrpPathr);
                kpiInfos.Add(NBIotKpiKey.CoverSinrPath, sinrPathr);
                kpiInfos.Add(NBIotKpiKey.CoverULPath, macULPathr);
                kpiInfos.Add(NBIotKpiKey.CoverAvgRSRP, kpiCell.AvgRsrp);
                kpiInfos.Add(NBIotKpiKey.CoverAvgSINR, kpiCell.AvgSinr);
                kpiInfos.Add(NBIotKpiKey.CoverAvgSpeed, ((NbIotCellKpiPic)kpiCell).AvgULSpeed);
                kpiInfos.Add(NBIotKpiKey.CoverSpeedUpTen, ((NbIotCellKpiPic)kpiCell).ULSpeedUpTenRate);
                kpiInfos.Add(NBIotKpiKey.CoverCoverRate, ((NbIotCellKpiPic)kpiCell).CoverRate);
                kpiInfos.Add(NBIotKpiKey.AntennaOppositeRate, ((NbIotCellKpiPic)kpiCell).AntennaOppositeRate);
                return kpiInfos;
            }
            catch (Exception ex)
            {
                reportInfo(ex);
            }
            return new Dictionary<NBIotKpiKey, object>();
        }

        protected override NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpiPic targetKpiCell = new NbIotCellKpiPic(targetCell);
            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);
            return targetKpiCell;
        }

        protected override void getTestPointKpi(List<TestPoint> testPoints, LTECell targetCell, NbIotCellKpi targetKpiCell)
        {
            foreach (TestPoint tp in testPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell != null && cell.Token == targetCell.Token)
                {
                    double tpCellAngle = GetTp_CellAngle(tp, targetCell);
                    if (tpCellAngle > 90)
                    {
                        ((NbIotCellKpiPic)targetKpiCell).TpCountAntennaOpposite++;
                    }
                    targetKpiCell.AddPoint(tp);
                }
            }
        }

        /// <summary>
        /// 采样点与小区覆盖方向夹角
        /// </summary>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <returns></returns>
        public double GetTp_CellAngle(TestPoint tp, LTECell srcNBCell)
        {
            double tpLongitude = tp.Longitude;
            double tpLatitude = tp.Latitude;

            double angleDiff = 0;
            double distance = MathFuncs.GetDistance(srcNBCell.Longitude, srcNBCell.Latitude, tpLongitude, tpLatitude);

            ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
            double angle;
            double ygap = MathFuncs.GetDistance(srcNBCell.Longitude, srcNBCell.Latitude, srcNBCell.Longitude, tpLatitude);
            double angleV = Math.Acos(ygap / distance);
            if (tpLongitude >= srcNBCell.Longitude && tpLatitude >= srcNBCell.Latitude)//1象限
            {
                angle = angleV * 180 / Math.PI;
            }
            else if (tpLongitude <= srcNBCell.Longitude && tpLatitude >= srcNBCell.Latitude)//2象限
            {
                angle = 360 - angleV * 180 / Math.PI;
            }
            else if (tpLongitude <= srcNBCell.Longitude && tpLatitude <= srcNBCell.Latitude)//3象限
            {
                angle = 180 + angleV * 180 / Math.PI;
            }
            else//4象限
            {
                angle = 180 - angleV * 180 / Math.PI;
            }

            angleDiff = Math.Abs(angle - srcNBCell.Direction);
            if (angleDiff > 180)
            {
                angleDiff = 360 - angleDiff;
            }
            return angleDiff;
        }

        /// <summary>
        /// 获取小区及所有采样点占用的最大范围
        /// </summary>
        /// <param name="fileManager"></param>
        /// <param name="LTECell"></param>
        /// <returns></returns>
        public MTGis.DbRect GetCoverBounds(DTFileDataManager fileManager, LTECell LTECell)
        {
            double lngMin = LTECell.Longitude;
            double lngMax = LTECell.Longitude;
            double latMin = LTECell.Latitude;
            double latMax = LTECell.Latitude;

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (tp.Longitude > 70 && tp.Latitude > 3
                    && tp.GetMainCell() == LTECell)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }
            }
            MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001
                , lngMax + 0.001, latMax + 0.001);

            return bounds;
        }
        public string FireMapAndTakePic(string paramName, MTGis.DbRect bounds, TestPoint nearestTp
            , LTECell srcLTECell)
        {
            MainModel mModel = MainModel.GetInstance();
            mModel.FireSetDefaultMapSerialTheme("LTE_TDD", paramName);
            mModel.DrawFlyLines = false;

            foreach (MapSerialInfo serialInfo in mModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
            {
                if (serialInfo.Name.Equals(paramName))
                {
                    mModel.MainForm.GetMapForm().GetDTLayer().CurFlyLinesSerialInfo = serialInfo;
                    break;
                }
            }

            mModel.DrawLinesPntToCells = true;
            mModel.PntToCellsDic.Clear();
            List<ZTFunc.LongLat> longlatList = new List<ZTFunc.LongLat>();
            mModel.PntToCellsDic.Add(nearestTp, longlatList);
            longlatList.Add(new ZTFunc.LongLat((float)srcLTECell.EndPointLongitude
                , (float)srcLTECell.EndPointLatitude));

            mModel.FireDTDataChanged(mModel.MainForm);
            mModel.MainForm.GetMapForm().GoToView(bounds);

            return takePicture(srcLTECell.BTSName, srcLTECell.Name, paramName);
        }

        #region 覆盖截图
        private string takePicture(string btsName, string cellName, string paramName)
        {
            string filePath = GetCoverPicPath(btsName, cellName, paramName);
            if (System.IO.Directory.Exists(filePath))//删除本站之前的覆盖截图
            {
                System.IO.Directory.Delete(filePath, true);
            }

            Bitmap bitMap = MainModel.GetInstance().MainForm.GetMapForm().DrawToBitmapDIY();
            bitMap.Save(filePath, ImageFormat.Png);
            bitMap.Dispose();
            return filePath;
        }

        /// <summary>
        /// 获取小区某种覆盖截图的保存路径
        /// </summary>
        /// <param name="btsName"></param>
        /// <param name="cellName"></param>
        /// <param name="postfix"></param>
        /// <returns></returns>
        public string GetCoverPicPath(string btsName, string cellName, string paramName)
        {
            string folderPath = GetBtsPicFolder(btsName);
            if (!System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.CreateDirectory(folderPath);
            }
            return System.IO.Path.Combine(folderPath, cellName + "_" + paramName + ".png");
        }
        /// <summary>
        /// 获取站点覆盖截图保存文件夹地址
        /// </summary>
        /// <param name="btsName"></param>
        /// <returns></returns>
        public string GetBtsPicFolder(string btsName)
        {
            return System.IO.Path.Combine(picFolderPath, btsName.Trim());
        }
        #endregion

        /// <summary>
        /// 最近的采样点
        /// </summary>
        /// <param name="testPoints"></param>
        /// <param name="nbiotCell"></param>
        /// <param name="nearestDistance"></param>
        /// <returns></returns>
        public TestPoint GetNearestTp(List<TestPoint> testPoints, LTECell nbiotCell, out double nearestDistance)
        {
            nearestDistance = double.MaxValue;
            TestPoint nearestTp = null;
            foreach (TestPoint tp in testPoints)
            {
                if (tp["lte_RSRP"] != null && tp["lte_SINR"] != null && tp["lte_MAC_UL"] != null)
                {
                    double curDistance = tp.Distance2(nbiotCell.Longitude, nbiotCell.Latitude);
                    if (curDistance < nearestDistance)
                    {
                        nearestDistance = curDistance;
                        nearestTp = tp;
                    }
                }
            }
            return nearestTp;
        }

        public static void InsertExcelPicture(Excel.Workbook eBook, string startCell, string picPath)
        {
            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[4];
            Excel.Range rng = eSheet.get_Range(startCell, Type.Missing);

            double width = eBook.Application.CentimetersToPoints(13.15);
            double height = eBook.Application.CentimetersToPoints(8.58);
            eSheet.Shapes.AddPicture(picPath,
                Microsoft.Office.Core.MsoTriState.msoFalse,
                Microsoft.Office.Core.MsoTriState.msoCTrue,
                (float)(double)rng.Left, (float)(double)rng.Top,
                (float)width, (float)height);
        }


        public class NbIotCellKpiPic : NbIotCellKpi
        {
            public NbIotCellKpiPic(LTECell cell)
                : base(cell.Name)
            {
                switch (cell.DESC)
                {
                    case "主城区高":
                        rsrpLimit = -84;
                        break;
                    case "主城区低":
                    case "一般城区":
                        rsrpLimit = -87;
                        break;
                    case "县城及乡镇":
                        rsrpLimit = -89;
                        break;
                    default:
                        rsrpLimit = -84;
                        break;
                }
            }
            /// <summary>
            /// 覆盖率判断阈值
            /// </summary>
            protected int rsrpLimit;
            protected double sumULSpeed;
            protected int cntULSpeed;
            protected int cntULSpeedUpTen;

            public double AvgULSpeed
            {
                get { return cntULSpeed == 0 ? double.MinValue : Math.Round(sumULSpeed / cntULSpeed, 2); }
            }

            /// <summary>
            /// 速率大于10k的占比
            /// </summary>
            public double ULSpeedUpTenRate
            {
                get { return cntULSpeed == 0 ? double.MinValue : Math.Round(cntULSpeedUpTen * 100d / cntULSpeed, 2); }
            }

            /// <summary>
            /// 达到覆盖率阈值的采样点数
            /// </summary>
            protected int cntRsrpReachLimit;

            /// <summary>
            /// 覆盖率
            /// </summary>
            public double CoverRate
            {
                get { return cntRsrp == 0 ? double.MinValue : Math.Round(cntRsrpReachLimit * 100d / cntRsrp, 2); }
            }

            /// <summary>
            /// 天线接反的采样点数
            /// </summary>
            public int TpCountAntennaOpposite { get; set; }

            public double AntennaOppositeRate
            {
                get { return PointCount == 0 ? double.MinValue : Math.Round(TpCountAntennaOpposite * 100d / PointCount, 2); }
            }

            public new void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                    //统计达到覆盖率阈值的采样点数
                    if (rsrp >= rsrpLimit)
                    {
                        cntRsrpReachLimit++;
                    }
                }

                int? macUL = (int?)tp["lte_MAC_UL"];
                if (macUL != null)
                {
                    ++cntULSpeed;
                    sumULSpeed += (float)macUL;
                    //速率大于10k
                    if (macUL >= 10240)
                    {
                        cntULSpeedUpTen++;
                    }
                }
            }
        }
    }
    #endregion

    #region 室分站
    #endregion
}
