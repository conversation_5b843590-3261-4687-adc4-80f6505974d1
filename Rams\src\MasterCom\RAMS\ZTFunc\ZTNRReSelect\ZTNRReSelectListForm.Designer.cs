﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTNRReSelectListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTNRReSelectListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewHandOverAna = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGridName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeRsrp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeSinr = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeAppSpeed = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforePdcpSpeed = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeRsrq = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeRssi = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDestCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDestTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDestCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDestCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDestDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterRsrp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterSinr = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterAppSpeed = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterPdcpSpeed = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterRsrq = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterRssi = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSameSite = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeArfcn = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforePCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterArfcn = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewHandOverAna)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 48);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(129, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // ListViewHandOverAna
            // 
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnFileName);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnGridName);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnTime);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnCellName);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnCellTAC);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnCellCI);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnCellID);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeArfcn);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforePCI);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnCellDistance);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeRsrp);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeSinr);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeAppSpeed);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforePdcpSpeed);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeRsrq);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeRssi);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnDestCellName);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnDestTAC);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnDestCI);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnDestCellID);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterArfcn);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterPCI);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnDestDistance);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterRsrp);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterSinr);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterAppSpeed);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterPdcpSpeed);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterRsrq);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterRssi);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnSameSite);
            this.ListViewHandOverAna.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewHandOverAna.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnGridName,
            this.olvColumnTime,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnCellName,
            this.olvColumnCellTAC,
            this.olvColumnCellCI,
            this.olvColumnCellID,
            this.olvColumnBeforeArfcn,
            this.olvColumnBeforePCI,
            this.olvColumnCellDistance,
            this.olvColumnBeforeRsrp,
            this.olvColumnBeforeSinr,
            this.olvColumnBeforeAppSpeed,
            this.olvColumnBeforeRsrq,
            this.olvColumnBeforeRssi,
            this.olvColumnDestCellName,
            this.olvColumnDestTAC,
            this.olvColumnDestCI,
            this.olvColumnDestCellID,
            this.olvColumnAfterArfcn,
            this.olvColumnAfterPCI,
            this.olvColumnDestDistance,
            this.olvColumnAfterRsrp,
            this.olvColumnAfterSinr,
            this.olvColumnAfterAppSpeed,
            this.olvColumnAfterRsrq,
            this.olvColumnAfterRssi,
            this.olvColumnSameSite});
            this.ListViewHandOverAna.ContextMenuStrip = this.ctxMenu;
            this.ListViewHandOverAna.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewHandOverAna.FullRowSelect = true;
            this.ListViewHandOverAna.GridLines = true;
            this.ListViewHandOverAna.HeaderWordWrap = true;
            this.ListViewHandOverAna.IsNeedShowOverlay = false;
            this.ListViewHandOverAna.Location = new System.Drawing.Point(1, 1);
            this.ListViewHandOverAna.Name = "ListViewHandOverAna";
            this.ListViewHandOverAna.OwnerDraw = true;
            this.ListViewHandOverAna.ShowGroups = false;
            this.ListViewHandOverAna.Size = new System.Drawing.Size(1368, 501);
            this.ListViewHandOverAna.TabIndex = 7;
            this.ListViewHandOverAna.UseCompatibleStateImageBehavior = false;
            this.ListViewHandOverAna.View = System.Windows.Forms.View.Details;
            this.ListViewHandOverAna.VirtualMode = true;
            this.ListViewHandOverAna.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewHandOverAna_MouseDoubleClick);
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 40;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 120;
            // 
            // olvColumnGridName
            // 
            this.olvColumnGridName.HeaderFont = null;
            this.olvColumnGridName.Text = "网格名称";
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "时间";
            this.olvColumnTime.Width = 80;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "源小区";
            // 
            // olvColumnCellTAC
            // 
            this.olvColumnCellTAC.HeaderFont = null;
            this.olvColumnCellTAC.Text = "源TAC";
            // 
            // olvColumnCellECI
            // 
            this.olvColumnCellCI.HeaderFont = null;
            this.olvColumnCellCI.Text = "源CI";
            // 
            // olvColumnCellID
            // 
            this.olvColumnCellID.HeaderFont = null;
            this.olvColumnCellID.Text = "源CellID";
            // 
            // olvColumnCellDistance
            // 
            this.olvColumnCellDistance.HeaderFont = null;
            this.olvColumnCellDistance.Text = "源小区与事件距离";
            // 
            // olvColumnBeforeRsrp
            // 
            this.olvColumnBeforeRsrp.HeaderFont = null;
            this.olvColumnBeforeRsrp.Text = "前RSRP";
            // 
            // olvColumnBeforeSinr
            // 
            this.olvColumnBeforeSinr.HeaderFont = null;
            this.olvColumnBeforeSinr.Text = "前SINR";
            // 
            // olvColumnBeforeAppSpeed
            // 
            this.olvColumnBeforeAppSpeed.HeaderFont = null;
            this.olvColumnBeforeAppSpeed.Text = "前APP速率";
            // 
            // olvColumnBeforePdcpSpeed
            // 
            this.olvColumnBeforePdcpSpeed.DisplayIndex = 14;
            this.olvColumnBeforePdcpSpeed.HeaderFont = null;
            this.olvColumnBeforePdcpSpeed.IsVisible = false;
            this.olvColumnBeforePdcpSpeed.Text = "前PDCP速率";
            // 
            // olvColumnBeforeRsrq
            // 
            this.olvColumnBeforeRsrq.HeaderFont = null;
            this.olvColumnBeforeRsrq.Text = "前RSRQ";
            // 
            // olvColumnBeforeRssi
            // 
            this.olvColumnBeforeRssi.HeaderFont = null;
            this.olvColumnBeforeRssi.Text = "前RSSI";
            // 
            // olvColumnDestCellName
            // 
            this.olvColumnDestCellName.HeaderFont = null;
            this.olvColumnDestCellName.Text = "目的小区";
            // 
            // olvColumnDestTAC
            // 
            this.olvColumnDestTAC.HeaderFont = null;
            this.olvColumnDestTAC.Text = "目的TAC";
            // 
            // olvColumnDestECI
            // 
            this.olvColumnDestCI.HeaderFont = null;
            this.olvColumnDestCI.Text = "目的CI";
            // 
            // olvColumnDestCellID
            // 
            this.olvColumnDestCellID.HeaderFont = null;
            this.olvColumnDestCellID.Text = "目的CellID";
            // 
            // olvColumnDestDistance
            // 
            this.olvColumnDestDistance.HeaderFont = null;
            this.olvColumnDestDistance.Text = "目的小区与事件距离";
            // 
            // olvColumnAfterRsrp
            // 
            this.olvColumnAfterRsrp.HeaderFont = null;
            this.olvColumnAfterRsrp.Text = "后RSRP";
            // 
            // olvColumnAfterSinr
            // 
            this.olvColumnAfterSinr.HeaderFont = null;
            this.olvColumnAfterSinr.Text = "后SINR";
            // 
            // olvColumnAfterAppSpeed
            // 
            this.olvColumnAfterAppSpeed.HeaderFont = null;
            this.olvColumnAfterAppSpeed.Text = "后APP速率";
            // 
            // olvColumnAfterPdcpSpeed
            // 
            this.olvColumnAfterPdcpSpeed.DisplayIndex = 23;
            this.olvColumnAfterPdcpSpeed.HeaderFont = null;
            this.olvColumnAfterPdcpSpeed.IsVisible = false;
            this.olvColumnAfterPdcpSpeed.Text = "后PDCP速率";
            // 
            // olvColumnAfterRsrq
            // 
            this.olvColumnAfterRsrq.HeaderFont = null;
            this.olvColumnAfterRsrq.Text = "后RSRQ";
            // 
            // olvColumnAfterRssi
            // 
            this.olvColumnAfterRssi.HeaderFont = null;
            this.olvColumnAfterRssi.Text = "后RSSI";
            // 
            // olvColumnSameSite
            // 
            this.olvColumnSameSite.HeaderFont = null;
            this.olvColumnSameSite.Text = "同站";
            // 
            // olvColumnBeforeArfcn
            // 
            this.olvColumnBeforeArfcn.HeaderFont = null;
            this.olvColumnBeforeArfcn.Text = "源频点";
            // 
            // olvColumnBeforePCI
            // 
            this.olvColumnBeforePCI.HeaderFont = null;
            this.olvColumnBeforePCI.Text = "源PCI";
            // 
            // olvColumnAfterArfcn
            // 
            this.olvColumnAfterArfcn.HeaderFont = null;
            this.olvColumnAfterArfcn.Text = "目的频点";
            // 
            // olvColumnAfterPCI
            // 
            this.olvColumnAfterPCI.HeaderFont = null;
            this.olvColumnAfterPCI.Text = "目的PCI";
            // 
            // ZTNRReSelectListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1370, 502);
            this.Controls.Add(this.ListViewHandOverAna);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTNRReSelectListForm";
            this.Text = "重定向前后指标分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewHandOverAna)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private BrightIdeasSoftware.TreeListView ListViewHandOverAna;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeRsrp;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeSinr;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnDestCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeAppSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnDestCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCI;
        private BrightIdeasSoftware.OLVColumn olvColumnDestTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnDestCI;
        private BrightIdeasSoftware.OLVColumn olvColumnCellDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnDestDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnGridName;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforePdcpSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterRsrp;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterSinr;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterAppSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterPdcpSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnSameSite;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeRsrq;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeRssi;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterRsrq;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterRssi;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeArfcn;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforePCI;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterArfcn;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterPCI;

    }
}