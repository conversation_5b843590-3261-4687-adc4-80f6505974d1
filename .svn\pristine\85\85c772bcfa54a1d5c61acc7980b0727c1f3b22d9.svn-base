using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.Chris.Util
{
    public class RangeSetting : UserControl
    {
        public RangeSetting()
        {
            InitializeComponent();
            comboBoxMinIncluded.SelectedIndex = 1;
            comboBoxMaxIncluded.SelectedIndex = 1;
        }

        public NumericUpDown NumericUpDownMin
        {
            get { return numericUpDownMin; }
        }

        public NumericUpDown NumericUpDownMax
        {
            get { return numericUpDownMax; }
        }

        public RangeSetting(Range rangeAll, int decimalPlaces, Range range)
            : this()
        {
            RangeAll = rangeAll;
            SetDecimalPlaces(decimalPlaces);
            Range = range;
        }

        public void SetDecimalPlaces(int value)
        {
            if (value < 0)
            {
                numericUpDownMin.DecimalPlaces = 0;
                numericUpDownMax.DecimalPlaces = 0;
            }
            else if (value > 10)
            {
                numericUpDownMin.DecimalPlaces = 10;
                numericUpDownMax.DecimalPlaces = 10;
            }
            else
            {
                numericUpDownMin.DecimalPlaces = value;
                numericUpDownMax.DecimalPlaces = value;
            }
        }

        public Range RangeAll
        {
            get
            {
                return null;
            }
            set
            {
                if (value != null)
                {
                    numericUpDownMin.Minimum = convertDouble2Decimal(value.Min);
                    numericUpDownMin.Maximum = convertDouble2Decimal(value.Max);
                    comboBoxMinIncluded.SelectedIndex = value.MinIncluded ? 1 : 0;
                    numericUpDownMin.Value = numericUpDownMin.Minimum;
                    numericUpDownMax.Minimum = convertDouble2Decimal(value.Min);
                    numericUpDownMax.Maximum = convertDouble2Decimal(value.Max);
                    numericUpDownMax.Value = numericUpDownMin.Maximum;
                    comboBoxMaxIncluded.SelectedIndex = value.MaxIncluded ? 1 : 0;
                }
            }
        }

        public Range Range
        {
            get
            {
                if (numericUpDownMin.Enabled)
                {
                    Range range = new Range(convertDecimal2Double(numericUpDownMin.Value), comboBoxMinIncluded.SelectedIndex > 0, convertDecimal2Double(numericUpDownMax.Value), comboBoxMaxIncluded.SelectedIndex > 0);
                    if (range.IsValid())
                    {
                        return range;
                    }
                }
                return null;
            }
            set
            {
                if (value != null)
                {
                    decimal minValue = convertDouble2Decimal(value.Min);
                    if (numericUpDownMin.Minimum <= minValue && minValue <= numericUpDownMin.Maximum)
                    {
                        numericUpDownMin.Value = minValue;
                    }
                    comboBoxMinIncluded.SelectedIndex = value.MinIncluded ? 1 : 0;
                    decimal maxValue = convertDouble2Decimal(value.Max);
                    if (numericUpDownMax.Minimum <= maxValue && maxValue <= numericUpDownMax.Maximum)
                    {
                        numericUpDownMax.Value = maxValue;
                    }
                    comboBoxMaxIncluded.SelectedIndex = value.MaxIncluded ? 1 : 0;
                }
            }
        }

        public void SetReadOnly(bool value)
        {
            numericUpDownMin.Enabled = value;
            comboBoxMinIncluded.Enabled = value;
            numericUpDownMax.Enabled = value;
            comboBoxMaxIncluded.Enabled = value;
        }

        private decimal convertDouble2Decimal(double value)
        {
            if (value <= (double)decimal.MinValue)
            {
                return decimal.MinValue;
            }
            else if (value >= (double)decimal.MaxValue)
            {
                return decimal.MaxValue;
            }

            return (decimal)value;
        }

        private double convertDecimal2Double(decimal value)
        {
            if (value <= decimal.MinValue)
            {
                return double.MinValue;
            }
            else if (value >= decimal.MaxValue)
            {
                return double.MaxValue;
            }

            return (double)value;
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.Label label1;
            this.numericUpDownMax = new System.Windows.Forms.NumericUpDown();
            this.comboBoxMaxIncluded = new System.Windows.Forms.ComboBox();
            this.comboBoxMinIncluded = new System.Windows.Forms.ComboBox();
            this.numericUpDownMin = new System.Windows.Forms.NumericUpDown();
            label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMin)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(116, 7);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(11, 12);
            label1.TabIndex = 2;
            label1.Text = "X";
            // 
            // numericUpDownMax
            // 
            this.numericUpDownMax.Location = new System.Drawing.Point(182, 3);
            this.numericUpDownMax.Name = "numericUpDownMax";
            this.numericUpDownMax.Size = new System.Drawing.Size(58, 21);
            this.numericUpDownMax.TabIndex = 4;
            this.numericUpDownMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // comboBoxMaxIncluded
            // 
            this.comboBoxMaxIncluded.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxMaxIncluded.FormattingEnabled = true;
            this.comboBoxMaxIncluded.Items.AddRange(new object[] {
            "<",
            "<="});
            this.comboBoxMaxIncluded.Location = new System.Drawing.Point(136, 4);
            this.comboBoxMaxIncluded.Name = "comboBoxMaxIncluded";
            this.comboBoxMaxIncluded.Size = new System.Drawing.Size(40, 20);
            this.comboBoxMaxIncluded.TabIndex = 3;
            // 
            // comboBoxMinIncluded
            // 
            this.comboBoxMinIncluded.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxMinIncluded.FormattingEnabled = true;
            this.comboBoxMinIncluded.Items.AddRange(new object[] {
            "<",
            "<="});
            this.comboBoxMinIncluded.Location = new System.Drawing.Point(67, 4);
            this.comboBoxMinIncluded.Name = "comboBoxMinIncluded";
            this.comboBoxMinIncluded.Size = new System.Drawing.Size(40, 20);
            this.comboBoxMinIncluded.TabIndex = 1;
            // 
            // numericUpDownMin
            // 
            this.numericUpDownMin.Location = new System.Drawing.Point(3, 3);
            this.numericUpDownMin.Name = "numericUpDownMin";
            this.numericUpDownMin.Size = new System.Drawing.Size(58, 21);
            this.numericUpDownMin.TabIndex = 0;
            this.numericUpDownMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // RangeSetting
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.Color.Transparent;
            this.Controls.Add(this.numericUpDownMax);
            this.Controls.Add(this.comboBoxMaxIncluded);
            this.Controls.Add(label1);
            this.Controls.Add(this.comboBoxMinIncluded);
            this.Controls.Add(this.numericUpDownMin);
            this.Name = "RangeSetting";
            this.Size = new System.Drawing.Size(243, 28);
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMin)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        public NumericUpDown numericUpDownMin { get; set; }

        private ComboBox comboBoxMinIncluded;

        private ComboBox comboBoxMaxIncluded;

        public NumericUpDown numericUpDownMax { get; set; }
    }
}
