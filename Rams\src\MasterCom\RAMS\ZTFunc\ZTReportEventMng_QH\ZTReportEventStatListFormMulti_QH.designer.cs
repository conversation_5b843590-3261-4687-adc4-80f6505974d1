﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTReportEventStatListFormMulti_QH
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTReportEventStatListFormMulti_QH));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl2 = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn59 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn64 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl2 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl3 = new DevExpress.XtraGrid.GridControl();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn65 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn66 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn67 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn68 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn69 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn70 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn71 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn72 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn73 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn74 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn75 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn76 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn77 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn78 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn79 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn80 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl3 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl4 = new DevExpress.XtraGrid.GridControl();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn81 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn82 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn83 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn84 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn85 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn86 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn87 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn88 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn89 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn90 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn91 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn92 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn93 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn94 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn95 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn96 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl4 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl5 = new DevExpress.XtraGrid.GridControl();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn97 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn98 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn99 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn100 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn101 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn102 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn103 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn104 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn105 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn106 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn107 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn108 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn109 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn110 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn111 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn112 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl5 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl6 = new DevExpress.XtraGrid.GridControl();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn113 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn114 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn115 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn116 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn117 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn118 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn119 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn120 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn121 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn122 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn123 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn124 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn125 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn126 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn127 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn128 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl6 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl7 = new DevExpress.XtraGrid.GridControl();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn135 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn136 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn137 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn138 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn139 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn140 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn141 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn142 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn143 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn144 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn193 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn194 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn195 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn196 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn197 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn198 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl7 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl11 = new DevExpress.XtraGrid.GridControl();
            this.gridView12 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn199 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn200 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn201 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn202 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn203 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn204 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn205 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn206 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn207 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn208 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn209 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn210 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn211 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn212 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn213 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn214 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl11 = new DevExpress.XtraTab.XtraTabControl();
            this.tabTDBlock = new System.Windows.Forms.TabPage();
            this.splitContainerControl12 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlTDBlockReason = new DevExpress.XtraGrid.GridControl();
            this.gridViewTDBlockReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn157 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn158 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn159 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn160 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn215 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn216 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl13 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlChartTDBlockReason = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlTDBlockDetail = new DevExpress.XtraTab.XtraTabControl();
            this.gridViewGSMBlockReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn145 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn146 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn147 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn148 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn149 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn150 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlGSMBlockReason = new DevExpress.XtraGrid.GridControl();
            this.tabTDDrop = new System.Windows.Forms.TabPage();
            this.splitContainerControl10 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlTDDropReason = new DevExpress.XtraGrid.GridControl();
            this.gridViewTDDropReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn151 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn152 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn153 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn154 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn155 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn156 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl11 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlChartTDDropReason = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlTDDropDetail = new DevExpress.XtraTab.XtraTabControl();
            this.tabGSMBlock = new System.Windows.Forms.TabPage();
            this.splitContainerControl5 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitContainerControl9 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlChartGSMBlockReason = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlGSMBlockDetail = new DevExpress.XtraTab.XtraTabControl();
            this.gridView15 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView16 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.tabGSMDrop = new System.Windows.Forms.TabPage();
            this.splitContainerControl4 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlGSMDropReason = new DevExpress.XtraGrid.GridControl();
            this.gridViewGSMDropReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn129 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn131 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn132 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn130 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn133 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn134 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView17 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.splitContainerControl8 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlChartGSMDropReason = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlGSMDropDetail = new DevExpress.XtraTab.XtraTabControl();
            this.tabTDProfile = new System.Windows.Forms.TabPage();
            this.splitContainerControl3 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlTDProfile = new DevExpress.XtraGrid.GridControl();
            this.gridViewTDProfile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView18 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.tabControlChartTDProfile = new DevExpress.XtraTab.XtraTabControl();
            this.tabGSMProfile = new System.Windows.Forms.TabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlGSMProfile = new DevExpress.XtraGrid.GridControl();
            this.gridViewGSMProfile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnAreaName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBeginTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEndTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDropCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDropUnFinishedCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDropFinishedCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDropFinishedPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBlockCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBlockUnFinishedCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBlockFinishedCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBlockFinishedPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEventCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEventUnFinishedCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEventFinishedCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEventFinishedPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView19 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.tabControlChartGSMProfile = new DevExpress.XtraTab.XtraTabControl();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabLTEProfile = new System.Windows.Forms.TabPage();
            this.splitContainerControl20 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlLTEProfile = new DevExpress.XtraGrid.GridControl();
            this.gridViewLTEProfile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn161 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn163 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn164 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn165 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn166 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn167 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn168 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn169 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn170 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn171 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn172 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn173 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn174 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn175 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn176 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView10 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.tabControlChartLTEProfile = new DevExpress.XtraTab.XtraTabControl();
            this.tabLTEScanProfile = new System.Windows.Forms.TabPage();
            this.splitContainerControl21 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlLTEScanProfile = new DevExpress.XtraGrid.GridControl();
            this.gridViewLTEScanProfile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn177 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn179 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn180 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn181 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn182 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn183 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn184 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn185 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn186 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn187 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn188 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn189 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn190 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn191 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn192 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView11 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.tabControlChartLTEScanProfile = new DevExpress.XtraTab.XtraTabControl();
            this.tabLTEDrop = new System.Windows.Forms.TabPage();
            this.splitContainerControl6 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlLTEDropReason = new DevExpress.XtraGrid.GridControl();
            this.gridViewLTEDropReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn217 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn218 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn219 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn220 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn221 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn222 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView20 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.splitContainerControl7 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlChartLTEDropReason = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlLTEDropDetail = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabControl8 = new DevExpress.XtraTab.XtraTabControl();
            this.tabLTEBlock = new System.Windows.Forms.TabPage();
            this.splitContainerControl16 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlLTEBlockReason = new DevExpress.XtraGrid.GridControl();
            this.gridViewLTEBlockReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn229 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn230 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn231 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn232 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn233 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn234 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl17 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlChartLTEBlockReason = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlLTEBlockDetail = new DevExpress.XtraTab.XtraTabControl();
            this.tabLTEScanDrop = new System.Windows.Forms.TabPage();
            this.splitContainerControl14 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlLTEScanDropReason = new DevExpress.XtraGrid.GridControl();
            this.gridViewLTEScanDropReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn223 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn224 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn225 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn226 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn227 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn228 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView22 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.splitContainerControl15 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlChartLTEScanDropReason = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlLTEScanDropDetail = new DevExpress.XtraTab.XtraTabControl();
            this.tabLTEScanBlock = new System.Windows.Forms.TabPage();
            this.splitContainerControl18 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlLTEScanBlockReason = new DevExpress.XtraGrid.GridControl();
            this.gridViewLTEScanBlockReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn235 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn236 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn237 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn238 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn239 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn240 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl19 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlChartLTEScanBlockReason = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlLTEScanBlockDetail = new DevExpress.XtraTab.XtraTabControl();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl11)).BeginInit();
            this.tabTDBlock.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl12)).BeginInit();
            this.splitContainerControl12.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl13)).BeginInit();
            this.splitContainerControl13.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlTDBlockDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSMBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSMBlockReason)).BeginInit();
            this.tabTDDrop.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl10)).BeginInit();
            this.splitContainerControl10.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl11)).BeginInit();
            this.splitContainerControl11.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlTDDropDetail)).BeginInit();
            this.tabGSMBlock.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).BeginInit();
            this.splitContainerControl5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl9)).BeginInit();
            this.splitContainerControl9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartGSMBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlGSMBlockDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView16)).BeginInit();
            this.tabGSMDrop.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).BeginInit();
            this.splitContainerControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSMDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSMDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl8)).BeginInit();
            this.splitContainerControl8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartGSMDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlGSMDropDetail)).BeginInit();
            this.tabTDProfile.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).BeginInit();
            this.splitContainerControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDProfile)).BeginInit();
            this.tabGSMProfile.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSMProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSMProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartGSMProfile)).BeginInit();
            this.tabControl1.SuspendLayout();
            this.tabLTEProfile.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl20)).BeginInit();
            this.splitContainerControl20.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEProfile)).BeginInit();
            this.tabLTEScanProfile.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl21)).BeginInit();
            this.splitContainerControl21.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEScanProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEScanProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEScanProfile)).BeginInit();
            this.tabLTEDrop.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl6)).BeginInit();
            this.splitContainerControl6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl7)).BeginInit();
            this.splitContainerControl7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlLTEDropDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl8)).BeginInit();
            this.tabLTEBlock.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl16)).BeginInit();
            this.splitContainerControl16.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl17)).BeginInit();
            this.splitContainerControl17.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlLTEBlockDetail)).BeginInit();
            this.tabLTEScanDrop.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl14)).BeginInit();
            this.splitContainerControl14.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEScanDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEScanDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl15)).BeginInit();
            this.splitContainerControl15.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEScanDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlLTEScanDropDetail)).BeginInit();
            this.tabLTEScanBlock.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl18)).BeginInit();
            this.splitContainerControl18.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEScanBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEScanBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl19)).BeginInit();
            this.splitContainerControl19.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEScanBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlLTEScanBlockDetail)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl2.Appearance.Options.UseForeColor = true;
            this.splitContainerControl2.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(3, 3);
            this.splitContainerControl2.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl2.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.gridControl1);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.xtraTabControl1);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(870, 352);
            this.splitContainerControl2.SplitterPosition = 266;
            this.splitContainerControl2.TabIndex = 7;
            this.splitContainerControl2.Text = "splitContainerControl1";
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.ctxMenu;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(870, 266);
            this.gridControl1.TabIndex = 5;
            this.gridControl1.UseEmbeddedNavigator = true;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.AllowCellMerge = true;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowDetailButtons = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "区域名称";
            this.gridColumn1.FieldName = "AreaName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 77;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "网格组名称";
            this.gridColumn2.FieldName = "GroupName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 77;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "开始时间";
            this.gridColumn3.FieldName = "BeginTime";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 116;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "结束时间";
            this.gridColumn4.FieldName = "EndTime";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 116;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "掉话总数量";
            this.gridColumn5.FieldName = "DropCount";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            this.gridColumn5.Width = 72;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "掉话未完成数量";
            this.gridColumn6.FieldName = "DropUnFinishedCount";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            this.gridColumn6.Width = 96;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "掉话已完成数量";
            this.gridColumn7.FieldName = "DropFinishedCount";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            this.gridColumn7.Width = 96;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "掉话已完成百分比(%)";
            this.gridColumn8.FieldName = "DropFinishedPercent";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            this.gridColumn8.Width = 130;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "未接通总数量";
            this.gridColumn9.FieldName = "BlockCount";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            this.gridColumn9.Width = 84;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "未接通未完成数量";
            this.gridColumn10.FieldName = "BlockUnFinishedCount";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            this.gridColumn10.Width = 108;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "未接通已完成数量";
            this.gridColumn11.FieldName = "BlockFinishedCount";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 10;
            this.gridColumn11.Width = 47;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "未接通已完成百分比(%)";
            this.gridColumn12.FieldName = "BlockFinishedPercent";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 11;
            this.gridColumn12.Width = 47;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "事件总数量";
            this.gridColumn13.FieldName = "EventCount";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 12;
            this.gridColumn13.Width = 47;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "事件未完成数量";
            this.gridColumn14.FieldName = "EventUnFinishedCount";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 13;
            this.gridColumn14.Width = 47;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "事件已完成数量";
            this.gridColumn15.FieldName = "EventFinishedCount";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 14;
            this.gridColumn15.Width = 48;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "事件已完成百分比(%)";
            this.gridColumn16.FieldName = "EventFinishedPercent";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 15;
            this.gridColumn16.Width = 50;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.Size = new System.Drawing.Size(870, 82);
            this.xtraTabControl1.TabIndex = 6;
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn47,
            this.gridColumn48});
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsView.AllowCellMerge = true;
            this.gridView2.OptionsView.ColumnAutoWidth = false;
            this.gridView2.OptionsView.ShowDetailButtons = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "区域名称";
            this.gridColumn33.FieldName = "AreaName";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 0;
            this.gridColumn33.Width = 77;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "网格组名称";
            this.gridColumn34.FieldName = "GroupName";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 1;
            this.gridColumn34.Width = 77;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "开始时间";
            this.gridColumn35.FieldName = "BeginTime";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 2;
            this.gridColumn35.Width = 116;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "结束时间";
            this.gridColumn36.FieldName = "EndTime";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 3;
            this.gridColumn36.Width = 116;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "掉话总数量";
            this.gridColumn37.FieldName = "DropCount";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 4;
            this.gridColumn37.Width = 72;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "掉话未完成数量";
            this.gridColumn38.FieldName = "DropUnFinishedCount";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 5;
            this.gridColumn38.Width = 96;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "掉话已完成数量";
            this.gridColumn39.FieldName = "DropFinishedCount";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 6;
            this.gridColumn39.Width = 96;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "掉话已完成百分比(%)";
            this.gridColumn40.FieldName = "DropFinishedPercent";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 7;
            this.gridColumn40.Width = 130;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "未接通总数量";
            this.gridColumn41.FieldName = "BlockCount";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 8;
            this.gridColumn41.Width = 84;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "未接通未完成数量";
            this.gridColumn42.FieldName = "BlockUnFinishedCount";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 9;
            this.gridColumn42.Width = 108;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "未接通已完成数量";
            this.gridColumn43.FieldName = "BlockFinishedCount";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 10;
            this.gridColumn43.Width = 47;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "未接通已完成百分比(%)";
            this.gridColumn44.FieldName = "BlockFinishedPercent";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 11;
            this.gridColumn44.Width = 47;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "事件总数量";
            this.gridColumn45.FieldName = "EventCount";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 12;
            this.gridColumn45.Width = 47;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "事件未完成数量";
            this.gridColumn46.FieldName = "EventUnFinishedCount";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 13;
            this.gridColumn46.Width = 47;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "事件已完成数量";
            this.gridColumn47.FieldName = "EventFinishedCount";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 14;
            this.gridColumn47.Width = 48;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "事件已完成百分比(%)";
            this.gridColumn48.FieldName = "EventFinishedPercent";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 15;
            this.gridColumn48.Width = 50;
            // 
            // gridControl2
            // 
            this.gridControl2.ContextMenuStrip = this.ctxMenu;
            this.gridControl2.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl2.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl2.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl2.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl2.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl2.Location = new System.Drawing.Point(158, 22);
            this.gridControl2.MainView = this.gridView3;
            this.gridControl2.Name = "gridControl2";
            this.gridControl2.Size = new System.Drawing.Size(733, 175);
            this.gridControl2.TabIndex = 5;
            this.gridControl2.UseEmbeddedNavigator = true;
            this.gridControl2.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn49,
            this.gridColumn50,
            this.gridColumn51,
            this.gridColumn52,
            this.gridColumn53,
            this.gridColumn54,
            this.gridColumn55,
            this.gridColumn56,
            this.gridColumn57,
            this.gridColumn58,
            this.gridColumn59,
            this.gridColumn60,
            this.gridColumn61,
            this.gridColumn62,
            this.gridColumn63,
            this.gridColumn64});
            this.gridView3.GridControl = this.gridControl2;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsView.AllowCellMerge = true;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.OptionsView.ShowDetailButtons = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "区域名称";
            this.gridColumn49.FieldName = "AreaName";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 0;
            this.gridColumn49.Width = 77;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "网格组名称";
            this.gridColumn50.FieldName = "GroupName";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 1;
            this.gridColumn50.Width = 77;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "开始时间";
            this.gridColumn51.FieldName = "BeginTime";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 2;
            this.gridColumn51.Width = 116;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "结束时间";
            this.gridColumn52.FieldName = "EndTime";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 3;
            this.gridColumn52.Width = 116;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "掉话总数量";
            this.gridColumn53.FieldName = "DropCount";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 4;
            this.gridColumn53.Width = 72;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "掉话未完成数量";
            this.gridColumn54.FieldName = "DropUnFinishedCount";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 5;
            this.gridColumn54.Width = 96;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "掉话已完成数量";
            this.gridColumn55.FieldName = "DropFinishedCount";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 6;
            this.gridColumn55.Width = 96;
            // 
            // gridColumn56
            // 
            this.gridColumn56.Caption = "掉话已完成百分比(%)";
            this.gridColumn56.FieldName = "DropFinishedPercent";
            this.gridColumn56.Name = "gridColumn56";
            this.gridColumn56.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn56.Visible = true;
            this.gridColumn56.VisibleIndex = 7;
            this.gridColumn56.Width = 130;
            // 
            // gridColumn57
            // 
            this.gridColumn57.Caption = "未接通总数量";
            this.gridColumn57.FieldName = "BlockCount";
            this.gridColumn57.Name = "gridColumn57";
            this.gridColumn57.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn57.Visible = true;
            this.gridColumn57.VisibleIndex = 8;
            this.gridColumn57.Width = 84;
            // 
            // gridColumn58
            // 
            this.gridColumn58.Caption = "未接通未完成数量";
            this.gridColumn58.FieldName = "BlockUnFinishedCount";
            this.gridColumn58.Name = "gridColumn58";
            this.gridColumn58.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn58.Visible = true;
            this.gridColumn58.VisibleIndex = 9;
            this.gridColumn58.Width = 108;
            // 
            // gridColumn59
            // 
            this.gridColumn59.Caption = "未接通已完成数量";
            this.gridColumn59.FieldName = "BlockFinishedCount";
            this.gridColumn59.Name = "gridColumn59";
            this.gridColumn59.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn59.Visible = true;
            this.gridColumn59.VisibleIndex = 10;
            this.gridColumn59.Width = 47;
            // 
            // gridColumn60
            // 
            this.gridColumn60.Caption = "未接通已完成百分比(%)";
            this.gridColumn60.FieldName = "BlockFinishedPercent";
            this.gridColumn60.Name = "gridColumn60";
            this.gridColumn60.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn60.Visible = true;
            this.gridColumn60.VisibleIndex = 11;
            this.gridColumn60.Width = 47;
            // 
            // gridColumn61
            // 
            this.gridColumn61.Caption = "事件总数量";
            this.gridColumn61.FieldName = "EventCount";
            this.gridColumn61.Name = "gridColumn61";
            this.gridColumn61.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn61.Visible = true;
            this.gridColumn61.VisibleIndex = 12;
            this.gridColumn61.Width = 47;
            // 
            // gridColumn62
            // 
            this.gridColumn62.Caption = "事件未完成数量";
            this.gridColumn62.FieldName = "EventUnFinishedCount";
            this.gridColumn62.Name = "gridColumn62";
            this.gridColumn62.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn62.Visible = true;
            this.gridColumn62.VisibleIndex = 13;
            this.gridColumn62.Width = 47;
            // 
            // gridColumn63
            // 
            this.gridColumn63.Caption = "事件已完成数量";
            this.gridColumn63.FieldName = "EventFinishedCount";
            this.gridColumn63.Name = "gridColumn63";
            this.gridColumn63.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn63.Visible = true;
            this.gridColumn63.VisibleIndex = 14;
            this.gridColumn63.Width = 48;
            // 
            // gridColumn64
            // 
            this.gridColumn64.Caption = "事件已完成百分比(%)";
            this.gridColumn64.FieldName = "EventFinishedPercent";
            this.gridColumn64.Name = "gridColumn64";
            this.gridColumn64.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn64.Visible = true;
            this.gridColumn64.VisibleIndex = 15;
            this.gridColumn64.Width = 50;
            // 
            // xtraTabControl2
            // 
            this.xtraTabControl2.Location = new System.Drawing.Point(158, 7);
            this.xtraTabControl2.Name = "xtraTabControl2";
            this.xtraTabControl2.Size = new System.Drawing.Size(733, 253);
            this.xtraTabControl2.TabIndex = 6;
            // 
            // gridControl3
            // 
            this.gridControl3.ContextMenuStrip = this.ctxMenu;
            this.gridControl3.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl3.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl3.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl3.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl3.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl3.Location = new System.Drawing.Point(158, 22);
            this.gridControl3.MainView = this.gridView4;
            this.gridControl3.Name = "gridControl3";
            this.gridControl3.Size = new System.Drawing.Size(733, 175);
            this.gridControl3.TabIndex = 5;
            this.gridControl3.UseEmbeddedNavigator = true;
            this.gridControl3.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView4});
            // 
            // gridView4
            // 
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn65,
            this.gridColumn66,
            this.gridColumn67,
            this.gridColumn68,
            this.gridColumn69,
            this.gridColumn70,
            this.gridColumn71,
            this.gridColumn72,
            this.gridColumn73,
            this.gridColumn74,
            this.gridColumn75,
            this.gridColumn76,
            this.gridColumn77,
            this.gridColumn78,
            this.gridColumn79,
            this.gridColumn80});
            this.gridView4.GridControl = this.gridControl3;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsBehavior.Editable = false;
            this.gridView4.OptionsView.AllowCellMerge = true;
            this.gridView4.OptionsView.ColumnAutoWidth = false;
            this.gridView4.OptionsView.ShowDetailButtons = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn65
            // 
            this.gridColumn65.Caption = "区域名称";
            this.gridColumn65.FieldName = "AreaName";
            this.gridColumn65.Name = "gridColumn65";
            this.gridColumn65.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn65.Visible = true;
            this.gridColumn65.VisibleIndex = 0;
            this.gridColumn65.Width = 77;
            // 
            // gridColumn66
            // 
            this.gridColumn66.Caption = "网格组名称";
            this.gridColumn66.FieldName = "GroupName";
            this.gridColumn66.Name = "gridColumn66";
            this.gridColumn66.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn66.Visible = true;
            this.gridColumn66.VisibleIndex = 1;
            this.gridColumn66.Width = 77;
            // 
            // gridColumn67
            // 
            this.gridColumn67.Caption = "开始时间";
            this.gridColumn67.FieldName = "BeginTime";
            this.gridColumn67.Name = "gridColumn67";
            this.gridColumn67.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn67.Visible = true;
            this.gridColumn67.VisibleIndex = 2;
            this.gridColumn67.Width = 116;
            // 
            // gridColumn68
            // 
            this.gridColumn68.Caption = "结束时间";
            this.gridColumn68.FieldName = "EndTime";
            this.gridColumn68.Name = "gridColumn68";
            this.gridColumn68.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn68.Visible = true;
            this.gridColumn68.VisibleIndex = 3;
            this.gridColumn68.Width = 116;
            // 
            // gridColumn69
            // 
            this.gridColumn69.Caption = "掉话总数量";
            this.gridColumn69.FieldName = "DropCount";
            this.gridColumn69.Name = "gridColumn69";
            this.gridColumn69.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn69.Visible = true;
            this.gridColumn69.VisibleIndex = 4;
            this.gridColumn69.Width = 72;
            // 
            // gridColumn70
            // 
            this.gridColumn70.Caption = "掉话未完成数量";
            this.gridColumn70.FieldName = "DropUnFinishedCount";
            this.gridColumn70.Name = "gridColumn70";
            this.gridColumn70.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn70.Visible = true;
            this.gridColumn70.VisibleIndex = 5;
            this.gridColumn70.Width = 96;
            // 
            // gridColumn71
            // 
            this.gridColumn71.Caption = "掉话已完成数量";
            this.gridColumn71.FieldName = "DropFinishedCount";
            this.gridColumn71.Name = "gridColumn71";
            this.gridColumn71.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn71.Visible = true;
            this.gridColumn71.VisibleIndex = 6;
            this.gridColumn71.Width = 96;
            // 
            // gridColumn72
            // 
            this.gridColumn72.Caption = "掉话已完成百分比(%)";
            this.gridColumn72.FieldName = "DropFinishedPercent";
            this.gridColumn72.Name = "gridColumn72";
            this.gridColumn72.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn72.Visible = true;
            this.gridColumn72.VisibleIndex = 7;
            this.gridColumn72.Width = 130;
            // 
            // gridColumn73
            // 
            this.gridColumn73.Caption = "未接通总数量";
            this.gridColumn73.FieldName = "BlockCount";
            this.gridColumn73.Name = "gridColumn73";
            this.gridColumn73.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn73.Visible = true;
            this.gridColumn73.VisibleIndex = 8;
            this.gridColumn73.Width = 84;
            // 
            // gridColumn74
            // 
            this.gridColumn74.Caption = "未接通未完成数量";
            this.gridColumn74.FieldName = "BlockUnFinishedCount";
            this.gridColumn74.Name = "gridColumn74";
            this.gridColumn74.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn74.Visible = true;
            this.gridColumn74.VisibleIndex = 9;
            this.gridColumn74.Width = 108;
            // 
            // gridColumn75
            // 
            this.gridColumn75.Caption = "未接通已完成数量";
            this.gridColumn75.FieldName = "BlockFinishedCount";
            this.gridColumn75.Name = "gridColumn75";
            this.gridColumn75.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn75.Visible = true;
            this.gridColumn75.VisibleIndex = 10;
            this.gridColumn75.Width = 47;
            // 
            // gridColumn76
            // 
            this.gridColumn76.Caption = "未接通已完成百分比(%)";
            this.gridColumn76.FieldName = "BlockFinishedPercent";
            this.gridColumn76.Name = "gridColumn76";
            this.gridColumn76.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn76.Visible = true;
            this.gridColumn76.VisibleIndex = 11;
            this.gridColumn76.Width = 47;
            // 
            // gridColumn77
            // 
            this.gridColumn77.Caption = "事件总数量";
            this.gridColumn77.FieldName = "EventCount";
            this.gridColumn77.Name = "gridColumn77";
            this.gridColumn77.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn77.Visible = true;
            this.gridColumn77.VisibleIndex = 12;
            this.gridColumn77.Width = 47;
            // 
            // gridColumn78
            // 
            this.gridColumn78.Caption = "事件未完成数量";
            this.gridColumn78.FieldName = "EventUnFinishedCount";
            this.gridColumn78.Name = "gridColumn78";
            this.gridColumn78.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn78.Visible = true;
            this.gridColumn78.VisibleIndex = 13;
            this.gridColumn78.Width = 47;
            // 
            // gridColumn79
            // 
            this.gridColumn79.Caption = "事件已完成数量";
            this.gridColumn79.FieldName = "EventFinishedCount";
            this.gridColumn79.Name = "gridColumn79";
            this.gridColumn79.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn79.Visible = true;
            this.gridColumn79.VisibleIndex = 14;
            this.gridColumn79.Width = 48;
            // 
            // gridColumn80
            // 
            this.gridColumn80.Caption = "事件已完成百分比(%)";
            this.gridColumn80.FieldName = "EventFinishedPercent";
            this.gridColumn80.Name = "gridColumn80";
            this.gridColumn80.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn80.Visible = true;
            this.gridColumn80.VisibleIndex = 15;
            this.gridColumn80.Width = 50;
            // 
            // xtraTabControl3
            // 
            this.xtraTabControl3.Location = new System.Drawing.Point(158, 7);
            this.xtraTabControl3.Name = "xtraTabControl3";
            this.xtraTabControl3.Size = new System.Drawing.Size(733, 253);
            this.xtraTabControl3.TabIndex = 6;
            // 
            // gridControl4
            // 
            this.gridControl4.ContextMenuStrip = this.ctxMenu;
            this.gridControl4.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl4.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl4.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl4.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl4.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl4.Location = new System.Drawing.Point(158, 22);
            this.gridControl4.MainView = this.gridView5;
            this.gridControl4.Name = "gridControl4";
            this.gridControl4.Size = new System.Drawing.Size(733, 175);
            this.gridControl4.TabIndex = 5;
            this.gridControl4.UseEmbeddedNavigator = true;
            this.gridControl4.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView5});
            // 
            // gridView5
            // 
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn81,
            this.gridColumn82,
            this.gridColumn83,
            this.gridColumn84,
            this.gridColumn85,
            this.gridColumn86,
            this.gridColumn87,
            this.gridColumn88,
            this.gridColumn89,
            this.gridColumn90,
            this.gridColumn91,
            this.gridColumn92,
            this.gridColumn93,
            this.gridColumn94,
            this.gridColumn95,
            this.gridColumn96});
            this.gridView5.GridControl = this.gridControl4;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsBehavior.Editable = false;
            this.gridView5.OptionsView.AllowCellMerge = true;
            this.gridView5.OptionsView.ColumnAutoWidth = false;
            this.gridView5.OptionsView.ShowDetailButtons = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn81
            // 
            this.gridColumn81.Caption = "区域名称";
            this.gridColumn81.FieldName = "AreaName";
            this.gridColumn81.Name = "gridColumn81";
            this.gridColumn81.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn81.Visible = true;
            this.gridColumn81.VisibleIndex = 0;
            this.gridColumn81.Width = 77;
            // 
            // gridColumn82
            // 
            this.gridColumn82.Caption = "网格组名称";
            this.gridColumn82.FieldName = "GroupName";
            this.gridColumn82.Name = "gridColumn82";
            this.gridColumn82.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn82.Visible = true;
            this.gridColumn82.VisibleIndex = 1;
            this.gridColumn82.Width = 77;
            // 
            // gridColumn83
            // 
            this.gridColumn83.Caption = "开始时间";
            this.gridColumn83.FieldName = "BeginTime";
            this.gridColumn83.Name = "gridColumn83";
            this.gridColumn83.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn83.Visible = true;
            this.gridColumn83.VisibleIndex = 2;
            this.gridColumn83.Width = 116;
            // 
            // gridColumn84
            // 
            this.gridColumn84.Caption = "结束时间";
            this.gridColumn84.FieldName = "EndTime";
            this.gridColumn84.Name = "gridColumn84";
            this.gridColumn84.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn84.Visible = true;
            this.gridColumn84.VisibleIndex = 3;
            this.gridColumn84.Width = 116;
            // 
            // gridColumn85
            // 
            this.gridColumn85.Caption = "掉话总数量";
            this.gridColumn85.FieldName = "DropCount";
            this.gridColumn85.Name = "gridColumn85";
            this.gridColumn85.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn85.Visible = true;
            this.gridColumn85.VisibleIndex = 4;
            this.gridColumn85.Width = 72;
            // 
            // gridColumn86
            // 
            this.gridColumn86.Caption = "掉话未完成数量";
            this.gridColumn86.FieldName = "DropUnFinishedCount";
            this.gridColumn86.Name = "gridColumn86";
            this.gridColumn86.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn86.Visible = true;
            this.gridColumn86.VisibleIndex = 5;
            this.gridColumn86.Width = 96;
            // 
            // gridColumn87
            // 
            this.gridColumn87.Caption = "掉话已完成数量";
            this.gridColumn87.FieldName = "DropFinishedCount";
            this.gridColumn87.Name = "gridColumn87";
            this.gridColumn87.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn87.Visible = true;
            this.gridColumn87.VisibleIndex = 6;
            this.gridColumn87.Width = 96;
            // 
            // gridColumn88
            // 
            this.gridColumn88.Caption = "掉话已完成百分比(%)";
            this.gridColumn88.FieldName = "DropFinishedPercent";
            this.gridColumn88.Name = "gridColumn88";
            this.gridColumn88.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn88.Visible = true;
            this.gridColumn88.VisibleIndex = 7;
            this.gridColumn88.Width = 130;
            // 
            // gridColumn89
            // 
            this.gridColumn89.Caption = "未接通总数量";
            this.gridColumn89.FieldName = "BlockCount";
            this.gridColumn89.Name = "gridColumn89";
            this.gridColumn89.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn89.Visible = true;
            this.gridColumn89.VisibleIndex = 8;
            this.gridColumn89.Width = 84;
            // 
            // gridColumn90
            // 
            this.gridColumn90.Caption = "未接通未完成数量";
            this.gridColumn90.FieldName = "BlockUnFinishedCount";
            this.gridColumn90.Name = "gridColumn90";
            this.gridColumn90.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn90.Visible = true;
            this.gridColumn90.VisibleIndex = 9;
            this.gridColumn90.Width = 108;
            // 
            // gridColumn91
            // 
            this.gridColumn91.Caption = "未接通已完成数量";
            this.gridColumn91.FieldName = "BlockFinishedCount";
            this.gridColumn91.Name = "gridColumn91";
            this.gridColumn91.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn91.Visible = true;
            this.gridColumn91.VisibleIndex = 10;
            this.gridColumn91.Width = 47;
            // 
            // gridColumn92
            // 
            this.gridColumn92.Caption = "未接通已完成百分比(%)";
            this.gridColumn92.FieldName = "BlockFinishedPercent";
            this.gridColumn92.Name = "gridColumn92";
            this.gridColumn92.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn92.Visible = true;
            this.gridColumn92.VisibleIndex = 11;
            this.gridColumn92.Width = 47;
            // 
            // gridColumn93
            // 
            this.gridColumn93.Caption = "事件总数量";
            this.gridColumn93.FieldName = "EventCount";
            this.gridColumn93.Name = "gridColumn93";
            this.gridColumn93.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn93.Visible = true;
            this.gridColumn93.VisibleIndex = 12;
            this.gridColumn93.Width = 47;
            // 
            // gridColumn94
            // 
            this.gridColumn94.Caption = "事件未完成数量";
            this.gridColumn94.FieldName = "EventUnFinishedCount";
            this.gridColumn94.Name = "gridColumn94";
            this.gridColumn94.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn94.Visible = true;
            this.gridColumn94.VisibleIndex = 13;
            this.gridColumn94.Width = 47;
            // 
            // gridColumn95
            // 
            this.gridColumn95.Caption = "事件已完成数量";
            this.gridColumn95.FieldName = "EventFinishedCount";
            this.gridColumn95.Name = "gridColumn95";
            this.gridColumn95.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn95.Visible = true;
            this.gridColumn95.VisibleIndex = 14;
            this.gridColumn95.Width = 48;
            // 
            // gridColumn96
            // 
            this.gridColumn96.Caption = "事件已完成百分比(%)";
            this.gridColumn96.FieldName = "EventFinishedPercent";
            this.gridColumn96.Name = "gridColumn96";
            this.gridColumn96.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn96.Visible = true;
            this.gridColumn96.VisibleIndex = 15;
            this.gridColumn96.Width = 50;
            // 
            // xtraTabControl4
            // 
            this.xtraTabControl4.Location = new System.Drawing.Point(158, 7);
            this.xtraTabControl4.Name = "xtraTabControl4";
            this.xtraTabControl4.Size = new System.Drawing.Size(733, 253);
            this.xtraTabControl4.TabIndex = 6;
            // 
            // gridControl5
            // 
            this.gridControl5.ContextMenuStrip = this.ctxMenu;
            this.gridControl5.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl5.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl5.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl5.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl5.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl5.Location = new System.Drawing.Point(158, 22);
            this.gridControl5.MainView = this.gridView6;
            this.gridControl5.Name = "gridControl5";
            this.gridControl5.Size = new System.Drawing.Size(733, 175);
            this.gridControl5.TabIndex = 5;
            this.gridControl5.UseEmbeddedNavigator = true;
            this.gridControl5.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView6});
            // 
            // gridView6
            // 
            this.gridView6.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn97,
            this.gridColumn98,
            this.gridColumn99,
            this.gridColumn100,
            this.gridColumn101,
            this.gridColumn102,
            this.gridColumn103,
            this.gridColumn104,
            this.gridColumn105,
            this.gridColumn106,
            this.gridColumn107,
            this.gridColumn108,
            this.gridColumn109,
            this.gridColumn110,
            this.gridColumn111,
            this.gridColumn112});
            this.gridView6.GridControl = this.gridControl5;
            this.gridView6.Name = "gridView6";
            this.gridView6.OptionsBehavior.Editable = false;
            this.gridView6.OptionsView.AllowCellMerge = true;
            this.gridView6.OptionsView.ColumnAutoWidth = false;
            this.gridView6.OptionsView.ShowDetailButtons = false;
            this.gridView6.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn97
            // 
            this.gridColumn97.Caption = "区域名称";
            this.gridColumn97.FieldName = "AreaName";
            this.gridColumn97.Name = "gridColumn97";
            this.gridColumn97.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn97.Visible = true;
            this.gridColumn97.VisibleIndex = 0;
            this.gridColumn97.Width = 77;
            // 
            // gridColumn98
            // 
            this.gridColumn98.Caption = "网格组名称";
            this.gridColumn98.FieldName = "GroupName";
            this.gridColumn98.Name = "gridColumn98";
            this.gridColumn98.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn98.Visible = true;
            this.gridColumn98.VisibleIndex = 1;
            this.gridColumn98.Width = 77;
            // 
            // gridColumn99
            // 
            this.gridColumn99.Caption = "开始时间";
            this.gridColumn99.FieldName = "BeginTime";
            this.gridColumn99.Name = "gridColumn99";
            this.gridColumn99.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn99.Visible = true;
            this.gridColumn99.VisibleIndex = 2;
            this.gridColumn99.Width = 116;
            // 
            // gridColumn100
            // 
            this.gridColumn100.Caption = "结束时间";
            this.gridColumn100.FieldName = "EndTime";
            this.gridColumn100.Name = "gridColumn100";
            this.gridColumn100.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn100.Visible = true;
            this.gridColumn100.VisibleIndex = 3;
            this.gridColumn100.Width = 116;
            // 
            // gridColumn101
            // 
            this.gridColumn101.Caption = "掉话总数量";
            this.gridColumn101.FieldName = "DropCount";
            this.gridColumn101.Name = "gridColumn101";
            this.gridColumn101.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn101.Visible = true;
            this.gridColumn101.VisibleIndex = 4;
            this.gridColumn101.Width = 72;
            // 
            // gridColumn102
            // 
            this.gridColumn102.Caption = "掉话未完成数量";
            this.gridColumn102.FieldName = "DropUnFinishedCount";
            this.gridColumn102.Name = "gridColumn102";
            this.gridColumn102.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn102.Visible = true;
            this.gridColumn102.VisibleIndex = 5;
            this.gridColumn102.Width = 96;
            // 
            // gridColumn103
            // 
            this.gridColumn103.Caption = "掉话已完成数量";
            this.gridColumn103.FieldName = "DropFinishedCount";
            this.gridColumn103.Name = "gridColumn103";
            this.gridColumn103.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn103.Visible = true;
            this.gridColumn103.VisibleIndex = 6;
            this.gridColumn103.Width = 96;
            // 
            // gridColumn104
            // 
            this.gridColumn104.Caption = "掉话已完成百分比(%)";
            this.gridColumn104.FieldName = "DropFinishedPercent";
            this.gridColumn104.Name = "gridColumn104";
            this.gridColumn104.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn104.Visible = true;
            this.gridColumn104.VisibleIndex = 7;
            this.gridColumn104.Width = 130;
            // 
            // gridColumn105
            // 
            this.gridColumn105.Caption = "未接通总数量";
            this.gridColumn105.FieldName = "BlockCount";
            this.gridColumn105.Name = "gridColumn105";
            this.gridColumn105.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn105.Visible = true;
            this.gridColumn105.VisibleIndex = 8;
            this.gridColumn105.Width = 84;
            // 
            // gridColumn106
            // 
            this.gridColumn106.Caption = "未接通未完成数量";
            this.gridColumn106.FieldName = "BlockUnFinishedCount";
            this.gridColumn106.Name = "gridColumn106";
            this.gridColumn106.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn106.Visible = true;
            this.gridColumn106.VisibleIndex = 9;
            this.gridColumn106.Width = 108;
            // 
            // gridColumn107
            // 
            this.gridColumn107.Caption = "未接通已完成数量";
            this.gridColumn107.FieldName = "BlockFinishedCount";
            this.gridColumn107.Name = "gridColumn107";
            this.gridColumn107.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn107.Visible = true;
            this.gridColumn107.VisibleIndex = 10;
            this.gridColumn107.Width = 47;
            // 
            // gridColumn108
            // 
            this.gridColumn108.Caption = "未接通已完成百分比(%)";
            this.gridColumn108.FieldName = "BlockFinishedPercent";
            this.gridColumn108.Name = "gridColumn108";
            this.gridColumn108.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn108.Visible = true;
            this.gridColumn108.VisibleIndex = 11;
            this.gridColumn108.Width = 47;
            // 
            // gridColumn109
            // 
            this.gridColumn109.Caption = "事件总数量";
            this.gridColumn109.FieldName = "EventCount";
            this.gridColumn109.Name = "gridColumn109";
            this.gridColumn109.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn109.Visible = true;
            this.gridColumn109.VisibleIndex = 12;
            this.gridColumn109.Width = 47;
            // 
            // gridColumn110
            // 
            this.gridColumn110.Caption = "事件未完成数量";
            this.gridColumn110.FieldName = "EventUnFinishedCount";
            this.gridColumn110.Name = "gridColumn110";
            this.gridColumn110.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn110.Visible = true;
            this.gridColumn110.VisibleIndex = 13;
            this.gridColumn110.Width = 47;
            // 
            // gridColumn111
            // 
            this.gridColumn111.Caption = "事件已完成数量";
            this.gridColumn111.FieldName = "EventFinishedCount";
            this.gridColumn111.Name = "gridColumn111";
            this.gridColumn111.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn111.Visible = true;
            this.gridColumn111.VisibleIndex = 14;
            this.gridColumn111.Width = 48;
            // 
            // gridColumn112
            // 
            this.gridColumn112.Caption = "事件已完成百分比(%)";
            this.gridColumn112.FieldName = "EventFinishedPercent";
            this.gridColumn112.Name = "gridColumn112";
            this.gridColumn112.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn112.Visible = true;
            this.gridColumn112.VisibleIndex = 15;
            this.gridColumn112.Width = 50;
            // 
            // xtraTabControl5
            // 
            this.xtraTabControl5.Location = new System.Drawing.Point(158, 7);
            this.xtraTabControl5.Name = "xtraTabControl5";
            this.xtraTabControl5.Size = new System.Drawing.Size(733, 253);
            this.xtraTabControl5.TabIndex = 6;
            // 
            // gridControl6
            // 
            this.gridControl6.ContextMenuStrip = this.ctxMenu;
            this.gridControl6.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl6.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl6.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl6.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl6.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl6.Location = new System.Drawing.Point(158, 22);
            this.gridControl6.MainView = this.gridView7;
            this.gridControl6.Name = "gridControl6";
            this.gridControl6.Size = new System.Drawing.Size(733, 175);
            this.gridControl6.TabIndex = 5;
            this.gridControl6.UseEmbeddedNavigator = true;
            this.gridControl6.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView7});
            // 
            // gridView7
            // 
            this.gridView7.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn113,
            this.gridColumn114,
            this.gridColumn115,
            this.gridColumn116,
            this.gridColumn117,
            this.gridColumn118,
            this.gridColumn119,
            this.gridColumn120,
            this.gridColumn121,
            this.gridColumn122,
            this.gridColumn123,
            this.gridColumn124,
            this.gridColumn125,
            this.gridColumn126,
            this.gridColumn127,
            this.gridColumn128});
            this.gridView7.GridControl = this.gridControl6;
            this.gridView7.Name = "gridView7";
            this.gridView7.OptionsBehavior.Editable = false;
            this.gridView7.OptionsView.AllowCellMerge = true;
            this.gridView7.OptionsView.ColumnAutoWidth = false;
            this.gridView7.OptionsView.ShowDetailButtons = false;
            this.gridView7.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn113
            // 
            this.gridColumn113.Caption = "区域名称";
            this.gridColumn113.FieldName = "AreaName";
            this.gridColumn113.Name = "gridColumn113";
            this.gridColumn113.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn113.Visible = true;
            this.gridColumn113.VisibleIndex = 0;
            this.gridColumn113.Width = 77;
            // 
            // gridColumn114
            // 
            this.gridColumn114.Caption = "网格组名称";
            this.gridColumn114.FieldName = "GroupName";
            this.gridColumn114.Name = "gridColumn114";
            this.gridColumn114.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn114.Visible = true;
            this.gridColumn114.VisibleIndex = 1;
            this.gridColumn114.Width = 77;
            // 
            // gridColumn115
            // 
            this.gridColumn115.Caption = "开始时间";
            this.gridColumn115.FieldName = "BeginTime";
            this.gridColumn115.Name = "gridColumn115";
            this.gridColumn115.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn115.Visible = true;
            this.gridColumn115.VisibleIndex = 2;
            this.gridColumn115.Width = 116;
            // 
            // gridColumn116
            // 
            this.gridColumn116.Caption = "结束时间";
            this.gridColumn116.FieldName = "EndTime";
            this.gridColumn116.Name = "gridColumn116";
            this.gridColumn116.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn116.Visible = true;
            this.gridColumn116.VisibleIndex = 3;
            this.gridColumn116.Width = 116;
            // 
            // gridColumn117
            // 
            this.gridColumn117.Caption = "掉话总数量";
            this.gridColumn117.FieldName = "DropCount";
            this.gridColumn117.Name = "gridColumn117";
            this.gridColumn117.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn117.Visible = true;
            this.gridColumn117.VisibleIndex = 4;
            this.gridColumn117.Width = 72;
            // 
            // gridColumn118
            // 
            this.gridColumn118.Caption = "掉话未完成数量";
            this.gridColumn118.FieldName = "DropUnFinishedCount";
            this.gridColumn118.Name = "gridColumn118";
            this.gridColumn118.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn118.Visible = true;
            this.gridColumn118.VisibleIndex = 5;
            this.gridColumn118.Width = 96;
            // 
            // gridColumn119
            // 
            this.gridColumn119.Caption = "掉话已完成数量";
            this.gridColumn119.FieldName = "DropFinishedCount";
            this.gridColumn119.Name = "gridColumn119";
            this.gridColumn119.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn119.Visible = true;
            this.gridColumn119.VisibleIndex = 6;
            this.gridColumn119.Width = 96;
            // 
            // gridColumn120
            // 
            this.gridColumn120.Caption = "掉话已完成百分比(%)";
            this.gridColumn120.FieldName = "DropFinishedPercent";
            this.gridColumn120.Name = "gridColumn120";
            this.gridColumn120.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn120.Visible = true;
            this.gridColumn120.VisibleIndex = 7;
            this.gridColumn120.Width = 130;
            // 
            // gridColumn121
            // 
            this.gridColumn121.Caption = "未接通总数量";
            this.gridColumn121.FieldName = "BlockCount";
            this.gridColumn121.Name = "gridColumn121";
            this.gridColumn121.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn121.Visible = true;
            this.gridColumn121.VisibleIndex = 8;
            this.gridColumn121.Width = 84;
            // 
            // gridColumn122
            // 
            this.gridColumn122.Caption = "未接通未完成数量";
            this.gridColumn122.FieldName = "BlockUnFinishedCount";
            this.gridColumn122.Name = "gridColumn122";
            this.gridColumn122.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn122.Visible = true;
            this.gridColumn122.VisibleIndex = 9;
            this.gridColumn122.Width = 108;
            // 
            // gridColumn123
            // 
            this.gridColumn123.Caption = "未接通已完成数量";
            this.gridColumn123.FieldName = "BlockFinishedCount";
            this.gridColumn123.Name = "gridColumn123";
            this.gridColumn123.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn123.Visible = true;
            this.gridColumn123.VisibleIndex = 10;
            this.gridColumn123.Width = 47;
            // 
            // gridColumn124
            // 
            this.gridColumn124.Caption = "未接通已完成百分比(%)";
            this.gridColumn124.FieldName = "BlockFinishedPercent";
            this.gridColumn124.Name = "gridColumn124";
            this.gridColumn124.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn124.Visible = true;
            this.gridColumn124.VisibleIndex = 11;
            this.gridColumn124.Width = 47;
            // 
            // gridColumn125
            // 
            this.gridColumn125.Caption = "事件总数量";
            this.gridColumn125.FieldName = "EventCount";
            this.gridColumn125.Name = "gridColumn125";
            this.gridColumn125.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn125.Visible = true;
            this.gridColumn125.VisibleIndex = 12;
            this.gridColumn125.Width = 47;
            // 
            // gridColumn126
            // 
            this.gridColumn126.Caption = "事件未完成数量";
            this.gridColumn126.FieldName = "EventUnFinishedCount";
            this.gridColumn126.Name = "gridColumn126";
            this.gridColumn126.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn126.Visible = true;
            this.gridColumn126.VisibleIndex = 13;
            this.gridColumn126.Width = 47;
            // 
            // gridColumn127
            // 
            this.gridColumn127.Caption = "事件已完成数量";
            this.gridColumn127.FieldName = "EventFinishedCount";
            this.gridColumn127.Name = "gridColumn127";
            this.gridColumn127.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn127.Visible = true;
            this.gridColumn127.VisibleIndex = 14;
            this.gridColumn127.Width = 48;
            // 
            // gridColumn128
            // 
            this.gridColumn128.Caption = "事件已完成百分比(%)";
            this.gridColumn128.FieldName = "EventFinishedPercent";
            this.gridColumn128.Name = "gridColumn128";
            this.gridColumn128.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn128.Visible = true;
            this.gridColumn128.VisibleIndex = 15;
            this.gridColumn128.Width = 50;
            // 
            // xtraTabControl6
            // 
            this.xtraTabControl6.Location = new System.Drawing.Point(158, 7);
            this.xtraTabControl6.Name = "xtraTabControl6";
            this.xtraTabControl6.Size = new System.Drawing.Size(733, 253);
            this.xtraTabControl6.TabIndex = 6;
            // 
            // gridControl7
            // 
            this.gridControl7.ContextMenuStrip = this.ctxMenu;
            this.gridControl7.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl7.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl7.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl7.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl7.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl7.Location = new System.Drawing.Point(158, 22);
            this.gridControl7.MainView = this.gridView8;
            this.gridControl7.Name = "gridControl7";
            this.gridControl7.Size = new System.Drawing.Size(733, 175);
            this.gridControl7.TabIndex = 5;
            this.gridControl7.UseEmbeddedNavigator = true;
            this.gridControl7.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView8});
            // 
            // gridView8
            // 
            this.gridView8.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn135,
            this.gridColumn136,
            this.gridColumn137,
            this.gridColumn138,
            this.gridColumn139,
            this.gridColumn140,
            this.gridColumn141,
            this.gridColumn142,
            this.gridColumn143,
            this.gridColumn144,
            this.gridColumn193,
            this.gridColumn194,
            this.gridColumn195,
            this.gridColumn196,
            this.gridColumn197,
            this.gridColumn198});
            this.gridView8.GridControl = this.gridControl7;
            this.gridView8.Name = "gridView8";
            this.gridView8.OptionsBehavior.Editable = false;
            this.gridView8.OptionsView.AllowCellMerge = true;
            this.gridView8.OptionsView.ColumnAutoWidth = false;
            this.gridView8.OptionsView.ShowDetailButtons = false;
            this.gridView8.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn135
            // 
            this.gridColumn135.Caption = "区域名称";
            this.gridColumn135.FieldName = "AreaName";
            this.gridColumn135.Name = "gridColumn135";
            this.gridColumn135.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn135.Visible = true;
            this.gridColumn135.VisibleIndex = 0;
            this.gridColumn135.Width = 77;
            // 
            // gridColumn136
            // 
            this.gridColumn136.Caption = "网格组名称";
            this.gridColumn136.FieldName = "GroupName";
            this.gridColumn136.Name = "gridColumn136";
            this.gridColumn136.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn136.Visible = true;
            this.gridColumn136.VisibleIndex = 1;
            this.gridColumn136.Width = 77;
            // 
            // gridColumn137
            // 
            this.gridColumn137.Caption = "开始时间";
            this.gridColumn137.FieldName = "BeginTime";
            this.gridColumn137.Name = "gridColumn137";
            this.gridColumn137.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn137.Visible = true;
            this.gridColumn137.VisibleIndex = 2;
            this.gridColumn137.Width = 116;
            // 
            // gridColumn138
            // 
            this.gridColumn138.Caption = "结束时间";
            this.gridColumn138.FieldName = "EndTime";
            this.gridColumn138.Name = "gridColumn138";
            this.gridColumn138.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn138.Visible = true;
            this.gridColumn138.VisibleIndex = 3;
            this.gridColumn138.Width = 116;
            // 
            // gridColumn139
            // 
            this.gridColumn139.Caption = "掉话总数量";
            this.gridColumn139.FieldName = "DropCount";
            this.gridColumn139.Name = "gridColumn139";
            this.gridColumn139.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn139.Visible = true;
            this.gridColumn139.VisibleIndex = 4;
            this.gridColumn139.Width = 72;
            // 
            // gridColumn140
            // 
            this.gridColumn140.Caption = "掉话未完成数量";
            this.gridColumn140.FieldName = "DropUnFinishedCount";
            this.gridColumn140.Name = "gridColumn140";
            this.gridColumn140.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn140.Visible = true;
            this.gridColumn140.VisibleIndex = 5;
            this.gridColumn140.Width = 96;
            // 
            // gridColumn141
            // 
            this.gridColumn141.Caption = "掉话已完成数量";
            this.gridColumn141.FieldName = "DropFinishedCount";
            this.gridColumn141.Name = "gridColumn141";
            this.gridColumn141.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn141.Visible = true;
            this.gridColumn141.VisibleIndex = 6;
            this.gridColumn141.Width = 96;
            // 
            // gridColumn142
            // 
            this.gridColumn142.Caption = "掉话已完成百分比(%)";
            this.gridColumn142.FieldName = "DropFinishedPercent";
            this.gridColumn142.Name = "gridColumn142";
            this.gridColumn142.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn142.Visible = true;
            this.gridColumn142.VisibleIndex = 7;
            this.gridColumn142.Width = 130;
            // 
            // gridColumn143
            // 
            this.gridColumn143.Caption = "未接通总数量";
            this.gridColumn143.FieldName = "BlockCount";
            this.gridColumn143.Name = "gridColumn143";
            this.gridColumn143.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn143.Visible = true;
            this.gridColumn143.VisibleIndex = 8;
            this.gridColumn143.Width = 84;
            // 
            // gridColumn144
            // 
            this.gridColumn144.Caption = "未接通未完成数量";
            this.gridColumn144.FieldName = "BlockUnFinishedCount";
            this.gridColumn144.Name = "gridColumn144";
            this.gridColumn144.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn144.Visible = true;
            this.gridColumn144.VisibleIndex = 9;
            this.gridColumn144.Width = 108;
            // 
            // gridColumn193
            // 
            this.gridColumn193.Caption = "未接通已完成数量";
            this.gridColumn193.FieldName = "BlockFinishedCount";
            this.gridColumn193.Name = "gridColumn193";
            this.gridColumn193.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn193.Visible = true;
            this.gridColumn193.VisibleIndex = 10;
            this.gridColumn193.Width = 47;
            // 
            // gridColumn194
            // 
            this.gridColumn194.Caption = "未接通已完成百分比(%)";
            this.gridColumn194.FieldName = "BlockFinishedPercent";
            this.gridColumn194.Name = "gridColumn194";
            this.gridColumn194.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn194.Visible = true;
            this.gridColumn194.VisibleIndex = 11;
            this.gridColumn194.Width = 47;
            // 
            // gridColumn195
            // 
            this.gridColumn195.Caption = "事件总数量";
            this.gridColumn195.FieldName = "EventCount";
            this.gridColumn195.Name = "gridColumn195";
            this.gridColumn195.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn195.Visible = true;
            this.gridColumn195.VisibleIndex = 12;
            this.gridColumn195.Width = 47;
            // 
            // gridColumn196
            // 
            this.gridColumn196.Caption = "事件未完成数量";
            this.gridColumn196.FieldName = "EventUnFinishedCount";
            this.gridColumn196.Name = "gridColumn196";
            this.gridColumn196.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn196.Visible = true;
            this.gridColumn196.VisibleIndex = 13;
            this.gridColumn196.Width = 47;
            // 
            // gridColumn197
            // 
            this.gridColumn197.Caption = "事件已完成数量";
            this.gridColumn197.FieldName = "EventFinishedCount";
            this.gridColumn197.Name = "gridColumn197";
            this.gridColumn197.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn197.Visible = true;
            this.gridColumn197.VisibleIndex = 14;
            this.gridColumn197.Width = 48;
            // 
            // gridColumn198
            // 
            this.gridColumn198.Caption = "事件已完成百分比(%)";
            this.gridColumn198.FieldName = "EventFinishedPercent";
            this.gridColumn198.Name = "gridColumn198";
            this.gridColumn198.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn198.Visible = true;
            this.gridColumn198.VisibleIndex = 15;
            this.gridColumn198.Width = 50;
            // 
            // xtraTabControl7
            // 
            this.xtraTabControl7.Location = new System.Drawing.Point(158, 7);
            this.xtraTabControl7.Name = "xtraTabControl7";
            this.xtraTabControl7.Size = new System.Drawing.Size(733, 253);
            this.xtraTabControl7.TabIndex = 6;
            // 
            // gridControl11
            // 
            this.gridControl11.ContextMenuStrip = this.ctxMenu;
            this.gridControl11.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl11.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl11.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl11.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl11.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl11.Location = new System.Drawing.Point(158, 22);
            this.gridControl11.MainView = this.gridView12;
            this.gridControl11.Name = "gridControl11";
            this.gridControl11.Size = new System.Drawing.Size(733, 175);
            this.gridControl11.TabIndex = 5;
            this.gridControl11.UseEmbeddedNavigator = true;
            this.gridControl11.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView12});
            // 
            // gridView12
            // 
            this.gridView12.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn199,
            this.gridColumn200,
            this.gridColumn201,
            this.gridColumn202,
            this.gridColumn203,
            this.gridColumn204,
            this.gridColumn205,
            this.gridColumn206,
            this.gridColumn207,
            this.gridColumn208,
            this.gridColumn209,
            this.gridColumn210,
            this.gridColumn211,
            this.gridColumn212,
            this.gridColumn213,
            this.gridColumn214});
            this.gridView12.GridControl = this.gridControl11;
            this.gridView12.Name = "gridView12";
            this.gridView12.OptionsBehavior.Editable = false;
            this.gridView12.OptionsView.AllowCellMerge = true;
            this.gridView12.OptionsView.ColumnAutoWidth = false;
            this.gridView12.OptionsView.ShowDetailButtons = false;
            this.gridView12.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn199
            // 
            this.gridColumn199.Caption = "区域名称";
            this.gridColumn199.FieldName = "AreaName";
            this.gridColumn199.Name = "gridColumn199";
            this.gridColumn199.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn199.Visible = true;
            this.gridColumn199.VisibleIndex = 0;
            this.gridColumn199.Width = 77;
            // 
            // gridColumn200
            // 
            this.gridColumn200.Caption = "网格组名称";
            this.gridColumn200.FieldName = "GroupName";
            this.gridColumn200.Name = "gridColumn200";
            this.gridColumn200.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn200.Visible = true;
            this.gridColumn200.VisibleIndex = 1;
            this.gridColumn200.Width = 77;
            // 
            // gridColumn201
            // 
            this.gridColumn201.Caption = "开始时间";
            this.gridColumn201.FieldName = "BeginTime";
            this.gridColumn201.Name = "gridColumn201";
            this.gridColumn201.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn201.Visible = true;
            this.gridColumn201.VisibleIndex = 2;
            this.gridColumn201.Width = 116;
            // 
            // gridColumn202
            // 
            this.gridColumn202.Caption = "结束时间";
            this.gridColumn202.FieldName = "EndTime";
            this.gridColumn202.Name = "gridColumn202";
            this.gridColumn202.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn202.Visible = true;
            this.gridColumn202.VisibleIndex = 3;
            this.gridColumn202.Width = 116;
            // 
            // gridColumn203
            // 
            this.gridColumn203.Caption = "掉话总数量";
            this.gridColumn203.FieldName = "DropCount";
            this.gridColumn203.Name = "gridColumn203";
            this.gridColumn203.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn203.Visible = true;
            this.gridColumn203.VisibleIndex = 4;
            this.gridColumn203.Width = 72;
            // 
            // gridColumn204
            // 
            this.gridColumn204.Caption = "掉话未完成数量";
            this.gridColumn204.FieldName = "DropUnFinishedCount";
            this.gridColumn204.Name = "gridColumn204";
            this.gridColumn204.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn204.Visible = true;
            this.gridColumn204.VisibleIndex = 5;
            this.gridColumn204.Width = 96;
            // 
            // gridColumn205
            // 
            this.gridColumn205.Caption = "掉话已完成数量";
            this.gridColumn205.FieldName = "DropFinishedCount";
            this.gridColumn205.Name = "gridColumn205";
            this.gridColumn205.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn205.Visible = true;
            this.gridColumn205.VisibleIndex = 6;
            this.gridColumn205.Width = 96;
            // 
            // gridColumn206
            // 
            this.gridColumn206.Caption = "掉话已完成百分比(%)";
            this.gridColumn206.FieldName = "DropFinishedPercent";
            this.gridColumn206.Name = "gridColumn206";
            this.gridColumn206.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn206.Visible = true;
            this.gridColumn206.VisibleIndex = 7;
            this.gridColumn206.Width = 130;
            // 
            // gridColumn207
            // 
            this.gridColumn207.Caption = "未接通总数量";
            this.gridColumn207.FieldName = "BlockCount";
            this.gridColumn207.Name = "gridColumn207";
            this.gridColumn207.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn207.Visible = true;
            this.gridColumn207.VisibleIndex = 8;
            this.gridColumn207.Width = 84;
            // 
            // gridColumn208
            // 
            this.gridColumn208.Caption = "未接通未完成数量";
            this.gridColumn208.FieldName = "BlockUnFinishedCount";
            this.gridColumn208.Name = "gridColumn208";
            this.gridColumn208.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn208.Visible = true;
            this.gridColumn208.VisibleIndex = 9;
            this.gridColumn208.Width = 108;
            // 
            // gridColumn209
            // 
            this.gridColumn209.Caption = "未接通已完成数量";
            this.gridColumn209.FieldName = "BlockFinishedCount";
            this.gridColumn209.Name = "gridColumn209";
            this.gridColumn209.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn209.Visible = true;
            this.gridColumn209.VisibleIndex = 10;
            this.gridColumn209.Width = 47;
            // 
            // gridColumn210
            // 
            this.gridColumn210.Caption = "未接通已完成百分比(%)";
            this.gridColumn210.FieldName = "BlockFinishedPercent";
            this.gridColumn210.Name = "gridColumn210";
            this.gridColumn210.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn210.Visible = true;
            this.gridColumn210.VisibleIndex = 11;
            this.gridColumn210.Width = 47;
            // 
            // gridColumn211
            // 
            this.gridColumn211.Caption = "事件总数量";
            this.gridColumn211.FieldName = "EventCount";
            this.gridColumn211.Name = "gridColumn211";
            this.gridColumn211.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn211.Visible = true;
            this.gridColumn211.VisibleIndex = 12;
            this.gridColumn211.Width = 47;
            // 
            // gridColumn212
            // 
            this.gridColumn212.Caption = "事件未完成数量";
            this.gridColumn212.FieldName = "EventUnFinishedCount";
            this.gridColumn212.Name = "gridColumn212";
            this.gridColumn212.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn212.Visible = true;
            this.gridColumn212.VisibleIndex = 13;
            this.gridColumn212.Width = 47;
            // 
            // gridColumn213
            // 
            this.gridColumn213.Caption = "事件已完成数量";
            this.gridColumn213.FieldName = "EventFinishedCount";
            this.gridColumn213.Name = "gridColumn213";
            this.gridColumn213.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn213.Visible = true;
            this.gridColumn213.VisibleIndex = 14;
            this.gridColumn213.Width = 48;
            // 
            // gridColumn214
            // 
            this.gridColumn214.Caption = "事件已完成百分比(%)";
            this.gridColumn214.FieldName = "EventFinishedPercent";
            this.gridColumn214.Name = "gridColumn214";
            this.gridColumn214.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn214.Visible = true;
            this.gridColumn214.VisibleIndex = 15;
            this.gridColumn214.Width = 50;
            // 
            // xtraTabControl11
            // 
            this.xtraTabControl11.Location = new System.Drawing.Point(158, 7);
            this.xtraTabControl11.Name = "xtraTabControl11";
            this.xtraTabControl11.Size = new System.Drawing.Size(733, 253);
            this.xtraTabControl11.TabIndex = 6;
            // 
            // tabTDBlock
            // 
            this.tabTDBlock.Controls.Add(this.splitContainerControl12);
            this.tabTDBlock.Location = new System.Drawing.Point(4, 23);
            this.tabTDBlock.Name = "tabTDBlock";
            this.tabTDBlock.Size = new System.Drawing.Size(1025, 542);
            this.tabTDBlock.TabIndex = 8;
            this.tabTDBlock.Text = "TD未接通原因分析";
            this.tabTDBlock.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl12
            // 
            this.splitContainerControl12.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl12.Appearance.Options.UseForeColor = true;
            this.splitContainerControl12.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl12.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl12.Horizontal = false;
            this.splitContainerControl12.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl12.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl12.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl12.Name = "splitContainerControl12";
            this.splitContainerControl12.Panel1.Controls.Add(this.gridControlTDBlockReason);
            this.splitContainerControl12.Panel1.Text = "Panel1";
            this.splitContainerControl12.Panel2.Controls.Add(this.splitContainerControl13);
            this.splitContainerControl12.Panel2.Text = "Panel2";
            this.splitContainerControl12.Size = new System.Drawing.Size(1025, 542);
            this.splitContainerControl12.SplitterPosition = 246;
            this.splitContainerControl12.TabIndex = 8;
            this.splitContainerControl12.Text = "splitContainerControl12";
            // 
            // gridControlTDBlockReason
            // 
            this.gridControlTDBlockReason.ContextMenuStrip = this.ctxMenu;
            this.gridControlTDBlockReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlTDBlockReason.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlTDBlockReason.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlTDBlockReason.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlTDBlockReason.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlTDBlockReason.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlTDBlockReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlTDBlockReason.MainView = this.gridViewTDBlockReason;
            this.gridControlTDBlockReason.Name = "gridControlTDBlockReason";
            this.gridControlTDBlockReason.Size = new System.Drawing.Size(1025, 246);
            this.gridControlTDBlockReason.TabIndex = 5;
            this.gridControlTDBlockReason.UseEmbeddedNavigator = true;
            this.gridControlTDBlockReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewTDBlockReason});
            // 
            // gridViewTDBlockReason
            // 
            this.gridViewTDBlockReason.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn157,
            this.gridColumn158,
            this.gridColumn159,
            this.gridColumn160,
            this.gridColumn215,
            this.gridColumn216});
            this.gridViewTDBlockReason.GridControl = this.gridControlTDBlockReason;
            this.gridViewTDBlockReason.Name = "gridViewTDBlockReason";
            this.gridViewTDBlockReason.OptionsBehavior.Editable = false;
            this.gridViewTDBlockReason.OptionsView.AllowCellMerge = true;
            this.gridViewTDBlockReason.OptionsView.ShowDetailButtons = false;
            this.gridViewTDBlockReason.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn157
            // 
            this.gridColumn157.Caption = "区域名称";
            this.gridColumn157.FieldName = "AreaName";
            this.gridColumn157.Name = "gridColumn157";
            this.gridColumn157.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn157.Visible = true;
            this.gridColumn157.VisibleIndex = 0;
            this.gridColumn157.Width = 140;
            // 
            // gridColumn158
            // 
            this.gridColumn158.Caption = "开始时间";
            this.gridColumn158.FieldName = "BeginTime";
            this.gridColumn158.Name = "gridColumn158";
            this.gridColumn158.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn158.Visible = true;
            this.gridColumn158.VisibleIndex = 1;
            this.gridColumn158.Width = 160;
            // 
            // gridColumn159
            // 
            this.gridColumn159.Caption = "结束时间";
            this.gridColumn159.FieldName = "EndTime";
            this.gridColumn159.Name = "gridColumn159";
            this.gridColumn159.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn159.Visible = true;
            this.gridColumn159.VisibleIndex = 2;
            this.gridColumn159.Width = 160;
            // 
            // gridColumn160
            // 
            this.gridColumn160.Caption = "原因";
            this.gridColumn160.FieldName = "ReasonName";
            this.gridColumn160.Name = "gridColumn160";
            this.gridColumn160.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn160.Visible = true;
            this.gridColumn160.VisibleIndex = 3;
            this.gridColumn160.Width = 160;
            // 
            // gridColumn215
            // 
            this.gridColumn215.Caption = "事件数量";
            this.gridColumn215.FieldName = "ReasonCount";
            this.gridColumn215.Name = "gridColumn215";
            this.gridColumn215.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn215.Visible = true;
            this.gridColumn215.VisibleIndex = 4;
            this.gridColumn215.Width = 160;
            // 
            // gridColumn216
            // 
            this.gridColumn216.Caption = "原因占比（%）";
            this.gridColumn216.FieldName = "ReasonPercent";
            this.gridColumn216.Name = "gridColumn216";
            this.gridColumn216.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn216.Visible = true;
            this.gridColumn216.VisibleIndex = 5;
            this.gridColumn216.Width = 160;
            // 
            // splitContainerControl13
            // 
            this.splitContainerControl13.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl13.Appearance.Options.UseForeColor = true;
            this.splitContainerControl13.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl13.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl13.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl13.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl13.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl13.Name = "splitContainerControl13";
            this.splitContainerControl13.Panel1.Controls.Add(this.tabControlChartTDBlockReason);
            this.splitContainerControl13.Panel1.Text = "Panel1";
            this.splitContainerControl13.Panel2.Controls.Add(this.tabControlTDBlockDetail);
            this.splitContainerControl13.Panel2.Text = "Panel2";
            this.splitContainerControl13.Size = new System.Drawing.Size(1025, 292);
            this.splitContainerControl13.SplitterPosition = 522;
            this.splitContainerControl13.TabIndex = 9;
            this.splitContainerControl13.Text = "splitContainerControl13";
            // 
            // tabControlChartTDBlockReason
            // 
            this.tabControlChartTDBlockReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartTDBlockReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartTDBlockReason.Name = "tabControlChartTDBlockReason";
            this.tabControlChartTDBlockReason.Size = new System.Drawing.Size(522, 292);
            this.tabControlChartTDBlockReason.TabIndex = 6;
            // 
            // tabControlTDBlockDetail
            // 
            this.tabControlTDBlockDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlTDBlockDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlTDBlockDetail.Name = "tabControlTDBlockDetail";
            this.tabControlTDBlockDetail.Size = new System.Drawing.Size(499, 292);
            this.tabControlTDBlockDetail.TabIndex = 7;
            // 
            // gridViewGSMBlockReason
            // 
            this.gridViewGSMBlockReason.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn145,
            this.gridColumn146,
            this.gridColumn147,
            this.gridColumn148,
            this.gridColumn149,
            this.gridColumn150});
            this.gridViewGSMBlockReason.GridControl = this.gridControlGSMBlockReason;
            this.gridViewGSMBlockReason.Name = "gridViewGSMBlockReason";
            this.gridViewGSMBlockReason.OptionsBehavior.Editable = false;
            this.gridViewGSMBlockReason.OptionsView.AllowCellMerge = true;
            this.gridViewGSMBlockReason.OptionsView.ShowDetailButtons = false;
            this.gridViewGSMBlockReason.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn145
            // 
            this.gridColumn145.Caption = "区域名称";
            this.gridColumn145.FieldName = "AreaName";
            this.gridColumn145.Name = "gridColumn145";
            this.gridColumn145.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn145.Visible = true;
            this.gridColumn145.VisibleIndex = 0;
            this.gridColumn145.Width = 140;
            // 
            // gridColumn146
            // 
            this.gridColumn146.Caption = "开始时间";
            this.gridColumn146.FieldName = "BeginTime";
            this.gridColumn146.Name = "gridColumn146";
            this.gridColumn146.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn146.Visible = true;
            this.gridColumn146.VisibleIndex = 1;
            this.gridColumn146.Width = 160;
            // 
            // gridColumn147
            // 
            this.gridColumn147.Caption = "结束时间";
            this.gridColumn147.FieldName = "EndTime";
            this.gridColumn147.Name = "gridColumn147";
            this.gridColumn147.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn147.Visible = true;
            this.gridColumn147.VisibleIndex = 2;
            this.gridColumn147.Width = 160;
            // 
            // gridColumn148
            // 
            this.gridColumn148.Caption = "原因";
            this.gridColumn148.FieldName = "ReasonName";
            this.gridColumn148.Name = "gridColumn148";
            this.gridColumn148.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn148.Visible = true;
            this.gridColumn148.VisibleIndex = 3;
            this.gridColumn148.Width = 160;
            // 
            // gridColumn149
            // 
            this.gridColumn149.Caption = "事件数量";
            this.gridColumn149.FieldName = "ReasonCount";
            this.gridColumn149.Name = "gridColumn149";
            this.gridColumn149.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn149.Visible = true;
            this.gridColumn149.VisibleIndex = 4;
            this.gridColumn149.Width = 160;
            // 
            // gridColumn150
            // 
            this.gridColumn150.Caption = "原因占比（%）";
            this.gridColumn150.FieldName = "ReasonPercent";
            this.gridColumn150.Name = "gridColumn150";
            this.gridColumn150.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn150.Visible = true;
            this.gridColumn150.VisibleIndex = 5;
            this.gridColumn150.Width = 160;
            // 
            // gridControlGSMBlockReason
            // 
            this.gridControlGSMBlockReason.ContextMenuStrip = this.ctxMenu;
            this.gridControlGSMBlockReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlGSMBlockReason.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlGSMBlockReason.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlGSMBlockReason.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlGSMBlockReason.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlGSMBlockReason.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlGSMBlockReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlGSMBlockReason.MainView = this.gridViewGSMBlockReason;
            this.gridControlGSMBlockReason.Name = "gridControlGSMBlockReason";
            this.gridControlGSMBlockReason.Size = new System.Drawing.Size(1025, 246);
            this.gridControlGSMBlockReason.TabIndex = 5;
            this.gridControlGSMBlockReason.UseEmbeddedNavigator = true;
            this.gridControlGSMBlockReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewGSMBlockReason});
            // 
            // tabTDDrop
            // 
            this.tabTDDrop.Controls.Add(this.splitContainerControl10);
            this.tabTDDrop.Location = new System.Drawing.Point(4, 23);
            this.tabTDDrop.Name = "tabTDDrop";
            this.tabTDDrop.Size = new System.Drawing.Size(1025, 542);
            this.tabTDDrop.TabIndex = 7;
            this.tabTDDrop.Text = "TD掉话原因分析";
            this.tabTDDrop.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl10
            // 
            this.splitContainerControl10.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl10.Appearance.Options.UseForeColor = true;
            this.splitContainerControl10.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl10.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl10.Horizontal = false;
            this.splitContainerControl10.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl10.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl10.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl10.Name = "splitContainerControl10";
            this.splitContainerControl10.Panel1.Controls.Add(this.gridControlTDDropReason);
            this.splitContainerControl10.Panel1.Text = "Panel1";
            this.splitContainerControl10.Panel2.Controls.Add(this.splitContainerControl11);
            this.splitContainerControl10.Panel2.Text = "Panel2";
            this.splitContainerControl10.Size = new System.Drawing.Size(1025, 542);
            this.splitContainerControl10.SplitterPosition = 246;
            this.splitContainerControl10.TabIndex = 8;
            this.splitContainerControl10.Text = "splitContainerControl10";
            // 
            // gridControlTDDropReason
            // 
            this.gridControlTDDropReason.ContextMenuStrip = this.ctxMenu;
            this.gridControlTDDropReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlTDDropReason.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlTDDropReason.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlTDDropReason.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlTDDropReason.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlTDDropReason.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlTDDropReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlTDDropReason.MainView = this.gridViewTDDropReason;
            this.gridControlTDDropReason.Name = "gridControlTDDropReason";
            this.gridControlTDDropReason.Size = new System.Drawing.Size(1025, 246);
            this.gridControlTDDropReason.TabIndex = 5;
            this.gridControlTDDropReason.UseEmbeddedNavigator = true;
            this.gridControlTDDropReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewTDDropReason});
            // 
            // gridViewTDDropReason
            // 
            this.gridViewTDDropReason.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn151,
            this.gridColumn152,
            this.gridColumn153,
            this.gridColumn154,
            this.gridColumn155,
            this.gridColumn156});
            this.gridViewTDDropReason.GridControl = this.gridControlTDDropReason;
            this.gridViewTDDropReason.Name = "gridViewTDDropReason";
            this.gridViewTDDropReason.OptionsBehavior.Editable = false;
            this.gridViewTDDropReason.OptionsView.AllowCellMerge = true;
            this.gridViewTDDropReason.OptionsView.ShowDetailButtons = false;
            this.gridViewTDDropReason.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn151
            // 
            this.gridColumn151.Caption = "区域名称";
            this.gridColumn151.FieldName = "AreaName";
            this.gridColumn151.Name = "gridColumn151";
            this.gridColumn151.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn151.Visible = true;
            this.gridColumn151.VisibleIndex = 0;
            this.gridColumn151.Width = 140;
            // 
            // gridColumn152
            // 
            this.gridColumn152.Caption = "开始时间";
            this.gridColumn152.FieldName = "BeginTime";
            this.gridColumn152.Name = "gridColumn152";
            this.gridColumn152.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn152.Visible = true;
            this.gridColumn152.VisibleIndex = 1;
            this.gridColumn152.Width = 160;
            // 
            // gridColumn153
            // 
            this.gridColumn153.Caption = "结束时间";
            this.gridColumn153.FieldName = "EndTime";
            this.gridColumn153.Name = "gridColumn153";
            this.gridColumn153.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn153.Visible = true;
            this.gridColumn153.VisibleIndex = 2;
            this.gridColumn153.Width = 160;
            // 
            // gridColumn154
            // 
            this.gridColumn154.Caption = "原因";
            this.gridColumn154.FieldName = "ReasonName";
            this.gridColumn154.Name = "gridColumn154";
            this.gridColumn154.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn154.Visible = true;
            this.gridColumn154.VisibleIndex = 3;
            this.gridColumn154.Width = 160;
            // 
            // gridColumn155
            // 
            this.gridColumn155.Caption = "事件数量";
            this.gridColumn155.FieldName = "ReasonCount";
            this.gridColumn155.Name = "gridColumn155";
            this.gridColumn155.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn155.Visible = true;
            this.gridColumn155.VisibleIndex = 4;
            this.gridColumn155.Width = 160;
            // 
            // gridColumn156
            // 
            this.gridColumn156.Caption = "原因占比（%）";
            this.gridColumn156.FieldName = "ReasonPercent";
            this.gridColumn156.Name = "gridColumn156";
            this.gridColumn156.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn156.Visible = true;
            this.gridColumn156.VisibleIndex = 5;
            this.gridColumn156.Width = 160;
            // 
            // splitContainerControl11
            // 
            this.splitContainerControl11.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl11.Appearance.Options.UseForeColor = true;
            this.splitContainerControl11.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl11.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl11.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl11.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl11.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl11.Name = "splitContainerControl11";
            this.splitContainerControl11.Panel1.Controls.Add(this.tabControlChartTDDropReason);
            this.splitContainerControl11.Panel1.Text = "Panel1";
            this.splitContainerControl11.Panel2.Controls.Add(this.tabControlTDDropDetail);
            this.splitContainerControl11.Panel2.Text = "Panel2";
            this.splitContainerControl11.Size = new System.Drawing.Size(1025, 292);
            this.splitContainerControl11.SplitterPosition = 522;
            this.splitContainerControl11.TabIndex = 9;
            this.splitContainerControl11.Text = "splitContainerControl11";
            // 
            // tabControlChartTDDropReason
            // 
            this.tabControlChartTDDropReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartTDDropReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartTDDropReason.Name = "tabControlChartTDDropReason";
            this.tabControlChartTDDropReason.Size = new System.Drawing.Size(522, 292);
            this.tabControlChartTDDropReason.TabIndex = 6;
            // 
            // tabControlTDDropDetail
            // 
            this.tabControlTDDropDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlTDDropDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlTDDropDetail.Name = "tabControlTDDropDetail";
            this.tabControlTDDropDetail.Size = new System.Drawing.Size(499, 292);
            this.tabControlTDDropDetail.TabIndex = 7;
            // 
            // tabGSMBlock
            // 
            this.tabGSMBlock.Controls.Add(this.splitContainerControl5);
            this.tabGSMBlock.Location = new System.Drawing.Point(4, 23);
            this.tabGSMBlock.Name = "tabGSMBlock";
            this.tabGSMBlock.Size = new System.Drawing.Size(1025, 542);
            this.tabGSMBlock.TabIndex = 6;
            this.tabGSMBlock.Text = "GSM未接通原因分析";
            this.tabGSMBlock.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl5
            // 
            this.splitContainerControl5.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl5.Appearance.Options.UseForeColor = true;
            this.splitContainerControl5.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl5.Horizontal = false;
            this.splitContainerControl5.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl5.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl5.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl5.Name = "splitContainerControl5";
            this.splitContainerControl5.Panel1.Controls.Add(this.gridControlGSMBlockReason);
            this.splitContainerControl5.Panel1.Text = "Panel1";
            this.splitContainerControl5.Panel2.Controls.Add(this.splitContainerControl9);
            this.splitContainerControl5.Panel2.Text = "Panel2";
            this.splitContainerControl5.Size = new System.Drawing.Size(1025, 542);
            this.splitContainerControl5.SplitterPosition = 246;
            this.splitContainerControl5.TabIndex = 8;
            this.splitContainerControl5.Text = "splitContainerControl5";
            // 
            // splitContainerControl9
            // 
            this.splitContainerControl9.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl9.Appearance.Options.UseForeColor = true;
            this.splitContainerControl9.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl9.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl9.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl9.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl9.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl9.Name = "splitContainerControl9";
            this.splitContainerControl9.Panel1.Controls.Add(this.tabControlChartGSMBlockReason);
            this.splitContainerControl9.Panel1.Text = "Panel1";
            this.splitContainerControl9.Panel2.Controls.Add(this.tabControlGSMBlockDetail);
            this.splitContainerControl9.Panel2.Text = "Panel2";
            this.splitContainerControl9.Size = new System.Drawing.Size(1025, 292);
            this.splitContainerControl9.SplitterPosition = 522;
            this.splitContainerControl9.TabIndex = 9;
            this.splitContainerControl9.Text = "splitContainerControl9";
            // 
            // tabControlChartGSMBlockReason
            // 
            this.tabControlChartGSMBlockReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartGSMBlockReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartGSMBlockReason.Name = "tabControlChartGSMBlockReason";
            this.tabControlChartGSMBlockReason.Size = new System.Drawing.Size(522, 292);
            this.tabControlChartGSMBlockReason.TabIndex = 6;
            // 
            // tabControlGSMBlockDetail
            // 
            this.tabControlGSMBlockDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlGSMBlockDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlGSMBlockDetail.Name = "tabControlGSMBlockDetail";
            this.tabControlGSMBlockDetail.Size = new System.Drawing.Size(499, 292);
            this.tabControlGSMBlockDetail.TabIndex = 7;
            // 
            // gridView15
            // 
            this.gridView15.Name = "gridView15";
            // 
            // gridView16
            // 
            this.gridView16.Name = "gridView16";
            // 
            // tabGSMDrop
            // 
            this.tabGSMDrop.Controls.Add(this.splitContainerControl4);
            this.tabGSMDrop.Location = new System.Drawing.Point(4, 23);
            this.tabGSMDrop.Name = "tabGSMDrop";
            this.tabGSMDrop.Size = new System.Drawing.Size(1025, 542);
            this.tabGSMDrop.TabIndex = 2;
            this.tabGSMDrop.Text = "GSM掉话原因分析";
            this.tabGSMDrop.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl4
            // 
            this.splitContainerControl4.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl4.Appearance.Options.UseForeColor = true;
            this.splitContainerControl4.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl4.Horizontal = false;
            this.splitContainerControl4.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl4.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl4.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl4.Name = "splitContainerControl4";
            this.splitContainerControl4.Panel1.Controls.Add(this.gridControlGSMDropReason);
            this.splitContainerControl4.Panel1.Text = "Panel1";
            this.splitContainerControl4.Panel2.Controls.Add(this.splitContainerControl8);
            this.splitContainerControl4.Panel2.Text = "Panel2";
            this.splitContainerControl4.Size = new System.Drawing.Size(1025, 542);
            this.splitContainerControl4.SplitterPosition = 232;
            this.splitContainerControl4.TabIndex = 8;
            this.splitContainerControl4.Text = "splitContainerControl4";
            // 
            // gridControlGSMDropReason
            // 
            this.gridControlGSMDropReason.ContextMenuStrip = this.ctxMenu;
            this.gridControlGSMDropReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlGSMDropReason.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlGSMDropReason.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlGSMDropReason.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlGSMDropReason.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlGSMDropReason.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlGSMDropReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlGSMDropReason.MainView = this.gridViewGSMDropReason;
            this.gridControlGSMDropReason.Name = "gridControlGSMDropReason";
            this.gridControlGSMDropReason.Size = new System.Drawing.Size(1025, 232);
            this.gridControlGSMDropReason.TabIndex = 5;
            this.gridControlGSMDropReason.UseEmbeddedNavigator = true;
            this.gridControlGSMDropReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewGSMDropReason,
            this.gridView17});
            // 
            // gridViewGSMDropReason
            // 
            this.gridViewGSMDropReason.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn129,
            this.gridColumn131,
            this.gridColumn132,
            this.gridColumn130,
            this.gridColumn133,
            this.gridColumn134});
            this.gridViewGSMDropReason.GridControl = this.gridControlGSMDropReason;
            this.gridViewGSMDropReason.Name = "gridViewGSMDropReason";
            this.gridViewGSMDropReason.OptionsBehavior.Editable = false;
            this.gridViewGSMDropReason.OptionsView.AllowCellMerge = true;
            this.gridViewGSMDropReason.OptionsView.ShowDetailButtons = false;
            this.gridViewGSMDropReason.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn129
            // 
            this.gridColumn129.Caption = "区域名称";
            this.gridColumn129.FieldName = "AreaName";
            this.gridColumn129.Name = "gridColumn129";
            this.gridColumn129.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn129.Visible = true;
            this.gridColumn129.VisibleIndex = 0;
            this.gridColumn129.Width = 140;
            // 
            // gridColumn131
            // 
            this.gridColumn131.Caption = "开始时间";
            this.gridColumn131.FieldName = "BeginTime";
            this.gridColumn131.Name = "gridColumn131";
            this.gridColumn131.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn131.Visible = true;
            this.gridColumn131.VisibleIndex = 1;
            this.gridColumn131.Width = 160;
            // 
            // gridColumn132
            // 
            this.gridColumn132.Caption = "结束时间";
            this.gridColumn132.FieldName = "EndTime";
            this.gridColumn132.Name = "gridColumn132";
            this.gridColumn132.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn132.Visible = true;
            this.gridColumn132.VisibleIndex = 2;
            this.gridColumn132.Width = 160;
            // 
            // gridColumn130
            // 
            this.gridColumn130.Caption = "原因";
            this.gridColumn130.FieldName = "ReasonName";
            this.gridColumn130.Name = "gridColumn130";
            this.gridColumn130.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn130.Visible = true;
            this.gridColumn130.VisibleIndex = 3;
            this.gridColumn130.Width = 160;
            // 
            // gridColumn133
            // 
            this.gridColumn133.Caption = "事件数量";
            this.gridColumn133.FieldName = "ReasonCount";
            this.gridColumn133.Name = "gridColumn133";
            this.gridColumn133.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn133.Visible = true;
            this.gridColumn133.VisibleIndex = 4;
            this.gridColumn133.Width = 160;
            // 
            // gridColumn134
            // 
            this.gridColumn134.Caption = "原因占比（%）";
            this.gridColumn134.FieldName = "ReasonPercent";
            this.gridColumn134.Name = "gridColumn134";
            this.gridColumn134.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn134.Visible = true;
            this.gridColumn134.VisibleIndex = 5;
            this.gridColumn134.Width = 160;
            // 
            // gridView17
            // 
            this.gridView17.GridControl = this.gridControlGSMDropReason;
            this.gridView17.Name = "gridView17";
            // 
            // splitContainerControl8
            // 
            this.splitContainerControl8.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl8.Appearance.Options.UseForeColor = true;
            this.splitContainerControl8.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl8.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl8.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl8.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl8.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl8.Name = "splitContainerControl8";
            this.splitContainerControl8.Panel1.Controls.Add(this.tabControlChartGSMDropReason);
            this.splitContainerControl8.Panel1.Text = "Panel1";
            this.splitContainerControl8.Panel2.Controls.Add(this.tabControlGSMDropDetail);
            this.splitContainerControl8.Panel2.Text = "Panel2";
            this.splitContainerControl8.Size = new System.Drawing.Size(1025, 306);
            this.splitContainerControl8.SplitterPosition = 522;
            this.splitContainerControl8.TabIndex = 9;
            this.splitContainerControl8.Text = "splitContainerControl8";
            // 
            // tabControlChartGSMDropReason
            // 
            this.tabControlChartGSMDropReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartGSMDropReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartGSMDropReason.Name = "tabControlChartGSMDropReason";
            this.tabControlChartGSMDropReason.Size = new System.Drawing.Size(522, 306);
            this.tabControlChartGSMDropReason.TabIndex = 6;
            // 
            // tabControlGSMDropDetail
            // 
            this.tabControlGSMDropDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlGSMDropDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlGSMDropDetail.Name = "tabControlGSMDropDetail";
            this.tabControlGSMDropDetail.Size = new System.Drawing.Size(499, 306);
            this.tabControlGSMDropDetail.TabIndex = 7;
            // 
            // tabTDProfile
            // 
            this.tabTDProfile.Controls.Add(this.splitContainerControl3);
            this.tabTDProfile.Location = new System.Drawing.Point(4, 23);
            this.tabTDProfile.Name = "tabTDProfile";
            this.tabTDProfile.Padding = new System.Windows.Forms.Padding(3);
            this.tabTDProfile.Size = new System.Drawing.Size(1025, 542);
            this.tabTDProfile.TabIndex = 1;
            this.tabTDProfile.Text = "TD异常事件处理完成率";
            this.tabTDProfile.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl3
            // 
            this.splitContainerControl3.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl3.Appearance.Options.UseForeColor = true;
            this.splitContainerControl3.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl3.Horizontal = false;
            this.splitContainerControl3.Location = new System.Drawing.Point(3, 3);
            this.splitContainerControl3.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl3.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl3.Name = "splitContainerControl3";
            this.splitContainerControl3.Panel1.Controls.Add(this.gridControlTDProfile);
            this.splitContainerControl3.Panel1.Text = "Panel1";
            this.splitContainerControl3.Panel2.Controls.Add(this.tabControlChartTDProfile);
            this.splitContainerControl3.Panel2.Text = "Panel2";
            this.splitContainerControl3.Size = new System.Drawing.Size(1019, 536);
            this.splitContainerControl3.SplitterPosition = 266;
            this.splitContainerControl3.TabIndex = 8;
            this.splitContainerControl3.Text = "splitContainerControl3";
            // 
            // gridControlTDProfile
            // 
            this.gridControlTDProfile.ContextMenuStrip = this.ctxMenu;
            this.gridControlTDProfile.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlTDProfile.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlTDProfile.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlTDProfile.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlTDProfile.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlTDProfile.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlTDProfile.Location = new System.Drawing.Point(0, 0);
            this.gridControlTDProfile.MainView = this.gridViewTDProfile;
            this.gridControlTDProfile.Name = "gridControlTDProfile";
            this.gridControlTDProfile.Size = new System.Drawing.Size(1019, 266);
            this.gridControlTDProfile.TabIndex = 5;
            this.gridControlTDProfile.UseEmbeddedNavigator = true;
            this.gridControlTDProfile.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewTDProfile,
            this.gridView18});
            // 
            // gridViewTDProfile
            // 
            this.gridViewTDProfile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn17,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32});
            this.gridViewTDProfile.GridControl = this.gridControlTDProfile;
            this.gridViewTDProfile.Name = "gridViewTDProfile";
            this.gridViewTDProfile.OptionsBehavior.Editable = false;
            this.gridViewTDProfile.OptionsView.AllowCellMerge = true;
            this.gridViewTDProfile.OptionsView.ColumnAutoWidth = false;
            this.gridViewTDProfile.OptionsView.ShowDetailButtons = false;
            this.gridViewTDProfile.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "区域名称";
            this.gridColumn17.FieldName = "AreaName";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 0;
            this.gridColumn17.Width = 77;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "开始时间";
            this.gridColumn19.FieldName = "BeginTime";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 1;
            this.gridColumn19.Width = 116;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "结束时间";
            this.gridColumn20.FieldName = "EndTime";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 2;
            this.gridColumn20.Width = 116;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "掉话总数量";
            this.gridColumn21.FieldName = "DropCount";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 3;
            this.gridColumn21.Width = 72;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "掉话未完成数量";
            this.gridColumn22.FieldName = "DropUnFinishedCount";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 4;
            this.gridColumn22.Width = 96;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "掉话已完成数量";
            this.gridColumn23.FieldName = "DropFinishedCount";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 5;
            this.gridColumn23.Width = 96;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "掉话已完成百分比(%)";
            this.gridColumn24.FieldName = "DropFinishedPercent";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 6;
            this.gridColumn24.Width = 130;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "未接通总数量";
            this.gridColumn25.FieldName = "BlockCount";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 7;
            this.gridColumn25.Width = 84;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "未接通未完成数量";
            this.gridColumn26.FieldName = "BlockUnFinishedCount";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 8;
            this.gridColumn26.Width = 108;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "未接通已完成数量";
            this.gridColumn27.FieldName = "BlockFinishedCount";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 9;
            this.gridColumn27.Width = 47;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "未接通已完成百分比(%)";
            this.gridColumn28.FieldName = "BlockFinishedPercent";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 10;
            this.gridColumn28.Width = 47;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "事件总数量";
            this.gridColumn29.FieldName = "EventCount";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 11;
            this.gridColumn29.Width = 47;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "事件未完成数量";
            this.gridColumn30.FieldName = "EventUnFinishedCount";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 12;
            this.gridColumn30.Width = 47;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "事件已完成数量";
            this.gridColumn31.FieldName = "EventFinishedCount";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 13;
            this.gridColumn31.Width = 48;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "事件已完成百分比(%)";
            this.gridColumn32.FieldName = "EventFinishedPercent";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 14;
            this.gridColumn32.Width = 50;
            // 
            // gridView18
            // 
            this.gridView18.GridControl = this.gridControlTDProfile;
            this.gridView18.Name = "gridView18";
            // 
            // tabControlChartTDProfile
            // 
            this.tabControlChartTDProfile.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartTDProfile.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartTDProfile.Name = "tabControlChartTDProfile";
            this.tabControlChartTDProfile.Size = new System.Drawing.Size(1019, 266);
            this.tabControlChartTDProfile.TabIndex = 6;
            // 
            // tabGSMProfile
            // 
            this.tabGSMProfile.Controls.Add(this.splitContainerControl1);
            this.tabGSMProfile.Location = new System.Drawing.Point(4, 23);
            this.tabGSMProfile.Name = "tabGSMProfile";
            this.tabGSMProfile.Padding = new System.Windows.Forms.Padding(3);
            this.tabGSMProfile.Size = new System.Drawing.Size(1025, 542);
            this.tabGSMProfile.TabIndex = 0;
            this.tabGSMProfile.Text = "GSM异常事件处理完成率";
            this.tabGSMProfile.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl1.Appearance.Options.UseForeColor = true;
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(3, 3);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridControlGSMProfile);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.tabControlChartGSMProfile);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1019, 536);
            this.splitContainerControl1.SplitterPosition = 267;
            this.splitContainerControl1.TabIndex = 7;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridControlGSMProfile
            // 
            this.gridControlGSMProfile.ContextMenuStrip = this.ctxMenu;
            this.gridControlGSMProfile.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlGSMProfile.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlGSMProfile.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlGSMProfile.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlGSMProfile.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlGSMProfile.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlGSMProfile.Location = new System.Drawing.Point(0, 0);
            this.gridControlGSMProfile.MainView = this.gridViewGSMProfile;
            this.gridControlGSMProfile.Name = "gridControlGSMProfile";
            this.gridControlGSMProfile.Size = new System.Drawing.Size(1019, 267);
            this.gridControlGSMProfile.TabIndex = 5;
            this.gridControlGSMProfile.UseEmbeddedNavigator = true;
            this.gridControlGSMProfile.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewGSMProfile,
            this.gridView19});
            // 
            // gridViewGSMProfile
            // 
            this.gridViewGSMProfile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnAreaName,
            this.gridColumnBeginTime,
            this.gridColumnEndTime,
            this.gridColumnDropCount,
            this.gridColumnDropUnFinishedCount,
            this.gridColumnDropFinishedCount,
            this.gridColumnDropFinishedPercent,
            this.gridColumnBlockCount,
            this.gridColumnBlockUnFinishedCount,
            this.gridColumnBlockFinishedCount,
            this.gridColumnBlockFinishedPercent,
            this.gridColumnEventCount,
            this.gridColumnEventUnFinishedCount,
            this.gridColumnEventFinishedCount,
            this.gridColumnEventFinishedPercent});
            this.gridViewGSMProfile.GridControl = this.gridControlGSMProfile;
            this.gridViewGSMProfile.Name = "gridViewGSMProfile";
            this.gridViewGSMProfile.OptionsBehavior.Editable = false;
            this.gridViewGSMProfile.OptionsView.AllowCellMerge = true;
            this.gridViewGSMProfile.OptionsView.ColumnAutoWidth = false;
            this.gridViewGSMProfile.OptionsView.ShowDetailButtons = false;
            this.gridViewGSMProfile.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnAreaName
            // 
            this.gridColumnAreaName.Caption = "区域名称";
            this.gridColumnAreaName.FieldName = "AreaName";
            this.gridColumnAreaName.Name = "gridColumnAreaName";
            this.gridColumnAreaName.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumnAreaName.Visible = true;
            this.gridColumnAreaName.VisibleIndex = 0;
            this.gridColumnAreaName.Width = 77;
            // 
            // gridColumnBeginTime
            // 
            this.gridColumnBeginTime.Caption = "开始时间";
            this.gridColumnBeginTime.FieldName = "BeginTime";
            this.gridColumnBeginTime.Name = "gridColumnBeginTime";
            this.gridColumnBeginTime.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnBeginTime.Visible = true;
            this.gridColumnBeginTime.VisibleIndex = 1;
            this.gridColumnBeginTime.Width = 116;
            // 
            // gridColumnEndTime
            // 
            this.gridColumnEndTime.Caption = "结束时间";
            this.gridColumnEndTime.FieldName = "EndTime";
            this.gridColumnEndTime.Name = "gridColumnEndTime";
            this.gridColumnEndTime.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnEndTime.Visible = true;
            this.gridColumnEndTime.VisibleIndex = 2;
            this.gridColumnEndTime.Width = 116;
            // 
            // gridColumnDropCount
            // 
            this.gridColumnDropCount.Caption = "掉话总数量";
            this.gridColumnDropCount.FieldName = "DropCount";
            this.gridColumnDropCount.Name = "gridColumnDropCount";
            this.gridColumnDropCount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnDropCount.Visible = true;
            this.gridColumnDropCount.VisibleIndex = 3;
            this.gridColumnDropCount.Width = 72;
            // 
            // gridColumnDropUnFinishedCount
            // 
            this.gridColumnDropUnFinishedCount.Caption = "掉话未完成数量";
            this.gridColumnDropUnFinishedCount.FieldName = "DropUnFinishedCount";
            this.gridColumnDropUnFinishedCount.Name = "gridColumnDropUnFinishedCount";
            this.gridColumnDropUnFinishedCount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnDropUnFinishedCount.Visible = true;
            this.gridColumnDropUnFinishedCount.VisibleIndex = 4;
            this.gridColumnDropUnFinishedCount.Width = 96;
            // 
            // gridColumnDropFinishedCount
            // 
            this.gridColumnDropFinishedCount.Caption = "掉话已完成数量";
            this.gridColumnDropFinishedCount.FieldName = "DropFinishedCount";
            this.gridColumnDropFinishedCount.Name = "gridColumnDropFinishedCount";
            this.gridColumnDropFinishedCount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnDropFinishedCount.Visible = true;
            this.gridColumnDropFinishedCount.VisibleIndex = 5;
            this.gridColumnDropFinishedCount.Width = 96;
            // 
            // gridColumnDropFinishedPercent
            // 
            this.gridColumnDropFinishedPercent.Caption = "掉话已完成百分比(%)";
            this.gridColumnDropFinishedPercent.FieldName = "DropFinishedPercent";
            this.gridColumnDropFinishedPercent.Name = "gridColumnDropFinishedPercent";
            this.gridColumnDropFinishedPercent.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnDropFinishedPercent.Visible = true;
            this.gridColumnDropFinishedPercent.VisibleIndex = 6;
            this.gridColumnDropFinishedPercent.Width = 130;
            // 
            // gridColumnBlockCount
            // 
            this.gridColumnBlockCount.Caption = "未接通总数量";
            this.gridColumnBlockCount.FieldName = "BlockCount";
            this.gridColumnBlockCount.Name = "gridColumnBlockCount";
            this.gridColumnBlockCount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnBlockCount.Visible = true;
            this.gridColumnBlockCount.VisibleIndex = 7;
            this.gridColumnBlockCount.Width = 84;
            // 
            // gridColumnBlockUnFinishedCount
            // 
            this.gridColumnBlockUnFinishedCount.Caption = "未接通未完成数量";
            this.gridColumnBlockUnFinishedCount.FieldName = "BlockUnFinishedCount";
            this.gridColumnBlockUnFinishedCount.Name = "gridColumnBlockUnFinishedCount";
            this.gridColumnBlockUnFinishedCount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnBlockUnFinishedCount.Visible = true;
            this.gridColumnBlockUnFinishedCount.VisibleIndex = 8;
            this.gridColumnBlockUnFinishedCount.Width = 108;
            // 
            // gridColumnBlockFinishedCount
            // 
            this.gridColumnBlockFinishedCount.Caption = "未接通已完成数量";
            this.gridColumnBlockFinishedCount.FieldName = "BlockFinishedCount";
            this.gridColumnBlockFinishedCount.Name = "gridColumnBlockFinishedCount";
            this.gridColumnBlockFinishedCount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnBlockFinishedCount.Visible = true;
            this.gridColumnBlockFinishedCount.VisibleIndex = 9;
            this.gridColumnBlockFinishedCount.Width = 47;
            // 
            // gridColumnBlockFinishedPercent
            // 
            this.gridColumnBlockFinishedPercent.Caption = "未接通已完成百分比(%)";
            this.gridColumnBlockFinishedPercent.FieldName = "BlockFinishedPercent";
            this.gridColumnBlockFinishedPercent.Name = "gridColumnBlockFinishedPercent";
            this.gridColumnBlockFinishedPercent.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnBlockFinishedPercent.Visible = true;
            this.gridColumnBlockFinishedPercent.VisibleIndex = 10;
            this.gridColumnBlockFinishedPercent.Width = 47;
            // 
            // gridColumnEventCount
            // 
            this.gridColumnEventCount.Caption = "事件总数量";
            this.gridColumnEventCount.FieldName = "EventCount";
            this.gridColumnEventCount.Name = "gridColumnEventCount";
            this.gridColumnEventCount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnEventCount.Visible = true;
            this.gridColumnEventCount.VisibleIndex = 11;
            this.gridColumnEventCount.Width = 47;
            // 
            // gridColumnEventUnFinishedCount
            // 
            this.gridColumnEventUnFinishedCount.Caption = "事件未完成数量";
            this.gridColumnEventUnFinishedCount.FieldName = "EventUnFinishedCount";
            this.gridColumnEventUnFinishedCount.Name = "gridColumnEventUnFinishedCount";
            this.gridColumnEventUnFinishedCount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnEventUnFinishedCount.Visible = true;
            this.gridColumnEventUnFinishedCount.VisibleIndex = 12;
            this.gridColumnEventUnFinishedCount.Width = 47;
            // 
            // gridColumnEventFinishedCount
            // 
            this.gridColumnEventFinishedCount.Caption = "事件已完成数量";
            this.gridColumnEventFinishedCount.FieldName = "EventFinishedCount";
            this.gridColumnEventFinishedCount.Name = "gridColumnEventFinishedCount";
            this.gridColumnEventFinishedCount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnEventFinishedCount.Visible = true;
            this.gridColumnEventFinishedCount.VisibleIndex = 13;
            this.gridColumnEventFinishedCount.Width = 48;
            // 
            // gridColumnEventFinishedPercent
            // 
            this.gridColumnEventFinishedPercent.Caption = "事件已完成百分比(%)";
            this.gridColumnEventFinishedPercent.FieldName = "EventFinishedPercent";
            this.gridColumnEventFinishedPercent.Name = "gridColumnEventFinishedPercent";
            this.gridColumnEventFinishedPercent.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumnEventFinishedPercent.Visible = true;
            this.gridColumnEventFinishedPercent.VisibleIndex = 14;
            this.gridColumnEventFinishedPercent.Width = 50;
            // 
            // gridView19
            // 
            this.gridView19.GridControl = this.gridControlGSMProfile;
            this.gridView19.Name = "gridView19";
            // 
            // tabControlChartGSMProfile
            // 
            this.tabControlChartGSMProfile.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartGSMProfile.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartGSMProfile.Name = "tabControlChartGSMProfile";
            this.tabControlChartGSMProfile.Size = new System.Drawing.Size(1019, 265);
            this.tabControlChartGSMProfile.TabIndex = 6;
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabGSMProfile);
            this.tabControl1.Controls.Add(this.tabTDProfile);
            this.tabControl1.Controls.Add(this.tabLTEProfile);
            this.tabControl1.Controls.Add(this.tabLTEScanProfile);
            this.tabControl1.Controls.Add(this.tabGSMDrop);
            this.tabControl1.Controls.Add(this.tabGSMBlock);
            this.tabControl1.Controls.Add(this.tabTDDrop);
            this.tabControl1.Controls.Add(this.tabTDBlock);
            this.tabControl1.Controls.Add(this.tabLTEDrop);
            this.tabControl1.Controls.Add(this.tabLTEBlock);
            this.tabControl1.Controls.Add(this.tabLTEScanDrop);
            this.tabControl1.Controls.Add(this.tabLTEScanBlock);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1033, 569);
            this.tabControl1.TabIndex = 6;
            // 
            // tabLTEProfile
            // 
            this.tabLTEProfile.Controls.Add(this.splitContainerControl20);
            this.tabLTEProfile.Location = new System.Drawing.Point(4, 23);
            this.tabLTEProfile.Name = "tabLTEProfile";
            this.tabLTEProfile.Padding = new System.Windows.Forms.Padding(3);
            this.tabLTEProfile.Size = new System.Drawing.Size(1025, 542);
            this.tabLTEProfile.TabIndex = 9;
            this.tabLTEProfile.Text = "LTE异常事件处理完成率";
            this.tabLTEProfile.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl20
            // 
            this.splitContainerControl20.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl20.Appearance.Options.UseForeColor = true;
            this.splitContainerControl20.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl20.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl20.Horizontal = false;
            this.splitContainerControl20.Location = new System.Drawing.Point(3, 3);
            this.splitContainerControl20.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl20.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl20.Name = "splitContainerControl20";
            this.splitContainerControl20.Panel1.Controls.Add(this.gridControlLTEProfile);
            this.splitContainerControl20.Panel1.Text = "Panel1";
            this.splitContainerControl20.Panel2.Controls.Add(this.tabControlChartLTEProfile);
            this.splitContainerControl20.Panel2.Text = "Panel2";
            this.splitContainerControl20.Size = new System.Drawing.Size(1019, 536);
            this.splitContainerControl20.SplitterPosition = 267;
            this.splitContainerControl20.TabIndex = 8;
            this.splitContainerControl20.Text = "splitContainerControl20";
            // 
            // gridControlLTEProfile
            // 
            this.gridControlLTEProfile.ContextMenuStrip = this.ctxMenu;
            this.gridControlLTEProfile.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlLTEProfile.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlLTEProfile.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlLTEProfile.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlLTEProfile.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlLTEProfile.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlLTEProfile.Location = new System.Drawing.Point(0, 0);
            this.gridControlLTEProfile.MainView = this.gridViewLTEProfile;
            this.gridControlLTEProfile.Name = "gridControlLTEProfile";
            this.gridControlLTEProfile.Size = new System.Drawing.Size(1019, 267);
            this.gridControlLTEProfile.TabIndex = 5;
            this.gridControlLTEProfile.UseEmbeddedNavigator = true;
            this.gridControlLTEProfile.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewLTEProfile,
            this.gridView10});
            // 
            // gridViewLTEProfile
            // 
            this.gridViewLTEProfile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn161,
            this.gridColumn163,
            this.gridColumn164,
            this.gridColumn165,
            this.gridColumn166,
            this.gridColumn167,
            this.gridColumn168,
            this.gridColumn169,
            this.gridColumn170,
            this.gridColumn171,
            this.gridColumn172,
            this.gridColumn173,
            this.gridColumn174,
            this.gridColumn175,
            this.gridColumn176});
            this.gridViewLTEProfile.GridControl = this.gridControlLTEProfile;
            this.gridViewLTEProfile.Name = "gridViewLTEProfile";
            this.gridViewLTEProfile.OptionsBehavior.Editable = false;
            this.gridViewLTEProfile.OptionsView.AllowCellMerge = true;
            this.gridViewLTEProfile.OptionsView.ColumnAutoWidth = false;
            this.gridViewLTEProfile.OptionsView.ShowDetailButtons = false;
            this.gridViewLTEProfile.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn161
            // 
            this.gridColumn161.Caption = "区域名称";
            this.gridColumn161.FieldName = "AreaName";
            this.gridColumn161.Name = "gridColumn161";
            this.gridColumn161.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn161.Visible = true;
            this.gridColumn161.VisibleIndex = 0;
            this.gridColumn161.Width = 77;
            // 
            // gridColumn163
            // 
            this.gridColumn163.Caption = "开始时间";
            this.gridColumn163.FieldName = "BeginTime";
            this.gridColumn163.Name = "gridColumn163";
            this.gridColumn163.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn163.Visible = true;
            this.gridColumn163.VisibleIndex = 1;
            this.gridColumn163.Width = 116;
            // 
            // gridColumn164
            // 
            this.gridColumn164.Caption = "结束时间";
            this.gridColumn164.FieldName = "EndTime";
            this.gridColumn164.Name = "gridColumn164";
            this.gridColumn164.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn164.Visible = true;
            this.gridColumn164.VisibleIndex = 2;
            this.gridColumn164.Width = 116;
            // 
            // gridColumn165
            // 
            this.gridColumn165.Caption = "掉话总数量";
            this.gridColumn165.FieldName = "DropCount";
            this.gridColumn165.Name = "gridColumn165";
            this.gridColumn165.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn165.Visible = true;
            this.gridColumn165.VisibleIndex = 3;
            this.gridColumn165.Width = 72;
            // 
            // gridColumn166
            // 
            this.gridColumn166.Caption = "掉话未完成数量";
            this.gridColumn166.FieldName = "DropUnFinishedCount";
            this.gridColumn166.Name = "gridColumn166";
            this.gridColumn166.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn166.Visible = true;
            this.gridColumn166.VisibleIndex = 4;
            this.gridColumn166.Width = 96;
            // 
            // gridColumn167
            // 
            this.gridColumn167.Caption = "掉话已完成数量";
            this.gridColumn167.FieldName = "DropFinishedCount";
            this.gridColumn167.Name = "gridColumn167";
            this.gridColumn167.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn167.Visible = true;
            this.gridColumn167.VisibleIndex = 5;
            this.gridColumn167.Width = 96;
            // 
            // gridColumn168
            // 
            this.gridColumn168.Caption = "掉话已完成百分比(%)";
            this.gridColumn168.FieldName = "DropFinishedPercent";
            this.gridColumn168.Name = "gridColumn168";
            this.gridColumn168.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn168.Visible = true;
            this.gridColumn168.VisibleIndex = 6;
            this.gridColumn168.Width = 130;
            // 
            // gridColumn169
            // 
            this.gridColumn169.Caption = "未接通总数量";
            this.gridColumn169.FieldName = "BlockCount";
            this.gridColumn169.Name = "gridColumn169";
            this.gridColumn169.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn169.Visible = true;
            this.gridColumn169.VisibleIndex = 7;
            this.gridColumn169.Width = 84;
            // 
            // gridColumn170
            // 
            this.gridColumn170.Caption = "未接通未完成数量";
            this.gridColumn170.FieldName = "BlockUnFinishedCount";
            this.gridColumn170.Name = "gridColumn170";
            this.gridColumn170.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn170.Visible = true;
            this.gridColumn170.VisibleIndex = 8;
            this.gridColumn170.Width = 108;
            // 
            // gridColumn171
            // 
            this.gridColumn171.Caption = "未接通已完成数量";
            this.gridColumn171.FieldName = "BlockFinishedCount";
            this.gridColumn171.Name = "gridColumn171";
            this.gridColumn171.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn171.Visible = true;
            this.gridColumn171.VisibleIndex = 9;
            this.gridColumn171.Width = 47;
            // 
            // gridColumn172
            // 
            this.gridColumn172.Caption = "未接通已完成百分比(%)";
            this.gridColumn172.FieldName = "BlockFinishedPercent";
            this.gridColumn172.Name = "gridColumn172";
            this.gridColumn172.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn172.Visible = true;
            this.gridColumn172.VisibleIndex = 10;
            this.gridColumn172.Width = 47;
            // 
            // gridColumn173
            // 
            this.gridColumn173.Caption = "事件总数量";
            this.gridColumn173.FieldName = "EventCount";
            this.gridColumn173.Name = "gridColumn173";
            this.gridColumn173.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn173.Visible = true;
            this.gridColumn173.VisibleIndex = 11;
            this.gridColumn173.Width = 47;
            // 
            // gridColumn174
            // 
            this.gridColumn174.Caption = "事件未完成数量";
            this.gridColumn174.FieldName = "EventUnFinishedCount";
            this.gridColumn174.Name = "gridColumn174";
            this.gridColumn174.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn174.Visible = true;
            this.gridColumn174.VisibleIndex = 12;
            this.gridColumn174.Width = 47;
            // 
            // gridColumn175
            // 
            this.gridColumn175.Caption = "事件已完成数量";
            this.gridColumn175.FieldName = "EventFinishedCount";
            this.gridColumn175.Name = "gridColumn175";
            this.gridColumn175.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn175.Visible = true;
            this.gridColumn175.VisibleIndex = 13;
            this.gridColumn175.Width = 48;
            // 
            // gridColumn176
            // 
            this.gridColumn176.Caption = "事件已完成百分比(%)";
            this.gridColumn176.FieldName = "EventFinishedPercent";
            this.gridColumn176.Name = "gridColumn176";
            this.gridColumn176.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn176.Visible = true;
            this.gridColumn176.VisibleIndex = 14;
            this.gridColumn176.Width = 50;
            // 
            // gridView10
            // 
            this.gridView10.GridControl = this.gridControlLTEProfile;
            this.gridView10.Name = "gridView10";
            // 
            // tabControlChartLTEProfile
            // 
            this.tabControlChartLTEProfile.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartLTEProfile.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartLTEProfile.Name = "tabControlChartLTEProfile";
            this.tabControlChartLTEProfile.Size = new System.Drawing.Size(1019, 265);
            this.tabControlChartLTEProfile.TabIndex = 6;
            // 
            // tabLTEScanProfile
            // 
            this.tabLTEScanProfile.Controls.Add(this.splitContainerControl21);
            this.tabLTEScanProfile.Location = new System.Drawing.Point(4, 23);
            this.tabLTEScanProfile.Name = "tabLTEScanProfile";
            this.tabLTEScanProfile.Size = new System.Drawing.Size(1025, 542);
            this.tabLTEScanProfile.TabIndex = 10;
            this.tabLTEScanProfile.Text = "LTEScan异常事件处理完成率";
            this.tabLTEScanProfile.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl21
            // 
            this.splitContainerControl21.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl21.Appearance.Options.UseForeColor = true;
            this.splitContainerControl21.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl21.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl21.Horizontal = false;
            this.splitContainerControl21.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl21.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl21.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl21.Name = "splitContainerControl21";
            this.splitContainerControl21.Panel1.Controls.Add(this.gridControlLTEScanProfile);
            this.splitContainerControl21.Panel1.Text = "Panel1";
            this.splitContainerControl21.Panel2.Controls.Add(this.tabControlChartLTEScanProfile);
            this.splitContainerControl21.Panel2.Text = "Panel2";
            this.splitContainerControl21.Size = new System.Drawing.Size(1025, 542);
            this.splitContainerControl21.SplitterPosition = 267;
            this.splitContainerControl21.TabIndex = 9;
            this.splitContainerControl21.Text = "splitContainerControl21";
            // 
            // gridControlLTEScanProfile
            // 
            this.gridControlLTEScanProfile.ContextMenuStrip = this.ctxMenu;
            this.gridControlLTEScanProfile.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlLTEScanProfile.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlLTEScanProfile.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlLTEScanProfile.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlLTEScanProfile.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlLTEScanProfile.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlLTEScanProfile.Location = new System.Drawing.Point(0, 0);
            this.gridControlLTEScanProfile.MainView = this.gridViewLTEScanProfile;
            this.gridControlLTEScanProfile.Name = "gridControlLTEScanProfile";
            this.gridControlLTEScanProfile.Size = new System.Drawing.Size(1025, 267);
            this.gridControlLTEScanProfile.TabIndex = 5;
            this.gridControlLTEScanProfile.UseEmbeddedNavigator = true;
            this.gridControlLTEScanProfile.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewLTEScanProfile,
            this.gridView11});
            // 
            // gridViewLTEScanProfile
            // 
            this.gridViewLTEScanProfile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn177,
            this.gridColumn179,
            this.gridColumn180,
            this.gridColumn181,
            this.gridColumn182,
            this.gridColumn183,
            this.gridColumn184,
            this.gridColumn185,
            this.gridColumn186,
            this.gridColumn187,
            this.gridColumn188,
            this.gridColumn189,
            this.gridColumn190,
            this.gridColumn191,
            this.gridColumn192});
            this.gridViewLTEScanProfile.GridControl = this.gridControlLTEScanProfile;
            this.gridViewLTEScanProfile.Name = "gridViewLTEScanProfile";
            this.gridViewLTEScanProfile.OptionsBehavior.Editable = false;
            this.gridViewLTEScanProfile.OptionsView.AllowCellMerge = true;
            this.gridViewLTEScanProfile.OptionsView.ColumnAutoWidth = false;
            this.gridViewLTEScanProfile.OptionsView.ShowDetailButtons = false;
            this.gridViewLTEScanProfile.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn177
            // 
            this.gridColumn177.Caption = "区域名称";
            this.gridColumn177.FieldName = "AreaName";
            this.gridColumn177.Name = "gridColumn177";
            this.gridColumn177.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn177.Visible = true;
            this.gridColumn177.VisibleIndex = 0;
            this.gridColumn177.Width = 77;
            // 
            // gridColumn179
            // 
            this.gridColumn179.Caption = "开始时间";
            this.gridColumn179.FieldName = "BeginTime";
            this.gridColumn179.Name = "gridColumn179";
            this.gridColumn179.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn179.Visible = true;
            this.gridColumn179.VisibleIndex = 1;
            this.gridColumn179.Width = 116;
            // 
            // gridColumn180
            // 
            this.gridColumn180.Caption = "结束时间";
            this.gridColumn180.FieldName = "EndTime";
            this.gridColumn180.Name = "gridColumn180";
            this.gridColumn180.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn180.Visible = true;
            this.gridColumn180.VisibleIndex = 2;
            this.gridColumn180.Width = 116;
            // 
            // gridColumn181
            // 
            this.gridColumn181.Caption = "掉话总数量";
            this.gridColumn181.FieldName = "DropCount";
            this.gridColumn181.Name = "gridColumn181";
            this.gridColumn181.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn181.Visible = true;
            this.gridColumn181.VisibleIndex = 3;
            this.gridColumn181.Width = 72;
            // 
            // gridColumn182
            // 
            this.gridColumn182.Caption = "掉话未完成数量";
            this.gridColumn182.FieldName = "DropUnFinishedCount";
            this.gridColumn182.Name = "gridColumn182";
            this.gridColumn182.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn182.Visible = true;
            this.gridColumn182.VisibleIndex = 4;
            this.gridColumn182.Width = 96;
            // 
            // gridColumn183
            // 
            this.gridColumn183.Caption = "掉话已完成数量";
            this.gridColumn183.FieldName = "DropFinishedCount";
            this.gridColumn183.Name = "gridColumn183";
            this.gridColumn183.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn183.Visible = true;
            this.gridColumn183.VisibleIndex = 5;
            this.gridColumn183.Width = 96;
            // 
            // gridColumn184
            // 
            this.gridColumn184.Caption = "掉话已完成百分比(%)";
            this.gridColumn184.FieldName = "DropFinishedPercent";
            this.gridColumn184.Name = "gridColumn184";
            this.gridColumn184.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn184.Visible = true;
            this.gridColumn184.VisibleIndex = 6;
            this.gridColumn184.Width = 130;
            // 
            // gridColumn185
            // 
            this.gridColumn185.Caption = "未接通总数量";
            this.gridColumn185.FieldName = "BlockCount";
            this.gridColumn185.Name = "gridColumn185";
            this.gridColumn185.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn185.Visible = true;
            this.gridColumn185.VisibleIndex = 7;
            this.gridColumn185.Width = 84;
            // 
            // gridColumn186
            // 
            this.gridColumn186.Caption = "未接通未完成数量";
            this.gridColumn186.FieldName = "BlockUnFinishedCount";
            this.gridColumn186.Name = "gridColumn186";
            this.gridColumn186.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn186.Visible = true;
            this.gridColumn186.VisibleIndex = 8;
            this.gridColumn186.Width = 108;
            // 
            // gridColumn187
            // 
            this.gridColumn187.Caption = "未接通已完成数量";
            this.gridColumn187.FieldName = "BlockFinishedCount";
            this.gridColumn187.Name = "gridColumn187";
            this.gridColumn187.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn187.Visible = true;
            this.gridColumn187.VisibleIndex = 9;
            this.gridColumn187.Width = 47;
            // 
            // gridColumn188
            // 
            this.gridColumn188.Caption = "未接通已完成百分比(%)";
            this.gridColumn188.FieldName = "BlockFinishedPercent";
            this.gridColumn188.Name = "gridColumn188";
            this.gridColumn188.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn188.Visible = true;
            this.gridColumn188.VisibleIndex = 10;
            this.gridColumn188.Width = 47;
            // 
            // gridColumn189
            // 
            this.gridColumn189.Caption = "事件总数量";
            this.gridColumn189.FieldName = "EventCount";
            this.gridColumn189.Name = "gridColumn189";
            this.gridColumn189.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn189.Visible = true;
            this.gridColumn189.VisibleIndex = 11;
            this.gridColumn189.Width = 47;
            // 
            // gridColumn190
            // 
            this.gridColumn190.Caption = "事件未完成数量";
            this.gridColumn190.FieldName = "EventUnFinishedCount";
            this.gridColumn190.Name = "gridColumn190";
            this.gridColumn190.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn190.Visible = true;
            this.gridColumn190.VisibleIndex = 12;
            this.gridColumn190.Width = 47;
            // 
            // gridColumn191
            // 
            this.gridColumn191.Caption = "事件已完成数量";
            this.gridColumn191.FieldName = "EventFinishedCount";
            this.gridColumn191.Name = "gridColumn191";
            this.gridColumn191.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn191.Visible = true;
            this.gridColumn191.VisibleIndex = 13;
            this.gridColumn191.Width = 48;
            // 
            // gridColumn192
            // 
            this.gridColumn192.Caption = "事件已完成百分比(%)";
            this.gridColumn192.FieldName = "EventFinishedPercent";
            this.gridColumn192.Name = "gridColumn192";
            this.gridColumn192.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn192.Visible = true;
            this.gridColumn192.VisibleIndex = 14;
            this.gridColumn192.Width = 50;
            // 
            // gridView11
            // 
            this.gridView11.GridControl = this.gridControlLTEScanProfile;
            this.gridView11.Name = "gridView11";
            // 
            // tabControlChartLTEScanProfile
            // 
            this.tabControlChartLTEScanProfile.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartLTEScanProfile.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartLTEScanProfile.Name = "tabControlChartLTEScanProfile";
            this.tabControlChartLTEScanProfile.Size = new System.Drawing.Size(1025, 271);
            this.tabControlChartLTEScanProfile.TabIndex = 6;
            // 
            // tabLTEDrop
            // 
            this.tabLTEDrop.Controls.Add(this.splitContainerControl6);
            this.tabLTEDrop.Controls.Add(this.xtraTabControl8);
            this.tabLTEDrop.Location = new System.Drawing.Point(4, 23);
            this.tabLTEDrop.Name = "tabLTEDrop";
            this.tabLTEDrop.Size = new System.Drawing.Size(1025, 542);
            this.tabLTEDrop.TabIndex = 11;
            this.tabLTEDrop.Text = "LTE掉话原因分析";
            this.tabLTEDrop.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl6
            // 
            this.splitContainerControl6.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl6.Appearance.Options.UseForeColor = true;
            this.splitContainerControl6.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl6.Horizontal = false;
            this.splitContainerControl6.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl6.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl6.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl6.Name = "splitContainerControl6";
            this.splitContainerControl6.Panel1.Controls.Add(this.gridControlLTEDropReason);
            this.splitContainerControl6.Panel1.Text = "Panel1";
            this.splitContainerControl6.Panel2.Controls.Add(this.splitContainerControl7);
            this.splitContainerControl6.Panel2.Text = "Panel2";
            this.splitContainerControl6.Size = new System.Drawing.Size(1025, 542);
            this.splitContainerControl6.SplitterPosition = 232;
            this.splitContainerControl6.TabIndex = 9;
            this.splitContainerControl6.Text = "splitContainerControl6";
            // 
            // gridControlLTEDropReason
            // 
            this.gridControlLTEDropReason.ContextMenuStrip = this.ctxMenu;
            this.gridControlLTEDropReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlLTEDropReason.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlLTEDropReason.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlLTEDropReason.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlLTEDropReason.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlLTEDropReason.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlLTEDropReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlLTEDropReason.MainView = this.gridViewLTEDropReason;
            this.gridControlLTEDropReason.Name = "gridControlLTEDropReason";
            this.gridControlLTEDropReason.Size = new System.Drawing.Size(1025, 232);
            this.gridControlLTEDropReason.TabIndex = 5;
            this.gridControlLTEDropReason.UseEmbeddedNavigator = true;
            this.gridControlLTEDropReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewLTEDropReason,
            this.gridView20});
            // 
            // gridViewLTEDropReason
            // 
            this.gridViewLTEDropReason.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn217,
            this.gridColumn218,
            this.gridColumn219,
            this.gridColumn220,
            this.gridColumn221,
            this.gridColumn222});
            this.gridViewLTEDropReason.GridControl = this.gridControlLTEDropReason;
            this.gridViewLTEDropReason.Name = "gridViewLTEDropReason";
            this.gridViewLTEDropReason.OptionsBehavior.Editable = false;
            this.gridViewLTEDropReason.OptionsView.AllowCellMerge = true;
            this.gridViewLTEDropReason.OptionsView.ShowDetailButtons = false;
            this.gridViewLTEDropReason.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn217
            // 
            this.gridColumn217.Caption = "区域名称";
            this.gridColumn217.FieldName = "AreaName";
            this.gridColumn217.Name = "gridColumn217";
            this.gridColumn217.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn217.Visible = true;
            this.gridColumn217.VisibleIndex = 0;
            this.gridColumn217.Width = 140;
            // 
            // gridColumn218
            // 
            this.gridColumn218.Caption = "开始时间";
            this.gridColumn218.FieldName = "BeginTime";
            this.gridColumn218.Name = "gridColumn218";
            this.gridColumn218.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn218.Visible = true;
            this.gridColumn218.VisibleIndex = 1;
            this.gridColumn218.Width = 160;
            // 
            // gridColumn219
            // 
            this.gridColumn219.Caption = "结束时间";
            this.gridColumn219.FieldName = "EndTime";
            this.gridColumn219.Name = "gridColumn219";
            this.gridColumn219.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn219.Visible = true;
            this.gridColumn219.VisibleIndex = 2;
            this.gridColumn219.Width = 160;
            // 
            // gridColumn220
            // 
            this.gridColumn220.Caption = "原因";
            this.gridColumn220.FieldName = "ReasonName";
            this.gridColumn220.Name = "gridColumn220";
            this.gridColumn220.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn220.Visible = true;
            this.gridColumn220.VisibleIndex = 3;
            this.gridColumn220.Width = 160;
            // 
            // gridColumn221
            // 
            this.gridColumn221.Caption = "事件数量";
            this.gridColumn221.FieldName = "ReasonCount";
            this.gridColumn221.Name = "gridColumn221";
            this.gridColumn221.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn221.Visible = true;
            this.gridColumn221.VisibleIndex = 4;
            this.gridColumn221.Width = 160;
            // 
            // gridColumn222
            // 
            this.gridColumn222.Caption = "原因占比（%）";
            this.gridColumn222.FieldName = "ReasonPercent";
            this.gridColumn222.Name = "gridColumn222";
            this.gridColumn222.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn222.Visible = true;
            this.gridColumn222.VisibleIndex = 5;
            this.gridColumn222.Width = 160;
            // 
            // gridView20
            // 
            this.gridView20.GridControl = this.gridControlLTEDropReason;
            this.gridView20.Name = "gridView20";
            // 
            // splitContainerControl7
            // 
            this.splitContainerControl7.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl7.Appearance.Options.UseForeColor = true;
            this.splitContainerControl7.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl7.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl7.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl7.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl7.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl7.Name = "splitContainerControl7";
            this.splitContainerControl7.Panel1.Controls.Add(this.tabControlChartLTEDropReason);
            this.splitContainerControl7.Panel1.Text = "Panel1";
            this.splitContainerControl7.Panel2.Controls.Add(this.tabControlLTEDropDetail);
            this.splitContainerControl7.Panel2.Text = "Panel2";
            this.splitContainerControl7.Size = new System.Drawing.Size(1025, 306);
            this.splitContainerControl7.SplitterPosition = 522;
            this.splitContainerControl7.TabIndex = 9;
            this.splitContainerControl7.Text = "splitContainerControl7";
            // 
            // tabControlChartLTEDropReason
            // 
            this.tabControlChartLTEDropReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartLTEDropReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartLTEDropReason.Name = "tabControlChartLTEDropReason";
            this.tabControlChartLTEDropReason.Size = new System.Drawing.Size(522, 306);
            this.tabControlChartLTEDropReason.TabIndex = 6;
            // 
            // tabControlLTEDropDetail
            // 
            this.tabControlLTEDropDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlLTEDropDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlLTEDropDetail.Name = "tabControlLTEDropDetail";
            this.tabControlLTEDropDetail.Size = new System.Drawing.Size(499, 306);
            this.tabControlLTEDropDetail.TabIndex = 7;
            // 
            // xtraTabControl8
            // 
            this.xtraTabControl8.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl8.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl8.Name = "xtraTabControl8";
            this.xtraTabControl8.Size = new System.Drawing.Size(1025, 542);
            this.xtraTabControl8.TabIndex = 7;
            // 
            // tabLTEBlock
            // 
            this.tabLTEBlock.Controls.Add(this.splitContainerControl16);
            this.tabLTEBlock.Location = new System.Drawing.Point(4, 23);
            this.tabLTEBlock.Name = "tabLTEBlock";
            this.tabLTEBlock.Size = new System.Drawing.Size(1025, 542);
            this.tabLTEBlock.TabIndex = 13;
            this.tabLTEBlock.Text = "LTE未接通原因分析";
            this.tabLTEBlock.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl16
            // 
            this.splitContainerControl16.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl16.Appearance.Options.UseForeColor = true;
            this.splitContainerControl16.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl16.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl16.Horizontal = false;
            this.splitContainerControl16.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl16.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl16.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl16.Name = "splitContainerControl16";
            this.splitContainerControl16.Panel1.Controls.Add(this.gridControlLTEBlockReason);
            this.splitContainerControl16.Panel1.Text = "Panel1";
            this.splitContainerControl16.Panel2.Controls.Add(this.splitContainerControl17);
            this.splitContainerControl16.Panel2.Text = "Panel2";
            this.splitContainerControl16.Size = new System.Drawing.Size(1025, 542);
            this.splitContainerControl16.SplitterPosition = 246;
            this.splitContainerControl16.TabIndex = 9;
            this.splitContainerControl16.Text = "splitContainerControl16";
            // 
            // gridControlLTEBlockReason
            // 
            this.gridControlLTEBlockReason.ContextMenuStrip = this.ctxMenu;
            this.gridControlLTEBlockReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlLTEBlockReason.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlLTEBlockReason.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlLTEBlockReason.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlLTEBlockReason.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlLTEBlockReason.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlLTEBlockReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlLTEBlockReason.MainView = this.gridViewLTEBlockReason;
            this.gridControlLTEBlockReason.Name = "gridControlLTEBlockReason";
            this.gridControlLTEBlockReason.Size = new System.Drawing.Size(1025, 246);
            this.gridControlLTEBlockReason.TabIndex = 5;
            this.gridControlLTEBlockReason.UseEmbeddedNavigator = true;
            this.gridControlLTEBlockReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewLTEBlockReason});
            // 
            // gridViewLTEBlockReason
            // 
            this.gridViewLTEBlockReason.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn229,
            this.gridColumn230,
            this.gridColumn231,
            this.gridColumn232,
            this.gridColumn233,
            this.gridColumn234});
            this.gridViewLTEBlockReason.GridControl = this.gridControlLTEBlockReason;
            this.gridViewLTEBlockReason.Name = "gridViewLTEBlockReason";
            this.gridViewLTEBlockReason.OptionsBehavior.Editable = false;
            this.gridViewLTEBlockReason.OptionsView.AllowCellMerge = true;
            this.gridViewLTEBlockReason.OptionsView.ShowDetailButtons = false;
            this.gridViewLTEBlockReason.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn229
            // 
            this.gridColumn229.Caption = "区域名称";
            this.gridColumn229.FieldName = "AreaName";
            this.gridColumn229.Name = "gridColumn229";
            this.gridColumn229.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn229.Visible = true;
            this.gridColumn229.VisibleIndex = 0;
            this.gridColumn229.Width = 140;
            // 
            // gridColumn230
            // 
            this.gridColumn230.Caption = "开始时间";
            this.gridColumn230.FieldName = "BeginTime";
            this.gridColumn230.Name = "gridColumn230";
            this.gridColumn230.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn230.Visible = true;
            this.gridColumn230.VisibleIndex = 1;
            this.gridColumn230.Width = 160;
            // 
            // gridColumn231
            // 
            this.gridColumn231.Caption = "结束时间";
            this.gridColumn231.FieldName = "EndTime";
            this.gridColumn231.Name = "gridColumn231";
            this.gridColumn231.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn231.Visible = true;
            this.gridColumn231.VisibleIndex = 2;
            this.gridColumn231.Width = 160;
            // 
            // gridColumn232
            // 
            this.gridColumn232.Caption = "原因";
            this.gridColumn232.FieldName = "ReasonName";
            this.gridColumn232.Name = "gridColumn232";
            this.gridColumn232.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn232.Visible = true;
            this.gridColumn232.VisibleIndex = 3;
            this.gridColumn232.Width = 160;
            // 
            // gridColumn233
            // 
            this.gridColumn233.Caption = "事件数量";
            this.gridColumn233.FieldName = "ReasonCount";
            this.gridColumn233.Name = "gridColumn233";
            this.gridColumn233.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn233.Visible = true;
            this.gridColumn233.VisibleIndex = 4;
            this.gridColumn233.Width = 160;
            // 
            // gridColumn234
            // 
            this.gridColumn234.Caption = "原因占比（%）";
            this.gridColumn234.FieldName = "ReasonPercent";
            this.gridColumn234.Name = "gridColumn234";
            this.gridColumn234.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn234.Visible = true;
            this.gridColumn234.VisibleIndex = 5;
            this.gridColumn234.Width = 160;
            // 
            // splitContainerControl17
            // 
            this.splitContainerControl17.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl17.Appearance.Options.UseForeColor = true;
            this.splitContainerControl17.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl17.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl17.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl17.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl17.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl17.Name = "splitContainerControl17";
            this.splitContainerControl17.Panel1.Controls.Add(this.tabControlChartLTEBlockReason);
            this.splitContainerControl17.Panel1.Text = "Panel1";
            this.splitContainerControl17.Panel2.Controls.Add(this.tabControlLTEBlockDetail);
            this.splitContainerControl17.Panel2.Text = "Panel2";
            this.splitContainerControl17.Size = new System.Drawing.Size(1025, 292);
            this.splitContainerControl17.SplitterPosition = 522;
            this.splitContainerControl17.TabIndex = 9;
            this.splitContainerControl17.Text = "splitContainerControl17";
            // 
            // tabControlChartLTEBlockReason
            // 
            this.tabControlChartLTEBlockReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartLTEBlockReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartLTEBlockReason.Name = "tabControlChartLTEBlockReason";
            this.tabControlChartLTEBlockReason.Size = new System.Drawing.Size(522, 292);
            this.tabControlChartLTEBlockReason.TabIndex = 6;
            // 
            // tabControlLTEBlockDetail
            // 
            this.tabControlLTEBlockDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlLTEBlockDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlLTEBlockDetail.Name = "tabControlLTEBlockDetail";
            this.tabControlLTEBlockDetail.Size = new System.Drawing.Size(499, 292);
            this.tabControlLTEBlockDetail.TabIndex = 7;
            // 
            // tabLTEScanDrop
            // 
            this.tabLTEScanDrop.Controls.Add(this.splitContainerControl14);
            this.tabLTEScanDrop.Location = new System.Drawing.Point(4, 23);
            this.tabLTEScanDrop.Name = "tabLTEScanDrop";
            this.tabLTEScanDrop.Size = new System.Drawing.Size(1025, 542);
            this.tabLTEScanDrop.TabIndex = 12;
            this.tabLTEScanDrop.Text = "LTEScan掉话原因分析";
            this.tabLTEScanDrop.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl14
            // 
            this.splitContainerControl14.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl14.Appearance.Options.UseForeColor = true;
            this.splitContainerControl14.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl14.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl14.Horizontal = false;
            this.splitContainerControl14.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl14.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl14.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl14.Name = "splitContainerControl14";
            this.splitContainerControl14.Panel1.Controls.Add(this.gridControlLTEScanDropReason);
            this.splitContainerControl14.Panel1.Text = "Panel1";
            this.splitContainerControl14.Panel2.Controls.Add(this.splitContainerControl15);
            this.splitContainerControl14.Panel2.Text = "Panel2";
            this.splitContainerControl14.Size = new System.Drawing.Size(1025, 542);
            this.splitContainerControl14.SplitterPosition = 232;
            this.splitContainerControl14.TabIndex = 10;
            this.splitContainerControl14.Text = "splitContainerControl14";
            // 
            // gridControlLTEScanDropReason
            // 
            this.gridControlLTEScanDropReason.ContextMenuStrip = this.ctxMenu;
            this.gridControlLTEScanDropReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlLTEScanDropReason.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlLTEScanDropReason.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlLTEScanDropReason.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlLTEScanDropReason.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlLTEScanDropReason.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlLTEScanDropReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlLTEScanDropReason.MainView = this.gridViewLTEScanDropReason;
            this.gridControlLTEScanDropReason.Name = "gridControlLTEScanDropReason";
            this.gridControlLTEScanDropReason.Size = new System.Drawing.Size(1025, 232);
            this.gridControlLTEScanDropReason.TabIndex = 5;
            this.gridControlLTEScanDropReason.UseEmbeddedNavigator = true;
            this.gridControlLTEScanDropReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewLTEScanDropReason,
            this.gridView22});
            // 
            // gridViewLTEScanDropReason
            // 
            this.gridViewLTEScanDropReason.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn223,
            this.gridColumn224,
            this.gridColumn225,
            this.gridColumn226,
            this.gridColumn227,
            this.gridColumn228});
            this.gridViewLTEScanDropReason.GridControl = this.gridControlLTEScanDropReason;
            this.gridViewLTEScanDropReason.Name = "gridViewLTEScanDropReason";
            this.gridViewLTEScanDropReason.OptionsBehavior.Editable = false;
            this.gridViewLTEScanDropReason.OptionsView.AllowCellMerge = true;
            this.gridViewLTEScanDropReason.OptionsView.ShowDetailButtons = false;
            this.gridViewLTEScanDropReason.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn223
            // 
            this.gridColumn223.Caption = "区域名称";
            this.gridColumn223.FieldName = "AreaName";
            this.gridColumn223.Name = "gridColumn223";
            this.gridColumn223.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn223.Visible = true;
            this.gridColumn223.VisibleIndex = 0;
            this.gridColumn223.Width = 140;
            // 
            // gridColumn224
            // 
            this.gridColumn224.Caption = "开始时间";
            this.gridColumn224.FieldName = "BeginTime";
            this.gridColumn224.Name = "gridColumn224";
            this.gridColumn224.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn224.Visible = true;
            this.gridColumn224.VisibleIndex = 1;
            this.gridColumn224.Width = 160;
            // 
            // gridColumn225
            // 
            this.gridColumn225.Caption = "结束时间";
            this.gridColumn225.FieldName = "EndTime";
            this.gridColumn225.Name = "gridColumn225";
            this.gridColumn225.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn225.Visible = true;
            this.gridColumn225.VisibleIndex = 2;
            this.gridColumn225.Width = 160;
            // 
            // gridColumn226
            // 
            this.gridColumn226.Caption = "原因";
            this.gridColumn226.FieldName = "ReasonName";
            this.gridColumn226.Name = "gridColumn226";
            this.gridColumn226.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn226.Visible = true;
            this.gridColumn226.VisibleIndex = 3;
            this.gridColumn226.Width = 160;
            // 
            // gridColumn227
            // 
            this.gridColumn227.Caption = "事件数量";
            this.gridColumn227.FieldName = "ReasonCount";
            this.gridColumn227.Name = "gridColumn227";
            this.gridColumn227.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn227.Visible = true;
            this.gridColumn227.VisibleIndex = 4;
            this.gridColumn227.Width = 160;
            // 
            // gridColumn228
            // 
            this.gridColumn228.Caption = "原因占比（%）";
            this.gridColumn228.FieldName = "ReasonPercent";
            this.gridColumn228.Name = "gridColumn228";
            this.gridColumn228.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn228.Visible = true;
            this.gridColumn228.VisibleIndex = 5;
            this.gridColumn228.Width = 160;
            // 
            // gridView22
            // 
            this.gridView22.GridControl = this.gridControlLTEScanDropReason;
            this.gridView22.Name = "gridView22";
            // 
            // splitContainerControl15
            // 
            this.splitContainerControl15.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl15.Appearance.Options.UseForeColor = true;
            this.splitContainerControl15.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl15.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl15.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl15.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl15.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl15.Name = "splitContainerControl15";
            this.splitContainerControl15.Panel1.Controls.Add(this.tabControlChartLTEScanDropReason);
            this.splitContainerControl15.Panel1.Text = "Panel1";
            this.splitContainerControl15.Panel2.Controls.Add(this.tabControlLTEScanDropDetail);
            this.splitContainerControl15.Panel2.Text = "Panel2";
            this.splitContainerControl15.Size = new System.Drawing.Size(1025, 306);
            this.splitContainerControl15.SplitterPosition = 522;
            this.splitContainerControl15.TabIndex = 9;
            this.splitContainerControl15.Text = "splitContainerControl15";
            // 
            // tabControlChartLTEScanDropReason
            // 
            this.tabControlChartLTEScanDropReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartLTEScanDropReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartLTEScanDropReason.Name = "tabControlChartLTEScanDropReason";
            this.tabControlChartLTEScanDropReason.Size = new System.Drawing.Size(522, 306);
            this.tabControlChartLTEScanDropReason.TabIndex = 6;
            // 
            // tabControlLTEScanDropDetail
            // 
            this.tabControlLTEScanDropDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlLTEScanDropDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlLTEScanDropDetail.Name = "tabControlLTEScanDropDetail";
            this.tabControlLTEScanDropDetail.Size = new System.Drawing.Size(499, 306);
            this.tabControlLTEScanDropDetail.TabIndex = 7;
            // 
            // tabLTEScanBlock
            // 
            this.tabLTEScanBlock.Controls.Add(this.splitContainerControl18);
            this.tabLTEScanBlock.Location = new System.Drawing.Point(4, 23);
            this.tabLTEScanBlock.Name = "tabLTEScanBlock";
            this.tabLTEScanBlock.Size = new System.Drawing.Size(1025, 542);
            this.tabLTEScanBlock.TabIndex = 14;
            this.tabLTEScanBlock.Text = "LTEScan未接通原因分析";
            this.tabLTEScanBlock.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl18
            // 
            this.splitContainerControl18.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl18.Appearance.Options.UseForeColor = true;
            this.splitContainerControl18.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl18.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl18.Horizontal = false;
            this.splitContainerControl18.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl18.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl18.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl18.Name = "splitContainerControl18";
            this.splitContainerControl18.Panel1.Controls.Add(this.gridControlLTEScanBlockReason);
            this.splitContainerControl18.Panel1.Text = "Panel1";
            this.splitContainerControl18.Panel2.Controls.Add(this.splitContainerControl19);
            this.splitContainerControl18.Panel2.Text = "Panel2";
            this.splitContainerControl18.Size = new System.Drawing.Size(1025, 542);
            this.splitContainerControl18.SplitterPosition = 246;
            this.splitContainerControl18.TabIndex = 10;
            this.splitContainerControl18.Text = "splitContainerControl18";
            // 
            // gridControlLTEScanBlockReason
            // 
            this.gridControlLTEScanBlockReason.ContextMenuStrip = this.ctxMenu;
            this.gridControlLTEScanBlockReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlLTEScanBlockReason.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlLTEScanBlockReason.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlLTEScanBlockReason.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlLTEScanBlockReason.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlLTEScanBlockReason.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlLTEScanBlockReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlLTEScanBlockReason.MainView = this.gridViewLTEScanBlockReason;
            this.gridControlLTEScanBlockReason.Name = "gridControlLTEScanBlockReason";
            this.gridControlLTEScanBlockReason.Size = new System.Drawing.Size(1025, 246);
            this.gridControlLTEScanBlockReason.TabIndex = 5;
            this.gridControlLTEScanBlockReason.UseEmbeddedNavigator = true;
            this.gridControlLTEScanBlockReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewLTEScanBlockReason});
            // 
            // gridViewLTEScanBlockReason
            // 
            this.gridViewLTEScanBlockReason.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn235,
            this.gridColumn236,
            this.gridColumn237,
            this.gridColumn238,
            this.gridColumn239,
            this.gridColumn240});
            this.gridViewLTEScanBlockReason.GridControl = this.gridControlLTEScanBlockReason;
            this.gridViewLTEScanBlockReason.Name = "gridViewLTEScanBlockReason";
            this.gridViewLTEScanBlockReason.OptionsBehavior.Editable = false;
            this.gridViewLTEScanBlockReason.OptionsView.AllowCellMerge = true;
            this.gridViewLTEScanBlockReason.OptionsView.ShowDetailButtons = false;
            this.gridViewLTEScanBlockReason.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn235
            // 
            this.gridColumn235.Caption = "区域名称";
            this.gridColumn235.FieldName = "AreaName";
            this.gridColumn235.Name = "gridColumn235";
            this.gridColumn235.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn235.Visible = true;
            this.gridColumn235.VisibleIndex = 0;
            this.gridColumn235.Width = 140;
            // 
            // gridColumn236
            // 
            this.gridColumn236.Caption = "开始时间";
            this.gridColumn236.FieldName = "BeginTime";
            this.gridColumn236.Name = "gridColumn236";
            this.gridColumn236.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn236.Visible = true;
            this.gridColumn236.VisibleIndex = 1;
            this.gridColumn236.Width = 160;
            // 
            // gridColumn237
            // 
            this.gridColumn237.Caption = "结束时间";
            this.gridColumn237.FieldName = "EndTime";
            this.gridColumn237.Name = "gridColumn237";
            this.gridColumn237.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn237.Visible = true;
            this.gridColumn237.VisibleIndex = 2;
            this.gridColumn237.Width = 160;
            // 
            // gridColumn238
            // 
            this.gridColumn238.Caption = "原因";
            this.gridColumn238.FieldName = "ReasonName";
            this.gridColumn238.Name = "gridColumn238";
            this.gridColumn238.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn238.Visible = true;
            this.gridColumn238.VisibleIndex = 3;
            this.gridColumn238.Width = 160;
            // 
            // gridColumn239
            // 
            this.gridColumn239.Caption = "事件数量";
            this.gridColumn239.FieldName = "ReasonCount";
            this.gridColumn239.Name = "gridColumn239";
            this.gridColumn239.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn239.Visible = true;
            this.gridColumn239.VisibleIndex = 4;
            this.gridColumn239.Width = 160;
            // 
            // gridColumn240
            // 
            this.gridColumn240.Caption = "原因占比（%）";
            this.gridColumn240.FieldName = "ReasonPercent";
            this.gridColumn240.Name = "gridColumn240";
            this.gridColumn240.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn240.Visible = true;
            this.gridColumn240.VisibleIndex = 5;
            this.gridColumn240.Width = 160;
            // 
            // splitContainerControl19
            // 
            this.splitContainerControl19.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl19.Appearance.Options.UseForeColor = true;
            this.splitContainerControl19.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl19.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl19.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl19.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl19.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl19.Name = "splitContainerControl19";
            this.splitContainerControl19.Panel1.Controls.Add(this.tabControlChartLTEScanBlockReason);
            this.splitContainerControl19.Panel1.Text = "Panel1";
            this.splitContainerControl19.Panel2.Controls.Add(this.tabControlLTEScanBlockDetail);
            this.splitContainerControl19.Panel2.Text = "Panel2";
            this.splitContainerControl19.Size = new System.Drawing.Size(1025, 292);
            this.splitContainerControl19.SplitterPosition = 522;
            this.splitContainerControl19.TabIndex = 9;
            this.splitContainerControl19.Text = "splitContainerControl19";
            // 
            // tabControlChartLTEScanBlockReason
            // 
            this.tabControlChartLTEScanBlockReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlChartLTEScanBlockReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartLTEScanBlockReason.Name = "tabControlChartLTEScanBlockReason";
            this.tabControlChartLTEScanBlockReason.Size = new System.Drawing.Size(522, 292);
            this.tabControlChartLTEScanBlockReason.TabIndex = 6;
            // 
            // tabControlLTEScanBlockDetail
            // 
            this.tabControlLTEScanBlockDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlLTEScanBlockDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlLTEScanBlockDetail.Name = "tabControlLTEScanBlockDetail";
            this.tabControlLTEScanBlockDetail.Size = new System.Drawing.Size(499, 292);
            this.tabControlLTEScanBlockDetail.TabIndex = 7;
            // 
            // ZTReportEventStatListFormMulti_QH
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1033, 569);
            this.Controls.Add(this.tabControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTReportEventStatListFormMulti_QH";
            this.Text = "异常事件统计";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl11)).EndInit();
            this.tabTDBlock.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl12)).EndInit();
            this.splitContainerControl12.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl13)).EndInit();
            this.splitContainerControl13.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlTDBlockDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSMBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSMBlockReason)).EndInit();
            this.tabTDDrop.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl10)).EndInit();
            this.splitContainerControl10.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl11)).EndInit();
            this.splitContainerControl11.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlTDDropDetail)).EndInit();
            this.tabGSMBlock.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).EndInit();
            this.splitContainerControl5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl9)).EndInit();
            this.splitContainerControl9.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartGSMBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlGSMBlockDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView16)).EndInit();
            this.tabGSMDrop.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).EndInit();
            this.splitContainerControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSMDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSMDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl8)).EndInit();
            this.splitContainerControl8.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartGSMDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlGSMDropDetail)).EndInit();
            this.tabTDProfile.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).EndInit();
            this.splitContainerControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDProfile)).EndInit();
            this.tabGSMProfile.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSMProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSMProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartGSMProfile)).EndInit();
            this.tabControl1.ResumeLayout(false);
            this.tabLTEProfile.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl20)).EndInit();
            this.splitContainerControl20.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEProfile)).EndInit();
            this.tabLTEScanProfile.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl21)).EndInit();
            this.splitContainerControl21.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEScanProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEScanProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEScanProfile)).EndInit();
            this.tabLTEDrop.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl6)).EndInit();
            this.splitContainerControl6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl7)).EndInit();
            this.splitContainerControl7.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlLTEDropDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl8)).EndInit();
            this.tabLTEBlock.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl16)).EndInit();
            this.splitContainerControl16.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl17)).EndInit();
            this.splitContainerControl17.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlLTEBlockDetail)).EndInit();
            this.tabLTEScanDrop.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl14)).EndInit();
            this.splitContainerControl14.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEScanDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEScanDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl15)).EndInit();
            this.splitContainerControl15.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEScanDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlLTEScanDropDetail)).EndInit();
            this.tabLTEScanBlock.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl18)).EndInit();
            this.splitContainerControl18.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLTEScanBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTEScanBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl19)).EndInit();
            this.splitContainerControl19.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartLTEScanBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlLTEScanBlockDetail)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.GridControl gridControl2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn59;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn64;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl2;
        private DevExpress.XtraGrid.GridControl gridControl3;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn65;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn66;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn67;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn68;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn69;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn70;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn71;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn72;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn73;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn74;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn75;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn76;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn77;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn78;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn79;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn80;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl3;
        private DevExpress.XtraGrid.GridControl gridControl4;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn81;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn82;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn83;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn84;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn85;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn86;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn87;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn88;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn89;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn90;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn91;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn92;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn93;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn94;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn95;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn96;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl4;
        private DevExpress.XtraGrid.GridControl gridControl5;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn97;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn98;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn99;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn100;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn101;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn102;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn103;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn104;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn105;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn106;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn107;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn108;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn109;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn110;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn111;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn112;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl5;
        private DevExpress.XtraGrid.GridControl gridControl6;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn113;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn114;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn115;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn116;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn117;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn118;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn119;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn120;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn121;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn122;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn123;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn124;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn125;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn126;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn127;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn128;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl6;
        private DevExpress.XtraGrid.GridControl gridControl7;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn135;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn136;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn137;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn138;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn139;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn140;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn141;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn142;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn143;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn144;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn193;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn194;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn195;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn196;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn197;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn198;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl7;
        private DevExpress.XtraGrid.GridControl gridControl11;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn199;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn200;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn201;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn202;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn203;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn204;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn205;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn206;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn207;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn208;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn209;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn210;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn211;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn212;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn213;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn214;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl11;
        private System.Windows.Forms.TabPage tabTDBlock;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl12;
        private DevExpress.XtraGrid.GridControl gridControlTDBlockReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTDBlockReason;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn157;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn158;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn159;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn160;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn215;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn216;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl13;
        private DevExpress.XtraTab.XtraTabControl tabControlChartTDBlockReason;
        private DevExpress.XtraTab.XtraTabControl tabControlTDBlockDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGSMBlockReason;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn145;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn146;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn147;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn148;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn149;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn150;
        private DevExpress.XtraGrid.GridControl gridControlGSMBlockReason;
        private System.Windows.Forms.TabPage tabTDDrop;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl10;
        private DevExpress.XtraGrid.GridControl gridControlTDDropReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTDDropReason;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn151;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn152;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn153;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn154;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn155;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn156;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl11;
        private DevExpress.XtraTab.XtraTabControl tabControlChartTDDropReason;
        private DevExpress.XtraTab.XtraTabControl tabControlTDDropDetail;
        private System.Windows.Forms.TabPage tabGSMBlock;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl5;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl9;
        private DevExpress.XtraTab.XtraTabControl tabControlChartGSMBlockReason;
        private DevExpress.XtraTab.XtraTabControl tabControlGSMBlockDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView15;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView16;
        private System.Windows.Forms.TabPage tabGSMDrop;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl4;
        private DevExpress.XtraGrid.GridControl gridControlGSMDropReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGSMDropReason;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn129;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn131;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn132;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn130;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn133;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn134;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl8;
        private DevExpress.XtraTab.XtraTabControl tabControlChartGSMDropReason;
        private DevExpress.XtraTab.XtraTabControl tabControlGSMDropDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView17;
        private System.Windows.Forms.TabPage tabTDProfile;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl3;
        private DevExpress.XtraGrid.GridControl gridControlTDProfile;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTDProfile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraTab.XtraTabControl tabControlChartTDProfile;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView18;
        private System.Windows.Forms.TabPage tabGSMProfile;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridControlGSMProfile;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGSMProfile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBeginTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEndTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDropCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDropUnFinishedCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDropFinishedCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDropFinishedPercent;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBlockCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBlockUnFinishedCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBlockFinishedCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBlockFinishedPercent;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEventCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEventUnFinishedCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEventFinishedCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEventFinishedPercent;
        private DevExpress.XtraTab.XtraTabControl tabControlChartGSMProfile;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView19;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabLTEProfile;
        private System.Windows.Forms.TabPage tabLTEScanProfile;
        private System.Windows.Forms.TabPage tabLTEBlock;
        private System.Windows.Forms.TabPage tabLTEScanDrop;
        private System.Windows.Forms.TabPage tabLTEScanBlock;
        private System.Windows.Forms.TabPage tabLTEDrop;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl6;
        private DevExpress.XtraGrid.GridControl gridControlLTEDropReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewLTEDropReason;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn217;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn218;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn219;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn220;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn221;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn222;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView20;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl7;
        private DevExpress.XtraTab.XtraTabControl tabControlChartLTEDropReason;
        private DevExpress.XtraTab.XtraTabControl tabControlLTEDropDetail;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl8;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl14;
        private DevExpress.XtraGrid.GridControl gridControlLTEScanDropReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewLTEScanDropReason;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn223;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn224;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn225;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn226;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn227;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn228;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView22;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl15;
        private DevExpress.XtraTab.XtraTabControl tabControlChartLTEScanDropReason;
        private DevExpress.XtraTab.XtraTabControl tabControlLTEScanDropDetail;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl16;
        private DevExpress.XtraGrid.GridControl gridControlLTEBlockReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewLTEBlockReason;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn229;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn230;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn231;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn232;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn233;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn234;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl17;
        private DevExpress.XtraTab.XtraTabControl tabControlChartLTEBlockReason;
        private DevExpress.XtraTab.XtraTabControl tabControlLTEBlockDetail;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl18;
        private DevExpress.XtraGrid.GridControl gridControlLTEScanBlockReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewLTEScanBlockReason;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn235;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn236;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn237;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn238;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn239;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn240;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl19;
        private DevExpress.XtraTab.XtraTabControl tabControlChartLTEScanBlockReason;
        private DevExpress.XtraTab.XtraTabControl tabControlLTEScanBlockDetail;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl20;
        private DevExpress.XtraGrid.GridControl gridControlLTEProfile;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewLTEProfile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn161;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn163;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn164;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn165;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn166;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn167;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn168;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn169;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn170;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn171;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn172;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn173;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn174;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn175;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn176;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView10;
        private DevExpress.XtraTab.XtraTabControl tabControlChartLTEProfile;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl21;
        private DevExpress.XtraGrid.GridControl gridControlLTEScanProfile;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewLTEScanProfile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn177;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn179;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn180;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn181;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn182;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn183;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn184;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn185;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn186;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn187;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn188;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn189;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn190;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn191;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn192;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView11;
        private DevExpress.XtraTab.XtraTabControl tabControlChartLTEScanProfile;

    }
}