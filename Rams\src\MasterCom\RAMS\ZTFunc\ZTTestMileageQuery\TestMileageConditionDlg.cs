﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TestMileageConditionDlg : BaseDialog
    {
        TestMileageType testType;
        public TestMileageConditionDlg(TestMileageType testType)
            : base()
        {
            InitializeComponent();
            this.testType = testType;
            if (testType == TestMileageType.GridTest)
            {
                this.Text = "网格测试里程条件设置";
                labelTestType.Text = "地市名称：";
                comboBoxGridName.Visible = true;
                textBoxMainLine.Visible = false;
                comboBoxGridName.Items.Clear();
                foreach (string str in MasterCom.RAMS.Model.DistrictManager.GetInstance().DistrictNames)
                {
                    if (!string.IsNullOrEmpty(str))
                    {
                        comboBoxGridName.Items.Add(str);
                    }
                }
            }
            else
            {
                this.Text = "交通干线测试里程条件设置";
                labelTestType.Text = "交通干线：";
                comboBoxGridName.Visible = false;
                textBoxMainLine.Visible = true;
            }
        }

        public void SetCondition(int year, string testTypeDes)
        {
            numYear.Value = (decimal)year;
            if (testType == TestMileageType.GridTest)
            {
                if (string.IsNullOrEmpty(testTypeDes) && comboBoxGridName.Items.Count > 0)
                {
                    comboBoxGridName.SelectedIndex = 0;
                }
                else
                {
                    comboBoxGridName.Text = testTypeDes;
                }
            }
            else
            {
                textBoxMainLine.Text = testTypeDes;
            }
        }
        public void GetCondition(out int year, out string testTypeDes)
        {
            year = (int)numYear.Value;
            if (testType == TestMileageType.GridTest)
            {
                testTypeDes = comboBoxGridName.Text;
                if (testTypeDes == "山东")
                {
                    testTypeDes = "";
                }
            }
            else
            {
                testTypeDes = textBoxMainLine.Text;
            }
        }

    }
}
