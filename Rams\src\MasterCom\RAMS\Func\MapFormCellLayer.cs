﻿using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.Func
{
    public class MapCellLayer : LayerBase, IKMLExport
    {
        /// <summary>
        /// 显示最新工参
        /// </summary>
        public static bool DrawCurrent { get; set; } = true;

        /// <summary>
        /// 当前显示工参的时间点。当DrawCurrent为false时，显示该时间点的工参。可在图层控制里面选择时间
        /// </summary>
        public static DateTime CurShowTimeAt { get; set; } = DateTime.Now;

        #region 绘制小区,天线样式
        /// <summary>
        /// 静态构造方法，创建好各个图元的Path
        /// </summary>
        static MapCellLayer()
        {
            creatGraphicsPath();
        }
        
        private static List<GraphicsPath> cellPaths = new List<GraphicsPath>();
        public List<GraphicsPath> CellPaths
        {
            get { return cellPaths; }
        }

        private static List<PointF[]> antennaPoints = new List<PointF[]>();
        public List<PointF[]> AntennaPoints
        {
            get { return antennaPoints; }
        }

        private static List<LayerPoint> antennaGEPoints = new List<LayerPoint>();
        public List<LayerPoint> AntennaGEPoints
        {
            get { return antennaGEPoints; }
        }

        private static List<GraphicsPath> antennaPaths = new List<GraphicsPath>();
        public List<GraphicsPath> AntennaPaths
        {
            get { return antennaPaths; }
        }

        /// <summary>
        /// 小区覆盖渐变画刷
        /// </summary>
        private static List<PathGradientBrush> cellPathGradientBrushs = new List<PathGradientBrush>();
        public List<PathGradientBrush> CellPathGradientBrushs
        {
            get { return cellPathGradientBrushs; }
        }

        /// <summary>
        /// 组成小区形状的点数组
        /// </summary>
        private static List<PointF[]> cellPoints = new List<PointF[]>();
        public List<PointF[]> CellPoints
        {
            get { return cellPoints; }
        }

        /// <summary>
        /// 绘制小区天线的样式
        /// </summary>
        protected static void creatGraphicsPath()
        {
            add900CellPoint();
            add1800CellPoint();
            addCoSiteCellPoint();
            addPerformanceCellPoint();
            addCellPath();
            foreach (GraphicsPath pathTemp in cellPaths)
            {
                PathGradientBrush pathGradientBrush = new PathGradientBrush(pathTemp);
                pathGradientBrush.CenterPoint = new PointF(0, 0);
                cellPathGradientBrushs.Add(pathGradientBrush);
            }
            addAntennaPoint();
            addAntennaPath();
            addAntennaGEPoint();
        }

        private static void add900CellPoint()
        {
            //默认的，900的小区比1800的大一点
            float radius = 6;//该radius对应cell.EndPointLongitude里半径的参数
            //900室内小区形状，对应Path index 为0
            cellPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 36;
            float x1 = 0.75f;
            float y1 = 0.14f;
            float x2 = 0.85f;
            float y2 = 0.15f;
            float x3 = 0.92f;
            float y3 = 0.14f;
            float x4 = 0.96f;
            float y4 = 0.12f;
            float x5 = 0.99f;
            float y5 = 0.07f;
            //900室外小区形状，对应Path index 为1，
            cellPoints.Add(new PointF[]
                {/* 以下点的坐标顺序为900室外小区的纺锤状*/
                    new PointF(0, 0),
                    new PointF(radius * x1, -radius * y1),
                    new PointF(radius * x2, -radius * y2),
                    new PointF(radius * x3, -radius * y3),
                    new PointF(radius * x4, -radius * y4),
                    new PointF(radius * x5, -radius * y5),
                    new PointF(radius, 0),
                    new PointF(radius * x5, radius * y5),
                    new PointF(radius * x4, radius * y4),
                    new PointF(radius * x3, radius * y3),
                    new PointF(radius * x2, radius * y2),
                    new PointF(radius * x1, radius * y1)
                });
        }

        private static void add1800CellPoint()
        {
            float radius = 2;
            //1800室内小区形状，对应Path index 为4
            cellPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 22;
            float x1 = 0.75f;
            float y1 = 0.29f;
            float x2 = 0.85f;
            float y2 = 0.30f;
            float x3 = 0.92f;
            float y3 = 0.28f;
            float x4 = 0.96f;
            float y4 = 0.24f;
            float x5 = 0.99f;
            float y5 = 0.14f;
            //1800室外小区形状，对应Path index 为3
            cellPoints.Add(new PointF[]
                {/* 以下点的坐标顺序为1800室外小区的纺锤状*/
                    new PointF(0, 0),
                    new PointF(radius * x1, -radius * y1),
                    new PointF(radius * x2, -radius * y2),
                    new PointF(radius * x3, -radius * y3),
                    new PointF(radius * x4, -radius * y4),
                    new PointF(radius * x5, -radius * y5),
                    new PointF(radius, 0),
                    new PointF(radius * x5, radius * y5),
                    new PointF(radius * x4, radius * y4),
                    new PointF(radius * x3, radius * y3),
                    new PointF(radius * x2, radius * y2),
                    new PointF(radius * x1, radius * y1)
                });
        }

        private static void addCoSiteCellPoint()
        {
            float radius = 30;
            float x1 = 0.75f;
            float y1 = 0.29f;
            //共站室外小区形状，对应Path index 为4
            cellPoints.Add(new PointF[]
                {/* 以下点的坐标顺序为共站室外小区的纺锤状*/
                    new PointF(0, 0),
                    new PointF(radius * x1, -radius * y1),
                    new PointF(radius, 0),
                    new PointF(radius * x1, radius * y1)
                });
        }

        private static void addPerformanceCellPoint()
        {
            //性能问题查询  by小雷
            float radius = 36;
            float x1 = 0.55f;
            float y1 = 0.64f;
            float x2 = 0.85f;
            float y2 = 0.15f;
            float x3 = 0.92f;
            float y3 = 0.14f;
            float x4 = 0.96f;
            float y4 = 0.12f;
            float x5 = 0.99f;
            float y5 = 0.07f;
            cellPoints.Add(new PointF[]
                {
                    new PointF(0, 0),
                    new PointF(radius * x1, -radius * y1),
                    new PointF(radius * x2, -radius * y2),
                    new PointF(radius * x3, -radius * y3),
                    new PointF(radius * x4, -radius * y4),
                    new PointF(radius * x5, -radius * y5),
                    new PointF(radius, 0),
                    new PointF(radius * x5, radius * y5),
                    new PointF(radius * x4, radius * y4),
                    new PointF(radius * x3, radius * y3),
                    new PointF(radius * x2, radius * y2),
                    new PointF(radius * x1, radius * y1)
                });
        }

        private static void addCellPath()
        {
            //由点按顺序组成一个GraphicsPath，画图元的时候按规则放大缩小，填充对应的GraphicsPath就OK了
            GraphicsPath path = new GraphicsPath();
            path.AddEllipse(cellPoints[0][0].X, cellPoints[0][0].Y, cellPoints[0][2].X * 2, cellPoints[0][2].Y * 2);
            cellPaths.Add(path);//900室内小区，圆形

            path = new GraphicsPath();
            path.AddPolygon(cellPoints[1]);
            cellPaths.Add(path);//900室外小区，纺锤形
            path = new GraphicsPath();

            path.AddEllipse(cellPoints[2][0].X, cellPoints[2][0].Y, cellPoints[2][2].X * 2, cellPoints[2][2].Y * 2);
            cellPaths.Add(path);//1800室内小区，圆形

            path = new GraphicsPath();
            path.AddPolygon(cellPoints[3]);
            cellPaths.Add(path);//1800室外小区，纺锤形

            path = new GraphicsPath();
            path.AddPolygon(cellPoints[4]);
            cellPaths.Add(path);//共站室外小区，纺锤形

            path = new GraphicsPath();
            path.AddPolygon(cellPoints[5]);   //by 小雷
            cellPaths.Add(path);
        }

        private static void addAntennaPoint()
        {
            //天线代码类似，不写注释了
            float radius = 4;
            antennaPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 36;
            antennaPoints.Add(new PointF[]
                {
                    new PointF(0, -2),
                    new PointF(radius - 3, -2),
                    new PointF(radius - 4, -6),
                    new PointF(radius+2, 0),
                    new PointF(radius - 4, 6),
                    new PointF(radius - 3, 2),
                    new PointF(0, 2),
                });
            radius = 2;
            antennaPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 22;
            antennaPoints.Add(new PointF[]
                {
                    new PointF(0, -3),
                    new PointF(radius - 6, -3),
                    new PointF(radius - 6, -6),
                    new PointF(radius, 0),
                    new PointF(radius - 6, 6),
                    new PointF(radius - 6, 3),
                    new PointF(0, 3),
                });

            radius = 30;
            antennaPoints.Add(new PointF[]
                {
                    new PointF(0, -3),
                    new PointF(radius - 6, -3),
                    new PointF(radius - 6, -6),
                    new PointF(radius, 0),
                    new PointF(radius - 6, 6),
                    new PointF(radius - 6, 3),
                    new PointF(0, 3),
                });
        }

        private static void addAntennaPath()
        {
            GraphicsPath path = new GraphicsPath();
            path.AddEllipse(antennaPoints[0][0].X, antennaPoints[0][0].Y, antennaPoints[0][2].X * 2, antennaPoints[0][2].Y * 2);
            antennaPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaPoints[1]);
            antennaPaths.Add(path);
            path = new GraphicsPath();
            path.AddEllipse(antennaPoints[2][0].X, antennaPoints[2][0].Y, antennaPoints[2][2].X * 2, antennaPoints[2][2].Y * 2);
            antennaPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaPoints[3]);
            antennaPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaPoints[4]);
            antennaPaths.Add(path);
        }

        private static void addAntennaGEPoint()
        {
            double radiusGE = 0.00004;
            int part = 36;//将一个圆分为几份
            antennaGEPoints = GEdrawCircle(radiusGE, part, 0);

            radiusGE = 0.00036;
            antennaGEPoints.Add(new LayerPoint(0, -0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00003, -0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00005, -0.00005, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE, 0, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00005, 0.00005, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00003, 0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(0, 0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(0, -0.00001, 1));

            radiusGE = 0.00002;
            part = 36;
            antennaGEPoints.AddRange(GEdrawCircle(radiusGE, part, 2));

            radiusGE = 0.00022;
            antennaGEPoints.Add(new LayerPoint(0, -0.00003, 3));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00006, -0.00003, 3));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00006, -0.00006, 3));
            antennaGEPoints.Add(new LayerPoint(radiusGE, 0, 3));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00006, 0.00006, 3));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00006, 0.00003, 3));
            antennaGEPoints.Add(new LayerPoint(0, 0.00003, 3));
            antennaGEPoints.Add(new LayerPoint(0, -0.00003, 3));
        }

        private static List<LayerPoint> GEdrawCircle(double radius, int part, int index)
        {
            List<LayerPoint> basePoint = new List<LayerPoint>();
            double angel = 2 * Math.PI / part;
            for (int i = 0; i <= part; i++)
            {
                basePoint.Add(new LayerPoint(radius * Math.Sin(i * angel), radius * Math.Cos(i * angel), index));
            }
            return basePoint;
        }
        #endregion

        public override MainModel MainModel
        {
            get { return mainModel; }
            set
            {
                mainModel = value;
                mainModel.ServerCellsChanged += serverCellsChanged;
                serverCellsChanged(null, null);
            }
        }

        public MapCellLayer(string name)
            : base(name)
        {
            this.VisibleScaleEnabled = true;
            this.VisibleScale = new VisibleScale(0, 80000);
            BrushDSC1800Cell = new SolidBrush(Color.FromArgb(56, 201, 232));
            BrushGSM900Cell = new SolidBrush(Color.FromArgb(56, 201, 232));
        }

        #region Whether Draw
        public bool DrawCell { get; set; } = false;
        public bool DrawAntenna { get; set; } = true;
        public bool DrawRepeater { get; set; } = true;
        public bool DrawRepeaterLabel { get; set; } = true;
        public bool DrawBTS { get; set; } = true;
        public bool DrawPlanBTS { get; set; } = false;
        public bool DrawPlanBTSLabel { get; set; } = false;
        public bool DrawBTSsLine { get; set; } = false;//站间线
        public bool DrawBTSLabel { get; set; } = true;
        public bool DrawCellLabel { get; set; } = false;
        public bool DrawAntennaLabel { get; set; } = false;
        public bool DrawServer { get; set; } = true;
        public bool DrawGSM900 { get; set; } = true;
        public bool DrawDSC1800 { get; set; } = true;
        /// <summary>
        /// 900/1800共址
        /// </summary>
        public bool DrawCoSite { get; set; } = true;
        public bool DrawUpper { get; set; } = true;
        public bool DrawOutdoor { get; set; } = true;
        public bool DrawIndoor { get; set; } = true;
        #endregion

        #region Brush
        public List<Brush> BrushesServer { get; private set; } = new List<Brush>();
        public Brush BrushGSM900Cell { get; private set; }
        public Brush BrushDSC1800Cell { get; private set; }
        public Brush BrushCoSiteCell { get; private set; } = new SolidBrush(Color.Coral);
        public Brush BrushGSM900Antenna { get; private set; } = new SolidBrush(Color.Black);
        public Brush BrushDSC1800Antenna { get; private set; } = new SolidBrush(Color.DarkGray);
        public Brush BrushCoSiteAntenna { get; private set; } = new SolidBrush(Color.Chocolate);
        public Brush BrushCoBCCH { get; private set; } = new SolidBrush(Color.Red);
        public Brush BrushCoTCH { get; private set; } = new SolidBrush(Color.Orange);
        public Brush BrushAdjBCCH { get; private set; } = new SolidBrush(Color.Yellow);
        public Brush BrushAdjTCH { get; private set; } = new SolidBrush(Color.DeepPink);
        public Brush BrushCoBSIC { get; private set; } = new SolidBrush(Color.BurlyWood);
        public Brush BrushNeighbour { get; private set; } = new SolidBrush(Color.Cyan);
        public Brush BrushNeighbourEachOther { get; private set; } = new SolidBrush(Color.DarkGreen);
        public Brush BrushTDNeighbour { get; private set; } = new SolidBrush(Color.Cyan);
        public Brush BrushAlarm { get; private set; } = new SolidBrush(Color.Red);

        public Pen PenSelected { get; private set; } = new Pen(Color.Red, 1);
        public Pen PenGSM900BTS { get; private set; } = new Pen(Color.Black, 1);
        public Pen PenDSC1800BTS { get; private set; } = new Pen(Color.DarkGray, 2);
        public Pen PenCoSiteBTS { get; private set; } = new Pen(Color.Maroon, 300);
        public Pen PenSelectedNB { get; private set; } = new Pen(Color.Yellow, 1);
        
        public Brush BrushJamorgcell { get; private set; } = new SolidBrush(Color.Lime);
        public Brush BrushCliqueCell { get; private set; } = new SolidBrush(Color.Yellow);
        public Brush BrushClusterCell { get; private set; } = new SolidBrush(Color.LimeGreen);
        public Brush BrushDisCoStat { get; private set; } = new SolidBrush(Color.Magenta);
        public Brush BrushMainCell { get; private set; } = new SolidBrush(Color.Red);//施主小区颜色
        public Brush BrushSourceTargetCells { get; private set; } = new SolidBrush(Color.BlueViolet);//原小区目标小区颜色（簇优化）
        public Brush BrushJamCell { get; private set; } = new SolidBrush(Color.Maroon);//干扰小区颜色（簇优化）
        public Brush BrushOrgCells { get; private set; } = new SolidBrush(Color.Orange);//原小区（被干扰小区）颜色（簇优化）
        #endregion

        #region Color
        public bool ColorViaEnabled { get; set; } = true;
        public Color ColorBegin { get; set; } = Color.Red;
        public Color ColorVia { get; set; } = Color.Yellow;
        public Color ColorEnd { get; set; } = Color.Green;
        public Color ColorGSM900Cell { get; set; } = Color.Purple;
        public Color ColorDSC1800Cell { get; set; } = Color.Violet;
        public Color ColorCoSiteCell { get; set; } = Color.Coral;
        public Color ColorGSM900Antenna { get; set; } = Color.Black;
        public Color ColorDSC1800Antenna { get; set; } = Color.DarkGray;
        public Color ColorCoSiteAntenna { get; set; } = Color.Chocolate;
        public Color ColorCoBCCH { get; set; } = Color.Red;
        public Color ColorCoTCH { get; set; } = Color.Orange;
        public Color ColorAdjBCCH { get; set; } = Color.Yellow;
        public Color ColorAdjTCH { get; set; } = Color.DeepPink;
        public Color ColorCoBSIC { get; set; } = Color.BurlyWood;
        public Color ColorNeighbour { get; set; } = Color.Cyan;
        public Color ColorNeighbourEachOther { get; set; } = Color.DarkGreen;
        public Color ColorAlarm { get; set; } = Color.Red;
        public Color ColorSelected { get; set; } = Color.Red;
        public Color ColorGSM900BTS { get; set; } = Color.Black;
        public Color ColorDSC1800BTS { get; set; } = Color.DarkGray;
        public Color ColorCoSiteBTS { get; set; } = Color.Maroon;
        public Color ColorPlanBTS { get; set; } = Color.Orange;
        public Color ColorCover { get; set; } = Color.LightGreen;
        #endregion

        #region MTGis
        private List<Cell> cellsInCurrentView = null;
        public List<Cell> CellsInCurrentView
        {
            get { return cellsInCurrentView; }
        }

        #region 不知道干嘛用的 ZTScanInterfereForm,ZTScanShearForm中使用
        private void serverCellsChanged(object sender, EventArgs e)
        {
            makeBrushes();
            Invalidate();
        }

        private void makeBrushes()
        {
            if (mainModel == null)
            {
                return;
            }
            BrushGSM900Cell = new SolidBrush(ColorGSM900Cell);
            BrushDSC1800Cell = new SolidBrush(ColorDSC1800Cell);
            BrushCoSiteCell = new SolidBrush(ColorCoSiteCell);
            BrushGSM900Antenna = new SolidBrush(ColorGSM900Antenna);
            BrushDSC1800Antenna = new SolidBrush(ColorDSC1800Antenna);
            BrushCoSiteAntenna = new SolidBrush(ColorCoSiteAntenna);
            BrushCoBCCH = new SolidBrush(ColorCoBCCH);
            BrushCoTCH = new SolidBrush(ColorCoTCH);
            BrushAdjBCCH = new SolidBrush(ColorAdjBCCH);
            BrushAdjTCH = new SolidBrush(ColorAdjTCH);
            BrushCoBSIC = new SolidBrush(ColorCoBSIC);
            BrushNeighbour = new SolidBrush(ColorNeighbour);
            BrushNeighbourEachOther = new SolidBrush(ColorNeighbourEachOther);
            BrushTDNeighbour = new SolidBrush(ColorNeighbour);
            BrushAlarm = new SolidBrush(ColorAlarm);
            PenSelected = new Pen(ColorSelected, 4);
            PenGSM900BTS = new Pen(ColorGSM900BTS, 1);
            PenDSC1800BTS = new Pen(ColorDSC1800BTS, 2);
            PenCoSiteBTS = new Pen(ColorCoSiteBTS, 3);
            BrushesServer.Clear();

            mainModel.ServerCellPens.Clear();
            int count = mainModel.ServerCells.Count + mainModel.ServerTDCells.Count;
            for (int i = 0; i < count; i++)
            {
                float percent = count == 1 ? 0.5F : (float)i / (count - 1);
                Color beginColor;
                Color endColor;
                if (ColorViaEnabled)
                {
                    percent = percent * 2;
                    if (percent < 1)
                    {
                        beginColor = ColorBegin;
                        endColor = ColorVia;
                    }
                    else
                    {
                        percent -= 1;
                        beginColor = ColorVia;
                        endColor = ColorEnd;
                    }
                }
                else
                {
                    beginColor = ColorBegin;
                    endColor = ColorEnd;
                }
                BrushesServer.Add(new SolidBrush(Color.FromArgb(
                    (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                    (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                    (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                    )));
                mainModel.ServerCellPens.Add(new Pen(Color.FromArgb(
                    (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                    (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                    (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                    ), 1));
            }
        }

        private void getScanInterfereCells()
        {
            if (scanInterfereCells == null)
            {
                scanInterfereCells = new Dictionary<Cell, List<MasterCom.RAMS.Net.GridItem>>();
            }
            scanInterfereCells.Clear();
            foreach (ScanInterfereResult result in mainModel.ScanInterfereResults)
            {
                Cell cell1 = mainModel.CellManager.GetCurrentCell(result.cellIDOne);
                Cell cell2 = mainModel.CellManager.GetCurrentCell(result.cellIDTwo);
                if (scanInterfereCells.ContainsKey(cell1))
                {
                    scanInterfereCells[cell1].AddRange(result.gridsOne);
                }
                else
                {
                    List<MasterCom.RAMS.Net.GridItem> gridList = new List<MasterCom.RAMS.Net.GridItem>(result.gridsOne);
                    scanInterfereCells[cell1] = gridList;
                }
                if (scanInterfereCells.ContainsKey(cell2))
                {
                    scanInterfereCells[cell2].AddRange(result.gridsTwo);
                }
                else
                {
                    List<MasterCom.RAMS.Net.GridItem> gridList = new List<MasterCom.RAMS.Net.GridItem>(result.gridsTwo);
                    scanInterfereCells[cell2] = gridList;
                }
            }
            makeBrushesScanInterfere(scanInterfereCells.Count);
        }

        private void getScanShearCells()
        {
            if (scanInterfereCells == null)
            {
                scanInterfereCells = new Dictionary<Cell, List<MasterCom.RAMS.Net.GridItem>>();
            }
            scanInterfereCells.Clear();
            foreach (ScanShearResult result in mainModel.ScanShearResults)
            {
                Cell cell1 = mainModel.CellManager.GetCurrentCell(result.cellIDMain);
                Cell cell2 = mainModel.CellManager.GetCurrentCell(result.cellIDOther);
                if (scanInterfereCells.ContainsKey(cell1))
                {
                    scanInterfereCells[cell1].AddRange(result.grids);
                }
                else
                {
                    List<MasterCom.RAMS.Net.GridItem> gridList = new List<MasterCom.RAMS.Net.GridItem>();
                    gridList.AddRange(result.grids);
                    scanInterfereCells[cell1] = gridList;
                }
                if (scanInterfereCells.ContainsKey(cell2))
                {
                    scanInterfereCells[cell2].AddRange(result.grids);
                }
                else
                {
                    List<MasterCom.RAMS.Net.GridItem> gridList = new List<MasterCom.RAMS.Net.GridItem>();
                    gridList.AddRange(result.grids);
                    scanInterfereCells[cell2] = gridList;
                }
            }
            makeBrushesScanInterfere(scanInterfereCells.Count);
        }

        private void makeBrushesScanInterfere(int count)
        {
            BrushesScanInterfere.Clear();
            PensScanInterfere.Clear();
            for (int i = 0; i < count; i++)
            {
                float percent = count == 1 ? 0.5F : (float)i / (count - 1);
                Color beginColor;
                Color endColor;
                if (ColorViaEnabled)
                {
                    percent = percent * 2;
                    if (percent < 1)
                    {
                        beginColor = ColorBegin;
                        endColor = ColorVia;
                    }
                    else
                    {
                        percent -= 1;
                        beginColor = ColorVia;
                        endColor = ColorEnd;
                    }
                }
                else
                {
                    beginColor = ColorBegin;
                    endColor = ColorEnd;
                }
                BrushesScanInterfere.Add(new SolidBrush(Color.FromArgb(
                    (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                    (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                    (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                    )));
                PensScanInterfere.Add(new Pen(Color.FromArgb(
                    (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                    (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                    (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                    ), 1));
            }
        }
        #endregion
        #endregion

        /// <summary>
        /// 绘制图层
        /// </summary>
        /// <param name="clientRect">屏幕范围</param>
        /// <param name="updateRect">扩展屏幕周围</param>
        /// <param name="graphics">画布</param>
        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            //扩展
            updateRect.Inflate((int)(4000000 / mapScale), (int)(4000000 / mapScale));
            DbRect dRect = null;
            //坐标变换，将扩展后的屏幕坐标转换为投影坐标
            gisAdapter.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            if (mainModel.ScanInterfereResults.Count > 0 || mainModel.ScanShearResults.Count > 0)
            {
                if (mainModel.ScanInterfereResults.Count > 0)
                {
                    getScanInterfereCells();
                }
                if (mainModel.ScanShearResults.Count > 0)
                {
                    getScanShearCells();
                }
            }
            else
            {
                scanInterfereCells = null;
            }

            #region 小区覆盖仿真 ZTGSMCellEmulateCoverAna,ZTTdCellEmulateCoverAna中使用
            if (mainModel.CellEmulationPointsList.Count > 0) //小区覆盖仿真覆盖模型
            {
                GraphicsPath regionPath = new GraphicsPath();
                drawCellEmulationCov(graphics, regionPath);
            }
            #endregion

            cellsInCurrentView = null;
            drawEntry(graphics, dRect);

            drawLabel(graphics, dRect);

            if (curMapType == LayerMapType.MTGis)
            {
                drawInterfere(graphics);//绘制干扰源
            }
        }

        private void drawEntry(Graphics graphics, DbRect dRect)
        {
            if (DrawCell)
            {
                drawCellEntry(graphics, dRect);
            }
            if (DrawAntenna)
            {
                drawAntenaEntry(graphics, dRect);
            }
            if (DrawRepeater)
            {
                drawRepeaterEntry(graphics, dRect);
            }
            if (DrawBTS)
            {
                drawBTSEntry(graphics, dRect);
            }
            if (DrawPlanBTS && curMapType == LayerMapType.MTGis)
            {
                drawPLanBTSsEntry(graphics, dRect);
            }

            if (DrawBTSsLine && mainModel.SelectedCell != null)
            {
                drawLineBetweenBTSEntry(graphics, dRect);
            }
        }

        private void drawLabel(Graphics graphics, DbRect dRect)
        {
            if (DrawRepeaterLabel && curMapType == LayerMapType.MTGis && mapScale < 50000)
            {
                drawRepeaterLabelEntry(graphics, dRect);
            }
            if (DrawPlanBTSLabel && curMapType == LayerMapType.MTGis && mapScale < 50000)
            {
                drawPlanBTSLabelEntry(graphics);
            }
            if (DrawBTSLabel && mapScale < 50000)
            {
                drawBTSLabelEntry(graphics, dRect);
            }

            bool drawCellHighlight = (curMapType == LayerMapType.MTGis && (mainModel.CellHighLightAll || mainModel.CellHighlightList.Count > 0));

            if (DrawCellLabel && (mapScale < 50000 || drawCellHighlight))
            {
                drawCellLabelEntry(graphics, dRect);
            }
            if (DrawAntennaLabel && (mapScale < 50000 || drawCellHighlight))
            {
                drawAntennaLabelEntry(graphics, dRect);
            }
            if (mapScale < 1000000)
            {
                drawPlanningLabelEntry(graphics, dRect);
            }
        }

        #region Draw
        #region drawCellEmulationCov 小区覆盖仿真
        private void drawCellEmulationCov(Graphics graphics, GraphicsPath regionPath)
        {
            if (mainModel.SearchGeometrys.Region == null)
                return;

            setRegionPath(graphics, regionPath);

            foreach (List<MasterCom.RAMS.ZTFunc.LongLat> longLatList in mainModel.CellEmulationPointsList)
            {
                fillGraphicPath(graphics, longLatList);

                if (mainModel.DrawEmulationPoints)
                {
                    foreach (MasterCom.RAMS.ZTFunc.LongLat ll in longLatList)
                    {
                        DbPoint dPt2 = new DbPoint(ll.fLongitude, ll.fLatitude);
                        PointF ptf2;
                        gisAdapter.ToDisplay(dPt2, out ptf2);
                        Brush brush = new SolidBrush(Color.Green);

                        float ratio = CustomDrawLayer.GetTestPointDisplayRatio(mapScale);
                        float radius = ratio * 10;
                        graphics.TranslateTransform(ptf2.X, ptf2.Y);
                        graphics.ScaleTransform(radius, radius);
                        GraphicsPath path = SymbolManager.GetInstance().Paths[1];
                        graphics.FillPath(brush, path);
                        graphics.ResetTransform();
                    }
                }
            }
            graphics.ResetClip();
        }

        private void fillGraphicPath(Graphics graphics, List<ZTFunc.LongLat> longLatList)
        {
            List<PointF> ptfList = new List<PointF>();
            addPtfList(longLatList, ptfList);
            GraphicsPath pathS = new GraphicsPath();
            PointF[] ptfs = new PointF[ptfList.Count];
            for (int i = 0; i < ptfList.Count; i++)
            {
                ptfs[i] = ptfList[i];
            }
            pathS.AddPolygon(ptfs);
            graphics.FillPath(new SolidBrush(Color.Yellow), pathS);
        }

        private void addPtfList(List<ZTFunc.LongLat> longLatList, List<PointF> ptfList)
        {
            foreach (MasterCom.RAMS.ZTFunc.LongLat ll in longLatList)
            {
                DbPoint dPt = new DbPoint(ll.fLongitude, ll.fLatitude);
                PointF ptf;
                gisAdapter.ToDisplay(dPt, out ptf);
                ptfList.Add(ptf);
            }
        }

        private void setRegionPath(Graphics graphics, GraphicsPath regionPath)
        {
            List<ResvRegion> resvRegions = mainModel.SearchGeometrys.SelectedResvRegions;//预存区域
            if (resvRegions != null && resvRegions.Count > 0)
            {
                foreach (ResvRegion rr in resvRegions)
                {
                    int numR = rr.Shape.numPoints;
                    PointF[] regionRtfs = new PointF[numR];
                    for (int p = 0; p < numR; p++)
                    {
                        MapWinGIS.Point point = rr.Shape.get_Point(p);
                        DbPoint dPt = new DbPoint(point.x, point.y);
                        PointF ptf;
                        gisAdapter.ToDisplay(dPt, out ptf);
                        regionRtfs[p] = ptf;
                    }

                    GraphicsPath path = new GraphicsPath();
                    if (curMapType == LayerMapType.Google)
                    {
                        path.FillMode = FillMode.Winding;
                    }
                    path.AddPolygon(regionRtfs);
                    regionPath.AddPath(path, false);
                }
            }
            else
            {
                int numP = mainModel.SearchGeometrys.Region.numPoints;
                PointF[] regionPtfs = new PointF[numP];
                for (int p = 0; p < numP; p++)
                {
                    MapWinGIS.Point point = mainModel.SearchGeometrys.Region.get_Point(p);
                    DbPoint dPt = new DbPoint(point.x, point.y);
                    PointF ptf;
                    gisAdapter.ToDisplay(dPt, out ptf);
                    regionPtfs[p] = ptf;
                }
                regionPath.AddPolygon(regionPtfs);
            }
            graphics.SetClip(regionPath);//将显示小区的覆盖图形与选择区域剪切，只显示交集
        }
        #endregion

        #region drawCellEntry
        private void drawCellEntry(Graphics graphics, DbRect dRect)
        {
            List<Cell> cells = getCells();
            if (cells != null)
            {
                cellsInCurrentView = new List<Cell>();
                List<Cell> cellsOf900 = null;
                List<Cell> cellsOf1800 = null;
                List<Cell> cellsOfCoSite = null;
                getRegionCells(dRect, cells, out cellsOf900, out cellsOfCoSite, out cellsOf1800);
                //让GSM1800的小区显示在上面，GSM900的小区显示在下面
                drawCells(graphics, cellsOf900);
                drawCells(graphics, cellsOfCoSite);
                drawCells(graphics, cellsOf1800);
                cellsInCurrentView.AddRange(cellsOf900);
                cellsInCurrentView.AddRange(cellsOfCoSite);
                cellsInCurrentView.AddRange(cellsOf1800);
            }
            cells = mainModel.ServerCells;
            if (cells != null && DrawServer)
            {
                List<Cell> cellsOf900 = null;
                List<Cell> cellsOf1800 = null;
                List<Cell> cellsOfCoSite = null;
                getRegionCells(dRect, cells, out cellsOf900, out cellsOfCoSite, out cellsOf1800);
                //让GSM1800的小区显示在上面，GSM900的小区显示在下面
                drawCells(graphics, cellsOf900);
                drawCells(graphics, cellsOfCoSite);
                drawCells(graphics, cellsOf1800);
            }
        }

        /// <summary>
        /// 获取Cells
        /// </summary>
        /// <returns>Cells</returns>
        private List<Cell> getCells()
        {
            List<Cell> cells = null;
            if (DrawCurrent)
            {
                cells = mainModel.CellManager.GetCurrentCells();
            }
            else
            {
                cells = mainModel.CellManager.GetCells(CurShowTimeAt);
            }
            return cells;
        }

        private void getRegionCells(DbRect regionRect, List<Cell> cells, out List<Cell> cellOf900, out List<Cell> cellOfCoSite, out List<Cell> cellOf1800)
        {
            cellOf900 = new List<Cell>();
            cellOf1800 = new List<Cell>();
            cellOfCoSite = new List<Cell>();
            foreach (Cell cell in cells)
            {
                if (cell.Antennas.Count > 0 && cell.Within(regionRect.x1, regionRect.y1, regionRect.x2, regionRect.y2))
                {
                    if (cell.BandType == BTSBandType.GSM900)
                    {
                        cellOf900.Add(cell);
                    }
                    else if (cell.BandType == BTSBandType.DSC1800)
                    {
                        cellOf1800.Add(cell);
                    }
                    else if (cell.BandType == BTSBandType.CoSite)
                    {
                        cellOfCoSite.Add(cell);
                    }
                }
            }
        }

        private void drawCells(Graphics graphics, List<Cell> cells)
        {
            foreach (Cell cell in cells)
            {
                bool isValid = getValidCellType(cell);
                if (isValid)
                {
                    paintCell(cell, graphics, mapScale);
                }
            }
        }

        private bool getValidCellType(Cell cell)
        {
            if (((cell.BandType == BTSBandType.GSM900 && DrawGSM900)
               || (cell.BandType == BTSBandType.DSC1800 && DrawDSC1800)
               || (cell.BandType == BTSBandType.CoSite && DrawCoSite))
               && ((cell.Type == BTSType.Outdoor && DrawOutdoor)
               || (cell.Type == BTSType.Upper && DrawUpper)
               || (cell.Type == BTSType.Indoor && DrawIndoor)))
            {
                return true;
            }
            return false;
        }
        #endregion

        #region drawAntenaEntry
        private void drawAntenaEntry(Graphics graphics, DbRect dRect)
        {
            List<Cell> cells = getCells();
            List<Antenna> gsm900Antennas;
            List<Antenna> gsm1800Antennas;
            List<Antenna> coSiteAntennas;
            if (cells != null)
            {
                //让GSM1800的小区显示在上面，GSM900的小区显示在下面
                getRegionAntennas(dRect, cells, out gsm900Antennas, out coSiteAntennas, out gsm1800Antennas);
                drawAntennas(graphics, gsm900Antennas);
                drawAntennas(graphics, coSiteAntennas);
                drawAntennas(graphics, gsm1800Antennas);
            }
            cells = mainModel.ServerCells;
            if (cells != null && DrawServer)
            {
                //让GSM1800的小区显示在上面，GSM900的小区显示在下面
                getRegionAntennas(dRect, cells, out gsm900Antennas, out coSiteAntennas, out gsm1800Antennas);
                drawAntennas(graphics, gsm900Antennas);
                drawAntennas(graphics, coSiteAntennas);
                drawAntennas(graphics, gsm1800Antennas);
            }
        }

        private void getRegionAntennas(DbRect regionRect, List<Cell> cells, out List<Antenna> antennaOf900, out List<Antenna> antennaOfCoSite, out List<Antenna> antennaOf1800)
        {
            antennaOf900 = new List<Antenna>();
            antennaOfCoSite = new List<Antenna>();
            antennaOf1800 = new List<Antenna>();
            foreach (Cell cell in cells)
            {
                addAntennaFromCell(regionRect, antennaOf900, antennaOfCoSite, antennaOf1800, cell);
            }
        }

        private static void addAntennaFromCell(DbRect regionRect, List<Antenna> antennaOf900, List<Antenna> antennaOfCoSite, List<Antenna> antennaOf1800, Cell cell)
        {
            foreach (Antenna antenna in cell.Antennas)
            {
                if (antenna.Within(regionRect.x1, regionRect.y1, regionRect.x2, regionRect.y2))
                {
                    if (antenna.BandType == BTSBandType.GSM900)
                    {
                        antennaOf900.Add(antenna);
                    }
                    else if (antenna.BandType == BTSBandType.DSC1800)
                    {
                        antennaOf1800.Add(antenna);
                    }
                    else if (antenna.BandType == BTSBandType.CoSite)
                    {
                        antennaOfCoSite.Add(antenna);
                    }
                }
            }
        }

        private void drawAntennas(Graphics graphics, List<Antenna> antennas)
        {
            foreach (Antenna antenna in antennas)
            {
                bool isValid = getValidAntennaType(antenna);
                if (isValid)
                {
                    paintAntenna(antenna, graphics, mapScale);
                }
            }
        }

        private bool getValidAntennaType(Antenna antenna)
        {
            if (((antenna.BandType == BTSBandType.GSM900 && DrawGSM900)
               || (antenna.BandType == BTSBandType.DSC1800 && DrawDSC1800)
               || (antenna.BandType == BTSBandType.CoSite && DrawCoSite))
               && ((antenna.Type == BTSType.Outdoor && DrawOutdoor)
               || (antenna.Type == BTSType.Upper && DrawUpper)
               || (antenna.Type == BTSType.Indoor && DrawIndoor)))
            {
                return true;
            }
            return false;
        }
        #endregion

        #region drawRepeaterEntry
        private void drawRepeaterEntry(Graphics graphics, DbRect dRect)
        {
            List<Repeater> repeaters = getRepeaters();
            if (repeaters != null)
            {
                foreach (Repeater repeater in repeaters)
                {
                    bool isvalid = judgeRepeaterValid(repeater, dRect);
                    if (isvalid)
                    {
                        paintRepeater(repeater, graphics);
                    }
                }
            }
        }

        private bool judgeRepeaterValid(Repeater repeater, DbRect dRect)
        {
            if (repeater.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                if ((repeater.BandType == BTSBandType.GSM900 && DrawGSM900) || (repeater.BandType == BTSBandType.DSC1800 && DrawDSC1800))
                {
                    return true;
                }
                return false;
            }
            return false;
        }

        /// <summary>
        /// 获取Repeaters
        /// </summary>
        /// <returns>Repeaters</returns>
        private List<Repeater> getRepeaters()
        {
            List<Repeater> repeaters = null;
            if (DrawCurrent)
            {
                repeaters = mainModel.CellManager.GetCurrentRepeaters();
            }
            else
            {
                repeaters = mainModel.CellManager.GetRepeaters(CurShowTimeAt);
            }
            return repeaters;
        }
        #endregion

        #region drawRepeaterLabelEntry
        public List<System.Drawing.Rectangle> DrawedRepeaterLabelRectangles { get; private set; } = new List<System.Drawing.Rectangle>();
        private void drawRepeaterLabelEntry(Graphics graphics, DbRect dRect)
        {
            List<Repeater> repeaters = getRepeaters();
            if (repeaters != null)
            {
                DrawedRepeaterLabelRectangles.Clear();
                foreach (Repeater repeater in repeaters)
                {
                    bool isvalid = judgeRepeaterValid(repeater, dRect);
                    if (isvalid)
                    {
                        paintRepeaterLabel(repeater, graphics);
                    }
                }
            }
        }
        #endregion

        #region drawBTSEntry
        private void drawBTSEntry(Graphics graphics, DbRect dRect)
        {
            List<BTS> btss = getBTSs();
            if (btss != null)
            {
                foreach (BTS bts in btss)
                {
                    bool isValid = getValidBtsType(bts, dRect);
                    if (isValid)
                    {
                        paintBTS(bts, graphics);
                    }
                }
            }
        }

        private bool getValidBtsType(BTS bts, DbRect dRect)
        {
            if (bts.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                if (((bts.BandType == BTSBandType.GSM900 && DrawGSM900)
                   || (bts.BandType == BTSBandType.DSC1800 && DrawDSC1800)
                   || (bts.BandType == BTSBandType.CoSite && DrawCoSite))
                   && ((bts.Type == BTSType.Outdoor && DrawOutdoor)
                   || (bts.Type == BTSType.Upper && DrawUpper)
                   || (bts.Type == BTSType.Indoor && DrawIndoor)))
                {
                    return true;
                }
                return false;
            }
            return false;
        }

        /// <summary>
        /// 获取BTSs
        /// </summary>
        /// <returns>BTSs</returns>
        private List<BTS> getBTSs()
        {
            List<BTS> btss = null;
            if (DrawCurrent)
            {
                btss = mainModel.CellManager.GetCurrentBTSs();
            }
            else
            {
                btss = mainModel.CellManager.GetBTSs(CurShowTimeAt);
            }
            return btss;
        }
        #endregion

        #region drawPLanBTSsEntry
        private void drawPLanBTSsEntry(Graphics graphics, DbRect dRect)
        {
            List<PlanBTS> pLanBTSs = null;
            pLanBTSs = mainModel.CellManager.GetCurrentPlanBTSs();
            if (pLanBTSs != null)
            {
                initPlanBTSPath();
                foreach (PlanBTS bts in pLanBTSs)
                {
                    if (bts.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
                    {
                        paintPlanBTS(bts, graphics);
                    }
                }
            }
        }
        #endregion

        #region drawLineBetweenBTSEntry
        public Color BtssLineColor { get; set; } = Color.Orange;
        public int BtssLineWidth { get; set; } = 1;
        public Color BtsLineLblColor { get; set; }
        public double BtssDistance { get; set; }
        public double BtsLineAngle { get; set; }

        internal class BTSsDistance
        {
            public BTS bts;
            public int distance;
            public BTSsDistance(BTS bts, int distance)
            {
                this.bts = bts;
                this.distance = distance;
            }
            public static int SortBTSsDistance(BTSsDistance bts1, BTSsDistance bts2)
            {
                return bts1.distance.CompareTo(bts2.distance);
            }
        }

        private void drawLineBetweenBTSEntry(Graphics graphics, DbRect dRect)
        {
            BTS curBTS = mainModel.SelectedCell.BelongBTS;
            List<BTS> BTSs = getBTSs();
            if (BTSs != null)
            {
                Pen pen = new Pen(BtssLineColor, BtssLineWidth);
                Brush brsh = new SolidBrush(BtsLineLblColor);
                DbPoint dPointCurBTS = new DbPoint(curBTS.Longitude, curBTS.Latitude);
                PointF pointCurBTS;
                gisAdapter.ToDisplay(dPointCurBTS, out pointCurBTS);
                List<BTSsDistance> distanceLst = new List<BTSsDistance>();
                addDistanceLst(dRect, curBTS, BTSs, dPointCurBTS, distanceLst);
                distanceLst.Sort(BTSsDistance.SortBTSsDistance);

                List<BTSsDistance> tempLst = new List<BTSsDistance>();
                for (int i = 0; i < distanceLst.Count; i++)
                {
                    drawLineByDistanceLst(graphics, pen, brsh, pointCurBTS, distanceLst, tempLst, i);
                }
            }
        }

        private void drawLineByDistanceLst(Graphics graphics, Pen pen, Brush brsh, PointF pointCurBTS, List<BTSsDistance> distanceLst, List<BTSsDistance> tempLst, int i)
        {
            bool isDraw = true;
            BTS bts = distanceLst[i].bts;
            int distance = distanceLst[i].distance;

            DbPoint dPointAtartBTS = new DbPoint(bts.Longitude, bts.Latitude);
            PointF startPt;
            gisAdapter.ToDisplay(dPointAtartBTS, out startPt);
            foreach (BTSsDistance item in tempLst)
            {
                DbPoint dPointEndBTS = new DbPoint(item.bts.Longitude, item.bts.Latitude);
                PointF endPt;
                gisAdapter.ToDisplay(dPointEndBTS, out endPt);
                if (MathFuncs.CalAngle(pointCurBTS, startPt, endPt) < BtsLineAngle)
                {
                    isDraw = false;
                }
            }
            if (isDraw)
            {
                tempLst.Add(distanceLst[i]);
                graphics.DrawLine(pen, pointCurBTS, startPt);
                Font fontMeasure = new Font(new FontFamily("宋体"), 10, FontStyle.Bold);
                graphics.DrawString("" + distance + "米", fontMeasure, brsh, (startPt.X + pointCurBTS.X) / 2 - 10, (pointCurBTS.Y + startPt.Y) / 2 - 10);
            }
        }

        private void addDistanceLst(DbRect dRect, BTS curBTS, List<BTS> BTSs, DbPoint dPointCurBTS, List<BTSsDistance> distanceLst)
        {
            foreach (BTS bts in BTSs)
            {
                bool isValid = getValidBtsLineType(bts, dRect);
                if (isValid && bts != curBTS)
                {
                    DbPoint dPointBts = new DbPoint(bts.Longitude, bts.Latitude);
                    double longitudeDistance = (Math.Sin((90 - dPointBts.y) * 2 * Math.PI / 360) + Math.Sin((90 - dPointCurBTS.y) * 2 * Math.PI / 360)) / 2 * (dPointBts.x - dPointCurBTS.x) / 360 * 40075360;
                    double latitudeDistance = (dPointBts.y - dPointCurBTS.y) / 360 * 39940670;
                    double distance = Math.Sqrt(longitudeDistance * longitudeDistance + latitudeDistance * latitudeDistance);
                    if (0 < distance && distance < BtssDistance)
                    {
                        distanceLst.Add(new BTSsDistance(bts, (int)distance));
                    }
                }
            }
        }

        private bool getValidBtsLineType(BTS bts, DbRect dRect)
        {
            if (bts.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                if (((bts.BandType == BTSBandType.GSM900 && DrawGSM900)
                   || (bts.BandType == BTSBandType.DSC1800 && DrawDSC1800)
                   || (curMapType == LayerMapType.Google && (bts.BandType == BTSBandType.CoSite && DrawCoSite)))
                   && ((bts.Type == BTSType.Outdoor && DrawOutdoor)
                   || (bts.Type == BTSType.Upper && DrawUpper)
                   || (bts.Type == BTSType.Indoor && DrawIndoor)))
                {
                    return true;
                }
                return false;
            }
            return false;
        }
        #endregion

        #region drawPlanBTSLabelEntry
        public List<System.Drawing.Rectangle> DrawedPlanBTSLabelRectangles { get; private set; } = new List<System.Drawing.Rectangle>();
        private void drawPlanBTSLabelEntry(Graphics graphics)
        {
            List<PlanBTS> btss = null;
            btss = mainModel.CellManager.GetCurrentPlanBTSs();
            if (btss != null)
            {
                DrawedPlanBTSLabelRectangles.Clear();
                foreach (PlanBTS bts in btss)
                {
                    paintPlanBTSLabel(bts, graphics);
                }
            }
        }
        #endregion

        #region drawBTSLabelEntry
        public List<System.Drawing.Rectangle> DrawedBTSLabelRectangles { get; private set; } = new List<System.Drawing.Rectangle>();
        private void drawBTSLabelEntry(Graphics graphics, DbRect dRect)
        {
            List<BTS> btss = getBTSs();
            if (btss != null)
            {
                DrawedBTSLabelRectangles.Clear();
                foreach (BTS bts in btss)
                {
                    bool isValid = getValidBtsType(bts, dRect);
                    if (isValid)
                    {
                        paintBTSLabel(bts, graphics);
                    }
                }
            }
        }
        #endregion

        #region drawCellLabelEntry
        public List<System.Drawing.Rectangle> DrawedCellLabelRectangles { get; private set; } = new List<System.Drawing.Rectangle>();
        private void drawCellLabelEntry(Graphics graphics, DbRect dRect)
        {
            List<Cell> cells = getCells();
            if (cells != null)
            {
                DrawedCellLabelRectangles.Clear();//清空矩形
                foreach (Cell cell in cells)//地图上的位置数据
                {
                    if (cell.Antennas.Count > 0 && (curMapType != LayerMapType.MTGis || mainModel.CellHighLightAll || mainModel.CellHighlightList.Count == 0 || mainModel.CellHighlightList.Contains(cell)))
                    {
                        bool isValid = getValidCellType(cell, dRect);
                        if (isValid)
                        {
                            paintCellLabel(cell, graphics);
                        }
                    }
                }
            }
        }

        private bool getValidCellType(Cell cell, DbRect dRect)
        {
            if (cell.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                if (((cell.BandType == BTSBandType.GSM900 && DrawGSM900)
                   || (cell.BandType == BTSBandType.DSC1800 && DrawDSC1800)
                   || (cell.BandType == BTSBandType.CoSite && DrawCoSite))
                   && ((cell.Type == BTSType.Outdoor && DrawOutdoor)
                   || (cell.Type == BTSType.Upper && DrawUpper)
                   || (cell.Type == BTSType.Indoor && DrawIndoor)))
                {
                    return true;
                }
                return false;
            }
            return false;
        }
        #endregion

        #region drawAntennaLabelEntry
        public List<System.Drawing.Rectangle> DrawedAntennaLabelRectangles { get; private set; } = new List<System.Drawing.Rectangle>();
        private void drawAntennaLabelEntry(Graphics graphics, DbRect dRect)
        {
            List<Cell> cells = getCells();
            if (cells.Count > 0)
            {
                DrawedAntennaLabelRectangles.Clear();
                foreach (Cell cell in cells)
                {
                    paintValidAntennaLabel(graphics, dRect, cell);
                }
            }
        }

        private void paintValidAntennaLabel(Graphics graphics, DbRect dRect, Cell cell)
        {
            foreach (Antenna antenna in cell.Antennas)
            {
                bool isHighlight = judgeCellHighlight(antenna);
                if (isHighlight)
                {
                    break;
                }
                bool isValid = getValidAntennaType(antenna, dRect);
                if (isValid)
                {
                    paintAntennaLabel(antenna, graphics);
                }
            }
        }

        private bool judgeCellHighlight(Antenna antenna)
        {
            if (curMapType == LayerMapType.MTGis && mapScale >= 50000 && !mainModel.CellHighLightAll && mainModel.CellHighlightList.Count > 0)
            {
                if (mainModel.CellHighlightList.Contains(antenna.BelongCell))
                {
                    return true;
                }
                return false;
            }
            return false;
        }

        private bool getValidAntennaType(Antenna antenna, DbRect dRect)
        {
            if (antenna.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                if (((antenna.BandType == BTSBandType.GSM900 && DrawGSM900)
                   || (antenna.BandType == BTSBandType.DSC1800 && DrawDSC1800)
                   || (antenna.BandType == BTSBandType.CoSite && DrawCoSite))
                   && ((antenna.Type == BTSType.Outdoor && DrawOutdoor)
                   || (antenna.Type == BTSType.Upper && DrawUpper)
                   || (antenna.Type == BTSType.Indoor && DrawIndoor)))
                {
                    return true;
                }
                return false;
            }
            return false;
        }
        #endregion

        #region drawPlanningLabelEntry
        public Dictionary<string, int> DicIDCount { get; set; } = new Dictionary<string, int>();
        private void drawPlanningLabelEntry(Graphics graphics, DbRect dRect)
        {
            List<Cell> cells = getCells();
            if (cells != null)
            {
                DicIDCount = new Dictionary<string, int>();
                foreach (Cell cell in cells)
                {
                    paintValidPlanningLabel(graphics, dRect, cell);
                }
            }

            drawJamcellLine(graphics);

            drawDisCoStatCellLine(graphics);

            drawSourceCellLine(graphics);

            drawJamCellOnGISLine(graphics);
        }

        private void paintValidPlanningLabel(Graphics graphics, DbRect dRect, Cell cell)
        {
            if (cell.Antennas.Count > 0)
            {
                bool isValid = getValidCellType(cell, dRect);
                if (isValid)
                {
                    paintPlanningLabel(cell, graphics);
                }
            }
        }

        private void drawJamCellOnGISLine(Graphics graphics)
        {
            if (mainModel.ClusterAnalysisSet.JamCellOnGIS != null && mainModel.ClusterAnalysisSet.OrgCellsOnGIS.Count > 0)
            {
                foreach (Cell orgCell in mainModel.ClusterAnalysisSet.OrgCellsOnGIS)
                {
                    DbPoint jamDPoint = new DbPoint(mainModel.ClusterAnalysisSet.JamCellOnGIS.Antennas[0].EndPointLongitude
                        , mainModel.ClusterAnalysisSet.JamCellOnGIS.Antennas[0].EndPointLatitude);
                    PointF jamPointF;
                    gisAdapter.ToDisplay(jamDPoint, out jamPointF);

                    DbPoint orgDPoint = new DbPoint(orgCell.Antennas[0].EndPointLongitude, orgCell.Antennas[0].EndPointLatitude);
                    PointF orgPointF;
                    gisAdapter.ToDisplay(orgDPoint, out orgPointF);

                    graphics.DrawLine(new Pen(BrushOrgCells, 2), jamPointF, orgPointF);
                }
            }
        }

        private void drawSourceCellLine(Graphics graphics)
        {
            if (mainModel.ClusterAnalysisSet.SourceCell != null && mainModel.ClusterAnalysisSet.TargetCell != null)
            {
                DbPoint sourceDPoint = new DbPoint(mainModel.ClusterAnalysisSet.SourceCell.Antennas[0].EndPointLongitude, mainModel.ClusterAnalysisSet.SourceCell.Antennas[0].EndPointLatitude);
                PointF sourcePointF;
                gisAdapter.ToDisplay(sourceDPoint, out sourcePointF);

                DbPoint targetDPoint = new DbPoint(mainModel.ClusterAnalysisSet.TargetCell.Antennas[0].EndPointLongitude, mainModel.ClusterAnalysisSet.TargetCell.Antennas[0].EndPointLatitude);
                PointF targetPointF;
                gisAdapter.ToDisplay(targetDPoint, out targetPointF);

                graphics.DrawLine(new Pen(BrushSourceTargetCells, 2), sourcePointF, targetPointF);
            }
        }

        private void drawDisCoStatCellLine(Graphics graphics)
        {
            if (mainModel.ClusterAnalysisSet.DisCoStatDic != null)
            {
                foreach (Cell cell1 in mainModel.ClusterAnalysisSet.DisCoStatDic.Keys)
                {
                    foreach (Cell cell2 in mainModel.ClusterAnalysisSet.DisCoStatDic[cell1])
                    {
                        DbPoint antenna1DPoint = new DbPoint(cell1.Antennas[0].EndPointLongitude, cell1.Antennas[0].EndPointLatitude);
                        PointF antenna1PointF;
                        gisAdapter.ToDisplay(antenna1DPoint, out antenna1PointF);

                        DbPoint antenna2DPoint = new DbPoint(cell2.Antennas[0].EndPointLongitude, cell2.Antennas[0].EndPointLatitude);
                        PointF antenna2PointF;
                        gisAdapter.ToDisplay(antenna2DPoint, out antenna2PointF);

                        graphics.DrawLine(new Pen(BrushDisCoStat), antenna1PointF, antenna2PointF);
                    }
                }
            }
        }

        private void drawJamcellLine(Graphics graphics)
        {
            if (mainModel.JamOrgDic != null)
            {
                foreach (Cell jamcell in mainModel.JamOrgDic.Keys)
                {
                    foreach (Cell orgcell in mainModel.JamOrgDic[jamcell])
                    {
                        DbPoint jamDPoint = new DbPoint(jamcell.EndPointLongitude, jamcell.EndPointLatitude);
                        PointF jamPointF;
                        gisAdapter.ToDisplay(jamDPoint, out jamPointF);

                        DbPoint orgDPoint = new DbPoint(orgcell.EndPointLongitude, orgcell.EndPointLatitude);
                        PointF orgPointF;
                        gisAdapter.ToDisplay(orgDPoint, out orgPointF);

                        graphics.DrawLine(new Pen(Color.Green), jamPointF, orgPointF);
                    }
                }
            }
        }
        #endregion

        #region drawInterfere
        public bool DrawInterfereDis { get; set; } = false;
        public Color DrawInterfereColor { get; set; } = Color.Red;
        public int DrawInterfereSizi { get; set; } = 4;
        public bool DrawInterfereLabel { get; set; } = false;
        public bool DrawInterfereName { get; set; } = false;

        /// <summary>
        /// 绘制干扰源区域
        /// </summary>
        /// <param name="graphics"></param>
        private void drawInterfere(Graphics graphics)
        {
            if (mainModel.InterfereList == null || mainModel.InterfereList.Count == 0)
                return;

            if (DrawInterfereDis)
            {
                foreach (Interfere inter in mainModel.InterfereList)
                {
                    Color co = DrawInterfereColor;
                    //Pen pen = new Pen(Color.FromArgb(100, co), 4)
                    Brush brsh = new SolidBrush(Color.FromArgb(100, co));

                    DbPoint dPoint = new DbPoint(inter.Ilongitude, inter.Ilatitude);
                    PointF point;
                    gisAdapter.ToDisplay(dPoint, out point);

                    Cell cell = mainModel.CellManager.GetCurrentCell(inter.Icellid);
                    if (cell != null)
                    {
                        Pen flyPen = new Pen(Color.CadetBlue, 1);
                        DbPoint dPoint2 = new DbPoint(cell.Longitude, cell.Latitude);
                        PointF point2;
                        gisAdapter.ToDisplay(dPoint2, out point2);
                        graphics.DrawLine(flyPen, point, point2);
                    }

                    graphics.TranslateTransform(point.X, point.Y);
                    graphics.ScaleTransform((float)(10000 / mapScale), (float)(10000 / mapScale));
                    graphics.FillPath(new SolidBrush(Color.Green), planBTSPath);
                    RectangleF rect;
                    rect = new RectangleF(-DrawInterfereSizi, -DrawInterfereSizi, DrawInterfereSizi * 2, DrawInterfereSizi * 2);
                    //graphics.DrawEllipse(pen, rect)
                    graphics.FillEllipse(brsh, rect);
                    if (DrawInterfereName)
                    {
                        Font f = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
                        Brush brshFount = new SolidBrush(Color.Blue);
                        graphics.DrawString(inter.Straddr, f, brshFount, rect);
                    }
                }
            }
        }
        #endregion
        #endregion

        #region Paint
        Dictionary<Cell, List<Net.GridItem>> scanInterfereCells = null;
        public List<Brush> BrushesScanInterfere { get; private set; } = new List<Brush>();
        public List<Pen> PensScanInterfere { get; private set; } = new List<Pen>();
        public bool DrawAlarm { get; set; } = true;
        public int ZoomScaleAlarm { get; set; } = 2;
        public bool DrawLineDonarCell2Repeater { get; set; } = true;

        #region Cell设置
        private static List<PathGradientBrush> cellHighlightPathGradientBrushs = new List<PathGradientBrush>();
        public List<PathGradientBrush> CellHighlightPathGradientBrushs
        {
            get { return cellHighlightPathGradientBrushs; }
        }

        private static List<GraphicsPath> cellHighlightPaths = new List<GraphicsPath>();
        public List<GraphicsPath> CellHighlightPaths
        {
            get { return cellHighlightPaths; }
        }
        
        public bool DrawCover { get; set; } = false;

        private void paintCell(Cell cell, Graphics graphics, double scale)
        {
            if (cell.Antennas.Count == 0)
            {
                return;
            }
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            Brush brush = BrushGSM900Cell;
            Pen penScanInterfere = new Pen(Color.AliceBlue);
            bool drawScanInterfereCell = false;
            int index = 0;
            bool circle = false;
            initCellIndexBrush(cell, ref brush, ref index, ref circle);
            setBrushByCellType(cell, ref brush, ref penScanInterfere, ref drawScanInterfereCell);

            bool alarmCell = false;
            if (DrawAlarm && mainModel.OutServiceInfoManager.isContainSnapShot(OutServiceInfo.NetType.GSM, cell))
            {
                brush = BrushAlarm;
                alarmCell = true;
            }

            brush = setCellMultiCovListColorBrush(cell, brush);
            brush = setCellRedundantCovListClolrBrush(cell, brush);

            List<PathGradientBrush> pathGradientBrushs;
            List<GraphicsPath> paths;
            setPathAndGradientBrush(cell, out pathGradientBrushs, out paths);

            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(cell.Direction - 90);
            drawCoverCell(cell, graphics, index, circle, pathGradientBrushs, paths);

            if (curMapType == LayerMapType.MTGis)
            {
                scale = getDisplayScale(scale);
            }
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));
            if (alarmCell)
            {
                graphics.ScaleTransform(ZoomScaleAlarm, ZoomScaleAlarm);
            }
            GraphicsPath path = paths[index];
            graphics.FillPath(brush, path);
            graphics.DrawPath(Pens.LightSteelBlue, path);
            drawSelectedCellPath(cell, graphics, path);
            graphics.ResetTransform();

            drawSelectedCellRepeater(cell, graphics);
            drawScanInterfereCellByGrid(cell, graphics, penScanInterfere, drawScanInterfereCell);
        }

        private void drawScanInterfereCellByGrid(Cell cell, Graphics graphics, Pen penScanInterfere, bool drawScanInterfereCell)
        {
            if (curMapType == LayerMapType.MTGis && drawScanInterfereCell)
            {
                List<Net.GridItem> gridList = scanInterfereCells[cell];
                foreach (Net.GridItem grid in gridList)
                {
                    DbPoint dPointCell = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                    PointF pointCell;
                    gisAdapter.ToDisplay(dPointCell, out pointCell);
                    DbPoint dPointGrid = new DbPoint(grid.CenterLng, grid.CenterLat);
                    PointF pointGrid;
                    gisAdapter.ToDisplay(dPointGrid, out pointGrid);
                    if (pointCell.X - pointGrid.X > -10000 && pointCell.X - pointGrid.X < 10000 && pointCell.Y - pointGrid.Y > -10000 && pointCell.Y - pointGrid.Y < 10000)
                    {
                        graphics.DrawLine(penScanInterfere, pointGrid.X, pointGrid.Y, pointCell.X, pointCell.Y);
                    }
                }
            }
        }

        private void drawSelectedCellRepeater(Cell cell, Graphics graphics)
        {
            if (DrawLineDonarCell2Repeater && DrawRepeater && (mainModel.SelectedCell == cell || mainModel.SelectedCells.Contains(cell)))
            {
                foreach (Repeater repeater in cell.Repeaters)
                {
                    if (repeater.Current != null)
                    {
                        DbPoint dPointCell = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                        PointF pointCell;
                        gisAdapter.ToDisplay(dPointCell, out pointCell);
                        DbPoint dPointRepeater = new DbPoint(repeater.Current.Longitude, repeater.Current.Latitude);
                        PointF pointRepeater;
                        gisAdapter.ToDisplay(dPointRepeater, out pointRepeater);
                        drawValidRepeaterByCellType(cell, graphics, pointCell, pointRepeater);
                    }
                }
            }
        }

        private void drawValidRepeaterByCellType(Cell cell, Graphics graphics, PointF pointCell, PointF pointRepeater)
        {
            if (pointCell.X - pointRepeater.X > -10000 && pointCell.X - pointRepeater.X < 10000 && pointCell.Y - pointRepeater.Y > -10000 && pointCell.Y - pointRepeater.Y < 10000)
            {
                switch (cell.CellType)
                {
                    case MainOrNBCell.Other:
                    case MainOrNBCell.MainCell:
                        graphics.DrawLine(PenSelected, pointRepeater.X, pointRepeater.Y, pointCell.X, pointCell.Y);
                        break;
                    case MainOrNBCell.NBCell:
                        graphics.DrawLine(PenSelectedNB, pointRepeater.X, pointRepeater.Y, pointCell.X, pointCell.Y);
                        break;
                }
            }
        }

        private void drawSelectedCellPath(Cell cell, Graphics graphics, GraphicsPath path)
        {
            if (mainModel.SelectedCell == cell || mainModel.SelectedCells.Contains(cell))
            {
                switch (cell.CellType)
                {
                    case MainOrNBCell.Other:
                    case MainOrNBCell.MainCell:
                        graphics.DrawPath(PenSelected, path);
                        break;
                    case MainOrNBCell.NBCell:
                        graphics.DrawPath(PenSelectedNB, path);
                        break;
                }
            }
        }

        private void drawCoverCell(Cell cell, Graphics graphics, int index, bool circle, List<PathGradientBrush> pathGradientBrushs, List<GraphicsPath> paths)
        {
            if (DrawCover && (mainModel.SelectedCell == cell || mainModel.SelectedCells.Contains(cell)))
            {
                graphics.ScaleTransform((float)(50000 / mapScale), (float)(50000 / mapScale));
                PathGradientBrush pathGradientBrush = pathGradientBrushs[index];
                pathGradientBrush.CenterColor = ColorCover;
                pathGradientBrush.SurroundColors = new Color[] { Color.FromArgb(0x00, ColorCover) };
                graphics.FillPath(pathGradientBrush, paths[index]);
                graphics.ScaleTransform((float)(mapScale / 50000), (float)(mapScale / 50000));
                if (!circle)
                {
                    graphics.ScaleTransform(1, 0.5F);
                }
            }
        }

        private void setPathAndGradientBrush(Cell cell, out List<PathGradientBrush> pathGradientBrushs, out List<GraphicsPath> paths)
        {
            if (mainModel.CellHighLightAll)
            {
                pathGradientBrushs = cellHighlightPathGradientBrushs;
                paths = cellHighlightPaths;
            }
            else
            {
                if (mainModel.CellHighlightList.Contains(cell))
                {
                    pathGradientBrushs = cellHighlightPathGradientBrushs;
                    paths = cellHighlightPaths;
                }
                else
                {
                    pathGradientBrushs = cellPathGradientBrushs;
                    paths = cellPaths;
                }
            }
        }

        private Brush setCellRedundantCovListClolrBrush(Cell cell, Brush brush)
        {
            if (curMapType == LayerMapType.MTGis && mainModel.CellRedundantCovList.Count > 0)
            {
                foreach (CellRedundantCoverageInfo cellRedundantCov in mainModel.CellRedundantCovList)
                {
                    if (cell.Equals(cellRedundantCov.Cell))
                    {
                        Color curColor = mainModel.GetCellRedundantCovColor(cellRedundantCov.RedundantLevel);
                        if (curColor != Color.Empty)
                        {
                            brush = new SolidBrush(curColor);
                        }
                        break;
                    }
                }
            }

            return brush;
        }

        private Brush setCellMultiCovListColorBrush(Cell cell, Brush brush)
        {
            if (curMapType == LayerMapType.MTGis && mainModel.CellMultiCovList.Count > 0)
            {
                if (mainModel.CellMultiCovMCellSubCellDic.Count > 0)
                {
                    brush = setBrushByCellMultiCovMCellSubCellDic(cell, brush);
                }
                else
                {
                    brush = setBrushByCellMultiCovList(cell, brush);
                }
            }

            return brush;
        }

        private Brush setBrushByCellMultiCovList(Cell cell, Brush brush)
        {
            foreach (CellMultiCoverageInfo cellMultiCov in mainModel.CellMultiCovList)
            {
                if (cell.Equals(cellMultiCov.Cell))
                {
                    Color curColor;
                    if (mainModel.ShowCellCoverAbsLevel)
                    {
                        curColor = mainModel.GetCellMultiCovColor(cellMultiCov.AbsLevel);
                    }
                    else
                    {
                        curColor = mainModel.GetCellMultiCovColor(cellMultiCov.AvgMultiCovLev);
                    }
                    if (curColor != Color.Empty)
                    {
                        brush = new SolidBrush(curColor);
                    }
                    break;
                }
            }

            return brush;
        }

        private Brush setBrushByCellMultiCovMCellSubCellDic(Cell cell, Brush brush)
        {
            Color bClr = Color.Empty;
            List<Cell> cells = new List<Cell>();
            foreach (Cell mCell in mainModel.CellMultiCovMCellSubCellDic.Keys)
            {
                cells = mainModel.CellMultiCovMCellSubCellDic[mCell];
                if (mCell == cell)
                {
                    bClr = Color.Red;
                    break;
                }
            }
            if (bClr == Color.Empty)
            {
                foreach (Cell subCell in cells)
                {
                    if (subCell == cell)
                    {
                        bClr = Color.Cyan;
                        break;
                    }
                }
            }
            if (bClr != Color.Empty)
            {
                brush = new SolidBrush(bClr);
            }

            return brush;
        }

        #region setCellBrush
        private void setBrushByCellType(Cell cell, ref Brush brush, ref Pen penScanInterfere, ref bool drawScanInterfereCell)
        {
            if (DrawServer)
            {
                brush = setServerBrush(cell, brush);
            }
            if (mainModel.CoBCCHCells.Contains(cell))
            {
                brush = BrushCoBCCH;
            }
            else if (mainModel.CoTCHCells.Contains(cell))
            {
                brush = BrushCoTCH;
            }
            else if (mainModel.AdjBCCHCells.Contains(cell))
            {
                brush = BrushAdjBCCH;
            }
            else if (mainModel.AdjTCHCells.Contains(cell))
            {
                brush = BrushAdjTCH;
            }
            else if (mainModel.CoBSICCells.Contains(cell))
            {
                brush = BrushCoBSIC;
            }
            else if (mainModel.NeighbourEachOtherCells.Contains(cell))
            {
                brush = BrushNeighbourEachOther;
            }
            else if (mainModel.NeighbourCells.Contains(cell))
            {
                brush = BrushNeighbour;
            }
            else if (mainModel.TDNeighbourCells.Contains(cell))
            {
                brush = setTDNeighbourCellsBrush(brush);
            }
            else if (curMapType == LayerMapType.MTGis && mainModel.LTENeighbourCells.Contains(cell))
            {
                brush = setLTENeighbourCellsBrush(brush);
            }
            else if (curMapType == LayerMapType.MTGis && scanInterfereCells != null && scanInterfereCells.Count > 0)
            {
                setScanInterfereCellsBrush(cell, ref brush, ref penScanInterfere, ref drawScanInterfereCell);
            }
        }

        private Brush setTDNeighbourCellsBrush(Brush brush)
        {
            if (curMapType == LayerMapType.MTGis)
            {
                //brush = BrushTDNeighbour;    //画刷也改成TD小区图层上的2G单向    by ZLR
                brush = new SolidBrush(mainModel.MainForm.GetMapForm().GetTDCellLayer().ColorNeighbour2G);
            }
            else if (curMapType == LayerMapType.Google)
            {
                brush = BrushTDNeighbour;
            }

            return brush;
        }

        private Brush setLTENeighbourCellsBrush(Brush brush)
        {
            Brush tmpBrush = mainModel.MainForm.GetMapForm().GetLTECellLayer().BrushNeighbourCell;
            if (tmpBrush != null)
            {
                brush = tmpBrush;
            }

            return brush;
        }

        private void setScanInterfereCellsBrush(Cell cell, ref Brush brush, ref Pen penScanInterfere, ref bool drawScanInterfereCell)
        {
            int iLoop = 0;
            foreach (KeyValuePair<Cell, List<Net.GridItem>> keyValue in scanInterfereCells)
            {
                if (keyValue.Key == cell)
                {
                    brush = BrushesScanInterfere[iLoop];
                    penScanInterfere = PensScanInterfere[iLoop];
                    drawScanInterfereCell = true;
                    break;
                }
                iLoop++;
            }
        }

        private Brush setServerBrush(Cell cell, Brush brush)
        {
            for (int i = 0; i < mainModel.ServerCells.Count; i++)
            {
                if (mainModel.ServerCells[i] == cell)
                {
                    brush = BrushesServer[i];
                    break;
                }
            }

            return brush;
        }
        #endregion

        private void initCellIndexBrush(Cell cell, ref Brush brush, ref int index, ref bool circle)
        {
            //关于path对应index的选取，请看 MapFormCellLayer静态构造方法注释
            if (cell.BandType == BTSBandType.GSM900 && cell.DirectionType == AntennaDirectionType.Omni)
            {
                index = 0;
                brush = BrushGSM900Cell;
                circle = true;
            }
            else if (cell.BandType == BTSBandType.GSM900 && cell.DirectionType == AntennaDirectionType.Beam)
            {
                index = 1;
                brush = BrushGSM900Cell;
            }
            else if (cell.BandType == BTSBandType.DSC1800 && cell.DirectionType == AntennaDirectionType.Omni)
            {
                index = 2;
                brush = BrushDSC1800Cell;
                circle = true;
            }
            else if (cell.BandType == BTSBandType.DSC1800 && cell.DirectionType == AntennaDirectionType.Beam)
            {
                index = 3;
                brush = BrushDSC1800Cell;
            }
            else if (cell.BandType == BTSBandType.CoSite && cell.DirectionType == AntennaDirectionType.Beam)
            {
                index = 4;
                brush = BrushCoSiteCell;
            }
        }
        #endregion

        #region Antenna设置
        public List<GraphicsPath> AntennaHighlightPaths { get; private set; } = new List<GraphicsPath>();
        private void paintAntenna(Antenna antenna, Graphics graphics, double scale)
        {
            DbPoint dPoint = new DbPoint(antenna.Longitude, antenna.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            Brush brush = BrushGSM900Antenna;
            Pen ersAntennaPen = new Pen(Brushes.Gray, 1.0f);
            ersAntennaPen.DashStyle = DashStyle.Dash; int index = 0;
            bool isTDneighbour = false;
            bool isServer = false;
            bool isClique = false;
            bool isCluster = false;
            bool isClusterInterference = false;
            InitAntennaIndexBrush(antenna, ref brush, ref index);

            setBrushByServerCells(antenna, ref brush, ref isServer);

            brush = getBrushByCellMultiCov(antenna, brush);

            brush = getBrushByhNeighbour(antenna, brush);

            setBrushByTDneighbour(antenna, ref brush, ref isTDneighbour);

            brush = getBrushByModelInfo(antenna, brush);

            setBrushByClusterAnalysisSet(antenna, ref brush, ref isClique, ref isCluster);

            brush = getBrushByJamorgcells(antenna, brush);

            setClusterInterferenceInfo(antenna, ref brush, ref isClusterInterference);

            bool alarmCell = false;
            if (DrawAlarm)
            {
                bool isContain = mainModel.OutServiceInfoManager.isContainSnapShot(OutServiceInfo.NetType.GSM, antenna.BelongCell);
                if (isContain)
                {
                    brush = BrushAlarm;
                    alarmCell = true;
                }
            }

            List<GraphicsPath> paths = getCellHighLighPath(antenna);

            Pen penScanInterfere = new Pen(Color.AliceBlue);
            bool drawScanInterfereCell = false;
            setScanInterfereInfo(antenna, ref brush, ref penScanInterfere, ref drawScanInterfereCell);

            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(antenna.Direction - 90);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));
            if (alarmCell)
            {
                graphics.ScaleTransform(ZoomScaleAlarm, ZoomScaleAlarm);
            }
            GraphicsPath path = paths[index];
            graphics.FillPath(brush, path);
            drawTDneighbourPath(graphics, isTDneighbour, path);
            if (isServer)
            {
                graphics.DrawPath(new Pen(brush, 5), path);
            }
            if (isClique || isCluster)
            {
                graphics.DrawPath(new Pen(brush, 2), path);
            }
            if (isClusterInterference)
            {
                graphics.DrawPath(new Pen(brush, 2), path);
            }

            drawSelectedCellPath(antenna, graphics, path);

            drawAntennaRepeater(antenna, graphics);

            paintScanInterfereCell(antenna, graphics, penScanInterfere, drawScanInterfereCell);
        }

        private void setBrushByServerCells(Antenna antenna, ref Brush brush, ref bool isServer)
        {
            if (DrawServer)
            {
                if (curMapType == LayerMapType.MTGis)
                {
                    for (int i = 0; i < mainModel.ServerCells.Count; i++)
                    {
                        if (antenna.BelongCell == mainModel.ServerCells[i])
                        {
                            brush = new SolidBrush(Color.Red);     //统一连接到选中小区的飞线颜色
                            isServer = true;
                            break;
                        }
                    }
                }
                else
                {
                    int tempI = mainModel.ServerCells.IndexOf(antenna.BelongCell);
                    if (-1 != tempI)
                    {
                        brush = BrushesServer[tempI];
                        isServer = true;
                    }
                }
            }
        }

        private Brush getBrushByCellMultiCov(Antenna antenna, Brush brush)
        {
            if (curMapType == LayerMapType.MTGis && mainModel.CellMultiCovList.Count > 0)
            {
                Color bClr = Color.Empty;
                List<Cell> cells = new List<Cell>();
                if (mainModel.CellMultiCovMCellSubCellDic.Count > 0)
                {
                    setBrushByCellMultiCovMCellSubCellDic(antenna, ref brush, ref bClr, ref cells);
                }
                else
                {
                    brush = getBrushByCellMultiCovList(antenna, brush);
                }
            }

            return brush;
        }

        private Brush getBrushByCellMultiCovList(Antenna antenna, Brush brush)
        {
            foreach (CellMultiCoverageInfo cellMultiCov in mainModel.CellMultiCovList)
            {
                if (antenna.BelongCell == cellMultiCov.Cell)
                {
                    Color curColor;
                    if (mainModel.ShowCellCoverAbsLevel)
                    {
                        curColor = mainModel.GetCellMultiCovColor(cellMultiCov.AbsLevel);
                    }
                    else
                    {
                        curColor = mainModel.GetCellMultiCovColor(cellMultiCov.AvgMultiCovLev);
                    }
                    if (curColor != Color.Empty)
                    {
                        brush = new SolidBrush(curColor);
                    }
                    break;
                }
            }

            return brush;
        }

        private void setBrushByCellMultiCovMCellSubCellDic(Antenna antenna, ref Brush brush, ref Color bClr, ref List<Cell> cells)
        {
            foreach (Cell mCell in mainModel.CellMultiCovMCellSubCellDic.Keys)
            {
                cells = mainModel.CellMultiCovMCellSubCellDic[mCell];
                if (antenna.BelongCell == mCell)
                {
                    bClr = Color.Red;
                    break;
                }
            }
            if (bClr == Color.Empty)
            {
                foreach (Cell subCell in cells)
                {
                    if (antenna.BelongCell == subCell)
                    {
                        bClr = Color.Cyan;
                        break;
                    }
                }
            }
            if (bClr != Color.Empty)
            {
                brush = new SolidBrush(bClr);
            }
        }

        private Brush getBrushByhNeighbour(Antenna antenna, Brush brush)
        {
            foreach (Cell cell in mainModel.NeighbourCells)
            {
                if (antenna.BelongCell == cell)
                {
                    brush = BrushNeighbour;
                    break;
                }
            }
            foreach (Cell cell in mainModel.NeighbourEachOtherCells)
            {
                if (antenna.BelongCell == cell)
                {
                    brush = BrushNeighbourEachOther;
                    break;
                }
            }

            return brush;
        }

        private void setBrushByTDneighbour(Antenna antenna, ref Brush brush, ref bool isTDneighbour)
        {
            foreach (Cell cell in mainModel.TDNeighbourCells)     //见没被调用过,现用于TD小区的2G邻区   by ZLR
            {
                if (antenna.BelongCell == cell)
                {
                    if (curMapType == LayerMapType.MTGis)
                    {
                        //brush = BrushTDNeighbour;         //改为TD小区同层下的2G邻区的画刷   by ZLR
                        try
                        {
                            brush = new SolidBrush(mainModel.MainForm.GetMapForm().GetTDCellLayer().ColorNeighbour2G);
                        }
                        catch
                        {
                            //continue
                        }
                    }
                    else if (curMapType == LayerMapType.Google)
                    {
                        brush = BrushTDNeighbour;
                    }
                    isTDneighbour = true;
                    break;
                }
            }
        }

        private Brush getBrushByModelInfo(Antenna antenna, Brush brush)
        {
            foreach (Cell cell in mainModel.CoBSICCells)
            {
                if (antenna.BelongCell == cell)
                {
                    brush = BrushCoBSIC;
                    break;
                }
            }
            foreach (Cell cell in mainModel.AdjTCHCells)
            {
                if (antenna.BelongCell == cell)
                {
                    brush = BrushAdjTCH;
                    break;
                }
            }
            foreach (Cell cell in mainModel.AdjBCCHCells)
            {
                if (antenna.BelongCell == cell)
                {
                    brush = BrushAdjBCCH;
                    break;
                }
            }
            foreach (Cell cell in mainModel.CoTCHCells)
            {
                if (antenna.BelongCell == cell)
                {
                    brush = BrushCoTCH;
                    break;
                }
            }
            foreach (Cell cell in mainModel.CoBCCHCells)
            {
                if (antenna.BelongCell == cell)
                {
                    brush = BrushCoBCCH;
                    break;
                }
            }

            return brush;
        }

        private void setBrushByClusterAnalysisSet(Antenna antenna, ref Brush brush, ref bool isClique, ref bool isCluster)
        {
            foreach (Cell clustercell in mainModel.ClusterAnalysisSet.ClusterCellList)
            {
                if (antenna.BelongCell.ID == clustercell.ID)
                {
                    brush = BrushClusterCell;
                    isCluster = true;
                    break;
                }
            }

            foreach (Cell cliquecell in mainModel.ClusterAnalysisSet.CliqueCellList)
            {
                if (antenna.BelongCell.ID == cliquecell.ID)
                {
                    brush = BrushCliqueCell;
                    isClique = true;
                    break;
                }
            }

            brush = getBrushByDisCoStatDic(antenna, brush);

            if (mainModel.ClusterAnalysisSet.SourceCell != null && antenna.BelongCell.ID == mainModel.ClusterAnalysisSet.SourceCell.ID)
            {
                brush = BrushSourceTargetCells;
            }

            if (mainModel.ClusterAnalysisSet.TargetCell != null && antenna.BelongCell.ID == mainModel.ClusterAnalysisSet.TargetCell.ID)
            {
                brush = BrushSourceTargetCells;
            }
        }

        private Brush getBrushByDisCoStatDic(Antenna antenna, Brush brush)
        {
            foreach (Cell selectedcell in mainModel.ClusterAnalysisSet.DisCoStatDic.Keys)
            {
                if (antenna.BelongCell.ID == selectedcell.ID)
                {
                    brush = BrushMainCell;
                    break;
                }
            }

            foreach (Cell selectedcell in mainModel.ClusterAnalysisSet.DisCoStatDic.Keys)
            {
                foreach (Cell tempcell in mainModel.ClusterAnalysisSet.DisCoStatDic[selectedcell])
                {
                    if (antenna.BelongCell.ID == tempcell.ID)
                    {
                        brush = BrushDisCoStat;
                        break;
                    }
                }
            }

            return brush;
        }

        private Brush getBrushByJamorgcells(Antenna antenna, Brush brush)
        {
            List<Cell> jamorgcells = new List<Cell>();
            if (mainModel.JamOrgDic != null && mainModel.JamOrgDic.Keys.Count == 1)
            {
                foreach (Cell jamcell in mainModel.JamOrgDic.Keys)
                {
                    jamorgcells.Add(jamcell);
                    foreach (Cell orgcell in mainModel.JamOrgDic[jamcell])
                    {
                        jamorgcells.Add(orgcell);
                    }
                }
            }
            foreach (Cell cell in jamorgcells)
            {
                if (antenna.BelongCell == cell)
                {
                    brush = BrushJamorgcell;
                    break;
                }
            }

            return brush;
        }

        private void setClusterInterferenceInfo(Antenna antenna, ref Brush brush, ref bool isClusterInterference)
        {
            if (mainModel.ClusterAnalysisSet.JamCellOnGIS != null && antenna.BelongCell.ID == mainModel.ClusterAnalysisSet.JamCellOnGIS.ID)
            {
                brush = BrushJamCell;
                isClusterInterference = true;
            }

            foreach (Cell orgCell in mainModel.ClusterAnalysisSet.OrgCellsOnGIS)
            {
                if (antenna.BelongCell.ID == orgCell.ID)
                {
                    brush = BrushOrgCells;
                    isClusterInterference = true;
                    break;
                }
            }
        }

        private void setScanInterfereInfo(Antenna antenna, ref Brush brush, ref Pen penScanInterfere, ref bool drawScanInterfereCell)
        {
            if (curMapType == LayerMapType.MTGis && scanInterfereCells != null && scanInterfereCells.Count > 0)
            {
                int iLoop = 0;
                foreach (KeyValuePair<Cell, List<Net.GridItem>> keyValue in scanInterfereCells)
                {
                    if (antenna.BelongCell == keyValue.Key)
                    {
                        drawScanInterfereCell = true;
                        brush = BrushesScanInterfere[iLoop];
                        penScanInterfere = PensScanInterfere[iLoop];
                        break;
                    }
                    iLoop++;
                }
            }
        }

        private List<GraphicsPath> getCellHighLighPath(Antenna antenna)
        {
            List<GraphicsPath> paths;
            if (mainModel.CellHighLightAll)
            {
                paths = AntennaHighlightPaths;
            }
            else
            {
                paths = antennaPaths;
                foreach (Cell cell in mainModel.CellHighlightList)
                {
                    if (cell.Antennas[0] == antenna)
                    {
                        paths = AntennaHighlightPaths;
                        break;
                    }
                }
            }

            return paths;
        }

        private void drawTDneighbourPath(Graphics graphics, bool isTDneighbour, GraphicsPath path)
        {
            if (isTDneighbour)
            {
                if (curMapType == LayerMapType.MTGis)
                {
                    //graphics.DrawPath(new Pen(BrushTDNeighbour, 5), paths[index]);    //改为TD小区同层下的2G邻区的画刷    by ZLR
                    try
                    {
                        Brush brushTD2GNeighbour = new SolidBrush(mainModel.MainForm.GetMapForm().GetTDCellLayer().ColorNeighbour2G);
                        graphics.DrawPath(new Pen(brushTD2GNeighbour, 5), path);
                    }
                    catch
                    {
                        //continue
                    }
                }
                else if (curMapType == LayerMapType.Google)
                {
                    graphics.DrawPath(new Pen(BrushTDNeighbour, 5), path);
                }
            }
        }

        private void drawSelectedCellPath(Antenna antenna, Graphics graphics, GraphicsPath path)
        {
            if (antenna.BelongCell == mainModel.SelectedCell)
            {
                graphics.DrawPath(PenSelected, path);
            }
            else
            {
                foreach (Cell cell in mainModel.SelectedCells)
                {
                    if (antenna.BelongCell == cell)
                    {
                        switch (cell.CellType)
                        {
                            case MainOrNBCell.Other:
                            case MainOrNBCell.MainCell:
                                graphics.DrawPath(PenSelected, path);
                                break;
                            case MainOrNBCell.NBCell:
                                graphics.DrawPath(PenSelectedNB, path);
                                break;
                        }
                    }
                }
            }
        }

        private void drawAntennaRepeater(Antenna antenna, Graphics graphics)
        {
            graphics.ResetTransform();
            if (DrawLineDonarCell2Repeater && DrawRepeater && antenna.BelongCell == mainModel.SelectedCell)
            {
                Cell cell = antenna.BelongCell;
                DbPoint dPointCell = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                PointF pointCell;
                gisAdapter.ToDisplay(dPointCell, out pointCell);
                foreach (Repeater repeater in cell.Repeaters)
                {
                    if (repeater.Current != null)
                    {
                        DbPoint dPointRepeater = new DbPoint(repeater.Current.Longitude, repeater.Current.Latitude);
                        PointF pointRepeater;
                        gisAdapter.ToDisplay(dPointRepeater, out pointRepeater);
                        if (pointCell.X - pointRepeater.X > -10000 && pointCell.X - pointRepeater.X < 10000 && pointCell.Y - pointRepeater.Y > -10000 && pointCell.Y - pointRepeater.Y < 10000)
                        {
                            graphics.DrawLine(PenSelected, pointRepeater.X, pointRepeater.Y, pointCell.X, pointCell.Y);
                        }
                    }
                }
            }
        }

        private void paintScanInterfereCell(Antenna antenna, Graphics graphics, Pen penScanInterfere, bool drawScanInterfereCell)
        {
            if (curMapType == LayerMapType.MTGis && drawScanInterfereCell)
            {
                List<MasterCom.RAMS.Net.GridItem> gridList = new List<MasterCom.RAMS.Net.GridItem>();
                foreach (Cell cell in scanInterfereCells.Keys)
                {
                    if (antenna.BelongCell == cell)
                    {
                        gridList = scanInterfereCells[cell];
                    }
                }

                foreach (MasterCom.RAMS.Net.GridItem grid in gridList)
                {
                    DbPoint dPointCell = new DbPoint(antenna.EndPointLongitude, antenna.EndPointLatitude);
                    PointF pointCell;
                    gisAdapter.ToDisplay(dPointCell, out pointCell);
                    DbPoint dPointGrid = new DbPoint(grid.CenterLng, grid.CenterLat);
                    PointF pointGrid;
                    gisAdapter.ToDisplay(dPointGrid, out pointGrid);
                    if (pointCell.X - pointGrid.X > -10000 && pointCell.X - pointGrid.X < 10000 && pointCell.Y - pointGrid.Y > -10000 && pointCell.Y - pointGrid.Y < 10000)
                    {
                        graphics.DrawLine(penScanInterfere, pointGrid.X, pointGrid.Y, pointCell.X, pointCell.Y);
                    }
                }
            }
        }

        private void InitAntennaIndexBrush(Antenna antenna, ref Brush brush, ref int index)
        {
            if (antenna.BandType == BTSBandType.GSM900 && antenna.DirectionType == AntennaDirectionType.Omni)
            {
                index = 0;
                brush = BrushGSM900Antenna;
            }
            else if (antenna.BandType == BTSBandType.GSM900 && antenna.DirectionType == AntennaDirectionType.Beam)
            {
                index = 1;
                brush = BrushGSM900Antenna;
            }
            else if (antenna.BandType == BTSBandType.DSC1800 && antenna.DirectionType == AntennaDirectionType.Omni)
            {
                index = 2;
                brush = BrushDSC1800Antenna;
            }
            else if (antenna.BandType == BTSBandType.DSC1800 && antenna.DirectionType == AntennaDirectionType.Beam)
            {
                index = 3;
                brush = BrushDSC1800Antenna;
            }
            else if (antenna.BandType == BTSBandType.CoSite && antenna.DirectionType == AntennaDirectionType.Beam)
            {
                index = 4;
                brush = BrushCoSiteAntenna;
            }
        }
        #endregion

        #region Repeater设置=
        public int SizeRepeater { get; set; } = 16;

        private static System.Drawing.Image repeaterImage = System.Drawing.Image.FromFile(System.Windows.Forms.Application.StartupPath + @"\images\Repeater.gif");
        public System.Drawing.Image RepeaterImage
        {
            get { return repeaterImage; }
        }

        private void paintRepeater(Repeater repeater, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(repeater.Longitude, repeater.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            graphics.DrawImage(repeaterImage, -SizeRepeater / 2, -SizeRepeater / 2, SizeRepeater, SizeRepeater);
            if (repeater == mainModel.SelectedRepeater)
            {
                graphics.DrawRectangle(PenSelected, -SizeRepeater / 2, -SizeRepeater / 2, SizeRepeater, SizeRepeater);
            }
            graphics.ResetTransform();
            if (DrawLineDonarCell2Repeater && (DrawCell || DrawAntenna) && repeater == mainModel.SelectedRepeater && repeater.CurrentDonarCell != null)
            {
                Cell cell = repeater.CurrentDonarCell;
                DbPoint dPointCell = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                PointF pointCell;
                gisAdapter.ToDisplay(dPointCell, out pointCell);
                if (pointCell.X - point.X > -10000 && pointCell.X - point.X < 10000 && pointCell.Y - point.Y > -10000 && pointCell.Y - point.Y < 10000)
                {
                    graphics.DrawLine(PenSelected, point.X, point.Y, pointCell.X, pointCell.Y);
                }
            }
        }
        #endregion

        #region RepeaterLabel设置

        private Font fontRepeaterLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Regular);

        private void paintRepeaterLabel(Repeater repeater, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(repeater.Longitude, repeater.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string repeaterDes = repeater.Name;
            SizeF size = graphics.MeasureString(repeaterDes, fontRepeaterLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in DrawedRepeaterLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(repeaterDes, fontRepeaterLabel, Brushes.Black, 3, -size.Height / 2);
                DrawedRepeaterLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }
        #endregion

        #region BTS设置
        public int SizeBTS { get; set; } = 6;

        private void paintBTS(BTS bts, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            if (curMapType == LayerMapType.MTGis)
            {
                float ratio = CustomDrawLayer.GetTestPointDisplayRatio(mapScale);
                graphics.ScaleTransform(ratio, ratio);

                if (bts.BandType == BTSBandType.GSM900)
                {
                    graphics.DrawRectangle(PenGSM900BTS, -SizeBTS * ratio * 1500 / 2, -SizeBTS * ratio * 1500 / 2, SizeBTS * ratio * 1500, SizeBTS * ratio * 1500);
                }
                else if (bts.BandType == BTSBandType.CoSite)
                {
                    graphics.DrawRectangle(PenCoSiteBTS, -SizeBTS * 2f * ratio * 1500 / 2, -SizeBTS * 2f * ratio * 1500 / 2, SizeBTS * 2f * ratio * 1500, SizeBTS * 2f * ratio * 1500);
                }
                else
                {
                    graphics.DrawRectangle(PenDSC1800BTS, -SizeBTS * 1.5f * ratio * 1500 / 2, -SizeBTS * 1.5f * ratio * 1500 / 2, SizeBTS * 1.5f * ratio * 1500, SizeBTS * 1.5f * ratio * 1500);
                }

                if (mainModel.SelectedBTS == bts)
                {
                    graphics.DrawRectangle(new Pen(Color.Red, 120), -SizeBTS * ratio * 1700 / 2, -SizeBTS * ratio * 1700 / 2, SizeBTS * ratio * 1700, SizeBTS * ratio * 1700);
                }
            }
            else if (curMapType == LayerMapType.Google)
            {
                if (bts.BandType == BTSBandType.GSM900)
                {
                    graphics.DrawRectangle(PenGSM900BTS, -SizeBTS / 2, -SizeBTS / 2, SizeBTS, SizeBTS);
                }
                else if (bts.BandType == BTSBandType.CoSite)
                {
                    graphics.DrawRectangle(PenCoSiteBTS, -SizeBTS * 2f / 2, -SizeBTS * 2f / 2, SizeBTS * 2f, SizeBTS * 2f);
                }
                else
                {
                    graphics.DrawRectangle(PenDSC1800BTS, -SizeBTS * 1.5f / 2, -SizeBTS * 1.5f / 2, SizeBTS * 1.5f, SizeBTS * 1.5f);
                }
            }
            graphics.ResetTransform();
        }
        #endregion

        #region PlanBTS设置
        private static GraphicsPath planBTSPath { get; set; } = new GraphicsPath();
        public GraphicsPath PlanBTSPath
        {
            get { return planBTSPath; }
        }
        
        public int SizePlanBTS { get; set; } = 15;

        private static PointF[] planBTSPoints { get; set; }
        public PointF[] PlanBTSPoints
        {
            get { return planBTSPoints; }
        }

        private void initPlanBTSPath()
        {
            planBTSPath = new GraphicsPath();
            float radius = SizePlanBTS;
            float sin6 = (float)Math.Sin(Math.PI * 6 / 180);
            float cos6 = (float)Math.Cos(Math.PI * 6 / 180);
            float sin12 = (float)Math.Sin(Math.PI * 12 / 180);
            float cos12 = (float)Math.Cos(Math.PI * 12 / 180);
            float sin18 = (float)Math.Sin(Math.PI * 18 / 180);
            float cos18 = (float)Math.Cos(Math.PI * 18 / 180);
            float sin24 = (float)Math.Sin(Math.PI * 24 / 180);
            float cos24 = (float)Math.Cos(Math.PI * 24 / 180);
            float sin30 = (float)Math.Sin(Math.PI * 30 / 180);
            float cos30 = (float)Math.Cos(Math.PI * 30 / 180);
            float sin36 = (float)Math.Sin(Math.PI * 36 / 180);
            float cos36 = (float)Math.Cos(Math.PI * 36 / 180);
            float sin42 = (float)Math.Sin(Math.PI * 42 / 180);
            float cos42 = (float)Math.Cos(Math.PI * 42 / 180);
            float sin48 = cos42;
            float cos48 = sin42;
            float sin54 = cos36;
            float cos54 = sin36;
            float sin60 = cos30;
            float cos60 = sin30;

            planBTSPoints = new PointF[] 
                {
                    new PointF(0, 0), 
                    new PointF(-radius * sin30, -radius * cos30), 
                    new PointF(-radius * sin24, -radius * cos24), 
                    new PointF(-radius * sin18, -radius * cos18), 
                    new PointF(-radius * sin12, -radius * cos12), 
                    new PointF(-radius * sin6, -radius * cos6),
                    new PointF(radius * sin12, -radius * cos12), 
                    new PointF(radius * sin18, -radius * cos18), 
                    new PointF(radius * sin24, -radius * cos24), 
                    new PointF(radius * sin30, -radius * cos30),
                    new PointF(0, 0),
                    new PointF(radius, 0),
                    new PointF(radius * cos6, radius * sin6),
                    new PointF(radius * cos12, radius * sin12), 
                    new PointF(radius * cos18, radius * sin18), 
                    new PointF(radius * cos24, radius * sin24), 
                    new PointF(radius * cos30, radius * sin30),
                    new PointF(radius * cos36, radius * sin36),
                    new PointF(radius * cos42, radius * sin42), 
                    new PointF(radius * cos48, radius * sin48), 
                    new PointF(radius * cos54, radius * sin54), 
                    new PointF(radius * cos60, radius * sin60),
                    new PointF(0, 0),
                    new PointF(-radius, 0),
                    new PointF(-radius * cos6, radius * sin6),
                    new PointF(-radius * cos12, radius * sin12), 
                    new PointF(-radius * cos18, radius * sin18), 
                    new PointF(-radius * cos24, radius * sin24), 
                    new PointF(-radius * cos30, radius * sin30),
                    new PointF(-radius * cos36, radius * sin36),
                    new PointF(-radius * cos42, radius * sin42), 
                    new PointF(-radius * cos48, radius * sin48), 
                    new PointF(-radius * cos54, radius * sin54), 
                    new PointF(-radius * cos60, radius * sin60)
                };
            planBTSPath.AddPolygon(planBTSPoints);
        }

        private void paintPlanBTS(PlanBTS bts, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);

            graphics.ScaleTransform((float)(10000 / mapScale), (float)(10000 / mapScale));
            graphics.FillPath(new SolidBrush(ColorPlanBTS), planBTSPath);
            if (bts == mainModel.SelectedPlanBTS)
            {
                RectangleF rect;
                rect = new RectangleF(-SizePlanBTS, -SizePlanBTS, SizePlanBTS * 2, SizePlanBTS * 2);
                graphics.DrawEllipse(PenSelected, rect);
            }
            graphics.ResetTransform();
        }
        #endregion

        #region PlanBTSLabel设置
        public bool DrawPlanBTSName { get; set; } = false;
        public bool DrawPlanBTSLongi { get; set; } = false;
        public bool DrawPlanBTSLati { get; set; } = false;
        public bool DrawPlanBTSNo { get; set; } = false;
        public bool DrawPlanBTSType { get; set; } = false;
        public bool DrawPlanBTSReason { get; set; } = false;
        public bool DrawPlanBTSProgress { get; set; } = false;
        public bool DrawPlanBTSAddr { get; set; } = false;
        public bool DrawPlanBTSComment { get; set; } = false;

        public string getPlanBTSLabelDes(PlanBTS bts, int length)
        {
            string des = "";
            if (DrawPlanBTSName)
            {
                des += bts.Name + " ";
            }
            if (DrawPlanBTSNo)
            {
                des += bts.No + " ";
            }
            if (DrawPlanBTSLongi)
            {
                des += bts.Longitude.ToString() + " ";
            }
            if (DrawPlanBTSLati)
            {
                des += bts.Latitude.ToString() + " ";
            }
            if (DrawPlanBTSType)
            {
                des += bts.TypeDes + " ";
            }
            if (DrawPlanBTSReason)
            {
                des += bts.Reason + " ";
            }
            if (DrawPlanBTSProgress)
            {
                des += bts.Progress + " ";
            }
            if (DrawPlanBTSAddr)
            {
                des += bts.Address + " ";
            }
            if (DrawPlanBTSComment)
            {
                des += bts.Comment + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }
        
        public Font FontPlanBTSLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);

        private void paintPlanBTSLabel(PlanBTS bts, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string btsDes = getPlanBTSLabelDes(bts, 100);
            SizeF size = graphics.MeasureString(btsDes, FontPlanBTSLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in DrawedPlanBTSLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(btsDes, FontPlanBTSLabel, Brushes.Black, 3, -size.Height / 2);
                DrawedPlanBTSLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }
        #endregion

        #region BTSLabel设置
        public bool DrawBTSName { get; set; } = true;
        public bool DrawBTSMSC { get; set; } = false;
        public bool DrawBTSBSC { get; set; } = false;
        public bool DrawBTSLongitude { get; set; } = false;
        public bool DrawBTSLatitude { get; set; } = false;
        public bool DrawBTSType { get; set; } = false;
        public bool DrawBTSDescription { get; set; } = false;

        public string getBTSLabelDes(BTS bts, int length)
        {
            string des = "";
            if (DrawBTSName)
            {
                des += bts.Name + " ";
            }
            if (DrawBTSMSC)
            {
                des += bts.BelongBSC.BelongMSC.Name + " ";
            }
            if (DrawBTSBSC)
            {
                des += bts.BelongBSC.Name + " ";
            }
            if (DrawBTSLongitude)
            {
                des += bts.Longitude.ToString() + " ";
            }
            if (DrawBTSLatitude)
            {
                des += bts.Latitude.ToString() + " ";
            }
            if (DrawBTSType)
            {
                des += bts.TypeDescription + " ";
            }
            if (DrawBTSDescription)
            {
                des += bts.Description + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }
        
        public Font FontBTSLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);

        private void paintBTSLabel(BTS bts, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string btsDes = getBTSLabelDes(bts, 100);
            SizeF size = graphics.MeasureString(btsDes, FontBTSLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in DrawedBTSLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(btsDes, FontBTSLabel, Brushes.Black, 3, -size.Height / 2);
                DrawedBTSLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }
        #endregion

        #region CellLabel设置
        public bool DrawCellName { get; set; } = true;
        public bool DrawCellCode { get; set; } = false;
        public bool DrawCellLAC { get; set; } = false;
        public bool DrawCellCI { get; set; } = false;
        public bool DrawCellBCCH { get; set; } = false;
        public bool DrawCellBSIC { get; set; } = false;
        public bool DrawCellTCH { get; set; } = false;
        public bool DrawCellHOP { get; set; } = false;

        public string getCellLabelDes(Cell cell, int length)
        {
            string des = "";
            if (DrawCellName)
            {
                des += cell.Name + " ";
            }
            if (DrawCellCode)
            {
                des += cell.Code + " ";
            }
            if (DrawCellLAC)
            {
                des += cell.LAC.ToString() + " ";
            }
            if (DrawCellCI)
            {
                des += cell.CI.ToString() + " ";
            }
            if (DrawCellBCCH)
            {
                des += cell.BCCH.ToString() + " ";
            }
            if (DrawCellBSIC)
            {
                des += cell.BSIC.ToString() + " ";
            }
            if (DrawCellTCH)
            {
                des += cell.TCHDescription + " ";
            }
            if (DrawCellHOP)
            {
                des += cell.HOP.ToString() + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }
        
        public Font FontCellLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);

        private void paintCellLabel(Cell cell, Graphics graphics)
        {
            Font fontLabel = FontCellLabel;
            DbPoint dPoint = null;
            if (cell.Antennas.Count > 0)
            {
                dPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
            }
            else
            {
                dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            }
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string cellDes = getCellLabelDes(cell, 100);
            SizeF size = graphics.MeasureString(cellDes, fontLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in DrawedCellLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(cellDes, fontLabel, Brushes.Black, 3, -size.Height / 2);
                DrawedCellLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }
        #endregion

        #region AntennaLabel设置
        public bool DrawAntennaGroup { get; set; } = false;
        public bool DrawAntennaSN { get; set; } = false;
        public bool DrawAntennaLongitude { get; set; } = false;
        public bool DrawAntennaLatitude { get; set; } = false;
        public bool DrawAntennaDirectionType { get; set; } = false;
        public bool DrawAntennaDirection { get; set; } = false;
        public bool DrawAntennaDownward { get; set; } = false;
        public bool DrawAntennaAltitude { get; set; } = false;
        public bool DrawAntennaGain { get; set; } = false;
        public bool DrawAntennaEIRP { get; set; } = false;
        public bool DrawAntennaDescription { get; set; } = false;

        public string getAntennaLabelDes(Antenna antenna, int length)
        {
            string des = "";
            if (DrawAntennaGroup)
            {
                des += antenna.AnttenaGroupSN.ToString() + " ";
            }
            if (DrawAntennaSN)
            {
                des += antenna.SN.ToString() + " ";
            }
            if (DrawAntennaLongitude)
            {
                des += antenna.Longitude.ToString() + " ";
            }
            if (DrawAntennaLatitude)
            {
                des += antenna.Latitude.ToString() + " ";
            }
            if (DrawAntennaDirectionType)
            {
                des += antenna.DirectionType.ToString() + " ";
            }
            if (DrawAntennaDirection)
            {
                des += antenna.Direction.ToString() + " ";
            }
            if (DrawAntennaDownward)
            {
                des += antenna.Downward.ToString() + " ";
            }
            if (DrawAntennaAltitude)
            {
                des += antenna.Altitude.ToString() + " ";
            }
            if (DrawAntennaGain)
            {
                des += antenna.Gain.ToString() + " ";
            }
            if (DrawAntennaEIRP)
            {
                des += antenna.EIRP.ToString() + " ";
            }
            if (DrawAntennaDescription)
            {
                des += antenna.Description + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }
        
        public Font FontAntennaLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);

        private void paintAntennaLabel(Antenna antenna, Graphics graphics)
        {
            Font fontLabel = FontAntennaLabel;
            DbPoint dPoint = new DbPoint(antenna.EndPointLongitude, antenna.EndPointLatitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string antennaDes = getAntennaLabelDes(antenna, 100);
            SizeF size = graphics.MeasureString(antennaDes, fontLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in DrawedAntennaLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(antennaDes, fontLabel, Brushes.Black, 3, -size.Height / 2);
                DrawedAntennaLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }
        #endregion

        #region PlanningLabel设置
        public bool DrawCoBCCH { get; set; } = true;
        public bool DrawCoTCH { get; set; } = true;
        public bool DrawAdjBCCH { get; set; } = true;
        public bool DrawAdjTCH { get; set; } = true;
        public bool DrawPlanningLabel { get; set; } = false;
        public bool ShowSimple { get; set; } = true;//按简单模式显示
        public bool ShowDetail { get; set; } = false;//按详细模式显示
        public bool ShowNone { get; set; } = false;//按颜色模式显示
        public Font FontPlanningLabel { get; set; } = new Font(new FontFamily("宋体"), 9);
        public Color ColorPlanningLabel { get; set; } = Color.Black;//标签文字颜色

        private void paintPlanningLabel(Cell cell, Graphics graphics)
        {
            Brush brush = getPlanningLabelBrush(cell);
            if (brush == null && !DrawPlanningLabel)
            {
                return;
            }
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            int index = getPlanningLabelIndex(cell);
            RectangleF bounds = cellPaths[index].GetBounds();
            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(cell.Direction - 90);
            graphics.ScaleTransform((float)(10000 / mapScale), (float)(10000 / mapScale));
            graphics.TranslateTransform(bounds.X + bounds.Width, bounds.Y + bounds.Height / 2);
            graphics.RotateTransform(-(cell.Direction - 90));
            if (mapScale < 9000)
            {
                graphics.ScaleTransform(1 / (float)(10000 / mapScale), 1 / (float)(10000 / mapScale));
                graphics.ScaleTransform((float)(10000.0 / 9000.0), (float)(10000.0 / 9000.0));
            }
            if (ShowSimple)
            {
                showPlanningLabelSimple(cell, graphics, brush);
            }
            else if (ShowDetail)
            {
                showPlanningLabelDetail(cell, graphics, brush);
            }

            graphics.ResetTransform();
        }

        private Brush getPlanningLabelBrush(Cell cell)
        {
            Brush brush = null;
            if (DrawCoBCCH && mainModel.CoBCCHCells.Contains(cell))
            {
                brush = BrushCoBCCH;
            }
            else if (DrawCoTCH && mainModel.CoTCHCells.Contains(cell))
            {
                brush = BrushCoTCH;
            }
            else if (DrawAdjBCCH && mainModel.AdjBCCHCells.Contains(cell))
            {
                brush = BrushAdjBCCH;
            }
            else if (DrawAdjTCH && mainModel.AdjTCHCells.Contains(cell))
            {
                brush = BrushAdjTCH;
            }

            return brush;
        }

        private int getPlanningLabelIndex(Cell cell)
        {
            int index = 0;
            if (cell.BandType == BTSBandType.GSM900 && cell.DirectionType == AntennaDirectionType.Omni)
            {
                index = 0;
            }
            else if (cell.BandType == BTSBandType.GSM900 && cell.DirectionType == AntennaDirectionType.Beam)
            {
                index = 1;
            }
            else if (cell.BandType == BTSBandType.DSC1800 && cell.DirectionType == AntennaDirectionType.Omni)
            {
                index = 2;
            }
            else if (cell.BandType == BTSBandType.DSC1800 && cell.DirectionType == AntennaDirectionType.Beam)
            {
                index = 3;
            }
            else if (cell.BandType == BTSBandType.CoSite && cell.DirectionType == AntennaDirectionType.Beam)
            {
                index = 4;
            }

            return index;
        }

        private void showPlanningLabelSimple(Cell cell, Graphics graphics, Brush brush)
        {
            List<string> descriptions = mainModel.GetLabel(cell);
            SizeF size = graphics.MeasureString(descriptions[0], FontPlanningLabel);
            float x = 0;
            float y = 0;
            float offset = 15;
            getCoordinate(cell, size.Width, size.Height, ref x, ref y);
            string key;
            if (cell.Direction == -999)
            {
                key = cell.BelongBTS.Longitude.ToString() + "_" + cell.BelongBTS.Latitude.ToString() + "_" + cell.Direction.ToString();
            }
            else
            {
                key = cell.BelongBTS.Longitude.ToString() + "_" + cell.BelongBTS.Latitude.ToString() + "_" + cell.Direction.ToString() + "_" + cell.Type.ToString();
            }
            if (DicIDCount.ContainsKey(key))//记录偏移倍数
            {
                int count = DicIDCount[key];
                DicIDCount[key] = count + 1;
            }
            else
            {
                DicIDCount.Add(key, 0);
            }

            if (brush != null)
            {
                y += offset * DicIDCount[key];
                graphics.FillRectangle(brush, x, y, size.Width, size.Height);
                graphics.DrawString(descriptions[0], FontPlanningLabel, new SolidBrush(ColorPlanningLabel), x, y);
            }
            else
            {
                y += offset * DicIDCount[key];

                graphics.DrawString(descriptions[0], FontPlanningLabel, new SolidBrush(ColorPlanningLabel), x, y);
            }
        }

        private void showPlanningLabelDetail(Cell cell, Graphics graphics, Brush brush)
        {
            List<string> descriptions = cell.GetLabel(20);

            float x = 0;
            float y = 0;
            getCoordinate(cell, 100, 20 * descriptions.Count, ref x, ref y);
            foreach (string description in descriptions)
            {
                SizeF size = graphics.MeasureString(description, FontPlanningLabel);
                if (brush != null)
                {
                    graphics.FillRectangle(brush, x, y, size.Width, size.Height);
                    graphics.DrawString(description, FontPlanningLabel, new SolidBrush(ColorPlanningLabel), x, y + 2);
                }
                else
                {
                    graphics.DrawString(description, FontPlanningLabel, new SolidBrush(ColorPlanningLabel), x, y + 2);
                }
                y += size.Height;
            }
        }

        private void getCoordinate(Cell cell, float width, float height, ref float x, ref float y)
        {
            if (cell.Direction >= 0 && cell.Direction < 90)//第一象限
            {
                y = -height;
            }
            else if (cell.Direction >= 90 && cell.Direction <= 180) //第四象限
            {
                x = 0;
                y = 0;
            }
            else if (cell.Direction > 180 && cell.Direction < 270) //第三象限
            {
                x = -width;
            }
            else if (cell.Direction >= 270 && cell.Direction < 360) //第二象限
            {
                x = -width;
                y = -height;
            }
        }

        #endregion
        #endregion

        #region 设置参数信息mapsetting.mc
        public override void GetObjectData(System.Runtime.Serialization.SerializationInfo info, System.Runtime.Serialization.StreamingContext context)
        {
            base.GetObjectData(info, context);
            info.AddValue("featrueMaxZoomScale", FeatrueMaxZoomScale);

            info.AddValue("drawCurrent", DrawCurrent);
            info.AddValue("DrawServer", DrawServer);
            info.AddValue("ColorBegin", ColorBegin);
            info.AddValue("ColorViaEnabled", ColorViaEnabled);
            info.AddValue("ColorVia", ColorVia);
            info.AddValue("ColorEnd", ColorEnd);
            info.AddValue("DrawGSM900", DrawGSM900);
            info.AddValue("DrawDSC1800", DrawDSC1800);
            info.AddValue("DrawUpper", DrawUpper);
            info.AddValue("DrawOutdoor", DrawOutdoor);
            info.AddValue("DrawIndoor", DrawIndoor);
            info.AddValue("ColorSelected", ColorSelected);
            info.AddValue("DrawAlarm", DrawAlarm);
            info.AddValue("ZoomScaleAlarm", ZoomScaleAlarm);
            info.AddValue("ColorAlarm", ColorAlarm);

            info.AddValue("DrawBTS", DrawBTS);
            info.AddValue("ColorGSM900BTS", ColorGSM900BTS);
            info.AddValue("ColorDSC1800BTS", ColorDSC1800BTS);
            info.AddValue("ColorCoSiteBTS", ColorCoSiteBTS);
            info.AddValue("sizeBTS", SizeBTS);
            info.AddValue("DrawBTSLabel", DrawBTSLabel);
            info.AddValue("fontBTSLabelFontFamilyName", FontBTSLabel.FontFamily.Name);
            info.AddValue("fontBTSLabelFontSize", FontBTSLabel.Size);
            info.AddValue("fontBTSLabelFontStyle", (int)FontBTSLabel.Style);

            info.AddValue("DrawPlanBTS", DrawPlanBTS);
            info.AddValue("ColorPlanBTS", ColorPlanBTS);
            info.AddValue("sizePlanBTS", SizePlanBTS);
            info.AddValue("DrawPlanBTSLabel", DrawPlanBTSLabel);
            info.AddValue("fontPlanBTSLabelFontFamilyName", FontPlanBTSLabel.FontFamily.Name);
            info.AddValue("fontPlanBTSLabelFontSize", FontPlanBTSLabel.Size);
            info.AddValue("fontPlanBTSLabelFontStyle", (int)FontPlanBTSLabel.Style);

            info.AddValue("DrawCell", DrawCell);
            info.AddValue("DrawCover", DrawCover);
            info.AddValue("ColorGSM900Cell", ColorGSM900Cell);
            info.AddValue("ColorDSC1800Cell", ColorDSC1800Cell);
            info.AddValue("ColorCoSiteCell", ColorCoSiteCell);
            info.AddValue("ColorCover", ColorCover);

            info.AddValue("DrawAntenna", DrawAntenna);
            info.AddValue("ColorGSM900Antenna", ColorGSM900Antenna);
            info.AddValue("ColorDSC1800Antenna", ColorDSC1800Antenna);
            info.AddValue("ColorCoSiteAntenna", ColorCoSiteAntenna);

            info.AddValue("drawPlanningLabel", DrawPlanningLabel);
            info.AddValue("drawCoBCCH", DrawCoBCCH);
            info.AddValue("drawCoTCH", DrawCoTCH);
            info.AddValue("drawAdjBCCH", DrawAdjBCCH);
            info.AddValue("drawAdjTCH", DrawAdjTCH);
            info.AddValue("showSimple", ShowSimple);
            info.AddValue("showDetail", ShowDetail);
            info.AddValue("showNone", ShowNone);
            info.AddValue("ColorCoBCCH", ColorCoBCCH);
            info.AddValue("ColorCoTCH", ColorCoTCH);
            info.AddValue("ColorAdjBCCH", ColorAdjBCCH);
            info.AddValue("ColorAdjTCH", ColorAdjTCH);
            info.AddValue("ColorCoBSIC", ColorCoBSIC);
            info.AddValue("ColorNeighbour", ColorNeighbour);
            info.AddValue("ColorNeighbourEachOther", ColorNeighbourEachOther);
            info.AddValue("fontPlanningLabelFontFamilyName", FontPlanningLabel.FontFamily.Name);
            info.AddValue("fontPlanningLabelFontSize", FontPlanningLabel.Size);
            info.AddValue("fontPlanningLabelFontStyle", (int)FontPlanningLabel.Style);
            info.AddValue("colorPlanningLabel", ColorPlanningLabel);

            info.AddValue("DrawRepeater", DrawRepeater);
            info.AddValue("DrawRepeaterLabel", DrawRepeaterLabel);
            info.AddValue("sizeRepeater", SizeRepeater);
            info.AddValue("DrawLineDonarCell2Repeater", DrawLineDonarCell2Repeater);

            //BTS Index
            info.AddValue("drawBTSName", DrawBTSName);
            info.AddValue("drawBTSMSC", DrawBTSMSC);
            info.AddValue("drawBTSBSC", DrawBTSBSC);
            info.AddValue("drawBTSLongitude", DrawBTSLongitude);
            info.AddValue("drawBTSLatitude", DrawBTSLatitude);
            info.AddValue("drawBTSType", DrawBTSType);
            info.AddValue("drawBTSDescription", DrawBTSDescription);

            //PlanBTS Index
            info.AddValue("drawPlanBTSName", DrawPlanBTSName);
            info.AddValue("drawPlanBTSNo", DrawPlanBTSNo);
            info.AddValue("drawPlanBTSLongi", DrawPlanBTSLongi);
            info.AddValue("drawPlanBTSLati", DrawPlanBTSLati);
            info.AddValue("drawPlanBTSType", DrawPlanBTSType);
            info.AddValue("drawPlanBTSReason", DrawPlanBTSReason);
            info.AddValue("drawPlanBTSProgress", DrawPlanBTSProgress);
            info.AddValue("drawPlanBTSAddr", DrawPlanBTSAddr);
            info.AddValue("drawPlanBTSComment", DrawPlanBTSComment);

            //Cell Index
            info.AddValue("DrawCellLabel", DrawCellLabel);
            info.AddValue("fontCellLabelFamilyName", FontCellLabel.FontFamily.Name);
            info.AddValue("fontCellLabelFontSize", FontCellLabel.Size);
            info.AddValue("fontCellLabelFontStyle", (int)FontCellLabel.Style);
            info.AddValue("drawCellName", DrawCellName);
            info.AddValue("drawCellCode", DrawCellCode);
            info.AddValue("drawCellLAC", DrawCellLAC);
            info.AddValue("drawCellCI", DrawCellCI);
            info.AddValue("drawCellBCCH", DrawCellBCCH);
            info.AddValue("drawCellBSIC", DrawCellBSIC);
            info.AddValue("drawCellTCH", DrawCellTCH);
            info.AddValue("drawCellHOP", DrawCellHOP);

            //Antenna Index
            info.AddValue("DrawAntennaLabel", DrawAntennaLabel);
            info.AddValue("fontAntennaLabelFamilyName", FontAntennaLabel.FontFamily.Name);
            info.AddValue("fontAntennaLabelFontSize", FontAntennaLabel.Size);
            info.AddValue("fontAntennaLabelFontStyle", (int)FontAntennaLabel.Style);
            info.AddValue("drawAntennaGroup", DrawAntennaGroup);
            info.AddValue("drawAntennaSN", DrawAntennaSN);
            info.AddValue("drawAntennaLongitude", DrawAntennaLongitude);
            info.AddValue("drawAntennaLatitude", DrawAntennaLatitude);
            info.AddValue("drawAntennaDirectionType", DrawAntennaDirectionType);
            info.AddValue("drawAntennaDirection", DrawAntennaDirection);
            info.AddValue("drawAntennaDownward", DrawAntennaDownward);
            info.AddValue("drawAntennaAltitude", DrawAntennaAltitude);
            info.AddValue("drawAntennaGain", DrawAntennaGain);
            info.AddValue("drawAntennaEIRP", DrawAntennaEIRP);
            info.AddValue("drawAntennaDescription", DrawAntennaDescription);

            //Interfere
            info.AddValue("DrawInterfereDis", DrawInterfereDis);
            info.AddValue("DrawInterfereColor", DrawInterfereColor);
            info.AddValue("DrawInterfereSizi", DrawInterfereSizi);
            info.AddValue("DrawInterfereLabel", DrawInterfereLabel);
            info.AddValue("DrawInterfereName", DrawInterfereName);
        }

        public void SetObjectData(System.Runtime.Serialization.SerializationInfo info, System.Runtime.Serialization.StreamingContext context)
        {
            FeatrueMaxZoomScale = info.GetDouble("featrueMaxZoomScale");

            DrawCurrent = info.GetBoolean("drawCurrent");
            DrawServer = info.GetBoolean("DrawServer");
            ColorBegin = (Color)info.GetValue("ColorBegin", typeof(Color));
            ColorViaEnabled = info.GetBoolean("ColorViaEnabled");
            ColorVia = (Color)info.GetValue("ColorVia", typeof(Color));
            ColorEnd = (Color)info.GetValue("ColorEnd", typeof(Color));
            DrawGSM900 = info.GetBoolean("DrawGSM900");
            DrawDSC1800 = info.GetBoolean("DrawDSC1800");
            DrawUpper = info.GetBoolean("DrawUpper");
            DrawOutdoor = info.GetBoolean("DrawOutdoor");
            DrawIndoor = info.GetBoolean("DrawIndoor");
            ColorSelected = (Color)info.GetValue("ColorSelected", typeof(Color));
            DrawAlarm = info.GetBoolean("DrawAlarm");
            ZoomScaleAlarm = info.GetInt32("ZoomScaleAlarm");
            ColorAlarm = (Color)info.GetValue("ColorAlarm", typeof(Color));

            DrawBTS = info.GetBoolean("DrawBTS");
            ColorGSM900BTS = (Color)info.GetValue("ColorGSM900BTS", typeof(Color));
            ColorDSC1800BTS = (Color)info.GetValue("ColorDSC1800BTS", typeof(Color));
            ColorCoSiteBTS = (Color)info.GetValue("ColorCoSiteBTS", typeof(Color));
            SizeBTS = info.GetInt32("sizeBTS");
            DrawBTSLabel = info.GetBoolean("DrawBTSLabel");
            FontBTSLabel = new Font(new FontFamily(info.GetString("fontBTSLabelFontFamilyName")), info.GetSingle("fontBTSLabelFontSize"), (FontStyle)info.GetInt32("fontBTSLabelFontStyle"));

            DrawPlanBTS = info.GetBoolean("DrawPlanBTS");
            ColorPlanBTS = (Color)info.GetValue("ColorPlanBTS", typeof(Color));
            SizePlanBTS = info.GetInt32("sizePlanBTS");
            DrawPlanBTSLabel = info.GetBoolean("DrawPlanBTSLabel");
            FontPlanBTSLabel = new Font(new FontFamily(info.GetString("fontPlanBTSLabelFontFamilyName")), info.GetSingle("fontPlanBTSLabelFontSize"), (FontStyle)info.GetInt32("fontPlanBTSLabelFontStyle"));

            DrawCell = info.GetBoolean("DrawCell");
            DrawCover = info.GetBoolean("DrawCover");
            ColorGSM900Cell = (Color)info.GetValue("ColorGSM900Cell", typeof(Color));
            ColorDSC1800Cell = (Color)info.GetValue("ColorDSC1800Cell", typeof(Color));
            ColorCoSiteCell = (Color)info.GetValue("ColorCoSiteCell", typeof(Color));
            ColorCover = (Color)info.GetValue("ColorCover", typeof(Color));

            DrawAntenna = info.GetBoolean("DrawAntenna");
            ColorGSM900Antenna = (Color)info.GetValue("ColorGSM900Antenna", typeof(Color));
            ColorDSC1800Antenna = (Color)info.GetValue("ColorDSC1800Antenna", typeof(Color));
            ColorCoSiteAntenna = (Color)info.GetValue("ColorCoSiteAntenna", typeof(Color));

            DrawPlanningLabel = info.GetBoolean("drawPlanningLabel");
            DrawCoBCCH = info.GetBoolean("drawCoBCCH");
            DrawCoTCH = info.GetBoolean("drawCoTCH");
            DrawAdjBCCH = info.GetBoolean("drawAdjBCCH");
            DrawAdjTCH = info.GetBoolean("drawAdjTCH");
            ShowSimple = info.GetBoolean("showSimple");
            ShowDetail = info.GetBoolean("showDetail");
            ShowNone = info.GetBoolean("showNone");
            ColorCoBCCH = (Color)info.GetValue("ColorCoBCCH", typeof(Color));
            ColorCoTCH = (Color)info.GetValue("ColorCoTCH", typeof(Color));
            ColorAdjBCCH = (Color)info.GetValue("ColorAdjBCCH", typeof(Color));
            ColorAdjTCH = (Color)info.GetValue("ColorAdjTCH", typeof(Color));
            ColorCoBSIC = (Color)info.GetValue("ColorCoBSIC", typeof(Color));
            ColorNeighbour = (Color)info.GetValue("ColorNeighbour", typeof(Color));
            ColorNeighbourEachOther = (Color)info.GetValue("ColorNeighbourEachOther", typeof(Color));
            FontPlanningLabel = new Font(new FontFamily(info.GetString("fontPlanningLabelFontFamilyName")), info.GetSingle("fontPlanningLabelFontSize"), (FontStyle)info.GetInt32("fontPlanningLabelFontStyle"));
            ColorPlanningLabel = (Color)info.GetValue("colorPlanningLabel", typeof(Color));

            DrawRepeater = info.GetBoolean("DrawRepeater");
            DrawRepeaterLabel = info.GetBoolean("DrawRepeaterLabel");
            SizeRepeater = info.GetInt32("sizeRepeater");
            DrawLineDonarCell2Repeater = info.GetBoolean("DrawLineDonarCell2Repeater");

            //BTS Index
            DrawBTSName = info.GetBoolean("drawBTSName");
            DrawBTSMSC = info.GetBoolean("drawBTSMSC");
            DrawBTSBSC = info.GetBoolean("drawBTSBSC");
            DrawBTSLongitude = info.GetBoolean("drawBTSLongitude");
            DrawBTSLatitude = info.GetBoolean("drawBTSLatitude");
            DrawBTSType = info.GetBoolean("drawBTSType");
            DrawBTSDescription = info.GetBoolean("drawBTSDescription");

            //PlanBTS Index
            DrawPlanBTSName = info.GetBoolean("drawPlanBTSName");
            DrawPlanBTSNo = info.GetBoolean("drawPlanBTSNo");
            DrawPlanBTSLongi = info.GetBoolean("drawPlanBTSLongi");
            DrawPlanBTSLati = info.GetBoolean("drawPlanBTSLati");
            DrawPlanBTSType = info.GetBoolean("drawPlanBTSType");
            DrawPlanBTSReason = info.GetBoolean("drawPlanBTSReason");
            DrawPlanBTSProgress = info.GetBoolean("drawPlanBTSProgress");
            DrawPlanBTSAddr = info.GetBoolean("drawPlanBTSAddr");
            DrawPlanBTSComment = info.GetBoolean("drawPlanBTSComment");

            //Cell Index
            DrawCellLabel = info.GetBoolean("DrawCellLabel");
            FontCellLabel = new Font(new FontFamily(info.GetString("fontCellLabelFamilyName")), info.GetSingle("fontCellLabelFontSize"), (FontStyle)info.GetInt32("fontCellLabelFontStyle"));
            DrawCellName = info.GetBoolean("drawCellName");
            DrawCellCode = info.GetBoolean("drawCellCode");
            DrawCellLAC = info.GetBoolean("drawCellLAC");
            DrawCellCI = info.GetBoolean("drawCellCI");
            DrawCellBCCH = info.GetBoolean("drawCellBCCH");
            DrawCellBSIC = info.GetBoolean("drawCellBSIC");
            DrawCellTCH = info.GetBoolean("drawCellTCH");
            DrawCellHOP = info.GetBoolean("drawCellHOP");

            //Antenna Index
            DrawAntennaLabel = info.GetBoolean("DrawAntennaLabel");
            FontAntennaLabel = new Font(new FontFamily(info.GetString("fontAntennaLabelFamilyName")), info.GetSingle("fontAntennaLabelFontSize"), (FontStyle)info.GetInt32("fontAntennaLabelFontStyle"));
            DrawAntennaGroup = info.GetBoolean("drawAntennaGroup");
            DrawAntennaSN = info.GetBoolean("drawAntennaSN");
            DrawAntennaLongitude = info.GetBoolean("drawAntennaLongitude");
            DrawAntennaLatitude = info.GetBoolean("drawAntennaLatitude");
            DrawAntennaDirectionType = info.GetBoolean("drawAntennaDirectionType");
            DrawAntennaDirection = info.GetBoolean("drawAntennaDirection");
            DrawAntennaDownward = info.GetBoolean("drawAntennaDownward");
            DrawAntennaAltitude = info.GetBoolean("drawAntennaAltitude");
            DrawAntennaGain = info.GetBoolean("drawAntennaGain");
            DrawAntennaEIRP = info.GetBoolean("drawAntennaEIRP");
            DrawAntennaDescription = info.GetBoolean("drawAntennaDescription");

            //Interfere
            DrawInterfereDis = info.GetBoolean("DrawInterfereDis");
            DrawInterfereColor = (Color)info.GetValue("DrawInterfereColor", typeof(Color));
            DrawInterfereSizi = info.GetInt32("DrawInterfereSizi");
            DrawInterfereLabel = info.GetBoolean("DrawInterfereLabel");
            DrawInterfereName = info.GetBoolean("DrawInterfereName");

            makeBrushes();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                //LayerBase中属性
                param["visibleScaleEnabled"] = VisibleScaleEnabled;
                param["minVisibleScan"] = VisibleScale.ScaleMin;
                param["maxVisibleScan"] = VisibleScale.ScaleMax;

                param["featrueMaxZoomScale"] = FeatrueMaxZoomScale;
                param["drawCurrent"] = DrawCurrent;
                param["DrawServer"] = DrawServer;
                param["ColorBegin"] = ColorBegin.ToArgb();
                param["ColorViaEnabled"] = ColorViaEnabled;
                param["ColorVia"] = ColorVia.ToArgb();
                param["ColorEnd"] = ColorEnd.ToArgb();
                param["DrawGSM900"] = DrawGSM900;
                param["DrawDSC1800"] = DrawDSC1800;
                param["DrawUpper"] = DrawUpper;
                param["DrawIndoor"] = DrawIndoor;
                param["ColorSelected"] = ColorSelected.ToArgb();
                param["DrawAlarm"] = DrawAlarm;
                param["ZoomScaleAlarm"] = ZoomScaleAlarm;
                param["ColorAlarm"] = ColorAlarm.ToArgb();

                param["DrawBTS"] = DrawBTS;
                param["ColorGSM900BTS"] = ColorGSM900BTS.ToArgb();
                param["ColorDSC1800BTS"] = ColorDSC1800BTS.ToArgb();
                param["ColorCoSiteBTS"] = ColorCoSiteBTS.ToArgb();
                param["sizeBTS"] = SizeBTS;
                param["DrawBTSLabel"] = DrawBTSLabel;
                param["fontBTSLabelFontFamilyName"] = FontBTSLabel.FontFamily.Name;
                param["fontBTSLabelFontSize"] = FontBTSLabel.Size;
                param["fontBTSLabelFontStyle"] = (int)FontBTSLabel.Style;

                param["DrawPlanBTS"] = DrawPlanBTS;
                param["ColorPlanBTS"] = ColorPlanBTS.ToArgb();
                param["sizePlanBTS"] = SizePlanBTS;
                param["DrawPlanBTSLabel"] = DrawPlanBTSLabel;
                param["fontPlanBTSLabelFontFamilyName"] = FontPlanBTSLabel.FontFamily.Name;
                param["fontPlanBTSLabelFontSize"] = FontPlanBTSLabel.Size;
                param["fontPlanBTSLabelFontStyle"] = (int)FontPlanBTSLabel.Style;

                //cell
                param["DrawCell"] = DrawCell;
                param["DrawCover"] = DrawCover;
                param["ColorGSM900Cell"] = ColorGSM900Cell.ToArgb();
                param["ColorDSC1800Cell"] = ColorDSC1800Cell.ToArgb();
                param["ColorCoSiteCell"] = ColorCoSiteCell.ToArgb();
                param["ColorCover"] = ColorCover.ToArgb();
                param["DrawCellLabel"] = DrawCellLabel;
                param["fontCellLabelFamilyName"] = FontCellLabel.FontFamily.Name;
                param["fontCellLabelFontSize"] = FontCellLabel.Size;
                param["fontCellLabelFontStyle"] = (int)FontCellLabel.Style;
                param["drawCellName"] = DrawCellName;
                param["drawCellCode"] = DrawCellCode;
                param["drawCellLAC"] = DrawCellLAC;
                param["drawCellCI"] = DrawCellCI;
                param["drawCellBCCH"] = DrawCellBCCH;
                param["drawCellBSIC"] = DrawCellBSIC;
                param["drawCellTCH"] = DrawCellTCH;
                param["drawCellHOP"] = DrawCellHOP;

                //Antenna
                param["DrawAntenna"] = DrawAntenna;
                param["ColorGSM900Antenna"] = ColorGSM900Antenna.ToArgb();
                param["ColorDSC1800Antenna"] = ColorDSC1800Antenna.ToArgb();
                param["ColorCoSiteAntenna"] = ColorCoSiteAntenna.ToArgb();
                param["DrawAntennaLabel"] = DrawAntennaLabel;
                param["drawAntennaLabelFamilyName"] = FontAntennaLabel.FontFamily.Name;
                param["fontAntennaLabelFontSize"] = FontAntennaLabel.Size;
                param["fontAntennaLabelFontStyle"] = (int)FontAntennaLabel.Style;
                param["drawAntennaGroup"] = DrawAntennaGroup;
                param["drawAntennaSN"] = DrawAntennaSN;
                param["drawAntennaLongitude"] = DrawAntennaLongitude;
                param["drawAntennaLatitude"] = DrawAntennaLatitude;
                param["drawAntennaDirectionType"] = DrawAntennaDirectionType;
                param["drawAntennaDirection"] = DrawAntennaDirection;
                param["drawAntennaDownward"] = DrawAntennaDownward;
                param["drawAntennaAltitude"] = DrawAntennaAltitude;
                param["drawAntennaGain"] = DrawAntennaGain;
                param["drawAntennaEIRP"] = DrawAntennaEIRP;
                param["drawAntennaDescription"] = DrawAntennaDescription;

                param["drawPlanningLabel"] = DrawPlanningLabel;
                param["drawCoBCCH"] = DrawCoBCCH;
                param["drawCoTCH"] = DrawCoTCH;
                param["drawAdjBCCH"] = DrawAdjBCCH;
                param["drawAdjTCH"] = DrawAdjTCH;
                param["showSimple"] = ShowSimple;
                param["showDetail"] = ShowDetail;
                param["showNone"] = ShowNone;
                param["ColorCoBCCH"] = ColorCoBCCH.ToArgb();
                param["ColorCoTCH"] = ColorCoTCH.ToArgb();
                param["ColorAdjBCCH"] = ColorAdjBCCH.ToArgb();
                param["ColorAdjTCH"] = ColorAdjTCH.ToArgb();
                param["ColorCoBSIC"] = ColorCoBSIC.ToArgb();
                param["ColorNeighbour"] = ColorNeighbour.ToArgb();
                param["ColorNeighbourEachOther"] = ColorNeighbourEachOther.ToArgb();
                param["fontPlanningLabelFontFamilyName"] = FontPlanningLabel.FontFamily.Name;
                param["fontPlanningLabelFontSize"] = FontPlanningLabel.Size;
                param["fontPlanningLabelFontStyle"] = (int)FontPlanningLabel.Style;
                param["colorPlanningLabel"] = ColorPlanningLabel.ToArgb();

                param["DrawRepeater"] = DrawRepeater;
                param["DrawRepeaterLabel"] = DrawRepeaterLabel;
                param["sizeRepeater"] = SizeRepeater;
                param["DrawLineDonarCell2Repeater"] = DrawLineDonarCell2Repeater;
                param["isEnabled"] = this.Enabled;

                //Interfere
                param["DrawInterfereDis"] = DrawInterfereDis;
                param["DrawInterfereColor"] = DrawInterfereColor.ToArgb();
                param["DrawInterfereSizi"] = DrawInterfereSizi;
                param["DrawInterfereLabel"] = DrawInterfereLabel;
                param["DrawInterfereName"] = DrawInterfereName;
                return param;
            }
            set
            {
                if (value.ContainsKey("visibleScaleEnabled"))
                {
                    VisibleScaleEnabled = (bool)value["visibleScaleEnabled"];
                }
                if (value.ContainsKey("minVisibleScan"))
                {
                    VisibleScale.ScaleMin = (double)value["minVisibleScan"];
                }
                if (value.ContainsKey("maxVisibleScan"))
                {
                    VisibleScale.ScaleMax = (double)value["maxVisibleScan"];
                }
                try
                {
                    FeatrueMaxZoomScale = (double)value["featrueMaxZoomScale"];

                    DrawCurrent = (bool)value["drawCurrent"];
                    DrawServer = (bool)value["DrawServer"];
                    ColorBegin = Color.FromArgb((int)value["ColorBegin"]);
                    ColorViaEnabled = (bool)value["ColorViaEnabled"];
                    ColorVia = Color.FromArgb((int)value["ColorVia"]);
                    ColorEnd = Color.FromArgb((int)value["ColorEnd"]);
                    DrawGSM900 = (bool)value["DrawGSM900"];
                    DrawDSC1800 = (bool)value["DrawDSC1800"];
                    DrawUpper = (bool)value["DrawUpper"];
                    DrawIndoor = (bool)value["DrawIndoor"];
                    ColorSelected = Color.FromArgb((int)value["ColorSelected"]);

                    DrawAlarm = (bool)value["DrawAlarm"];
                    ZoomScaleAlarm = (int)value["ZoomScaleAlarm"];
                    ColorAlarm = Color.FromArgb((int)value["ColorAlarm"]);


                    DrawBTS = (bool)value["DrawBTS"];
                    ColorGSM900BTS = Color.FromArgb((int)value["ColorGSM900BTS"]);
                    ColorDSC1800BTS = Color.FromArgb((int)value["ColorDSC1800BTS"]);
                    ColorCoSiteBTS = Color.FromArgb((int)value["ColorCoSiteBTS"]);
                    SizeBTS = (int)value["sizeBTS"];
                    DrawBTSLabel = (bool)value["DrawBTSLabel"];
                    FontBTSLabel = new Font(new FontFamily((String)value["fontBTSLabelFontFamilyName"]), (float)value["fontBTSLabelFontSize"], (FontStyle)(int)value["fontBTSLabelFontStyle"]);

                    DrawPlanBTS = (bool)value["DrawPlanBTS"];
                    ColorPlanBTS = Color.FromArgb((int)value["ColorPlanBTS"]);
                    SizePlanBTS = (int)value["sizePlanBTS"];
                    DrawPlanBTSLabel = (bool)value["DrawPlanBTSLabel"];
                    FontPlanBTSLabel = new Font(new FontFamily((String)value["fontPlanBTSLabelFontFamilyName"]), (float)value["fontPlanBTSLabelFontSize"], (FontStyle)(int)value["fontPlanBTSLabelFontStyle"]);

                    //Cell
                    DrawCell = (bool)value["DrawCell"];
                    DrawCover = (bool)value["DrawCover"];
                    ColorGSM900Cell = Color.FromArgb((int)value["ColorGSM900Cell"]);
                    ColorDSC1800Cell = Color.FromArgb((int)value["ColorDSC1800Cell"]);
                    ColorCoSiteCell = Color.FromArgb((int)value["ColorCoSiteCell"]);
                    ColorCover = Color.FromArgb((int)value["ColorCover"]);

                    DrawCellLabel = (bool)value["DrawCellLabel"];

                    if (value.ContainsKey("fontCellLabelFontStyle") && value["fontCellLabelFontStyle"] != null)
                    {
                        FontCellLabel = new Font(new FontFamily((String)value["fontCellLabelFamilyName"]), (float)value["fontCellLabelFontSize"], (FontStyle)(int)value["fontCellLabelFontStyle"]);
                    }
                    DrawCellName = (bool)value["drawCellName"];
                    DrawCellCode = (bool)value["drawCellCode"];
                    DrawCellLAC = (bool)value["drawCellLAC"];
                    DrawCellCI = (bool)value["drawCellCI"];
                    DrawCellBCCH = (bool)value["drawCellBCCH"];
                    DrawCellBSIC = (bool)value["drawCellBSIC"];
                    DrawCellTCH = (bool)value["drawCellTCH"];
                    DrawCellHOP = (bool)value["drawCellHOP"];

                    //Antenna
                    DrawAntenna = (bool)value["DrawAntenna"];
                    ColorGSM900Antenna = Color.FromArgb((int)value["ColorGSM900Antenna"]);
                    ColorDSC1800Antenna = Color.FromArgb((int)value["ColorDSC1800Antenna"]);
                    ColorCoSiteAntenna = Color.FromArgb((int)value["ColorCoSiteAntenna"]);

                    DrawAntennaLabel = (bool)value["DrawAntennaLabel"];
                    if (value.ContainsKey("fontAntennaLabelFontStyle") && value["fontAntennaLabelFontStyle"] != null)
                    {
                        FontAntennaLabel = new Font(new FontFamily((String)value["drawAntennaLabelFamilyName"]), (float)value["fontAntennaLabelFontSize"], (FontStyle)(int)value["fontAntennaLabelFontStyle"]);
                    }
                    DrawAntennaGroup = (bool)value["drawAntennaGroup"];
                    DrawAntennaSN = (bool)value["drawAntennaSN"];
                    DrawAntennaLongitude = (bool)value["drawAntennaLongitude"];
                    DrawAntennaLatitude = (bool)value["drawAntennaLatitude"];
                    DrawAntennaDirectionType = (bool)value["drawAntennaDirectionType"];
                    DrawAntennaDirection = (bool)value["drawAntennaDirection"];
                    DrawAntennaDownward = (bool)value["drawAntennaDownward"];
                    DrawAntennaAltitude = (bool)value["drawAntennaAltitude"];
                    DrawAntennaGain = (bool)value["drawAntennaGain"];
                    DrawAntennaEIRP = (bool)value["drawAntennaEIRP"];
                    DrawAntennaDescription = (bool)value["drawAntennaDescription"];

                    DrawPlanningLabel = (bool)value["drawPlanningLabel"];
                    DrawCoBCCH = (bool)value["drawCoBCCH"];
                    DrawCoTCH = (bool)value["drawCoTCH"];
                    DrawAdjBCCH = (bool)value["drawAdjBCCH"];
                    DrawAdjTCH = (bool)value["drawAdjTCH"];
                    ColorCoBCCH = Color.FromArgb((int)value["ColorCoBCCH"]);
                    ColorCoTCH = Color.FromArgb((int)value["ColorCoTCH"]);
                    ColorAdjBCCH = Color.FromArgb((int)value["ColorAdjBCCH"]);
                    ColorAdjTCH = Color.FromArgb((int)value["ColorAdjTCH"]);
                    ColorCoBSIC = Color.FromArgb((int)value["ColorCoBSIC"]);
                    ColorNeighbour = Color.FromArgb((int)value["ColorNeighbour"]);
                    ColorNeighbourEachOther = Color.FromArgb((int)value["ColorNeighbourEachOther"]);
                    FontPlanningLabel = new Font(new FontFamily((String)value["fontPlanningLabelFontFamilyName"]), (float)value["fontPlanningLabelFontSize"], (FontStyle)(int)value["fontPlanningLabelFontStyle"]);

                    DrawRepeater = (bool)value["DrawRepeater"];
                    SizeRepeater = (int)value["sizeRepeater"];
                    DrawLineDonarCell2Repeater = (bool)value["DrawLineDonarCell2Repeater"];

                    ShowSimple = (bool)value["showSimple"];
                    ShowDetail = (bool)value["showDetail"];
                    ShowNone = (bool)value["showNone"];
                    ColorPlanningLabel = Color.FromArgb((int)value["colorPlanningLabel"]);
                    this.Enabled = (bool)value["isEnabled"];
                    DrawRepeaterLabel = (bool)value["DrawRepeaterLabel"];

                    //Interfere
                    DrawInterfereDis = (bool)value["DrawInterfereDis"];
                    DrawInterfereColor = Color.FromArgb((int)value["DrawInterfereColor"]);
                    DrawInterfereSizi = (int)value["DrawInterfereSizi"];
                    DrawInterfereLabel = (bool)value["DrawInterfereLabel"];
                    DrawInterfereName = (bool)value["DrawInterfereName"];
                }
                catch
                {
                    //continue
                }
                makeBrushes();
            }
        }
        #endregion

        #region Select

        public override void mapForm_MapFeatureSelecting(object sender, EventArgs e) 
        {
            MapForm mf = sender as MapForm;
            Select(((MapForm.MapEventArgs)e).MapOp2, mf.Btss, mf.PlanBTSs, mf.Cells, mf.Antennas, mf.Repeaters);
        }

        public void Select(MapOperation2 mop2, List<BTS> selectedBTSs, List<PlanBTS> selectedPlanBTSs, List<Cell> selectedCells, List<Antenna> selectedAntennas, List<Repeater> selectedRepeaters)
        {
            select(mop2, selectedBTSs, selectedPlanBTSs, selectedCells, selectedAntennas, selectedRepeaters);
        }

        private void select(MapOperation2 mop2, List<BTS> selectedBTSs, List<PlanBTS> selectedPlanBTSs, List<Cell> selectedCells, List<Antenna> selectedAntennas, List<Repeater> selectedRepeaters)
        {
            if (IsVisible)
            {
                DbRect dRect = mop2.GetRegion().Bounds;
                getSelectedBTSs(mop2, selectedBTSs, dRect);
                getSelectedPlanBTSs(mop2, selectedPlanBTSs, dRect);
                getSelectedRepeaters(mop2, selectedRepeaters, dRect);
                getSelectedAntennas(mop2, selectedAntennas, dRect);
                getSelectedCells(mop2, selectedCells, dRect);
            }
        }

        private void getSelectedBTSs(MapOperation2 mop2, List<BTS> selectedBTSs, DbRect dRect)
        {
            if (DrawBTS)
            {
                List<BTS> btss = null;
                if (DrawCurrent)
                {
                    btss = mainModel.CellManager.GetCurrentBTSs();
                }
                else
                {
                    btss = mainModel.CellManager.GetBTSs(CurShowTimeAt);
                }
                if (btss != null)
                {
                    foreach (BTS bts in btss)
                    {
                        addValidSelectedBTS(mop2, selectedBTSs, dRect, bts);
                    }
                }
            }
        }

        private void addValidSelectedBTS(MapOperation2 mop2, List<BTS> selectedBTSs, DbRect dRect, BTS bts)
        {
            if (bts.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
            {
                bool drawBtsType = ((bts.BandType == BTSBandType.GSM900 && DrawGSM900)
                        || (bts.BandType == BTSBandType.DSC1800 && DrawDSC1800))
                    && ((bts.Type == BTSType.Outdoor && DrawOutdoor)
                        || (bts.Type == BTSType.Upper && DrawUpper)
                        || (bts.Type == BTSType.Indoor && DrawIndoor));
                if (drawBtsType)
                {
                    BTS selectedBTS = selectBTS(bts, mop2);
                    if (selectedBTS != null)
                    {
                        selectedBTSs.Add(selectedBTS);
                    }
                }
            }
        }

        private void getSelectedPlanBTSs(MapOperation2 mop2, List<PlanBTS> selectedPlanBTSs, DbRect dRect)
        {
            if (DrawPlanBTS)
            {
                List<PlanBTS> btss = null;
                btss = mainModel.CellManager.GetCurrentPlanBTSs();
                if (btss != null)
                {
                    foreach (PlanBTS bts in btss)
                    {
                        addValidSelectedPlanBTS(mop2, selectedPlanBTSs, dRect, bts);
                    }
                }
            }
        }

        private void addValidSelectedPlanBTS(MapOperation2 mop2, List<PlanBTS> selectedPlanBTSs, DbRect dRect, PlanBTS bts)
        {
            if (bts.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
            {
                PlanBTS selectedPlanBTS = selectPlanBTS(bts, mop2, mapScale);
                if (selectedPlanBTS != null)
                {
                    selectedPlanBTSs.Add(selectedPlanBTS);
                }
            }
        }

        private void getSelectedRepeaters(MapOperation2 mop2, List<Repeater> selectedRepeaters, DbRect dRect)
        {
            if (DrawRepeater)
            {
                List<Repeater> repeaters = null;
                if (DrawCurrent)
                {
                    repeaters = mainModel.CellManager.GetCurrentRepeaters();
                }
                else
                {
                    repeaters = mainModel.CellManager.GetRepeaters(CurShowTimeAt);
                }
                if (repeaters != null)
                {
                    foreach (Repeater repeater in repeaters)
                    {
                        addValidSelectedRepeater(mop2, selectedRepeaters, dRect, repeater);
                    }
                }
            }
        }

        private void addValidSelectedRepeater(MapOperation2 mop2, List<Repeater> selectedRepeaters, DbRect dRect, Repeater repeater)
        {
            if (repeater.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
            {
                bool drawRepeaterType = (repeater.BandType == BTSBandType.GSM900 && DrawGSM900)
                    || (repeater.BandType == BTSBandType.DSC1800 && DrawDSC1800);
                if (drawRepeaterType)
                {
                    Repeater selectedRepeater = selectRepeater(repeater, mop2);
                    if (selectedRepeater != null)
                    {
                        selectedRepeaters.Add(selectedRepeater);
                    }
                }
            }
        }

        private void getSelectedAntennas(MapOperation2 mop2, List<Antenna> selectedAntennas, DbRect dRect)
        {
            if (!DrawAntenna)
            {
                return;
            }
            List<Cell> cells = null;
            if (DrawServer)
            {
                cells = mainModel.ServerCells;
            }
            if (cells != null)
            {
                foreach (Cell cell in cells)
                {
                    addValidSelectedAntenna(mop2, selectedAntennas, dRect, cell, (a, b) => true);
                }
            }
            if (DrawCurrent)
            {
                cells = mainModel.CellManager.GetCurrentCells();
            }
            else
            {
                cells = mainModel.CellManager.GetCells(CurShowTimeAt);
            }
            if (cells.Count > 0)
            {
                foreach (Cell cell in cells)
                {
                    addValidSelectedAntenna(mop2, selectedAntennas, dRect, cell, (a, b) => !a.Contains(b));
                }
            }
        }

        private void addValidSelectedAntenna(MapOperation2 mop2, List<Antenna> selectedAntennas, DbRect dRect, Cell cell, Func<Antenna> func)
        {
            foreach (Antenna antenna in cell.Antennas)
            {
                if (antenna.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                {
                    Antenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                    if (selectedAntenna != null && func(selectedAntennas, selectedAntenna))
                    {
                        selectedAntennas.Add(selectedAntenna);
                    }
                }
            }
        }

        private void getSelectedCells(MapOperation2 mop2, List<Cell> selectedCells, DbRect dRect)
        {
            if (!DrawCell)
            {
                return;
            }

            List<Cell> cells = null;
            if (DrawServer)
            {
                cells = mainModel.ServerCells;
            }
            if (cells != null)
            {
                foreach (Cell cell in cells)
                {
                    if (cell.Antennas.Count == 0)
                    {
                        continue;
                    }
                    addValidSelectedCell(mop2, selectedCells, dRect, cell, (a, b) => true);
                }
            }
            if (DrawCurrent)
            {
                cells = mainModel.CellManager.GetCurrentCells();
            }
            else
            {
                cells = mainModel.CellManager.GetCells(CurShowTimeAt);
            }
            if (cells != null)
            {
                foreach (Cell cell in cells)
                {
                    addValidSelectedCell(mop2, selectedCells, dRect, cell, (a, b) => !a.Contains(b));
                }
            }
        }

        delegate bool Func<T>(List<T> list, T item);

        private void addValidSelectedCell(MapOperation2 mop2, List<Cell> selectedCells, DbRect dRect, Cell cell, Func<Cell> func)
        {
            if (cell.Antennas.Count != 0 
                && cell.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
            {
                Cell selectedCell = selectCell(cell, mop2, mapScale);
                if (selectedCell != null && func(selectedCells, selectedCell))
                {
                    selectedCells.Add(selectedCell);
                }
            }
        }

        private static List<PointF[]> cellHighlightPoints = new List<PointF[]>();
        public List<PointF[]> CellHighlightPoints
        {
            get { return cellHighlightPoints; }
        }

        private Cell selectCell(Cell cell, MapOperation2 mop2, double scale)
        {
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            float ratio = (float)(10000 / scale);
            if (DrawAlarm && mainModel.OutServiceInfoManager.isContainSnapShot(MasterCom.RAMS.Func.OutServiceInfo.NetType.GSM, cell))
            {
                ratio *= ZoomScaleAlarm;
            }
            int index = 0;
            bool circle = false;
            getCellTypeShapeInfo(cell, ref index, ref circle);

            List<PointF[]> points;
            if (mainModel.CellHighLightAll)
            {
                points = cellHighlightPoints;
            }
            else
            {
                if (mainModel.CellHighlightList.Contains(cell))
                {
                    points = cellHighlightPoints;
                }
                else
                {
                    points = cellPoints;
                }
            }

            if (circle)
            {
                RectangleF rect = new RectangleF(point.X + points[index][0].X * ratio, point.Y + points[index][0].Y * ratio, points[index][2].X * 2 * ratio, points[index][2].Y * 2 * ratio);
                DbRect dRect;
                gisAdapter.FromDisplay(rect, out dRect);
                if (mop2.CheckCenterInDRect(dRect))//Ellipse
                {
                    return cell;
                }
            }
            else
            {
                PointF[] pointsNew = getPointsNew(cell.Direction, point, ratio, index, points);
                DbPoint[] dpoints;
                gisAdapter.FromDisplay(pointsNew, out dpoints);
                if (mop2.CheckCenterInDbPoints(dpoints))
                {
                    return cell;
                }
            }
            return null;
        }

        private static void getCellTypeShapeInfo(Cell cell, ref int index, ref bool circle)
        {
            if (cell.BandType == BTSBandType.GSM900 && cell.DirectionType == AntennaDirectionType.Omni)
            {
                index = 0;
                circle = true;
            }
            else if (cell.BandType == BTSBandType.GSM900 && cell.DirectionType == AntennaDirectionType.Beam)
            {
                index = 1;
            }
            else if (cell.BandType == BTSBandType.DSC1800 && cell.DirectionType == AntennaDirectionType.Omni)
            {
                index = 2;
                circle = true;
            }
            else if (cell.BandType == BTSBandType.DSC1800 && cell.DirectionType == AntennaDirectionType.Beam)
            {
                index = 3;
            }
            else if (cell.BandType == BTSBandType.CoSite && cell.DirectionType == AntennaDirectionType.Beam)
            {
                index = 4;
            }
        }

        private PointF[] getPointsNew(short direction, PointF point, float ratio, int index, List<PointF[]> points)
        {
            PointF[] pointsNew = new PointF[points[index].Length + 1];
            for (int i = 0; i < points[index].Length + 1; i++)
            {
                PointF pointNew = points[index][i % points[index].Length];
                if (pointNew.X == 0 && pointNew.Y == 0)
                {
                    pointNew.X = point.X;
                    pointNew.Y = point.Y;
                }
                else
                {
                    double pointRadius = Math.Sqrt(pointNew.X * ratio * pointNew.X * ratio + pointNew.Y * ratio * pointNew.Y * ratio);
                    double pointDirection = (direction - 90) / 180.0F * Math.PI + Math.Atan2(pointNew.Y, pointNew.X);
                    pointNew.X = point.X + (float)(pointRadius * Math.Cos(pointDirection));
                    pointNew.Y = point.Y + (float)(pointRadius * Math.Sin(pointDirection));
                }
                pointsNew[i] = pointNew;
            }

            return pointsNew;
        }

        private static List<PointF[]> antennaHighlightPoints = new List<PointF[]>();
        public List<PointF[]> AntennaHighlightPoints
        {
            get { return antennaHighlightPoints; }
        }

        private Antenna selectAntenna(Antenna antenna, MapOperation2 mop2, double scale)
        {
            DbPoint dPoint = new DbPoint(antenna.Longitude, antenna.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            float ratio = (float)(10000 / scale);
            if (DrawAlarm)
            {
                bool contain = mainModel.OutServiceInfoManager.isContainSnapShot(MasterCom.RAMS.Func.OutServiceInfo.NetType.GSM, antenna.BelongCell);
                if (contain)
                {
                    ratio *= ZoomScaleAlarm;
                }
            }
            int index = 0;
            bool circle = false;
            getAntennaTypeShapeInfo(antenna, ref index, ref circle);

            List<PointF[]> points = getAntennaPoints(antenna);

            if (circle)
            {
                RectangleF rect = new RectangleF(point.X + points[index][0].X * ratio, point.Y + points[index][0].Y * ratio, points[index][2].X * 2 * ratio, points[index][2].Y * 2 * ratio);
                DbRect dRect;
                gisAdapter.FromDisplay(rect, out dRect);
                if (mop2.CheckCenterInDRect(dRect))//Ellipse
                {
                    return antenna;
                }
            }
            else
            {
                PointF[] pointsNew = getPointsNew(antenna.Direction, point, ratio, index, points);
                DbPoint[] dpoints;
                gisAdapter.FromDisplay(pointsNew, out dpoints);
                if (mop2.CheckCenterInDbPoints(dpoints))
                {
                    return antenna;
                }
            }
            return null;
        }

        private List<PointF[]> getAntennaPoints(Antenna antenna)
        {
            List<PointF[]> points;
            if (mainModel.CellHighLightAll)
            {
                points = antennaHighlightPoints;
            }
            else
            {
                points = antennaPoints;
                foreach (Cell cell in mainModel.CellHighlightList)
                {
                    if (cell.Antennas[0] == antenna)
                    {
                        points = antennaHighlightPoints;
                        break;
                    }
                }
            }

            return points;
        }

        private void getAntennaTypeShapeInfo(Antenna antenna, ref int index, ref bool circle)
        {
            if (antenna.BandType == BTSBandType.GSM900 && antenna.DirectionType == AntennaDirectionType.Omni)
            {
                index = 0;
                circle = true;
            }
            else if (antenna.BandType == BTSBandType.GSM900 && antenna.DirectionType == AntennaDirectionType.Beam)
            {
                index = 1;
            }
            else if (antenna.BandType == BTSBandType.DSC1800 && antenna.DirectionType == AntennaDirectionType.Omni)
            {
                index = 2;
                circle = true;
            }
            else if (antenna.BandType == BTSBandType.DSC1800 && antenna.DirectionType == AntennaDirectionType.Beam)
            {
                index = 3;
            }
            else if (antenna.BandType == BTSBandType.CoSite && antenna.DirectionType == AntennaDirectionType.Beam)
            {
                index = 4;
            }
        }

        private BTS selectBTS(BTS bts, MapOperation2 mop2)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            RectangleF rect;
            if (bts.BandType == BTSBandType.GSM900)
            {
                rect = new RectangleF(point.X - SizeBTS / 2, point.Y - SizeBTS / 2, SizeBTS, SizeBTS);
            }
            else if (bts.BandType == BTSBandType.CoSite)
            {
                rect = new RectangleF(point.X - SizeBTS * 2f / 2, point.Y - SizeBTS * 2f / 2, SizeBTS * 2f, SizeBTS * 2f);
            }
            else
            {
                rect = new RectangleF(point.X - SizeBTS * 1.5f / 2, point.Y - SizeBTS * 1.5f / 2, SizeBTS * 1.5f, SizeBTS * 1.5f);
            }
            DbRect dRect;
            gisAdapter.FromDisplay(rect, out dRect);
            if (mop2.CheckCenterInDRect(dRect))
            {
                return bts;
            }
            return null;
        }

        private PlanBTS selectPlanBTS(PlanBTS bts, MapOperation2 mop2, double scale)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            float ratio = (float)(10000 / scale);
            RectangleF rect = new RectangleF(point.X - SizePlanBTS * ratio, point.Y - SizePlanBTS * ratio, SizePlanBTS * 2 * ratio, SizePlanBTS * 2 * ratio);
            DbRect dRect;
            gisAdapter.FromDisplay(rect, out dRect);
            if (mop2.CheckCenterInDRect(dRect)) //Ellipse
            {
                return bts;
            }
            return null;
        }

        private Repeater selectRepeater(Repeater repeater, MapOperation2 mop2)
        {
            DbPoint dPoint = new DbPoint(repeater.Longitude, repeater.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            RectangleF rect;
            rect = new RectangleF(point.X - SizeRepeater / 2, point.Y - SizeRepeater / 2, SizeRepeater, SizeRepeater);
            DbRect dRect;
            gisAdapter.FromDisplay(rect, out dRect);
            if (mop2.CheckCenterInDRect(dRect))
            {
                return repeater;
            }
            return null;
        }

        public Cell SelectCell(MapOperation2 mop2)
        {
            if (!IsVisible)
            {
                return null;
            }

            DbRect dRect = mop2.GetRegion().Bounds;
            Cell selectedCell = null;
            if (DrawAntenna)
            {
                selectedCell = getDrawServerAntennaCell(mop2, dRect);
                if (selectedCell != null)
                {
                    return selectedCell;
                }
                selectedCell = getDrawCurrentAntennaCell(mop2, dRect);
                if (selectedCell != null)
                {
                    return selectedCell;
                }
            }
            if (DrawCell)
            {
                selectedCell = getDrawServerCell(mop2, dRect);
                if (selectedCell != null)
                {
                    return selectedCell;
                }
                selectedCell = getDrawCurrentCell(mop2, dRect);
                if (selectedCell != null)
                {
                    return selectedCell;
                }
            }
            return null;
        }

        private Cell getDrawServerAntennaCell(MapOperation2 mop2, DbRect dRect)
        {
            Cell cell = null;
            List<Cell> cells = null;
            if (DrawServer)
            {
                cells = mainModel.ServerCells;
            }
            if (cells != null)
            {
                cell = getValidCell(mop2, dRect, cells, getSelectedCell);
            }

            return cell;
        }

        private Cell getDrawCurrentAntennaCell(MapOperation2 mop2, DbRect dRect)
        {
            Cell cell = null;
            List<Cell> cells = null;
            if (DrawCurrent)
            {
                cells = mainModel.CellManager.GetCurrentCells();
            }
            else
            {
                cells = mainModel.CellManager.GetCells(CurShowTimeAt);
            }
            if (cells.Count > 0)
            {
                cell = getValidCell(mop2, dRect, cells, getSelectedAntennaCell);
            }

            return cell;
        }

        private Cell getValidCell(MapOperation2 mop2, DbRect dRect, List<Cell> cells, Func func)
        {
            foreach (Cell cell in cells)
            {
                foreach (Antenna antenna in cell.Antennas)
                {
                    if (antenna.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                    {
                        Antenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                        if (selectedAntenna != null)
                        {
                            return func(cell, selectedAntenna);
                        }
                    }
                }
            }
            return null;
        }

        delegate Cell Func(Cell cell, Antenna selectedAntenna);

        private Cell getSelectedCell(Cell cell, Antenna selectedAntenna)
        {
            return cell;
        }

        private Cell getSelectedAntennaCell(Cell cell, Antenna selectedAntenna)
        {
            return selectedAntenna.BelongCell;
        }

        private Cell getDrawServerCell(MapOperation2 mop2, DbRect dRect)
        {
            List<Cell> cells = null;
            if (DrawServer)
            {
                cells = mainModel.ServerCells;
            }
            if (cells != null)
            {
                Cell selectedCell = getSelectedCell(mop2, dRect, cells);
                return selectedCell;
            }
            return null;
        }

        private Cell getDrawCurrentCell(MapOperation2 mop2, DbRect dRect)
        {
            List<Cell> cells;
            if (DrawCurrent)
            {
                cells = mainModel.CellManager.GetCurrentCells();
            }
            else
            {
                cells = mainModel.CellManager.GetCells(CurShowTimeAt);
            }
            if (cells != null)
            {
                Cell selectedCell = getSelectedCell(mop2, dRect, cells);
                return selectedCell;
            }

            return null;
        }

        private Cell getSelectedCell(MapOperation2 mop2, DbRect dRect, List<Cell> cells)
        {
            foreach (Cell cell in cells)
            {
                if (cell.Antennas.Count > 0
                    && cell.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                {
                    Cell selectedCell = selectCell(cell, mop2, mapScale);
                    if (selectedCell != null)
                    {
                        return selectedCell;
                    }
                }
            }
            return null;
        }

        public Repeater SelectRepeater(MapOperation2 mop2)
        {
            if (IsVisible)
            {
                DbRect dRect = mop2.GetRegion().Bounds;
                if (DrawRepeater)
                {
                    List<Repeater> repeaters = null;
                    if (DrawCurrent)
                    {
                        repeaters = mainModel.CellManager.GetCurrentRepeaters();
                    }
                    else
                    {
                        repeaters = mainModel.CellManager.GetRepeaters(CurShowTimeAt);
                    }
                    if (repeaters != null)
                    {
                        Repeater selectRepeater = getSelectRepeater(mop2, dRect, repeaters);
                        return selectRepeater;
                    }
                }
            }
            return null;
        }

        private Repeater getSelectRepeater(MapOperation2 mop2, DbRect dRect, List<Repeater> repeaters)
        {
            foreach (Repeater repeater in repeaters)
            {
                if (repeater.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01)
                    && (repeater.BandType == BTSBandType.GSM900 && DrawGSM900) || (repeater.BandType == BTSBandType.DSC1800 && DrawDSC1800))
                {
                    Repeater selectedRepeater = selectRepeater(repeater, mop2);
                    if (selectedRepeater != null)
                    {
                        return selectedRepeater;
                    }
                }
            }
            return null;
        }

        public PlanBTS SelectPlanBTS(MapOperation2 mop2)
        {
            if (IsVisible)
            {
                DbRect dRect = mop2.GetRegion().Bounds;
                if (DrawPlanBTS)
                {
                    List<PlanBTS> planBTSs = null;
                    if (DrawCurrent)
                    {
                        planBTSs = mainModel.CellManager.GetCurrentPlanBTSs();
                    }
                    else
                    {
                        planBTSs = mainModel.CellManager.GetPlanBTSs(CurShowTimeAt);
                    }
                    if (planBTSs != null)
                    {
                        PlanBTS selectPlanBTSs = getSelectedPlanBTS(mop2, dRect, planBTSs);
                        return selectPlanBTSs;
                    }
                }
            }
            return null;
        }

        private PlanBTS getSelectedPlanBTS(MapOperation2 mop2, DbRect dRect, List<PlanBTS> planBTSs)
        {
            foreach (PlanBTS planBTS in planBTSs)
            {
                if (planBTS.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                {
                    PlanBTS selectedPlanBTS = selectPlanBTS(planBTS, mop2, mapScale);
                    if (selectedPlanBTS != null)
                    {
                        return selectedPlanBTS;
                    }
                }
            }
            return null;
        }
        #endregion

        #region Only Public Use(No external use for the time being, just copy it)
        public List<Cell> GetCellsByRegion(MTPolygon mapRegion)
        {
            List<Cell> cellsInView = new List<Cell>();
            List<Cell> allCells = null;
            if (DrawCurrent)
            {
                allCells = mainModel.CellManager.GetCurrentCells();
            }
            else
            {
                allCells = mainModel.CellManager.GetCells(CurShowTimeAt);
            }
            if (allCells == null)
            {
                return new List<Cell>();
            }
            foreach (Cell cell in allCells)
            {
                if (mapRegion.Contains(cell.Longitude, cell.Latitude))
                {
                    cellsInView.Add(cell);
                }
            }
            return cellsInView;
        }

        public void RefreshBrushes()
        {
            makeBrushes();
        }

        #region IKMLExport 成员
        private XmlElement TransformPoint(Antenna antenna, int index, XmlDocument doc, string ColorCode)
        {
            StringBuilder location = new StringBuilder();
            foreach (LayerPoint p in antennaGEPoints)
            {
                if (p.Index == index)
                {
                    if (index == 1 || index == 3)
                    {
                        double angle1 = (90 - antenna.Direction) * Math.PI / 180;
                        double angle2 = Math.Atan(p.Y / p.X);
                        double angle3 = angle1 + angle2;
                        double r = Math.Sqrt(p.X * p.X + p.Y * p.Y);
                        double tarX = r * Math.Cos(angle3) + antenna.Longitude;
                        double tarY = r * Math.Sin(angle3) + antenna.Latitude;
                        location.Append(tarX.ToString() + "," + tarY.ToString() + ",10 ");
                    }
                    else if (index == 0 || index == 2)
                    {
                        double tarX = antenna.Longitude + p.X;
                        double tarY = antenna.Latitude + p.Y;
                        location.Append(tarX.ToString() + "," + tarY.ToString() + ",10 ");
                    }
                }
            }
            Brush brush;
            if (DrawServer)
            {
                for (int i = 0; i < mainModel.ServerCells.Count; i++)
                {
                    if (antenna.BelongCell == mainModel.ServerCells[i])
                    {
                        brush = BrushesServer[i];
                        SolidBrush sbrush = brush as SolidBrush;
                        ColorCode = ChangeColor(sbrush.Color);
                        break;
                    }
                }
            }
            return AddLine(doc, antenna.BelongCell.Name, antenna.Description, location.ToString(), ColorCode);
        }

        private string ChangeColor(Color color)//将Color类型转为string类型
        {
            string alpha = Convert.ToString(color.A, 16);
            string red = Convert.ToString(color.R, 16);
            string green = Convert.ToString(color.G, 16);
            string blue = Convert.ToString(color.B, 16);
            return DealwithColor(alpha) + DealwithColor(blue) + DealwithColor(green) + DealwithColor(red);
        }

        private string DealwithColor(string ColorCode)//对不足两位的颜色值进行补零处理
        {
            if (ColorCode.Length < 2)
            {
                for (int i = 0; i < 2 - ColorCode.Length; i++)
                {
                    ColorCode = "0" + ColorCode;
                }
            }
            return ColorCode;
        }

        private XmlElement AddLine(XmlDocument doc, string Name, string description, string location, string ColorCode)
        {
            XmlElement elemPlacemark = doc.CreateElement("Placemark");
            XmlElement elemName = doc.CreateElement("name");
            elemName.AppendChild(doc.CreateTextNode("小区：" + Name));
            elemPlacemark.AppendChild(elemName);
            XmlElement elemdescription = doc.CreateElement("description");
            elemdescription.AppendChild(doc.CreateTextNode(description));
            elemPlacemark.AppendChild(elemdescription);
            XmlElement elemstyle = doc.CreateElement("Style");
            elemPlacemark.AppendChild(elemstyle);
            XmlElement elemPolyStyle = doc.CreateElement("PolyStyle");
            elemstyle.AppendChild(elemPolyStyle);
            XmlElement elemcolor = doc.CreateElement("color");
            elemcolor.AppendChild(doc.CreateTextNode(ColorCode));
            elemPolyStyle.AppendChild(elemcolor);
            XmlElement elemoutline = doc.CreateElement("outline");
            elemoutline.AppendChild(doc.CreateTextNode("0"));
            elemPolyStyle.AppendChild(elemoutline);
            XmlElement elemPolygon = doc.CreateElement("Polygon");
            elemPlacemark.AppendChild(elemPolygon);
            XmlElement elemextrude = doc.CreateElement("extrude");
            elemextrude.AppendChild(doc.CreateTextNode("1"));
            elemPolygon.AppendChild(elemextrude);
            XmlElement elemaltitudeMode = doc.CreateElement("altitudeMode");
            elemaltitudeMode.AppendChild(doc.CreateTextNode("relativeToGround"));
            elemPolygon.AppendChild(elemaltitudeMode);
            XmlElement elemouterBoundaryIs = doc.CreateElement("outerBoundaryIs");
            elemPolygon.AppendChild(elemouterBoundaryIs);
            XmlElement elemLinearRing = doc.CreateElement("LinearRing");
            elemPolygon.AppendChild(elemLinearRing);
            XmlElement elemCoordinates = doc.CreateElement("coordinates");
            elemCoordinates.AppendChild(doc.CreateTextNode(location));
            elemLinearRing.AppendChild(elemCoordinates);
            elemouterBoundaryIs.AppendChild(elemLinearRing);
            return elemPlacemark;
        }


        void IKMLExport.ExportKml(KMLExporter exporter, XmlElement parentElem)
        {
            XmlElement layerElement = exporter.CreateFolder("小区信息", false);
            parentElem.AppendChild(layerElement);

            if (DrawAntenna && mapScale < 1000000)
            {
                List<Antenna> antennas = null;
                if (DrawCurrent)
                {
                    antennas = mainModel.CellManager.GetCurrentAntennas();
                }
                else
                {
                    antennas = mainModel.CellManager.GetAntennas(CurShowTimeAt);
                }
                if (antennas != null)
                {
                    foreach (Antenna antenna in antennas)
                    {
                        int index = 0;
                        string ColorCode = "FFCC0066";
                        setIndexColorCodeByType(antenna, ref index, ref ColorCode);
                        layerElement.AppendChild(TransformPoint(antenna, index, exporter.getRootNode(), ColorCode));
                    }
                }
            }
        }

        private void setIndexColorCodeByType(Antenna antenna, ref int index, ref string ColorCode)
        {
            if (antenna.BandType == BTSBandType.GSM900 && antenna.DirectionType == AntennaDirectionType.Omni)
            {
                index = 0;
                ColorCode = "FFCC0066";
            }
            else if (antenna.BandType == BTSBandType.GSM900 && antenna.DirectionType == AntennaDirectionType.Beam)
            {
                index = 1;
                ColorCode = "FFCC0066";
            }
            else if (antenna.BandType == BTSBandType.DSC1800 && antenna.DirectionType == AntennaDirectionType.Omni)
            {
                index = 2;
                ColorCode = "FFFF9966";
            }
            else if (antenna.BandType == BTSBandType.DSC1800 && antenna.DirectionType == AntennaDirectionType.Beam)
            {
                index = 3;
                ColorCode = "FFFF9966";
            }
        }
        #endregion

        internal int MakeShpFile(string filename)
        {
            Shapefile shpFile = new Shapefile();
            try
            {
                int idIdx = 0;
                int fLongId = idIdx++;
                int fLatId = idIdx++;
                int fNameId = idIdx++;
                int fCodeId = idIdx++;
                int fLacId = idIdx++;
                int fCiId = idIdx++;
                int fBcchId = idIdx++;
                int fBsicId = idIdx++;
                int fTchId = idIdx++;
                int fDirectionId = idIdx++;
                int fDownwordId = idIdx++;
                int fBscId = idIdx++;
                int fMscId = idIdx++;
                int fTypeId = idIdx;

                bool result = shpFile.CreateNew("", MapWinGIS.ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }

                ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 7, 0, ref fLongId);
                ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 7, 0, ref fLatId);
                ShapeHelper.InsertNewField(shpFile, "Name", FieldType.STRING_FIELD, 120, 0, ref fNameId);
                ShapeHelper.InsertNewField(shpFile, "Code", FieldType.STRING_FIELD, 7, 30, ref fCodeId);
                ShapeHelper.InsertNewField(shpFile, "LAC", FieldType.INTEGER_FIELD, 7, 0, ref fLacId);
                ShapeHelper.InsertNewField(shpFile, "CI", FieldType.INTEGER_FIELD, 7, 0, ref fCiId);
                ShapeHelper.InsertNewField(shpFile, "BCCH", FieldType.INTEGER_FIELD, 7, 0, ref fBcchId);
                ShapeHelper.InsertNewField(shpFile, "BSIC", FieldType.INTEGER_FIELD, 7, 0, ref fBsicId);
                ShapeHelper.InsertNewField(shpFile, "TCH", FieldType.STRING_FIELD, 300, 0, ref fTchId);
                ShapeHelper.InsertNewField(shpFile, "方向角", FieldType.INTEGER_FIELD, 7, 0, ref fDirectionId);
                ShapeHelper.InsertNewField(shpFile, "下倾角", FieldType.INTEGER_FIELD, 7, 0, ref fDownwordId);
                ShapeHelper.InsertNewField(shpFile, "BSC", FieldType.STRING_FIELD, 7, 30, ref fBscId);
                ShapeHelper.InsertNewField(shpFile, "MSC", FieldType.STRING_FIELD, 7, 30, ref fMscId);
                ShapeHelper.InsertNewField(shpFile, "室内室外", FieldType.STRING_FIELD, 7, 30, ref fTypeId);
                List<Cell> cells = null;
                if (DrawCurrent)
                {
                    cells = mainModel.CellManager.GetCurrentCells();
                }
                else
                {
                    cells = mainModel.CellManager.GetCells(CurShowTimeAt);
                }
                double radius = 0.00048775;//大约50米
                foreach (Cell cell in cells)
                {
                    Shape shp = null;
                    shp = getShapeByType(radius, cell, shp);
                    int shpIdx = 0;
                    shpFile.EditInsertShape(shp, ref shpIdx);
                    shpFile.EditCellValue(fLongId, shpIdx, cell.Longitude);
                    shpFile.EditCellValue(fLatId, shpIdx, cell.Latitude);
                    shpFile.EditCellValue(fNameId, shpIdx, cell.Name);
                    shpFile.EditCellValue(fCodeId, shpIdx, cell.Code);
                    shpFile.EditCellValue(fLacId, shpIdx, cell.LAC);
                    shpFile.EditCellValue(fCiId, shpIdx, cell.CI);
                    shpFile.EditCellValue(fBcchId, shpIdx, cell.BCCH);
                    shpFile.EditCellValue(fBsicId, shpIdx, (int)cell.BSIC);
                    string tch = cell.TCHDescWithComma;
                    if (tch.Length >= 255)
                    {
                        tch = tch.Substring(0, 254);
                    }
                    shpFile.EditCellValue(fTchId, shpIdx, tch);
                    shpFile.EditCellValue(fDirectionId, shpIdx, cell.Direction);
                    shpFile.EditCellValue(fDownwordId, shpIdx, cell.Downword);
                    shpFile.EditCellValue(fBscId, shpIdx, cell.BelongBSCName);
                    shpFile.EditCellValue(fMscId, shpIdx, cell.BelongMSCName);
                    shpFile.EditCellValue(fTypeId, shpIdx, cell.Type.ToString());
                }
                shpFile.SaveAs(filename, null);
                return 1;
            }
            catch
            {
                return -1;
            }
            finally
            {
                shpFile.Close();
            }
        }

        private static Shape getShapeByType(double radius, Cell cell, Shape shp)
        {
            if (cell.BandType == BTSBandType.GSM900 && cell.DirectionType == AntennaDirectionType.Omni)
            {
                shp = ShapeHelper.CreateCircleShape(cell.Longitude, cell.Latitude, radius);
            }
            else if (cell.BandType == BTSBandType.GSM900 && cell.DirectionType == AntennaDirectionType.Beam)
            {
                shp = ShapeHelper.CreateOutdoorCellShape(cell.Longitude, cell.Latitude, cell.EndPointLongitude, cell.EndPointLatitude, cell.Direction);
            }
            else if (cell.BandType == BTSBandType.DSC1800 && cell.DirectionType == AntennaDirectionType.Omni)
            {
                shp = ShapeHelper.CreateCircleShape(cell.Longitude, cell.Latitude, radius);
            }
            else if (cell.BandType == BTSBandType.DSC1800 && cell.DirectionType == AntennaDirectionType.Beam)
            {
                shp = ShapeHelper.CreateOutdoorCellShape(cell.Longitude, cell.Latitude, cell.EndPointLongitude, cell.EndPointLatitude, cell.Direction);
            }

            return shp;
        }

        private List<LayerPoint> antennaGEHighlightPoints = new List<LayerPoint>();
        public List<LayerPoint> AntennaGEHighlightPoints
        {
            get { return antennaGEHighlightPoints; }
        }
        
        public Font FontCellHighlightLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public Font FontAntennaHighlightLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);

        /// <summary>
        /// 初始化放大，缩小图元的Path
        /// </summary>
        public void InitCellPath()
        {
            float multiple = CD.CellLengthRadio;
            if (multiple <= 0)
            {
                return;
            }
            cellHighlightPoints.Clear();
            cellHighlightPaths.Clear();
            float radius = 4;
            radius = radius * (multiple);
            cellHighlightPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 36;
            radius = radius * (multiple);
            float x1 = 0.75f;
            float y1 = 0.14f;
            float x2 = 0.85f;
            float y2 = 0.15f;
            float x3 = 0.92f;
            float y3 = 0.14f;
            float x4 = 0.96f;
            float y4 = 0.12f;
            float x5 = 0.99f;
            float y5 = 0.07f;
            cellHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, 0), 
                    new PointF(radius * x1, -radius * y1), 
                    new PointF(radius * x2, -radius * y2), 
                    new PointF(radius * x3, -radius * y3), 
                    new PointF(radius * x4, -radius * y4), 
                    new PointF(radius * x5, -radius * y5), 
                    new PointF(radius, 0),
                    new PointF(radius * x5, radius * y5), 
                    new PointF(radius * x4, radius * y4), 
                    new PointF(radius * x3, radius * y3), 
                    new PointF(radius * x2, radius * y2), 
                    new PointF(radius * x1, radius * y1)
                });
            radius = 2;
            radius = radius * (multiple);
            cellHighlightPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 22;
            radius = radius * (multiple);
            x1 = 0.75f;
            y1 = 0.29f;
            x2 = 0.85f;
            y2 = 0.30f;
            x3 = 0.92f;
            y3 = 0.28f;
            x4 = 0.96f;
            y4 = 0.24f;
            x5 = 0.99f;
            y5 = 0.14f;
            cellHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, 0), 
                    new PointF(radius * x1, -radius * y1), 
                    new PointF(radius * x2, -radius * y2), 
                    new PointF(radius * x3, -radius * y3), 
                    new PointF(radius * x4, -radius * y4), 
                    new PointF(radius * x5, -radius * y5), 
                    new PointF(radius, 0),
                    new PointF(radius * x5, radius * y5), 
                    new PointF(radius * x4, radius * y4), 
                    new PointF(radius * x3, radius * y3), 
                    new PointF(radius * x2, radius * y2), 
                    new PointF(radius * x1, radius * y1)
                });

            radius = 30;
            radius = radius * (multiple);
            x1 = 0.75f;
            y1 = 0.29f;
            cellHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, 0), 
                    new PointF(radius * x1, -radius * y1), 
                    new PointF(radius, 0), 
                    new PointF(radius * x1, radius * y1)
                });

            GraphicsPath path = new GraphicsPath();
            path.AddEllipse(cellHighlightPoints[0][0].X, cellHighlightPoints[0][0].Y, cellHighlightPoints[0][2].X * 2, cellHighlightPoints[0][2].Y * 2);
            cellHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(cellHighlightPoints[1]);
            cellHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddEllipse(cellHighlightPoints[2][0].X, cellHighlightPoints[2][0].Y, cellHighlightPoints[2][2].X * 2, cellHighlightPoints[2][2].Y * 2);
            cellHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(cellHighlightPoints[3]);
            cellHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(cellHighlightPoints[4]);
            cellHighlightPaths.Add(path);
            foreach (GraphicsPath pathTemp in cellHighlightPaths)
            {
                PathGradientBrush pathGradientBrush = new PathGradientBrush(pathTemp);
                pathGradientBrush.CenterPoint = new PointF(0, 0);
                cellHighlightPathGradientBrushs.Add(pathGradientBrush);
            }

            antennaHighlightPoints.Clear();
            AntennaHighlightPaths.Clear();
            radius = 4;
            radius = radius * (multiple);
            antennaHighlightPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 36;
            radius = radius * (multiple);
            antennaHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, -2), 
                    new PointF(radius - 3, -2), 
                    new PointF(radius - 4, -6), 
                    new PointF(radius+2, 0), 
                    new PointF(radius - 4, 6), 
                    new PointF(radius - 3, 2), 
                    new PointF(0, 2), 
                });
            radius = 2;
            radius = radius * (multiple);
            antennaHighlightPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 22;
            radius = radius * (multiple);
            antennaHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, -3), 
                    new PointF(radius - 6, -3), 
                    new PointF(radius - 6, -6), 
                    new PointF(radius, 0), 
                    new PointF(radius - 6, 6), 
                    new PointF(radius - 6, 3), 
                    new PointF(0, 3), 
                });
            radius = 30;
            radius = radius * (multiple);
            antennaHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, -3), 
                    new PointF(radius - 6, -3), 
                    new PointF(radius - 6, -6), 
                    new PointF(radius, 0), 
                    new PointF(radius - 6, 6), 
                    new PointF(radius - 6, 3), 
                    new PointF(0, 3), 
                });

            path = new GraphicsPath();
            path.AddEllipse(antennaHighlightPoints[0][0].X, antennaHighlightPoints[0][0].Y, antennaHighlightPoints[0][2].X * 2, antennaHighlightPoints[0][2].Y * 2);
            AntennaHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaHighlightPoints[1]);
            AntennaHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddEllipse(antennaHighlightPoints[2][0].X, antennaHighlightPoints[2][0].Y, antennaHighlightPoints[2][2].X * 2, antennaHighlightPoints[2][2].Y * 2);
            AntennaHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaHighlightPoints[3]);
            AntennaHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaHighlightPoints[4]);
            AntennaHighlightPaths.Add(path);

            antennaGEHighlightPoints.Clear();
            double radiusGE = 0.00004;
            radiusGE = radiusGE * (multiple);
            int part = 36;//将一个圆分为几份
            antennaGEHighlightPoints = GEdrawCircle(radiusGE, part, 0);

            radiusGE = 0.00036;
            radiusGE = radiusGE * (multiple);
            antennaGEHighlightPoints.Add(new LayerPoint(0, -0.00001, 1));
            antennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00003, -0.00001, 1));
            antennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00005, -0.00005, 1));
            antennaGEHighlightPoints.Add(new LayerPoint(radiusGE, 0, 1));
            antennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00005, 0.00005, 1));
            antennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00003, 0.00001, 1));
            antennaGEHighlightPoints.Add(new LayerPoint(0, 0.00001, 1));
            antennaGEHighlightPoints.Add(new LayerPoint(0, -0.00001, 1));

            radiusGE = 0.00002;
            radiusGE = radiusGE * (multiple);
            part = 36;
            antennaGEHighlightPoints.AddRange(GEdrawCircle(radiusGE, part, 2));

            radiusGE = 0.00022;
            radiusGE = radiusGE * (multiple);
            antennaGEHighlightPoints.Add(new LayerPoint(0, -0.00003, 3));
            antennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00006, -0.00003, 3));
            antennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00006, -0.00006, 3));
            antennaGEHighlightPoints.Add(new LayerPoint(radiusGE, 0, 3));
            antennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00006, 0.00006, 3));
            antennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00006, 0.00003, 3));
            antennaGEHighlightPoints.Add(new LayerPoint(0, 0.00003, 3));
            antennaGEHighlightPoints.Add(new LayerPoint(0, -0.00003, 3));

            float fSize = float.Parse((1 + Math.Log10(multiple)).ToString());
            FontCellHighlightLabel = new Font(new FontFamily("宋体"), 9 * fSize, FontStyle.Bold);
            FontAntennaHighlightLabel = new Font(new FontFamily("宋体"), 9 * fSize, FontStyle.Bold);
        }
        #endregion
    }

    public class LayerPoint
    {
        public double X { get; set; }
        public double Y { get; set; }
        public int Index { get; set; }
        public LayerPoint(double x, double y, int index)
        {
            X = x;
            Y = y;
            Index = index;
        }
    }
}
