﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTCellSet
{
    public partial class CellServiceConditionDlg : BaseDialog
    {
        public CellServiceConditionDlg()
            : base()
        {
            InitializeComponent();
        }

        public void GetCondition(out int sec)
        {
            sec = (int)numMaxDelaySec.Value;
        }

        public void SetCondition(int sec)
        {
            numMaxDelaySec.Value = (decimal)sec;
        }

    }
}
