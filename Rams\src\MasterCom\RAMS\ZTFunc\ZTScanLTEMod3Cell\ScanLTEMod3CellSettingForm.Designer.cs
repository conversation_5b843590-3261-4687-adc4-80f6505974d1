﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ScanLTEMod3CellSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.numDiffRxlev = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.numMinRxlev = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numSampleCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.numMaxSinr = new DevExpress.XtraEditors.SpinEdit();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffRxlev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxlev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSinr.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(238, 95);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(12, 12);
            this.labelControl8.TabIndex = 20;
            this.labelControl8.Text = "dB";
            // 
            // numDiffRxlev
            // 
            this.numDiffRxlev.EditValue = new decimal(new int[] {
            12,
            0,
            0,
            0});
            this.numDiffRxlev.Location = new System.Drawing.Point(142, 92);
            this.numDiffRxlev.Name = "numDiffRxlev";
            this.numDiffRxlev.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDiffRxlev.Properties.Appearance.Options.UseFont = true;
            this.numDiffRxlev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDiffRxlev.Properties.IsFloatValue = false;
            this.numDiffRxlev.Properties.Mask.EditMask = "N00";
            this.numDiffRxlev.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numDiffRxlev.Properties.MinValue = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numDiffRxlev.Size = new System.Drawing.Size(90, 20);
            this.numDiffRxlev.TabIndex = 16;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(71, 95);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(60, 12);
            this.labelControl7.TabIndex = 19;
            this.labelControl7.Text = "相对覆盖≤";
            // 
            // numMinRxlev
            // 
            this.numMinRxlev.EditValue = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            this.numMinRxlev.Location = new System.Drawing.Point(142, 22);
            this.numMinRxlev.Name = "numMinRxlev";
            this.numMinRxlev.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMinRxlev.Properties.Appearance.Options.UseFont = true;
            this.numMinRxlev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinRxlev.Properties.IsFloatValue = false;
            this.numMinRxlev.Properties.Mask.EditMask = "N00";
            this.numMinRxlev.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numMinRxlev.Properties.MinValue = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMinRxlev.Size = new System.Drawing.Size(90, 20);
            this.numMinRxlev.TabIndex = 15;
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(238, 26);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(18, 12);
            this.labelControl6.TabIndex = 18;
            this.labelControl6.Text = "dBm";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(71, 28);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(60, 12);
            this.labelControl5.TabIndex = 17;
            this.labelControl5.Text = "最强信号≥";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(59, 131);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(72, 12);
            this.labelControl1.TabIndex = 21;
            this.labelControl1.Text = "总采样点数≥";
            // 
            // numSampleCount
            // 
            this.numSampleCount.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numSampleCount.Location = new System.Drawing.Point(142, 128);
            this.numSampleCount.Name = "numSampleCount";
            this.numSampleCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSampleCount.Properties.Appearance.Options.UseFont = true;
            this.numSampleCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSampleCount.Properties.IsFloatValue = false;
            this.numSampleCount.Properties.Mask.EditMask = "N00";
            this.numSampleCount.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numSampleCount.Size = new System.Drawing.Size(90, 20);
            this.numSampleCount.TabIndex = 22;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(95, 60);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(36, 12);
            this.labelControl2.TabIndex = 23;
            this.labelControl2.Text = "SINR≤";
            // 
            // numMaxSinr
            // 
            this.numMaxSinr.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numMaxSinr.Location = new System.Drawing.Point(142, 57);
            this.numMaxSinr.Name = "numMaxSinr";
            this.numMaxSinr.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMaxSinr.Properties.Appearance.Options.UseFont = true;
            this.numMaxSinr.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxSinr.Properties.IsFloatValue = false;
            this.numMaxSinr.Properties.Mask.EditMask = "N00";
            this.numMaxSinr.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numMaxSinr.Properties.MinValue = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numMaxSinr.Size = new System.Drawing.Size(90, 20);
            this.numMaxSinr.TabIndex = 24;
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(228, 171);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 25;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(147, 171);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 26;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // ScanLTEMod3CellSettingForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(315, 210);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.numMaxSinr);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.numSampleCount);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.numDiffRxlev);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.numMinRxlev);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl5);
            this.Name = "ScanLTEMod3CellSettingForm";
            this.Text = "LTE模三干扰查询设置";
            ((System.ComponentModel.ISupportInitialize)(this.numDiffRxlev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxlev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSinr.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.SpinEdit numDiffRxlev;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit numMinRxlev;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit numSampleCount;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit numMaxSinr;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
    }
}