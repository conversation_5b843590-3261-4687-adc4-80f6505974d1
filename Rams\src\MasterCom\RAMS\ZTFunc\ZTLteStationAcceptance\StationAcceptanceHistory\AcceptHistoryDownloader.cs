﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using Excel = Microsoft.Office.Interop.Excel;

using System.Data;
using System.Data.Sql;
using System.Data.SqlClient;

namespace MasterCom.RAMS.ZTFunc.AcceptHistory
{
    class DiySqlCellSelect : DIYSQLBase
    {
        public DiySqlCellSelect(MainModel mainModel)
            : base(mainModel)
        {
            this.MainDB = true;
        }

        public override string Name
        {
            get { return "DIYSQLCellSelect"; }
        }

        public List<AcceptHistorySite> ListResultItem
        {
            get;
            set;
        }

        protected override string getSqlTextString()
        {
            return "SELECT "
                + "[sid],[sectorid],[验证通过],"
                + "[下载SINR好点],[下载SINR中点],[下载SINR差点],[下载RSRP好点],[下载RSRP中点],[下载RSRP差点],"
                + "[上传SINR好点],[上传SINR中点],[上传SINR差点],[上传RSRP好点],[上传RSRP中点],[上传RSRP差点],"
                + "[下载速率好点],[下载速率中点],[下载速率差点],[上传速率好点],[上传速率中点],[上传速率差点]," 
                + "[RRC连接尝试次数],[RRC连接成功次数],[ERAB连接尝试次数],[ERAB连接成功次数],[Access尝试次数],[Access成功次数],"
                + "[被叫CSFB尝试次数],[被叫CSFB成功次数],[34G尝试次数],[34G成功次数] "
                + "FROM tb_lte_cell_acceptance";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[31];
            rType[0] = E_VType.E_Int64;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            for (int i = 3; i <= 20; i++)
            {
                rType[i] = E_VType.E_Float;
            }
            for (int i = 21; i <= 30; i++)
            {
                rType[i] = E_VType.E_Int;
            }

            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    AcceptHistoryCell itemCell = new AcceptHistoryCell();
                    itemCell.SID= package.Content.GetParamInt64();//sid
                    itemCell.CellStr = package.Content.GetParamInt().ToString();//sectorid
                    itemCell.IsPass = package.Content.GetParamString();//验证通过
                    itemCell.DownSinrGood = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//下载SINR好点
                    itemCell.DownSinrTall = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//[下载SINR中点],
                    itemCell.DownSinrBad = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//[下载SINR差点],
                    itemCell.DownRsrpGood = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//[下载RSRP好点]
                    itemCell.DownRsrpTall = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//,[下载RSRP中点],
                    itemCell.DownRsrpBad = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//[下载RSRP差点]
                    itemCell.UpSinrGood = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//"[上传SINR好点],
                    itemCell.UpSinrTall = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//[上传SINR中点],
                    itemCell.UpSinrBad = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//[上传SINR差点],
                    itemCell.UpRsrpGood = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//[上传RSRP好点],
                    itemCell.UpRsrpTall = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//[上传RSRP中点],
                    itemCell.UpRsrpBad = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//[上传RSRP差点],
                    itemCell.DownSpeedGood = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//"[下载速率好点],
                    itemCell.DownSpeedTall = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//[下载速率中点],
                    itemCell.DownSpeedBad = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//下载速率差点],
                    itemCell.UpSpeedGood = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//上传速率好点],
                    itemCell.UpSpeedTall = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//上传速率中点],
                    itemCell.UpSpeedBad = DBVerifier.CheckAndGetFResult(package.Content.GetParamFloat());//上传速率差点],
                    itemCell.RrcRequestCnt = DBVerifier.CheckAndGetIResult(package.Content.GetParamInt());// "[RRC连接尝试次数],
                    itemCell.RrcSuccessCnt = DBVerifier.CheckAndGetIResult(package.Content.GetParamInt());//[RRC连接成功次数],
                    itemCell.ErabRequestCnt = DBVerifier.CheckAndGetIResult(package.Content.GetParamInt());//ERAB连接尝试次数],
                    itemCell.ErabSuccessCnt = DBVerifier.CheckAndGetIResult(package.Content.GetParamInt());//ERAB连接成功次数],
                    itemCell.CsfbRequestCnt = DBVerifier.CheckAndGetIResult(package.Content.GetParamInt());//被叫CSFB尝试次数],
                    itemCell.CsfbSuccessCnt = DBVerifier.CheckAndGetIResult(package.Content.GetParamInt());//被叫CSFB成功次数],
                    itemCell.AccessRequestCnt = DBVerifier.CheckAndGetIResult(package.Content.GetParamInt());//Access尝试次数],
                    itemCell.AccessSuccessCnt = DBVerifier.CheckAndGetIResult(package.Content.GetParamInt());//Access成功次数],
                    itemCell.ReselectRequestCnt = DBVerifier.CheckAndGetIResult(package.Content.GetParamInt());//[34G尝试次数],
                    itemCell.ReselectSuccessCnt = DBVerifier.CheckAndGetIResult(package.Content.GetParamInt());//34G成功次数] 

                    // O(M * N)
                    foreach (AcceptHistorySite itemSite in this.ListResultItem)
                    {
                        if (itemSite.ID == itemCell.SID)
                        {
                            itemCell.HandoverRequestCnt = itemSite.HandoverRequestCnt;
                            itemCell.HandoverSuccessCnt = itemSite.HandoverSuccessCnt;
                            itemSite.HistoryCells.Add(itemCell);
                            break;
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    class DiySqlSiteSelect : DIYSQLBase
    {
        public DiySqlSiteSelect(MainModel mainModel)
            : base(mainModel)
        {
            MainDB = true;
            ListResultItem = new List<AcceptHistorySite>();
        }

        public override string Name
        {
            get { return "DIYSQLSiteSelect"; }
        }

        public List<AcceptHistorySite> ListResultItem
        {
            get;
            private set;
        }

        protected override string getSqlTextString()
        {
            return  "SELECT "
                + "[id],[日期],[地州],[基站名称],[基站类型],[ENodeBID],[验证通过],[系统内切换尝试次数],[系统内切换成功次数] "
                + "FROM tb_lte_site_acceptance";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[9];
            rType[0] = E_VType.E_Int64;//id
            rType[1] = E_VType.E_Int;//日期
            rType[2] = E_VType.E_String;//地州
            rType[3] = E_VType.E_String;//基站名称
            rType[4] = E_VType.E_String;//基站类型
            rType[5] = E_VType.E_Int;//ENodeBID
            rType[6] = E_VType.E_String;//验证通过
            rType[7] = E_VType.E_Int;//系统内切换尝试次数
            rType[8] = E_VType.E_Int;//系统内切换成功次数
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    AcceptHistorySite item = new AcceptHistorySite();
                    item.ID = package.Content.GetParamInt64();
                    item.DateTimeStr = new DateTime(1970, 1, 1, 8, 0, 0, 0).AddSeconds(package.Content.GetParamInt()).ToString("yyyy-MM-dd HH:mm:ss");
                    item.CityName = package.Content.GetParamString();
                    item.SiteName = package.Content.GetParamString();
                    item.SiteTypeName = package.Content.GetParamString();
                    item.EnodebID = package.Content.GetParamInt();
                    item.IsPass = package.Content.GetParamString();
                    item.HandoverRequestCnt = package.Content.GetParamInt();
                    item.HandoverSuccessCnt = package.Content.GetParamInt();

                    this.ListResultItem.Add(item);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    static class DBVerifier
    {
        public const int NULLNUMBER = -1000000;

        /// <summary>
        /// 检查对象的值，若是 -10000000（上传的时候约定的值）则当做空值处理，
        /// 返回null
        /// </summary>
        /// <param name="ob"></param>
        /// <returns></returns>
        public static int? CheckAndGetIResult(int arg)
        {
            return arg == DBVerifier.NULLNUMBER ? null : (int?)arg;
        }

        public static double? CheckAndGetFResult(float arg)
        {
            return (arg == DBVerifier.NULLNUMBER ? null : (double?)Math.Round(arg, 2));
        }
    }
}
