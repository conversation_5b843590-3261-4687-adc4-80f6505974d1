﻿namespace MasterCom.RAMS.Func
{
    partial class CellGridBlockResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.ListViewBlocks = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBlockID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnServCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colPrimaryType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSpecificType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDetail = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatus = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCgID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTlLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTlLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBrLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBrLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnIsProb = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnInBlock = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDirection = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.expandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.collapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.exportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.colSuggest = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.ListViewBlocks)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // ListViewBlocks
            // 
            this.ListViewBlocks.AllColumns.Add(this.olvColumnSN);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnType);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnBlockID);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnLAC);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnCI);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnServCell);
            this.ListViewBlocks.AllColumns.Add(this.colPrimaryType);
            this.ListViewBlocks.AllColumns.Add(this.colSpecificType);
            this.ListViewBlocks.AllColumns.Add(this.colSuggest);
            this.ListViewBlocks.AllColumns.Add(this.colDetail);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnStatus);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnCgID);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnTlLng);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnTlLat);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnBrLng);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnBrLat);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnIsProb);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnInBlock);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnAvgRSRP);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnDirection);
            this.ListViewBlocks.AllColumns.Add(this.olvColumnDistance);
            this.ListViewBlocks.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnType,
            this.olvColumnBlockID,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnServCell,
            this.colPrimaryType,
            this.colSpecificType,
            this.colSuggest,
            this.colDetail,
            this.olvColumnStatus,
            this.olvColumnCgID,
            this.olvColumnTlLng,
            this.olvColumnTlLat,
            this.olvColumnBrLng,
            this.olvColumnBrLat,
            this.olvColumnIsProb,
            this.olvColumnInBlock,
            this.olvColumnAvgRSRP,
            this.olvColumnDirection,
            this.olvColumnDistance});
            this.ListViewBlocks.ContextMenuStrip = this.contextMenuStrip1;
            this.ListViewBlocks.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewBlocks.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewBlocks.FullRowSelect = true;
            this.ListViewBlocks.GridLines = true;
            this.ListViewBlocks.HeaderWordWrap = true;
            this.ListViewBlocks.IsNeedShowOverlay = false;
            this.ListViewBlocks.Location = new System.Drawing.Point(0, 0);
            this.ListViewBlocks.Name = "ListViewBlocks";
            this.ListViewBlocks.OwnerDraw = true;
            this.ListViewBlocks.ShowGroups = false;
            this.ListViewBlocks.ShowImagesOnSubItems = true;
            this.ListViewBlocks.Size = new System.Drawing.Size(1257, 507);
            this.ListViewBlocks.TabIndex = 8;
            this.ListViewBlocks.UseCompatibleStateImageBehavior = false;
            this.ListViewBlocks.View = System.Windows.Forms.View.Details;
            this.ListViewBlocks.VirtualMode = true;
            this.ListViewBlocks.ItemChecked += new System.Windows.Forms.ItemCheckedEventHandler(this.ListViewBlocks_ItemChecked);
            this.ListViewBlocks.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewBlocks_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnType
            // 
            this.olvColumnType.HeaderFont = null;
            this.olvColumnType.Text = "问题点类型";
            this.olvColumnType.Width = 80;
            // 
            // olvColumnBlockID
            // 
            this.olvColumnBlockID.HeaderFont = null;
            this.olvColumnBlockID.Text = "问题点ID";
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            this.olvColumnLAC.Width = 80;
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            this.olvColumnCI.Width = 80;
            // 
            // olvColumnServCell
            // 
            this.olvColumnServCell.HeaderFont = null;
            this.olvColumnServCell.Text = "小区";
            this.olvColumnServCell.Width = 150;
            // 
            // colPrimaryType
            // 
            this.colPrimaryType.HeaderFont = null;
            this.colPrimaryType.Text = "主因";
            // 
            // colSpecificType
            // 
            this.colSpecificType.HeaderFont = null;
            this.colSpecificType.Text = "具因";
            // 
            // colDetail
            // 
            this.colDetail.HeaderFont = null;
            this.colDetail.Text = "详情";
            // 
            // olvColumnStatus
            // 
            this.olvColumnStatus.HeaderFont = null;
            this.olvColumnStatus.Text = "问题点预判标识";
            // 
            // olvColumnCgID
            // 
            this.olvColumnCgID.HeaderFont = null;
            this.olvColumnCgID.Text = "栅格ID";
            // 
            // olvColumnTlLng
            // 
            this.olvColumnTlLng.HeaderFont = null;
            this.olvColumnTlLng.Text = "左上角经度";
            this.olvColumnTlLng.Width = 80;
            // 
            // olvColumnTlLat
            // 
            this.olvColumnTlLat.HeaderFont = null;
            this.olvColumnTlLat.Text = "左上角纬度";
            this.olvColumnTlLat.Width = 80;
            // 
            // olvColumnBrLng
            // 
            this.olvColumnBrLng.HeaderFont = null;
            this.olvColumnBrLng.Text = "右下角经度";
            this.olvColumnBrLng.Width = 80;
            // 
            // olvColumnBrLat
            // 
            this.olvColumnBrLat.HeaderFont = null;
            this.olvColumnBrLat.Text = "右下角纬度";
            this.olvColumnBrLat.Width = 80;
            // 
            // olvColumnIsProb
            // 
            this.olvColumnIsProb.HeaderFont = null;
            this.olvColumnIsProb.Text = "是否是问题栅格";
            // 
            // olvColumnInBlock
            // 
            this.olvColumnInBlock.HeaderFont = null;
            this.olvColumnInBlock.Text = "是否汇聚在问题点内";
            // 
            // olvColumnAvgRSRP
            // 
            this.olvColumnAvgRSRP.HeaderFont = null;
            this.olvColumnAvgRSRP.Text = "平均RSRP";
            // 
            // olvColumnDirection
            // 
            this.olvColumnDirection.HeaderFont = null;
            this.olvColumnDirection.Text = "方向角";
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "和小区的距离";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.expandAll,
            this.collapsAll,
            this.exportExcel});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(130, 70);
            // 
            // expandAll
            // 
            this.expandAll.Name = "expandAll";
            this.expandAll.Size = new System.Drawing.Size(129, 22);
            this.expandAll.Text = "展开所有";
            this.expandAll.Click += new System.EventHandler(this.expandAll_Click);
            // 
            // collapsAll
            // 
            this.collapsAll.Name = "collapsAll";
            this.collapsAll.Size = new System.Drawing.Size(129, 22);
            this.collapsAll.Text = "折叠所有";
            this.collapsAll.Click += new System.EventHandler(this.collapsAll_Click);
            // 
            // exportExcel
            // 
            this.exportExcel.Name = "exportExcel";
            this.exportExcel.Size = new System.Drawing.Size(129, 22);
            this.exportExcel.Text = "导出Excel";
            this.exportExcel.Click += new System.EventHandler(this.exportExcel_Click);
            // 
            // colSuggest
            // 
            this.colSuggest.HeaderFont = null;
            this.colSuggest.Text = "建议方案";
            // 
            // CellGridBlockResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1257, 507);
            this.Controls.Add(this.ListViewBlocks);
            this.Name = "CellGridBlockResultForm";
            this.Text = "栅格问题点";
            ((System.ComponentModel.ISupportInitialize)(this.ListViewBlocks)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView ListViewBlocks;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnType;
        private BrightIdeasSoftware.OLVColumn olvColumnBlockID;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnServCell;
        private BrightIdeasSoftware.OLVColumn olvColumnTlLng;
        private BrightIdeasSoftware.OLVColumn olvColumnTlLat;
        private BrightIdeasSoftware.OLVColumn olvColumnBrLng;
        private BrightIdeasSoftware.OLVColumn olvColumnBrLat;
        private BrightIdeasSoftware.OLVColumn olvColumnStatus;
        private BrightIdeasSoftware.OLVColumn olvColumnIsProb;
        private BrightIdeasSoftware.OLVColumn olvColumnCgID;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem expandAll;
        private System.Windows.Forms.ToolStripMenuItem collapsAll;
        private BrightIdeasSoftware.OLVColumn olvColumnInBlock;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRSRP;
        private System.Windows.Forms.ToolStripMenuItem exportExcel;
        private BrightIdeasSoftware.OLVColumn olvColumnDirection;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn colPrimaryType;
        private BrightIdeasSoftware.OLVColumn colSpecificType;
        private BrightIdeasSoftware.OLVColumn colDetail;
        private BrightIdeasSoftware.OLVColumn colSuggest;

    }
}