<?xml version="1.0"?>
<Configs>
  <Config name="DIYSampleGroups">
    <Item name="groups" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">GSM RxlevSub</Item>
        <Item typeName="String" key="themeName">GSM RxLevSub</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">N_RxLev</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">LAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">GSM 覆盖查询(带小区)</Item>
        <Item typeName="String" key="themeName">GSM RxLevSub</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">RxQualSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">BCCH</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">BSIC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">LAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">GSM强信号弱质量</Item>
        <Item typeName="String" key="themeName">GSM强信号弱质量</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">RxQualSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">GSM载频故障点</Item>
        <Item typeName="String" key="themeName">GSM载频故障点</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">C_I_ARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">C_I_Rxlev</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">TD_PCCPCH_RSCP</Item>
        <Item typeName="String" key="themeName">TD_PCCPCH_RSCP</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_PCCPCH_RSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_SCell_LAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_SCell_CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_SCell_CPI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_SCell_UARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_NCell_PCCPCH_RSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_NCell_UARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_NCell_CPI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_DPCH_RSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">TD TXPOWER</Item>
        <Item typeName="String" key="themeName">TD TXPOWER</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_TxPower</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">TD GSM_Rxlev</Item>
        <Item typeName="String" key="themeName">TD_GSM_Rxlev</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name" />
            <Item typeName="Int32" key="param_arg">-1</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">TD_PCCPCH_C2I</Item>
        <Item typeName="String" key="themeName">TD_PCCPCH_C2I</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_PCCPCH_C2I</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">TD_DPCH_C2I</Item>
        <Item typeName="String" key="themeName">TD_DPCH_C2I</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_DPCH_C2I</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">TD_GSM_Rxqual</Item>
        <Item typeName="String" key="themeName">TD_GSM_Rxqual</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name" />
            <Item typeName="Int32" key="param_arg">-1</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">TD导频污染</Item>
        <Item typeName="String" key="themeName">TD导频污染</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_NCell_PCCPCH_RSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_PCCPCH_RSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name" />
            <Item typeName="Int32" key="param_arg">-1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name" />
            <Item typeName="Int32" key="param_arg">-1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name" />
            <Item typeName="Int32" key="param_arg">-1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name" />
            <Item typeName="Int32" key="param_arg">-1</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">TD_DPCH信息</Item>
        <Item typeName="String" key="themeName">TD DPCHRSCP</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_DPCH_RSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_DPCH_UARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_DPCH_ISCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_DPCH_C2I</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">WCDMA EC/IO Total</Item>
        <Item typeName="String" key="themeName">WCDMA:TotalEc_Io</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_TotalEc_Io</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">WCDMA BLER</Item>
        <Item typeName="String" key="themeName">WCDMA BLER</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_BLER</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">WCDMA RSCP</Item>
        <Item typeName="String" key="themeName">WCDMA RSCP(Total)</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_TotalRSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">WCDMA TxPower</Item>
        <Item typeName="String" key="themeName">WCDMA:TotalRSCP</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_TxPower</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">WCDMA导频污染</Item>
        <Item typeName="String" key="themeName">WCDMA导频污染</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_SNeiRSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_SNeiEcIo</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_TotalEc_Io</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_TotalRSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">WCDMA_RF环境综合评估</Item>
        <Item typeName="String" key="themeName">WCDMA无线环境综合评估</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_TotalEc_Io</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_TotalRSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_TxPower</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">WCDMA_上下行平衡性分析</Item>
        <Item typeName="String" key="themeName">WCDMA上下行平衡性分析</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_TotalEc_Io</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_TotalRSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_TxPower</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">W_BLER</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">CDMA导频污染</Item>
        <Item typeName="String" key="themeName">CDMA导频污染</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CD_NS_Ec_Io</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">CDMA EC/IO(total)</Item>
        <Item typeName="String" key="themeName">CDMA EC/IO(total)</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CD_TotalEcIo</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">CDMA上下行平衡性分析</Item>
        <Item typeName="String" key="themeName">CDMA上下行平衡性分析</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CD_RxAGC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CD_TotalEcIo</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CD_TX_Power</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CD_FFER</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">CDMA无线环境综合评估</Item>
        <Item typeName="String" key="themeName">CDMA无线环境综合评估</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CD_TotalEcIo</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CD_TX_Power</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CD_FFER</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CD_RxAGC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">CDMA软切换比例分析</Item>
        <Item typeName="String" key="themeName">CDMA软切换比例分析</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">CD_NS_SetType</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">GSM扫频_Rxlev</Item>
        <Item typeName="String" key="themeName">GSM_SCAN Rxlev</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">GSCAN_BCCH</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">GSCAN_BSIC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">GSCAN_RxLev</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">GSM扫频重叠覆盖小区数</Item>
        <Item typeName="String" key="themeName">GSM扫频重叠覆盖小区数</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">GSCAN_BSIC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">GSCAN_BCCH</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">GSCAN_RxLev</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">HSDPA</Item>
        <Item key="themeName" />
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">FileName</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">Longitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">Latitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">Time</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TargetLAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TargetCI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TargetRAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_CQI_Max</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_CQI_Mean</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_CQI_Min</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_16QAM_Rate</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_A_DPCH_RSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_DSCH_ACKRate</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_DSCH_BLER</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_DSCH_NACKRate</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_PDSCH_AverageSize</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_PDSCH_CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_PDSCH_RSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_QPSK_Rate</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_SCCH_CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_SCCH_RSCP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_ScchScheduled_Count</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_ScchScheduled_Rate</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_HS_WorkUARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_SCell_UARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_SCell_LAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_SCell_CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_SCell_RAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_TA(chips)</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_APP_AverageSpeed</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_APP_Speed</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_MAC_HSPDU_AverageSize</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_MAC_HSPDU_DiscardRate</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_MAC_HSPDU_RecvRate</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_MACLayer_Rate</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_HSDPA_MAC_HSPDU_RecvTotalErrRate</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_SCell_RNC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_SCell_RSSI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_APP_ThroughputDL</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_APP_TransferedSize</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">TD_APP_TransferedTime</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">LTE感知 RSRP</Item>
        <Item typeName="String" key="themeName">LTE感知 RSRP</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_uep_RSRP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">Latitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">Longitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">Time</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">LTE speed</Item>
        <Item typeName="String" key="themeName">LTE:APP_Speed_kb</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_APP_Speed</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_RSRP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">LTE TDD RSRP</Item>
        <Item typeName="String" key="themeName">LTE_TDD:RSRQ</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_TAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_ECI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_EARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_PCI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_NCell_EARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_NCell_PCI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_TAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_ECI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_EARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_PCI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_NCell_EARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_NCell_PCI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_RSRP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_TAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_ECI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_EARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_PCI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_NCell_EARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_NCell_PCI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_TAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_ECI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_EARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_PCI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_NCell_EARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">lte_NCell_PCI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">LTE_SCAN</Item>
        <Item typeName="String" key="themeName">LTE_SCAN:TopN_CELL_Specific_RSRP</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">LTESCAN_TopN_ECI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">LTESCAN_TopN_PCI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">LTESCAN_TopN_EARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">LTESCAN_TopN_CELL_Specific_RSRP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">NR</Item>
        <Item typeName="String" key="themeName" />
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="param_name">NR_SS_RSRP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>