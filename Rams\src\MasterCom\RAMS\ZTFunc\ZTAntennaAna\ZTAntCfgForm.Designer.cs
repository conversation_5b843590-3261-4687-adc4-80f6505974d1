﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTAntCfgForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.chbCsvFile = new System.Windows.Forms.CheckBox();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.tbPath = new System.Windows.Forms.TextBox();
            this.btnBrowse = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.chbDtNb = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chbSample = new System.Windows.Forms.CheckBox();
            this.chbSecAna = new System.Windows.Forms.CheckBox();
            this.tBCell = new System.Windows.Forms.TextBox();
            this.chbByCellName = new System.Windows.Forms.CheckBox();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // chbCsvFile
            // 
            this.chbCsvFile.AutoSize = true;
            this.chbCsvFile.Location = new System.Drawing.Point(110, 48);
            this.chbCsvFile.Name = "chbCsvFile";
            this.chbCsvFile.Size = new System.Drawing.Size(96, 16);
            this.chbCsvFile.TabIndex = 3;
            this.chbCsvFile.Text = "二维数据导出";
            this.chbCsvFile.UseVisualStyleBackColor = true;
            this.chbCsvFile.CheckedChanged += new System.EventHandler(this.chbCsvFile_CheckedChanged);
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(239, 222);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(58, 23);
            this.btnOK.TabIndex = 6;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Location = new System.Drawing.Point(303, 222);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(58, 23);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(6, 73);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 8;
            this.label1.Text = "文件路径：";
            // 
            // tbPath
            // 
            this.tbPath.Location = new System.Drawing.Point(71, 70);
            this.tbPath.Name = "tbPath";
            this.tbPath.Size = new System.Drawing.Size(214, 21);
            this.tbPath.TabIndex = 9;
            // 
            // btnBrowse
            // 
            this.btnBrowse.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBrowse.Location = new System.Drawing.Point(291, 68);
            this.btnBrowse.Name = "btnBrowse";
            this.btnBrowse.Size = new System.Drawing.Size(58, 23);
            this.btnBrowse.TabIndex = 10;
            this.btnBrowse.Text = "浏览";
            this.btnBrowse.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.chbByCellName);
            this.groupBox1.Controls.Add(this.tBCell);
            this.groupBox1.Controls.Add(this.chbDtNb);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(361, 85);
            this.groupBox1.TabIndex = 11;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "数据源设定";
            // 
            // chbDtNb
            // 
            this.chbDtNb.AutoSize = true;
            this.chbDtNb.Location = new System.Drawing.Point(10, 21);
            this.chbDtNb.Name = "chbDtNb";
            this.chbDtNb.Size = new System.Drawing.Size(144, 16);
            this.chbDtNb.TabIndex = 12;
            this.chbDtNb.Text = "联合邻区数据（路测）";
            this.chbDtNb.UseVisualStyleBackColor = true;
            this.chbDtNb.CheckedChanged += new System.EventHandler(this.chbDtNb_CheckedChanged);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chbSample);
            this.groupBox2.Controls.Add(this.chbSecAna);
            this.groupBox2.Controls.Add(this.chbCsvFile);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.tbPath);
            this.groupBox2.Controls.Add(this.btnBrowse);
            this.groupBox2.Location = new System.Drawing.Point(12, 112);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(361, 104);
            this.groupBox2.TabIndex = 12;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "结果集设定";
            // 
            // chbSample
            // 
            this.chbSample.AutoSize = true;
            this.chbSample.Checked = true;
            this.chbSample.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chbSample.Location = new System.Drawing.Point(8, 24);
            this.chbSample.Name = "chbSample";
            this.chbSample.Size = new System.Drawing.Size(84, 16);
            this.chbSample.TabIndex = 12;
            this.chbSample.Text = "采样点回放";
            this.toolTip.SetToolTip(this.chbSample, "勾选可回放小区采样点，但会有较大内存开销。");
            this.chbSample.UseVisualStyleBackColor = true;
            this.chbSample.CheckedChanged += new System.EventHandler(this.chbSample_CheckedChanged);
            // 
            // chbSecAna
            // 
            this.chbSecAna.AutoSize = true;
            this.chbSecAna.Location = new System.Drawing.Point(8, 48);
            this.chbSecAna.Name = "chbSecAna";
            this.chbSecAna.Size = new System.Drawing.Size(96, 16);
            this.chbSecAna.TabIndex = 11;
            this.chbSecAna.Text = "二维数据分析";
            this.chbSecAna.UseVisualStyleBackColor = true;
            this.chbSecAna.CheckedChanged += new System.EventHandler(this.chbSecAna_CheckedChanged);
            // 
            // tBCell
            // 
            this.tBCell.Location = new System.Drawing.Point(124, 43);
            this.tBCell.Name = "tBCell";
            this.tBCell.Size = new System.Drawing.Size(225, 21);
            this.tBCell.TabIndex = 14;
            // 
            // chbByCellName
            // 
            this.chbByCellName.AutoSize = true;
            this.chbByCellName.Location = new System.Drawing.Point(10, 48);
            this.chbByCellName.Name = "chbByCellName";
            this.chbByCellName.Size = new System.Drawing.Size(108, 16);
            this.chbByCellName.TabIndex = 15;
            this.chbByCellName.Text = "按小区名称查询";
            this.chbByCellName.UseVisualStyleBackColor = true;
            this.chbByCellName.CheckedChanged += new System.EventHandler(this.chbByCellName_CheckedChanged);
            // 
            // ZTAntCfgForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(385, 250);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBox2);
            this.Name = "ZTAntCfgForm";
            this.Text = "查询设定";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.CheckBox chbCsvFile;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox tbPath;
        private System.Windows.Forms.Button btnBrowse;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.CheckBox chbSecAna;
        private System.Windows.Forms.CheckBox chbDtNb;
        private System.Windows.Forms.CheckBox chbSample;
        private System.Windows.Forms.TextBox tBCell;
        private System.Windows.Forms.CheckBox chbByCellName;
    }
}