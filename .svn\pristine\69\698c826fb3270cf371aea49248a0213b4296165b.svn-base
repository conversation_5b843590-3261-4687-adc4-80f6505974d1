﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.CQT
{
    public class ZTDIYIpDownLoadAna : DIYEventQuery
    {
        public ZTDIYIpDownLoadAna(MainModel mainModel)
            :base(mainModel)
        {
            isAddEventToDTDataManager = false;
        }
        public List<DownloadEvtInfo> downloadEvtInfoList { get; set; }
        public Dictionary<string,DownloadInfo> ipDownloadInfoDic { get; set; }
        public List<DownloadEvtPair> downloadEvtPairList { get; set; }
        protected List<int> fileidList = null;
        protected List<FileInfo> fileList = null;
        protected List<string> fileLogList = null;
        List<QueryFileDetailByPeriod.Fileinfo> fileinfoList = null;
        public override string Name
        {
            get { return "TD数据Ip下载速率分析(按区域)"; }
        }

        public override string IconName
        {
            get { throw new NotImplementedException("The method or operation is not implemented."); }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13031, this.Name);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool prepareAskWhatEvent()
        {
            List<int> selectedEventIDs = new List<int>();
            selectedEventIDs.Add(57);//下载开始
            selectedEventIDs.Add(58);//下载成功
            selectedEventIDs.Add(59);//下载失败
            Condition.EventIDs = selectedEventIDs;
            return true;
        }

        protected override bool isValidPoint(double jd, double wd)
        {
            try
            {
                if (MainModel.SearchGeometrys.Region == null) //不选区域按全区查询
                {
                    return true;
                }
                else//选区域按区域查询
                {
                    return Condition.Geometorys == null || Condition.Geometorys.GeoOp.Contains(jd, wd);
                }
            }
            catch
            {
                return false;
            }
        }

        protected override void doWithDTData(Event evt)
        {
            if (evt.ID==57)
            {
                DownloadEvtInfo downloadIEvtInfo = new DownloadEvtInfo();
                downloadIEvtInfo.iTargetCI = (int)evt["TargetCI"];
                downloadIEvtInfo.itime = evt.Time;
                downloadIEvtInfo.time = JavaDate.GetDateTimeFromMilliseconds(1000L * evt.Time);
                string month;
                if (downloadIEvtInfo.time.Month < 10)
                    month = "0" + downloadIEvtInfo.time.Month;
                else
                    month = downloadIEvtInfo.time.Month.ToString();
                string tablename ="tb_log_file_"+ downloadIEvtInfo.time.Year + "_" + month;
                if (!fileLogList.Contains(tablename))
                {
                    fileLogList.Add(tablename);
                }
                downloadIEvtInfo.ifileid = evt.FileID;
                downloadIEvtInfo.ieventid = evt.ID;
                downloadIEvtInfo.downloadQuantity = 0;
                downloadIEvtInfo.downloadUsedTime = 0;

                downloadEvtInfoList.Add(downloadIEvtInfo);
            }
            else if (evt.ID == 58 || evt.ID == 59)
            {
                DownloadEvtInfo downloadEvtInfo = new DownloadEvtInfo();
                downloadEvtInfo.iTargetCI = (int)evt["TargetCI"];
                downloadEvtInfo.itime = evt.Time;
                downloadEvtInfo.time = JavaDate.GetDateTimeFromMilliseconds(1000L * evt.Time);
                downloadEvtInfo.ifileid = evt.FileID;
                downloadEvtInfo.ieventid = evt.ID;
                downloadEvtInfo.downloadQuantity = int.Parse(evt["Value1"].ToString());
                downloadEvtInfo.downloadUsedTime = int.Parse(evt["Value4"].ToString());

                downloadEvtInfoList.Add(downloadEvtInfo);
            }
        }

        protected override void query()
        {
            downloadEvtInfoList = new List<DownloadEvtInfo>();
            ipDownloadInfoDic = new Dictionary<string, DownloadInfo>();
            downloadEvtPairList = new List<DownloadEvtPair>();
            fileidList = new List<int>();
            fileList = new List<FileInfo>();
            fileLogList = new List<string>();
            if (!prepareAskWhatEvent())
            {
                return;
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = null;
            try
            {
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.IsDrawEventResult = false;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                MainModel.SelectedMessage = null;

                clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                clientProxy.Close();

                showResult();
                MainModel.FireDTDataChanged(this);
            }
            finally
            {
                //continue
            }
        }

        protected override void queryInThread(object o) 
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                if(condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = 20;
                        //img grid
                        queryPeriodInfo(clientProxy, package, period,false);
                    }
                }

                AnalyseIp();
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected void showResult()
        {
            WaitBox.Text = "准备显示查询结果…";
            WaitBox.ProgressPercent = 99;
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(IpDownloadForm).FullName);
            IpDownloadForm form = obj == null ? null : obj as IpDownloadForm;
            if (form == null || form.IsDisposed)
            {
                form = new IpDownloadForm(MainModel);
                form.Owner = MainModel.MainForm;
            }
            form.FillData(ipDownloadInfoDic);
            form.Visible = true;
            form.BringToFront();
        }

        /// <summary>
        /// 统计Ip的下载情况
        /// </summary>
        private void AnalyseIp()
        {
            WaitBox.Text = "正在统计Ip的下载情况…";
            WaitBox.ProgressPercent = 50;
            DownloadEvtPair pair = null ;
            foreach (DownloadEvtInfo evtInfo in downloadEvtInfoList)
            {
                if (evtInfo.ieventid == 57)
                {
                    pair = new DownloadEvtPair();
                    pair.stime = evtInfo.itime;
                    pair.stimedt = evtInfo.time;
                }
                else if (evtInfo.ieventid == 58 || evtInfo.ieventid == 59)
                {
                    if (pair != null)
                    {
                        pair.iTargetCI = evtInfo.iTargetCI;
                        pair.endEventId = evtInfo.ieventid;
                        pair.etime = evtInfo.itime;
                        pair.etimedt = evtInfo.time;
                        pair.ifileid = evtInfo.ifileid;
                        pair.downloadQuantity = evtInfo.downloadQuantity;
                        pair.downloadUsedTime = evtInfo.downloadUsedTime;

                        downloadEvtPairList.Add(pair);
                    }
                    pair = null;
                }
                if (!fileidList.Contains(evtInfo.ifileid))
                {
                    fileidList.Add(evtInfo.ifileid);
                }
            }

            QueryFiles();

            CalCulateDownLoad500kPct();

            CombinEvtPairs();
        }

        /// <summary>
        /// 查询时间范围内的所有文件
        /// </summary>
        private void QueryFiles()
        {
            fileinfoList = new List<QueryFileDetailByPeriod.Fileinfo>();

            foreach (string log in fileLogList)
            {
                QueryFileDetailByPeriod queryfiles = new QueryFileDetailByPeriod(MainModel, log);
                queryfiles.Query();
                fileinfoList.AddRange(queryfiles.fileinfoList);
            }
        }

        /// <summary>
        /// 统计下载事件所在文件中的采样点，计算500k速率的占比
        /// </summary>
        private void CalCulateDownLoad500kPct()
        {
            WaitBox.Text = "正在计算下载速率…";
            WaitBox.ProgressPercent = 70;

            List<TestPoint> tpList = new List<TestPoint>();
            foreach (int fileid in fileidList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }

                replayFile(tpList, fileid);
            }

            foreach (TestPoint tp in tpList)
            {
                foreach (DownloadEvtPair pair in downloadEvtPairList)
                {
                    getValidData(tp, pair);
                }
            }
        }

        private static void getValidData(TestPoint tp, DownloadEvtPair pair)
        {
            if (tp.FileID == pair.ifileid && pair.isWithin(tp.Time))
            {
                int? speed = (int?)tp["TD_APP_ThroughputDL"];
                if (speed != null && speed >= 0)
                {
                    if (speed / 1000 > 500)
                    {
                        pair.dl++;
                    }
                    pair.dlValidTimes++;
                }
            }
        }

        private void replayFile(List<TestPoint> tpList, int fileid)
        {
            foreach (QueryFileDetailByPeriod.Fileinfo fi in fileinfoList)
            {
                if (fileid == fi.id) //匹配时间范围内的文件id与下载事件所在的文件id，找出需要回放的文件
                {
                    FileInfo fileInfo = new FileInfo();
                    fileInfo.ID = fi.id;
                    fileInfo.DistrictID = fi.districtId;
                    fileInfo.ProjectID = fi.projId;
                    fileInfo.ServiceType = fi.servId;
                    fileInfo.SampleTbName = fi.sampletbname;
                    fileInfo.LogTable = fi.logtbname;

                    QueryCondition condition = new QueryCondition();
                    condition.FileInfos.Add(fileInfo);
                    try
                    {
                        ReplayFile query = new ReplayFile(MainModel);
                        query.SetQueryCondition(condition);
                        query.Query();

                        tpList.AddRange(query.testPointList);
                    }
                    catch
                    {
                        //continue
                    }
                }
            }
        }

        /// <summary>
        /// 合并同ip的事件对
        /// </summary>
        private void CombinEvtPairs()
        {
            WaitBox.ProgressPercent = 90;

            foreach (DownloadEvtPair pair in downloadEvtPairList)
            {
                if (ipDownloadInfoDic.ContainsKey(pair.IpWithTime_dateHourStr))
                {
                    DownloadInfo dInfo = ipDownloadInfoDic[pair.IpWithTime_dateHourStr];
                    dInfo.DateHourStr = pair.dateHourStr;
                    dInfo.downloadQuantity += pair.downloadQuantity;
                    dInfo.usedTime += pair.downloadUsedTime;
                    dInfo.dl += pair.dl;
                    dInfo.DlValidTimes += pair.dlValidTimes;
                    dInfo.DownloadTimes++;
                
                }
                else
                {
                    DownloadInfo dInfo = new DownloadInfo();
                    dInfo.Ip = pair.Ip;
                    dInfo.DateHourStr = pair.dateHourStr;
                    dInfo.downloadQuantity += pair.downloadQuantity;
                    dInfo.usedTime += pair.downloadUsedTime;
                    dInfo.dl += pair.dl;
                    dInfo.DlValidTimes += pair.dlValidTimes;
                    dInfo.DownloadTimes = 1;

                    ipDownloadInfoDic.Add(pair.IpWithTime_dateHourStr, dInfo);
                }
            }

            MainModel.IpDownloadInfoDic = ipDownloadInfoDic;
        }

        public class ReplayFile : DIYReplayFileWithNoWaitBox
        {
            public List<TestPoint> testPointList { get; set; }

            public ReplayFile(MainModel mainModel)
                : base(mainModel)
            {
                IncludeEvent = false;
                IsAddSampleToDTDataManager = false;
            }

            // TD_APP_ThroughputDL
            protected override DIYReplayContentOption getDIYReplayContent()
            {
                DIYReplayContentOption option = new DIYReplayContentOption();
                if (IncludeTestPoint)
                {
                    List<ColumnDefItem> columns = null;
                    columns = InterfaceManager.GetInstance().GetColumnDefByShowName("isampleid");
                    if (columns != null && columns.Count > 0)
                    {
                        option.SampleColumns.AddRange(columns);
                    }
                    columns = InterfaceManager.GetInstance().GetColumnDefByShowName("itime");
                    if (columns != null && columns.Count > 0)
                    {
                        option.SampleColumns.AddRange(columns);
                    }
                    columns = InterfaceManager.GetInstance().GetColumnDefByShowName("TD_APP_ThroughputDL");
                    if (columns != null && columns.Count > 0)
                    {
                        option.SampleColumns.AddRange(columns);
                    }
                }
                option.EventInclude = true;
                if (!IncludeEvent)
                {
                    option.EventInclude = false;
                }
                if (IncludeMessage)
                {
                    option.MessageInclude = true;
                    option.MessageL3HexCode = true;
                }
                return option;
            }

            protected override void query()
            {
                testPointList = new List<TestPoint>();
                base.query();
            }

            protected override void doWithDTData(TestPoint tp)
            {
                testPointList.Add(tp);
            }
        }

    }
    
    public class ZTDIYIpDownLoadAnaByFile : ZTDIYIpDownLoadAna
    {
        List<int> selFileIdList = null;
        public ZTDIYIpDownLoadAnaByFile(MainModel mainModel)
            :base(mainModel)
        {
            isAddEventToDTDataManager = false;
        }

        public override string Name
        {
            get { return "TD数据Ip下载速率分析(按文件)"; }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13044, this.Name);
        }

        protected override bool isValidPoint(double jd, double wd)
        {
            return true;
        }

        protected override void doWithDTData(Event evt)
        {
            if (selFileIdList.Contains(evt.FileID))
            {
                if (evt.ID == 57)
                {
                    DownloadEvtInfo downloadIEvtInfo = new DownloadEvtInfo();
                    downloadIEvtInfo.iTargetCI = (int)evt["TargetCI"];
                    downloadIEvtInfo.itime = evt.Time;
                    downloadIEvtInfo.time = JavaDate.GetDateTimeFromMilliseconds(1000L * evt.Time);
                    string month;
                    if (downloadIEvtInfo.time.Month < 10)
                        month = "0" + downloadIEvtInfo.time.Month;
                    else
                        month = downloadIEvtInfo.time.Month.ToString();
                    string tablename = "tb_log_file_" + downloadIEvtInfo.time.Year + "_" + month;
                    if (!fileLogList.Contains(tablename))
                    {
                        fileLogList.Add(tablename);
                    }
                    downloadIEvtInfo.ifileid = evt.FileID;
                    downloadIEvtInfo.ieventid = evt.ID;
                    downloadIEvtInfo.downloadQuantity = 0;
                    downloadIEvtInfo.downloadUsedTime = 0;

                    downloadEvtInfoList.Add(downloadIEvtInfo);
                }
                else if (evt.ID == 58 || evt.ID == 59)
                {
                    DownloadEvtInfo downloadEvtInfo = new DownloadEvtInfo();
                    downloadEvtInfo.iTargetCI = (int)evt["TargetCI"];
                    downloadEvtInfo.itime = evt.Time;
                    downloadEvtInfo.time = JavaDate.GetDateTimeFromMilliseconds(1000L * evt.Time);
                    downloadEvtInfo.ifileid = evt.FileID;
                    downloadEvtInfo.ieventid = evt.ID;
                    downloadEvtInfo.downloadQuantity = int.Parse(evt["Value1"].ToString());
                    downloadEvtInfo.downloadUsedTime = int.Parse(evt["Value4"].ToString());

                    downloadEvtInfoList.Add(downloadEvtInfo);
                }
            }
        }

        protected override void query()
        {
            downloadEvtInfoList = new List<DownloadEvtInfo>();
            ipDownloadInfoDic = new Dictionary<string, DownloadInfo>();
            downloadEvtPairList = new List<DownloadEvtPair>();
            fileidList = new List<int>();
            fileList = new List<FileInfo>();
            fileLogList = new List<string>();
            if (!prepareAskWhatEvent())
            {
                return;
            }

            selFileIdList = new List<int>();
            foreach (FileInfo file in condition.FileInfos)
            {
                selFileIdList.Add(file.ID);
            }
            if (selFileIdList.Count==0)
            {
                XtraMessageBox.Show("没选择需要查询的文件，请检查！");
                return;
            }

            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = null;
            try
            {
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.IsDrawEventResult = false;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                MainModel.SelectedMessage = null;

                clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                clientProxy.Close();

                showResult();
            }
            finally
            {
                //continue
            }
        }
    }

    public class QueryFileDetailByPeriod : DIYSQLBase
    {
        public class Fileinfo
        {
            public int id{ get; set; }
            public int districtId{ get; set; }
            public int projId{ get; set; }
            public int servId{ get; set; }
            public string sampletbname{ get; set; }
            public string logtbname{ get; set; }
        }

        string filetable { get; set; }
        public QueryFileDetailByPeriod(MainModel mainModel, string fileTable)
            : base(mainModel)
        {
            filetable = fileTable;
        }

        public List<Fileinfo> fileinfoList { get; set; } = new List<Fileinfo>();
        protected override string getSqlTextString()
        {
            return "select ifileid,iprojecttype,iservicetype,strsampletbname,logtbname from " + filetable;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            return rType;
        }

        public override string Name
        {
            get { return "文件信息"; }
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                fileinfoList = new List<Fileinfo>();
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }

        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    Fileinfo fi = new Fileinfo();
                    fi.districtId = clientProxy.DbID;
                    fi.id = package.Content.GetParamInt();
                    fi.projId = package.Content.GetParamInt();
                    fi.servId = package.Content.GetParamInt();
                    fi.sampletbname = package.Content.GetParamString();
                    fi.logtbname = package.Content.GetParamString();

                    fileinfoList.Add(fi);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DownloadEvtInfo
    {
        public string Ip
        {
            get
            {
                return
                ((iTargetCI & 0xFF000000) / Math.Pow(16, 6)).ToString() + '.' + ((iTargetCI & 0x00FF0000) / Math.Pow(16, 4)).ToString() + '.'
                + ((iTargetCI & 0x0000FF00) / Math.Pow(16, 2)).ToString() + '.' + (iTargetCI & 0x000000FF).ToString();
            }
        }
        public int iTargetCI{ get; set; }
        public int itime{ get; set; }
        public DateTime time{ get; set; }
        public int ifileid{ get; set; }
        public int ieventid{ get; set; }
        public int downloadQuantity{ get; set; }
        public int downloadUsedTime{ get; set; }

        public static IComparer<DownloadEvtInfo> GetCompareByTime()
        {
            if (comparerByTime == null)
            {
                comparerByTime = new ComparerByBeginTime();
            }
            return comparerByTime;
        }

        private static IComparer<DownloadEvtInfo> comparerByTime;

        public class ComparerByBeginTime : IComparer<DownloadEvtInfo>
        {
            public int Compare(DownloadEvtInfo x, DownloadEvtInfo y)
            {
                return x.itime.CompareTo(y.itime);
            }
        }
    }

    public class DownloadEvtPair
    {
        public string Ip
        {
            get
            {
                return
                ((iTargetCI & 0xFF000000) / Math.Pow(16, 6)).ToString() + '.' + ((iTargetCI & 0x00FF0000) / Math.Pow(16, 4)).ToString() + '.'
                + ((iTargetCI & 0x0000FF00) / Math.Pow(16, 2)).ToString() + '.' + (iTargetCI & 0x000000FF).ToString();
            }
        }

        public string dateHourStr
        {
            get { return stimedt.Date.ToShortDateString() +" "+ (stimedt.Hour).ToString() + "时~" + (stimedt.Hour+1).ToString() + "时"; }
        }

        public int iTargetCI{ get; set; }
        public int stime{ get; set; }
        public int etime{ get; set; }
        public string IpWithTime_dateHourStr
        {
            get { return this.Ip + "_" + stimedt.Date.ToShortDateString() + "_" + stimedt.Hour; }
        }

        public DateTime stimedt{ get; set; }
        public DateTime etimedt{ get; set; }
        public int ifileid{ get; set; }
        public int beginEventId { get; set; } = 57;
        public int endEventId{ get; set; }
        public int downloadQuantity{ get; set; }
        public int downloadUsedTime{ get; set; }

        public int dl { get; set; } = 0;
        public int dlValidTimes { get; set; } = 0;

        public bool isWithin(int time)
        {
            if (stime < time && etime > time)
            {
                return true;
            }
            else return false;
        }

        public static IComparer<DownloadEvtPair> GetCompareByTime()
        {
            if (comparerByTime == null)
            {
                comparerByTime = new ComparerByBeginTime();
            }
            return comparerByTime;
        }

        private static IComparer<DownloadEvtPair> comparerByTime;

        public class ComparerByBeginTime : IComparer<DownloadEvtPair>
        {
            public int Compare(DownloadEvtPair x, DownloadEvtPair y)
            {
                return x.stime.CompareTo(y.stime);
            }
        }
    }

    public class DownloadInfo
    {
        public string Ip { get; set; }
        /// <summary>
        /// 时间段
        /// </summary>
        public string DateHourStr { get; set; }
        public double downloadQuantity { get; set; }//此ip的下载总数据量,单位为kb
        public string DownloadSpeed
        {
            get
            {
                if (DownloadTimes > 0)
                {
                    return Math.Round((downloadQuantity * 8 / 1024) / (usedTime / 1000), 2).ToString() ;
                }
                else return "-";
            }
        }
        /// <summary>
        /// 此ip的下载次数
        /// </summary>
        public int DownloadTimes { get; set; }

        public int usedTime { get; set; }
        /// <summary>
        /// 此ip的下载总时长
        /// </summary>
        public string UsedTime
        {
            get { return ((double)usedTime / 1000).ToString(); }
        }

        /// <summary>
        /// 500k以上速率占比
        /// </summary>
        public string Above500KPct 
        {
            get
            {
                if (DlValidTimes > 0)
                {
                    return Math.Round(100 * 1.0 * dl / DlValidTimes, 2) + "%";
                }
                else return "-";
            }
        }
        public int dl { get; set; } = 0;
        public int DlValidTimes { get; set; }

        public List<Period> periods { get; set; } = new List<Period>();

        public class Period
        {
            public int stime { get; set; }
            public int etime { get; set; }

            public Period(int stime, int etime)
            {
                this.stime = stime;
                this.etime = etime;
            }

            public bool isWithin(int itime)
            {
                if (itime > stime && itime < etime)
                {
                    return true;
                }
                else
                    return false;
            }
        }
    }
}
