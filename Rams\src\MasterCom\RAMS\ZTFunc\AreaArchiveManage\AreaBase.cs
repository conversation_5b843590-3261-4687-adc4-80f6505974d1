﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class AreaBase : IComparable<AreaBase>
    {
        public override string ToString()
        {
            return Name;
        }

        public string FullName
        {
            get
            {
                string fn = Name;
                AreaBase parent = this.ParentArea;
                if (parent != null)
                {
                    fn = parent.FullName + "." + fn;
                }
                return fn;
            }
        }

        public int DistrictID
        {
            get;
            set;
        }

        public AreaBase(AreaRank rank,string name)
        {
            this.Rank = rank;
            this.Name = name;
            AreaID = -1;
        }

        public string SN
        {
            get;
            set;
        }

        /// <summary>
        /// 对应arealist的iareatypeid
        /// </summary>
        public int AreaTypeID
        {
            get;
            set;
        }

        /// <summary>
        /// 对应arealist的iareaid
        /// </summary>
        public int AreaID
        {
            get;
            set;
        }

        /// <summary>
        /// 对应tb_cfg_zt_area_list表的iID，对于每个Area唯一
        /// </summary>
        public int ID
        {
            get;
            set;
        }

        public string Name
        {
            get;
            set;
        }

        public string StrComment
        {
            get;
            set;
        }

        public AreaBase ParentArea
        {
            get;
            set;
        }

        public string PareantAreaName
        {
            get
            {
                if (ParentArea != null)
                {
                    return ParentArea.Name;
                }
                return string.Empty;
            }
        }

        public AreaBase GetUpperSectionArea(AreaRank rank)
        {
            if (this.Rank==rank)
            {
                return this;
            }
            else
            {
                AreaBase upperArea = ParentArea;
                while (upperArea!=null)
                {
                    if (upperArea.Rank==rank)
                    {
                        break;
                    }
                    upperArea = upperArea.ParentArea;
                }
                return upperArea;
            }
        }

        public AreaBase GetLowerSectionArea(AreaRank rank)
        {
            if (this.Rank == rank)
            {
                return this;
            }
            else
            {
                if (this.SubAreas == null)
                {
                    return null;
                }
                AreaBase rtArea = null;
                foreach (AreaBase sub in this.SubAreas)
                {
                    rtArea = sub.GetLowerSectionArea(rank);

                    if (rtArea != null)
                    {
                        break;
                    }
                }
                return rtArea;
            }
        }

        public AreaBase GetSectionArea(AreaRank rank)
        {
            AreaBase rtArea = GetUpperSectionArea(rank);
            if (rtArea == null)
                rtArea = GetLowerSectionArea(rank);
            return rtArea;
        }

        public double MinLng
        {
            get {
                return Bounds.x1;
            }
        }

        public double MaxLng
        {
            get
            {
                return Bounds.x2;
            }
        }

        public double MaxLat
        {
            get
            {
                return Bounds.y2;
            }
        }

        public double MinLat
        {
            get
            {
                return Bounds.y1;
            }
        }

        public List<AreaBase> SubAreas
        {
            get;
            set;
        }

        public AreaRank Rank { get; set; }

        private MapWinGIS.Shape shape = null;
        public MapWinGIS.Shape Shape
        {
            get { return shape; }
            set
            {
                shape = value;
                squareKM = Math.Round(RegionAreaCalculator.CalculateArea(value), 2);
                PolygonPoints = new List<DbPoint>();
                double maxX = double.MinValue;
                double maxY = double.MinValue;
                double minX = double.MaxValue;
                double minY = double.MaxValue;
                for (int i = 0; i < shape.numPoints; i++)
                {
                    MapWinGIS.Point pt = shape.get_Point(i);
                    DbPoint dPt = new DbPoint(pt.x, pt.y);
                    PolygonPoints.Add(dPt);
                    maxX = Math.Max(maxX, pt.x);
                    maxY = Math.Max(maxY, pt.y);
                    minY = Math.Min(minY, pt.y);
                    minX = Math.Min(minX, pt.x);
                }
                Bounds = new DbRect(minX, minY, maxX, maxY);
            }
        }

        DbPoint centroid = null;
        public DbPoint Centroid
        {
            get
            {
                if (shape != null && centroid == null)
                {
                    centroid = new DbPoint(Math.Round(shape.Centroid.x, 6)
                        , Math.Round(shape.Centroid.y, 6));
                }
                return centroid;
            }
        }

        private MapOperation2 mapOper = null;
        public MapOperation2 MapOper
        {
            get
            {
                if (mapOper == null && shape != null)
                {
                    mapOper = new MapOperation2();
                    mapOper.FillPolygon(shape);
                }
                return mapOper;
            }
        }

        public List<DbPoint> PolygonPoints
        {
            get;
            private set;
        }

        private double squareKM = double.NaN;
        public double SquareKM
        {
            get
            {
                if (double.IsNaN(squareKM))
                {
                    squareKM = 0;
                    foreach (AreaBase area in GetLeafs())
                    {
                        squareKM += area.squareKM;
                    }
                    squareKM = Math.Round(squareKM, 2);
                }
                return squareKM;
            }
            private set { squareKM = value; }
        }

        private DbRect bounds = null;
        public DbRect Bounds
        {
            get
            {
                if (bounds == null)
                {
                    double maxX = double.MinValue;
                    double maxY = double.MinValue;
                    double minX = double.MaxValue;
                    double minY = double.MaxValue;
                    if (SubAreas != null)
                    {
                        foreach (AreaBase area in SubAreas)
                        {
                            maxX = Math.Max(maxX, area.Bounds.x2);
                            maxY = Math.Max(maxY, area.Bounds.y2);
                            minY = Math.Min(minY, area.Bounds.y1);
                            minX = Math.Min(minX, area.Bounds.x1);
                        }
                    }
                    bounds = new DbRect(minX, minY, maxX, maxY);
                }
                return bounds;
            }
            set
            {
                bounds = value;
            }
        }

        public double XYRate
        {
            get
            {
                double x = Bounds.Width();
                double y = bounds.Height();
                return Math.Round(x / y, 2);
            }
        }

        public string RankName
        {
            get
            {
                if (Rank != null)
                {
                    return Rank.Name;
                }
                return null;
            }
        }

        #region IComparable<AreaBase> 成员

        public int CompareTo(AreaBase other)
        {
            return this.Rank.CompareTo(other.Rank);
        }

        #endregion

        internal void AddArea(AreaBase area)
        {
            if (SubAreas==null)
            {
                SubAreas = new List<AreaBase>();
            }
            area.ParentArea = this;
            SubAreas.Add(area);
        }

        public List<AreaBase> GetLeafs()
        {
            List<AreaBase> leafs = new List<AreaBase>();
            if (SubAreas == null)
            {
                leafs.Add(this);
                return leafs;
            }
            else
            {
                foreach (AreaBase sub in SubAreas)
                {
                    leafs.AddRange(sub.GetLeafs());
                }
            }
            return leafs;
        }

        public string[] GetAreaPath()
        {
            string[] strArry = new string[Rank.ID + 1];
            AreaBase pArea = this;
            while (pArea != null)
            {
                strArry[pArea.Rank.ID] = pArea.Name;
                pArea = pArea.ParentArea;
            }
            return strArry;
        }

        public override bool Equals(object obj)
        {
            if (obj.GetType() != this.GetType())
            {
                return false;
            }
            AreaBase area = obj as AreaBase;
            if (area == null)
            {
                return false;
            }

            return this.GetHashCode().Equals(area.GetHashCode());
        }

        public override int GetHashCode()
        {
            return FullName.GetHashCode();
        }

        internal double Distance2(AreaBase otherArea)
        {
            double dis = double.NaN;
            if (this.shape != null && otherArea.shape != null)
            {
                double x = this.shape.Centroid.x;
                double y = this.shape.Centroid.y;
                double otherX = otherArea.shape.Centroid.x;
                double otherY = otherArea.shape.Centroid.y;
                dis = MasterCom.Util.MathFuncs.GetDistance(x, y, otherX, otherY);
            }
            return dis;
        }
    }

    public class AreaShape
    {
        public string SN
        {
            get;
            private set;
        }
        public MapWinGIS.Shape Shape
        {
            get;
            private set;
        }
        public AreaShape(string sn, MapWinGIS.Shape shape)
        {
            this.SN = sn;
            this.Shape = shape;
        }

        public static AreaShape Create(string sn, string serailizedStr)
        {
            AreaShape area = null;
            MapWinGIS.Shape temp = new MapWinGIS.Shape();
            if (temp.CreateFromString(serailizedStr))
            {
                area = new AreaShape(sn, temp);
            }
            return area;
        }

    }

    public class AreaRank : IComparable<AreaRank>
    {
        public override string ToString()
        {
            return Name;
        }
        public AreaRank(int id, string name, int areaTypeID, AreaRank parent)
        {
            this.ID = id;
            this.Name = name;
            this.AreaTypeID = areaTypeID;
            this.ParentRank = parent;
        }
        public int AreaTypeID
        {
            get;
            set;
        }
        public int ID { get; set; }
        public string Name { get; set; }
        public int Rank { get; set; }
        public AreaRank ParentRank
        {
            get;
            private set;
        }
        public bool HasChildren { get; set; } = true;

        #region IComparable<AreaRank> 成员

        public int CompareTo(AreaRank other)
        {
            return this.Rank.CompareTo(other.Rank);
        }

        public bool BelongTo(AreaBase area, out AreaBase areaBoss)
        {
            areaBoss = null;
            AreaBase areabase = area;
            do 
            {
                if (areabase.Rank == this)
                {
                    areaBoss = areabase;
                    return true;
                }
                areabase = areabase.ParentArea;
            } while (areabase != null);
            return false;
        }

        #endregion
    }

}



