﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class SatisfactionAnalysisReportForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxQuestionType = new System.Windows.Forms.ComboBox();
            this.cbxCity = new System.Windows.Forms.ComboBox();
            this.cbxMonth = new System.Windows.Forms.ComboBox();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.btnQuery = new System.Windows.Forms.Button();
            this.btnClear = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.lstCondition = new System.Windows.Forms.ListBox();
            this.btnRemove = new System.Windows.Forms.Button();
            this.btnAdd = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxConnection = new System.Windows.Forms.ComboBox();
            this.cbxParamValue = new System.Windows.Forms.ComboBox();
            this.cbxOperator = new System.Windows.Forms.ComboBox();
            this.cbxParam = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.gvCellScore = new System.Windows.Forms.DataGridView();
            this.gvcCellName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcLac = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcCI = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcCellNetwork = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcGSMScore = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcCellTDScore = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cmnuCell = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tmenuGSMCellKPI = new System.Windows.Forms.ToolStripMenuItem();
            this.tmenuTDCellKPI = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.gvGridScore = new System.Windows.Forms.DataGridView();
            this.gvcGridName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcGridSchene = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcCCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcGridGSMScore = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcGridTDScore = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cmnuGrid = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tmenuGSMGridKPI = new System.Windows.Forms.ToolStripMenuItem();
            this.tmenuTDGridKPI = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.gvResult = new System.Windows.Forms.DataGridView();
            this.gvcUserPhone = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcUserSex = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcTerminalType = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcNetworkConvert = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcNetworkQuality = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcInternet = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcVoiceCall = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cmnuResult = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.渲染该用户轨迹ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.渲染全部用户ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn9 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn10 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn11 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn12 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn13 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn14 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn15 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn16 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn17 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn18 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvCellScore)).BeginInit();
            this.cmnuCell.SuspendLayout();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvGridScore)).BeginInit();
            this.cmnuGrid.SuspendLayout();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvResult)).BeginInit();
            this.cmnuResult.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbxQuestionType);
            this.groupBox1.Controls.Add(this.cbxCity);
            this.groupBox1.Controls.Add(this.cbxMonth);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.btnQuery);
            this.groupBox1.Controls.Add(this.btnClear);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.lstCondition);
            this.groupBox1.Controls.Add(this.btnRemove);
            this.groupBox1.Controls.Add(this.btnAdd);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.cbxConnection);
            this.groupBox1.Controls.Add(this.cbxParamValue);
            this.groupBox1.Controls.Add(this.cbxOperator);
            this.groupBox1.Controls.Add(this.cbxParam);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1144, 157);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "分析参数";
            // 
            // cbxQuestionType
            // 
            this.cbxQuestionType.FormattingEnabled = true;
            this.cbxQuestionType.Location = new System.Drawing.Point(815, 83);
            this.cbxQuestionType.Name = "cbxQuestionType";
            this.cbxQuestionType.Size = new System.Drawing.Size(229, 22);
            this.cbxQuestionType.TabIndex = 10;
            // 
            // cbxCity
            // 
            this.cbxCity.FormattingEnabled = true;
            this.cbxCity.Location = new System.Drawing.Point(815, 42);
            this.cbxCity.Name = "cbxCity";
            this.cbxCity.Size = new System.Drawing.Size(95, 22);
            this.cbxCity.TabIndex = 10;
            // 
            // cbxMonth
            // 
            this.cbxMonth.FormattingEnabled = true;
            this.cbxMonth.Location = new System.Drawing.Point(948, 38);
            this.cbxMonth.Name = "cbxMonth";
            this.cbxMonth.Size = new System.Drawing.Size(96, 22);
            this.cbxMonth.TabIndex = 10;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(751, 87);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(67, 14);
            this.label6.TabIndex = 9;
            this.label6.Text = "问卷类型：";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(779, 48);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(43, 14);
            this.label7.TabIndex = 8;
            this.label7.Text = "地市：";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(911, 43);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(43, 14);
            this.label5.TabIndex = 8;
            this.label5.Text = "月份：";
            // 
            // btnQuery
            // 
            this.btnQuery.Location = new System.Drawing.Point(897, 117);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(87, 27);
            this.btnQuery.TabIndex = 7;
            this.btnQuery.Text = "查询";
            this.btnQuery.UseVisualStyleBackColor = true;
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // btnClear
            // 
            this.btnClear.Location = new System.Drawing.Point(309, 114);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(87, 27);
            this.btnClear.TabIndex = 7;
            this.btnClear.Text = "清空";
            this.btnClear.UseVisualStyleBackColor = true;
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(420, 15);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(91, 14);
            this.label4.TabIndex = 6;
            this.label4.Text = "统计条件集合：";
            // 
            // lstCondition
            // 
            this.lstCondition.FormattingEnabled = true;
            this.lstCondition.ItemHeight = 14;
            this.lstCondition.Items.AddRange(new object[] {
            "AND 网络质量 <= 8"});
            this.lstCondition.Location = new System.Drawing.Point(416, 33);
            this.lstCondition.Name = "lstCondition";
            this.lstCondition.SelectionMode = System.Windows.Forms.SelectionMode.MultiExtended;
            this.lstCondition.Size = new System.Drawing.Size(327, 116);
            this.lstCondition.TabIndex = 5;
            // 
            // btnRemove
            // 
            this.btnRemove.Location = new System.Drawing.Point(309, 73);
            this.btnRemove.Name = "btnRemove";
            this.btnRemove.Size = new System.Drawing.Size(87, 27);
            this.btnRemove.TabIndex = 4;
            this.btnRemove.Text = "《";
            this.btnRemove.UseVisualStyleBackColor = true;
            this.btnRemove.Click += new System.EventHandler(this.btnRemove_Click);
            // 
            // btnAdd
            // 
            this.btnAdd.Location = new System.Drawing.Point(309, 33);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Size = new System.Drawing.Size(87, 27);
            this.btnAdd.TabIndex = 4;
            this.btnAdd.Text = "》";
            this.btnAdd.UseVisualStyleBackColor = true;
            this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(14, 125);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(55, 14);
            this.label3.TabIndex = 3;
            this.label3.Text = "连接符：";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(14, 96);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(55, 14);
            this.label8.TabIndex = 2;
            this.label8.Text = "指标值：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(14, 66);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(55, 14);
            this.label2.TabIndex = 2;
            this.label2.Text = "操作符：";
            // 
            // cbxConnection
            // 
            this.cbxConnection.FormattingEnabled = true;
            this.cbxConnection.Items.AddRange(new object[] {
            "AND",
            "OR"});
            this.cbxConnection.Location = new System.Drawing.Point(83, 120);
            this.cbxConnection.Name = "cbxConnection";
            this.cbxConnection.Size = new System.Drawing.Size(201, 22);
            this.cbxConnection.TabIndex = 1;
            this.cbxConnection.Text = "AND";
            // 
            // cbxParamValue
            // 
            this.cbxParamValue.FormattingEnabled = true;
            this.cbxParamValue.Items.AddRange(new object[] {
            "10",
            "9",
            "8",
            "7",
            "6",
            "5",
            "4",
            "3",
            "2",
            "1",
            "0"});
            this.cbxParamValue.Location = new System.Drawing.Point(83, 91);
            this.cbxParamValue.Name = "cbxParamValue";
            this.cbxParamValue.Size = new System.Drawing.Size(201, 22);
            this.cbxParamValue.TabIndex = 1;
            this.cbxParamValue.Text = "8";
            // 
            // cbxOperator
            // 
            this.cbxOperator.FormattingEnabled = true;
            this.cbxOperator.Items.AddRange(new object[] {
            "=",
            ">",
            "<",
            ">=",
            "<="});
            this.cbxOperator.Location = new System.Drawing.Point(83, 62);
            this.cbxOperator.Name = "cbxOperator";
            this.cbxOperator.Size = new System.Drawing.Size(201, 22);
            this.cbxOperator.TabIndex = 1;
            this.cbxOperator.Text = "<=";
            // 
            // cbxParam
            // 
            this.cbxParam.FormattingEnabled = true;
            this.cbxParam.Items.AddRange(new object[] {
            "网络覆盖",
            "网络质量",
            "手机上网",
            "语音通话"});
            this.cbxParam.Location = new System.Drawing.Point(83, 33);
            this.cbxParam.Name = "cbxParam";
            this.cbxParam.Size = new System.Drawing.Size(201, 22);
            this.cbxParam.TabIndex = 1;
            this.cbxParam.Text = "网络质量";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(14, 37);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(55, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "指标项：";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.groupBox3);
            this.groupBox2.Controls.Add(this.groupBox4);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.groupBox2.Location = new System.Drawing.Point(0, 472);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(1144, 205);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "占用小区网格";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.gvCellScore);
            this.groupBox3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox3.Location = new System.Drawing.Point(3, 18);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(597, 184);
            this.groupBox3.TabIndex = 2;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "占用小区";
            // 
            // gvCellScore
            // 
            this.gvCellScore.AllowUserToAddRows = false;
            this.gvCellScore.AllowUserToDeleteRows = false;
            this.gvCellScore.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.gvCellScore.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.gvcCellName,
            this.gvcLac,
            this.gvcCI,
            this.gvcCellNetwork,
            this.gvcGSMScore,
            this.gvcCellTDScore});
            this.gvCellScore.ContextMenuStrip = this.cmnuCell;
            this.gvCellScore.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gvCellScore.Location = new System.Drawing.Point(3, 18);
            this.gvCellScore.MultiSelect = false;
            this.gvCellScore.Name = "gvCellScore";
            this.gvCellScore.ReadOnly = true;
            this.gvCellScore.RowTemplate.Height = 23;
            this.gvCellScore.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.gvCellScore.Size = new System.Drawing.Size(591, 163);
            this.gvCellScore.TabIndex = 0;
            // 
            // gvcCellName
            // 
            this.gvcCellName.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.gvcCellName.DataPropertyName = "CellName";
            this.gvcCellName.HeaderText = "小区名称";
            this.gvcCellName.Name = "gvcCellName";
            this.gvcCellName.ReadOnly = true;
            this.gvcCellName.Width = 140;
            // 
            // gvcLac
            // 
            this.gvcLac.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.gvcLac.DataPropertyName = "LAC";
            this.gvcLac.HeaderText = "LAC";
            this.gvcLac.Name = "gvcLac";
            this.gvcLac.ReadOnly = true;
            this.gvcLac.Width = 50;
            // 
            // gvcCI
            // 
            this.gvcCI.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.gvcCI.DataPropertyName = "CI";
            this.gvcCI.HeaderText = "CI";
            this.gvcCI.Name = "gvcCI";
            this.gvcCI.ReadOnly = true;
            this.gvcCI.Width = 50;
            // 
            // gvcCellNetwork
            // 
            this.gvcCellNetwork.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.gvcCellNetwork.DataPropertyName = "Network";
            this.gvcCellNetwork.HeaderText = "网络类型";
            this.gvcCellNetwork.Name = "gvcCellNetwork";
            this.gvcCellNetwork.ReadOnly = true;
            this.gvcCellNetwork.Width = 80;
            // 
            // gvcGSMScore
            // 
            this.gvcGSMScore.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.gvcGSMScore.DataPropertyName = "GSMAllScore";
            this.gvcGSMScore.HeaderText = "GSM总得分";
            this.gvcGSMScore.Name = "gvcGSMScore";
            this.gvcGSMScore.ReadOnly = true;
            // 
            // gvcCellTDScore
            // 
            this.gvcCellTDScore.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.gvcCellTDScore.DataPropertyName = "TDAllScore";
            this.gvcCellTDScore.HeaderText = "TD总得分";
            this.gvcCellTDScore.Name = "gvcCellTDScore";
            this.gvcCellTDScore.ReadOnly = true;
            // 
            // cmnuCell
            // 
            this.cmnuCell.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tmenuGSMCellKPI,
            this.tmenuTDCellKPI});
            this.cmnuCell.Name = "cmnuCell";
            this.cmnuCell.Size = new System.Drawing.Size(153, 48);
            // 
            // tmenuGSMCellKPI
            // 
            this.tmenuGSMCellKPI.Name = "tmenuGSMCellKPI";
            this.tmenuGSMCellKPI.Size = new System.Drawing.Size(152, 22);
            this.tmenuGSMCellKPI.Text = "查看GSM指标";
            // 
            // tmenuTDCellKPI
            // 
            this.tmenuTDCellKPI.Name = "tmenuTDCellKPI";
            this.tmenuTDCellKPI.Size = new System.Drawing.Size(152, 22);
            this.tmenuTDCellKPI.Text = "查看TD指标";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.gvGridScore);
            this.groupBox4.Dock = System.Windows.Forms.DockStyle.Right;
            this.groupBox4.Location = new System.Drawing.Point(600, 18);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(541, 184);
            this.groupBox4.TabIndex = 1;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "占用网格";
            // 
            // gvGridScore
            // 
            this.gvGridScore.AllowUserToAddRows = false;
            this.gvGridScore.AllowUserToDeleteRows = false;
            this.gvGridScore.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.gvGridScore.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.gvGridScore.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.gvcGridName,
            this.gvcGridSchene,
            this.gvcCCount,
            this.gvcGridGSMScore,
            this.gvcGridTDScore});
            this.gvGridScore.ContextMenuStrip = this.cmnuGrid;
            this.gvGridScore.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gvGridScore.Location = new System.Drawing.Point(3, 18);
            this.gvGridScore.MultiSelect = false;
            this.gvGridScore.Name = "gvGridScore";
            this.gvGridScore.ReadOnly = true;
            this.gvGridScore.RowTemplate.Height = 23;
            this.gvGridScore.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.gvGridScore.Size = new System.Drawing.Size(535, 163);
            this.gvGridScore.TabIndex = 0;
            // 
            // gvcGridName
            // 
            this.gvcGridName.DataPropertyName = "GridName";
            this.gvcGridName.HeaderText = "网格名称";
            this.gvcGridName.Name = "gvcGridName";
            this.gvcGridName.ReadOnly = true;
            // 
            // gvcGridSchene
            // 
            this.gvcGridSchene.DataPropertyName = "GridSchene";
            this.gvcGridSchene.HeaderText = "网格场景";
            this.gvcGridSchene.Name = "gvcGridSchene";
            this.gvcGridSchene.ReadOnly = true;
            // 
            // gvcCCount
            // 
            this.gvcCCount.DataPropertyName = "ComplaintCount";
            this.gvcCCount.HeaderText = "投诉量";
            this.gvcCCount.Name = "gvcCCount";
            this.gvcCCount.ReadOnly = true;
            // 
            // gvcGridGSMScore
            // 
            this.gvcGridGSMScore.DataPropertyName = "GSMAllScore";
            this.gvcGridGSMScore.HeaderText = "GSM总得分";
            this.gvcGridGSMScore.Name = "gvcGridGSMScore";
            this.gvcGridGSMScore.ReadOnly = true;
            // 
            // gvcGridTDScore
            // 
            this.gvcGridTDScore.DataPropertyName = "TDAllScore";
            this.gvcGridTDScore.HeaderText = "TD总得分";
            this.gvcGridTDScore.Name = "gvcGridTDScore";
            this.gvcGridTDScore.ReadOnly = true;
            // 
            // cmnuGrid
            // 
            this.cmnuGrid.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tmenuGSMGridKPI,
            this.tmenuTDGridKPI});
            this.cmnuGrid.Name = "cmnuGrid";
            this.cmnuGrid.Size = new System.Drawing.Size(153, 48);
            // 
            // tmenuGSMGridKPI
            // 
            this.tmenuGSMGridKPI.Name = "tmenuGSMGridKPI";
            this.tmenuGSMGridKPI.Size = new System.Drawing.Size(152, 22);
            this.tmenuGSMGridKPI.Text = "查看GSM指标";
            // 
            // tmenuTDGridKPI
            // 
            this.tmenuTDGridKPI.Name = "tmenuTDGridKPI";
            this.tmenuTDGridKPI.Size = new System.Drawing.Size(152, 22);
            this.tmenuTDGridKPI.Text = "查看TD指标";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.gvResult);
            this.groupBox5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox5.Location = new System.Drawing.Point(0, 157);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(1144, 315);
            this.groupBox5.TabIndex = 2;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "用户结果列表";
            // 
            // gvResult
            // 
            this.gvResult.AllowUserToAddRows = false;
            this.gvResult.AllowUserToDeleteRows = false;
            this.gvResult.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.gvResult.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.gvResult.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.gvcUserPhone,
            this.gvcUserSex,
            this.gvcTerminalType,
            this.gvcNetworkConvert,
            this.gvcNetworkQuality,
            this.gvcInternet,
            this.gvcVoiceCall});
            this.gvResult.ContextMenuStrip = this.cmnuResult;
            this.gvResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gvResult.Location = new System.Drawing.Point(3, 18);
            this.gvResult.MultiSelect = false;
            this.gvResult.Name = "gvResult";
            this.gvResult.ReadOnly = true;
            this.gvResult.RowTemplate.Height = 23;
            this.gvResult.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.gvResult.Size = new System.Drawing.Size(1138, 294);
            this.gvResult.TabIndex = 0;
            this.gvResult.SelectionChanged += new System.EventHandler(this.gvResult_SelectionChanged);
            // 
            // gvcUserPhone
            // 
            this.gvcUserPhone.DataPropertyName = "userPhone";
            this.gvcUserPhone.HeaderText = "电话";
            this.gvcUserPhone.Name = "gvcUserPhone";
            this.gvcUserPhone.ReadOnly = true;
            // 
            // gvcUserSex
            // 
            this.gvcUserSex.DataPropertyName = "userSex";
            this.gvcUserSex.HeaderText = "性别";
            this.gvcUserSex.Name = "gvcUserSex";
            this.gvcUserSex.ReadOnly = true;
            // 
            // gvcTerminalType
            // 
            this.gvcTerminalType.DataPropertyName = "terminalType";
            this.gvcTerminalType.HeaderText = "终端类型";
            this.gvcTerminalType.Name = "gvcTerminalType";
            this.gvcTerminalType.ReadOnly = true;
            // 
            // gvcNetworkConvert
            // 
            this.gvcNetworkConvert.DataPropertyName = "networkCover";
            this.gvcNetworkConvert.HeaderText = "网络覆盖";
            this.gvcNetworkConvert.Name = "gvcNetworkConvert";
            this.gvcNetworkConvert.ReadOnly = true;
            // 
            // gvcNetworkQuality
            // 
            this.gvcNetworkQuality.DataPropertyName = "networkQuality";
            this.gvcNetworkQuality.HeaderText = "网络质量";
            this.gvcNetworkQuality.Name = "gvcNetworkQuality";
            this.gvcNetworkQuality.ReadOnly = true;
            // 
            // gvcInternet
            // 
            this.gvcInternet.DataPropertyName = "mobileInternet";
            this.gvcInternet.HeaderText = "手机上网";
            this.gvcInternet.Name = "gvcInternet";
            this.gvcInternet.ReadOnly = true;
            // 
            // gvcVoiceCall
            // 
            this.gvcVoiceCall.DataPropertyName = "voiceCall";
            this.gvcVoiceCall.HeaderText = "语音通话";
            this.gvcVoiceCall.Name = "gvcVoiceCall";
            this.gvcVoiceCall.ReadOnly = true;
            // 
            // cmnuResult
            // 
            this.cmnuResult.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.渲染该用户轨迹ToolStripMenuItem,
            this.渲染全部用户ToolStripMenuItem});
            this.cmnuResult.Name = "cmnuResult";
            this.cmnuResult.Size = new System.Drawing.Size(161, 70);
            // 
            // 渲染该用户轨迹ToolStripMenuItem
            // 
            this.渲染该用户轨迹ToolStripMenuItem.Name = "渲染该用户轨迹ToolStripMenuItem";
            this.渲染该用户轨迹ToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.渲染该用户轨迹ToolStripMenuItem.Text = "渲染该用户轨迹";
            this.渲染该用户轨迹ToolStripMenuItem.Click += new System.EventHandler(this.渲染该用户轨迹ToolStripMenuItem_Click);
            // 
            // 渲染全部用户ToolStripMenuItem
            // 
            this.渲染全部用户ToolStripMenuItem.Name = "渲染全部用户ToolStripMenuItem";
            this.渲染全部用户ToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.渲染全部用户ToolStripMenuItem.Text = "渲染全部用户";
            this.渲染全部用户ToolStripMenuItem.Click += new System.EventHandler(this.渲染全部用户ToolStripMenuItem_Click);
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.dataGridViewTextBoxColumn1.DataPropertyName = "CellName";
            this.dataGridViewTextBoxColumn1.HeaderText = "小区名称";
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            this.dataGridViewTextBoxColumn1.ReadOnly = true;
            this.dataGridViewTextBoxColumn1.Width = 120;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.dataGridViewTextBoxColumn2.DataPropertyName = "LAC";
            this.dataGridViewTextBoxColumn2.HeaderText = "LAC";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            this.dataGridViewTextBoxColumn2.ReadOnly = true;
            this.dataGridViewTextBoxColumn2.Width = 50;
            // 
            // dataGridViewTextBoxColumn3
            // 
            this.dataGridViewTextBoxColumn3.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.dataGridViewTextBoxColumn3.DataPropertyName = "CI";
            this.dataGridViewTextBoxColumn3.HeaderText = "CI";
            this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            this.dataGridViewTextBoxColumn3.ReadOnly = true;
            this.dataGridViewTextBoxColumn3.Width = 50;
            // 
            // dataGridViewTextBoxColumn4
            // 
            this.dataGridViewTextBoxColumn4.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.dataGridViewTextBoxColumn4.DataPropertyName = "Network";
            this.dataGridViewTextBoxColumn4.HeaderText = "网络类型";
            this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
            this.dataGridViewTextBoxColumn4.ReadOnly = true;
            this.dataGridViewTextBoxColumn4.Width = 80;
            // 
            // dataGridViewTextBoxColumn5
            // 
            this.dataGridViewTextBoxColumn5.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.dataGridViewTextBoxColumn5.DataPropertyName = "GSMAllScore";
            this.dataGridViewTextBoxColumn5.HeaderText = "GSM总得分";
            this.dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
            this.dataGridViewTextBoxColumn5.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn6
            // 
            this.dataGridViewTextBoxColumn6.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.dataGridViewTextBoxColumn6.DataPropertyName = "TDAllScore";
            this.dataGridViewTextBoxColumn6.HeaderText = "TD总得分";
            this.dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
            this.dataGridViewTextBoxColumn6.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn7
            // 
            this.dataGridViewTextBoxColumn7.DataPropertyName = "GridName";
            this.dataGridViewTextBoxColumn7.HeaderText = "网格名称";
            this.dataGridViewTextBoxColumn7.Name = "dataGridViewTextBoxColumn7";
            this.dataGridViewTextBoxColumn7.ReadOnly = true;
            this.dataGridViewTextBoxColumn7.Width = 90;
            // 
            // dataGridViewTextBoxColumn8
            // 
            this.dataGridViewTextBoxColumn8.DataPropertyName = "GridSchene";
            this.dataGridViewTextBoxColumn8.HeaderText = "网格场景";
            this.dataGridViewTextBoxColumn8.Name = "dataGridViewTextBoxColumn8";
            this.dataGridViewTextBoxColumn8.ReadOnly = true;
            this.dataGridViewTextBoxColumn8.Width = 90;
            // 
            // dataGridViewTextBoxColumn9
            // 
            this.dataGridViewTextBoxColumn9.DataPropertyName = "ComplaintCount";
            this.dataGridViewTextBoxColumn9.HeaderText = "投诉量";
            this.dataGridViewTextBoxColumn9.Name = "dataGridViewTextBoxColumn9";
            this.dataGridViewTextBoxColumn9.ReadOnly = true;
            this.dataGridViewTextBoxColumn9.Width = 90;
            // 
            // dataGridViewTextBoxColumn10
            // 
            this.dataGridViewTextBoxColumn10.DataPropertyName = "GSMAllScore";
            this.dataGridViewTextBoxColumn10.HeaderText = "GSM总得分";
            this.dataGridViewTextBoxColumn10.Name = "dataGridViewTextBoxColumn10";
            this.dataGridViewTextBoxColumn10.ReadOnly = true;
            this.dataGridViewTextBoxColumn10.Width = 91;
            // 
            // dataGridViewTextBoxColumn11
            // 
            this.dataGridViewTextBoxColumn11.DataPropertyName = "TDAllScore";
            this.dataGridViewTextBoxColumn11.HeaderText = "TD总得分";
            this.dataGridViewTextBoxColumn11.Name = "dataGridViewTextBoxColumn11";
            this.dataGridViewTextBoxColumn11.ReadOnly = true;
            this.dataGridViewTextBoxColumn11.Width = 91;
            // 
            // dataGridViewTextBoxColumn12
            // 
            this.dataGridViewTextBoxColumn12.DataPropertyName = "userPhone";
            this.dataGridViewTextBoxColumn12.HeaderText = "电话";
            this.dataGridViewTextBoxColumn12.Name = "dataGridViewTextBoxColumn12";
            this.dataGridViewTextBoxColumn12.ReadOnly = true;
            this.dataGridViewTextBoxColumn12.Width = 144;
            // 
            // dataGridViewTextBoxColumn13
            // 
            this.dataGridViewTextBoxColumn13.DataPropertyName = "userSex";
            this.dataGridViewTextBoxColumn13.HeaderText = "性别";
            this.dataGridViewTextBoxColumn13.Name = "dataGridViewTextBoxColumn13";
            this.dataGridViewTextBoxColumn13.ReadOnly = true;
            this.dataGridViewTextBoxColumn13.Width = 145;
            // 
            // dataGridViewTextBoxColumn14
            // 
            this.dataGridViewTextBoxColumn14.DataPropertyName = "terminalType";
            this.dataGridViewTextBoxColumn14.HeaderText = "终端类型";
            this.dataGridViewTextBoxColumn14.Name = "dataGridViewTextBoxColumn14";
            this.dataGridViewTextBoxColumn14.ReadOnly = true;
            this.dataGridViewTextBoxColumn14.Width = 144;
            // 
            // dataGridViewTextBoxColumn15
            // 
            this.dataGridViewTextBoxColumn15.DataPropertyName = "networkCover";
            this.dataGridViewTextBoxColumn15.HeaderText = "网络覆盖";
            this.dataGridViewTextBoxColumn15.Name = "dataGridViewTextBoxColumn15";
            this.dataGridViewTextBoxColumn15.ReadOnly = true;
            this.dataGridViewTextBoxColumn15.Width = 144;
            // 
            // dataGridViewTextBoxColumn16
            // 
            this.dataGridViewTextBoxColumn16.DataPropertyName = "networkQuality";
            this.dataGridViewTextBoxColumn16.HeaderText = "网络质量";
            this.dataGridViewTextBoxColumn16.Name = "dataGridViewTextBoxColumn16";
            this.dataGridViewTextBoxColumn16.ReadOnly = true;
            this.dataGridViewTextBoxColumn16.Width = 144;
            // 
            // dataGridViewTextBoxColumn17
            // 
            this.dataGridViewTextBoxColumn17.DataPropertyName = "mobileInternet";
            this.dataGridViewTextBoxColumn17.HeaderText = "手机上网";
            this.dataGridViewTextBoxColumn17.Name = "dataGridViewTextBoxColumn17";
            this.dataGridViewTextBoxColumn17.ReadOnly = true;
            this.dataGridViewTextBoxColumn17.Width = 145;
            // 
            // dataGridViewTextBoxColumn18
            // 
            this.dataGridViewTextBoxColumn18.DataPropertyName = "voiceCall";
            this.dataGridViewTextBoxColumn18.HeaderText = "语音通话";
            this.dataGridViewTextBoxColumn18.Name = "dataGridViewTextBoxColumn18";
            this.dataGridViewTextBoxColumn18.ReadOnly = true;
            this.dataGridViewTextBoxColumn18.Width = 144;
            // 
            // SatisfactionAnalysisReportForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1144, 677);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Name = "SatisfactionAnalysisReportForm";
            this.Text = "满意度调查问卷分析";
            this.Load += new System.EventHandler(this.SatisfactionAnalysisReportForm_Load);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvCellScore)).EndInit();
            this.cmnuCell.ResumeLayout(false);
            this.groupBox4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvGridScore)).EndInit();
            this.cmnuGrid.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvResult)).EndInit();
            this.cmnuResult.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cbxParam;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cbxConnection;
        private System.Windows.Forms.ComboBox cbxOperator;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ListBox lstCondition;
        private System.Windows.Forms.Button btnRemove;
        private System.Windows.Forms.Button btnAdd;
        private System.Windows.Forms.Button btnQuery;
        private System.Windows.Forms.Button btnClear;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.ComboBox cbxQuestionType;
        private System.Windows.Forms.ComboBox cbxMonth;
        private System.Windows.Forms.ComboBox cbxCity;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.ComboBox cbxParamValue;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.DataGridView gvResult;
        private System.Windows.Forms.DataGridView gvGridScore;
        private System.Windows.Forms.ContextMenuStrip cmnuResult;
        private System.Windows.Forms.ContextMenuStrip cmnuCell;
        private System.Windows.Forms.ToolStripMenuItem 渲染该用户轨迹ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 渲染全部用户ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem tmenuGSMCellKPI;
        private System.Windows.Forms.ToolStripMenuItem tmenuTDCellKPI;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.DataGridView gvCellScore;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcUserPhone;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcUserSex;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcTerminalType;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcNetworkConvert;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcNetworkQuality;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcInternet;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcVoiceCall;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcGridName;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcGridSchene;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcCCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcGridGSMScore;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcGridTDScore;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcCellName;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcLac;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcCI;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcCellNetwork;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcGSMScore;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcCellTDScore;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn7;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn8;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn9;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn10;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn11;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn12;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn13;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn14;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn15;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn16;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn17;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn18;
        private System.Windows.Forms.ContextMenuStrip cmnuGrid;
        private System.Windows.Forms.ToolStripMenuItem tmenuGSMGridKPI;
        private System.Windows.Forms.ToolStripMenuItem tmenuTDGridKPI;
    }
}