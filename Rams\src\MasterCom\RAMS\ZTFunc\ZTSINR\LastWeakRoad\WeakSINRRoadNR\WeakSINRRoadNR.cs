﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakSINRRoadNR : WeakSINRRoad
    {
        public WeakSINRRoadNR()
        { 
        
        }

        public WeakSINRRoadNR(WeakSINRRoadNR road)
               : base(road)
        {
        }

        protected int lteRsrpCount = 0;
        protected float lteRsrpSum = 0;
        public float? AvgLteRSRP { get; protected set; }

        protected int lteSinrCount = 0;
        protected float lteSinrSum = 0;
        public float? AvgLteSINR { get; protected set; }

        public List<string> ArfcnPciList { get; set; } = new List<string>();
        public string ArfcnPcis
        {
            get
            {
                StringBuilder arfcnPcis = new StringBuilder();
                foreach (string arfcnPci in ArfcnPciList)
                {
                    if (arfcnPcis.Length > 0)
                    {
                        arfcnPcis.Append(" | ");
                    }
                    arfcnPcis.Append(arfcnPci);
                }
                return arfcnPcis.ToString();
            }
        }

        public string AreaName { get; set; }

        protected override void addCellInfoList(TestPoint testPoint)
        {
            NRCell nrCell = testPoint.GetMainCell_NR();

            if (nrCell != null)
            {
                //string lacci = nrCell.TAC.ToString() + "_" + nrCell.NCI.ToString();
                //if (!lacciList.Contains(lacci))
                //{
                //    lacciList.Add(lacci);
                //}

                string arfcnPci = nrCell.SSBARFCN.ToString() + "_" + nrCell.PCI.ToString();
                if (!ArfcnPciList.Contains(arfcnPci))
                {
                    ArfcnPciList.Add(arfcnPci);
                }

                if (!cellNames.Contains(nrCell.Name))
                {
                    cellNames.Add(nrCell.Name);
                }
            }
        }

        public void AddOtherTPInfo(TestPoint testPoint)
        {
            float? lteRsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(testPoint, true);
            if (lteRsrp != null)
            {
                lteRsrpCount++;
                lteRsrpSum += (float)lteRsrp;
            }

            float? lteSinr = NRTpHelper.NrLteTpManager.GetSCellSinr(testPoint, true);
            if (lteSinr != null)
            {
                lteSinrCount++;
                lteSinrSum += (float)lteSinr;
            }
        }

        protected float? caculateAvg(int count, float sum)
        {
            float? res = null;
            if (count > 0)
            {
                res = (float?)Math.Round(sum / count, 2);
            }
            return res;
        }

        public override void MakeSummary()
        {
            AvgLteRSRP = caculateAvg(lteRsrpCount, lteRsrpSum);
            AvgLteSINR = caculateAvg(lteSinrCount, lteSinrSum);
            findAreaName();
        }

        protected virtual void findAreaName()
        {
            string strAreaName = GISManager.GetInstance().GetAreaPlaceDesc(MidLng, MidLat);
            AreaName = getValidData(strAreaName, AreaName);
        }

        private string getValidData(string strName, string dataName)
        {
            string name = dataName;
            if (strName != null)
            {
                if (string.IsNullOrEmpty(name))
                {
                    name = strName;
                }
                else
                {
                    if (!name.Contains(strName) && strName != "")
                    {
                        name += "," + strName;
                    }
                }
            }
            return name;
        }

    }
}
