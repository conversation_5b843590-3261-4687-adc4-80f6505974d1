using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using System.Xml;
using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.NOP.CM;
using MasterCom.RAMS.AnaZT;
using MasterCom.RAMS.Compare;
using MasterCom.RAMS.CQT;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.EventBlock;
using MasterCom.RAMS.Func.OutServiceInfo;
using MasterCom.RAMS.Func.PerformanceParam;
using MasterCom.RAMS.Func.PlanningInfo;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Model.PerformanceParam;
using MasterCom.RAMS.Model.RoadProtection;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.src;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.ZTFunc.ZTCellSplit;
using MasterCom.RAMS.ZTFunc.ZTCluster;
using MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna;
using MasterCom.RAMS.ZTFunc.ZTLastWeakRoadData;
using MasterCom.RAMS.ZTFunc.ZTPerformRelated;
using MasterCom.Util;
using MasterCom.RAMS.NewBlackBlock;
using MasterCom.RAMS.UserMng;
namespace MasterCom.RAMS.Model
{
    public class MainModel
    {
        private MainModel()
        {
            visibleOffsetManager = new VisibleOffsetManager(this);
            dtDataManager = new DTDataManager(this);
            modelInstance = this;
#if Guangdong
            MainDbUser.LoginName = "popkpi";
            MainDbUser.Password = "poppsw";
#endif
            DistrictChanged += MainModel_DistrictChanged;
            InitData();
        }

        private void InitData()
        {
            SelectedLTECells = new List<LTECell>();
            LoginManageCfg = new LoginValues();
        }

        void MainModel_DistrictChanged(object sender, EventArgs e)
        {
            QueryNewBlackBlock.TokenBlockTypeDic = null;
        }
        private static MainModel modelInstance = null;
        private static readonly object lockObj = new object();
        internal static MainModel GetInstance()
        {
            if (modelInstance == null)
            {
                lock (lockObj)
                {
                    if (modelInstance == null)
                    {
                        modelInstance = new MainModel();
                    }
                }
            }
            return modelInstance;
        }

        public void SetMainForm(MainForm mainForm)
        {
            this.mainForm = mainForm;
        }

        private Dictionary<string, object> mapParamConfigDic = new Dictionary<string, object>();
        public Dictionary<string, object> MapParamConfigDic
        {
            get
            {
                return mapParamConfigDic;
            }
            set
            {
                mapParamConfigDic = value;
            }

        }
        private Dictionary<string, object> userParamConfigDic = new Dictionary<string, object>();
        /// <summary>
        /// 用户自己定制的个性设置，无需升级更新
        /// </summary>
        public Dictionary<string, object> UserParamConfigDic
        {
            get
            {
                return userParamConfigDic;
            }
            set
            {
                userParamConfigDic = value;
            }
        }

        private Blackboard blackBoard = new Blackboard();
        /// <summary>
        /// 任何人都可用，用来保存窗口
        /// </summary>
        public Blackboard Blackboard
        {
            get { return blackBoard; }
        }

        public object GetObjectFromBlackboard(string key)
        {
            object ret = null;
            blackBoard.Properties.TryGetValue(key, out ret);
            return ret;
        }

        public object GetObjectFromBlackboard(Type objType)
        {
            return GetObjectFromBlackboard(objType.FullName);
        }

        private ModelConfig modelConfig;
        public ModelConfig ModelConfig
        {
            get {
                if (modelConfig == null)
                    modelConfig = new ModelConfig();
                return modelConfig; }
        }
        public bool bDisableFileDownload = false;

        public MainForm MainForm
        {
            get { return mainForm; }
        }

        public void Init()
        {
            loadConfig();
        }

        public int VersionID
        {
            get { return versionId; }
            set { versionId = value; }
        }
        /// <summary>
        /// 从服务端获取过来的客户端最新版本ID，tb_cfg_static_version
        /// </summary>
        public int LatestVersionID
        {
            get;
            set;
        }
        public bool NeedUpdate
        {
            get { return LatestVersionID > versionId; }
        }

        public Server Server
        {
            get { return server; }
            set { server = value; }
        }

        private List<Server> serverOfUsableList = new List<Server>();
        /// <summary>
        /// 从服务端获取的（或从本地配置中读取的）所有可连接的服务器列表
        /// </summary>
        public List<Server> ServerOfUsableList
        {
            get
            {
                if (serverOfUsableList == null)
                {
                    return new List<Server>();
                }
                return serverOfUsableList;
            }
            set
            {
                serverOfUsableList = value;
            }
        }

        public User User
        {
            get { return user; }
            set { user = value; }
        }

        private User mainDbUser = new User();
        public User MainDbUser
        {
            get { return mainDbUser; }
            set { mainDbUser = value; }
        }

        public List<string> UsedUsers
        {
            get { return usedUsers; }
            set { usedUsers = value; }
        }

        public LoginValues LoginManageCfg { get; set; }

        public MenuConfig MenuConfig
        {
            get { return menuConfig; }
            set { menuConfig = value; }
        }

        public WorkSpace WorkSpace
        {
            get { return workSpace; }
            set { workSpace = value; }
        }

        public int DistrictID
        {
            get { return districtID; }
            set { districtID = value; }
        }

        public bool HasLoadedInterfaceInfo
        {
            get;
            set;
        }

        public event EventHandler SqlTextBoxChanged;

        public void SubmitSqlTextBoxChanged(object sender)
        {
            if (SqlTextBoxChanged != null)
            {
                SqlTextBoxChanged(sender, null);
            }
        }

        public event EventHandler DistrictChanged;

        public void FireDistrictChanged(object sender)
        {
            if (DistrictChanged != null)
            {
                DistrictChanged(sender, null);
            }
        }

        public event EventHandler CellManagerChanged;

        public void FireCellManagerChanged(object sender)
        {
            if (CellManagerChanged != null)
            {
                CellManagerChanged(sender, null);
            }
        }

        public CellManager CellManager
        {
            get { return cellManager; }
        }

        public List<FileInfo> FileInfos
        {
            get { return fileInfos; }
            set { fileInfos = value; }
        }

        public FileInfo SelectedFileInfo
        {
            get { return selectedFileInfo; }
            set { selectedFileInfo = value; }
        }

        public List<int> FileAgentFilter
        {
            get { return fileAgentFilter; }
            set { fileAgentFilter = value; }
        }

        public event EventHandler FileInfosChanged;

        public void FireFileInfosChanged(object sender)
        {
            MtAction action = new ActionCreateChildFrameSingle();
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = mainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.FileInfoForm";
            actionParam["Text"] = "查询文件结果";
            actionParam["ImageFilePath"] = @"images\regionfile.gif";
            action.Param = actionParam;
            action.OnAction();
            if (FileInfosChanged != null)
            {
                FileInfosChanged(sender, null);
            }
        }
        public LTEBTS SelectedLTEBTS
        {
            get;
            set;
        }
        public BTS SelectedBTS
        {
            get { return selectedBTS; }
            set { selectedBTS = value; }
        }

        public PlanBTS SelectedPlanBTS
        {
            get { return selectedPlanBTS; }
            set { selectedPlanBTS = value; }
        }

        public event EventHandler SelectedBTSChanged;

        public void FireSelectedBTSChanged(object sender)
        {
            if (SelectedBTSChanged != null)
            {
                SelectedBTSChanged(sender, null);
            }
        }

        public event EventHandler CellDrawInfoChanged;

        public void FireCellDrawInfoChanged(object sender)
        {
            if (CellDrawInfoChanged != null)
            {
                CellDrawInfoChanged(sender, null);
            }
        }

        public Cell SelectedCell
        {
            get { return selectedCell; }
            set
            {
                if (this.selectedCells != null
                    && selectedCell != null)
                {
                    selectedCells.Remove(selectedCell);
                }
                selectedCell = value;
                if (selectedCell != null)
                {
                    if (this.selectedCells == null)
                    {
                        selectedCells = new List<Cell>();
                    }
                    if (!selectedCells.Contains(selectedCell))
                    {
                        selectedCells.Add(selectedCell);
                    }
                }
            }
        }

        public TDNodeB SelectedTDNodeB
        {
            get { return selectedTDNodeB; }
            set { selectedTDNodeB = value; }
        }

        public TDCell SelectedTDCell
        {
            get { return selectedTDCell; }
            set
            {
                if (selectedTDCells != null && selectedTDCell != null)
                {
                    selectedTDCells.Remove(selectedTDCell);
                }
                selectedTDCell = value;
                if (selectedTDCell != null)
                {
                    if (selectedTDCells == null)
                    {
                        selectedTDCells = new List<TDCell>();
                    }
                    if (!selectedTDCells.Contains(selectedTDCell))
                    {
                        selectedTDCells.Add(selectedTDCell);
                    }
                }
            }
        }

        private LTECell selectedLTECell = null;
        public LTECell SelectedLTECell
        {
            get { return selectedLTECell; }
            set
            {
                if (SelectedLTECells != null && selectedLTECell != null)
                {
                    SelectedLTECells.Remove(selectedLTECell);
                }
                selectedLTECell = value;
                if (selectedLTECell != null)
                {
                    if (SelectedLTECells == null)
                    {
                        SelectedLTECells = new List<LTECell>();
                    }
                    if (!SelectedLTECells.Contains(selectedLTECell))
                    {
                        SelectedLTECells.Add(selectedLTECell);
                    }
                }
            }
        }

        public List<NRCell> SelectedNRCells { get; set; } = new List<NRCell>();
        public NRCell SelectedNRCell { get; set; }

        public void SetSelectedNRCell(NRCell nrCell)
        {
            //初始化,去除上一个选择的小区
            if (SelectedNRCells != null)
            {
                if (SelectedNRCell != null)
                {
                    SelectedNRCells.Remove(SelectedNRCell);
                }
            }
            else
            {
                SelectedNRCells = new List<NRCell>();
            }

            //添加新选择的小区
            if (nrCell == null)
            {
                SelectedNRCell = null;
            }
            else 
            {
                SelectedNRCell = nrCell;
                if (!SelectedNRCells.Contains(SelectedNRCell))
                {
                    SelectedNRCells.Add(SelectedNRCell);
                }
            }
        }

        public NRBTS SelectedNRBTS { get; set; }

        public WNodeB SelectedWNodeB
        {
            get { return selectedWNodeB; }
            set { selectedWNodeB = value; }
        }
        public WCell SelectedWCell
        {
            get { return selectedWCell; }
            set
            {
                if (selectedWCells != null && selectedWCell != null)
                {
                    selectedWCells.Remove(selectedWCell);
                }
                selectedWCell = value;
                if (selectedWCell != null)
                {
                    if (selectedWCells == null)
                    {
                        selectedWCells = new List<WCell>();
                    }
                    if (!selectedWCells.Contains(selectedWCell))
                    {
                        selectedWCells.Add(selectedWCell);
                    }

                }
            }
        }

        public CDNodeB SelectedCDNodeB
        {
            get { return selectedCDNodeB; }
            set { selectedCDNodeB = value; }
        }
        public CDCell SelectedCDCell
        {
            get { return selectedCDCell; }
            set
            {
                if (selectedCDCells != null && selectedCDCell != null)
                {
                    selectedCDCells.Remove(selectedCDCell);
                }
                selectedCDCell = value;
                if (selectedCDCell != null)
                {
                    if (selectedCDCells == null)
                    {
                        selectedCDCells = new List<CDCell>();
                    }
                    if (!selectedCDCells.Contains(selectedCDCell))
                    {
                        selectedCDCells.Add(selectedCDCell);
                    }
                }
            }
        }

        public List<Cell> SelectedCells
        {
            get { return selectedCells; }
            set { selectedCells = value; }
        }

        public List<TDCell> SelectedTDCells
        {
            get { return selectedTDCells; }
            set { selectedTDCells = value; }
        }

        public List<LTECell> SelectedLTECells
        {
            get { return selectedLTECells; }
            set { selectedLTECells = value; }
        }

        public List<WCell> SelectedWCells
        {
            get { return selectedWCells; }
            set { selectedWCells = value; }
        }

        public List<CDCell> SelectedCDCells
        {
            get { return selectedCDCells; }
            set { selectedCDCells = value; }
        }

        public CQTAddressItem SelectedCQTAddrItem
        {
            get { return selectedCQTAddrItem; }
            set { selectedCQTAddrItem = value; }
        }

        private ArrayList selCqtPlaces=new ArrayList();
        public ArrayList SelCqtPlaces
        {
            get { return selCqtPlaces; }
            set { this.selCqtPlaces=value; }
        }

        private List<LTECell> showPCICells = new List<LTECell>();
        public List<LTECell> ShowPCICells
        {
            get { return showPCICells; }
            set { showPCICells = value; }
        }

        public event EventHandler SelectedCellChanged;

        public void FireSelectedCellChanged(object sender)
        {
            if (SelectedCellChanged != null)
            {
                SelectedCellChanged(sender, null);
            }
        }

        public event EventHandler CommonSpecialInfoChanged;
        public void FireCommonSpecialInfoChanged(object sender, String specialName,object info)
        {
            if(CommonSpecialInfoChanged!=null)
            {
                CommonEventArgs arg = new CommonEventArgs();
                arg.name = specialName;
                arg.info= info;
                CommonSpecialInfoChanged(sender, arg);
            }
        }
        public int ServerUnduplicateCount
        {
            get
            {
                Dictionary<string, bool> namedic = new Dictionary<string, bool>();
                foreach(Cell ce in serverCells)
                {
                    namedic[ce.Name] = true;
                }
                return namedic.Count;
            }
            
        }
        public List<Cell> ServerCells
        {
            get { return serverCells; }
        }
       
        public List<TDCell> ServerTDCells
        {
            get { return serverTDCells; }
        }
        private List<LTECell> serverLTECells = new List<LTECell>();
        public List<LTECell> ServerLTECells
        {
            get { return serverLTECells; }
        }
        public List<WCell> ServerWCells
        {
            get { return serverWCells; }
        }
        public List<CDCell> ServerCDCells
        {
            get { return serverCDCells; }
        }

        public List<NRCell> ServerNRCells { get; private set; } = new List<NRCell>();

        public List<Pen> ServerCellPens
        {
            get { return serverCellPens; }
            set { serverCellPens = value; }
        }
        /// <summary>
        /// 指定查询是哪种类型（按文件or按区域or按小区or...）
        /// </summary>
        public enum NeedSearchType
        {
            Region,
            Street,
            Cell,
            ResvRegion,
            File,
            None
        }

        private object queryType;
        public object QueryType
        {
            get { return queryType; }
            set { queryType = value; }
        }

        private bool isFusionInclude = false;
        public bool IsFusionInclude
        {
            get { return isFusionInclude; }
            set { isFusionInclude = value; }
        }

        public event EventHandler ServerCellsChanged;

        public void FireServerCellsChanged(object sender)
        {
            if (ServerCellsChanged != null)
            {
                ServerCellsChanged(sender, null);
            }
        }

        public event EventHandler GridQueryChanged;

        public void FireGridQueryChanged(object sender)
        {
            if (GridQueryChanged != null)
            {
                GridQueryChanged(sender, null);
            }
        }

        public DTDataManager DTDataManager
        {
            get { return dtDataManager; }
            set { dtDataManager = value; }
        }

        private List<TestPoint> tempPointList = new List<TestPoint>();
        public List<TestPoint> TempPointList
        {
            get { return tempPointList; }
            set { tempPointList = value; }
        }

        public MasterCom.RAMS.Func.CustomExpressionForm.FormAction FormAction;

        public List<CExpress> CExpressList
        {
            get { return cExpressList; }
            set { cExpressList = value; }
        }

        public CExpressRet_and CExpressRet_and
        {
            get { return cExpressRest_and; }
            set { cExpressRest_and = value; }
        }

        /// <summary>
        /// 清空、初始化DTDataManager、SelectedTestPoints等
        /// </summary>
        public void ClearDTData()
        {
            DTDataManager.Clear();
            SelectedTestPoints.Clear();
            SelectedEvents.Clear();
            SelectedMessage = null;
            CoCellTestPointDic = null;
            GC.Collect();
        }

        public event EventHandler ClearDataEvent;
        public void FireClearData(object sender)
        {
            if (ClearDataEvent!=null)
            {
                ClearDataEvent(sender, EventArgs.Empty);
            }
        }

        public event EventHandler DTDataChanged;

        public void FireDTDataChanged(object sender)
        {
            //================== clear ==================
            serverCells.Clear();
            serverTDCells.Clear();
            serverWCells.Clear();
            serverLTECells = new List<LTECell>();
            ServerNRCells.Clear();
            foreach (DTFileDataManager fileDataManager in dtDataManager.FileDataManagers)
            {
                #region 关联采样点对应的服务小区
                foreach (TestPoint testPoint in fileDataManager.TestPoints)
                {
                    TDCell tdCell = null;
                    Cell cell = null;
                    WCell wCell = null;
                    LTECell lteCell = null;
                    NRCell nrCell = null;

                    getTPServerCell(testPoint, ref tdCell, ref cell, ref wCell, ref lteCell, ref nrCell);
                    addServerCell(tdCell, cell, wCell, lteCell, nrCell);
                }
                #endregion

                #region 关联事件对于的服务小区
                foreach (Event e in fileDataManager.Events)
                {
                    ICell cell = e.GetSrcCell();
                    addServerCell(cell);
                }
                #endregion
            }

            //================== 刷新 ==================
            FireServerCellsChanged(this);
            dtDataManager.Sort();
            if (DTDataChanged != null)
            {
                DTDataChanged(sender, null);
            }
        }

        private static void getTPServerCell(TestPoint testPoint, ref TDCell tdCell, ref Cell cell, ref WCell wCell, ref LTECell lteCell, ref NRCell nrCell)
        {
            switch (testPoint)
            {
                case TestPoint_NR _://5G时双连接，会同时占用到5G和4G小区
                    nrCell = testPoint.GetMainCell_NR();
                    lteCell = testPoint.GetMainCell_LTE();
                    break;
                case LTETestPointDetail _:
                case SignalTestPoint _:
                    lteCell = testPoint.GetMainCell_LTE();
                    break;
                case LTEFddTestPoint _:
                    lteCell = testPoint.GetMainCell_LTE_FDD();
                    break;
                case ScanTestPoint_NR _:
                    nrCell = testPoint.GetCell_NRScan(0);
                    break;
                case ScanTestPoint_LTE _:
                case ScanTestPoint_NBIOT _:
                    lteCell = testPoint.GetCell_LTEScan(0);
                    break;
                case TDTestPointDetail _:
                case TDTestPointSummary _:
                    testPoint.GetMainCell_TD(out tdCell, out cell);
                    break;
                case ScanTestPoint_TD _:
                    tdCell = testPoint.GetCell_TDScan(0);
                    break;
                case WCDMATestPointDetail _:
                case WCDMATestPointSummary _:
                    wCell = testPoint.GetMainCell_W();
                    break;
                case TestPointScan _:
                    cell = testPoint.GetCell_TestPointScan(0);
                    break;
                case ScanTestPoint_G _:
                    cell = testPoint.GetCell_GSMScan(0);
                    break;
                default:
                    cell = testPoint.GetMainCell_GSM();
                    break;
            }
        }

        private void addServerCell(TDCell tdCell, Cell cell, WCell wCell, LTECell lteCell, NRCell nrCell)
        {
            if (nrCell != null && !ServerNRCells.Contains(nrCell))
            {
                ServerNRCells.Add(nrCell);
            }

            if (lteCell != null && !serverLTECells.Contains(lteCell))
            {
                serverLTECells.Add(lteCell);
            }
            else if (cell != null && !serverCells.Contains(cell))
            {
                serverCells.Add(cell);
            }
            else if (wCell != null && !serverWCells.Contains(wCell))
            {
                serverWCells.Add(wCell);
            }
            else if (tdCell != null && !serverTDCells.Contains(tdCell))
            {
                serverTDCells.Add(tdCell);
            }
        }

        private void addServerCell(ICell cell)
        {
            switch (cell)
            {
                case NRCell nrCell:
                    if (!ServerNRCells.Contains(nrCell))
                    {
                        ServerNRCells.Add(nrCell);
                    }
                    break;
                case LTECell lteCell:
                    if (!serverLTECells.Contains(lteCell))
                    {
                        serverLTECells.Add(lteCell);
                    }
                    break;
                case WCell wCell:
                    if (!serverWCells.Contains(wCell))
                    {
                        serverWCells.Add(wCell);
                    }
                    break;
                case TDCell tdCell:
                    if (!serverTDCells.Contains(tdCell))
                    {
                        serverTDCells.Add(tdCell);
                    }
                    break;
                case Cell gsmCell:
                    if (!serverCells.Contains(gsmCell))
                    {
                        serverCells.Add(gsmCell);
                    }
                    break;
            }
        }

        private WeakCoverAndIndexStruRelatedRxqualityInfo weakCovAndIndexRelatedRxqual = new WeakCoverAndIndexStruRelatedRxqualityInfo();
        public WeakCoverAndIndexStruRelatedRxqualityInfo WeakCovAndIndexRelatedRxqual
        {
            get { return weakCovAndIndexRelatedRxqual; }
            set { value = weakCovAndIndexRelatedRxqual; }
        }
        public void ClearSelectedEvents()
        {
            foreach (Event evt in selectedEvents)
            {
                evt.Selected = false;
            }
            selectedEvents.Clear();
        }
        public void ClearSelectedTestPoints()
        {
            foreach(TestPoint tp in selectedTestPoints)
            {
                tp.Selected = false;
            }
            selectedTestPoints.Clear();
        }
        public List<TestPoint> SelectedTestPoints
        {
            get { return selectedTestPoints; }
        }
        public List<WCDMATestPointDetail> SelectedTestPoints_W
        {
            get { return selectedTestPoints_W; }
        }

        public event EventHandler SelectedTestPointsChanged;

        public void FireSelectedTestPointsChanged(object sender)
        {
            if (SelectedTestPointsChanged != null)
            {
                SelectedTestPointsChanged(sender, null);
            }
        }

        public List<Event> SelectedEvents
        {
            get { return selectedEvents; }
            set { selectedEvents = value; }
        }

        public event EventHandler SelectedEventsChanged;

        public void FireSelectedEventsChanged(object sender)
        {
            if (SelectedEventsChanged != null)
            {
                SelectedEventsChanged(sender, null);
            }
        }
        private NewBlockQueryCond newBlackBlockCond = new NewBlockQueryCond();
        public NewBlockQueryCond NewBlackBlockCond
        {
            get { return newBlackBlockCond; }
            set { newBlackBlockCond = value; }
        }

        private NewBlockQueryCond curNewBbCond;
        public NewBlockQueryCond CurNewBbCond//问题黑点查询条件
        {
            get { return curNewBbCond; }
            set { curNewBbCond = value; }
        }
        public event EventHandler wlanApInfoDetailShow;
        public void FireWlanApInfoDetailShow(object sender)
        {
            if (wlanApInfoDetailShow != null)
            {
                wlanApInfoDetailShow(sender, null);
            }
        }

        public Message SelectedMessage
        {
            get { return selectedMessage; }
            set { selectedMessage = value; }
        }

        public event EventHandler SelectedMessageChanged;

        public void FireSelectedMessageChanged(object sender)
        {
            if (SelectedMessageChanged != null)
            {
                SelectedMessageChanged(sender, null);
            }
        }

        //public StatReportData CurStatReportData
        //{
        //    get { return curStatReportData; }
        //    set { curStatReportData = value; }
        //}

        //summary:KPI统计（区分运营商）
        //Hui 2010.03.24
        private StatReportData curChinaMobileStatReportData;
        public StatReportData CurChinaMobileStatReportData //中国移动KPI统计结果
        {
            get { return curChinaMobileStatReportData; }
            set { curChinaMobileStatReportData = value; }
        }

        private StatReportData curChinaMobileStatReportDataMo;
        public StatReportData CurChinaMobileStatReportDataMo //中国移动KPI统计结果Mo
        {
            get { return curChinaMobileStatReportDataMo; }
            set { curChinaMobileStatReportDataMo = value; }
        }

        private StatReportData curChinaMobileStatReportDataMt;
        public StatReportData CurChinaMobileStatReportDataMt //中国移动KPI统计结果Mt
        {
            get { return curChinaMobileStatReportDataMt; }
            set { curChinaMobileStatReportDataMt = value; }
        }

        private StatReportData curChinaUnicomStatReportData;
        public StatReportData CurChinaUnicomStatReportData //中国联通KPI统计结果
        {
            get { return curChinaUnicomStatReportData; }
            set { curChinaUnicomStatReportData = value; }
        }

        private StatReportData curChinaUnicomStatReportDataMo;
        public StatReportData CurChinaUnicomStatReportDataMo //中国联通KPI统计结果
        {
            get { return curChinaUnicomStatReportDataMo; }
            set { curChinaUnicomStatReportDataMo = value; }
        }

        private StatReportData curChinaUnicomStatReportDataMt;
        public StatReportData CurChinaUnicomStatReportDataMt //中国联通KPI统计结果
        {
            get { return curChinaUnicomStatReportDataMt; }
            set { curChinaUnicomStatReportDataMt = value; }
        }

        private StatReportData curChinaTelecomStatReportData;
        public StatReportData CurChinaTelecomStatReportData //中国电信KPI统计结果
        {
            get { return curChinaTelecomStatReportData; }
            set { curChinaTelecomStatReportData = value; }
        }

        private StatReportData curChinaTelecomStatReportDataMt;
        public StatReportData CurChinaTelecomStatReportDataMt //中国电信KPI统计结果
        {
            get { return curChinaTelecomStatReportDataMt; }
            set { curChinaTelecomStatReportDataMt = value; }
        }

        private StatReportData curChinaTelecomStatReportDataMo;
        public StatReportData CurChinaTelecomStatReportDataMo //中国电信KPI统计结果
        {
            get { return curChinaTelecomStatReportDataMo; }
            set { curChinaTelecomStatReportDataMo = value; }
        }

        private List<StatReportData> curChinaMobileStatReportDataList = new List<StatReportData>();
        public List<StatReportData> CurChinaMobileStatReportDataList //中国移动KPI统计结果
        {
            get { return curChinaMobileStatReportDataList; }
            set { curChinaMobileStatReportDataList = value; }
        }

        private List<StatReportData> curChinaMobileStatReportDataListMo = new List<StatReportData>();
        public List<StatReportData> CurChinaMobileStatReportDataListMo //中国移动KPI统计结果
        {
            get { return curChinaMobileStatReportDataListMo; }
            set { curChinaMobileStatReportDataListMo = value; }
        }

        private List<StatReportData> curChinaMobileStatReportDataListMt = new List<StatReportData>();
        public List<StatReportData> CurChinaMobileStatReportDataListMt //中国移动KPI统计结果
        {
            get { return curChinaMobileStatReportDataListMt; }
            set { curChinaMobileStatReportDataListMt = value; }
        }

        private List<StatReportData> curChinaUnicomStatReportDataList = new List<StatReportData>();
        public List<StatReportData> CurChinaUnicomStatReportDataList //中国联通KPI统计结果
        {
            get { return curChinaUnicomStatReportDataList; }
            set { curChinaUnicomStatReportDataList = value; }
        }

        private List<StatReportData> curChinaUnicomStatReportDataListMo = new List<StatReportData>();
        public List<StatReportData> CurChinaUnicomStatReportDataListMo //中国联通KPI统计结果
        {
            get { return curChinaUnicomStatReportDataListMo; }
            set { curChinaUnicomStatReportDataListMo = value; }
        }

        private List<StatReportData> curChinaUnicomStatReportDataListMt = new List<StatReportData>();
        public List<StatReportData> CurChinaUnicomStatReportDataListMt //中国联通KPI统计结果
        {
            get { return curChinaUnicomStatReportDataListMt; }
            set { curChinaUnicomStatReportDataListMt = value; }
        }

        private List<StatReportData> curChinaTelecomStatReportDataList = new List<StatReportData>();
        public List<StatReportData> CurChinaTelecomStatReportDataList //中国电信KPI统计结果
        {
            get { return curChinaTelecomStatReportDataList; }
            set { curChinaTelecomStatReportDataList = value; }
        }

        private List<StatReportData> curChinaTelecomStatReportDataListMo = new List<StatReportData>();
        public List<StatReportData> CurChinaTelecomStatReportDataListMo //中国电信KPI统计结果
        {
            get { return curChinaTelecomStatReportDataListMo; }
            set { curChinaTelecomStatReportDataListMo = value; }
        }

        private List<StatReportData> curChinaTelecomStatReportDataListMt = new List<StatReportData>();
        public List<StatReportData> CurChinaTelecomStatReportDataListMt //中国电信KPI统计结果
        {
            get { return curChinaTelecomStatReportDataListMt; }
            set { curChinaTelecomStatReportDataListMt = value; }
        }

        private bool kpiBlanceCaleGrid = false;
        public bool KPIBlanceCaleGrid
        {
            get { return kpiBlanceCaleGrid; }
            set { kpiBlanceCaleGrid = value; }
        }

        private bool kpiCaleGridPercent = false;
        public bool KPICaleGridPercent
        {
            get { return kpiCaleGridPercent; }
            set { kpiCaleGridPercent = value; }
        }
        //summary:KPI统计（区分运营商）

        //KPI小区统计 GIS显示
        private List<DbPoint> curStatGridPoints;
        public List<DbPoint> CurStatGridPoints
        {
            get { return curStatGridPoints; }
            set { curStatGridPoints = value; }
        }
        public List<GridPartParam> CurGridCoverData
        {
            get { return gridCoverInfoList; }
            set { gridCoverInfoList = value; }
        }
        public GridMatrix<ColorUnit> CurGridColorUnitMatrix
        {
            get { return gridColorUnitMatrix; }
            set { gridColorUnitMatrix = value; }
        }

        public GridMatrix<GridCellUnit> CurGridCellUnitMatrix
        {
            get { return gridCellUnitMatrix; }
            set { gridCellUnitMatrix = value; }
        }

        public List<GridPartParam> CurCPGridCoverDataYD
        {
            get { return gridCPCoverInfoListYD; }
            set { gridCPCoverInfoListYD = value; }
        }
        public List<GridPartParam> CurCPGridCoverDataLT
        {
            get { return gridCPCoverInfoListLT; }
            set { gridCPCoverInfoListLT = value; }
        }
        public List<CompUnit> CompUnitHisData
        {
            get { return compUnitHisList;}
            set { compUnitHisList = value;}
        }
        //新竞对数据
        public  List<CPDataInfo> CurCPDataInfoList 
        {
            get { return curCPDataInfoList; }
            set { curCPDataInfoList = value; }
        }
        public Dictionary<int, CPDataType> CurCPDataTypeDic
        {
            get { return curCPDataTypeDic; }
            set { curCPDataTypeDic = value; }
        }

        /// ////////////////////////////////////////////////
        public bool IsDrawEventResult = false;
        public List<EventResult> EventResultList
        {
            get { return evtResultList; }
            set { evtResultList = value; }
        }
        public List<PlaceRxLev> CellSimuResultList
        {
            get { return cellSimuResultList; }
            set { cellSimuResultList = value; }
        }
   
        /// <summary>
        /// 供参考的最后一个投诉的产生时间
        /// </summary>
        public GridMatrix<InjectGridUnit> CurStreetInjectMatrix
        {
            get { return streetInjectColorMatrix; }
            set { streetInjectColorMatrix = value; }
        }
     
        public List<StreetInjectInfo> TotalStreetInjInfoResultList
        {
            get { return totalInfoResultList; }
            set { totalInfoResultList = value; }
        }

        public List<AreaStreetCarrierInjectInfo> AreaStreetCarrierInjectInfos
        {
            get { return areaStreetCarrierInjectInfos; }
            set { areaStreetCarrierInjectInfos = value; }
        }
        public List<AreaStreetCarrierInjectInfo> AreaStreetCarrierInjectInfosCopy
        {
            get { return areaStreetCarrierInjectInfosCopy; }
            set { areaStreetCarrierInjectInfosCopy = value; }
        }

        public List<GridLongLat> GridNonContainLongLatList
        {
            get { return gridNonContainLongLatList; }
            set { gridNonContainLongLatList = value; }
        }
        public RxlevGridLongLat RxlevGridLongLat
        {
            get { return rxlevGridLongLat; }
            set { rxlevGridLongLat = value; }
        }
        public CellEmulateShowItem cellEmulateShowItem;

        public SampleRangeInfo SampleRangeInfo
        {
            get { return sampleRangeInfo; }
            set { sampleRangeInfo = value; }
        }
        public List<MasterCom.RAMS.Model.Interface.DIYMainCovercellChangeTableQueryByRegion.MainCoverInfo> MainCoverInfoList
        {
            get { return mainCoverInfoList; }
            set { mainCoverInfoList = value; }
        }

        private List<TDScanMainCellChangeFileItem> tDScanMCChangeFileItemLst = new List<TDScanMainCellChangeFileItem>();
        public List<TDScanMainCellChangeFileItem> TDScanMCChangeFileItemLst
        {
            get {return tDScanMCChangeFileItemLst; }
            set { tDScanMCChangeFileItemLst = value; }
        }

        private List<WScanMainCellChangeFileItem> wScanMCChangeFileItemLst = new List<WScanMainCellChangeFileItem>();
        public List<WScanMainCellChangeFileItem> WScanMCChangeFileItemLst
        {
            get { return wScanMCChangeFileItemLst; }
            set { wScanMCChangeFileItemLst = value; }
        }

        private List<TDScanMainCellChangeCellItem> curSelTdScanMcChangeCellItems = new List<TDScanMainCellChangeCellItem>();
        public List<TDScanMainCellChangeCellItem> CurSelTdScanMcChangeCellItems {
            get { return curSelTdScanMcChangeCellItems; }
            set { curSelTdScanMcChangeCellItems = value; }
        }


        private List<WScanMainCellChangeCellItem> curSelWScanMcChangeCellItems = new List<WScanMainCellChangeCellItem>();
        public List<WScanMainCellChangeCellItem> CurSelWScanMcChangeCellItems
        {
            get { return curSelWScanMcChangeCellItems; }
            set { curSelWScanMcChangeCellItems = value; }
        }

        public List<cellComparisonInfo> CellComparisonInfoList
        {
            get { return cellComparisonInfoList; }
            set { cellComparisonInfoList = value; }
        }

        public List<Item_Maincell> Item_MaincellList
        {
            get { return item_MaincellList; }
            set { item_MaincellList = value; }
        }

        private Dictionary<string,List<DropPerceptionInfo>> eventDropPerceptionDic=new Dictionary<string,List<DropPerceptionInfo>>();
        public Dictionary<string, List<DropPerceptionInfo>> EventDropPerceptionDic
        {
            get { return eventDropPerceptionDic; }
            set { eventDropPerceptionDic = value; }
        }

        private Dictionary<string, ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo> reasonClassifyInfoDic = new Dictionary<string, ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo>();
        public Dictionary<string, ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo> ReasonClassifyInfoDic
        {
            get { return reasonClassifyInfoDic; }
            set { reasonClassifyInfoDic = value; }
        }

        private Dictionary<string, Dictionary<string,ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo>> weakQualReasonInfo = new Dictionary<string, Dictionary<string,ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo>>();
        public Dictionary<string, Dictionary<string, ZTDIYWeakQualAnaByRegion.ReasonClassifyInfo>> WeakQualReasonInfo
        {
            get { return weakQualReasonInfo; }
            set { weakQualReasonInfo = value; }
        }

        private Dictionary<MasterCom.RAMS.Net.ZTDIYWeakQualAnaByRegion.CellSub, ZTDIYWeakQualAnaByRegion.WeakQualCellInfo> cellWeakQualCellInfoDic = new Dictionary<MasterCom.RAMS.Net.ZTDIYWeakQualAnaByRegion.CellSub, ZTDIYWeakQualAnaByRegion.WeakQualCellInfo>();
        public Dictionary<MasterCom.RAMS.Net.ZTDIYWeakQualAnaByRegion.CellSub, ZTDIYWeakQualAnaByRegion.WeakQualCellInfo> CellWeakQualCellInfoDic
        {
            get { return cellWeakQualCellInfoDic; }
            set { cellWeakQualCellInfoDic = value; }
        }

        private List<ZTGsmAntenna.CellAngleData> listCellAngleData = new List<ZTGsmAntenna.CellAngleData>();
        public List<ZTGsmAntenna.CellAngleData> ListCellAngleData
        {
            get { return listCellAngleData; }
            set { listCellAngleData = value; }
        }

        private List<ZTTdAntenna.CellAngleData> listCellTdAngelData = new List<ZTTdAntenna.CellAngleData>();
        public List<ZTTdAntenna.CellAngleData> ListCellTdAngelData
        {
            get { return listCellTdAngelData; }
            set { listCellTdAngelData = value; }
        }

        private List<ZTLteAntMR.CellParaData> listCellLteMRData = new List<ZTLteAntMR.CellParaData>();
        public List<ZTLteAntMR.CellParaData> ListCellLteMRData
        {
            get { return listCellLteMRData; }
            set { listCellLteMRData = value; }
        }

        public List<MasterCom.RAMS.NewBlackBlock.BlackBlockItem> CurBlackBlockList
        {
            get { return blackBlockList; }
            set { blackBlockList = value; }
        }


        public List<MasterCom.RAMS.NewBlackBlock.BlockGrid> CurBlackBlockGrids
        {
            get { return blackBlockGrids; }
            set { blackBlockGrids = value; }
        }
        public string CurSelBlackBlockGridKey
        {
            get { return selBlackBlockGridKey; }
            set { selBlackBlockGridKey = value; }
        }
        public MasterCom.RAMS.NewBlackBlock.BlockQueryCond BlackBlockCond
        {
            get { return blackBlockCond; }
            set { blackBlockCond = value; }
        }
        public List<EventBlock> CurEventBlockList
        {
            get { return eventBlockList; }
            set { eventBlockList = value; }
        }
   
        public List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint> WeakQualReasonTpList
        {
            get { return weakQualReasonTpList; }
            set { weakQualReasonTpList = value; }
        }

        public List<NoMainCellBlock> CurNoMainCellBlockList
        {
            get { return noMainCellBlockList; }
            set { noMainCellBlockList = value; }
        }
        public List<List<RoadWarningEntity>> _道路预警集合
        {
            get { return 道路预警集合; }
            set { 道路预警集合 = value; }
        }

        public List<RoadWarningEntity> RoadWarningList
        {
            get { return roadWarningList; }
            set { roadWarningList = value; }
        }
        
        public Dictionary<int, List<GsmSampleStat>> LastWeakRoadDic
        {
            get { return lastWeakRoadDic; }
            set { lastWeakRoadDic = value; }
        }

        public Dictionary<int, Dictionary<int, List<MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna.No1Info>>> GridRoadNo1ListDic
        {
            get { return gridRoadNo1ListDic; }
            set { gridRoadNo1ListDic = value; }
        }


        public int NextNo
        {
            get { return nextNo; }
            set { nextNo = value; }
        }

        public List<GridRoadInfo> GridRoadInfoList
        {
            get { return gridRoadInfoList; }
            set { gridRoadInfoList = value; }
        }

        public Dictionary<int, GridRoadInfo> GridRoadInfoDic
        {
            get { return gridRoadInfoDic; }
            set { gridRoadInfoDic = value; }
        }


        public List<LastWeakStatis> LastweakstatisList
        {
            get { return lastweakstatisList; }
            set { lastweakstatisList = value; }
        }

        public List<LastWeakProportion> LastweakproTDList
        {
            get { return lastweakproTDList; }
            set { lastweakproTDList = value; }
        }

        public List<LastWeakStatis> LastweakstatisTDList
        {
            get { return lastweakstatisTDList; }
            set { lastweakstatisTDList = value; }
        }

        public List<LastWeakProportion> LastweakproList
        {
            get { return lastweakproList; }
            set { lastweakproList = value; }
        }

        public List<GridRoadDataInfo> GridRoadDataInfoList
        {
            get { return gridRoadDataInfoList; }
            set { gridRoadDataInfoList = value; }
        }

        public Dictionary<string, List<LastWeakSecSample>> LastweaksecSample
        {
            get { return lastweaksecSample; }
            set { lastweaksecSample = value; }
        }

        public Dictionary<int, GridRoadDataInfo> GridRoadDataInfoDic
        {
            get { return gridRoadDataInfoDic; }
            set { gridRoadDataInfoDic = value; }
        }

        public Dictionary<int, double> DistanceDic
        {
            get { return distanceDic; }
            set { distanceDic = value; }
        }

        public Dictionary<int, List<EdgeSampleStat>> LastWeakRoadDataDic
        {
            get { return lastWeakRoadDataDic; }
            set { lastWeakRoadDataDic = value; }
        }

        public List<CellSplitManager> CellSplitManagerList
        {
            get { return cellSplitManagerList; }
            set { cellSplitManagerList = value; }
        }

        public Dictionary<string, List<MTGridRoadInfo>> MtGridRoadinfoDic
        {
            get { return mtGridRoadinfoDic; }
            set { mtGridRoadinfoDic = value; }
        }

        public Dictionary<string, List<PoorRoadCustomManager>> PoorRoadCustomDic
        {
            get { return poorRoadCustomDic; }
            set { poorRoadCustomDic = value; }
        }


        public Dictionary<string, List<MTGridRoadInfo>> MtGridRoadinfoDicDraw
        {
            get { return mtGridRoadinfoDicDraw; }
            set { mtGridRoadinfoDicDraw = value; }
        }

        public Dictionary<string, List<MustTestRoadManager>> MtGridRoadinfoDicNewDraw
        {
            get { return mtGridRoadinfoDicNewDraw; }
            set { mtGridRoadinfoDicNewDraw = value; }
        }

        public List<MustTestRoadDesc> MusttestroaddescList_select
        {
            get { return musttestroaddescList_select; }
            set { musttestroaddescList_select = value; }
        }

        public string TitleName
        {
            get { return titleName; }
            set { titleName = value; }
        }

        public List<CellSplitNewManager> CellSplitNewManagerList
        {
            get { return cellSplitNewManagerList; }
            set { cellSplitNewManagerList = value; }
        }

        public List<JoinHandoverAnalysis> HandoverAnalysisList
        {
            get { return handoverAnalysisList; }
            set { handoverAnalysisList = value; }
        }
      

        public List<ClusterInfoMR> ClusterinfoList
        {
            get { return clusterinfoList; }
            set { clusterinfoList = value; }
        }

        public List<PerformanceItem> PerformanceItemList
        {
            get { return performanceItemList; }
            set { performanceItemList = value; }
        }

        public List<CellCoverageItem> CellcoverageitemList
        {
            get { return cellcoverageitemList; }
            set { cellcoverageitemList = value; }
        }


        public bool IsCloud
        {
            get { return isCloud; }
            set { isCloud = value; }
        }

        public Dictionary<string, List<CellProblemTest>> CellproblemTestDic
        {
            get { return cellproblemTestDic; }
            set { cellproblemTestDic = value; }
        }


        public Dictionary<int, List<Cell>> CloudCell
        {
            get { return cloudCell; }
            set { cloudCell = value; }
        }



        public List<CellProblem> CellproList
        {
            get { return cellproList; }
            set { cellproList = value; }
        }
        

        public List<PerformanceItem> PerformanceItemListAll
        {
            get { return performanceItemListAll; }
            set { performanceItemListAll = value; }
        }

        public List<ClusterManager> ClusterManagerList
        {
            get { return clusterManagerList; }
            set { clusterManagerList = value; }
        }   

        public Dictionary<string, Color> CellsplitColorDic
        {
            get { return cellsplitColorDic; }
            set { cellsplitColorDic = value; }
        }

        public Dictionary<int, List<TdSampleStat>> LastWeakRoadTdDic
        {
            get { return lastWeakRoadTdDic; }
            set { lastWeakRoadTdDic = value; }
        }

        public Dictionary<int, List<TdSampleStatData>> LastWeakRoadDataTdDic
        {
            get { return lastWeakRoadDataTdDic; }
            set { lastWeakRoadDataTdDic = value; }
        }

        public bool ShowLastWeakRoadFly
        {
            get { return showLastWeakRoadFly; }
            set { showLastWeakRoadFly = value; }
        }

        public bool ShowLastWeakRoadLable
        {
            get { return showLastWeakRoadLable; }
            set { showLastWeakRoadLable = value; }
        }
        
        public List<PilotFrequencyPolluteBlock> CurPilotFrequencyPolluteBlockList
        {
            get { return pilotFrequencyPolluteBlockList; }
            set { pilotFrequencyPolluteBlockList = value; }
        }

        public List<PilotFrequencyPolluteBlock_W> CurPilotFrequencyPolluteBlockList_W
        {
            get { return pilotFrequencyPolluteBlockList_W; }
            set { pilotFrequencyPolluteBlockList_W = value; }
        }

        public List<NRPilotFrequencyPolluteBlock> CurPilotFrequencyPolluteBlockList_NR
        { get; set; } = new List<NRPilotFrequencyPolluteBlock>();

        public LeakOutCell_SCAN CurLeakOutCellSePoints
        {
            get { return leakOutCellSePoints; }
            set { leakOutCellSePoints = value; }
        }

        public List<LeakOutCell_SCAN> LeakOutCell_SCANList
        {
            get { return leakOutCell_SCANList; }
            set { leakOutCell_SCANList = value; }
        }

        public List<MainCellHandOverFileItem> MainCellHandOverFileItemList
        {
            get { return mainCellHandOverFileItemList; }
            set { mainCellHandOverFileItemList = value; }
        }

        public List<LeakOutCell> LeakOutCellList
        {
            get { return leakOutCellList; }
            set { leakOutCellList = value; }
        }

        public List<CellWeakCover_SCAN> CellWeakCover_SCANList
        {
            get { return cellWeakCover_SCANList; }
            set { cellWeakCover_SCANList = value; }
        }

        public List<WeakCoverBlock> CurWeakCoverBlockList
        {
            get { return weakCoverBlockList; }
            set { weakCoverBlockList = value; }
        }
 
        public Dictionary<int, int> ItemNewBlockDic
        {
            get { return itemNewBlockDic; }
            set { itemNewBlockDic = value; }
        }

        public bool HasQueryComplainItem = false;
        
        /// <summary>
        /// 投诉最后记录日期
        /// </summary>
        public int LastComplainDateValue
        {
            get { return lastComplainDateValue; }
            set { lastComplainDateValue = value; }
        }

        public ReportStyle CurSelKPIReportStyle
        {
            get { return curSelKpiRptStyle; }
            set { curSelKpiRptStyle = value; }
        }

        /// <summary>
        /// for 一般CQT地点的呈现
        /// </summary>
        public List<CQTAddressItem> CurCQTPointList
        {
            get { return cqtAddrPointList; }
            set { cqtAddrPointList = value; }
        }

        public List<CQTTypeInfoItem> CQTTypeInfoList
        {
            get { return cqtTypeInfoList; }
            set { cqtTypeInfoList = value; }
        }
        public Image CQTShowImg
        {
            set { cqtShowImg = value; }
            get { return cqtShowImg; }
        }
        public Image CQTPlanImg
        {
            get { return cqtPlanImg; }
            set { cqtPlanImg = value; }
        }
        public DbPoint CQTPlanImgLTPos
        {
            get { return cqtPlanImgLTPos; }
            set { cqtPlanImgLTPos = value; }
        }
        public double CQTPlanIMGScale
        {
            get { return cqtPlanImgScale; }
            set { cqtPlanImgScale = value; }
        }
        private DbPoint cqtPlanImgBRPos=new DbPoint(0,0);//CQT平面图右下角坐标
        public DbPoint CQTPlanImgBRPos
        {
            get { return cqtPlanImgBRPos; }
            set { cqtPlanImgBRPos = value; }
        }

        private int cqtPlanImgOpaqueValue = 255;
        public int CQTPlanImgOpaqueValue
        {
            get { return cqtPlanImgOpaqueValue; }
            set { cqtPlanImgOpaqueValue = value; }
        }

        public enum OriginType
        {
            LeftTop=1,
            LeftBottom,
            RightTop,
            RightBottom,
        }
        private OriginType cqtPlanImgOrigin = OriginType.LeftBottom;//初始设左下角为坐标原点
        public OriginType CQTPlanImgOrigin
        {
            get { return cqtPlanImgOrigin; }
            set { cqtPlanImgOrigin = value; }
        }
        public MapWinGIS.Shape LastSearchGeometry
        {
            get { return lastSearchGeometry; }
            set { lastSearchGeometry = value; }
        }

        public void FireCompareGridCoverQueried(object sender)
        {
            if(this.gridCPCoverInfoListYD==null || this.gridCPCoverInfoListLT==null)
            {
                return;
            }
            if(CPGridCoverChanged!=null)
            {
                CPGridCoverChanged(sender, null);
            }
        }
        public void FireIdleMatrixQueried(object sender)
        {
            if (this.IdleNeibScanLostMatrix == null)
            {
                return;
            }
            if (IdleNeibLostChanged != null)
            {
                IdleNeibLostChanged(sender, null);
            }
        }
        public void FireCompareHisGridQueried(object sender)
        {
            if (this.compUnitHisList == null)
            {
                return;
            }
            MainForm.GetMapForm().FireAddCompHisLayer();
            if (CPHisGridChanged != null)
            {
                CPHisGridChanged(sender, null);
            }
        }
        public void FireCompareHisGridNewQueried(object sender)
        {
            if (this.curCPDataInfoList == null
                || this.curCPDataTypeDic == null)
            {
                return;
            }
            MainForm.GetMapForm().FireAddCompHisLayer();
            if (CPHisGridNewChanged != null)
            {
                CPHisGridNewChanged(sender, null);
            }
        }
        public void FireEventResultQueried(object sender)
        {
            if(this.evtResultList==null)
            {
                return;
            }
            if(EventResultChanged !=null)
            {
                EventResultChanged(sender, null);
            }
        }

        public void FireGridCoverQueried(object sender)
        {
            if (GridCoverChanged != null)
            {
                GridCoverChanged(sender, null);
            }
        }
        public void FireStreetInjectQueried(object sender)
        {
            if (StreetInjectChanged != null)
            {
                StreetInjectChanged(sender, null);
            }
        }
        public void FireStreetInjectDetailQueried(object sender)
        {
            if (StreetInjectDetailChanged != null)
            {
                StreetInjectDetailChanged(sender, null);
            }
        }
        public void FireLastRoadCovQueried(object sender)
        {
            if (LastRoadCovQueried != null)
            {
                LastRoadCovQueried(sender, null);
            }
        }

        public void FireLastRoadBackCovQueried(object sender)
        {
            if (LastRoadBackCovQueried != null)
            {
                LastRoadBackCovQueried(sender, null);
            }
        }

        public event EventHandler GridCoverChanged;
        public event EventHandler CPGridCoverChanged;
        public event EventHandler CPHisGridChanged;
        public event EventHandler CPHisGridNewChanged;
        public event EventHandler EventResultChanged;
        public event EventHandler StreetInjectChanged;
        public event EventHandler StreetInjectDetailChanged;
        public event EventHandler LastRoadCovQueried;
        public event EventHandler LastRoadBackCovQueried;
        public event EventHandler CellSimuChanged;
        public event EventHandler IdleNeibLostChanged;

        public void FireStatQueried(object sender, ReportStyle curSelStyle, string formText)
        {
            if (this.curChinaMobileStatReportData == null && this.curChinaUnicomStatReportData == null && this.curChinaTelecomStatReportData == null)
            {
                return;
            }

            int count = 0; //记录最大的次数
            if (curChinaMobileStatReportData != null)
            {
                int cmCount = curChinaMobileStatReportData.periodList.Count;
                if (cmCount > count)
                {
                    count = cmCount;
                }
            }
            if (curChinaUnicomStatReportData != null)
            {
                int cuCount = curChinaUnicomStatReportData.periodList.Count;
                if (cuCount > count)
                {
                    count = cuCount;
                }
            }
            if (curChinaTelecomStatReportData != null)
            {
                int ctCount = curChinaTelecomStatReportData.periodList.Count;
                if (ctCount > count)
                {
                    count = ctCount;
                }
            }
            ActionCreateChildFrame action = new ActionCreateChildFrame();
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = mainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Stat.StatReportDockForm";
            actionParam["Text"] = "统计结果显示";
            if (!formText.Trim().Equals(""))
            {
                actionParam["Text"] += "(" + formText.Trim() + ")";
            }
            actionParam["ImageFilePath"] = @"images\stat.gif";
            action.Param = actionParam;
            this.CurSelKPIReportStyle = curSelStyle;
            action.OnAction();
            StatReportDockForm statForm = action.CreatedForm as StatReportDockForm;
            statForm.ClearContents();
            if (sender is QueryBase)
            {
                QueryBase query = sender as QueryBase;
                statForm.LogItemSrc = query.GetCurFuncLogItem();
            }

            DataUnitAreaKPIQuery cmQuery = null;
            DataUnitAreaKPIQuery cuQuery = null;
            DataUnitAreaKPIQuery ctQuery = null;

            DataUnitAreaKPIQuery cmQueryMo = null;
            DataUnitAreaKPIQuery cuQueryMo = null;
            DataUnitAreaKPIQuery ctQueryMo = null;

            DataUnitAreaKPIQuery cmQueryMt = null;
            DataUnitAreaKPIQuery cuQueryMt = null;
            DataUnitAreaKPIQuery ctQueryMt = null;

            for (int i = 0; i < count; i++)
            {
                if (curChinaMobileStatReportData != null)
                {
                    int cmCount = curChinaMobileStatReportData.periodList.Count;
                    if (i < cmCount)
                    {
                        if (cmQuery == null)
                        {
                            cmQuery = new DataUnitAreaKPIQuery();
                        }
                        cmQuery.addStatData(curChinaMobileStatReportData.periodList[i]);
                    }
                }
                if (curChinaUnicomStatReportData != null)
                {
                    int cuCount = curChinaUnicomStatReportData.periodList.Count;
                    if (i < cuCount)
                    {
                        if (cuQuery == null)
                        {
                            cuQuery = new DataUnitAreaKPIQuery();
                        }
                        cuQuery.addStatData(curChinaUnicomStatReportData.periodList[i]);
                    }
                }
                if (curChinaTelecomStatReportData != null)
                {
                    int ctCount = curChinaTelecomStatReportData.periodList.Count;
                    if (i < ctCount)
                    {
                        if (ctQuery == null)
                        {
                            ctQuery = new DataUnitAreaKPIQuery();
                        }
                        ctQuery.addStatData(curChinaTelecomStatReportData.periodList[i]);
                    }
                }

                if (curChinaMobileStatReportDataMo != null)
                {
                    int cmCount = curChinaMobileStatReportDataMo.periodList.Count;
                    if (i < cmCount)
                    {
                        if (cmQueryMo == null)
                        {
                            cmQueryMo = new DataUnitAreaKPIQuery();
                        }
                        cmQueryMo.addStatData(curChinaMobileStatReportDataMo.periodList[i]);
                    }
                }
                if (curChinaUnicomStatReportDataMo != null)
                {
                    int cuCount = curChinaUnicomStatReportDataMo.periodList.Count;
                    if (i < cuCount)
                    {
                        if (cuQueryMo == null)
                        {
                            cuQueryMo = new DataUnitAreaKPIQuery();
                        }

                        cuQueryMo.addStatData(curChinaUnicomStatReportDataMo.periodList[i]);
                    }
                }
                if (curChinaTelecomStatReportDataMo != null)
                {
                    int ctCount = curChinaTelecomStatReportDataMo.periodList.Count;
                    if (i < ctCount)
                    {
                        if (ctQueryMo == null)
                        {
                            ctQueryMo = new DataUnitAreaKPIQuery();
                        }
                        ctQueryMo.addStatData(curChinaTelecomStatReportDataMo.periodList[i]);
                    }
                }

                if (curChinaMobileStatReportDataMt != null)
                {
                    int cmCount = curChinaMobileStatReportDataMt.periodList.Count;
                    if (i < cmCount)
                    {
                        if (cmQueryMt == null)
                        {
                            cmQueryMt = new DataUnitAreaKPIQuery();
                        }
                        cmQueryMt.addStatData(curChinaMobileStatReportDataMt.periodList[i]);
                    }
                }
                if (curChinaUnicomStatReportDataMt != null)
                {
                    int cuCount = curChinaUnicomStatReportDataMt.periodList.Count;
                    if (i < cuCount)
                    {
                        if (cuQueryMt == null)
                        {
                            cuQueryMt = new DataUnitAreaKPIQuery();
                        }
                        cuQueryMt.addStatData(curChinaUnicomStatReportDataMt.periodList[i]);
                    }
                }
                if (curChinaTelecomStatReportDataMt != null)
                {
                    int ctCount = curChinaTelecomStatReportDataMt.periodList.Count;
                    if (i < ctCount)
                    {
                        if (ctQueryMt == null)
                        {
                            ctQueryMt = new DataUnitAreaKPIQuery();
                        }
                        ctQueryMt.addStatData(curChinaTelecomStatReportDataMt.periodList[i]);
                    }
                }
            }

            statForm.StatQueried(cmQuery, cuQuery, ctQuery,curSelStyle,
                cmQueryMo, cuQueryMo, ctQueryMo,
                cmQueryMt, cuQueryMt, ctQueryMt);
        }
      
        public void FireCellStatQueried(object sender, ReportStyle curSelStyle, Dictionary<object, Dictionary <DbPoint,string>> cellDic, bool model2D)
        {
            if (this.curChinaMobileStatReportDataList == null && this.curChinaUnicomStatReportDataList == null && this.curChinaTelecomStatReportDataList == null)
            {
                return;
            }
            List<DataUnitAreaKPIQuery> cmQueryList = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> cuQueryList = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> ctQueryList = new List<DataUnitAreaKPIQuery>();

            List<DataUnitAreaKPIQuery> cmQueryListMo = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> cuQueryListMo = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> ctQueryListMo = new List<DataUnitAreaKPIQuery>();

            List<DataUnitAreaKPIQuery> cmQueryListMt = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> cuQueryListMt = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> ctQueryListMt = new List<DataUnitAreaKPIQuery>();

            int dataCount = 0;
            int cmDataCount = 0;
            int cuDataCount = 0;
            int ctDataCount = 0;
            if (curChinaMobileStatReportDataList != null)
            {
                cmDataCount = curChinaMobileStatReportDataList.Count;
                if (dataCount < cmDataCount)
                {
                    dataCount = cmDataCount;
                }
            }
            if (curChinaUnicomStatReportDataList != null)
            {
                cuDataCount = curChinaUnicomStatReportDataList.Count;
                if (dataCount < cuDataCount)
                {
                    dataCount = cuDataCount;
                }
            }
            if (curChinaTelecomStatReportDataList != null)
            {
                ctDataCount = curChinaTelecomStatReportDataList.Count;
                if (dataCount < ctDataCount)
                {
                    dataCount = ctDataCount;
                }
            }
            for (int j = 0; j < dataCount; j++)
            {
                StatReportData chinaMobileStatReportData = null;
                StatReportData chinaUnicomStatReportData = null;
                StatReportData chinaTelecomStatReportData = null;

                StatReportData chinaMobileStatReportDataMo = null;
                StatReportData chinaUnicomStatReportDataMo = null;
                StatReportData chinaTelecomStatReportDataMo = null;

                StatReportData chinaMobileStatReportDataMt = null;
                StatReportData chinaUnicomStatReportDataMt = null;
                StatReportData chinaTelecomStatReportDataMt = null;

                if (j < cmDataCount)
                {
                    chinaMobileStatReportData = curChinaMobileStatReportDataList[j];
                    //chinaMobileStatReportDataMo = curChinaMobileStatReportDataListMo[j];
                    //chinaMobileStatReportDataMt = curChinaMobileStatReportDataListMt[j];
                }
                if (j < cuDataCount)
                {
                    chinaUnicomStatReportData = curChinaUnicomStatReportDataList[j];
                    //chinaUnicomStatReportDataMo = curChinaUnicomStatReportDataListMo[j];
                    //chinaUnicomStatReportDataMt = curChinaUnicomStatReportDataListMt[j];
                }
                if (j < ctDataCount)
                {
                    chinaTelecomStatReportData = curChinaTelecomStatReportDataList[j];
                    //chinaTelecomStatReportDataMo = curChinaTelecomStatReportDataListMo[j];
                    //chinaTelecomStatReportDataMt = curChinaTelecomStatReportDataListMt[j];
                }

                int count = 0; //记录最大的次数
                int cmCount = 0;
                int cuCount = 0;
                int ctCount = 0;
                if (chinaMobileStatReportData != null)
                {
                    cmCount = chinaMobileStatReportData.periodList.Count;
                    if (cmCount > count)
                    {
                        count = cmCount;
                    }
                }
                if (chinaUnicomStatReportData != null)
                {
                    cuCount = chinaUnicomStatReportData.periodList.Count;
                    if (cuCount > count)
                    {
                        count = cuCount;
                    }
                }
                if (chinaTelecomStatReportData != null)
                {
                    ctCount = chinaTelecomStatReportData.periodList.Count;
                    if (ctCount > count)
                    {
                        count = ctCount;
                    }
                }

                DataUnitAreaKPIQuery cmQuery = null;
                DataUnitAreaKPIQuery cuQuery = null;
                DataUnitAreaKPIQuery ctQuery = null;

                DataUnitAreaKPIQuery cmQueryMo = null;
                DataUnitAreaKPIQuery cuQueryMo = null;
                DataUnitAreaKPIQuery ctQueryMo = null;

                DataUnitAreaKPIQuery cmQueryMt = null;
                DataUnitAreaKPIQuery cuQueryMt = null;
                DataUnitAreaKPIQuery ctQueryMt = null;

                for (int i = 0; i < count; i++)
                {
                    if (chinaMobileStatReportData != null)
                    {
                        if (i < cmCount)
                        {
                            if (cmQuery == null)
                            {
                                cmQuery = chinaMobileStatReportData.periodList[i];
                                //cmQueryMo = chinaMobileStatReportDataMo.periodList[i];
                                //cmQueryMt = chinaMobileStatReportDataMt.periodList[i];
                            }
                            else
                            {
                                cmQuery.addStatData(chinaMobileStatReportData.periodList[i]);
                                //cmQueryMo.addStatData(chinaMobileStatReportDataMo.periodList[i]);
                                //cmQueryMt.addStatData(chinaMobileStatReportDataMt.periodList[i]);
                            }
                        }
                    }
                    if (chinaUnicomStatReportData != null)
                    {
                        if (i < cuCount)
                        {
                            if (cuQuery == null)
                            {
                                cuQuery = chinaUnicomStatReportData.periodList[i];
                                //cuQueryMo = chinaUnicomStatReportDataMo.periodList[i];
                                //cuQueryMt = chinaUnicomStatReportDataMt.periodList[i];
                            }
                            else
                            {
                                cuQuery.addStatData(chinaUnicomStatReportData.periodList[i]);
                                //cuQueryMo.addStatData(chinaUnicomStatReportDataMo.periodList[i]);
                                //cuQueryMt.addStatData(chinaUnicomStatReportDataMt.periodList[i]);
                            }
                        }
                    }
                    if (chinaTelecomStatReportData != null)
                    {
                        if (i < ctCount)
                        {
                            if (ctQuery == null)
                            {
                                ctQuery = chinaTelecomStatReportData.periodList[i];
                                //ctQueryMo = chinaTelecomStatReportDataMo.periodList[i];
                                //ctQueryMt = chinaTelecomStatReportDataMt.periodList[i];
                            }
                            else
                            {
                                ctQuery.addStatData(chinaTelecomStatReportData.periodList[i]);
                                //ctQueryMo.addStatData(chinaTelecomStatReportDataMo.periodList[i]);
                                //ctQueryMt.addStatData(chinaTelecomStatReportDataMt.periodList[i]);
                            }
                        }
                    }
                }
                if (cmQuery != null)
                {
                    cmQueryList.Add(cmQuery);
                }
                if (cuQuery != null)
                {
                    cuQueryList.Add(cuQuery);
                }
                if (ctQuery != null)
                {
                    ctQueryList.Add(ctQuery);
                }

                if (cmQueryMo != null)
                {
                    cmQueryListMo.Add(cmQueryMo);
                }
                if (cuQueryMo != null)
                {
                    cuQueryListMo.Add(cuQueryMo);
                }
                if (ctQueryMo != null)
                {
                    ctQueryListMo.Add(ctQueryMo);
                }

                if (cmQueryMt != null)
                {
                    cmQueryListMt.Add(cmQueryMt);
                }
                if (cuQueryMt != null)
                {
                    cuQueryListMt.Add(cuQueryMt);
                }
                if (ctQuery != null)
                {
                    ctQueryListMt.Add(ctQueryMt);
                }
            }

            if (model2D)
            {
                //MainForm.FireStatReportDockForm_B(cmQueryList, cuQueryList, ctQueryList, cellDic, curSelStyle);
            }
            else
            {
                ActionCreateChildFrame action = new ActionCreateChildFrame();
                Dictionary<string, object> actionParam = new Dictionary<string, object>();
                actionParam["MainForm"] = mainForm;
                actionParam["AssemblyName"] = "RAMS.exe";
                actionParam["TypeName"] = "MasterCom.RAMS.Stat.StatReportDockForm";
                actionParam["Text"] = "统计结果显示";
                actionParam["ImageFilePath"] = @"images\stat.gif";
                action.Param = actionParam;
                this.CurSelKPIReportStyle = curSelStyle;
                action.OnAction();
                StatReportDockForm statForm = action.CreatedForm as StatReportDockForm;
                statForm.ClearContents();
                if (sender is QueryBase)
                {
                    QueryBase query = sender as QueryBase;
                    statForm.LogItemSrc = query.GetCurFuncLogItem();
                }
                statForm.StatQueried(cmQueryList, cuQueryList, ctQueryList, cellDic, curSelStyle);
            }
        }
     
        public void FireMultiRegionStatQueried(object sender, ReportStyle curSelStyle, Dictionary<string, MapWinGIS.Shape> regionDic, bool model2D)
        {
            if (this.curChinaMobileStatReportDataList == null && this.curChinaUnicomStatReportDataList == null && this.curChinaTelecomStatReportDataList == null)
            {
                return;
            }
            List<DataUnitAreaKPIQuery> cmQueryList = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> cuQueryList = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> ctQueryList = new List<DataUnitAreaKPIQuery>();

            List<DataUnitAreaKPIQuery> cmQueryListMo = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> cuQueryListMo = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> ctQueryListMo = new List<DataUnitAreaKPIQuery>();

            List<DataUnitAreaKPIQuery> cmQueryListMt = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> cuQueryListMt = new List<DataUnitAreaKPIQuery>();
            List<DataUnitAreaKPIQuery> ctQueryListMt = new List<DataUnitAreaKPIQuery>();

            int dataCount = 0;
            int cmDataCount = 0;
            int cuDataCount = 0;
            int ctDataCount = 0;
            if (curChinaMobileStatReportDataList != null)
            {
                cmDataCount = curChinaMobileStatReportDataList.Count;
                if (dataCount < cmDataCount)
                {
                    dataCount = cmDataCount;
                }
            }
            if (curChinaUnicomStatReportDataList != null)
            {
                cuDataCount = curChinaUnicomStatReportDataList.Count;
                if (dataCount < cuDataCount)
                {
                    dataCount = cuDataCount;
                }
            }
            if (curChinaTelecomStatReportDataList != null)
            {
                ctDataCount = curChinaTelecomStatReportDataList.Count;
                if (dataCount < ctDataCount)
                {
                    dataCount = ctDataCount;
                }
            }
            for (int j = 0; j < dataCount; j++)
            {
                StatReportData chinaMobileStatReportData = null;
                StatReportData chinaUnicomStatReportData = null;
                StatReportData chinaTelecomStatReportData = null;
                
                StatReportData chinaMobileStatReportDataMo = null;
                StatReportData chinaUnicomStatReportDataMo = null;
                StatReportData chinaTelecomStatReportDataMo = null;

                StatReportData chinaMobileStatReportDataMt = null;
                StatReportData chinaUnicomStatReportDataMt = null;
                StatReportData chinaTelecomStatReportDataMt = null;

                if (j < cmDataCount)
                {
                    chinaMobileStatReportData = curChinaMobileStatReportDataList[j];
                    if (j < curChinaMobileStatReportDataListMo.Count)
                    {
                        chinaMobileStatReportDataMo = curChinaMobileStatReportDataListMo[j];
                        chinaMobileStatReportDataMt = curChinaMobileStatReportDataListMt[j];
                    }
                }
                if (j < cuDataCount)
                {
                    chinaUnicomStatReportData = curChinaUnicomStatReportDataList[j];
                    if (j < curChinaUnicomStatReportDataListMo.Count)
                    {
                        chinaUnicomStatReportDataMo = curChinaUnicomStatReportDataListMo[j];
                        chinaUnicomStatReportDataMt = curChinaUnicomStatReportDataListMt[j];
                    }
               }
                if (j < ctDataCount)
                {
                    chinaTelecomStatReportData = curChinaTelecomStatReportDataList[j];
                    if (j < curChinaTelecomStatReportDataListMo.Count)
                    {
                        chinaTelecomStatReportDataMo = curChinaTelecomStatReportDataListMo[j];
                        chinaTelecomStatReportDataMt = curChinaTelecomStatReportDataListMt[j];
                    }
               }

                int count = 0; //记录最大的次数
                int cmCount = 0;
                int cuCount = 0;
                int ctCount = 0;
                if (chinaMobileStatReportData != null)
                {
                    cmCount = chinaMobileStatReportData.periodList.Count;
                    if (cmCount > count)
                    {
                        count = cmCount;
                    }
                }
                if (chinaUnicomStatReportData != null)
                {
                    cuCount = chinaUnicomStatReportData.periodList.Count;
                    if (cuCount > count)
                    {
                        count = cuCount;
                    }
                }
                if (chinaTelecomStatReportData != null)
                {
                    ctCount = chinaTelecomStatReportData.periodList.Count;
                    if (ctCount > count)
                    {
                        count = ctCount;
                    }
                }

                DataUnitAreaKPIQuery cmQuery = null;
                DataUnitAreaKPIQuery cuQuery = null;
                DataUnitAreaKPIQuery ctQuery = null;

                DataUnitAreaKPIQuery cmQueryMo = null;
                DataUnitAreaKPIQuery cuQueryMo = null;
                DataUnitAreaKPIQuery ctQueryMo = null;

                DataUnitAreaKPIQuery cmQueryMt = null;
                DataUnitAreaKPIQuery cuQueryMt = null;
                DataUnitAreaKPIQuery ctQueryMt = null;

                for (int i = 0; i < count; i++)
                {
                    if (chinaMobileStatReportData != null)
                    {
                        if (i < cmCount)
                        {
                            if (cmQuery == null)
                            {
                                cmQuery = chinaMobileStatReportData.periodList[i];
                            }
                            else
                            {
                                cmQuery.addStatData(chinaMobileStatReportData.periodList[i]);
                            }
                        }
                    }
                    if (chinaMobileStatReportDataMo != null)
                    {
                        if (i < cmCount)
                        {
                            if (cmQueryMo == null)
                            {
                                cmQueryMo = chinaMobileStatReportDataMo.periodList[i];
                            }
                            else
                            {
                                cmQueryMo.addStatData(chinaMobileStatReportDataMo.periodList[i]);
                            }
                        }
                    }
                    if (chinaMobileStatReportDataMt != null)
                    {
                        if (i < cmCount)
                        {
                            if (cmQueryMt == null)
                            {
                                cmQueryMt = chinaMobileStatReportDataMt.periodList[i];
                            }
                            else
                            {
                                cmQueryMt.addStatData(chinaMobileStatReportDataMt.periodList[i]);
                            }
                        }
                    }
                    if (chinaUnicomStatReportData != null)
                    {
                        if (i < cuCount)
                        {
                            if (cuQuery == null)
                            {
                                cuQuery = chinaUnicomStatReportData.periodList[i];
                            }
                            else
                            {
                                cuQuery.addStatData(chinaUnicomStatReportData.periodList[i]);
                            }
                        }
                    }
                    if (chinaUnicomStatReportDataMo != null)
                    {
                        if (i < cuCount)
                        {
                            if (cuQueryMo == null)
                            {
                                cuQueryMo = chinaUnicomStatReportDataMo.periodList[i];
                            }
                            else
                            {
                                cuQueryMo.addStatData(chinaUnicomStatReportDataMo.periodList[i]);
                            }
                        }
                    }
                    if (chinaUnicomStatReportDataMt != null)
                    {
                        if (i < cuCount)
                        {
                            if (cuQueryMt == null)
                            {
                                cuQueryMt = chinaUnicomStatReportDataMt.periodList[i];
                            }
                            else
                            {
                                cuQueryMt.addStatData(chinaUnicomStatReportDataMt.periodList[i]);
                            }
                        }
                    }
                    if (chinaTelecomStatReportData != null)
                    {
                        if (i < ctCount)
                        {
                            if (ctQuery == null)
                            {
                                ctQuery = chinaTelecomStatReportData.periodList[i];
                            }
                            else
                            {
                                ctQuery.addStatData(chinaTelecomStatReportData.periodList[i]);
                            }
                        }
                    }
                    if (chinaTelecomStatReportDataMo != null)
                    {
                        if (i < ctCount)
                        {
                            if (ctQueryMo == null)
                            {
                                ctQueryMo = chinaTelecomStatReportDataMo.periodList[i];
                            }
                            else
                            {
                                ctQueryMo.addStatData(chinaTelecomStatReportDataMo.periodList[i]);
                            }
                        }
                    }
                    if (chinaTelecomStatReportDataMt != null)
                    {
                        if (i < ctCount)
                        {
                            if (ctQueryMt == null)
                            {
                                ctQueryMt = chinaTelecomStatReportDataMt.periodList[i];
                            }
                            else
                            {
                                ctQueryMt.addStatData(chinaTelecomStatReportDataMt.periodList[i]);
                            }
                        }
                    }
                }
                if (cmQuery != null)
                {
                    cmQueryList.Add(cmQuery);
                }
                if (cuQuery != null)
                {
                    cuQueryList.Add(cuQuery);
                }
                if (ctQuery != null)
                {
                    ctQueryList.Add(ctQuery);
                }

                if (cmQueryMo != null)
                {
                    cmQueryListMo.Add(cmQueryMo);
                }
                if (cuQueryMo != null)
                {
                    cuQueryListMo.Add(cuQueryMo);
                }
                if (ctQueryMo != null)
                {
                    ctQueryListMo.Add(ctQueryMo);
                }

                if (cmQueryMt!= null)
                {
                    cmQueryListMt.Add(cmQueryMt);
                }
                if (cuQueryMt != null)
                {
                    cuQueryListMt.Add(cuQueryMt);
                }
                if (ctQueryMt != null)
                {
                    ctQueryListMt.Add(ctQueryMt);
                }
            }

            if (model2D)
            {
                MainForm.FireStatReportDockForm_B(cmQueryList, cuQueryList, ctQueryList, regionDic, curSelStyle);
            }
            else
            {
                ActionCreateChildFrame action = new ActionCreateChildFrame();
                Dictionary<string, object> actionParam = new Dictionary<string, object>();
                actionParam["MainForm"] = mainForm;
                actionParam["AssemblyName"] = "RAMS.exe";
                actionParam["TypeName"] = "MasterCom.RAMS.Stat.StatReportDockForm";
                actionParam["Text"] = "统计结果显示";
                actionParam["ImageFilePath"] = @"images\stat.gif";
                action.Param = actionParam;
                this.CurSelKPIReportStyle = curSelStyle;
                action.OnAction();
                StatReportDockForm statForm = action.CreatedForm as StatReportDockForm;
                statForm.ClearContents();
                if (sender is QueryBase)
                {
                    QueryBase query = sender as QueryBase;
                    statForm.LogItemSrc = query.GetCurFuncLogItem();
                }
                //mainForm.FireSpeechCodecByRegionForm(cmQueryList, cuQueryList, ctQueryList, regionNameList);
                statForm.StatQueried(cmQueryList, cuQueryList, ctQueryList, regionDic, curSelStyle,
                    cmQueryListMo, cuQueryListMo, ctQueryListMo,
                    cmQueryListMt, cuQueryListMt, ctQueryListMt);
            }
        }

        private void loadConfig()
        {
            XmlConfigFile configFile = new XmlConfigFile(Application.StartupPath + @"\config\App.xml");
            XmlElement configServer = configFile.GetConfig("Server");
            bool isCancelChoose = ChooseConnectionServer(configFile, configServer);//选择服务端的IP和TestPageUrl
            if (isCancelChoose)
            {
                Environment.Exit(0);
            }
#if IPPrefix_DownLoadFile
            if (server.IP != null)
            {
                if (server.IP.StartsWith("10.") || server.IP.StartsWith("172.") || server.IP.StartsWith("192."))
                {
                    bDisableFileDownload = false;
                }
                else
                {
                    bDisableFileDownload = true;
                }
            }
#endif
            XmlElement configUser = configFile.GetConfig("User");
            user.LoginName = configFile.GetItemValue(configUser, "LastUser") as string;
            try
            {
                this.isRememberPW = (bool)configFile.GetItemValue(configUser, "IsRememberPW");

                if (isRememberPW)
                {
                    string pw = configFile.GetItemValue(configUser, "EncodedPW") as string;
                    if (!string.IsNullOrEmpty(pw))
                    {
                        user.Password = DES.Decode(pw);
                    }
                }
            }
            catch (Exception)
            {
            }
            try
            {
                this.isAutoLogin = (bool)configFile.GetItemValue(configUser, "IsAutoLogin");
                this.autoLoginUserDelay = (int)configFile.GetItemValue(configUser, "AutoLoginUserDelay");
            }
            catch (Exception)
            {
            }
            configFile.AddItem(configUser, "LastUser", user.LoginName);
            usedUsers.Clear();
            List<object> list = configFile.GetItemValue(configUser, "UsedUsers") as List<object>;
            if (list != null)
            {
                foreach (object o in list)
                {
                    usedUsers.Add(o as string);
                }
            }
            XmlElement configWorkSpace = configFile.GetConfig("WorkSpace");
            lastWorkSpaceName = configFile.GetItemValue(configWorkSpace, "LastWorkSpace") as string;
            if (lastWorkSpaceName == null)
            {
                lastWorkSpaceName = "Default";
            }
            object vv = configFile.GetItemValue(configWorkSpace, "VerLoadedFlag1");
            if (vv != null && vv is int)
            {
                this.verLoadedFlag1 = (int)vv;
            }
            else
            {
                this.verLoadedFlag1 = 0;
            }
            //======For Map URL
            try
            {
                List<string> mapURLs = new List<string>();
                XmlElement configMap = configFile.GetConfig("Map");
                List<object> listMap = configFile.GetItemValue(configMap, "MapURLs") as List<object>;
                if (listMap != null)
                {
                    foreach (object o in listMap)
                    {
                        mapURLs.Add(o as string);
                    }
                }
                mapURLsList.Clear();
                for (int w = 0; w < mapURLs.Count; w++)
                {
                    mapURLsList.Add(mapURLs[w]);
                }

            }
            catch
            {

            }

            //===end  Map URL

            //======For UsableServers
#if SeverLimitMaxConnect
            try
            {
                ServerOfUsableList.Clear();

                XmlElement configUsableServers = configFile.GetConfig("UsableServers");
                if (configUsableServers != null)
                {
                    foreach (XmlElement xmlItem in configUsableServers.ChildNodes)
                    {
                        Dictionary<string, object> serverDic = configFile.GetItemValue(xmlItem) as Dictionary<string, object>;
                        if (serverDic != null)
                        {
                            Server serverInfo = new Server();
                            serverInfo.Param = serverDic;
                            this.ServerOfUsableList.Add(serverInfo);
                            if (serverInfo.Token == this.Server.Token)
                            {
                                this.Server.MaxConnectCount = serverInfo.MaxConnectCount;
                            }
                        }
                    }
                }
            }
            catch
            {
            }
#endif
            configFile = new XmlConfigFile(Application.StartupPath + @"\config\App.mnc");
            XmlElement item = configFile.GetItem("Menu", "Menu");
            menuConfig = new MenuConfig();
            menuConfig.FillItemValue(configFile, item);
            //read VersionId
            XmlFiles updaterXmlFiles = new XmlFiles(Application.StartupPath + "\\UpdateList.xml");
            try
            {
                string vidStr = updaterXmlFiles.GetNodeValue("//VID");
                int vid = 0;
                if (!int.TryParse(vidStr, out vid))
                {
                    vid = 0;
                }
                versionId = vid;
            }
            catch
            {
                versionId = 1;
            }
        }

        private string lastServerIP = string.Empty;
        private string autoLoginServerIP = string.Empty;
        private int autoLoginServerDelay = 3;
        private int autoLoginUserDelay = 3;
        private bool isRememberPW = true;
        private bool isAutoLogin = false;

        public bool IsRemenberPW
        {
            get { return isRememberPW; }
            set { isRememberPW = value; }
        }
        public bool IsAutoLogin
        {
            get { return isAutoLogin; }
            set { isAutoLogin = value; }
        }
        public int AutoLoginUserDelay
        {
            get { return autoLoginUserDelay; }
            set { autoLoginUserDelay = value; }
        }
        public int AutoLoginServerDelay
        {
            get { return autoLoginServerDelay; }
            set { autoLoginServerDelay = value; }
        }
        public string AutoLoginServerIP
        {
            get { return autoLoginServerIP; }
            set { autoLoginServerIP = value; }
        }

        public void SaveConfig()
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement configServer = configFile.AddConfig("Server");
                for (int i = 0; i < servers.Count; i++)
                {
                    Server svr = servers[i];
                    if (string.IsNullOrEmpty(svr.IP))
                    {
                        continue;
                    }
                    string token = (i == 0 ? string.Empty : (i + 1).ToString());
                    configFile.AddItem(configServer, "IP" + token, svr.IP);
                    configFile.AddItem(configServer, "Port" + token, svr.Port);
                    configFile.AddItem(configServer, "TestPageUrl" + token, svr.TestPageUrl);
                    configFile.AddItem(configServer, "Name" + token, svr.NetName);
                    configFile.AddItem(configServer, "ServerType" + token, svr.ServerType);
                }
                configFile.AddItem(configServer, "LastServerIP", lastServerIP);
                configFile.AddItem(configServer, "AutoLoginServerIP", autoLoginServerIP);
                configFile.AddItem(configServer, "AutoLoginServerDelay", autoLoginServerDelay);
                //=====save Map url
                XmlElement configMap = configFile.AddConfig("Map");
                configFile.AddItem(configMap, "MapURLs", mapURLsList);

                //==end save Map url
                XmlElement configUser = configFile.AddConfig("User");
                configFile.AddItem(configUser, "LastUser", user.LoginName);
                configFile.AddItem(configUser, "IsRememberPW", isRememberPW);
                configFile.AddItem(configUser, "IsAutoLogin", isAutoLogin);
                configFile.AddItem(configUser, "AutoLoginUserDelay", autoLoginUserDelay);
                if (isRememberPW)
                {
                    configFile.AddItem(configUser, "EncodedPW", DES.Encode(user.Password));
                }
                configFile.AddItem(configUser, "UsedUsers", usedUsers);
                XmlElement configWorkSpace = configFile.AddConfig("WorkSpace");
                configFile.AddItem(configWorkSpace, "LastWorkSpace", lastWorkSpaceName);
                configFile.AddItem(configWorkSpace, "VerLoadedFlag1", VerLoadedFlag1);

#if SeverLimitMaxConnect
                XmlElement configUsableServers = configFile.AddConfig("UsableServers");
                foreach (Server serverInfo in ServerOfUsableList)
                {
                    configFile.AddItem(configUsableServers, "ServerInfo", serverInfo.Param);
                }
#endif

                configFile.Save(Application.StartupPath + @"\config\App.xml");
                configFile = new XmlConfigFile();
            }
            catch
            {
            }
        }


        private bool ChooseConnectionServer(XmlConfigFile configFile, XmlElement configServer)//选择服务端的IP和TestPageUrl
        {
            bool isCancelChoose = false;//是否取消选择服务端，true取消，false不取消
            servers.Clear();
            Server svr1 = new Model.Server();
            servers.Add(svr1);
            svr1.IP = configFile.GetItemValue(configServer, "IP") as string;
            svr1.Port = (int)configFile.GetItemValue(configServer, "Port");
            try
            {
                svr1.TestPageUrl = configFile.GetItemValue(configServer, "TestPageUrl") as string;
            }
            catch { }
            try
            {
                svr1.NetName = configFile.GetItemValue(configServer, "Name") as string;
            }
            catch
            { }
            try
            {
                svr1.ServerType = configFile.GetItemValue(configServer, "ServerType") as string;
            }
            catch { }
            for (int i = 2; i <= 20; i++)
            {
                Server svrTmp = new Model.Server();
                try
                {
                    object o = configFile.GetItemValue(configServer, "IP" + i.ToString());
                    if (o != null)
                    {
                        svrTmp.IP = o as string;
                        o = configFile.GetItemValue(configServer, "Port" + i.ToString());
                        if (o != null)
                        {
                            svrTmp.Port = (int)o;
                        }
                        else
                        {//IP2没配置端口号，则用第一个IP的端口号
                            svrTmp.Port = svr1.Port;
                        }
                        servers.Add(svrTmp);
                        svrTmp.TestPageUrl = configFile.GetItemValue(configServer, "TestPageUrl" + i.ToString()) as string;
                        svrTmp.NetName = configFile.GetItemValue(configServer, "Name" + i.ToString()) as string;
                        svrTmp.ServerType = configFile.GetItemValue(configServer, "ServerType" + i.ToString()) as string;
                    }
                }
                catch { }
            }
            try
            {
                lastServerIP = configFile.GetItemValue(configServer, "LastServerIP") as string;
                autoLoginServerIP = configFile.GetItemValue(configServer, "AutoLoginServerIP") as string;
                autoLoginServerDelay = (int)configFile.GetItemValue(configServer, "AutoLoginServerDelay");
            }
            catch { }
            if (servers.Count>1)
            {
#if AutoSelectServer
                if (lastServerIP != null)
                {
                    foreach (Server svr in servers)
                    {
                        if (svr.IP == lastServerIP)
                        {
                            server = svr;
                            break;
                        }
                    }
                    
                }
#endif
                if (server.IP == null)
                {
                    isCancelChoose = popConnectServeSelectionDlg(servers);
                }
                while (!isCancelChoose && !CanConnectToServer(server))
                {
                    this.autoLoginServerIP = string.Empty;
                    isCancelChoose = popConnectServeSelectionDlg(servers);
                }
            }
            else
            {
                server = svr1;
                while (!CanConnectToServer(svr1))
                {
                    DialogResult dRet = MessageBox.Show(this.mainForm, "当前配置的IP连接超时，仍使用当前配置IP连接？（是：使用当前配置IP，否：手工输入IP进行连接）", "提示", MessageBoxButtons.YesNoCancel);
                    if (dRet == DialogResult.Yes)
                    {
                    }
                    else if (dRet == DialogResult.No)
                    {
                        InputServerIPDlg dlg = new InputServerIPDlg();
                        if (dlg.ShowDialog() == DialogResult.OK)
                        {
                            string stringIP = dlg.GetIPString();
                            server.IP = stringIP;
                        }
                        else
                        {
                            isCancelChoose = true;
                            break;
                        }
                    }
                    else
                    {
                        isCancelChoose = true;
                        break;
                    }
                }
            }
            return isCancelChoose;
        }

        /// <summary>
        /// IP协议类型
        /// </summary>
        public IPType IPTypeInfo = IPType.IPV4;

        private bool popConnectServeSelectionDlg(List<Server> servers)
        {
            bool isCancelChoose = true;
            ConnectServeSelectionDlg dlg = new ConnectServeSelectionDlg(servers, this.autoLoginServerIP, this.autoLoginServerDelay);
            if ((this.autoLoginServerDelay == 0 && this.autoLoginServerIP != null && this.autoLoginServerIP != string.Empty)
                || dlg.ShowDialog() == DialogResult.OK)
            {
                this.autoLoginServerIP = dlg.AutoLoginServerIP;
                server = dlg.SelServer;
                lastServerIP = server.IP;
                isCancelChoose = false;
                if (lastServerIP.Contains(":"))
                {
                    IPTypeInfo = IPType.IPV6;
                }
                else
                {
                    IPTypeInfo = IPType.IPV4;
                }
            }
            else
            {
                isCancelChoose = true;
            }
            return isCancelChoose;
        }

        public void AddUsedUser(string userName)
        {
            while (usedUsers.Contains(userName))
            {
                usedUsers.Remove(userName);
            }
            usedUsers.Add(userName);
        }

        public List<string> GetLabel(Cell cell)
        {
            List<string> descriptions = new List<string>();
            string description = "";
            if (this.LocalAdjBcchTch.ContainsKey(cell))
            {
                List<short> list = this.LocalAdjBcchTch[cell];
                if (list.Count > 0)
                {
                    description += (list[0].ToString());
                    for (int i = 1; i < list.Count; i++)
                    {
                        description += ("," + list[i].ToString());
                    }
                }
            }
            if (this.LocalCoBcchTch.ContainsKey(cell))
            {
                List<short> list = this.LocalCoBcchTch[cell];
                if (list.Count > 0)
                {
                    if (this.LocalAdjBcchTch.ContainsKey(cell))
                    {
                        description += ",";
                    }
                    for (int i = 0; i < list.Count-1; i++)
                    {
                        description += ("同" + list[i].ToString()+",");
                    }
                    description += ("同" + list[list.Count - 1].ToString());
                }
            }
            descriptions.Add(description);
            return descriptions;
        }

        public List<string> GetLabel(TDCell cell)
        {
            List<string> descriptions = new List<string>();
            string description = "";
            if (this.coCpiFreq.ContainsKey(cell))
            {
                List<int> list = this.coCpiFreq[cell];
                if (list.Count > 0)
                {
                    for (int i = 0; i < list.Count - 1; i++)
                    {
                        description += ("同" + list[i].ToString() + ",");
                    }
                    description += ("同" + list[list.Count - 1].ToString());
                }
            }
            descriptions.Add(description);
            return descriptions;
        }

        public bool CanConnectToServer(Server svr) //通过服务端获取到版本号来测试是否能连接上服务端
        {
            ClientProxy cp = new ClientProxy(svr.IP, svr.Port);
            int versionId = 0;
            bool ret = false;
            try
            {
                cp.setTimeout(10000,10000);
                cp.SetConnectTimeout(3000);
                ret = cp.RequestVersionID(out versionId);
            }
            catch
            {
            }
            finally
            {
                cp.Close();
            }
            return ret;
        }

        private int versionId = 0;

        [NonSerialized()]
        private MainForm mainForm;

        private Server server = new Server();

        private User user = new User();

        private List<string> usedUsers = new List<string>();
        private List<Server> servers = new List<Server>();
        /// <summary>
        /// 用户自己配置的服务器信息
        /// </summary>
        public List<Server> Servers
        {
            get { return servers; }
        }

        private string lastWorkSpaceName = "";
        public string LastWorkSpaceName //最近打开的工作空间名
        {
            get { return lastWorkSpaceName; }
            set { lastWorkSpaceName = value; }
        }
        /// <summary>
        /// 是否注册了组件 DevExpress
        /// </summary>
        public int VerLoadedFlag1
        {
            get { return verLoadedFlag1; }
            set { verLoadedFlag1 = value; }
        }
        private int verLoadedFlag1 = 0;

        public List<String> MapURLsList
        {
            get
            {
                return mapURLsList;
            }
            set
            {
                mapURLsList = value;
            }
        }
        private List<String> mapURLsList = new List<string>();

        private MenuConfig menuConfig;

        private WorkSpace workSpace;

        private int districtID = -1;

        private CellManager cellManager = CellManager.GetInstance();

        private List<int> fileAgentFilter = new List<int>();

        private List<FileInfo> fileInfos = new List<FileInfo>();

        private FileInfo selectedFileInfo = new FileInfo();

        private DTDataManager dtDataManager ;

        //private StatReportData curStatReportData;

        private List<CExpress> cExpressList = new List<CExpress>();

        private CExpressRet_and cExpressRest_and=new CExpressRet_and();

        private BTS selectedBTS;

        private PlanBTS selectedPlanBTS;

        private Cell selectedCell;

        private TDNodeB selectedTDNodeB;

        private TDCell selectedTDCell;

        private WNodeB selectedWNodeB;

        private WCell selectedWCell;

        private CDNodeB selectedCDNodeB;

        private CDCell selectedCDCell;

        private List<Cell> selectedCells = new List<Cell>();

        private List<TDCell> selectedTDCells = new List<TDCell>();

        private List<WCell> selectedWCells = new List<WCell>();

        private List<CDCell> selectedCDCells = new List<CDCell>();

        private List<LTECell> selectedLTECells = new List<LTECell>();

        private CQTAddressItem selectedCQTAddrItem;

        private List<Cell> serverCells = new List<Cell>(); 

        private List<TDCell> serverTDCells = new List<TDCell>();

        private List<WCell> serverWCells = new List<WCell>();

        private List<CDCell> serverCDCells = new List<CDCell>();

        private List<Cell> coBCCHCells = new List<Cell>();

        public List<Cell> CoBCCHCells
        {
            get { return coBCCHCells; }
        }

        private List<TDCell> coCPITDCells = new List<TDCell>();
        public List<TDCell> CoCPITDCells
        {
            get { return coCPITDCells; }
        }

        private List<Cell> adjBCCHCells = new List<Cell>();
        public List<Cell> AdjBCCHCells
        {
            get { return adjBCCHCells; }
        }

        private List<Cell> coTCHCells = new List<Cell>();
        public List<Cell> CoTCHCells
        {
            get { return coTCHCells; }
        }

        private List<TDCell> coFreqTDCells = new List<TDCell>();
        public List<TDCell> CoFreqTDCells
        {
            get { return coFreqTDCells; }
        }

        private List<Cell> adjTCHCells = new List<Cell>();
        public List<Cell> AdjTCHCells
        {
            get { return adjTCHCells; }
        }

        private List<Cell> coBSICCells = new List<Cell>();
        public List<Cell> CoBSICCells
        {
            get { return coBSICCells; }
        }

        private List<TDCell> coCPIGroupTDCells = new List<TDCell>();
        public List<TDCell> CoCPIGroupTDCells
        {
            get { return coCPIGroupTDCells; }
        }

        private List<Cell> neighbourCells = new List<Cell>();
        public List<Cell> NeighbourCells
        {
            get { return neighbourCells; }
        }

        private List<Cell> neighbourEachOtherCells = new List<Cell>();
        public List<Cell> NeighbourEachOtherCells
        {
            get { return neighbourEachOtherCells; }
        }

        private List<Cell> tdneighbourCells = new List<Cell>();
        public List<Cell> TDNeighbourCells
        {
            get { return tdneighbourCells; }
        }

        private List<TDCell> tdneighbourTDCells = new List<TDCell>();
        public List<TDCell> TDNeighbourTDCells
        {
            get { return tdneighbourTDCells; }
        }

        private List<LTECell> lteneighbourLTECells = new List<LTECell>();
        public List<LTECell> LTENeighbourLTECells
        {
            get { return lteneighbourLTECells; }
        }

        private List<TDCell> lteneighbourTDCells = new List<TDCell>();
        public List<TDCell> LTENeighbourTDCells
        {
            get { return lteneighbourTDCells; }
        }

        private List<Cell> lteneighbourCells = new List<Cell>();
        public List<Cell> LTENeighbourCells
        {
            get { return lteneighbourCells; }
        }

        private List<Cell> wneighbourCells = new List<Cell>();
        public List<Cell> WNeighbourCells
        {
            get { return wneighbourCells; }
        }

        private List<WCell> wneighbourWCells = new List<WCell>();
        public List<WCell> WNeighbourWCells
        {
            get { return wneighbourWCells; }
        }

        private List<CDCell> cdneighbourCDCells = new List<CDCell>();
        public List<CDCell> CDNeighbourCDCells
        {
            get { return cdneighbourCDCells; }
        }

        private List<ScanCellCoverInfo> scanCellCoverInfoList = new List<ScanCellCoverInfo>();
        public List<ScanCellCoverInfo> ScanCellCoverInfoList
        {
            get { return scanCellCoverInfoList; }
            set { scanCellCoverInfoList = value; }
        }

        private List<ScanInterfereResult> scanInterfereResults = null;
        public List<ScanInterfereResult> ScanInterfereResults
        {
            get 
            { 
                if (scanInterfereResults == null)
                {
                    scanInterfereResults = new List<ScanInterfereResult>();
                }
                return scanInterfereResults; 
            }
            set { scanInterfereResults = value; }
        }

        private List<ScanShearResult> scanShearResults = null;
        public List<ScanShearResult> ScanShearResults
        {
            get
            {
                if (scanShearResults == null)
                {
                    scanShearResults = new List<ScanShearResult>();
                }
                return scanShearResults;
            }
            set { scanShearResults = value; }
        }

        private Dictionary<Cell, List<short>> localCoBcchTch = new Dictionary<Cell, List<short>>();
        public Dictionary<Cell, List<short>> LocalCoBcchTch //同频点字典表
        {
            get { return localCoBcchTch; }
            set { localCoBcchTch = value; }
        }

        private Dictionary<TDCell, List<int>> coCpiFreq = new Dictionary<TDCell, List<int>>();
        /// <summary>
        /// 同扰码字典
        /// </summary>
        public Dictionary<TDCell, List<int>> CoCpiFreq
        {
            get { return coCpiFreq; }
            set { coCpiFreq = value; }
        }

        private Dictionary<Cell, List<short>> localAdjBcchTch = new Dictionary<Cell, List<short>>();
        public Dictionary<Cell, List<short>> LocalAdjBcchTch //邻频点字典表
        {
            get { return localAdjBcchTch; }
            set { localAdjBcchTch = value; }
        }

        private List<Interfere> interfereList = new List<Interfere>();
        public List<Interfere> InterfereList
        {
            get { return interfereList; }
            set { interfereList = value; }
        }

        private List<Pen> serverCellPens = new List<Pen>();

        private List<TestPoint> selectedTestPoints = new List<TestPoint>();
        private List<WCDMATestPointDetail> selectedTestPoints_W = new List<WCDMATestPointDetail>();
        private List<Event> selectedEvents = new List<Event>();

        private ReportStyle curSelKpiRptStyle = null;
        private GridMatrix<ColorUnit> gridColorUnitMatrix = null;
        private GridMatrix<GridCellUnit> gridCellUnitMatrix = null;
        private List<GridPartParam> gridCoverInfoList = new List<GridPartParam>();
        private List<GridPartParam> gridCPCoverInfoListYD = null;//竞争对比移动栅格数据
        private List<GridPartParam> gridCPCoverInfoListLT = null;//竞争对比联通栅格数据
        private List<CompUnit> compUnitHisList = null;
        private List<CPDataInfo> curCPDataInfoList = null;
        private Dictionary<int, CPDataType> curCPDataTypeDic = null;
        private List<EventResult> evtResultList = null;
        private List<PlaceRxLev> cellSimuResultList = null;

        private GridMatrix<InjectGridUnit> streetInjectColorMatrix = null;
        private List<StreetInjectInfo> totalInfoResultList = null;
        private List<GridLongLat> gridNonContainLongLatList = null;
        private RxlevGridLongLat rxlevGridLongLat = null;


        

        private List<AreaStreetCarrierInjectInfo> areaStreetCarrierInjectInfos = new List<AreaStreetCarrierInjectInfo>();
        private List<AreaStreetCarrierInjectInfo> areaStreetCarrierInjectInfosCopy = new List<AreaStreetCarrierInjectInfo>();
        private List<CQTAddressItem> cqtAddrPointList = new List<CQTAddressItem>();
        private List<CQTTypeInfoItem> cqtTypeInfoList = new List<CQTTypeInfoItem>();
        private Image cqtShowImg = null;
        private Image cqtPlanImg = null;//CQT平面图
        private DbPoint cqtPlanImgLTPos=new DbPoint(0,0);//CQT平面图左上角坐标
        private double cqtPlanImgScale = 79 / (Math.Sqrt(Math.Pow(500, 2) + Math.Pow(500, 2)) / 96 * 0.0254);
        private List<BlackBlockItem> blackBlockList = new List<BlackBlockItem>();
        public List<BlockGrid> blackBlockGrids = new List<BlockGrid>();
        private string selBlackBlockGridKey = "";
        private MasterCom.RAMS.NewBlackBlock.BlockQueryCond blackBlockCond = new BlockQueryCond();
        private List<EventBlock> eventBlockList = new List<EventBlock>();//事件位置汇聚分析
        private List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint> weakQualReasonTpList = new List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint>(); //2G质差原因
        //===========================
        private List<NoMainCellBlock> noMainCellBlockList = new List<NoMainCellBlock>();//无主导小区分析
        private List<PilotFrequencyPolluteBlock> pilotFrequencyPolluteBlockList = new List<PilotFrequencyPolluteBlock>();//TD导频污染分析
        private List<PilotFrequencyPolluteBlock_W> pilotFrequencyPolluteBlockList_W = new List<PilotFrequencyPolluteBlock_W>();//W导频污染分析
        private LeakOutCell_SCAN leakOutCellSePoints = new LeakOutCell_SCAN();//TD室分外泄小区分析
        private List<LeakOutCell_SCAN> leakOutCell_SCANList = new List<LeakOutCell_SCAN>();
        private List<CellWeakCover_SCAN> cellWeakCover_SCANList = new List<CellWeakCover_SCAN>();
        private List<MainCellHandOverFileItem> mainCellHandOverFileItemList = new List<MainCellHandOverFileItem>();
        
        private List<WeakCoverBlock> weakCoverBlockList = new List<WeakCoverBlock>();//TD扫频弱覆盖分析
        private SampleRangeInfo sampleRangeInfo = new SampleRangeInfo();//TD扫频采样点占比
        private List<MasterCom.RAMS.Model.Interface.DIYMainCovercellChangeTableQueryByRegion.MainCoverInfo> mainCoverInfoList = new List<DIYMainCovercellChangeTableQueryByRegion.MainCoverInfo>();  //TD扫频的小区变更表
        private List<LeakOutCell> leakOutCellList = new List<LeakOutCell>();


        //=============================
        private List<List<RoadWarningEntity>> 道路预警集合 = new List<List<RoadWarningEntity>>();  //道路预警分析
        private List<RoadWarningEntity> roadWarningList = new List<RoadWarningEntity>(); 

        //=============================
        private Dictionary<int, List<GsmSampleStat>> lastWeakRoadDic = new Dictionary<int, List<GsmSampleStat>>(); //持续差路段分析
        private Dictionary<int, List<TdSampleStat>> lastWeakRoadTdDic = new Dictionary<int, List<TdSampleStat>>(); //TD持续差路段分析
        private Dictionary<String, SampleSpeedInfo> sampleSpeedInfo = new Dictionary<String, SampleSpeedInfo>(); //TD速率分析
        private bool showLastWeakRoadFly;

        public int lastWeakRoadiid=-1;

        private Dictionary<int, List<EdgeSampleStat>> lastWeakRoadDataDic = new Dictionary<int, List<EdgeSampleStat>>();
        private Dictionary<int, List<TdSampleStatData>> lastWeakRoadDataTdDic = new Dictionary<int, List<TdSampleStatData>>();

        private List<GridRoadInfo> gridRoadInfoList = new List<GridRoadInfo>();
        private List<GridRoadDataInfo> gridRoadDataInfoList = new List<GridRoadDataInfo>();

        Dictionary<int, Dictionary<int, List<MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna.No1Info>>> gridRoadNo1ListDic = new Dictionary<int, Dictionary<int, List<MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna.No1Info>>>(); //为第一步设计

        private List<LastWeakStatis> lastweakstatisList = new List<LastWeakStatis>();
        private List<LastWeakProportion> lastweakproList = new List<LastWeakProportion>();

        private List<LastWeakStatis> lastweakstatisTDList = new List<LastWeakStatis>();
        private List<LastWeakProportion> lastweakproTDList = new List<LastWeakProportion>();

        private Dictionary<string, List<LastWeakSecSample>> lastweaksecSample = new Dictionary<string, List<LastWeakSecSample>>();


        private Dictionary<int, GridRoadInfo> gridRoadInfoDic = new Dictionary<int, GridRoadInfo>();  //同List数据一致,dic方便匹配
        private Dictionary<int, GridRoadDataInfo> gridRoadDataInfoDic = new Dictionary<int, GridRoadDataInfo>();  //同List数据一致,dic方便匹配

        private bool showLastWeakRoadLable;  //标签显示

       
        


        private int nextNo = 0;   //持续差专题步骤
        private Dictionary<int, double> distanceDic = new Dictionary<int, double>();


        //=============================

        private Dictionary<string, List<MTGridRoadInfo>> mtGridRoadinfoDic = new Dictionary<string, List<MTGridRoadInfo>>();  //必测道路

        private Dictionary<string, List<MTGridRoadInfo>> mtGridRoadinfoDicDraw = new Dictionary<string, List<MTGridRoadInfo>>();  //用于绘图必测道路
        private Dictionary<string, List<MustTestRoadManager>> mtGridRoadinfoDicNewDraw = new Dictionary<string, List<MustTestRoadManager>>();  //绘图必测道路-新结构

        private List<MustTestRoadDesc> musttestroaddescList_select = new List<MustTestRoadDesc>(); //必测道路用户筛选_ GIS联动

       

        //=============================
        //覆盖差道路自定义汇聚
        private Dictionary<string, List<PoorRoadCustomManager>> poorRoadCustomDic = new Dictionary<string, List<PoorRoadCustomManager>>();

      
        //=============================

        private List<CellSplitManager> cellSplitManagerList = new List<CellSplitManager>();  //小区分裂

        private List<CellSplitNewManager> cellSplitNewManagerList = new List<CellSplitNewManager>();  //新小区分裂

        private Dictionary<string, Color> cellsplitColorDic = new Dictionary<string, Color>();
        private string titleName;  //当前指标名称


        //=============================

        private List<ClusterInfoMR> clusterinfoList = new List<ClusterInfoMR>();   //簇优化MR
        private List<ClusterManager> clusterManagerList = new List<ClusterManager>();

         //=============================

        private List<PerformanceItem> performanceItemList = new List<PerformanceItem>();  //性能问题点查询
        private List<PerformanceItem> performanceItemListAll = new List<PerformanceItem>(); //全性能问题点查询

        
        //==============================

        private List<CellCoverageItem> cellcoverageitemList = new List<CellCoverageItem>();
        private List<CellProblem> cellproList = new List<CellProblem>();

        private Dictionary<int, List<Cell>> cloudCell = new Dictionary<int, List<Cell>>();   //小区上一个云~
        private bool isCloud = true;
        private Dictionary<string, List<CellProblemTest>> cellproblemTestDic = new Dictionary<string, List<CellProblemTest>>();  //联合预警

        
        //==============================

        List<JoinHandoverAnalysis> handoverAnalysisList = new List<JoinHandoverAnalysis>(); //联合切换分析

        //==============================
        private MasterCom.RAMS.ZTFunc.conditionRecorder conditionRecorderCellComp = null;
        public MasterCom.RAMS.ZTFunc.conditionRecorder ConditionRecorderCellComp
        {
            get { return conditionRecorderCellComp; }
            set { conditionRecorderCellComp = value; }
        }
        private MasterCom.RAMS.ZTFunc.conditionRecorder conditionRecorderRxlevComp = null;
        public MasterCom.RAMS.ZTFunc.conditionRecorder ConditionRecorderRxlevComp
        {
            get { return conditionRecorderRxlevComp; }
            set { conditionRecorderRxlevComp = value; }
        }
        private MasterCom.RAMS.ZTFunc.conditionRecorder conditionRecorderSCellDeficiencyByRegion = null;
        public MasterCom.RAMS.ZTFunc.conditionRecorder ConditionRecorderSCellDeficiencyByRegion
        {
            get { return conditionRecorderSCellDeficiencyByRegion; }
            set { conditionRecorderSCellDeficiencyByRegion = value; }
        }
        private MasterCom.RAMS.ZTFunc.conditionRecorder conditionRecorderSCellDeficencyByCell = null;
        public MasterCom.RAMS.ZTFunc.conditionRecorder ConditionRecorderSCellDeficencyByCell
        {
            get { return conditionRecorderSCellDeficencyByCell; }
            set { conditionRecorderSCellDeficencyByCell = value; }
        }
        /// <summary>
        /// 公用conditionRecorder
        /// </summary>
        private Func.conditionRecorder conditionRecorderCommon = null;
        public Func.conditionRecorder ConditionRecorderCommon
        {
            get { return conditionRecorderCommon; }
            set { conditionRecorderCommon = value; }
        }


        private List<cellComparisonInfo> cellComparisonInfoList = new List<cellComparisonInfo>();  //小区占用对比
        private SetQueryForm setQueryForm = null;
        public SetQueryForm SetQueryForm
        {
            get { return setQueryForm; }
            set { setQueryForm = value; }
        }

        private List<Item_Maincell> item_MaincellList = new List<Item_Maincell>();    //强小区缺失

        //=============================

        //投诉热点黑点分析
        //深圳
        Dictionary<int, int> itemNewBlockDic = new Dictionary<int, int>();   //<blockId, newBlockId>
        private int lastComplainDateValue;

        //投诉热点黑点分析

        private MapWinGIS.Shape lastSearchGeometry = null;
        private Message selectedMessage;

        //Idle邻区缺失
        private IdleCellsInGrid[,] idleMatrix = null;
        public IdleCellsInGrid[,] IdleNeibScanLostMatrix
        {
            get
            {
                return idleMatrix;
            }
            set
            {
                idleMatrix = value;
            }
        }
        //GSM扫频杂乱小区
        private List<TestPoint> g_SCANTestPoints = new List<TestPoint>();
        public List<TestPoint> G_SCANTestPoints
        {
            get { return g_SCANTestPoints; }
            set { g_SCANTestPoints = value; }
        }

        private VisibleOffsetManager visibleOffsetManager;

        public VisibleOffsetManager VisibleOffsetManager
        {
            get { return visibleOffsetManager; }
        }

        private Antenna selectedAntenna;

        public Antenna SelectedAntenna
        {
            get { return selectedAntenna; }
            set { selectedAntenna = value; }
        }

        private CategoryManager categoryManager = CategoryManager.GetInstance();

        public CategoryManager CategoryManager
        {
            get { return categoryManager; }
        }

        private ProjectManager projectManager = ProjectManager.GetInstance();

        public ProjectManager ProjectManager
        {
            get { return projectManager; }
        }

        private SearchGeometrys searchGeometrys = new SearchGeometrys();
        public SearchGeometrys SearchGeometrys
        {
            get { return searchGeometrys; }
        }

        private bool multiGeometrys = false;
        public bool MultiGeometrys
        {
            get { return multiGeometrys; }
            set { multiGeometrys = value; }
        }

        private bool rootNodeGeometrys = false;
        public bool RootNodeGeometrys
        {
            get { return rootNodeGeometrys; }
            set { rootNodeGeometrys = value; }
        }

        public event EventHandler SearchGeometrysChanged;
        public void FireSearchGeometrysChanged(object sender)
        {
            if (SearchGeometrysChanged != null)
            {
                SearchGeometrysChanged(sender, null);
            }
        }

        private List<SimulationRegionInfo> simulationRegionInfoList = new List<SimulationRegionInfo>();
        public List<SimulationRegionInfo> SimulationRegionInfoList
        {
            get { return simulationRegionInfoList; }
            set { simulationRegionInfoList = value; }
        }

        private List<EventInfoSet> eventInfoSets = new List<EventInfoSet>();

        private string eventSetFileName;

        public List<EventInfoSet> EventInfoSets
        {
            get { return eventInfoSets; }
        }

        public void LoadEventInfoSets(string configFileName)
        {
            eventSetFileName = configFileName;
            if (File.Exists(configFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(configFileName);
                List<object> list = configFile.GetItemValue("Common", "EventSet", EventInfoSet.GetItemValue) as List<object>;
                if (list != null)
                {
                    eventInfoSets.Clear();
                    foreach (object value in list)
                    {
                        eventInfoSets.Add((EventInfoSet)value);
                    }
                }
            }
        }

        public void SaveEventInfoSets()
        {
            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement configCommon = configFile.AddConfig("Common");
            XmlElement configList = configFile.AddItem(configCommon, "EventSet", eventInfoSets, EventInfoSet.AddItem);
            configFile.Save(eventSetFileName);
        }

        public void SaveEventInfoSets(string esFileName)
        {
            this.eventSetFileName = esFileName;
            SaveEventInfoSets();
        }

        private string testPointSampleDiscription = "";

        public string TestPointSampleDiscription
        {
            get { return testPointSampleDiscription; }
        }

        public event EventHandler TestPointSampleChanged;

        public void FireTestPointSampleChanged(bool needSample, int sampleLevel, int actualSampleLevel)
        {
            if (TestPointSampleChanged != null)
            {
                if (needSample)
                {
                    if (sampleLevel >= 1 && sampleLevel <= 15)
                    {
                        testPointSampleDiscription = "固定抽样[级别:" + sampleLevel + "]";
                    }
                    else
                    {
                        testPointSampleDiscription = "自动抽样" + ((actualSampleLevel >= 1 && actualSampleLevel <= 15) ? "[级别:" + actualSampleLevel + "]" : "[不抽样]");
                    }
                }
                else
                {
                    testPointSampleDiscription = "不抽样";
                }
                TestPointSampleChanged(this, null);
            }
        }

        private Dictionary<int, List<string>> regionTableNames = new Dictionary<int, List<string>>();

        /// <summary>
        /// 预存区域信息: （第一部分）图层路径;（第二部分）列名;（第三部分）是否用户定制区域(0为用户定制从user.prcy读取，1为公共的从default.prc读取)
        /// </summary>
        public List<string> RegionTableNames
        {
            get
            {
                if (!regionTableNames.ContainsKey(districtID))
                {
                    regionTableNames[districtID] = new List<string>();
                }
                return regionTableNames[districtID];
            }
        }

        private string regionTableFileName;

        public void LoadRegionTable(string configFileName)
        {
            regionTableNames.Clear();
            regionTableFileName = configFileName;
            if (File.Exists(configFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(regionTableFileName);
                Dictionary<string, object> map = configFile.GetItemValue("Common", "RegionTableName") as Dictionary<string, object>;
                if (map != null)
                {
                    foreach (KeyValuePair<string, object> pair in map)
                    {
                        try
                        {
                            int dbID = int.Parse(pair.Key);
                            List<string> list = new List<string>();
                            regionTableNames[dbID] = list;
                            if (pair.Value != null)
                            {
                                foreach (object o in pair.Value as List<object>)
                                {
                                    list.Add((string)o + ";1");
                                }
                            }
                        }
                        catch
                        {
                        }
                    }
                }
            }
            string userRegionTableFile = Application.StartupPath + @"\userData\user.prc";
            if (File.Exists(userRegionTableFile))
            {
                XmlConfigFile configFile = new XmlConfigFile(userRegionTableFile);
                Dictionary<string, object> map = configFile.GetItemValue("Common", "RegionTableName") as Dictionary<string, object>;
                if (map != null)
                {
                    foreach (KeyValuePair<string, object> pair in map)
                    {
                        try
                        {
                            int dbID = int.Parse(pair.Key);
                            if (regionTableNames.ContainsKey(dbID))
                            {
                                if (pair.Value != null)
                                {
                                    foreach (object o in pair.Value as List<object>)
                                    {
                                        regionTableNames[dbID].Add((string)o + ";0");
                                    }
                                }
                            }
                            else
                            {
                                List<string> list = new List<string>();
                                regionTableNames[dbID] = list;
                                if (pair.Value != null)
                                {
                                    foreach (object o in pair.Value as List<object>)
                                    {
                                        list.Add((string)o + ";0");
                                    }
                                }
                            }                            
                        }
                        catch
                        {
                        }
                    }
                }
            }
        }

        public void SaveRegionTable()
        {
            Dictionary<int, List<string>> userMap = new Dictionary<int,List<string>>();
            Dictionary<int, List<string>> publicMap = new Dictionary<int,List<string>>();

            foreach (int districtID in regionTableNames.Keys)
            {
                foreach (string typeName in regionTableNames[districtID])
                {
                    string[] strs = typeName.Split(';');
                    if (strs.Length == 3)
                    {
                        if (strs[2] == "0")
                        {
                            if (userMap.ContainsKey(districtID))
                            {
                                userMap[districtID].Add(strs[0] + ";" + strs[1]);
                            }
                            else
                            {
                                List<string> list = new List<string>();
                                list.Add(strs[0] + ";" + strs[1]);
                                userMap.Add(districtID, list);
                            }
                        }
                        else if (strs[2] == "1")
                        {
                            if (publicMap.ContainsKey(districtID))
                            {
                                publicMap[districtID].Add(strs[0] + ";" + strs[1]);
                            }
                            else
                            {
                                List<string> list = new List<string>();
                                list.Add(strs[0] + ";" + strs[1]);
                                publicMap.Add(districtID, list);
                            }
                        }
                    }
                }
            }

            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement configCommon = configFile.AddConfig("Common");
            XmlElement configList = configFile.AddItem(configCommon, "RegionTableName", publicMap);
            configFile.Save(regionTableFileName);

            configFile = new XmlConfigFile();
            configCommon = configFile.AddConfig("Common");
            configList = configFile.AddItem(configCommon, "RegionTableName", userMap);
            configFile.Save(Application.StartupPath + @"\userData\user.prc");
        }

        public void SaveRegionTable(string regShpFileName)
        {
            this.regionTableFileName = regShpFileName;
            SaveRegionTable();
        }

        public event EventHandler Link2SZMapCommandFired;

        public void FireLink2SZMapCommandFired()
        {
            if (Link2SZMapCommandFired != null)
            {
                Link2SZMapCommandFired(this, null);
            }
        }

        private Repeater selectedRepeater;

        public Repeater SelectedRepeater
        {
            get { return selectedRepeater; }
            set { selectedRepeater = value; }
        }

        public event EventHandler DtDataMapChanged;

        public void FireDtDataMapChanged(object sender)
        {
            if (DtDataMapChanged != null)
            {
                DtDataMapChanged(sender, null);
            }
        }

        public event EventHandler SelectedRepeaterChanged;

        public void FireSelectedRepeaterChanged(object sender)
        {
            if (SelectedRepeaterChanged != null)
            {
                SelectedRepeaterChanged(sender, null);
            }
        }

        public AreaManager AreaManager
        {
            get { return AreaManager.GetInstance(); }
        }

        public PermissionManager PermissionManager
        {
            get { return PermissionManager.GetInstance(); }
        }

        private string wrongDirSelCellName;

        public string WrongDirSelCellName
        {
            get { return wrongDirSelCellName; }
            set { wrongDirSelCellName = value; }
        }

        private Dictionary<string, ScanJamTotal> jamTotalDic;
        public Dictionary<string, ScanJamTotal> JamTotalDic
        {
            get { return jamTotalDic; }
            set { jamTotalDic = value; }
        }

        private Dictionary<string, List<ScanJamPair>> jamPairDic;

        public Dictionary<string, List<ScanJamPair>> JamPairDic
        {
            get { return jamPairDic; }
            set { jamPairDic = value; }
        }

        private Cell intercell;

        /// <summary>
        /// 扫频过覆盖分析
        /// </summary>
        public Cell Intercell
        {
            get { return intercell; }
            set { intercell = value; }
        }

        private ScanInterSet scanInterSet = new ScanInterSet();
        public ScanInterSet ScanInterSet
        {
            get{ return scanInterSet;}
            set {scanInterSet = value;}
        }

        //=========================================================================================

        private QueryCondition queryCondition = null;

        /// <summary>
        /// 查询条件
        /// </summary>
        public QueryCondition QueryCondition
        {
            get 
            {
                if (queryCondition == null)
                {
                    queryCondition = new QueryCondition();
                }
                return queryCondition; 
            }
            set { queryCondition = value; }
        }

        private DTDataManager orgDTDataManager;

        public DTDataManager OrgDTDataManager
        {
            get { return orgDTDataManager; }
            set { orgDTDataManager = value; }
        }

        private DTDataManager jamDTDataManager;

        public DTDataManager JamDTDataManager
        {
            get { return jamDTDataManager; }
            set { jamDTDataManager = value; }
        }

        private Dictionary<Cell, List<Cell>> jamorgDic = new Dictionary<Cell, List<Cell>>();

        /// <summary>
        /// 以干扰小区作为键，以原小区列表作为值的哈希表
        /// </summary>
        public Dictionary<Cell, List<Cell>> JamOrgDic
        {
            get { return jamorgDic; }
            set { jamorgDic = value; }
        }

        //summary:簇优化
        private ClusterSet clusterAnalysisSet = new ClusterSet();
        public ClusterSet ClusterAnalysisSet //簇优化分析
        {
            get { return clusterAnalysisSet; }
            set { clusterAnalysisSet = value; }
        }
        //簇优化


        //summary:簇优化仿真
        private MasterCom.RAMS.ZTFunc.ZTCluster.Simulation.CellAreaManager cellAreaManager = MasterCom.RAMS.ZTFunc.ZTCluster.Simulation.CellAreaManager.GetInstance();
        public MasterCom.RAMS.ZTFunc.ZTCluster.Simulation.CellAreaManager CellAreaManager //小区片区管理器
        {
            get { return cellAreaManager; }
        }
        //簇优化仿真

        //summary:簇优图层，栅格显示
        private List<MapFormClusterGridItem> mapFormClusterGridItemList = new List<MapFormClusterGridItem>();
        public List<MapFormClusterGridItem> MapFormClusterGridItemList
        {
            get { return mapFormClusterGridItemList; }
            set { mapFormClusterGridItemList = value; }
        }


        //summary:性能参数
        private DateTime ppStartTime = new DateTime();

        public DateTime PPStartTime
        {
            get { return ppStartTime; }
            set { ppStartTime = value; }
        }

        private DateTime ppEndTime = new DateTime();

        public DateTime PPEndTime
        {
            get { return ppEndTime; }
            set { ppEndTime = value; }
        }

        private List<CellInfoParamItem> ppCellInfoItemList = new List<CellInfoParamItem>();

        public List<CellInfoParamItem> PPCellInfoItemList
        {
            get { return ppCellInfoItemList; }
            set { ppCellInfoItemList = value; }
        }

        private List<TrafficParamItem> ppTrafficInfoItemList = new List<TrafficParamItem>();

        public List<TrafficParamItem> PPTrafficInfoItemList
        {
            get { return ppTrafficInfoItemList; }
            set { ppTrafficInfoItemList = value; }
        }

        private List<GprsDataParamItem> ppGPRSInfoItemList = new List<GprsDataParamItem>();

        public List<GprsDataParamItem> PPGPRSInfoItemList
        {
            get { return ppGPRSInfoItemList; }
            set { ppGPRSInfoItemList = value; }
        }

        private int ppTag = new int();

        public int PPTag //1 for 按小区和时间段查 2 for 按时间段查
        {
            get { return ppTag; }
            set { ppTag = value; }
        }

        private int ppCellInfoMark = 1;

        public int PPCellInfoMark
        {
            get { return ppCellInfoMark; }
            set { ppCellInfoMark = value; }
        }

        private int ppGPRSInfoMark = 1;

        public int PPGPRSInfoMark
        {
            get { return ppGPRSInfoMark; }
            set { ppGPRSInfoMark = value; }
        }

        private int ppTrafficInfoMark = 1;

        public int PPTrafficInfoMark
        {
            get { return ppTrafficInfoMark; }
            set { ppTrafficInfoMark = value; }
        }
        //性能参数


        //summary:测试报告
        private List<TestReport> testReportList = new List<TestReport>();

        public List<TestReport> TestReportList
        {
            get { return testReportList; }
            set { testReportList = value; }
        }

        private List<TestReport> selectedTestReportList = new List<TestReport>();

        public List<TestReport> SelectedTestReportList
        {
            get { return selectedTestReportList; }
            set { selectedTestReportList = value; }
        }

        public event EventHandler TestReportsChanged;

        public void FireTestReportsChanged(object sender)
        {
            MtAction action = new ActionCreateChildFrameSingle();
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = mainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.TestReportForm";
            actionParam["Text"] = "查询测试报告结果";
            actionParam["ImageFilePath"] = @"images\unfrozen.gif";
            action.Param = actionParam;
            action.OnAction();
            if (TestReportsChanged != null)
            {
                TestReportsChanged(sender, null);
            }
        }
        //测试报告

        //对比回放
        private bool isFileReplayByCompareMode = false;
        /// <summary>
        /// 指示文件回放模式是否为对比回放
        /// </summary>
        public bool IsFileReplayByCompareMode
        {
            get {
                return isFileReplayByCompareMode;
            }
            set
            {
                isFileReplayByCompareModeBak = isFileReplayByCompareMode;
                if (isFileReplayByCompareMode) //原来是对比回放则需要清理工作空间
                {
                    foreach (WorkSheet ws in workSpace.WorkSheets)
                    {
                        for (int i = ws.ChildFormConfigs.Count - 1; i >= 0; i-- )
                        {
                            Form cf = ws.ChildFormConfigs[i].ChildForm;
                            if (cf is ChildForm && cf.Text.Contains(ChildFormConfig.FormTitleFileNameMark))
                            {
                                //if (((ChildForm)cf).IsCompareForm)
                                //{
                                //    cf.Close();
                                //    if (ws.ChildFormConfigs.Count - 1 > i)
                                //    {
                                //        mainForm.RemoveTreeNode(ws, ws.ChildFormConfigs[i]);
                                //        ws.ChildFormConfigs.RemoveAt(i);
                                //    }
                                //}
                                //else
                                //{
                                    cf.Text = cf.Text.Substring(0, cf.Text.IndexOf(ChildFormConfig.FormTitleFileNameMark) + 1);
                                //}
                            }
                        }
                    }
                }
                isFileReplayByCompareMode = value;
            }
        }
        private bool isFileReplayByCompareModeBak = false;
        public bool IsFileReplayByCompareModeBak
        {
            get { return isFileReplayByCompareModeBak; }
            set { isFileReplayByCompareModeBak = value; }
        }

        private bool isFileReplayByMTRMode = false;
        /// <summary>
        /// 指示文件回放模式是否为MTR回放
        /// </summary>
        public bool IsFileReplayByMTRMode
        {
            get
            {
                return isFileReplayByMTRMode;
            }
            set
            {
                isFileReplayByMTRModeBak = isFileReplayByMTRMode;
                if (isFileReplayByMTRMode) //原来是对比回放则需要清理工作空间
                {
                    foreach (WorkSheet ws in workSpace.WorkSheets)
                    {
                        for (int i = ws.ChildFormConfigs.Count - 1; i >= 0; i--)
                        {
                            Form cf = ws.ChildFormConfigs[i].ChildForm;
                            if (cf is ChildForm && cf.Text.Contains(ChildFormConfig.FormTitleFileNameMark))
                            {
                                //if (((ChildForm)cf).IsMTRForm)
                                //{
                                //    cf.Close();
                                //    if (ws.ChildFormConfigs.Count - 1 > i)
                                //    {
                                //        mainForm.RemoveTreeNode(ws, ws.ChildFormConfigs[i]);
                                //        ws.ChildFormConfigs.RemoveAt(i);
                                //    }
                                //}
                                //else
                                //{
                                    cf.Text = cf.Text.Substring(0, cf.Text.IndexOf(ChildFormConfig.FormTitleFileNameMark) + 1);
                                //}
                            }
                        }
                    }
                }
                isFileReplayByMTRMode = value;
            }
        }
        private bool isFileReplayByMTRModeBak = false;
        public bool IsFileReplayByMTRModeBak
        {
            get { return isFileReplayByMTRModeBak; }
            set { isFileReplayByMTRModeBak = value; }
        }

        private bool isFileReplayByMTRToLogMode = false;
        /// <summary>
        /// 指示文件回放模式是否为MTR回放
        /// </summary>
        public bool IsFileReplayByMTRToLogMode
        {
            get
            {
                return isFileReplayByMTRToLogMode;
            }
            set
            {
                isFileReplayByMTRToLogModeBak = isFileReplayByMTRToLogMode;
                if (isFileReplayByMTRToLogMode) //原来是对比回放则需要清理工作空间
                {
                    foreach (WorkSheet ws in workSpace.WorkSheets)
                    {
                        for (int i = ws.ChildFormConfigs.Count - 1; i >= 0; i--)
                        {
                            Form cf = ws.ChildFormConfigs[i].ChildForm;
                            if (cf is ChildForm && cf.Text.Contains(ChildFormConfig.FormTitleFileNameMark))
                            {
                                cf.Text = cf.Text.Substring(0, cf.Text.IndexOf(ChildFormConfig.FormTitleFileNameMark) + 1);
                            }
                        }
                    }
                }
                isFileReplayByMTRToLogMode = value;
            }
        }
        private bool isFileReplayByMTRToLogModeBak = false;
        public bool IsFileReplayByMTRToLogModeBak
        {
            get { return isFileReplayByMTRToLogModeBak; }
            set { isFileReplayByMTRToLogModeBak = value; }
        }

        private bool isCompareMode = false;

        public bool IsCompareMode
        {
            get { return isCompareMode; }
            set { isCompareMode = value; }
        }

        private bool isPrepareWithoutGridPartParam = false;
        /// <summary>
        /// 不需用数据，直接画栅格
        /// </summary>
        public bool IsPrepareWithoutGridPartParam
        {
            get { return isPrepareWithoutGridPartParam; }
            set { isPrepareWithoutGridPartParam = value; }
        }

        /// <summary>
        /// 解决某些对工作空间的更改操作（如删除地图窗口）后选择Message无法自动同步到相应TestPoint的问题
        /// </summary>
        public void SelectTestPointByMessage()
        {
            foreach (DTFileDataManager fdm in dtDataManager.FileDataManagers)
            {
                for (int idx = fdm.DTDatas.IndexOf(selectedMessage); idx >= 0; idx-- )
                {
                    if (fdm.DTDatas[idx] is TestPoint)
                    {
                        selectedTestPoints.Add(fdm.DTDatas[idx] as TestPoint);
                        return;
                    }
                }
            }
        }
        //////////////////////////////////////////////////

        /// <summary>
        /// 添加功能结果窗口
        /// </summary>
        /// <param name="formTypeName">className</param>
        /// <param name="form">form object</param>
        public void AddFuncResultForm(string formTypeName, MinCloseForm form)
        {
            if (blackBoard.Properties.ContainsKey(formTypeName))
            {
                blackBoard.Properties[formTypeName] = form;
            }
            else
            {
                blackBoard.Add(formTypeName, form);
            }
        }

        ///summary:添加最小化列表窗口信息
        public void AddQuickWindowItem(string name, string text, string imagePath)
        {
            if (!quickWindowItemDic.ContainsKey(name))
            {
                QuickWindowItem item = new QuickWindowItem();
                item.Name = name;
                item.Des = text;
                item.IconPath = imagePath;
                quickWindowItemDic.Add(name, item);
            }
            
        }

        //summary: 记录最小化列表窗口的信息，键为名称，值为名称对应的窗口信息
        private Dictionary<string, QuickWindowItem> quickWindowItemDic = new Dictionary<string, QuickWindowItem>();
        public Dictionary<string, QuickWindowItem> QuickWindowItemDic
        {
            get { return quickWindowItemDic; }
            set { quickWindowItemDic = value; }
        }
        /////////////////////////////////////////////////

        //summary:记录问题黑点的设置(按异常事件个数着色)
        private List<ColorRange> blackBlockAbEventColorRanges = new List<ColorRange>();
        public List<ColorRange> BlackBlockAbEventColorRanges
        {
            get { return blackBlockAbEventColorRanges; }
            set { blackBlockAbEventColorRanges = value; }
        }

        /// <summary>
        /// 记录投诉热点黑点着色设置(按投诉单数)
        /// </summary>
        private List<ColorRange> hotBlackBlockColorRanges = new List<ColorRange>();
        public List<ColorRange> HotBlackBlockColorRanges
        {
            get { return hotBlackBlockColorRanges; }
            set { hotBlackBlockColorRanges = value; }
        }

        //summary:记录Excel案例组设置(按异常事件个数着色)
        private List<ColorRange> excelBlockAbEventColorRanges = new List<ColorRange>();
        public List<ColorRange> ExcelBlockAbEventColorRanges
        {
            get { return excelBlockAbEventColorRanges; }
            set { excelBlockAbEventColorRanges = value; }
        }


        //summary:记录正确的规划信息字典(规划信息)
        private Dictionary<string, PlanningInfo> planningInfoDic = new Dictionary<string, PlanningInfo>();//正确的规划信息字典，键是btsID_经度_纬度,值是规划信息
        public Dictionary<string, PlanningInfo> PlanningInfoDic
        {
            get { return planningInfoDic; }
            set { planningInfoDic = value; }
        }

        /// <summary>
        /// 记录GSM无主控小区着色设置(按采样点数)
        /// </summary>
        private List<ColorRange> noMainCellBlockColorRanges = new List<ColorRange>();
        public List<ColorRange> NoMainCellBlockColorRanges
        {
            get { return noMainCellBlockColorRanges; }
            set { noMainCellBlockColorRanges = value; }
        }

        /// <summary>
        /// 记录持续差道路汇聚颜色设置
        /// </summary>
        private LastWeakItem lastWeakColorSetup = new LastWeakItem();

        public LastWeakItem LastWeakColorSetup
        {
            get { return lastWeakColorSetup; }
            set { lastWeakColorSetup = value; }
        }

        /// <summary>
        /// 记录GSM无主控小区着色设置(按采样点数)
        /// </summary>
        private List<ColorRange> pilotFrequencyPolluteBlockColorRanges = new List<ColorRange>();
        public List<ColorRange> PilotFrequencyPolluteBlockColorRanges
        {
            get { return pilotFrequencyPolluteBlockColorRanges; }
            set { pilotFrequencyPolluteBlockColorRanges = value; }
        }
        ////////////////////////////////////////////////

        /// <summary>
        /// 记录GSM数据低质量小区着色设置(按采样点数)
        /// </summary>
        private List<ColorRange> gsmDataRateLowQualBlockColorRanges = new List<ColorRange>();
        public List<ColorRange> GSMDataRateLowQualBlockColorRanges
        {
            get { return gsmDataRateLowQualBlockColorRanges; }
            set { gsmDataRateLowQualBlockColorRanges = value; }
        }

        /// <summary>
        /// 记录GSM数据下载速率着色设置(按采样点数)
        /// </summary>
        private List<ColorRange> coverUnbalancedBlockRanges = new List<ColorRange>();
        public List<ColorRange> CoverUnbalancedBlockRanges
        {
            get { return coverUnbalancedBlockRanges; }
            set { coverUnbalancedBlockRanges = value; }
        }

        /// <summary>
        /// 记录上下行覆盖不平衡着色设置(按采样点数)
        /// </summary>
        private List<ColorRange> gsmDataDLRateBlockColorRanges = new List<ColorRange>();
        public List<ColorRange> GSMDataDLRateBlockColorRanges
        {
            get { return gsmDataDLRateBlockColorRanges; }
            set { gsmDataDLRateBlockColorRanges = value; }
        }

        /// <summary>
        /// 记录GSM扫频道路（相对）重叠覆盖度采样点着色
        /// </summary>
        private List<ColorRange> gscanMultiCoverageColorRanges = new List<ColorRange>();
        public List<ColorRange> GScanMultiCoverageColorRanges
        {
            get { return gscanMultiCoverageColorRanges; }
            set { gscanMultiCoverageColorRanges = value; }
        }

        private List<ColorRange> gscanMultiCoverageColorRanges_TD = new List<ColorRange>();
        public List<ColorRange> GScanMultiCoverageColorRanges_TD
        {
            get { return gscanMultiCoverageColorRanges_TD; }
            set { gscanMultiCoverageColorRanges_TD = value; }
        }

        private CellMultiCoverageRanges_LTE cellMultiCoverageRanges_LTE = null;
        public CellMultiCoverageRanges_LTE CellMultiCoverageRanges_LTE
        {
            get
            {
                if (cellMultiCoverageRanges_LTE == null)
                {
                    cellMultiCoverageRanges_LTE = new CellMultiCoverageRanges_LTE();
                }
                return cellMultiCoverageRanges_LTE;
            }
            set { cellMultiCoverageRanges_LTE = value; }
        }

        private EventBlockLegendOption evtBlkLegendOption = null;
        /// <summary>
        /// 事件汇聚图例设置
        /// </summary>
        public EventBlockLegendOption EventBlockLegendOption
        {
            get
            {
                if (evtBlkLegendOption == null)
                {
                    evtBlkLegendOption = new EventBlockLegendOption();
                }
                return evtBlkLegendOption;
            }
            set { evtBlkLegendOption = value; }
        }

        private List<WeakCoverInfo_W> weakRoadCovWLst = new List<WeakCoverInfo_W>();
        public List<WeakCoverInfo_W> WeakRoadCovWLst
        {
            get { return weakRoadCovWLst; }
            set { weakRoadCovWLst = value; }
        }

        private List<WeakCoverInfo_CD> weakCoverRoadCDList = new List<WeakCoverInfo_CD>();
        public List<WeakCoverInfo_CD> WeakCoverRoadCDList
        {
            get { return weakCoverRoadCDList; }
            set { weakCoverRoadCDList = value; }
        }

        private List<RoadMultiCoverageInfo> roadMultiCoveragePoints = new List<RoadMultiCoverageInfo>();
        public List<RoadMultiCoverageInfo> RoadMultiCoveragePoints
        {
            get { return roadMultiCoveragePoints; }
            set { roadMultiCoveragePoints = value; }
        }

        private List<GridRoadMultiCoverageInfo> roadMultiCoverageGrids900 = null;
        public List<GridRoadMultiCoverageInfo> RoadMultiCoverageGrids900
        {
            get 
            {
                if (roadMultiCoverageGrids900 == null)
                {
                    roadMultiCoverageGrids900 = new List<GridRoadMultiCoverageInfo>();
                }
                return roadMultiCoverageGrids900; 
            }
            set { roadMultiCoverageGrids900 = value; }
        }

        private List<GridRoadMultiCoverageInfo> roadMultiCoverageGrids1800 = null;
        public List<GridRoadMultiCoverageInfo> RoadMultiCoverageGrids1800
        {
            get
            {
                if (roadMultiCoverageGrids1800 == null)
                {
                    roadMultiCoverageGrids1800 = new List<GridRoadMultiCoverageInfo>();
                }
                return roadMultiCoverageGrids1800;
            }
            set { roadMultiCoverageGrids1800 = value; }
        }

        private List<GridRoadMultiCoverageInfo> roadMultiCoverageGridsTotal = null;
        public List<GridRoadMultiCoverageInfo> RoadMultiCoverageGridsTotal
        {
            get
            {
                if (roadMultiCoverageGridsTotal == null)
                {
                    roadMultiCoverageGridsTotal = new List<GridRoadMultiCoverageInfo>();
                }
                return roadMultiCoverageGridsTotal;
            }
            set { roadMultiCoverageGridsTotal = value; }
        }

        private List<GridRoadMultiCoverageInfo> roadMultiCoverageWeakCoverGrids900 = null;
        public List<GridRoadMultiCoverageInfo> RoadMultiCoverageWeakCoverGrids900
        {
            get
            {
                if (roadMultiCoverageWeakCoverGrids900 == null)
                {
                    roadMultiCoverageWeakCoverGrids900 = new List<GridRoadMultiCoverageInfo>();
                }
                return roadMultiCoverageWeakCoverGrids900;
            }
            set { roadMultiCoverageWeakCoverGrids900 = value; }
        }

        private List<GridRoadMultiCoverageInfo> roadMultiCoverageWeakCoverGrids1800 = null;
        public List<GridRoadMultiCoverageInfo> RoadMultiCoverageWeakCoverGrids1800
        {
            get
            {
                if (roadMultiCoverageWeakCoverGrids1800 == null)
                {
                    roadMultiCoverageWeakCoverGrids1800 = new List<GridRoadMultiCoverageInfo>();
                }
                return roadMultiCoverageWeakCoverGrids1800;
            }
            set { roadMultiCoverageWeakCoverGrids1800 = value; }
        }

        private List<GridRoadMultiCoverageInfo> roadMultiCoverageWeakCoverGridsTotal = null;
        public List<GridRoadMultiCoverageInfo> RoadMultiCoverageWeakCoverGridsTotal
        {
            get
            {
                if (roadMultiCoverageWeakCoverGridsTotal == null)
                {
                    roadMultiCoverageWeakCoverGridsTotal = new List<GridRoadMultiCoverageInfo>();
                }
                return roadMultiCoverageWeakCoverGridsTotal;
            }
            set { roadMultiCoverageWeakCoverGridsTotal = value; }
        }

        public void ClearRoadMultiCoverage()
        {
            roadMultiCoveragePoints.Clear();
            roadMultiCoverageGrids900 = null;
            roadMultiCoverageGrids1800 = null;
            roadMultiCoverageGridsTotal = null;
            roadMultiCoverageWeakCoverGrids900 = null;
            roadMultiCoverageWeakCoverGrids1800 = null;
            roadMultiCoverageWeakCoverGridsTotal = null;
        }
        private List<RoadMultiCoverageInfo_TD> curRegionRoadMultiCoveragePoints_TD = new List<RoadMultiCoverageInfo_TD>();
        public List<RoadMultiCoverageInfo_TD> CurRegionRoadMultiCoveragePoints_TD
        {
            get { return curRegionRoadMultiCoveragePoints_TD; }
            set { curRegionRoadMultiCoveragePoints_TD = value; }
        }

        private List<GridRoadMultiCoverageInfo_TD> curRegionRoadMultiCoverageGrids_TD = null;
        public List<GridRoadMultiCoverageInfo_TD> CurRegionRoadMultiCoverageGrids_TD
        {
            get
            {
                if (curRegionRoadMultiCoverageGrids_TD == null)
                {
                    curRegionRoadMultiCoverageGrids_TD = new List<GridRoadMultiCoverageInfo_TD>();
                }
                return curRegionRoadMultiCoverageGrids_TD;
            }
            set { curRegionRoadMultiCoverageGrids_TD = value; }
        }

        private List<GridRoadMultiCoverageInfo_TD> curRegionRoadMultiCoverageWeakCoverGrids_TD = null;
        public List<GridRoadMultiCoverageInfo_TD> CurRegionRoadMultiCoverageWeakCoverGrids_TD
        {
            get
            {
                if (curRegionRoadMultiCoverageWeakCoverGrids_TD == null)
                {
                    curRegionRoadMultiCoverageWeakCoverGrids_TD = new List<GridRoadMultiCoverageInfo_TD>();
                }
                return curRegionRoadMultiCoverageWeakCoverGrids_TD;
            }
            set { curRegionRoadMultiCoverageWeakCoverGrids_TD = value; }
        }

        private Dictionary<string, List<RoadMultiCoverageInfo_TD>> regionRoadMultiCoveragePoints_TD = new Dictionary<string, List<RoadMultiCoverageInfo_TD>>();
        public Dictionary<string, List<RoadMultiCoverageInfo_TD>> RegionRoadMultiCoveragePoints_TD
        {
            get { return regionRoadMultiCoveragePoints_TD; }
            set { regionRoadMultiCoveragePoints_TD = value; }
        }

        private Dictionary<string, List<GridRoadMultiCoverageInfo_TD>> regionRoadMultiCoverageGrids_TD = null;
        public Dictionary<string, List<GridRoadMultiCoverageInfo_TD>> RegionRoadMultiCoverageGrids_TD
        {
            get
            {
                if (regionRoadMultiCoverageGrids_TD == null)
                {
                    regionRoadMultiCoverageGrids_TD = new Dictionary<string, List<GridRoadMultiCoverageInfo_TD>>();
                }
                return regionRoadMultiCoverageGrids_TD;
            }
            set { regionRoadMultiCoverageGrids_TD = value; }
        }

        private Dictionary<string, List<GridRoadMultiCoverageInfo_TD>> regionRoadMultiCoverageWeakCoverGrids_TD = null;
        public Dictionary<string, List<GridRoadMultiCoverageInfo_TD>> RegionRoadMultiCoverageWeakCoverGrids_TD
        {
            get
            {
                if (regionRoadMultiCoverageWeakCoverGrids_TD == null)
                {
                    regionRoadMultiCoverageWeakCoverGrids_TD = new Dictionary<string, List<GridRoadMultiCoverageInfo_TD>>();
                }
                return regionRoadMultiCoverageWeakCoverGrids_TD;
            }
            set { regionRoadMultiCoverageWeakCoverGrids_TD = value; }
        }

        public void ClearRoadMultiCoverage_TD(List<string> regionList)
        {
            regionRoadMultiCoveragePoints_TD.Clear();
            foreach (string region in regionList)
            {
                regionRoadMultiCoveragePoints_TD.Add(region, new List<RoadMultiCoverageInfo_TD>());
            }
            regionRoadMultiCoveragePoints_TD.Add("全部汇总", new List<RoadMultiCoverageInfo_TD>());
            regionRoadMultiCoverageGrids_TD = null;
            regionRoadMultiCoverageWeakCoverGrids_TD = null;
            curRegionRoadMultiCoveragePoints_TD.Clear();
            curRegionRoadMultiCoverageGrids_TD = null;
            curRegionRoadMultiCoverageWeakCoverGrids_TD = null;
        }

        private Dictionary<string, CpiInterfereCell_Pair> cpiInterfereCellPairDic = new Dictionary<string, CpiInterfereCell_Pair>();
        public Dictionary<string, CpiInterfereCell_Pair> CpiInterfereCellPairDic_TD
        {
            get { return cpiInterfereCellPairDic; }
            set { cpiInterfereCellPairDic = value; }
        }

        private List<ColorRange> cellMultiCoverageColorRanges = new List<ColorRange>();
        public List<ColorRange> CellMultiCoverageColorRanges
        {
            get { return cellMultiCoverageColorRanges; }
            set { cellMultiCoverageColorRanges = value; }
        }

        private List<ColorRange> cellMultiCoverageColorRanges_TD = new List<ColorRange>();
        public List<ColorRange> CellMultiCoverageColorRanges_TD
        {
            get { return cellMultiCoverageColorRanges_TD; }
            set { cellMultiCoverageColorRanges_TD = value; }
        }

        private List<CellMultiCoverageInfo> cellMultiCovList = new List<CellMultiCoverageInfo>();
        public List<CellMultiCoverageInfo> CellMultiCovList
        {
            get { return cellMultiCovList; }
            set { cellMultiCovList = value; }
        }

        private List<TDCellInCovRegionInfo> cellMultiCovList_TD = new List<TDCellInCovRegionInfo>();
        public List<TDCellInCovRegionInfo> CellMultiCovList_TD
        {
            get { return cellMultiCovList_TD; }
            set { cellMultiCovList_TD = value; }
        }

        private List<WCellInCovRegionInfo> cellMultiCovList_W = new List<WCellInCovRegionInfo>();
        public List<WCellInCovRegionInfo> CellMultiCovList_W
        {
            get { return cellMultiCovList_W; }
            set { cellMultiCovList_W = value; }
        }

        private List<LTECellMultiCovInfo> cellMultiCovList_LTE = new List<LTECellMultiCovInfo>();
        public List<LTECellMultiCovInfo> CellMultiCovList_LTE
        {
            get { return cellMultiCovList_LTE; }
            set { cellMultiCovList_LTE = value; }
        }

        private List<LteCellMultiCovInfoExt> lteAntennaMultiCovList = new List<LteCellMultiCovInfoExt>();
        public List<LteCellMultiCovInfoExt> LteAntennaMultiCovList
        {
            get { return lteAntennaMultiCovList; }
            set { lteAntennaMultiCovList = value; }
        }

        private FarCellCover curFarCellCover = null;
        public FarCellCover CurFarCellCover
        {
            get { return curFarCellCover; }
            set { curFarCellCover = value; }
        }

        private CellWrongDir curCellWrongDir = null;
        public CellWrongDir CurCellWrongDir
        {
            get { return curCellWrongDir; }
            set { curCellWrongDir = value; }
        }

        private CellWeakCoverByGridInfoBase curCellWeakCoverByGrid = null;
        public CellWeakCoverByGridInfoBase CurCellWeakCoverByGrid
        {
            get { return curCellWeakCoverByGrid; }
            set { curCellWeakCoverByGrid = value; }
        }

        public void ClearCellWeakCoverByGridData()
        {
            CurCellWeakCoverByGrid = null;
        }

        private bool showCellCoverAbsLevel = false;
        public bool ShowCellCoverAbsLevel
        {
            get { return showCellCoverAbsLevel; }
            set { showCellCoverAbsLevel = value; }
        }

        private bool showCellCoverRelLevel = false;
        public bool ShowCellCoverRelLevel
        {
            get { return showCellCoverRelLevel; }
            set { showCellCoverRelLevel = value; }
        }

        private bool showCellCoverMulLevel = false;
        public bool ShowCellCoverMulLevel
        {
            get { return showCellCoverMulLevel; }
            set { showCellCoverMulLevel = value; }
        }

        private List<ColorRange> cellRedundantCoverageColorRanges = new List<ColorRange>();
        public List<ColorRange> CellRedundantCoverageColorRanges
        {
            get { return cellRedundantCoverageColorRanges; }
            set { cellRedundantCoverageColorRanges = value; }
        }

        private List<ColorRange> cellRedundantCoverageColorRanges_TD = new List<ColorRange>();
        public List<ColorRange> CellRedundantCoverageColorRanges_TD
        {
            get { return cellRedundantCoverageColorRanges_TD; }
            set { cellRedundantCoverageColorRanges_TD = value; }
        }

        private List<CellRedundantCoverageInfo> cellRedundantCovList = new List<CellRedundantCoverageInfo>();
        public List<CellRedundantCoverageInfo> CellRedundantCovList
        {
            get { return cellRedundantCovList; }
            set { cellRedundantCovList = value; }
        }

        private List<TDCellRedundantInfo> cellRedundantCovList_TD = new List<TDCellRedundantInfo>();
        public List<TDCellRedundantInfo> CellRedundantCovList_TD
        {
            get { return cellRedundantCovList_TD; }
            set { cellRedundantCovList_TD = value; }
        }

        private List<WCellRedundantInfo> cellRedundantCovList_W = new List<WCellRedundantInfo>();
        public List<WCellRedundantInfo> CellRedundantCovList_W
        {
            get { return cellRedundantCovList_W; }
            set { cellRedundantCovList_W = value; }
        }

        private List<PathInfo> pathInfoList = new List<PathInfo>();
        public List<PathInfo> PathInfoList
        {
            get { return pathInfoList; }
            set { pathInfoList = value; }
        }

        private bool showCellRedundantAbsLevel = false;
        public bool ShowCellRedundantAbsLevel
        {
            get { return showCellRedundantAbsLevel; }
            set { showCellRedundantAbsLevel = value; }
        }

        private bool showCellRedundantRelLevel = false;
        public bool ShowCellRedundantRelLevel
        {
            get { return showCellRedundantRelLevel; }
            set { showCellRedundantRelLevel = value; }
        }

        private bool showCellRedundantMulLevel = false;
        public bool ShowCellRedundantMulLevel
        {
            get { return showCellRedundantMulLevel; }
            set { showCellRedundantMulLevel = value; }
        }

        private bool drawLinesPntToCells = false;
        public bool DrawLinesPntToCells
        {
            get { return drawLinesPntToCells; }
            set { drawLinesPntToCells = value; }
        }

        private Dictionary<TestPoint, List<MasterCom.RAMS.ZTFunc.LongLat>> pntToCellsDic = new Dictionary<TestPoint, List<MasterCom.RAMS.ZTFunc.LongLat>>();
        public Dictionary<TestPoint, List<MasterCom.RAMS.ZTFunc.LongLat>> PntToCellsDic
        {
            get { return pntToCellsDic; }
            set { pntToCellsDic = value; }
        }

        /// <summary>
        /// 获取小区重叠覆盖度颜色
        /// </summary>
        /// <returns>覆盖度</returns>
        public Color GetCellMultiCovColor(double covLev)
        {
            foreach (MasterCom.MControls.ColorRange range in CellMultiCoverageColorRanges)
            {
                if (covLev >= range.minValue && covLev < range.maxValue)
                {
                    return range.color;
                }
            }
            return Color.Empty;
        }

        public Color GetCellRedundantCovColor(double covLev)
        {
            foreach (MasterCom.MControls.ColorRange range in CellRedundantCoverageColorRanges)
            {
                if (covLev >= range.minValue && covLev < range.maxValue)
                {
                    return range.color;
                }
            }
            return Color.Empty;
        }
         
        private List<ProblemBlockItem> curProblemBlockList = new List<ProblemBlockItem>();
        public List<ProblemBlockItem> CurProblemBlockList
        {
            get { return curProblemBlockList; }
            set { curProblemBlockList = value; }
        }
         
        private List<EventInfoSecond> curProblemBlockEventList = new List<EventInfoSecond>();
        public List<EventInfoSecond> CurProblemBlockEventList
        {
            get { return curProblemBlockEventList; }
            set { curProblemBlockEventList = value; }
        }
         
        private List<TabProblemInfo> tabProblemInfoList = new List<TabProblemInfo>();
        public List<TabProblemInfo> TabProblemInfoList
        {
            get { return tabProblemInfoList; }
            set { tabProblemInfoList = value; }
        }

        public event EventHandler PlanningInfoChanged; //规划信息事件(规划信息)

        public void FirePlanningInfoChanged(object sender)
        {
            if (PlanningInfoChanged != null)
            {
                PlanningInfoChanged(sender, null);
            }
        }



        private LegendShowItem legendItem = new LegendShowItem();      
        /// <summary>
        /// 图例显示项目(根据实际查询数据显示)
        /// </summary>
        public LegendShowItem LegendItem
        {
            get { return legendItem; }
            set { legendItem = value; }
        }

        /// <summary>
        /// 更新图例
        /// </summary>
        public void RefreshLegend()
        {
            MainForm.RefreshLegend();
        }
        public void RefreshCellGridLegend(ref CellGridCondition con)
        {
            MainForm.RefreshCellGridLegend(ref con);
        }

        private ConfigInfo systemConfigInfo = null;
        /// <summary>
        /// 系统设置项目
        /// </summary>
        public ConfigInfo SystemConfigInfo
        {
            get 
            { 
                if (systemConfigInfo == null)
                {
                    systemConfigInfo = new ConfigInfo();
                }
                return systemConfigInfo; 
            }
            set { systemConfigInfo = value; }
        }

        public Dictionary<string, string> getWorkSpaceDic(string configPath) //根据当前屏幕分辨率获取configPath下工作空间字典
        {
            Dictionary<string, string> workSpaceDic = new Dictionary<string, string>();
            DirectoryInfo dirInfo = new DirectoryInfo(configPath);
            if (dirInfo.Exists)
            {
                foreach (System.IO.FileInfo fileInfo in dirInfo.GetFiles())
                {
                    if (fileInfo.Name.StartsWith("comparemode_"))
                    {
                        continue;
                    }
                    if (fileInfo.Name.ToLower().EndsWith(".wks"))
                    {
                        string workSpaceBaseName = fileInfo.Name.Substring(0, fileInfo.Name.LastIndexOf('.'));
                        workSpaceDic.Add(workSpaceBaseName, fileInfo.FullName);
                    }
                }
            }
            return workSpaceDic;
        }

        public bool isResolutionLessThan1024_768() //判断当前屏幕分辨率是否小于等于1024*768
        {
            int resolutionWidth = Screen.PrimaryScreen.Bounds.Width;
            int resolutionHeight = Screen.PrimaryScreen.Bounds.Height;
            if (resolutionWidth <= 1024 || resolutionHeight <= 768)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        private CloseSimuLog curCloseSimuLog;
        public CloseSimuLog CurCloseSimuLog //当前闭站模拟的LOG信息
        {
            get { return curCloseSimuLog; }
            set { curCloseSimuLog = value; }
        }

        private List<int> closedCellIDs = new List<int>();
        public List<int> ClosedCellIDs //已关闭的小区ID列表
        {
            get { return closedCellIDs; }
            set { closedCellIDs = value; }
        }

        private List<Cell> closedCells = new List<Cell>();
        public List<Cell> ClosedCells //已关闭的小区列表
        {
            get { return closedCells; }
            set { closedCells = value; }
        }

        Dictionary<int, CloseSimuGridInfo> curCloseSimuGridInfoDic = new Dictionary<int, CloseSimuGridInfo>();
        public Dictionary<int, CloseSimuGridInfo> CurCloseSimuGridInfoDic //闭站模拟数据
        {
            get { return curCloseSimuGridInfoDic; }
            set { curCloseSimuGridInfoDic = value; }
        }

        public void FireCellSimuQueried(object sender)
        {
            if(CellSimuChanged!=null)
            {
                CellSimuChanged(sender, null);
            }
        }

        //新疆FDD单验,根据小区指标绘制不同的主服小区颜色
        private bool drawDifferentServerColor = false;
        public bool DrawDifferentServerColor
        {
            get { return drawDifferentServerColor; }
            set { drawDifferentServerColor = value; }
        }

        public FddSetting FDDSetting { get; set; } = new FddSetting();

        public NbIotSetting NBIOTSetting { get; set; } = new NbIotSetting();

        //begin 飞线设置by huangjianhui
        private bool drawFlyLines = false; //是否显示所有飞线
        public bool DrawFlyLines
        {
            get { return drawFlyLines; }
            set { drawFlyLines = value; }
        }
        private bool drawEventFlyLines = false;
        /// <summary>
        /// 画事件飞线
        /// </summary>
        public bool DrawEventFlyLines
        {
            get { return drawEventFlyLines; }
            set { drawEventFlyLines = value; }
        }

        public int FlyLineDistLimit = -1;

        private bool drawSampleHigh = false;
        public bool DrawSampleHigh
        {
            get { return drawSampleHigh; }
            set { drawSampleHigh = value; }
        }
        //end 飞线设置
        private Dictionary<TDCell, List<TestPoint>> selTDCellPairDic = new Dictionary<TDCell, List<TestPoint>>();
        public Dictionary<TDCell, List<TestPoint>> SelTDCellPairDic
        {
            get { return selTDCellPairDic; }
            set { selTDCellPairDic = value; }
        }
        private Dictionary<WCell, List<TestPoint>> selWCellPairDic = new Dictionary<WCell, List<TestPoint>>();
        public Dictionary<WCell, List<TestPoint>> SelWCellPairDic
        {
            get { return selWCellPairDic; }
            set { selWCellPairDic = value; }
        }
        private Dictionary<ZTDIYNonconformity.DefineCell, List<TestPoint>> selDCellPairDic = new Dictionary<ZTDIYNonconformity.DefineCell, List<TestPoint>>();
        public Dictionary<ZTDIYNonconformity.DefineCell, List<TestPoint>> SelDCellPairDic
        {
            get { return selDCellPairDic; }
            set { selDCellPairDic = value; }
        }
        private Dictionary<ZTDIYWCDMANonconformity.DefineCell, List<TestPoint>> selWDCellPairDic = new Dictionary<ZTDIYWCDMANonconformity.DefineCell, List<TestPoint>>();
        public Dictionary<ZTDIYWCDMANonconformity.DefineCell, List<TestPoint>> SelWDCellPairDic
        {
            get { return selWDCellPairDic; }
            set { selWDCellPairDic = value; }
        }
        private ZTAntennaBase.SimulationPoints simulationPoints;
        public ZTAntennaBase.SimulationPoints SimulationPoints
        {
            get { return simulationPoints; }
            set { simulationPoints = value;  }
        }
        private Dictionary<string, int> cellAntSampleIndexDic;//小区在每采样点的序号
        public Dictionary<string, int> CellAntSampleIndexDic
        {
            get { return cellAntSampleIndexDic; }
            set { cellAntSampleIndexDic = value; }
        }

        private bool drawEmulationPoints = false;//是否在图层上显示仿真点
        public bool DrawEmulationPoints
        {
            get { return drawEmulationPoints; }
            set { drawEmulationPoints = value; }
        }

        private List<List<MasterCom.RAMS.ZTFunc.LongLat>> cellEmulationPointsList = new List<List<MasterCom.RAMS.ZTFunc.LongLat>>();
        public List<List<MasterCom.RAMS.ZTFunc.LongLat>> CellEmulationPointsList
        {
            get { return cellEmulationPointsList; }
            set { cellEmulationPointsList = value; }
        }
        private bool drawEventDateLbl = false;//是否在图层上显示事件时间
        public bool DrawEventDateLbl
        {
            get { return drawEventDateLbl; }
            set { drawEventDateLbl = value; }
        }
        private string eventDateLblTxtFormat;
        public string EventDateLblTxtFormat //事件时间标签显示格式
        {
            get { return eventDateLblTxtFormat; }
            set { eventDateLblTxtFormat = value; }
        }
        private bool drawHandoverSerialNum = false;//是否在图层上显示切换重选序列号，切换&重选信息功能使用
        public bool DrawHandoverSerialNum
        {
            get { return drawHandoverSerialNum; }
            set { drawHandoverSerialNum = value; }
        }
        private bool drawHandoverSeq = false;//是否绘制切换序列
        public bool DrawHandoverSeq
        {
            get { return drawHandoverSeq; }
            set { drawHandoverSeq = value; }
        }
        public List<Event> HandOverSeqEvents = new List<Event>();

        //private List<TDCellUnit> curTDCellUnitList = new List<TDCellUnit>();//同频同扰码组小区单元列表（北京移动）
        //public List<TDCellUnit> CurTDCellUnitList
        //{
        //    get { return curTDCellUnitList; }
        //    set { curTDCellUnitList = value; }
        //}

        private bool drawCoArfcnCoCpiGrpLine = false; //true画连线，false不画连线（北京移动）
        public bool DrawCoArfcnCoCpiGrpLine
        {
            get { return drawCoArfcnCoCpiGrpLine; }
            set { drawCoArfcnCoCpiGrpLine = value; }
        }
        /// <summary>
        /// 外部调用，加入黑点小区过滤用
        /// </summary>
        public string _BlackBlockFilter_Cell = "";

        //江苏小区区域统计==================================peifuping
        private Dictionary<string, DataUnitAreaKPIQuery> cellAreaDataDic = new Dictionary<string, DataUnitAreaKPIQuery>();
        public Dictionary<string, DataUnitAreaKPIQuery> CellAreaDataDic
        {
            get { return cellAreaDataDic; }
            set { cellAreaDataDic = value; }
        }

        private ReportStyle lastSelectedReportStyle = null; //上一次选择的报表样式（KPI）
        public ReportStyle LastSelectedReportStyle
        {
            get { return lastSelectedReportStyle; }
            set { lastSelectedReportStyle = value; }
        }

        public List<CellSetByFileForm.CellSetResultData> CellSetOfFile
        {
            get;
            set;
        }

        public List<CellSetForm.CellSetResultData> CellSetOfRegion
        {
            get;
            set;
        }

        //小区集数据
        private CellSetForm.CellSetResultData cellSetRetData;
        public CellSetForm.CellSetResultData CellSetResultDataInfo
        {
            get { return cellSetRetData; }
            set { cellSetRetData = value; }
        }

        private Dictionary<string, CellSetForm.CellSetResultData> cellSetRetDataDic;
        public Dictionary<string, CellSetForm.CellSetResultData> CellSetRetDataDic
        {
            get { return cellSetRetDataDic; }
            set { cellSetRetDataDic = value; }
        }

        //WCDMA小区集数据
        private WCDMACellSetForm.WCDMACellSetResultData WCDMAcellSetRetData;
        public WCDMACellSetForm.WCDMACellSetResultData WCDMACellSetResultDataInfo
        {
            get { return WCDMAcellSetRetData; }
            set { WCDMAcellSetRetData = value; }
        }

        public List<CellSetForm.CellSetResultData_Unused> CellSetUnusedOfRegion
        {
            get;
            set;
        }

        public List<CellSetForm.CellSetResultData_NBCell> NBCellSetRetData
        {
            get;
            set;
        }

        public List<CellSetForm.CellSetResultData_BCCH> BCCHSetRetData
        {
            get;
            set;
        }

        public List<CellSetByFileForm.CellSetResultData_Unused> CellSetUnusedOfFile
        {
            get;
            set;
        }

        public List<CellSetByFileForm.CellSetResultData_NBCell> NBCellSetRetDataByFile
        {
            get;
            set;
        }

        public List<CellSetByFileForm.CellSetResultData_BCCH> BCCHSetRetDataByFile
        {
            get;
            set;
        }

        public List<CellSetByFileForm.SNCellGatherInfo> SNCellGatherInfoListByFile
        {
            get;
            set;
        }

        public List<CellSetForm.SNCellGatherInfo> SNCellGatherInfoList
        {
            get;
            set;
        }

        //CQT小区集统计
        private CqtCellSetForm.CqtCellSetResultData cqtCellSetRetData;
        public CqtCellSetForm.CqtCellSetResultData CqtCellSetResultDataInfo
        {
            get { return cqtCellSetRetData; }
            set { cqtCellSetRetData = value; }
        }

        private CqtCellSetForm.CqtCellSetResultData_FrequencyPoint cqtBcchSetRetData;
        public CqtCellSetForm.CqtCellSetResultData_FrequencyPoint CqtBCCHSetRetData
        {
            get { return cqtBcchSetRetData; }
            set { cqtBcchSetRetData = value; }
        }

        private CqtCellSetForm.CqtBTSSetResultData cqtBtsSetRetData;
        public CqtCellSetForm.CqtBTSSetResultData CqtBTSSetResultDataInfo
        {
            get { return cqtBtsSetRetData; }
            set { cqtBtsSetRetData = value; }
        }

        private CqtCellSetForm.CqtCellSetResultData_NBCell cqtNbCellSetRetData;
        public CqtCellSetForm.CqtCellSetResultData_NBCell CqtNBCellSetRetData
        {
            get { return cqtNbCellSetRetData; }
            set { cqtNbCellSetRetData = value; }
        }

        private List<CoverageCellInfo> coverageOfCellInfos = new List<CoverageCellInfo>();
        /// <summary>
        /// 小区覆盖带小区信息集合
        /// </summary>
        public List<CoverageCellInfo> CoverageOfCellInfos
        {
            get { return coverageOfCellInfos; }
            set { coverageOfCellInfos = value; }
        }

        /// <summary>
        /// 小区覆盖带显示
        /// </summary>
        public showSetting CovCellShowSetting;

        public ZTCellCoverageRangeAnaItem CellCvrRngItem
        {
            get;
            set;
        }

        public bool IsDrawCoverRangeLine
        {
            get;
            set;
        }

        private ZTDIYEventsComparisonForm.EventsResultForReduplicateEvents eventsResultForReduplicateEvents = new ZTDIYEventsComparisonForm.EventsResultForReduplicateEvents();
        /// <summary>
        /// 重复事件结果
        /// </summary>
        public ZTDIYEventsComparisonForm.EventsResultForReduplicateEvents EventsResultForReduplicateEvents
        {
            get { return eventsResultForReduplicateEvents; }
            set { eventsResultForReduplicateEvents = value; }
        }


        //退站小区信息管理器
        private OutServiceInfoManager outServiceInfoManager = new OutServiceInfoManager();
        public OutServiceInfoManager OutServiceInfoManager
        {
            get { return outServiceInfoManager; }
            set { outServiceInfoManager = value; }
        }

        private Dictionary<string, List<string>> streetMap = new Dictionary<string, List<string>>(); //道路图层的道路列表
        public Dictionary<string, List<string>> StreetMap
        {
            get { return streetMap; }
            set { streetMap = value; }
        }

        private List<InjectionStreetSet> selectedInjectionStreetSetList = new List<InjectionStreetSet>();//已选道路集信息列表
        public List<InjectionStreetSet> SelectedInjectionStreetSetList
        {
            get { return selectedInjectionStreetSetList; }
            set { selectedInjectionStreetSetList = value; }
        }

        private Dictionary<string, DownloadInfo> ipDownloadInfoDic = new Dictionary<string, DownloadInfo>();
        public Dictionary<string, DownloadInfo> IpDownloadInfoDic
        {
            get { return ipDownloadInfoDic; }
            set { ipDownloadInfoDic = value; }
        }

        private int cqtCarrierID = 1;
        public int CQTCarrierID
        {
            get { return cqtCarrierID; }
            set { cqtCarrierID = value; }
        }

        private int cqtServiceType = 1;
        /// <summary>
        /// GSM_AMR = 1,
        /// GPRS_DATA = 2,
        /// EDGE_DATA = 3,
        /// TD_AMR = 4,
        /// TD_DATA = 5,
        /// CDMA_AMR = 6,
        /// CDMA1X_DATA = 7,
        /// CDMA2000_AMR = 8,
        /// CDMA2000_DATA = 9,
        /// WCDMA_AMR = 10,
        /// WCDMA_DATA = 11,
        /// WCDMA_HSDPA = 15,
        /// TD_HSDPA = 18,
        /// WLAN = 31
        /// </summary>
        public int CQTServiceType
        {
            get { return cqtServiceType; }
            set { cqtServiceType = value; }
        }

        internal void FireSetDefaultMapSerialTheme(string serialThemeName)
        {
            DTLayerSerialManager.Instance.SetDefaultSerial(serialThemeName);
            RefreshLegend();
        }
        internal void FireSetDefaultMapSerialThemes(params string[] serialThemeNames)
        {
            DTLayerSerialManager.Instance.SetDefaultSerials(serialThemeNames);
            RefreshLegend();
        }
        public void FireSetDefaultMapSerialTheme(string sysName, string paramName)
        {
            DTLayerSerialManager.Instance.SetDefaultSerial(sysName, paramName);
            RefreshLegend();
        }

        private void setMapSerialTheme(MapForm mapform, string serialThemeName)
        {
            if (mapform==null)
            {
                return;
            }
            MapDTLayer dtLayer = mapform.GetDTLayer();
            if (dtLayer==null)
            {
                return;
            }
            bool foundWithName = false;
            foreach (MapSerialInfo serialInfo in dtLayer.SerialInfos)
            {
                if (serialInfo.Name.ToUpper() == serialThemeName.ToUpper())
                {
                    foundWithName = true;
                    break;
                }
            }
            if (foundWithName)
            {
                foreach (MapSerialInfo serialInfo in dtLayer.SerialInfos)
                {
                    if (serialInfo.Name.ToUpper() == serialThemeName.ToUpper())
                    {
                        serialInfo.Visible = true;
                    }
                    else
                    {
                        serialInfo.Visible = false;
                    }
                }
                RefreshLegend();
            }
        }

        private BlockQueryCond curBbCond;

        public BlockQueryCond CurBbCond//问题黑点查询条件
        {
            get { return curBbCond; }
            set { curBbCond = value; }
        }

        private string sqlGetGMapConStr = null;
        internal string GetGMapConString()
        {
            if(sqlGetGMapConStr==null)
            {
                DIYSqlGetGMapConstr conStrQuery = new DIYSqlGetGMapConstr(this);
                conStrQuery.Query();
                sqlGetGMapConStr = conStrQuery.GetResultConstr();
            }
            return sqlGetGMapConStr;
        }

        private bool cellHighlightAll = false;
        public bool CellHighLightAll
        {
            get { return cellHighlightAll; }
            set { cellHighlightAll = value; }
        }

        private float cellHighlightMultiple = 1.0F;
        public float CellHighlightMultiple
        {
            get { return cellHighlightMultiple; }
            set { cellHighlightMultiple = value; }
        }

        private List<Cell> cellHighlightList = new List<Cell>();
        public List<Cell> CellHighlightList
        {
            get { return cellHighlightList; }
            set { cellHighlightList = value; }
        }

        public List<WCell> CellHighlightList_W = new List<WCell>();
        public List<TDCell> CellHighlightList_TD = new List<TDCell>();

        private List<ProblemCellInfo> problemCells = new List<ProblemCellInfo>();
        public List<ProblemCellInfo> ProblemCells
        {
            get { return problemCells; }
            set { problemCells = value; }
        }
        //========ExcelBlock==================begin
        private List<string> excelProjectList = null;
        public List<string> ExcelProjectList
        {
            get 
            {
                if (excelProjectList == null)
                {
                    excelProjectList = new List<string>();
                }
                return excelProjectList;
            }
            set
            {
                excelProjectList = value;
            }
        }

        public List<CellSetForm.BTSSetResultData> BTSSetOfRegion
        {
            get;
            set;
        }

        public List<CellSetByFileForm.BTSSetResultData> BTSSetOfFile
        {
            get;
            set;
        }

        public Dictionary<Cell, List<Cell>> CellMultiCovMCellSubCellDic = new Dictionary<Cell, List<Cell>>();

        public Dictionary<TDCell, List<TDCell>> CellMultiCovMCellSubCellDic_TD = new Dictionary<TDCell, List<TDCell>>();

        public LTECellMultiCovInfo SelectedCellMultiCov_LTE
        {
            get;
            set;
        }

        public CellMultiCovType CellMultiCovType
        {
            get;
            set;
        }

        private List<string> excelProbStatList = null;
        public List<string> ExcelProbStatList
        {
            get
            {
                if (excelProbStatList == null)
                {
                    excelProbStatList = new List<string>();
                }
                return excelProbStatList;
            }
            set
            {
                excelProbStatList = value;
            }
        }

        private List<string> excelCauseTypeList = null;
        public List<string> ExcelCauseTypeList
        {
            get
            {
                if (excelCauseTypeList == null)
                {
                    excelCauseTypeList = new List<string>();
                }
                return excelCauseTypeList;
            }
            set
            {
                excelCauseTypeList = value;
            }
        }
        //========ExcelBlock==================end

        private Dictionary<DbPoint, string> popShowGISLabelDic = null;
        public Dictionary<DbPoint, string> PopShowGISLabelDic
        {
            get 
            {
                if (popShowGISLabelDic == null)
                {
                    popShowGISLabelDic = new Dictionary<DbPoint, string>();
                }
                return popShowGISLabelDic;
            }
            set
            {
                popShowGISLabelDic = value;
            }
        }

        private bool showEventInterference = false;
        public bool ShowEventInterference
        {
            get { return showEventInterference; }
            set { showEventInterference = value; }
        }

        private List<GSMDataLowQualityBlock> curGSMDataLowQualityBlocks = new List<GSMDataLowQualityBlock>();
        public List<GSMDataLowQualityBlock> CurGSMDataLowQualityBlocks
        {
            get { return curGSMDataLowQualityBlocks; }
            set { curGSMDataLowQualityBlocks = value; }
        }

        private List<GSMDataDLRateMainBlock> gsmDataDLRateBlocks = new List<GSMDataDLRateMainBlock>();
        public List<GSMDataDLRateMainBlock> GSMDataDLRateBlocks
        {
            get { return gsmDataDLRateBlocks; }
            set { gsmDataDLRateBlocks = value; }
        }
 
        private List<CoverUnbalancedMainBlock> coverUnbalancedBlocks = new List<CoverUnbalancedMainBlock>();
        public List<CoverUnbalancedMainBlock> CoverUnbalancedBlocks
        {
            get { return coverUnbalancedBlocks; }
            set { coverUnbalancedBlocks = value; }
        }

        #region 文件标记
        public event EventHandler MarkedFileChanged;

        public void FireMarkedFileChanged(object sender, EventArgs e)
        {
            if (MarkedFileChanged != null)
            {
                MarkedFileChanged(sender, e);
            }
        }
        public List<MarkedFileGroup> MarkedFiles
        {
            get;
            set;
        }
        public void AddMarkedFileGroup(MarkedFileGroup grp)
        {
            if (this.MarkedFiles == null)
            {
                MarkedFiles = new List<MarkedFileGroup>();
            }
            MarkedFileGroup temp = MarkedFiles.Find(delegate(MarkedFileGroup x) { return x.Name.Equals(grp); });
            if (temp != null)
            {
                MarkedFiles.Remove(temp);
            }
            MarkedFiles.Add(grp);
            FireMarkedFileChanged(this, EventArgs.Empty);
        }

        public void RemoveMarkedFileGroup(object sender, MarkedFileGroup toRemove)
        {
            if (MarkedFiles != null && toRemove != null)
            {
                MarkedFiles.Remove(toRemove);
            }
            FireMarkedFileChanged(sender,EventArgs.Empty);
        }
        #endregion

        private bool showCellParam = false;
        public bool ShowCellParam
        {
            get { return showCellParam; }
            set { showCellParam = value; }
        }

        private bool initCellParamConfig = false;
        public bool InitCellParamConfig
        {
            get { return initCellParamConfig; }
            set { initCellParamConfig = value; }
        }

        ParameterManager parameterManager = Chris.Util.Singleton<ParameterManager>.GetInstance();
        public ParameterManager ParameterManager
        {
            get { return parameterManager; }
        }
        public string NOPDBSetting
        {
            get
            {
                MasterCom.RAMS.NOP.NopDbManager nopDbManager = MasterCom.RAMS.NOP.NopDbManager.Instance;
                if (nopDbManager.NopDbSetting != null)
                {
                    return nopDbManager.NopDbSetting.NopDBConnectStr;
                }
                return null;
            }
        }
        MasterCom.NOP.UM.User nopUser;
        public MasterCom.NOP.UM.User NopUser
        {
            get { return nopUser; }
            set { nopUser = value; }
        }

        private bool hasGetMSC = false;
        public bool HasGetMSC
        {
            get { return hasGetMSC; }
            set { hasGetMSC = value; }
        }

        private bool hasMSCData = false;
        public bool HasMSCData
        {
            get { return hasMSCData; }
            set { hasMSCData = value; }
        }

        private bool needType = false;
        public bool NeedType
        {
            get { return needType; }
            set { needType = value; }
        }

        public static string TD_SCell_LAC = "TD_SCell_LAC";
        public static string TD_SCell_CI = "TD_SCell_CI";
        public static string TD_SCell_UARFCN = "TD_SCell_UARFCN";
        public static string TD_SCell_CPI = "TD_SCell_CPI";
        public static string TD_GSM_SCell_LAC = "TD_GSM_SCell_LAC";
        public static string TD_GSM_SCell_CI = "TD_GSM_SCell_CI";
        public static string TD_GSM_SCell_ARFCN = "TD_GSM_SCell_ARFCN";
        public static string TD_GSM_SCell_BSIC = "TD_GSM_SCell_BSIC";
        public static string TD_GSM_NCell_ARFCN = "TD_GSM_NCell_ARFCN";
        public static string TD_GSM_NCell_BSIC = "TD_GSM_NCell_BSIC";
        public static string TD_SCell_Name = "TD_SCell_Name";
        public static string TD_GSM_SCell_Name = "TD_GSM_SCell_Name";
        public static string TD_TA = "TD_TA(chips)";

        public static string LTE_TAC = "lte_TAC";
        public static string LTE_ECI = "lte_ECI";
        public static string LTE_RSRP = "lte_RSRP";
        public static string LTE_RSRQ = "lte_RSRQ";
        public static string LTE_SINR = "lte_SINR";
        public static string LTE_PCI = "lte_PCI";
        public static string LTE_NCell_EARFCN = "lte_NCell_EARFCN";
        public static string LTE_NCell_PCI = "lte_NCell_PCI";
        public static string LTE_NCell_RSRP = "lte_NCell_RSRP";
        public static string LTE_GSM_C2I_C2I = "lte_gsm_PCCPCH_C2I";
        public static string LTE_GSM_SCell_LAC = "lte_gsm_SC_LAC";
        public static string LTE_GSM_SCell_CI = "lte_gsm_SC_CI";
        public static string LTE_GSM_SCell_BCCH = "lte_gsm_SC_BCCH";
        public static string LTE_GSM_SCell_BSIC = "lte_gsm_SC_BSIC";
        public static string LTE_GSM_SCell_Name = "lte_gsm_SC_Name";
        public static string LTE_GSM_NCell_BCCH = "lte_gsm_NC_BCCH";
        public static string LTE_GSM_NCell_BSIC = "lte_gsm_NC_BSIC";
        public static string LTE_GSM_NCell_Rxlev = "lte_gsm_NC_RxLev";
        public static string LTE_GSM_RxlevSub = "lte_gsm_DM_RxLevSub";
        public static string LTE_GSM_RxQualSub = "lte_gsm_DM_RxQualSub";

        public bool IsValidLonLat(double longitude, double latitude)
        {
#if DEBUG
            return true;
#endif
#if Shenzhen || Dongguan
            if (longitude < 110 || longitude > 120 || latitude < 20 || latitude > 25)
            {
                return false;
            }
#elif HLJLT ||Heilongjiang
            if (longitude < 120 || longitude > 135 || latitude < 40 || latitude > 55)
            {
                return false;
            }
#elif NMG
            if (longitude < 90 || longitude > 130 || latitude < 37 || latitude > 56)
            {
                return false;
            }
#elif Xian
            if (longitude < 105 || longitude > 112 || latitude < 31 || latitude > 40)
            {
                return false;
            }
#elif Shanxi
            if (longitude < 100 || longitude > 125 || latitude < 25 || latitude > 50)
            {
                return false;
            }
#elif Beijing
            if (longitude < 115 || longitude > 120 || latitude < 35 || latitude > 45)
            {
                return false;
            }
#elif ShanxiJin
            if (longitude < 110 || longitude > 115 || latitude < 34 || latitude > 41)
            {
                return false;
            }
#elif Shanghai
            if (longitude < 115 || longitude > 125 || latitude < 30 || latitude > 35)
            {
                return false;
            }
#elif Guizhou
            if (longitude < 103 || longitude > 110 || latitude < 24 || latitude > 30)
            {
                return false;
            }
#elif Suzhou
            if (longitude < 119 || longitude > 122 || latitude < 30 || latitude > 33)
            {
                return false;
            }
#elif Ningxia
            if (longitude < 105 || longitude > 107 || latitude < 37 || latitude > 40)
            {
                return false;
            }
#elif Xinjiang || XJLT || Wulumuqi
            if (longitude < 50 || longitude > 160 || latitude < 20 || latitude > 80)
            {
                return false;
            }
#elif Yunnan
            if (longitude < 97 || longitude > 107 || latitude < 21 || latitude > 30)
            {
                return false;
            }
#elif Shandong
            if (longitude < 123 || longitude > 114 || latitude < 33 || latitude > 40)
            {
                return false;
            }
#elif Chongqing
            if (longitude < 105 || longitude > 111 || latitude < 28 || latitude > 33)
            {
                return false;
            }
#elif Chengdu
            if (longitude < 101 || longitude > 106 || latitude < 29 || latitude > 33)
            {
                return false;
            }
#elif Guangdong
            if (longitude < 70 || longitude > 140 || latitude < 3 || latitude > 55)
            {
                return false;
            }
#elif Hubei
            if (longitude < 106 || longitude > 117 || latitude < 28 || latitude > 34)
            {
                return false;
            }
#elif Qinghai || QinghaiDX
            if (longitude < 80 || longitude > 110 || latitude < 3 || latitude > 55)
            {
                return false;
            }
#else
            if (longitude < 70 || longitude > 140 || latitude < 3 || latitude > 55)
            {
                return false;
            }
#endif
            return true;
        }

        public event EventHandler FileInfoFormStateChanged;
        public void FireFileInfoFormStateChanged(object sender, EventArgs e)
        {
            if (FileInfoFormStateChanged!=null)
            {
                FileInfoFormStateChanged(sender, e);
            }
        }

        private List<WeakCoverEvent> weakCoverEvents = new List<WeakCoverEvent>();
        public List<WeakCoverEvent> WeakCoverEvents
        {
            get { return weakCoverEvents; }
            set { weakCoverEvents = value; }
        }

        private List<WeakCoverEvent> cellUpdateEvents = new List<WeakCoverEvent>();
        public List<WeakCoverEvent> CellUpdateEvents 
        {
            get { return cellUpdateEvents; }
            set { cellUpdateEvents = value; }
        }

        private bool streetInjectMultiTables = false;
        public bool StreetInjectMultiTables
        {
            get { return streetInjectMultiTables; }
            set { streetInjectMultiTables = value; }
        }

        private Dictionary<int, List<StreetInjectTableInfo>> streetInjectTablesDic = new Dictionary<int, List<StreetInjectTableInfo>>();
        public Dictionary<int, List<StreetInjectTableInfo>> StreetInjectTablesDic
        {
            get { return streetInjectTablesDic; }
        }

        private List<StreetInjectTableInfo> streetInjectTablesList = new List<StreetInjectTableInfo>();
        public List<StreetInjectTableInfo> StreetInjectTablesList
        {
            get { return streetInjectTablesList; }
        }

        // 用于绘制小区云图
        public List<CellWeightRegionInfo> cellWeightRegionInfoList = new List<CellWeightRegionInfo>();
        public void ClearCellWeightRegion()
        {
            cellWeightRegionInfoList.Clear();
            tdCellWeightRegionInfoList.Clear();

            CellWeightRegionLayer.ClearLastLayerCache();
        }
        public List<TDCellWeightRegionInfo> tdCellWeightRegionInfoList = new List<TDCellWeightRegionInfo>();

        // 小区理想覆盖半径，该数组计算后不会被清空
        public Dictionary<Cell, double> cellRadiusDic = new Dictionary<Cell, double>();

        public Dictionary<TDCell, double> tdCellRadiusDic = new Dictionary<TDCell, double>();

        public List<CellCoverDistance> CellCoverDistanceList = new List<CellCoverDistance>();

        public Dictionary<BTSBandType, List<GridForIndexOfRoadStructure>> GridForIndexOfRoadStructureDic = new Dictionary<BTSBandType, List<GridForIndexOfRoadStructure>>();
        /// <summary>
        /// 道路结构指数
        /// </summary>
        public List<ColorRange> GridForIndexOfRoadStructureColorRanges = new List<ColorRange>();
        public BTSBandType GridForIndexOfRoadStructureShowBandType = BTSBandType.GSM900;
        public GridForIndexOfRoadStructure curGridForIndexOfRoadStructure;
        public void ClearIndexOfRoadStructure()
        {
            GridForIndexOfRoadStructureDic.Clear();
            curGridForIndexOfRoadStructure = null;
        }

        private List<FastFading> fastFadingList = null;
        public List<FastFading> FastFadingList
        {
            get 
            {
                if (fastFadingList == null)
                {
                    fastFadingList = new List<FastFading>();
                }
                return fastFadingList; 
            }
            set { fastFadingList = value; }
        }

        private List<GridFastFading> gridFastFadingList = null;
        public List<GridFastFading> GridFastFadingList
        {
            get
            {
                if (gridFastFadingList == null)
                {
                    gridFastFadingList = new List<GridFastFading>();
                }
                return gridFastFadingList;
            }
            set { gridFastFadingList = value; }
        }

        public void ClearFastFading()
        {
            fastFadingList = null;
            gridFastFadingList = null;
        }

        private List<FrequencyShortage> frequencyShortageList = null;
        public List<FrequencyShortage> FrequencyShortageList
        {
            get
            {
                if (frequencyShortageList == null)
                {
                    frequencyShortageList = new List<FrequencyShortage>();
                }
                return frequencyShortageList;
            }
            set { frequencyShortageList = value; }
        }

        private List<GridFrequencyShortage> gridFrequencyShortageList = null;
        public List<GridFrequencyShortage> GridFrequencyShortageList
        {
            get
            {
                if (gridFrequencyShortageList == null)
                {
                    gridFrequencyShortageList = new List<GridFrequencyShortage>();
                }
                return gridFrequencyShortageList;
            }
            set { gridFrequencyShortageList = value; }
        }

        public void ClearFrequencyShortage()
        {
            frequencyShortageList = null;
            gridFrequencyShortageList = null;
        }
        public List<ResvRegion> StreetInjectResvRegions = new List<ResvRegion>();

        //=====深圳考核
        /// <summary>
        /// 深圳重复问题点
        /// </summary>
        public Dictionary<int, BlackBlock_Point> BlackBlockPointsDic = new Dictionary<int, BlackBlock_Point>();

        /// <summary>
        /// 深圳栅格问题点, ID, gridproblem
        /// </summary>
        public Dictionary<int, GridProblem> AllGridProblemsDic = new Dictionary<int, GridProblem>();
        public Dictionary<int, GridProblem> CurGridProblemDic = new Dictionary<int, GridProblem>();
        public GridProblem CurGridProblem;
        public List<ColorRange> GridProblemColorRanges = new List<ColorRange>();

        /// <summary>
        /// 深圳道路问题点, ID, roadproblem
        /// </summary>
        public Dictionary<int, RoadProblem> AllRoadProblemsDic = new Dictionary<int, RoadProblem>();
        public Dictionary<int, RoadProblem> CurRoadProblemDic = new Dictionary<int, RoadProblem>();
        //=================


        public event EventHandler SaveMWSCommandFired;
        public void FireSaveMWSCommandFired()
        {
            if (SaveMWSCommandFired != null)
            {
                SaveMWSCommandFired(this, null);
            }
        }

        private List<TDCell> neighbourEachOtherTDCells = new List<TDCell>();

        public List<TDCell> NeighbourEachOtherTDCells
        {
            get { return neighbourEachOtherTDCells; }
        }

        public List<ScanOverlapPoint> CurOverlapPointList = new List<ScanOverlapPoint>();
        public List<OverlapSubCellInfo> CurOverlapInfoList = new List<OverlapSubCellInfo>();
        public bool ShowOverlapLine = false;
        public bool ShowNearestLine = false;

        /// <summary>
        /// 是否启动后台程序模式
        /// </summary>
        public bool IsBackground
        {
            get
            {
#if ProblemCheck_Background
                if (this.HasShowBackgroundFunc)
                {
                    return systemConfigInfo.isBackground;
                }
#endif
                return false;

            }
        }
        public bool HasShowBackgroundFunc = false;
        public static bool CheckIsRightMastercomPwd()
        {
            return CheckIsRightMastercomPwd("口令验证", "mastercom168");
        }
        public static bool CheckIsRightMastercomPwd(string formText)
        {
            return CheckIsRightMastercomPwd(formText, "mastercom168");
        }
        public static bool CheckIsRightMastercomPwd(string formText, string strCommand)
        {
            TextInputBox box = new TextInputBox(formText, "口令", string.Empty);
            box.SetPwdMode(true);
            if (box.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            if (box.TextInput != strCommand)
            {
                MessageBox.Show("口令错误");
                return false;
            }
            return true;
        }

        private bool queryFromBackground = false;
        public bool QueryFromBackground
        {
            get { return queryFromBackground; }
            set { queryFromBackground = value; }
        }

        public bool BackgroundStarted = false;

        public bool BackgroundStopRequest = false;

        private List<BackgroundResult> curBackgroundResultList;
        public List<BackgroundResult> CurBackgroundResultList
        {
            get
            {
                if (curBackgroundResultList == null)
                {
                    curBackgroundResultList = new List<BackgroundResult>();
                }
                return curBackgroundResultList;
            }
            set { curBackgroundResultList = value; }
        }

        private BackgroundResult curBackgroundResult;
        public BackgroundResult CurBackgroundResult
        {
            get { return curBackgroundResult; }
            set { curBackgroundResult = value; }
        }

        public MasterCom.RAMS.CQT.CQTPoint SelCQTPoint = null;
        public CQTLibrary.PublicItem.EvaluateResult SelCQTPoint2 = null;  //专题GIS 选中CQT

        //GSM扫频干扰指数========================
        public List<InterfereFactorSample> CurScanInterfereSampleList = new List<InterfereFactorSample>();
        public InterfereFactorSample CurScanInterfereSample = null;

        private List<ColorRange> scanInterfereFactorColorRanges = new List<ColorRange>();
        public List<ColorRange> ScanInterfereFactorColorRanges
        {
            get { return scanInterfereFactorColorRanges; }
            set { scanInterfereFactorColorRanges = value; }
        }

        public void ClearScanInterfereSample()
        {
            CurScanInterfereSampleList.Clear();
            CurScanInterfereSample = null;
        }
        //GSM扫频干扰指数========================

        private bool isShowCoCellLine = true;
        public bool IsShowCoCellLine
        {
            get { return isShowCoCellLine; }
            set { isShowCoCellLine = value; }
        }

        public bool IsNeedHideKeyInfo
        {
            get
            {
#if PermissionControl_DataExport
                if (user == null || CurFuncLogItem == null)
                {
                    return false;
                }
                else
                {
                    //如果开启脱敏,判断各个功能点对应是否脱敏
                    FuncExportPermit exportPermit = ExportFuncResultManager.GetInstance().GetFuncExportPermit(CurFuncLogItem);
                    return exportPermit.IsHideKeyInfo;
                }
#endif
                return false;
            }
        }
        public Dictionary<TestPoint, List<ICell>> CoCellTestPointDic
        {
            get;
            set;
        }


        public List<AtomicPolygon> CurSelPolygons
        {
            get;
            set;
        }

        public int CurMS { get; set; }

        internal object CreateResultForm(Type type)
        {
            object ret = GetObjectFromBlackboard(type);
            if (ret==null)
            {
                ret = type.Assembly.CreateInstance(type.FullName);
            }
            return ret;
        }

        /// <summary>
        /// 当前执行的专题功能信息
        /// </summary>
        public MasterCom.RAMS.UserMng.LogInfoItem CurFuncLogItem { get; set; }
        public bool DebugFlag { get; set; }

#if PermissionControl_Func
        private bool? isDateLimited = null;
        /// <summary>
        /// 用户账号是否需要对查询日期进行限制,true为需要限制
        /// </summary>
        public bool IsDateLimited
        {
            get
            {
                if (isDateLimited == null)
                {
                    return false;
                }
                return (bool)isDateLimited;
            }
        }

        public void SetDateLimit()
        {
            isDateLimited = user.HasFunctionRight(414);
        }
#endif
        /// <summary>
        /// 遍历控件设置日期限制
        /// </summary>
        /// <param name="controls"></param>
        public void DeadlineInfo(Control.ControlCollection controls)
        {
            foreach (Control item in controls)
            {
                if (item.HasChildren)
                {
                    DeadlineInfo(item.Controls);
                }

                if (item is DateTimePicker)
                {
                    DateTimePicker dateTimePicker = item as DateTimePicker;
                    dateTimePicker.MinDate = mainForm.DeadlineInfo;
                }
                else if (item is DevExpress.XtraEditors.DateEdit)
                {
                    DevExpress.XtraEditors.DateEdit dateEdit = item as DevExpress.XtraEditors.DateEdit;
                    dateEdit.Properties.MinValue = mainForm.DeadlineInfo;
                }

                //if (item is TabControl)
                //{
                //    TabControl tab = item as TabControl;
                //    foreach (TabPage page in tab.TabPages)
                //    {
                //        DeadlineInfo(page.Controls);
                //    }
                //}
                //else if (item is DevExpress.XtraTab.XtraTabControl)
                //{
                //    DevExpress.XtraTab.XtraTabControl tab = item as DevExpress.XtraTab.XtraTabControl;
                //    foreach (DevExpress.XtraTab.XtraTabPage page in tab.TabPages)
                //    {
                //        DeadlineInfo(page.Controls);
                //    }
                //}
                //else if (item is SplitContainer || item is Panel || item is GroupBox || item is UserControl
                //    || item is ContainerControl || item is DevExpress.XtraEditors.XtraPanel || item is DevExpress.XtraEditors.SplitterControl
                //    || item is DevExpress.XtraEditors.SplitContainerControl || item is DevExpress.XtraEditors.PanelControl
                //    || item is DevExpress.XtraEditors.GroupControl || item is DevExpress.XtraEditors.XtraUserControl)
                //{
                //    DeadlineInfo(item.Controls);
                //}
                //else if (item is DateTimePicker)
                //{
                //    DateTimePicker dateTimePicker = item as DateTimePicker;
                //    dateTimePicker.MinDate = mainForm.DeadlineInfo;
                //}
                //else if (item is DevExpress.XtraEditors.DateEdit)
                //{
                //    DevExpress.XtraEditors.DateEdit dateEdit = item as DevExpress.XtraEditors.DateEdit;
                //    dateEdit.Properties.MinValue = mainForm.DeadlineInfo;
                //}
            }
        }
    }
    /// <summary>
    /// 记录用户历史操作信息的类
    /// </summary>
    public class QueryInfo {
        public QueryBase QueryClass = null;         ////查询类
        public LayerBase QueryLayer = null;         ////对应图层类
        public MinCloseForm QueryForm = null;         ////对应窗体
        public object QueryData = null;         ////查询数据
        public string QueryRemark = "";         ////查询描述
        public QueryInfo(QueryBase queryClass, LayerBase queryLayer, MinCloseForm queryForm, string queryRemark, object queryData)
        {
            this.QueryClass = queryClass;
            this.QueryLayer = queryLayer;
            this.QueryForm = queryForm;
            this.QueryData = queryData;
            this.QueryRemark = queryRemark;
        }
        public override string ToString()
        {
            return this.QueryRemark.ToString();
        }
    }

    public class  CommonEventArgs: EventArgs
    {
        public string name = "";
        public object info;
    }
}
