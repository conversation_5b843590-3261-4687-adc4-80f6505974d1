﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class MessageSelectForm : BaseDialog
    {
        public MessageSelectForm()
        {
            InitializeComponent();
            this.SelectedMessageID = -1;

            this.Load += Form_Load;
            treeView.AfterCheck += TreeView_AfterCheck;
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            txtFilter.TextChanged += TxtFilter_TextChanged;

            treeView.BeginUpdate();
            curCheckedNode = null;
            LoadMessages(treeView.Nodes, MessageInfoManager.GetInstance().Messages, txtFilter.Text);
            treeView.EndUpdate();
        }

        public int SelectedMessageID
        {
            get;
            set;
        }

        private void Form_Load(object sender, EventArgs e)
        {
            //
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            List<TreeNode> checkedNodes = GetCheckedNodes();
            if (checkedNodes.Count == 0)
            {
                MessageBox.Show("至少选中一个信令", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
                return;
            }
            SelectedMessageID = (checkedNodes[0].Tag as MessageInfo).ID;
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            treeView.AfterCheck -= TreeView_AfterCheck;
            treeView.BeginUpdate();
            treeView.Nodes.Clear();
            curCheckedNode = null;
            LoadMessages(treeView.Nodes, MessageInfoManager.GetInstance().Messages, txtFilter.Text);
            treeView.EndUpdate();
            treeView.AfterCheck += TreeView_AfterCheck;
        }

        private void TreeView_AfterCheck(object sender, TreeViewEventArgs e)
        {
            bool isChecked = e.Node.Checked;
            treeView.AfterCheck -= TreeView_AfterCheck;
            treeView.BeginUpdate();
            DisableCheck();
            if (isChecked)
            {
                EnableCheck(e.Node);
            }
            treeView.EndUpdate();
            treeView.AfterCheck += TreeView_AfterCheck;
        }

        private void LoadMessages(TreeNodeCollection nodes, List<MessageInfo> messages, string filterString)
        {
            foreach (MasterCom.RAMS.Model.MessageInfo message in messages)
            {
                if (!string.IsNullOrEmpty(filterString)
                    && message.Name.IndexOf(filterString, StringComparison.CurrentCultureIgnoreCase) == -1
                    && !message.IsMessageCollection())
                {
                    continue;
                }
                TreeNode node = new TreeNode();
                node.Text = message.Name;
                node.Tag = message;
                nodes.Add(node);
                if (message.IsMessageCollection())
                {
                    LoadMessages(node.Nodes, message.Messages, filterString);
                }
            }

            for (int i = 0; i < nodes.Count; ++i)
            {
                if (nodes[i].Nodes.Count == 0 && nodes[i].Tag is MessageCollection)
                {
                    nodes.RemoveAt(i);
                    --i;
                }
            }
        }

        private List<TreeNode> GetCheckedNodes()
        {
            List<TreeNode> retList = new List<TreeNode>();
            Queue<TreeNode> queue = new Queue<TreeNode>();
            foreach (TreeNode node in treeView.Nodes)
            {
                queue.Enqueue(node);
            }

            while (queue.Count > 0)
            {
                TreeNode curNode = queue.Dequeue();
                if (curNode.Nodes.Count == 0)
                {
                    if (curNode.Checked)
                    {
                        retList.Add(curNode);
                    }
                    continue;
                }

                foreach (TreeNode childNode in curNode.Nodes)
                {
                    queue.Enqueue(childNode);
                }
            }

            return retList;
        }

        private void DisableCheck()
        {
            while (curCheckedNode != null)
            {
                curCheckedNode.Checked = false;
                curCheckedNode = curCheckedNode.Parent;
            }
        }

        private void EnableCheck(TreeNode node)
        {
            if (node != null)
            {
                while (node.Nodes.Count > 0)
                {
                    node = node.Nodes[0];
                }
                curCheckedNode = node;

                while (node != null)
                {
                    node.Checked = true;
                    node = node.Parent;
                }
            }
        }

        private TreeNode curCheckedNode;
    }
}
