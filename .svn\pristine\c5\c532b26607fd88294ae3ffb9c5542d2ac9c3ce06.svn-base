using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class InterferenceSelectionPanel : UserControl
    {
        public InterferenceSelectionPanel(ToolStripDropDown dropDown, ToolBarButton toolBarButtonShowInterference)
        {
            InitializeComponent();
            this.parentDropDown = dropDown;
            this.toolBarButtonShowInterference = toolBarButtonShowInterference;
        }

        public void initializePanel()
        {
            rbtnAllMap.Checked = IsWholeMap;
            rbtnDistance.Checked = !IsWholeMap;
            numericUpDownDistance.Value = (decimal)Distance;
            cbxCoFreq.Checked = IsCoFreq;
            cbxAdjFreq.Checked = IsAdjFreq;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            IsWholeMap = rbtnAllMap.Checked;
            Distance = (double)numericUpDownDistance.Value;
            IsCoFreq = cbxCoFreq.Checked;
            IsAdjFreq = cbxAdjFreq.Checked;
            this.parentDropDown.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.parentDropDown.Close();
        }

        private void rbtnDistance_CheckedChanged(object sender, EventArgs e)
        {
            numericUpDownDistance.Enabled = rbtnDistance.Checked;          
        }

        public bool IsWholeMap { get; set; } = true;

        public double Distance { get; set; }

        public bool IsCoFreq { get; set; } = true;

        public bool IsAdjFreq { get; set; } = true;

        private ToolStripDropDown parentDropDown;

        private ToolBarButton toolBarButtonShowInterference;
    }
}
