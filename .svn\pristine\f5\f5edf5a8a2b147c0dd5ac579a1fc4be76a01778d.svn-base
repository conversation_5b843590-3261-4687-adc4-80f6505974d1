using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.Func.ProblemCell
{
    public partial class SampleExtentSettingForm : Form
    {
        public SampleExtentSettingForm()
        {
            InitializeComponent();
            rangeSettingPESQ.NumericUpDownMin.Increment = 0.1M;
            rangeSettingPESQ.NumericUpDownMin.DecimalPlaces = 1;
            rangeSettingPESQ.NumericUpDownMax.Increment = 0.1M;
            rangeSettingPESQ.NumericUpDownMax.DecimalPlaces = 1;
            rangeSettingPESQ.RangeAll = new Range(0, true, 5, true);
          
            rangeSettingLev.RangeAll = new Range(-120, true, -10, false);
            rangeSettingQual.RangeAll = new Range(0, true, 7, true);
        }
        public Range PESQRange
        {
            get { return rangeSettingPESQ.Range; }
        }
        public Range QualRange
        {
            get { return rangeSettingQual.Range; }
        }
        public Range LevRange
        {
            get { return rangeSettingLev.Range; }
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}