﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYLowSpeedCellBase_NR : DIYAnalyseByCellBackgroundBaseByFile
    {
        protected string themeName { get; set; } = "";//默认选中指标
        protected virtual string rsrpName { get { return "NR_SS_RSRP"; } }
        protected virtual string sinrName { get { return "NR_SS_SINR"; } }

        private LowSpeedCellCond_NR settingCond;
        private Dictionary<ICell, LowSpeedCell_NR> cellItemDict = new Dictionary<ICell, LowSpeedCell_NR>();

        protected static readonly object lockObj = new object();

        protected ZTDIYLowSpeedCellBase_NR()
           : base(MainModel.GetInstance())
        {
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            carrierID = CarrierType.ChinaMobile;
            init();
        }

        protected void init()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, false); 
            Columns.Add("NR_APP_type");
            Columns.Add("NR_APP_Speed");
            Columns.Add("NR_APP_Status");
            Columns.Add("NR_APP_ThroughputDL");
            Columns.Add("NR_APP_ThroughputUL");
            Columns.Add("NR_FTP_Download_Rate");
            Columns.Add("NR_FTP_Upload_Rate");
            Columns.Add("NR_Http_Download_Rate");
            Columns.Add("NR_Http_Upload_Rate");
            Columns.Add("NR_Http_Page_Rate");
            Columns.Add("NR_Email_POP_Rate");
            Columns.Add("NR_Email_SMTP_Rate");

            Columns.Add("NR_Throughput_MAC_DL");
            Columns.Add("NR_Throughput_MAC_UL");
            Columns.Add("NR_Throughput_PDCP_DL");
            Columns.Add("NR_Throughput_PDCP_UL");

            Columns.Add("NR_16QAM_Count_DL");
            Columns.Add("NR_64QAM_Count_DL");
            Columns.Add("NR_256QAM_Count_DL");
            Columns.Add("NR_BPSK_Count_DL");
            Columns.Add("NR_QPSK_Count_DL");

            themeName = "NR:SS_RSRP";
        }

        protected override bool getCondition()
        {
            LowSpeedCellDlg_NR settingForm = new LowSpeedCellDlg_NR();
            settingForm.SetCondition(settingCond);
            if (settingForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            settingCond = settingForm.GetCondition();
            return true;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            cellItemDict = new Dictionary<ICell, LowSpeedCell_NR>();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        doWithTestPoint(tp);
                    }
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        protected virtual void doWithTestPoint(TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp, true);
            if (rsrp == null || sinr == null)
            {
                return;
            }

            ICell cell = tp.GetMainCell();
            if (cell == null)
            {
                return;
            }
            if (!cellItemDict.ContainsKey(cell))
            {
                LowSpeedCell_NR lowSpeedCell = new LowSpeedCell_NR(cell);
                lowSpeedCell.AreaName = GISManager.GetInstance().GetAreaPlaceDesc(cell.Longitude, cell.Latitude);
                cellItemDict.Add(cell, lowSpeedCell);
            }
            cellItemDict[cell].TestPoints.Add(tp);

            List<LowSpeedCellCond_NR.SpeedTypeInfo> speedInfo =  judgeLowSpeedTp(rsrp, sinr, tp);
            cellItemDict[cell].AddPoint((double)rsrp, (double)sinr, speedInfo, tp);
        }

        protected List<LowSpeedCellCond_NR.SpeedTypeInfo> judgeLowSpeedTp(double? rsrp, double? sinr, TestPoint tp)
        {
            List<LowSpeedCellCond_NR.SpeedTypeInfo> speedList = settingCond.GetValidSpeed(tp);
            //在rsrp,sinr范围外算是低速率点
            if (sinr < settingCond.MinSinr || sinr > settingCond.MaxSinr
              || rsrp < settingCond.MinRsrp || rsrp > settingCond.MaxRsrp)
            {
                foreach (var item in speedList)
                {
                    item.IsValid = true;
                }
            }

            return speedList;
        }

        protected override void getResultsAfterQuery()
        {
            Dictionary<ICell, LowSpeedCell_NR> tmpDic = new Dictionary<ICell, LowSpeedCell_NR>();
            foreach (KeyValuePair<ICell, LowSpeedCell_NR> kvp in cellItemDict)
            {
                LowSpeedCell_NR cell = kvp.Value;
                cell.Calculate();

                if (cell.ProblemCount < settingCond.MinProblemCount || cell.ProblemRate < settingCond.MinProblemRate)
                {
                    continue;
                }
                tmpDic.Add(kvp.Key, cell);
            }
            cellItemDict = tmpDic;
        }

        protected override void fireShowForm()
        {
            setThemeName();
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            LowSpeedCellForm_NR cellForm = MainModel.GetInstance().CreateResultForm(typeof(LowSpeedCellForm_NR)) as LowSpeedCellForm_NR;
            cellForm.FillData(new List<LowSpeedCell_NR>(cellItemDict.Values));
            if (!cellForm.Visible)
            {
                cellForm.Show(MainModel.MainForm);
            }
            cellItemDict = null;
        }

        private void setThemeName()
        {
            if (settingCond.IsAppSpeed)
            {
                themeName = "NR:APP_Speed";
            }
            else
            {
                if (settingCond.ThroughputTypeInfo == LowSpeedCellCond_NR.ThroughputType.MAC)
                {
                    themeName = "NR:Throughput_MAC_DL_Mb";
                }
                else
                {
                    themeName = "NR:Throughput_PDCP_DL_Mb";
                }
            }
        }
    }

    public class ZTDIYLowSpeedCellByRegion_NR : ZTDIYLowSpeedCellBase_NR
    {
        private ZTDIYLowSpeedCellByRegion_NR()
                : base()
        {
            FilterSampleByRegion = true;
        }

        private static ZTDIYLowSpeedCellByRegion_NR instance = null;
        public static ZTDIYLowSpeedCellByRegion_NR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDIYLowSpeedCellByRegion_NR();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "低速率小区_NR(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35013, this.Name);
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is TestPoint_NR && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return true;
            }
            return false;
        }
    }

    public class ZTDIYLowSpeedCellByFile_NR : ZTDIYLowSpeedCellBase_NR
    {
        private ZTDIYLowSpeedCellByFile_NR()
            : base()
        {
        }

        private static ZTDIYLowSpeedCellByFile_NR instance = null;
        public static ZTDIYLowSpeedCellByFile_NR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDIYLowSpeedCellByFile_NR();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "低速率小区_NR(按文件)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35013, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
