﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TerminalSatisfactionReportForm : MinCloseForm
    {
        public TerminalSatisfactionReportForm()
            : base()
        {
            InitializeComponent();
            init();
        }

        public TerminalSatisfactionReportForm(MainModel mainmodel)
            : base(mainmodel)
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            ZTTerminalCBXItemSQLQuery cbxQuery = new ZTTerminalCBXItemSQLQuery(MainModel);
            cbxQuery.Query();
            cbxCity.Items.Clear();
            foreach (string city in cbxQuery.CityList)
            {
                cbxCity.Items.Add(city);
            }
            cbxCity.Items.Add("全部");
            cbxCity.Text = "全部";
            cbxMonth.Items.Clear();
            foreach (string month in cbxQuery.MonthList)
            {
                cbxMonth.Items.Add(month);
            }
            cbxMonth.Items.Add("全部");
            cbxMonth.SelectedIndex = 0;
            cbxQuestionType.Items.Clear();
            foreach (string questiontype in cbxQuery.QuestionTypeList)
            {
                cbxQuestionType.Items.Add(questiontype);
            }
            cbxQuestionType.Items.Add("全部");
            cbxQuestionType.Text = "全部";
        }

        private BindingSource bindingSource = new BindingSource();

        public void FillData(List<TerminalStatData> terminalStatDatas)
        {
            bindingSource = new BindingSource();
            bindingSource.DataSource = typeof(TerminalStatData);
            foreach (TerminalStatData terminalStatData in terminalStatDatas)
            {
                bindingSource.Add(terminalStatData);
            }
            this.gvResult.DataSource = bindingSource;
        }

        private void btnQuery_Click(object sender, EventArgs e)
        {
            ZTTerminalSatisfactionSQLQuery query = new ZTTerminalSatisfactionSQLQuery(this.MainModel);
            query.SetCity(cbxCity.Text == null || cbxCity.Text == "全部" || cbxCity.Text.Trim() == "" ? null : cbxCity.Text);
            query.SetMonth(cbxMonth.Text);
            query.SetRequestionType (cbxQuestionType.Text == null || cbxQuestionType.Text == "全部" || cbxQuestionType.Text.Trim() == "" ? null : cbxQuestionType.Text);
            query.Query();
        }

        private void menuItemExport_Click(object sender, EventArgs e)
        {
            if (gvResult.Rows.Count > 0)
            {
                ExcelNPOIManager.ExportToExcel(gvResult);
            }
        }
    }
}
