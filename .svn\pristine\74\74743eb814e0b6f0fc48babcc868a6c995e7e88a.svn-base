﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTCsfbCallStat
{
    public class SingleCallStatInfo
    {
        public SingleCallStatInfo(CallInfo moCall, CallInfo mtCall)
        {
            if (moCall != null)
            {
                this.MoFileName = moCall.FileName;
                this.OneEvent = moCall.Events[0];
                if (moCall.EvtCallAttempt != null)
                {
                    this.MoCallAttemptTime = moCall.EvtCallAttempt.DateTime;
                    this.MoLng = moCall.EvtCallAttempt.Longitude;
                    this.MoLat = moCall.EvtCallAttempt.Latitude;
                }

                this.MoPreCallNetType = moCall.PreCallNetType;
                this.MoTac = moCall.Tac;
                this.MoEci = moCall.Eci;
                this.MoEnodebid = moCall.Enodebid;
                this.MoCellid = moCall.Cellid;

                this.MoMsgCallProceeding = moCall.MsgCallProceeding;
                this.MoRsrp = moCall.Rsrp;
                this.MoLac = moCall.Lac;
                this.MoCi = moCall.Ci;
                this.MoRxLev_Rscp = moCall.RxLev_Rscp;
                this.MoCallNetType = moCall.CallNetType;
                this.MoBackResult = moCall.BackResult;
                this.MoCallResultDesc = moCall.CallResultDesc;
                this.MoExRequest2Alerting = moCall.ExRequest2Alerting;
                this.MoExRequest2Release = moCall.ExRequest2Release;
                this.MoRelease2SysInfo = moCall.Release2SysInfo;
                this.MoSysInfo2CmRequest = moCall.SysInfo2CmRequest;
                this.MoCmRequest2CmAccept = moCall.CmRequest2CmAccept;
                this.MoCmRequest2AuRequest = moCall.CmRequest2AuRequest;
                this.MoAuRequest2AuResponse = moCall.AuRequest2AuResponse;
                this.MoAuResponse2CmAccept = moCall.AuResponse2CmAccept;
                this.MoCmAccept2Setup = moCall.CmAccept2Setup;
                this.MoCmRequest2Setup = moCall.CmRequest2Setup;
                this.MoSetup2Proc = moCall.Setup2Proc;
                this.MoProc2AssignmentCmd = moCall.Proc2AssignmentCmd;
                this.MoAssigmentCmd2Complete = moCall.AssigmentCmd2Complete;
                this.MoComplete2Alerting = moCall.Complete2Alerting;
                this.MoCallProceed2ChannelModify = moCall.CallProceed2ChannelModify;
                this.MoChannelModify2ChannelAcknowledge = moCall.ChannelModify2ChannelAcknowledge;
                this.MoChannelAcknowledge2Alerting = moCall.ChannelAcknowledge2Alerting;
                this.MoCallProceeding2Alerting = moCall.CallProceeding2Alerting;
            }

            if (mtCall != null)
            {
                if (OneEvent == null)
                {
                    this.OneEvent = mtCall.Events[0];
                }
                this.MtFileName = mtCall.FileName;
                if (mtCall.EvtCallAttempt != null)
                {
                    this.MtCallAttemptTime = mtCall.EvtCallAttempt.DateTime;
                    this.MtLng = mtCall.EvtCallAttempt.Longitude;
                    this.MtLat = mtCall.EvtCallAttempt.Latitude;
                }

                this.MtMsgMtPaging = mtCall.MsgMtPaging;
                this.MtMsgServiceNotification = mtCall.MsgServiceNotification;
                this.MtPreCallNetType = mtCall.PreCallNetType;
                this.MtTac = mtCall.Tac;
                this.MtEci = mtCall.Eci;
                this.MtCellid = mtCall.Cellid;
                this.MtEnodebid = mtCall.Enodebid;
                this.MtRsrp = mtCall.Rsrp;
                this.MtLac = mtCall.Lac;
                this.MtCi = mtCall.Ci;
                this.MtRxLev_Rscp = mtCall.RxLev_Rscp;
                this.MtCallNetType = mtCall.CallNetType;
                this.MtBackResult = mtCall.BackResult;
                this.MtCallResultDesc = mtCall.CallResultDesc;
                this.MtPaging2Alerting = mtCall.Paging2Alerting;
                this.MtPaging2ExRequest = mtCall.Paging2ExRequest;
                this.MtExRequest2Release = mtCall.ExRequest2Release;
                this.MtRelease2SysInfo = mtCall.Release2SysInfo;
                this.MtSysInfo2Response = mtCall.SysInfo2Response;
                this.MtResponse2Setup = mtCall.Response2Setup;
                this.MtPResponse2AuRequest = mtCall.PResponse2AuRequest;
                this.MtAuRequest2AuResponse = mtCall.MtAuRequest2AuResponse;
                this.MtAuResponse2Setup = mtCall.AuResponse2Setup;
                this.MtSetup2ClConfirmed = mtCall.Setup2ClConfirmed;
                this.MtClConfirmed2AsCommand = mtCall.ClConfirmed2AsCommand;
                this.MtAsCommand2AsComplete = mtCall.AsCommand2AsComplete;
                this.MtResponse2Confirmed = mtCall.Response2Confirmed;
                this.MtResponse2AssigmentCmd = mtCall.Response2AssigmentCmd;
                this.MtResponse2Complete = mtCall.Response2Complete;
                this.MtComplete2Alerting = mtCall.Complete2Alerting;
            }

        }

        public int Sn { get; set; }
        public string MoFileName { get; private set; }
        public string MtFileName { get; private set; }

        public DateTime? MoCallAttemptTime { get; private set; }
        public double? MoLng { get; private set; }
        public double? MoLat { get; private set; }
        public string MoPreCallNetType { get; private set; }
        public int? MoTac { get; private set; }
        public int? MoEci { get; private set; }
        public int? MoEnodebid { get; private set; }
        public int? MoCellid { get; private set; } 

        public float? MoRsrp { get; private set; }
        public int? MoLac { get; private set; }
        public int? MoCi { get; private set; }
        public float? MoRxLev_Rscp { get; private set; }
        public string MoCallNetType { get; private set; }
        public string MoBackResult { get; private set; }
        public string MoCallResultDesc { get; set; }
        public int? MoExRequest2Alerting { get; private set; }
        public int? MoExRequest2Release { get; private set; }
        public int? MoRelease2SysInfo { get; private set; }
        public int? MoSysInfo2CmRequest { get; private set; }
        public int? MoCmRequest2CmAccept { get; private set; }
        public int? MoCmRequest2AuRequest { get; private set; }
        public int? MoAuRequest2AuResponse { get; private set; }
        public int? MoAuResponse2CmAccept { get; private set; }
        public int? MoCmAccept2Setup { get; private set; }
        public int? MoCmRequest2Setup { get; private set; }
        public int? MoSetup2Proc { get; private set; }
        public int? MoProc2AssignmentCmd { get; private set; }
        public int? MoAssigmentCmd2Complete { get; private set; }
        public int? MoComplete2Alerting { get; private set; }

        public int? MoCallProceed2ChannelModify { get; private set; }
        public int? MoChannelModify2ChannelAcknowledge { get; private set; }
        public int? MoChannelAcknowledge2Alerting { get; private set; }
        public int? MoCallProceeding2Alerting { get; private set; }

        public Message MoMsgCallProceeding { get; set; }
        public Message MtMsgServiceNotification { get; set; }
        public Message MtMsgMtPaging { get; set; }

        public int? CallProceeding2Paging
        {
            get
            {
                int? secd = null; 
                if (MoMsgCallProceeding != null)
                {
                    if (MtMsgServiceNotification != null)
                    {
                        if (MtMsgServiceNotification.DateTime >= MoMsgCallProceeding.DateTime)
                        {
                            secd = (int)(MtMsgServiceNotification.DateTime - MoMsgCallProceeding.DateTime).TotalMilliseconds;
                        }
                    }
                    else if (MtMsgMtPaging != null
                        && MtMsgMtPaging.DateTime >= MoMsgCallProceeding.DateTime)
                    {
                        secd = (int)(MtMsgMtPaging.DateTime - MoMsgCallProceeding.DateTime).TotalMilliseconds;
                    }
                }
                return secd;
            }
        }

        public int? MoProceeding2MtPaging
        {
            get
            {
                if (MoProc2AssignmentCmd != null && MoAssigmentCmd2Complete != null && MoComplete2Alerting != null && MtPaging2Alerting != null)
                {
                    return MoProc2AssignmentCmd + MoAssigmentCmd2Complete + MoComplete2Alerting - MtPaging2Alerting;
                }
                return null;
            }
        }

        public DateTime? MtCallAttemptTime { get; set; }

        public double? MtLng { get; set; }

        public double? MtLat { get; set; }

        public int? MtTac { get; set; }
        public int? MtEci { get; set; }
        public int? MtEnodebid { get; private set; }
        public int? MtCellid { get; private set; }

        public string MtPreCallNetType { get; set; }

        public float? MtRsrp { get; set; }

        public int? MtLac { get; set; }

        public int? MtCi { get; set; }

        public float? MtRxLev_Rscp { get; set; }

        public string MtCallNetType { get; set; }
        public string MtBackResult { get; private set; }

        public string MtCallResultDesc { get; set; }

        public int? MtPaging2Alerting { get; private set; }
        public int? MtPaging2ExRequest { get; private set; }
        public int? MtExRequest2Release { get; private set; }
        public int? MtRelease2SysInfo { get; private set; }
        public int? MtSysInfo2Response { get; private set; }
        public int? MtResponse2Setup { get; private set; }
        public int? MtPResponse2AuRequest { get; private set; }
        public int? MtAuRequest2AuResponse { get; private set; }
        public int? MtAuResponse2Setup { get; private set; }
        public int? MtSetup2ClConfirmed { get; private set; }
        public int? MtClConfirmed2AsCommand { get; private set; }
        public int? MtAsCommand2AsComplete { get; private set; }
        public int? MtResponse2Confirmed { get; private set; }
        public int? MtResponse2AssigmentCmd { get; private set; }
        public int? MtResponse2Complete { get; private set; }
        public int? MtComplete2Alerting { get; private set; }

        public Model.Event OneEvent { get; private set; }
    }
}
