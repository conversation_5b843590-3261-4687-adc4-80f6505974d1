﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.Net
{
    public abstract class DIYCellGridQueryBase : DIYGridQuery
    {
        protected DIYCellGridQueryBase(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.cell_grid;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                WaitBox.Text = "开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                MapGridLayer gridLayer = this.MainModel.MainForm.GetMapForm().GetGridShowLayer();
                List<GridColorModeItem> modeItems = new List<GridColorModeItem>();
                if (gridLayer.CurUsingColorModeList != null && gridLayer.CurUsingColorModeList.Count > 0)
                {
                    modeItems.AddRange(gridLayer.CurUsingColorModeList);
                }
                else
                {
                    modeItems.Add(gridLayer.CurUsingColorMode);
                }
                string statImgIDSet = getStatImgNeededTriadID(modeItems);
                foreach (TimePeriod period in condition.Periods)
                {
                    List<CellQueryCondUnit> cellLacCiChangedPeriods = parseChangeLACCIPeriodsOfCell(period);
                    foreach (CellQueryCondUnit cond in cellLacCiChangedPeriods)
                    {
                        queryPeriodInfo(cond.period, clientProxy, cond, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                    }
                }
                WaitBox.Text = "数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                log.Error("Error:" + e.Message);
            }
            finally
            {
                System.Threading.Thread.Sleep(1000);
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }

        protected override void queryPeriodInfo(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            System.Diagnostics.Debug.Assert(reservedParams.Length > 2, "缺少参数");
            //query kpi stat only
            isQueringEvent = false;
            preparePackageBasicContent(clientProxy.Package, period, reservedParams[0]);
            preparePackageNeededInfo_StatImg(clientProxy.Package, reservedParams[1]);
            clientProxy.Send();
            recieveInfo_ImgGrid(clientProxy, reservedParams[2]);

            afterRecieveOnePeriodData();
        }

        private List<CellQueryCondUnit> parseChangeLACCIPeriodsOfCell(TimePeriod period)
        {
            List<CellQueryCondUnit> condPeriodList = new List<CellQueryCondUnit>();
            int meter = 5000;
            double ltlong = 0, ltlat = 0, brlong = 0, brlat = 0; //定义左上右下经纬度
            if (condition.Geometorys.SelectedCell != null)
            {
                parseRectByLongLatDistance(condition.Geometorys.SelectedCell.Longitude, condition.Geometorys.SelectedCell.Latitude, meter, out ltlong, out ltlat, out brlong, out brlat);
                List<Cell> cellsnaps = condition.Geometorys.SelectedCell.GetAll(period);
                CellInfo info = new CellInfo(ltlong, ltlat,  brlong, brlat);
                foreach (Cell snap in cellsnaps)
                {
                    info.lac = snap.LAC;
                    info.ci = snap.CI;
                    info.period = snap.ValidPeriod;
                    addCellQueryCondUnit(period, condPeriodList, info);
                }
            }
            else if (condition.Geometorys.SelectedTDCell != null)
            {
                parseRectByLongLatDistance(condition.Geometorys.SelectedTDCell.Longitude, condition.Geometorys.SelectedTDCell.Latitude, meter, out ltlong, out ltlat, out brlong, out brlat);
                List<TDCell> cellsnaps = condition.Geometorys.SelectedTDCell.GetAll(period);
                CellInfo info = new CellInfo(ltlong, ltlat, brlong, brlat);
                foreach (TDCell snap in cellsnaps)
                {
                    info.lac = snap.LAC;
                    info.ci = snap.CI;
                    info.period = snap.ValidPeriod;
                    addCellQueryCondUnit(period, condPeriodList, info);
                }
            }
            else if (condition.Geometorys.SelectedWCell != null)
            {
                parseRectByLongLatDistance(condition.Geometorys.SelectedWCell.Longitude, condition.Geometorys.SelectedWCell.Latitude, meter, out ltlong, out ltlat, out brlong, out brlat);
                List<WCell> cellsnaps = condition.Geometorys.SelectedWCell.GetAll(period);
                CellInfo info = new CellInfo(ltlong, ltlat, brlong, brlat);
                foreach (WCell snap in cellsnaps)
                {
                    info.lac = snap.LAC;
                    info.ci = snap.CI;
                    info.period = snap.ValidPeriod;
                    addCellQueryCondUnit(period, condPeriodList, info);
                }
            }
            return condPeriodList;
        }

        private void addCellQueryCondUnit(TimePeriod period, List<CellQueryCondUnit> condPeriodList, CellInfo info)
        {
            if (info.lac != 0 && info.lac != info.formarLac && info.ci != 0 && info.ci != info.formarCi)
            {
                CellQueryCondUnit cond = new CellQueryCondUnit();
                cond.LAC = info.lac;
                cond.CI = info.ci;
                TimePeriod tpseg = new TimePeriod();
                DateTime fromTime = info.period.BeginTime;
                DateTime toTime = info.period.EndTime;
                if (fromTime < period.BeginTime)
                {
                    fromTime = period.BeginTime;
                }
                if (toTime > period.EndTime)
                {
                    toTime = period.EndTime;
                }
                tpseg.SetPeriod(fromTime, toTime);
                cond.period = tpseg;
                cond.ltLongitude = info.ltlong;
                cond.ltLatitude = info.ltlat;
                cond.brLongitude = info.brlong;
                cond.brLatitude = info.brlat;
                condPeriodList.Add(cond);
                info.formarLac = info.lac;
                info.formarCi = info.ci;
            }
        }

        class CellInfo
        {
            public CellInfo( double ltlong,  double ltlat,  double brlong,  double brlat)
            {
                this.ltlong = ltlong;
                this.ltlat = ltlat;
                this.brlong = brlong;
                this.brlat = brlat;
            }

            public double ltlong;
            public double ltlat;
            public double brlong;
            public double brlat;
            public int lac;
            public int ci;
            public int formarLac;
            public int formarCi;
            public TimePeriod period;
        }

        private void parseRectByLongLatDistance(double longitude, double latitude, int meter, out double ltlong, out double ltlat, out double brlong, out double brlat)//通过中心经纬度及其到边框的距离求经纬度（模糊值）
        {
            double longPerMeter = 0.00001;
            double latPerMeter = 0.000009;
            ltlong = longitude - meter * longPerMeter;
            ltlat = latitude + meter * latPerMeter;
            brlong = longitude + meter * longPerMeter;
            brlat = latitude - meter * latPerMeter;
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            GridMatrix<ColorUnit> colorMatrix = reservedParams[0] as GridMatrix<ColorUnit>;
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = "正在从服务器接收数据...";
                }
                recved = true;
                KPIStatDataBase singleStatData = null;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(colorMatrix, package, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }
                setPercent(ref counter, ref curPercent);
            }
        }

        private void setPercent(ref int counter, ref int curPercent)
        {
            int tmp = (int)(Math.Log(counter++) * 10);
            if (tmp < 95 && tmp > 0 && curPercent != tmp)
            {
                WaitBox.ProgressPercent = tmp;
            }
            else if (tmp > 95)
            {
                curPercent = 5;
                counter = 0;
            }
        }

        private void fillData(GridMatrix<ColorUnit> colorMatrix, Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            package.Content.GetParamInt();//lac
            package.Content.GetParamInt();//ci
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            fillStatData(package, curImgColumnDef, singleStatData);
            ColorUnit cu = new ColorUnit();
            cu.LTLng = lng;
            cu.LTLat = lat;
            int rAt, cAt;
            GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);
            cu = colorMatrix[rAt, cAt];
            if (cu == null)
            {
                cu = new ColorUnit();
                cu.LTLng = lng;
                cu.LTLat = lat;
                colorMatrix[rAt, cAt] = cu;
            }
            cu.Status = 1;
            cu.DataHub.AddStatData(singleStatData, false);
        }

        protected override void preparePackageCommand(Package package)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.KPI_CELL_GRID;
            package.Content.PrepareAddParam();
        }

        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            if (reservedParams != null && reservedParams.Length > 0)
            {
                CellQueryCondUnit cond = reservedParams[0] as CellQueryCondUnit;
                package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
                package.Content.AddParam(cond.ltLongitude);
                package.Content.AddParam(cond.ltLatitude);
                package.Content.AddParam(cond.brLongitude);
                package.Content.AddParam(cond.brLatitude);
                AddDIYEndOpFlag(package);
                package.Content.AddParam((byte)OpOptionDef.CellSelect);
                package.Content.AddParam(cond.LAC);
                package.Content.AddParam(cond.CI);
            }
        }

    }
}
