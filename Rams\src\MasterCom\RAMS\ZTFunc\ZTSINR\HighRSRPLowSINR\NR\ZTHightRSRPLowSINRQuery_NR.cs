﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTHightRSRPLowSINRQuery_NR : ZTHightRSRPLowSINRBaseQuery<ZTHightRSRPLowSINRQuery_NR>
    {
        protected override string themeName { get { return "NR:SS_SINR"; } }
        protected override string rsrpName { get { return "NR_SS_RSRP"; } }
        protected override string sinrName { get { return "NR_SS_SINR"; } }

        public ZTHightRSRPLowSINRQuery_NR()
            : base()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_lte_RSRP");
            Columns.Add("NR_lte_SINR");
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "强信号弱质量_NR"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35008, this.Name);//////
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                setRoadCond();
                return true;
            }
            HighRSRPLowSINRNRSettingDlg dlg = new HighRSRPLowSINRNRSettingDlg(WeakCond);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                WeakCond = dlg.GetCondition();
                setRoadCond();
                return true;
            }
            return false;
        }

        protected override void addLowSinrInfo(List<TestPoint> tps, double percent)
        {
            HighRSRPLowSINR_NR info = new HighRSRPLowSINR_NR();
            info.SN = resultList.Count + 1;
            info.Percent = percent;
            double dis = 0;
            TestPoint lastTp = null;
            Dictionary<string, int> cellDic = new Dictionary<string, int>();
            foreach (TestPoint tp in tps)
            {
                if (lastTp == null)
                {
                    info.StartTime = tp.DateTime;
                }
                else
                {
                    dis = tp.Distance2(lastTp);
                }
                float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
                float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
                info.AddTestPoint(tp, rsrp, sinr, dis);
                info.AddOtherTPInfo(tp);
                lastTp = tp;

                getCellName(tp, cellDic);
            }

            StringBuilder name = new StringBuilder();
            foreach (var cell in cellDic.Keys)
            {
                name.Append(cell + ";");
            }
            info.CellListDesc = name.ToString();

            info.FindRoadName();
            resultList.Add(info);
        }

        private void getCellName(TestPoint tp, Dictionary<string, int> cellDic)
        {
            NRCell nrcell = tp.GetMainCell_NR();
            string cellName = "";
            if (nrcell != null)
            {
                cellName = nrcell.Name;
            }
            else 
            {
                int? earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
                int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
                if (earfcn != null && pci != null)
                {
                    cellName = $"{earfcn}_{pci}";
                }
            }

            if (!string.IsNullOrEmpty(cellName) && !cellDic.ContainsKey(cellName))
            {
                cellDic.Add(cellName, 0);
            }
        }

        protected override void fireShowForm()
        {
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            HighRSRPLowSINRForm_NR frm = mainModel.CreateResultForm(typeof(HighRSRPLowSINRForm_NR)) as HighRSRPLowSINRForm_NR;
            List<HighRSRPLowSINR_NR> resList = new List<HighRSRPLowSINR_NR>();
            foreach (var item in resultList)
            {
                resList.Add(item as HighRSRPLowSINR_NR);
            }

            frm.FillData(resList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class NRHightRSRPLowSINRQueryByFile : ZTHightRSRPLowSINRQuery_NR
    {
        private static NRHightRSRPLowSINRQueryByFile instance = null;
        public new static NRHightRSRPLowSINRQueryByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new NRHightRSRPLowSINRQueryByFile();
            }
            return instance;
        }

        public override string Name
        {
            get { return "强信号弱质量_NR(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isTPInRegion(TestPoint tp)
        {
            return true;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
