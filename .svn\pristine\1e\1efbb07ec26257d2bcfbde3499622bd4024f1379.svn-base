﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTReportEventStatSetForm_QH
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.dtEnd = new System.Windows.Forms.DateTimePicker();
            this.dtBegin = new System.Windows.Forms.DateTimePicker();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.cmbNetType = new System.Windows.Forms.ComboBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.treeListProject = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn3 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.ckbSelectNoneProj = new System.Windows.Forms.CheckBox();
            this.ckbSelectAllProj = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListProject)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(441, 462);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 27);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnOK.Location = new System.Drawing.Point(337, 462);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // dtEnd
            // 
            this.dtEnd.Location = new System.Drawing.Point(103, 53);
            this.dtEnd.Name = "dtEnd";
            this.dtEnd.Size = new System.Drawing.Size(141, 21);
            this.dtEnd.TabIndex = 18;
            // 
            // dtBegin
            // 
            this.dtBegin.Location = new System.Drawing.Point(103, 21);
            this.dtBegin.Name = "dtBegin";
            this.dtBegin.Size = new System.Drawing.Size(141, 21);
            this.dtBegin.TabIndex = 17;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(32, 26);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 21;
            this.label1.Text = "开始时间";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(32, 57);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 22;
            this.label2.Text = "结束时间";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(276, 26);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(53, 12);
            this.label10.TabIndex = 27;
            this.label10.Text = "网络类型";
            // 
            // cmbNetType
            // 
            this.cmbNetType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbNetType.FormattingEnabled = true;
            this.cmbNetType.Items.AddRange(new object[] {
            "GSM",
            "TD",
            "LTESCAN",
            "LTE",
            "全部"});
            this.cmbNetType.Location = new System.Drawing.Point(346, 23);
            this.cmbNetType.Name = "cmbNetType";
            this.cmbNetType.Size = new System.Drawing.Size(126, 22);
            this.cmbNetType.TabIndex = 26;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.treeListProject);
            this.groupBox2.Controls.Add(this.ckbSelectNoneProj);
            this.groupBox2.Controls.Add(this.ckbSelectAllProj);
            this.groupBox2.Location = new System.Drawing.Point(31, 123);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(497, 321);
            this.groupBox2.TabIndex = 28;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "项目筛选";
            // 
            // treeListProject
            // 
            this.treeListProject.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn1,
            this.treeListColumn3});
            this.treeListProject.Location = new System.Drawing.Point(21, 24);
            this.treeListProject.Name = "treeListProject";
            this.treeListProject.BeginUnboundLoad();
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.EndUnboundLoad();
            this.treeListProject.OptionsView.AutoWidth = false;
            this.treeListProject.OptionsView.ShowCheckBoxes = true;
            this.treeListProject.OptionsView.ShowFocusedFrame = false;
            this.treeListProject.OptionsView.ShowIndicator = false;
            this.treeListProject.Size = new System.Drawing.Size(451, 253);
            this.treeListProject.TabIndex = 29;
            this.treeListProject.TreeLineStyle = DevExpress.XtraTreeList.LineStyle.None;
            // 
            // treeListColumn1
            // 
            this.treeListColumn1.Caption = "参与统计";
            this.treeListColumn1.FieldName = "参与统计";
            this.treeListColumn1.MinWidth = 36;
            this.treeListColumn1.Name = "treeListColumn1";
            this.treeListColumn1.OptionsColumn.AllowEdit = false;
            this.treeListColumn1.Visible = true;
            this.treeListColumn1.VisibleIndex = 0;
            this.treeListColumn1.Width = 84;
            // 
            // treeListColumn3
            // 
            this.treeListColumn3.Caption = "项目";
            this.treeListColumn3.FieldName = "项目";
            this.treeListColumn3.Name = "treeListColumn3";
            this.treeListColumn3.OptionsColumn.AllowEdit = false;
            this.treeListColumn3.Visible = true;
            this.treeListColumn3.VisibleIndex = 1;
            this.treeListColumn3.Width = 338;
            // 
            // ckbSelectNoneProj
            // 
            this.ckbSelectNoneProj.AutoSize = true;
            this.ckbSelectNoneProj.Location = new System.Drawing.Point(91, 286);
            this.ckbSelectNoneProj.Name = "ckbSelectNoneProj";
            this.ckbSelectNoneProj.Size = new System.Drawing.Size(60, 16);
            this.ckbSelectNoneProj.TabIndex = 28;
            this.ckbSelectNoneProj.Text = "全不选";
            this.ckbSelectNoneProj.UseVisualStyleBackColor = true;
            this.ckbSelectNoneProj.CheckedChanged += new System.EventHandler(this.ckbSelectNoneProj_CheckedChanged);
            // 
            // ckbSelectAllProj
            // 
            this.ckbSelectAllProj.AutoSize = true;
            this.ckbSelectAllProj.Location = new System.Drawing.Point(21, 286);
            this.ckbSelectAllProj.Name = "ckbSelectAllProj";
            this.ckbSelectAllProj.Size = new System.Drawing.Size(48, 16);
            this.ckbSelectAllProj.TabIndex = 27;
            this.ckbSelectAllProj.Text = "全选";
            this.ckbSelectAllProj.UseVisualStyleBackColor = true;
            this.ckbSelectAllProj.CheckedChanged += new System.EventHandler(this.ckbSelectAllProj_CheckedChanged);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.dtBegin);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Controls.Add(this.cmbNetType);
            this.groupBox1.Controls.Add(this.dtEnd);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Location = new System.Drawing.Point(31, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(497, 95);
            this.groupBox1.TabIndex = 29;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "时间及网络类型";
            // 
            // ZTReportEventStatSetForm_QH
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(560, 501);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Name = "ZTReportEventStatSetForm_QH";
            this.ShowIcon = false;
            this.Text = "条件设置";
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListProject)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.DateTimePicker dtEnd;
        private System.Windows.Forms.DateTimePicker dtBegin;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.ComboBox cmbNetType;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.CheckBox ckbSelectNoneProj;
        private System.Windows.Forms.CheckBox ckbSelectAllProj;
        private DevExpress.XtraTreeList.TreeList treeListProject;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn1;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn3;
        private System.Windows.Forms.GroupBox groupBox1;
    }
}