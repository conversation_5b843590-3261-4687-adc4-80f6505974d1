﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTReportXtraForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTReportXtraForm(MainModel main)
        {
            InitializeComponent();
            mainmodel = main;

            CQTLibrary.RAMS.NET.MainModel model = new CQTLibrary.RAMS.NET.MainModel();
            model.DistrictID = mainmodel.DistrictID;
            model.UserName = mainmodel.User.LoginName;
            model.UserPass = mainmodel.User.Password;
            model.ServerIP = mainmodel.Server.IP;
            model.ServerPort = mainmodel.Server.Port;

            this.cqtModel = model;
        }

        CQTLibrary.RAMS.NET.MainModel cqtModel { get; set; }
        MainModel mainmodel = null;
        CQTLibrary.CqtZTFunc.NetWorkExam network = new CQTLibrary.CqtZTFunc.NetWorkExam();
        List<CQTLibrary.PublicItem.ExamScore> exam = new List<CQTLibrary.PublicItem.ExamScore>();

        DateTime stime = new DateTime();
        DateTime etime = new DateTime();
        List<CQTLibrary.PublicItem.ParaColumnItem> paralist = new List<CQTLibrary.PublicItem.ParaColumnItem>();
        CQTLibrary.PublicItem.AreaDetail area = new CQTLibrary.PublicItem.AreaDetail();


        public void setData(CQTLibrary.PublicItem.ExamItem eitem, DateTime stime, DateTime etime, List<CQTLibrary.PublicItem.ParaColumnItem> paralist, CQTLibrary.PublicItem.AreaDetail area)
        {
            this.stime = stime;
            this.etime = etime;
            this.paralist = paralist;
            this.area = area;
            gridControl1.DataSource = exam;

            dateEdit1.DateTime = stime;
            dateEdit2.DateTime = etime;
            textEdit2.Text = area.Strcomment;
            textEdit5.Text = eitem.Fexamvalue.ToString();
            textEdit6.Text = eitem.Strexamreault;

            labelControl1.Text = eitem.Strcqtname + "体检报告";

            CQTLibrary.RAMS.NET.WaitBox.Show("开始统计测试KPI...", queryStatData);
            System.Threading.Thread.Sleep(20);

            CQTLibrary.RAMS.NET.WaitBox.Show("开始统计性能KPI...", queryStatData2);
            System.Threading.Thread.Sleep(20);

            CQTLibrary.RAMS.NET.WaitBox.Show("开始统计投诉KPI...", queryStatData3);
            System.Threading.Thread.Sleep(20);

            CQTLibrary.RAMS.NET.WaitBox.Show("开始分析占用小区集信息...", queryStatData4);
            System.Threading.Thread.Sleep(20);

            CQTLibrary.RAMS.NET.WaitBox.Show("开始覆盖分析...", queryStatData5);
            System.Threading.Thread.Sleep(20);

            CQTLibrary.RAMS.NET.WaitBox.Show("开始质量分析...", queryStatData6);
            System.Threading.Thread.Sleep(20);

            CQTLibrary.RAMS.NET.WaitBox.Show("开始切换分析...", queryStatData7);
            System.Threading.Thread.Sleep(20);

            CQTLibrary.RAMS.NET.WaitBox.Show("开始投诉分析...", queryStatData8);
            System.Threading.Thread.Sleep(20);

            CQTLibrary.RAMS.NET.WaitBox.Show("开始性能趋势分析...", queryStatData9);
            System.Threading.Thread.Sleep(20);

            CQTLibrary.RAMS.NET.WaitBox.Show("开始问题点分析...", queryStatData10);
            System.Threading.Thread.Sleep(20);
        }

        /// <summary>
        /// 测试KPI
        /// </summary>
        protected void queryStatData()
        {
            try
            {
                List<CQTLibrary.PublicItem.ExamScore> examtwo = network.examStepTwo(stime, etime, cqtModel, paralist, area);

                exam.AddRange(examtwo);
                gridControl1.RefreshDataSource();
            }
            catch
            {
                //continue
            }
            finally
            {
                CQTLibrary.RAMS.NET.WaitBox.Close();
            }
        }

        /// <summary>
        /// 性能KPI
        /// </summary>
        protected void queryStatData2()
        {
            try
            {
                List<CQTLibrary.PublicItem.ExamScore> examthird = network.examStepThird(stime, etime, cqtModel, paralist, area);

                exam.AddRange(examthird);
                gridControl1.RefreshDataSource();
            }
            catch
            {
                //continue
            }
            finally
            {
                CQTLibrary.RAMS.NET.WaitBox.Close();
            }
        }

        /// <summary>
        /// 投诉KPI
        /// </summary>
        protected void queryStatData3()
        {
            try
            {
                List<CQTLibrary.PublicItem.ExamScore> examfour = network.examStepFour(stime, etime, cqtModel, paralist, area);

                exam.AddRange(examfour);
                gridControl1.RefreshDataSource();
            }
            catch
            {
                //continue
            }
            finally
            {
                CQTLibrary.RAMS.NET.WaitBox.Close();
            }
        }

        /// <summary>
        /// 占用小区集信息
        /// </summary>
        protected void queryStatData4()
        {
            try
            {
                string examfive = network.examStepFive(stime, etime, cqtModel, paralist, area);
                textEdit7.Text = examfive;
                
                string strNetInfo = "";
                if (examfive.IndexOf("GSM") >=0)
                    strNetInfo = "GSM";
                if (examfive.IndexOf("TD") >= 0)
                {
                    if (strNetInfo == "")
                        strNetInfo = "TD";
                    else
                        strNetInfo += "、TD";
                }
                textEdit3.Text = strNetInfo;
            }
            catch
            {
                //continue
            }
            finally
            {
                CQTLibrary.RAMS.NET.WaitBox.Close();
            }
        }

        /// <summary>
        /// 覆盖分析
        /// </summary>
        protected void queryStatData5()
        {
            try
            {
                string examsex = network.examStepSix(stime, etime, cqtModel, paralist, area);
                textEdit8.Text = examsex;
            }
            catch
            {
                //continue
            }
            finally
            {
                CQTLibrary.RAMS.NET.WaitBox.Close();
            }
        }

        /// <summary>
        /// 质量分析
        /// </summary>
        protected void queryStatData6()
        {
            try
            {
                string examseven = network.examStepSeven(stime, etime, cqtModel, paralist, area);
                textEdit9.Text = examseven;
            }
            catch
            {
                //continue
            }
            finally
            {
                CQTLibrary.RAMS.NET.WaitBox.Close();
            }
        }

        /// <summary>
        /// 切换分析
        /// </summary>
        protected void queryStatData7()
        {
            try
            {
                string exameight = network.examStepEight(stime, etime, cqtModel, paralist, area);
                textEdit10.Text = exameight;
            }
            catch
            {
                //continue
            }
            finally
            {
                CQTLibrary.RAMS.NET.WaitBox.Close();
            }
        }

        /// <summary>
        /// 投诉分析
        /// </summary>
        protected void queryStatData8()
        {
            try
            {
                string examnine = network.examStepNine(stime, etime, cqtModel, paralist, area);
                textEdit11.Text = examnine;
            }
            catch
            {
                //continue
            }
            finally
            {
                CQTLibrary.RAMS.NET.WaitBox.Close();
            }
        }

        /// <summary>
        /// 性能趋势分析
        /// </summary>
        protected void queryStatData9()
        {
            try
            {
                string examtem = network.examStepTen(stime, etime, cqtModel, paralist, area);
                textEdit12.Text = examtem;
            }
            catch
            {
                //continue
            }
            finally
            {
                CQTLibrary.RAMS.NET.WaitBox.Close();
            }
        }

        /// <summary>
        /// 问题点分析
        /// </summary>
        protected void queryStatData10()
        {
            try
            {
                string examtem = network.examStepEleven(stime, etime, cqtModel, area);
                textEdit13.Text = examtem;
            }
            catch
            {
                //continue
            }
            finally
            {
                CQTLibrary.RAMS.NET.WaitBox.Close();
            }
        }
    }
}