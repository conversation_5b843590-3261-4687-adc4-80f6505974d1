using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.Net
{
    public class ScanGridManager
    {
        public static ScanInterfereResultManager doScanInterfereAna(MainModel mainModel, GridMatrix<GridCellsInfo> cellGrids, int dValue, MapForm.DisplayInterferenceType interferenceType)
        {
            List<Dictionary<int, GSMCellRxLev>> refList = new List<Dictionary<int, GSMCellRxLev>>();
            for (int r = cellGrids.MinRowIdx; r <= cellGrids.MaxRowIdx; r++)
            {
                for (int c = cellGrids.MinColIdx; c <= cellGrids.MaxColIdx; c++)
                {
                    Dictionary<int, GSMCellRxLev> gridCells = new Dictionary<int, GSMCellRxLev>();
                    int rTmp = r;   //中心
                    int cTmp = c;
                    combineGridCells(rTmp, cTmp, cellGrids, gridCells);
                    //第一层
                    rTmp = r - 1;   //左上
                    cTmp = c - 1;
                    combineGridCells(rTmp, cTmp, cellGrids, gridCells);
                    rTmp = r - 1;   //上
                    cTmp = c;
                    combineGridCells(rTmp, cTmp, cellGrids, gridCells);
                    rTmp = r - 1;   //右上
                    cTmp = c + 1;
                    combineGridCells(rTmp, cTmp, cellGrids, gridCells);
                    rTmp = r;   //右
                    cTmp = c + 1;
                    combineGridCells(rTmp, cTmp, cellGrids, gridCells);
                    rTmp = r + 1;   //右下
                    cTmp = c + 1;
                    combineGridCells(rTmp, cTmp, cellGrids, gridCells);
                    rTmp = r + 1;   //下
                    cTmp = c;
                    combineGridCells(rTmp, cTmp, cellGrids, gridCells);
                    rTmp = r + 1;   //左下
                    cTmp = c - 1;
                    combineGridCells(rTmp, cTmp, cellGrids, gridCells);
                    rTmp = r;   //左
                    cTmp = c - 1;
                    combineGridCells(rTmp, cTmp, cellGrids, gridCells);
                    if (gridCells.Count > 0)
                    {
                        refList.Add(gridCells);
                    }
                }
            }
            return combineInterfereCells(refList, dValue, interferenceType);
        }

        private static void combineGridCells(int r, int c, GridMatrix<GridCellsInfo> cellGrids, Dictionary<int, GSMCellRxLev> gridCells)
        {
            GridCellsInfo grid = cellGrids[r, c];
            if (grid != null)
            {
                foreach (GSMCellRxLev cellInfo in grid.cellInfo.Values)
                {
                    if (gridCells.ContainsKey(cellInfo.cellID))
                    {
                        int oldCount = gridCells[cellInfo.cellID].sampleNum;
                        double oldRxLevMean = gridCells[cellInfo.cellID].rxlevAvg;
                        double oldRxLevMax = gridCells[cellInfo.cellID].rxlevMax;
                        gridCells[cellInfo.cellID].sampleNum = oldCount + cellInfo.sampleNum;
                        gridCells[cellInfo.cellID].rxlevAvg = (oldRxLevMean * oldCount + cellInfo.rxlevAvg * cellInfo.sampleNum) / (oldCount + cellInfo.sampleNum);
                        gridCells[cellInfo.cellID].rxlevMax = cellInfo.rxlevMax > oldRxLevMax ? cellInfo.rxlevMax : oldRxLevMax;
                    }
                    else
                    {
                        GSMCellRxLev cell = new GSMCellRxLev();
                        gridCells[cellInfo.cellID] = cell;
                        gridCells[cellInfo.cellID].cellID = cellInfo.cellID;
                        gridCells[cellInfo.cellID].sampleNum = cellInfo.sampleNum;
                        gridCells[cellInfo.cellID].rxlevAvg = cellInfo.rxlevAvg;
                        gridCells[cellInfo.cellID].rxlevMax = cellInfo.rxlevMax;
                    }
                    if (gridCells[cellInfo.cellID].grids == null)
                    {
                        gridCells[cellInfo.cellID].grids = new List<GridItem>();
                    }
                    GridItem gridItem = new GridItem(grid.LTLng, grid.LTLat);
                    gridCells[cellInfo.cellID].grids.Add(gridItem);
                    if (gridCells[cellInfo.cellID].gridCellDic == null)
                    {
                        gridCells[cellInfo.cellID].gridCellDic = new Dictionary<GridItem, GSMCellRxLev>();
                    }
                    gridCells[cellInfo.cellID].gridCellDic[gridItem] = cellInfo;
                }
            }
        }

        private static ScanInterfereResultManager combineInterfereCells(List<Dictionary<int, GSMCellRxLev>> cellRxlevList, int dValue, MapForm.DisplayInterferenceType interferenceType)
        {
            ScanInterfereResultManager scanInterfereResultManager = new ScanInterfereResultManager(dValue == 0 ? 0 : 1, interferenceType);
            foreach (Dictionary<int, GSMCellRxLev> cells in cellRxlevList)
            {
                List<GSMCellRxLev> cellInfos = new List<GSMCellRxLev>(cells.Values);
                cellInfos.Sort(GSMCellRxLev.GetCompareByRxLevAvg());
                for (int i = 0; i < cellInfos.Count; i++)
                {
                    if (cellInfos[i].cellID == 0)
                    {
                        continue;
                    }
                    Cell cellI = CellManager.GetInstance().GetCurrentCell(cellInfos[i].cellID);
                    if (cellI == null)
                    {
                        continue;
                    }
                    for (int j = i + 1; j < cellInfos.Count; j++)
                    {
                        if (cellInfos[j].cellID == 0)
                        {
                            continue;
                        }
                        Cell cellJ = CellManager.GetInstance().GetCurrentCell(cellInfos[j].cellID);
                        if (cellJ == null)
                        {
                            continue;
                        }
                        List<string> typeNameList = new List<string>();
                        if (interferenceValid(cellI, cellJ, dValue, interferenceType, typeNameList))
                        {
                            ScanInterfereResult result = new ScanInterfereResult();
                            result.cellIDOne = cellInfos[i].cellID;
                            result.rxLevMeanOne = cellInfos[i].rxlevAvg;
                            result.sampleCountOne = cellInfos[i].sampleNum;
                            result.gridsOne.AddRange(cellInfos[i].grids);
                            result.gridCellsOne = cellInfos[i].gridCellDic;
                            result.bcchOne = cellI.BCCH;

                            result.cellIDTwo = cellInfos[j].cellID;
                            result.rxLevMeanTwo = cellInfos[j].rxlevAvg;
                            result.sampleCountTwo = cellInfos[j].sampleNum;
                            result.gridsTwo.AddRange(cellInfos[j].grids);
                            result.gridCellsTwo = cellInfos[j].gridCellDic;
                            result.bcchTwo = cellJ.BCCH;
                            result.bcch = cellI.BCCH;
                            result.angle = CaleAngle(cellI.Direction, cellJ.Direction);
                            foreach (string typeName in typeNameList)
                            {
                                scanInterfereResultManager.AddScanInterfereResult(result, typeName);
                            }
                        }
                    }
                }
            }
            return scanInterfereResultManager;
        }

        private static bool interferenceValid(Cell cellOne, Cell cellTwo, int dValue, MapForm.DisplayInterferenceType interferenceType, List<string> typeNameList)
        {
            bool valid = false;
            if (interferenceType == MapForm.DisplayInterferenceType.BCCHTCH)
            {
                if (dValue > 0) //邻频
                {
                    foreach (short dch in cellTwo.DCH)
                    {
                        if (Math.Abs(cellOne.BCCH - dch) <= dValue && Math.Abs(cellOne.BCCH - dch) > 0)
                        {
                            typeNameList.Add("AdjBCCH");
                            valid = true;
                            break;
                        }
                    }
                    foreach (short dch in cellTwo.DCH)
                    {
                        bool isBreak = false;
                        foreach (short tch in cellOne.TCH)
                        {
                            if (Math.Abs(tch - dch) <= dValue && Math.Abs(tch - dch) > 0)
                            {
                                typeNameList.Add("AdjTCH");
                                valid = true;
                                isBreak = true;
                                break;
                            }
                        }
                        if (isBreak)
                        {
                            break;
                        }
                    }
                }
                else            //同频
                {
                    if (cellOne.IsCo_BCCH(cellTwo.BCCH))
                    {
                        typeNameList.Add("CoBCCH");
                        valid = true;
                    }
                    if (cellOne.IsCo_TCH(cellTwo.DCH))
                    {
                        typeNameList.Add("CoTCH");
                        valid = true;
                    }
                }
            }
            else if (interferenceType == MapForm.DisplayInterferenceType.BCCH_CPI)
            {
                if (dValue > 0) //邻频
                {
                    if (Math.Abs(cellOne.BCCH - cellTwo.BCCH) <= dValue && Math.Abs(cellOne.BCCH - cellTwo.BCCH) > 0)
                    {
                        typeNameList.Add("AdjBCCH");
                        valid = true;
                    }
                }
                else            //同频
                {
                    if (cellOne.IsCo_BCCH(cellTwo.BCCH))
                    {
                        typeNameList.Add("CoBCCH");
                        valid = true;
                    }
                }
            }
            else if (interferenceType == MapForm.DisplayInterferenceType.TCH_FREQ)
            {
                if (dValue > 0) //邻频
                {
                    foreach (short tch in cellTwo.TCH)
                    {
                        if (Math.Abs(cellOne.BCCH - tch) <= dValue && Math.Abs(cellOne.BCCH - tch) > 0)
                        {
                            typeNameList.Add("AdjBCCH");
                            valid = true;
                            break;
                        }
                    }
                    foreach (short tchTwo in cellTwo.TCH)
                    {
                        bool isBreak = false;
                        foreach (short tchOne in cellOne.TCH)
                        {
                            if (Math.Abs(tchOne - tchTwo) <= dValue && Math.Abs(tchOne - tchTwo) > 0)
                            {
                                typeNameList.Add("AdjTCH");
                                valid = true;
                                isBreak = true;
                                break;
                            }
                        }
                        if (isBreak)
                        {
                            break;
                        }
                    }
                }
                else            //同频
                {
                    if (cellOne.IsCo_TCH(cellTwo.TCH))
                    {
                        typeNameList.Add("CoTCH");
                        valid = true;
                    }
                }
            }
            return valid;
        }

        public static List<ScanShearResult> doScanShearAna(MainModel mainModel, GridMatrix<GridCellsInfo> cellGrids)
        {
            List<ScanShearResult> refList = new List<ScanShearResult>();
            foreach (GridCellsInfo cellGrid in cellGrids)
            {
                if (cellGrid != null && cellGrid.cellInfo.Count > 1)
                {
                    Dictionary<int, GSMCellRxLev> cellsDic = cellGrid.cellInfo;
                    List<GSMCellRxLev> cellsList = new List<GSMCellRxLev>(cellsDic.Values);
                    cellsList.Sort(GSMCellRxLev.GetCompareByRxLevAvg());
                    ScanShearResult result = judgeShearCells(mainModel, cellGrid, cellsList[0], cellsList[1]);
                    if (result != null)
                    {
                        refList.Add(result);
                    }
                    if (cellsList.Count > 2)
                    {
                        result = judgeShearCells(mainModel, cellGrid, cellsList[0], cellsList[2]);
                        if (result != null)
                        {
                            refList.Add(result);
                        }
                    }
                }
            }
            return refList;
        }

        public static ScanShearResult judgeShearCells(MainModel mainModel, GridCellsInfo cellGrid, GSMCellRxLev cellRxLevMain, GSMCellRxLev cellRxLevOther)
        {
            if (cellRxLevMain.cellID == 0 || cellRxLevOther.cellID == 0)
            {
                return null;
            }
            Cell cellMain = mainModel.CellManager.GetCurrentCell(cellRxLevMain.cellID);
            Cell cellOther = mainModel.CellManager.GetCurrentCell(cellRxLevOther.cellID);
            if (cellMain == null || cellOther == null)
            {
                return null;
            }
            if (cellMain.BCCH == cellOther.BCCH && cellMain.BSIC != cellOther.BSIC)
            {
                bool isInAngle = MathFuncs.JudgePoint(cellMain.Longitude, cellMain.Latitude, cellOther.Longitude, cellOther.Latitude, cellMain.Direction);
                if (isInAngle)
                {
                    return null;
                }
                double distance = MathFuncs.GetDistance(cellMain.Longitude, cellMain.Latitude, cellOther.Longitude, cellOther.Latitude);
                ScanShearResult result = new ScanShearResult();
                result.cellIDMain = cellRxLevMain.cellID;
                result.rxLevMeanMain = cellRxLevMain.rxlevAvg;
                result.sampleCountMain = cellRxLevMain.sampleNum;
                result.bsicMain = cellMain.BSIC;

                result.cellIDOther = cellRxLevOther.cellID;
                result.rxLevMeanOther = cellRxLevOther.rxlevAvg;
                result.sampleCountOther = cellRxLevOther.sampleNum;
                result.bsicOther = cellOther.BSIC;

                result.bcch = cellMain.BCCH;
                result.distance = distance;
                result.grids.Add(new GridItem(cellGrid.LTLng, cellGrid.LTLat));
                return result;
            }
            return null;
        }

        public static List<MissNbCellSubResult> GetNBResult(Dictionary<GridItem, Dictionary<string, Dictionary<int, GSMCellRxLev>>> dicCell, int cellRxLevMin,
            int cellRxLevMax, int nbCellRxLevMin, int nbCellRxLevMax, InspectType inspectType, int topN, int dvalueWithTop1, CellBandTypeFilter bandTypeFilter)
        {
            if (inspectType == InspectType.T2G || inspectType == InspectType.LTE2GSM || inspectType == InspectType.LTE2TD)
            {
                return doNBResultByASC_X2Y(MainModel.GetInstance(), dicCell, cellRxLevMin, cellRxLevMax, nbCellRxLevMin, nbCellRxLevMax, inspectType,
                    topN, dvalueWithTop1, bandTypeFilter);
            }
            else
            {
                return doNBResultByASC(MainModel.GetInstance(), dicCell, cellRxLevMin, cellRxLevMax, nbCellRxLevMin, nbCellRxLevMax, inspectType,
                    topN, dvalueWithTop1, bandTypeFilter);
            }
        }

        /// <summary>
        /// 2G和T2T
        /// </summary>
        /// <param name="mainModel"></param>
        /// <param name="dicCell"></param>
        /// <param name="cellRxLevMin"></param>
        /// <param name="cellRxLevMax"></param>
        /// <param name="nbCellRxLevMin"></param>
        /// <param name="nbCellRxLevMax"></param>
        /// <param name="servicetype"></param>
        /// <param name="topN"></param>
        /// <param name="dvalueWithTop1"></param>
        /// <param name="bandTypeFilter"></param>
        /// <returns></returns>
        private static List<MissNbCellSubResult> doNBResultByASC(MainModel mainModel, Dictionary<GridItem, Dictionary<string, Dictionary<int, GSMCellRxLev>>> dicCell, int cellRxLevMin,
            int cellRxLevMax, int nbCellRxLevMin, int nbCellRxLevMax, InspectType inspectType, int topN, int dvalueWithTop1, CellBandTypeFilter bandTypeFilter)
        {
            //<主小区，<邻区，栅格>>
            Dictionary<ICell, Dictionary<ICell, MainMissNbPairGridInfo>> mainNbDic = new Dictionary<ICell, Dictionary<ICell, MainMissNbPairGridInfo>>();
            foreach (GridItem grid in dicCell.Keys)
            {
                Dictionary<int, GSMCellRxLev> dicCellInGrid = new Dictionary<int, GSMCellRxLev>();
                if (dicCell[grid].ContainsKey("GSM"))
                {
                    dicCellInGrid = sortCellRxLev(dicCell[grid]["GSM"]);
                }
                else if (dicCell[grid].ContainsKey("TD"))
                {
                    dicCellInGrid = sortCellRxLev(dicCell[grid]["TD"]);
                }
                else if (dicCell[grid].ContainsKey("W"))
                {
                    dicCellInGrid = sortCellRxLev(dicCell[grid]["W"]);
                }
                else if (dicCell[grid].ContainsKey("LTE"))
                {
                    dicCellInGrid = sortCellRxLev(dicCell[grid]["LTE"]);
                }
                else
                {
                    continue;
                }
                int mainLoop = 0;
                double maxRxLev = -9999;
                foreach (int mainCellID in dicCellInGrid.Keys)
                {
                    if (dicCellInGrid[mainCellID].rxlevAvg < cellRxLevMin || dicCellInGrid[mainCellID].rxlevAvg > cellRxLevMax)
                    {
                        continue;
                    }
                    ICell mainCell = null;
                    if (inspectType == InspectType.GSM)
                    {
                        Cell cell = CellManager.GetInstance().GetCurrentCell(mainCellID);
                        if (bandTypeFilter == CellBandTypeFilter.GSM900_GSM900 || bandTypeFilter == CellBandTypeFilter.GSM900_DSC1800)
                        {
                            if (cell == null || cell.BandType != BTSBandType.GSM900)
                            {
                                continue;
                            }
                        }
                        if (bandTypeFilter == CellBandTypeFilter.DSC1800_DSC1800 || bandTypeFilter == CellBandTypeFilter.DSC1800_GSM900)
                        {
                            if (cell == null || cell.BandType != BTSBandType.DSC1800)
                            {
                                continue;
                            }
                        }
                        mainCell = cell;
                    }
                    else if (inspectType == InspectType.T2T)
                    {
                        mainCell = CellManager.GetInstance().GetCurrentTDCell(mainCellID);
                    }
                    else if (inspectType == InspectType.W2W)
                    {
                        mainCell = CellManager.GetInstance().GetCurrentWCell(mainCellID);
                    }
                    else if (inspectType == InspectType.LTE2LTE)
                    {
                        mainCell = CellManager.GetInstance().GetCurrentLTECell(mainCellID);
                    }
                    if (mainCell == null)
                    {
                        continue;
                    }
                    mainLoop++;
                    if (mainLoop == 1)
                    {
                        maxRxLev = dicCellInGrid[mainCellID].rxlevAvg;
                    }
                    if (dvalueWithTop1 > 0 || topN > 0)
                    {/*
                      * 当选择了前N强或参考场强差时，只把第一强小区看做主小区！
                      */
                        if (mainLoop > 1)
                        {
                            break;
                        }
                    }
                    int nbLoop = 0;
                    foreach (int nbCellID in dicCellInGrid.Keys)
                    {
                        if (dicCellInGrid[nbCellID].rxlevAvg < nbCellRxLevMin || dicCellInGrid[nbCellID].rxlevAvg > nbCellRxLevMax)
                        {
                            continue;
                        }
                        ICell nbCell;
                        if (inspectType == InspectType.GSM)
                        {
                            Cell cell = CellManager.GetInstance().GetCurrentCell(nbCellID);
                            if (bandTypeFilter == CellBandTypeFilter.GSM900_GSM900 || bandTypeFilter == CellBandTypeFilter.DSC1800_GSM900)
                            {
                                if (cell == null || cell.BandType != BTSBandType.GSM900)
                                {
                                    continue;
                                }
                            }
                            else if (bandTypeFilter == CellBandTypeFilter.DSC1800_DSC1800 || bandTypeFilter == CellBandTypeFilter.GSM900_DSC1800)
                            {
                                if (cell == null || cell.BandType != BTSBandType.DSC1800)
                                {
                                    continue;
                                }
                            }
                            nbCell = cell;
                        }
                        else if (inspectType == InspectType.T2T)
                        {
                            nbCell = CellManager.GetInstance().GetCurrentTDCell(nbCellID);
                        }
                        else if (inspectType == InspectType.LTE2LTE)
                        {
                            nbCell = CellManager.GetInstance().GetCurrentLTECell(nbCellID);
                        }
                        else
                        {
                            nbCell = CellManager.GetInstance().GetCurrentWCell(nbCellID);
                        }
                        if (nbCell == null)
                        {
                            continue;
                        }
                        nbLoop++;
                        if (mainCell == nbCell)
                        {
                            continue;
                        }
                        if (topN != 0)
                        {
                            if (nbLoop > topN)
                            {
                                continue;
                            }
                        }
                        else if (dvalueWithTop1 != 0)
                        {
                            if (maxRxLev - dicCellInGrid[nbCellID].rxlevAvg > dvalueWithTop1)
                            {
                                break;
                            }
                        }
                        else if (dicCellInGrid[mainCellID].rxlevAvg > dicCellInGrid[nbCellID].rxlevAvg)
                        {
                            break;
                        }

                        if (mainNbDic.ContainsKey(mainCell))
                        {
                            if (mainNbDic[mainCell].ContainsKey(nbCell))
                            {
                                mainNbDic[mainCell][nbCell].gridCount++;
                            }
                            else
                            {
                                mainNbDic[mainCell].Add(nbCell, new MainMissNbPairGridInfo(1, 0, 0));
                            }
                        }
                        else
                        {
                            Dictionary<ICell, MainMissNbPairGridInfo> nbDic = new Dictionary<ICell, MainMissNbPairGridInfo>();
                            nbDic.Add(nbCell, new MainMissNbPairGridInfo(1, 0, 0));
                            mainNbDic.Add(mainCell, nbDic);
                        }
                        mainNbDic[mainCell][nbCell].sampleCountNB += dicCellInGrid[nbCellID].sampleNum;
                        mainNbDic[mainCell][nbCell].AddCellGridInfo(grid, dicCellInGrid[mainCellID], dicCellInGrid[nbCellID]);
                    }
                }
            }

            //==================== Compare with the configuration =====================
            switch (inspectType)
            {
                case InspectType.GSM:
                    {
                        return CompareWithConfig_GSM(inspectType, mainNbDic);
                    }
                case InspectType.T2T:
                    {
                        return CompareWithConfig_TD(inspectType, mainNbDic);
                    }
                case InspectType.W2W:
                    {
                        return CompareWithConfig_W(inspectType, mainNbDic);
                    }
                case InspectType.LTE2LTE:
                    {
                        return CompareWithConfig_LTE(inspectType, mainNbDic);
                    }
                default:
                    return null;
            }

        }

        /// <summary>
        /// T2G
        /// </summary>
        /// <param name="mainModel"></param>
        /// <param name="dicCell"></param>
        /// <param name="cellRxLevMin"></param>
        /// <param name="cellRxLevMax"></param>
        /// <param name="nbCellRxLevMin"></param>
        /// <param name="nbCellRxLevMax"></param>
        /// <param name="servicetype"></param>
        /// <param name="topN"></param>
        /// <param name="dvalueWithTop1"></param>
        /// <param name="bandTypeFilter"></param>
        /// <returns></returns>
        private static List<MissNbCellSubResult> doNBResultByASC_X2Y(MainModel mainModel
            , Dictionary<GridItem, Dictionary<string, Dictionary<int, GSMCellRxLev>>> dicCell
            , int cellRxLevMin, int cellRxLevMax, int nbCellRxLevMin, int nbCellRxLevMax
            , InspectType inspectType, int topN, int dvalueWithTop1, CellBandTypeFilter bandTypeFilter)
        {
            //<小区，<邻区，栅格数>>
            Dictionary<ICell, Dictionary<ICell, MainMissNbPairGridInfo>> dicResult = new Dictionary<ICell, Dictionary<ICell, MainMissNbPairGridInfo>>();
            foreach (GridItem grid in dicCell.Keys)
            {
                Dictionary<int, GSMCellRxLev> dicCellInGridMain = new Dictionary<int, GSMCellRxLev>();
                Dictionary<int, GSMCellRxLev> dicCellInGridOther = new Dictionary<int, GSMCellRxLev>();
                if (inspectType == InspectType.T2G)
                {
                    if (dicCell[grid].ContainsKey("TD"))
                    {
                        dicCellInGridMain = sortCellRxLev(dicCell[grid]["TD"]);
                    }
                    else
                    {
                        continue;
                    }
                    if (dicCell[grid].ContainsKey("GSM"))
                    {
                        dicCellInGridOther = sortCellRxLev(dicCell[grid]["GSM"]);
                    }
                    else
                    {
                        continue;
                    }
                }
                else if (inspectType == InspectType.LTE2GSM || inspectType == InspectType.LTE2TD)
                {
                    if (dicCell[grid].ContainsKey("LTE"))
                    {
                        dicCellInGridMain = sortCellRxLev(dicCell[grid]["LTE"]);
                    }
                    else
                    {
                        continue;
                    }

                    if (dicCell[grid].ContainsKey("GSM"))
                    {
                        dicCellInGridOther = sortCellRxLev(dicCell[grid]["GSM"]);
                    }
                    else if (dicCell[grid].ContainsKey("TD"))
                    {
                        dicCellInGridOther = sortCellRxLev(dicCell[grid]["TD"]);
                    }
                    else
                    {
                        continue;
                    }
                }
                else if (inspectType == InspectType.NR2LTE)
                {
                    if (dicCell[grid].ContainsKey("NR"))
                    {
                        dicCellInGridMain = sortCellRxLev(dicCell[grid]["NR"]);
                    }
                    else
                    {
                        continue;
                    }

                    if (dicCell[grid].ContainsKey("LTE"))
                    {
                        dicCellInGridOther = sortCellRxLev(dicCell[grid]["LTE"]);
                    }
                    else
                    {
                        continue;
                    }
                }
                int iLoop = 0;
                double maxRxLev = -9999;
                foreach (int mainCellID in dicCellInGridMain.Keys)
                {
                    if (mainCellID == 0)
                    {
                        continue;
                    }
                    ICell mainCell = null;
                    if (inspectType == InspectType.T2G)
                    {
                        mainCell = CellManager.GetInstance().GetCurrentTDCell(mainCellID);
                    }
                    else if (inspectType == InspectType.LTE2GSM || inspectType == InspectType.LTE2TD)
                    {
                        mainCell = CellManager.GetInstance().GetCurrentLTECell(mainCellID);
                    }
                    else if (inspectType == InspectType.NR2LTE)
                    {
                        mainCell = CellManager.GetInstance().GetCurrentNRCell(mainCellID);
                    }
                    if (mainCell == null)
                    {
                        continue;
                    }
                    iLoop++;
                    /*
                     * 第一强都不一定是主服，所以只分析最强小区，第二强取了也没啥意义
                     * （深圳移动 的TD-GSM的讨论结果）
                     */
                    if (iLoop > 1)
                    {
                        break;
                    }
                    if (dicCellInGridMain[mainCellID].rxlevAvg < cellRxLevMin || dicCellInGridMain[mainCellID].rxlevAvg > cellRxLevMax)
                    {
                        continue;
                    }
                    int iLoop2 = 0;
                    foreach (int otherCellID in dicCellInGridOther.Keys)
                    {
                        if (otherCellID == 0)
                        {
                            continue;
                        }
                        if (dicCellInGridOther[otherCellID].rxlevAvg < nbCellRxLevMin || dicCellInGridOther[otherCellID].rxlevAvg > nbCellRxLevMax)
                        {
                            continue;
                        }
                        ICell nbCell = null;
                        if (inspectType == InspectType.T2G || inspectType == InspectType.LTE2GSM)
                        {
                            nbCell = CellManager.GetInstance().GetCurrentCell(otherCellID);
                        }
                        else if (inspectType == InspectType.LTE2TD)
                        {
                            nbCell = CellManager.GetInstance().GetCurrentTDCell(otherCellID);
                        }
                        else if (inspectType == InspectType.NR2LTE)
                        {
                            nbCell = CellManager.GetInstance().GetCurrentLTECell(otherCellID);
                        }

                        if (nbCell == null)
                        {
                            continue;
                        }
                        iLoop2++;
                        if (iLoop2 == 1)
                        {
                            maxRxLev = dicCellInGridOther[otherCellID].rxlevAvg;
                        }
                        if (topN != 0)
                        {
                            if (iLoop2 > topN)
                            {
                                continue;
                            }
                        }
                        else if (dvalueWithTop1 != 0)
                        {
                            if (maxRxLev - dicCellInGridOther[otherCellID].rxlevAvg > dvalueWithTop1)
                            {
                                break;
                            }
                        }
                        else if (dicCellInGridMain[mainCellID].rxlevAvg > dicCellInGridOther[otherCellID].rxlevAvg)
                        {
                            break;
                        }

                        if (dicResult.ContainsKey(mainCell))
                        {
                            if (dicResult[mainCell].ContainsKey(nbCell))
                            {
                                dicResult[mainCell][nbCell].gridCount++;
                            }
                            else
                            {
                                dicResult[mainCell].Add(nbCell, new MainMissNbPairGridInfo(1, 0, 0));
                            }
                        }
                        else
                        {
                            Dictionary<ICell, MainMissNbPairGridInfo> nbDic = new Dictionary<ICell, MainMissNbPairGridInfo>();
                            nbDic.Add(nbCell, new MainMissNbPairGridInfo(1, 0, 0));
                            dicResult.Add(mainCell, nbDic);
                        }
                        dicResult[mainCell][nbCell].sampleCountNB += dicCellInGridOther[otherCellID].sampleNum;
                        dicResult[mainCell][nbCell].AddCellGridInfo(grid, dicCellInGridMain[mainCellID], dicCellInGridOther[otherCellID]);
                    }
                }
            }

            //==================== Compare with the configuration =====================
            if (inspectType == InspectType.T2G)
            {
                return CompareWithConfig_TD(inspectType, dicResult);
            }
            else if (inspectType == InspectType.LTE2GSM || inspectType == InspectType.LTE2TD)
            {
                return CompareWithConfig_LTE(inspectType, dicResult);
            }
            else if (inspectType == InspectType.NR2LTE)
            {
                return CompareWithConfig_NR(inspectType, dicResult);
            }
            else
            {
                return null;
            }
        }

        public static Dictionary<int, GSMCellRxLev> sortCellRxLev(Dictionary<int, GSMCellRxLev> dicCellInGrid)
        {
            List<int> cells = new List<int>();
            List<GSMCellRxLev> cellRxLevs = new List<GSMCellRxLev>();
            foreach (int cellID in dicCellInGrid.Keys)
            {
                if (cellRxLevs.Count == 0)
                {
                    cellRxLevs.Add(dicCellInGrid[cellID]);
                    cells.Add(cellID);
                    continue;
                }
                bool bBreak = false;
                for (int i = 0; i < cellRxLevs.Count; i++)
                {
                    if (cellRxLevs[i].rxlevAvg < dicCellInGrid[cellID].rxlevAvg)
                    {
                        cellRxLevs.Insert(i, dicCellInGrid[cellID]);
                        cells.Insert(i, cellID);
                        bBreak = true;
                        break;
                    }
                }
                if (!bBreak)
                {
                    cellRxLevs.Add(dicCellInGrid[cellID]);
                    cells.Add(cellID);
                }
            }
            Dictionary<int, GSMCellRxLev> refDic = new Dictionary<int, GSMCellRxLev>();
            foreach (int cellID in cells)
            {
                refDic[cellID] = dicCellInGrid[cellID];
            }
            return refDic;
        }

        public static List<MissNbCellSubResult> CompareWithConfig_GSM(InspectType type, Dictionary<ICell, Dictionary<ICell, MainMissNbPairGridInfo>> mainNbDic)
        {
            List<MissNbCellSubResult> nbList = new List<MissNbCellSubResult>();
            foreach (ICell mainObj in mainNbDic.Keys)
            {
                CompareNBCell_GSM((Cell)mainObj, mainNbDic[mainObj], ((Cell)mainObj).GetNeighbourCells(), ref nbList);
            }
            return nbList;
        }

        public static List<MissNbCellSubResult> CompareWithConfig_LTE(InspectType type, Dictionary<ICell, Dictionary<ICell, MainMissNbPairGridInfo>> mainNbDic)
        {
            List<MissNbCellSubResult> nbList = new List<MissNbCellSubResult>();
            foreach (ICell mainCellObj in mainNbDic.Keys)
            {
                List<ICell> nbCellCfg = new List<ICell>();
                if (type == InspectType.LTE2GSM)
                {
                    foreach (Cell cell in ((LTECell)mainCellObj).NeighbourGSMCells)
                    {
                        nbCellCfg.Add(cell);
                    }
                }
                else if (type == InspectType.LTE2TD)
                {
                    foreach (TDCell cell in ((LTECell)mainCellObj).NeighbourTDCells)
                    {
                        nbCellCfg.Add(cell);
                    }
                }
                else if (type == InspectType.LTE2LTE)
                {
                    foreach (LTECell cell in ((LTECell)mainCellObj).NeighbourCells)
                    {
                        nbCellCfg.Add(cell);
                    }
                }
                CompareNBCell_LTE((LTECell)mainCellObj, mainNbDic[mainCellObj], nbCellCfg, ref nbList);
            }
            return nbList;
        }

        public static List<MissNbCellSubResult> CompareWithConfig_NR(InspectType type, Dictionary<ICell, Dictionary<ICell, MainMissNbPairGridInfo>> mainNbDic)
        {
            List<MissNbCellSubResult> nbList = new List<MissNbCellSubResult>();
            //foreach (ICell mainCellObj in mainNbDic.Keys)
            //{
            //    List<ICell> nbCellCfg = new List<ICell>();
            //    if (type == InspectType.NR2LTE)
            //    {
            //        foreach (Cell cell in ((NRCell)mainCellObj).NeighbourTDCells)
            //        {
            //            nbCellCfg.Add(cell);
            //        }
            //    }
            //    else if (type == InspectType.LTE2TD)
            //    {
            //        foreach (TDCell cell in ((LTECell)mainCellObj).NeighbourTDCells)
            //        {
            //            nbCellCfg.Add(cell);
            //        }
            //    }
            //    else if (type == InspectType.LTE2LTE)
            //    {
            //        foreach (LTECell cell in ((LTECell)mainCellObj).NeighbourCells)
            //        {
            //            nbCellCfg.Add(cell);
            //        }
            //    }
            //    CompareNBCell_LTE((LTECell)mainCellObj, mainNbDic[mainCellObj], nbCellCfg, ref nbList);
            //}
            return nbList;
        }

        private static void CompareNBCell_LTE(LTECell mainCell, Dictionary<ICell, MainMissNbPairGridInfo> dicNBReal, List<ICell> nbCellsCfg, ref List<MissNbCellSubResult> nbList)
        {
            #region 从测试数据分析
            foreach (ICell nbCellObj in dicNBReal.Keys)
            {
                MissNbCellSubResult result = new MissNbCellSubResult(mainCell, nbCellObj);
                result.gridnum = dicNBReal[nbCellObj].gridCount;
                result.sampleNum = dicNBReal[nbCellObj].sampleCountNB;
                double mainCellRxLevAvg;
                double nbCellRxLevAvg;
                Dictionary<GridItem, double> mainCellGridDic;
                Dictionary<GridItem, double> nbCellGridDic;
                dicNBReal[nbCellObj].PerformSummary(out mainCellGridDic, out mainCellRxLevAvg, out nbCellGridDic, out nbCellRxLevAvg);
                result.MainCellRxLevAvg = mainCellRxLevAvg;
                result.MainCellGrids = mainCellGridDic;
                result.NBCellRxLevAvg = nbCellRxLevAvg;
                result.NBCellGrids = nbCellGridDic;
                SetDistanceAndAngle(mainCell.Longitude, mainCell.Latitude, mainCell.Direction, nbCellObj.Longitude, nbCellObj.Latitude, ref result.distance, ref result.isInAngle);
                result.Angle = CaleAngle(mainCell.Direction, nbCellObj.Direction);
                result.result = nbCellsCfg.Contains(nbCellObj) ? "相同" : "数据中有，配置中未找到";
                nbList.Add(result);
            }
            #endregion

            #region 从配置分析
            foreach (ICell nbCell in nbCellsCfg)
            {
                if (!dicNBReal.ContainsKey(nbCell))
                {
                    MissNbCellSubResult result = new MissNbCellSubResult(mainCell, (ICell)nbCell);
                    result.result = "配置有，数据中未找到";
                    result.gridnum = 0;
                    result.sampleNum = 0;
                    SetDistanceAndAngle(mainCell.Longitude, mainCell.Latitude, mainCell.Direction, nbCell.Longitude, nbCell.Latitude, ref result.distance, ref result.isInAngle);
                    result.Angle = CaleAngle(mainCell.Direction, nbCell.Direction);
                    nbList.Add(result);
                }
            }
            #endregion
        }

        public static List<MissNbCellSubResult> CompareWithConfig_TD(InspectType type, Dictionary<ICell, Dictionary<ICell, MainMissNbPairGridInfo>> mainNbResult)
        {
            List<MissNbCellSubResult> nbList = new List<MissNbCellSubResult>();
            foreach (ICell objCell in mainNbResult.Keys)
            {
                TDCell mainCell = objCell as TDCell;
                //List<ICell> nbCells = null;
                List<ICell> nbCells = new List<ICell>();
                if (type == InspectType.T2G)
                {
                    foreach (Cell cell in mainCell.NeighbourCells)
                    {
                        nbCells.Add(cell);
                    }
                }
                else if (type == InspectType.T2T)
                {
                    foreach (TDCell cell in mainCell.NeighbourTDCells)
                    {
                        nbCells.Add(cell);
                    }
                }
                CompareNBCell_TD(mainCell, mainNbResult[objCell], nbCells, ref nbList);
            }
            return nbList;
        }

        public static void CompareNBCell_GSM(Cell mainCell, Dictionary<ICell, MainMissNbPairGridInfo> dicNBReal, List<Cell> nbCellsConfig, ref List<MissNbCellSubResult> nbList)
        {
            foreach (ICell nbCellObj in dicNBReal.Keys)
            {
                Cell nbCell = nbCellObj as Cell;
                MissNbCellSubResult result = new MissNbCellSubResult((ICell)mainCell, (ICell)nbCell);
                result.gridnum = dicNBReal[nbCellObj].gridCount;
                result.sampleNum = dicNBReal[nbCellObj].sampleCountNB;
                double mainCellRxLevAvg;
                double nbCellRxLevAvg;
                Dictionary<GridItem, double> mainCellGridDic;
                Dictionary<GridItem, double> nbCellGridDic;
                dicNBReal[nbCellObj].PerformSummary(out mainCellGridDic, out mainCellRxLevAvg, out nbCellGridDic, out nbCellRxLevAvg);
                result.MainCellRxLevAvg = mainCellRxLevAvg;
                result.MainCellGrids = mainCellGridDic;
                result.NBCellRxLevAvg = nbCellRxLevAvg;
                result.NBCellGrids = nbCellGridDic;
                SetDistanceAndAngle(mainCell.Longitude, mainCell.Latitude, mainCell.Direction, nbCell.Longitude, nbCell.Latitude, ref result.distance, ref result.isInAngle);
                result.Angle = CaleAngle(mainCell.Direction, nbCell.Direction);
                result.result = nbCellsConfig.Contains(nbCell) ? "相同" : "数据中有，配置中未找到";
                nbList.Add(result);
            }

            foreach (ICell nbCellObj in nbCellsConfig)
            {
                if (!dicNBReal.ContainsKey(nbCellObj))
                {
                    MissNbCellSubResult result = new MissNbCellSubResult((ICell)mainCell, (ICell)nbCellObj);
                    Cell nbCell = nbCellObj as Cell;
                    result.result = "配置有，数据中未找到";
                    result.gridnum = 0;
                    result.sampleNum = 0;
                    SetDistanceAndAngle(mainCell.Longitude, mainCell.Latitude, mainCell.Direction, nbCell.Longitude, nbCell.Latitude, ref result.distance, ref result.isInAngle);
                    result.Angle = CaleAngle(mainCell.Direction, nbCell.Direction);
                    nbList.Add(result);
                }
            }
        }

        public static void CompareNBCell_TD(TDCell mainCell, Dictionary<ICell, MainMissNbPairGridInfo> dicNBReal, List<ICell> nbCellsConfig, ref List<MissNbCellSubResult> nbList)
        {
            #region 从测试数据分析
            foreach (ICell nbCell in dicNBReal.Keys)
            {
                MissNbCellSubResult result = new MissNbCellSubResult(mainCell, nbCell);
                result.gridnum = dicNBReal[nbCell].gridCount;
                result.sampleNum = dicNBReal[nbCell].sampleCountNB;
                double mainCellRxLevAvg;
                double nbCellRxLevAvg;
                Dictionary<GridItem, double> mainCellGridDic;
                Dictionary<GridItem, double> nbCellGridDic;
                dicNBReal[nbCell].PerformSummary(out mainCellGridDic, out mainCellRxLevAvg, out nbCellGridDic, out nbCellRxLevAvg);
                result.MainCellRxLevAvg = mainCellRxLevAvg;
                result.MainCellGrids = mainCellGridDic;
                result.NBCellRxLevAvg = nbCellRxLevAvg;
                result.NBCellGrids = nbCellGridDic;

                SetDistanceAndAngle(mainCell.Longitude, mainCell.Latitude, mainCell.Direction, nbCell.Longitude, nbCell.Latitude, ref result.distance, ref result.isInAngle);
                result.Angle = CaleAngle(mainCell.Direction, nbCell.Direction);
                result.result = nbCellsConfig.Contains(nbCell) ? "相同" : "数据中有，配置中未找到";
                nbList.Add(result);
            }
            #endregion

            #region 从配置分析
            foreach (ICell nbCellObj in nbCellsConfig)
            {
                if (!dicNBReal.ContainsKey(nbCellObj))
                {
                    MissNbCellSubResult result = new MissNbCellSubResult(mainCell, nbCellObj);
                    result.result = "配置有，数据中未找到";
                    result.gridnum = 0;
                    result.sampleNum = 0;
                    SetDistanceAndAngle(mainCell.Longitude, mainCell.Latitude, mainCell.Direction, nbCellObj.Longitude, nbCellObj.Latitude, ref result.distance, ref result.isInAngle);
                    result.Angle = CaleAngle(mainCell.Direction, nbCellObj.Direction);
                    nbList.Add(result);
                }
            }
            #endregion
        }

        public static List<MissNbCellSubResult> CompareWithConfig_W(InspectType type, Dictionary<ICell, Dictionary<ICell, MainMissNbPairGridInfo>> dicResult)
        {
            List<MissNbCellSubResult> nbList = new List<MissNbCellSubResult>();

            foreach (ICell mainCell in dicResult.Keys)
            {
                CompareNBCell_W((WCell)mainCell, dicResult[mainCell], ((WCell)mainCell).NeighbourWCells, ref nbList);
            }
            return nbList;
        }

        public static void CompareNBCell_W(WCell mainCell, Dictionary<ICell, MainMissNbPairGridInfo> dicNBReal, List<WCell> nbCellsCfg, ref List<MissNbCellSubResult> nbList)
        {
            foreach (ICell nbCell in dicNBReal.Keys)
            {
                MissNbCellSubResult result = new MissNbCellSubResult((ICell)mainCell, (ICell)nbCell);
                result.gridnum = dicNBReal[nbCell].gridCount;
                result.sampleNum = dicNBReal[nbCell].sampleCountNB;
                double mainCellRxLevAvg;
                double nbCellRxLevAvg;
                Dictionary<GridItem, double> mainCellGridDic;
                Dictionary<GridItem, double> nbCellGridDic;
                dicNBReal[nbCell].PerformSummary(out mainCellGridDic, out mainCellRxLevAvg, out nbCellGridDic, out nbCellRxLevAvg);
                result.MainCellRxLevAvg = mainCellRxLevAvg;
                result.MainCellGrids = mainCellGridDic;
                result.NBCellRxLevAvg = nbCellRxLevAvg;
                result.NBCellGrids = nbCellGridDic;
                SetDistanceAndAngle(mainCell.Longitude, mainCell.Latitude, (short)mainCell.Direction, nbCell.Longitude, nbCell.Latitude, ref result.distance, ref result.isInAngle);
                result.Angle = CaleAngle((short)mainCell.Direction, (short)nbCell.Direction);
                result.result = nbCellsCfg.Contains((WCell)nbCell) ? "相同" : "数据中有，配置中未找到";
                nbList.Add(result);
            }

            foreach (WCell nbCell in nbCellsCfg)
            {
                if (!dicNBReal.ContainsKey(nbCell))
                {
                    MissNbCellSubResult result = new MissNbCellSubResult((ICell)mainCell, (ICell)nbCell);
                    result.result = "配置有，数据中未找到";
                    SetDistanceAndAngle(mainCell.Longitude, mainCell.Latitude, (short)mainCell.Direction, nbCell.Longitude, nbCell.Latitude, ref result.distance, ref result.isInAngle);
                    result.Angle = CaleAngle((short)mainCell.Direction, (short)nbCell.Direction);
                    nbList.Add(result);
                }
            }
        }

        public static void SetDistanceAndAngle(double orgLongitude, double orgLatitude, short orgDirection, double nbLongitude, double nbLatitude,
            ref double distance, ref bool bIsInAngle)
        {
            distance = MathFuncs.GetDistance(orgLongitude, orgLatitude, nbLongitude, nbLatitude);
            bIsInAngle = MathFuncs.JudgePoint(orgLongitude, orgLatitude, nbLongitude, nbLatitude, orgDirection);
        }

        public static void CalAngle(int cellID, int otherCellID, ref MissNbCellSubResult result)
        {
            Cell orgCell = CellManager.GetInstance().GetCurrentCell(cellID);
            Cell nbCell = CellManager.GetInstance().GetCurrentCell(otherCellID);

            if ((orgCell != null) && (nbCell != null))
            {
                float angle = Math.Abs(orgCell.Direction - nbCell.Direction);
                result.Angle = angle > 180F ? 360F - angle : angle;
            }
            else
            {
                result.Angle = -1;
            }
        }

        public static float CaleAngle(short orgDirection, short nbDirection)
        {
            float angle = Math.Abs(orgDirection - nbDirection);
            return angle > 180F ? 360F - angle : angle;
        }

        private static List<Cell> GetNBCellByConfig_GSM(MainModel mainModel, int cellID)
        {
            Cell cell = CellManager.GetInstance().GetCurrentCell(cellID);
            return GetNBCellByConfig_GSM(mainModel, cell);
        }

        public static List<Cell> GetNBCellByConfig_GSM(MainModel mainModel, Cell cell)
        {
            List<Cell> cells = new List<Cell>();
            if (cell != null)
            {
                cells = cell.GetNeighbourCells();
            }
            return cells;
        }

        private static void GetNBCellByConfig_W(MainModel mainModel, int cellID, ref List<WCell> wCellList, ref List<Cell> cellList)
        {
            WCell cell = CellManager.GetInstance().GetCurrentWCell(cellID);
            GetNBCellByConfig_W(mainModel, cell, ref wCellList, ref cellList);
        }

        public static void GetNBCellByConfig_W(MainModel mainModel, WCell cell, ref List<WCell> wCellList, ref List<Cell> cellList)
        {
            if (cell != null)
            {
                cellList = cell.NeighbourGCells;
                wCellList = cell.NeighbourWCells;
            }
        }
    }

    public class MainMissNbPairGridInfo
    {
        public MainMissNbPairGridInfo(int gridCount, int sampleCount, double rxlevMean)
        {
            this.gridCount = gridCount;
            this.sampleCountNB = sampleCount;
        }

        public int sampleCountNB = 0;
        public int gridCount = 0;

        private double mainCellRxlevMean = 0;
        public double MainCellRxlevMean
        {
            get { return mainCellRxlevMean; }
            set { mainCellRxlevMean = value; }
        }

        public void AddCellGridInfo(GridItem grid, GSMCellRxLev mainCellRxLevInfo, GSMCellRxLev nbCellRxLevInfo)
        {
            mainCellGridDic.Add(grid, mainCellRxLevInfo);
            nbCellGridDic.Add(grid, nbCellRxLevInfo);
        }

        /// <summary>
        /// 汇总计算
        /// </summary>
        /// <param name="mainCellGridRxLevDic">主小区栅格平均场强字典</param>
        /// <param name="nbCellGridRxLevDic">邻区栅格平均场强字典</param>
        public void PerformSummary(out Dictionary<GridItem, double> mainCellGridRxLevDic, out double mainCellRxlevAvg
            , out Dictionary<GridItem, double> nbCellGridRxLevDic, out double nbCellRxlevAvg)
        {
            mainCellGridRxLevDic = new Dictionary<GridItem, double>();
            nbCellGridRxLevDic = new Dictionary<GridItem, double>();
            double allGridRxlevSum = 0;
            foreach (KeyValuePair<GridItem, GSMCellRxLev> pair in mainCellGridDic)
            {
                allGridRxlevSum += pair.Value.rxlevAvg;
                mainCellGridRxLevDic.Add(pair.Key, Math.Round(pair.Value.rxlevAvg, 2));
            }
            mainCellRxlevAvg = Math.Round(allGridRxlevSum / mainCellGridDic.Count, 2);

            allGridRxlevSum = 0;
            foreach (KeyValuePair<GridItem, GSMCellRxLev> pair in nbCellGridDic)
            {
                allGridRxlevSum += pair.Value.rxlevAvg;
                nbCellGridRxLevDic.Add(pair.Key, Math.Round(pair.Value.rxlevAvg, 2));
            }
            nbCellRxlevAvg = Math.Round(allGridRxlevSum / nbCellGridDic.Count, 2);
        }

        /// <summary>
        /// 参考小区，栅格-栅格信息
        /// </summary>
        private Dictionary<GridItem, GSMCellRxLev> mainCellGridDic = new Dictionary<GridItem, GSMCellRxLev>();
        /// <summary>
        /// 目标小区，栅格-栅格信息
        /// </summary>
        private Dictionary<GridItem, GSMCellRxLev> nbCellGridDic = new Dictionary<GridItem, GSMCellRxLev>();
    }

    public class MissNbCellSubResult
    {
        private ICell mainCell = null;
        /// <summary>
        /// 主小区
        /// </summary>
        public ICell MainCell
        {
            get { return mainCell; }
            set { mainCell = value; }
        }
        //public int MainCellID
        //{
        //    get
        //    {
        //        if (mainCell is Cell)
        //        {
        //            return ((Cell)mainCell).ID;
        //        }
        //        else if (mainCell is TDCell)
        //        {
        //            return ((TDCell)mainCell).ID;
        //        }
        //        else if (mainCell is LTECell)
        //        {
        //            return ((LTECell)mainCell).ID;
        //        }
        //        else if (mainCell is WCell)
        //        {
        //            return ((WCell)mainCell).ID;
        //        }
        //        else if (mainCell is CDCell)
        //        {
        //            return ((CDCell)mainCell).ID;
        //        }
        //        return -1;
        //    }
        //}

        //public int NbCellID
        //{
        //    get
        //    {
        //        if (nbCell is Cell)
        //        {
        //            return ((Cell)nbCell).ID;
        //        }
        //        else if (nbCell is TDCell)
        //        {
        //            return ((TDCell)nbCell).ID;
        //        }
        //        else if (nbCell is LTECell)
        //        {
        //            return ((LTECell)nbCell).ID;
        //        }
        //        else if (nbCell is WCell)
        //        {
        //            return ((WCell)nbCell).ID;
        //        }
        //        else if (nbCell is CDCell)
        //        {
        //            return ((CDCell)nbCell).ID;
        //        }
        //        return -1;
        //    }
        //}
        private ICell nbCell = null;
        /// <summary>
        /// 邻区
        /// </summary>
        public ICell NbCell
        {
            get { return nbCell; }
            set { nbCell = value; }
        }

        /// <summary>
        /// 是否为mod3邻区
        /// </summary>
        private bool isMod3NBCell = false;
        public bool IsMod3NBCell
        {
            get { return isMod3NBCell; }
            set { isMod3NBCell = value; }
        }
        /// <summary>
        /// 是否室内小区 1：是 2否
        /// </summary>
        public int NbCellInDoor
        {
            get
            {
                if (nbCell is Cell)
                {
                    return ((Cell)nbCell).Type == BTSType.Indoor ? 1 : 2;
                }
                else if (nbCell is TDCell)
                {
                    return ((TDCell)nbCell).Type == TDNodeBType.Indoor ? 1 : 2;
                }
                else if (nbCell is WCell)
                {
                    return ((WCell)nbCell).Type == WNodeBType.Indoor ? 1 : 2;
                }
                else if (nbCell is LTECell)
                {
                    return ((LTECell)nbCell).Type == LTEBTSType.Indoor ? 1 : 2;
                }
                return -1;
            }
        }

        public MissNbCellSubResult(ICell mainCell, ICell nbCell)
        {
            this.mainCell = mainCell;
            this.nbCell = nbCell;
        }

        public string result;
        public int gridnum;
        public int sampleNum;
        public double distance = -9999999;
        public bool isInAngle = false;
        public float Angle
        {
            get { return angle; }
            set
            {
                if (value < 0)
                {

                }
                angle = value;
            }
        }
        private float angle = -1;
        public string InAngle
        {
            get { return isInAngle ? "是" : "否"; }
        }
        private double mainCellRxlev = double.NaN;
        public double MainCellRxLevAvg
        {
            get { return mainCellRxlev; }
            set { mainCellRxlev = value; }
        }
        public string OneGridPos
        {
            get
            {
                string str = string.Empty;
                foreach (GridItem grid in MainCellGrids.Keys)
                {
                    str = string.Format("{0},{1}", grid.CenterLng, grid.CenterLat);
                    break;
                }
                return str;
            }
        }
        private Dictionary<GridItem, double> mainCellGridRxlevDic = new Dictionary<GridItem, double>();
        public Dictionary<GridItem, double> MainCellGrids
        {
            get { return mainCellGridRxlevDic; }
            set { mainCellGridRxlevDic = value; }
        }

        private double nbCellRxlevAvg = double.NaN;
        public double NBCellRxLevAvg
        {
            get { return nbCellRxlevAvg; }
            set { nbCellRxlevAvg = value; }
        }

        public GridMatrix<ColorUnit> GetNBCellGridMatrix(InspectType inspectType)
        {
            GridMatrix<ColorUnit> colorMatrix = new GridMatrix<ColorUnit>();
            foreach (KeyValuePair<GridItem, double> pair in nbCellGridRxlevDic)
            {
                KPIStatDataBase imgData = null;
                if (inspectType == InspectType.GSM || inspectType == InspectType.T2G || inspectType == InspectType.LTE2GSM)
                {
                    imgData = new StatDataSCAN_GSM();
                    imgData[StatDataSCAN_GSM.RxqualMeanValueKey, -1] = pair.Value;
                }
                else if (inspectType == InspectType.T2T || inspectType == InspectType.LTE2TD)
                {
                    imgData = new StatDataSCAN_TD();
                    imgData[StatDataSCAN_TD.PCCPCHRSCPMeanValueKey, -1] = pair.Value;
                }
                else if (inspectType == InspectType.W2W)
                {
                    imgData = new StatDataSCAN_WCDMA();
                    imgData[StatDataSCAN_WCDMA.RSCPMeanValueKey, -1] = pair.Value;
                }
                else if (inspectType == InspectType.LTE2LTE)
                {
                    imgData = new StatDataSCAN_LTE();
                    imgData[StatDataSCAN_LTE.PSSRPMeanValueKey, -1] = pair.Value;
                }
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(pair.Key.CenterLng, pair.Key.CenterLat, out rAt, out cAt);
                ColorUnit cu = new ColorUnit();
                colorMatrix[rAt, cAt] = cu;
                double ltLng, laLat;
                GridHelper.GetLeftTopByCustomSizeGridIndex(1, rAt, cAt, out ltLng, out laLat);
                cu.LTLng = ltLng;
                cu.LTLat = laLat;
                cu.Status = 1;
                cu.DataHub.AddStatData(imgData, false);
            }
            return colorMatrix;
        }

        public void GetNBCellGridMatrix(InspectType inspectType, GridMatrix<ColorUnit> colorMatrix)
        {
            foreach (KeyValuePair<GridItem, double> pair in nbCellGridRxlevDic)
            {
                KPIStatDataBase imgData = null;
                if (inspectType == InspectType.GSM || inspectType == InspectType.T2G || inspectType == InspectType.LTE2GSM)
                {
                    imgData = new StatDataSCAN_GSM();
                    imgData[StatDataSCAN_GSM.RxqualMeanValueKey, -1] = pair.Value;
                }
                else if (inspectType == InspectType.T2T || inspectType == InspectType.LTE2TD)
                {
                    imgData = new StatDataSCAN_TD();
                    imgData[StatDataSCAN_TD.PCCPCHRSCPMeanValueKey, -1] = pair.Value;
                }
                else if (inspectType == InspectType.W2W)
                {
                    imgData = new StatDataSCAN_WCDMA();
                    imgData[StatDataSCAN_WCDMA.RSCPMeanValueKey, -1] = pair.Value;
                }
                else if (inspectType == InspectType.LTE2LTE)
                {
                    imgData = new StatDataSCAN_LTE();
                    imgData[StatDataSCAN_LTE.PSSRPMeanValueKey, -1] = pair.Value;
                }
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(pair.Key.CenterLng, pair.Key.CenterLat, out rAt, out cAt);
                ColorUnit cu = new ColorUnit();
                colorMatrix[rAt, cAt] = cu;
                double ltLng, laLat;
                GridHelper.GetLeftTopByCustomSizeGridIndex(1, rAt, cAt, out ltLng, out laLat);
                cu.LTLng = ltLng;
                cu.LTLat = laLat;
                cu.Status = 1;
                cu.DataHub.AddStatData(imgData, false);
            }
        }

        private Dictionary<GridItem, double> nbCellGridRxlevDic = new Dictionary<GridItem, double>();
        public Dictionary<GridItem, double> NBCellGrids
        {
            get { return nbCellGridRxlevDic; }
            set { nbCellGridRxlevDic = value; }
        }
        public bool visible = true;

        public bool CanShow(string NBCellStatus, int gridCountMin, string NBCellDisMin, string NBCellDisMax,
                            string AngleMin, string AngleMax, int InAngle, int IncludeIndoor, int sampleCount)
        {
            if (NBCellStatus != "全部" && NBCellStatus != result)
            {
                visible = false;
                return false;
            }

            if (gridCountMin > gridnum)
            {
                visible = false;
                return false;
            }

            if (sampleNum < sampleCount)
            {
                visible = false;
                return false;
            }

            if (Convert.ToInt32(NBCellDisMin) > distance
                || Convert.ToInt32(NBCellDisMax) < distance)
            {
                visible = false;
                return false;
            }

            if ((Convert.ToInt32(AngleMin) > Angle || Convert.ToInt32(AngleMax) < Angle) && angle > 0)
            {
                visible = false;
                return false;
            }

            if ((InAngle == 1 && !isInAngle) || (InAngle == 2 && isInAngle))
            {
                visible = false;
                return false;
            }

            if (IncludeIndoor != 0 && (NbCellInDoor == 0 || NbCellInDoor != IncludeIndoor))
            {
                visible = false;
                return false;
            }

            visible = true;
            return true;
        }

        private string roadDesc = null;
        public string RoadDesc
        {
            get
            {
                return ""; //added by wj 屏蔽获取道路信息，避免性能问题
                if (roadDesc == null)
                {
                    List<double> lonList = new List<double>();
                    List<double> latList = new List<double>();
                    foreach (GridItem grid in mainCellGridRxlevDic.Keys)
                    {
                        lonList.Add(grid.CenterLng);
                        latList.Add(grid.CenterLat);
                    }
                    roadDesc = GISManager.GetInstance().GetRoadPlaceDesc(lonList, latList);
                }
                return roadDesc;
            }
        }
    }

    public class ScanInterfereResultManager
    {
        public ScanInterfereResultManager(int model, MapForm.DisplayInterferenceType interferenceType)
        {
            if (model == 0)
            {
                if (interferenceType == MapForm.DisplayInterferenceType.BCCHTCH)
                {
                    ScanInterfereResultType type1 = new ScanInterfereResultType("CoBCCH");
                    scanInterfereTypeList.Add(type1);
                    ScanInterfereResultType type2 = new ScanInterfereResultType("CoTCH");
                    scanInterfereTypeList.Add(type2);
                }
                else if (interferenceType == MapForm.DisplayInterferenceType.BCCH_CPI)
                {
                    ScanInterfereResultType type1 = new ScanInterfereResultType("CoBCCH");
                    scanInterfereTypeList.Add(type1);
                }
                else if (interferenceType == MapForm.DisplayInterferenceType.TCH_FREQ)
                {
                    ScanInterfereResultType type2 = new ScanInterfereResultType("CoTCH");
                    scanInterfereTypeList.Add(type2);
                }
            }
            else
            {
                if (interferenceType == MapForm.DisplayInterferenceType.BCCHTCH)
                {
                    ScanInterfereResultType type3 = new ScanInterfereResultType("AdjBCCH");
                    scanInterfereTypeList.Add(type3);
                    ScanInterfereResultType type4 = new ScanInterfereResultType("AdjTCH");
                    scanInterfereTypeList.Add(type4);
                }
                else if (interferenceType == MapForm.DisplayInterferenceType.BCCH_CPI)
                {
                    ScanInterfereResultType type3 = new ScanInterfereResultType("AdjBCCH");
                    scanInterfereTypeList.Add(type3);
                }
                else if (interferenceType == MapForm.DisplayInterferenceType.TCH_FREQ)
                {
                    ScanInterfereResultType type3 = new ScanInterfereResultType("AdjBCCH");
                    scanInterfereTypeList.Add(type3);
                    ScanInterfereResultType type4 = new ScanInterfereResultType("AdjTCH");
                    scanInterfereTypeList.Add(type4);
                }
            }
        }

        public List<ScanInterfereResultType> scanInterfereTypeList = new List<ScanInterfereResultType>();

        public void AddScanInterfereResult(ScanInterfereResult result, string typeName)
        {
            foreach (ScanInterfereResultType resultType in scanInterfereTypeList)
            {
                if (resultType.typeName.Equals(typeName))
                {
                    result.index = resultType.scanInterfereList.Count + 1;
                    resultType.scanInterfereList.Add(result);
                    break;
                }
            }
        }
    }

    public class ScanInterfereResultType
    {
        public ScanInterfereResultType(string typeName)
        {
            this.typeName = typeName;
        }

        public string typeName;
        public List<ScanInterfereResult> scanInterfereList = new List<ScanInterfereResult>();
    }

    public class ScanInterfereResult : Object
    {
        public int index;
        public int cellIDOne;
        public double rxLevMeanOne;
        public int lacOne;
        public int ciOne;
        public int bcchOne;
        public string tchOne;
        public int sampleCountOne = 0;
        public List<GridItem> gridsOne = new List<GridItem>();
        public Dictionary<GridItem, GSMCellRxLev> gridCellsOne = new Dictionary<GridItem, GSMCellRxLev>();

        public int cellIDTwo;
        public double rxLevMeanTwo;
        public int lacTwo;
        public int ciTwo;
        public int bcchTwo;
        public int tchTwo;
        public int sampleCountTwo = 0;
        public List<GridItem> gridsTwo = new List<GridItem>();
        public Dictionary<GridItem, GSMCellRxLev> gridCellsTwo = new Dictionary<GridItem, GSMCellRxLev>();

        public short bcch;
        public float angle;

        public double RxLevMeanOne
        {
            get { return Math.Round(rxLevMeanOne, 2); }
        }
        public double RxLevMeanTwo
        {
            get { return Math.Round(rxLevMeanTwo, 2); }
        }

        public override bool Equals(object obj)
        {
            if (!(obj is ScanInterfereResult))
            {
                return false;
            }
            ScanInterfereResult other = obj as ScanInterfereResult;
            if (bcch != other.bcch)
            {
                return false;
            }
            if (!((cellIDOne.Equals(other.cellIDOne) && cellIDTwo.Equals(other.cellIDTwo)) ||
                (cellIDTwo.Equals(other.cellIDOne) && cellIDOne.Equals(other.cellIDTwo))))
            {
                return false;
            }
            return true;
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        public ScanInterfereResult Clone()
        {
            ScanInterfereResult refResult = new ScanInterfereResult();
            refResult.index = this.index;
            refResult.cellIDOne = this.cellIDOne;
            refResult.rxLevMeanOne = this.rxLevMeanOne;
            refResult.bcchOne = this.bcchOne;
            refResult.sampleCountOne = this.sampleCountOne;
            refResult.gridsOne.AddRange(this.gridsOne);
            foreach (KeyValuePair<GridItem, GSMCellRxLev> keyValue in this.gridCellsOne)
            {
                refResult.gridCellsOne.Add(keyValue.Key, keyValue.Value);
            }

            refResult.cellIDTwo = this.cellIDTwo;
            refResult.rxLevMeanTwo = this.rxLevMeanTwo;
            refResult.bcchTwo = this.bcchTwo;
            refResult.sampleCountTwo = this.sampleCountTwo;
            refResult.gridsTwo.AddRange(this.gridsTwo);
            foreach (KeyValuePair<GridItem, GSMCellRxLev> keyValue in this.gridCellsTwo)
            {
                refResult.gridCellsTwo.Add(keyValue.Key, keyValue.Value);
            }

            refResult.bcch = this.bcch;
            refResult.angle = this.angle;

            return refResult;
        }
    }

    public class ScanShearResult : Object
    {
        public int cellIDMain;
        public double rxLevMeanMain;
        public int sampleCountMain = 0;
        public byte bsicMain;

        public int cellIDOther;
        public double rxLevMeanOther;
        public int sampleCountOther = 0;
        public byte bsicOther;

        public List<GridItem> grids = new List<GridItem>();
        public short bcch;
        public double distance;

        public double RxLevMeanMain
        {
            get { return Math.Round(rxLevMeanMain, 2); }
        }
        public double RxLevMeanOther
        {
            get { return Math.Round(rxLevMeanOther, 2); }
        }
        public double Distance
        {
            get { return Math.Round(distance, 2); }
        }

        public override bool Equals(object obj)
        {
            if (!(obj is ScanShearResult))
            {
                return false;
            }
            ScanShearResult other = obj as ScanShearResult;
            if (bcch != other.bcch)
            {
                return false;
            }
            if (!(cellIDMain.Equals(other.cellIDMain) && cellIDOther.Equals(other.cellIDOther)))
            {
                return false;
            }
            return true;
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}
