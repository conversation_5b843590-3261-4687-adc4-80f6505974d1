﻿using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTScanGridLayer : LayerBase
    {
        public enum RenderingIndex
        {
            R0_RP,
            R0_RQ,
            R0_CINR,
            SSS_RSSI,
            SSS_RP
        }

        public ZTScanGridLayer()
            : base("NBIOT扫频栅格图层")
        {

        }
        
        /// <summary>
        /// 本次选择的栅格
        /// </summary>
        public ScanGridInfo SelectedGrid { get; set; }

        public event EventHandler SelectedGridChanged;

        /// <summary>
        /// 需渲染的栅格数据合集
        /// </summary>
        public List<ScanGridInfo> GridInfos
        {
            get;
            set;
        }
        /// <summary>
        /// 已选择栅格的边框画笔
        /// </summary>
        private readonly Pen penSelected = new Pen(Color.Red, 3);

        /// <summary>
        /// 当前渲染指标
        /// </summary>
        private RenderingIndex curRangeType
        {
            get;
            set;
        }

        public string SerialInfoName
        {
            get;
            set;
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || GridInfos == null || GridInfos.Count <= 0)
            {
                return;
            }
            DbRect dRect;
            GisAdapter.FromDisplay(updateRect, out dRect);

            //选中栅格包含的频点,PCI合集
            List<ScanGridInfo> selectGridCellList = new List<ScanGridInfo>();
            InitColorBySelectedSerials();
            foreach (ScanGridInfo grid in GridInfos)
            {
                drawGrid(grid, dRect, graphics);
                if (SelectedGrid != null && grid.MGRTIndex == SelectedGrid.MGRTIndex)
                {
                    selectGridCellList.Add(grid);
                }
            }
            drawSelGrid(dRect, graphics);
            DrawSelectedGridFlyLine(graphics, selectGridCellList);
        }

        /// <summary>
        /// 绘制栅格
        /// </summary>
        /// <param name="grid"></param>
        /// <param name="dRect"></param>
        /// <param name="graphics"></param>
        private void drawGrid(ScanGridInfo grid, DbRect dRect, Graphics graphics)
        {
            if (grid.Within(dRect))
            {
                Color color = GetColor(grid);
                if (color != Color.Empty)
                {
                    DbPoint ltPoint = new DbPoint(grid.TLLongitude, grid.TLLatitude);
                    PointF pointLt;
                    GisAdapter.ToDisplay(ltPoint, out pointLt);
                    DbPoint brPoint = new DbPoint(grid.BRLongitude, grid.BRLatitude);
                    PointF pointBr;
                    GisAdapter.ToDisplay(brPoint, out pointBr);
                    Brush brush = new SolidBrush(color);

                    graphics.FillRectangle(brush, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
                }
            }
        }

        /// <summary>
        /// 绘制选中的栅格
        /// </summary>
        /// <param name="graphics"></param>
        private void drawSelGrid(DbRect dRect, Graphics graphics)
        {
            if (SelectedGrid == null)
            {
                return;
            }
            //绘制选中栅格
            drawGrid(SelectedGrid, dRect, graphics);
            //绘制选中栅格边框
            DbPoint ltPoint = new DbPoint(SelectedGrid.TLLongitude, SelectedGrid.TLLatitude);
            PointF pointLt;
            GisAdapter.ToDisplay(ltPoint, out pointLt);
            DbPoint brPoint = new DbPoint(SelectedGrid.BRLongitude, SelectedGrid.BRLatitude);
            PointF pointBr;
            GisAdapter.ToDisplay(brPoint, out pointBr);
            graphics.DrawRectangle(penSelected, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
        }

        MapSerialInfo serial = null;
        private readonly Color InvalidateColor = Color.Black;
        public void InitColor(RenderingIndex curRangeType)
        {
            SerialInfoName = "";
            this.curRangeType = curRangeType;
            switch (curRangeType)
            {
                case RenderingIndex.R0_RP:
                    SerialInfoName = "LTESCAN_TopN_CELL_Specific_RSRP";
                    break;
                case RenderingIndex.R0_RQ:
                    SerialInfoName = "LTESCAN_TopN_CELL_Specific_RSRQ";
                    break;
                case RenderingIndex.R0_CINR:
                    SerialInfoName = "LTESCAN_TopN_CELL_Specific_RSSINR";
                    break;
                case RenderingIndex.SSS_RP:
                    SerialInfoName = "LTESCAN_TopN_SSS_RP";
                    break;
                case RenderingIndex.SSS_RSSI:
                    SerialInfoName = "LTESCAN_TopN_SSS_RSSI";
                    break;
            }
            serial = DTLayerSerialManager.Instance.GetSerialByName(SerialInfoName);
        }

        public void InitColorBySelectedSerials()
        {
            if (DTLayerSerialManager.Instance.SelectedSerials.Count > 0)
            {
                SerialInfoName = DTLayerSerialManager.Instance.SelectedSerials[0].IDName;
                serial = DTLayerSerialManager.Instance.SelectedSerials[0];
            }
        }

        /// <summary>
        /// 获取栅格颜色
        /// </summary>
        /// <param name="grid"></param>
        /// <returns></returns>
        public Color GetColor(ScanGridInfo grid)
        {
            float value = 0;
            switch (curRangeType)
            {
                case RenderingIndex.R0_RP:
                    value = grid.R0_RP;
                    break;
                case RenderingIndex.R0_RQ:
                    value = grid.R0_RQ;
                    break;
                case RenderingIndex.R0_CINR:
                    value = grid.R0_CINR;
                    break;
                case RenderingIndex.SSS_RP:
                    value = grid.SSS_RP;
                    break;
                case RenderingIndex.SSS_RSSI:
                    value = grid.SSS_RSSI;
                    break;
                default:
                    return InvalidateColor;
            }

            Color? color = serial.ColorDisplayParam.Info.GetColor(value);
            if (color == null)
            {
                return Color.Empty;
            }
            return (Color)color;
        }

        #region 飞线
        private readonly List<LTECell> selectGridCells = new List<LTECell>();
        /// <summary>
        /// 所选栅格通过频点,PCI获取的对应小区合集
        /// </summary>
        public List<LTECell> SelectGridCells
        {
            get { return selectGridCells; }
        }

        private void DrawSelectedGridFlyLine(Graphics graphics, List<ScanGridInfo> gridCells)
        {
            if (SelectedGrid == null)
            {
                return;
            }

            int i = 0;
            PointF p1;
            GisAdapter.ToDisplay(new DbPoint(SelectedGrid.CentLng, SelectedGrid.CentLat), out p1);

            selectGridCells.Clear();
            foreach (ScanGridInfo gridInfo in gridCells)
            {
                LTECell cell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(
                    DateTime.Now, gridInfo.EARFCN, gridInfo.PCI, SelectedGrid.CentLng, SelectedGrid.CentLat);

                if (cell != null)
                {
                    selectGridCells.Add(cell);
                    Pen pen = new Pen(colors[i++ % colors.Length], 2);
                    PointF p2;
                    GisAdapter.ToDisplay(new DbPoint(cell.Longitude, cell.Latitude), out p2);
                    graphics.DrawLine(pen, p1, p2);
                }
            }
        }

        private readonly Color[] colors = new Color[]
        {
            Color.Yellow,
            Color.Green, 
            Color.Purple,
            Color.YellowGreen,
        };
        #endregion

        #region Select
        public override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            Select(((MapForm.MapEventArgs)e).MapOp2);
        }

        /// <summary>
        /// 用于判断是哪个结果窗体的点选
        /// </summary>
        public object CurType { get; set; }
        public void Select(MapOperation2 mop2)
        {
            if (!IsVisible || GridInfos == null || GridInfos.Count <= 0)
            {
                return;
            }
            SelectedGrid = null;
            selectGridCells.Clear();

            //循环所有栅格,如果点击的坐标在某个栅格的范围内则记录为所选点
            getSelectedGrid(mop2);
            SelectedGridChanged?.Invoke(CurType, EventArgs.Empty);
        }

        private void getSelectedGrid(MapOperation2 mop2)
        {
            foreach (ScanGridInfo grid in GridInfos)
            {
                DbRect dRect = new DbRect(grid.TLLongitude, grid.TLLatitude, grid.BRLongitude, grid.BRLatitude);
                if (mop2.CheckCenterInDRect(dRect))
                {
                    SelectedGrid = grid;

                    //找到选择的栅格后,添加该栅格的所有小区
                    LTECell cell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(
                      DateTime.Now, grid.EARFCN, grid.PCI, SelectedGrid.CentLng, SelectedGrid.CentLat);
                    if (cell != null)
                    {
                        selectGridCells.Add(cell);
                    }
                    break;
                }
            }
        }
        #endregion

        #region 导出图层
        internal int MakeShpFile_inject(string filename)
        {
            try
            {
                if (GridInfos == null || GridInfos.Count == 0)
                {
                    return 0;
                }
                Shapefile shpFile = new Shapefile();
                bool result = shpFile.CreateNewWithShapeID("", ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }

                //列
                int idIdx = 0;
                int iGrid = idIdx++;
                int iGridSample = idIdx++;
                int iEARFCN = idIdx++;
                int iPCI = idIdx++;
                int iSSS_RSSI = idIdx++;
                int iSSS_RP = idIdx++;
                int iR0_CINR = idIdx++;
                int iR0_RP = idIdx++;
                int iR0_RQ = idIdx++;
                int iGridSize = idIdx++;
                int iTLLongitude = idIdx++;
                int iTLLatitude = idIdx++;
                int iBRLongitude = idIdx++;
                int iBRLatitude = idIdx;
                ShapeHelper.InsertNewField(shpFile, "栅格号", FieldType.STRING_FIELD, 7, 0, ref iGrid);
                ShapeHelper.InsertNewField(shpFile, "栅格采样点数", FieldType.INTEGER_FIELD, 7, 0, ref iGridSample);
                ShapeHelper.InsertNewField(shpFile, "EARFCN", FieldType.INTEGER_FIELD, 7, 0, ref iEARFCN);
                ShapeHelper.InsertNewField(shpFile, "PCI", FieldType.INTEGER_FIELD, 7, 0, ref iPCI);
                ShapeHelper.InsertNewField(shpFile, "SSS_RSSI", FieldType.DOUBLE_FIELD, 7, 0, ref iSSS_RSSI);
                ShapeHelper.InsertNewField(shpFile, "SSS_RP", FieldType.DOUBLE_FIELD, 7, 0, ref iSSS_RP);
                ShapeHelper.InsertNewField(shpFile, "R0_CINR", FieldType.DOUBLE_FIELD, 7, 0, ref iR0_CINR);
                ShapeHelper.InsertNewField(shpFile, "R0_RP", FieldType.DOUBLE_FIELD, 7, 0, ref iR0_RP);
                ShapeHelper.InsertNewField(shpFile, "R0_RQ", FieldType.DOUBLE_FIELD, 7, 0, ref iR0_RQ);
                ShapeHelper.InsertNewField(shpFile, "栅格经度", FieldType.INTEGER_FIELD, 7, 0, ref iGridSize);
                ShapeHelper.InsertNewField(shpFile, "栅格左上经度", FieldType.DOUBLE_FIELD, 7, 0, ref iTLLongitude);
                ShapeHelper.InsertNewField(shpFile, "栅格左上纬度", FieldType.DOUBLE_FIELD, 7, 0, ref iTLLatitude);
                ShapeHelper.InsertNewField(shpFile, "栅格右下经度", FieldType.DOUBLE_FIELD, 7, 0, ref iBRLongitude);
                ShapeHelper.InsertNewField(shpFile, "栅格右下纬度", FieldType.DOUBLE_FIELD, 7, 0, ref iBRLatitude);

                int shpIdx = 0;
                foreach (ScanGridInfo info in GridInfos)
                {
                    shpFile.EditInsertShape(ShapeHelper.CreateRectShape(info.TLLongitude, info.TLLatitude, info.BRLongitude, info.BRLatitude), ref shpIdx);
                    shpFile.EditCellValue(iGrid, shpIdx, info.MGRTIndex);
                    shpFile.EditCellValue(iGridSample, shpIdx, info.SampleCount);
                    shpFile.EditCellValue(iEARFCN, shpIdx, info.EARFCN);
                    shpFile.EditCellValue(iPCI, shpIdx, info.PCI);
                    shpFile.EditCellValue(iSSS_RSSI, shpIdx, info.SSS_RSSI);
                    shpFile.EditCellValue(iSSS_RP, shpIdx, info.SSS_RP);
                    shpFile.EditCellValue(iR0_CINR, shpIdx, info.R0_CINR);
                    shpFile.EditCellValue(iR0_RP, shpIdx, info.R0_RP);
                    shpFile.EditCellValue(iR0_RQ, shpIdx, info.R0_RQ);
                    shpFile.EditCellValue(iGridSize, shpIdx, info.GridSize);
                    shpFile.EditCellValue(iTLLongitude, shpIdx, info.TLLongitude);
                    shpFile.EditCellValue(iTLLatitude, shpIdx, info.TLLatitude);
                    shpFile.EditCellValue(iBRLongitude, shpIdx, info.BRLongitude);
                    shpFile.EditCellValue(iBRLatitude, shpIdx, info.BRLatitude);
                    shpIdx++;
                }
                shpFile.SaveAs(filename, null);
                shpFile.Close();
                return 1;
            }
            catch (Exception)
            {
                return -1;
            }
        }
        #endregion
    }
}
