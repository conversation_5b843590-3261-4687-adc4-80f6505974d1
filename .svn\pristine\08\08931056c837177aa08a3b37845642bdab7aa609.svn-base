using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTGSMDataDLRateSettingForm : Form
    {
        public ZTGSMDataDLRateSettingForm()
        {
            InitializeComponent();
            rangeSettingDLRate.RangeAll = new Range(0, false, 5000, true);
            rangeSettingDLRate.Range = new Range(0, false, 90, true);
        }

        public Range DataRateRange
        {
            get { return rangeSettingDLRate.Range; }
        }
        public int Radius
        {
            get { return (int)numRadius.Value; }
        }
        public int TestPointCountLimit
        {
            get { return (int)numSampleCountLimit.Value; }
        }

        public bool IsByAppDLRate
        {
            get { return radioButtonAppRate.Checked; }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}