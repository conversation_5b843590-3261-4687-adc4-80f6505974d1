using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.MControls;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.GridQueryHistogram
{
    public partial class AutoSettingForm : Form
    {
        public AutoSettingForm(float min,float max)
        {
            InitializeComponent();
            this.min = min;
            this.max = max;
            for (int i = 0; i < SymbolManager.GetInstance().Count;i++ )
            {
                comboBoxRangeCount.Items.Add(i + 1);
            }
            comboBoxRangeCount.SelectedIndex = 4;
        }

        private float min;
        private float max;
        
        public List<ColorRange> ColorRanges { get; set; }

        private void labelVir_Click(object sender, EventArgs e)
        {
            ColorDialog dlg = new ColorDialog();
            dlg.Color = labelVir.BackColor;
            if (dlg.ShowDialog()==DialogResult.OK)
            {
                labelVir.BackColor = dlg.Color;
            }
        }

        private void labelBegin_Click(object sender, EventArgs e)
        {
            ColorDialog dlg = new ColorDialog();
            dlg.Color = labelBegin.BackColor;
            if (dlg.ShowDialog()==DialogResult.OK)
            {
                labelBegin.BackColor = dlg.Color;
            }
        }

        private void labelEnd_Click(object sender, EventArgs e)
        {
            ColorDialog dlg = new ColorDialog();
            dlg.Color = labelEnd.BackColor;
            if (dlg.ShowDialog()==DialogResult.OK)
            {
                labelEnd.BackColor = dlg.Color;
            }
        }

        private void checkBoxVir_CheckedChanged(object sender, EventArgs e)
        {
            checkBoxVir.Enabled = checkBoxVir.Checked;
        }


        public int RangeCount
        {
            get { return comboBoxRangeCount.SelectedIndex + 1; }
        }

        protected void MakeRange()
        {
            for (int i = 0; i < RangeCount; i++)
            {
                ColorRange colorrange = ColorRanges[i];
                float range = max - min;
                colorrange.minValue = (int)((min + range * i / RangeCount) * 10) / 10.0F;
                colorrange.maxValue = (int)((min + range * (i + 1) / RangeCount) * 10) / 10.0F;
            }
        }

        private void makeRangeColor()
        {
            for (int i = 0; i < RangeCount; i++)
            {
                ColorRange colorrange = ColorRanges[i];
                float percent = RangeCount == 1 ? 0.5F : (float)i / (RangeCount - 1);
                Color beginColor;
                Color endColor;
                if (checkBoxVir.Checked)
                {
                    percent = percent * 2;
                    if (percent < 1)
                    {
                        beginColor = labelBegin.BackColor;
                        endColor = labelVir.BackColor;
                    }
                    else
                    {
                        percent -= 1;
                        beginColor = labelVir.BackColor;
                        endColor = labelEnd.BackColor;
                    }
                }
                else
                {
                    beginColor = labelBegin.BackColor;
                    endColor = labelEnd.BackColor;
                }
                colorrange.color = Color.FromArgb(
                    (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                    (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                    (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                    );
            }
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            List<ColorRange> colorRanges = new List<ColorRange>(RangeCount);
            for (int i = 0; i < RangeCount;i++ )
            {
                colorRanges.Add(new ColorRange());
            }
            ColorRanges = colorRanges;
            MakeRange();
            makeRangeColor();
            DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.Dispose();
        }
    }
}