﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MapWinGIS;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTTDCellEmulateCoverAnaByMR : QueryBase
    {
        public ZTTDCellEmulateCoverAnaByMR(MainModel mainModel)
            : base(mainModel)
        {
        }

        public static int queryidTD { get; set; } = -1;
        public static bool startTD { get; set; }//判断是否进入"TD仿真(MR)"

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "基于MR的TD覆盖仿真"; }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19023, this.Name);
        }

        List<CellEmulateCovResult> ccResultList;
        CfgSettingItem cfgSet = null;
        CellEmulateShowItem showItem = null;
        string strCity = "";
        protected override void query()
        {
            initData();//初始化数据
            cfgSet = new CfgSettingItem();
            showItem = new CellEmulateShowItem();
            //用于查询条件传值，需要赋值
            if (!fillValue(cfgSet, showItem))
            {
                return;
            }

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";

                strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                MainModel.ClearDTData();
                SelectRegion();
                WaitBox.Show("开始绘制小区仿真点...", queryInThread, clientProxy);

                CalculateResult();
                fireShowResultForm();
                int queryid = (int)(JavaDate.GetMilliseconds(DateTime.Now) / 1000L);
                queryidTD = queryid;
                MainModel.MainForm.FireCellEmulateMRQueried(queryid);
            }
            catch
            {
                clientProxy.Close();
            }
        }

        private bool fillValue(CfgSettingItem csItem,CellEmulateShowItem item)
        {
            CfgSettingDlg dlg = CfgSettingDlg.GetInstance();
            if (dlg.ShowDialog()==DialogResult.OK)
            {
                if (dlg.BeginTime>dlg.EndTime)
                {
                    MessageBox.Show("开始时间不能大于结束时间！");
                    return false;
                }
                csItem.istime = dlg.BeginTime;
                csItem.ietime = dlg.EndTime;
                csItem.iLongitude = dlg.gridSizeLon;
                csItem.iLatitude = dlg.gridSizeLat;
                csItem.iRxlev = dlg.Rxlev;
                csItem.isTaDownWard = dlg.IsTaDownWard;
                csItem.iTestValue = dlg.TestValue;
                csItem.iRepeaterStatus = dlg.RepeaterBts;

                item.visibleRxlevTopN = dlg.VisibleRxlevTopN;
                item.gridSpanLon = dlg.gridSizeLon * 0.0000001;
                item.gridSpanLat = dlg.gridSizeLat * 0.0000001;
                return true;
            }
            return false;
        }

        MapWinGIS.Shape gmt;
        Dictionary<string, MapOperation2> regionMopDic = null;
        Dictionary<string, List<GridLongLat>> gridLongLatDic = null;
        Dictionary<string, List<GridLongLat>> gridNonContainLongLatDic = null;
        Dictionary<string, RxlevGridNum> rxlevGridNumDic = null;
        RxlevGridLongLat rxlevGridLongLat = null;

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void initData()
        {
            gmt = null;
            regionMopDic = null;
            gridLongLatDic = new Dictionary<string, List<GridLongLat>>();
            gridNonContainLongLatDic = new Dictionary<string, List<GridLongLat>>();
            rxlevGridNumDic = new Dictionary<string, RxlevGridNum>();
            rxlevGridLongLat = new RxlevGridLongLat();
            startTD = true;
        }

        private void SelectRegion()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            gmt = MainModel.SearchGeometrys.Region;
            regionMopDic = new Dictionary<string, MapOperation2>();

            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
            }
        }

        private void queryInThread(object o)
        {
            string strDesc = "";
            try
            {
                List<TDCell> allTdCell = CellManager.GetInstance().GetCurrentTDCells();
                Dictionary<CellLaiKey, RxlevCoverDist> cellDistDic = anaCellRxlevTa(cfgSet, strCity);
                Dictionary<GridLongLat, int> gridRscpDic = getTestGridRscp(cfgSet, strCity);

                Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic = new Dictionary<string, Dictionary<GridLongLat, string>>();
                getRegionGrid(ref rectAllDic, ref strDesc);
                List<RxlevLongLat> cellEmulationPointsList = anaCoverAreaByCell(allTdCell, cellDistDic, ref strDesc);
                allTdCell.Clear();
                cellDistDic.Clear();
                Dictionary<GridLongLat, RxlevShape> winShapeDic = drawCoverAreaByCell(cellEmulationPointsList, ref strDesc);
                cellEmulationPointsList.Clear();
                rejectCoverGrid(winShapeDic, rectAllDic, gridRscpDic, ref strDesc, cfgSet);
            }
            catch(Exception exp)
            {
                log.Error("查询出错，步骤为：" + strDesc);
                MessageBox.Show("发生错误：" + exp.Message, "信息提示", MessageBoxButtons.OK);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        /// <summary>
        /// 获取区域内栅格
        /// </summary>
        private void getRegionGrid(ref Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic, ref string strDesc)
        {
            strDesc = "获取区域内栅格";

            int iMinLongitude = (int)(MainModel.SearchGeometrys.Region.Extents.xMin * 10000000) / cfgSet.iLongitude * cfgSet.iLongitude - cfgSet.iLongitude;
            int iMaxLongitude = (int)(MainModel.SearchGeometrys.Region.Extents.xMax * 10000000) / cfgSet.iLongitude * cfgSet.iLongitude + cfgSet.iLongitude;
            int iMinLatitude = (int)(MainModel.SearchGeometrys.Region.Extents.yMin * 10000000) / cfgSet.iLatitude * cfgSet.iLatitude - cfgSet.iLatitude;
            int iMaxLatitude = (int)(MainModel.SearchGeometrys.Region.Extents.yMax * 10000000) / cfgSet.iLatitude * cfgSet.iLatitude + cfgSet.iLatitude;

            while (iMinLongitude <= iMaxLongitude)
            {
                int iTmpLat = iMinLatitude;
                while (iTmpLat <= iMaxLatitude)
                {
                    DbRect rect = new DbRect();
                    rect.x1 = (float)(iMinLongitude) / 10000000;
                    rect.y1 = (float)(iTmpLat - cfgSet.iLatitude) / 10000000;
                    rect.x2 = (float)(iMinLongitude + cfgSet.iLongitude) / 10000000;
                    rect.y2 = (float)iTmpLat / 10000000;
                    GridLongLat gll = GridLongLat.cvtLongLat(rect);
                    foreach (string strRegionName in regionMopDic.Keys)
                    {
                        MapOperation2 mapOper = regionMopDic[strRegionName];
                        RegionExtern regExt = mapOper.GetRegion();//经纬度过滤，提高效率
                        if (rect.x2 < regExt.Bounds.x1 || rect.x1 > regExt.Bounds.x2 ||
                            rect.y2 < regExt.Bounds.y1 || rect.y1 > regExt.Bounds.y2)
                            continue;

                        if (mapOper.CheckRectIntersectWithRegion(rect))
                        {
                            addRectAllDic(rectAllDic, gll, strRegionName);
                            break;
                        }
                    }
                    iTmpLat += cfgSet.iLatitude;
                }
                iMinLongitude += cfgSet.iLongitude;
            }
            WaitBox.ProgressPercent = 50;
        }

        private void addRectAllDic(Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic, GridLongLat gll, string strRegionName)
        {
            if (gridLongLatDic.ContainsKey(strRegionName))
            {
                List<GridLongLat> rectList = gridLongLatDic[strRegionName];
                rectList.Add(gll);

                Dictionary<GridLongLat, string> tmpDic = rectAllDic[strRegionName];
                try
                {
                    tmpDic.Add(gll, strRegionName);
                }
                catch (Exception ex)
                {
                    log.Error(ex.StackTrace);
                }
            }
            else
            {
                List<GridLongLat> rectList = new List<GridLongLat>();
                rectList.Add(gll);
                gridLongLatDic.Add(strRegionName, rectList);

                Dictionary<GridLongLat, string> tmpDic = new Dictionary<GridLongLat, string>();
                tmpDic.Add(gll, strRegionName);
                rectAllDic.Add(strRegionName, tmpDic);
            }
        }

        /// <summary>
        /// 每个小区的覆盖区域
        /// </summary>
        private List<RxlevLongLat> anaCoverAreaByCell(List<TDCell> allTdCell, Dictionary<CellLaiKey, RxlevCoverDist> cellDistDic, ref string strDesc)
        {
            strDesc = "每个小区的覆盖区域";
            WaitBox.Text = "[" + strCity + "] 计算每个小区的覆盖区域...";

            double covAddDistance = 5500;   //最远覆盖距离：用于判断区域以外参与计算的小区
            covAddDistance = covAddDistance * 0.00001;
            double xMin = MainModel.SearchGeometrys.Region.Extents.xMin - covAddDistance;
            double xMax = MainModel.SearchGeometrys.Region.Extents.xMax + covAddDistance;
            double yMin = MainModel.SearchGeometrys.Region.Extents.yMin - covAddDistance;
            double yMax = MainModel.SearchGeometrys.Region.Extents.yMax + covAddDistance;

            int iTotalNum = allTdCell.Count;
            int iCurNum = 0;
            List<RxlevLongLat> cellEmulationPointsList = new List<RxlevLongLat>();

            foreach (TDCell cell in allTdCell)
            {
                iCurNum++;
                WaitBox.ProgressPercent = (int)(100 * ((float)(iCurNum) / iTotalNum));
                if (cell.Type != TDNodeBType.Outdoor)
                {
                    continue;
                }
                if (cell.Longitude > xMin && cell.Longitude < xMax && cell.Latitude > yMin && cell.Latitude < yMax)
                {
                    CellLaiKey clKey = new CellLaiKey();
                    clKey.ILac = cell.LAC;
                    clKey.ICi = cell.CI;
                    if (!cellDistDic.ContainsKey(clKey))
                        continue;
                    RxlevCoverDist rxlevCoverDist = cellDistDic[clKey];
                    RxlevLongLat rxlevLongLat = new RxlevLongLat();
                    rxlevLongLat.fillCellLongLat(cell, rxlevCoverDist);
                    cellEmulationPointsList.Add(rxlevLongLat);
                }
            }
            WaitBox.ProgressPercent = 70;
            return cellEmulationPointsList;
        }

        /// <summary>
        /// 绘制每个小区的覆盖区域
        /// </summary>
        private Dictionary<GridLongLat, RxlevShape> drawCoverAreaByCell(List<RxlevLongLat> cellEmulationPointsList, ref string strDesc)
        {
            strDesc = "绘制每个小区的覆盖区域";
            WaitBox.Text = "[" + strCity + "] 分析栅格覆盖区域...";

            //将小区覆盖区域绘制成图形
            Dictionary<GridLongLat, RxlevShape> winShapeDic = new Dictionary<GridLongLat, RxlevShape>();
            foreach (RxlevLongLat longLatList in cellEmulationPointsList)
            {
                if (longLatList.Rxlev70LongLatList.Count + longLatList.Rxlev80LongLatList.Count +
                    longLatList.Rxlev85LongLatList.Count + longLatList.Rxlev90LongLatList.Count +
                    longLatList.Rxlev94LongLatList.Count + longLatList.Rxlev95LongLatList.Count == 0)
                    continue;
                RxlevShape.drawShape(longLatList,ref winShapeDic);
            }
            return winShapeDic;
        }

        /// <summary>
        /// 剔除已覆盖栅格区域
        /// </summary>
        private void rejectCoverGrid(Dictionary<GridLongLat, RxlevShape> winShapeDic, Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic,
                                     Dictionary<GridLongLat, int> gridRscpDic, ref string strDesc, CfgSettingItem cfgSet)
        {
            strDesc = "剔除已覆盖栅格区域";
            WaitBox.Text = "[" + strCity + "] 剔除已覆盖栅格区域...";
            Dictionary<GridLongLat, string> gridStatDic = new Dictionary<GridLongLat, string>();

            if (cfgSet.iRxlev <= -70)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 70);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 70);
            }
            if (cfgSet.iRxlev <= -80)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 80);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 80);
            }
            if (cfgSet.iRxlev <= -85)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 85);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 85);
            }
            if (cfgSet.iRxlev <= -90)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 90);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 90);
            }
            if (cfgSet.iRxlev <= -94)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 94);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 94);
            }
            if (cfgSet.iRxlev <= -95)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 95);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 95);
            }
            winShapeDic.Clear();

            addGridLongLat(gridStatDic);

            MainModel.RxlevGridLongLat = rxlevGridLongLat;
            MainModel.cellEmulateShowItem = showItem;
            WaitBox.ProgressPercent = 90;
        }

        private void addGridLongLat(Dictionary<GridLongLat, string> gridStatDic)
        {
            foreach (string strRegion in gridLongLatDic.Keys)
            {
                foreach (GridLongLat gll in gridLongLatDic[strRegion])
                {
                    if (gridStatDic.ContainsKey(gll))
                        continue;

                    addRxlevGridNum(strRegion, -999);

                    if (gridNonContainLongLatDic.ContainsKey(strRegion))
                    {
                        gridNonContainLongLatDic[strRegion].Add(gll);
                    }
                    else
                    {
                        List<GridLongLat> rectList = new List<GridLongLat>();
                        rectList.Add(gll);
                        gridNonContainLongLatDic.Add(strRegion, rectList);
                    }
                    rxlevGridLongLat.RxlevNonCoverGllList.Add(gll);
                }
            }
        }

        /// <summary>
        /// 按测试数据剔除删格
        /// </summary>
        private void rejectCoverGridByTestRxlev(Dictionary<GridLongLat, int> gridRscpDic, ref Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic,
                                                ref Dictionary<GridLongLat, string> gridStatDic, int iRxlev)
        {
            foreach (GridLongLat gll in gridRscpDic.Keys)
            {
                int iGirdRxlev = gridRscpDic[gll];
                if (iGirdRxlev < cfgSet.iRxlev)
                {
                    continue;
                }
                string strRegion = "";
                foreach (string tmpRegion in rectAllDic.Keys)
                {
                    if (rectAllDic[tmpRegion].ContainsKey(gll))
                    {
                        strRegion = tmpRegion;
                        break;
                    }
                }
                if (strRegion != "")
                {
                    dealRegionGrid(gridStatDic, iRxlev, gll, iGirdRxlev, strRegion);
                }
            }
            rectAllDic = removeGridLongLat(rectAllDic, gridStatDic);
        }

        private void dealRegionGrid(Dictionary<GridLongLat, string> gridStatDic, int iRxlev, GridLongLat gll, int iGirdRxlev, string strRegion)
        {
            if (judgeValidGrid(iRxlev, iGirdRxlev, 50, -70))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev70GllList.Add(gll);
                addRxlevGridNum(strRegion, 70);
            }
            if (judgeValidGrid(iRxlev, iGirdRxlev, -70, -80))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev80GllList.Add(gll);
                addRxlevGridNum(strRegion, 80);
            }
            if (judgeValidGrid(iRxlev, iGirdRxlev, -80, -85))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev85GllList.Add(gll);
                addRxlevGridNum(strRegion, 85);
            }
            if (judgeValidGrid(iRxlev, iGirdRxlev, -85, -90))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev90GllList.Add(gll);
                addRxlevGridNum(strRegion, 90);
            }
            if (judgeValidGrid(iRxlev, iGirdRxlev, -90, -94))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev94GllList.Add(gll);
                addRxlevGridNum(strRegion, 94);
            }
            if (judgeValidGrid(iRxlev, iGirdRxlev, -94, -95))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev95GllList.Add(gll);
                addRxlevGridNum(strRegion, 95);
            }
        }

        private bool judgeValidGrid(int iRxlev, int iGirdRxlev, int iGirdRxlevMax, int iGirdRxlevMin)
        {
            return iRxlev == -iGirdRxlevMax && iGirdRxlev >= iGirdRxlevMin && iGirdRxlev < iGirdRxlevMax;
        }

        /// <summary>
        /// 累加每级电平栅格数
        /// </summary>
        private void addRxlevGridNum(string strRegion, int iRxlev)
        {
            if (!rxlevGridNumDic.ContainsKey(strRegion))
            {
                RxlevGridNum rxlGrid = new RxlevGridNum();
                rxlevGridNumDic.Add(strRegion, rxlGrid);
            }
            if (iRxlev == 70)
            {
                rxlevGridNumDic[strRegion].iRxlev70Num += 1;
            }
            else if (iRxlev == 80)
            {
                rxlevGridNumDic[strRegion].iRxlev80Num += 1;
            }
            else if (iRxlev == 85)
            {
                rxlevGridNumDic[strRegion].iRxlev85Num += 1;
            }
            else if (iRxlev == 90)
            {
                rxlevGridNumDic[strRegion].iRxlev90Num += 1;
            }
            else if (iRxlev == 94)
            {
                rxlevGridNumDic[strRegion].iRxlev94Num += 1;
            }
            else if (iRxlev == 95)
            {
                rxlevGridNumDic[strRegion].iRxlev95Num += 1;
            }
            else if (iRxlev == -999)//无覆盖
            {
                rxlevGridNumDic[strRegion].iRxlevNonCoverNum += 1;
            }
        }

        /// <summary>
        /// 按电平剔除栅格
        /// </summary>
        private void rejectCoverGridByRxlev(Dictionary<GridLongLat, RxlevShape> winShapeDic, ref Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic,
                                            ref Dictionary<GridLongLat, string> gridStatDic, int iRxlev)
        {
            int wsCount = winShapeDic.Count;
            int iCurNum2 = 0;
            WaitBox.Text = string.Format("[{0}] 剔除场强-{1}dBm已覆盖栅格区域...", strCity, iRxlev);
            foreach (GridLongLat extGll in winShapeDic.Keys)
            {
                iCurNum2++;
                WaitBox.ProgressPercent = (int)(100 * ((float)(iCurNum2) / wsCount));
                Dictionary<GridLongLat, string> rectSubDic = getSubGridLongLat(rectAllDic, extGll);
                Dictionary<GridLongLat, string> gridStatSubDic = new Dictionary<GridLongLat, string>();
                RxlevShape rxlevWinShape = winShapeDic[extGll];

                if (iRxlev == 70)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev70ShapeList, 70, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }
                else if (iRxlev == 80)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev80ShapeList, 80, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }
                else if (iRxlev == 85)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev85ShapeList, 85, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }
                else if (iRxlev == 90)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev90ShapeList, 90, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }
                else if (iRxlev == 94)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev94ShapeList, 94, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }
                else if (iRxlev == 95)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev95ShapeList, 95, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }

                rectAllDic = removeGridLongLat(rectAllDic, gridStatSubDic);
            }
        }


        /// <summary>
        /// 按覆盖强度剔除删格
        /// </summary>
        private void rejectCoverGridByRxlev(List<MapWinGIS.Shape> winShapeList,int iRxlev, ref Dictionary<GridLongLat, string> rectSubDic, 
                                            ref Dictionary<GridLongLat, string> gridStatSubDic,ref Dictionary<GridLongLat, string> gridStatDic)
        {
            foreach (MapWinGIS.Shape winShape in winShapeList)
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(winShape);
                DbRect tmpRect = MapOperation.GetShapeBounds(winShape);

                foreach (GridLongLat gll in rectSubDic.Keys)
                {
                    if (!gll.compareValue(tmpRect))
                        continue;

                    if (gridStatDic.ContainsKey(gll))
                        continue;

                    setGridStat(iRxlev, rectSubDic, gridStatSubDic, gridStatDic, mapOp2, gll);
                }
                rectSubDic = removeGridLongLat(rectSubDic, gridStatSubDic);
            }
        }

        private void setGridStat(int iRxlev, Dictionary<GridLongLat, string> rectSubDic, Dictionary<GridLongLat, string> gridStatSubDic, Dictionary<GridLongLat, string> gridStatDic, MapOperation2 mapOp2, GridLongLat gll)
        {
            string strRegion = rectSubDic[gll];
            DbRect rect = GridLongLat.cvtRect(gll);
            if (mapOp2.CheckRectIntersectWithRegion(rect)
                && !gridStatDic.ContainsKey(gll))
            {
                gridStatDic.Add(gll, strRegion);
                gridStatSubDic.Add(gll, strRegion);
                if (iRxlev == 70)
                {
                    rxlevGridLongLat.Rxlev70GllList.Add(gll);
                    addRxlevGridNum(strRegion, 70);
                }
                else if (iRxlev == 80)
                {
                    rxlevGridLongLat.Rxlev80GllList.Add(gll);
                    addRxlevGridNum(strRegion, 80);
                }
                else if (iRxlev == 85)
                {
                    rxlevGridLongLat.Rxlev85GllList.Add(gll);
                    addRxlevGridNum(strRegion, 85);
                }
                else if (iRxlev == 90)
                {
                    rxlevGridLongLat.Rxlev90GllList.Add(gll);
                    addRxlevGridNum(strRegion, 90);
                }
                else if (iRxlev == 94)
                {
                    rxlevGridLongLat.Rxlev94GllList.Add(gll);
                    addRxlevGridNum(strRegion, 94);
                }
                else if (iRxlev == 95)
                {
                    rxlevGridLongLat.Rxlev95GllList.Add(gll);
                    addRxlevGridNum(strRegion, 95);
                }
            }
        }

        /// <summary>
        /// 移除重复项
        /// </summary>
        private Dictionary<GridLongLat, string> removeGridLongLat(Dictionary<GridLongLat, string> rectAllDic, Dictionary<GridLongLat, string> gridStatDic)
        {
            foreach (GridLongLat gll in gridStatDic.Keys)
            {
                if (rectAllDic.ContainsKey(gll))
                    rectAllDic.Remove(gll);
            }
            return rectAllDic;
        }

        /// <summary>
        /// 移除重复项
        /// </summary>
        private Dictionary<string, Dictionary<GridLongLat, string>> removeGridLongLat(Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic, Dictionary<GridLongLat, string> gridStatDic)
        {
            foreach (GridLongLat gll in gridStatDic.Keys)
            {
                foreach (string strRegion in rectAllDic.Keys)
                {
                    if (rectAllDic[strRegion].ContainsKey(gll))
                        rectAllDic[strRegion].Remove(gll);
                }
            }
            return rectAllDic;
        }

        /**
        /// <summary>
        /// 获取小区覆盖区域子集
        /// </summary>
        private Dictionary<GridLongLat, string> getSubGridLongLat(Dictionary<GridLongLat, string> rectAllDic,GridLongLat extGll)
        {
            Dictionary<GridLongLat, string> rectSubDic = new Dictionary<GridLongLat, string>();
            foreach (GridLongLat gll in rectAllDic.Keys)
            {
                if (gll.fbrlongitude < extGll.fltlongitude || gll.fltlongitude > extGll.fbrlongitude ||
                    gll.fltlatitude < extGll.fbrlatitude || gll.fbrlatitude > extGll.fltlatitude)
                    continue;

                rectSubDic.Add(gll, rectAllDic[gll]);
            }
            return rectSubDic;
        }
        */

        /// <summary>
        /// 获取小区覆盖区域子集
        /// </summary>
        private Dictionary<GridLongLat, string> getSubGridLongLat(Dictionary<string,Dictionary<GridLongLat, string>> rectAllDic, GridLongLat extGll)
        {
            Dictionary<GridLongLat, string> rectSubDic = new Dictionary<GridLongLat, string>();
            foreach (string strRegion in rectAllDic.Keys)
            {
                foreach (GridLongLat gll in rectAllDic[strRegion].Keys)
                {
                    if (gll.fbrlongitude < extGll.fltlongitude || gll.fltlongitude > extGll.fbrlongitude ||
                        gll.fltlatitude < extGll.fbrlatitude || gll.fbrlatitude > extGll.fltlatitude)
                        continue;

                    if (!rectSubDic.ContainsKey(gll))
                        rectSubDic.Add(gll, strRegion);
                }
            }
            return rectSubDic;
        }

        private void fireShowResultForm()
        {
            GSMCellEmulateCovMRForm frm = null;
            frm = MainModel.GetObjectFromBlackboard(typeof(GSMCellEmulateCovMRForm).FullName) as GSMCellEmulateCovMRForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new GSMCellEmulateCovMRForm(MainModel);
            }
            frm.FillData(ccResultList);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
        }

        /// <summary>
        /// 计算最终统计结果
        /// </summary>
        private void CalculateResult()
        {
            ccResultList = new List<CellEmulateCovResult>();
            List<ResvRegion> resvRegions = MainModel.GetInstance().SearchGeometrys.SelectedResvRegions;//预存区域
            MapWinGIS.Shape curRegion = MainModel.SearchGeometrys.Region;

            //float regionSquare = ((float)((cfgSet.iLongitude / 100) * (cfgSet.iLongitude / 100))) / (1000 * 1000);

            foreach (string strRegion in rxlevGridNumDic.Keys)
            {
                int iTotalGrid = rxlevGridNumDic[strRegion].iRxlev70Num + rxlevGridNumDic[strRegion].iRxlev80Num + rxlevGridNumDic[strRegion].iRxlev85Num
                    + rxlevGridNumDic[strRegion].iRxlev90Num + rxlevGridNumDic[strRegion].iRxlev94Num + rxlevGridNumDic[strRegion].iRxlev95Num + rxlevGridNumDic[strRegion].iRxlevNonCoverNum;
                //float iTotalSquare = iTotalGrid * regionSquare;

                int iCoverGrid70 = rxlevGridNumDic[strRegion].iRxlev70Num;
                //float iCoverSquare70 = rxlevGridNumDic[strRegion].iRxlev70Num * regionSquare;
                string fCoverRate70 = string.Format("{0}%", (100 * ((float)iCoverGrid70) / iTotalGrid).ToString("0.00"));

                int iCoverGrid80 = rxlevGridNumDic[strRegion].iRxlev80Num + iCoverGrid70;
                //float iCoverSquare80 = rxlevGridNumDic[strRegion].iRxlev80Num * regionSquare + iCoverSquare70;
                string fCoverRate80 = string.Format("{0}%", (100 * ((float)iCoverGrid80) / iTotalGrid).ToString("0.00"));

                int iCoverGrid85 = rxlevGridNumDic[strRegion].iRxlev85Num + iCoverGrid80;
                //float iCoverSquare85 = rxlevGridNumDic[strRegion].iRxlev70Num * regionSquare + iCoverSquare80;
                string fCoverRate85 = string.Format("{0}%", (100 * ((float)iCoverGrid85) / iTotalGrid).ToString("0.00"));

                int iCoverGrid90 = rxlevGridNumDic[strRegion].iRxlev90Num + iCoverGrid85;
                //float iCoverSquare90 = rxlevGridNumDic[strRegion].iRxlev90Num * regionSquare + iCoverSquare85;
                string fCoverRate90 = string.Format("{0}%", (100 * ((float)iCoverGrid90) / iTotalGrid).ToString("0.00"));

                int iCoverGrid94 = rxlevGridNumDic[strRegion].iRxlev94Num + iCoverGrid90;
                //float iCoverSquare94 = rxlevGridNumDic[strRegion].iRxlev94Num * regionSquare + iCoverSquare90;
                string fCoverRate94 = string.Format("{0}%", (100 * ((float)iCoverGrid94) / iTotalGrid).ToString("0.00"));

                int iCoverGrid95 = rxlevGridNumDic[strRegion].iRxlev95Num + iCoverGrid94;
                //float iCoverSquare95 = rxlevGridNumDic[strRegion].iRxlev95Num * regionSquare + iCoverSquare94;
                string fCoverRate95 = string.Format("{0}%", (100 * ((float)iCoverGrid95) / iTotalGrid).ToString("0.00"));

                CellEmulateCovResult ret = new CellEmulateCovResult();
                ret.region = strRegion;
                ret.totalGrid = iTotalGrid;

                ret.coverGrid70 = iCoverGrid70;
                ret.coverGrid80 = iCoverGrid80;
                ret.coverGrid85 = iCoverGrid85;
                ret.coverGrid90 = iCoverGrid90;
                ret.coverGrid94 = iCoverGrid94;
                ret.coverGrid95 = iCoverGrid95;

                ret.coverRate70 = fCoverRate70;
                ret.coverRate80 = fCoverRate80;
                ret.coverRate85 = fCoverRate85;
                ret.coverRate90 = fCoverRate90;
                ret.coverRate94 = fCoverRate94;
                ret.coverRate95 = fCoverRate95;

                if (resvRegions != null && resvRegions.Count > 0)
                {
                    foreach (ResvRegion region in resvRegions)
                    {
                        double area = RegionAreaCalculator.CalculateArea(region.Shape);
                        if (region.RegionName == strRegion)
                        {
                            ret.totalSquare = area.ToString("0.00");
                            //ret.coverSquare = (area * ((float)iCoverGrid) / iTotalGrid).ToString("0.00");
                            ret.coverSquare70 = (area * ((float)iCoverGrid70) / iTotalGrid).ToString("0.00");
                            ret.coverSquare80 = (area * ((float)iCoverGrid80) / iTotalGrid).ToString("0.00");
                            ret.coverSquare85 = (area * ((float)iCoverGrid85) / iTotalGrid).ToString("0.00");
                            ret.coverSquare90 = (area * ((float)iCoverGrid90) / iTotalGrid).ToString("0.00");
                            ret.coverSquare94 = (area * ((float)iCoverGrid94) / iTotalGrid).ToString("0.00");
                            ret.coverSquare95 = (area * ((float)iCoverGrid95) / iTotalGrid).ToString("0.00");
                        }
                    }
                }
                else if (curRegion != null)
                {
                    double area = RegionAreaCalculator.CalculateArea(MainModel.GetInstance().SearchGeometrys.Region);
                    ret.totalSquare = area.ToString("0.00");
                    //ret.coverSquare = (area * ((float)iCoverGrid) / iTotalGrid).ToString("0.00");
                    ret.coverSquare70 = (area * ((float)iCoverGrid70) / iTotalGrid).ToString("0.00");
                    ret.coverSquare80 = (area * ((float)iCoverGrid80) / iTotalGrid).ToString("0.00");
                    ret.coverSquare85 = (area * ((float)iCoverGrid85) / iTotalGrid).ToString("0.00");
                    ret.coverSquare90 = (area * ((float)iCoverGrid90) / iTotalGrid).ToString("0.00");
                    ret.coverSquare94 = (area * ((float)iCoverGrid94) / iTotalGrid).ToString("0.00");
                    ret.coverSquare95 = (area * ((float)iCoverGrid95) / iTotalGrid).ToString("0.00");
                }

                ccResultList.Add(ret);
            }
        }

        /**
        /// <summary>
        /// 导出EXCEL
        /// </summary>
        private void OutputExcel(CfgSettingItem cfgSet)
        {
            float regionSquare = ((float)((cfgSet.iLongitude / 100) * (cfgSet.iLongitude / 100))) / (1000 * 1000);
            List<NPOIRow> datas = new List<NPOIRow>();
            List<object> cols = new List<object>();
            NPOIRow nr1 = new NPOIRow();

            #region EXCEL-SHEET1列表构造
            cols.Add("区域名称");
            cols.Add("总栅格数");
            cols.Add("覆盖栅格数");
            cols.Add("总面积(平方公里)");
            cols.Add("覆盖面积(平方公里)");
            cols.Add("小区覆盖率(%)");
            nr1.cellValues = cols;
            datas.Add(nr1);
            #endregion

            foreach (string strRegion in gridLongLatDic.Keys)
            {
                int iTotalGrid = gridLongLatDic[strRegion].Count;
                float iTotalSquare = iTotalGrid * regionSquare;

                int iCoverGrid = iTotalGrid;
                if (gridNonContainLongLatDic.ContainsKey(strRegion))
                    iCoverGrid = iTotalGrid - gridNonContainLongLatDic[strRegion].Count;
                float iCoverSquare = iCoverGrid * regionSquare;
                string fCoverRate = string.Format("{0}%", (100 * ((float)iCoverGrid) / iTotalGrid).ToString("0.00"));

                NPOIRow nr2 = new NPOIRow();
                List<object> cols2 = new List<object>();
                cols2.Add(strRegion);
                cols2.Add(iTotalGrid);
                cols2.Add(iCoverGrid);
                cols2.Add(iTotalSquare.ToString("0.00"));
                cols2.Add(iCoverSquare.ToString("0.00"));
                cols2.Add(fCoverRate);
                nr2.cellValues = cols2;
                datas.Add(nr2);
            }

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("小区覆盖仿真统计");
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);

            WaitBox.Close();
        }
        */

        /// <summary>
        /// 通过数据库获取各类表信息
        /// </summary>
        private Dictionary<CellLaiKey, RxlevCoverDist> anaCellRxlevTa(CfgSettingItem cfgSet, string strCity)
        {
            //获取小区MR数据
            WaitBox.Text = "[" + strCity + "] 获取小区MR数据...";
            WaitBox.ProgressPercent = 10;
            DiySqlGetUtranCellRxlevTa sqlGetCellRxlevTa = new DiySqlGetUtranCellRxlevTa(MainModel);
            sqlGetCellRxlevTa.SetQueryCondition(condition);
            sqlGetCellRxlevTa.setParam(cfgSet, strCity);
            sqlGetCellRxlevTa.Query();
            List<UtranCellRscpTaItem> utranCellRscpTaList = sqlGetCellRxlevTa.cellRxlevTaList;
            WaitBox.ProgressPercent = 30;
            
            //计算每个小区的覆盖距离
            Dictionary<CellLaiKey, RxlevCoverDist> distDic = calcCellCoverDistance(utranCellRscpTaList, cfgSet);
            return distDic;
        }

        /// <summary>
        /// 通过数据库获取各类表信息
        /// </summary>
        private Dictionary<GridLongLat, int> getTestGridRscp(CfgSettingItem cfgSet, string strCity)
        {
            WaitBox.Text = "[" + strCity + "] 获取测试栅格数据...";
            WaitBox.ProgressPercent = 30;
            DiySqlGetTdGridRscp sqlGetTdGridRscp = new DiySqlGetTdGridRscp(MainModel);
            sqlGetTdGridRscp.SetQueryCondition(condition);
            sqlGetTdGridRscp.csItem = cfgSet;
            sqlGetTdGridRscp.setParam(strCity);
            sqlGetTdGridRscp.Query();
            Dictionary<GridLongLat, int> gridRscpDic = sqlGetTdGridRscp.gridRscpDic;
            WaitBox.ProgressPercent = 35;
            return gridRscpDic;
        }

        /// <summary>
        /// 生成一个矩形shape
        /// </summary>
        public static MapWinGIS.Shape CreateRectShape(List<LongLat> longLatList, ref GridLongLat gll)
        {
            //左上角开始，顺时针InsertPoint
            MapWinGIS.Shape shp = new MapWinGIS.Shape();
            shp.Create(ShpfileType.SHP_POLYGON);
            int idx = 0;
            foreach (LongLat ll in longLatList)
            {
                MapWinGIS.Point pnt = new MapWinGIS.Point();
                pnt.x = ll.fLongitude;//左上角
                pnt.y = ll.fLatitude;
                shp.InsertPoint(pnt, ref idx);
                idx++;

                gll.updateValue(ll);
            }
            return shp;
        }

        /// <summary>
        /// 小区覆盖仿真
        /// </summary>
        public static List<LongLat> getCellEmulateCover(LongLat btsLongLat, int iangle_dir, int coverDistance)
        {
            List<LongLat> cellEmulateList = new List<LongLat>();

            int sDir = iangle_dir - 60;
            int eDir = iangle_dir + 60;
            for (int i = sDir; i <= eDir; i += 5)
            {
                LongLat tLongLat = calcPointX(i, coverDistance, btsLongLat);
                cellEmulateList.Add(tLongLat);
            }
            return cellEmulateList;
        }

        /// <summary>
        /// 计算每个小区的覆盖距离
        /// </summary>
        private Dictionary<CellLaiKey, RxlevCoverDist> calcCellCoverDistance(List<UtranCellRscpTaItem> cellRscpTaList, CfgSettingItem cfgSet)
        {
            Dictionary<CellLaiKey, RxlevCoverDist> cellDistDic = new Dictionary<CellLaiKey, RxlevCoverDist>();
            foreach (UtranCellRscpTaItem crtItem in cellRscpTaList)
            {
                CellLaiKey clKey = new CellLaiKey();
                clKey.ILac = crtItem.lac;
                clKey.ICi = crtItem.ci;
                RxlevCoverDist rcDist = new RxlevCoverDist();
                float fCoverRate = 0;

                if (cfgSet.iRxlev <= -70)
                {
                    fCoverRate = ((float)crtItem.rscp_ge_70) / crtItem.rscp_total;
                    rcDist.Rxlev70CoverDist = calcTaDistNew(crtItem, cfgSet, fCoverRate);
                }
                if (cfgSet.iRxlev <= -80)
                {
                    fCoverRate = ((float)crtItem.rscp_ge_80) / crtItem.rscp_total;
                    rcDist.Rxlev80CoverDist = calcTaDistNew(crtItem, cfgSet, fCoverRate);
                }
                if (cfgSet.iRxlev <= -85)
                {
                    fCoverRate = ((float)crtItem.rscp_ge_85) / crtItem.rscp_total;
                    rcDist.Rxlev85CoverDist = calcTaDistNew(crtItem, cfgSet, fCoverRate);
                }
                if (cfgSet.iRxlev <= -90)
                {
                    fCoverRate = ((float)crtItem.rscp_ge_90) / crtItem.rscp_total;
                    rcDist.Rxlev90CoverDist = calcTaDistNew(crtItem, cfgSet, fCoverRate);
                }
                if (cfgSet.iRxlev <= -94)
                {
                    fCoverRate = ((float)crtItem.rscp_ge_94) / crtItem.rscp_total;
                    rcDist.Rxlev94CoverDist = calcTaDistNew(crtItem, cfgSet, fCoverRate);
                }
                if (cfgSet.iRxlev <= -95)
                {
                    fCoverRate = ((float)crtItem.rscp_ge_95) / crtItem.rscp_total;
                    rcDist.Rxlev95CoverDist = calcTaDistNew(crtItem, cfgSet, fCoverRate);
                }
                cellDistDic.Add(clKey, rcDist);
            }
            return cellDistDic;
        }

        /// <summary>
        /// 计算TA距离
        /// </summary>
        private int calcTaDistNew(UtranCellRscpTaItem crtItem, CfgSettingItem cfgSet, float fCoverRate)
        {
            if (crtItem.ta_total == 0)
                return 0;
            int iArrayLength = crtItem.ta_le_num.Length;
            int iDist = 0;
            int idx = 0;
            int iMaxTa = judgeRepeaterBts(crtItem, cfgSet.iRepeaterStatus);

            for (int i = 1; i <= iArrayLength - 1; i++)
            {
                float fTaRate1 = ((float)crtItem.ta_le_num[i - 1]) / crtItem.ta_total;
                float fTaRate2 = ((float)crtItem.ta_le_num[i]) / crtItem.ta_total;

                if ((i == 1 && fTaRate1 >= fCoverRate))
                {
                    idx = 0;
                    break;
                }
                else if ((fTaRate1 < fCoverRate && fTaRate2 >= fCoverRate) || (iMaxTa == i - 1))
                {
                    idx = i - 1;
                    break;
                }
            }

            if (idx < 0)
            {
                idx = 0;
            }

            if (cfgSet.isTaDownWard)
            {
                iDist = (idx + 1) * 117;
            }
            else
            {
                iDist = (idx + 2) * 117;
            }
            return iDist;
        }

        /// <summary>
        /// 过滤直放站算法
        /// </summary>
        /// <param name="iCfg">1.仅保存正常TA;2.不过滤直放站;3.截断空值TA</param>
        /// <returns>返回TA值</returns>
        private int judgeRepeaterBts(UtranCellRscpTaItem crtItem, int iCfg)
        {
            int iTaNormal = 0; //正常覆盖TA值
            int iTaNull = 0;   //真空TA值
            int iTaExtend = crtItem.ta_le_num.Length;//第二段TA值
            int iRepeaterStatus = 0;     //直放站状态

            for (int i = 5; i <= 25; i++)//初始值为5，使用距离大于等于5*117
            {
                int j = i + 8;//连接8个TA为0，约1000米
                if (crtItem.ta_le_num[j] - crtItem.ta_le_num[i] == 0 && iRepeaterStatus == 0)
                {
                    iTaNormal = i;
                    iTaNull = j - iTaNormal;
                    iRepeaterStatus = 1;
                }
                else if (crtItem.ta_le_num[j] - crtItem.ta_le_num[i] != 0 && iRepeaterStatus == 1)
                {
                    iTaNull = j - iTaNormal;
                    iRepeaterStatus = 2;
                }
                else if (crtItem.ta_le_num[j] - crtItem.ta_le_num[i] == 0 && iRepeaterStatus == 2)
                {
                    iTaExtend = j - (iTaNull + iTaNormal);
                    iRepeaterStatus = 3;
                }
            }

            if (iRepeaterStatus == 0)//未发现直放站，且连续不为0
            {
                iTaNormal = crtItem.ta_le_num.Length - 1;
            }
            else if (iRepeaterStatus == 1)//未发现直放站，存在连续为0情况
            {
                iTaExtend = 0;
            }
            else if (iRepeaterStatus == 2)//发现直放站情况
            {
                iTaExtend = crtItem.ta_le_num.Length - (iTaNull + iTaNormal);
            }

            int iResult = 0;
            if (iCfg == 1)
            {
                iResult = iTaNormal;
            }
            else if (iCfg == 2)
            {
                iResult = crtItem.ta_le_num.Length-1;//数组长度为11，会被置为9
            }
            else if (iCfg == 3)
            {
                iResult = iTaNormal + iTaExtend;
            }
            return iResult;
        }

        /**
        /// <summary>
        /// 计算TA距离
        /// </summary>
        private int calcTaDist(UtranCellRscpTaItem crtItem, CfgSettingItem cfgSet, float fCoverRate)
        {
            if (crtItem.ta_total == 0)
                return 0;

            int iDist = 0;
            int idx = 0;
            int iMaxTa = judgeRepeaterBts(crtItem);
            for (int i = 1; i <= 36; i++)
            {
                float fTaRate1 = ((float)crtItem.ta_le_num[i - 1])/ crtItem.ta_total;
                float fTaRate2 = ((float)crtItem.ta_le_num[i]) / crtItem.ta_total;

                if (fTaRate1 >= fCoverRate || iMaxTa < i-1)
                {
                    idx = i - 1;
                    break;
                }
                if (fTaRate2 >= fCoverRate || iMaxTa < i)
                {
                    idx = i;
                    break;
                }
            }
            if (cfgSet.isTaDownWard)
            {
                iDist = (idx + 1) * 117;
            }
            else
            {
                iDist = (idx + 2) * 117;
            }
            return iDist;
        }
   
        /// <summary>
        /// 过滤直放站算法
        /// </summary>
        /// <returns>返回实际TA值</returns>
        private int judgeRepeaterBts(UtranCellRscpTaItem crtItem)
        {
            int iTa = 0;
            for (int i = 5; i <= 25; i++) //初始值为5，使用距离大于等于5*117
            {
                if (crtItem.ta_le_num[i + 8] - crtItem.ta_le_num[i] == 0) //连接8个TA为0，约1000米
                {
                    iTa = i;
                    break;
                }
            }
            return iTa;
        }
        */

        /// <summary>
        /// 粗略定位另一点信息
        /// </summary>
        /// <param name="iangle">夹角</param>
        public static LongLat calcPointX(int iangle, float idis, LongLat s)
        {
            LongLat e = new LongLat();
            double a = Math.Cos((90 - iangle) * 2 * Math.PI / 360);
            double b = Math.Sin((90 - iangle) * 2 * Math.PI / 360);
            e.fLongitude = s.fLongitude + (float)(a * (idis / 40075360) * 360);//32507969.15
            e.fLatitude = s.fLatitude + (float)(b * (idis / 39940670) * 360); //40172187.93
            return e;
        }
    }

    public class DiySqlGetUtranCellRxlevTa : DIYSQLBase
    {
        /// <summary>
        /// SQL查询语句
        /// </summary>
        public string strSQL { get; set; }

        /// <summary>
        /// 存储过程参数
        /// </summary>
        /// <param name="timeStr">时间</param>
        public void setParam(CfgSettingItem csItem, string strCity)
        {
            DateTime dTime = JavaDate.GetDateTimeFromMilliseconds(csItem.istime * 1000L);
            DateTime tmpTime = dTime;
            DateTime eTime = JavaDate.GetDateTimeFromMilliseconds(csItem.ietime * 1000L);
            List<string> tbList = new List<string>();
            while (tmpTime <= eTime)
            {
                string strTableName = string.Format("DTASYSTEM.dbo.tb_stat_cell_pccpchrscp_ta_dd_{0:yyMM}", tmpTime);
                if (!tbList.Contains(strTableName))
                    tbList.Add(strTableName);
                tmpTime = tmpTime.AddDays(1);
            }

            StringBuilder tmpSql = new StringBuilder();
            strSQL = "";
            foreach (string tbName in tbList)
            {
                if (tmpSql.Length > 0)
                {
                    tmpSql.Append(" union all ");
                }
                tmpSql.Append("select lac,ci,sum(pccpchrscp_total) as pccpchrscp_total,sum(pccpchrscp_ge_80) as pccpchrscp_ge_80,sum(pccpchrscp_ge_85) as pccpchrscp_ge_85," +
                         "sum(pccpchrscp_ge_90) as pccpchrscp_ge_90,sum(pccpchrscp_ge_94) as pccpchrscp_ge_94,sum(pccpchrscp_ge_95) as pccpchrscp_ge_95,sum(ta_total) as ta_total," +
                         "sum(ta_le_0) as ta_le_0,sum(ta_le_1) as ta_le_1,sum(ta_le_2) as ta_le_2,sum(ta_le_3) as ta_le_3,sum(ta_le_4) as ta_le_4,sum(ta_le_5) as ta_le_5,sum(ta_le_6) as ta_le_6," +
                         "sum(ta_le_7) as ta_le_7,sum(ta_le_8) as ta_le_8,sum(ta_le_9) as ta_le_9,sum(ta_le_10) as ta_le_10,sum(ta_le_11) as ta_le_11,sum(ta_le_12) as ta_le_12," +
                         "sum(ta_le_13) as ta_le_13,sum(ta_le_14) as ta_le_14,sum(ta_le_15) as ta_le_15,sum(ta_le_16) as ta_le_16,sum(ta_le_17) as ta_le_17,sum(ta_le_18) as ta_le_18," +
                         "sum(ta_le_19) as ta_le_19,sum(ta_le_20) as ta_le_20,sum(ta_le_21) as ta_le_21,sum(ta_le_22) as ta_le_22,sum(ta_le_23) as ta_le_23,sum(ta_le_24) as ta_le_24," +
                         "sum(ta_le_25) as ta_le_25,sum(ta_le_26) as ta_le_26,sum(ta_le_27) as ta_le_27,sum(ta_le_28) as ta_le_28,sum(ta_le_29) as ta_le_29,sum(ta_le_30) as ta_le_30," +
                         "sum(ta_le_31) as ta_le_31,sum(ta_le_32) as ta_le_32,sum(ta_le_33) as ta_le_33,sum(ta_le_34) as ta_le_34,sum(ta_le_35) as ta_le_35,sum(ta_le_36) as ta_le_36 " +
                         "from " + tbName + " where stime >= " + csItem.istime + " and stime <= " + csItem.ietime + " and region_name = '" + strCity + "' group by lac,ci ");
            }
            strSQL = "select * from (" + tmpSql.ToString() + ") a";
        }

        public DiySqlGetUtranCellRxlevTa(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return strSQL;
        }
        public override string Name
        {
            get { return "DiySqlGetUtranCellRxlevTa"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[46];
            for (int i = 0; i < 46; i++)
            {
                rType[i] = E_VType.E_Int;
            }
            return rType;
        }

        public List<UtranCellRscpTaItem> cellRxlevTaList { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            cellRxlevTaList = new List<UtranCellRscpTaItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    UtranCellRscpTaItem crItem = new UtranCellRscpTaItem();
                    crItem.lac = package.Content.GetParamInt();
                    crItem.ci = package.Content.GetParamInt();
                    crItem.rscp_total = package.Content.GetParamInt();
                    crItem.rscp_ge_80 = package.Content.GetParamInt();
                    crItem.rscp_ge_85 = package.Content.GetParamInt();
                    crItem.rscp_ge_90 = package.Content.GetParamInt();
                    crItem.rscp_ge_70 = package.Content.GetParamInt();//将原来的94读至70
                    crItem.rscp_ge_95 = package.Content.GetParamInt();
                    crItem.ta_total = package.Content.GetParamInt();

                    crItem.ta_le_num[0] = package.Content.GetParamInt();
                    crItem.ta_le_num[1] = package.Content.GetParamInt();
                    crItem.ta_le_num[2] = package.Content.GetParamInt();
                    crItem.ta_le_num[3] = package.Content.GetParamInt();
                    crItem.ta_le_num[4] = package.Content.GetParamInt();
                    crItem.ta_le_num[5] = package.Content.GetParamInt();
                    crItem.ta_le_num[6] = package.Content.GetParamInt();
                    crItem.ta_le_num[7] = package.Content.GetParamInt();
                    crItem.ta_le_num[8] = package.Content.GetParamInt();
                    crItem.ta_le_num[9] = package.Content.GetParamInt();
                    crItem.ta_le_num[10] = package.Content.GetParamInt();

                    crItem.ta_le_num[11] = package.Content.GetParamInt();
                    crItem.ta_le_num[12] = package.Content.GetParamInt();
                    crItem.ta_le_num[13] = package.Content.GetParamInt();
                    crItem.ta_le_num[14] = package.Content.GetParamInt();
                    crItem.ta_le_num[15] = package.Content.GetParamInt();
                    crItem.ta_le_num[16] = package.Content.GetParamInt();
                    crItem.ta_le_num[17] = package.Content.GetParamInt();
                    crItem.ta_le_num[18] = package.Content.GetParamInt();
                    crItem.ta_le_num[19] = package.Content.GetParamInt();
                    crItem.ta_le_num[20] = package.Content.GetParamInt();

                    crItem.ta_le_num[21] = package.Content.GetParamInt();
                    crItem.ta_le_num[22] = package.Content.GetParamInt();
                    crItem.ta_le_num[23] = package.Content.GetParamInt();
                    crItem.ta_le_num[24] = package.Content.GetParamInt();
                    crItem.ta_le_num[25] = package.Content.GetParamInt();
                    crItem.ta_le_num[26] = package.Content.GetParamInt();
                    crItem.ta_le_num[27] = package.Content.GetParamInt();
                    crItem.ta_le_num[28] = package.Content.GetParamInt();
                    crItem.ta_le_num[29] = package.Content.GetParamInt();
                    crItem.ta_le_num[30] = package.Content.GetParamInt();

                    crItem.ta_le_num[31] = package.Content.GetParamInt();
                    crItem.ta_le_num[32] = package.Content.GetParamInt();
                    crItem.ta_le_num[33] = package.Content.GetParamInt();
                    crItem.ta_le_num[34] = package.Content.GetParamInt();
                    crItem.ta_le_num[35] = package.Content.GetParamInt();
                    crItem.ta_le_num[36] = package.Content.GetParamInt();

                    cellRxlevTaList.Add(crItem);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class UtranCellRscpTaItem
    {
        public UtranCellRscpTaItem()
        {
            ta_le_num = new int[37];
        }
        public int lac { get; set; }
        public int ci { get; set; }
        public int rscp_total { get; set; }
        public int rscp_ge_70 { get; set; }
        public int rscp_ge_80 { get; set; }
        public int rscp_ge_85 { get; set; }
        public int rscp_ge_90 { get; set; }
        public int rscp_ge_94 { get; set; }
        public int rscp_ge_95 { get; set; }
        public int ta_total { get; set; }
        public int[] ta_le_num { get; set; }
    }

    public class DiySqlGetTdGridRscp : DIYSQLBase
    {
        /// <summary>
        /// SQL查询语句
        /// </summary>
        public string strSQL { get; set; }
        /// <summary>
        /// 界面配置
        /// </summary>
        public CfgSettingItem csItem { get; set; }

        /// <summary>
        /// 存储过程参数
        /// </summary>
        /// <param name="timeStr">时间</param>
        public void setParam(string strCity)
        {
            DateTime dTime = Convert.ToDateTime(string.Format("{0:yyyy-MM}-1 00:00:00", JavaDate.GetDateTimeFromMilliseconds(csItem.istime * 1000L)));
            DateTime tmpTime = dTime;
            DateTime eTime = Convert.ToDateTime(string.Format("{0:yyyy-MM}-1 00:00:00", JavaDate.GetDateTimeFromMilliseconds(csItem.ietime * 1000L).AddMonths(1))).AddSeconds(-1);

            List<string> tbList = new List<string>();
            while (tmpTime <= eTime)
            {
                string strTableName = string.Format("DTASYSTEM.dbo.tb_auto_para_competition_td_{0:yyMM}", tmpTime);
                if (!tbList.Contains(strTableName))
                    tbList.Add(strTableName);
                tmpTime = tmpTime.AddDays(1);
            }

            StringBuilder tmpSql = new StringBuilder();
            strSQL = "";
            foreach (string tbName in tbList)
            {
                if (tmpSql.Length > 0)
                {
                    tmpSql.Append(" union all ");
                }
                tmpSql.Append("select iltlongitude,iltlatitude,ibrlongitude,ibrlatitude,iPccpchRscp,ivalue1,ivalue2 " +
                         "from " + tbName + " where dstime >= '" + dTime + "' and detime <= '" + eTime + "' and strcity = '" + strCity + "'");
            }
            strSQL = "select iltlongitude,iltlatitude,ibrlongitude,ibrlatitude,avg(iPccpchRscp) as avgrscp,max(ivalue1) as maxrscp,min(ivalue2) as minrscp from (" + tmpSql.ToString() +
                     ") a group by iltlongitude,iltlatitude,ibrlongitude,ibrlatitude";
        }

        public DiySqlGetTdGridRscp(MainModel mainModel)
            : base(mainModel)
        {
            csItem = new CfgSettingItem();
        }

        protected override string getSqlTextString()
        {
            return strSQL;
        }
        public override string Name
        {
            get { return "DiySqlGetGridRscp"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            return rType;
        }

        public Dictionary<GridLongLat, int> gridRscpDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            gridRscpDic = new Dictionary<GridLongLat, int>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            GridLongLat gllItem = new GridLongLat();
            int iltLongitude = package.Content.GetParamInt();
            int iltLatitude = package.Content.GetParamInt();
            package.Content.GetParamInt();//iBrLongitude
            package.Content.GetParamInt();//iBrLatiutde
            int iBrLongitude;
            int iBrLatiutde;

            //栅格化
            iltLongitude = iltLongitude / csItem.iLongitude * csItem.iLongitude;
            iltLatitude = iltLatitude / csItem.iLatitude * csItem.iLatitude;
            iBrLongitude = iltLongitude / csItem.iLongitude * csItem.iLongitude + csItem.iLongitude;
            iBrLatiutde = iltLatitude / csItem.iLatitude * csItem.iLatitude - csItem.iLatitude;

            gllItem.fltlongitude = ((float)iltLongitude) / 10000000;
            gllItem.fltlatitude = ((float)iltLatitude) / 10000000;
            gllItem.fbrlongitude = ((float)iBrLongitude) / 10000000;
            gllItem.fbrlatitude = ((float)iBrLatiutde) / 10000000;

            int iAvgRscp = package.Content.GetParamInt();
            int iMaxRscp = package.Content.GetParamInt();
            int iMinRscp = package.Content.GetParamInt();

            if (!gridRscpDic.ContainsKey(gllItem))
            {
                if (csItem.iTestValue == 1)
                {
                    gridRscpDic.Add(gllItem, iMaxRscp);
                }
                else if (csItem.iTestValue == 2)
                {
                    gridRscpDic.Add(gllItem, iAvgRscp);
                }
                else if (csItem.iTestValue == 3)
                {
                    gridRscpDic.Add(gllItem, iMinRscp);
                }
            }
        }
    }
}
