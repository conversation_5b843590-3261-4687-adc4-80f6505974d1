﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTVoLteESRVCCAna;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoLteWeakCoverAnaByRegion : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static VoLteWeakCoverAnaByRegion instance = null;
        WeakCoverAnaCondtion weakCoverCondtion = new WeakCoverAnaCondtion();
        Dictionary<string, VoLteRsrpRegionInfo> regionInfoDic = null;   //区域维度
        Dictionary<Cell4VoLteRsrp, VoLteRsrpCellInfo> cellInfoDic = null;  //小区维度
        Dictionary<string, MapOperation2> regionMopDic = null;
        List<VoLteRsrpPendingPointInfo> tpList_Pending = null;   //用于存放未找到原因的采样点，用于后续再次判断

        public static VoLteWeakCoverAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLteWeakCoverAnaByRegion();
                    }
                }
            }
            return instance;
        }

        public VoLteWeakCoverAnaByRegion(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        protected VoLteWeakCoverAnaByRegion()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = false;
            this.Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_NCell_RSRP");
        }


        public override string Name
        {
            get
            {
                return "VoLTE弱覆盖分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27008, this.Name);
        }
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            VoLteWeakCoverAnaSetForm dlg = new VoLteWeakCoverAnaSetForm();
            dlg.SetCondition(weakCoverCondtion);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            weakCoverCondtion = dlg.GetCondition();
            return true;
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            regionInfoDic = new Dictionary<string, VoLteRsrpRegionInfo>();
            cellInfoDic = new Dictionary<Cell4VoLteRsrp, VoLteRsrpCellInfo>();
            regionMopDic = new Dictionary<string, MapOperation2>();
            tpList_Pending = new List<VoLteRsrpPendingPointInfo>();
            InitRegionMop2();
        }

        protected override void getResultsAfterQuery()
        {
            dealPendingList();
            fillCellOtherInfo();
        }
        protected override void fireShowForm()
        {
            if (regionInfoDic.Count == 0 && cellInfoDic.Count==0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            VoLteWeakCoverAnaListForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(VoLteWeakCoverAnaListForm)) as VoLteWeakCoverAnaListForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new VoLteWeakCoverAnaListForm(MainModel);
            }
            frm.FillData(regionInfoDic, cellInfoDic);
            frm.Show(MainModel.MainForm);
            frm.BringToFront();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<Event> evtList = new List<Event>();
                foreach (Event evt in fileMng.Events)
                {
                    if (evt.ID == 851 || evt.ID == 899 || evt.ID == 3156 || evt.ID == 3159)//Handover Success
                    {
                        evtList.Add(evt);
                    }
                }
                int index = -1;
                foreach (TestPoint tp in fileMng.TestPoints)
                {
                    addValidWeakRsrpTp(fileMng, evtList, ref index, tp);
                }
            }
        }

        private void addValidWeakRsrpTp(DTFileDataManager fileMng, List<Event> evtList, ref int index, TestPoint tp)
        {
            try
            {
                index++;
                VoLteRsrpRegionInfo curRegion = getCurRegion(tp);
                if (curRegion == null)
                {
                    return;
                }
                VoLteRsrpCellInfo curCell = getCurCell(tp);

                if (isWeakRsrpTp(tp))
                {
                    string reason = getResult(index, fileMng.TestPoints, evtList);
                    if (reason == "其它")
                    {
                        string regionTag = isContainPoint(new DbPoint(tp.Longitude, tp.Latitude));
                        Cell4VoLteRsrp cellTag = getCellTag(tp, curCell.Cell);
                        VoLteRsrpPendingPointInfo pendingInfo = new VoLteRsrpPendingPointInfo(tp, cellTag, regionTag);
                        tpList_Pending.Add(pendingInfo);
                        return;
                    }
                    addToRegion(reason, tp, ref curRegion);
                    addToCell(reason, tp, ref curCell);
                }
            }
            catch
            {
                //continue
            }
        }

        protected void dealPendingList()
        {
            foreach (VoLteRsrpPendingPointInfo tpPending in tpList_Pending)
            {
                VoLteRsrpRegionInfo regionInfo = regionInfoDic[tpPending.RegionTag];
                VoLteRsrpCellInfo cellInfo = cellInfoDic[tpPending.CellTag];

                //在同一个文件中找
                string reason = "";
                reason = getReasonFromNearestPoint(cellInfo, tpPending);
                addToRegion(reason, tpPending.Tp, ref regionInfo);
                addToCell(reason, tpPending.Tp, ref cellInfo);
            }
        }
        protected void fillCellOtherInfo()
        {
            Cursor.Current = Cursors.WaitCursor;

            foreach (Cell4VoLteRsrp cell in cellInfoDic.Keys)
            {
                if (cell.Name == "未知小区")
                {
                    cellInfoDic[cell].AreaName = "";
                    cellInfoDic[cell].RoadName = "";
                }
                else
                {
                    cellInfoDic[cell].AreaName = GISManager.GetInstance().GetGridDesc(cell.Longitude, cell.Latitude);
                    cellInfoDic[cell].RoadName = GISManager.GetInstance().GetRoadPlaceDesc(cell.Longitude, cell.Latitude);
                }
            }

            Cursor.Current = Cursors.Default;
        }

        private void addToRegion(string strReason, TestPoint tp, ref VoLteRsrpRegionInfo curRegion)
        {
            curRegion.RsrpTotal++;

            if (curRegion.ReasonDic.ContainsKey(strReason))
            {
                curRegion.ReasonDic[strReason].RsrpTotal++;
                curRegion.ReasonDic[strReason].TpList.Add(new VoLteRsrpReasonColorTestPoint(strReason, tp));
            }
        }
        public void addToCell(string strReason, TestPoint tp, ref VoLteRsrpCellInfo curCell)
        {
            curCell.RsrpTotal++;

            if (curCell.ReasonDic.ContainsKey(strReason))
            {
                curCell.ReasonDic[strReason].RsrpTotal++;
                curCell.ReasonDic[strReason].TpList.Add(new VoLteRsrpReasonColorTestPoint(strReason, tp));
            }

            if (curCell.FileDic.ContainsKey(tp.FileName))
            {
                int SN = curCell.FileDic[tp.FileName].Count + 1;
                curCell.FileDic[tp.FileName].Add(new VoLteRsrpPointInfo(SN, strReason, tp));
            }
            else
            {
                VoLteRsrpPointInfo fileInfo = new VoLteRsrpPointInfo(1, strReason, tp);
                List<VoLteRsrpPointInfo> tpList = new List<VoLteRsrpPointInfo>();
                tpList.Add(fileInfo);
                curCell.FileDic.Add(tp.FileName, tpList);
            }
        }

        protected string getResult(int curTpIndex, List<TestPoint> pointsList, List<Event> evtList)
        {
            TestPoint tp = pointsList[curTpIndex];
            bool hasResult = false;
            string reason = "";
            foreach (var keyValue in weakCoverCondtion.InventoryDic)
            {
                if (hasResult)
                {
                    break;
                }
                if (!keyValue.Value)
                {
                    continue;
                }
                reason = keyValue.Key;
                hasResult = judgeHasResult(curTpIndex, pointsList, tp, hasResult, reason);
            }

            if (hasResult)
            {
                return reason;
            }
            else
            {
                return "其它";
            }
        }

        private bool judgeHasResult(int curTpIndex, List<TestPoint> pointsList, TestPoint tp, bool hasResult, string reason)
        {
            switch (reason)
            {
                case "缺少规划站":
                    if (isPoorBts(tp))
                    {
                        hasResult = true;
                    }
                    break;
                case "切换不合理":
                    if (isHandoverProblem(pointsList, curTpIndex))
                    {
                        hasResult = true;
                    }
                    break;
                case "覆盖不稳定":
                    if (isUnstabitilyCover(pointsList, curTpIndex))
                    {
                        hasResult = true;
                    }
                    break;
                case "过覆盖":
                    if (isOverCover(pointsList, tp))
                    {
                        hasResult = true;
                    }
                    break;
                default:
                    break;
            }

            return hasResult;
        }

        private bool isPoorBts(TestPoint tp)
        {
            List<LTEBTS> btsList = CellManager.GetInstance().GetLTEBTSs(MapLTECellLayer.CurShowSnapshotTime);
            foreach (LTEBTS bts in btsList)
            {
                double dis = MathFuncs.GetDistance(bts.Longitude, bts.Latitude, tp.Longitude, tp.Latitude);
                if (dis <= weakCoverCondtion.LteBtsDisGate)
                {
                    return false;
                }
            }
            return true;
        }
        private bool isHandoverProblem(List<TestPoint> pointsList, int curTpIndex)//是否切换不合理
        {
            TestPoint curTp = pointsList[curTpIndex];

            for (int i = curTpIndex - 1; i >=0; i--)
            {
                TestPoint tp = pointsList[i];
                if ((curTp.DateTime - tp.DateTime).TotalSeconds > weakCoverCondtion.MinSecondsMainLowerNear)
                {
                    break;
                }
                float? rsrp = GetRSRP(tp);
                float? maxNRsrp = GetNRSRP(tp, 0);
                if (rsrp != null && maxNRsrp != null && maxNRsrp - rsrp >= weakCoverCondtion.RsrpMainLowerNear)
                {
                    return true;
                }
            }
            return false;
        }
        private bool isUnstabitilyCover(List<TestPoint> pointsList, int curIndex)//是否覆盖不稳定
        {
            TestPoint curTp = pointsList[curIndex];
            for (int i = curIndex - 1; i >= 0; i--)
            {
                TestPoint tp = pointsList[i];
                if ((curTp.DateTime - tp.DateTime).TotalSeconds > weakCoverCondtion.LastSecondsBeforeWeak)
                {
                    break;
                }
                float? rsrp = GetRSRP(tp);
                if (rsrp != null && rsrp < weakCoverCondtion.RsrpWeakGate)
                {
                    return false;
                }
            }
            return true;
        }
        private bool isOverCover(List<TestPoint> pointsList, TestPoint curTp)
        {
            LTECell curCell = curTp.GetMainLTECell_TdOrFdd();
            if (curCell != null)
            {
                if (curCell.Type == LTEBTSType.Indoor)
                {
                    return false;
                }
                double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(curCell, weakCoverCondtion.IdealCoverBtsCount);
                double rationalDistance = radiusOfCell * weakCoverCondtion.IdealCoverRadFactor;

                double distanceToCell = MathFuncs.GetDistance(curTp.Longitude, curTp.Latitude, curCell.Longitude, curCell.Latitude);
                if (distanceToCell > rationalDistance)
                {
                    return true;
                }

                for (int i = 0; i < pointsList.Count; i++)
                {
                    TestPoint tp = pointsList[i];
                    LTECell cell = tp.GetMainLTECell_TdOrFdd();
                    if (cell != null && cell.ID == curCell.ID)
                    {
                        distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, curCell.Longitude, curCell.Latitude);
                        if (distanceToCell > rationalDistance)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
        private bool isWeakRsrpTp(TestPoint tp)
        {
            float? rsrp = GetRSRP(tp);
            if (rsrp == null || rsrp > weakCoverCondtion.RsrpWeakGate)
            {
                return false;
            }
            return true;
        }
        private void InitRegionMop2()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;

            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
            }
        }

        protected VoLteRsrpRegionInfo getCurRegion(TestPoint tp)
        {
            //获取区域名称
            string strRegionName = isContainPoint(new DbPoint(tp.Longitude, tp.Latitude));
            if (strRegionName == null)
            {
                return null;
            }

            VoLteRsrpRegionInfo curRegion = null;
            if (regionInfoDic.ContainsKey(strRegionName))
            {
                curRegion = regionInfoDic[strRegionName];
                curRegion.TpTotal++;
            }
            else
            {
                curRegion = new VoLteRsrpRegionInfo(strRegionName,weakCoverCondtion);
                regionInfoDic[strRegionName] = curRegion;
            }

            return curRegion;
        }
        protected VoLteRsrpCellInfo getCurCell(TestPoint tp)
        {
            LTECell cell = tp.GetMainLTECell_TdOrFdd();
            Cell4VoLteRsrp cellTag = getCellTag(tp, cell);

            VoLteRsrpCellInfo curCell = null;
            if (cellInfoDic.ContainsKey(cellTag))
            {
                curCell = cellInfoDic[cellTag];
                curCell.TpTotal++;
            }
            else
            {
                curCell = new VoLteRsrpCellInfo(cell, weakCoverCondtion);
                cellInfoDic[cellTag] = curCell;
            }
            return curCell;
        }
        protected Cell4VoLteRsrp getCellTag(TestPoint tp, LTECell cell)
        {
            Cell4VoLteRsrp cellTag = new Cell4VoLteRsrp();
            if (cell != null)
            {
                cellTag.Name = cell.Name;
                cellTag.TAC = cell.TAC;
                cellTag.ECI = cell.ECI;
                cellTag.Longitude = cell.Longitude;
                cellTag.Latitude = cell.Latitude;
            }
            else
            {
                cellTag.Name = "未知小区";
                if (GetTAC(tp) != null)
                {
                    cellTag.TAC = (int)(ushort)GetTAC(tp);
                }
                else
                {
                    cellTag.TAC = 0;
                }
                if (GetECI(tp) != null)
                {
                    cellTag.ECI = (int)GetECI(tp);
                }
                else
                {
                    cellTag.ECI = 0;
                }
                cellTag.Longitude = 0;
                cellTag.Latitude = 0;
            }
            return cellTag;
        }

        private string isContainPoint(DbPoint dPoint)
        {
            foreach (string strKey in regionMopDic.Keys)
            {
                if (regionMopDic[strKey].CheckPointInRegion(dPoint.x, dPoint.y))
                {
                    return strKey;
                }
            }
            return null;
        }
        protected string getReasonFromNearestPoint(VoLteRsrpCellInfo cellInfo, VoLteRsrpPendingPointInfo tpPending)
        {
            string strFileName = tpPending.Tp.FileName;

            if (cellInfo.FileDic.ContainsKey(strFileName))  //取出文件对应的所有点
            {
                List<VoLteRsrpPointInfo> fileTpList = cellInfo.FileDic[strFileName];

                VoLteRsrpPointInfo afterTp, beforeTp;
                getBeforeAndAfterTP(tpPending, fileTpList, out afterTp, out beforeTp);

                if (afterTp != null)
                {
                    return afterTp.Reason;
                }
                else if (beforeTp != null)
                {
                    return beforeTp.Reason;
                }
                else
                {
                    return "其它";
                }
            }
            else
            {
                return "其它";
            }
        }

        private static void getBeforeAndAfterTP(VoLteRsrpPendingPointInfo tpPending, List<VoLteRsrpPointInfo> fileTpList, 
            out VoLteRsrpPointInfo afterTp, out VoLteRsrpPointInfo beforeTp)
        {
            afterTp = null;
            beforeTp = null;
            foreach (VoLteRsrpPointInfo tpFile in fileTpList)
            {
                if ((tpPending.Tp.Time - tpFile.ColorTp.Tp.Time) <= 0 && (tpPending.Tp.Time - tpFile.ColorTp.Tp.Time) >= -5)   //向后看5秒内
                {
                    if (afterTp == null)
                    {
                        afterTp = tpFile;
                    }
                    else if (afterTp.ColorTp.Tp.Time > tpFile.ColorTp.Tp.Time)  //更近
                    {
                        afterTp = tpFile;
                    }
                }
                else if ((tpPending.Tp.Time - tpFile.ColorTp.Tp.Time) > 0 && (tpPending.Tp.Time - tpFile.ColorTp.Tp.Time) <= 5)  //向前看5秒内
                {
                    if (beforeTp == null)
                    {
                        beforeTp = tpFile;
                    }
                    else if (beforeTp.ColorTp.Tp.Time < tpFile.ColorTp.Tp.Time)  //更近
                    {
                        beforeTp = tpFile;
                    }
                }
            }
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
        protected float? GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRP"];
            }
            return (float?)tp["lte_RSRP"];
        }
        protected float? GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_NCell_RSRP", index];
            }
            return (float?)tp["lte_NCell_RSRP", index];
        }
        protected object GetTAC(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_TAC"];
            }
            return tp["lte_TAC"];
        }
        protected object GetECI(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_ECI"];
            }
            return tp["lte_ECI"];
        }
    }

    public class VoLteWeakCoverAnaByRegion_FDD : VoLteWeakCoverAnaByRegion
    {
        private static VoLteWeakCoverAnaByRegion_FDD instance = null;
        public static new VoLteWeakCoverAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLteWeakCoverAnaByRegion_FDD();
                    }
                }
            }
            return instance;
        }
        public VoLteWeakCoverAnaByRegion_FDD()
            : base()
        {
            this.Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_NCell_RSRP");

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)testPoint.ServiceType)) return false;
            return true;
        }
        public override string Name
        {
            get
            {
                return "VoLTE_FDD弱覆盖分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30006, this.Name);
        }
    }

    public class VoLteRsrpRegionInfo
    {
        public string RegionName { get; set; }
        public int TpTotal { get; set; }
        public int RsrpTotal { get; set; }
        public double WeakRsrpPer
        {
            get
            {
                if (TpTotal != 0)
                {
                    return Math.Round((double)100 * RsrpTotal / TpTotal, 2);
                }
                return 0;
            }
        }
        public Dictionary<string, VoLteRsrpReasonInfo> ReasonDic { get; set; }

        public VoLteRsrpRegionInfo(string regionName, WeakCoverAnaCondtion weakCondtion)
        {
            this.RegionName = regionName;
            this.TpTotal = 1;
            this.RsrpTotal = 0;
            this.ReasonDic = VoLteRsrpReasonInfo.InitReasonDic(weakCondtion);
        }
    }

    public class VoLteRsrpReasonInfo
    {
        public string Reason { get; set; }
        public int RsrpTotal { get; set; }
        public List<VoLteRsrpReasonColorTestPoint> TpList { get; set; }

        public VoLteRsrpReasonInfo(string reason)
        {
            this.Reason = reason;
            this.RsrpTotal = 0;
            this.TpList = new List<VoLteRsrpReasonColorTestPoint>();
        }

        public static Dictionary<string, VoLteRsrpReasonInfo> InitReasonDic(WeakCoverAnaCondtion weakCoverCondtion)
        {
            Dictionary<string, VoLteRsrpReasonInfo> reasonDic = new Dictionary<string, VoLteRsrpReasonInfo>();
            if (weakCoverCondtion != null)
            {
                foreach (var keyValue in weakCoverCondtion.InventoryDic)
                {
                    if (keyValue.Value)
                    {
                        VoLteRsrpReasonInfo info = new VoLteRsrpReasonInfo(keyValue.Key);
                        reasonDic.Add(keyValue.Key, info);
                    }
                }
            }
            VoLteRsrpReasonInfo info2 = new VoLteRsrpReasonInfo("其它");
            reasonDic.Add("其它", info2);
            return reasonDic;
        }
    }

    public class VoLteRsrpReasonColorTestPoint
    {
        public string Reason { get; set; }
        public System.Drawing.Color Color { get; set; }
        public TestPoint Tp { get; set; }

        public VoLteRsrpReasonColorTestPoint(string reason, TestPoint tp)
        {
            this.Reason = reason;
            this.Tp = tp;
            setPointColor(reason);
        }

        private void setPointColor(string reason)
        {
            switch (reason)
            {
                case "弱覆盖":
                    Color = System.Drawing.Color.Olive;
                    break;
                case "切换不合理":
                    Color = System.Drawing.Color.Purple;
                    break;
                case "其它":
                    Color = System.Drawing.Color.Crimson;
                    break;
                default:
                    break;
            }
        }
    }


    public class VoLteRsrpCellInfo
    {
        public LTECell Cell{ get; set; }
        public int TpTotal{ get; set; }
        public int RsrpTotal{ get; set; }

        public string AreaName{ get; set; }
        public string RoadName{ get; set; }
        public Dictionary<string, VoLteRsrpReasonInfo> ReasonDic{ get; set; }
        public Dictionary<string, List<VoLteRsrpPointInfo>> FileDic{ get; set; }

        public VoLteRsrpCellInfo(LTECell cell, WeakCoverAnaCondtion weakCondtion)
        {
            this.Cell = cell;
            this.TpTotal = 1;
            this.RsrpTotal = 0;
            this.ReasonDic = VoLteRsrpReasonInfo.InitReasonDic(weakCondtion);
            this.FileDic = new Dictionary<string, List<VoLteRsrpPointInfo>>();
        }
    }
    public class VoLteRsrpPointInfo
    {
        public int SN { get; set; }
        public string Reason { get; set; }
        public string TpTime { get; set; }
        public float Rsrp { get; set; }
        public float Sinr { get; set; }
        public VoLteRsrpReasonColorTestPoint ColorTp { get; set; }

        public VoLteRsrpPointInfo(int SN, string reason, TestPoint tp)
        {
            this.SN = SN;
            this.Reason = reason;
            this.TpTime = tp.DateTimeStringWithMillisecond;

            float? rsrp = null;
            if (tp is LTEFddTestPoint)
            {
                rsrp = (float?)tp["lte_fdd_RSRP"];
            }
            else
            {
                rsrp = (float?)tp["lte_RSRP"];
            }
            if (rsrp != null)
            {
                this.Rsrp = (float)rsrp;
            }

            float? sinr = null;
            if (tp is LTEFddTestPoint)
            {
                sinr = (float?)tp["lte_fdd_SINR"];
            }
            else
            {
                sinr = (float?)tp["lte_SINR"];
            }
            if (sinr != null)
            {
                this.Sinr = (float)sinr;
            }
            ColorTp = new VoLteRsrpReasonColorTestPoint(reason, tp);
        }
    }
    public class Cell4VoLteRsrp
    {
        public string Name { get; set; }
        public int TAC { get; set; }
        public int ECI { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public Cell4VoLteRsrp()
        {
            Name = "";
            TAC = 0;
            ECI = 0;
            Longitude = 0;
            Latitude = 0;
        }

        public override bool Equals(object obj)
        {
            Cell4VoLteRsrp other = obj as Cell4VoLteRsrp;

            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.Name.Equals(other.Name)
                    && this.TAC.Equals(other.TAC)
                    && this.ECI.Equals(other.ECI));
        }

        public override int GetHashCode()
        {
            return (this.TAC.ToString() + this.ECI.ToString()).GetHashCode();
        }
    }

    public class VoLteRsrpPendingPointInfo
    {
        public TestPoint Tp{ get; set; }
        public Cell4VoLteRsrp CellTag{ get; set; }
        public string RegionTag{ get; set; }

        public VoLteRsrpPendingPointInfo(TestPoint tp, Cell4VoLteRsrp cellTag, string regionTag)
        {
            this.Tp = tp;
            this.CellTag = cellTag;
            this.RegionTag = regionTag;
        }
    }
}
