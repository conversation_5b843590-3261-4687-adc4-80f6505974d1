﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptAna_SX_NR : StationAcceptBaseWithWorkParams<string, string>
    {
        #region 基础信息
        protected static readonly object lockObj = new object();
        private static StationAcceptAna_SX_NR intance = null;
        public static StationAcceptAna_SX_NR GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new StationAcceptAna_SX_NR();
                    }
                }
            }
            return intance;
        }

        protected StationAcceptAna_SX_NR()
            : base(MainModel.GetInstance())
        {
            if (intance != null)
            {
                return;
            }
            this.isIgnoreExport = true;
        }

        public override string Name
        {
            get
            {
                return "陕西NR单站验收";
            }
        }

        protected override void init()
        {
            backgroundConfigManager = BackgroundFuncConfigManager.GetInstance();
            FilterSampleByRegion = false;
            IncludeEvent = false;

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);

            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_APP_type");
            Columns.Add("NR_Throughput_PDCP_DL");
            Columns.Add("NR_Throughput_PDCP_UL");

            workParamSumDic = new Dictionary<string, Dictionary<string, BtsAcceptWorkParamBase<string>>>();
        }
        #endregion

        #region 属性变量
        public StationAcceptCondition_SX_NR StationCondition { get; set; } = new StationAcceptCondition_SX_NR();

        private BackgroundFuncConfigManager backgroundConfigManager = null;
        private BtsAcceptRecordInfo_SX<string> recordInfo;
        private Dictionary<FileInfo, ErrorFileType> errorFileDic;

        protected StationAutoAcceptManager_SX_NR manager;
        private List<CellAcceptFileInfo_SX_NR> fileAnalysedResultList;
        #endregion

        #region 初始化
        protected override void loadWorkParams()
        {
            StationAcceptWorkParams_SX_NR workParams = GetWorkParamsHelper_SX_NR.GetInstance().GetWorkParams(StationCondition) as StationAcceptWorkParams_SX_NR;
            if (workParams != null)
            {
                workParams.setWorkParam(workParamSumDic);
            }
        }

        protected override bool judgeWorkParams()
        {
            if (workParamSumDic == null || workParamSumDic.Count <= 0 || !workParamSumDic.ContainsKey(curDistrictName))
            {
                reportBackgroundInfo("未读取到" + curDistrictName + "的待单验的基站信息");
                return false;
            }
            return true;
        }

        protected override void initCurBtsAcceptInfo()
        {
            errorFileDic = new Dictionary<FileInfo, ErrorFileType>();
            fileAnalysedResultList = new List<CellAcceptFileInfo_SX_NR>();
        }

        protected override bool judgeValidBts(BtsAcceptWorkParamBase<string> btsInfo)
        {
            if (string.IsNullOrEmpty(StationCondition.FileNameFilters))
            {
                return true;
            }

            string[] split = new string[] { "or" };
            string[] filters = StationCondition.FileNameFilters.Split(split, StringSplitOptions.RemoveEmptyEntries);
            foreach (var filter in filters)
            {
                if (btsInfo.BtsNameFull.Contains(filter.Trim()))
                {
                    return true;
                }
                foreach (var cellInfo in btsInfo.CellWorkParams)
                {
                    if (cellInfo.CellNameFull.Contains(filter.Trim()))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        protected override void setFileNameKeyStr(BtsAcceptWorkParamBase<string> btsInfo)
        {
            BtsAcceptWorkParam_SX sxBtsInfo = btsInfo as BtsAcceptWorkParam_SX;
            recordInfo = new BtsAcceptRecordInfo_SX<string>(btsInfo);
            StringBuilder strbFilter = new StringBuilder();
            strbFilter.Append(sxBtsInfo.BtsName);
            foreach (CellAcceptWorkParamBase info in btsInfo.CellWorkParamDic.Values)
            {
                CellAcceptWorkParam_SX sxInfo = info as CellAcceptWorkParam_SX;
                strbFilter.Append(" or ");
                strbFilter.Append(sxInfo.CellNameKey);
            }
            FileNameKeyStr = strbFilter.ToString();
        }
        #endregion

        #region 获取文件
        protected override bool filterFile(FileInfo fileInfo)
        {
            bool isValid = FileNameRuleHelper_SX.JudgeFileValidByName(fileInfo.Name);
            if (!isValid)
            {
                string strTip = string.Format("[{0}]是站[{1}]对应的环测文件或者并非单验文件", fileInfo.Name, curBtsInfo.BtsNameFull);
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(strTip);
                return true;
            }
            return false;
        }
		
        protected override void getFilesForAnalyse()
        {
            BackgroundFuncQueryManager.GetInstance().GetFilterFile_CellAccept(GetSubFuncID(), ServiceTypeString
                            , ((int)carrierID).ToString(), "strfilename", FileNameKeyStr);

            if (mainModel.FileInfos.Count == 0)
            {
                recordInfo.BtsAcceptErrorInfo += "未找到基站对应的单验文件;";
                return;
            }

            StationAcceptHelper_SX.GetNewestFile(mainModel);
        }
        #endregion

        #region 回放后分析文件
        protected override void doStatWithQuery()
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            manager = new StationAutoAcceptManager_SX_NR();
            manager.AnalyzeFile(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0]);
            saveFileAnalyzedResult();
            MainModel.DTDataManager.Clear();
        }

        private void saveFileAnalyzedResult()
        {
            ErrorFileType type = StationAcceptHelper_SX.JudgeError(manager);
            if (type == ErrorFileType.NULL)
            {
                fileAnalysedResultList.Add(manager.AcceptFileInfo as CellAcceptFileInfo_SX_NR);
  				var cellInfo = curBtsInfo.CellWorkParamDic[manager.AcceptFileInfo.CellNameKey];
                manager.AcceptFileInfo.BtsName = cellInfo.BtsNameFull;
                manager.AcceptFileInfo.CellName = cellInfo.CellNameFull;
            }
            else
            {
                errorFileDic.Add(curAnaFileInfo, type);
            }
        }
        #endregion

        #region 分析完文件后处理单验结果
        protected override void doSomethingAfterAnalyseFiles()
        {
            BtsAcceptInfo_SX<NRCell,string> btsAcceptInfo = getAcceptedResultInfo();
            if (btsAcceptInfo == null && mainModel.FileInfos.Count > 0)
            {
                //有文件进行过分析,但没有结果
                recordInfo.BtsAcceptErrorInfo += "当前站没有分析结果;";
            }
            else if (btsAcceptInfo != null && btsAcceptInfo.IsOutDoor)
            {
                QueryCondition queryCond = StationAcceptHelper_SX.GetQueryCondition(backgroundConfigManager);
                getOutCellCircleTestInfo(btsAcceptInfo, queryCond);
            }

            ExportReportHelper_SX_NR export = new ExportReportHelper_SX_NR();
            export.ExportReportToExcel(btsAcceptInfo, StationCondition.FilePath, recordInfo);

            StationAcceptHelper_SX.BackToBackgroundWorkSheet();
            string errorInfo = dealErrorInfo();
            recordInfo.Calculate();

            //导出无误后更新工参状态
            var update = new UpdateWorkParamDes_SX(curBtsInfo.BtsNameFull, errorInfo, recordInfo.IsBtsPassAccept, NetType.NR);
            update.Query();

            var record = new UpLoadAcceptRecord_SX<string>(recordInfo, NetType.NR);
            record.Query();
        }

        #region 获取单验结果
        protected BtsAcceptInfo_SX<NRCell, string> getAcceptedResultInfo()
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取已分析文件结果" + fileAnalysedResultList.Count + "个...");

            if (fileAnalysedResultList.Count == 0)
            {
                return null;
            }
			var fstFileRes = fileAnalysedResultList[0];
            bool isOutDoor = fstFileRes.Cell.Type == NRBTSType.Outdoor;
            int btsId = fstFileRes.Cell.BelongBTS.BTSID;
            BtsAcceptInfo_SX<NRCell, string> btsAcceptInfo = getNewBtsAcceptInfo(isOutDoor, fstFileRes.BtsName, btsId);

            foreach (var item in fileAnalysedResultList)
            {
                AcceptKpiInfo_SX kpiInfo = new AcceptKpiInfo_SX();
                kpiInfo.Add(item);

                if (!btsAcceptInfo.CellAcceptInfoDic.TryGetValue(item.CellName, out var cellAcceptInfo))
                {
                    cellAcceptInfo = getNewCellAcceptInfo(isOutDoor);
                    cellAcceptInfo.CellId = item.CellId;
                    cellAcceptInfo.CellName = item.CellName;
                    cellAcceptInfo.BtsId = btsId;
                    cellAcceptInfo.BtsName = item.BtsName;
                    cellAcceptInfo.CellNameKey = item.CellNameKey;

                    btsAcceptInfo.CellAcceptInfoDic[item.CellName] = cellAcceptInfo;
                }
                cellAcceptInfo.AddFileAcceptInfo(item.FileNameKey, kpiInfo);
            }

            foreach (var item in btsAcceptInfo.CellAcceptInfoDic.Values)
            {
                item.Calculate();
            }

            dealCellRecord(btsAcceptInfo);
            return btsAcceptInfo;
        }

        private BtsAcceptInfo_SX<NRCell,string> getNewBtsAcceptInfo(bool isOutDoor, string btsName, int btsId)
        {
            BtsAcceptInfo_SX<NRCell, string> btsAcceptInfo = new BtsAcceptInfo_SX<NRCell, string>(isOutDoor)
            {
                BtsName = btsName,
                BtsId = btsId
            };
            return btsAcceptInfo;
        }

        private CellAcceptInfoBase_SX getNewCellAcceptInfo(bool isOutDoor)
        {
            CellAcceptInfoBase_SX cellAcceptInfo;
            if (isOutDoor)
            {
                cellAcceptInfo = new OutDoorCellAcceptInfo_SX();
            }
            else
            {
                cellAcceptInfo = new IndoorCellAcceptInfo_SX();
            }
            return cellAcceptInfo;
        }

        private void dealCellRecord(BtsAcceptInfo_SX<NRCell, string> btsInfo)
        {
            foreach (var cellInfo in btsInfo.CellAcceptInfoDic.Values)
            {
                foreach (var kpiDesc in cellInfo.KpiInfoDic)
                {
                    addCellErrorInfo(cellInfo.CellNameKey, kpiDesc.Value.Rsrp.Count, kpiDesc.Key, "Rsrp");
                    addCellErrorInfo(cellInfo.CellNameKey, kpiDesc.Value.Sinr.Count, kpiDesc.Key, "Sinr");
                    addCellErrorInfo(cellInfo.CellNameKey, kpiDesc.Value.Speed.Count, kpiDesc.Key, "Speed");
                    addCellErrorInfo(cellInfo.CellNameKey, kpiDesc.Value.PingBig.Count, kpiDesc.Key, "PingBig");
                    addCellErrorInfo(cellInfo.CellNameKey, kpiDesc.Value.PingSmall.Count, kpiDesc.Key, "PingSmall");
                }
            }
        }

        private void addCellErrorInfo(string cellNameKey, int tpCount, string kpiDesc, string type)
        {
            if (tpCount == 0)
            {
                recordInfo.CellRecordInfoDic[cellNameKey].CellAcceptErrorInfo += string.Format("没有获取到[{0}]{1}数据;", kpiDesc, type);
            }
        }
        #endregion

        /// <summary>
        /// 处理环测文件
        /// </summary>
        private void getOutCellCircleTestInfo(BtsAcceptInfo_SX<NRCell, string> btsAcceptInfo, QueryCondition queryCond)
        {
            List<CellAcceptInfoBase_SX> cellInfoList = new List<CellAcceptInfoBase_SX>(btsAcceptInfo.CellAcceptInfoDic.Values);
            if (cellInfoList.Count > 0)
            {
                CellAcceptInfoBase_SX cellInfo = cellInfoList[0];
                NRCell cell = CellManager.GetInstance().GetNRCellLatest(cellInfo.CellNameKey);
                if (cell != null && cell.BelongBTS != null)
                {
                    btsAcceptInfo.CellList = cell.BelongBTS.Cells;
                }
            }

            if (queryCond != null)
            {
                StationAcceptHelper_SX.GetCellCircleTestTPs(Columns, btsAcceptInfo, queryCond, NetType.NR);
            }
        }

        private string dealErrorInfo()
        {
            string curErrorinfo = "";
            if (!string.IsNullOrEmpty(recordInfo.BtsAcceptErrorInfo) || errorFileDic.Count > 0)
            {
                StringBuilder errorBulider = new StringBuilder();
                errorBulider.AppendLine(recordInfo.BtsAcceptErrorInfo);
                foreach (var errorFile in errorFileDic)
                {
                    string errorType = EnumDescriptionAttribute.GetText(errorFile.Value);
                    errorBulider.AppendLine("文件[" + errorFile.Key.Name + "]" + errorType + ";");
                }
                curErrorinfo = errorBulider.ToString();
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("单验问题信息 : " + curErrorinfo);
            }
            else
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("[{0}]单验正常\r\n", recordInfo.BtsName));
            }

            return curErrorinfo.Replace("\r\n", "");
        }
        #endregion

        #region Background
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.NR业务专题; }
        }
        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.单站验收; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>
                {
                    ["BackgroundStat"] = BackgroundStat,
                    ["ExportReportSet"] = StationCondition.Params
                };
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("ExportReportSet"))
                {
                    StationCondition.Params = param["ExportReportSet"] as Dictionary<string, object>;
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new StationAcceptPropertiesSX_NR(this);
            }
        }
        #endregion
    }
}
