﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.IO;
using DevExpress.XtraEditors;
using CQTLibrary.PublicItem;

namespace MasterCom.RAMS
{
    public delegate Dictionary<DateTime, List<ParaResult>> GetAlarmDetailInfoByCqtFunc(string strCqtName);

    public delegate Dictionary<LaiKey, Dictionary<DateTime, List<ParaResult>>> GetAlarmDetailInfoByCqtFuncCell(string strCqtName);
    class CQTAdressPropertyAssessProcessor
    {
        private static string CqtFormulaCfgFileName = AppDomain.CurrentDomain.SetupInformation.ApplicationBase +
            "userData\\CqtFormulaCfg.xml";
        private readonly MainModel model;
        private CQTLibrary.RAMS.NET.MainModel CQTModel;
        private readonly CQTAdressPropertyAssessForm CQTAdrPrpAssForm;
        private QueryCondition condition { get; set; }

        private List<AlarmResult> alarmResults;
        private CQTLibrary.DbManage.IOSearch ioSearch;
        private CQTLibrary.CqtZTFunc.AlarmAna alarmAna;
        //private DateTime timeStart;
        //private DateTime timeEnd;
        private CQTAdrePropertyAssessGSMAlarmForm GsmAlarmForm { get; set; }

        //protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        //{
        //    return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21031, "查询业务网络体检");//临时
        //}
        public CQTAdressPropertyAssessProcessor(MainModel model,QueryCondition condition)
        {
            this.model = model;
            this.condition = condition;
            IniCQTModel();
            CQTAdrPrpAssForm = new CQTAdressPropertyAssessForm(condition);

        }
        private void IniCQTModel()
        { 
            CQTModel = new CQTLibrary.RAMS.NET.MainModel();
            CQTModel.DistrictID = model.DistrictID;
            CQTModel.ServerIP = model.Server.IP;
            CQTModel.ServerPort = model.Server.Port;
            CQTModel.UserName = model.User.LoginName;
            CQTModel.UserID = model.User.ID;
            CQTModel.UserPass = model.User.Password;
        }
        public void Run()
        {
            if (CQTAdrPrpAssForm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                if (!File.Exists(CqtFormulaCfgFileName))
                {
                    XtraMessageBox.Show("CQT配置文件不全，请您检查配置文件");
                    return;
                }
                
                //TimePeriod timePeriod = condition.Periods[0];
                //timeStart = timePeriod.BeginTime;
                //timeEnd = timePeriod.EndTime;                
                ioSearch = new CQTLibrary.DbManage.IOSearch();
                alarmAna = new CQTLibrary.CqtZTFunc.AlarmAna();
                //获得预警数据
                WaitBox.Show(WaitBoxForGetAlarmResult);
                
                GsmAlarmForm = new CQTAdrePropertyAssessGSMAlarmForm(alarmResults, CQTAdrPrpAssForm.NetTypeSelected.TDCheckBoxRes);
                GsmAlarmForm.GetAlarmDetailInfoByCqtDo = new GetAlarmDetailInfoByCqtFunc(GetAlarmDetailInfoByCqt);
                GsmAlarmForm.GetAlarmDetailInfoByCqtCellDo = new GetAlarmDetailInfoByCqtFuncCell(GetAlarmDetailCellInfoByCqt);

                

                GsmAlarmForm.ShowDialog();
            }
        }

        private void WaitBoxForGetAlarmResult()
        {
            WaitBox.Text = "正在查询性能数据...";
            WaitBox.ProgressPercent = 30;
            //alarmResults = alarmAna.alarmZtFunc(timeStart, timeEnd, CQTModel, ioSearch.getConfigXml(CqtFormulaCfgFileName),
            //        CQTAdrPrpAssForm.NetTypeSelected.GetNetType(),
            //        CQTAdrPrpAssForm.CellVillageSelected.GetCellVillage(),
            //        CQTAdrPrpAssForm.CompareTimeSelected.GetCompareTime(),
            //        CQTAdrPrpAssForm.AnalysisModelSelected.GetAnalysisModel());
            alarmResults = alarmAna.alarmZtFunc(CQTAdrPrpAssForm.CompareTimeSelected.GetSTime(), CQTAdrPrpAssForm.CompareTimeSelected.GetEtime()
                   ,CQTAdrPrpAssForm.CompareTimeSelected.GetCompareSTime(),CQTAdrPrpAssForm.CompareTimeSelected.GetCompareETime(),CQTModel
                   , ioSearch.getConfigXml(CqtFormulaCfgFileName)
                   , CQTAdrPrpAssForm.NetTypeSelected.GetNetType()
                   , CQTAdrPrpAssForm.CellVillageSelected.GetCellVillage()
                   , CQTAdrPrpAssForm.AnalysisModelSelected.GetAnalysisModel());

            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        private Dictionary<DateTime, List<ParaResult>> GetAlarmDetailInfoByCqt(string strCqtName)
        {
            return alarmAna.getAlarmDetailInfoByCqt(CQTAdrPrpAssForm.CompareTimeSelected.GetSTime(), CQTAdrPrpAssForm.CompareTimeSelected.GetCompareETime(), CQTModel, ioSearch.getConfigXml(CqtFormulaCfgFileName),
                    CQTAdrPrpAssForm.NetTypeSelected.GetNetType(),
                    CQTAdrPrpAssForm.CellVillageSelected.GetCellVillage(),
                    strCqtName);
        }

        private Dictionary<LaiKey, Dictionary<DateTime, List<ParaResult>>> GetAlarmDetailCellInfoByCqt(string strCqtName)
        {
            return alarmAna.getAlarmDetailInfoByCqtCell(CQTAdrPrpAssForm.CompareTimeSelected.GetSTime(), CQTAdrPrpAssForm.CompareTimeSelected.GetCompareETime(), CQTModel, ioSearch.getConfigXml(CqtFormulaCfgFileName),
                    CQTAdrPrpAssForm.NetTypeSelected.GetNetType(),
                    CQTAdrPrpAssForm.CellVillageSelected.GetCellVillage(),
                    strCqtName);
        }
    }
}
