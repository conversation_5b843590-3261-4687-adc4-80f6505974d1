﻿using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class ZTCqtRenderingLayer : LayerBase
    {
        public ZTCqtRenderingLayer()
            : base("CQT渲染图层")
        {

        }

        //采样点大小
        readonly int size = 16;
        //采样点集合
        public List<LayerDataInfo> SelectedPoints { get; set; } = new List<LayerDataInfo>();
        public LayerDataInfo LastSelectedData { get; set; } = new LayerDataInfo();
        public event EventHandler SelectedPointsChanged;
        public List<LayerDataInfo> DataInfos { get; set; } = new List<LayerDataInfo>();
        private readonly Pen penSelected = new Pen(Color.Red, 3);

        private static ZTCqtRenderingLayer layerInstance = null;
        private static readonly object lockObj = new object();
        internal static ZTCqtRenderingLayer GetInstance()
        {
            if (layerInstance == null)
            {
                lock (lockObj)
                {
                    if (layerInstance == null)
                    {
                        layerInstance = new ZTCqtRenderingLayer();
                    }
                }
            }
            return layerInstance;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || DataInfos.Count <= 0)
            {
                return;
            }
            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(mapScale);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            DrawPoint(DataInfos, graphics, ratio);
        }

        private void DrawPoint(List<LayerDataInfo> dataInfos, Graphics graphics, float ratio)
        {
            foreach (LayerDataInfo dataInfo in dataInfos)
            {
                if (dataInfo.DisplayLongitude == 0 && dataInfo.DisplayLatitude == 0)
                {
                    continue;
                }
                //画采样点
                DbPoint dPoint = new DbPoint(dataInfo.DisplayLongitude, dataInfo.DisplayLatitude);
                PointF point;
                gisAdapter.ToDisplay(dPoint, out point);
                //创建实心画笔
                SolidBrush brush;
                Color? color = getColorFrom(dataInfo);
                if (color == null)
                {
                    continue;
                }
                brush = new SolidBrush((Color)color);
                //设置原点
                graphics.TranslateTransform(point.X, point.Y);
                //设置缩放比例
                float radius = ratio * size;
                //x,y方向上的缩放比例(用于缩放地图时随地图大小进行缩放)
                graphics.ScaleTransform(radius, radius);
                //SymbolManager中有9个初始图形,对应0是半径为8的圆形
                GraphicsPath path = SymbolManager.GetInstance().Paths[0];
                //填充实心圆为采样点
                graphics.FillPath(brush, path);

                if (dataInfo.Selected)
                {
                    //如果选中绘制边框
                    graphics.DrawPath(penSelected, path);
                }
                graphics.ResetTransform();
            }
        }

        public string SerialInfoName { get; set; } = "";
        /// <summary>
        /// 根据value从ranges取颜色
        /// </summary>
        /// <param name="tpb"></param>
        /// <returns></returns>
        public Color? getColorFrom(LayerDataInfo tpb)
        {
            MapSerialInfo serial = DTLayerSerialManager.Instance.GetSerialByName(SerialInfoName);
            Color? color = serial.ColorDisplayParam.Info.GetColor(tpb.RenderingIndex);

            return color;
        }

#region Select
        public override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            Select(((MapForm.MapEventArgs)e).MapOp2);
        }

        public void Select(MapOperation2 mop2)
        {
            if (!IsVisible || DataInfos.Count <= 0)
            {
                return;
            }
            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(mapScale);

            LastSelectedData.Selected = false;
            foreach (LayerDataInfo data in SelectedPoints)
            {
                data.Selected = false;
            }
            SelectedPoints.Clear();

            //循环所有采样点,如果点击的坐标在某个采样点的范围内则记录为所选点
            foreach (LayerDataInfo data in DataInfos)
            {
                if (SelectedPoints.Count == 1)
                {
                    break;
                }
                DbPoint dPoint = new DbPoint(data.DisplayLongitude, data.DisplayLatitude);
                PointF point;
                gisAdapter.ToDisplay(dPoint, out point);
                //缩放比例
                float radius = (ratio * size);
                //8为SymbolManager中对应圆的半径,16是直径
                RectangleF rect = new RectangleF(point.X - radius * 8, point.Y - radius * 8, radius * 16, radius * 16);
                DbRect dRect;
                gisAdapter.FromDisplay(rect, out dRect);

                if (mop2.CheckCenterInDRect(dRect))
                {
                    data.Selected = true;
                    SelectedPoints.Add(data);
                    LastSelectedData = data;
                }
            }
            if (SelectedPointsChanged != null)
            {
                SelectedPointsChanged(this, EventArgs.Empty);
            }
        }
#endregion
    }

    public class LayerDataInfo
    {
        public double Longitude { get; set; } = 0;
        public double Latitude { get; set; } = 0;
        public float RenderingIndex { get; set; } = 0;
        public bool Selected { get; set; } = false;

        public double DisplayLongitude { get; set; } = 0;
        public double DisplayLatitude { get; set; } = 0;
    }
}
