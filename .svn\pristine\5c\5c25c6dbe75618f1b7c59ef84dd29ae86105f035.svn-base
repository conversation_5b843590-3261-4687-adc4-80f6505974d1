﻿using System;
using System.Collections.Generic;
using System.Text;
using Chris.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.NOP.RAMS.Extend.Command
{
    public delegate void FileInfoGotEventHandler(object sender, TaskFileInfoGotEventArgs e);

    public class CmdGetTaskFileInfo : CommandBasic
    {
        public static event FileInfoGotEventHandler FileInfoGot;
        public override bool Execute()
        {
            if (FileInfoGot == null)
            {
                return true;
            }
            FileInfo fi = new FileInfo();
            DateTime probBTime = DateTime.MinValue;
            DateTime probETime = DateTime.MinValue;
            if (this.Param.ContainsKey("DistrictID")
                && this.Param.ContainsKey("FileID")
                && this.Param.ContainsKey("LogTableName")
                && this.Param.ContainsKey("ServiceType")
                && this.Param.ContainsKey("ProbBeginTime")
                && this.Param.ContainsKey("ProbEndTime"))
            {
                bool error = false;
                try
                {
                    fi.DistrictID = int.Parse(this.Param["DistrictID"].ToString());
                    fi.ID = int.Parse(this.Param["FileID"].ToString());
                    fi.LogTable = this.Param["LogTableName"].ToString();
                    fi.ServiceType = int.Parse(this.Param["ServiceType"].ToString());
                    probBTime = (DateTime)this.Param["ProbBeginTime"];
                    probETime = (DateTime)this.Param["ProbEndTime"];
                }
                catch (Exception)
                {
                    error = true;
                    return false;
                }
                TaskFileInfoGotEventArgs evtArgs = new TaskFileInfoGotEventArgs();
                evtArgs.Error = error;
                evtArgs.FileInfo = fi;
                evtArgs.ProbBeginTime = probBTime;
                evtArgs.ProbEndTime = probETime;
                FileInfoGot(this, evtArgs);
            }

            return true;
            //ReportGridForm reportGridForm = this.Param.ContainsKey("ReportGridForm") ? (this.Param["ReportGridForm"] as ReportGridForm) : null;
            //string text = (!this.Param.ContainsKey("Title")) ? null : (this.Param["Title"] as string);
            //int? num = (!this.Param.ContainsKey("TitleParameterCount")) ? null : (this.Param["TitleParameterCount"] as int?);
            //List<object> list = new List<object>();
            //if (num.HasValue)
            //{
            //    for (int num2 = 1; num2 <= num; num2++)
            //    {
            //        object item = (!this.Param.ContainsKey("TitleParameter" + num2)) ? null : this.Param["TitleParameter" + num2];
            //        list.Add(item);
            //    }
            //}
            //int? num3 = this.Param.ContainsKey("ShowType") ? (this.Param["ShowType"] as int?) : null;
            //StringBuilder stringBuilder = new StringBuilder();
            //stringBuilder.Append(this.Param.ContainsKey("SQL") ? (this.Param["SQL"] as string) : null);
            //int? num4 = (!this.Param.ContainsKey("SQLParameterCount")) ? null : (this.Param["SQLParameterCount"] as int?);
            //List<object> list2 = new List<object>();
            //if (num4.HasValue)
            //{
            //    for (int num5 = 1; num5 <= num4; num5++)
            //    {
            //        object item2 = this.Param.ContainsKey("SQLParameter" + num5) ? this.Param["SQLParameter" + num5] : null;
            //        list2.Add(item2);
            //    }
            //}
            //if (stringBuilder.Length != 0)
            //{
            //    for (int i = 1; i <= list2.Count; i++)
            //    {
            //        stringBuilder.Replace("#" + i, DBHelper.GetFieldString(list2[i - 1]));
            //    }
            //}
            //IDataSetAdapter dataSetAdapter = new DataSetNetAdapter(mainModel.Server.Hostname, mainModel.Server.Port, mainModel.Server.Hostname, mainModel.Server.Password);
            //ResultSet resultSet = dataSetAdapter.ExecuteReader(stringBuilder.ToString());
            //if (resultSet != null && resultSet.Rows.Count > 0)
            //{
            //    if (num3.HasValue && reportGridForm != null)
            //    {
            //        if (text != null)
            //        {
            //            for (int j = 1; j <= list.Count; j++)
            //            {
            //                text = text.Replace("#" + j, string.Concat(list[j - 1]));
            //            }
            //        }
            //        reportGridForm.ShowRelativeData(1, resultSet, text, this.Param);
            //    }
            //    return true;
            //}
            //XtraMessageBox.Show("没有找到相应的记录!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
            //return false;
        }
    }

    public class TaskFileInfoGotEventArgs : EventArgs
    {
        public FileInfo FileInfo = null;
        public DateTime ProbBeginTime;
        public DateTime ProbEndTime;
        public bool Error = false;
    }

}
