﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatByCells : QueryKPIStatBase
    {
        public QueryKPIStatByCells()
            : base()
        { }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.cell;
        }

        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.KPI_CELL;
                package.Content.PrepareAddParam();
            }
        }

        /// <summary>
        /// RequestType.KPI_CELL，需要一个结束标记。
        /// </summary>
        /// <param name="package"></param>
        /// <param name="reservedParams"></param>
        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            AddDIYEndOpFlag(package);
        }
        
        public List<ICell> Cells { get; set; }

        Dictionary<string, ICell> cellDic = null;
        protected override bool getConditionBeforeQuery()
        {
            List<ICell> cells = Cells;
            if (cells != null)
            {
                //
            }
            else
            {
                cells = new List<ICell>();
            }
            cellDic = new Dictionary<string, ICell>();
            foreach (ICell cell in cells)
            {
                cellDic[cell.Token] = cell;
            }

            return base.getConditionBeforeQuery();
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                KPIStatDataBase singleStatData = null;
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    double lac = package.Content.GetParamInt();
                    double ci = package.Content.GetParamInt();
                    ICell cell = null;
                    string cellKey = string.Format("{0}_{1}", lac, ci);
                    if (!cellDic.TryGetValue(cellKey,out cell))
                    {
                        continue;
                    }
                    fillStatData(package, curImgColumnDef, singleStatData);
                    int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
                    MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
                    KpiDataManager.AddStatData(string.Empty, cell, fi, singleStatData, false);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    System.Diagnostics.Debug.Assert(false, package.Content.Type.ToString());
                    break;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

    }
}
