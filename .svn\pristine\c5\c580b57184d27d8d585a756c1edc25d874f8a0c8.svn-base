﻿using System;
using System.Collections.Generic;
using System.Text;

using System.Data;
using System.Data.Sql;
using System.Data.SqlClient;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class CellArgumentManager
    {
        public static CellArgumentManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new CellArgumentManager();
                }
                return instance;
            }
        }

        public string SqlConnectionString
        {
            get;
            private set;
        }

        public void FireLoadCellArguments(int cityID, bool isForce)
        {
            if (curCityID == cityID && !isForce)
            {
                return;
            }
            curCityID = cityID;

            recordDic.Clear();
            SqlConnection sqlConn = null;
            SqlCommand cmd = null;
            SqlDataReader reader = null;
            try
            {
                sqlConn = new SqlConnection(this.SqlConnectionString);
                sqlConn.Open();

                string sql = "SELECT * from [tb_association_argument] where [地市ID] = " + curCityID;
                cmd = new SqlCommand(sql, sqlConn);
                reader = cmd.ExecuteReader();
                while (reader.Read())
                {
                    CellArgumentRecord record = new CellArgumentRecord();
                    record.FillData(reader);

                    if (!recordDic.ContainsKey(record.CellName))
                    {
                        recordDic.Add(record.CellName, record);
                    }
                    else
                    {
                        recordDic[record.CellName].MergeRecord(record);
                    }
                }
                this.logger.Info(string.Format("Load {0} CellArguments.", recordDic.Count));
            }
            catch (System.Exception ex)
            {
                this.logger.Error("Load CellArguments Error", ex);
            }
            finally
            {
                if (sqlConn != null) sqlConn.Dispose();
                if (cmd != null) cmd.Dispose();
                else if (reader != null) cmd.Dispose();
            }
        }

        public CellArgumentRecord GetCellArgument(string cellName)
        {
            CellArgumentRecord record = null;
            recordDic.TryGetValue(cellName, out record);
            return record;
        }

        private CellArgumentManager()
        {
            CellArgumentDbSetting dbSetting = new CellArgumentDbSetting();
            this.SqlConnectionString = dbSetting.ConnectionString;
        }

        private int curCityID = -1;

        private readonly Dictionary<string, CellArgumentRecord> recordDic = new Dictionary<string, CellArgumentRecord>();

        private readonly log4net.ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private static CellArgumentManager instance;
    }

    public class CellArgumentRecord
    {
        public int CityID
        {
            get;
            set;
        }

        public string CellID
        {
            get;
            set;
        }

        public string CellName
        {
            get;
            set;
        }

        public string ENodeBID
        {
            get;
            set;
        }

        public string ENodeBName
        {
            get;
            set;
        }

        public int? Earfcn
        {
            get;
            set;
        }

        public int? Pci
        {
            get;
            set;
        }

        public float? RefSignalPower
        {
            get;
            set;
        }

        public float? Pa
        {
            get;
            set;
        }

        public float? Pb
        {
            get;
            set;
        }

        /// <summary>
        /// 小区偏移量
        /// </summary>
        public int? CellOcn
        {
            get;
            set;
        }

        /// <summary>
        /// 小区偏置
        /// </summary>
        public int? CellOcs
        {
            get;
            set;
        }

        public List<string> NbCells
        {
            get;
            set;
        }

        public CellArgumentRecord()
        {
            NbCells = new List<string>();
        }

        public void MergeRecord(CellArgumentRecord record)
        {
            if (record.CellID != this.CellID)
            {
                return;
            }

            CityID = record.CityID;
            if (record.CellName != null) CellName = record.CellName;
            if (record.ENodeBID != null) ENodeBID = record.ENodeBID;
            if (record.ENodeBName != null) ENodeBName = record.ENodeBName;
            if (record.Pci != null) Pci = record.Pci;
            if (record.Earfcn != null) Earfcn = record.Earfcn;
            if (record.RefSignalPower != null) RefSignalPower = record.RefSignalPower;
            if (record.Pa != null) Pa = record.Pa;
            if (record.Pb != null) Pb = record.Pb;
            if (record.CellOcn != null) CellOcn = record.CellOcn;
            foreach (string nbCell in record.NbCells)
            {
                if (!this.NbCells.Contains(nbCell))
                {
                    this.NbCells.Add(nbCell);
                }
            }
        }

        public void FillData(SqlDataReader reader)
        {
            CityID = (int)reader["地市ID"];
            CellID = (string)reader["小区标识"];
            CellName = (string)reader["小区名称"];
            ENodeBID = (string)reader["eNodeB标识"];
            ENodeBName = (string)reader["eNodeB名称"];
            Earfcn = Convert.IsDBNull(reader["频点"]) ? null : (int?)reader["频点"];
            Pci = Convert.IsDBNull(reader["物理小区识别码"]) ? null : (int?)reader["物理小区识别码"];
            RefSignalPower = Convert.IsDBNull(reader["参考信号功率"]) ? null : (float?)(double)reader["参考信号功率"];
            Pa = Convert.IsDBNull(reader["PA"]) ? null : (float?)(double)reader["PA"];
            Pb = Convert.IsDBNull(reader["PB"]) ? null : (float?)(double)reader["PB"];
            CellOcn = Convert.IsDBNull(reader["小区偏移量"]) ? null : (int?)reader["小区偏移量"];
            CellOcs = Convert.IsDBNull(reader["小区偏置"]) ? null : (int?)reader["小区偏置"];

            if (!Convert.IsDBNull(reader["邻区列表"]))
            {
                NbCells.AddRange(reader["邻区列表"].ToString().Split(','));
            }
        }
    }

    public class CellArgumentDbSetting : AlarmDbSetting
    {
    }
}
