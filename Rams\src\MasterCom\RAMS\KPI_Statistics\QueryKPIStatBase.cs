﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using DBDataViewer;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public abstract class QueryKPIStatBase : QueryBase
    {
        public KPIDataManager KpiDataManager { get; set; }
        protected QueryKPIStatBase() : base(MainModel.GetInstance()) { }
        protected QueryKPIStatBase(MainModel mm)
            : base(mm)
        { }
        protected abstract StatTbToken getTableNameToken();
        protected ReportStyle curReportStyle = null;
        protected bool isQueryAllParams = false;

        public override string IconName
        {
            get { return null; }
        }

        protected virtual void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }

                clientProxy.Recieve();
                package.Content.PrepareGetParam();//准备获得参数
                KPIStatDataBase singleStatData = null;
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    recieveAndHandleSpecificStatData(package, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END ||
                    package.Content.Type == ResponseType.RESTYPE_SEARCHERROR)
                {
                    break;
                }
                else
                {
                    break;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

        /// <summary>
        /// 接收填充统数据，并处理
        /// 不同接口，接收略有不同
        /// </summary>
        /// <param name="statData">统计数据</param>
        protected virtual void recieveAndHandleSpecificStatData(Package package
            , List<StatImgDefItem> curImgColumnDef
            , KPIStatDataBase singleStatData)
        {
            package.Content.GetParamDouble();//lng
            package.Content.GetParamDouble();//lat
            fillStatData(package, curImgColumnDef, singleStatData);
            //do someting after filldata
        }

        protected virtual void recieveInfo_Event(ClientProxy clientProxy, params object[] reservedParams)
        {
            DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> colDefSet = new List<ColumnDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, colDefSet);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (isColDefContent(package, colDefSet))
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT
                   || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_GSM
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_TDSCDMA
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_WCDMA
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA2000
                || package.Content.Type == ResponseType.AREASTAT_KPI_EVENT_LTE
                || package.Content.Type == ResponseType.AREASTAT_KPI_EVENT_LTE_FDD
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_NR)
                {
                    NREventHelper.ReSetIntCI(colDefSet);
                    fillData(colDefSet, package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT_NR)
                {
                    NREventHelper.SetLongCI(colDefSet);
                    fillData(colDefSet, package);
                }
                else if (package.Content.Type == ResponseType.END ||
                    package.Content.Type == ResponseType.RESTYPE_SEARCHERROR)
                {
                    break;
                }
                else
                {
                    System.Diagnostics.Debug.Assert(false, package.Content.Type.ToString());
                    break;
                }

                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

        protected virtual void fillData(List<ColumnDefItem> colDefSet, Package package)
        {
            Event evt = Event.Create(package.Content, colDefSet);
            if (isQueryAllParams || this.evtIDSvrIDDic.ContainsKey(evt.ID))
            {
                handleStatEvent(evt);
            }
        }

        protected bool needSeparateByServiceID(Event evt)
        {
            Dictionary<int, bool> serviceTypeDic = null;
            if (this.evtIDSvrIDDic.TryGetValue(evt.ID, out serviceTypeDic) && serviceTypeDic != null)
            {
                return serviceTypeDic.ContainsKey(evt.ServiceType);
            }
            return false;
        }
        protected string needSeparateByFileName(Event evt)
        {
            string fileNameKey = "";
            Dictionary<string, bool> fileNameDic = null;
            if (this.evtIDFileNameDic != null && this.evtIDFileNameDic.TryGetValue(evt.ID, out fileNameDic))
            {
                fileNameKey = getFileNameKey(evt.FileName, fileNameDic);
            }
            return fileNameKey;
        }

        protected string getFileNameKey(string fileName, Dictionary<string, bool> fileNameDic)
        {
            string fileNameKey = "";
            if (!string.IsNullOrEmpty(fileName) && fileNameDic != null)
            {//该指标需要根据文件名关键字分开统计
                foreach (string strKey in fileNameDic.Keys)
                {
                    string[] strKeyArray = strKey.Replace(" AND ", QueryCondition.Splitor).Split(new string[] { QueryCondition.Splitor }, StringSplitOptions.RemoveEmptyEntries);

                    bool isAllValid = false;
                    foreach (string strNameKey in strKeyArray)
                    {
                        if (fileName.Contains(strNameKey))
                        {
                            isAllValid = true;
                        }
                        else
                        {
                            isAllValid = false;
                            break;
                        }
                    }
                    if (isAllValid)
                    {
                        fileNameKey = strKey;
                        break;
                    }
                }
            }
            return fileNameKey;
        }
        protected virtual void handleStatEvent(Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            Console.Write(eventData);
        }

        protected virtual bool isImgColDefContent(Package package, List<StatImgDefItem> imgColDefSet)
        {
            if (package.Content.Type == ResponseType.COLUMN_DEFINE)
            {
                imgColDefSet.Clear();
                parseToCurImgColumnDef(package.Content.GetParamString(), imgColDefSet);
                return true;
            }
            else
            {
                return false;
            }
        }

        protected virtual bool isKPIDataContent(Package package, out KPIStatDataBase statData)
        {
            statData = null;
            switch (package.Content.Type)
            {
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID:
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_GPRS:
                case ResponseType.RESTYPE_DIY_LOG_KPI_GSM:
                case ResponseType.RESTYPE_DIY_LOG_KPI_GPRS:
                    statData = new StatDataGSM();
                    break;
                case ResponseType.KPI_LTE_AMR:
                case ResponseType.RESTYPE_DIY_LOG_KPI_LTE_AMR:
                    statData = new StatDataLTE();
                    break;
                case ResponseType.RESTYPE_DIY_LOG_KPI_LTE_FDD_AMR:
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_LTE_FDD_AMR:
                    statData = new StatDataLTE_FDD();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_AMR:
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_PS:
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_VP:
                case ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_AMR:
                case ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_PS:
                case ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_VP:
                    statData = new StatDataTD();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_AMR:
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PS:
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_VP:
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PSHS:
                case ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_AMR:
                case ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PS:
                case ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_VP:
                case ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PSHS:
                    statData = new StatDataWCDMA();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_CDMA_V:
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_CDMA_D:
                case ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_V:
                case ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_D:
                    statData = new StatDataCDMA_Voice();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_CDMA2000_D:
                case ResponseType.RESTYPE_DIY_LOG_KPI_CDMA2000_D:
                    statData = new StatDataCDMA_EVDO();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_GSM:
                //case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_DTGSM: // - KPI_LTE_AMR 冲突 RESTYPE_DIY_AREA_COVER_GRID_LTE_AMR(重新定义返回值)
                case ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_GSM:
                    statData = new StatDataSCAN_GSM();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_LTE_TOPN:
                case ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_LTETOPN:
                case ResponseType.RESTYPE_DIY_LOG_KPI_LTE_FREQSPECTRUM:
                    statData = new StatDataSCAN_LTE();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_TDSCDMA:
                case ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_TD:
                    statData = new StatDataSCAN_TD();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_WCDMA:
                    //case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_DTWCDMA: // - RESTYPE_DIY_AREA_COVER_GRID_CDMA2000_D 冲突(重新定义返回值)
                    statData = new StatDataSCAN_WCDMA();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_CDMA:
                    statData = new StatDataSCAN_CDMA();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_GSM_MTR:
                case ResponseType.RESTYPE_DIY_LOG_KPI_GSM_MTR:
                    statData = new StatDataGSM_MTR();
                    break;
                case ResponseType.RESTYPE_DIY_LOG_KPI_WLAN:
                    statData = new StatDataWLAN();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_LTE_SIGNAL:
                    statData = new StatDataLTE_Signal();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_NBIOT_TOPN:
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_SCAN_NBIOT_TOPN:
                    statData = new StatDataSCAN_NBIOT();
                    break;
                case ResponseType.RESTYPE_DIY_LOG_KPI_NR_AMR:
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_NR:
                    statData = new StatDataNR();
                    break;
                case ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_NR:
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_NR:
                case ResponseType.RESTYPE_DIY_LOG_KPI_NR_FREQSPECTRUM:
                    statData = new StatDataSCAN_NR();
                    break;
            }
            return statData != null;
        }

        protected void fillStatData(Package package, List<StatImgDefItem> imgColDefSet
            , KPIStatDataBase statData)
        {
            FileInfo fi = null;
            foreach (StatImgDefItem cdf in imgColDefSet)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> statInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (KeyValuePair<string, DataItem> pair in statInfoDic)
                {
                    fi = setStatData(statData, fi, cdf, pair);
                }
            }
        }

        private FileInfo setStatData(KPIStatDataBase statData, FileInfo fi, StatImgDefItem cdf, KeyValuePair<string, DataItem> pair)
        {
            Dictionary<int, bool> serviceIDDic = null;
            Dictionary<string, bool> fileNameDic = null;

            bool needSvrIdSave = true;
            if (imgCodeSvrIDDic != null)
            {
                needSvrIdSave = imgCodeSvrIDDic.TryGetValue(pair.Key, out serviceIDDic);
            }
            bool needFileNameSave = true;
            if (imgCodeFileNameDic != null)
            {
                needFileNameSave = imgCodeFileNameDic.TryGetValue(pair.Key, out fileNameDic);
            }

            if (needSvrIdSave || needFileNameSave || cdf.imgID == 1 || isQueryAllParams)
            {//image1保存的是基础指标（文件信息，距离，点数等）
                if (fi == null)
                {//默认iamge1第一个指标为FileID
                    int fileID = int.Parse(pair.Value.Value.ToString());
                    fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
                }
                if (fi != null)
                {
                    int svId = -1;//默认不区分业务类型
                    if (serviceIDDic != null && serviceIDDic.ContainsKey(fi.ServiceType))
                    {//该指标需要根据业务类分开统计
                        svId = fi.ServiceType;
                    }
                    string fileNameKey = getFileNameKey(fi.Name, fileNameDic);

                    statData[pair.Key, svId, fileNameKey] = double.Parse(pair.Value.Value.ToString());
                }
            }

            return fi;
        }

        protected virtual void fireShowResult()//显示结果
        {
            if (KpiDataManager == null)
            {
                return;
            }
            ActionCreateChildFrame action = new ActionCreateChildFrame();//创建新窗体
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = typeof(KPIReportMainForm).FullName;
            actionParam["Text"] = "统计结果";
            actionParam["ImageFilePath"] = @"images\stat.gif";
            action.Param = actionParam;
            action.OnAction();//初始化窗体
            KPIReportMainForm statForm = action.CreatedForm as KPIReportMainForm;
            statForm.LogItemSrc = this.getRecLogItem();
            statForm.ShowReport(KPIReportManager.Instance.Reports, curReportStyle, KpiDataManager.GetReportGroupDataSet());//获得结果集并显示
            KpiDataManager = null;
            //GC.Collect();
        }

        public override string Name
        {
            get { return "KPI统计查询基类"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected virtual bool getConditionBeforeQuery()//弹出列表选择框
        {
            ReportPickerDlg dlg = new ReportPickerDlg();
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curReportStyle = dlg.Report;
            isQueryAllParams = dlg.IsQueryAllParams;
            KpiDataManager = new KPIDataManager();
            return true;
        }

        public bool IsShowResultForm { get; set; } = true;
        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            foreach (int districtID in condition.DistrictIDs)//根据地市id找文件
            {
                condition.DistrictID = districtID;
                queryDistrictData(districtID);
            }
            afterRecieveAllData();
            if (IsShowResultForm)
            {
                fireShowResult();
            }
        }

        protected virtual void queryDistrictData(int districtID)
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected virtual void queryInThread(object o)
        {
            try
            {
                System.Threading.Thread.Sleep(100);
                WaitBox.Text = "开始查询KPI统计数据...";
                WaitBox.CanCancel = true;
                ClientProxy clientProxy = (ClientProxy)o;

                string imgTriadIDSet = getStatImgNeededTriadID();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(null, clientProxy, imgTriadIDSet);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        WaitBox.Text = "正在统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = 10;
                        queryPeriodInfo(period, clientProxy, imgTriadIDSet);
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        protected virtual void afterRecieveAllData(params object[] reservedParams)
        {
            if (KpiDataManager != null)
            {
                KpiDataManager.FinalMtMoStatData();
            }
        }

        protected bool isQueringEvent = false;
        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_GRID;
                package.Content.PrepareAddParam();
            }
        }

        protected virtual void preparePackageNeededInfo_StatImg(Package package, params object[] paramSet)
        {
            if (paramSet.Length > 0)
            {
                string triadIDSet = paramSet[0] as string;
                package.Content.AddParam(triadIDSet);
            }
        }

        protected Dictionary<string, Dictionary<int, bool>> imgCodeSvrIDDic = null;
        protected Dictionary<int, Dictionary<int, bool>> evtIDSvrIDDic = null;

        protected Dictionary<string, Dictionary<string, bool>> imgCodeFileNameDic = null;
        protected Dictionary<int, Dictionary<string, bool>> evtIDFileNameDic = null;

        protected Dictionary<string, bool> extractTriadID(string exp, List<int> serviceIDSet
            , ref Dictionary<string, Dictionary<int, bool>> imgCodeSvrIDDic
            , ref Dictionary<int, Dictionary<int, bool>> evtIDSvrIDDic,
            List<string> fileNameKeyList, ref Dictionary<string, Dictionary<string, bool>> imgCodeFileNameDic
            , ref Dictionary<int, Dictionary<string, bool>> evtIDFileNameDic)
        {
            if (string.IsNullOrEmpty(exp))
            {
                return new Dictionary<string, bool>();
            }
            if (imgCodeSvrIDDic == null)
            {
                imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            }
            if (evtIDSvrIDDic == null)
            {
                evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            }

            if (imgCodeFileNameDic == null)
            {
                imgCodeFileNameDic = new Dictionary<string, Dictionary<string, bool>>();
            }
            if (evtIDFileNameDic == null)
            {
                evtIDFileNameDic = new Dictionary<int, Dictionary<string, bool>>();
            }
            Dictionary<string, bool> triadIDDic = new Dictionary<string, bool>();
            Dictionary<string, bool> imgCodeDic;
            Dictionary<int, bool> eventIDDic;
            Dictionary<string, bool> tIDDic = KPIFormulaManager.ExtractTriadID(getTableNameToken()
                , exp, out imgCodeDic, out eventIDDic);

            foreach (string id in tIDDic.Keys)
            {
                triadIDDic[id] = true;
            }

            //只保留报表包含的指标与事件
            foreach (string imgCode in imgCodeDic.Keys)
            {
                addImgCodeSvrIDDic(serviceIDSet, imgCodeSvrIDDic, imgCode);

                addImgCodeFileNameDic(fileNameKeyList, imgCodeFileNameDic, imgCode);
            }

            foreach (int evtID in eventIDDic.Keys)
            {
                addEvtIDSvrIDDic(serviceIDSet, evtIDSvrIDDic, evtID);

                addEvtIDFileNameDic(fileNameKeyList, evtIDFileNameDic, evtID);
            }
            return triadIDDic;
        }

        private void addImgCodeSvrIDDic(List<int> serviceIDSet, Dictionary<string, Dictionary<int, bool>> imgCodeSvrIDDic, string imgCode)
        {
            Dictionary<int, bool> svrIDDic;
            if (!imgCodeSvrIDDic.TryGetValue(imgCode, out svrIDDic))
            {
                imgCodeSvrIDDic[imgCode] = null;//默认不按业务区分指标
            }
            if (serviceIDSet != null
                && serviceIDSet.Count > 0)
            {
                if (svrIDDic == null)
                {
                    svrIDDic = new Dictionary<int, bool>();
                    imgCodeSvrIDDic[imgCode] = svrIDDic;
                }
                foreach (int id in serviceIDSet)
                {
                    svrIDDic[id] = true;
                }
            }
        }

        private void addImgCodeFileNameDic(List<string> fileNameKeyList, Dictionary<string, Dictionary<string, bool>> imgCodeFileNameDic, string imgCode)
        {
            Dictionary<string, bool> fileNameDic;
            if (!imgCodeFileNameDic.TryGetValue(imgCode, out fileNameDic))
            {
                imgCodeFileNameDic[imgCode] = null;//默认不按文件名关键字分指标
            }
            if (fileNameKeyList != null
                && fileNameKeyList.Count > 0)
            {
                if (fileNameDic == null)
                {
                    fileNameDic = new Dictionary<string, bool>();
                    imgCodeFileNameDic[imgCode] = fileNameDic;
                }
                foreach (string fileName in fileNameKeyList)
                {
                    fileNameDic[fileName] = true;
                }
            }
        }

        private void addEvtIDSvrIDDic(List<int> serviceIDSet, Dictionary<int, Dictionary<int, bool>> evtIDSvrIDDic, int evtID)
        {
            Dictionary<int, bool> svrIDDic;
            if (!evtIDSvrIDDic.TryGetValue(evtID, out svrIDDic))
            {
                evtIDSvrIDDic[evtID] = null;//默认不按业务区分指标
            }
            if (serviceIDSet != null
                && serviceIDSet.Count > 0)
            {
                if (svrIDDic == null)
                {
                    svrIDDic = new Dictionary<int, bool>();
                    evtIDSvrIDDic[evtID] = svrIDDic;
                }
                foreach (int id in serviceIDSet)
                {
                    svrIDDic[id] = true;
                }
            }
        }

        private void addEvtIDFileNameDic(List<string> fileNameKeyList, Dictionary<int, Dictionary<string, bool>> evtIDFileNameDic, int evtID)
        {
            Dictionary<string, bool> fileNameDic;
            if (!evtIDFileNameDic.TryGetValue(evtID, out fileNameDic))
            {
                evtIDFileNameDic[evtID] = null;//默认不按文件名关键字分指标
            }
            if (fileNameKeyList != null
                && fileNameKeyList.Count > 0)
            {
                if (fileNameDic == null)
                {
                    fileNameDic = new Dictionary<string, bool>();
                    evtIDFileNameDic[evtID] = fileNameDic;
                }
                foreach (string fileName in fileNameKeyList)
                {
                    fileNameDic[fileName] = true;
                }
            }
        }

        protected virtual string getStatImgNeededTriadID(params object[] paramSet)//解析获取需要的三元组
        {
            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            this.imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            this.evtIDFileNameDic = new Dictionary<int, Dictionary<string, bool>>();
            this.imgCodeFileNameDic = new Dictionary<string, Dictionary<string, bool>>();
            if (isQueryAllParams)
            {
                return "-1,-1,-1";
            }


            Dictionary<string, bool> triadIDDic = new Dictionary<string, bool>();

            foreach (RptCell cell in curReportStyle.rptCellList)
            {
                Dictionary<string, bool> tempDic = extractTriadID(cell.exp
                    , cell.ServiceIDSet, ref this.imgCodeSvrIDDic, ref this.evtIDSvrIDDic
                    , cell.FileNameKeyValueList, ref this.imgCodeFileNameDic, ref this.evtIDFileNameDic);
                if (tempDic != null)
                {
                    foreach (string id in tempDic.Keys)
                    {
                        triadIDDic[id] = true;
                    }
                }
            }

            StringBuilder sb = new StringBuilder();
            foreach (string triadID in triadIDDic.Keys)
            {
                sb.Append(triadID);
                sb.Append(",");
            }
            if (sb.Length > 0)
            {//remove last ","
                sb = sb.Remove(sb.Length - 1, 1);
            }
            return sb.ToString();
        }

        /// <summary>
        /// 不限制业务类型，获取三元组ID
        /// </summary>
        /// <param name="formulaSet"></param>
        /// <returns></returns>
        protected string getTriadIDIgnoreServiceType(IEnumerable<string> formulaSet)
        {
            this.imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            this.evtIDFileNameDic = new Dictionary<int, Dictionary<string, bool>>();
            this.imgCodeFileNameDic = new Dictionary<string, Dictionary<string, bool>>();
            Dictionary<string, bool> triadIDDic = new Dictionary<string, bool>();

            foreach (string exp in formulaSet)
            {
                Dictionary<string, bool> tempDic = extractTriadID(exp
                    , null, ref this.imgCodeSvrIDDic, ref this.evtIDSvrIDDic
                    , null, ref this.imgCodeFileNameDic, ref this.evtIDFileNameDic);
                if (tempDic != null)
                {
                    foreach (string id in tempDic.Keys)
                    {
                        triadIDDic[id] = true;
                    }
                }
            }

            StringBuilder sb = new StringBuilder();
            foreach (string triadID in triadIDDic.Keys)
            {
                sb.Append(triadID);
                sb.Append(",");
            }
            if (sb.Length > 0)
            {//remove last ","
                sb = sb.Remove(sb.Length - 1, 1);
            }
            return sb.ToString();
        }

        /// <summary>
        /// 查询某时间段内的数据
        /// </summary>
        /// <param name="period">当该参数为null时，视为按轮查询</param>
        /// <param name="clientProxy"></param>
        /// <param name="package"></param>
        protected virtual void queryPeriodInfo(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            //stat
            isQueringEvent = false;
            preparePackageBasicContent(clientProxy.Package, period, reservedParams);
            preparePackageNeededInfo_StatImg(clientProxy.Package, reservedParams);
            clientProxy.Send();
            recieveInfo_ImgGrid(clientProxy);

            if (isQueryAllParams || this.evtIDSvrIDDic.Count > 0)
            {
                //event
                isQueringEvent = true;
                preparePackageBasicContent(clientProxy.Package, period, reservedParams);
                preparePackageNeededInfo_Event(clientProxy.Package);
                clientProxy.Send();
                recieveInfo_Event(clientProxy);
            }
            afterRecieveOnePeriodData(period);
        }

        protected override void AddGeographicFilter(Package package)
        {
            if (isQueringEvent)
            {
                AddDIYEndOpFlag(package);
            }
        }

        protected virtual void preparePackageNeededInfo_Event(Package package)
        {
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,1,43,");
            sbuilder.Append("0,2,43,");
            sbuilder.Append("0,3,43,");
            sbuilder.Append("0,4,43,");
            sbuilder.Append("0,5,43,");
            sbuilder.Append("0,6,43,");
            sbuilder.Append("0,7,43,");
            sbuilder.Append("0,8,43,");
            sbuilder.Append("0,9,43,");
            sbuilder.Append("0,10,43,");
            sbuilder.Append("0,11,43,");
            sbuilder.Append("0,12,43,");
            sbuilder.Append("0,13,43,");
            sbuilder.Append("0,14,43,");
            sbuilder.Append("0,15,43,");
            sbuilder.Append("0,16,43,");
            sbuilder.Append("0,17,43,");
            sbuilder.Append("0,18,43,");
            sbuilder.Append("0,19,43,");
            sbuilder.Append("0,20,43,");
            sbuilder.Append("0,21,43,");
            sbuilder.Append("0,22,43,");
            sbuilder.Append("0,23,43,");
            sbuilder.Append("0,24,43,");
            sbuilder.Append("0,25,43,");
            sbuilder.Append("0,26,43");
            package.Content.AddParam(sbuilder.ToString());
        }

        protected virtual void afterRecieveOnePeriodData(params object[] reservedParams)
        {

        }
    }
}
