﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.MTGis
{
    public class AtomicPolygon
    {
        public DbRect Bounds
        {
            get;
            private set;
        }

        public IList<DbPoint> Vertexes
        {
            get;
            private set;
        }

        public AtomicPolygon(IList<DbPoint> vertexes)
        {
            if (vertexes == null)
            {
                throw new ArgumentNullException("vertexes", "多边形顶点为空");
            }
            else if (vertexes.Count < 3)
            {
                throw new ArgumentException("多边形顶点数不能少于3点");
            }
            init(vertexes);
        }

        private void init(IList<DbPoint> vertexes)
        {
            this.Vertexes = vertexes;
            double xMin = double.MaxValue;
            double xMax = double.MinValue;
            double yMin = double.MaxValue;
            double yMax = double.MinValue;
            foreach (DbPoint p in vertexes)
            {
                xMin = Math.Min(xMin, p.x);
                xMax = Math.Max(xMax, p.x);
                yMin = Math.Min(yMin, p.y);
                yMax = Math.Max(yMax, p.y);
            }
            Bounds = new DbRect(xMin, yMin, xMax, yMax);
        }

        public AtomicPolygon(MapWinGIS.Shape shape)
        {
            if (shape.NumParts>1)
            {
                throw new ArgumentException("参数 MapWinGIS.Shape shape 不能为MultiPolygon!");
            }
            DbPoint[] arr = new DbPoint[shape.numPoints];
            for (int i = 0; i < shape.numPoints; i++)
            {
                MapWinGIS.Point p = shape.get_Point(i);
                arr[i] = new DbPoint(p.x, p.y);
            }
            init(arr);
        }

        public bool IsPointInOrOnPolygon(double x, double y)
        {
            if (!Bounds.IsPointInThisRect(x, y))
            {
                return false;
            }

            //See "The Point in Polygon Problem for Arbitrary Polygons" by Hormann & Agathos
            //http://citeseerx.ist.psu.edu/viewdoc/download?doi=*********.5498&rep=rep1&type=pdf
            int intResult = 0;//0在多边形外，1在多边形里面，-1在多边形边界上
            int cnt = Vertexes.Count;
            DbPoint pt1 = Vertexes[0];
            for (int i = 1; i <= cnt; ++i)
            {
                DbPoint ptNext = (i == cnt ? Vertexes[0] : Vertexes[i]);
                if (ptNext.y == y && ((ptNext.x == x) || (pt1.y == y && ((ptNext.x > x) == (pt1.x < x)))))
                {
                    return true;
                }

                if ((pt1.y < y) != (ptNext.y < y))
                {
                    bool isInRegion = judgeInRegion(x, y, ref intResult, pt1, ptNext);
                    if (isInRegion)
                    {
                        return true;
                    }
                }
                pt1 = ptNext;
            }
            return intResult != 0;
        }

        private bool judgeInRegion(double x, double y, ref int intResult, DbPoint pt1, DbPoint ptNext)
        {
            if (pt1.x >= x)
            {
                if (ptNext.x > x)
                {
                    intResult = 1 - intResult;
                }
                else
                {
                    bool res = getResult(x, y, ref intResult, pt1, ptNext);
                    if (res)
                    {
                        return true;
                    }
                }
            }
            else
            {
                if (ptNext.x > x)
                {
                    bool res = getResult(x, y, ref intResult, pt1, ptNext);
                    if (res)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private bool getResult(double x, double y, ref int intResult, DbPoint pt1, DbPoint ptNext)
        {
            double d = (pt1.x - x) * (ptNext.y - y) - (ptNext.x - x) * (pt1.y - y);
            if (d == 0)
            {
                return true;
            }
            else if ((d > 0) == (ptNext.y > pt1.y))
            {
                intResult = 1 - intResult;
            }
            return false;
        }

        public bool LineIntersects(DbPoint p1, DbPoint p2)
        {
            int i, j = Vertexes.Count - 1;
            for (i = 0; i < Vertexes.Count; i++)
            {
                if (intersect(Vertexes[i], Vertexes[j], p1, p2))
                {
                    return true;
                }
            }
            return false;
        }


        bool onsegment(DbPoint pi, DbPoint pj, DbPoint pk) //判断点pk是否在线段pi pj上   
        {
            if (Math.Min(pi.x, pj.x) <= pk.x && pk.x <= Math.Max(pi.x, pj.x)
                && Math.Min(pi.y, pj.y) <= pk.y && pk.y <= Math.Max(pi.y, pj.y))
            {
                return true;
            }
            return false;
        }
        double direction(DbPoint pi, DbPoint pj, DbPoint pk) //计算向量pkpi和向量pjpi的叉积   
        {
            return (pi.x - pk.x) * (pi.y - pj.y) - (pi.y - pk.y) * (pi.x - pj.x);
        }

        bool intersect(DbPoint p1, DbPoint p2, DbPoint p3, DbPoint p4) //判断线段p1p2和p3p4是否相交   
        {
            double d1 = direction(p3, p4, p1);
            double d2 = direction(p3, p4, p2);
            double d3 = direction(p1, p2, p3);
            double d4 = direction(p1, p2, p4);
            if (d1 * d2 < 0 && d3 * d4 < 0)
                return true;
            if (d1 == 0 && onsegment(p3, p4, p1))
                return true;
            if (d2 == 0 && onsegment(p3, p4, p2))
                return true;
            if (d3 == 0 && onsegment(p1, p2, p3))
                return true;
            if (d4 == 0 && onsegment(p1, p2, p4))
                return true;
            return false;
        }

    }
}
