﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.Interface
{
    enum SearchConditionType
    {
        SearchConditionType_AreaSelectIntersect = 1,
        SearchConditionType_AreaSelectInside,
        SearchConditionType_AreaSelectSample,
        SearchConditionType_MaxThan,
        SearchConditionType_<PERSON>Than,
        SearchConditionType_Between,
        SearchConditionType_InSelect,

        SearchConditionType_EndFlag = 255
    };

}
