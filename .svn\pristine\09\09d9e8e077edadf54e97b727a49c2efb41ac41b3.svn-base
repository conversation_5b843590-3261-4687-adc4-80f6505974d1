﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTNRLTECollaborativeAnaInfo
    {
        public ZTNRLTECollaborativeAnaType Type { get; set; }
        public string TypeDesc { get; private set; }
        public string FileName { get; set; }

        public List<TestPoint> TestPoints { get; private set; } = new List<TestPoint>();

        public int TestPointCount { get; private set; }

        public int SN { get; set; }

        public NRLTECollaborativeAnaTPInfo NRInfo { get; set; } = new NRLTECollaborativeAnaTPInfo();
        public NRLTECollaborativeAnaTPInfo LTEInfo { get; set; } = new NRLTECollaborativeAnaTPInfo();

        public double StaySecond { get; private set; }
        public double StayDistance { get; private set; }
        public string RoadName { get; private set; }

        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public void AddTestPoint(TestPoint tp, TPInfo nrInfo, TPInfo lteInfo, double dis2LastTP)
        {
            TestPoints.Add(tp);
            NRInfo.AddData(nrInfo);
            LTEInfo.AddData(lteInfo);
            StayDistance += dis2LastTP;
        }

        public void Calculate()
        {
            TestPointCount = TestPoints.Count;
            NRInfo.Calculate(TestPointCount);
            LTEInfo.Calculate(TestPointCount);
            if (TestPointCount > 1)
            {
                StaySecond = (TestPoints[TestPointCount - 1].DateTime - TestPoints[0].DateTime).TotalSeconds;
            }
            findRoadName();
            setTpeDesc();
        }

        private void findRoadName()
        {
            if (TestPointCount > 0)
            {
                List<double> lngs = new List<double>();
                List<double> lats = new List<double>();
                TestPoint tp = TestPoints[0];
                FileName = tp.FileName;

                Longitude = tp.Longitude;
                Latitude = tp.Latitude;

                lngs.Add(tp.Longitude);
                lats.Add(tp.Latitude);
                tp = TestPoints[(TestPointCount / 2)];
                lngs.Add(tp.Longitude);
                lats.Add(tp.Latitude);
                tp = TestPoints[TestPointCount - 1];
                lngs.Add(tp.Longitude);
                lats.Add(tp.Latitude);
                RoadName = GISManager.GetInstance().GetRoadPlaceDesc(lngs, lats);
            }
        }

        private void setTpeDesc()
        {
            switch (Type)
            {
                case ZTNRLTECollaborativeAnaType.WeakCover4G5G:
                    TypeDesc = "覆盖同差";
                    break;
                case ZTNRLTECollaborativeAnaType.Better4G:
                    TypeDesc = "4G优于5G";
                    break;
                case ZTNRLTECollaborativeAnaType.Better5G:
                    TypeDesc = "5G优于4G";
                    break;
            }
        }

        public class TPInfo
        {
            public float Rsrp { get; set; }
            public float Sinr { get; set; }
        }
    }

    public class NRLTECollaborativeAnaTPInfo
    {
        public float MinRsrp { get; set; } = float.MaxValue;
        public float MaxRsrp { get; set; } = float.MinValue;
        public float SumRsrp { get; set; }
        public float AvgRsrp { get; set; }
        public float MinSinr { get; set; } = float.MaxValue;
        public float MaxSinr { get; set; } = float.MinValue;
        public float SumSinr { get; set; }
        public float AvgSinr { get; set; }

        public void AddData(ZTNRLTECollaborativeAnaInfo.TPInfo info)
        {
            MinRsrp = Math.Min(MinRsrp, info.Rsrp);
            MaxRsrp = Math.Max(MaxRsrp, info.Rsrp);
            SumRsrp += info.Rsrp;

            if (info.Sinr >= -100 && info.Sinr <= 100)
            {
                MinSinr = Math.Min(MinSinr, info.Sinr);
                MaxSinr = Math.Max(MaxSinr, info.Sinr);
                SumSinr += info.Sinr;
            }
        }

        public void Calculate(int tpCount)
        {
            if (tpCount > 0)
            {
                AvgRsrp = (float)Math.Round(SumRsrp / tpCount, 2);
                AvgSinr = (float)Math.Round(SumSinr / tpCount, 2);
            }
        }
    }

    public enum ZTNRLTECollaborativeAnaType
    {
        Unknown,
        //覆盖同差
        WeakCover4G5G,
        //4G优于5G
        Better4G,
        //5G优于4G
        Better5G
    }
}
