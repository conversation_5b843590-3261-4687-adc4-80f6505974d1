﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Globalization;

namespace MasterCom.RAMS.ZTFunc
{
    class AdviseSubmit: DIYSQLBase
    {
        private readonly bool IsTDIn;
        private readonly DateTime timeStart;
        private readonly DateTime timeEnd;
        private readonly string advise;
        public int ChangeCCount;
        public AdviseSubmit(MainModel mainModel,string advise, bool IsTDIn, 
            DateTime timeStart, DateTime timeEnd)
            : base(mainModel)
        {
            this.IsTDIn = IsTDIn;
            this.timeStart = timeStart;
            this.timeEnd = timeEnd;
            this.advise = advise;
            ChangeCCount = 0;
        }

        protected override string getSqlTextString()
        {
            string IDOfTmdat = timeStart.ToString("yyyyMMdd") + 
                timeEnd.ToString("yyyyMMdd") + (IsTDIn ? "1" : "0");
            string statement = "delete from tb_para_Advise where ID = "+ IDOfTmdat
                +"\r\ninsert into tb_para_Advise values(" + IDOfTmdat+",'"+advise+"')";           
            return statement;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    try
                    {
                        ChangeCCount = package.Content.GetParamInt();
                    }
                    catch
                    {
                        //continue
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgress(ref index, ref progress);
            }
        }

        private static void setProgress(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        public override string Name
        {
            get { return "PerDetailInfQuery"; }
        }

    }
}
