﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRLastWeakMosAnaByTPDlg : MinCloseForm
    {
        public NRLastWeakMosAnaByTPDlg()
        {
            InitializeComponent();
            spinEditTPNum.ValueChanged += spinEditTPNum_ValueChanged;
        }

        void spinEditTPNum_ValueChanged(object sender, EventArgs e)
        {
            double d = (double)spinEditTPNum.Value;
            if (d > 9999) d = 9999;
            if (d < 0) d = 0;
            d = Math.Round(d, 0);
            spinEditTPNum.Value = (decimal)d;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public void SetValue(NRLastWeakMosByTPCondition curCondtion)
        {
            spinEditMOS.Value = (decimal)curCondtion.MOS;
            spinEditTPNum.Value = curCondtion.TpNum;
            spinEditDistance.Value = (decimal)curCondtion.Distance;
            checkEditACall.Checked = curCondtion.IsACall;
        }

        public NRLastWeakMosByTPCondition GetResult()
        {
            NRLastWeakMosByTPCondition curCondtion = new NRLastWeakMosByTPCondition();
            curCondtion.MOS = (double)spinEditMOS.Value;
            curCondtion.TpNum = (int)spinEditTPNum.Value;
            curCondtion.Distance = (double)spinEditDistance.Value;
            curCondtion.IsACall = checkEditACall.Checked;
            return curCondtion;
        }
    }
}
