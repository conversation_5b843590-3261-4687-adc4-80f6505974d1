using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class WlanChannelsChartForm : ChildForm
    {
        public WlanChannelsChartForm()
        {
            InitializeComponent();
        }

        public override void Init()
        {
            base.Init();
            MainModel.SelectedTestPointsChanged += selectedTestPointsChanged;
            Disposed += disposed;
            initializeComponent();
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                return param;
            }
        }

        private void initializeComponent()
        {
            wlanChannelChart.Series.Clear();
        }

        private void disposed(object sender, EventArgs e)
        {
            MainModel.SelectedTestPointsChanged -= selectedTestPointsChanged;
        }

        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            wlanChannelChart.Series.Clear();
            if (MainModel.SelectedTestPoints.Count > 0)
            {
                dealValidTP();
            }
        }

        private void dealValidTP()
        {
            if (MainModel.SelectedTestPoints[0] is WLANTestPoint)
            {
                WLANTestPoint testPoint = MainModel.SelectedTestPoints[0] as WLANTestPoint;
                for (int i = 0; i < 50; i++)
                {
                    int? channel = (int?)testPoint["WLAN_ARR_AP_Channel", i];
                    int? rssi = (int?)testPoint["WLAN_ARR_AP_RSSI", i];
                    object bssidObj = testPoint["WLAN_ARR_AP_BSSID_String", i];
                    object ssidObj = testPoint["WLAN_ARR_AP_SSID", i];
                    string ssid = ssidObj == null ? "" : ssidObj.ToString();
                    if (channel == null || rssi == null || bssidObj == null)
                    {
                        break;
                    }
                    else
                    {
                        DrawChannelLine((int)channel, (int)rssi, ssid);
                    }
                }
            }
        }

        private void DrawChannelLine(int channel, int rssi, string ssid)
        {
            ((System.ComponentModel.ISupportInitialize)(this.wlanChannelChart)).BeginInit();
            Series series1 = new Series("", ViewType.Spline);
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((LineSeriesView)series1.View).LineStyle.DashStyle = DashStyle.Dot;
            ((LineSeriesView)series1.View).LineMarkerOptions.Visible = false;
            series1.ArgumentScaleType = ScaleType.Numerical;
            series1.Points.Add(new SeriesPoint(channel - 2, -100));
            series1.Points.Add(new SeriesPoint(channel, rssi));
            PointSeriesLabel lable1 = new PointSeriesLabel();
            ((System.ComponentModel.ISupportInitialize)(lable1)).BeginInit();
            lable1.Angle = 90;
            lable1.BackColor = System.Drawing.Color.Transparent;
            lable1.Border.Visible = false;
            lable1.LineLength = 6;
            lable1.LineVisible = false;
            series1.Label = lable1;
            series1.Tag = ssid;
            series1.Points.Add(new SeriesPoint(channel + 2, -100));
            wlanChannelChart.Series.Add(series1);
            ((System.ComponentModel.ISupportInitialize)(lable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.wlanChannelChart)).EndInit();
        }

        private void wlanChannelChart_CustomDrawSeriesPoint(object sender, CustomDrawSeriesPointEventArgs e)
        {
            if (e.SeriesPoint.Values[0] == -100)
            {
                e.LabelText = "";
            }
            else
            {
                e.LabelText = e.Series.Tag as string;
            }
        }
    }

    public class WlanApChannelInfo
    {
        public string channel { get; set; }
        public List<WlanApInfo> apInfoList { get; set; }

        public WlanApChannelInfo()
        {
            channel = "";
            apInfoList = new List<WlanApInfo>();
        }
    }

    public class WlanApMacInfo
    {
        public string ssid { get; set; }
        public List<WlanApInfo> apInfoList { get; set; }

        public WlanApMacInfo()
        {
            ssid = "";
            apInfoList = new List<WlanApInfo>();
        }
    }

    public class WlanApInfo
    {
        public string channel { get; set; }
        public string freqecncy { get; set; }
        public string ssid { get; set; }
        public string bssid { get; set; }
        public string rssi { get; set; }
        public string ci { get; set; }
        public string sfi { get; set; }
        public string nfi { get; set; }
        public string firstSeen { get; set; }
        public string lastSeen { get; set; }
        public string activeTime { get; set; }
        public string rx { get; set; }
        public string tx { get; set; }
        public string band { get; set; }
        public string abgn { get; set; }
        public string type { get; set; }
        public string encrytion { get; set; }
        public string security { get; set; }
        public string bridge { get; set; }
        public string dcf_pcf { get; set; }
        public string beaconInterval { get; set; }
        public string supportRate { get; set; }
        public string maxRate { get; set; }
        public string preamble { get; set; }
        public string noise { get; set; }

        public WlanApInfo()
        {
            channel = "";
            freqecncy = "";
            ssid = "";
            bssid = "";
            rssi = "";
            ci = "";
            sfi = "";
            nfi = "";
            firstSeen = "";
            lastSeen = "";
            activeTime = "";
            rx = "";
            tx = "";
            band = "";
            abgn = "";
            type = "";
            encrytion = "";
            security = "";
            bridge = "";
            dcf_pcf = "";
            beaconInterval = "";
            supportRate = "";
            maxRate = "";
            preamble = "";
            noise = "";
        }

        public void FillInfo(WLANTestPoint testPoint, int index)
        {
            channel = ObjToString(testPoint["WLAN_ARR_AP_Channel", index]);
            freqecncy = ObjToString(testPoint["WLAN_ARR_AP_Freqecncy", index]);
            ssid = ObjToString(testPoint["WLAN_ARR_AP_SSID", index]);
            bssid = ObjToString(testPoint["WLAN_ARR_AP_BSSID_String", index]);
            rssi = ObjToString(testPoint["WLAN_ARR_AP_RSSI", index]);
            ci = ObjToString(testPoint["WLAN_ARR_AP_SNR", index]);
            sfi = ObjToString(testPoint["WLAN_ARR_AP_SFI", index]);
            nfi = ObjToString(testPoint["WLAN_ARR_AP_NFI", index]);
            firstSeen = ObjToString(testPoint["WLAN_ARR_AP_CDS_First_Seen_String", index]);
            lastSeen = ObjToString(testPoint["WLAN_ARR_AP_CDS_Last_Seen_String", index]);
            activeTime = ObjToString(testPoint["WLAN_ARR_AP_CDS_Active_Time", index]);
            rx = ObjToString(testPoint["WLAN_ARR_AP_CDS_Rxthrput", index]);
            tx = ObjToString(testPoint["WLAN_ARR_AP_CDS_Txthrput", index]);
            band = ObjToString(testPoint["WLAN_ARR_AP_CDS_Band", index]);
            abgn = ObjToString(testPoint["WLAN_ARR_AP_NetType", index]);
            type = ObjToString(testPoint["WLAN_ARR_AP_Infrastructure", index]);
            encrytion = ObjToString(testPoint["WLAN_ARR_AP_CDS_Encry", index]);
            security = ObjToString(testPoint["WLAN_ARR_AP_Security", index]);
            bridge = ObjToString(testPoint["WLAN_ARR_AP_CDS_Bridge", index]);
            dcf_pcf = ObjToString(testPoint["WLAN_ARR_AP_CDS_PCF_DCF", index]);
            beaconInterval = ObjToString(testPoint["WLAN_ARR_AP_CDS_BeanconInterval", index]);
            supportRate = ObjToString(testPoint["WLAN_ARR_AP_susp_speed_String", index]);
            maxRate = ObjToString(testPoint["WLAN_ARR_AP_CDS_MaxRate", index]);
            preamble = ObjToString(testPoint["WLAN_ARR_AP_CDS_PreambleLen", index]);
            noise = ObjToString(testPoint["WLAN_ARR_AP_Noise", index]);

        }

        public string ObjToString(object obj)
        {
            string str;
            str = obj == null ? "" : obj.ToString();
            return str;
        }
    }
}