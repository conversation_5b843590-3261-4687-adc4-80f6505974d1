﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellCheckDiffFreqAnaByFile : ZTLteNBCellCheckDiffFreqAnaBase
    {
        public ZTLteNBCellCheckDiffFreqAnaByFile(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected static readonly object lockObj = new object();
        private static ZTLteNBCellCheckDiffFreqAnaByFile instance = null;
        public static ZTLteNBCellCheckDiffFreqAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLteNBCellCheckDiffFreqAnaByFile(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "LTE邻区核查(按文件)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22044, this.Name);//////
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class LteFddNBCellCheckDiffFreqAnaByFile : ZTLteNBCellCheckDiffFreqAnaByFile
    {
        public LteFddNBCellCheckDiffFreqAnaByFile(MainModel mainModel)
            : base(mainModel)
        {
        }
        private static LteFddNBCellCheckDiffFreqAnaByFile instance = null;
        public new static LteFddNBCellCheckDiffFreqAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteFddNBCellCheckDiffFreqAnaByFile(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26018, this.Name);
        }
    }
}