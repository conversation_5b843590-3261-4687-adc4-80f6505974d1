﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYQueryLteHighriskDataForm : MinCloseForm
    {
        public ZTDIYQueryLteHighriskDataForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }
        Dictionary<string, List<LTEHighriskInterfereCellInfo>> overCoverCellInterfereInfo = null;
        List<LTEHighriskItemInfo> lteHighHrisItemInfo = null;
        public void FillData(List<LTEHighriskItemInfo> lteHighHrisItemInfoList
            , Dictionary<string, List<LTEHighriskInterfereCellInfo>> overCoverCellInterfereInfoDic)
        {
            lteHighHrisItemInfo = new List<LTEHighriskItemInfo>();
            BindingSource source = new BindingSource();
            source.DataSource = lteHighHrisItemInfoList;
            dataGrid.DataSource = source;
            dataGrid.RefreshDataSource();
            this.lteHighHrisItemInfo = lteHighHrisItemInfoList;

            overCoverCellInterfereInfo = new Dictionary<string, List<LTEHighriskInterfereCellInfo>>();
            this.overCoverCellInterfereInfo = overCoverCellInterfereInfoDic;
        }

        private void outPutDataExcel_Click(object sender, EventArgs e)
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            List<NPOIRow> datas2 = new List<NPOIRow>();

            NPOIRow nr1 = new NPOIRow();
            List<object> cols = new List<object>();
            cols.Add("小区名称");
            cols.Add("TAC");
            cols.Add("ECI");
            cols.Add("过覆盖采样点数");
            cols.Add("弱覆盖采样点数");
            cols.Add("覆盖采样点数");
            cols.Add("过覆盖采样点占比");
            cols.Add("弱覆盖采样点占比");
            nr1.cellValues = cols;
            datas.Add(nr1);

            NPOIRow nr2 = new NPOIRow();
            List<object> cols2 = new List<object>();
            cols2.Add("主服小区");
            cols2.Add("被干扰小区");
            cols2.Add("过覆盖采样点数");
            cols2.Add("覆盖采样点数");
            cols2.Add("过覆盖采样点占比");
            nr2.cellValues = cols2;
            datas2.Add(nr2);

            foreach (LTEHighriskItemInfo lteHighHriskItem in lteHighHrisItemInfo)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();
                objs.Add(lteHighHriskItem.StrCellName);
                objs.Add(lteHighHriskItem.StrTAC);
                objs.Add(lteHighHriskItem.StrECI);
                objs.Add(lteHighHriskItem.IOverCoverSampleCount);
                objs.Add(lteHighHriskItem.IWeakCoverSampleCount);
                objs.Add(lteHighHriskItem.ISampleCount);
                objs.Add(lteHighHriskItem.StrOverCoverRate);
                objs.Add(lteHighHriskItem.StrWeakCoverRate);

                nr.cellValues = objs;
                datas.Add(nr);
            }
            foreach (string strCell in overCoverCellInterfereInfo.Keys)
            {
                foreach (LTEHighriskInterfereCellInfo lteHighHriskItem in overCoverCellInterfereInfo[strCell])
                {
                    NPOIRow nr = new NPOIRow();
                    List<object> objs = new List<object>();
                    objs.Add(lteHighHriskItem.StrMainCellName);
                    objs.Add(lteHighHriskItem.StrInterfereCellName);
                    objs.Add(lteHighHriskItem.IInterfereCellSampleCount);
                    objs.Add(lteHighHriskItem.IInterfereCellTotalSampleCount);
                    objs.Add(lteHighHriskItem.StrIInterfereOverCoverRate);

                    nr.cellValues = objs;
                    datas2.Add(nr);
                }
            }


            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);
            nrDatasList.Add(datas2);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("高风险小区列表");
            sheetNames.Add("主服邻区过覆盖列表");

            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }

        private void dataGrid_Click(object sender, EventArgs e)
        {
            if (gridView1.SelectedRowsCount > 0)
            {
                int[] rows = this.gridView1.GetSelectedRows();
                foreach (int iRow in rows)
                {
                    LTEHighriskItemInfo lteHighHriskItem = this.gridView1.GetRow(iRow) as LTEHighriskItemInfo;
                    BindingSource interfereSource = new BindingSource();
                    if (overCoverCellInterfereInfo.ContainsKey(lteHighHriskItem.StrCellName))
                    {
                        interfereSource.DataSource = overCoverCellInterfereInfo[lteHighHriskItem.StrCellName];
                    }
                    else
                    {
                        List<LTEHighriskInterfereCellInfo> tmpList = new List<LTEHighriskInterfereCellInfo>();
                        interfereSource.DataSource = tmpList;

                    }
                    interfereDataGrid.DataSource = interfereSource;
                }
            }
        }
    }
}
