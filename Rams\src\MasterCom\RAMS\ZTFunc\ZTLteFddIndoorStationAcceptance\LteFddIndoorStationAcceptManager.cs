﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.IO;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc
{
    class LteFddIndoorStationAcceptManager : IStationAcceptManager
    {
        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        protected string workDir = "";
        protected LteStationAcceptCondition acceptCond = null;
        protected List<LteFddIndoorStationAccept> acceptorList = null;
        protected Dictionary<string, int> sectorIDDic = null;
        protected string btsName;

        protected string errMsg;
        public string ErrMsg
        {
            get { return errMsg; }
        }

        protected string hasExportedFiles;
        public string HasExportedFiles
        {
            get { return hasExportedFiles; }
        }

        public virtual void SetAcceptCond(LteStationAcceptCondition cond)
        {
            workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/LteFddStationAcceptance");
            this.acceptCond = cond;
            errMsg = "";
            this.acceptorList = new List<LteFddIndoorStationAccept>()
            {
                new FddIndoorAcpFtpDownload(),
                new FddIndoorAcpFtpUpload(),
                new FddIndoorAcpHandover(),
                new FddIndoorAcpCsfbRate(),
                new FddIndoorAcpRrcRate(),
                new FddIndoorAcpErabRate(),
                new FddIndoorAcpAccRate(),
                new FddIndoorAcp24ReselectRate(),
                new FddIndoorAcpVolteVoiceMo(),
                new FddIndoorAcpVolteVoiceMt(),
                new FddIndoorAcpLeakOutLock(),
                new FddIndoorAcpLeakOutScan(),
                new FddIndoorAcpCellName(),
                new FddIndoorAcpCellActualPara(),
                new FddIndoorAcpCellPlanPara(),
                new FddIndoorAcpCellAntenna(),
                new FddIndoorAcpCellLeveling(),
                new FddIndoorAcpLeveling(),
                new FddIndoorAcpCellCoverePic(),
                new FddIndoorAcpLeakOutScanPic(),
                new FddIndoorAcpPerformance(),
                new FddIndoorAcpAlarm()
            };
            sectorIDDic = new Dictionary<string, int>();
            DiyQueryFddDBSetting.GetInstance().Query();
        }

        public virtual void AnalyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            try
            {
                btsName = getValidBtsName(fileManager.FileName);
                if (string.IsNullOrEmpty(btsName))
                {
                    return;
                }

                string PCI = getPCI(fileInfo.Name);
                if (string.IsNullOrEmpty(PCI))
                {
                    return;
                }
                if (!sectorIDDic.ContainsKey(PCI))
                {
                    int sectorID = sectorIDDic.Count;
                    sectorIDDic[PCI] = sectorID;
                }

                LTECell lteCell = GetTargetCell(fileManager, PCI);
                if (lteCell != null || fileInfo.Name.Contains("扫频"))//扫频泄露文件关联不到小区
                {
                    foreach (LteFddIndoorStationAccept acp in acceptorList)
                    {
                        acp.Init(lteCell, btsName);
                        acp.AnalyzeFile(fileInfo, fileManager, PCI, sectorIDDic[PCI]);
                    }

                    dealResult();
                }
            }
            catch
            {
                Clear();
                throw;
            }
        }

        protected virtual void dealResult()
        {
            FddIndoorAcpCellLeveling levelingSPData = (FddIndoorAcpCellLeveling)acceptorList[16];
            if (levelingSPData.HasData)
            {
                FddIndoorAcpLeveling levelingData = (FddIndoorAcpLeveling)acceptorList[17];
                levelingData.ClearData();
            }
        }

        protected virtual string getValidBtsName(string fileName)
        {
            string fileBtsName = "";
            string[] names = fileName.Split('_');
            if (names.Length > 3 && fileName.ToUpper().Contains("PCI"))
            {
                fileBtsName = names[2];
            }
            return fileBtsName;
        }

        private LTECell GetTargetCell(DTFileDataManager fileManager, string pci)
        {
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                List<LTECell> cells = StationAcceptCellHelper_XJ.Instance.GetLTECellList(tp);
                foreach (LTECell lteCell in cells)
                {
                    if (lteCell != null && lteCell.Name.Contains(btsName) && lteCell.PCI.ToString() == pci)
                    {
                        return lteCell;
                    }
                }
            }
            return null;
        }

        //private LTECell GetTargetCell(DTFileDataManager fileManager, string pci)
        //{
        //    LTECell targeCell = null;
        //    int maxPoint = int.MinValue;
        //    Dictionary<LTECell, int> cellPointDic = new Dictionary<LTECell, int>();

        //    foreach (TestPoint tp in fileManager.TestPoints)
        //    {
        //        List<LTECell> cells = tp.GetCellList_LTE(true);
        //        foreach (LTECell lteCell in cells)
        //        {
        //            if (!cellPointDic.ContainsKey(lteCell))
        //            {
        //                cellPointDic.Add(lteCell, 0);
        //            }
        //            ++cellPointDic[lteCell];

        //            if (maxPoint < cellPointDic[lteCell])
        //            {
        //                maxPoint = cellPointDic[lteCell];
        //                targeCell = lteCell;
        //            }
        //        }
        //    }

        //    if (targeCell != null && targeCell.Name.Contains(btsName) && targeCell.PCI.ToString() == pci)
        //    {
        //        return targeCell;
        //    }
        //    return null;
        //}

        protected virtual string getPCI(string fileName)
        {
            int index = fileName.ToUpper().IndexOf("PCI") + 3;
            int endIndex = -1;
            string PCI = "";
            for (int i = index; i < fileName.Length; i++)
            {
                char ch = fileName[i];
                if (ch > '9' || ch < '0')
                {
                    endIndex = i;
                    break;
                }
            }

            int length = -1;
            if (endIndex > index)
            {
                length = endIndex - index;
            }
            if (length > 0)
            {
                PCI = fileName.Substring(index, length);
            }
            return PCI;
        }

        public void DoWorkAfterAnalyze()
        {
            try
            {
                CreateFileForBts(sectorIDDic);
            }
            finally
            {
                Clear();
            }
        }

        protected void CreateFileForBts(Dictionary<string, int> btsDic)
        {
            hasExportedFiles = "";
            foreach (LteFddIndoorStationAccept acp in acceptorList)
            {
                if (acp.BtsNames.Count > 1)
                {
                    errMsg = "所选文件列表中包含多个基站文件,只能分析一个基站文件!";
                    return;
                }
            }

            int sectorCount = btsDic.Count;
            string targetFile = GetTargetFile(btsName, sectorCount, acceptCond.SaveFolder);
            if (string.IsNullOrEmpty(targetFile))
            {
                return;
            }
            exportFile(btsDic, targetFile);

            saveCurReport(btsDic, sectorCount);
        }

        private void exportFile(Dictionary<string, int> btsDic, string targetFile)
        {
            List<int> sectorIDs = getSortSectorIDs(btsDic);
            Excel.Application xlApp = null;
            Excel.Workbook eBook = null;
            try
            {
                WaitTextBox.Text = "正在导出Excel...";
                xlApp = new Excel.Application();
                xlApp.Visible = false;
                eBook = xlApp.Workbooks.Open(targetFile,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing);

                foreach (LteFddIndoorStationAccept acp in acceptorList)
                {
                    acp.FillResult(eBook, sectorIDs);
                }

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);
                hasExportedFiles = btsName;
            }
            catch (Exception ex)
            {
                //这里使用eBook.Close会卡死在这里,不清楚为什么,不close又无法退出excel进程导致进程被占用下次无法导出,除非手动杀死excel进程
                log.Error(ex.Message + ex.StackTrace);
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
            }
        }

        protected virtual void saveCurReport(Dictionary<string, int> btsDic, int cellCount)
        {
            string path = getPath();
            if (string.IsNullOrEmpty(path))
            {
                return;
            }

            string targetFile = GetTargetFile(btsName, cellCount, path);
            if (!string.IsNullOrEmpty(targetFile))
            {
                exportFile(btsDic, targetFile);
            }
        }

        protected virtual string getPath()
        {
            return Singleton<FddIndoorStationAcceptConfigHelper>.Instance.GetCurSavePath();
        }

        /// <summary>
        /// 按PCI大小将结果排序
        /// </summary>
        /// <param name="btsDic"></param>
        /// <returns></returns>
        protected List<int> getSortSectorIDs(Dictionary<string, int> btsDic)
        {
            List<int> sortPCI = new List<int>();
            List<KeyValuePair<string, int>> lst = new List<KeyValuePair<string, int>>(btsDic);
            lst.Sort(delegate (KeyValuePair<string, int> s1, KeyValuePair<string, int> s2)
            {
                return s1.Key.CompareTo(s2.Key);
            });

            foreach (var item in lst)
            {
                sortPCI.Add(item.Value);
            }
            return sortPCI;
        }

        private void Clear()
        {
            foreach (LteFddIndoorStationAccept acp in acceptorList)
            {
                acp.Clear();
            }
        }

        public virtual string GetTargetFile(string btsName, int sectorCount, string saveFolder)
        {
            if (sectorCount > 3)
            {
                errMsg = string.Format("基站{0}PCI小区超过3个，不支持报告导出", btsName);
                return "";
            }

            string templateFile = "LTEFDD室分站验收模板.xlsx";
            templateFile = Path.Combine(workDir, templateFile);
            if (!File.Exists(templateFile))
            {
                errMsg = string.Format("[{0}]路径下不存在报告模板文件", templateFile);
                return "";
            }

            string targetFile = string.Format("LTEFDD室分站验收_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            File.Copy(templateFile, targetFile, true);
            return targetFile;
        }
    }
}
