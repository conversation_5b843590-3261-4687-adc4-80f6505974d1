﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MapWinGIS;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class LoadAreaFromDb : DIYSQLBase
    {
        public LoadAreaFromDb(ZTAreaManager areaMngr)
            : base(MainModel.GetInstance())
        {
            this.areaMngr = areaMngr;
            MainDB = false;
        }
        private readonly ZTAreaManager areaMngr = null;

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                areaDic = new Dictionary<int, Dictionary<string, int>>();
                foreach (AreaRank rank in this.areaMngr.Ranks)
                {
                    Dictionary<string, int> dic = new Dictionary<string, int>();
                    areaDic[rank.AreaTypeID] = dic;
                    foreach (CategoryEnumItem item in AreaManager.GetInstance()[rank.AreaTypeID])
                    {
                        dic[item.Name] = item.ID;
                    }
                }

                //sendAndReceive(clientProxy, TableType.Rank);
                snAreaDic.Clear();
                sendAndReceive(clientProxy, TableType.AreaSet);
                sendAndReceive(clientProxy, TableType.Shape);

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }

        Dictionary<int, Dictionary<string, int>> areaDic = new Dictionary<int, Dictionary<string, int>>();
        readonly Dictionary<string, AreaBase> snAreaDic = new Dictionary<string, AreaBase>();
        private void sendAndReceive(ClientProxy clientProxy,TableType curTable)
        {
            clientProxy.Package.Command = Command.DIYSearch;
            clientProxy.Package.SubCommand = SubCommand.Request;
            clientProxy.Package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;//当前库
            clientProxy.Package.Content.PrepareAddParam();
            string strsql = getSqlTextString(curTable);
            E_VType[] retArrDef = getSqlRetTypeArr(curTable);

            clientProxy.Package.Content.AddParam(strsql);
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    sb.Append(",");
                }
            }

            clientProxy.Package.Content.AddParam(sb.ToString().TrimEnd(','));
            clientProxy.Send();
            receiveRetData(clientProxy, curTable);
        }

        private string getSqlTextString(TableType table)
        {
            switch (table)
            {
                case TableType.Rank:
                    return "SELECT iID,strName,iRank,iParentID,strComment FROM tb_cfg_zt_area_rank order by iID";
                case TableType.AreaSet:
                    return "SELECT iRankID,iID,strName,iParentID,strSn,strComment FROM tb_cfg_zt_area_list order by iRankID";
                case TableType.Shape:
                    return "SELECT strSn,strShapeInfo FROM tb_cfg_zt_area_shape";
                default:
                    return null;
            }
        }

        private Model.Interface.E_VType[] getSqlRetTypeArr(TableType table)
        {
            E_VType[] valueType = null;
            int i = 0;
            if (table == TableType.Rank)
            {
                valueType = new E_VType[5];
                valueType[i++] = E_VType.E_Int;
                valueType[i++] = E_VType.E_String;
                valueType[i++] = E_VType.E_Int;
                valueType[i++] = E_VType.E_Int;
                valueType[i] = E_VType.E_String;
            }
            else if (table == TableType.AreaSet)
            {
                valueType = new E_VType[6];
                valueType[i++] = E_VType.E_Int;
                valueType[i++] = E_VType.E_Int;
                valueType[i++] = E_VType.E_String;
                valueType[i++] = E_VType.E_Int;
                valueType[i++] = E_VType.E_String;
                valueType[i] = E_VType.E_String;
            }
            else if (table == TableType.Shape)
            {
                valueType = new E_VType[2];
                valueType[i++] = E_VType.E_String;
                valueType[i] = E_VType.E_String;
            }
            return valueType;
        }

        private void receiveRetData(ClientProxy clientProxy, TableType table)
        {
            Package package = clientProxy.Package;
            //List<AreaRank> rankSet = new List<AreaRank>();
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(table, package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
            //if (rankSet.Count > 0)
            //{
            //    areaMngr.Ranks = rankSet;
            //}
        }

        private void fillData(TableType table, Package package)
        {
            if (table == TableType.Rank)
            {
                //int id = package.Content.GetParamInt();
                //string name = package.Content.GetParamString();
                //int iRank = package.Content.GetParamInt();
                //int parentID = package.Content.GetParamInt();
                //string comment = package.Content.GetParamString();
                //AreaRank parentRank = rankSet.Find(delegate(AreaRank x) { return x.ID == parentID; });
                //AreaRank rank = new AreaRank(id, name, parentRank);
                //rankSet.Add(rank);
            }
            else if (table == TableType.AreaSet)
            {
                addArea(package);
            }
            else if (table == TableType.Shape)
            {
                addShape(package);
            }
        }

        private void addArea(Package package)
        {
            int rankID = package.Content.GetParamInt();
            int id = package.Content.GetParamInt();
            string name = package.Content.GetParamString();
            int parentID = package.Content.GetParamInt();
            string sn = package.Content.GetParamString();
            string comment = package.Content.GetParamString();
            AreaRank rank = areaMngr.GetRank(rankID);

            AreaBase parentArea = areaMngr.GetArea(parentID);

            AreaBase area = new AreaBase(rank, name);
            area.DistrictID = dbid;
            area.ID = id;
            area.AreaTypeID = rank.AreaTypeID;
            area.SN = sn;
            area.StrComment = comment;

            //if (parentID == -1)
            //{
            //    areaMngr.AddRootArea(area);
            //    area.AreaTypeID = int.Parse(sn);
            //}
            //else
            //{
            //    AreaBase root = areaMngr.GetRoot(area);
            //    area.AreaTypeID = root.AreaTypeID;
            //}

            //if (parentID != -1 && !string.IsNullOrEmpty(sn))
            //{
            //    foreach (CategoryEnumItem item in AreaManager.GetInstance()[area.AreaTypeID])
            //    {
            //        if (item.Name == sn)
            //        {
            //            area.AreaID = item.ID;
            //            break;
            //        }
            //    }
            //}

            if (parentArea != null)
            {
                parentArea.AddArea(area);
            }

            Dictionary<string, int> areaNameIDDic = null;
            if (areaDic.TryGetValue(area.AreaTypeID, out areaNameIDDic))
            {
                int iid;
                if (areaNameIDDic.TryGetValue(area.FullName, out iid))
                {
                    area.AreaID = areaNameIDDic[area.FullName];
                }
            }
            if (!string.IsNullOrEmpty(area.SN))
            {
                snAreaDic[area.SN] = area;
            }
            areaMngr.AddArea(area);
        }

        private void addShape(Package package)
        {
            string sn = package.Content.GetParamString();
            string shapeStr = package.Content.GetParamString();
            MapWinGIS.Shape temp = new MapWinGIS.Shape();
            if (temp.CreateFromString(shapeStr))
            {
                AreaBase area;
                if (snAreaDic.TryGetValue(sn, out area))
                {
                    area.Shape = temp;
                }
            }
            else
            {
                MessageBox.Show("序号为" + sn + "的区域图层信息有误，请核查!");
            }
        }

        private enum TableType
        {
            Rank,
            AreaSet,
            Shape
        }

        protected override string getSqlTextString()
        {
            throw new NotImplementedException();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            throw new NotImplementedException();
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            throw new NotImplementedException();
        }
    }
}
