﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using System.Runtime.Serialization;
using System.Drawing;
using System.Drawing.Imaging;

namespace MasterCom.RAMS.CQT
{
    public class CQTZTMapPointLayer : CustomDrawLayer//, IKMLExport
    {
        public CQTZTMapPointLayer(MapOperation mp, string name)
            : base(mp, name)
        {
            this.VisibleScaleEnabled = true;
            this.VisibleScale = new VisibleScale(0, 10000000);
            CQTPoints2ShowSec = new List<CQTLibrary.PublicItem.ComplainCase>();
        }
        
        public float FLongttude { get; set; }
        public float FLatitue { get; set; }
        public string CqtName { get; set; }
        public List<CQTLibrary.PublicItem.ComplainCase> CQTPoints2ShowSec { get; set; }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            updateRect.Inflate((int)(40 * 10000 / Map.Scale), (int)(40 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            drawCQTComplanPoints(graphics);
        }

        private void drawCQTComplanPoints(Graphics graphics)
        {
            for (int k = 0; k < 1;k++ )
            {
                DbPoint dPoint = new DbPoint(FLongttude, FLatitue);
                PointF point;
                this.Map.ToDisplay(dPoint, out point);
                float radius = (float)(2000 / Map.Scale);
                RectangleF rect = new RectangleF(point.X - radius * 128, point.Y - radius * 256, radius * 256, radius * 256);
                Image img = Properties.Resources.cqtDefault;
                Image imgClone = (Image)img.Clone();
                ImageAttributes imageAttributes = new ImageAttributes();
                int width = img.Width;
                int height = img.Height;
                ColorMap colorMap = new ColorMap();
                colorMap.OldColor = Color.FromArgb(255, 104, 51);
                colorMap.NewColor = Color.Red;
                ColorMap[] remapTable = { colorMap };
                imageAttributes.SetRemapTable(remapTable, ColorAdjustType.Bitmap);
                Graphics g = Graphics.FromImage(imgClone);
                g.DrawImage(
                   imgClone,
                   new Rectangle(0, 0, width, height),  // destination rectangle 
                   0, 0,        // upper-left corner of source rectangle 
                   width,       // width of source rectangle
                   height,      // height of source rectangle
                   GraphicsUnit.Pixel,
                   imageAttributes);

                Graphics gg = Graphics.FromImage(imgClone);
                Font font = new Font("宋体",60f); 
                Brush brush = Brushes.Black;
                PointF pointz = new PointF(0f,0f); 
                System.Drawing.StringFormat sf = new System.Drawing.StringFormat();
                sf.FormatFlags = StringFormatFlags.DisplayFormatControl;
                gg.DrawString(CqtName, font, brush, pointz, sf);
                graphics.DrawImage(imgClone, rect);
                
            }

            foreach (CQTLibrary.PublicItem.ComplainCase pnt in CQTPoints2ShowSec)
            {
                DbPoint dPoint = new DbPoint(pnt.Flongitude, pnt.Flatitude);
                PointF point;
                this.Map.ToDisplay(dPoint, out point);
                float radius = (float)(2000 / Map.Scale);
                RectangleF rect = new RectangleF(point.X - radius * 128, point.Y - radius * 256, radius * 256 * 3 / 4, radius * 256 * 3 / 4);
                Image img = Properties.Resources.cqtDefault;
                Image imgClone = (Image)img.Clone();
                ImageAttributes imageAttributes = new ImageAttributes();
                int width = img.Width;
                int height = img.Height;
                ColorMap colorMap = new ColorMap();
                colorMap.OldColor = Color.FromArgb(255, 104, 51);
                colorMap.NewColor = Color.YellowGreen;
                ColorMap[] remapTable = { colorMap };
                imageAttributes.SetRemapTable(remapTable, ColorAdjustType.Bitmap);
                Graphics g = Graphics.FromImage(imgClone);
                g.DrawImage(
                   imgClone,
                   new Rectangle(0, 0, width, height),  // destination rectangle 
                   0, 0,        // upper-left corner of source rectangle 
                   width,       // width of source rectangle
                   height,      // height of source rectangle
                   GraphicsUnit.Pixel,
                   imageAttributes);
                Graphics ggg = Graphics.FromImage(imgClone);
                Font font = new Font("宋体", 50f, FontStyle.Bold);
                Brush brush = Brushes.Red;
                PointF pointz = new PointF(0f, 0f);
                System.Drawing.StringFormat sf = new System.Drawing.StringFormat();
                sf.FormatFlags = StringFormatFlags.DisplayFormatControl;
                ggg.DrawString(pnt.Cpname.Split('|')[0], font, brush, pointz, sf);
                graphics.DrawImage(imgClone, rect);
            }
        }
    }
}
