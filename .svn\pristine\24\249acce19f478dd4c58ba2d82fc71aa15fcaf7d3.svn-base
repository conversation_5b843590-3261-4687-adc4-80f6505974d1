﻿namespace MasterCom.RAMS.Func.OwnSampleAnalyse
{
    partial class SampleFilterSelectorDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.cbxOwnFunc = new System.Windows.Forms.ComboBox();
            this.btnEdit = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.cbxDebugMode = new System.Windows.Forms.CheckBox();
            this.tabControlX = new System.Windows.Forms.TabControl();
            this.tabAnaSample = new System.Windows.Forms.TabPage();
            this.tabAnaGrid = new System.Windows.Forms.TabPage();
            this.tabControlX.SuspendLayout();
            this.tabAnaSample.SuspendLayout();
            this.SuspendLayout();
            // 
            // cbxOwnFunc
            // 
            this.cbxOwnFunc.FormattingEnabled = true;
            this.cbxOwnFunc.Location = new System.Drawing.Point(31, 22);
            this.cbxOwnFunc.Name = "cbxOwnFunc";
            this.cbxOwnFunc.Size = new System.Drawing.Size(311, 20);
            this.cbxOwnFunc.TabIndex = 0;
            // 
            // btnEdit
            // 
            this.btnEdit.Location = new System.Drawing.Point(359, 22);
            this.btnEdit.Name = "btnEdit";
            this.btnEdit.Size = new System.Drawing.Size(31, 23);
            this.btnEdit.TabIndex = 1;
            this.btnEdit.Text = "...";
            this.btnEdit.UseVisualStyleBackColor = true;
            this.btnEdit.Click += new System.EventHandler(this.btnEdit_Click);
            // 
            // btnOK
            // 
            this.btnOK.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnOK.Location = new System.Drawing.Point(151, 123);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnCancel.Location = new System.Drawing.Point(320, 123);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // cbxDebugMode
            // 
            this.cbxDebugMode.AutoSize = true;
            this.cbxDebugMode.Location = new System.Drawing.Point(186, 52);
            this.cbxDebugMode.Name = "cbxDebugMode";
            this.cbxDebugMode.Size = new System.Drawing.Size(156, 16);
            this.cbxDebugMode.TabIndex = 3;
            this.cbxDebugMode.Text = "调试类模式(Debug Only)";
            this.cbxDebugMode.UseVisualStyleBackColor = true;
            this.cbxDebugMode.Visible = false;
            // 
            // tabControlX
            // 
            this.tabControlX.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControlX.Controls.Add(this.tabAnaSample);
            this.tabControlX.Controls.Add(this.tabAnaGrid);
            this.tabControlX.Location = new System.Drawing.Point(1, 2);
            this.tabControlX.Name = "tabControlX";
            this.tabControlX.SelectedIndex = 0;
            this.tabControlX.Size = new System.Drawing.Size(544, 110);
            this.tabControlX.TabIndex = 4;
            // 
            // tabAnaSample
            // 
            this.tabAnaSample.Controls.Add(this.cbxDebugMode);
            this.tabAnaSample.Controls.Add(this.cbxOwnFunc);
            this.tabAnaSample.Controls.Add(this.btnEdit);
            this.tabAnaSample.Location = new System.Drawing.Point(4, 21);
            this.tabAnaSample.Name = "tabAnaSample";
            this.tabAnaSample.Padding = new System.Windows.Forms.Padding(3);
            this.tabAnaSample.Size = new System.Drawing.Size(536, 85);
            this.tabAnaSample.TabIndex = 0;
            this.tabAnaSample.Text = "采样点操作";
            this.tabAnaSample.UseVisualStyleBackColor = true;
            // 
            // tabAnaGrid
            // 
            this.tabAnaGrid.Location = new System.Drawing.Point(4, 21);
            this.tabAnaGrid.Name = "tabAnaGrid";
            this.tabAnaGrid.Padding = new System.Windows.Forms.Padding(3);
            this.tabAnaGrid.Size = new System.Drawing.Size(536, 85);
            this.tabAnaGrid.TabIndex = 1;
            this.tabAnaGrid.Text = "栅格操作";
            this.tabAnaGrid.UseVisualStyleBackColor = true;
            // 
            // SampleFilterSelectorDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(546, 158);
            this.Controls.Add(this.tabControlX);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SampleFilterSelectorDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "选择扩展操作";
            this.tabControlX.ResumeLayout(false);
            this.tabAnaSample.ResumeLayout(false);
            this.tabAnaSample.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ComboBox cbxOwnFunc;
        private System.Windows.Forms.Button btnEdit;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.CheckBox cbxDebugMode;
        private System.Windows.Forms.TabControl tabControlX;
        private System.Windows.Forms.TabPage tabAnaSample;
        private System.Windows.Forms.TabPage tabAnaGrid;
    }
}