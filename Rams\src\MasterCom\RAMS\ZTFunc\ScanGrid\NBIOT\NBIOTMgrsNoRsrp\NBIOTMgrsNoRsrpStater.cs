﻿using System.Collections.Generic;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsNoRsrpStater : NbIotMgrsWeakRsrpStater
    {
        public override void DoStat(NbIotMgrsFuncItem curFuncItem)
        {
            this.tmpFuncItem = curFuncItem;
        }

        public override void SetResultControl()
        {
            NbIotMgrsNoRsrpResult resultControl = new NbIotMgrsNoRsrpResult();
            staterName = resultControl.Desc;
            object[] values = tmpFuncItem.FuncCondtion as object[];
            SerialGridCount = (int)values[0];
            CurRsrpMax = (double)values[1];
            CheckSINR = (bool)values[2];
            CurMaxSINR = (double)values[3];
            CheckSINRType = (bool)values[4];
            resultControl.FillData(tmpFuncItem);
            resultControlList = new List<NbIotMgrsResultControlBase>() { resultControl };
        }

        protected override List<WeakGrid> GetWeakGridList(Dictionary<string, List<ScanGridInfo>> gridList)
        {
            List<WeakGrid> weakGridList = new List<WeakGrid>();
            //按栅格中最强rsrp小区判断弱覆盖栅格
            foreach (List<ScanGridInfo> scanGrid in gridList.Values)
            {
                //将栅格中的小区按rsrp降序排序
                scanGrid.Sort((x, y) => { return -x.R0_RP.CompareTo(y.R0_RP); });
                ScanGridInfo grid = scanGrid[0];
                judgeWeakGrid(weakGridList, scanGrid, grid);
            }
            return weakGridList;
        }

        private void judgeWeakGrid(List<WeakGrid> weakGridList, List<ScanGridInfo> scanGrid, ScanGridInfo grid)
        {
            if (CheckSINR)
            {
                if (CheckSINRType)
                {
                    if ((grid.R0_RP < CurRsrpMax) && (grid.R0_CINR < CurMaxSINR))
                    {
                        addWeakGrid(weakGridList, scanGrid);
                    }
                }
                else
                {
                    if ((grid.R0_RP < CurRsrpMax) || (grid.R0_CINR < CurMaxSINR))
                    {
                        addWeakGrid(weakGridList, scanGrid);
                    }
                }
            }
            else
            {
                if (grid.R0_RP < CurRsrpMax)
                {
                    addWeakGrid(weakGridList, scanGrid);
                }
            }
        }

        private void addWeakGrid(List<WeakGrid> weakGridList, List<ScanGridInfo> scanGrid)
        {
            WeakGrid weakGrid = new WeakGrid();
            weakGrid.CellGrid = scanGrid;
            weakGridList.Add(weakGrid);
        }

        public override Dictionary<string, string> GetResultData()
        {
            return resultList;
        }

        public override void Clear()
        {
            this.tmpFuncItem = null;
        }

        protected override void addResultInfo(List<AreaGridData> areaDataList, CarrierAreaResult carrierAreaResult)
        {
            foreach (var area in areaDataList)
            {
                foreach (var item in carrierAreaResult.carrierResult.AreaList)
                {
                    if (item.AreaName == area.AreaName)
                    {
                        item.SerialNoRSRPGridCoverage = carrierAreaResult.getAreaResultData(area.AreaName, area.IssuesGridCount);
                        break;
                    }
                }
            }

            carrierAreaResult.carrierResult.SerialNoRSRPGridCoverage = carrierAreaResult.getTotalResultData(serialWeakGridCount);
        }
    }
}
