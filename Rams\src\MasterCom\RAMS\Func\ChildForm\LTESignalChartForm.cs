using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class LTESignalChartForm : ChildForm
    {
        public LTESignalChartForm()
        {
            InitializeComponent();
        }

        public override void Init()
        {
            base.Init();
            MainModel.SelectedTestPointsChanged += selectedTestPointsChanged;
            Disposed += disposed;
            initializeComponent();
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                return param;
            }
        }

        private void initializeComponent()
        {
            this.chartControl1.Series[0].Points.Clear();
            this.chartControl1.ObjectHotTracked += chartControl1_ObjectHotTracked;
            this.chartControl1.ObjectSelected += chartControl1_ObjectSelected;
            this.chartControl1.MouseWheel += chartControl1_MouseWheel;

            ((PointSeriesView)this.chartControl1.Series[0].View).PointMarkerOptions.Size = this.pointSize;
            ((PointSeriesView)this.chartControl1.Series[1].View).PointMarkerOptions.Size = this.pointSize;
        }

        private void disposed(object sender, EventArgs e)
        {
            MainModel.SelectedTestPointsChanged -= selectedTestPointsChanged;
            this.chartControl1.ObjectHotTracked -= chartControl1_ObjectHotTracked;
            this.chartControl1.ObjectSelected -= chartControl1_ObjectSelected;
            this.chartControl1.MouseWheel -= chartControl1_MouseWheel;
        }

        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            Dictionary<int, float> valueDic = new Dictionary<int, float>();
            if (MainModel.SelectedTestPoints.Count > 0
                && MainModel.SelectedTestPoints[0] is ScanTestPoint_CW)
            {
                ScanTestPoint_CW testPoint = MainModel.SelectedTestPoints[0] as ScanTestPoint_CW;
                for (int i = 0; i < 250; i++)
                {
                    int? frequency = (int?)testPoint["SCAN_CW_Frequency", i];
                    float? pwr = (float?)testPoint["SCAN_CW_Pwr", i];
                    if (frequency == null || pwr == null)
                    {
                        break;
                    }
                    valueDic.Add((int)frequency, (float)pwr);
                }
                DrawSignalLine(valueDic);
            }
        }

        private void DrawSignalLine(Dictionary<int, float> valueDic)
        {
            this.chartControl1.Series[0].Points.Clear();
            this.chartControl1.Series[1].Points.Clear();
            float maxVal = float.MinValue;
            float minVal = float.MaxValue;
            foreach (int key in valueDic.Keys)
            {
                this.chartControl1.Series[0].Points.Add(new SeriesPoint(key, Math.Round(valueDic[key], 3)));
                maxVal = Math.Max(maxVal, valueDic[key]);
                minVal = Math.Min(minVal, valueDic[key]);
            }
            XYDiagram diagram = (XYDiagram)this.chartControl1.Diagram;
            diagram.AxisY.Range.MinValue = minVal - (maxVal - minVal) / 10;
            diagram.AxisY.Range.MaxValue = maxVal + (maxVal - minVal) / 10;
        }

        private void chartControl1_ObjectHotTracked(object sender, HotTrackEventArgs e)
        {
            e.Cancel = true;
        }

        private void chartControl1_ObjectSelected(object sender, HotTrackEventArgs e)
        {
            e.Cancel = true;
            if (e.Object is Series && e.AdditionalObject is SeriesPoint)
            {
                
                SeriesPoint sp = e.AdditionalObject as SeriesPoint;
                this.chartControl1.Series[1].Points.Clear();
                this.chartControl1.Series[1].Points.Add(new SeriesPoint(sp.NumericalArgument, sp.Values[0]));
            }
        }

        private void chartControl1_MouseWheel(object sender, MouseEventArgs e)
        {
            if (e.Delta == 120)
            {
                this.pointSize += 1;
            }
            else if (e.Delta == -120)
            {
                this.pointSize = this.pointSize > 1 ? this.pointSize - 1 : 1;
            }
            ((PointSeriesView)this.chartControl1.Series[0].View).PointMarkerOptions.Size = this.pointSize;
            ((PointSeriesView)this.chartControl1.Series[1].View).PointMarkerOptions.Size = this.pointSize;
        }

        private int pointSize = 5;
    }

}