﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using System.IO;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte DownLoadFileByPath_DIY = 0x0C;
    }

    public class DownLoadFileByPathMulti : DownLoadFileByPath
    {
        public DownLoadFileByPathMulti(MainModel mainModel)
            : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "下载文件（按路径文件名）"; }
        }

        public override string IconName
        {
            get { return "Images/download.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="destination">本地存放路径</param>
        /// <param name="filePath">服务器相对路径</param>
        /// <param name="fileName">文件名</param>
        public void SetFilePathFileName(string destination, string servDir, string fileName)
        {
            this.localDir = destination;
            this.servDir = servDir;
            this.fileName = fileName;
        }

        readonly Dictionary<string, List<string>> servDirFileNameDic = null;
        readonly string localSaveDir = null;
        public DownLoadFileByPathMulti(Dictionary<string, List<string>> servDirFileNameDic, string localSaveDir)
            : base(MainModel.GetInstance())
        {
            this.servDirFileNameDic = servDirFileNameDic;
            this.localSaveDir = localSaveDir;
        }

        protected override void query()
        {
            if (servDirFileNameDic == null || localSaveDir == null)
            {
                return;
            }
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitTextBox.Show("正在接收接口信息...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected virtual void prepareSearchPackage(Package package, string servDir, string fileName)
        {
            package.Command = Command.DataManage;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.DownLoadFileByPath_DIY;
            package.Content.PrepareAddParam();
            package.Content.AddParam(servDir);
            package.Content.AddParam(fileName);
        }

        private string localDir { get; set; }
        private string servDir { get; set; }
        private string fileName { get; set; }

        private void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                foreach (string dir in servDirFileNameDic.Keys)
                {
                    foreach (string file in servDirFileNameDic[dir])
                    {
                        WaitTextBox.Text = "正在接收" + file + "...";
                        prepareSearchPackage(package, dir, file);
                        clientProxy.Send();
                        string localFullPath = Path.Combine(localSaveDir, file);
                        receiveFile(clientProxy, localFullPath);
                    }
                }
            }
            catch (Exception e)
            {
                ErrorInfo += e.Message;
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        protected override void receiveFile(ClientProxy clientProxy, string fileName)
        {
            FileStream fileStream = new FileStream(fileName, FileMode.Create);
            try
            {
                Package package = clientProxy.Package;
                while (true)
                {
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.FileBegin
                        || package.Content.Type == ResponseType.FileContinue
                        || package.Content.Type == ResponseType.FileEnd)
                    {
                        package.Content.GetParamString();
                        package.Content.GetParamInt();
                        package.Content.GetParamInt();//length
                        fileStream.Write(package.Content.Buff, package.Content.CurOffset, package.Content.Buff.Length - package.Content.CurOffset);
                        if (package.Content.Type == ResponseType.FileEnd)
                        {
                            break;
                        }
                    }
                    else if (package.Content.Type == ResponseType.FileNotFound)
                    {
                        fileStream.Close();
                        fileStream.Dispose();
                        File.Delete(fileName);
                        break;
                    }
                }
            }
            finally
            {
                fileStream.Close();
            }
        }
    }
}
