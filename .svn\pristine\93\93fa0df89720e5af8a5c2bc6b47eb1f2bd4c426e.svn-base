﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class ShowCellTimeForm : ShowFuncForm
    {
        public ShowCellTimeForm(MainModel mm)
            : base(mm)
        { }
        protected override void showForm()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf==null)
            {
                System.Windows.Forms.MessageBox.Show("地图窗口不可见！");
                return;
            }
            mf.ViewHistoryCell();
        }

        public override string Name
        {
            get { return "工参显示时间设置"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19006, this.Name);
        }
    }
}
