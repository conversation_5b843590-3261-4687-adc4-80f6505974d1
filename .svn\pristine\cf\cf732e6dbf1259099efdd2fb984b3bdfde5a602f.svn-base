﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTCoverageOfCellAna
{
    public partial class ChooseModeDlg : BaseDialog
    {
        public ChooseModeDlg()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 是否只统计单个小区
        /// </summary>
        public bool IsSingleMode
        {
            get
            {
                if (radioButton1.Checked)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }
    }
}
