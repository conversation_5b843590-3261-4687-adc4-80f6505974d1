using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Util;
using Chris<PERSON>Util;
using MasterCom.NOP.CM;

namespace MasterCom.RAMS.Model
{
    public partial class CellParamConfigForm : MinCloseForm
    {
        KeyIntIdentityStringManager<Parameter> parameterManager;
        KeyIntIdentityStringManager<ParameterType> parameterTypeManager;
        public CellParamConfigForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            cbxConfigType.SelectedIndex = 0;
        }

        public void FillData()
        {
            parameterManager = MainModel.ParameterManager.GetParameterManager();
            parameterTypeManager = MainModel.ParameterManager.GetParameterTypeManager();
        }

        private void btnQuery_Click(object sender, EventArgs e)
        {
            FillData();
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            ExcelOperator.ExportObjectListViewExcel(objectListView, true);
        }
    }
}