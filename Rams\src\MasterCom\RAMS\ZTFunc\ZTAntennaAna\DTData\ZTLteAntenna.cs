﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using System.Data;
using System.Data.SqlClient;
using System.ComponentModel;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteAntenna : ZTAntennaBase
    {
        public ZTLteAntenna()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            carrierID = CarrierType.ChinaMobile;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);

            antCfgParaDic = new Dictionary<int, AntCfgSub>();
            antCfgParaSDic = new Dictionary<int, AntCfgSub>();
            BDtNbInfo = false;
            BSecAna = false;
            BSecDataExport = false;
            BSampleShow = true;
            BDTCellName = false;
            resultDic = new Dictionary<int, CellAngleData>();
            backGrounpData = new Dictionary<int, CellInfoOutPutItem>();
        }

        public override string Name
        {
            get { return "天线分析_LTE"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 23020, this.Name);
        }

        #region 变量
        readonly AntTimeCfg timeCfg = new AntTimeCfg();
        readonly Dictionary<string, CellAngleData> dicUtranCellAngelData = new Dictionary<string, CellAngleData>();
        readonly Dictionary<string, AntennaTypeStat> antennaTypeStatDic = new Dictionary<string, AntennaTypeStat>();
        public Dictionary<string, DataUnitAreaKPIQuery> cmDataUnitAreaKPIQueryDic { get; set; }
        Dictionary<int, AntennaPara> antParaEciDic = new Dictionary<int, AntennaPara>();
        Dictionary<string, AntennaPara> antParaCellNameDic = new Dictionary<string, AntennaPara>();
        Dictionary<int, CellPara> cellParaEciDic = new Dictionary<int, CellPara>();
        Dictionary<int, CellPara> cellParaEciSDic = new Dictionary<int, CellPara>();//Sector取一位数

        Dictionary<int, LteMrItem> lteMREciDic = new Dictionary<int, LteMrItem>();
        Dictionary<int, LteMrItem> lteMREciSDic = new Dictionary<int, LteMrItem>();

        //状态库天线数据
        public Dictionary<int, AntCfgSub> antCfgParaDic { get; set; }
        public Dictionary<int, AntCfgSub> antCfgParaSDic { get; set; }
        private static ZTLteAntenna instance = null;
        string strCityName = "";
        public bool BDtNbInfo { get; set; }//是否加载邻区信息
        public bool BSecAna { get; set; }//二维数据分析
        public bool BSecDataExport { get; set; }//二维数据导出
        public bool BSampleShow { get; set; }
        public bool BDTCellName { get; set; }//是否按小区查询
        List<String> cellNameList = new List<string>();//用于指定查询小区的列表

        Dictionary<int, CellAngleData> resultDic { get; set; }//后台结果集
        Dictionary<int, CellInfoOutPutItem> backGrounpData { get; set; }
        protected static readonly object lockObj = new object();
        #endregion

        public static ZTLteAntenna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLteAntenna();
                    }
                }
            }
            return instance;
        }

        protected override void query()
        {
            if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
            {
                setVarBeforQuery();
                InitRegionMop2();
                ZTAntCfgForm antCfg = new ZTAntCfgForm(true);
                if (antCfg.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                BDtNbInfo = antCfg.BDtNbInfo;
                BSecAna = antCfg.BSecAna;
                BSecDataExport = antCfg.BSecDataExport;
                BSampleShow = antCfg.BSampleShow;
                BDTCellName = antCfg.BDTCellName;
                cellNameList = antCfg.cellNameList;
            }

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                dicUtranCellAngelData.Clear();
                antennaTypeStatDic.Clear();
                MainModel.ClearDTData();
                if (!MainModel.IsBackground)
                {
                    if (!MainModel.QueryFromBackground)
                    {
                        setAntTimeCfg();
                        WaitBox.CanCancel = true;
                        WaitBox.Text = "正在查询...";
                        strCityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                        calcCellKpiStat();
                        WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                        WaitBox.Show("读取MR数据处理...", doWithCellMRData);

                        WaitBox.Show("读取小区权值数据...", doWithParaData);
                        WaitBox.Show("读取状态库天线信息...", doWithAntCfgData);
                        WaitBox.Show("读取小区性能数据...", doWithCellParaData);
                        WaitBox.Show("联合数据处理...", AnaCellAngleData);

                        dealMainUtranCellSample();
                    }
                    else
                    {
                        getBackgroundData();
                        initBackgroundImageDesc();
                    }
                    MainModel.FireSetDefaultMapSerialTheme("LTE", "RSRP");
                    dicUtranCellAngelData.Clear();
                    antennaTypeStatDic.Clear();
                }
                else
                {
                    strCityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                    doBackgroundStatByPeriod(clientProxy);
                }
            }
            catch
            {
                clientProxy.Close();
            }
            finally
            {
                MainModel.ClearDTData();
                ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic = null;
            }
        }

        private void setAntTimeCfg()
        {
            if (Condition != null)
            {
                if (condition.IsByRound)
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 00:00:00", condition.ByRoundYear, condition.ByRoundRound))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 23:59:59", condition.ByRoundYear, condition.ByRoundRound)).AddMonths(1).AddDays(-1)) / (1000L));
                }
                else
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 00:00:00", condition.Periods[0].BeginTime))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 23:59:59", condition.Periods[0].EndTime.AddDays(-1)))) / (1000L));
                }
            }
        }

        protected override void statData(ClientProxy clientProxy)
        {
            setAntTimeCfg();
            setVarBeforQuery();
            InitRegionMop2();
            calcCellKpiStat();
            queryInThread(clientProxy);
            CalcAngleValue();
            AnaCellAngleData();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                bool inRegion = mapOp2.CheckPointInRegion(tp.Longitude, tp.Latitude);

                if (inRegion && tp is LTETestPointDetail)
                {
                    anaSCell(tp);
                }
            }
            catch (Exception e)
            {
                log.Error(e.Message);
            }
        }

        private void anaSCell(TestPoint tp)
        {
            #region 主服小区分析
            int? iTac = (int?)(ushort?)tp["lte_TAC"];
            int? iEci = (int?)tp["lte_ECI"];
            int? iEarfcn = (int?)tp["lte_EARFCN"];
            int? iPCI = (int?)(short?)tp["lte_PCI"];

            LTECell lteMainCell;
            string strLteCellKey;
            bool isValid = getMainCell(tp, iTac, iEci, iEarfcn, iPCI, out lteMainCell, out strLteCellKey);
            if (!isValid)
            {
                return;
            }

            LteAntennaInfo info;
            isValid = getLteAntennaInfo(tp, out info);
            if (!isValid)
            {
                return;
            }

            List<BtsSubInfo> sampleBtsList;
            sampleBtsList = GetBtsListBySample(tp.Longitude, tp.Latitude, "4G");
            sampleBtsList.Sort();
            CellAngleData utranCellAngleData = new CellAngleData();
            if (lteMainCell == null)
            {
                if (!dicUtranCellAngelData.ContainsKey(strLteCellKey))
                {
                    utranCellAngleData = new CellAngleData();
                    utranCellAngleData.strTime = tp.DateTime.ToString("yyyy-MM-dd");
                    utranCellAngleData.strcityname = strCityName;
                    string strGridTypeName = "";
                    isContainPoint(tp.Longitude, tp.Latitude, ref strGridTypeName);
                    if (strGridTypeName == "")
                        strGridTypeName = "无网格号";
                    utranCellAngleData.strgridname = strGridTypeName;
                    utranCellAngleData.cellname = strLteCellKey;
                    utranCellAngleData.iTac = (int)iTac;
                    utranCellAngleData.iEci = (int)iEci;
                    utranCellAngleData.uarfcn = (int)iEarfcn;
                    utranCellAngleData.band = LTECell.GetBandTypeByEarfcn_BJ((int)iEarfcn);
                    CalStrNetWorkTimeRate(ref utranCellAngleData);//小区指标统计
                    dicUtranCellAngelData[strLteCellKey] = utranCellAngleData;
                }
                else
                {
                    utranCellAngleData = dicUtranCellAngelData[strLteCellKey];
                }

                info.IAngleDiffRel = 0;
                info.IAngleDiffAbs = 0;
                info.CellDistance = 0;
                dealWithCellAndAngleStat(ref utranCellAngleData, tp, lteMainCell, sampleBtsList, info, -1);
            }
            else
            {
                double dAngleDiffRel = 0;
                double dAngLeDiffAbs = 0;
                double utranCellDistance = 0;
                ZTAntFuncHelper.CellCollection col = new ZTAntFuncHelper.CellCollection()
                {
                    Cell = null,
                    TDCell = null,
                    LteCell = lteMainCell
                };
                ZTAntFuncHelper.calcSampleAngle(col, tp.Longitude, tp.Latitude, out dAngleDiffRel, out dAngLeDiffAbs, out utranCellDistance);
                if (utranCellDistance > CD.MAX_COV_DISTANCE_LTE)
                    return;

                info.IAngleDiffRel = (int)dAngleDiffRel;//相对夹角
                info.IAngleDiffAbs = (int)dAngLeDiffAbs;//相对夹角(绝对值)
                info.CellDistance = utranCellDistance;
                fillCellInfo(ref utranCellAngleData, lteMainCell, tp);
                dealWithCellAndAngleStat(ref utranCellAngleData, tp, lteMainCell, sampleBtsList, info, -1);
            }
            utranCellAngleData.sampleTotal++;
            if (BDtNbInfo)
                doWithDTNbData(tp, sampleBtsList);//邻小区天线分析
            #endregion
        }

        private bool getMainCell(TestPoint tp, int? iTac, int? iEci, int? iEarfcn, int? iPCI, out LTECell lteMainCell, out string strLteCellKey)
        {
            strLteCellKey = "";
            lteMainCell = tp.GetMainCell_LTE();
            if (iTac == null || iEci == null || iEarfcn == null || iPCI == null)
                return false;

            if (lteMainCell == null)
            {//tac eci 优先匹配
                lteMainCell = CellManager.GetInstance().GetNearestLTECellByTACCI(JavaDate.GetDateTimeFromMilliseconds(tp.Time * 1000L), iTac, iEci, tp.Longitude, tp.Latitude);
            }
            if (lteMainCell == null)
            {//tac eci 匹配不到小区，由earfcn pci匹配
                lteMainCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(JavaDate.GetDateTimeFromMilliseconds(tp.Time * 1000L), iEarfcn, iPCI, tp.Longitude, tp.Latitude);
            }

            if (BDTCellName)
            {
                if (lteMainCell == null)
                    return false;

                if (!cellNameList.Contains(lteMainCell.Name))
                    return false;
            }

            strLteCellKey = "";
            if (lteMainCell == null)
                strLteCellKey = iTac + "_" + iEci;
            else if (lteMainCell.Direction > 360)
                return false;

            return true;
        }

        private bool getLteAntennaInfo(TestPoint tp, out LteAntennaInfo info)
        {
            info = new LteAntennaInfo();
            if (tp["lte_RSRP"] == null || tp["lte_RSSI"] == null || tp["lte_SINR"] == null)
                return false;
            info.LteRsrp = (float?)tp["lte_RSRP"];
            if (info.LteRsrp > -10 || info.LteRsrp < -141)
                return false;
            info.LteRSSI = (float?)tp["lte_RSSI"];
            if (info.LteRSSI > -10 || info.LteRSSI < -141)
                return false;
            info.LteSINR = (float?)tp["lte_SINR"];
            if (info.LteSINR < -50 || info.LteSINR > 50)
                return false;
            info.LteRSRQ = (float?)tp["lte_RSRQ"];
            if (tp["lte_RSRQ"] != null)
            {
                float fRSRQ = 0;
                if (float.TryParse(tp["lte_RSRQ"].ToString(), out fRSRQ))
                    info.LteRSRQ = (float?)fRSRQ;
            }
            info.LteTA = 0;
            if (tp["lte_Uu_Timing_Advance"] != null)
            {
                float fTA = 0;
                if (float.TryParse(tp["lte_Uu_Timing_Advance"].ToString(), out fTA))
                    info.LteTA = (float?)fTA;
            }
            return true;
        }

        /// <summary>
        /// 邻小区天线分析
        /// </summary>
        private void doWithDTNbData(TestPoint tp, List<BtsSubInfo> sampleBtsList)
        {
            for (int i = 0; i < 6; i++)
            {
                #region 读取邻小区信息
                int? lteNbEarfcn, lteNbPCI;
                float? lteNbRsrp;
                int type = getValidTPInfo(tp, i, out lteNbEarfcn, out lteNbPCI, out lteNbRsrp);
                if (type == 0)
                {
                    continue;
                }
                else if (type == -1)
                {
                    return;
                }
                #endregion

                LTECell lteNbCell = tp.GetNBCell_LTE(i);
                if (lteNbCell == null)
                {//tac eci 匹配不到小区，由earfcn pci匹配
                    lteNbCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(JavaDate.GetDateTimeFromMilliseconds(tp.Time * 1000L), lteNbEarfcn, lteNbPCI, tp.Longitude, tp.Latitude);
                }

                if (lteNbCell == null || lteNbCell.Direction > 360)
                    continue;

                double dAngleDiffRel;
                double dAngLeDiffAbs;
                double utranCellDistance;
                ZTAntFuncHelper.CellCollection col = new ZTAntFuncHelper.CellCollection()
                {
                    Cell = null,
                    TDCell = null,
                    LteCell = lteNbCell
                };
                ZTAntFuncHelper.calcSampleAngle(col, tp.Longitude, tp.Latitude, out dAngleDiffRel, out dAngLeDiffAbs, out utranCellDistance);
                if (utranCellDistance > CD.MAX_COV_DISTANCE_LTE)
                    continue;

                LteAntennaInfo info = new LteAntennaInfo();
                info.IAngleDiffRel = (int)dAngleDiffRel;
                info.IAngleDiffAbs = (int)dAngLeDiffAbs;
                info.CellDistance = utranCellDistance;
                info.LteRsrp = lteNbRsrp;

                CellAngleData utranCellAngleData = new CellAngleData();
                fillCellInfo(ref utranCellAngleData, lteNbCell, tp);
                utranCellAngleData.sampleTotal++;
                dealWithNbCellAndAngleStat(ref utranCellAngleData, tp, sampleBtsList, info, i);
            }
        }

        private int getValidTPInfo(TestPoint tp, int i, out int? lteNbEarfcn, out int? lteNbPCI, out float? lteNbRsrp)
        {
            lteNbEarfcn = null;
            lteNbPCI = null;
            lteNbRsrp = null;

            if (tp["lte_NCell_EARFCN", i] == null || tp["lte_NCell_PCI", i] == null || tp["lte_NCell_RSRP", i] == null)
                return 0;

            lteNbEarfcn = (int?)tp["lte_NCell_EARFCN", i];
            if (lteNbEarfcn <= 0)
                return 0;

            lteNbPCI = (int?)(short?)tp["lte_NCell_PCI", i];
            if (lteNbPCI < 0 || lteNbPCI > 600)
                return 0;

            lteNbRsrp = (float?)tp["lte_NCell_RSRP", i];
            if (lteNbRsrp > -10 || lteNbRsrp < -141)
                return -1;

            return 1;
        }

        /// <summary>
        /// LTE小区信息填值
        /// </summary>
        private void fillCellInfo(ref CellAngleData utranCellAngleData, LTECell lteCell, TestPoint tp)
        {
            if (dicUtranCellAngelData.ContainsKey(lteCell.Name))
            {
                utranCellAngleData = dicUtranCellAngelData[lteCell.Name];
                if (ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic.ContainsKey(lteCell.Name))
                    ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic[lteCell.Name].lteNewImg = null;
            }
            else
            {
                utranCellAngleData = new CellAngleData();
                utranCellAngleData.strbscname = lteCell.BelongBTS.Name;
                utranCellAngleData.strTime = tp.DateTime.ToString("yyyy-MM-dd");
                utranCellAngleData.strcityname = strCityName;
                string strGridTypeName = "";
                isContainPoint(lteCell.Longitude, lteCell.Latitude, ref strGridTypeName);
                if (strGridTypeName == "")
                    strGridTypeName = "无网格号";
                utranCellAngleData.strgridname = strGridTypeName;
                utranCellAngleData.cellname = lteCell.Name;
                utranCellAngleData.iTac = lteCell.TAC;
                utranCellAngleData.iEci = lteCell.ECI;
                utranCellAngleData.uarfcn = lteCell.EARFCN;
                utranCellAngleData.band = LTECell.GetBandTypeByEarfcn_BJ(lteCell.EARFCN);

                utranCellAngleData.ialtitude = lteCell.Altitude;
                utranCellAngleData.iangle_dir = (lteCell.Direction);
                utranCellAngleData.iangle_ob = (lteCell.Downward);
                utranCellAngleData.cellLongitude = lteCell.Longitude;
                utranCellAngleData.cellLatitude = lteCell.Latitude;
                utranCellAngleData.btsInfoList = GetBtsList(null, null, lteCell);
                utranCellAngleData.btsInfoList.Sort();
                CalStrNetWorkTimeRate(ref utranCellAngleData);//小区指标统计
                dicUtranCellAngelData[lteCell.Name] = utranCellAngleData;
            }
        }

        /// <summary>
        /// 按小区统计
        /// </summary>
        private void dealWithCellAndAngleStat(ref CellAngleData utranCellAngleData, TestPoint tp, LTECell lteMainCell, List<BtsSubInfo> sampleBtsList, LteAntennaInfo info, int iNbIndex)
        {
            //cellDistance, lteRsrp, lteRSSI, lteSINR, lteRSRQ, lteTA, lteRSRP0, lteRSRP1, ltePathloss);
            int iCellNum = CalcCellNum(sampleBtsList, info.CellDistance);
            CellAngleDataExt cExt = utranCellAngleData.cExt;
            //float lteRSRP0 = float.MaxValue;
            //float lteRSRP1 = float.MaxValue;
            //float ltePathloss = float.MaxValue;
            dealWithCellAndAngleStatCellInfoSub(tp, ref cExt, lteMainCell, info);
            cExt.iOverCellNum += iCellNum;
            cExt.fSampleDistSum += info.CellDistance;
            cExt.fDistCellNum += CalcRadius(utranCellAngleData.btsInfoList);

            //360角度级
            CellInfoItem ciItem = utranCellAngleData.ciItem;
            calcAngleInfo(ref ciItem, info);
            ciItem.antAngleDic[info.IAngleDiffRel].DSampDist += info.CellDistance;
            ciItem.antAngleDic[info.IAngleDiffRel].DCover += iCellNum;
            ciItem.antAngleDic[info.IAngleDiffRel].ISampNum += 1;

            //天线垂直面数据
            int iLevel = ZTAntFuncHelper.getDistanceLevel(info.CellDistance);
            if (iLevel < 200)
            {
                ZTAntAngleItem vertItem;
                if (!ciItem.antVertDic.TryGetValue(iLevel, out vertItem))
                    vertItem = new ZTAntAngleItem();

                vertItem.IRsrp += (int)info.LteRsrp;
                vertItem.ISinr += (int)info.LteSINR;
                vertItem.ISampNum += 1;
                ciItem.antVertDic[iLevel] = vertItem;
            }

            //区间级
            AngleData angleData = new AngleData();
            string sectionName = ZTAntFuncHelper.GetAngleSection(info.IAngleDiffAbs);
            if (utranCellAngleData.angleDatas.ContainsKey(sectionName))
            {
                angleData = utranCellAngleData.angleDatas[sectionName];
            }
            else
            {
                utranCellAngleData.angleDatas[sectionName] = angleData;
                angleData.rsrpMax = -140;
            }
            calcSectionInfo(ref angleData, info);
            angleData.iDistCellNum += iCellNum;
            angleData.rsrpMax = (angleData.rsrpMax > info.LteRsrp ? angleData.rsrpMax : (float)info.LteRsrp);
            angleData.fSampleDistSum += info.CellDistance;

            if (BSampleShow)//存储采样点信息
            {
                utranCellAngleData.tpList.Add(tp);
                string snk = tp.SN.ToString() + tp.FileID.ToString() + tp.Time.ToString();
                if (!utranCellAngleData.tpIndexDic.ContainsKey(snk))
                    utranCellAngleData.tpIndexDic.Add(snk, iNbIndex);
            }
        }

        /// <summary>
        /// 计算小区级信息
        /// </summary>
        private void dealWithCellAndAngleStatCellInfoSub(TestPoint tp, ref CellAngleDataExt cExt
            , LTECell lteMainCell, LteAntennaInfo info)
        {
            #region 小区基本统计
            getRsrp(tp, cExt, info);
            getPathloss(tp, cExt, info);
            getDLFrequency(tp, cExt, lteMainCell);
            cExt.rsrpSum += (int)info.LteRsrp;
            cExt.rsrpNum++;
            cExt.rssiSum += (int)info.LteRSSI;
            cExt.rssiNum++;
            cExt.sinrSum += (int)info.LteSINR;
            cExt.sinrNum++;
            if ((int)info.LteSINR < -3)
                cExt.sinrNum_3 += 1;
            if ((int)info.LteSINR < -5)
                cExt.sinrNum_5 += 1;

            if (info.LteRSRQ != null && info.LteRSRQ >= -40 && info.LteRSRQ <= 40)
            {
                cExt.rsrqSum += (int)info.LteRSRQ;
                cExt.rsrqNum++;
            }
            if (info.LteTA != null && info.LteTA >= 0)
            {
                cExt.TASum += (int)info.LteTA;
                cExt.TANum++;
            }
            #endregion

            #region 小区扩展统计
            if (info.IAngleDiffAbs <= 60)
            {
                add60AngleData(cExt, info);
            }
            else if (info.IAngleDiffAbs <= 150)
            {
                add150AngleData(cExt, info);
            }
            else if (info.IAngleDiffAbs <= 180)
            {
                add180AngleData(cExt, info);
            }
            #endregion
        }

        private static void getRsrp(TestPoint tp, CellAngleDataExt cExt, LteAntennaInfo info)
        {
            if (tp["lte_RSRP_Rx0"] != null)
            {
                info.LteRSRP0 = (float)(float?)tp["lte_RSRP_Rx0"];
                if (info.LteRSRP0 >= -140 && info.LteRSRP0 <= 25)
                {
                    cExt.rsrp0Sum += (int)info.LteRSRP0;
                    cExt.rsrp0Num++;
                }
            }
            if (tp["lte_RSRP_Rx1"] != null)
            {
                info.LteRSRP1 = (float)(float?)tp["lte_RSRP_Rx1"];
                if (info.LteRSRP1 >= -140 && info.LteRSRP1 <= 25)
                {
                    cExt.rsrp1Sum += (int)info.LteRSRP1;
                    cExt.rsrp1Num++;
                }
            }
            if (info.LteRSRP0 != float.MaxValue && info.LteRSRP1 != float.MaxValue)
            {
                cExt.rscpDiffSum += Math.Abs(info.LteRSRP0 - info.LteRSRP1);
                cExt.rscpDiffNum++;
            }
        }

        private static void getPathloss(TestPoint tp, CellAngleDataExt cExt, LteAntennaInfo info)
        {
            if (tp["lte_Pathloss"] != null)
            {
                info.LtePathloss = (float)int.Parse(tp["lte_Pathloss"].ToString());
                if (info.LtePathloss >= 0 && info.LtePathloss <= 200)
                {
                    cExt.fpathlossSum += (int)info.LtePathloss;
                    cExt.fpathlossNum++;
                }
            }
        }

        private void getDLFrequency(TestPoint tp, CellAngleDataExt cExt, LTECell lteMainCell)
        {
            if (tp["lte_DL_Frequency"] != null && lteMainCell != null)
            {
                double fPathlossDistance = getDLPathlossDistance(tp, float.Parse(tp["lte_DL_Frequency"].ToString())
                    , lteMainCell.BelongBTS, lteMainCell.Altitude);
                if (fPathlossDistance >= 0 && fPathlossDistance <= 30000)
                {
                    cExt.fpathlossDistanceSum += fPathlossDistance;
                    cExt.fpathlossDistanceNum++;
                }
            }
        }

        private static void add60AngleData(CellAngleDataExt cExt, LteAntennaInfo info)
        {
            cExt.iSample0To60Num += 1;
            cExt.fRsrp0To60Sum += (float)info.LteRsrp;
            cExt.mainRsrpList.Add((int)info.LteRsrp);
            if (info.LteRsrp >= -110 && info.LteRsrp <= -45 && info.LteSINR >= -3 && info.LteSINR < 40)
                cExt.iRsrpF110SinrF3Sample0To60Num += 1;
            if (info.LteRsrp >= -110 && info.LteRsrp <= -45)
                cExt.iRsrpF110Sample0To60Num += 1;
            if (info.LteSINR >= -3 && info.LteSINR < 40)
                cExt.iSinrF3Sample0To60Num += 1;
        }

        private static void add150AngleData(CellAngleDataExt cExt, LteAntennaInfo info)
        {
            cExt.iSample60To150Num += 1;
            cExt.fRsrp60To150Sum += (float)info.LteRsrp;
            if (info.LteRsrp >= -110 && info.LteRsrp <= -45 && info.LteSINR >= -3 && info.LteSINR < 40)
                cExt.iRsrpF110SinrF3Sample60To150Num += 1;
            if (info.LteRsrp >= -110 && info.LteRsrp <= -45)
                cExt.iRsrpF110Sample60To150Num += 1;
            if (info.LteSINR >= -3 && info.LteSINR < 40)
                cExt.iSinrF3Sample60To150Num += 1;
        }

        private static void add180AngleData(CellAngleDataExt cExt, LteAntennaInfo info)
        {
            cExt.backRsrpList.Add((int)info.LteRsrp);
            cExt.iSample150To180Num += 1;
            cExt.fRsrp150To180Sum += (float)info.LteRsrp;
            if (info.LteRsrp >= -110 && info.LteRsrp <= -45 && info.LteSINR >= -3 && info.LteSINR < 40)
                cExt.iRsrpF110SinrF3Sample150To180Num += 1;
            if (info.LteRsrp >= -110 && info.LteRsrp <= -45)
                cExt.iRsrpF110Sample150To180Num += 1;
            if (info.LteSINR >= -3 && info.LteSINR < 40)
                cExt.iSinrF3Sample150To180Num += 1;
        }

        /// <summary>
        /// 按邻小区统计
        /// </summary>
        private void dealWithNbCellAndAngleStat(ref CellAngleData utranCellAngleData, TestPoint tp
            , List<BtsSubInfo> sampleBtsList, LteAntennaInfo info, int iNbIndex)
        {
            int iCellNum = CalcCellNum(sampleBtsList, info.CellDistance);
            CellAngleDataExt cExt = utranCellAngleData.cExt;
            cExt.rsrpSum += (int)info.LteRsrp;
            cExt.rsrpNum++;
            cExt.iOverCellNum += iCellNum;
            cExt.fSampleDistSum += info.CellDistance;
            cExt.fDistCellNum += CalcRadius(utranCellAngleData.btsInfoList);

            //360角度级
            CellInfoItem ciItem = utranCellAngleData.ciItem;
            ZTAntAngleItem angleItem;
            if (!ciItem.antAngleDic.TryGetValue(info.IAngleDiffRel, out angleItem))
                angleItem = new ZTAntAngleItem();

            angleItem.IRsrp += (int)info.LteRsrp;
            angleItem.DSampDist += info.CellDistance;
            angleItem.DCover += iCellNum;
            angleItem.ISampNum += 1;
            ciItem.antAngleDic[info.IAngleDiffRel] = angleItem;

            //天线垂直面数据
            int iLevel = ZTAntFuncHelper.getDistanceLevel(info.CellDistance);
            if (iLevel < 200)
            {
                ZTAntAngleItem vertItem;
                if (!ciItem.antVertDic.TryGetValue(iLevel, out vertItem))
                    vertItem = new ZTAntAngleItem();

                vertItem.IRsrp += (int)info.LteRsrp;
                vertItem.ISampNum += 1;
                ciItem.antVertDic[iLevel] = vertItem;
            }

            //区间级
            AngleData angleData = new AngleData();
            string sectionName = ZTAntFuncHelper.GetAngleSection(info.IAngleDiffAbs);
            if (utranCellAngleData.angleDatas.ContainsKey(sectionName))
            {
                angleData = utranCellAngleData.angleDatas[sectionName];
            }
            else
            {
                utranCellAngleData.angleDatas[sectionName] = angleData;
                angleData.rsrpMax = -140;
            }
            angleData.rsrpSum += (int)info.LteRsrp;
            angleData.rsrpNum++;
            angleData.iDistCellNum += iCellNum;
            angleData.rsrpMax = (angleData.rsrpMax > info.LteRsrp ? angleData.rsrpMax : (float)info.LteRsrp);
            angleData.fSampleDistSum += info.CellDistance;

            if (BSampleShow)//存储采样点信息
            {
                utranCellAngleData.tpList.Add(tp);
                string snk = tp.SN.ToString() + tp.FileID.ToString() + tp.Time.ToString();
                if (!utranCellAngleData.tpIndexDic.ContainsKey(snk))
                    utranCellAngleData.tpIndexDic.Add(snk, iNbIndex);
            }
        }

        /// <summary>
        /// 小区级KPI统计
        /// </summary>
        private void calcCellKpiStat()
        {
            ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
            DIYCellsOfRegionStatLteAnten cellOfRegionStatLteAnten = new DIYCellsOfRegionStatLteAnten(mainModel);
            cellOfRegionStatLteAnten.SetQueryCondition(condition);
            cellOfRegionStatLteAnten.Query();
        }

        /// <summary>
        /// 360度值计算及分析
        /// </summary>
        private void calcAngleInfo(ref CellInfoItem ciItem, LteAntennaInfo info)
        {
            ZTAntAngleItem angleItem;
            if (!ciItem.antAngleDic.TryGetValue(info.IAngleDiffRel, out angleItem))
                angleItem = new ZTAntAngleItem();

            angleItem.IRsrp += (int)info.LteRsrp;
            if (info.LteRSRP0 != float.MaxValue)
            {
                angleItem.IRsrp0 += (int)info.LteRSRP0;
            }
            if (info.LteRSRP1 != float.MaxValue)
            {
                angleItem.IRsrp1 += (int)info.LteRSRP1;
            }
            if (info.LtePathloss != float.MaxValue)
            {
                angleItem.IPathlsos += (int)info.LtePathloss;
            }

            angleItem.IRssi += (int)info.LteRSSI;
            angleItem.ISinr += (int)info.LteSINR;

            if ((int)info.LteSINR < -3)
            {
                angleItem.ISinr3 += (int)info.LteSINR;
            }

            if (info.LteRSRQ >= -40 && info.LteRSRQ <= 40)
            {
                angleItem.IRsrq += (int)info.LteRSRQ;
            }
            ciItem.antAngleDic[info.IAngleDiffRel] = angleItem;
        }

        public class LteAntennaInfo
        {
            public int IAngleDiffAbs { get; set; }
            public int IAngleDiffRel { get; set; }

            public double CellDistance { get; set; }
            public float? LteRsrp { get; set; }
            public float? LteRSSI { get; set; }
            public float? LteSINR { get; set; }
            public float? LteRSRQ { get; set; }
            public float? LteTA { get; set; }

            public float LteRSRP0 { get; set; } = float.MaxValue;
            public float LteRSRP1 { get; set; } = float.MaxValue;
            public float LtePathloss { get; set; } = float.MaxValue;
        }

        /// <summary>
        /// 区间级值计算及分析
        /// </summary>
        private void calcSectionInfo(ref AngleData angleData, LteAntennaInfo info)
        {
            angleData.rsrpSum += (int)info.LteRsrp;
            angleData.rsrpNum++;

            if (info.LteRSRP0 != float.MaxValue)
            {
                angleData.rsrp0Sum += (int)info.LteRSRP0;
                angleData.rsrp0Num++;
            }
            if (info.LteRSRP1 != float.MaxValue)
            {
                angleData.rsrp1Sum += (int)info.LteRSRP1;
                angleData.rsrp1Num++;
            }
            if (info.LteRSRP0 != float.MaxValue && info.LteRSRP1 != float.MaxValue)
            {
                angleData.rsrpDiffSum += Math.Abs(info.LteRSRP0 - info.LteRSRP1);
                angleData.rsrpDiffNum++;
            }

            if (info.LtePathloss != float.MaxValue)
            {
                angleData.fpathlossSum += (int)info.LtePathloss;
                angleData.fpathlossNum++;
            }

            angleData.rssiSum += (int)info.LteRSSI;
            angleData.rssiNum++;

            angleData.sinrSum += (int)info.LteSINR;
            angleData.sinrNum++;
            if ((int)info.LteSINR < -3)
            {
                angleData.sinrNum_3++;
            }
            if ((int)info.LteSINR < -5)
            {
                angleData.sinrNum_5++;
            }

            if (info.LteRSRQ >= -40 && info.LteRSRQ <= 40)
            {
                angleData.rsrqSum += (int)info.LteRSRQ;
                angleData.rsrqNum++;
            }
            if (info.LteTA != null && info.LteTA >= 0)
            {
                angleData.TASum += (int)info.LteTA;
                angleData.TANum++;
            }

            if (angleData.iDistCellNum >= 4)
            {
                angleData.iOverBigThan4SampleNum++;
            }

            //用于天线问题类型分析
            addRangeRsrp(angleData, info);

            addTop5Rsrp(angleData, info);
        }

        private static void addRangeRsrp(AngleData angleData, LteAntennaInfo info)
        {
            if (info.CellDistance < 500)
            {
                if (info.LteRsrp >= -140 && info.LteRsrp < 40)
                {
                    angleData.rsrpSum0_500 += (float)info.LteRsrp;
                    angleData.rsrpSampleNum0_500++;
                }
            }
            else if (info.CellDistance >= 500 && info.CellDistance < 1000)
            {
                if (info.LteRsrp >= -140 && info.LteRsrp < 40)
                {
                    angleData.rsrpSum500_1000 += (float)info.LteRsrp;
                    angleData.rsrpSampleNum500_1000++;
                }
            }
            else
            {
                //
            }
        }

        private void addTop5Rsrp(AngleData angleData, LteAntennaInfo info)
        {
            if (angleData.top5RsrpList.Count >= 5)
            {
                angleData.top5RsrpList.Sort();
                if (angleData.top5RsrpList[0] < info.LteRsrp)
                {
                    angleData.top5RsrpList[0] = (int)(info.LteRsrp);
                }
            }
            else
            {
                angleData.top5RsrpList.Add((int)(info.LteRsrp));
            }
        }

        /// <summary>
        /// 查询小区性能参数数据
        /// </summary>
        private void doWithCellParaData()
        {
            WaitBox.CanCancel = true;
            DiyCellPara cellPara = new DiyCellPara(MainModel, timeCfg);
            cellPara.SetQueryCondition(condition);
            cellPara.Query();
            cellParaEciDic = cellPara.cellParaEciDic;
            cellParaEciSDic = cellPara.cellParaEciSDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 查询状态库天线数据
        /// </summary>
        private void doWithAntCfgData()
        {
            WaitBox.CanCancel = true;
            DiyCfgPara cellPara = new DiyCfgPara(MainModel);
            cellPara.Query();
            antCfgParaDic = cellPara.antCfgParaDic;
            antCfgParaSDic = cellPara.antCfgParaSDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 查询小区LTEMR数据
        /// </summary>
        private void doWithCellMRData()
        {
            WaitBox.CanCancel = true;
            DiyLTECellMR lteCelMR = new DiyLTECellMR(MainModel, timeCfg);
            lteCelMR.SetQueryCondition(condition);
            lteCelMR.Query();
            lteMREciDic = lteCelMR.lteMREciDic;
            lteMREciSDic = lteCelMR.lteMREciSDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 查询小区参数数据
        /// </summary>
        private void doWithParaData()
        {
            WaitBox.CanCancel = true;
            DiyAntennaPara antPara = new DiyAntennaPara(MainModel, timeCfg);
            antPara.SetQueryCondition(condition);
            antPara.Query();
            antParaEciDic = antPara.antParaEciDic;
            antParaCellNameDic = antPara.antParaCellNameDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 分析小区的角度数组，进行平滑化处理
        /// </summary>
        private void AnaCellAngleData()
        {
            int iStart = 0;
            int iEnd = 360;
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 0;
            int iCount = dicUtranCellAngelData.Count;
            int iNum = 0;

            foreach (string strCellName in dicUtranCellAngelData.Keys)
            {
                try
                {
                    if (WaitBox.CancelRequest)
                        break;
                    WaitBox.ProgressPercent = (int)(100 * ((iNum * 1.0) / iCount));

                    dealCellAngleData(iStart, iEnd, strCellName); 
                    iNum++;
                }
                catch (Exception e)
                {
                    log.Error(e.Message);
                }
            }

            WaitBox.Close();
        }

        private void dealCellAngleData(int iStart, int iEnd, string strCellName)
        {
            #region 采样点的覆盖波动处理
            CellAngleData caData = dicUtranCellAngelData[strCellName];
            double[] rsrpPathLossArray = new double[360];//修正距离损耗RSRP
            double[] rsrpUnifiedArray = new double[360];//归一化RSRP
            double[] rsrpNewArray = new double[360];//平滑RSRP

            int[] rsrpArray = caData.ciItem.rsrpArray;
            double[] sampleDistArray = caData.ciItem.sampArray;
            int[] sampleNumArray = caData.ciItem.sampNumArray;

            double dSumValue = 0;
            double dNewValue = 0;
            for (int i = iStart; i < iEnd; i++)
            {
                double dTmpRsrp = (double)sampleNumArray[i] == 0 ? -140 : rsrpArray[i] / sampleNumArray[i];
                double dTmpDist = Math.Round(sampleNumArray[i] == 0 ? 0 : (sampleDistArray[i] / sampleNumArray[i]), 2) / 1000;
                //修正自由空间路损
                double dFreeSpacePathLoss = dTmpRsrp == -140 ? 0 : 33 * Math.Log10(dTmpDist);
                double dDistancePathLoss = dTmpRsrp + dFreeSpacePathLoss;
                rsrpPathLossArray[i] = dDistancePathLoss;
                double dTmpValue = Math.Pow(10, dDistancePathLoss / 10);
                dSumValue += dTmpValue;
            }
            dNewValue = 10 * Math.Log10(dSumValue / 360);
            setAntAngle(iStart, iEnd, caData, rsrpPathLossArray, rsrpUnifiedArray, rsrpNewArray, dNewValue);
            #endregion

            #region 权值理想覆盖模型
            int iNewEci = (caData.iEci / 256) * 256 + ((caData.iEci % 256) % 10);
            AntennaPara antPara;
            if (!antParaEciDic.TryGetValue(iNewEci, out antPara)
                && !antParaCellNameDic.TryGetValue(caData.cellname, out antPara))
            {
                antPara = new AntennaPara();
            }
            caData.antPara = antPara;
            AntParaItem ant1Item = new AntParaItem(antPara.strbandtype, antPara.drangeport1, antPara.drangeport2, antPara.drangeport3, antPara.drangeport4, antPara.strdevvender);
            ant1Item.Init(antPara.dphaseport1, antPara.dphaseport2, antPara.dphaseport3, antPara.dphaseport4);
            AntParaItem ant2Item = new AntParaItem(antPara.strbandtype, antPara.drangeport5, antPara.drangeport6, antPara.drangeport7, antPara.drangeport8, antPara.strdevvender);
            ant2Item.Init(antPara.dphaseport5, antPara.dphaseport6, antPara.dphaseport7, antPara.dphaseport8);

            caData.ciItem.model1Array = ant1Item.getPowerArray();
            caData.ciItem.model2Array = ant2Item.getPowerArray();
            caData.ciItem.modelMaxArray = ZTAntFuncHelper.getMaxPowerArray(caData.ciItem.model1Array, caData.ciItem.model2Array);
            LongLat ll = new LongLat();
            ll.fLongitude = (float)(caData.cellLongitude);
            ll.fLatitude = (float)(caData.cellLatitude);
            int iMaxValue = -50;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(caData.ciItem.newRsrpArray, ref iMaxValue, ref iMinValue);
            caData.longLatTestList = ZTAntFuncHelper.getCellEmulateCoverTest(ll, caData.ciItem.newRsrpArray, iMaxValue, iMinValue, caData.iangle_dir);

            int iMaxValue2 = -50;
            int iMinValue2 = 50;//未用上
            ZTAntFuncHelper.getMaxAndMinValue(caData.ciItem.modelMaxArray, ref iMaxValue2, ref iMinValue2);
            caData.longLatModelList = ZTAntFuncHelper.getCellEmulateCoverModel(ll, caData.ciItem.modelMaxArray, iMaxValue2, iMinValue, caData.iangle_dir);

            #endregion

            #region 小区性能数据及状态库数据

            //小区性能数据
            CellPara cellPara;
            if (!cellParaEciDic.TryGetValue(iNewEci, out cellPara)
                && !cellParaEciSDic.TryGetValue(iNewEci, out cellPara))
            {
                cellPara = new CellPara();
            }
            caData.cellPara = cellPara;

            //状态库天线数据
            AntCfgSub antCfg;
            if (!antCfgParaDic.TryGetValue(iNewEci, out antCfg)
                && !antCfgParaSDic.TryGetValue(iNewEci, out antCfg))
            {
                antCfg = new AntCfgSub();
            }
            caData.antCfg = antCfg;
            LteMrItem lteMRItem;
            if (!lteMREciDic.TryGetValue(iNewEci, out lteMRItem)
                && !lteMREciSDic.TryGetValue(iNewEci, out lteMRItem))
            {
                lteMRItem = new LteMrItem();
            }
            caData.lteMRItem = lteMRItem;
            #endregion
        }

        private void setAntAngle(int iStart, int iEnd, CellAngleData caData, double[] rsrpPathLossArray, double[] rsrpUnifiedArray, double[] rsrpNewArray, double dNewValue)
        {
            for (int i = iStart; i < iEnd; i++)
            {
                double dDistancePathLoss = rsrpPathLossArray[i];
                double dUnifiedValue = dDistancePathLoss > -120 ? dDistancePathLoss - dNewValue : -20;
                rsrpUnifiedArray[i] = dUnifiedValue;
            }
            for (int i = iStart; i < iEnd; i++)
            {
                double iNewRsrp = ZTAntFuncHelper.getAvgRsrp(rsrpUnifiedArray, i);
                rsrpNewArray[i] = iNewRsrp;
                if (caData.ciItem.antAngleDic.ContainsKey(i))
                    caData.ciItem.antAngleDic[i].DRsrpNew = iNewRsrp;
                else
                {
                    ZTAntAngleItem newRsrpItem = new ZTAntAngleItem();
                    newRsrpItem.DRsrpNew = iNewRsrp;
                    caData.ciItem.antAngleDic[i] = newRsrpItem;
                }
            }
        }

        /// <summary>
        /// 计算各瓣角的值
        /// </summary>
        private void CalcAngleValue()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 0;
            int iCount = dicUtranCellAngelData.Count;
            int iNum = 0;
            resultDic.Clear();
            if (Condition.IsByRound)
            {
                DateTime sTime = DateTime.Parse(string.Format("{0}/{1:00}/01 00:00:00", Condition.ByRoundYear, Condition.ByRoundRound));
                DateTime eTime = sTime.AddMonths(1).AddSeconds(-1);
                TimePeriod tp = new TimePeriod();
                tp.SetPeriod(sTime, eTime);
                Condition.Periods.Clear();
                Condition.Periods.Add(tp);
            }

            foreach (string strCellName in dicUtranCellAngelData.Keys)
            {
                try
                {
                    if (WaitBox.CancelRequest)
                        break;
                    WaitBox.ProgressPercent = (int)(100 * ((iNum++ * 1.0) / iCount));
                    
                    CellAngleData caData = dicUtranCellAngelData[strCellName];
                    int iEci = caData.iEci;
                    resultDic.Add(iEci, caData);
                }
                catch (Exception e)
                {
                    log.Error(e.Message);
                }
            }
            WaitBox.Close();
        }

        /// <summary>
        /// 设置需要的字段
        /// </summary>
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param;
            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_DL_Frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP_Rx0";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP_Rx1";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_Pathloss";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRQ";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSSI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_Uu_Timing_Advance";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            //***********************************邻区信息*********************************
            for (int i = 0; i < 6; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "lte_NCell_EARFCN";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_NCell_PCI";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_NCell_RSRP";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                columnsDef.Add((object)param);
            }
            //****************************************************************************

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LteAntenna");
            tmpDic.Add("themeName", (object)"lte_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void fillContentNeeded_Sample(Package package)
        {
            if (curSelDIYSampleGroup == null)
            {
                throw (new Exception("没有选择的参数指标！"));
            }
            List<ColumnDefItem> colDefList = getNeededColumnDefList(curSelDIYSampleGroup);
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,1,52,0,1,53,");//isampleid
            for (int i = 0; i < colDefList.Count; i++)
            {
                ColumnDefItem sid = colDefList[i];
                sbuilder.Append(sid.GetTriIdStr());
                if (i < colDefList.Count - 1)
                {
                    sbuilder.Append(",");
                }
            }
            package.Content.AddParam(sbuilder.ToString());
        }

        ///<summary>
        ///显示结果窗体
        ///</summary>
        private void FireShowResultForm(List<List<NPOIRow>> nrDatasList, List<string> sheetNames)
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteAntennaForm).FullName);
            LteAntennaForm form = obj == null ? null : obj as LteAntennaForm;
            if (form == null || form.IsDisposed)
            {
                form = new LteAntennaForm(MainModel);
            }
            form.nrDatasList = nrDatasList;
            form.sheetNames = sheetNames;

            form.FillData(dicUtranCellAngelData);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        #region 数据整理及导出
        /// <summary>
        /// 按主服导出数据
        /// </summary>
        private void dealMainUtranCellSample()
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            NPOIRow nr1 = new NPOIRow();
            nr1.cellValues = GetExcelSheet1ColName();
            datas.Add(nr1);

            List<NPOIRow> data2s = new List<NPOIRow>();
            NPOIRow nr2 = new NPOIRow();
            nr2.cellValues = GetExcelSheet2ColName();
            data2s.Add(nr2);

            List<NPOIRow> data8s = new List<NPOIRow>();
            NPOIRow nr8 = new NPOIRow();
            nr8.cellValues = GetExcelSheet4ColName();
            data8s.Add(nr8);

            List<string> listSection = new List<string>();
            listSection.Add("[0,15]");
            listSection.Add("(15,30]");
            listSection.Add("(30,60]");
            listSection.Add("(60,90]");
            listSection.Add("(90,120]");
            listSection.Add("(120,150]");
            listSection.Add("(150,180]");

            int count = 0;
            anaAntennaProblemTypeByCell();

            foreach (CellAngleData data in dicUtranCellAngelData.Values)
            {
                foreach (string section in listSection)
                {
                    datas.AddRange(FillSetionValue(data, section));
                }
                data2s.AddRange(FillAngleValue(data));
                NPOIRow nr9 = new NPOIRow();
                nr9.cellValues = FillCellValue(data, count);
                data8s.Add(nr9);
                count++;
            }

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);
            nrDatasList.Add(data2s);
            nrDatasList.Add(data8s);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("覆盖角度区间化统计分析");
            sheetNames.Add("天线角度级采样数据分析");
            sheetNames.Add("小区权值设置及覆盖统计");

            FireShowResultForm(nrDatasList, sheetNames);
        }

        private List<object> GetExcelSheet1ColName()
        {
            List<object> cols = new List<object>();
            cols.Add("小区名称");
            cols.Add("方向角偏差区间");
            cols.Add("区间采样点数");
            cols.Add("采样点占比");
            cols.Add("区间平均RSRP(dBm)");
            cols.Add("区间平均RSRP0(dBm)");
            cols.Add("区间平均RSRP1(dBm)");
            cols.Add("区间△RSRP(dBm)");
            cols.Add("区间最大RSRP(dBm)");
            cols.Add("区间RSSI平均");
            cols.Add("区间SINR平均");
            cols.Add("区间RSRQ平均");
            cols.Add("区间平均TA");
            cols.Add("区间平均通信距离");
            cols.Add("区间过覆盖指数");

            cols.Add("[0,30]区间与(150,180]区间最强PCCPCH的差值");
            cols.Add("频点");
            cols.Add("频段");
            cols.Add("总采样点数");
            cols.Add("天线类型");
            cols.Add("下倾角");
            cols.Add("挂高");
            cols.Add("方向角");
            cols.Add("小区RSCP平均");
            cols.Add("小区RSSI平均");
            cols.Add("小区SINR平均");
            cols.Add("小区RSRQ平均");
            cols.Add("小区平均通信距离");
            cols.Add("小区过覆盖指数");
            cols.Add("小区区域站点密集指数");
            cols.Add("小区平均TA");
            return cols;
        }

        private List<object> GetExcelSheet2ColName()
        {
            List<object> col2s = new List<object>();
            col2s.Add("小区名称");
            col2s.Add("指标项");
            for (int i = 0; i < 360; i++)
            {
                col2s.Add(i.ToString());
            }
            return col2s;
        }

        private List<object> GetExcelSheet4ColName()
        {
            List<object> col8s = new List<object>();
            col8s.Add("序号");
            col8s.Add("时间");
            col8s.Add("地市");
            col8s.Add("所在网格");
            col8s.Add("BSC名");
            col8s.Add("小区名");
            col8s.Add("频点标示");
            col8s.Add("分析结果");
            col8s.Add("采样点总数");
            col8s.Add("小区RSRP均值");
            col8s.Add("小区RSRP0均值");
            col8s.Add("小区RSRP1均值");
            col8s.Add("小区△RSRP均值");
            col8s.Add("小区PathLoss均值");
            col8s.Add("小区路损指标均值");
            col8s.Add("小区SINR<=-3占比(%)");
            col8s.Add("小区C/I平均");
            col8s.Add("小区平均通信距离");
            col8s.Add("小区过覆盖指数");
            col8s.Add("小区区域站点密集指数");
            col8s.Add("小区平均TA");
            col8s.Add("小区传输模式(TM=1)时长占比");
            col8s.Add("小区传输模式(TM=2)时长占比");
            col8s.Add("小区传输模式(TM=3)时长占比");
            col8s.Add("小区传输模式(TM=4)时长占比");
            col8s.Add("小区传输模式(TM=5)时长占比");
            col8s.Add("小区传输模式(TM=6)时长占比");
            col8s.Add("小区传输模式(TM=7)时长占比");
            col8s.Add("小区传输模式(TM=8)时长占比");
            col8s.Add("小区单流时长占比");
            col8s.Add("小区双流时长占比");
            col8s.Add("小区总时长");

            col8s.Add("覆盖率(RSRP≥-110&SINR>=-3)");
            col8s.Add("LTE覆盖率(RSRP≥-110)");
            col8s.Add("LTE覆盖率(SINR>=-3)");

            col8s.Add("±(0,60°)范围内采样点比例");
            col8s.Add("±(0,60°)范围内小区平均RSRP");
            col8s.Add("±(0,60°)范围内覆盖率(RSRP≥-110&SINR>=-3)");
            col8s.Add("±(0,60°)范围内LTE覆盖率(RSRP≥-110)");
            col8s.Add("±(0,60°)范围内LTE覆盖率(SINR>=-3)");
            col8s.Add("±(60,150°)范围内采样点比例");
            col8s.Add("±(150,180°)范围内采样点比例");
            col8s.Add("前后比");

            col8s.Add("主设备厂家");
            col8s.Add("CGI");
            col8s.Add("波束宽度");
            col8s.Add("覆盖类型");
            col8s.Add("Gmax");
            col8s.Add("3dB功率角");
            col8s.Add("6dB功率角");

            col8s.Add("端口1幅度");
            col8s.Add("端口2幅度");
            col8s.Add("端口3幅度");
            col8s.Add("端口4幅度");
            col8s.Add("端口5幅度");
            col8s.Add("端口6幅度");
            col8s.Add("端口7幅度");
            col8s.Add("端口8幅度");

            col8s.Add("端口1相位");
            col8s.Add("端口2相位");
            col8s.Add("端口3相位");
            col8s.Add("端口4相位");
            col8s.Add("端口5相位");
            col8s.Add("端口6相位");
            col8s.Add("端口7相位");
            col8s.Add("端口8相位");
            col8s.Add("是否(8通道)智能天线");

            col8s.Add("预置下倾角");
            col8s.Add("机械下倾角");
            col8s.Add("电调下倾角");
            col8s.Add("挂高");

            col8s.Add("上行吞吐量");
            col8s.Add("下行吞吐量");
            col8s.Add("无线接通率");
            col8s.Add("无线掉线率");
            col8s.Add("切换成功率");
            col8s.Add("ERAB建立成功率");
            col8s.Add("ERAB掉线率");

            col8s.Add("RSRP均值");
            col8s.Add("SINR均值");
            col8s.Add("95覆盖率");
            col8s.Add("110覆盖率");
            return col8s;
        }

        /// <summary>
        /// 按区拆分导出数据
        /// </summary>
        private List<NPOIRow> FillSetionValue(CellAngleData data, string section)
        {
            List<NPOIRow> dataNpoi = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            List<object> objs = new List<object>();

            float rsxp15Max = -140;
            if (data.angleDatas.ContainsKey("[0,15]"))
            {
                rsxp15Max = data.angleDatas["[0,15]"].rsrpMax;
            }

            if (data.angleDatas.ContainsKey("(15,30]")
                && data.angleDatas["(15,30]"].rsrpMax > rsxp15Max)
            {
                rsxp15Max = data.angleDatas["(15,30]"].rsrpMax;
            }

            objs.Add(data.cellname);
            //objs.Add(data.strProblemType);

            objs.Add(section);
            if (data.angleDatas.ContainsKey(section))
            {
                addAngleData(data, section, objs);
            }
            else
            {
                objs.AddRange(getDefaultValue(13));
            }

            if (data.angleDatas.ContainsKey("(150,180]") && rsxp15Max > -140)
            {
                objs.Add(rsxp15Max - data.angleDatas["(150,180]"].rsrpMax);
            }
            else
            {
                objs.Add("");
            }
            objs.Add(data.uarfcn);//频点
            objs.Add(data.band.ToString().Split('_')[0]);//频段
            objs.Add(data.sampleTotal);//总采样点
            objs.Add(data.anaType);//天线类型
            objs.Add(data.iangle_ob);//下倾角
            objs.Add(data.ialtitude);//挂高
            objs.Add(data.iangle_dir);//方位角

            addExt(data, objs);

            nr.cellValues = objs;
            dataNpoi.Add(nr);

            return dataNpoi;
        }

        private static void addAngleData(CellAngleData data, string section, List<object> objs)
        {
            AngleData angleData = data.angleDatas[section];
            objs.Add(angleData.rsrpNum);
            objs.Add(Math.Round(data.sampleTotal == 0 ? 0 : angleData.rsrpNum * 100.0 / data.sampleTotal, 2));
            objs.Add(Math.Round(angleData.rsrpNum == 0 ? 0 : angleData.rsrpSum * 1.0 / angleData.rsrpNum, 2));
            objs.Add(Math.Round(angleData.rsrp0Num == 0 ? 0 : angleData.rsrp0Sum * 1.0 / angleData.rsrp0Num, 2));
            objs.Add(Math.Round(angleData.rsrp1Num == 0 ? 0 : angleData.rsrp1Sum * 1.0 / angleData.rsrp1Num, 2));
            objs.Add(Math.Round(angleData.rsrpDiffNum == 0 ? 0 : angleData.rsrpDiffSum * 1.0 / angleData.rsrpDiffNum, 2));
            objs.Add(angleData.rsrpMax);

            objs.Add(Math.Round(angleData.rssiNum == 0 ? 0 : angleData.rssiSum * 1.0 / angleData.rssiNum, 2));
            objs.Add(Math.Round(angleData.sinrNum == 0 ? 0 : angleData.sinrSum * 1.0 / angleData.sinrNum, 2));
            objs.Add(Math.Round(angleData.rsrqNum == 0 ? 0 : angleData.rsrqSum * 1.0 / angleData.rsrqNum, 2));
            objs.Add(Math.Round(angleData.TANum == 0 ? 0 : angleData.TASum * 1.0 / angleData.TANum, 2));

            objs.Add(Math.Round(angleData.rsrpNum == 0 ? 0 : angleData.fSampleDistSum / angleData.rsrpNum, 2));
            objs.Add(Math.Round(angleData.rsrpNum == 0 ? 0 : angleData.iDistCellNum * 1.0 / angleData.rsrpNum, 2));
        }

        private static void addExt(CellAngleData data, List<object> objs)
        {
            objs.Add(Math.Round(data.cExt.rsrpNum == 0 ? 0 : data.cExt.rsrpSum * 1.0 / data.cExt.rsrpNum, 2));
            objs.Add(Math.Round(data.cExt.rssiNum == 0 ? 0 : data.cExt.rssiSum * 1.0 / data.cExt.rssiNum, 2));
            objs.Add(Math.Round(data.cExt.sinrNum == 0 ? 0 : data.cExt.sinrSum * 1.0 / data.cExt.sinrNum, 2));
            objs.Add(Math.Round(data.cExt.rsrqNum == 0 ? 0 : data.cExt.rsrqSum * 1.0 / data.cExt.rsrqNum, 2));

            objs.Add(Math.Round(data.sampleTotal == 0 ? 0 : data.cExt.fSampleDistSum / data.sampleTotal, 2));//小区平均通信距离
            objs.Add(Math.Round(data.sampleTotal == 0 ? 0 : data.cExt.iOverCellNum * 1.0 / data.sampleTotal, 2));//小区过覆盖指数
            objs.Add(Math.Round(data.sampleTotal == 0 ? 0 : data.cExt.fDistCellNum / data.sampleTotal, 2));//区域站点密集指数
            objs.Add(Math.Round(data.cExt.TANum == 0 ? 0 : data.cExt.TASum * 1.0 / data.cExt.TANum, 2));//小区平均TA
        }

        private List<object> getDefaultValue(int index)
        {
            List<object> tmpCols = new List<object>();
            for (int i = 0; i < index; i++)
            {
                tmpCols.Add("");
            }
            return tmpCols;
        }

        /// <summary>
        /// 小区级赋值
        /// </summary>
        private List<object> FillCellValue(CellAngleData data, int idx)
        {
            List<object> objs8 = new List<object>();
            objs8.Add(idx);
            objs8.Add(data.strTime);
            objs8.Add(data.strcityname);
            objs8.Add(data.strgridname);
            objs8.Add(data.strbscname);
            objs8.Add(data.cellname);
            objs8.Add(data.uarfcn);

            objs8.Add(data.strProblemType);//天线问题类型
            objs8.Add(data.sampleTotal);//总采样点
            objs8.Add(getValidData(data.cExt.rsrpNum, 0, 0, data.cExt.rsrpSum / data.cExt.rsrpNum));//电平均值
            objs8.Add(getValidData(data.cExt.rsrp0Num, 0, 0, data.cExt.rsrp0Sum / data.cExt.rsrp0Num));//RSRP0电平均值
            objs8.Add(getValidData(data.cExt.rsrp1Num, 0, 0, data.cExt.rsrp1Sum / data.cExt.rsrp1Num));//RSRP1电平均值
            objs8.Add(getValidData(data.cExt.rscpDiffNum, 0, 0, data.cExt.rscpDiffSum / data.cExt.rscpDiffNum));//△RSRP电平均值
            objs8.Add(getValidData(data.cExt.fpathlossNum, 0, 0, data.cExt.fpathlossSum / data.cExt.fpathlossNum));//pathloss电平均值
            objs8.Add(getValidData(data.cExt.fpathlossDistanceNum, 0, 0, (float)data.cExt.fpathlossDistanceSum / data.cExt.fpathlossDistanceNum));//路损指标
            objs8.Add(data.cExt.Sinr);//RXQUAL下行5-7级占比(%)
            objs8.Add(getValidData(data.cExt.sinrNum, 0, 0, data.cExt.sinrSum / data.cExt.sinrNum));//小区C/I平均
            objs8.Add(getValidData(data.sampleTotal, 0, 0, data.cExt.fSampleDistSum / data.sampleTotal));//小区平均通信距离
            objs8.Add(getValidData((float)data.sampleTotal, 0, 0, (float)data.cExt.iOverCellNum / data.sampleTotal));//小区过覆盖指数
            objs8.Add(getValidData(data.sampleTotal, 0, 0, data.cExt.fDistCellNum / data.sampleTotal));//区域站点密集指数
            objs8.Add(getValidData((float)data.cExt.TANum, 0, 0, (float)data.cExt.TASum / data.cExt.TANum));//小区平均TA=
            objs8.Add(data.strLteNetWorkTimeRate_1);
            objs8.Add(data.strLteNetWorkTimeRate_2);
            objs8.Add(data.strLteNetWorkTimeRate_3);
            objs8.Add(data.strLteNetWorkTimeRate_4);
            objs8.Add(data.strLteNetWorkTimeRate_5);
            objs8.Add(data.strLteNetWorkTimeRate_6);
            objs8.Add(data.strLteNetWorkTimeRate_7);
            objs8.Add(data.strLteNetWorkTimeRate_8);
            objs8.Add(data.strLteNetWorkTimeRate_Single);
            objs8.Add(data.strLteNetWorkTimeRate_Double);
            objs8.Add(data.lteNetWorkTime.ToString("0.0000"));

            float lteCR = 0;
            if (float.TryParse(data.strLteCoverRate, out lteCR))
            {
                objs8.Add((lteCR * 100).ToString("0.00"));
            }
            else
            {
                objs8.Add("-");//覆盖率(RSRP≥-110&SINR>=-3)
            }
            float lteRSRP = 0;
            if (float.TryParse(data.strLteRsrpRate, out lteRSRP))
            {
                objs8.Add((lteRSRP * 100).ToString("0.00"));
            }
            else
            {
                objs8.Add("-");//LTE覆盖率(RSRP≥-110)
            }
            float FLteSinrRate = 0;
            if (float.TryParse(data.strLteSinrRate, out FLteSinrRate))
            {
                objs8.Add((FLteSinrRate * 100).ToString("0.00"));
            }
            else
            {
                objs8.Add("-");//LTE覆盖率(SINR>=-3)
            }

            objs8.Add(data.cExt.主瓣采样点比例);
            objs8.Add(data.cExt.主瓣小区平均RSRP);
            objs8.Add(data.cExt.主瓣覆盖率RSRPf110SINRf3);
            objs8.Add(data.cExt.主瓣覆盖率RSRPf110);
            objs8.Add(data.cExt.主瓣覆盖率SINRf3);
            objs8.Add(data.cExt.旁瓣采样点比例);
            objs8.Add(data.cExt.背瓣采样点比例);
            objs8.Add(data.cExt.前后比);

            objs8.Add(data.antPara.strdevvender);//主设备厂家
            objs8.Add("460-00-" + data.antPara.ienodebid.ToString() + "-" + data.antPara.isectorid.ToString());//CGI
            objs8.Add(data.antPara.strbeamwidth);//波束宽度
            objs8.Add(data.antPara.strtype);//覆盖类型

            double[] paraArray = data.ciItem.calcCellPara();
            objs8.Add(Math.Round(paraArray[0], 2));//Gmax
            objs8.Add(Math.Round(paraArray[1], 0));//3dB功率角
            objs8.Add(Math.Round(paraArray[2], 0));//6dB功率角
            objs8.Add(Math.Round(data.antPara.drangeport1, 2));//端口1幅度
            objs8.Add(Math.Round(data.antPara.drangeport2, 2));//端口2幅度
            objs8.Add(Math.Round(data.antPara.drangeport3, 2));//端口3幅度
            objs8.Add(Math.Round(data.antPara.drangeport4, 2));//端口4幅度
            objs8.Add(Math.Round(data.antPara.drangeport5, 2));//端口5幅度
            objs8.Add(Math.Round(data.antPara.drangeport6, 2));//端口6幅度
            objs8.Add(Math.Round(data.antPara.drangeport7, 2));//端口7幅度
            objs8.Add(Math.Round(data.antPara.drangeport8, 2));//端口8幅度
            objs8.Add(Math.Round(data.antPara.dphaseport1, 2));//端口1相位
            objs8.Add(Math.Round(data.antPara.dphaseport2, 2));//端口2相位
            objs8.Add(Math.Round(data.antPara.dphaseport3, 2));//端口3相位
            objs8.Add(Math.Round(data.antPara.dphaseport4, 2));//端口4相位
            objs8.Add(Math.Round(data.antPara.dphaseport5, 2));//端口5相位
            objs8.Add(Math.Round(data.antPara.dphaseport6, 2));//端口6相位
            objs8.Add(Math.Round(data.antPara.dphaseport7, 2));//端口7相位
            objs8.Add(Math.Round(data.antPara.dphaseport8, 2));//端口8相位
            objs8.Add(data.antPara.isSmartAnt);//是否智能天线

            objs8.Add(Math.Round(data.antCfg.预置下倾角, 2));//预置下倾角
            objs8.Add(Math.Round(data.antCfg.机械下倾角, 2));//机械下倾角
            objs8.Add(Math.Round(data.antCfg.电调下倾角, 2));//电调下倾角
            objs8.Add(Math.Round(data.antCfg.挂高, 2));//挂高

            objs8.Add(Math.Round(data.cellPara.F上行吞吐量, 2));//上行吞吐量
            objs8.Add(Math.Round(data.cellPara.F下行吞吐量, 2));//下行吞吐量
            objs8.Add(Math.Round(data.cellPara.F无线接通率, 2));//无线接通率
            objs8.Add(Math.Round(data.cellPara.F无线掉线率, 2));//无线掉线率
            objs8.Add(Math.Round(data.cellPara.F切换成功率, 2));//切换成功率
            objs8.Add(Math.Round(data.cellPara.fERAB建立成功率, 2));//ERAB建立成功率
            objs8.Add(Math.Round(data.cellPara.fERAB掉线率, 2));//ERAB掉线率

            objs8.Add(Math.Round(data.lteMRItem.dAvgRsrp, 2));//平均RSRP
            objs8.Add(Math.Round(data.lteMRItem.dAvgSinr, 2));//平均SINR
            objs8.Add(Math.Round(data.lteMRItem.dRate95Rsrp, 2));//-95覆盖率
            objs8.Add(Math.Round(data.lteMRItem.dRate110Rsrp, 2));//-110覆盖率
            return objs8;
        }

        /// <summary>
        /// 按角度拆分导出数据
        /// </summary>
        private List<NPOIRow> FillAngleValue(CellAngleData data)
        {
            List<NPOIRow> dataNpoi = new List<NPOIRow>();
            addNpoiRow(data, dataNpoi, "RSRP", -140, data.ciItem.rsrpArray);
            addNpoiRow(data, dataNpoi, "RSRP0", -140, data.ciItem.rsrp0Array);
            addNpoiRow(data, dataNpoi, "RSRP1", -140, data.ciItem.rsrp1Array);
            addNpoiRow(data, dataNpoi, "Pathloss", 0, data.ciItem.pathlossArray);
            addNpoiRow(data, dataNpoi, "RSSI", -140, data.ciItem.rssiArray);
            addNpoiRow(data, dataNpoi, "RSRQ", 0, data.ciItem.rsrqArray);
            addNpoiRow(data, dataNpoi, "过覆盖指数", 0, data.ciItem.coverArray);
            addNpoiRow(data, dataNpoi, "通信距离", 0, data.ciItem.sampArray);
            addNpoiRow(data, dataNpoi, "SINR", -25, data.ciItem.sinrArray);
            return dataNpoi;
        }

        private void addNpoiRow(CellAngleData data, List<NPOIRow> dataNpoi, string paramName, int equalData, int[] paramArry)
        {
            int iStart = 0;
            int iEnd = 360;
            List<object> objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add(paramName);
            for (int i = iStart; i < iEnd; i++)
            {
                int value = getValidData(data.ciItem.sampNumArray[i], 0, equalData, paramArry[i] / data.ciItem.sampNumArray[i]);
                objsL.Add(value);
            }
            NPOIRow nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);
        }

        private void addNpoiRow(CellAngleData data, List<NPOIRow> dataNpoi, string paramName, double equalData, double[] paramArry)
        {
            int iStart = 0;
            int iEnd = 360;
            List<object> objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add(paramName);
            for (int i = iStart; i < iEnd; i++)
            {
                double value = getValidData(data.ciItem.sampNumArray[i], 0, equalData, paramArry[i] / data.ciItem.sampNumArray[i]);   
                objsL.Add(value);
            }
            NPOIRow nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);
        }

        private int getValidData(int value, int compareValue, int equalData, int unequalData)
        {
            if (value == compareValue)
            {
                return equalData;
            }
            return unequalData;
        }

        private double getValidData(float value, float compareValue, float equalData, float unequalData)
        {
            if (value == compareValue)
            {
                return Math.Round(equalData, 2);
            }
            return Math.Round(unequalData, 2);
        }

        private double getValidData(int value, int compareValue, double equalData, double unequalData)
        {
            if (value == compareValue)
            {
                return Math.Round(equalData, 2);
            }
            return Math.Round(unequalData, 2);
        }

        #endregion

        #region 分析天线问题类型
        /// <summary>
        /// 按小区分析天线问题类型
        /// </summary>
        private void anaAntennaProblemTypeByCell()
        {
            foreach (string strCellName in dicUtranCellAngelData.Keys)
            {
                Dictionary<string, AngleData> angleDatasDic = dicUtranCellAngelData[strCellName].angleDatas;
                string problemName = "";
                string strGridTypeName = "";
                isContainPoint(dicUtranCellAngelData[strCellName].cellLongitude
                    , dicUtranCellAngelData[strCellName].cellLatitude, ref strGridTypeName);

                if (strGridTypeName == "")
                    strGridTypeName = "无网格号";
                if (!antennaTypeStatDic.ContainsKey(strCityName + "|" + strGridTypeName))
                {
                    AntennaTypeStat antennaTypeStat = new AntennaTypeStat();
                    antennaTypeStat.strCity = strCityName;
                    antennaTypeStat.strGrid = strGridTypeName;

                    if (dicUtranCellAngelData[strCellName].sampleTotal >= 300 && dicUtranCellAngelData[strCellName].cExt.Sinr5_3 >= 1)
                    {
                        #region 天线问题类型
                        try
                        {
                            if (antProblemType1(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStat.iProblemType1++;
                                if (problemName == "")
                                    problemName = "主瓣弱覆盖";
                                else
                                    problemName += ";主瓣弱覆盖";
                            }
                            if (antProblemType2(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStat.iProblemType2++;
                                if (problemName == "")
                                    problemName = "后瓣强度异常";
                                else
                                    problemName += ";后瓣强度异常";
                            }
                            if (antProblemType3(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStat.iProblemType3++;
                                if (problemName == "")
                                    problemName = "旁瓣信号泄露";
                                else
                                    problemName += ";旁瓣信号泄露";
                            }
                            if (antProblemType4(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStat.iProblemType4++;
                                if (problemName == "")
                                    problemName = "后瓣过覆盖";
                                else
                                    problemName += ";后瓣过覆盖";
                            }
                            if (antProblemType5(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStat.iProblemType5++;
                                if (problemName == "")
                                    problemName = "旁瓣过覆盖";
                                else
                                    problemName += ";旁瓣过覆盖";
                            }
                            if (antProblemType6(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStat.iProblemType6++;
                                if (problemName == "")
                                    problemName = "主瓣过覆盖";
                                else
                                    problemName += ";主瓣过覆盖";
                            }
                        }
                        catch (Exception exp)
                        {
                            log.Error("分析天线问题类型时发生错误：" + exp.Message);
                        }
                        #endregion

                        dicUtranCellAngelData[strCellName].strProblemType = problemName;
                    }
                    if (dicUtranCellAngelData[strCellName].cExt.Sinr5_3 >= 2)
                    {
                        antennaTypeStat.iProblemType7++;
                    }
                    foreach (string str in angleDatasDic.Keys)
                    {
                        if (angleDatasDic[str].FSinrValue >= 10)
                        {
                            antennaTypeStat.iProblemType8++;
                        }
                        if ((angleDatasDic[str].iOverBigThan4SampleNum * 1.0) / angleDatasDic[str].rsrpNum > 0.1)
                        {
                            antennaTypeStat.iProblemType9++;
                        }
                    }
                    antennaTypeStatDic.Add(strCityName + "|" + strGridTypeName, antennaTypeStat);
                }
                else
                {
                    if (dicUtranCellAngelData[strCellName].sampleTotal >= 300 && dicUtranCellAngelData[strCellName].cExt.Sinr5_3 >= 1)
                    {
                        #region 天线问题类型
                        try
                        {
                            if (antProblemType1(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStatDic[strCityName + "|" + strGridTypeName].iProblemType1++;
                                if (problemName == "")
                                    problemName = "主瓣弱覆盖";
                                else
                                    problemName += ";主瓣弱覆盖";
                            }
                            if (antProblemType2(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStatDic[strCityName + "|" + strGridTypeName].iProblemType2++;
                                if (problemName == "")
                                    problemName = "后瓣强度异常";
                                else
                                    problemName += ";后瓣强度异常";
                            }
                            if (antProblemType3(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStatDic[strCityName + "|" + strGridTypeName].iProblemType3++;
                                if (problemName == "")
                                    problemName = "旁瓣信号泄露";
                                else
                                    problemName += ";旁瓣信号泄露";
                            }
                            if (antProblemType4(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStatDic[strCityName + "|" + strGridTypeName].iProblemType4++;
                                if (problemName == "")
                                    problemName = "后瓣过覆盖";
                                else
                                    problemName += ";后瓣过覆盖";
                            }
                            if (antProblemType5(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStatDic[strCityName + "|" + strGridTypeName].iProblemType5++;
                                if (problemName == "")
                                    problemName = "旁瓣过覆盖";
                                else
                                    problemName += ";旁瓣过覆盖";
                            }
                            if (antProblemType6(angleDatasDic, dicUtranCellAngelData[strCellName].sampleTotal))
                            {
                                antennaTypeStatDic[strCityName + "|" + strGridTypeName].iProblemType6++;
                                if (problemName == "")
                                    problemName = "主瓣过覆盖";
                                else
                                    problemName += ";主瓣过覆盖";
                            }
                        }
                        catch (Exception exp)
                        {
                            log.Error("分析天线问题类型时发生错误：" + exp.Message);
                        }
                        #endregion

                        dicUtranCellAngelData[strCellName].strProblemType = problemName;
                    }
                    if (dicUtranCellAngelData[strCellName].cExt.Sinr5_3 >= 2)
                    {
                        antennaTypeStatDic[strCityName + "|" + strGridTypeName].iProblemType7++;
                    }
                    foreach (string str in angleDatasDic.Keys)
                    {
                        if (angleDatasDic[str].FSinrValue >= 10)
                        {
                            antennaTypeStatDic[strCityName + "|" + strGridTypeName].iProblemType8++;
                        }
                        if ((angleDatasDic[str].iOverBigThan4SampleNum * 1.0) / angleDatasDic[str].rsrpNum > 0.1)
                        {
                            antennaTypeStatDic[strCityName + "|" + strGridTypeName].iProblemType9++;
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 主瓣弱覆盖（增益故障）
        /// </summary>
        private bool antProblemType1(Dictionary<string, AngleData> angleDatas, int sampleTotal)
        {
            float rxlevSampleNum = 0;
            float rxlevSampleNum0_500 = 0;
            float rxlevSum0_500 = 0;
            float rxlevSampleNum500_1000 = 0;
            float rxlevSum500_1000 = 0;

            if (angleDatas.ContainsKey("[0,15]"))
            {
                AngleData a = angleDatas["[0,15]"];
                rxlevSampleNum += a.rsrpNum;

                rxlevSampleNum0_500 += a.rsrpSampleNum0_500;
                rxlevSum0_500 += a.rsrpSum0_500;

                rxlevSampleNum500_1000 += a.rsrpSampleNum500_1000;
                rxlevSum500_1000 += a.rsrpSum500_1000;
            }
            if (angleDatas.ContainsKey("(15,30]"))
            {
                AngleData a = angleDatas["(15,30]"];
                rxlevSampleNum += a.rsrpNum;

                rxlevSampleNum0_500 += a.rsrpSampleNum0_500;
                rxlevSum0_500 += a.rsrpSum0_500;

                rxlevSampleNum500_1000 += a.rsrpSampleNum500_1000;
                rxlevSum500_1000 += a.rsrpSum500_1000;
            }
            if (angleDatas.ContainsKey("(30,60]"))
            {
                AngleData a = angleDatas["(30,60]"];
                rxlevSampleNum += a.rsrpNum;

                rxlevSampleNum0_500 += a.rsrpSampleNum0_500;
                rxlevSum0_500 += a.rsrpSum0_500;

                rxlevSampleNum500_1000 += a.rsrpSampleNum500_1000;
                rxlevSum500_1000 += a.rsrpSum500_1000;
            }

            if ((rxlevSampleNum / sampleTotal) >= 0.5 && rxlevSampleNum0_500 > 0 && (rxlevSum0_500 / rxlevSampleNum0_500) <= -80)
            {
                return true;
            }
            else if ((rxlevSampleNum / sampleTotal) > 0.5 && rxlevSampleNum500_1000 > 0 && (rxlevSum500_1000 / rxlevSampleNum500_1000) <= -90)
            {
                return true;
            }

            return false;
        }
        /// <summary>
        /// 后瓣强度异常（前后比故障）
        /// </summary>
        private bool antProblemType2(Dictionary<string, AngleData> angleDatas, int sampleTotal)
        {
            float rxlevSampleNum0_30 = 0;
            float rxlevSampleNum150_180 = 0;
            float avgRxlev0_30 = 0;
            float avgRxlev150_180 = 0;
            float rxqual150_180 = 0;
            List<int> top5RxlevList0_30 = new List<int>();

            if (angleDatas.ContainsKey("(150,180]"))
            {
                AngleData a = angleDatas["(150,180]"];
                rxlevSampleNum150_180 = a.rsrpNum;
                avgRxlev150_180 = a.top5RsrpAvg;
                rxqual150_180 = a.FSinr;
            }
            if (angleDatas.ContainsKey("[0,15]"))
            {
                AngleData a = angleDatas["[0,15]"];
                rxlevSampleNum0_30 += a.rsrpNum;
                top5RxlevList0_30 = a.top5RsrpList;
            }
            if (angleDatas.ContainsKey("(15,30]"))
            {
                AngleData a = angleDatas["(15,30]"];
                rxlevSampleNum0_30 += a.rsrpNum;
                addTop5RxlevList0_30(top5RxlevList0_30, a);
            }

            float fRxlev = 0;
            foreach (int iRxlev in top5RxlevList0_30)
            {
                fRxlev += iRxlev;
            }
            avgRxlev0_30 = fRxlev / top5RxlevList0_30.Count;

            if ((rxlevSampleNum0_30 / sampleTotal) >= 0.2 && (rxlevSampleNum150_180 / sampleTotal) >= 0.03
                && (avgRxlev0_30 - avgRxlev150_180) <= 10 && rxqual150_180 > 2)
            {
                return true;
            }

            return false;
        }

        private static void addTop5RxlevList0_30(List<int> top5RxlevList0_30, AngleData a)
        {
            foreach (int iRxlev in a.top5RsrpList)
            {
                top5RxlevList0_30.Sort();
                if (top5RxlevList0_30.Count >= 5)
                {
                    if (top5RxlevList0_30[0] < iRxlev)
                    {
                        top5RxlevList0_30[0] = iRxlev;
                    }
                }
                else
                {
                    top5RxlevList0_30.Add(iRxlev);
                }
            }
        }

        /// <summary>
        /// 旁瓣信号泄露（旁瓣变形）
        /// </summary>
        private bool antProblemType3(Dictionary<string, AngleData> angleDatas, int sampleTotal)
        {
            float rxlevSampleNum60_90 = 0;
            float rxlevSampleNum90_120 = 0;
            float rxlevSampleNum120_150 = 0;

            float rxqual60_90 = 0;
            float rxqual90_120 = 0;
            float rxqual120_150 = 0;

            if (angleDatas.ContainsKey("(60,90]"))
            {
                AngleData a = angleDatas["(60,90]"];
                rxlevSampleNum60_90 = a.rsrpNum;
                rxqual60_90 = a.FSinr;
            }
            if (angleDatas.ContainsKey("(90,120]"))
            {
                AngleData a = angleDatas["(90,120]"];
                rxlevSampleNum90_120 = a.rsrpNum;
                rxqual90_120 = a.FSinr;
            }
            if (angleDatas.ContainsKey("(120,150]"))
            {
                AngleData a = angleDatas["(120,150]"];
                rxlevSampleNum120_150 = a.rsrpNum;
                rxqual120_150 = a.FSinr;
            }

            if ((rxlevSampleNum60_90 + rxlevSampleNum90_120 + rxlevSampleNum120_150) / sampleTotal > 0.4 &&
               (rxqual60_90 > 2 || rxqual90_120 > 2 || rxqual120_150 > 2))
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 后瓣过覆盖
        /// </summary>
        private bool antProblemType4(Dictionary<string, AngleData> angleDatas, int sampleTotal)
        {
            float rxlevSampleNum150_180 = 0;
            float avgCoverDistance150_180 = 0;
            float avgOverCoverNum150_180 = 0;
            float avgRxqual150_180 = 0;

            if (angleDatas.ContainsKey("(150,180]"))
            {
                AngleData a = angleDatas["(150,180]"];
                rxlevSampleNum150_180 = a.rsrpNum;
                avgCoverDistance150_180 = (float)(a.fSampleDistSum / a.rsrpNum);
                avgOverCoverNum150_180 = (float)(a.iDistCellNum * 1.0 / a.rsrpNum);
                avgRxqual150_180 = a.FSinr;
            }

            if ((rxlevSampleNum150_180 / sampleTotal >= 0.3 && avgRxqual150_180 > 2) ||
                (rxlevSampleNum150_180 / sampleTotal >= 0.03 && avgCoverDistance150_180 >= 1000 && avgRxqual150_180 > 2) ||
                (rxlevSampleNum150_180 / sampleTotal >= 0.03 && avgOverCoverNum150_180 >= 4 && avgRxqual150_180 > 2))
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 旁瓣过覆盖
        /// </summary>
        private bool antProblemType5(Dictionary<string, AngleData> angleDatas, int sampleTotal)
        {
            float rxlevSampleNum60_90 = 0;
            float avgOverCoverNum60_90 = 0;
            float avgRxqual60_90 = 0;

            float rxlevSampleNum90_120 = 0;
            float avgOverCoverNum90_120 = 0;
            float avgRxqual90_120 = 0;

            float rxlevSampleNum120_150 = 0;
            float avgOverCoverNum120_150 = 0;
            float avgRxqual120_150 = 0;

            if (angleDatas.ContainsKey("(60,90]"))
            {
                AngleData a = angleDatas["(60,90]"];
                rxlevSampleNum60_90 = a.rsrpNum;
                avgOverCoverNum60_90 = (float)((a.iDistCellNum * 1.0) / a.rsrpNum);
                avgRxqual60_90 = a.FSinr;
            }
            if (angleDatas.ContainsKey("(90,120]"))
            {
                AngleData a = angleDatas["(90,120]"];
                rxlevSampleNum90_120 = a.rsrpNum;
                avgOverCoverNum90_120 = (float)((a.iDistCellNum * 1.0) / a.rsrpNum);
                avgRxqual90_120 = a.FSinr;
            }
            if (angleDatas.ContainsKey("(120,150]"))
            {
                AngleData a = angleDatas["(120,150]"];
                rxlevSampleNum120_150 = a.rsrpNum;
                avgOverCoverNum120_150 = (float)((a.iDistCellNum * 1.0) / a.rsrpNum);
                avgRxqual120_150 = a.FSinr;
            }

            if ((rxlevSampleNum60_90 / sampleTotal > 0.1 && avgOverCoverNum60_90 >= 4 && avgRxqual60_90 > 2) ||
                (rxlevSampleNum90_120 / sampleTotal > 0.1 && avgOverCoverNum90_120 >= 4 && avgRxqual90_120 > 2) ||
                (rxlevSampleNum120_150 / sampleTotal > 0.1 && avgOverCoverNum120_150 >= 4 && avgRxqual120_150 > 2))
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 主瓣过覆盖
        /// </summary>
        private bool antProblemType6(Dictionary<string, AngleData> angleDatas, int sampleTotal)
        {
            float rxlevSampleNum0_30 = 0;
            int iOverCoverNum0_30 = 0;
            int iPCCHC2I_3 = 0;
            int iPCCHC2I_All = 0;

            float avgOverCoverNum0_30 = 0;
            float avgRxqual0_30 = 0;

            float rxlevSampleNum30_60 = 0;
            float avgOverCoverNum30_60 = 0;
            float avgRxqual30_60 = 0;

            if (angleDatas.ContainsKey("[0,15]"))
            {
                AngleData a = angleDatas["[0,15]"];
                rxlevSampleNum0_30 += a.rsrpNum;
                iOverCoverNum0_30 += a.iDistCellNum;
                iPCCHC2I_All = a.sinrNum;
                iPCCHC2I_3 += a.sinrNum_3;
            }

            if (angleDatas.ContainsKey("(15,30]"))
            {
                AngleData a = angleDatas["(15,30]"];
                rxlevSampleNum0_30 += a.rsrpNum;
                iOverCoverNum0_30 += a.iDistCellNum;
                iPCCHC2I_All = a.sinrNum;
                iPCCHC2I_3 += a.sinrNum_3;
            }

            avgOverCoverNum0_30 = iOverCoverNum0_30 / rxlevSampleNum0_30;
            avgRxqual0_30 = getRxqual(iPCCHC2I_3, iPCCHC2I_All);

            if (angleDatas.ContainsKey("(30,60]"))
            {
                AngleData a = angleDatas["(30,60]"];
                rxlevSampleNum30_60 = a.rsrpNum;
                avgOverCoverNum30_60 = (float)((a.iDistCellNum * 1.0) / a.rsrpNum);
                avgRxqual30_60 = a.FSinr;
            }

            if ((rxlevSampleNum0_30 / sampleTotal > 0.1 && avgOverCoverNum0_30 >= 4 && avgRxqual0_30 > 2) ||
                (rxlevSampleNum30_60 / sampleTotal > 0.1 && avgOverCoverNum30_60 >= 4 && avgRxqual30_60 > 2))
            {
                return true;
            }

            return false;
        }
        #endregion

        #region KPI指标统计

        /// <summary>
        /// 计算话音质量占比
        /// </summary>
        public float getRxqual(int iSINR_3, int iSINR)
        {
            try
            {
                if ((iSINR_3 + iSINR) == 0)
                {
                    return 0;
                }
                else
                {
                    return (float)(Math.Round(iSINR_3 * 100.0 / iSINR, 2));
                }
            }
            catch
            {
                return 0;
            }
        }
        /// <summary>
        /// 计算路损指标
        /// </summary>
        private double getDLPathlossDistance(TestPoint tp, double fMz, LTEBTS lteBts, int fAltitude)
        {
            double fPathlossDistacne;
            double distance = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, lteBts.Longitude, lteBts.Latitude);
            fPathlossDistacne = 40 * (1 - 4 * 0.001 * fAltitude) * Math.Log10(distance / 1000) - 18 * Math.Log10(fAltitude)
             + 21 * Math.Log10(fMz) + 80;
            return fPathlossDistacne;
        }
        /// <summary>
        /// 计算时长占比
        /// </summary>
        private void CalStrNetWorkTimeRate(ref CellAngleData utranCellAngleData)
        {
            if (!ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic.ContainsKey(utranCellAngleData.cellname))
                return;

            Dictionary<string, double> WInfoDic = ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic[utranCellAngleData.cellname].lteNewImg.WInfoDic;
            if (ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic.ContainsKey(utranCellAngleData.cellname)
                && WInfoDic != null && WInfoDic.Count > 0)
            {
                setLteCover(utranCellAngleData, WInfoDic);
                setRsrp(utranCellAngleData, WInfoDic);
                setSinr(utranCellAngleData, WInfoDic);

                if (WInfoDic.ContainsKey("61211002"))
                {
                    utranCellAngleData.lteNetWorkTime = WInfoDic["61211002"] / 1000;
                    if (utranCellAngleData.lteNetWorkTime == 0)
                    {
                        return;
                    }
                    setStrLteNetWorkTimeRate(utranCellAngleData, WInfoDic);
                }
                if (WInfoDic.ContainsKey("61211101"))
                {
                    double dRate_base = WInfoDic["61211101"];
                    if (dRate_base == 0)
                    {
                        return;
                    }
                    setLteNetWorkTimeRate(utranCellAngleData, WInfoDic);
                }
            }
        }

        private void setLteCover(CellAngleData utranCellAngleData, Dictionary<string, double> WInfoDic)
        {
            if (WInfoDic.ContainsKey("61210101"))//LTE覆盖2（RSRP>-110且SINR>=-3）
            {
                double dRate_base = WInfoDic["61210101"];
                if (dRate_base != 0 && WInfoDic.ContainsKey("61210105"))
                {
                    utranCellAngleData.strLteCoverRate = (WInfoDic["61210105"] / WInfoDic["61210101"]).ToString();
                }
            }
        }

        private void setRsrp(CellAngleData utranCellAngleData, Dictionary<string, double> WInfoDic)
        {
            if (WInfoDic.ContainsKey("61210301"))//RSRP占比（RSRP>-110）
            {
                double dRate_base = WInfoDic["61210301"];
                if (dRate_base != 0)
                {
                    double dRate_Sample = 0;
                    if (WInfoDic.ContainsKey("6121037D"))
                    {
                        dRate_Sample += WInfoDic["6121037D"];
                    }
                    if (WInfoDic.ContainsKey("61210318"))
                    {
                        dRate_Sample += WInfoDic["61210318"];
                    }
                    utranCellAngleData.strLteRsrpRate = (dRate_Sample / dRate_base).ToString();
                }
            }
        }

        private void setSinr(CellAngleData utranCellAngleData, Dictionary<string, double> WInfoDic)
        {
            if (WInfoDic.ContainsKey("61210401"))//RINR占比（SINR>=-3）
            {
                double dRate_base = WInfoDic["61210401"];
                if (dRate_base != 0 && WInfoDic.ContainsKey("61210402"))
                {
                    utranCellAngleData.strLteSinrRate = (WInfoDic["61210402"] / WInfoDic["61210401"]).ToString();
                }
            }
        }

        private void setStrLteNetWorkTimeRate(CellAngleData utranCellAngleData, Dictionary<string, double> WInfoDic)
        {
            if (WInfoDic.ContainsKey("61211004"))
            {
                utranCellAngleData.strLteNetWorkTimeRate_Single = (100 * WInfoDic["61211004"] / WInfoDic["61211002"]).ToString("0.00") + "%";
            }
            if (WInfoDic.ContainsKey("61211006"))
            {
                utranCellAngleData.strLteNetWorkTimeRate_Double = (100 * WInfoDic["61211006"] / WInfoDic["61211002"]).ToString("0.00") + "%";
            }
        }

        private void setLteNetWorkTimeRate(CellAngleData utranCellAngleData, Dictionary<string, double> WInfoDic)
        {
            if (WInfoDic.ContainsKey("61211102"))
            {
                utranCellAngleData.strLteNetWorkTimeRate_1 = (100 * WInfoDic["61211102"] / WInfoDic["61211101"]).ToString("0.00") + "%";
            }
            if (WInfoDic.ContainsKey("61211103"))
            {
                utranCellAngleData.strLteNetWorkTimeRate_2 = (100 * WInfoDic["61211103"] / WInfoDic["61211101"]).ToString("0.00") + "%";
            }
            if (WInfoDic.ContainsKey("61211104"))
            {
                utranCellAngleData.strLteNetWorkTimeRate_3 = (100 * WInfoDic["61211104"] / WInfoDic["61211101"]).ToString("0.00") + "%";
            }
            if (WInfoDic.ContainsKey("61211105"))
            {
                utranCellAngleData.strLteNetWorkTimeRate_4 = (100 * WInfoDic["61211105"] / WInfoDic["61211101"]).ToString("0.00") + "%";
            }
            if (WInfoDic.ContainsKey("61211106"))
            {
                utranCellAngleData.strLteNetWorkTimeRate_5 = (100 * WInfoDic["61211106"] / WInfoDic["61211101"]).ToString("0.00") + "%";
            }
            if (WInfoDic.ContainsKey("61211107"))
            {
                utranCellAngleData.strLteNetWorkTimeRate_6 = (100 * WInfoDic["61211107"] / WInfoDic["61211101"]).ToString("0.00") + "%";
            }
            if (WInfoDic.ContainsKey("61211108"))
            {
                utranCellAngleData.strLteNetWorkTimeRate_7 = (100 * WInfoDic["61211108"] / WInfoDic["61211101"]).ToString("0.00") + "%";
            }
            if (WInfoDic.ContainsKey("61211109"))
            {
                utranCellAngleData.strLteNetWorkTimeRate_8 = (100 * WInfoDic["61211109"] / WInfoDic["61211101"]).ToString("0.00") + "%";
            }
        }

        #endregion

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["BDtNbInfo"] = BDtNbInfo;
                param["BSecAna"] = BSecAna;
                param["BSecDataExport"] = BSecDataExport;
                param["BSampleShow"] = BSampleShow;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("BDtNbInfo"))
                {
                    BDtNbInfo = (bool)param["BDtNbInfo"];
                }
                if (param.ContainsKey("BSecAna"))
                {
                    BSecAna = (bool)param["BSecAna"];
                }
                if (param.ContainsKey("BSecDataExport"))
                {
                    BSecDataExport = (bool)param["BSecDataExport"];
                }
                if (param.ContainsKey("BSampleShow"))
                {
                    BSampleShow = (bool)param["BSampleShow"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new LteAntennaProperties_LTE(this);
            }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }

        protected override void saveBackgroundData()
        {
            if (resultDic.Count == 0)
                return;
            DIYQueryDBConect diyQuery = new DIYQueryDBConect(MainModel, strCityName.Replace("市", ""));
            diyQuery.Query();
            Dictionary<string, string> cityConnDic = diyQuery.strConnDic;
            string strCurConn = cityConnDic[strCityName.Replace("市", "")];
            string tableName = "tb_probchk_dt_cell_angle_result";
            DataTable importTable = CreateImportTable(tableName);
            DiySqlNonQuery queryNon = null;
            string sql = "if exists (select * from sysobjects where name = '" + tableName + "') delete  from " + tableName + " where istime = " + Condition.Periods[0].IBeginTime + " and ietime = " + Condition.Periods[0].IEndTime
                            + " and strprojectid = '" + BackgroundFuncConfigManager.GetInstance().ProjectType + "'";
            queryNon = new DiySqlNonQuery(MainModel, sql);
            queryNon.Query();

            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (CellAngleData caData in resultDic.Values)
            {
                BackgroundResult result = new BackgroundResult();
                result.ProjectString = BackgroundFuncConfigManager.GetInstance().ProjectType;
                result.ISTime = Condition.Periods[0].IBeginTime;
                result.IETime = Condition.Periods[0].IEndTime;
                result.SubFuncID = GetSubFuncID();
                result.AddImageValue(caData.cellname);
                result.AddImageValue(caData.iEci);
                result.AddImageValue(caData.sampleTotal);
                result.AddImageValue(caData.strLteCoverRate);//覆盖率(RSRP≥-110&SINR>=-3)
                result.AddImageValue(caData.strLteRsrpRate); //LTE覆盖率(RSRP≥-110)
                result.AddImageValue(caData.strLteSinrRate); //LTE覆盖率(SINR>=-3)
                result.AddImageValue(caData.cExt.RSRP均值.ToString());//RSRP均值
                result.AddImageValue(caData.cExt.SINR均值.ToString());//SINR均值
                result.AddImageValue(caData.小区过覆盖指数.ToString());//小区过覆盖指数

                result.AddImageValue(caData.cExt.主瓣采样点比例);
                result.AddImageValue(caData.cExt.主瓣小区平均RSRP);
                result.AddImageValue(caData.cExt.主瓣覆盖率RSRPf110SINRf3);
                result.AddImageValue(caData.cExt.主瓣覆盖率RSRPf110);
                result.AddImageValue(caData.cExt.主瓣覆盖率SINRf3);
                result.AddImageValue(caData.cExt.旁瓣采样点比例);
                result.AddImageValue(caData.cExt.背瓣采样点比例);
                result.AddImageValue(caData.cExt.前后比);

                //新增指标，兼容前期数据
                result.AddImageValue(caData.cExt.旁瓣小区平均RSRP);
                result.AddImageValue(caData.cExt.旁瓣覆盖率RSRPf110SINRf3);
                result.AddImageValue(caData.cExt.旁瓣覆盖率RSRPf110);
                result.AddImageValue(caData.cExt.旁瓣覆盖率SINRf3);

                result.AddImageValue(caData.cExt.背瓣小区平均RSRP);
                result.AddImageValue(caData.cExt.背瓣覆盖率RSRPf110SINRf3);
                result.AddImageValue(caData.cExt.背瓣覆盖率RSRPf110);
                result.AddImageValue(caData.cExt.背瓣覆盖率SINRf3);

                result.AddImageValue(caData.cExt.RSRP0电平均值.ToString());//RSRP0电平均值
                result.AddImageValue(caData.cExt.RSRP1电平均值.ToString());//RSRP1电平均值
                result.AddImageValue(caData.cExt.DRSRP电平均值.ToString());//DRSRP电平均值
                result.AddImageValue(caData.cExt.pathloss电平均值.ToString());//pathloss电平均值
                result.AddImageValue(caData.cExt.pathloss指标.ToString());//pathloss指标
                bgResultList.Add(result);

                //入库小区角度信息
                #region 角度及画图信息
                StringBuilder RSRP = new StringBuilder();//|1_23.1|
                StringBuilder 平滑RSRP = new StringBuilder();//|1_23.1|
                StringBuilder SINR = new StringBuilder();//|1_23.1|
                StringBuilder 过覆盖指数 = new StringBuilder();//|1_23.1|
                StringBuilder 通讯距离 = new StringBuilder();//|1_23.1|
                StringBuilder 总采样点 = new StringBuilder();//|1_23.1|
                StringBuilder RSRP0值 = new StringBuilder();//|1_23.1|
                StringBuilder RSRP1值 = new StringBuilder();//|1_23.1|
                StringBuilder pathloss值 = new StringBuilder();//|1_23.1|
                StringBuilder rssiArray值 = new StringBuilder();//|1_23.1|
                StringBuilder sinr3值 = new StringBuilder();//|1_23.1|
                StringBuilder rsrq值 = new StringBuilder();//|1_23.1|
                foreach (int key in caData.ciItem.antAngleDic.Keys)
                {
                    RSRP.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].IRsrp);
                    平滑RSRP.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].DRsrpNew);
                    SINR.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].ISinr);
                    过覆盖指数.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].DCover);
                    通讯距离.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].DSampDist);
                    总采样点.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].ISampNum);
                    RSRP0值.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].IRsrp0);
                    RSRP1值.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].IRsrp1);
                    pathloss值.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].IPathlsos);
                    rssiArray值.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].IRssi);
                    sinr3值.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].ISinr3);
                    rsrq值.Append("|" + key + "_" + caData.ciItem.antAngleDic[key].IRsrq);
                }
                RSRP = RSRP.Remove(0, 1);
                平滑RSRP = 平滑RSRP.Remove(0, 1);
                SINR = SINR.Remove(0, 1);
                过覆盖指数 = 过覆盖指数.Remove(0, 1);
                通讯距离 = 通讯距离.Remove(0, 1);
                总采样点 = 总采样点.Remove(0, 1);
                RSRP0值 = RSRP0值.Remove(0, 1);
                pathloss值 = pathloss值.Remove(0, 1);
                rssiArray值 = rssiArray值.Remove(0, 1);
                RSRP1值 = RSRP1值.Remove(0, 1);
                sinr3值 = sinr3值.Remove(0, 1);
                rsrq值 = rsrq值.Remove(0, 1);

                StringBuilder strModelMaxArray = new StringBuilder(); //最大模型
                foreach (double date in caData.ciItem.modelMaxArray)
                {
                    strModelMaxArray.Append("|" + date);
                }
                strModelMaxArray = strModelMaxArray.Remove(0, 1);

                StringBuilder strRSRP = new StringBuilder();
                StringBuilder strSampleNum = new StringBuilder();
                foreach (int key in caData.ciItem.antVertDic.Keys)
                {
                    strRSRP.Append("|" + key + "_" + caData.ciItem.antVertDic[key].IRsrp);
                    strSampleNum.Append("|" + key + "_" + caData.ciItem.antVertDic[key].ISampNum);
                }
                strRSRP = strRSRP.Remove(0, 1);
                strSampleNum = strSampleNum.Remove(0, 1);
                object[] values = new object[] { Condition.Periods[0].IBeginTime, Condition.Periods[0].IEndTime, caData.cellname,
                    result.ProjectString, caData.iEci,RSRP.ToString(), 平滑RSRP.ToString(), SINR.ToString(), 过覆盖指数.ToString(),
                    通讯距离.ToString(), strModelMaxArray.ToString(), strRSRP.ToString(), strSampleNum.ToString(), 总采样点.ToString(),
                    RSRP0值.ToString(), RSRP1值.ToString(), pathloss值.ToString(), rssiArray值.ToString(), sinr3值.ToString(), rsrq值.ToString() };
                importTable.Rows.Add(values);
                #endregion
            }
            if (importTable.Rows.Count != 0)
            {
                SqlBulkCopy bcp = new SqlBulkCopy(strCurConn);
                try
                {
                    bcp.BatchSize = 2000;
                    bcp.BulkCopyTimeout = 3600;
                    bcp.DestinationTableName = importTable.TableName;
                    bcp.WriteToServer(importTable);

                }
                catch (Exception ex)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("保存小区角度信息失败！\n\t" + ex.Message);
                }
                finally
                {
                    bcp.Close();
                }
                BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                    Condition.Periods[0].IEndTime, bgResultList);
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("入库成功！");
            }
        }

        private DataTable CreateImportTable(string tableName)
        {
            DataTable importTable = new DataTable(tableName);
            importTable.Columns.Add("istime", typeof(int));
            importTable.Columns.Add("ietime", typeof(int));
            importTable.Columns.Add("strcellname", typeof(string));
            importTable.Columns.Add("strprojectid", typeof(string));
            importTable.Columns.Add("iECI", typeof(int));
            importTable.Columns.Add("strRSRP", typeof(string));
            importTable.Columns.Add("strpRSRP", typeof(string));
            importTable.Columns.Add("strSINR", typeof(string));
            importTable.Columns.Add("过覆盖指数", typeof(string));
            importTable.Columns.Add("通讯距离", typeof(string));
            importTable.Columns.Add("strModelMaxArray", typeof(string));
            importTable.Columns.Add("strRSRP200", typeof(string));
            importTable.Columns.Add("strSampleNum", typeof(string));
            importTable.Columns.Add("SampleNum", typeof(string));
            importTable.Columns.Add("RSRP0值", typeof(string));
            importTable.Columns.Add("RSRP1值", typeof(string));
            importTable.Columns.Add("pathloss值", typeof(string));
            importTable.Columns.Add("rssiArray值", typeof(string));
            importTable.Columns.Add("sinr3值", typeof(string));
            importTable.Columns.Add("rsrq值", typeof(string));
            return importTable;
        }

        protected override void getBackgroundData()
        {
            string[] strProject = BackgroundFuncConfigManager.GetInstance().ProjectType.Split('|');
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Cell(Condition.Periods[0].BeginTime.AddSeconds(1),
                Condition.Periods[0].EndTime.AddSeconds(-1), GetSubFuncID(), Name, StatType, strProject[0]);
        }

        protected override void initBackgroundImageDesc()
        {
            backGrounpData.Clear();
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                #region 整理描述值
                string cellName = bgResult.GetImageValueString();
                int iECI = bgResult.GetImageValueInt();

                int sampleTotal = bgResult.GetImageValueInt();
                string strLteCoverRate = bgResult.GetImageValueString();
                string strLteRsrpRate = bgResult.GetImageValueString();
                string strLteSinrRate = bgResult.GetImageValueString();

                string strRsrp = bgResult.GetImageValueString();
                string strSinr = bgResult.GetImageValueString();
                string strDist = bgResult.GetImageValueString();

                string str主瓣采样点比例 = bgResult.GetImageValueString();
                string str主瓣小区平均RSRP = bgResult.GetImageValueString();
                string str主瓣覆盖率RSRPf110SINRf3 = bgResult.GetImageValueString();
                string str主瓣覆盖率RSRPf110 = bgResult.GetImageValueString();
                string str主瓣覆盖率SINRf3 = bgResult.GetImageValueString();
                string str旁瓣采样点比例 = bgResult.GetImageValueString();
                string str背瓣采样点比例 = bgResult.GetImageValueString();
                string str前后比 = bgResult.GetImageValueString();

                string 旁瓣小区平均RSRP = bgResult.GetImageValueString();
                string 旁瓣覆盖率RSRPf110SINRf3 = bgResult.GetImageValueString();
                string 旁瓣覆盖率RSRPf110 = bgResult.GetImageValueString();
                string 旁瓣覆盖率SINRf3 = bgResult.GetImageValueString();

                string 背瓣小区平均RSRP = bgResult.GetImageValueString();
                string 背瓣覆盖率RSRPf110SINRf3 = bgResult.GetImageValueString();
                string 背瓣覆盖率RSRPf110 = bgResult.GetImageValueString();
                string 背瓣覆盖率SINRf3 = bgResult.GetImageValueString();

                string RSRP0电平均值 = bgResult.GetImageValueString();//RSRP0电平均值
                string RSRP1电平均值 = bgResult.GetImageValueString();//RSRP1电平均值
                string DRSRP电平均值 = bgResult.GetImageValueString();//DRSRP电平均值
                string pathloss电平均值 = bgResult.GetImageValueString();//pathloss电平均值
                string pathloss指标 = bgResult.GetImageValueString();//pathloss指标

                if (!backGrounpData.ContainsKey(iECI))
                {
                    CellInfoOutPutItem ciop = new CellInfoOutPutItem();
                    ciop.CellName = cellName;
                    ciop.IECI = iECI;
                    ciop.IsampleTotal = sampleTotal;
                    setLte(strLteCoverRate, strLteRsrpRate, strLteSinrRate, ciop);

                    ciop.FRsrp = Convert.ToSingle(strRsrp);
                    ciop.FSinr = Convert.ToSingle(strSinr);
                    ciop.StrDist = strDist;
                    float 主瓣小区平均RSRP = 0;
                    if (float.TryParse(str主瓣小区平均RSRP, out 主瓣小区平均RSRP))
                        ciop.F主瓣小区平均RSRP = 主瓣小区平均RSRP;
                    ciop.Str旁瓣小区平均RSRP = 旁瓣小区平均RSRP;
                    ciop.Str背瓣小区平均RSRP = 背瓣小区平均RSRP;

                    set主瓣(str主瓣采样点比例, str主瓣覆盖率RSRPf110SINRf3, str主瓣覆盖率RSRPf110, str主瓣覆盖率SINRf3, ciop);
                    set旁瓣(str旁瓣采样点比例, 旁瓣覆盖率RSRPf110SINRf3, 旁瓣覆盖率RSRPf110, 旁瓣覆盖率SINRf3, ciop);
                    set背瓣(str背瓣采样点比例, 背瓣覆盖率RSRPf110SINRf3, 背瓣覆盖率RSRPf110, 背瓣覆盖率SINRf3, ciop);
                    int I前后比 = 0;
                    if (Int32.TryParse(str前后比, out I前后比))
                        ciop.I前后比 = I前后比;
                    setFDRSRP(RSRP0电平均值, RSRP1电平均值, DRSRP电平均值, ciop);
                    ciop.StrPathloss电平均值 = pathloss电平均值;
                    ciop.StrPathloss指标 = pathloss指标;
                    //if (query.strCellInfoDic.ContainsKey(iECI))
                    //    ciop.citem = query.strCellInfoDic[iECI];
                    backGrounpData[iECI] = ciop;
                }

                StringBuilder sb = new StringBuilder();
                sb.Append("小区名：");
                sb.Append(cellName);
                sb.Append("\r\n");
                sb.Append("ECI：");
                sb.Append(iECI);
                sb.Append("\r\n");
                sb.Append("采样点总数：");
                sb.Append(sampleTotal);
                sb.Append("\r\n");
                sb.Append("LTE覆盖率：");
                sb.Append(GetDataRate(strLteCoverRate));
                sb.Append("\r\n");
                sb.Append("RSRP覆盖率：");
                sb.Append(GetDataRate(strLteRsrpRate));
                sb.Append("\r\n");
                sb.Append("SINR覆盖率：");
                sb.Append(GetDataRate(strLteSinrRate));
                sb.Append("\r\n");
                sb.Append("平均RSRP：");
                sb.Append(strRsrp);
                sb.Append("\r\n");
                sb.Append("平均SINR：");
                sb.Append(strSinr);
                sb.Append("\r\n");
                sb.Append("过覆盖指数：");
                sb.Append(strDist);
                sb.Append("\r\n");
                sb.Append("主瓣采样点比例：");
                sb.Append(GetDataRate(str主瓣采样点比例));
                sb.Append("\r\n");
                sb.Append("主瓣小区平均RSRP：");
                sb.Append(str主瓣小区平均RSRP);
                sb.Append("\r\n");
                sb.Append("主瓣覆盖率RSRPf110SINRf3：");
                sb.Append(GetDataRate(str主瓣覆盖率RSRPf110SINRf3));
                sb.Append("\r\n");
                sb.Append("主瓣覆盖率RSRPf110：");
                sb.Append(GetDataRate(str主瓣覆盖率RSRPf110));
                sb.Append("\r\n");
                sb.Append("主瓣覆盖率SINRf3：");
                sb.Append(GetDataRate(str主瓣覆盖率SINRf3));
                sb.Append("\r\n");
                sb.Append("旁瓣采样点比例：");
                sb.Append(GetDataRate(str旁瓣采样点比例));
                sb.Append("\r\n");
                sb.Append("旁瓣小区平均RSRP：");
                sb.Append(旁瓣小区平均RSRP);
                sb.Append("\r\n");
                sb.Append("旁瓣覆盖率RSRPf110SINRf3：");
                sb.Append(GetDataRate(旁瓣覆盖率RSRPf110SINRf3));
                sb.Append("\r\n");
                sb.Append("旁瓣覆盖率RSRPf110：");
                sb.Append(GetDataRate(旁瓣覆盖率RSRPf110));
                sb.Append("\r\n");
                sb.Append("旁瓣覆盖率SINRf3：");
                sb.Append(GetDataRate(旁瓣覆盖率SINRf3));
                sb.Append("\r\n");
                sb.Append("背瓣采样点比例：");
                sb.Append(GetDataRate(str背瓣采样点比例));
                sb.Append("\r\n");
                sb.Append("背瓣小区平均RSRP：");
                sb.Append(背瓣小区平均RSRP);
                sb.Append("\r\n");
                sb.Append("背瓣覆盖率RSRPf110SINRf3：");
                sb.Append(GetDataRate(背瓣覆盖率RSRPf110SINRf3));
                sb.Append("\r\n");
                sb.Append("背瓣覆盖率RSRPf110：");
                sb.Append(GetDataRate(背瓣覆盖率RSRPf110));
                sb.Append("\r\n");
                sb.Append("背瓣覆盖率SINRf3：");
                sb.Append(GetDataRate(背瓣覆盖率SINRf3));
                sb.Append("\r\n");
                sb.Append("前后比：");
                sb.Append(str前后比);

                sb.Append("\r\n");
                sb.Append("RSRP0电平均值：");//RSRP0电平均值
                sb.Append(RSRP0电平均值);//RSRP0电平均值
                sb.Append("\r\n");
                sb.Append("RSRP1电平均值：");//RSRP1电平均值
                sb.Append(RSRP1电平均值);//RSRP1电平均值
                sb.Append("\r\n");
                sb.Append("DRSRP电平均值：");//DRSRP电平均值
                sb.Append(DRSRP电平均值);//DRSRP电平均值
                sb.Append("\r\n");
                sb.Append("pathloss电平均值：");//pathloss电平均值
                sb.Append(pathloss电平均值);//pathloss电平均值
                sb.Append("\r\n");
                sb.Append("pathloss指标：");//pathloss指标
                sb.Append(pathloss指标);//pathloss指标
                #endregion

                bgResult.ImageDesc = sb.ToString();
            }
        }

        private static void setLte(string strLteCoverRate, string strLteRsrpRate, string strLteSinrRate, CellInfoOutPutItem ciop)
        {
            float lteCR = 0;
            if (float.TryParse(strLteCoverRate, out lteCR))
                ciop.FLteCoverRate = lteCR * 100;
            float lteRSRP = 0;
            if (float.TryParse(strLteRsrpRate, out lteRSRP))
                ciop.FLteRsrpRate = lteRSRP * 100;
            float FLteSinrRate = 0;
            if (float.TryParse(strLteSinrRate, out FLteSinrRate))
                ciop.FLteSinrRate = FLteSinrRate * 100;
        }

        private static void set主瓣(string str主瓣采样点比例, string str主瓣覆盖率RSRPf110SINRf3, string str主瓣覆盖率RSRPf110, string str主瓣覆盖率SINRf3, CellInfoOutPutItem ciop)
        {
            float 主瓣采样点比例 = 0;
            if (float.TryParse(str主瓣采样点比例, out 主瓣采样点比例))
                ciop.F主瓣采样点比例 = 主瓣采样点比例 * 100;
            float F主瓣覆盖率RSRPf110SINRf3 = 0;
            if (float.TryParse(str主瓣覆盖率RSRPf110SINRf3, out F主瓣覆盖率RSRPf110SINRf3))
                ciop.F主瓣覆盖率RSRPf110SINRf3 = F主瓣覆盖率RSRPf110SINRf3 * 100;
            float F主瓣覆盖率RSRPf110 = 0;
            if (float.TryParse(str主瓣覆盖率RSRPf110, out F主瓣覆盖率RSRPf110))
                ciop.F主瓣覆盖率RSRPf110 = F主瓣覆盖率RSRPf110 * 100;
            float F主瓣覆盖率SINRf3 = 0;
            if (float.TryParse(str主瓣覆盖率SINRf3, out F主瓣覆盖率SINRf3))
                ciop.F主瓣覆盖率SINRf3 = F主瓣覆盖率SINRf3 * 100;
        }

        private static void set旁瓣(string str旁瓣采样点比例, string 旁瓣覆盖率RSRPf110SINRf3, string 旁瓣覆盖率RSRPf110, string 旁瓣覆盖率SINRf3, CellInfoOutPutItem ciop)
        {
            float 旁瓣采样点比例 = 0;
            if (float.TryParse(str旁瓣采样点比例, out 旁瓣采样点比例))
                ciop.F旁瓣采样点比例 = 旁瓣采样点比例 * 100;

            float F旁瓣覆盖率RSRPf110SINRf3 = 0;
            if (float.TryParse(旁瓣覆盖率RSRPf110SINRf3, out F旁瓣覆盖率RSRPf110SINRf3))
                ciop.F旁瓣覆盖率RSRPf110SINRf3 = F旁瓣覆盖率RSRPf110SINRf3 * 100;
            float F旁瓣覆盖率RSRPf110 = 0;
            if (float.TryParse(旁瓣覆盖率RSRPf110, out F旁瓣覆盖率RSRPf110))
                ciop.F旁瓣覆盖率RSRPf110 = F旁瓣覆盖率RSRPf110 * 100;
            float F旁瓣覆盖率SINRf3 = 0;
            if (float.TryParse(旁瓣覆盖率SINRf3, out F旁瓣覆盖率SINRf3))
                ciop.F旁瓣覆盖率SINRf3 = F旁瓣覆盖率SINRf3 * 100;
        }

        private static void set背瓣(string str背瓣采样点比例, string 背瓣覆盖率RSRPf110SINRf3, string 背瓣覆盖率RSRPf110, string 背瓣覆盖率SINRf3, CellInfoOutPutItem ciop)
        {
            float bbSample = 0;
            if (float.TryParse(str背瓣采样点比例, out bbSample))
                ciop.F背瓣采样点比例 = bbSample * 100;

            float F背瓣覆盖率RSRPf110SINRf3 = 0;
            if (float.TryParse(背瓣覆盖率RSRPf110SINRf3, out F背瓣覆盖率RSRPf110SINRf3))
                ciop.F背瓣覆盖率RSRPf110SINRf3 = F背瓣覆盖率RSRPf110SINRf3 * 100;
            float F背瓣覆盖率RSRPf110 = 0;
            if (float.TryParse(背瓣覆盖率RSRPf110, out F背瓣覆盖率RSRPf110))
                ciop.F背瓣覆盖率RSRPf110 = F背瓣覆盖率RSRPf110 * 100;
            float F背瓣覆盖率SINRf3 = 0;
            if (float.TryParse(背瓣覆盖率SINRf3, out F背瓣覆盖率SINRf3))
                ciop.F背瓣覆盖率SINRf3 = F背瓣覆盖率SINRf3 * 100;
        }

        private static void setFDRSRP(string RSRP0电平均值, string RSRP1电平均值, string DRSRP电平均值, CellInfoOutPutItem ciop)
        {
            float FRSRP0电平均值 = 0;
            if (float.TryParse(RSRP0电平均值, out FRSRP0电平均值))
                ciop.FRSRP0电平均值 = FRSRP0电平均值;
            float FRSRP1电平均值 = 0;
            if (float.TryParse(RSRP1电平均值, out FRSRP1电平均值))
                ciop.FRSRP1电平均值 = FRSRP1电平均值;
            float FDRSRP电平均值 = 0;
            if (float.TryParse(DRSRP电平均值, out FDRSRP电平均值))
                ciop.FDRSRP电平均值 = FDRSRP电平均值;
        }

        private string GetDataRate(string data)
        {
            double tmpData = 0.0;
            if (double.TryParse(data, out tmpData))
            {
                return (100.0 * tmpData).ToString("0.00") + "%";
            }
            return "-";
        }

        public Dictionary<int, CellInfoOutPutItem> getBackgroundData(int istime, int ietime, string strPro)
        {
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Cell(istime + 1,
                ietime - 1, GetSubFuncID(), Name, StatType, strPro);
            initBackgroundImageDesc();
            return backGrounpData;
        }
        #endregion

        public class CellAngleData
        {
            //小区
            public string strTime { get; set; }
            public string strcityname { get; set; }
            public string strgridname { get; set; }
            public string strbscname { get; set; }
            public string cellname { get; set; }
            public int iTac { get; set; }
            public int iEci { get; set; }
            public int uarfcn { get; set; }
            public LTEBandType band { get; set; }
            public int sampleTotal { get; set; }//该区间采样点数	

            //工参信息
            public string anaType { get; set; }//天线类型
            public int iangle_ob { get; set; }//下倾角
            public int ialtitude { get; set; }//挂高
            public int iangle_dir { get; set; }//方向角

            public CellAngleDataExt cExt { get; set; }
            public Dictionary<string, AngleData> angleDatas { get; set; }
            public CellInfoItem ciItem { get; set; }
            public AntennaPara antPara { get; set; }
            public CellPara cellPara { get; set; }
            public AntCfgSub antCfg { get; set; }
            public LteMrItem lteMRItem { get; set; }

            public List<TestPoint> tpList { get; set; }
            public string strProblemType { get; set; }//判断天线问题类型
            public double cellLongitude { get; set; }
            public double cellLatitude { get; set; }

            public List<LongLat> longLatTestList { get; set; }//小区测试模型
            public List<LongLat> longLatModelList { get; set; }//小区理想模型
            public List<BtsSubInfo> btsInfoList { get; set; }
            public Dictionary<string, int> tpIndexDic { get; set; }//存放本小区在每个采样点中序号，以便回放呈现

            public string strLteNetWorkTimeRate_1 { get; set; }
            public string strLteNetWorkTimeRate_2 { get; set; }
            public string strLteNetWorkTimeRate_3 { get; set; }
            public string strLteNetWorkTimeRate_4 { get; set; }
            public string strLteNetWorkTimeRate_5 { get; set; }
            public string strLteNetWorkTimeRate_6 { get; set; }
            public string strLteNetWorkTimeRate_7 { get; set; }
            public string strLteNetWorkTimeRate_8 { get; set; }
            public string strLteNetWorkTimeRate_Single { get; set; }
            public string strLteNetWorkTimeRate_Double { get; set; }
            public double lteNetWorkTime { get; set; }

            public string strLteCoverRate { get; set; }
            public string strLteRsrpRate { get; set; }
            public string strLteSinrRate { get; set; }

            public CellAngleData()
            {
                strTime = "";
                strcityname = "";
                strgridname = "";
                strbscname = "";
                cellname = "";
                iTac = 0;
                iEci = 0;
                uarfcn = 0;
                sampleTotal = 0;

                anaType = "";
                iangle_ob = 0;
                ialtitude = 0;
                iangle_dir = 0;

                strProblemType = "";
                cellLongitude = 0;
                cellLatitude = 0;

                strLteNetWorkTimeRate_1 = "-";
                strLteNetWorkTimeRate_2 = "-";
                strLteNetWorkTimeRate_3 = "-";
                strLteNetWorkTimeRate_4 = "-";
                strLteNetWorkTimeRate_5 = "-";
                strLteNetWorkTimeRate_6 = "-";
                strLteNetWorkTimeRate_7 = "-";
                strLteNetWorkTimeRate_8 = "-";
                strLteNetWorkTimeRate_Single = "-";
                strLteNetWorkTimeRate_Double = "-";
                lteNetWorkTime = 0;

                strLteCoverRate = "";
                strLteRsrpRate = "";
                strLteSinrRate = "";

                ciItem = new CellInfoItem();
                antPara = new AntennaPara();
                cellPara = new CellPara();
                antCfg = new AntCfgSub();

                band = LTEBandType.Undefined;
                cExt = new CellAngleDataExt();
                angleDatas = new Dictionary<string, AngleData>();
                tpList = new List<TestPoint>();
                longLatTestList = new List<LongLat>();
                longLatModelList = new List<LongLat>();
                btsInfoList = new List<BtsSubInfo>();
                tpIndexDic = new Dictionary<string, int>();
            }

            public double 小区过覆盖指数
            {
                get
                {
                    return Math.Round(sampleTotal == 0 ? 0 : cExt.iOverCellNum * 1.0 / sampleTotal, 2);
                }
            }
        }

        public class CellInfoItem
        {
            //水平面360度模型
            public Dictionary<int, ZTAntAngleItem> antAngleDic { get; set; }

            //垂直面200级模型
            public Dictionary<int, ZTAntAngleItem> antVertDic { get; set; }

            //权值理想模型
            public double[] model1Array { get; set; }
            public double[] model2Array { get; set; }
            public double[] modelMaxArray { get; set; }

            public CellInfoItem()
            {
                antAngleDic = new Dictionary<int, ZTAntAngleItem>();
                antVertDic = new Dictionary<int, ZTAntAngleItem>();
                model1Array = new double[180];
                model2Array = new double[180];
                modelMaxArray = new double[180];
            }

            //public CellInfoItem(string Rsrp, string 平滑Rsrp, string Sinr, string 过覆盖指数, string 通讯距离
            //    , string SampleNum, string strModelMaxArray, string strRSRP, string strSampleNum, string RSRP0值
            //    , string RSRP1值, string pathloss值, string rssiArray值, string sinr3值, string rsrq值)
            //{
            //    //Complete member initialization//后台体检专用
            //    antAngleDic = new Dictionary<int, ZTAntAngleItem>();
            //    antVertDic = new Dictionary<int, ZTAntAngleItem>();
            //    modelMaxArray = new double[180];
            //    string[] Rsrps = Rsrp.Split('|');
            //    foreach (string strR in Rsrps)
            //    {
            //        string[] tmpR = strR.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].IRsrp = Convert.ToInt32(tmpR[1]);
            //        }
            //    }
            //    string[] 平滑Rsrps = 平滑Rsrp.Split('|');
            //    foreach (string strRs in 平滑Rsrps)
            //    {
            //        string[] tmpR = strRs.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].DRsrpNew = Convert.ToDouble(tmpR[1]);
            //        }
            //    }
            //    string[] Sinrs = Sinr.Split('|');
            //    foreach (string strS in Sinrs)
            //    {
            //        string[] tmpR = strS.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].ISinr = Convert.ToInt32(tmpR[1]);
            //        }
            //    }
            //    string[] 过覆盖指数s = 过覆盖指数.Split('|');
            //    foreach (string strG in 过覆盖指数s)
            //    {
            //        string[] tmpR = strG.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].DCover = Convert.ToDouble(tmpR[1]);
            //        }
            //    }
            //    string[] SampleNums = SampleNum.Split('|');
            //    foreach (string strS in SampleNums)
            //    {
            //        string[] tmpR = strS.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].ISampNum = Convert.ToInt32(tmpR[1]);
            //        }
            //    }
            //    string[] 通讯距离s = 通讯距离.Split('|');
            //    foreach (string strT in 通讯距离s)
            //    {
            //        string[] tmpR = strT.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].DSampDist = Convert.ToDouble(tmpR[1]);
            //        }
            //    }

            //    string[] RSRP0值s = RSRP0值.Split('|');
            //    foreach (string strT in RSRP0值s)
            //    {
            //        string[] tmpR = strT.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].IRsrp0 = Convert.ToInt32(tmpR[1]);
            //        }
            //    }

            //    string[] RSRP1值s = RSRP1值.Split('|');
            //    foreach (string strT in RSRP1值s)
            //    {
            //        string[] tmpR = strT.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].IRsrp1 = Convert.ToInt32(tmpR[1]);
            //        }
            //    }

            //    string[] pathloss值s = pathloss值.Split('|');
            //    foreach (string strT in pathloss值s)
            //    {
            //        string[] tmpR = strT.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].IPathlsos = Convert.ToInt32(tmpR[1]);
            //        }
            //    }

            //    string[] rssiArray值s = rssiArray值.Split('|');
            //    foreach (string strT in rssiArray值s)
            //    {
            //        string[] tmpR = strT.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].IRssi = Convert.ToInt32(tmpR[1]);
            //        }
            //    }

            //    string[] sinr3值s = sinr3值.Split('|');
            //    foreach (string strT in sinr3值s)
            //    {
            //        string[] tmpR = strT.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].ISinr3 = Convert.ToInt32(tmpR[1]);
            //        }
            //    }
            //    string[] rsrq值s = rsrq值.Split('|');
            //    foreach (string strT in rsrq值s)
            //    {
            //        string[] tmpR = strT.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antAngleDic.ContainsKey(key))
            //                antAngleDic[key] = new ZTAntAngleItem();
            //            antAngleDic[key].IRsrq = Convert.ToInt32(tmpR[1]);
            //        }
            //    }

            //    string[] strModelMaxArrays = strModelMaxArray.Split('|');
            //    if (strModelMaxArrays.Length == modelMaxArray.Length)
            //    {
            //        for (int i = 0; i < strModelMaxArrays.Length; i++)
            //            modelMaxArray[i] = Convert.ToDouble(strModelMaxArrays[i]);
            //    }
            //    string[] strRSRPs = strRSRP.Split('|');
            //    foreach (string strRP in strRSRPs)
            //    {
            //        string[] tmpR = strRP.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antVertDic.ContainsKey(key))
            //                antVertDic[key] = new ZTAntAngleItem();
            //            antVertDic[key].IRsrp = Convert.ToInt32(tmpR[1]);
            //        }
            //    }
            //    string[] strSampleNums = strSampleNum.Split('|');
            //    foreach (string strSN in strSampleNums)
            //    {
            //        string[] tmpR = strSN.Split('_');
            //        int key = -1;
            //        if (Int32.TryParse(tmpR[0], out key))
            //        {
            //            if (!antVertDic.ContainsKey(key))
            //                antVertDic[key] = new ZTAntAngleItem();
            //            antVertDic[key].ISampNum = Convert.ToInt32(tmpR[1]);
            //        }
            //    }
            //}

            public CellInfoItem(string Rsrp, string 平滑Rsrp, string Sinr, string 通讯距离, string SampleNum)
            {
                //Complete member initialization//后台体检专用
                antAngleDic = new Dictionary<int, ZTAntAngleItem>();
                addAntAngleDic(Rsrp, new Func(setIRsrp));
                addAntAngleDic(平滑Rsrp, new Func(setDRsrp));
                addAntAngleDic(Sinr, new Func(setISinr));
                addAntAngleDic(通讯距离, new Func(setDSampDist));
                addAntAngleDic(SampleNum, new Func(setISampNum));
            }

            private void addAntAngleDic(string str, Func func)
            {
                string[] strs = str.Split('|');
                foreach (string strR in strs)
                {
                    string[] tmpR = strR.Split('_');
                    int key = -1;
                    if (Int32.TryParse(tmpR[0], out key))
                    {
                        if (!antAngleDic.ContainsKey(key))
                            antAngleDic[key] = new ZTAntAngleItem();

                        func(tmpR[1], key);
                    }
                }
            }

            delegate void Func(string value, int key);

            private void setIRsrp(string value, int key)
            {
                antAngleDic[key].IRsrp = Convert.ToInt32(value);
            }

            private void setDRsrp(string value, int key)
            {
                antAngleDic[key].DRsrpNew = Convert.ToDouble(value);
            }

            private void setISinr(string value, int key)
            {
                antAngleDic[key].ISinr = Convert.ToInt32(value);
            }

            private void setDSampDist(string value, int key)
            {
                antAngleDic[key].DSampDist = Convert.ToInt32(value);
            }

            private void setISampNum(string value, int key)
            {
                antAngleDic[key].ISampNum = Convert.ToInt32(value);
            }

            #region 数值封装
            /// <summary>
            /// RSRP原值-垂直面
            /// </summary>
            public int[] rsrpVertArray
            {
                get
                {
                    int[] tmp = new int[200];
                    for (int i = 0; i < 200; i++)
                    {
                        if (antVertDic.ContainsKey(i))
                            tmp[i] = antVertDic[i].IRsrp;
                        else
                            tmp[i] = -140;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// 采样点数-垂直面
            /// </summary>
            public int[] sampNumVertArray
            {
                get
                {
                    int[] tmp = new int[200];
                    for (int i = 0; i < 200; i++)
                    {
                        if (antVertDic.ContainsKey(i))
                            tmp[i] = antVertDic[i].ISampNum;
                        else
                            tmp[i] = 0;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// RSRP原值
            /// </summary>
            public int[] rsrpArray
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].IRsrp;
                        else
                            tmp[i] = -140;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// 平滑RSRP
            /// </summary>
            public double[] newRsrpArray
            {
                get
                {
                    double[] tmp = new double[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].DRsrpNew;
                        else
                            tmp[i] = -20;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// SINR原值
            /// </summary>
            public int[] sinrArray
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].ISinr;
                        else
                            tmp[i] = -25;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// 覆盖指数
            /// </summary>
            public double[] coverArray
            {
                get
                {
                    double[] tmp = new double[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].DCover;
                        else
                            tmp[i] = 0;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// 通信距离
            /// </summary>
            public double[] sampArray
            {
                get
                {
                    double[] tmp = new double[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].DSampDist;
                        else
                            tmp[i] = 0;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// 采样点总数
            /// </summary>
            public int[] sampNumArray
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].ISampNum;
                        else
                            tmp[i] = 0;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// RSRP0值
            /// </summary>
            public int[] rsrp0Array
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].IRsrp0;
                        else
                            tmp[i] = -140;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// RSRP1值
            /// </summary>
            public int[] rsrp1Array
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].IRsrp1;
                        else
                            tmp[i] = -140;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// pathloss值
            /// </summary>
            public int[] pathlossArray
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].IPathlsos;
                        else
                            tmp[i] = 0;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// rssiArray值
            /// </summary>
            public int[] rssiArray
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].IRssi;
                        else
                            tmp[i] = -140;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// sinr3值
            /// </summary>
            public int[] sinr3Array
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].ISinr3;
                        else
                            tmp[i] = 0;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// rsrq值
            /// </summary>
            public int[] rsrqArray
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].IRsrq;
                        else
                            tmp[i] = 0;
                    }
                    return tmp;
                }
            }

            #endregion

            /// <summary>
            /// 计算Gmax及半功率角
            /// </summary>
            public double[] calcCellPara()
            {
                double[] resultArray = new double[3];
                double dMaxValue = -999;
                double d3dBValue = 0;
                double d6dBValue = 0;
                foreach (double dTmp in modelMaxArray)
                {
                    dMaxValue = dMaxValue >= dTmp ? dMaxValue : dTmp;
                }
                foreach (double dTmp in modelMaxArray)
                {
                    if (dTmp > dMaxValue - 3)
                    {
                        d3dBValue++;
                    }
                    if (dTmp > dMaxValue - 6)
                    {
                        d6dBValue++;
                    }
                }
                resultArray[0] = dMaxValue;
                resultArray[1] = d3dBValue;
                resultArray[2] = d6dBValue;
                return resultArray;
            }
        }

        public class CellAngleDataExt
        {
            public float rsrpSum { get; set; }
            public float rsrpNum { get; set; }
            public float rsrpMax { get; set; }//该区间最大RXLEV	
            public float rssiSum { get; set; }
            public float rssiNum { get; set; }
            public float sinrSum { get; set; }
            public float sinrNum { get; set; }
            public float sinrNum_3 { get; set; }
            public float sinrNum_5 { get; set; }
            public float rsrqSum { get; set; }
            public float rsrqNum { get; set; }

            public float rsrp0Sum { get; set; }
            public float rsrp0Num { get; set; }
            public float rsrp1Sum { get; set; }
            public float rsrp1Num { get; set; }
            public float rscpDiffSum { get; set; }
            public float rscpDiffNum { get; set; }
            public float fpathlossSum { get; set; }
            public float fpathlossNum { get; set; }
            public double fpathlossDistanceSum { get; set; }
            public float fpathlossDistanceNum { get; set; }

            public int TASum { get; set; }//该区间TA总和
            public int TANum { get; set; }//该区间采样点数

            public int iOverCellNum { get; set; }//该区间过覆盖指数之和
            public double fSampleDistSum { get; set; }//采样点距离之和
            public double fDistCellNum { get; set; }//区域站点密集指数之和

            public int iSample0To60Num { get; set; }
            public int iSample60To150Num { get; set; }
            public int iSample150To180Num { get; set; }
            public float fRsrp0To60Sum { get; set; }
            public float fRsrp60To150Sum { get; set; }
            public float fRsrp150To180Sum { get; set; }
            public int iRsrpF110SinrF3Sample0To60Num { get; set; }
            public int iRsrpF110SinrF3Sample60To150Num { get; set; }
            public int iRsrpF110SinrF3Sample150To180Num { get; set; }
            public int iRsrpF110Sample0To60Num { get; set; }
            public int iRsrpF110Sample60To150Num { get; set; }
            public int iRsrpF110Sample150To180Num { get; set; }
            public int iSinrF3Sample0To60Num { get; set; }
            public int iSinrF3Sample60To150Num { get; set; }
            public int iSinrF3Sample150To180Num { get; set; }

            public List<int> mainRsrpList { get; set; }
            public List<int> backRsrpList { get; set; }

            public double RSRP均值
            {
                get
                {
                    return Math.Round(rsrpNum == 0 ? 0 : rsrpSum / rsrpNum, 2);
                }
            }
            public double SINR均值
            {
                get
                {
                    return Math.Round(sinrNum == 0 ? 0 : sinrSum * 1.0 / sinrNum, 2);
                }
            }
            public double RSRP0电平均值
            {
                get
                {
                    return Math.Round(rsrp0Num == 0 ? 0 : rsrp0Sum / rsrp0Num, 2);
                }
            }
            public double RSRP1电平均值
            {
                get
                {
                    return Math.Round(rsrp1Num == 0 ? 0 : rsrp1Sum / rsrp1Num, 2);
                }
            }
            public double DRSRP电平均值
            {
                get
                {
                    return Math.Round(rscpDiffNum == 0 ? 0 : rscpDiffSum / rscpDiffNum, 2);
                }
            }
            public double pathloss电平均值
            {
                get
                {
                    return Math.Round(fpathlossNum == 0 ? 0 : fpathlossSum / fpathlossNum, 2);
                }
            }
            public double pathloss指标
            {
                get
                {
                    return Math.Round(fpathlossDistanceNum == 0 ? 0 : fpathlossDistanceSum / fpathlossDistanceNum, 2);
                }
            }
            public string 主瓣采样点比例
            {
                get
                {
                    if (rsrpNum == 0)
                        return "-";
                    else
                        return (((float)iSample0To60Num) / rsrpNum).ToString();
                }
            }
            public string 主瓣小区平均RSRP
            {
                get
                {
                    if (iSample0To60Num == 0)
                        return "-";
                    else
                        return ((int)(fRsrp0To60Sum / iSample0To60Num)) + "";
                }
            }
            public string 主瓣覆盖率RSRPf110SINRf3
            {
                get
                {
                    if (iSample0To60Num == 0)
                        return "-";
                    else
                        return (((float)iRsrpF110SinrF3Sample0To60Num) / iSample0To60Num).ToString();
                }
            }
            public string 主瓣覆盖率RSRPf110
            {
                get
                {
                    if (iSample0To60Num == 0)
                        return "-";
                    else
                        return (((float)iRsrpF110Sample0To60Num) / iSample0To60Num).ToString();
                }
            }
            public string 主瓣覆盖率SINRf3
            {
                get
                {
                    if (iSample0To60Num == 0)
                        return "-";
                    else
                        return (((float)iSinrF3Sample0To60Num) / iSample0To60Num).ToString();
                }
            }
            public string 旁瓣采样点比例
            {
                get
                {
                    if (rsrpNum == 0)
                        return "-";
                    else
                        return (((float)iSample60To150Num) / rsrpNum).ToString();
                }
            }
            public string 旁瓣小区平均RSRP
            {
                get
                {
                    if (iSample60To150Num == 0)
                        return "-";
                    else
                        return ((int)(fRsrp60To150Sum / iSample60To150Num)) + "";
                }
            }
            public string 旁瓣覆盖率RSRPf110SINRf3
            {
                get
                {
                    if (iSample60To150Num == 0)
                        return "-";
                    else
                        return (((float)iRsrpF110SinrF3Sample60To150Num) / iSample60To150Num).ToString();
                }
            }
            public string 旁瓣覆盖率RSRPf110
            {
                get
                {
                    if (iSample60To150Num == 0)
                        return "-";
                    else
                        return (((float)iRsrpF110Sample60To150Num) / iSample60To150Num).ToString();
                }
            }
            public string 旁瓣覆盖率SINRf3
            {
                get
                {
                    if (iSample60To150Num == 0)
                        return "-";
                    else
                        return (((float)iSinrF3Sample60To150Num) / iSample60To150Num).ToString();
                }
            }
            public string 背瓣采样点比例
            {
                get
                {
                    if (rsrpNum == 0)
                        return "";
                    else
                        return (((float)iSample150To180Num) / rsrpNum).ToString();
                }
            }
            public string 背瓣小区平均RSRP
            {
                get
                {
                    if (iSample150To180Num == 0)
                        return "-";
                    else
                        return ((int)(fRsrp150To180Sum / iSample150To180Num)) + "";
                }
            }
            public string 背瓣覆盖率RSRPf110SINRf3
            {
                get
                {
                    if (iSample150To180Num == 0)
                        return "-";
                    else
                        return (((float)iRsrpF110SinrF3Sample150To180Num) / iSample150To180Num).ToString();
                }
            }
            public string 背瓣覆盖率RSRPf110
            {
                get
                {
                    if (iSample150To180Num == 0)
                        return "-";
                    else
                        return (((float)iRsrpF110Sample150To180Num) / iSample150To180Num).ToString();
                }
            }
            public string 背瓣覆盖率SINRf3
            {
                get
                {
                    if (iSample150To180Num == 0)
                        return "-";
                    else
                        return (((float)iSinrF3Sample150To180Num) / iSample150To180Num).ToString();
                }
            }
            public string 前后比
            {
                get
                {
                    if (mainRsrpList.Count == 0 || backRsrpList.Count == 0)
                    {
                        return "-";
                    }
                    else
                    {
                        float mainRsrpAvg = calcTop5AvgValue(mainRsrpList);
                        float backRsrpAvg = calcTop5AvgValue(backRsrpList);
                        return ((int)(mainRsrpAvg - backRsrpAvg)).ToString();
                    }
                }
            }

            /// <summary>
            /// 计算List中最大五个值的均值
            /// </summary>
            private float calcTop5AvgValue(List<int> tmpList)
            {
                tmpList.Sort();
                float sumRsrp = 0;
                float numRsrp = 0;
                int iCount = tmpList.Count;
                int iNum = tmpList.Count > 5 ? 5 : tmpList.Count;
                for (int i = iCount - 1; i >= iCount - iNum; i--)
                {
                    sumRsrp += tmpList[i];
                    numRsrp++;
                }
                float rsrpAvg = sumRsrp / numRsrp;
                return rsrpAvg;
            }

            public CellAngleDataExt()
            {
                rsrpSum = 0;
                rsrpNum = 0;
                rsrpMax = 0;
                rssiSum = 0;
                rssiNum = 0;
                sinrSum = 0;
                sinrNum = 0;
                sinrNum_3 = 0;
                sinrNum_5 = 0;
                rsrqSum = 0;
                rsrqNum = 0;

                rsrp0Sum = 0;
                rsrp0Num = 0;
                rsrp1Sum = 0;
                rsrp1Num = 0;
                rscpDiffSum = 0;
                rscpDiffNum = 0;
                fpathlossSum = 0;
                fpathlossNum = 0;
                fpathlossDistanceSum = 0;
                fpathlossDistanceNum = 0;

                TASum = 0;
                TANum = 0;

                iOverCellNum = 0;
                fSampleDistSum = 0;
                fDistCellNum = 0;

                iSample0To60Num = 0;
                iSample60To150Num = 0;
                iSample150To180Num = 0;
                fRsrp0To60Sum = 0;
                fRsrp60To150Sum = 0;
                fRsrp150To180Sum = 0;
                iRsrpF110SinrF3Sample0To60Num = 0;
                iRsrpF110SinrF3Sample60To150Num = 0;
                iRsrpF110SinrF3Sample150To180Num = 0;
                iRsrpF110Sample0To60Num = 0;
                iRsrpF110Sample60To150Num = 0;
                iRsrpF110Sample150To180Num = 0;
                iSinrF3Sample0To60Num = 0;
                iSinrF3Sample60To150Num = 0;
                iSinrF3Sample150To180Num = 0;

                mainRsrpList = new List<int>();
                backRsrpList = new List<int>();
            }

            public string Sinr
            {
                get
                {
                    try
                    {
                        if ((sinrNum_3 + sinrNum) == 0)
                        {
                            return "-";
                        }
                        else
                        {
                            return Math.Round(sinrNum_3 * 100.0 / sinrNum, 2) + "";
                        }
                    }
                    catch
                    {
                        return "";
                    }
                }
            }

            public float Sinr5_3
            {
                get
                {
                    try
                    {
                        if ((sinrNum_3 + sinrNum) == 0)
                        {
                            return 0;
                        }
                        else
                        {
                            return (float)(Math.Round(sinrNum_3 * 100.0 / sinrNum, 2));
                        }
                    }
                    catch
                    {
                        return 0;
                    }
                }
            }

            public float Sinr5_5
            {
                get
                {
                    try
                    {
                        if ((sinrNum_5 + sinrNum) == 0)
                        {
                            return 0;
                        }
                        else
                        {
                            return (float)(Math.Round(sinrNum_5 * 100.0 / sinrNum, 2));
                        }
                    }
                    catch
                    {
                        return 0;
                    }
                }
            }
        }

        public class AngleData
        {
            public AngleData()
            {
                top5RsrpList = new List<int>();
            }
            //采样点
            public float rsrpSum { get; set; }//该区间平均RXLEV(dBm)
            public int rsrpNum { get; set; }//该区间采样点数	

            public float rsrp0Sum { get; set; }
            public int rsrp0Num { get; set; }
            public float rsrp1Sum { get; set; }
            public int rsrp1Num { get; set; }
            public float rsrpDiffSum { get; set; }
            public int rsrpDiffNum { get; set; }
            public float fpathlossSum { get; set; }
            public float fpathlossNum { get; set; }

            public float rsrpMax { get; set; }//该区间最大RXLEV	
            public float rssiSum { get; set; }
            public int rssiNum { get; set; }
            public float sinrSum { get; set; }
            public int sinrNum { get; set; }
            public int sinrNum_3 { get; set; }
            public int sinrNum_5 { get; set; }
            public float rsrqSum { get; set; }
            public int rsrqNum { get; set; }
            public float TASum { get; set; }//该区间TA总和
            public int TANum { get; set; }//该区间采样点数

            public double fSampleDistSum { get; set; }//采样点距离之和
            public int iDistCellNum { get; set; }//该区间过覆盖指数之和

            public int iOverBigThan4SampleNum { get; set; }
            public float rsrpSum0_500 { get; set; }
            public float rsrpSum500_1000 { get; set; }
            public int rsrpSampleNum0_500 { get; set; }
            public int rsrpSampleNum500_1000 { get; set; }
            public List<int> top5RsrpList { get; set; }
            public float top5RsrpAvg
            {
                get
                {
                    float rsrpAvg = 0;
                    foreach (int iRxlev in top5RsrpList)
                    {
                        rsrpAvg += iRxlev;
                    }
                    return rsrpAvg / top5RsrpList.Count;
                }
            }

            public string Sinr
            {
                get
                {
                    try
                    {
                        if ((sinrNum_3 + sinrNum) == 0)
                        {
                            return "-";
                        }
                        else
                        {
                            return Math.Round(sinrNum_3 * 100.0 / sinrNum, 2) + "";
                        }
                    }
                    catch
                    {
                        return "";
                    }
                }
            }

            public float FSinr
            {
                get
                {
                    try
                    {
                        if ((sinrNum_3 + sinrNum) == 0)
                        {
                            return 0;
                        }
                        else
                        {
                            return (float)Math.Round(sinrNum_3 * 100.0 / sinrNum, 2);
                        }
                    }
                    catch
                    {
                        return 0;
                    }
                }
            }

            public float FSinrValue
            {
                get
                {
                    try
                    {
                        if ((sinrNum_5 + sinrNum) == 0)
                        {
                            return 0;
                        }
                        else
                        {
                            return (float)Math.Round(sinrNum_5 * 100.0 / sinrNum, 2);
                        }
                    }
                    catch
                    {
                        return 0;
                    }
                }
            }
        }

        public class CellInfoOutPutItem
        {
            public CellInfoOutPutItem()
            {
                FLteCoverRate = 101;
                FLteRsrpRate = 101;
                FLteSinrRate = 101;
                FRsrp = -45;
                FSinr = 40;

                F主瓣小区平均RSRP = -140;
                F主瓣覆盖率RSRPf110SINRf3 = 101;
                F主瓣覆盖率RSRPf110 = 101;
                F主瓣覆盖率SINRf3 = 101;
                F旁瓣覆盖率RSRPf110SINRf3 = 101;
                F旁瓣覆盖率RSRPf110 = 101;
                F旁瓣覆盖率SINRf3 = 101;
                F背瓣覆盖率RSRPf110SINRf3 = 101;
                F背瓣覆盖率RSRPf110 = 101;
                F背瓣覆盖率SINRf3 = 101;

                I前后比 = 10;
                FRSRP0电平均值 = -140;
                FRSRP1电平均值 = -140;
                citem = new ZTLteAntenna.CellInfoItem();
            }

            public string CellName { get; set; }
            public int IECI { get; set; }
            [Description("采样点数")]
            public int IsampleTotal { get; set; }
            [Description("覆盖率(RSRP≥-110&SINR>=-3)")]
            public float FLteCoverRate { get; set; }
            [Description("LTE覆盖率(RSRP≥-110)")]
            public float FLteRsrpRate { get; set; }
            [Description("LTE覆盖率(SINR>=-3)")]
            public float FLteSinrRate { get; set; }
            [Description("小区平均RSRP")]
            public float FRsrp { get; set; }
            [Description("小区平均SINR")]
            public float FSinr { get; set; }
            public string StrDist { get; set; }
            [Description("主瓣采样点比例")]
            public float F主瓣采样点比例 { get; set; }
            [Description("主瓣采样点数")]
            public float F主瓣采样点数
            {
                get
                {
                    return F主瓣采样点比例 * IsampleTotal / 100;
                }
                set { F主瓣采样点比例 = value; }
            }
            [Description("主瓣小区平均RSRP")]
            public float F主瓣小区平均RSRP { get; set; }
            [Description("主瓣覆盖率(RSRP≥-110&SINR>=-3)")]
            public float F主瓣覆盖率RSRPf110SINRf3 { get; set; }
            [Description("主瓣覆盖率(RSRP≥-110)")]
            public float F主瓣覆盖率RSRPf110 { get; set; }
            [Description("主瓣LTE覆盖率(SINR>=-3)")]
            public float F主瓣覆盖率SINRf3 { get; set; }
            [Description("旁瓣采样点比例")]
            public float F旁瓣采样点比例 { get; set; }
            [Description("旁瓣采样点数")]
            public float f旁瓣采样点数
            {
                get
                {
                    return IsampleTotal * F旁瓣采样点比例 / 100;
                }
            }
            [Description("旁瓣小区平均RSRP")]
            public string Str旁瓣小区平均RSRP { get; set; }
            [Description("旁瓣覆盖率(RSRP≥-110&SINR>=-3)")]
            public float F旁瓣覆盖率RSRPf110SINRf3 { get; set; }
            [Description("旁瓣LTE覆盖率(SINR>=-3)")]
            public float F旁瓣覆盖率RSRPf110 { get; set; }
            [Description("旁瓣LTE覆盖率(SINR>=-3)")]
            public float F旁瓣覆盖率SINRf3 { get; set; }
            [Description("背瓣采样点比例")]
            public float F背瓣采样点比例 { get; set; }
            [Description("背瓣采样点数")]
            public float F背瓣采样点数
            {
                get
                {
                    return F背瓣采样点比例 * IsampleTotal / 100;
                }
            }
            [Description("背瓣小区平均RSRP")]
            public string Str背瓣小区平均RSRP { get; set; }
            [Description("背瓣覆盖率(RSRP≥-110&SINR>=-3)")]
            public float F背瓣覆盖率RSRPf110SINRf3 { get; set; }
            [Description("背瓣LTE覆盖率(RSRP≥-110)")]
            public float F背瓣覆盖率RSRPf110 { get; set; }
            [Description("f背瓣覆盖率RSRPf110")]
            public float F背瓣覆盖率SINRf3 { get; set; }
            [Description("前后比")]
            public int I前后比 { get; set; }
            [Description("RSRP0电平均值")]
            public float FRSRP0电平均值 { get; set; }
            [Description("RSRP1电平均值")]
            public float FRSRP1电平均值 { get; set; }
            /// <summary>
            /// △RSRP值
            /// </summary>
            [Description("DRSRP电平均值")]
            public float FDRSRP电平均值 { get; set; }
            [Description("Pathloss电平均值")]
            public string StrPathloss电平均值 { get; set; }
            [Description("Pathloss指标")]
            public string StrPathloss指标 { get; set; }
            [Description("旁背瓣采样点比例")]
            public float f旁背瓣采样点比例
            {
                get
                {
                    return F旁瓣采样点比例 + F背瓣采样点比例;
                }
            }

            public ZTLteAntenna.CellInfoItem citem { get; set; }
        }
    }

    public class DIYCellsOfRegionStatLteAnten : DIYCellsOfRegionStatQuery
    {
        public DIYCellsOfRegionStatLteAnten(MainModel mainModel)
            : base(mainModel)
        {
            this.needFindCell = false;
        }
        public override string Name
        {
            get { return "天线分析小区级指标统计(按区域小区)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 12989, this.Name);
        }
        protected override void query()
        {
            loadReportFromFile();
            foreach (ReportStyle reportStyle in rptStyleList)
            {
                if (reportStyle.name.Contains("LTE天线分析小区级指标统计"))
                {
                    curSelStyle = reportStyle;
                }
            }
            if (curSelStyle == null && rptStyleList.Count > 0)
            {
                curSelStyle = rptStyleList[0];
            }
            curSelStyleContentAll = true;
            MainModel.CurChinaMobileStatReportData = null;
            MainModel.CurChinaUnicomStatReportData = null;
            MainModel.CurChinaTelecomStatReportData = null;
            MainModel.CurChinaMobileStatReportDataList.Clear();
            MainModel.CurChinaUnicomStatReportDataList.Clear();
            MainModel.CurChinaTelecomStatReportDataList.Clear();
            cmDataUnitAreaKPIQueryDic = null;
            cuDataUnitAreaKPIQueryDic = null;
            ctDataUnitAreaKPIQueryDic = null;
            multiGeometrys = false;
            WaitBox.CanCancel = true;
            WaitBox.Text = "小区级指标统计...";
            if (MainModel.MultiGeometrys && condition.Geometorys.SelectedResvRegions != null && condition.Geometorys.SelectedResvRegions.Count > 0)
            {
                multiGeometrys = true;
            }
            if (Condition.CarrierTypes.Contains(1) && !WaitBox.CancelRequest)
            {
                queryByCarrier(queryChinaMobileInThread);
            }
            validCellDic = new Dictionary<string, object>();
        }
        protected override void queryChinaMobileInThread(object o) //查询中国移动KPI统计
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                List<DataUnitAreaKPIQuery> paraPeriodList = new List<DataUnitAreaKPIQuery>();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, paraPeriodList, 1, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "小区级指标统计 KPI 时段[" + period.GetShortString() + "]内的数据...";
                        queryPeriodInfo(clientProxy, package, paraPeriodList, 1, period, false);
                    }
                }
                if (cmDataUnitAreaKPIQueryDic != null)
                {
                    ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDic;
                }
            }
            catch (Exception e)
            {
                log.Error(e.Message);
            }
            finally
            {
                WaitBox.Close();
            }
        }
        protected override bool isValidCell(int lac, long ci)
        {
            return true;
        }
    }

    public class DIYQueryCellDTAngleResult : DIYSQLBase
    {
        readonly Dictionary<int, int> cellEciDic;
        public DIYQueryCellDTAngleResult(MainModel mm, Dictionary<int, int> cellEciDic)
            : base(mm)
        {
            mainModel = mm;
            this.cellEciDic = cellEciDic;
            cellInfoDic = new Dictionary<int, ZTLteAntenna.CellInfoItem>();
        }
        int istime = 0;
        int ietime = 0;
        int iEci = 0;
        int iNewEci = 0;

        public override string Name
        {
            get { return "查询LTE天线360角度信息"; }
        }
        protected override string getSqlTextString()
        {
            string sql = @"select [strcellname],[iECI],[strRSRP],[strpRSRP],[strSINR],[通讯距离],[SampleNum]
                        from tb_probchk_dt_cell_angle_result where istime = " + istime + " and ietime = " + ietime;
            if (this.iEci != 0)
            {
                sql = @"select [strcellname],[iECI],[strRSRP],[strpRSRP],[strSINR],[通讯距离],[SampleNum]
                        from tb_probchk_dt_cell_angle_result where istime = " + istime + " and ietime = " + ietime +
                        " and (iECI = " + this.iEci + " or iECI = " + this.iNewEci + ")";
            }
            return sql;
        }

        public void SetCondition(int istime, int ietime)
        {
            this.istime = istime;
            this.ietime = ietime;
            this.iEci = 0;
        }

        public void SetCondition(int istime, int ietime, int iEci)
        {
            this.istime = istime;
            this.ietime = ietime;
            this.iEci = iEci;
            this.iNewEci = (iEci / 256) * 256 + ((iEci % 256) % 10);
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[7];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }
        public Dictionary<int, ZTLteAntenna.CellInfoItem> cellInfoDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            cellInfoDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    package.Content.GetParamString();//cellName
                    int Eci = package.Content.GetParamInt();
                    int newEci = (Eci / 256) * 256 + ((Eci % 256) % 10);
                    if (cellEciDic.Count != 0 && !cellEciDic.ContainsKey(newEci))
                        continue;

                    string Rsrp = package.Content.GetParamString();
                    string 平滑Rsrp = package.Content.GetParamString();
                    string Sinr = package.Content.GetParamString();
                    string 通讯距离 = package.Content.GetParamString();
                    string SampleNum = package.Content.GetParamString();
                    ZTLteAntenna.CellInfoItem cellInfoItem = new ZTLteAntenna.CellInfoItem(Rsrp, 平滑Rsrp, Sinr, 通讯距离, SampleNum);
                    if (!cellInfoDic.ContainsKey(newEci))
                        cellInfoDic[newEci] = new ZTLteAntenna.CellInfoItem();
                    cellInfoDic[newEci] = cellInfoItem;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
}
