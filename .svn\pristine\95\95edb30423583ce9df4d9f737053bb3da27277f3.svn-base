﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ProblemGridQuery;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryProblemGrid : DIYSQLBase
    {
        public QueryProblemGrid()
            : base(MainModel.GetInstance())
        {
            MainDB = true;
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        
        public DateTime DateFrom { get; set; } = DateTime.Now.AddMonths(-1);
        public DateTime DateTo { get; set; } = DateTime.Now.Date.AddDays(1).AddMilliseconds(-1);

        List<int> cityIDSet = null;

        protected override void query()
        {
            SettingDlg dlg = new SettingDlg();
            dlg.DateFrom = DateFrom;
            dlg.DateTo = DateTo;
            dlg.CityIDSet = this.cityIDSet;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            this.cityIDSet = dlg.CityIDSet;
            this.DateFrom = dlg.DateFrom;
            this.DateTo = dlg.DateTo;
            this.gridDic = new Dictionary<string, ProblemGrid>();
            orderIDDic = new Dictionary<int, ProblemOrder>();
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitTextBox.Show("正在查询工单...", queryInThread, clientProxy);
                fireShowResultForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void fireShowResultForm()
        {
            if (orderIDDic == null || orderIDDic.Count == 0)
            {
                MessageBox.Show("无问题栅格");
                return;
            }
            ProblemGridListForm form = MainModel.CreateResultForm(typeof(ProblemGridListForm)) as ProblemGridListForm;
            form.FillData(orderIDDic.Values);
            form.Visible = true;
            form.BringToFront();
            orderIDDic = null;
            gridDic = null;
        }

        private enum TbName
        {
            Order,
            Grid,
            GridKPI
        }
        private TbName curTb = TbName.Order;
        protected override void queryInThread(object o)
        {
            try
            {
                queryFromDB((ClientProxy)o, TbName.Order);
                queryFromDB((ClientProxy)o, TbName.Grid);
                queryFromDB((ClientProxy)o, TbName.GridKPI);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        private void queryFromDB(ClientProxy proxy, TbName tb)
        {
            curTb = tb;
            Package package = proxy.Package;
            string strsql = getSqlTextString();
            E_VType[] retArrDef = getSqlRetTypeArr();
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (MainDB)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
            }
            else
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strsql);
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    sb.Append(",");
                }
            }

            package.Content.AddParam(sb.ToString().TrimEnd(','));
            proxy.Send();
            receiveRetData(proxy);
        }

        protected override string getSqlTextString()
        {
            string txt = string.Empty;
            StringBuilder sb = new StringBuilder();
            foreach (int id in cityIDSet)
            {
                sb.Append( id + ",");
            }
            string ids = sb.ToString().TrimEnd(',');
            switch (curTb)
            {
                case TbName.Order:
                    txt = "select orderID,网格号,栅格号,中心经度,中心纬度,测试日期,测试文件,项目类型,业务类型,问题类型,问题栅格数,涉及问题小区,状态,cityID,projectID,serviceID from [KPIMNG_DB_SHANDONG].[dbo].[tb_grid_order]";
                    txt += string.Format(" where cityID in({0}) and 统计开始时间<'{1}' and 统计结束时间>'{2}';"
                        , ids, DateTo.ToString("yyyyMMdd HH:mm:ss"), DateFrom.ToString("yyyyMMdd HH:mm:ss"));

                    break;
                case TbName.Grid:
                    txt = "select orderSN,栅格号,中心经度,中心纬度,测试文件,小区名 from [KPIMNG_DB_SHANDONG].[dbo].[tb_grid_info]";
                    txt += string.Format(" where cityID in({0})", ids);
                    break;
                case TbName.GridKPI:
                    txt = "select orderSN,栅格号,指标名,指标值 from [KPIMNG_DB_SHANDONG].[dbo].[tb_grid_kpi] where 指标类型=1";
                    break;
                default:
                    break;
            }
            return txt;
        }

        protected override Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr = null;
            int i = 0;
            if (TbName.Order == curTb)
            {
                arr = new E_VType[16];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i] = E_VType.E_Int;
            }
            else if (TbName.Grid == curTb)
            {
                arr = new E_VType[6];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Float;
                arr[i++] = E_VType.E_Float;
                arr[i++] = E_VType.E_String;
                arr[i] = E_VType.E_String;
            }
            else if (TbName.GridKPI == curTb)
            {
                arr = new E_VType[4];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i] = E_VType.E_Float;
            }
            return arr;
        }

        Dictionary<int, ProblemOrder> orderIDDic = null;
        Dictionary<string, ProblemGrid> gridDic = null;
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            if (TbName.Order == curTb)
            {
                ProblemOrder order = new ProblemOrder(package.Content);
                orderIDDic[order.ID] = order;
            }
            else if (TbName.Grid == curTb)
            {
                ProblemGrid grid = new ProblemGrid(package.Content);
                ProblemOrder order = null;
                if (orderIDDic.TryGetValue(grid.OrderID, out order))
                {
                    order.AddGrid(grid);
                    gridDic[grid.OrderID + "_" + grid.GridSN] = grid;
                }
            }
            else if (TbName.GridKPI == curTb)
            {
                // txt = "select orderSN,栅格号,指标名,指标值,指标类型 from [KPIMNG_DB_SHANDONG].[dbo].[tb_grid_kpi] where 指标类型=1";
                int id = package.Content.GetParamInt();
                string sn = package.Content.GetParamString();
                string key = id + "_" + sn;
                ProblemGrid grid = null;
                if (gridDic.TryGetValue(key, out grid))
                {
                    string kpiName = package.Content.GetParamString();
                    double val = Math.Round((double)package.Content.GetParamFloat(), 2);
                    grid.KPIDic[kpiName] = val;
                }
            }
        }
    }
}
