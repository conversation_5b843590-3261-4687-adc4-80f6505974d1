﻿using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraTab;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class UltraSiteCellFormNR : MinCloseForm
    {
        public UltraSiteCellFormNR()
        {
            InitializeComponent();
        }

        public void FillData(List<UltraFarSiteInfo> farCells, List<UltraHighSiteInfo> highCells, List<UltraNearSiteInfo> nearSites)
        {
            gridControlFar.DataSource = farCells;
            gridControlFar.RefreshDataSource();
            gridControlHigh.DataSource = highCells;
            gridControlHigh.RefreshDataSource();
            gridControlNear.DataSource = nearSites;
            gridControlNear.RefreshDataSource();
        }

        private void miExportXlsx_Click(object sender, EventArgs e)
        {
            List<GridView> gvs = new List<GridView>();
            List<string> sheetNames = new List<string>();
            foreach (XtraTabPage tp in xtraTabControl1.TabPages)
            {
                DevExpress.XtraGrid.GridControl gc = tp.Controls[0] as DevExpress.XtraGrid.GridControl;
                GridView gv = gc.MainView as GridView;

                gvs.Add(gv);
                sheetNames.Add(tp.Text);
            }
            ExcelNPOIManager.ExportToExcel(gvs, sheetNames);
        }

        private void gvFar_DoubleClick(object sender, EventArgs e)
        {
            UltraFarSiteInfo site = gvFar.GetFocusedRow() as UltraFarSiteInfo;
            if (site != null)
            {
                setNRSite(site);
            }
        }

        private void setNRSite(UltraFarSiteInfo site)
        {
            MainModel.SelectedNRCells.Clear();
            NRCell cell = site.Cell as NRCell;
            MainModel.SelectedNRCells.Add(cell);
            double lngMax = site.Cell.Longitude;
            double latMax = site.Cell.Latitude;
            double lngMin = lngMax;
            double latMin = latMax;
            foreach (SiteDistance s in site.DirSites)
            {
                NRBTS bts = s.Site as NRBTS;
                foreach (NRCell xcell in bts.LatestCells)
                {
                    MainModel.SelectedNRCells.Add(xcell);
                }
                lngMax = Math.Max(lngMax, bts.Longitude);
                latMax = Math.Max(latMax, bts.Latitude);
                lngMin = Math.Min(lngMin, bts.Longitude);
                latMin = Math.Min(latMin, bts.Latitude);
            }
            foreach (MasterCom.RAMS.Model.ISite s in site.DelaunaySites)
            {
                NRBTS bts = s as NRBTS;
                foreach (NRCell xcell in bts.LatestCells)
                {
                    MainModel.SelectedNRCells.Add(xcell);
                }
                lngMax = Math.Max(lngMax, bts.Longitude);
                latMax = Math.Max(latMax, bts.Latitude);
                lngMin = Math.Min(lngMin, bts.Longitude);
                latMin = Math.Min(latMin, bts.Latitude);
            }
            DbRect rect = new DbRect(lngMin - 0.001, latMin - 0.001, lngMax + 0.001, latMax + 0.001);
            MainModel.MainForm.GetMapForm().GoToView(rect);
        }

        private void gvHigh_DoubleClick(object sender, EventArgs e)
        {
            MainModel.SelectedNRCells.Clear();
            UltraHighSiteInfo site = gvHigh.GetFocusedRow() as UltraHighSiteInfo;
            if (site != null)
            {
                NRCell cell = site.Cell as NRCell;
                MainModel.SelectedNRCells.Add(cell);
                MainModel.MainForm.GetMapForm().GoToView(cell.Longitude, cell.Latitude, 6000);
            }
        }

        private void gvNear_DoubleClick(object sender, EventArgs e)
        {
            MainModel.SelectedNRCells.Clear();
            UltraNearSiteInfo site = gvNear.GetFocusedRow() as UltraNearSiteInfo;
            if (site != null)
            {
                double lngMax = 0;
                double latMax = 0;
                double lngMin = 0;
                double latMin = 0;

                NRBTS bts = site.Site as NRBTS;
                foreach (NRCell cell in bts.LatestCells)
                {
                    MainModel.SelectedNRCells.Add(cell);
                }
                bts = site.OtherSite as NRBTS;
                foreach (NRCell cell in bts.LatestCells)
                {
                    MainModel.SelectedNRCells.Add(cell);
                }
                lngMax = Math.Max(site.Site.Longitude, site.OtherSite.Longitude);
                lngMin = Math.Min(site.Site.Longitude, site.OtherSite.Longitude);
                latMax = Math.Max(site.Site.Latitude, site.OtherSite.Latitude);
                latMin = Math.Min(site.Site.Latitude, site.OtherSite.Latitude);
                DbRect rect = new DbRect(lngMin - 0.001, latMin - 0.001, lngMax + 0.001, latMax + 0.001);
                MainModel.MainForm.GetMapForm().GoToView(rect);
            }
        }
    }
}
