﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEAntennaOverlapCoverageSetForm : BaseDialog
    {

        public LTEAntennaOverlapCoverageSetForm()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public void GetSettingFilterRet(out int iMinLevel, out int iMaxLevel)
        {
            iMinLevel = (int)minLevelValue.Value;
            iMaxLevel = (int)maxLevelValue.Value;
        }
    }
}
