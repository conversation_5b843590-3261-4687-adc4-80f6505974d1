﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTNRScanGoodRsrpPoorSinr;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanGoodRsrpPoorSinr : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        private static NRScanGoodRsrpPoorSinr intance = null;
        protected static readonly object lockObj = new object();
        public static NRScanGoodRsrpPoorSinr GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRScanGoodRsrpPoorSinr();
                    }
                }
            }
            return intance;
        }

        protected NRScanGoodRsrpPoorSinr()
            : base(MainModel.GetInstance())
        {
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRScan);
        }

        public override string Name
        {
            get { return "高电平质差_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36012, this.Name);
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cellDic = new Dictionary<NRCell, NRScanGoodRsrpPoorSinrCell>();
        }

        private Dictionary<NRCell, NRScanGoodRsrpPoorSinrCell> cellDic = null;
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (!Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
            {
                return false;
            }

            float mainRsrp = float.NaN;
            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(tp);
            foreach (var i in groupDic.Values)
            {
                NRCell cell = tp.GetCell_NRScan(i);
                cell = setCellName(tp, i, cell);
                float rsrp;
                float sinr;
                bool isValid = getValidData(tp, i, out rsrp, out sinr);
                if (!isValid)
                {
                    break;
                }

                if (float.IsNaN(mainRsrp))
                {
                    mainRsrp = rsrp;
                }
                isValid = addValidData(tp, mainRsrp, i, cell, rsrp, sinr);
                if (!isValid)
                {
                    break;
                }
            }
            return false;
        }

        private bool addValidData(TestPoint tp, float mainRsrp, int i, NRCell cell, float rsrp, float sinr)
        {
            if (rsrp >= funcCond.Rsrp && sinr <= funcCond.Sinr)
            {
                if (funcCond.CheckBand)
                {
                    float band = mainRsrp - rsrp;
                    if (band > funcCond.CoverBand)
                    {
                        //超过设定覆盖带，跳出
                        return false;
                    }
                }
                NRScanGoodRsrpPoorSinrCell cellItem;
                if (!cellDic.TryGetValue(cell, out cellItem))
                {
                    cellItem = new NRScanGoodRsrpPoorSinrCell(cell);
                    cellDic.Add(cell, cellItem);
                }
                cellItem.AddPoint(tp, rsrp, sinr, i == 0);
            }
            return true;
        }

        private bool getValidData(TestPoint tp, int i, out float rsrp, out float sinr)
        {
            rsrp = -9999;
            sinr = -9999;
            float? value = NRTpHelper.NrScanTpManager.GetCellRsrp(tp, i, true);
            if (value == null)
            {
                return false;
            }
            rsrp = (float)value;

            value = NRTpHelper.NrScanTpManager.GetCellSinr(tp, i, true);
            if (value == null)
            {
                return false;
            }
            sinr = (float)value;
            return true;
        }

        private static NRCell setCellName(TestPoint tp, int i, NRCell cell)
        {
            if (cell == null)
            {
                cell = new NRCell();

                int? earfcn = (int?)NRTpHelper.NrScanTpManager.GetEARFCN(tp, i);
                int? pci = (int?)NRTpHelper.NrScanTpManager.GetPCI(tp, i);
                if (earfcn != null && pci != null)
                {
                    cell.Name = earfcn.ToString() + "_" + pci.ToString();
                }
            }

            return cell;
        }

        FuncCondition funcCond = null;
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            NRScanGoodRsrpPoorSinrDlg dlg = new NRScanGoodRsrpPoorSinrDlg();
            dlg.Condition = funcCond;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.Condition;
            return true;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            return NRTpHelper.InitNrScanParamSample(NRTpHelper.NrScanTpManager.SinrThemeName);
        }

        protected override void getResultAfterQuery()
        {
            int sn = 1;
            foreach (NRScanGoodRsrpPoorSinrCell cell in cellDic.Values)
            {
                cell.Sn = sn++;
                cell.MakeSummary();
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            NRScanGoodRsrpPoorSinrForm form = MainModel.GetInstance().CreateResultForm(typeof(NRScanGoodRsrpPoorSinrForm)) as NRScanGoodRsrpPoorSinrForm;
            form.FillData(new List<NRScanGoodRsrpPoorSinrCell>(cellDic.Values));
            form.Visible = true;
            form.BringToFront();
            cellDic = null;
        }

    }
}
