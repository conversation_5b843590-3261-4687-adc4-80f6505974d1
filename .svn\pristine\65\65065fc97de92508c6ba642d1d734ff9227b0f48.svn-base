﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTPointImportForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridColumnPointName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAddr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumntlLong = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumntlLat = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnbrLong = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnbrLat = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAltitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.labelFormat = new System.Windows.Forms.Label();
            this.buttonImport = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.btnFileOpen = new System.Windows.Forms.Button();
            this.textBoxFilePath = new System.Windows.Forms.TextBox();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAliasName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPointType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDensityType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSpaceType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCoverType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetWorkType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBelongArea = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBelongArea2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetTypeCell1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLacCell1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCiCell1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNameCell1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBSCCell1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetTypeCell2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLacCell2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCiCell2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNameCell2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBSCCell2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetTypeCell3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLacCell3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCiCell3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNameCell3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBSCCell3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetTypeCell4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLacCell4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCiCell4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNameCell4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBSCCell4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetTypeCell5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLacCell5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCiCell5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNameCell5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBSCCell5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetTypeCell6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLacCell6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCiCell6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNameCell6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBSCCell6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.labelFormat_Atu = new System.Windows.Forms.Label();
            this.buttonImport_Atu = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.btnFileOpen_Atu = new System.Windows.Forms.Button();
            this.textBoxFilePath_Atu = new System.Windows.Forms.TextBox();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.gridControl_Atu = new DevExpress.XtraGrid.GridControl();
            this.gridViewAtu = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tableLayoutPanel1.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl_Atu)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewAtu)).BeginInit();
            this.SuspendLayout();
            // 
            // gridColumnPointName
            // 
            this.gridColumnPointName.Caption = "名称";
            this.gridColumnPointName.FieldName = "Name";
            this.gridColumnPointName.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumnPointName.Name = "gridColumnPointName";
            this.gridColumnPointName.Visible = true;
            this.gridColumnPointName.VisibleIndex = 0;
            // 
            // gridColumnAddr
            // 
            this.gridColumnAddr.Caption = "地址";
            this.gridColumnAddr.FieldName = "AddrAt";
            this.gridColumnAddr.Name = "gridColumnAddr";
            this.gridColumnAddr.Visible = true;
            this.gridColumnAddr.VisibleIndex = 1;
            // 
            // gridColumntlLong
            // 
            this.gridColumntlLong.Caption = "左上角经度";
            this.gridColumntlLong.FieldName = "LTLongitude";
            this.gridColumntlLong.Name = "gridColumntlLong";
            this.gridColumntlLong.Visible = true;
            this.gridColumntlLong.VisibleIndex = 3;
            // 
            // gridColumntlLat
            // 
            this.gridColumntlLat.Caption = "左上角纬度";
            this.gridColumntlLat.FieldName = "LTLatitude";
            this.gridColumntlLat.Name = "gridColumntlLat";
            this.gridColumntlLat.Visible = true;
            this.gridColumntlLat.VisibleIndex = 4;
            // 
            // gridColumnbrLong
            // 
            this.gridColumnbrLong.Caption = "右下角经度";
            this.gridColumnbrLong.FieldName = "BRLongitude";
            this.gridColumnbrLong.Name = "gridColumnbrLong";
            this.gridColumnbrLong.Visible = true;
            this.gridColumnbrLong.VisibleIndex = 5;
            // 
            // gridColumnbrLat
            // 
            this.gridColumnbrLat.Caption = "右下角纬度";
            this.gridColumnbrLat.FieldName = "BRLatitude";
            this.gridColumnbrLat.Name = "gridColumnbrLat";
            this.gridColumnbrLat.Visible = true;
            this.gridColumnbrLat.VisibleIndex = 6;
            // 
            // gridColumnAltitude
            // 
            this.gridColumnAltitude.Caption = "海拔";
            this.gridColumnAltitude.FieldName = "Altitude";
            this.gridColumnAltitude.Name = "gridColumnAltitude";
            this.gridColumnAltitude.Visible = true;
            this.gridColumnAltitude.VisibleIndex = 7;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 1;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Controls.Add(this.panel1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.groupControl1, 0, 1);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 1;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 54F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(862, 386);
            this.tableLayoutPanel1.TabIndex = 4;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.labelFormat);
            this.panel1.Controls.Add(this.buttonImport);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Controls.Add(this.btnFileOpen);
            this.panel1.Controls.Add(this.textBoxFilePath);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(856, 48);
            this.panel1.TabIndex = 1;
            // 
            // labelFormat
            // 
            this.labelFormat.AutoSize = true;
            this.labelFormat.Font = new System.Drawing.Font("Tahoma", 20F);
            this.labelFormat.ForeColor = System.Drawing.Color.Red;
            this.labelFormat.Location = new System.Drawing.Point(668, 9);
            this.labelFormat.Name = "labelFormat";
            this.labelFormat.Size = new System.Drawing.Size(123, 33);
            this.labelFormat.TabIndex = 8;
            this.labelFormat.Text = "格式正确";
            // 
            // buttonImport
            // 
            this.buttonImport.Location = new System.Drawing.Point(575, 12);
            this.buttonImport.Name = "buttonImport";
            this.buttonImport.Size = new System.Drawing.Size(87, 27);
            this.buttonImport.TabIndex = 7;
            this.buttonImport.Text = "导入";
            this.buttonImport.UseVisualStyleBackColor = true;
            this.buttonImport.Click += new System.EventHandler(this.buttonImport_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(10, 17);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(55, 14);
            this.label1.TabIndex = 5;
            this.label1.Text = "文件路径";
            // 
            // btnFileOpen
            // 
            this.btnFileOpen.Location = new System.Drawing.Point(478, 12);
            this.btnFileOpen.Name = "btnFileOpen";
            this.btnFileOpen.Size = new System.Drawing.Size(89, 27);
            this.btnFileOpen.TabIndex = 1;
            this.btnFileOpen.Text = "选择文件";
            this.btnFileOpen.UseVisualStyleBackColor = true;
            this.btnFileOpen.Click += new System.EventHandler(this.btnFileOpen_Click);
            // 
            // textBoxFilePath
            // 
            this.textBoxFilePath.Enabled = false;
            this.textBoxFilePath.Location = new System.Drawing.Point(73, 13);
            this.textBoxFilePath.Name = "textBoxFilePath";
            this.textBoxFilePath.Size = new System.Drawing.Size(391, 22);
            this.textBoxFilePath.TabIndex = 0;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.gridControl1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(3, 57);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(856, 326);
            this.groupControl1.TabIndex = 2;
            this.groupControl1.Text = "错误列表";
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(2, 23);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(852, 301);
            this.gridControl1.TabIndex = 0;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(137, 26);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemExport.Text = "导出到xls...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnPointName,
            this.gridColumnAddr,
            this.gridColumnDesc,
            this.gridColumntlLong,
            this.gridColumntlLat,
            this.gridColumnbrLong,
            this.gridColumnbrLat,
            this.gridColumnAltitude,
            this.gridColumnAliasName,
            this.gridColumnPointType,
            this.gridColumnDensityType,
            this.gridColumnSpaceType,
            this.gridColumnCoverType,
            this.gridColumnNetWorkType,
            this.gridColumnBelongArea,
            this.gridColumnBelongArea2,
            this.gridColumnNetTypeCell1,
            this.gridColumnLacCell1,
            this.gridColumnCiCell1,
            this.gridColumnNameCell1,
            this.gridColumnBSCCell1,
            this.gridColumnNetTypeCell2,
            this.gridColumnLacCell2,
            this.gridColumnCiCell2,
            this.gridColumnNameCell2,
            this.gridColumnBSCCell2,
            this.gridColumnNetTypeCell3,
            this.gridColumnLacCell3,
            this.gridColumnCiCell3,
            this.gridColumnNameCell3,
            this.gridColumnBSCCell3,
            this.gridColumnNetTypeCell4,
            this.gridColumnLacCell4,
            this.gridColumnCiCell4,
            this.gridColumnNameCell4,
            this.gridColumnBSCCell4,
            this.gridColumnNetTypeCell5,
            this.gridColumnLacCell5,
            this.gridColumnCiCell5,
            this.gridColumnNameCell5,
            this.gridColumnBSCCell5,
            this.gridColumnNetTypeCell6,
            this.gridColumnLacCell6,
            this.gridColumnCiCell6,
            this.gridColumnNameCell6,
            this.gridColumnBSCCell6});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsDetail.EnableDetailToolTip = true;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.gridView1_CustomDrawCell);
            // 
            // gridColumnDesc
            // 
            this.gridColumnDesc.Caption = "描述信息";
            this.gridColumnDesc.FieldName = "Desc";
            this.gridColumnDesc.Name = "gridColumnDesc";
            this.gridColumnDesc.Visible = true;
            this.gridColumnDesc.VisibleIndex = 2;
            // 
            // gridColumnAliasName
            // 
            this.gridColumnAliasName.Caption = "别名";
            this.gridColumnAliasName.FieldName = "AliasName";
            this.gridColumnAliasName.Name = "gridColumnAliasName";
            this.gridColumnAliasName.Visible = true;
            this.gridColumnAliasName.VisibleIndex = 8;
            // 
            // gridColumnPointType
            // 
            this.gridColumnPointType.Caption = "地点类型";
            this.gridColumnPointType.FieldName = "PointType";
            this.gridColumnPointType.Name = "gridColumnPointType";
            this.gridColumnPointType.Visible = true;
            this.gridColumnPointType.VisibleIndex = 9;
            // 
            // gridColumnDensityType
            // 
            this.gridColumnDensityType.Caption = "密度类型";
            this.gridColumnDensityType.FieldName = "DensityType";
            this.gridColumnDensityType.Name = "gridColumnDensityType";
            this.gridColumnDensityType.Visible = true;
            this.gridColumnDensityType.VisibleIndex = 10;
            // 
            // gridColumnSpaceType
            // 
            this.gridColumnSpaceType.Caption = "建筑类型";
            this.gridColumnSpaceType.FieldName = "SpaceType";
            this.gridColumnSpaceType.Name = "gridColumnSpaceType";
            this.gridColumnSpaceType.Visible = true;
            this.gridColumnSpaceType.VisibleIndex = 11;
            // 
            // gridColumnCoverType
            // 
            this.gridColumnCoverType.Caption = "覆盖类型";
            this.gridColumnCoverType.FieldName = "CoverType";
            this.gridColumnCoverType.Name = "gridColumnCoverType";
            this.gridColumnCoverType.Visible = true;
            this.gridColumnCoverType.VisibleIndex = 12;
            this.gridColumnCoverType.Width = 60;
            // 
            // gridColumnNetWorkType
            // 
            this.gridColumnNetWorkType.Caption = "网络类型";
            this.gridColumnNetWorkType.FieldName = "NetworkType";
            this.gridColumnNetWorkType.Name = "gridColumnNetWorkType";
            this.gridColumnNetWorkType.Visible = true;
            this.gridColumnNetWorkType.VisibleIndex = 13;
            this.gridColumnNetWorkType.Width = 60;
            // 
            // gridColumnBelongArea
            // 
            this.gridColumnBelongArea.Caption = "所属区域1";
            this.gridColumnBelongArea.FieldName = "BelongArea";
            this.gridColumnBelongArea.Name = "gridColumnBelongArea";
            this.gridColumnBelongArea.Visible = true;
            this.gridColumnBelongArea.VisibleIndex = 14;
            this.gridColumnBelongArea.Width = 67;
            // 
            // gridColumnBelongArea2
            // 
            this.gridColumnBelongArea2.Caption = "所属区域2";
            this.gridColumnBelongArea2.FieldName = "BelongArea2";
            this.gridColumnBelongArea2.Name = "gridColumnBelongArea2";
            this.gridColumnBelongArea2.Visible = true;
            this.gridColumnBelongArea2.VisibleIndex = 15;
            this.gridColumnBelongArea2.Width = 67;
            // 
            // gridColumnNetTypeCell1
            // 
            this.gridColumnNetTypeCell1.Caption = "覆盖小区1_网格类型";
            this.gridColumnNetTypeCell1.FieldName = "NetType_Cell1";
            this.gridColumnNetTypeCell1.Name = "gridColumnNetTypeCell1";
            this.gridColumnNetTypeCell1.Visible = true;
            this.gridColumnNetTypeCell1.VisibleIndex = 16;
            this.gridColumnNetTypeCell1.Width = 122;
            // 
            // gridColumnLacCell1
            // 
            this.gridColumnLacCell1.Caption = "覆盖小区1_LAC";
            this.gridColumnLacCell1.FieldName = "LAC_Cell1";
            this.gridColumnLacCell1.Name = "gridColumnLacCell1";
            this.gridColumnLacCell1.Visible = true;
            this.gridColumnLacCell1.VisibleIndex = 17;
            this.gridColumnLacCell1.Width = 95;
            // 
            // gridColumnCiCell1
            // 
            this.gridColumnCiCell1.Caption = "覆盖小区1_CI";
            this.gridColumnCiCell1.FieldName = "CI_Cell1";
            this.gridColumnCiCell1.Name = "gridColumnCiCell1";
            this.gridColumnCiCell1.Visible = true;
            this.gridColumnCiCell1.VisibleIndex = 18;
            this.gridColumnCiCell1.Width = 85;
            // 
            // gridColumnNameCell1
            // 
            this.gridColumnNameCell1.Caption = "覆盖小区1_小区名称";
            this.gridColumnNameCell1.FieldName = "CellName_Cell1";
            this.gridColumnNameCell1.Name = "gridColumnNameCell1";
            this.gridColumnNameCell1.Visible = true;
            this.gridColumnNameCell1.VisibleIndex = 19;
            this.gridColumnNameCell1.Width = 122;
            // 
            // gridColumnBSCCell1
            // 
            this.gridColumnBSCCell1.Caption = "覆盖小区1_BSC名称";
            this.gridColumnBSCCell1.FieldName = "BSCName_Cell1";
            this.gridColumnBSCCell1.Name = "gridColumnBSCCell1";
            this.gridColumnBSCCell1.Visible = true;
            this.gridColumnBSCCell1.VisibleIndex = 20;
            this.gridColumnBSCCell1.Width = 119;
            // 
            // gridColumnNetTypeCell2
            // 
            this.gridColumnNetTypeCell2.Caption = "覆盖小区2_网络类型";
            this.gridColumnNetTypeCell2.FieldName = "NetType_Cell2";
            this.gridColumnNetTypeCell2.Name = "gridColumnNetTypeCell2";
            this.gridColumnNetTypeCell2.Visible = true;
            this.gridColumnNetTypeCell2.VisibleIndex = 21;
            this.gridColumnNetTypeCell2.Width = 122;
            // 
            // gridColumnLacCell2
            // 
            this.gridColumnLacCell2.Caption = "覆盖小区2_LAC";
            this.gridColumnLacCell2.FieldName = "LAC_Cell2";
            this.gridColumnLacCell2.Name = "gridColumnLacCell2";
            this.gridColumnLacCell2.Visible = true;
            this.gridColumnLacCell2.VisibleIndex = 22;
            this.gridColumnLacCell2.Width = 95;
            // 
            // gridColumnCiCell2
            // 
            this.gridColumnCiCell2.Caption = "覆盖小区2_CI";
            this.gridColumnCiCell2.FieldName = "CI_Cell2";
            this.gridColumnCiCell2.Name = "gridColumnCiCell2";
            this.gridColumnCiCell2.Visible = true;
            this.gridColumnCiCell2.VisibleIndex = 23;
            this.gridColumnCiCell2.Width = 85;
            // 
            // gridColumnNameCell2
            // 
            this.gridColumnNameCell2.Caption = "覆盖小区2_小区名称";
            this.gridColumnNameCell2.FieldName = "CellName_Cell2";
            this.gridColumnNameCell2.Name = "gridColumnNameCell2";
            this.gridColumnNameCell2.Visible = true;
            this.gridColumnNameCell2.VisibleIndex = 24;
            this.gridColumnNameCell2.Width = 122;
            // 
            // gridColumnBSCCell2
            // 
            this.gridColumnBSCCell2.Caption = "覆盖小区2_BSC名称";
            this.gridColumnBSCCell2.FieldName = "BSCName_Cell2";
            this.gridColumnBSCCell2.Name = "gridColumnBSCCell2";
            this.gridColumnBSCCell2.Visible = true;
            this.gridColumnBSCCell2.VisibleIndex = 25;
            this.gridColumnBSCCell2.Width = 119;
            // 
            // gridColumnNetTypeCell3
            // 
            this.gridColumnNetTypeCell3.Caption = "覆盖小区3_网络类型";
            this.gridColumnNetTypeCell3.FieldName = "NetType_Cell3";
            this.gridColumnNetTypeCell3.Name = "gridColumnNetTypeCell3";
            this.gridColumnNetTypeCell3.Visible = true;
            this.gridColumnNetTypeCell3.VisibleIndex = 26;
            this.gridColumnNetTypeCell3.Width = 122;
            // 
            // gridColumnLacCell3
            // 
            this.gridColumnLacCell3.Caption = "覆盖小区3_LAC";
            this.gridColumnLacCell3.FieldName = "LAC_Cell3";
            this.gridColumnLacCell3.Name = "gridColumnLacCell3";
            this.gridColumnLacCell3.Visible = true;
            this.gridColumnLacCell3.VisibleIndex = 27;
            this.gridColumnLacCell3.Width = 95;
            // 
            // gridColumnCiCell3
            // 
            this.gridColumnCiCell3.Caption = "覆盖小区3_CI";
            this.gridColumnCiCell3.FieldName = "CI_Cell3";
            this.gridColumnCiCell3.Name = "gridColumnCiCell3";
            this.gridColumnCiCell3.Visible = true;
            this.gridColumnCiCell3.VisibleIndex = 28;
            this.gridColumnCiCell3.Width = 85;
            // 
            // gridColumnNameCell3
            // 
            this.gridColumnNameCell3.Caption = "覆盖小区3_小区名称";
            this.gridColumnNameCell3.FieldName = "CellName_Cell3";
            this.gridColumnNameCell3.Name = "gridColumnNameCell3";
            this.gridColumnNameCell3.Visible = true;
            this.gridColumnNameCell3.VisibleIndex = 29;
            this.gridColumnNameCell3.Width = 122;
            // 
            // gridColumnBSCCell3
            // 
            this.gridColumnBSCCell3.Caption = "覆盖小区3_BSC名称";
            this.gridColumnBSCCell3.FieldName = "BSCName_Cell3";
            this.gridColumnBSCCell3.Name = "gridColumnBSCCell3";
            this.gridColumnBSCCell3.Visible = true;
            this.gridColumnBSCCell3.VisibleIndex = 30;
            this.gridColumnBSCCell3.Width = 119;
            // 
            // gridColumnNetTypeCell4
            // 
            this.gridColumnNetTypeCell4.Caption = "覆盖小区4_网络类型";
            this.gridColumnNetTypeCell4.FieldName = "NetType_Cell4";
            this.gridColumnNetTypeCell4.Name = "gridColumnNetTypeCell4";
            this.gridColumnNetTypeCell4.Visible = true;
            this.gridColumnNetTypeCell4.VisibleIndex = 31;
            this.gridColumnNetTypeCell4.Width = 122;
            // 
            // gridColumnLacCell4
            // 
            this.gridColumnLacCell4.Caption = "覆盖小区4_LAC";
            this.gridColumnLacCell4.FieldName = "LAC_Cell4";
            this.gridColumnLacCell4.Name = "gridColumnLacCell4";
            this.gridColumnLacCell4.Visible = true;
            this.gridColumnLacCell4.VisibleIndex = 32;
            this.gridColumnLacCell4.Width = 95;
            // 
            // gridColumnCiCell4
            // 
            this.gridColumnCiCell4.Caption = "覆盖小区4_CI";
            this.gridColumnCiCell4.FieldName = "CI_Cell4";
            this.gridColumnCiCell4.Name = "gridColumnCiCell4";
            this.gridColumnCiCell4.Visible = true;
            this.gridColumnCiCell4.VisibleIndex = 33;
            this.gridColumnCiCell4.Width = 85;
            // 
            // gridColumnNameCell4
            // 
            this.gridColumnNameCell4.Caption = "覆盖小区4_小区名称";
            this.gridColumnNameCell4.FieldName = "CellName_Cell4";
            this.gridColumnNameCell4.Name = "gridColumnNameCell4";
            this.gridColumnNameCell4.Visible = true;
            this.gridColumnNameCell4.VisibleIndex = 34;
            this.gridColumnNameCell4.Width = 122;
            // 
            // gridColumnBSCCell4
            // 
            this.gridColumnBSCCell4.Caption = "覆盖小区4_BSC名称";
            this.gridColumnBSCCell4.FieldName = "BSCName_Cell4";
            this.gridColumnBSCCell4.Name = "gridColumnBSCCell4";
            this.gridColumnBSCCell4.Visible = true;
            this.gridColumnBSCCell4.VisibleIndex = 35;
            this.gridColumnBSCCell4.Width = 119;
            // 
            // gridColumnNetTypeCell5
            // 
            this.gridColumnNetTypeCell5.Caption = "覆盖小区5_网络类型";
            this.gridColumnNetTypeCell5.FieldName = "NetType_Cell5";
            this.gridColumnNetTypeCell5.Name = "gridColumnNetTypeCell5";
            this.gridColumnNetTypeCell5.Visible = true;
            this.gridColumnNetTypeCell5.VisibleIndex = 36;
            this.gridColumnNetTypeCell5.Width = 122;
            // 
            // gridColumnLacCell5
            // 
            this.gridColumnLacCell5.Caption = "覆盖小区5_LAC";
            this.gridColumnLacCell5.FieldName = "LAC_Cell5";
            this.gridColumnLacCell5.Name = "gridColumnLacCell5";
            this.gridColumnLacCell5.Visible = true;
            this.gridColumnLacCell5.VisibleIndex = 37;
            this.gridColumnLacCell5.Width = 95;
            // 
            // gridColumnCiCell5
            // 
            this.gridColumnCiCell5.Caption = "覆盖小区5_CI";
            this.gridColumnCiCell5.FieldName = "CI_Cell5";
            this.gridColumnCiCell5.Name = "gridColumnCiCell5";
            this.gridColumnCiCell5.Visible = true;
            this.gridColumnCiCell5.VisibleIndex = 38;
            this.gridColumnCiCell5.Width = 85;
            // 
            // gridColumnNameCell5
            // 
            this.gridColumnNameCell5.Caption = "覆盖小区5_小区名称";
            this.gridColumnNameCell5.FieldName = "CellName_Cell5";
            this.gridColumnNameCell5.Name = "gridColumnNameCell5";
            this.gridColumnNameCell5.Visible = true;
            this.gridColumnNameCell5.VisibleIndex = 39;
            this.gridColumnNameCell5.Width = 122;
            // 
            // gridColumnBSCCell5
            // 
            this.gridColumnBSCCell5.Caption = "覆盖小区5_BSC名称";
            this.gridColumnBSCCell5.FieldName = "BSCName_Cell5";
            this.gridColumnBSCCell5.Name = "gridColumnBSCCell5";
            this.gridColumnBSCCell5.Visible = true;
            this.gridColumnBSCCell5.VisibleIndex = 40;
            this.gridColumnBSCCell5.Width = 119;
            // 
            // gridColumnNetTypeCell6
            // 
            this.gridColumnNetTypeCell6.Caption = "覆盖小区6_网络类型";
            this.gridColumnNetTypeCell6.FieldName = "NetType_Cell6";
            this.gridColumnNetTypeCell6.Name = "gridColumnNetTypeCell6";
            this.gridColumnNetTypeCell6.Visible = true;
            this.gridColumnNetTypeCell6.VisibleIndex = 41;
            this.gridColumnNetTypeCell6.Width = 122;
            // 
            // gridColumnLacCell6
            // 
            this.gridColumnLacCell6.Caption = "覆盖小区6_LAC";
            this.gridColumnLacCell6.FieldName = "LAC_Cell6";
            this.gridColumnLacCell6.Name = "gridColumnLacCell6";
            this.gridColumnLacCell6.Visible = true;
            this.gridColumnLacCell6.VisibleIndex = 42;
            this.gridColumnLacCell6.Width = 95;
            // 
            // gridColumnCiCell6
            // 
            this.gridColumnCiCell6.Caption = "覆盖小区6_CI";
            this.gridColumnCiCell6.FieldName = "CI_Cell6";
            this.gridColumnCiCell6.Name = "gridColumnCiCell6";
            this.gridColumnCiCell6.Visible = true;
            this.gridColumnCiCell6.VisibleIndex = 43;
            this.gridColumnCiCell6.Width = 85;
            // 
            // gridColumnNameCell6
            // 
            this.gridColumnNameCell6.Caption = "覆盖小区6_小区名称";
            this.gridColumnNameCell6.FieldName = "CellName_Cell6";
            this.gridColumnNameCell6.Name = "gridColumnNameCell6";
            this.gridColumnNameCell6.Visible = true;
            this.gridColumnNameCell6.VisibleIndex = 44;
            this.gridColumnNameCell6.Width = 122;
            // 
            // gridColumnBSCCell6
            // 
            this.gridColumnBSCCell6.Caption = "覆盖小区6_BSC名称";
            this.gridColumnBSCCell6.FieldName = "BSCName_Cell6";
            this.gridColumnBSCCell6.Name = "gridColumnBSCCell6";
            this.gridColumnBSCCell6.Visible = true;
            this.gridColumnBSCCell6.VisibleIndex = 45;
            this.gridColumnBSCCell6.Width = 119;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(869, 416);
            this.xtraTabControl1.TabIndex = 5;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.tableLayoutPanel1);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(862, 386);
            this.xtraTabPage1.Text = "名通CQT";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.tableLayoutPanel2);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(862, 386);
            this.xtraTabPage2.Text = "集团CQT";
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 1;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Controls.Add(this.panel2, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.groupControl2, 0, 1);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 1;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 54F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(862, 386);
            this.tableLayoutPanel2.TabIndex = 5;
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.labelFormat_Atu);
            this.panel2.Controls.Add(this.buttonImport_Atu);
            this.panel2.Controls.Add(this.label3);
            this.panel2.Controls.Add(this.btnFileOpen_Atu);
            this.panel2.Controls.Add(this.textBoxFilePath_Atu);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(3, 3);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(856, 48);
            this.panel2.TabIndex = 1;
            // 
            // labelFormat_Atu
            // 
            this.labelFormat_Atu.AutoSize = true;
            this.labelFormat_Atu.Font = new System.Drawing.Font("Tahoma", 20F);
            this.labelFormat_Atu.ForeColor = System.Drawing.Color.Red;
            this.labelFormat_Atu.Location = new System.Drawing.Point(668, 9);
            this.labelFormat_Atu.Name = "labelFormat_Atu";
            this.labelFormat_Atu.Size = new System.Drawing.Size(123, 33);
            this.labelFormat_Atu.TabIndex = 8;
            this.labelFormat_Atu.Text = "格式正确";
            // 
            // buttonImport_Atu
            // 
            this.buttonImport_Atu.Location = new System.Drawing.Point(575, 12);
            this.buttonImport_Atu.Name = "buttonImport_Atu";
            this.buttonImport_Atu.Size = new System.Drawing.Size(87, 27);
            this.buttonImport_Atu.TabIndex = 7;
            this.buttonImport_Atu.Text = "导入";
            this.buttonImport_Atu.UseVisualStyleBackColor = true;
            this.buttonImport_Atu.Click += new System.EventHandler(this.buttonImport_Atu_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(10, 17);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(55, 14);
            this.label3.TabIndex = 5;
            this.label3.Text = "文件路径";
            // 
            // btnFileOpen_Atu
            // 
            this.btnFileOpen_Atu.Location = new System.Drawing.Point(478, 12);
            this.btnFileOpen_Atu.Name = "btnFileOpen_Atu";
            this.btnFileOpen_Atu.Size = new System.Drawing.Size(89, 27);
            this.btnFileOpen_Atu.TabIndex = 1;
            this.btnFileOpen_Atu.Text = "选择文件";
            this.btnFileOpen_Atu.UseVisualStyleBackColor = true;
            this.btnFileOpen_Atu.Click += new System.EventHandler(this.btnFileOpen_Atu_Click);
            // 
            // textBoxFilePath_Atu
            // 
            this.textBoxFilePath_Atu.Enabled = false;
            this.textBoxFilePath_Atu.Location = new System.Drawing.Point(73, 13);
            this.textBoxFilePath_Atu.Name = "textBoxFilePath_Atu";
            this.textBoxFilePath_Atu.Size = new System.Drawing.Size(391, 22);
            this.textBoxFilePath_Atu.TabIndex = 0;
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.gridControl_Atu);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(3, 57);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(856, 326);
            this.groupControl2.TabIndex = 2;
            this.groupControl2.Text = "错误列表";
            // 
            // gridControl_Atu
            // 
            this.gridControl_Atu.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl_Atu.Location = new System.Drawing.Point(2, 23);
            this.gridControl_Atu.MainView = this.gridViewAtu;
            this.gridControl_Atu.Name = "gridControl_Atu";
            this.gridControl_Atu.Size = new System.Drawing.Size(852, 301);
            this.gridControl_Atu.TabIndex = 0;
            this.gridControl_Atu.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewAtu});
            // 
            // gridViewAtu
            // 
            this.gridViewAtu.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22});
            this.gridViewAtu.GridControl = this.gridControl_Atu;
            this.gridViewAtu.Name = "gridViewAtu";
            this.gridViewAtu.OptionsBehavior.Editable = false;
            this.gridViewAtu.OptionsDetail.EnableDetailToolTip = true;
            this.gridViewAtu.OptionsView.ColumnAutoWidth = false;
            this.gridViewAtu.OptionsView.ShowGroupPanel = false;
            this.gridViewAtu.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.gridViewAtu_CustomDrawCell);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "TESTPOINT_ID";
            this.gridColumn1.FieldName = "TESTPOINT_ID";
            this.gridColumn1.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 115;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "TESTPOINT_NAME";
            this.gridColumn2.FieldName = "TESTPOINT_NAME";
            this.gridColumn2.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 132;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "TESTPOINT_DES";
            this.gridColumn3.FieldName = "TESTPOINT_DES";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 122;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "LT_LONGITUDE";
            this.gridColumn4.FieldName = "LT_LONGITUDE";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 126;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "LT_LATITUDE";
            this.gridColumn5.FieldName = "LT_LATITUDE";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            this.gridColumn5.Width = 110;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "GRID_ID";
            this.gridColumn6.FieldName = "GRID_ID";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            this.gridColumn6.Width = 82;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "GROUP_ID";
            this.gridColumn7.FieldName = "GROUP_ID";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            this.gridColumn7.Width = 89;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "RB_LONGITUDE";
            this.gridColumn8.FieldName = "RB_LONGITUDE";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            this.gridColumn8.Width = 115;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "RB_LATITUDE";
            this.gridColumn9.FieldName = "RB_LATITUDE";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            this.gridColumn9.Width = 109;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "CIVILIAN_NAME";
            this.gridColumn10.FieldName = "CIVILIAN_NAME";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            this.gridColumn10.Width = 116;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "OFFICIAL_ADDRESS";
            this.gridColumn11.FieldName = "OFFICIAL_ADDRESS";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 10;
            this.gridColumn11.Width = 142;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "CIVILIAN_ADDRESS";
            this.gridColumn12.FieldName = "CIVILIAN_ADDRESS";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 11;
            this.gridColumn12.Width = 133;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "FUNCTION_CLASS";
            this.gridColumn13.FieldName = "FUNCTION_CLASS";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 12;
            this.gridColumn13.Width = 123;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "DENSITY_CLASS";
            this.gridColumn14.FieldName = "DENSITY_CLASS";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 13;
            this.gridColumn14.Width = 113;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "INFLUENCE_CLASS";
            this.gridColumn15.FieldName = "INFLUENCE_CLASS";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 14;
            this.gridColumn15.Width = 131;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "AREA_CLASS";
            this.gridColumn16.FieldName = "AREA_CLASS";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 15;
            this.gridColumn16.Width = 93;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "SPATIAL_CLASS";
            this.gridColumn17.FieldName = "SPATIAL_CLASS";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 16;
            this.gridColumn17.Width = 119;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "COVER_CLASS";
            this.gridColumn18.FieldName = "COVER_CLASS";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 17;
            this.gridColumn18.Width = 102;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "NETWORK_CLASS";
            this.gridColumn19.FieldName = "NETWORK_CLASS";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 18;
            this.gridColumn19.Width = 120;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "SERVICE_SELL";
            this.gridColumn20.FieldName = "SERVICE_SELL";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 19;
            this.gridColumn20.Width = 101;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "TESTPOINT_IDENTIFIER";
            this.gridColumn21.FieldName = "TESTPOINT_IDENTIFIER";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 20;
            this.gridColumn21.Width = 158;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "BASE_ALTITUDE";
            this.gridColumn22.FieldName = "BASE_ALTITUDE";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 21;
            this.gridColumn22.Width = 109;
            // 
            // CQTPointImportForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(869, 416);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "CQTPointImportForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "CQT地点导入";
            this.tableLayoutPanel1.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl_Atu)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewAtu)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Button buttonImport;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnFileOpen;
        private System.Windows.Forms.TextBox textBoxFilePath;
        private System.Windows.Forms.Label labelFormat;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPointName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAddr;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumntlLong;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumntlLat;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnbrLong;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnbrLat;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAltitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAliasName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPointType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDensityType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSpaceType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCoverType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetWorkType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBelongArea;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBelongArea2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetTypeCell1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLacCell1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCiCell1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNameCell1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBSCCell1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetTypeCell2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLacCell2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCiCell2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNameCell2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBSCCell2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetTypeCell3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLacCell3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCiCell3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNameCell3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBSCCell3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetTypeCell4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLacCell4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCiCell4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNameCell4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBSCCell4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetTypeCell5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLacCell5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCiCell5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNameCell5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBSCCell5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetTypeCell6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLacCell6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCiCell6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNameCell6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBSCCell6;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Label labelFormat_Atu;
        private System.Windows.Forms.Button buttonImport_Atu;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Button btnFileOpen_Atu;
        private System.Windows.Forms.TextBox textBoxFilePath_Atu;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraGrid.GridControl gridControl_Atu;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewAtu;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;

    }
}