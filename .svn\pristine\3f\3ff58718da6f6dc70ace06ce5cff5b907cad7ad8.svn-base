﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteMgrsCoverageRangeSetting
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label2 = new System.Windows.Forms.Label();
            this.numRsrpMin = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numRsrpDiff = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chkFilterF2 = new DevExpress.XtraEditors.CheckEdit();
            this.chkFBandType = new DevExpress.XtraEditors.CheckEdit();
            this.btnFile = new System.Windows.Forms.Button();
            this.checkEditSampleData = new DevExpress.XtraEditors.CheckEdit();
            this.cbxFreqType = new System.Windows.Forms.ComboBox();
            this.checkEditTwoEarfcn = new DevExpress.XtraEditors.CheckEdit();
            this.txtCsvPath = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numOptionalRsrp = new System.Windows.Forms.NumericUpDown();
            this.chkOptionalRsrp = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpDiff)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkFilterF2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkFBandType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditSampleData.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditTwoEarfcn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOptionalRsrp)).BeginInit();
            this.SuspendLayout();
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(403, 34);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(23, 12);
            this.label2.TabIndex = 5;
            this.label2.Text = "dBm";
            // 
            // numRsrpMin
            // 
            this.numRsrpMin.Location = new System.Drawing.Point(277, 30);
            this.numRsrpMin.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numRsrpMin.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numRsrpMin.Name = "numRsrpMin";
            this.numRsrpMin.Size = new System.Drawing.Size(120, 21);
            this.numRsrpMin.TabIndex = 4;
            this.numRsrpMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRsrpMin.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(207, 34);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 3;
            this.label1.Text = "最强信号≥";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(404, 70);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 8;
            this.label4.Text = "dB";
            // 
            // numRsrpDiff
            // 
            this.numRsrpDiff.Location = new System.Drawing.Point(278, 66);
            this.numRsrpDiff.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numRsrpDiff.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numRsrpDiff.Name = "numRsrpDiff";
            this.numRsrpDiff.Size = new System.Drawing.Size(120, 21);
            this.numRsrpDiff.TabIndex = 7;
            this.numRsrpDiff.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRsrpDiff.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(195, 70);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 6;
            this.label3.Text = "相对覆盖带≤";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.groupBox2);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.numOptionalRsrp);
            this.groupBox1.Controls.Add(this.chkOptionalRsrp);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numRsrpMin);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.numRsrpDiff);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(620, 329);
            this.groupBox1.TabIndex = 11;
            this.groupBox1.TabStop = false;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chkFilterF2);
            this.groupBox2.Controls.Add(this.chkFBandType);
            this.groupBox2.Controls.Add(this.btnFile);
            this.groupBox2.Controls.Add(this.checkEditSampleData);
            this.groupBox2.Controls.Add(this.cbxFreqType);
            this.groupBox2.Controls.Add(this.checkEditTwoEarfcn);
            this.groupBox2.Controls.Add(this.txtCsvPath);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Location = new System.Drawing.Point(126, 127);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(384, 135);
            this.groupBox2.TabIndex = 37;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "分析方式";
            // 
            // chkFilterF2
            // 
            this.chkFilterF2.Enabled = false;
            this.chkFilterF2.Location = new System.Drawing.Point(13, 74);
            this.chkFilterF2.Name = "chkFilterF2";
            this.chkFilterF2.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkFilterF2.Properties.Appearance.Options.UseFont = true;
            this.chkFilterF2.Properties.Caption = "剔除F2频点(38544)";
            this.chkFilterF2.Size = new System.Drawing.Size(132, 19);
            this.chkFilterF2.TabIndex = 44;
            // 
            // chkFBandType
            // 
            this.chkFBandType.Enabled = false;
            this.chkFBandType.Location = new System.Drawing.Point(13, 49);
            this.chkFBandType.Name = "chkFBandType";
            this.chkFBandType.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkFBandType.Properties.Appearance.Options.UseFont = true;
            this.chkFBandType.Properties.Caption = "F频F1/F2分频处理";
            this.chkFBandType.Size = new System.Drawing.Size(132, 19);
            this.chkFBandType.TabIndex = 44;
            // 
            // btnFile
            // 
            this.btnFile.Enabled = false;
            this.btnFile.Location = new System.Drawing.Point(340, 97);
            this.btnFile.Name = "btnFile";
            this.btnFile.Size = new System.Drawing.Size(37, 23);
            this.btnFile.TabIndex = 43;
            this.btnFile.Text = "浏览";
            this.btnFile.UseVisualStyleBackColor = true;
            this.btnFile.Click += new System.EventHandler(this.btnFile_Click);
            // 
            // checkEditSampleData
            // 
            this.checkEditSampleData.Location = new System.Drawing.Point(13, 99);
            this.checkEditSampleData.Name = "checkEditSampleData";
            this.checkEditSampleData.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkEditSampleData.Properties.Appearance.Options.UseFont = true;
            this.checkEditSampleData.Properties.Caption = "导出数据";
            this.checkEditSampleData.Size = new System.Drawing.Size(71, 19);
            this.checkEditSampleData.TabIndex = 38;
            this.checkEditSampleData.CheckedChanged += new System.EventHandler(this.checkEditSampleData_CheckedChanged);
            // 
            // cbxFreqType
            // 
            this.cbxFreqType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxFreqType.FormattingEnabled = true;
            this.cbxFreqType.Location = new System.Drawing.Point(228, 20);
            this.cbxFreqType.Name = "cbxFreqType";
            this.cbxFreqType.Size = new System.Drawing.Size(149, 20);
            this.cbxFreqType.TabIndex = 11;
            // 
            // checkEditTwoEarfcn
            // 
            this.checkEditTwoEarfcn.Location = new System.Drawing.Point(13, 21);
            this.checkEditTwoEarfcn.Name = "checkEditTwoEarfcn";
            this.checkEditTwoEarfcn.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkEditTwoEarfcn.Properties.Appearance.Options.UseFont = true;
            this.checkEditTwoEarfcn.Properties.Caption = "多层网剔除异频";
            this.checkEditTwoEarfcn.Size = new System.Drawing.Size(115, 19);
            this.checkEditTwoEarfcn.TabIndex = 3;
            // 
            // txtCsvPath
            // 
            this.txtCsvPath.Enabled = false;
            this.txtCsvPath.Location = new System.Drawing.Point(90, 97);
            this.txtCsvPath.Name = "txtCsvPath";
            this.txtCsvPath.Size = new System.Drawing.Size(245, 21);
            this.txtCsvPath.TabIndex = 42;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(159, 24);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 6;
            this.label5.Text = "频段设置：";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(404, 111);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(23, 12);
            this.label8.TabIndex = 24;
            this.label8.Text = "dBm";
            // 
            // numOptionalRsrp
            // 
            this.numOptionalRsrp.Enabled = false;
            this.numOptionalRsrp.Location = new System.Drawing.Point(277, 102);
            this.numOptionalRsrp.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numOptionalRsrp.Minimum = new decimal(new int[] {
            10000,
            0,
            0,
            -2147483648});
            this.numOptionalRsrp.Name = "numOptionalRsrp";
            this.numOptionalRsrp.Size = new System.Drawing.Size(120, 21);
            this.numOptionalRsrp.TabIndex = 23;
            this.numOptionalRsrp.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOptionalRsrp.Value = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            // 
            // chkOptionalRsrp
            // 
            this.chkOptionalRsrp.AutoSize = true;
            this.chkOptionalRsrp.Location = new System.Drawing.Point(192, 104);
            this.chkOptionalRsrp.Name = "chkOptionalRsrp";
            this.chkOptionalRsrp.Size = new System.Drawing.Size(84, 16);
            this.chkOptionalRsrp.TabIndex = 22;
            this.chkOptionalRsrp.Text = "信号强度≥";
            this.chkOptionalRsrp.UseVisualStyleBackColor = true;
            // 
            // LteMgrsCoverageRangeSetting
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.groupBox1);
            this.Name = "LteMgrsCoverageRangeSetting";
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpDiff)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkFilterF2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkFBandType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditSampleData.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditTwoEarfcn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOptionalRsrp)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numRsrpMin;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numRsrpDiff;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numOptionalRsrp;
        private System.Windows.Forms.CheckBox chkOptionalRsrp;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.ComboBox cbxFreqType;
        private DevExpress.XtraEditors.CheckEdit checkEditTwoEarfcn;
        private System.Windows.Forms.Label label5;
        private DevExpress.XtraEditors.CheckEdit checkEditSampleData;
        private System.Windows.Forms.Button btnFile;
        private System.Windows.Forms.TextBox txtCsvPath;
        private DevExpress.XtraEditors.CheckEdit chkFBandType;
        private DevExpress.XtraEditors.CheckEdit chkFilterF2;

    }
}
