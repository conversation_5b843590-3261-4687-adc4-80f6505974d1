﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class ReasonPnlChangeFreq : ReasonPanelBase
    {
        public ReasonPnlChangeFreq()
        {
            InitializeComponent();
        }

        public override void AttachReason(ReasonBase reason)
        {
            base.AttachReason(reason);
            timePersist.ValueChanged -= timePersist_ValueChanged;
            timePersist.Value = ZTWeakSINRReason.timeLimit;
            timePersist.ValueChanged += timePersist_ValueChanged;
            numDistanceLimit.ValueChanged -= numDistanceLimit_ValueChanged;
            numDistanceLimit.Value = ZTWeakSINRReason.distanceLimit;
            numDistanceLimit.ValueChanged += numDistanceLimit_ValueChanged;
            numHandoverCount.ValueChanged -= numHandoverCount_ValueChanged;
            numHandoverCount.Value = ZTWeakSINRReason.handoverCount;
            numHandoverCount.ValueChanged += numHandoverCount_ValueChanged;
        }

        void timePersist_ValueChanged(object sender, EventArgs e)
        {
            ZTWeakSINRReason.timeLimit = (int)timePersist.Value;
        }
        void numDistanceLimit_ValueChanged(object sender, EventArgs e)
        {
            ZTWeakSINRReason.distanceLimit = (int)numDistanceLimit.Value;
        }
        void numHandoverCount_ValueChanged(object sender, EventArgs e)
        {
            ZTWeakSINRReason.handoverCount = (int)numHandoverCount.Value;
        }
    }
}
