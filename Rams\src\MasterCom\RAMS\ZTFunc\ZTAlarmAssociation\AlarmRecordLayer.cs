﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class AlarmRecordLayer : CustomDrawLayer
    {
        public Image CellAlarmImg { get; set; }
        public Image SiteAlarmImg { get; set; }
        public int NbCellCount { get; set; } = 6;
        public Size ImgSize { get; set; } = new Size(10, 10);

        public List<AlarmDrawItem> CustomAlarmItems
        {
            get;
            private set;
        }

        public AlarmRecordLayer(MapOperation mop, string name)
            : base(mop, name)
        {
            this.CustomAlarmItems = new List<AlarmDrawItem>();
            this.VisibleChanged += this.DTDataChanged;
            MainModel.DTDataChanged += this.DTDataChanged;
            MainModel.MainForm.GetMapForm().ToolInfoClickEvent += ToolInfoClick;
            CellAlarmImg = Image.FromFile("images/关联分析/小区告警.png");
            SiteAlarmImg = Image.FromFile("images/关联分析/基站告警.png");
        }

        public override void LayerDispose()
        {
 	        MainModel.DTDataChanged -= this.DTDataChanged;
            this.VisibleChanged -= this.DTDataChanged;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }

            updateRect.Inflate((int)(4000000 / Map.Scale), (int)(4000000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(Map.Scale);
            float radius = ratio * 40;

            foreach (AlarmDrawItem cellItem in cellDrawItemDic.Values)
            {
                DrawItem(cellItem, dRect, radius, graphics);
            }
            foreach (AlarmDrawItem siteItem in siteDrawItemDic.Values)
            {
                DrawItem(siteItem, dRect, radius, graphics);
            }
            foreach (AlarmDrawItem customItem in CustomAlarmItems)
            {
                DrawItem(customItem, dRect, radius, graphics);
            }
        }

        public void Clear()
        {
            cellDrawItemDic.Clear();
            siteDrawItemDic.Clear();
            CustomAlarmItems.Clear();
        }

        public void ReloadAlarmRecords()
        {
            AlarmRecordManager.Instance.FireLoadAlarmRecords(MainModel.DistrictID, true);
            this.DTDataChanged(this, EventArgs.Empty);
        }

        private void DrawItem(AlarmDrawItem item, DbRect dRect, float radius, System.Drawing.Graphics graphics)
        {
            if (!dRect.IsPointInThisRect(item.DbPoint.x, item.DbPoint.y))
            {
                return;
            }

            PointF pt;
            this.Map.ToDisplay(item.DbPoint, out pt);
            RectangleF rect = new RectangleF(pt.X - radius * this.ImgSize.Width / 2
                , pt.Y - radius * this.ImgSize.Height / 2
                , radius * this.ImgSize.Width, radius * this.ImgSize.Height);
            if (item.Cell != null)
            {
                graphics.DrawImage(CellAlarmImg, rect);
            }
            else
            {
                graphics.DrawImage(SiteAlarmImg, rect);
            }
        }

        private void CacheAlarmRecords(ICell cell, TestPoint tp)
        {
            if (cell == null)
            {
                return;
            }

            List<AlarmRecord> cellRecords = AlarmRecordManager.Instance.GetAlarmRecords(cell.Name, tp.DateTime);
            if (cellRecords.Count > 0)
            {
                if (!cellDrawItemDic.ContainsKey(cell))
                {
                    cellDrawItemDic.Add(cell, new AlarmDrawItem(cell));
                }
                cellDrawItemDic[cell].AddRange(cellRecords);
            }

            ISite site = cell.Site;
            if (site == null)
            {
                return;
            }

            List<AlarmRecord> siteRecords = AlarmRecordManager.Instance.GetAlarmRecords(site.Name, tp.DateTime);
            if (siteRecords.Count > 0)
            {
                if (!siteDrawItemDic.ContainsKey(site))
                {
                    siteDrawItemDic.Add(site, new AlarmDrawItem(site));
                }
                siteDrawItemDic[site].AddRange(siteRecords);
            }
        }

        private void DTDataChanged(object sender, EventArgs e)
        {
            if (!IsVisible)
            {
                return;
            }

            cellDrawItemDic.Clear();
            siteDrawItemDic.Clear();
            AlarmRecordManager.Instance.FireLoadAlarmRecords(MainModel.DistrictID);
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPoints = file.TestPoints;
                foreach (TestPoint tp in testPoints)
                {
                    if (!(tp is LTETestPointDetail) && !(tp is LTEUepTestPoint))
                    {
                        continue;
                    }

                    ICell mainCell = tp.GetMainCell();
                    CacheAlarmRecords(mainCell, tp);

                    for (int i = 0; i < this.NbCellCount; ++i)
                    {
                        ICell nbCell = tp.GetNBCell_LTE(i);
                        if (nbCell == null)
                        {
                            continue;
                        }
                        CacheAlarmRecords(nbCell, tp);
                    }
                }
            }
        }

        private void ToolInfoClick(double lng, double lat, MapOperation2 mop2, ref List<string> titles, ref List<string> infos)
        {
            DbRect bound = mop2.GetRegion().Bounds;
            
            setTitleInfo(titles, infos, bound, "小区告警", new List<AlarmDrawItem>(cellDrawItemDic.Values));
            setTitleInfo(titles, infos, bound, "基站告警", new List<AlarmDrawItem>(siteDrawItemDic.Values));
            setTitleInfo(titles, infos, bound, "", CustomAlarmItems);
        }

        private void setTitleInfo(List<string> titles, List<string> infos, DbRect bound, string title, List<AlarmDrawItem> itemList)
        {
            foreach (AlarmDrawItem item in itemList)
            {
                if (!bound.IsPointInThisRect(item.DbPoint.x, item.DbPoint.y))
                {
                    continue;
                }

                List<AlarmRecord> records = item.AlarmRecords;
                foreach (AlarmRecord rec in records)
                {
                    if (string.IsNullOrEmpty(title))
                    {
                        titles.Add(rec.IsCellAlarm ? "小区告警" : "基站告警");
                    }
                    else
                    {
                        titles.Add(title);
                    }
                    infos.Add(rec.GetDesc());
                }
            }
        }

        private readonly Dictionary<ICell, AlarmDrawItem> cellDrawItemDic = new Dictionary<ICell,AlarmDrawItem>();
        private readonly Dictionary<ISite, AlarmDrawItem> siteDrawItemDic = new Dictionary<ISite,AlarmDrawItem>();
    }

    public class AlarmDrawItem
    {
        public ICell Cell
        {
            get;
            private set;
        }

        public ISite Site
        {
            get;
            private set;
        }

        public DbPoint DbPoint
        {
            get;
            private set;
        }

        public List<AlarmRecord> AlarmRecords
        {
            get;
            private set;
        }

        private AlarmDrawItem()
        {
            AlarmRecords = new List<AlarmRecord>();
        }

        public AlarmDrawItem(ICell cell) : this()
        {
            Cell = cell;
            this.DbPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
        }

        public AlarmDrawItem(ISite site) : this()
        {
            Site = site;
            this.DbPoint = new DbPoint(site.Longitude, site.Latitude);
        }

        public void Add(AlarmRecord record)
        {
            if (!recordCntDic.ContainsKey(record))
            {
                recordCntDic.Add(record, 0);
                AlarmRecords.Add(record);
            }
            ++recordCntDic[record];
        }

        public void AddRange(IEnumerable<AlarmRecord> records)
        {
            foreach (AlarmRecord rec in records)
            {
                this.Add(rec);
            }
        }

        private readonly Dictionary<AlarmRecord, int> recordCntDic = new Dictionary<AlarmRecord,int>();
    }

    public class AlarmRecordComparer : IComparer<AlarmRecord>
    {
        public int Compare(AlarmRecord x, AlarmRecord y)
        {
            if (x == y)
            {
                return 0;
            }
            return x.AlarmID > y.AlarmID ? 1 : -1;
        }
    }
}

