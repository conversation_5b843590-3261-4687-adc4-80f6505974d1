﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellCoverageRangeAnaBase : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        public Dictionary<ICell, ZTCellCoverageRangeAnaItem> resultDic { get; set; } = new Dictionary<ICell, ZTCellCoverageRangeAnaItem>();
        public ZTCellCoverageRangeAnaCondition cellCoverCondition { get; set; } = new ZTCellCoverageRangeAnaCondition();   //查询条件
        public ICell curCell { get; set; }

        public ZTCellCoverageRangeAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        public ZTCellCoverageRangeAnaBase(bool isVoLTE)
            : this(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            if (isVoLTE)
            {
                ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            }
            else
            {
                ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
                ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            }
        }

        protected static readonly object lockObj = new object();
        private static ZTCellCoverageRangeAnaBase intance = null;
        public static ZTCellCoverageRangeAnaBase GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTCellCoverageRangeAnaBase(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }


        public override string Name
        {
            get { return "小区覆盖带分析(按小区)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12064, this.Name);
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            resultDic = new Dictionary<ICell, ZTCellCoverageRangeAnaItem>();
            curCell = null;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedCell == null
                && searchGeometrys.SelectedTDCell == null
                && searchGeometrys.SelectedWCell == null
                && searchGeometrys.SelectedLTECell == null
                && searchGeometrys.SelectedNRCell == null)
            {
                return false;
            }
            return true;
        }

        protected override void AddDIYRegion_Intersect(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);

            addDiyRegion(package);
        }

        protected override void AddDIYRegion_Sample(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectSample);

            addDiyRegion(package);
        }

        private void addDiyRegion(Package package)
        {
            double tlLong = 0;
            double tlLat = 0;
            double brLong = 0;
            double brLat = 0;
            for (int i = 0; i < cellCoverCondition.CellList.Count; i++)
            {
                if (i == 0)
                {
                    tlLong = cellCoverCondition.CellList[0].Longitude;
                    tlLat = cellCoverCondition.CellList[0].Latitude;
                    brLong = cellCoverCondition.CellList[0].Longitude;
                    brLat = cellCoverCondition.CellList[0].Latitude;
                }
                else
                {
                    getLngAndLat(ref tlLong, ref tlLat, ref brLong, ref brLat, i);
                }
            }

            package.Content.AddParam(tlLong - 0.05);
            package.Content.AddParam(tlLat + 0.05);
            package.Content.AddParam(brLong + 0.05);
            package.Content.AddParam(brLat - 0.05);
        }

        private void getLngAndLat(ref double tlLong, ref double tlLat, ref double brLong, ref double brLat, int i)
        {
            if (cellCoverCondition.CellList[i].Longitude < tlLong)
            {
                tlLong = cellCoverCondition.CellList[i].Longitude;
            }
            if (cellCoverCondition.CellList[i].Longitude > brLong)
            {
                brLong = cellCoverCondition.CellList[i].Longitude;
            }
            if (cellCoverCondition.CellList[i].Latitude < brLat)
            {
                brLat = cellCoverCondition.CellList[i].Latitude;
            }
            if (cellCoverCondition.CellList[i].Latitude > tlLat)
            {
                tlLat = cellCoverCondition.CellList[i].Latitude;
            }
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param;
            param = new Dictionary<string, object>();
            param["param_name"] = "isampleid";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "N_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "N_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "N_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_NCell_UARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_NCell_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_NCell_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_NCell_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_NCell_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_NCell_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_TotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SNeiFreq";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SNeiPSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SNeiRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            NRTpHelper.InitNrParamSample(columnsDef);
            NRTpHelper.InitNrNCellParamSample(columnsDef);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"sample");
            tmpDic.Add("themeName", (object)"小区采样点");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        ZTCellCoverageRangeAnaSetForm setForm = null;
        protected override bool getConditionBeforeQuery()
        {
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
            {
                return false;
            }

            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTCellCoverageRangeAnaSetForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                cellCoverCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            for (int j = 0; j < cellCoverCondition.CellList.Count; j++)
            {
                curCell = cellCoverCondition.CellList[j];

                if (tp is TestPointDetail && curCell is Cell)
                {
                    doGSMCell(tp);
                }
                else if (tp is TDTestPointDetail && curCell is TDCell)
                {
                    doTDCell(tp);
                }
                else if (tp is LTETestPointDetail && curCell is LTECell)
                {
                    doTDDLTECell(tp);
                }
                else if (tp is LTEFddTestPoint && curCell is LTECell)
                {
                    doFDDLTECell(tp);
                }
                else if (tp is WCDMATestPointDetail && curCell is WCell)
                {
                    doWCDMACell(tp);
                }
                else if (tp is TestPoint_NR && curCell is NRCell)
                {
                    doNRCell(tp);
                }
            }
        }

        private void doGSMCell(TestPoint tp)
        {
            Cell selectedCell = curCell as Cell;

            if (selectedCell.GetDistance(tp.Longitude, tp.Latitude) > 3000) //GSM，查看小区3公里范围内采样点
            {
                return;
            }

            if (tp["RxLevSub"] == null)
            {
                return;
            }

            float rxlev = (float)(short)tp["RxLevSub"];

            Cell tpCell = tp.GetMainCell_GSM();
            bool isAdded = addServResultDic(tp, selectedCell, rxlev, tpCell, 1);
            if (isAdded)
            {
                return;
            }

            if (cellCoverCondition.CellFilterType == 1)  //需要查看邻区
            {
                for (int i = 0; i < 6; i++)
                {
                    Cell nCell = tp.GetNBCell_GSM(i);
                    if (nCell == null || nCell.ID != selectedCell.ID || tp["N_RxLev", i] == null)
                    {
                        continue;
                    }

                    float nRxlev = (float)(short)tp["N_RxLev", i];
                    if ((rxlev - nRxlev) > cellCoverCondition.NbDiffServ) //与主服的信号强度超过条件值（如6dB），其余邻区不再处理
                    {
                        break;
                    }

                    addNbResultDic(tp, selectedCell, nRxlev, 1);

                    break;  //只可能有一个邻区符合，其余邻区不再处理
                }
            }
        }

        private bool addServResultDic(TestPoint tp, ICell selectedCell, float rxlev, ICell tpCell, int cellType)
        {
            if (tpCell != null && tpCell.ID == selectedCell.ID)
            {
                ZTCellCoverageRangeAnaItem cellItem = null;
                if (!resultDic.TryGetValue(selectedCell, out cellItem))
                {
                    cellItem = new ZTCellCoverageRangeAnaItem(cellType, selectedCell);
                    resultDic.Add(selectedCell, cellItem);
                }
                cellItem.addServTp(tp, rxlev);

                //如果有主服，不需要判断邻区
                return true;
            }
            return false;
        }

        private void addNbResultDic(TestPoint tp, ICell selectedCell, float nRxlev, int cellType)
        {
            ZTCellCoverageRangeAnaItem cellItem = null;
            if (!resultDic.TryGetValue(selectedCell, out cellItem))
            {
                cellItem = new ZTCellCoverageRangeAnaItem(cellType, selectedCell);
                resultDic.Add(selectedCell, cellItem);
            }
            cellItem.addNbTp(tp, nRxlev);
        }

        private void doTDCell(TestPoint tp)
        {
            TDCell selectedCell = curCell as TDCell;

            if (selectedCell.GetDistance(tp.Longitude, tp.Latitude) > 5000) //TD，查看小区5公里范围内采样点
            {
                return;
            }

            if (tp["TD_PCCPCH_RSCP"] == null)
            {
                return;
            }

            float pccpchRscp = (float)tp["TD_PCCPCH_RSCP"];

            TDCell tpCell = tp.GetMainCell_TD_TDCell();
            bool isAdded = addServResultDic(tp, selectedCell, pccpchRscp, tpCell, 2);
            if (isAdded)
            {
                return;
            }

            if (cellCoverCondition.CellFilterType == 1)  //需要查看邻区
            {
                for (int i = 0; i < 6; i++)
                {
                    TDCell nCell = tp.GetNBCell_TD_TDCell(i);
                    if (nCell == null || nCell.ID != selectedCell.ID || tp["TD_NCell_PCCPCH_RSCP", i] == null)
                    {
                        continue;
                    }

                    float nPccpchRscp = (float)tp["TD_NCell_PCCPCH_RSCP", i];
                    if ((pccpchRscp - nPccpchRscp) > cellCoverCondition.NbDiffServ) //与主服的信号强度超过条件值（如6dB），其余邻区不再处理
                    {
                        break;
                    }

                    addNbResultDic(tp, selectedCell, nPccpchRscp, 2);
                    break;  //只可能有一个邻区符合，其余邻区不再处理
                }
            }
        }

        private void doTDDLTECell(TestPoint tp)
        {
            LTECell selectedCell = curCell as LTECell;

            if (selectedCell.GetDistance(tp.Longitude, tp.Latitude) > 5000) //TDDLTE，查看小区5公里范围内采样点
            {
                return;
            }

            if (tp["lte_RSRP"] == null)
            {
                return;
            }

            float rsrp = (float)tp["lte_RSRP"];

            LTECell tpCell = tp.GetMainCell_LTE();
            bool isAdded = addServResultDic(tp, selectedCell, rsrp, tpCell, 3);
            if (isAdded)
            {
                return;
            }

            if (cellCoverCondition.CellFilterType == 1)  //需要查看邻区
            {
                for (int i = 0; i < 6; i++)
                {
                    LTECell nCell = tp.GetNBCell_LTE(i);
                    if (nCell == null || nCell.ID != selectedCell.ID || tp["lte_NCell_RSRP", i] == null)
                    {
                        continue;
                    }

                    float nRsrp = (float)tp["lte_NCell_RSRP", i];
                    if ((rsrp - nRsrp) > cellCoverCondition.NbDiffServ) //与主服的信号强度超过条件值（如6dB），其余邻区不再处理
                    {
                        break;
                    }

                    addNbResultDic(tp, selectedCell, nRsrp, 3);
                    break;  //只可能有一个邻区符合，其余邻区不再处理
                }
            }
        }

        private void doFDDLTECell(TestPoint tp)
        {
            LTECell selectedCell = curCell as LTECell;

            if (selectedCell.GetDistance(tp.Longitude, tp.Latitude) > 5000) //TDDLTE，查看小区5公里范围内采样点
            {
                return;
            }

            if (tp["lte_fdd_RSRP"] == null)
            {
                return;
            }

            float rsrp = (float)tp["lte_fdd_RSRP"];

            LTECell tpCell = tp.GetMainCell_LTE_FDD();
            bool isAdded = addServResultDic(tp, selectedCell, rsrp, tpCell, 3);
            if (isAdded)
            {
                return;
            }

            if (cellCoverCondition.CellFilterType == 1)  //需要查看邻区
            {
                for (int i = 0; i < 6; i++)
                {
                    LTECell nCell = tp.GetNBCell_LTE_FDD(i);
                    if (nCell == null || nCell.ID != selectedCell.ID || tp["lte_fdd_NCell_RSRP", i] == null)
                    {
                        continue;
                    }

                    float nRsrp = (float)tp["lte_fdd_NCell_RSRP", i];
                    if ((rsrp - nRsrp) > cellCoverCondition.NbDiffServ) //与主服的信号强度超过条件值（如6dB），其余邻区不再处理
                    {
                        break;
                    }

                    addNbResultDic(tp, selectedCell, nRsrp, 5);
                    break;  //只可能有一个邻区符合，其余邻区不再处理
                }
            }
        }

        private void doWCDMACell(TestPoint tp)
        {
            WCell selectedCell = curCell as WCell;

            if (selectedCell.GetDistance(tp.Longitude, tp.Latitude) > 5000) //WCDMA，查看小区5公里范围内采样点
            {
                return;
            }

            if (tp["W_TotalRSCP"] == null)
            {
                return;
            }

            float totalRscp = (float)tp["W_TotalRSCP"];

            WCell tpCell = tp.GetMainCell_W();
            bool isAdded = addServResultDic(tp, selectedCell, totalRscp, tpCell, 4);
            if (isAdded)
            {
                return;
            }

            if (cellCoverCondition.CellFilterType == 1)  //需要查看邻区
            {
                for (int i = 0; i < 6; i++)
                {
                    WCell nCell = tp.GetNBCell_W_WCell(i);
                    if (nCell == null || nCell.ID != selectedCell.ID || tp["W_SNeiRSCP", i] == null)
                    {
                        continue;
                    }

                    float nTotalRscp = (float)tp["W_SNeiRSCP", i];
                    if ((totalRscp - nTotalRscp) > cellCoverCondition.NbDiffServ) //与主服的信号强度超过条件值（如6dB），其余邻区不再处理
                    {
                        break;
                    }

                    addNbResultDic(tp, selectedCell, nTotalRscp, 4);
                    break;  //只可能有一个邻区符合，其余邻区不再处理
                }
            }
        }

        private void doNRCell(TestPoint tp)
        {
            NRCell selectedCell = curCell as NRCell;

            if (selectedCell.GetDistance(tp.Longitude, tp.Latitude) > 5000)
            {
                return;
            }

            float? data = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            if (data == null)
            {
                return;
            }
            float rsrp = (float)data;

            NRCell tpCell = tp.GetMainCell_NR();
            bool isAdded = addServResultDic(tp, selectedCell, rsrp, tpCell, 6);
            if (isAdded)
            {
                return;
            }

            if (cellCoverCondition.CellFilterType == 1)
            {
                dealNRNCell(tp, selectedCell, rsrp);
            }
        }

        private void dealNRNCell(TestPoint tp, NRCell selectedCell, float rsrp)
        {
            for (int i = 0; i < 16; i++)
            {
                NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(tp, i);
                if (type == NRTpHelper.NRNCellType.NCELL)
                {
                    NRCell nCell = tp.GetNBCell_NR(i);
                    float? data = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                    if (nCell == null || nCell.ID != selectedCell.ID || data == null)
                    {
                        continue;
                    }

                    float nRsrp = (float)data;
                    if ((rsrp - nRsrp) > cellCoverCondition.NbDiffServ)
                    {
                        break;
                    }

                    addNbResultDic(tp, selectedCell, nRsrp, 6);
                    break;
                }
                else if (type == NRTpHelper.NRNCellType.UNKNOWN)
                {
                    break;
                }
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            if (resultDic.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            List<ZTCellCoverageRangeAnaItem> resultList = new List<ZTCellCoverageRangeAnaItem>();
            foreach (ICell cell in resultDic.Keys)
            {
                resultDic[cell].SN = resultList.Count + 1;
                resultList.Add(resultDic[cell]);
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTCellCoverageRangeAnaListForm).FullName);
            ZTCellCoverageRangeAnaListForm cellCoverAnaListForm = obj == null ? null : obj as ZTCellCoverageRangeAnaListForm;
            if (cellCoverAnaListForm == null || cellCoverAnaListForm.IsDisposed)
            {
                cellCoverAnaListForm = new ZTCellCoverageRangeAnaListForm(MainModel);
            }

            cellCoverAnaListForm.FillData(resultList);
            if (!cellCoverAnaListForm.Visible)
            {
                cellCoverAnaListForm.Show(MainModel.MainForm);
            }
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            return true;
        }
    }

    public class ZTCellCoverageRangeAnaBase_FDD : ZTCellCoverageRangeAnaBase
    {
        private static ZTCellCoverageRangeAnaBase_FDD instance = null;
        public static new ZTCellCoverageRangeAnaBase_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellCoverageRangeAnaBase_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public ZTCellCoverageRangeAnaBase_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }

        public override string Name
        {
            get { return "小区覆盖带分析LTE_FDD(按小区)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26054, this.Name);
        }
    }

    public class ZTCellCoverageRangeAnaBase_FDD_VOLTE : ZTCellCoverageRangeAnaBase_FDD
    {
        private static ZTCellCoverageRangeAnaBase_FDD_VOLTE instance = null;
        public static new ZTCellCoverageRangeAnaBase_FDD_VOLTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellCoverageRangeAnaBase_FDD_VOLTE(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public ZTCellCoverageRangeAnaBase_FDD_VOLTE(MainModel mainModel)
            : base(mainModel)
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "小区覆盖带分析VoLTE_FDD(按小区)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30002, this.Name);
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)tp.ServiceType)) return false;
            return true;
        }
    }

    public class ZTCellCoverageRangeAnaItem
    {
        /// <summary>
        /// 小区类型，GSM：1，TD：2, LTE: 3, WCDMA: 4
        /// </summary>
        public int SN { get; set; }
        public int CellType { get; set;}
        public ICell CurCell { get; set;}
        public ZTCellCoverageRangeAnaTpItem ServTpItem { get; set;}   //主服
        public ZTCellCoverageRangeAnaTpItem NbTpItem { get; set; }     //邻区
        
        public ZTCellCoverageRangeAnaItem(int cellType, ICell cell)
        {
            this.CellType = cellType;
            this.CurCell = cell;
            ServTpItem = new ZTCellCoverageRangeAnaTpItem();
            NbTpItem = new ZTCellCoverageRangeAnaTpItem();
        }

        public void addServTp(TestPoint tp, float rxlev)
        {
            addTestPoint2TpItem(ServTpItem, tp, rxlev);
        }

        public void addNbTp(TestPoint tp, float rxlev)
        {
            addTestPoint2TpItem(NbTpItem, tp, rxlev);
        }

        public void addTestPoint2TpItem(ZTCellCoverageRangeAnaTpItem tpItem, TestPoint tp, float rxlev)
        {
            if (tpItem.TpRxlevCount == 0)
            {
                tpItem.TpRxlevMax = rxlev;
                tpItem.TpRxlevMin = rxlev;
            }
            else
            {
                if (tpItem.TpRxlevMax < rxlev)
                {
                    tpItem.TpRxlevMax = rxlev;
                }
                if (tpItem.TpRxlevMin > rxlev)
                {
                    tpItem.TpRxlevMin = rxlev;
                }
            }

            tpItem.TpRxlevCount++;
            tpItem.TpRxlevSum += rxlev;
            tpItem.TpList.Add(tp);
        }

        #region 预处理
        public string CellTypeStr
        {
            get
            {
                if (CellType == 1)
                {
                    return "GSM";
                }
                if (CellType == 2)
                {
                    return "TD-SCDMA";
                }
                if (CellType == 3)
                {
                    return "TDD-LTE";
                }
                if (CellType == 4)
                {
                    return "WCDMA";
                }
                if (CellType == 5)
                {
                    return "FDD_LTE";
                }
                if (CellType == 6)
                {
                    return "NR";
                }
                else
                {
                    return "";
                }
            }
        }

        public string CellName
        {
            get
            {
                return CurCell.Name;
            }
        }

        public string LAC
        {
            get
            {
                if (CellType == 1)
                {
                    return ((Cell)CurCell).LAC.ToString();
                }
                if (CellType == 2)
                {
                    return ((TDCell)CurCell).LAC.ToString();
                }
                if (CellType == 3 || CellType == 5)
                {
                    return ((LTECell)CurCell).TAC.ToString();
                }
                if (CellType == 4)
                {
                    return ((WCell)CurCell).LAC.ToString();
                }
                if (CellType == 6)
                {
                    return ((NRCell)CurCell).TAC.ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string CI
        {
            get
            {
                if (CellType == 1)
                {
                    return ((Cell)CurCell).CI.ToString();
                }
                if (CellType == 2)
                {
                    return ((TDCell)CurCell).CI.ToString();
                }
                if (CellType == 3 || CellType == 5)
                {
                    return ((LTECell)CurCell).ECI.ToString();
                }
                if (CellType == 4)
                {
                    return ((WCell)CurCell).CI.ToString();
                }
                if (CellType == 6)
                {
                    return ((NRCell)CurCell).NCI.ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        #endregion
    }

    public class ZTCellCoverageRangeAnaTpItem
    {
        public List<TestPoint> TpList { get; set; }
        public float TpRxlevCount { get; set; }
        public float TpRxlevMax { get; set; }
        public float TpRxlevMin { get; set; }
        public float TpRxlevSum { get; set; }

        public ZTCellCoverageRangeAnaTpItem()
        {
            TpList = new List<TestPoint>();
            TpRxlevCount = 0;
            TpRxlevMax = 0;
            TpRxlevMin = 0;
            TpRxlevSum = 0;
        }

        #region 预处理
        public string RxlevAvg
        {
            get
            {
                if (TpRxlevCount > 0)
                {
                    return Math.Round(TpRxlevSum / TpRxlevCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string RxlevMax
        {
            get
            {
                if (TpRxlevCount > 0)    
                {
                    return Math.Round(TpRxlevMax,2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string RxlevMin
        {
            get
            {
                if (TpRxlevCount > 0)
                {
                    return Math.Round(TpRxlevMin, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        #endregion
    }

    public class ZTCellCoverageRangeAnaCondition
    {
        public int CellFilterType { get; set; }           //小区筛选设置
        public int NbDiffServ { get; set; }               //邻区和主服信号强度差
        public int SampleColorType { get; set; }          //采样点着色设置
        public List<ICell> CellList { get; set; }         //选择的小区

        public ZTCellCoverageRangeAnaCondition()
        {
            CellFilterType = 1;
            NbDiffServ = 6;
            SampleColorType = 1;
            CellList = new List<ICell>();
        }
    }
}
