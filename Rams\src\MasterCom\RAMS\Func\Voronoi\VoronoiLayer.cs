﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Windows.Forms;
using MapWinGIS;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 单实例的Voronoi图层控制，由于速度问题没有继承CustomLayer
    /// </summary>
    public class VoronoiLayer
    {
        public static VoronoiLayer GetInstance()
        {
            if (instance == null)
            {
                instance = new VoronoiLayer();
            }
            return instance;
        }
        
        public Color LineColor { get; set; } = Color.LightGreen;

        public short LineWidth { get; set; } = 3;

        /// <summary>
        /// 绘制泰森多边形
        /// </summary>
        /// <param name="voiCoverList">内层Vertex数组为一个多边形；一个泰森多边形由1个或多个多边形组成</param>
        public void Draw(List<List<Vertex[]>> voiCoverList)
        {
            if (isDrawn)
            {
                Clear();
            }
            
            foreach (List<Vertex[]> poly in voiCoverList)
            {
                uint color = (uint)ColorTranslator.ToOle(LineColor);
                foreach (Vertex[] vs in poly)
                {
                    int numPoints = vs.Length + 1;
                    double[] xList = new double[vs.Length + 1];
                    double[] yList = new double[vs.Length + 1];
                    for (int i = 0; i <= vs.Length; ++i)
                    {
                        xList[i] = vs[i % vs.Length].X;
                        yList[i] = vs[i % vs.Length].Y;
                    }
                    object xPoints = xList;
                    object yPoints = yList;
                    axMap.DrawWidePolygonEx(drawLayer, ref xPoints, ref yPoints, numPoints, color, false, LineWidth);

                }
            }
            isDrawn = true;
            SaveForExport(voiCoverList);
        }

        public void Draw(List<List<Vertex[]>> voiCoverList, List<string> labels)
        {
            Draw(voiCoverList);

            for (int i = 0; i < voiCoverList.Count && i < labels.Count; ++i)
            {
                List<Vertex[]> polys = voiCoverList[i];
                string text = labels[i];

                Vertex[] tmpVs = polys[0];
                for (int j = 1; j < polys.Count; ++j)
                {
                    if (polys[j].Length > tmpVs.Length)
                    {
                        tmpVs = polys[j];
                    }
                }

                Vertex loc = GetLabelLocation(tmpVs);
                uint color = (uint)ColorTranslator.ToOle(Color.Black);
                axMap.AddDrawingLabel(drawLayer, text, color, loc.X, loc.Y, tkHJustification.hjCenter);
            }

            SaveForExport(labels);
        }

        public void Refresh()
        {
            if (savedVois == null)
            {
                //
            }
            else if (savedLabels == null)
            {
                Draw(savedVois);
            }
            else
            {
                Draw(savedVois, savedLabels);
            }
        }

        public void Clear()
        {
            if (!isDrawn)
            {
                return;
            }
            axMap.ClearDrawing(drawLayer);
            drawLayer = axMap.NewDrawing(tkDrawReferenceList.dlSpatiallyReferencedList);
            isDrawn = false;
            savedVois = null;
            savedVois = null;
        }

        public void ExportToShp()
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = FilterHelper.Shp;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            WaitBox.Show("正在导出泰森多边形图层...", ExportToShpInWaitBox, dlg.FileName);
        }

        private void SaveForExport(List<List<Vertex[]>> voiCoverList)
        {
            if (savedVois == null)
            {
                savedVois = new List<List<Vertex[]>>();
            }
            savedVois.Clear();

            foreach (List<Vertex[]> vsList in voiCoverList)
            {
                savedVois.Add(vsList);
            }
        }

        private void SaveForExport(List<string> labels)
        {
            if (savedLabels == null)
            {
                savedLabels = new List<string>();
            }
            savedLabels.Clear();

            foreach (string str in labels)
            {
                savedLabels.Add(str);
            }
        }

        private Vertex GetLabelLocation(Vertex[] vs)
        {
            double x1 = double.MaxValue;
            double y1 = double.MaxValue;
            double x2 = double.MinValue;
            double y2 = double.MinValue;

            foreach (Vertex v in vs)
            {
                x1 = Math.Min(x1, v.X);
                y1 = Math.Min(y1, v.Y);
                x2 = Math.Max(x2, v.X);
                y2 = Math.Max(y2, v.Y);
            }

            return new Vertex((x1 + x2) / 2, (y1 + y2) / 2);
        }

        private void ExportToShpInWaitBox(object obj)
        {
            bool hasError = false;
            string fileName = obj as string;
            Shapefile shp = new Shapefile();
            hasError = !shp.CreateNewWithShapeID(fileName, ShpfileType.SHP_POLYGON);
            if (hasError)
            {
                StopExportToShp("创建Shapefile失败", "错误");
                return;
            }

            if (savedVois == null || savedVois.Count == 0)
            {
                StopExportToShp("泰森多边形图层没有数据可以导出", "提示");
                return;
            }

            //shp.StartEditingShapes(true, null);
            int fieldIndex = -1;
            if (savedLabels != null && savedLabels.Count == savedVois.Count)
            {
                MapWinGIS.Field field = new Field();
                field.Name = "BSC Name";
                field.Type = FieldType.STRING_FIELD;
                field.Precision = 0;
                field.Width = 0;
                fieldIndex = shp.NumFields;
                if (!shp.EditInsertField(field, ref fieldIndex, null))
                {
                    fieldIndex = -1;
                }
            }

            // 插入shape和cellvalue
            hasError = insertShape(hasError, shp, fieldIndex);
            if (hasError)
            {
                shp.StopEditingShapes(false, true, null);
                shp.Close();
                StopExportToShp("导出泰森多边形失败", "错误");
                return;
            }

            shp.StopEditingShapes(true, true, null);
            shp.Save(null);
            shp.Close();
            StopExportToShp("导出成功!", "提示");
        }

        private bool insertShape(bool hasError, Shapefile shp, int fieldIndex)
        {
            for (int i = 0; i < savedVois.Count; ++i)
            {
                WaitBox.ProgressPercent = (i + 1) * 100 / savedVois.Count;

                foreach (Vertex[] vs in savedVois[i])
                {
                    MapWinGIS.Shape shape = new MapWinGIS.Shape();
                    shape.ShapeType = ShpfileType.SHP_POLYGON;
                    int shapeIndex = shp.NumShapes;
                    int pointIndex = shape.numPoints;

                    for (int j = 0; j <= vs.Length; ++j)
                    {
                        Vertex v = vs[j % vs.Length];
                        MapWinGIS.Point pt = new MapWinGIS.Point();
                        pt.x = v.X;
                        pt.y = v.Y;
                        shape.InsertPoint(pt, ref pointIndex);
                        ++pointIndex;
                    }

                    hasError = !shp.EditInsertShape(shape, ref shapeIndex);
                    if (hasError)
                    {
                        return hasError;
                    }

                    if (fieldIndex != -1)
                    {
                        shp.EditCellValue(fieldIndex, shapeIndex, savedLabels[i]);
                    }
                }
            }

            return hasError;
        }

        private void StopExportToShp(string exportResult, string msgTitle)
        {
            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
            MessageBox.Show(exportResult, msgTitle);
        }

        private VoronoiLayer()
        {
            mainModel = MainModel.GetInstance();
            mapForm = mainModel.MainForm.GetMapForm();
            axMap = mapForm.GetMapFormControl();
            drawLayer = axMap.NewDrawing(tkDrawReferenceList.dlSpatiallyReferencedList);
            isDrawn = false;
        }

        private static VoronoiLayer instance;
        private MainModel mainModel { get; set; }
        private int drawLayer;
        private bool isDrawn;
        private MapForm mapForm { get; set; }
        private readonly AxMapWinGIS.AxMap axMap;
        private List<List<Vertex[]>> savedVois;
        private List<string> savedLabels;
    }
}
