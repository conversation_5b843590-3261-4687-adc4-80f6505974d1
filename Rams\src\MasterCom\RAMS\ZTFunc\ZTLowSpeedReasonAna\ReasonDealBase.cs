﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Xml;
using DBDataViewer;

namespace MasterCom.RAMS.ZTFunc
{
    public enum eStat_NET_TYPE : byte
    {
        PS384 = 1,
        PS128 = 2,
        PS64 = 3,
        GPRS = 4,
        HSPA = 5,
        CDMA1X = 6,
        EVDO = 7,
        EDGE = 8,

        //special type
        NoNetType = 100
    }

    public abstract class ReasonDealBase
    {
        public static LowSpeedTempData data { get; set; }
        public bool IsCheck { get; set; } = true;
        public abstract Color TPColor { get; }
        public abstract string GetReasonName();
        public abstract string GetSuggestion();
        public abstract string GetDescription();
        public virtual bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if ((tp["TD_APP_DataStatus_DL"] != null) &&
                ((int)tp["TD_APP_DataStatus_DL"] == (int)eStat_NET_TYPE.HSPA))
            {
                return true;
            }
            return false;
        }
        public override string ToString() { return GetReasonName(); }
    }

    public class Reason_HSPA : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Olive; }
        }

        public override string GetReasonName()
        {
            return "占用HSPA";
        }

        public override string GetDescription()
        {
            return "定义：占用HSPA网络";
        }

        public override string GetSuggestion()
        {
            return "";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (tp["TD_APP_DataStatus_DL"] != null 
                && (int)tp["TD_APP_DataStatus_DL"] == (int)eStat_NET_TYPE.HSPA
                && (float?)tp["TD_PCCPCH_RSCP"] > condi.HSPA_Pccpch_Rscp 
                && (int?)tp["TD_PCCPCH_C2I"] > condi.HSPA_Pccpch_C2I)
            {
                return true;
            }
            return false;
        }
    }
    public class Reason_TD_R4 : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Gold; }
        }

        public override string GetReasonName()
        {
            return "占用R4";
        }

        public override string GetDescription()
        {
            return "定义：占用R4";
        }

        public override string GetSuggestion()
        {
            return "";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if ((tp["TD_APP_DataStatus_DL"] != null &&
                ((int)tp["TD_APP_DataStatus_DL"] == (int)eStat_NET_TYPE.PS128
                    || (int)tp["TD_APP_DataStatus_DL"] == (int)eStat_NET_TYPE.PS64
                    || (int)tp["TD_APP_DataStatus_DL"] == (int)eStat_NET_TYPE.PS384)))
            {
                return true;
            }
            return false;
        }
    }

    public class Reason_GSM : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Brown; }
        }

        public override string GetReasonName()
        {
            return "占用EDGE";
        }

        public override string GetDescription()
        {
            return "定义：占用EDGE";
        }

        public override string GetSuggestion()
        {
            return "";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if ((tp["TD_APP_DataStatus_DL"] != null
                && (int)tp["TD_APP_DataStatus_DL"] == (int)eStat_NET_TYPE.EDGE))
            {
                return true;
            }
            return false;
        }
    }

    public class Reason_Other : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Black; }
        }

        public override string GetReasonName()
        {
            return "其它";
        }

        public override string GetDescription()
        {
            return "";
        }

        public override string GetSuggestion()
        {
            return "";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            return true;
        }
    }

    public class Reason_WeakCover : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Red; }
        }

        public override string GetReasonName()
        {
            return "弱覆盖";
        }

        public override string GetDescription()
        {
            return "定义：主服及第一邻小区场强均小于等于门限值。";
        }

        public override string GetSuggestion()
        {
            return "调整功控参数调整、天馈调整、加站（拉远、直放站）。";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            float? sRscp = (float?)tp["TD_PCCPCH_RSCP"];
            int? nRscp = (int?)tp["TD_NCell_PCCPCH_RSCP", 0];
            if (isValid(sRscp) && isValid(nRscp)
                && sRscp <= condi.PoorCover_PccpchRscp && nRscp <= condi.PoorCover_PccpchRscp)
            {
                return true;
            }
            return false;
        }

        private bool isValid(float? rscp)
        {
            return rscp != null && rscp >= -140 && rscp <= -10;
        }
    }

    public class Reason_CoverLap : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Green; }
        }

        public override string GetReasonName()
        {
            return "过覆盖";
        }

        public override string GetDescription()
        {
            return "定义：采样点场强大于等于门限值，且采样点与小区的距离大于等于理想覆盖半径的1.6倍。"
                + Environment.NewLine + Environment.NewLine
                + "理想覆盖半径定义：小区与主瓣方向正负60度内最近3个基站的平均距离。";
        }

        public override string GetSuggestion()
        {
            return "功控参数调整、天馈调整。";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            TDCell mainCell = tp.GetMainCell_TD_TDCell();

            if (mainCell != null)
            {
                double cellDistance = mainCell.GetDistance(tp.Longitude, tp.Latitude);

                double maxDistance = (double)condi.CoverLap_Ratio * CfgDataProvider.CalculateRadius(mainCell, 3);

                float? sRscp = (float?)tp["TD_PCCPCH_RSCP"];
                if (isValid(sRscp) && sRscp >= condi.CoverLap_PccpchRscp && cellDistance >= maxDistance)
                {
                    return true;
                }
                return false;
            }
            return false;
        }

        private bool isValid(float? rscp)
        {
            return rscp != null && rscp >= -140 && rscp <= -10;
        }
    }

    public class Reason_BackCover : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Blue; }
        }

        public override string GetReasonName()
        {
            return "背向覆盖";
        }

        public override string GetDescription()
        {
            return "定义：采样点未落在该小区的主瓣方向内，并且小区与采样点的距离大于门限值。";
        }

        public override string GetSuggestion()
        {
            return "功控参数调整、天馈调整、查天线前后抑制比（加隔离网、换天线）。";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            TDCell mainCell = tp.GetMainCell_TD_TDCell();
            if (mainCell == null)
            {
                return false;
            }
            if (mainCell.Direction > 360)
            {
                return false;
            }
            if (mainCell.GetDistance(tp.Longitude, tp.Latitude) < condi.BackCover_Distance)
            {
                return false;
            }
            if (!isValidAngle(mainCell, tp.Longitude, tp.Latitude))
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 采样点到小区的夹角是否属于正常角度
        /// </summary>
        private bool isValidAngle(TDCell cell, double longitude, double latitude)
        {
            double angleDiff = 0;
            double distance = cell.GetDistance(longitude, latitude);

            ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
            double angle;
            double ygap = cell.GetDistance(cell.Longitude, latitude);
            double angleV = Math.Acos(ygap / distance);
            if (longitude >= cell.Longitude && latitude >= cell.Latitude)//1象限
            {
                angle = angleV * 180 / Math.PI;
            }
            else if (longitude <= cell.Longitude && latitude >= cell.Latitude)//2象限
            {
                angle = 360 - angleV * 180 / Math.PI;
            }
            else if (longitude <= cell.Longitude && latitude <= cell.Latitude)//3象限
            {
                angle = 180 + angleV * 180 / Math.PI;
            }
            else//4象限
            {
                angle = 180 - angleV * 180 / Math.PI;
            }

            angleDiff = Math.Abs(angle - cell.Direction);
            if (angleDiff > 180)
            {
                angleDiff = 360 - angleDiff;
            }
            if (angleDiff > 90)
            {
                return false;
            }
            return true;
        }
    }

    public class Reason_PilotPollution : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Orange; }
        }

        public override string GetReasonName()
        {
            return "导频污染";
        }

        public override string GetDescription()
        {
            return "定义：与最大电平相差6dBm以内的小区个数大于等于N个，且小区的场强大于等于门限值。";
        }

        public override string GetSuggestion()
        {
            return "调整问题路段涉及小区天馈，降低道路重叠覆盖度。";
        }

        private bool isValid(float? rscp)
        {
            return rscp != null && rscp >= -140 && rscp <= -10;
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            int count = 0;
            List<float> rscpList = new List<float>();

            //主服场强
            float? rscp = (float?)tp["TD_PCCPCH_RSCP"];
            if (isValid(rscp))
            {
                rscpList.Add((float)rscp);
            }
            else
            {
                return false;
            }

            //邻区场强
            try
            {
                for (int i = 0; i < 6; i++)
                {
                    int? nRscp = (int?)tp["TD_NCell_PCCPCH_RSCP", i];
                    if (!isValid(nRscp))
                    {
                        break;
                    }
                    else
                    {
                        rscpList.Add((float)nRscp);
                    }
                }

                //排序后统计与最强信号6dB内的小区数量
                if (rscpList.Count > 0)
                {
                    rscpList.Sort();

                    int index = rscpList.Count;
                    float maxRscp = rscpList[index - 1];

                    for (int i = 0; i < index; i++)
                    {
                        if ((maxRscp - rscpList[i]) < 6)
                        {
                            count++;
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
            return count >= condi.Pollute_CellNum;
        }
    }

    public class Reason_CornerEffect : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Purple; }
        }

        public override string GetReasonName()
        {
            return "拐角效应";
        }

        public override string GetDescription()
        {
            return "定义：PCCPCH_RSCP<-80dBm，且业务信道DPCH RSCP电平值3秒内恶化大于等于15dBm，在此期间出现高BLER采样点";
        }

        public override string GetSuggestion()
        {
            return "加快切换判决时间，调整天馈线增加重叠覆盖范围。";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            if (data.fileEventDic.ContainsKey(tp.FileID))
            {
                List<Event> evtList = data.fileEventDic[tp.FileID];
                List<Event> cornerEvtList = new List<Event>();
                foreach (Event evt in evtList)
                {
                    if (evt.ID == 239)
                    {
                        cornerEvtList.Add(evt);
                    }
                }
                foreach (Event evt in cornerEvtList)
                {
                    if (tp.DateTime >= evt.DateTime.AddSeconds(0 - condi.Corner_BeforeSecond) && tp.DateTime <= evt.DateTime.AddSeconds(condi.Corner_BeforeSecond))
                    {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    public class Reason_PoorC2I : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Tomato; }
        }

        public override string GetReasonName()
        {
            return "C/I差";
        }

        public override string GetDescription()
        {
            return "定义：PCCPCH_RSCP覆盖良好，但 PCCPCH_C/I 或 DPCH C/I 或 HSSCCH_C/I 或 HSPDSCH_C/I 低于门限值。";
        }

        public override string GetSuggestion()
        {
            return "在附近查找主频点与服务小区工作频点相同小区，核查是否存在干扰，针对干扰小区需进行频点优化、扰码优化、邻区优化。";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            if ((tp["TD_APP_DataStatus_DL"] != null &&
                ((int)tp["TD_APP_DataStatus_DL"] == (int)eStat_NET_TYPE.PS128
                    || (int)tp["TD_APP_DataStatus_DL"] == (int)eStat_NET_TYPE.PS64
                    || (int)tp["TD_APP_DataStatus_DL"] == (int)eStat_NET_TYPE.PS384)))
            {
                //return true
            }
            float? sRscp = (float?)tp["TD_PCCPCH_RSCP"];
            int? pccpch_ci = (int?)tp["TD_PCCPCH_C2I"];
            int? dpch_ci = (int?)tp["TD_DPCH_C2I"];
            int? hsscch_ci = (int?)tp["TD_HSDPA_HS_SCCH_CI"];
            int? hspdsch_ci = (int?)tp["TD_HSDPA_HS_PDSCH_CI"];

            if (sRscp != null && sRscp >= condi.PoorC2I_PccpchRscp)
            {
                return judegeIsValid(condi, pccpch_ci, dpch_ci, hsscch_ci, hspdsch_ci);
            }
            return false;
        }

        private bool judegeIsValid(LowSpeedCondi condi, int? pccpch_ci, int? dpch_ci, int? hsscch_ci, int? hspdsch_ci)
        {
            if (pccpch_ci != null && pccpch_ci <= condi.PoorC2I_PccpchC2i && pccpch_ci <= 40 && pccpch_ci >= -30)
            {
                return true;
            }
            if (dpch_ci != null && dpch_ci <= condi.PoorC2I_DpchC2i && dpch_ci <= 40 && dpch_ci >= -30)
            {
                return true;
            }
            if (hsscch_ci != null && hsscch_ci <= condi.PoorC2I_HsScchC2i && hsscch_ci <= 40 && hspdsch_ci >= -30)
            {
                return true;
            }
            if (hspdsch_ci != null && hspdsch_ci <= condi.PoorC2I_HsPdschC2i && hspdsch_ci <= 40 && hspdsch_ci >= -30)
            {
                return true;
            }
            return false;
        }
    }

    public class Reason_UpInterrupt : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Cyan; }
        }

        public override string GetReasonName()
        {
            return "上行干扰";
        }

        public override string GetDescription()
        {
            return "定义：PCCPCH_RSCP覆盖良好且PCCPCH C/I良好，UE TxPower高于门限值。";
        }

        public override string GetSuggestion()
        {
            return "GPS失步核查、外部干扰核查。";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            float? sRscp = (float?)tp["TD_PCCPCH_RSCP"];
            int? pccpch_ci = (int?)tp["TD_PCCPCH_C2I"];
            int? txpower = (int?)tp["TD_TxPower"];
            if (sRscp != null && pccpch_ci != null && txpower != null && sRscp >= condi.UpInter_PccpchRscp
                && pccpch_ci >= condi.UpInter_PccpchC2i && txpower >= condi.UpInter_UETxPower)
            {
                return true;
            }
            return false;
        }
    }

    //扰码相关性需要增加选项 方法一：按照组之间的相关性来判断，方法二：按照码与码之间的相关性来判断。
    public class Reason_CoCpi : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Coral; }
        }

        public override string GetReasonName()
        {
            return "同频同码干扰";
        }

        public override string GetDescription()
        {
            return "定义：邻小区主载波频点和占用小区业务频点相同，且扰码自相关性大于门限值。";
        }

        public override string GetSuggestion()
        {
            return "核查频率、码字。";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            float? sRscp = (float?)tp["TD_PCCPCH_RSCP"];
            int? sFcn = (int?)tp["TD_SCell_UARFCN"];
            int? sCpi = (int?)tp["TD_SCell_CPI"];
            if (sRscp == null || sFcn == null || sCpi == null || sRscp < -140 || sRscp > -10)
            {
                return false;
            }
            for (int indexA = 0; indexA < 10; indexA++)
            {
                int? rscpA = (int?)tp["TD_NCell_PCCPCH_RSCP", indexA];
                int? fcnA = (int?)tp["TD_NCell_UARFCN", indexA];
                int? cpiA = (int?)tp["TD_NCell_CPI", indexA];
                if (rscpA == null || fcnA == null || cpiA == null || rscpA < -140 || rscpA > -10)
                {
                    continue;
                }
                if (sFcn == fcnA)
                {
                    float coefficient = 0;
                    switch (condi.InterfereType)
                    {
                        case EInterfere.ByCoCPI:
                            coefficient = MasterCom.Util.TDCpiGroup.GetCpiCoefficient((int)sCpi, (int)cpiA);
                            break;
                        case EInterfere.ByGroup:
                            coefficient = MasterCom.Util.TDCpiGroup.GetCpiCoefficientByGroup((short)sCpi, (short)cpiA);
                            break;
                        default:
                            break;
                    }
                    if (coefficient >= condi.PscInter_Ratio)
                    {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    public class Reason_PCConfig : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Pink; }
        }

        public override string GetReasonName()
        {
            return "功控配置";
        }

        public override string GetDescription()
        {
            return "定义：主服小区PCCPCH_RSCP-DPCH RSCP的差值超过门限值。";
        }

        public override string GetSuggestion()
        {
            return "可能是下行功控功率补偿不及时导致下行业务频点干扰，建议调整功率控制步长、下行最大、最小功率门限。";
        }

        private bool isValid(float? rscp)
        {
            return rscp != null && rscp >= -140 && rscp <= -10;
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            float? sRscp = (float?)tp["TD_PCCPCH_RSCP"];
            float? dpch_rscp = (float?)tp["TD_DPCH_RSCP"];
            if (isValid(sRscp) && isValid(dpch_rscp) && (sRscp - dpch_rscp) >= condi.PowerControl_Diff)
            {
                return true;
            }
            return false;
        }
    }

    public class Reason_HODelay : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Violet; }
        }

        public override string GetReasonName()
        {
            return "切换不及时";
        }

        public override string GetDescription()
        {
            return "定义：主服小区PCCPCH_RSCP低于门限值，邻区中存在PCCPCH_RSCP超过主服小区NdBm，且持续5秒及以上的情况，在此期间出现高BLER采样点。";
        }

        public override string GetSuggestion()
        {
            return "减小2A事件触发时间,检查目标小区是否拥塞。";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            if (data.fileEventDic.ContainsKey(tp.FileID))
            {
                foreach (Event evt in data.fileEventDic[tp.FileID])
                {
                    if (evt.ID == 240
                        && tp.DateTime >= evt.DateTime.AddSeconds(0 - condi.HODelay_BeforeSecond)
                        && tp.DateTime <= evt.DateTime.AddSeconds(condi.HODelay_AfterSecond))
                    {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    public class Reason_HOProblem : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Yellow; }
        }

        public override string GetReasonName()
        {
            return "切换不合理";
        }

        public override string GetDescription()
        {
            return "定义：切换后主服小区电平小于邻小区电平NdB，且持续3秒以上。";
        }

        public override string GetSuggestion()
        {
            return "核查切换参数、核查邻区、核查源小区、目标小区状态。";
        }

        private bool isValid(float? rscp)
        {
            return rscp != null && rscp >= -140 && rscp <= -10;
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            if (data.fileEventDic.ContainsKey(tp.FileID))
            {
                foreach (Event evt in data.fileEventDic[tp.FileID])
                {
                    if ((evt.ID == 145 || evt.ID == 148)
                        && sLessNRscp(evt.DateTime, evt.DateTime.AddSeconds(3), tp, condi))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 时间段内 Rs<Rn+3
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="tp"></param>
        /// <returns></returns>
        private bool sLessNRscp(DateTime startTime, DateTime endTime, TestPoint tp, LowSpeedCondi condi)
        {
            if (tp.DateTime < startTime || tp.DateTime > endTime)
            {
                return false;
            }
            float? sRscp = (float?)tp["TD_PCCPCH_RSCP"];
            if (!isValid(sRscp))
            {
                return false;
            }

            int? nRscp = (int?)tp["TD_NCell_PCCPCH_RSCP", 0]; //只看第一邻区

            if (!isValid(nRscp))
            {
                return false;
            }

            if ((sRscp - nRscp) <= condi.HOAbnormal_Diff)
            {
                return true;
            }
            return false;
        }
    }

    public class Reason_AntennaFault : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.Beige; }
        }

        public override string GetReasonName()
        {
            return "智能天线故障";
        }

        public override string GetDescription()
        {
            return "定义：当前采样点的DPCH_RSCP与最近5个采样点的DPCH_RSCP的最大值进行比较，降低超过30dBm，在此期间出现高BLER采样点。";
        }

        public override string GetSuggestion()
        {
            return "检查小区智能天线RU。";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            if (data.tpList_Antenna.Count < 6)
            {
                return false;
            }
            float? dpch_rscp = (float?)tp["TD_DPCH_RSCP"];
            if (dpch_rscp == null)
            {
                return false;
            }
            float dpchMax = -1000000;
            for (int i = 0; i < data.tpList_Antenna.Count - 1; i++)
            {
                float? nDpch_rscp = (float?)data.tpList_Antenna[i]["TD_DPCH_RSCP"];
                if (nDpch_rscp != null && nDpch_rscp > dpchMax)
                {
                    dpchMax = (float)nDpch_rscp;
                }
            }
            return dpchMax - (float)dpch_rscp >= condi.Antenna_DpchRscp;
        }
    }

    public class Reason_LowSchedule : ReasonDealBase
    {
        public override Color TPColor
        {
            get { return Color.CornflowerBlue; }
        }

        public override string GetReasonName()
        {
            return "调度率低";
        }

        public override string GetDescription()
        {
            return "定义：调度率低于门限值。";
        }

        public override string GetSuggestion()
        {
            return "对交换机资源进行核查。";
        }

        public override bool IsAchieve(TestPoint tp, LowSpeedCondi condi)
        {
            if (!base.IsAchieve(tp, condi))
            {
                return false;
            }
            int? scheduled = (int?)tp["TD_HSDPA_HS_ScchScheduled_Rate"];
            if (scheduled == null || scheduled == -10000000)
            {
                return false;
            }
            return scheduled <= condi.HSScchScheduledRate;
        }
    }
}
