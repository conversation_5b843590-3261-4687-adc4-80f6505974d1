﻿namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    partial class KPIForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControlKPI = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewKPI = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.label1 = new System.Windows.Forms.Label();
            this.radioBtn_LTE = new System.Windows.Forms.RadioButton();
            this.radioBtn_GSM = new System.Windows.Forms.RadioButton();
            this.radioBtn_TD = new System.Windows.Forms.RadioButton();
            this.btn_kpiTemplet = new DevExpress.XtraEditors.SimpleButton();
            this.btn_searchKPI = new DevExpress.XtraEditors.SimpleButton();
            this.radioGroupBox = new System.Windows.Forms.GroupBox();
            this.comboBox_template = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBox_testName = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlKPI)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewKPI)).BeginInit();
            this.radioGroupBox.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.comboBox_template.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBox_testName.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControlKPI
            // 
            this.gridControlKPI.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridControlKPI.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlKPI.Location = new System.Drawing.Point(12, 47);
            this.gridControlKPI.MainView = this.gridViewKPI;
            this.gridControlKPI.Name = "gridControlKPI";
            this.gridControlKPI.Size = new System.Drawing.Size(928, 255);
            this.gridControlKPI.TabIndex = 0;
            this.gridControlKPI.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewKPI});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // ExportToExcel
            // 
            this.ExportToExcel.Name = "ExportToExcel";
            this.ExportToExcel.Size = new System.Drawing.Size(129, 22);
            this.ExportToExcel.Text = "导出Excel";
            this.ExportToExcel.Click += new System.EventHandler(this.ExportToExcel_Click);
            // 
            // gridViewKPI
            // 
            this.gridViewKPI.Appearance.ViewCaption.Options.UseTextOptions = true;
            this.gridViewKPI.Appearance.ViewCaption.TextOptions.Trimming = DevExpress.Utils.Trimming.Word;
            this.gridViewKPI.Appearance.ViewCaption.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridViewKPI.ColumnPanelRowHeight = 50;
            this.gridViewKPI.GridControl = this.gridControlKPI;
            this.gridViewKPI.IndicatorWidth = 40;
            this.gridViewKPI.Name = "gridViewKPI";
            this.gridViewKPI.OptionsBehavior.Editable = false;
            this.gridViewKPI.OptionsBehavior.ReadOnly = true;
            this.gridViewKPI.OptionsView.ColumnAutoWidth = false;
            this.gridViewKPI.OptionsView.EnableAppearanceEvenRow = true;
            this.gridViewKPI.CustomDrawRowIndicator += new DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventHandler(this.gridViewKPI_CustomDrawRowIndicator);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 19);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "测试名称：";
            // 
            // radioBtn_LTE
            // 
            this.radioBtn_LTE.AutoSize = true;
            this.radioBtn_LTE.Location = new System.Drawing.Point(143, 12);
            this.radioBtn_LTE.Name = "radioBtn_LTE";
            this.radioBtn_LTE.Size = new System.Drawing.Size(46, 18);
            this.radioBtn_LTE.TabIndex = 3;
            this.radioBtn_LTE.Text = "LTE";
            this.radioBtn_LTE.UseVisualStyleBackColor = true;
            this.radioBtn_LTE.MouseClick += new System.Windows.Forms.MouseEventHandler(this.radioBtn_LTE_MouseClick);
            // 
            // radioBtn_GSM
            // 
            this.radioBtn_GSM.AutoSize = true;
            this.radioBtn_GSM.Checked = true;
            this.radioBtn_GSM.Location = new System.Drawing.Point(17, 12);
            this.radioBtn_GSM.Name = "radioBtn_GSM";
            this.radioBtn_GSM.Size = new System.Drawing.Size(49, 18);
            this.radioBtn_GSM.TabIndex = 4;
            this.radioBtn_GSM.TabStop = true;
            this.radioBtn_GSM.Text = "GSM";
            this.radioBtn_GSM.UseVisualStyleBackColor = true;
            this.radioBtn_GSM.MouseClick += new System.Windows.Forms.MouseEventHandler(this.radioBtn_GSM_MouseClick);
            // 
            // radioBtn_TD
            // 
            this.radioBtn_TD.AutoSize = true;
            this.radioBtn_TD.Location = new System.Drawing.Point(84, 12);
            this.radioBtn_TD.Name = "radioBtn_TD";
            this.radioBtn_TD.Size = new System.Drawing.Size(41, 18);
            this.radioBtn_TD.TabIndex = 5;
            this.radioBtn_TD.Text = "TD";
            this.radioBtn_TD.UseVisualStyleBackColor = true;
            this.radioBtn_TD.MouseClick += new System.Windows.Forms.MouseEventHandler(this.radioBtn_TD_MouseClick);
            // 
            // btn_kpiTemplet
            // 
            this.btn_kpiTemplet.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_kpiTemplet.Location = new System.Drawing.Point(756, 15);
            this.btn_kpiTemplet.Name = "btn_kpiTemplet";
            this.btn_kpiTemplet.Size = new System.Drawing.Size(89, 22);
            this.btn_kpiTemplet.TabIndex = 6;
            this.btn_kpiTemplet.Text = "指标模版";
            this.btn_kpiTemplet.Click += new System.EventHandler(this.btn_kpiTemplet_Click);
            // 
            // btn_searchKPI
            // 
            this.btn_searchKPI.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_searchKPI.Location = new System.Drawing.Point(851, 15);
            this.btn_searchKPI.Name = "btn_searchKPI";
            this.btn_searchKPI.Size = new System.Drawing.Size(89, 22);
            this.btn_searchKPI.TabIndex = 7;
            this.btn_searchKPI.Text = "查询";
            this.btn_searchKPI.Click += new System.EventHandler(this.btn_searchKPI_Click);
            // 
            // radioGroupBox
            // 
            this.radioGroupBox.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.radioGroupBox.Controls.Add(this.radioBtn_LTE);
            this.radioGroupBox.Controls.Add(this.radioBtn_TD);
            this.radioGroupBox.Controls.Add(this.radioBtn_GSM);
            this.radioGroupBox.Location = new System.Drawing.Point(312, 7);
            this.radioGroupBox.Name = "radioGroupBox";
            this.radioGroupBox.Size = new System.Drawing.Size(200, 34);
            this.radioGroupBox.TabIndex = 10;
            this.radioGroupBox.TabStop = false;
            // 
            // comboBox_template
            // 
            this.comboBox_template.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.comboBox_template.Location = new System.Drawing.Point(534, 16);
            this.comboBox_template.Name = "comboBox_template";
            this.comboBox_template.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBox_template.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.comboBox_template.Size = new System.Drawing.Size(212, 21);
            this.comboBox_template.TabIndex = 14;
            this.comboBox_template.SelectedIndexChanged += new System.EventHandler(this.comboBox_templet_SelectedIndexChanged);
            // 
            // comboBox_testName
            // 
            this.comboBox_testName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.comboBox_testName.Location = new System.Drawing.Point(85, 16);
            this.comboBox_testName.Name = "comboBox_testName";
            this.comboBox_testName.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBox_testName.Size = new System.Drawing.Size(204, 21);
            this.comboBox_testName.TabIndex = 15;
            this.comboBox_testName.EditValueChanged += new System.EventHandler(this.comboBox_testName_EditValueChanged);
            // 
            // KPIForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(952, 314);
            this.ControlBox = false;
            this.Controls.Add(this.comboBox_testName);
            this.Controls.Add(this.comboBox_template);
            this.Controls.Add(this.radioGroupBox);
            this.Controls.Add(this.btn_searchKPI);
            this.Controls.Add(this.btn_kpiTemplet);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.gridControlKPI);
            this.Name = "KPIForm";
            this.Text = "指标";
            ((System.ComponentModel.ISupportInitialize)(this.gridControlKPI)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewKPI)).EndInit();
            this.radioGroupBox.ResumeLayout(false);
            this.radioGroupBox.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.comboBox_template.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBox_testName.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlKPI;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewKPI;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.RadioButton radioBtn_LTE;
        private System.Windows.Forms.RadioButton radioBtn_GSM;
        private System.Windows.Forms.RadioButton radioBtn_TD;
        private DevExpress.XtraEditors.SimpleButton btn_kpiTemplet;
        private DevExpress.XtraEditors.SimpleButton btn_searchKPI;
        private System.Windows.Forms.GroupBox radioGroupBox;
        private DevExpress.XtraEditors.ComboBoxEdit comboBox_template;
        private DevExpress.XtraEditors.CheckedComboBoxEdit comboBox_testName;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem ExportToExcel;
    }
}