﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class ZTWeakSINRRoadBaseQuery<T> : DIYAnalyseByFileBackgroundBase where T : new()
    {
        protected abstract string themeName { get; }
        protected abstract string rsrpName { get; }
        protected abstract string sinrName { get; }

        protected int areaID { get; set; } = -1;
        protected int areaTypeID { get; set; } = -1;
        protected List<WeakSINRRoad> weakCoverList = null;
        public WeakSINRRoadCondition WeakCondition { get; set; } = new WeakSINRRoadCondition();
        private static T instance { get; set; }
        public static T GetInstance()
        {
            if (instance == null)
            {
                instance = new T();
            }
            return instance;
        }

        protected ZTWeakSINRRoadBaseQuery()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
        }

        public override string Name
        {
            get { return "SINR质差路段"; }
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                setRoadCond();
                return true;
            }
            WeakSINRRoadNRSettingDlg dlg = new WeakSINRRoadNRSettingDlg(WeakCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                WeakCondition = dlg.GetCondition();
                setRoadCond();
                return true;
            }
            return false;
        }

        protected PercentRoadBuilder roadBuilder;
        public void setRoadCond()
        {
            PercentRoadCondition roadCond = new PercentRoadCondition(WeakCondition.WeakSINRPercent / 100, OnOneRoadComplete);
            roadCond.IsCheckDuration = WeakCondition.CheckMinDuration;
            roadCond.MinDuration = WeakCondition.MinDuration;
            roadCond.IsCheckMinLength = WeakCondition.CheckMinDistance;
            roadCond.MinLength = WeakCondition.MinCoverRoadDistance;
            roadCond.IsCheckDistanceGap = true;
            roadCond.MaxDistanceGap = WeakCondition.Max2TPDistance;

            this.roadBuilder = new PercentRoadBuilder(roadCond);
        }

        protected List<TestPoint> tps { get; set; } = new List<TestPoint>();
        protected double duration = 0;

        protected void OnOneRoadComplete(object sender, PercentRoadItem roadItem)
        {
            double curWeakPercent = Math.Round(roadItem.ValidPercent * 100, 2);
            double curDis = roadItem.Length;
            duration = roadItem.Duration;
            if (curWeakPercent < this.WeakCondition.WeakSINRPercent)
            {
                return;
            }
            tps = roadItem.TestPoints;
            addToReportInfo(tps, curWeakPercent, curDis);
        }

        protected virtual void addToReportInfo(List<TestPoint> testPointList, double curWeakPercent, double curDis)
        {
            if (testPointList.Count == 0)
            {
                return;
            }
            WeakSINRRoad weakCover = new WeakSINRRoad();
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                float? sinr = getSinr(testPoint);
                float? rsrp = getRsrp(testPoint);
                weakCover.Add(sinr, rsrp, testPoint);
                if (weakCover.MotorWay == "" || weakCover.MotorWay == null)
                {
                    weakCover.SetMotorWay(testPoint.AreaID, testPoint.AreaTypeID);
                }
            }
            weakCover.WeakPercent = curWeakPercent;
            weakCover.Distance = curDis;
            weakCover.Duration = duration;
            weakCover.CityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            saveWeakCoverInfo(weakCover);
        }

        protected override void fireShowForm()
        {
            throw (new Exception("需实现界面呈现"));
        }

        protected virtual bool isTPInRegion(TestPoint tp)
        {
            if (condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
            {
                return true;
            }
            return false;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            bool ret = false;
            try
            {
                bool inRegion = isTPInRegion(testPoint);
                if (inRegion)
                {
                    float? sinr = getSinr(testPoint);
                    ret = WeakCondition.IsValidate(sinr);
                }
            }
            catch
            {
                //continue
            }
            return ret;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            weakCoverList = new List<WeakSINRRoad>();
        }

        protected virtual float? getSinr(TestPoint testPoint)
        {
            return (float?)testPoint[sinrName];
        }

        protected virtual float? getRsrp(TestPoint testPoint)
        {
            return (float?)testPoint[rsrpName];
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    tps = new List<TestPoint>();
                    areaID = fileDataManager.GetFileInfo().AreaID;
                    areaTypeID = fileDataManager.GetFileInfo().AreaTypeID;

                    foreach (TestPoint tp in fileDataManager.TestPoints)
                    {
                        if (WeakCondition.CheckRSRP)
                        {
                            if (getRsrp(tp) >= WeakCondition.MinRSRP)
                            {
                                roadBuilder.AddPoint(tp, this.isValidTestPoint(tp));
                            }
                        }
                        else
                        {
                            roadBuilder.AddPoint(tp, this.isValidTestPoint(tp));
                        }
                    }
                    this.roadBuilder.StopRoading();
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }
  
        protected virtual void saveWeakCoverInfo(WeakSINRRoad info)
        {
            if (info == null || !WeakCondition.MatchMinWeakCoverDistance(info.Distance) || !WeakCondition.MatchMinWeakCoverDuration(info.Duration))
            {
                return;
            }
            //save 2 list
            if (weakCoverList == null)
            {
                weakCoverList = new List<WeakSINRRoad>();
            }
            if (!weakCoverList.Contains(info))
            {
                info.SN = weakCoverList.Count + 1;
                info.FindRoadName();
                info.FindGridName();
                info.MakeSummary();
                info.SetMotorWay(areaID, areaTypeID);
                weakCoverList.Add(info);
            }
        }
    }
}
