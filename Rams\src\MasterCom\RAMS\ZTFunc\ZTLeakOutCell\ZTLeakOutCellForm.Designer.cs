﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLeakOutCellForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.objectListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSn = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCPI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnUARFCN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellSrvType = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTestPointCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnStartTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnEndTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLevMin = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLevMax = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLevMean = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDistance = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miColAll = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnSn);
            this.objectListView.AllColumns.Add(this.olvColumnCellName);
            this.objectListView.AllColumns.Add(this.olvColumnLAC);
            this.objectListView.AllColumns.Add(this.olvColumnCI);
            this.objectListView.AllColumns.Add(this.olvColumnCPI);
            this.objectListView.AllColumns.Add(this.olvColumnUARFCN);
            this.objectListView.AllColumns.Add(this.olvColumnCellSrvType);
            this.objectListView.AllColumns.Add(this.olvColumnFileName);
            this.objectListView.AllColumns.Add(this.olvColumnTestPointCount);
            this.objectListView.AllColumns.Add(this.olvColumnStartTime);
            this.objectListView.AllColumns.Add(this.olvColumnEndTime);
            this.objectListView.AllColumns.Add(this.olvColumnRxLevMin);
            this.objectListView.AllColumns.Add(this.olvColumnRxLevMax);
            this.objectListView.AllColumns.Add(this.olvColumnRxLevMean);
            this.objectListView.AllColumns.Add(this.olvColumnDistance);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSn,
            this.olvColumnCellName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnCPI,
            this.olvColumnUARFCN,
            this.olvColumnCellSrvType,
            this.olvColumnFileName,
            this.olvColumnTestPointCount,
            this.olvColumnStartTime,
            this.olvColumnEndTime,
            this.olvColumnRxLevMin,
            this.olvColumnRxLevMax,
            this.olvColumnRxLevMean,
            this.olvColumnDistance});
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.Location = new System.Drawing.Point(0, 0);
            this.objectListView.Name = "objectListView";
            this.objectListView.OwnerDraw = true;
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(1247, 405);
            this.objectListView.TabIndex = 2;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            this.objectListView.VirtualMode = true;
            this.objectListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.objectListView_MouseDoubleClick);
            // 
            // olvColumnSn
            // 
            this.olvColumnSn.HeaderFont = null;
            this.olvColumnSn.Text = "序号";
            this.olvColumnSn.Width = 50;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.AspectName = "CellName";
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区";
            this.olvColumnCellName.Width = 120;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.AspectName = "LAC";
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.AspectName = "CI";
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnCPI
            // 
            this.olvColumnCPI.AspectName = "CPI";
            this.olvColumnCPI.HeaderFont = null;
            this.olvColumnCPI.Text = "CPI";
            // 
            // olvColumnUARFCN
            // 
            this.olvColumnUARFCN.AspectName = "UARFCN";
            this.olvColumnUARFCN.HeaderFont = null;
            this.olvColumnUARFCN.Text = "UARFCN";
            // 
            // olvColumnCellSrvType
            // 
            this.olvColumnCellSrvType.HeaderFont = null;
            this.olvColumnCellSrvType.Text = "主服/邻小区";
            this.olvColumnCellSrvType.Width = 80;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.AspectName = "FileName";
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 200;
            // 
            // olvColumnTestPointCount
            // 
            this.olvColumnTestPointCount.AspectName = "TestPointCount";
            this.olvColumnTestPointCount.HeaderFont = null;
            this.olvColumnTestPointCount.Text = "采样点数";
            // 
            // olvColumnStartTime
            // 
            this.olvColumnStartTime.AspectName = "SequenceStartTime";
            this.olvColumnStartTime.HeaderFont = null;
            this.olvColumnStartTime.Text = "采样点开始时间";
            this.olvColumnStartTime.Width = 100;
            // 
            // olvColumnEndTime
            // 
            this.olvColumnEndTime.AspectName = "SequenceEndTime";
            this.olvColumnEndTime.HeaderFont = null;
            this.olvColumnEndTime.Text = "采样点结束时间";
            this.olvColumnEndTime.Width = 100;
            // 
            // olvColumnRxLevMin
            // 
            this.olvColumnRxLevMin.AspectName = "MinRxLev";
            this.olvColumnRxLevMin.HeaderFont = null;
            this.olvColumnRxLevMin.Text = "最小电平";
            // 
            // olvColumnRxLevMax
            // 
            this.olvColumnRxLevMax.AspectName = "MaxRxLev";
            this.olvColumnRxLevMax.HeaderFont = null;
            this.olvColumnRxLevMax.Text = "最大电平";
            // 
            // olvColumnRxLevMean
            // 
            this.olvColumnRxLevMean.AspectName = "AvgRxLev";
            this.olvColumnRxLevMean.HeaderFont = null;
            this.olvColumnRxLevMean.Text = "平均电平";
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.AspectName = "Distance";
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "持续距离(m)";
            this.olvColumnDistance.Width = 100;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls,
            this.toolStripSeparator1,
            this.miExpandAll,
            this.miColAll});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(149, 76);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(148, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(145, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(148, 22);
            this.miExpandAll.Text = "展开所有节点";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miColAll
            // 
            this.miColAll.Name = "miColAll";
            this.miColAll.Size = new System.Drawing.Size(148, 22);
            this.miColAll.Text = "收起所有节点";
            this.miColAll.Click += new System.EventHandler(this.miColAll_Click);
            // 
            // ZTLeakOutCellForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1247, 405);
            this.ContextMenuStrip = this.contextMenuStrip;
            this.Controls.Add(this.objectListView);
            this.Name = "ZTLeakOutCellForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "外泄小区列表";
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnSn;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnTestPointCount;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMin;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMax;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMean;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnCPI;
        private BrightIdeasSoftware.OLVColumn olvColumnUARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnStartTime;
        private BrightIdeasSoftware.OLVColumn olvColumnEndTime;
        private BrightIdeasSoftware.OLVColumn olvColumnCellSrvType;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miColAll;
    }
}