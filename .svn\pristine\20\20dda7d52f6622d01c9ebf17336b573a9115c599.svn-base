﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class OverCoverLapSetForm : BaseDialog
    {
        public OverCoverLapSetForm()
        {
            InitializeComponent();
        }
        public void SetCondition(OverCoverCondition cond)
        {
            if (cond == null)
            {
                cond = new OverCoverCondition();
            }
            numFilter.Value = cond.RxlevFilter;
            numNearestCellCount.Value = cond.NearestCellCount;
            numDisFactor.Value = (decimal)cond.DisFactor;
            chkFreqBand.Checked = cond.IsChkBand;
            cbxFreqBand.SelectedItem = cond.BandType;
            cbxSampleCount.Checked = cond.IsChkSampleCount;
            numSampleCount.Value = cond.SampleMinCount;
            cbxPercent.Checked = cond.IsChkOverPercent;
            numPercent.Value = (decimal)cond.BadPercent * 100;
            chkDistance.Checked = cond.IsChkDistance;
            numDistanceMin.Value = cond.MinDistance;
            numDistanceMax.Value = cond.MaxDistance;
        }
        public OverCoverCondition GetCondition()
        {
            OverCoverCondition cond = new OverCoverCondition();
            cond.RxlevFilter = (int)numFilter.Value;
            cond.NearestCellCount = (int)numNearestCellCount.Value;
            cond.DisFactor = (float)numDisFactor.Value;
            cond.IsChkBand = chkFreqBand.Checked;
            cond.BandType = cbxFreqBand.SelectedItem.ToString();
            cond.IsChkSampleCount = cbxSampleCount.Checked;
            cond.SampleMinCount = (int)numSampleCount.Value;
            cond.IsChkOverPercent = cbxPercent.Checked;
            cond.BadPercent = (int)numPercent.Value * 0.01f;
            cond.IsChkDistance = chkDistance.Checked;
            cond.MinDistance = (int)numDistanceMin.Value;
            cond.MaxDistance = (int)numDistanceMax.Value;
            return cond;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void cbxSampleCount_CheckedChanged(object sender, EventArgs e)
        {
            numSampleCount.Enabled = cbxSampleCount.Checked;
        }

        private void cbxPercent_CheckedChanged(object sender, EventArgs e)
        {
            numPercent.Enabled = cbxPercent.Checked;
        }

        private void chkDistance_CheckedChanged(object sender, EventArgs e)
        {
            numDistanceMin.Enabled = chkDistance.Checked;
            numDistanceMax.Enabled = chkDistance.Checked;
        }

        private void chkFreqBand_CheckedChanged(object sender, EventArgs e)
        {
            cbxFreqBand.Enabled = chkFreqBand.Checked;
        }
    }

    public class OverCoverCondition
    {
        public int NearestCellCount { get; set; } = 3;
        public float DisFactor { get; set; } = 1.6f;
        public int RxlevFilter { get; set; } = -90;
        public bool IsChkSampleCount { get; set; }

        private int sampleMinCount = 100;
        public int SampleMinCount
        {
            get 
            {
                if (sampleMinCount < 1)
                {
                    sampleMinCount = 100;
                }
                return sampleMinCount;
            }
            set { sampleMinCount = value; }
        }
        public bool IsChkOverPercent { get; set; }

        private float badPercent = 1.0f;
        public float BadPercent
        {
            get
            {
                if (badPercent > 1 || badPercent < 0.01)
                {
                    badPercent = 1.0f;
                }
                return badPercent;
            }
            set { badPercent = value; }
        }
        public bool IsChkDistance { get; set; }

        private int minDistance = 300;
        public int MinDistance
        {
            get
            {
                if (minDistance > 100000 || minDistance < 0)
                {
                    minDistance = 300;
                }
                return minDistance;
            }
            set { minDistance = value; }
        }
        private int maxDistance = 3000;
        public int MaxDistance
        {
            get 
            {
                if (maxDistance > 100000 || maxDistance < 0)
                {
                    maxDistance = 3000;
                }
                return maxDistance; 
            }
            set { maxDistance = value; }
        }
        public bool IsChkBand { get; set; }
        
        public string BandType { get; set; } = "D";
    }
}