﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTCellSplit
{
    public class CellSplitManager
    {
        public int Cellid { get; set; }
        public string Points { get; set; }
        public List<CellSplitPoints> CellSplitPointsList { get; set; }
    }

    public class CellSplitEXT
    {
        public int Cellid { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
    }

    public class CellSplitPoints
    {
        public double Longitude { get; set; }
        public double Latitude { get; set; }
    }

    public class CellSplitSetup
    {
        public string Name { get; set; }
        public string Table { get; set; }

        override public string ToString()
        {
            return Name;
        }
    }


    public class CellSplitKPI
    {
        public DateTime Tmdat { get; set; }
        public int Cellid { get; set; }
        public int Bscid { get; set; }
        public int Lac { get; set; }
        public int Ci { get; set; }
        public float Kpi { get; set; }
    }

    /// <summary>
    /// 新多边形数据
    /// </summary>
    public class CellSplitNewEXT
    {
        public int Iid { get; set; }
        public double Ilongitude { get; set; }
        public double Ilatitude { get; set; }
        public int Ibandtype { get; set; }
        public int Iround { get; set; }
        public int Isubid { get; set; }
        public double Islongitude { get; set; }
        public double Islatitude { get; set; }
        public int Idistance { get; set; }
        public int Iangle { get; set; }
    }

    public class CellSplitNewEXTConfig
    {
        public int Ilac { get; set; }
        public int Ici { get; set; }
        public int Ibandtype { get; set; }
        public double Ilongitude { get; set; }
        public double Ilatitude { get; set; }
        public int Iangle_dir { get; set; }
        public int Ifingerid { get; set; }
        public double Ifingerlongitude { get; set; }
        public double Ifingerlatitude { get; set; }
    }

    public class CellSplitNewManager
    {
        public string Lac_ci { get; set; }
        public List<CellSplitNewEXT> CellSplitPointsList { get; set; }
    }

    public class LongLatItem
    {
        public double FLongitude { get; set; }
        public double FLatitude { get; set; }

        public LongLatItem()
        {
            FLongitude = 0;
            FLatitude = 0;
        }

        public override bool Equals(object obj)
        {
            LongLatItem other = obj as LongLatItem;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.FLongitude.Equals(other.FLongitude) &&
                    this.FLatitude.Equals(other.FLatitude));
        }

        public override int GetHashCode()
        {
            return this.FLongitude.GetHashCode();
        }
    }
}
