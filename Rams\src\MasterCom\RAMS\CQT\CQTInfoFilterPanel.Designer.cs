﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTInfoFilterPanel
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.ccmbxDensityType = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.ccmbxSpaceType = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.ccmbxNetType = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.ccmbxCvrType = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.ccmbxPointType = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.ccmbxDensityType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ccmbxSpaceType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ccmbxNetType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ccmbxCvrType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ccmbxPointType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // ccmbxDensityType
            // 
            this.ccmbxDensityType.Location = new System.Drawing.Point(344, 33);
            this.ccmbxDensityType.Name = "ccmbxDensityType";
            this.ccmbxDensityType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ccmbxDensityType.Properties.DropDownRows = 10;
            this.ccmbxDensityType.Properties.SelectAllItemCaption = "全选";
            this.ccmbxDensityType.Size = new System.Drawing.Size(200, 21);
            this.ccmbxDensityType.TabIndex = 7;
            // 
            // ccmbxSpaceType
            // 
            this.ccmbxSpaceType.Location = new System.Drawing.Point(631, 2);
            this.ccmbxSpaceType.Name = "ccmbxSpaceType";
            this.ccmbxSpaceType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ccmbxSpaceType.Properties.DropDownRows = 10;
            this.ccmbxSpaceType.Properties.SelectAllItemCaption = "全选";
            this.ccmbxSpaceType.Size = new System.Drawing.Size(200, 21);
            this.ccmbxSpaceType.TabIndex = 9;
            // 
            // ccmbxNetType
            // 
            this.ccmbxNetType.Location = new System.Drawing.Point(344, 2);
            this.ccmbxNetType.Name = "ccmbxNetType";
            this.ccmbxNetType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ccmbxNetType.Properties.DropDownRows = 10;
            this.ccmbxNetType.Properties.SelectAllItemCaption = "全选";
            this.ccmbxNetType.Size = new System.Drawing.Size(200, 21);
            this.ccmbxNetType.TabIndex = 8;
            // 
            // ccmbxCvrType
            // 
            this.ccmbxCvrType.Location = new System.Drawing.Point(60, 33);
            this.ccmbxCvrType.Name = "ccmbxCvrType";
            this.ccmbxCvrType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ccmbxCvrType.Properties.DropDownRows = 10;
            this.ccmbxCvrType.Properties.SelectAllItemCaption = "全选";
            this.ccmbxCvrType.Size = new System.Drawing.Size(200, 21);
            this.ccmbxCvrType.TabIndex = 11;
            // 
            // ccmbxPointType
            // 
            this.ccmbxPointType.Location = new System.Drawing.Point(60, 2);
            this.ccmbxPointType.Name = "ccmbxPointType";
            this.ccmbxPointType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ccmbxPointType.Properties.DropDownRows = 10;
            this.ccmbxPointType.Properties.SelectAllItemCaption = "全选";
            this.ccmbxPointType.Size = new System.Drawing.Size(200, 21);
            this.ccmbxPointType.TabIndex = 10;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(286, 36);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(52, 14);
            this.labelControl2.TabIndex = 4;
            this.labelControl2.Text = "密度类型:";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(573, 5);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(52, 14);
            this.labelControl5.TabIndex = 3;
            this.labelControl5.Text = "建筑类型:";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(2, 5);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(52, 14);
            this.labelControl4.TabIndex = 2;
            this.labelControl4.Text = "地点类型:";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(286, 5);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(52, 14);
            this.labelControl3.TabIndex = 6;
            this.labelControl3.Text = "网络类型:";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(2, 36);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(52, 14);
            this.labelControl1.TabIndex = 5;
            this.labelControl1.Text = "覆盖类型:";
            // 
            // CQTInfoFilterPanel
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.ccmbxDensityType);
            this.Controls.Add(this.ccmbxSpaceType);
            this.Controls.Add(this.ccmbxNetType);
            this.Controls.Add(this.ccmbxCvrType);
            this.Controls.Add(this.ccmbxPointType);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl1);
            this.Name = "CQTInfoFilterPanel";
            this.Size = new System.Drawing.Size(831, 57);
            ((System.ComponentModel.ISupportInitialize)(this.ccmbxDensityType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ccmbxSpaceType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ccmbxNetType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ccmbxCvrType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ccmbxPointType.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.CheckedComboBoxEdit ccmbxDensityType;
        private DevExpress.XtraEditors.CheckedComboBoxEdit ccmbxSpaceType;
        private DevExpress.XtraEditors.CheckedComboBoxEdit ccmbxNetType;
        private DevExpress.XtraEditors.CheckedComboBoxEdit ccmbxCvrType;
        private DevExpress.XtraEditors.CheckedComboBoxEdit ccmbxPointType;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl1;
    }
}
