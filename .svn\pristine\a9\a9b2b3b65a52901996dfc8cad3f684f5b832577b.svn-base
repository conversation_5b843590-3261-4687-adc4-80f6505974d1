﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class VoiceAnaByFreqBandResult
    {
        protected VoiceAnaByFreqBandResult(string freqBand)
        {
            FreqBand = freqBand;
        }

        public string FreqBand { get; }

        public int CellCount { get; set; }
        public Dictionary<int, int> CellDic { get; set; } = new Dictionary<int, int>();

        public CallInfo MoCallInfo { get; set; } = new CallInfo();
        public CallInfo MtCallInfo { get; set; } = new CallInfo();

        public DataInfo SampleRate { get; set; } = new DataInfo();
        public DataInfo RsrpMoreThan110Rate { get; set; } = new DataInfo();
        public DataInfo SinrMoreThan3Rate { get; set; } = new DataInfo();
        public DataInfo SinrMoreThan0Rate { get; set; } = new DataInfo();
        public DataInfo MosMoreThan3Rate { get; set; } = new DataInfo();
        public DataInfo CoverRate { get; set; } = new DataInfo();
        public AvgInfo RsrpData { get; set; } = new AvgInfo();
        public AvgInfo SinrData { get; set; } = new AvgInfo();

        protected abstract float? getMOSParamName(TestPoint tp, ref string mosParamName);
        protected abstract int? getPCI(TestPoint tp);

        public virtual void AddTP(TestPoint tp, float rsrp, float sinr, ref string mosParamName)
        {
            RsrpData.Sum += rsrp;
            RsrpData.Count++;
            SinrData.Sum += sinr;
            SinrData.Count++;

            SampleRate.Count++;
            RsrpMoreThan110Rate.TotalCount++;
            SinrMoreThan3Rate.TotalCount++;
            SinrMoreThan0Rate.TotalCount++;
            CoverRate.TotalCount++;

            if (rsrp >= -110)
            {
                RsrpMoreThan110Rate.Count++;
            }
            if (sinr >= -3)
            {
                SinrMoreThan3Rate.Count++;
            }
            if (sinr >= 0)
            {
                SinrMoreThan0Rate.Count++;
            }
            if (rsrp >= -110 && sinr >= -3)
            {
                CoverRate.Count++;
            }

            int? pci = getPCI(tp);
            if (pci != null && !CellDic.ContainsKey((int)pci))
            {
                CellDic.Add((int)pci, 0);
            }

            float? mos = getMOSParamName(tp, ref mosParamName);
            if (mos != null && mos > 0 && mos <= 5)
            {
                MosMoreThan3Rate.TotalCount++;
                if (mos >= 3)
                {
                    MosMoreThan3Rate.Count++;
                }
            }
        }

        public void SetTotalSampleCount(int fileTotalTestPoint)
        {
            SampleRate.TotalCount = fileTotalTestPoint;
        }

        public virtual void AddEvt(Event evt, int moMtFlag, int evtFlag)
        {
            if (moMtFlag == 1)
            {
                MoCallInfo.AddEvt(evt, evtFlag);
            }
            else
            {
                MtCallInfo.AddEvt(evt, evtFlag);
            }
        }

        public virtual void Calculate()
        {
            CellCount = CellDic.Count;
            SampleRate.Calculate();
            RsrpMoreThan110Rate.Calculate();
            SinrMoreThan3Rate.Calculate();
            SinrMoreThan0Rate.Calculate();
            MosMoreThan3Rate.Calculate();
            CoverRate.Calculate();
            RsrpData.Calculate();
            SinrData.Calculate();
        }

        //添加用于计算合计
        public void Add(VoiceAnaByFreqBandResult res)
        {
            foreach (var cell in res.CellDic.Keys)
            {
                if (!CellDic.ContainsKey(cell))
                {
                    CellDic.Add(cell, 0);
                }
            }
            MoCallInfo.Add(res.MoCallInfo);
            MtCallInfo.Add(res.MtCallInfo);
            SampleRate.Count += res.SampleRate.Count;
            RsrpMoreThan110Rate.Add(res.RsrpMoreThan110Rate);
            SinrMoreThan3Rate.Add(res.SinrMoreThan3Rate);
            SinrMoreThan0Rate.Add(res.SinrMoreThan0Rate);
            MosMoreThan3Rate.Add(res.MosMoreThan3Rate);
            CoverRate.Add(res.CoverRate);
            RsrpData.Add(res.RsrpData);
            SinrData.Add(res.SinrData);
        }

        public class AvgInfo
        {
            public double Sum { get; set; }
            public int Count { get; set; }
            public double Avg { get; set; }
            public string AvgDesc { get; set; }

            public void Calculate()
            {
                if (Count != 0)
                {
                    Avg = Math.Round(Sum / Count, 2);
                    AvgDesc = Avg.ToString();
                }
                else
                {
                    AvgDesc = "-";
                }
            }

            public void Add(AvgInfo info)
            {
                Sum += info.Sum;
                Count += info.Count;
            }
        }

        public class DataInfo
        {
            public int Count { get; set; }
            public int TotalCount { get; set; }
            public double Rate { get; set; }
            public string RateDesc { get; set; }

            public void Calculate()
            {
                if (TotalCount != 0)
                {
                    Rate = Math.Round(Count * 1d / TotalCount, 4);
                    RateDesc = Rate * 100 + "%";
                }
                else
                {
                    RateDesc = "-";
                }
            }

            public void Add(DataInfo info)
            {
                Count += info.Count;
                TotalCount += info.TotalCount;
            }
        }

        public class CallInfo
        {
            public int CallRequestCount { get; set; }
            public int CallEstablishedCount { get; set; }
            public int DropCallCount { get; set; }

            public void Add(CallInfo info)
            {
                CallRequestCount += info.CallRequestCount;
                CallEstablishedCount += info.CallEstablishedCount;
                DropCallCount += info.DropCallCount;
            }

            public void AddEvt(Event evt, int evtFlag)
            {
                switch (evtFlag)
                {
                    case 1:
                        CallRequestCount++;
                        break;
                    case 2:
                        CallEstablishedCount++;
                        break;
                    case 3:
                        DropCallCount++;
                        break;

                }
            }
        }
    }


}
