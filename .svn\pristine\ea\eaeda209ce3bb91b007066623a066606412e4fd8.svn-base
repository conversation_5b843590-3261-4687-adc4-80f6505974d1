﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FileDistributeConfirmForm : BaseForm
    {
        public FileDistributeConfirmForm(List<DistributeFileCity> cityList, List<FileInfo> fileList)
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            miDelCity.Click += MiDelCity_Click;
            miAddCity.Click += MiAddCity_Click;
            treeView1.MouseDown += TreeView_MouseDown;
            contextMenuStrip1.Opening += ContextMenuStrip_Opening;

            this.cityList = cityList;
            this.fileList = fileList;
            TransferToView();
            FillView();
        }

        private void ContextMenuStrip_Opening(object sender, CancelEventArgs e)
        {
            if (treeView1.SelectedNode == null)
            {
                e.Cancel = true;
            }
            else if (treeView1.SelectedNode.Level == 0) // selected file
            {
                miAddCity.Visible = true;
                miDelCity.Visible = false;
            }
            else if (treeView1.SelectedNode.Level == 1) // select city
            {
                miAddCity.Visible = false;
                miDelCity.Visible = true;
            }
        }

        private void TreeView_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button != MouseButtons.Right)
            {
                return;
            }

            TreeNode selectedNodt = treeView1.GetNodeAt(e.X, e.Y);
            if (selectedNodt == null)
            {
                return;
            }
            treeView1.SelectedNode = selectedNodt;
        }

        private void MiDelCity_Click(object sender, EventArgs e)
        {
            CityView cv = treeView1.SelectedNode.Tag as CityView;
            FileView fv = treeView1.SelectedNode.Parent.Tag as FileView;

            if (fv.CityViews.Contains(cv))
            {
                fv.CityViews.Remove(cv);
                FillView();
            }
        }

        private void MiAddCity_Click(object sender, EventArgs e)
        {
            // 查找并构建尚未显示的地市
            List<CityView> prepareCitys = new List<CityView>();
            FileView fv = treeView1.SelectedNode.Tag as FileView;
            foreach (DistributeFileCity city in cityList)
            {
                bool existed = false;
                foreach (CityView cv in fv.CityViews)
                {
                    if (city.CityName == cv.CityName)
                    {
                        existed = true;
                        break;
                    }
                }
                if (!existed)
                {
                    CityView cv = new CityView();
                    cv.CityName = city.CityName;
                    if (city.FileSampleCountDic.ContainsKey(fv.FileId))
                    {
                        cv.SampleCount = city.FileSampleCountDic[fv.FileId];
                    }
                    prepareCitys.Add(cv);
                }
            }

            // 填充到选择对话框
            List<string> cityInfos = new List<string>();
            foreach (CityView cv in prepareCitys)
            {
                cityInfos.Add(string.Format("{0}[采样点数: {1}]", cv.CityName, cv.SampleCount));
            }
            FileDistributeAddCityForm addForm = new FileDistributeAddCityForm();
            addForm.FillCitys(cityInfos);
            if (addForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            if (addForm.SelectedIndex == -1 || addForm.SelectedIndex >= prepareCitys.Count)
            {
                return;
            }

            // 更新
            fv.CityViews.Add(prepareCitys[addForm.SelectedIndex]);
            FillView();
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            TransferToData();
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void TransferToView()
        {
            Dictionary<int, FileView> fileIdViewDic = new Dictionary<int, FileView>();
            foreach (FileInfo fileInfo in fileList)
            {
                FileView fileView = new FileView(fileInfo);
                fileIdViewDic.Add(fileInfo.ID, fileView);
            }

            foreach (DistributeFileCity city in cityList)
            {
                List<FileInfo> tmpList = city.FileList;
                foreach (FileInfo fileInfo in tmpList)
                {
                    if (!fileIdViewDic.ContainsKey(fileInfo.ID))
                    {
                        continue;
                    }

                    CityView cityView = new CityView();
                    cityView.CityName = city.CityName;
                    cityView.SampleCount = city.FileSampleCountDic[fileInfo.ID];
                    fileIdViewDic[fileInfo.ID].CityViews.Add(cityView);
                }
            }

            fileViews = new List<FileView>(fileIdViewDic.Values);
        }

        private void TransferToData()
        {
            List<DistributeFileCity> newCityList = new List<DistributeFileCity>();
            foreach (DistributeFileCity city in cityList)
            {
                newCityList.Add(new DistributeFileCity(city));
            }

            foreach (FileView fv in fileViews)
            {
                foreach (CityView cv in fv.CityViews)
                {
                    foreach (DistributeFileCity newCity in newCityList)
                    {
                        if (newCity.CityName == cv.CityName)
                        {
                            newCity.AddFile(fv.FileId);
                            newCity.FileSampleCountDic[fv.FileId] = cv.SampleCount;
                            break;
                        }
                    }
                }
            }

            cityList.Clear();
            cityList.AddRange(newCityList);
        }

        private void FillView()
        {
            treeView1.BeginUpdate();
            treeView1.Nodes.Clear();
            foreach (FileView fv in fileViews)
            {
                TreeNode fn = new TreeNode(string.Format("{0}[采样点数:{1}]", fv.FileName, fv.SampleCount));
                fn.Tag = fv;
                treeView1.Nodes.Add(fn);

                foreach (CityView cv in fv.CityViews)
                {
                    TreeNode cn = new TreeNode(string.Format("{0}[采样点数:{1}]", cv.CityName, cv.SampleCount));
                    cn.Tag = cv;
                    fn.Nodes.Add(cn);
                }
            }
            treeView1.ExpandAll();
            treeView1.EndUpdate();
        }

        private List<DistributeFileCity> cityList;
        private List<FileInfo> fileList;
        private List<FileView> fileViews;

        private class FileView
        {
            public FileView(FileInfo file)
            {
                FileId = file.ID;
                FileName = file.Name;
                SampleCount = file.TestPointCount;
                CityViews = new List<CityView>();
            }

            public int FileId
            {
                get;
                private set;
            }

            public string FileName
            {
                get;
                set;
            }

            public int SampleCount
            {
                get;
                set;
            }

            public List<CityView> CityViews
            {
                get;
                set;
            }
        }

        private class CityView
        {
            public CityView()
            {

            }

            public string CityName
            {
                get;
                set;
            }

            public int SampleCount
            {
                get;
                set;
            }
        }
    }
}
