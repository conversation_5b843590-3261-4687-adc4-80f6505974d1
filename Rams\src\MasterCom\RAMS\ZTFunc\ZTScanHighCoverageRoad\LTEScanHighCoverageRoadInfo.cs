﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEScanHighCoverageRoadCondition : ScanHighCoverageRoadCondition
    {
        public LTEScanHighCoverageRoadCondition()
        {
            this.RelCoverate = 4;
            this.RoadDistance = 50;
            this.RxLevMaxDiff = 6;
            this.RxLevMin = -95;
            this.SampleDistance = 50;
        }
        // 是否屏蔽小区
        public bool IsShieldProblem { get; set; }
        public bool IsLeakOutCheck { get; set; }
        public bool IsFarCoverCheck { get; set; }
        public int NearestBtsCount { get; set; } = 3;
        public double RadiusFactor { get; set; } = 1.6;

        public bool IsCloseSimuCheck { get; set; }
        public Dictionary<string, bool> CloseSimuDic { get; set; }

        public bool IsTwoEarfcn { get; set; }
        public bool IsFreqBand { get; set; }
        public LTEBandType band { get; set; }

        /// <summary>
        ///运营商 0：移动 1：电信 2：联通
        /// </summary>
        public int CarrierId { get; set; }

        /// <summary>
        /// 频点集合
        /// </summary>
        public List<FreqPoint> ListFreqPoint { get; set; }

        public int AbsValue { get; set; } = -110;
        public bool IsAbsCheck { get; set; }
        public bool IsRelativeCheck { get; set; }
        public int AbsCoverate { get; set; } = 11;

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["IsShieldProblem"] = IsShieldProblem;
                param["IsLeakOutCheck"] = IsLeakOutCheck;
                param["IsFarCoverCheck"] = IsFarCoverCheck;
                param["NearestBtsCount"] = NearestBtsCount;
                param["RadiusFactor"] = RadiusFactor;
                param["IsCloseSimuCheck"] = IsCloseSimuCheck;
                param["CloseSimuDic"] = CloseSimuDic;
                param["IsTwoEarfcn"] = IsTwoEarfcn;
                param["IsFreqBand"] = IsFreqBand;
                param["band"] = (int)band;
                param["AbsValue"] = AbsValue;
                param["IsAbsCheck"] = IsAbsCheck;
                param["IsRelativeCheck"] = IsRelativeCheck;
                param["AbsCoverate"] = AbsCoverate;
                param["RxLevMaxDiff"] = RxLevMaxDiff;
                param["RxLevMin"] = RxLevMin;
                param["RelCoverate"] = RelCoverate;
                param["RoadDistance"] = RoadDistance;
                param["RoadMinPercent"] = RoadMinPercent;
                param["SampleDistance"] = SampleDistance;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                IsShieldProblem = setParam(param, "IsShieldProblem", IsShieldProblem);
                IsLeakOutCheck = setParam(param, "IsLeakOutCheck", IsLeakOutCheck);
                IsFarCoverCheck = setParam(param, "IsFarCoverCheck", IsFarCoverCheck);
                NearestBtsCount = setParam(param, "NearestBtsCount", NearestBtsCount);
                RadiusFactor = setParam(param, "RadiusFactor", RadiusFactor);
                IsCloseSimuCheck = setParam(param, "IsCloseSimuCheck", IsCloseSimuCheck);
                addCloseSimuDic(param);
                IsTwoEarfcn = setParam(param, "IsTwoEarfcn", IsTwoEarfcn);
                IsFreqBand = setParam(param, "IsFreqBand", IsFreqBand);
                band = setParam(param, "band", band);
                AbsValue = setParam(param, "AbsValue", AbsValue);
                IsAbsCheck = setParam(param, "IsAbsCheck", IsAbsCheck);
                IsRelativeCheck = setParam(param, "IsRelativeCheck", IsRelativeCheck);
                AbsCoverate = setParam(param, "AbsCoverate", AbsCoverate);
                RxLevMaxDiff = setParam(param, "RxLevMaxDiff", RxLevMaxDiff);
                RxLevMin = setParam(param, "RxLevMin", RxLevMin);
                RelCoverate = setParam(param, "RelCoverate", RelCoverate);
                RoadDistance = setParam(param, "RoadDistance", RoadDistance);
                RoadMinPercent = setParam(param, "RoadMinPercent", RoadMinPercent);
                SampleDistance = setParam(param, "SampleDistance", SampleDistance);
            }
        }

        private void addCloseSimuDic(Dictionary<string, object> param)
        {
            Dictionary<string, object> closeSimuDic = setParam(param, "CloseSimuDic", new Dictionary<string, object>());
            if (closeSimuDic.Count > 0)
            {
                if (CloseSimuDic == null)
                {
                    CloseSimuDic = new Dictionary<string, bool>();
                }
                else
                {
                    CloseSimuDic.Clear();
                }
                foreach (var varPair in closeSimuDic)
                {
                    CloseSimuDic.Add(varPair.Key, (bool)varPair.Value);
                }
            }
        }

        private T setParam<T>(Dictionary<string, object> param, string name, T defaultValue)
        {
            T res = defaultValue;
            if (param.ContainsKey(name))
            {
                res = (T)param[name];
                if (res == null)
                {
                    res = defaultValue;
                }
            }
           
            return res;
        }
    }

    public class LTEScanHighCoverageRoadInfo
    {
        public LTEScanHighCoverageRoadInfo(string type)
        {
            this.NetType = type;
        }
        public string NetType { get; set; }
        public int SN { get; set; }
        public int FileID { get; set; }
        public string FileName { get; set; } = "";
        public string RoadName { get; set; } = "";
        public float RssiMax { get; set; } = 255;
        public float RssiMin { get; set; } = 255;
        public float RssiAvg { get; set; } = 255;
        public float RssiSample { get; set; }
        public List<LTEScanHighCoveragePointInfo> SampleLst { get; set; } = new List<LTEScanHighCoveragePointInfo>();

        private double distance = 0;
        public double Distance
        {
            get { return Math.Round(distance, 2); }
            set { distance = value; }
        }

        private double percent = 0;
        public double Percent
        {
            get { return Math.Round(percent, 2); }
            set { percent = value; }
        }

        public double LongitudeMid
        {
            get
            {
                int index = SampleLst.Count / 2;
                return SampleLst[index].Tp.Longitude;
            }
        }
        public double LatitudeMid
        {
            get
            {
                int index = SampleLst.Count / 2;
                return SampleLst[index].Tp.Latitude;
            }
        }

        public void SetRssi(float rssi)
        {
            RssiSample++;

            if (RssiSample == 1)     //first
            {
                RssiMax = rssi;
                RssiMin = rssi;
                RssiAvg = rssi;
            }
            else
            {
                if (RssiMax < rssi)
                {
                    RssiMax = rssi;
                }
                if (RssiMin > rssi)
                {
                    RssiMin = rssi;
                }

                RssiAvg += rssi;
            }
        }

        public void GetResult()
        {
            FileID = SampleLst[0].Tp.FileID;
            FileName = SampleLst[0].Tp.FileName;
            if (RssiSample > 0)
            {
                RssiAvg = (float)Math.Round((double)RssiAvg / (double)RssiSample, 2);
            }
            RoadName = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(LongitudeMid, LatitudeMid);
        }

        public string GetFirstTime()
        {
            if (SampleLst.Count > 0)
            {
                return SampleLst[0].Tp.TimeStringWithMillisecond;
            }
            return "";
        }
        public string GetLasttime()
        {
            if (SampleLst.Count > 0)
            {
                return SampleLst[SampleLst.Count - 1].Tp.TimeStringWithMillisecond;
            }
            return "";
        }

        public BackgroundResult ConverToBackgroundResult(int subFuncId)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.SubFuncID = subFuncId;
            bgResult.ProjectString = MasterCom.RAMS.BackgroundFunc.BackgroundFuncBaseSetting.GetInstance().projectType;

            bgResult.FileID = FileID;
            bgResult.FileName = FileName;
            bgResult.ISTime = SampleLst[0].Tp.Time;
            bgResult.IETime = SampleLst[SampleLst.Count - 1].Tp.Time;

            bgResult.CellType = BackgroundCellType.LTE;
            bgResult.LongitudeMid = this.LongitudeMid;
            bgResult.LatitudeMid = this.LatitudeMid;
            bgResult.RoadDesc = this.RoadName;
            bgResult.DistanceLast = this.Distance;
            bgResult.SampleCount = this.SampleLst.Count;
            bgResult.RxLevMax = this.RssiMax;
            bgResult.RxLevMean = this.RssiAvg;
            bgResult.RxLevMin = this.RssiMin;
            bgResult.StrDesc = this.NetType;

            bgResult.AddImageValue((float)this.Percent);
            bgResult.AddImageValue(this.GetFirstTime());
            bgResult.AddImageValue(this.GetLasttime());
            return bgResult;
        }
    }

    public class LTEScanHighCoveragePointInfo
    {
        public int SN { get; set; }
        public TestPoint Tp { get; set; }
        public List<LTEScanHighCoverageCellInfo> CellList { get; set; }

        public LTEScanHighCoveragePointInfo(int SN, TestPoint tp, List<LTEScanHighCoverageCellInfo> cellList)
        {
            this.SN = SN;
            this.Tp = tp;
            this.CellList = cellList;
        }
    }

    public class LTEScanHighCoverageCellInfo
    {
        public int SN { get; set; }
        public LTECell LteCell { get; set; }
        public string CellName { get; set; } = "";
        public string TAC { get; set; }
        public string ECI { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }
        public string Longitude { get; set; }
        public string Latitude { get; set; }
        public float Rssi { get; set; }
        public bool IsValid { get; private set; } = false;
        public string Distance { get; set; }

        public LTEScanHighCoverageCellType CellType
        {
            get;
            set;
        }

        public string CellTypeDesc
        {
            get;
            set;
        }

        public LTEScanHighCoverageCellInfo(TestPoint tp, int index)
        {
            this.SN = index + 1;

            int? _tac = (int?)tp["LTESCAN_TopN_TAC", index];
            int? _eci = (int?)tp["LTESCAN_TopN_ECI", index];
            float? _rssi = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", index];
            int? _earfcn = (int?)tp["LTESCAN_TopN_EARFCN", index];
            short? _pci = (short?)tp["LTESCAN_TopN_PCI", index];

            if (_rssi != null && _earfcn != null && _pci != null)
            {
                IsValid = true;
                LTECell curCell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, _tac, _eci, _earfcn, (int?)_pci, tp.Longitude, tp.Latitude);

                if (curCell != null)
                {
                    this.LteCell = curCell;
                    this.CellName = curCell.Name;
                    this.TAC = curCell.TAC.ToString();
                    this.ECI = curCell.ECI.ToString();
                    this.Longitude = curCell.Longitude.ToString();
                    this.Latitude = curCell.Latitude.ToString();
                    this.Distance = Math.Round(curCell.GetDistance(tp.Longitude, tp.Latitude), 2).ToString();
                }
                else
                {
                    this.LteCell = null;
                    this.CellName = _earfcn.ToString() + "_" + _pci.ToString();
                    this.TAC = "-";
                    this.ECI = "-";
                    this.Longitude = "-";
                    this.Latitude = "-";
                    this.Distance = "-";
                }
                this.EARFCN = (int)_earfcn;
                this.PCI = (int)_pci;
                this.Rssi = (float)_rssi;
            }
        }
    }

    public enum LTEScanHighCoverageCellType
    {
        Unknown = 0x00,
        FarCover = 0x01,
        LeakOut = 0x02,
        CloseSimu = 0x04,
    }

    public class LTEScanHighCoverageCellChecker
    {
        public LTEScanHighCoverageCellChecker(LTEScanHighCoverageRoadCondition cond)
        {
            this.cond = cond;
        }

        public LTEScanHighCoverageCellType JudgeCellType(TestPoint tp, ICell cell)
        {
            if (cell == null || !(cell is LTECell))
            {
                return LTEScanHighCoverageCellType.Unknown;
            }
            LTECell lteCell = cell as LTECell;

            int tac = lteCell.TAC;
            int eci = lteCell.ECI;
            string key = string.Format("{0}_{1}", tac, eci);
            if (this.cond.IsCloseSimuCheck && this.cond.CloseSimuDic.ContainsKey(key))
            {
                return LTEScanHighCoverageCellType.CloseSimu;
            }

            if (this.cond.IsLeakOutCheck && lteCell.Type == LTEBTSType.Indoor)
            {
                return LTEScanHighCoverageCellType.LeakOut;
            }
            
            if (this.cond.IsFarCoverCheck)
            {
                double radius = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(lteCell, this.cond.NearestBtsCount);
                double dis = MasterCom.Util.MathFuncs.GetDistance(lteCell.Longitude, lteCell.Latitude, tp.Longitude, tp.Latitude);
                if (dis > radius * cond.RadiusFactor)
                {
                    return LTEScanHighCoverageCellType.FarCover;
                }
            }
            
            return LTEScanHighCoverageCellType.Unknown;
        }

        public bool JudgeCloseSimu(ICell cell)
        {
            if (this.cond.IsCloseSimuCheck || cell == null || !(cell is LTECell))
            {
                return false;
            }
            LTECell lteCell = cell as LTECell;

            int tac = lteCell.TAC;
            int eci = lteCell.ECI;
            string key = string.Format("{0}_{1}", tac, eci);
            return this.cond.CloseSimuDic.ContainsKey(key);
        }

        public static string GetCellTypeDesc(LTEScanHighCoverageCellType cellType)
        {
            switch (cellType)
            {
                case LTEScanHighCoverageCellType.Unknown:
                    return "";
                case LTEScanHighCoverageCellType.CloseSimu:
                    return "模拟闭站";
                case LTEScanHighCoverageCellType.LeakOut:
                    return "室分信号";
                case LTEScanHighCoverageCellType.FarCover:
                    return "越区覆盖";
            }
            return "";
        }

        private readonly LTEScanHighCoverageRoadCondition cond;
    }
}
