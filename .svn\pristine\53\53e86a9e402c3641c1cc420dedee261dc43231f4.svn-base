﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.MTGis
{
    public class MTPolygon
    {
        public string Name
        {
            get;
            set;
        }

        public DbRect Bounds
        {
            get;
            private set;
        }

        public MTPolygon()
        {
            Parts = new List<MultiPolygon>();
            this.Name = string.Empty;
            this.Bounds = new DbRect();
        }

        internal void Append(MapWinGIS.Shape shape)
        {
            MultiPolygon mP = new MultiPolygon(shape);
            append(mP);
        }

        private void append(MultiPolygon multiPolygon)
        {
            if (Bounds.IsEmpty)
            {
                Bounds = multiPolygon.Bounds.Clone();
            }
            else
            {
                Bounds.MergeRects(multiPolygon.Bounds);
            }
            Parts.Add(multiPolygon);
        }

        public List<MultiPolygon> Parts
        {
            get;
            private set;
        }

        internal bool ContainsRectCenter(DbRect dbRect)
        {
            return CheckPointInRegion(dbRect.Center().x, dbRect.Center().y);
        }

        internal bool CheckPointInRegion(double lng, double lat)
        {
            foreach (MultiPolygon polygon in Parts)
            {
                if (polygon.Contains(lng, lat))
                {
                    return true;
                }
            }
            return false;
        }


        internal bool Contains(double x, double y)
        {
            return CheckPointInRegion(x, y);
        }

        internal bool CheckRectCenterInRegion(DbRect dbRect)
        {
            return ContainsRectCenter(dbRect);
        }

        internal void AppendRoadToPolygon(MapWinGIS.Shape roadLine)
        {
            if (roadLine.ShapeType != MapWinGIS.ShpfileType.SHP_POLYLINE)
            {
                throw new ArgumentException("参数错误，roadLine必须为SHP_POLYLINE");
            }
            const double roadWidth = 0.0004;//约为40米

            for (int pi = 0; pi < roadLine.NumParts; pi++)
            {
                List<DbPoint> points = new List<DbPoint>();
                List<DbPoint> ptListOne = new List<DbPoint>();
                List<DbPoint> ptListTwo = new List<DbPoint>();
                List<DbPoint> pnts = ShapeHelper.GetPartShapePoints(roadLine, pi);
                int numPointsOfPart = pnts.Count;
                double lastXDiff = 0;
                double lastYDiff = 0;
                for (int px = 0; px < numPointsOfPart - 1; px++)
                {
                    DbPoint ptThis = pnts[px];
                    DbPoint ptNext = pnts[px + 1];
                    double atan = Math.Atan2(ptNext.y - ptThis.y, ptNext.x - ptThis.x);
                    double xDiff = roadWidth * Math.Sin(atan);
                    double yDiff = roadWidth * Math.Cos(atan);
                    ptListOne.Add(new DbPoint(ptThis.x - xDiff, ptThis.y + yDiff));
                    ptListTwo.Add(new DbPoint(ptThis.x + xDiff, ptThis.y - yDiff));
                    if (px == numPointsOfPart - 2)
                    {
                        lastXDiff = xDiff;
                        lastYDiff = yDiff;
                    }
                }

                DbPoint lastPoint = pnts[pnts.Count - 1];
                ptListOne.Add(new DbPoint(lastPoint.x - lastXDiff, lastPoint.y + lastYDiff));
                ptListTwo.Add(new DbPoint(lastPoint.x + lastXDiff, lastPoint.y - lastYDiff));
                points.AddRange(ptListOne);
                ptListTwo.Reverse();
                points.AddRange(ptListTwo);
                if (points.Count < 3)
                {
                    continue;
                }
                MultiPolygon mP = new MultiPolygon(points);
                append(mP);
            }
        }

        internal bool CheckStreetInRegion(MapWinGIS.Shape street)
        {
            for (int part = 0; part < street.NumParts; part++)
            {
                List<DbPoint> pnts = ShapeHelper.GetPartShapePoints(street, part);
                foreach (DbPoint pnt in pnts)
                {
                    if (this.Contains(pnt.x, pnt.y))
                    {
                        return true;
                    }
                }
            }
            return false;
        }
        internal bool CheckRectIntersectSelStreets(DbRect rect)
        {
            foreach(MultiPolygon polygon in this.Parts)
            {
                List<AtomicPolygon> atomicParts = polygon.AtomicParts;
                foreach(AtomicPolygon poly in atomicParts)
                {
                    foreach(DbPoint pt in poly.Vertexes)
                    {
                        if(rect.IsPointInThisRect(pt))
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
    }
}
