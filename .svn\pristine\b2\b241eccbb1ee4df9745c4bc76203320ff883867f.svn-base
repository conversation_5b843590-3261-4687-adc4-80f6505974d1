﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class WrongCover : CauseBase
    {
        public override string Name
        {
            get { return "覆盖不符"; }
        }
        [NonSerialized]
        private ICell serverCell = null;
        public int Angle { get; set; } = 60;
        public override string Desc
        {
            get
            {
                return string.Format("采样点不在小区的正负{0}主瓣方向内", Angle);
            }
        }

        public override string Suggestion
        {
            get
            {
                return string.Format("核查小区{0}工参的方向角与实际覆盖方向", serverCell != null ? serverCell.Name : "");
            }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                LTECell cell = pnt.GetMainLTECell_TdOrFdd();
                if (cell==null)
                {
                    return;
                }
                if (!MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, pnt.Longitude, pnt.Latitude, cell.Direction, Angle))
                //if (!MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, pnt.Longitude, pnt.Latitude, cell.Direction))
                {
                    WrongCover cln = this.Clone() as WrongCover;
                    segItem.SetReason(new LowSpeedPointDetail(pnt, cln));
                }
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["angle"] = this.Angle;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.Angle = (int)value["angle"];
            }
        }
    }

}
