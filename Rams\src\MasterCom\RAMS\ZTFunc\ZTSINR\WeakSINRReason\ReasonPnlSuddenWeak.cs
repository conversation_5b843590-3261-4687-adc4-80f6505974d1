﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class ReasonPnlSuddenWeak : ReasonPanelBase
    {
        public ReasonPnlSuddenWeak()
        {
            InitializeComponent();
        }

        public override void AttachReason(ReasonBase reason)
        {
            base.AttachReason(reason);
            numTimeLimit.ValueChanged -= timeLimit_ValueChanged;
            numTimeLimit.Value = (decimal)ZTWeakSINRReason.suddenWeakTime;
            numTimeLimit.ValueChanged += timeLimit_ValueChanged;
            numSinrAvg.ValueChanged -= numSinrAvg_ValueChanged;
            numSinrAvg.Value = (decimal)ZTWeakSINRReason.suddenWeakAvg;
            numSinrAvg.ValueChanged += numSinrAvg_ValueChanged;
        }

        void timeLimit_ValueChanged(object sender, EventArgs e)
        {
            ZTWeakSINRReason.suddenWeakTime = (int)numTimeLimit.Value;
        }
        void numSinrAvg_ValueChanged(object sender, EventArgs e)
        {
            ZTWeakSINRReason.suddenWeakAvg = (float)numSinrAvg.Value;
        }
    }
}
