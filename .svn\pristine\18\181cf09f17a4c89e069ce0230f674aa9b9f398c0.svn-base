﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Stat;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class KPIReportManager
    {
        bool isCanEditPublicReport { get; set; } = false;
        private static KPIReportManager instance = null;
        private KPIReportManager()
        {
            Init();
        }

        public void Init()
        {
            Reports = new List<ReportStyle>();
            try
            {
                System.IO.DirectoryInfo directory = new System.IO.DirectoryInfo(string.Format(System.Windows.Forms.Application.StartupPath + "/config/templates/"));
                System.IO.FileInfo[] files = directory.GetFiles("kpi_*.xml", System.IO.SearchOption.TopDirectoryOnly);
                if (files.Length > 0)
                {
                    foreach (System.IO.FileInfo file in files)
                    {
                        XmlConfigFile configFile = new XmlConfigFile(file.FullName);
                        Dictionary<string, object> dic = configFile.GetItemValue("ReportSetting", "styles") as Dictionary<string, object>;
                        if (dic == null)
                        {
                            continue;
                        }
                        ReportStyle rptstyle = new ReportStyle();
                        rptstyle.Param = dic;
                        int index = file.Name.IndexOf("kpi_") + 4;
                        rptstyle.name = file.Name.Substring(index).Replace(".xml", "");
                        Reports.Add(rptstyle);
                    }
                }
                else
                {
                    XmlConfigFile configFile = new XmlConfigFile(string.Format(System.Windows.Forms.Application.StartupPath + "/config/reports.xml"));
                    List<Object> list = configFile.GetItemValue("ReportSetting", "styles") as List<Object>;
                    if (list != null)
                    {
                        Reports.Clear();
                        foreach (object value in list)
                        {
                            ReportStyle rptStyle = new ReportStyle();
                            rptStyle.Param = value as Dictionary<string, object>;
                            Reports.Add(rptStyle);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show("加载KPI报表异常。" + e.Message);
            }
        }

        public static KPIReportManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new KPIReportManager();
                }
                return instance;
            }
        }

        public List<ReportStyle> Reports
        {
            get;
            set;
        }

        public bool IsReportCanEdit(ReportStyle report)
        {
            if (report != null)
            {
                return IsReportCanEdit(report.name);
            }
            return true;
        }
        public bool IsReportCanEdit(string reportName)
        {
#if PublicKpiEditLimit
            if (!string.IsNullOrEmpty(reportName) && reportName.StartsWith("public_", StringComparison.CurrentCultureIgnoreCase))
            {//public_类报表修改需要有管理员权限，而且需输入口令
                if (MasterCom.RAMS.Model.MainModel.GetInstance().User.DBID == -1)
                {
                    if (!isCanEditPublicReport)
                    {
                        isCanEditPublicReport = MasterCom.RAMS.Model.MainModel.CheckIsRightMastercomPwd("public类报表修改需输入口令验证");
                    }
                    return isCanEditPublicReport;
                }
                else
                {
                    System.Windows.Forms.MessageBox.Show("您没有权限修改公用报表");
                    return false;
                }
            }
#endif
            return true;
        }
    }
}
