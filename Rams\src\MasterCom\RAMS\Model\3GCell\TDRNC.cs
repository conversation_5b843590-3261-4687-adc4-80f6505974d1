using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class TDRNC
    {
        public TDRNC()
        {
        }

        public TDRNC(string name)
        {
            Name = name;
        }
        
        public string Code { get; set; } = string.Empty;

        public string Name { get; set; }

        public List<TDNodeB> BTSs
        {
            get { return btss; }
        }

        public void AddNodeB(TDNodeB bts)
        {
            btss.Add(bts);
            bts.BelongBSC = this;
        }
        public TDMSC BelongMSC { get; set; }

        private readonly List<TDNodeB> btss = new List<TDNodeB>();

        public static IComparer<TDRNC> GetCompareByName()
        {
            if (comparerByName == null)
            {
                comparerByName = new ComparerByName();
            }
            return comparerByName;
        }

        private static IComparer<TDRNC> comparerByName;

        public class ComparerByName : IComparer<TDRNC>
        {
            public int Compare(TDRNC x, TDRNC y)
            {
                return x.Name.CompareTo(y.Name);
            }
        }
    }
}
