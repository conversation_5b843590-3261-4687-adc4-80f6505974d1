﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LTEAntennaOverlapCoverageSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.minLevelValue = new DevExpress.XtraEditors.SpinEdit();
            this.maxLevelValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelMinLevel = new DevExpress.XtraEditors.LabelControl();
            this.labelMaxLevel = new DevExpress.XtraEditors.LabelControl();
            this.grpLevel = new DevExpress.XtraEditors.GroupControl();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.minLevelValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.maxLevelValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpLevel)).BeginInit();
            this.grpLevel.SuspendLayout();
            this.SuspendLayout();
            // 
            // minLevelValue
            // 
            this.minLevelValue.EditValue = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            this.minLevelValue.Location = new System.Drawing.Point(77, 38);
            this.minLevelValue.Name = "minLevelValue";
            this.minLevelValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.minLevelValue.Properties.Appearance.Options.UseFont = true;
            this.minLevelValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.minLevelValue.Properties.IsFloatValue = false;
            this.minLevelValue.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.minLevelValue.Properties.Mask.EditMask = "N00";
            this.minLevelValue.Size = new System.Drawing.Size(82, 20);
            this.minLevelValue.TabIndex = 5;
            // 
            // maxLevelValue
            // 
            this.maxLevelValue.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.maxLevelValue.Location = new System.Drawing.Point(234, 38);
            this.maxLevelValue.Name = "maxLevelValue";
            this.maxLevelValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.maxLevelValue.Properties.Appearance.Options.UseFont = true;
            this.maxLevelValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.maxLevelValue.Properties.IsFloatValue = false;
            this.maxLevelValue.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.maxLevelValue.Properties.Mask.EditMask = "N00";
            this.maxLevelValue.Size = new System.Drawing.Size(82, 20);
            this.maxLevelValue.TabIndex = 6;
            // 
            // labelMinLevel
            // 
            this.labelMinLevel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelMinLevel.Appearance.Options.UseFont = true;
            this.labelMinLevel.Location = new System.Drawing.Point(23, 41);
            this.labelMinLevel.Name = "labelMinLevel";
            this.labelMinLevel.Size = new System.Drawing.Size(48, 12);
            this.labelMinLevel.TabIndex = 7;
            this.labelMinLevel.Text = "最小值：";
            // 
            // labelMaxLevel
            // 
            this.labelMaxLevel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelMaxLevel.Appearance.Options.UseFont = true;
            this.labelMaxLevel.Location = new System.Drawing.Point(180, 41);
            this.labelMaxLevel.Name = "labelMaxLevel";
            this.labelMaxLevel.Size = new System.Drawing.Size(48, 12);
            this.labelMaxLevel.TabIndex = 8;
            this.labelMaxLevel.Text = "最大值：";
            // 
            // grpLevel
            // 
            this.grpLevel.Controls.Add(this.labelMinLevel);
            this.grpLevel.Controls.Add(this.maxLevelValue);
            this.grpLevel.Controls.Add(this.labelMaxLevel);
            this.grpLevel.Controls.Add(this.minLevelValue);
            this.grpLevel.Location = new System.Drawing.Point(12, 12);
            this.grpLevel.Name = "grpLevel";
            this.grpLevel.Size = new System.Drawing.Size(373, 83);
            this.grpLevel.TabIndex = 9;
            this.grpLevel.Text = "主覆盖小区电平区间";
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.Location = new System.Drawing.Point(310, 153);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 13;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(222, 153);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 12;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // LTEAntennaOverlapCoverageSetForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(397, 188);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.grpLevel);
            this.Name = "LTEAntennaOverlapCoverageSetForm";
            this.Text = "LTE重叠覆盖干扰分析";
            ((System.ComponentModel.ISupportInitialize)(this.minLevelValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.maxLevelValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpLevel)).EndInit();
            this.grpLevel.ResumeLayout(false);
            this.grpLevel.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit minLevelValue;
        private DevExpress.XtraEditors.SpinEdit maxLevelValue;
        private DevExpress.XtraEditors.LabelControl labelMinLevel;
        private DevExpress.XtraEditors.LabelControl labelMaxLevel;
        private DevExpress.XtraEditors.GroupControl grpLevel;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
    }
}