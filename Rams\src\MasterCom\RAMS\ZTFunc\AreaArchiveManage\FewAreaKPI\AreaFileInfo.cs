﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class AreaFileInfo
    {
        public AreaFileInfo(AreaBase area, List<FileInfo> list)
        {
            this.Area = area;
            this.Files = list;
        }
        public AreaFileInfo()
        {
            Files = new List<FileInfo>();
        }
        public List<FileInfo> Files { get; set; }
        public AreaBase Area { get; set; }

    }
}
