﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanInterfereStatForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.ListViewGroup listViewGroup1 = new System.Windows.Forms.ListViewGroup("ListViewGroup", System.Windows.Forms.HorizontalAlignment.Left);
            this.ctxDetails = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowCoverByCell = new System.Windows.Forms.ToolStripMenuItem();
            this.DownExcelToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPageOverCover = new System.Windows.Forms.TabPage();
            this.listViewOverCoverEs = new System.Windows.Forms.ListView();
            this.columnHeader2 = new System.Windows.Forms.ColumnHeader();
            this.tabCtrlStat = new System.Windows.Forms.TabControl();
            this.tabPageInjection = new System.Windows.Forms.TabPage();
            this.listViewInjection = new System.Windows.Forms.ListView();
            this.columnHeader1 = new System.Windows.Forms.ColumnHeader();
            this.label1 = new System.Windows.Forms.Label();
            this.listViewOverCoverCell = new System.Windows.Forms.ListView();
            this.columnHeaderSN = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderJamcellname = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderPairNum = new System.Windows.Forms.ColumnHeader();
            this.columnHeader3 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader4 = new System.Windows.Forms.ColumnHeader();
            this.ctxDetails.SuspendLayout();
            this.tabPageOverCover.SuspendLayout();
            this.tabCtrlStat.SuspendLayout();
            this.tabPageInjection.SuspendLayout();
            this.SuspendLayout();
            // 
            // ctxDetails
            // 
            this.ctxDetails.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowCoverByCell,
            this.DownExcelToolStripMenuItem});
            this.ctxDetails.Name = "ctxDetails";
            this.ctxDetails.Size = new System.Drawing.Size(153, 70);
            // 
            // miShowCoverByCell
            // 
            this.miShowCoverByCell.Name = "miShowCoverByCell";
            this.miShowCoverByCell.Size = new System.Drawing.Size(152, 22);
            this.miShowCoverByCell.Text = "显示小区覆盖";
            this.miShowCoverByCell.Click += new System.EventHandler(this.miShowCoverByCell_Click);
            // 
            // DownExcelToolStripMenuItem
            // 
            this.DownExcelToolStripMenuItem.Name = "DownExcelToolStripMenuItem";
            this.DownExcelToolStripMenuItem.Size = new System.Drawing.Size(152, 22);
            this.DownExcelToolStripMenuItem.Text = "导出Excel";
            this.DownExcelToolStripMenuItem.Click += new System.EventHandler(this.DownExcelToolStripMenuItem_Click);
            // 
            // tabPageOverCover
            // 
            this.tabPageOverCover.Controls.Add(this.listViewOverCoverEs);
            this.tabPageOverCover.Location = new System.Drawing.Point(4, 24);
            this.tabPageOverCover.Name = "tabPageOverCover";
            this.tabPageOverCover.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageOverCover.Size = new System.Drawing.Size(864, 194);
            this.tabPageOverCover.TabIndex = 0;
            this.tabPageOverCover.Text = "过覆盖评估";
            this.tabPageOverCover.UseVisualStyleBackColor = true;
            // 
            // listViewOverCoverEs
            // 
            this.listViewOverCoverEs.AutoArrange = false;
            this.listViewOverCoverEs.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader2});
            this.listViewOverCoverEs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewOverCoverEs.FullRowSelect = true;
            this.listViewOverCoverEs.GridLines = true;
            this.listViewOverCoverEs.HideSelection = false;
            this.listViewOverCoverEs.LabelWrap = false;
            this.listViewOverCoverEs.Location = new System.Drawing.Point(3, 3);
            this.listViewOverCoverEs.Name = "listViewOverCoverEs";
            this.listViewOverCoverEs.ShowGroups = false;
            this.listViewOverCoverEs.Size = new System.Drawing.Size(858, 188);
            this.listViewOverCoverEs.TabIndex = 0;
            this.listViewOverCoverEs.UseCompatibleStateImageBehavior = false;
            this.listViewOverCoverEs.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "片区名/时间";
            this.columnHeader2.Width = 120;
            // 
            // tabCtrlStat
            // 
            this.tabCtrlStat.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.tabCtrlStat.Appearance = System.Windows.Forms.TabAppearance.FlatButtons;
            this.tabCtrlStat.Controls.Add(this.tabPageOverCover);
            this.tabCtrlStat.Controls.Add(this.tabPageInjection);
            this.tabCtrlStat.Location = new System.Drawing.Point(-1, 288);
            this.tabCtrlStat.Name = "tabCtrlStat";
            this.tabCtrlStat.SelectedIndex = 0;
            this.tabCtrlStat.Size = new System.Drawing.Size(872, 222);
            this.tabCtrlStat.TabIndex = 1;
            // 
            // tabPageInjection
            // 
            this.tabPageInjection.Controls.Add(this.listViewInjection);
            this.tabPageInjection.Location = new System.Drawing.Point(4, 24);
            this.tabPageInjection.Name = "tabPageInjection";
            this.tabPageInjection.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageInjection.Size = new System.Drawing.Size(864, 194);
            this.tabPageInjection.TabIndex = 1;
            this.tabPageInjection.Text = "渗透率";
            this.tabPageInjection.UseVisualStyleBackColor = true;
            // 
            // listViewInjection
            // 
            this.listViewInjection.AllowColumnReorder = true;
            this.listViewInjection.AutoArrange = false;
            this.listViewInjection.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1});
            this.listViewInjection.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewInjection.FullRowSelect = true;
            this.listViewInjection.GridLines = true;
            listViewGroup1.Header = "ListViewGroup";
            listViewGroup1.Name = "片区名时间";
            this.listViewInjection.Groups.AddRange(new System.Windows.Forms.ListViewGroup[] {
            listViewGroup1});
            this.listViewInjection.HideSelection = false;
            this.listViewInjection.LabelWrap = false;
            this.listViewInjection.Location = new System.Drawing.Point(3, 3);
            this.listViewInjection.Name = "listViewInjection";
            this.listViewInjection.ShowGroups = false;
            this.listViewInjection.Size = new System.Drawing.Size(858, 188);
            this.listViewInjection.TabIndex = 0;
            this.listViewInjection.UseCompatibleStateImageBehavior = false;
            this.listViewInjection.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "片区名/时间";
            this.columnHeader1.Width = 105;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(0, 2);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(125, 12);
            this.label1.TabIndex = 8;
            this.label1.Text = "扫频过覆盖摘要情况：";
            // 
            // listViewOverCoverCell
            // 
            this.listViewOverCoverCell.AllowColumnReorder = true;
            this.listViewOverCoverCell.AutoArrange = false;
            this.listViewOverCoverCell.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSN,
            this.columnHeaderJamcellname,
            this.columnHeaderPairNum,
            this.columnHeader3,
            this.columnHeader4});
            this.listViewOverCoverCell.FullRowSelect = true;
            this.listViewOverCoverCell.GridLines = true;
            this.listViewOverCoverCell.HideSelection = false;
            this.listViewOverCoverCell.LabelWrap = false;
            this.listViewOverCoverCell.Location = new System.Drawing.Point(2, 17);
            this.listViewOverCoverCell.Name = "listViewOverCoverCell";
            this.listViewOverCoverCell.ShowGroups = false;
            this.listViewOverCoverCell.Size = new System.Drawing.Size(869, 271);
            this.listViewOverCoverCell.TabIndex = 7;
            this.listViewOverCoverCell.UseCompatibleStateImageBehavior = false;
            this.listViewOverCoverCell.View = System.Windows.Forms.View.Details;
            this.listViewOverCoverCell.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listViewOverCoverCell_MouseClick);
            // 
            // columnHeaderSN
            // 
            this.columnHeaderSN.Text = "序号";
            this.columnHeaderSN.Width = 68;
            // 
            // columnHeaderJamcellname
            // 
            this.columnHeaderJamcellname.Text = "过覆盖小区名";
            this.columnHeaderJamcellname.Width = 97;
            // 
            // columnHeaderPairNum
            // 
            this.columnHeaderPairNum.Text = "干扰小区个数";
            this.columnHeaderPairNum.Width = 92;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "被干扰小区名";
            this.columnHeader3.Width = 486;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "干扰小区所属片区";
            this.columnHeader4.Width = 116;
            // 
            // ZTScanInterfereStatForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.ActiveCaptionText;
            this.ClientSize = new System.Drawing.Size(872, 511);
            this.Controls.Add(this.tabCtrlStat);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.listViewOverCoverCell);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "ZTScanInterfereStatForm";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "扫频过覆盖统计";
            this.ctxDetails.ResumeLayout(false);
            this.tabPageOverCover.ResumeLayout(false);
            this.tabCtrlStat.ResumeLayout(false);
            this.tabPageInjection.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxDetails;
        private System.Windows.Forms.ToolStripMenuItem miShowCoverByCell;
        private System.Windows.Forms.TabPage tabPageOverCover;
        private System.Windows.Forms.TabControl tabCtrlStat;
        private System.Windows.Forms.TabPage tabPageInjection;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ListView listViewOverCoverCell;
        private System.Windows.Forms.ColumnHeader columnHeaderSN;
        private System.Windows.Forms.ColumnHeader columnHeaderJamcellname;
        private System.Windows.Forms.ColumnHeader columnHeaderPairNum;
        private System.Windows.Forms.ListView listViewInjection;
        private System.Windows.Forms.ListView listViewOverCoverEs;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private System.Windows.Forms.ColumnHeader columnHeader4;
        private System.Windows.Forms.ToolStripMenuItem DownExcelToolStripMenuItem;
    }
}