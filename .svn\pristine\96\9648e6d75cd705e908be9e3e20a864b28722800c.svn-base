﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class TestPointBlock
    {
        private readonly List<TestPoint> testPoints = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }
        public int TestPointCount
        {
            get { return testPoints.Count; }
        }
        public bool Overlap { get; set; } = false;
        
        public string Grid { get; set; } = "";

        public double this[TPBlockDisplayColumn col]
        {
            get
            {
                double retValue = double.NaN;
                if (col.ValueType == ESummaryValueType.Average)
                {
                    double sum = 0;
                    int cnt = 0;
                    if (paramTotalValueDic.TryGetValue(col.Param<PERSON>ey, out sum) && paramCountDic.TryGetValue(col.ParamKey, out cnt))
                    {
                        return Math.Round(sum / cnt, 2);
                    }
                }
                else if (col.ValueType == ESummaryValueType.Max)
                {
                    double max = 0;
                    if (paramMaxValueDic.TryGetValue(col.ParamKey, out max))
                    {
                        return max;
                    }
                }
                else if (col.ValueType == ESummaryValueType.Min)
                {
                    double min = 0;
                    if (paramMinValueDic.TryGetValue(col.ParamKey, out min))
                    {
                        return min;
                    }
                }
                return retValue;
            }
        }
        private readonly Dictionary<string, int> paramCountDic = new Dictionary<string, int>();
        private readonly Dictionary<string, double> paramMinValueDic = new Dictionary<string, double>();
        private readonly Dictionary<string, double> paramMaxValueDic = new Dictionary<string, double>();
        private readonly Dictionary<string, double> paramTotalValueDic = new Dictionary<string, double>();

        /// <summary>
        /// 收集一采样点并计算保存该点的相关指标信息
        /// </summary>
        /// <param name="tp"></param>
        /// <param name="displayCols"></param>
        public void CollectPoint(TestPoint tp, List<TPBlockDisplayColumn> displayCols)
        {
            testPoints.Add(tp);
            List<string> paramsPast = new List<string>();
            foreach (TPBlockDisplayColumn col in displayCols)
            {
                string paramKey = col.ParamKey;
                if (paramsPast.Contains(paramKey))
                {//同一指标，可能有不同列（极值or平均值），只需要取一次指标即可
                    continue;
                }
                else
                {
                    paramsPast.Add(paramKey);
                }
                double dValue;
                object objValue = tp[col.DisplayParam.ParamInfo.Name, col.ParamArrayIndex];
                if (objValue != null && double.TryParse(objValue.ToString(), out dValue))
                {
                    saveTestPointParamValue(col, dValue);
                }
            }
        }

        private void saveTestPointParamValue(TPBlockDisplayColumn col, double value)
        {
            if (col.DisplayParam.ValueMin > value || col.DisplayParam.ValueMax < value)
            {//不在有效值域內，过滤掉
                return;
            }
            string nameKey = col.ParamKey;
            //采样点个数，指标总和值处理，为求平均做准备
            if (paramCountDic.ContainsKey(nameKey))
            {
                paramCountDic[nameKey]++;
                paramTotalValueDic[nameKey] += value;
            }
            else
            {
                paramCountDic.Add(nameKey, 1);
                paramTotalValueDic.Add(nameKey, value);
            }

            double min;
            if (paramMinValueDic.TryGetValue(nameKey, out min))
            {
                paramMinValueDic[nameKey] = Math.Min(min, value);
            }
            else
            {
                paramMinValueDic.Add(nameKey, value);
            }

            double max;
            if (paramMaxValueDic.TryGetValue(nameKey, out max))
            {
                paramMaxValueDic[nameKey] = Math.Max(max, value);
            }
            else
            {
                paramMaxValueDic.Add(nameKey, value);
            }
        }

        internal void Join(TestPointBlock block)
        {
            testPoints.AddRange(block.testPoints);
            foreach (string key in block.paramCountDic.Keys)
            {
                if (paramCountDic.ContainsKey(key))
                {
                    paramCountDic[key] += block.paramCountDic[key];
                    paramTotalValueDic[key] += block.paramTotalValueDic[key];
                    paramMinValueDic[key] = Math.Min(paramMinValueDic[key], block.paramMinValueDic[key]);
                    paramMaxValueDic[key] = Math.Max(paramMaxValueDic[key], block.paramMaxValueDic[key]);
                }
                else
                {
                    paramCountDic.Add(key, block.paramCountDic[key]);
                    paramTotalValueDic.Add(key, block.paramTotalValueDic[key]);
                    paramMinValueDic.Add(key, block.paramMinValueDic[key]);
                    paramMaxValueDic.Add(key, block.paramMaxValueDic[key]);
                }
            }
        }

        internal bool Within(double x1, double y1, double x2, double y2)
        {
            foreach (TestPoint tp in testPoints)
            {
                if (tp.Longitude >= x1 && tp.Longitude <= x2 && tp.Latitude >= y1 && tp.Latitude <= y2)
                {
                    return true;
                }
            }
            return false;
        }
        /// <summary>
        /// 以tp的经纬度坐标为圆心，作半径为radius的圆，与已有采样点的同半径圆作相交判断。
        /// 如果能与任一采样点的圆相交，则视为可以汇聚，否则视为不可以汇聚。
        /// </summary>
        /// <param name="tp"></param>
        /// <param name="radius"></param>
        /// <returns></returns>
        public bool CanCollect(TestPoint tp, double radius)
        {
            double d = 2 * radius;
            double lngSpan = d * 0.00001;//直径（米）对应的经度跨度
            double latSpan = d * 0.00001;//直径（米）对应的纬度跨度
            bool ret = false;
            foreach (TestPoint tpBlock in testPoints)
            {//距离判断优化，先进行判断经纬度差值判断（快），再转化成米单位的距离判断。
                if (Math.Abs(tpBlock.Longitude - tp.Longitude) <= lngSpan
                    && Math.Abs(tpBlock.Latitude - tp.Latitude) <= latSpan)
                {
                    return true;
                }
                if (tp.Distance2(tpBlock) <= d)
                {
                    return true;
                }
            }
            return ret;
        }



    }
}
