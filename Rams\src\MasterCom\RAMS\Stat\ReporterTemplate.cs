﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace MasterCom.RAMS.Stat
{
    /// <summary>
    /// 一个报表样式
    /// </summary>
    public class ReporterTemplate
    {
        public TimeFormatHelper timeFormat { get; set; } = new TimeFormatHelper();
        public string Name { get; set; } = "未命名";
        public int KeyCount { get; set; } = 1;//关键列的维度 几个维度的前后组合唯一


        public List<ColumnSet> Columns { get; set; } = new List<ColumnSet>();
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Name"] = Name;
                param["KeyCount"] = KeyCount;
                param["TimeShowType"] = timeFormat.FormatType;
                List<object> columnsParams = new List<object>();
                param["Columns"] = columnsParams;
                foreach (ColumnSet cs in Columns)
                {
                    columnsParams.Add(cs.Param);
                }
                return param;
            }
            set
            {
                Name = (String)value["Name"];
                KeyCount = (int)value["KeyCount"];
                timeFormat.FormatType = 0;//default
                object obj = 0;
                if (value.TryGetValue("TimeShowType", out obj))
                {
                    timeFormat.FormatType = (int)obj;
                }
                Columns.Clear();
                List<object> columnsParams = (List<object>)value["Columns"];
                foreach (object o in columnsParams)
                {
                    Dictionary<string, object> columnParam = (Dictionary<string, object>)o;
                    ColumnSet cs = new ColumnSet();
                    cs.Param = columnParam;
                    Columns.Add(cs);
                }
            }
        }
        public override string ToString()
        {
            return Name;
        }

        public bool IsSeparateByServiceID
        {
            get
            {
                foreach (ColumnSet col in Columns)
                {
                    if (col.ServiceIDSet.Count > 0)
                    {
                        return true;
                    }
                }
                return false;
            }
        }

        public List<int> CarrierIDs
        {
            get
            {
                List<int> ids = new List<int>();
                foreach (ColumnSet cs in this.Columns)
                {
                    if (!ids.Contains(cs.carrierId))
                    {
                        ids.Add(cs.carrierId);
                    }
                }
                return ids;
            }
        }

        public ReportStyle SaveAsKpiRpt()
        {
            ReportStyle rptStyle = new ReportStyle();
            rptStyle.name = Name;
            rptStyle.graphs = new List<GraphEntity>();
            rptStyle.stylename = "";
            rptStyle.Visible = true;
            rptStyle.rptColInfos = new List<ColumnInfo>();
            foreach (ColumnSet cs in this.Columns)
            {
                RptCell titleCell = new RptCell();
                titleCell.bkColor = Color.Cyan;
                titleCell.carrierID = (byte)cs.carrierId;
                titleCell.colAt = this.Columns.IndexOf(cs);
                titleCell.exp = cs.Title;
                titleCell.foreColor = Color.Black;
                titleCell.momt = (byte)cs.momt;
                titleCell.DynamicBKColorParams = null;
                titleCell.rowAt = 0;
                rptStyle.rptCellList.Add(titleCell);
                rptStyle.rptColWidths.Add(100);

                RptCell rptCell = new RptCell();
                rptCell.bkColor = Color.White;
                rptCell.carrierID = (byte)cs.carrierId;
                rptCell.colAt = this.Columns.IndexOf(cs);
                rptCell.exp = cs.Exp;
                rptCell.foreColor = Color.Black;
                rptCell.momt = (byte)cs.momt;
                rptCell.DynamicBKColorParams = null;
                rptCell.rowAt = 1;
                rptCell.ServiceIDSet = cs.ServiceIDSet;
                rptStyle.rptCellList.Add(rptCell);

            }
            return rptStyle;
        }
    }

    public class ReporterTemplateManager
    {
        protected ReporterTemplateManager()
        {

        }

        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public static ReporterTemplate LoadSingleReportFromFile(string titleHead)
        {
            ReporterTemplate rptTemplate = null;
            try
            {
                string path = string.Format(Application.StartupPath + "/config/templates");
                if (System.IO.Directory.Exists(path))
                {
                    rptTemplate = getSingleRptTemplate(titleHead, rptTemplate, path);
                }
                else
                {
                    return rptTemplate;
                }
            }
            catch (Exception e)
            {
                log.Warn("读入统计报表模板文件发生错误:" + e.Message);
            }
            return rptTemplate;
        }

        private static ReporterTemplate getSingleRptTemplate(string titleHead, ReporterTemplate rptTemplate, string path)
        {
            System.IO.DirectoryInfo dinfo = new System.IO.DirectoryInfo(path);
            System.IO.FileInfo[] fileList = dinfo.GetFiles(titleHead + "*", System.IO.SearchOption.TopDirectoryOnly);
            foreach (System.IO.FileInfo file in fileList)
            {
                try
                {
                    rptTemplate = getRptTemplate(titleHead, rptTemplate, file);
                }
                catch (System.Exception ex)
                {
                    MessageBox.Show("报表加载异常：" + file.FullName + " 异常信息：" + ex.ToString());
                    break;
                }
                if (rptTemplate != null)
                {
                    break;
                }
            }

            return rptTemplate;
        }

        private static ReporterTemplate getRptTemplate(string titleHead, ReporterTemplate rptTemplate, System.IO.FileInfo file)
        {
            XmlConfigFile configFile = new XmlConfigFile(file.FullName);
            List<object> list = configFile.GetItemValue("StatReports", "reports", getItemValue) as List<object>;
            if (list != null)
            {
                foreach (object o in list)
                {
                    if (o != null)
                    {
                        rptTemplate = o as ReporterTemplate;
                        rptTemplate.Name = file.Name.Replace(titleHead, "").Replace(".xml", "");
                        break;
                    }
                }
            }

            return rptTemplate;
        }

        /// <summary>
        /// 以本titleHead开头的报表只保留这一个(其他的会被清除)
        /// </summary>
        /// <param name="titleHead"></param>
        /// <param name="rptTemplate"></param>
        public static void SaveSingleReportTemplate(string titleHead, ReporterTemplate rptTemplate)
        {
            if (string.IsNullOrEmpty(titleHead) || rptTemplate == null)
            {
                return;
            }
            SaveReportTemplate(titleHead, new List<ReporterTemplate> { rptTemplate });
        }
        public static List<ReporterTemplate> LoadReportFromFile(string titleHead)
        {
            List<ReporterTemplate> rptTemplates = new List<ReporterTemplate>();
            try
            {
                if (titleHead == "")
                {
                    return rptTemplates;
                }
                if (System.IO.Directory.Exists(string.Format(Application.StartupPath + "/config/templates")))
                {
                    System.IO.DirectoryInfo dinfo = new System.IO.DirectoryInfo(string.Format(Application.StartupPath + "/config/templates"));
                    foreach (System.IO.FileInfo file in dinfo.GetFiles(titleHead + "*", System.IO.SearchOption.TopDirectoryOnly))
                    {
                        try
                        {
                            addRptTemplates(titleHead, rptTemplates, file);
                        }
                        catch (System.Exception ex)
                        {
                            MessageBox.Show("报表加载异常：" + file.FullName + " 异常信息：" + ex.ToString());
                        }
                    }
                }
                else
                {
                    return rptTemplates;
                }
            }
            catch (Exception e)
            {
                log.Warn("读入统计报表模板文件发生错误:" + e.Message);
            }
            return rptTemplates;
        }

        private static void addRptTemplates(string titleHead, List<ReporterTemplate> rptTemplates, System.IO.FileInfo file)
        {
            XmlConfigFile configFile = new XmlConfigFile(file.FullName);
            List<object> list = configFile.GetItemValue("StatReports", "reports", getItemValue) as List<object>;
            if (list != null)
            {
                foreach (object o in list)
                {
                    if (o != null)
                    {
                        ReporterTemplate rpttpl = o as ReporterTemplate;
                        if (list.Count == 1)
                            rpttpl.Name = file.Name.Replace(titleHead, "").Replace(".xml", "");
                        rptTemplates.Add(rpttpl);
                    }
                }
            }
        }

        private static object getItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(ReporterTemplate).Name))
            {
                ReporterTemplate rpt = new ReporterTemplate();
                Dictionary<string, object> dic = configFile.GetItemValue(item, "Param", DTDisplayParameterInfo.GetItemValue) as Dictionary<string, object>;
                if (dic == null)
                {
                    return null;
                }
                rpt.Param = dic;
                return rpt;
            }
            return null;
        }

        public static void SaveReportTemplate(string titleHead, List<ReporterTemplate> rptTemplates)
        {
            if (string.IsNullOrEmpty(titleHead) || rptTemplates == null)
            {
                return;
            }
            System.IO.DirectoryInfo directory = new System.IO.DirectoryInfo(string.Format(Application.StartupPath + "/config/templates"));
            foreach (System.IO.FileInfo file in directory.GetFiles(titleHead + "*", System.IO.SearchOption.TopDirectoryOnly))
            {
                file.Delete();
            }
            foreach (ReporterTemplate rpttpl in rptTemplates)
            {
                List<ReporterTemplate> rpttpls = new List<ReporterTemplate>();
                rpttpls.Add(rpttpl);
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement config = configFile.AddConfig("StatReports");
                configFile.AddItem(config, "reports", rpttpls, addItem);
                configFile.Save(string.Format(Application.StartupPath + "/config/templates/" + titleHead + rpttpl.Name + ".xml"));
            }
        }

        private static XmlElement addItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is ReporterTemplate)
            {
                ReporterTemplate rpt = value as ReporterTemplate;
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Param", rpt.Param);
                return item;
            }
            else if (value is ColumnSet)
            {
                ColumnSet cs = value as ColumnSet;
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Param", cs.Param);
                return item;
            }
            else if (value is HiLighter)
            {
                HiLighter hi = value as HiLighter;
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Param", hi.Param);
                return item;
            }
            return null;
        }
    }
}
