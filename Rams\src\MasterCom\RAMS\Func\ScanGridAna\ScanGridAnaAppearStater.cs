﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

namespace MasterCom.RAMS.Func
{
    public class ScanGridAnaAppearStater
    {
        private readonly ScanGridAnaResult anaResult;
        private readonly ScanGridAnaResult cmpResult;
        private readonly DataTable[] dts;

        public ScanGridAnaAppearStater(ScanGridAnaResult anaResult, ScanGridAnaResult cmpResult)
        {
            this.anaResult = anaResult;
            this.cmpResult = cmpResult;

            dts = new DataTable[4];
            for (int i = 0; i < 4; ++i)
            {
                dts[i] = new DataTable();
            }
        }

        public void Stat()
        {
            if (cmpResult == null)
            {
                return;
            }

            List<ScanGridAnaRegionInfo> anaRegions = new List<ScanGridAnaRegionInfo>(anaResult.RegionList);
            List<ScanGridAnaRegionInfo> cmpRegions = new List<ScanGridAnaRegionInfo>(cmpResult.RegionList);
            anaRegions.Sort();
            cmpRegions.Sort();
            for (int i = 0; i < 4; ++i)
            {
                List<RegionCompareInfo> rcInfoList = new List<RegionCompareInfo>();
                PrepareColumns(dts[i]);

                for (int j = 0; j < anaRegions.Count; ++j)
                {
                    RegionCompareInfo rcInfo = new RegionCompareInfo();
                    rcInfo.Stat(anaRegions[j], cmpRegions[j], (ScanGridAnaGridType)i);
                    dts[i].Rows.Add(rcInfo.GetObjectRow());
                    rcInfoList.Add(rcInfo);
                }

                RegionCompareInfo summaryInfo = GetSummaryInfo(rcInfoList);
                dts[i].Rows.Add(summaryInfo.GetObjectRow());
            }
        }

        public List<DataTable> GetResult(ScanGridAnaGridType netType)
        {
            List<DataTable> retList = new List<DataTable>();
            retList.Add(dts[(int)netType]);
            return retList;
        }

        private void PrepareColumns(DataTable dt)
        {
            dt.Columns.Add("网格ID", typeof(string));
            dt.Columns.Add("当前时间段", typeof(int));
            dt.Columns.Add("对比时间段", typeof(int));
            dt.Columns.Add("共同出现", typeof(int));
            dt.Columns.Add("当前时间段独有", typeof(int));
            dt.Columns.Add("对比时间段独有", typeof(int));
            dt.Columns.Add("测试深度", typeof(double));
        }

        private RegionCompareInfo GetSummaryInfo(List<RegionCompareInfo> rcInfoList)
        {
            RegionCompareInfo rcInfo = new RegionCompareInfo();
            rcInfo.RegionName = "汇总";
            foreach (RegionCompareInfo info in rcInfoList)
            {
                rcInfo.GridType = info.GridType;
                rcInfo.SrcGridCount += info.SrcGridCount;
                rcInfo.TarGridCount += info.TarGridCount;
                rcInfo.SameGridCount += info.SameGridCount;
                rcInfo.ReduceGridCount += info.ReduceGridCount;
                rcInfo.RaiseGridCount += info.RaiseGridCount;
            }
            rcInfo.CompareDepth = rcInfo.TarGridCount + rcInfo.ReduceGridCount == 0 ?
                0 : rcInfo.SrcGridCount * 1.0 / (rcInfo.TarGridCount + rcInfo.ReduceGridCount);
            return rcInfo;
        }
    }

    /// <summary>
    /// 网格对应的一条对比列表信息
    /// </summary>
    public class RegionCompareInfo
    {
        public ScanGridAnaGridType GridType
        {
            get;
            set;
        }
        public string RegionName
        {
            get;
            set;
        }
        /// <summary>
        /// 时段一栅格数
        /// </summary>
        public int SrcGridCount
        {
            get;
            set;
        }
        /// <summary>
        /// 时段二栅格数
        /// </summary>
        public int TarGridCount
        {
            get;
            set;
        }
        /// <summary>
        /// 同时出现栅格数
        /// </summary>
        public int SameGridCount
        {
            get;
            set;
        }
        /// <summary>
        /// 时段一消失的栅格数
        /// </summary>
        public int ReduceGridCount
        {
            get;
            set;
        }
        /// <summary>
        /// 时段二新增的栅格数
        /// </summary>
        public int RaiseGridCount
        {
            get;
            set;
        }
        public double CompareDepth
        {
            get;
            set;
        }

        /// <summary>
        /// 根据网络类别对比两个相同网格中的栅格情况
        /// </summary>
        /// <param name="srcRegion"></param>
        /// <param name="tarRegion"></param>
        /// <param name="gridType"></param>
        public void Stat(ScanGridAnaRegionInfo srcRegion, ScanGridAnaRegionInfo tarRegion, ScanGridAnaGridType gridType)
        {
            this.srcRegion = srcRegion;
            this.tarRegion = tarRegion;
            this.GridType = gridType;
            RegionName = srcRegion.RegionName;

            Dictionary<string, int> srcGridDic = GetExistedDic(srcRegion, gridType);
            Dictionary<string, int> tarGridDic = GetExistedDic(tarRegion, gridType);
            SrcGridCount = srcGridDic.Count;
            TarGridCount = tarGridDic.Count;

            SameGridCount = RaiseGridCount = 0;
            foreach (string key in tarGridDic.Keys)
            {
                if (srcGridDic.ContainsKey(key))
                {
                    ++SameGridCount;
                }
                else
                {
                    ++RaiseGridCount;
                }
            }
            ReduceGridCount = SrcGridCount - SameGridCount;
            //CompareDepth = SrcGridCount + RaiseGridCount == 0 ? 0 : TarGridCount * 1.0 / (SrcGridCount + RaiseGridCount);
            CompareDepth = TarGridCount + ReduceGridCount == 0 ? 0 : SrcGridCount * 1.0 / (TarGridCount + ReduceGridCount);
        }

        public object[] GetObjectRow()
        {
            return new object[] { RegionName, SrcGridCount, TarGridCount, SameGridCount, ReduceGridCount, RaiseGridCount, CompareDepth };
        }

        /// <summary>
        /// 获取区域中特定网络类型的已存在的栅格
        /// </summary>
        /// <param name="region"></param>
        /// <param name="gridType"></param>
        /// <returns></returns>
        private Dictionary<string, int> GetExistedDic(ScanGridAnaRegionInfo region, ScanGridAnaGridType gridType)
        {
            Dictionary<string, int> existedDic = new Dictionary<string, int>();
            foreach (ScanGridAnaGridInfo grid in region.GridList)
            {
                if (gridType != grid.GridType)
                {
                    continue;
                }
                if (existedDic.ContainsKey(grid.MGRSGridString))
                {
                    continue;
                }
                existedDic.Add(grid.MGRSGridString, 0);
            }
            return existedDic;
        }

        private ScanGridAnaRegionInfo srcRegion { get; set; }
        private ScanGridAnaRegionInfo tarRegion { get; set; }
    }
}
