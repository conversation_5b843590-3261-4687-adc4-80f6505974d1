﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.ZTCellWrongDir
{
    public class CellWrongDirItem_Scan
    {
        public CellWrongDirItem_Scan(ICell cell)
        {
            this.Cell = cell;
            WrongTestPoints = new List<TestPoint>();
        }
        public ICell Cell
        {
            get;
            private set;
        }

        public int CellID
        {
            get { return ((LTECell)Cell).SCellID; }
        }

        public int TAC
        {
            get { return ((LTECell)Cell).TAC; }
        }

        public int ECI
        {
            get { return ((LTECell)Cell).ECI; }
        }

        public int EARFCN
        {
            get { return ((LTECell)Cell).EARFCN; }
        }
        public int PCI
        {
            get { return ((LTECell)Cell).PCI; }
        }

        public short DirectionCfg
        {
            get { return ((LTECell)Cell).Direction; }
        }

        public string CellName
        {
            get { return Cell.Name; }
        }

        double maxLongitude = 0;
        double minLongitude = 99999;
        public double LongitudeMid
        {
            get
            {
                if (maxLongitude != 0 && minLongitude != 99999)
                {
                    return Math.Round((maxLongitude + minLongitude) / 2, 7);
                }
                return 0;
            }
        }

        double maxLatitude = 0;
        double minLatitude = 99999;
        public double LatitudeMid
        {
            get
            {
                if (maxLatitude != 0 && minLatitude != 99999)
                {
                    return Math.Round((maxLatitude + minLatitude) / 2, 7);
                }
                return 0;
            }
        }
        private readonly List<string> fileNameList = new List<string>();
        public string FileName
        {
            get
            {
                if (fileNameList != null)
                {
                    StringBuilder strb = new StringBuilder();
                    foreach (string str in fileNameList)
                    {
                        strb.Append(str + ",");
                    }
                    if (strb.Length > 1)
                    {
                        return strb.Remove(strb.Length - 1, 1).ToString();
                    }
                }
                return "";
            }
        }
        public List<TestPoint> WrongTestPoints
        {
            get;
            private set;
        }
        public int WrongPntCnt { get; set; }

        public int TotalPntCnt
        {
            get;
            private set;
        }

        public int GoodPntCnt
        {
            get { return TotalPntCnt - WrongPntCnt; }
        }

        public int WrongMCellCnt
        {
            get;
            private set;
        }
        public int WrongNoneMCellCnt
        {
            get { return WrongPntCnt - WrongMCellCnt; }
        }

        private float wrongRxLevMin = float.MaxValue;
        public float WrongRxLevMin
        {
            get { return wrongRxLevMin; }
        }

        private float wrongRxLevMax = float.MinValue;
        public float WrongRxLevMax
        {
            get { return wrongRxLevMax; }
        }

        private float totalRxLev = 0;
        public float WrongRxLevAvg
        {
            get
            {
                return (float)Math.Round(1.0 * totalRxLev / WrongPntCnt, 2);
            }
        }

        public double WrongRate
        {
            get { return Math.Round(100.0 * WrongPntCnt / TotalPntCnt, 2); }
        }

        public int WrongDirMean
        { get; private set; }

        public int WrongDirMax
        { get; private set; }

        public int WrongDirMin
        { get; private set; }

        public int DirDiff
        {
            get;
            private set;
        }

        int iStartTime = 0;
        int iEndTime = 0;
        public void CalcWrongDir()
        {
            GetMaxAndMinWrongDir();
            GetMeanAndDiffWrongDir();
        }
        public void GetMeanAndDiffWrongDir()
        {
            int diffDir = WrongDirMax - WrongDirMin;
            if (diffDir > 180)
            {
                double meanDir = (360 - diffDir) / 2.0;
                if (meanDir > WrongDirMin)
                {
                    WrongDirMean = (int)(360 - (meanDir - WrongDirMin));
                }
                else
                {
                    WrongDirMean = (int)(WrongDirMin - meanDir);
                }
            }
            else
            {
                double halfDir = diffDir / 2.0;
                WrongDirMean = (short)(WrongDirMin + halfDir);
            }

            DirDiff = Math.Abs(WrongDirMean - this.DirectionCfg);
            DirDiff = DirDiff > 180 ? 360 - DirDiff : DirDiff;
        }

        public void GetMaxAndMinWrongDir()
        {
            WrongDirMax = -1;
            WrongDirMin = 361;

            foreach (TestPoint tp in WrongTestPoints)
            {
                int dir = MathFuncs.getAngleFromPointToPoint(this.Cell.Longitude, this.Cell.Latitude
                    , tp.Longitude, tp.Latitude);
                WrongDirMin = Math.Min(dir, WrongDirMin);
                WrongDirMax = Math.Max(dir, WrongDirMax);

                if (maxLongitude < tp.Longitude)
                {
                    maxLongitude = tp.Longitude;
                }
                if (minLongitude > tp.Longitude && tp.Longitude != 0)
                {
                    minLongitude = tp.Longitude;
                }
                if (maxLatitude < tp.Latitude)
                {
                    maxLatitude = tp.Latitude;
                }
                if (minLatitude > tp.Latitude && tp.Longitude != 0)
                {
                    minLatitude = tp.Latitude;
                }
            }
        }

        /// <summary>
        /// 添加采样点信息。如果是正常覆盖的采样点，只记录个数。
        /// </summary>
        /// <param name="tp">采样点</param>
        /// <param name="rxlev">场强</param>
        /// <param name="wrongPnt">是否为异常覆盖点</param>
        /// <param name="cellIdx">是否为作为该点的主强小区</param>
        public void AddPoint(TestPoint tp, float rxLev, bool isWrongPnt, bool isMainCell)
        {
            TotalPntCnt++;
            if (iStartTime == 0)
            {
                iStartTime = tp.Time;
            }
            iEndTime = tp.Time;

            if (!isWrongPnt)
            {
                return;
            }
            WrongTestPoints.Add(tp);
            WrongPntCnt++;
            totalRxLev += rxLev;
            wrongRxLevMin = Math.Min(wrongRxLevMin, rxLev);
            wrongRxLevMax = Math.Max(wrongRxLevMax, rxLev);
            if (isMainCell)
            {
                WrongMCellCnt++;
            }
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            this.GetMaxAndMinWrongDir();
            BackgroundResult bgResult = new BackgroundResult(); 
            bgResult.CellType = BackgroundCellType.LTE;
            bgResult.CellIDDesc = this.CellID.ToString();
            bgResult.StrDesc = this.CellName;
            bgResult.LAC = TAC;
            bgResult.CI = ECI;
            bgResult.BCCH = this.EARFCN;
            bgResult.BSIC = this.PCI;
            bgResult.ISTime = this.iStartTime;
            bgResult.IETime = this.iEndTime;
            bgResult.LongitudeMid = LongitudeMid;
            bgResult.LatitudeMid = LatitudeMid;

            bgResult.SampleCount = this.WrongPntCnt;//异常点个数
            bgResult.RxLevMean = this.WrongRxLevAvg;
            bgResult.RxLevMax = this.WrongRxLevMax;
            bgResult.RxLevMin = this.WrongRxLevMin;

            bgResult.AddImageValue(WrongDirMax);
            bgResult.AddImageValue(WrongDirMin);
            bgResult.AddImageValue(TotalPntCnt);
            bgResult.AddImageValue(WrongMCellCnt);//主强异常点个数
            bgResult.AddImageValue(DirectionCfg);//工参方向角

            //bgResult.AddImageValue(WrongDirMean);//判断方位角
            //bgResult.AddImageValue(DirDiff);//两角差值
            //bgResult.AddImageValue(GoodPntCnt);//正常点个数
            //bgResult.AddImageValue((float)WrongRate);//异常百分比(%)
            //bgResult.AddImageValue(WrongNoneMCellCnt);//非主强异常点个数
            return bgResult;
        }
        public void AddInfoFromBackgroundResult(BackgroundResult bgResult)
        {
            this.maxLongitude = Math.Max(this.maxLongitude, bgResult.LongitudeMid);
            this.minLongitude = Math.Min(this.minLongitude, bgResult.LongitudeMid);
            this.maxLatitude = Math.Max(this.maxLatitude, bgResult.LatitudeMid);
            this.minLatitude = Math.Min(this.minLatitude, bgResult.LatitudeMid);
            this.WrongPntCnt += bgResult.SampleCount;
            this.totalRxLev += bgResult.SampleCount * bgResult.RxLevMean;
            this.wrongRxLevMax = Math.Max(this.wrongRxLevMax, bgResult.RxLevMax);
            this.wrongRxLevMin = Math.Min(this.wrongRxLevMin, bgResult.RxLevMin);

            int wrongDirMax = bgResult.GetImageValueInt();
            this.WrongDirMax = Math.Max(this.WrongDirMax, wrongDirMax);

            int wrongDirMin = bgResult.GetImageValueInt();
            this.WrongDirMin = Math.Min(this.WrongDirMin, wrongDirMin);

            this.TotalPntCnt += bgResult.GetImageValueInt();
            this.WrongMCellCnt += bgResult.GetImageValueInt();
            if (!fileNameList.Contains(bgResult.FileName))
            {
                fileNameList.Add(bgResult.FileName);
            }
        }

        public static string GetResultTitle()
        {
             string str = "小区名,CellID,TAC,ECI,EARFCN,PCI,工参方向角,判断方位角,两角差值,异常点个数,异常点中心经度,异常点中心纬度,异常点平均场强,异常点最小场强,异常点最大场强,正常点个数,异常百分比(%),主强异常点个数,非主强异常点个数,文件名";
            return str;
        }
        public string GetResultDesString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(getDes(CellName) + ",");
            sb.Append(getDes(CellID) + ",");
            sb.Append(getDes(TAC) + ",");
            sb.Append(getDes(ECI) + ",");
            sb.Append(getDes(EARFCN) + ",");
            sb.Append(getDes(PCI) + ",");
            sb.Append(getDes(DirectionCfg) + ",");
            sb.Append(getDes(WrongDirMean) + ",");
            sb.Append(getDes(DirDiff) + ",");
            sb.Append(getDes(WrongPntCnt) + ",");
            sb.Append(getDes(LongitudeMid) + ",");
            sb.Append(getDes(LatitudeMid) + ",");
            sb.Append(getDes(WrongRxLevAvg) + ",");
            sb.Append(getDes(WrongRxLevMin) + ",");
            sb.Append(getDes(WrongRxLevMax) + ",");
            sb.Append(getDes(TotalPntCnt) + ",");
            sb.Append(getDes(WrongRate) + ",");
            sb.Append(getDes(WrongMCellCnt) + ",");
            sb.Append(getDes(WrongNoneMCellCnt) + ",");
            sb.Append(getDes(FileName) + ",");
            return sb.ToString();
        }
        private string getDes(object objValue)
        {
            if (objValue == null)
                objValue = "";
            if (objValue.ToString().IndexOf(",") >= 0)
            {
                objValue = objValue.ToString().Replace(",", "，");
            }
            return objValue.ToString();
        }
    }
}
