﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using AxMapWinGIS;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;
using System.Drawing.Drawing2D;

namespace MasterCom.RAMS.MapControlTool
{
    public class MapControlToolAddPolygon
    {
        public PolygonDrawStyle Style { get; set; }
        public event EventHandler PolygonCreated;
        public List<DbPoint> Points
        {
            get { return points; }
        }

        public class PolygonDrawStyle
        {
            public Font TipsFont { get; set; }
            public Brush TipsBrush { get; set; }
            public SolidBrush TipsBackBrush { get; set; }
            public Pen PolygonPen { get; set; }
            public SolidBrush PolygonBackBrush { get; set; }
        }

        public MapControlToolAddPolygon(MapForm mapForm, AxMap map)
        {
            mapControl = map;
            mapOP = new MapOperation(mapControl);
            points = new List<DbPoint>();
            mf = mapForm;
            MasterCom.RAMS.Model.MainModel.GetInstance().ClearDataEvent += new EventHandler(mapForm_ClearDataEvent);
            SetDefaultDrawStyle();
        }

        public void CreatePolygon(List<DbPoint> pnts)
        {
            points = pnts;
            drawFinished();
        }

        public void Draw(Graphics g)
        {
            drawPolygon(g, points);
        }

        private void mapForm_ClearDataEvent(object sender, EventArgs e)
        {
            ClearPolygon();
        }

        public void ClearPolygon()
        {
            points.Clear();
        }

        public void Activate()
        {
            if (!isActive)
            {
                mapControl.MouseDownEvent += mapControl_MouseDownEvent;
                mapControl.DblClick += mapControl_DblClick;
            }
            isActive = true;
            mapControl.CursorMode = tkCursorMode.cmNone;
            mapControl.MapCursor = tkCursor.crsrCross;
        }

        public void Deactivate()
        {
            if (!isActive) return;
            mapControl.MouseMoveEvent -= mapControl_MouseMoveEvent;
            mapControl.MouseDownEvent -= mapControl_MouseDownEvent;
            mapControl.DblClick -= mapControl_DblClick;
            isActive = false;
        }

        private void SetDefaultDrawStyle()
        {
            Style = new PolygonDrawStyle();
            Style.TipsFont = new Font("宋体", 11, FontStyle.Bold);
            Style.TipsBrush = Brushes.Red;
            Style.TipsBackBrush = new SolidBrush(Color.FromArgb(200, Color.White));
            Style.PolygonPen = new Pen(Color.Green, 3);
            Style.PolygonBackBrush = new SolidBrush(Color.FromArgb(50, Color.Green));
        }

        private void mapControl_MouseDownEvent(object sender, _DMapEvents_MouseDownEvent e)
        {
            mapControl.MouseMoveEvent -= mapControl_MouseMoveEvent;
            if (e.button != 1)//非鼠标左键，开始新的测量点
            {
                drawFinished();
            }
            else
            {
                if (!drawStarted)
                {
                    points.Clear();
                    if (mf != null)
                    {
                        mf.updateMap();
                    }
                }
                double longi = 0;
                double lati = 0;
                mapControl.PixelToProj(e.x, e.y, ref longi, ref lati);
                points.Add(new DbPoint(longi, lati));
                drawStarted = true;
                mapControl.MouseMoveEvent += mapControl_MouseMoveEvent;
            }
        }

        private void mapControl_MouseMoveEvent(object sender, _DMapEvents_MouseMoveEvent e)
        {
            if (points.Count == 0)
            {
                return;
            }
            PointF[] tempArr;
            PointF[] pArr = new PointF[points.Count + 1];
            mapOP.ToDisplay(points.ToArray(), out tempArr);
            if (tempArr[tempArr.Length - 1].X == e.x && tempArr[tempArr.Length - 1].Y == e.y)
            {
                return;
            }

            tempArr.CopyTo(pArr, 0);
            pArr[points.Count] = new PointF(e.x, e.y);//当前鼠标位置点坐标
            GraphicsPath path = new GraphicsPath();
            path.AddLines(pArr);
            path.Widen(Style.PolygonPen);
            Region invRegion = new Region(mapControl.ClientRectangle);
            invRegion.Exclude(path);
            mapControl.Invalidate(invRegion);
            mapControl.Update();
            List<DbPoint> tempPnts = new List<DbPoint>();
            tempPnts.AddRange(points);
            double x = 0;
            double y = 0;
            mapControl.PixelToProj(e.x, e.y, ref x, ref y);
            tempPnts.Add(new DbPoint(x, y));
            Graphics g = mapControl.CreateGraphics();
            g.SmoothingMode = SmoothingMode.AntiAlias;
            drawPolygon(g, tempPnts);
        }

        private void mapControl_DblClick(object sender, EventArgs e)
        {
            drawFinished();
        }

        private void drawFinished()
        {
            mapControl.MouseMoveEvent -= mapControl_MouseMoveEvent;
            drawStarted = false;
            if (points.Count<3)
            {
                points.Clear();
            }
            PolygonCreated(this, EventArgs.Empty);
        }

        private void drawPolygon(Graphics g, List<DbPoint> pointList)
        {
            if (pointList.Count < 2)
            {
                return;
            }
            PointF[] pArr;
            mapOP.ToDisplay(pointList.ToArray(), out pArr);
            if (drawStarted)
            {
                string str = "双击鼠标左键可以完成多边形绘画！";
                SizeF sizeF = g.MeasureString(str, Style.TipsFont);
                g.FillRectangle(Style.TipsBackBrush, pArr[0].X, pArr[0].Y, sizeF.Width, sizeF.Height);
                g.DrawString(str, Style.TipsFont, Brushes.Red, pArr[0]);
            }
            g.DrawPolygon(Style.PolygonPen, pArr);
            if (Style.PolygonBackBrush != null)
            {
                g.FillPolygon(Style.PolygonBackBrush, pArr);
            }
        }

        private readonly MapOperation mapOP;
        private readonly MapForm mf;
        private readonly AxMap mapControl;
        private List<DbPoint> points;//经纬度坐标
        private bool isActive = false;
        private bool drawStarted = false;
    }
}
