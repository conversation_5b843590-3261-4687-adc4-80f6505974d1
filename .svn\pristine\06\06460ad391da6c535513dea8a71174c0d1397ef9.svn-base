﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable]
    public class NROverCoverCause : NRLowSpeedCauseBase
    {
        public override string Name
        {
            get
            {
                return "过覆盖";
            }
        }

        public float RSRPMin { get; set; } = -85;

        public float OverRatio { get; set; } = 1.6f;

        public override string Desc
        {
            get
            {
                return string.Format("采样点信号强度超过{0}，采样点与小区之间的距离超过小区理想覆盖半径的{1}倍"
                    , RSRPMin, OverRatio);
            }
        }

        public override string Suggestion
        {
            get { return null; }
        }

        public override void Judge(NRLowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP, NRTpManagerBase nRCond)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }
                float? rsrp = nRCond.GetSCellRsrp(pnt);
                if (rsrp > RSRPMin)
                {
                    continue;
                }
                NRCell sCell = pnt.GetMainCell_NR();
                if (sCell == null || sCell.Antennas == null || sCell.Antennas.Count == 0)
                {
                    continue;
                }
                double sDistance = pnt.Distance2(sCell.Longitude, sCell.Latitude);
                double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(sCell, 3);
                if (sDistance <= radiusOfCell * 1.6)
                {
                    continue;
                }

                dealLowSpeedSeg(segItem, pnt, nRCond);
                if (!segItem.NeedJudge)
                {
                    return;
                }
            }
        }

        private void dealLowSpeedSeg(NRLowSpeedSeg segItem, TestPoint pnt, NRTpManagerBase nRCond)
        {
            //foreach (NRLowSpeedCauseBase subReason in SubCauses)
            //{
            //    if (!segItem.IsNeedJudge(pnt))
            //    {
            //        break;
            //    }
            //    subReason.JudgeSinglePoint(segItem, pnt, nRCond);
            //}

            if (segItem.IsNeedJudge(pnt))
            {
                NRLowSpeedUnknowReason r = new NRLowSpeedUnknowReason();
                r.Parent = this;
                segItem.SetReason(new NRLowSpeedPointDetail(pnt, r, nRCond));
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["rsrpMin"] = this.RSRPMin;

                //List<object> list = new List<object>();
                //foreach (NRLowSpeedCauseBase cause in SubCauses)
                //{
                //    list.Add(cause.CfgParam);
                //}
                //paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPMin = (float)value["rsrpMin"];

                //SubCauses = new List<NRLowSpeedCauseBase>();
                //List<object> list = value["SubCauseSet"] as List<object>;
                //foreach (object item in list)
                //{
                //    Dictionary<string, object> dic = item as Dictionary<string, object>;
                //    string typeName = dic["TypeName"].ToString();
                //    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                //    NRLowSpeedCauseBase cause = (NRLowSpeedCauseBase)assembly.CreateInstance(typeName);
                //    cause.CfgParam = dic;
                //    AddSubReason(cause);
                //}
            }
        }
    }
}