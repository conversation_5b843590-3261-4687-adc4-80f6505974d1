﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using DBDataViewer;
using System.IO;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_DIY_AREA_COVER_GRID = 0x10;
        public const byte REQTYPE_DIY_AREA_EVENT = 0x12;
        public const byte REQTYPE_DIY_AREA_EVNET_ES = 0x1c;
        public const byte REQTYPE_DIY_LOG_KPI = 0x19;
        public const byte REQTYPE_DIY_STATI_SCAN_CELL_GRID = 0x1d;

        //NB扫频渗透率
        public const byte REQTYPE_DIY_STATI_NB_SCAN_GRID = 0x1e;
    }
    public static partial class ResponseType
    {
        public const byte KPI_LTE_AMR = 0xc8;
        public const byte RESTYPE_COLUMN_FILE_INFO = 0x11;
        public const byte RESTYPE_DIY_FILE_INFO = 0x12;

        public const byte RESTYPE_DIY_AREA_COVER_GRID = 0xa1;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_GPRS = 0xa5;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_AMR = 0xa9;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_PS = 0xab;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_VP = 0xad;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_WCDMA_AMR = 0xb0;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PS = 0xb2;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_WCDMA_VP = 0xb4;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PSHS = 0xb6;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_CDMA_V = 0xb8;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_CDMA_D = 0xba;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_CDMA2000_D = 0xbc;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_SCAN_GSM = 0xbe;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_SCAN_CDMA = 0xc0;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_SCAN_WCDMA = 0xc2;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_SCAN_TDSCDMA = 0xc4;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_GSM_MTR = 0xc6;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_LTE_FDD_AMR = 0xcc;

        //event
        public const byte RESTYPE_DIY_AREA_EVENT = 0xa3;
        public const byte RESTYPE_DIY_AREA_EVENT_NR = 0xa4;

        //event_ES
        public const byte RESTYPE_DIY_AREA_EVENT_ES = 0xa5;

        //log kpi
        public const byte RESTYPE_DIY_LOG_KPI_GSM = 0x21;

        public const byte RESTYPE_DIY_LOG_KPI_GPRS = 0x23;

        public const byte RESTYPE_DIY_LOG_KPI_SCAN = 0x25;//not used yet

        public const byte RESTYPE_DIY_LOG_KPI_TDSCDMA_AMR = 0x27;

        public const byte RESTYPE_DIY_LOG_KPI_TDSCDMA_PS = 0x29;

        public const byte RESTYPE_DIY_LOG_KPI_TDSCDMA_VP = 0x2b;

        public const byte RESTYPE_DIY_LOG_KPI_WCDMA_AMR = 0x2d;

        public const byte RESTYPE_DIY_LOG_KPI_WCDMA_PS = 0x2f;

        public const byte RESTYPE_DIY_LOG_KPI_WCDMA_VP = 0x31;

        public const byte RESTYPE_DIY_LOG_KPI_WCDMA_PSHS = 0x33;

        public const byte RESTYPE_DIY_LOG_KPI_CDMA_V = 0x35;

        public const byte RESTYPE_DIY_LOG_KPI_CDMA_D = 0x37;

        public const byte RESTYPE_DIY_LOG_KPI_CDMA2000_D = 0x39;

        public const byte RESTYPE_DIY_LOG_KPI_GSM_MTR = 0x3d;

        public const byte RESTYPE_DIY_LOG_KPI_SCAN_GSM = 0x3b;

        public const byte RESTYPE_DIY_LOG_KPI_SCAN_TD = 0x3f;

        public const byte RESTYPE_DIY_LOG_KPI_WLAN = 0x41;

        public const byte RESTYPE_DIY_LOG_KPI_LTE_AMR = 0x43;

        public const byte RESTYPE_DIY_LOG_KPI_LTE_FDD_AMR = 0x46;

        public const byte RESTYPE_DIY_LOG_KPI_SCAN_LTETOPN = 0x45;

        public const byte RESTYPE_DIY_LOG_KPI_LTE_FREQSPECTRUM = 0xce; //LTE扫频-频谱分析
        //public const byte RESTYPE_DIY_AREA_COVER_GRID_LTE_FREQSPECTRUM = 0xce

        //scan grid
        public const byte RESTYPE_DIY_AREA_COVER_GRID_SCAN_DTGSM = 0xc8;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_SCAN_DTWCDMA = 0xbc;

        public const byte RESTYPE_DIY_AREA_COVER_GRID_SCAN_LTE_TOPN = 0xca;

        // lte_signal
        public const byte RESTYPE_DIY_AREA_COVER_GRID_LTE_SIGNAL = 0xcd;

        //NBIOT
        public const byte RESTYPE_DIY_AREA_COVER_GRID_SCAN_NBIOT_TOPN = 0xd0;
        public const byte RESTYPE_DIY_CELL_COVER_GRID_SCAN_NBIOT_TOPN = 0xd1;
        public const byte RESTYPE_DIY_LOG_KPI_SCAN_NBIOTTOPN = 0xd1;
        public const byte RESTYPE_DIY_AREASTAT_KPI_SCAN_NBIOTTOPN = 0x46;

        //NR
        public const byte RESTYPE_DIY_LOG_KPI_NR_AMR = 0xd2;
        public const byte RESTYPE_DIY_AREA_COVER_GRID_NR = 0xd3;
        public const byte RESTYPE_DIY_AREASTAT_KPI_NR = 0x47;
        public const byte RESTYPE_DIY_AREASTAT_KPI_EVENT_NR = 0x2C;
        public const byte RESTYPE_DIY_CELL_COVER_GRID_NR = 0xd2;

        //NR SCAN
        public const byte RESTYPE_DIY_LOG_KPI_SCAN_NR = 0xd4;
        public const byte RESTYPE_DIY_AREA_COVER_GRID_SCAN_NR = 0xd5;

        public const byte RESTYPE_DIY_LOG_KPI_NR_FREQSPECTRUM = 0xd6;
        //public const byte RESTYPE_DIY_AREA_COVER_GRID_NR_FREQSPECTRUM = 0xd6;
    }

    public enum OpOptionDef
    {
        AreaSelectIntersect = 1,    //区域与区域相交，比较lt，br经纬度
        AreaSelectInside = 2,       //区域在区域里面，比较lt，br经纬度
        AreaSelectSample = 3,       //点在区域里面，比较点的经纬度
        MaxEqualThan = 4,//大于等于
        MinEqualThan = 5,//小于等于
        Between = 6,
        InSelect = 7,
        TimeSpanIntersect = 8,
        TimeSpanInside = 9,
        StrLike = 10,
        Equal = 11,
        UnEqual = 12,
        CellSelect = 13,
        iTimeSelect = 14,
        DIYSql = 15,
        EndFlag = 255
    }

    public abstract class DIYStatQuery : QueryBase
    {
        protected DIYStatQuery(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        protected abstract StatTbToken getBranchToken();

        protected List<ReportStyle> rptStyleList = new List<ReportStyle>();
        public ReportStyle curSelStyle { get; set; }
        public bool curSelStyleContentAll { get; set; } = false;
        protected virtual void loadReportFromFile()
        {
            try
            {
                System.IO.DirectoryInfo directory = new DirectoryInfo(string.Format(Application.StartupPath + "/config/templates/"));
                System.IO.FileInfo[] files = directory.GetFiles("kpi_*.xml", SearchOption.TopDirectoryOnly);
                if (files.Length > 0)
                {
                    loadTemplates(files);
                }
                else
                {
                    loadReports();
                }
            }
            catch (Exception e)
            {
                log.Warn("读入报表模板文件发生错误:" + e.Message);
            }
            if (rptStyleList == null)
            {
                rptStyleList = new List<ReportStyle>();
            }
        }

        private void loadTemplates(System.IO.FileInfo[] files)
        {
            if (rptStyleList == null)
            {
                rptStyleList = new List<ReportStyle>();
            }
            rptStyleList.Clear();
            foreach (System.IO.FileInfo file in files)
            {
                XmlConfigFile configFile = new XmlConfigFile(file.FullName);
                Dictionary<string, object> dic = configFile.GetItemValue("ReportSetting", "styles") as Dictionary<string, object>;
                if (dic == null)
                {
                    continue;
                }
                ReportStyle rptstyle = new ReportStyle();
                rptstyle.Param = dic;
                int index = file.Name.IndexOf("kpi_") + 4;
                rptstyle.name = file.Name.Substring(index).Replace(".xml", "");
                rptStyleList.Add(rptstyle);
            }
            rptStyleList.Sort();
        }

        private void loadReports()
        {
            XmlConfigFile configFile = new XmlConfigFile(string.Format(Application.StartupPath + "/config/reports.xml"));
            List<Object> list = configFile.GetItemValue("ReportSetting", "styles") as List<Object>;
            if (list != null)
            {
                if (rptStyleList == null)
                {
                    rptStyleList = new List<ReportStyle>();
                }
                rptStyleList.Clear();
                foreach (object value in list)
                {
                    ReportStyle rptStyle = new ReportStyle();
                    rptStyle.Param = value as Dictionary<string, object>;
                    rptStyleList.Add(rptStyle);
                }
                rptStyleList.Sort();
            }
        }

        protected virtual List<string> getNeededImgDefs(int carrierId)
        {
            Dictionary<string, bool> retDic = new Dictionary<string, bool>();
            if (curSelStyle == null)
            {
                return new List<string>();
            }
            foreach (RptCell rpt in curSelStyle.rptCellList)
            {
                if ((carrierId == -1 || rpt.carrierID == carrierId) && rpt.exp != null)
                {
                    extractIncludedParams(rpt.exp, retDic);
                }
            }
            return new List<string>(retDic.Keys);
        }

        protected string getTokenStrFrom(string str, int pos, ref int end)
        {
            if (pos < 0 || pos > str.Length - 1)
            {
                return "";
            }
            int start = pos;
            end = pos;
            while (start >= 0)//向前找
            {
                char ch = str[start];
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/' || ch == ' ' || ch == '{' || ch == '}' || ch == '(' || ch == ')')
                {
                    break;
                }
                start--;
            }
            while (end <= str.Length - 1)//向后找
            {
                char ch = str[end];
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/' || ch == ' ' || ch == '{' || ch == '}' || ch == '(' || ch == ')')
                {
                    break;
                }
                end++;
            }
            if (end <= str.Length && end > start)
            {
                return str.Substring(start + 1, end - start - 1);
            }
            return "";
        }

        protected void extractIncludedParams(string expstr, Dictionary<string, bool> retDic)
        {
            List<string> exps = new List<string>();
            int pos = 0;
            int length = expstr.Length;
            int start = pos; int end = pos;
            while (end < pos + length)
            {
                string str = getTokenStrFrom(expstr, start, ref end);
                if (!string.IsNullOrEmpty(str))
                    exps.Add(str);
                else
                    end++;
                start = end;
            }
            foreach (string str in exps)
            {
                string imgCode = getCodeFromStr(str);
                if (imgCode != string.Empty)
                {
                    string triID = InterfaceManager.GetInstance().GetRevisedStatImgTriadID(getBranchToken(), imgCode);
                    if (triID != null)
                    {
                        retDic[triID] = true;
                    }
                }
            }
        }

        private string getCodeFromStr(string str)
        {
            int pos = str.IndexOf('_');
            if (pos != -1)
            {
                return str.Substring(pos + 1);
            }
            else
            {
                return string.Empty;
            }
        }
        protected void fillContentNeeded_Event(Package package)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("0,1,43,");
            sb.Append("0,2,43,");
            sb.Append("0,3,43,");
            sb.Append("0,4,43,");
            sb.Append("0,5,43,");
            sb.Append("0,6,43,");
            sb.Append("0,7,43,");
            sb.Append("0,8,43,");
            sb.Append("0,9,43,");
            sb.Append("0,10,43,");
            sb.Append("0,11,43,");
            sb.Append("0,12,43,");
            sb.Append("0,13,43,");
            sb.Append("0,14,43,");
            sb.Append("0,15,43,");
            sb.Append("0,16,43,");
            sb.Append("0,17,43,");
            sb.Append("0,18,43,");
            sb.Append("0,19,43,");
            sb.Append("0,20,43,");
            sb.Append("0,21,43,");
            sb.Append("0,22,43,");
            sb.Append("0,23,43,");
            sb.Append("0,24,43,");
            sb.Append("0,25,43,");
            sb.Append("0,26,43");
            package.Content.AddParam(sb.ToString());
        }
        protected virtual void fillContentNeeded_ImgGrid(Package package, int carrierId)
        {
            if (curSelStyleContentAll)
            {
                package.Content.AddParam("-1,-1,-1");
            }
            else
            {
                List<string> imgDefList = getNeededImgDefs(carrierId);
                StringBuilder sbuilder = new StringBuilder();
                for (int i = 0; i < imgDefList.Count; i++)
                {
                    sbuilder.Append(imgDefList[i]);
                    if (i < imgDefList.Count - 1)
                    {
                        sbuilder.Append(",");
                    }
                }
                package.Content.AddParam(sbuilder.ToString());
            }
        }
        protected bool momtFlag = false;
        protected bool multiGeometrys = false;
        protected bool stopQuery = false;
        protected bool byStreetsOfRegion = false;
        /// <summary>
        /// 按区域内的栅格统计
        /// </summary>
        protected bool byGrid = false;
        protected Dictionary<string, MapWinGIS.Shape> regionDic = new Dictionary<string, MapWinGIS.Shape>();
        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            loadReportFromFile();
            SelectReportDlg dlg = new SelectReportDlg();
            dlg.FillCurrentReports(ref rptStyleList);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            bool curSelStyleContentAllTmp;
            curSelStyle = dlg.GetSelectedReport(out curSelStyleContentAllTmp);
            curSelStyleContentAll = curSelStyleContentAllTmp;
            curEventStatFilter = dlg.GetEventStatFilter();
            momtFlag = dlg.GetMomtFlag();

            MainModel.KPICaleGridPercent = dlg.GetCaleGridPercent();
            MainModel.KPIBlanceCaleGrid = dlg.GetBlanceCale();

            InitData();

            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            getReadyBeforeQuery();
            queryByCarrier(1, queryChinaMobileInThread);
            queryByCarrier(2, queryChinaUnicomInThread);
            queryByCarrier(3, queryChinaTelecomInThread);

            getResultAfterQuery();
            if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion || byGrid)
            {
                addReportData(cmDataUnitAreaKPIQueryDic, MainModel.CurChinaMobileStatReportDataList);
                addReportData(cuDataUnitAreaKPIQueryDic, MainModel.CurChinaUnicomStatReportDataList);
                addReportData(ctDataUnitAreaKPIQueryDic, MainModel.CurChinaTelecomStatReportDataList);

                addReportData(cmDataUnitAreaKPIQueryDicMo, MainModel.CurChinaMobileStatReportDataListMo);
                addReportData(cuDataUnitAreaKPIQueryDicMo, MainModel.CurChinaUnicomStatReportDataListMo);
                addReportData(ctDataUnitAreaKPIQueryDicMo, MainModel.CurChinaTelecomStatReportDataListMo);

                addReportData(cmDataUnitAreaKPIQueryDicMt, MainModel.CurChinaMobileStatReportDataListMt);
                addReportData(cuDataUnitAreaKPIQueryDicMt, MainModel.CurChinaUnicomStatReportDataListMt);
                addReportData(ctDataUnitAreaKPIQueryDicMt, MainModel.CurChinaTelecomStatReportDataListMt);
            }
            MainModel.FireMultiRegionStatQueried(this, curSelStyle, regionDic, false);
        }

        private void InitData()
        {
            MainModel.CurChinaMobileStatReportData = null;
            MainModel.CurChinaUnicomStatReportData = null;
            MainModel.CurChinaTelecomStatReportData = null;
            MainModel.CurChinaMobileStatReportDataList.Clear();
            MainModel.CurChinaUnicomStatReportDataList.Clear();
            MainModel.CurChinaTelecomStatReportDataList.Clear();

            MainModel.CurChinaMobileStatReportDataMo = null;
            MainModel.CurChinaUnicomStatReportDataMo = null;
            MainModel.CurChinaTelecomStatReportDataMo = null;
            MainModel.CurChinaMobileStatReportDataListMo.Clear();
            MainModel.CurChinaUnicomStatReportDataListMo.Clear();
            MainModel.CurChinaTelecomStatReportDataListMo.Clear();

            MainModel.CurChinaMobileStatReportDataMt = null;
            MainModel.CurChinaUnicomStatReportDataMt = null;
            MainModel.CurChinaTelecomStatReportDataMt = null;
            MainModel.CurChinaMobileStatReportDataListMt.Clear();
            MainModel.CurChinaUnicomStatReportDataListMt.Clear();
            MainModel.CurChinaTelecomStatReportDataListMt.Clear();

            cmDataUnitAreaKPIQueryDic = null;
            cuDataUnitAreaKPIQueryDic = null;
            ctDataUnitAreaKPIQueryDic = null;

            cmDataUnitAreaKPIQueryDicMo = null;
            cuDataUnitAreaKPIQueryDicMo = null;
            ctDataUnitAreaKPIQueryDicMo = null;

            cmDataUnitAreaKPIQueryDicMt = null;
            cuDataUnitAreaKPIQueryDicMt = null;
            ctDataUnitAreaKPIQueryDicMt = null;
        }

        private void queryByCarrier(int carrierType, CallBackMethodWithParams queryInThread)
        {
            if (Condition.CarrierTypes.Contains(carrierType) && !WaitBox.CancelRequest)
            {
                Condition.Momt = 0;
                queryByCarrier(queryInThread);

                if (momtFlag)
                {
                    Condition.Momt = 1;
                    queryByCarrier(queryInThread);
                    Condition.Momt = 2;
                    queryByCarrier(queryInThread);
                }
            }
        }

        public void addReportData(Dictionary<string, DataUnitAreaKPIQuery> kpiData, List<StatReportData> modelData)
        {
            foreach (DataUnitAreaKPIQuery data in kpiData.Values)
            {
                modelData.Add(new StatReportData(data));
            }
        }

        protected virtual void getResultAfterQuery()
        {

        }

        protected virtual bool getConditionBeforeQuery()
        {
            return true;
        }

        protected virtual void getReadyBeforeQuery()
        {
            multiGeometrys = false;
            stopQuery = false;
            regionDic.Clear();
            if (MainModel.MultiGeometrys && condition.Geometorys.SelectedResvRegions != null 
                && condition.Geometorys.SelectedResvRegions.Count > 0)//
            {
                multiGeometrys = true;
                setRegionDic();
            }
            if (multiGeometrys || MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid || byGrid)
            {
                gridDic = new Dictionary<string, StatGridUnit>();
                gridDicForKPIBlance = new Dictionary<string, StatGridUnit>();
            }
            else if (Condition.MultiTime)
            {
                foreach (TimePeriod period in Condition.Periods)
                {
                    regionDic[period.GetShortString()] = null;
                }
            }
        }

        private void setRegionDic()
        {
            foreach (ResvRegion resvRegion in condition.Geometorys.SelectedResvRegions)
            {
                if (Condition.MultiTime)
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        regionDic[resvRegion.RegionName + period.ToString()] = null;
                    }
                }
                else
                {
                    regionDic[resvRegion.RegionName] = resvRegion.Shape;
                }
            }
        }

        protected void queryByCarrier(CallBackMethodWithParams queryInThread)
        {
            ClientProxy clientProxy = new ClientProxy();

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected virtual void queryChinaMobileInThread(object o) //查询中国移动KPI统计
        {
            try
            {
                WaitBox.CanCancel = true;
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                List<DataUnitAreaKPIQuery> paraPeriodList = new List<DataUnitAreaKPIQuery>();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, paraPeriodList, 1, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, paraPeriodList, 1, period, false);
                        if (WaitBox.CancelRequest)
                        {
                            stopQuery = true;
                            break;
                        }
                    }
                }
                if (!multiGeometrys && !Condition.MultiTime && !byStreetsOfRegion&&!byGrid)
                {
                    if (Condition.Momt == 0)
                    {
                        MainModel.CurChinaMobileStatReportDataList.Add(new StatReportData(paraPeriodList));
                    }
                    else if (Condition.Momt == 1)
                    {
                        MainModel.CurChinaMobileStatReportDataListMo.Add(new StatReportData(paraPeriodList));
                    }
                    else if (Condition.Momt == 2)
                    {
                        MainModel.CurChinaMobileStatReportDataListMt.Add(new StatReportData(paraPeriodList));
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected void queryPeriodInfo(ClientProxy clientProxy, Package package, List<DataUnitAreaKPIQuery> paraPeriodList, int carrierId, TimePeriod period, bool byRound)
        {
            //img grid
            prepareStatPackage_ImgGrid_FileFilter(package, period, (byte)carrierId, byRound);
            fillContentNeeded_ImgGrid(package, carrierId);
            clientProxy.Send();
            recieveInfo_ImgGrid(clientProxy, paraPeriodList, carrierId, period);
            //event
            prepareStatPackage_Event_FileFilter(package, period, (byte)carrierId, byRound);
            prepareStatPackage_Event_EventFilter(package, period);
            fillContentNeeded_Event(package);
            clientProxy.Send();
            recieveInfo_Event(clientProxy, paraPeriodList, carrierId, period);
        }

        protected virtual void queryChinaUnicomInThread(object o) //查询中国联通KPI统计
        {
            try
            {
                WaitBox.CanCancel = true;
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                List<DataUnitAreaKPIQuery> paraPeriodList = new List<DataUnitAreaKPIQuery>();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, paraPeriodList, 2, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, paraPeriodList, 2, period, false);
                        if (WaitBox.CancelRequest)
                        {
                            stopQuery = true;
                            break;
                        }
                    }
                }

                if (!multiGeometrys && !Condition.MultiTime)
                {
                    if (Condition.Momt == 0)
                    {
                        MainModel.CurChinaUnicomStatReportDataList.Add(new StatReportData(paraPeriodList));
                    }
                    else if (Condition.Momt == 1)
                    {
                        MainModel.CurChinaUnicomStatReportDataListMo.Add(new StatReportData(paraPeriodList));
                    }
                    else if (Condition.Momt == 2)
                    {
                        MainModel.CurChinaUnicomStatReportDataListMt.Add(new StatReportData(paraPeriodList));
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }

        }

        protected virtual void queryChinaTelecomInThread(object o) //查询中国电信KPI统计
        {
            try
            {
                WaitBox.CanCancel = true;
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                List<DataUnitAreaKPIQuery> paraPeriodList = new List<DataUnitAreaKPIQuery>();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, paraPeriodList, 3, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, paraPeriodList, 3, period, false);
                        if (WaitBox.CancelRequest)
                        {
                            stopQuery = true;
                            break;
                        }
                    }
                }
                if (!multiGeometrys && !Condition.MultiTime)
                {
                    if (Condition.Momt == 0)
                    {
                        MainModel.CurChinaTelecomStatReportDataList.Add(new StatReportData(paraPeriodList));
                    }
                    else if (Condition.Momt == 1)
                    {
                        MainModel.CurChinaTelecomStatReportDataListMo.Add(new StatReportData(paraPeriodList));
                    }
                    else if (Condition.Momt == 2)
                    {
                        MainModel.CurChinaTelecomStatReportDataListMt.Add(new StatReportData(paraPeriodList));
                    }
                }

            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected virtual void recieveInfo_Event(ClientProxy clientProxy, List<DataUnitAreaKPIQuery> paraRetList, int carrierId, TimePeriod period)
        {
            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
            DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> curDefColumnDef = new List<ColumnDefItem>();
            bool isSeparateEvtByServiceID = this.curSelStyle.IsSeparateEvtByServiceID;
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    //
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curDefColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curDefColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT)
                {
                    NREventHelper.ReSetIntCI(curDefColumnDef);
                    fillData(carrierId, period, retResult, curDefColumnDef, isSeparateEvtByServiceID, package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT_NR)
                {
                    NREventHelper.SetLongCI(curDefColumnDef);
                    fillData(carrierId, period, retResult, curDefColumnDef, isSeparateEvtByServiceID, package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
            paraRetList.Add(retResult);
        }

        private void fillData(int carrierId, TimePeriod period, DataUnitAreaKPIQuery retResult, List<ColumnDefItem> curDefColumnDef, bool isSeparateEvtByServiceID, Package package)
        {
            DataEvent data = DataEvent.Create(package.Content, curDefColumnDef);
            if (isEventInRegion(data.jd, data.wd))
            {
                bool isValid = checkEventFilter(data);
                if (isValid)
                {
                    if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion || byGrid)
                    {
                        DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                        dataUnitAreaKPIQuery.addStatData(data, isSeparateEvtByServiceID);
                        saveDataByRegion(data.jd, data.wd, dataUnitAreaKPIQuery, carrierId, period);
                    }
                    else
                    {
                        retResult.addStatData(data, isSeparateEvtByServiceID);
                    }
                }
            }
        }

        /// <summary>
        /// 判断是否在区域内。为了与区域查询事件查询结果吻合，应该以事件的经纬度去判断，而不再是用对应的栅格相交判断。
        /// </summary>
        /// <param name="lng"></param>
        /// <param name="lat"></param>
        /// <returns></returns>
        protected virtual bool isEventInRegion(double lng,double lat)
        {
            return true;
        }

        public EventStatFilter curEventStatFilter { get; set; }
        /// <summary>
        /// 进行事件条件过滤
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        protected bool checkEventFilter(DataEvent data)
        {
            return curEventStatFilter == null || curEventStatFilter.CheckEventFilter(data);
        }

        protected virtual void recieveInfo_ImgGrid(ClientProxy clientProxy, List<DataUnitAreaKPIQuery> paraRetList,int carrierId, TimePeriod period)
        {
            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
            DTDataHeaderManager.GetInstance();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    //
                }
                else if ( package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curImgColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurImgColumnDef(idpairs, curImgColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID
                  || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_GPRS)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataGSM_NewImg newImg = new DataGSM_NewImg();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }

                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion||byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true,longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            if (MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLatForKPIBlance(longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                                retResult.countGridUnitUseTimeForKPIBlance(longi, lati);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_AMR
                    || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_PS
                    || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_VP)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataTDSCDMA_NewImg newImg = new DataTDSCDMA_NewImg();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion||byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            if (MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLatForKPIBlance(longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                                retResult.countGridUnitUseTimeForKPIBlance(longi, lati);
                            }
                            retResult.addStatData(newImg);
                        }
                    }

                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_AMR
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PS
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_VP
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PSHS)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataWCDMA_AMR newImg = new DataWCDMA_AMR();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion||byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_CDMA_V
                    || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_CDMA_D)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataCDMA_Voice newImg = new DataCDMA_Voice();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion||byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_CDMA2000_D)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataEVDO_Data newImg = new DataEVDO_Data();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion||byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_GSM)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataScan_GSM newImg = new DataScan_GSM();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.WInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion||byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_TDSCDMA)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataScan_TD newImg = new DataScan_TD();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion||byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_LTE_TOPN
                      || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_LTE_FREQSPECTRUM)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataScan_LTE newImg = new DataScan_LTE();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion||byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_NR
                    || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_NR_FREQSPECTRUM)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataScan_NR newImg = new DataScan_NR();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion || byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_WCDMA)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataScan_WCDMA newImg = new DataScan_WCDMA();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion||byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_CDMA)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataScan_CDMA newImg = new DataScan_CDMA();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion||byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_GSM_MTR)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataMTR_GSM newImg = new DataMTR_GSM();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.WInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion||byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.KPI_LTE_AMR)
                {
                    double longi = package.Content.GetParamDouble();
                    double lati = package.Content.GetParamDouble();
                    if (isValidPoint(longi, lati))
                    {
                        DataLTE newImg = new DataLTE();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.WInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        if (multiGeometrys || Condition.MultiTime || byStreetsOfRegion || byGrid)
                        {
                            DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                            data.addStatData(newImg);
                            saveDataByRegion(longi, lati, data, carrierId, period);
                        }
                        else
                        {
                            if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                            {
                                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(true, longi, lati);
                                if (gridUnit == null)
                                {
                                    continue;
                                }
                                gridUnit.data.addStatData(newImg);
                                retResult.addStatGridUnit(gridUnit);
                            }
                            retResult.addStatData(newImg);
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
            paraRetList.Add(retResult);
        }
        /// <summary>
        /// 解析idpairs，输出至curColumnDef
        /// </summary>
        /// <param name="idpairs"></param>
        /// <param name="curColumnDef"></param>
        protected new void parseToCurImgColumnDef(string idpairs, List<StatImgDefItem> curColumnDef)
        {
            string[] splits = idpairs.Split(',');
            if (splits.Length % 3 != 0)
            {
                throw (new Exception("返回的ID三元组异常 （数目）" + idpairs));
            }
            int colCount = splits.Length / 3;
            try
            {
                for (int i = 0; i < colCount; i++)
                {
                    string strImgId = splits[i * 3];
                    string strParaId = splits[i * 3 + 1];
                    string strTableId = splits[i * 3 + 2];
                    int imgid = int.Parse(strImgId);
                    int paraid = int.Parse(strParaId);
                    int tableid = int.Parse(strTableId);
                    StatImgDefItem defItem = InterfaceManager.GetInstance().GetStatImgDef(imgid, paraid, tableid);
                    if (defItem == null)
                    {
                        throw (new Exception("返回的ID三元组异常（未找到）" + imgid + "," + paraid + "," + tableid));
                    }
                    curColumnDef.Add(defItem);
                }
            }
            catch
            {
                throw (new Exception("返回的ID三元组异常（解析）" + idpairs));
            }

        }

        protected virtual void addQueryPackageParamBase(Package package, byte carrierID)
        {
            package.Content.AddParam((byte)condition.Projects.Count);
            foreach (int projectID in condition.Projects)
            {
                package.Content.AddParam((byte)projectID);
            }
            package.Content.AddParam((byte)0xff);
            package.Content.AddParam((byte)condition.ServiceTypes.Count);
            foreach (int type in condition.ServiceTypes)
            {
                package.Content.AddParam((byte)type);
            }
            //
            if (condition.IsAllAgent)
            {
                package.Content.AddParam((byte)0xff);
            }
            else
            {
                package.Content.AddParam((byte)(condition.AgentIds.Count));
                foreach (int id in condition.AgentIds)
                {
                    package.Content.AddParam((byte)id);
                }
            }
            package.Content.AddParam((byte)1);
            package.Content.AddParam(carrierID);
        }

        protected virtual void prepareStatPackage_ImgGrid_FileFilter(Package package, TimePeriod period, byte carrierID, bool byRound)
        {
        }
        protected virtual void prepareStatPackage_Event_FileFilter(Package package, TimePeriod period, byte carrierID, bool byRound)
        {
        }
        protected virtual void prepareStatPackage_Event_EventFilter(Package package, TimePeriod period)
        {
        }
        protected virtual bool isValidPoint(double jd, double wd)
        {
            return true;
        }

        protected virtual void saveDataByRegion(double longitude, double latitude, DataUnitAreaKPIQuery data, int carrierId, TimePeriod period)
        {
            bool isEvent = data.eStore != null;
            if (byGrid)
            {
                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(!isEvent, longitude, latitude);
                string gridKey = string.Format("{0}_{1}", gridUnit.ltLong, gridUnit.ltLat);
                regionDic[gridKey] = null;
                if (MainModel.KPICaleGridPercent && !isEvent)
                {
                    gridUnit.data.addStatData(data);
                }

                Dictionary<string, DataUnitAreaKPIQuery> dataUnitAreaKPIQueryDic = null;
                if (carrierId == 1)
                {
                    if (condition.Momt == 0)
                    {
                        if (cmDataUnitAreaKPIQueryDic == null)
                        {
                            cmDataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        }
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        if (cmDataUnitAreaKPIQueryDicMo == null)
                        {
                            cmDataUnitAreaKPIQueryDicMo = new Dictionary<string, DataUnitAreaKPIQuery>();
                        }
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        if (cmDataUnitAreaKPIQueryDicMt == null)
                        {
                            cmDataUnitAreaKPIQueryDicMt = new Dictionary<string, DataUnitAreaKPIQuery>();
                        }
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDicMt;
                    }
                }
                else if (carrierId == 2)
                {
                    if (condition.Momt == 0)
                    {
                        if (cuDataUnitAreaKPIQueryDic == null)
                        {
                            cuDataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        }
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        if (cuDataUnitAreaKPIQueryDicMo == null)
                        {
                            cuDataUnitAreaKPIQueryDicMo = new Dictionary<string, DataUnitAreaKPIQuery>();
                        }
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        if (cuDataUnitAreaKPIQueryDicMt == null)
                        {
                            cuDataUnitAreaKPIQueryDicMt = new Dictionary<string, DataUnitAreaKPIQuery>();
                        }
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDicMt;
                    }
                }
                else if (carrierId == 3)
                {
                    if (condition.Momt == 0)
                    {
                        if (ctDataUnitAreaKPIQueryDic == null)
                        {
                            ctDataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        }
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        if (ctDataUnitAreaKPIQueryDicMo == null)
                        {
                            ctDataUnitAreaKPIQueryDicMo = new Dictionary<string, DataUnitAreaKPIQuery>();
                        }
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        if (ctDataUnitAreaKPIQueryDicMt == null)
                        {
                            ctDataUnitAreaKPIQueryDicMt = new Dictionary<string, DataUnitAreaKPIQuery>();
                        }
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDicMt;
                    }
                }
                DataUnitAreaKPIQuery oldData;
                string key = Condition.MultiTime ? gridKey + "_" + period.ToString() : gridKey;
                if (!dataUnitAreaKPIQueryDic.TryGetValue(key, out oldData))
                {
                    oldData = new DataUnitAreaKPIQuery();
                    dataUnitAreaKPIQueryDic[key] = oldData;
                }
                oldData.addStatData(data);
                if (MainModel.KPICaleGridPercent && !isEvent)
                {
                    oldData.addStatGridUnit(gridUnit);
                }
            }
            else if (multiGeometrys)
            {
                List<string> regionNameList = getRegionName(isEvent, longitude, latitude);
                if (regionNameList.Count <= 0)
                {
                    return;
                }
                StatGridUnit gridUnit = getStatGridUnitByLongAndLat(!isEvent, longitude, latitude);
                if (MainModel.KPICaleGridPercent && !isEvent)
                {
                    gridUnit.data.addStatData(data);
                }
                if (carrierId == 1)
                {
                    Dictionary<string, DataUnitAreaKPIQuery> dataUnitAreaKPIQueryDic = null;
                    if (condition.Momt == 0)
                    {
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDicMt;
                    }

                    if (dataUnitAreaKPIQueryDic == null)
                    {
                        dataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        foreach (ResvRegion resvRegion in Condition.Geometorys.SelectedResvRegions)
                        {
                            if (Condition.MultiTime)
                            {
                                foreach (TimePeriod item in Condition.Periods)
                                {
                                    DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                                    dataUnitAreaKPIQueryDic[resvRegion.RegionName + item.ToString()] = dataUnitAreaKPIQuery;
                                }
                            }
                            else
                            {
                                DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                                dataUnitAreaKPIQueryDic[resvRegion.RegionName] = dataUnitAreaKPIQuery;
                            }
                        }
                        if (condition.Momt == 0)
                        {
                            cmDataUnitAreaKPIQueryDic = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 1)
                        {
                            cmDataUnitAreaKPIQueryDicMo = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 2)
                        {
                            cmDataUnitAreaKPIQueryDicMt = dataUnitAreaKPIQueryDic;
                        }
                    }

                    if (Condition.MultiTime)
                    {
                        foreach (string regionName in regionNameList)
                        {
                            dataUnitAreaKPIQueryDic[regionName + period.ToString()].addStatData(data);
                            if (MainModel.KPICaleGridPercent && !isEvent)
                            {
                                dataUnitAreaKPIQueryDic[regionName + period.ToString()].addStatGridUnit(gridUnit);
                            }
                        }
                    }
                    else
                    {
                        foreach (string regionName in regionNameList)
                        {
                            dataUnitAreaKPIQueryDic[regionName].addStatData(data);
                            if (MainModel.KPICaleGridPercent && !isEvent)
                            {
                                dataUnitAreaKPIQueryDic[regionName].addStatGridUnit(gridUnit);
                            }
                        }
                    }
                }
                else if (carrierId == 2)
                {
                    Dictionary<string, DataUnitAreaKPIQuery> dataUnitAreaKPIQueryDic = null;
                    if (condition.Momt == 0)
                    {
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDicMt;
                    }
                    if (dataUnitAreaKPIQueryDic == null)
                    {
                        dataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        foreach (ResvRegion resvRegion in Condition.Geometorys.SelectedResvRegions)
                        {
                            if (Condition.MultiTime)
                            {
                                foreach (TimePeriod item in Condition.Periods)
                                {
                                    DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                                    dataUnitAreaKPIQueryDic[resvRegion.RegionName + item.ToString()] = dataUnitAreaKPIQuery;
                                }
                            }
                            else
                            {
                                DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                                dataUnitAreaKPIQueryDic[resvRegion.RegionName] = dataUnitAreaKPIQuery;
                            }
                        }
                        if (condition.Momt == 0)
                        {
                            cuDataUnitAreaKPIQueryDic = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 1)
                        {
                            cuDataUnitAreaKPIQueryDicMo = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 2)
                        {
                            cuDataUnitAreaKPIQueryDicMt = dataUnitAreaKPIQueryDic;
                        }
                    }
                    if (Condition.MultiTime)
                    {
                        foreach (string regionName in regionNameList)
                        {
                            dataUnitAreaKPIQueryDic[regionName + period.ToString()].addStatData(data);
                            if (MainModel.KPICaleGridPercent && !isEvent)
                            {
                                dataUnitAreaKPIQueryDic[regionName + period.ToString()].addStatGridUnit(gridUnit);
                            }
                        }
                    }
                    else
                    {
                        foreach (string regionName in regionNameList)
                        {
                            dataUnitAreaKPIQueryDic[regionName].addStatData(data);
                            if (MainModel.KPICaleGridPercent && !isEvent)
                            {
                                dataUnitAreaKPIQueryDic[regionName].addStatGridUnit(gridUnit);
                            }
                        }
                    }
                }
                else if (carrierId == 3)
                {
                    Dictionary<string, DataUnitAreaKPIQuery> dataUnitAreaKPIQueryDic = null;
                    if (condition.Momt == 0)
                    {
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDicMt;
                    }
                    if (dataUnitAreaKPIQueryDic == null)
                    {
                        dataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        foreach (ResvRegion resvRegion in Condition.Geometorys.SelectedResvRegions)
                        {
                            if (Condition.MultiTime)
                            {
                                foreach (TimePeriod item in Condition.Periods)
                                {
                                    DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                                    dataUnitAreaKPIQueryDic[resvRegion.RegionName + item.ToString()] = dataUnitAreaKPIQuery;
                                }
                            }
                            else
                            {
                                DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                                dataUnitAreaKPIQueryDic[resvRegion.RegionName] = dataUnitAreaKPIQuery;
                            }
                        }
                        if (condition.Momt == 0)
                        {
                            ctDataUnitAreaKPIQueryDic = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 1)
                        {
                            ctDataUnitAreaKPIQueryDicMo = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 2)
                        {
                            ctDataUnitAreaKPIQueryDicMt = dataUnitAreaKPIQueryDic;
                        }
                    }
                    if (Condition.MultiTime)
                    {
                        foreach (string regionName in regionNameList)
                        {
                            dataUnitAreaKPIQueryDic[regionName + period.ToString()].addStatData(data);
                            if (MainModel.KPICaleGridPercent && !isEvent)
                            {
                                dataUnitAreaKPIQueryDic[regionName + period.ToString()].addStatGridUnit(gridUnit);
                            }
                        }
                    }
                    else
                    {
                        foreach (string regionName in regionNameList)
                        {
                            dataUnitAreaKPIQueryDic[regionName].addStatData(data);
                            if (MainModel.KPICaleGridPercent && !isEvent)
                            {
                                dataUnitAreaKPIQueryDic[regionName].addStatGridUnit(gridUnit);
                            }
                        }
                    }
                }
            }
            else if (Condition.MultiTime)
            {
                StatGridUnit gridUnit = null;
                if (MainModel.KPICaleGridPercent && !isEvent)
                {
                    gridUnit = getStatGridUnitByLongAndLat(true, longitude, latitude);
                    if (gridUnit == null)
                    {
                        return;
                    }
                    gridUnit.data.addStatData(data);
                }
                if (carrierId == 1)
                {
                    Dictionary<string, DataUnitAreaKPIQuery> dataUnitAreaKPIQueryDic = null;
                    if (condition.Momt == 0)
                    {
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDicMt;
                    }

                    if (dataUnitAreaKPIQueryDic == null)
                    {
                        dataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        foreach (TimePeriod item in Condition.Periods)
                        {
                            DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                            dataUnitAreaKPIQueryDic[item.ToString()] = dataUnitAreaKPIQuery;
                        }
                    }
                    dataUnitAreaKPIQueryDic[period.ToString()].addStatData(data);
                    if (MainModel.KPICaleGridPercent && !isEvent)
                    {
                        dataUnitAreaKPIQueryDic[period.ToString()].addStatGridUnit(gridUnit);
                    }
                    if (condition.Momt == 0)
                    {
                        cmDataUnitAreaKPIQueryDic = dataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        cmDataUnitAreaKPIQueryDicMo = dataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 2)
                    {
                        cmDataUnitAreaKPIQueryDicMt = dataUnitAreaKPIQueryDic;
                    }
                }
                else if (carrierId == 2)
                {
                    Dictionary<string, DataUnitAreaKPIQuery> dataUnitAreaKPIQueryDic = null;
                    if (condition.Momt == 0)
                    {
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDicMt;
                    }
                    if (dataUnitAreaKPIQueryDic == null)
                    {
                        dataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        foreach (TimePeriod item in Condition.Periods)
                        {
                            DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                            dataUnitAreaKPIQueryDic[item.ToString()] = dataUnitAreaKPIQuery;
                        }
                    }
                    dataUnitAreaKPIQueryDic[period.ToString()].addStatData(data);
                    if (MainModel.KPICaleGridPercent && !isEvent)
                    {
                        dataUnitAreaKPIQueryDic[period.ToString()].addStatGridUnit(gridUnit);
                    }
                    if (condition.Momt == 0)
                    {
                        cuDataUnitAreaKPIQueryDic = dataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        cuDataUnitAreaKPIQueryDicMo = dataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 2)
                    {
                        cuDataUnitAreaKPIQueryDicMt = dataUnitAreaKPIQueryDic;
                    }
                }
                else if (carrierId == 3)
                {
                    Dictionary<string, DataUnitAreaKPIQuery> dataUnitAreaKPIQueryDic = null;
                    if (condition.Momt == 0)
                    {
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDicMt;
                    }
                    if (dataUnitAreaKPIQueryDic == null)
                    {
                        dataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        foreach (TimePeriod item in Condition.Periods)
                        {
                            DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                            dataUnitAreaKPIQueryDic[item.ToString()] = dataUnitAreaKPIQuery;
                        }
                    }
                    dataUnitAreaKPIQueryDic[period.ToString()].addStatData(data);
                    if (MainModel.KPICaleGridPercent && !isEvent)
                    {
                        dataUnitAreaKPIQueryDic[period.ToString()].addStatGridUnit(gridUnit);
                    }
                    if (condition.Momt == 0)
                    {
                        ctDataUnitAreaKPIQueryDic = dataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        ctDataUnitAreaKPIQueryDicMo = dataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 2)
                    {
                        ctDataUnitAreaKPIQueryDicMt = dataUnitAreaKPIQueryDic;
                    }
                }
            }
            else if (Condition.WholeProvince)
            {
                StatGridUnit gridUnit = null;
                if (MainModel.KPICaleGridPercent && !isEvent)
                {
                    gridUnit = getStatGridUnitByLongAndLat(true, longitude, latitude);
                    if (gridUnit == null)
                    {
                        return;
                    }
                    gridUnit.data.addStatData(data);
                }
                if (carrierId == 1)
                {
                    Dictionary<string, DataUnitAreaKPIQuery> dataUnitAreaKPIQueryDic = null;
                    if (condition.Momt == 0)
                    {
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        dataUnitAreaKPIQueryDic = cmDataUnitAreaKPIQueryDicMt;
                    }

                    if (dataUnitAreaKPIQueryDic == null)
                    {
                        dataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        foreach (string districtName in DistrictManager.GetInstance().DistrictNames)
                        {
                            DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                            dataUnitAreaKPIQueryDic[districtName] = dataUnitAreaKPIQuery;
                        }
                        if (condition.Momt == 0)
                        {
                            cmDataUnitAreaKPIQueryDic = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 1)
                        {
                            cmDataUnitAreaKPIQueryDicMo = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 2)
                        {
                            cmDataUnitAreaKPIQueryDicMt = dataUnitAreaKPIQueryDic;
                        }
                    }
                    string curCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                    dataUnitAreaKPIQueryDic[curCity].addStatData(data);
                    if (MainModel.KPICaleGridPercent && !isEvent)
                    {
                        dataUnitAreaKPIQueryDic[curCity].addStatGridUnit(gridUnit);
                    }
                }
                else if (carrierId == 2)
                {
                    Dictionary<string, DataUnitAreaKPIQuery> dataUnitAreaKPIQueryDic = null;
                    if (condition.Momt == 0)
                    {
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        dataUnitAreaKPIQueryDic = cuDataUnitAreaKPIQueryDicMt;
                    }
                    if (dataUnitAreaKPIQueryDic == null)
                    {
                        dataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        foreach (string districtName in DistrictManager.GetInstance().DistrictNames)
                        {
                            DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                            dataUnitAreaKPIQueryDic[districtName] = dataUnitAreaKPIQuery;
                        }
                        if (condition.Momt == 0)
                        {
                            cuDataUnitAreaKPIQueryDic = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 1)
                        {
                            cuDataUnitAreaKPIQueryDicMo = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 2)
                        {
                            cuDataUnitAreaKPIQueryDicMt = dataUnitAreaKPIQueryDic;
                        }
                    }
                    string curCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                    dataUnitAreaKPIQueryDic[curCity].addStatData(data);
                    if (MainModel.KPICaleGridPercent && !isEvent)
                    {
                        dataUnitAreaKPIQueryDic[curCity].addStatGridUnit(gridUnit);
                    }
                }
                else if (carrierId == 3)
                {
                    Dictionary<string, DataUnitAreaKPIQuery> dataUnitAreaKPIQueryDic = null;
                    if (condition.Momt == 0)
                    {
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDic;
                    }
                    else if (condition.Momt == 1)
                    {
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDicMo;
                    }
                    else if (condition.Momt == 2)
                    {
                        dataUnitAreaKPIQueryDic = ctDataUnitAreaKPIQueryDicMt;
                    }
                    if (dataUnitAreaKPIQueryDic == null)
                    {
                        dataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
                        foreach (string districtName in DistrictManager.GetInstance().DistrictNames)
                        {
                            DataUnitAreaKPIQuery dataUnitAreaKPIQuery = new DataUnitAreaKPIQuery();
                            dataUnitAreaKPIQueryDic[districtName] = dataUnitAreaKPIQuery;
                        }
                        if (condition.Momt == 0)
                        {
                            ctDataUnitAreaKPIQueryDic = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 1)
                        {
                            ctDataUnitAreaKPIQueryDicMo = dataUnitAreaKPIQueryDic;
                        }
                        else if (condition.Momt == 2)
                        {
                            ctDataUnitAreaKPIQueryDicMt = dataUnitAreaKPIQueryDic;
                        }
                    }
                    string curCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                    dataUnitAreaKPIQueryDic[curCity].addStatData(data);
                    if (MainModel.KPICaleGridPercent && !isEvent)
                    {
                        dataUnitAreaKPIQueryDic[curCity].addStatGridUnit(gridUnit);
                    }
                }
            }
        }

        protected Dictionary<string, DataUnitAreaKPIQuery> cmDataUnitAreaKPIQueryDic = null;
        protected Dictionary<string, DataUnitAreaKPIQuery> cuDataUnitAreaKPIQueryDic = null;
        protected Dictionary<string, DataUnitAreaKPIQuery> ctDataUnitAreaKPIQueryDic = null;

        protected Dictionary<string, DataUnitAreaKPIQuery> cmDataUnitAreaKPIQueryDicMo = null;
        protected Dictionary<string, DataUnitAreaKPIQuery> cuDataUnitAreaKPIQueryDicMo = null;
        protected Dictionary<string, DataUnitAreaKPIQuery> ctDataUnitAreaKPIQueryDicMo = null;

        protected Dictionary<string, DataUnitAreaKPIQuery> cmDataUnitAreaKPIQueryDicMt = null;
        protected Dictionary<string, DataUnitAreaKPIQuery> cuDataUnitAreaKPIQueryDicMt = null;
        protected Dictionary<string, DataUnitAreaKPIQuery> ctDataUnitAreaKPIQueryDicMt = null;

        protected Dictionary<string, StatGridUnit> gridDic = null;
        protected Dictionary<string, StatGridUnit> gridDicForKPIBlance = null;

        private List<string> getRegionName(bool isEvent, double ltLng, double ltLat)
        {
            List<string> regionNameList = new List<string>();
            DbRect rect = null;
            if (!isEvent)
            {
                GridUnit grid = new GridUnit();
                grid.LTLng = ltLng;
                grid.LTLat = ltLat;
                rect = grid.Bounds;
            }
            if (Condition.Geometorys.SelectedResvRegions != null && Condition.Geometorys.SelectedResvRegions.Count > 0)
            {
                foreach (ResvRegion resvRegion in Condition.Geometorys.SelectedResvRegions)
                {
                    addValidRegionName(isEvent, ltLng, ltLat, regionNameList, rect, resvRegion);
                }
            }
            return regionNameList;
        }

        private void addValidRegionName(bool isEvent, double ltLng, double ltLat, List<string> regionNameList, DbRect rect, ResvRegion resvRegion)
        {
            if (isEvent)
            {
                if (resvRegion.GeoOp.CheckPointInRegion(ltLng, ltLat))
                {
                    regionNameList.Add(resvRegion.RegionName);
                }
            }
            else
            {
                if (resvRegion.GeoOp.CheckRectCenterInRegion(rect))
                {
                    regionNameList.Add(resvRegion.RegionName);
                }
            }
        }

        protected StatGridUnit getStatGridUnitByLongAndLat(bool isGridLocation, double longitude, double latitude)
        {
            double left = isGridLocation ? longitude : MasterCom.RAMS.Grid.GridHelper.RoundAsLeft(longitude);
            double top = isGridLocation ? latitude : MasterCom.RAMS.Grid.GridHelper.RoundAsTop(latitude);
            string key = left + "_" + top;
            StatGridUnit gridUnit = null;
            if (gridDic.ContainsKey(key))
            {
                gridUnit = gridDic[key];
            }
            else
            {
                gridUnit = new StatGridUnit();
                gridUnit.ltLong = left;
                gridUnit.ltLat = top;
                if (MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
                {
                    gridUnit.data = new DataUnitAreaKPIQuery();
                }
                gridDic[key] = gridUnit;
            }
            return gridUnit;
        }

        protected StatGridUnit getStatGridUnitByLongAndLatForKPIBlance(double longitude, double latitude)
        {
            string key = longitude + "_" + latitude;
            StatGridUnit gridUnit = null;
            if (gridDicForKPIBlance.ContainsKey(key))
            {
                gridUnit = gridDicForKPIBlance[key];
            }
            else
            {
                gridUnit = new StatGridUnit();
                gridUnit.ltLong = longitude;
                gridUnit.ltLat = latitude;
                if (MainModel.KPIBlanceCaleGrid)
                {
                    gridUnit.data = new DataUnitAreaKPIQuery();
                }
                gridDicForKPIBlance[key] = gridUnit;
            }
            return gridUnit;
        }

    }

    public class StatGridUnit
    {
        public StatGridUnit()
        {
            row = -1;
            col = -1;
            regionName = "";
        }

        public double ltLong { get; set; }
        public double ltLat { get; set; }
        //need to perfect qjw
        public int iLong
        {
            get
            {
                int GRID_ROUNDING_LONG = (int)(CD.ATOM_SPAN_LONG * 10000000);
                return ((int)(ltLong * 10000000)) / GRID_ROUNDING_LONG * GRID_ROUNDING_LONG;
            }
        }
        public int iLat
        {
            get
            {
                int GRID_ROUNDING_LAT = (int)(CD.ATOM_SPAN_LAT * 10000000);
                return ((int)(ltLat * 10000000)) / GRID_ROUNDING_LAT * GRID_ROUNDING_LAT;
            }
        }
        public int row { get; set; }
        public int col { get; set; }
        public string regionName { get; set; }
        public DataUnitAreaKPIQuery data { get; set; }

        public void copyFrom(StatGridUnit gridUnit)
        {
            ltLong = gridUnit.ltLong;
            ltLat = gridUnit.ltLat;
            row = gridUnit.row;
            col = gridUnit.col;
            regionName = gridUnit.regionName;
            if (gridUnit.data != null)
            {
                if (data == null)
                {
                    data = new DataUnitAreaKPIQuery();
                }
                data.copyFrom(gridUnit.data);
            }
        }
    }
}
