﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Collections;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SameEarfcnPciResultForm : MinCloseForm
    {
        private SameEarfcnPciSetConditionDlg conditionDlg;
        private SameEarfcnPciStater stater;
        private List<SameEarfcnPciCell> curViewList = null;

        public SameEarfcnPciResultForm(MainModel mm, SameEarfcnPciSetConditionDlg dlg) : base(mm)
        {
            InitializeComponent();
            DisposeWhenClose = true;
            InitObjectListView();
            this.conditionDlg = dlg;
            stater = new SameEarfcnPciStater(mm);
            this.btnFind.Click += BtnFind_Click;
            this.btnFind.Hide();
            this.panel1.Hide();
            this.miExportExcel.Click += MiExportExcel_Click;
            this.treeListView.DoubleClick += TreeListView_DoubleClick;
            this.Load += Form_Load;
        }

        private void Form_Load(object sender, EventArgs e)
        {
            int width = this.treeListView.ClientSize.Width;
            int columnWidth = width / this.treeListView.Columns.Count;
            foreach (BrightIdeasSoftware.OLVColumn column in this.treeListView.Columns)
            {
                column.Width = columnWidth;
            }

            initTreeListView();
        }

        private void BtnFind_Click(object sender, EventArgs e)
        {
            conditionDlg = SameEarfcnPciSetConditionDlg.GetDlg();
            if (conditionDlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            initTreeListView();
        }

        private void initTreeListView()
        {
            SameEarfcnPciCond cond = this.GetCondition();
            List<SameEarfcnPciCell> cellList = stater.DoStat(cond);
            treeListView.SetObjects(cellList);
            curViewList = cellList;
        }

        private void InitObjectListView()
        {
            this.colCellName.AspectGetter = delegate (object row)
            {
                if (row is SameEarfcnPciCell)
                {
                    SameEarfcnPciCell interfereCell = row as SameEarfcnPciCell;
                    return interfereCell.selectedCell.Name;
                }
                else if (row is SameEarfcnPciCellType)
                {
                    SameEarfcnPciCellType interfereCellType = row as SameEarfcnPciCellType;
                    return interfereCellType.typeName;
                }
                else if (row is SameEarfcnPciCellItem)
                {
                    SameEarfcnPciCellItem cellView = row as SameEarfcnPciCellItem;
                    return cellView.CellName;
                }
                else
                {
                    return "";
                }
            };
            setCellInfo();
            this.colDistance.AspectGetter = delegate (object row)
            {
                if (row is SameEarfcnPciCellItem)
                {
                    SameEarfcnPciCellItem cellView = row as SameEarfcnPciCellItem;
                    return Math.Round(cellView.Distance, 2);
                }
                else
                {
                    return "";
                }
            };
            this.colAngle.AspectGetter = delegate (object row)
            {
                if (row is SameEarfcnPciCellItem)
                {
                    SameEarfcnPciCellItem cellView = row as SameEarfcnPciCellItem;
                    return cellView.InterAngle;
                }
                else
                {
                    return "";
                }
            };
            this.treeListView.CanExpandGetter = delegate (object row)
            {
                return row is SameEarfcnPciCell || row is SameEarfcnPciCellType;
            };
            this.treeListView.ChildrenGetter = delegate (object row)
            {
                if (row is SameEarfcnPciCell)
                {
                    SameEarfcnPciCell interfereCell = row as SameEarfcnPciCell;
                    List<SameEarfcnPciCellType> typeList = new List<SameEarfcnPciCellType>();

                    foreach (string type in interfereCell.cellsTypeDic.Keys)
                    {
                        typeList.Add(interfereCell.cellsTypeDic[type]);
                    }
                    return typeList;
                }
                else if (row is SameEarfcnPciCellType)
                {
                    SameEarfcnPciCellType interfereCellType = row as SameEarfcnPciCellType;
                    return interfereCellType.cellItems;
                }
                else
                {
                    return new ArrayList();
                }
            };
        }

        private void setCellInfo()
        {
            this.colCellID.AspectGetter = delegate (object row)
            {
                if (row is SameEarfcnPciCell)
                {
                    SameEarfcnPciCell interfereCell = row as SameEarfcnPciCell;
                    if (conditionDlg.isRadioNew)
                    {
                        return interfereCell.selectedCell.CellID;
                    }
                    return interfereCell.selectedCell.SCellID;
                }
                else if (row is SameEarfcnPciCellItem)
                {
                    SameEarfcnPciCellItem cellView = row as SameEarfcnPciCellItem;
                    return cellView.CellID;
                }
                else
                {
                    return "";
                }
            };
            this.colEarfcn.AspectGetter = delegate (object row)
            {
                if (row is SameEarfcnPciCell)
                {
                    SameEarfcnPciCell interfereCell = row as SameEarfcnPciCell;
                    return interfereCell.selectedCell.EARFCN;
                }
                else if (row is SameEarfcnPciCellItem)
                {
                    SameEarfcnPciCellItem cellView = row as SameEarfcnPciCellItem;
                    return cellView.Earfcn;
                }
                else
                {
                    return "";
                }
            };
            this.colPci.AspectGetter = delegate (object row)
            {
                if (row is SameEarfcnPciCell)
                {
                    SameEarfcnPciCell interfereCell = row as SameEarfcnPciCell;
                    return interfereCell.selectedCell.PCI;
                }
                else if (row is SameEarfcnPciCellItem)
                {
                    SameEarfcnPciCellItem cellView = row as SameEarfcnPciCellItem;
                    return cellView.Pci;
                }
                else
                {
                    return "";
                }
            };
            this.colLongitude.AspectGetter = delegate (object row)
            {
                return getLongitude(row);
            };
            this.colLatitude.AspectGetter = delegate (object row)
            {
                return getLatitude(row);
            };
        }

        private object getLongitude(object row)
        {
            if (row is SameEarfcnPciCell)
            {
                SameEarfcnPciCell interfereCell = row as SameEarfcnPciCell;
                if (conditionDlg.isRadioNew)
                {
                    return interfereCell.selectedCell.LongitudeTemp;
                }
                return interfereCell.selectedCell.Longitude;
            }
            else if (row is SameEarfcnPciCellItem)
            {
                SameEarfcnPciCellItem cellView = row as SameEarfcnPciCellItem;
                return cellView.Longitude;
            }
            else
            {
                return "";
            }
        }

        private object getLatitude(object row)
        {
            if (row is SameEarfcnPciCell)
            {
                SameEarfcnPciCell interfereCell = row as SameEarfcnPciCell;
                if (conditionDlg.isRadioNew)
                {
                    return interfereCell.selectedCell.LatitudeTemp;
                }
                return interfereCell.selectedCell.Latitude;
            }
            else if (row is SameEarfcnPciCellItem)
            {
                SameEarfcnPciCellItem cellView = row as SameEarfcnPciCellItem;
                return cellView.Latitude;
            }
            else
            {
                return "";
            }
        }

        private SameEarfcnPciCond GetCondition()
        {
            SameEarfcnPciCond cond = new SameEarfcnPciCond();
            if (conditionDlg.IsDisAngle)
            {
                cond.MaxDistance = conditionDlg.Distance;
                cond.minAngle = conditionDlg.MinAngle;
                cond.maxAngle = conditionDlg.MaxAngle;
            }
            cond.IsRejectIndoorCell = conditionDlg.IsRejectIndoorCell;
            cond.IsDisAngle = conditionDlg.IsDisAngle;
            cond.IsFirstNBCell = conditionDlg.IsFirstNBCell;
            cond.IsSecondNBCell = conditionDlg.isSecondNBCell;

            cond.IsRadioNew = conditionDlg.isRadioNew;
            cond.xlsFileName = conditionDlg.XlsFileName;
            return cond;
        }

        #region Export Excel
        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            exportExcel(false);
        }

        #region 按照小区对导出，导出前去重 
        private void miExportExcelPair_Click(object sender, EventArgs e)
        {
            exportExcel(true);
        }
        #endregion

        /// <summary>
        /// </summary>
        /// <param name="exportExcelMode">0，按照每个节点导出：1，导出小区对，导出前，需要去重（A-B,B-A的情况只保留A-B）</param>
        private void exportExcel(bool isPeerMode)
        {
            Dictionary<string, int> peerDic = new Dictionary<string, int>();

            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.cellValues.Add("主小区名");
            addCellInfo(row);
            row.cellValues.Add("核查方法");
            row.cellValues.Add("同频同PCI小区名");
            addCellInfo(row);
            row.cellValues.Add("距离");
            row.cellValues.Add("夹角");
            rows.Add(row);
            int rowCount = 1;
            foreach (SameEarfcnPciCell srcCell in curViewList)
            {
                foreach (string type in srcCell.cellsTypeDic.Keys)
                {
                    foreach (SameEarfcnPciCellItem cellItem in srcCell.cellsTypeDic[type].cellItems)
                    {
                        bool isFull = addValidRowData(isPeerMode, peerDic, rows, ref rowCount, srcCell, type, cellItem);
                        if (isFull)
                        {
                            return;
                        }
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private bool addValidRowData(bool isPeerMode, Dictionary<string, int> peerDic, List<NPOIRow> rows, ref int rowCount, SameEarfcnPciCell srcCell, string type, SameEarfcnPciCellItem cellItem)
        {
            if (isPeerMode)
            {
                string strPeer = srcCell.selectedCell.Name + "|" + cellItem.CellName;
                string strPeer2 = cellItem.CellName + "|" + srcCell.selectedCell.Name;

                if (!peerDic.ContainsKey(strPeer) && !peerDic.ContainsKey(strPeer2)) //小区对存在
                {
                    peerDic.Add(strPeer, 1);

                    rowCount++;
                    if (rowCount > 65535)
                    {
                        return false;
                    }
                    addRowData(rows, srcCell, type, cellItem);
                }
            }
            else
            {
                rowCount++;
                if (rowCount > 65535)
                {
                    return false;
                }
                addRowData(rows, srcCell, type, cellItem);
            }

            return true;
        }

        private void addRowData(List<NPOIRow> rows, SameEarfcnPciCell srcCell, string type, SameEarfcnPciCellItem cellItem)
        {
            NPOIRow row = new NPOIRow();
            row.cellValues.Add(srcCell.selectedCell.Name);
            if (conditionDlg.isRadioNew)
            {
                row.cellValues.Add(srcCell.selectedCell.CellID);
            }
            else
            {
                row.cellValues.Add(srcCell.selectedCell.SCellID);
            }
            row.cellValues.Add(srcCell.selectedCell.EARFCN);
            row.cellValues.Add(srcCell.selectedCell.PCI);
            if (conditionDlg.isRadioNew)
            {
                row.cellValues.Add(srcCell.selectedCell.LongitudeTemp);
                row.cellValues.Add(srcCell.selectedCell.LatitudeTemp);
            }
            else
            {
                row.cellValues.Add(srcCell.selectedCell.Longitude);
                row.cellValues.Add(srcCell.selectedCell.Latitude);
            }
            row.cellValues.Add(srcCell.cellsTypeDic[type].typeName);
            row.cellValues.Add(cellItem.CellName);
            row.cellValues.Add(cellItem.CellID);
            row.cellValues.Add(cellItem.Earfcn);
            row.cellValues.Add(cellItem.Pci);
            row.cellValues.Add(cellItem.Longitude);
            row.cellValues.Add(cellItem.Latitude);
            row.cellValues.Add(Math.Round(cellItem.Distance, 2));
            row.cellValues.Add(cellItem.InterAngle);
            rows.Add(row);
        }

        private static void addCellInfo(NPOIRow row)
        {
            row.cellValues.Add("小区ID");
            row.cellValues.Add("EARFCN");
            row.cellValues.Add("PCI");
            row.cellValues.Add("经度");
            row.cellValues.Add("纬度");
        }
        #endregion


        private void TreeListView_DoubleClick(object sender, EventArgs e)
        {
            if (conditionDlg.isRadioNew)
            {
                return;
            }
            MainModel.SelectedLTECell = null;
            if (MainModel.SelectedLTECells != null)
            {
                MainModel.SelectedLTECells.Clear();
            }
            else
            {
                MainModel.SelectedLTECells = new List<LTECell>();
            }
            
            if (treeListView.SelectedObject is SameEarfcnPciCell)
            {
                SameEarfcnPciCell interfereCell = treeListView.SelectedObject as SameEarfcnPciCell;
                MainModel.SelectedLTECell = interfereCell.selectedCell;
                MainModel.MainForm.GetMapForm().GoToView(MainModel.SelectedLTECell.Longitude, MainModel.SelectedLTECell.Latitude);
            }
            else if (treeListView.SelectedObject is SameEarfcnPciCellType)
            {
                SameEarfcnPciCellType interfereType = treeListView.SelectedObject as SameEarfcnPciCellType;
                if (interfereType.cellItems.Count > 0)
                {
                    foreach (SameEarfcnPciCellItem cellItem in interfereType.cellItems)
                    {
                        MainModel.SelectedLTECells.Add(cellItem.LteCell);
                    }
                    GoToView(interfereType.cellItems);
                }
            }
            else if (treeListView.SelectedObject is SameEarfcnPciCellItem)
            {
                MainModel.SelectedLTECell = (treeListView.SelectedObject as SameEarfcnPciCellItem).LteCell;
                MainModel.MainForm.GetMapForm().GoToView(MainModel.SelectedLTECell.Longitude, MainModel.SelectedLTECell.Latitude);
            }
        }

        private void GoToView(List<SameEarfcnPciCellItem> cellList)
        {
            double ltLong = 100000;
            double ltLat = -100000;
            double brLong = -100000;
            double brLat = 100000;

            foreach (SameEarfcnPciCellItem cellInfo in cellList)
            {
                if (cellInfo.Longitude < ltLong)
                {
                    ltLong = cellInfo.Longitude;
                }
                if (cellInfo.Longitude > brLong)
                {
                    brLong = cellInfo.Longitude;
                }
                if (cellInfo.Latitude < brLat)
                {
                    brLat = cellInfo.Latitude;
                }
                if (cellInfo.Latitude > ltLat)
                {
                    ltLat = cellInfo.Latitude;
                }
            }
            MainModel.MainForm.GetMapForm().GoToView((ltLong + brLong) / 2, (ltLat + brLat) / 2, 8000);
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            treeListView.ExpandAll();
        }


        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            treeListView.CollapseAll();
        }
    }
}
