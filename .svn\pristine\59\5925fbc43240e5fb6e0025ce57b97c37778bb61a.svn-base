﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTProblemSummary2G
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ExcelToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.tabData = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xqGrid = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn70 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn71 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn72 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn46 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn69 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.summarryGD = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand48 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand49 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand50 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand51 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand52 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand53 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand54 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand55 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand71 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn42 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand56 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand57 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn28 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand58 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn29 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand59 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn30 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand60 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand61 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand62 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand63 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn31 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand64 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand65 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand66 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn33 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand72 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn43 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand67 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn34 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand68 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn35 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand69 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn36 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand70 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn37 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.summarry = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand6 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand7 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand10 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand11 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand8 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn38 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand12 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand73 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn44 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand9 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn39 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand13 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand14 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand15 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand16 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand17 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn40 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand18 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn41 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand19 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand20 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand21 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand22 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand23 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand24 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand25 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand26 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn45 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.summarryOrderGrid = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand76 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn47 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand77 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn48 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand78 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn49 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand79 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand80 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn50 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand81 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn51 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand82 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn52 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand83 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn53 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand84 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn54 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand85 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn55 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand86 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn56 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand87 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn57 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand88 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn58 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand89 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand90 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn59 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand91 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn60 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand92 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn61 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand93 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand94 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn62 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand95 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn63 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand96 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn64 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand97 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn65 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand98 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn66 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand99 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn67 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn68 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand74 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn73 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand27 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand28 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand29 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand30 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand101 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand31 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand102 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand103 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand32 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand45 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand75 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand104 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand33 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand34 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand35 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand36 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand37 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand38 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand39 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand100 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand40 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand41 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand46 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand42 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand43 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand44 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand47 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabData)).BeginInit();
            this.tabData.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xqGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.summarryGD)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView3)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.summarry)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).BeginInit();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.summarryOrderGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView4)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ExcelToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(125, 26);
            // 
            // ExcelToolStripMenuItem
            // 
            this.ExcelToolStripMenuItem.Name = "ExcelToolStripMenuItem";
            this.ExcelToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.ExcelToolStripMenuItem.Text = "导出Excel";
            this.ExcelToolStripMenuItem.Click += new System.EventHandler(this.ExcelToolStripMenuItem_Click);
            // 
            // gridBand1
            // 
            this.gridBand1.Caption = "gridBand1";
            this.gridBand1.Name = "gridBand1";
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "gridBand2";
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 75;
            // 
            // gridBand3
            // 
            this.gridBand3.Caption = "gridBand3";
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 75;
            // 
            // tabData
            // 
            this.tabData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabData.Location = new System.Drawing.Point(0, 0);
            this.tabData.Name = "tabData";
            this.tabData.SelectedTabPage = this.xtraTabPage1;
            this.tabData.Size = new System.Drawing.Size(1084, 453);
            this.tabData.TabIndex = 8;
            this.tabData.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3,
            this.xtraTabPage4});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.xqGrid);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1077, 423);
            this.xtraTabPage1.Text = "详情";
            // 
            // xqGrid
            // 
            this.xqGrid.ContextMenuStrip = this.contextMenuStrip1;
            this.xqGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xqGrid.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.xqGrid.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.xqGrid.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.xqGrid.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.xqGrid.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode1.RelationName = "Level1";
            this.xqGrid.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.xqGrid.Location = new System.Drawing.Point(0, 0);
            this.xqGrid.MainView = this.bandedGridView2;
            this.xqGrid.Name = "xqGrid";
            this.xqGrid.Size = new System.Drawing.Size(1077, 423);
            this.xqGrid.TabIndex = 7;
            this.xqGrid.UseEmbeddedNavigator = true;
            this.xqGrid.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView2});
            // 
            // bandedGridView2
            // 
            this.bandedGridView2.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand27,
            this.gridBand28,
            this.gridBand29,
            this.gridBand30,
            this.gridBand101,
            this.gridBand31,
            this.gridBand102,
            this.gridBand103,
            this.gridBand32,
            this.gridBand45,
            this.gridBand75,
            this.gridBand104,
            this.gridBand33,
            this.gridBand34,
            this.gridBand35,
            this.gridBand36,
            this.gridBand37,
            this.gridBand38,
            this.gridBand39,
            this.gridBand100,
            this.gridBand40,
            this.gridBand41,
            this.gridBand46,
            this.gridBand42,
            this.gridBand43,
            this.gridBand44,
            this.gridBand47});
            this.bandedGridView2.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn70,
            this.bandedGridColumn5,
            this.bandedGridColumn6,
            this.bandedGridColumn73,
            this.bandedGridColumn7,
            this.bandedGridColumn8,
            this.bandedGridColumn9,
            this.bandedGridColumn10,
            this.bandedGridColumn11,
            this.bandedGridColumn12,
            this.bandedGridColumn13,
            this.bandedGridColumn14,
            this.bandedGridColumn15,
            this.bandedGridColumn16,
            this.bandedGridColumn17,
            this.bandedGridColumn18,
            this.bandedGridColumn19,
            this.bandedGridColumn20,
            this.bandedGridColumn21,
            this.bandedGridColumn46,
            this.bandedGridColumn69,
            this.bandedGridColumn71,
            this.bandedGridColumn72});
            this.bandedGridView2.GridControl = this.xqGrid;
            this.bandedGridView2.Name = "bandedGridView2";
            this.bandedGridView2.OptionsBehavior.Editable = false;
            this.bandedGridView2.OptionsBehavior.FocusLeaveOnTab = true;
            this.bandedGridView2.OptionsSelection.MultiSelect = true;
            this.bandedGridView2.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView2.OptionsView.ShowColumnHeaders = false;
            this.bandedGridView2.OptionsView.ShowGroupPanel = false;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.AppearanceHeader.Options.UseTextOptions = true;
            this.bandedGridColumn1.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridColumn1.Caption = "序号";
            this.bandedGridColumn1.FieldName = "IID";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            this.bandedGridColumn1.Width = 45;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "轮次";
            this.bandedGridColumn2.FieldName = "STurn";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            this.bandedGridColumn2.Width = 55;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "城市";
            this.bandedGridColumn3.FieldName = "SCity";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            this.bandedGridColumn3.Width = 46;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "测试日期";
            this.bandedGridColumn4.FieldName = "STestTime";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.Visible = true;
            this.bandedGridColumn4.Width = 70;
            // 
            // bandedGridColumn70
            // 
            this.bandedGridColumn70.Caption = "项目类型";
            this.bandedGridColumn70.FieldName = "SProject";
            this.bandedGridColumn70.Name = "bandedGridColumn70";
            this.bandedGridColumn70.Visible = true;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "测试点名称";
            this.bandedGridColumn5.FieldName = "STestPoint";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.Visible = true;
            // 
            // bandedGridColumn71
            // 
            this.bandedGridColumn71.Caption = "测试点经度";
            this.bandedGridColumn71.FieldName = "DLongitude";
            this.bandedGridColumn71.Name = "bandedGridColumn71";
            this.bandedGridColumn71.Visible = true;
            // 
            // bandedGridColumn72
            // 
            this.bandedGridColumn72.Caption = "测试点纬度";
            this.bandedGridColumn72.FieldName = "DLatitude";
            this.bandedGridColumn72.Name = "bandedGridColumn72";
            this.bandedGridColumn72.Visible = true;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "测试点属性";
            this.bandedGridColumn6.FieldName = "SSitePro";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.Visible = true;
            this.bandedGridColumn6.Width = 80;
            // 
            // bandedGridColumn19
            // 
            this.bandedGridColumn19.Caption = "场所属性";
            this.bandedGridColumn19.FieldName = "Strcssx";
            this.bandedGridColumn19.Name = "bandedGridColumn19";
            this.bandedGridColumn19.Visible = true;
            // 
            // bandedGridColumn46
            // 
            this.bandedGridColumn46.Caption = "覆盖属性";
            this.bandedGridColumn46.FieldName = "StrCoverType";
            this.bandedGridColumn46.Name = "bandedGridColumn46";
            this.bandedGridColumn46.Visible = true;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "重要等级";
            this.bandedGridColumn7.FieldName = "SImportLeve";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.Visible = true;
            this.bandedGridColumn7.Width = 60;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "主问题类别";
            this.bandedGridColumn8.FieldName = "SMainType";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.Visible = true;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "次问题类别";
            this.bandedGridColumn9.FieldName = "SSecondType";
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.Visible = true;
            // 
            // bandedGridColumn10
            // 
            this.bandedGridColumn10.Caption = "问题点位置";
            this.bandedGridColumn10.FieldName = "SPointPosition";
            this.bandedGridColumn10.Name = "bandedGridColumn10";
            this.bandedGridColumn10.Visible = true;
            // 
            // bandedGridColumn11
            // 
            this.bandedGridColumn11.Caption = "测试文件存放路径";
            this.bandedGridColumn11.FieldName = "STestFilePosition";
            this.bandedGridColumn11.Name = "bandedGridColumn11";
            this.bandedGridColumn11.Visible = true;
            this.bandedGridColumn11.Width = 80;
            // 
            // bandedGridColumn12
            // 
            this.bandedGridColumn12.Caption = "原因分析";
            this.bandedGridColumn12.FieldName = "SReasonAna";
            this.bandedGridColumn12.Name = "bandedGridColumn12";
            this.bandedGridColumn12.Visible = true;
            this.bandedGridColumn12.Width = 60;
            // 
            // bandedGridColumn13
            // 
            this.bandedGridColumn13.Caption = "原因类别";
            this.bandedGridColumn13.FieldName = "SReasonType";
            this.bandedGridColumn13.Name = "bandedGridColumn13";
            this.bandedGridColumn13.Visible = true;
            this.bandedGridColumn13.Width = 60;
            // 
            // bandedGridColumn69
            // 
            this.bandedGridColumn69.Caption = "初步分析";
            this.bandedGridColumn69.FieldName = "SPreliminaryAna";
            this.bandedGridColumn69.Name = "bandedGridColumn69";
            this.bandedGridColumn69.Visible = true;
            // 
            // bandedGridColumn14
            // 
            this.bandedGridColumn14.Caption = "是否已解决";
            this.bandedGridColumn14.FieldName = "SSolve";
            this.bandedGridColumn14.Name = "bandedGridColumn14";
            this.bandedGridColumn14.Visible = true;
            this.bandedGridColumn14.Width = 70;
            // 
            // bandedGridColumn15
            // 
            this.bandedGridColumn15.Caption = "主要问题点原因分析";
            this.bandedGridColumn15.FieldName = "SPointAna";
            this.bandedGridColumn15.Name = "bandedGridColumn15";
            this.bandedGridColumn15.Visible = true;
            this.bandedGridColumn15.Width = 90;
            // 
            // bandedGridColumn20
            // 
            this.bandedGridColumn20.Caption = "次要问题点原因分析";
            this.bandedGridColumn20.FieldName = "Strcywtyyfx";
            this.bandedGridColumn20.Name = "bandedGridColumn20";
            this.bandedGridColumn20.Visible = true;
            this.bandedGridColumn20.Width = 90;
            // 
            // bandedGridColumn16
            // 
            this.bandedGridColumn16.Caption = "问题点类型";
            this.bandedGridColumn16.FieldName = "SPointType";
            this.bandedGridColumn16.Name = "bandedGridColumn16";
            this.bandedGridColumn16.Visible = true;
            this.bandedGridColumn16.Width = 70;
            // 
            // bandedGridColumn17
            // 
            this.bandedGridColumn17.Caption = "解决方案";
            this.bandedGridColumn17.FieldName = "SSolveScheme";
            this.bandedGridColumn17.Name = "bandedGridColumn17";
            this.bandedGridColumn17.Visible = true;
            this.bandedGridColumn17.Width = 60;
            // 
            // bandedGridColumn18
            // 
            this.bandedGridColumn18.Caption = "预计解决时间";
            this.bandedGridColumn18.FieldName = "SExceptSolveTime";
            this.bandedGridColumn18.Name = "bandedGridColumn18";
            this.bandedGridColumn18.Visible = true;
            this.bandedGridColumn18.Width = 91;
            // 
            // bandedGridColumn21
            // 
            this.bandedGridColumn21.Caption = "如因为物业或工程建设原因，请写明具体理由";
            this.bandedGridColumn21.FieldName = "Strbz";
            this.bandedGridColumn21.Name = "bandedGridColumn21";
            this.bandedGridColumn21.Visible = true;
            this.bandedGridColumn21.Width = 100;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.summarryGD);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1077, 423);
            this.xtraTabPage2.Text = "汇总";
            // 
            // summarryGD
            // 
            this.summarryGD.ContextMenuStrip = this.contextMenuStrip1;
            this.summarryGD.Dock = System.Windows.Forms.DockStyle.Fill;
            this.summarryGD.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.summarryGD.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.summarryGD.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.summarryGD.EmbeddedNavigator.Buttons.EndEdit.Enabled = false;
            this.summarryGD.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.summarryGD.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.summarryGD.Location = new System.Drawing.Point(0, 0);
            this.summarryGD.MainView = this.bandedGridView3;
            this.summarryGD.Name = "summarryGD";
            this.summarryGD.Size = new System.Drawing.Size(1077, 423);
            this.summarryGD.TabIndex = 5;
            this.summarryGD.UseEmbeddedNavigator = true;
            this.summarryGD.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView3});
            // 
            // bandedGridView3
            // 
            this.bandedGridView3.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand48,
            this.gridBand49,
            this.gridBand50,
            this.gridBand51,
            this.gridBand60,
            this.gridBand64,
            this.gridBand68,
            this.gridBand69,
            this.gridBand70});
            this.bandedGridView3.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn22,
            this.bandedGridColumn23,
            this.bandedGridColumn24,
            this.bandedGridColumn25,
            this.bandedGridColumn26,
            this.bandedGridColumn27,
            this.bandedGridColumn28,
            this.bandedGridColumn29,
            this.bandedGridColumn30,
            this.bandedGridColumn31,
            this.bandedGridColumn32,
            this.bandedGridColumn33,
            this.bandedGridColumn34,
            this.bandedGridColumn35,
            this.bandedGridColumn36,
            this.bandedGridColumn37,
            this.bandedGridColumn42,
            this.bandedGridColumn43});
            this.bandedGridView3.GridControl = this.summarryGD;
            this.bandedGridView3.Name = "bandedGridView3";
            this.bandedGridView3.OptionsBehavior.Editable = false;
            this.bandedGridView3.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView3.OptionsView.ShowColumnHeaders = false;
            this.bandedGridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridBand48
            // 
            this.gridBand48.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand48.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand48.Caption = "项目";
            this.gridBand48.Columns.Add(this.bandedGridColumn22);
            this.gridBand48.Name = "gridBand48";
            this.gridBand48.Width = 55;
            // 
            // bandedGridColumn22
            // 
            this.bandedGridColumn22.Caption = "项目";
            this.bandedGridColumn22.FieldName = "SProject";
            this.bandedGridColumn22.Name = "bandedGridColumn22";
            this.bandedGridColumn22.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.bandedGridColumn22.Visible = true;
            this.bandedGridColumn22.Width = 55;
            // 
            // gridBand49
            // 
            this.gridBand49.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand49.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand49.Caption = "城市类别";
            this.gridBand49.Columns.Add(this.bandedGridColumn23);
            this.gridBand49.Name = "gridBand49";
            this.gridBand49.Width = 74;
            // 
            // bandedGridColumn23
            // 
            this.bandedGridColumn23.Caption = "城市类别";
            this.bandedGridColumn23.FieldName = "SCityType";
            this.bandedGridColumn23.Name = "bandedGridColumn23";
            this.bandedGridColumn23.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.bandedGridColumn23.Visible = true;
            this.bandedGridColumn23.Width = 74;
            // 
            // gridBand50
            // 
            this.gridBand50.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand50.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand50.Caption = "城市";
            this.gridBand50.Columns.Add(this.bandedGridColumn24);
            this.gridBand50.Name = "gridBand50";
            this.gridBand50.Width = 55;
            // 
            // bandedGridColumn24
            // 
            this.bandedGridColumn24.Caption = "城市";
            this.bandedGridColumn24.FieldName = "SCity";
            this.bandedGridColumn24.Name = "bandedGridColumn24";
            this.bandedGridColumn24.Visible = true;
            this.bandedGridColumn24.Width = 55;
            // 
            // gridBand51
            // 
            this.gridBand51.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand51.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand51.Caption = "问题点统计";
            this.gridBand51.Children.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand52,
            this.gridBand53,
            this.gridBand54,
            this.gridBand55,
            this.gridBand71,
            this.gridBand56,
            this.gridBand57,
            this.gridBand58,
            this.gridBand59});
            this.gridBand51.Name = "gridBand51";
            this.gridBand51.Width = 359;
            // 
            // gridBand52
            // 
            this.gridBand52.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand52.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand52.Caption = "弱覆盖";
            this.gridBand52.Name = "gridBand52";
            this.gridBand52.Visible = false;
            this.gridBand52.Width = 50;
            // 
            // gridBand53
            // 
            this.gridBand53.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand53.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand53.Caption = "话音质量差";
            this.gridBand53.Name = "gridBand53";
            this.gridBand53.Visible = false;
            this.gridBand53.Width = 75;
            // 
            // gridBand54
            // 
            this.gridBand54.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand54.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand54.Caption = "未接通";
            this.gridBand54.Columns.Add(this.bandedGridColumn25);
            this.gridBand54.Name = "gridBand54";
            this.gridBand54.Width = 54;
            // 
            // bandedGridColumn25
            // 
            this.bandedGridColumn25.Caption = "未接通";
            this.bandedGridColumn25.FieldName = "INotConnected";
            this.bandedGridColumn25.Name = "bandedGridColumn25";
            this.bandedGridColumn25.Visible = true;
            this.bandedGridColumn25.Width = 54;
            // 
            // gridBand55
            // 
            this.gridBand55.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand55.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand55.Caption = "掉话";
            this.gridBand55.Columns.Add(this.bandedGridColumn26);
            this.gridBand55.Name = "gridBand55";
            this.gridBand55.Width = 68;
            // 
            // bandedGridColumn26
            // 
            this.bandedGridColumn26.Caption = "掉话";
            this.bandedGridColumn26.FieldName = "IDroppedCalls";
            this.bandedGridColumn26.Name = "bandedGridColumn26";
            this.bandedGridColumn26.Visible = true;
            this.bandedGridColumn26.Width = 68;
            // 
            // gridBand71
            // 
            this.gridBand71.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand71.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand71.Caption = "GSM深度 覆盖不足";
            this.gridBand71.Columns.Add(this.bandedGridColumn42);
            this.gridBand71.Name = "gridBand71";
            this.gridBand71.RowCount = 2;
            this.gridBand71.Width = 65;
            // 
            // bandedGridColumn42
            // 
            this.bandedGridColumn42.Caption = "GSM深度覆盖不足";
            this.bandedGridColumn42.FieldName = "ILessthendeep";
            this.bandedGridColumn42.Name = "bandedGridColumn42";
            this.bandedGridColumn42.Visible = true;
            this.bandedGridColumn42.Width = 65;
            // 
            // gridBand56
            // 
            this.gridBand56.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand56.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand56.Caption = "下载速率低";
            this.gridBand56.Columns.Add(this.bandedGridColumn27);
            this.gridBand56.Name = "gridBand56";
            this.gridBand56.Width = 87;
            // 
            // bandedGridColumn27
            // 
            this.bandedGridColumn27.Caption = "下载速率低";
            this.bandedGridColumn27.FieldName = "IDownLess";
            this.bandedGridColumn27.Name = "bandedGridColumn27";
            this.bandedGridColumn27.Visible = true;
            this.bandedGridColumn27.Width = 87;
            // 
            // gridBand57
            // 
            this.gridBand57.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand57.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand57.Caption = "下载掉线";
            this.gridBand57.Columns.Add(this.bandedGridColumn28);
            this.gridBand57.Name = "gridBand57";
            this.gridBand57.Visible = false;
            // 
            // bandedGridColumn28
            // 
            this.bandedGridColumn28.Caption = "下载掉线";
            this.bandedGridColumn28.FieldName = "IDownDrop";
            this.bandedGridColumn28.Name = "bandedGridColumn28";
            this.bandedGridColumn28.Visible = true;
            this.bandedGridColumn28.Width = 70;
            // 
            // gridBand58
            // 
            this.gridBand58.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand58.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand58.Caption = "下载超时";
            this.gridBand58.Columns.Add(this.bandedGridColumn29);
            this.gridBand58.Name = "gridBand58";
            this.gridBand58.Visible = false;
            // 
            // bandedGridColumn29
            // 
            this.bandedGridColumn29.Caption = "下载超时";
            this.bandedGridColumn29.FieldName = "IDownTimeOut";
            this.bandedGridColumn29.Name = "bandedGridColumn29";
            this.bandedGridColumn29.Visible = true;
            this.bandedGridColumn29.Width = 70;
            // 
            // gridBand59
            // 
            this.gridBand59.AppearanceHeader.BackColor = System.Drawing.Color.White;
            this.gridBand59.AppearanceHeader.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridBand59.AppearanceHeader.Options.UseBackColor = true;
            this.gridBand59.AppearanceHeader.Options.UseFont = true;
            this.gridBand59.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand59.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand59.Caption = "问题点汇总";
            this.gridBand59.Columns.Add(this.bandedGridColumn30);
            this.gridBand59.Name = "gridBand59";
            this.gridBand59.RowCount = 2;
            this.gridBand59.Width = 85;
            // 
            // bandedGridColumn30
            // 
            this.bandedGridColumn30.Caption = "指标未达标点汇总";
            this.bandedGridColumn30.FieldName = "ILessThenCole";
            this.bandedGridColumn30.Name = "bandedGridColumn30";
            this.bandedGridColumn30.Visible = true;
            this.bandedGridColumn30.Width = 85;
            // 
            // gridBand60
            // 
            this.gridBand60.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand60.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand60.Caption = "不达标点统计";
            this.gridBand60.Children.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand61,
            this.gridBand62,
            this.gridBand63});
            this.gridBand60.Name = "gridBand60";
            this.gridBand60.Visible = false;
            this.gridBand60.Width = 205;
            // 
            // gridBand61
            // 
            this.gridBand61.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand61.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand61.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand61.Caption = "GSM深度覆 盖不达标";
            this.gridBand61.Name = "gridBand61";
            // 
            // gridBand62
            // 
            this.gridBand62.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand62.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand62.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand62.Caption = "TD深度覆 盖不达标";
            this.gridBand62.Name = "gridBand62";
            this.gridBand62.Width = 65;
            // 
            // gridBand63
            // 
            this.gridBand63.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand63.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand63.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand63.Caption = "不达标 点汇总";
            this.gridBand63.Columns.Add(this.bandedGridColumn31);
            this.gridBand63.Name = "gridBand63";
            // 
            // bandedGridColumn31
            // 
            this.bandedGridColumn31.Caption = "G不达标点汇总";
            this.bandedGridColumn31.FieldName = "ILessThenDeepCole";
            this.bandedGridColumn31.Name = "bandedGridColumn31";
            this.bandedGridColumn31.Visible = true;
            this.bandedGridColumn31.Width = 70;
            // 
            // gridBand64
            // 
            this.gridBand64.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand64.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand64.Caption = "劣势点统计";
            this.gridBand64.Children.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand65,
            this.gridBand66,
            this.gridBand72,
            this.gridBand67});
            this.gridBand64.Name = "gridBand64";
            this.gridBand64.Width = 157;
            // 
            // gridBand65
            // 
            this.gridBand65.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand65.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand65.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand65.Caption = "劣于1家 竞争对手";
            this.gridBand65.Columns.Add(this.bandedGridColumn32);
            this.gridBand65.Name = "gridBand65";
            this.gridBand65.Visible = false;
            // 
            // bandedGridColumn32
            // 
            this.bandedGridColumn32.Caption = "G劣1";
            this.bandedGridColumn32.FieldName = "ILessG1";
            this.bandedGridColumn32.Name = "bandedGridColumn32";
            this.bandedGridColumn32.Visible = true;
            this.bandedGridColumn32.Width = 70;
            // 
            // gridBand66
            // 
            this.gridBand66.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand66.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand66.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand66.Caption = "劣于2家 竞争对手";
            this.gridBand66.Columns.Add(this.bandedGridColumn33);
            this.gridBand66.Name = "gridBand66";
            this.gridBand66.RowCount = 2;
            this.gridBand66.Visible = false;
            this.gridBand66.Width = 82;
            // 
            // bandedGridColumn33
            // 
            this.bandedGridColumn33.Caption = "G劣2";
            this.bandedGridColumn33.FieldName = "ILessG2";
            this.bandedGridColumn33.Name = "bandedGridColumn33";
            this.bandedGridColumn33.Visible = true;
            this.bandedGridColumn33.Width = 82;
            // 
            // gridBand72
            // 
            this.gridBand72.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand72.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand72.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridBand72.Caption = "G劣";
            this.gridBand72.Columns.Add(this.bandedGridColumn43);
            this.gridBand72.Name = "gridBand72";
            this.gridBand72.Width = 75;
            // 
            // bandedGridColumn43
            // 
            this.bandedGridColumn43.Caption = "G劣";
            this.bandedGridColumn43.FieldName = "ILessGHG";
            this.bandedGridColumn43.Name = "bandedGridColumn43";
            this.bandedGridColumn43.Visible = true;
            // 
            // gridBand67
            // 
            this.gridBand67.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand67.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand67.Caption = "劣势点汇总";
            this.gridBand67.Columns.Add(this.bandedGridColumn34);
            this.gridBand67.Name = "gridBand67";
            this.gridBand67.Width = 82;
            // 
            // bandedGridColumn34
            // 
            this.bandedGridColumn34.Caption = "G劣点";
            this.bandedGridColumn34.FieldName = "ILessG";
            this.bandedGridColumn34.Name = "bandedGridColumn34";
            this.bandedGridColumn34.Visible = true;
            this.bandedGridColumn34.Width = 82;
            // 
            // gridBand68
            // 
            this.gridBand68.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand68.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand68.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand68.Caption = "地市总问 题点数";
            this.gridBand68.Columns.Add(this.bandedGridColumn35);
            this.gridBand68.Name = "gridBand68";
            this.gridBand68.Width = 81;
            // 
            // bandedGridColumn35
            // 
            this.bandedGridColumn35.Caption = "地市总问题点数";
            this.bandedGridColumn35.FieldName = "ICityColePoint";
            this.bandedGridColumn35.Name = "bandedGridColumn35";
            this.bandedGridColumn35.Visible = true;
            this.bandedGridColumn35.Width = 81;
            // 
            // gridBand69
            // 
            this.gridBand69.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand69.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand69.Caption = "测试总数";
            this.gridBand69.Columns.Add(this.bandedGridColumn36);
            this.gridBand69.Name = "gridBand69";
            this.gridBand69.Width = 97;
            // 
            // bandedGridColumn36
            // 
            this.bandedGridColumn36.Caption = "测试总数";
            this.bandedGridColumn36.FieldName = "ITestCole";
            this.bandedGridColumn36.Name = "bandedGridColumn36";
            this.bandedGridColumn36.Visible = true;
            this.bandedGridColumn36.Width = 97;
            // 
            // gridBand70
            // 
            this.gridBand70.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand70.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand70.Caption = "测试通过率";
            this.gridBand70.Columns.Add(this.bandedGridColumn37);
            this.gridBand70.Name = "gridBand70";
            this.gridBand70.Width = 125;
            // 
            // bandedGridColumn37
            // 
            this.bandedGridColumn37.Caption = "测试通过率";
            this.bandedGridColumn37.FieldName = "ITestSucess";
            this.bandedGridColumn37.Name = "bandedGridColumn37";
            this.bandedGridColumn37.Visible = true;
            this.bandedGridColumn37.Width = 125;
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.summarry);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(1077, 423);
            this.xtraTabPage3.Text = "汇总";
            // 
            // summarry
            // 
            this.summarry.ContextMenuStrip = this.contextMenuStrip1;
            this.summarry.Dock = System.Windows.Forms.DockStyle.Fill;
            this.summarry.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.summarry.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.summarry.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.summarry.EmbeddedNavigator.Buttons.EndEdit.Enabled = false;
            this.summarry.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.summarry.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.summarry.Location = new System.Drawing.Point(0, 0);
            this.summarry.MainView = this.bandedGridView1;
            this.summarry.Name = "summarry";
            this.summarry.Size = new System.Drawing.Size(1077, 423);
            this.summarry.TabIndex = 4;
            this.summarry.UseEmbeddedNavigator = true;
            this.summarry.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView1});
            // 
            // bandedGridView1
            // 
            this.bandedGridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand4,
            this.gridBand5,
            this.gridBand6,
            this.gridBand7,
            this.gridBand16,
            this.gridBand20,
            this.gridBand24,
            this.gridBand25,
            this.gridBand26});
            this.bandedGridView1.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.bandedGridColumn38,
            this.bandedGridColumn39,
            this.bandedGridColumn40,
            this.bandedGridColumn41,
            this.bandedGridColumn44,
            this.bandedGridColumn45});
            this.bandedGridView1.GridControl = this.summarry;
            this.bandedGridView1.Name = "bandedGridView1";
            this.bandedGridView1.OptionsBehavior.Editable = false;
            this.bandedGridView1.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView1.OptionsView.ShowColumnHeaders = false;
            this.bandedGridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridBand4
            // 
            this.gridBand4.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand4.Caption = "项目";
            this.gridBand4.Columns.Add(this.gridColumn1);
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 55;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "项目";
            this.gridColumn1.FieldName = "SProject";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn1.Visible = true;
            this.gridColumn1.Width = 55;
            // 
            // gridBand5
            // 
            this.gridBand5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand5.Caption = "城市类别";
            this.gridBand5.Columns.Add(this.gridColumn2);
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 74;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "城市类别";
            this.gridColumn2.FieldName = "SCityType";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn2.Visible = true;
            this.gridColumn2.Width = 74;
            // 
            // gridBand6
            // 
            this.gridBand6.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand6.Caption = "城市";
            this.gridBand6.Columns.Add(this.gridColumn3);
            this.gridBand6.Name = "gridBand6";
            this.gridBand6.Width = 55;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "城市";
            this.gridColumn3.FieldName = "SCity";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.Width = 55;
            // 
            // gridBand7
            // 
            this.gridBand7.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand7.Caption = "问题点统计";
            this.gridBand7.Children.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand10,
            this.gridBand11,
            this.gridBand8,
            this.gridBand12,
            this.gridBand73,
            this.gridBand9,
            this.gridBand13,
            this.gridBand14,
            this.gridBand15});
            this.gridBand7.Name = "gridBand7";
            this.gridBand7.Width = 638;
            // 
            // gridBand10
            // 
            this.gridBand10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand10.Caption = "未接通";
            this.gridBand10.Columns.Add(this.gridColumn6);
            this.gridBand10.Name = "gridBand10";
            this.gridBand10.Width = 54;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "未接通";
            this.gridColumn6.FieldName = "INotConnected";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.Width = 54;
            // 
            // gridBand11
            // 
            this.gridBand11.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand11.Caption = "掉话";
            this.gridBand11.Columns.Add(this.gridColumn7);
            this.gridBand11.Name = "gridBand11";
            this.gridBand11.Width = 68;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "掉话";
            this.gridColumn7.FieldName = "IDroppedCalls";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.Width = 68;
            // 
            // gridBand8
            // 
            this.gridBand8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand8.Caption = "弱覆盖";
            this.gridBand8.Columns.Add(this.bandedGridColumn38);
            this.gridBand8.Name = "gridBand8";
            this.gridBand8.Width = 54;
            // 
            // bandedGridColumn38
            // 
            this.bandedGridColumn38.Caption = "弱覆盖";
            this.bandedGridColumn38.FieldName = "IWeakCoverage";
            this.bandedGridColumn38.Name = "bandedGridColumn38";
            this.bandedGridColumn38.Visible = true;
            this.bandedGridColumn38.Width = 54;
            // 
            // gridBand12
            // 
            this.gridBand12.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand12.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand12.Caption = "下载速率低";
            this.gridBand12.Columns.Add(this.gridColumn8);
            this.gridBand12.Name = "gridBand12";
            this.gridBand12.Width = 87;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "下载速率低";
            this.gridColumn8.FieldName = "IDownLess";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.Width = 87;
            // 
            // gridBand73
            // 
            this.gridBand73.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand73.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand73.Caption = "感知掉话";
            this.gridBand73.Columns.Add(this.bandedGridColumn44);
            this.gridBand73.Name = "gridBand73";
            this.gridBand73.Width = 75;
            // 
            // bandedGridColumn44
            // 
            this.bandedGridColumn44.Caption = "感知掉话";
            this.bandedGridColumn44.FieldName = "IFillDropedCall";
            this.bandedGridColumn44.Name = "bandedGridColumn44";
            this.bandedGridColumn44.Visible = true;
            // 
            // gridBand9
            // 
            this.gridBand9.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand9.Caption = "话质差";
            this.gridBand9.Columns.Add(this.bandedGridColumn39);
            this.gridBand9.Name = "gridBand9";
            this.gridBand9.Width = 75;
            // 
            // bandedGridColumn39
            // 
            this.bandedGridColumn39.Caption = "话质差";
            this.bandedGridColumn39.FieldName = "IPoorVoice";
            this.bandedGridColumn39.Name = "bandedGridColumn39";
            this.bandedGridColumn39.Visible = true;
            // 
            // gridBand13
            // 
            this.gridBand13.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand13.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand13.Caption = "下载掉线";
            this.gridBand13.Columns.Add(this.gridColumn9);
            this.gridBand13.Name = "gridBand13";
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "下载掉线";
            this.gridColumn9.FieldName = "IDownDrop";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.Width = 70;
            // 
            // gridBand14
            // 
            this.gridBand14.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand14.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand14.Caption = "下载超时";
            this.gridBand14.Columns.Add(this.gridColumn10);
            this.gridBand14.Name = "gridBand14";
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "下载超时";
            this.gridColumn10.FieldName = "IDownTimeOut";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.Width = 70;
            // 
            // gridBand15
            // 
            this.gridBand15.AppearanceHeader.BackColor = System.Drawing.Color.White;
            this.gridBand15.AppearanceHeader.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridBand15.AppearanceHeader.Options.UseBackColor = true;
            this.gridBand15.AppearanceHeader.Options.UseFont = true;
            this.gridBand15.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand15.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand15.Caption = "问题点汇总";
            this.gridBand15.Columns.Add(this.gridColumn11);
            this.gridBand15.Name = "gridBand15";
            this.gridBand15.RowCount = 2;
            this.gridBand15.Width = 85;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "指标未达标点汇总";
            this.gridColumn11.FieldName = "ILessThenCole";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.Width = 85;
            // 
            // gridBand16
            // 
            this.gridBand16.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand16.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand16.Caption = "不达标点统计";
            this.gridBand16.Children.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand17,
            this.gridBand18,
            this.gridBand19});
            this.gridBand16.Name = "gridBand16";
            this.gridBand16.Width = 160;
            // 
            // gridBand17
            // 
            this.gridBand17.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand17.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand17.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand17.Caption = "GSM深度覆 盖不达标";
            this.gridBand17.Columns.Add(this.bandedGridColumn40);
            this.gridBand17.Name = "gridBand17";
            this.gridBand17.Width = 90;
            // 
            // bandedGridColumn40
            // 
            this.bandedGridColumn40.Caption = "GSM深度覆盖不达标";
            this.bandedGridColumn40.FieldName = "ILessThenDeepGSM";
            this.bandedGridColumn40.Name = "bandedGridColumn40";
            this.bandedGridColumn40.Visible = true;
            this.bandedGridColumn40.Width = 90;
            // 
            // gridBand18
            // 
            this.gridBand18.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand18.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand18.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand18.Caption = "TD深度覆 盖不达标";
            this.gridBand18.Columns.Add(this.bandedGridColumn41);
            this.gridBand18.Name = "gridBand18";
            this.gridBand18.Visible = false;
            this.gridBand18.Width = 90;
            // 
            // bandedGridColumn41
            // 
            this.bandedGridColumn41.Caption = "TD深度覆盖不达标";
            this.bandedGridColumn41.FieldName = "ILessThenDeepTD";
            this.bandedGridColumn41.Name = "bandedGridColumn41";
            this.bandedGridColumn41.Visible = true;
            this.bandedGridColumn41.Width = 90;
            // 
            // gridBand19
            // 
            this.gridBand19.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand19.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand19.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand19.Caption = "不达标 点汇总";
            this.gridBand19.Columns.Add(this.gridColumn14);
            this.gridBand19.Name = "gridBand19";
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "G不达标点汇总";
            this.gridColumn14.FieldName = "ILessThenDeepCole";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.Width = 70;
            // 
            // gridBand20
            // 
            this.gridBand20.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand20.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand20.Caption = "劣势点统计";
            this.gridBand20.Children.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand21,
            this.gridBand22,
            this.gridBand23});
            this.gridBand20.Name = "gridBand20";
            this.gridBand20.Width = 234;
            // 
            // gridBand21
            // 
            this.gridBand21.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand21.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand21.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand21.Caption = "劣于1家 竞争对手";
            this.gridBand21.Columns.Add(this.gridColumn15);
            this.gridBand21.Name = "gridBand21";
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "G劣1";
            this.gridColumn15.FieldName = "ILessG1";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.Width = 70;
            // 
            // gridBand22
            // 
            this.gridBand22.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand22.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand22.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand22.Caption = "劣于2家 竞争对手";
            this.gridBand22.Columns.Add(this.gridColumn16);
            this.gridBand22.Name = "gridBand22";
            this.gridBand22.Width = 82;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "G劣2";
            this.gridColumn16.FieldName = "ILessG2";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.Width = 82;
            // 
            // gridBand23
            // 
            this.gridBand23.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand23.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand23.Caption = "劣势点汇总";
            this.gridBand23.Columns.Add(this.gridColumn17);
            this.gridBand23.Name = "gridBand23";
            this.gridBand23.Width = 82;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "G劣点";
            this.gridColumn17.FieldName = "ILessG";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.Width = 82;
            // 
            // gridBand24
            // 
            this.gridBand24.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand24.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand24.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand24.Caption = "地市总问 题点数";
            this.gridBand24.Columns.Add(this.gridColumn18);
            this.gridBand24.Name = "gridBand24";
            this.gridBand24.Width = 81;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "地市总问题点数";
            this.gridColumn18.FieldName = "ICityColePoint";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.Width = 81;
            // 
            // gridBand25
            // 
            this.gridBand25.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand25.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand25.Caption = "测试总数";
            this.gridBand25.Columns.Add(this.gridColumn19);
            this.gridBand25.Name = "gridBand25";
            this.gridBand25.Width = 97;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "测试总数";
            this.gridColumn19.FieldName = "ITestCole";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.Width = 97;
            // 
            // gridBand26
            // 
            this.gridBand26.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand26.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand26.Caption = "测试通过率";
            this.gridBand26.Columns.Add(this.gridColumn20);
            this.gridBand26.Name = "gridBand26";
            this.gridBand26.Width = 125;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "测试通过率";
            this.gridColumn20.FieldName = "ITestSucess";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.Width = 125;
            // 
            // bandedGridColumn45
            // 
            this.bandedGridColumn45.Name = "bandedGridColumn45";
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.summarryOrderGrid);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(1077, 423);
            this.xtraTabPage4.Text = "汇总";
            // 
            // summarryOrderGrid
            // 
            this.summarryOrderGrid.ContextMenuStrip = this.contextMenuStrip1;
            this.summarryOrderGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.summarryOrderGrid.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.summarryOrderGrid.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.summarryOrderGrid.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.summarryOrderGrid.EmbeddedNavigator.Buttons.EndEdit.Enabled = false;
            this.summarryOrderGrid.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.summarryOrderGrid.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.summarryOrderGrid.Location = new System.Drawing.Point(0, 0);
            this.summarryOrderGrid.MainView = this.bandedGridView4;
            this.summarryOrderGrid.Name = "summarryOrderGrid";
            this.summarryOrderGrid.Size = new System.Drawing.Size(1077, 423);
            this.summarryOrderGrid.TabIndex = 5;
            this.summarryOrderGrid.UseEmbeddedNavigator = true;
            this.summarryOrderGrid.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView4});
            // 
            // bandedGridView4
            // 
            this.bandedGridView4.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand76,
            this.gridBand77,
            this.gridBand78,
            this.gridBand79,
            this.gridBand89,
            this.gridBand93,
            this.gridBand97,
            this.gridBand98,
            this.gridBand99});
            this.bandedGridView4.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn47,
            this.bandedGridColumn48,
            this.bandedGridColumn49,
            this.bandedGridColumn50,
            this.bandedGridColumn51,
            this.bandedGridColumn53,
            this.bandedGridColumn56,
            this.bandedGridColumn57,
            this.bandedGridColumn58,
            this.bandedGridColumn61,
            this.bandedGridColumn62,
            this.bandedGridColumn63,
            this.bandedGridColumn64,
            this.bandedGridColumn65,
            this.bandedGridColumn66,
            this.bandedGridColumn67,
            this.bandedGridColumn52,
            this.bandedGridColumn55,
            this.bandedGridColumn59,
            this.bandedGridColumn60,
            this.bandedGridColumn54,
            this.bandedGridColumn68});
            this.bandedGridView4.GridControl = this.summarryOrderGrid;
            this.bandedGridView4.Name = "bandedGridView4";
            this.bandedGridView4.OptionsBehavior.Editable = false;
            this.bandedGridView4.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView4.OptionsView.ShowColumnHeaders = false;
            this.bandedGridView4.OptionsView.ShowGroupPanel = false;
            // 
            // gridBand76
            // 
            this.gridBand76.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand76.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand76.Caption = "项目";
            this.gridBand76.Columns.Add(this.bandedGridColumn47);
            this.gridBand76.Name = "gridBand76";
            this.gridBand76.Width = 96;
            // 
            // bandedGridColumn47
            // 
            this.bandedGridColumn47.Caption = "项目";
            this.bandedGridColumn47.FieldName = "SProject";
            this.bandedGridColumn47.Name = "bandedGridColumn47";
            this.bandedGridColumn47.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.bandedGridColumn47.Visible = true;
            this.bandedGridColumn47.Width = 96;
            // 
            // gridBand77
            // 
            this.gridBand77.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand77.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand77.Caption = "城市类别";
            this.gridBand77.Columns.Add(this.bandedGridColumn48);
            this.gridBand77.Name = "gridBand77";
            this.gridBand77.Width = 74;
            // 
            // bandedGridColumn48
            // 
            this.bandedGridColumn48.Caption = "城市类别";
            this.bandedGridColumn48.FieldName = "SCityType";
            this.bandedGridColumn48.Name = "bandedGridColumn48";
            this.bandedGridColumn48.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.bandedGridColumn48.Visible = true;
            this.bandedGridColumn48.Width = 74;
            // 
            // gridBand78
            // 
            this.gridBand78.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand78.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand78.Caption = "城市";
            this.gridBand78.Columns.Add(this.bandedGridColumn49);
            this.gridBand78.Name = "gridBand78";
            this.gridBand78.Width = 55;
            // 
            // bandedGridColumn49
            // 
            this.bandedGridColumn49.Caption = "城市";
            this.bandedGridColumn49.FieldName = "SCity";
            this.bandedGridColumn49.Name = "bandedGridColumn49";
            this.bandedGridColumn49.Visible = true;
            this.bandedGridColumn49.Width = 55;
            // 
            // gridBand79
            // 
            this.gridBand79.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand79.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand79.Caption = "问题点统计";
            this.gridBand79.Children.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand80,
            this.gridBand81,
            this.gridBand82,
            this.gridBand83,
            this.gridBand84,
            this.gridBand85,
            this.gridBand86,
            this.gridBand87,
            this.gridBand88});
            this.gridBand79.Name = "gridBand79";
            this.gridBand79.Width = 489;
            // 
            // gridBand80
            // 
            this.gridBand80.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand80.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand80.Caption = "未接通";
            this.gridBand80.Columns.Add(this.bandedGridColumn50);
            this.gridBand80.Name = "gridBand80";
            this.gridBand80.Width = 73;
            // 
            // bandedGridColumn50
            // 
            this.bandedGridColumn50.Caption = "未接通";
            this.bandedGridColumn50.FieldName = "INotConnected";
            this.bandedGridColumn50.Name = "bandedGridColumn50";
            this.bandedGridColumn50.Visible = true;
            this.bandedGridColumn50.Width = 73;
            // 
            // gridBand81
            // 
            this.gridBand81.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand81.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand81.Caption = "掉话";
            this.gridBand81.Columns.Add(this.bandedGridColumn51);
            this.gridBand81.Name = "gridBand81";
            this.gridBand81.Width = 98;
            // 
            // bandedGridColumn51
            // 
            this.bandedGridColumn51.Caption = "掉话";
            this.bandedGridColumn51.FieldName = "IDroppedCalls";
            this.bandedGridColumn51.Name = "bandedGridColumn51";
            this.bandedGridColumn51.Visible = true;
            this.bandedGridColumn51.Width = 98;
            // 
            // gridBand82
            // 
            this.gridBand82.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand82.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand82.Caption = "弱覆盖";
            this.gridBand82.Columns.Add(this.bandedGridColumn52);
            this.gridBand82.Name = "gridBand82";
            this.gridBand82.Visible = false;
            this.gridBand82.Width = 54;
            // 
            // bandedGridColumn52
            // 
            this.bandedGridColumn52.Caption = "弱覆盖";
            this.bandedGridColumn52.FieldName = "IWeakCoverage";
            this.bandedGridColumn52.Name = "bandedGridColumn52";
            this.bandedGridColumn52.Visible = true;
            this.bandedGridColumn52.Width = 54;
            // 
            // gridBand83
            // 
            this.gridBand83.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand83.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand83.Caption = "下载速率低";
            this.gridBand83.Columns.Add(this.bandedGridColumn53);
            this.gridBand83.Name = "gridBand83";
            this.gridBand83.Width = 114;
            // 
            // bandedGridColumn53
            // 
            this.bandedGridColumn53.Caption = "下载速率低";
            this.bandedGridColumn53.FieldName = "IDownLess";
            this.bandedGridColumn53.Name = "bandedGridColumn53";
            this.bandedGridColumn53.Visible = true;
            this.bandedGridColumn53.Width = 114;
            // 
            // gridBand84
            // 
            this.gridBand84.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand84.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand84.Caption = "感知掉话";
            this.gridBand84.Columns.Add(this.bandedGridColumn54);
            this.gridBand84.Name = "gridBand84";
            this.gridBand84.Visible = false;
            this.gridBand84.Width = 75;
            // 
            // bandedGridColumn54
            // 
            this.bandedGridColumn54.Caption = "感知掉话";
            this.bandedGridColumn54.FieldName = "IFillDropedCall";
            this.bandedGridColumn54.Name = "bandedGridColumn54";
            this.bandedGridColumn54.Visible = true;
            // 
            // gridBand85
            // 
            this.gridBand85.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand85.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand85.Caption = "话质差";
            this.gridBand85.Columns.Add(this.bandedGridColumn55);
            this.gridBand85.Name = "gridBand85";
            this.gridBand85.Visible = false;
            this.gridBand85.Width = 75;
            // 
            // bandedGridColumn55
            // 
            this.bandedGridColumn55.Caption = "话质差";
            this.bandedGridColumn55.FieldName = "IPoorVoice";
            this.bandedGridColumn55.Name = "bandedGridColumn55";
            this.bandedGridColumn55.Visible = true;
            // 
            // gridBand86
            // 
            this.gridBand86.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand86.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand86.Caption = "下载掉线";
            this.gridBand86.Columns.Add(this.bandedGridColumn56);
            this.gridBand86.Name = "gridBand86";
            this.gridBand86.Width = 92;
            // 
            // bandedGridColumn56
            // 
            this.bandedGridColumn56.Caption = "下载掉线";
            this.bandedGridColumn56.FieldName = "IDownDrop";
            this.bandedGridColumn56.Name = "bandedGridColumn56";
            this.bandedGridColumn56.Visible = true;
            this.bandedGridColumn56.Width = 92;
            // 
            // gridBand87
            // 
            this.gridBand87.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand87.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand87.Caption = "下载超时";
            this.gridBand87.Columns.Add(this.bandedGridColumn57);
            this.gridBand87.Name = "gridBand87";
            this.gridBand87.Visible = false;
            // 
            // bandedGridColumn57
            // 
            this.bandedGridColumn57.Caption = "下载超时";
            this.bandedGridColumn57.FieldName = "IDownTimeOut";
            this.bandedGridColumn57.Name = "bandedGridColumn57";
            this.bandedGridColumn57.Visible = true;
            this.bandedGridColumn57.Width = 70;
            // 
            // gridBand88
            // 
            this.gridBand88.AppearanceHeader.BackColor = System.Drawing.Color.White;
            this.gridBand88.AppearanceHeader.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridBand88.AppearanceHeader.Options.UseBackColor = true;
            this.gridBand88.AppearanceHeader.Options.UseFont = true;
            this.gridBand88.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand88.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand88.Caption = "问题点汇总";
            this.gridBand88.Columns.Add(this.bandedGridColumn58);
            this.gridBand88.Name = "gridBand88";
            this.gridBand88.RowCount = 2;
            this.gridBand88.Width = 112;
            // 
            // bandedGridColumn58
            // 
            this.bandedGridColumn58.Caption = "指标未达标点汇总";
            this.bandedGridColumn58.FieldName = "ILessThenCole";
            this.bandedGridColumn58.Name = "bandedGridColumn58";
            this.bandedGridColumn58.Visible = true;
            this.bandedGridColumn58.Width = 112;
            // 
            // gridBand89
            // 
            this.gridBand89.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand89.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand89.Caption = "不达标点统计";
            this.gridBand89.Children.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand90,
            this.gridBand91,
            this.gridBand92});
            this.gridBand89.Name = "gridBand89";
            this.gridBand89.Visible = false;
            this.gridBand89.Width = 160;
            // 
            // gridBand90
            // 
            this.gridBand90.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand90.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand90.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand90.Caption = "GSM深度覆 盖不达标";
            this.gridBand90.Columns.Add(this.bandedGridColumn59);
            this.gridBand90.Name = "gridBand90";
            this.gridBand90.Width = 90;
            // 
            // bandedGridColumn59
            // 
            this.bandedGridColumn59.Caption = "GSM深度覆盖不达标";
            this.bandedGridColumn59.FieldName = "ILessThenDeepGSM";
            this.bandedGridColumn59.Name = "bandedGridColumn59";
            this.bandedGridColumn59.Visible = true;
            this.bandedGridColumn59.Width = 90;
            // 
            // gridBand91
            // 
            this.gridBand91.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand91.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand91.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand91.Caption = "TD深度覆 盖不达标";
            this.gridBand91.Columns.Add(this.bandedGridColumn60);
            this.gridBand91.Name = "gridBand91";
            this.gridBand91.Visible = false;
            this.gridBand91.Width = 90;
            // 
            // bandedGridColumn60
            // 
            this.bandedGridColumn60.Caption = "TD深度覆盖不达标";
            this.bandedGridColumn60.FieldName = "ILessThenDeepTD";
            this.bandedGridColumn60.Name = "bandedGridColumn60";
            this.bandedGridColumn60.Visible = true;
            this.bandedGridColumn60.Width = 90;
            // 
            // gridBand92
            // 
            this.gridBand92.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand92.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand92.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand92.Caption = "不达标 点汇总";
            this.gridBand92.Columns.Add(this.bandedGridColumn61);
            this.gridBand92.Name = "gridBand92";
            // 
            // bandedGridColumn61
            // 
            this.bandedGridColumn61.Caption = "G不达标点汇总";
            this.bandedGridColumn61.FieldName = "ILessThenDeepCole";
            this.bandedGridColumn61.Name = "bandedGridColumn61";
            this.bandedGridColumn61.Visible = true;
            this.bandedGridColumn61.Width = 70;
            // 
            // gridBand93
            // 
            this.gridBand93.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand93.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand93.Caption = "劣势点统计";
            this.gridBand93.Children.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand94,
            this.gridBand95,
            this.gridBand96});
            this.gridBand93.Name = "gridBand93";
            this.gridBand93.Visible = false;
            this.gridBand93.Width = 234;
            // 
            // gridBand94
            // 
            this.gridBand94.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand94.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand94.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand94.Caption = "劣于1家 竞争对手";
            this.gridBand94.Columns.Add(this.bandedGridColumn62);
            this.gridBand94.Name = "gridBand94";
            // 
            // bandedGridColumn62
            // 
            this.bandedGridColumn62.Caption = "G劣1";
            this.bandedGridColumn62.FieldName = "ILessG1";
            this.bandedGridColumn62.Name = "bandedGridColumn62";
            this.bandedGridColumn62.Visible = true;
            this.bandedGridColumn62.Width = 70;
            // 
            // gridBand95
            // 
            this.gridBand95.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand95.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand95.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand95.Caption = "劣于2家 竞争对手";
            this.gridBand95.Columns.Add(this.bandedGridColumn63);
            this.gridBand95.Name = "gridBand95";
            this.gridBand95.Width = 82;
            // 
            // bandedGridColumn63
            // 
            this.bandedGridColumn63.Caption = "G劣2";
            this.bandedGridColumn63.FieldName = "ILessG2";
            this.bandedGridColumn63.Name = "bandedGridColumn63";
            this.bandedGridColumn63.Visible = true;
            this.bandedGridColumn63.Width = 82;
            // 
            // gridBand96
            // 
            this.gridBand96.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand96.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand96.Caption = "劣势点汇总";
            this.gridBand96.Columns.Add(this.bandedGridColumn64);
            this.gridBand96.Name = "gridBand96";
            this.gridBand96.Width = 82;
            // 
            // bandedGridColumn64
            // 
            this.bandedGridColumn64.Caption = "G劣点";
            this.bandedGridColumn64.FieldName = "ILessG";
            this.bandedGridColumn64.Name = "bandedGridColumn64";
            this.bandedGridColumn64.Visible = true;
            this.bandedGridColumn64.Width = 82;
            // 
            // gridBand97
            // 
            this.gridBand97.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand97.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand97.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand97.Caption = "地市总问 题点数";
            this.gridBand97.Columns.Add(this.bandedGridColumn65);
            this.gridBand97.Name = "gridBand97";
            this.gridBand97.Width = 86;
            // 
            // bandedGridColumn65
            // 
            this.bandedGridColumn65.Caption = "地市总问题点数";
            this.bandedGridColumn65.FieldName = "ICityColePoint";
            this.bandedGridColumn65.Name = "bandedGridColumn65";
            this.bandedGridColumn65.Visible = true;
            this.bandedGridColumn65.Width = 86;
            // 
            // gridBand98
            // 
            this.gridBand98.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand98.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand98.Caption = "测试总数";
            this.gridBand98.Columns.Add(this.bandedGridColumn66);
            this.gridBand98.Name = "gridBand98";
            this.gridBand98.Width = 97;
            // 
            // bandedGridColumn66
            // 
            this.bandedGridColumn66.Caption = "测试总数";
            this.bandedGridColumn66.FieldName = "ITestCole";
            this.bandedGridColumn66.Name = "bandedGridColumn66";
            this.bandedGridColumn66.Visible = true;
            this.bandedGridColumn66.Width = 97;
            // 
            // gridBand99
            // 
            this.gridBand99.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand99.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand99.Caption = "测试通过率";
            this.gridBand99.Columns.Add(this.bandedGridColumn67);
            this.gridBand99.Name = "gridBand99";
            this.gridBand99.Width = 125;
            // 
            // bandedGridColumn67
            // 
            this.bandedGridColumn67.Caption = "测试通过率";
            this.bandedGridColumn67.FieldName = "ITestSucess";
            this.bandedGridColumn67.Name = "bandedGridColumn67";
            this.bandedGridColumn67.Visible = true;
            this.bandedGridColumn67.Width = 125;
            // 
            // bandedGridColumn68
            // 
            this.bandedGridColumn68.Name = "bandedGridColumn68";
            // 
            // gridBand74
            // 
            this.gridBand74.Name = "gridBand74";
            // 
            // bandedGridColumn73
            // 
            this.bandedGridColumn73.Caption = "匹配站点信息";
            this.bandedGridColumn73.FieldName = "StrValue8";
            this.bandedGridColumn73.Name = "bandedGridColumn73";
            this.bandedGridColumn73.Visible = true;
            this.bandedGridColumn73.Width = 91;
            // 
            // gridBand27
            // 
            this.gridBand27.Caption = "序号";
            this.gridBand27.Columns.Add(this.bandedGridColumn1);
            this.gridBand27.Name = "gridBand27";
            this.gridBand27.Width = 45;
            // 
            // gridBand28
            // 
            this.gridBand28.Caption = "轮次";
            this.gridBand28.Columns.Add(this.bandedGridColumn2);
            this.gridBand28.Name = "gridBand28";
            this.gridBand28.Width = 55;
            // 
            // gridBand29
            // 
            this.gridBand29.Caption = "城市";
            this.gridBand29.Columns.Add(this.bandedGridColumn3);
            this.gridBand29.Name = "gridBand29";
            this.gridBand29.RowCount = 2;
            this.gridBand29.Width = 46;
            // 
            // gridBand30
            // 
            this.gridBand30.Caption = "测试日期";
            this.gridBand30.Columns.Add(this.bandedGridColumn4);
            this.gridBand30.Name = "gridBand30";
            // 
            // gridBand101
            // 
            this.gridBand101.Caption = "项目类型";
            this.gridBand101.Columns.Add(this.bandedGridColumn70);
            this.gridBand101.Name = "gridBand101";
            this.gridBand101.Width = 75;
            // 
            // gridBand31
            // 
            this.gridBand31.Caption = "测试点名称";
            this.gridBand31.Columns.Add(this.bandedGridColumn5);
            this.gridBand31.Name = "gridBand31";
            this.gridBand31.Width = 75;
            // 
            // gridBand102
            // 
            this.gridBand102.Caption = "测试点经度";
            this.gridBand102.Columns.Add(this.bandedGridColumn71);
            this.gridBand102.Name = "gridBand102";
            this.gridBand102.Width = 75;
            // 
            // gridBand103
            // 
            this.gridBand103.Caption = "测试点纬度";
            this.gridBand103.Columns.Add(this.bandedGridColumn72);
            this.gridBand103.Name = "gridBand103";
            this.gridBand103.Width = 75;
            // 
            // gridBand32
            // 
            this.gridBand32.Caption = "测试点属性";
            this.gridBand32.Columns.Add(this.bandedGridColumn6);
            this.gridBand32.Name = "gridBand32";
            this.gridBand32.Width = 80;
            // 
            // gridBand45
            // 
            this.gridBand45.Caption = "场所属性";
            this.gridBand45.Columns.Add(this.bandedGridColumn19);
            this.gridBand45.Name = "gridBand45";
            this.gridBand45.Width = 75;
            // 
            // gridBand75
            // 
            this.gridBand75.Caption = "覆盖属性";
            this.gridBand75.Columns.Add(this.bandedGridColumn46);
            this.gridBand75.Name = "gridBand75";
            this.gridBand75.Width = 75;
            // 
            // gridBand104
            // 
            this.gridBand104.Caption = "匹配站点信息";
            this.gridBand104.Columns.Add(this.bandedGridColumn73);
            this.gridBand104.Name = "gridBand104";
            this.gridBand104.Width = 91;
            // 
            // gridBand33
            // 
            this.gridBand33.Caption = "重要等级";
            this.gridBand33.Columns.Add(this.bandedGridColumn7);
            this.gridBand33.Name = "gridBand33";
            this.gridBand33.Width = 60;
            // 
            // gridBand34
            // 
            this.gridBand34.Caption = "主问题类别";
            this.gridBand34.Columns.Add(this.bandedGridColumn8);
            this.gridBand34.Name = "gridBand34";
            this.gridBand34.Width = 75;
            // 
            // gridBand35
            // 
            this.gridBand35.Caption = "次问题类别";
            this.gridBand35.Columns.Add(this.bandedGridColumn9);
            this.gridBand35.Name = "gridBand35";
            this.gridBand35.Width = 75;
            // 
            // gridBand36
            // 
            this.gridBand36.Caption = "问题点位置";
            this.gridBand36.Columns.Add(this.bandedGridColumn10);
            this.gridBand36.Name = "gridBand36";
            this.gridBand36.Width = 75;
            // 
            // gridBand37
            // 
            this.gridBand37.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand37.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand37.Caption = "测试文件 存放路径";
            this.gridBand37.Columns.Add(this.bandedGridColumn11);
            this.gridBand37.Name = "gridBand37";
            this.gridBand37.Width = 80;
            // 
            // gridBand38
            // 
            this.gridBand38.Caption = "原因分析";
            this.gridBand38.Columns.Add(this.bandedGridColumn12);
            this.gridBand38.Name = "gridBand38";
            this.gridBand38.Width = 60;
            // 
            // gridBand39
            // 
            this.gridBand39.Caption = "原因类别";
            this.gridBand39.Columns.Add(this.bandedGridColumn13);
            this.gridBand39.Name = "gridBand39";
            this.gridBand39.Width = 60;
            // 
            // gridBand100
            // 
            this.gridBand100.Caption = "初步分析";
            this.gridBand100.Columns.Add(this.bandedGridColumn69);
            this.gridBand100.Name = "gridBand100";
            this.gridBand100.Width = 75;
            // 
            // gridBand40
            // 
            this.gridBand40.Caption = "是否已解决";
            this.gridBand40.Columns.Add(this.bandedGridColumn14);
            this.gridBand40.Name = "gridBand40";
            // 
            // gridBand41
            // 
            this.gridBand41.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand41.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand41.Caption = "主要问题 点原因分析";
            this.gridBand41.Columns.Add(this.bandedGridColumn15);
            this.gridBand41.Name = "gridBand41";
            this.gridBand41.Width = 90;
            // 
            // gridBand46
            // 
            this.gridBand46.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand46.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand46.Caption = "次要问题 点原因分析";
            this.gridBand46.Columns.Add(this.bandedGridColumn20);
            this.gridBand46.Name = "gridBand46";
            this.gridBand46.Width = 90;
            // 
            // gridBand42
            // 
            this.gridBand42.Caption = "问题点类型";
            this.gridBand42.Columns.Add(this.bandedGridColumn16);
            this.gridBand42.Name = "gridBand42";
            // 
            // gridBand43
            // 
            this.gridBand43.Caption = "解决方案";
            this.gridBand43.Columns.Add(this.bandedGridColumn17);
            this.gridBand43.Name = "gridBand43";
            this.gridBand43.Width = 60;
            // 
            // gridBand44
            // 
            this.gridBand44.Caption = "预计解决时间";
            this.gridBand44.Columns.Add(this.bandedGridColumn18);
            this.gridBand44.Name = "gridBand44";
            this.gridBand44.Width = 91;
            // 
            // gridBand47
            // 
            this.gridBand47.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand47.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridBand47.Caption = "如因为物业或 工程建设原因， 请写明具体理由";
            this.gridBand47.Columns.Add(this.bandedGridColumn21);
            this.gridBand47.Name = "gridBand47";
            this.gridBand47.Width = 100;
            // 
            // CQTProblemSummary2G
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1084, 453);
            this.ContextMenuStrip = this.contextMenuStrip1;
            this.Controls.Add(this.tabData);
            this.Location = new System.Drawing.Point(0, 25);
            this.Name = "CQTProblemSummary2G";
            this.Text = "CQT2G问题点分析";
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabData)).EndInit();
            this.tabData.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xqGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.summarryGD)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView3)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.summarry)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).EndInit();
            this.xtraTabPage4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.summarryOrderGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView4)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ExcelToolStripMenuItem;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraTab.XtraTabControl tabData;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraGrid.GridControl xqGrid;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn21;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraGrid.GridControl summarryGD;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn35;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn36;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn37;
        private DevExpress.XtraGrid.GridControl summarry;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn38;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn39;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn40;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn41;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn42;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn43;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand48;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand49;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand50;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand51;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand52;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand53;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand54;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand55;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand71;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand56;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand57;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand58;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand59;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand60;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand61;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand62;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand63;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand64;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand65;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand66;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand72;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand67;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand68;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand69;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand70;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn44;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand6;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand7;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand10;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand11;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand8;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand12;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand73;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand9;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand13;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand14;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand15;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand16;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand17;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand18;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand19;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand20;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand74;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn45;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand21;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand22;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand23;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand24;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand25;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn46;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private DevExpress.XtraGrid.GridControl summarryOrderGrid;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView4;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand76;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn47;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand77;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn48;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand78;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn49;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand79;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand80;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn50;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand81;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn51;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand82;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn52;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand83;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn53;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand84;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn54;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand85;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn55;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand86;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn56;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand87;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn57;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand88;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn58;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand89;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand90;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn59;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand91;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn60;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand92;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn61;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand93;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand94;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn62;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand95;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn63;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand96;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn64;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand97;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn65;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand98;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn66;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand99;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn67;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn68;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn69;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn70;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn71;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn72;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand27;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand28;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand29;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand30;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand101;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand31;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand102;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand103;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand32;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand45;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand75;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand104;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn73;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand33;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand34;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand35;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand36;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand37;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand38;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand39;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand100;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand40;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand41;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand46;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand42;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand43;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand44;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand47;
    }
}