﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsBaseSettingControl : LteMgrsConditionControlBase
    {
        private List<NbIotBandRange> bandValues;

        public override string Title
        {
            get { return "基本设置"; }
        }

        public NbIotMgrsBaseSettingControl()
        {
            InitializeComponent();
            this.numGridSize.Value = (decimal)NbIotMgrsBaseSettingManager.Instance.GridSize;
            bandValues = NbIotMgrsBaseSettingManager.Instance.BandValues;
            gridControl1.DataSource = bandValues;
            gridControl1.RefreshDataSource();
        }

        public override object GetCondition(out string invalidReason)
        {
            NbIotMgrsBaseSettingManager.Instance.GridSize = (int)numGridSize.Value;
            NbIotMgrsBaseSettingManager.Instance.BandValues = bandValues;
            NbIotMgrsBaseSettingManager.Instance.SetBandType();
            invalidReason = null;
            return null;
        }

        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            NbIotMgrsBaseSettingManager.Instance.SaveConfig(xcfg);
        }

        private void buttonAdd_Click(object sender, EventArgs e)
        {
            NbIotBandValueSettingBox box = new NbIotBandValueSettingBox(null);
            if (box.ShowDialog() == DialogResult.OK)
            {
                bandValues.Add(box.BandValue);
                gridControl1.RefreshDataSource();
            }
        }

        private void buttonModify_Click(object sender, EventArgs e)
        {
            object row = gv.GetRow(gv.GetSelectedRows()[0]);
            if (row is NbIotBandRange)
            {
                NbIotBandRange bandValue = row as NbIotBandRange;
                NbIotBandValueSettingBox box = new NbIotBandValueSettingBox(bandValue);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    bandValues.Remove(bandValue);
                    bandValues.Add(box.BandValue);
                    gridControl1.RefreshDataSource();
                }
            }
        }

        private void buttonRemove_Click(object sender, EventArgs e)
        {
            int index = gv.GetSelectedRows()[0];
            bandValues.RemoveAt(index);
            gridControl1.RefreshDataSource();
        }
    }

    public class NbIotBandRange
    {
        public string BandName { get; set; }
        public int Type { get; set; }
        public int Value { get; set; }
        public int MaxRangeValue { get; set; }
        public int MinRangeValue { get; set; }

        public string EarfcnDescription
        {
            get
            {
                if (Type == 0)
                {
                    return string.Format("x = {0}", Value);
                }
                else
                {
                    return string.Format("{0} <= x <= {1}", MinRangeValue, MaxRangeValue);
                }
            }
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BandName"] = BandName;
                param["Type"] = Type;
                param["Value"] = Value;
                param["MaxRangeValue"] = MaxRangeValue;
                param["MinRangeValue"] = MinRangeValue;
                return param;
            }
            set
            {
                BandName = value["BandName"].ToString();
                Type = (int)value["Type"];
                Value = (int)value["Value"];
                MaxRangeValue = (int)value["MaxRangeValue"];
                MinRangeValue = (int)value["MinRangeValue"];
            }
        }
    }
}
