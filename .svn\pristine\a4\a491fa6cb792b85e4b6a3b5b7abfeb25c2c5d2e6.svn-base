﻿using System;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTGSMCellReselectAnaSetForm : BaseDialog
    {
        public ZTGSMCellReselectAnaSetForm()
        {
            InitializeComponent();
        }

        public ZTGSMCellReselectAnaCondition GetCondition()
        {
            ZTGSMCellReselectAnaCondition condition = new ZTGSMCellReselectAnaCondition();
            condition.BeforeSecond = (int)numBeforeSecond.Value;
            condition.AfterSecond = (int)numAfterSecond.Value;
            condition.AfterAttemptSecond = (int)numAttemptAfterSecond.Value;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void ZTGSMCellReselectAnaSetForm_Load(object sender, EventArgs e)
        {
            //
        }
    }
}
