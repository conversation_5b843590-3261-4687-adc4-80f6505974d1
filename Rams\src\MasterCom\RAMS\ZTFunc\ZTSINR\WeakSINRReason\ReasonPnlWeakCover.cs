﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class ReasonPnlWeakCover : ReasonPanelBase
    {
        public ReasonPnlWeakCover()
        {
            InitializeComponent();
        }

        public override void AttachReason(ReasonBase reason)
        {
            base.AttachReason(reason);
            numMaxRSRP.ValueChanged -= numMaxRSRP_ValueChanged;
            numMaxRSRP.Value = (decimal)((ReasonWeakCover)reason).MaxRSRP;
            numMaxRSRP.ValueChanged += numMaxRSRP_ValueChanged;
        }

        void numMaxRSRP_ValueChanged(object sender, EventArgs e)
        {
            ((ReasonWeakCover)reason).MaxRSRP = (float)numMaxRSRP.Value;
        }

    }
}
