<?xml version="1.0" encoding="UTF-8"?>
<Configs>
	<Config name="StatParamCfg">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">基础信息</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">测试时间 kTimeValue</Item>
						<Item typeName="String" key="FName">kTimeValue</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">设备类型 kEqpId</Item>
						<Item typeName="String" key="FName">kEqpId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">文件类型 kFileTypeId</Item>
						<Item typeName="String" key="FName">kFileTypeId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">业务类型 kSvTypeId</Item>
						<Item typeName="String" key="FName">kSvTypeId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">运营商类型 kCarrierId</Item>
						<Item typeName="String" key="FName">kCarrierId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">项目类型 kProjId</Item>
						<Item typeName="String" key="FName">kProjId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">所属轮次 kRound</Item>
						<Item typeName="String" key="FName">kRound</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
</Configs>
