﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using DevExpress.XtraCharts;
using System.IO;
using MasterCom.RAMS.Util;
using Excel = Microsoft.Office.Interop.Excel;
using DevExpress.XtraEditors;
using System.Collections;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class KPIInfoPanel_ng_GIS : UserControl, PopShowPanelInterface
    {
        //第一命令字：config
        public const byte REQTYPE_CONFIG_KPI_TABLE = 0x2b;  //REQUEST
        public const byte RESTYPE_CONFIG_KPI_TABLE = 0x2b;

        //第一命令字：查询    启动预读
        public const byte REQTYPE_TABLE_DATA_INFO = 0x83; //REQUEST 
        public const byte RESTYPE_TABLE_DATA_INFO = 0x83;
        private MainModel MainModel = MainModel.GetInstance();
        public Dictionary<string, Object> colorRangeDic { get; set; }
        public Dictionary<string, AlarmCfgItem> alarmCfgDic { get; set; }

        public static KPIInfoPanel_ng_GIS theKPIInfoPanel { get; set; }

        AxisRange defaultRangeX;
        AxisRange defaultRangeY;
        public KPIInfoPanel_ng_GIS()
        {
            InitializeComponent();
            btnColor.Visible = true;
            initShowInfo();
            theKPIInfoPanel = this;
            if (chartControl.Diagram != null)
            {
                defaultRangeX = (AxisRange)((XYDiagram)chartControl.Diagram).AxisX.Range.Clone();
                defaultRangeY = (AxisRange)((XYDiagram)chartControl.Diagram).AxisY.Range.Clone();
            }
            loadMapLevelSetting();
            createPopShowRegionLayer();
            pnlKPIContent.Visible = false;
            GetSortColumnsInfo();

            curRetDataDic = new Dictionary<string, KPIPopTable>();
        }

        private void initShowInfo()
        {
            cbxShowType.Items.Add("最近一周");
            cbxShowType.Items.Add("最近一月");
            cbxShowType.Items.Add("按周");
            cbxShowType.Items.Add("按月");
            cbxShowType.Items.Add("按天");
            cbxShowType.SelectedIndex = 0;

        }

        #region PopShowPanelInterface 成员

        public void RunQuery(BackgroundWorker worker, TaskInfo task)
        {
            isProvUser = MainModel.User.DBID == -1;
            curDrillLevel = 0;
            Dictionary<string, KPIPopTable> entryHeaderDic = queryPopEntry(worker);
            foreach (string tbkey in entryHeaderDic.Keys)
            {
                KPIPopTable hdUnit = entryHeaderDic[tbkey];
                hdUnit.Sort();
                hdUnit.initFinderDic();
                List<KPIResultInfo> resultList = queryResultFromHeader(worker, hdUnit, MainModel.User.DBID);
                hdUnit.cityDataResult = buildCityStruct(resultList);
            }
            task.retResultInfo = entryHeaderDic;
           
        }

        private Dictionary<string, List<KPIResultInfo>> buildCityStruct(List<KPIResultInfo> resultList)
        {
            Dictionary<string, List<KPIResultInfo>> cityDic = new Dictionary<string, List<KPIResultInfo>>();
            foreach(KPIResultInfo info in resultList)
            {
                string strcity = DistrictManager.GetInstance().getDistrictName(info.dbid);
                List<KPIResultInfo> list = null;
                if(!cityDic.TryGetValue(strcity,out list))
                {
                    list = new List<KPIResultInfo>();
                    cityDic[strcity] = list;
                }
                list.Add(info);
            }
            return cityDic;
        }

        private List<KPIResultInfo> queryResultFromHeader(BackgroundWorker worker, KPIPopTable hdUnit,int dbid)
        {
            List<KPIResultInfo> retList = new List<KPIResultInfo>();
            ClientProxy clientProxy = new ClientProxy();

            string username = MainModel.User.LoginName;
            string password = MainModel.User.Password;
            int districtID = MainModel.DistrictID;
#if Guangdong
            //if (districtID == 14 || districtID == 15 || districtID == 22)
            {
                username = MainModel.MainDbUser.LoginName;
                password = MainModel.MainDbUser.Password;
                districtID = 2;
            }
#endif
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, username, password, districtID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);

                package.Content.AddParam(hdUnit.tablename);
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        KPIResultInfo retItem = hdUnit.ReadResultItemFrom(package.Content);
                        retList.Add(retItem);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }

        }
        private Dictionary<string, KPIPopTable> queryPopEntry(BackgroundWorker worker)
        {
            DiySqlPopKpiColor kpiColorQeruyTask = new DiySqlPopKpiColor(MainModel);
            kpiColorQeruyTask.Query();
            colorRangeDic = kpiColorQeruyTask.colorRangeDic;
            foreach (string key in colorRangeDic.Keys)
            {
                (colorRangeDic[key] as List<DTParameterRangeColor>).Sort();
            }
#if PopShow_KPI_Color
            DiySqlPopKpiAlarmCfg kpiAlarmCfgrQeruyTask = new DiySqlPopKpiAlarmCfg(MainModel);
            kpiAlarmCfgrQeruyTask.Query();
            alarmCfgDic = kpiAlarmCfgrQeruyTask.alarmCfgDic;
#endif

            Dictionary<string, KPIPopTable> entryDicList = new Dictionary<string, KPIPopTable>();
            ClientProxy clientProxy = new ClientProxy();
            string username = MainModel.User.LoginName;
            string password = MainModel.User.Password;
            int dbid = MainModel.DistrictID;
#if Guangdong
            //if (dbid == 14 || dbid == 15 || dbid == 22)
            {
                username = MainModel.MainDbUser.LoginName;
                password = MainModel.MainDbUser.Password;
                dbid = 2;
            }
#endif

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, username, password, dbid) != ConnectResult.Success)
            {
                worker.ReportProgress(99,"连接服务器端出错！");
                return entryDicList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.CellConfigManage;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_CONFIG_KPI_TABLE;
                package.Content.PrepareAddParam();
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_CONFIG_KPI_TABLE)
                    {
                        package.Content.PrepareGetParam();
                        PopKPIEntryItem entry = new PopKPIEntryItem();
                        entry.Fill(package.Content);
                        addEntryList(entryDicList, entry);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                }
                return entryDicList;
            }
            finally
            {
                clientProxy.Close();
            }
            

        }

        private void addEntryList(Dictionary<string, KPIPopTable> entryDicList, PopKPIEntryItem entry)
        {
            if (entry.strtablename == "tb_popblackblock"
                || entry.strtablename == "tb_pop_esinsight"
                || entry.strtablename == "tb_pop_compbench_status"
                || entry.strtablename == "tb_pop_compbench_unit"/*|| entry.strtablename.IndexOf("_agent_")!=-1*/)
            {
                //特殊的供其它程序使用的，不在KPI显示处显示
            }
            else
            {
                KPIPopTable headerUnit = null;
                if (!entryDicList.TryGetValue(entry.strtablename, out headerUnit))
                {
                    headerUnit = new KPIPopTable();
                    headerUnit.tablename = entry.strtablename;
                    entryDicList[headerUnit.tablename] = headerUnit;
                }
#if PopShow_KPI_Color
                            string key = entry.strtablename + entry.strcolname;
                            if (kpiColorQeruyTask.colorRangeDic.ContainsKey(key))
                            {
                                entry.colorLst = kpiColorQeruyTask.colorRangeDic[key] as List<DTParameterRangeColor>;
                            }
#endif
                headerUnit.entryList.Add(entry);
            }
        }
        #endregion

        #region PopShowPanelInterface 成员

        public Dictionary<string, KPIPopTable> curRetDataDic { get; set; }
        public void FireFreshShowData(TaskInfo task)
        {
            if (!(task.retResultInfo is Dictionary<string, KPIPopTable>))
            {
                curRetDataDic.Clear();
            }
            else
            {
                curRetDataDic = task.retResultInfo as Dictionary<string, KPIPopTable>;
            }
            cbxReportSel.Items.Clear();
            foreach(KPIPopTable popTable in curRetDataDic.Values)
            {
                if (popTable.tablename.Contains("_agent_"))
                {
                    continue;
                }
                if (popTable.ToString().EndsWith("(联通)") || popTable.ToString().EndsWith("(电信)") || popTable.ToString().EndsWith("(移动)"))
                {
                    continue;
                }
                cbxReportSel.Items.Add(popTable);
            }
            if(cbxReportSel.Items.Count>0)
            {
                cbxReportSel.SelectedIndex = 0;
            }
        }

        #endregion

        #region PopShowPanelInterface 成员


        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm, WelcomForm welcomform)
        {
            //KillMapAInfo
            /**
            this.MainModel = mm;
            btnColor.Visible = this.MainModel.User.HasRight(102);
            popShowGISLayer.MainModel = MainModel;
            if (MainModel.User.DBID == -1)
            {
                refreshPopShowRegion(mapProvince.name, -1);
            }
            else
            {
                refreshPopShowRegion(DistrictManager.GetInstance().getDistrictName(MainModel.User.DBID), -1);
            }
            */
        }

        #endregion

        private void mapControl_MouseLeave(object sender, EventArgs e)
        {
            int col = lastRefreshCol;
            lastRefreshCol = -2;
            refreshPopShowRegion(lastRefreshName, col);
        }

        private void cbxReportSel_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(isProvUser)
            {
                curDrillLevel = 0;
                cityName = "";
            }
            refreshShowReport(true);
        }

        private void cbxShowType_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selShowType = cbxShowType.SelectedItem as string;
            if(selShowType==null)
            {
                return;
            }
            refreshShowReport(false);
        }
        private void refreshShowReport(bool resetContent)
        {
            bRefreshShowReport = true;
            btnLevel0.Visible = isProvUser;
            curShowRes.Clear();
            dataGridView.Columns.Clear();
            KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
            string selShowType = cbxShowType.SelectedItem as string;
            if(kpiPopTable!=null && selShowType!=null)
            {
                if (KPIShowType == 3)
                {
                    competitionMethod(kpiPopTable, selShowType);
                    return;
                }

                FinalShowResult showRet = parseShowFromTable(kpiPopTable, selShowType);
                if (KPIShowType == 2)
                {
                    showRet = getKPISort(showRet);
                    sortKPI(showRet, sortAreaType == 1 ? 3 : 5);
                    curShowRes = showRet;
                }
                if (resetContent)
                {
                    Dictionary<string, bool> namesDic = getNameList(showRet);
                    cbxContentType.Properties.Items.Clear();
                    cbxContentType.Properties.Items.Add("(全部)");
                    foreach (string nm in namesDic.Keys)
                    {
                        cbxContentType.Properties.Items.Add(nm);
                    }
                    cbxContentType.Text = "(全部)";
                }
                showInGrid(showRet, false);
                setSelectIndex();
            }
            bRefreshShowReport = false;
        }

        private void setSelectIndex()
        {
            if (cbxLegend.Items.Count > 0)
            {
                if (curSelKPICol == -1)
                {
                    cbxLegend.SelectedIndex = 0;
                }
                else
                {
                    cbxLegend.SelectedIndex = curSelKPICol;
                }
            }
        }

        private void competitionMethod(KPIPopTable kpiPopTable, string selShowType)
        {
            KPIPopTable retCM = null;
            KPIPopTable retCU = null;
            KPIPopTable retCT = null;
            getKpiPopTable(kpiPopTable, ref retCM, ref retCU, ref retCT);
            if (retCM == null || retCU == null || retCT == null)
            {
                MessageBox.Show("请先定制三网对比指标！");
                return;
            }
            FinalShowResult sRet = parseShowCompetition(retCM, retCU, retCT, selShowType);
            showInGrid(sRet, false);
            setSelectIndex();
        }

        private void getKpiPopTable(KPIPopTable kpiPopTable, ref KPIPopTable retCM, ref KPIPopTable retCU, ref KPIPopTable retCT)
        {
            foreach (KPIPopTable table in curRetDataDic.Values)
            {
                if (table.ToString().Equals(kpiPopTable.ToString() + "(联通)"))
                {
                    retCU = table;
                }
                else if (table.ToString().Equals(kpiPopTable.ToString() + "(电信)"))
                {
                    retCT = table;
                }
                else if (table.ToString().Equals(kpiPopTable.ToString() + "(移动)"))
                {
                    retCM = table;
                }
            }
            judgeCurOperator(kpiPopTable, ref retCM, ref retCU, ref retCT);
        }

        private void judgeCurOperator(KPIPopTable kpiPopTable, ref KPIPopTable retCM, ref KPIPopTable retCU, ref KPIPopTable retCT)
        {
            if (retCM != null && retCU != null && retCT == null)
            {
                retCT = kpiPopTable;
                curOperator = 2;
            }
            if (retCT != null && retCU != null && retCM == null)
            {
                retCM = kpiPopTable;
                curOperator = 0;
            }
            if (retCM != null && retCT != null && retCU == null)
            {
                retCU = kpiPopTable;
                curOperator = 1;
            }
        }

        private void GetSortColumnsInfo()
        {
            string configFileName = Application.StartupPath + @"\config\PopShowKPISortColumns.xml";
            if (File.Exists(configFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(configFileName);
                string columnsInfo = "";
                try
                {
                    columnsInfo = configFile.GetItemValue("PopShowKPIColumns", "DescColumns") as string;
                    if (columnsInfo != null)
                    {
                        sortDescColumns = columnsInfo;
                    }
                    columnsInfo = configFile.GetItemValue("PopShowKPIColumns", "AscColumns") as string;
                }
                catch
                {
                    //continue
                }
                if (columnsInfo != null)
                {
                    sortAscColumns = columnsInfo;
                }
            }
        }

        private FinalShowResult getKPICompetition(FinalShowResult showRes)
        {
            FinalShowResult refShowRes = new FinalShowResult();
            List<int> colList = new List<int>();
            refShowRes.columnNames.Add("时间");
            refShowRes.columnNames.Add("地市");
            for (int i = 0; i < showRes.columnNames.Count; i++)
            {
                string colName = showRes.columnNames[i];
                if (sortDescColumns.Contains(colName) || sortAscColumns.Contains(colName))
                {
                    refShowRes.columnNames.Add(colName);
                    colList.Add(i);
                }
            }
            string[] selMonth = null;
            if (checkedCbxMonth.Visible)
            {
                selMonth = checkedCbxMonth.Text.Split(',');
            }
            foreach (List<object> dataRow in showRes.dataRows)
            {
                bool isAdd = judgeNeedsAdd(selMonth, dataRow);
                if (isAdd)
                {
                    addKPICompetitionRowData(refShowRes, colList, dataRow);
                }
            }
            return refShowRes;
        }

        private void addKPICompetitionRowData(FinalShowResult refShowRes, List<int> colList, List<object> dataRow)
        {
            List<object> newRow = new List<object>();
            newRow.Add(dataRow[0]);
            newRow.Add(dataRow[1]);
            foreach (int col in colList)
            {
                newRow.Add(dataRow[col]);
            }
            refShowRes.dataRows.Add(newRow);
        }

        private bool judgeNeedsAdd(string[] selMonth, List<object> dataRow)
        {
            if (selMonth != null)
            {
                bool isAdd = false;
                foreach (string month in selMonth)
                {
                    if (month.Trim().Equals(dataRow[0]))
                    {
                        isAdd = true;
                        break;
                    }
                }
                if (!isAdd)
                {
                    return false;
                }
            }
            return true;
        }

        private FinalShowResult getKPISort(FinalShowResult showRes)
        {
            FinalShowResult refShowRes = new FinalShowResult();
            List<int> colList = new List<int>();
            refShowRes.columnNames.Add("时间");
            refShowRes.columnNames.Add("省内排名");
            if (sortAreaType == 2)
            {
                refShowRes.columnNames.Add("地市排名");
            }
            refShowRes.columnNames.Add("地市");
            if (sortAreaType == 2)
            {
                refShowRes.columnNames.Add("网格");
            }
            for (int i = 0; i < showRes.columnNames.Count; i++)
            {
                string colName = showRes.columnNames[i];
                if (sortDescColumns.Contains(colName) || sortAscColumns.Contains(colName))
                {
                    refShowRes.columnNames.Add(colName);
                    colList.Add(i);
                }
            }
            string[] selMonth = null;
            if (checkedCbxMonth.Visible)
            {
                selMonth = checkedCbxMonth.Text.Split(',');
            }
            foreach (List<object> dataRow in showRes.dataRows)
            {
                bool isAdd = judgeNeedsAdd(selMonth, dataRow);
                if (isAdd)
                {
                    addKPISortRowData(refShowRes, colList, dataRow);
                }
            }
            return refShowRes;
        }

        private void addKPISortRowData(FinalShowResult refShowRes, List<int> colList, List<object> dataRow)
        {
            List<object> newRow = new List<object>();
            newRow.Add(dataRow[0]);
            newRow.Add("");
            if (sortAreaType == 2)
            {
                newRow.Add("");
            }
            if (sortAreaType == 2)
            {
                if (dataRow[2].ToString().Equals(""))
                {
                    newRow.Add("");
                }
                else
                {
                    newRow.Add(DistrictManager.GetInstance().getDistrictName((int)dataRow[2]));
                }
                newRow.Add(dataRow[1]);
            }
            else
            {
                newRow.Add(dataRow[1]);
            }
            foreach (int col in colList)
            {
                newRow.Add(dataRow[col]);
            }
            refShowRes.dataRows.Add(newRow);
        }

        private void sortKPI(FinalShowResult showRes, int col)
        {
            bool sortDesc = true;
            string colName = showRes.columnNames[col];
            if (sortDescColumns.Contains(colName))
            {
                if (sortType == 2)
                {
                    sortDesc = false;
                }
            }
            else if (sortAscColumns.Contains(colName))
            {
                if (sortType == 1)
                {
                    sortDesc = false;
                }
            }
            else
            {
                //
            }
            if (sortAreaType == 1)
            {
                sortCity(showRes, col, sortDesc);
            }
            else
            {
                sortAreaByCity(showRes, 3, col, sortDesc);
            }
        }

        private void sortCity(FinalShowResult showRes, int col, bool sortDesc)
        {
            showRes.dataRows = sortRows(showRes.dataRows, 2, 1, col, sortDesc);
        }

        private List<List<object>> sortRows(List<List<object>> dataRows, int nameCol, int writeCol, int sortCol, bool sortDesc)
        {
            List<int> indexList = new List<int>();
            List<List<object>> valueList = new List<List<object>>();
            List<object> totalRow = new List<object>();
            for (int i = 0; i < dataRows.Count; i++)
            {
                if (dataRows[i][nameCol].ToString().Equals("汇总"))
                {
                    totalRow = dataRows[i];
                    continue;
                }
                dealValueList(dataRows, sortCol, sortDesc, indexList, valueList, i);
            }
            if (totalRow.Count > 0)
            {
                valueList.Add(totalRow);
            }

            int preIndex = 0;
            object preObj = null;
            for (int i = 0; i < valueList.Count; i++)
            {
                List<object> row = valueList[i];
                double preValue;
                if (preObj != null && double.TryParse(preObj.ToString(), out preValue))
                {
                    double value;
                    if (double.TryParse(row[sortCol].ToString(), out value)
                        && preValue == value)
                    {
                        row[writeCol] = preIndex;
                        continue;
                    }
                }
                row[writeCol] = i + 1;
                preIndex = i + 1;
                preObj = row[sortCol];
            }
            return valueList;
        }

        private static void dealValueList(List<List<object>> dataRows, int sortCol, bool sortDesc, List<int> indexList, List<List<object>> valueList, int i)
        {
            for (int j = 0; j <= valueList.Count; j++)
            {
                if (j == valueList.Count)
                {
                    valueList.Add(dataRows[i]);
                    indexList.Add(i);
                    return;
                }
                bool isAdded = addData(dataRows, sortCol, sortDesc, indexList, valueList, i, j);
                if (isAdded)
                {
                    return;
                }
            }
        }

        private static bool addData(List<List<object>> dataRows, int sortCol, bool sortDesc, List<int> indexList, List<List<object>> valueList, int i, int j)
        {
            if (!double.TryParse(dataRows[i][sortCol].ToString(), out double value))
            {
                valueList.Add(dataRows[i]);
                indexList.Add(i);
                return true;
            }
            if (!double.TryParse(valueList[j][sortCol].ToString(), out double valueCom))
            {
                valueList.Insert(j, dataRows[i]);
                indexList.Insert(j, i);
                return true;
            }
            if (sortDesc)
            {
                if (value > valueCom)
                {
                    valueList.Insert(j, dataRows[i]);
                    indexList.Insert(j, i);
                    return true;
                }
            }
            else
            {
                if (value < valueCom)
                {
                    valueList.Insert(j, dataRows[i]);
                    indexList.Insert(j, i);
                    return true;
                }
            }
            return false;
        }

        private void sortAreaByCity(FinalShowResult curShowRes, int cityCol, int sortCol, bool sortDesc)
        {
            Dictionary<string, List<List<object>>> rowsGroup = new Dictionary<string, List<List<object>>>();
            List<List<object>> rows = curShowRes.dataRows;
            for (int i = 0; i < rows.Count; i++)
            {
                string city = rows[i][cityCol].ToString();
                if (!string.IsNullOrEmpty(city))
                {
                    if (!rowsGroup.ContainsKey(city))
                    {
                        List<List<object>> rowList = new List<List<object>>();
                        rowList.Add(rows[i]);
                        rowsGroup[city] = rowList;
                    }
                    else
                    {
                        rowsGroup[city].Add(rows[i]);
                    }
                }
            }
            List<List<object>> newRows = new List<List<object>>();
            foreach (List<List<object>> cityRows in rowsGroup.Values)
            {
                List<List<object>> tmpRows = sortRows(cityRows, 4, 2, sortCol, sortDesc);
                newRows.AddRange(tmpRows);
            }
            curShowRes.dataRows = sortRows(newRows, 4, 1, sortCol, sortDesc);
        }

        private Dictionary<string, bool> getNameList(FinalShowResult showRet)
        {
            Dictionary<string, bool> ret = new Dictionary<string, bool>();
            int col = 1;
            if (KPIShowType == 2)
            {
                col = sortAreaType == 1 ? 2 : 4;
            }
            foreach (List<object> vList in showRet.dataRows)
            {
                ret[vList[col] as string] = true;
            }
            return ret;
        }
        /// <summary>
        /// 是否当前登录的是省用户/多地市用户
        /// </summary>
        private bool isProvUser = false;
        /// <summary>
        /// 当前所选地市
        /// </summary>
        private string cityName = "";
        /// <summary>
        /// 0 全省汇总，1 全省  2地市片区
        /// </summary>
        private int curDrillLevel = 0;  

        private FinalShowResult parseShowCompetition(KPIPopTable tableCM, KPIPopTable tableCU, KPIPopTable tableCT, string selShowType)
        {
            FinalShowResult showRetCM = parseShowFromTable(tableCM, selShowType);
            FinalShowResult showRetCU = parseShowFromTable(tableCU, selShowType);
            FinalShowResult showRetCT = parseShowFromTable(tableCT, selShowType);
            if (selShowType.Equals("按月"))
            {
                List<FinalShowResult> showRetList = new List<FinalShowResult>();
                showRetList.Add(showRetCM);
                showRetList.Add(showRetCU);
                showRetList.Add(showRetCT);
                checkedCbxMonth.Properties.Tag = showRetList;
            }
            return getShowResultCompetition(showRetCM, showRetCU, showRetCT);
        }

        private FinalShowResult getShowResultCompetition(FinalShowResult showRetCM, FinalShowResult showRetCU, FinalShowResult showRetCT)
        {
            if (showRetCM.columnNames.Count == 0)
            {
                return null;
            }
            showRetCM = getKPICompetition(showRetCM);
            showRetCU = getKPICompetition(showRetCU);
            showRetCT = getKPICompetition(showRetCT);
            showRetCM.SortByTimeAndName(showRetCU);
            showRetCM.SortByTimeAndName(showRetCT);
            int colOffset = 0;
            string colNameOffset0 = "(移动)";
            string colNameOffset1 = "(联通)";
            string colNameOffset2 = "(电信)";
            FinalShowResult showRet0 = showRetCM;
            FinalShowResult showRet1 = showRetCU;
            FinalShowResult showRet2 = showRetCT;
            if (curOperator == 1)
            {
                colNameOffset0 = "(联通)";
                colNameOffset1 = "(移动)";
                colNameOffset2 = "(电信)";
                showRet0 = showRetCU;
                showRet1 = showRetCM;
                showRet2 = showRetCT;
            }
            else if (curOperator == 2)
            {
                colNameOffset0 = "(电信)";
                colNameOffset1 = "(移动)";
                colNameOffset2 = "(联通)";
                showRet0 = showRetCT;
                showRet1 = showRetCM;
                showRet2 = showRetCU;
            }
            FinalShowResult showRet = new FinalShowResult();
            for (int i = 0; i < showRet0.dataRows.Count; i++)
            {
                List<object> row = new List<object>();
                showRet.dataRows.Add(row);
            }
            for (int i = 0; i < showRet0.columnNames.Count; i++)
            {
                if (i > 1)
                {
                    showRet.columnNames.Add(showRet0.columnNames[i] + colNameOffset0);
                    showRet.columnNames.Add(showRet1.columnNames[i] + colNameOffset1);
                    showRet.columnNames.Add(showRet2.columnNames[i] + colNameOffset2);
                    setCompetitionRes(showRet0, showRet1, showRet2, showRet, i);
                    colOffset += 2;
                }
                else
                {
                    showRet.columnNames.Add(showRet0.columnNames[i]);
                    for (int r = 0; r < showRet.dataRows.Count; r++)
                    {
                        showRet.dataRows[r].Add(showRet0.dataRows[r][i]);
                    }
                }
            }
            return showRet;
        }

        private static void setCompetitionRes(FinalShowResult showRet0, FinalShowResult showRet1, FinalShowResult showRet2, FinalShowResult showRet, int i)
        {
            for (int r = 0; r < showRet.dataRows.Count; r++)
            {
                if (showRet0.dataRows.Count > r && showRet0.dataRows[r].Count > i)
                {
                    showRet.dataRows[r].Add(showRet0.dataRows[r][i]);
                }
                else
                {
                    showRet.dataRows[r].Add("-");
                }
                if (showRet1.dataRows.Count > r && showRet1.dataRows[r].Count > i)
                {
                    showRet.dataRows[r].Add(showRet1.dataRows[r][i]);
                }
                else
                {
                    showRet.dataRows[r].Add("-");
                }
                if (showRet2.dataRows.Count > r && showRet2.dataRows[r].Count > i)
                {
                    showRet.dataRows[r].Add(showRet2.dataRows[r][i]);
                }
                else
                {
                    showRet.dataRows[r].Add("-");
                }
            }
        }

        private FinalShowResult parseShowFromTable(KPIPopTable kpiPopTable, string selShowType)
        {
            FinalShowResult sRet = new FinalShowResult();
            checkedCbxMonth.Visible = true;
            if (KPIShowType == 2 && sortAreaType == 2)
            {
                kpiPopTable.reloadDataResultByLevel(isProvUser, cityName, "", true);
            }
            else
            {
                kpiPopTable.reloadDataResultByLevel(isProvUser, cityName, "", false);
            }
            if (selShowType == "按天")
            {
                sRet = prepareShowByDay(kpiPopTable);
            }
            else if (selShowType == "按月")
            {
                checkedCbxMonth.Visible = true;
                sRet = prepareShowByMonth(kpiPopTable);
            }
            else if (selShowType == "按周")
            {
                labelMonth.Visible = false;
                checkedCbxMonth.Visible = false;
                sRet = prepareShowByWeek(kpiPopTable);
            }
            else if(selShowType == "最近一月")
            {
                labelMonth.Visible = false;
                checkedCbxMonth.Visible = false;
                KPIResultInfo notUsed = null;
                sRet = prepareShowByLastMonth(kpiPopTable, out notUsed);
            }
            else if (selShowType == "最近一周")
            {
                labelMonth.Visible = false;
                checkedCbxMonth.Visible = false;
                KPIResultInfo notUsed = null;
                sRet = prepareShowByLastWeek(kpiPopTable, out notUsed);
            }
            return sRet;
        }

        private FinalShowResult prepareShowByDay(KPIPopTable kpiPopTable)
        {
            labelMonth.Visible = false;
            checkedCbxMonth.Visible = false;
            FinalShowResult sRet = new FinalShowResult();
            initResult(kpiPopTable, sRet);
            foreach (KPIResultInfo retInfo in kpiPopTable.dataResult)
            {
                List<object> objList = new List<object>();
                DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(retInfo.stime * 1000L);
                objList.Add(stimeDate.ToString("yyyy.MM.dd"));
                objList.Add(retInfo.strname);
                if (KPIShowType == 2 && sortAreaType == 2)
                {
                    objList.Add(retInfo.dbid);
                }
                for (int i = 0; i < retInfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = retInfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }
        private FinalShowResult prepareShowByLastWeek(KPIPopTable kpiPopTable, out KPIResultInfo totalResult)
        {
            totalResult = null;
            if (kpiPopTable.ToString() == "GSM语音业务报表_汇总")
            {
                return getGsmTotalResLastWeek(kpiPopTable);
            }
            else
            {
                return getTotalResLastWeek(kpiPopTable, out totalResult);
            }
        }

        private FinalShowResult getGsmTotalResLastWeek(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = new FinalShowResult();
            initResult(kpiPopTable, sRet);
            KPIResultInfo total = null;//abc***
            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (tableData.ToString().IndexOf("GSM语音业务报表_") == 0 && tableData.ToString() != "GSM语音业务报表_汇总")
                {
                    //
                    total = dealGsmTotalLastWeek(kpiPopTable, sRet, total, tableData);
                }
            }
            if (total != null)
            {
                List<object> objTotalList = new List<object>();
                objTotalList.Add(getWeekStr(total.stime));
                objTotalList.Add("汇总");
                if (KPIShowType == 2 && sortAreaType == 2)
                {
                    objTotalList.Add("");
                }
                for (int i = 0; i < total.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = total.valueList[i];
                    objTotalList.Add(v);
                }
                sRet.dataRows.Add(objTotalList);
            }

            return sRet;
        }

        private KPIResultInfo dealGsmTotalLastWeek(KPIPopTable kpiPopTable, FinalShowResult sRet, KPIResultInfo total, KPIPopTable tableData)
        {
            if (KPIShowType == 2 && sortAreaType == 2)
            {
                tableData.reloadDataResultByLevel(isProvUser, cityName, "", true);
            }
            else
            {
                tableData.reloadDataResultByLevel(isProvUser, cityName, "", false);
            }
            KPIResultInfo kRi = null;
            FinalShowResult projResult = prepareShowByLastWeek(tableData, out kRi);
            if (projResult.dataRows.Count > 0)
            {
                string nameColValue = projResult.dataRows[projResult.dataRows.Count - 1][1].ToString();
                if (nameColValue == "汇总")
                {
                    List<object> retGatherOfProj = new List<object>();
                    retGatherOfProj.AddRange(projResult.dataRows[projResult.dataRows.Count - 1]);
                    retGatherOfProj[1] = tableData.ToString();
                    sRet.dataRows.Add(retGatherOfProj);
                    if (total == null)
                    {
                        total = kRi;
                    }
                    else
                    {
                        total.Gather(kRi, kpiPopTable);
                    }
                }
            }

            return total;
        }

        private FinalShowResult getTotalResLastWeek(KPIPopTable kpiPopTable, out KPIResultInfo totalResult)
        {
            FinalShowResult sRet = new FinalShowResult();
            initResult(kpiPopTable, sRet);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getWeekStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                    retDic[key].stime = getWeekBeginTime(rinfo.stime);
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }
            int maxstime = 0;
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime > maxstime)
                {
                    maxstime = ginfo.stime;
                }
            }
            KPIResultInfo total = dealTotalLastweek(kpiPopTable, sRet, retDic, maxstime);
            totalResult = total;
            //=======//abc***
            if (total != null)
            {
                List<object> objTotalList = new List<object>();
                objTotalList.Add(getWeekStr(maxstime));
                objTotalList.Add("汇总");
                if (KPIShowType == 2 && sortAreaType == 2)
                {
                    objTotalList.Add("");
                }
                for (int i = 0; i < total.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = total.valueList[i];
                    objTotalList.Add(v);
                }
                sRet.dataRows.Add(objTotalList);
            }

            //==========//abc***
            return sRet;
        }

        private KPIResultInfo dealTotalLastweek(KPIPopTable kpiPopTable, FinalShowResult sRet, Dictionary<string, KPIResultInfo> retDic, int maxstime)
        {
            KPIResultInfo total = null;//abc***
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime == maxstime)
                {
                    List<object> objList = new List<object>();
                    objList.Add(getWeekStr(ginfo.stime));
                    objList.Add(ginfo.strname);
                    if (KPIShowType == 2 && sortAreaType == 2)
                    {
                        objList.Add(ginfo.dbid);
                    }
                    for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                    {
                        object v = ginfo.valueList[i];
                        objList.Add(v);
                    }
                    sRet.dataRows.Add(objList);
                    if (total == null)
                    {
                        total = ginfo.copyInstance();
                    }
                    else
                    {
                        total.Gather(ginfo, kpiPopTable);//abc***
                    }
                }
            }

            return total;
        }

        private FinalShowResult prepareShowByWeek(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = new FinalShowResult();
            initResult(kpiPopTable, sRet);
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getWeekStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }

            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                List<object> objList = new List<object>();
                objList.Add(getWeekStr(ginfo.stime));
                objList.Add(ginfo.strname);
                if (KPIShowType == 2 && sortAreaType == 2)
                {
                    objList.Add(ginfo.dbid);
                }
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }
        private FinalShowResult prepareShowByLastMonth(KPIPopTable kpiPopTable, out KPIResultInfo totalResult)
        {
            totalResult = null;
            if (kpiPopTable.ToString() == "GSM语音业务报表_汇总")
            {
                return getGsmTotalResLastMonth(kpiPopTable);
            }
            else
            {
                return getTotalResLastMonth(kpiPopTable, out totalResult);
            }
        }

        private FinalShowResult getGsmTotalResLastMonth(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = new FinalShowResult();
            initResult(kpiPopTable, sRet);
            KPIResultInfo total = null;//abc***
            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (tableData.ToString().IndexOf("GSM语音业务报表_") == 0 && tableData.ToString() != "GSM语音业务报表_汇总")
                {
                    total = dealGsmTotalLastMonth(kpiPopTable, sRet, total, tableData);
                }
            }
            if (total != null)
            {
                List<object> objTotalList = new List<object>();
                DateTime tstimeDate = JavaDate.GetDateTimeFromMilliseconds(1000L * total.stime);
                objTotalList.Add(tstimeDate.ToString("yyyy-MM"));
                objTotalList.Add("汇总");
                if (KPIShowType == 2 && sortAreaType == 2)
                {
                    objTotalList.Add("");
                }
                for (int i = 0; i < total.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = total.valueList[i];
                    objTotalList.Add(v);
                }
                sRet.dataRows.Add(objTotalList);
            }

            return sRet;
        }

        private KPIResultInfo dealGsmTotalLastMonth(KPIPopTable kpiPopTable, FinalShowResult sRet, KPIResultInfo total, KPIPopTable tableData)
        {
            //
            if (KPIShowType == 2 && sortAreaType == 2)
            {
                tableData.reloadDataResultByLevel(isProvUser, cityName, "", true);
            }
            else
            {
                tableData.reloadDataResultByLevel(isProvUser, cityName, "", false);
            }
            KPIResultInfo kRi = null;
            FinalShowResult projResult = prepareShowByLastMonth(tableData, out kRi);
            if (projResult.dataRows.Count > 0)
            {
                string nameColValue = projResult.dataRows[projResult.dataRows.Count - 1][1].ToString();
                if (nameColValue == "汇总")
                {
                    List<object> retGatherOfProj = new List<object>();
                    retGatherOfProj.AddRange(projResult.dataRows[projResult.dataRows.Count - 1]);
                    retGatherOfProj[1] = tableData.ToString();
                    sRet.dataRows.Add(retGatherOfProj);
                    if (total == null)
                    {
                        total = kRi;
                    }
                    else
                    {
                        total.Gather(kRi, kpiPopTable);
                    }
                }
            }

            return total;
        }

        private FinalShowResult getTotalResLastMonth(KPIPopTable kpiPopTable, out KPIResultInfo totalResult)
        {
            FinalShowResult sRet = new FinalShowResult();
            initResult(kpiPopTable, sRet);
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getMonthStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                    retDic[key].stime = getMonthBeginTime(rinfo.stime);
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }
            int maxstime = 0;
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime > maxstime)
                {
                    maxstime = ginfo.stime;
                }
            }
            KPIResultInfo total = dealTotalLastMonth(kpiPopTable, sRet, retDic, maxstime);
            totalResult = total;
            //=======//abc***
            if (total != null)
            {
                List<object> objTotalList = new List<object>();
                DateTime tstimeDate = JavaDate.GetDateTimeFromMilliseconds(1000L * maxstime);
                objTotalList.Add(tstimeDate.ToString("yyyy-MM"));
                objTotalList.Add("汇总");
                if (KPIShowType == 2 && sortAreaType == 2)
                {
                    objTotalList.Add("");
                }
                for (int i = 0; i < total.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = total.valueList[i];
                    objTotalList.Add(v);
                }
                sRet.dataRows.Add(objTotalList);
            }
            //==========//abc***
            return sRet;
        }

        private KPIResultInfo dealTotalLastMonth(KPIPopTable kpiPopTable, FinalShowResult sRet, Dictionary<string, KPIResultInfo> retDic, int maxstime)
        {
            KPIResultInfo total = null;//abc***
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime == maxstime)
                {
                    List<object> objList = new List<object>();
                    DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(ginfo.stime * 1000L);
                    objList.Add(stimeDate.ToString("yyyy-MM"));
                    objList.Add(ginfo.strname);
                    if (KPIShowType == 2 && sortAreaType == 2)
                    {
                        objList.Add(ginfo.dbid);
                    }
                    for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                    {
                        object v = ginfo.valueList[i];
                        objList.Add(v);
                    }
                    sRet.dataRows.Add(objList);
                    if (total == null)
                    {
                        total = ginfo.copyInstance();
                    }
                    else
                    {
                        total.Gather(ginfo, kpiPopTable);//abc***
                    }

                }
            }

            return total;
        }

        private FinalShowResult prepareShowByMonth(KPIPopTable kpiPopTable)
        {
            string[] sCheckedMonths = checkedCbxMonth.Properties.GetCheckedItems().ToString().Split(',');
            List<string> checkedMonthsBefore = new List<string>();
            foreach (string month in sCheckedMonths)
            {
                checkedMonthsBefore.Add(month.Trim());
            }
            checkedCbxMonth.Properties.Items.Clear();
            labelMonth.Visible = true;
            checkedCbxMonth.Visible = true;
            checkMonthState = false;
            FinalShowResult sRet = new FinalShowResult();
            initResult(kpiPopTable, sRet);
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            Dictionary<string, string> monthDic = new Dictionary<string, string>();
            List<string> monthList = new List<string>();
            dealMonthDataResult(kpiPopTable, retDic, monthDic, monthList);
            bool containsBeforeMonth = checkedMonthsBefore.Count > 0;
            foreach (string monthBefore in checkedMonthsBefore)
            {
                if (!monthList.Contains(monthBefore))
                {
                    containsBeforeMonth = false;
                    break;
                }
            }

            addCheckedCbxMonth(checkedMonthsBefore, monthDic, monthList, containsBeforeMonth);

            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                List<object> objList = new List<object>();
                DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(ginfo.stime * 1000L);
                objList.Add(stimeDate.ToString("yyyy-MM"));
                objList.Add(ginfo.strname);
                if (KPIShowType == 2 && sortAreaType == 2)
                {
                    objList.Add(ginfo.dbid);
                }
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            checkedCbxMonth.Properties.Tag = sRet;
            checkMonthState = true;
            return sRet;
        }

        private void dealMonthDataResult(KPIPopTable kpiPopTable, Dictionary<string, KPIResultInfo> retDic, Dictionary<string, string> monthDic, List<string> monthList)
        {
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string monthKey = getMonthStr(rinfo.stime);
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + monthKey;
                KPIResultInfo ginfo = null;
                string monthInfo;
                if (!monthDic.TryGetValue(monthKey, out monthInfo))
                {
                    monthDic[monthKey] = monthKey;
                }
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
                if (!monthList.Contains(monthKey))
                {
                    monthList.Add(monthKey);
                }
            }
        }

        private void addCheckedCbxMonth(List<string> checkedMonthsBefore, Dictionary<string, string> monthDic, List<string> monthList, bool containsBeforeMonth)
        {
            int index = 0;
            foreach (string month in monthDic.Values)
            {
                index++;
                if (containsBeforeMonth)
                {
                    if (checkedMonthsBefore.Contains(monthList[index - 1]))
                    {
                        checkedCbxMonth.Properties.Items.Add(month, true);
                    }
                    else
                    {
                        checkedCbxMonth.Properties.Items.Add(month, false);
                    }
                }
                else
                {
                    if (index == monthDic.Values.Count)
                    {
                        checkedCbxMonth.Properties.Items.Add(month, true);//默认选中所有月份
                    }
                    else
                    {
                        checkedCbxMonth.Properties.Items.Add(month, false);
                    }
                }
            }
        }

        private void initResult(KPIPopTable kpiPopTable, FinalShowResult sRet)
        {
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("名称");
            if (KPIShowType == 2 && sortAreaType == 2)
            {
                sRet.columnNames.Add("DBID");
            }
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
        }

        private int getWeekBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            DateTime dtbegin = dt.AddDays(-dayToMondy);
            dtbegin = (new DateTime(dtbegin.Year, dtbegin.Month, dtbegin.Day)).ToLocalTime();
            long seconds = JavaDate.GetMilliseconds(dtbegin)/1000;
            return (int)seconds;
        }

        private string getWeekStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch(dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy =0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case  DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;
            }
            return dt.AddDays(-dayToMondy).ToString("yyyy.MM.dd_") + dt.AddDays(6 - dayToMondy).ToString("MM.dd");
        }
        private int getMonthBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DateTime dtbegin = new DateTime(dt.Year, dt.Month, 1).ToLocalTime();
            return (int)(JavaDate.GetMilliseconds(dtbegin)/1000);
        }

        private string getMonthStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            return dt.ToString("yyyy-MM");
        }

        private void showInGrid(FinalShowResult showRet, bool initGridOnly)
        {
            dataGridView.Rows.Clear();
            dataGridView.Columns.Clear();
            if (!initGridOnly)
            {
                cbxLegend.Items.Clear();
                mnuSortLegend.Items.Clear();
            }
            if (showRet.columnNames.Count == 0)
            {
                return;
            }
            lastRefreshCol = -2;
            for (int i = 0; i < showRet.columnNames.Count; i++)
            {
                string columnname = showRet.columnNames[i];
                dataGridView.Columns.Add("Column" + i, columnname);
                if (!initGridOnly)
                {
                    initOther(i, columnname);
                }
            }
            dataGridView.Columns[0].SortMode = DataGridViewColumnSortMode.Programmatic;
            addRowToGrid(showRet);
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            if (KPIShowType != 2)
            {
                dataGridView.Sort(dataGridView.Columns[0], ListSortDirection.Ascending);
            }
            dataGridView.Columns[0].Frozen = true;
            dataGridView.Columns[1].Frozen = true;
            if (KPIShowType == 2)
            {
                if (sortAreaType == 1)
                {
                    dataGridView.Columns[2].Frozen = true;
                }
                if (sortAreaType == 2)
                {
                    dataGridView.Columns[3].Frozen = true;
                    dataGridView.Columns[4].Frozen = true;
                    dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.DisplayedCells;
                }
            }
        }

        private void initOther(int i, string columnname)
        {
            if (KPIShowType == 1)
            {
                if (i > 1)
                {
                    PopShowKPIColumn column = new PopShowKPIColumn(columnname, i);
                    cbxLegend.Items.Add(column);
                    ToolStripItem item = mnuSortLegend.Items.Add(column.ToString());
                    item.Tag = mnuSortLegend.Items.Count - 1;
                    item.Click += new EventHandler(mnuSortLegendItem_Click);
                }
            }
            else if (KPIShowType == 2)
            {
                if (i > (sortAreaType == 1 ? 2 : 4))
                {
                    PopShowKPIColumn column = new PopShowKPIColumn(columnname, i);
                    cbxLegend.Items.Add(column);
                    ToolStripItem item = mnuSortLegend.Items.Add(column.ToString());
                    item.Tag = mnuSortLegend.Items.Count - 1;
                    item.Click += new EventHandler(mnuSortLegendItem_Click);
                }
            }
            else if (KPIShowType == 3)
            {
                if (i > 1 && (i % 3 == 0))
                {
                    columnname = columnname.Substring(0, columnname.Length - 4);
                    PopShowKPIColumn column = new PopShowKPIColumn(columnname, i);
                    cbxLegend.Items.Add(column);
                    ToolStripItem item = mnuSortLegend.Items.Add(column.ToString());
                    item.Tag = mnuSortLegend.Items.Count - 1;
                    item.Click += new EventHandler(mnuSortLegendItem_Click);
                }
            }
            else
            {
                //
            }
        }

        private void addRowToGrid(FinalShowResult showRet)
        {
            if (showRet.dataRows.Count > 0)
            {
                string selShowType = cbxShowType.SelectedItem as string;
                KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
                string rptName = kpiPopTable.ToString();
                if (curDrillLevel == 0 && (selShowType == "最近一周" || selShowType == "最近一月") && rptName != "GSM语音业务报表_汇总" && KPIShowType == 1)
                {
                    int indexRowAt = 0;
                    List<object> dataRow = showRet.dataRows[showRet.dataRows.Count - 1];
                    AddRowToGrid(indexRowAt, dataRow, showRet);
                }
                else
                {
                    int indexRowAt = 0;
                    for (int r = 0; r < showRet.dataRows.Count; r++)
                    {
                        List<object> dataRow = showRet.dataRows[r];
                        indexRowAt = AddRowToGrid(indexRowAt, dataRow, showRet);
                    }
                }
            }
        }

        private int AddRowToGrid(int indexRowAt, List<object> dataRow, FinalShowResult showRet)
        {
            bool isAdd = false;

            if (checkedCbxMonth.Visible)
            {
                string[] selMonth = checkedCbxMonth.Text.Split(',');
                if (selMonth != null)
                {
                    foreach (string month in selMonth)
                    {
                        if (month.Trim().Equals(dataRow[0]))
                        {
                            isAdd = true;
                            break;
                        }
                    }
                }
            }
            else
            {
                isAdd = true;
            }

            int col = 1;
            if (KPIShowType == 2)
            {
                col = sortAreaType == 1 ? 2 : 4;
            }
            if (((cbxContentType.Text).Contains("(全部)") || (cbxContentType.Text).Contains((string)dataRow[col])) && isAdd)
            {
                dataGridView.Rows.Add(1);
                for (int c = 0; c < dataRow.Count; c++)
                {
                    object dv = dataRow[c];
                    if (dv is DateTime)
                    {
                        dataGridView.Rows[indexRowAt].Cells[c].Value = ((DateTime)dv).ToString("yyyy-MM-dd");
                    }
                    else
                    {
                        dataGridView.Rows[indexRowAt].Cells[c].Value = dataRow[c];

                        if (KPIShowType == 3 && c > 1)
                        {
                            string competitionKey = "三网对比三网对比";
                            if (colorRangeDic == null || !colorRangeDic.ContainsKey(competitionKey))
                            {
                                List<DTParameterRangeColor> colorRanges = new List<DTParameterRangeColor>();
                                DTParameterRangeColor rangeColor = new DTParameterRangeColor(0, 0, Color.Green);
                                colorRanges.Add(rangeColor);
                                rangeColor = new DTParameterRangeColor(1, 1, Color.Yellow);
                                colorRanges.Add(rangeColor);
                                rangeColor = new DTParameterRangeColor(2, 2, Color.Red);
                                colorRanges.Add(rangeColor);
                                if (colorRangeDic == null)
                                {
                                    colorRangeDic = new Dictionary<string, object>();
                                }
                                colorRangeDic[competitionKey] = colorRanges;
                            }
                            Color colorGood = Color.Green;
                            Color colorOK = Color.Yellow;
                            Color colorBad = Color.Red;
                            foreach (DTParameterRangeColor rangeColor in (colorRangeDic[competitionKey] as List<DTParameterRangeColor>))
                            {
                                if (rangeColor.Min == 0)
                                {
                                    colorGood = rangeColor.Value;
                                }
                                else if (rangeColor.Min == 1)
                                {
                                    colorOK = rangeColor.Value;
                                }
                                else if (rangeColor.Min == 2)
                                {
                                    colorBad = rangeColor.Value;
                                }
                            }
                            if ((c + 1) % 3 == 2)
                            {
                                bool sortDesc = true;
                                string colName = dataGridView.Columns[c].HeaderText.Substring(0, dataGridView.Columns[c].HeaderText.Length - 4);
                                if (sortAscColumns.Contains(colName) && sortType == 1)
                                {
                                    sortDesc = false;
                                }
                                double dValue0;
                                double dValue1;
                                double dValue2;
                                int worseTimes = 0;
                                if (!double.TryParse(dataGridView.Rows[indexRowAt].Cells[c - 2].Value.ToString(), out dValue0))
                                {
                                    dataGridView.Rows[indexRowAt].Cells[c - 2].Style.BackColor = Color.Empty;
                                }
                                else
                                {
                                    if (double.TryParse(dataGridView.Rows[indexRowAt].Cells[c - 1].Value.ToString(), out dValue1))
                                    {
                                        if (sortDesc)
                                        {
                                            if (dValue0 < dValue1)
                                            {
                                                worseTimes++;
                                            }
                                        }
                                        else
                                        {
                                            if (dValue0 > dValue1)
                                            {
                                                worseTimes++;
                                            }
                                        }
                                    }

                                    if (double.TryParse(dataGridView.Rows[indexRowAt].Cells[c].Value.ToString(), out dValue2))
                                    {
                                        if (sortDesc)
                                        {
                                            if (dValue0 < dValue2)
                                            {
                                                worseTimes++;
                                            }
                                        }
                                        else
                                        {
                                            if (dValue0 > dValue2)
                                            {
                                                worseTimes++;
                                            }
                                        }
                                    }

                                    if (worseTimes == 0)
                                    {
                                        dataGridView.Rows[indexRowAt].Cells[c - 2].Style.BackColor = colorGood;
                                        dataGridView.Rows[indexRowAt].Cells[c - 1].Style.BackColor = colorGood;
                                        dataGridView.Rows[indexRowAt].Cells[c].Style.BackColor = colorGood;
                                    }
                                    else if (worseTimes == 1)
                                    {
                                        dataGridView.Rows[indexRowAt].Cells[c - 2].Style.BackColor = colorOK;
                                        dataGridView.Rows[indexRowAt].Cells[c - 1].Style.BackColor = colorOK;
                                        dataGridView.Rows[indexRowAt].Cells[c].Style.BackColor = colorOK;
                                    }
                                    else if (worseTimes == 2)
                                    {
                                        dataGridView.Rows[indexRowAt].Cells[c - 2].Style.BackColor = colorBad;
                                        dataGridView.Rows[indexRowAt].Cells[c - 1].Style.BackColor = colorBad;
                                        dataGridView.Rows[indexRowAt].Cells[c].Style.BackColor = colorBad;
                                    }
                                }
                            }

                            if (indexRowAt == 0)
                            {
                                dataGridView.Rows[indexRowAt].Cells[c].Tag = colorRangeDic["三网对比三网对比"];
                            }
                            continue;
                        }
                        KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
                        StringBuilder sb = new StringBuilder(kpiPopTable.tablename);
                        foreach (PopKPIEntryItem item in kpiPopTable.entryList)
                        {
                            if (item.strcoldesc == showRet.columnNames[c])
                            {
                                sb.Append(item.strcolname);
                                break;
                            }
                        }
                        string key = sb.ToString();
                        if (colorRangeDic != null && colorRangeDic.ContainsKey(key))
                        {
                            List<DTParameterRangeColor> colorRangeLst = colorRangeDic[key] as List<DTParameterRangeColor>;
                            foreach (DTParameterRangeColor colorRange in colorRangeLst)
                            {
                                if (colorRange.Within((float)dataRow[c]))
                                {
                                    dataGridView.Rows[indexRowAt].Cells[c].Style.BackColor = colorRange.Value;
                                    dataGridView.Rows[indexRowAt].Cells[c].ToolTipText = colorRange.DesInfo;
                                    if (indexRowAt == 0)
                                    {
                                        dataGridView.Rows[indexRowAt].Cells[c].Tag = colorRangeLst;
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
                indexRowAt++;
            }
            return indexRowAt;
        }

        private void cbxContentType_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshShowReport(false);
            if (tabSortAreaType.SelectedIndex == 1)
            {
                KPIInfoPanelHelper.FreshShowChart_GIS(2, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, cbxContentType), defaultRangeX, defaultRangeY, KPIShowType, sortAreaType);
            }
        }

        private void dataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            int minCol = 2;
            if (KPIShowType == 2)
            {
                minCol = sortAreaType == 1 ? 3 : 5;
            }
            if (e.ColumnIndex >= minCol && e.ColumnIndex < dataGridView.Columns.Count && e.RowIndex < dataGridView.Rows.Count && e.RowIndex != -1)
            {
                if (tabShow.SelectedIndex == 1)
                {
                    KPIInfoPanelHelper.FreshShowChart_GIS(e.ColumnIndex, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, cbxContentType), defaultRangeX, defaultRangeY, KPIShowType, sortAreaType);
                }
                changeLegend(e, minCol);
            }
        }

        private void changeLegend(DataGridViewCellEventArgs e, int minCol)
        {
            string levelName = getLevelName();
            if (lastRefreshName != levelName || lastRefreshCol != e.ColumnIndex)
            {
                refreshPopShowRegion(levelName, e.ColumnIndex);
                if (KPIShowType == 3)
                {
                    cbxLegend.SelectedIndex = (e.ColumnIndex + 1) / 3 - 1;
                }
                else
                {
                    cbxLegend.SelectedIndex = e.ColumnIndex - minCol;
                }
                lstLegend.Items.Clear();
                if (dataGridView[e.ColumnIndex, 0].Tag != null)
                {
                    List<DTParameterRangeColor> rangeColorList = (List<DTParameterRangeColor>)dataGridView[e.ColumnIndex, 0].Tag;
                    initLstLegend(rangeColorList);
                }
            }
        }

        private string getLevelName()
        {
            string levelName = "";
            if (curDrillLevel == 0)
            {
                levelName = isProvUser ? mapProvince.name : DistrictManager.GetInstance().getDistrictName(MainModel.User.DBID);
            }
            else if (curDrillLevel == 1)
            {
                levelName = isProvUser ? mapProvince.name : DistrictManager.GetInstance().getDistrictName(MainModel.User.DBID);
            }
            else if (curDrillLevel == 2)
            {
                levelName = isProvUser ? cityName : DistrictManager.GetInstance().getDistrictName(MainModel.User.DBID);
            }

            return levelName;
        }

        private void initLstLegend(List<DTParameterRangeColor> rangeColorList)
        {
            foreach (DTParameterRangeColor rangeColor in rangeColorList)
            {
                lstLegend.Items.Add(rangeColor);
            }
            if (rangeColorList.Count > 3)
            {
                lstLegend.Height = (rangeColorList.Count + 1) * 15 + 2;
            }
            else
            {
                lstLegend.Height = 4 * 15 + 2;
            }
        }

        internal void ReturnTChartControl(Steema.TeeChart.TChart tchart)
        {
            tchart.Parent = null;
            splitMain.Panel2.Controls.Add(tchart);
            splitMain.Panel2Collapsed = false;
            tchart.Dock = DockStyle.Fill;
        }

        private void btnColor_Click(object sender, EventArgs e)
        {
            kpiColorCfgForm form = new kpiColorCfgForm(this);
            form.ShowDialog();
        }

        private void btnLevel0_Click(object sender, EventArgs e)
        {
            int colIndex = 1;
            if (KPIShowType == 2)
            {
                colIndex = 2;
            }
            if(isProvUser)
            {
                curDrillLevel = 0;
                cityName = "";
                MessageBox.Show("刷新报表");
                refreshShowReport(true);
                MessageBox.Show("刷新地图");
                refreshPopShowRegion(mapProvince.name, colIndex + 1);
            }
            else
            {
                MessageBox.Show("刷新地图图");
                refreshPopShowRegion(DistrictManager.GetInstance().getDistrictName(MainModel.User.DBID), colIndex + 1);
            }
        }

        private void dataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (KPIShowType == 2 && sortAreaType == 2)
            {
                return;
            }
            int colIndex = 1;
            if (KPIShowType == 2)
            {
                colIndex = 2;
                if (isProvUser && curDrillLevel == 0)
                {
                    curDrillLevel = 1;
                }
            }
            if (e.ColumnIndex == colIndex && e.RowIndex < dataGridView.Rows.Count && e.RowIndex != -1)//选择了地市名称
            {
                KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
                if (kpiPopTable.ToString() == "GSM语音业务报表_汇总")
                {
                    dealSelectedItem(e);
                }
                else
                {
                    refreshData(e, colIndex);
                }
            }
        }

        private void dealSelectedItem(DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 1)
            {
                string rptName = dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Value as string;
                foreach (KPIPopTable ptable in cbxReportSel.Items)
                {
                    if (ptable.ToString() == rptName)
                    {
                        //find
                        cbxReportSel.SelectedItem = ptable;
                        break;
                    }
                }
            }
        }

        private void refreshData(DataGridViewCellEventArgs e, int colIndex)
        {
            if (curDrillLevel == 0)
            {
                curDrillLevel = 1;
                refreshShowReport(true);
                if (isProvUser)
                {
                    refreshPopShowRegion(mapProvince.name, colIndex + 1);
                }
                else
                {
                    refreshPopShowRegion(DistrictManager.GetInstance().getDistrictName(MainModel.User.DBID), colIndex + 1);
                }
            }
            else if (curDrillLevel == 1)
            {
                string cityname = dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Value as string;
                if (cityname != null)
                {
                    this.cityName = cityname;
                    this.curDrillLevel = 2;
                    refreshShowReport(true);
                    refreshPopShowRegion(cityname, colIndex + 1);
                }
            }
        }

        private void miExp2Word_Click(object sender, EventArgs e)
        {
            if (dataGridView.RowCount<=0)
            {
                MessageBox.Show("当前无数据可导出！");
                return;
            }
            SelectSavePath();
        }
        private void SelectSavePath()
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Title = "选择要保存文档的路径";
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Filter = FilterHelper.Excel;
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                ExcelControl excel = new ExcelControl();
                try
                {
                    ExportInfo(excel);
                    excel.SaveFile(saveFileDlg.FileName);
                }
                catch
                {
                    MessageBox.Show("导出数据出错！");
                    return;
                }
                finally 
                {
                    excel.CloseExcel();
                }
                if (DialogResult.Yes == MessageBox.Show(this, "Excel文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(saveFileDlg.FileName);
                    }
                    catch
                    {
                        MessageBox.Show("打开失败!\r\n文件名:" + saveFileDlg.FileName);
                    }
                }
            }
        }

        public void ExportInfo(ExcelControl excel)
        {
            string dirPath = Path.Combine(Application.StartupPath, "KPIPictTemp");
            Directory.CreateDirectory(dirPath);

            KPIExport2XlsParam excelParam = new KPIExport2XlsParam();
            excelParam.excelApp = excel;
            excelParam.dgv = dataGridView;
            excelParam.pictPath = dirPath;
            excelParam.title = cbxReportSel.SelectedItem.ToString() +"_"+ cbxShowType.SelectedItem.ToString();
            WaitBox.Show(this, printPict, excelParam);
        }

        private void printPict(object param)
        {
            WaitBox.Text = "正在生成图片...";
            KPIExport2XlsParam wordParam = param as KPIExport2XlsParam;
            for (int i = 2; i < dataGridView.ColumnCount; i++)
            {
                KPIInfoPanelHelper.FreshShowChart_GIS(i, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, cbxContentType), defaultRangeX, defaultRangeY, KPIShowType, sortAreaType);
                string pictPath = wordParam.pictPath + Path.DirectorySeparatorChar + i.ToString() + ".jpg";
                chartControl.ExportToImage(pictPath, System.Drawing.Imaging.ImageFormat.Jpeg);
                WaitBox.ProgressPercent = (int)(i * 100.0 / dataGridView.ColumnCount);
            }
            fireExp2Xls(wordParam);
        }

        private void fireExp2Xls(object param)
        {
            KPIExport2XlsParam excelParam = param as KPIExport2XlsParam;
            excelParam.excelApp.Sheet.Name = "KPI报表_" + excelParam.title;
            excelParam.excelApp.ExportExcel(excelParam.excelApp.Sheet, excelParam.dgv);
            object cellHeightPt = ((Microsoft.Office.Interop.Excel.Range)(excelParam.excelApp.Sheet.Cells[1, 1])).Height;
            float fCellHeightPt = float.Parse(cellHeightPt.ToString());//Excel单元格的高度（磅）
            float picWidth = chartControl.Width / 96f * 72;//图片的宽度（磅）
            float picHeight = chartControl.Height / 96f * 72f;//图片的高度（磅）
            int h = (int)(picHeight / fCellHeightPt + 1);
            for (int index = 2; index < excelParam.dgv.ColumnCount; index++)//插入图片
            {
                int rowIndex=excelParam.dgv.RowCount+2+h*(index -2);
                string pictPath = excelParam.pictPath + Path.DirectorySeparatorChar + index.ToString() + ".jpg";
                excelParam.excelApp.InsertPicture(rowIndex, pictPath, picWidth,picHeight);
            }
            WaitBox.Close();
        }

        private bool isChartFocused = false;
        public bool IsChartFocused
        {
            get { return isChartFocused; }
        }
        private void chartControl_MouseEnter(object sender, EventArgs e)
        {
            chartControl.Focus();
            isChartFocused = true;
        }
        
        private void chartControl_MouseLeave(object sender, EventArgs e)
        {
            isChartFocused = false;
            this.Focus();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            WelcomForm welFrm = this.Parent.Parent.Parent as WelcomForm;
           if (welFrm!=null)
           {
               welFrm.ExpAll2Xls();
           }
        }

        private void exp2Xls_Click(object sender, EventArgs e)
        {
            SelectSavePath();
        }

        private void checkedCbxMonth_EditValueChanged(object sender, EventArgs e)
        {
            if (checkMonthState)
            {
                needFreshGrid = true;
            }
            else
            {
                needFreshGrid = false;
            }
        }
        private bool needFreshGrid = false;
        private bool checkMonthState = false;
        public class KPIExport2XlsParam
        {
            public ExcelControl excelApp { get; set; }
            public string pictPath { get; set; }
            public DataGridView dgv { get; set; }
            public string title { get; set; }
        }

        private void checkedComboBoxEdit1_Properties_Closed(object sender, DevExpress.XtraEditors.Controls.ClosedEventArgs e)
        {
            if (needFreshGrid)
            {
                if (checkedCbxMonth.Properties.Tag is FinalShowResult)
                {
                    showFinalShowResult();
                }
                else if (checkedCbxMonth.Properties.Tag is List<FinalShowResult>)
                {
                    showFinalShowResultList();
                }
            }
        }

        private void showFinalShowResult()
        {
            FinalShowResult rSet = checkedCbxMonth.Properties.Tag as FinalShowResult;
            if (rSet != null)
            {
                if (KPIShowType == 2)
                {
                    rSet = getKPISort(rSet);
                    sortKPI(rSet, sortAreaType == 1 ? 3 : 5);
                    curShowRes = rSet;
                }
                showInGrid(rSet, false);
                if (KPIShowType == 2 && cbxLegend.Items.Count > 0)
                {
                    if (curSelKPICol == -1)
                    {
                        cbxLegend.SelectedIndex = 0;
                    }
                    else
                    {
                        cbxLegend.SelectedIndex = curSelKPICol;
                    }
                }
                if (tabSortAreaType.SelectedIndex == 1)
                {
                    KPIInfoPanelHelper.FreshShowChart_GIS(2, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, cbxContentType), defaultRangeX, defaultRangeY, KPIShowType, sortAreaType);
                }
            }
        }

        private void showFinalShowResultList()
        {
            List<FinalShowResult> rSetList = checkedCbxMonth.Properties.Tag as List<FinalShowResult>;
            FinalShowResult rSet = getShowResultCompetition(rSetList[0], rSetList[1], rSetList[2]);
            showInGrid(rSet, false);
            if (cbxLegend.Items.Count > 0)
            {
                if (curSelKPICol == -1)
                {
                    cbxLegend.SelectedIndex = 0;
                }
                else
                {
                    cbxLegend.SelectedIndex = curSelKPICol;
                }
            }
            if (tabSortAreaType.SelectedIndex == 1)
            {
                KPIInfoPanelHelper.FreshShowChart_GIS(2, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, cbxContentType), defaultRangeX, defaultRangeY, KPIShowType, sortAreaType);
            }
        }

        private void checkedCbxMonth_MouseDown(object sender, MouseEventArgs e)
        {
            checkedCbxMonth.Properties.DropDownRows = checkedCbxMonth.Properties.Items.Count + 1;
        }

        private void cbxContentType_EditValueChanged(object sender, EventArgs e)
        {
            if (!bRefreshShowReport)
            {
                refreshShowReport(false);
                if (tabSortAreaType.SelectedIndex == 1)
                {
                    KPIInfoPanelHelper.FreshShowChart_GIS(2, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, cbxContentType), defaultRangeX, defaultRangeY, KPIShowType, sortAreaType);
                }
            }
        }

        private void cbxContentType_MouseDown(object sender, MouseEventArgs e)
        {
            cbxContentType.Properties.DropDownRows = cbxContentType.Properties.Items.Count + 1;
        }

        private void refreshPopShowRegion(string name, int col)
        {
            //KillMapAInfo
            /**
            ClearPopShowRegion();
            if (MainModel != null)
            {
                MainModel.PopShowGISLabelDic.Clear();
            }
            PopShowMapUnit mapUnit = getMapUnitByName(name);
            if (mapUnit == null)
            {
                MessageBox.Show("无法获取" + name + "图元。");
                return;
            }
            lastRefreshName = name;
            lastRefreshCol = col;
            openRegionTable(mapUnit);
            try
            {
                Table table = Session.Current.Catalog.GetTable("PopShowRegion");
                FeatureCollection curFeatureCol = new FeatureCollection(table.TableInfo.Columns);
                MapInfo.Geometry.CoordSysFactory factory = Session.Current.CoordSysFactory;
                MapInfo.Geometry.CoordSys csysWGS84 = factory.CreateCoordSys("EPSG:4326");
                foreach (KeyValuePair<string, Feature> keyValue in mapUnit.nameFeatureDic)
                {
                    Feature ft = new Feature(table.TableInfo.Columns);
                    Color color = Color.White;
                    string value = "";
                    string colName = "";
                    List<string> competitionValues = null;
                    List<string> competitionColName = null;
                    if (!(cbxShowType.Text.Equals("按天") || cbxShowType.Text.Equals("按周") ||
                        (cbxShowType.Text.Equals("按月") && (checkedCbxMonth.Text.Contains(",") || checkedCbxMonth.Text.Contains("全部")))) && col > 0)
                    {
                        int rowCount = dataGridView.RowCount;
                        int colArea = 1;
                        if (KPIShowType == 2)
                        {
                            colArea = sortAreaType == 1 ? 2 : 4;
                        }
                        for (int row = 0; row < rowCount; row++)
                        {
                            if (dataGridView[colArea, row].Value.ToString().Equals(keyValue.Key))
                            {
                                if (KPIShowType == 3)
                                {
                                    double dValue;
                                    string sValueTmp;
                                    string colNameTmp;
                                    competitionValues = new List<string>();
                                    competitionColName = new List<string>();
                                    int beginCol = col - (col + 1) % 3;
                                    colNameTmp = dataGridView.Columns[beginCol].HeaderText;
                                    competitionColName.Add(colNameTmp);
                                    color = dataGridView[beginCol, row].Style.BackColor;
                                    if (color.Name.Equals("0"))
                                    {
                                        color = Color.White;
                                    }
                                    if (double.TryParse(dataGridView[beginCol, row].Value.ToString(), out dValue))
                                    {
                                        competitionValues.Add((Math.Round(dValue, 2)).ToString());
                                    }
                                    else
                                    {
                                        competitionValues.Add(dataGridView[beginCol, row].Value.ToString());
                                    }

                                    colNameTmp = dataGridView.Columns[beginCol + 1].HeaderText;
                                    competitionColName.Add(colNameTmp);
                                    color = dataGridView[beginCol + 1, row].Style.BackColor;
                                    if (color.Name.Equals("0"))
                                    {
                                        color = Color.White;
                                    }
                                    if (double.TryParse(dataGridView[beginCol + 1, row].Value.ToString(), out dValue))
                                    {
                                        competitionValues.Add((Math.Round(dValue, 2)).ToString());
                                    }
                                    else
                                    {
                                        competitionValues.Add(dataGridView[beginCol + 1, row].Value.ToString());
                                    }

                                    colNameTmp = dataGridView.Columns[beginCol + 2].HeaderText;
                                    competitionColName.Add(colNameTmp);
                                    color = dataGridView[beginCol + 2, row].Style.BackColor;
                                    if (color.Name.Equals("0"))
                                    {
                                        color = Color.White;
                                    }
                                    if (double.TryParse(dataGridView[beginCol + 2, row].Value.ToString(), out dValue))
                                    {
                                        competitionValues.Add((Math.Round(dValue, 2)).ToString());
                                    }
                                    else
                                    {
                                        competitionValues.Add(dataGridView[beginCol + 2, row].Value.ToString());
                                    }
                                    break;
                                }
                                else
                                {
                                    colName = dataGridView.Columns[col].HeaderText;
                                    color = dataGridView[col, row].Style.BackColor;
                                    if (color.Name.Equals("0"))
                                    {
                                        color = Color.White;
                                    }
                                    double dValue;
                                    if (double.TryParse(dataGridView[col, row].Value.ToString(), out dValue))
                                    {
                                        value = (Math.Round(dValue, 2)).ToString();
                                    }
                                    else
                                    {
                                        value = dataGridView[col, row].Value.ToString();
                                    }
                                    break;
                                }
                            }
                        }
                    }

                    MapInfo.Styles.SimpleInterior simpleInterior = new MapInfo.Styles.SimpleInterior(2, color);
                    MapInfo.Styles.SimpleLineStyle simpleLineStyle = new MapInfo.Styles.SimpleLineStyle();
                    LineWidth lineWidth = new LineWidth(1, LineWidthUnit.Pixel);
                    simpleLineStyle.Width = lineWidth;
                    MapInfo.Styles.AreaStyle areaStyle = new MapInfo.Styles.AreaStyle(simpleLineStyle, simpleInterior);
                    MapInfo.Styles.CompositeStyle compositeStyle = new MapInfo.Styles.CompositeStyle(areaStyle, null, null, null);
                    ft.Geometry = keyValue.Value.Geometry;
                    ft["MI_Style"] = compositeStyle;
                    ft["Name"] = keyValue.Key;
                    if (KPIShowType == 3)
                    {
                        ft["Info"] = competitionColName.Count <= 0 ? "" : competitionColName[0] + ":" + competitionValues[0] + "\r\n" + competitionColName[1] + 
                            ":" + competitionValues[1] + "\r\n" + competitionColName[2] + ":" + competitionValues[2];
                    }
                    else
                    {
                        ft["Info"] = colName.Equals("") ? "" : colName + ":" + value;
                    }
                    curFeatureCol.Add(ft);
                    curNameFeatureDic[keyValue.Key] = ft;
                    if (MainModel != null && KPIShowType != 3 && !value.Equals(""))
                    {
                        MainModel.PopShowGISLabelDic.Add(ft.Geometry.Centroid, value);
                    }
                }
                table.BeginAccess(TableAccessMode.Write);
                table.InsertFeatures(curFeatureCol);
                table.EndAccess();
            }
            catch
            {
                
            }
            mapControl.Map.SetView(popShowRegionLayer);
            //*/
        }

        private void createPopShowRegionLayer()
        {
            //KillMapAInfo
            /**
            try
            {
                TableInfoMemTable tableInfoRegion = new TableInfoMemTable("PopShowRegion");
                tableInfoRegion.Temporary = true;
                tableInfoRegion.Columns.Add(ColumnFactory.CreateFeatureGeometryColumn("MI_Geometry", mapControl.Map.GetDisplayCoordSys()));
                tableInfoRegion.Columns.Add(ColumnFactory.CreateStyleColumn());
                tableInfoRegion.Columns.Add(ColumnFactory.CreateStringColumn("Name", 20));
                tableInfoRegion.Columns.Add(ColumnFactory.CreateStringColumn("Info", 100));
                TableSessionInfo tsiRegion = new MapInfo.Data.TableSessionInfo();
                tsiRegion.UserClose = false;
                tsiRegion.UserDisplayMap = false;
                tsiRegion.UserMap = false;
                tsiRegion.UserRemoveMap = false;
                Table tableRegion = Session.Current.Catalog.CreateTable(tableInfoRegion, tsiRegion);
                popShowRegionLayer = new FeatureLayer(tableRegion, "指标GIS呈现", "PopShowRegion");
                mapControl.Map.Layers.Insert(0, popShowRegionLayer);
            }
            catch (Exception ex)
            {
                MessageBox.Show("载入地图时发生错误" + ex);
            }
            
            mapControl.Map.Layers.Insert(0, popShowGISLayer);
            //*/
        }

        /**
        private void ClearPopShowRegion()
        {
            //KillMapAInfo
            MIConnection connection = new MIConnection();
            connection.Open();
            MICommand cmd = connection.CreateCommand();
            cmd.CommandText = "DELETE PopShowRegion";
            cmd.ExecuteNonQuery();
            cmd.Dispose();
            connection.Close();
            connection.Dispose();

            curNameFeatureDic.Clear();
            curSelFeature = null;
        }

        private void openRegionTable(PopShowMapUnit mapUnit)
        {
            //KillMapAInfo
            if (mapUnit.nameFeatureDic.Count > 0)
            {
                return;
            }
            string[] path = mapUnit.filePath.Split(';');
            string regionTableName = path[0];
            string columnName = path[1];
            string typeName = null;
            Table table = null;
            try
            {
                if (!System.IO.File.Exists(Application.StartupPath + regionTableName))
                {
                    MessageBox.Show("找不到图层文件：" + Application.StartupPath + regionTableName);
                    return;
                }
                System.IO.FileInfo fileInfo = new System.IO.FileInfo(Application.StartupPath + regionTableName);
                typeName = fileInfo.Name;
                if (typeName.IndexOf('.') >= 0)
                {
                    typeName = typeName.Substring(0, typeName.IndexOf('.'));
                }
                table = Session.Current.Catalog.OpenTable(Application.StartupPath + regionTableName, typeName);
            }
            catch
            {
                return;
            }
            MIConnection connection = new MIConnection();
            connection.Open();
            try
            {
                MICommand command = connection.CreateCommand();
                command.CommandText = "Select * from " + table.Alias;
                MIDataReader dataReader = command.ExecuteReader();
                DataTable schema = dataReader.GetSchemaTable();
                List<string> nameColumnNames = new List<string>();
                foreach (DataRow dataRow in schema.Rows)
                {
                    if (dataRow["DataType"] == typeof(string))
                    {
                        nameColumnNames.Add((string)dataRow["ColumnName"]);
                    }
                }
                foreach (Feature feature in table)
                {
                    string name = feature[columnName] as string;
                    if (name != null && name.Trim().Length > 0)
                    {
                        Geometry geometry = feature["Obj"] as Geometry;
                        if (!(geometry is LegacyText))
                        {
                            mapUnit.nameFeatureDic[name] = feature;
                        }
                    }
                }
                dataReader.Close();
                command.Cancel();
                command.Dispose();
            }
            catch
            {
            }
            finally
            {
                connection.Close();
            }
            //
        }
        */

        private void loadMapLevelSetting()
        {
            mapProvince = new PopShowMapProvince();
            if (!System.IO.File.Exists(Application.StartupPath + @"\config\PopShowMapSetting.xml"))
            {
                MessageBox.Show("缺少文件PopShowMapSetting.xml");
            }
            XmlConfigFile configFile = new XmlConfigFile(string.Format(Application.StartupPath + "/config/PopShowMapSetting.xml"));
            mapProvince.Param = configFile.GetItemValue("PopShowMapSetting", "MapProvince") as Dictionary<string, object>;
        }

        /**
        private PopShowMapUnit getMapUnitByName(string name)
        {
            if (mapProvince.name.Equals(name))
            {
                return mapProvince;
            }
            foreach (PopShowMapCity city in mapProvince.citys)
            {
                if (city.name.Equals(name))
                {
                    return city;
                }
                foreach (PopShowMapRegion region in city.regions)
                {
                    if (region.name.Equals(name))
                    {
                        return region;
                    }
                }
            }
            return null;
        }
        */

        private void lstLegend_DrawItem(object sender, DrawItemEventArgs e)
        {
            if (e.Index < 0)
            {
                return;
            }
            object item = lstLegend.Items[e.Index];
            if (item is string)
            {
                e.Graphics.DrawString(item as string, lstLegend.Font, Brushes.Black, new RectangleF(e.Bounds.X, e.Bounds.Y + 2, e.Bounds.Width, e.Bounds.Height));
            }
            else if (item is DTParameterRangeColor)
            {
                DTParameterRangeColor rangeColor = item as DTParameterRangeColor;
                Brush brush = new SolidBrush(rangeColor.Value);
                e.Graphics.FillRectangle(brush, e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                e.Graphics.DrawString(rangeColor.RangeDescriptionForPopShowGIS, lstLegend.Font, Brushes.Black, new RectangleF(e.Bounds.X, e.Bounds.Y + 2, e.Bounds.Width, e.Bounds.Height));
            }
        }

        private void btnLegend_Click(object sender, EventArgs e)
        {
            if (btnLegend.Text.Equals("-"))
            {
                btnLegend.Text = "+";
            }
            else
            {
                btnLegend.Text = "-";
            }
            lstLegend.Visible = btnLegend.Text.Equals("-");
        }

        private void cbxLegend_SelectedIndexChanged(object sender, EventArgs e)
        {
            PopShowKPIColumn column = (PopShowKPIColumn)cbxLegend.SelectedItem;
            if (KPIShowType == 2 && !bRefreshShowReport)
            {
                sortKPI(curShowRes, column.colIndex);
                showInGrid(curShowRes, true);
            }
            if (KPIShowType == 1)
            {
                showData(column);
            }
            else if (KPIShowType == 2)
            {
                showDataRanking(column);
            }
            else if (KPIShowType == 3)
            {
                showCompetitionRes(column);
            }
            dataGridView_CellClick(sender, new DataGridViewCellEventArgs(column.colIndex, 0));
        }

        private void showData(PopShowKPIColumn column)
        {
            if (!dataGridView.Columns[column.colIndex].Displayed)
            {
                dataGridView.FirstDisplayedScrollingColumnIndex = column.colIndex;
                dataGridView[column.colIndex, 0].Selected = true;
            }
        }

        private void showDataRanking(PopShowKPIColumn column)
        {
            for (int i = sortAreaType == 1 ? 3 : 5; i < dataGridView.Columns.Count; i++)
            {
                if (i != column.colIndex)
                {
                    dataGridView.Columns[i].Visible = false;
                }
                else
                {
                    dataGridView.Columns[i].Visible = true;
                }
            }
            lblKPIName.Text = column.columnName;
            curSelKPICol = cbxLegend.SelectedIndex;
        }

        private void showCompetitionRes(PopShowKPIColumn column)
        {
            for (int i = 2; i < dataGridView.Columns.Count; i++)
            {
                if (i < column.colIndex - 1 || i > column.colIndex + 1)
                {
                    dataGridView.Columns[i].Visible = false;
                }
                else
                {
                    dataGridView.Columns[i].Visible = true;
                }
            }
            lblKPIName.Text = cbxLegend.Text;
            curSelKPICol = cbxLegend.SelectedIndex;
        }

        /**
private void mapControl_MouseClick(object sender, MouseEventArgs e)
{
  //KillMapAInfo
 
  PointF displayPoint = new PointF(e.X, e.Y);
  DPoint mapPoint = new DPoint();
  mapControl.Map.DisplayTransform.FromDisplay(displayPoint, out mapPoint);
  Table table = Session.Current.Catalog.GetTable("PopShowRegion");
  string levelName = "";
  foreach (Feature ft in table)
  {
      if (ft.Geometry.ContainsPoint(mapPoint))
      {
          levelName = ft["Name"].ToString();
          break;
      }
  }
  if (!levelName.Trim().Equals(""))
  {
      drillLevel(levelName);
  }
  //
}

private void drillLevel(string levelName)
{
   for (int r = 0; r < dataGridView.Rows.Count; r++)
   {
       if (dataGridView[1, r].Value.ToString().Equals(levelName))
       {
           dataGridView_CellDoubleClick(null, new DataGridViewCellEventArgs(1, r));
       }
   }
}
*/

        private PopShowMapProvince mapProvince;
        //KillMapAInfo
        /**
        private Dictionary<string, Feature> curNameFeatureDic = new Dictionary<string, Feature>();
        private Feature curSelFeature = null;
        MapFormPopShowGISLayer popShowGISLayer = new MapFormPopShowGISLayer("标注图层", "Label");
        FeatureLayer popShowRegionLayer = null;
        //*/
        private string lastRefreshName = "";
        /// <summary>
        /// GIS最后显示列
        /// </summary>
        private int lastRefreshCol = -1;
        /// <summary>
        /// 1.指标列表 2.指标排名 3.三网对比
        /// </summary>
        private int KPIShowType = 1;
        /// <summary>
        /// 1.地市排名 2.网格排名
        /// </summary>
        private int sortAreaType = 1;
        /// <summary>
        /// 1.正序 2.倒序
        /// </summary>
        private int sortType = 1;
        private int curSelKPICol = -1;
        private FinalShowResult curShowRes = new FinalShowResult();
        private bool bRefreshShowReport = false;
        private string sortDescColumns = "呼叫全程完好率|呼叫全程成功率|接通率|MOS占比|RxQuality占比";
        private string sortAscColumns = "掉话率";
        /// <summary>
        /// 当前运营商：0 移动，1 联通，2 电信
        /// </summary>
        private int curOperator = 0;

        public class PopShowMapUnit
        {
            public string name { get; set; }
            public string filePath { get; set; }
        }

        public class PopShowMapProvince : PopShowMapUnit
        {
            public List<PopShowMapCity> citys { get; set; } = new List<PopShowMapCity>();
            public Dictionary<string, object> Param
            {
                get
                {
                    Dictionary<string, object> param = new Dictionary<string, object>();
                    param["Name"] = name;
                    param["FilePath"] = filePath;
                    param["Citys"] = citys;
                    return param;
                }
                set
                {
                    if (value.ContainsKey("Name"))
                    {
                        object obj = value["Name"];
                        name = obj == null ? "" : ((string)obj).Trim();
                    }
                    if (value.ContainsKey("FilePath"))
                    {
                        object obj = value["FilePath"];
                        filePath = obj == null ? "" : ((string)obj).Trim();
                    }
                    if (value.ContainsKey("Citys"))
                    {
                        List<object> objList = (List<object>)value["Citys"];
                        foreach (object o in objList)
                        {
                            Dictionary<string, object> param = (Dictionary<string, object>)o;
                            PopShowMapCity city = new PopShowMapCity();
                            city.Param = param;
                            citys.Add(city);
                        }
                    }
                }
            }
        }
        public class PopShowMapCity : PopShowMapUnit
        {
            public List<PopShowMapRegion> regions { get; set; } = new List<PopShowMapRegion>();
            public Dictionary<string, object> Param
            {
                get
                {
                    Dictionary<string, object> param = new Dictionary<string, object>();
                    param["Name"] = name;
                    param["FilePath"] = filePath;
                    param["Regions"] = regions;
                    return param;
                }
                set
                {
                    if (value.ContainsKey("Name"))
                    {
                        object obj = value["Name"];
                        name = obj == null ? "" : ((string)obj).Trim();
                    }
                    if (value.ContainsKey("FilePath"))
                    {
                        object obj = value["FilePath"];
                        filePath = obj == null ? "" : ((string)obj).Trim();
                    }
                    if (value.ContainsKey("Regions"))
                    {
                        List<object> objList = (List<object>)value["Regions"];
                        foreach (object o in objList)
                        {
                            Dictionary<string, object> param = (Dictionary<string, object>)o;
                            PopShowMapRegion region = new PopShowMapRegion();
                            region.Param = param;
                            regions.Add(region);
                        }
                    }
                }
            }
        }
        public class PopShowMapRegion : PopShowMapUnit
        {
            public Dictionary<string, object> Param
            {
                get
                {
                    Dictionary<string, object> param = new Dictionary<string, object>();
                    param["Name"] = name;
                    param["FilePath"] = filePath;
                    return param;
                }
                set
                {
                    if (value.ContainsKey("Name"))
                    {
                        object obj = value["Name"];
                        name = obj == null ? "" : ((string)obj).Trim();
                    }
                    if (value.ContainsKey("FilePath"))
                    {
                        object obj = value["FilePath"];
                        filePath = obj == null ? "" : ((string)obj).Trim();
                    }
                }
            }
        }

        private class PopShowKPIColumn
        {
            public PopShowKPIColumn(string colName, int colIndex)
            {
                this.columnName = colName;
                this.colIndex = colIndex;
            }
            public string columnName;
            public int colIndex;
            public override string ToString()
            {
                return columnName;
            }
        }

        private void rdbKPIList_CheckedChanged(object sender, EventArgs e)
        {
            if (rdbKPIList.Checked)
            {
                pnlKPIContent.Visible = false;
                KPIShowType = 1;
                refreshShowReport(false);
            }
        }

        private void rdbKPISort_CheckedChanged(object sender, EventArgs e)
        {
            if (rdbKPISort.Checked)
            {
                tabSortAreaType.Visible = true;
                btnSortType.Visible = true;
                pnlKPIContent.Height = 56;
                btnSelectKPI.Top = lblKPIName.Top + 6;
                pnlKPIContent.Visible = true;
                KPIShowType = 2;
                refreshShowReport(false);
            }
        }

        private void rdbKPICompetition_CheckedChanged(object sender, EventArgs e)
        {
            if (rdbKPICompetition.Checked)
            {
                tabSortAreaType.Visible = false;
                btnSortType.Visible = false;
                pnlKPIContent.Height = 35;
                btnSelectKPI.Top = lblKPIName.Top + 6;
                pnlKPIContent.Visible = true;
                KPIShowType = 3;
                refreshShowReport(false);
            }
        }

        private void tabSortAreaType_SelectedIndexChanged(object sender, EventArgs e)
        {
            sortAreaType = tabSortAreaType.SelectedIndex + 1;
            curSelKPICol = -1;
            refreshShowReport(false);
        }

        private void btnSortType_Click(object sender, EventArgs e)
        {
            if (sortType == 1)
            {
                sortType++;
            }
            else
            {
                sortType--;
            }
            cbxLegend_SelectedIndexChanged(null, null);
        }

        private void btnSelectKPI_Click(object sender, EventArgs e)
        {
            mnuSortLegend.Show(lblKPIName, new System.Drawing.Point(btnSelectKPI.Right - mnuSortLegend.Width, btnSelectKPI.Top));
        }

        private void mnuSortLegendItem_Click(object sender, EventArgs e)
        {
            int index = (int)((ToolStripItem)sender).Tag;
            cbxLegend.SelectedIndex = index;
        }

        private void tabShow_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (tabShow.SelectedIndex == 1)
            {
                KPIInfoPanelHelper.FreshShowChart_GIS(((PopShowKPIColumn)cbxLegend.SelectedItem).colIndex, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, cbxContentType), defaultRangeX, defaultRangeY, KPIShowType, sortAreaType);
            }
        }
    }
}
