﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Model.PerformanceParam;
using MasterCom.RAMS.Model.PerformanceParam.TD切换分析;

namespace MasterCom.RAMS.Model.PerformanceParam.TD切换分析
{
    /// <summary>
    /// 获得TD联合查询的数据
    /// </summary>
    class DiySqlQueryJointHandoverAnalysisGetDataTD : DiySqlQueryJointAwitchingAnalysisGetDate
    {
        public DiySqlQueryJointHandoverAnalysisGetDataTD(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return "select  iMon,iLac,iCi,iTargetLac,iTargetCi,iNet,iHoNum,foutRate,fintRate,iReq,iSucc,fSuccRate,iLevel,strcomment  from tb_para_utrancell_handover_ana where iMon=" + imon + " and  iReq >= 100 order by iMon";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[14];
            rType[0] = E_VType.E_Int;

            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;

            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;

            rType[5] = E_VType.E_Int;

            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_String;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_Int;
            rType[11] = E_VType.E_String;
            rType[12] = E_VType.E_Int;
            rType[13] = E_VType.E_String;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            string date = imon.ToString().Substring(0, 4) + "-" + imon.ToString().Substring(4, 2);
            DateTime dateTime = Convert.ToDateTime(date);
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package, dateTime);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package, DateTime dateTime)
        {
            JoinHandoverAnalysisTD analysis = new JoinHandoverAnalysisTD();
            analysis.IMon = package.Content.GetParamInt();

            analysis.ILac = package.Content.GetParamInt();
            analysis.ICi = package.Content.GetParamInt();
            analysis.ITargetLac = package.Content.GetParamInt();
            analysis.ITargetCi = package.Content.GetParamInt();

            analysis.INet = package.Content.GetParamInt();

            analysis.IHoNum = package.Content.GetParamInt();
            analysis.FoutRate = Convert.ToDecimal(package.Content.GetParamString()) / 100;
            analysis.FintRate = Convert.ToDecimal(package.Content.GetParamString()) / 100;
            analysis.IReq = package.Content.GetParamInt();
            analysis.ISucc = package.Content.GetParamInt();
            analysis.FSuccRate = Convert.ToDecimal(package.Content.GetParamString()) / 100;
            analysis.ILevel = package.Content.GetParamInt();
            analysis.Strcomment = package.Content.GetParamString();
            //analysis.Cell = 
            TDCell tdCellTemp = mainModel.CellManager.GetTDCell(dateTime, analysis.ILac, analysis.ICi);
            if (tdCellTemp != null)
            {
                AnalysisList.Add(analysis);
            }
        }

        public override string Name
        {
            get { return "DiySqlQueryJointHandoverAnalysisGetDateTD"; }
        }
    }
}
