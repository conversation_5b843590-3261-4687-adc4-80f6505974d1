﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public partial class OwnFuncEditorDlg : BaseForm
    {
        CryptionData cdata = new CryptionData();
        public OwnFuncEditorDlg()
        {
            InitializeComponent();
        }

        private void cbxColorOwnFunc_SelectedIndexChanged(object sender, EventArgs e)
        {
            comboBoxType.SelectedItem = null;
            OwnFuncCommander commander = cbxColorOwnFunc.SelectedItem as OwnFuncCommander;
            if(commander!=null)
            {
                tbxFuncName.Text = commander.funcName;
                tbxDesc.Text = commander.desc;
                tbxMinF.Text = commander.minF.ToString();
                tbxMaxF.Text = commander.maxF.ToString();
                tbxCodeInput.Text = cdata.DecryptionStringdata(commander.codeString);
                tbxNoteDesc.Text = commander.descriptionNote;

                MapSerialInfo msi = getSerialInfo(commander);
                if (msi != null && msi.OwnColorFuncRanges != null)
                {
                    comboBoxType.SelectedItem = msi.Type;
                    colorRangePnl.SetRange(commander.minF, commander.maxF);
                    colorRangePnl.FillData(msi.OwnColorFuncRanges);
                }
            }
            else
            {
                tbxFuncName.Text = "";
                tbxDesc.Text = "";
                tbxCodeInput.Text = "";
            }
        }

        private void tbxMinF_TextChanged(object sender, EventArgs e)
        {
            reSetRange();
        }

        private void tbxMaxF_TextChanged(object sender, EventArgs e)
        {
            reSetRange();
        }

        private void buttonNew_Click(object sender, EventArgs e)
        {
            comboBoxType.SelectedItem = null;
            tbxFuncName.Text = "";
            tbxDesc.Text = "";
            tbxMinF.Text = "0";
            tbxMaxF.Text = "100";
            tbxCodeInput.Text = initInput();
            tbxNoteDesc.Text = "";

            colorRangePnl.SetRange(0, 100);
            colorRangePnl.FillData(new List<MasterCom.MControls.ColorRange>());
        }

        private string initInput()
        {
            StringBuilder sbInput = new StringBuilder();
            sbInput.Append("using System;\r\n");
            sbInput.Append("using MasterCom.RAMS.Model;\r\n");
            sbInput.Append("/*\r\n操作函数描述\r\n");
            sbInput.Append("**/\r\n");
            sbInput.Append("    class 操作函数名称\r\n");
            sbInput.Append("    {{\r\n");
            sbInput.Append("        public float GetFuncResult(TestPoint tp,string param)\r\n");
            sbInput.Append("        {{\r\n");
            sbInput.Append("        }}\r\n");
            sbInput.Append("    }}\r\n");
            return sbInput.ToString();
        }

        private void reSetRange()
        {
            float minF;
            float maxF;
            if (float.TryParse(tbxMinF.Text, out minF) &&
                float.TryParse(tbxMaxF.Text, out maxF) &&
                minF <= maxF)
            {
                colorRangePnl.SetRange(minF, maxF);
            }
        }

        private MapSerialInfo getSerialInfo(OwnFuncCommander commander)
        {
            foreach (MapSerialInfo msi in DTLayerSerialManager.Instance.CustomSerials)
            {
                if (msi.OwnColorFuncDesc == commander.desc)
                {
                    return msi;
                }
            }
            return null;
        }

        private bool judgeValidCommand(out float minF, out float maxF)
        {
            minF = 0;
            maxF = 0;
            if (comboBoxType.Text == string.Empty ||
               tbxFuncName.Text.Trim() == "" ||
               tbxDesc.Text.Trim() == "")
            {
                MessageBox.Show("类型名称，操作名称，描述名称不能为空！");
                return false;
            }
            
            if (!float.TryParse(tbxMinF.Text.ToString(), out minF))
            {
                MessageBox.Show("最小值需为数值！");
                return false;
            }
            if (!float.TryParse(tbxMaxF.Text.ToString(), out maxF))
            {
                MessageBox.Show("最大值需为数值！");
                return false;
            }
            if (minF >= maxF)
            {
                MessageBox.Show("最大值需要大于最小值！");
                return false;
            }

            return true;
        }

        private void btnSaveCommand_Click(object sender, EventArgs e)
        {
            float minF;
            float maxF;
            bool isValid = judgeValidCommand(out minF, out maxF);

            if(!isValid)
            {
                return;
            }
            
            OwnFuncCommander commander = new OwnFuncCommander();
            commander.funcName = tbxFuncName.Text.Trim();
            commander.desc = tbxDesc.Text.Trim();
            commander.descriptionNote = tbxNoteDesc.Text.Trim();       
            commander.minF = minF;
            commander.maxF = maxF;
            commander.codeString = cdata.EncryptionStringData(tbxCodeInput.Text);
            commander._classReady = false;
            commander._hasError = false;
            string retStringErr =commander.initFuncClass_TestPoint();
            if(!commander._classReady)
            {
                MessageBox.Show("Check Error" + retStringErr);
                return;
            }
            bool bExist = false;
            foreach (OwnFuncCommander cmd in cbxColorOwnFunc.Items)
            {
                if(cmd.funcName==commander.funcName || cmd.desc == commander.desc)
                {
                    bExist = true;
                    if (DialogResult.OK == MessageBox.Show(this, "该操作函数" + commander.funcName + "," + commander.desc + "存在同名,是否替换？", "保存", MessageBoxButtons.OKCancel))
                    {
                        cmd.funcName = commander.funcName;
                        cmd.desc = commander.desc;
                        cmd.codeString = commander.codeString;
                        cmd.descriptionNote = commander.descriptionNote;
                        cmd.minF = commander.minF;
                        cmd.maxF = commander.maxF;
                        cmd._classReady = false;
                        cmd._hasError = false;
                        cmd.clzzInst = null;
                        return;
                    }
                }
            }
            if (!bExist)
            {
                DialogResult res = MessageBox.Show(this, "正在保存新增操作函数" + commander.funcName + "," + commander.desc + ",确认增加？", "增加", MessageBoxButtons.OKCancel);
                if (res == DialogResult.OK)
                {
                    MapSerialInfo msi = new MapSerialInfo();
                    msi.Type = comboBoxType.Text;
                    msi.Name = commander.funcName;
                    msi.ColorParamEnabled = true;
                    msi.OwnColorFuncDesc = commander.desc;
                    msi.OwnColorFuncRanges = colorRangePnl.ColorRanges;

                    DTLayerSerialManager.Instance.AddCustomSerial(msi);
                    FavoriteSerialConfig.Instance.AddFavorite(msi);

                    cbxColorOwnFunc.Items.Add(commander);
                    cbxColorOwnFunc.SelectedItem = commander;

                    mainModel.MainForm.GetMapForm().GetDTLayer().OwnFuncCommanderDic[commander.desc] = commander;
                }
            }
                
        }
        private void btnDeleteFunc_Click(object sender, EventArgs e)
        {
            OwnFuncCommander commander = cbxColorOwnFunc.SelectedItem as OwnFuncCommander;
            if (commander != null)
            {
                DialogResult res = MessageBox.Show(this, "删除该操作函数" + commander.desc + "？", "删除", MessageBoxButtons.OKCancel);
                if (res == DialogResult.OK)
                {
                    cbxColorOwnFunc.Items.Remove(commander);

                    mainModel.MainForm.GetMapForm().GetDTLayer().OwnFuncCommanderDic.Remove(commander.desc);

                    MapSerialInfo msi = getSerialInfo(commander);
                    if(msi != null)
                        DTLayerSerialManager.Instance.RemoveCustomSerial(msi);
                }
            }
        }

        internal void FillCommanders(Dictionary<string, OwnFuncCommander> commandersDic)
        {
            comboBoxType.Items.Clear();
            foreach (MapSerialInfo msi in DTLayerSerialManager.Instance.CustomSerials)
            {
                if (!comboBoxType.Items.Contains(msi.Type))
                    comboBoxType.Items.Add(msi.Type);
            }

            cbxColorOwnFunc.Items.Clear();
            foreach(OwnFuncCommander commander in commandersDic.Values)
            {
                cbxColorOwnFunc.Items.Add(commander);
            }

            if (cbxColorOwnFunc.Items.Count > 0)
            {
                cbxColorOwnFunc.SelectedIndex = 0;
            }
        }
        internal Dictionary<string, OwnFuncCommander> GetResultDic()
        {
            Dictionary<string, OwnFuncCommander> retDic = new Dictionary<string, OwnFuncCommander>();
            foreach(OwnFuncCommander commander in cbxColorOwnFunc.Items)
            {
                retDic[commander.desc] = commander;
            }
            return retDic;
        }
    }
}