using System;
using System.Collections.Generic;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using MasterCom.ES.ColorManager;
using MasterCom.RAMS.Util;


namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellOccupyShortInfoForm : MinCloseForm
    {
        MapForm mapForm;

        List<CellOccupySortInfo> cellInfoList = new List<CellOccupySortInfo>();

        public CellOccupyShortInfoForm()
            : base()
        {
            InitializeComponent();
            this.mapForm = MainModel.MainForm.GetMapForm();
            init();
        }

        public void FillData(List<CellOccupySortInfo> cellInfoList)
        {
            this.cellInfoList = cellInfoList;
            objectListView.ClearObjects();
            objectListView.SetObjects(this.cellInfoList);
        }

        private void init()
        {
            olvColumnSN.AspectGetter = delegate(object row)
            {
                IList list = objectListView.Objects as IList;
                return list.IndexOf(row as CellOccupySortInfo) + 1;
            };
            olvColumnCellName.AspectGetter = delegate(object row)
            {
                CellOccupySortInfo item = row as CellOccupySortInfo;
                return item.cellName;
            };
            olvColumnLAC.AspectGetter = delegate(object row)
            {
                CellOccupySortInfo item = row as CellOccupySortInfo;
                return item.LAC;
            };
            olvColumnCI.AspectGetter = delegate(object row)
            {
                CellOccupySortInfo item = row as CellOccupySortInfo;
                return item.CI;
            };
            olvColumnFileName.AspectGetter = delegate(object row)
            {
                CellOccupySortInfo item = row as CellOccupySortInfo;
                return item.fileName;
            };
            olvColumnBeginDateTime.AspectGetter = delegate(object row)
            {
                CellOccupySortInfo item = row as CellOccupySortInfo;
                return item.BeginDateTimeWithMillisecondString;
            };
            olvColumnEndDateTime.AspectGetter = delegate(object row)
            {
                CellOccupySortInfo item = row as CellOccupySortInfo;
                return item.EndDateTimeWithMillisecondString;
            };
            olvColumnTimeLast.AspectGetter = delegate(object row)
            {
                CellOccupySortInfo item = row as CellOccupySortInfo;
                return item.TimeLast;
            };
            olvColumnDistanceLast.AspectGetter = delegate(object row)
            {
                CellOccupySortInfo item = row as CellOccupySortInfo;
                return item.DistanceLast;
            };
        }

        private void objectListView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            CellOccupySortInfo item = objectListView.SelectedObject as CellOccupySortInfo;
            this.MainModel.SelectedCells.Clear();
            this.MainModel.SelectedCells.Add(item.cell);
            this.MainModel.SelectedCell = item.cell;
            mapForm.GoToView(item.cell.Longitude, item.cell.Latitude);
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            ExcelOperator.ExportObjectListViewExcel(objectListView, true);
        }
    }
}

