﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TauHandoverConflictResultListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.ListViewTauHandover = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctmStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripExport = new System.Windows.Forms.ToolStripMenuItem();
            this.olvColumnDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.ListViewTauHandover)).BeginInit();
            this.ctmStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // ListViewTauHandover
            // 
            this.ListViewTauHandover.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewTauHandover.AllColumns.Add(this.olvColumnFileName);
            this.ListViewTauHandover.AllColumns.Add(this.olvColumnDate);
            this.ListViewTauHandover.AllColumns.Add(this.olvColumnTime);
            this.ListViewTauHandover.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnDate,
            this.olvColumnTime});
            this.ListViewTauHandover.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewTauHandover.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewTauHandover.FullRowSelect = true;
            this.ListViewTauHandover.GridLines = true;
            this.ListViewTauHandover.HeaderWordWrap = true;
            this.ListViewTauHandover.IsNeedShowOverlay = false;
            this.ListViewTauHandover.Location = new System.Drawing.Point(0, 0);
            this.ListViewTauHandover.Name = "ListViewTauHandover";
            this.ListViewTauHandover.OwnerDraw = true;
            this.ListViewTauHandover.ShowGroups = false;
            this.ListViewTauHandover.Size = new System.Drawing.Size(495, 366);
            this.ListViewTauHandover.TabIndex = 10;
            this.ListViewTauHandover.UseCompatibleStateImageBehavior = false;
            this.ListViewTauHandover.View = System.Windows.Forms.View.Details;
            this.ListViewTauHandover.VirtualMode = true;
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 40;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            this.olvColumnFileName.Width = 250;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "开始时间";
            this.olvColumnTime.Width = 100;
            // 
            // ctmStrip
            // 
            this.ctmStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripReplay,
            this.ToolStripExport});
            this.ctmStrip.Name = "ctmStrip";
            this.ctmStrip.Size = new System.Drawing.Size(154, 48);
            // 
            // ToolStripReplay
            // 
            this.ToolStripReplay.Name = "ToolStripReplay";
            this.ToolStripReplay.Size = new System.Drawing.Size(153, 22);
            this.ToolStripReplay.Text = "回放文件";
            this.ToolStripReplay.Click += new System.EventHandler(this.ToolStripReplay_Click);
            // 
            // ToolStripExport
            // 
            this.ToolStripExport.Name = "ToolStripExport";
            this.ToolStripExport.Size = new System.Drawing.Size(153, 22);
            this.ToolStripExport.Text = "导出Excel文件";
            this.ToolStripExport.Click += new System.EventHandler(this.ToolStripExport_Click);
            // 
            // olvColumnDate
            // 
            this.olvColumnDate.HeaderFont = null;
            this.olvColumnDate.Text = "开始日期";
            this.olvColumnDate.Width = 100;
            // 
            // TauHandoverConflictResultListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(495, 366);
            this.ContextMenuStrip = this.ctmStrip;
            this.Controls.Add(this.ListViewTauHandover);
            this.Name = "TauHandoverConflictResultListForm";
            this.Text = "TauHandoverConflictResultListForm";
            ((System.ComponentModel.ISupportInitialize)(this.ListViewTauHandover)).EndInit();
            this.ctmStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView ListViewTauHandover;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private System.Windows.Forms.ContextMenuStrip ctmStrip;
        private System.Windows.Forms.ToolStripMenuItem ToolStripReplay;
        private System.Windows.Forms.ToolStripMenuItem ToolStripExport;
        private BrightIdeasSoftware.OLVColumn olvColumnDate;
    }
}