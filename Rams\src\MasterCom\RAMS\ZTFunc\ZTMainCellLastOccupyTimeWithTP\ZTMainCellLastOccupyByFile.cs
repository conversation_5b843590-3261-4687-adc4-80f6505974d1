﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTMainCellLastOccupyByFile : ZTMainCellLastOccupyBase
    {

        public ZTMainCellLastOccupyByFile(MainModel mainModel)
            : base(mainModel)
        {

        }

        public override string Name
        {
            get { return "LTE主服占用时长(按文件)"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22098, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is LTETestPointDetail)
            {
                return true;
            }
            else
                return false;
        }
    }

    public class ZTMainCellLastOccupyByFile_GSM : ZTMainCellLastOccupyByFile
    {
        public ZTMainCellLastOccupyByFile_GSM(MainModel mainModel)
            : base(mainModel)
        {

        }

        ZTMainCellLastOccupySetGSMConditionForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTMainCellLastOccupySetGSMConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                resultList=new List<CellLastOccupyFileInfo>();
                hoCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        public override string Name
        {
            get
            {
                return "GSM主服占用时长(按文件)";
            }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12095, this.Name);
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is TestPointDetail)
            {
                return true;
            }
            else
                return false;
        }
    }

    public class ZTMainCellLastOccupyByFile_FDD : ZTMainCellLastOccupyBase_FDD
    {
        public ZTMainCellLastOccupyByFile_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "LTE_FDD主服占用时长(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is LTEFddTestPoint)
            {
                return true;
            }
            else
                return false;
        }
    }
}
