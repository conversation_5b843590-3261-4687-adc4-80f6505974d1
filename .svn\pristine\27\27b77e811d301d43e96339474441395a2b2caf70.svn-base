﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.Utils;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;

namespace MasterCom.Util
{
    public static class DevGridControlManager
    {
        /// <summary>
        /// 根据条件设置GridView列的背景色
        /// </summary>
        /// <param name="col">列</param>
        /// <param name="cond">条件</param>
        /// <param name="value">条件比较值</param>
        /// <param name="cellBackClr">背景色</param>
        public static void SetColumnStyle(GridColumn col, FormatConditionEnum cond, object value, Color cellBackClr)
        {
            StyleFormatCondition cn = new StyleFormatCondition(cond, col, null, value);
            cn.Appearance.BackColor = cellBackClr;
            col.View.FormatConditions.Add(cn);
        }

        /// <summary>
        /// 根据条件设置GridView列的背景色，应用到整行
        /// </summary>
        /// <param name="col"></param>
        /// <param name="cond"></param>
        /// <param name="value"></param>
        /// <param name="cellBackClr"></param>
        public static void SetRowStyleByColumn(GridColumn col, FormatConditionEnum cond, object value, Color cellBackClr)
        {
            StyleFormatCondition cn = new StyleFormatCondition(cond, col, null, value, value, true);
            cn.Appearance.BackColor = cellBackClr;
            col.View.FormatConditions.Add(cn);
        }

        /// <summary>
        /// 设置GridView列的显示样式
        /// </summary>
        /// <param name="col"></param>
        /// <param name="displayFormatType"></param>
        /// <param name="formatStr"></param>
        public static void SetColumnDisplayFormat(GridColumn col, FormatType displayFormatType, string formatStr)
        {
            col.DisplayFormat.FormatType = displayFormatType;
            col.DisplayFormat.FormatString = formatStr;
        }

        /// <summary>
        /// 设置当没有数据行的提示信息『CustomDrawEmptyForeground』
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="e">CustomDrawEventArgs</param>
        /// <param name="noRecordMsg">提示信息</param>
        public static void DrawNoRowCountMessage(GridView gridView, CustomDrawEventArgs e, string noRecordMsg)
        {
            if (gridView == null)
                throw new ArgumentNullException("gridView");
            if (gridView.RowCount == 0 && !string.IsNullOrEmpty(noRecordMsg))
            {
                Font font = new Font("宋体", 10, FontStyle.Bold);
                Rectangle rect = new Rectangle(e.Bounds.Left + 5, e.Bounds.Top + 5, e.Bounds.Width - 5, e.Bounds.Height - 5);
                e.Graphics.DrawString(noRecordMsg, font, Brushes.Red, rect);
            }
        }
    }
}
