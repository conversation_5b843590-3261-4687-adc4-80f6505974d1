﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRHandoverFailDlg : BaseForm
    {
        public NRHandoverFailDlg(NRHandoverFailCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(NRHandoverFailCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            chkAnaLte.Checked = condition.IsAnaLte;
        }

        public NRHandoverFailCondition GetCondition()
        {
            NRHandoverFailCondition condition = new NRHandoverFailCondition();
            condition.IsAnaLte = chkAnaLte.Checked;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }

    public class NRHandoverFailCondition
    {
       public bool IsAnaLte { get; set; } = false;
    }
}
