﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTTerminalCBXItemSQLQuery : DIYSQLBase
    {
        public ZTTerminalCBXItemSQLQuery(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "获取查询条件"; }
        }
        private readonly List<string> monthList = new List<string>();
        private readonly List<string> cityList = new List<string>();
        private readonly List<string> questionTypeList = new List<string>();
        /// <summary>
        /// 月份集合
        /// </summary>
        public List<string> MonthList
        {
            get { return monthList; }
        }
        /// <summary>
        /// 地市集合
        /// </summary>
        public List<string> CityList
        {
            get { return cityList; }
        }
        /// <summary>
        /// 问卷类型
        /// </summary>
        public List<string> QuestionTypeList
        {
            get { return questionTypeList; }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override string getSqlTextString()
        {
            string sql = "select distinct 地市,月份,问卷类型 from Complain_Sys..wlmq_user_satisfaction where Provider='中国移动' order by 月份 desc";
            return sql;
        }

        protected override MasterCom.RAMS.Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vtypes = new E_VType[3];
            vtypes[0] = E_VType.E_String;
            vtypes[1] = E_VType.E_String;
            vtypes[2] = E_VType.E_String;
            return vtypes;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            cityList.Clear();
            monthList.Clear();
            questionTypeList.Clear();
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            string city = package.Content.GetParamString();
            string ymonth = package.Content.GetParamString();
            string questionType = package.Content.GetParamString();
            if (!cityList.Contains(city))
            {
                cityList.Add(city);
            }
            if (!monthList.Contains(ymonth))
            {
                monthList.Add(ymonth);
            }
            if (!questionTypeList.Contains(questionType))
            {
                questionTypeList.Add(questionType);
            }
        }
    }
}
