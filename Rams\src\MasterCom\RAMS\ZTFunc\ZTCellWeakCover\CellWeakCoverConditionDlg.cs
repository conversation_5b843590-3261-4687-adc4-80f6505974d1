using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Frame
{
    public partial class CellWeakCoverConditionDlg : Form
    {
        public CellWeakCoverConditionDlg()
        {
            InitializeComponent();
        }

        private static CellWeakCoverConditionDlg dlg = new CellWeakCoverConditionDlg();
        public static CellWeakCoverConditionDlg GetDlg()
        {
            return dlg;
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public int RscpHigh
        {
            get { return int.Parse(spinEditHigh.Value.ToString()); }
        }

        public int RscpMean
        {
            get { return int.Parse(spinEditMean.Value.ToString()); }
        }
    }
}