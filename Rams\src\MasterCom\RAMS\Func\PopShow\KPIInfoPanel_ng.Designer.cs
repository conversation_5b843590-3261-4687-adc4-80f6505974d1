﻿namespace MasterCom.RAMS.Func.PopShow
{
    partial class KPIInfoPanel_ng
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions1 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.exp2Xls = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.cxtMsKPI_ng = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExp2Word = new System.Windows.Forms.ToolStripMenuItem();
            this.cbxReportSel = new System.Windows.Forms.ComboBox();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxShowType = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.panel2 = new System.Windows.Forms.Panel();
            this.cbxContentType = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.checkedCbxMonth = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.labelMonth = new System.Windows.Forms.Label();
            this.btnLevel0 = new System.Windows.Forms.Button();
            this.btnColor = new System.Windows.Forms.Button();
            this.splitMain = new System.Windows.Forms.SplitContainer();
            this.chartControl = new DevExpress.XtraCharts.ChartControl();
            this.panel3 = new System.Windows.Forms.Panel();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.cxtMsKPI_ng.SuspendLayout();
            this.panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxContentType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkedCbxMonth.Properties)).BeginInit();
            this.splitMain.Panel1.SuspendLayout();
            this.splitMain.Panel2.SuspendLayout();
            this.splitMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.BackColor = System.Drawing.Color.Silver;
            this.panel1.Controls.Add(this.exp2Xls);
            this.panel1.Controls.Add(this.button1);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(990, 30);
            this.panel1.TabIndex = 1;
            // 
            // exp2Xls
            // 
            this.exp2Xls.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.exp2Xls.Location = new System.Drawing.Point(831, 3);
            this.exp2Xls.Name = "exp2Xls";
            this.exp2Xls.Size = new System.Drawing.Size(75, 23);
            this.exp2Xls.TabIndex = 2;
            this.exp2Xls.Text = "导出本页";
            this.exp2Xls.UseVisualStyleBackColor = true;
            this.exp2Xls.Click += new System.EventHandler(this.exp2Xls_Click);
            // 
            // button1
            // 
            this.button1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.button1.Location = new System.Drawing.Point(912, 3);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(75, 23);
            this.button1.TabIndex = 1;
            this.button1.Text = "导出所有页面";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.ForeColor = System.Drawing.Color.White;
            this.label1.Location = new System.Drawing.Point(19, 9);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(61, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "KPI指标";
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.BackgroundColor = System.Drawing.Color.White;
            this.dataGridView.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.ContextMenuStrip = this.cxtMsKPI_ng;
            this.dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView.Location = new System.Drawing.Point(0, 0);
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.ReadOnly = true;
            this.dataGridView.RowHeadersVisible = false;
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.Size = new System.Drawing.Size(542, 370);
            this.dataGridView.TabIndex = 3;
            this.dataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView_CellDoubleClick);
            this.dataGridView.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView_CellClick);
            // 
            // cxtMsKPI_ng
            // 
            this.cxtMsKPI_ng.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExp2Word});
            this.cxtMsKPI_ng.Name = "cxtMsKPI_ng";
            this.cxtMsKPI_ng.Size = new System.Drawing.Size(179, 26);
            // 
            // miExp2Word
            // 
            this.miExp2Word.Name = "miExp2Word";
            this.miExp2Word.Size = new System.Drawing.Size(178, 22);
            this.miExp2Word.Text = "导出数据到Excel...";
            this.miExp2Word.Click += new System.EventHandler(this.miExp2Word_Click);
            // 
            // cbxReportSel
            // 
            this.cbxReportSel.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxReportSel.FormattingEnabled = true;
            this.cbxReportSel.Location = new System.Drawing.Point(3, 4);
            this.cbxReportSel.Name = "cbxReportSel";
            this.cbxReportSel.Size = new System.Drawing.Size(226, 20);
            this.cbxReportSel.TabIndex = 4;
            this.cbxReportSel.SelectedIndexChanged += new System.EventHandler(this.cbxReportSel_SelectedIndexChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(242, 9);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 5;
            this.label2.Text = "方式：";
            // 
            // cbxShowType
            // 
            this.cbxShowType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxShowType.FormattingEnabled = true;
            this.cbxShowType.Location = new System.Drawing.Point(279, 4);
            this.cbxShowType.Name = "cbxShowType";
            this.cbxShowType.Size = new System.Drawing.Size(110, 20);
            this.cbxShowType.TabIndex = 4;
            this.cbxShowType.SelectedIndexChanged += new System.EventHandler(this.cbxShowType_SelectedIndexChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(394, 9);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(41, 12);
            this.label3.TabIndex = 5;
            this.label3.Text = "内容：";
            // 
            // panel2
            // 
            this.panel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel2.Controls.Add(this.cbxContentType);
            this.panel2.Controls.Add(this.checkedCbxMonth);
            this.panel2.Controls.Add(this.labelMonth);
            this.panel2.Controls.Add(this.btnLevel0);
            this.panel2.Controls.Add(this.btnColor);
            this.panel2.Controls.Add(this.cbxReportSel);
            this.panel2.Controls.Add(this.cbxShowType);
            this.panel2.Controls.Add(this.label2);
            this.panel2.Controls.Add(this.label3);
            this.panel2.Location = new System.Drawing.Point(11, 36);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(971, 27);
            this.panel2.TabIndex = 6;
            // 
            // cbxContentType
            // 
            this.cbxContentType.Location = new System.Drawing.Point(427, 3);
            this.cbxContentType.Name = "cbxContentType";
            this.cbxContentType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxContentType.Properties.Closed += new DevExpress.XtraEditors.Controls.ClosedEventHandler(this.checkedComboBoxEdit1_Properties_Closed);
            this.cbxContentType.Size = new System.Drawing.Size(132, 21);
            this.cbxContentType.TabIndex = 10;
            this.cbxContentType.EditValueChanged += new System.EventHandler(this.cbxContentType_EditValueChanged);
            this.cbxContentType.MouseDown += new System.Windows.Forms.MouseEventHandler(this.cbxContentType_MouseDown);
            // 
            // checkedCbxMonth
            // 
            this.checkedCbxMonth.Location = new System.Drawing.Point(604, 2);
            this.checkedCbxMonth.Name = "checkedCbxMonth";
            this.checkedCbxMonth.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.checkedCbxMonth.Properties.Closed += new DevExpress.XtraEditors.Controls.ClosedEventHandler(this.checkedComboBoxEdit1_Properties_Closed);
            this.checkedCbxMonth.Size = new System.Drawing.Size(211, 21);
            this.checkedCbxMonth.TabIndex = 9;
            this.checkedCbxMonth.Visible = false;
            this.checkedCbxMonth.EditValueChanged += new System.EventHandler(this.checkedCbxMonth_EditValueChanged);
            this.checkedCbxMonth.MouseDown += new System.Windows.Forms.MouseEventHandler(this.checkedCbxMonth_MouseDown);
            // 
            // labelMonth
            // 
            this.labelMonth.AutoSize = true;
            this.labelMonth.Location = new System.Drawing.Point(565, 7);
            this.labelMonth.Name = "labelMonth";
            this.labelMonth.Size = new System.Drawing.Size(41, 12);
            this.labelMonth.TabIndex = 8;
            this.labelMonth.Text = "月份：";
            this.labelMonth.Visible = false;
            // 
            // btnLevel0
            // 
            this.btnLevel0.Anchor = System.Windows.Forms.AnchorStyles.Right;
            this.btnLevel0.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnLevel0.Location = new System.Drawing.Point(910, 4);
            this.btnLevel0.Name = "btnLevel0";
            this.btnLevel0.Size = new System.Drawing.Size(57, 19);
            this.btnLevel0.TabIndex = 0;
            this.btnLevel0.Text = "全省<<";
            this.btnLevel0.UseVisualStyleBackColor = true;
            this.btnLevel0.Click += new System.EventHandler(this.btnLevel0_Click);
            // 
            // btnColor
            // 
            this.btnColor.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnColor.Location = new System.Drawing.Point(823, 2);
            this.btnColor.Name = "btnColor";
            this.btnColor.Size = new System.Drawing.Size(83, 23);
            this.btnColor.TabIndex = 7;
            this.btnColor.Text = "颜色设置";
            this.btnColor.UseVisualStyleBackColor = true;
            this.btnColor.Visible = false;
            this.btnColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // splitMain
            // 
            this.splitMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.splitMain.Location = new System.Drawing.Point(11, 69);
            this.splitMain.Name = "splitMain";
            // 
            // splitMain.Panel1
            // 
            this.splitMain.Panel1.Controls.Add(this.dataGridView);
            // 
            // splitMain.Panel2
            // 
            this.splitMain.Panel2.Controls.Add(this.chartControl);
            this.splitMain.Panel2.Controls.Add(this.panel3);
            this.splitMain.Size = new System.Drawing.Size(971, 370);
            this.splitMain.SplitterDistance = 542;
            this.splitMain.TabIndex = 7;
            // 
            // chartControl
            // 
            this.chartControl.AppearanceName = "Northern Lights";
            xyDiagram1.AxisX.AutoScaleBreaks.Enabled = true;
            xyDiagram1.AxisX.AutoScaleBreaks.MaxCount = 2;
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            xyDiagram1.EnableAxisXScrolling = true;
            xyDiagram1.EnableAxisXZooming = true;
            this.chartControl.Diagram = xyDiagram1;
            this.chartControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl.Location = new System.Drawing.Point(0, 0);
            this.chartControl.Name = "chartControl";
            this.chartControl.PaletteName = "Nature Colors";
            this.chartControl.RuntimeSelection = true;
            sideBySideBarSeriesLabel1.LineVisible = true;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 1";
            pointOptions1.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Number;
            series1.PointOptions = pointOptions1;
            sideBySideBarSeriesLabel2.LineVisible = true;
            sideBySideBarSeriesLabel2.Visible = false;
            series2.Label = sideBySideBarSeriesLabel2;
            series2.Name = "Series 2";
            this.chartControl.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1,
        series2};
            sideBySideBarSeriesLabel3.LineVisible = true;
            this.chartControl.SeriesTemplate.Label = sideBySideBarSeriesLabel3;
            this.chartControl.Size = new System.Drawing.Size(425, 360);
            this.chartControl.TabIndex = 0;
            this.chartControl.MouseEnter += new System.EventHandler(this.chartControl_MouseEnter);
            this.chartControl.MouseLeave += new System.EventHandler(this.chartControl_MouseLeave);
            // 
            // panel3
            // 
            this.panel3.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel3.Location = new System.Drawing.Point(0, 360);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(425, 10);
            this.panel3.TabIndex = 1;
            // 
            // KPIInfoPanel_ng
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.Transparent;
            this.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.Controls.Add(this.splitMain);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Name = "KPIInfoPanel_ng";
            this.Size = new System.Drawing.Size(996, 453);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.cxtMsKPI_ng.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxContentType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkedCbxMonth.Properties)).EndInit();
            this.splitMain.Panel1.ResumeLayout(false);
            this.splitMain.Panel2.ResumeLayout(false);
            this.splitMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.DataGridView dataGridView;
        private System.Windows.Forms.ComboBox cbxReportSel;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cbxShowType;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.SplitContainer splitMain;
        private System.Windows.Forms.Button btnColor;
        private DevExpress.XtraCharts.ChartControl chartControl;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Button btnLevel0;
        private System.Windows.Forms.ContextMenuStrip cxtMsKPI_ng;
        private System.Windows.Forms.ToolStripMenuItem miExp2Word;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Button exp2Xls;
        private DevExpress.XtraEditors.CheckedComboBoxEdit checkedCbxMonth;
        private System.Windows.Forms.Label labelMonth;
        private DevExpress.XtraEditors.CheckedComboBoxEdit cbxContentType;
    }
}
