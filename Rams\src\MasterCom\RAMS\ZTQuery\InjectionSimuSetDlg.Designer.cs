﻿namespace MasterCom.RAMS.ZTQuery
{
    partial class InjectionSimuSetDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(InjectionSimuSetDlg));
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.cbxStreetInjColumn = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.edtStreetInjMap = new DevExpress.XtraEditors.ButtonEdit();
            this.edtTestMap = new DevExpress.XtraEditors.ButtonEdit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxStreetInjColumn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtStreetInjMap.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtTestMap.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(330, 150);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 5;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(421, 150);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(17, 95);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(84, 14);
            this.labelControl1.TabIndex = 8;
            this.labelControl1.Text = "测试路径文件：";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(17, 25);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(84, 14);
            this.labelControl2.TabIndex = 11;
            this.labelControl2.Text = "渗透图层文件：";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(17, 52);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(84, 14);
            this.labelControl3.TabIndex = 12;
            this.labelControl3.Text = "道路名称列名：";
            // 
            // cbxStreetInjColumn
            // 
            this.cbxStreetInjColumn.Location = new System.Drawing.Point(107, 49);
            this.cbxStreetInjColumn.Name = "cbxStreetInjColumn";
            this.cbxStreetInjColumn.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxStreetInjColumn.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxStreetInjColumn.Size = new System.Drawing.Size(117, 21);
            this.cbxStreetInjColumn.TabIndex = 13;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl4.Appearance.Options.UseForeColor = true;
            this.labelControl4.Location = new System.Drawing.Point(17, 154);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(228, 14);
            this.labelControl4.TabIndex = 14;
            this.labelControl4.Text = "注：渗透图层不设置，使用默认渗透图层。";
            // 
            // edtStreetInjMap
            // 
            this.edtStreetInjMap.Location = new System.Drawing.Point(107, 22);
            this.edtStreetInjMap.Name = "edtStreetInjMap";
            this.edtStreetInjMap.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtStreetInjMap.Size = new System.Drawing.Size(401, 21);
            this.edtStreetInjMap.TabIndex = 15;
            this.edtStreetInjMap.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.edtStreetInjMap_ButtonClick);
            // 
            // edtTestMap
            // 
            this.edtTestMap.Location = new System.Drawing.Point(107, 92);
            this.edtTestMap.Name = "edtTestMap";
            this.edtTestMap.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtTestMap.Size = new System.Drawing.Size(401, 21);
            this.edtTestMap.TabIndex = 16;
            this.edtTestMap.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.edtTestMap_ButtonClick);
            // 
            // InjectionSimuSetDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("InjectionSimuSetDlg.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(528, 194);
            this.Controls.Add(this.edtTestMap);
            this.Controls.Add(this.edtStreetInjMap);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.cbxStreetInjColumn);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "InjectionSimuSetDlg";
            this.Text = "测试路径渗透率模拟运算";
            ((System.ComponentModel.ISupportInitialize)(this.cbxStreetInjColumn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtStreetInjMap.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtTestMap.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.ComboBoxEdit cbxStreetInjColumn;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.ButtonEdit edtStreetInjMap;
        private DevExpress.XtraEditors.ButtonEdit edtTestMap;
    }
}