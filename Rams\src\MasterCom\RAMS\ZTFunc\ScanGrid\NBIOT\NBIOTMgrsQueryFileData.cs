﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsQueryFileData : DIYSQLBase
    {
        /// <summary>
        /// 查询该文件在所选区域范围内的栅格合集
        /// </summary>
        /// <param name="mainModel"></param>
        public NbIotMgrsQueryFileData(MainModel mainModel)
            : base(mainModel)
        {
        }

        /// <summary>
        /// 地图框选的区域
        /// </summary>
        protected MTPolygon selectRect;

        protected FileInfo curFile;

        protected List<ScanGridInfo> scanGridInfoList = new List<ScanGridInfo>();

        #region 基础数据重写
        public override string Name
        {
            get { return "NBIOT扫频栅格数据查询"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        #endregion

        public void SetCondition(MTPolygon selectRect)
        {
            this.selectRect = selectRect;
        }

        #region 查询流程
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("正在查询栅格数据文件...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }

        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                foreach (var file in mainModel.FileInfos)
                {
                    curFile = file;
                    scanGridInfoList = new List<ScanGridInfo>();
                    Package package = clientProxy.Package;
                    string strsql = getSqlTextString();
                    E_VType[] retArrDef = getSqlRetTypeArr();//获得枚举类型数组
                    package.Command = Command.DIYSearch;//枚举类型：DIY接口
                    package.SubCommand = SubCommand.Request;//枚举类型：请求
                    if (MainDB)
                    {
                        package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                    }
                    else
                    {
                        package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                    }
                    package.Content.PrepareAddParam();
                    package.Content.AddParam(strsql);
                    StringBuilder sb = new StringBuilder();
                    if (retArrDef != null)
                    {
                        for (int i = 0; i < retArrDef.Length; i++)
                        {
                            sb.Append((int)retArrDef[i]);
                            sb.Append(",");
                        }
                    }
                    package.Content.AddParam(sb.ToString().TrimEnd(','));
                    clientProxy.Send();
                    receiveRetData(clientProxy);

                    if (curFile.CarrierType == 1)
                    {
                        NbIotMgrsQueryFuncBase.CMScanGridInfoList.AddRange(scanGridInfoList);
                    }
                    else if (curFile.CarrierType == 2)
                    {
                        NbIotMgrsQueryFuncBase.CUScanGridInfoList.AddRange(scanGridInfoList);
                    }
                    else if (curFile.CarrierType == 3)
                    {
                        NbIotMgrsQueryFuncBase.CTScanGridInfoList.AddRange(scanGridInfoList);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder strbSql = new StringBuilder();
            string tableName = NbIotMgrsQueryFuncBase.ModelTableprefix + curFile.StrWeek;

            strbSql.AppendFormat(@"SELECT ifileid,bms,itime,wtimems,strMGRTIndex,itllongitude,itllatitude,
ibrlongitude,ibrlatitude,iGridSize,EARFCN,PCI,SSS_RSSI,SSS_RP,R0_RP,R0_RQ,R0_CINR,iSampleCount
from {0} where ifileid = {1}", tableName, curFile.ID);

            return strbSql.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[18];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Byte;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Short;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_Int;
            rType[11] = E_VType.E_Int;
            rType[12] = E_VType.E_IntFloat;
            rType[13] = E_VType.E_IntFloat;
            rType[14] = E_VType.E_IntFloat;
            rType[15] = E_VType.E_IntFloat;
            rType[16] = E_VType.E_IntFloat;
            rType[17] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ScanGridInfo info = ScanGridInfo.Fill(package.Content);
                    if (isValidGrid(info))
                    {
                        scanGridInfoList.Add(info);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                setProgressPercent(ref index, ref progress);
            }
        }

        protected bool isValidGrid(ScanGridInfo grid)
        {
            //过滤不在框选区域内的栅格
            if (selectRect.CheckPointInRegion(grid.CentLng, grid.CentLat))
            {
                return true;
            }
            return false;
        }
        #endregion
    }

}
