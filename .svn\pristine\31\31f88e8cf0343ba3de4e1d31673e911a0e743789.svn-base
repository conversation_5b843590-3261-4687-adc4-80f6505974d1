﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYNoSignalCell_LTEScan : DIYSampleByRegion
    {
        
        private readonly Dictionary<ICell, NoSignalCell> noSignalCellDic = new Dictionary<ICell, NoSignalCell>();
        public ZTDIYNoSignalCell_LTEScan(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }
        
        public override string Name
        {
            get { return "无信号小区核查"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23010, this.Name);//待修改
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";//GSM的GSCAN_RxLev
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";//GSM的GSCAN_BCCH
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";//GSM的GSCAN_BSIC
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", ">LTE_SCAN");
            tmpDic.Add("themeName", "TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool getConditionBeforeQuery()//在查询之前获取查询条件
        {
            noSignalCellDic.Clear();
            return ResetBTSToMop2Dict();
        }

        protected Dictionary<LTEBTS, MapOperation2> ltebtsToMop2 = new Dictionary<LTEBTS, MapOperation2>();
        protected  bool ResetBTSToMop2Dict()
        {
            ltebtsToMop2.Clear();
            LTEBTSVoiCoverManager voiManager = LTEBTSVoiCoverManager.GetInstance();
            if (!AppendBTSToMop2Dict(voiManager))
            {
                return false;
            }
            voiManager.Clear();
            return true;
        }

        private bool AppendBTSToMop2Dict(LTEBTSVoiCoverManager voiManager)//多边形矩形判断
        {
            voiManager.Condition = new VoiCoverCondition();
            switch (voiManager.Construct(false, true))
            {
                case VoiCoverResult.Cancel:
                    return false;
                case VoiCoverResult.Failed:
                    System.Windows.Forms.MessageBox.Show(voiManager.LastErrorText);
                    return false;
                default:
                    break;
            }

            Dictionary<LTEBTS, List<Vertex[]>> ltebtsToPoly = voiManager.LTEBtsToPolygonDict;
            foreach (LTEBTS ltebts in ltebtsToPoly.Keys)
            {
                List<PointF[]> pfList = new List<PointF[]>();
                foreach (Vertex[] vs in ltebtsToPoly[ltebts])
                {
                    PointF[] pf = new PointF[vs.Length];
                    for (int i = 0; i < vs.Length; ++i)
                    {
                        pf[i] = vs[i].ToPointF();
                    }
                    pfList.Add(pf);
                }
                MapOperation2 mop2 = new MapOperation2();
                mop2.FillPolygon(pfList);
                ltebtsToMop2.Add(ltebts, mop2);
            }
            return true;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            // 扫频点的理论覆盖基站
            LTEBTS curBts = null;
            foreach (LTEBTS ltebts in ltebtsToMop2.Keys)
            {
                if (!ltebtsToMop2[ltebts].CheckPointInRegion(tp.Longitude, tp.Latitude))
                {
                    continue;
                }
                curBts = ltebts;
                break;
            }
            if (curBts == null)
            {
                return;
            }

            // 扫频点的理论覆盖小区
            LTECell curCell = null;
            foreach (LTECell ltecell in curBts.Cells)
            {
                if (!MathFuncs.JudgePoint(ltecell.Longitude, ltecell.Latitude, tp.Longitude, tp.Latitude, (int)ltecell.Direction))
                {
                    continue;
                }
                curCell = ltecell;
                break;
            }
            if (curCell == null)
            {
                return;
            }

            // 扫频点是否扫出理论覆盖小区

            bool isCover = judgeCover(tp, curCell);

            if (isCover)
            {
                return;
            }
            if (!noSignalCellDic.ContainsKey(curCell))
            {
                NoSignalCell noSignalCell = new NoSignalCell(curCell);
                noSignalCell.XH = noSignalCellDic.Count + 1;
                noSignalCellDic[curCell] = noSignalCell;
            }
            noSignalCellDic[curCell].TestPointList.Add(tp);

        }

        private static bool judgeCover(TestPoint tp, LTECell curCell)
        {
            bool isCover = false;
            for (int i = 0; i < 50; ++i)
            {
                int? bcch = (int?)tp["LTESCAN_TopN_EARFCN", i];
                short? bsic = (short?)tp["LTESCAN_TopN_PCI", i];
                if (bcch == null && bsic == null)
                {
                    break;
                }
                if (bcch == curCell.EARFCN && bsic == curCell.PCI)
                {
                    isCover = true;
                    break;
                }
            }

            return isCover;
        }

        protected override void FireShowFormAfterQuery()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(NoSignalCellForm).FullName);
            NoSignalCellForm noSignalCellForm = obj == null ? null : obj as NoSignalCellForm;
            if (noSignalCellForm == null || noSignalCellForm.IsDisposed)
            {
                noSignalCellForm = new NoSignalCellForm(MainModel);
            }
            noSignalCellForm.FillData(noSignalCellDic);//noSignalCellDic
            if (!noSignalCellForm.Visible)
            {
                noSignalCellForm.Show(MainModel.MainForm);
            } 
        }
    }
}
