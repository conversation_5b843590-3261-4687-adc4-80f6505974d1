﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

using MasterCom.MControls;

namespace MasterCom.RAMS.Func
{
    public class ScanGridAnaRxlevStater
    {
        private ScanGridAnaResult anaResult { get; set; }
        private ScanGridAnaResult cmpResult { get; set; }
        private readonly ScanGridAnaRxLevSingleStater anaStater;
        private readonly ScanGridAnaRxLevSingleStater cmpStater;
        private List<ColorRange> colorRanges { get; set; }

        public ScanGridAnaRxlevStater(ScanGridAnaResult anaResult, ScanGridAnaResult cmpResult, List<ColorRange> colorRanges)
        {
            this.anaResult = anaResult;
            this.cmpResult = cmpResult;
            this.colorRanges = colorRanges;

            anaStater = cmpStater = null;
            anaStater = new ScanGridAnaRxLevSingleStater(anaResult, colorRanges);
            if (cmpResult != null)
            {
                cmpStater = new ScanGridAnaRxLevSingleStater(cmpResult, colorRanges);
            }
        }

        public void Stat()
        {
            anaStater.Stat();
            if (cmpStater != null)
            {
                cmpStater.Stat();
            }
        }

        public List<DataTable> GetResult(ScanGridAnaGridType netType)
        {
            List<DataTable> retList = anaStater.GetResult(netType);
            if (cmpStater != null)
            {
                retList.AddRange(cmpStater.GetResult(netType));
            }
            return retList;
        }
    }

    public class ScanGridAnaRxLevSingleStater
    {
        private readonly ScanGridAnaResult result;
        private readonly List<ColorRange> colorRanges;
        private readonly Dictionary<string, int[]>[] netRegionCounter;
        private readonly Dictionary<string, double>[] netRegionAvgRxlev;
        private readonly DataTable[,] dts;

        public ScanGridAnaRxLevSingleStater(ScanGridAnaResult result, List<ColorRange> colorRanges)
        {
            this.result = result;
            this.colorRanges = colorRanges;

            netRegionAvgRxlev = new Dictionary<string, double>[4];
            netRegionCounter = new Dictionary<string, int[]>[4];
            for (int i = 0; i < 4; ++i)
            {
                netRegionCounter[i] = new Dictionary<string, int[]>();
                netRegionAvgRxlev[i] = new Dictionary<string, double>();
            }

            dts = new DataTable[4, 4];
            for (int i = 0; i < 4; ++i)
            {
                for (int j = 0; j < 4; ++j)
                {
                    dts[i, j] = new DataTable();
                }
            }
        }

        public void Stat()
        {
            int cnt = colorRanges.Count;
            foreach (ScanGridAnaRegionInfo region in result.RegionList)
            {
                for (int i = 0; i < 4; ++i)
                {
                    if (!netRegionCounter[i].ContainsKey(region.RegionName))
                    {
                        netRegionCounter[i].Add(region.RegionName, new int[cnt]);
                    }
                    if (!netRegionAvgRxlev[i].ContainsKey(region.RegionName))
                    {
                        netRegionAvgRxlev[i].Add(region.RegionName, 0);
                    }
                }

                foreach (ScanGridAnaGridInfo grid in region.GridList)
                {
                    addValidGridInfo(cnt, region, grid);
                }
            }

            PrepareDataTable();
        }

        private void addValidGridInfo(int cnt, ScanGridAnaRegionInfo region, ScanGridAnaGridInfo grid)
        {
            if (grid.IsValidGrid)
            {
                int netType = (int)grid.GridType;
                for (int i = 0; i < cnt; ++i)
                {
                    ColorRange range = colorRanges[i];
                    if (grid.MaxRxlev >= range.minValue && grid.MaxRxlev < range.maxValue)
                    {
                        ++netRegionCounter[netType][region.RegionName][i];
                        break;
                    }
                }

                netRegionAvgRxlev[netType][region.RegionName] += grid.MaxRxlev;
            }
        }

        public List<DataTable> GetResult(ScanGridAnaGridType netType)
        {
            List<DataTable> retList = new List<DataTable>();
            for (int i = 0; i < 4; ++i)
            {
                retList.Add(dts[(int)netType, i]);
            }
            return retList;
        }

        private void PrepareDataTable()
        {
            for (int i = 0; i < 4; ++i)
            {
                PrepareDataTableCount(i);
                PrepareDataTableRate(i);
            }
        }

        private void PrepareDataTableRate(int netType)
        {
            PrepareColumnForGridViewRate(dts[netType, 2]);
            PrepareColumnForChartControlRate(dts[netType, 3]);
            DataTable dtGridCount = dts[netType, 0];

            int sumOut = 0;
            for (int i = 0; i < dtGridCount.Rows.Count; ++i)
            {
                if (dtGridCount.Rows[i][0].ToString() == "网格外")
                {
                    sumOut = (int)dtGridCount.Rows[i][dtGridCount.Columns.Count - 2];
                    break;
                }
            }

            for (int i = 0; i < dtGridCount.Rows.Count; ++i)
            {
                int rowSize = dtGridCount.Columns.Count - 1;
                int rowSum = (int)dtGridCount.Rows[i][rowSize - 1];
                int tbSum = (int)dtGridCount.Rows[dtGridCount.Rows.Count - 1][rowSize - 1] + sumOut;

                List<object> row = new List<object>();
                row.Add(dtGridCount.Rows[i][0]);
                for (int j = 1; j < rowSize - 1; ++j)
                {
                    row.Add(rowSum == 0 ? 0 : (int)dtGridCount.Rows[i][j] * 1.0 / rowSum);
                }

                dts[netType, 3].Rows.Add(row.ToArray());
                row.Add(rowSum == 0 ? 0 : rowSum * 1.0 / tbSum);
                dts[netType, 2].Rows.Add(row.ToArray());
            }
        }

        private void PrepareDataTableCount(int netType)
        {
            PrepareColumnForGridViewCount(dts[netType, 0]);
            PrepareColumnForChartControlCount(dts[netType, 1]);

            Dictionary<string, int[]> regionCounterDic = netRegionCounter[netType];
            Dictionary<string, double> regionRxlevDic = netRegionAvgRxlev[netType];
            foreach (string regionName in regionCounterDic.Keys)
            {
                List<object> objs = new List<object>();
                objs.Add(regionName);

                int cnt = 0;
                double avgRxlev = 0;
                foreach (int s in regionCounterDic[regionName])
                {
                    cnt += s;
                    objs.Add(s);
                }
                avgRxlev = cnt == 0 ? 0 : regionRxlevDic[regionName] / cnt;

                List<object> gridObjs = new List<object>(objs);
                gridObjs.Add(cnt);
                gridObjs.Add(Math.Round(avgRxlev, 2));
                dts[netType, 0].Rows.Add(gridObjs.ToArray());
                dts[netType, 1].Rows.Add(objs.ToArray());
            }

            DataTable dt = dts[netType, 0];
            object[] row = getRowInfo(dt);
            dt.Rows.Add(row);

            dts[netType, 1] = dts[netType, 0].Copy();
            dts[netType, 1].Columns.Remove("合计");
            dts[netType, 1].Columns.Remove("平均电平");
        }

        private static object[] getRowInfo(DataTable dt)
        {
            object[] row = new object[dt.Columns.Count];
            row[0] = "汇总(网格内)";
            for (int i = 1; i < dt.Columns.Count; ++i)
            {
                double sum = 0;
                for (int j = 0; j < dt.Rows.Count; ++j)
                {
                    if (dt.Rows[j][0].ToString() == "网格外")
                    {
                        continue;
                    }
                    sum += double.Parse(dt.Rows[j][i].ToString());
                }

                if (i == dt.Columns.Count - 1)
                {
                    row[i] = dt.Rows.Count - 1 == 0 ? 0d : Math.Round(sum / (dt.Rows.Count - 1), 2);
                }
                else
                {
                    row[i] = (int)sum;
                }
            }

            return row;
        }

        private void PrepareColumnForGridViewCount(DataTable dt)
        {
            dt.Columns.Add("网格ID", typeof(string));
            foreach (ColorRange range in colorRanges)
            {
                dt.Columns.Add(string.Format("[{0},{1})", range.minValue, range.maxValue), typeof(int));
            }
            dt.Columns.Add("合计", typeof(int));
            dt.Columns.Add("平均电平", typeof(float));
        }

        private void PrepareColumnForGridViewRate(DataTable dt)
        {
            dt.Columns.Add("网格ID", typeof(string));
            foreach (ColorRange range in colorRanges)
            {
                dt.Columns.Add(string.Format("[{0},{1})", range.minValue, range.maxValue), typeof(double));
            }
            dt.Columns.Add("合计", typeof(double));
        }

        private void PrepareColumnForChartControlCount(DataTable dt)
        {
            dt.Columns.Add("网格ID", typeof(string));
            foreach (ColorRange range in colorRanges)
            {
                dt.Columns.Add(string.Format("[{0},{1})", range.minValue, range.maxValue), typeof(int));
            }
        }

        private void PrepareColumnForChartControlRate(DataTable dt)
        {
            dt.Columns.Add("网格ID", typeof(string));
            foreach (ColorRange range in colorRanges)
            {
                dt.Columns.Add(string.Format("[{0},{1})", range.minValue, range.maxValue), typeof(double));
            }
        }
    }
}
