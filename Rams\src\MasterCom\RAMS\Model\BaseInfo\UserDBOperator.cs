﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class UserDBOperator : DIYSQLBase
    {
        private readonly List<User> users2Update;
        private readonly bool removeOnly = false;
        public UserDBOperator(List<User> roles2Update,bool removeOnly)
            : base(MainModel.GetInstance())
        {
            this.users2Update = roles2Update;
            this.removeOnly = removeOnly;
            MainDB = true;
        }

        //sql语句可能过长，需分包处理
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }

                E_VType[] retArrDef = getSqlRetTypeArr();
                string strsql = getSqlTextString();
                string[] strArr = strsql.Split(';');
                int curIdx = 0;
                while (curIdx < strArr.Length)
                {
                    curIdx = addParam(package, retArrDef, strArr, curIdx);
                    clientProxy.Send();
                    System.Threading.Thread.Sleep(1000);
                }
                receiveRetData(clientProxy);
            }
            catch
            {
                //continue
            }
        }

        private int addParam(Package package, E_VType[] retArrDef, string[] strArr, int curIdx)
        {
            StringBuilder txt = new StringBuilder();
            for (; curIdx < strArr.Length; curIdx++)
            {
                txt.Append(strArr[curIdx] + ";");
                if (txt.Length > 6000)
                {
                    break;
                }
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(txt.ToString());
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    if (i < retArrDef.Length - 1)
                    {
                        sb.Append(",");
                    }
                }
            }
            package.Content.AddParam(sb.ToString());
            return curIdx;
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            foreach (User usr in users2Update)
            {
#if LoginManage
                string updateCondition = "";
                if (usr.UserStatus != 4)
                {
                    updateCondition += ",iTryTimes=0 ";
                }
                if (usr.UserStatus == 4 || usr.UserStatus == 6 || usr.UserStatus == 7)
                {
                    updateCondition += ",lastlocked_time=getdate() ";
                }
                string insertCondition = "null";
                if (usr.UserStatus == 4 || usr.UserStatus == 6 || usr.UserStatus == 7)
                {
                    insertCondition = "getdate() ";
                }
#endif
                if (removeOnly)
                {
                    sb.Append(string.Format("delete from tb_cfg_static_user where iid={0};", usr.ID));
                    sb.Append(string.Format("delete from tb_cfg_static_user_role where user_id={0};", usr.ID));
                    sb.Append(string.Format("delete from tb_cfg_static_user_dataSourceRole where user_id={0};", usr.ID));
#if LoginManage
                    sb.Append(string.Format("delete from tb_cfg_static_user_changePwd_record where user_id={0};", usr.ID));
#endif
                }
                else
                {//新增 or 修改
                    string str = string.Empty;
                    if (string.IsNullOrEmpty(usr.NewPassword))
                    {//新密码为空，只能对已有账号做修改，update即可
#if LoginManage
                        str = @"update tb_cfg_static_user set strname='{0}',logon_code='{1}',phone='{2}',strcomment='{3}',icityid={4},
iuserstate = {5} " + updateCondition + "where iid={6};";
                        str = string.Format(str, usr.Name, usr.LoginName, usr.Phone, usr.Description, usr.CityID, usr.UserStatus, usr.ID);
#else
                        str = @"update tb_cfg_static_user set strname='{0}',logon_code='{1}',phone='{2}',strcomment='{3}',icityid={4} where iid={5};";
                        str = string.Format(str, usr.Name, usr.LoginName, usr.Phone, usr.Description, usr.CityID, usr.ID);
#endif
                    }
                    else
                    {//新密码不为空，有可能是新增账号，也有可能是对已有账号修改了密码。所以需判断是update 还是insert
                        string md5Pw = ClientProxy.getMD5String(Encoding.Default.GetBytes(usr.NewPassword));
                        str = @"if not exists (SELECT top(1) * FROM tb_cfg_static_user where iid={0})
begin
{1}
end
else
begin
{2}
end;";
                        string insertTxt = string.Empty;
                        string updateTxt = string.Empty;
#if LoginManage
                        insertTxt = @"insert INTO tb_cfg_static_user (iid,strname,logon_code,logon_pwd,phone,strcomment,icityid,iuserstate,lastlocked_time) 
values ({0},'{1}','{2}','{3}','{4}','{5}',{6},{7}," + insertCondition + ");";
                        insertTxt = string.Format(insertTxt, usr.ID, usr.Name, usr.LoginName, md5Pw, usr.Phone, usr.Description, usr.CityID, usr.UserStatus);
                        updateTxt = @"update tb_cfg_static_user set strname='{0}',logon_code='{1}',logon_pwd='{2}',phone='{3}',strcomment='{4}',
icityid={5},iuserstate = {6}" + updateCondition + " where iid={7}";
                        updateTxt = string.Format(updateTxt, usr.Name, usr.LoginName, md5Pw, usr.Phone, usr.Description, usr.CityID, usr.UserStatus, usr.ID);
#else
                        insertTxt = @"insert INTO tb_cfg_static_user (iid,strname,logon_code,logon_pwd,phone,strcomment,icityid) values ({0},'{1}','{2}','{3}','{4}','{5}',{6});";
                        insertTxt = string.Format(insertTxt, usr.ID, usr.Name, usr.LoginName, md5Pw, usr.Phone, usr.Description, usr.CityID);
                        updateTxt = @"update tb_cfg_static_user set strname='{0}',logon_code='{1}',logon_pwd='{2}',phone='{3}',strcomment='{4}',icityid={5} where iid={6}";
                        updateTxt = string.Format(updateTxt, usr.Name, usr.LoginName, md5Pw, usr.Phone, usr.Description, usr.CityID, usr.ID);
#endif
                        str = string.Format(str, usr.ID, insertTxt, updateTxt);
                    }

                    sb.Append(str);
                }
            }
            return sb.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        public override string Name
        {
            get { return "更新用户表（适用新增，修改，删除）"; }
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
        }
    }
}
