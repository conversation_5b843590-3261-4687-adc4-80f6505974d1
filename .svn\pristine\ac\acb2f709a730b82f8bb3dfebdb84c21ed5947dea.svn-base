﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.NOP
{
    class DIYQueryFileInfoForUnDoneGroupStat : DIYQueryFileInfoBase
    {
        public DIYQueryFileInfoForUnDoneGroupStat(MainModel mainModel)
            : base(mainModel)
        {
        }


        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        private static DIYQueryFileInfoForUnDoneGroupStat instance = null;

        public static DIYQueryFileInfoForUnDoneGroupStat GetInstance()
        {
            if (instance == null)
            {
                instance = new DIYQueryFileInfoForUnDoneGroupStat(MainModel.GetInstance());
            }
            return instance;
        }

        public override string Name
        {
            get { return "待处理工单分组别统计"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 29000, 29008, this.Name);
        }


        public override string IconName
        {
            get { return ""; }
        }

       

        private DIYSQLForUnDoneGroupStat GroupQuery = null;
        private UnDoneGroupStatForm resultForm = null;
        private bool shouldShowForm = false;
        private string strWhere = "";
        protected override void query()
        {
            strWhere = "";
            if (conditionDlg == null)
            {
                conditionDlg = new GroupStatSetDlg();
            }
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                conditionDlg.getCondition1(out strWhere);
            }
            else
            {
                return;
            }
            
            WaitBox.Show("开始查询工单...", queryInThread);

            if (shouldShowForm)
            {
                fireShowForm();
            }
            shouldShowForm = false;

        }

        private void fireShowForm()
        {
            if (GroupQuery.GroupModels == null)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            UnDoneGroupStatAnalyzer analyzer = new UnDoneGroupStatAnalyzer();
            analyzer.Analyze(GroupQuery.GroupModels);
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new UnDoneGroupStatForm();
            }
            resultForm.FillData(analyzer.Results,analyzer.TaskResults);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }

        GroupStatSetDlg conditionDlg = null;
        private void queryInThread()
        {
            try
            {
                GroupQuery = new DIYSQLForUnDoneGroupStat(MainModel, strWhere);
                GroupQuery.Query();
                shouldShowForm = true;
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }
    }

    class DIYSQLForUnDoneGroupStat : DIYSQLBase
    {
        public DIYSQLForUnDoneGroupStat(MainModel mainModel, string strWhere)
            : base(mainModel)
        {
            MainDB = false;
            this.strWhere = "where 工单当前状态 <> '已丢弃' and " + strWhere;
        }

        protected string strWhere;//查询条件
        protected override string getSqlTextString()
        {
            return "select _id,_name,用户组别,工单当前状态,工单月份,地市,区域名称,进度操作 "//进度操作
            + " from  MTNOH_AAA_Platform.dbo.task_9 b  " + strWhere + " order by _id";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[8];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            return rType;
        }

        private List<GroupStatModel> groupModels;
        public List<GroupStatModel> GroupModels
        {
            get { return groupModels; }
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            
            groupModels = new List<GroupStatModel>();

            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package)
        {
            GroupStatModel groupModel;
            int taskID = package.Content.GetParamInt();
            string taskName = package.Content.GetParamString();
            string groupName = package.Content.GetParamString();
            string description = package.Content.GetParamString();
            string ordertime = package.Content.GetParamString();
            string district = package.Content.GetParamString();
            string areaName = package.Content.GetParamString();
            string opera = package.Content.GetParamString();

            groupModel = new GroupStatModel();
            groupModel.AreaName = areaName;
            groupModel.Description = description;
            groupModel.District = district;
            groupModel.GroupName = groupName;
            groupModel.OrderTime = ordertime;
            groupModel.TaskID = taskID;
            groupModel.TaskName = taskName;
            if (opera != null && (opera.Contains("申诉审核") || opera.Contains("挂起处理")))
            {
                groupModel.Description = opera;
            }
            groupModels.Add(groupModel);
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }



    }
}
