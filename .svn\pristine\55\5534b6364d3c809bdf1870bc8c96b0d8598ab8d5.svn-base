﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSQLReportEventPreprocessQuery_BJ : DIYSQLBase
    {
        private readonly List<int> roleList = new List<int>();
        public ZTSQLReportEventPreprocessQuery_BJ(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return "select b.role_id from tb_cfg_static_user a, tb_cfg_static_user_role b where a.logon_code = '" + MainModel.User.LoginName + "' and a.iid = b.user_id";
        }

        public List<int> RoleList
        {
            get
            {
                return roleList;
            }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int index = 0;
            E_VType[] rType = new E_VType[1];
            rType[index] = E_VType.E_Int;

            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    this.roleList.Add(package.Content.GetParamInt());
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "ZTSQLReportEventPreprocessQuery_BJ"; }
        }
    }
}
