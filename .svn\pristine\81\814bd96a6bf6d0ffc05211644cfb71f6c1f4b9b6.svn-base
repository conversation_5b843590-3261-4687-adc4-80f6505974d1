﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.NOP
{
    public class QueryProcNodeCfg : DIYSQLBase
    {
        private readonly bool isVer2017;
        public QueryProcNodeCfg(bool isVer2017 = false)
            : base(MainModel.GetInstance())
        {
            this.isVer2017 = isVer2017;
        }

        protected override string getSqlTextString()
        {
            string svrName = NopCfgMngr.Instance.TaskDBServerName;

            return @"select [id],[name],[version],[xml],[mtime] from " + svrName
               + ".dbo.tb_cfg_proc_version left join " + svrName
               + ".dbo.tb_cfg_proc_xml on id=pid order by pid,seqid;";
            /*
            return @"select [id],[name],[version],[xml] from " + svrName + (isVer2017 ? "_2017" : "")
                + ".dbo.tb_cfg_proc_version left join " + svrName + (isVer2017 ? "_2017" : "")
                + ".dbo.tb_cfg_proc_xml on id=pid order by pid,seqid;";
                */
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr = new E_VType[5];
            int i = 0;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            ProcRoutine lastProc = null;
            StringBuilder lastXml = new StringBuilder();
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package, ref lastProc, ref lastXml);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
            if (lastProc != null && lastXml.Length > 0)
            {//保存最后一配置
                lastProc.LoadFrom(lastXml.ToString());
                if (isVer2017)
                {
                    ProcRoutineManager2017.Instance.AddProc(lastProc);
                }
                else
                {
                    ProcRoutineManager.Instance.AddProc(lastProc);
                }
            }
        }

        private void fillData(Package package, ref ProcRoutine lastProc, ref StringBuilder lastXml)
        {
            int id = package.Content.GetParamInt();
            string name = package.Content.GetParamString();
            int ver = package.Content.GetParamInt();
            string xml = package.Content.GetParamString();
            string dtStr = package.Content.GetParamString();
            ProcRoutine proc = new ProcRoutine();
            proc.ID = id;
            proc.Name = name;
            proc.Version = ver;
            proc.ModifyDateTime = dtStr;
            if (lastProc != null && lastProc.ID != proc.ID)
            {//由于xml可能过大，后台已做拆分处理。仅当前包为新的流程配置信息，保存上一流程配置
                lastProc.LoadFrom(lastXml.ToString());
                if (isVer2017)
                {
                    ProcRoutineManager2017.Instance.AddProc(lastProc);
                }
                else
                {
                    ProcRoutineManager.Instance.AddProc(lastProc);
                }
                lastXml = new StringBuilder();
            }
            lastXml.Append(xml);
            lastProc = proc;
        }
    }
}
