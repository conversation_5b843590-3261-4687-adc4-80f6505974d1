﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class SameEarfcnPciResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel1 = new System.Windows.Forms.Panel();
            this.btnFind = new System.Windows.Forms.Button();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.colCellName = new BrightIdeasSoftware.OLVColumn();
            this.colCellID = new BrightIdeasSoftware.OLVColumn();
            this.colEarfcn = new BrightIdeasSoftware.OLVColumn();
            this.colPci = new BrightIdeasSoftware.OLVColumn();
            this.colLongitude = new BrightIdeasSoftware.OLVColumn();
            this.colLatitude = new BrightIdeasSoftware.OLVColumn();
            this.colDistance = new BrightIdeasSoftware.OLVColumn();
            this.colAngle = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcelPair = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.btnFind);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(878, 46);
            this.panel1.TabIndex = 0;
            // 
            // btnFind
            // 
            this.btnFind.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnFind.Location = new System.Drawing.Point(791, 12);
            this.btnFind.Name = "btnFind";
            this.btnFind.Size = new System.Drawing.Size(75, 23);
            this.btnFind.TabIndex = 0;
            this.btnFind.Text = "查找";
            this.btnFind.UseVisualStyleBackColor = true;
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.colCellName);
            this.treeListView.AllColumns.Add(this.colCellID);
            this.treeListView.AllColumns.Add(this.colEarfcn);
            this.treeListView.AllColumns.Add(this.colPci);
            this.treeListView.AllColumns.Add(this.colLongitude);
            this.treeListView.AllColumns.Add(this.colLatitude);
            this.treeListView.AllColumns.Add(this.colDistance);
            this.treeListView.AllColumns.Add(this.colAngle);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colCellName,
            this.colCellID,
            this.colEarfcn,
            this.colPci,
            this.colLongitude,
            this.colLatitude,
            this.colDistance,
            this.colAngle});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip1;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(0, 46);
            this.treeListView.MultiSelect = false;
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(878, 457);
            this.treeListView.TabIndex = 1;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            // 
            // colCellName
            // 
            this.colCellName.HeaderFont = null;
            this.colCellName.Text = "小区名";
            // 
            // colCellID
            // 
            this.colCellID.HeaderFont = null;
            this.colCellID.Text = "小区ID";
            // 
            // colEarfcn
            // 
            this.colEarfcn.HeaderFont = null;
            this.colEarfcn.Text = "EARFCN";
            // 
            // colPci
            // 
            this.colPci.HeaderFont = null;
            this.colPci.Text = "PCI";
            // 
            // colLongitude
            // 
            this.colLongitude.HeaderFont = null;
            this.colLongitude.Text = "经度";
            // 
            // colLatitude
            // 
            this.colLatitude.HeaderFont = null;
            this.colLatitude.Text = "纬度";
            // 
            // colDistance
            // 
            this.colDistance.HeaderFont = null;
            this.colDistance.Text = "距离(米)";
            // 
            // colAngle
            // 
            this.colAngle.HeaderFont = null;
            this.colAngle.Text = "夹角";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.miExportExcelPair,
            this.miExpandAll,
            this.miCollapseAll});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(190, 114);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(189, 22);
            this.miExportExcel.Text = "导出Excel";
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(189, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(189, 22);
            this.miCollapseAll.Text = "全部折叠";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportExcelPair
            // 
            this.miExportExcelPair.Name = "miExportExcelPair";
            this.miExportExcelPair.Size = new System.Drawing.Size(189, 22);
            this.miExportExcelPair.Text = "导出Excel（小区对）";
            this.miExportExcelPair.Click += new System.EventHandler(this.miExportExcelPair_Click);
            // 
            // SameEarfcnPciResultForm
            // 
            this.AcceptButton = this.btnFind;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(878, 503);
            this.Controls.Add(this.treeListView);
            this.Controls.Add(this.panel1);
            this.Name = "SameEarfcnPciResultForm";
            this.Text = "LTE同频同PCI";
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Button btnFind;
        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn colCellName;
        private BrightIdeasSoftware.OLVColumn colEarfcn;
        private BrightIdeasSoftware.OLVColumn colPci;
        private BrightIdeasSoftware.OLVColumn colLongitude;
        private BrightIdeasSoftware.OLVColumn colLatitude;
        private BrightIdeasSoftware.OLVColumn colDistance;
        private BrightIdeasSoftware.OLVColumn colCellID;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private BrightIdeasSoftware.OLVColumn colAngle;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripMenuItem miExportExcelPair;
    }
}