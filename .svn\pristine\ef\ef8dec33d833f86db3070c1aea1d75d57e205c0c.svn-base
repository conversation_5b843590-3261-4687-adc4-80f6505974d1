﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTTestTaskResult : DIYQueryFileInfoBase
    {
        protected List<CQTTestTask> testTasks = null;
        public DIYQueryCQTTestTaskResult(MainModel mm ,List<CQTTestTask> testTasks)
            : base(mm)
        {
            this.testTasks = testTasks;
            IsShowFileInfoForm = false;
        }

        protected override void query()
        {
            makeQueryCondition();
            base.query();
        }

        protected void makeQueryCondition()
        {
            this.condition = new QueryCondition();
            List<int> districtIDs = MainModel.MainForm.GetQueryConditionDistrictIDs();
            if (districtIDs == null || districtIDs.Count == 0)
            {
                condition.DistrictIDs.Add(MainModel.DistrictID);
            }
            else
            {
                condition.DistrictIDs = districtIDs;
            }
            DateTime bTime = DateTime.MaxValue;
            DateTime eTime = DateTime.MinValue;
            foreach (CQTTestTask task in testTasks)
            {
                task.ClearTestInfo();
                if (task.BeginTime < bTime)
                {
                    bTime = task.BeginTime;
                }
                if (task.EndTime > eTime)
                {
                    eTime = task.EndTime;
                }
                if (!condition.CarrierTypes.Contains(task.CareerID))
                {
                    condition.CarrierTypes.Add(task.CareerID);
                }
                if (!condition.ServiceTypes.Contains(task.ServiceID))
                {
                    condition.ServiceTypes.Add(task.ServiceID);
                }
                if (!condition.AgentIds.Contains(task.AgentID))
                {
                    condition.AgentIds.Add(task.AgentID);
                }
            }
            condition.Periods.Add(new TimePeriod(bTime, eTime));
            CategoryEnumItem[] projItems = ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items;
            foreach (CategoryEnumItem item in projItems)
            {
                condition.Projects.Add(item.ID);
            }
        }

        protected override void doWithFileInfo(FileInfo fi)
        {
            string mainName = "";
            string subName = "";
            string floorName = "";
            CQTKPIDataManager.GetPointName(fi.Name, out mainName, out subName, out floorName);
            if (!string.IsNullOrEmpty(mainName))
            {
                foreach (CQTTestTask task in testTasks)
                {
                    if (task.CQTPointName==mainName)
                    {
                        task.AddTestFile(fi, subName);
                    }
                }
            }
        }

    }
}
