﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.ES.UI
{
    public partial class AddRoutineItemDlg : BaseFormStyle
    {
        public string InputName
        {
            get 
            {
                return tbxRoutineName.Text;
            }
        }
        public bool IsInProc
        {
            get
            {
                return cbxInProc.Checked;
            }
        }
        public AddRoutineItemDlg()
        {
            InitializeComponent();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }
    }
}