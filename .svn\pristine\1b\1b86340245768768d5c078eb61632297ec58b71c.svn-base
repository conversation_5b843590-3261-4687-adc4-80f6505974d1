﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class TestWorkSettingDlg : BaseDialog
    {
        private WorkTestCondition cond;

        public TestWorkSettingDlg()
        {
            InitializeComponent();

            listBoxTimeVec.HorizontalScrollbar = true;
            cond = new WorkTestCondition();
        }

        public void SetCondition(WorkTestCondition condition)
        {
            this.cond = condition;
            this.spinEditPermeate.Value = condition.Permeate;

            listBoxTimeVec.Items.Clear();

            if (condition.TimeVec.Count > 0)
            {
                foreach (TimePeriod period in condition.TimeVec)
                {
                    listBoxTimeVec.Items.Add(period);
                }
            }
            else
            {
                listBoxTimeVec.Items.Add(createTimePeriod());
            }
        }

        public WorkTestCondition GetCondition()
        {
            List<TimePeriod> times = new List<TimePeriod>();
            foreach (object item in listBoxTimeVec.Items)
            {
                times.Add(item as TimePeriod);
            }
            if (times.Count == 0)
            {
                times.Add(createTimePeriod());
            }

            cond.SetContext(times,
                (int)spinEditPermeate.Value, labelAchieve.BackColor, labelUnAchieve.BackColor);

            return cond;
        }

        private void simpleButtonAdd_Click(object sender, EventArgs e)
        {
            TimePeriod tp = createTimePeriod();
            ZTFileCompareDatetimeOptionDlg dlg = new ZTFileCompareDatetimeOptionDlg(tp.BeginTime, tp.EndTime);
            dlg.Text = "增加时间段";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                DateTime beginTime;
                DateTime endTime;
                dlg.GetSettingTime(out beginTime, out endTime);
                listBoxTimeVec.Items.Add(new TimePeriod(beginTime, endTime));
            }
        }

        private TimePeriod createTimePeriod()
        {
            MasterCom.RAMS.Model.QueryCondition curCond = ArchiveSettingManager.GetInstance().Condition.BaseCondition;
            if (curCond.Periods.Count > 0)
            {
                return curCond.Periods[0];
            }
            DateTime dtNow = DateTime.Now.Date;
            return new TimePeriod(dtNow.AddDays(-1), dtNow.AddDays(1).AddMilliseconds(-1));
        }

        private void simpleButtonRemove_Click(object sender, EventArgs e)
        {
            if (listBoxTimeVec.SelectedItem == null) return;

            listBoxTimeVec.Items.Remove(listBoxTimeVec.SelectedItem);
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class WorkTestCondition : AreaTestCondition
    {
        public List<TimePeriod> TimeVec { get; set; }

        public WorkTestCondition()
        {
            TimeVec = new List<TimePeriod>();
        }

        public void SetContext(List<TimePeriod> times, int permeate, Color colorAch, Color colorUnAch)
        {
            this.TimeVec = times;
            base.SetContext(permeate, colorAch, colorUnAch);
        }

        public override void CheckAchieve(CPermeate permeate, bool isVillage)
        {
            if (isVillage)
                permeate.BAchieve = permeate.IValidCnt > 0;
            else
                permeate.BAchieve = permeate.DPermeate >= Permeate;
        }
    }
}
