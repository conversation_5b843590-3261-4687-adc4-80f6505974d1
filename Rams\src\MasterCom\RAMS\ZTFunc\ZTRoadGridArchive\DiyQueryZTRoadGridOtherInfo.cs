﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryZTRoadGridAreaSum : DIYSQLBase
    {
        public DiyQueryZTRoadGridAreaSum()
            : base(MainModel.GetInstance())
        {

        }

        public List<ZTRoadGridArchiveSumRes> ZTRoadGridArchiveSumResList { get; set; } = new List<ZTRoadGridArchiveSumRes>();

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                getDataBySql(clientProxy, package);
            }
            catch (Exception ex)
            {
                log.Error(ex.ToString());
            }
        }

        protected override string getSqlTextString()
        {
            string strSQL = "select roadid,roadname,countyname,length,roadtypename from tb_cfg_roadana_road";
            return strSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_String;
            return rType;
        }

        protected virtual void getDataBySql(ClientProxy clientProxy, Package package)
        {
            string strsql = getSqlTextString();
            E_VType[] retArrDef = getSqlRetTypeArr();//获得枚举类型数组
            package.Command = Command.DIYSearch;//枚举类型：DIY接口
            package.SubCommand = SubCommand.Request;//枚举类型：请求
            if (MainDB)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
            }
            else
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strsql);
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    sb.Append(",");
                }
            }

            package.Content.AddParam(sb.ToString().TrimEnd(','));
            clientProxy.Send();
            receiveRetData(clientProxy);
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;

            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        protected virtual void fillData(Package package)
        {
            ZTRoadGridArchiveSumRes info = new ZTRoadGridArchiveSumRes();
            info.RoadID = package.Content.GetParamInt();
            info.RoadName = package.Content.GetParamString();
            info.CompanyName = package.Content.GetParamString();
            info.RoadLength = package.Content.GetParamFloat();
            info.RoadLength = Math.Round(info.RoadLength / 1000, 2);
            info.AreaTypeName = package.Content.GetParamString();
            ZTRoadGridArchiveSumResList.Add(info);
        }
    }

    public class DiyQueryZTRoadGridAreaDetailList : DiyQueryZTRoadGridAreaSum
    {
        public List<ZTRoadGridArchiveRes> RoadGridArchiveResList { get; set; } = new List<ZTRoadGridArchiveRes>();
        protected ZTRoadGridArchiveCondition settingCondition;

        public void SetCondition(ZTRoadGridArchiveCondition settingCondition)
        {
            this.settingCondition = settingCondition;
        }

        protected override string getSqlTextString()
        {
            string strSQL = string.Format(@"select CityID,AreaTypeID,AreaTypeName,RoadID,RoadName,AreaID,AreaName,TlLongitude,TlLatitude,BrLongitude,BrLatitude,
CenterLongitude,CenterLatitude,PointShape from tb_cfg_arealist_point_road where RoadID in ({0});", settingCondition.RoadListStr);
            return strSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[14];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_Int;
            rType[11] = E_VType.E_Int;
            rType[12] = E_VType.E_Int;
            rType[13] = E_VType.E_VARYBIN;
            return rType;
        }

        protected override void fillData(Package package)
        {
            ZTRoadGridArchiveRes info = new ZTRoadGridArchiveRes();
            info.CityID = package.Content.GetParamInt();
            info.AreaTypeID = package.Content.GetParamInt();
            info.AreaTypeName = package.Content.GetParamString();
            info.RoadID = package.Content.GetParamInt();
            info.RoadName = package.Content.GetParamString();
            info.AreaID = package.Content.GetParamInt();
            info.AreaName = package.Content.GetParamString();
            info.TLLongitude = (package.Content.GetParamInt() / 10000000.0);
            info.TLLatitude = (package.Content.GetParamInt() / 10000000.0);
            info.BRLongitude = (package.Content.GetParamInt() / 10000000.0);
            info.BRLatitude = (package.Content.GetParamInt() / 10000000.0);
            info.CenterLongitude = (package.Content.GetParamInt() / 10000000.0);
            info.CenterLatitude = (package.Content.GetParamInt() / 10000000.0);
            byte[] pointBytes = package.Content.GetParamBytes();
            info.DbPointList = info.BytesToPoints(pointBytes);
            RoadGridArchiveResList.Add(info);
        }
    }

    public class DiyQueryZTRoadGridDataVerify : DiyQueryZTRoadGridAreaSum
    {
        public List<ZTRoadGridArchiveDataVerify> DataVerifyResList { get; set; } = new List<ZTRoadGridArchiveDataVerify>();
        public Dictionary<string, List<string>> ConverageDataVerifyDic { get; set; } = new Dictionary<string, List<string>>();

        protected override string getSqlTextString()
        {
            string strSQL = "select 地市ID,粒度,数据标识 from tb_数据核查_数据日期";
            return strSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            return rType;
        }

        protected override void fillData(Package package)
        {
            ZTRoadGridArchiveDataVerify info = new ZTRoadGridArchiveDataVerify();
            info.CityID = package.Content.GetParamInt();
            info.Type = package.Content.GetParamString();
            info.DataSuffix = package.Content.GetParamString();
            DataVerifyResList.Add(info);
        }

        public void SetConverageData()
        {
            ConverageDataVerifyDic.Clear();
            foreach (var data in DataVerifyResList)
            {
                List<string> dataSuffixList;
                if (!ConverageDataVerifyDic.TryGetValue(data.Type, out dataSuffixList))
                {
                    dataSuffixList = new List<string>();
                    ConverageDataVerifyDic.Add(data.Type, dataSuffixList);
                }
                dataSuffixList.Add(data.DataSuffix);
            }
        }
    }

    public class DiyQueryZTRoadGridAreaInfo : DiyQueryZTRoadGridAreaSum
    {
        public Dictionary<int, string> AreaInfo { get; set; } = new Dictionary<int, string>();

        protected override string getSqlTextString()
        {
            string strSQL = "select areaid,countyname from tb_cfg_roadana_area_otherinfo";
            return strSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            return rType;
        }

        protected override void fillData(Package package)
        {
            int areaid = package.Content.GetParamInt();
            string countyname = package.Content.GetParamString();
            AreaInfo.Add(areaid, countyname);
        }
    }

    public class DiyQueryZTRoadGridProblem : DiyQueryZTRoadGridAreaSum
    {
        public List<string> ProblemList { get; set; } = new List<string>();

        protected override string getSqlTextString()
        {
            string strSQL = "select problem from tb_cfg_roadana_problem group by problem";
            return strSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_String;
            return rType;
        }

        protected override void fillData(Package package)
        {
            string problem = package.Content.GetParamString();
            ProblemList.Add(problem);
        }
    }

    public class DiyQueryZTRoadGridNetReason : DiyQueryZTRoadGridAreaSum
    {
        public List<string> NetReasonList { get; set; } = new List<string>();

        protected override string getSqlTextString()
        {
            string strSQL = "select netreasonType from tb_cfg_roadana_netreason group by netreasonType";
            return strSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_String;
            return rType;
        }

        protected override void fillData(Package package)
        {
            string netReason = package.Content.GetParamString();
            NetReasonList.Add(netReason);
        }
    }
}
