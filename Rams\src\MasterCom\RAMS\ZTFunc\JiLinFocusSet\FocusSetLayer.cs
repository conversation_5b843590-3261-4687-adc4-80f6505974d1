﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc.JiLinFocusSet
{
    public class FocusSetLayer : LayerBase
    {
        public FocusSetLayer()
            : base("工单图层")
        {
        }

        readonly SolidBrush brush = new SolidBrush(Color.FromArgb(200, Color.Red));
        public List<FocusSetMainItem> Orders { get; set; }
        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || Orders == null || Orders.Count == 0)
            {
                return;
            }

            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate(50, 50);
            DbRect dRect;
            this.gisAdapter.FromDisplay(updateRect, out dRect);
            PointF lbPnt;
            PointF lbPnt100;
            this.gisAdapter.ToDisplay(new DbPoint(dRect.x1, dRect.y1), out  lbPnt);
            this.gisAdapter.ToDisplay(new DbPoint(dRect.x1 + 0.001, dRect.y1), out lbPnt100);
            float radius = Math.Abs(lbPnt100.X - lbPnt.X);

            foreach (FocusSetMainItem item in Orders)
            {
                foreach (EventItem evtItem in item.Items)
                {
                    if (dRect.IsPointInThisRect(evtItem.Longitude, evtItem.Latitude))
                    {
                        PointF pnt;
                        this.gisAdapter.ToDisplay(new DbPoint(evtItem.Longitude, evtItem.Latitude), out  pnt);
                        graphics.FillEllipse(brush, pnt.X - radius / 2, pnt.Y - radius / 2, radius, radius);
                    }
                }
            }
        }

        public FocusSetMainItem SelOrder { get; set; }
    }
}
