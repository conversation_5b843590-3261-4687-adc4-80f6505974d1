﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class FindPseudoStationByRegion : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static FindPseudoStationByRegion instance = null;

        protected List<PseudoStationInfoItem> pseudoStationInfoList = new List<PseudoStationInfoItem>();

        public static FindPseudoStationByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new FindPseudoStationByRegion();
                    }
                }
            }
            return instance;
        }

        protected FindPseudoStationByRegion()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            ServiceTypes.Add(ServiceType.GSM_VOICE);
        }
        public override string Name
        {
            get
            {
                return "LTE伪基站查找(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22063, this.Name);
        }

        protected override void fireShowForm()
        {
            if (pseudoStationInfoList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            PseudoStationInfoListForm frm = MainModel.CreateResultForm(typeof(PseudoStationInfoListForm)) as PseudoStationInfoListForm;
            frm.FillData(pseudoStationInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            pseudoStationInfoList = new List<PseudoStationInfoItem>();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    List<DTData> dtDataList = new List<DTData>();
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        dtDataList.Add((DTData)tp);
                    }

                    foreach (Message msg in file.Messages)
                    {
                        if (msg.ID == (int)EnumMsg.CPData)
                        {
                            dtDataList.Add((DTData)msg);
                        }
                    }

                    addDTDataByEvent(file, dtDataList);

                    dtDataList.Sort(comparer);

                    PseudoStationInfoItem pseudoStationInfo = dealDTDatas(file, dtDataList);

                    addToResultList(pseudoStationInfo);
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private static void addDTDataByEvent(DTFileDataManager file, List<DTData> dtDataList)
        {
            Event lastEvt = new Event();
            Event evt;
            Event nextEvt = new Event();
            for (int i = 0; i < file.Events.Count; i++)
            {
                evt = file.Events[i];
                if (i > 0)
                {
                    lastEvt = file.Events[i - 1];
                }
                if (i < file.Events.Count - 1)
                {
                    nextEvt = file.Events[i + 1];
                }

                if (evt.ID == (int)EnumCsfbEvent.LocationUpdateRequestLte_Gsm || evt.ID == (int)EnumCsfbEvent.LocationUpdateSuccessLte_Gsm
                    || evt.ID == (int)EnumCsfbEvent.LocationUpdateRequestGsm || evt.ID == (int)EnumCsfbEvent.LocationUpdateAcceptGsm
                    || evt.ID == (int)EnumCsfbEvent.MOCSFBRequest || evt.ID == (int)EnumCsfbEvent.MTCSFBRequest
                    || lastEvt.ID == (int)EnumCsfbEvent.LocationUpdateSuccessLte_Gsm || lastEvt.ID == (int)EnumCsfbEvent.LocationUpdateAcceptGsm
                    || nextEvt.ID == (int)EnumCsfbEvent.LocationUpdateRequestLte_Gsm || nextEvt.ID == (int)EnumCsfbEvent.LocationUpdateRequestGsm)
                {
                    dtDataList.Add((DTData)evt);
                }
            }
        }

        private PseudoStationInfoItem dealDTDatas(DTFileDataManager file, List<DTData> dtDataList)
        {
            TestPoint lastTp = new TestPoint();
            Event currentEvent;
            PseudoStationInfoItem pseudoStationInfo = new PseudoStationInfoItem(file.FileName);
            for (int i = 0; i < dtDataList.Count; i++)
            {
                if (dtDataList[i] is TestPoint)
                {
                    lastTp = dtDataList[i] as TestPoint;
                }
                else if (dtDataList[i] is Event)
                {
                    currentEvent = dtDataList[i] as Event;
                    if (pseudoStationInfo.IsGotBegin && (currentEvent.ID == (int)EnumCsfbEvent.LocationUpdateRequestLte_Gsm
                        || currentEvent.ID == (int)EnumCsfbEvent.LocationUpdateRequestGsm))
                    {
                        addToResultList(pseudoStationInfo);
                        pseudoStationInfo = new PseudoStationInfoItem(file.FileName);
                    }
                    doWithEvent(i, dtDataList, lastTp, ref pseudoStationInfo);
                }
                else if (dtDataList[i] is Message && pseudoStationInfo.IsGotBegin)
                {
                    doWithMsg(i, dtDataList, ref pseudoStationInfo);
                }
            }

            return pseudoStationInfo;
        }

        private void doWithEvent(int preIdx, List<DTData> dtDataList, TestPoint lastTp, ref PseudoStationInfoItem curItem)
        {
            Event evt = (Event)dtDataList[preIdx];
            if (evt.ID == (int)EnumCsfbEvent.LocationUpdateRequestLte_Gsm || evt.ID == (int)EnumCsfbEvent.LocationUpdateRequestGsm)//LAC更新请求
            {
                curItem.IsGotBegin = true;
                curItem.Events.Add(evt);
                curItem.EvtLocationUpdateRequest = evt;

                SearchLastEvent(preIdx, dtDataList, lastTp, ref curItem);
            }
            else if (evt.ID == (int)EnumCsfbEvent.LocationUpdateSuccessLte_Gsm || evt.ID == (int)EnumCsfbEvent.LocationUpdateAcceptGsm)//LAC更新成功
            {
                if (curItem.IsGotBegin && !curItem.IsEnd)
                {
                    curItem.Events.Add(evt);
                    curItem.IsUpdateSucess = true;
                    curItem.EvtLocationUpdateSuccess = evt;
                }
            }
            else if (evt.ID != (int)EnumCsfbEvent.MOCSFBRequest && evt.ID != (int)EnumCsfbEvent.MTCSFBRequest && curItem.IsUpdateSucess)//LocationUpdateSuccess的下一个事件
            {
                curItem.IsEnd = true;
            }
        }

        private void SearchLastEvent(int preIdx, List<DTData> dtDataList, TestPoint lastTp, ref PseudoStationInfoItem curItem)
        {
            for (int i = preIdx - 1; i >= 0; i--)
            {
                if (dtDataList[i] is Event)
                {
                    Event evt = dtDataList[i] as Event;

                    if (curItem.EvtBeforeLocationUpdate == null)
                    {
                        curItem.EvtBeforeLocationUpdate = evt;
                    }

                    if ((curItem.EvtLocationUpdateRequest.DateTime - evt.DateTime).TotalSeconds >= 60)
                    {
                        break;
                    }
                    if (curItem.EvtLocationUpdateRequest.ID == (int)EnumCsfbEvent.LocationUpdateRequestLte_Gsm 
                        && (evt.ID == (int)EnumCsfbEvent.MOCSFBRequest || evt.ID == (int)EnumCsfbEvent.MTCSFBRequest))
                    {
                        curItem.EvtCSFBRequest = evt;
                        curItem.Events.Add(evt);
                        curItem.TPCSFBRequestLast = lastTp;
                        break;
                    }
                }
            }
        }

        private void doWithMsg(int preIdx, List<DTData> dtDataList, ref PseudoStationInfoItem curItem)
        {
            Message msg = dtDataList[preIdx] as Message;
            curItem.Messages.Add(msg);
            if (msg.ID == (int)EnumMsg.CPData && !curItem.IsEnd)
            {
                curItem.ListMsgCPData.Add(msg);
            }
            if (curItem.ListMsgCPData.Count == 1)
            {
                for (int i = preIdx + 1; i < dtDataList.Count; i++)
                {
                    if (dtDataList[i] is TestPoint)
                    {
                        curItem.TPCPDataNext = dtDataList[i] as TestPoint;
                        break;
                    }
                }
            }
        }
        private void addToResultList(PseudoStationInfoItem curItem)
        {
            try
            {
                if (!curItem.IsGotBegin || curItem.EvtLocationUpdateRequest == null || curItem.EvtLocationUpdateSuccess == null)
                {
                    return;
                }

                curItem.LAC = (int?)curItem.EvtLocationUpdateSuccess["LAC"];

                if (curItem.LAC == null || CellManager.GetInstance().LacExists((int)curItem.LAC))
                {
                    return;
                }
                curItem.CI = (int?)curItem.EvtLocationUpdateSuccess["CI"];

                if (curItem.TPCPDataNext != null)
                {
                    if (curItem.TPCPDataNext is LTETestPointDetail)
                    {
                        curItem.EARFCN = (short?)curItem.TPCPDataNext["lte_gsm_SC_BCCH"];
                    }
                    else
                    {
                        curItem.EARFCN = (short?)curItem.TPCPDataNext["BCCH"];
                    }
                }
                curItem.Time = curItem.EvtLocationUpdateRequest.DateTime.ToString();
                curItem.Latitude = curItem.EvtLocationUpdateRequest.Latitude;
                curItem.Longitude = curItem.EvtLocationUpdateRequest.Longitude;

                dealSms(curItem);

                dealCSFB(curItem);

                if (curItem.EvtBeforeLocationUpdate != null)
                {
                    curItem.CellNameLast = curItem.EvtBeforeLocationUpdate.CellNameSrc;
                }
                curItem.SN = pseudoStationInfoList.Count + 1;
                pseudoStationInfoList.Add(curItem);
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private static void dealSms(PseudoStationInfoItem curItem)
        {
            if (curItem.ListMsgCPData != null)
            {
                List<string> listPhoneNumber = new List<string>();
                StringBuilder sbPhoneNumber = new StringBuilder();
                for (int i = 0; i < curItem.ListMsgCPData.Count; i++)
                {
                    MessageWithSource msgWithSource = curItem.ListMsgCPData[i] as MessageWithSource;
                    string phoneNumber = "";
                    MessageDecodeHelper.StartDissect(msgWithSource.Direction, msgWithSource.Source, msgWithSource.Length, msgWithSource.ID);
                    MessageDecodeHelper.GetSingleString("gsm_sms.tp-oa", ref phoneNumber);
                    phoneNumber = phoneNumber.Replace("\0", "").Trim();
                    if (phoneNumber == "" || listPhoneNumber.Contains(phoneNumber))
                    {
                        continue;
                    }
                    listPhoneNumber.Add(phoneNumber);
                    sbPhoneNumber.Append(phoneNumber + ";");
                }
                if (sbPhoneNumber.ToString() != "")
                {
                    curItem.PhoneNumber = sbPhoneNumber.Remove(sbPhoneNumber.Length - 1, 1).ToString();
                }
            }
        }

        private void dealCSFB(PseudoStationInfoItem curItem)
        {
            if (curItem.EvtCSFBRequest != null)
            {
                curItem.CellNameCsfbBegin = curItem.EvtCSFBRequest.CellNameSrc;
                curItem.IsInNBCellBegin = "否";
                if (curItem.EARFCN != null)
                {
                    for (int i = 0; i < 12; ++i)
                    {
                        short? earcn = null;
                        if (curItem.TPCSFBRequestLast is LTETestPointDetail)
                        {
                            earcn = (short?)curItem.TPCSFBRequestLast["lte_gsm_NC_BCCH", i];
                        }
                        if (earcn != null && earcn == curItem.EARFCN)
                        {
                            curItem.IsInNBCellBegin = "是";
                            break;
                        }
                    }
                }
            }
        }

        private readonly Comparer comparer = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }

        protected enum EnumMsg
        {
            LocationUpdatingAccept2G = 1282,
            LocationUpdatingAccept3G = 1899627778,

            LocationUpdatingRequest2G = 1288,
            LocationUpdatingRequest3G = 1899627784,

            Setup2G = 773,
            Setup3G = 1899627269,

            CPData = 2305,
        }

    }
}
