﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteStationAcceptQuery : DIYReplayFileQuery
    {
        public LteStationAcceptQuery(MainModel mainModel)
            : base(mainModel)
        {
            isAutoLoadCQTPicture = false;
        }

        public override string Name
        {
            get
            {
                return "LTE宏站验收";
            }
        }

        /// <summary>
        /// 成功导出Excel文件
        /// </summary>
        protected string exportReportFiles = "";

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22055, Name);
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            List<ColumnDefItem> items = null;
            foreach (string col in queryColumns)
            {
                items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                option.SampleColumns.AddRange(items);
            }

            option.EventInclude = true;
            option.MessageInclude = true;

            return option;
        }

        protected override bool isValidCondition()
        {
            if (setForm == null)
            {
                setForm = new LteStationSettingForm();
            }

            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            initManager(setForm.GetCondition());
            return true;
        }

        protected virtual void initManager(LteStationAcceptCondition cond)
        {
            manager = new LteStationAcceptManager();
            manager.SetAcceptCond(cond);
        }

        protected override void query()
        {
            MainModel.FireDTDataChanged(MainModel.MainForm);
            errMsg = null;
            base.query();
        }

        protected override void fireShowResult()
        {
            if (!string.IsNullOrEmpty(errMsg))
            {
                System.Windows.Forms.MessageBox.Show(errMsg, Name, System.Windows.Forms.MessageBoxButtons.OK);
            }
            else if (string.IsNullOrEmpty(exportReportFiles))
            {
                System.Windows.Forms.MessageBox.Show("没有结果可以导出,请检查文件命名和工参信息", Name, 
                    System.Windows.Forms.MessageBoxButtons.OK);
            }
            else
            {
                System.Windows.Forms.MessageBox.Show(string.Format("({0})站点的报告导出完成!", exportReportFiles.TrimEnd(',')),
                    Name, System.Windows.Forms.MessageBoxButtons.OK);
            }
        }

        // 分析完所有文件后调用
        protected override void doPostReplayAction()
        {
            WaitTextBox.Show("正在导出Excel文件...", afterAnalyzeInThread);
        }

        // 回放完一个文件后调用
        protected override void queryReplayInfo(ClientProxy clientProxy, Package package,FileInfo fileInfo)
        {
            base.queryReplayInfo(clientProxy, package, fileInfo);
            if (judgeValidFile(fileInfo))
            {
                analyzeFile(fileInfo);
                MainModel.DTDataManager.Clear();
            }
        }

        protected virtual bool judgeValidFile(FileInfo fileInfo)
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return false;
            }
            return true;
        }

        protected virtual void analyzeFile(FileInfo fileInfo)
        {
            try
            {
                manager.AnalyzeFile(fileInfo, MainModel.DTDataManager.FileDataManagers[0]);
            }
            catch (Exception ex)
            {
                errMsg = ex.Message + Environment.NewLine + ex.StackTrace;
                throw;
            }
        }

        protected virtual void afterAnalyzeInThread()
        {
            try
            {
                exportReportFiles = "";
                if (string.IsNullOrEmpty(errMsg))
                {
                    manager.DoWorkAfterAnalyze();
                    exportReportFiles = manager.HasExportedFiles;
                    errMsg = manager.ErrMsg;
                    manager = null;
                }
            }
            catch (Exception ex)
            {
                errMsg = ex.Message + Environment.NewLine + ex.StackTrace;
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        protected IStationAcceptManager manager;

        private LteStationSettingForm setForm;

        protected string errMsg;

        #region queryColumns
        protected virtual List<string> queryColumns
        {
            get
            {
                return new List<string>()
                        {
                             "isampleid",
                             "itime",
                             "ilongitude",
                             "ilatitude",
                             "lte_TAC", 
                             "lte_ECI",
                             "lte_RSRP",
                             "lte_SINR",
                             "lte_EARFCN",
                             "lte_PCI",
                             "lte_APP_ThroughputUL",
                             "lte_APP_ThroughputDL",
                             "lte_PDCP_UL",
                             "lte_PDCP_DL",
                             "lte_APP_type",
                        };
            }
        }
            
            
        #endregion
    }
}
