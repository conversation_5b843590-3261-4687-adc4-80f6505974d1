﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsWeakRsrpStater : LteMgrsStaterBase
    {
        public LteMgrsWeakRsrpStater()
        {
            GridSize = LteMgrsBaseSettingManager.Instance.GridSize;
        }

        public double RsrpMax
        {
            get;
            set;
        }

        public int GridCount
        {
            get;
            set;
        }

        public double GridSize
        {
            get;
            set;
        }

        public LteMgrsRsrpBandType FreqType
        {
            get;
            set;
        }

        public override void DoStat(LteMgrsFuncItem curFuncItem)
        {
            this.tmpFuncItem = curFuncItem;
        }

        public override List<LteMgrsResultControlBase> GetResult()
        {
            LteMgrsWeakRsrpResult resultControl = new LteMgrsWeakRsrpResult();
            resultControl.FillData(tmpFuncItem);
            return new List<LteMgrsResultControlBase>() { resultControl };
        }

        public override void Clear()
        {
            this.tmpFuncItem = null;
        }

        public List<LteMgrsWeakRsrpView> GetViews(LteMgrsCity city)
        {
            List<LteMgrsWeakRsrpView> retList = new List<LteMgrsWeakRsrpView>();
            foreach (LteMgrsRegion region in city.RegionDic.Values)
            {
                // find weak grids
                List<LteMgrsGrid> gridList = new List<LteMgrsGrid>(region.GridDic.Values);
                Dictionary<LteMgrsGrid, float> gridRsrpDic = FiltterGridRsrpDic(gridList);

                // find block
                LteMgrsBlockFinder<LteMgrsGrid> blockFinder = new LteMgrsBlockFinder<LteMgrsGrid>(GridSize, GridCount);
                List<LteMgrsGrid[]> blocks = blockFinder.FindBlocks(new List<LteMgrsGrid>(gridRsrpDic.Keys));

                // result view
                foreach (LteMgrsGrid[] grids in blocks)
                {
                    float[] values = new float[grids.Length];
                    for (int i = 0; i < grids.Length; ++i)
                    {
                        values[i] = gridRsrpDic[grids[i]];
                    }
                    LteMgrsWeakRsrpView view = new LteMgrsWeakRsrpView(city.CityName, region.RegionName, grids, values);
                    view.FilterCells(this.FreqType);
                    view.SN = retList.Count;
                    retList.Add(view);
                }
            }
            return retList;
        }

        public List<LteMgrsDrawItem> GetDrawList(List<LteMgrsWeakRsrpView> viewList)
        {
            List<LteMgrsDrawItem> retList = new List<LteMgrsDrawItem>();
            foreach (LteMgrsWeakRsrpView curView in viewList)
            {
                LteMgrsGrid[] grids = curView.Grids;
                float[] values = curView.Rsrps;
                for (int i = 0; i < curView.GridViews.Count; ++i)
                {
                    LteMgrsGrid grid = grids[i];
                    float rsrp = values[i];
                    LteMgrsDrawItem item = new LteMgrsDrawItem(new DbPoint(grid.TLLng, grid.BRLat), new DbPoint(grid.BRLng, grid.TLLat));
                    item.FillColor = Ranges.GetColor(rsrp);
                    item.ToolInfoDetail = grid.DetailInfo;
                    item.ToolInfoTitle = grid.MgrsString;
                    item.GridCellInfo = curView.GridViews[i];
                    retList.Add(item);
                }
            }
            return retList;
        }

        public LteMgrsLegendGroup GetLegend()
        {
            return Ranges.GetLegend();
        }

        private Dictionary<LteMgrsGrid, float> FiltterGridRsrpDic(List<LteMgrsGrid> gridList)
        {
            Dictionary<LteMgrsGrid, float> retDic = new Dictionary<LteMgrsGrid, float>();
            foreach (LteMgrsGrid grid in gridList)
            {
                float value = LteMgrsGridHelper.GetTopRsrp(grid, FreqType);
                if (float.IsNaN(value) || value > RsrpMax)
                {
                    continue;
                }
                retDic.Add(grid, value);
            }
            return retDic;
        }

        private LteMgrsFuncItem tmpFuncItem;

        public static LteMgrsColorRange Ranges { get; set; } = LteMgrsRsrpRangeStater.Ranges;
    }

    public class LteMgrsWeakRsrpView
    {
        public LteMgrsWeakRsrpView(string cityName, string regionName, LteMgrsGrid[] grids, float[] rsrps)
        {
            CityName = cityName;
            RegionName = regionName;
            Grids = grids;
            Rsrps = rsrps;
            GridCount = grids.Length;

            double rsrpSum = 0;
            MaxRsrp = double.MinValue;
            MinRsrp = double.MaxValue;
            StringBuilder descSb = new StringBuilder();
            for (int i = 0; i < grids.Length; ++i)
            {
                rsrpSum += rsrps[i];
                MaxRsrp = Math.Max(MaxRsrp, rsrps[i]);
                MinRsrp = Math.Min(MinRsrp, rsrps[i]);
                descSb.Append(grids[i].MgrsString + ";");
            }
            AvgRsrp = grids.Length == 0 ? 0 : rsrpSum / grids.Length;
            if (descSb.Length > 0)
            {
                descSb.Remove(descSb.Length - 1, 1);
            }
            GridSetDesc = descSb.ToString();

            GridViews = new List<LteMgrsWeakRsrpGrid>();
            for (int i = 0; i < grids.Length; ++i)
            {
                LteMgrsWeakRsrpGrid weakGrid = new LteMgrsWeakRsrpGrid(grids[i]);
                weakGrid.TopRsrp = rsrps[i];
                GridViews.Add(weakGrid);
            }
        }

        public LteMgrsGrid[] Grids
        {
            get;
            private set;
        }

        public float[] Rsrps
        {
            get;
            private set;
        }

        public List<LteMgrsWeakRsrpGrid> GridViews
        {
            get;
            private set;
        }

        public int SN
        {
            get;
            set;
        }

        public string CityName
        {
            get;
            private set;
        }

        public string RegionName
        {
            get;
            private set;
        }

        public string GridSetDesc
        {
            get;
            private set;
        }

        public int GridCount
        {
            get;
            private set;
        }

        public double MaxRsrp
        {
            get;
            private set;
        }

        public double MinRsrp
        {
            get;
            private set;
        }

        public double AvgRsrp
        {
            get;
            private set;
        }

        public string RoadName
        {
            get
            {
                if (roadName != null)
                {
                    return roadName;
                }

                Dictionary<string, int> roadDic = new Dictionary<string,int>();
                StringBuilder sb = new StringBuilder();
                foreach (LteMgrsGrid grid in Grids)
                {
                    string oneName = GISManager.GetInstance().GetRoadPlaceDesc(grid.CentLng, grid.CentLat);
                    if (string.IsNullOrEmpty(oneName))
                    {
                        continue;
                    }

                    if (!roadDic.ContainsKey(oneName))
                    {
                        roadDic[oneName] = 0;
                        if (sb.Length > 0)
                        {
                            sb.Append("|");
                        }
                        sb.Append(oneName);
                    }
                    ++roadDic[oneName];
                }
                roadName = sb.ToString();
                return roadName;
            }
        }

        public void FilterCells(LteMgrsRsrpBandType rsrpBandType)
        {
            foreach (LteMgrsWeakRsrpGrid grid in GridViews)
            {
                grid.Cells = LteMgrsGridHelper.FilterCells(grid.Cells, rsrpBandType);
            }
        }

        protected string roadName = null;
    }

    public class LteMgrsWeakRsrpGrid
    {
        public LteMgrsWeakRsrpGrid(LteMgrsGrid grid)
        {
            MgrsString = grid.MgrsString;
            CentLng = grid.CentLng;
            CentLat = grid.CentLat;
            SampleCount = grid.TestPointCount;
            Cells = grid.CellList;
            MgrsGrid = grid;
            double rsrpSum = 0;
            List<LteMgrsFreq> freqList = grid.FreqList;
            foreach (LteMgrsFreq freq in freqList)
            {
                rsrpSum += freq.RsrpAvg;
            }
            AvgRsrp = freqList.Count == 0 ? 0 : rsrpSum / freqList.Count;
        }

        public LteMgrsGrid MgrsGrid
        {
            get;
            private set;
        }

        public List<LteMgrsCell> Cells
        {
            get;
            set;
        }

        public string MgrsString
        {
            get;
            set;
        }

        public double CentLng
        {
            get;
            set;
        }

        public double CentLat
        {
            get;
            set;
        }

        public int SampleCount
        {
            get;
            set;
        }

        /// <summary>
        /// 在重叠度计算中，TopRsrp用作栅格特定频段的重叠覆盖度
        /// </summary>
        public double TopRsrp
        {
            get;
            set;
        }

        /// <summary>
        /// 栅格特定频段的平均场强
        /// </summary>
        public double AvgRsrp
        {
            get;
            set;
        }
    }
}
