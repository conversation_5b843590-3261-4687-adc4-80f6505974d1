﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;

namespace MasterCom.Util
{
    public class FtpSettings
    {
        public string FtpUserName { get; set; }
        public string FtpUserPwd { get; set; }
        public string FtpServerPath { get; set; }
        public Dictionary<string, object> Params
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["FtpUserName"] = this.FtpUserName;
                param["FtpUserPwd"] = this.FtpUserPwd;
                param["FtpServerPath"] = this.FtpServerPath;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("FtpUserName"))
                {
                    this.FtpUserName = (string)param["FtpUserName"];
                }
                if (param.ContainsKey("FtpUserPwd"))
                {
                    this.FtpUserPwd = (string)param["FtpUserPwd"];
                }
                if (param.ContainsKey("FtpServerPath"))
                {
                    this.FtpServerPath = (string)param["FtpServerPath"];
                }
            }
        }
    }
    public class FtpHelper
    {
        readonly string ftpServerIp;//ftp地址(ftp地址格式为:"ip"、"ip:端口号"、"ip//子目录"、"ip:端口号//子目录"，例如: "************:21//单站验收")
        readonly string ftpUserID;//ftp用户名
        readonly string ftpPassword;//ftp用户密码
        public FtpHelper(FtpSettings ftpSet)
        {
            this.ftpServerIp = ftpSet.FtpServerPath;
            this.ftpUserID = ftpSet.FtpUserName;
            this.ftpPassword = ftpSet.FtpUserPwd;
        }
        public FtpHelper(string ftpUserID, string ftpPassword, string ftpServerIp)
        {
            this.ftpServerIp = ftpServerIp;
            this.ftpUserID = ftpUserID;
            this.ftpPassword = ftpPassword;
        }

        private string getUri(string ftpRemotePath)
        {
            if (!string.IsNullOrEmpty(ftpRemotePath))
            {
                return "ftp://" + ftpServerIp + "/" + ftpRemotePath;
            }
            else
            {
                return "ftp://" + ftpServerIp;
            }
        }

        #region 上传

        /// <summary>  
        /// 上传单个文件  
        /// </summary>  
        /// <param name="localFilePath">本地文件完整路径</param>
        /// <param name="ftpRemotePath">FTP远程目录(该目录需已存在)</param>
        public void UpLoadFile(string localFilePath, string ftpRemotePath)
        {
            if (!File.Exists(localFilePath))
            {
                reportInfo("文件：“" + localFilePath + "” 不存在！");
                return;
            }
            System.IO.FileInfo localFileInfo = new System.IO.FileInfo(localFilePath);
            string strUri = getUri(ftpRemotePath) + Path.AltDirectorySeparatorChar + localFileInfo.Name;

            FtpWebRequest reqFTP = (FtpWebRequest)FtpWebRequest.Create(new Uri(strUri));//根据地址创建FtpWebRequest对象   
            reqFTP.Credentials = new NetworkCredential(ftpUserID, ftpPassword);
            reqFTP.KeepAlive = false;// 默认为true，连接不会被关闭 // 在一个命令之后被执行  
            reqFTP.Method = WebRequestMethods.Ftp.UploadFile;// 指定执行什么命令  
            reqFTP.UseBinary = true;// 指定数据传输类型  
            reqFTP.ContentLength = localFileInfo.Length;// 上传文件时通知服务器文件的大小  
            int buffLength = 2048;// 缓冲大小设置为2kb  
            byte[] buff = new byte[buffLength];
            int contentLen;


            FileStream fs = null;
            Stream strm = null;
            try
            {
                fs = localFileInfo.OpenRead();// 打开一个文件流 (System.IO.FileStream) 去读上传的文件  
                strm = reqFTP.GetRequestStream();// 把上传的文件写入流  
                contentLen = fs.Read(buff, 0, buffLength);// 每次读文件流的2kb  

                while (contentLen != 0)// 流内容没有结束  
                {
                    // 把内容从file stream 写入 upload stream  
                    strm.Write(buff, 0, contentLen);
                    contentLen = fs.Read(buff, 0, buffLength);
                }
                strm.Close();
                fs.Close();
                reportInfo("文件【" + localFilePath + "】上传成功！");
            }
            catch (Exception ex)
            {
                if (strm != null)
                {
                    strm.Close();
                }
                if (fs != null)
                {
                    fs.Close();
                }
                reportInfo("上传文件【" + localFilePath + "】时，发生错误：" + ex.Message);
            }
        }

        /// <summary>  
        /// 上传整个文件夹  
        /// </summary>  
        /// <param name="localFolderPath">要上传的文件夹完整路径</param>  
        /// <param name="ftpRemotePath">FTP远程目录(该目录需已存在)</param>
        public void UploadFolder(string localFolderPath, string ftpRemotePath)
        {
            if (!Directory.Exists(localFolderPath))
            {
                reportInfo("本地文件夹：“" + localFolderPath + "” 不存在！");
                return;
            }
            string localFolderName = System.IO.Path.GetFileName(localFolderPath);

            if (string.IsNullOrEmpty(ftpRemotePath))
            {
                ftpRemotePath = localFolderName;
            }
            else
            {
                ftpRemotePath += ("/" + localFolderName);
            }
            if (!CheckOrCreateFtpFolder(ftpRemotePath))
            {
                return;
            }

            //先上传该文件夹下直属的文件
            string[] childFilePaths = Directory.GetFiles(localFolderPath);
            foreach (string childFilePath in childFilePaths)
            {
                UpLoadFile(childFilePath, ftpRemotePath);
            }

            //再上传子文件夹中的文件
            string[] childFolderPaths = Directory.GetDirectories(localFolderPath);
            foreach (string childFolderPath in childFolderPaths)
            {
                UploadFolder(childFolderPath, ftpRemotePath);
            }
            reportInfo("文件夹【" + localFolderPath + "】上传成功！");
        }
        #endregion

        #region 下载

        /// <summary>
        /// 下载整个ftp目录
        /// </summary>
        /// <param name="ftpRemotePath">FTP远程目录（文件夹）</param>
        /// <param name="localFolderPath">本地保存的完整目录（文件夹）</param>
        /// <param name="delFileAfterDownload">每下载一个文件后，是否将ftp上的该文件删除</param>
        /// <returns>是否下载成功</returns>
        public void DownloadFolder(string ftpRemotePath, string localFolderPath, bool delFileAfterDownload)
        {
            List<string> fileAllList = GetFtpAllFiles(ftpRemotePath);
            List<string> folderList = new List<string>();

            if (!Directory.Exists(localFolderPath))
            {
                Directory.CreateDirectory(localFolderPath);
            }

            //1.首先下载所有的文件,并获取文件夹信息
            foreach (string file in fileAllList)
            {
                string extension = Path.GetExtension(file);
                if (string.IsNullOrEmpty(extension))
                {
                    folderList.Add(file);
                }
                else
                {
                    string localFilePath = Path.Combine(localFolderPath, Path.GetFileName(file));
                    DownloadFile(file, localFilePath, delFileAfterDownload);
                }
            }

            //2.下载文件夹
            foreach (string dir in folderList)
            {
                //有时候会出现空的子目录，这时候要排除
                if (string.IsNullOrEmpty(dir))
                {
                    continue;
                }
                string localChildFolderPath = Path.Combine(localFolderPath, Path.GetFileName(dir));
                DownloadFolder(dir, localChildFolderPath, delFileAfterDownload);//下载子文件夹
            }
        }

        /// <summary>  
        /// 从FTP服务器下载文件，指定本地路径和本地文件名  
        /// </summary>  
        /// <param name="ftpRemotePath">远程文件名</param>  
        /// <param name="localFilePath">保存本地的文件名（包含路径）</param>
        /// <param name="delFileAfterDownload">每下载一个文件后，是否将ftp上的该文件删除</param>
        /// <returns>是否下载成功</returns>  
        public bool DownloadFile(string ftpRemotePath, string localFilePath, bool delFileAfterDownload)
        {
            FileStream outputStream = null;
            FtpWebResponse response = null;
            Stream ftpStream = null;
            string strUri = getUri(ftpRemotePath);
            try
            {
                outputStream = new FileStream(localFilePath, FileMode.Create);
                if (ftpServerIp == null || ftpServerIp.Trim().Length == 0)
                {
                    reportInfo("ftp下载目标服务器地址未设置！");
                    return false;
                }
                FtpWebRequest reqFTP = (FtpWebRequest)FtpWebRequest.Create(new Uri(strUri));
                reqFTP.Credentials = new NetworkCredential(ftpUserID, ftpPassword);
                reqFTP.UseBinary = true;
                reqFTP.KeepAlive = false;

                reqFTP.Method = WebRequestMethods.Ftp.DownloadFile;
                response = (FtpWebResponse)reqFTP.GetResponse();
                ftpStream = response.GetResponseStream();

                long totalDownloadedByte = 0;
                int bufferSize = 2048;
                int readCount;
                byte[] buffer = new byte[bufferSize];
                readCount = ftpStream.Read(buffer, 0, bufferSize);
                while (readCount > 0)
                {
                    totalDownloadedByte = readCount + totalDownloadedByte;
                    outputStream.Write(buffer, 0, readCount);
                    readCount = ftpStream.Read(buffer, 0, bufferSize);
                }
                ftpStream.Close();
                outputStream.Close();
                response.Close();

                if (delFileAfterDownload)
                {
                    DeleteFtpFile(ftpRemotePath);
                }
                reportInfo("文件【" + strUri + "】下载成功！");
                return true;
            }
            catch (Exception ex)
            {
                if (ftpStream != null)
                {
                    ftpStream.Close();
                }
                if (outputStream != null)
                {
                    outputStream.Close();
                }
                if (response != null)
                {
                    response.Close();
                }
                reportInfo("文件【" + strUri + "】下载出错：" + ex.Message);
                return false;
            }
        }

        /// <summary>  
        /// 从FTP服务器下载文件，指定本地路径和本地文件名  
        /// </summary>  
        /// <param name="ftpRemotePath">远程文件名</param>  
        /// <param name="localFilePath">保存本地的文件名（包含路径）</param>
        /// <param name="delFileAfterDownload">每下载一个文件后，是否将ftp上的该文件删除</param>
        /// <returns>是否下载成功</returns>  
        public bool DownloadBrokenFile(string ftpRemotePath, string localFilePath, bool delFileAfterDownload)
        {
            try
            {
                long size = 0;
                if (File.Exists(localFilePath))
                {
                    using (FileStream outputStream = new FileStream(localFilePath, FileMode.Open))
                    {
                        size = outputStream.Length;
                    }
                }
                return downloadBrokenFile(ftpRemotePath, localFilePath, size, delFileAfterDownload);
            }
            catch (Exception ex)
            {
                reportError(ex);
                return false;
            }
        }

        /// <summary>  
        /// 从FTP服务器下载文件，指定本地路径和本地文件名（支持断点下载）  
        /// </summary>  
        /// <param name="ftpRemotePath">远程文件名</param>  
        /// <param name="localFilePath">保存本地的文件名（包含路径）</param>
        /// <param name="size">已下载文件流大小</param>
        /// <param name="delFileAfterDownload">每下载一个文件后，是否将ftp上的该文件删除</param>
        /// <returns>是否下载成功</returns>  
        private bool downloadBrokenFile(string ftpRemotePath, string localFilePath, long size, bool delFileAfterDownload)
        {
            FileStream outputStream = null;
            FtpWebResponse response = null;
            Stream ftpStream = null;
            string strUri = getUri(ftpRemotePath);
            try
            {
                outputStream = new FileStream(localFilePath, FileMode.Append);
                if (ftpServerIp == null || ftpServerIp.Trim().Length == 0)
                {
                    reportInfo("ftp下载目标服务器地址未设置！");
                    return false;
                }
                FtpWebRequest reqFTP = (FtpWebRequest)FtpWebRequest.Create(new Uri(strUri));
                reqFTP.Credentials = new NetworkCredential(ftpUserID, ftpPassword);
                reqFTP.UseBinary = true;
                reqFTP.KeepAlive = false;
                reqFTP.ContentOffset = size;

                reqFTP.Method = WebRequestMethods.Ftp.DownloadFile;
                response = (FtpWebResponse)reqFTP.GetResponse();
                ftpStream = response.GetResponseStream();

                long totalDownloadedByte = 0;
                int bufferSize = 2048;
                int readCount;
                byte[] buffer = new byte[bufferSize];
                readCount = ftpStream.Read(buffer, 0, bufferSize);
                while (readCount > 0)
                {
                    totalDownloadedByte = readCount + totalDownloadedByte;
                    outputStream.Write(buffer, 0, readCount);
                    readCount = ftpStream.Read(buffer, 0, bufferSize);
                }
                ftpStream.Close();
                outputStream.Close();
                response.Close();

                if (delFileAfterDownload)
                {
                    DeleteFtpFile(ftpRemotePath);
                }
                reportInfo("文件【" + strUri + "】下载成功！");
                return true;
            }
            catch (Exception ex)
            {
                if (ftpStream != null)
                {
                    ftpStream.Close();
                }
                if (outputStream != null)
                {
                    outputStream.Close();
                }
                if (response != null)
                {
                    response.Close();
                }
                reportInfo("文件【" + strUri + "】下载出错：" + ex.Message);
                return false;
            }
        }

        #endregion

        #region 删除
        public bool DeleteFtpFile(string ftpRemotePath)//删除单个文件
        {
            string strUri = getUri(ftpRemotePath);
            try
            {
                FtpWebRequest reqFTP = (FtpWebRequest)FtpWebRequest.Create(new Uri(strUri));
                reqFTP.Credentials = new NetworkCredential(ftpUserID, ftpPassword);
                reqFTP.KeepAlive = false;
                reqFTP.Method = WebRequestMethods.Ftp.DeleteFile;

                FtpWebResponse response = (FtpWebResponse)reqFTP.GetResponse();
                response.Close();
            }
            catch (Exception ex)
            {
                reportInfo("删除文件【" + strUri + "】时，发生错误：" + ex.Message);
                return false;
            }
            return true;
        }

        public bool DeleteEmptyFtpFolder(string ftpRemotePath)//删除空文件夹
        {
            string strUri = getUri(ftpRemotePath);
            try
            {
                FtpWebRequest reqFTP = (FtpWebRequest)FtpWebRequest.Create(new Uri(strUri));
                reqFTP.Credentials = new NetworkCredential(ftpUserID, ftpPassword);
                reqFTP.Method = WebRequestMethods.Ftp.RemoveDirectory;
                FtpWebResponse response = (FtpWebResponse)reqFTP.GetResponse();
                response.Close();
            }
            catch (Exception ex)
            {
                reportInfo("删除目录【" + strUri + "】时，发生错误：" + ex.Message);
                return false;
            }
            return true;
        }

        /// <summary>
        /// 删除文件夹以及其下面的所有内容
        /// </summary>
        /// <param name="ftp">ftp</param>
        public void DeleteFtpFolderWithAll(string ftpRemotePath)
        {
            List<string> fileAllList = GetFtpAllFiles(ftpRemotePath);
            List<string> folderList = new List<string>();

            //1.首先删除所有的文件,并获取文件夹信息
            foreach (string file in fileAllList)
            {
                string extension = System.IO.Path.GetExtension(file);
                if (string.IsNullOrEmpty(extension))
                {
                    folderList.Add(file);
                }
                else
                {
                    DeleteFtpFile(file);
                }
            }

            //2.删除文件夹
            foreach (string dir in folderList)
            {
                //有时候会出现空的子目录，这时候要排除
                if (string.IsNullOrEmpty(dir))
                {
                    continue;
                }
                DeleteFtpFolderWithAll(dir);//删除子文件夹
            }
            DeleteEmptyFtpFolder(ftpRemotePath);//删除当前文件夹
        }
        #endregion

        /// <summary>
        /// 文件重命名
        /// </summary>
        /// <param name="curFtpFilename">当前文件名称(文件名和扩展名,不包括上级文件夹路径)</param>
        /// <param name="newFtpFilename">重命名后文件名称(文件名和扩展名,不包括上级文件夹路径)</param>
        /// <param name="ftpRemotePath">该文件所属的远程目录</param>
        /// <param name="coverSameNameFile">文件名相同则直接覆盖</param>
        public void RenameFtpFile(string curFtpFilename, string newFtpFilename
            , string ftpRemotePath, bool coverSameNameFile)
        {
            string curFilePath = ftpRemotePath + Path.AltDirectorySeparatorChar + curFtpFilename;
            string strUri = getUri(curFilePath);
            try
            {
                if (!isFtpPathExist(curFilePath))//检测FTP文件是否存在  
                {
                    reportInfo("FTP文件：“" + curFilePath + "” 不存在！");
                    return;
                }

                string newFtpFilePath = ftpRemotePath + Path.AltDirectorySeparatorChar + newFtpFilename;
                if (coverSameNameFile && isFtpPathExist(newFtpFilePath))
                {
                    DeleteFtpFile(newFtpFilePath);
                }

                System.Net.FtpWebRequest reNameFtp = (FtpWebRequest)FtpWebRequest.Create(new Uri(strUri));//根据服务器信息FtpWebRequest创建类的对象
                reNameFtp.Credentials = new System.Net.NetworkCredential(ftpUserID, ftpPassword);//提供身份验证信息
                reNameFtp.KeepAlive = false; //设置请求完成之后是否保持到FTP服务器的控制连接，默认值为true
                reNameFtp.Method = WebRequestMethods.Ftp.Rename;
                reNameFtp.RenameTo = newFtpFilename;

                FtpWebResponse response = (FtpWebResponse)reNameFtp.GetResponse();
                response.Close();
            }
            catch (Exception ex)
            {
                reportInfo("重命名文件【" + strUri + "】时，发生错误：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取当前目录下明细(包含文件和文件夹)
        /// </summary>
        /// <returns></returns>
        public List<string> GetFtpAllFiles(string ftpRemotePath)
        {
            string strUri = getUri(ftpRemotePath);
            List<string> files = new List<string>();

            WebResponse response = null;
            StreamReader reader = null;
            try
            {
                FtpWebRequest reqFTP = (FtpWebRequest)FtpWebRequest.Create(new Uri(strUri));
                reqFTP.UseBinary = true;
                reqFTP.Credentials = new NetworkCredential(ftpUserID, ftpPassword);
                reqFTP.Method = WebRequestMethods.Ftp.ListDirectory;
                response = reqFTP.GetResponse();
                reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8);

                string lastFtpRemotePath = Path.GetDirectoryName(ftpRemotePath).Replace("\\", "/");//上级目录
                string line = reader.ReadLine();
                while (line != null)
                {
                    if (string.IsNullOrEmpty(lastFtpRemotePath))
                    {
                        files.Add(line);
                    }
                    else
                    {
                        files.Add(lastFtpRemotePath + "/" + line);
                    }
                    line = reader.ReadLine();
                }
                reader.Close();
                response.Close();
            }
            catch (Exception ex)
            {
                if (reader != null)
                {
                    reader.Close();
                }
                if (response != null)
                {
                    response.Close();
                }
                reportInfo("获取目录【" + strUri + "】下明细时，发生错误：" + ex.Message);
            }
            return files;
        }

        /// <summary>
        /// 检查路径是否存在，不存在则创建该路径,存在则直接返回
        /// </summary>
        /// <param name="ftpRemotePath">FTP远程目录</param>
        /// <returns></returns>
        public bool CheckOrCreateFtpFolder(string ftpRemotePath)
        {
            if (!isFtpPathExist(ftpRemotePath))//检测FTP文件夹路径是否存在  
            {
                return createFtpFolder(ftpRemotePath);//不存在，则创建此文件夹路径  
            }
            return true;
        }

        /// <summary>
        /// 创建新的文件夹，若已存在该文件夹，删除后再创建
        /// </summary>
        /// <param name="ftpRemotePath">FTP远程目录</param>
        /// <returns></returns>
        public bool CreatNewFtpFolder(string ftpRemotePath)
        {
            if (isFtpPathExist(ftpRemotePath))//检测FTP文件夹路径是否存在  
            {
                DeleteFtpFolderWithAll(ftpRemotePath);
            }
            return createFtpFolder(ftpRemotePath);//不存在，则创建此文件夹路径
        }

        private bool isFtpPathExist(string ftpRemotePath)
        {
            bool flag = true;
            try
            {
                string strUri = getUri(ftpRemotePath);
                //实例化FTP连接  
                FtpWebRequest chkFtp = (FtpWebRequest)FtpWebRequest.Create(new Uri(strUri));
                chkFtp.Credentials = new NetworkCredential(ftpUserID, ftpPassword);
                chkFtp.Method = WebRequestMethods.Ftp.ListDirectory;
                FtpWebResponse response = (FtpWebResponse)chkFtp.GetResponse();
                response.Close();
            }
            catch
            {
                flag = false;
            }
            return flag;
        }
        private bool createFtpFolder(string ftpRemotePath)
        {
            string strUri = getUri(ftpRemotePath);
            try
            {
                FtpWebRequest reqFTP = (FtpWebRequest)FtpWebRequest.Create(new Uri(strUri));
                reqFTP.Method = WebRequestMethods.Ftp.MakeDirectory;
                reqFTP.UseBinary = true;
                reqFTP.Credentials = new NetworkCredential(ftpUserID, ftpPassword);
                FtpWebResponse response = (FtpWebResponse)reqFTP.GetResponse();
                Stream ftpStream = response.GetResponseStream();
                ftpStream.Close();
                response.Close();
            }
            catch (Exception ex)
            {
                reportInfo("新建文件夹【" + strUri + "】时，发生错误：" + ex.Message);
                return false;
            }
            return true;
        }

        private void reportInfo(string str)
        {
            if (MainModel.GetInstance().IsBackground && MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
            }
            else
            {
                System.Windows.Forms.MessageBox.Show(str);
            }
        }
        private void reportError(Exception ex)
        {
            if (MainModel.GetInstance().IsBackground && MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
            }
            else
            {
                System.Windows.Forms.MessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
            }
        }
    }
}
