﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class LTECellInfoForm : BaseForm
    {
        LTECell cell = null;
        public LTECellInfoForm(MainModel mm,LTECell cell):base(mm)
        {
            InitializeComponent();
            if (cell!=null)
            {
                this.cell = cell;
                fillInfo();
            }
        }

        private void fillInfo()
        {
            txtName.Text = cell.Name;
            txtID.Text = cell.ID.ToString();
            txtCode.Text = cell.Code;
            txtLng.Text = cell.Longitude.ToString("F7");
            txtLat.Text = cell.Latitude.ToString("F7");
            txtTAC.Text = cell.TAC.ToString();
            txtECI.Text = cell.ECI.ToString();
            txtPCI.Text = cell.PCI.ToString();
            txtEarfcn.Text = cell.EARFCN.ToString();
            txtDirection.Text = cell.Direction.ToString();
            txtSectorID.Text = cell.SectorID.ToString();
            txtBTS.Text = cell.BTSName;
            txtDesc.Text = cell.DESC;
            txtFreqList.Text = cell.FreqListStr;
        }

        private void btnBTSInfo_Click(object sender, EventArgs e)
        {
            new LTEBTSInfoForm(MainModel, cell.BelongBTS).Show(Owner);
        }



    }
}
