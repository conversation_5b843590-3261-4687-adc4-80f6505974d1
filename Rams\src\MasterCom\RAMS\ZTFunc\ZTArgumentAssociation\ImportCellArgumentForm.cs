﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ImportCellArgumentForm : BaseDialog
    {
        public ImportCellArgumentForm()
        {
            InitializeComponent();
            InitCbxCitys();

            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnSelect.Click += BtnSelect_Click;
            cbxStartIndex.SelectedIndex = 0;
        }

        public ImportCellArgumentCondition GetCondition()
        {
            ImportCellArgumentCondition cond = new ImportCellArgumentCondition();
            cond.CityID = DistrictManager.GetInstance().GetDistrictID(cbxCity.SelectedItem as string);
            cond.FilePath = txtFile.Text;
            cond.IsClear = chkClear.Checked;
            cond.CellStartIndex = int.Parse(cbxStartIndex.SelectedItem as string);
            return cond;
        }

        private void BtnSelect_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel + "|" + FilterHelper.Csv;
            dlg.FilterIndex = 3;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtFile.Text = dlg.FileName;
            dlg.Dispose();
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (cbxCity.SelectedItem == null)
            {
                MessageBox.Show("请选择地市", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
                return;
            }
            else if (string.IsNullOrEmpty(txtFile.Text))
            {
                MessageBox.Show("请选择数据文件", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void InitCbxCitys()
        {
            cbxCity.Items.Clear();

            MainModel mainModel = MainModel.GetInstance();
            if (mainModel.User.DBID == -1)
            {
                string[] cityNames = DistrictManager.GetInstance().DistrictNames;
                foreach (string city in cityNames)
                {
                    if (string.IsNullOrEmpty(city))
                    {
                        continue;
                    }
                    cbxCity.Items.Add(city);
                }
                if (cbxCity.Items.Count > 0)
                {
                    cbxCity.SelectedIndex = 0;
                }
            }
            else
            {
                string cityName = DistrictManager.GetInstance().getDistrictName(mainModel.DistrictID);
                if (cityName != null)
                {
                    cbxCity.Items.Add(cityName);
                    cbxCity.SelectedIndex = 0;
                }
            }
        }
    }
}
