﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Func
{
    public class CellGridDetailInfo
    {
        public int CityID
        {
            get;
            set;
        }
        public int LAC
        {
            get;
            set;
        }
        public int CI
        {
            get;
            set;
        }
        public DateTime Time
        {
            get;
            set;
        }
        public int TlLongitude
        {
            get;
            set;
        }
        public int TlLatitude
        {
            get;
            set;
        }
        public int BrLongitude
        {
            get;
            set;
        }
        public int BrLatitude
        {
            get;
            set;
        }
        public int RSRP_nTotal
        {
            get;
            set;
        }
        public Int64 RSRP_nSum
        {
            get;
            set;
        }

        public LTECell lteCell
        {
            get
            {
                return CellManager.GetInstance().GetLTECell(this.Time, this.LAC, this.CI);
            }
        }
        public double AvgRSRP
        {
            get
            {
                return Math.Round((double)RSRP_nSum / (double)RSRP_nTotal, 2);
            }
        }
        public double MidLongitude
        {
            get
            {
                return ((double)TlLongitude + (double)BrLongitude) / 2;
            }
        }
        public double MidLatitude
        {
            get
            {
                return ((double)TlLatitude + (double)BrLatitude) / 2;
            }
        }
        public DbRect Bounds
        {
            get { return new DbRect((double)TlLongitude / 10000000, (double)BrLatitude / 10000000, (double)BrLongitude / 10000000, (double)TlLatitude / 10000000); }
        }
        public static string GetColumnSelSql()
        {
            string sql = @"[icityid]
                    ,[iLAC]
                    ,[iCI]
                    ,[startTime]
                    ,[itllongitude]
                    ,[itllatitude]
                    ,[ibrlongitude]
                    ,[ibrlatitude]
                    ,[RSRP_nTotal]
                    ,[RSRP_nSum]";
            return sql;
        }
        public static CellGridDetailInfo FillFrom(Content content)
        {
            CellGridDetailInfo detail = new CellGridDetailInfo();
            detail.CityID = content.GetParamInt();
            detail.LAC = content.GetParamInt();
            detail.CI = content.GetParamInt();
            detail.Time = Convert.ToDateTime("1970-01-01 8:00:00").AddSeconds((double)content.GetParamInt());
            detail.TlLongitude = content.GetParamInt();
            detail.TlLatitude = content.GetParamInt();
            detail.BrLongitude = content.GetParamInt();
            detail.BrLatitude = content.GetParamInt();
            detail.RSRP_nTotal = content.GetParamInt();
            detail.RSRP_nSum = content.GetParamInt64();

            return detail;
        }
    }

    public class CellGridWithCell
    {
        public int SN
        {
            get;
            set;
        }
        public bool isChecked
        {
            get;
            set;
        }
        public int LAC
        {
            get;
            set;
        }
        public int CI
        {
            get;
            set;
        }
        public string Cell
        {
            get;
            set;
        }
        //private List<CellGridDetailInfo> cellGridDetails = new List<CellGridDetailInfo>();
        public List<CellGridDetailInfo> CellGridDetails
        {
            get;
            set;
        }
        //public void AddDetail(CellGridDetailInfo detail)
        //{
        //    cellGridDetails.Add(detail);
        //}
    }

    public class DIYSQLCellGridDetail : DIYSQLBase
    {
        private readonly List<CellGridDetailInfo> cellGridDetails = new List<CellGridDetailInfo>();
        public List<CellGridDetailInfo> CellGridDetails
        {
            get
            {
                return cellGridDetails;
            }
        }
        private readonly string table;
        public DIYSQLCellGridDetail(MainModel mainModel, string table)
            : base(mainModel)
        {
            this.table = table;
        }

        protected override string getSqlTextString()
        {
            string tbName = "MBD_CITY_Shenzhen.dbo." + this.table;
            string strSql = @" select "
                        + CellGridDetailInfo.GetColumnSelSql()
                        + " from " + tbName
                        + " WHERE icityid>0 "
            //+ "AND iLAC>0 "
            + "AND iCI>0 AND RSRP_nTotal>=10 ";

            return strSql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] types = new E_VType[10];
            types[0] = E_VType.E_Int;
            types[1] = E_VType.E_Int;
            types[2] = E_VType.E_Int;
            types[3] = E_VType.E_Int;
            types[4] = E_VType.E_Int;
            types[5] = E_VType.E_Int;
            types[6] = E_VType.E_Int;
            types[7] = E_VType.E_Int;
            types[8] = E_VType.E_Int;
            types[9] = E_VType.E_Int64;

            return types;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellGridDetailInfo cg = CellGridDetailInfo.FillFrom(package.Content);
                    cellGridDetails.Add(cg);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }
        public override string Name
        {
            get { return "DIYSQLCellGrid"; }
        }
    }

    public class DIYSQLCellGridTable : DIYSQLBase
    {
        private readonly List<string> tableNames = new List<string>();
        public List<string> TableNames
        {
            get { return tableNames; }
        }
        protected override string getSqlTextString()
        {
            string strSql = @" select name from MBD_CITY_Shenzhen.dbo.sysobjects where type = 'U' and name LIKE 'TB_SIGNAL_CELLGRID_01_%' ORDER BY RIGHT(name,6) DESC";

            return strSql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] types = new E_VType[1];
            types[0] = E_VType.E_String;

            return types;
        }

        private string getName(Content content)
        {
            return content.GetParamString();
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string name = getName(package.Content);
                    tableNames.Add(name);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }
        public override string Name
        {
            get { return "DIYSQLCellGridTable"; }
        }
    }
}
