﻿using System;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsWeakRsrpSetting : LteMgrsConditionControlBase
    {
        public NbIotMgrsWeakRsrpSetting()
        {
            InitializeComponent();
            initValues();
        }

        public override string Title
        {
            get { return "连续弱覆盖"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            invalidReason = null;
            return new object[] {
                (int)numGridCount.Value,
                (double)numMaxRSRP.Value,
                (double)numMinRSRP.Value,
                chkSINR.Checked,
                (double)numMaxSINR.Value,
                radAndOr.SelectedIndex == 0,
            };
        }

        /// <summary>
        /// 保存条件信息
        /// </summary>
        /// <param name="xcfg"></param>
        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            XmlElement configWeakRsrp = xcfg.AddConfig("WeakRsrp");
            xcfg.AddItem(configWeakRsrp, "GridCount", (int)numGridCount.Value);
            xcfg.AddItem(configWeakRsrp, "MaxRsrp", (double)numMaxRSRP.Value);
            xcfg.AddItem(configWeakRsrp, "MinRsrp", (double)numMinRSRP.Value);
            xcfg.AddItem(configWeakRsrp, "CheckSINR", chkSINR.Checked.ToString());
            xcfg.AddItem(configWeakRsrp, "MaxSINR", (double)numMaxSINR.Value);
            xcfg.AddItem(configWeakRsrp, "CheckSINRType", radAndOr.SelectedIndex);
        }

        /// <summary>
        /// 从配置文件读取参数值
        /// </summary>
        private void initValues()
        {
            XmlConfigFile configFile = new MyXmlConfigFile(NbIotMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                XmlElement configWeakRsrp = configFile.GetConfig("WeakRsrp");
                object obj = configFile.GetItemValue(configWeakRsrp, "GridCount");
                if (obj != null)
                {
                    numGridCount.Value = (int)obj;
                }
                obj = configFile.GetItemValue(configWeakRsrp, "MaxRsrp");
                if (obj != null)
                {
                    numMaxRSRP.Value = (decimal)(double)obj;
                }
                obj = configFile.GetItemValue(configWeakRsrp, "MinRsrp");
                if (obj != null)
                {
                    numMinRSRP.Value = (decimal)(double)obj;
                }
                obj = configFile.GetItemValue(configWeakRsrp, "CheckSINR");
                if (obj != null)
                {
                    chkSINR.Checked = obj.ToString() == "True";
                }
                obj = configFile.GetItemValue(configWeakRsrp, "MaxSINR");
                if (obj != null)
                {
                    numMaxSINR.Value = (decimal)(double)obj;
                }
                setCheckedItem(configFile, configWeakRsrp);
            }
        }

        private void setCheckedItem(XmlConfigFile configFile, XmlElement configWeakRsrp)
        {
            if (chkSINR.Checked)
            {
                object obj = configFile.GetItemValue(configWeakRsrp, "CheckSINRType");
                if (obj != null)
                {
                    radAndOr.SelectedIndex = (int)obj;
                }
            }
            else
            {
                radAndOr.SelectedIndex = 0;
            }
        }

        private void chkSINR_CheckedChanged(object sender, EventArgs e)
        {
            numMaxSINR.Enabled = chkSINR.Checked;
            if (!chkSINR.Checked)
            {
                radAndOr.SelectedIndex = 0;
            }
            radAndOr.Enabled = chkSINR.Checked;
        }
    }
}
