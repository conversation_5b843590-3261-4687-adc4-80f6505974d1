﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HighRSRPLowSINRForm : MinCloseForm
    {
        public HighRSRPLowSINRForm()
        {
            InitializeComponent();
        }

        public void FillData(List<HighRSRPLowSINR> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            gv.BestFitColumns();
            MainModel.ClearDTData();
            foreach (HighRSRPLowSINR item in list)
            {
                foreach (TestPoint tp in item.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            
            MainModel.FireDTDataChanged(this);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            HighRSRPLowSINR weakCover = gv.GetFocusedRow() as HighRSRPLowSINR;
            if (weakCover != null)
            {
                MainModel.ClearDTData();
                foreach (TestPoint tp in weakCover.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            //SaveFileDialog dlg = new SaveFileDialog();
            //dlg.RestoreDirectory = true;
            //dlg.Filter = "Excel2007文件(*.xlsx)|*.xlsx";
            //if (dlg.ShowDialog() == DialogResult.OK)
            //{
            //    this.Cursor = System.Windows.Forms.Cursors.WaitCursor;
            //    gv.ExportToXlsx(dlg.FileName);
            //    this.Cursor = System.Windows.Forms.Cursors.Default;
            //}
            ExcelNPOIManager.ExportToExcel(gv);
        }

    }
}
