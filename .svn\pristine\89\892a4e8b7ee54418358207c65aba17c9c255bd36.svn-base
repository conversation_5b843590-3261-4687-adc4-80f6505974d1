﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsBaseSettingControl : LteMgrsConditionControlBase
    {
        public LteMgrsBaseSettingControl()
        {
            InitializeComponent();
            dataGridView.CellDoubleClick += DataGridView_CellDoubleClick;
            dataGridView.DataBindingComplete += DataGridView_DataBindingComplete;
            this.numGridSize.Value = (decimal)LteMgrsBaseSettingManager.Instance.GridSize;
            /*
            List<LteMgrsCitySettingItem> cityItems = LteMgrsBaseSettingManager.Instance.CityItems;
            dataGridView.AutoGenerateColumns = false;
            dataGridView.DataSource = cityItems;
             */
            BindingCityList(this, EventArgs.Empty);
            MainModel.GetInstance().DistrictChanged += BindingCityList;
        }

        public override string Title
        {
            get { return "基本设置"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                LteMgrsCitySettingItem item = row.DataBoundItem as LteMgrsCitySettingItem;
                item.FieldName = row.Cells["colFieldName"].Value as string;
            }

            List<LteMgrsCitySettingItem> cityItems = dataGridView.DataSource as List<LteMgrsCitySettingItem>;
            if (!LteMgrsBaseSettingManager.Instance.CheckCityItems(cityItems, out invalidReason))
            {
                return null;
            }

            LteMgrsBaseSettingManager.Instance.SetCityItems(cityItems);
            LteMgrsBaseSettingManager.Instance.GridSize = (int)numGridSize.Value;

            invalidReason = null;
            return null;
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex != colShpFile.Index)
            {
                return;
            }

            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Shp;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Value = dlg.FileName;
            List<string> fieldNames = GetFieldNames(dlg.FileName);
            LteMgrsCitySettingItem curItem = dataGridView.Rows[e.RowIndex].DataBoundItem as LteMgrsCitySettingItem;
            curItem.FieldNames.Clear();
            curItem.FieldNames.AddRange(fieldNames);
            RefreshRowFieldNames(dataGridView.Rows[e.RowIndex], fieldNames, null);
        }

        private void DataGridView_DataBindingComplete(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                LteMgrsCitySettingItem item = row.DataBoundItem as LteMgrsCitySettingItem;
                RefreshRowFieldNames(row, item.FieldNames, item.FieldName);
            }
        }

        private void RefreshRowFieldNames(DataGridViewRow row, List<string> fieldNames, string defaultValue)
        {
            DataGridViewComboBoxCell cbxCell = ((DataGridViewComboBoxCell)row.Cells["colFieldName"]);
            cbxCell.Items.Clear();
            foreach (string fName in fieldNames)
            {
                cbxCell.Items.Add(fName);
            }
            if (defaultValue != null && fieldNames.Contains(defaultValue))
            {
                cbxCell.Value = defaultValue;
            }
            else if (cbxCell.Items.Count > 0)
            {
                cbxCell.Value = cbxCell.Items[0];
            }
        }

        private List<string> GetFieldNames(string shpFile)
        {
            List<string> retList = new List<string>();
            MapWinGIS.Shapefile shp = new MapWinGIS.Shapefile();
            shp.Open(shpFile, null);
            for (int i = 0; i < shp.NumFields; ++i)
            {
                MapWinGIS.Field field = shp.get_Field(i);
                retList.Add(field.Name);
            }
            shp.Close();
            return retList;
        }

        private void BindingCityList(object sender, EventArgs e)
        {
            List<LteMgrsCitySettingItem> cityItems = new List<LteMgrsCitySettingItem>();
            foreach (LteMgrsCitySettingItem si in LteMgrsBaseSettingManager.Instance.CityItems)
            {
                if (si.CityName == DistrictManager.GetInstance().getDistrictName(MainModel.GetInstance().DistrictID))
                {
                    cityItems.Add(si);
                }
            }
            dataGridView.AutoGenerateColumns = false;
            dataGridView.DataSource = cityItems;
        }

        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            LteMgrsBaseSettingManager.Instance.SaveConfig(xcfg);
        }

    }
}
