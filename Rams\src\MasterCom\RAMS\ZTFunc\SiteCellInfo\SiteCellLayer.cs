﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.SiteCellInfo
{
    public class SiteCellLayer : LayerBase
    {
        public SiteCellLayer()
            : base("乡镇村庄小区图层")
        {

        }

        public CellSiteInfo CellSiteInfo { get; set; }
        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || CellSiteInfo == null || CellSiteInfo.Site == null)
            {
                return;
            }

            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate(50, 50);
            DbRect dRect;
            this.gisAdapter.FromDisplay(updateRect, out dRect);
            PointF siteP = PointF.Empty;
            if (CellSiteInfo.Site != null)
            {
                DbPoint pt = new DbPoint(CellSiteInfo.Site.CenterLng, CellSiteInfo.Site.CenterLat);
                this.gisAdapter.ToDisplay(pt, out siteP);
            }
          
            graphics.FillEllipse(Brushes.Blue, siteP.X - 4, siteP.Y - 4, 8, 8);
            Pen pen = new Pen(Color.Red, 2);
            foreach (BTSSiteInfo btsInfo in CellSiteInfo.Site.btsDisDic.Keys)
            {
                DbPoint btsPt = new DbPoint(btsInfo.BTS.Longitude, btsInfo.BTS.Latitude);
                PointF btsPf;
                this.gisAdapter.ToDisplay(btsPt, out btsPf);
                if (btsInfo == this.CellSiteInfo.BTSInfo)
                {
                    graphics.DrawLine(pen, siteP, btsPf);
                }
                else
                {
                    graphics.DrawLine(Pens.Lime, siteP, btsPf);
                }
            }
        }

    }
}
