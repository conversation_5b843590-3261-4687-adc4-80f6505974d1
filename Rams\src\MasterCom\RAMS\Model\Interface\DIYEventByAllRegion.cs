﻿using System;
using System.Collections.Generic;
using System.Text;


using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.Net
{
    public class DIYEventByAllRegion : DIYEventQuery
    {
        public DIYEventByAllRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "事件查询(全区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11009, this.Name);
        }
        protected override void AddDIYRegion_Intersect(Package package)
        {
            //
        }
        protected override void prepareOtherEventFilter(Package package)
        {
            //
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override bool isValidPoint(double ltX,double ltY,double brX,double brY)
        {
            return true;
           
        }
        protected override bool isValidPoint(double jd, double wd)
        {
            return true;
            
        }

    }
}
