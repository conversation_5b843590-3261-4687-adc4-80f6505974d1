﻿namespace MasterCom.RAMS.ZTFunc.ZTAtuLogKPIGroup
{
    partial class TemplateSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.btnRemoveTemplate = new DevExpress.XtraEditors.SimpleButton();
            this.gridCtrlTmpl = new DevExpress.XtraGrid.GridControl();
            this.gvTmpl = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btnNewReport = new DevExpress.XtraEditors.SimpleButton();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.viewOption = new System.Windows.Forms.DataGridView();
            this.cbxLogicalType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.btnAddCol = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.btnRemoveRow = new DevExpress.XtraEditors.SimpleButton();
            this.rngClrSetting = new MasterCom.RAMS.CQT.ScoreRangeColorSettingPanel();
            this.colKPIName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colKPI = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colLogic = new System.Windows.Forms.DataGridViewComboBoxColumn();
            this.colValue = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colDefaultStatus = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.colStatPercent = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlTmpl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvTmpl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.viewOption)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxLogicalType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.DataPropertyName = "KPIName";
            this.dataGridViewTextBoxColumn1.HeaderText = "条件值";
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            this.dataGridViewTextBoxColumn1.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewTextBoxColumn1.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.DataPropertyName = "KPIFormular";
            this.dataGridViewTextBoxColumn2.HeaderText = "标题";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            this.dataGridViewTextBoxColumn2.ReadOnly = true;
            this.dataGridViewTextBoxColumn2.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewTextBoxColumn2.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            this.dataGridViewTextBoxColumn2.Width = 200;
            // 
            // dataGridViewTextBoxColumn3
            // 
            this.dataGridViewTextBoxColumn3.DataPropertyName = "RefrentValue";
            this.dataGridViewTextBoxColumn3.HeaderText = "参考值";
            this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            this.dataGridViewTextBoxColumn3.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewTextBoxColumn3.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.groupControl4);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.AutoScroll = true;
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl3);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1044, 491);
            this.splitContainerControl1.SplitterPosition = 312;
            this.splitContainerControl1.TabIndex = 11;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.btnRemoveTemplate);
            this.groupControl4.Controls.Add(this.gridCtrlTmpl);
            this.groupControl4.Controls.Add(this.btnSave);
            this.groupControl4.Controls.Add(this.btnNewReport);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(312, 487);
            this.groupControl4.TabIndex = 0;
            this.groupControl4.Text = "模板列表";
            // 
            // btnRemoveTemplate
            // 
            this.btnRemoveTemplate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRemoveTemplate.Location = new System.Drawing.Point(112, 441);
            this.btnRemoveTemplate.Name = "btnRemoveTemplate";
            this.btnRemoveTemplate.Size = new System.Drawing.Size(87, 27);
            this.btnRemoveTemplate.TabIndex = 3;
            this.btnRemoveTemplate.Text = "删除";
            this.btnRemoveTemplate.Click += new System.EventHandler(this.btnRemoveTemplate_Click);
            // 
            // gridCtrlTmpl
            // 
            this.gridCtrlTmpl.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridCtrlTmpl.Location = new System.Drawing.Point(3, 26);
            this.gridCtrlTmpl.MainView = this.gvTmpl;
            this.gridCtrlTmpl.Name = "gridCtrlTmpl";
            this.gridCtrlTmpl.ShowOnlyPredefinedDetails = true;
            this.gridCtrlTmpl.Size = new System.Drawing.Size(304, 409);
            this.gridCtrlTmpl.TabIndex = 1;
            this.gridCtrlTmpl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvTmpl});
            // 
            // gvTmpl
            // 
            this.gvTmpl.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedCell.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedCell.BorderColor = System.Drawing.Color.Black;
            this.gvTmpl.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gvTmpl.Appearance.FocusedCell.Options.UseBorderColor = true;
            this.gvTmpl.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedRow.BorderColor = System.Drawing.Color.Black;
            this.gvTmpl.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvTmpl.Appearance.FocusedRow.Options.UseBorderColor = true;
            this.gvTmpl.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1});
            this.gvTmpl.GridControl = this.gridCtrlTmpl;
            this.gvTmpl.Name = "gvTmpl";
            this.gvTmpl.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvTmpl.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvTmpl.OptionsDetail.ShowDetailTabs = false;
            this.gvTmpl.OptionsView.EnableAppearanceEvenRow = true;
            this.gvTmpl.OptionsView.ShowDetailButtons = false;
            this.gvTmpl.OptionsView.ShowGroupPanel = false;
            this.gvTmpl.OptionsView.ShowIndicator = false;
            this.gvTmpl.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gvTmpl_FocusedRowChanged);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "名称";
            this.gridColumn1.FieldName = "Name";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Location = new System.Drawing.Point(219, 441);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(87, 27);
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "保存";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnNewReport
            // 
            this.btnNewReport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNewReport.Location = new System.Drawing.Point(5, 441);
            this.btnNewReport.Name = "btnNewReport";
            this.btnNewReport.Size = new System.Drawing.Size(87, 27);
            this.btnNewReport.TabIndex = 2;
            this.btnNewReport.Text = "新建";
            this.btnNewReport.Click += new System.EventHandler(this.btnNewReport_Click);
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.splitContainerControl2);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Enabled = false;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(724, 487);
            this.groupControl3.TabIndex = 0;
            this.groupControl3.Text = "模板设置";
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(2, 23);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.viewOption);
            this.splitContainerControl2.Panel1.Controls.Add(this.cbxLogicalType);
            this.splitContainerControl2.Panel1.Controls.Add(this.btnAddCol);
            this.splitContainerControl2.Panel1.Controls.Add(this.labelControl15);
            this.splitContainerControl2.Panel1.Controls.Add(this.btnRemoveRow);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.rngClrSetting);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(720, 462);
            this.splitContainerControl2.SplitterPosition = 262;
            this.splitContainerControl2.TabIndex = 12;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // viewOption
            // 
            this.viewOption.AllowUserToAddRows = false;
            this.viewOption.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.viewOption.BackgroundColor = System.Drawing.Color.White;
            this.viewOption.ColumnHeadersHeight = 40;
            this.viewOption.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.viewOption.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colKPIName,
            this.colKPI,
            this.colLogic,
            this.colValue,
            this.colDefaultStatus,
            this.colStatPercent});
            this.viewOption.GridColor = System.Drawing.SystemColors.GradientActiveCaption;
            this.viewOption.Location = new System.Drawing.Point(12, 39);
            this.viewOption.Name = "viewOption";
            this.viewOption.RowHeadersVisible = false;
            this.viewOption.RowTemplate.Height = 23;
            this.viewOption.Size = new System.Drawing.Size(700, 219);
            this.viewOption.TabIndex = 0;
            this.viewOption.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.viewOption_CellDoubleClick);
            this.viewOption.CellEndEdit += new System.Windows.Forms.DataGridViewCellEventHandler(this.viewOption_CellEndEdit);
            this.viewOption.CellValidating += new System.Windows.Forms.DataGridViewCellValidatingEventHandler(this.viewOption_CellValidating);
            this.viewOption.EditingControlShowing += new System.Windows.Forms.DataGridViewEditingControlShowingEventHandler(this.viewOption_EditingControlShowing);
            this.viewOption.SelectionChanged += new System.EventHandler(this.viewOption_SelectionChanged);
            // 
            // cbxLogicalType
            // 
            this.cbxLogicalType.Location = new System.Drawing.Point(70, 16);
            this.cbxLogicalType.Name = "cbxLogicalType";
            this.cbxLogicalType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxLogicalType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxLogicalType.Size = new System.Drawing.Size(105, 21);
            this.cbxLogicalType.TabIndex = 10;
            this.cbxLogicalType.SelectedIndexChanged += new System.EventHandler(this.cbxLogicalType_SelectedIndexChanged);
            // 
            // btnAddCol
            // 
            this.btnAddCol.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAddCol.Location = new System.Drawing.Point(625, 10);
            this.btnAddCol.Name = "btnAddCol";
            this.btnAddCol.Size = new System.Drawing.Size(87, 27);
            this.btnAddCol.TabIndex = 9;
            this.btnAddCol.Text = "添加行";
            this.btnAddCol.Click += new System.EventHandler(this.btnAddCol_Click);
            // 
            // labelControl15
            // 
            this.labelControl15.Location = new System.Drawing.Point(12, 19);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Size = new System.Drawing.Size(52, 14);
            this.labelControl15.TabIndex = 11;
            this.labelControl15.Text = "判断逻辑:";
            // 
            // btnRemoveRow
            // 
            this.btnRemoveRow.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRemoveRow.Enabled = false;
            this.btnRemoveRow.Location = new System.Drawing.Point(532, 10);
            this.btnRemoveRow.Name = "btnRemoveRow";
            this.btnRemoveRow.Size = new System.Drawing.Size(87, 27);
            this.btnRemoveRow.TabIndex = 7;
            this.btnRemoveRow.Text = "移除行";
            this.btnRemoveRow.Click += new System.EventHandler(this.btnRemoveRow_Click);
            // 
            // rngClrSetting
            // 
            this.rngClrSetting.DescColumnsVisible = true;
            this.rngClrSetting.Location = new System.Drawing.Point(12, 10);
            this.rngClrSetting.Name = "rngClrSetting";
            this.rngClrSetting.Size = new System.Drawing.Size(364, 181);
            this.rngClrSetting.TabIndex = 0;
            // 
            // colKPIName
            // 
            this.colKPIName.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.colKPIName.DataPropertyName = "KPIName";
            this.colKPIName.HeaderText = "指标名称";
            this.colKPIName.Name = "colKPIName";
            // 
            // colKPI
            // 
            this.colKPI.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.colKPI.DataPropertyName = "KPIFormula";
            this.colKPI.HeaderText = "指标公式";
            this.colKPI.Name = "colKPI";
            this.colKPI.ReadOnly = true;
            this.colKPI.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.colKPI.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // colLogic
            // 
            this.colLogic.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.colLogic.DataPropertyName = "Logic";
            this.colLogic.HeaderText = "条件逻辑";
            this.colLogic.Items.AddRange(new object[] {
            ">",
            "=",
            "<",
            "≥",
            "≤",
            "!="});
            this.colLogic.Name = "colLogic";
            // 
            // colValue
            // 
            this.colValue.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.colValue.DataPropertyName = "ReferentValue";
            this.colValue.HeaderText = "参考值(数字)";
            this.colValue.Name = "colValue";
            this.colValue.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.colValue.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // colDefaultStatus
            // 
            this.colDefaultStatus.DataPropertyName = "AsSatisfiedWhenNoValue";
            this.colDefaultStatus.HeaderText = "无指标值时视为满足条件";
            this.colDefaultStatus.Name = "colDefaultStatus";
            // 
            // colStatPercent
            // 
            this.colStatPercent.DataPropertyName = "StatPercent";
            this.colStatPercent.HeaderText = "统计满足条件占比";
            this.colStatPercent.Name = "colStatPercent";
            // 
            // TemplateSettingDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1044, 491);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "TemplateSettingDlg";
            this.Text = "设置";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlTmpl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvTmpl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.viewOption)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxLogicalType.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private DevExpress.XtraEditors.SimpleButton btnRemoveTemplate;
        private DevExpress.XtraGrid.GridControl gridCtrlTmpl;
        private DevExpress.XtraGrid.Views.Grid.GridView gvTmpl;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.SimpleButton btnNewReport;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private System.Windows.Forms.DataGridView viewOption;
        private DevExpress.XtraEditors.ComboBoxEdit cbxLogicalType;
        private DevExpress.XtraEditors.SimpleButton btnAddCol;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.SimpleButton btnRemoveRow;
        private CQT.ScoreRangeColorSettingPanel rngClrSetting;
        private System.Windows.Forms.DataGridViewTextBoxColumn colKPIName;
        private System.Windows.Forms.DataGridViewTextBoxColumn colKPI;
        private System.Windows.Forms.DataGridViewComboBoxColumn colLogic;
        private System.Windows.Forms.DataGridViewTextBoxColumn colValue;
        private System.Windows.Forms.DataGridViewCheckBoxColumn colDefaultStatus;
        private System.Windows.Forms.DataGridViewCheckBoxColumn colStatPercent;
    }
}