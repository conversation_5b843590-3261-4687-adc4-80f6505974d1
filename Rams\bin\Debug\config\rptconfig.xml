<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSPY v2004 rel. 2 U (http://www.xmlspy.com) by klw (ccc) -->
<Configs>
	<Config name="Projects">
		<Item typeName="IList" name="configs">
				<Item typeName="IDictionary">
					<Item typeName="String" key="Name">每月例行测试(DT)</Item>
					<Item typeName="Int32" key="ID">1</Item>
				</Item>
				<Item typeName="IDictionary">
					<Item typeName="String" key="Name">问题点复测(DT)</Item>
					<Item typeName="Int32" key="ID">3</Item>
				</Item>
				<Item typeName="IDictionary">
					<Item typeName="String" key="Name">变频项目(DT)</Item>
					<Item typeName="Int32" key="ID">7</Item>
				</Item>
				<Item typeName="IDictionary">
					<Item typeName="String" key="Name">扫频数据</Item>
					<Item typeName="Int32" key="ID">12</Item>
				</Item>
				<Item typeName="IDictionary">
					<Item typeName="String" key="Name">省公司巡检数据(DT)</Item>
					<Item typeName="Int32" key="ID">13</Item>
				</Item>
				<Item typeName="IDictionary">
					<Item typeName="String" key="Name">通信保障及额外测试(DT)</Item>
					<Item typeName="Int32" key="ID">15</Item>
				</Item>
				<Item typeName="IDictionary">
					<Item typeName="String" key="Name">投诉测试(DT)</Item>
					<Item typeName="Int32" key="ID">17</Item>
				</Item>
				<Item typeName="IDictionary">
					<Item typeName="String" key="Name">宏基站验收测试(DT)</Item>
					<Item typeName="Int32" key="ID">19</Item>
				</Item>
				<Item typeName="IDictionary">
					<Item typeName="String" key="Name">TD测试(DT)</Item>
					<Item typeName="Int32" key="ID">5</Item>
				</Item>
			</Item>
	</Config>
	<Config name="RoundProj">
		<Item name="roundIds" typeName="IList">
			<Item typeName="Int32">7</Item>
			<Item typeName="Int32">8</Item>
		</Item>
	</Config>
	<Config name="GisStatConfig">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">每月例行测试(DT)</Item>
				<Item typeName="Int32" key="ProjId">1</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">每月例行测试(CQT)</Item>
				<Item typeName="Int32" key="ProjId">2</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">问题点复测(DT)</Item>
				<Item typeName="Int32" key="ProjId">3</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">问题点复测(CQT)</Item>
				<Item typeName="Int32" key="ProjId">4</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">变频项目(DT)</Item>
				<Item typeName="Int32" key="ProjId">7</Item>
				<Item typeName="Int32" key="IsRound">1</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">变频项目(CQT)</Item>
				<Item typeName="Int32" key="ProjId">8</Item>
				<Item typeName="Int32" key="IsRound">1</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">村通测试</Item>
				<Item typeName="Int32" key="ProjId">9</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">扫频数据</Item>
				<Item typeName="Int32" key="ProjId">12</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">省公司巡检数据(DT)</Item>
				<Item typeName="Int32" key="ProjId">13</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">省公司巡检数据(CQT)</Item>
				<Item typeName="Int32" key="ProjId">14</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">通信保障及额外测试(DT)</Item>
				<Item typeName="Int32" key="ProjId">15</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">通信保障及额外测试(CQT)</Item>
				<Item typeName="Int32" key="ProjId">16</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">投诉测试(DT)</Item>
				<Item typeName="Int32" key="ProjId">17</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">投诉测试(CQT)</Item>
				<Item typeName="Int32" key="ProjId">18</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">宏基站验收测试(DT)</Item>
				<Item typeName="Int32" key="ProjId">19</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">宏基站验收测试(CQT)</Item>
				<Item typeName="Int32" key="ProjId">20</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">室内覆盖验收测试</Item>
				<Item typeName="Int32" key="ProjId">21</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">TD测试(DT)</Item>
				<Item typeName="Int32" key="ProjId">5</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="ProjName">TD测试(CQT)</Item>
				<Item typeName="Int32" key="ProjId">6</Item>
				<Item typeName="Int32" key="IsRound">0</Item>
			</Item>
		</Item>
	</Config>
	<Config name="NoGisStatConfig">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item typeName="String" key="AreaTypeName">本地网</Item>
				<Item typeName="Int32" key="AreaTypeID">1</Item>
				<Item typeName="IList" key="projects">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">每月例行测试(DT)</Item>
						<Item typeName="Int32" key="ID">1</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">问题点复测(DT)</Item>
						<Item typeName="Int32" key="ID">3</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">变频项目(DT)</Item>
						<Item typeName="Int32" key="ID">7</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">扫频数据</Item>
						<Item typeName="Int32" key="ID">12</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">省公司巡检数据(DT)</Item>
						<Item typeName="Int32" key="ID">13</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">通信保障及额外测试(DT)</Item>
						<Item typeName="Int32" key="ID">15</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">投诉测试(DT)</Item>
						<Item typeName="Int32" key="ID">17</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">宏基站验收测试(DT)</Item>
						<Item typeName="Int32" key="ID">19</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD测试(DT)</Item>
						<Item typeName="Int32" key="ID">5</Item>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="AreaTypeName">行政区县</Item>
				<Item typeName="Int32" key="AreaTypeID">2</Item>
				<Item typeName="IList" key="projects">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">每月例行测试(DT)</Item>
						<Item typeName="Int32" key="ID">1</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">问题点复测(DT)</Item>
						<Item typeName="Int32" key="ID">3</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">变频项目(DT)</Item>
						<Item typeName="Int32" key="ID">7</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">扫频数据</Item>
						<Item typeName="Int32" key="ID">12</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">省公司巡检数据(DT)</Item>
						<Item typeName="Int32" key="ID">13</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">通信保障及额外测试(DT)</Item>
						<Item typeName="Int32" key="ID">15</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">投诉测试(DT)</Item>
						<Item typeName="Int32" key="ID">17</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">宏基站验收测试(DT)</Item>
						<Item typeName="Int32" key="ID">19</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD测试(DT)</Item>
						<Item typeName="Int32" key="ID">5</Item>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="AreaTypeName">镇（街道办）</Item>
				<Item typeName="Int32" key="AreaTypeID">9</Item>
				<Item typeName="IList" key="projects">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">每月例行测试(DT)</Item>
						<Item typeName="Int32" key="ID">1</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">问题点复测(DT)</Item>
						<Item typeName="Int32" key="ID">3</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">变频项目(DT)</Item>
						<Item typeName="Int32" key="ID">7</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">扫频数据</Item>
						<Item typeName="Int32" key="ID">12</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">省公司巡检数据(DT)</Item>
						<Item typeName="Int32" key="ID">13</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">通信保障及额外测试(DT)</Item>
						<Item typeName="Int32" key="ID">15</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">投诉测试(DT)</Item>
						<Item typeName="Int32" key="ID">17</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">宏基站验收测试(DT)</Item>
						<Item typeName="Int32" key="ID">19</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD测试(DT)</Item>
						<Item typeName="Int32" key="ID">5</Item>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="AreaTypeName">高速公路</Item>
				<Item typeName="Int32" key="AreaTypeID">3</Item>
				<Item typeName="IList" key="projects">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">每月例行测试(DT)</Item>
						<Item typeName="Int32" key="ID">1</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">问题点复测(DT)</Item>
						<Item typeName="Int32" key="ID">3</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">变频项目(DT)</Item>
						<Item typeName="Int32" key="ID">7</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">省公司巡检数据(DT)</Item>
						<Item typeName="Int32" key="ID">13</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">通信保障及额外测试(DT)</Item>
						<Item typeName="Int32" key="ID">15</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">投诉测试(DT)</Item>
						<Item typeName="Int32" key="ID">17</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD测试(DT)</Item>
						<Item typeName="Int32" key="ID">5</Item>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="AreaTypeName">国道</Item>
				<Item typeName="Int32" key="AreaTypeID">4</Item>
				<Item typeName="IList" key="projects">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">每月例行测试(DT)</Item>
						<Item typeName="Int32" key="ID">1</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">问题点复测(DT)</Item>
						<Item typeName="Int32" key="ID">3</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">变频项目(DT)</Item>
						<Item typeName="Int32" key="ID">7</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">省公司巡检数据(DT)</Item>
						<Item typeName="Int32" key="ID">13</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">通信保障及额外测试(DT)</Item>
						<Item typeName="Int32" key="ID">15</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">投诉测试(DT)</Item>
						<Item typeName="Int32" key="ID">17</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD测试(DT)</Item>
						<Item typeName="Int32" key="ID">5</Item>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="AreaTypeName">海域</Item>
				<Item typeName="Int32" key="AreaTypeID">5</Item>
				<Item typeName="IList" key="projects">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">每月例行测试(DT)</Item>
						<Item typeName="Int32" key="ID">1</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">问题点复测(DT)</Item>
						<Item typeName="Int32" key="ID">3</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">变频项目(DT)</Item>
						<Item typeName="Int32" key="ID">7</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">省公司巡检数据(DT)</Item>
						<Item typeName="Int32" key="ID">13</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">通信保障及额外测试(DT)</Item>
						<Item typeName="Int32" key="ID">15</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">投诉测试(DT)</Item>
						<Item typeName="Int32" key="ID">17</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD测试(DT)</Item>
						<Item typeName="Int32" key="ID">5</Item>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="AreaTypeName">铁路</Item>
				<Item typeName="Int32" key="AreaTypeID">6</Item>
				<Item typeName="IList" key="projects">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">每月例行测试(DT)</Item>
						<Item typeName="Int32" key="ID">1</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">问题点复测(DT)</Item>
						<Item typeName="Int32" key="ID">3</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">变频项目(DT)</Item>
						<Item typeName="Int32" key="ID">7</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">省公司巡检数据(DT)</Item>
						<Item typeName="Int32" key="ID">13</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">通信保障及额外测试(DT)</Item>
						<Item typeName="Int32" key="ID">15</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">投诉测试(DT)</Item>
						<Item typeName="Int32" key="ID">17</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD测试(DT)</Item>
						<Item typeName="Int32" key="ID">5</Item>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
	<Config name="WkStatConfig">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item typeName="String" key="InnerName">WorkAllStatForm</Item>
				<Item typeName="String" key="Title">综合工作量统计</Item>
				<Item typeName="Int32" key="ProjId">-1</Item>
				<Item typeName="IList" key="ChildFuncs">
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="InnerName">FScan</Item>
				<Item typeName="String" key="Title">扫频测试</Item>
				<Item typeName="Int32" key="ProjId">12</Item>
				<Item typeName="IList" key="ChildFuncs">
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="InnerName">cqt</Item>
				<Item typeName="String" key="Title">CQT测试</Item>
				<Item typeName="Int32" key="ProjId">0</Item>
				<Item typeName="IList" key="ChildFuncs">
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">每月例行测试(CQT)</Item>
						<Item typeName="String" key="Title">每月例行测试(CQT)</Item>
						<Item typeName="Int32" key="ProjId">2</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">问题点复测(CQT)</Item>
						<Item typeName="String" key="Title">问题点复测(CQT)</Item>
						<Item typeName="Int32" key="ProjId">4</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">变频项目(CQT)</Item>
						<Item typeName="String" key="Title">变频项目(CQT)</Item>
						<Item typeName="Int32" key="ProjId">8</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">村通测试</Item>
						<Item typeName="String" key="Title">村通测试</Item>
						<Item typeName="Int32" key="ProjId">9</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">省公司巡检数据(CQT)</Item>
						<Item typeName="String" key="Title">省公司巡检数据(CQT)</Item>
						<Item typeName="Int32" key="ProjId">14</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">通信保障及额外测试(CQT)</Item>
						<Item typeName="String" key="Title">通信保障及额外测试(CQT)</Item>
						<Item typeName="Int32" key="ProjId">16</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">投诉测试(CQT)</Item>
						<Item typeName="String" key="Title">投诉测试(CQT)</Item>
						<Item typeName="Int32" key="ProjId">18</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">宏基站验收测试(CQT)</Item>
						<Item typeName="String" key="Title">宏基站验收测试(CQT)</Item>
						<Item typeName="Int32" key="ProjId">20</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">室内覆盖验收测试</Item>
						<Item typeName="String" key="Title">室内覆盖验收测试</Item>
						<Item typeName="Int32" key="ProjId">21</Item>
					<Item typeName="IList" key="ChildFuncs"/>
				</Item>
				<Item typeName="IDictionary">
					<Item typeName="String" key="InnerName">TD测试(CQT)</Item>
						<Item typeName="String" key="Title">TD测试(CQT)</Item>
						<Item typeName="Int32" key="ProjId">6</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="InnerName">dt</Item>
				<Item typeName="String" key="Title">DT测试</Item>
				<Item typeName="Int32" key="ProjId">0</Item>
				<Item typeName="IList" key="ChildFuncs">
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">每月例行测试(DT)</Item>
						<Item typeName="String" key="Title">每月例行测试(DT)</Item>
						<Item typeName="Int32" key="ProjId">1</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">问题点复测(DT)</Item>
						<Item typeName="String" key="Title">问题点复测(DT)</Item>
						<Item typeName="Int32" key="ProjId">3</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">变频项目(DT)</Item>
						<Item typeName="String" key="Title">变频项目(DT)</Item>
						<Item typeName="Int32" key="ProjId">7</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">省公司巡检数据(DT)</Item>
						<Item typeName="String" key="Title">省公司巡检数据(DT)</Item>
						<Item typeName="Int32" key="ProjId">13</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">通信保障及额外测试(DT)</Item>
						<Item typeName="String" key="Title">通信保障及额外测试(DT)</Item>
						<Item typeName="Int32" key="ProjId">15</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">投诉测试(DT)</Item>
						<Item typeName="String" key="Title">投诉测试(DT)</Item>
						<Item typeName="Int32" key="ProjId">17</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">宏基站验收测试(DT)</Item>
						<Item typeName="String" key="Title">宏基站验收测试(DT)</Item>
						<Item typeName="Int32" key="ProjId">19</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="InnerName">TD测试(DT)</Item>
						<Item typeName="String" key="Title">TD测试(DT)</Item>
						<Item typeName="Int32" key="ProjId">5</Item>
						<Item typeName="IList" key="ChildFuncs"/>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
</Configs>
