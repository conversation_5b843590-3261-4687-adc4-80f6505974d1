﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellOccupyQueryByRegion_W : ZTDIYGSMCellOccupyQueryByRegion
    {
        private static ZTDIYCellOccupyQueryByRegion_W intance = null;
        public new static ZTDIYCellOccupyQueryByRegion_W GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYCellOccupyQueryByRegion_W();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYCellOccupyQueryByRegion_W()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.WCDMA_VOICE);
            ServiceTypes.Add(ServiceType.WCDMA_DATA);
            statEventIDs.Clear();
            statEventIDs.AddRange(new List<int> { 537, 539, 579, 581, 542, 545, 548, 551 });
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }

        public override string Name
        {
            get { return "短时占用小区查询_W"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14034, this.Name);
        }

        protected override int? GetLac(TestPoint tp)
        {
            return (int?)tp["W_SysLAI"];
        }

        protected override int? GetCi(TestPoint tp)
        {
            return (int?)tp["W_SysCellID"];
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.WCDMA业务专题; }
        }
        #endregion
    }
}
