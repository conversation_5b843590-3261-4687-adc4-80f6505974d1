﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateRTPointsDataGridForm : CreateChildForm
    {
        public CreateRTPointsDataGridForm(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建参数信息窗口 RTPointsDataGridForm ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20017, this.Name);
        }
        public override string Name
        {
            get
            {
                return "创建参数信息窗口";
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.RTPointsDataGridForm";
            actionParam["Text"] = "参数信息窗口";
            actionParam["ImageFilePath"] = @"images\frame_info.gif";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
