﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WCDMAScanNonconformityForm : MinCloseForm
    {
        double nonconformityTpPctThreshold = 0.2;
        List<ZTDIYWCDMANonconformity.CellData> nonconformityCellData = null;
        public WCDMAScanNonconformityForm()
            : base()
        {
            InitializeComponent();
            if (cbxIsIndoor.Items.Count >= 3)
                cbxIsIndoor.SelectedIndex = 0;
            listView.ListViewItemSorter = new ListViewSorter(listView);
        }
        public void FillData(List<ZTDIYWCDMANonconformity.CellData> nonconformityCellData, string strIsIndoor)
        {
            if (nonconformityCellData == null)
            {
                return;
            }
            foreach (ZTDIYWCDMANonconformity.CellData item in nonconformityCellData)
            {
                item.CalcWrongDir();
            }
            this.nonconformityCellData = nonconformityCellData;

            List<string> highlightCellList = new List<string>();
            listView.Items.Clear();
            int count = 1;

            if (nonconformityCellData.Count == 0)
                return;

            foreach (ZTDIYWCDMANonconformity.CellData cd in nonconformityCellData)
            {
                if (cd.wCell != null) //使用系统小区工参
                {
                    string inoutdoor = getDoorType(cd);
                    if (cbxIsIndoor.SelectedItem.ToString() != "全部" && inoutdoor != strIsIndoor)
                        continue;
                    count = dealWCellInfo(highlightCellList, count, cd, inoutdoor);
                }
                else if (cd.dcell != null)
                {
                    if (cbxIsIndoor.SelectedItem.ToString() != "全部" && cd.dcell.Indoor.ToString() != strIsIndoor)
                        continue;
                    count = dealDefineCellInfo(highlightCellList, count, cd);
                }
            }
            MainModel.MainForm.GetMapForm().GetTDCellLayer().HighlightCellList = highlightCellList;
        }

        private void refreshHighlightCell(string strIsIndoor)
        {
            List<string> highlightCellList = new List<string>();
            MainModel.MainForm.GetMapForm().GetTDCellLayer().DrawNonconformityCell = true;
            nonconformityTpPctThreshold = 0.01 * (double)numUPPct.Value;
            listView.Items.Clear();
            int count = 1;
            foreach (ZTDIYWCDMANonconformity.CellData cd in this.nonconformityCellData)
            {
                if (cd.wCell != null) //使用系统小区工参
                {
                    string inoutdoor = getDoorType(cd);
                    if (cbxIsIndoor.SelectedItem.ToString() != "全部" && inoutdoor != strIsIndoor)
                        continue;
                    count = dealWCellInfo(highlightCellList, count, cd, inoutdoor);
                }
                else if (cd.dcell != null)
                {
                    if (cbxIsIndoor.SelectedItem.ToString() != "全部" && cd.dcell.Indoor.ToString() != strIsIndoor)
                        continue;
                    count = dealDefineCellInfo(highlightCellList, count, cd);
                }
            }
            MainModel.MainForm.GetMapForm().GetTDCellLayer().HighlightCellList = highlightCellList;
            MainModel.MainForm.GetMapForm().GetTDCellLayer().Invalidate();
        }

        private string getDoorType(ZTDIYWCDMANonconformity.CellData cd)
        {
            string inoutdoor = "";
            if (cd.wCell.Type == WNodeBType.Indoor)
                inoutdoor = "是";
            else if (cd.wCell.Type == WNodeBType.Outdoor)
                inoutdoor = "否";
            return inoutdoor;
        }

        private int dealWCellInfo(List<string> highlightCellList, int count, ZTDIYWCDMANonconformity.CellData cd, string inoutdoor)
        {
            ListViewItem item = new ListViewItem();
            item.Tag = cd;

            item.Text = count.ToString();
            item.SubItems.Add(cd.wCell.Name);
            item.SubItems.Add(cd.wCell.LAC + "_" + cd.wCell.CI);
            item.SubItems.Add(cd.wCell.UARFCN.ToString());
            item.SubItems.Add(cd.wCell.PSC.ToString());
            item.SubItems.Add(cd.wCell.Longitude.ToString());
            item.SubItems.Add(cd.wCell.Latitude.ToString());
            item.SubItems.Add(cd.wCell.Direction.ToString());
            item.SubItems.Add(cd.WrongDirMean.ToString());
            item.SubItems.Add(cd.DirDiff.ToString());
            item.SubItems.Add(cd.nonconformityTpList.Count.ToString());
            item.SubItems.Add((cd.countTpAll - cd.nonconformityTpList.Count).ToString());
            item.SubItems.Add(Math.Round(100 * ((double)cd.nonconformityTpList.Count / (double)cd.countTpAll), 2) + "%");
            if (((double)cd.nonconformityTpList.Count / (double)cd.countTpAll) > nonconformityTpPctThreshold)
            {
                highlightCellList.Add(cd.wCell.Name);

                item.SubItems.Add(cd.AvgDistance.ToString());
                item.SubItems.Add(cd.closestDistance.ToString());
                item.SubItems.Add(cd.farestDistance.ToString());
                item.SubItems.Add(inoutdoor);

                listView.Items.Add(item);
                count++;
            }

            return count;
        }

        private int dealDefineCellInfo(List<string> highlightCellList, int count, ZTDIYWCDMANonconformity.CellData cd)
        {
            ListViewItem item = new ListViewItem();
            item.Tag = cd;

            item.Text = count.ToString();
            item.SubItems.Add(cd.dcell.Cellname);
            item.SubItems.Add(cd.dcell.Lac + "_" + cd.dcell.Ci);
            item.SubItems.Add(cd.dcell.Freq.ToString());
            item.SubItems.Add(cd.dcell.Cpi.ToString());
            item.SubItems.Add(cd.dcell.Longitude.ToString());
            item.SubItems.Add(cd.dcell.Latitude.ToString());
            item.SubItems.Add(cd.dcell.Direction.ToString());
            item.SubItems.Add(cd.WrongDirMean.ToString());
            item.SubItems.Add(cd.DirDiff.ToString());
            item.SubItems.Add(cd.nonconformityTpList.Count.ToString());
            item.SubItems.Add((cd.countTpAll - cd.nonconformityTpList.Count).ToString());
            item.SubItems.Add(Math.Round(100 * ((double)cd.nonconformityTpList.Count / (double)cd.countTpAll), 2) + "%");
            if (((double)cd.nonconformityTpList.Count / (double)cd.countTpAll) > nonconformityTpPctThreshold)
            {
                highlightCellList.Add(cd.dcell.Cellname);

                item.SubItems.Add(cd.AvgDistance.ToString());
                item.SubItems.Add(cd.closestDistance.ToString());
                item.SubItems.Add(cd.farestDistance.ToString());
                item.SubItems.Add(cd.dcell.Indoor.ToString());

                listView.Items.Add(item);
                count++;
            }

            return count;
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(listView);
        }

        private void listView_DoubleClick(object sender, EventArgs e)
        {
            MainModel.MainForm.GetMapForm().GetTDCellLayer().DrawCellLabel = true;
            MainModel.MainForm.GetMapForm().GetTDCellLayer().DrawCellName = true;

            ZTDIYWCDMANonconformity.CellData selCd = listView.SelectedItems[0].Tag as ZTDIYWCDMANonconformity.CellData;

            MainModel.ClearDTData();
            MainModel.SelTDCellPairDic.Clear();
            MainModel.SelDCellPairDic.Clear();
            if (selCd.wCell != null)
            {
                foreach (TestPoint tp in selCd.nonconformityTpList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.SelWCellPairDic.Add(selCd.wCell, selCd.nonconformityTpList);
            }
            else if (selCd.dcell!=null)
            {
                foreach (TestPoint tp in selCd.nonconformityTpList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.SelWDCellPairDic.Add(selCd.dcell, selCd.nonconformityTpList);
            }
            MainModel.MainForm.GetMapForm().GetTDCellLayer().DrawSelTDCellPair = true;
            MainModel.MainForm.GetMapForm().GetTDCellLayer().Invalidate();
            MainModel.FireDTDataChanged(this);

            if (selCd.wCell != null)
                GoToView(selCd.nonconformityTpList, selCd.wCell.Longitude, selCd.wCell.Latitude);
            else if (selCd.dcell != null)
                GoToView(selCd.nonconformityTpList, selCd.dcell.Longitude, selCd.dcell.Latitude);
        }

        private void GoToView(List<TestPoint> tpList, double longitude,double latitude)
        {
            double ltLong = 100000;
            double ltLat = -100000;
            double brLong = -100000;
            double brLat = 100000;

            foreach (TestPoint tp in tpList)
            {
                if (tp.Longitude < ltLong)
                {
                    ltLong = tp.Longitude;
                }
                if (tp.Longitude > brLong)
                {
                    brLong = tp.Longitude;
                }
                if (tp.Latitude < brLat)
                {
                    brLat = tp.Latitude;
                }
                if (tp.Latitude > ltLat)
                {
                    ltLat = tp.Latitude;
                }
            }

            if (longitude < ltLong)
            {
                ltLong = longitude;
            }
            if (longitude > brLong)
            {
                brLong = longitude;
            }
            if (latitude < brLat)
            {
                brLat = latitude;
            }
            if (latitude > ltLat)
            {
                ltLat = latitude;
            }

            MapForm mapform = MainModel.MainForm.GetMapForm();
            if (mapform != null)
            {
                mapform.GoToView(new DbRect(ltLong, ltLat, brLong, brLat));
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            string indoor = "";
            if (cbxIsIndoor.SelectedItem.ToString() == "非室分")
                indoor = "否";
            else if (cbxIsIndoor.SelectedItem.ToString() == "是室分")
                indoor = "是";
            else
                indoor = "全部";
            refreshHighlightCell(indoor);
        }

        private void cbxIsIndoor_SelectedIndexChanged(object sender, EventArgs e)
        {
            string indoor ="";
            if (cbxIsIndoor.SelectedItem.ToString() == "非室分")
                indoor = "否";
            else if (cbxIsIndoor.SelectedItem.ToString() == "是室分")
                indoor = "是";
            else
                indoor = "全部";
            FillData(this.nonconformityCellData, indoor);
        }
    }
}
