﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    /// <summary>
    /// 读取报表文件
    /// </summary>
    public class WirelessNetTestReport
    {
        private static WirelessNetTestReport instance = null;
        public static WirelessNetTestReport Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new WirelessNetTestReport();
                }
                return instance;
            }
        }

        public string CarSpeedFormula { get; } = "{(Lte_0875+Nr_80040006)*3600/(Lte_0874+Nr_80040005)}";

        public string ErrMsg { get; private set; }

        public Dictionary<FormulaType, List<ReportExcelStyle>> FormulaDic { get; set; }

        public Dictionary<FormulaType, Dictionary<string, bool>> ImgCodeTotalDic { get; set; }
        public Dictionary<FormulaType, Dictionary<int, bool>> EventIDTotalDic { get; set; }

        public bool LoadReport()
        {
            ImgCodeTotalDic = new Dictionary<FormulaType, Dictionary<string, bool>>()
            {
                { FormulaType.全部, new Dictionary<string, bool>() },
                { FormulaType.数据, new Dictionary<string, bool>() },
                { FormulaType.语音, new Dictionary<string, bool>() },
            };
            EventIDTotalDic = new Dictionary<FormulaType, Dictionary<int, bool>>()
            {
                { FormulaType.全部, new Dictionary<int, bool>() },
                { FormulaType.数据, new Dictionary<int, bool>() },
                { FormulaType.语音, new Dictionary<int, bool>() },
            };

            FormulaDic = loadReportExcel();
            if (FormulaDic.Count == 0)
            {
                ErrMsg = "没有读取到有效的公式信息";
                return false;
            }

            try
            {
                //foreach (var fromula in FormulaDic)
                //{
                //    GetKpiEverySingleFormula(fromula.Formula, fromula.EventIDTotalDic, fromula.ImgCodeTotalDic);

                //    var type = fromula.ServiceType;

                //    unionDic(EventIDTotalDic[type], fromula.EventIDTotalDic);
                //    unionDic(ImgCodeTotalDic[type], fromula.ImgCodeTotalDic);
                //}

                //addAllTotal(EventIDTotalDic);
                //addAllTotal(ImgCodeTotalDic);

                //unionDic(FormulaDic[FormulaType.数据], FormulaDic[FormulaType.全部]);
                //unionDic(FormulaDic[FormulaType.语音], FormulaDic[FormulaType.全部]);
            }
            catch (Exception e)
            {
                ErrMsg = e.Message;
            }

            if (!string.IsNullOrEmpty(ErrMsg))
            {
                return false;
            }

            return true;
        }

        #region 读取Excel文件
        private Dictionary<FormulaType, List<ReportExcelStyle>> loadReportExcel()
        {
            var serviceFormulaDic = new Dictionary<FormulaType, List<ReportExcelStyle>>()
            {
                { FormulaType.数据, new List<ReportExcelStyle>() },
                { FormulaType.语音, new List<ReportExcelStyle>() },
                { FormulaType.全部, new List<ReportExcelStyle>() },
            };

            var dic = new Dictionary<FormulaType, Dictionary<string, ReportExcelStyle>>();
            try
            {
                string fullPath = $@"{WirelessNetTestHelper.BasicPath}\报表.xlsx";
                if (File.Exists(fullPath))
                {
                    dic = ExcelHelper.ReadExcel<Dictionary<FormulaType, Dictionary<string, ReportExcelStyle>>>(fullPath, dealData);
                }
                else
                {
                    ErrMsg = $"{fullPath}文件不存在";
                }
            }
            catch (Exception e)
            {
                ErrMsg = "读取场景配置信息失败:" + e.Message;
            }

            if (!string.IsNullOrEmpty(ErrMsg))
            {
                return serviceFormulaDic;
            }

            var allFormulaDic = new Dictionary<string, ReportExcelStyle>();
            foreach (var service in dic)
            {
                var list = new List<ReportExcelStyle>(service.Value.Values);
                list.Sort((x, y) => x.Idx.CompareTo(y.Idx));
                serviceFormulaDic[service.Key] = list;

                foreach (var item in service.Value)
                {
                    if (!allFormulaDic.TryGetValue(item.Key, out _))
                    {
                        allFormulaDic.Add(item.Key, item.Value);
                    }
                }
            }
            var allFormula = new List<ReportExcelStyle>(allFormulaDic.Values);
            allFormula.Sort((x, y) => x.Idx.CompareTo(y.Idx));
            serviceFormulaDic[FormulaType.全部] = allFormula;

            return serviceFormulaDic;
        }

        private void dealData(Dictionary<FormulaType, Dictionary<string, ReportExcelStyle>> dic, DataRow dr)
        {
            ReportExcelStyle data = new ReportExcelStyle();
            ErrMsg = data.FillData(dr);

            //if (!dic.TryGetValue(data.NetType, out var serviceFormulaDic))
            //{
            //    serviceFormulaDic = new Dictionary<FormulaType, Dictionary<string, ReportExcelStyle>>();
            //    dic.Add(data.NetType, serviceFormulaDic);
            //}
            if (!dic.TryGetValue(data.ServiceType, out var formulaDic))
            {
                formulaDic = new Dictionary<string, ReportExcelStyle>();
                dic.Add(data.ServiceType, formulaDic);
            }
            if (!formulaDic.ContainsKey(data.FormulaName))
            {
                formulaDic.Add(data.FormulaName, data);
            }
        }
        #endregion

        #region 解析公式用到的指标
        public void GetKpiEverySingleFormula(string formula
            , Dictionary<int, bool> eventIDTotalDic
            , Dictionary<string, bool> imgCodeTotalDic)
        {
            Dictionary<string, bool> elements = extractStatElement(formula);
            foreach (string str in elements.Keys)
            {
                bool isEvt = delEvent(str, eventIDTotalDic);
                if (!isEvt)
                {
                    dealImage(str, imgCodeTotalDic);
                }
            }
        }

        private bool delEvent(string str, Dictionary<int, bool> eventIDTotalDic)
        {
            int lIdx = str.IndexOf("[");
            int rIdx = str.IndexOf("]");
            if (lIdx != -1 && rIdx != -1 && rIdx > lIdx)
            {//事件公式
                string evtId = str.Substring(lIdx + 1, rIdx - (lIdx + 1));
                int id;
                if (int.TryParse(evtId, out id))
                {
                    eventIDTotalDic[id + 1] = true;
                    return true;
                }
            }
            return false;
        }

        private void dealImage(string str, Dictionary<string, bool> imgCodeTotalDic)
        {
            string imgCode = str;
            int splitIdx = str.IndexOf('_');
            if (splitIdx != -1)
            {
                imgCode = str.Substring(splitIdx + 1);
            }
            if (imgCode == string.Empty)
            {
                return;
            }

            StatImgDefItem defItem = InterfaceManager.GetInstance().GetStatImgDef(imgCode);
            if (defItem != null)
            {
                imgCodeTotalDic[imgCode] = true;
                if (!string.IsNullOrEmpty(defItem.bak))
                {
                    StatImgDefItem otherDefItem = InterfaceManager.GetInstance().GetStatImgDef(defItem.bak);
                    if (otherDefItem != null)
                    {
                        imgCodeTotalDic[defItem.bak] = true;
                    }
                }
            }
        }

        private Dictionary<string, bool> extractStatElement(string formula)
        {
            Dictionary<string, bool> elements = new Dictionary<string, bool>();
            int length = formula.Length;
            int start = 0;
            int end = 0;
            while (end < length)
            {
                string str = getElementStrFrom(formula, start, ref end);
                if (!string.IsNullOrEmpty(str))
                    elements[str] = true;
                else
                    end++;
                start = end;
            }
            return elements;
        }

        private string getElementStrFrom(string formula, int pos, ref int end)
        {
            if (pos < 0 || pos > formula.Length - 1)
            {
                return string.Empty;
            }
            int start = pos;
            end = pos;
            while (start >= 0)//向前找
            {
                char ch = formula[start];
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/' || ch == ' ' || ch == '{' || ch == '}' || ch == '(' || ch == ')')
                {
                    break;
                }
                start--;
            }
            while (end <= formula.Length - 1)//向后找
            {
                char ch = formula[end];
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/' || ch == ' ' || ch == '{' || ch == '}' || ch == '(' || ch == ')')
                {
                    break;
                }
                end++;
            }
            if (end <= formula.Length && end > start)
            {
                return formula.Substring(start + 1, end - start - 1);
            }
            return string.Empty;
        }
        #endregion

        #region 统计用到的全部指标
        private void unionDic<U, V>(Dictionary<U, V> sumDic, Dictionary<U, V> dic)
        {
            foreach (var data in dic)
            {
                if (!sumDic.TryGetValue(data.Key, out _))
                {
                    sumDic.Add(data.Key, data.Value);
                }
            }
        }

        private void addAllTotal<T>(Dictionary<FormulaType, Dictionary<T, bool>> dic)
        {
            addAll(dic[FormulaType.数据], dic[FormulaType.全部]);
            addAll(dic[FormulaType.语音], dic[FormulaType.全部]);
        }

        private void addAll<T>(Dictionary<T, bool> dic, Dictionary<T, bool> allDic)
        {
            foreach (var item in dic)
            {
                if (!allDic.TryGetValue(item.Key, out _))
                {
                    allDic.Add(item.Key, item.Value);
                }
            }
        }
        #endregion

        public string GetCarSpeed(KPI_Statistics.StatDataHubBase statData)
        {
            var carSpeed = statData.CalcValueByFormula(CarSpeedFormula);
            if (carSpeed == 0)
            {
                return "";
            }
            carSpeed = Math.Round(carSpeed, 2);
            return carSpeed.ToString();
        }

        public bool AddFormulaTableColumns(DataTable dt)
        {
            if (!FormulaDic.TryGetValue(FormulaType.全部, out var allFormulaDic))
            {
                return false;
            }
            //移动电信分成2行数据保存,导出时再按照其他格式保存
            //由于读取公式时已经按指标顺序字段排过序了,所以这里直接添加
            foreach (var formula in allFormulaDic)
            {
                dt.Columns.Add(formula.FormulaName);
            }
            return true;
        }
    }

    public class ReportExcelStyle
    {
        public int Idx { get; set; }
        //public NetType NetType { get; set; } = NetType.UNKNOWN;
        public string FormulaName { get; set; }
        public string Formula { get; set; }
        //public string DataType { get; set; }
        public int DeciNum { get; set; }
        public FormulaType ServiceType { get; set; }

        public Dictionary<string, bool> ImgCodeTotalDic { get; set; } = new Dictionary<string, bool>();
        public Dictionary<int, bool> EventIDTotalDic { get; set; } = new Dictionary<int, bool>();

        public string FillData(DataRow dr)
        {
            try
            {
                Idx = Convert.ToInt32(dr["序号"]);
                //string net = dr["网络类型"].ToString().Trim();
                //NetType = WirelessNetTestHelper.Instance.GetValidEnum(net, NetType.UNKNOWN);
                FormulaName = dr["公式名"].ToString().Trim();
                Formula = dr["公式"].ToString().Replace(" ", "");
                //DataType = dr["数据类型"].ToString().Trim();
                string serviceType = dr["业务类型"].ToString().Trim();
                ServiceType = WirelessNetTestHelper.Instance.GetValidEnum(serviceType, FormulaType.全部);
            }
            catch (Exception e)
            {
                return $"序号{Idx}行数据解析出错:{e.Message}";
            }
            return "";
        }

        public override string ToString()
        {
            return FormulaName;
        }
    }
}
