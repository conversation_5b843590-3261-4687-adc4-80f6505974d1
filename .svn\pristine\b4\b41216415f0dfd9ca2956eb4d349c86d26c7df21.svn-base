﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRCellSetByStreetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gcEarfcn = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gvEarfcn = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gcCell = new DevExpress.XtraGrid.GridControl();
            this.gvCell = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.gcBTS = new DevExpress.XtraGrid.GridControl();
            this.gvBTS = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.gcUnusedCell = new DevExpress.XtraGrid.GridControl();
            this.gvUnusedCell = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.gcNbCell = new DevExpress.XtraGrid.GridControl();
            this.gvNbCell = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn59 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage6 = new DevExpress.XtraTab.XtraTabPage();
            this.gcSNCell = new DevExpress.XtraGrid.GridControl();
            this.gvSNCell = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn64 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn65 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn66 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn67 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn68 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.panel1 = new System.Windows.Forms.Panel();
            ((System.ComponentModel.ISupportInitialize)(this.gcEarfcn)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvEarfcn)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCell)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcBTS)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvBTS)).BeginInit();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcUnusedCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvUnusedCell)).BeginInit();
            this.xtraTabPage5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcNbCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvNbCell)).BeginInit();
            this.xtraTabPage6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcSNCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSNCell)).BeginInit();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // gcEarfcn
            // 
            this.gcEarfcn.ContextMenuStrip = this.contextMenuStrip;
            this.gcEarfcn.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcEarfcn.Location = new System.Drawing.Point(0, 0);
            this.gcEarfcn.MainView = this.gvEarfcn;
            this.gcEarfcn.Name = "gcEarfcn";
            this.gcEarfcn.Size = new System.Drawing.Size(970, 420);
            this.gcEarfcn.TabIndex = 1;
            this.gcEarfcn.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvEarfcn});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // gvEarfcn
            // 
            this.gvEarfcn.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8});
            this.gvEarfcn.GridControl = this.gcEarfcn;
            this.gvEarfcn.Name = "gvEarfcn";
            this.gvEarfcn.OptionsBehavior.Editable = false;
            this.gvEarfcn.OptionsCustomization.AllowFilter = false;
            this.gvEarfcn.OptionsSelection.MultiSelect = true;
            this.gvEarfcn.OptionsView.ColumnAutoWidth = false;
            this.gvEarfcn.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "数据区域";
            this.gridColumn2.FieldName = "RegionName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 0;
            this.gridColumn2.Width = 120;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "小区类型";
            this.gridColumn3.FieldName = "CellType";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 1;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "频点";
            this.gridColumn4.FieldName = "EARFCN";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 2;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "小区数";
            this.gridColumn5.FieldName = "CellCount";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 3;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "小区占比";
            this.gridColumn6.DisplayFormat.FormatString = "P2";
            this.gridColumn6.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn6.FieldName = "CellProportion";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 4;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "采样点数";
            this.gridColumn7.FieldName = "TPCount";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 5;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "采样点占比";
            this.gridColumn8.DisplayFormat.FormatString = "P2";
            this.gridColumn8.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn8.FieldName = "TPProportion";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 6;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.ContextMenuStrip = this.contextMenuStrip;
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(977, 450);
            this.xtraTabControl1.TabIndex = 2;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3,
            this.xtraTabPage4,
            this.xtraTabPage5,
            this.xtraTabPage6});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gcEarfcn);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(970, 420);
            this.xtraTabPage1.Text = "覆盖频点";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gcCell);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(970, 420);
            this.xtraTabPage2.Text = "覆盖小区";
            // 
            // gcCell
            // 
            this.gcCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcCell.Location = new System.Drawing.Point(0, 0);
            this.gcCell.MainView = this.gvCell;
            this.gcCell.Name = "gcCell";
            this.gcCell.Size = new System.Drawing.Size(970, 420);
            this.gcCell.TabIndex = 2;
            this.gcCell.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvCell});
            // 
            // gvCell
            // 
            this.gvCell.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn9,
            this.gridColumn28});
            this.gvCell.GridControl = this.gcCell;
            this.gvCell.Name = "gvCell";
            this.gvCell.OptionsBehavior.Editable = false;
            this.gvCell.OptionsCustomization.AllowFilter = false;
            this.gvCell.OptionsSelection.MultiSelect = true;
            this.gvCell.OptionsView.ColumnAutoWidth = false;
            this.gvCell.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "数据区域";
            this.gridColumn10.FieldName = "RegionName";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 0;
            this.gridColumn10.Width = 120;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "小区名称";
            this.gridColumn11.FieldName = "Stater.CellName";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 1;
            this.gridColumn11.Width = 150;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "频点";
            this.gridColumn12.FieldName = "Stater.EARFCN";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 2;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "PCI";
            this.gridColumn13.FieldName = "Stater.PCI";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 3;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "小区类型";
            this.gridColumn14.FieldName = "CellType";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 6;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "采样点数";
            this.gridColumn15.FieldName = "Stater.TPCount";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 7;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "采样点占比";
            this.gridColumn16.DisplayFormat.FormatString = "P2";
            this.gridColumn16.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn16.FieldName = "Stater.TPProportion";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 8;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "最小场强";
            this.gridColumn17.FieldName = "Stater.RsrpDataSub.Min";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 9;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "最大场强";
            this.gridColumn18.FieldName = "Stater.RsrpDataSub.Max";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 10;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "平均场强";
            this.gridColumn19.FieldName = "Stater.RsrpDataSub.Avg";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 11;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "最小质量";
            this.gridColumn20.FieldName = "Stater.SinrDataSub.Min";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 12;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "最大质量";
            this.gridColumn21.FieldName = "Stater.SinrDataSub.Max";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 13;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "平均质量";
            this.gridColumn22.FieldName = "Stater.SinrDataSub.Avg";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 14;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "最小距离";
            this.gridColumn23.FieldName = "Stater.DistanceDataSub.Min";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 15;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "最大距离";
            this.gridColumn24.FieldName = "Stater.DistanceDataSub.Max";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 16;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "平均距离";
            this.gridColumn25.FieldName = "Stater.DistanceDataSub.Avg";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 17;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "经度";
            this.gridColumn26.FieldName = "Stater.Cell.Longitude";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 18;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "纬度";
            this.gridColumn27.FieldName = "Stater.Cell.Latitude";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 19;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "TAC";
            this.gridColumn9.FieldName = "Stater.TAC";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 4;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "NCI";
            this.gridColumn28.FieldName = "Stater.NCI";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 5;
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.gcBTS);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(970, 420);
            this.xtraTabPage3.Text = "覆盖基站";
            // 
            // gcBTS
            // 
            this.gcBTS.ContextMenuStrip = this.contextMenuStrip;
            this.gcBTS.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcBTS.Location = new System.Drawing.Point(0, 0);
            this.gcBTS.MainView = this.gvBTS;
            this.gcBTS.Name = "gcBTS";
            this.gcBTS.Size = new System.Drawing.Size(970, 420);
            this.gcBTS.TabIndex = 2;
            this.gcBTS.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvBTS});
            // 
            // gvBTS
            // 
            this.gvBTS.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44});
            this.gvBTS.GridControl = this.gcBTS;
            this.gvBTS.Name = "gvBTS";
            this.gvBTS.OptionsBehavior.Editable = false;
            this.gvBTS.OptionsCustomization.AllowFilter = false;
            this.gvBTS.OptionsSelection.MultiSelect = true;
            this.gvBTS.OptionsView.ColumnAutoWidth = false;
            this.gvBTS.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "数据区域";
            this.gridColumn29.FieldName = "RegionName";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 0;
            this.gridColumn29.Width = 120;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "基站名称";
            this.gridColumn30.FieldName = "Stater.BTSName";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 1;
            this.gridColumn30.Width = 150;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "基站类型";
            this.gridColumn31.FieldName = "Type";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 2;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "采样点数";
            this.gridColumn32.FieldName = "Stater.TPCount";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 3;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "采样点占比";
            this.gridColumn33.DisplayFormat.FormatString = "P2";
            this.gridColumn33.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn33.FieldName = "Stater.TPProportion";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 4;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "最小场强";
            this.gridColumn34.FieldName = "Stater.RsrpDataSub.Min";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 5;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "最大场强";
            this.gridColumn35.FieldName = "Stater.RsrpDataSub.Max";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 6;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "平均场强";
            this.gridColumn36.FieldName = "Stater.RsrpDataSub.Avg";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 7;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "最小质量";
            this.gridColumn37.FieldName = "Stater.SinrDataSub.Min";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 8;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "最大质量";
            this.gridColumn38.FieldName = "Stater.SinrDataSub.Max";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 9;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "平均质量";
            this.gridColumn39.FieldName = "Stater.SinrDataSub.Avg";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 10;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "最小距离";
            this.gridColumn40.FieldName = "Stater.DistanceDataSub.Min";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 11;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "最大距离";
            this.gridColumn41.FieldName = "Stater.DistanceDataSub.Max";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 12;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "平均距离";
            this.gridColumn42.FieldName = "Stater.DistanceDataSub.Avg";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 13;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "经度";
            this.gridColumn43.FieldName = "Stater.BTS.Longitude";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 14;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "纬度";
            this.gridColumn44.FieldName = "Stater.BTS.Latitude";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 15;
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.gcUnusedCell);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(970, 420);
            this.xtraTabPage4.Text = "区域内未使用小区";
            // 
            // gcUnusedCell
            // 
            this.gcUnusedCell.ContextMenuStrip = this.contextMenuStrip;
            this.gcUnusedCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcUnusedCell.Location = new System.Drawing.Point(0, 0);
            this.gcUnusedCell.MainView = this.gvUnusedCell;
            this.gcUnusedCell.Name = "gcUnusedCell";
            this.gcUnusedCell.Size = new System.Drawing.Size(970, 420);
            this.gcUnusedCell.TabIndex = 2;
            this.gcUnusedCell.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvUnusedCell});
            // 
            // gvUnusedCell
            // 
            this.gvUnusedCell.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn46,
            this.gridColumn47,
            this.gridColumn48,
            this.gridColumn49,
            this.gridColumn50,
            this.gridColumn45,
            this.gridColumn51});
            this.gvUnusedCell.GridControl = this.gcUnusedCell;
            this.gvUnusedCell.Name = "gvUnusedCell";
            this.gvUnusedCell.OptionsBehavior.Editable = false;
            this.gvUnusedCell.OptionsCustomization.AllowFilter = false;
            this.gvUnusedCell.OptionsSelection.MultiSelect = true;
            this.gvUnusedCell.OptionsView.ColumnAutoWidth = false;
            this.gvUnusedCell.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "数据区域";
            this.gridColumn46.FieldName = "RegionName";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 0;
            this.gridColumn46.Width = 120;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "小区名称";
            this.gridColumn47.FieldName = "CellName";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 1;
            this.gridColumn47.Width = 150;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "频点";
            this.gridColumn48.FieldName = "EARFCN";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 2;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "PCI";
            this.gridColumn49.FieldName = "PCI";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 3;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "小区类型";
            this.gridColumn50.FieldName = "CellType";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 6;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "TAC";
            this.gridColumn45.FieldName = "TAC";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 4;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "NCI";
            this.gridColumn51.FieldName = "NCI";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 5;
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.gcNbCell);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.Size = new System.Drawing.Size(970, 420);
            this.xtraTabPage5.Text = "邻区中小区";
            // 
            // gcNbCell
            // 
            this.gcNbCell.ContextMenuStrip = this.contextMenuStrip;
            this.gcNbCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcNbCell.Location = new System.Drawing.Point(0, 0);
            this.gcNbCell.MainView = this.gvNbCell;
            this.gcNbCell.Name = "gcNbCell";
            this.gcNbCell.Size = new System.Drawing.Size(970, 420);
            this.gcNbCell.TabIndex = 2;
            this.gcNbCell.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvNbCell});
            // 
            // gvNbCell
            // 
            this.gvNbCell.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn52,
            this.gridColumn53,
            this.gridColumn54,
            this.gridColumn55,
            this.gridColumn56,
            this.gridColumn57,
            this.gridColumn58,
            this.gridColumn59,
            this.gridColumn62});
            this.gvNbCell.GridControl = this.gcNbCell;
            this.gvNbCell.Name = "gvNbCell";
            this.gvNbCell.OptionsBehavior.Editable = false;
            this.gvNbCell.OptionsCustomization.AllowFilter = false;
            this.gvNbCell.OptionsSelection.MultiSelect = true;
            this.gvNbCell.OptionsView.ColumnAutoWidth = false;
            this.gvNbCell.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "数据区域";
            this.gridColumn52.FieldName = "RegionName";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 0;
            this.gridColumn52.Width = 120;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "小区名称";
            this.gridColumn53.FieldName = "CellName";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 1;
            this.gridColumn53.Width = 150;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "频点";
            this.gridColumn54.FieldName = "EARFCN";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 2;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "PCI";
            this.gridColumn55.FieldName = "PCI";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 3;
            // 
            // gridColumn56
            // 
            this.gridColumn56.Caption = "小区类型";
            this.gridColumn56.FieldName = "CellType";
            this.gridColumn56.Name = "gridColumn56";
            this.gridColumn56.Visible = true;
            this.gridColumn56.VisibleIndex = 6;
            // 
            // gridColumn57
            // 
            this.gridColumn57.Caption = "出现次数";
            this.gridColumn57.FieldName = "Count";
            this.gridColumn57.Name = "gridColumn57";
            this.gridColumn57.Visible = true;
            this.gridColumn57.VisibleIndex = 7;
            // 
            // gridColumn58
            // 
            this.gridColumn58.Caption = "平均场强";
            this.gridColumn58.FieldName = "RsrpAvg";
            this.gridColumn58.Name = "gridColumn58";
            this.gridColumn58.Visible = true;
            this.gridColumn58.VisibleIndex = 8;
            // 
            // gridColumn59
            // 
            this.gridColumn59.Caption = "TAC";
            this.gridColumn59.FieldName = "TAC";
            this.gridColumn59.Name = "gridColumn59";
            this.gridColumn59.Visible = true;
            this.gridColumn59.VisibleIndex = 4;
            // 
            // gridColumn62
            // 
            this.gridColumn62.Caption = "NCI";
            this.gridColumn62.FieldName = "NCI";
            this.gridColumn62.Name = "gridColumn62";
            this.gridColumn62.Visible = true;
            this.gridColumn62.VisibleIndex = 5;
            // 
            // xtraTabPage6
            // 
            this.xtraTabPage6.Controls.Add(this.gcSNCell);
            this.xtraTabPage6.Name = "xtraTabPage6";
            this.xtraTabPage6.Size = new System.Drawing.Size(970, 420);
            this.xtraTabPage6.Text = "主服及邻区汇总";
            // 
            // gcSNCell
            // 
            this.gcSNCell.ContextMenuStrip = this.contextMenuStrip;
            this.gcSNCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcSNCell.Location = new System.Drawing.Point(0, 0);
            this.gcSNCell.MainView = this.gvSNCell;
            this.gcSNCell.Name = "gcSNCell";
            this.gcSNCell.Size = new System.Drawing.Size(970, 420);
            this.gcSNCell.TabIndex = 2;
            this.gcSNCell.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvSNCell});
            // 
            // gvSNCell
            // 
            this.gvSNCell.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn60,
            this.gridColumn61,
            this.gridColumn63,
            this.gridColumn64,
            this.gridColumn65,
            this.gridColumn66,
            this.gridColumn1,
            this.gridColumn67,
            this.gridColumn68});
            this.gvSNCell.GridControl = this.gcSNCell;
            this.gvSNCell.Name = "gvSNCell";
            this.gvSNCell.OptionsBehavior.Editable = false;
            this.gvSNCell.OptionsCustomization.AllowFilter = false;
            this.gvSNCell.OptionsSelection.MultiSelect = true;
            this.gvSNCell.OptionsView.ColumnAutoWidth = false;
            this.gvSNCell.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn60
            // 
            this.gridColumn60.Caption = "数据区域";
            this.gridColumn60.FieldName = "RegionName";
            this.gridColumn60.Name = "gridColumn60";
            this.gridColumn60.Visible = true;
            this.gridColumn60.VisibleIndex = 0;
            this.gridColumn60.Width = 120;
            // 
            // gridColumn61
            // 
            this.gridColumn61.Caption = "小区名称";
            this.gridColumn61.FieldName = "CellName";
            this.gridColumn61.Name = "gridColumn61";
            this.gridColumn61.Visible = true;
            this.gridColumn61.VisibleIndex = 1;
            this.gridColumn61.Width = 150;
            // 
            // gridColumn63
            // 
            this.gridColumn63.Caption = "小区类型";
            this.gridColumn63.FieldName = "CellType";
            this.gridColumn63.Name = "gridColumn63";
            this.gridColumn63.Visible = true;
            this.gridColumn63.VisibleIndex = 6;
            // 
            // gridColumn64
            // 
            this.gridColumn64.Caption = "频点";
            this.gridColumn64.FieldName = "EARFCN";
            this.gridColumn64.Name = "gridColumn64";
            this.gridColumn64.Visible = true;
            this.gridColumn64.VisibleIndex = 2;
            // 
            // gridColumn65
            // 
            this.gridColumn65.Caption = "PCI";
            this.gridColumn65.FieldName = "PCI";
            this.gridColumn65.Name = "gridColumn65";
            this.gridColumn65.Visible = true;
            this.gridColumn65.VisibleIndex = 3;
            // 
            // gridColumn66
            // 
            this.gridColumn66.Caption = "主服次数";
            this.gridColumn66.FieldName = "SCellCount";
            this.gridColumn66.Name = "gridColumn66";
            this.gridColumn66.Visible = true;
            this.gridColumn66.VisibleIndex = 7;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "邻区次数";
            this.gridColumn1.FieldName = "NCellCount";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 8;
            // 
            // gridColumn67
            // 
            this.gridColumn67.Caption = "TAC";
            this.gridColumn67.FieldName = "TAC";
            this.gridColumn67.Name = "gridColumn67";
            this.gridColumn67.Visible = true;
            this.gridColumn67.VisibleIndex = 4;
            // 
            // gridColumn68
            // 
            this.gridColumn68.Caption = "NCI";
            this.gridColumn68.FieldName = "NCI";
            this.gridColumn68.Name = "gridColumn68";
            this.gridColumn68.Visible = true;
            this.gridColumn68.VisibleIndex = 5;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.xtraTabControl1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(977, 450);
            this.panel1.TabIndex = 3;
            // 
            // NRCellSetByStreetForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(977, 450);
            this.Controls.Add(this.panel1);
            this.Name = "NRCellSetByStreetForm";
            this.Text = "道路小区集分析";
            ((System.ComponentModel.ISupportInitialize)(this.gcEarfcn)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvEarfcn)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCell)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcBTS)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvBTS)).EndInit();
            this.xtraTabPage4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcUnusedCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvUnusedCell)).EndInit();
            this.xtraTabPage5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcNbCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvNbCell)).EndInit();
            this.xtraTabPage6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcSNCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSNCell)).EndInit();
            this.panel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gcEarfcn;
        private DevExpress.XtraGrid.Views.Grid.GridView gvEarfcn;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gcCell;
        private DevExpress.XtraGrid.Views.Grid.GridView gvCell;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraGrid.GridControl gcBTS;
        private DevExpress.XtraGrid.Views.Grid.GridView gvBTS;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private DevExpress.XtraGrid.GridControl gcUnusedCell;
        private DevExpress.XtraGrid.Views.Grid.GridView gvUnusedCell;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private DevExpress.XtraGrid.GridControl gcNbCell;
        private DevExpress.XtraGrid.Views.Grid.GridView gvNbCell;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage6;
        private DevExpress.XtraGrid.GridControl gcSNCell;
        private DevExpress.XtraGrid.Views.Grid.GridView gvSNCell;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn64;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn65;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn66;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn59;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn67;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn68;
    }
}