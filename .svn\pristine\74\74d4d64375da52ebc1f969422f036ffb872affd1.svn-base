﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using System.Collections;
using MasterCom.MTGis;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Net;
namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDownloadSpeedStatFromDB : QueryBase
    {

        public ZTDownloadSpeedStatFromDB(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "优胜率统计(查询数据库历史数据)"; }
        }
        public override string IconName
        {
            get { return "Images/fileq.gif"; }
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22078, "查询");
        }

        #region 全局变量
        List<string> sqlMonthList = null;
        string strCityName = "";
        public double dPercent { get; set; } = 1.0;
        public double dSpeed { get; set; } = 25;
        public bool GetShowGridDataInfo { get; set; } = false;
        string strCarr = "移动VS联通";
        string strGridType = "";
        private List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoList = null;
        Dictionary<string, Dictionary<GridTypeName, GridMutCarriersCountInfo>> cityMutCarriersCountInfoDic = null;
        #endregion

        protected override void query()
        {
            if (!condition.CarrierTypes.Contains(1) || condition.CarrierTypes.Count != 2)
            {
                MessageBox.Show("运营商选择有误，请选择移动联通或者移动电信");
                return;
            }
            strCarr = "移动VS联通";
            if (condition.CarrierTypes.Contains(3))
            {
                strCarr = "移动VS电信";
            }
            sqlMonthList = new List<string>();
            gridDownLoadTimeSpeedInfoList = new List<GridDownLoadTimeSpeedInfo>();
            cityMutCarriersCountInfoDic = new Dictionary<string, Dictionary<GridTypeName, GridMutCarriersCountInfo>>();
            DateTime dBeginTime = condition.Periods[0].BeginTime;
            DateTime dEndTime = condition.Periods[0].EndTime.AddSeconds(-1);
            while (dBeginTime <= dEndTime)
            {
                string strMonth = dBeginTime.ToString("yyyyMM");
                if (!sqlMonthList.Contains(strMonth))
                {
                    sqlMonthList.Add(strMonth);
                }
                dBeginTime = dBeginTime.AddDays(1);
            }
            sqlMonthList.Sort();

            int iID = mainModel.DistrictID;
            mainModel.DistrictID = condition.DistrictIDs[0];
            List<string> gridTypeList = new List<string>();
            if (sqlMonthList.Count > 0)
            {
                DIYGridTypeFromDB diyQuery = new DIYGridTypeFromDB(mainModel, sqlMonthList[0], strCarr);
                diyQuery.Query();
                gridTypeList.AddRange(diyQuery.gridTypeList);
            }

            if (gridTypeList.Count == 0)
            {
                MessageBox.Show("该条件下无入库的信息，请检查条件！");
                return;
            }
            mainModel.DistrictID = iID;
            GridCompareCombieSetForm gridForm = new GridCompareCombieSetForm(true, false, true, gridTypeList);
            if (gridForm.ShowDialog() == DialogResult.OK)
            {
                dPercent = 1.0 * gridForm.GetPercent / 100;
                dSpeed = gridForm.GetSpeed;
                GetShowGridDataInfo = gridForm.GetIsShowGridDataInfo;
                strGridType = gridForm.GetGridType;
            }
            else
            {
                return;
            }
            try
            {
                WaitBox.Show("开始查询......", queryInThread);

                fireShowResult();
            }
            catch
            {
                //continue
            }
        }

        private void queryInThread()
        {
            int iID = mainModel.DistrictID;
            WaitBox.CanCancel = true;
            foreach (int iCity in condition.DistrictIDs)
            {
                Dictionary<string, Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>> monthGridDownInfoDic
                    = new Dictionary<string, Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>>();

                foreach (string strMonth in sqlMonthList)
                {
                    strCityName = DistrictManager.GetInstance().getDistrictName(iCity);
                    WaitBox.Text = "正在查询 " + strCityName + " " + strMonth + " 数据.......";
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    mainModel.DistrictID = iCity;
                    DIYSQLGridDownloadInfo diySQLGridDownloadInfo = new DIYSQLGridDownloadInfo(mainModel, strMonth, strCarr, strGridType);
                    diySQLGridDownloadInfo.Query();
                    if (!monthGridDownInfoDic.ContainsKey(strMonth))
                    {
                        monthGridDownInfoDic.Add(strMonth, diySQLGridDownloadInfo.gridDownLoadTimeSpeedInfoDic);
                    }
                }
                WaitBox.Text = strCityName + " 数据查询完毕，开始对比流程...........";
                doCompare(monthGridDownInfoDic);
            }
            mainModel.DistrictID = iID;
            WaitBox.Close();
        }

        #region 算法一
        protected void doCompare(Dictionary<string, Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>> monthGridDownInfoDic)
        {
            string strCurMonth = sqlMonthList[sqlMonthList.Count - 1];
            Dictionary<GridTypeName, GridMutCarriersCountInfo> gridMutCarriersCountInfoDic
                = new Dictionary<GridTypeName, GridMutCarriersCountInfo>();

            foreach (CenterLongLat cll in monthGridDownInfoDic[strCurMonth].Keys)
            {
                GridTypeName gridName = new GridTypeName();
                gridName.strGridType = monthGridDownInfoDic[strCurMonth][cll].StrGridType;
                gridName.strGridName = monthGridDownInfoDic[strCurMonth][cll].StrGridName;

                if (!gridMutCarriersCountInfoDic.ContainsKey(gridName))
                {
                    gridMutCarriersCountInfoDic[gridName] = new GridMutCarriersCountInfo();
                    gridMutCarriersCountInfoDic[gridName].StrCity = strCityName;
                    gridMutCarriersCountInfoDic[gridName].StrGridType = gridName.strGridType;
                    gridMutCarriersCountInfoDic[gridName].StrGridName = gridName.strGridName;
                    for (int i = sqlMonthList.Count - 1; i >= 0; i--)
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList.Add(0);
                        gridMutCarriersCountInfoDic[gridName].IGuestHistoryGridList.Add(0);
                    }
                }
                gridMutCarriersCountInfoDic[gridName].IAllGrid++;
                if (monthGridDownInfoDic[strCurMonth][cll].D主队RSRP采样点数 > - 1)
                {
                    gridMutCarriersCountInfoDic[gridName].IHostGrid++;
                }
                if (monthGridDownInfoDic[strCurMonth][cll].D客队RSRP采样点数 > -1)
                {
                    gridMutCarriersCountInfoDic[gridName].IGuestGrid++;
                }
                if (monthGridDownInfoDic[strCurMonth][cll].D主队RSRP采样点数 == -1
                    && monthGridDownInfoDic[strCurMonth][cll].D客队RSRP采样点数 == -1)
                {//均无信息
                    continue;
                }
                string strTDDMonthText = "当前";
                string strFDDMonthText = "当前";
                monthGridDownInfoDic[strCurMonth][cll].Str主队数据源 = strCurMonth;
                monthGridDownInfoDic[strCurMonth][cll].Str客队数据源 = strCurMonth;
                if (monthGridDownInfoDic[strCurMonth][cll].D主队RSRP采样点数 > -1)
                {
                    if (monthGridDownInfoDic[strCurMonth][cll].DTDDDownTime <= 0
                        && monthGridDownInfoDic[strCurMonth][cll].D客队RSRP采样点数 == -1)
                    {//TDD有GPS信息但无下载业务，且FDD无GPS信息。-----不推移
                    }
                    else if (monthGridDownInfoDic[strCurMonth][cll].StrGridTDDStatstatus == "TDD无业务不脱网"
                        && monthGridDownInfoDic[strCurMonth][cll].StrGridFDDStatstatus == "FDD无业务不脱网")
                    {//TDD无下载且不脱网,FDD无下载且不脱网。-----两者均需推移
                        int indexTDD = findGridHistoryData(ref monthGridDownInfoDic,strCurMonth,cll,"TDD");
                        if (indexTDD > -1)
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList[indexTDD]++;
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGrid++;
                            strTDDMonthText = "历史";
                            monthGridDownInfoDic[strCurMonth][cll].Str主队数据源 = sqlMonthList[sqlMonthList.Count - 1 - indexTDD];
                        }
                        int indexFDD = findGridHistoryData(ref monthGridDownInfoDic, strCurMonth, cll, "FDD");
                        if (indexFDD > 0)
                        {
                            gridMutCarriersCountInfoDic[gridName].IGuestHistoryGridList[indexFDD]++;
                            gridMutCarriersCountInfoDic[gridName].IGuestHistoryGrid++;
                            strFDDMonthText = "历史";
                            monthGridDownInfoDic[strCurMonth][cll].Str客队数据源 = sqlMonthList[sqlMonthList.Count - 1 - indexFDD];
                        }
                    }
                    else if (monthGridDownInfoDic[strCurMonth][cll].StrGridTDDStatstatus == "TDD无业务不脱网"
                        && monthGridDownInfoDic[strCurMonth][cll].StrGridFDDStatstatus == "FDD下载业务")
                    {//TDD无下载且不脱网，且FDD有下载业务。-----需推移
                        int index = findGridHistoryData(ref monthGridDownInfoDic, strCurMonth, cll, "TDD");
                        if (index > -1)
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList[index]++;
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGrid++;
                            strTDDMonthText = "历史";
                            monthGridDownInfoDic[strCurMonth][cll].Str主队数据源 = sqlMonthList[sqlMonthList.Count - 1 - index];
                        }
                    }
                    else if (monthGridDownInfoDic[strCurMonth][cll].StrGridTDDStatstatus == "TDD下载业务"
                        && (monthGridDownInfoDic[strCurMonth][cll].StrGridFDDStatstatus == "FDD无信息"
                        || monthGridDownInfoDic[strCurMonth][cll].StrGridFDDStatstatus == "FDD无业务不脱网"))
                    {//TDD有下载业务，FDD无GPS信息或有GPS信息但无业务且不脱网。-----需推移
                        int index = findGridHistoryData(ref monthGridDownInfoDic, strCurMonth, cll, "FDD");
                        if (index > -1)
                        {
                            gridMutCarriersCountInfoDic[gridName].IGuestHistoryGridList[index]++;
                            gridMutCarriersCountInfoDic[gridName].IGuestHistoryGrid++;
                            strFDDMonthText = "历史";
                            monthGridDownInfoDic[strCurMonth][cll].Str客队数据源 = sqlMonthList[sqlMonthList.Count - 1 - index];
                        }
                    }
                }
                else
                {
                    if (monthGridDownInfoDic[strCurMonth][cll].DFDDDownTime <= 0)
                    {//TDD无GPS信息，且FDD无下载业务-----不推移
                    }
                    else
                    {//FDD做下载业务，TDD无GPS信息。-----需推移
                        int index = findGridHistoryData(ref monthGridDownInfoDic, strCurMonth, cll, "TDD");
                        if (index > -1)
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList[index]++;
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGrid++;
                            strTDDMonthText = "历史";
                            monthGridDownInfoDic[strCurMonth][cll].Str主队数据源 = sqlMonthList[sqlMonthList.Count - 1 - index];
                        }
                    }
                }
                double dHostDownSpeed = monthGridDownInfoDic[strCurMonth][cll].DTDDDownSpeed;
                double dGuestDownSpeed = monthGridDownInfoDic[strCurMonth][cll].DFDDDownSpeed;
                #region 栅格详情
                if (GetShowGridDataInfo)
                {
                    GridDownLoadTimeSpeedInfo gridDownLoadTimeSpeedInfo = new GridDownLoadTimeSpeedInfo();
                    gridDownLoadTimeSpeedInfo.StrCityInfo = strCityName;
                    gridDownLoadTimeSpeedInfo.StrGridType = monthGridDownInfoDic[strCurMonth][cll].StrGridType;
                    gridDownLoadTimeSpeedInfo.StrGridName = monthGridDownInfoDic[strCurMonth][cll].StrGridName;
                    gridDownLoadTimeSpeedInfo.StrGirdCenterInfo = monthGridDownInfoDic[strCurMonth][cll].StrGirdCenterInfo;
                    gridDownLoadTimeSpeedInfo.Itllng = monthGridDownInfoDic[strCurMonth][cll].Itllng;
                    gridDownLoadTimeSpeedInfo.Itllat = monthGridDownInfoDic[strCurMonth][cll].Itllat;
                    gridDownLoadTimeSpeedInfo.Icentlng = monthGridDownInfoDic[strCurMonth][cll].Icentlng;
                    gridDownLoadTimeSpeedInfo.Icentlat = monthGridDownInfoDic[strCurMonth][cll].Icentlat;
                    gridDownLoadTimeSpeedInfo.DTDDDownTime = monthGridDownInfoDic[strCurMonth][cll].DTDDDownTime;
                    gridDownLoadTimeSpeedInfo.DTDDDownSize = monthGridDownInfoDic[strCurMonth][cll].DTDDDownSize;
                    gridDownLoadTimeSpeedInfo.DTDDDownSpeed = monthGridDownInfoDic[strCurMonth][cll].DTDDDownSpeed;
                    gridDownLoadTimeSpeedInfo.DTDDSampleNum = monthGridDownInfoDic[strCurMonth][cll].DTDDSampleNum;
                    gridDownLoadTimeSpeedInfo.D主队RSRP采样点数 = monthGridDownInfoDic[strCurMonth][cll].D主队RSRP采样点数;
                    gridDownLoadTimeSpeedInfo.D主队脱网标记 = monthGridDownInfoDic[strCurMonth][cll].D主队脱网标记;
                    gridDownLoadTimeSpeedInfo.D主队占它网时长 = monthGridDownInfoDic[strCurMonth][cll].D主队占它网时长;
                    gridDownLoadTimeSpeedInfo.Str主队数据源 = monthGridDownInfoDic[strCurMonth][cll].Str主队数据源;
                    gridDownLoadTimeSpeedInfo.DFDDDownTime = monthGridDownInfoDic[strCurMonth][cll].DFDDDownTime;
                    gridDownLoadTimeSpeedInfo.DFDDDownSize = monthGridDownInfoDic[strCurMonth][cll].DFDDDownSize;
                    gridDownLoadTimeSpeedInfo.DFDDDownSpeed = monthGridDownInfoDic[strCurMonth][cll].DFDDDownSpeed;
                    gridDownLoadTimeSpeedInfo.DFDDSampleNum = monthGridDownInfoDic[strCurMonth][cll].DFDDSampleNum;
                    gridDownLoadTimeSpeedInfo.D客队RSRP采样点数 = monthGridDownInfoDic[strCurMonth][cll].D客队RSRP采样点数;
                    gridDownLoadTimeSpeedInfo.D客队脱网标记 = monthGridDownInfoDic[strCurMonth][cll].D客队脱网标记;
                    gridDownLoadTimeSpeedInfo.D客队占它网时长 = monthGridDownInfoDic[strCurMonth][cll].D客队占它网时长;
                    gridDownLoadTimeSpeedInfo.Str客队数据源 = monthGridDownInfoDic[strCurMonth][cll].Str客队数据源;
                    gridDownLoadTimeSpeedInfoList.Add(gridDownLoadTimeSpeedInfo);
                }
                #endregion
                if (monthGridDownInfoDic[strCurMonth][cll].DTDDDownTime > 0)
                {
                    gridMutCarriersCountInfoDic[gridName].I含历史主队下载栅格数++;
                    if (strTDDMonthText.Equals("当前"))
                    {
                        gridMutCarriersCountInfoDic[gridName].I主队本月下载栅格数++;
                    }
                }
                if (monthGridDownInfoDic[strCurMonth][cll].DFDDDownTime > 0)
                {
                    gridMutCarriersCountInfoDic[gridName].I含历史客队下载栅格数++;
                    if (strFDDMonthText.Equals("当前"))
                    {
                        gridMutCarriersCountInfoDic[gridName].I客队本月下载栅格数++;
                    }
                }
                if (monthGridDownInfoDic[strCurMonth][cll].D主队RSRP采样点数 == -1
                    || monthGridDownInfoDic[strCurMonth][cll].D客队RSRP采样点数 == -1)
                {//任意一方无信息
                    continue;
                }
                if (monthGridDownInfoDic[strCurMonth][cll].StrGridTDDStatstatus == "TDD无业务不脱网"
                        && monthGridDownInfoDic[strCurMonth][cll].StrGridFDDStatstatus == "FDD无业务不脱网")
                {//均无业务且均不脱网
                    continue;
                }
                if (monthGridDownInfoDic[strCurMonth][cll].StrGridTDDStatstatus == "TDD脱网"
                        && monthGridDownInfoDic[strCurMonth][cll].StrGridFDDStatstatus == "FDD脱网")
                {//均无业务且均脱网
                    continue;
                }
                if ((monthGridDownInfoDic[strCurMonth][cll].StrGridTDDStatstatus == "TDD下载业务"
                        && monthGridDownInfoDic[strCurMonth][cll].StrGridFDDStatstatus == "FDD无业务不脱网")
                    || (monthGridDownInfoDic[strCurMonth][cll].StrGridTDDStatstatus == "TDD无业务不脱网"
                        && monthGridDownInfoDic[strCurMonth][cll].StrGridFDDStatstatus == "FDD下载业务"))
                {//一方存在下载业务，另一方既无业务也不脱网
                    continue;
                }
                gridMutCarriersCountInfoDic[gridName].ICompareGrid++;
                if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                {
                    gridMutCarriersCountInfoDic[gridName].I本月对比总栅格数++;
                }
                if (monthGridDownInfoDic[strCurMonth][cll].DTDDDownTime <= 0
                    && monthGridDownInfoDic[strCurMonth][cll].DFDDDownTime <= 0)
                {
                    if (monthGridDownInfoDic[strCurMonth][cll].StrGridTDDStatstatus == "TDD脱网")
                    {
                        gridMutCarriersCountInfoDic[gridName].I主队脱网栅格数++;
                        gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                        if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                        }
                        GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "TDD", monthGridDownInfoDic[strCurMonth][cll].D主队脱网标记
                            , monthGridDownInfoDic[strCurMonth][cll].D主队占它网时长, monthGridDownInfoDic[strCurMonth][cll].D主队RSRP采样点数);
                    }
                    else
                    {
                        gridMutCarriersCountInfoDic[gridName].I客队脱网栅格数++;
                        gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                        if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                        }
                        GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "FDD", monthGridDownInfoDic[strCurMonth][cll].D客队脱网标记
                            , monthGridDownInfoDic[strCurMonth][cll].D客队占它网时长, monthGridDownInfoDic[strCurMonth][cll].D客队RSRP采样点数);
                    }
                }
                else if (monthGridDownInfoDic[strCurMonth][cll].DTDDDownTime > 0 && monthGridDownInfoDic[strCurMonth][cll].DFDDDownTime <= 0)
                {
                    gridMutCarriersCountInfoDic[gridName].I客队脱网栅格数++;
                    gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                    if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                    }
                    GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "FDD", monthGridDownInfoDic[strCurMonth][cll].D客队脱网标记
                        , monthGridDownInfoDic[strCurMonth][cll].D客队占它网时长, monthGridDownInfoDic[strCurMonth][cll].D客队RSRP采样点数);
                }
                else if (monthGridDownInfoDic[strCurMonth][cll].DTDDDownTime <= 0 && monthGridDownInfoDic[strCurMonth][cll].DFDDDownTime > 0)
                {
                    gridMutCarriersCountInfoDic[gridName].I主队脱网栅格数++;
                    gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                    if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                    }
                    GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "TDD", monthGridDownInfoDic[strCurMonth][cll].D主队脱网标记
                        , monthGridDownInfoDic[strCurMonth][cll].D主队占它网时长, monthGridDownInfoDic[strCurMonth][cll].D主队RSRP采样点数);
                }
                else
                {
                    if (dHostDownSpeed > dSpeed && dGuestDownSpeed > dSpeed)
                    {
                        if (dHostDownSpeed >= dGuestDownSpeed * dPercent)
                        {
                            gridMutCarriersCountInfoDic[gridName].IMoreGoodGrid++;
                        }
                        else
                        {
                            gridMutCarriersCountInfoDic[gridName].IMoreWeakGrid++;
                        }
                    }
                    else if (dHostDownSpeed > dSpeed && dGuestDownSpeed <= dSpeed)
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostMoreGuestLess++;
                        gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                        if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                        }
                    }
                    else if (dHostDownSpeed <= dSpeed && dGuestDownSpeed > dSpeed)
                    {
                        if (dHostDownSpeed >= dGuestDownSpeed * dPercent)
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostLessGuestMore++;
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                            if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                            }
                        }
                        else
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostLessGuestMoreWeak++;
                            gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                            if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                            }
                        }
                    }
                    else if (dHostDownSpeed <= dSpeed && dGuestDownSpeed <= dSpeed)
                    {
                        if (dHostDownSpeed >= dGuestDownSpeed * dPercent)
                        {
                            gridMutCarriersCountInfoDic[gridName].ILessGoodGrid++;
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                            if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                            }
                        }
                        else
                        {
                            gridMutCarriersCountInfoDic[gridName].ILessWeakGrid++;
                            gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                            if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                            }
                        }
                    }
                }
            }
            foreach (GridTypeName gridName in gridMutCarriersCountInfoDic.Keys)
            {
                gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList[0]
                    = gridMutCarriersCountInfoDic[gridName].ICompareGrid - gridMutCarriersCountInfoDic[gridName].IHostHistoryGrid;
                gridMutCarriersCountInfoDic[gridName].IGuestHistoryGridList[0]
                    = gridMutCarriersCountInfoDic[gridName].ICompareGrid - gridMutCarriersCountInfoDic[gridName].IGuestHistoryGrid;
            }
            if (!cityMutCarriersCountInfoDic.ContainsKey(strCityName))
            {
                cityMutCarriersCountInfoDic.Add(strCityName, gridMutCarriersCountInfoDic);
            }
        }

        private int findGridHistoryData(ref Dictionary<string, Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>> monthGridDownInfoDic
            ,string strCurMonth, CenterLongLat cll, string strNetType)
        {
            int index = -1;
            for (int i = sqlMonthList.Count - 2; i >= 0; i--)
            {
                if(monthGridDownInfoDic[sqlMonthList[i]].ContainsKey(cll))
                {
                    if (strNetType.Equals("TDD"))
                    {
                        if (monthGridDownInfoDic[sqlMonthList[i]][cll].DTDDDownTime > 0)
                        {
                            monthGridDownInfoDic[strCurMonth][cll].D主队RSRP采样点数 = monthGridDownInfoDic[sqlMonthList[i]][cll].D主队RSRP采样点数;
                            monthGridDownInfoDic[strCurMonth][cll].D主队脱网标记 = monthGridDownInfoDic[sqlMonthList[i]][cll].D主队脱网标记;
                            monthGridDownInfoDic[strCurMonth][cll].D主队脱网时长 = monthGridDownInfoDic[sqlMonthList[i]][cll].D主队脱网时长;
                            monthGridDownInfoDic[strCurMonth][cll].D主队占它网时长 = monthGridDownInfoDic[sqlMonthList[i]][cll].D主队占它网时长;
                            monthGridDownInfoDic[strCurMonth][cll].DTDDDownTime = monthGridDownInfoDic[sqlMonthList[i]][cll].DTDDDownTime;
                            monthGridDownInfoDic[strCurMonth][cll].DTDDDownSize = monthGridDownInfoDic[sqlMonthList[i]][cll].DTDDDownSize;
                            monthGridDownInfoDic[strCurMonth][cll].DTDDDownSpeed = monthGridDownInfoDic[sqlMonthList[i]][cll].DTDDDownSpeed;
                            monthGridDownInfoDic[strCurMonth][cll].DTDDSampleNum = monthGridDownInfoDic[sqlMonthList[i]][cll].DTDDSampleNum;
                            monthGridDownInfoDic[strCurMonth][cll].Str主队数据源 = monthGridDownInfoDic[sqlMonthList[i]][cll].Str主队数据源;
                            index = sqlMonthList.Count - 2 + 1 - i;
                            break;
                        }
                    }
                    else
                    {
                        if (monthGridDownInfoDic[sqlMonthList[i]][cll].DFDDDownTime > 0)
                        {
                            monthGridDownInfoDic[strCurMonth][cll].D客队RSRP采样点数 = monthGridDownInfoDic[sqlMonthList[i]][cll].D客队RSRP采样点数;
                            monthGridDownInfoDic[strCurMonth][cll].D客队脱网标记 = monthGridDownInfoDic[sqlMonthList[i]][cll].D客队脱网标记;
                            monthGridDownInfoDic[strCurMonth][cll].D客队脱网时长 = monthGridDownInfoDic[sqlMonthList[i]][cll].D客队脱网时长;
                            monthGridDownInfoDic[strCurMonth][cll].D客队占它网时长 = monthGridDownInfoDic[sqlMonthList[i]][cll].D客队占它网时长;
                            monthGridDownInfoDic[strCurMonth][cll].DFDDDownTime = monthGridDownInfoDic[sqlMonthList[i]][cll].DFDDDownTime;
                            monthGridDownInfoDic[strCurMonth][cll].DFDDDownSize = monthGridDownInfoDic[sqlMonthList[i]][cll].DFDDDownSize;
                            monthGridDownInfoDic[strCurMonth][cll].DFDDDownSpeed = monthGridDownInfoDic[sqlMonthList[i]][cll].DFDDDownSpeed;
                            monthGridDownInfoDic[strCurMonth][cll].DFDDSampleNum = monthGridDownInfoDic[sqlMonthList[i]][cll].DFDDSampleNum;
                            monthGridDownInfoDic[strCurMonth][cll].Str客队数据源 = monthGridDownInfoDic[sqlMonthList[i]][cll].Str客队数据源;
                            index = sqlMonthList.Count - 2 + 1- i;
                            break;
                        }
                    }
                }
            }
            return index;
        }

        #endregion

        #region 算法二
        /**
        private void doStatWithGridInfo(Dictionary<string, Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>> monthGridDownInfoDic)
        {
            Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo> tddCenterLongLatGridInfoDic
                = new Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>();
            Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo> fddCenterLongLatGridInfoDic
                = new Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>();

            Dictionary<CenterLongLat, int> cllALLDic = new Dictionary<CenterLongLat, int>();
            Dictionary<CenterLongLat, int> cllTDDDic = new Dictionary<CenterLongLat, int>();
            Dictionary<CenterLongLat, int> cllFDDDic = new Dictionary<CenterLongLat, int>();
            Dictionary<CenterLongLat, int> cllTDDDownloadDic = new Dictionary<CenterLongLat, int>();
            Dictionary<CenterLongLat, int> cllFDDDownloadDic = new Dictionary<CenterLongLat, int>();
           
            foreach (string month in monthGridDownInfoDic.Keys)
            {
                foreach (CenterLongLat cll in monthGridDownInfoDic[month].Keys)
                {
                    List<bool> isNeedStatList = judgeGridIsNeedStat(monthGridDownInfoDic, month, cll);
                    if (isNeedStatList[0] == true)
                    {
                        tddCenterLongLatGridInfoDic[cll] = monthGridDownInfoDic[month][cll];
                    }
                    if (isNeedStatList[1] == true)
                    {
                        fddCenterLongLatGridInfoDic[cll] = monthGridDownInfoDic[month][cll];
                    }
                    if (!cllALLDic.ContainsKey(cll))
                    {
                        cllALLDic.Add(cll,0);
                    }
                    if (monthGridDownInfoDic[month][cll].D主队RSRP采样点数 > -1)
                    {
                        if (!cllTDDDic.ContainsKey(cll))
                        {
                            cllTDDDic.Add(cll,0);
                        }
                        if (monthGridDownInfoDic[month][cll].DTDDDownTime > 0 && !cllTDDDownloadDic.ContainsKey(cll))
                        {
                            cllTDDDownloadDic.Add(cll,0);
                        }
                    }
                    if (monthGridDownInfoDic[month][cll].D客队RSRP采样点数 > -1)
                    {
                        if (!cllFDDDic.ContainsKey(cll))
                        {
                            cllFDDDic.Add(cll,0);
                        }
                        if (monthGridDownInfoDic[month][cll].DFDDDownTime > 0 && !cllFDDDownloadDic.ContainsKey(cll))
                        {
                            cllFDDDownloadDic.Add(cll,0);
                        }
                    }
                }
            }
            List<int> iCllCount = new List<int>() { cllALLDic.Keys.Count, cllTDDDic.Keys.Count, cllFDDDic.Keys.Count
                , cllTDDDownloadDic.Keys.Count ,cllFDDDownloadDic.Keys.Count};
            doWithSingelGridStatInfo(tddCenterLongLatGridInfoDic, fddCenterLongLatGridInfoDic, monthGridDownInfoDic, iCllCount);
        }
   
        private List<bool> judgeGridIsNeedStat(Dictionary<string, Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>> monthGridDownInfoDic
            , string month, CenterLongLat cll)
        {
            List<bool> bNeed = new List<bool>() { false, false };
            if (monthGridDownInfoDic[month][cll].D主队RSRP采样点数 > -1)
            {
                if (monthGridDownInfoDic[month][cll].StrGridTDDStatstatus == "TDD下载业务")
                { //TDD做下载业务,且FDD存在最后业务或脱网
                    for (int i = sqlMonthList.Count - 1; i >= 0; i--)
                    {
                        if (monthGridDownInfoDic[sqlMonthList[i]].ContainsKey(cll)
                             && monthGridDownInfoDic[sqlMonthList[i]][cll].D客队RSRP采样点数 > -1)
                        {
                            if (monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridFDDStatstatus == "FDD下载业务" 
                                || monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridFDDStatstatus == "FDD脱网")
                            {
                                bNeed[0] = true;
                                break;
                            }
                        }
                    }
                }
                else
                {
                    if (monthGridDownInfoDic[month][cll].StrGridTDDStatstatus == "TDD脱网")
                    { //TDD脱网，且FDD存在最后不脱网
                        for (int i = sqlMonthList.Count - 1; i >= 0; i--)
                        {
                            if (monthGridDownInfoDic[sqlMonthList[i]].ContainsKey(cll)
                                 && monthGridDownInfoDic[sqlMonthList[i]][cll].D客队RSRP采样点数 > -1)
                            {
                                if (monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridFDDStatstatus == "FDD脱网")
                                {
                                    break;
                                }
                                if (monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridFDDStatstatus == "FDD下载业务"
                                    || monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridFDDStatstatus == "FDD无业务不脱网")
                                {
                                    bNeed[0] = true;
                                    break;
                                }
                            }
                        }
                    }
                    else
                    { //TDD无业务，FDD最后一轮必须脱网
                        for (int i = sqlMonthList.Count - 1; i >= 0; i--)
                        {
                            if (monthGridDownInfoDic[sqlMonthList[i]].ContainsKey(cll)
                                 && monthGridDownInfoDic[sqlMonthList[i]][cll].D客队RSRP采样点数 > -1)
                            {
                                if (monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridFDDStatstatus == "FDD下载业务"
                                    || monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridFDDStatstatus == "FDD无业务不脱网")
                                {
                                    break;
                                }
                                if (monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridFDDStatstatus == "FDD脱网")
                                {
                                    bNeed[0] = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            if (monthGridDownInfoDic[month][cll].D客队RSRP采样点数 > -1)
            {
                if (monthGridDownInfoDic[month][cll].StrGridFDDStatstatus == "FDD下载业务")
                { //FDD做下载业务，且TDD存在最后业务或脱网
                    for (int i = sqlMonthList.Count - 1; i >= 0; i--)
                    {
                        if (monthGridDownInfoDic[sqlMonthList[i]].ContainsKey(cll)
                             && monthGridDownInfoDic[sqlMonthList[i]][cll].D主队RSRP采样点数 > -1)
                        {
                            if (monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridTDDStatstatus == "TDD下载业务"
                                || monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridTDDStatstatus == "TDD脱网")
                            {
                                bNeed[1] = true;
                                break;
                            }
                        }
                    }
                }
                else
                {
                    if (monthGridDownInfoDic[month][cll].StrGridFDDStatstatus == "FDD脱网")
                    { //FDD脱网，且TDD存在最后不脱网
                        for (int i = sqlMonthList.Count - 1; i >= 0; i--)
                        {
                            if (monthGridDownInfoDic[sqlMonthList[i]].ContainsKey(cll)
                                 && monthGridDownInfoDic[sqlMonthList[i]][cll].D主队RSRP采样点数 > -1)
                            {
                                if (monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridTDDStatstatus == "TDD脱网")
                                {
                                    break;
                                }
                                if (monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridTDDStatstatus == "TDD下载业务"
                                    || monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridTDDStatstatus == "TDD无业务不脱网")
                                {
                                    bNeed[1] = true;
                                    break;
                                }
                            }
                        }
                    }
                    else
                    { //FDD无业务，TDD存在最后脱网
                        for (int i = sqlMonthList.Count - 1; i >= 0; i--)
                        {
                            if (monthGridDownInfoDic[sqlMonthList[i]].ContainsKey(cll)
                                 && monthGridDownInfoDic[sqlMonthList[i]][cll].D主队RSRP采样点数 > -1)
                            {
                                if (monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridTDDStatstatus == "TDD下载业务"
                                    || monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridTDDStatstatus == "TDD无业务不脱网")
                                {
                                    break;
                                }
                                if (monthGridDownInfoDic[sqlMonthList[i]][cll].StrGridTDDStatstatus == "TDD脱网")
                                {
                                    bNeed[1] = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            return bNeed;
        }
    
        private void doWithSingelGridStatInfo(Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo> tddCenterLongLatGridInfoDic
            , Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo> fddCenterLongLatGridInfoDic
            , Dictionary<string, Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>> monthGridDownInfoDic
            , List<int> iCllCount)
        {
            Dictionary<GridTypeName, GridMutCarriersCountInfo> gridMutCarriersCountInfoDic
                = new Dictionary<GridTypeName, GridMutCarriersCountInfo>();
            string strCurMonth = sqlMonthList[sqlMonthList.Count - 1];
            foreach (CenterLongLat cll in fddCenterLongLatGridInfoDic.Keys)
            {
                if (!tddCenterLongLatGridInfoDic.ContainsKey(cll))
                {
                    continue;
                }
                GridDownLoadTimeSpeedInfo gridTDDInfo = tddCenterLongLatGridInfoDic[cll];
                GridDownLoadTimeSpeedInfo gridInfo = fddCenterLongLatGridInfoDic[cll];

                gridInfo.Str客队数据源 = gridInfo.StrMonth;

                gridInfo.DTDDDownSpeed = gridTDDInfo.DTDDDownSpeed;
                gridInfo.DTDDDownTime = gridTDDInfo.DTDDDownTime;
                gridInfo.DTDDSampleNum = gridTDDInfo.DTDDSampleNum;
                gridInfo.D主队RSRP采样点数 = gridTDDInfo.D主队RSRP采样点数;
                gridInfo.D主队脱网标记 = gridTDDInfo.D主队脱网标记;
                gridInfo.D主队脱网时长 = gridTDDInfo.D主队脱网时长;
                gridInfo.D主队占它网时长 = gridTDDInfo.D主队占它网时长;
                gridInfo.Str主队数据源 = gridTDDInfo.StrMonth;

                if (gridInfo.StrGridTDDStatstatus == "TDD无业务不脱网" && gridInfo.StrGridFDDStatstatus == "FDD无业务不脱网")
                {//均无业务且均不脱网
                    continue;
                }
                if (gridInfo.StrGridTDDStatstatus == "TDD脱网" && gridInfo.StrGridFDDStatstatus == "FDD脱网")
                {//均无业务且均脱网
                    continue;
                }
                if ((gridInfo.StrGridTDDStatstatus == "TDD下载业务" && gridInfo.StrGridFDDStatstatus == "FDD无业务不脱网")
                    || (gridInfo.StrGridTDDStatstatus == "TDD无业务不脱网" && gridInfo.StrGridFDDStatstatus == "FDD下载业务"))
                {//一方存在下载业务，另一方既无业务且不脱网
                    continue;
                }

                gridDownLoadTimeSpeedInfoList.Add(gridInfo);

                GridTypeName gridName = new GridTypeName();
                gridName.strGridType = gridInfo.StrGridType;
                gridName.strGridName = gridInfo.StrGridName;
                if (!gridMutCarriersCountInfoDic.ContainsKey(gridName))
                {
                    gridMutCarriersCountInfoDic[gridName] = new GridMutCarriersCountInfo();
                    gridMutCarriersCountInfoDic[gridName].StrCity = strCityName;
                    gridMutCarriersCountInfoDic[gridName].StrGridType = gridName.strGridType;
                    gridMutCarriersCountInfoDic[gridName].StrGridName = gridName.strGridName;
                    GridCurMonthOtherInfo(ref gridMutCarriersCountInfoDic, gridName, monthGridDownInfoDic
                        , tddCenterLongLatGridInfoDic, fddCenterLongLatGridInfoDic, iCllCount);
                }

                for (int i = sqlMonthList.Count - 2; i >= 0; i--)
                {
                    if (gridInfo.Str主队数据源.Equals(sqlMonthList[i]))
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostHistoryGrid++;
                        gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList[sqlMonthList.Count - 2 - i]++;
                    }
                    if (gridInfo.Str客队数据源.Equals(sqlMonthList[i]))
                    {
                        gridMutCarriersCountInfoDic[gridName].IGuestHistoryGrid++;
                        gridMutCarriersCountInfoDic[gridName].IGuestHistoryGridList[sqlMonthList.Count - 2 - i]++;
                    }
                }

                gridMutCarriersCountInfoDic[gridName].ICompareGrid++;

                if (gridInfo.Str客队数据源.Equals(strCurMonth) && gridInfo.Str主队数据源.Contains(strCurMonth))
                {
                    gridMutCarriersCountInfoDic[gridName].I本月对比总栅格数++;
                }

                if (gridInfo.DTDDDownTime <= 0 && gridInfo.DFDDDownTime <= 0)
                {
                    if ((gridInfo.D主队脱网标记 + gridInfo.D主队占它网时长) > 0 || gridInfo.D主队RSRP采样点数 == 0)
                    {
                        gridMutCarriersCountInfoDic[gridName].I主队脱网栅格数++;
                        gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                        if (gridInfo.Str客队数据源.Equals(strCurMonth) && gridInfo.Str主队数据源.Equals(strCurMonth))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                        }
                        GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "TDD", gridInfo.D主队脱网标记
                            , gridInfo.D主队占它网时长, gridInfo.D主队RSRP采样点数);
                    }
                    else// if ((gridInfo.D客队脱网标记 + gridInfo.D客队占它网时长) > 0 || gridInfo.D客队RSRP采样点数 == 0)
                    {
                        gridMutCarriersCountInfoDic[gridName].I客队脱网栅格数++;
                        gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                        if (gridInfo.Str客队数据源.Equals(strCurMonth) && gridInfo.Str主队数据源.Equals(strCurMonth))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                        }
                        GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "FDD", gridInfo.D客队脱网标记
                            , gridInfo.D客队占它网时长, gridInfo.D客队RSRP采样点数);
                    }

                }
                else if (gridInfo.DTDDDownTime > 0 && gridInfo.DFDDDownTime <= 0)
                {
                    gridMutCarriersCountInfoDic[gridName].I客队脱网栅格数++;
                    gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                    if (gridInfo.Str客队数据源.Equals(strCurMonth) && gridInfo.Str主队数据源.Equals(strCurMonth))
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                    }
                    GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "FDD", gridInfo.D客队脱网标记
                        , gridInfo.D客队占它网时长, gridInfo.D客队RSRP采样点数);
                }
                else if (gridInfo.DTDDDownTime <= 0 && gridInfo.DFDDDownTime > 0)
                {
                    gridMutCarriersCountInfoDic[gridName].I主队脱网栅格数++;
                    gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                    if (gridInfo.Str客队数据源.Equals(strCurMonth) && gridInfo.Str主队数据源.Equals(strCurMonth))
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                    }
                    GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "TDD", gridInfo.D主队脱网标记
                        , gridInfo.D主队占它网时长, gridInfo.D主队RSRP采样点数);
                }
                else
                {
                    if (gridInfo.DTDDDownSpeed > dSpeed && gridInfo.DFDDDownSpeed > dSpeed)
                    {
                        if (gridInfo.DTDDDownSpeed >= gridInfo.DFDDDownSpeed * dPercent)
                        {
                            gridMutCarriersCountInfoDic[gridName].IMoreGoodGrid++;
                        }
                        else
                        {
                            gridMutCarriersCountInfoDic[gridName].IMoreWeakGrid++;
                        }
                    }
                    else if (gridInfo.DTDDDownSpeed > dSpeed && gridInfo.DFDDDownSpeed <= dSpeed)
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostMoreGuestLess++;
                        gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                        if (gridInfo.Str客队数据源.Equals(strCurMonth) && gridInfo.Str主队数据源.Equals(strCurMonth))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                        }
                    }
                    else if (gridInfo.DTDDDownSpeed <= dSpeed && gridInfo.DFDDDownSpeed > dSpeed)
                    {
                        if (gridInfo.DTDDDownSpeed >= gridInfo.DFDDDownSpeed * dPercent)
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostLessGuestMore++;
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                            if (gridInfo.Str客队数据源.Equals(strCurMonth) && gridInfo.Str主队数据源.Equals(strCurMonth))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                            }
                        }
                        else
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostLessGuestMoreWeak++;
                            gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                            if (gridInfo.Str客队数据源.Equals(strCurMonth) && gridInfo.Str主队数据源.Equals(strCurMonth))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                            }
                        }
                    }
                    else if (gridInfo.DTDDDownSpeed <= dSpeed && gridInfo.DFDDDownSpeed <= dSpeed)
                    {
                        if (gridInfo.DTDDDownSpeed >= gridInfo.DFDDDownSpeed * dPercent)
                        {
                            gridMutCarriersCountInfoDic[gridName].ILessGoodGrid++;
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                            if (gridInfo.Str客队数据源.Equals(strCurMonth) && gridInfo.Str主队数据源.Equals(strCurMonth))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                            }
                        }
                        else
                        {
                            gridMutCarriersCountInfoDic[gridName].ILessWeakGrid++;
                            gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                            if (gridInfo.Str客队数据源.Equals(strCurMonth) && gridInfo.Str主队数据源.Equals(strCurMonth))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                            }
                        }
                    }
                }
            }
            if (!cityMutCarriersCountInfoDic.ContainsKey(strCityName))
            {
                cityMutCarriersCountInfoDic.Add(strCityName, gridMutCarriersCountInfoDic);
            }
        }
     
        private void GridCurMonthOtherInfo(ref Dictionary<GridTypeName, GridMutCarriersCountInfo> gridSumInfoDic, GridTypeName gridName
            , Dictionary<string, Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>> monthGridDownInfoDic
            , Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo> tddCenterLongLatGridInfoDic
            , Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo> fddCenterLongLatGridInfoDic, List<int> iCllCount)
        {
            string strCurMon = sqlMonthList[sqlMonthList.Count - 1];

            gridSumInfoDic[gridName].IAllGrid = iCllCount[0];
            gridSumInfoDic[gridName].IHostGrid = iCllCount[1];
            gridSumInfoDic[gridName].IGuestGrid = iCllCount[2];
            gridSumInfoDic[gridName].I含历史主队下载栅格数 = iCllCount[3];
            gridSumInfoDic[gridName].I含历史客队下载栅格数 = iCllCount[4];
            foreach (CenterLongLat cll in monthGridDownInfoDic[strCurMon].Keys)
            {
                if (monthGridDownInfoDic[strCurMon][cll].D主队RSRP采样点数 > -1)
                {
                    if (monthGridDownInfoDic[strCurMon][cll].DTDDDownTime > 0)
                    {
                        gridSumInfoDic[gridName].I主队本月下载栅格数++;
                    }
                }
                if (monthGridDownInfoDic[strCurMon][cll].D客队RSRP采样点数 > -1)
                {
                    if (monthGridDownInfoDic[strCurMon][cll].DFDDDownTime > 0)
                    {
                        gridSumInfoDic[gridName].I客队本月下载栅格数++;
                    }
                }
            }
            for (int i = sqlMonthList.Count - 2; i >= 0; i--)
            {
                gridSumInfoDic[gridName].IHostHistoryGridList.Add(0);
                gridSumInfoDic[gridName].IGuestHistoryGridList.Add(0);
            }
        }
        **/
        #endregion

        #region 共用方法
        private void GridKeySumInfo(ref GridMutCarriersCountInfo gridSumInfo, string strCity, GridTypeName gridTypeName)
        {
            gridSumInfo.IAllGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IAllGrid;
            gridSumInfo.IHostHistoryGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostHistoryGrid;
            gridSumInfo.IGuestHistoryGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IGuestHistoryGrid;
            for (int i = 0; i < cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostHistoryGridList.Count; i++)
            {
                if (gridSumInfo.IHostHistoryGridList.Count != cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostHistoryGridList.Count)
                {
                    gridSumInfo.IHostHistoryGridList.Add(0);
                    gridSumInfo.IGuestHistoryGridList.Add(0);
                }
                gridSumInfo.IHostHistoryGridList[i] += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostHistoryGridList[i];
                gridSumInfo.IGuestHistoryGridList[i] += cityMutCarriersCountInfoDic[strCity][gridTypeName].IGuestHistoryGridList[i];
            }
            gridSumInfo.ICompareGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].ICompareGrid;
            gridSumInfo.I主队脱网栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I主队脱网栅格数;
            gridSumInfo.I主队脱网栅格数_脱网标记 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I主队脱网栅格数_脱网标记;
            gridSumInfo.I主队脱网栅格数_占它网时长 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I主队脱网栅格数_占它网时长;
            gridSumInfo.I主队脱网栅格数_无RSRP采样点 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I主队脱网栅格数_无RSRP采样点;
            gridSumInfo.I客队脱网栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I客队脱网栅格数;
            gridSumInfo.I客队脱网栅格数_脱网标记 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I客队脱网栅格数_脱网标记;
            gridSumInfo.I客队脱网栅格数_占它网时长 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I客队脱网栅格数_占它网时长;
            gridSumInfo.I客队脱网栅格数_无RSRP采样点 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I客队脱网栅格数_无RSRP采样点;
            gridSumInfo.IGuestGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IGuestGrid;
            gridSumInfo.IHostGoodGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostGoodGrid;
            gridSumInfo.IHostGoodGridCurMonth += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostGoodGridCurMonth;
            gridSumInfo.IHostGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostGrid;
            gridSumInfo.IHostLessGuestMore += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostLessGuestMore;
            gridSumInfo.IHostLessGuestMoreWeak += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostLessGuestMoreWeak;
            gridSumInfo.IHostMoreGuestLess += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostMoreGuestLess;
            gridSumInfo.IHostWeakGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostWeakGrid;
            gridSumInfo.IHostWeakGridCurMonth += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostWeakGridCurMonth;
            gridSumInfo.ILessGoodGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].ILessGoodGrid;
            gridSumInfo.ILessWeakGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].ILessWeakGrid;
            gridSumInfo.IMoreGoodGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IMoreGoodGrid;
            gridSumInfo.IMoreWeakGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IMoreWeakGrid;

            gridSumInfo.I主队本月下载栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I主队本月下载栅格数;
            gridSumInfo.I客队本月下载栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I客队本月下载栅格数;
            gridSumInfo.I本月对比总栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I本月对比总栅格数;
            gridSumInfo.I含历史主队下载栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I含历史主队下载栅格数;
            gridSumInfo.I含历史客队下载栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I含历史客队下载栅格数;
        }

        private void GridOff_NetSumInfo(ref Dictionary<GridTypeName, GridMutCarriersCountInfo> gridSumInfoDic, GridTypeName gridName, string strNet
            , double d脱网标记, double d占它网时长, double dRSRP采样点数)
        {
            if (d脱网标记 > 0)
            {
                if (strNet.Equals("TDD"))
                {
                    gridSumInfoDic[gridName].I主队脱网栅格数_脱网标记++;
                }
                else
                {
                    gridSumInfoDic[gridName].I客队脱网栅格数_脱网标记++;
                }
            }
            else if (d占它网时长 > 0)
            {
                if (strNet.Equals("TDD"))
                {
                    gridSumInfoDic[gridName].I主队脱网栅格数_占它网时长++;
                }
                else
                {
                    gridSumInfoDic[gridName].I客队脱网栅格数_占它网时长++;
                }
            }
            else if (dRSRP采样点数 == 0)
            {
                if (strNet.Equals("TDD"))
                {
                    gridSumInfoDic[gridName].I主队脱网栅格数_无RSRP采样点++;
                }
                else
                {
                    gridSumInfoDic[gridName].I客队脱网栅格数_无RSRP采样点++;
                }
            }
        }

        protected void fireShowResult()
        {
            if (cityMutCarriersCountInfoDic.Count == 0)
            {
                MessageBox.Show("没有结果");
                return;
            }
            GridCompareCountForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(GridCompareCountForm).FullName);
            showForm = obj == null ? null : obj as GridCompareCountForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new GridCompareCountForm(MainModel, false);
            }

            List<GridMutCarriersCountInfo> gridInfoList = new List<GridMutCarriersCountInfo>();
            Dictionary<string, List<GridTypeName>> cityGridTypeNameDic = new Dictionary<string, List<GridTypeName>>();
            GridMutCarriersCountInfo sumInfo = new GridMutCarriersCountInfo();

            sumInfo.StrCity = "汇总";
            sumInfo.StrGridType = "汇总";
            sumInfo.StrGridName = "汇总";
            sumInfo = summaryGrid(gridInfoList, cityGridTypeNameDic, sumInfo);
            summaryCityGrid(gridInfoList, cityGridTypeNameDic);
            summaryCity(gridInfoList);
            sumInfo.ISN = gridInfoList.Count + 1;
            gridInfoList.Add(sumInfo);
            showForm.FillData(gridInfoList, gridDownLoadTimeSpeedInfoList, GetShowGridDataInfo);
            showForm.Show(MainModel.MainForm);
        }

        private GridMutCarriersCountInfo summaryGrid(List<GridMutCarriersCountInfo> gridInfoList, Dictionary<string, List<GridTypeName>> cityGridTypeNameDic, GridMutCarriersCountInfo sumInfo)
        {
            foreach (string strCity in cityMutCarriersCountInfoDic.Keys)
            {
                if (!cityGridTypeNameDic.ContainsKey(strCity))
                {
                    cityGridTypeNameDic[strCity] = new List<GridTypeName>();
                }
                foreach (GridTypeName gridTypeName in cityMutCarriersCountInfoDic[strCity].Keys)
                {
                    GridTypeName gridType = new GridTypeName();
                    gridType.strGridType = gridTypeName.strGridType;
                    gridType.strGridName = "汇总";
                    if (!cityGridTypeNameDic[strCity].Contains(gridType))
                    {
                        cityGridTypeNameDic[strCity].Add(gridType);
                    }
                    GridKeySumInfo(ref sumInfo, strCity, gridTypeName);
                    cityMutCarriersCountInfoDic[strCity][gridTypeName].ISN = gridInfoList.Count + 1;
                    gridInfoList.Add(cityMutCarriersCountInfoDic[strCity][gridTypeName]);
                }
            }

            return sumInfo;
        }

        private void summaryCityGrid(List<GridMutCarriersCountInfo> gridInfoList, Dictionary<string, List<GridTypeName>> cityGridTypeNameDic)
        {
            foreach (string strCity in cityMutCarriersCountInfoDic.Keys)
            {
                foreach (GridTypeName gridKey in cityGridTypeNameDic[strCity])
                {
                    GridMutCarriersCountInfo sumInfoKey = new GridMutCarriersCountInfo();
                    sumInfoKey.StrCity = strCity;
                    sumInfoKey.StrGridType = gridKey.strGridType;
                    sumInfoKey.StrGridName = gridKey.strGridName;
                    foreach (GridTypeName gridTypeName in cityMutCarriersCountInfoDic[strCity].Keys)
                    {
                        GridTypeName gridTypeNameTmp = new GridTypeName();
                        gridTypeNameTmp.strGridType = gridTypeName.strGridType;
                        gridTypeNameTmp.strGridName = "汇总";
                        if (gridKey.Equals(gridTypeNameTmp))
                        {
                            GridKeySumInfo(ref sumInfoKey, strCity, gridTypeName);
                        }
                    }
                    sumInfoKey.ISN = gridInfoList.Count + 1;
                    gridInfoList.Add(sumInfoKey);
                }

            }
        }

        private void summaryCity(List<GridMutCarriersCountInfo> gridInfoList)
        {
            foreach (string strCity in cityMutCarriersCountInfoDic.Keys)
            {
                GridMutCarriersCountInfo sumInfoCity = new GridMutCarriersCountInfo();
                sumInfoCity.StrCity = strCity;
                sumInfoCity.StrGridType = "汇总";
                sumInfoCity.StrGridName = "汇总";
                foreach (GridTypeName gridTypeName in cityMutCarriersCountInfoDic[strCity].Keys)
                {
                    GridKeySumInfo(ref sumInfoCity, strCity, gridTypeName);
                }
                sumInfoCity.ISN = gridInfoList.Count + 1;
                gridInfoList.Add(sumInfoCity);
            }
        }
        #endregion
    }

    public class DIYSQLGridDownloadInfo : DIYSQLBase
    {
        private readonly string tableName;
        private readonly string strCarrierName;
        private readonly string strGridType;
        public override string Name
        {
            get { return "查询历史栅格数据"; }
        }
        public DIYSQLGridDownloadInfo(MainModel mainModel, string tableName, string strCarrierName
            , string strGridType)
            : base(mainModel)
        {
            this.tableName = tableName;
            this.strCarrierName = strCarrierName;
            this.strGridType = strGridType;
        }
        protected override string getSqlTextString()
        {
            return string.Format("select * from gd_grid_downLoad_speed_Info_{0} where 运营商 like '%{1}%' and 网格类型 like '%{2}%' "
                , this.tableName, this.strCarrierName,this.strGridType);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[29];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_String;
            rType[11] = E_VType.E_Int;
            rType[12] = E_VType.E_Int;
            rType[13] = E_VType.E_Float;
            rType[14] = E_VType.E_Float;
            rType[15] = E_VType.E_Float;
            rType[16] = E_VType.E_Float;
            rType[17] = E_VType.E_Float;
            rType[18] = E_VType.E_Float;
            rType[19] = E_VType.E_Float;
            rType[20] = E_VType.E_Int;
            rType[21] = E_VType.E_Int;
            rType[22] = E_VType.E_Float;
            rType[23] = E_VType.E_Float;
            rType[24] = E_VType.E_Float;
            rType[25] = E_VType.E_Float;
            rType[26] = E_VType.E_Float;
            rType[27] = E_VType.E_Float;
            rType[28] = E_VType.E_Float;
            return rType;
        }

        public Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoDic { get; set; }
            = new Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            int iCount = 1;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    iCount = fillData(iCount, package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private int fillData(int iCount, Package package)
        {
            try
            {
                GridDownLoadTimeSpeedInfo gridDownLoadTimeSpeedInfo = new GridDownLoadTimeSpeedInfo();
                gridDownLoadTimeSpeedInfo.StrCityInfo = package.Content.GetParamString();
                package.Content.GetParamString();//strPro
                package.Content.GetParamString();//strServ
                gridDownLoadTimeSpeedInfo.StrCarrName = package.Content.GetParamString();
                gridDownLoadTimeSpeedInfo.StrGridType = package.Content.GetParamString();
                gridDownLoadTimeSpeedInfo.StrGridName = package.Content.GetParamString();
                gridDownLoadTimeSpeedInfo.Itllng = package.Content.GetParamInt();
                gridDownLoadTimeSpeedInfo.Itllat = package.Content.GetParamInt();
                gridDownLoadTimeSpeedInfo.Icentlng = package.Content.GetParamInt();
                gridDownLoadTimeSpeedInfo.Icentlat = package.Content.GetParamInt();
                gridDownLoadTimeSpeedInfo.StrGirdCenterInfo = package.Content.GetParamString();
                gridDownLoadTimeSpeedInfo.StrMonth = this.tableName;
                gridDownLoadTimeSpeedInfo.D主队RSRP采样点数 = package.Content.GetParamInt();
                gridDownLoadTimeSpeedInfo.DTDDSampleNum = package.Content.GetParamInt();
                gridDownLoadTimeSpeedInfo.DTDDDownTime = package.Content.GetParamFloat();
                gridDownLoadTimeSpeedInfo.DTDDDownSize = package.Content.GetParamFloat();
                gridDownLoadTimeSpeedInfo.DTDDDownSpeed = package.Content.GetParamFloat() / 1000;
                gridDownLoadTimeSpeedInfo.D主队脱网标记 = package.Content.GetParamFloat();
                gridDownLoadTimeSpeedInfo.D主队占它网时长 = package.Content.GetParamFloat();
                package.Content.GetParamFloat();//TDD预留1
                package.Content.GetParamFloat();//TDD预留2
                gridDownLoadTimeSpeedInfo.D客队RSRP采样点数 = package.Content.GetParamInt();
                gridDownLoadTimeSpeedInfo.DFDDSampleNum = package.Content.GetParamInt();
                gridDownLoadTimeSpeedInfo.DFDDDownTime = package.Content.GetParamFloat();
                gridDownLoadTimeSpeedInfo.DFDDDownSize = package.Content.GetParamFloat();
                gridDownLoadTimeSpeedInfo.DFDDDownSpeed = package.Content.GetParamFloat() / 1000;
                gridDownLoadTimeSpeedInfo.D客队脱网标记 = package.Content.GetParamFloat();
                gridDownLoadTimeSpeedInfo.D客队占它网时长 = package.Content.GetParamFloat();
                package.Content.GetParamFloat();//FDD预留1
                package.Content.GetParamFloat();//FDD预留2
                CenterLongLat cll = new CenterLongLat(1.0 * gridDownLoadTimeSpeedInfo.Icentlng / 10000000
                    , 1.0 * gridDownLoadTimeSpeedInfo.Icentlat / 10000000);
                if (!gridDownLoadTimeSpeedInfoDic.ContainsKey(cll))
                {
                    gridDownLoadTimeSpeedInfoDic.Add(cll, gridDownLoadTimeSpeedInfo);
                }
                if (WaitBox.ProgressPercent >= 95)
                {
                    WaitBox.ProgressPercent = 1;
                }
                if (iCount++ % 20 == 0)
                {
                    WaitBox.ProgressPercent += 1;
                }
            }
            catch
            {
                //continue
            }

            return iCount;
        }
    }

    public class DIYGridTypeFromDB : DIYSQLBase
    {
        private readonly string tableName;
        private readonly string strCarrierName;
        public override string Name
        {
            get { return "查询已入库的图层类型"; }
        }
        public DIYGridTypeFromDB(MainModel mainModel, string tableName, string strCarrierName)
            : base(mainModel)
        {
            this.tableName = tableName;
            this.strCarrierName = strCarrierName;
        }
        protected override string getSqlTextString()
        {
            string strSQL = string.Format("select distinct 网格类型 from gd_grid_downLoad_speed_Info_{0} where 运营商 like '%{1}%' ", this.tableName, this.strCarrierName);
            return strSQL;
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_String;
            return rType;
        }

        public List<string> gridTypeList { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            gridTypeList = new List<string>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    try
                    {
                        string strGridType = package.Content.GetParamString();
                        if (!gridTypeList.Contains(strGridType))
                        {
                            gridTypeList.Add(strGridType);
                        }

                    }
                    catch
                    {
                        //continue
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
}
