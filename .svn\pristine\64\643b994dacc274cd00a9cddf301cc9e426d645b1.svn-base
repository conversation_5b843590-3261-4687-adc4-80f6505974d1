﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LtePlanningRedirectSummaryView
    {
        public int CellCount
        {
            get;
            set;
        }

        public int No2GCount
        {
            get;
            set;
        }

        public int NoRedirectCount
        {
            get;
            set;
        }

        public double No2GRate
        {
            get { return CellCount == 0 ? 0 : 1d * No2GCount / CellCount; }
        }

        public double NoRedirectRate
        {
            get { return CellCount == 0 ? 0 : 1d * NoRedirectCount / CellCount; }
        }
    }

    public class LtePlanningRedirectCellView : LtePlanningCellView
    {
        public LtePlanningRedirectCellView(LTECell lteCell)
            : base(lteCell)
        {
        }

        public LtePlanningRedirectType RedirectType { get; set; }

        public string RedirectDesc
        {
            get
            {
                return RedirectType == LtePlanningRedirectType.No2G ? "不含2G" : "无重定向";
            }
        }
    }

    public enum LtePlanningRedirectType
    {
        No2G,
        NoRedirect,
    }

    public class LtePlanningRedirectEvaluator
    {
        public LtePlanningRedirectEvaluator(MainModel mainModel)
        {
            this.mainModel = mainModel;
            this.cellViewList = new List<LtePlanningRedirectCellView>();

            this.mtStartEvtID = 885;
            this.moStartEvtID = 877;
            this.mtEndEvtID = 1002;
            this.moEndEvtID = 1001;
            this.tarMsgID = 1093625861;
        }

        public int No2GCount
        {
            get;
            private set;
        }

        public int NoRedirectCount
        {
            get;
            private set;
        }

        // 分析文件
        public void EvaluateFile()
        {
            foreach (DTFileDataManager fileDataManager in mainModel.DTDataManager.FileDataManagers)
            {
                tpList = fileDataManager.TestPoints;
                evtList = fileDataManager.Events;
                msgList = fileDataManager.Messages;

                int startEvtSn, endEvtSn;
                while (true)
                {
                    GetNextEventSN(out startEvtSn, out endEvtSn);
                    if (startEvtSn == -1 || endEvtSn == -1) // 未找到完整的事件对
                    {
                        break;
                    }

                    addValidCellView(startEvtSn, endEvtSn);
                }
            }
        }

        private void addValidCellView(int startEvtSn, int endEvtSn)
        {
            TestPoint tp = GetTestPoint(startEvtSn);
            if (tp != null)                         // 未找到开始事件对应的采样点
            {
                LTECell lteCell = GetLteCell(tp);
                if (lteCell != null)                    // 未能匹配到小区
                {
                    LtePlanningRedirectCellView cellView = new LtePlanningRedirectCellView(lteCell);
                    Message msg = GetTargetMessage(startEvtSn, endEvtSn);
                    if (msg == null)                // 无重定向小区
                    {
                        cellView.RedirectType = LtePlanningRedirectType.NoRedirect;
                        cellViewList.Add(cellView);
                    }
                    else if (!IsFound2GFreq(msg))   // 有重定向但不含2G
                    {
                        cellView.RedirectType = LtePlanningRedirectType.No2G;
                        cellViewList.Add(cellView);
                    }
                    // 有重定向且包含2G频点
                }
            }
        }

        public List<LtePlanningRedirectCellView> GetResult()
        {
            List<LtePlanningRedirectCellView> result = new List<LtePlanningRedirectCellView>();
            Dictionary<LTECell, LtePlanningRedirectCellView> no2GDic = new Dictionary<LTECell,LtePlanningRedirectCellView>();
            Dictionary<LTECell, LtePlanningRedirectCellView> noRedirectDic = new Dictionary<LTECell,LtePlanningRedirectCellView>();
            foreach (LtePlanningRedirectCellView cellView in cellViewList)
            {
                if (cellView.RedirectType == LtePlanningRedirectType.No2G && !no2GDic.ContainsKey(cellView.LteCell))
                {
                    no2GDic.Add(cellView.LteCell, cellView);
                }
                else if (cellView.RedirectType == LtePlanningRedirectType.NoRedirect && !noRedirectDic.ContainsKey(cellView.LteCell))
                {
                    noRedirectDic.Add(cellView.LteCell, cellView);
                }
            }
            cellViewList.Clear();
            No2GCount = no2GDic.Count;
            NoRedirectCount = noRedirectDic.Count;
            result.AddRange(no2GDic.Values);
            result.AddRange(noRedirectDic.Values);
            return result;
        }

        // 查找事件对
        private void GetNextEventSN(out int startSn, out int endSn)
        {
            int startID = -1;
            startSn = endSn = -1;
            for (; evtCursor < evtList.Count; ++evtCursor)
            {
                bool isEnd = getSn(ref startSn, ref endSn, ref startID);
                if (isEnd)
                {
                    break;
                }
            }

            if (startSn == -1 || endSn == -1)
            {
                startSn = endSn = -1;
            }
        }

        private bool getSn(ref int startSn, ref int endSn, ref int startID)
        {
            Event evt = evtList[evtCursor];
            if (evt.ID == moStartEvtID || evt.ID == mtStartEvtID)
            {
                startSn = evt.SN;
                startID = evt.ID;
            }
            else if (evt.ID == moEndEvtID)
            {
                if (startID == -1 || startID == mtStartEvtID) // 未找到开始或者开始不匹配
                {
                    startID = startSn = -1;
                }
                else
                {
                    endSn = evt.SN;
                    return true;
                }
            }
            else if (evt.ID == mtEndEvtID)
            {
                if (startID == -1 || startID == moStartEvtID) // 未找到开始或者开始不匹配
                {
                    startID = startSn = -1;
                }
                else
                {
                    endSn = evt.SN;
                }
            }
            return false;
        }

        // 在事件对范围内查找消息
        private Message GetTargetMessage(int startEvtSn, int endEvtSn)
        {
            for (; msgCursor < msgList.Count; ++msgCursor)
            {
                Message msg = msgList[msgCursor];
                if (msg.SN < startEvtSn - 1)
                {
                    continue;
                }
                else if (msg.SN > endEvtSn - 1)
                {
                    break;
                }

                if (msg.ID == tarMsgID)
                {
                    return msg;
                }
            }
            return null;
        }

        // 由开始事件匹配对应采样点
        private TestPoint GetTestPoint(int startEvtSn)
        {
            TestPoint retTp = null;
            for (;tpCursor < tpList.Count; ++tpCursor)
            {
                TestPoint curTp = tpList[tpCursor];
                if (curTp.SN <= startEvtSn)
                {
                    retTp = curTp;
                }
                else if (curTp.SN > startEvtSn)
                {
                    break;
                }
            }
            return retTp;
        }

        // 由采样点匹配小区
        private LTECell GetLteCell(TestPoint tp)
        {
            LtePlanningTestPoint ptp = new LtePlanningTestPoint(tp);
            return ptp.MainCell;
        }

        // 从消息中找出2G频点
        private bool IsFound2GFreq(Message msg)
        {
            MessageWithSource msgSrc = msg as MessageWithSource;
            int arfcnCount = 0;
            MessageDecodeHelper.StartDissect(msg.Direction,msgSrc.Source, msgSrc.Source.Length, msgSrc.ID);
            MessageDecodeHelper.GetSingleSInt("lte-rrc.explicitListOfARFCNs", ref arfcnCount);
            return arfcnCount != 0;
        }

        private readonly int mtStartEvtID;
        private readonly int mtEndEvtID;
        private readonly int moStartEvtID;
        private readonly int moEndEvtID;
        private readonly int tarMsgID;

        private int evtCursor;
        private int msgCursor;
        private int tpCursor;
        private List<TestPoint> tpList;
        private List<Event> evtList;
        private List<Message> msgList;

        private readonly MainModel mainModel;
        private readonly List<LtePlanningRedirectCellView> cellViewList;
    }
}
