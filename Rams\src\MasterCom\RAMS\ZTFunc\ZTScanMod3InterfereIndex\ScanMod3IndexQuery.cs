﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Frame;

namespace MasterCom.RAMS.ZTFunc
{
    public class ScanMod3IndexQuery : DIYDoWithTestPointQueryBase
    {
        public static ScanMod3IndexQuery Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ScanMod3IndexQuery();
                }
                return instance;
            }
        }

        public override string Name
        {
            get { return "扫频模三干扰指数"; }
        }

        public override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23016, "道路干扰指数");
        }

        public override List<string> QueryColumns
        {
            get { return queryColumns; }
        }

        public override bool isValidCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new ScanMod3IndexSettingForm();
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            this.cond = setForm.GetCondition();
            return true;
        }

        public override void DoWithTestPoint(TestPoint tp)
        {
            double topRsrp = 0;
            int topEarfcn = -1;

            // 将一个采样点按频点分类
            for (int i = 0; i < 50; ++i)
            {
                float? rsrp = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
                int? earfcn = (int?)tp["LTESCAN_TopN_EARFCN", i];
                int? pci = (int?)(short?)tp["LTESCAN_TopN_PCI", i];
                if (rsrp == null || earfcn == null || pci == null)
                {
                    break;
                }
                if (i == 0)
                {
                    topEarfcn = (int)earfcn;
                    topRsrp = (double)rsrp;
                }

                ScanMod3IndexSample sample = null;
                if (!tmpSampleDic.TryGetValue((int)earfcn, out sample))
                {
                    sample = new ScanMod3IndexSample(tp);
                    sample.Earfcn = (int)earfcn;
                    sample.TopRsrp = topRsrp;
                    sample.TopEarfcn = topEarfcn;
                    tmpSampleDic.Add(sample.Earfcn, sample);
                }
                sample.RsrpList.Add((double)rsrp);
                sample.PciList.Add((int)pci);

                ScanMod3IndexEarfcn freq = null;
                if (!freqDic.TryGetValue((int)earfcn, out freq))
                {
                    freq = new ScanMod3IndexEarfcn((int)earfcn);
                    freqDic.Add(freq.Earfcn, freq);
                }
                freq.AddSample(sample);
            }

            sumResultInfo(tp);

            tmpSampleDic.Clear();
        }

        private void sumResultInfo(TestPoint tp)
        {
            // 按频点分类后，汇总到小区
            foreach (KeyValuePair<int, ScanMod3IndexSample> kvp in tmpSampleDic)
            {
                List<ScanMod3IndexCell> cellList = null;
                kvp.Value.GetResult(this.cond, out cellList);

                foreach (ScanMod3IndexCell lteCell in cellList)
                {
                    ScanMod3IndexCellView cellView = null;
                    if (!cellDic.TryGetValue(lteCell.LteCell, out cellView))
                    {
                        cellView = new ScanMod3IndexCellView(lteCell.LteCell);
                        cellDic.Add(lteCell.LteCell, cellView);
                    }
                    cellView.AddTestPoint(tp, lteCell.Rsrp, lteCell.IsMainCell, lteCell.IsInterfered);
                }
            }
        }

        public override void GetResultAfterQuery()
        {
            //
        }

        public override void FireShowResult()
        {
            List<ScanMod3IndexCellView> cellViews = new List<ScanMod3IndexCellView>(cellDic.Values);
            List<ScanMod3IndexSampleView> sampleViews = new List<ScanMod3IndexSampleView>();
            foreach (ScanMod3IndexEarfcn earfcn in freqDic.Values)
            {
                List<ScanMod3IndexSampleView> views = earfcn.GetResult();
                sampleViews.AddRange(views);
            }

            ScanMod3IndexResultForm resultForm = MainModel.GetObjectFromBlackboard(typeof(ScanMod3IndexResultForm).FullName) as ScanMod3IndexResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new ScanMod3IndexResultForm(MainModel);
            }
            resultForm.FillData(sampleViews, cellViews);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }

        public override void Clear()
        {
            freqDic.Clear();
            cellDic.Clear();
        }

        private ScanMod3IndexQuery()
        {
        }

        private ScanMod3IndexSettingForm setForm;

        private ScanMod3IndexCondition cond;

        private readonly Dictionary<int, ScanMod3IndexEarfcn> freqDic = new Dictionary<int, ScanMod3IndexEarfcn>();

        private readonly Dictionary<LTECell, ScanMod3IndexCellView> cellDic = new Dictionary<LTECell, ScanMod3IndexCellView>();

        private readonly Dictionary<int, ScanMod3IndexSample> tmpSampleDic = new Dictionary<int, ScanMod3IndexSample>();

        #region private List<string> queryColumns
        private readonly List<string> queryColumns = new List<string>()
        {
            "isampleid",
            "itime",
            "ilongitude",
            "ilatitude",
            "LTESCAN_TopN_PSS_RP", 
            "LTESCAN_TopN_EARFCN",
            "LTESCAN_TopN_PCI",
            "LTESCAN_TopN_CELL_Specific_RSRP",
            "LTESCAN_TopN_CELL_Specific_RSSINR",
        };
        #endregion

        private static ScanMod3IndexQuery instance;
    }

    public class ScanMod3IndexCondition
    {
        /// <summary>
        /// 与最强相差该值范围内的作为主服小区
        /// </summary>
        public double MainCellDiff { get; set; }

        /// <summary>
        /// 与主服相差该值范围内的作为邻区
        /// </summary>
        public double NbCellDiff { get; set; }

        /// <summary>
        /// 第一强小于等于该值作为无效点
        /// </summary>
        public double InvalidRsrp { get; set; }
    }

    public class ScanMod3IndexEarfcn
    {
        public ScanMod3IndexEarfcn(int earfcn)
        {
            this.Earfcn = earfcn;
        }

        public int Earfcn
        {
            get;
            private set;
        }

        public void AddSample(ScanMod3IndexSample sample)
        {
            if (!sampleDic.ContainsKey(sample.TestPoint))
            {
                sampleDic.Add(sample.TestPoint, sample);
            }
        }

        public List<ScanMod3IndexSampleView> GetResult()
        {
            List<ScanMod3IndexSampleView> sampleViews = new List<ScanMod3IndexSampleView>();
            foreach (ScanMod3IndexSample sample in sampleDic.Values)
            {
                sampleViews.Add(new ScanMod3IndexSampleView(sample));
            }
            return sampleViews;
        }

        private readonly Dictionary<TestPoint, ScanMod3IndexSample> sampleDic = new Dictionary<TestPoint, ScanMod3IndexSample>();
    }

    /// <summary>
    /// 标识一个采样点里面某个频点的信息
    /// </summary>
    public class ScanMod3IndexSample
    {
        public ScanMod3IndexSample(TestPoint tp)
        {
            this.TestPoint = tp;
            this.RsrpList = new List<double>();
            this.PciList = new List<int>();
        }

        public TestPoint TestPoint
        {
            get;
            private set;
        }

        public double TopRsrp
        {
            get;
            set;
        }

        public int TopEarfcn
        {
            get;
            set;
        }

        public int Earfcn
        {
            get;
            set;
        }

        public List<double> RsrpList
        {
            get;
            private set;
        }

        public List<int> PciList
        {
            get;
            private set;
        }

        public double InterfereIndex
        {
            get;
            private set;
        }

        public bool IsInvalid
        {
            get;
            private set;
        }

        /// <summary>
        /// 从该频点下不同扰码匹配出小区
        /// 同时判断该频点下不同扰码是否存在干扰
        /// </summary>
        /// <param name="cond"></param>
        /// <param name="cellList"></param>
        public virtual void GetResult(ScanMod3IndexCondition cond, out List<ScanMod3IndexCell> cellList)
        {
            cellList = new List<ScanMod3IndexCell>();

            // 无效点判断
            if (TopRsrp <= cond.InvalidRsrp)
            {
                IsInvalid = true;
                return;
            }
            IsInvalid = false;

            // 主服匹配和干扰判断
            int mainCnt = 0, interCnt = 0;
            for (int i = 0; i < PciList.Count; ++i)
            {
                if (TopRsrp - RsrpList[i] > cond.MainCellDiff)
                {
                    break; // 非主服
                }

                // 干扰判断
                mainCnt += 1;
                bool isInterfered = judgeIsInterfered(cond, i);
                interCnt += isInterfered ? 1 : 0;

                // 主服匹配
                LTECell mainCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(this.TestPoint.DateTime, this.Earfcn, this.PciList[i], this.TestPoint.Longitude, this.TestPoint.Latitude);
                if (mainCell != null)
                {
                    ScanMod3IndexCell cellItem = new ScanMod3IndexCell(mainCell);
                    cellItem.IsMainCell = true;
                    cellItem.IsInterfered = isInterfered;
                    cellItem.Rsrp = RsrpList[i];
                    cellList.Add(cellItem);
                }
            }
            this.InterfereIndex = mainCnt == 0 ? 0 : 1d * interCnt / mainCnt;
        }

        private bool judgeIsInterfered(ScanMod3IndexCondition cond, int i)
        {
            bool isInterfered = false;
            for (int j = 0; j < PciList.Count; ++j)
            {
                if (i == j)
                {
                    continue;
                }
                else if (RsrpList[i] - RsrpList[j] > cond.NbCellDiff) // 非干扰覆盖度内
                {
                    break;
                }
                else if (PciList[i] % 3 != PciList[j] % 3)            // 没有模干扰
                {
                    continue;
                }
                isInterfered = true;
                break;
            }

            return isInterfered;
        }
    }

    /// <summary>
    /// 代表一个采样点里面某个频点扰码匹配出来的小区
    /// </summary>
    public class ScanMod3IndexCell
    {
        public ScanMod3IndexCell(LTECell lteCell)
        {
            this.LteCell = lteCell;
        }

        public LTECell LteCell
        {
            get;
            private set;
        }

        public bool IsMainCell
        {
            get;
            set;
        }

        public bool IsInterfered
        {
            get;
            set;
        }

        public double Rsrp
        {
            get;
            set;
        }
    }

    public class ScanMod3IndexCellView : IComparable<ScanMod3IndexCellView>
    {
        public ScanMod3IndexCellView(LTECell lteCell)
        {
            TestPoints = new List<TestPoint>();
            this.LteCell = lteCell;
            this.Tac = lteCell.TAC;
            this.Eci = lteCell.ECI;
            this.CellID = lteCell.SCellID;
            this.Earfcn = lteCell.EARFCN;
            this.Pci = lteCell.PCI;
            this.CellName = lteCell.Name;
        }

        public void AddTestPoint(TestPoint tp, double rsrp, bool isMainSample, bool isInterfered)
        {
            TestPoints.Add(tp);
            sumRsrp += rsrp;
            MainSampleCount += isMainSample ? 1 : 0;
            MainInterfereCount += isInterfered ? 1 : 0;
        }

        public int CompareTo(ScanMod3IndexCellView other)
        {
            if (this == other || (this.MainInterfereCount == other.MainSampleCount && this.InterfereIndex == other.InterfereIndex))
            {
                return 0;
            }
            else if (this.MainSampleCount == other.MainSampleCount)
            {
                return this.InterfereIndex > other.InterfereIndex ? 1 : 0;
            }
            else
            {
                return this.MainSampleCount > other.MainSampleCount ? 1 : 0;
            }
        }

        public LTECell LteCell
        {
            get;
            private set;
        }

        public int MainSampleCount
        {
            get;
            private set;
        }

        public int MainInterfereCount
        {
            get;
            private set;
        }

        public double AvgRsrp
        {
            get { return TestPoints.Count == 0 ? 0 : sumRsrp / TestPoints.Count; }
        }

        public double InterfereIndex
        {
            get { return MainSampleCount == 0 ? 0 : 1d * MainInterfereCount / MainSampleCount; }
        }

        public string CellName
        {
            get;
            private set;
        }

        public int Tac
        {
            get;
            private set;
        }

        public int Pci
        {
            get;
            private set;
        }

        public int Eci
        {
            get;
            private set;
        }

        public int Earfcn
        {
            get;
            private set;
        }

        public int CellID
        {
            get;
            private set;
        }

        public List<TestPoint> TestPoints
        {
            get;
            private set;
        }

        private double sumRsrp;
    }

    public class ScanMod3IndexSampleView
    {
        public ScanMod3IndexSampleView(ScanMod3IndexSample sample)
        {
            this.Earfcn = sample.Earfcn;
            this.InterfereIndex = sample.InterfereIndex;
            this.IsInvalid = sample.IsInvalid ? "是" : "否";
            this.TopEarfcn = sample.TopEarfcn;
            this.TopRsrp = Math.Round(sample.TopRsrp, 2);

            this.TestPoint = sample.TestPoint;
            this.TimeString = this.TestPoint.DateTimeStringWithMillisecond;
            this.Longitude = this.TestPoint.Longitude;
            this.Latitude = this.TestPoint.Latitude;

            StringBuilder sb = new StringBuilder();
            foreach (double rsrp in sample.RsrpList)
            {
                sb.Append(Math.Round(rsrp, 2).ToString() + "|");
            }
            this.RsrpListString = sb.Remove(sb.Length - 1, 1).ToString();

            sb.Remove(0, sb.Length);
            foreach (int pci in sample.PciList)
            {
                sb.Append((pci % 3).ToString() + "|");
            }
            this.Mod3ListString = sb.Remove(sb.Length - 1, 1).ToString();
        }

        public TestPoint TestPoint
        {
            get;
            private set;
        }

        public double TopRsrp
        {
            get;
            private set;
        }

        public int TopEarfcn
        {
            get;
            private set;
        }

        public string TimeString
        {
            get;
            private set;
        }

        public double Longitude
        {
            get;
            private set;
        }

        public double Latitude
        {
            get;
            private set;
        }

        public string RsrpListString
        {
            get;
            private set;
        }

        public string Mod3ListString
        {
            get;
            private set;
        }

        public double InterfereIndex
        {
            get;
            private set;
        }

        public string IsInvalid
        {
            get;
            private set;
        }

        public int Earfcn
        {
            get;
            private set;
        }
    }
}
