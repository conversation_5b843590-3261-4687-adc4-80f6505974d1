﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class SameEarfcnPciQuery : QueryBase
    {
        public SameEarfcnPciQuery(MainModel mm)
            : base(mm)
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override string Name
        {
            get { return "LTE同频同PCI"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19039, this.Name);
        }

        private SameEarfcnPciResultForm form { get; set; }
        protected override void query()
        {
            SameEarfcnPciSetConditionDlg conditionDlg = SameEarfcnPciSetConditionDlg.GetDlg();

            while (true)
            {
                if (conditionDlg.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                if ((!conditionDlg.IsFirstNBCell
                     && !conditionDlg.isSecondNBCell
                     && !conditionDlg.IsDisAngle))
                {
                    MessageBox.Show("请至少选择一种核查方式！");
                }
                else
                {
                    break;
                }
            }

            MainModel.CellManager.GetLTENBCellInfo();

            form = MainModel.GetObjectFromBlackboard(typeof(SameEarfcnPciResultForm).FullName) as SameEarfcnPciResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new SameEarfcnPciResultForm(MainModel, conditionDlg);
                form.Owner = MainModel.MainForm;
            }
            form.Visible = true;
            form.BringToFront();
        }
    }
}
