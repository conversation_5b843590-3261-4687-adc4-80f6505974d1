﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MapWinGIS;
using MasterCom.RAMS.ZTFunc;
using System.Data;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRailwayAna : DIYQueryFileInfo
    {
        public ZTRailwayAna(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "高铁专网小区占网统计"; }
        }
        Dictionary<string, Dictionary<string, ZTRailWayZWInfo>> dicFileCity= null;
        List<ZTRailWayZWInfo> ZTList = null;
        RailWayZWCell zwCell = null;
        Dictionary<LACCIKey, string> LacCityNameMap = null;
        Dictionary<string, string> specialLac = null;
        Dictionary<LACCIKey, string> LacCICityNameMap = null;

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20048, this.Name);
        }

        protected bool getCondition()
        {
            ZTRailWayCondition conditionForm = new ZTRailWayCondition();
            if (conditionForm.ShowDialog() == DialogResult.OK)
            {
                zwCell = conditionForm.zwCell;
                return zwCell != null && zwCell.isExistData;
            }
            return false;
        }

        protected void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTRailWayResultForm).FullName);
            ZTRailWayResultForm form = obj == null ? null : obj as ZTRailWayResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new ZTRailWayResultForm(MainModel);
            }
            form.FillData(ZTList);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        private void clearData()
        {
            zwCell.Dispose();
            ZTList = null;
            LacCityNameMap = null;
            specialLac = null;
            LacCICityNameMap = null;
        }
        protected override void query()
        {
            if (!getCondition())
                return;
            WaitBox.CanCancel = true;
            WaitBox.Show("正在查询地市Lac信息...", queryBefore);
            base.query();
            WaitBox.CanCancel = true;
            WaitBox.Show("专网信息导入成功，开始分析文件...", analyseFiles);
            doSomeAfterResult();
            fireShowForm();
            clearData();
        }

        private void queryBefore()
        {
            dicFileCity = new Dictionary<string, Dictionary<string, ZTRailWayZWInfo>>();
            ZTList = new List<ZTRailWayZWInfo>();
            LacCityNameMap = new Dictionary<LACCIKey, string>();
            specialLac = new Dictionary<string, string>();
            LacCICityNameMap = new Dictionary<LACCIKey, string>();

            WaitBox.ProgressPercent = 20;
            WaitBox.Text = "正在查询地市Lac信息...";
            DIYSQLQueryLacInfo queryLac = new DIYSQLQueryLacInfo(mainModel, 2);
            queryLac.Query();
            foreach (LACCIKey key in queryLac.LacCICityNameMap.Keys)
            {
                if (!LacCityNameMap.ContainsKey(key))
                    LacCityNameMap.Add(key, queryLac.LacCICityNameMap[key]);
            }
            WaitBox.ProgressPercent = 60;
            foreach (LACCIKey lacci in zwCell.dicZWCell.Keys)
            {
                if (!LacCityNameMap.ContainsKey(lacci))
                    LacCityNameMap.Add(lacci, zwCell.dicZWCell[lacci]);
            }

            DIYSQLQueryLacCICityName lacCiQuery = new DIYSQLQueryLacCICityName(mainModel, 2);
            lacCiQuery.Query();
            foreach (LACCIKey key in lacCiQuery.LacCICityNameMap.Keys)
            {
                if (!LacCICityNameMap.ContainsKey(key))
                    LacCICityNameMap.Add(key, lacCiQuery.LacCICityNameMap[key]);
            }

            #region 特殊Lac地市归属
            specialLac.Add("460-00-9052-52304", "广州");
            specialLac.Add("460-00-9052-50037", "广州");
            specialLac.Add("460-00-9052-49807", "广州");
            specialLac.Add("460-00-9052-51641", "广州");
            specialLac.Add("460-00-9052-51568", "广州");
            specialLac.Add("460-00-9052-48577", "广州");
            specialLac.Add("460-00-9052-37262", "广州");

            specialLac.Add("460-00-691272-1", "广州");
            specialLac.Add("460-00-691273-1", "广州");
            specialLac.Add("460-00-691274-1", "广州");
            specialLac.Add("460-00-691279-2", "广州");
            specialLac.Add("460-00-691280-1", "广州");
            specialLac.Add("460-00-691279-1", "广州");
            specialLac.Add("460-00-691279-3", "广州");
            specialLac.Add("460-00-691277-1", "广州");
            specialLac.Add("460-00-691278-1", "广州");
            specialLac.Add("460-00-691276-1", "广州");
            specialLac.Add("460-00-691275-1", "广州");

            specialLac.Add("460-00-91010-1", "佛山");
            specialLac.Add("460-00-91009-1", "佛山");
            specialLac.Add("460-00-91008-1", "佛山");
            specialLac.Add("460-00-91032-1", "佛山");
            specialLac.Add("460-00-91012-1", "佛山");
            specialLac.Add("460-00-91014-1", "佛山");
            specialLac.Add("460-00-91014-2", "佛山");
            specialLac.Add("460-00-91013-1", "佛山");
            specialLac.Add("460-00-91013-2", "佛山");
            specialLac.Add("460-00-9052-43187", "佛山");
            specialLac.Add("460-00-9052-43181", "佛山");
            specialLac.Add("460-00-9052-43189", "佛山");
            specialLac.Add("460-00-9052-43177", "佛山");
            specialLac.Add("460-00-9052-43178", "佛山");
            specialLac.Add("460-00-9052-43186", "佛山");
            #endregion
            WaitBox.Close();
        }

        Dictionary<string, string> dic = null;
        Dictionary<string, string> dicIn = null;
        private void analyseFiles()
        {
            try
            {
                dic = new Dictionary<string, string>();
                dicIn = new Dictionary<string, string>();
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                int iloop = 0;
                foreach (FileInfo fileInfo in files)
                {
                    WaitBox.Text = "正在分析文件(" + (++iloop) + "/" + files.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        private void replay()
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            condition.Geometorys = Condition.Geometorys;

            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
            query.FilterSampleByRegion = false;
            query.FilterEventByRegion = false;
            query.IncludeTestPoint = true;
            query.IncludeEvent = false;
            query.IncludeMessage = false;
            query.SetQueryCondition(condition);
            query.Query();
            doStatWithQuery();
            MainModel.ClearDTData();
        }

        protected void doStatWithQuery()
        {
            try
            {
                string strNet = "";
                foreach (DTFileDataManager fi in MainModel.DTDataManager.FileDataManagers)
                {
                    if (!dicFileCity.ContainsKey(fi.FileName))
                    {
                        dicFileCity[fi.FileName] = new Dictionary<string, ZTRailWayZWInfo>();
                        dic[fi.FileName] = "";
                        dicIn[fi.FileName] = "";
                    }
                    ServiceType ser = (ServiceType)fi.ServiceType;
                    if (ser == ServiceType.LTE_TDD_DATA || ser == ServiceType.LTE_TDD_IDLE
                        || ser == ServiceType.LTE_FDD_DATA || ser == ServiceType.LTE_FDD_IDLE)
                        strNet = "LTE";
                    else
                        strNet = "GSM";
                    Dictionary<string, ZTRailWayZWInfo> cityStat = dicFileCity[fi.FileName];
                    List<TestPoint> testPointList = fi.TestPoints;

                    dealTestPoints(strNet, fi, cityStat, testPointList);
                    foreach (ZTRailWayZWInfo rwInfo in dicFileCity[fi.FileName].Values)
                    {
                        ZTList.Add(rwInfo);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealTestPoints(string strNet, DTFileDataManager fi, Dictionary<string, ZTRailWayZWInfo> cityStat, List<TestPoint> testPointList)
        {
            string cgi = "", strCity = "", preCity = "";
            int? Lac, CI;
            StringBuilder sb = new StringBuilder();
            StringBuilder sbIn = new StringBuilder();
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint tp = testPointList[i];
                getLacCi(out Lac, out CI, tp);

                bool isExist = false;
                if (Lac != null)
                {
                    isExist = judgeIsExist(strNet, fi, ref cgi, ref strCity, Lac, CI, tp);
                }
                if (isExist)
                {
                    LACCIKey lacci = new LACCIKey(strNet == "LTE" ? -10000000 : (int)Lac, (int)CI, strNet);
                    long lTimeSpan;
                    if (i > 0)
                    {
                        lTimeSpan = tp.lTimeWithMillsecond - testPointList[i - 1].lTimeWithMillsecond;
                    }
                    else
                    {
                        lTimeSpan = 0;
                    }

                    setCityStat(fi, cityStat, strCity, ref preCity, tp, lacci, lTimeSpan);
                    cgi = "";
                }
            }
            dic[fi.FileName] = sb.ToString();
            dicIn[fi.FileName] = sbIn.ToString();
        }

        private void  setCityStat(DTFileDataManager fi, Dictionary<string, ZTRailWayZWInfo> cityStat, string strCity, 
            ref string preCity, TestPoint tp, LACCIKey lacci, long lTimeSpan)
        {
            if (!string.IsNullOrEmpty(strCity))
            {
                if (!cityStat.ContainsKey(strCity))
                {
                    cityStat[strCity] = new ZTRailWayZWInfo(fi);
                    cityStat[strCity].City = strCity;
                }
                cityStat[strCity].iTotalSample++;
                cityStat[strCity].lTotalTime += lTimeSpan;
                if (zwCell.isExistCell(lacci))   //是否专网小区
                {
                    addZWCell(cityStat, strCity, preCity, lTimeSpan);
                }
                else
                {
                    addNotZWCell(cityStat, strCity, preCity, tp);
                }
                preCity = strCity;
                cityStat[strCity].AddTime(tp);
            }
        }

        private static void addZWCell(Dictionary<string, ZTRailWayZWInfo> cityStat, string strCity, string preCity, long lTimeSpan)
        {
            cityStat[strCity].iZWTotalSample++;
            cityStat[strCity].lZWTotalTime += lTimeSpan;
            cityStat[strCity].Add("1");
            if (!string.IsNullOrEmpty(preCity) && preCity != strCity)
            {
                cityStat[preCity].Add("+" + strCity); //地市来回切换的情况，序列不应连接，造成多次统计
            }
        }

        private static void addNotZWCell(Dictionary<string, ZTRailWayZWInfo> cityStat, string strCity, string preCity, TestPoint tp)
        {
            if (!string.IsNullOrEmpty(preCity) && preCity != strCity)
            {
                cityStat[preCity].Add("0");
                cityStat[preCity].Add("+" + strCity); //地市来回切换的情况，序列不应连接，造成多次统计
            }
            string order = cityStat[strCity].strArray;
            if (order.EndsWith("1"))
            {
                cityStat[strCity].dtList.Add(tp.DateTime);
            }
            cityStat[strCity].Add("0");
        }

        private bool judgeIsExist(string strNet, DTFileDataManager fi, ref string cgi, ref string strCity, int? Lac, int? CI, TestPoint tp)
        {
            bool isExist = false;
            LACCIKey lacci = new LACCIKey((int)Lac, -10000000, strNet);
            if (CI != null)
            {
                cgi = RailWayZWCell.GetCGI((int)Lac, (int)CI);
                dic[fi.FileName] += string.Format("|{0}.000,{1},{2}", tp.DateTime.ToString("yyyy-MM-dd HH:mm:ss"), Lac, CI);
                if (LacCityNameMap.ContainsKey(lacci) || specialLac.ContainsKey(cgi) || isValidTestPoint(tp)
                    || zwCell.isExistCell(new LACCIKey(strNet == "LTE" ? -10000000 : (int)Lac, (int)CI, strNet)))  //不在区域内过滤掉
                {
                    dicIn[fi.FileName] += string.Format("|{0}.000,{1},{2}", tp.DateTime.ToString("yyyy-MM-dd HH:mm:ss"), Lac, CI);
                    strCity = GetCityName(Lac, CI, cgi, strNet);
                    isExist = true;
                }
            }
            else
            {
                dic[fi.FileName] += string.Format("|{0}.000,{1},{2}", tp.DateTime.ToString("yyyy-MM-dd HH:mm:ss"), Lac, -10000000);
                if (LacCityNameMap.ContainsKey(lacci) || isValidTestPoint(tp))  //不在区域内过滤掉
                {
                    dicIn[fi.FileName] += string.Format("|{0}.000,{1},{2}", tp.DateTime.ToString("yyyy-MM-dd HH:mm:ss"), Lac, -10000000);
                    strCity = GetCityName(Lac, CI, cgi, strNet);
                    isExist = true;
                }
            }
            return isExist;
        }

        private void getLacCi(out int? Lac, out int? CI, TestPoint tp)
        {
            Lac = (int?)tp["lte_SCell_LAC"];
            if (Lac == null)
            {
                Lac = (int?)tp["LAC"];
            }

            CI = (int?)tp["lte_SCell_CI"];
            if (CI == null)
            {
                CI = (int?)tp["lte_ECI"];
            }
            if (CI == null)
            {
                CI = (int?)tp["lte_gsm_SC_CI"];
            }
            if (CI == null)
            {
                CI = (int?)tp["CI"];
            }
        }

        private string GetCityName(int? Lac, int? CI, string cgi, string strNet)
        {
            string strCity = "";
            LACCIKey lacci = new LACCIKey(Lac != null ? (int)Lac : -10000000, CI != null ? (int)CI : -10000000, strNet);
            LACCIKey lac = new LACCIKey(Lac != null ? (int)Lac : -10000000, -10000000, strNet);
            if (LacCICityNameMap.ContainsKey(lacci))
            {
                strCity = LacCICityNameMap[lacci];
            }
            else if (LacCityNameMap.ContainsKey(lac))
            {
                strCity = LacCityNameMap[lac];
            }
            else if (zwCell.isExistCell(lacci))
            {
                strCity = zwCell.dicZWCell[lacci];
            }
            if (!string.IsNullOrEmpty(cgi) && specialLac.ContainsKey(cgi))
                strCity = specialLac[cgi];
            return strCity;
        }
        private bool isValidTestPoint(TestPoint testPoint)
        {
            if (Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return true;
            }
            return false;
        }

        private void doSomeAfterResult()
        {
            Dictionary<string, ZTRailWayZWInfo> dicCityAreaResult = new Dictionary<string, ZTRailWayZWInfo>();
            Dictionary<string, ZTRailWayZWInfo> dicCityResult = new Dictionary<string, ZTRailWayZWInfo>();
            
            ZTRailWayZWInfo all = new ZTRailWayZWInfo();
            foreach (ZTRailWayZWInfo result in ZTList)
            {
                all.Add(result);
                string key = result.strAreaType + result.City;
                ZTRailWayZWInfo statAreaCity;
                if (!dicCityAreaResult.TryGetValue(key, out statAreaCity))
                {
                    statAreaCity = new ZTRailWayZWInfo();
                    statAreaCity.FileName = "汇总";
                    statAreaCity.strAreaType = result.strAreaType;
                    statAreaCity.strNetType = "全部";
                    statAreaCity.City = result.City;
                }
                statAreaCity.Add(result);
                dicCityAreaResult[key] = statAreaCity;

                ZTRailWayZWInfo statCity;
                key = result.City;
                if (!dicCityResult.TryGetValue(key, out statCity))
                {
                    statCity = new ZTRailWayZWInfo();
                    statCity.FileName = "汇总";
                    statCity.strAreaType = "全部";
                    statCity.strNetType = "全部";
                    statCity.City = result.City;
                }
                statCity.Add(result);
                dicCityResult[key] = statCity;
            }
            all.FileName = "全部";
            all.strAreaType = "全部";
            all.strNetType = "全部";
            all.City = "全部";
            ZTList.AddRange(dicCityAreaResult.Values);
            ZTList.AddRange(dicCityResult.Values);
            ZTList.Add(all);
        }
    }

    public class ZTRailWayZWInfo
    {
        public string FileName { get; set; } = "";
        public string City { get; set; } = "";
        public string strNetType { get; set; } = "";
        public string strAreaType { get; set; } = "";
        public int iTotalSample { get; set; } = 0;
        public long lTotalTime { get; set; } = 0;

        public double dTestTime { get; set; } = 0;

        public string STime
        {
            get
            {
                if (isTime == 0)
                    return "";
                return JavaDate.GetDateTimeFromMilliseconds(isTime * 1000L).ToString();
            }
        }
        private int isTime = 0;
        public string ETime
        {
            get
            {
                if (ieTime == 0)
                    return "";
                return JavaDate.GetDateTimeFromMilliseconds(ieTime * 1000L).ToString();
            }
        }
        private int ieTime = 0;

        public int iZWTotalSample { get; set; } = 0;
        public long lZWTotalTime { get; set; } = 0;

        public List<DateTime> dtList { get; set; } = new List<DateTime>();
        /// <summary>
        /// 切换序列及切换次数
        /// </summary>
        public string strArray { get; set; } = "";

        private int ishiftNum = 0;
        public int iShiftNum
        {
            get
            {
                setIShiftNum();
                return ishiftNum;
            }
            set { this.ishiftNum = value; }
        }

        private void setIShiftNum()
        {
            if (ishiftNum == 0 && !string.IsNullOrEmpty(strArray))
            {
                string[] strTmp = strArray.Split('+');
                int iNum = getINum(strTmp);
                ishiftNum = iNum;
            }
        }

        private static int getINum(string[] strTmp)
        {
            int iNum = 0;
            foreach (string cell in strTmp)
            {
                string order = cell.Replace("10", "A");
                foreach (char c in order)
                {
                    if (c == 'A')
                        iNum++;
                }
            }

            return iNum;
        }

        public string ZWTime
        {
            get
            {
                if (iTotalSample == 0)
                    return "0";
                return (lZWTotalTime / 1000.0).ToString("0.00");
            }
        }

        public string TotalTime
        {
            get
            {
                if (iTotalSample == 0)
                    return "0";
                return (lTotalTime / 1000.0).ToString("0.00");
            }
        }

        public string ZWTimeRate
        {
            get
            {
                if (iTotalSample == 0)
                    return "0.00%";
                return (lZWTotalTime * 100.0 / lTotalTime).ToString("0.00") + "%";
            }
        }
        public ZTRailWayZWInfo() { }

        private Dictionary<string, int> dicFileSample = null;

        public void Add(ZTRailWayZWInfo rwInfo)
        {
            this.iZWTotalSample += rwInfo.iZWTotalSample;
            this.iTotalSample += rwInfo.iTotalSample;
            this.dTestTime += rwInfo.dTestTime;
            this.iShiftNum += rwInfo.iShiftNum;
            this.lTotalTime += rwInfo.lTotalTime;
            this.lZWTotalTime += rwInfo.lZWTotalTime;
            if (dicFileSample == null)
                dicFileSample = new Dictionary<string, int>();
            if (!dicFileSample.ContainsKey(rwInfo.FileName))
            {
                dicFileSample[rwInfo.FileName] = 1;
            }
        }

        /// <summary>
        /// 切换序列
        /// </summary>
        public void Add(string i)
        {
            this.strArray += i;
        }
        public ZTRailWayZWInfo(DTFileDataManager fi)
        {
            this.FileName = fi.FileName;
            this.strNetType = fi.TestPoints[0].FileInfo.ServiceTypeDescription;
            if (string.IsNullOrEmpty(this.strAreaType))
            {
                for (int idx = 0; idx < fi.TestPoints.Count; idx++)
                {
                    this.strAreaType = fi.TestPoints[idx].FileInfo.AreaString;
                    if (!string.IsNullOrEmpty(this.strAreaType))
                        break;
                }
            }
        }
        public void AddTime(TestPoint tp)
        {
            if (isTime == 0 || isTime > tp.Time)
                isTime = tp.Time;
            if (ieTime < tp.Time)
                ieTime = tp.Time;
            this.dTestTime = ieTime - isTime;
        }
    }

    public class RailWayZWCell : IDisposable
    {
        public Dictionary<LACCIKey, string> dicZWCell { get; set; }
        public bool isExistData
        {
            get
            {
                if (dicZWCell == null || dicZWCell.Count == 0)
                    return false;
                return true;
            }
        }
        public RailWayZWCell()
        {
            dicZWCell = new Dictionary<LACCIKey, string>();
        }
        public void Add(LACCIKey lacci, string CellName)
        {
            if (!dicZWCell.ContainsKey(lacci))
            {
                dicZWCell[lacci] = CellName;
            }
        }
        public void Add(int iLAC, int iCI, string strNet, string CellName)
        {
            LACCIKey lacci = new LACCIKey(iLAC, iCI, strNet);
            Add(lacci, CellName);
        }
        public static string GetCGI(int iLAC, int iCI)
        {
            string CGI = "";
            if (iCI < 100000)
            {
                CGI = string.Format("460-00-{0}-{1}", iLAC, iCI);
            }
            else
            {
                int EnodebId = iCI / 256;
                int CellId = iCI - iCI / 256 * 256;
                CGI = string.Format("460-00-{0}-{1}", EnodebId, CellId);
            }
            return CGI;
        }
        public bool isExistCell(LACCIKey lacci)
        {
            return dicZWCell.ContainsKey(lacci);
        }
        
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            dicZWCell = null;
        }
    }

    public class LACCIKey
    {
        public int Lac { get; set; }
        public int CI { get; set; }
        public string StrNet { get; set; }
        public LACCIKey() { }
        public LACCIKey(int iLac, int iCi, string strNet)
        {
            this.Lac = iLac;
            this.CI = iCi;
            this.StrNet = strNet;
        }
        public override bool Equals(object obj)
        {
            LACCIKey other = obj as LACCIKey;
            if (other == null)
                return false;
            if (!base.GetType().Equals(obj.GetType()))
                return false;
            return this.Lac.Equals(other.Lac) && this.CI.Equals(other.CI)
                   && this.StrNet.Equals(other.StrNet);
        }
        public override int GetHashCode()
        {
            return (Lac.ToString() + "-" + CI.ToString() + "-" + StrNet).GetHashCode();
        }
    }

    public class DIYSQLQueryLacInfo : DIYSQLQueryLacCICityName
    {
        public DIYSQLQueryLacInfo(MainModel mainModel, int DistrictID)
            : base(mainModel, DistrictID)
        {
        }

        public override string Name
        {
            get { return "tb_cfg_cell_lac"; }
        }
        protected override string getSqlTextString()
        {
            string sql = "SELECT DISTINCT LAC,-10000000 as CI,City,net FROM dtasystem.dbo.tb_cfg_cell_lac";
            return sql;
        }
    }

    public class DIYSQLQueryLacCICityName : DIYSQLBase
    {
        public DIYSQLQueryLacCICityName(MainModel mainModel, int districtID)
            : base(mainModel)
        {
            this.districtID = districtID;
        }

        public override string Name
        {
            get { return "DIYSQLQueryLacCICityName"; }
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, districtID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }

        }

        /// <summary>
        /// 查询LAC相关的地市名
        /// </summary>
        public Dictionary<LACCIKey, string> LacCICityNameMap { get; set; } = new Dictionary<LACCIKey, string>();
        readonly int districtID;
        protected override string getSqlTextString()
        {
            string sql = "SELECT LAC,CI,strcity,net FROM dtasystem.dbo.tb_cfg_cell_lac_ci";
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[4];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            return rType;
        }


        protected override void receiveRetData(ClientProxy clientProxy)
        {
            LacCICityNameMap.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    try
                    {
                        LACCIKey lacci = new LACCIKey();
                        lacci.Lac = package.Content.GetParamInt();
                        lacci.CI = package.Content.GetParamInt();
                        string city = package.Content.GetParamString();
                        lacci.StrNet = package.Content.GetParamString();
                        if (!LacCICityNameMap.ContainsKey(lacci))
                            LacCICityNameMap.Add(lacci, city);
                    }
                    catch
                    {
                        //continue
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

    }
}
