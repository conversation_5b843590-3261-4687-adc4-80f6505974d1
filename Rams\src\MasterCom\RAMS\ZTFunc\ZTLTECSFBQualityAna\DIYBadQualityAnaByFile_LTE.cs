﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Net;
using Microsoft.Office.Interop.Excel;
using System.Windows.Forms;
using System.Drawing;
using MasterCom.RAMS.Frame;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.ExportTestPoint;

namespace MasterCom.RAMS.Net
{
    public class DIYBadQualityAnaByFile_LTE : DIYBadQualityAnaByFile
    {
        public DIYBadQualityAnaByFile_LTE(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "质差切换相关小区信息"; }
        }

        public override string IconName
        {
            get { return "Images/fileq.gif"; }
        }

        protected override void replay(int minBadQualSencond, int secondPerUnit, int rxQualThreShold, ref BadQualityFile badQualFile, ref HandoverCellsFile handoverCellsFile)
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            condition.Geometorys = Condition.Geometorys;
            Replay_LTE query = new Replay_LTE(MainModel, minBadQualSencond, secondPerUnit, rxQualThreShold);
            query.SetQueryCondition(condition);
            query.Query();
            badQualFile = query.badQualFile;
            handoverCellsFile = query.handoverCellsFile;
        }



        protected class Replay_LTE : DIYAnalyseByFileBackgroundBase
        {
            int minBadQualSencond { get; set; }
            int secondPerUnit { get; set; }
            int rxQualThreShold { get; set; }
            public BadQualityFile badQualFile { get; set; } = new BadQualityFile();
            public HandoverCellsFile handoverCellsFile { get; set; } = new HandoverCellsFile();
            public Replay_LTE(MainModel mainModel, int minBadQualSencond, int secondPerUnit, int rxQualThreShold)
                : base(MainModel.GetInstance())
            {
                this.secondPerUnit = secondPerUnit;
                this.minBadQualSencond = minBadQualSencond;
                this.rxQualThreShold = rxQualThreShold;
                countToAna = (int)(secondPerUnit * 1000.0 / 480 + 0.5);
                badCountToAna = (int)(minBadQualSencond * 1000.0 / 480 + 0.5);
                newManager = new DTDataManager(mainModel);
            }

            protected override void query()
            {
                MainModel.FileInfos.Clear();
                MainModel.FileInfos.AddRange(this.condition.FileInfos);
                WaitBox.CanCancel = true;
                WaitBox.Show("开始分析文件...", analyseFiles);
            }
            protected override void doStatWithQuery()
            {
                doJudge();
            }

            readonly LinkedList<TestPoint> tps = new LinkedList<TestPoint>();
            readonly int countToAna = 0;
            readonly int badCountToAna = 0;
            DTDataManager newManager { get; set; }

            private void doJudge()
            {
                MainModel.DTDataManager.Sort();

                judgeSamples();
                judgeEvents();
            }

            protected virtual int? get_RxQualSub(TestPoint tp)
            {
                return (int?)(byte?)tp["lte_gsm_DM_RxQualSub"];
            }
            private void judgeSamples()
            {
                if (MainModel.DTDataManager.FileDataManagers.Count <= 0) return;
                badQualFile.fildID = MainModel.DTDataManager.FileDataManagers[0].FileID;
                badQualFile.fileName = MainModel.DTDataManager.FileDataManagers[0].FileName;
                List<TestPoint> testPointList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
                for (int i = 0; i < testPointList.Count; i++)
                {
                    TestPoint tp = testPointList[i];
                    int? tem = this.get_RxQualSub(tp);
                    int rxQual = tem == null ? 255 : (int)tem;
                    if (rxQual == 255)
                    {
                        continue;
                    }
                    if (rxQual >= rxQualThreShold)
                    {
                        tps.AddLast(tp);
                    }
                    else if (tps.Count > 0)
                    {
                        if (tps.Count >= countToAna)
                        {
                            getBadQual(tps.Count);
                        }
                        else if (tps.Count >= badCountToAna)
                        {
                            anaBadCount(testPointList, i);
                        }
                        else
                        {
                            initParameters();
                        }
                    }
                }
            }

            private void anaBadCount(List<TestPoint> testPointList, int i)
            {
                int badCount = tps.Count;
                int spreadCountAfter = (countToAna - tps.Count) / 2;
                int spreadCountBefore = spreadCountAfter;
                if ((countToAna - tps.Count) % 2 == 1)
                {
                    spreadCountBefore++;
                }
                for (int j = 1; j <= spreadCountAfter; j++)
                {
                    if (i + j < testPointList.Count)
                    {
                        tps.AddLast(testPointList[i + j]);
                    }
                }
                for (int j = 1; j <= spreadCountBefore; j++)
                {
                    if (i - badCount - j >= 0)
                    {
                        tps.AddFirst(testPointList[i - badCount - j]);
                    }
                }
                getBadQual(badCount);
            }

            private void initParameters()
            {
                tps.Clear();
            }
            protected virtual BadQuality getBadQualityInstance(LinkedList<TestPoint> tpList, int badCount)
            {
                return new BadQuality_LTE(tpList, badCount);
            }
            private void getBadQual(int badCount)
            {
                badQualFile.badQualList.Add(this.getBadQualityInstance(tps, badCount));
                initParameters();
            }
            private void judgeEvents()
            {
                if (MainModel.DTDataManager.FileDataManagers.Count <= 0) return;
                handoverCellsFile.fildID = MainModel.DTDataManager.FileDataManagers[0].FileID;
                handoverCellsFile.fileName = MainModel.DTDataManager.FileDataManagers[0].FileName;
                List<TestPoint> tpList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
                List<DTDataIndex> dtDataIndexs = new List<DTDataIndex>();
                int tpIndex = 0;
                foreach (DTData dtData in MainModel.DTDataManager.FileDataManagers[0].DTDatas)
                {
                    if (dtData is TestPoint)
                    {
                        dtDataIndexs.Add(new DTDataIndex(MainModel.DTDataManager.FileDataManagers[0].FileID, DTDataType.TestPoint, tpIndex));
                        tpIndex++;
                    }
                }
                foreach (Event e in MainModel.DTDataManager.FileDataManagers[0].Events)
                {
                    if (e.ID == 1039)
                    {
                        int eLac1 = (int)e["LAC"];
                        int eCi1 = (int)e["CI"];
                        int eLac2 = (int)e["TargetLAC"];
                        int eCi2 = (int)e["TargetCI"];
                        Cell cellSer = CellManager.GetInstance().GetCell(e.DateTime, (ushort)eLac1, (ushort)eCi1);
                        Cell cellTar = CellManager.GetInstance().GetCell(e.DateTime, (ushort)eLac2, (ushort)eCi2);
                        if (cellSer != null && cellTar != null && cellSer != cellTar)
                        {
                            int selectedRowIndex = getSelectedRowIndex(dtDataIndexs, e);

                            addHandoverCellsList(tpList, e, selectedRowIndex);
                        }
                    }
                }
            }

            private int getSelectedRowIndex(List<DTDataIndex> dtDataIndexs, Event e)
            {
                int selectedRowIndex = -1;
                for (int index = 0; index < dtDataIndexs.Count; index++)
                {
                    DTDataIndex dtDataIndex = dtDataIndexs[index];
                    if (MainModel.DTDataManager[dtDataIndex].SN > e.SN)
                    {
                        break;
                    }
                    else if (MainModel.DTDataManager[dtDataIndex].SN == e.SN)
                    {
                        selectedRowIndex = index;
                        break;
                    }
                    selectedRowIndex = index;
                }

                return selectedRowIndex;
            }

            private void addHandoverCellsList(List<TestPoint> tpList, Event e, int selectedRowIndex)
            {
                if (selectedRowIndex != -1)
                {
                    LinkedList<TestPoint> tpListBefore = new LinkedList<TestPoint>();
                    LinkedList<TestPoint> tpListAfter = new LinkedList<TestPoint>();
                    tpListBefore.AddFirst(tpList[selectedRowIndex]);
                    for (int i = 1; i <= 10; i++)
                    {
                        if (selectedRowIndex + i < tpList.Count)
                        {
                            tpListAfter.AddLast(tpList[selectedRowIndex + i]);
                        }
                        if (i < 10 && selectedRowIndex - i >= 0)
                        {
                            tpListBefore.AddFirst(tpList[selectedRowIndex - i]);
                        }
                    }
                    handoverCellsFile.handoverCellsList.Add(this.************************(tpListBefore, tpListAfter, e));
                }
            }

            protected virtual HandoverCells ************************(LinkedList<TestPoint> tpListBefore, LinkedList<TestPoint> tpListAfter, Event eve)
            {
                return new HandoverCells_LTE(tpListBefore, tpListAfter, eve);
            }
        }
    }

    public class BadQuality_LTE : BadQuality
    {
        public BadQuality_LTE(LinkedList<TestPoint> tpList, int badCount)
            : base(tpList, badCount)
        {
        }
        
        protected override int? get_RxLevSub(TestPoint tp)
        {
            return (int?)(short?)tp["lte_gsm_DM_RxLevSub"];
        }
        protected override void get_N_Param(TestPoint tp, out short? nbcch, out byte? nbsic, out int? nrxlev, int index)
        {
            nbcch = (short?)tp["lte_gsm_NC_BCCH", index];
            nbsic = (byte?)tp["lte_gsm_NC_BSIC", index];
            nrxlev = (int?)(short?)tp["lte_gsm_NC_RxLev", index];
        }
         
        protected override Cell getMainCell(TestPoint tp)
        {
            return tp.GetMainCell_LTE_GSM();
        }
        protected override Cell get_N_Cell(TestPoint tp, int index)
        {
            return tp.GetNBCell_LTE_GSM(index);
        }
    }


    public class HandoverCells_LTE : HandoverCells
    {
        public HandoverCells_LTE(LinkedList<TestPoint> tpListBefore, LinkedList<TestPoint> tpListAfter, Event eve)
            : base(tpListBefore, tpListAfter, eve)
        {
        }
        
        protected override int? get_RxLevSub(TestPoint tp)
        {
            return (int?)(short?)tp["lte_gsm_DM_RxLevSub"];
        }
        protected override void get_N_Param(TestPoint tp, out short? nbcch, out byte? nbsic, out int? nrxlev, int index)
        {
            nbcch = (short?)tp["lte_gsm_NC_BCCH", index];
            nbsic = (byte?)tp["lte_gsm_NC_BSIC", index];
            nrxlev = (int?)(short?)tp["lte_gsm_NC_RxLev", index];
        }
        protected override Cell get_MainCell(TestPoint tp)
        {
            return tp.GetMainCell_LTE_GSM();
        }
        protected override Cell get_N_Cell(TestPoint tp, int index)
        {
            return tp.GetNBCell_LTE_GSM(index);
        }
    }
}
