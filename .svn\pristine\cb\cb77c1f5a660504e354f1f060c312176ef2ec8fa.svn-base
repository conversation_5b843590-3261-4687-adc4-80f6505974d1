﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FreqBandSelectionPanel : UserControl
    {
        ToolStripDropDown parentDropDown;
        ListView lvFreqBand;
        //设置标志，防止死循环
        bool check = false;
        public List<CarrierFreqBand> ListCarrierFreqBand { get; set; }

        public FreqBandSelectionPanel(ToolStripDropDown dropDown, ListView lvFreqBand)
        {
            InitializeComponent();
            this.parentDropDown = dropDown;
            this.lvFreqBand = lvFreqBand;
            this.ListCarrierFreqBand = getCarrierFreqBand();
        }


        public void FreshItems()
        {
            makeTreeView(treeViewFreqBand);
        }


        /// <summary>
        /// 得到频点树形结构
        /// </summary>
        /// <param name="treeView"></param>
        private void makeTreeView(TreeView treeView)
        {
            if (ListCarrierFreqBand == null)
            {
                return;
            }
            if (ListCarrierFreqBand.Count == 0)
            {
                return;
            }
            treeView.Nodes.Clear();
            foreach (CarrierFreqBand carrierFreqBand in ListCarrierFreqBand)
            {
                TreeNode rootNode = new TreeNode();
                rootNode.Text = carrierFreqBand.Carrier;
                rootNode.Tag = carrierFreqBand;
                foreach (FreqBand freqBand in carrierFreqBand.ListFreqBand)
                {
                    TreeNode freqBandNode = new TreeNode();
                    freqBandNode.Text = freqBand.FreqBandName;
                    freqBandNode.Tag = freqBand;
                    rootNode.Nodes.Add(freqBandNode);
                    foreach (FreqPoint freqPoint in freqBand.ListFreqPoint)
                    {
                        TreeNode freqPointNode = new TreeNode();
                        freqPointNode.Text = freqPoint.FreqPointName;
                        freqPointNode.Tag = freqPoint;
                        freqBandNode.Nodes.Add(freqPointNode);
                    }
                }
                rootNode.ExpandAll();
                treeView.Nodes.Add(rootNode);
            }
        }

        /// <summary>
        /// 设置节点选中
        /// </summary>
        public void SetNodeChecked(List<FreqPoint> curListFrqPoint)
        {
            if (curListFrqPoint == null)
            {
                return;
            }
            if (curListFrqPoint.Count <= 0)
            {
                return;
            }
            foreach (TreeNode rootNode in treeViewFreqBand.Nodes)
            {
                foreach (TreeNode node in rootNode.Nodes)
                {
                    foreach (TreeNode childNode in node.Nodes)
                    {
                        if (checkTreeNode(curListFrqPoint, childNode))
                        {
                            childNode.Checked = true;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 判断是否为选中的节点
        /// </summary>
        /// <param name="list"></param>
        /// <param name="node"></param>
        /// <returns></returns>
        private bool checkTreeNode(List<FreqPoint> list, TreeNode node)
        {
            bool isCheck = false;
            foreach (FreqPoint fp in list)
            {
                string freqPointName = ((FreqPoint)node.Tag).FreqPointName;
                if (fp.FreqPointName == freqPointName)
                {
                    isCheck = true;
                    break;
                }
            }
            return isCheck;
        }

        /// <summary>
        /// 确定选中
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOK_Click(object sender, EventArgs e)
        {
            lvFreqBand.Items.Clear();
            List<FreqPoint> listFreqPoint = GetListFreqBand();
            foreach (FreqPoint fp in listFreqPoint)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Text = fp.Carrier + "_" + fp.FreqPointName;
                lvi.Tag = fp;
                lvFreqBand.Items.Add(lvi);
            }
            parentDropDown.Close();
        }

        /// <summary>
        /// 得到勾选的频点
        /// </summary>
        /// <returns></returns>
        public List<FreqPoint> GetListFreqBand()
        {
            List<FreqPoint> listFreqPoint = new List<FreqPoint>();
            foreach (TreeNode rootNode in treeViewFreqBand.Nodes)
            {
                foreach (TreeNode node in rootNode.Nodes)
                {
                    foreach (TreeNode childNode in node.Nodes)
                    {
                        FreqPoint fp = (FreqPoint)childNode.Tag;
                        if (childNode.Checked)
                        {
                            listFreqPoint.Add(fp);
                        }
                    }
                }
            }
            return listFreqPoint;
        }

        /// <summary>
        /// 清除所有
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnClear_Click(object sender, EventArgs e)
        {
            foreach (TreeNode node in treeViewFreqBand.Nodes)
            {
                node.Checked = false;
                foreach (TreeNode childNode in node.Nodes)
                {
                    childNode.Checked = false;
                }
            }
        }

        /// <summary>
        /// 更新节点状态
        /// </summary>
        /// <param name="node"></param>
        /// <param name="checkedIdSet"></param>
        private void UpdateNodeState(TreeNode node, List<int> checkedIdSet)
        {
            node.Checked = false;
            if (node.Tag is int)
            {
                int id = (int)node.Tag;
                node.Checked = checkedIdSet.Contains(id);
            }
            foreach (TreeNode subNode in node.Nodes)
            {
                UpdateNodeState(subNode, checkedIdSet);
            }
        }

        /// <summary>
        ///节点选中
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void treeViewFreqBand_AfterCheck(object sender, TreeViewEventArgs e)
        {
            TreeNode node = e.Node;
            if (!check)
                setChildNodes(node);
            setParentNodes(node);
            check = false;
        }

        /// <summary>
        ///设置子节点状态
        /// </summary>
        /// <param name="node"></param>
        private void setChildNodes(TreeNode node)
        {
            foreach (TreeNode child in node.Nodes)
            {
                child.Checked = node.Checked;
            }
            check = true;
        }

        /// <summary>
        /// 设置父节点状态
        /// </summary>
        /// <param name="node"></param>
        private void setParentNodes(TreeNode node)
        {
            if (node.Parent != null)
            {
                //如果当前节点状态为勾选，则需要所有兄弟节点都勾选才能勾选父节点
                if (node.Checked)
                    foreach (TreeNode brother in node.Parent.Nodes)
                    {
                        if (!brother.Checked)
                            return;
                    }
                node.Parent.Checked = node.Checked;
            }
        }

        /// <summary>
        /// 得到频点
        /// </summary>
        /// <returns></returns>
        private List<CarrierFreqBand> getCarrierFreqBand()
        {
            ListCarrierFreqBand = new List<CarrierFreqBand>();
            ListCarrierFreqBand.Add(getYDFreqBand());
            ListCarrierFreqBand.Add(getDXFreqBand());
            ListCarrierFreqBand.Add(getLTFreqBand());
            return ListCarrierFreqBand;
        }

        /// <summary>
        /// 移动
        /// </summary>
        /// <returns></returns>
        private CarrierFreqBand getYDFreqBand()
        {
            string carrier = "移动";
            CarrierFreqBand carrierFreqBand = null;
            List<FreqBand> listFreqBand = new List<FreqBand>();
            List<FreqPoint> listFreqPoint = new List<FreqPoint>();
            FreqPoint freqPoint = new FreqPoint(carrier, "A", 0, "A频段", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("A", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "D", 37900, "D_37900", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "D", 38100, "D_38100", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "D", 38098, "D_38098", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "D", 0, "D_其他", false, true);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("D", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "E", 0, "E频段", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("E", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "F", 38350, "F_38350", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "F", 38400, "F_38400", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "F", 38544, "F_38544", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "F", 0, "F_其他", false, true);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("F", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "FDD", 1300, "FDD_1300", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "FDD", 1309, "FDD_1309", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "FDD", 1347, "FDD_1347", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "FDD", 1350, "FDD_1350", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "FDD", 1397, "FDD_1397", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "FDD", 1400, "FDD_1400", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "FDD", 3525, "FDD_3525", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "FDD", 3600, "FDD_3600", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "FDD", 3675, "FDD_3675", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "FDD", 3683, "FDD_3683", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("FDD", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "TDD", 36275, "TDD_36275", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 37900, "TDD_37900", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 38098, "TDD_38098", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 38355, "TDD_38355", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 38400, "TDD_38400", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 38496, "TDD_38496", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 38544, "TDD_38544", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 38950, "TDD_38950", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 39148, "TDD_39148", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 39292, "TDD_39292", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 39946, "TDD_39946", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 40144, "TDD_40144", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 40936, "TDD_40936", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 41134, "TDD_41134", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "TDD", 41332, "TDD_41332", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("TDD", listFreqPoint));
            carrierFreqBand = new CarrierFreqBand(carrier, listFreqBand);
            return carrierFreqBand;
        }

        /// <summary>
        /// 电信
        /// </summary>
        /// <returns></returns>
        private CarrierFreqBand getDXFreqBand()
        {
            string carrier = "电信";
            CarrierFreqBand carrierFreqBand = null;
            List<FreqBand> listFreqBand = new List<FreqBand>();
            List<FreqPoint> listFreqPoint = new List<FreqPoint>();
            FreqPoint freqPoint = new FreqPoint(carrier, "FDD", 100, "FDD_100", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "FDD", 1825, "FDD_1825", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "FDD", 2452, "FDD_2452", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("FDD", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "NB-IOT", 2506, "NB-IOT_2506", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("NB-IOT", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "TDD", 41140, "TDD_41140", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("TDD", listFreqPoint));
            carrierFreqBand = new CarrierFreqBand(carrier, listFreqBand);
            return carrierFreqBand;
        }

        /// <summary>
        /// 联通
        /// </summary>
        /// <returns></returns>
        private CarrierFreqBand getLTFreqBand()
        {
            string carrier = "联通";
            CarrierFreqBand carrierFreqBand = null;
            List<FreqBand> listFreqBand = new List<FreqBand>();
            List<FreqPoint> listFreqPoint = new List<FreqPoint>();
            FreqPoint freqPoint = new FreqPoint(carrier, "L1800", 1506, "L1800_1506", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "L1800", 1533, "L1800_1533", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "L1800", 1650, "L1800_1650", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "L1800", 1700, "L1800_1700", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("L1800", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "L2100", 375, "L2100_375", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "L2100", 400, "L2100_400", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "L2100", 500, "L2100_500", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("L2100", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "L2600", 40340, "L2600_40340", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("L2600", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "NB-IoT", 3743, "NB-IoT_3743", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("NB-IoT", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "U900", 3085, "U900_3085", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("U900", listFreqPoint));

            listFreqPoint = new List<FreqPoint>();
            freqPoint = new FreqPoint(carrier, "U2100", 10663, "U2100_10663", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "U2100", 10688, "U2100_10688", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "U2100", 10713, "U2100_10713", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "U2100", 10738, "U2100_10738", false, false);
            listFreqPoint.Add(freqPoint);
            freqPoint = new FreqPoint(carrier, "U2100", 10838, "U2100_10838", false, false);
            listFreqPoint.Add(freqPoint);
            listFreqBand.Add(new FreqBand("U2100", listFreqPoint));
            carrierFreqBand = new CarrierFreqBand(carrier, listFreqBand);
            return carrierFreqBand;
        }
    }

    /// <summary>
    /// 运营商频段
    /// </summary>
    public class CarrierFreqBand
    {
        /// <summary>
        /// 运营商
        /// </summary>
        public string Carrier { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<FreqBand> ListFreqBand { get; set; }

        public CarrierFreqBand(string carrier, List<FreqBand> listFreqBand)
        {
            Carrier = carrier;
            ListFreqBand = listFreqBand;
        }
    }

    /// <summary>
    ///频段
    /// </summary>
    public class FreqBand
    {
        public string FreqBandName { get; set; }
        public List<FreqPoint> ListFreqPoint { get; set; }

        public FreqBand(string freqBandName, List<FreqPoint> listFreqPoint)
        {
            ListFreqPoint = listFreqPoint;
            FreqBandName = freqBandName;
        }
    }

    public class FreqPoint
    {
        public string Carrier { get; set; }
        /// <summary>
        ///所在频段
        /// </summary>
        public string FreqBandName { get; set; }
        /// <summary>
        /// 频点值
        /// </summary>
        public int Freq { get; set; }
        //频点名称
        public string FreqPointName { get; set; }
        /// <summary>
        /// 是否按频段进行筛选
        /// </summary>
        public bool IsFreqBand { get; set; }
        /// <summary>
        /// 当前频段下的其他频点
        /// </summary>
        public bool IsOtherFreqPoint { get; set; }


        public FreqPoint(string carrier, string freqBandName, int freq, string freqPointName, bool isFreqBand, bool isOtherFreqPoint)
        {
            Carrier = carrier;
            FreqBandName = freqBandName;
            Freq = freq;
            FreqPointName = freqPointName;
            IsFreqBand = isFreqBand;
            IsOtherFreqPoint = isOtherFreqPoint;
        }

        /// <summary>
        /// 小区是否在有效频段内
        /// </summary>
        /// <param name="cellRsrp"></param>
        /// <returns></returns>
        public static bool ValidCellBandType(List<FreqPoint> listFreqPoint, int Iearfcn)
        {
            bool isValidCellBand = false;
            foreach (FreqPoint fp in listFreqPoint)
            {
                if (judgeValidCell(fp, Iearfcn))
                {
                    isValidCellBand = true;
                    break;
                }
            }
            return isValidCellBand;
        }

        private static bool judgeValidCell(FreqPoint fp, int Iearfcn)
        {
            bool isValidCell = false;
            if (fp.IsFreqBand || fp.IsOtherFreqPoint)//频段或其他
            {
                LTEBandType band = (LTEBandType)Enum.Parse(typeof(LTEBandType), fp.FreqBandName.Trim());
                isValidCell = LTECell.GetBandTypeByEarfcn_BJ(Iearfcn) == band || LTECell.GetTddOrFddByEarfcn(Iearfcn) == band;
            }
            else //频点
            {
                isValidCell = Iearfcn == fp.Freq;
            }
            return isValidCell;
        }

    }
}
