﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Reflection;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.BackgroundFunc
{
    public static class ExportAcceptReportManager
    {
        private static List<LTECell> curCellList = null;
        private static MainModel mainModel = MainModel.GetInstance();
        public static string ExportOutDoorReport(StationAcceptInfo_HB btsAcceptInfo, List<LTECell> cellList, string folderPath)
        {
            curCellList = cellList;
            Excel.Application excel = new Excel.Application();
            Excel.Workbook workbook = excel.Workbooks.Add(true);
            Excel.Worksheet worksheet = (Excel.Worksheet)excel.ActiveSheet;

            try
            {
                CellAcceptInfo_HB cellInfoCircle = btsAcceptInfo.CellCircleTestInfo;
                int titleSize = 12;
                worksheet.Name = "室外站单站验收报告";
                ((Excel.Range)worksheet.Columns["A:D", System.Type.Missing]).ColumnWidth = 22;
                ((Excel.Range)worksheet.Columns["B", System.Type.Missing]).HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                ((Excel.Range)worksheet.Columns["D", System.Type.Missing]).HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                Excel.Range excelRange;
                int excelrow = 0;

                saveOneLevelTitleToWorksheet(worksheet, ref excelrow, "单验环测总体情况");

                excelrow++;
                worksheet.Cells[excelrow, 1] = "基站站名：";
                excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 1], worksheet.Cells[excelrow, 1]);
                excelRange.Font.Size = titleSize;
                excelRange.Font.Bold = true;
                worksheet.Cells[excelrow, 2] = btsAcceptInfo.CellParamInfo.BtsName;

                worksheet.Cells[excelrow, 3] = "测试日期";
                excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 3], worksheet.Cells[excelrow, 3]);
                excelRange.Font.Size = titleSize;
                excelRange.Font.Bold = true;
                worksheet.Cells[excelrow, 4] = btsAcceptInfo.TestTimeDes;

                excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 1], worksheet.Cells[excelrow, 4]);
                excelRange.RowHeight = 22;

                saveTwoLevelTitleToWorksheet(worksheet, ref excelrow, "主要测试指标");

                StringBuilder strOppositeCells = new StringBuilder();
                foreach (var cellInfoPair in btsAcceptInfo.CellAcceptInfoDic)
                {
                    CellAcceptInfo_HB cellInfo = cellInfoPair.Value;
                    if (cellInfo.IsAntennaOpposite == true)
                    {
                        strOppositeCells.Append(cellInfo.CellParamInfo.CellName + "  ");
                    }

                    saveTwoLevelTitleToWorksheet(worksheet, ref excelrow, cellInfo.CellParamInfo.CellName);

                    excelrow++;
                    worksheet.Cells[excelrow, 1] = "平均RSRP";
                    worksheet.Cells[excelrow, 2] = cellInfo.RsrpAvg;
                    worksheet.Cells[excelrow, 3] = "是否存在上行好点";
                    setHasTpCellValue(worksheet, excelrow, 4, cellInfo.HasUlGoodTp);
                    excelrow++;
                    worksheet.Cells[excelrow, 1] = "平均SINR";
                    worksheet.Cells[excelrow, 2] = cellInfo.SinrAvg;
                    worksheet.Cells[excelrow, 3] = "是否存在下行好点";
                    setHasTpCellValue(worksheet, excelrow, 4, cellInfo.HasDlGoodTp);
                    excelrow++;
                    worksheet.Cells[excelrow, 1] = "平均下载速率";
                    worksheet.Cells[excelrow, 2] = cellInfo.PdcpDLAvg;
                    worksheet.Cells[excelrow, 3] = "是否存在上行中点";
                    setHasTpCellValue(worksheet, excelrow, 4, cellInfo.HasUlCommonTp);
                    excelrow++;
                    worksheet.Cells[excelrow, 1] = "平均上传速率";
                    worksheet.Cells[excelrow, 2] = cellInfo.PdcpULAvg;
                    worksheet.Cells[excelrow, 3] = "是否存在下行中点";
                    setHasTpCellValue(worksheet, excelrow, 4, cellInfo.HasDlCommonTp);
                    excelrow++;
                    worksheet.Cells[excelrow, 3] = "是否存在上行差点";
                    setHasTpCellValue(worksheet, excelrow, 4, cellInfo.HasUlBadTp);
                    excelrow++;
                    worksheet.Cells[excelrow, 3] = "是否存在下行差点";
                    setHasTpCellValue(worksheet, excelrow, 4, cellInfo.HasDlBadTp);

                    addOneEmptyColumn(worksheet, ref excelrow);
                }

                saveTwoLevelTitleToWorksheet(worksheet, ref excelrow, btsAcceptInfo.CellParamInfo.BtsName);

                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "RSRP弱覆盖占比", btsAcceptInfo.RatioInfoWeakRsrp.RatioDes, btsAcceptInfo.RatioInfoWeakRsrp.IsValidRatio);
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "下行小于2M占比", btsAcceptInfo.RatioInfoLowDl.RatioDes, btsAcceptInfo.RatioInfoLowDl.IsValidRatio);
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "上行512K占比", btsAcceptInfo.RatioInfoLowUl.RatioDes, btsAcceptInfo.RatioInfoLowUl.IsValidRatio);
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "SINR质差占比", btsAcceptInfo.RatioInfoWeakSinr.RatioDes, btsAcceptInfo.RatioInfoWeakSinr.IsValidRatio);

                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "切换成功率", btsAcceptInfo.RatioInfoHandover.RatioDes, btsAcceptInfo.IsValidHandOverSucessRatio);
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "CSFB全程呼叫成功率", btsAcceptInfo.CsfbCallInfo.SucessRatioAllCallDes, btsAcceptInfo.CsfbCallInfo.IsValidSucessRatioAllCall);
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "VoLTE语音全程呼叫成功率", btsAcceptInfo.VolteVoiceCallInfo.SucessRatioAllCallDes, btsAcceptInfo.VolteVoiceCallInfo.IsValidSucessRatioAllCall);
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "VoLTE语音业务接通率", btsAcceptInfo.VolteVoiceCallInfo.SucessRatioMoCallDes, btsAcceptInfo.VolteVoiceCallInfo.IsValidSucessRatioMoCall);
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "VoLTE视频全程呼叫成功率", btsAcceptInfo.VolteVideoCallInfo.SucessRatioAllCallDes, btsAcceptInfo.VolteVideoCallInfo.IsValidSucessRatioAllCall);
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "VoLTE视频业务接通率", btsAcceptInfo.VolteVideoCallInfo.SucessRatioMoCallDes, btsAcceptInfo.VolteVideoCallInfo.IsValidSucessRatioMoCall);
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "SRVCC切换成功率", btsAcceptInfo.RatioInfoSRVCC.RatioDes, btsAcceptInfo.RatioInfoSRVCC.IsValidRatio);
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "是否完成环测", btsAcceptInfo.IsCircleTestHasValidTp ? "是" : "否", btsAcceptInfo.IsCircleTestHasValidTp);

                string isAntennaOpposite = "";
                if (btsAcceptInfo.IsAntennaOpposite == true)
                {
                    isAntennaOpposite = "是";
                }
                else if (btsAcceptInfo.IsAntennaOpposite == false)
                {
                    isAntennaOpposite = "否";
                }
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "天线是否接反", isAntennaOpposite, btsAcceptInfo.IsAntennaOpposite != true);
                addOneEmptyColumn(worksheet, ref excelrow);

                saveOneLevelTitleToWorksheet(worksheet, ref excelrow, "天馈接反具体情况");
                saveCirCleAcceptInfoToWorksheet(worksheet, ref excelrow, "怀疑天馈接反小区对",
                    strOppositeCells.ToString(), string.IsNullOrEmpty(strOppositeCells.ToString()));
                addOneEmptyColumn(worksheet, ref excelrow);

                saveOneLevelTitleToWorksheet(worksheet, ref excelrow, "单验GPS拉线图");

                excelRange = worksheet.get_Range(worksheet.Cells[1, 1], worksheet.Cells[excelrow, 4]);
                excelRange.Cells.Borders.LineStyle = Excel.XlLineStyle.xlContinuous;

                #region 地图显示

                float topPosition = (float)(((excelrow - 4) * 13.6) + 110);//减去已设置RowHeight的几行，再加上这几行的行高之和
                excelrow += 2;

                saveDLInfoToExcel(btsAcceptInfo, cellList, worksheet, cellInfoCircle, ref excelrow, ref topPosition);

                saveULInfoToExcel(btsAcceptInfo, cellList, worksheet, cellInfoCircle, ref excelrow, ref topPosition);

                mainModel.SelectedLTECells = null;
                mainModel.DrawFlyLines = false;
                if (btsAcceptInfo.IsFddBts)
                {
                    fireMapSerialTheme("LTE_FDD:PCI");
                }
                else
                {
                    fireMapSerialTheme("lte_PCI");
                }
                saveCallTracePicToExcel(worksheet, ref excelrow, ref topPosition, "CSFB主叫", cellInfoCircle.CsfbCallInfo.TestPointMoList, cellInfoCircle.CsfbCallInfo.ProblemEvtMoList);
                saveCallTracePicToExcel(worksheet, ref excelrow, ref topPosition, "CSFB被叫", cellInfoCircle.CsfbCallInfo.TestPointMtList, cellInfoCircle.CsfbCallInfo.ProblemEvtMtList);
                saveCallTracePicToExcel(worksheet, ref excelrow, ref topPosition, "VoLTE语音主叫", cellInfoCircle.VolteVoiceCallInfo.TestPointMoList, cellInfoCircle.VolteVoiceCallInfo.ProblemEvtMoList);
                saveCallTracePicToExcel(worksheet, ref excelrow, ref topPosition, "VoLTE语音被叫", cellInfoCircle.VolteVoiceCallInfo.TestPointMtList, cellInfoCircle.VolteVoiceCallInfo.ProblemEvtMtList);
                saveCallTracePicToExcel(worksheet, ref excelrow, ref topPosition, "VoLTE视频主叫", cellInfoCircle.VolteVideoCallInfo.TestPointMoList, cellInfoCircle.VolteVideoCallInfo.ProblemEvtMoList);
                saveCallTracePicToExcel(worksheet, ref excelrow, ref topPosition, "VoLTE视频被叫", cellInfoCircle.VolteVideoCallInfo.TestPointMtList, cellInfoCircle.VolteVideoCallInfo.ProblemEvtMtList);
                #endregion

                string strFileName = string.Format("{0}\\{1}.xlsx", folderPath, btsAcceptInfo.CellParamInfo.ENodeBID);
                excel.DisplayAlerts = false;
                workbook.SaveAs(strFileName, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Excel.XlSaveAsAccessMode.xlNoChange, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value);
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("成功导出" + btsAcceptInfo.CellParamInfo.BtsName + "的单验报告");
                return strFileName;
            }
            catch (Exception ex)
            {
                writeErrLog(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
                return "";
            }
            finally
            {
                excel.Quit();
                //GC.Collect();
            }
        }

        private static void saveDLInfoToExcel(StationAcceptInfo_HB btsAcceptInfo, List<LTECell> cellList, Excel.Worksheet worksheet, CellAcceptInfo_HB cellInfoCircle, ref int excelrow, ref float topPosition)
        {
            if (cellInfoCircle.DLTpList.Count > 0 && cellInfoCircle.HasFoundValidPositionDlTp)
            {
                #region PCI轨迹图
                MapLTECellLayer lteLayer = mainModel.MainForm.GetMapForm().GetLTECellLayer();
                bool drawCellLabel = lteLayer.DrawCellLabel;
                lteLayer.DrawCellLabel = true;
                lteLayer.DrawCellName = true;//显示小区名标签

                mainModel.SelectedLTECells = null;
                mainModel.DrawFlyLines = false;
                reSetPciMapView(cellList, btsAcceptInfo.IsFddBts);//设置pci图例

                goToMapView(cellList, cellInfoCircle.DLTpList);
                if (btsAcceptInfo.IsFddBts)
                {
                    saveSerialThemePicToExcel(ref excelrow, "LTE_FDD:PCI", worksheet, ref topPosition, "PCI轨迹图");
                }
                else
                {
                    saveSerialThemePicToExcel(ref excelrow, "lte_PCI", worksheet, ref topPosition, "PCI轨迹图");
                }
                lteLayer.DrawCellLabel = drawCellLabel;
                #endregion

                mainModel.DrawFlyLines = true;
                mainModel.SelectedLTECells = cellList;
                if (btsAcceptInfo.IsFddBts)
                {
                    saveSerialThemePicToExcel(ref excelrow, "LTE_FDD:RSRP", worksheet, ref topPosition, "RSRP拉线图");
                    saveSerialThemePicToExcel(ref excelrow, "LTE_FDD:SINR", worksheet, ref topPosition, "SINR拉线图");
                    saveSerialThemePicToExcel(ref excelrow, "LTE_FDD:PDCP_DL_Mb", worksheet, ref topPosition, "下载速率拉线图");
                }
                else
                {
                    saveSerialThemePicToExcel(ref excelrow, "TD_LTE_RSRP", worksheet, ref topPosition, "RSRP拉线图");
                    saveSerialThemePicToExcel(ref excelrow, "TD_LTE_SINR", worksheet, ref topPosition, "SINR拉线图");
                    saveSerialThemePicToExcel(ref excelrow, "lte_PDCP_DL_Mb", worksheet, ref topPosition, "下载速率拉线图");
                }
            }
            else
            {
                saveTitleToExcel(worksheet, ref excelrow, ref topPosition, "无环测下载采样点，无法呈现轨迹图");
            }
        }

        private static void saveULInfoToExcel(StationAcceptInfo_HB btsAcceptInfo, List<LTECell> cellList, Excel.Worksheet worksheet, CellAcceptInfo_HB cellInfoCircle, ref int excelrow, ref float topPosition)
        {
            if (cellInfoCircle.ULTpList.Count > 0 && cellInfoCircle.HasFoundValidPositionUlTp)
            {
                mainModel.DrawFlyLines = true;
                mainModel.SelectedLTECells = cellList;
                goToMapView(cellList, cellInfoCircle.ULTpList);
                if (btsAcceptInfo.IsFddBts)
                {
                    saveSerialThemePicToExcel(ref excelrow, "LTE_FDD:PDCP_UL_Mb", worksheet, ref topPosition, "上传速率拉线图");
                }
                else
                {
                    saveSerialThemePicToExcel(ref excelrow, "lte_PDCP_UL_Mb", worksheet, ref topPosition, "上传速率拉线图");
                }
            }
            else
            {
                saveTitleToExcel(worksheet, ref excelrow, ref topPosition, "无环测上传采样点，无法呈现轨迹图");
            }
        }

        private static void setHasTpCellValue(Excel.Worksheet worksheet, int rowIndex, int columnIndex, bool isValid)
        {
            worksheet.Cells[rowIndex, columnIndex] = isValid ? "是" : "否";
            setCellBackColor(worksheet, rowIndex, columnIndex, isValid);
        }
        private static void saveCirCleAcceptInfoToWorksheet(Excel.Worksheet worksheet, ref int excelrow
            , string strKey, string strValue, bool isValid)
        {
            excelrow++;
            worksheet.Cells[excelrow, 1] = strKey;
            worksheet.Cells[excelrow, 2] = strValue;
            Excel.Range excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 2], worksheet.Cells[excelrow, 4]);
            excelRange.Merge(excelRange.MergeCells);
            setCellBackColor(excelRange, isValid);
        }
        private static void saveCallTracePicToExcel(Excel.Worksheet worksheet, ref int excelrow, ref float topPosition
            , string callTypeName, List<TestPoint> pointList, List<Event> evtList)
        {
            mainModel.ClearDTData();
            if (pointList.Count > 0)
            {
                if (evtList != null && evtList.Count > 0)
                {
                    foreach (Event evt in evtList)
                    {
                        mainModel.DTDataManager.Add(evt);
                    }
                }

                goToMapView(curCellList, pointList, false);
                savePicToExcel(ref excelrow, worksheet, ref topPosition, callTypeName + "轨迹图");
            }
            else
            {
                //saveTitleToExcel(worksheet, ref excelrow, ref topPosition, "无环测" + callTypeName + "采样点，无法呈现轨迹图")
            }
        }
        private static void reSetPciMapView(List<LTECell> cellList, bool isFddBts)
        {
            string strSerialByName = isFddBts ? "LTE_FDD:PCI" : "lte_PCI";
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName(strSerialByName);
            if (msi != null)
            {
                List<Color> colorList = new List<Color> { Color.Red, Color.Green, Color.Blue, Color.GreenYellow, Color.Gray, Color.DarkOrange };
                if (colorList.Count >= cellList.Count)
                {
                    msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                    int i = 0;
                    foreach (LTECell cell in cellList)
                    {
                        DTParameterRangeColor paramColor = new DTParameterRangeColor(cell.PCI, cell.PCI, colorList[i]);
                        paramColor.MaxIncluded = true;
                        msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                        i++;
                    }
                }
            }
        }

        public static string ExportInDoorReport(StationAcceptInfo_HB btsAcceptInfo, List<LTECell> cellList, string folderPath)
        {
            Excel.Application excel = new Excel.Application();
            Excel.Workbook workbook = excel.Workbooks.Add(true);
            Excel.Worksheet worksheet = (Excel.Worksheet)excel.ActiveSheet;

            try
            {
                saveInDoorSumInfoToNewSheet(btsAcceptInfo, worksheet);

                List<AcceptInfoBase_HB> acceptInfoList = new List<AcceptInfoBase_HB>();
                foreach (var cellInfoPair in btsAcceptInfo.CellAcceptInfoDic)
                {
                    CellAcceptInfo_HB cellInfo = cellInfoPair.Value;
                    acceptInfoList.AddRange(cellInfo.TestPositionAcceptInfoDic.Values);
                }

                worksheet = (Excel.Worksheet)excel.Sheets.Add();
                worksheet.Name = "室内站单站验收报告";
                ((Excel.Range)worksheet.Columns["A:D", System.Type.Missing]).ColumnWidth = 22;
                Excel.Range excelRange;
                int excelrow = 0;
                int titleSize = 12;

                saveOneLevelTitleToWorksheet(worksheet, ref excelrow, "单验总体情况");

                excelrow++;
                worksheet.Cells[excelrow, 1] = "基站站名：";
                excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 1], worksheet.Cells[excelrow, 1]);
                excelRange.Font.Size = titleSize;
                excelRange.Font.Bold = true;
                worksheet.Cells[excelrow, 2] = btsAcceptInfo.CellParamInfo.BtsName;

                worksheet.Cells[excelrow, 3] = "测试日期";
                excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 3], worksheet.Cells[excelrow, 3]);
                excelRange.Font.Size = titleSize;
                excelRange.Font.Bold = true;
                worksheet.Cells[excelrow, 4] = btsAcceptInfo.TestTimeDes;

                foreach (var cellInfo in acceptInfoList)
                {
                    saveTwoLevelTitleToWorksheet(worksheet, ref excelrow, cellInfo.TestPosition + "测试指标详情");
                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "EnodeBID", cellInfo.EnodeBids);
                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "PCI", cellInfo.PCIs);
                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "TAC", cellInfo.TACs);

                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "综合覆盖率1", cellInfo.CoverRate1Des, cellInfo.IsCoverRate1Valid);
                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "综合覆盖率2", cellInfo.CoverRate2Des);
                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "综合覆盖率3", cellInfo.CoverRate3Des);
                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "平均RSRP", cellInfo.RsrpAvg);
                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "平均SINR", cellInfo.SinrAvg);
                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "平均下载速率", cellInfo.PdcpDLAvg, cellInfo.IsPdcpDLAvgValid);
                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "下载速率峰值", cellInfo.PdcpDLMax);
                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "平均上传速率", cellInfo.PdcpULAvg, cellInfo.IsPdcpULAvgValid);
                    saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "上传速率峰值", cellInfo.PdcpULMax);

                    addOneEmptyColumn(worksheet, ref excelrow);
                }
                excelRange = worksheet.get_Range(worksheet.Cells[2, 1], worksheet.Cells[2, 4]);
                excelRange.RowHeight = 22;

                #region 室分站覆盖图不再呈现
                saveOneLevelTitleToWorksheet(worksheet, ref excelrow, "单验覆盖呈现图");

                excelRange = worksheet.get_Range(worksheet.Cells[1, 1], worksheet.Cells[excelrow, 4]);
                excelRange.Cells.Borders.LineStyle = Excel.XlLineStyle.xlContinuous;

                #region 地图显示
                float topPosition = (float)(((excelrow - 3) * 13.6) + 80);//减去已设置RowHeight的几行，再加上这几行的行高之和
                excelrow += 2;

                mainModel.DrawFlyLines = true;
                mainModel.SelectedLTECells.Clear();
                mainModel.SelectedLTECells = cellList;

                foreach (var cellInfoPair in btsAcceptInfo.CellAcceptInfoDic)
                {
                    CellAcceptInfo_HB cellInfo = cellInfoPair.Value;
                    foreach (var testPositionInfo in cellInfo.TestPositionAcceptInfoDic.Values)
                    {
                        if (testPositionInfo.TpListOfValidDlRsrp.Count > 0 && testPositionInfo.HasFoundValidSingleTestPosition)
                        {
                            goToMapView(cellList, testPositionInfo.TpListOfValidDlRsrp);

                            saveSerialThemePicToExcel(ref excelrow, "TD_LTE_RSRP", worksheet, ref topPosition, testPositionInfo.TestPosition + "覆盖呈现图");
                        }
                    }
                }
                #endregion
                #endregion

                string strFileName = string.Format("{0}\\{1}.xlsx", folderPath, btsAcceptInfo.CellParamInfo.ENodeBID);
                excel.DisplayAlerts = false;
                workbook.SaveAs(strFileName, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Excel.XlSaveAsAccessMode.xlNoChange, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value);
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("成功导出" + btsAcceptInfo.CellParamInfo.BtsName + "的单验报告");
                return strFileName;
            }
            catch (Exception ex)
            {
                writeErrLog(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
                return "";
            }
            finally
            {
                excel.Quit();
                //GC.Collect();
            }
        }
        private static void saveInDoorSumInfoToNewSheet(StationAcceptInfo_HB btsAcceptInfo, Excel.Worksheet worksheet)
        {
            worksheet.Name = "汇总指标";
            ((Excel.Range)worksheet.Columns["A:D", System.Type.Missing]).ColumnWidth = 22;
            Excel.Range excelRange;
            int excelrow = 0;

            List<CellAcceptInfo_HB> acceptInfoList = new List<CellAcceptInfo_HB>(btsAcceptInfo.CellAcceptInfoDic.Values);
            acceptInfoList.Add(btsAcceptInfo);

            foreach (var cellInfo in acceptInfoList)
            {
                if (cellInfo is StationAcceptInfo_HB)
                {
                    saveTwoLevelTitleToWorksheet(worksheet, ref excelrow, "整体测试指标详情");
                }
                else
                {
                    saveTwoLevelTitleToWorksheet(worksheet, ref excelrow, cellInfo.CellParamInfo.CellName);
                }

                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "综合覆盖率1", cellInfo.CoverRate1Des, cellInfo.IsCoverRate1Valid);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "综合覆盖率2", cellInfo.CoverRate2Des);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "综合覆盖率3", cellInfo.CoverRate3Des);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "平均RSRP", cellInfo.RsrpAvg);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "平均SINR", cellInfo.SinrAvg);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "平均下载速率", cellInfo.PdcpDLAvg, cellInfo.IsPdcpDLAvgValid);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "下载速率峰值", cellInfo.PdcpDLMax);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "平均上传速率", cellInfo.PdcpULAvg, cellInfo.IsPdcpULAvgValid);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "上传速率峰值", cellInfo.PdcpULMax);

                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "CSFB全程呼叫成功率", cellInfo.CsfbCallInfo.SucessRatioAllCallDes, cellInfo.CsfbCallInfo.IsValidSucessRatioAllCall);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "VoLTE语音全程呼叫成功率", cellInfo.VolteVoiceCallInfo.SucessRatioAllCallDes, cellInfo.VolteVoiceCallInfo.IsValidSucessRatioAllCall);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "VoLTE语音业务接通率", cellInfo.VolteVoiceCallInfo.SucessRatioMoCallDes, cellInfo.VolteVoiceCallInfo.IsValidSucessRatioMoCall);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "VoLTE视频全程呼叫成功率", cellInfo.VolteVideoCallInfo.SucessRatioAllCallDes, cellInfo.VolteVideoCallInfo.IsValidSucessRatioAllCall);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "VoLTE视频业务接通率", cellInfo.VolteVideoCallInfo.SucessRatioMoCallDes, cellInfo.VolteVideoCallInfo.IsValidSucessRatioMoCall);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "SRVCC切换成功率", cellInfo.RatioInfoSRVCC.RatioDes, cellInfo.RatioInfoSRVCC.IsValidRatio);

                saveTwoLevelTitleToWorksheet(worksheet, ref excelrow, "切换指标", false);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "切换成功率", cellInfo.RatioInfoHandover.RatioDes, cellInfo.IsValidHandOverSucessRatio);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "是否存在室内外占用", cellInfo.HasInAndOutSrc ? "是" : "否", cellInfo.IsValidInAndOutSrc);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "切换小区", cellInfo.HandOverCellInfosDes);

                saveTwoLevelTitleToWorksheet(worksheet, ref excelrow, "外泄指标", false);
                saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, "外泄受控比例", cellInfo.RatioInfoWx.RatioDes, cellInfo.RatioInfoWx.IsValidRatio);
                addOneEmptyColumn(worksheet, ref excelrow);
            }
            excelRange = worksheet.get_Range(worksheet.Cells[1, 1], worksheet.Cells[excelrow, 4]);
            excelRange.Cells.Borders.LineStyle = Excel.XlLineStyle.xlContinuous;
        }
        private static void saveFloorAcceptInfoToWorksheet(Excel.Worksheet worksheet, ref int excelrow
            , string strKey, object objValue)
        {
           saveFloorAcceptInfoToWorksheet(worksheet, ref excelrow, strKey, objValue, true);
        }
        private static void saveFloorAcceptInfoToWorksheet(Excel.Worksheet worksheet, ref int excelrow
            , string strKey, object objValue, bool isValid)
        {
            excelrow++;
            worksheet.Cells[excelrow, 1] = strKey;
            worksheet.Cells[excelrow, 3] = objValue;
            Excel.Range excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 1], worksheet.Cells[excelrow, 2]);
            excelRange.Merge(excelRange.MergeCells);
            excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 3], worksheet.Cells[excelrow, 4]);
            excelRange.Merge(excelRange.MergeCells);
            excelRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft;
            excelRange.RowHeight = 13.5;
            setCellBackColor(excelRange, isValid);
        }

        private static void setCellBackColor(Excel.Worksheet worksheet, int rowIndex, int columnIndex, bool isValid)
        {
            if (!isValid)
            {
                Excel.Range excelRange = worksheet.get_Range(worksheet.Cells[rowIndex, columnIndex], worksheet.Cells[rowIndex, columnIndex]);
                setCellBackColor(excelRange, isValid);
            }
        }
        private static void setCellBackColor(Excel.Range excelRange, bool isValid)
        {
            if (!isValid)
            {
                Color color = Color.Red;
                excelRange.Interior.Color = System.Drawing.Color.FromArgb(color.A, color.B, color.G, color.R).ToArgb();
            }
        }
        private static void saveOneLevelTitleToWorksheet(Excel.Worksheet worksheet, ref int excelrow, string strTitle)
        {
            excelrow++;
            worksheet.Cells[excelrow, 1] = strTitle;
            Excel.Range excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 1], worksheet.Cells[excelrow, 4]);
            excelRange.RowHeight = 28;
            excelRange.Font.Size = 17;
            excelRange.Font.Bold = true;
            excelRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
            excelRange.Merge(excelRange.MergeCells);
        }
        private static void saveTwoLevelTitleToWorksheet(Excel.Worksheet worksheet, ref int excelrow, string strTitle
            , bool isBold = true)
        {
            excelrow++;
            worksheet.Cells[excelrow, 1] = strTitle;
            Excel.Range excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 1], worksheet.Cells[excelrow, 4]);
            excelRange.Font.Size = 12;
            excelRange.Font.Bold = isBold;
            excelRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
            excelRange.Merge(excelRange.MergeCells);
        }

        private static void addOneEmptyColumn(Excel.Worksheet worksheet, ref int excelrow)
        {
            excelrow++;
            Excel.Range excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 1], worksheet.Cells[excelrow, 4]);
            excelRange.Merge(excelRange.MergeCells);
        }
        private static void goToMapView(List<LTECell> cellList, List<TestPoint> pntLst)
        { 
            goToMapView(cellList, pntLst, true);
        }
        private static void goToMapView(List<LTECell> cellList, List<TestPoint> pntLst, bool isClearDatas)
        {
            MinMaxLongitudeLatitude lngLat = new MinMaxLongitudeLatitude();
            if (cellList != null && cellList.Count > 0)
            {
                foreach (LTECell cell in cellList)
                {
                    setMinMaxLongitudeLatitude(lngLat, cell.EndPointLongitude, cell.EndPointLatitude);
                }
            }

            if (isClearDatas)
            {
                mainModel.ClearDTData();
            }
            if (pntLst != null)
            {
                foreach (TestPoint tp in pntLst)
                {
                    mainModel.DTDataManager.Add(tp);

                    setMinMaxLongitudeLatitude(lngLat, tp.Longitude, tp.Latitude);
                }
            }
            mainModel.FireDTDataChanged(StationAcceptAna_HB.GetInstance());

            MasterCom.MTGis.DbRect rect = new MasterCom.MTGis.DbRect(lngLat.MinLongitude, lngLat.MinLatitude - 0.00015, 
                lngLat.MaxLongitude, lngLat.MaxLatitude + 0.00015);
            mainModel.MainForm.GetMapForm().GoToView(rect);
        }

        private static void setMinMaxLongitudeLatitude(MinMaxLongitudeLatitude lngLat, double longitude, double latitude)
        {
            if (lngLat.MaxLongitude < longitude)
            {
                lngLat.MaxLongitude = longitude;
            }
            if (lngLat.MinLongitude > longitude && longitude != 0)
            {
                lngLat.MinLongitude = longitude;
            }
            if (lngLat.MaxLatitude < latitude)
            {
                lngLat.MaxLatitude = latitude;
            }
            if (lngLat.MinLatitude > latitude && longitude != 0)
            {
                lngLat.MinLatitude = latitude;
            }
        }

        private class MinMaxLongitudeLatitude
        {
            public double MaxLongitude { get; set; } = 0;
            public double MinLongitude { get; set; } = 99999;
            public double MaxLatitude { get; set; } = 0;
            public double MinLatitude { get; set; } = 99999;
        }


        private static void fireMapSerialTheme(string themeName)
        {
            MapDTLayer dtLayer = mainModel.MainForm.GetMapForm().GetDTLayer();
            dtLayer.IsBySerials = false;
            dtLayer.FlyColorFromOrig = true;

            mainModel.FireSetDefaultMapSerialTheme(themeName);
            dtLayer.Invalidate();
        }
        private static void saveSerialThemePicToExcel(ref int excelrow, string themeName, Excel.Worksheet worksheet, ref float topPosition, string strTitle)
        {
            fireMapSerialTheme(themeName);
            savePicToExcel(ref excelrow, worksheet, ref topPosition, strTitle);
        }
        private static void savePicToExcel(ref int excelrow, Excel.Worksheet worksheet, ref float topPosition, string strTitle)
        {
            saveTitleToExcel(worksheet, ref excelrow, ref topPosition, strTitle);

            string imgPath = string.Format(System.Windows.Forms.Application.StartupPath + "/singleStation1.png");
            if (System.IO.File.Exists(imgPath))
            {
                System.IO.File.Delete(imgPath);
            }
            System.Threading.Thread.Sleep(100);
            mainModel.MainForm.GetMapForm().OutputCurrentResolutionMap(imgPath);

            worksheet.Shapes.AddPicture(imgPath, Microsoft.Office.Core.MsoTriState.msoFalse
                , Microsoft.Office.Core.MsoTriState.msoCTrue, 10, topPosition + 3, 712, 300);

            int rowIndex = excelrow;
            int columnIndex = 8;
            foreach (MasterCom.RAMS.Func.MapForm.SerialCbsClrInfo serialInfo in mainModel.MainForm.LegendPanel.SerialCbsClrInfoList)
            {
                string strRange = serialInfo.countInfoDesc + "（" + serialInfo.range.RangeDescription + ")";
                worksheet.Cells[rowIndex, columnIndex] = "█";
                Excel.Range excelRange = worksheet.get_Range(worksheet.Cells[rowIndex, columnIndex], worksheet.Cells[rowIndex, columnIndex]);
                excelRange.Font.Bold = true;
                excelRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;
                try
                {
                    Color color = ((DTParameterRangeColor)serialInfo.range).Value;
                    excelRange.Font.Color = System.Drawing.Color.FromArgb(color.A, color.B, color.G, color.R).ToArgb();//winform颜色与excel颜色略有不同
                }
                catch
                {
                    //continue
                }

                worksheet.Cells[rowIndex, columnIndex + 1] = strRange;
                excelRange = worksheet.get_Range(worksheet.Cells[rowIndex, columnIndex + 1], worksheet.Cells[rowIndex, columnIndex + 4]);
                excelRange.Font.Bold = true;
                excelRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft;
                excelRange.Merge(excelRange.MergeCells);
                rowIndex++;
            }
            topPosition += 336.8f;
            excelrow += 25;
        }
        private static void saveTitleToExcel(Excel.Worksheet worksheet, ref int excelrow, ref float topPosition, string strTitle)
        {
            worksheet.Shapes.AddTextEffect(Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect1, strTitle, "Black", 15,
                Microsoft.Office.Core.MsoTriState.msoFalse, Microsoft.Office.Core.MsoTriState.msoTrue, 10, topPosition);
            excelrow += 2;
            topPosition += 27;
        }
        private static void writeErrLog(string strErr)
        {
            string path = BackgroundFuncManager.BackgroundLogSavePath;

            if (!File.Exists(path))
            {
                File.Create(path).Close();
            }
            using (StreamWriter sw = File.AppendText(path))
            {
                sw.Write(DateTime.Now.ToString() + "  " + strErr + "\r\n");
                sw.Flush();
                sw.Close();
            }
        }
    }
}
