﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Model
{
    public class TestPointParmValueGet_Gsm : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_Gsm() : base() { }

        public override string Name
        {
            get { return "GSM参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            int? lac = (int?)tp["LAC"];
            if (lac != null && lac >= 0 && lac <= 65535)
            {
                return lac;
            }
            return null;
        }

        public override object GetCI(TestPoint tp)
        {
            int? ci = (int?)tp["CI"];
            if (ci != null && ci >= 0 && ci <= 65535)
            {
                return ci;
            }
            return null;
        }

        public override object GetBCCH(TestPoint tp)
        {
            short? bcch = (short?)tp["BCCH"];
            if (bcch != null && bcch >= 0 && bcch <= 1024)
            {
                return bcch;
            }
            return null;
        }

        public override object GetBSIC(TestPoint tp)
        {
            byte? bsic = (byte?)tp["BSIC"];
            if (bsic != null && bsic >= 0 && bsic <= 77)
            {
                return bsic;
            }
            return null;
        }

        public override object GetRxlev(TestPoint tp)
        {
            short? rxLev = (short?)tp["RxLevSub"];
            if (rxLev != null && rxLev >= -120 && rxLev <= -10)
            {
                return rxLev;
            }
            return null;
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, null, null,
                (short?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            short? nBcch = (short?)tp["N_BCCH", idx];
            if (nBcch != null && nBcch >= 0 && nBcch <= 1024)
            {
                return nBcch;
            }
            return null;
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            byte? nBsic = (byte?)tp["N_BSIC", idx];
            if (nBsic != null && nBsic >= 0 && nBsic <= 77)
            {
                return nBsic;
            }
            return null;
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            short? nRxLev = (short?)tp["N_RxLev", idx];
            if (nRxLev != null && nRxLev >= -120 && nRxLev <= -10)
            {
                return nRxLev;
            }
            return null;
        }
    }

    public class TestPointParmValueGet_LteGsm : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_LteGsm() : base()
        {
        }
        public override string Name
        {
            get { return "TDD-LTE_GSM参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            int? lac = (int?)tp["lte_gsm_SC_LAC"];
            if (lac != null && lac >= 0 && lac <= 65535)
            {
                return lac;
            }
            return null;
        }

        public override object GetCI(TestPoint tp)
        {
            int? ci = (int?)tp["lte_gsm_SC_CI"];
            if (ci != null && ci >= 0 && ci <= 65535)
            {
                return ci;
            }
            return null;
        }

        public override object GetBCCH(TestPoint tp)
        {
            short? bcch = (short?)tp["lte_gsm_SC_BCCH"];
            if (bcch != null && bcch >= 0 && bcch <= 1024)
            {
                return bcch;
            }
            return null;
        }

        public override object GetBSIC(TestPoint tp)
        {
            byte? bsic = (byte?)tp["lte_gsm_SC_BSIC"];
            if (bsic != null && bsic >= 0 && bsic <= 77)
            {
                return bsic;
            }
            return null;
        }

        public override object GetRxlev(TestPoint tp)
        {
            short? rxLev = (short?)tp["lte_gsm_DM_RxLevSub"];
            if (rxLev != null && rxLev >= -120 && rxLev <= -10)
            {
                return rxLev;
            }
            return null;
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            short? nBcch = (short?)tp["lte_gsm_NC_BCCH", idx];
            if (nBcch != null && nBcch >= 0 && nBcch <= 1024)
            {
                return nBcch;
            }
            return null;
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            byte? nBsic = (byte?)tp["lte_gsm_NC_BSIC", idx];
            if (nBsic != null && nBsic >= 0 && nBsic <= 77)
            {
                return nBsic;
            }
            return null;
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            short? nRxLev = (short?)tp["lte_gsm_NC_RxLev", idx];
            if (nRxLev != null && nRxLev >= -120 && nRxLev <= -10)
            {
                return nRxLev;
            }
            return null;
        }
    }

    public class TestPointParmValueGet_LteFddGsm : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_LteFddGsm() : base() { }

        public override string Name
        {
            get { return "FDD-LTE_GSM参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["lte_fdd_gsm_SC_LAC"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["lte_fdd_gsm_SC_CI"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["lte_fdd_gsm_SC_BCCH"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["lte_fdd_gsm_SC_BSIC"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["lte_fdd_gsm_DM_RxLevSub"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["lte_fdd_gsm_NC_BCCH", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["lte_fdd_gsm_NC_BSIC", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["lte_fdd_gsm_NC_RxLev", idx];
        }
    }

    public class TestPointParmValueGet_Td : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_Td() : base() { }

        public override string Name
        {
            get { return "TD参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)(int?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["TD_SCell_LAC"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["TD_SCell_CI"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["TD_SCell_UARFCN"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["TD_SCell_CPI"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["TD_PCCPCH_RSCP"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)(int?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["TD_NCell_UARFCN", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["TD_NCell_CPI", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["TD_NCell_PCCPCH_RSCP", idx];
        }
    }

    public class TestPointParmValueGet_LteTd : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_LteTd() : base() { }

        public override string Name
        {
            get { return "TDD-LTE_TD参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int)GetBCCH(tp), (byte?)(int?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["lte_td_SC_LAC"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["lte_td_SC_CellID"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["lte_td_SC_UARFCN"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["lte_td_SC_CPI"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["lte_td_DM_PCCPCH_RSCP"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)(int?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["lte_td_NCell_UARFCN", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["lte_td_NCell_CPI", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["lte_td_NCell_PCCPCH_RSCP", idx];
        }
    }

    public class TestPointParmValueGet_W : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_W() : base() { }

        public override string Name
        {
            get { return "WCDMA参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            WCell cell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)(int?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["W_SysLAI"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["W_SysCellID"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["W_frequency"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["W_Reference_PSC"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["W_Reference_RSCP"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            WCell cell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)(int?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["W_SNeiFreq", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["W_SNeiPSC", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["W_SNeiRSCP", idx];
        }
    }

    public class TestPointParmValueGet_LteFddW : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_LteFddW() : base() { }

        public override string Name
        {
            get { return "FDD-LTE_W参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            WCell cell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_SysLAI"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_SysCellID"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_frequency"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_Reference_PSC"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_TotalRSCP"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            WCell cell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["lte_fdd_wcdma_SNeiFreq", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["lte_fdd_wcdma_SNeiPSC", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["lte_fdd_wcdma_SNeiRSCP", idx];
        }
    }

    public class TestPointParmValueGet_Nr : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_Nr() : base() { }

        public override string Name
        {
            get { return "NR参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, (ushort?)(int?)GetLAC(tp)
                , (ushort?)(int?)GetCI(tp), (short?)(int?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            ushort? lac = (ushort?)tp["NR_TAC"];
            if (lac != null && lac >= 0 && lac <= 65534)
            {
                return lac;
            }
            return null;
        }

        public override object GetCI(TestPoint tp)
        {
            long? ci = (long?)tp["NR_NCI"];
            if (ci != null && ci >= 0 && ci <= 214748364700)
            {
                return ci;
            }
            return null;
        }

        public override object GetBCCH(TestPoint tp)
        {
            int? bcch = (int?)tp["NR_SSB_ARFCN"];
            if (bcch != null && bcch >= 0 && bcch <= 65535)
            {
                return bcch;
            }
            return null;
        }

        public override object GetBSIC(TestPoint tp)
        {
            short? bsic = (short?)tp["NR_PCI"];
            if (bsic != null && bsic >= 0 && bsic <= 32767)
            {
                return bsic;
            }
            return null;
        }

        public override object GetRxlev(TestPoint tp)
        {
            float? rxlev = (float?)tp["NR_SS_RSRP"];
            if (rxlev != null && rxlev >= -141 && rxlev <= 25)
            {
                return rxlev;
            }
            return null;
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            NRCell cell = CellManager.GetInstance().GetNearestNRCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            int? nBcch = (int?)tp["NR_NCell_ARFCN", idx];
            if (nBcch != null && nBcch >= 0 && nBcch <= 65535)
            {
                return nBcch;
            }
            return null;
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            short? nBsic = (short?)tp["NR_NCell_PCI", idx];
            if (nBsic != null && nBsic >= 0 && nBsic <= 32767)
            {
                return nBsic;
            }
            return null;
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            float? nRxlev = (float?)tp["NR_NCell_RSRP", idx];
            if (nRxlev != null && nRxlev >= -141 && nRxlev <= 25)
            {
                return nRxlev;
            }
            return null;
        }
    }



    public class TestPointParmValueGet_Lte : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_Lte() : base() { }

        public override string Name
        {
            get { return "TDD-LTE参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, (ushort?)(int?)GetLAC(tp)
                , (ushort?)(int?)GetCI(tp),(short?)(int?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            ushort? lac = (ushort?)tp["lte_TAC"];
            if (lac != null && lac >= 0 && lac <= 65534)
            {
                return lac;
            }
            return null;
        }

        public override object GetCI(TestPoint tp)
        {
            int? ci = (int?)tp["lte_ECI"];
            if (ci != null && ci >= 0 && ci <= 2147483647)
            {
                return ci;
            }
            return null;
        }

        public override object GetBCCH(TestPoint tp)
        {
            int? bcch = (int?)tp["lte_EARFCN"];
            if (bcch != null && bcch >= 0 && bcch <= 65535)
            {
                return bcch;
            }
            return null;
        }

        public override object GetBSIC(TestPoint tp)
        {
            short? bsic = (short?)tp["lte_PCI"];
            if (bsic != null && bsic >= 0 && bsic <= 32767)
            {
                return bsic;
            }
            return null;
        }

        public override object GetRxlev(TestPoint tp)
        {
            float? rxlev = (float?)tp["lte_RSRP"];
            if (rxlev != null && rxlev >= -141 && rxlev <= 25)
            {
                return rxlev;
            }
            return null;
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            int? nBcch = (int?)tp["lte_NCell_EARFCN", idx];
            if (nBcch != null && nBcch >= 0 && nBcch <= 65535)
            {
                return nBcch;
            }
            return null;
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            short? nBsic = (short?)tp["lte_NCell_PCI", idx];
            if (nBsic != null && nBsic >= 0 && nBsic <= 32767)
            {
                return nBsic;
            }
            return null;
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            float? nRxlev = (float?)tp["lte_NCell_RSRP", idx];
            if (nRxlev != null && nRxlev >= -141 && nRxlev <= 25)
            {
                return nRxlev;
            }
            return null;
        }
    }

    public class TestPointParmValueGet_LteFdd : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_LteFdd() : base() { }

        public override string Name
        {
            get { return "FDD-LTE参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["lte_fdd_SCell_LAC"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["lte_fdd_SCell_CI"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["lte_fdd_EARFCN"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["lte_fdd_PCI"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["lte_fdd_RSRP"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["lte_fdd_NCell_EARFCN", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["lte_fdd_NCell_PCI", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["lte_fdd_NCell_RSRP", idx];
        }
    }

    public class TestPointParmValueGet_GsmScan : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_GsmScan() : base() { }

        public override string Name
        {
            get { return "GSM-SCAN参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            ICell cell = GetNbCell(tp, 0);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return null;
        }

        public override object GetCI(TestPoint tp)
        {
            return null;
        }

        public override object GetBCCH(TestPoint tp)
        {
            return null;
        }

        public override object GetBSIC(TestPoint tp)
        {
            return null;
        }

        public override object GetRxlev(TestPoint tp)
        {
            return null;
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["GSCAN_BCCH", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["GSCAN_BSIC", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["GSCAN_RxLev", idx];
        }
    }

    public class TestPointParmValueGet_TdScan : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_TdScan() : base() { }

        public override string Name
        {
            get { return "TD-SCAN参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            ICell cell = GetNbCell(tp, 0);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return null;
        }

        public override object GetCI(TestPoint tp)
        {
            return null;
        }

        public override object GetBCCH(TestPoint tp)
        {
            return null;
        }

        public override object GetBSIC(TestPoint tp)
        {
            return null;
        }

        public override object GetRxlev(TestPoint tp)
        {
            return null;
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)(int?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["TDS_PCCPCH_Channel", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["TDS_PCCPCH_CPI", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["TDS_PCCPCH_RSCP", idx];
        }
    }

    public class TestPointParmValueGet_LteScan : TestPointParmValueGetBase
    {
        public TestPointParmValueGet_LteScan() : base()
        {
        }

        public override string Name
        {
            get { return "LTE-SCAN参数值获取"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            ICell cell = GetNbCell(tp, 0);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return null;
        }

        public override object GetCI(TestPoint tp)
        {
            return null;
        }

        public override object GetBCCH(TestPoint tp)
        {
            return null;
        }

        public override object GetBSIC(TestPoint tp)
        {
            return null;
        }

        public override object GetRxlev(TestPoint tp)
        {
            return null;
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["LTESCAN_TopN_EARFCN", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["LTESCAN_TopN_PCI", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["LTESCAN_TopN_PSS_RP", idx];
        }
    }


    public abstract class TestPointParmValueGetBase
    {
        protected TestPointParmValueGetBase()
        {
        }
        public static TestPointParmValueGetBase CreateInstance(TestPoint tp)
        {
            TestPointParmValueGetBase dealer = null;
            if (tp is TestPoint_NR)
            {
                dealer = new TestPointParmValueGet_Nr();
            }
            else if (tp is LTETestPointDetail || tp is LTEUepTestPoint)
            {
                dealer = getLteParam(tp);
            }
            else if (tp is TDTestPointDetail || tp is TDTestPointSummary)
            {
                dealer = new TestPointParmValueGet_Td();
            }
            else if (tp is WCDMATestPointDetail || tp is WCDMATestPointSummary)
            {
                dealer = new TestPointParmValueGet_W();
            }
            else if (tp is LTEFddTestPoint)
            {
                dealer = getFddParam(tp, dealer);
            }
            else if (tp is TestPointScan || tp is ScanTestPoint_G)
            {
                dealer = new TestPointParmValueGet_GsmScan();
            }
            else if (tp is ScanTestPoint_TD)
            {
                dealer = new TestPointParmValueGet_TdScan();
            }
            else if (tp is ScanTestPoint_LTE)
            {
                dealer = new TestPointParmValueGet_LteScan();
            }
            else if (tp is ScanTestPoint_NR)
            {
                dealer = null;
            }
            else
            {
                dealer = new TestPointParmValueGet_Gsm();
            }
            return dealer;
        }

        private static TestPointParmValueGetBase getLteParam(TestPoint tp)
        {
            TestPointParmValueGetBase dealer;
            switch (tp.NetworkType)
            {
                case TestPoint.ECurrNetType.GSM:
                    dealer = new TestPointParmValueGet_LteGsm();
                    break;
                case TestPoint.ECurrNetType.TD:
                    dealer = new TestPointParmValueGet_LteTd();
                    break;
                //case TestPoint.ECurrNetType.LTE:
                //case TestPoint.ECurrNetType.Unknow:
                //case TestPoint.ECurrNetType.NoService:
                //case TestPoint.ECurrNetType.Invalid:
                //    dealer = new TestPointParmValueGet_Lte();
                //    break;
                default:
                    dealer = new TestPointParmValueGet_Lte();
                    break;
            }

            return dealer;
        }

        private static TestPointParmValueGetBase getFddParam(TestPoint tp, TestPointParmValueGetBase dealer)
        {
            switch (tp.NetworkType)
            {
                case TestPoint.ECurrNetType.GSM:
                    dealer = new TestPointParmValueGet_LteFddGsm();
                    break;
                case TestPoint.ECurrNetType.WCDMA:
                    dealer = new TestPointParmValueGet_LteFddW();
                    break;
                case TestPoint.ECurrNetType.CDMA_EVDO:
                    break;
                //case TestPoint.ECurrNetType.LTE:
                //case TestPoint.ECurrNetType.Unknow:
                //case TestPoint.ECurrNetType.NoService:
                //case TestPoint.ECurrNetType.Invalid:
                //    dealer = new TestPointParmValueGet_LteFdd();
                //    break;
                default:
                    dealer = new TestPointParmValueGet_LteFdd();
                    break;
            }

            return dealer;
        }

        public abstract string Name { get; }

        public abstract ICell GetMainCell(TestPoint tp);

        public abstract object GetLAC(TestPoint tp);

        public abstract object GetCI(TestPoint tp);

        public abstract object GetBCCH(TestPoint tp);

        public abstract object GetBSIC(TestPoint tp);

        public abstract object GetRxlev(TestPoint tp);

        public abstract ICell GetNbCell(TestPoint tp, int idx);

        public abstract object GetNbBCCH(TestPoint tp, int idx);

        public abstract object GetNbBSIC(TestPoint tp, int idx);

        public abstract object GetNbRxlev(TestPoint tp, int idx);
    }

}
