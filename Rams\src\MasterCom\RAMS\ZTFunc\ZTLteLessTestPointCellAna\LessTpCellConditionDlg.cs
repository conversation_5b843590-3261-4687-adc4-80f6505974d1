﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LessTpCellConditionDlg : BaseDialog
    {
        public LessTpCellConditionDlg()
            : base()
        {
            InitializeComponent();
        }

        public void GetCondition(out int sec)
        {
            sec = (int)numMaxDelaySec.Value;
        }

        public void SetCondition(int sec)
        {
            numMaxDelaySec.Value = (decimal)sec;
        }

    }
}
