﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.Util
{
    public partial class PictureBrowser : BaseForm
    {
        public PictureBrowser()
        {
            InitializeComponent();

            tsCbx.Items.Clear();
            tsCbx.Items.Add("适应窗体");
            tsCbx.Items.Add("原始大小");
            tsCbx.SelectedIndexChanged += tsCbx_SelectedIndexChanged;
            tsCbx.SelectedIndex = 0;
        }

        public void SetPictrue(Image img)
        {
            this.pictureBox.Image = img;
        }

        void tsCbx_SelectedIndexChanged(object sender, EventArgs e)
        {
            pictureBox.Location = new Point(3, 3);
            pictureBox.SizeMode = PictureBoxSizeMode.Normal;
            pictureBox.Width = panel1.Width - 6;
            pictureBox.Height = panel1.Height - 6;
            string mode = tsCbx.SelectedItem as string;
            switch (mode)
            {
                case "原始大小":
                    pictureBox.SizeMode = PictureBoxSizeMode.AutoSize;
                    break;
                case "适应窗体":
                    pictureBox.SizeMode = PictureBoxSizeMode.Zoom;
                    break;
                default:
                    break;
            }
        }

        private void panel1_SizeChanged(object sender, EventArgs e)
        {
            if (pictureBox.SizeMode == PictureBoxSizeMode.Zoom)
            {
                pictureBox.Width = panel1.Width - 6;
                pictureBox.Height = panel1.Height - 6;
            }
        }
    }
}
