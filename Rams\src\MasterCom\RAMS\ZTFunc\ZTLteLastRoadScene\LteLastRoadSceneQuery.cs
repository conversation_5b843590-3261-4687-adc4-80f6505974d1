﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTLteLastRoadScene;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteLastRoadSceneQuery : DIYAnalyseByFileBackgroundBase
    {
        private RoadSceneCondition funcCond = null;
        private List<RoadSceneBase> roadSceneSet = null;

        protected static readonly object lockObj = new object();
        private static LteLastRoadSceneQuery instance = null;
        public static LteLastRoadSceneQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteLastRoadSceneQuery();
                    }
                }
            }
            return instance;
        }

        protected LteLastRoadSceneQuery()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTE));
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTE));
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get
            {
                return "LTE路段场景分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22043, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                roadSceneSet = new List<RoadSceneBase>();
                return true;
            }
            if (!showFuncCondSetDlg())
            {
                return false;
            }
            roadSceneSet = new List<RoadSceneBase>();
            return true;
        }
        protected bool showFuncCondSetDlg()
        {
            ConditionDlg dlg = new ConditionDlg();
            dlg.Condition = funcCond;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.Condition;
            return true;
        }

        protected override void fireShowForm()
        {
            if (roadSceneSet.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }

            ResultForm frm = MainModel.GetObjectFromBlackboard(typeof(ResultForm)) as ResultForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new ResultForm();
            }
            frm.FillData(roadSceneSet);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            roadSceneSet = null;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    anaMultiCvrRoad_Scan(file);
                    anaWeakCoverRoad_DT(file);
                    anaFloatingRoad_DT(file);
                    anaHandoverRoad(file);
                }
            }
            catch (Exception e)
            {
                showException(e);
            }
        }

        private void anaMultiCvrRoad_Scan(DTFileDataManager file)
        {
            List<RoadSceneItemMulti> segs = new List<RoadSceneItemMulti>();
            RoadSceneItemMulti segItem = null;
            foreach (TestPoint tp in file.TestPoints)
            {
                if (!(tp is ScanTestPoint_LTE))
                {
                    return;
                }
                bool inRegion = condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                if (!inRegion)
                {//区域外点，保存上一段路段，continue
                    if (segItem!=null)
                    {
                        segs.Add(segItem);
                    }
                    segItem = null;
                    continue;
                }
                List<LTECell> cells;
                int multiLev = getTestPointMultiLev_Scan(tp, funcCond.MultiRsrp, funcCond.MultiBand, out cells);
                if (multiLev < funcCond.MultiLev)
                {//重叠覆盖度不符合条件，闭合段路并保存
                    if (segItem != null)
                    {
                        segs.Add(segItem);
                    }
                    segItem = null;
                    continue;
                }
                if (segItem == null)
                {
                    segItem = new RoadSceneItemMulti();
                }
                segItem.AddTestPoint(tp, cells);
            }

            if (segItem != null && !segs.Contains(segItem))
            {//避免遗漏最后一段
                segs.Add(segItem);
            }

            foreach (RoadSceneItemMulti seg in segs)
            {
                if (seg.MakeSummary(funcCond))
                {
                    List<LTECell> farCells = new List<LTECell>();
                    List<LTECell> nearCells = new List<LTECell>();
                    List<LTECell> cells = seg.Cells;
                    for (int i = 0; i < cells.Count; i++)
                    {
                        LTECell hostCell = cells[i];
                        for (int j = i + 1; j < cells.Count; j++)
                        {
                            LTECell guestCell = cells[j];
                            double distance = hostCell.GetDistance(guestCell.Longitude, guestCell.Latitude);
                            if (distance <= funcCond.MultiDistance)
                            {
                                if (!nearCells.Contains(hostCell))
                                {
                                    nearCells.Add(hostCell);
                                }
                                if (!nearCells.Contains(guestCell))
                                {
                                    nearCells.Add(guestCell);
                                }
                            }
                            else
                            {
                                if (!farCells.Contains(hostCell))
                                {
                                    farCells.Add(hostCell);
                                }
                                if (!farCells.Contains(guestCell))
                                {
                                    farCells.Add(guestCell);
                                }
                            }
                        }
                    }

                    seg.Sn = roadSceneSet.Count + 1;

                    if (nearCells.Count >= funcCond.MultiCellNum)
                    {
                        RoadSceneItemMulti item = new RoadSceneItemMulti(seg);
                        item.SceneName = "高密度站间距";
                        item.SetSpecifiedCells(nearCells);
                        roadSceneSet.Add(item);
                    }
                    if (farCells.Count >= funcCond.MultiCellNum)
                    {
                        RoadSceneItemMulti item = new RoadSceneItemMulti(seg);
                        item.SceneName = "多小区竞争覆盖";
                        item.SetSpecifiedCells(farCells);
                        roadSceneSet.Add(item);
                    }
                }
            }
        }

        private void anaWeakCoverRoad_DT(DTFileDataManager file)
        {
            List<RoadSceneWeakCover> segs = getWeakCoverSegments(file, -141, funcCond.WeakCvrRsrp);

            foreach (RoadSceneWeakCover item in segs)
            {
                if (item.MakeSummary(funcCond))
                {
                    item.Sn = roadSceneSet.Count + 1;
                    roadSceneSet.Add(item);
                }
            }
        }

        private void anaFloatingRoad_DT(DTFileDataManager file)
        {
            List<RoadSceneFastFade> segs = getFastFadeSegments(file);

            foreach (RoadSceneFastFade item in segs)
            {
                if (item.MakeSummary(funcCond))
                {
                    item.Sn = roadSceneSet.Count + 1;
                    roadSceneSet.Add(item);
                }
            }
        }

        private List<RoadSceneWeakCover> getWeakCoverSegments(DTFileDataManager file, float rsrpMin, float rsrpMax)
        {
            List<RoadSceneWeakCover> segs = new List<RoadSceneWeakCover>();
            RoadSceneWeakCover segItem = null;
            foreach (TestPoint tp in file.TestPoints)
            {
                if (!CheckTestPoint(tp))
                {
                    break;
                }
                segItem = setRoadSceneWeakCover(rsrpMin, rsrpMax, segs, segItem, tp);
            }
            if (segItem != null && !segs.Contains(segItem))
            {
                segs.Add(segItem);
            }
            return segs;
        }

        private RoadSceneWeakCover setRoadSceneWeakCover(float rsrpMin, float rsrpMax, List<RoadSceneWeakCover> segs, RoadSceneWeakCover segItem, TestPoint tp)
        {
            bool inRegion = condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
            if (!inRegion)
            {//不在区域内，封闭上一段道路，跳过该采样点
                if (segItem != null)
                {
                    segs.Add(segItem);
                }
                segItem = null;
            }
            else
            {
                float? rsrp = GetRSRP(tp);
                if (rsrpMin <= rsrp && rsrp <= rsrpMax)
                {
                    if (segItem == null)
                    {
                        segItem = new RoadSceneWeakCover();
                    }
                    segItem.AddTestPoint(tp);
                }
                else if (segItem != null)
                {//封闭上一段道路
                    segs.Add(segItem);
                    segItem = null;
                }
            }

            return segItem;
        }

        private List<RoadSceneFastFade> getFastFadeSegments(DTFileDataManager file)
        {
            List<RoadSceneFastFade> segs = new List<RoadSceneFastFade>();
            RoadSceneFastFade segItem = null;
            foreach (TestPoint tp in file.TestPoints)
            {
                if (!CheckTestPoint(tp))
                {
                    break;
                }
                bool inRegion = condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                if (!inRegion)
                {//不在区域内，封闭上一段道路，跳过该采样点
                    if (segItem != null)
                    {
                        segs.Add(segItem);
                    }
                    segItem = null;
                    continue;
                }
                segItem = setRoadSceneFastFade(segs, segItem, tp);
            }
            if (segItem != null && !segs.Contains(segItem))
            {
                segs.Add(segItem);
            }
            return segs;
        }

        private RoadSceneFastFade setRoadSceneFastFade(List<RoadSceneFastFade> segs, RoadSceneFastFade segItem, TestPoint tp)
        {
            LTECell cell = tp.GetMainLTECell_TdOrFdd();
            float? rsrp = GetRSRP(tp);
            if (-141 <= rsrp && rsrp <= 25 && cell != null)
            {
                if (segItem == null)
                {
                    segItem = new RoadSceneFastFade(cell);
                }
                else if (segItem.MainCell != cell)
                {
                    segs.Add(segItem);
                    segItem = new RoadSceneFastFade(cell);
                }
                segItem.AddTestPoint(tp);
            }
            else if (segItem != null)
            {//封闭上一段道路
                segs.Add(segItem);
                segItem = null;
            }

            return segItem;
        }

        private void anaHandoverRoad(DTFileDataManager file)
        {
            if (!CheckTestPoint(file.TestPoints[0]))
            {
                return;
            }

            List<Event> hoEvents = new List<Event>();
            foreach (Event evt in file.Events)
            {
                if (evt.ID == 851)
                {//intra ho
                    hoEvents.Add(evt);
                }
            }

            List<RoadSceneIntraHo> segs = new List<RoadSceneIntraHo>();
            RoadSceneIntraHo hoSeg = null;
            List<TestPoint> prePointSet = null;
            int testPointIdx = 0;
            for (int i = 0; i < hoEvents.Count - 1; i++)
            {//分析一次切换的前后采样点，得到切换前后的小区，判断是否同站。
                Event evt1 = hoEvents[i];
                Event evt2 = hoEvents[i + 1];
                List<TestPoint> tpSet1 = prePointSet;
                if (tpSet1 == null)
                {
                    tpSet1 = new List<TestPoint>();
                }
                List<TestPoint> tpSet2 = new List<TestPoint>();

                prePointSet = getPrePointSet(file, ref testPointIdx, evt1, evt2, tpSet1, tpSet2);

                hoSeg = addSegsData(segs, hoSeg, evt1, tpSet1, tpSet2);
            }
            if (hoSeg != null && hoSeg.HandoverNum > 2 && !segs.Contains(hoSeg))
            {//避免遗漏最后一段
                segs.Add(hoSeg);
            }

            addRoadSceneSet(segs);
        }

        private RoadSceneIntraHo addSegsData(List<RoadSceneIntraHo> segs, RoadSceneIntraHo hoSeg, Event evt1, List<TestPoint> tpSet1, List<TestPoint> tpSet2)
        {
            LTECell orgCell = getSegMainCell(tpSet1);
            LTECell tarCell = getSegMainCell(tpSet2);
            if (orgCell != null && tarCell != null
                && orgCell.Altitude >= funcCond.SiteAltitude
               && orgCell.BelongBTS == tarCell.BelongBTS)
            {
                if (hoSeg == null)
                {
                    hoSeg = new RoadSceneIntraHo();
                    hoSeg.AddCell(orgCell);
                    hoSeg.AddTestPoints(tpSet1);
                }
                hoSeg.AddHoEvent(evt1);
                hoSeg.AddCell(tarCell);
                hoSeg.AddTestPoints(tpSet2);
            }
            else if (hoSeg != null)
            {
                if (hoSeg.HandoverNum > 2)
                {
                    segs.Add(hoSeg);
                }
                hoSeg = null;
            }

            return hoSeg;
        }

        private List<TestPoint> getPrePointSet(DTFileDataManager file, ref int testPointIdx, Event evt1, Event evt2, List<TestPoint> tpSet1, List<TestPoint> tpSet2)
        {
            List<TestPoint> prePointSet;
            for (; testPointIdx < file.TestPoints.Count; testPointIdx++)
            {
                TestPoint tp = file.TestPoints[testPointIdx];
                if (tp.SN < evt1.SN)
                {
                    if (evt1.Time - tp.Time <= 200)
                    {//切换前200秒采样点
                        tpSet1.Add(tp);
                    }
                }
                else if (tp.SN < evt2.SN)
                {
                    tpSet2.Add(tp);
                }
                else
                {
                    break;
                }
            }
            prePointSet = tpSet2;
            return prePointSet;
        }

        private void addRoadSceneSet(List<RoadSceneIntraHo> segs)
        {
            foreach (RoadSceneIntraHo item in segs)
            {
                if (item.MakeSummary(funcCond))
                {
                    item.Sn = roadSceneSet.Count + 1;
                    roadSceneSet.Add(item);
                }
            }
        }

        /// <summary>
        /// 获取某段时间内采样点占用最多的主服小区
        /// </summary>
        /// <param name="testPoints"></param>
        /// <returns></returns>
        private LTECell getSegMainCell(List<TestPoint> testPoints)
        {
            Dictionary<LTECell, int> cellNumDic = new Dictionary<LTECell, int>();
            foreach (TestPoint tp in testPoints)
            {
                LTECell cell = tp.GetMainLTECell_TdOrFdd();
                if (cell != null)
                {
                    if (cellNumDic.ContainsKey(cell))
                    {
                        cellNumDic[cell]++;
                    }
                    else
                    {
                        cellNumDic[cell] = 1;
                    }
                }
            }

            LTECell mainCell = null;
            int maxNum = 0;
            foreach (LTECell cell in cellNumDic.Keys)
            {
                int num = cellNumDic[cell];
                if (num > maxNum)
                {
                    maxNum = num;
                    mainCell = cell;
                }
            }
            return mainCell;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="minRsrp"></param>
        /// <param name="band"></param>
        /// <returns></returns>
        private int getTestPointMultiLev_Scan(TestPoint tp,float mainRsrpMin,int band,out List<LTECell> cells)
        {
            int lev = 0;
            cells = new List<LTECell>();
            float? mainRsrp = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP",0];
            if (mainRsrp >= mainRsrpMin)
            {
                LTECell cell = tp.GetCell_LTEScan(0);
                if (cell!=null)
                {
                    cells.Add(cell);
                }
                lev++;//主强自身
                for (int i = 1; i < 16; i++)
                {
                    object obj = tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
                    if (obj == null)
                    {
                        continue;
                    }
                    float nRsrp = float.Parse(obj.ToString());
                    if (((float)mainRsrp) - nRsrp > band)
                    {//覆盖度外，跳出
                        break;
                    }
                    lev++;
                    cell = tp.GetCell_LTEScan(i);
                    if (cell != null)
                    {
                        cells.Add(cell);
                    }
                }
            }
            return lev;
        }

        /**
        /// <summary>
        /// 保存一段持续道路信息，不会进行持续距离的判断过滤。
        /// </summary>
        /// <param name="seg"></param>
        private void saveOneRoadSeg( RoadSceneBase seg)
        {
            if (seg==null)
            {
                return;
            }
            if (!roadSceneSet.Contains(seg))
            {
                roadSceneSet.Add(seg);
            }
        }
        */

        protected virtual bool CheckTestPoint(TestPoint tp)
        {
            if (tp is LTETestPointDetail)
            {
                return true;
            }
            return false;
        }
        public virtual float? GetRSRP(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }
        public virtual float? GetSINR(TestPoint tp)
        {
            return (float?)tp["lte_SINR"];
        }
        public virtual float? GetNRSRP(TestPoint tp, int index)
        {
            return (float?)tp["lte_NCell_RSRP", index];
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.其他; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                if (funcCond != null)
                {
                    param["FuncCond"] = funcCond.Param;
                }
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("FuncCond"))
                {
                    if (funcCond == null)
                    {
                        funcCond = new RoadSceneCondition();
                    }
                    funcCond.Param = param["FuncCond"] as Dictionary<string, object>;
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CommonSimpleProperties(this, showFuncCondSetDlg);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (RoadSceneBase item in roadSceneSet)
            {
                BackgroundResult result = item.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                result.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            roadSceneSet.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            if (!BackgroundFuncConfigManager.GetInstance().AutoExportResult)
            {
                return;
            }
            List<NPOIRow> resultNPOIRowList = new List<NPOIRow>();

            NPOIRow rowTitle = new NPOIRow();
            rowTitle.AddCellValue("序号");
            rowTitle.AddCellValue("场景");
            rowTitle.AddCellValue("道路信息");
            rowTitle.AddCellValue("持续距离");
            rowTitle.AddCellValue("中心经度");
            rowTitle.AddCellValue("中心纬度");
            rowTitle.AddCellValue("平均RSRP");
            rowTitle.AddCellValue("平均SINR");
            rowTitle.AddCellValue("关联小区");
            resultNPOIRowList.Add(rowTitle);

            int sn = 0;
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                sn++;
                NPOIRow resultRow = new NPOIRow();
                resultRow.AddCellValue(sn);
                resultRow.AddCellValue(bgResult.StrDesc);
                resultRow.AddCellValue(bgResult.RoadDesc);
                resultRow.AddCellValue(bgResult.DistanceLast);
                resultRow.AddCellValue(bgResult.LongitudeMid);
                resultRow.AddCellValue(bgResult.LatitudeMid);
                resultRow.AddCellValue(bgResult.RxLevMean);
                resultRow.AddCellValue(bgResult.RxQualMean);
                resultRow.AddCellValue(bgResult.CellIDDesc);
                resultNPOIRowList.Add(resultRow);
            }
            this.BackgroundNPOIRowResultDic.Clear();
            this.BackgroundNPOIRowResultDic[this.Name] = resultNPOIRowList;
        }
        #endregion
   }

    public class LteLastRoadSceneQuery_FDD : LteLastRoadSceneQuery
    {
        private static LteLastRoadSceneQuery_FDD instance = null;
        public static new LteLastRoadSceneQuery_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteLastRoadSceneQuery_FDD();
                    }
                }
            }
            return instance;
        }

        public LteLastRoadSceneQuery_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTEFDD));
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTEFDD));
            carrierID = CarrierType.ChinaUnicom;
        }
        public override string Name
        {
            get
            {
                return "LTE_FDD路段场景分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26032, this.Name);
        }

        protected override bool CheckTestPoint(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return true;
            }
            return false;
        }
        public override float? GetRSRP(TestPoint tp)
        {
            return (float?)tp["lte_fdd_RSRP"];
        }
        public override float? GetSINR(TestPoint tp)
        {
            return (float?)tp["lte_fdd_SINR"];
        }
        public override float? GetNRSRP(TestPoint tp, int index)
        {
            return (float?)tp["lte_fdd_NCell_RSRP", index];
        }
    }

    public class RoadSceneBase
    {
        public RoadSceneBase()
        {
            TestPoints = new List<TestPoint>();
            Cells = new List<LTECell>();
            Events = new List<Event>();
            Messages = new List<Model.Message>();
        }
        public string SceneName
        { get; set; }
        public int Sn
        { get; set; }
        
        public List<TestPoint> TestPoints { get; set; }

        public List<Event> Events
        {
            get;
            protected set;
        }
        public List<MasterCom.RAMS.Model.Message> Messages
        { get; protected set; }

        public float RsrpMin
        { get; protected set; }
        public float RsrpMax
        { get; protected set; }
        public float RsrpAvg
        { get; protected set; }

        public float? SinrMin
        { get; protected set; }
        public float? SinrMax
        { get; protected set; }
        public float? SinrAvg
        { get; protected set; }

        public double LngMid
        {
            get
            {
                double lng = 0;
                if (TestPoints.Count>0)
                {
                    lng = TestPoints[TestPoints.Count / 2].Longitude;
                }
                return lng;
            }
        }
        public double LatMid
        {
            get
            {
                double lat = 0;
                if (TestPoints.Count > 0)
                {
                    lat = TestPoints[TestPoints.Count / 2].Latitude;
                }
                return lat;
            }
        }

        public string RoadDesc
        { get; protected set; }

        public string CellNames
        {
            get
            {
                StringBuilder names = new StringBuilder();
                foreach (LTECell cell in Cells)
                {
                    names.Append(cell.Name + ";");
                }
                return names.ToString().TrimEnd(';');
            }
        }

        public List<LTECell> Cells { get; protected set; }

        public double Distance
        { get; protected set; }

        public virtual bool MakeSummary(RoadSceneCondition cond)
        {
            List<double> lng = new List<double>();
            List<double> lat = new List<double>();
            lng.Add(TestPoints[0].Longitude);
            lat.Add(TestPoints[0].Latitude);
            lng.Add(LngMid);
            lat.Add(LatMid);
            lng.Add(TestPoints[TestPoints.Count - 1].Longitude);
            lat.Add(TestPoints[TestPoints.Count - 1].Latitude);
            RoadDesc = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(lng, lat);
            Distance = Math.Round(Distance, 2);
            return true;
        }

        public virtual void AddTestPoint(TestPoint tp)
        {
            if (TestPoints.Count > 0)
            {
                this.Distance += TestPoints[TestPoints.Count - 1].Distance2(tp.Longitude, tp.Latitude);
            }
            TestPoints.Add(tp);
        }

        readonly LteLastRoadSceneQuery query = LteLastRoadSceneQuery.GetInstance();
        readonly LteLastRoadSceneQuery_FDD queryfdd = LteLastRoadSceneQuery_FDD.GetInstance();
        protected virtual float? GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return queryfdd.GetRSRP(tp);
            }
            return query.GetRSRP(tp);
        }
        protected virtual float? GetSINR(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return queryfdd.GetSINR(tp);
            }
            return query.GetSINR(tp);
        }
        protected virtual float? GetNRSRP(TestPoint tp,int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return queryfdd.GetNRSRP(tp, index);
            }
            return query.GetNRSRP(tp, index);
        }
        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            if (TestPoints.Count > 0)
            {
                bgResult.FileID = TestPoints[0].FileID;
                bgResult.FileName = TestPoints[0].FileName;
                bgResult.LongitudeStart = TestPoints[0].Longitude;
                bgResult.LatitudeStart = TestPoints[0].Latitude;
                bgResult.LongitudeEnd = TestPoints[TestPoints.Count - 1].Longitude;
                bgResult.LatitudeEnd = TestPoints[TestPoints.Count - 1].Latitude;
                bgResult.ISTime = (int)(JavaDate.GetMilliseconds(TestPoints[0].DateTime) / 1000);
                bgResult.IETime = (int)(JavaDate.GetMilliseconds(TestPoints[TestPoints.Count - 1].DateTime) / 1000);
            }
            bgResult.LongitudeMid = LngMid;
            bgResult.LatitudeMid = LatMid;
            bgResult.DistanceLast = Distance;
            bgResult.SampleCount = TestPoints.Count;
            bgResult.RxLevMean = RsrpAvg;
            bgResult.RxLevMin = RsrpMin;
            bgResult.RxLevMax = RsrpMax;
            bgResult.RxQualMean = SinrAvg == null ? 255 : (float)SinrAvg;
            bgResult.RxQualMin = SinrMin == null ? 255 : (float)SinrMin;
            bgResult.RxQualMax = SinrMax == null ? -255 : (float)SinrMax;
            bgResult.RoadDesc = RoadDesc;
            bgResult.CellIDDesc = CellNames;
            bgResult.StrDesc = SceneName;
            return bgResult;
        }
    }

    public class RoadSceneItemMulti : RoadSceneBase
    {
        public RoadSceneItemMulti()
            : base()
        {
        }
        public RoadSceneItemMulti(RoadSceneItemMulti srcItem)
            : this()
        {
            Sn = srcItem.Sn;
            this.Distance = srcItem.Distance;
            this.RoadDesc = srcItem.RoadDesc;
            this.RsrpAvg = srcItem.RsrpAvg;
            this.RsrpMax = srcItem.RsrpMax;
            this.RsrpMin = srcItem.RsrpMin;
            this.SinrAvg = srcItem.SinrAvg;
            this.SinrMax = srcItem.SinrMax;
            this.SinrMin = srcItem.SinrMin;
            this.TestPoints.AddRange(srcItem.TestPoints);
        }

        public void SetSpecifiedCells(List<LTECell> cells)
        {
            this.Cells = cells;
        }

        public void AddTestPoint(TestPoint tp, List<LTECell> cells)
        {
            AddTestPoint(tp);
            foreach (LTECell cell in cells)
            {
                if (!Cells.Contains(cell))
                {
                    Cells.Add(cell);
                }
            }
        }

        public override bool MakeSummary(RoadSceneCondition cond)
        {
            if (Distance < cond.MultiDistance)
            {
                return false;
            }

            base.MakeSummary(cond);
            float rsrpTotal = 0;
            RsrpMin = float.MaxValue;
            RsrpMax = float.MinValue;
            int sinrNum = 0;
            float sinrTotal = 0;
            SinrMax = float.MinValue;
            SinrMin = float.MaxValue;
            foreach (TestPoint tp in TestPoints)
            {
                float rsrp = float.Parse(tp["LTESCAN_TopN_CELL_Specific_RSRP", 0].ToString());
                RsrpMin = Math.Min(rsrp, RsrpMin);
                RsrpMax = Math.Max(rsrp, RsrpMax);
                rsrpTotal += rsrp;

                float? sinr = (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR", 0];
                if (sinr >= -50 && sinr <= 50)
                {
                    sinrNum++;
                    SinrMax = Math.Max((float)sinr, (float)SinrMax);
                    SinrMin = Math.Min((float)sinr, (float)SinrMin);
                    sinrTotal += (float)sinr;
                }
            }
            RsrpAvg = (float)Math.Round(rsrpTotal / TestPoints.Count, 2);
            SinrAvg = (float)Math.Round(sinrTotal / sinrNum, 2);

            return true;
        }

    }

    public class RoadSceneWeakCover : RoadSceneBase
    {
        private class SvrCellInfo
        {
            public LTECell Cell;
            public int Number;
            public double DistanceTotal = 0;
            public double DistanceAvg
            {
                get
                {
                    return DistanceTotal / Number;
                }
            }
        }
        public override bool MakeSummary(RoadSceneCondition cond)
        {
            if (Distance<cond.WeakCvrDistance)
            {
                return false;
            }
            base.MakeSummary(cond);
            SceneName = "持续衰落";


            int rsrpNum = 0;
            float rsrpTotal = 0;
            RsrpMin = float.MaxValue;
            RsrpMax = float.MinValue;

            int sinrNum = 0;
            float sinrTotal = 0;
            SinrMax = float.MinValue;
            SinrMin = float.MaxValue;

            List<SvrCellInfo> svrCells = new List<SvrCellInfo>();
            foreach (TestPoint tp in TestPoints)
            {
                float? rsrp = GetRSRP(tp);
                if (-141 <= rsrp && rsrp <= 25)
                {//合法值
                    rsrpNum++;
                    rsrpTotal += (float)rsrp;
                    RsrpMin = Math.Min((float)rsrp, RsrpMin);
                    RsrpMax = Math.Max((float)rsrp, RsrpMax);
                }

                float? sinr = GetSINR(tp);
                if (-50 <= sinr && sinr <= 50)
                {
                    sinrNum++;
                    sinrTotal += (float)sinr;
                    SinrMax = Math.Max((float)sinr, (float)SinrMax);
                    SinrMin = Math.Min((float)sinr, (float)SinrMin);
                }
                addSvrCell(svrCells, tp);
            }
            RsrpAvg = (float)Math.Round(rsrpTotal / rsrpNum, 2);
            SinrAvg = (float)Math.Round(sinrTotal / sinrNum, 2);
            foreach (SvrCellInfo cell in svrCells)
            {
                if (cell.DistanceAvg<=cond.WeakCvrCellDis)
                {
                    Cells.Add(cell.Cell);
                }
                else
                {
                    return false;
                }
            }
            return Cells.Count > 0;
        }

        private void addSvrCell(List<SvrCellInfo> svrCells, TestPoint tp)
        {
            LTECell cell = tp.GetMainLTECell_TdOrFdd();
            if (cell != null)
            {
                double distance = tp.Distance2(cell.Longitude, cell.Latitude);
                SvrCellInfo svrCell = svrCells.Find(
                    delegate (SvrCellInfo x) { return x.Cell == cell; });
                if (svrCell == null)
                {
                    svrCell = new SvrCellInfo();
                    svrCell.Cell = cell;
                    svrCells.Add(svrCell);
                }
                svrCell.DistanceTotal += distance;
                svrCell.Number++;
            }
        }
    }

    public class RoadSceneFastFade : RoadSceneBase
    {

        private class CellPointInfo
        {
            public LTECell Cell;
            private readonly Dictionary<TestPoint, float> pointDic = new Dictionary<TestPoint, float>();
            public void AddPoint(TestPoint tp, float rsrp)
            {
                if (rsrpMin==float.MinValue)
                {
                    rsrpMin = rsrp;
                }
                if (rsrpMax==float.MaxValue)
                {
                    rsrpMax = rsrp;
                }
                rsrpMin = Math.Min(rsrp, rsrpMin);
                rsrpMax = Math.Max(rsrp, rsrpMax);
                pointDic.Add(tp, rsrp);
            }
            private float rsrpMin = float.MinValue;
            private float rsrpMax = float.MaxValue;
            public bool IsOverFloat(float floatValue, double distance)
            {
                if (rsrpMax - rsrpMin < floatValue)
                {
                    return false;
                }
                List<TestPoint> pts = new List<TestPoint>(pointDic.Keys);

                for (int i = 0; i < pts.Count; i++)
                {
                    float rsrpHost = pointDic[pts[i]];
                    for (int j = i + 1; i < pts.Count; i++)
                    {
                        float rsrpGuest = pointDic[pts[j]];
                        if (Math.Abs(rsrpHost - rsrpGuest) >= floatValue
                            && pts[i].Distance2(pts[j]) <= distance)
                        {
                            return true;
                        }
                    }
                }
                return false;
            }

        }

        public LTECell MainCell
        {
            get;
            private set;
        }
        public RoadSceneFastFade(LTECell cell)
        {
            MainCell = cell;
        }

        public override bool MakeSummary(RoadSceneCondition cond)
        {
            SceneName = "间断性快衰";
            base.MakeSummary(cond);
            int rsrpNum = 0;
            float rsrpTotal = 0;
            RsrpMin = float.MaxValue;
            RsrpMax = float.MinValue;

            int sinrNum = 0;
            float sinrTotal = 0;
            SinrMax = float.MinValue;
            SinrMin = float.MaxValue;

            List<CellPointInfo> nbCells = new List<CellPointInfo>();
            CellPointInfo mainCellInfo = new CellPointInfo();
            mainCellInfo.Cell = MainCell;
            foreach (TestPoint tp in TestPoints)
            {
                float? rsrp = GetRSRP(tp);
                seteRsrpInfo(cond, ref rsrpNum, ref rsrpTotal, mainCellInfo, tp, rsrp);

                setSinrInfo(ref sinrNum, ref sinrTotal, tp);

                for (int i = 0; i < 6; i++)
                {
                    rsrp = GetNRSRP(tp, i);
                    addNbCellInfo(cond, nbCells, tp, rsrp, i);
                }
            }
            RsrpAvg = (float)Math.Round(rsrpTotal / rsrpNum, 2);
            SinrAvg = (float)Math.Round(sinrTotal / sinrNum, 2);
            bool overFloat = false;
            overFloat = mainCellInfo.IsOverFloat(cond.FastFadeRsrpDiff, cond.FastFadeDistance);
            if (overFloat)
            {
                Cells.Add(MainCell);
            }
            foreach (CellPointInfo cellInfo in nbCells)
            {
                if (cellInfo.IsOverFloat(cond.FastFadeRsrpDiff, cond.FastFadeDistance))
                {
                    overFloat = true;
                    Cells.Add(cellInfo.Cell);
                }
            }
            return overFloat;
        }

        private void seteRsrpInfo(RoadSceneCondition cond, ref int rsrpNum, ref float rsrpTotal, CellPointInfo mainCellInfo, TestPoint tp, float? rsrp)
        {
            if (-141 <= rsrp && rsrp <= 25)
            {//合法值
                rsrpNum++;
                rsrpTotal += (float)rsrp;
                RsrpMin = Math.Min((float)rsrp, RsrpMin);
                RsrpMax = Math.Max((float)rsrp, RsrpMax);
                if (rsrp >= cond.FastFadeRsrp)
                {
                    mainCellInfo.AddPoint(tp, (float)rsrp);
                }
            }
        }

        private void setSinrInfo(ref int sinrNum, ref float sinrTotal, TestPoint tp)
        {
            float? sinr = GetSINR(tp);
            if (-50 <= sinr && sinr <= 50)
            {
                sinrNum++;
                sinrTotal += (float)sinr;
                SinrMax = Math.Max((float)sinr, (float)SinrMax);
                SinrMin = Math.Min((float)sinr, (float)SinrMin);
            }
        }

        private void addNbCellInfo(RoadSceneCondition cond, List<CellPointInfo> nbCells, TestPoint tp, float? rsrp, int i)
        {
            if (-141 <= rsrp && rsrp <= 25 && rsrp >= cond.FastFadeRsrp)
            {//合法值
                LTECell cell = tp.GetNBLTECell_TdOrFdd(i);
                if (cell != null)
                {
                    CellPointInfo info = nbCells.Find(delegate (CellPointInfo x) { return x.Cell == cell; });
                    if (info == null)
                    {
                        info = new CellPointInfo();
                        info.Cell = cell;
                        nbCells.Add(info);
                    }
                    info.AddPoint(tp, (float)rsrp);
                }
            }
        }
    }

    public class RoadSceneIntraHo : RoadSceneBase
    {
        public void AddCell(LTECell cell)
        {
            if (!Cells.Contains(cell))
            {
                Cells.Add(cell);
            }
        }

        public int HandoverNum
        {
            get {return Events.Count; }
        }

        public void AddTestPoints(List<TestPoint> tpSet)
        {
            foreach (TestPoint tp in tpSet)
            {
                AddTestPoint(tp);
            }
        }

        internal void AddHoEvent(Event e)
        {
            Events.Add(e);
        }

        public override bool MakeSummary(RoadSceneCondition cond)
        {
            SceneName = "路面塔";
            base.MakeSummary(cond);
            int rsrpNum = 0;
            float rsrpTotal = 0;
            RsrpMin = float.MaxValue;
            RsrpMax = float.MinValue;

            int sinrNum = 0;
            float sinrTotal = 0;
            SinrMax = float.MinValue;
            SinrMin = float.MaxValue;

            foreach (TestPoint tp in TestPoints)
            {
                float? rsrp = GetRSRP(tp);
                if (-141 <= rsrp && rsrp <= 25)
                {//合法值
                    rsrpNum++;
                    rsrpTotal += (float)rsrp;
                    RsrpMin = Math.Min((float)rsrp, RsrpMin);
                    RsrpMax = Math.Max((float)rsrp, RsrpMax);
                }

                float? sinr = GetSINR(tp);
                if (-50 <= sinr && sinr <= 50)
                {
                    sinrNum++;
                    sinrTotal += (float)sinr;
                    SinrMax = Math.Max((float)sinr, (float)SinrMax);
                    SinrMin = Math.Min((float)sinr, (float)SinrMin);
                }
            }
            RsrpAvg = (float)Math.Round(rsrpTotal / rsrpNum, 2);
            SinrAvg = (float)Math.Round(sinrTotal / sinrNum, 2);
            return true;
        }

    }

}
