﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func.CoverageCheck;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.ZTFunc.ZTNRLTECollaborativeAnaByGrid;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLTECollaborativeAnaGridLayer : LayerBase
    {
        public NRLTECollaborativeAnaGridLayer()
            : base("4/5G协同栅格图层")
        {

        }

        /// <summary>
        /// 本次选择的栅格
        /// </summary>
        public Result SelectedGrid { get; set; }

        public event EventHandler SelectedGridChanged;

        /// <summary>
        /// 需渲染的栅格数据合集
        /// </summary>
        public static List<SerialResult> SerialGridInfos { get; set; } = new List<SerialResult>();
        public static Dictionary<ZTNRLTECollaborativeAnaType, TextColorRange> ColorRange { get; set; } = new Dictionary<ZTNRLTECollaborativeAnaType, TextColorRange>()
        {
            { ZTNRLTECollaborativeAnaType.WeakCover4G5G, new TextColorRange(Color.Red, "覆盖同差") },
            { ZTNRLTECollaborativeAnaType.Better4G, new TextColorRange(Color.Yellow, "4G优于5G") },
            { ZTNRLTECollaborativeAnaType.Better5G, new TextColorRange(Color.Cyan, "5G优于4G") },
        };
        public static Color InvalidColor { get; set; } = Color.Black;
        public static Dictionary<TextColorRange, int> GridRangeCountDic { get; set; } 

        public void Clear()
        {
            GridRangeCountDic.Clear();
            SerialGridInfos.Clear();
        }

        /// <summary>
        /// 已选择栅格的边框画笔
        /// </summary>
        private readonly Pen penSelected = new Pen(Color.Red, 3);

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || SerialGridInfos == null || SerialGridInfos.Count <= 0)
            {
                return;
            }
            DbRect dRect;
            GisAdapter.FromDisplay(updateRect, out dRect);

            //选中栅格包含的频点,PCI合集
            List<Result> selectGridCellList = new List<Result>();
            //InitColorBySelectedSerials();
            foreach (var serialGridInfo in SerialGridInfos)
            {
                foreach (var grid in serialGridInfo.ResultList)
                {
                    drawGrid(grid, dRect, graphics);
                    if (SelectedGrid != null && grid.Token == SelectedGrid.Token)
                    {
                        selectGridCellList.Add(grid);
                    }
                }
            }
            drawSelGrid(dRect, graphics);
        }

        /// <summary>
        /// 绘制栅格
        /// </summary>
        /// <param name="grid"></param>
        /// <param name="dRect"></param>
        /// <param name="graphics"></param>
        private void drawGrid(Result grid, DbRect dRect, Graphics graphics)
        {
            if (grid.Grid.Within(dRect))
            {
                Color color = GetColor(grid);//grid.Grid.color;
                if (color != Color.Empty)
                {
                    DbPoint ltPoint = new DbPoint(grid.Grid.LTLng, grid.Grid.LTLat);
                    PointF pointLt;
                    GisAdapter.ToDisplay(ltPoint, out pointLt);
                    DbPoint brPoint = new DbPoint(grid.Grid.BRLng, grid.Grid.BRLat);
                    PointF pointBr;
                    GisAdapter.ToDisplay(brPoint, out pointBr);
                    Brush brush = new SolidBrush(color);

                    graphics.FillRectangle(brush, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
                }
            }
        }

        /// <summary>
        /// 绘制选中的栅格
        /// </summary>
        /// <param name="graphics"></param>
        private void drawSelGrid(DbRect dRect, Graphics graphics)
        {
            if (SelectedGrid == null)
            {
                return;
            }
            //绘制选中栅格
            drawGrid(SelectedGrid, dRect, graphics);
            //绘制选中栅格边框
            DbPoint ltPoint = new DbPoint(SelectedGrid.Grid.LTLng, SelectedGrid.Grid.LTLat);
            PointF pointLt;
            GisAdapter.ToDisplay(ltPoint, out pointLt);
            DbPoint brPoint = new DbPoint(SelectedGrid.Grid.BRLng, SelectedGrid.Grid.BRLat);
            PointF pointBr;
            GisAdapter.ToDisplay(brPoint, out pointBr);
            graphics.DrawRectangle(penSelected, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
        }
     
        /// <summary>
        /// 获取栅格颜色
        /// </summary>
        /// <param name="grid"></param>
        /// <returns></returns>
        public Color GetColor(Result grid)
        {
            TextColorRange range;
            if (ColorRange.TryGetValue(grid.Type, out range))
            {
                if (!range.Visible)
                {
                    return Color.Empty;
                }
                return range.color;
            }
            return InvalidColor;
        }

        public void DealGridRangeCount()
        {
            GridRangeCountDic = new Dictionary<TextColorRange, int>();
            foreach (var serialGridInfo in SerialGridInfos)
            {
                foreach (var grid in serialGridInfo.ResultList)
                {
                    TextColorRange range;
                    if (ColorRange.TryGetValue(grid.Type, out range))
                    {
                        int count;
                        if (!GridRangeCountDic.TryGetValue(range, out count))
                        {
                            GridRangeCountDic.Add(range, count);
                        }
                        GridRangeCountDic[range]++;
                    }
                }
            }
        }

        #region Select
        public override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            Select(((Func.MapForm.MapEventArgs)e).MapOp2);
        }

        /// <summary>
        /// 用于判断是哪个结果窗体的点选
        /// </summary>
        public object CurType { get; set; }
        public void Select(MapOperation2 mop2)
        {
            if (!IsVisible || SerialGridInfos == null || SerialGridInfos.Count <= 0)
            {
                return;
            }
            SelectedGrid = null;

            //循环所有栅格,如果点击的坐标在某个栅格的范围内则记录为所选点
            getSelectedGrid(mop2);
            SelectedGridChanged?.Invoke(CurType, EventArgs.Empty);
        }

        private void getSelectedGrid(MapOperation2 mop2)
        {
            foreach (var serialGridInfo in SerialGridInfos)
            {
                foreach (var gridInfo in serialGridInfo.ResultList)
                {
                    var grid = gridInfo.Grid;
                    DbRect dRect = new DbRect(grid.LTLng, grid.LTLat, grid.BRLng, grid.BRLat);
                    if (mop2.CheckCenterInDRect(dRect))
                    {
                        SelectedGrid = gridInfo;
                        break;
                    }
                }
            }
        }
        #endregion
    }
}
