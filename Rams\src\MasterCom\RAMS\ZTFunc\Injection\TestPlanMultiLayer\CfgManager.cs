﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.Injection.TestPlanMultiLayer
{
    class CfgManager
    {
        readonly string cfgPath = string.Format(Application.StartupPath + "/config/ztfunc/TestPlanMultiInjection.xml");
        private static CfgManager instance = null;
        private CfgManager()
        {
            AreaTypeOptions = new List<AreaTypeOption>();
            LoadCfg();
        }
        public static CfgManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new CfgManager();
                }
                return instance;
            }
        }

        public List<AreaTypeOption> AreaTypeOptions
        {
            get;
            set;
        }

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                List<object> list = new List<object>();
                paramDic["AreaOptions"] = list;
                foreach (AreaTypeOption item in AreaTypeOptions)
                {
                    list.Add(item.Param);
                }
                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.AreaTypeOptions.Clear();
                foreach (Dictionary<string, object> dic in (List<object>)value["AreaOptions"])
                {
                    AreaTypeOption areaOp = new AreaTypeOption();
                    areaOp.Param = dic;
                    this.AreaTypeOptions.Add(areaOp);
                }
            }
        }

        public void LoadCfg()
        {
            if (File.Exists(cfgPath))
            {
                XmlConfigFile configFile = new XmlConfigFile(cfgPath);
                CfgParam = configFile.GetItemValue("ConditionCfg", "Condition") as Dictionary<string, object>;
            }
        }

        public void Save()
        {
            XmlConfigFile xmlFile = new XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("ConditionCfg");
            xmlFile.AddItem(cfgE, "Condition", this.CfgParam);
            xmlFile.Save(cfgPath);
        }
    }
}
