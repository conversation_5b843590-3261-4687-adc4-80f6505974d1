﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Func.OutServiceInfo;

namespace MasterCom.RAMS.Model
{
    public class LTECell : Snapshot<LTECell>, ICell, IComparable<LTECell>
    {
        public static LTEBandType GetBandTypeByEarfcn(int earfcn)
        {
            LTEBandType band = LTEBandType.Undefined;
            if (36200 <= earfcn && earfcn <= 36349)
            {
                band = LTEBandType.A;
            }
            else if (37750 <= earfcn && earfcn <= 38249)
            {
                band = LTEBandType.D;
            }
            else if (38250 <= earfcn && earfcn <= 38649)
            {
                band = LTEBandType.F;
            }
            else if (38650 <= earfcn && earfcn <= 39649)
            {
                band = LTEBandType.E;
            }
            return band;
        }

        /// <summary>
        /// 按集团划分规则
        /// </summary>
        public static LTEBandTypeJT GetBandTypeByJT(int earfcn)
        {
            LTEBandTypeJT band = LTEBandTypeJT.Undefined;
            if (36200 <= earfcn && earfcn <= 36349)
            {
                band = LTEBandTypeJT.A;
            }
            else if (37750 <= earfcn && earfcn <= 38249)
            {
                if (earfcn < 38000)
                {
                    band = LTEBandTypeJT.D1;
                }
                else if (earfcn < 38200)
                {
                    band = LTEBandTypeJT.D2;
                }
                else
                {
                    band = LTEBandTypeJT.D3;
                }
            }
            else if (38250 <= earfcn && earfcn <= 38649)
            {
                band = LTEBandTypeJT.F;
            }
            else if (38650 <= earfcn && earfcn <= 39649)
            {
                band = LTEBandTypeJT.E;
            }
            else if (40890 <= earfcn && earfcn < 41040)
            {
                band = LTEBandTypeJT.D3;
            }
            return band;
        }

        public static LTEBandType GetTddOrFddByEarfcn(int earfcn)
        {
            LTEBandType band = LTEBandType.Undefined;
            if (36200 <= earfcn && earfcn <= 36349
                || 37750 <= earfcn && earfcn <= 38249
                || 38250 <= earfcn && earfcn <= 38649
                || 38650 <= earfcn && earfcn <= 39649
                || 40890 <= earfcn && earfcn <= 41039
                || earfcn == 39946
                || earfcn == 40144
                || earfcn == 40738
                || earfcn == 40540
                || earfcn == 21734
                || earfcn == 21736
                || earfcn == 21738
                )
            {
                band = LTEBandType.TDD;
            }
            else if (1200 <= earfcn && earfcn <= 1400
                || 3657 <= earfcn && earfcn <= 3707
                || 19200 <= earfcn && earfcn <= 19400
                || 21657 <= earfcn && earfcn <= 21707
                || earfcn == 3525
                || earfcn == 3532
                || earfcn == 3576
                || earfcn == 3600
                || earfcn == 3715)
            {
                band = LTEBandType.FDD;
            }
            return band;
        }

        /// <summary>
        ///北京要求的新加按频点统计 ADEF
        /// </summary>
        /// <param name="earfcn"></param>
        /// <returns></returns>
        public static LTEBandType GetBandTypeByEarfcn_BJ(int earfcn)
        {
            LTEBandType band = LTEBandType.Undefined;
            if (36200 <= earfcn && earfcn <= 36349)
            {
                band = LTEBandType.A;
            }
            else if (37750 <= earfcn && earfcn <= 38249)
            {
                band = getBandD(earfcn);
            }
            else if (38250 <= earfcn && earfcn <= 38649)
            {
                band = getBandF(earfcn);
            }
            else if (38650 <= earfcn && earfcn <= 39649)
            {
                band = LTEBandType.E;
            }
            return band;
        }

        private static LTEBandType getBandD(int earfcn)
        {
            LTEBandType band = LTEBandType.D;
            if (earfcn == 37900)
            {
                band = LTEBandType.D_37900;
            }
            else if (earfcn == 37902)
            {
                band = LTEBandType.D_37902;
            }
            else if (earfcn == 38098)
            {
                band = LTEBandType.D_38098;
            }
            else if (earfcn == 38100)
            {
                band = LTEBandType.D_38100;
            }

            return band;
        }

        private static LTEBandType getBandF(int earfcn)
        {
            LTEBandType band = LTEBandType.F;
            if (earfcn == 38350)
            {
                band = LTEBandType.F_38350;
            }
            else if (earfcn == 38400)
            {
                band = LTEBandType.F_38400;
            }
            else if (earfcn == 38544)
            {
                band = LTEBandType.F_38544;
            }

            return band;
        }

        public override string ToString()
        {
            return Name;
        }
        public LTECell()
        {
            Value = this;
        }

        private double? lengthRatioByFreq = null;
        public double LengthRatioByFreq
        {
            get
            {
                if (lengthRatioByFreq == null)
                {
                    switch (GetBandTypeByEarfcn(EARFCN))
                    {
                        case LTEBandType.Undefined:
                            lengthRatioByFreq = 1;
                            break;
                        case LTEBandType.A:
                            lengthRatioByFreq = 1;
                            break;
                        case LTEBandType.D:
                        case LTEBandType.D_37900:
                        case LTEBandType.D_38100:
                        case LTEBandType.D_38098:
                            lengthRatioByFreq = 1.2;
                            break;
                        case LTEBandType.E:
                            lengthRatioByFreq = 1.4;
                            break;
                        case LTEBandType.F:
                        case LTEBandType.F_38350:
                        case LTEBandType.F_38400:
                        case LTEBandType.F_38544:
                            lengthRatioByFreq = 1.6;
                            break;
                        default:
                            lengthRatioByFreq = 1;
                            break;
                    }
                }
                return (double)lengthRatioByFreq;
            }
        }

        public double EndPointLongitude
        {
            get
            {
                if (this.Type == LTEBTSType.Indoor) //室内
                {
                    return Longitude;
                }
                else
                {
                    double offset = Math.Cos(-(Direction - 90) * Math.PI / 180) * 0.000024
                            * MasterCom.RAMS.Func.MapLTECellLayer.CellDefaultDisplayLength
                            * MasterCom.RAMS.Func.MapLTECellLayer.ShapeLengthScale
                            * LengthRatioByFreq;
                    return Longitude + offset;
                }
            }
        }

        public double EndPointLatitude
        {
            get
            {
                if (this.Type == LTEBTSType.Indoor)//室内
                {
                    return Latitude;
                }
                else
                {
                    double offset = Math.Sin(-(Direction - 90) * Math.PI / 180) * 0.000024
                            * MasterCom.RAMS.Func.MapLTECellLayer.CellDefaultDisplayLength
                            * MasterCom.RAMS.Func.MapLTECellLayer.ShapeLengthScale
                            * LengthRatioByFreq;
                    return Latitude + offset;
                }
            }
        }
        
        public LTEPCI_Mod3 PCI_MOD3 { get; set; } = LTEPCI_Mod3.Undefined;
        public LTEPCI_Mod6 PCI_MOD6 { get; set; } = LTEPCI_Mod6.Undefined;
        public MainOrNBCell CellType { get; set; } = MainOrNBCell.Other;

        //由于存在拉远站情况,添加配置,小区经纬度可以按天线经纬度来算
        public double Longitude
        {
            get 
            {
                if (MainModel.GetInstance().SystemConfigInfo.IsBtsLngLat)
                {
                    return BelongBTS.Longitude;
                }
                else
                {
                    return Antennas.Count > 0 ? Antennas[0].Longitude : 0;
                }
            }
        }

        public double Latitude
        {
            get
            {
                if (MainModel.GetInstance().SystemConfigInfo.IsBtsLngLat)
                {
                    return BelongBTS.Latitude;
                }
                else
                {
                    return Antennas.Count > 0 ? Antennas[0].Latitude : 0;
                }
            }
        }

        //用于同频同PCI 导入Excel与工参比较使用
        public double LongitudeTemp { get; set; }
        public double LatitudeTemp { get; set; }
        public short DirectionTemp { get; set; }

        public string BTSName
        {
            get { return BelongBTS.Name; }
        }

        public LTEBTSType Type
        {
            get { return BelongBTS.Type; }
        }

        public string BTSDescription
        {
            get { return BelongBTS.Description; }
        }

        /// <summary>
        /// 由于工参工具没有填写对应的cellid，该值暂改为根据ECI逆推出来。
        /// 即 ECI/256*10+SectorID = EnodeBID * 10 + sectorID
        /// </summary>
        public int SCellID { get; set; }

        public int CellID { get; set; }

        /// <summary>
        /// 取对应天线与正北方向，顺时针的夹角
        /// </summary>
        public short Direction
        {
            get { return Antennas.Count > 0 ? Antennas[0].Direction : (short)0; }
        }

        public short Downward
        {
            get { return Antennas.Count > 0 ? Antennas[0].Downward : (short)0; }
        }

        public int Altitude
        {
            get { return Antennas.Count > 0 ? Antennas[0].Altitude : (short)0; }
        }

        public int PCI { get; set; }

        public string NewPCI { get; set; } = "";

        /// <summary>
        /// 频点
        /// </summary>
        public int EARFCN { get; set; }

        public LTEBandType BandType
        {
            get
            {
                return LTECell.GetBandTypeByEarfcn(EARFCN);
            }
        }

        public string Code { get; set; }

        public string Name { get; set; }

        public int TAC { get; set; }

        public int ECI { get; set; }

        public string DESC { get; set; }

        public List<LTEAntenna> Antennas
        {
            get { return antennas; }
        }
        public List<LTECell> NeighbourCells
        {
            get { return neighbourCells; }
        }

        public List<Cell> NeighbourGSMCells
        {
            get { return neighbourGSMCells; }
        }

        public List<TDCell> NeighbourTDCells
        {
            get { return neighbourTDCells; }
        }
        public List<WCell> NeighbourWCells
        {
            get { return neighbourWCells; }
        }
        public LTEBTS BelongBTS { get; set; }

        public ISite Site
        {
            get { return BelongBTS; }
        }

        public void AddNeighbourCell(LTECell cell)
        {
            if (!neighbourCells.Contains(cell))
            {
                neighbourCells.Add(cell);
            }
        }

        public void AddNeighbourCell(Cell cell)
        {
            if (!neighbourGSMCells.Contains(cell))
            {
                neighbourGSMCells.Add(cell);
            }
        }

        public void AddNeighbourCell(TDCell cell)
        {
            if (!neighbourTDCells.Contains(cell))
            {
                neighbourTDCells.Add(cell);
            }
        }
        public void AddNeighbourCell(WCell cell)
        {
            if (!neighbourWCells.Contains(cell))
            {
                neighbourWCells.Add(cell);
            }
        }
        public bool IsCo_Freq(int earfcn)
        {
            return this.EARFCN == earfcn;
        }
        
        public bool IsCo_FreqList(int earfcn)
        {
            string[] freqArry = freqs.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (string freq in freqArry)
            {
                if (int.Parse(freq) == earfcn)
                    return true;
            }
            return false;
        }

        public bool IsInGsmFreq(int bcch)
        {
            string[] freqArry = GsmFreqListStr.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (string freq in freqArry)
            {
                if (int.Parse(freq) == bcch)
                    return true;
            }
            return false;
        }

        public string DetailInfo
        {
            get
            {
                StringBuilder info = new StringBuilder();
                info.Append(this.BelongBTS.DetailInfo);
                info.Append("\r\nName:").Append(Name);
                info.Append("\r\nCode:").Append(Code);
                info.Append("\r\nTAC:").Append(TAC);
                info.Append("\r\nECI:").Append(ECI);
                info.Append("\r\n扇区号:").Append(SectorID.ToString());
                info.Append("\r\n主频:").Append(EARFCN);
                info.Append("\r\n扰码:").Append(PCI);
                info.Append("\r\n站高:").Append(Altitude);
                info.Append("\r\n下倾角:").Append(Downward);
                info.Append("\r\n方向角:").Append(Direction);
                info.Append("\r\n描述:").Append(DESC);
                info.Append("\r\n");
                return info.ToString();
            }
        }

        public bool Within(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }

        public void Fill(MasterCom.RAMS.Net.Content content, CellManager cellManager)
        {
            base.Fill(content.GetParamInt(), content.GetParamInt(), content.GetParamInt());
            Code = content.GetParamString();
            Name = content.GetParamString();
            int btsID = content.GetParamInt();
            foreach (LTEBTS bts in cellManager.GetLTEBTSs(btsID, ValidPeriod))
            {
                if (bts.ValidPeriod.Contains(ValidPeriod.BeginTime) || ValidPeriod.IsIntersect(bts.ValidPeriod))
                {
                    bts.Cells.Add(this);
                    BelongBTS = bts;
                }
            }
            TAC = content.GetParamInt();
            ECI = content.GetParamInt();
            CellID = content.GetParamInt();
            SectorID = content.GetParamInt();
            PCI = content.GetParamInt();
            EARFCN = content.GetParamInt();
            freqs = content.GetParamString();
            DESC = content.GetParamString();
            if (content.BCanGetString())
            {
                gsmfreqs = content.GetParamString();
            }
            if (content.BCanGetString())
            {
                tdfreqs = content.GetParamString();
            }
            SCellID = (ECI / 256) * 10 + SectorID;
        }

        public double GetDistance(double x, double y)
        {
            if (this.Antennas.Count == 0)
            {
                return MathFuncs.GetDistance(Longitude, Latitude, x, y);
            } 
            else
            {
                double dDistance = double.MaxValue;
                foreach (LTEAntenna antenna in this.Antennas)
                {
                    double dDistanceTmp = MathFuncs.GetDistance(antenna.Longitude, antenna.Latitude, x, y);
                    if (dDistanceTmp < dDistance)
                    {
                        dDistance = dDistanceTmp;
                    }
                }
                return dDistance;
            }
        }

        /// <summary>
        /// 扇区号ＩＤ
        /// </summary>
        public int SectorID { get; set; }
        private string freqs;
        public string FreqListStr
        {
            get { return freqs; }
        }
        private string gsmfreqs = "";//LTE-GSM频点
        public string GsmFreqListStr
        {
            get { return gsmfreqs; }
        }
        private string tdfreqs = "";//LTE-TD频点
        public string TDFreqListStr
        {
            get { return tdfreqs; }
        }
        
        private readonly List<LTEAntenna> antennas = new List<LTEAntenna>();
        private readonly List<LTECell> neighbourCells = new List<LTECell>();
        private readonly List<Cell> neighbourGSMCells = new List<Cell>();
        private readonly List<TDCell> neighbourTDCells = new List<TDCell>();
        private readonly List<WCell> neighbourWCells = new List<WCell>();

        public System.Drawing.Color ServerCellColor
        {
            get;
            set;
        }

        public LTEAntennaDirectionType DirectionType
        {
            get { return this.antennas.Count > 0 ? this.antennas[0].DirectionType : LTEAntennaDirectionType.Beam; }
        }

        #region ICell 成员
        public string Token
        {
            get { return string.Format("{0}_{1}", TAC, ECI); }
        }
        #endregion

        #region IComparable<LTECell> 成员

        public int CompareTo(LTECell other)
        {
            if (other == null)
            {
                return 1;
            }
            return this.Name.CompareTo(other.Name);
        }

        #endregion
    }

    public enum LTEPCI_Mod3
    {
        Value0 = 0,
        Value1 = 1,
        Value2 = 2,

        Undefined = 100,
    }

    public enum LTEPCI_Mod6
    {
        Value0 = 0,
        Value1 = 1,
        Value2 = 2,
        Value3 = 3,
        Value4 = 4,
        Value5 = 5,

        Undefined = 100,
    }
    public enum LTEBandType
    {
        Undefined,
        A,
        D,
        D_37900,//D1
        D_37902,
        D_38098,
        D_38100,//D2
        E,
        F,
        F_38350,
        F_38400,
        F_38544,
        TDD,
        FDD
    }

    /// <summary>
    /// 按集团定义
    /// </summary>
    public enum LTEBandTypeJT
    {
        Undefined,
        A,
        D1,
        D2,
        D3,
        E,
        F,
        D
    }

    /// <summary>
    /// 用于邻区核查中控制主小区与漏配小区的颜色
    /// </summary>
    public enum MainOrNBCell
    {
        MainCell,
        NBCell,
        Other,
    }
}
