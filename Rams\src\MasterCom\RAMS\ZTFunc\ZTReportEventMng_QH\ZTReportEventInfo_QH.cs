﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTReportEventInfo_QH:Event
    {
        public string NetType { get; set; }
        public string AreaName { get; set; }
        public string TestDate { get; set; }
        public string TestTime { get; set; }
        public string CellName { get; set; }
        public new int LAC { get; set; }
        public new int CI { get; set; }
        public string Remark { get; set; }
        public string Cause { get; set; }
        public string CauseDetail { get; set; }
        public string Method { get; set; }
        public string Solution { get; set; }
        public bool IsRectify { get; set; }
        public bool IsReTest { get; set; }
        public string OptEffect { get; set; }
        public string DwUserName { get; set; }
        public string DwTime { get; set; }
        public string PreTypeDesc { get; set; }
        public string ReasonDesc { get; set; }
        public string RoadName { get; set; }
        //审核人
        public string Auditor { get; set; }
        //审核时间
        public int TimeAudit { get; set; }
        public string SavaPath { get; set; }
        public int SeqID { get; set; }
        public int EventTimeSec { get; set; }
        public int EventTimeMs { get; set; }
        public int Bms { get; set; }
        public bool IsKeyProb { get; set; } = false;
        public bool IsDone { get; set; } = false;
        public string EventType { get; set; }
        public string EventName { get; set; }
        public string DealLine { get; set; }

        public ZTReportEventInfo_QH()
        {
        }

        public bool Fill(MasterCom.RAMS.Net.Content content , MainModel mainModel, QueryEvent queryEvent)
        {
            this.FileInfo = new Model.FileInfo();
            FileInfo.ID = content.GetParamInt();
            FileInfo.ProjectID = content.GetParamInt();
            SeqID = content.GetParamInt();
            EventTimeSec = content.GetParamInt();
            EventTimeMs = content.GetParamInt();
            TestDate = JavaDate.GetDateTimeFromMilliseconds(EventTimeSec*1000L).ToShortDateString();
            TestTime = JavaDate.GetDateTimeFromMilliseconds(EventTimeSec*1000L).ToString("HH:mm:ss") + " " + EventTimeMs.ToString();
            DealLine = getDeadLine(TestDate);
            Bms = content.GetParamInt();
            ID = content.GetParamInt(); //eventId
            EventName = EventInfo == null? null : EventInfo.Name;
            EventType = EventInfo == null? null : getEventType(EventInfo.ID);
            Longitude = Convert.ToDouble(content.GetParamInt())/10000000;
            Latitude = Convert.ToDouble(content.GetParamInt())/10000000;
            LAC = content.GetParamInt();
            CI = content.GetParamInt();
            PreTypeDesc = content.GetParamString();
            ReasonDesc = content.GetParamString();
            CellName = content.GetParamString();

            AreaName = content.GetParamString(); 

            RoadName = content.GetParamString();
            FileInfo.Name = content.GetParamString();
            FileInfo.ServiceType = content.GetParamInt();
            FileInfo.SampleTbName = content.GetParamString();
            Remark = content.GetParamString();
            NetType = content.GetParamString();
            Cause = content.GetParamString();
            CauseDetail = content.GetParamString();
            Method = content.GetParamString();
            Solution = content.GetParamString();
            IsRectify = content.GetParamInt() != 0;
            IsReTest = content.GetParamInt() != 0;
            OptEffect = content.GetParamString();
            DwUserName = content.GetParamString();
            DwTime = content.GetParamString();
            IsKeyProb = content.GetParamInt() == 1;
            if (queryEvent == QueryEvent.QueryArchived)
            {
                IsDone = content.GetParamInt() == 1;
            }
            FileInfo.LogTable = "tb_log_file_" + JavaDate.GetDateTimeFromMilliseconds(EventTimeSec * 1000L).ToString("yyyy_MM");
            return true;
        }

        private static string getEventType(int eventID)
        {
            string eventType = string.Empty;

            switch(eventID)
            {
                case 6:
                case 907:
                    eventType = "掉话"; // "GSM主叫掉话"
                    break;
                case 7:
                case 908:
                    eventType = "掉话"; // "GSM被叫掉话"
                    break;
                case 8:
                case 10:
                case 82:
                case 88:
                    eventType = "未接通"; //"GSM主叫接入失败"
                    break;

                case 106:
                case 112:
                case 199:
                    eventType = "掉话"; // "TD主叫掉话"
                    break;
                case 118:
                case 124:
                case 200:
                    eventType = "掉话"; // "TD被叫掉话"
                    break;
                case 105:
                case 111:
                case 189:
                case 201:
                    eventType = "未接通"; //"TD主叫接入失败"
                    break;
                case 2003:
                    eventType = "重叠覆盖";//LTE扫频高重叠覆盖道路
                    break;
                default:
                    break;
            }
            return eventType;
        }


        /// <summary>
        /// 获取事件的最迟反馈时间，时间为测试时间延后3天的17点
        /// </summary>
        /// <param name="eventTimeSec"></param>
        /// <returns></returns>
        private static string getDeadLine(string testDate)
        {
            DateTime testData = DateTime.Parse(testDate).Date;
            int restWorkDay = DayOfWeek.Friday - testData.DayOfWeek;
            int days2Add = 0;
            if(restWorkDay < 0)
            {
                days2Add = 4;
            }
            else if(restWorkDay >= 3)
            {
                days2Add = 3;
            }
            else
            {
                days2Add = 5;
            }
            return testData.AddDays(days2Add).AddHours(17).ToString("yyyy/MM/dd HH:mm:ss");
        }
    }
}