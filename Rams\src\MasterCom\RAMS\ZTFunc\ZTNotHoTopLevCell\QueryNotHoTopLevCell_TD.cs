﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTNotHoTopLevCell;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryNotHoTopLevCell_TD : QueryNotHoTopLevCell
    {
        private static QueryNotHoTopLevCell_TD instance = null;
        public new static QueryNotHoTopLevCell_TD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QueryNotHoTopLevCell_TD();
                    }
                }
            }
            return instance;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13050, this.Name);
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                for (int i = 0; i < file.DTDatas.Count; i++)
                {
                    Event evt = file.DTDatas[i] as Event;
                    if (evt != null && (evt.ID == 142
                        || evt.ID == 145 || evt.ID == 148
                        || evt.ID == 151 || evt.ID == 232)
                        && isDataInRegion(evt.Longitude, evt.Latitude))
                    {
                        TestPoint preTp = null;
                        for (int j = i - 1; j >= 0; j--)
                        {//获取切换前采样点
                            TestPoint tp = file.DTDatas[j] as TestPoint;
                            if (tp == null || tp.Time > evt.Time)
                            {
                                continue;
                            }
                            preTp = tp;
                            break;
                        }
                        if (preTp == null)
                        {
                            continue;
                        }

                        TestPoint sufTp = null;
                        for (int j = i + 1; j < file.DTDatas.Count; j++)
                        {//获取切换后采样点
                            TestPoint tp = file.DTDatas[j] as TestPoint;
                            if (tp == null
                                || tp.Time < evt.Time)
                            {
                                continue;
                            }
                            sufTp = tp;
                            break;
                        }
                        if (sufTp == null)
                        {
                            continue;
                        }

                        ICell orgCell = preTp.GetMainCell();
                        float? orgLev = null;
                        if (preTp[MainModel.TD_GSM_SCell_LAC] != null
                                && preTp[MainModel.TD_GSM_SCell_CI] != null)
                        {
                            orgLev = (float?)(int?)preTp["TD_GSM_RxlevSub"];
                        }
                        else
                        {
                            orgLev = (float?)preTp["TD_PCCPCH_RSCP"];
                        }
                        if (orgCell == null)
                        {
                            continue;
                        }

                        ICell tarCell = sufTp.GetMainCell();
                        float? tarLev;
                        bool isHo2TD = true;
                        if (sufTp[MainModel.TD_GSM_SCell_LAC] != null
                                && sufTp[MainModel.TD_GSM_SCell_CI] != null)
                        {
                            tarLev = (float?)(int?)sufTp["TD_GSM_RxlevSub"];
                            isHo2TD = false;
                        }
                        else
                        {
                            tarLev = (float?)sufTp["TD_PCCPCH_RSCP"];
                        }
                        if (tarCell == null || tarCell.Name == orgCell.Name)
                        {
                            continue;
                        }
                        float topLev = float.MinValue;
                        int topIdx = -1;
                        for (int x = 0; x < 10; x++)
                        {
                            float? nRxLev = null;
                            if (isHo2TD)
                            {
                                nRxLev = (float?)(int?)preTp["TD_NCell_PCCPCH_RSCP", x];
                            }
                            else
                            {
                                nRxLev = (float?)(short?)preTp["TD_GSM_NCell_Rxlev", x];
                            }
                            if (nRxLev == null)
                            {
                                break;
                            }
                            if (nRxLev > topLev)
                            {
                                topLev = (float)nRxLev;
                                topIdx = x;
                            }
                        }
                        if (topIdx == -1)
                        {
                            continue;
                        }
                        ICell topNCell = null;
                        if (isHo2TD)
                        {
                            topNCell = preTp.GetNBCell_TD_TDCell(topIdx);
                        }
                        else
                        {
                            topNCell = preTp.GetNBCell_TD_GSMCell(topIdx);
                        }
                        if (topNCell != null && topNCell.Name != tarCell.Name)
                        {
                            NotHoTopLevCellInfo info = new NotHoTopLevCellInfo(orgCell, (float)orgLev
                                , tarCell, (float)tarLev, topNCell, topLev);
                            info.HoSuccessEvt = evt;
                            info.TestPoints.Add(preTp);
                            info.TestPoints.Add(sufTp);
                            resultSet.Add(info);
                        }
                    }
                }
            }
        }
    }
}
