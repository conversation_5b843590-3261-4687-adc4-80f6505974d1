﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRVoiceAnaByFreqBandResult : VoiceAnaByFreqBandResult
    {
        public NRVoiceAnaByFreqBandResult(string freqBand)
            : base(freqBand)
        {

        }

        protected override int? getPCI(TestPoint tp)
        {
            int? nrPci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            if (nrPci != null)
            {
                return nrPci;
            }

            int? ltePci = (int?)NRTpHelper.NrLteTpManager.GetPCI(tp);
            
            return ltePci;
        }

        protected override float? getMOSParamName(TestPoint tp, ref string mosParamName)
        {
            float? mos = NRTpHelper.NrTpManager.GetMosInfo(tp, ref mosParamName);
            return mos;
        }
    }

    public class NRVoiceAnaByFreqBandFileResult
    { 
        public string FileName { get; set; }
        public List<NRVoiceAnaByFreqBandResult> ResList { get; set; } = new List<NRVoiceAnaByFreqBandResult>();
    }
}
