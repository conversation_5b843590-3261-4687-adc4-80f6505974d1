﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NoGisBatExpRptSetFileNameForm : MasterCom.RAMS.Func.BaseDialog
    {
        public NoGisBatExpRptSetFileNameForm(string fileName)
        {
            InitializeComponent();
            tBoxFileName.Text = fileName;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            tBoxFileName.Text += "(" + comBoxAdd.Text + ")";
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            tBoxFileName.Text = string.Empty;
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            string fileName = tBoxFileName.Text;
            if (fileName.IndexOf("(区域名)") < 0 || fileName.IndexOf("(报表名)") < 0)
            {
                MessageBox.Show("必须添加区域名和报表名两个字段!");
            }
            else
            {
                MasterCom.RAMS.Stat.NoGisBatExpRptForm.fileNameDIY = fileName;
                this.DialogResult = DialogResult.OK;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnAddTime_Click(object sender, EventArgs e)
        {
            tBoxFileName.Text += "(:" + cBoxTime.Text + ":)";
        }
    }
}
