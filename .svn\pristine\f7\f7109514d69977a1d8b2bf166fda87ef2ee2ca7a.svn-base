﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Net;
using DTParameter = MasterCom.RAMS.Model.DTParameter;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellCoverLapByRegion_TD : ZTCellCoverLapByRegion
    {
        private static ZTCellCoverLapByRegion_TD intance = null;
        public new static ZTCellCoverLapByRegion_TD GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTCellCoverLapByRegion_TD();
                    }
                }
            }
            return intance;
        }

        protected ZTCellCoverLapByRegion_TD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TDSCDMA_VOICE);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "过覆盖分析_TD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13003, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup leakGroup = new DIYSampleGroup();
            leakGroup.ThemeName = "---";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter(MainModel.TD_SCell_LAC);
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter(MainModel.TD_SCell_CI);
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter(MainModel.TD_SCell_UARFCN);
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter(MainModel.TD_SCell_CPI);
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TD_PCCPCH_RSCP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }

            return leakGroup;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                //进行过覆盖算法运算
                if (inRegion && tp is TDTestPointDetail)
                {
                    float? pccpchRSCP = (float?)tp["TD_PCCPCH_RSCP"];
                    if (pccpchRSCP == null)
                    {
                        return false;
                    }
                    if (pccpchRSCP < curFilterRxlev)
                    {
                        return false;
                    }
                    TDCell cell = tp.GetMainCell_TD_TDCell();
                    if (cell != null)
                    {
                        return judgeCoverLap(tp, pccpchRSCP, cell);
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool judgeCoverLap(TestPoint tp, float? pccpchRSCP, TDCell cell)
        {
            if (cell.Type != TDNodeBType.Outdoor)
            {
                return false;
            }
            CellCoverLap_TD covLap = getCellCoverLap(tp, cell);
            double distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, cell.Longitude, cell.Latitude);
            bool isBadCheck = distanceToCell > covLap.rationalDistance;
            if (isBadCheck)
            {
                covLap.AddBadSample(tp, distanceToCell, (float)pccpchRSCP);
                return true;
            }
            else
            {
                covLap.goodSampleCount++;
                return false;
            }
        }

        private CellCoverLap_TD getCellCoverLap(TestPoint tp, TDCell cell)
        {
            CellCoverLap_TD covLap = null;
            CellCoverLap clTmp = null;
            if (!cellLapRetDic.TryGetValue(cell.Name, out clTmp))
            {
                double radiusOfCell = CfgDataProvider.CalculateRadius(cell, nearestCellCount);
                covLap = new CellCoverLap_TD();
                covLap._CellCovRadius = radiusOfCell;
                covLap.rationalDistance = radiusOfCell * disFactor;
                covLap.tdCell = cell;
                covLap.nearestBTSs = CfgDataProvider.GetNearestBTSs(cell, nearestCellCount);
                covLap.mnger = new DTDataManager(MainModel.GetInstance());
                cellLapRetDic[cell.Name] = covLap;
            }
            else
            {
                covLap = (CellCoverLap_TD)clTmp;
            }
            if (fileIDNameDic.ContainsKey(tp.FileID)
                && !covLap.strFileID.Contains(fileIDNameDic[tp.FileID] + ""))
            {
                covLap.strFileID += fileIDNameDic[tp.FileID] + ",";
            }

            return covLap;
        }

        protected override void getResultAfterQuery()
        {
            curSelDIYSampleGroup.ThemeName = "TD_PCCPCH_RSCP";
            FilterCellCoverLap();
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.TD业务专题; }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CoverLapProperties_TD(this,false);
            }
        }
        #endregion
    };
}
