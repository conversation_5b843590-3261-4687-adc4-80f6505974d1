﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class PointStatusColoringQuery : ShowFuncForm
    {
        public PointStatusColoringQuery(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "点按状态着色"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20037, this.Name);
        }

        protected override void showForm()
        {
            PointStatusColoringForm form = MainModel.GetObjectFromBlackboard(typeof(PointStatusColoringForm).FullName) as PointStatusColoringForm;
            if (form == null || form.IsDisposed)
            {
                form = new PointStatusColoringForm(MainModel);
            }
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
    }
}
