﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRReasonPnlSuddenWeak
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label3 = new System.Windows.Forms.Label();
            this.numTimeLimit = new DevExpress.XtraEditors.SpinEdit();
            this.numSinrAvg = new DevExpress.XtraEditors.SpinEdit();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.grp)).BeginInit();
            this.grp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSinrAvg.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grp
            // 
            this.grp.Controls.Add(this.numTimeLimit);
            this.grp.Controls.Add(this.numSinrAvg);
            this.grp.Controls.Add(this.label1);
            this.grp.Controls.Add(this.label3);
            this.grp.Size = new System.Drawing.Size(606, 71);
            this.grp.Text = "质量毛刺";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(36, 39);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(89, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "在质差点的前后";
            // 
            // numTimeLimit
            // 
            this.numTimeLimit.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numTimeLimit.Location = new System.Drawing.Point(131, 34);
            this.numTimeLimit.Name = "numTimeLimit";
            this.numTimeLimit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numTimeLimit.Properties.IsFloatValue = false;
            this.numTimeLimit.Properties.Mask.EditMask = "N00";
            this.numTimeLimit.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numTimeLimit.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numTimeLimit.Size = new System.Drawing.Size(54, 21);
            this.numTimeLimit.TabIndex = 3;
            // 
            // numSinrAvg
            // 
            this.numSinrAvg.EditValue = new decimal(new int[] {
            15,
            0,
            0,
            0});
            this.numSinrAvg.Location = new System.Drawing.Point(310, 34);
            this.numSinrAvg.Name = "numSinrAvg";
            this.numSinrAvg.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSinrAvg.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numSinrAvg.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSinrAvg.Size = new System.Drawing.Size(54, 21);
            this.numSinrAvg.TabIndex = 2;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(191, 39);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(113, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "秒内，质量平均值≥";
            // 
            // ReasonPnlSuddenWeak
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Name = "ReasonPnlSuddenWeak";
            this.Size = new System.Drawing.Size(606, 71);
            ((System.ComponentModel.ISupportInitialize)(this.grp)).EndInit();
            this.grp.ResumeLayout(false);
            this.grp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSinrAvg.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SpinEdit numTimeLimit;
        private DevExpress.XtraEditors.SpinEdit numSinrAvg;
        private System.Windows.Forms.Label label1;
    }
}
