﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.Util;

namespace MasterCom.Util
{
    public static class TxtExporter
    {
        public static void Export(ObjectListView listView, bool remindOpen)
        {
            getReadyBeforeExport(listView, remindOpen);
        }

        private static void getReadyBeforeExport(ObjectListView listView, bool remindOpen)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "Text file 文本文件(*.txt)|*.txt";
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            start2Export(dlg.FileName, remindOpen, listView);
        }

        private static void start2Export(string fileName, bool remindOpen, ObjectListView ctrl)
        {
            object[] args = new object[3];
            args[0] = fileName;
            args[1] = remindOpen;
            args[2] = ctrl;
            WaitBox.Show("正在导出...", exportInThread, args);
        }

        /// <summary>
        /// args应为object[]数组，至少有3个参数，分别为文件名（string）,提醒打开（bool），需要导出数据的控件（control）
        /// </summary>
        /// <param name="args"></param>
        private static void exportInThread(object args)
        {
            object[] argsArray = args as object[];
            string fileName = argsArray[0] as string;
            bool remindOpen = (bool)argsArray[1];
            ObjectListView listView = argsArray[2] as ObjectListView;
            bool hasError = false;
            try
            {
                int columnCount = listView.AllColumns.Count;
                System.IO.FileStream fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.OpenOrCreate, System.IO.FileAccess.Write, System.IO.FileShare.Read);
                System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);
                try
                {
                    fillStreamWriter(listView, columnCount, streamWriter);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.ToString());
                }
                finally
                {
                    if (streamWriter != null)
                    {
                        streamWriter.Close();
                        streamWriter.Dispose();
                    }
                    if (fileStream != null)
                    {
                        fileStream.Close();
                        fileStream.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                hasError = true;
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            afterExport(fileName, remindOpen, hasError);
        }

        private static void fillStreamWriter(ObjectListView listView, int columnCount, System.IO.StreamWriter streamWriter)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < columnCount; i++)
            {
                OLVColumn column = listView.AllColumns[i];
                if (!column.IsVisible)
                {
                    continue;
                }
                sb.Append(column.Text + "\t");
            }
            streamWriter.WriteLine(sb.ToString());
            foreach (object obj in listView.Objects)
            {
                sb.Remove(0, sb.Length);
                for (int i = 0; i < columnCount; i++)
                {
                    OLVColumn column = listView.AllColumns[i];
                    if (!column.IsVisible)
                    {
                        continue;
                    }
                    object value = column.GetValue(obj);
                    sb.Append((value == null ? "" : value.ToString()) + "\t");
                    WaitBox.ProgressPercent = (int)(100.0 * (i + 1) / columnCount);
                }
                streamWriter.WriteLine(sb.ToString());
            }
        }

        private static void afterExport(string fileName, bool remindOpen, bool hasError)
        {
            if (remindOpen && !hasError)
            {
                DialogResult res = MessageBox.Show("文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo);
                if (DialogResult.Yes == res)
                {
                    try
                    {
                        System.Diagnostics.Process.Start(fileName);
                    }
                    catch
                    {
                        MessageBox.Show("打开失败!\r\n文件名:" + fileName);
                    }
                }
            }
        }
    }
}
