﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LowSpeedCellDlg_W : BaseDialog
    {
        public LowSpeedCellDlg_W()
        {
            InitializeComponent();
        }

        public void SetCondition(LowSpeedCellCondition_W cond)
        {
            chkFTPDownLoad.Checked = cond.DlCondition.IsCheck;
            numFTPDownLoadMin.Value = (decimal)cond.DlCondition.MinSpeed;
            numFTPDownLoadMax.Value = (decimal)cond.DlCondition.MaxSpeed;
            chkFTPUpLoad.Checked = cond.UlCondition.IsCheck;
            numFTPUpLoadMin.Value = (decimal)cond.UlCondition.MinSpeed;
            numFTPUpLoadMax.Value = (decimal)cond.UlCondition.MaxSpeed;
            numMinRscp.Value = (decimal)cond.RscpMin;
            numMaxRscp.Value = (decimal)cond.RscpMax;
            numProblemCount.Value = cond.PointCountMin;
            numProblemRate.Value = (decimal)cond.LowPercent;
        }
        public LowSpeedCellCondition_W GetCondition()
        {
            LowSpeedCellCondition_W cond = new LowSpeedCellCondition_W();
            cond.DlCondition.IsCheck = chkFTPDownLoad.Checked;
            cond.DlCondition.MinSpeed = (float)numFTPDownLoadMin.Value;
            cond.DlCondition.MaxSpeed = (float)numFTPDownLoadMax.Value;
            cond.UlCondition.IsCheck = chkFTPUpLoad.Checked;
            cond.UlCondition.MinSpeed = (float)numFTPUpLoadMin.Value;
            cond.UlCondition.MaxSpeed = (float)numFTPUpLoadMax.Value;
            cond.RscpMin = (float)numMinRscp.Value;
            cond.RscpMax = (float)numMaxRscp.Value;
            cond.PointCountMin = (int)numProblemCount.Value;
            cond.LowPercent = (float)numProblemRate.Value;
            return cond;
        }
    }
    public class LowSpeedCellCondition_W
    {
        public LowSpeedCellCondition_W()
        {
            DlCondition = new SpeedCondition();
            UlCondition = new SpeedCondition();
            UlCondition.MaxSpeed = 512;
            RscpMin = -120;
            RscpMax = 10;
            LowPercent = 80;
            PointCountMin = 1;
        }
        public SpeedCondition DlCondition { get; set; }
        public SpeedCondition UlCondition { get; set; }
        public float RscpMin { get; set; }
        public float RscpMax { get; set; }
        public float LowPercent { get; set; }
        public int PointCountMin { get; set; }
        public bool IsSavePoint { get; set; }
        public bool IsValidSpeed(TestPoint tp)
        {
            float? rscp = (float?)tp["W_TotalRSCP"];
            if (rscp != null && rscp >= RscpMin && rscp <= RscpMax)
            {
                int? type = (int?)tp["W_APP_type"];
                if (type == null)
                {
                    return false;
                }

                object objSpeed = tp["W_APP_Speed"];
                if (objSpeed == null)
                {
                    return false;
                }
                float? speed = (float)(int)objSpeed / 1024;
                if (speed < 0)
                {
                    return false;
                }
                if (type == (int)AppType.FTP_Download && DlCondition.IsCheck)
                {
                    return speed <= DlCondition.MaxSpeed && speed >= DlCondition.MinSpeed;
                }
                else if (type == (int)AppType.FTP_Upload && UlCondition.IsCheck)
                {
                    return speed <= UlCondition.MaxSpeed && speed >= UlCondition.MinSpeed;
                }
            }
            return false;
        }
    }
}
