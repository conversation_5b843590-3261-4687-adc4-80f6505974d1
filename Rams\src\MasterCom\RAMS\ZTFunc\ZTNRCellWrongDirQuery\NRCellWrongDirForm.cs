﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCellWrongDirForm : MinCloseForm
    {
        public NRCellWrongDirForm()
            : base()
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<NRCellWrongDir> wrongList, bool isTwiceBatch)
        {
            gcCompetedResult.Visible = isTwiceBatch;
            
            bindData(wrongList, isTwiceBatch);

            refreshTestPoint(wrongList);
        }

        private void bindData(List<NRCellWrongDir> wrongList, bool isTwiceBatch)
        {
            List<NRCellWrongDir> lstWrongBind = new List<NRCellWrongDir>();

            foreach (NRCellWrongDir cellWrong in wrongList)
            {
                NRCellWrongDir firstBatch = cellWrong.Clone();
                firstBatch.cellWrongBatch = CellWrongBatch.First;
                lstWrongBind.Add(firstBatch);

                if (isTwiceBatch)
                {
                    NRCellWrongDir secondBatch = cellWrong.Clone();
                    secondBatch.cellWrongBatch = CellWrongBatch.Second;
                    lstWrongBind.Add(secondBatch);
                }
            }
            gridControl.DataSource = lstWrongBind;
            gridControl.RefreshDataSource();
        }

        private void refreshTestPoint(List<NRCellWrongDir> wrongList)
        {
            MainModel.DTDataManager.Clear();
            foreach (NRCellWrongDir wrongCell in wrongList)
            {
                foreach (TestPoint tp in wrongCell.resultFirstBatch.WrongPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                foreach (TestPoint tp in wrongCell.resultSecondBatch.WrongPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireDTDataChanged(this);
        }

        private void refreshTestPoint(NRCellWrongDir cellWrong)
        {
            MainModel.DTDataManager.Clear();

            foreach (TestPoint tp in cellWrong.resultFirstBatch.WrongPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach (TestPoint tp in cellWrong.resultSecondBatch.WrongPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }

            MainModel.FireDTDataChanged(this);
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            MainModel.SelectedTDCell = null;
            int[] hds = gridView.GetSelectedRows();
            object row = null;
            if (hds.Length > 0)
            {
                row = gridView.GetRow(hds[0]);
            }
            else
            {
                return;
            }
            NRCellWrongDir wrongCell = null;
            if (row is NRCellWrongDir)
            {
                NRCellWrongDir wrongNRCell = row as NRCellWrongDir;
                wrongCell = wrongNRCell;
                MainModel.SetSelectedNRCell(wrongNRCell.Cell);
            }
            else
            {
                return;
            }

            refreshTestPoint(wrongCell);

            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GoToView(wrongCell.Longitude, wrongCell.Latitude);
            }
        }
    }
}
