﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraTab;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.FewAreaKPI
{
    public partial class LowestAreaKpiForm : MinCloseForm
    {
        public LowestAreaKpiForm()
            : base()
        {
            InitializeComponent();
        }

        private List<AreaBase> areaSet = null;
        public void FillData(DataSet dataSet,List<AreaBase> areas)
        {
            this.areaSet = areas;
            tabCtrl.SelectedPageChanged -= tabCtrl_SelectedPageChanged;
            this.tabCtrl.TabPages.Clear();
            ZTAreaArchiveLayer layer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(ZTAreaArchiveLayer)) as ZTAreaArchiveLayer;
            layer.Areas = areas;
            foreach (DataTable table in dataSet.Tables)
            {
                XtraTabPage page = tabCtrl.TabPages.Add(table.TableName);
                GridControl gridCtrl = new GridControl();
                GridView gv = new GridView(gridCtrl);
                gv.OptionsBehavior.Editable = false;
                gv.OptionsView.ShowGroupPanel = false;
                gridCtrl.MainView = gv;
                gridCtrl.DataSource = table;
                gv.PopulateColumns(table);
                foreach (DevExpress.XtraGrid.Columns.GridColumn col in gv.Columns)
                {
                    col.Caption = col.FieldName;
                    if (col.Caption == "Tag")
                    {
                        col.Visible = false;
                    }
                }
                gv.FocusedRowChanged += gv_FocusedRowChanged;
                gv.DoubleClick += gv_DoubleClick;
                page.Controls.Add(gridCtrl);
                gridCtrl.Dock = DockStyle.Fill;
            }
            tabCtrl.SelectedPageChanged += tabCtrl_SelectedPageChanged;
            tabCtrl.SelectedTabPageIndex = 0;
        }

        void gv_DoubleClick(object sender, EventArgs e)
        {
            ZTAreaArchiveLayer layer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(ZTAreaArchiveLayer)) 
                as ZTAreaArchiveLayer;
            layer.Areas = this.areaSet;
            GridView gv = sender as GridView;
            if (gv == null)
            {
                return;
            }
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gv.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            DataRowView rowView = gv.GetRow(info.RowHandle) as DataRowView;
            foreach (object cell in rowView.Row.ItemArray)
            {
                if (cell is AreaFileInfo)
                {
                    AreaFileInfo areaFi = cell as AreaFileInfo;
                    MainModel.MainForm.GetMapForm().GoToView(areaFi.Area.Bounds);
                    break;
                }
            }
        }

        void gv_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            GridView gv = sender as GridView;
            fillFileView(gv);
        }

        void tabCtrl_SelectedPageChanged(object sender, TabPageChangedEventArgs e)
        {
            GridControl gridCtrl = e.Page.Controls[0] as GridControl;
            GridView gv = gridCtrl.MainView as GridView;
            fillFileView(gv);
        }

        void fillFileView(GridView gv)
        {
            this.gridControlFileInfo.DataSource = null;
            System.Data.DataRowView rowView = gv.GetFocusedRow() as System.Data.DataRowView;
            if (rowView == null)
            {
                this.gridControlFileInfo.RefreshDataSource();
                return;
            }
            foreach (object cell in rowView.Row.ItemArray)
            {
                if (cell is AreaFileInfo)
                {
                    AreaFileInfo areaFi = cell as AreaFileInfo;
                    this.gridControlFileInfo.DataSource = areaFi.Files;
                    break;
                }
            }
            this.gridControlFileInfo.RefreshDataSource();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            GridControl grid = tabCtrl.SelectedTabPage.Controls[0] as GridControl;
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(grid.MainView as GridView);
        }

        private void ctxMenu_Opening(object sender, CancelEventArgs e)
        {
            //
        }

        private void miRelayFiles_Click(object sender, EventArgs e)
        {
            List<FileInfo> retList = new List<FileInfo>();
            foreach (int handle in gridViewFile.GetSelectedRows())
            {
                FileInfo file = gridViewFile.GetRow(handle) as FileInfo;
                if (file != null)
                {
                    retList.Add(file);
                }
            }

            if (retList.Count > 0)
            {
                ReplayFileManager.ReplayFiles(retList);
            }
        }

        private void gridViewFile_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = sender as GridView;
            if (gv == null)
            {
                return;
            }
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gv.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }

            FileInfo fi = gv.GetRow(info.RowHandle) as FileInfo;
            List<FileInfo> fiSet = new List<FileInfo>();
            fiSet.Add(fi);
            ReplayFileManager.ReplayFiles(fiSet);
        }

    }
}
