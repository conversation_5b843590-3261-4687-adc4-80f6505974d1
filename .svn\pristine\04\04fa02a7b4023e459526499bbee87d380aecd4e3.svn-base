﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class BlackBlock_Point
    {
        private int id;
        public int ID
        {
            get { return id; }
        }

        private int status;
        public int Status
        {
            get { return status; }
        }
        public string StatusString
        {
            get
            {
                return status == 0 ? "已创建" : "已关闭";
            }
        }

        private int weight;
        public int Weight
        {
            get { return weight; }
        }

        private int lac;
        public int LAC
        {
            get { return lac; }
        }

        private int ci;
        public int CI
        {
            get { return ci; }
        }

        private double longitude;
        public double Longitude
        {
            get { return longitude; }
        }

        private double latitude;
        public double Latitude
        {
            get { return latitude; }
        }

        private int createdDate;
        public int CreatedDate
        {
            get { return createdDate; }
        }
        public string CreatedDateString
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(createdDate * 1000L).ToString("yyyy-MM-dd"); }
        }

        private int closedDate;
        public string ClosedDateString
        {
            get { return closedDate == 0 ? "" : JavaDate.GetDateTimeFromMilliseconds(closedDate * 1000L).ToString("yyyy-MM-dd"); }
        }

        private int lastAbnormalDate;
        public string LastAbnormalDateString
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(lastAbnormalDate * 1000L).ToString("yyyy-MM-dd"); }
        }

        private int goodDaysCount;
        public int GoodDaysCount
        {
            get { return goodDaysCount; }
        }

        private int atuValidateCount;
        public int ATUValidateCount
        {
            get { return atuValidateCount; }
        }

        private int lastValidateDate;
        public string LastValidateDateString
        {
            get { return lastValidateDate == 0 ? "" : JavaDate.GetDateTimeFromMilliseconds(lastValidateDate * 1000L).ToString("yyyy-MM-dd"); }
        }

        private int validateStatus;
        public string ValidateStatusString
        {
            get
            {
                switch (validateStatus)
                {
                    case 1:
                        return "验证测试正常";
                    case 2:
                        return "验证测试异常";
                    default:
                        return "未验证测试";
                }
            }
        }

        private string areaIDs;
        public string AreaIDs
        {
            get { return areaIDs; }
        }

        private string areaNames;
        public string AreaNames
        {
            get { return areaNames; }
        }

        private string dwAreaNames;
        public string DWAreaNames
        {
            get { return dwAreaNames; }
        }

        private string gridNames;
        public string GridNames
        {
            get { return gridNames; }
        }

        private string roadNames;
        public string RoadNames
        {
            get { return roadNames; }
        }

        private string cellNames;
        public string CellNames
        {
            get { return cellNames; }
        }

        private string name;
        public string Name
        {
            get { return name; }
        }

        private string reason;
        public string Reason
        {
            get { return reason; }
        }

        private string solution;
        public string Solution
        {
            get { return solution; }
        }

        public List<BlackBlock_Point_Event> Events { get; set; } = new List<BlackBlock_Point_Event>();
        internal void AddEvent(BlackBlock_Point_Event be)
        {
            Events.Add(be);
        }

        public List<BlackBlock_Point_Date> ValidateDates { get; set; } = new List<BlackBlock_Point_Date>();
        internal void AddValidateDate(BlackBlock_Point_Date validateDate)
        {
            ValidateDates.Add(validateDate);
        }

        public List<BlackBlock_Point_Event> EventsValidate { get; set; } = new List<BlackBlock_Point_Event>();
        internal void AddEventValidate(BlackBlock_Point_Event be)
        {
            EventsValidate.Add(be);
        }

        public void Fill(Content content)
        {
            id = content.GetParamInt();
            status = content.GetParamInt();
            weight = content.GetParamInt();
            lac = content.GetParamInt();
            ci = content.GetParamInt();
            longitude = 0.0000001 * content.GetParamInt();
            latitude = 0.0000001 * content.GetParamInt();
            createdDate = content.GetParamInt();
            closedDate = content.GetParamInt();
            lastAbnormalDate = content.GetParamInt();
            goodDaysCount = content.GetParamInt();
            atuValidateCount = content.GetParamInt();
            lastValidateDate = content.GetParamInt();
            validateStatus = content.GetParamInt();
            areaIDs = content.GetParamString();
            areaNames = content.GetParamString();
            dwAreaNames = content.GetParamString();
            gridNames = content.GetParamString();
            roadNames = content.GetParamString();
            cellNames = content.GetParamString();
            name = content.GetParamString();
            reason = content.GetParamString();
            solution = content.GetParamString();
            //concertSolution = content.GetParamString()
        }

        public bool WithIn(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }
    }

    public class BlackBlock_Point_Event
    {
        private int blockID;
        public int BlockID
        {
            get { return blockID; }
        }
        private int projectID;
        public int ProjectID
        {
            get { return projectID; }
        }
        public string ProjectName
        {
            get 
            {
                if (CategoryManager.GetInstance()["Project"] != null)
                {
                    return ((CategoryEnum)CategoryManager.GetInstance()["Project"])[projectID].Name;
                }
                return ""; 
            }
        }

        private int fileID;
        public int FileID
        {
            get { return fileID; }
        }
        private int timeValue;
        public int TimeValue
        {
            get { return timeValue; }
        }
        public DateTime DateTime
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(timeValue * 1000L); }
        }
        public string DateTimeString
        {
            get { return DateTime.ToString("yyyy-MM-dd HH:mm:ss"); }
        }
        private double longitude;
        public double Longitude
        {
            get { return longitude; }
        }
        private double latitude;
        public double Latitude
        {
            get { return latitude; }
        }
        private int lac;
        public int LAC
        {
            get { return lac; }
        }
        private int ci;
        public int CI
        {
            get { return ci; }
        }
        private int eventID;
        public int EventID
        {
            get { return eventID; }
        }
        private int type;
        public string TypeString
        {
            get
            {
                return type == 0 ? "200米内" : @"同LAC\CI";
            }
        }
        private int centerPoint;
        public string CenterPointString
        {
            get
            {
                return centerPoint == 0 ? "否" : "是";
            }
        }
        private string fileName;
        public string FileName
        {
            get { return fileName; }
        }
        //新增
        private int serviceType;
        public int ServiceType
        {
            get { return serviceType; }
        }
        private string sampleTableName;
        public string SampleTableName
        {
            get { return sampleTableName; }
        }
        public int DistrictID { get; set; } = 1;
        public string EventDesc //事件描述
        {
            get
            {
                EventInfo e = EventInfoManager.GetInstance()[eventID];
                if (e != null)
                {
                    return e.Name;
                }
                else
                {
                    return "";
                }
            }
        }

        public EventInfo EventInfo
        {
            get
            {
                try
                {
                    return EventInfoManager.GetInstance()[eventID];
                }
                catch
                {
                    return null;
                }
            }
        }

        public string CellName
        {
            get
            {
                Cell cell = null;
                TDCell tdCell = null;
                if (EventInfo != null)
                {
                    if (EventInfo.Name.StartsWith("TD"))
                    {
                        tdCell = CellManager.GetInstance().GetTDCell(this.DateTime, lac, ci);
                    }
                    else
                    {
                        cell = CellManager.GetInstance().GetCell(this.DateTime, (ushort)lac, (ushort)ci);
                    }
                }
                else
                {
                    cell = CellManager.GetInstance().GetCell(this.DateTime, (ushort)lac, (ushort)ci);
                    if (cell == null)
                    {
                        tdCell = CellManager.GetInstance().GetTDCell(this.DateTime, lac, ci);
                    }
                }
                if (cell != null)
                {
                    return cell.Name;
                }
                if (tdCell != null)
                {
                    return tdCell.Name;
                }

                return "";
            }
        }

        internal void Fill(MasterCom.RAMS.Net.Content content)
        {
            blockID = content.GetParamInt();
            fileID = content.GetParamInt();
            content.GetParamInt();//sn
            longitude = 0.0000001 * content.GetParamInt();
            latitude = 0.0000001 * content.GetParamInt();
            eventID = content.GetParamInt();
            lac = content.GetParamInt();
            ci = content.GetParamInt();
            projectID = content.GetParamInt();
            timeValue = content.GetParamInt();
            centerPoint = content.GetParamInt();
            type = content.GetParamInt();
            fileName = content.GetParamString();
            serviceType = content.GetParamInt();
            sampleTableName = content.GetParamString();
        }

        internal Event ConvertToEvent()
        {
            Event eve = new Event();
            eve.Time = TimeValue;

            DTDataHeader header = new DTDataHeader();
            header.ID = FileID;
            header.Name = FileName;
            header.ProjectID = ProjectID;
            header.ServiceType = ServiceType;
            header.LogTable = string.Format("tb_log_file_{0}_{1:D2}", eve.DateTime.Year, eve.DateTime.Month);
            header.SampleTbName = SampleTableName;
            header.DistrictID = DistrictID;
            eve.ApplyHeader(header);

            eve.Longitude = Longitude;
            eve.Latitude = Latitude;
            eve.ID = EventID;
            eve["LAC"] = LAC;
            eve["CI"] = CI;
            return eve;
        }
    }

    public class BlackBlock_Point_Date
    {
        private int blockID;
        public int BlockID
        {
            get { return blockID; }
        }

        private int date;
        public string DateString
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(date * 1000L).ToString("yyyy-MM-dd"); }
        }

        private int testResult;
        public string TestResultString
        {
            get
            {
                switch (testResult)
                {
                    case 1:
                        return "验证测试正常";
                    case 2:
                        return "验证测试异常";
                    default:
                        return "未验证测试";
                }
            }
        }

        private bool includeProjectsNeeds = false;
        public string IncludeProjectsNeedsString
        {
            get { return includeProjectsNeeds ? "是" : "否"; }
        }

        internal void Fill(Content content)
        {
            blockID = content.GetParamInt();
            date = content.GetParamInt();
            testResult = content.GetParamInt();
            includeProjectsNeeds = content.GetParamInt() == 1;
        }
    }
}
