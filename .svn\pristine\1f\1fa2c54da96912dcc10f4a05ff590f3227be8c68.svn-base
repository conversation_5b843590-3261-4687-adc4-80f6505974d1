﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSQLReportEventInfoQuery_BJ : DIYSQLBase
    {
        readonly ReportEventCondition reportEventCond = null;
        public ZTSQLReportEventInfoQuery_BJ(MainModel mainModel, ReportEventCondition reportEventCond)
            : base(mainModel)
        {
            this.reportEventCond = reportEventCond;
        }

        private readonly List<ZTReportEventInfo_BJ> reportEventInfoList = new List<ZTReportEventInfo_BJ>();

        public List<ZTReportEventInfo_BJ> GetReportEventInfoList()
        {
            return reportEventInfoList;
        }

        protected override string getSqlTextString()
        {
            int stime = (int)(JavaDate.GetMilliseconds(reportEventCond.BeginTime) / 1000L);
            int etime = (int)(JavaDate.GetMilliseconds(reportEventCond.EndTime) / 1000L);

            return "exec mc_sp_beijing_report_event_get '" + reportEventCond.UserName + "'," + stime + "," + etime + ",'" + reportEventCond.NetType + "'";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int index = 0;
            E_VType[] rType = new E_VType[35];
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;

            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;

            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_String;

            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        private void fillData(Package package)
        {
            ZTReportEventInfo_BJ info = new ZTReportEventInfo_BJ();
            info.SN = reportEventInfoList.Count + 1;
            if (info.Fill(package.Content, this.MainModel))
            {
                bool deviceValid = isValidDevice(info);
                bool projectValid = isValidProject(info);
                bool regionValid = isInRegion(info);
                if (deviceValid && projectValid && regionValid)
                {
                    reportEventInfoList.Add(info);
                    MainModel.DTDataManager.Add(info);
                }
            }
        }

        private bool isValidDevice(ZTReportEventInfo_BJ eventInfo)
        {
            bool isValid = false;

            //如果是ATU数据,按照设备进行筛选过滤
            if (eventInfo.FileName.ToLower().IndexOf("ms") >= 0)   //文件名称中包含ms,定为ATU数据
            {
                foreach (string device in reportEventCond.DeviceDic.Keys)
                {
                    if (eventInfo.FileName.IndexOf(device) >= 0)
                    {
                        isValid = true;
                        break;
                    }
                }
            }
            else   //非ATU数据，根据设置决定是否显示
            {
                if (reportEventCond.IsShieldOther)  //需要屏蔽其它设备
                {
                    isValid = false;
                }
                else
                {
                    isValid = true;
                }
            }

            return isValid;
        }

        private bool isValidProject(ZTReportEventInfo_BJ eventInfo)
        {
            if (reportEventCond.ProjDic.ContainsKey(eventInfo.ProjectType))
            {
                return true;
            }

            return false;
        }

        private bool isInRegion(ZTReportEventInfo_BJ eventInfo)
        {
            if(MainModel.SearchGeometrys != null && MainModel.SearchGeometrys.IsSelectRegion())
            {
                return MainModel.SearchGeometrys.GeoOp.Contains(eventInfo.Longitude , eventInfo.Latitude);
            }
            return true;
        }

        public override string Name
        {
            get { return "ZTSQLReportEventInfoQuery_BJ"; }
        }
    }
}
