﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.ZTFunc.ZTCoverageOfCellAna;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCoverageOfCellAnaByCell : QueryBase
    {
        public ZTDIYCoverageOfCellAnaByCell(MainModel mainmodel)
            : base(mainmodel)
        {
        }

        public override string Name
        {
            get { return "小区覆盖带分析(按小区)"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12064, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedCell == null
                && searchGeometrys.SelectedTDCell == null
                && searchGeometrys.SelectedWCell == null
                && searchGeometrys.SelectedLTECell == null)
            {
                return false;
            }
            return true;
        }

        protected override void query()
        {
            MainModel.CovCellShowSetting = null;
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;

                WaitBox.Show("开始统计数据...", queryInThreadDefinedCell);
                foreach (TestPoint tp in tarTplist)
                {
                    MainModel.DTDataManager.Add(tp);
                }

                bool multiCell = MainModel.SelectedCells.Count > 1
                    || MainModel.SelectedTDCells.Count > 1
                    || (MainModel.SelectedLTECells != null && MainModel.SelectedLTECells.Count > 1);
                showResultForm(multiCell);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        List<TestPoint> tarTplist;
        private void queryInThreadDefinedCell()
        {
            try
            {
                WaitBox.ProgressPercent = 50;
                WaitBox.Text = "正在统计主/邻区下的采样点…";
                MainModel.CoverageOfCellInfos.Clear();
                tarTplist = new List<TestPoint>();
                List<ICell> cells = getCells();

                getCoverageCell(cells);
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.ProgressPercent = 99;
                WaitBox.Text = "统计完毕，准备显示。";
                WaitBox.Close();
            }
        }

        private List<ICell> getCells()
        {
            List<ICell> cells = new List<ICell>();

            if (MainModel.SelectedCells.Count > 0)
            {
                foreach (Cell cell in MainModel.SelectedCells)
                {
                    cells.Add(cell);
                }
            }
            else if (MainModel.SelectedTDCells.Count > 0)
            {
                foreach (TDCell tdcell in MainModel.SelectedTDCells)
                {
                    cells.Add(tdcell);
                }
            }
            else if (MainModel.SelectedLTECells != null && MainModel.SelectedLTECells.Count > 0)
            {
                foreach (LTECell cell in MainModel.SelectedLTECells)
                {
                    cells.Add(cell);
                }
            }
            else if (MainModel.SelectedWCells != null && MainModel.SelectedWCells.Count > 0)
            {
                foreach (WCell cell in MainModel.SelectedWCells)
                {
                    cells.Add(cell);
                }
            }

            return cells;
        }

        private void getCoverageCell(List<ICell> cells)
        {
            foreach (ICell cell in cells)
            {
                Dictionary<ICell, CoverageCellInfo> cellInfoDic = new Dictionary<ICell, CoverageCellInfo>();
                DIYSampleToCell sampleToCell = new DIYSampleToCell(MainModel, cell, cellInfoDic, tarTplist);
                sampleToCell.SetQueryCondition(Condition);
                sampleToCell.Query();
                foreach (CoverageCellInfo cellInfo in cellInfoDic.Values)
                {
                    MainModel.CoverageOfCellInfos.Add(cellInfo);
                }
                if (cell is Cell)
                {
                    MainModel.FireSetDefaultMapSerialTheme("GSM RxLevSub");
                }
                else if (cell is TDCell)
                {
                    MainModel.FireSetDefaultMapSerialTheme("TD_PCCPCH_RSCP");
                }
                else if (cell is LTECell)
                {
                    MainModel.FireSetDefaultMapSerialTheme("TD_LTE_RSRP");
                }
                else if (cell is WCell)
                {
                    MainModel.FireSetDefaultMapSerialTheme("WCDMA", "TotalRSCP");
                }
            }
        }

        private void showResultForm(bool isMultiCells)
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(CoverageShowForm).FullName);
            CoverageShowForm coverageShowForm = obj == null ? null : obj as CoverageShowForm;
            if (coverageShowForm == null || coverageShowForm.IsDisposed)
            {
                coverageShowForm = new CoverageShowForm(MainModel);
            }
            coverageShowForm.isMultiCells = isMultiCells;
            coverageShowForm.FillData();

            coverageShowForm.Show(MainModel.MainForm);

            coverageShowForm.clickOK();
        }
    }

    public class DIYSampleToCell : DIYSampleByRegion
    {
        readonly Dictionary<ICell, CoverageCellInfo> cellInfoDic;
        readonly ICell curCell;
        readonly List<TestPoint> tpList;

        public DIYSampleToCell(MainModel mainModel, ICell cell, Dictionary<ICell, CoverageCellInfo> cellInfoDic,List<TestPoint> tpList)
            : base(mainModel)
        {
            this.curCell = cell;
            this.cellInfoDic = cellInfoDic;
            this.tpList = tpList;
            isAddSampleToDTDataManager = false;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);


            param = new Dictionary<string, object>();
            param["param_name"] = "isampleid";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "N_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_NCell_UARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_NCell_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_NCell_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_TotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SNeiFreq";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SNeiPSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SNeiRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"sample");
            tmpDic.Add("themeName", (object)"小区采样点");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void AddDIYRegion_Intersect(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            if (curCell is Cell)
            {
                package.Content.AddParam(curCell.Longitude - 0.03);
                package.Content.AddParam(curCell.Latitude + 0.03);
                package.Content.AddParam(curCell.Longitude + 0.03);
                package.Content.AddParam(curCell.Latitude - 0.03);
            }
            else if (curCell is TDCell || curCell is LTECell || curCell is WCell)
            {
                package.Content.AddParam(curCell.Longitude - 0.05);
                package.Content.AddParam(curCell.Latitude + 0.05);
                package.Content.AddParam(curCell.Longitude + 0.05);
                package.Content.AddParam(curCell.Latitude - 0.05);
            }
        }

        protected override void AddDIYRegion_Sample(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectSample);
            if (curCell is Cell)
            {
                package.Content.AddParam(curCell.Longitude - 0.03);
                package.Content.AddParam(curCell.Latitude + 0.03);
                package.Content.AddParam(curCell.Longitude + 0.03);
                package.Content.AddParam(curCell.Latitude - 0.03);
            }
            else if (curCell is TDCell || curCell is LTECell || curCell is WCell)
            {
                package.Content.AddParam(curCell.Longitude - 0.05);
                package.Content.AddParam(curCell.Latitude + 0.05);
                package.Content.AddParam(curCell.Longitude + 0.05);
                package.Content.AddParam(curCell.Latitude - 0.05);
            }
        }

        protected override void query()
        {
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
            {
                return;
            }
            WaitBox.CanCancel = true;
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);

            }
            catch
            {
                clientProxy.Close();
            }
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                if (curCell is Cell)
                {
                    bool inRegion = (curCell.Longitude - 0.03 < tp.Longitude) && (curCell.Longitude + 0.03 > tp.Longitude)
                        && (curCell.Latitude - 0.03 < tp.Latitude) && (curCell.Latitude + 0.03 > tp.Latitude);
                    if (inRegion)
                    {
                        return true;
                    }
                }
                else if (curCell is TDCell || curCell is LTECell || curCell is WCell)
                {
                    bool inRegion = (curCell.Longitude - 0.05 < tp.Longitude) && (curCell.Longitude + 0.05 > tp.Longitude)
                        && (curCell.Latitude - 0.05 < tp.Latitude) && (curCell.Latitude + 0.05 > tp.Latitude);
                    if (inRegion)
                    {
                        return true;
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        protected override void doWithDTData(TestPoint tp)
        {
            if (tp is TestPointDetail && curCell is Cell)
            {
                Cell selectedCell = curCell as Cell;
                int? lac = (int?)tp["LAC"];
                int? ci = (int?)tp["CI"];
                if (lac == null && ci == null)
                {
                    return;
                }
                if (selectedCell.LAC == lac && selectedCell.CI == ci)
                {
                    CoverageCellInfo cellInfo = null;
                    if (!cellInfoDic.TryGetValue(selectedCell, out cellInfo))
                    {
                        cellInfo = new CoverageCellInfo();
                        cellInfo.cellType = 1;
                        cellInfo.cell = curCell;
                        cellInfoDic.Add(selectedCell, cellInfo);
                    }
                    cellInfo.tpList.Add(tp);
                    tpList.Add(tp);
                }
                else
                {
                    for (int i = 0; i < 6; i++)
                    {
                        if (tp["N_BCCH", i] == null || tp["N_BSIC", i] == null)
                            continue;
                        short nBcch = (short)tp["N_BCCH", i];
                        byte nBsic = (byte)tp["N_BSIC", i];
                        if (selectedCell.BCCH != nBcch || selectedCell.BSIC != nBsic)
                        {
                            continue;
                        }
                        Cell nbCell = CellManager.GetInstance().GetNearestCell(tp.DateTime, nBcch, nBsic, tp.Longitude, tp.Latitude);
                        if (nbCell == null || nbCell.ID != selectedCell.ID) //同频同扰码但非查询小区的小区不算
                        {
                            continue;
                        }

                        CoverageCellInfo cellInfo = null;
                        if (!cellInfoDic.TryGetValue(selectedCell, out cellInfo))
                        {
                            cellInfo = new CoverageCellInfo();
                            cellInfo.cellType = 1;
                            cellInfo.cell = selectedCell;
                            cellInfoDic.Add(selectedCell, cellInfo);
                        }
                        cellInfo.tpListAsNbcell.Add(tp);
                        string snk = tp.SN.ToString() + tp.FileID.ToString() + tp.Time.ToString();
                        cellInfo.snNnIDic[snk] = i;
                        tpList.Add(tp);
                        break;
                    }
                }
            }
            else if (tp is TDTestPointDetail && curCell is TDCell)
            {
                TDCell selectedTdCell = curCell as TDCell;
                if (selectedTdCell != null && tp["TD_SCell_LAC"] != null && tp["TD_SCell_CI"] != null)
                {
                    if (selectedTdCell.LAC == (int)tp["TD_SCell_LAC"] && selectedTdCell.CI == (int)tp["TD_SCell_CI"])
                    {
                        if (cellInfoDic.ContainsKey(selectedTdCell))
                        {
                            CoverageCellInfo cellInfo = cellInfoDic[selectedTdCell];
                            cellInfo.tpList.Add(tp);
                            tpList.Add(tp);
                        }
                        else
                        {
                            CoverageCellInfo cellInfo = new CoverageCellInfo();
                            cellInfo.cellType = 2;
                            cellInfo.cell = MainModel.SelectedCell;
                            cellInfo.tpList.Add(tp);
                            cellInfoDic.Add(selectedTdCell, cellInfo);
                            tpList.Add(tp);
                        }
                    }
                    else
                    {
                        for (int i = 0; i < 6; i++)
                        {
                            if (tp["TD_NCell_UARFCN", i] == null || tp["TD_NCell_CPI", i] == null)
                                continue;
                            int nArfcn = (int)tp["TD_NCell_UARFCN", i];
                            int nCpi = (int)tp["TD_NCell_CPI", i];
                            if (selectedTdCell.FREQ == nArfcn && selectedTdCell.CPI == nCpi)
                            {
                                TDCell nbCell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, (short)nArfcn, (short)nCpi, tp.Longitude, tp.Latitude);
                                if (nbCell == null)
                                    continue;
                                if (nbCell.ID != MainModel.SelectedTDCell.ID)
                                {
                                    continue;
                                }
                                if (cellInfoDic.ContainsKey(selectedTdCell))
                                {
                                    CoverageCellInfo cellInfo = cellInfoDic[selectedTdCell];
                                    cellInfo.tpListAsNbcell.Add(tp);
                                    string snk = tp.SN.ToString() + tp.FileID.ToString() + tp.Time.ToString();
                                    if (!cellInfo.snNnIDic.ContainsKey(snk))
                                    {
                                        cellInfo.snNnIDic.Add(snk, i);
                                    }
                                    tpList.Add(tp);
                                }
                                else
                                {
                                    CoverageCellInfo cellInfo = new CoverageCellInfo();
                                    cellInfo.cellType = 2;
                                    cellInfo.cell = MainModel.SelectedTDCell;
                                    cellInfo.tpListAsNbcell.Add(tp);
                                    string snk = tp.SN.ToString() + tp.FileID.ToString() + tp.Time.ToString();
                                    if (!cellInfo.snNnIDic.ContainsKey(snk))
                                    {
                                        cellInfo.snNnIDic.Add(snk, i);
                                    }
                                    cellInfoDic.Add(selectedTdCell, cellInfo);
                                    tpList.Add(tp);
                                }
                                break;
                            }
                        }
                    }
                }
            }
            else if (tp is LTETestPointDetail && curCell is LTECell)
            {
                LTECell selectedCell = curCell as LTECell;
                LTECell sCell = tp.GetMainCell_LTE();
                if (sCell != null && sCell.ID == selectedCell.ID)
                {
                    CoverageCellInfo cellInfo = null;
                    if (!cellInfoDic.TryGetValue(selectedCell, out cellInfo))
                    {
                        cellInfo = new CoverageCellInfo();
                        cellInfo.cellType = 3;
                        cellInfo.cell = curCell;
                        cellInfoDic.Add(selectedCell, cellInfo);
                    }
                    cellInfo.tpList.Add(tp);
                    tpList.Add(tp);
                }
                else
                {
                    for (int i = 0; i < 6; i++)
                    {
                        LTECell nCell = tp.GetNBCell_LTE(i);
                        if (nCell == null || nCell.ID != selectedCell.ID)
                        {
                            continue;
                        }

                        CoverageCellInfo cellInfo = null;
                        if (!cellInfoDic.TryGetValue(selectedCell, out cellInfo))
                        {
                            cellInfo = new CoverageCellInfo();
                            cellInfo.cellType = 3;
                            cellInfo.cell = selectedCell;
                            cellInfoDic.Add(selectedCell, cellInfo);
                        }
                        cellInfo.tpListAsNbcell.Add(tp);
                        string snk = tp.SN.ToString() + tp.FileID.ToString() + tp.Time.ToString();
                        cellInfo.snNnIDic[snk] = i;
                        tpList.Add(tp);
                        break;
                    }
                }
            }
            else if (tp is WCDMATestPointDetail && curCell is WCell)
            {
                WCell selectedCell = curCell as WCell;
                WCell sCell = tp.GetMainCell_W();
                if (sCell != null && sCell.ID == selectedCell.ID)
                {
                    CoverageCellInfo cellInfo = null;
                    if (!cellInfoDic.TryGetValue(selectedCell, out cellInfo))
                    {
                        cellInfo = new CoverageCellInfo();
                        cellInfo.cellType = 4;
                        cellInfo.cell = curCell;
                        cellInfoDic.Add(selectedCell, cellInfo);
                    }
                    cellInfo.tpList.Add(tp);
                    tpList.Add(tp);
                }
                else
                {
                    for (int i = 0; i < 6; i++)
                    {
                        WCell nCell = tp.GetNBCell_W_WCell(i);
                        if (nCell == null || nCell.ID != selectedCell.ID)
                        {
                            continue;
                        }

                        CoverageCellInfo cellInfo = null;
                        if (!cellInfoDic.TryGetValue(selectedCell, out cellInfo))
                        {
                            cellInfo = new CoverageCellInfo();
                            cellInfo.cellType = 4;
                            cellInfo.cell = selectedCell;
                            cellInfoDic.Add(selectedCell, cellInfo);
                        }
                        cellInfo.tpListAsNbcell.Add(tp);
                        string snk = tp.SN.ToString() + tp.FileID.ToString() + tp.Time.ToString();
                        cellInfo.snNnIDic[snk] = i;
                        tpList.Add(tp);
                        break;
                    }
                }
            }
        }

        protected override void queryInThread(object o)
        {
            ClientProxy clientProxy = (ClientProxy)o;
            Package package = clientProxy.Package;
            foreach (TimePeriod period in Condition.Periods)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                WaitBox.Text = "正在统计采样点数据...";
                queryPeriodInfo(clientProxy, package, period, false);
            }
        }
    }

    public class CoverageCellInfo
    {
        /// <summary>
        /// 小区类型，GSM：1，TD：2, LTE: 3, WCDMA: 4
        /// </summary>
        public int cellType { get; set; }
        public ICell cell { get; set; }
        public List<TestPoint> tpList { get; set; } = new List<TestPoint>(); //小区下的采样点集
        public List<TestPoint> tpListAsNbcell { get; set; } = new List<TestPoint>();//小区作为其它小区的邻区时，关联它的采样点集
        public Dictionary<string, int> snNnIDic { get; set; } = new Dictionary<string, int>();
        public List<CoverageCellInfo> nbcellList { get; set; } = new List<CoverageCellInfo>();

        public double Longitude
        {
            get
            {
                return cell.Longitude;
            }
        }
        public double Latitude
        {
            get
            {
                return cell.Latitude;
            }
        }

        public string Name
        {
            get
            {
                return cell.Name;
            }
        }
    }
}
