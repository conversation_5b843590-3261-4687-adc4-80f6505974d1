﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class AdviseProcessor
    {
        protected AdviseProcessor()
        {

        }

        /// <summary>
        /// 根据时间范围返回已经提交的建议
        /// </summary>
        /// <param name="timeStart"></param>
        /// <param name="timeEnd"></param>
        /// <returns></returns>
        public static string GetAdviseOfTimeRegion(MainModel mainModel,DateTime timeStart, DateTime timeEnd,
            bool IsTDInf)
        {
            query = new AdviseQuery(mainModel, IsTDInf, timeStart, timeEnd);
            WaitBox.Show(GetAdviseInf);
            return advise;
        }
        private static string advise;
        private static AdviseQuery query;
        private static void GetAdviseInf()
        {
            WaitBox.ProgressPercent = 30;
            query.Query();
            WaitBox.ProgressPercent = 100 ;
            WaitBox.Close();
            advise = query.Advise;
        }

        /// <summary>
        /// 根据时间范围以及建议将其提交到数据库
        /// </summary>
        /// <param name="advise"></param>
        /// <param name="timeStart"></param>
        /// <param name="timeEnd"></param>
        /// <returns></returns>
        public static bool SubmitAdviseToDB(MainModel mainModel,string advise,DateTime timeStart, DateTime timeEnd,
            bool IsTDInf)
        {
            submit = new AdviseSubmit(mainModel, advise, IsTDInf, timeStart, timeEnd);
            WaitBox.Show(SubmitAd);
            return IsSubmitSuccess;
        }
        private static AdviseSubmit submit;
        private static bool IsSubmitSuccess = false;
        private static void SubmitAd()
        {
            WaitBox.ProgressPercent = 25;
            submit.Query();
            WaitBox.ProgressPercent = 100;            
            IsSubmitSuccess = (submit.ChangeCCount == 1);
            IsSubmitSuccess = true;
            WaitBox.Close();
        }
    }
}
