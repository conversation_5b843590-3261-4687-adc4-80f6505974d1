﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTNBCellMissForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStripListView = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExpendAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemUnOpenAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemPushOutGridInfo = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemPushOut = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcelOneByOne = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.lblConditionVisible = new System.Windows.Forms.LinkLabel();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnOrgCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colMainCellRxLevMean = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellLac = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellBCCH = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellBSIC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellRxLevMean = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellDes = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGridCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colOneGridPos = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAngle = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnIsOrgAngle = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.pnlCondition = new System.Windows.Forms.Panel();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.numNBDistanceMax = new System.Windows.Forms.NumericUpDown();
            this.numNBDistanceMin = new System.Windows.Forms.NumericUpDown();
            this.numNBCellRxLevMax = new System.Windows.Forms.NumericUpDown();
            this.numNBCellRxLevMin = new System.Windows.Forms.NumericUpDown();
            this.label14 = new System.Windows.Forms.Label();
            this.numCellRxLevMax = new System.Windows.Forms.NumericUpDown();
            this.cbxBandType = new System.Windows.Forms.ComboBox();
            this.lblBandType = new System.Windows.Forms.Label();
            this.rdbNBigerThenMain = new System.Windows.Forms.RadioButton();
            this.label13 = new System.Windows.Forms.Label();
            this.numDValueWithTop1 = new System.Windows.Forms.NumericUpDown();
            this.rdbTopN = new System.Windows.Forms.RadioButton();
            this.rdbDValueWithTop1 = new System.Windows.Forms.RadioButton();
            this.label12 = new System.Windows.Forms.Label();
            this.numTopN = new System.Windows.Forms.NumericUpDown();
            this.numGridCountMin = new System.Windows.Forms.NumericUpDown();
            this.numRxLevDValue = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.lblConditionDisappear = new System.Windows.Forms.LinkLabel();
            this.numCellRxLevMin = new System.Windows.Forms.NumericUpDown();
            this.label10 = new System.Windows.Forms.Label();
            this.numSampleCount = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.cbxInDoorCell = new System.Windows.Forms.ComboBox();
            this.cbxInOrgAngle = new System.Windows.Forms.ComboBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.comboBoxAngleMax = new System.Windows.Forms.ComboBox();
            this.comboBoxAngleMin = new System.Windows.Forms.ComboBox();
            this.label5 = new System.Windows.Forms.Label();
            this.btnOK = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.cmbNBCellStatus = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.contextMenuStripListView.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.pnlCondition.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numNBDistanceMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNBDistanceMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNBCellRxLevMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNBCellRxLevMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellRxLevMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDValueWithTop1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTopN)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCountMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellRxLevMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStripListView
            // 
            this.contextMenuStripListView.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExpendAll,
            this.ToolStripMenuItemUnOpenAll,
            this.ToolStripMenuItemPushOutGridInfo,
            this.ToolStripMenuItemPushOut,
            this.miExportToExcelOneByOne});
            this.contextMenuStripListView.Name = "contextMenuStripListView";
            this.contextMenuStripListView.Size = new System.Drawing.Size(175, 114);
            // 
            // ToolStripMenuItemExpendAll
            // 
            this.ToolStripMenuItemExpendAll.Name = "ToolStripMenuItemExpendAll";
            this.ToolStripMenuItemExpendAll.Size = new System.Drawing.Size(174, 22);
            this.ToolStripMenuItemExpendAll.Text = "展开所有节点";
            this.ToolStripMenuItemExpendAll.Click += new System.EventHandler(this.ToolStripMenuItemExpendAll_Click);
            // 
            // ToolStripMenuItemUnOpenAll
            // 
            this.ToolStripMenuItemUnOpenAll.Name = "ToolStripMenuItemUnOpenAll";
            this.ToolStripMenuItemUnOpenAll.Size = new System.Drawing.Size(174, 22);
            this.ToolStripMenuItemUnOpenAll.Text = "折叠所有节点";
            this.ToolStripMenuItemUnOpenAll.Click += new System.EventHandler(this.ToolStripMenuItemUnOpenAll_Click);
            // 
            // ToolStripMenuItemPushOutGridInfo
            // 
            this.ToolStripMenuItemPushOutGridInfo.Name = "ToolStripMenuItemPushOutGridInfo";
            this.ToolStripMenuItemPushOutGridInfo.Size = new System.Drawing.Size(174, 22);
            this.ToolStripMenuItemPushOutGridInfo.Text = "栅格信息列表";
            this.ToolStripMenuItemPushOutGridInfo.Visible = false;
            this.ToolStripMenuItemPushOutGridInfo.Click += new System.EventHandler(this.ToolStripMenuItemPushOutGridInfo_Click);
            // 
            // ToolStripMenuItemPushOut
            // 
            this.ToolStripMenuItemPushOut.Name = "ToolStripMenuItemPushOut";
            this.ToolStripMenuItemPushOut.Size = new System.Drawing.Size(174, 22);
            this.ToolStripMenuItemPushOut.Text = "导出列表";
            this.ToolStripMenuItemPushOut.Click += new System.EventHandler(this.ToolStripMenuItemPuthOut_Click);
            // 
            // miExportToExcelOneByOne
            // 
            this.miExportToExcelOneByOne.Name = "miExportToExcelOneByOne";
            this.miExportToExcelOneByOne.Size = new System.Drawing.Size(174, 22);
            this.miExportToExcelOneByOne.Text = "逐条导出到Excel...";
            this.miExportToExcelOneByOne.Click += new System.EventHandler(this.miExportToExcelOneByOne_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.lblConditionVisible);
            this.groupBox2.Controls.Add(this.listViewTotal);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox2.Location = new System.Drawing.Point(0, 0);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(1119, 513);
            this.groupBox2.TabIndex = 13;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "筛选条件";
            // 
            // lblConditionVisible
            // 
            this.lblConditionVisible.AutoSize = true;
            this.lblConditionVisible.Location = new System.Drawing.Point(7, 0);
            this.lblConditionVisible.Name = "lblConditionVisible";
            this.lblConditionVisible.Size = new System.Drawing.Size(55, 14);
            this.lblConditionVisible.TabIndex = 14;
            this.lblConditionVisible.TabStop = true;
            this.lblConditionVisible.Text = "筛选条件";
            this.lblConditionVisible.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lblConditionVisible_LinkClicked);
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnOrgCell);
            this.listViewTotal.AllColumns.Add(this.colMainCellRxLevMean);
            this.listViewTotal.AllColumns.Add(this.olvColumnNBCell);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellLac);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellCI);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellBCCH);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellBSIC);
            this.listViewTotal.AllColumns.Add(this.olvColumnNBCellRxLevMean);
            this.listViewTotal.AllColumns.Add(this.olvColumnNBCellDes);
            this.listViewTotal.AllColumns.Add(this.olvColumnGridCount);
            this.listViewTotal.AllColumns.Add(this.colOneGridPos);
            this.listViewTotal.AllColumns.Add(this.olvColumnSampleCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellDistance);
            this.listViewTotal.AllColumns.Add(this.olvColumnAngle);
            this.listViewTotal.AllColumns.Add(this.olvColumnIsOrgAngle);
            this.listViewTotal.AllColumns.Add(this.olvColumnRoadDesc);
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnOrgCell,
            this.colMainCellRxLevMean,
            this.olvColumnNBCell,
            this.olvColumnCellLac,
            this.olvColumnCellCI,
            this.olvColumnCellBCCH,
            this.olvColumnCellBSIC,
            this.olvColumnNBCellRxLevMean,
            this.olvColumnNBCellDes,
            this.olvColumnGridCount,
            this.colOneGridPos,
            this.olvColumnSampleCount,
            this.olvColumnCellDistance,
            this.olvColumnAngle,
            this.olvColumnIsOrgAngle,
            this.olvColumnRoadDesc});
            this.listViewTotal.ContextMenuStrip = this.contextMenuStripListView;
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.IsNeedShowOverlay = false;
            this.listViewTotal.Location = new System.Drawing.Point(3, 18);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(1113, 492);
            this.listViewTotal.TabIndex = 11;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            this.listViewTotal.DrawItem += new System.Windows.Forms.DrawListViewItemEventHandler(this.listViewTotal_DrawItem);
            this.listViewTotal.DoubleClick += new System.EventHandler(this.listViewTotal_DoubleClick);
            // 
            // olvColumnOrgCell
            // 
            this.olvColumnOrgCell.HeaderFont = null;
            this.olvColumnOrgCell.Text = "参考小区";
            this.olvColumnOrgCell.Width = 112;
            // 
            // colMainCellRxLevMean
            // 
            this.colMainCellRxLevMean.HeaderFont = null;
            this.colMainCellRxLevMean.Text = "参考主小区平均场强";
            // 
            // olvColumnNBCell
            // 
            this.olvColumnNBCell.HeaderFont = null;
            this.olvColumnNBCell.Text = "邻区(个数)";
            this.olvColumnNBCell.Width = 127;
            // 
            // olvColumnCellLac
            // 
            this.olvColumnCellLac.HeaderFont = null;
            this.olvColumnCellLac.Text = "位置区码(LAC)";
            this.olvColumnCellLac.Width = 61;
            // 
            // olvColumnCellCI
            // 
            this.olvColumnCellCI.HeaderFont = null;
            this.olvColumnCellCI.Text = "小区码（CI）";
            this.olvColumnCellCI.Width = 44;
            // 
            // olvColumnCellBCCH
            // 
            this.olvColumnCellBCCH.HeaderFont = null;
            this.olvColumnCellBCCH.Text = "小区频点(BCCH)";
            this.olvColumnCellBCCH.Width = 100;
            // 
            // olvColumnCellBSIC
            // 
            this.olvColumnCellBSIC.HeaderFont = null;
            this.olvColumnCellBSIC.Text = "小区扰码(BSIC)";
            this.olvColumnCellBSIC.Width = 100;
            // 
            // olvColumnNBCellRxLevMean
            // 
            this.olvColumnNBCellRxLevMean.HeaderFont = null;
            this.olvColumnNBCellRxLevMean.Text = "邻区平均场强";
            // 
            // olvColumnNBCellDes
            // 
            this.olvColumnNBCellDes.HeaderFont = null;
            this.olvColumnNBCellDes.Text = "邻区状况";
            this.olvColumnNBCellDes.Width = 70;
            // 
            // olvColumnGridCount
            // 
            this.olvColumnGridCount.AspectName = "";
            this.olvColumnGridCount.HeaderFont = null;
            this.olvColumnGridCount.Text = "栅格数";
            // 
            // colOneGridPos
            // 
            this.colOneGridPos.HeaderFont = null;
            this.colOneGridPos.Text = "其一栅格经纬度";
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "采样点数";
            // 
            // olvColumnCellDistance
            // 
            this.olvColumnCellDistance.HeaderFont = null;
            this.olvColumnCellDistance.Text = "邻区间距（米）";
            this.olvColumnCellDistance.Width = 91;
            // 
            // olvColumnAngle
            // 
            this.olvColumnAngle.HeaderFont = null;
            this.olvColumnAngle.Text = "天线夹角";
            // 
            // olvColumnIsOrgAngle
            // 
            this.olvColumnIsOrgAngle.HeaderFont = null;
            this.olvColumnIsOrgAngle.Text = "是否在主瓣方向";
            this.olvColumnIsOrgAngle.Width = 335;
            // 
            // olvColumnRoadDesc
            // 
            this.olvColumnRoadDesc.HeaderFont = null;
            this.olvColumnRoadDesc.Text = "涉及道路";
            this.olvColumnRoadDesc.Width = 100;
            // 
            // pnlCondition
            // 
            this.pnlCondition.BackColor = System.Drawing.SystemColors.Control;
            this.pnlCondition.Controls.Add(this.groupBox1);
            this.pnlCondition.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlCondition.Location = new System.Drawing.Point(0, 0);
            this.pnlCondition.Name = "pnlCondition";
            this.pnlCondition.Size = new System.Drawing.Size(1119, 132);
            this.pnlCondition.TabIndex = 14;
            this.pnlCondition.Visible = false;
            // 
            // groupBox1
            // 
            this.groupBox1.BackColor = System.Drawing.SystemColors.Window;
            this.groupBox1.Controls.Add(this.numNBDistanceMax);
            this.groupBox1.Controls.Add(this.numNBDistanceMin);
            this.groupBox1.Controls.Add(this.numNBCellRxLevMax);
            this.groupBox1.Controls.Add(this.numNBCellRxLevMin);
            this.groupBox1.Controls.Add(this.label14);
            this.groupBox1.Controls.Add(this.numCellRxLevMax);
            this.groupBox1.Controls.Add(this.cbxBandType);
            this.groupBox1.Controls.Add(this.lblBandType);
            this.groupBox1.Controls.Add(this.rdbNBigerThenMain);
            this.groupBox1.Controls.Add(this.label13);
            this.groupBox1.Controls.Add(this.numDValueWithTop1);
            this.groupBox1.Controls.Add(this.rdbTopN);
            this.groupBox1.Controls.Add(this.rdbDValueWithTop1);
            this.groupBox1.Controls.Add(this.label12);
            this.groupBox1.Controls.Add(this.numTopN);
            this.groupBox1.Controls.Add(this.numGridCountMin);
            this.groupBox1.Controls.Add(this.numRxLevDValue);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.lblConditionDisappear);
            this.groupBox1.Controls.Add(this.numCellRxLevMin);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Controls.Add(this.numSampleCount);
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Controls.Add(this.cbxInDoorCell);
            this.groupBox1.Controls.Add(this.cbxInOrgAngle);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.comboBoxAngleMax);
            this.groupBox1.Controls.Add(this.comboBoxAngleMin);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.btnOK);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.cmbNBCellStatus);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1119, 132);
            this.groupBox1.TabIndex = 11;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "筛选条件";
            // 
            // numNBDistanceMax
            // 
            this.numNBDistanceMax.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numNBDistanceMax.Location = new System.Drawing.Point(239, 43);
            this.numNBDistanceMax.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numNBDistanceMax.Name = "numNBDistanceMax";
            this.numNBDistanceMax.Size = new System.Drawing.Size(75, 22);
            this.numNBDistanceMax.TabIndex = 45;
            this.numNBDistanceMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numNBDistanceMax.Value = new decimal(new int[] {
            500,
            0,
            0,
            0});
            // 
            // numNBDistanceMin
            // 
            this.numNBDistanceMin.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numNBDistanceMin.Location = new System.Drawing.Point(125, 44);
            this.numNBDistanceMin.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numNBDistanceMin.Name = "numNBDistanceMin";
            this.numNBDistanceMin.Size = new System.Drawing.Size(75, 22);
            this.numNBDistanceMin.TabIndex = 44;
            this.numNBDistanceMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // numNBCellRxLevMax
            // 
            this.numNBCellRxLevMax.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numNBCellRxLevMax.Location = new System.Drawing.Point(753, 43);
            this.numNBCellRxLevMax.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numNBCellRxLevMax.Minimum = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numNBCellRxLevMax.Name = "numNBCellRxLevMax";
            this.numNBCellRxLevMax.Size = new System.Drawing.Size(70, 22);
            this.numNBCellRxLevMax.TabIndex = 43;
            this.numNBCellRxLevMax.Value = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            // 
            // numNBCellRxLevMin
            // 
            this.numNBCellRxLevMin.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numNBCellRxLevMin.Location = new System.Drawing.Point(573, 43);
            this.numNBCellRxLevMin.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numNBCellRxLevMin.Minimum = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numNBCellRxLevMin.Name = "numNBCellRxLevMin";
            this.numNBCellRxLevMin.Size = new System.Drawing.Size(70, 22);
            this.numNBCellRxLevMin.TabIndex = 42;
            this.numNBCellRxLevMin.Value = new decimal(new int[] {
            92,
            0,
            0,
            -2147483648});
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(650, 47);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(93, 14);
            this.label14.TabIndex = 41;
            this.label14.Text = "≤ 邻小区电平 ≤";
            // 
            // numCellRxLevMax
            // 
            this.numCellRxLevMax.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numCellRxLevMax.Location = new System.Drawing.Point(753, 15);
            this.numCellRxLevMax.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numCellRxLevMax.Minimum = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numCellRxLevMax.Name = "numCellRxLevMax";
            this.numCellRxLevMax.Size = new System.Drawing.Size(70, 22);
            this.numCellRxLevMax.TabIndex = 40;
            this.numCellRxLevMax.Value = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            // 
            // cbxBandType
            // 
            this.cbxBandType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxBandType.FormattingEnabled = true;
            this.cbxBandType.Items.AddRange(new object[] {
            "全部",
            "900-900",
            "900-1800",
            "1800-1800",
            "1800-900"});
            this.cbxBandType.Location = new System.Drawing.Point(444, 99);
            this.cbxBandType.Name = "cbxBandType";
            this.cbxBandType.Size = new System.Drawing.Size(83, 22);
            this.cbxBandType.TabIndex = 39;
            this.cbxBandType.SelectedIndexChanged += new System.EventHandler(this.cbxBandType_SelectedIndexChanged);
            // 
            // lblBandType
            // 
            this.lblBandType.AutoSize = true;
            this.lblBandType.Location = new System.Drawing.Point(371, 103);
            this.lblBandType.Name = "lblBandType";
            this.lblBandType.Size = new System.Drawing.Size(67, 14);
            this.lblBandType.TabIndex = 38;
            this.lblBandType.Text = "小区类别：";
            // 
            // rdbNBigerThenMain
            // 
            this.rdbNBigerThenMain.AutoSize = true;
            this.rdbNBigerThenMain.Location = new System.Drawing.Point(875, 73);
            this.rdbNBigerThenMain.Name = "rdbNBigerThenMain";
            this.rdbNBigerThenMain.Size = new System.Drawing.Size(157, 18);
            this.rdbNBigerThenMain.TabIndex = 37;
            this.rdbNBigerThenMain.TabStop = true;
            this.rdbNBigerThenMain.Text = "计算邻区场强大于主小区";
            this.rdbNBigerThenMain.UseVisualStyleBackColor = true;
            this.rdbNBigerThenMain.CheckedChanged += new System.EventHandler(this.rdbNBigerThenMain_CheckedChanged);
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(1039, 47);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(45, 14);
            this.label13.TabIndex = 36;
            this.label13.Text = "db小区";
            // 
            // numDValueWithTop1
            // 
            this.numDValueWithTop1.Location = new System.Drawing.Point(990, 43);
            this.numDValueWithTop1.Name = "numDValueWithTop1";
            this.numDValueWithTop1.Size = new System.Drawing.Size(42, 22);
            this.numDValueWithTop1.TabIndex = 35;
            this.numDValueWithTop1.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numDValueWithTop1.ValueChanged += new System.EventHandler(this.numDValueWithTop1_ValueChanged);
            // 
            // rdbTopN
            // 
            this.rdbTopN.AutoSize = true;
            this.rdbTopN.Location = new System.Drawing.Point(875, 17);
            this.rdbTopN.Name = "rdbTopN";
            this.rdbTopN.Size = new System.Drawing.Size(61, 18);
            this.rdbTopN.TabIndex = 34;
            this.rdbTopN.TabStop = true;
            this.rdbTopN.Text = "计算前";
            this.rdbTopN.UseVisualStyleBackColor = true;
            this.rdbTopN.CheckedChanged += new System.EventHandler(this.rdbTopN_CheckedChanged);
            // 
            // rdbDValueWithTop1
            // 
            this.rdbDValueWithTop1.AutoSize = true;
            this.rdbDValueWithTop1.Location = new System.Drawing.Point(875, 45);
            this.rdbDValueWithTop1.Name = "rdbDValueWithTop1";
            this.rdbDValueWithTop1.Size = new System.Drawing.Size(109, 18);
            this.rdbDValueWithTop1.TabIndex = 33;
            this.rdbDValueWithTop1.TabStop = true;
            this.rdbDValueWithTop1.Text = "计算与最强相差";
            this.rdbDValueWithTop1.UseVisualStyleBackColor = true;
            this.rdbDValueWithTop1.CheckedChanged += new System.EventHandler(this.rdbDValueWithTop1_CheckedChanged);
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(991, 19);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(43, 14);
            this.label12.TabIndex = 32;
            this.label12.Text = "强小区";
            // 
            // numTopN
            // 
            this.numTopN.Location = new System.Drawing.Point(942, 15);
            this.numTopN.Name = "numTopN";
            this.numTopN.Size = new System.Drawing.Size(42, 22);
            this.numTopN.TabIndex = 31;
            this.numTopN.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numTopN.ValueChanged += new System.EventHandler(this.numTopN_ValueChanged);
            // 
            // numGridCountMin
            // 
            this.numGridCountMin.Location = new System.Drawing.Point(444, 71);
            this.numGridCountMin.Name = "numGridCountMin";
            this.numGridCountMin.Size = new System.Drawing.Size(84, 22);
            this.numGridCountMin.TabIndex = 26;
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numRxLevDValue.Location = new System.Drawing.Point(753, 71);
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Size = new System.Drawing.Size(70, 22);
            this.numRxLevDValue.TabIndex = 25;
            this.numRxLevDValue.Value = new decimal(new int[] {
            15,
            0,
            0,
            0});
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(572, 75);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(175, 14);
            this.label11.TabIndex = 24;
            this.label11.Text = "邻区与最强邻区电平差值上限：";
            // 
            // lblConditionDisappear
            // 
            this.lblConditionDisappear.AutoSize = true;
            this.lblConditionDisappear.Location = new System.Drawing.Point(7, 0);
            this.lblConditionDisappear.Name = "lblConditionDisappear";
            this.lblConditionDisappear.Size = new System.Drawing.Size(55, 14);
            this.lblConditionDisappear.TabIndex = 23;
            this.lblConditionDisappear.TabStop = true;
            this.lblConditionDisappear.Text = "筛选条件";
            this.lblConditionDisappear.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lblConditionDisappear_LinkClicked);
            // 
            // numCellRxLevMin
            // 
            this.numCellRxLevMin.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numCellRxLevMin.Location = new System.Drawing.Point(573, 15);
            this.numCellRxLevMin.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numCellRxLevMin.Minimum = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numCellRxLevMin.Name = "numCellRxLevMin";
            this.numCellRxLevMin.Size = new System.Drawing.Size(70, 22);
            this.numCellRxLevMin.TabIndex = 22;
            this.numCellRxLevMin.Value = new decimal(new int[] {
            92,
            0,
            0,
            -2147483648});
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(650, 19);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(97, 14);
            this.label10.TabIndex = 21;
            this.label10.Text = "≤主服小区电平≤";
            // 
            // numSampleCount
            // 
            this.numSampleCount.Location = new System.Drawing.Point(125, 99);
            this.numSampleCount.Name = "numSampleCount";
            this.numSampleCount.Size = new System.Drawing.Size(75, 22);
            this.numSampleCount.TabIndex = 20;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(16, 103);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(103, 14);
            this.label9.TabIndex = 19;
            this.label9.Text = "邻区采样点下限：";
            // 
            // cbxInDoorCell
            // 
            this.cbxInDoorCell.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxInDoorCell.FormattingEnabled = true;
            this.cbxInDoorCell.Items.AddRange(new object[] {
            "全部",
            "是",
            "否"});
            this.cbxInDoorCell.Location = new System.Drawing.Point(444, 43);
            this.cbxInDoorCell.Name = "cbxInDoorCell";
            this.cbxInDoorCell.Size = new System.Drawing.Size(83, 22);
            this.cbxInDoorCell.TabIndex = 18;
            // 
            // cbxInOrgAngle
            // 
            this.cbxInOrgAngle.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxInOrgAngle.FormattingEnabled = true;
            this.cbxInOrgAngle.Items.AddRange(new object[] {
            "全部",
            "是",
            "否"});
            this.cbxInOrgAngle.Location = new System.Drawing.Point(444, 15);
            this.cbxInOrgAngle.Name = "cbxInOrgAngle";
            this.cbxInOrgAngle.Size = new System.Drawing.Size(83, 22);
            this.cbxInOrgAngle.TabIndex = 17;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(371, 47);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(67, 14);
            this.label8.TabIndex = 16;
            this.label8.Text = "室内小区：";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(371, 19);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(67, 14);
            this.label7.TabIndex = 15;
            this.label7.Text = "在主瓣角：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(210, 75);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(19, 14);
            this.label6.TabIndex = 13;
            this.label6.Text = "至";
            // 
            // comboBoxAngleMax
            // 
            this.comboBoxAngleMax.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxAngleMax.FormattingEnabled = true;
            this.comboBoxAngleMax.Location = new System.Drawing.Point(239, 71);
            this.comboBoxAngleMax.Name = "comboBoxAngleMax";
            this.comboBoxAngleMax.Size = new System.Drawing.Size(74, 22);
            this.comboBoxAngleMax.TabIndex = 12;
            // 
            // comboBoxAngleMin
            // 
            this.comboBoxAngleMin.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxAngleMin.FormattingEnabled = true;
            this.comboBoxAngleMin.Location = new System.Drawing.Point(125, 71);
            this.comboBoxAngleMin.Name = "comboBoxAngleMin";
            this.comboBoxAngleMin.Size = new System.Drawing.Size(74, 22);
            this.comboBoxAngleMin.TabIndex = 11;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(210, 47);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 10;
            this.label5.Text = "至";
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(1001, 97);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(94, 27);
            this.btnOK.TabIndex = 8;
            this.btnOK.Text = "立即筛选";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(52, 19);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(67, 14);
            this.label4.TabIndex = 6;
            this.label4.Text = "邻区状况：";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(52, 47);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 4;
            this.label3.Text = "邻区间距：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(357, 75);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(79, 14);
            this.label2.TabIndex = 2;
            this.label2.Text = "栅格数下限：";
            // 
            // cmbNBCellStatus
            // 
            this.cmbNBCellStatus.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbNBCellStatus.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.cmbNBCellStatus.FormattingEnabled = true;
            this.cmbNBCellStatus.Location = new System.Drawing.Point(125, 15);
            this.cmbNBCellStatus.Name = "cmbNBCellStatus";
            this.cmbNBCellStatus.Size = new System.Drawing.Size(188, 22);
            this.cmbNBCellStatus.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(40, 75);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(79, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "邻区方位角：";
            // 
            // ZTNBCellMissForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1119, 513);
            this.Controls.Add(this.pnlCondition);
            this.Controls.Add(this.groupBox2);
            this.MinimumSize = new System.Drawing.Size(787, 348);
            this.Name = "ZTNBCellMissForm";
            this.Text = "邻区配置核查";
            this.contextMenuStripListView.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.pnlCondition.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numNBDistanceMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNBDistanceMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNBCellRxLevMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNBCellRxLevMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellRxLevMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDValueWithTop1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTopN)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCountMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellRxLevMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripListView;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemPushOut;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpendAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemUnOpenAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemPushOutGridInfo;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.LinkLabel lblConditionVisible;
        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnOrgCell;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCell;
        private BrightIdeasSoftware.OLVColumn olvColumnCellLac;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCI;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellRxLevMean;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellDes;
        private BrightIdeasSoftware.OLVColumn olvColumnGridCount;
        private BrightIdeasSoftware.OLVColumn olvColumnCellDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnAngle;
        private BrightIdeasSoftware.OLVColumn olvColumnIsOrgAngle;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadDesc;
        private BrightIdeasSoftware.OLVColumn olvColumnCellBCCH;
        private BrightIdeasSoftware.OLVColumn olvColumnCellBSIC;
        private System.Windows.Forms.Panel pnlCondition;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.LinkLabel lblConditionDisappear;
        private System.Windows.Forms.NumericUpDown numCellRxLevMin;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.NumericUpDown numSampleCount;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.ComboBox cbxInDoorCell;
        private System.Windows.Forms.ComboBox cbxInOrgAngle;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.ComboBox comboBoxAngleMax;
        private System.Windows.Forms.ComboBox comboBoxAngleMin;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cmbNBCellStatus;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown numRxLevDValue;
        private System.Windows.Forms.NumericUpDown numGridCountMin;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.NumericUpDown numDValueWithTop1;
        private System.Windows.Forms.RadioButton rdbTopN;
        private System.Windows.Forms.RadioButton rdbDValueWithTop1;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.NumericUpDown numTopN;
        private System.Windows.Forms.RadioButton rdbNBigerThenMain;
        private System.Windows.Forms.ComboBox cbxBandType;
        private System.Windows.Forms.Label lblBandType;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcelOneByOne;
        private System.Windows.Forms.NumericUpDown numNBCellRxLevMax;
        private System.Windows.Forms.NumericUpDown numNBCellRxLevMin;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.NumericUpDown numCellRxLevMax;
        private System.Windows.Forms.NumericUpDown numNBDistanceMax;
        private System.Windows.Forms.NumericUpDown numNBDistanceMin;
        private BrightIdeasSoftware.OLVColumn colMainCellRxLevMean;
        private BrightIdeasSoftware.OLVColumn colOneGridPos;
    }
}