﻿using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
    public partial class MapRenderBaseForm : MinCloseForm
    {
        ZTCqtRenderingLayer layer = null;
        MapForm mf = null;
        //用于渲染的数据集合
        List<LayerDataInfo> infos;
        //查询条件
        CqtSettingConditionInfos coverCond = new CqtSettingConditionInfos();
        //CQT文件名
        string fileName = "";
        //是否重新渲染
        bool reRendering = false;

        List<CqtRenderingBaseInfo> cqtRenderingInfo = new List<CqtRenderingBaseInfo>();

        public MapRenderBaseForm()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 加载窗体时加载部分界面数据和条件数据
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="coverCond"></param>
        public void SetCondition(string fileName, CqtSettingConditionInfos coverCond)
        {
            this.fileName = fileName;
            this.coverCond = coverCond;
            cqtRenderingFilterControl.SetInitData(fileName, coverCond.CQTFilterInfos);
            this.dateTimePickerBeginDate.ValueChanged -= new System.EventHandler(this.dateTimePicker_ValueChanged);
            this.dateTimePickerEndDate.ValueChanged -= new System.EventHandler(this.dateTimePicker_ValueChanged);
            dateTimePickerBeginDate.Value = coverCond.Period.BeginTime;
            dateTimePickerEndDate.Value = coverCond.Period.EndTime;
            this.dateTimePickerBeginDate.ValueChanged += new System.EventHandler(this.dateTimePicker_ValueChanged);
            this.dateTimePickerEndDate.ValueChanged += new System.EventHandler(this.dateTimePicker_ValueChanged);
        }

        /// <summary>
        /// 图层跳转到数据所在位置
        /// </summary>
        private void GotoSelectedView(float zoom)
        {
            object imlongitude = gridViewInfo.GetFocusedRowCellValue("RealLongitude");
            object imlatitude = gridViewInfo.GetFocusedRowCellValue("RealLatitude");
            if (imlongitude != null && imlatitude != null && imlongitude.ToString() != "" && imlatitude.ToString() != "")
            {
                double fLong = (double)imlongitude;
                double fLat = (double)imlatitude;
                MainModel.MainForm.GetMapForm().GoToViewWithMapChange(fLong, fLat, zoom);
            }
        }

        private void gridViewInfo_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            //设置选中行
            int focusedhandle = gridViewInfo.FocusedRowHandle;
            LayerDataInfo selectedData = infos[focusedhandle];
            if (selectedData.Longitude == 0 && selectedData.Latitude == 0)
            {
                return;
            }

            layer.SelectedPoints.Clear();
            layer.LastSelectedData.Selected = false;

            selectedData.Selected = true;
            layer.SelectedPoints.Add(selectedData);
            layer.LastSelectedData = selectedData;

            GotoSelectedView(500);
        }

        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            int index = infos.IndexOf(layer.LastSelectedData);
            if (index >= 0)
            {
                gridViewInfo.SelectionChanged -= gridViewInfo_SelectionChanged;
                gridViewInfo.ClearSelection();
                gridViewInfo.FocusedRowHandle = index;
                gridViewInfo.SelectRow(index);
                gridViewInfo.SelectionChanged += gridViewInfo_SelectionChanged;
            }
        }

        private void disposed(object sender, EventArgs e)
        {
            layer.SelectedPointsChanged -= selectedTestPointsChanged;
        }

        /// <summary>
        /// 时间发生变时,重新加载筛选条件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dateTimePicker_ValueChanged(object sender, EventArgs e)
        {
            TimePeriod timePeriod = new TimePeriod(dateTimePickerBeginDate.Value.Date, dateTimePickerEndDate.Value.Date + new TimeSpan(0, 23, 59, 59));

            CqtFilterQuery filterInfo = new CqtFilterQuery(mModel, timePeriod);
            this.MainModel.MainForm.ExcuteBarItemTask(filterInfo, null);

            cqtRenderingFilterControl.SetInitData(fileName, filterInfo.CQTFilterInfos);
        }

        /// <summary>
        /// 重新查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOK_Click(object sender, EventArgs e)
        {
            setCondition(ref coverCond);

            //根据条件查询
            switch (cmbBoxStyle.SelectedIndex)
            {
                case 1:
                    DiyMapRenderByDateFloorQuery queryByDateFloor = new DiyMapRenderByDateFloorQuery(mModel, coverCond, this);
                    queryByDateFloor.Query();
                    break;
                case 2:
                    DiyMapRenderByDateFloorTacEciQuery queryByDateFloorTACECI = new DiyMapRenderByDateFloorTacEciQuery(mModel, coverCond, this);
                    queryByDateFloorTACECI.Query();
                    break;
                case 3:
                    DiyMapRenderByDateFloorAntennaQuery queryByDateAntennaFloor = new DiyMapRenderByDateFloorAntennaQuery(mModel, coverCond, this);
                    queryByDateAntennaFloor.Query();
                    break;
                default:
                    DiyMapRenderBaseQuery query = new DiyMapRenderBaseQuery(mModel, coverCond, this);
                    query.Query();
                    break;
            }

            reRendering = true;
        }

        /// <summary>
        /// 查询前设置查询条件
        /// </summary>
        /// <param name="info"></param>
        public void setCondition(ref CqtSettingConditionInfos coverCond)
        {
            //渲染类别
            coverCond.RenderingMode = cmbBoxMode.SelectedIndex;
            //筛选条件
            string filter = cqtRenderingFilterControl.GetData();
            if (filter != "")
            {
                coverCond.RenderingFilter = new List<string>(filter.Split(','));
            }
            //指标
            coverCond.RenderingIndex = cmbBoxIndex.SelectedItem.ToString();
            //时间
            coverCond.Period = new TimePeriod(dateTimePickerBeginDate.Value.Date, dateTimePickerEndDate.Value.Date + new TimeSpan(0, 23, 59, 59));
        }

        public void FillData(List<CqtRenderingBaseInfo> cqtRenderingInfo, List<LayerDataInfo> infos)
        {
            this.infos = infos;
            this.cqtRenderingInfo = cqtRenderingInfo;
            init();

            mf = MainModel.MainForm.GetMapForm();
            //设置图层
            MasterCom.MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(ZTCqtRenderingLayer));
            layer = clayer as ZTCqtRenderingLayer;
            layer.SerialInfoName = coverCond.RenderingIndex;
            layer.DataInfos = infos;
            layer.SelectedPointsChanged += selectedTestPointsChanged;
            Disposed += disposed;

            MainModel.FireSetDefaultMapSerialTheme(coverCond.RenderingIndex);

            //填充数据
            switch (cmbBoxStyle.SelectedIndex)
            { 
                case 1:
                    List<CqtRenderByDateFloorInfo> datas = new List<CqtRenderByDateFloorInfo>();
                    foreach (CqtRenderingBaseInfo data in cqtRenderingInfo)
                    {
                        datas.Add((CqtRenderByDateFloorInfo)data);
                    }
                    gridControlInfo.DataSource = datas;
                    break;
                case 2:
                    List<CqtRenderByTacEciInfo> dataByDateFloor = new List<CqtRenderByTacEciInfo>();
                    foreach (CqtRenderingBaseInfo data in cqtRenderingInfo)
                    {
                        dataByDateFloor.Add((CqtRenderByTacEciInfo)data);
                    }
                    gridControlInfo.DataSource = dataByDateFloor;
                    break;
                case 3:
                    List<CqtRenderByAntennaInfo> dataByAntenna = new List<CqtRenderByAntennaInfo>();
                    foreach (CqtRenderingBaseInfo data in cqtRenderingInfo)
                    {
                        dataByAntenna.Add((CqtRenderByAntennaInfo)data);
                    }
                    gridControlInfo.DataSource = dataByAntenna;
                    break;
                default:
                    gridControlInfo.DataSource = cqtRenderingInfo;
                    break;
            }
            gridControlInfo.RefreshDataSource();

            GotoSelectedView(1000);
        }

        /// <summary>
        /// 根据查询条件初始化结果列表
        /// </summary>
        /// <param name="info"></param>
        private void init()
        {
            Dictionary<string, string> colDic = new Dictionary<string, string>();

            switch (cmbBoxStyle.SelectedIndex)
            {
                case 1:
                    colDic.Add("Time", "时间");
                    colDic.Add("FloorName", "楼层名");
                    colDic.Add("Point_x", "打点经度");
                    colDic.Add("Point_y", "打点纬度");
                    colDic.Add("SampleNum", "采样点数");
                    colDic.Add("RSRPAvg", "平均RSRP");
                    colDic.Add("SINRAvg", "平均SINR");
                    break;
                case 2:
                    colDic.Add("Time", "时间");
                    colDic.Add("FloorName", "楼层名");
                    colDic.Add("Point_x", "打点经度");
                    colDic.Add("Point_y", "打点纬度");
                    colDic.Add("TAC", "TAC");
                    colDic.Add("ECI", "ECI");
                    colDic.Add("SampleNum", "采样点数");
                    colDic.Add("RSRPAvg", "平均RSRP");
                    colDic.Add("SINRAvg", "平均SINR");
                    break;
                case 3:
                    colDic.Add("Time", "时间");
                    colDic.Add("FloorName", "楼层名");
                    colDic.Add("MAC", "MAC");
                    colDic.Add("MACCount", "MAC数");
                    colDic.Add("Point_x", "打点经度");
                    colDic.Add("Point_y", "打点纬度");
                    colDic.Add("RSRPAvg", "平均RSRP");
                    colDic.Add("SINRAvg", "平均SINR");
                    break;
                default:
                    colDic.Add("Time", "时间");
                    colDic.Add("MAC", "MAC");
                    colDic.Add("RSSI", "场强");
                    colDic.Add("Mac_x", "蓝牙经度");
                    colDic.Add("Mac_y", "蓝牙纬度");
                    colDic.Add("FloorName", "楼层名");
                    colDic.Add("Point_x", "打点经度");
                    colDic.Add("Point_y", "打点纬度");
                    colDic.Add("Loc_x", "定位经度");
                    colDic.Add("Loc_y", "定位纬度");
                    colDic.Add("TAC", "TAC");
                    colDic.Add("ECI", "ECI");
                    colDic.Add("RSRP", "RSRP");
                    colDic.Add("SINR", "SINR");
                    colDic.Add("FolderName", "文件夹名");
                    break;
            }

            int i = 0;
            gridViewInfo.Columns.Clear();
            foreach (string item in colDic.Keys)
            {
                gridViewInfo.Columns.Add(new GridColumn() { FieldName = item, Caption = colDic[item], VisibleIndex = i++ });
            }
        }

        private void cmbBoxIndex_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!reRendering && (infos == null || infos.Count == 0))
            {
                return;
            }

            layer.SerialInfoName = cmbBoxIndex.SelectedItem.ToString();
            for (int i = 0; i < infos.Count; i++)
            {
                inderRendering(cqtRenderingInfo[i], infos[i]);
            }

            layer.DataInfos = infos;
            MainModel.FireSetDefaultMapSerialTheme(cmbBoxIndex.SelectedItem.ToString());
            GotoSelectedView(1000);
        }

        private void cmbBoxMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!reRendering && (infos == null || infos.Count == 0))
            {
                return;
            }

            for (int i = 0; i < infos.Count; i++)
            {
                modeRendering(cqtRenderingInfo[i], infos[i]);
            }

            layer.DataInfos = infos;
            GotoSelectedView(1000);
        }

        /// <summary>
        /// 类别渲染
        /// </summary>
        /// <param name="data"></param>
        /// <param name="info"></param>
        private void modeRendering(CqtRenderingBaseInfo data, LayerDataInfo info)
        {
            if (cmbBoxMode.SelectedIndex == 0)
            {
                //打点位置
                data.RealLongitude = data.Point_x;
                data.RealLatitude = data.Point_y;
            }
            else
            {
                //蓝牙信息
                data.RealLongitude = data.Loc_x;
                data.RealLatitude = data.Loc_y;
            }
            if (data.RealLongitude != 0 && data.RealLatitude != 0)
            {
                double realLongitude = 0;
                double realLatitude = 0;
                calculateLongiLati(coverCond.LTLongitude, coverCond.LTLatitude, data.RealLongitude, data.RealLatitude,
                            ref realLongitude, ref realLatitude, coverCond.Scale);
                info.Longitude = realLongitude;
                info.Latitude = realLatitude;
                data.RealLongitude = realLongitude;
                data.RealLatitude = realLatitude;
            }
            else
            {
                info.Longitude = 0;
                info.Latitude = 0;
            }
        }

        /// <summary>
        /// 指标渲染
        /// </summary>
        /// <param name="data"></param>
        /// <param name="info"></param>
        private void inderRendering(CqtRenderingBaseInfo data, LayerDataInfo info)
        {
            switch (cmbBoxStyle.SelectedIndex)
            {
                case 1:
                    if (cmbBoxIndex.SelectedItem.ToString() == "LTE_TDD:RSRP")
                    {
                        info.RenderingIndex = ((CqtRenderByDateFloorInfo)data).RSRPAvg;
                    }
                    else if (cmbBoxIndex.SelectedItem.ToString() == "LTE_TDD:SINR")
                    {
                        info.RenderingIndex = ((CqtRenderByDateFloorInfo)data).SINRAvg;
                    }
                    break;
                case 2:
                    if (cmbBoxIndex.SelectedItem.ToString() == "LTE_TDD:RSRP")
                    {
                        info.RenderingIndex = ((CqtRenderByTacEciInfo)data).RSRPAvg;
                    }
                    else if (cmbBoxIndex.SelectedItem.ToString() == "LTE_TDD:SINR")
                    {
                        info.RenderingIndex = ((CqtRenderByTacEciInfo)data).SINRAvg;
                    }
                    break;
                case 3:
                    if (cmbBoxIndex.SelectedItem.ToString() == "LTE_TDD:RSRP")
                    {
                        info.RenderingIndex = ((CqtRenderByAntennaInfo)data).RSRPAvg;
                    }
                    else if (cmbBoxIndex.SelectedItem.ToString() == "LTE_TDD:SINR")
                    {
                        info.RenderingIndex = ((CqtRenderByAntennaInfo)data).SINRAvg;
                    }
                    break;
                default:
                    if (cmbBoxIndex.SelectedItem.ToString() == "LTE_TDD:RSRP")
                    {
                        info.RenderingIndex = data.RSRP;
                    }
                    else if (cmbBoxIndex.SelectedItem.ToString() == "LTE_TDD:SINR")
                    {
                        info.RenderingIndex = data.SINR;
                    }
                    else if (cmbBoxIndex.SelectedItem.ToString() == "LTE_TDD:RSSI")
                    {
                        info.RenderingIndex = data.RSSI;
                    }
                    break;
            }
        }

        /// <summary>
        /// 将获取的数据进行处理赋给结果集
        /// </summary>
        public void DealDataAfterQuery(List<CqtRenderingBaseInfo> cqtRenderingInfos)
        {
            List<LayerDataInfo> infoList = new List<LayerDataInfo>();
            Dictionary<string, System.Collections.Hashtable> dicLongLat = new Dictionary<string, System.Collections.Hashtable>();
            Random randomNum = new Random();

            foreach (CqtRenderingBaseInfo data in cqtRenderingInfos)
            {
                LayerDataInfo info = new LayerDataInfo();

                //渲染类别
                modeRendering(data, info);

                //渲染指标
                inderRendering(data, info);

                info.Selected = data.Selected;

                if (info.Longitude != 0 && info.Latitude != 0)
                {
                    setDisplayLongLat(dicLongLat, randomNum, info);
                }

                infoList.Add(info);
            }

            FillData(cqtRenderingInfos, infoList);
        }

        private static void setDisplayLongLat(Dictionary<string, System.Collections.Hashtable> dicLongLat, Random randomNum, LayerDataInfo info)
        {
            string dataLongLat = info.Longitude.ToString() + "_" + info.Latitude.ToString();
            if (!dicLongLat.ContainsKey(dataLongLat))
            {
                System.Collections.Hashtable hashtable = new System.Collections.Hashtable();
                dicLongLat[dataLongLat] = hashtable;
                info.DisplayLongitude = info.Longitude;
                info.DisplayLatitude = info.Latitude;
            }
            else
            {
                for (int i = 0; i < 10; i++)
                {
                    double longitudeOffset = randomNum.Next(-5, 5) / 1000000.0;
                    double latitudeOffset = randomNum.Next(-5, 5) / 1000000.0;
                    if (!dicLongLat[dataLongLat].ContainsKey(longitudeOffset) && !dicLongLat[dataLongLat].ContainsValue(latitudeOffset))
                    {
                        info.DisplayLongitude = info.Longitude + longitudeOffset;
                        info.DisplayLatitude = info.Latitude + latitudeOffset;
                        dicLongLat[dataLongLat].Add(longitudeOffset, latitudeOffset);
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 根据左上角经纬度和采样点的偏移量计算出采样点经纬度
        /// </summary>
        /// <param name="ltlongitude">左上角经度</param>
        /// <param name="ltlatitude">左上角纬度</param>
        /// <param name="longitudeOffset">采样点经度偏移量</param>
        /// <param name="latitudeOffset">采样点纬度偏移量</param>
        /// <param name="tplongitude">返回采样点经度</param>
        /// <param name="tplatitude">返回采样点纬度</param>
        private void calculateLongiLati(double ltlongitude, double ltlatitude, double longitudeOffset, double latitudeOffset,
            ref double tplongitude, ref double tpltlatitude, double scale)
        {
            //左上角点
            DbPoint ltDpt = new DbPoint(ltlongitude, ltlatitude);
            //实际显示左上角点
            PointF displayLTPt;
            mModel.MainForm.GetMapForm().GetMapOperation().ToDisplay(ltDpt, out displayLTPt);
            //放大缩小比例
            double ratio = scale / mModel.MainForm.GetMapForm().GetMapOperation().Scale;
            float widthRatio = 1.2585F;//CQT图层显示时会将经纬度放大此系数,所以这里转换时也放大
            float tpX = (float)(displayLTPt.X + (ratio * longitudeOffset * widthRatio));
            float tpY = (float)(displayLTPt.Y + (ratio * latitudeOffset));
            PointF realDisplayTPpt = new PointF(tpX, tpY);
            DbPoint tpPt;
            mModel.MainForm.GetMapForm().GetMapOperation().FromDisplay(realDisplayTPpt, out tpPt);
            tplongitude = tpPt.x;
            tpltlatitude = tpPt.y;
        }

        private void cmbBoxStyle_SelectedIndexChanged(object sender, EventArgs e)
        {
            //渲染方式改变后需要重新查询才能渲染
            reRendering = false;
            clearDatas();
            switch (cmbBoxStyle.SelectedIndex)
            {
                case 1:
                case 2:
                case 3:
                    cmbBoxIndex.Items.Clear();
                    cmbBoxIndex.Items.Add("LTE_TDD:RSRP");
                    cmbBoxIndex.Items.Add("LTE_TDD:SINR");
                    cmbBoxIndex.SelectedIndex = 0;
                    cmbBoxMode.Items.Clear();
                    cmbBoxMode.Items.Add("按打点位置信息");
                    cmbBoxMode.SelectedIndex = 0;
                    break;
                default:
                    cmbBoxIndex.Items.Clear();
                    cmbBoxIndex.Items.Add("LTE_TDD:RSRP");
                    cmbBoxIndex.Items.Add("LTE_TDD:SINR");
                    cmbBoxIndex.Items.Add("LTE_TDD:RSSI");
                    cmbBoxIndex.SelectedIndex = 0;
                    cmbBoxMode.Items.Clear();
                    cmbBoxMode.Items.Add("按打点位置信息");
                    cmbBoxMode.Items.Add("按蓝牙定位信息");
                    cmbBoxMode.SelectedIndex = 0;
                    break;
            }
        }

        private void clearDatas()
        {
            infos = null;
            cqtRenderingInfo = null;
            gridControlInfo.DataSource = null;
            gridViewInfo.Columns.Clear();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            List<List<object>> exportList = GridViewTransfer.Transfer(this.gridControlInfo);
            ExcelNPOIManager.ExportToExcel(exportList);
        }

        private void dateTimePickerBeginDate_Enter(object sender, EventArgs e)
        {
            btnOK.Enabled = false;
        }

        private void dateTimePickerBeginDate_Leave(object sender, EventArgs e)
        {
            btnOK.Enabled = true;
        }

        private void dateTimePickerEndDate_Enter(object sender, EventArgs e)
        {
            btnOK.Enabled = false;
        }

        private void dateTimePickerEndDate_Leave(object sender, EventArgs e)
        {
            btnOK.Enabled = true;
        }
    }

    public class CqtSettingConditionInfos
    {
        //0为按打点位置,1为按蓝牙定位
        public int RenderingMode { get; set; } = 0;
        //筛选条件
        public List<string> RenderingFilter { get; set; } = new List<string>();

        public List<CqtFilterInfo> CQTFilterInfos { get; set; }
        //渲染指标
        public string RenderingIndex { get; set; } = "";
        //时间
        public TimePeriod Period { get; set; } = new TimePeriod();

        public double LTLongitude { get; set; } = 0;
        public double LTLatitude { get; set; } = 0;
        public double Scale { get; set; } = 0;
    }
}
