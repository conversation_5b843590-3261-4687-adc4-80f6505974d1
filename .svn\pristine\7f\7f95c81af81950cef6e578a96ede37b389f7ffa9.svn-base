﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45GHighReverseFlowCoverage
{
    public class DiyQuerySerialNumber : DIYSQLBase
    {
        public Dictionary<string, SerialNumber> InfoDic { get; protected set; }
        public int MaxSerialNumber { get; protected set; } = 1;

        public static string TableName { get; } = "tb_high_reverse_flow_coverage_number";

        public DiyQuerySerialNumber()
            : base()
        {
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            string sql = $"SELECT [id],[city],[cityid],[enodebid],[serialnumber],[longitude],[latitude] " +
                $"FROM {TableName}";

            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[7];
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx] = E_VType.E_Int;
            return rType;
        }

        protected override bool isValidCondition()
        {
            InfoDic = new Dictionary<string, SerialNumber>();
            return true;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    dealReceiveData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        protected virtual void dealReceiveData(Package package)
        {
            SerialNumber info = new SerialNumber();
            info.FillDataBySQL(package);
            InfoDic.Add(info.Key, info);
            MaxSerialNumber = Math.Max(MaxSerialNumber, info.ID);
        }
    }
}
