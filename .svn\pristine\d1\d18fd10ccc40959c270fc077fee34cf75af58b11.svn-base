﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEHandoverSerialByRegion : DIYAnalyseByFileBackgroundBase
    {
        public LTEHandoverSerialByRegion(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "LTE切换序列(按区域)"; }
        }

        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22031, this.Name);
        }

        protected override bool getCondition()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            return true;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<LTEHandoverSerialGroupView>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                LTEHandoverSerialStater stater = new LTEHandoverSerialStater(file);
                resultList.AddRange(stater.DoStat());
            }
        }
        protected override void getResultsAfterQuery()
        {
            int i = 0;
            foreach (LTEHandoverSerialGroupView handView in resultList)
            {
                i++;
                handView.SN = i;
            }
        }

        protected override void fireShowForm()
        {
            LTEHandoverSerialResultForm resultForm = MainModel.GetObjectFromBlackboard(typeof(LTEHandoverSerialResultForm).FullName) as LTEHandoverSerialResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new LTEHandoverSerialResultForm(MainModel);
            }
            resultForm.FillData(resultList);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }

        private List<LTEHandoverSerialGroupView> resultList;
    }

    public class LTEHandoverSerialByFile : LTEHandoverSerialByRegion
    {
        public LTEHandoverSerialByFile(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "LTE切换序列(按文件)"; }
        }

        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22031, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }
        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class LTEHandoverSerialByRegion_FDD : LTEHandoverSerialByRegion
    {
        public LTEHandoverSerialByRegion_FDD(MainModel mainModel)
            : base(mainModel)
        {
            carrierID = CarrierType.ChinaUnicom;
        }
        public override string Name
        {
            get { return "LTE_FDD切换序列(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26046, this.Name);
        }

        protected override bool getCondition()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            return true;
        }
    }

    public class LTEHandoverSerialByFile_FDD : LTEHandoverSerialByRegion_FDD
    {
        public LTEHandoverSerialByFile_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "LTE_FDD切换序列(按文件)"; }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }
        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
