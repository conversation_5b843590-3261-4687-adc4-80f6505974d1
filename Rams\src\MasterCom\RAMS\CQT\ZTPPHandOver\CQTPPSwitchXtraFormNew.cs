﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTPPSwitchXtraFormNew : DevExpress.XtraEditors.XtraForm
    {
        public CQTPPSwitchXtraFormNew(MainModel Mainmodel, string netWoker)
        {
            InitializeComponent();
            mainmodel = Mainmodel;
            if (netWoker.Equals("GSM"))
                this.Text = "GSM乒乓切换事件分析";
            else if (netWoker.Equals("TD"))
                this.Text = "TD乒乓切换事件分析";
        }
        MainModel mainmodel = null;
        List<HandOverItemCQT> handoverResult = new List<HandOverItemCQT>();
        public void setData(List<HandOverItemCQT> result, Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>> cqtResult)
        {
            handoverResult = result;
            List<HoSubInfoCQT> resultNew = new List<HoSubInfoCQT>();
            foreach (Dictionary<string, HoSubInfoCQT> item in cqtResult.Values)
            {
                foreach (string item2 in item.Keys)
                {
                    int tepmnum = item[item2].StrHandOver.Split('>').Length - 1;
                    item[item2].StrHandOverKey = item2;
                    item[item2].IResult = Math.Round((double)(item[item2].Isecond / (item[item2].Itime * tepmnum)), 2, MidpointRounding.AwayFromZero);

                    resultNew.Add(item[item2]);
                }
            }
            this.gridControl1.DataSource = resultNew;
        }
        //刷新数据
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            DIYQueryCQTPPHandOverAna dqpha = new DIYQueryCQTPPHandOverAna(mainmodel, "");
            Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>> cqtResult
                = dqpha.getHo2FreqList(handoverResult, Convert.ToInt32(spSecond.Value), Convert.ToInt32(spTurn.Value), Convert.ToInt32(spTime.Value));
            List<HoSubInfoCQT> resultNew = new List<HoSubInfoCQT>();
            foreach (Dictionary<string, HoSubInfoCQT> item in cqtResult.Values)
            {
                foreach (string item2 in item.Keys)
                {
                    int tepmnum = item[item2].StrHandOver.Split('>').Length - 1;
                    item[item2].StrHandOverKey = item2;
                    item[item2].IResult = Math.Round((double)(item[item2].Isecond / (item[item2].Itime * tepmnum)), 2, MidpointRounding.AwayFromZero);

                    resultNew.Add(item[item2]);
                }
            }
            this.gridControl1.DataSource = resultNew;
            this.gridControl1.RefreshDataSource();
        }
        //选中行,获取详细切换信息
        private void gridView1_Click(object sender, EventArgs e)
        {
            int[] rows = gridView1.GetSelectedRows();
            if (rows.Length == 0)
                return;
            object o = gridView1.GetRow(rows[0]);
            HoSubInfoCQT evaluate = o as HoSubInfoCQT;

            if (evaluate != null)
            {
                textBox2.Text = evaluate.StrHandOver;
            }
        }
        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }
    }
}