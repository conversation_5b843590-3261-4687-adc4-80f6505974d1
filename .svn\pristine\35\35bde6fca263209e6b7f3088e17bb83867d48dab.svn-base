﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTUltraSite
{
    public partial class SettingDlg : BaseDialog
    {
        public SettingDlg()
        {
            InitializeComponent();
        }

        public void SetCondition(UltraSiteCondition cond)
        {
            if (cond==null)
            {
                return;
            }
            numSiteDistance.Value = (decimal)cond.NearDistanceMin;
            numAltitude.Value = (decimal)cond.AltitudeMax;
            numDirRange.Value = (decimal)cond.AvgSitesDistanceAngle;
            numAvgDistance.Value = (decimal)cond.AvgSiteDistanceMax;
            numDiffBandDis.Value = (decimal)cond.DiffBandDistanceMin;
        }

        public UltraSiteCondition GetCondition()
        {
            UltraSiteCondition cond = new UltraSiteCondition();
            cond.NearDistanceMin = (double)numSiteDistance.Value;
            cond.AltitudeMax = (double)numAltitude.Value;
            cond.AvgSitesDistanceAngle = (int)numDirRange.Value;
            cond.AvgSiteDistanceMax = (double)numAvgDistance.Value;
            cond.DiffBandDistanceMin = (double)numDiffBandDis.Value;
            return cond;
        }

    }
}
