﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTTDFailuresQueryForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTTDFailuresQueryForm()
        {
            InitializeComponent();
        }

        public void getSelect(out  double numgao, out int numchixu)
        {
            numgao = double.Parse(spinEdit2.Value.ToString());
            numchixu = Convert.ToInt32(spinEdit3.Value);
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

       
    }
}