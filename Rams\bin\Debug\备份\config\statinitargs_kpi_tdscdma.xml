<?xml version="1.0" encoding="UTF-8"?>
<Configs>
	<Config name="StatParamCfg">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">TD-SCDMA参数</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD-SCDMA语音业务参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">T网占用时长 tdaDurationTD</Item>
								<Item typeName="String" key="FName">tdaDurationTD</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">G网占用时长 tdaDurationGSM</Item>
								<Item typeName="String" key="FName">tdaDurationGSM</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">T网测试距离 tdaDistanceTD</Item>
								<Item typeName="String" key="FName">tdaDistanceTD</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">G网测试距离 tdaDistanceGSM</Item>
								<Item typeName="String" key="FName">tdaDistanceGSM</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER采样点数 tdaBler_SampleNum</Item>
								<Item typeName="String" key="FName">tdaBler_SampleNum</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER总数 tdaBler_Total</Item>
								<Item typeName="String" key="FName">tdaBler_Total</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER错误总数 tdaBler_Err</Item>
								<Item typeName="String" key="FName">tdaBler_Err</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PCCPCH采样点数 tdaPCCPCH_RSCPCI_SampleNum</Item>
								<Item typeName="String" key="FName">tdaPCCPCH_RSCPCI_SampleNum</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PCCPCH RSCP>-95 C/I>-3数量 tdaPCCPCH_RSCP_CI_95Sample</Item>
								<Item typeName="String" key="FName">tdaPCCPCH_RSCP_CI_95Sample</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PCCPCH RSCP>-90 C/I>-3数量 tdaPCCPCH_RSCP_CI_90Sample</Item>
								<Item typeName="String" key="FName">tdaPCCPCH_RSCP_CI_90Sample</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">DPCH_C/I采样点数 tdaDPCH_C2I_SampleNum</Item>
								<Item typeName="String" key="FName">tdaDPCH_C2I_SampleNum</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">DPCH C/I>-3采样点数 tdaDPCH_C2I_F3_Sample</Item>
								<Item typeName="String" key="FName">tdaDPCH_C2I_F3_Sample</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Rxlev采样点数 tdaRxlev_SampleNum</Item>
								<Item typeName="String" key="FName">tdaRxlev_SampleNum</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxLev[-10~-75] tdaRxlev_F75</Item>
								<Item typeName="String" key="FName">tdaRxlev_F75</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxLev(-75,-80]  tdaRxlev_F80</Item>
								<Item typeName="String" key="FName">tdaRxlev_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxLev(-80,-85] tdaRxlev_F85</Item>
								<Item typeName="String" key="FName">tdaRxlev_F85</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxLev(-85,-90] tdaRxlev_F90</Item>
								<Item typeName="String" key="FName">tdaRxlev_F90</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxLev(-90,-94] tdaRxlev_F94</Item>
								<Item typeName="String" key="FName">tdaRxlev_F94</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxLev(-94,-100] tdaRxlev_F100</Item>
								<Item typeName="String" key="FName">tdaRxlev_F100</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS采样点数 tdaMOS_Sample</Item>
								<Item typeName="String" key="FName">tdaMOS_Sample</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS值总量  tdaMOS_Total</Item>
								<Item typeName="String" key="FName">tdaMOS_Total</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD-SCDMA数据业务参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD测试采样点数</Item>
								<Item typeName="String" key="FName">gSampleTotleTD</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">总测试时长</Item>
								<Item typeName="String" key="FName">gDuration</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD测试时长</Item>
								<Item typeName="String" key="FName">gDurationTD</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">2G GPRS测试时长</Item>
								<Item typeName="String" key="FName">gDuration_GPRS</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">2G EDGE测试时长</Item>
								<Item typeName="String" key="FName">gDuration_EDGE</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">总测试距离</Item>
								<Item typeName="String" key="FName">gDistance</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD测试距离</Item>
								<Item typeName="String" key="FName">gDistanceTD</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">2G GPRS测试距离</Item>
								<Item typeName="String" key="FName">gDistance_GPRS</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">2G EDGE测试距离</Item>
								<Item typeName="String" key="FName">gDistance_EDGE</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Rxlev</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_02090101</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_10_45</Item>
										<Item typeName="String" key="FName">Gx_02090102</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_46_50</Item>
										<Item typeName="String" key="FName">Gx_02090103</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_51_55</Item>
										<Item typeName="String" key="FName">Gx_02090104</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_56_60</Item>
										<Item typeName="String" key="FName">Gx_02090105</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_61_65</Item>
										<Item typeName="String" key="FName">Gx_02090106</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_66_70</Item>
										<Item typeName="String" key="FName">Gx_02090107</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_71_75</Item>
										<Item typeName="String" key="FName">Gx_02090108</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_76_80</Item>
										<Item typeName="String" key="FName">Gx_02090109</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_81</Item>
										<Item typeName="String" key="FName">Gx_0209010A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_82</Item>
										<Item typeName="String" key="FName">Gx_0209010B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_83</Item>
										<Item typeName="String" key="FName">Gx_0209010C</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_84</Item>
										<Item typeName="String" key="FName">Gx_0209010D</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_85</Item>
										<Item typeName="String" key="FName">Gx_0209010E</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_86</Item>
										<Item typeName="String" key="FName">Gx_0209010F</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_87</Item>
										<Item typeName="String" key="FName">Gx_02090110</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_88</Item>
										<Item typeName="String" key="FName">Gx_02090111</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_89</Item>
										<Item typeName="String" key="FName">Gx_02090112</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_90</Item>
										<Item typeName="String" key="FName">Gx_02090113</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_91</Item>
										<Item typeName="String" key="FName">Gx_02090114</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_92</Item>
										<Item typeName="String" key="FName">Gx_02090115</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_93</Item>
										<Item typeName="String" key="FName">Gx_02090116</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_94</Item>
										<Item typeName="String" key="FName">Gx_02090117</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_95</Item>
										<Item typeName="String" key="FName">Gx_02090118</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_96</Item>
										<Item typeName="String" key="FName">Gx_02090119</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_97</Item>
										<Item typeName="String" key="FName">Gx_0209011A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_98</Item>
										<Item typeName="String" key="FName">Gx_0209011B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_MAX</Item>
										<Item typeName="String" key="FName">Gx_0209011C</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GSM_RSCP_MIN</Item>
										<Item typeName="String" key="FName">Gx_0209011D</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_02090301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_10_45</Item>
										<Item typeName="String" key="FName">Gx_02090302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_46_50</Item>
										<Item typeName="String" key="FName">Gx_02090303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_51_55</Item>
										<Item typeName="String" key="FName">Gx_02090304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_56_60</Item>
										<Item typeName="String" key="FName">Gx_02090305</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_61_65</Item>
										<Item typeName="String" key="FName">Gx_02090306</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_66_70</Item>
										<Item typeName="String" key="FName">Gx_02090307</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_71_75</Item>
										<Item typeName="String" key="FName">Gx_02090308</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_76_80</Item>
										<Item typeName="String" key="FName">Gx_02090309</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_81</Item>
										<Item typeName="String" key="FName">Gx_0209030A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_82</Item>
										<Item typeName="String" key="FName">Gx_0209030B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_83</Item>
										<Item typeName="String" key="FName">Gx_0209030C</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_84</Item>
										<Item typeName="String" key="FName">Gx_0209030D</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_85</Item>
										<Item typeName="String" key="FName">Gx_0209030E</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_86</Item>
										<Item typeName="String" key="FName">Gx_0209030F</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_87</Item>
										<Item typeName="String" key="FName">Gx_02090310</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_88</Item>
										<Item typeName="String" key="FName">Gx_02090311</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_89</Item>
										<Item typeName="String" key="FName">Gx_02090312</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_90</Item>
										<Item typeName="String" key="FName">Gx_02090313</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_91</Item>
										<Item typeName="String" key="FName">Gx_02090314</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_92</Item>
										<Item typeName="String" key="FName">Gx_02090315</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_93</Item>
										<Item typeName="String" key="FName">Gx_02090316</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_94</Item>
										<Item typeName="String" key="FName">Gx_02090317</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_95</Item>
										<Item typeName="String" key="FName">Gx_02090318</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_96</Item>
										<Item typeName="String" key="FName">Gx_02090319</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_97</Item>
										<Item typeName="String" key="FName">Gx_0209031A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_98</Item>
										<Item typeName="String" key="FName">Gx_0209031B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_MAX</Item>
										<Item typeName="String" key="FName">Gx_0209031C</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_TDSCDMA_RSCP_MIN</Item>
										<Item typeName="String" key="FName">Gx_0209031D</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RLC</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS384_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090101</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS384_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090102</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS384_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090103</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS384_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090104</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS128_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS128_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS128_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS128_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS64_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS64_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS64_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS64_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GPRS_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090401</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GPRS_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090402</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GPRS_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090403</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GPRS_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090404</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_HSPA_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090501</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_HSPA_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090502</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_HSPA_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090503</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_HSPA_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090504</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090801</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090802</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03090803</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03090804</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS384_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_04090101</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS384_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_04090102</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS384_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_04090103</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS384_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_04090104</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS128_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_04090201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS128_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_04090202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS128_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_04090203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS128_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_04090204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS64_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_04090301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS64_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_04090302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS64_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_04090303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_PS64_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_04090304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GPRS_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_04090401</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GPRS_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_04090402</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GPRS_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_04090403</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_GPRS_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_04090404</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_HSPA_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_04090501</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_HSPA_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_04090502</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_HSPA_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_04090503</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_HSPA_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_04090504</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_04090801</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_04090802</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_04090803</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_04090804</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">APP</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901010201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901010202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901010203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901010204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901010601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901010602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901010603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901010604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901010C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901010C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901010C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901010C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901020201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901020202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901020203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901020204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901020601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901020602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901020603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901020604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901020C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901020C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901020C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901020C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901030201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901030202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901030203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901030204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901030601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901030602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901030603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901030604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901030C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901030C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901030C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901030C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901040601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901040602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901040603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901040604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901040C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901040C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901040C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901040C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901040201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901040202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901040203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901040204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901050201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901050202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901050203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901050204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901050601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901050602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901050603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901050604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901050C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901050C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901050C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901050C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901080201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901080202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901080203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901080204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901080601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901080602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901080603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901080604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050901080C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050901080C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050901080C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_DOWNLOAD_EDGE_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050901080C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902010301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902010302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902010303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902010304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902010701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902010702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902010703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902010704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902010D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902010D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902010D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS384_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902010D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902020301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902020302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902020303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902020304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902020701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902020702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902020703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902020704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902020D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902020D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902020D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS128_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902020D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902030301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902030302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902030303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902030304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902030701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902030702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902030703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902030704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902030D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902030D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902030D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_PS64_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902030D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902040301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902040302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902040303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902040304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902040701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902040702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902040703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902040704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902040D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902040D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902040D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_GPRS_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902040D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902050301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902050302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902050303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902050304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902050701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902050702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902050703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902050704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902050D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902050D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902050D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_HSPA_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902050D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902080301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902080302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902080303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902080304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902080701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902080702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902080703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902080704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050902080D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050902080D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050902080D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TD_EDGE_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050902080D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CQI</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Sample 采样点个数</Item>
										<Item typeName="String" key="FName">Gx_5C090101</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Total 总值</Item>
										<Item typeName="String" key="FName">Gx_5C090102</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Mid_Total 中值</Item>
										<Item typeName="String" key="FName">Gx_5C090103</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MCS</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_FTP_Download_MCS1包数量</Item>
										<Item typeName="String" key="FName">Gx_0709080201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_FTP_Download_MCS2包数量</Item>
										<Item typeName="String" key="FName">Gx_0709080202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_FTP_Download_MCS3包数量</Item>
										<Item typeName="String" key="FName">Gx_0709080203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_FTP_Download_MCS4包数量</Item>
										<Item typeName="String" key="FName">Gx_0709080204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_FTP_Download_MCS5包数量</Item>
										<Item typeName="String" key="FName">Gx_0709080205</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_FTP_Download_MCS6包数量</Item>
										<Item typeName="String" key="FName">Gx_0709080206</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_FTP_Download_MCS7包数量</Item>
										<Item typeName="String" key="FName">Gx_0709080207</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_FTP_Download_MCS8包数量</Item>
										<Item typeName="String" key="FName">Gx_0709080208</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_FTP_Download_MCS9包数量</Item>
										<Item typeName="String" key="FName">Gx_0709080209</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_FTP_Download_MCS总包数量</Item>
										<Item typeName="String" key="FName">Gx_070908020A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TDSCDMA_DT_EDGE_FTP_Download_MCS总采样点数</Item>
										<Item typeName="String" key="FName">Gx_070908020B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD-SCDMA视频参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">T网占用时长 tdvDuration</Item>
								<Item typeName="String" key="FName">tdvDuration</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">T网测试距离 tdvDistance</Item>
								<Item typeName="String" key="FName">tdvDistance</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER采样点数,R4协议 tdvBler_SampleNum</Item>
								<Item typeName="String" key="FName">tdvBler_SampleNum</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER总数,R4协议 tdvBler_Total</Item>
								<Item typeName="String" key="FName">tdvBler_Total</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER错误总数 tdvBler_Err</Item>
								<Item typeName="String" key="FName">tdvBler_Err</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TDSCDMA事件参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MO_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">100</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MO_Established 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">101</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MO_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">102</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">接入时长(rrcConnectionRequest到Alerting的时间毫秒)</Item>
										<Item typeName="String" key="FName">value3</Item>
										<Item typeName="Int32" key="FTag">102</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MO_Disconnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">103</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MO_CallFail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">104</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MO_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">105</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MO_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">106</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MO_Established 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">107</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MO_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">108</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MO_Disconnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">109</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MO_CallFail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">110</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MO_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">111</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MT_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">112</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MT_Established 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">113</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MT_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">114</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">接入时长(rrcConnectionRequest到Alerting的时间毫秒)</Item>
										<Item typeName="String" key="FName">value3</Item>
										<Item typeName="Int32" key="FTag">114</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MT_Disconnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">115</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MT_CallFail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">116</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_MT_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">117</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MT_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">118</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MT_Established 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">119</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MT_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">120</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">接入时长(ChannelRequest到Alerting的时间毫秒)</Item>
										<Item typeName="String" key="FName">value3</Item>
										<Item typeName="Int32" key="FTag">120</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MT_Disconnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">121</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MT_CallFail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">122</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_MT_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">123</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_LocationUpdate_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">124</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_LocationUpdate_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">125</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_LocationUpdate_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">126</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_LocationUpdate_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">127</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_LocationUpdate_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">128</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_LocationUpdate_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">129</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_RAU_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">130</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_RAU_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">131</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_RAU_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">132</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_RAU_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">133</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_RAU_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">134</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GSM_RAU_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">135</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_CellReselection_T2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">136</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选时延</Item>
										<Item typeName="String" key="FName">value5</Item>
										<Item typeName="Int32" key="FTag">136</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选前电平平均值</Item>
										<Item typeName="String" key="FName">value6</Item>
										<Item typeName="Int32" key="FTag">136</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选后电平平均值</Item>
										<Item typeName="String" key="FName">value7</Item>
										<Item typeName="Int32" key="FTag">136</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_CellReselection_Fail_T2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">137</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_CellReselection_G2T 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">138</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选时延</Item>
										<Item typeName="String" key="FName">value5</Item>
										<Item typeName="Int32" key="FTag">138</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选前电平平均值</Item>
										<Item typeName="String" key="FName">value6</Item>
										<Item typeName="Int32" key="FTag">138</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选后电平平均值</Item>
										<Item typeName="String" key="FName">value7</Item>
										<Item typeName="Int32" key="FTag">138</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_CellReselection_Fail_G2T 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">139</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverRequest_T2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">140</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverSuccess_T2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">141</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换时延</Item>
										<Item typeName="String" key="FName">value5</Item>
										<Item typeName="Int32" key="FTag">141</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换前电平平均值</Item>
										<Item typeName="String" key="FName">value6</Item>
										<Item typeName="Int32" key="FTag">141</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换后电平平均值</Item>
										<Item typeName="String" key="FName">value7</Item>
										<Item typeName="Int32" key="FTag">141</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverFail_T2G  数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">142</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverRequest_IntraT 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">143</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverSuccess_IntraT 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">144</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换时延</Item>
										<Item typeName="String" key="FName">value5</Item>
										<Item typeName="Int32" key="FTag">144</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换前电平平均值</Item>
										<Item typeName="String" key="FName">value6</Item>
										<Item typeName="Int32" key="FTag">144</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换后电平平均值</Item>
										<Item typeName="String" key="FName">value7</Item>
										<Item typeName="Int32" key="FTag">144</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverFail_IntraT 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">145</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverRequest_Baton 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">146</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverSuccess_Baton 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">147</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换时延</Item>
										<Item typeName="String" key="FName">value5</Item>
										<Item typeName="Int32" key="FTag">147</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换前电平平均值</Item>
										<Item typeName="String" key="FName">value6</Item>
										<Item typeName="Int32" key="FTag">147</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换后电平平均值</Item>
										<Item typeName="String" key="FName">value7</Item>
										<Item typeName="Int32" key="FTag">147</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverFail_Baton 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">148</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverRequest_IntraG 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">149</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverSuccess_IntraG 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">150</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换时延</Item>
										<Item typeName="String" key="FName">value5</Item>
										<Item typeName="Int32" key="FTag">150</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换前电平平均值</Item>
										<Item typeName="String" key="FName">value6</Item>
										<Item typeName="Int32" key="FTag">150</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">切换后电平平均值</Item>
										<Item typeName="String" key="FName">value7</Item>
										<Item typeName="Int32" key="FTag">150</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_HandoverFail_IntraG 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">151</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_R4_DataDrop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">152</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_GPRSEDGE_DataDrop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">153</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MO_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">154</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MO_Cir_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">155</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MO_VP_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">156</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MO_Disonnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">157</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MO_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">158</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MT_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">159</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MT_CirConnect  数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">160</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MT_VP_Connect  数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">161</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MT_Disonnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">162</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MT_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">163</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_PPP_Dial_Start 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">164</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_PPP_Dial_Success  数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">165</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_FTP_Download_Connect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">166</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_FTP_Send_RETR 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">167</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_FTP_Download_First_Data 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">168</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_FTP_Download_Last_Data 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">169</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_FTP_Download_Disconnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">170</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_FTP_Download_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">171</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RRC Connection Completed 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">172</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RAB Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">173</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RAB Setup Completed 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">174</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RAB Setup Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">175</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_CellUPdate数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">176</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_CellUPdateConfirm 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">177</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_CellReselection_T2T 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">178</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选时延</Item>
										<Item typeName="String" key="FName">value5</Item>
										<Item typeName="Int32" key="FTag">178</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选前电平平均值</Item>
										<Item typeName="String" key="FName">value6</Item>
										<Item typeName="Int32" key="FTag">178</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选后电平平均值</Item>
										<Item typeName="String" key="FName">value7</Item>
										<Item typeName="Int32" key="FTag">178</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_CellReselection_Fail_T2T 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">179</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_CellReselection_G2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">180</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选时延</Item>
										<Item typeName="String" key="FName">value5</Item>
										<Item typeName="Int32" key="FTag">180</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选前电平平均值</Item>
										<Item typeName="String" key="FName">value6</Item>
										<Item typeName="Int32" key="FTag">180</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">重选后电平平均值</Item>
										<Item typeName="String" key="FName">value7</Item>
										<Item typeName="Int32" key="FTag">180</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_CellReselection_Fail_G2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">181</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MO_Connect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">182</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MO_Failed 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">183</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MT_Connect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">184</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TD_VP_MT_Failed 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">185</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
</Configs>
