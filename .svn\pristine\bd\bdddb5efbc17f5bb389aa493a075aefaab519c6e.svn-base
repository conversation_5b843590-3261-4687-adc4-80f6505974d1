﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYQueryCellOptimalCoverDataAna : DIYGridQuery
    {
        public ZTDIYQueryCellOptimalCoverDataAna(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "栅格最优覆盖查询(按区域)"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12092, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            this.AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            return condition.Geometorys.GeoOp.CheckRectCenterInRegion(grid.Bounds);
        }

        #region 全局变量
        DTDataHeaderManager headerManager = null;
        Dictionary<string, string> strFormulaDic = null;
        Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic = null;
        Dictionary<string, List<OptimalCoverGridInfo>> strKeyOptimalCoverDic = null;
        public static List<OptimalCoverDataInfo> optimalCoverResultList { get; set; }
        string strCityName = "";
        private GridColorFixed gridColorFixed = null;
        ZTDIYQueryCellOptimalCoverCorlorForm opCoverColor = null;
        #endregion

        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }           
            if (opCoverColor == null)
                opCoverColor = new ZTDIYQueryCellOptimalCoverCorlorForm();
            if (opCoverColor.ShowDialog() != DialogResult.OK)
                return;
            gridColorFixed = new GridColorFixed();            
            gridColorFixed = opCoverColor.GridColorFixed;
            setFormula();
            strKeyOptimalCoverDic = new Dictionary<string, List<OptimalCoverGridInfo>>();
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            optimalCoverResultList = new List<OptimalCoverDataInfo>();
            InitRegionMop2();
            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            this.imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            foreach (int districtID in condition.DistrictIDs)
            {
                strCityName = DistrictManager.GetInstance().getDistrictName(districtID);
                queryDistrictData(districtID);
                ChangDataByMutKey();
                ChangDataForResult();
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
            }
            afterRecieveAllData();
            fireShowResult();
        }
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                WaitBox.Text = "开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();

                string statImgIDSet = getStatImgNeededTriadID();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(null, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                }
                else
                {
                    foreach (TimePeriod period in condition.Periods)
                    {
                        queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                    }
                }
                WaitBox.Text = "数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }
        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            return this.getTriadIDIgnoreServiceType(this.strFormulaDic.Values);
        }
        /// <summary>
        /// 重写接收信息方法，要增加接收文件信息
        /// </summary>
        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            GridMatrix<ColorUnit> colorMatrix = reservedParams[0] as GridMatrix<ColorUnit>;
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            headerManager = new DTDataHeaderManager();
            headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = "正在从服务器接收数据...";
                }
                recved = true;
                KPIStatDataBase singleStatData = null;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader header = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    headerManager.AddDTDataHeader(header);
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(colorMatrix, package, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref counter, ref curPercent);
            }
        }

        private void fillData(GridMatrix<ColorUnit> colorMatrix, Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            if (isValidStatImg(lng, lat))
            {
                fillStatData(package, curImgColumnDef, singleStatData);
                ColorUnit cu = new ColorUnit();
                cu.LTLng = lng;
                cu.LTLat = lat;
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);
                cu = colorMatrix[rAt, cAt];
                if (cu == null)
                {
                    cu = new ColorUnit();
                    cu.LTLng = lng;
                    cu.LTLat = lat;
                    colorMatrix[rAt, cAt] = cu;
                }
                cu.Status = 1;
                cu.DataHub.AddStatData(singleStatData, false);
            }
        }

        /// <summary>
        /// 获取预存或圈选的区域
        /// </summary>
        private void InitRegionMop2()
        {
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegionMap(resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private void addRegionMap(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!mutRegionMopDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                mutRegionMopDic.Add(strGridType, regionMop);
            }
        }

        /// <summary>
        /// 定位所在网格(区域相交)
        /// </summary>
        private void isContainDbRect(DbRect dRect, ref string gridTypeGrid)
        {
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (mutRegionMopDic[gridType][grid].CheckRectIntersectWithRegion(dRect))
                    {
                        gridTypeGrid = gridType + "," + grid;
                        break;
                    }
                }
            }
        }
        /// <summary>
        /// 定制各指标公式
        /// </summary>
        private void setFormula()
        {
            strFormulaDic = new Dictionary<string, string>();
            strFormulaDic.Add("GSM场强", "Mx_640102");
            strFormulaDic.Add("TD场强", "Tx_5C04030D");
            strFormulaDic.Add("WCDMA场强", "Wx_710A3F");
            strFormulaDic.Add("GSM覆盖率采样点_MO", "Mx_640117+Mx_64010A+Mx_640109+Mx_640108");
            strFormulaDic.Add("GSM覆盖率采样点_BASE", "Mx_640101");
            strFormulaDic.Add("GSM覆盖率里程_MO", "Mx_640119+Mx_640116+Mx_640115+Mx_640114");
            strFormulaDic.Add("GSM覆盖率里程_BASE", "Mx_0861");
            strFormulaDic.Add("TD覆盖率采样点_MO", "Tx_5C040301+Tx_5C040302+Tx_5C040303+Tx_5C040304+Tx_5C040305");
            strFormulaDic.Add("TD覆盖率采样点_BASE", "Tx_5C04030A");
            strFormulaDic.Add("TD覆盖率里程_MO", "Tx_5C040366+Tx_5C040367+Tx_5C040368+Tx_5C040369+Tx_5C04036A");
            strFormulaDic.Add("TD覆盖率里程_BASE", "Tx_0865");
            strFormulaDic.Add("WCDMA覆盖率采样点_MO", "Wx_710A33+Wx_710A34+Wx_710A35+Wx_710A36+Wx_710A37");
            strFormulaDic.Add("WCDMA覆盖率采样点_BASE", "Wx_710A3C");
            strFormulaDic.Add("WCDMA覆盖率里程_MO", "Wx_710A5D+Wx_710A5E+Wx_710A5F+Wx_710A60+Wx_710A61");
            strFormulaDic.Add("WCDMA覆盖率里程_BASE", "Wx_0863");
        }
        /// <summary>
        /// 通过文件ID判断所属运营商（主要针对GSM栅格）
        /// </summary>
        private int getICarrier(int iFileID)
        {
            int iCarrier = -1;
            if (headerManager.GetHeaderMap().ContainsKey(iFileID))
                iCarrier = headerManager.GetHeaderMap()[iFileID].CarrierType;
            return iCarrier;
        }
        /// <summary>
        /// 按天处理数据的主键
        /// </summary>
        protected void ChangDataByMutKey()
        {                       
            string strKey = "";
            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                float retfGSM = -150;
                float retfTDORWCDMA = -150;
                int iCarrier = -1;
                string strGSMDate = "";
                string strTDDate = "";
                string strWCDMADate = "";
                string gridTypeGrid = "";    
                OptimalCoverGridInfo ocg = new OptimalCoverGridInfo();
                StatDataGSM dataStatGSM = cu.DataHub.GetStatData(typeof(StatDataGSM)) as StatDataGSM;
                StatDataTD dataStatTD = cu.DataHub.GetStatData(typeof(StatDataTD)) as StatDataTD;
                StatDataWCDMA dataStatWCDMA = cu.DataHub.GetStatData(typeof(StatDataWCDMA)) as StatDataWCDMA;
                isContainDbRect(cu.Bounds, ref gridTypeGrid);//判断栅格所属网格
                if ((dataStatGSM == null && dataStatTD == null && dataStatWCDMA == null) || gridTypeGrid == "")
                    continue;
                ocg.StrCity = strCityName;
                ocg.StrGridType = gridTypeGrid.Split(',')[0];
                ocg.StrGrid = gridTypeGrid.Split(',')[1];
                ocg.CuInfo = cu;
                if (dataStatGSM == null)
                {
                    if (dataStatTD != null)
                    {
                        ocg.ICarrier = 1;
                        ocg.StrDate = JavaDate.GetDateTimeFromMilliseconds(1000L * headerManager.GetHeaderMap()[dataStatTD.FileID].BeginTime).ToString("yyyy-MM-dd");
                        strKey = ocg.StrDate + "|" + ocg.StrCity + "|" + ocg.StrGridType + "|" + ocg.StrGrid + "|" + "移动G&T";
                        CaluCuTD(cu, ref retfTDORWCDMA, ref ocg);
                        if (retfTDORWCDMA < -90 || retfTDORWCDMA > -10)
                            continue;
                        cu.retv = retfTDORWCDMA;
                        ocg.FOptimalRxLe = cu.retv;
                        ocg.F2GRxLe = -150;
                        ocg.F3GRxLe = retfTDORWCDMA;
                        if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                        {
                            List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                            ocgListTem.Add(ocg);
                            strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                        }
                        else
                            strKeyOptimalCoverDic[strKey].Add(ocg);
                    }
                    if (dataStatWCDMA != null)
                    {
                        ocg.ICarrier = 2;
                        ocg.StrDate = JavaDate.GetDateTimeFromMilliseconds(1000L * headerManager.GetHeaderMap()[dataStatWCDMA.FileID].BeginTime).ToString("yyyy-MM-dd");
                        strKey = ocg.StrDate + "|" + ocg.StrCity + "|" + ocg.StrGridType + "|" + ocg.StrGrid + "|" + "联通G&W";
                        CaluCuWCDMA(cu, ref retfTDORWCDMA, ref ocg);
                        if (retfTDORWCDMA < -90 || retfTDORWCDMA > -10)
                            continue;
                        cu.retv = retfTDORWCDMA;
                        ocg.FOptimalRxLe = cu.retv;
                        ocg.F2GRxLe = -150;
                        ocg.F3GRxLe = retfTDORWCDMA;
                        if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                        {
                            List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                            ocgListTem.Add(ocg);
                            strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                        }
                        else
                            strKeyOptimalCoverDic[strKey].Add(ocg);
                    }
                }
                else
                {
                    if (dataStatTD == null && dataStatWCDMA == null)
                    {
                        iCarrier = getICarrier(dataStatGSM.FileID);
                        strGSMDate = JavaDate.GetDateTimeFromMilliseconds(1000L * headerManager.GetHeaderMap()[dataStatGSM.FileID].BeginTime).ToString("yyyy-MM-dd");
                        ocg.StrDate = strGSMDate;
                        ocg.ICarrier = iCarrier;
                        StringBuilder sb = new StringBuilder();
                        sb.Append(ocg.StrDate + "|" + ocg.StrCity + "|" + ocg.StrGridType + "|" + ocg.StrGrid + "|");
                        if (iCarrier == 1)
                        {
                            sb.Append("移动G&T");
                            ocg.StrNetDisplay = "移动G&T";
                        }
                        else
                        {
                            sb.Append("联通G&W");
                            ocg.StrNetDisplay = "联通G&W";
                        }
                        strKey = sb.ToString();
                        CaluCuGSM(cu, ref retfGSM, ref ocg);
                        if (retfGSM < -90 || retfGSM > -10)
                            continue;
                        cu.retv = retfGSM;
                        ocg.FOptimalRxLe = cu.retv;
                        ocg.F2GRxLe = retfGSM;
                        ocg.F3GRxLe = -150;
                        if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                        {
                            List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                            ocgListTem.Add(ocg);
                            strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                        }
                        else
                            strKeyOptimalCoverDic[strKey].Add(ocg);
                    }
                    else if (dataStatTD != null && dataStatWCDMA == null)
                    {
                        iCarrier = getICarrier(dataStatGSM.FileID);
                        strGSMDate = JavaDate.GetDateTimeFromMilliseconds(1000L * headerManager.GetHeaderMap()[dataStatGSM.FileID].BeginTime).ToString("yyyy-MM-dd");
                        strTDDate = JavaDate.GetDateTimeFromMilliseconds(1000L * headerManager.GetHeaderMap()[dataStatTD.FileID].BeginTime).ToString("yyyy-MM-dd");
                        if (iCarrier != 1 || strGSMDate != strTDDate)
                        {
                            ocg.StrDate = strGSMDate;
                            ocg.ICarrier = iCarrier;
                            StringBuilder sb = new StringBuilder();
                            sb.Append(ocg.StrDate + "|" + ocg.StrCity + "|" + ocg.StrGridType + "|" + ocg.StrGrid + "|");
                            if (iCarrier == 1)
                            {
                                sb.Append("移动G&T");
                                ocg.StrNetDisplay = "移动G&T";
                            }
                            else
                            {
                                sb.Append("联通G&W");
                                ocg.StrNetDisplay = "联通G&W";
                            }
                            strKey = sb.ToString();
                            CaluCuGSM(cu, ref retfGSM, ref ocg);
                            cu.retv = retfGSM;
                            ocg.FOptimalRxLe = cu.retv;
                            ocg.F2GRxLe = retfGSM;
                            ocg.F3GRxLe = -150;
                            if (retfGSM >= -90 && retfGSM <= -10)
                            {
                                if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                {
                                    List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                    ocgListTem.Add(ocg);
                                    strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                }
                                else
                                    strKeyOptimalCoverDic[strKey].Add(ocg);
                            }

                            OptimalCoverGridInfo ocgtd = new OptimalCoverGridInfo();
                            ocgtd.StrCity = strCityName;
                            ocgtd.StrGridType = gridTypeGrid.Split(',')[0];
                            ocgtd.StrGrid = gridTypeGrid.Split(',')[1];
                            ocgtd.CuInfo = cu;

                            ocgtd.ICarrier = 1;
                            ocgtd.StrDate = strTDDate;
                            strKey = ocgtd.StrDate + "|" + ocgtd.StrCity + "|" + ocgtd.StrGridType + "|" + ocgtd.StrGrid + "|" + "移动G&T";
                            CaluCuTD(cu, ref retfTDORWCDMA, ref ocgtd);
                            cu.retv = retfTDORWCDMA;
                            ocgtd.FOptimalRxLe = cu.retv;
                            ocgtd.F2GRxLe = -150;
                            ocgtd.F3GRxLe = retfTDORWCDMA;
                            if (retfTDORWCDMA >= -90 || retfTDORWCDMA <= -10)
                            {
                                if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                {
                                    List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                    ocgListTem.Add(ocgtd);
                                    strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                }
                                else
                                    strKeyOptimalCoverDic[strKey].Add(ocgtd);
                            }
                        }
                        else
                        {
                            if (strGSMDate == strTDDate)
                            {
                                ocg.StrDate = strGSMDate;
                                ocg.ICarrier = iCarrier;
                                strKey = ocg.StrDate + "|" + ocg.StrCity + "|" + ocg.StrGridType + "|" + ocg.StrGrid + "|" + "移动G&T";
                                ocg.StrNetDisplay = "移动G&T";
                                CaluCuGSM(cu, ref retfGSM, ref ocg);
                                CaluCuTD(cu, ref retfTDORWCDMA, ref ocg);
                                setValueForRxleve(cu, ref ocg, retfGSM, retfTDORWCDMA);
                                if (ocg.FOptimalRxLe >= -90 || ocg.FOptimalRxLe <= -10)
                                {
                                    if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                    {
                                        List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                        ocgListTem.Add(ocg);
                                        strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                    }
                                    else
                                        strKeyOptimalCoverDic[strKey].Add(ocg);
                                }
                            }
                        }
                    }
                    else if (dataStatTD == null && dataStatWCDMA != null)
                    {
                        iCarrier = getICarrier(dataStatGSM.FileID);
                        strGSMDate = JavaDate.GetDateTimeFromMilliseconds(1000L * headerManager.GetHeaderMap()[dataStatGSM.FileID].BeginTime).ToString("yyyy-MM-dd");
                        strWCDMADate = JavaDate.GetDateTimeFromMilliseconds(1000L * headerManager.GetHeaderMap()[dataStatWCDMA.FileID].BeginTime).ToString("yyyy-MM-dd");
                        if (iCarrier != 2 || strGSMDate != strWCDMADate)
                        {
                            ocg.StrDate = strGSMDate;
                            ocg.ICarrier = iCarrier;
                            StringBuilder sb = new StringBuilder();
                            sb.Append(ocg.StrDate + "|" + ocg.StrCity + "|" + ocg.StrGridType + "|" + ocg.StrGrid + "|");
                            if (iCarrier == 1)
                            {
                                sb.Append("移动G&T");
                                ocg.StrNetDisplay = "移动G&T";
                            }
                            else
                            {
                                sb.Append("联通G&W");
                                ocg.StrNetDisplay = "联通G&W";
                            }
                            strKey = sb.ToString();
                            CaluCuGSM(cu, ref retfGSM, ref ocg);

                            cu.retv = retfGSM;
                            ocg.FOptimalRxLe = cu.retv;
                            ocg.F2GRxLe = retfGSM;
                            ocg.F3GRxLe = -150;
                            if (retfGSM >= -90 && retfGSM <= -10)
                            {
                                if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                {
                                    List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                    ocgListTem.Add(ocg);
                                    strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                }
                                else
                                    strKeyOptimalCoverDic[strKey].Add(ocg);
                            }
                            OptimalCoverGridInfo ocgwcdma = new OptimalCoverGridInfo();
                            ocgwcdma.StrCity = strCityName;
                            ocgwcdma.StrGridType = gridTypeGrid.Split(',')[0];
                            ocgwcdma.StrGrid = gridTypeGrid.Split(',')[1];
                            ocgwcdma.CuInfo = cu;
                            ocgwcdma.ICarrier = 2;
                            ocgwcdma.StrDate = strWCDMADate;
                            strKey = ocgwcdma.StrDate + "|" + ocgwcdma.StrCity + "|" + ocgwcdma.StrGridType + "|" + ocgwcdma.StrGrid + "|" + "联通G&W";
                            CaluCuTD(cu, ref retfTDORWCDMA, ref ocgwcdma);
                            cu.retv = retfTDORWCDMA;
                            ocgwcdma.FOptimalRxLe = cu.retv;
                            ocgwcdma.F2GRxLe = -150;
                            ocgwcdma.F3GRxLe = retfTDORWCDMA;
                            if (retfTDORWCDMA >= -90 || retfTDORWCDMA <= -10)
                            {
                                if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                {
                                    List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                    ocgListTem.Add(ocgwcdma);
                                    strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                }
                                else
                                    strKeyOptimalCoverDic[strKey].Add(ocgwcdma);
                            }
                        }
                        else
                        {
                            if (strGSMDate == strWCDMADate)
                            {
                                ocg.StrDate = strGSMDate;
                                ocg.ICarrier = iCarrier;
                                strKey = ocg.StrDate + "|" + ocg.StrCity + "|" + ocg.StrGridType + "|" + ocg.StrGrid + "|" + "移动G&T";
                                ocg.StrNetDisplay = "移动G&T";
                                
                                CaluCuGSM(cu, ref retfGSM, ref ocg);
                                CaluCuTD(cu, ref retfTDORWCDMA, ref ocg);
                                setValueForRxleve(cu, ref ocg, retfGSM, retfTDORWCDMA);
                                if (ocg.FOptimalRxLe >= -90 || ocg.FOptimalRxLe <= -10)
                                {
                                    if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                    {
                                        List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                        ocgListTem.Add(ocg);
                                        strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                    }
                                    else
                                        strKeyOptimalCoverDic[strKey].Add(ocg);
                                }
                            }
                        }
                    }
                    else if (dataStatTD != null && dataStatWCDMA != null)
                    {
                        iCarrier = getICarrier(dataStatGSM.FileID);
                        strGSMDate = JavaDate.GetDateTimeFromMilliseconds(1000L * headerManager.GetHeaderMap()[dataStatGSM.FileID].BeginTime).ToString("yyyy-MM-dd");
                        strTDDate = JavaDate.GetDateTimeFromMilliseconds(1000L * headerManager.GetHeaderMap()[dataStatTD.FileID].BeginTime).ToString("yyyy-MM-dd");
                        strWCDMADate = JavaDate.GetDateTimeFromMilliseconds(1000L * headerManager.GetHeaderMap()[dataStatWCDMA.FileID].BeginTime).ToString("yyyy-MM-dd");
                        if (iCarrier == 1)
                        {
                            if (strGSMDate != strTDDate)
                            {
                                ocg.StrDate = strGSMDate;
                                ocg.ICarrier = iCarrier;
                                StringBuilder sb = new StringBuilder();
                                sb.Append(ocg.StrDate + "|" + ocg.StrCity + "|" + ocg.StrGridType + "|" + ocg.StrGrid + "|");
                                sb.Append("移动G&T");
                                strKey = sb.ToString();
                                ocg.StrNetDisplay = "移动G&T";
                                CaluCuGSM(cu, ref retfGSM, ref ocg);
                                cu.retv = retfGSM;
                                ocg.FOptimalRxLe = cu.retv;
                                ocg.F2GRxLe = retfGSM;
                                ocg.F3GRxLe = -150;
                                if (retfGSM >= -90 && retfGSM <= -10)
                                {
                                    if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                    {
                                        List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                        ocgListTem.Add(ocg);
                                        strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                    }
                                    else
                                        strKeyOptimalCoverDic[strKey].Add(ocg);
                                }

                                OptimalCoverGridInfo ocgtd = new OptimalCoverGridInfo();
                                ocgtd.StrCity = strCityName;
                                ocgtd.StrGridType = gridTypeGrid.Split(',')[0];
                                ocgtd.StrGrid = gridTypeGrid.Split(',')[1];
                                ocgtd.CuInfo = cu;

                                ocgtd.ICarrier = 1;
                                ocgtd.StrDate = strTDDate;
                                strKey = ocgtd.StrDate + "|" + ocgtd.StrCity + "|" + ocgtd.StrGridType + "|" + ocgtd.StrGrid + "|" + "移动G&T";
                                CaluCuTD(cu, ref retfTDORWCDMA, ref ocgtd);
                                cu.retv = retfTDORWCDMA;
                                ocgtd.FOptimalRxLe = cu.retv;
                                ocgtd.F2GRxLe = -150;
                                ocgtd.F3GRxLe = retfTDORWCDMA;
                                if (retfTDORWCDMA >= -90 || retfTDORWCDMA <= -10)
                                {
                                    if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                    {
                                        List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                        ocgListTem.Add(ocgtd);
                                        strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                    }
                                    else
                                        strKeyOptimalCoverDic[strKey].Add(ocgtd);
                                }
                            }
                            else
                            {
                                if (strGSMDate == strTDDate)
                                {
                                    ocg.StrDate = strGSMDate;
                                    ocg.ICarrier = iCarrier;
                                    strKey = ocg.StrDate + "|" + ocg.StrCity + "|" + ocg.StrGridType + "|" + ocg.StrGrid + "|" + "移动G&T";
                                    ocg.StrNetDisplay = "移动G&T";
                                    CaluCuGSM(cu, ref retfGSM, ref ocg);
                                    CaluCuTD(cu, ref retfTDORWCDMA, ref ocg);
                                    setValueForRxleve(cu, ref ocg, retfGSM, retfTDORWCDMA);
                                    if (ocg.FOptimalRxLe >= -90 || ocg.FOptimalRxLe <= -10)
                                    {
                                        if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                        {
                                            List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                            ocgListTem.Add(ocg);
                                            strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                        }
                                        else
                                            strKeyOptimalCoverDic[strKey].Add(ocg);
                                    }
                                }
                            }

                            OptimalCoverGridInfo ocgwcdma = new OptimalCoverGridInfo();
                            ocgwcdma.StrCity = strCityName;
                            ocgwcdma.StrGridType = gridTypeGrid.Split(',')[0];
                            ocgwcdma.StrGrid = gridTypeGrid.Split(',')[1];
                            ocgwcdma.CuInfo = cu;
                            ocgwcdma.ICarrier = 2;
                            ocgwcdma.StrDate = strWCDMADate;
                            strKey = ocgwcdma.StrDate + "|" + ocgwcdma.StrCity + "|" + ocgwcdma.StrGridType + "|" + ocgwcdma.StrGrid + "|" + "联通G&W";
                            CaluCuTD(cu, ref retfTDORWCDMA, ref ocgwcdma);
                            cu.retv = retfTDORWCDMA;
                            ocgwcdma.FOptimalRxLe = cu.retv;
                            ocgwcdma.F2GRxLe = -150;
                            ocgwcdma.F3GRxLe = retfTDORWCDMA;
                            if (retfTDORWCDMA >= -90 || retfTDORWCDMA <= -10)
                            {
                                if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                {
                                    List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                    ocgListTem.Add(ocgwcdma);
                                    strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                }
                                else
                                    strKeyOptimalCoverDic[strKey].Add(ocgwcdma);
                            }
                        }
                        else
                        {
                            if (strGSMDate != strWCDMADate)
                            {
                                ocg.StrDate = strGSMDate;
                                ocg.ICarrier = iCarrier;
                                StringBuilder sb = new StringBuilder();
                                sb.Append(ocg.StrDate + "|" + ocg.StrCity + "|" + ocg.StrGridType + "|" + ocg.StrGrid + "|");
                                sb.Append("联通G&W");
                                strKey = sb.ToString();
                                CaluCuGSM(cu, ref retfGSM, ref ocg);

                                cu.retv = retfGSM;
                                ocg.FOptimalRxLe = cu.retv;
                                ocg.F2GRxLe = retfGSM;
                                ocg.F3GRxLe = -150;
                                if (retfGSM >= -90 && retfGSM <= -10)
                                {
                                    if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                    {
                                        List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                        ocgListTem.Add(ocg);
                                        strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                    }
                                    else
                                        strKeyOptimalCoverDic[strKey].Add(ocg);
                                }
                                OptimalCoverGridInfo ocgwcdma = new OptimalCoverGridInfo();
                                ocgwcdma.StrCity = strCityName;
                                ocgwcdma.StrGridType = gridTypeGrid.Split(',')[0];
                                ocgwcdma.StrGrid = gridTypeGrid.Split(',')[1];
                                ocgwcdma.CuInfo = cu;
                                ocgwcdma.ICarrier = 2;
                                ocgwcdma.StrDate = strWCDMADate;
                                strKey = ocgwcdma.StrDate + "|" + ocgwcdma.StrCity + "|" + ocgwcdma.StrGridType + "|" + ocgwcdma.StrGrid + "|" + "联通G&W";
                                CaluCuTD(cu, ref retfTDORWCDMA, ref ocgwcdma);
                                cu.retv = retfTDORWCDMA;
                                ocgwcdma.FOptimalRxLe = cu.retv;
                                ocgwcdma.F2GRxLe = -150;
                                ocgwcdma.F3GRxLe = retfTDORWCDMA;
                                if (retfTDORWCDMA >= -90 || retfTDORWCDMA <= -10)
                                {
                                    if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                    {
                                        List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                        ocgListTem.Add(ocgwcdma);
                                        strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                    }
                                    else
                                        strKeyOptimalCoverDic[strKey].Add(ocgwcdma);
                                }
                            }
                            else
                            {
                                if (strGSMDate == strWCDMADate)
                                {
                                    ocg.StrDate = strGSMDate;
                                    ocg.ICarrier = iCarrier;
                                    strKey = ocg.StrDate + "|" + ocg.StrCity + "|" + ocg.StrGridType + "|" + ocg.StrGrid + "|" + "联通G&W";
                                    ocg.StrNetDisplay = "联通G&W";
                                    CaluCuGSM(cu, ref retfGSM, ref ocg);
                                    CaluCuTD(cu, ref retfTDORWCDMA, ref ocg);
                                    setValueForRxleve(cu, ref ocg, retfGSM, retfTDORWCDMA);
                                    if (ocg.FOptimalRxLe >= -90 || ocg.FOptimalRxLe <= -10)
                                    {
                                        if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                        {
                                            List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                            ocgListTem.Add(ocg);
                                            strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                        }
                                        else
                                            strKeyOptimalCoverDic[strKey].Add(ocg);
                                    }
                                }
                            }

                            OptimalCoverGridInfo ocgtd = new OptimalCoverGridInfo();
                            ocgtd.StrCity = strCityName;
                            ocgtd.StrGridType = gridTypeGrid.Split(',')[0];
                            ocgtd.StrGrid = gridTypeGrid.Split(',')[1];
                            ocgtd.CuInfo = cu;

                            ocgtd.ICarrier = 1;
                            ocgtd.StrDate = strTDDate;
                            strKey = ocgtd.StrDate + "|" + ocgtd.StrCity + "|" + ocgtd.StrGridType + "|" + ocgtd.StrGrid + "|" + "移动G&T";
                            CaluCuTD(cu, ref retfTDORWCDMA, ref ocgtd);
                            cu.retv = retfTDORWCDMA;
                            ocgtd.FOptimalRxLe = cu.retv;
                            ocgtd.F2GRxLe = -150;
                            ocgtd.F3GRxLe = retfTDORWCDMA;
                            if (retfTDORWCDMA >= -90 || retfTDORWCDMA <= -10)
                            {
                                if (!strKeyOptimalCoverDic.ContainsKey(strKey))
                                {
                                    List<OptimalCoverGridInfo> ocgListTem = new List<OptimalCoverGridInfo>();
                                    ocgListTem.Add(ocgtd);
                                    strKeyOptimalCoverDic.Add(strKey, ocgListTem);
                                }
                                else
                                    strKeyOptimalCoverDic[strKey].Add(ocgtd);
                            }
                        }
                    }
                }              
            }
        }
        /// <summary>
        /// 最优场强赋值
        /// </summary>
        private void setValueForRxleve(ColorUnit cu, ref OptimalCoverGridInfo ocg, 
            float retfGSM, float retfTDORWCDMA)
        {
            if (retfGSM != 0 && retfTDORWCDMA != 0)
                cu.retv = retfGSM >= retfTDORWCDMA ? retfGSM : retfTDORWCDMA;
            else if (retfGSM != 0)
                cu.retv = retfGSM;
            else if (retfTDORWCDMA != 0)
                cu.retv = retfTDORWCDMA;
            else
                cu.retv = 0;
            ocg.FOptimalRxLe = cu.retv;
            ocg.F2GRxLe = retfGSM;
            ocg.F3GRxLe = retfTDORWCDMA;
        }
        /// <summary>
        /// 计算GSM的指标值
        /// </summary>
        private void CaluCuGSM(ColorUnit cu, ref float retfGSM, ref OptimalCoverGridInfo ocg)
        {
            retfGSM = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["GSM场强"]);
            ocg.D2GCoverMOBySample = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["GSM覆盖率采样点_MO"]);
            ocg.D2GCoverBASEBySample = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["GSM覆盖率采样点_BASE"]);
            ocg.D2GCoverMOByTest = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["GSM覆盖率里程_MO"]);
            ocg.D2GCoverBASEByTest = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["GSM覆盖率里程_BASE"]);
        }
        /// <summary>
        /// 计算TD的指标值
        /// </summary>
        private void CaluCuTD(ColorUnit cu, ref float retfTDORWCDMA, ref OptimalCoverGridInfo ocg)
        {
            retfTDORWCDMA = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["TD场强"]);
            ocg.D3GCoverMOBySample = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["TD覆盖率采样点_MO"]);
            ocg.D3GCoverBASEBySample = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["TD覆盖率采样点_BASE"]);
            ocg.D3GCoverMOByTest = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["TD覆盖率里程_MO"]);
            ocg.D3GCoverBASEByTest = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["TD覆盖率里程_BASE"]);
        }
        /// <summary>
        /// 计算WCMDA的指标值
        /// </summary>
        private void CaluCuWCDMA(ColorUnit cu, ref float retfTDORWCDMA, ref OptimalCoverGridInfo ocg)
        {
            retfTDORWCDMA = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["WCDMA场强"]);
            ocg.D3GCoverMOBySample = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["WCDMA覆盖率采样点_MO"]);
            ocg.D3GCoverBASEBySample = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["WCDMA覆盖率采样点_BASE"]);
            ocg.D3GCoverMOByTest = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["WCDMA覆盖率里程_MO"]);
            ocg.D3GCoverBASEByTest = (float)cu.DataHub.CalcValueByFormula(strFormulaDic["WCDMA覆盖率里程_BASE"]);
        }
        /// <summary>
        /// 转换为输出的结果集
        /// </summary>
        protected void ChangDataForResult()
        {
            Dictionary<string, List<OptimalCoverValueInfo>> strDateOCV
                = new Dictionary<string, List<OptimalCoverValueInfo>>();
            List<int> iDateTem = new List<int>();
            foreach (string sKey in strKeyOptimalCoverDic.Keys)
            {
                int date = int.Parse(sKey.Split('|')[0].Replace("-",""));
                if (!iDateTem.Contains(date))
                    iDateTem.Add(date);
            }
            iDateTem.Sort();
            foreach (int iDate in iDateTem)
            {
                string strDate = iDate.ToString().Substring(0, 4) + "-" + iDate.ToString().Substring(4, 2)
                    + "-" + iDate.ToString().Substring(6, 2);
                List<OptimalCoverValueInfo> optimalCoverValue = new List<OptimalCoverValueInfo>();
                foreach (string sKey in strKeyOptimalCoverDic.Keys)
                {
                    if (!sKey.Contains(strDate))
                        continue;
                    addOptimalCoverData(strDate, optimalCoverValue, sKey);
                }
                optimalCoverResultList.Add(calCoverSummarryByDate(optimalCoverValue, strDate, "移动G&T"));
                optimalCoverResultList.Add(calCoverSummarryByDate(optimalCoverValue, strDate, "联通G&W"));
                strDateOCV.Add(strDate, optimalCoverValue);               
            }
            optimalCoverResultList.Add(calCoverSummarryByCity(strDateOCV, "移动G&T"));
            optimalCoverResultList.Add(calCoverSummarryByCity(strDateOCV, "联通G&W"));
        }

        private void addOptimalCoverData(string strDate, List<OptimalCoverValueInfo> optimalCoverValue, string sKey)
        {
            OptimalCoverDataInfo ocd = new OptimalCoverDataInfo();
            string[] strL = sKey.Split('|');
            ocd.StrDate = strDate;
            ocd.StrCity = strL[1];
            ocd.StrGridType = strL[2];
            ocd.StrGrid = strL[3];
            ocd.StrCarrier = strL[4];

            OptimalCoverValueInfo ocv = calValueByKey(strL, strKeyOptimalCoverDic[sKey]);
            optimalCoverValue.Add(ocv);

            ocd.DOptimalBySample = 0;
            if (ocv.F23GOptimalSampleBASE != 0)
                ocd.DOptimalBySample = 100.0 * ocv.F23GOptimalSampleMO / ocv.F23GOptimalSampleBASE;
            ocd.DOptimalByTest = 0;
            if (ocv.F23GOptimalTestBASE != 0)
                ocd.DOptimalByTest = 100.0 * ocv.F23GOptimalTestMO / ocv.F23GOptimalTestBASE;
            ocd.DGSMBySample = 0;
            if (ocv.F2GSampleBASE != 0)
                ocd.DGSMBySample = 100.0 * ocv.F2GSampleMO / ocv.F2GSampleBASE;
            ocd.DGSMByTest = 0;
            if (ocv.F2GTestBASE != 0)
                ocd.DGSMByTest = 100.0 * ocv.F2GTestMO / ocv.F2GTestBASE;
            ocd.DTDBySample = 0;
            if (ocv.F3GSampleBASE != 0)
                ocd.DTDBySample = 100.0 * ocv.F3GSampleMO / ocv.F3GSampleBASE;
            ocd.DTDByTest = 0;
            if (ocv.F3GTestBASE != 0)
                ocd.DTDByTest = 100.0 * ocv.F3GTestMO / ocv.F3GTestBASE;
            optimalCoverResultList.Add(ocd);
        }

        /// <summary>
        /// 重写后续处理过程
        /// </summary>
        protected override void fireShowResult()
        {
            MainModel.RefreshLegend();
            CalculateColor();
            MainModel.IsPrepareWithoutGridPartParam = true;
            MainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = null;
            MainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = gridColorFixed;
            
            MainModel.RefreshLegend();
            MainModel.IsPrepareWithoutGridPartParam = false;

            showDataForm();
        }
        /// <summary>
        /// 按主键计算主键汇总
        /// </summary>
        private OptimalCoverValueInfo calValueByKey(string[] strLE, List<OptimalCoverGridInfo> optimalList)
        {
            OptimalCoverValueInfo value = new OptimalCoverValueInfo();
            value.StrDate = strLE[0];
            value.StrCity = strLE[1];
            value.StrNetDisplay = strLE[4];
            foreach (OptimalCoverGridInfo ocg in optimalList)
            {
                if (ocg.F2GRxLe == 0 && ocg.F3GRxLe == 0)
                    continue;
                value.F2GSampleMO += ocg.D2GCoverMOBySample;
                value.F2GSampleBASE += ocg.D2GCoverBASEBySample;
                value.F2GTestMO += ocg.D2GCoverMOByTest;
                value.F2GTestBASE += ocg.D2GCoverBASEByTest;
                value.F3GSampleMO += ocg.D3GCoverMOBySample;
                value.F3GSampleBASE += ocg.D3GCoverBASEBySample;
                value.F3GTestMO += ocg.D3GCoverMOByTest;
                value.F3GTestBASE += ocg.D3GCoverBASEByTest;
                if (ocg.FOptimalRxLe == ocg.F2GRxLe)
                {
                    value.F23GOptimalSampleMO += ocg.D2GCoverMOBySample;
                    value.F23GOptimalSampleBASE += ocg.D2GCoverBASEBySample;
                    value.F23GOptimalTestMO += ocg.D2GCoverMOByTest;
                    value.F23GOptimalTestBASE += ocg.D2GCoverBASEByTest;
                }
                else if (ocg.FOptimalRxLe == ocg.F3GRxLe)
                {
                    value.F23GOptimalSampleMO += ocg.D3GCoverMOBySample;
                    value.F23GOptimalSampleBASE += ocg.D3GCoverBASEBySample;
                    value.F23GOptimalTestMO += ocg.D3GCoverMOByTest;
                    value.F23GOptimalTestBASE += ocg.D3GCoverBASEByTest;
                }
            }
            return value;
        }
        /// <summary>
        /// 按天汇总
        /// </summary>
        private OptimalCoverDataInfo calCoverSummarryByDate(List<OptimalCoverValueInfo> optimalCoverValueTem
            ,string strD, string strCarrier)
        {
            OptimalCoverDataInfo ocdi = new OptimalCoverDataInfo();
            ocdi.StrDate = strD;
            ocdi.StrCity = strCityName;
            ocdi.StrGridType = "汇总";
            ocdi.StrGrid = "汇总";
            ocdi.StrCarrier = strCarrier;

            OptimalCoverValueInfo optimal = new OptimalCoverValueInfo();
            foreach (OptimalCoverValueInfo ocv in optimalCoverValueTem)
            {
                if (ocv.StrNetDisplay != strCarrier)
                    continue;
                optimal.F23GOptimalSampleMO += ocv.F23GOptimalSampleMO;
                optimal.F23GOptimalSampleBASE += ocv.F23GOptimalSampleBASE;
                optimal.F23GOptimalTestMO += ocv.F23GOptimalTestMO;
                optimal.F23GOptimalTestBASE += ocv.F23GOptimalTestBASE;
                optimal.F2GSampleMO += ocv.F2GSampleMO;
                optimal.F2GSampleBASE += ocv.F2GSampleBASE;
                optimal.F2GTestMO += ocv.F2GTestMO;
                optimal.F2GTestBASE += ocv.F2GTestBASE;
                optimal.F3GSampleMO += ocv.F3GSampleMO;
                optimal.F3GSampleBASE += ocv.F3GSampleBASE;
                optimal.F3GTestMO += ocv.F3GTestMO;
                optimal.F3GTestBASE += ocv.F3GTestBASE;
            }
            ocdi.DOptimalBySample = 0;
            if (optimal.F23GOptimalSampleBASE != 0)
                ocdi.DOptimalBySample = 100.0 * optimal.F23GOptimalSampleMO / optimal.F23GOptimalSampleBASE;
            ocdi.DOptimalByTest = 0;
            if (optimal.F23GOptimalTestBASE != 0)
                ocdi.DOptimalByTest = 100.0 * optimal.F23GOptimalTestMO / optimal.F23GOptimalTestBASE;
            ocdi.DGSMBySample = 0;
            if (optimal.F2GSampleBASE != 0)
                ocdi.DGSMBySample += 100.0 * optimal.F2GSampleMO / optimal.F2GSampleBASE;
            ocdi.DGSMByTest = 0;
            if (optimal.F2GTestBASE != 0)
                ocdi.DGSMByTest = 100.0 * optimal.F2GTestMO / optimal.F2GTestBASE;
            ocdi.DTDBySample = 0;
            if (optimal.F3GSampleBASE != 0)
                ocdi.DTDBySample = 100.0 * optimal.F3GSampleMO / optimal.F3GSampleBASE;
            ocdi.DTDByTest = 0;
            if (optimal.F3GTestBASE != 0)
                ocdi.DTDByTest = 100.0 * optimal.F3GTestMO / optimal.F3GTestBASE;
            return ocdi;
        }
        /// <summary>
        /// 按地市汇总
        /// </summary>
        private OptimalCoverDataInfo calCoverSummarryByCity(Dictionary<string, List<OptimalCoverValueInfo>> strDateOCV
            ,string strCarrier)
        {
            OptimalCoverDataInfo ocdi = new OptimalCoverDataInfo();
            ocdi.StrDate = "汇总";
            ocdi.StrCity = strCityName;
            ocdi.StrGridType = "汇总";
            ocdi.StrGrid = "汇总";
            ocdi.StrCarrier = strCarrier;
            OptimalCoverValueInfo optimal = new OptimalCoverValueInfo();
            foreach (string date in strDateOCV.Keys)
            {
                foreach (OptimalCoverValueInfo ocv in strDateOCV[date])
                {
                    if (ocv.StrNetDisplay != strCarrier)
                        continue;
                    optimal.F23GOptimalSampleMO += ocv.F23GOptimalSampleMO;
                    optimal.F23GOptimalSampleBASE += ocv.F23GOptimalSampleBASE;
                    optimal.F23GOptimalTestMO += ocv.F23GOptimalTestMO;
                    optimal.F23GOptimalTestBASE += ocv.F23GOptimalTestBASE;
                    optimal.F2GSampleMO += ocv.F2GSampleMO;
                    optimal.F2GSampleBASE += ocv.F2GSampleBASE;
                    optimal.F2GTestMO += ocv.F2GTestMO;
                    optimal.F2GTestBASE += ocv.F2GTestBASE;
                    optimal.F3GSampleMO += ocv.F3GSampleMO;
                    optimal.F3GSampleBASE += ocv.F3GSampleBASE;
                    optimal.F3GTestMO += ocv.F3GTestMO;
                    optimal.F3GTestBASE += ocv.F3GTestBASE;
                }
            }
            ocdi.DOptimalBySample = 0;
            if (optimal.F23GOptimalSampleBASE != 0)
                ocdi.DOptimalBySample = 100.0 * optimal.F23GOptimalSampleMO / optimal.F23GOptimalSampleBASE;
            ocdi.DOptimalByTest = 0;
            if (optimal.F23GOptimalTestBASE != 0)
                ocdi.DOptimalByTest = 100.0 * optimal.F23GOptimalTestMO / optimal.F23GOptimalTestBASE;
            ocdi.DGSMBySample = 0;
            if (optimal.F2GSampleBASE != 0)
                ocdi.DGSMBySample = 100.0 * optimal.F2GSampleMO / optimal.F2GSampleBASE;
            ocdi.DGSMByTest = 0;
            if (optimal.F2GTestBASE != 0)
                ocdi.DGSMByTest = 100.0 * optimal.F2GTestMO / optimal.F2GTestBASE;
            ocdi.DTDBySample = 0;
            if (optimal.F3GSampleBASE != 0)
                ocdi.DTDBySample = 100.0 * optimal.F3GSampleMO / optimal.F3GSampleBASE;
            ocdi.DTDByTest = 0;
            if (optimal.F3GTestBASE != 0)
                ocdi.DTDByTest = 100.0 * optimal.F3GTestMO / optimal.F3GTestBASE;
            return ocdi;
        }

        /// <summary>
        /// 设置栅格颜色
        /// </summary>
        private void CalculateColor()
        {
            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                if (cu != null)
                {
                    cu.color = judeRxleveSector(cu.retv);
                }
            }
        }
        /// <summary>
        /// 判断场强所属渲染区间
        /// </summary>
        private Color judeRxleveSector(float rxleveMean)
        {
            Color color = new Color();
            bool isFind = false;
            foreach (GridColorFixedItem item in gridColorFixed.items)
            {
                string[] strItem = item.desc.Replace(" <= 最优场强均值 < ", ",").Split(',');
                if (rxleveMean >= float.Parse(strItem[0])
                    && rxleveMean <= float.Parse(strItem[1]))
                {
                    color = item.color;
                    isFind = true;
                    break;
                }
            }
            if (!isFind)
                color = Color.Red;
            return color;
        }
        /// <summary>
        /// 显示结果集
        /// </summary>
        private void showDataForm()
        {
            ZTDIYQueryCellOptimalCoverDataFormAna showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(ZTDIYQueryCellOptimalCoverDataFormAna).FullName);
            showForm = obj == null ? null : obj as ZTDIYQueryCellOptimalCoverDataFormAna;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new ZTDIYQueryCellOptimalCoverDataFormAna(MainModel);
            }
            showForm.setData(optimalCoverResultList);
            showForm.Show(MainModel.MainForm);
        }
    }
    public class OptimalCoverGridInfo
    {
        /// <summary>
        /// 日期
        /// </summary>
        public string StrDate { get; set; }
        /// <summary>
        /// 地市
        /// </summary>
        public string StrCity { get; set; }
        /// <summary>
        /// 文件ID
        /// </summary>
        public int IFileID { get; set; }
        /// <summary>
        /// 运营商ID
        /// </summary>
        public int ICarrier { get; set; }
        /// <summary>
        /// 网络类型
        /// </summary>
        public int INet { get; set; }
        /// <summary>
        /// 显示网络类型
        /// </summary>
        public string StrNetDisplay { get; set; }
        /// <summary>
        /// 网格类型
        /// </summary>
        public string StrGridType { get; set; }
        /// <summary>
        /// 网格
        /// </summary>
        public string StrGrid { get; set; }
        /// <summary>
        /// 栅格信息
        /// </summary>
        public ColorUnit CuInfo { get; set; }
        /// <summary>
        /// 最优场强
        /// </summary>
        public float FOptimalRxLe { get; set; }
        /// <summary>
        /// 2G场强
        /// </summary>
        public float F2GRxLe { get; set; }
        /// <summary>
        /// 3G场强
        /// </summary>
        public float F3GRxLe { get; set; }
        /// <summary>
        /// 2G单个栅格覆盖率分子（按采样点）
        /// </summary>
        public double D2GCoverMOBySample { get; set; }
        /// <summary>
        ///  2G单个栅格覆盖率分母（按采样点）
        /// </summary>
        public double D2GCoverBASEBySample { get; set; }
        /// <summary>
        ///  2G单个栅格覆盖率分子（按里程）
        /// </summary>
        public double D2GCoverMOByTest { get; set; }
        /// <summary>
        ///  2G单个栅格覆盖率分母（按里程）
        /// </summary>
        public double D2GCoverBASEByTest { get; set; }
        /// <summary>
        ///  3G单个栅格覆盖率分子（按采样点）
        /// </summary>
        public double D3GCoverMOBySample { get; set; }
        /// <summary>
        ///  3G单个栅格覆盖率分母（按采样点）
        /// </summary>
        public double D3GCoverBASEBySample { get; set; }
        /// <summary>
        ///  3G单个栅格覆盖率分子（按里程）
        /// </summary>
        public double D3GCoverMOByTest { get; set; }
        /// <summary>
        ///  3G单个栅格覆盖率分母（按里程）
        /// </summary>
        public double D3GCoverBASEByTest { get; set; }

        public OptimalCoverGridInfo()
        {
            StrDate = "";
            StrCity = "";
            IFileID = 0;
            ICarrier = 0;
            INet = 0;
            StrNetDisplay = "";
            StrGridType = "";
            StrGrid = "";
            CuInfo = new ColorUnit();
            D2GCoverMOBySample = 0;
            D2GCoverBASEBySample = 0;
            D2GCoverMOByTest = 0;
            D2GCoverBASEByTest = 0;
            D3GCoverMOBySample = 0;
            D3GCoverBASEBySample = 0;
            D3GCoverMOByTest = 0;
            D3GCoverBASEByTest = 0;
        }
    }
    public class OptimalCoverDataInfo
    {       
        /// <summary>
        /// 日期
        /// </summary>
        public string StrDate { get; set; }
        /// <summary>
        /// 地市
        /// </summary>
        public string StrCity { get; set; }
        /// <summary>
        /// 网格类型
        /// </summary>
        public string StrGridType { get; set; }
        /// <summary>
        /// 网格
        /// </summary>
        public string StrGrid { get; set; }
        /// <summary>
        /// 运营商
        /// </summary>
        public string StrCarrier { get; set; }
        /// <summary>
        /// 23G最优覆盖率(-90dBm)-按采样点(%)
        /// </summary>
        public double DOptimalBySample { get; set; }
        /// <summary>
        /// 23G最优覆盖率(-90dBm)-按采样点(%)(字符型)
        /// </summary>
        public string StrOptimalBySample
        {
            get { return DOptimalBySample.ToString("0.00") + "%"; }
        }
        /// <summary>
        /// 23G最优覆盖率(-90dBm)-按里程(%)
        /// </summary>
        public double DOptimalByTest { get; set; }
        /// <summary>
        /// 23G最优覆盖率(-90dBm)-按里程(%)(字符型)
        /// </summary>
        public string StrOptimalByTest
        {
            get { return DOptimalByTest.ToString("0.00") + "%"; }
        }
        /// <summary>
        /// 2G覆盖率(-90dBm)-按采样点(%)
        /// </summary>
        public double DGSMBySample { get; set; }
        /// <summary>
        /// 2G覆盖率(-90dBm)-按采样点(%)(字符型)
        /// </summary>
        public string StrGSMBySample
        {
            get { return DGSMBySample.ToString("0.00") + "%"; }
        }
        /// <summary>
        /// 2G覆盖率(-90dBm)-按里程(%)
        /// </summary>
        public double DGSMByTest { get; set; }
        /// <summary>
        /// 2G覆盖率(-90dBm)-按里程(%)(字符型)
        /// </summary>
        public string StrGSMByTest
        {
            get { return DGSMByTest.ToString("0.00") + "%"; }
        }
        /// <summary>
        /// 3G覆盖率(-90dBm)-按采样点(%)
        /// </summary>
        public double DTDBySample { get; set; }
        /// <summary>
        /// 3G覆盖率(-90dBm)-按采样点(%)(字符型)
        /// </summary>
        public string StrTDBySample
        {
            get { return DTDBySample.ToString("0.00") + "%"; }
        }
        /// <summary>
        /// 3G覆盖率(-90dBm)-按里程(%)
        /// </summary>
        public double DTDByTest { get; set; }
        /// <summary>
        /// 3G覆盖率(-90dBm)-按里程(%)(字符型)
        /// </summary>
        public string StrTDByTest
        {
            get { return DTDByTest.ToString("0.00") + "%"; }
        }
    }
    public class OptimalCoverValueInfo
    {
        /// <summary>
        /// 日期
        /// </summary>
        public string StrDate { get; set; }
        /// <summary>
        /// 地市
        /// </summary>
        public string StrCity { get; set; }
        /// <summary>
        /// 显示网络类型
        /// </summary>
        public string StrNetDisplay { get; set; }
        /// <summary>
        /// 23G最优覆盖分子（采样点）
        /// </summary>
        public double F23GOptimalSampleMO { get; set; }
        /// <summary>
        /// 23G最优覆盖分母（采样点）
        /// </summary>
        public double F23GOptimalSampleBASE { get; set; }
        /// <summary>
        /// 23G最优覆盖分子（里程）
        /// </summary>
        public double F23GOptimalTestMO { get; set; }
        /// <summary>
        /// 23G最优覆盖分母（里程）
        /// </summary>
        public double F23GOptimalTestBASE { get; set; }
        /// <summary>
        /// 2G覆盖分子（采样点）
        /// </summary>
        public double F2GSampleMO { get; set; }
        /// <summary>
        /// 2G覆盖分母（采样点）
        /// </summary>
        public double F2GSampleBASE { get; set; }
        /// <summary>
        /// 2G覆盖分子（里程）
        /// </summary>
        public double F2GTestMO { get; set; }
        /// <summary>
        /// 2G覆盖分母（里程）
        /// </summary>
        public double F2GTestBASE { get; set; }
        /// <summary>
        /// 3G覆盖分子（采样点）
        /// </summary>
        public double F3GSampleMO { get; set; }
        /// <summary>
        /// 3G覆盖分母（采样点）
        /// </summary>
        public double F3GSampleBASE { get; set; }
        /// <summary>
        /// 3G覆盖分子（里程）
        /// </summary>
        public double F3GTestMO { get; set; }
        /// <summary>
        /// 3G覆盖分母（里程）
        /// </summary>
        public double F3GTestBASE { get; set; }
    }
}