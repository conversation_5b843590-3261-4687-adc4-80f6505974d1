﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage.HighReverseFlowCoverageImport
{
    public class ImportExcelDataHelper : ImportExcelData
    {
        public List<CellParamInfo> NrCellList { get; set; }
        public List<CellParamInfo> LteCellList { get; set; }
        public SqlConnectionStringBuilder SqlConnect { get; set; }

        public ImportExcelDataHelper(string title)
           : base(title)
        {
        }

        #region 加载文件数据
        protected override void dealImportData(DataSet ds)
        {
            NrCellList = new List<CellParamInfo>();
            LteCellList = new List<CellParamInfo>();

            Dictionary<long, bool> nrCellDic = new Dictionary<long, bool>();
            Dictionary<long, bool> lteCellDic = new Dictionary<long, bool>();

            WaitBox.ProgressPercent = 30;
            DataTable nrCellTB = ds.Tables[0];
            loadCellConfig(nrCellTB, NrCellList, "NR", nrCellDic);
            WaitBox.ProgressPercent = 60;
            DataTable lteCellTB = ds.Tables[1];
            loadCellConfig(lteCellTB, LteCellList, "LTE", lteCellDic);
            WaitBox.ProgressPercent = 90;
        }

        private void loadCellConfig(DataTable tb, List<CellParamInfo> allCellList, string desc
            , Dictionary<long, bool> cellDic)
        {
            try
            {
                foreach (DataRow dr in tb.Rows)
                {
                    CellParamInfo info;
                    if (desc == "NR")
                    {
                        info = new NRCellInfo();
                    }
                    else
                    {
                        info = new LTECellInfo();
                    }
                    info.FillDataByExcel(dr);

                    judgeValid(info, cellDic);

                    allCellList.Add(info);
                }

                if (allCellList.Count == 0)
                {
                    throw (new Exception($"{desc}小区sheet无数据"));
                }
            }
            catch
            {
                throw (new Exception($"加载{desc}小区sheet出错"));
            }
        }
        #endregion

        private void judgeValid(CellParamInfo info, Dictionary<long, bool> cellDic)
        {
            if (info.CityID == 0)
            {
                info.HasErr = true;
                info.ErrMsg = "CityID为0;";
            }
            else if (info.Bts.ILongitude == 0 || info.Bts.ILatitude == 0)
            {
                info.HasErr = true;
                info.ErrMsg = "基站经纬度为0;";
            }

            if (info.CI == 0)
            {
                info.HasErr = true;
                info.ErrMsg += "CI为0;";
            }
            else if (cellDic.TryGetValue(info.CI, out var _))
            {
                info.HasErr = true;
                info.ErrMsg += "CI重复;";
            }
            else
            {
                cellDic.Add(info.CI, true);
            }
        }

        #region 数据入库
        protected override void importDataToDB()
        {
            try
            {
                WaitBox.ProgressPercent = 30;
                insertNrCellInfo();
                WaitBox.ProgressPercent = 60;
                insertLteCellInfo();
                WaitBox.ProgressPercent = 90;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private bool insertNrCellInfo()
        {
            HighReverseFlowCoverageConfig.Instance.WriteLog("正在导入5G工参结果...", "info");
            var insert = new DiyInsertNrCellInfo(NrCellList);

            insert.Bcp(SqlConnect);

            return judgeInsertResult("插入5G工参结果表失败", insert.ErrMsg);
        }

        private bool insertLteCellInfo()
        {
            HighReverseFlowCoverageConfig.Instance.WriteLog("正在导入4G工参结果...", "info");
            var insert = new DiyInsertLteCellInfo(LteCellList);
            insert.Bcp(SqlConnect);

            return judgeInsertResult("插入4G工参结果表失败", insert.ErrMsg);
        }

        private static bool judgeInsertResult(string desc, string errMsg)
        {
            if (!string.IsNullOrEmpty(errMsg))
            {
                HighReverseFlowCoverageConfig.Instance.WriteLog($"{desc}:{errMsg}", "error");
                return false;
            }
            return true;
        }
        #endregion

        #region 下载模板
        protected override List<ExportToExcelModel> dealTemplateData()
        {
            List<ExportToExcelModel> lsData = new List<ExportToExcelModel>();
            initNrCellModel(lsData);

            initLteCellModel(lsData);
            return lsData;
        }

        private void initNrCellModel(List<ExportToExcelModel> lsData)
        {
            ExportToExcelModel sheet = new ExportToExcelModel();
            sheet.SheetName = "NR";

            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.AddCellValue("省份");
            nr.AddCellValue("省份ID");
            nr.AddCellValue("地市");
            nr.AddCellValue("地市ID");
            nr.AddCellValue("区域");
            nr.AddCellValue("网格");
            nr.AddCellValue("基站号");
            nr.AddCellValue("小区名");
            nr.AddCellValue("覆盖类型");
            nr.AddCellValue("频段");
            nr.AddCellValue("TAC");
            nr.AddCellValue("CI");
            nr.AddCellValue("celllongitude");
            nr.AddCellValue("celllatitude");
            nr.AddCellValue("btslongitude");
            nr.AddCellValue("btslatitude");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("新疆");
            nr.AddCellValue("12200");
            nr.AddCellValue("乌鲁木齐");
            nr.AddCellValue("991");
            nr.AddCellValue("城区道路");
            nr.AddCellValue("0991-0009-wlmq");
            nr.AddCellValue("2228877");
            nr.AddCellValue("乌鲁木齐会展大道东-H5H-2613");
            nr.AddCellValue("宏站");
            nr.AddCellValue("700MHz");
            nr.AddCellValue("5767219");
            nr.AddCellValue("9129480195");
            nr.AddCellValue("876291000");
            nr.AddCellValue("438708000");
            nr.AddCellValue("876291000");
            nr.AddCellValue("438708000");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("新疆");
            nr.AddCellValue("12200");
            nr.AddCellValue("乌鲁木齐");
            nr.AddCellValue("991");
            nr.AddCellValue("城区道路");
            nr.AddCellValue("0991-0009-wlmq");
            nr.AddCellValue("1557374");
            nr.AddCellValue("乌鲁木齐机场大道-H5H-01");
            nr.AddCellValue("宏站");
            nr.AddCellValue("2.6GHz");
            nr.AddCellValue("5735878");
            nr.AddCellValue("9127548394");
            nr.AddCellValue("876291000");
            nr.AddCellValue("438708000");
            nr.AddCellValue("876291000");
            nr.AddCellValue("438708000");
            rows.Add(nr);
            sheet.Data = rows;
            lsData.Add(sheet);
        }

        private void initLteCellModel(List<ExportToExcelModel> lsData)
        {
            ExportToExcelModel sheet = new ExportToExcelModel();
            sheet.SheetName = "LTE";

            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.AddCellValue("省份");
            nr.AddCellValue("省份ID");
            nr.AddCellValue("地市");
            nr.AddCellValue("地市ID");
            nr.AddCellValue("区域");
            nr.AddCellValue("网格");
            nr.AddCellValue("基站号");
            nr.AddCellValue("小区名");
            nr.AddCellValue("覆盖类型");
            nr.AddCellValue("TAC");
            nr.AddCellValue("CI");
            nr.AddCellValue("celllongitude");
            nr.AddCellValue("celllatitude");
            nr.AddCellValue("btslongitude");
            nr.AddCellValue("btslatitude");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("新疆");
            nr.AddCellValue("12200");
            nr.AddCellValue("乌鲁木齐");
            nr.AddCellValue("991");
            nr.AddCellValue("城区道路");
            nr.AddCellValue("0991-0001-wlmq");
            nr.AddCellValue("245894");
            nr.AddCellValue("乌鲁木齐长征新村52号-HLH-3-FDD1800");
            nr.AddCellValue("宏站");
            nr.AddCellValue("39405");
            nr.AddCellValue("62948952");
            nr.AddCellValue("875869350");
            nr.AddCellValue("437586280");
            nr.AddCellValue("875869350");
            nr.AddCellValue("437586280");
            rows.Add(nr);
            sheet.Data = rows;
            lsData.Add(sheet);
        }
        #endregion

        public void Clear()
        {
            NrCellList = null;
            LteCellList = null;
        }
    }
}
