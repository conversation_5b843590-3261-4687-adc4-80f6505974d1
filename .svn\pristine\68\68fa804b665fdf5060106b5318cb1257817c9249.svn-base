﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace MasterCom.RAMS.NOP
{
    public class NopDbManager
    {
        private readonly string nopDbCfgName = Application.StartupPath + "\\config\\NOPSetting.xml";

        private static NopDbManager instance = null;
        public static NopDbManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NopDbManager();
                }
                return instance;
            }
        }

        private NopDbManager()
        {
            load();
        }

        private NopDBSetting nopDbSetting = null;
        public NopDBSetting NopDbSetting { get { return nopDbSetting; } }

        private void load()
        {
            if (!File.Exists(nopDbCfgName))
            {
                return;
            }

            try
            {
                XmlConfigFile configFile = new XmlConfigFile(nopDbCfgName);
                nopDbSetting = new NopDBSetting();
                nopDbSetting.DbIP = configFile.GetItemValue("NOP", "DBIP") as string;
                nopDbSetting.DbPort = (int?)configFile.GetItemValue("NOP", "DBPort");
                nopDbSetting.DbDataBaseName = configFile.GetItemValue("NOP", "DBDataBaseName") as string;
                nopDbSetting.DbUserName = configFile.GetItemValue("NOP", "DBUserName") as string;
                nopDbSetting.DbPassword = DES.Decode(configFile.GetItemValue("NOP", "DBPassword") as string);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + ex.StackTrace + ex.Source);
            }
        }
    }
    public class NopDBSetting
    {
        public string DbIP { get; set; }
        public int? DbPort { get; set; }
        public string DbDataBaseName { get; set; }
        public string DbUserName { get; set; }
        public string DbPassword { get; set; }
        public string DbIpWithPort
        {
            get
            {
                if (DbPort == null)
                {
                    return DbIP;
                }
                else
                {
                    return string.Format("{0},{1}", DbIP, DbPort);
                }
            }
        }
        public string DbFullName
        {
            get
            {
                string dbFullName;
                if (!string.IsNullOrEmpty(DbIpWithPort))
                {
                    dbFullName = string.Format("[{0}].[{1}]", DbIpWithPort, DbDataBaseName);
                }
                else
                {
                    dbFullName = DbDataBaseName;
                }
                return dbFullName;
            }
        }
        public string NopDBConnectStr
        {
            get
            {
                return string.Format("Data Source={0};Initial Catalog={1};User ID={2};Password=***"
                    , DbIpWithPort, DbDataBaseName, DbUserName, DbPassword);
            }
        }
    }
}
