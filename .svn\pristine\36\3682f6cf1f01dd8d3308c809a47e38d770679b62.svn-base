﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class CellParamColumn
    {
        public CellParamColumn(CellParamTable table)
        {
            this.table = table;
        }

        public override string ToString()
        {
            return Name;
        }

        public string FullName
        {
            get { return table.FullName + "." + Name; }
        }

        private readonly CellParamTable table = null;
        public CellParamTable Table
        {
            get { return table; }
        }

        public string Name
        {
            get;
            set;
        }

        /// <summary>
        /// 是否为数字列
        /// </summary>
        public bool IsNumericColumn
        {
            get;
            set;
        }

        public string Description { get; set; }

        public string Alias { get; set; }

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> cfgParam = new Dictionary<string, object>();
                cfgParam["Name"] = Name;
                cfgParam["Alias"] = Alias;
                cfgParam["Description"] = Description;
                cfgParam["IsNumericColumn"] = IsNumericColumn;
                return cfgParam;
            }
            set
            {
                if (value==null)
                {
                    return;
                }
                this.Name = value["Name"] as string;
                this.Alias = value["Alias"] as string;
                this.Description = value["Description"] as string;
                IsNumericColumn = (bool)value["IsNumericColumn"];
            }
        }


    }
}
