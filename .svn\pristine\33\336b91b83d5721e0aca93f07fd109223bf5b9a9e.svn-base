﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestManager
{
    public partial class AreaResultPanel : BaseResultControl
    {
        private Dictionary<AreaBase, CAreaSummary> areaSummaryMap;

        private AnaDealerBase anaDealer;

        private Dictionary<AreaBase, TreeListNode> areaNodeDic;

        private List<DevExpress.XtraGrid.Views.Grid.GridView> gvVec;

        private DevExpress.XtraCharts.ChartControl chartControl;

        private bool bShowChart = false;
        public bool BShowChart
        {
            get { return bShowChart; }
            set 
            { 
                bShowChart = value;
                splitNodeChart.Visible = bShowChart;
            }
        }

        private ZTAreaArchiveLayer layer
        {
            get
            {
                ZTAreaArchiveLayer layerTmp = MainModel.GetInstance().MainForm.GetMapForm().GetLayerBase(typeof(ZTAreaArchiveLayer)) as ZTAreaArchiveLayer;
                if (layerTmp == null)
                {
                    layerTmp = new ZTAreaArchiveLayer();
                    MainModel.GetInstance().MainForm.GetMapForm().AddLayerBase(layerTmp);
                }
                return layerTmp;
            }
        }

        public AreaResultPanel()
        {
            InitializeComponent();
            chartControl = new DevExpress.XtraCharts.ChartControl();
            chartControl.Parent = groupControlChart;
            chartControl.Dock = DockStyle.Fill;
            MakeChartPageVisible(false);

            areaSummaryMap = new Dictionary<AreaBase, CAreaSummary>();
            areaNodeDic = new Dictionary<AreaBase, TreeListNode>();

            gvVec = new List<DevExpress.XtraGrid.Views.Grid.GridView>();
            gvVec.Add(gridViewCM);
            gvVec.Add(gridViewCU);
            gvVec.Add(gridViewCT);
        }

        public void MakeChartPageVisible(bool bVisible)
        {
            BShowChart = bVisible;
            if (BShowChart)
            {
                splitContainerControlNodeDetail.Parent = splitNodeChart.Panel1;
                splitContainerControlNodeDetail.Dock = DockStyle.Fill;
                splitNodeChart.Parent = panel1;
                splitNodeChart.Dock = DockStyle.Fill;
            }
            else
            {
                splitContainerControlNodeDetail.Parent = panel1;
                splitContainerControlNodeDetail.Dock = DockStyle.Fill;

                splitNodeChart.Parent = null;
            }
        }

        public void Init(AnaDealerBase anaDealer)
        {
            this.anaDealer = anaDealer;
        }

        public void FillData(Dictionary<AreaBase, CAreaSummary> areaSummaryMap)
        {
            this.areaSummaryMap = areaSummaryMap;
            refreshData();

            fillLayer();
        }

        private void fillLayer()
        {
            Dictionary<AreaBase, List<AreaBase>> rootLeafDic = ArchiveSettingManager.GetInstance().Condition.VillageCondition.RootLeafDic;
            foreach (List<AreaBase> leafs in rootLeafDic.Values)
            {
                layer.Areas.AddRange(leafs);
            }
        }

        private void refreshData()
        {
            if (anaDealer == null || areaSummaryMap == null) return;

            fillTreeList();
        }

        private void fillTreeList()
        {
            treeListArea.Nodes.Clear();
            anaDealer.CreateColumn(treeListArea);

            foreach (AreaBase area in areaSummaryMap.Keys)
            {
                if (area.ParentArea == null)
                {
                    appendNode(areaSummaryMap[area], null);
                }
            }

            treeListArea.FocusedNode = null;
            if(treeListArea.Nodes.Count > 0)
                treeListArea.FocusedNode = treeListArea.Nodes[0];
        }

        private void appendNode(CAreaSummary summary, TreeListNode parentNode)
        {
            TreeListNode node = treeListArea.AppendNode(anaDealer.GetRowContext(summary), parentNode);
            node.Tag = summary;
            areaNodeDic[summary.Area] = node;

            if (summary.Area.SubAreas == null) return;

            if (summary.Area.Rank != ZTAreaManager.Instance.LowestRank.ParentRank)
            {
                foreach (AreaBase sub in summary.Area.SubAreas)
                {
                    if (areaSummaryMap.ContainsKey(sub))
                    {
                        appendNode(areaSummaryMap[sub], node);
                    }
                }
            }
            else
            {
                foreach (CAreaSummary village in summary.VillageVec)
                {
                    appendNode(village, node);
                }
            }
        }

        private void treeListArea_CustomDrawNodeCell(object sender, DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs e)
        {
            CAreaSummary summary = e.Node.Tag as CAreaSummary;
            object column = e.Column.Tag;

            if (column == null) return;

            e.Appearance.BackColor = anaDealer.GetColor(summary, column);
        }

        private void treeListArea_FocusedNodeChanged(object sender, DevExpress.XtraTreeList.FocusedNodeChangedEventArgs e)
        {
            if (e.Node == null) return;

            CAreaSummary summary = e.Node.Tag as CAreaSummary;
            if (summary == null) return;

            refreshDetail(summary);
            showChart(summary);
            showFiles(summary);
        }

        private void refreshDetail(CAreaSummary summary)
        {
            List<CAreaDetail> detailVec = anaDealer.GetAreaDetail(summary, areaSummaryMap);

            columnHeader1.Width = 150;
            columnHeader2.Width = listViewDetail.Width - columnHeader1.Width - 21;
            listViewDetail.Items.Clear();
            foreach (CAreaDetail detail in detailVec)
            {
                listViewDetail.Items.AddRange(detail.GetItems(anaDealer));
            }
        }

        private void showFiles(CAreaSummary summary)
        {
            if (summary == null) return;

            List<CAreaFile> fileCM = new List<CAreaFile>();
            List<CAreaFile> fileCU = new List<CAreaFile>();
            List<CAreaFile> fileCT = new List<CAreaFile>();
            foreach (FileInfo file in summary.AreaKpiGroup.FileIDDic.Values)
            {
                CAreaFile areaFile = new CAreaFile(file);
                switch (file.CarrierType)
                {
                    case (int)ECarrier.移动:
                        fileCM.Add(areaFile);
                        areaFile.Sn = fileCM.Count;
                        break;
                    case (int)ECarrier.联通:
                        fileCU.Add(areaFile);
                        areaFile.Sn = fileCU.Count;
                        break;
                    case (int)ECarrier.电信:
                        fileCT.Add(areaFile);
                        areaFile.Sn = fileCT.Count;
                        break;
                    default:
                        break;
                }
            }
            gridControlCM.DataSource = fileCM;
            gridControlCU.DataSource = fileCU;
            gridControlCT.DataSource = fileCT;
        }

        private void contextMenuStrip_Opening(object sender, CancelEventArgs e)
        {
            //ToolStripMenuItemReplay.Enabled = xtraTabControlFile.Focused;

            renderMenu();
        }

        private void renderMenu()
        {
            ToolStripMenuItemRender.Enabled = false;
            ToolStripMenuItemRender.DropDownItems.Clear();

            if (!treeListArea.Focused) return;

            TreeListNode node = treeListArea.FocusedNode;
            if (node == null) return;

            CAreaSummary summary = node.Tag as CAreaSummary;
            if (summary == null) return;

            List<AreaBase> leafs = summary.Area.GetLeafs();
            if (leafs.Count <= 0) return;

            for (int idx = 2; idx < treeListArea.Columns.Count;idx++ )
            {
                ToolStripMenuItem item = new ToolStripMenuItem(treeListArea.Columns[idx].Caption);
                item.Tag = treeListArea.Columns[idx];
                item.Click += new EventHandler(item_Click);
                ToolStripMenuItemRender.DropDownItems.Add(item);
            }

            ToolStripMenuItemRender.Enabled = ToolStripMenuItemRender.DropDownItems.Count > 0;
        }

        private void item_Click(object sender, EventArgs e)
        {
            layer.ClearSelectArea();
            layer.AreaColorDic.Clear();

            ToolStripMenuItem item = sender as ToolStripMenuItem;
            if (item == null) return;

            DevExpress.XtraTreeList.Columns.TreeListColumn col = item.Tag as DevExpress.XtraTreeList.Columns.TreeListColumn;
            if (col == null) return;

            TreeListNode node = treeListArea.FocusedNode;
            if (node == null) return;

            CAreaSummary summary = node.Tag as CAreaSummary;
            if (summary == null) return;

            addVillage(summary, col.Tag);

            MainModel.GetInstance().MainForm.GetMapForm().GoToView(summary.Area.Bounds);
        }

        private void addVillage(CAreaSummary summary, object column)
        {
            if (summary.Area.Rank == ZTAreaManager.Instance.LowestRank)
            {
                layer.AddSelectArea(summary.Area);
                layer.AreaColorDic[summary.Area] = anaDealer.GetColor(summary, column);
            }
            else
            {
                foreach (CAreaSummary village in summary.VillageVec)
                {
                    addVillage(village, column);
                }
            }
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            if (treeListArea.Focused)
                exportTreeList();
            else
                exportFiles();
        }

        private void exportTreeList()
        {
            try
            {
                List<NPOIRow> rows = new List<NPOIRow>();
                NPOIRow captionRow = new NPOIRow();
                foreach (DevExpress.XtraTreeList.Columns.TreeListColumn col in treeListArea.Columns)
                {
                    captionRow.AddCellValue(col.Caption);
                }
                rows.Add(captionRow);
                foreach (TreeListNode node in treeListArea.Nodes)
                {
                    getDataRow(node, rows);
                }
                ExcelNPOIManager.ExportToExcel(rows);
            }
            catch
            {
                MessageBox.Show("导出到Excel失败");
            }
        }

        private void getDataRow(TreeListNode node, List<NPOIRow> rows)
        {
            NPOIRow row = new NPOIRow();
            foreach (DevExpress.XtraTreeList.Columns.TreeListColumn col in treeListArea.Columns)
            {
                row.AddCellValue(node.GetValue(col));
            }
            rows.Add(row);
            foreach (TreeListNode subNode in node.Nodes)
            {
                getDataRow(subNode, rows);
            }
        }

        private void exportFiles()
        {
            try
            {
                List<string> sheetVec = new List<string>();
                sheetVec.Add("移动");
                sheetVec.Add("联通");
                sheetVec.Add("电信");
                ExcelNPOIManager.ExportToExcel(gvVec, sheetVec);
            }
            catch
            {
                MessageBox.Show("导出到Excel失败");
            }
        }

        private void ToolStripMenuItemReplay_Click(object sender, EventArgs e)
        {
            List<FileInfo> retList = new List<FileInfo>();

            foreach (DevExpress.XtraGrid.Views.Grid.GridView gv in gvVec)
            {
                foreach (int handle in gv.GetSelectedRows())
                {
                    CAreaFile file = gv.GetRow(handle) as CAreaFile;
                    if (file != null)
                    {
                        retList.Add(file.AreaFile);
                    }
                }
            }

            if (retList.Count > 0)
            {
                ReplayFileManager.ReplayFiles(retList);
            }
        }

        private void showChart(CAreaSummary summary)
        {
            if (!BShowChart) return;

            chartControl.Series.Clear();
            foreach (ECarrier car in Enum.GetValues(typeof(ECarrier)))
            {
                Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>> timeServCarPerMap = summary.Permeate as Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>>;

                DevExpress.XtraCharts.Series series = new DevExpress.XtraCharts.Series(string.Format("{0} {1}", summary.Area.Name, car), DevExpress.XtraCharts.ViewType.Spline);

                int idx = 1;
                foreach (TimePeriod tp in timeServCarPerMap.Keys)
                {
                    series.Points.Add(
                                new DevExpress.XtraCharts.SeriesPoint(
                                    string.Format("第{0}个时段", idx++),
                                    timeServCarPerMap[tp][car].DPermeate)
                                    );
                }
                chartControl.Series.Add(series);
            }
        }
    }

    public class CAreaFile
    {
        public FileInfo AreaFile { get; set; }

        public int Sn { get; set; }

        public string FileName
        {
            get { return AreaFile.Name; }
        }

        public string ProjectType
        {
            get { return AreaFile.ProjectDescription; }
        }

        public string TestType
        {
            get { return ((CategoryEnum)CategoryManager.GetInstance()["TestType"]).GetDescription(AreaFile.TestType); }
        }

        public string ServiceType
        {
            get { return AreaFile.ServiceTypeDescription; }
        }

        public string DeviceType
        {
            get { return ((CategoryEnum)CategoryManager.GetInstance()["DeviceType"]).GetDescription(AreaFile.DeviceType); }
        }

        public string FileType
        {
            get { return AreaFile.FileTypeDescription; }
        }

        public CAreaFile(FileInfo file)
        {
            this.AreaFile = file;
        }
    }

    public class CAreaDetail
    {
        public string CurArea { get; set; }
        public string CurRank { get; set; }
        public string CurAreaPercent { get; set; }
        public string CMPercent { get; set; }
        public string CUPercent { get; set; }
        public string CTPercent { get; set; }
        public string CMSubAchieveArea { get { return CMSubAchieveAreaBuilder.ToString(); } }
        public string CMSubUnAchieveArea { get { return CMSubUnAchieveAreaBuilder.ToString(); } }
        public string CUSubAchieveArea { get { return CUSubAchieveAreaBuilder.ToString(); } }
        public string CUSubUnAchieveArea { get { return CUSubUnAchieveAreaBuilder.ToString(); } }
        public string CTSubAchieveArea { get { return CTSubAchieveAreaBuilder.ToString(); } }
        public string CTSubUnAchieveArea { get { return CTSubUnAchieveAreaBuilder.ToString(); } }

        private StringBuilder CMSubAchieveAreaBuilder { get; set; }
        private StringBuilder CMSubUnAchieveAreaBuilder { get; set; }
        private StringBuilder CUSubAchieveAreaBuilder { get; set; }
        private StringBuilder CUSubUnAchieveAreaBuilder { get; set; }
        private StringBuilder CTSubAchieveAreaBuilder { get; set; }
        private StringBuilder CTSubUnAchieveAreaBuilder { get; set; }

        public CAreaSummary Summary { get; set; }

        public CAreaDetail(CAreaSummary summary)
        {
            this.Summary = summary;
            this.CurArea = summary.Area.Name;
            this.CurRank = summary.Area.RankName;

            CMSubAchieveAreaBuilder = new StringBuilder();
            CMSubUnAchieveAreaBuilder = new StringBuilder();
            CUSubAchieveAreaBuilder = new StringBuilder();
            CUSubUnAchieveAreaBuilder = new StringBuilder();
            CTSubAchieveAreaBuilder = new StringBuilder();
            CTSubUnAchieveAreaBuilder = new StringBuilder();
        }

        public void AppendPercent(string percent, ECarrier car)
        {
            switch (car)
            {
                case ECarrier.移动:
                    CMPercent = percent;
                    break;
                case ECarrier.联通:
                    CUPercent = percent;
                    break;
                case ECarrier.电信:
                    CTPercent = percent;
                    break;
                default:
                    break;
            }
        }

        public void AppendAchieveArea(string achieveArea, ECarrier car)
        {
            switch (car)
            {
                case ECarrier.移动:
                    CMSubAchieveAreaBuilder.Append(string.Format(" {0}", achieveArea));
                    break;
                case ECarrier.联通:
                    CUSubAchieveAreaBuilder.Append(string.Format(" {0}", achieveArea));
                    break;
                case ECarrier.电信:
                    CTSubAchieveAreaBuilder.Append(string.Format(" {0}", achieveArea));
                    break;
                default:
                    break;
            }
        }

        public void AppendUnAchieveArea(string unAchieveArea, ECarrier car)
        {
            switch (car)
            {
                case ECarrier.移动:
                    CMSubUnAchieveAreaBuilder.Append(string.Format(" {0}", unAchieveArea));
                    break;
                case ECarrier.联通:
                    CUSubUnAchieveAreaBuilder.Append(string.Format(" {0}", unAchieveArea));
                    break;
                case ECarrier.电信:
                    CTSubUnAchieveAreaBuilder.Append(string.Format(" {0}", unAchieveArea));
                    break;
                default:
                    break;
            }
        }

        public ListViewItem[] GetItems(AnaDealerBase anaDealer)
        {
            string desc = "合格";
            if (anaDealer != null && anaDealer is AlarmTestAnaDealer)
            {
                desc = "预警";
            }

            int idx = 0;
            ListViewItem[] lviArr = new ListViewItem[12];

            lviArr[idx++] = addItem(new string[] { "当前区域：", string.Format("{0}", CurArea) }, Color.Wheat);
            lviArr[idx++] = addItem(new string[] { "行政级别：", string.Format("{0}", CurRank) }, Color.Wheat);
            lviArr[idx++] = addItem(new string[] { string.Format("移动{0}率：", desc), string.Format("{0}", CMPercent) }, Color.LightPink);
            lviArr[idx++] = addItem(new string[] { string.Format("下属{0}区域：", desc), string.Format("{0}", CMSubAchieveArea) }, Color.LightPink);
            lviArr[idx++] = addItem(new string[] { string.Format("下属未{0}区域：", desc), string.Format("{0}", CMSubUnAchieveArea) }, Color.LightPink);
            lviArr[idx++] = addItem(new string[] { string.Format("联通{0}率：", desc), string.Format("{0}", CTPercent) }, Color.LightGreen);
            lviArr[idx++] = addItem(new string[] { string.Format("下属{0}区域：", desc), string.Format("{0}", CUSubAchieveArea) }, Color.LightGreen);
            lviArr[idx++] = addItem(new string[] { string.Format("下属未{0}区域：", desc), string.Format("{0}", CUSubUnAchieveArea) }, Color.LightGreen);
            lviArr[idx++] = addItem(new string[] { string.Format("电信{0}率：", desc), string.Format("{0}", CTPercent) }, Color.LightSkyBlue);
            lviArr[idx++] = addItem(new string[] { string.Format("下属{0}区域：", desc), string.Format("{0}", CTSubAchieveArea) }, Color.LightSkyBlue);
            lviArr[idx++] = addItem(new string[] { string.Format("下属未{0}区域：", desc), string.Format("{0}", CTSubUnAchieveArea) }, Color.LightSkyBlue);
            lviArr[idx] = new ListViewItem() { };

            return lviArr;
        }

        private ListViewItem addItem(string[] subArr, Color bkCol)
        {
            ListViewItem lvi = new ListViewItem(subArr);
            lvi.BackColor = bkCol;
            return lvi;
        }
    }
}
