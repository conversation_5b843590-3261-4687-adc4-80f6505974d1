﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class PoorRsrqRoad
    {
        protected readonly List<string> cellNames = new List<string>();
        public string CellName
        {
            get
            {
                StringBuilder cellNameStr = new StringBuilder();
                foreach (string name in cellNames)
                {
                    if (cellNameStr.Length > 0)
                    {
                        cellNameStr.Append(" | ");
                    }
                    cellNameStr.Append(name);
                }
                return cellNameStr.ToString();
            }
        }

        protected readonly List<string> lacciList = new List<string>();
        public string LACCIs
        {
            get
            {
                StringBuilder laccis = new StringBuilder();
                foreach (string lacci in lacciList)
                {
                    if (laccis.Length > 0)
                    {
                        laccis.Append(" | ");
                    }
                    laccis.Append(lacci);
                }
                return laccis.ToString();
            }
        }
        public int SN
        {
            get;
            set;
        }
        public double Second
        {
            get
            {
                double sec = 0;
                if (testPoints.Count > 1)
                {
                    sec = (testPoints[testPoints.Count - 1].DateTime - testPoints[0].DateTime).TotalSeconds;
                }
                return sec;
            }
        }
        public double Distance
        {

            get
            {
                return Math.Round(distance, 2);
            }
        }
        public string FileName
        {
            get
            {
                string name = string.Empty;
                if (testPoints.Count > 0)
                {
                    name = testPoints[0].FileName;
                }
                return name;
            }
        }

        protected double distance = 0;
        protected readonly List<TestPoint> testPoints = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }

        public float totalRsrq { get; set; }
        public float totalSINR { get; set; }
        public float totalRSRP { get; set; }

        protected float minRsrq = float.MaxValue;
        public float MinRsrq
        {
            get { return minRsrq; }
        }
        protected float maxRsrq = float.MinValue;
        public float MaxRsrq
        {
            get { return maxRsrq; }
        }
        public float AvgRsrq
        {
            get;
            protected set;
        }

        public int sinrNum { get; set; }
        protected float minSINR = float.MaxValue;
        public float MinSINR
        {
            get { return minSINR; }
        }
        protected float minRSPR = float.MaxValue;
        public float MinRSRP
        {
            get { return minRSPR; }
        }
        public float MaxSINR
        {
            get { return maxSINR; }
        }
        protected float maxSINR = float.MinValue;
        public float MaxRSRP
        {
            get { return maxRSRP; }
        }
        protected float maxRSRP = float.MinValue;
        public float AvgSINR
        {
            get { return (float)Math.Round(totalSINR / sinrNum, 2); }
        }
        public int rsrpNum { get; set; }
        public float AvgRSRP
        {
            get { return (float)Math.Round(totalRSRP / rsrpNum, 2); }
        }
        public int TestPointCount
        {
            get { return testPoints.Count; }
        }
        internal void Add(float rsrq, float? rsrp,float? sinr, double distance, TestPoint testPoint)
        {
            addCellList(testPoint);
            addOtherTPInfo(testPoint);

            totalRsrq += rsrq;
            minRsrq = Math.Min(minRsrq, rsrq);
            maxRsrq = Math.Max(maxRsrq, rsrq);

            if (sinr != null && -50 <= sinr && sinr <= 50)
            {
                sinrNum++;
                totalSINR += (float)sinr;
                minSINR = Math.Min(minSINR, (float)sinr);
                maxSINR = Math.Max(maxSINR, (float)sinr);
            }

            if (rsrp != null && rsrp >= -141 && rsrp <= -10)
            {
                rsrpNum++;
                totalRSRP += (float)rsrp;
                minRSPR = Math.Min(minRSPR, (float)rsrp);
                maxRSRP = Math.Max(maxRSRP, (float)rsrp);
            }
            this.distance += distance;
            testPoints.Add(testPoint);
        }

        protected virtual void addOtherTPInfo(TestPoint testPoint)
        { 
        
        }

        protected virtual void addCellList(TestPoint testPoint)
        {
            LTECell lteCell = null;
            if (testPoint is ScanTestPoint_NBIOT)
            {
                lteCell = testPoint.GetCell_LTEScan(0);
            }
            else
            {
                lteCell = testPoint.GetMainLTECell_TdOrFdd();
            }
            if (lteCell != null)
            {
                string lacci = lteCell.TAC.ToString() + "_" + lteCell.SCellID.ToString();
                if (!lacciList.Contains(lacci))
                {
                    lacciList.Add(lacci);
                }
                if (!cellNames.Contains(lteCell.Name))
                {
                    cellNames.Add(lteCell.Name);
                }
            }
        }

        public string RoadName { get; set; } = string.Empty;

        public double MidLng
        {
            get
            {
                double lng = double.NaN;
                if (testPoints.Count > 0)
                {
                    lng = testPoints[(testPoints.Count / 2)].Longitude;
                }
                return lng;
            }
        }
        public double MidLat
        {
            get
            {
                double lat = double.NaN;
                if (testPoints.Count > 0)
                {
                    lat = testPoints[(testPoints.Count / 2)].Latitude;
                }
                return lat;
            }
        }
        public void FindRoadName()
        {
            RoadName = GISManager.GetInstance().GetRoadPlaceDesc(MidLng, MidLat);
        }

        public virtual void MakeSummary()
        {
            FindRoadName();
            AvgRsrq = (float)Math.Round(totalRsrq / testPoints.Count, 2);
        }
    }
}
