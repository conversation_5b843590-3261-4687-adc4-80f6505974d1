﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MControls;
using System.Drawing;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.Stat
{
    public class ReportStyle : IComparable<ReportStyle>
    {
        public bool Save()
        {
            XmlConfigFile configFile = new XmlConfigFile();
            System.Xml.XmlElement cfg = configFile.AddConfig("ReportSetting");
            configFile.AddItem(cfg, "styles", this.Param);
            try
            {
                configFile.Save(string.Format(Application.StartupPath + "/config/templates/kpi_" + this.name + ".xml"));
            }
            catch
            {
                return false;
            }
            return true;
        }

        public string name { get; set; } = "";
        public string stylename { get; set; } = "";
        public List<RptCell> rptCellList { get; set; } = new List<RptCell>();//要显示的所有单元格内容
        public List<GraphEntity> graphs { get; set; } = new List<GraphEntity>();//图表
        public List<int> rptColWidths { get; set; } = new List<int>();//列的宽度
        public List<ColumnInfo> rptColInfos { get; set; } = new List<ColumnInfo>();//列信息
        internal ReportStyle copyInstance()
        {
            ReportStyle nstyle = new ReportStyle();
            nstyle.name = name;
            foreach (RptCell cell in rptCellList)
            {
                nstyle.rptCellList.Add(cell.CopyInstance());
            }
            foreach(GraphEntity ent in graphs)
            {
                nstyle.graphs.Add(ent.copyInstance());
            }
            foreach (ColumnInfo colInfo in rptColInfos)
            {
                nstyle.rptColInfos.Add(colInfo);
            }
            foreach (int colWidth in rptColWidths)
            {
                nstyle.rptColWidths.Add(colWidth);
            }
            return nstyle;
        }
        public override string ToString()
        {
            return name;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Name"] = name;
                List<object> cellsParams = new List<object>();
                param["Cells"] = cellsParams;
                foreach (RptCell cr in rptCellList)
                {
                    if (cr.exp == null && cr.bkColor != Color.White)
                    {
                        continue;
                    }
                   cellsParams.Add(cr.Param);
                }
                List<object> graphParams = new List<object>();
                param["Graphs"] = graphParams;
                foreach (GraphEntity entity in graphs)
                {
                    graphParams.Add(entity.Param);
                }
                List<object> colsParams = new List<object>();
                param["ColInfo"] = colsParams;
                foreach (ColumnInfo colInfo in rptColInfos)
                {
                    colsParams.Add(colInfo.Param);
                }
                param["ColWidth"] = rptColWidths;
                return param;
            }
            set
            {
                name = (String)value["Name"];
                rptCellList.Clear();
                List<object> cellsParams = (List<object>)value["Cells"];
                foreach (object o in cellsParams)
                {
                    Dictionary<string, object> rptParam = (Dictionary<string, object>)o;
                    RptCell cr = new RptCell();
                    cr.Param = rptParam;
                    rptCellList.Add(cr);
                  }
                graphs.Clear();
                List<object> graphsParams = (List<object>)value["Graphs"];
                foreach (object o in graphsParams)
                {
                    Dictionary<string, object> graphsParam = (Dictionary<string, object>)o;
                    GraphEntity ge = new GraphEntity();
                    ge.Param = graphsParam;
                    graphs.Add(ge);
                }

                if (value.ContainsKey("ColInfo"))
                {
                    rptColInfos.Clear();
                    List<object> colsParams = (List<object>)value["ColInfo"];
                    foreach (object o in colsParams)
                    {
                        Dictionary<string, object> rptParam = (Dictionary<string, object>)o;
                        ColumnInfo colInfo = new ColumnInfo();
                        colInfo.Param = rptParam;
                        rptColInfos.Add(colInfo);
                    }
                }

                rptColWidths.Clear();
                if (value.ContainsKey("ColWidth"))
                {
                    List<object> colWids = (List<object>)value["ColWidth"];
                    foreach (object o in colWids)
                    {
                        rptColWidths.Add((int)o);
                    }
                }
                else
                {
                    int colMaxNum = ColCount;
                    for (int i = 0; i < colMaxNum; i++)
                    {
                        rptColWidths.Add(100);
                    }
                }
            }
        }

        public int ColCount
        {
            get
            {
                int max = 0;
                foreach (RptCell cell in rptCellList)
                {
                    max = Math.Max(max, cell.colAt);
                }
                return max + 1;
            }
        }

        public int RowCount
        {
            get
            {
                int max = 0;
                foreach (RptCell cell in rptCellList)
                {
                    max = Math.Max(max, cell.rowAt);
                }
                return max + 1;
            }
        }

        public RptCell GetCell(int rowIndex, int colIndex)
        {
            foreach (RptCell cell in rptCellList)
            {
                if (cell.rowAt==rowIndex&&cell.colAt==colIndex)
                {
                    return cell;
                }
            }
            return null;
        }

        public void AddCell(RptCell cell)
        {
            if (cell == null)
            {
                return;
            }
            for (int i = rptCellList.Count - 1; i >= 0; i--)
            {
                RptCell oldCell = rptCellList[i];
                if (oldCell.colAt == cell.colAt && oldCell.rowAt == cell.rowAt)
                {
                    rptCellList.RemoveAt(i);
                }
            }
            rptCellList.Add(cell);
        }

        /// <summary>
        /// 简单样式的报表定义：
        /// 1、所有的单元格，行位置索引只能0或者1（只有2行）；
        /// 2、行位置索引为0的单元格， 必须为普通文字单元格，不能为公式单元格；
        /// 意义：方便多维度统计时的报表呈现
        /// </summary>
        public bool IsSimpleStyleReport
        {
            get
            {
                foreach (RptCell cell in rptCellList)
                {
                    if (cell.rowAt > 1)
                    {
                        return false;
                    }
                    else if (cell.rowAt == 0 && !string.IsNullOrEmpty(cell.exp)
                        && cell.exp.Contains("{")
                        && cell.exp.Contains("}"))
                    {
                        return false;
                    }
                }
                return true;
            }
        }

        /// <summary>
        /// 插入若干行
        /// </summary>
        /// <param name="isPre">true:往前插入;false:往后插入</param>
        /// <param name="startIndex">从该行位置索引开始插入</param>
        /// <param name="count">行数</param>
        public void InsertRows(bool isPre, int startIndex, int count)
        {
            if (startIndex < 0 || count <= 0)
            {
                return;
            }
            if (!isPre)
            {
                startIndex++;
            }
            bool isAppend = true;
            foreach (RptCell cell in rptCellList)
            {
                if (cell.rowAt >= startIndex)
                {
                    isAppend = false;
                    cell.rowAt += count;
                }
            }
            if (isAppend)
            {
                RptCell cell = new RptCell();
                cell.rowAt = startIndex + count - 1;
                cell.exp = string.Empty;
                rptCellList.Add(cell);
            }
        }

        /// <summary>
        /// 插入若干列
        /// </summary>
        /// <param name="isPre">true:往前插入;false:往后插入</param>
        /// <param name="startIndex">从该列位置索引开始插入</param>
        /// <param name="count">列数</param>
        public void InsertColumns(bool isPre, int startIndex, int count)
        {
            if (startIndex < 0 && count <= 0)
            {
                return;
            }
            if (!isPre)
            {
                startIndex++;
            }
            bool isAppend = true;
            foreach (RptCell cell in rptCellList)
            {
                if (cell.colAt >= startIndex)
                {
                    isAppend = false;
                    cell.colAt += count;
                }
            }
            if (isAppend)
            {
                RptCell cell = new RptCell();
                cell.colAt = startIndex + count - 1;
                cell.exp = string.Empty;
                rptCellList.Add(cell);
            }
        }

        internal GraphEntity FindGraphAt(int rowSel, int colSel)
        {
            foreach (GraphEntity entity in graphs)
            {
                foreach (CellPosition cp in entity.values)
                {
                    if (cp.RowIndex == rowSel && cp.ColIndex == colSel)
                    {
                        return entity;
                    }
                }
                foreach (CellPosition cp in entity.labels)
                {
                    if (cp.RowIndex == rowSel && cp.ColIndex == colSel)
                    {
                        return entity;
                    }
                }
            }
            return null;
        }

        //////////////////////////////////////////////////////
        //Add by:Gene
        public bool Visible { get; set; } = false;
        //////////////////////////////////////////////////////

        #region IComparable<ReportStyle> 成员

        public int CompareTo(ReportStyle other)
        {
            return this.name.CompareTo(other.name);
        }

        #endregion

        internal void RemoveRows(int rowIndex, int count)
        {
            int maxIdx = rowIndex + count - 1;
            for (int i = 0; i < rptCellList.Count; i++)
            {
                RptCell cell = rptCellList[i];
                if (cell.rowAt >= rowIndex && cell.rowAt <= maxIdx)
                {
                    rptCellList.RemoveAt(i);
                    i--;
                }
                else if (cell.rowAt > maxIdx)
                {
                    cell.rowAt -= count;
                }
            }
        }

        internal void RemoveColumns(int colIndex, int count)
        {
            int maxIdx = colIndex + count - 1;
            for (int i = 0; i < rptCellList.Count; i++)
            {
                RptCell cell = rptCellList[i];
                if (cell.colAt >= colIndex && cell.colAt <= maxIdx)
                {
                    rptCellList.RemoveAt(i);
                    i--;
                }
                else if (cell.colAt > maxIdx)
                {
                    cell.colAt -= count;
                }
            }
        }

        public bool IsSeparateEvtByServiceID
        {
            get
            {
                foreach (RptCell cell in rptCellList)
                {
                    if (cell.ServiceIDSet.Count > 0)
                    {
                        return true;
                    }
                }
                return false;
            }
        }

        public bool HasGridPerCell {
            get
            {
                foreach (RptCell cell in rptCellList)
                {
                    if ((cell.exp != null && cell.ExtraExp == RptCell.GridAvg)
                        || (cell.exp != null
                        && cell.exp.Contains("}")
                        && cell.exp.Contains("@")))
                    {
                        return true;
                    }
                }
                return false;
            }
        }
    }
    public class GraphEntity
    {
        public string graphTitle { get; set; }
        public string graphXString { get; set; }
        public string graphYString { get; set; }

        public List<CellPosition> labels { get; set; } = new List<CellPosition>();
        public List<CellPosition> values { get; set; } = new List<CellPosition>();

        internal GraphEntity copyInstance()
        {
            GraphEntity ge = new GraphEntity();
            ge.graphTitle = graphTitle;
            ge.graphXString = graphXString;
            ge.graphYString = graphYString;
            foreach (CellPosition cp in labels)
            {
                ge.labels.Add(cp.createInstance());
            }
            foreach (CellPosition cp in values)
            {
                ge.values.Add(cp.createInstance());
            }
            return ge;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Title"] = graphTitle;
                param["XString"] = graphXString;
                param["YString"] = graphYString;
                List<object> labelsParams = new List<object>();
                param["Labels"] = labelsParams;
                foreach (CellPosition cp in labels)
                {
                    labelsParams.Add(cp.Param);
                }
                List<object> valuesParams = new List<object>();
                param["Values"] = valuesParams;
                foreach (CellPosition cp in values)
                {
                    valuesParams.Add(cp.Param);
                }
                return param;
            }
            set
            {
                graphTitle = (string)value["Title"];
                graphXString = (string)value["XString"];
                graphYString = (string)value["YString"];
                labels.Clear();
                List<object> labelsParams = (List<object>)value["Labels"];
                foreach (object o in labelsParams)
                {
                    CellPosition cp = new CellPosition();
                    cp.Param = (Dictionary<string, object>)o;
                    labels.Add(cp);
                }
                values.Clear();
                List<object> valuesParams = (List<object>)value["Values"];
                foreach (object o in valuesParams)
                {
                    CellPosition cp = new CellPosition();
                    cp.Param = (Dictionary<string, object>)o;
                    values.Add(cp);
                }
            }
        }


        
    }
    public class CellPosition
    {
        public int RowIndex { get; set; }
        public int ColIndex { get; set; }
        internal CellPosition createInstance()
        {
            CellPosition cp = new CellPosition();
            cp.RowIndex = RowIndex;
            cp.ColIndex = ColIndex;
            return cp;
        }
        public CellPosition()
        {

        }
        public CellPosition(int r,int c)
        {
            this.RowIndex = r;
            this.ColIndex = c;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Row"] = RowIndex;
                param["Col"] = ColIndex;
                return param;
            }
            set
            {
                RowIndex = (int)value["Row"];
                ColIndex = (int)value["Col"];
            }
        }

        
    }

    public class ColumnInfo
    {
        public int col { get; set; }
        public string name { get; set; }
        public int width { get; set; }
        public Color bkColor { get; set; }
        public Color foreColor { get; set; }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Column"] = col;
                param["Name"] = name;
                param["Width"] = width;
                param["BkColorR"] = (int)(bkColor.R);
                param["BkColorG"] = (int)(bkColor.G);
                param["BkColorB"] = (int)(bkColor.B);
                param["ForeColorR"] = (int)(foreColor.R);
                param["ForeColorG"] = (int)(foreColor.G);
                param["ForeColorB"] = (int)(foreColor.B);
                return param;
            }
            set
            {
                col = (int)value["Column"];
                name = (string)value["Name"];
                width = (int)value["Width"];
                int r = (int)value["BkColorR"];
                int g = (int)value["BkColorG"];
                int b = (int)value["BkColorB"];
                bkColor = Color.FromArgb(r, g, b);
                r = (int)value["ForeColorR"];
                g = (int)value["ForeColorG"];
                b = (int)value["ForeColorB"];
                foreColor = Color.FromArgb(r, g, b);
            }
        }
    }
}
