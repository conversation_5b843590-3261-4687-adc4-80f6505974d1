﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class WeakRoadReport
    {
        public WeakRoadReport()
        { }
        public WeakRoadReport(string name)
        {
            FileName = Name = name;
        }
        public override string ToString()
        {
            return Name;
        }
        public string Name { get; set; } = "弱覆盖路段";
        public int FuncId { get; set; } = 101;
        public bool IsCanUse { get; set; }
        public int RecentMonths { get; set; } = 2;
        public float? DisLastMax { get; set; } = 500;
        public float? DisLastMin { get; set; } = 100;
        public float? RsrpMax { get; set; } = -105;
        public float? WeakPerMin { get; set; } = 70;
        public string Projects { get; set; } = "1,2";
        public string FilePath { get; set; } = "C:\\Users\\<USER>\\Desktop";
        public int FileFilterIndex { get; set; }
        public string FileName { get; set; } = "弱覆盖路段";
        public List<int> DistrictIDSet { get; set; } = new List<int>();
        public List<int> ServiceIDSet { get; set; } = new List<int>();
        public int? TpCellDisMax { get; set; } = 300;
        public int? TpCellAngleMax { get; set; } = 60;
        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["Name"] = this.Name;
                paramDic["DisLastMax"] = this.DisLastMax;
                paramDic["DisLastMin"] = this.DisLastMin;
                paramDic["RsrpMax"] = this.RsrpMax;
                paramDic["WeakPerMin"] = this.WeakPerMin;
                paramDic["Projects"] = this.Projects;
                paramDic["RecentMonths"] = this.RecentMonths;
                paramDic["FilePath"] = this.FilePath;
                paramDic["FileName"] = this.FileName;
                paramDic["FuncId"] = this.FuncId;
                paramDic["IsCanUse"] = this.IsCanUse;
                paramDic["ServiceIDSet"] = this.ServiceIDSet;
                paramDic["DistrictIDSet"] = this.DistrictIDSet;
                paramDic["TpCellDisMax"] = this.TpCellDisMax;
                paramDic["TpCellAngleMax"] = this.TpCellAngleMax;
                paramDic["FileFilterIndex"] = this.FileFilterIndex;
                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.Name = value["Name"].ToString();
                this.DisLastMax = (float?)value["DisLastMax"];
                this.DisLastMin = (float?)value["DisLastMin"];
                this.RsrpMax = (float?)value["RsrpMax"];
                this.WeakPerMin = (float?)value["WeakPerMin"];
                this.Projects = value["Projects"].ToString();
                this.RecentMonths = (int)value["RecentMonths"];
                this.FilePath = value["FilePath"].ToString();
                this.FileName = value["FileName"].ToString();
                this.FuncId = (int)value["FuncId"];
                this.IsCanUse = (bool)value["IsCanUse"];
                try
                {
                    this.TpCellDisMax = (int)value["TpCellDisMax"];
                    this.TpCellAngleMax = (int)value["TpCellAngleMax"];
                    this.FileFilterIndex = (int)value["FileFilterIndex"];
                }
                catch
                { 
                    //continue
                }

                object objService = value["ServiceIDSet"];
                if (objService is List<object>)
                {
                    ServiceIDSet.Clear();
                    foreach (int id in objService as List<object>)
                    {
                        ServiceIDSet.Add(id);
                    }
                }
                else if (objService is List<int>)
                {
                    ServiceIDSet = value["ServiceIDSet"] as List<int>;
                }

                object objDistrict = value["DistrictIDSet"];
                if (objDistrict is List<object>)
                {
                    DistrictIDSet.Clear();
                    foreach (int id in objDistrict as List<object>)
                    {
                        DistrictIDSet.Add(id);
                    }
                }
                else if (objDistrict is List<int>)
                {
                    DistrictIDSet = value["DistrictIDSet"] as List<int>;
                }
            }
        }
    }
}
