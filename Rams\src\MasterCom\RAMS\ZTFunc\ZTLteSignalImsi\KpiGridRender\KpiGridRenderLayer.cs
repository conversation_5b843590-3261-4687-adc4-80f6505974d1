﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;

using MasterCom.Util;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using GridDataHub = MasterCom.RAMS.ZTFunc.KpiGridRenderByRegion.GridDataHub;

namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    public class KpiGridRenderLayer : LayerBase
    {
        public KpiGridRenderLayer(): base("区域内栅格")
        {
            mainModel.MainForm.GetMapForm().ToolInfoClickEvent += InfoTool_Click;
        }

        public void SetMatrixAndColor(GridMatrix<GridDataHub> gridMatrix, GridColorModeItem colorMode)
        {
            this.gridMatrix = gridMatrix;
            this.colorMode = colorMode;

            foreach (GridDataHub gdh in gridMatrix)
            {
                GridAttr ga = new GridAttr();
                ga.Value = (float)GetGridValue(gdh);
                gdh.Tag = ga;
            }
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (this.gridMatrix == null)
            {
                return;
            }

            foreach (GridDataHub gdh in gridMatrix)
            {
                GridAttr ga = gdh.Tag as GridAttr;
                ga.IsVisible = false;

                Color gridColor = this.colorMode.GetColor(ga.Value);
                if (gridColor == Color.Empty)
                {
                    continue;
                }

                DbPoint topLeft = new DbPoint(gdh.LTLng, gdh.LTLat);
                DbPoint bottomRight = new DbPoint(gdh.BRLng, gdh.BRLat);
                PointF pfTopLeft, pfBottomRight;
                gisAdapter.ToDisplay(topLeft, out pfTopLeft);
                gisAdapter.ToDisplay(bottomRight, out pfBottomRight);
                RectangleF rectF = new RectangleF(pfTopLeft.X, pfTopLeft.Y, 
                    pfBottomRight.X - pfTopLeft.X, pfBottomRight.Y - pfTopLeft.Y);

                if (selectedGrid != null && gdh == selectedGrid)
                {
                    graphics.FillRectangle(new SolidBrush(Color.FromArgb(80, gridColor)), rectF);
                }
                else
                {
                    graphics.FillRectangle(new SolidBrush(gridColor), rectF);
                }

                ga.IsVisible = true;
            }
        }

        public override void LayerDispose()
        {
            base.LayerDispose();
            mainModel.MainForm.GetMapForm().ToolInfoClickEvent -= InfoTool_Click;
        }

        private void InfoTool_Click(double lng, double lat, MapOperation2 mop2, ref List<string> titles, ref List<string> infos)
        {
            this.selectedGrid = null;
            GridDataHub curGdh = null;
            foreach (GridDataHub gdh in gridMatrix)
            {
                if (!(gdh.Tag as GridAttr).IsVisible)
                {
                    continue;
                }

                if (gdh.LTLng <= lng && lng <= gdh.BRLng
                    && gdh.BRLat <= lat && lat <= gdh.LTLat)
                {
                    curGdh = gdh;
                    break;
                }
            }

            if (curGdh != null)
            {
                //string road = GISManager.GetInstance().GetRoadPlaceDesc(curGdh.CenterLng, curGdh.CenterLat);
                double value = GetGridValue(curGdh);
                StringBuilder sb = new StringBuilder();
                sb.AppendLine("中心经度: " + curGdh.CenterLng);
                sb.AppendLine("中心纬度: " + curGdh.CenterLat);
                //sb.AppendLine("道路名称: " + road);
                sb.AppendLine("渲染指标: " + (double.IsNaN(value) ? "null" : value.ToString()));

                titles.Add("栅格信息");
                infos.Add(sb.ToString());

                this.selectedGrid = curGdh;
            }

            base.Invalidate();
        }

        private double GetGridValue(GridDataHub gdh)
        {
            double value = gdh.DataHub.CalcFormula(Model.CarrierType.ChinaMobile, -1, this.colorMode.formula);
            if (double.IsNaN(value))
            {
                value = gdh.DataHub.CalcFormula(Model.CarrierType.ChinaMobile, 1, this.colorMode.formula);
                if (double.IsNaN(value))
                {
                    value = gdh.DataHub.CalcFormula(Model.CarrierType.ChinaMobile, 2, this.colorMode.formula);
                }
            }
            return value;
        }

        private GridMatrix<GridDataHub> gridMatrix;

        private GridColorModeItem colorMode;

        private GridDataHub selectedGrid;

        private class GridAttr
        {
            public bool IsVisible
            {
                get;
                set;
            }

            public float Value
            {
                get;
                set;
            }
        }
    }
}
