﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTSiteRoadDistanceListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listViewDistance = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSiteName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSiteLong = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSiteLat = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTbName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRoadName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRoadHeadLong = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRoadHeadLat = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRoadTailLong = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRoadTailLat = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDistance = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.listViewDistance)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // listViewDistance
            // 
            this.listViewDistance.AllColumns.Add(this.olvColumnSN);
            this.listViewDistance.AllColumns.Add(this.olvColumnSiteName);
            this.listViewDistance.AllColumns.Add(this.olvColumnCellName);
            this.listViewDistance.AllColumns.Add(this.olvColumnSiteLong);
            this.listViewDistance.AllColumns.Add(this.olvColumnSiteLat);
            this.listViewDistance.AllColumns.Add(this.olvColumnTbName);
            this.listViewDistance.AllColumns.Add(this.olvColumnRoadName);
            this.listViewDistance.AllColumns.Add(this.olvColumnRoadHeadLong);
            this.listViewDistance.AllColumns.Add(this.olvColumnRoadHeadLat);
            this.listViewDistance.AllColumns.Add(this.olvColumnRoadTailLong);
            this.listViewDistance.AllColumns.Add(this.olvColumnRoadTailLat);
            this.listViewDistance.AllColumns.Add(this.olvColumnDistance);
            this.listViewDistance.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnSiteName,
            this.olvColumnCellName,
            this.olvColumnSiteLong,
            this.olvColumnSiteLat,
            this.olvColumnTbName,
            this.olvColumnRoadName,
            this.olvColumnRoadHeadLong,
            this.olvColumnRoadHeadLat,
            this.olvColumnRoadTailLong,
            this.olvColumnRoadTailLat,
            this.olvColumnDistance});
            this.listViewDistance.ContextMenuStrip = this.contextMenuStrip;
            this.listViewDistance.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewDistance.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewDistance.FullRowSelect = true;
            this.listViewDistance.GridLines = true;
            this.listViewDistance.Location = new System.Drawing.Point(0, 0);
            this.listViewDistance.MultiSelect = false;
            this.listViewDistance.Name = "listViewDistance";
            this.listViewDistance.OwnerDraw = true;
            this.listViewDistance.ShowGroups = false;
            this.listViewDistance.Size = new System.Drawing.Size(1080, 415);
            this.listViewDistance.TabIndex = 1;
            this.listViewDistance.UseCompatibleStateImageBehavior = false;
            this.listViewDistance.View = System.Windows.Forms.View.Details;
            this.listViewDistance.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnSiteName
            // 
            this.olvColumnSiteName.HeaderFont = null;
            this.olvColumnSiteName.Text = "基站名称";
            this.olvColumnSiteName.Width = 100;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 100;
            // 
            // olvColumnSiteLong
            // 
            this.olvColumnSiteLong.HeaderFont = null;
            this.olvColumnSiteLong.Text = "基站经度";
            this.olvColumnSiteLong.Width = 80;
            // 
            // olvColumnSiteLat
            // 
            this.olvColumnSiteLat.HeaderFont = null;
            this.olvColumnSiteLat.Text = "基站纬度";
            this.olvColumnSiteLat.Width = 80;
            // 
            // olvColumnTbName
            // 
            this.olvColumnTbName.HeaderFont = null;
            this.olvColumnTbName.Text = "图层名称";
            this.olvColumnTbName.Width = 100;
            // 
            // olvColumnRoadName
            // 
            this.olvColumnRoadName.HeaderFont = null;
            this.olvColumnRoadName.Text = "道路名称";
            this.olvColumnRoadName.Width = 100;
            // 
            // olvColumnRoadHeadLong
            // 
            this.olvColumnRoadHeadLong.HeaderFont = null;
            this.olvColumnRoadHeadLong.Text = "道路起点经度";
            this.olvColumnRoadHeadLong.Width = 90;
            // 
            // olvColumnRoadHeadLat
            // 
            this.olvColumnRoadHeadLat.HeaderFont = null;
            this.olvColumnRoadHeadLat.Text = "道路起点纬度";
            this.olvColumnRoadHeadLat.Width = 90;
            // 
            // olvColumnRoadTailLong
            // 
            this.olvColumnRoadTailLong.HeaderFont = null;
            this.olvColumnRoadTailLong.Text = "道路终点经度";
            this.olvColumnRoadTailLong.Width = 90;
            // 
            // olvColumnRoadTailLat
            // 
            this.olvColumnRoadTailLat.HeaderFont = null;
            this.olvColumnRoadTailLat.Text = "道路终点纬度";
            this.olvColumnRoadTailLat.Width = 90;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "距离(米)";
            this.olvColumnDistance.Width = 84;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportToExcel.Text = "导出Excel";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // ZTSiteRoadDistanceListForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1080, 415);
            this.Controls.Add(this.listViewDistance);
            this.Name = "ZTSiteRoadDistanceListForm";
            this.Text = "基站道路距离分析结果";
            ((System.ComponentModel.ISupportInitialize)(this.listViewDistance)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView listViewDistance;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnSiteName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnSiteLong;
        private BrightIdeasSoftware.OLVColumn olvColumnSiteLat;
        private BrightIdeasSoftware.OLVColumn olvColumnTbName;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadHeadLong;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadHeadLat;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadTailLong;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadTailLat;
    }
}