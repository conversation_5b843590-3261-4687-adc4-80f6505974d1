﻿using System;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Collections.Generic;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTCellCoverageRangeAnaSetForm : BaseDialog
    {
        public ZTCellCoverageRangeAnaSetForm()
        {
            InitializeComponent();
        }

        public ZTCellCoverageRangeAnaCondition GetCondition()
        {
            ZTCellCoverageRangeAnaCondition condition = new ZTCellCoverageRangeAnaCondition();

            if(rdbServCell.Checked)
            {
                condition.CellFilterType = 2;
            }
            condition.NbDiffServ = (int)numNbDiffServ.Value;
            if (this.rdbOccupiedCell.Checked)
            {
                condition.SampleColorType = 2;
            }

            if (MainModel.SelectedCells != null && MainModel.SelectedCells.Count > 0)
            {
                addCell(condition, MainModel.SelectedCells);
            }
            else if (MainModel.SelectedTDCells != null && MainModel.SelectedTDCells.Count > 0)
            {
                addCell(condition, MainModel.SelectedTDCells);
            }
            else if (MainModel.SelectedLTECells != null && MainModel.SelectedLTECells.Count > 0)
            {
                addCell(condition, MainModel.SelectedLTECells);
            }
            else if (MainModel.SelectedWCells != null && MainModel.SelectedWCells.Count > 0)
            {
                addCell(condition, MainModel.SelectedWCells);
            }
            else if (MainModel.SelectedNRCells != null && MainModel.SelectedNRCells.Count > 0)
            {
                addCell(condition, MainModel.SelectedNRCells);
            }

            return condition;
        }

        private void addCell<T>(ZTCellCoverageRangeAnaCondition condition, List<T> cellList)
        {
            foreach (T cell in cellList)
            {
                if (cell is ICell)
                {
                    ICell iCell = cell as ICell;
                    condition.CellList.Add(iCell);
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void ZTCellCoverageRangeAnaSetForm_Load(object sender, EventArgs e)
        {
            //
        }
    }
}
