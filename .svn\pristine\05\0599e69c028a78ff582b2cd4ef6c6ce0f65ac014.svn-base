<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="contextMenuStrip2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>182, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAICAAAAEACACoCAAAJgAAABAQAAABACAAaAQAAM4IAAAoAAAAIAAAAEAAAAABAAgAAAAAAIAE
        AAAAAAAAAAAAAAABAAAAAAAAAAABAP///wD//8wA/8zMAP//mQDM/5kA/8yZAMzMmQDMmZkA//9mAMz/
        ZgD/zGYAzMxmAP+ZZgDMmWYAmZlmAP//MwD/zDMAzMwzAP+ZMwDMmTMAmZkzAP9mMwDMZjMAmWYzAGZm
        MwD/MzMAzDMzAJkzMwBmMzMAzAAzAJkAMwBmADMA/8wAAMzMAACZzAAA/5kAAMyZAACZmQAAzGYAAJlm
        AABmZgAAzDMAAJkzAABmMwAAMzMAAMwAAACZAAAAZgAAADMAAAAAABEAABEAAN0AAAC7AAAAqgAAAIgA
        AAB3AAAAVQAAAEQAAAAiAAAAEQAAAO7u7gDd3d0AERERAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAEBAQEBAQEBAQEBAOzw7MTEzPDw8QDxAQEBAQEBAQEBAQEBAQEBA
        QEA8PDs/MCsoGzU7Ozs7Mzs8QEBAQEBAQEBAQEBAQEA7Ozs6OyoOBBEmLjE8PDw8PEA8QEBAQEBAQEBA
        QEBAOzs7LSwnBAIUKzk8Ozw7Ozw8PDxAPEBAQEBAQEBAQDs7OzEsKAYEHDkpFSIVGC07Ozw7PDw8PEBA
        QEBAQEA7MTs7LCsMESAmBAcMFBUsOzsxOzI7PDM8QDxAQEBAOzE7MTE6FBEdEQIPLCwpKCksMDYrHDEt
        MTw8QEBAQEAxMTExMTYlHBQMGDomJissMSwxMTAqFDU6LTs8PEBAMTExMTE6KCosDBgtLCwwMDk6MTEt
        MTErFCUrLTNAQDExMTE6MTgqMBUVOSwrHxsbGxc3ODkmFC0nDBQsOzBAMTE6MTEsHy4tGCg6LysXJRMR
        FBMnNiwpFCwrBAw3PDwxOjo6Oig4OCwoLDcqJyIMCQkEEiEnNywVCSwXAhgxQEA6LDkwJjAwLCw3KycS
        CwQCAgQEEhQrODEPCykHBis8QDo6OSwSLDAsLC8nFAoJAgEBAgQHFBc2MSw+JhcCKzFAOio5LAksLCw5
        LycUEAQCAQECAgkRFDQtMQcSLAknO0AuJy4dBCgoLTAvKAwLCQIBAj0ECREUGzkrDgQ4IxsxQCoLJSsE
        FSgrOTcbFBILBQIDBAkJEiUeOSYZCTE1NTtALgwJKwkHKygwODUWIRILBwkJEBIiGB4sKSsJLTo6O0Au
        DQIXFAYdJigwGxoXIRIMEhARFBcnLzksKQsxLDo8QDkqBAsrCQg5LCsvGxcXFCUSJSQWJys5LCwmFTEo
        PDtAOTglBhErEg4rLCwvKhsnJScnGhc1OTAtOiwsMS0xPEAwMCsnCycrFRgsMDA3NjUqGxsbLzgdLDow
        LToxOzs7QDAwKygnFycvLBcmKzA4Nzc3NzcwLCgxKys6MTs7ODxAMDAwODcqGxsvOSspKywsMDk4LCwY
        LDg2OjExMTs7QEBAMDA4MDgwOC8vOSssKywnKCkrDCwrGzcxMTExOztAQEBAMDg4ODgwOCgrMDAsKx0U
        DAwrOCcXMDoxMTsxQEBAQEA4MDg4ODg4LCcSEAQCPQQJFSsSFDcsMTExMTtAQEBAQEA4ODg3ODg4ODgo
        GBUoLysTBhEsLDo6MTE7QEBAQEBAQEA4ODg4ODgrLDgwLiQJAQISLyg6OjExQEBAQEBAQEBAQEBAODg4
        ODgvNyoXEgYLGyw5OTE6QEBAQEBAQEBAQEBAQEBAQDAwODAwOC4qLi45OTowQEBAQEBAQEBAQEBAQEBA
        QEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQED/4AH//wAAf/wAAB/4AAAP8AAAB+AAAAPAAAADwAAAAYAA
        AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAGAAAABwAAAA8AAAAPgAAAH8AAAD/gAAB/+AAB//4AB/ygAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAA
        AAAAAAAAAAAAAAAAAAAhAAAAIQAAARgAAAAXAAAAFgAAPAMAAJgbAAD+YhgJ+zYKBP8SAQD7DAMA/QgA
        AJIBAAAAAAAAAAAAAAcGAAAAIQAAAyEAAAAVAAAOHAEAsiACAP98PBv/26hh/rxtEP9UAAD/EAAB/xQC
        AP0OAAD/BgAA4QEAAGAGAAAABAAABiIAAAYfAAAUIwAAyCUAAP+BUQ/76bpj/bKJU/+SfDP+k40h/0gq
        Df4gAAD/EgAD/AYAAP8HAQD/CQAAXgQAAAAxAAAALQAAtikAAP9CBAD4xHsg/sGSQP6rm1j+iXog/4Vh
        B/9NLAD/WQQA/n4eD/9eIgj6GgAA/QYCAecFAAAOMgAAvTQAAPsrAAD8dhIB/qNJC/6Vgzr/WyQC/4gf
        CP+KFQ7/YwoG/0ELAP90QQj+umYV/59vGfweBAD/EwAAlzsCAP88AgD/SxQA/5AICP96RAz/dDIF/6Ae
        AP/Sjif//cpS/+u1Pf+6SAD/eFAN/5x8Hv7Sp1j/klg5/gYAAP8uBgD8RgIA/ntJBf91MgP+Xx0A/5kh
        AP/Vox//9fqH//7/3//4/6H/1rBA/4IHAP97Yzv/t6RO/tyvcf8vAgD7aRAF/5oVAP+gbjP/nIMc/1IM
        AP+cJwX/3tJD//3+rP/89///+/nD//vmUv+2PBH/azEc/76uW//BgRz/ahEM/6sOBP/nmj//zJ1D/8Cl
        Tv9zKAD/lQsR/+uWKP/i4l3/7+in//z/Zf/X1iv/sTAe/3pCBf+voj//awkA/0oCBf9OAAD+04BO/+q6
        Xf/Hllj/gVQX/44VBv/eQSf/16Al/8rFJv/0txr/2msd/34SAf9eNQD/lHoe/ksjAv8UAAD7NwAC/ngV
        AP7loTP/y4Ed/ptmKf9zNwb/iQ4F/7geDf/DSQz/zTAh/5oSC/9TGwf/WBwA/kcYAP8yDAD+JAAA/zoA
        AP9pAgD/hyAA/rw6Ef+fGA/+hzEJ/3cxAP9tDAD/dQAA/3IRAv9zRRH/gCEE/nEKAP8qAAD8LQAA/ykA
        AJdNAABCKwAA5nIAAP5tAAD6eAEC/4smAP6OSAr/l2Mr/7CLRf+vnDD/l1IT/rlBFv9KAAL6LgAA/SYA
        AOciAAAObwAAAEAAAF9bAQD/hgEA/3cBAP6CIAD/o2QQ/7yXU//OnFj/4bBk/9qiOP96OAj+QAIA/yoA
        AP8xAABeIgAAAFgAAApBAAAACgAAYTcAANppAAD7eQMA/nIAAP+lFwD/6549/9SHa/90JAf+QwIA+xkA
        ANoEAABhGAAAAAsAAAb/AAAAAAAACwAAAAAAAAAqDgAA2lkAAPtOAgH9exEH/6QAAP9SAAD9JwAA+xUA
        AdoAAAAqAAAAAAAAAAsmAAAA+A8AAOADAADAAQAAgAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAgAEAAIABAADAAwAA8A8AAA==
</value>
  </data>
</root>