﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MapWinGIS;

namespace MasterCom.MTGis
{
    public class LinePatternItem
    {
        public String Name { get; set; }
        public bool UseLinePattern { get; set; } = false;
        public float LineWidth { get; set; } = 2.0f;
        public Color LineColor { get; set; } = Color.FromArgb(230, 220, 220);
        public LinePattern LinePattern { get; set; }
        public override string ToString()
        {
            return Name;
        }
        public static List<LinePatternItem> InitCustomLineType()
        {
            Utils utils = new Utils();
            List<LinePatternItem> retlist = new List<LinePatternItem>();
            LinePatternItem item = new LinePatternItem();
            item.UseLinePattern = false;
            item.Name = "基本";
            retlist.Add(item);
            item = new LinePatternItem();
            item.Name = "铁路";
            item.UseLinePattern = true;
            item.LinePattern = new LinePattern();
            item.LinePattern.AddLine(utils.ColorByName(tkMapColor.Black), 3.0f, tkDashStyle.dsSolid);
            item.LinePattern.AddLine(utils.ColorByName(tkMapColor.Gray), 1.0f, tkDashStyle.dsSolid);
            retlist.Add(item);
            item = new LinePatternItem();
            item.Name = "高速";
            item.UseLinePattern = true;
            item.LinePattern = new LinePattern();
            item.LinePattern.AddLine(utils.ColorByName(tkMapColor.Gray), 5.0f, tkDashStyle.dsSolid);
            item.LinePattern.AddLine(utils.ColorByName(tkMapColor.Yellow), 4.0f, tkDashStyle.dsSolid);
            retlist.Add(item);
            item = new LinePatternItem();
            item.Name = "国道";
            item.UseLinePattern = true;
            item.LinePattern = new LinePattern();
            item.LinePattern.AddLine(utils.ColorByName(tkMapColor.Gray), 4.0f, tkDashStyle.dsSolid);
            item.LinePattern.AddLine((uint)ColorTranslator.ToOle(Color.FromArgb(255, 0, 255)), 3.0f, tkDashStyle.dsSolid);
            retlist.Add(item);
            item = new LinePatternItem();
            item.Name = "省道";
            item.UseLinePattern = true;
            item.LinePattern = new LinePattern();
            item.LinePattern.AddLine(utils.ColorByName(tkMapColor.Gray), 3.0f, tkDashStyle.dsSolid);
            item.LinePattern.AddLine(utils.ColorByName(tkMapColor.Orange), 2.0f, tkDashStyle.dsSolid);
            retlist.Add(item);
            item = new LinePatternItem();
            item.Name = "县级路";
            item.UseLinePattern = false;
            item.LineWidth = 2;
            item.LineColor = Color.FromArgb(0, 255, 0);
            retlist.Add(item);
            item = new LinePatternItem();
            item.Name = "乡村路";
            item.UseLinePattern = false;
            item.LineWidth = 2;
            item.LineColor = Color.FromArgb(240, 150, 150);
            retlist.Add(item);

            return retlist;
        }
    }
}
