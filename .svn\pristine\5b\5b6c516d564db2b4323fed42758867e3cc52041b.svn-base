﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.NOP
{
    public class LowTaskInfoQuery : DIYSQLBase
    {
        readonly int taskVersionId;
        readonly int taskId;
        public LowTask LowTaskInfo { get; set; }
        public LowTaskInfoQuery(int taskVersionId, int taskId)
            : base(MainModel.GetInstance())
        {
            this.taskVersionId = taskVersionId;
            this.taskId = taskId;
        }
        public override string Name
        {
            get { return "LowTaskInfoQuery"; }
        }
        protected override string getSqlTextString()
        {
            NopDBSetting nopDBSetting = NopDbManager.Instance.NopDbSetting;
#if Ningxia
            string strColumn = " 派发地市,规划图层绘制 ";
#else
            string strColumn = " 测试地市,规划图层绘制 ";
#endif
            if (nopDBSetting != null)
            {
                return string.Format(@"select {0} from {1}.dbo.task_{2} 
 where _id = {3} ", strColumn, nopDBSetting.DbFullName, taskVersionId, taskId);
            }
            else
            {
                return string.Format(@"select {0} from MTNOH_AAA_Platform.dbo.task_{1} 
 where _id = {2} ", strColumn, taskVersionId, taskId);
            }
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vType = new E_VType[2];
            vType[0] = E_VType.E_String;
            vType[1] = E_VType.E_String;
            return vType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            int index = 0;
            int progress = 0;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string districtName = package.Content.GetParamString();
                    string planGisJsonData = package.Content.GetParamString();

                    LowTaskInfo = new LowTask(taskVersionId, taskId);
                    LowTaskInfo.DistrictName = districtName;
                    LowTaskInfo.PlanGisJsonData = planGisJsonData;

                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                setProgressPercent(ref index, ref progress);
            }
        }
    }
    public class LowTask
    {
        public LowTask(int taskVersionId, int taskId)
        {
            this.TaskVersionId = taskVersionId;
            this.Id = taskId;
        }
        public int Id { get; set; }
        public int TaskVersionId { get; set; }
        public string DistrictName { get; set; }
        public string PlanGisJsonData { get; set; }
    }
}
