﻿namespace MasterCom.RAMS.Func.ProblemCell
{
    partial class ProblemCellForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCell = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLacCi = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandoverTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPointCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLog = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPESQ = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLevSub = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxQualSub = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSpeechCodec = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCloseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnSN);
            this.treeListView.AllColumns.Add(this.olvColumnCell);
            this.treeListView.AllColumns.Add(this.olvColumnLacCi);
            this.treeListView.AllColumns.Add(this.olvColumnHandoverTime);
            this.treeListView.AllColumns.Add(this.olvColumnPointCount);
            this.treeListView.AllColumns.Add(this.olvColumnLog);
            this.treeListView.AllColumns.Add(this.olvColumnPESQ);
            this.treeListView.AllColumns.Add(this.olvColumnRxLevSub);
            this.treeListView.AllColumns.Add(this.olvColumnRxQualSub);
            this.treeListView.AllColumns.Add(this.olvColumnLongitude);
            this.treeListView.AllColumns.Add(this.olvColumnLatitude);
            this.treeListView.AllColumns.Add(this.olvColumnSpeechCodec);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCell,
            this.olvColumnLacCi,
            this.olvColumnHandoverTime,
            this.olvColumnPointCount,
            this.olvColumnLog,
            this.olvColumnPESQ,
            this.olvColumnRxLevSub,
            this.olvColumnRxQualSub,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnSpeechCodec});
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.HeaderWordWrap = true;
            this.treeListView.IsNeedShowOverlay = false;
            this.treeListView.Location = new System.Drawing.Point(0, 0);
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(1309, 547);
            this.treeListView.TabIndex = 3;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 40;
            // 
            // olvColumnCell
            // 
            this.olvColumnCell.AspectName = "";
            this.olvColumnCell.HeaderFont = null;
            this.olvColumnCell.Text = "小区";
            this.olvColumnCell.Width = 70;
            // 
            // olvColumnLacCi
            // 
            this.olvColumnLacCi.HeaderFont = null;
            this.olvColumnLacCi.Text = "LAC-CI";
            this.olvColumnLacCi.Width = 90;
            // 
            // olvColumnHandoverTime
            // 
            this.olvColumnHandoverTime.HeaderFont = null;
            this.olvColumnHandoverTime.Text = "切换次数";
            // 
            // olvColumnPointCount
            // 
            this.olvColumnPointCount.AspectName = "";
            this.olvColumnPointCount.HeaderFont = null;
            this.olvColumnPointCount.Text = "采样点个数";
            this.olvColumnPointCount.Width = 70;
            // 
            // olvColumnLog
            // 
            this.olvColumnLog.HeaderFont = null;
            this.olvColumnLog.Text = "LOG";
            this.olvColumnLog.Width = 90;
            // 
            // olvColumnPESQ
            // 
            this.olvColumnPESQ.HeaderFont = null;
            this.olvColumnPESQ.Text = "PESQ";
            this.olvColumnPESQ.Width = 50;
            // 
            // olvColumnRxLevSub
            // 
            this.olvColumnRxLevSub.HeaderFont = null;
            this.olvColumnRxLevSub.Text = "RxLevSub";
            // 
            // olvColumnRxQualSub
            // 
            this.olvColumnRxQualSub.HeaderFont = null;
            this.olvColumnRxQualSub.Text = "RxQualSub";
            this.olvColumnRxQualSub.Width = 74;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 120;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 120;
            // 
            // olvColumnSpeechCodec
            // 
            this.olvColumnSpeechCodec.HeaderFont = null;
            this.olvColumnSpeechCodec.Text = "编码方式";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCloseAll,
            this.toolStripMenuItem1,
            this.miExport2Xls});
            this.contextMenuStrip.Name = "contextMenuStrip1";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 76);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCloseAll
            // 
            this.miCloseAll.Name = "miCloseAll";
            this.miCloseAll.Size = new System.Drawing.Size(129, 22);
            this.miCloseAll.Text = "全部合并";
            this.miCloseAll.Click += new System.EventHandler(this.miCloseAll_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(129, 22);
            this.miExport2Xls.Text = "导出Excel";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // ProblemCellForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1309, 547);
            this.ContextMenuStrip = this.contextMenuStrip;
            this.Controls.Add(this.treeListView);
            this.Name = "ProblemCellForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "问题小区";
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCell;
        private BrightIdeasSoftware.OLVColumn olvColumnLacCi;
        private BrightIdeasSoftware.OLVColumn olvColumnPointCount;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnPESQ;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevSub;
        private BrightIdeasSoftware.OLVColumn olvColumnRxQualSub;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCloseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private BrightIdeasSoftware.OLVColumn olvColumnLog;
        private BrightIdeasSoftware.OLVColumn olvColumnSpeechCodec;
        private BrightIdeasSoftware.OLVColumn olvColumnHandoverTime;
    }
}