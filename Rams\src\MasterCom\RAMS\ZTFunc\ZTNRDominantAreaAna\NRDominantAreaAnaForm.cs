﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTNRDominantAreaAna
{
    public partial class NRDominantAreaAnaForm : MinCloseForm
    {
        public NRDominantAreaAnaForm()
        {
            InitializeComponent();
        }

        public void FillData(List<NRDominantAreaResult> resultList, List<NRDominantAreaFileResult> fileResultList)
        {
            gcSence.DataSource = resultList;
            gcSence.RefreshDataSource();

            gcLogDetail.DataSource = fileResultList;
            gcLogDetail.RefreshDataSource();
        }

        private void btnExportSence_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gvSence);
        }

        private void btnExportLogDetail_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gvLogDetail);
        }
    }
}
