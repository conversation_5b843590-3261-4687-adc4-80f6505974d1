﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class AreaKpiQueryDirectly : AreaKpiBaseQuery
    {
        public Dictionary<AreaBase, CKpiValue> KpiValueDic
        {
            get;
            private set;
        }

        protected List<TemplateColumn> tmplaColumns;

        public AreaKpiQueryDirectly()
        {
            KpiValueDic = new Dictionary<AreaBase, CKpiValue>();
            tmplaColumns = new List<TemplateColumn>();
        }

        public void SetColumns(List<TemplateColumn> columns)
        {
            tmplaColumns = columns;
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<StatImgDefItem> curImgColumnDef
           , KPIStatDataBase singleStatData)
        {
            int areaTypeID = package.Content.GetParamInt();
            int areaSubID = package.Content.GetParamInt();
            AreaBase area = getArea(areaTypeID, areaSubID);
            if (area == null)
            {
                return;
            }
            fillStatData(package, curImgColumnDef, singleStatData);
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);
            AreaKPIDataGroup<AreaBase> group;
            if (!AreaKpiMap.TryGetValue(area, out group))
            {
                group = new AreaKPIDataGroup<AreaBase>(area, IsStatLatestOnly);
                AreaKpiMap[area] = group;
            }
            group.AddStatData(fi, singleStatData);
        }

        protected override void handleStatEvent(Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);

            AreaBase area = getArea(evt.AreaTypeID, evt.AreaID);
            if (area == null)
            {
                return;
            }

            AreaKPIDataGroup<AreaBase> group;
            if (!AreaKpiMap.TryGetValue(area, out group))
            {
                group = new AreaKPIDataGroup<AreaBase>(area, IsStatLatestOnly);
                AreaKpiMap[area] = group;
            }
            group.AddStatData(fi, eventData);
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            foreach (Dictionary<int, AreaBase> idAreaDic in typePairMap.Values)
            {
                foreach (AreaBase area in idAreaDic.Values)
                {
                    CKpiValue kpi;
                    if (!KpiValueDic.TryGetValue(area, out kpi))
                    {
                        kpi = new CKpiValue(area);
                        KpiValueDic[area] = kpi;
                    }
                    AreaKPIDataGroup<AreaBase> group;
                    if (!AreaKpiMap.TryGetValue(area, out group))
                    {
                        group = new AreaKPIDataGroup<AreaBase>(area, IsStatLatestOnly);
                        AreaKpiMap[area] = group;
                    }
                    group.FinalMtMoGroup();
                    kpi.CalcValue(AreaKpiMap[area], tmplaColumns);
                }
            }
        }
    }
}
