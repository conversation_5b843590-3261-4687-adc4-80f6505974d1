﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptManager_TianJin : MultiStationAutoAcceptManager
    {
        //基站信息验收结果
        public BtsAcceptFileInfo_TianJin AcceptBtsFileInfo { get; set; }

        //小区信息验收结果
        public Dictionary<string, CellAcceptFileInfo_TianJin> AcceptCellFileInfoList { get; set; } = new Dictionary<string, CellAcceptFileInfo_TianJin>();

        //多文件临时结果属性
        public Dictionary<string, List<CellAcceptKpiAna_TianJin>> CellsResListTmp { get; set; } = new Dictionary<string, List<CellAcceptKpiAna_TianJin>>();

        protected new virtual List<CellAcceptKpiAna_TianJin> getAcceptAnaList(LTEBTSType btsType)
        {
            List<CellAcceptKpiAna_TianJin> acceptAnaList = null;
            if (btsType == LTEBTSType.Outdoor)
            {
                acceptAnaList = new List<CellAcceptKpiAna_TianJin>()
                {
                   new AcpAutoFtpDL_TianJin(),
                   new AcpAutoFtpUL_TianJin(),
                   new AcpAutoPing_TianJin(),
                   new AcpAutoCSFB_TianJin(),
                   new AcpAutoCellBtsCoverPic_TianJin(),
                   new AcpAutoCellCoverTest_TianJin(),
                   new AcpAutoInterOperation_TianJin()
                };
            }
            return acceptAnaList;
        }

        public virtual List<CellAcceptKpiAna_TianJin> getMutilCellAcceptAnaList(LTEBTSType btsType)
        {
            List<CellAcceptKpiAna_TianJin> acceptAnaList = null;
            if (btsType == LTEBTSType.Outdoor)
            {
                acceptAnaList = new List<CellAcceptKpiAna_TianJin>()
                {
                   new AcpAutoHandover_TianJin(),
                   new AcpAutoAntenna_TianJin(),
                   new AcpAutoCellCoverPic_TianJin(),
                   new AcpAutoOverlap_TianJin()
                };
            }
            return acceptAnaList;
        }

        BtsWorkParam_TianJin curBtsInfo;
        public void AnalyzeFile(MasterCom.RAMS.Model.FileInfo fileInfo, DTFileDataManager fileManager, BtsWorkParam_TianJin curBtsInfo)
        {
            try
            {
                this.curBtsInfo = curBtsInfo;
                fileInfo.Momt = fileManager.MoMtFlag;
                if (fileManager.DTDatas.Count > 0)
                {
                    fileInfo = fileManager.GetFileInfo();//网络体检查到的FileInfo信息不全，回放文件得到的FileInfo较全
                }

                anaFile(fileInfo, fileManager);
            }
            catch (Exception ex)
            {
                reportInfo(ex);
            }
        }

        protected virtual void anaFile(FileInfo fileInfo, DTFileDataManager fileManager)
        {
            //1.根据文件判断是否需要分析多个小区
            bool anaMultiCells = false;
            if (fileManager.FileName.Contains("DT切换"))
            {
                anaMultiCells = true;
            }

            //2.判断文件是否合理(是否能关联到文件名对应的小区或基站)
            LTECell targetCell;
            bool isValidFile = judgeFileValid(fileManager, anaMultiCells, out targetCell);
            if (!isValidFile)
            {
                reportInfo(string.Format("文件{0}未找到目标小区或基站", fileInfo.Name));
                return;
            }

            //3.分析文件
            if (anaMultiCells)
            {
                List<CellAcceptKpiAna_TianJin> acceptMutilCellAnaList = getMutilCellAcceptAnaList(targetCell.Type);
                foreach (var cell in curBtsInfo.Bts.Cells)
                {
                    anaOneCellResByFile(fileInfo, fileManager, cell, acceptMutilCellAnaList);
                }
            }
            List<CellAcceptKpiAna_TianJin> acceptAnaList = getAcceptAnaList(targetCell.Type);
            anaOneCellResByFile(fileInfo, fileManager, targetCell, acceptAnaList);
        }

        private void anaOneCellResByFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell, List<CellAcceptKpiAna_TianJin> acceptAnaList)
        {
            foreach (CellAcceptKpiAna_TianJin acp in acceptAnaList)
            {
                if (acp.IsValidFile(fileInfo))
                {
                    doStatWithData(acp, fileInfo, fileManager, targetCell);
                }
            }
        }

        protected virtual bool judgeFileValid(DTFileDataManager fileManager, bool anaMultiCells, out LTECell targeCell)
        {
            targeCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = GetTpSrcCell(tp);

                if (isSrcTestCell(cell, fileManager.FileName, anaMultiCells))
                {
                    targeCell = cell;
                    break;
                }
            }
            if (targeCell != null && targeCell.Type == LTEBTSType.Outdoor)
            {
                return true;
            }
            return false;
        }

        protected void doStatWithData(CellAcceptKpiAna_TianJin acp, FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            if (acp.IsAnaBtsInfo == AnaType.AnaCell)
            {
                CellAcceptFileInfo_TianJin AcceptFileInfo;
                if (!AcceptCellFileInfoList.TryGetValue(targetCell.Token, out AcceptFileInfo))
                {
                    AcceptFileInfo = new CellAcceptFileInfo_TianJin(fileInfo, targetCell);
                    AcceptCellFileInfoList.Add(targetCell.Token, AcceptFileInfo);
                }

                Dictionary<KpiKey_TJ, object> kpiInfoDic = acp.GetFileKpiInfos(fileInfo, fileManager, targetCell);
                setValidKpiRes(AcceptFileInfo.AcceptKpiDic, kpiInfoDic);
            }
            else if (acp.IsAnaBtsInfo == AnaType.AnaMultiCells)
            {
                if (AcceptBtsFileInfo == null)
                {
                    AcceptBtsFileInfo = new BtsAcceptFileInfo_TianJin(fileInfo, curBtsInfo.Bts);
                }
                Dictionary<KpiKey_TJ, object> kpiInfoDic = acp.GetFileKpiInfos(fileInfo, fileManager, targetCell);
                setValidKpiRes(AcceptBtsFileInfo.AcceptKpiDic, kpiInfoDic);
            }
            else if (acp.IsAnaBtsInfo == AnaType.AnaMultiFiles)
            {
                string typeName = acp.GetType().ToString();
                List<CellAcceptKpiAna_TianJin> acpList;
                if (!CellsResListTmp.TryGetValue(typeName, out acpList))
                {
                    acpList = new List<CellAcceptKpiAna_TianJin>();
                    CellsResListTmp.Add(typeName, acpList);
                }
                acpList.Add(acp);
                acp.GetFileKpiInfos(fileInfo, fileManager, targetCell);
            }
        }

        private void setValidKpiRes(Dictionary<uint, object> acceptKpiDic, Dictionary<KpiKey_TJ, object> kpiInfoDic)
        {
            foreach (KpiKey_TJ key in kpiInfoDic.Keys)
            {
                object valueObj = kpiInfoDic[key];
                if (valueObj is double)
                {
                    double valueDouble = (double)valueObj;
                    if (valueDouble == double.MinValue)
                    {
                        valueObj = double.NaN;
                    }
                    //else
                    //{
                    //    valueObj = Math.Round(valueDouble, 4);
                    //}
                }

                acceptKpiDic.Add((uint)key, valueObj);
            }
        }

        public virtual Dictionary<uint, object> DoAfterAnalyseAllFiles(Dictionary<string, List<CellAcceptKpiAna_TianJin>> cellsResListTmp)
        {
            Dictionary<uint, object> kpiInfos = new Dictionary<uint, object>();
            foreach (var res in cellsResListTmp)
            {
                string typeName = typeof(AcpAutoInterOperation_TianJin).ToString();
                if (res.Key == typeName)
                {
                    //部分结果根据多个文件分析得出,所以需要在分析完所有文件后再计算
                    AcpAutoInterOperation_TianJin acp = new AcpAutoInterOperation_TianJin();
                    Dictionary<KpiKey_TJ, object>  acpKpiInfo = acp.DealFinalData(res.Value);
                    setValidKpiRes(kpiInfos, acpKpiInfo);
                }
            }

            return kpiInfos;
        }

        #region 获取测试文件对应的主服小区
        /// <summary>
        /// 根据采样点匹配主服小区
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        public static new LTECell GetTpSrcCell(TestPoint tp)
        {
            LTECell cell = null;
            if (tp is LTEFddTestPoint)
            {
                cell = CellManager.GetInstance().GetNearestLTECellByTACCI(tp.DateTime, (int?)(ushort?)tp["lte_fdd_TAC"], (int?)tp["lte_fdd_ECI"]
                    , tp.Longitude, tp.Latitude);

                int? earfcn = (int?)tp["lte_fdd_EARFCN"];
                int? pci = (int?)(short?)tp["lte_fdd_PCI"];

                if (cell == null && earfcn != null && pci != null)
                {
                    cell = getLTECellByEARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude, tp.FileName);
                }
            }
            return cell;
        }

        /// <summary>
        /// 根据频点PCI匹配小区
        /// </summary>
        /// <param name="time"></param>
        /// <param name="earfcn"></param>
        /// <param name="pci"></param>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private static LTECell getLTECellByEARFCNPCI(DateTime time, int? earfcn, int? pci, double longitude
           , double latitude, string fileName)
        {
            if (earfcn == null || pci == null)
            {
                return null;
            }
            List<LTECell> cells = CellManager.GetInstance().GetLTECellListByEARFCNPCI(time, earfcn, pci);
            if (cells != null && cells.Count > 0)
            {
                if (!string.IsNullOrEmpty(fileName))
                {
                    LTECell cell = getValidCell(time, longitude, latitude, cells, fileName);
                    if (cell != null)
                    {
                        return cell;
                    }
                }

                return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(time, earfcn, pci, longitude, latitude);
            }
            return null;
        }

        private static LTECell getValidCell(DateTime time, double longitude, double latitude, List<LTECell> cells, string fileName)
        {
            foreach (LTECell cell in cells)
            {
                if (cell.ValidPeriod.Contains(time))
                {
                    if (longitude > 0 && latitude > 0 && CellManager.GetInstance().SystemConfigInfo.distLimit
                        && cell.GetDistance(longitude, latitude) > CD.MAX_COV_DISTANCE_LTE)//距离限制设置
                    {
                        continue;
                    }
                    if (fileName.Contains(cell.BTSName))
                    {
                        return cell;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 根据文件名判断是否为测试文件的主服小区(后续根据文件命名规范修改此判断)
        /// </summary>
        /// <param name="cell"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private bool isSrcTestCell(LTECell cell, string fileName, bool anaMultiCells)
        {
            if (cell == null || cell.BelongBTS == null)
            {
                return false;
            }

            string keyStr = cell.BelongBTS.Name.ToUpper() + "_" + cell.CellID;
            if (anaMultiCells)
            {
                keyStr = cell.BelongBTS.Name.ToUpper();
            }
            if (fileName.ToUpper().Contains(keyStr.Trim()))
            {
                return true;
            }
            return false;
        }
        #endregion     
    }

    public static class StaionAcceptResultHelper_TJ
    {
        public static OutDoorBtsAcceptInfo_TianJin GetOutDoorBtsResultByBgData(BtsWorkParam_TianJin curBtsInfo,
            List<CellAcceptFileInfo_TianJin> curBtsCellsResList, BtsAcceptFileInfo_TianJin curBtsRes)
        {
            OutDoorBtsAcceptInfo_TianJin btsAcceptInfo = null;
            if (curBtsRes != null)
            {
                btsAcceptInfo = new OutDoorBtsAcceptInfo_TianJin(curBtsInfo.Bts);
                btsAcceptInfo.BtsAcceptRes = curBtsRes;
                btsAcceptInfo.AddAcceptKpiInfo(curBtsRes.AcceptKpiDic);
            }

            foreach (CellAcceptFileInfo_TianJin result in curBtsCellsResList)
            {
                addOutdoorCellAcceptInfo(curBtsInfo, ref btsAcceptInfo, result);
            }

            if (btsAcceptInfo != null)
            {
                btsAcceptInfo.CheckBtsIsAccordAccept();
            }
            return btsAcceptInfo;
        }

        private static void addOutdoorCellAcceptInfo(BtsWorkParam_TianJin curBtsInfo, 
            ref OutDoorBtsAcceptInfo_TianJin btsAcceptInfo, CellAcceptFileInfo_TianJin result)
        {
            if (btsAcceptInfo == null)
            {
                btsAcceptInfo = new OutDoorBtsAcceptInfo_TianJin(curBtsInfo.Bts);
            }

            OutDoorCellAcceptInfo_TianJin cellAcceptInfo;
            if (!btsAcceptInfo.CellsAcceptDic.TryGetValue(result.Cell.CellID, out cellAcceptInfo))
            {
                cellAcceptInfo = new OutDoorCellAcceptInfo_TianJin(result.Cell);
                btsAcceptInfo.CellsAcceptDic.Add(result.Cell.CellID, cellAcceptInfo);
            }
            cellAcceptInfo.AddAcceptKpiInfo(result.AcceptKpiDic);
        }

        public static void reportBackgroundInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }

        public static void reportBackgroundError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }
}
