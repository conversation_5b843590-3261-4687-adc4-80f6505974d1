﻿namespace MasterCom.RAMS.Func.SystemSetting
{
    partial class LowTaskFileManageProperties
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.panel1 = new System.Windows.Forms.Panel();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.numAnalyseErrorTime = new System.Windows.Forms.NumericUpDown();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.numAnalyseTime = new System.Windows.Forms.NumericUpDown();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtAttachPath = new System.Windows.Forms.TextBox();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.btnExcelPath = new DevExpress.XtraEditors.ButtonEdit();
            this.txtWebPath = new System.Windows.Forms.TextBox();
            this.btnImportedExcelPath = new DevExpress.XtraEditors.ButtonEdit();
            this.btnPicPath = new DevExpress.XtraEditors.ButtonEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl = new DevExpress.XtraEditors.GroupControl();
            this.chkBackgroundStat = new DevExpress.XtraEditors.CheckEdit();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.numTPDistance = new System.Windows.Forms.NumericUpDown();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.numTPTimeDiff = new System.Windows.Forms.NumericUpDown();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.numTPOverlap = new System.Windows.Forms.NumericUpDown();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.numTimeOverlap = new System.Windows.Forms.NumericUpDown();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAnalyseErrorTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAnalyseTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnExcelPath.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnImportedExcelPath.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnPicPath.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).BeginInit();
            this.groupControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTPDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTPTimeDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTPOverlap)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeOverlap)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.panel1);
            this.groupControl1.Location = new System.Drawing.Point(0, 50);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(461, 188);
            this.groupControl1.TabIndex = 5;
            this.groupControl1.Text = "报告保存路径";
            // 
            // panel1
            // 
            this.panel1.AutoScroll = true;
            this.panel1.Controls.Add(this.labelControl16);
            this.panel1.Controls.Add(this.labelControl17);
            this.panel1.Controls.Add(this.numAnalyseErrorTime);
            this.panel1.Controls.Add(this.labelControl7);
            this.panel1.Controls.Add(this.labelControl6);
            this.panel1.Controls.Add(this.numAnalyseTime);
            this.panel1.Controls.Add(this.labelControl3);
            this.panel1.Controls.Add(this.labelControl1);
            this.panel1.Controls.Add(this.txtAttachPath);
            this.panel1.Controls.Add(this.labelControl2);
            this.panel1.Controls.Add(this.labelControl5);
            this.panel1.Controls.Add(this.btnExcelPath);
            this.panel1.Controls.Add(this.txtWebPath);
            this.panel1.Controls.Add(this.btnImportedExcelPath);
            this.panel1.Controls.Add(this.btnPicPath);
            this.panel1.Controls.Add(this.labelControl4);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(2, 23);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(457, 163);
            this.panel1.TabIndex = 105;
            // 
            // labelControl16
            // 
            this.labelControl16.Location = new System.Drawing.Point(262, 50);
            this.labelControl16.Name = "labelControl16";
            this.labelControl16.Size = new System.Drawing.Size(24, 14);
            this.labelControl16.TabIndex = 96;
            this.labelControl16.Text = "小时";
            // 
            // labelControl17
            // 
            this.labelControl17.Location = new System.Drawing.Point(23, 50);
            this.labelControl17.Name = "labelControl17";
            this.labelControl17.Size = new System.Drawing.Size(104, 14);
            this.labelControl17.TabIndex = 94;
            this.labelControl17.Text = "异常文件处理时间 :";
            this.labelControl17.ToolTip = "解析异常属于本地问题,故先预留时间给本地人工处理,超过时间没处理再上传";
            // 
            // numAnalyseErrorTime
            // 
            this.numAnalyseErrorTime.Location = new System.Drawing.Point(144, 48);
            this.numAnalyseErrorTime.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numAnalyseErrorTime.Name = "numAnalyseErrorTime";
            this.numAnalyseErrorTime.Size = new System.Drawing.Size(112, 22);
            this.numAnalyseErrorTime.TabIndex = 95;
            this.numAnalyseErrorTime.Value = new decimal(new int[] {
            72,
            0,
            0,
            0});
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(262, 16);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(24, 14);
            this.labelControl7.TabIndex = 92;
            this.labelControl7.Text = "小时";
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(47, 16);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(80, 14);
            this.labelControl6.TabIndex = 90;
            this.labelControl6.Text = "文件解析时间 :";
            this.labelControl6.ToolTip = "工单上传后预留给文件解析的时间,该时间内的文件结果不上传";
            // 
            // numAnalyseTime
            // 
            this.numAnalyseTime.Location = new System.Drawing.Point(144, 14);
            this.numAnalyseTime.Name = "numAnalyseTime";
            this.numAnalyseTime.Size = new System.Drawing.Size(112, 22);
            this.numAnalyseTime.TabIndex = 91;
            this.numAnalyseTime.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(30, 219);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(97, 14);
            this.labelControl3.TabIndex = 25;
            this.labelControl3.Text = "WebService地址 :";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(43, 85);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(84, 14);
            this.labelControl1.TabIndex = 29;
            this.labelControl1.Text = "工单Excel路径 :";
            // 
            // txtAttachPath
            // 
            this.txtAttachPath.Location = new System.Drawing.Point(144, 181);
            this.txtAttachPath.Name = "txtAttachPath";
            this.txtAttachPath.Size = new System.Drawing.Size(290, 22);
            this.txtAttachPath.TabIndex = 89;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(7, 118);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(120, 14);
            this.labelControl2.TabIndex = 31;
            this.labelControl2.Text = "录入后Excel搬移路径 :";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(23, 184);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(104, 14);
            this.labelControl5.TabIndex = 88;
            this.labelControl5.Text = "附件下载相对路径 :";
            // 
            // btnExcelPath
            // 
            this.btnExcelPath.Location = new System.Drawing.Point(144, 82);
            this.btnExcelPath.Name = "btnExcelPath";
            this.btnExcelPath.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.btnExcelPath.Properties.ReadOnly = true;
            this.btnExcelPath.Size = new System.Drawing.Size(291, 21);
            this.btnExcelPath.TabIndex = 82;
            this.btnExcelPath.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.btnEditSelectFolder);
            // 
            // txtWebPath
            // 
            this.txtWebPath.Location = new System.Drawing.Point(144, 215);
            this.txtWebPath.Name = "txtWebPath";
            this.txtWebPath.Size = new System.Drawing.Size(290, 22);
            this.txtWebPath.TabIndex = 87;
            // 
            // btnImportedExcelPath
            // 
            this.btnImportedExcelPath.Location = new System.Drawing.Point(144, 115);
            this.btnImportedExcelPath.Name = "btnImportedExcelPath";
            this.btnImportedExcelPath.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.btnImportedExcelPath.Properties.ReadOnly = true;
            this.btnImportedExcelPath.Size = new System.Drawing.Size(291, 21);
            this.btnImportedExcelPath.TabIndex = 84;
            this.btnImportedExcelPath.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.btnEditSelectFolder);
            // 
            // btnPicPath
            // 
            this.btnPicPath.Location = new System.Drawing.Point(144, 148);
            this.btnPicPath.Name = "btnPicPath";
            this.btnPicPath.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.btnPicPath.Properties.ReadOnly = true;
            this.btnPicPath.Size = new System.Drawing.Size(291, 21);
            this.btnPicPath.TabIndex = 86;
            this.btnPicPath.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.btnEditSelectFolder);
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(23, 151);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(104, 14);
            this.labelControl4.TabIndex = 85;
            this.labelControl4.Text = "本地图片保存路径 :";
            // 
            // groupControl
            // 
            this.groupControl.Controls.Add(this.chkBackgroundStat);
            this.groupControl.Location = new System.Drawing.Point(0, 0);
            this.groupControl.Name = "groupControl";
            this.groupControl.Size = new System.Drawing.Size(461, 50);
            this.groupControl.TabIndex = 4;
            this.groupControl.Text = "开关设置";
            // 
            // chkBackgroundStat
            // 
            this.chkBackgroundStat.Location = new System.Drawing.Point(15, 26);
            this.chkBackgroundStat.Name = "chkBackgroundStat";
            this.chkBackgroundStat.Properties.Caption = "启用";
            this.chkBackgroundStat.Size = new System.Drawing.Size(75, 19);
            this.chkBackgroundStat.TabIndex = 0;
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.labelControl14);
            this.groupControl2.Controls.Add(this.numTPDistance);
            this.groupControl2.Controls.Add(this.labelControl15);
            this.groupControl2.Controls.Add(this.labelControl12);
            this.groupControl2.Controls.Add(this.numTPTimeDiff);
            this.groupControl2.Controls.Add(this.labelControl13);
            this.groupControl2.Controls.Add(this.labelControl10);
            this.groupControl2.Controls.Add(this.numTPOverlap);
            this.groupControl2.Controls.Add(this.labelControl11);
            this.groupControl2.Controls.Add(this.labelControl8);
            this.groupControl2.Controls.Add(this.numTimeOverlap);
            this.groupControl2.Controls.Add(this.labelControl9);
            this.groupControl2.Location = new System.Drawing.Point(0, 238);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(461, 148);
            this.groupControl2.TabIndex = 6;
            this.groupControl2.Text = "同车测试配置";
            // 
            // labelControl14
            // 
            this.labelControl14.Location = new System.Drawing.Point(272, 116);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(12, 14);
            this.labelControl14.TabIndex = 104;
            this.labelControl14.Text = "米";
            // 
            // numTPDistance
            // 
            this.numTPDistance.Location = new System.Drawing.Point(146, 114);
            this.numTPDistance.Name = "numTPDistance";
            this.numTPDistance.Size = new System.Drawing.Size(120, 22);
            this.numTPDistance.TabIndex = 103;
            this.numTPDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // labelControl15
            // 
            this.labelControl15.Location = new System.Drawing.Point(62, 116);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Size = new System.Drawing.Size(68, 14);
            this.labelControl15.TabIndex = 102;
            this.labelControl15.Text = "采样点距离 :";
            this.labelControl15.ToolTip = "采样点距离在范围内则为同车测试点";
            // 
            // labelControl12
            // 
            this.labelControl12.Location = new System.Drawing.Point(272, 86);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(12, 14);
            this.labelControl12.TabIndex = 101;
            this.labelControl12.Text = "秒";
            // 
            // numTPTimeDiff
            // 
            this.numTPTimeDiff.Location = new System.Drawing.Point(146, 84);
            this.numTPTimeDiff.Name = "numTPTimeDiff";
            this.numTPTimeDiff.Size = new System.Drawing.Size(120, 22);
            this.numTPTimeDiff.TabIndex = 100;
            this.numTPTimeDiff.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            // 
            // labelControl13
            // 
            this.labelControl13.Location = new System.Drawing.Point(38, 86);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(92, 14);
            this.labelControl13.TabIndex = 99;
            this.labelControl13.Text = "采样点时间间隔 :";
            this.labelControl13.ToolTip = "采样点可能存在误差,允许前后几秒内采样点进行判断";
            // 
            // labelControl10
            // 
            this.labelControl10.Location = new System.Drawing.Point(272, 58);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(12, 14);
            this.labelControl10.TabIndex = 98;
            this.labelControl10.Text = "%";
            // 
            // numTPOverlap
            // 
            this.numTPOverlap.Location = new System.Drawing.Point(146, 56);
            this.numTPOverlap.Name = "numTPOverlap";
            this.numTPOverlap.Size = new System.Drawing.Size(120, 22);
            this.numTPOverlap.TabIndex = 97;
            this.numTPOverlap.Value = new decimal(new int[] {
            80,
            0,
            0,
            0});
            // 
            // labelControl11
            // 
            this.labelControl11.Location = new System.Drawing.Point(62, 58);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(68, 14);
            this.labelControl11.TabIndex = 96;
            this.labelControl11.Text = "路径吻合度 :";
            this.labelControl11.ToolTip = "文件路径(采样点)重叠部分需要达到阈值";
            // 
            // labelControl8
            // 
            this.labelControl8.Location = new System.Drawing.Point(272, 30);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(12, 14);
            this.labelControl8.TabIndex = 95;
            this.labelControl8.Text = "%";
            // 
            // numTimeOverlap
            // 
            this.numTimeOverlap.Location = new System.Drawing.Point(146, 28);
            this.numTimeOverlap.Name = "numTimeOverlap";
            this.numTimeOverlap.Size = new System.Drawing.Size(120, 22);
            this.numTimeOverlap.TabIndex = 94;
            this.numTimeOverlap.Value = new decimal(new int[] {
            80,
            0,
            0,
            0});
            // 
            // labelControl9
            // 
            this.labelControl9.Location = new System.Drawing.Point(62, 30);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(68, 14);
            this.labelControl9.TabIndex = 93;
            this.labelControl9.Text = "时间吻合度 :";
            this.labelControl9.ToolTip = "文件时段重叠部分需要达到阈值";
            // 
            // LowTaskFileManageProperties
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.groupControl);
            this.Name = "LowTaskFileManageProperties";
            this.Size = new System.Drawing.Size(461, 457);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAnalyseErrorTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAnalyseTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnExcelPath.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnImportedExcelPath.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnPicPath.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).EndInit();
            this.groupControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTPDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTPTimeDiff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTPOverlap)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeOverlap)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.GroupControl groupControl;
        private DevExpress.XtraEditors.CheckEdit chkBackgroundStat;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.ButtonEdit btnExcelPath;
        private DevExpress.XtraEditors.ButtonEdit btnImportedExcelPath;
        private DevExpress.XtraEditors.ButtonEdit btnPicPath;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private System.Windows.Forms.TextBox txtAttachPath;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private System.Windows.Forms.TextBox txtWebPath;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private System.Windows.Forms.NumericUpDown numAnalyseTime;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private System.Windows.Forms.NumericUpDown numTPDistance;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private System.Windows.Forms.NumericUpDown numTPTimeDiff;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private System.Windows.Forms.NumericUpDown numTPOverlap;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private System.Windows.Forms.NumericUpDown numTimeOverlap;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private System.Windows.Forms.NumericUpDown numAnalyseErrorTime;
    }
}
