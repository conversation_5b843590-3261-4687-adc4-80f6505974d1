﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class TestTag:IComparable<TestTag>
    {
        public override string ToString()
        {
            return Name;
        }

        public string Name
        {
            get;
            private set;
        }
        public string Desc
        {
            get;
            private set;
        }

        public TestTag(string testName, string desc)
        {
            this.Name = testName;
            this.Desc = desc;
        }


        #region IComparable<TestTag> 成员

        public int CompareTo(TestTag other)
        {
            return this.Desc.CompareTo(other.Desc);
        }

        #endregion
    }
}
