﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.Voronoi;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class GSMBTSVoiCoverManager
    {
        public Dictionary<BTS, List<Vertex[]>> BtsToPolygonDict { get; set; }
        public VoiCoverCondition Condition { get; set; }
        public string LastErrorText { get; set; }
        protected bool isShowProgress;
        protected bool isShowSetting;

        public static GSMBTSVoiCoverManager GetInstance()
        {
            if (instance == null)
            {
                instance = new GSMBTSVoiCoverManager();
            }
            return instance;
        }

        /// <summary>
        /// 构建GSM BTS的泰森多边形
        /// </summary>
        /// <param name="isShowSetting">是否显示设置窗口</param>
        /// <param name="isShowProgress">是否显示进度条</param>
        /// <returns></returns>
        public VoiCoverResult Construct(bool isShowSetting, bool isShowProgress)
        {
            Reset();
            this.isShowProgress = isShowProgress;
            this.isShowSetting = isShowSetting;

            if (isShowSetting && GetSettingCondition() != DialogResult.OK)
            {
                return VoiCoverResult.Cancel;
            }

            Construct();
            return LastErrorText == "" ? VoiCoverResult.Succeed : VoiCoverResult.Failed;
        }

        /// <summary>
        /// 在地图上显示结果
        /// </summary>
        public virtual void Show()
        {
            if (BtsToPolygonDict == null)
            {
                return;
            }

            List<List<Vertex[]>> drawList = new List<List<Vertex[]>>();
            foreach (BTS bts in BtsToPolygonDict.Keys)
            {
                drawList.Add(BtsToPolygonDict[bts]);
            }
            VoronoiLayer.GetInstance().Draw(drawList);
        }

        /// <summary>
        /// 清除构建结果和地图显示
        /// </summary>
        public virtual void Clear()
        {
            Reset();
            VoronoiLayer.GetInstance().Clear();
        }

        /// <summary>
        /// 构建泰森多边形的过程
        /// </summary>
        protected virtual void Construct()
        {
            List<BTS> btsList = null;
            if (MapCellLayer.DrawCurrent)
            {
                btsList = MainModel.GetInstance().CellManager.GetCurrentBTSs();
            }
            else
            {
                btsList = MainModel.GetInstance().CellManager.GetBTSs(MapCellLayer.CurShowTimeAt);
            }
            BtsToPolygonDict = VoronoiManager<BTS>.GetInstance().Construct(btsList, ValidFilter, isShowProgress);
            LastErrorText = VoronoiManager<BTS>.GetInstance().LastErrorText;
        }

        protected DialogResult GetSettingCondition()
        {
            Condition = new VoiCoverCondition();
            VoiCoverSettingForm form = new VoiCoverSettingForm(this);
            if (form.ShowDialog() == DialogResult.OK)
            {
                Condition = form.GetCondition();
            }
            return form.DialogResult;
        }

        /// <summary>
        /// 重置Manager的变量
        /// </summary>
        protected void Reset()
        {
            LastErrorText = "";
            isShowProgress = false;
            isShowSetting = false;
        }

        protected GSMBTSVoiCoverManager()
        {
        }

        protected virtual bool ValidFilter(BTS bts, MapOperation2 mop2)
        {
            if (bts.BandType != BTSBandType.GSM900 || bts.Type != Condition.GsmType)
            {
                return false;
            }
            return mop2.CheckPointInRegion(bts.VertexX, bts.VertexY);
        }

        private static GSMBTSVoiCoverManager instance;
    }
}
