﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 小区云图数据
    /// </summary>
    public class CellCloudPictureData
    {
        /// <summary>
        /// 小区经度
        /// </summary>
        public double Longitude { get; set; }

        /// <summary>
        /// 小区纬度
        /// </summary>
        public double Latitude { get; set; }

        /// <summary>
        /// 小区方向
        /// </summary>
        public short Direction { get; set; }

        /// <summary>
        /// 小区理想覆盖半径
        /// </summary>
        public double Radius { get; set; }

        /// <summary>
        /// 小区指标值
        /// </summary>
        public double Weight { get; set; }
    }

    /// <summary>
    /// 小区云图绘制设置
    /// </summary>
    public class CellCloudPictureConfig
    {
        /// <summary>
        /// 小区椭圆的长半轴占理想覆盖半径比例
        /// </summary>
        public double MajorSemiAxisRate { get; set; }

        /// <summary>
        /// 小区椭圆的短半轴占理想覆盖半径比例
        /// </summary>
        public double MinorSemiAxisRate { get; set; }

        /// <summary>
        /// 小区经纬点后移距离占椭圆长半轴比例
        /// </summary>
        public double CenterOffsetRate { get; set; }

        /// <summary>
        /// 路径渐变填充时，渐变起点前移距离占长半轴比例
        /// </summary>
        public double GradientOffsetRate { get; set; }

        /// <summary>
        /// 指标最大值
        /// </summary>
        public double MaxWeight { get; set; }

        /// <summary>
        /// 指标最小值
        /// </summary>
        public double MinWeight { get; set; }

        /// <summary>
        /// 云图图层的透明值
        /// </summary>
        public int AlphaValue { get; set; }

        /// <summary>
        /// 云图的渲染色带，长度固定为256
        /// </summary>
        public uint[] ColorArray
        {
            get;
            private set;
        }

        /// <summary>
        /// 云图的渲染色块，长度最大256
        /// </summary>
        public int[] ColorZone
        {
            get
            {
                return colorZone;
            }
            set
            {
                if (value.Length < 256)
                {
                    colorZone = value;
                }
                else
                {
                    colorZone = defaultZone;
                }
                Color[] colors = new Color[colorZone.Length];
                for (int i = 0; i < colorZone.Length; ++i)
                {
                    colors[i] = Color.FromArgb(AlphaValue, ColorTranslator.FromOle(colorZone[i]));
                }
                ColorArray = ColorZoneCreater.Create(colors);
            }
        }

        public CellCloudPictureConfig()
        {
            MajorSemiAxisRate = 0.3;
            MinorSemiAxisRate = 0.2;
            CenterOffsetRate = 0.15;
            GradientOffsetRate = 0.25;
            MaxWeight = 100;
            MinWeight = 0;
            AlphaValue = 200;
            ColorZone = defaultZone;
        }

        private int[] colorZone;
        private readonly int[] defaultZone = 
        {
            0x0000ff00,
            0x00ffff00,
            0x0000ffff,
            0x000000ff,
        };
    }

    /// <summary>
    /// 创建一个渐变色带数组
    /// </summary>
    public static class ColorZoneCreater
    {
        private static int length = 256;
        public static uint[] Create(Color[] colorZone)
        {
            Bitmap bitmap = new Bitmap(length, 1);
            RectangleF rectf = new RectangleF(
                0, 0, (float)Math.Ceiling((double)bitmap.Width / (colorZone.Length - 1)), bitmap.Height);
            Graphics graphics = Graphics.FromImage(bitmap);

            for (int i = 1; i < colorZone.Length; ++i)
            {
                rectf.X = (i - 1) * rectf.Width;
                LinearGradientBrush brush = new LinearGradientBrush(rectf, colorZone[i - 1],
                    colorZone[i], LinearGradientMode.Horizontal);
                graphics.FillRectangle(brush, rectf);
            }

            uint[] retArr = new uint[length];
            for (int i = 0; i < length; ++i)
            {
                retArr[i] = (uint)bitmap.GetPixel(i, 0).ToArgb();
            }

            bitmap.Dispose();
            graphics.Dispose();
            return retArr;
        }
    }

    public enum CellType { GSM, TD }

    /// <summary>
    /// GSM小区理想覆盖半径
    /// TD未有理想覆盖半径计算支持
    /// </summary>
    public static class CellRadiusManager
    {
        public static void Set(int lac, int ci)
        {
            Cell cell = CellManager.GetInstance().GetCurrentCell(lac, ci);
            Set(cell);
        }

        public static void Set(Cell cell)
        {
            if (cell == null)
            {
                return;
            }
            int lacci = (cell.LAC << 16) + cell.CI;
            if (!lacciRadiusDict.ContainsKey(lacci))
            {
                double radius = 0.0001951/20*MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, 3, false);
                lacciRadiusDict.Add(lacci, radius);
            }
        }

        public static void Set(TDCell tdCell)
        {
            if (tdCell == null)
            {
                return;
            }
            int lacci = (tdCell.LAC << 16) + tdCell.CI;
            if (!tdLacciRadiusDict.ContainsKey(lacci))
            {
                double radius = 0.0001951 / 20 * MasterCom.ES.Data.CfgDataProvider.CalculateRadius(tdCell, 3);
                tdLacciRadiusDict.Add(lacci, radius);
            }
        }

        public static double Get(int lac, int ci,CellType type)
        {
            if (type == CellType.GSM)
            {
                int lacci = (lac << 16) + ci;
                return lacciRadiusDict.ContainsKey(lacci) ? lacciRadiusDict[lacci] : -1;
            }
            else if (type == CellType.TD)
            {
                int lacci = (lac << 16) + ci;
                return tdLacciRadiusDict.ContainsKey(lacci) ? tdLacciRadiusDict[lacci] : -1;
            }
            return -1;
        }

        public static double Get(Cell cell)
        {
            return cell == null ? -1 : Get(cell.LAC, cell.CI, CellType.GSM);
        }

        public static double Get(TDCell tdCell)
        {
            return tdCell == null ? -1 : Get(tdCell.LAC, tdCell.CI, CellType.TD);
        }

        private static Dictionary<int, double> lacciRadiusDict = new Dictionary<int, double>();
        private static Dictionary<int, double> tdLacciRadiusDict = new Dictionary<int, double>();
    }
}
