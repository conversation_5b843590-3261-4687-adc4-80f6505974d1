﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSQLReportEventToOtherArea_BJ : DIYSQLBase
    {
        public ZTSQLReportEventToOtherArea_BJ(MainModel mainModel, ZTReportEventInfo_BJ reportEventInfo, string gridName)
            : base(mainModel)
        {
            this.reportEventInfo = reportEventInfo;
            this.gridName = gridName;
        }

        private readonly ZTReportEventInfo_BJ reportEventInfo;
        private readonly string gridName;
        private bool isSucceed = false;

        public bool GetResult()
        {
            return isSucceed;
        }

        protected override string getSqlTextString()
        {
            return "exec mc_sp_beijing_report_event_toOtherArea " + reportEventInfo.FileID + "," + reportEventInfo.SeqID + ","
                    + "'" + reportEventInfo.GridName + "'" + "," + "'" + this.gridName + "'" + "," + "'" + MainModel.User.LoginName + "'";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int index = 0;
            E_VType[] rType = new E_VType[1];
            rType[index] = E_VType.E_Int;

            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    int result = 0;
                    result = package.Content.GetParamInt();
                    if (result == 1)
                    {
                        isSucceed = true;  //更新成功
                    }
                    else
                    {
                        isSucceed = false;
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "ZTSQLReportEventToOtherArea_BJ"; }
        }
    }
}
