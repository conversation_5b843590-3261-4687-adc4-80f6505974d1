﻿using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    class MultiOutStaionAcceptResultQuery : MultiStationAcceptAna
    {
        Dictionary<int, OutDoorBtsAcceptInfo> outDoorBtsAcceptInfoDic = new Dictionary<int, OutDoorBtsAcceptInfo>();

        private static MultiOutStaionAcceptResultQuery intance = null;
        public static MultiOutStaionAcceptResultQuery GetIntance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new MultiOutStaionAcceptResultQuery();
                    }
                }
            }
            return intance;
        }
        protected MultiOutStaionAcceptResultQuery()
            : base()
        {
        }
        public override string Name
        {
            get { return "LTE宏站总体验收结果查询"; }
        }

        protected override bool getCondition()
        {
            return true;
        }
        protected override bool isValidCondition()
        {
            MainModel.QueryFromBackground = true;
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override void getReadyBeforeQuery()
        {
            outDoorBtsAcceptInfoDic.Clear();
        }

        protected override void query()
        {
            if (MainModel.IsBackground && MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                clientProxy.Close();
            }
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;

            int origId = MainModel.DistrictID;
            foreach (int districtID in condition.DistrictIDs)
            {
                MainModel.DistrictID = districtID;

                List<TimePeriod> Periods = condition.Periods;
                foreach (TimePeriod period in Periods)
                {
                    condition = new QueryCondition();
                    condition.Periods.Add(period);
                    getBackgroundData();
                    initBackgroundImageDesc();
                }
            }
            MainModel.DistrictID = origId;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;
            fireShowForm();
            MainModel.QueryFromBackground = false;
        }

        protected override void initBackgroundImageDesc()
        {
            AddBackgroundInfoToResults(BackgroundResultList, ref outDoorBtsAcceptInfoDic);
        }
        public static void AddBackgroundInfoToResults(List<BackgroundResult> backgroundResultList
            , ref Dictionary<int, OutDoorBtsAcceptInfo> btsAcceptInfoDic)
        {
            backgroundResultList.Sort(BackgroundResult.ComparerByISTimeDesc);

            foreach (BackgroundResult bgResult in backgroundResultList)
            {
                if (bgResult.StrDesc != "室外")
                {
                    continue;
                }

                DateTime bgResultTime = JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
                LTECell lteCell = CellManager.GetInstance().GetLTECell(bgResultTime, bgResult.LAC, bgResult.CI);
                if (lteCell == null)
                {
                    reportBackgroundInfo(string.Format("预处理文件{0}未关联到目标小区。", bgResult.FileName));
                    continue;
                }

                try
                {
                    byte[] bytes = bgResult.GetImageValueBytes();
                    Dictionary<uint, object> kpiDic = MasterCom.RAMS.NewBlackBlock.KeyValueImageParser.FromImage(bytes);

                    OutDoorBtsAcceptInfo btsAcceptInfo = null;
                    OutDoorCellAcceptInfo cellAcceptInfo = null;
                    if (bgResult.FileName != null && bgResult.FileName.Contains("系统内切换"))
                    {
                        btsAcceptInfo = getCurOutDoorBtsAcceptInfo(bgResultTime, lteCell.BelongBTS, ref btsAcceptInfoDic);
                        btsAcceptInfo.AddHandOverKpiInfo(kpiDic);
                    }
                    else
                    {
                        cellAcceptInfo = getCurOutDoorCellAcceptInfo(bgResultTime, lteCell, ref btsAcceptInfoDic);
                        cellAcceptInfo.AddAcceptKpiInfo(kpiDic);
                    }
                }
                catch(Exception ex)
                {
                    reportBackgroundError(ex);
                }
            }

            foreach (OutDoorBtsAcceptInfo btsInfo in btsAcceptInfoDic.Values)
            {
                btsInfo.CheckBtsIsAccordAccept();
            }
            upLoadCellCheckInfo(btsAcceptInfoDic);
        }
        private static void upLoadCellCheckInfo(Dictionary<int, OutDoorBtsAcceptInfo> outDoorBtsAcceptInfoDic)
        {
            try
            {
                if (outDoorBtsAcceptInfoDic == null || outDoorBtsAcceptInfoDic.Count <= 0)
                {
                    return;
                }
                BackgroundFuncConfigManager bgCfgManager = BackgroundFuncConfigManager.GetInstance();
                UpLoadOutdoorBtsKpiInfo_XJ ulQuery = new UpLoadOutdoorBtsKpiInfo_XJ(bgCfgManager.StartTime, bgCfgManager.EndTime,
                    new List<OutDoorBtsAcceptInfo>(outDoorBtsAcceptInfoDic.Values));
                ulQuery.Query();
            }
            catch(Exception ex)
            {
                reportBackgroundError(ex);
            }
        }

        private static OutDoorCellAcceptInfo getCurOutDoorCellAcceptInfo(DateTime bgTime, LTECell lteCell
            , ref Dictionary<int, OutDoorBtsAcceptInfo> btsAcceptInfoDic)
        {
            OutDoorBtsAcceptInfo btsAcceptInfo = getCurOutDoorBtsAcceptInfo(bgTime, lteCell.BelongBTS
                , ref btsAcceptInfoDic);

            OutDoorCellAcceptInfo cellAcceptInfo;
            if (!btsAcceptInfo.CellsAcceptDic.TryGetValue(lteCell.CellID, out cellAcceptInfo))
            {
                cellAcceptInfo = new OutDoorCellAcceptInfo(lteCell);
                btsAcceptInfo.CellsAcceptDic.Add(lteCell.CellID, cellAcceptInfo);
            }
            return cellAcceptInfo;
        }

        private static OutDoorBtsAcceptInfo getCurOutDoorBtsAcceptInfo(DateTime bgTime, LTEBTS lteBts
            , ref Dictionary<int, OutDoorBtsAcceptInfo> btsAcceptInfoDic)
        {
            OutDoorBtsAcceptInfo btsAcceptInfo;
            if (!btsAcceptInfoDic.TryGetValue(lteBts.BTSID, out btsAcceptInfo))
            {
                btsAcceptInfo = new OutDoorBtsAcceptInfo(lteBts);
                btsAcceptInfo.SN = btsAcceptInfoDic.Count + 1;
                btsAcceptInfo.AccpetTimePeriod.SetBeginTime(bgTime);
                btsAcceptInfoDic.Add(lteBts.BTSID, btsAcceptInfo);
            }
            btsAcceptInfo.AccpetTimePeriod.SetEndTime(bgTime);
            return btsAcceptInfo;
        }

        protected override void fireShowForm()
        {
            if (outDoorBtsAcceptInfoDic.Count == 0)
            {
                XtraMessageBox.Show("未查询到结果！");
                return;
            }
            MultiOutStaionAcceptResultForm frm = MainModel.CreateResultForm(typeof(MultiOutStaionAcceptResultForm)) as MultiOutStaionAcceptResultForm;
            frm.FillData(new List<OutDoorBtsAcceptInfo>(outDoorBtsAcceptInfoDic.Values));
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
