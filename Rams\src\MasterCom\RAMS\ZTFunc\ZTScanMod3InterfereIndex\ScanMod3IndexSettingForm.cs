﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanMod3IndexSettingForm : BaseDialog
    {
        public ScanMod3IndexSettingForm()
        {
            InitializeComponent();

            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        public ScanMod3IndexCondition GetCondition()
        {
            ScanMod3IndexCondition cond = new ScanMod3IndexCondition();
            cond.MainCellDiff = (double)this.numMainCellDiff.Value;
            cond.NbCellDiff = (double)this.numNbCellDiff.Value;
            cond.InvalidRsrp = (double)this.numInvalidRsrp.Value;
            return cond;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }
    }
}
