﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.Util;
using System.Windows.Forms;
using System.Runtime.Serialization;
using System.Collections;
using MasterCom.MControls;
using MasterCom.RAMS.Func.EventBlock;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Func.ProblemBlock;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Net;
namespace MasterCom.RAMS.Func
{
    [Serializable()]
    public class ZTDIYAnalysisSpecialSampleOpLayer : CustomDrawLayer
    {
        public Pen selPen { get; set; } = new Pen(Color.Green, 3);
        public ZTDIYAnalysisSpecialSampleOpLayer(MapOperation mp, string name)
            : base(mp, name)
        {
           
        }
        public static bool isCustomFly { get; set; } = true;
        public static bool isSpFfly { get; set; } = true;
        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || ZTDIYQueryScanAnalysis_LTE.ScanTestPointList == null
                || ZTDIYQueryScanAnalysis_LTE.ScanTestPointList.Count == 0)
            {
                return;
            }
            updateRect.Inflate((int)(30 * 10000 / Map.Scale), (int)(30 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            double temp_long = Map.GetCenter().x;
            double temp_lati = Map.GetCenter().y;
            DbPoint ptDSel = new DbPoint(temp_long, temp_lati);
            PointF scrPointSel;
            Map.ToDisplay(ptDSel, out scrPointSel);
            //底层20米的精度跨度大小 0.0001951
            double llGap = (0.000195 / 20) * 100;
            DbPoint ptDSel2 = new DbPoint(temp_long + llGap, temp_lati);
            PointF scrPointSel2;
            Map.ToDisplay(ptDSel2, out scrPointSel2);

            DrawItem(ZTDIYQueryScanAnalysis_LTE.ScanTestPointList, graphics);
        }

        private void DrawItem(List<TestPoint> testPointList, Graphics graphics)
        {
            SolidBrush brush = new SolidBrush(Color.FromArgb(230, Color.Red));
            foreach (TestPoint tp in testPointList)
            {
                Region blockReg = null;
                DbPoint dPoint = new DbPoint(tp.Longitude, tp.Latitude);
                PointF point;
                this.Map.ToDisplay(dPoint, out point);
                float radius = 8;
                RectangleF rectN = new RectangleF(point.X - radius, point.Y - radius, radius * 3 / 2, radius * 3 / 2);
                GraphicsPath gp = new GraphicsPath();
                gp.AddEllipse(rectN);

                blockReg = new Region(gp);
                graphics.FillRegion(brush, blockReg);
            }
        }

        public static int OutputShapeFile(string filename)
        {
            try
            {
                if (ZTDIYQueryScanAnalysis_LTE.ScanTestPointList.Count > 0)
                {
                    Shapefile shpFile = new Shapefile();
                    int idIdx = 0;

                    int strTime = idIdx++;
                    int strLac = idIdx++;
                    int strCi = idIdx++;
                    int fLongId = idIdx++;
                    int fLatId = idIdx++;
                    int iRsrp = idIdx++;
                    int strCellName_1 = idIdx++;
                    int strCellName_2 = idIdx++;
                    int strCellName_3 = idIdx++;
                    int strCellName_4 = idIdx++;
                    int strCellName_5 = idIdx++;
                    int strCellName_6 = idIdx;
                    
                    bool result = shpFile.CreateNew("", MapWinGIS.ShpfileType.SHP_POLYGON);
                    if (!result)
                    {
                        DevExpress.XtraEditors.XtraMessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                        return -1;
                    }

                    ShapeHelper.InsertNewField(shpFile, "strTime", FieldType.STRING_FIELD, 7, 20, ref strTime);
                    ShapeHelper.InsertNewField(shpFile, "strLac", FieldType.STRING_FIELD, 7, 20, ref strLac);
                    ShapeHelper.InsertNewField(shpFile, "strCi", FieldType.STRING_FIELD, 7, 20, ref strCi);
                    ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 7, 20, ref fLongId);
                    ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 7, 20, ref fLatId);
                    ShapeHelper.InsertNewField(shpFile, "iRsrp", FieldType.INTEGER_FIELD, 7, 20, ref iRsrp);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_1", FieldType.STRING_FIELD, 7, 20, ref strCellName_1);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_2", FieldType.STRING_FIELD, 7, 20, ref strCellName_2);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_3", FieldType.STRING_FIELD, 7, 20, ref strCellName_3);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_4", FieldType.STRING_FIELD, 7, 20, ref strCellName_4);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_5", FieldType.STRING_FIELD, 7, 20, ref strCellName_5);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_6", FieldType.STRING_FIELD, 7, 20, ref strCellName_6);
                    

                    double radius = 0.00048775;//大约50米
                    int shpIdx = 0;
                    MainModel.GetInstance().MainForm.Cursor = Cursors.WaitCursor;
                    foreach (TestPoint tp in ZTDIYQueryScanAnalysis_LTE.ScanTestPointList)
                    {
                        MapWinGIS.Shape spBase = getShape(radius, tp);
                        shpFile.EditInsertShape(spBase, ref shpIdx);
                        shpFile.EditCellValue(strTime, shpIdx, "20" + tp.DateTimeStringWithMillisecond);
                        string strlac = "";
                        string strci = "";
                        if (tp["LTESCAN_TopN_TAC"] != null)
                            strlac = tp["LTESCAN_TopN_TAC"].ToString();
                        if (tp["LTESCAN_TopN_ECI"] != null)
                            strci = tp["LTESCAN_TopN_ECI"].ToString();
                        shpFile.EditCellValue(strLac, shpIdx, strlac);
                        shpFile.EditCellValue(strCi, shpIdx, strci);
                        shpFile.EditCellValue(fLongId, shpIdx, tp.Longitude);
                        shpFile.EditCellValue(fLatId, shpIdx, tp.Latitude);
                        shpFile.EditCellValue(iRsrp, shpIdx, (int)(float?)tp["LTESCAN_TopN_CELL_Specific_RSRP"]);
                        List<LTECell> lteCellList = new List<LTECell>();
                        List<string> cellNameList = new List<string>();
                        addCellList(tp, lteCellList, cellNameList);
                        shpFile.EditCellValue(strCellName_1, shpIdx, cellNameList[0]);
                        shpFile.EditCellValue(strCellName_2, shpIdx, cellNameList[1]);
                        shpFile.EditCellValue(strCellName_3, shpIdx, cellNameList[2]);
                        shpFile.EditCellValue(strCellName_4, shpIdx, cellNameList[3]);
                        shpFile.EditCellValue(strCellName_5, shpIdx, cellNameList[4]);
                        shpFile.EditCellValue(strCellName_6, shpIdx, cellNameList[5]);

                        shpIdx++;
                    }
                    ShapeHelper.DeleteShpFile(filename);
                    shpFile.SaveAs(filename, null);
                    shpFile.Close();
                    return 1;
                }
                else
                {
                    return 0;
                }
            }
            catch
            {
                return -1;
            }
            finally
            {
                MainModel.GetInstance().MainForm.Cursor = Cursors.Default;
            }
        }

        private static MapWinGIS.Shape getShape(double radius, TestPoint tp)
        {
            MapWinGIS.Shape evtShp = ShapeHelper.CreateCircleShape(tp.Longitude, tp.Latitude, radius);
            return evtShp;
        }

        private static void addCellList(TestPoint tp, List<LTECell> lteCellList, List<string> cellNameList)
        {
            for (int k = 0; k < 50; k++)
            {
                LTECell lteCell = tp.GetCell_LTEScan(k);
                if (lteCell != null)
                {
                    lteCellList.Add(lteCell);
                    cellNameList.Add(lteCell.Name);
                }
            }
            if (cellNameList.Count < 6)
            {
                int iCount = 6 - cellNameList.Count;
                for (int j = 0; j < iCount; j++)
                    cellNameList.Add("");
            }
        }

        public static int OutputEventShapeFile(string filename,List<ScanAnalysisLTEInfo> outPutShpEventList)
        {
            try
            {
                if (outPutShpEventList.Count > 0)
                {
                    Shapefile shpFile = new Shapefile();
                    int idIdx = 0;
                    int iIndexID = idIdx++;
                    int strEventName = idIdx++;
                    int strGridType = idIdx++;
                    int strGridName = idIdx++;
                    int strRoadName = idIdx++;
                    int strLac = idIdx++;
                    int strCi = idIdx++;                   
                    int fLongId = idIdx++;
                    int fLatId = idIdx++;
                    int strTime = idIdx;

                    bool result = shpFile.CreateNew("", MapWinGIS.ShpfileType.SHP_POLYGON);
                    if (!result)
                    {
                        DevExpress.XtraEditors.XtraMessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                        return -1;
                    }

                    ShapeHelper.InsertNewField(shpFile, "iIndexID", FieldType.INTEGER_FIELD, 7, 20, ref iIndexID);
                    ShapeHelper.InsertNewField(shpFile, "strEventName", FieldType.STRING_FIELD, 7, 20, ref strEventName);
                    ShapeHelper.InsertNewField(shpFile, "strGridType", FieldType.STRING_FIELD, 7, 20, ref strGridType);
                    ShapeHelper.InsertNewField(shpFile, "strGridName", FieldType.STRING_FIELD, 7, 20, ref strGridName);
                    ShapeHelper.InsertNewField(shpFile, "strRoadName", FieldType.STRING_FIELD, 7, 20, ref strRoadName);
                    ShapeHelper.InsertNewField(shpFile, "strLac", FieldType.STRING_FIELD, 7, 20, ref strLac);
                    ShapeHelper.InsertNewField(shpFile, "strCi", FieldType.STRING_FIELD, 7, 20, ref strCi);
                    ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 7, 20, ref fLongId);
                    ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 7, 20, ref fLatId);
                    ShapeHelper.InsertNewField(shpFile, "strTime", FieldType.STRING_FIELD, 7, 20, ref strTime);

                    double radius = 0.00048775;//大约50米
                    int shpIdx = 0;
                    MainModel.GetInstance().MainForm.Cursor = Cursors.WaitCursor;
                    foreach (ScanAnalysisLTEInfo evtItem in outPutShpEventList)
                    {
                        MapWinGIS.Shape spBase = null;
                        MapWinGIS.Shape evtShp = ShapeHelper.CreateCircleShape(evtItem.Evt.Longitude, evtItem.Evt.Latitude, radius);
                        spBase = evtShp;
                        shpFile.EditInsertShape(spBase, ref shpIdx);
                        shpFile.EditCellValue(iIndexID, shpIdx, evtItem.IndexID);
                        shpFile.EditCellValue(strEventName, shpIdx, evtItem.Evt.Name);
                        shpFile.EditCellValue(strGridType, shpIdx, evtItem.StrGridType);
                        shpFile.EditCellValue(strGridName, shpIdx, evtItem.StrGridName);
                        shpFile.EditCellValue(strRoadName, shpIdx, evtItem.StrEventRoadName);
                        shpFile.EditCellValue(strLac, shpIdx, evtItem.StrLAC);
                        shpFile.EditCellValue(strCi, shpIdx, evtItem.StrCI);
                        shpFile.EditCellValue(fLongId, shpIdx, evtItem.Evt.Longitude);
                        shpFile.EditCellValue(fLatId, shpIdx, evtItem.Evt.Latitude);
                        shpFile.EditCellValue(strTime, shpIdx, evtItem.StrTime);
                        shpIdx++;
                    }
                    ShapeHelper.DeleteShpFile(filename);
                    shpFile.SaveAs(filename, null);
                    shpFile.Close();
                    return 1;
                }
                else
                {
                    return 0;
                }
            }
            catch
            {
                return -1;
            }
            finally
            {
                MainModel.GetInstance().MainForm.Cursor = Cursors.Default;
            }
        }

        public static int OutputReselectShapeFile(string filename)
        {
            try
            {
                if (ZTDIYQueryScanAnalysis_LTE.ScanTestPointList.Count > 0)
                {
                    Shapefile shpFile = new Shapefile();
                    int idIdx = 0;
                    int strTime = idIdx++;
                    int strLac = idIdx++;
                    int strCi = idIdx++;
                    int fLongId = idIdx++;
                    int fLatId = idIdx++;
                    int iRsrp = idIdx++;
                    int strCellName_1 = idIdx++;
                    int strCellName_2 = idIdx++;
                    int strCellName_3 = idIdx++;
                    int strCellName_4 = idIdx++;
                    int strCellName_5 = idIdx++;
                    int strCellName_6 = idIdx;

                    bool result = shpFile.CreateNew("", MapWinGIS.ShpfileType.SHP_POLYGON);
                    if (!result)
                    {
                        DevExpress.XtraEditors.XtraMessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                        return -1;
                    }
                    ShapeHelper.InsertNewField(shpFile, "strTime", FieldType.STRING_FIELD, 7, 20, ref strTime);
                    ShapeHelper.InsertNewField(shpFile, "strLac", FieldType.STRING_FIELD, 7, 20, ref strLac);
                    ShapeHelper.InsertNewField(shpFile, "strCi", FieldType.STRING_FIELD, 7, 20, ref strCi);
                    ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 7, 20, ref fLongId);
                    ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 7, 20, ref fLatId);
                    ShapeHelper.InsertNewField(shpFile, "iRsrp", FieldType.INTEGER_FIELD, 7, 20, ref iRsrp);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_1", FieldType.STRING_FIELD, 7, 20, ref strCellName_1);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_2", FieldType.STRING_FIELD, 7, 20, ref strCellName_2);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_3", FieldType.STRING_FIELD, 7, 20, ref strCellName_3);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_4", FieldType.STRING_FIELD, 7, 20, ref strCellName_4);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_5", FieldType.STRING_FIELD, 7, 20, ref strCellName_5);
                    ShapeHelper.InsertNewField(shpFile, "strCellName_6", FieldType.STRING_FIELD, 7, 20, ref strCellName_6);

                    double radius = 0.00048775;//大约50米
                    int shpIdx = 0;
                    MainModel.GetInstance().MainForm.Cursor = Cursors.WaitCursor;
                    foreach (TestPoint tp in ZTDIYQueryScanAnalysis_LTE.ScanTestPointList)
                    {
                        if (tp["lte_RSRP"] == null || tp["lte_TAC"] == null || tp["lte_ECI"] == null)
                            continue;
                        MapWinGIS.Shape spBase = getShape(radius, tp);
                        shpFile.EditInsertShape(spBase, ref shpIdx);
                        string strlac = tp["lte_TAC"].ToString();
                        string strci = tp["lte_ECI"].ToString();
                        List<LTECell> lteCellList = new List<LTECell>();
                        List<string> cellNameList = new List<string>();
                        addNBCellList(tp, lteCellList, cellNameList);
                        shpFile.EditCellValue(strTime, shpIdx, "20" + tp.DateTimeStringWithMillisecond);
                        shpFile.EditCellValue(strLac, shpIdx, strlac);
                        shpFile.EditCellValue(strCi, shpIdx, strci);
                        shpFile.EditCellValue(fLongId, shpIdx, tp.Longitude);
                        shpFile.EditCellValue(fLatId, shpIdx, tp.Latitude);
                        shpFile.EditCellValue(iRsrp, shpIdx, (int)(float?)tp["lte_RSRP"]);
                        shpFile.EditCellValue(strCellName_1, shpIdx, cellNameList[0]);
                        shpFile.EditCellValue(strCellName_2, shpIdx, cellNameList[1]);
                        shpFile.EditCellValue(strCellName_3, shpIdx, cellNameList[2]);
                        shpFile.EditCellValue(strCellName_4, shpIdx, cellNameList[3]);
                        shpFile.EditCellValue(strCellName_5, shpIdx, cellNameList[4]);
                        shpFile.EditCellValue(strCellName_6, shpIdx, cellNameList[5]);
                        shpIdx++;
                    }
                    ShapeHelper.DeleteShpFile(filename);
                    shpFile.SaveAs(filename, null);
                    shpFile.Close();
                    return 1;
                }
                else
                {
                    return 0;
                }
            }
            catch
            {
                return -1;
            }
            finally
            {
                MainModel.GetInstance().MainForm.Cursor = Cursors.Default;
            }
        }

        private static void addNBCellList(TestPoint tp, List<LTECell> lteCellList, List<string> cellNameList)
        {
            for (int k = 0; k < 50; k++)
            {
                LTECell lteCell = tp.GetNBCell_LTE(k);
                if (lteCell != null)
                {
                    lteCellList.Add(lteCell);
                    cellNameList.Add(lteCell.Name);
                }
            }
            if (cellNameList.Count < 6)
            {
                int iCount = 6 - cellNameList.Count;
                for (int j = 0; j < iCount; j++)
                    cellNameList.Add("");
            }
        }
    }
}
