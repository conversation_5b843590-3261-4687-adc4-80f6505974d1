﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class CCauseBase : ICloneable
    {
        public abstract string GetCauseName();

        public CCauseCond Cond { get; set; }

        public CCauseBase PrevCause { get; set; }

        public CCauseBase NextCause { get; set; }

        public int SN { get; set; } = 100;

        public CCauseBase GetCause(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            if (Cond.BChecked && Cond.IsValid(file, e, msgIdxBegin, msgIdxEnd))
            {
                return this;
            }
            else if(NextCause != null)
            {
                return NextCause.GetCause(file, e, msgIdxBegin, msgIdxEnd);
            }
            else
            {
                return new COtherCause();
            }
        }

        public override string ToString()
        {
            return GetCauseName();
        }

        #region ICloneable 成员

        public object Clone()
        {
            CCauseBase cause = (CCauseBase)this.MemberwiseClone();
            cause.Cond = (CCauseCond)(this.Cond.Clone());
            return cause;
        }

        #endregion
    }

    public class CServiceRejectCause : CCauseBase
    {
        public CServiceRejectCause()
        {
            Cond = new CServiceRejectCauseCond();
        }

        public override string GetCauseName()
        {
            return "基站故障";
        }
    }

    public class CFall2GCause : CCauseBase
    {
        public CFall2GCause()
        {
            Cond = new CFall2GCauseCond();
        }

        public override string GetCauseName()
        {
            return "未回落至GSM网络";
        }
    }

    public class CSDBlockCause : CCauseBase
    {
        public CSDBlockCause()
        {
            Cond = new CSDBlockCauseCond();
        }

        public override string GetCauseName()
        {
            return "资源不足";//"SD信道拥塞"
        }
    }

    public class CMtLocationUpdateCause : CCauseBase
    {
        public CMtLocationUpdateCause()
        {
            Cond = new CMtLocationUpdateCauseCond();
        }

        public override string GetCauseName()
        {
            return "被叫在做位置更新";
        }
    }

    public class CMtOOSCause : CCauseBase
    {
        public CMtOOSCause()
        {
            Cond = new CMtOOSCauseCond();
        }

        public override string GetCauseName()
        {
            return "缺少站点";// "手机脱网"
        }
    }

    public class CMtNotReceiveCause : CCauseBase
    {
        public CMtNotReceiveCause()
        {
            Cond = new CMtNotReceiveCauseCond();
        }

        public override string GetCauseName()
        {
            return "非无线网络侧";
        }
    }

    public class CMoHangUpCause : CCauseBase
    {
        public CMoHangUpCause()
        {
            Cond = new CMoHangUpCauseCond();
        }

        public override string GetCauseName()
        {
            return "非无线网络侧";
        }
    }

    public class CMoLocationUpdateCause : CCauseBase
    {
        public CMoLocationUpdateCause()
        {
            Cond = new CMoLocationUpdateCauseCond();
        }

        public override string GetCauseName()
        {
            return "终端或者设备故障";
        }
    }

    public class CWeakCovCause : CCauseBase
    {
        public CWeakCovCause()
        {
            Cond = new CWeakCovCauseCond();
        }

        public override string GetCauseName()
        {
            return "弱覆盖";
        }
    }

    public class COverCoverCause : CCauseBase
    {
        public COverCoverCause()
        {
            Cond = new COverCoverCauseCond();
        }

        public override string GetCauseName()
        {
            return "过覆盖";
        }
    }

    public class CCovInConformityCause : CCauseBase
    {
        public CCovInConformityCause()
        {
            Cond = new CCovInConformityCauseCond();
        }

        public override string GetCauseName()
        {
            return "覆盖不符";
        }
    }

    public class CBadQuaCause : CCauseBase
    {
        public CBadQuaCause()
        {
            Cond = new CBadQuaCauseCond();
        }

        public override string GetCauseName()
        {
            return "质差";
        }
    }

    public class CStrongRxlBadQuaCause : CCauseBase
    {
        public CStrongRxlBadQuaCause()
        {
            Cond = new CStrongRxlBadQuaCauseCond();
        }

        public override string GetCauseName()
        {
            return "强信号弱质量（干扰&硬件故障）";
        }
    }

    public class CCoverLapCause : CCauseBase
    {
        public CCoverLapCause()
        {
            Cond = new CCoverLapCauseCond();
        }

        public override string GetCauseName()
        {
            return "重叠覆盖";
        }
    }

    public class CModThreeCause : CCauseBase
    {
        public CModThreeCause()
        {
            Cond = new CModThreeCauseCond();
        }

        public override string GetCauseName()
        {
            return "模三干扰";
        }
    }

    public class CFrequentHOCause : CCauseBase
    {
        public CFrequentHOCause()
        {
            Cond = new CFrequentHOCauseCond();
        }

        public override string GetCauseName()
        {
            return "频繁切换";
        }
    }

    public class CDelayHOCause : CCauseBase
    {
        public CDelayHOCause()
        {
            Cond = new CDelayHOCauseCond();
        }

        public override string GetCauseName()
        {
            return "切换不及时";
        }
    }

    public class CUnReasonableHO : CCauseBase
    {
        public CUnReasonableHO()
        {
            Cond = new CUnReasonableHOCond();
        }

        public override string GetCauseName()
        {
            return "切换不合理";
        }
    }

    public class CFastFailureCause : CCauseBase
    {
        public CFastFailureCause()
        {
            Cond = new CFastFailureCauseCond();
        }

        public override string GetCauseName()
        {
            return "场强快衰";
        }
    }

    public class CIndoorLeakCause : CCauseBase
    {
        public CIndoorLeakCause()
        {
            Cond = new CIndoorLeakCauseCond();
        }

        public override string GetCauseName()
        {
            return "室分外泄";
        }
    }

    public class COtherCause : CCauseBase
    {
        public COtherCause()
        {
            Cond = new COtherCauseCond();
        }

        public override string GetCauseName()
        {
            return "其它";
        }
    }

    public class CMoProceedingCause : CCauseBase
    {
        public CMoProceedingCause()
        {
            Cond = new CMoProceedingCauseCond();
        }

        public override string GetCauseName()
        {
            return "人为原因";//"对端文件未找到"
        }
    }

    public class CLURCause : CCauseBase
    {
        public CLURCause()
        {
            Cond = new CLURCauseCond();
        }

        public override string GetCauseName()
        {
            return "终端故障";//"故障或干扰或覆盖原因"
        }
    }

    public class CMtTACause : CCauseBase
    {
        public CMtTACause()
        {
            Cond = new CMtTACauseCond();
        }

        public override string GetCauseName()
        {
            return "边界站点规划不合理";//"被叫正在做TA更新"
        }
    }

    public class CMtCallingCause : CCauseBase
    {
        public CMtCallingCause()
        {
            Cond = new CMtCallingCauseCond();
        }

        public override string GetCauseName()
        {
            return "人为原因";//"被叫正在通话"
        }
    }

    public class CMtCallingNoServiceRequestCause : CCauseBase
    {
        public CMtCallingNoServiceRequestCause()
        {
            Cond = new CMtCallingNoServiceRequestCauseCond();
        }

        public override string GetCauseName()
        {
            return "核心网/基站问题";// "被叫未收到寻呼消息"
        }
    }

    public class CMtCallingNoCallCause : CCauseBase
    {
        public CMtCallingNoCallCause()
        {
            Cond = new CMtCallingNoCallCauseCond();
        }

        public override string GetCauseName()
        {
            return "终端故障";// "被叫未起呼"
        }
    }

    public class CMtExtendServiceRequestCause : CCauseBase
    {
        public CMtExtendServiceRequestCause()
        {
            Cond = new CMtExtendServiceRequestCond();
        }

        public override string GetCauseName()
        {
            return "被叫未收到寻呼消息";
        }
    }

    public class CMoProceedingNoRRCReleaseCause : CCauseBase
    {
        public CMoProceedingNoRRCReleaseCause()
        {
            Cond = new CMoProceedingNoRRCReleaseCauseCond();
        }

        public override string GetCauseName()
        {
            return "基站故障";//被叫未发生回落
        }
    }

    public class CMoProceedingRRCReleaseWithEarfcnCause : CCauseBase
    {
        public CMoProceedingRRCReleaseWithEarfcnCause()
        {
            Cond = new CMoProceedingRRCReleaseWithEarfcnCauseCond();
        }

        public override string GetCauseName()
        {
            return ((CMoProceedingRRCReleaseWithEarfcnCauseCond)Cond).ReasonName;
        }
    }

    public class CMoProceedingRRCReleaseCause : CCauseBase
    {
        public CMoProceedingRRCReleaseCause()
        {
            Cond = new CMoProceedingRRCReleaseCauseCond();
        }

        public override string GetCauseName()
        {
            return "基站故障";//被叫未发生回落
        }
    }

    public class CMtLURCauseCause : CCauseBase
    {
        public CMtLURCauseCause()
        {
            Cond = new CMtLURCauseCond();
        }

        public override string GetCauseName()
        {
            return "边界站点规划不合理";// "被叫位置更新"
        }
    }

    public class CTCHDisconnectCause : CCauseBase
    {
        public CTCHDisconnectCause()
        {
            Cond = new CTCHDisconnectCauseCond();
        }

        public override string GetCauseName()
        {
            return ((CTCHDisconnectCauseCond)Cond).ResonName;
        }
    }

    public class CPersonDoesCause : CCauseBase
    {
        public CPersonDoesCause()
        {
            Cond = new CPersonDoesCauseCond();
        }

        public override string GetCauseName()
        {
            return "人为原因";
        }
    }

    public class CPersonDoes2Cause : CCauseBase
    {
        public CPersonDoes2Cause()
        {
            Cond = new CPersonDoes2CauseCond();
        }

        public override string GetCauseName()
        {
            return "基站故障";
        }
    }

    public class CPretendCause : CCauseBase
    {
        public CPretendCause()
        {
            Cond = new CPretendCauseCond();
        }

        public override string GetCauseName()
        {
            return "伪基站";
        }
    }

    public class CServiceAbortCause : CCauseBase
    {
        public CServiceAbortCause()
        {
            Cond = new CServiceAbortCauseCond();
        }

        public override string GetCauseName()
        {
            return "基站故障";
        }
    }

    public class CBlockCallCause : CCauseBase
    {
        public CBlockCallCause()
        {
            Cond = new CBlockCallCauseCond();
        }

        public override string GetCauseName()
        {
            return "人为原因";
        }
    }
}
