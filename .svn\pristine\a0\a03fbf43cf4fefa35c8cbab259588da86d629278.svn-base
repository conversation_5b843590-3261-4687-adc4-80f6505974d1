﻿namespace MasterCom.RAMS.Func.PopShow
{
    partial class kpiAlarmCfgForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.comboBoxTable = new System.Windows.Forms.ComboBox();
            this.listColumn = new System.Windows.Forms.ListView();
            this.columnIdx = new System.Windows.Forms.ColumnHeader();
            this.columnType = new System.Windows.Forms.ColumnHeader();
            this.columnDesc = new System.Windows.Forms.ColumnHeader();
            this.columnEXP = new System.Windows.Forms.ColumnHeader();
            this.label1 = new System.Windows.Forms.Label();
            this.btnSaveToDB = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.edtHistoryDays = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.btnModifyAlarm = new System.Windows.Forms.Button();
            this.ckbIsAlarm = new System.Windows.Forms.CheckBox();
            this.label10 = new System.Windows.Forms.Label();
            this.edtCmpThreshhold = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.edtAbsThreshhold = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.cmbCmpMethod = new System.Windows.Forms.ComboBox();
            this.label4 = new System.Windows.Forms.Label();
            this.cmbCheckMethod = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.cmbCheckPeriodType = new System.Windows.Forms.ComboBox();
            this.btnExit = new System.Windows.Forms.Button();
            this.columnAbsThreshHold = new System.Windows.Forms.ColumnHeader();
            this.columnCmpThreshHold = new System.Windows.Forms.ColumnHeader();
            this.columnPeriod = new System.Windows.Forms.ColumnHeader();
            this.columnHisDays = new System.Windows.Forms.ColumnHeader();
            this.groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // comboBoxTable
            // 
            this.comboBoxTable.FormattingEnabled = true;
            this.comboBoxTable.Location = new System.Drawing.Point(132, 10);
            this.comboBoxTable.Name = "comboBoxTable";
            this.comboBoxTable.Size = new System.Drawing.Size(248, 20);
            this.comboBoxTable.TabIndex = 0;
            this.comboBoxTable.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // listColumn
            // 
            this.listColumn.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnIdx,
            this.columnDesc,
            this.columnType,
            this.columnAbsThreshHold,
            this.columnCmpThreshHold,
            this.columnPeriod,
            this.columnHisDays});
            this.listColumn.FullRowSelect = true;
            this.listColumn.GridLines = true;
            this.listColumn.HideSelection = false;
            this.listColumn.Location = new System.Drawing.Point(12, 36);
            this.listColumn.MultiSelect = false;
            this.listColumn.Name = "listColumn";
            this.listColumn.Size = new System.Drawing.Size(466, 226);
            this.listColumn.TabIndex = 7;
            this.listColumn.UseCompatibleStateImageBehavior = false;
            this.listColumn.View = System.Windows.Forms.View.Details;
            this.listColumn.SelectedIndexChanged += new System.EventHandler(this.listColumn_SelectedIndexChanged);
            // 
            // columnIdx
            // 
            this.columnIdx.Text = "";
            this.columnIdx.Width = 26;
            // 
            // columnType
            // 
            this.columnType.Text = "分析方法";
            this.columnType.Width = 80;
            // 
            // columnDesc
            // 
            this.columnDesc.Text = "指标名称";
            this.columnDesc.Width = 100;
            // 
            // columnEXP
            // 
            this.columnEXP.Text = "公式";
            this.columnEXP.Width = 178;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(13, 13);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(113, 12);
            this.label1.TabIndex = 34;
            this.label1.Text = "请选择要设置的报表";
            // 
            // btnSaveToDB
            // 
            this.btnSaveToDB.Location = new System.Drawing.Point(281, 400);
            this.btnSaveToDB.Name = "btnSaveToDB";
            this.btnSaveToDB.Size = new System.Drawing.Size(92, 23);
            this.btnSaveToDB.TabIndex = 35;
            this.btnSaveToDB.Text = "保存到数据库";
            this.btnSaveToDB.UseVisualStyleBackColor = true;
            this.btnSaveToDB.Click += new System.EventHandler(this.saveToDB_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.edtHistoryDays);
            this.groupBox2.Controls.Add(this.label11);
            this.groupBox2.Controls.Add(this.btnModifyAlarm);
            this.groupBox2.Controls.Add(this.ckbIsAlarm);
            this.groupBox2.Controls.Add(this.label10);
            this.groupBox2.Controls.Add(this.edtCmpThreshhold);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.edtAbsThreshhold);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.cmbCmpMethod);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Controls.Add(this.cmbCheckMethod);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.cmbCheckPeriodType);
            this.groupBox2.Location = new System.Drawing.Point(12, 270);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(466, 124);
            this.groupBox2.TabIndex = 37;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "告警设置";
            // 
            // edtHistoryDays
            // 
            this.edtHistoryDays.Location = new System.Drawing.Point(72, 73);
            this.edtHistoryDays.Name = "edtHistoryDays";
            this.edtHistoryDays.Size = new System.Drawing.Size(121, 21);
            this.edtHistoryDays.TabIndex = 46;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(12, 77);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(59, 12);
            this.label11.TabIndex = 45;
            this.label11.Text = "比较天数:";
            // 
            // btnModifyAlarm
            // 
            this.btnModifyAlarm.Location = new System.Drawing.Point(368, 99);
            this.btnModifyAlarm.Name = "btnModifyAlarm";
            this.btnModifyAlarm.Size = new System.Drawing.Size(92, 23);
            this.btnModifyAlarm.TabIndex = 44;
            this.btnModifyAlarm.Text = "应用修改";
            this.btnModifyAlarm.UseVisualStyleBackColor = true;
            this.btnModifyAlarm.Click += new System.EventHandler(this.btnModifyAlarm_Click);
            // 
            // ckbIsAlarm
            // 
            this.ckbIsAlarm.AutoSize = true;
            this.ckbIsAlarm.Location = new System.Drawing.Point(72, 103);
            this.ckbIsAlarm.Name = "ckbIsAlarm";
            this.ckbIsAlarm.Size = new System.Drawing.Size(36, 16);
            this.ckbIsAlarm.TabIndex = 43;
            this.ckbIsAlarm.Text = "是";
            this.ckbIsAlarm.UseVisualStyleBackColor = true;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(12, 105);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(59, 12);
            this.label10.TabIndex = 42;
            this.label10.Text = "是否告警:";
            // 
            // edtCmpThreshhold
            // 
            this.edtCmpThreshhold.Location = new System.Drawing.Point(339, 73);
            this.edtCmpThreshhold.Name = "edtCmpThreshhold";
            this.edtCmpThreshhold.Size = new System.Drawing.Size(121, 21);
            this.edtCmpThreshhold.TabIndex = 40;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(259, 77);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(71, 12);
            this.label8.TabIndex = 39;
            this.label8.Text = "波动值门限:";
            // 
            // edtAbsThreshhold
            // 
            this.edtAbsThreshhold.Location = new System.Drawing.Point(339, 46);
            this.edtAbsThreshhold.Name = "edtAbsThreshhold";
            this.edtAbsThreshhold.Size = new System.Drawing.Size(121, 21);
            this.edtAbsThreshhold.TabIndex = 38;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(259, 50);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(71, 12);
            this.label7.TabIndex = 37;
            this.label7.Text = "绝对值门限:";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(12, 50);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(59, 12);
            this.label5.TabIndex = 35;
            this.label5.Text = "比较条件:";
            // 
            // cmbCmpMethod
            // 
            this.cmbCmpMethod.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCmpMethod.FormattingEnabled = true;
            this.cmbCmpMethod.Items.AddRange(new object[] {
            "大于门限值",
            "小于门限值"});
            this.cmbCmpMethod.Location = new System.Drawing.Point(72, 47);
            this.cmbCmpMethod.Name = "cmbCmpMethod";
            this.cmbCmpMethod.Size = new System.Drawing.Size(121, 20);
            this.cmbCmpMethod.TabIndex = 34;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(259, 24);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 12);
            this.label4.TabIndex = 33;
            this.label4.Text = "检查方法:";
            // 
            // cmbCheckMethod
            // 
            this.cmbCheckMethod.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCheckMethod.FormattingEnabled = true;
            this.cmbCheckMethod.Items.AddRange(new object[] {
            "绝对值分析",
            "波动分析",
            "绝对值分析和波动分析"});
            this.cmbCheckMethod.Location = new System.Drawing.Point(339, 20);
            this.cmbCheckMethod.Name = "cmbCheckMethod";
            this.cmbCheckMethod.Size = new System.Drawing.Size(121, 20);
            this.cmbCheckMethod.TabIndex = 32;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(12, 24);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(59, 12);
            this.label3.TabIndex = 31;
            this.label3.Text = "检查周期:";
            // 
            // cmbCheckPeriodType
            // 
            this.cmbCheckPeriodType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCheckPeriodType.FormattingEnabled = true;
            this.cmbCheckPeriodType.Items.AddRange(new object[] {
            "不检查",
            "按周",
            "按月"});
            this.cmbCheckPeriodType.Location = new System.Drawing.Point(72, 21);
            this.cmbCheckPeriodType.Name = "cmbCheckPeriodType";
            this.cmbCheckPeriodType.Size = new System.Drawing.Size(121, 20);
            this.cmbCheckPeriodType.TabIndex = 0;
            // 
            // btnExit
            // 
            this.btnExit.Location = new System.Drawing.Point(380, 400);
            this.btnExit.Name = "btnExit";
            this.btnExit.Size = new System.Drawing.Size(92, 23);
            this.btnExit.TabIndex = 38;
            this.btnExit.Text = "取消";
            this.btnExit.UseVisualStyleBackColor = true;
            this.btnExit.Click += new System.EventHandler(this.btnExit_Click);
            // 
            // columnAbsThreshHold
            // 
            this.columnAbsThreshHold.Text = "绝对门限";
            // 
            // columnCmpThreshHold
            // 
            this.columnCmpThreshHold.Text = "相对门限";
            // 
            // columnPeriod
            // 
            this.columnPeriod.Text = "检查周期";
            // 
            // columnHisDays
            // 
            this.columnHisDays.Text = "历史比较天数";
            this.columnHisDays.Width = 100;
            // 
            // kpiAlarmCfgForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(490, 427);
            this.Controls.Add(this.btnExit);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.btnSaveToDB);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.listColumn);
            this.Controls.Add(this.comboBoxTable);
            this.MaximizeBox = false;
            this.Name = "kpiAlarmCfgForm";
            this.Text = "KPI波动分析设置";
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ComboBox comboBoxTable;
        private System.Windows.Forms.ListView listColumn;
        private System.Windows.Forms.ColumnHeader columnIdx;
        private System.Windows.Forms.ColumnHeader columnType;
        private System.Windows.Forms.ColumnHeader columnDesc;
        private System.Windows.Forms.ColumnHeader columnEXP;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnSaveToDB;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.ComboBox cmbCmpMethod;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ComboBox cmbCheckMethod;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cmbCheckPeriodType;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox edtCmpThreshhold;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox edtAbsThreshhold;
        private System.Windows.Forms.CheckBox ckbIsAlarm;
        private System.Windows.Forms.Button btnExit;
        private System.Windows.Forms.Button btnModifyAlarm;
        private System.Windows.Forms.TextBox edtHistoryDays;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.ColumnHeader columnAbsThreshHold;
        private System.Windows.Forms.ColumnHeader columnCmpThreshHold;
        private System.Windows.Forms.ColumnHeader columnPeriod;
        private System.Windows.Forms.ColumnHeader columnHisDays;
        //private MasterCom.MControls.ColorRangeScreen colorRangeScreen1;
    }
}