﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class CellOccupyAna
    {
        private FileInfo fileHost { get; set; }
        private List<FileInfo> fileListGuest { get; set; }

        private QueryCondition condition { get; set; }

        private OccupyAppResult occupyResult { get; set; }
        public OccupyAppResult OccupyResult
        {
            get { return occupyResult; }
        }

        private List<string> lstColumns
        {
            get
            {
                List<string> columns = new List<string>();
                columns.Add("lte_TAC");
                columns.Add("lte_ECI");
                columns.Add("lte_EARFCN");
                columns.Add("lte_PCI");
                columns.Add("lte_APP_type");
                columns.Add("lte_APP_DataStatus_DL");
                columns.Add("lte_APP_DataStatus_UL");
                columns.Add("lte_gsm_SC_LAC");
                columns.Add("lte_gsm_SC_CI");
                columns.Add("lte_gsm_SC_BCCH");
                columns.Add("lte_gsm_SC_BSIC");
                columns.Add("lte_td_SC_LAC");
                columns.Add("lte_td_SC_CellID");
                columns.Add("lte_td_SC_UARFCN");
                columns.Add("lte_td_SC_CPI");
                columns.Add("mode");

                columns.Add("lte_fdd_TAC");
                columns.Add("lte_fdd_ECI");
                columns.Add("lte_fdd_EARFCN");
                columns.Add("lte_fdd_PCI");
                columns.Add("lte_fdd_APP_type");
                columns.Add("lte_fdd_APP_DataStatus_DL");
                columns.Add("lte_fdd_APP_DataStatus_UL");
                columns.Add("lte_fdd_gsm_SC_LAC");
                columns.Add("lte_fdd_gsm_SC_CI");
                columns.Add("lte_fdd_gsm_SC_BCCH");
                columns.Add("lte_fdd_gsm_SC_BSIC");
                columns.Add("lte_fdd_td_SC_LAC");
                columns.Add("lte_fdd_td_SC_CellID");
                columns.Add("lte_fdd_td_SC_UARFCN");
                columns.Add("lte_fdd_td_SC_CPI");

                return columns;
            }
        }

        public bool IsCheckDlAndUl { get; set; }

        public CellOccupyAna(FileInfo fHost, List<FileInfo> fGuest, bool isCheckDlAndUl)
        {
            this.fileHost = fHost;
            this.fileListGuest = fGuest;
            this.IsCheckDlAndUl = isCheckDlAndUl;
            this.occupyResult = new OccupyAppResult(fHost, fGuest, isCheckDlAndUl);
        }

        public void SetCondition(QueryCondition cond)
        {
            this.condition = cond;
        }

        public void Analyse()
        {
            if (condition == null)
                return;

            WaitBox.Text = string.Format("正在回放同车测试文件：{0}", 
                fileHost.Name);

            QueryCondition cond = new QueryCondition();
            cond.FileInfos.AddRange(occupyResult.ResultHost.FileList);
            cond.FileInfos.AddRange(occupyResult.ResultGuest.FileList);

            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel.GetInstance());
            query.IncludeTestPoint = true;
            query.IncludeEvent = false;
            query.IncludeMessage = false;
            query.Columns = lstColumns;
            query.SetQueryCondition(cond);
            query.Query();

            doStat();
        }

        private void doStat()
        {
            WaitBox.Text = string.Format("正在分析同车测试文件：{0}",
                fileHost.Name);

            List<TestPoint> lstTestPnts = new List<TestPoint>();

            List<TestPoint> lstEndPnts = new List<TestPoint>();
            foreach (DTFileDataManager file in MainModel.GetInstance().DTDataManager.FileDataManagers)
            {
                lstTestPnts.AddRange(file.TestPoints);

                if (file.TestPoints.Count > 0)
                    lstEndPnts.Add(file.TestPoints[file.TestPoints.Count - 1]);

                TestPoint lastPnt = null;
                foreach (TestPoint curPnt in file.TestPoints)
                {
                    if (!isValidTestPoint(curPnt) && lastPnt != null && isValidTestPoint(lastPnt) && !lstEndPnts.Contains(lastPnt))
                    {
                        lstEndPnts.Add(lastPnt);
                    }
                    lastPnt = curPnt;
                }
            }
            lstTestPnts.Sort(new ComparerTestPointByTime());

            foreach (TestPoint pnt in lstTestPnts)
            {
                bool isHost = (pnt.FileName == fileHost.Name);
                if (!isValidTestPoint(pnt))
                {
                    occupyResult.PrevPnt = null;
                    continue;
                }
                occupyResult.DealPoint(pnt, isHost, lstEndPnts);
            }
        }
        private bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                if (condition.Geometorys != null && condition.Geometorys.Region != null && !condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
                {
                    return false;
                }
                return true;
            }
            catch
            {
                return false;
            }
        }
    }

    public class ComparerTestPointByTime : IComparer<TestPoint>
    {
        public int Compare(TestPoint x, TestPoint y)
        {
            return x.lTimeWithMillsecond.CompareTo(y.lTimeWithMillsecond);
        }
    }

    public class OccupyAppResultSummary : OccupyAppResult
    {
        public OccupyAppResultSummary(bool isCheckDlAndUl)
            : base(null, null, isCheckDlAndUl)
        {
            resultHost = new OccupyCellResultSummary();
        }

        public override List<OccupyCellResult> OccupyCellResults
        {
            get
            {
                if (occupyCellResults == null)
                {
                    occupyCellResults = new List<OccupyCellResult>();
                    occupyCellResults.Add(ResultHost);
                }
                return occupyCellResults;
            }
        }

        public override string Name
        {
            get { return "文件汇总"; }
        }

        public void Merge(OccupyAppResult result)
        {
            ResultHost.Merge(result.ResultHost);
            ResultHost.Merge(result.ResultGuest);
        }
    }

    public class OccupyCellResultSummary : OccupyCellResult
    {
        public OccupyCellResultSummary()
            : base(null, true) { }

        public override string ID
        {
            get { return ""; }
        }

        public override string Name
        {
            get { return "汇总"; }
        }
    }

    public class OccupyAppResult
    {
        protected OccupyCellResult resultHost;
        public OccupyCellResult ResultHost
        {
            get { return resultHost; }
        }

        private OccupyCellResult resultGuest { get; set; }
        public OccupyCellResult ResultGuest
        {
            get { return resultGuest; }
        }

        protected List<OccupyCellResult> occupyCellResults = null;
        public virtual List<OccupyCellResult> OccupyCellResults
        {
            get
            {
                if (occupyCellResults==null)
                {
                    occupyCellResults = new List<OccupyCellResult>();
                    occupyCellResults.Add(ResultHost);
                    occupyCellResults.Add(ResultGuest);
                }
                return occupyCellResults;
            }
        }

        public TestPoint PrevPnt { get; set; }

        public virtual string Name
        {
            get
            {
                if (IsCheckDlAndUl)
                {
                    return string.Format("下载：{0} 上传：{1}", resultHost.Name, resultGuest.Name);
                }
                else
                {
                    return string.Format("VoLTE语音：{0} 下载：{1}", resultHost.Name, resultGuest.Name);
                }
            }
        }
        public bool IsCheckDlAndUl { get; set; }
        public OccupyAppResult(FileInfo fileHost, List<FileInfo> fileGuest, bool isCheckDlAndUl)
        {
            resultHost = new OccupyCellResult(new List<FileInfo> { fileHost }, true);
            resultGuest = new OccupyCellResult(fileGuest, false);
            this.IsCheckDlAndUl = isCheckDlAndUl;
        }

        public void DealPoint(TestPoint tp, bool isHost, List<TestPoint> lstEndPnts)
        {
            double duration = calcDuration(tp, PrevPnt);

            int dataStatus = getDataStatus(tp, isHost);

            bool idle = isIdle(tp, dataStatus, isHost);//是否空闲

            OccupyCellResult curResult = getOccupyResult(isHost);//获取当前采样点所在的文件结果对象
            OccupyCellResult oppositeresult = getOccupyResult(!isHost);

            curResult.SetIdleFlag(idle);

            if (curResult.PrevPnt != null && oppositeresult.PrevPnt != null)
            {
                int? freq = null;
                ICell cell = getSrcCell(tp, ref freq);
                curResult.SetPrevCell(cell, freq);

                bool isSameCell = (curResult.PrevCell != null && oppositeresult.PrevCell != null &&
                        curResult.PrevCell.Name == oppositeresult.PrevCell.Name);//是否占用同一小区

                addTime(curResult, oppositeresult, duration, isSameCell);
            }
            curResult.CalcTestDuration(tp);

            curResult.SetPrevPnt(tp);

            PrevPnt = tp;

            if (isEnd(tp, lstEndPnts))
                curResult.SetEnd();
        }

        private bool isEnd(TestPoint tp, List<TestPoint> lstEndPnts)
        {
            foreach (TestPoint endPnt in lstEndPnts)
            {
                if (tp.FileID == endPnt.FileID &&
                    tp.lTimeWithMillsecond  == endPnt.lTimeWithMillsecond)
                {
                    return true;
                }
            }

            return false;
        }

        private OccupyCellResult getOccupyResult(bool isHost)
        {
            if (isHost)
            {
                return resultHost;
            }

            return resultGuest;
        }

        private int getDataStatus(TestPoint tp, bool isHost)
        {
            if (IsCheckDlAndUl)
            {
                if (isHost)
                {
                    return getDLDataStatus(tp);
                }
                else
                {
                    return getULDataStatus(tp);
                }
            }
            else
            {
                if (isHost)
                {
                    return 0;
                }
                else
                {
                    return getDLDataStatus(tp);
                }
            }
        }

        private static int getULDataStatus(TestPoint tp)
        {
            short? status = null;
            if (tp is LTEFddTestPoint)
            {
                status = (short?)tp["lte_fdd_APP_DataStatus_UL"];
            }
            else
            {
                status = (short?)tp["lte_APP_DataStatus_UL"];
            }

            if (status == null)
            {
                return 0;
            }
            return (int)status;
        }

        private static int getDLDataStatus(TestPoint tp)
        {
            short? status = null;
            if (tp is LTEFddTestPoint)
            {
                status = (short?)tp["lte_fdd_APP_DataStatus_DL"];
            }
            else
            {
                status = (short?)tp["lte_APP_DataStatus_DL"];
            }

            if (status == null)
            {
                return 0;
            }
            return (int)status;
        }

        private double calcDuration(TestPoint curTp, TestPoint lastTp)
        {
            if (lastTp == null)
                return 0;

            return curTp.lTimeWithMillsecond - lastTp.lTimeWithMillsecond;
        }

        private bool isIdle(TestPoint tp, int dataStatus, bool isHost)
        {
            if (IsCheckDlAndUl)
            {
                short? appType = getAppType(tp);
                if (appType == null)
                {
                    return true;
                }

                bool isIdle = isHost ? isDownloadIdle((short)appType) : isUploadIdle((short)appType);

                return isIdle || dataStatus <= 0;
            }
            else
            {
                return judgeModeStatus(tp, dataStatus, isHost);
            }
        }

        private bool judgeModeStatus(TestPoint tp, int dataStatus, bool isHost)
        {
            if (isHost)
            {
                short? mode = (short?)tp["mode"];
                if (mode == (int)TestPoint.EMODE.GSM_DEDICATED || mode == (int)TestPoint.EMODE.TDSCDMA_DEDICATED
                    || mode == (int)TestPoint.EMODE.LTE_DEDICATED)//Volte语音文件的通话态即为业务态，否则为空闲态
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            else
            {
                short? appType = getAppType(tp);
                if (appType == null)
                {
                    return true;
                }

                bool isIdle = isDownloadIdle((short)appType);

                return isIdle || dataStatus <= 0;
            }
        }

        private short? getAppType(TestPoint tp)
        {
            short? appType;
            if (tp is LTEFddTestPoint)
            {
                appType = (short?)tp["lte_fdd_APP_type"];
            }
            else
            {
                appType = (short?)tp["lte_APP_type"];
            }

            return appType;
        }

        private bool isDownloadIdle(short appType)
        {
            return appType != (int)AppType.FTP_Download && appType != (int)AppType.Http_Download
                && appType != (int)AppType.Http_Video && appType != (int)AppType.Http; 
        }
        private bool isUploadIdle(short appType)
        {
            return appType != (int)AppType.FTP_Upload;
        }

        private void addTime(OccupyCellResult curResult, OccupyCellResult oppositeResult
            , double duration, bool isSameCell)
        {
            if (curResult.IsOccupy && oppositeResult.IsOccupy)//均业务态
            {
                if (isSameCell)
                {
                    curResult.AddSameCellOccupy(duration);
                    oppositeResult.AddSameCellOccupy(duration);
                }
                else
                {
                    if (curResult.Freq != null && oppositeResult.Freq != null && curResult.Freq == oppositeResult.Freq)
                    {
                        curResult.AddDiffCellCoFreqOccupy(duration);
                        oppositeResult.AddDiffCellCoFreqOccupy(duration);
                    }
                    else
                    {
                        curResult.AddDiffCellDiFreqOccupy(duration);
                        oppositeResult.AddDiffCellDiFreqOccupy(duration);
                    }
                }
            }
            else if (curResult.IsIdle && oppositeResult.IsIdle)//均空闲
            {
                curResult.AddIdle(duration, isSameCell);
                oppositeResult.AddIdle(duration, isSameCell);
            }
            else
            {
                //判断主端OccupyCellResult是否处于业务态
                bool isHostOccupy = curResult.IsHost ? curResult.IsOccupy : oppositeResult.IsOccupy;

                curResult.AddSingleOccupy(duration, isHostOccupy, isSameCell);
                oppositeResult.AddSingleOccupy(duration, isHostOccupy, isSameCell);
            }
        }

        private ICell getSrcCell(TestPoint tp, ref int? freq)
        {
            LTECell lteCell = tp.GetMainLTECell_TdOrFdd();
            if (lteCell != null)
            {
                freq = lteCell.EARFCN;
                return lteCell;
            }

            Cell gsmCell = tp.GetMainCell_LTE_GSM();
            if (gsmCell != null)
            {
                freq = gsmCell.BCCH;
                return gsmCell;
            }

            TDCell tdCell = tp.GetMainCell_LTE_TD();
            if (tdCell != null)
            {
                freq = tdCell.FREQ;
                return tdCell;
            }

            freq = GetEARFCN(tp);
            if (freq == null)
            {
                freq = GetBCCH(tp);
            }
            if (freq == null)
            {
                freq = GetUARFCN(tp);
            }
            return tp.GetMainCell();
        }

        protected int? GetEARFCN(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (int?)tp["lte_fdd_EARFCN"];
            }
            return (int?)tp["lte_EARFCN"];
        }
        protected short? GetBCCH(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (short?)tp["lte_fdd_gsm_SC_BCCH"];
            }
            return (short?)tp["lte_gsm_SC_BCCH"];
        }
        protected int? GetUARFCN(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (int?)tp["lte_fdd_td_SC_UARFCN"];
            }
            return (int?)tp["lte_td_SC_UARFCN"];
        }
    }

    public class OccupyCellResult
    {
        private List<FileInfo> fileList { get; set; }
        public List<FileInfo> FileList
        {
            get { return fileList; }
        }

        public virtual string ID
        {
            get
            {
                if (fileList != null)
                {
                    StringBuilder strb = new StringBuilder();
                    foreach (FileInfo file in fileList)
                    {
                        strb.Append(file.ID + ",");
                    }
                    if (strb.Length > 0)
                    {
                        strb.Remove(strb.Length - 1, 1);
                    }
                    return strb.ToString();
                }
                return "";
            }
        }

        public virtual string Name
        {
            get 
            {
                if (fileList != null)
                {
                    StringBuilder strb = new StringBuilder();
                    foreach (FileInfo file in fileList)
                    {
                        strb.Append(file.Name + ",");
                    }
                    if (strb.Length > 0)
                    {
                        strb.Remove(strb.Length - 1, 1);
                    }
                    return strb.ToString();
                }
                return ""; 
            }
        }
        public int? Freq { get; set; }

        public bool IsHost { get; set; } = false;

        private ICell prevCell = null;
        public ICell PrevCell
        {
            get { return prevCell; }
        }

        private TestPoint prevPnt = null;
        public TestPoint PrevPnt
        {
            get { return prevPnt; }
        }

        private bool isIdle;
        public bool IsIdle
        {
            get { return isIdle && prevPnt != null; }
        }

        public bool IsOccupy
        {
            get{return !isIdle && prevPnt != null;}
        }

        private double test_Duration = 0;
        public double TestDuration
        {
            get
            {
                return Math.Round(test_Duration / 1000, 2);
            }
        }

        private double bothIdle_SameCell_Duration = 0;//均空闲且占同一小区的时间
        public double BothIdle_SameCell_Percent
        {
            get { return Math.Round(100 * bothIdle_SameCell_Duration / test_Duration, 2); }
        }

        private double bothIdle_DiffCell_Duration = 0;//均空闲且占不同小区的时间
        public double BothIdle_DiffCell_Percent
        {
            get { return Math.Round(100 * bothIdle_DiffCell_Duration / test_Duration, 2); }
        }

        private double hostIdleGuestOccupy_SameCell_Duration = 0;//主端空闲，对端业务态，占同一小区的时间
        public double HostIdleGuestOccupy_SameCell_Percent
        {
            get { return Math.Round(100 * hostIdleGuestOccupy_SameCell_Duration / test_Duration, 2); }
        }

        private double hostIdleGuestOccupy_DiffCell_Duration = 0;//主端空闲，对端业务态，占不同小区的时间
        public double HostIdleGuestOccupy_DiffCell_Percent
        {
            get { return Math.Round(100 * hostIdleGuestOccupy_DiffCell_Duration / test_Duration, 2); }
        }

        private double hostOccupyGuestIdle_SameCell_Duration = 0;//主端业务态，对端空闲，占同一小区的时间
        public double HostOccupyGuestIdle_SameCell_Percent
        {
            get { return Math.Round(100 * hostOccupyGuestIdle_SameCell_Duration / test_Duration, 2); }
        }

        private double hostOccupyGuestIdle_DiffCell_Duration = 0;//主端业务态，对端空闲，占同一小区的时间
        public double HostOccupyGuestIdle_DiffCell_Percent
        {
            get { return Math.Round(100 * hostOccupyGuestIdle_DiffCell_Duration / test_Duration, 2); }
        }

        private double sameCellOccupy_Duration = 0;//均业务态且占同一小区的时间
        public double SameCellOccupyPercent
        {
            get { return Math.Round(100 * sameCellOccupy_Duration / test_Duration, 2); }
        }

        private double diffCellDiFreqOccupy_Duration = 0;//均业务态，占用异频不同小区的时间
        public double DiffCellDiFreqOccupyPercent
        {
            get { return Math.Round(100 * diffCellDiFreqOccupy_Duration / test_Duration, 2); }
        }

        private double diffCellCoFreqOccupy_Duration = 0;//均业务态，占用同频不同小区的时间
        public double DiffCellCoFreqOccupyPercent
        {
            get { return Math.Round(100 * diffCellCoFreqOccupy_Duration / test_Duration, 2); }
        }

        public OccupyCellResult(List<FileInfo> fileList, bool isHost)
        {
            this.fileList = fileList;
            isIdle = true;
            this.IsHost = isHost;
        }
        public void SetIdleFlag(bool isIdle)
        {
            this.isIdle = isIdle;
        }

        public void SetPrevCell(ICell cell,int? freq)
        {
            this.prevCell = cell;
            this.Freq = freq;
        }

        public void SetPrevPnt(TestPoint tp)
        {
            this.prevPnt = tp;
        }

        public void SetEnd()
        {
            prevCell = null;
            prevPnt = null;
        }

        public void CalcTestDuration(TestPoint tp)
        {
            this.test_Duration += calcDuration(tp, prevPnt);
        }

        public void AddIdle(double duration, bool isSameCell)
        {
            if (isSameCell)
            {
                this.bothIdle_SameCell_Duration += duration;
            }
            else
            {
                this.bothIdle_DiffCell_Duration += duration;
            }
        }

        public void AddSingleOccupy(double duration, bool hostOccupy, bool isSameCell)
        {
            if (hostOccupy)
            {
                addHostOccupyOnly(duration, isSameCell);
            }
            else
            {
                addGuestOccupyOnly(duration, isSameCell);
            }
        }

        private void addHostOccupyOnly(double duration, bool isSameCell)
        {
            if (isSameCell)
            {
                this.hostOccupyGuestIdle_SameCell_Duration += duration;
            }
            else
            {
                this.hostOccupyGuestIdle_DiffCell_Duration += duration;
            }
        }

        private void addGuestOccupyOnly(double duration, bool isSameCell)
        {
            if (isSameCell)
            {
                this.hostIdleGuestOccupy_SameCell_Duration += duration;
            }
            else
            {
                this.hostIdleGuestOccupy_DiffCell_Duration += duration;
            }
        }

        public void AddSameCellOccupy(double duration)
        {
            this.sameCellOccupy_Duration += duration;
        }

        public void AddDiffCellDiFreqOccupy(double duration)
        {
            this.diffCellDiFreqOccupy_Duration += duration;
        }
        public void AddDiffCellCoFreqOccupy(double duration)
        {
            this.diffCellCoFreqOccupy_Duration += duration;
        }

        private double calcDuration(TestPoint curTp, TestPoint lastTp)
        {
            if (lastTp == null)
                return 0;

            return curTp.lTimeWithMillsecond - lastTp.lTimeWithMillsecond;
        }

        public virtual void Merge(OccupyCellResult cellResult)
        {
            this.test_Duration += cellResult.test_Duration;
            this.bothIdle_SameCell_Duration += cellResult.bothIdle_SameCell_Duration;
            this.bothIdle_DiffCell_Duration += cellResult.bothIdle_DiffCell_Duration;
            this.hostIdleGuestOccupy_SameCell_Duration += cellResult.hostIdleGuestOccupy_SameCell_Duration;
            this.hostIdleGuestOccupy_DiffCell_Duration += cellResult.hostIdleGuestOccupy_DiffCell_Duration;
            this.hostOccupyGuestIdle_SameCell_Duration += cellResult.hostOccupyGuestIdle_SameCell_Duration;
            this.hostOccupyGuestIdle_DiffCell_Duration += cellResult.hostOccupyGuestIdle_DiffCell_Duration;
            this.sameCellOccupy_Duration += cellResult.sameCellOccupy_Duration;
            this.diffCellDiFreqOccupy_Duration += cellResult.diffCellDiFreqOccupy_Duration;
            this.diffCellCoFreqOccupy_Duration += cellResult.diffCellCoFreqOccupy_Duration;
        }
    }
}
