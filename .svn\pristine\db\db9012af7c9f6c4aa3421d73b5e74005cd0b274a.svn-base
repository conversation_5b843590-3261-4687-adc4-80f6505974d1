﻿using MasterCom.ES.Data;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteRxQualAnaByRegion_FDD : ZTGSMRxQualAnaByRegion
    {
        public ZTLteRxQualAnaByRegion_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26071, this.Name);
        }

        Dictionary<Cell4GSMRxQual, LTERxQualCellInfo_FDD> cellInfoDicFdd = null;  //小区维度

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_TotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_TotalEc_Io";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_SNeiRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE NoMainCell");
            tmpDic.Add("themeName", (object)"LTE RxLevSub");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
        protected override bool getCondition(out GSMRxQualAnaCondition rxQualCond)
        {
            rxQualCond = new GSMRxQualAnaCondition();
            ZTGSMRxQualAnaSetForm dlg = new ZTGSMRxQualAnaSetForm();
            dlg.InitForLTE();
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            dlg.getCondition(out rxQualCond);
            rxQualCond.FreqInterference_C2I = int.MinValue;
            return true;
        }
        protected override bool getReadyBeforeQuery()
        {
            if (!this.getCondition(out rxQualCond))
            {
                return false;
            }

            regionInfoDic = new Dictionary<string, GSMRxQualRegionInfo>();
            cellInfoDicFdd = new Dictionary<Cell4GSMRxQual, LTERxQualCellInfo_FDD>();
            fileEventDic = new Dictionary<int, List<Event>>();
            regionMopDic = new Dictionary<string, MapOperation2>();
            tpList_Pending = new List<GSMRxQualPendingPointInfo>();

            PrepareEvents();
            InitRegionMop2();

            return true;
        }
        /// <summary>
        ///准备查询质差原因所需的事件
        /// </summary>
        protected override void PrepareEvents()
        {
            DIYEventByRegion queryEvent = new DIYEventByRegion(MainModel);
            queryEvent.SetIsAddEventToDTDataManager(false);
            queryEvent.SetSaveAsFileEventsDic(true);
            queryEvent.showEventChooser = false;
            queryEvent.IsQueryAllEvents = false;
            List<int> eventIds = new List<int>();
            eventIds.Add(1146);//VOLTE eSRVCC HandOver Success(LTE -> GSM)  ==> gsm:17
            //eventIds.Add(1134);//LTE切换不及时事件
            eventIds.Add(1074); //VoLte MO Call Established     ==> gsm:4
            eventIds.Add(1080);//VOLTE MO Block Call  ==> gsm:10
            condition.EventIDs = eventIds;
            queryEvent.SetQueryCondition(condition);
            queryEvent.Query();
            fileEventDic = queryEvent.fileEventsDic;
        }
        protected override void dealPendingList()
        {
            foreach (GSMRxQualPendingPointInfo tpPending in tpList_Pending)
            {
                GSMRxQualRegionInfo regionInfo = regionInfoDic[tpPending.regionTag];
                LTERxQualCellInfo_FDD cellInfo = cellInfoDicFdd[tpPending.cellTag];

                //在同一个文件中找
                string reason = "";
                reason = getReasonFromNearestPointFdd(cellInfo, tpPending);
                addToRegion(reason, tpPending.tp, ref regionInfo);
                addToCellFdd(reason, tpPending.tp, ref cellInfo);
            }
        }
        protected override void fillCellOtherInfo()
        {
            Cursor.Current = Cursors.WaitCursor;

            foreach (Cell4GSMRxQual cell in cellInfoDicFdd.Keys)
            {
                if (cell.Name == "未知小区")
                {
                    cellInfoDicFdd[cell].areaName = "";
                    cellInfoDicFdd[cell].roadName = "";
                }
                else
                {
                    cellInfoDicFdd[cell].areaName = GISManager.GetInstance().GetGridDesc(cell.Longitude, cell.Latitude);
                    cellInfoDicFdd[cell].roadName = GISManager.GetInstance().GetRoadPlaceDesc(cell.Longitude, cell.Latitude);
                }
            }

            Cursor.Current = Cursors.Default;
        }
        protected string getReasonFromNearestPointFdd(LTERxQualCellInfo_FDD cellInfo, GSMRxQualPendingPointInfo tpPending)
        {
            string strFileName = tpPending.tp.FileName;

            if (cellInfo.fileDic.ContainsKey(strFileName))  //取出文件对应的所有点
            {
                List<GSMRxQualPointInfo> fileTpList = cellInfo.fileDic[strFileName];

                GSMRxQualPointInfo afterTp, beforeTp;
                getValidTP(tpPending, fileTpList, out afterTp, out beforeTp);

                if (afterTp != null)
                {
                    return afterTp.reason;
                }
                else if (beforeTp != null)
                {
                    return beforeTp.reason;
                }
                else
                {
                    return "其它";
                }
            }
            else
            {
                return "其它";
            }
        }

        private static void getValidTP(GSMRxQualPendingPointInfo tpPending, List<GSMRxQualPointInfo> fileTpList, out GSMRxQualPointInfo afterTp, out GSMRxQualPointInfo beforeTp)
        {
            afterTp = null;
            beforeTp = null;
            foreach (GSMRxQualPointInfo tpFile in fileTpList)
            {
                if ((tpPending.tp.Time - tpFile.colorTp.tp.Time) <= 0 && (tpPending.tp.Time - tpFile.colorTp.tp.Time) <= -5)   //向后看5秒内
                {
                    if (afterTp == null)
                    {
                        afterTp = tpFile;
                    }
                    else if (afterTp.colorTp.tp.Time > tpFile.colorTp.tp.Time)  //更近
                    {
                        afterTp = tpFile;
                    }
                }
                else if ((tpPending.tp.Time - tpFile.colorTp.tp.Time) > 0 && (tpPending.tp.Time - tpFile.colorTp.tp.Time) <= 5)  //向前看5秒内
                {
                    if (beforeTp == null)
                    {
                        beforeTp = tpFile;
                    }
                    else if (beforeTp.colorTp.tp.Time < tpFile.colorTp.tp.Time)  //更近
                    {
                        beforeTp = tpFile;
                    }
                }
            }
        }

        protected override void showResult()
        {
            ZTGSMRxQualAnaListForm form = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTGSMRxQualAnaListForm)) as ZTGSMRxQualAnaListForm;
            if (form == null || form.IsDisposed)
            {
                form = new ZTGSMRxQualAnaListForm(MainModel);
            }
            form.FillDataFdd(regionInfoDic, cellInfoDicFdd);
            form.Show(MainModel.MainForm);
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                float? rxQual = (float?)tp["lte_fdd_wcdma_TotalEc_Io"];
                if (rxQual != null && rxQual >= 0 && rxQual <= 7)
                {
                    try
                    {
                        return Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                    }
                    catch
                    {
                        try
                        {
                            return Condition.Geometorys.GeoOp2.CheckPointInRegion(tp.Longitude, tp.Latitude);//网络体检后台
                        }
                        catch (Exception)
                        {
                            //continue
                        }
                        return false;
                    }
                }
            }
            return false;
        }
        protected override bool isPoorRxQual(TestPoint tp)
        {
            float? rxqual = (float?)tp["lte_fdd_wcdma_TotalEc_Io"];
            if (rxqual < 5)
            {
                return false;
            }
            return true;
        }

        protected WCell getCellFdd(TestPoint tp)
        {
            return tp.GetMainCell_LTE_FDD_W();
        }
        protected LTERxQualCellInfo_FDD getCurCellFdd(TestPoint tp)
        {
            WCell cell = this.getCellFdd(tp);
            Cell4GSMRxQual cellTag = getCellTagFdd(tp, cell);

            LTERxQualCellInfo_FDD curCell = null;
            if (cellInfoDicFdd.ContainsKey(cellTag))
            {
                curCell = cellInfoDicFdd[cellTag];
                curCell.tpTotal++;
            }
            else
            {
                curCell = new LTERxQualCellInfo_FDD(cell);
                cellInfoDicFdd[cellTag] = curCell;
            }
            return curCell;
        }
        protected Cell4GSMRxQual getCellTagFdd(TestPoint tp, WCell cell)
        {
            Cell4GSMRxQual cellTag = new Cell4GSMRxQual();
            if (cell != null)
            {
                cellTag.Name = cell.Name;
                cellTag.LAC = cell.LAC;
                cellTag.CI = cell.CI;
                cellTag.Longitude = cell.Longitude;
                cellTag.Latitude = cell.Latitude;
            }
            else
            {
                cellTag.Name = "未知小区";
                if (tp["lte_fdd_wcdma_SysLAI"] != null)
                {
                    cellTag.LAC = (int)tp["lte_fdd_wcdma_SysLAI"];
                }
                else
                {
                    cellTag.LAC = 0;
                }
                if (tp["lte_fdd_wcdma_SysCellID"] != null)
                {
                    cellTag.CI = (int)tp["lte_fdd_wcdma_SysCellID"];
                }
                else
                {
                    cellTag.CI = 0;
                }
                cellTag.Longitude = 0;
                cellTag.Latitude = 0;
            }
            return cellTag;
        }
        protected override void doWithDTData(TestPoint tp)
        {
            GSMRxQualRegionInfo curRegion = getCurRegion(tp);
            if (curRegion == null)
            {
                return;
            }
            LTERxQualCellInfo_FDD curCell = getCurCellFdd(tp);

            if (!isPoorRxQual(tp))
            {
                return;
            }

            //如果没找到原因，暂时放入pending表中，等最后进行处理
            string reason = getResult(tp);
            if (reason == "其它")
            {
                string regionTag = isContainPoint(new DbPoint(tp.Longitude, tp.Latitude));
                Cell4GSMRxQual cellTag = getCellTagFdd(tp, curCell.cell);
                GSMRxQualPendingPointInfo pendingInfo = new GSMRxQualPendingPointInfo(tp, cellTag, regionTag);
                tpList_Pending.Add(pendingInfo);
                return;
            }
            addToRegion(reason, tp, ref curRegion);
            addToCellFdd(reason, tp, ref curCell);
        }
        protected GSMRxQualPointInfo getInfoInstanceFdd(int SN, string reason, TestPoint tp)
        {
            return new LTERxQualPointInfo_FDD(SN, reason, tp);
        }
        /// <summary>
        /// 按小区归分问题详情
        /// </summary>
        public void addToCellFdd(string strReason, TestPoint tp, ref LTERxQualCellInfo_FDD curCell)
        {
            curCell.rxQualTotal++;

            if (curCell.reasonDic.ContainsKey(strReason))
            {
                curCell.reasonDic[strReason].rxQualTotal++;
                curCell.reasonDic[strReason].tpList.Add(new GSMRxQualReasonColorTestPoint(strReason, tp));
            }

            if (curCell.fileDic.ContainsKey(tp.FileName))
            {
                int SN = curCell.fileDic[tp.FileName].Count + 1;
                curCell.fileDic[tp.FileName].Add(this.getInfoInstanceFdd(SN, strReason, tp));
            }
            else
            {
                GSMRxQualPointInfo fileInfo = this.getInfoInstanceFdd(1, strReason, tp);
                List<GSMRxQualPointInfo> tpList = new List<GSMRxQualPointInfo>();
                tpList.Add(fileInfo);
                curCell.fileDic.Add(tp.FileName, tpList);
            }
        }

        #region 覆盖类原因
        /// <summary>
        /// 是否弱覆盖
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isPoorCover(TestPoint tp)
        {
            float? sRxLev = (float?)tp["lte_fdd_wcdma_TotalRSCP"];
            float? nbRxLev = (float?)tp["lte_fdd_wcdma_SNeiRSCP", 0];
            if (sRxLev != null && nbRxLev != null
                && sRxLev <= rxQualCond.WeakCover_ServCell_PccpchRscp 
                && nbRxLev <= rxQualCond.WeakCover_NBCell_PccpchRscp)
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 是否过覆盖
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isCoverLap(TestPoint tp)
        {
            WCell mainCell = tp.GetMainCell_LTE_FDD_W();

            if (mainCell != null)
            {
                double cellDistance = mainCell.GetDistance((tp.Longitude), (tp.Latitude));
                double maxDistance = (double)rxQualCond.LapCover_Ratio * CfgDataProvider.CalculateRadius(mainCell, 3);

                float? sRxLev = (float?)tp["lte_fdd_wcdma_TotalRSCP"];
                if (sRxLev != null && sRxLev >= rxQualCond.LapCover_PccpchRscp && cellDistance >= maxDistance)
                {
                    return true;
                }
                return false;
            }
            return false;
        }
        /// <summary>
        /// 是否背向覆盖
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isBackCover(TestPoint tp)
        {
            WCell mainCell = tp.GetMainCell_LTE_FDD_W();

            if (mainCell == null)
            {
                return false;
            }
            if (mainCell.Direction > 360)
            {
                return false;
            }
            if (mainCell.GetDistance(tp.Longitude, tp.Latitude) < rxQualCond.BackCover_Distance)
            {
                return false;
            }
            if (!isValidAngleFdd(mainCell, tp.Longitude, tp.Latitude))
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 采样点到小区的夹角是否属于正常角度
        /// </summary>
        private bool isValidAngleFdd(WCell cell, double longitude, double latitude)
        {
            double angleDiff = 0;
            double distance = cell.GetDistance(longitude, latitude);

            ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
            double angle;
            double ygap = cell.GetDistance(cell.Longitude, latitude);
            double angleV = Math.Acos(ygap / distance);
            if (longitude >= cell.Longitude && latitude >= cell.Latitude)//1象限
            {
                angle = angleV * 180 / Math.PI;
            }
            else if (longitude <= cell.Longitude && latitude >= cell.Latitude)//2象限
            {
                angle = 360 - angleV * 180 / Math.PI;
            }
            else if (longitude <= cell.Longitude && latitude <= cell.Latitude)//3象限
            {
                angle = 180 + angleV * 180 / Math.PI;
            }
            else//4象限
            {
                angle = 180 - angleV * 180 / Math.PI;
            }

            angleDiff = Math.Abs(angle - cell.Direction);
            if (angleDiff > 180)
            {
                angleDiff = 360 - angleDiff;
            }
            if (angleDiff > 90)
            {
                return false;
            }
            return true;
        }
        /// <summary>
        /// 是否覆盖杂乱
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isNoMainCell(TestPoint tp)
        {
            int count = 0;
            List<int> rxLevList = new List<int>();

            //主服场强
            float? sRxLev = (float?)tp["lte_fdd_wcdma_TotalRSCP"];
            if (sRxLev != null)
            {
                rxLevList.Add((int)sRxLev);
            }
            else
            {
                return false;
            }

            //邻区场强
            try
            {
                for (int i = 0; i < 6; i++)
                {
                    float? nbRxLev = (float?)tp["lte_fdd_wcdma_SNeiRSCP", i];
                    if (nbRxLev == null)
                    {
                        break;
                    }
                    else
                    {
                        rxLevList.Add((int)nbRxLev);
                    }
                }

                //排序后统计与最强信号6dB内的小区数量
                if (rxLevList.Count > 0)
                {
                    rxLevList.Sort();

                    int index = rxLevList.Count;
                    float maxRscp = rxLevList[index - 1];

                    for (int i = 0; i < index; i++)
                    {
                        if ((maxRscp - rxLevList[i]) < rxQualCond.NoMainCover_PccpchRscp_Span)
                        {
                            count++;
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
            return count >= rxQualCond.NoMainCover_CellCount;
        }
        /// <summary>
        /// 是否室分小区
        /// </summary>
        protected override bool isIndoor(TestPoint tp)
        {
            WCell mainCell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, (int?)tp["W_SysLAI"], (int?)tp["W_SysCellID"], (int?)tp["W_frequency"], (int?)tp["W_Reference_PSC"], tp.Longitude, tp.Latitude);
            if (mainCell != null)
            {
                if (mainCell.Type == WNodeBType.Indoor)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return false;
        }

        #endregion

        #region 切换类原因
        /// <summary>
        /// 是否切换不及时  240
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isHODelay(TestPoint tp)
        {
            if (fileEventDic.ContainsKey(tp.FileID))
            {
                List<Event> evtList = fileEventDic[tp.FileID];
                foreach (Event evt in evtList)
                {
                    if (evt.ID == 240)
                    {
                        int iTime = int.Parse((evt["Value3"].ToString())) / 1000 + 1;//切换不及时持续时间
                        if (tp.DateTime >= evt.DateTime.AddSeconds(0 - iTime) && tp.DateTime <= evt.DateTime)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
        /// <summary>
        /// 是否切换不合理 
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isHandoverProblem(TestPoint tp)
        {
            if (fileEventDic.ContainsKey(tp.FileID))
            {
                List<Event> evtList = fileEventDic[tp.FileID];
                List<Event> hoEvtList = getHoEvtList(evtList);

                int i = 0;
                foreach (Event evt in hoEvtList)
                {
                    i++;
                    DateTime dCurTime = evt.DateTime;
                    DateTime dNextTime = getNextTime(hoEvtList, i, dCurTime);

                    //切换前后2秒比较
                    int ibeforeHoBcch = int.Parse(evt["Value1"].ToString());
                    int iafterHoBcch = int.Parse(evt["Value3"].ToString());
                    if (ibeforeHoBcch >= 128 || iafterHoBcch < 128)
                    {
                        int ibeforeHoRelev = int.Parse(evt["Value6"].ToString());
                        int iafterHoRelev = int.Parse(evt["Value8"].ToString());
                        if (ibeforeHoRelev > iafterHoRelev
                            && tp.DateTime >= dCurTime && tp.DateTime <= dNextTime)
                        {
                            return true;
                        }
                    }
                }
            }
            isHandoverFrequently(tp);  //切换频繁归类到切换不合理
            return false;
        }

        private static List<Event> getHoEvtList(List<Event> evtList)
        {
            List<Event> hoEvtList = new List<Event>();

            foreach (Event evt in evtList)
            {
                if (evt.ID == 3141 || evt.ID == 3144 || evt.ID == 3147 || evt.ID == 3150)
                {
                    hoEvtList.Add(evt);
                }
            }

            return hoEvtList;
        }

        private static DateTime getNextTime(List<Event> hoEvtList, int i, DateTime dCurTime)
        {
            DateTime dNextTime;
            if (hoEvtList.Count <= i) //最后一个事件
            {
                dNextTime = dCurTime.AddSeconds(10);
            }
            else
            {
                DateTime tmpTime = hoEvtList[i].DateTime;//下一事件时间
                if (tmpTime >= dCurTime.AddSeconds(10))
                {
                    dNextTime = dCurTime.AddSeconds(10);
                }
                else
                {
                    dNextTime = tmpTime;
                }
            }

            return dNextTime;
        }

        /// <summary>
        /// 是否切换频繁
        /// </summary>
        protected override bool isHandoverFrequently(TestPoint tp)
        {
            if (fileEventDic.ContainsKey(tp.FileID))
            {
                List<Event> evtList = fileEventDic[tp.FileID];
                foreach (Event evt in evtList)
                {
                    if ((evt.ID == 3135 || evt.ID == 3136)
                        && tp.DateTime >= evt.DateTime.AddSeconds(-rxQualCond.HOMore_Span) && tp.DateTime <= evt.DateTime)
                    {
                        //与第一邻区比较
                        return true;
                    }
                }
            }
            return false;
        }
        #endregion
    }

    public class LTERxQualCellInfo_FDD
    {
        public WCell cell { get; set; }
        public int tpTotal { get; set; }
        public int rxQualTotal { get; set; }

        public string areaName { get; set; }
        public string roadName { get; set; }
        public Dictionary<string, GSMRxQualReasonInfo> reasonDic { get; set; }
        public Dictionary<string, List<GSMRxQualPointInfo>> fileDic { get; set; }

        public LTERxQualCellInfo_FDD(WCell cell)
        {
            this.cell = cell;
            this.tpTotal = 1;
            this.rxQualTotal = 0;
            this.reasonDic = GSMRxQualReasonInfo.initReasonDic();
            this.fileDic = new Dictionary<string, List<GSMRxQualPointInfo>>();
        }
    }
    public class LTERxQualPointInfo_FDD : GSMRxQualPointInfo
    {
        protected override void getParam(TestPoint tp, out object rxlev, out int? c2i)
        {
            rxlev = (float?)tp["lte_fdd_wcdma_TotalRSCP"];
            c2i = null;
        }
        public LTERxQualPointInfo_FDD(int SN, string reason, TestPoint tp)
            : base(SN, reason, tp)
        {
            this.SN = SN;
            this.reason = reason;
            this.tpTime = tp.DateTimeStringWithMillisecond;

            object rxlev = null;
            int? c2i = null;
            this.getParam(tp, out rxlev, out c2i);
            if (rxlev != null)
            {
                this.rxlev = (int)rxlev;
            }

            if (c2i != null)
            {
                this.c2i = (int)c2i;
            }

            colorTp = new GSMRxQualReasonColorTestPoint(reason, tp);
        }
    }
}
