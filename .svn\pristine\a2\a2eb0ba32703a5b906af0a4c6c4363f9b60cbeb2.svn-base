﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTCsfbCallStat;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class EpsfbCallStatQuery : DIYAnalyseByFileBackgroundBase
    {
        //主叫
        protected List<int> MoCallAttemptEvtIdList = new List<int> { };
        //被叫
        protected List<int> MtCallAttemptEvtIdList = new List<int> { };

        protected static readonly object lockObj = new object();
        private static EpsfbCallStatQuery instance = null;
        public static EpsfbCallStatQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new EpsfbCallStatQuery();
                    }
                }
            }
            return instance;
        }

        protected EpsfbCallStatQuery()
           : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            this.Columns = new List<string>();
            //Columns.Add("lte_RSRP");
            //Columns.Add("lte_TAC");
            //Columns.Add("lte_ECI");
            //Columns.Add("lte_gsm_SC_LAC");
            //Columns.Add("lte_gsm_SC_CI");
            //Columns.Add("lte_td_SC_LAC");
            //Columns.Add("lte_td_SC_CellID");
            //Columns.Add("lte_gsm_DM_RxLevBCCH");
            //Columns.Add("lte_gsm_DM_RxLevSub");
            //Columns.Add("lte_td_DM_PCCPCH_RSCP");
        }

        public override string Name
        {
            get
            {
                return "EPSFB时延统计(区域文件)";
            }
        }

        protected bool checkDelay = true;
        protected int maxDelaySec = 180;

        protected override bool getCondition()
        {
            CallConditionDlg dlg = new CallConditionDlg();
            dlg.SetCondition(checkDelay, maxDelaySec);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out checkDelay, out maxDelaySec);
            callStatList = new List<SingleCallStatInfo>();
            return callStatList != null;
        }

        protected List<SingleCallStatInfo> callStatList = null;
        protected override void fireShowForm()
        {
            if (callStatList == null || callStatList.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }
            EpsfbCallStatListForm frm = MainModel.GetObjectFromBlackboard(typeof(EpsfbCallStatListForm)) as EpsfbCallStatListForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new EpsfbCallStatListForm();
            }
            frm.FillData(callStatList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            callStatList = null;
        }





    }
}
