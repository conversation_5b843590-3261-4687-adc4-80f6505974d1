﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using System.Windows.Forms;
using DevExpress.Utils.Drawing;
using DevExpress.XtraGrid;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.Injection
{
    public partial class InjectionResultForm : MinCloseForm
    {
        public InjectionResultForm()
            : base()
        {
            InitializeComponent();
        }

        float minPercentOfStreet = 0.9f;
        public bool showOnlyKeyStreets { get; set; }
        public void FillData(List<StreetInjectInfo> streetInfoList, List<RegionMileageInfo> regionInfo)
        {
            if (streetInfoList == null)
            {
                return;
            }
            List<StreetInjectInfo> showList = new List<StreetInjectInfo>();
            Dictionary<string, StreetInjectInfoTotal> tableInjectDic = new Dictionary<string, StreetInjectInfoTotal>();
            StreetInjectInfoTotal tableTotal = new StreetInjectInfoTotal();
            tableTotal.StreetTableName = "汇总";

            Dictionary<string, StreetInjectInfoTotal> regionInjectDic = new Dictionary<string, StreetInjectInfoTotal>();
            StreetInjectInfoTotal regionTotal = new StreetInjectInfoTotal();
            regionTotal.RegionName = "汇总";

            double totalNoneMustTestDistance = 0;
            foreach (RegionMileageInfo region in regionInfo)
            {
                totalNoneMustTestDistance += region.MileageInfo.UnCoveredData;
            }

            for (int st = 0; st < streetInfoList.Count; st++)
            {
                StreetInjectInfo info = streetInfoList[st];
                info.ToStandard = info.CoverPercent >= minPercentOfStreet;
                showList.Add(info);

                if (!tableInjectDic.TryGetValue(info.StreetTableName, out StreetInjectInfoTotal tableInject))
                {
                    tableInject = new StreetInjectInfoTotal();
                    tableInjectDic.Add(info.StreetTableName, tableInject);
                    tableInject.StreetTableName = info.StreetTableName;
                }
                tableInject.Add(info);
                tableTotal.Add(info);

                if (!regionInjectDic.TryGetValue(info.AreaName, out StreetInjectInfoTotal regionInject))
                {
                    regionInject = new StreetInjectInfoTotal();
                    regionInjectDic.Add(info.AreaName, regionInject);
                    regionInject.RegionName = info.AreaName;
                    RegionMileageInfo regionMileageInfo = regionInfo.Find(delegate (RegionMileageInfo x) { return x.Name.Equals(info.AreaName); });
                    if (regionMileageInfo != null)
                    {
                        regionInject.TestMileageKM = regionMileageInfo.MileageInfo.Total;
                        regionInject.CoveredMileageKM = regionMileageInfo.MileageInfo.CoveredData;
                        regionInject.NoneMustTestKM = regionMileageInfo.MileageInfo.UnCoveredData;
                        regionInject.MileageCoveredRate = regionMileageInfo.MileageInfo.CoveredRate;
                        regionInject.Area = Math.Round(regionMileageInfo.Area, 2);
                        regionTotal.Area += regionInject.Area;
                    }
                }
                regionInject.Add(info);
                regionTotal.Add(info);
            }

            foreach (string areaName in regionInjectDic.Keys)
            {
                regionTotal.NoneMustTestKM += regionInjectDic[areaName].NoneMustTestKM;
            }

            if (tableInjectDic.Count > 1)
            {
                tableInjectDic["汇总"] = tableTotal;
            }
            foreach (string tableName in tableInjectDic.Keys)
            {
                tableInjectDic[tableName].DiverseFactor = calcDiverseFactor(tableInjectDic[tableName].streetInjectList);
            }
            if (regionInjectDic.Count > 1)
            {
                regionInjectDic["汇总"] = regionTotal;
            }
            foreach (string regionName in regionInjectDic.Keys)
            {
                regionInjectDic[regionName].DiverseFactor = calcDiverseFactor(regionInjectDic[regionName].streetInjectList);
            }

            BindingSource source = new BindingSource();
            source.DataSource = showList;
            gridControlStreet.DataSource = showList;
            gridControlStreet.RefreshDataSource();
            DevGridControlManager.SetRowStyleByColumn(gridViewStreet.Columns[8], FormatConditionEnum.Equal, -1, Color.Gray);
            DevGridControlManager.SetColumnStyle(gridViewStreet.Columns[3], FormatConditionEnum.Less, minPercentOfStreet * 100, Color.Red);
            DevGridControlManager.SetColumnStyle(gridViewStreet.Columns[6], FormatConditionEnum.Equal, "不达标", Color.Red);
            gridViewStreet.Invalidate();
            source = new BindingSource();
            source.DataSource = tableInjectDic.Values;
            gridControlTable.DataSource = source;
            gridControlTable.RefreshDataSource();

            source = new BindingSource();
            source.DataSource = regionInjectDic.Values;
            gridControlRegion.DataSource = source;
            gridControlRegion.RefreshDataSource();
        }

        /// <summary>
        /// 计算变异系数
        /// </summary>
        /// <param name="showOnlyKeyStreets"></param>
        /// <returns></returns>
        private float calcDiverseFactor(List<StreetInjectInfo> steetInjList)
        {
            List<int> repeatCountList = new List<int>();
            getRepeatCount(steetInjList, repeatCountList);
            if (repeatCountList.Count < 2)
            {
                return 0;
            }
            long sumTotle = 0;
            foreach (int rptct in repeatCountList)
            {
                sumTotle += rptct;
            }
            float meanValue = ((float)sumTotle) / repeatCountList.Count;
            double pingfanghe = 0;
            foreach (int rptct in repeatCountList)
            {
                double yangbencha = rptct - meanValue;
                pingfanghe += (yangbencha * yangbencha);
            }
            double junfangcha = Math.Pow(pingfanghe / (repeatCountList.Count - 1), 0.5);
            return (float)(junfangcha / meanValue);
        }

        private void getRepeatCount(List<StreetInjectInfo> steetInjList, List<int> repeatCountList)
        {
            for (int st = 0; st < steetInjList.Count; st++)
            {
                StreetInjectInfo info = steetInjList[st];
                foreach (CovSegment cs in info.covSegmentList)
                {
                    if (info.testType != -1 && cs.Covered)
                    {
                        repeatCountList.Add((int)cs.RepeatTimes);
                    }
                }
            }
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridViewStreet);
        }

        private void gridViewRegion_CustomDrawBandHeader(object sender, DevExpress.XtraGrid.Views.BandedGrid.BandHeaderCustomDrawEventArgs e)
        {
            Rectangle r = e.Bounds;
            r.Inflate(-1, -1);
            Pen linePen = new Pen(Color.FromArgb(132, 157, 189));
            e.Graphics.DrawRectangle(linePen, r);
            Color bkClr = Color.Empty;
            if (e.Band == bandReg || e.Band == bandLayer)
            {
                bkClr = Color.Gold;
            }
            else if (e.Band == bandStreet || e.Band == bandStreetLayer)
            {
                bkClr = Color.DeepPink;
            }
            else if (e.Band == bandTestMileage || e.Band == bandMileageLayer)
            {
                bkClr = Color.DeepSkyBlue;
            }
            //A brush to fill the band's background in the normal state
            Brush brush = new LinearGradientBrush(e.Bounds, Color.Wheat, bkClr, 70);
            //A brush to fill the background when the band is pressed
            Brush brushPressed = new LinearGradientBrush(e.Bounds,
              Color.Wheat, Color.Gray, 70);
            //Fill the background
            e.Graphics.FillRectangle((e.Info.State == ObjectState.Pressed ? brushPressed : brush), r);
            r.Inflate(-2, 0);
            //Draw the band's caption with a shadowed effect
            e.Appearance.DrawString(e.Cache, e.Band.Caption, new Rectangle(r.X + 1, r.Y + 1,
              r.Width, r.Height), Brushes.White);
            e.Appearance.DrawString(e.Cache, e.Band.Caption, r, Brushes.Black);
            //Prevent default painting
            e.Handled = true;
        }

        private void miRegionExport_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridViewRegion);
        }

        private void miRoadExport_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridViewTableTotal);
        }
    }
}
