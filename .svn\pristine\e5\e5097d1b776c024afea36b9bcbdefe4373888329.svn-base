﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// LTE小区模干扰分析器
    /// </summary>
    public class LTEModInterferer
    {
        public static LTEModInterferer Instance
        {
            get { return instance; }
        }

        /// <summary>
        /// 从srcCells分析对tarCell造成干扰的小区
        /// </summary>
        /// <param name="tarCell">目标小区</param>
        /// <param name="srcCells">可能存在干扰的小区</param>
        /// <param name="cond"></param>
        /// <returns></returns>
        public List<LTEModInterfereCell> Stat(LTECell tarCell, List<LTECell> srcCells, LTEModInterfereCond cond)
        {
            List<LTECell>[] arrayList = ClassifyByModX(srcCells, cond.ModX);
            return StatInSameMod(tarCell, arrayList[tarCell.PCI % cond.ModX], cond);
        }

        /// <summary>
        /// 分析全部小区的干扰情况
        /// </summary>
        /// <param name="cells">需要分析的小区列表</param>
        /// <param name="cond"></param>
        /// <returns>Key为被干扰小区，Value为干扰源小区列表</returns>
        public Dictionary<LTECell, List<LTEModInterfereCell>> Stat(List<LTECell> cells, LTEModInterfereCond cond)
        {
            Dictionary<LTECell, List<LTEModInterfereCell>> retDic = new Dictionary<LTECell, List<LTEModInterfereCell>>();
            List<LTECell>[] arrayList = ClassifyByModX(cells, cond.ModX);

            foreach (LTECell tarCell in cells)
            {
                List<LTEModInterfereCell> interCells = StatInSameMod(tarCell, arrayList[tarCell.PCI % cond.ModX], cond);
                if (interCells.Count == 0)
                {
                    continue;
                }
                if (!retDic.ContainsKey(tarCell))
                {
                    retDic.Add(tarCell, interCells);
                }
            }

            return retDic;
        }

        /// <summary>
        /// 分析EARFCN,PCI,DISTANCE,ANGLE四个因素
        /// </summary>
        /// <param name="tarCell"></param>
        /// <param name="srcCells"></param>
        /// <param name="cond"></param>
        /// <returns></returns>
        private List<LTEModInterfereCell> StatInSameMod(LTECell tarCell, List<LTECell> srcCells, LTEModInterfereCond cond)
        {
            List<LTEModInterfereCell> retList = new List<LTEModInterfereCell>();
            if (!cond.Sids.Contains(tarCell.PCI % cond.ModX))
            {
                return retList;
            }

            foreach (LTECell cell in srcCells)
            {
                if (tarCell.ID == cell.ID)
                {
                    continue;
                }

                if (tarCell.EARFCN != cell.EARFCN)
                {
                    continue;
                }

                double distance = GetDistance(tarCell, cell);
                if (distance > cond.Distance)
                {
                    continue;
                }

                bool inRange = JudgeAngle(tarCell, cell, cond.Angle);
                if (!inRange)
                {
                    continue;
                }

                LTEModInterfereCell interCell = new LTEModInterfereCell(cell, distance);
                retList.Add(interCell);
            }
            return retList;
        }

        /// <summary>
        /// 按模值归类小区
        /// </summary>
        /// <param name="cellList"></param>
        /// <param name="modX"></param>
        /// <returns></returns>
        private List<LTECell>[] ClassifyByModX(List<LTECell> cellList, int modX)
        {
            List<LTECell>[] retArray = new List<LTECell>[modX];
            for (int i = 0; i < retArray.Length; ++i)
            {
                retArray[i] = new List<LTECell>();
            }
            foreach (LTECell cell in cellList)
            {
                int mod = cell.PCI % modX;
                retArray[mod].Add(cell);
            }
            return retArray;
        }

        private double GetDistance(LTECell tarCell, LTECell srcCell)
        {
            return MathFuncs.GetDistance(tarCell.Longitude, tarCell.Latitude, srcCell.Longitude, srcCell.Latitude);
        }

        private bool JudgeAngle(LTECell tarCell, LTECell srcCell, double angleRange)
        {
            return MathFuncs.JudgePoint(tarCell.Longitude, tarCell.Latitude, srcCell.Longitude, srcCell.Latitude, tarCell.Direction, (int)angleRange);
        }

        private LTEModInterferer()
        {
        }

        private static readonly LTEModInterferer instance = new LTEModInterferer();
    }

    /// <summary>
    /// LTE小区模干扰分析条件
    /// </summary>
    public class LTEModInterfereCond
    {
        public double Angle { get; set; } = 180;
        public double Distance { get; set; } = 2000;
        public List<int> Sids { get; set; }  // 模值
        public int ModX { get; set; } = 3;

        public LTEModInterfereCond()
        {
            Sids = new List<int>();
        }
    }

    /// <summary>
    /// 模干扰来源小区
    /// </summary>
    public class LTEModInterfereCell
    {
        public LTECell LteCell { get; set; }
        public double Distance { get; set; } // 与干扰目标的距离
        public LTEModInterfereCell()
        {
        }
        public LTEModInterfereCell(LTECell cell, double distance)
        {
            this.LteCell = cell;
            this.Distance = distance;
        }
    }
}
