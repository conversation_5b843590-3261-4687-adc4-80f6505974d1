﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.NOP
{
    public partial class GroupStatForm : MinCloseForm
    {
        public GroupStatForm()
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            DisposeWhenClose = true;
            //init();
        }

        //private void init()
        //{
        //    #region SatList
        //    this.olvColumnDistrict.AspectGetter = delegate(object row)
        //    {

        //        if (row is TaskStatResult)
        //        {
        //            TaskStatResult item = row as TaskStatResult;
        //            return item.District;
        //        }
        //        return "";
        //    };

        //    this.olvColumnT1.AspectGetter = delegate(object row)
        //    {
        //        if (row is TaskStatResult)
        //        {
        //            TaskStatResult item = row as TaskStatResult;
        //            return item.Counts[0];
        //        }
        //        return "";
        //    };

        //    this.olvColumnT2_1.AspectGetter = delegate(object row)
        //    {
        //        if (row is TaskStatResult)
        //        {
        //            TaskStatResult item = row as TaskStatResult;
        //            return item.Counts[1];
        //        }
        //        return "";
        //    };
        //    this.olvColumnT2_2.AspectGetter = delegate(object row)
        //    {
        //        if (row is TaskStatResult)
        //        {
        //            TaskStatResult item = row as TaskStatResult;
        //            return item.Counts[2];
        //        }
        //        return "";
        //    };
        //    this.olvColumnT2_3.AspectGetter = delegate(object row)
        //    {
        //        if (row is TaskStatResult)
        //        {
        //            TaskStatResult item = row as TaskStatResult;
        //            return item.Counts[3];
        //        }
        //        return "";
        //    };
        //    this.olvColumnT2_4.AspectGetter = delegate(object row)
        //    {
        //        if (row is TaskStatResult)
        //        {
        //            TaskStatResult item = row as TaskStatResult;
        //            return item.Counts[4];
        //        }
        //        return "";
        //    };

        //    this.olvColumnT2_5.AspectGetter = delegate(object row)
        //    {
        //        if (row is TaskStatResult)
        //        {
        //            TaskStatResult item = row as TaskStatResult;
        //            return item.Counts[5];
        //        }
        //        return "";
        //    };

        //    this.olvColumnT3.AspectGetter = delegate(object row)
        //    {
        //        if (row is TaskStatResult)
        //        {
        //            TaskStatResult item = row as TaskStatResult;
        //            return item.Counts[6];
        //        }
        //        return "";
        //    };

        //    this.olvColumnT0.AspectGetter = delegate(object row)
        //    {
        //        if (row is TaskStatResult)
        //        {
        //            TaskStatResult item = row as TaskStatResult;
        //            return item.Counts[7];
        //        }
        //        return "";
        //    };

        //    this.olvColumnGroupName.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.GroupName;
        //        }
        //        return "";
        //    };

        //    this.olvColumnEZhou.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[0];
        //        }
        //        return "";
        //    };
        //    this.olvColumnHuangShi.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[2];
        //        }
        //        return "";
        //    };

        //    this.olvColumnshien.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[1];
        //        }
        //        return "";
        //    };

        //    this.olvColumnHuangGang.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[3];
        //        }
        //        return "";
        //    };

        //    this.olvColumnJingMen.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[4];
        //        }
        //        return "";
        //    };
        //    this.olvColumnJingZhou.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[5];
        //        }
        //        return "";
        //    };

        //    this.olvColumnJiangHan.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[6];
        //        }
        //        return "";
        //    };

        //    this.olvColumnTianMen.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[7];
        //        }
        //        return "";
        //    };

        //    this.olvColumnQianJiang.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[8];
        //        }
        //        return "";
        //    };

        //    this.olvColumnShiYan.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[9];
        //        }
        //        return "";
        //    };

        //    this.olvColumnSuiZhou.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[10];
        //        }
        //        return "";
        //    };

        //    this.olvColumnWuHan.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[11];
        //        }
        //        return "";
        //    };

        //    this.olvColumnXiangYang.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[12];
        //        }
        //        return "";
        //    };

        //    this.olvColumnXianNing.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[13];
        //        }
        //        return "";
        //    };

        //    this.olvColumnXiaoGan.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[14];
        //        }
        //        return "";
        //    };

        //    this.olvColumnYiChang.AspectGetter = delegate(object row)
        //    {
        //        if (row is GroupStatResult)
        //        {
        //            GroupStatResult item = row as GroupStatResult;
        //            return item.Counts[15];
        //        }
        //        return "";
        //    };
        //    #endregion
        //}

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (resultFiles == null && results == null)
            {
                return;
            }



            #region
            List<NPOIRow> summBroTables = new List<NPOIRow>();
            NPOIRow summBroTitleRow = new NPOIRow();
            summBroTitleRow.AddCellValue("工单名称");
            summBroTitleRow.AddCellValue("地市");
            summBroTitleRow.AddCellValue("T1");
            summBroTitleRow.AddCellValue("T21");
            summBroTitleRow.AddCellValue("T22");
            summBroTitleRow.AddCellValue("T23");
            summBroTitleRow.AddCellValue("T24");
            summBroTitleRow.AddCellValue("T25");
            summBroTitleRow.AddCellValue("T3");
            summBroTitleRow.AddCellValue("T0");
            summBroTables.Add(summBroTitleRow);
            if (resultFiles != null)
            {
                foreach (TaskStatResult res in resultFiles)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(res.TaskName);
                    row.AddCellValue(res.District);
                    row.AddCellValue(res.T1);
                    row.AddCellValue(res.T21);
                    row.AddCellValue(res.T22);
                    row.AddCellValue(res.T23);
                    row.AddCellValue(res.T24);
                    row.AddCellValue(res.T25);
                    row.AddCellValue(res.T3);
                    row.AddCellValue(res.T0);
                    summBroTables.Add(row);
                }
            }


            List<NPOIRow> broTables = new List<NPOIRow>();
            NPOIRow broTitleRow = new NPOIRow();
            broTitleRow.AddCellValue("分类");
            broTitleRow.AddCellValue("鄂州");
            broTitleRow.AddCellValue("恩施");
            broTitleRow.AddCellValue("黄石");
            broTitleRow.AddCellValue("黄冈");
            broTitleRow.AddCellValue("荆门");
            broTitleRow.AddCellValue("荆州");
            broTitleRow.AddCellValue("江汉");
            broTitleRow.AddCellValue("天门");
            broTitleRow.AddCellValue("潜江");
            broTitleRow.AddCellValue("十堰");
            broTitleRow.AddCellValue("随州");
            broTitleRow.AddCellValue("武汉");
            broTitleRow.AddCellValue("襄阳");
            broTitleRow.AddCellValue("咸宁");
            broTitleRow.AddCellValue("孝感");
            broTitleRow.AddCellValue("宜昌");

            broTables.Add(broTitleRow);
            if (results != null)
            {
                foreach (GroupStatResult dtlModel in results)
                {
                    NPOIRow fileValues = new NPOIRow();
                    fileValues.AddCellValue(dtlModel.GroupName);
                    fileValues.AddCellValue(dtlModel.EZhou);
                    fileValues.AddCellValue(dtlModel.EnShi);
                    fileValues.AddCellValue(dtlModel.HuangShi);
                    fileValues.AddCellValue(dtlModel.HuangGang);
                    fileValues.AddCellValue(dtlModel.JingMen);
                    fileValues.AddCellValue(dtlModel.JingZhou);
                    fileValues.AddCellValue(dtlModel.JiangHan);
                    fileValues.AddCellValue(dtlModel.TianMen);
                    fileValues.AddCellValue(dtlModel.QianJiang);
                    fileValues.AddCellValue(dtlModel.ShiYan);
                    fileValues.AddCellValue(dtlModel.SuiZhou);
                    fileValues.AddCellValue(dtlModel.WuHan);
                    fileValues.AddCellValue(dtlModel.XiangYang);
                    fileValues.AddCellValue(dtlModel.XianNing);
                    fileValues.AddCellValue(dtlModel.XiaoGan);
                    fileValues.AddCellValue(dtlModel.YiChang);

                    broTables.Add(fileValues);
                }
            }


            #endregion


            ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() { broTables, summBroTables },
                new List<string>() { "类别-地市", "工单-类别" });
        }

        
        List<TaskStatResult> resultFiles = null;
        List<GroupStatResult> results = null;
        public void FillData(object result, object taskResult)
        {
            resultFiles = taskResult as List<TaskStatResult>;
            results = result as List<GroupStatResult>;
            gcGroup.DataSource = results;
            gcGroup.RefreshDataSource();
            gcTask.DataSource = resultFiles;
            gcTask.RefreshDataSource();
            

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }
    }
}
