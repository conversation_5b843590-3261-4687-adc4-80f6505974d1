﻿namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment
{
    partial class ConditionDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.grpFar = new System.Windows.Forms.GroupBox();
            this.numDirRange = new System.Windows.Forms.NumericUpDown();
            this.numAvgDistance = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.grpHigh = new System.Windows.Forms.GroupBox();
            this.numAltitude = new System.Windows.Forms.NumericUpDown();
            this.label18 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.grpNear = new System.Windows.Forms.GroupBox();
            this.numDiffBandDis = new System.Windows.Forms.NumericUpDown();
            this.label43 = new System.Windows.Forms.Label();
            this.label44 = new System.Windows.Forms.Label();
            this.label45 = new System.Windows.Forms.Label();
            this.numSiteDistance = new System.Windows.Forms.NumericUpDown();
            this.label20 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.label23 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.numWeakCoverDis = new System.Windows.Forms.NumericUpDown();
            this.numWeakCoverRSRP = new System.Windows.Forms.NumericUpDown();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label38 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label37 = new System.Windows.Forms.Label();
            this.numMod3RSRP = new System.Windows.Forms.NumericUpDown();
            this.numMod3Distance = new System.Windows.Forms.NumericUpDown();
            this.numMod3SINR = new System.Windows.Forms.NumericUpDown();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.numMultiDistance = new System.Windows.Forms.NumericUpDown();
            this.label29 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label28 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numMultiGrade = new System.Windows.Forms.NumericUpDown();
            this.numMultiRSRPDiff = new System.Windows.Forms.NumericUpDown();
            this.numMultiCvrRSRP = new System.Windows.Forms.NumericUpDown();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label13 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.numSiteCnt = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.label25 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.numCoverLapPer = new System.Windows.Forms.NumericUpDown();
            this.numCvrDisFactor = new System.Windows.Forms.NumericUpDown();
            this.numOvrLapRSRP = new System.Windows.Forms.NumericUpDown();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.label31 = new System.Windows.Forms.Label();
            this.label36 = new System.Windows.Forms.Label();
            this.label35 = new System.Windows.Forms.Label();
            this.label32 = new System.Windows.Forms.Label();
            this.label33 = new System.Windows.Forms.Label();
            this.numHoDistance = new System.Windows.Forms.NumericUpDown();
            this.numHoCnt = new System.Windows.Forms.NumericUpDown();
            this.numHoSecond = new System.Windows.Forms.NumericUpDown();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.radioByCell = new System.Windows.Forms.RadioButton();
            this.radioByFile = new System.Windows.Forms.RadioButton();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.grpFileCheck = new System.Windows.Forms.GroupBox();
            this.btnBrs = new System.Windows.Forms.Button();
            this.linkFileDesc = new System.Windows.Forms.LinkLabel();
            this.tbxGroupResult = new System.Windows.Forms.TextBox();
            this.label34 = new System.Windows.Forms.Label();
            this.gvFileDesc = new System.Windows.Forms.DataGridView();
            this.colCell = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colUltraType = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label10 = new System.Windows.Forms.Label();
            this.label39 = new System.Windows.Forms.Label();
            this.label40 = new System.Windows.Forms.Label();
            this.label41 = new System.Windows.Forms.Label();
            this.label42 = new System.Windows.Forms.Label();
            this.numFarRSRP = new System.Windows.Forms.NumericUpDown();
            this.numFarDistance = new System.Windows.Forms.NumericUpDown();
            this.numFarTpNum = new System.Windows.Forms.NumericUpDown();
            this.SameARFCN = new System.Windows.Forms.CheckBox();
            this.grpFar.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDirRange)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAvgDistance)).BeginInit();
            this.grpHigh.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAltitude)).BeginInit();
            this.grpNear.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffBandDis)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteDistance)).BeginInit();
            this.groupBox7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverDis)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRSRP)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMod3RSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMod3Distance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMod3SINR)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiGrade)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiRSRPDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCvrRSRP)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteCnt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCoverLapPer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCvrDisFactor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOvrLapRSRP)).BeginInit();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numHoDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHoCnt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHoSecond)).BeginInit();
            this.groupBox9.SuspendLayout();
            this.groupBox11.SuspendLayout();
            this.grpFileCheck.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvFileDesc)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFarRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFarDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFarTpNum)).BeginInit();
            this.SuspendLayout();
            // 
            // grpFar
            // 
            this.grpFar.Controls.Add(this.numDirRange);
            this.grpFar.Controls.Add(this.numAvgDistance);
            this.grpFar.Controls.Add(this.label6);
            this.grpFar.Controls.Add(this.label15);
            this.grpFar.Controls.Add(this.label16);
            this.grpFar.Controls.Add(this.label17);
            this.grpFar.Location = new System.Drawing.Point(534, 16);
            this.grpFar.Name = "grpFar";
            this.grpFar.Size = new System.Drawing.Size(253, 78);
            this.grpFar.TabIndex = 2;
            this.grpFar.TabStop = false;
            this.grpFar.Text = "超远站";
            // 
            // numDirRange
            // 
            this.numDirRange.Location = new System.Drawing.Point(112, 20);
            this.numDirRange.Maximum = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.numDirRange.Name = "numDirRange";
            this.numDirRange.Size = new System.Drawing.Size(63, 21);
            this.numDirRange.TabIndex = 0;
            this.numDirRange.Value = new decimal(new int[] {
            120,
            0,
            0,
            0});
            // 
            // numAvgDistance
            // 
            this.numAvgDistance.Location = new System.Drawing.Point(112, 49);
            this.numAvgDistance.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numAvgDistance.Name = "numAvgDistance";
            this.numAvgDistance.Size = new System.Drawing.Size(63, 21);
            this.numAvgDistance.TabIndex = 1;
            this.numAvgDistance.Value = new decimal(new int[] {
            700,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(181, 25);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(17, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "°";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(181, 55);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(17, 12);
            this.label15.TabIndex = 0;
            this.label15.Text = "米";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(41, 25);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(65, 12);
            this.label16.TabIndex = 0;
            this.label16.Text = "搜索角度：";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(29, 55);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(77, 12);
            this.label17.TabIndex = 0;
            this.label17.Text = "平均站间距＞";
            // 
            // grpHigh
            // 
            this.grpHigh.Controls.Add(this.numAltitude);
            this.grpHigh.Controls.Add(this.label18);
            this.grpHigh.Controls.Add(this.label19);
            this.grpHigh.Location = new System.Drawing.Point(277, 16);
            this.grpHigh.Name = "grpHigh";
            this.grpHigh.Size = new System.Drawing.Size(253, 78);
            this.grpHigh.TabIndex = 1;
            this.grpHigh.TabStop = false;
            this.grpHigh.Text = "超高站";
            // 
            // numAltitude
            // 
            this.numAltitude.Location = new System.Drawing.Point(107, 35);
            this.numAltitude.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numAltitude.Name = "numAltitude";
            this.numAltitude.Size = new System.Drawing.Size(75, 21);
            this.numAltitude.TabIndex = 0;
            this.numAltitude.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(188, 41);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(17, 12);
            this.label18.TabIndex = 0;
            this.label18.Text = "米";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(34, 41);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(65, 12);
            this.label19.TabIndex = 0;
            this.label19.Text = "天线挂高＞";
            // 
            // grpNear
            // 
            this.grpNear.Controls.Add(this.numDiffBandDis);
            this.grpNear.Controls.Add(this.label43);
            this.grpNear.Controls.Add(this.label44);
            this.grpNear.Controls.Add(this.label45);
            this.grpNear.Controls.Add(this.numSiteDistance);
            this.grpNear.Controls.Add(this.label20);
            this.grpNear.Controls.Add(this.label21);
            this.grpNear.Location = new System.Drawing.Point(20, 16);
            this.grpNear.Name = "grpNear";
            this.grpNear.Size = new System.Drawing.Size(253, 78);
            this.grpNear.TabIndex = 0;
            this.grpNear.TabStop = false;
            this.grpNear.Text = "超近站";
            // 
            // numDiffBandDis
            // 
            this.numDiffBandDis.Location = new System.Drawing.Point(139, 49);
            this.numDiffBandDis.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numDiffBandDis.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            -2147483648});
            this.numDiffBandDis.Name = "numDiffBandDis";
            this.numDiffBandDis.Size = new System.Drawing.Size(75, 21);
            this.numDiffBandDis.TabIndex = 4;
            this.numDiffBandDis.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDiffBandDis.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label43
            // 
            this.label43.AutoSize = true;
            this.label43.Location = new System.Drawing.Point(225, 55);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(17, 12);
            this.label43.TabIndex = 5;
            this.label43.Text = "米";
            // 
            // label44
            // 
            this.label44.AutoSize = true;
            this.label44.Location = new System.Drawing.Point(80, 63);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(53, 12);
            this.label44.TabIndex = 6;
            this.label44.Text = "相距需＞";
            // 
            // label45
            // 
            this.label45.AutoSize = true;
            this.label45.Location = new System.Drawing.Point(9, 44);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(125, 12);
            this.label45.TabIndex = 7;
            this.label45.Text = "若两基站异频(共站)，";
            // 
            // numSiteDistance
            // 
            this.numSiteDistance.Location = new System.Drawing.Point(139, 20);
            this.numSiteDistance.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numSiteDistance.Name = "numSiteDistance";
            this.numSiteDistance.Size = new System.Drawing.Size(74, 21);
            this.numSiteDistance.TabIndex = 0;
            this.numSiteDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSiteDistance.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(224, 25);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(17, 12);
            this.label20.TabIndex = 0;
            this.label20.Text = "米";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(53, 25);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(77, 12);
            this.label21.TabIndex = 0;
            this.label21.Text = "两基站相距＜";
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.label23);
            this.groupBox7.Controls.Add(this.label24);
            this.groupBox7.Controls.Add(this.label22);
            this.groupBox7.Controls.Add(this.label26);
            this.groupBox7.Controls.Add(this.numWeakCoverDis);
            this.groupBox7.Controls.Add(this.numWeakCoverRSRP);
            this.groupBox7.Location = new System.Drawing.Point(11, 205);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(901, 58);
            this.groupBox7.TabIndex = 1;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "弱覆盖";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(322, 25);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(65, 12);
            this.label23.TabIndex = 21;
            this.label23.Text = "持续距离≥";
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(474, 25);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(17, 12);
            this.label24.TabIndex = 0;
            this.label24.Text = "米";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(82, 25);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(65, 12);
            this.label22.TabIndex = 21;
            this.label22.Text = "主服场强≤";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(233, 25);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(23, 12);
            this.label26.TabIndex = 17;
            this.label26.Text = "dBm";
            // 
            // numWeakCoverDis
            // 
            this.numWeakCoverDis.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numWeakCoverDis.Location = new System.Drawing.Point(393, 20);
            this.numWeakCoverDis.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.numWeakCoverDis.Name = "numWeakCoverDis";
            this.numWeakCoverDis.Size = new System.Drawing.Size(75, 21);
            this.numWeakCoverDis.TabIndex = 1;
            this.numWeakCoverDis.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakCoverDis.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // numWeakCoverRSRP
            // 
            this.numWeakCoverRSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numWeakCoverRSRP.Location = new System.Drawing.Point(152, 21);
            this.numWeakCoverRSRP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numWeakCoverRSRP.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numWeakCoverRSRP.Name = "numWeakCoverRSRP";
            this.numWeakCoverRSRP.Size = new System.Drawing.Size(75, 21);
            this.numWeakCoverRSRP.TabIndex = 0;
            this.numWeakCoverRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakCoverRSRP.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(837, 639);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(750, 639);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 6;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label38);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.label11);
            this.groupBox3.Controls.Add(this.label37);
            this.groupBox3.Controls.Add(this.numMod3RSRP);
            this.groupBox3.Controls.Add(this.numMod3Distance);
            this.groupBox3.Controls.Add(this.numMod3SINR);
            this.groupBox3.Location = new System.Drawing.Point(10, 565);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(902, 58);
            this.groupBox3.TabIndex = 6;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "持续质差";
            // 
            // label38
            // 
            this.label38.AutoSize = true;
            this.label38.Location = new System.Drawing.Point(578, 27);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(65, 12);
            this.label38.TabIndex = 21;
            this.label38.Text = "持续距离≥";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(80, 26);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(65, 12);
            this.label9.TabIndex = 21;
            this.label9.Text = "主服场强＞";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(730, 26);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "米";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(234, 27);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(23, 12);
            this.label11.TabIndex = 17;
            this.label11.Text = "dBm";
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(348, 27);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(41, 12);
            this.label37.TabIndex = 21;
            this.label37.Text = "SINR≤";
            // 
            // numMod3RSRP
            // 
            this.numMod3RSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMod3RSRP.Location = new System.Drawing.Point(153, 20);
            this.numMod3RSRP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMod3RSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMod3RSRP.Name = "numMod3RSRP";
            this.numMod3RSRP.Size = new System.Drawing.Size(75, 21);
            this.numMod3RSRP.TabIndex = 0;
            this.numMod3RSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMod3RSRP.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // numMod3Distance
            // 
            this.numMod3Distance.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMod3Distance.Location = new System.Drawing.Point(649, 22);
            this.numMod3Distance.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.numMod3Distance.Name = "numMod3Distance";
            this.numMod3Distance.Size = new System.Drawing.Size(75, 21);
            this.numMod3Distance.TabIndex = 2;
            this.numMod3Distance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMod3Distance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // numMod3SINR
            // 
            this.numMod3SINR.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMod3SINR.Location = new System.Drawing.Point(394, 20);
            this.numMod3SINR.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMod3SINR.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numMod3SINR.Name = "numMod3SINR";
            this.numMod3SINR.Size = new System.Drawing.Size(75, 21);
            this.numMod3SINR.TabIndex = 1;
            this.numMod3SINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.SameARFCN);
            this.groupBox2.Controls.Add(this.numMultiDistance);
            this.groupBox2.Controls.Add(this.label29);
            this.groupBox2.Controls.Add(this.label30);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.label28);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.numMultiGrade);
            this.groupBox2.Controls.Add(this.numMultiRSRPDiff);
            this.groupBox2.Controls.Add(this.numMultiCvrRSRP);
            this.groupBox2.Location = new System.Drawing.Point(10, 417);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(902, 78);
            this.groupBox2.TabIndex = 4;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "重叠覆盖";
            // 
            // numMultiDistance
            // 
            this.numMultiDistance.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMultiDistance.Location = new System.Drawing.Point(394, 47);
            this.numMultiDistance.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.numMultiDistance.Name = "numMultiDistance";
            this.numMultiDistance.Size = new System.Drawing.Size(75, 21);
            this.numMultiDistance.TabIndex = 0;
            this.numMultiDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMultiDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(323, 51);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(65, 12);
            this.label29.TabIndex = 21;
            this.label29.Text = "持续距离≥";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(475, 51);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(17, 12);
            this.label30.TabIndex = 0;
            this.label30.Text = "米";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(82, 24);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 21;
            this.label3.Text = "最强信号≥";
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(311, 25);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(77, 12);
            this.label28.TabIndex = 21;
            this.label28.Text = "重叠覆盖度≥";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(41, 51);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(107, 12);
            this.label5.TabIndex = 21;
            this.label5.Text = "与最强信号差异 ≤";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(233, 51);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 12);
            this.label14.TabIndex = 17;
            this.label14.Text = "dB";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(233, 24);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(23, 12);
            this.label8.TabIndex = 17;
            this.label8.Text = "dBm";
            // 
            // numMultiGrade
            // 
            this.numMultiGrade.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMultiGrade.Location = new System.Drawing.Point(394, 20);
            this.numMultiGrade.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numMultiGrade.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMultiGrade.Name = "numMultiGrade";
            this.numMultiGrade.Size = new System.Drawing.Size(75, 21);
            this.numMultiGrade.TabIndex = 1;
            this.numMultiGrade.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMultiGrade.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // numMultiRSRPDiff
            // 
            this.numMultiRSRPDiff.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMultiRSRPDiff.Location = new System.Drawing.Point(152, 47);
            this.numMultiRSRPDiff.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numMultiRSRPDiff.Name = "numMultiRSRPDiff";
            this.numMultiRSRPDiff.Size = new System.Drawing.Size(75, 21);
            this.numMultiRSRPDiff.TabIndex = 1;
            this.numMultiRSRPDiff.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMultiRSRPDiff.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // numMultiCvrRSRP
            // 
            this.numMultiCvrRSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMultiCvrRSRP.Location = new System.Drawing.Point(152, 20);
            this.numMultiCvrRSRP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMultiCvrRSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMultiCvrRSRP.Name = "numMultiCvrRSRP";
            this.numMultiCvrRSRP.Size = new System.Drawing.Size(75, 21);
            this.numMultiCvrRSRP.TabIndex = 0;
            this.numMultiCvrRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMultiCvrRSRP.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label13);
            this.groupBox1.Controls.Add(this.label12);
            this.groupBox1.Controls.Add(this.numSiteCnt);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label27);
            this.groupBox1.Controls.Add(this.label25);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numCoverLapPer);
            this.groupBox1.Controls.Add(this.numCvrDisFactor);
            this.groupBox1.Controls.Add(this.numOvrLapRSRP);
            this.groupBox1.Location = new System.Drawing.Point(11, 269);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(901, 78);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "过覆盖";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(81, 22);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(65, 12);
            this.label13.TabIndex = 21;
            this.label13.Text = "主服场强＞";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(33, 50);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(113, 12);
            this.label12.TabIndex = 20;
            this.label12.Text = "理想覆盖参考基站数";
            // 
            // numSiteCnt
            // 
            this.numSiteCnt.Location = new System.Drawing.Point(151, 46);
            this.numSiteCnt.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numSiteCnt.Minimum = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numSiteCnt.Name = "numSiteCnt";
            this.numSiteCnt.Size = new System.Drawing.Size(75, 21);
            this.numSiteCnt.TabIndex = 1;
            this.numSiteCnt.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSiteCnt.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(474, 51);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 18;
            this.label2.Text = "倍";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(474, 23);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(11, 12);
            this.label27.TabIndex = 18;
            this.label27.Text = "%";
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(310, 22);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(77, 12);
            this.label25.TabIndex = 18;
            this.label25.Text = "异常点比例≥";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(259, 50);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(125, 12);
            this.label4.TabIndex = 18;
            this.label4.Text = "超过理想覆盖半径系数";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(232, 22);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(23, 12);
            this.label1.TabIndex = 17;
            this.label1.Text = "dBm";
            // 
            // numCoverLapPer
            // 
            this.numCoverLapPer.DecimalPlaces = 1;
            this.numCoverLapPer.Increment = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.numCoverLapPer.Location = new System.Drawing.Point(393, 18);
            this.numCoverLapPer.Name = "numCoverLapPer";
            this.numCoverLapPer.Size = new System.Drawing.Size(75, 21);
            this.numCoverLapPer.TabIndex = 2;
            this.numCoverLapPer.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numCoverLapPer.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // numCvrDisFactor
            // 
            this.numCvrDisFactor.DecimalPlaces = 1;
            this.numCvrDisFactor.Increment = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.numCvrDisFactor.Location = new System.Drawing.Point(393, 46);
            this.numCvrDisFactor.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numCvrDisFactor.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numCvrDisFactor.Name = "numCvrDisFactor";
            this.numCvrDisFactor.Size = new System.Drawing.Size(75, 21);
            this.numCvrDisFactor.TabIndex = 3;
            this.numCvrDisFactor.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numCvrDisFactor.Value = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            // 
            // numOvrLapRSRP
            // 
            this.numOvrLapRSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numOvrLapRSRP.Location = new System.Drawing.Point(151, 18);
            this.numOvrLapRSRP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numOvrLapRSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numOvrLapRSRP.Name = "numOvrLapRSRP";
            this.numOvrLapRSRP.Size = new System.Drawing.Size(75, 21);
            this.numOvrLapRSRP.TabIndex = 0;
            this.numOvrLapRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOvrLapRSRP.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // groupBox8
            // 
            this.groupBox8.Controls.Add(this.label31);
            this.groupBox8.Controls.Add(this.label36);
            this.groupBox8.Controls.Add(this.label35);
            this.groupBox8.Controls.Add(this.label32);
            this.groupBox8.Controls.Add(this.label33);
            this.groupBox8.Controls.Add(this.numHoDistance);
            this.groupBox8.Controls.Add(this.numHoCnt);
            this.groupBox8.Controls.Add(this.numHoSecond);
            this.groupBox8.Location = new System.Drawing.Point(10, 501);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(902, 58);
            this.groupBox8.TabIndex = 5;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "频繁切换";
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(83, 25);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(65, 12);
            this.label31.TabIndex = 21;
            this.label31.Text = "切换次数≥";
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(730, 25);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(17, 12);
            this.label36.TabIndex = 0;
            this.label36.Text = "米";
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(602, 25);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(41, 12);
            this.label35.TabIndex = 21;
            this.label35.Text = "距离≤";
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(347, 25);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(41, 12);
            this.label32.TabIndex = 21;
            this.label32.Text = "时间≤";
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(475, 25);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(17, 12);
            this.label33.TabIndex = 17;
            this.label33.Text = "秒";
            // 
            // numHoDistance
            // 
            this.numHoDistance.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numHoDistance.Location = new System.Drawing.Point(649, 20);
            this.numHoDistance.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.numHoDistance.Name = "numHoDistance";
            this.numHoDistance.Size = new System.Drawing.Size(75, 21);
            this.numHoDistance.TabIndex = 2;
            this.numHoDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numHoDistance.Value = new decimal(new int[] {
            150,
            0,
            0,
            0});
            // 
            // numHoCnt
            // 
            this.numHoCnt.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numHoCnt.Location = new System.Drawing.Point(152, 20);
            this.numHoCnt.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numHoCnt.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numHoCnt.Name = "numHoCnt";
            this.numHoCnt.Size = new System.Drawing.Size(75, 21);
            this.numHoCnt.TabIndex = 0;
            this.numHoCnt.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numHoCnt.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // numHoSecond
            // 
            this.numHoSecond.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numHoSecond.Location = new System.Drawing.Point(394, 20);
            this.numHoSecond.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numHoSecond.Name = "numHoSecond";
            this.numHoSecond.Size = new System.Drawing.Size(75, 21);
            this.numHoSecond.TabIndex = 1;
            this.numHoSecond.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numHoSecond.Value = new decimal(new int[] {
            15,
            0,
            0,
            0});
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.radioByCell);
            this.groupBox9.Controls.Add(this.radioByFile);
            this.groupBox9.Controls.Add(this.groupBox11);
            this.groupBox9.Controls.Add(this.grpFileCheck);
            this.groupBox9.Location = new System.Drawing.Point(11, 12);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(901, 187);
            this.groupBox9.TabIndex = 0;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "三超站点";
            // 
            // radioByCell
            // 
            this.radioByCell.AutoSize = true;
            this.radioByCell.Checked = true;
            this.radioByCell.Location = new System.Drawing.Point(6, 132);
            this.radioByCell.Name = "radioByCell";
            this.radioByCell.Size = new System.Drawing.Size(71, 16);
            this.radioByCell.TabIndex = 0;
            this.radioByCell.TabStop = true;
            this.radioByCell.Text = "即时统计";
            this.radioByCell.UseVisualStyleBackColor = true;
            // 
            // radioByFile
            // 
            this.radioByFile.AutoSize = true;
            this.radioByFile.Location = new System.Drawing.Point(6, 40);
            this.radioByFile.Name = "radioByFile";
            this.radioByFile.Size = new System.Drawing.Size(95, 16);
            this.radioByFile.TabIndex = 0;
            this.radioByFile.Text = "用已统计结果";
            this.toolTip.SetToolTip(this.radioByFile, "用已统计好的三超小区Excel表格。\r\n表格格式为，有2列，第一列内容为小区名称\r\n，第二列为小区所属的三超问题。");
            this.radioByFile.UseVisualStyleBackColor = true;
            this.radioByFile.CheckedChanged += new System.EventHandler(this.radioByFile_CheckedChanged);
            // 
            // groupBox11
            // 
            this.groupBox11.Controls.Add(this.grpNear);
            this.groupBox11.Controls.Add(this.grpFar);
            this.groupBox11.Controls.Add(this.grpHigh);
            this.groupBox11.Location = new System.Drawing.Point(102, 81);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(793, 100);
            this.groupBox11.TabIndex = 11;
            this.groupBox11.TabStop = false;
            this.groupBox11.Text = "即时统计";
            // 
            // grpFileCheck
            // 
            this.grpFileCheck.Controls.Add(this.btnBrs);
            this.grpFileCheck.Controls.Add(this.linkFileDesc);
            this.grpFileCheck.Controls.Add(this.tbxGroupResult);
            this.grpFileCheck.Controls.Add(this.label34);
            this.grpFileCheck.Enabled = false;
            this.grpFileCheck.Location = new System.Drawing.Point(102, 20);
            this.grpFileCheck.Name = "grpFileCheck";
            this.grpFileCheck.Size = new System.Drawing.Size(704, 55);
            this.grpFileCheck.TabIndex = 11;
            this.grpFileCheck.TabStop = false;
            this.grpFileCheck.Text = "集团统计结果";
            this.toolTip.SetToolTip(this.grpFileCheck, "用已统计好的三超小区Excel表格。");
            // 
            // btnBrs
            // 
            this.btnBrs.Location = new System.Drawing.Point(603, 20);
            this.btnBrs.Name = "btnBrs";
            this.btnBrs.Size = new System.Drawing.Size(75, 23);
            this.btnBrs.TabIndex = 1;
            this.btnBrs.Text = "浏览...";
            this.toolTip.SetToolTip(this.btnBrs, "用已统计好的三超小区Excel表格。");
            this.btnBrs.UseVisualStyleBackColor = true;
            this.btnBrs.Click += new System.EventHandler(this.btnBrs_Click);
            // 
            // linkFileDesc
            // 
            this.linkFileDesc.AutoSize = true;
            this.linkFileDesc.Location = new System.Drawing.Point(82, 0);
            this.linkFileDesc.Name = "linkFileDesc";
            this.linkFileDesc.Size = new System.Drawing.Size(65, 12);
            this.linkFileDesc.TabIndex = 12;
            this.linkFileDesc.TabStop = true;
            this.linkFileDesc.Text = "(格式示例)";
            this.linkFileDesc.MouseClick += new System.Windows.Forms.MouseEventHandler(this.linkFileDesc_MouseClick);
            // 
            // tbxGroupResult
            // 
            this.tbxGroupResult.Location = new System.Drawing.Point(121, 22);
            this.tbxGroupResult.Name = "tbxGroupResult";
            this.tbxGroupResult.Size = new System.Drawing.Size(465, 21);
            this.tbxGroupResult.TabIndex = 0;
            this.toolTip.SetToolTip(this.tbxGroupResult, "用已统计好的三超小区Excel表格。");
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(52, 25);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(65, 12);
            this.label34.TabIndex = 0;
            this.label34.Text = "文件路径：";
            // 
            // gvFileDesc
            // 
            this.gvFileDesc.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.gvFileDesc.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colCell,
            this.colUltraType});
            this.gvFileDesc.Location = new System.Drawing.Point(2, 2);
            this.gvFileDesc.Name = "gvFileDesc";
            this.gvFileDesc.ReadOnly = true;
            this.gvFileDesc.RowTemplate.Height = 23;
            this.gvFileDesc.RowTemplate.ReadOnly = true;
            this.gvFileDesc.RowTemplate.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.gvFileDesc.Size = new System.Drawing.Size(244, 150);
            this.gvFileDesc.TabIndex = 12;
            this.gvFileDesc.MouseLeave += new System.EventHandler(this.gvFileDesc_MouseLeave);
            // 
            // colCell
            // 
            this.colCell.HeaderText = "小区名称";
            this.colCell.Name = "colCell";
            this.colCell.ReadOnly = true;
            // 
            // colUltraType
            // 
            this.colUltraType.HeaderText = "所属问题";
            this.colUltraType.Name = "colUltraType";
            this.colUltraType.ReadOnly = true;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.label10);
            this.groupBox4.Controls.Add(this.label39);
            this.groupBox4.Controls.Add(this.label40);
            this.groupBox4.Controls.Add(this.label41);
            this.groupBox4.Controls.Add(this.label42);
            this.groupBox4.Controls.Add(this.numFarRSRP);
            this.groupBox4.Controls.Add(this.numFarDistance);
            this.groupBox4.Controls.Add(this.numFarTpNum);
            this.groupBox4.Location = new System.Drawing.Point(11, 353);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(901, 58);
            this.groupBox4.TabIndex = 3;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "超远覆盖";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(322, 24);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(65, 12);
            this.label10.TabIndex = 21;
            this.label10.Text = "覆盖距离≥";
            // 
            // label39
            // 
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(79, 26);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(65, 12);
            this.label39.TabIndex = 21;
            this.label39.Text = "主服场强＞";
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(474, 24);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(17, 12);
            this.label40.TabIndex = 0;
            this.label40.Text = "米";
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(229, 27);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(23, 12);
            this.label41.TabIndex = 17;
            this.label41.Text = "dBm";
            // 
            // label42
            // 
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(565, 27);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(77, 12);
            this.label42.TabIndex = 21;
            this.label42.Text = "采样点个数＞";
            // 
            // numFarRSRP
            // 
            this.numFarRSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numFarRSRP.Location = new System.Drawing.Point(151, 20);
            this.numFarRSRP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numFarRSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numFarRSRP.Name = "numFarRSRP";
            this.numFarRSRP.Size = new System.Drawing.Size(75, 21);
            this.numFarRSRP.TabIndex = 0;
            this.numFarRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numFarRSRP.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // numFarDistance
            // 
            this.numFarDistance.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numFarDistance.Location = new System.Drawing.Point(393, 20);
            this.numFarDistance.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.numFarDistance.Name = "numFarDistance";
            this.numFarDistance.Size = new System.Drawing.Size(75, 21);
            this.numFarDistance.TabIndex = 2;
            this.numFarDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numFarDistance.Value = new decimal(new int[] {
            1500,
            0,
            0,
            0});
            // 
            // numFarTpNum
            // 
            this.numFarTpNum.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numFarTpNum.Location = new System.Drawing.Point(648, 20);
            this.numFarTpNum.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numFarTpNum.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numFarTpNum.Name = "numFarTpNum";
            this.numFarTpNum.Size = new System.Drawing.Size(75, 21);
            this.numFarTpNum.TabIndex = 1;
            this.numFarTpNum.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numFarTpNum.Value = new decimal(new int[] {
            15,
            0,
            0,
            0});
            // 
            // SameARFCN
            // 
            this.SameARFCN.AutoSize = true;
            this.SameARFCN.Location = new System.Drawing.Point(568, 23);
            this.SameARFCN.Name = "SameARFCN";
            this.SameARFCN.Size = new System.Drawing.Size(228, 16);
            this.SameARFCN.TabIndex = 22;
            this.SameARFCN.Text = "同频(覆盖度内小区需与最强小区同频)";
            this.SameARFCN.UseVisualStyleBackColor = true;
            // 
            // ConditionDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(924, 674);
            this.Controls.Add(this.groupBox9);
            this.Controls.Add(this.groupBox7);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBox8);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Name = "ConditionDlg";
            this.Text = "设置";
            this.grpFar.ResumeLayout(false);
            this.grpFar.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDirRange)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAvgDistance)).EndInit();
            this.grpHigh.ResumeLayout(false);
            this.grpHigh.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAltitude)).EndInit();
            this.grpNear.ResumeLayout(false);
            this.grpNear.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffBandDis)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteDistance)).EndInit();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverDis)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRSRP)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMod3RSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMod3Distance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMod3SINR)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiGrade)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiRSRPDiff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCvrRSRP)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteCnt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCoverLapPer)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCvrDisFactor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOvrLapRSRP)).EndInit();
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numHoDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHoCnt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHoSecond)).EndInit();
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            this.groupBox11.ResumeLayout(false);
            this.grpFileCheck.ResumeLayout(false);
            this.grpFileCheck.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvFileDesc)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFarRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFarDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFarTpNum)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox grpFar;
        private System.Windows.Forms.NumericUpDown numDirRange;
        private System.Windows.Forms.NumericUpDown numAvgDistance;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.GroupBox grpHigh;
        private System.Windows.Forms.NumericUpDown numAltitude;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.GroupBox grpNear;
        private System.Windows.Forms.NumericUpDown numSiteDistance;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.NumericUpDown numWeakCoverRSRP;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown numMod3RSRP;
        private System.Windows.Forms.NumericUpDown numMod3SINR;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numMultiRSRPDiff;
        private System.Windows.Forms.NumericUpDown numMultiCvrRSRP;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.NumericUpDown numSiteCnt;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numCvrDisFactor;
        private System.Windows.Forms.NumericUpDown numOvrLapRSRP;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.NumericUpDown numWeakCoverDis;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.NumericUpDown numMultiDistance;
        private System.Windows.Forms.NumericUpDown numMultiGrade;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.NumericUpDown numCoverLapPer;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.NumericUpDown numHoDistance;
        private System.Windows.Forms.NumericUpDown numHoCnt;
        private System.Windows.Forms.NumericUpDown numHoSecond;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.NumericUpDown numMod3Distance;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.GroupBox groupBox11;
        private System.Windows.Forms.GroupBox grpFileCheck;
        private System.Windows.Forms.Button btnBrs;
        private System.Windows.Forms.TextBox tbxGroupResult;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.RadioButton radioByCell;
        private System.Windows.Forms.RadioButton radioByFile;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.NumericUpDown numFarRSRP;
        private System.Windows.Forms.NumericUpDown numFarDistance;
        private System.Windows.Forms.NumericUpDown numFarTpNum;
        private System.Windows.Forms.LinkLabel linkFileDesc;
        private System.Windows.Forms.DataGridView gvFileDesc;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCell;
        private System.Windows.Forms.DataGridViewTextBoxColumn colUltraType;
        private System.Windows.Forms.NumericUpDown numDiffBandDis;
        private System.Windows.Forms.Label label43;
        private System.Windows.Forms.Label label44;
        private System.Windows.Forms.Label label45;
        private System.Windows.Forms.CheckBox SameARFCN;
    }
}