﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteScanAntennaForm : MinCloseForm
    {
        public LteScanAntennaForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            initAngleArgs();
            rtbDesc.Text = WriteMRDesc();
            this.dataGridViewAngle.Columns[0].Frozen = true;
            this.dataGridViewAngle.Columns[1].Frozen = true;
        }

        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        int iPointIndex = 0;
        int iDataNum = 0;
        private Dictionary<string, ZTLteScanAntenna.CellAngleData> cellAngleDataDic = new Dictionary<string, ZTLteScanAntenna.CellAngleData>();
        #region 控件初始化及数据填值方法集

        /// <summary>
        /// 新填充方法是通过nrDatasList数据进行填充，请勿更改里面的顺序
        /// </summary>
        public void FillData(Dictionary<string, ZTLteScanAntenna.CellAngleData> cellAngleDataDic)
        {
            foreach (string strCellName in cellAngleDataDic.Keys)
            {
                this.cellAngleDataDic.Add(strCellName, cellAngleDataDic[strCellName]);
            }

            dataGridView.Columns.Clear();
            dataGridView.Rows.Clear();
            dataGridViewCell.Columns.Clear();
            dataGridViewCell.Rows.Clear();
            dataGridViewAnt.Columns.Clear();
            dataGridViewAnt.Rows.Clear();

            iDataNum = this.cellAngleDataDic.Count;
            labNum.Text = iDataNum.ToString();
            int iPage = iDataNum % 200 > 0 ? iDataNum / 200 + 1 : iDataNum / 200;
            labPage.Text = iPage.ToString();

            for (int i = 0; i < nrDatasList.Count; i++)
            {
                initData(i);
            }
            FillRegionAndAngleData();
            this.scanAntDataXTCtrl.SelectedTabPageIndex = 0;
        }

        private void initData(int i)
        {
            int idx = 0;
            foreach (NPOIRow row in nrDatasList[i])
            {
                if (idx == 0)
                {
                    intDataViewColumn(row.cellValues, i);
                    idx++;
                }
                else
                {
                    if (i == 2)
                    {
                        if (idx > 200)
                            break;

                        initDataRow(dataGridViewCell, row, row.cellValues[2].ToString());
                    }
                }
            }
        }

        /// <summary>
        /// 按小区模糊查找，前200个小区
        /// </summary>
        private void FillData(string strCellName)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow row in nrDatasList[2])
            {
                if (nrDatasList[2][0].cellValues[0].ToString() == row.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && row.cellValues[3].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;
                initDataRow(dataGridViewCell, row, row.cellValues[2].ToString());
                rowCellAt++;
            }
            FillRegionAndAngleData();
        }

        /// <summary>
        /// 按页数查找
        /// </summary>
        private void FillData(int iPage)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow row in nrDatasList[2])
            {
                if (nrDatasList[2][0].cellValues[0].ToString() == row.cellValues[0].ToString())
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;
                initDataRow(dataGridViewCell, row, row.cellValues[2].ToString());
                rowCellAt++;
            }
            FillRegionAndAngleData();
        }

        /// <summary>
        /// 初始化列头
        /// </summary>
        private void intDataViewColumn(List<object> objList, int iSheet)
        {
            int idx = 1;
            foreach (object obj in objList)
            {
                switch (iSheet)
                {
                    case 0:
                        dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
                        break;
                    case 2:
                        dataGridViewCell.Columns.Add(idx++.ToString(), obj.ToString());
                        break;
                    case 3:
                        dataGridViewAnt.Columns.Add(idx++.ToString(), obj.ToString());
                        break;
                    default:
                        break;
                }
            }
        }

        /// <summary>
        /// 初始化数据赋值
        /// </summary>
        private void initDataRow(DataGridView datatGridView, NPOIRow nop, string strCellName)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = strCellName;//小区名称
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            datatGridView.Rows.Add(row);
        }

        /// <summary>
        /// 小区天线辐射及区间级数据赋值
        /// </summary>
        private void FillRegionAndAngleData()
        {
            dataGridView.Rows.Clear();
            dataGridViewAnt.Rows.Clear();

            foreach (DataGridViewRow dgCell in dataGridViewCell.Rows)
            {
                if (dgCell == null || dgCell.Tag == null)
                    continue;

                foreach (NPOIRow row in nrDatasList[0])
                {
                    if (row.cellValues[0].ToString() == dgCell.Tag.ToString())
                    {
                        initDataRow(dataGridView, row, row.cellValues[0].ToString());
                    }
                }

                foreach (NPOIRow row in nrDatasList[3])
                {
                    if (row.cellValues[1].ToString() == dgCell.Tag.ToString())
                    {
                        initDataRow(dataGridViewAnt, row, row.cellValues[1].ToString());
                    }
                }
            }
        }

        ZTLteScanAntenna.CellAngleData data;
        ///<summary>
        ///生成0~360度的角度列
        ///</summary>
        public void initAngleArgs()
        {
            for (int i = -179; i < 181; i++ )
            {
                DataGridViewTextBoxColumn angelCol = new DataGridViewTextBoxColumn();
                angelCol.HeaderText = i + "°";
                angelCol.Width = 63;
                dataGridViewAngle.Columns.Add(angelCol);
            }

            cbbxSeries1.Items.Add("RSRP");
            cbbxSeries1.Items.Add("平滑RSRP");
            cbbxSeries1.Items.Add("SINR");
            cbbxSeries1.Items.Add("通信距离");
            cbbxSeries1.SelectedIndex = 0;

            cbbxSeries2.Items.Add("RSRP");
            cbbxSeries2.Items.Add("平滑RSRP");
            cbbxSeries2.Items.Add("SINR");
            cbbxSeries2.Items.Add("通信距离");
            cbbxSeries2.SelectedIndex = 3;

            Series series = chartControl1.Series[0];
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            series.ArgumentScaleType = ScaleType.Numerical; //设置x轴有固定间距
            SeriesPoint pt;
            pt = new SeriesPoint(-150, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(0, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(150, 50);
            series.Points.Add(pt);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
        }

        /// <summary>
        /// "天线辐射波形重建"中显示表格信息
        /// </summary>
        public void setAngelTable(ZTLteScanAntenna.CellAngleData data)
        {
            dataGridViewAngle.Rows.Clear();
            int rowAt = 0;
            dataGridViewAngle.Rows.Add(1);

            int[] rsrpArray = data.ciItem.rsrpArray;
            double[] newRsrpArray = data.ciItem.newRsrpArray;
            int[] sinrArray = data.ciItem.sinrArray;
            double[] sampleDistArray = data.ciItem.sampArray;
            int[] sampleNumArray = data.ciItem.sampNumArray;

            addRowValue(data, rowAt, "RSRP", rsrpArray, sampleNumArray, new IFunc(setRsrp));
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            addRowValue(data, rowAt, "平滑RSRP", newRsrpArray, sampleNumArray, new DFunc(setSmoothRsrp));
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            addRowValue(data, rowAt, "SINR", sinrArray, sampleNumArray, new IFunc(setSinr));
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            addRowValue(data, rowAt, "通信距离", sampleDistArray, sampleNumArray, new DFunc(setDistance));
        }

        private string setRsrp(int[] valueAry, int[] countAry, int index)
        {
            string str = (countAry[index] == 0 ? -140 : valueAry[index] / countAry[index]).ToString();
            return str;
        }

        private string setSmoothRsrp(double[] valueAry, int[] countAry, int index)
        {
            string str = (Math.Round(valueAry[index], 2)).ToString();
            return str;
        }

        private string setSinr(int[] valueAry, int[] countAry, int index)
        {
            string str = (countAry[index] == 0 ? -25 : valueAry[index] / countAry[index]).ToString();
            return str;
        }

        private string setDistance(double[] valueAry, int[] countAry, int index)
        {
            string str = (Math.Round(countAry[index] == 0 ? 0 : valueAry[index] / countAry[index], 2)).ToString();
            return str;
        }

        private void addRowValue(ZTLteScanAntenna.CellAngleData data, int rowAt, string name, int[] valueAry, int[] countAry, IFunc func)
        {
            int colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = name;
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = func(valueAry, countAry, i);
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = func(valueAry, countAry, i);
            }
        }

        private delegate string IFunc(int[] valueAry, int[] countAry, int index);

        private void addRowValue(ZTLteScanAntenna.CellAngleData data, int rowAt, string name, double[] valueAry, int[] countAry, DFunc func)
        {
            int colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = name;
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = func(valueAry, countAry, i);
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = func(valueAry, countAry, i);
            }
        }

        private delegate string DFunc(double[] valueAry, int[] countAry, int index);
        #endregion

        #region 绘制各类图表方法集

        /// <summary>
        /// 调整Y轴，按绝对值大小描写刻度
        /// </summary>
        private void checkReverse()
        {
            if ((double)(((XYDiagram)chartControl1.Diagram).AxisY.Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = true;
            }

            if ((double)(((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = true;
            }
        }

        /// <summary>
        /// 按参数值1画图表
        /// </summary>
        private void DrawTable1Series(int[] seriesValues)
        {
            int count = 0;
            SeriesPoint pt;
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;

            int[] sampleNumArray = data.ciItem.sampNumArray;
            for (int i = 181; i < 360; i++)
            {
                string arg = (-179 + count).ToString();
                double value = Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / sampleNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
                count++;
            }
            for (int i = 1; i < 181; i++)
            {
                string arg = i.ToString();
                double value = Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / sampleNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
            }

            chartControl1.Series.Insert(0, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值1画图表
        /// </summary>
        private void DrawTable1Series(double[] seriesValues)
        {
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;
            int[] sampleNumArray = data.ciItem.sampNumArray;

            int count = 0;
            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] / sampleNumArray[i], 2)));
            }
            for (int i = 1; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] / sampleNumArray[i], 2)));
            }

            chartControl1.Series.Insert(0, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;


            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值1画图表
        /// </summary>
        private void DrawTable1SeriesNotCalc(double[] seriesValues)
        {
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;
            int count = 0;
            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(seriesValues[i],2)));
            }
            for (int i = 1; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(seriesValues[i],2)));
            }

            chartControl1.Series.Insert(0, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;


            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值2画图表
        /// </summary>
        private void drawTable2Series(int[] seriesValues)
        {
            int count = 0;
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];
            int[] sampleNumArray = data.ciItem.sampNumArray;

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / sampleNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / sampleNumArray[i], 2)));
            }

            chartControl1.Series.Insert(1, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值2画图表
        /// </summary>
        private void drawTable2Series(double[] seriesValues)
        {
            int count = 0;
            
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];
            int[] sampleNumArray = data.ciItem.sampNumArray;

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] / sampleNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] / sampleNumArray[i], 2)));
            }

            chartControl1.Series.Insert(1, series);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值2画图表
        /// </summary>
        private void drawTable2SeriesNotCalc(double[] seriesValues)
        {
            int count = 0;
            
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(seriesValues[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(seriesValues[i], 2)));
            }

            chartControl1.Series.Insert(1, series);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        /// <summary>
        /// 按平滑线绘雷达图
        /// </summary>
        private void drawAntRadarSeries(double[] seriesValues, double[] modelSeriesValues)
        {
            chartControl2.Series.Clear();

            #region 实际测试数据
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;

            series.View = lineSeriesView;
            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 359; i >= 0; i--)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(seriesValues[i], 2)));
            }
            chartControl2.Series.Insert(0, series);
            #endregion

            #region 权值模型数据

            Series series2 = new Series();
            series2.ShowInLegend = false;
            series2.PointOptions.PointView = PointView.Values;

            RadarLineSeriesView lineSeriesView2 = new RadarLineSeriesView();
            lineSeriesView2.Color = Color.Orange;
            lineSeriesView2.LineMarkerOptions.Size = 2;

            series2.View = lineSeriesView2;
            series2.ArgumentScaleType = ScaleType.Numerical;
            series2.Label.Visible = false;

            int iInfoShow = 1;
            if (data.antPara.iStatus == 1)
            {
                if (data.antCfg.strBtsType.Contains("E") || data.antCfg.strBtsType.Contains("A"))
                    iInfoShow = 0;
            }
            else
                iInfoShow = 0;

            if (iInfoShow == 1)
            {
                for (int i = 0; i < 360; i++)
                {
                    double tmpValue = -120;
                    if (i >= 270)
                        tmpValue = modelSeriesValues[i - 270];
                    else if (i < 90)
                        tmpValue = modelSeriesValues[i + 90];

                    series2.Points.Add(new SeriesPoint(i.ToString(), Math.Round(tmpValue, 2)));
                }
            }
            chartControl2.Series.Insert(1, series2);
            #endregion

            int iMaxValue = -50;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(modelSeriesValues, ref iMaxValue, ref iMinValue);
            iMinValue = 50;//最小值不取模型值
            ZTAntFuncHelper.getMaxAndMinValue(seriesValues, ref iMaxValue, ref iMinValue);

            ((RadarDiagram)chartControl2.Diagram).AxisY.Range.MinValue = iMinValue;
            ((RadarDiagram)chartControl2.Diagram).AxisY.Range.MaxValue = iMaxValue;

            ((RadarDiagram)chartControl2.Diagram).AxisX.GridSpacing = 20;

            ((RadarDiagram)chartControl2.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartControl2.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;

            chartControl2.Focus();
        }

        /// <summary>
        /// 按MR二维数据绘雷达图
        /// </summary>
        private void drawCellMRRadarSeries()
        {
            chartControl8.Series.Clear();

            int idx = 0;
            Dictionary<int, List<string>> colorDic = getMRDataColorDic();
            int iMaxValue = 0;
            for (int c = 1; c <= 10; c++)
            {
                if (!colorDic.ContainsKey(c))
                    continue;

                AntLegend antLegend = ZTAntFuncHelper.GetMRDataLegend(c);
                Series series = new Series();
                series.ShowInLegend = true;
                series.LegendText = antLegend.strLegend;
                series.PointOptions.PointView = PointView.Values;

                RadarPointSeriesView pointSeriesView = new RadarPointSeriesView();
                pointSeriesView.Color = antLegend.colorType;
                pointSeriesView.PointMarkerOptions.Size = 3;

                series.View = pointSeriesView;
                series.ArgumentScaleType = ScaleType.Numerical;
                series.Label.Visible = false;

                foreach (string strInfo in colorDic[c])
                {
                    string[] str = strInfo.Split('_');
                    int iTmpValue = 0;
                    int.TryParse(str[1], out iTmpValue);
                    if (iTmpValue > iMaxValue)
                        iMaxValue = iTmpValue;
                    series.Points.Add(new SeriesPoint(str[0], str[1]));
                }

                chartControl8.Series.Insert(idx, series);
                idx++;
            }

            if (colorDic.Count > 0)
            {
                ((RadarDiagram)chartControl8.Diagram).AxisY.Range.MinValue = -1;
                ((RadarDiagram)chartControl8.Diagram).AxisY.Range.MaxValue = iMaxValue + 300;

                ((RadarDiagram)chartControl8.Diagram).AxisX.GridSpacing = 20;

                ((RadarDiagram)chartControl8.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
                ((RadarDiagram)chartControl8.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            }
            else
            {
                Series series = new Series();
                series.ShowInLegend = false;
                series.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                lineSeriesView.Color = Color.Blue;
                lineSeriesView.LineMarkerOptions.Size = 2;

                series.View = lineSeriesView;
                series.ArgumentScaleType = ScaleType.Numerical;
                series.Label.Visible = false;
                chartControl8.Series.Insert(0, series);
            }
            chartControl8.Focus();
        }

        /// <summary>
        /// 获取MR二维数据
        /// </summary>
        private Dictionary<int, List<string>> getMRDataColorDic()
        {
            Dictionary<int, List<string>> colorDic = new Dictionary<int, List<string>>();
            if (data != null)
            {
                for (int i = 0; i < 44; i++)
                {
                    for (int j = 0; j < 72; j++)
                    {
                        addColorDic(colorDic, i, j);
                    }
                }
            }
            return colorDic;
        }

        private void addColorDic(Dictionary<int, List<string>> colorDic, int i, int j)
        {
            int n = j * 5;
            if (data.cellMrData.AnaRttdAoa[i, j] > 0)
            {
                int c = ZTAntFuncHelper.GetMRDataLevel(data.cellMrData.AnaRttdAoa[i, j]);
                int iDist = ZTAntFuncHelper.calcDistByLteMrTa(i);
                string strDesc = string.Format("{0}_{1}", n.ToString(), iDist);
                if (colorDic.ContainsKey(c))
                {
                    colorDic[c].Add(strDesc);
                }
                else
                {
                    List<string> descList = new List<string>();
                    descList.Add(strDesc);
                    colorDic.Add(c, descList);
                }
            }
        }

        /// <summary>
        /// 按权值理想覆盖图
        /// </summary>
        private void DrawWeightTableSeries(double[] modelSeries)
        {
            chartControl5.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;

            int iInfoShow = 1;
            if (data.antPara.iStatus == 1)
            {
                if (data.antCfg.strBtsType.Contains("E") || data.antCfg.strBtsType.Contains("A"))
                    iInfoShow = 0;
            }
            else
                iInfoShow = 0;

            if (iInfoShow == 1)
            {
                for (int i = 0; i < 180; i++)
                {
                    series.Points.Add(new SeriesPoint((-90 + i).ToString(), Math.Round(modelSeries[i], 2)));
                }
            }

            iPointIndex = 0;
            chartControl5.Series.Insert(0, series);
            
            ((XYDiagram)chartControl5.Diagram).AxisX.Range.MinValue = -90;
            ((XYDiagram)chartControl5.Diagram).AxisX.Range.MaxValue = 90;

            chartControl5.Focus();
        }

        /// <summary>
        /// 按距离等间线绘图
        /// </summary>
        private void drawVertSplineSeries()
        {
            double[] tmpAarry = new double[200];
            chartControl3.Series.Clear();
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SplineSeriesView lineSeriesView = new SplineSeriesView();
            lineSeriesView.Color = Color.DarkCyan;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;

            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 200; i++)
            {
                double tmpValue = -140;
                if(data.ciItem.antVertDic.ContainsKey(i))
                    tmpValue = Math.Round(data.ciItem.antVertDic[i].IRsrp * 1.0 / data.ciItem.antVertDic[i].ISampNum, 2);

                series.Points.Add(new SeriesPoint(i.ToString(), tmpValue));
                tmpAarry[i] = tmpValue;
            }
            chartControl3.Series.Insert(0, series);

            int iMaxValue = -50;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(tmpAarry, ref iMaxValue, ref iMinValue);
            ((XYDiagram)chartControl3.Diagram).AxisY.Range.MinValue = iMinValue;
            ((XYDiagram)chartControl3.Diagram).AxisY.Range.MaxValue = iMaxValue;
            ((SplineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl3.Focus();
        }

        /// <summary>
        /// 添加MR测量项描述
        /// </summary>
        public static string WriteMRDesc()
        {
            string strDesc = "";
            strDesc += "1.时间提前量\n";
            strDesc += "(1)定义：为UE用于调整其主小区PUCCH/PUSCH/SRS上行发送的时间。具体计算方法为：在随机接入过程，" +
                        "eNodeB通过测量接收到导频信号来确定时间提前值，时间提前量取值范围为（0, 1, 2, ..., 1282）×16Ts；在RRC连接状态下，" +
                        "eNodeB基于测量对应UE的上行传输来确定每个UE的TA调整值，这个调整值的范围为（0, 1, 2,..., 63）×16Ts。" +
                        "本次得到的最新的时间提前量即为上次记录的时间提前量与本次eNodeB测量得到的调整值之和。\n";
            strDesc += "(2)取值范围：如从0到192Ts每16Ts为一个区间，对应MR.Tadv.00到MR.Tadv.11；从192Ts到1024Ts每32Ts为一个区间，" +
                       "对应MR.Tadv.12到MR.Tadv.37；从1024Ts到2048Ts每256Ts为一个区间，对应MR.Tadv.38到MR.Tadv.41；从2048Ts到4096Ts每1048Ts为一个区间，" +
                       "对应MR. Tadv.42和MR.Tadv.43；大于4096Ts为一个区间，对应MR.Tadv.44。\n";
            strDesc += "(3)作用：f)	该测量数据可用于确定UE距离基站的远近，实现小区的覆盖分析，判断是否需要对小区天线做出调整，"+
                       "考察基站的覆盖区域是否合理，是否存在过覆盖和覆盖阴影区等问题，还可以利用其辅助提供位置服务。\n";
            strDesc += "\n";

            strDesc += "2.UE发射功率余量\n";
            strDesc += "(1)定义：UE相对于配置的最大发射功率的余量。在headroom type 1中，此余量表示服务小区的UL-SCH发射功率与配置的最大发射功率的差值。"+
                       "在headroom type 2中，此余量表示每个激活的服务小区UL-SCH发射功率或者是PCell的PUSCH和PUCCH发射功率值和与配置的最大发射功率的差值。\n";
            strDesc += "(2)取值范围：1dB对应一个统计区间。MR.PowerHeadRoom.00 : -23<=PH<-22; MR.PowerHeadRoom.01 : -22<=PH<-21; ..."+
                       " MR.PowerHeadRoom.62 : 39<=PH<40; MR.PowerHeadRoom.63 : PH>=40\n";
            strDesc += "(3)作用：该测量数据可用于进行用户发射功率分析等。\n";
            strDesc += "\n";

            strDesc += "3.参考信号接收功率\n";
            strDesc += "(1)定义：在考虑测量的频带上，承载小区专属参考信号的资源单元（RE）的功率（W）的线性平均值,是反映服务小区覆盖的主要指标。"+
                       "本测量数据表示OMC-R统计周期内满足取值范围的按照分区间统计UE参考信号接收功率的样本个数。\n";
            strDesc += "(2)取值范围：如从-∞到-120dBm一个区间，对应MR.RSRP.00；从-120 dBm到-115 dBm为一个区间，对应MR.RSRP.01；"+
                       "从-115dBm到-80dBm每1dB一个区间，对应MR.RSRP.02到MR.RSRP.36；从-80dBm到-60dBm每2dB一个区间，对应MR.RSRP.37到MR.RSRP.46；"+
                       "大于-60dBm一个区间，对应MR.RSRP.47，依此类推。\n";
            strDesc += "(3)作用：该数据可用于评估LTE小区的覆盖情况，根据不同场强区间分布比例可判断该小区的大致覆盖范围。天线遮挡及硬件故障会造成信号弱，"+
                       "容易产生掉话及降低接通率，用于检查小区覆盖盲点/弱覆盖区域。通过源小区和邻区RSRP可进行导频污染分析。\n";
            strDesc += "\n";


            strDesc += "4.ENB天线到达角\n";
            strDesc += "(1)定义：一个用户相对参考方向的估计角度。测量参考方向应为正北，逆时针方向。可以辅助确定用户所处的方位，"+
                       "提供定位服务，精度为5度。本测量数据表示OMC-R统计周期内满足取值范围条件的按照分区间统计天线到达角的样本个数。"+
                       "适用于eNodeB具有多天线的情况，当天线个数小于等于2时，本测量项取值为NIL。\n";
            strDesc += "(2)取值范围：如0度到小于5度为一个区间，对应MR.AOA.00；355度到小于360度为一个区间，对应MR.AOA.71，依此类推。\n";
            strDesc += "(3)作用：该测量数据可用于确定用户所处的方位、进行覆盖分析等。\n";
            strDesc += "\n";

            strDesc += "5.上行信噪比\n";
            strDesc += "(1)定义：小区所有用户上行信噪比。具体计算方法：根据一个物理资源块（PRB）带宽上的PUSCH信号功率S和干扰功率I，计算每用户信噪比。\n";
            strDesc += "(2)取值范围：其中SINR小于-10dB，对应MR.SinrUL.00；从-10dB到25dB，每1dB为一个区间，对应MR.SinrUL.01到MR.SinrUL.35；大于25dB，对应MR.SinrUL.36。\n";
            strDesc += "(3)作用：该测量数据可用于间接分析业务质量。\n";
            strDesc += "\n";

            return strDesc;
        }

        /// <summary>
        /// 按MR发射功率余量图表
        /// </summary>
        private void DrawPowerTableSeries(int[] modelSeries)
        {
            chartControlPower.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 64; i++)
            {
                int iPower = i - 23;
                series.Points.Add(new SeriesPoint(iPower.ToString(), modelSeries[i]));
            }
            iPointIndex = 0;
            chartControlPower.Series.Insert(0, series);

            ((XYDiagram)chartControlPower.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlPower.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlPower.Focus();
        }
        
        /// <summary>
        /// 按MR参考信号接收功率余量图表
        /// </summary>
        private void DrawRsrpTableSeries(int[] modelSeries)
        {
            chartControlRsrp.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 48; i++)
            {
                int iRsrp = calcRsrpByNum(i);
                series.Points.Add(new SeriesPoint(iRsrp.ToString(), modelSeries[i]));
            }
            iPointIndex = 0;
            chartControlRsrp.Series.Insert(0, series);

            ((XYDiagram)chartControlRsrp.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlRsrp.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlRsrp.Focus();
        }

        /// <summary>
        /// 按时间差计算RSRP
        /// </summary>
        private int calcRsrpByNum(int iNum)
        {
            int iRsrp = 0;
            if (iNum == 0)
            {
                iRsrp = -120;
            }
            else if (iNum == 1)
            {
                iRsrp = -116;
            }
            else if (iNum < 37)
            {
                iRsrp = iNum - 117;
            }
            else if (iNum < 47)
            {
                iRsrp = (iNum - 37) * 2 + 37 - 116;
            }
            else
            {
                iRsrp = -60;
            }
            return iRsrp;
        }

        /// <summary>
        /// 按MR天线到达角图表
        /// </summary>
        private void DrawAoaTableSeries(int[] modelSeries)
        {
            chartControlAoa.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 72; i++)
            {
                int iAoa = (i + 1) * 5;
                series.Points.Add(new SeriesPoint(iAoa.ToString(), modelSeries[i]));
            }
            iPointIndex = 0;
            chartControlAoa.Series.Insert(0, series);

            ((XYDiagram)chartControlAoa.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlAoa.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlAoa.Focus();
        }

        /// <summary>
        /// 按MR上行信噪比图表
        /// </summary>
        private void DrawSinrTableSeries(int[] modelSeries)
        {
            chartControlSinr.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 37; i++)
            {
                int iSinr = i - 11;
                series.Points.Add(new SeriesPoint(iSinr.ToString(), modelSeries[i]));
            }
            iPointIndex = 0;
            chartControlSinr.Series.Insert(0, series);

            ((XYDiagram)chartControlSinr.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlSinr.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlSinr.Focus();
        }

        /// <summary>
        /// 按MR时间提前量图表
        /// </summary>
        private void DrawTATableSeries(int[] modelSeries)
        {
            chartControlTA.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 44; i++)
            {
                int iDist = ZTAntFuncHelper.calcDistByLteMrTa(i);
                series.Points.Add(new SeriesPoint(iDist.ToString(), modelSeries[i]));
            }
            iPointIndex = 0;
            chartControlTA.Series.Insert(0, series);

            ((XYDiagram)chartControlTA.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlTA.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlTA.Focus();
        }

        /// <summary>
        /// 使用二维数据绘图
        /// </summary>
        private void draw3DAreaSeries()
        {
            chartControl4.Series.Clear();

            if (data.ciItem.antTwoDic != null)
            {
                for (int i = 0; i < 200; i++)
                {
                    Series series = new Series();
                    series.ShowInLegend = false;
                    series.Label.Visible = false;
                    series.PointOptions.PointView = PointView.Values;


                    ManhattanBarSeriesView manhattanBarView = new ManhattanBarSeriesView();

                    series.View = manhattanBarView;

                    addValidSeriesPoints(i, series);
                    chartControl4.Series.Insert(i, series);
                }
            }

            chartControl4.Focus();
        }

        private void addValidSeriesPoints(int i, Series series)
        {
            for (int j = 0; j < 360; j++)
            {
                int idx = j * 1000 + i;
                double dRsrp = -140;
                if (data.ciItem.antTwoDic.ContainsKey(idx))
                    dRsrp = Math.Round(data.ciItem.antTwoDic[idx].ISampNum == 0 ? 0 : ((double)data.ciItem.antTwoDic[idx].IRsrp) / data.ciItem.antTwoDic[idx].ISampNum, 2);

                if (dRsrp <= -45 && dRsrp >= -120)
                {
                    series.Points.Add(new SeriesPoint(j.ToString(), dRsrp));
                }
            }
        }

        /// <summary>
        /// 按MR模拟覆盖图
        /// </summary>
        private void drawCellCoverRadarSeries(int[] seriesValues)
        {
            chartControl6.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;

            series.View = lineSeriesView;
            series.ArgumentScaleType = ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 72; i++)
            {
                int j = i * 5;
                series.Points.Add(new SeriesPoint(j, seriesValues[i]));
            }
            chartControl6.Series.Insert(0, series);

            ((RadarDiagram)chartControl6.Diagram).AxisY.Range.MinValue = -1;
            ((RadarDiagram)chartControl6.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(seriesValues);
            ((RadarDiagram)chartControl6.Diagram).AxisX.GridSpacing = 20;
            ((RadarDiagram)chartControl6.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartControl6.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;

            chartControl6.Focus();
        }
        #endregion

        #region 控件操作控制集
        private void cbbxSeries1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (data == null)
            {
                return;
            }

            Cursor.Current = Cursors.WaitCursor;
            if (this.cbbxSeries1.Text == this.cbbxSeries2.Text)
            {
                if (chartControl1.Series.Count == 1)
                {
                    return;
                }
                Series series = chartControl1.Series[1];
                series.Points.Clear();
                checkReverse();
                return; //选同一个参数，不作比较画图
            }

            UpdateTableSeries();
            checkReverse();
            Cursor.Current = Cursors.Default;
        }

        private void cbbxSeries2_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (data == null)
            {
                return;
            }

            Cursor.Current = Cursors.WaitCursor;
            if (this.cbbxSeries1.Text == this.cbbxSeries2.Text)
            {
                if (chartControl1.Series.Count == 2)
                {
                    return;
                }
                Series series = chartControl1.Series[2];
                series.Points.Clear();
                checkReverse();
                return; //选同一个参数，不作比较画图
            }

            UpdateTableSeries();
            checkReverse();
            Cursor.Current = Cursors.Default;
        }

        /// <summary>
        /// 更新表数据
        /// </summary>
        private void UpdateTableSeries()
        {
            chartControl1.Series.Clear();

            switch (this.cbbxSeries1.Text)
            {
                case "RSRP":
                    {
                        DrawTable1Series(data.ciItem.rsrpArray);
                    }
                    break;
                case "平滑RSRP":
                    {
                        DrawTable1SeriesNotCalc(data.ciItem.newRsrpArray);
                    }
                    break;
                case "SINR":
                    {
                        DrawTable1Series(data.ciItem.sinrArray);
                    }
                    break;
                case "通信距离":
                    {
                        DrawTable1Series(data.ciItem.sampArray);
                    }
                    break;
                default:
                    break;
            }

            //图例2内容
            switch (this.cbbxSeries2.Text)
            {
                case "RSRP":
                    {
                        drawTable2Series(data.ciItem.rsrpArray);
                    }
                    break;
                case "平滑RSRP":
                    {
                        drawTable2SeriesNotCalc(data.ciItem.newRsrpArray);
                    }
                    break;
                case "SINR":
                    {
                        drawTable2Series(data.ciItem.sinrArray);
                    }
                    break;
                case "通信距离":
                    {
                        drawTable2Series(data.ciItem.sampArray);
                    }
                    break;
                default:
                    break;
            }
        }

        private void miShowChart_Click(object sender, EventArgs e)
        {
            string strCellName = "";
            if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 0)
            {
                int iRowId = dataGridViewCell.SelectedCells[0].RowIndex;
                strCellName = dataGridViewCell.Rows[iRowId].Tag.ToString();
            }
            else if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 1)
            {
                int iRowId = dataGridViewAnt.SelectedCells[0].RowIndex;
                strCellName = dataGridViewAnt.Rows[iRowId].Tag.ToString();
            }
            else
            {
                strCellName = dataGridView.SelectedRows[0].Tag.ToString();
            }

            if (!cellAngleDataDic.TryGetValue(strCellName, out data))
            {
                return;
            }

            groupControl3.Text = string.Format("天线全向分析({0})", data.cellname);
            groupControl4.Text = string.Format("MR测量项数据图表({0})", data.cellname);
            if (data != null)
            {
                setAngelTable(data);

                if (chartControl1.Series.Count == 3)
                {
                    chartControl1.Series.RemoveAt(1);
                    chartControl1.Series.RemoveAt(1);
                }
                else if (chartControl1.Series.Count == 2)
                {
                    chartControl1.Series.RemoveAt(1);
                }

                this.scanAntDataXTCtrl.SelectedTabPageIndex = 3;
                cbbxSeries1_SelectedIndexChanged(null, null);
                cbbxSeries2_SelectedIndexChanged(null, null);

                drawAntRadarSeries(data.ciItem.newRsrpArray, data.ciItem.modelMaxArray);//俯视面雷达图
                drawVertSplineSeries();//垂直面覆盖图
                DrawWeightTableSeries(data.ciItem.modelMaxArray);//权值理想覆盖图
                draw3DAreaSeries();//三维图表

                DrawPowerTableSeries(data.cellMrData.lteMRPowerHeadRoomItem.dataValue);
                DrawRsrpTableSeries(data.cellMrData.lteMRRsrpItem.dataValue);
                DrawAoaTableSeries(data.cellMrData.lteMRAoaItem.dataValue);
                DrawSinrTableSeries(data.cellMrData.lteMRSinrUlItem.dataValue);
                DrawTATableSeries(data.cellMrData.lteMRTaItem.dataValue);
                drawCellMRRadarSeries();
                int[] dirArray = new int[72];
                for (int i = 0; i < 72; i++)
                {
                    if (data.dirSampleDic.ContainsKey(i))
                        dirArray[i] = data.dirSampleDic[i];
                }
                drawCellCoverRadarSeries(dirArray);
            }
        }

        /// <summary>
        /// 采样点信息回放
        /// </summary>
        private void miShowGis_Click(object sender, EventArgs e)
        {
            MainModel.ClearDTData();
            string strCellName = "";
            if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 0)
            {
                int iRowId = dataGridViewCell.SelectedCells[0].RowIndex;
                strCellName = dataGridViewCell.Rows[iRowId].Tag.ToString();
            }
            else if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 1)
            {
                int iRowId = dataGridViewAnt.SelectedCells[0].RowIndex;
                strCellName = dataGridViewAnt.Rows[iRowId].Tag.ToString();
            }
            else
            {
                strCellName = dataGridView.SelectedRows[0].Tag.ToString();
            }

            if (!cellAngleDataDic.TryGetValue(strCellName, out data))
            {
                return;
            }

            if (data != null)
            {
                foreach (TestPoint tp in data.tpList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.CellAntSampleIndexDic = data.tpIndexDic;
                MainModel.SelectedLTECell = CellManager.GetInstance().GetLTECellLatest(data.cellname);
                MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
                MainModel.FireDTDataChanged(this);
            }
        }

        /// <summary>
        /// 天线模拟仿真
        /// </summary>
        private void miShowSimulation_Click(object sender, EventArgs e)
        {
            if (dataGridViewCell.Rows.Count == 0)
            {
                return;
            }
            string strCellName = "";
            if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 0)
            {
                int iRowId = dataGridViewCell.SelectedCells[0].RowIndex;
                strCellName = dataGridViewCell.Rows[iRowId].Tag.ToString();
            }
            else if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 1)
            {
                int iRowId = dataGridViewAnt.SelectedCells[0].RowIndex;
                strCellName = dataGridViewAnt.Rows[iRowId].Tag.ToString();
            }
            else
            {
                strCellName = dataGridView.SelectedRows[0].Tag.ToString();
            }

            if (!cellAngleDataDic.TryGetValue(strCellName, out data))
            {
                return;
            }

            if (data != null)
            {
                ZTAntennaBase.SimulationPoints simulationPoints = new ZTAntennaBase.SimulationPoints();
                simulationPoints.cellLongitude = data.cellLongitude;
                simulationPoints.cellLatitude = data.cellLatitude;

                simulationPoints.strNet = "LTE";
                simulationPoints.longLatTestList.AddRange(data.longLatTestList);
                simulationPoints.longLatModelList.AddRange(data.longLatModelList);

                MainModel.SimulationPoints = simulationPoints;
                MainModel.SelectedLTECell = CellManager.GetInstance().GetLTECellLatest(data.cellname);
                MainModel.MainForm.GetMapForm().GoToView(data.cellLongitude, data.cellLatitude);
                MainModel.FireCellDrawInfoChanged(this);
            }
        }

        /// <summary>
        /// MR覆盖仿真
        /// </summary>
        private void miShowMRSimulation_Click(object sender, EventArgs e)
        {
            string strCellName = "";
            if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 0)
            {
                int iRowId = dataGridViewCell.SelectedCells[0].RowIndex;
                strCellName = dataGridViewCell.Rows[iRowId].Tag.ToString();
            }
            else if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 1)
            {
                int iRowId = dataGridViewAnt.SelectedCells[0].RowIndex;
                strCellName = dataGridViewAnt.Rows[iRowId].Tag.ToString();
            }
            else
            {
                strCellName = dataGridView.SelectedRows[0].Tag.ToString();
            }

            if (!cellAngleDataDic.TryGetValue(strCellName, out data))
            {
                return;
            }

            if (data != null)
            {
                ZTAntennaBase.SimulationPoints simulationPoints = new ZTAntennaBase.SimulationPoints();
                simulationPoints.cellLongitude = data.cellLongitude;
                simulationPoints.cellLatitude = data.cellLatitude;

                simulationPoints.strNet = "LTE";
                simulationPoints.longLatMRList.AddRange(data.mrAoaList);

                MainModel.SimulationPoints = simulationPoints;
                MainModel.SelectedLTECell = CellManager.GetInstance().GetLTECellLatest(data.cellname);
                MainModel.MainForm.GetMapForm().GoToView(data.cellLongitude, data.cellLatitude);
                MainModel.FireCellDrawInfoChanged(this);
            }
        }

        /// <summary>
        /// 天线分析数据导出
        /// </summary>
        private void miExportWholeExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }

        /// <summary>
        /// 天线分析数据导出csv
        /// </summary>
        private void 拆分导出CSVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }

        /// <summary>
        /// 各个选择项的右键菜单可见设置
        /// </summary>
        private void xtraTabControl1_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (scanAntDataXTCtrl.SelectedTabPageIndex == 0 || scanAntDataXTCtrl.SelectedTabPageIndex == 1 || scanAntDataXTCtrl.SelectedTabPageIndex == 2)
            {
                miShowChart.Visible = true;
                miShowGis.Visible = true;
                miShowSimulation.Visible = true;
            }
            else if (scanAntDataXTCtrl.SelectedTabPageIndex == 3 || scanAntDataXTCtrl.SelectedTabPageIndex == 4)
            {
                miShowChart.Visible = false;
                miShowGis.Visible = false;
                miShowSimulation.Visible = false;
            }
        }

        private void chartControl4_CustomDrawSeriesPoint(object sender, CustomDrawSeriesPointEventArgs e)
        {
            double dRsrp = e.SeriesPoint.Values[0];
            if (dRsrp <= -110)
                e.SeriesDrawOptions.Color = Color.DarkRed;
            else if (dRsrp <= -100)
                e.SeriesDrawOptions.Color = Color.Red;
            else if (dRsrp <= -90)
                e.SeriesDrawOptions.Color = Color.Orange;
            else if (dRsrp <= -80)
                e.SeriesDrawOptions.Color = Color.Yellow;
            else if (dRsrp <= -70)
                e.SeriesDrawOptions.Color = Color.Green;
            else
                e.SeriesDrawOptions.Color = Color.Blue;
        }

        private void chartControl5_CustomDrawSeriesPoint(object sender, CustomDrawSeriesPointEventArgs e)
        {
            double dRsrp = e.SeriesPoint.Values[0];
            iPointIndex = iPointIndex % 180;
            if (Math.Round(data.ciItem.model1Array[iPointIndex], 2) >= dRsrp)
                e.SeriesDrawOptions.Color = Color.Blue;
            else
                e.SeriesDrawOptions.Color = Color.Red;

            iPointIndex++;
        }

        private void chartControl2_SizeChanged(object sender, EventArgs e)
        {
            ((RadarDiagram)chartControl2.Diagram).AxisX.GridSpacing = 20;
        }

        #endregion

        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = iDataNum;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = iDataNum;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }
    }
}
