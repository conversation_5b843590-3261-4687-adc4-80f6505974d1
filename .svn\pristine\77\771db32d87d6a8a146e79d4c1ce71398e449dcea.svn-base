﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellWrongDirSettingDlg_TD : BaseDialog
    {
        public CellWrongDirSettingDlg_TD()
        {
            InitializeComponent();
        }

        public void SetCondition(CellWrongDirCondition cond)
        {
            if (cond == null)
                return;

            spinEditRSCP.Value = (decimal)cond.RxLevMin;
            spinEditDis.Value = (decimal)cond.DistanceMin;
            spinEditAngle.Value = cond.AngleMin;
            spinEditPer.Value = (decimal)cond.WrongRateMin;
        }

        public CellWrongDirCondition GetConditon()
        {
            DateTime dtDay = DateTime.Now.Date.AddDays(-1);
            TimePeriod tPeriod = new TimePeriod(dtDay, dtDay.AddDays(2).AddMilliseconds(-1));

            CellWrongDirCondition cond = new CellWrongDirCondition((float)spinEditRSCP.Value, (double)spinEditDis.Value
                , (int)spinEditAngle.Value, (double)spinEditPer.Value, false,
                new TimePeriod(tPeriod.BeginTime, tPeriod.EndTime),
                new TimePeriod(tPeriod.BeginTime, tPeriod.EndTime));
            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
