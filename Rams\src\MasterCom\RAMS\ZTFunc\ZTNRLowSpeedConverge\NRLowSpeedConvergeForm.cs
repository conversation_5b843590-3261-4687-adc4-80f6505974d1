﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRLowSpeedConvergeForm : MinCloseForm
    {
        public NRLowSpeedConvergeForm()
        {
            InitializeComponent();
        }
        NRLowSpeedConvergeLayer layer;
        List<NRLowSpeedConvergeBlock> blockList;
        public void FillData(List<NRLowSpeedConvergeBlock> blockList)
        {
            MainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
            this.blockList = blockList;
            MapForm mf = MainModel.MainForm.GetMapForm();
            MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(NRLowSpeedConvergeLayer));
            layer = clayer as NRLowSpeedConvergeLayer;
            layer.FillData(blockList);

            gridControl.DataSource = blockList;
            gridControl.RefreshDataSource();
        }

        private void miExportXlsSum_Click(object sender, EventArgs e)
        {
            try
            {
                List<NPOIRow> lstRows = getResultRows();
                ExcelNPOIManager.ExportToExcel(lstRows);
            }
            catch
            {
                MessageBox.Show("导出导Excel...失败！");
            }
        }

        private List<NPOIRow> getResultRows()
        {
            List<NPOIRow> lstRows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("采样点数");
            row.AddCellValue("平均速率");
            row.AddCellValue("平均RSRP");
            row.AddCellValue("平均SINR");
            row.AddCellValue("中心经度");
            row.AddCellValue("中心纬度");
            row.AddCellValue("下行码字0 QAM64占比");
            row.AddCellValue("下行码字1 QAM64占比");
            row.AddCellValue("平均PDSCH BLER");
            row.AddCellValue("平均PDCCH_DL_Grant_Count");
            //row.AddCellValue("单流占比");
            //row.AddCellValue("双流占比");

            row.AddCellValue("小区名");
            row.AddCellValue("频点");
            row.AddCellValue("PCI");
            row.AddCellValue("速率");
            row.AddCellValue("RSRP");
            row.AddCellValue("SINR");
            row.AddCellValue("时间");
            row.AddCellValue("与小区距离");
            row.AddCellValue("PDSCH_BLER");
            row.AddCellValue("PDCCH_DL_Grant_Count");
            lstRows.Add(row);

            for (int idx = 0; idx < blockList.Count; idx++)
            {
                NRLowSpeedConvergeBlock result = blockList[idx];

                row = new NPOIRow();
                row.AddCellValue(result.TestPointCount);
                row.AddCellValue(result.Speed.Avg);
                row.AddCellValue(result.Rsrp.Avg);
                row.AddCellValue(result.Sinr.Avg);
                row.AddCellValue(result.LongitudeMid);
                row.AddCellValue(result.LatitudeMid);
                row.AddCellValue(result.Code0Qam64Rate.Avg);
                row.AddCellValue(result.Code1Qam64Rate.Avg);
                row.AddCellValue(result.Bler.Avg);
                row.AddCellValue(result.DLGrantCount.Avg);
                lstRows.Add(row);

                foreach (var ocResult in result.Samplelist)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(ocResult.CellName);
                    subRow.AddCellValue(ocResult.EARFCN);
                    subRow.AddCellValue(ocResult.PCI);
                    subRow.AddCellValue(ocResult.Speed);
                    subRow.AddCellValue(ocResult.Rsrp);
                    subRow.AddCellValue(ocResult.Sinr);
                    subRow.AddCellValue(ocResult.Time);
                    subRow.AddCellValue(ocResult.Distance);
                    subRow.AddCellValue(ocResult.PDSCH_BLER);
                    subRow.AddCellValue(ocResult.PDCCH_DL_Grant_Count);
                }
            }

            return lstRows;
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gvBlock.SelectedRowsCount > 0)
            {
                NRLowSpeedConvergeBlock item = gvBlock.GetRow(gvBlock.GetSelectedRows()[0]) as NRLowSpeedConvergeBlock;
                MainModel.MainForm.GetMapForm().GoToView(item.LongitudeMid, item.LatitudeMid);
                if (item.TestPoints.Count == 0)
                {
                    return;
                }

                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in item.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
            }


            //if (listViewTotal.SelectedObject is NRLowSpeedConvergeBlock)
            //{
            //    NRLowSpeedConvergeBlock block = listViewTotal.SelectedObject as NRLowSpeedConvergeBlock;
            //    if (block != null)
            //    {
            //        mModel.DTDataManager.Clear();
            //        foreach (TestPoint tp in block.TestPoints)
            //        {
            //            mModel.DTDataManager.Add(tp);
            //        }
            //        mModel.FireDTDataChanged(this);
            //    }
            //}
            //else if (listViewTotal.SelectedObject is NRLowSpeedConvergeSample)
            //{
            //    NRLowSpeedConvergeSample sample = listViewTotal.SelectedObject as NRLowSpeedConvergeSample;
            //    if (sample != null)
            //    {
            //        mModel.DTDataManager.Clear();
            //        mModel.DTDataManager.Add(sample.testPoint);
            //        mModel.FireDTDataChanged(this);
            //    }
            //}
        }
    }
}
