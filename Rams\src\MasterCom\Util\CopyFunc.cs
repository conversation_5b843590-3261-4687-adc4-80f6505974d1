﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;

namespace MasterCom.Util
{
    /// <summary>
    /// 对象之间的深复制及不同对象之间同字段或同属性之间的赋值
    /// </summary>
    public static class CopyFunc
    {
        #region 根据公共字段或属性反射赋值
        /// <summary>
        /// 两个不同对象之间同属性，同字段赋值(备注:同一对象之间不需要使用该方法)
        /// </summary>
        /// <param name="destination">目标对象</param>
        /// <param name="source">源对象</param>
        /// <returns>成功赋值的属性及字段个数</returns>
        public static int Copy(object destination, object source)
        {
            if (destination == null || source == null)
            {
                return 0;
            }
            int i = 0;
            Type sourceType = source.GetType();
            Type desType = destination.GetType();
            foreach (FieldInfo mi in sourceType.GetFields())
            {
                try
                {
                    FieldInfo des = desType.GetField(mi.Name);
                    if (des != null && des.FieldType == mi.FieldType)
                    {
                        des.SetValue(destination, mi.GetValue(source));
                        i++;
                    }
                }
                catch
                {
                    //continue
                }
            }
            foreach (PropertyInfo pi in sourceType.GetProperties())
            {
                try
                {
                    PropertyInfo des = desType.GetProperty(pi.Name);
                    if (des != null && des.PropertyType == pi.PropertyType && des.CanWrite && pi.CanRead)
                    {
                        des.SetValue(destination, pi.GetValue(source, null), null);
                        i++;
                    }
                }
                catch
                {
                    //continue
                }
            }
            return i;
        }
        #endregion

        #region 深表复制，待复制的表必须添加特性[Serializable]
        /// <summary>
        /// 深表复制
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="RealObject">源对象</param>
        /// <returns>复制后的目标对象</returns>
        public static T Copy<T>(T RealObject)
        {
            using (Stream objectStream = new MemoryStream())
            {
                //利用 System.Runtime.Serialization序列化与反序列化完成引用对象的复制     
                IFormatter formatter = new BinaryFormatter();
                formatter.Serialize(objectStream, RealObject);
                objectStream.Seek(0, SeekOrigin.Begin);
                return (T)formatter.Deserialize(objectStream);
            }
        }
        #endregion
    }
}
