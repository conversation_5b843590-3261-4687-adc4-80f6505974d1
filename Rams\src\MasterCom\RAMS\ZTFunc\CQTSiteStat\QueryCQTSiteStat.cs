﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.CQTSiteStat;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryCQTSiteStat : QueryKPIStatBase
    {
        protected override Model.Interface.StatTbToken getTableNameToken()
        {
            return Model.Interface.StatTbToken.area;
        }

        public override string Name
        {
            get { return "CQT站点指标竞比查询"; }
        }

        protected override bool getConditionBeforeQuery()
        {
            CQTSiteCellMnger.Instance.Init();
            rptTemplate = new CQTSiteStatTemplate().CreateDefault();
            keyStatDic = new Dictionary<string, SiteStatInfo>();
            this.condition.Areas = new Dictionary<int, List<int>>();
            this.condition.Areas[10] = null;
            return true;
        }

        protected override void fireShowResult()
        {
            CQTSiteStatResultForm form = mainModel.CreateResultForm(typeof(CQTSiteStatResultForm)) as CQTSiteStatResultForm;
            form.FillData(this.rptTemplate, new List<SiteStatInfo>(this.keyStatDic.Values));
            form.Visible = true;
            form.BringToFront();
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            foreach (SiteStatInfo item in keyStatDic.Values)
            {
                item.KPIData.FinalMtMoGroup();
            }
        }

        protected override bool isKPIDataContent(Package package, out KPIStatDataBase statData)
        {
            statData = null;
            switch (package.Content.Type)
            {
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_GSM:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_GPRS:
                    statData = new StatDataGSM();
                    break;
                case ResponseType.AREASTAT_KPI_LTE:
                    statData = new StatDataLTE();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_LTE_FDD_AMR:
                    statData = new StatDataLTE_FDD();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_AMR:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_PS:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_VP:
                    statData = new StatDataTD();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA_V:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA_D:
                    statData = new StatDataCDMA_Voice();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA2000_D:
                    statData = new StatDataCDMA_EVDO();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_GSM:
                    statData = new StatDataSCAN_GSM();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_LTETOPN:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_LTEFREQSPECTRUM:
                    statData = new StatDataSCAN_LTE();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_TDSCDMA:
                    statData = new StatDataSCAN_TD();
                    break;
                //case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_WCDMA:
                //case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_DTWCDMA:
                //    statData = new StatDataSCAN_WCDMA();
                //    break;
                //case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_CDMA:
                //    statData = new StatDataSCAN_CDMA();
                //    break;
                //case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_GSM_MTR:
                //case ResponseType.RESTYPE_DIY_LOG_KPI_GSM_MTR:
                //    statData = new StatDataGSM_MTR();
                //    break;
                //case ResponseType.RESTYPE_DIY_LOG_KPI_WLAN:
                //    statData = new StatDataWLAN();
                //    break;
                //case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_LTE_SIGNAL:
                //    statData = new StatDataLTE_Signal();
                //    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_NBIOTTOPN:
                    statData = new StatDataSCAN_NBIOT();
                    break;
                //case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_NR:
                //    statData = new StatDataSCAN_NR();
                //    break;
            }
            return statData != null;
        }

        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            AddDIYEndOpFlag(package);
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,25,1");
            package.Content.AddParam("10");
        }

        protected override void AddGeographicFilter(Package package)
        {
            //
        }

        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREASTAT_EVNET;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREASTAT_KPI;
                package.Content.PrepareAddParam();
            }
        }

        CQTSiteStatTemplate rptTemplate = null;

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            this.imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            this.evtIDFileNameDic = new Dictionary<int, Dictionary<string, bool>>();
            this.imgCodeFileNameDic = new Dictionary<string, Dictionary<string, bool>>();
            Dictionary<string, bool> triadIDDic = new Dictionary<string, bool>();
            if (rptTemplate == null)
            {
                return "";
            }
            foreach (Column col in this.rptTemplate.Columns)
            {
                Dictionary<string, bool> tempDic = extractTriadID(col.Formula
                    , null, ref this.imgCodeSvrIDDic, ref this.evtIDSvrIDDic
                    , col.FileNames, ref this.imgCodeFileNameDic, ref this.evtIDFileNameDic);
                if (tempDic != null)
                {
                    foreach (string id in tempDic.Keys)
                    {
                        triadIDDic[id] = true;
                    }
                }
            }

            StringBuilder sb = new StringBuilder();
            foreach (string triadID in triadIDDic.Keys)
            {
                sb.Append(triadID);
                sb.Append(",");
            }
            if (sb.Length > 0)
            {//remove last ","
                sb = sb.Remove(sb.Length - 1, 1);
            }
            return sb.ToString();
        }
        protected override void preparePackageCondition(Package package, TimePeriod period
            , params object[] reservedParams)
        {
            base.preparePackageCondition(package, period, reservedParams);

            List<int> evtIdList = getNeedEventIdList();
            if (isQueringEvent && evtIdList.Count > 0)
            {
                StringBuilder sbEventIds = new StringBuilder();
                foreach (int evtId in evtIdList)
                {
                    sbEventIds.Append(evtId);
                    sbEventIds.Append(",");
                }
                if (sbEventIds.Length > 0)
                {
                    sbEventIds = sbEventIds.Remove(sbEventIds.Length - 1, 1);
                }
                string str = string.Format("iEventID in ({0})", sbEventIds.ToString());
                package.Content.AddParam((byte)OpOptionDef.DIYSql);
                package.Content.AddParam(str);
            }
        }

        protected virtual List<int> getNeedEventIdList()
        {
            List<int> evtIdList = new List<int>();
            if (evtIDSvrIDDic != null && evtIDSvrIDDic.Count > 0)
            {
                evtIdList = new List<int>(evtIDSvrIDDic.Keys);
            }
            return evtIdList;
        }

        private string createDataKey(FileInfo fi, out List<string> values)
        {
            CQTSiteCellInfo siteCellInfo = CQTSiteCellMnger.Instance.GetSiteInfo(fi.AreaID);
            values = new List<string>();
            StringBuilder key = new StringBuilder();
            for (int i = 0; i < rptTemplate.KeyColNum; i++)
            {
                Column col = rptTemplate.Columns[i];
                foreach (string keyName in Enum.GetNames(typeof(KeyWords)))
                {
                    if (keyName == col.Formula)
                    {
                        string tmp= getValueByKey((KeyWords)Enum.Parse(typeof(KeyWords), keyName)
                            , siteCellInfo, fi);
                        key.Append("[#]" + tmp);
                        values.Add(tmp);
                        break;
                    }
                }
            }
            return key.ToString();
        }

        private string getValueByKey(KeyWords key,CQTSiteCellInfo siteCellInfo, FileInfo fi)
        {
            string value = string.Empty;
            switch (key)
            {
                case KeyWords.DistrictName:
                    value = siteCellInfo.DistrictName;
                    break;
                case KeyWords.SiteName:
                    value = siteCellInfo.SiteName;
                    break;
                case KeyWords.TestDate:
                    value = JavaDate.GetDateTimeFromMilliseconds(fi.BeginTime * 1000L).Date.ToString("yyyy-MM-dd");
                    break;
                case KeyWords.CellName:
                    value = siteCellInfo.CellName;
                    break;
                case KeyWords.CGI:
                    value = siteCellInfo.CGI;
                    break;
                default:
                    break;
            }
            return value;
        }

        Dictionary<string, SiteStatInfo> keyStatDic = null;
        protected override void recieveAndHandleSpecificStatData(Package package
            , List<StatImgDefItem> curImgColumnDef
            , KPIStatDataBase singleStatData)
        {
            package.Content.GetParamInt();//areaType
            package.Content.GetParamInt();//areaID
            fillStatData(package, curImgColumnDef, singleStatData);

            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);
            List<string> values = null;
            string key = createDataKey(fi, out values);

            SiteStatInfo statInfo = null;
            if (!this.keyStatDic.TryGetValue(key, out statInfo))
            {
                statInfo = new SiteStatInfo();
                statInfo.Keys = values;
                this.keyStatDic[key] = statInfo;
            }
            statInfo.TestDate = JavaDate.GetDateTimeFromMilliseconds(fi.BeginTime * 1000L).Date.ToString("yyyy-MM-dd");
            statInfo.KPIData.AddStatData(fi, singleStatData, false);
        }

        protected override void handleStatEvent(Event evt)
        {
            StatDataEvent singleStatData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);

            List<string> values = null;
            string key = createDataKey(fi, out values);

            SiteStatInfo statInfo = null;
            if (!this.keyStatDic.TryGetValue(key, out statInfo))
            {
                statInfo = new SiteStatInfo();
                statInfo.Keys = values;
                this.keyStatDic[key] = statInfo;
            }
            statInfo.KPIData.AddStatData(fi, singleStatData, false);
        }

       

    }
}
