﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FileDistributeResultForm : MinCloseForm
    {
        public FileDistributeResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
        }

        public void FillData(List<DistributeFileItem> resultList)
        {
            List<ResultView> resultViews = new List<ResultView>();
            foreach (DistributeFileItem item in resultList)
            {
                resultViews.Add(new ResultView(item));
            }

            gridControl1.DataSource = resultViews;
            gridControl1.RefreshDataSource();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private class ResultView
        {
            public ResultView(DistributeFileItem fileItem)
            {
                FileName = fileItem.FileInfo.Name;
                TargetCityName = fileItem.TargetCityName;
                ResultDesc = fileItem.ResultDesc;
                SrcSavePath = fileItem.SourceFtpPath;
                TarSavePath = fileItem.TargetFtpPath;
                Tag = fileItem;
            }

            public string FileName
            {
                get;
                private set;
            }

            public string TargetCityName
            {
                get;
                private set;
            }

            public string ResultDesc
            {
                get;
                private set;
            }

            public string SrcSavePath
            {
                get;
                private set;
            }

            public string TarSavePath
            {
                get;
                private set;
            }

            public object Tag
            {
                get;
                set;
            }
        }
    }
}
