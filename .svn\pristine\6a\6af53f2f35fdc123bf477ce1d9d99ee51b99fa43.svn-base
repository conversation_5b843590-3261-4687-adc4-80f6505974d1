﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTECellSetByFileForm : MinCloseForm
    {

        public LTECellSetByFileForm()
        {
            InitializeComponent();
        }

        public void FillData(Dictionary<string, Dictionary<LTECell, LteCellInfo>> fileLteCellInfoDic
            , Dictionary<string, Dictionary<string, LteCellInfo>> fileNotFindCellInfoDic)
        {
            BindingSource source = new BindingSource();
            List<LteCellInfo> lcInfoLst = new List<LteCellInfo>();
            int sn = 1;
            foreach (Dictionary<LTECell, LteCellInfo> dic in fileLteCellInfoDic.Values)
            {
                foreach (LteCellInfo lcInfo in dic.Values)
                {
                    lcInfo.SN = sn++;
                    lcInfoLst.Add(lcInfo);
                }
            }
            foreach (Dictionary<string, LteCellInfo> dic in fileNotFindCellInfoDic.Values)
            {
                foreach (LteCellInfo lcInfo in dic.Values)
                {
                    lcInfo.SN = sn++;
                    lcInfo.Cellname = "";//匹配不上的小区名称显示为空
                    lcInfoLst.Add(lcInfo);
                }
            }

            source.DataSource = lcInfoLst;
            this.gridControl1.DataSource = source;
            this.gridControl1.RefreshDataSource();
        }

        public void FillData(Dictionary<LTECell, LteFddCellInfo> lteCellInfoDic
            , Dictionary<string, LteFddCellInfo> notFindCellInfoDic)
        {
            BindingSource source = new BindingSource();
            List<LteFddCellInfo> lcInfoLst = new List<LteFddCellInfo>();
            int sn = 1;
            foreach (LteFddCellInfo lcInfo in lteCellInfoDic.Values)
            {
                lcInfo.SN = sn++;
                lcInfoLst.Add(lcInfo);
            }
            foreach (LteFddCellInfo lcInfo in notFindCellInfoDic.Values)
            {
                lcInfo.SN = sn++;
                lcInfoLst.Add(lcInfo);
            }
            source.DataSource = lcInfoLst;
            this.gridControl1.DataSource = source;
            this.gridControl1.RefreshDataSource();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridView1);
        }

        public class CellSetResultData
        {
            public string fileName { get; set; }
            public long totalCount { get; set; }
        }

        private void miAddCellProp_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            dlg.RestoreDirectory = true;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                string fileName = dlg.FileName;
                DevExpress.XtraGrid.Columns.GridColumn col = new DevExpress.XtraGrid.Columns.GridColumn();
                col.Name = "CellProperty1";
                col.FieldName = "CellProperty";
                col.Visible = true;
                col.Caption = "属性";
                col.Width = 80;
                col.VisibleIndex = 8;
                bool isContians = false;
                foreach (DevExpress.XtraGrid.Columns.GridColumn cl in gridView1.Columns)
                {
                    if (cl.Name == col.Name)
                    {
                        isContians = true;
                        break;
                    }
                }
                if (!isContians)
                {
                    gridView1.Columns.Add(col);
                }
                ExcelNPOIReader reader = new ExcelNPOIReader(fileName);
                ExcelNPOITable tb = reader.GetTable();   
                if (tb.CellValues == null)
                {
                    return;
                }
                for (int i = 0; i < gridView1.DataRowCount; i++)
                {
                    updategridView(tb, i);
                }
            }
        }

        private void updategridView(ExcelNPOITable tb, int i)
        {
            gridView1.SetRowCellValue(i, gridView1.Columns["CellProperty"], " ");
            string tac = gridView1.GetRowCellValue(i, gridView1.Columns["Tac"]).ToString();
            string eci = gridView1.GetRowCellValue(i, gridView1.Columns["Eci"]).ToString();
            for (int j = 0; j < tb.CellValues.Count; j++)
            {
                if (tb.CellValues[j].GetValue(0) != null && tb.CellValues[j].GetValue(1) != null && tb.CellValues[j].GetValue(2) != null)
                {
                    string tbTAC = tb.CellValues[j].GetValue(0).ToString();
                    string tbECI = tb.CellValues[j].GetValue(1).ToString();
                    if (tac == tbTAC && eci == tbECI)
                    {
                        gridView1.SetRowCellValue(i, gridView1.Columns["CellProperty"], tb.CellValues[j].GetValue(2).ToString());
                        gridView1.UpdateCurrentRow();
                        break;
                    }
                }
            }
        }
    }
}
