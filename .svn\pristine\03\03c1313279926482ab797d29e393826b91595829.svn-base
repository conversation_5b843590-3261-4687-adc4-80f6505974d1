﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class UnstabitilyCoverPnl : UserControl
    {
        public UnstabitilyCoverPnl()
        {
            InitializeComponent();
        }

        UnstabitilyCover mainReason = null;
        public void LinkCondition(UnstabitilyCover reason)
        {
            this.mainReason = reason;

            numRSRPDiff.Value = (decimal)reason.RSRPDiff;
            numRSRPDiff.ValueChanged += numRSRPDiff_ValueChanged;

            numSecond.Value = (decimal)reason.Second;
            numSecond.ValueChanged += numSecond_ValueChanged;
        }

        void numSecond_ValueChanged(object sender, EventArgs e)
        {
            mainReason.Second = (int)numSecond.Value;
        }

        void numRSRPDiff_ValueChanged(object sender, EventArgs e)
        {
            mainReason.RSRPDiff = (float)numRSRPDiff.Value;
        }

    }

}
