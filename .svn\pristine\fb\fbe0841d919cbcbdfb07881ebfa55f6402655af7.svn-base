﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class SetThresholdForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.numUDUnRxlevThreshold = new System.Windows.Forms.NumericUpDown();
            this.buttonOK = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numUDWkRxlevThreshold = new System.Windows.Forms.NumericUpDown();
            ((System.ComponentModel.ISupportInitialize)(this.numUDUnRxlevThreshold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numUDWkRxlevThreshold)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.Location = new System.Drawing.Point(29, 53);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(137, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "无覆盖：最强信号RSCP <";
            // 
            // numUDUnRxlevThreshold
            // 
            this.numUDUnRxlevThreshold.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numUDUnRxlevThreshold.Location = new System.Drawing.Point(171, 47);
            this.numUDUnRxlevThreshold.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numUDUnRxlevThreshold.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numUDUnRxlevThreshold.Name = "numUDUnRxlevThreshold";
            this.numUDUnRxlevThreshold.Size = new System.Drawing.Size(82, 21);
            this.numUDUnRxlevThreshold.TabIndex = 1;
            this.numUDUnRxlevThreshold.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numUDUnRxlevThreshold.Value = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            this.numUDUnRxlevThreshold.ValueChanged += new System.EventHandler(this.numUDUnRxlevThreshold_ValueChanged);
            // 
            // buttonOK
            // 
            this.buttonOK.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonOK.Location = new System.Drawing.Point(105, 93);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(75, 23);
            this.buttonOK.TabIndex = 2;
            this.buttonOK.Text = "确定";
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // button1
            // 
            this.button1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.button1.Location = new System.Drawing.Point(202, 93);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(75, 23);
            this.button1.TabIndex = 3;
            this.button1.Text = "取消";
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label2.Location = new System.Drawing.Point(254, 54);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(23, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "dBm";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label3.Location = new System.Drawing.Point(29, 23);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(137, 12);
            this.label3.TabIndex = 5;
            this.label3.Text = "弱覆盖：最强信号RSCP <";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label4.Location = new System.Drawing.Point(254, 23);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(23, 12);
            this.label4.TabIndex = 6;
            this.label4.Text = "dBm";
            // 
            // numUDWkRxlevThreshold
            // 
            this.numUDWkRxlevThreshold.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numUDWkRxlevThreshold.Location = new System.Drawing.Point(171, 18);
            this.numUDWkRxlevThreshold.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numUDWkRxlevThreshold.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numUDWkRxlevThreshold.Name = "numUDWkRxlevThreshold";
            this.numUDWkRxlevThreshold.Size = new System.Drawing.Size(82, 21);
            this.numUDWkRxlevThreshold.TabIndex = 7;
            this.numUDWkRxlevThreshold.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numUDWkRxlevThreshold.ThousandsSeparator = true;
            this.numUDWkRxlevThreshold.Value = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.numUDWkRxlevThreshold.ValueChanged += new System.EventHandler(this.numUDWkRxlevThreshold_ValueChanged);
            // 
            // SetThresholdForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.Control;
            this.ClientSize = new System.Drawing.Size(302, 133);
            this.Controls.Add(this.numUDWkRxlevThreshold);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.buttonOK);
            this.Controls.Add(this.numUDUnRxlevThreshold);
            this.Controls.Add(this.label1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "SetThresholdForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "弱/无覆盖分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numUDUnRxlevThreshold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numUDWkRxlevThreshold)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numUDUnRxlevThreshold;
        private System.Windows.Forms.Button buttonOK;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numUDWkRxlevThreshold;
    }
}