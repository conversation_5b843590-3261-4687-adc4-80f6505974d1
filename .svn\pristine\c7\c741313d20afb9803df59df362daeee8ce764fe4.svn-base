﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTLteTestAcceptance
{
    public partial class LteTestSettingForm : BaseDialog
    {
        public LteTestSettingForm(string strFormText)
            : this()
        {
            this.Text = strFormText;
        }
        public LteTestSettingForm()
        {
            InitializeComponent();
            this.btnOK.Click += BtnOK_Click;
            this.btnCancel.Click += BtnCancel_Click;
            this.btnFolder.Click += BtnFolder_Click;
        }

        public LteTestAcceptCondition GetCondition()
        {
            LteTestAcceptCondition cond = new LteTestAcceptCondition();
            cond.SaveFolder = txtFolder.Text;
            return cond;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtFolder.Text))
            {
                MessageBox.Show("请选择导出文件保存目录!", this.Text, 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = System.Windows.Forms.DialogResult.None;
                return;
            }

            DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void BtnFolder_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                txtFolder.Text = dlg.SelectedPath;
            }
        }
    }

    public class LteTestAcceptCondition
    {
        public bool IsByFile
        {
            get;
            set;
        }

        public bool IsByDT
        {
            get;
            set;
        }

        public string SaveFolder
        {
            get;
            set;
        }
    }
}
