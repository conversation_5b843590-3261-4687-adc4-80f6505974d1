﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class ChartShowDialogKPI : Form
    {
        private KPIInfoPanel infoPanel;
        Steema.TeeChart.TChart teeChart = null;
        public ChartShowDialogKPI(KPIInfoPanel infoPanel)
        {
            InitializeComponent();
            this.infoPanel = infoPanel;
        }

        private void ChartShowDialog_FormClosing(object sender, FormClosingEventArgs e)
        {
            this.Controls.Remove(teeChart);
            infoPanel.ReturnTChartControl(teeChart);
        }

        internal void AppendTeeChart(Steema.TeeChart.TChart tchart)
        {
            this.Controls.Add(tchart);
            this.teeChart = tchart;
            teeChart.Dock = DockStyle.Fill;
        }
    }
}