﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class RangeSetting : Form
    {
        public RangeSetting()
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            spinEditMin.Value = 0;
            spinEditMax.Value = 100;
        }

        public CRange GetRange()
        {
            return new CRange((int)spinEditMin.Value, (int)spinEditMax.Value);
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void spinEditMin_ValueChanged(object sender, EventArgs e)
        {
            if (spinEditMin.Value > spinEditMax.Value)
            {
                spinEditMin.Value = (int)spinEditMax.Value;
            }
        }

        private void spinEditMax_ValueChanged(object sender, EventArgs e)
        {
            if (spinEditMax.Value < spinEditMin.Value)
            {
                spinEditMax.Value = (int)spinEditMin.Value;
            }
        }
    }

    public class CRange
    {
        public int IMin { get; set; }
        public int IMax { get; set; }

        public CRange()
        {
        }

        public CRange(int iMin, int iMax)
        {
            this.IMin = iMin;
            this.IMax = iMax;
        }

        public override string ToString()
        {
            return "[" + IMin.ToString() + "," + IMax.ToString() + "]";
        }
    }
}
