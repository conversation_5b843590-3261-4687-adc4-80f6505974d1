﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRModRoadQuery : ModRoadQueryBase
    {
        public NRModRoadQuery(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "NR模间干扰道路"; }
        }

        public override List<string> Columns
        {
            get
            {
                List<string> columns = NRTpHelper.InitBaseReplayParamBackground(false, true);
                columns.Add("isampleid");
                columns.Add("itime");
                columns.Add("ilongitude");
                columns.Add("ilatitude");
                columns.Add("NR_SS_RSRQ");
                columns.Add("NR_SS_RSSI");
                columns.Add("NR_NCell_RSRQ");
                columns.Add("NR_NCell_RSSI");
                columns.Add("NR_Throughput_PDCP_DL_Mb");
                columns.Add("NR_Throughput_PDCP_UL_Mb");
                return columns;
            }
        }

        public NRModRoadCondition cond { get; set; }
        private NRModRoadSettingForm setForm;
        private NRModRoadResultForm resultForm { get; set; }

        protected NRModRoadStater stater;
        public override ModRoadStaterBase Stater
        {
            get { return this.stater; }
        }

        public override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35018, "NR模间干扰道路");
        }

        public override bool isValidCondition()
        {
            if (mainModel.IsBackground || mainModel.QueryFromBackground)
            {
                stater = new NRModRoadStater(mainModel, cond);
                return true;
            }

            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new NRModRoadSettingForm();
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }

            cond = setForm.GetCondition();
            stater = new NRModRoadStater(mainModel, cond);
            curRoad = null;
            return true;
        }

        protected override ModRoadItemBase CreateNewRoad(TestPoint firstPoint, string fileName)
        {
            return new NRModRoadItem(firstPoint, fileName, cond);
        }

        public override void FireShowForm()
        {
            mainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");

            resultForm = mainModel.GetObjectFromBlackboard(typeof(NRModRoadResultForm).FullName) as NRModRoadResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new NRModRoadResultForm(mainModel);
            }
            resultForm.FillData(stater, cond);
            if (!resultForm.Visible)
            {
                resultForm.Show(mainModel.MainForm);
            }
        }
    }

    public class NRModRoadQueryByFile : ModRoadQueryByFile
    {
        public NRModRoadQueryByFile(MainModel mainModel)
               : base(mainModel, new NRModRoadQuery(mainModel))
        {
        }
    }

    public class NRModRoadQueryByRegion : ModRoadQueryByRegion
    {
        public NRModRoadQueryByRegion(MainModel mainModel)
          : base(mainModel, new NRModRoadQuery(mainModel))
        {
            queryer = new NRModRoadQuery(mainModel);
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        protected static readonly object lockObj = new object();
        private static NRModRoadQueryByRegion intance = null;
        public static NRModRoadQueryByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRModRoadQueryByRegion(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public NRModRoadCondition ModRoadCond
        {
            get
            {
                NRModRoadQuery modQuery = queryer as NRModRoadQuery;
                if (modQuery.cond == null)
                {
                    modQuery.cond = new NRModRoadCondition();
                }
                return modQuery.cond;
            }
        }
    }
}
