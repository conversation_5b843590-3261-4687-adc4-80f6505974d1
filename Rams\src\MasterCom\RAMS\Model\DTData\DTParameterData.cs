using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Model
{
    public class DTParameterDataMap
    {
        //private object[] valueArr;
        //private void setValue(DTParameter p, object value)
        //{
        //    int idx = DataParamInfoHolder.Instance.Add(p);
        //    if (valueArr == null)
        //    {
        //        valueArr = new object[idx + 1];
        //    }
        //    else if (valueArr.Length <= idx)
        //    {
        //        object[] t = new object[idx + 1];
        //        Array.Copy(valueArr, t, valueArr.Length);
        //        valueArr = t;
        //    }

        //    if (p.Info.ArrayBounds > 1)
        //    {
        //        object[] obj = valueArr[idx] as object[];
        //        if (obj == null)
        //        {
        //            obj = new object[p.ArrayIndex + 1];
        //            valueArr[idx] = obj;
        //        }
        //        else if (obj.Length <= p.ArrayIndex)
        //        {
        //            object[] t = new object[p.ArrayIndex + 1];
        //            Array.Copy(obj, t, obj.Length);
        //            obj = t;
        //            valueArr[idx] = obj;
        //        }
        //        obj[p.ArrayIndex] = value;
        //    }
        //    else
        //    {
        //        valueArr[idx] = value;
        //    }
        //}

        //private object getValue(DTParameter p)
        //{
        //    int idx = DataParamInfoHolder.Instance.GetIdx(p);
        //    if (idx == -1)
        //    {
        //        return null;
        //    }
        //    object val = null;
        //    if (valueArr.Length<=idx)
        //    {
        //        return null;
        //    }
        //    val = valueArr[idx];
        //    if (val is object[] && p.Info.ArrayBounds > 1)
        //    {
        //        object[] arr = val as object[];
        //        if (arr != null && arr.Length > p.ArrayIndex)
        //        {
        //            return arr[p.ArrayIndex];
        //        }
        //        else
        //        {
        //            return null;
        //        }
        //    }
        //    return val;
        //}

        public object this[DTParameter parameter]
        {
            get
            {
                //object ret = getValue(parameter);
                //return ret;
                if (Map.TryGetValue(parameter, out object res))
                {
                    return res;
                }
                return null;
            }
            set
            {
                if (parameter == null)
                {
                    return;
                }
               
                switch (parameter.Info.ValueType)
                {
                    case DTParameterValueType.Byte:
                        setByte(parameter, value);
                        break;
                    case DTParameterValueType.Short:
                        setShort(parameter, value);
                        break;
                    case DTParameterValueType.Int:
                        setInt(parameter, value);
                        break;
                    case DTParameterValueType.Long:
                        switch (value)
                        {
                            case long _:
                                Map[parameter] = value;
                                return;
                        }
                        break;
                    case DTParameterValueType.Float:
                        setFloat(parameter, value);
                        break;
                    case DTParameterValueType.Double:
                        switch (value)
                        {
                            case Double _:
                                Map[parameter] = value;
                                return;
                        }
                        break;
                    case DTParameterValueType.String:
                        switch (value)
                        {
                            case String _:
                                Map[parameter] = value;
                                return;
                        }
                        break;
                    case DTParameterValueType.UInt64:
                        setUInt64(parameter, value);
                        break;
                    default:
                        Map[parameter] = value;
                        break;
                }
            }
        }

        private void setByte(DTParameter parameter, object value)
        {
            switch (value)
            {
                case byte _:
                    Map[parameter] = value;
                    return;
                case int intData:
                    Map[parameter] = (byte)intData;
                    return;
                case short shortData:
                    Map[parameter] = (byte)shortData;
                    return;
                case float floatData:
                    Map[parameter] = (byte)floatData;
                    return;
            }
        }

        private void setShort(DTParameter parameter, object value)
        {
            switch (value)
            {
                case short _:
                    Map[parameter] = value;
                    return;
                case byte byteData:
                    Map[parameter] = (short)byteData;
                    return;
                case int intData:
                    Map[parameter] = (short)intData;
                    return;
                case float floatData:
                    Map[parameter] = (short)floatData;
                    return;
            }
        }

        private void setInt(DTParameter parameter, object value)
        {
            switch (value)
            {
                case int _:
                case ulong _:
                case long _:
                    Map[parameter] = value;
                    return;
                case float floatData:
                    Map[parameter] = (int)floatData;
                    return;
                case byte byteData:
                    Map[parameter] = (int)byteData;
                    return;
                case short shortData:
                    Map[parameter] = (int)shortData;
                    return;
            }
        }

        private void setFloat(DTParameter parameter, object value)
        {
            switch (value)
            {
                case float _:
                    Map[parameter] = value;
                    return;
                case byte byteData:
                    Map[parameter] = (float)byteData;
                    return;
                case short shortData:
                    Map[parameter] = (float)shortData;
                    return;
                case int intData:
                    Map[parameter] = (float)intData;
                    return;
            }
        }

        private void setUInt64(DTParameter parameter, object value)
        {
            switch (value)
            {
                case UInt64 _:
                    Map[parameter] = value;
                    return;
                case int intData:
                    Map[parameter] = intData;
                    return;
            }
        }

        public object this[String name, int arrayIndex]
        {
            get { return this[DTParameterManager.GetInstance().GetParameter(name, arrayIndex)]; }
            set { this[DTParameterManager.GetInstance().GetParameter(name, arrayIndex)] = value; }
        }

        public object this[String name]
        {
            get { return this[name, 0]; }
            set { this[name, 0] = value; }
        }

        public Dictionary<DTParameter, object> Map { get; set; } = new Dictionary<DTParameter, object>();
    }
}
