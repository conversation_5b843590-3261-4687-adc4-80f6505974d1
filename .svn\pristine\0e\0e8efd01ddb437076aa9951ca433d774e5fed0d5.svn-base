﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using Microsoft.Office.Interop.Excel;
using System.Drawing;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class ExcelOpHelper
    {
        private static object olock = new object();

        private static ExcelOpHelper instance = null;

        private string virtualFile = string.Empty;

        private Microsoft.Office.Interop.Excel.Application app = null;
        private Microsoft.Office.Interop.Excel.Workbook workbook = null;
        private Microsoft.Office.Interop.Excel.Worksheet worksheet = null;

        private readonly object MissingObj = Type.Missing;

        public static ExcelOpHelper Instance
        {
            get
            {
                if (instance == null)
                {
                    lock (olock)
                    {
                        if (instance == null)
                        {
                            instance = new ExcelOpHelper();
                        }
                    }
                }
                return instance;
            }
        }

        private ExcelOpHelper() { }

        private List<string> sheetNames = null;
        public List<string> SheetNames
        {
            get
            {
                if (sheetNames != null)
                    return sheetNames;

                sheetNames = new List<string>();
                foreach (Worksheet sheet in app.Sheets)
                {
                    sheetNames.Add(sheet.Name);
                }
                return sheetNames;
            }
        }

        public int RowCount
        {
            get
            {
                if (worksheet == null)
                    return -1;

                return worksheet.UsedRange.Rows.Count;
            }
        }

        public int ColumnCount
        {
            get
            {
                if (worksheet == null)
                    return -1;

                return worksheet.UsedRange.Columns.Count;
            }
        }

        public bool Load(string filePath)
        {
            try
            {
                Release();

                if (!File.Exists(filePath))
                    return false;

                virtualFile = FileOpHelper.CreateVirtualFile(filePath);
                if (!File.Exists(virtualFile))
                    return false;

                app = new Microsoft.Office.Interop.Excel.Application();
                app.Visible = false;

                workbook = app.Workbooks.Open(virtualFile, 
                    MissingObj, MissingObj, MissingObj, MissingObj, MissingObj, MissingObj, MissingObj,
                    MissingObj, MissingObj, MissingObj, MissingObj, MissingObj, MissingObj, MissingObj);

                worksheet = (Microsoft.Office.Interop.Excel.Worksheet)app.ActiveSheet;

                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool SetActiveSheet(string sheetName)
        {
            Worksheet sheet;
            if (!ContainSheet(sheetName, out sheet))
                return false;

            worksheet = sheet;
            return true;
        }

        public bool ContainSheet(string sheetName, out Worksheet oSheet)
        {
            oSheet = null;
            foreach (Worksheet sheet in app.Sheets)
            {
                if (sheet.Name.Equals(sheetName))
                {
                    oSheet = sheet;
                    break;
                }
            }
            return oSheet != null;
        }

        public List<XlsRow> GetRows(string sheetName)
        {
            List<XlsRow> rows = new List<XlsRow>();

            if (!SetActiveSheet(sheetName))
                return rows;

            //获取单元格内容
            for (int irow = 1; irow <= RowCount;irow++ )
            {
                XlsRow row = new XlsRow(irow);
                for (int icol = 1; icol <= ColumnCount;icol++ )
                {
                    Range rng = (Range)worksheet.Cells[irow, icol];

                    XlsCell cell = new XlsCell(icol);
                    if (rng.Interior.Color is double)
                    {
                        cell.CellBakColor = ColorTranslator.FromOle((int)(double)rng.Interior.Color);
                    }
                    if (rng.Font.Color is double)
                    {
                        cell.CellFontColor = ColorTranslator.FromOle((int)(double)rng.Font.Color);
                    }
                    cell.CellValue = rng.Value2 == null ? string.Empty : rng.Value2.ToString();

                    row.AddCell(cell);
                }
                rows.Add(row);
            }
            return rows;
        }

        public bool Release()
        {
            try
            {
                sheetNames = null;

                if (app != null)
                {
                    app.Quit();
                }

                releaseComObject(workbook);
                releaseComObject(worksheet);
                releaseComObject(app);

                workbook = null;
                worksheet = null;
                app = null;

                FileOpHelper.Delete(virtualFile);

                return true;
            }
            catch
            {
                return false;
            }

        }

        private void releaseComObject(object o)
        {
            if (o != null)
                System.Runtime.InteropServices.Marshal.ReleaseComObject(o);
        }
    }

    public class XlsRow
    {
        public int RowIndex { get; set; }
        public List<XlsCell> Cells { get; set; }

        public XlsRow(int rowIdx)
        {
            this.RowIndex = rowIdx;
            this.Cells = new List<XlsCell>();
        }

        public void AddCell(XlsCell cell)
        {
            Cells.Add(cell);
        }
    }

    public class XlsCell
    {
        public int CellIndex { get; set; }
        public Color CellFontColor { get; set; }
        public Color CellBakColor { get; set; }
        public string CellValue { get; set; }

        public XlsCell(int cellIdx)
        {
            this.CellIndex = cellIdx;
            this.CellFontColor = Color.Empty;
            this.CellBakColor = Color.Empty;
            this.CellValue = string.Empty;
        }
    }
}
