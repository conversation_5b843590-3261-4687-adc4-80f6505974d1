﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.MControls
{
    public partial class ColorRangeScreen : UserControl
    {
        bool rangeMode = true;
        public bool RangeMode
        {
            get { return rangeMode; }
            set { rangeMode = value; }
        }
        public List<ColorRange> ColorRanges
        {
            get { return colorRanges; }
            set { colorRanges = value; }
        }
        [NonSerialized]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        private List<ColorRange> colorRanges = new List<ColorRange>();
        public ColorRangeScreen()
        {
            InitializeComponent();
        }
        Font font = new Font("宋体", 9);
        Brush fontBrush = new SolidBrush(Color.Black);
        Pen tickPan = new Pen(new SolidBrush(Color.DarkBlue));
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            Graphics g = e.Graphics;

            float minR;
            float maxR;
            if(colorRanges.Count==0)
            {
                return;
            }
            if(rangeMode)
            {
                minR = colorRanges[0].minValue;
                maxR = colorRanges[colorRanges.Count - 1].maxValue;
                float span = maxR - minR;
                if (span <= 0)
                {
                    return;
                }
                int x0 = 0;
                int w = this.Width - 40;
                int h = this.Height / 2;
                foreach (ColorRange cr in colorRanges)
                {
                    float xpos = x0 + (cr.minValue - minR) * w / span;
                    float w1 = (cr.maxValue - cr.minValue) * w / span;
                    g.FillRectangle(new SolidBrush(cr.color), xpos, 0, w1, h);
                    g.DrawString(string.Format("{0:F1}", cr.minValue), font, fontBrush, xpos, h);
                    g.DrawLine(tickPan, xpos, h, xpos, 1.2f * h);
                }
                ColorRange cr2 = colorRanges[colorRanges.Count - 1];
                float xpos2 = x0 + (cr2.minValue - minR) * w / span;
                float w2 = (cr2.maxValue - cr2.minValue) * w / span;
                g.DrawString(string.Format("{0:F1}", cr2.maxValue), font, fontBrush, xpos2 + w2, h);
                g.DrawLine(tickPan, xpos2 + w2, h, xpos2 + w2, 1.2f * h);
            }
            else
            {
                int w = this.Width - 40;
                int h = this.Height-2;
                //g.DrawString("字典色",font,fontBrush,0,3);
                int x0 = 1;
                int w0 = (w - x0) / 7;
                for(int i=0;i<7;i++)
                {
                    g.FillRectangle(new SolidBrush(getColor(i)), x0+w0*i, 0, w0, h);
                    g.DrawRectangle(tickPan, x0 + w0 * i, 0, w0, h);
                }
                g.DrawString("...", font, fontBrush, x0 + w0 * 7+5, 2);
            }
           
        }
        //
        #region From RAMS/MasterCom.Util.ColorSequenceSupplier
        
        public static Color[] paintSequenceColors = new Color[]{
                  Color.Blue,
                 Color.Yellow,
                 Color.Green,
                 Color.Cyan,
                 Color.Maroon,
                 Color.Olive,
                 Color.Firebrick,
                 Color.SkyBlue,

                 Color.AliceBlue,
                 Color.AntiqueWhite,
                 Color.Aqua,
                 Color.Aquamarine,
                 Color.Azure,
                 Color.Beige,
                 Color.Bisque,
                 Color.Black,
                 Color.BlanchedAlmond,
                 Color.BlueViolet,
                 Color.Brown,
                 Color.BurlyWood,
                 Color.CadetBlue,
                 Color.Chartreuse,
                 Color.Chocolate,
                 Color.Coral,
                 Color.CornflowerBlue,
                 Color.Cornsilk,
                 Color.Crimson
            };
        public static Color getColor(int idx)
        {
            return paintSequenceColors[idx % paintSequenceColors.Length];
        }
        #endregion
    }
}
