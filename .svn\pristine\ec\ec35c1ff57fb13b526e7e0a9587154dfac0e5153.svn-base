﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryFddActualData : DiyQueryFddDataBase
    {
        private List<FddActualBtsCellData> actualBtsCellInfo = null;
        public List<FddActualBtsCellData> ActualBtsCellInfo
        {
            get { return actualBtsCellInfo; }
        }

        public DiyQueryFddActualData()
            : base()
        { }

        public override string Name
        {
            get
            {
                return "查询FDD单验实测数据";
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat(@"SELECT [TAC],[EnodebID],[CellID],[PCI],[频段],[主频点],[小区带宽],[根序列],[RsPower],[天线挂高],[预制电下倾角],[PDCCH符号数],[VOLTE开关],[SRVCC开关],[语音业务A2启动门限],[语音业务B2启动门限],[传输带宽],[小区载波配置] FROM tb_xinjiang_cellparam_test where [EnodebID]={0} and [站点名称]='{1}'", btsID, btsName);
            if (btstype == LTEBTSType.Outdoor)
            {
                selectSQL.Append(" order by [CellID]");
            }
            else
            {
                selectSQL.Append(" order by [PCI]");
            }
            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[18];
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void initData()
        {
            actualBtsCellInfo = new List<FddActualBtsCellData>();
        }

        protected override void dealReceiveData(Package package)
        {
            FddActualBtsCellData btsCell = new FddActualBtsCellData();
            btsCell.FillData(package);
            actualBtsCellInfo.Add(btsCell);
        }
    }

    public class FddActualBtsCellData : BtsCellData
    {
        #region 小区信息
        public int CellID { get; private set; }

        protected string carrierInfo;
        /// <summary>
        /// 载波配置
        /// </summary>
        public string CarrierInfo
        {
            get 
            {
                if (carrierInfo == "1")
                {
                    return "S111";
                }
                else
                {
                    return "";
                }
            }
        }
        
        public int PCI { get; private set; }

        protected string frequencyBand;
        /// <summary>
        /// 频段
        /// </summary>
        public string FrequencyBand
        {
             get 
            {
                if (string.IsNullOrEmpty(frequencyBand))
                {
                    return "";
                }
                else
                {
                    return "Band" + frequencyBand; 
                }
            }
        }
        
        /// <summary>
        /// 主频点
        /// </summary>
        public int Earfcn { get; private set; }

        protected string cellBroadBand;
        /// <summary>
        /// 小区宽带
        /// </summary>
        public string CellBroadBand
        {
            get 
            {
                if (string.IsNullOrEmpty(cellBroadBand))
                {
                    return "";
                }
                else
                {
                    return cellBroadBand + "M"; 
                }
            }
        }
        
        /// <summary>
        /// 根序列
        /// </summary>
        public int RootSN { get; private set; }
        /// <summary>
        /// 挂高
        /// </summary>
        public float Altitude { get; private set; }
        /// <summary>
        /// 电气下倾角
        /// </summary>
        public int Downtilt { get; private set; }
        #endregion

        protected int rsPower;
        public string RsPower
        {
            get 
            {
                if (rsPower != 0)
                {
                    return (rsPower + 0.2d).ToString();
                }
                return "";
            }
        }
        
        public short PA { get; private set; }
        public short PB { get; private set; }
        /// <summary>
        /// PDCCH符号数
        /// </summary>
        public int PDCCH { get; private set; }
        /// <summary>
        /// VOLTE开关
        /// </summary>
        public string VOLTE { get; private set; }
        /// <summary>
        /// SRVCC开关
        /// </summary>
        public string SRVCC { get; private set; }
        /// <summary>
        /// 语音业务A2启动门限
        /// </summary>
        public string A2Threshold { get; private set; }
        /// <summary>
        /// 语音业务A4触发门限
        /// </summary>
        public string A4Threshold { get; private set; }
        /// <summary>
        /// 传输宽带
        /// </summary>
        public int WideBand { get; private set; }

        public override void FillData(Package package)
        {
            TAC = package.Content.GetParamInt();
            ENodeBID = package.Content.GetParamInt();
            CellID = package.Content.GetParamInt();
            PCI = package.Content.GetParamInt();
            frequencyBand = package.Content.GetParamString();
            Earfcn = package.Content.GetParamInt();
            cellBroadBand = package.Content.GetParamInt().ToString();
            RootSN = package.Content.GetParamInt();
            rsPower = package.Content.GetParamInt() / 1000;
            Altitude = package.Content.GetParamFloat();
            Downtilt = package.Content.GetParamInt();
            PDCCH = package.Content.GetParamInt();
            VOLTE = package.Content.GetParamString();
            SRVCC = package.Content.GetParamString();
            A2Threshold = package.Content.GetParamString();
            A4Threshold = package.Content.GetParamString();
            WideBand = package.Content.GetParamInt();
            carrierInfo = package.Content.GetParamString();
        }
    }
}
