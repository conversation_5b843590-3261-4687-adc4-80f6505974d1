﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanMultiCoverageGrid : DIYSampleByRegion
    {
        public NRScanMultiCoverageGrid()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRScan);
        }

        private static NRScanMultiCoverageGrid intance = null;
        protected static readonly object LockObj = new object();
        public static NRScanMultiCoverageGrid GetInstance()
        {
            if (intance == null)
            {
                lock (LockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRScanMultiCoverageGrid();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "重叠覆盖栅格信息_NR扫频"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36007, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            return NRTpHelper.InitNrScanParamSample(NRTpHelper.NrScanTpManager.RsrpThemeName);
        }

        private NRScanMultiCoverageGridDlg condForm = null;
        private NRScanMultiCoverageGridCond curCondition { get; set; }
        protected override bool isValidCondition()
        {
            if (condForm == null)
            {
                condForm = new NRScanMultiCoverageGridDlg();
            }
            condForm.SetCondition(curCondition);
            if (condForm.ShowDialog() == DialogResult.OK)
            {
                curCondition = condForm.GetCondition();
                return true;
            }
            return false;
        }

        ZTAntennaBase antBase = null;

        public Dictionary<string, NRScanMultiCoverageGridInfo> dicResult { get; set; }
        protected override void query()
        {
            //地图匹配处理
            antBase = new ZTAntennaBase(MainModel);
            antBase.InitRegionMop2();

            dicResult = new Dictionary<string, NRScanMultiCoverageGridInfo>();
            base.query();
            WaitBox.Show("正在统计栅格小区信息...", doWithMgrsGrid);
            fireShowForm();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            string mgrsString = MgrsGridConverter.GetMgrsString(tp.Longitude, tp.Latitude, NRScanMultiCoverageGridHelper.SGridSize);
            string strGrid = "";
            if (antBase != null)
            {
                antBase.isContainPoint(tp.Longitude, tp.Latitude, ref strGrid);
            }
            if (!dicResult.ContainsKey(mgrsString))
            {
                NRScanMultiCoverageGridInfo grid = new NRScanMultiCoverageGridInfo(mgrsString);
                grid.StrGrid = strGrid;
                if (grid.CentLng > 180 || grid.CentLat > 90) // 转换库有问题
                {
                    return;
                }
                dicResult.Add(mgrsString, grid);
            }
            dicResult[mgrsString].DoWithTestPoint(tp);
        }

        private void doWithMgrsGrid()
        {
            int iSum = dicResult.Count;
            int idx = 0;
            double iProgress = 0;
            double iLastProgress = 0;
            foreach (var info in dicResult.Values)
            {
                idx++;
                iProgress = Math.Round(idx * 1.0 / iSum * 100, 0);
                if (iProgress % 2 == 0 && iProgress != 0 && iLastProgress != iProgress)
                {
                    iLastProgress = iProgress;
                    WaitBox.ProgressPercent = (int)iProgress;
                    WaitBox.Text = "正在统计栅格小区信息..." + iProgress + "%";
                }
                info.Calculate(curCondition);
                //dicResult[key].Coverage = doWithAllCell(dicResult[key]);
                info.clearData();
            }
            WaitBox.Close();
        }

      

        private void fireShowForm()
        {
            //ZTLteMgrsCoveForm showForm = new ZTLteMgrsCoveForm(MainModel);
            //showForm.iniData(dicResult, curCondition);
            //showForm.Show();

            var fmr = MainModel.CreateResultForm(typeof(NRScanMultiCoverageGridForm))
                as NRScanMultiCoverageGridForm;
            fmr.FillData(dicResult, curCondition);
            fmr.Visible = true;
            fmr.BringToFront();
        }
    }
}
