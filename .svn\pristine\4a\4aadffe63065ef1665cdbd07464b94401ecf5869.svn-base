﻿namespace MasterCom.RAMS.Stat
{
    partial class NoGisBatExpRptForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.progBar = new DevExpress.XtraEditors.ProgressBarControl();
            this.btnRun = new System.Windows.Forms.Button();
            this.btnNoGisPath = new System.Windows.Forms.Button();
            this.txtExportPath = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.chkLstReport = new System.Windows.Forms.CheckedListBox();
            this.chkLstDistrict = new System.Windows.Forms.CheckedListBox();
            this.chkLstProj = new System.Windows.Forms.CheckedListBox();
            this.chkLstService = new System.Windows.Forms.CheckedListBox();
            this.treeLstArea = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.chkLstAgent = new System.Windows.Forms.CheckedListBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.gBoxTimer = new System.Windows.Forms.GroupBox();
            this.label12 = new System.Windows.Forms.Label();
            this.nupDays = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.btnTimer = new System.Windows.Forms.Button();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.nupTimer = new System.Windows.Forms.NumericUpDown();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.btnSetFileName = new System.Windows.Forms.Button();
            this.label7 = new System.Windows.Forms.Label();
            this.tBoxFileName = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.cbxQueryFunc = new System.Windows.Forms.ComboBox();
            this.cBoxMergeData = new System.Windows.Forms.CheckBox();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.txtBatItemName = new System.Windows.Forms.TextBox();
            this.btnAddExpItem = new System.Windows.Forms.Button();
            this.btnSave = new System.Windows.Forms.Button();
            this.groupBoxFileName = new System.Windows.Forms.GroupBox();
            this.ccbePort = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.chkFileName = new System.Windows.Forms.CheckBox();
            this.cbxfileNumType = new System.Windows.Forms.ComboBox();
            this.btnClearPort = new System.Windows.Forms.Button();
            this.textBoxFileName = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chkAllRpt = new System.Windows.Forms.CheckBox();
            this.panel6 = new System.Windows.Forms.Panel();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.chkAllAgent = new System.Windows.Forms.CheckBox();
            this.panel2 = new System.Windows.Forms.Panel();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.chkAllDistrict = new System.Windows.Forms.CheckBox();
            this.panel5 = new System.Windows.Forms.Panel();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.chkAllService = new System.Windows.Forms.CheckBox();
            this.panel3 = new System.Windows.Forms.Panel();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.chkAllRegion = new System.Windows.Forms.CheckBox();
            this.panel4 = new System.Windows.Forms.Panel();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.chkAllProj = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chkLstBatItem = new System.Windows.Forms.CheckedListBox();
            this.ctxMenuBatItems = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miRemoveCur = new System.Windows.Forms.ToolStripMenuItem();
            this.miRemoveChk = new System.Windows.Forms.ToolStripMenuItem();
            this.chkAll = new System.Windows.Forms.CheckBox();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.dateEnd = new System.Windows.Forms.DateTimePicker();
            this.dateBegin = new System.Windows.Forms.DateTimePicker();
            this.txtProgInfo = new System.Windows.Forms.RichTextBox();
            this.ctxMenuProgTxt = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miClearTxt = new System.Windows.Forms.ToolStripMenuItem();
            this.label5 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.bkWorker = new System.ComponentModel.BackgroundWorker();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            ((System.ComponentModel.ISupportInitialize)(this.progBar.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeLstArea)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.gBoxTimer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nupDays)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nupTimer)).BeginInit();
            this.groupBox11.SuspendLayout();
            this.groupBox10.SuspendLayout();
            this.groupBoxFileName.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ccbePort.Properties)).BeginInit();
            this.tableLayoutPanel1.SuspendLayout();
            this.panel1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.panel6.SuspendLayout();
            this.groupBox8.SuspendLayout();
            this.panel2.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.panel5.SuspendLayout();
            this.groupBox7.SuspendLayout();
            this.panel3.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.panel4.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.ctxMenuBatItems.SuspendLayout();
            this.groupBox9.SuspendLayout();
            this.ctxMenuProgTxt.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // toolTip
            // 
            this.toolTip.InitialDelay = 500;
            this.toolTip.IsBalloon = true;
            // 
            // progBar
            // 
            this.progBar.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.progBar.Location = new System.Drawing.Point(103, 107);
            this.progBar.Name = "progBar";
            this.progBar.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F);
            this.progBar.Properties.Appearance.ForeColor = System.Drawing.Color.Green;
            this.progBar.Properties.EndColor = System.Drawing.Color.Green;
            this.progBar.Properties.ShowTitle = true;
            this.progBar.Properties.Step = 1;
            this.progBar.Size = new System.Drawing.Size(454, 18);
            this.progBar.TabIndex = 22;
            // 
            // btnRun
            // 
            this.btnRun.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRun.Location = new System.Drawing.Point(563, 164);
            this.btnRun.Name = "btnRun";
            this.btnRun.Size = new System.Drawing.Size(75, 23);
            this.btnRun.TabIndex = 15;
            this.btnRun.Text = "统计并导出";
            this.btnRun.UseVisualStyleBackColor = true;
            this.btnRun.Click += new System.EventHandler(this.btnRun_Click);
            // 
            // btnNoGisPath
            // 
            this.btnNoGisPath.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNoGisPath.Location = new System.Drawing.Point(569, 78);
            this.btnNoGisPath.Name = "btnNoGisPath";
            this.btnNoGisPath.Size = new System.Drawing.Size(75, 23);
            this.btnNoGisPath.TabIndex = 14;
            this.btnNoGisPath.Text = "浏览...";
            this.btnNoGisPath.UseVisualStyleBackColor = true;
            this.btnNoGisPath.Click += new System.EventHandler(this.btnNoGisPath_Click);
            // 
            // txtExportPath
            // 
            this.txtExportPath.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtExportPath.Location = new System.Drawing.Point(103, 79);
            this.txtExportPath.Name = "txtExportPath";
            this.txtExportPath.ReadOnly = true;
            this.txtExportPath.Size = new System.Drawing.Size(454, 22);
            this.txtExportPath.TabIndex = 21;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(54, 107);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 14);
            this.label4.TabIndex = 18;
            this.label4.Text = "进度：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(30, 82);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(67, 14);
            this.label2.TabIndex = 19;
            this.label2.Text = "导出路径：";
            // 
            // chkLstReport
            // 
            this.chkLstReport.CheckOnClick = true;
            this.chkLstReport.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chkLstReport.FormattingEnabled = true;
            this.chkLstReport.HorizontalScrollbar = true;
            this.chkLstReport.Location = new System.Drawing.Point(3, 18);
            this.chkLstReport.Name = "chkLstReport";
            this.chkLstReport.Size = new System.Drawing.Size(285, 251);
            this.chkLstReport.Sorted = true;
            this.chkLstReport.TabIndex = 25;
            // 
            // chkLstDistrict
            // 
            this.chkLstDistrict.CheckOnClick = true;
            this.chkLstDistrict.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chkLstDistrict.FormattingEnabled = true;
            this.chkLstDistrict.Location = new System.Drawing.Point(3, 18);
            this.chkLstDistrict.Name = "chkLstDistrict";
            this.chkLstDistrict.Size = new System.Drawing.Size(87, 251);
            this.chkLstDistrict.Sorted = true;
            this.chkLstDistrict.TabIndex = 25;
            // 
            // chkLstProj
            // 
            this.chkLstProj.CheckOnClick = true;
            this.chkLstProj.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chkLstProj.FormattingEnabled = true;
            this.chkLstProj.HorizontalScrollbar = true;
            this.chkLstProj.Location = new System.Drawing.Point(3, 18);
            this.chkLstProj.Name = "chkLstProj";
            this.chkLstProj.Size = new System.Drawing.Size(136, 251);
            this.chkLstProj.Sorted = true;
            this.chkLstProj.TabIndex = 25;
            // 
            // chkLstService
            // 
            this.chkLstService.CheckOnClick = true;
            this.chkLstService.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chkLstService.FormattingEnabled = true;
            this.chkLstService.HorizontalScrollbar = true;
            this.chkLstService.Location = new System.Drawing.Point(3, 18);
            this.chkLstService.Name = "chkLstService";
            this.chkLstService.Size = new System.Drawing.Size(116, 251);
            this.chkLstService.Sorted = true;
            this.chkLstService.TabIndex = 25;
            // 
            // treeLstArea
            // 
            this.treeLstArea.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn1});
            this.treeLstArea.Location = new System.Drawing.Point(3, 18);
            this.treeLstArea.Name = "treeLstArea";
            this.treeLstArea.BeginUnboundLoad();
            this.treeLstArea.AppendNode(new object[] {
            "fff"}, -1);
            this.treeLstArea.AppendNode(new object[] {
            "ffff"}, 0);
            this.treeLstArea.AppendNode(new object[] {
            "aaa"}, -1);
            this.treeLstArea.AppendNode(new object[] {
            "222"}, 2);
            this.treeLstArea.AppendNode(new object[] {
            "333"}, 3);
            this.treeLstArea.EndUnboundLoad();
            this.treeLstArea.OptionsView.ShowCheckBoxes = true;
            this.treeLstArea.OptionsView.ShowColumns = false;
            this.treeLstArea.OptionsView.ShowIndicator = false;
            this.treeLstArea.Size = new System.Drawing.Size(186, 240);
            this.treeLstArea.TabIndex = 26;
            // 
            // treeListColumn1
            // 
            this.treeListColumn1.MinWidth = 72;
            this.treeListColumn1.Name = "treeListColumn1";
            this.treeListColumn1.OptionsColumn.AllowEdit = false;
            this.treeListColumn1.Visible = true;
            this.treeListColumn1.VisibleIndex = 0;
            this.treeListColumn1.Width = 91;
            // 
            // chkLstAgent
            // 
            this.chkLstAgent.CheckOnClick = true;
            this.chkLstAgent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chkLstAgent.FormattingEnabled = true;
            this.chkLstAgent.HorizontalScrollbar = true;
            this.chkLstAgent.Location = new System.Drawing.Point(3, 18);
            this.chkLstAgent.Name = "chkLstAgent";
            this.chkLstAgent.Size = new System.Drawing.Size(108, 251);
            this.chkLstAgent.Sorted = true;
            this.chkLstAgent.TabIndex = 25;
            // 
            // groupBox1
            // 
            this.tableLayoutPanel2.SetColumnSpan(this.groupBox1, 2);
            this.groupBox1.Controls.Add(this.gBoxTimer);
            this.groupBox1.Controls.Add(this.groupBox11);
            this.groupBox1.Controls.Add(this.groupBox10);
            this.groupBox1.Controls.Add(this.groupBoxFileName);
            this.groupBox1.Controls.Add(this.tableLayoutPanel1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(3, 3);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1002, 454);
            this.groupBox1.TabIndex = 28;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "批量导出报表设置";
            // 
            // gBoxTimer
            // 
            this.gBoxTimer.Controls.Add(this.label12);
            this.gBoxTimer.Controls.Add(this.nupDays);
            this.gBoxTimer.Controls.Add(this.label11);
            this.gBoxTimer.Controls.Add(this.btnTimer);
            this.gBoxTimer.Controls.Add(this.label9);
            this.gBoxTimer.Controls.Add(this.label8);
            this.gBoxTimer.Controls.Add(this.nupTimer);
            this.gBoxTimer.Location = new System.Drawing.Point(603, 390);
            this.gBoxTimer.Name = "gBoxTimer";
            this.gBoxTimer.Size = new System.Drawing.Size(393, 58);
            this.gBoxTimer.TabIndex = 47;
            this.gBoxTimer.TabStop = false;
            this.gBoxTimer.Text = "未启动定时批量导出";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(268, 27);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(55, 14);
            this.label12.TabIndex = 6;
            this.label12.Text = "天前数据";
            // 
            // nupDays
            // 
            this.nupDays.Location = new System.Drawing.Point(206, 23);
            this.nupDays.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.nupDays.Name = "nupDays";
            this.nupDays.Size = new System.Drawing.Size(56, 22);
            this.nupDays.TabIndex = 5;
            this.nupDays.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nupDays.Value = new decimal(new int[] {
            30,
            0,
            0,
            0});
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(169, 27);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(31, 14);
            this.label11.TabIndex = 4;
            this.label11.Text = "查询";
            // 
            // btnTimer
            // 
            this.btnTimer.Location = new System.Drawing.Point(337, 23);
            this.btnTimer.Name = "btnTimer";
            this.btnTimer.Size = new System.Drawing.Size(50, 23);
            this.btnTimer.TabIndex = 3;
            this.btnTimer.Text = "启动";
            this.btnTimer.UseVisualStyleBackColor = true;
            this.btnTimer.Click += new System.EventHandler(this.btnTimer_Click);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(111, 27);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(31, 14);
            this.label9.TabIndex = 2;
            this.label9.Text = "分钟";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(6, 29);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(35, 14);
            this.label8.TabIndex = 1;
            this.label8.Text = "间隔:";
            // 
            // nupTimer
            // 
            this.nupTimer.Location = new System.Drawing.Point(47, 25);
            this.nupTimer.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.nupTimer.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nupTimer.Name = "nupTimer";
            this.nupTimer.Size = new System.Drawing.Size(56, 22);
            this.nupTimer.TabIndex = 0;
            this.nupTimer.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nupTimer.Value = new decimal(new int[] {
            30,
            0,
            0,
            0});
            // 
            // groupBox11
            // 
            this.groupBox11.Controls.Add(this.btnSetFileName);
            this.groupBox11.Controls.Add(this.label7);
            this.groupBox11.Controls.Add(this.tBoxFileName);
            this.groupBox11.Controls.Add(this.label6);
            this.groupBox11.Controls.Add(this.cbxQueryFunc);
            this.groupBox11.Controls.Add(this.cBoxMergeData);
            this.groupBox11.Location = new System.Drawing.Point(603, 305);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(393, 79);
            this.groupBox11.TabIndex = 46;
            this.groupBox11.TabStop = false;
            this.groupBox11.Text = "其它设置";
            // 
            // btnSetFileName
            // 
            this.btnSetFileName.Location = new System.Drawing.Point(337, 53);
            this.btnSetFileName.Name = "btnSetFileName";
            this.btnSetFileName.Size = new System.Drawing.Size(50, 22);
            this.btnSetFileName.TabIndex = 49;
            this.btnSetFileName.Text = "设置";
            this.btnSetFileName.UseVisualStyleBackColor = true;
            this.btnSetFileName.Click += new System.EventHandler(this.btnSetFileName_Click);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(6, 56);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(71, 14);
            this.label7.TabIndex = 48;
            this.label7.Text = "文件名格式:";
            // 
            // tBoxFileName
            // 
            this.tBoxFileName.Location = new System.Drawing.Point(83, 53);
            this.tBoxFileName.Name = "tBoxFileName";
            this.tBoxFileName.ReadOnly = true;
            this.tBoxFileName.Size = new System.Drawing.Size(239, 22);
            this.tBoxFileName.TabIndex = 47;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(211, 25);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(59, 14);
            this.label6.TabIndex = 46;
            this.label6.Text = "查询方式:";
            // 
            // cbxQueryFunc
            // 
            this.cbxQueryFunc.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxQueryFunc.DropDownWidth = 54;
            this.cbxQueryFunc.FormattingEnabled = true;
            this.cbxQueryFunc.Items.AddRange(new object[] {
            "新版",
            "旧版",
            "质检文件"});
            this.cbxQueryFunc.Location = new System.Drawing.Point(276, 21);
            this.cbxQueryFunc.Name = "cbxQueryFunc";
            this.cbxQueryFunc.Size = new System.Drawing.Size(111, 22);
            this.cbxQueryFunc.TabIndex = 45;
            // 
            // cBoxMergeData
            // 
            this.cBoxMergeData.AutoSize = true;
            this.cBoxMergeData.Location = new System.Drawing.Point(9, 21);
            this.cBoxMergeData.Name = "cBoxMergeData";
            this.cBoxMergeData.Size = new System.Drawing.Size(74, 18);
            this.cBoxMergeData.TabIndex = 36;
            this.cBoxMergeData.Text = "汇总数据";
            this.cBoxMergeData.UseVisualStyleBackColor = true;
            // 
            // groupBox10
            // 
            this.groupBox10.Controls.Add(this.label1);
            this.groupBox10.Controls.Add(this.txtBatItemName);
            this.groupBox10.Controls.Add(this.btnAddExpItem);
            this.groupBox10.Controls.Add(this.btnSave);
            this.groupBox10.Location = new System.Drawing.Point(6, 390);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(591, 58);
            this.groupBox10.TabIndex = 35;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "当前导出项";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(9, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(43, 14);
            this.label1.TabIndex = 19;
            this.label1.Text = "名称：";
            // 
            // txtBatItemName
            // 
            this.txtBatItemName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.txtBatItemName.Location = new System.Drawing.Point(55, 26);
            this.txtBatItemName.Name = "txtBatItemName";
            this.txtBatItemName.Size = new System.Drawing.Size(313, 22);
            this.txtBatItemName.TabIndex = 21;
            this.txtBatItemName.Text = "批量导出设置1";
            // 
            // btnAddExpItem
            // 
            this.btnAddExpItem.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnAddExpItem.Location = new System.Drawing.Point(374, 25);
            this.btnAddExpItem.Name = "btnAddExpItem";
            this.btnAddExpItem.Size = new System.Drawing.Size(130, 23);
            this.btnAddExpItem.TabIndex = 15;
            this.btnAddExpItem.Text = "添加到导出项↓";
            this.btnAddExpItem.UseVisualStyleBackColor = true;
            this.btnAddExpItem.Click += new System.EventHandler(this.btnAddExpItem_Click);
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnSave.Location = new System.Drawing.Point(510, 25);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(75, 23);
            this.btnSave.TabIndex = 14;
            this.btnSave.Text = "保存修改";
            this.btnSave.UseVisualStyleBackColor = true;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // groupBoxFileName
            // 
            this.groupBoxFileName.Controls.Add(this.ccbePort);
            this.groupBoxFileName.Controls.Add(this.chkFileName);
            this.groupBoxFileName.Controls.Add(this.cbxfileNumType);
            this.groupBoxFileName.Controls.Add(this.btnClearPort);
            this.groupBoxFileName.Controls.Add(this.textBoxFileName);
            this.groupBoxFileName.Controls.Add(this.label10);
            this.groupBoxFileName.Location = new System.Drawing.Point(6, 305);
            this.groupBoxFileName.Name = "groupBoxFileName";
            this.groupBoxFileName.Size = new System.Drawing.Size(591, 79);
            this.groupBoxFileName.TabIndex = 34;
            this.groupBoxFileName.TabStop = false;
            this.groupBoxFileName.Text = "  ";
            // 
            // ccbePort
            // 
            this.ccbePort.EditValue = "";
            this.ccbePort.Location = new System.Drawing.Point(58, 25);
            this.ccbePort.Name = "ccbePort";
            this.ccbePort.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ccbePort.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.CheckedListBoxItem[] {
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("1", "1"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("2", "2"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("3", "3"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("4", "4"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("5", "5"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("6", "6"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("7", "7"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("8", "8"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("9", "9")});
            this.ccbePort.Size = new System.Drawing.Size(236, 21);
            this.ccbePort.TabIndex = 46;
            this.ccbePort.EditValueChanged += new System.EventHandler(this.ccbePort_EditValueChanged);
            // 
            // chkFileName
            // 
            this.chkFileName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chkFileName.AutoSize = true;
            this.chkFileName.Location = new System.Drawing.Point(8, 0);
            this.chkFileName.Name = "chkFileName";
            this.chkFileName.Size = new System.Drawing.Size(86, 18);
            this.chkFileName.TabIndex = 28;
            this.chkFileName.Text = "文件名过滤";
            this.chkFileName.UseVisualStyleBackColor = true;
            // 
            // cbxfileNumType
            // 
            this.cbxfileNumType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxfileNumType.FormattingEnabled = true;
            this.cbxfileNumType.Items.AddRange(new object[] {
            "RCU",
            "ATU"});
            this.cbxfileNumType.Location = new System.Drawing.Point(300, 25);
            this.cbxfileNumType.Name = "cbxfileNumType";
            this.cbxfileNumType.Size = new System.Drawing.Size(53, 22);
            this.cbxfileNumType.TabIndex = 45;
            // 
            // btnClearPort
            // 
            this.btnClearPort.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.btnClearPort.Location = new System.Drawing.Point(359, 24);
            this.btnClearPort.Name = "btnClearPort";
            this.btnClearPort.Size = new System.Drawing.Size(45, 23);
            this.btnClearPort.TabIndex = 44;
            this.btnClearPort.Text = "清空";
            this.toolTip.SetToolTip(this.btnClearPort, "清空选择的端口");
            this.btnClearPort.UseVisualStyleBackColor = true;
            this.btnClearPort.Click += new System.EventHandler(this.btnClearPort_Click);
            // 
            // textBoxFileName
            // 
            this.textBoxFileName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxFileName.Location = new System.Drawing.Point(8, 53);
            this.textBoxFileName.Name = "textBoxFileName";
            this.textBoxFileName.Size = new System.Drawing.Size(577, 22);
            this.textBoxFileName.TabIndex = 0;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(9, 28);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(55, 14);
            this.label10.TabIndex = 47;
            this.label10.Text = "端口号：";
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanel1.ColumnCount = 6;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 30F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 10F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 20F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 13F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 12F));
            this.tableLayoutPanel1.Controls.Add(this.panel1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.panel6, 5, 0);
            this.tableLayoutPanel1.Controls.Add(this.panel2, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.panel5, 4, 0);
            this.tableLayoutPanel1.Controls.Add(this.panel3, 2, 0);
            this.tableLayoutPanel1.Controls.Add(this.panel4, 3, 0);
            this.tableLayoutPanel1.Location = new System.Drawing.Point(6, 21);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 1;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(990, 278);
            this.tableLayoutPanel1.TabIndex = 32;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.groupBox3);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(291, 272);
            this.panel1.TabIndex = 27;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.chkLstReport);
            this.groupBox3.Controls.Add(this.chkAllRpt);
            this.groupBox3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox3.Location = new System.Drawing.Point(0, 0);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(291, 272);
            this.groupBox3.TabIndex = 27;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "报表";
            // 
            // chkAllRpt
            // 
            this.chkAllRpt.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chkAllRpt.AutoSize = true;
            this.chkAllRpt.Location = new System.Drawing.Point(235, 0);
            this.chkAllRpt.Name = "chkAllRpt";
            this.chkAllRpt.Size = new System.Drawing.Size(50, 18);
            this.chkAllRpt.TabIndex = 28;
            this.chkAllRpt.Text = "全选";
            this.chkAllRpt.UseVisualStyleBackColor = true;
            this.chkAllRpt.CheckedChanged += new System.EventHandler(this.chkAllRpt_CheckedChanged);
            // 
            // panel6
            // 
            this.panel6.Controls.Add(this.groupBox8);
            this.panel6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel6.Location = new System.Drawing.Point(873, 3);
            this.panel6.Name = "panel6";
            this.panel6.Size = new System.Drawing.Size(114, 272);
            this.panel6.TabIndex = 33;
            // 
            // groupBox8
            // 
            this.groupBox8.Controls.Add(this.chkLstAgent);
            this.groupBox8.Controls.Add(this.chkAllAgent);
            this.groupBox8.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox8.Location = new System.Drawing.Point(0, 0);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(114, 272);
            this.groupBox8.TabIndex = 28;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "代维";
            // 
            // chkAllAgent
            // 
            this.chkAllAgent.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chkAllAgent.AutoSize = true;
            this.chkAllAgent.Location = new System.Drawing.Point(61, 0);
            this.chkAllAgent.Name = "chkAllAgent";
            this.chkAllAgent.Size = new System.Drawing.Size(50, 18);
            this.chkAllAgent.TabIndex = 28;
            this.chkAllAgent.Text = "全选";
            this.chkAllAgent.UseVisualStyleBackColor = true;
            this.chkAllAgent.CheckedChanged += new System.EventHandler(this.chkAllAgent_CheckedChanged);
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.groupBox4);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(300, 3);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(93, 272);
            this.panel2.TabIndex = 29;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.chkLstDistrict);
            this.groupBox4.Controls.Add(this.chkAllDistrict);
            this.groupBox4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox4.Location = new System.Drawing.Point(0, 0);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(93, 272);
            this.groupBox4.TabIndex = 28;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "地市";
            // 
            // chkAllDistrict
            // 
            this.chkAllDistrict.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chkAllDistrict.AutoSize = true;
            this.chkAllDistrict.Location = new System.Drawing.Point(40, 0);
            this.chkAllDistrict.Name = "chkAllDistrict";
            this.chkAllDistrict.Size = new System.Drawing.Size(50, 18);
            this.chkAllDistrict.TabIndex = 28;
            this.chkAllDistrict.Text = "全选";
            this.chkAllDistrict.UseVisualStyleBackColor = true;
            this.chkAllDistrict.CheckedChanged += new System.EventHandler(this.chkAllDistrict_CheckedChanged);
            // 
            // panel5
            // 
            this.panel5.Controls.Add(this.groupBox7);
            this.panel5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel5.Location = new System.Drawing.Point(745, 3);
            this.panel5.Name = "panel5";
            this.panel5.Size = new System.Drawing.Size(122, 272);
            this.panel5.TabIndex = 32;
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.chkLstService);
            this.groupBox7.Controls.Add(this.chkAllService);
            this.groupBox7.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox7.Location = new System.Drawing.Point(0, 0);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(122, 272);
            this.groupBox7.TabIndex = 28;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "业务";
            // 
            // chkAllService
            // 
            this.chkAllService.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chkAllService.AutoSize = true;
            this.chkAllService.Location = new System.Drawing.Point(69, 0);
            this.chkAllService.Name = "chkAllService";
            this.chkAllService.Size = new System.Drawing.Size(50, 18);
            this.chkAllService.TabIndex = 28;
            this.chkAllService.Text = "全选";
            this.chkAllService.UseVisualStyleBackColor = true;
            this.chkAllService.CheckedChanged += new System.EventHandler(this.chkAllService_CheckedChanged);
            // 
            // panel3
            // 
            this.panel3.Controls.Add(this.groupBox5);
            this.panel3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel3.Location = new System.Drawing.Point(399, 3);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(192, 272);
            this.panel3.TabIndex = 30;
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.treeLstArea);
            this.groupBox5.Controls.Add(this.chkAllRegion);
            this.groupBox5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox5.Location = new System.Drawing.Point(0, 0);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(192, 272);
            this.groupBox5.TabIndex = 28;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "区域";
            // 
            // chkAllRegion
            // 
            this.chkAllRegion.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chkAllRegion.AutoSize = true;
            this.chkAllRegion.Location = new System.Drawing.Point(139, 0);
            this.chkAllRegion.Name = "chkAllRegion";
            this.chkAllRegion.Size = new System.Drawing.Size(50, 18);
            this.chkAllRegion.TabIndex = 28;
            this.chkAllRegion.Text = "全选";
            this.chkAllRegion.UseVisualStyleBackColor = true;
            this.chkAllRegion.CheckedChanged += new System.EventHandler(this.chkAllRegion_CheckedChanged);
            // 
            // panel4
            // 
            this.panel4.Controls.Add(this.groupBox6);
            this.panel4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel4.Location = new System.Drawing.Point(597, 3);
            this.panel4.Name = "panel4";
            this.panel4.Size = new System.Drawing.Size(142, 272);
            this.panel4.TabIndex = 31;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.chkLstProj);
            this.groupBox6.Controls.Add(this.chkAllProj);
            this.groupBox6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox6.Location = new System.Drawing.Point(0, 0);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(142, 272);
            this.groupBox6.TabIndex = 28;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "项目";
            // 
            // chkAllProj
            // 
            this.chkAllProj.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chkAllProj.AutoSize = true;
            this.chkAllProj.Location = new System.Drawing.Point(89, 0);
            this.chkAllProj.Name = "chkAllProj";
            this.chkAllProj.Size = new System.Drawing.Size(50, 18);
            this.chkAllProj.TabIndex = 28;
            this.chkAllProj.Text = "全选";
            this.chkAllProj.UseVisualStyleBackColor = true;
            this.chkAllProj.CheckedChanged += new System.EventHandler(this.chkAllProj_CheckedChanged);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chkLstBatItem);
            this.groupBox2.Controls.Add(this.chkAll);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox2.Location = new System.Drawing.Point(3, 463);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(346, 196);
            this.groupBox2.TabIndex = 29;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "可选批量导出项";
            // 
            // chkLstBatItem
            // 
            this.chkLstBatItem.ContextMenuStrip = this.ctxMenuBatItems;
            this.chkLstBatItem.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chkLstBatItem.FormattingEnabled = true;
            this.chkLstBatItem.Location = new System.Drawing.Point(3, 18);
            this.chkLstBatItem.Name = "chkLstBatItem";
            this.chkLstBatItem.Size = new System.Drawing.Size(340, 175);
            this.chkLstBatItem.TabIndex = 29;
            this.chkLstBatItem.SelectedIndexChanged += new System.EventHandler(this.chkLstBatItem_SelectedIndexChanged);
            // 
            // ctxMenuBatItems
            // 
            this.ctxMenuBatItems.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miRemoveCur,
            this.miRemoveChk});
            this.ctxMenuBatItems.Name = "ctxMenuBatItems";
            this.ctxMenuBatItems.Size = new System.Drawing.Size(137, 48);
            // 
            // miRemoveCur
            // 
            this.miRemoveCur.Name = "miRemoveCur";
            this.miRemoveCur.Size = new System.Drawing.Size(136, 22);
            this.miRemoveCur.Text = "删除当前项";
            this.miRemoveCur.Click += new System.EventHandler(this.miRemoveCur_Click);
            // 
            // miRemoveChk
            // 
            this.miRemoveChk.Name = "miRemoveChk";
            this.miRemoveChk.Size = new System.Drawing.Size(136, 22);
            this.miRemoveChk.Text = "删除勾选项";
            this.miRemoveChk.Click += new System.EventHandler(this.miRemoveChk_Click);
            // 
            // chkAll
            // 
            this.chkAll.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chkAll.AutoSize = true;
            this.chkAll.Location = new System.Drawing.Point(289, 0);
            this.chkAll.Name = "chkAll";
            this.chkAll.Size = new System.Drawing.Size(50, 18);
            this.chkAll.TabIndex = 28;
            this.chkAll.Text = "全选";
            this.chkAll.UseVisualStyleBackColor = true;
            this.chkAll.CheckedChanged += new System.EventHandler(this.chkAll_CheckedChanged);
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.dateEnd);
            this.groupBox9.Controls.Add(this.dateBegin);
            this.groupBox9.Controls.Add(this.txtProgInfo);
            this.groupBox9.Controls.Add(this.label5);
            this.groupBox9.Controls.Add(this.label3);
            this.groupBox9.Controls.Add(this.progBar);
            this.groupBox9.Controls.Add(this.txtExportPath);
            this.groupBox9.Controls.Add(this.btnRun);
            this.groupBox9.Controls.Add(this.label2);
            this.groupBox9.Controls.Add(this.btnNoGisPath);
            this.groupBox9.Controls.Add(this.label4);
            this.groupBox9.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox9.Location = new System.Drawing.Point(355, 463);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(650, 196);
            this.groupBox9.TabIndex = 30;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "统计并导出";
            // 
            // dateEnd
            // 
            this.dateEnd.Location = new System.Drawing.Point(103, 51);
            this.dateEnd.Name = "dateEnd";
            this.dateEnd.Size = new System.Drawing.Size(200, 22);
            this.dateEnd.TabIndex = 0;
            // 
            // dateBegin
            // 
            this.dateBegin.Location = new System.Drawing.Point(103, 23);
            this.dateBegin.Name = "dateBegin";
            this.dateBegin.Size = new System.Drawing.Size(200, 22);
            this.dateBegin.TabIndex = 0;
            // 
            // txtProgInfo
            // 
            this.txtProgInfo.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtProgInfo.ContextMenuStrip = this.ctxMenuProgTxt;
            this.txtProgInfo.Location = new System.Drawing.Point(103, 131);
            this.txtProgInfo.Name = "txtProgInfo";
            this.txtProgInfo.ReadOnly = true;
            this.txtProgInfo.Size = new System.Drawing.Size(454, 56);
            this.txtProgInfo.TabIndex = 31;
            this.txtProgInfo.Text = "";
            // 
            // ctxMenuProgTxt
            // 
            this.ctxMenuProgTxt.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miClearTxt});
            this.ctxMenuProgTxt.Name = "ctxMenuProgTxt";
            this.ctxMenuProgTxt.Size = new System.Drawing.Size(101, 26);
            // 
            // miClearTxt
            // 
            this.miClearTxt.Name = "miClearTxt";
            this.miClearTxt.Size = new System.Drawing.Size(100, 22);
            this.miClearTxt.Text = "清空";
            this.miClearTxt.Click += new System.EventHandler(this.miClearTxt_Click);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(30, 57);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(67, 14);
            this.label5.TabIndex = 19;
            this.label5.Text = "结束时间：";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(30, 29);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 19;
            this.label3.Text = "开始时间：";
            // 
            // bkWorker
            // 
            this.bkWorker.WorkerReportsProgress = true;
            this.bkWorker.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bkWorker_DoWork);
            this.bkWorker.ProgressChanged += new System.ComponentModel.ProgressChangedEventHandler(this.bkWorker_ProgressChanged);
            this.bkWorker.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bkWorker_RunWorkerCompleted);
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 2;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 65F));
            this.tableLayoutPanel2.Controls.Add(this.groupBox1, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.groupBox2, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.groupBox9, 1, 1);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 2;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 69.63746F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 30.36254F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(1008, 662);
            this.tableLayoutPanel2.TabIndex = 33;
            // 
            // NoGisBatExpRptForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1008, 662);
            this.Controls.Add(this.tableLayoutPanel2);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MinimumSize = new System.Drawing.Size(1024, 700);
            this.Name = "NoGisBatExpRptForm";
            this.Text = "批量导出区域报表";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.NoGisBatExpRptForm_FormClosing);
            ((System.ComponentModel.ISupportInitialize)(this.progBar.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeLstArea)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.gBoxTimer.ResumeLayout(false);
            this.gBoxTimer.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nupDays)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nupTimer)).EndInit();
            this.groupBox11.ResumeLayout(false);
            this.groupBox11.PerformLayout();
            this.groupBox10.ResumeLayout(false);
            this.groupBox10.PerformLayout();
            this.groupBoxFileName.ResumeLayout(false);
            this.groupBoxFileName.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ccbePort.Properties)).EndInit();
            this.tableLayoutPanel1.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.panel6.ResumeLayout(false);
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.panel5.ResumeLayout(false);
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            this.panel3.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.panel4.ResumeLayout(false);
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.ctxMenuBatItems.ResumeLayout(false);
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            this.ctxMenuProgTxt.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.ProgressBarControl progBar;
        private System.Windows.Forms.Button btnRun;
        private System.Windows.Forms.Button btnNoGisPath;
        private System.Windows.Forms.TextBox txtExportPath;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckedListBox chkLstReport;
        private System.Windows.Forms.CheckedListBox chkLstDistrict;
        private System.Windows.Forms.CheckedListBox chkLstProj;
        private System.Windows.Forms.CheckedListBox chkLstService;
        private DevExpress.XtraTreeList.TreeList treeLstArea;
        private System.Windows.Forms.CheckedListBox chkLstAgent;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.CheckedListBox chkLstBatItem;
        private System.Windows.Forms.CheckBox chkAll;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Button btnAddExpItem;
        private System.Windows.Forms.TextBox txtBatItemName;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.DateTimePicker dateEnd;
        private System.Windows.Forms.DateTimePicker dateBegin;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Button btnSave;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn1;
        private System.Windows.Forms.CheckBox chkAllAgent;
        private System.Windows.Forms.CheckBox chkAllService;
        private System.Windows.Forms.CheckBox chkAllProj;
        private System.Windows.Forms.CheckBox chkAllDistrict;
        private System.Windows.Forms.CheckBox chkAllRpt;
        private System.ComponentModel.BackgroundWorker bkWorker;
        private System.Windows.Forms.RichTextBox txtProgInfo;
        private System.Windows.Forms.ContextMenuStrip ctxMenuProgTxt;
        private System.Windows.Forms.ToolStripMenuItem miClearTxt;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Panel panel6;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Panel panel5;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Panel panel4;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private System.Windows.Forms.CheckBox chkAllRegion;
        private System.Windows.Forms.GroupBox groupBoxFileName;
        private DevExpress.XtraEditors.CheckedComboBoxEdit ccbePort;
        private System.Windows.Forms.CheckBox chkFileName;
        private System.Windows.Forms.ComboBox cbxfileNumType;
        private System.Windows.Forms.Button btnClearPort;
        private System.Windows.Forms.TextBox textBoxFileName;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.ContextMenuStrip ctxMenuBatItems;
        private System.Windows.Forms.ToolStripMenuItem miRemoveCur;
        private System.Windows.Forms.ToolStripMenuItem miRemoveChk;
        private System.Windows.Forms.ComboBox cbxQueryFunc;
        private System.Windows.Forms.CheckBox cBoxMergeData;
        private System.Windows.Forms.GroupBox groupBox11;
        private System.Windows.Forms.Button btnSetFileName;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox tBoxFileName;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.GroupBox gBoxTimer;
        private System.Windows.Forms.Button btnTimer;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown nupTimer;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.NumericUpDown nupDays;
        private System.Windows.Forms.Label label11;
    }
}