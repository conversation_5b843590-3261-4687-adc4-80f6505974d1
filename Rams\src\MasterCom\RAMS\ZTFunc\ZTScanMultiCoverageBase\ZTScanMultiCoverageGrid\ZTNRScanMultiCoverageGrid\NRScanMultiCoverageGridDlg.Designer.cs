﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRScanMultiCoverageGridDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.numRsrpDiff = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.numRsrpMin = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.numOptionalRsrp = new System.Windows.Forms.NumericUpDown();
            this.chkOptionalRsrp = new System.Windows.Forms.CheckBox();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOptionalRsrp)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.numRsrpDiff);
            this.groupControl1.Controls.Add(this.label1);
            this.groupControl1.Controls.Add(this.numRsrpMin);
            this.groupControl1.Controls.Add(this.label2);
            this.groupControl1.Controls.Add(this.label3);
            this.groupControl1.Controls.Add(this.label8);
            this.groupControl1.Controls.Add(this.label5);
            this.groupControl1.Controls.Add(this.numOptionalRsrp);
            this.groupControl1.Controls.Add(this.chkOptionalRsrp);
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(273, 157);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "指标限定";
            // 
            // numRsrpDiff
            // 
            this.numRsrpDiff.Location = new System.Drawing.Point(104, 73);
            this.numRsrpDiff.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numRsrpDiff.Name = "numRsrpDiff";
            this.numRsrpDiff.Size = new System.Drawing.Size(120, 21);
            this.numRsrpDiff.TabIndex = 56;
            this.numRsrpDiff.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRsrpDiff.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(34, 38);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 52;
            this.label1.Text = "最强信号≥";
            // 
            // numRsrpMin
            // 
            this.numRsrpMin.Location = new System.Drawing.Point(104, 35);
            this.numRsrpMin.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numRsrpMin.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numRsrpMin.Name = "numRsrpMin";
            this.numRsrpMin.Size = new System.Drawing.Size(120, 21);
            this.numRsrpMin.TabIndex = 53;
            this.numRsrpMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRsrpMin.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(230, 38);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(23, 12);
            this.label2.TabIndex = 54;
            this.label2.Text = "dBm";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(22, 76);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 55;
            this.label3.Text = "相对覆盖带≤";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(230, 114);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(23, 12);
            this.label8.TabIndex = 60;
            this.label8.Text = "dBm";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(230, 75);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 57;
            this.label5.Text = "dB";
            // 
            // numOptionalRsrp
            // 
            this.numOptionalRsrp.Enabled = false;
            this.numOptionalRsrp.Location = new System.Drawing.Point(104, 110);
            this.numOptionalRsrp.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numOptionalRsrp.Minimum = new decimal(new int[] {
            10000,
            0,
            0,
            -2147483648});
            this.numOptionalRsrp.Name = "numOptionalRsrp";
            this.numOptionalRsrp.Size = new System.Drawing.Size(120, 21);
            this.numOptionalRsrp.TabIndex = 59;
            this.numOptionalRsrp.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOptionalRsrp.Value = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            // 
            // chkOptionalRsrp
            // 
            this.chkOptionalRsrp.AutoSize = true;
            this.chkOptionalRsrp.Location = new System.Drawing.Point(14, 113);
            this.chkOptionalRsrp.Name = "chkOptionalRsrp";
            this.chkOptionalRsrp.Size = new System.Drawing.Size(84, 16);
            this.chkOptionalRsrp.TabIndex = 58;
            this.chkOptionalRsrp.Text = "信号强度≥";
            this.chkOptionalRsrp.UseVisualStyleBackColor = true;
            this.chkOptionalRsrp.CheckedChanged += new System.EventHandler(this.chkOptionalRsrp_CheckedChanged);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Location = new System.Drawing.Point(177, 167);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 56;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(69, 167);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 55;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // NRScanMultiCoverageGridDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(273, 202);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupControl1);
            this.Name = "NRScanMultiCoverageGridDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "重叠覆盖栅格";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpDiff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOptionalRsrp)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.NumericUpDown numRsrpDiff;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numRsrpMin;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numOptionalRsrp;
        private System.Windows.Forms.CheckBox chkOptionalRsrp;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
    }
}