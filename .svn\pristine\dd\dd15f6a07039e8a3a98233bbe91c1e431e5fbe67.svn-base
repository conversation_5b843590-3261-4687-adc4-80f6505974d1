﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Func.SystemSetting
{
    public partial class LowTaskFileManageProperties : PropertiesControl
    {
        LowTaskFileManage queryFunc;
        public LowTaskFileManageProperties(LowTaskFileManage queryFunc)
        {
            InitializeComponent();
            this.queryFunc = queryFunc;
        }

        public override string ParentName
        {
            get { return queryFunc.FuncType.ToString(); }
        }

        public override string ParentSubName
        {
            get { return queryFunc.SubFuncType.ToString(); }
        }

        public override string SelfName
        {
            get { return queryFunc.Name; }
        }

        public override string TabPageName
        {
            get { return queryFunc.Name; }
        }

        public override void Flush()
        {
            chkBackgroundStat.Checked = queryFunc.BackgroundStat;

            btnExcelPath.Text = queryFunc.OrderCondition.ExcelPath;
            btnImportedExcelPath.Text = queryFunc.OrderCondition.ImportedExcelPath;
            btnPicPath.Text = queryFunc.OrderCondition.PicPath;
            txtAttachPath.Text = queryFunc.OrderCondition.AttachPath;
            txtWebPath.Text = queryFunc.OrderCondition.WebServicePath;
            numAnalyseTime.Value = queryFunc.OrderCondition.TimeDiff;
            numAnalyseErrorTime.Value = queryFunc.OrderCondition.ErrorTimeDiff;

            numTimeOverlap.Value = queryFunc.OrderCondition.TimeOverlap;
            numTPOverlap.Value = queryFunc.OrderCondition.TPOverlap;
            numTPTimeDiff.Value = queryFunc.OrderCondition.TPTimeDiff;
            numTPDistance.Value = queryFunc.OrderCondition.TPDistance;
        }

        public override void Apply()
        {
            queryFunc.BackgroundStat = chkBackgroundStat.Checked;

            queryFunc.OrderCondition.ExcelPath = btnExcelPath.Text;
            queryFunc.OrderCondition.ImportedExcelPath = btnImportedExcelPath.Text;
            queryFunc.OrderCondition.PicPath = btnPicPath.Text;
            queryFunc.OrderCondition.AttachPath = txtAttachPath.Text;
            queryFunc.OrderCondition.WebServicePath = txtWebPath.Text;
            queryFunc.OrderCondition.TimeDiff = (int)numAnalyseTime.Value;
            queryFunc.OrderCondition.ErrorTimeDiff = (int)numAnalyseErrorTime.Value;

            queryFunc.OrderCondition.TimeOverlap = (int)numTimeOverlap.Value;
            queryFunc.OrderCondition.TPOverlap = (int)numTPOverlap.Value;
            queryFunc.OrderCondition.TPTimeDiff = (int)numTPTimeDiff.Value;
            queryFunc.OrderCondition.TPDistance = (int)numTPDistance.Value;
        }

        private void btnEditSelectFolder(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            DevExpress.XtraEditors.ButtonEdit btnEdit = sender as DevExpress.XtraEditors.ButtonEdit;

            if (btnEdit != null)
            {
                FolderBrowserDialog folderDlg = new FolderBrowserDialog();
                if (folderDlg.ShowDialog() == DialogResult.OK)
                {
                    btnEdit.Text = folderDlg.SelectedPath;
                }
            }
        }
    }
}
