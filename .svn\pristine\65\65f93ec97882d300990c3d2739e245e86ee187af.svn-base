﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public enum CQTPointBeLong
    {
        MTPoint = 0,
        AtuPoint = 1,
    }
    public class CQTPointManager
    {
        private static CQTPointManager instance = null;
        private CQTPointManager()
        {
            cqtPnts = new List<CQTPoint>();
            idPointDic = new Dictionary<int, CQTPoint>();
            RegionPntList = new List<CQTPoint>();
        }
        public static CQTPointManager GetInstance()
        {
            if (instance == null)
            {
                instance = new CQTPointManager();
                MainModel.GetInstance().DistrictChanged += CQTPointManager_DistrictChanged;
            }
            return instance;
        }

        static void CQTPointManager_DistrictChanged(object sender, EventArgs e)
        {
            instance.CQTPoints = null;
        }

        private bool hasQueried = false;

        public void QueryPntFromDB()
        {
            if (hasQueried)
            {
                return;
            }
            DIYSQLQueryCQTPoint query = new DIYSQLQueryCQTPoint(MainModel.GetInstance());
            query.Query();
            hasQueried = true;
        }

        public void Clear()
        {
            RegionPntList = new List<CQTPoint>();
        }
        public void AddPoint(CQTPoint point)
        {
            cqtPnts.Add(point);
            idPointDic[point.ID] = point;
        }

        public CQTPoint GetPoint(int id)
        {
            CQTPoint point = null;
            if (idPointDic.ContainsKey(id))
            {
                point = idPointDic[id];
            }
            return point;
        }
        private readonly Dictionary<int, CQTPoint> idPointDic = null;
        private List<CQTPoint> cqtPnts;
        public List<CQTPoint> CQTPoints
        {
            set { cqtPnts = value; }
            get 
            {
                List<CQTPoint> pnts = new List<CQTPoint>();
                if (cqtPnts == null || cqtPnts.Count == 0)
                {
                    cqtPnts = new List<CQTPoint>();
                    QueryPntFromDB();
                }
                pnts.AddRange(cqtPnts);
                return pnts; 
            }
        }

        public List<CQTPoint> GetPointsByRegion(MTPolygon mapOper)
        {
            List<CQTPoint> pnts = new List<CQTPoint>();
            foreach (CQTPoint pnt in CQTPoints)
            {
                if (mapOper.Contains(pnt.Longitude, pnt.Latitude))
                {
                    pnts.Add(pnt);
                }
            }
            return pnts;
        }
        
        public List<CQTPoint> RegionPntList { get; set; }

        private List<CQTPoint> atuPntList;
        public List<CQTPoint> AtuPntList
        {
            set { atuPntList = value; }
            get 
            {
                if (atuPntList == null || atuPntList.Count == 0)
                {
                    atuPntList = new List<CQTPoint>(); 
                    foreach (CQTPoint pnt in cqtPnts)
                    {
                        if (pnt.otherType1 == (int)CQTPointBeLong.AtuPoint)
                        {
                            atuPntList.Add(pnt);
                        }
                    }
                }
                return atuPntList;
            }
        }

        private List<CQTPoint> mtPntList;
        public List<CQTPoint> MTPntList
        {
            set { mtPntList = value; }
            get
            {
                if (mtPntList == null || mtPntList.Count == 0)
                {
                    mtPntList = new List<CQTPoint>();
                    foreach (CQTPoint pnt in CQTPoints)
                    {
                        if (pnt.otherType1 == (int)CQTPointBeLong.MTPoint)
                        {
                            mtPntList.Add(pnt);
                        }
                    }
                }
                return mtPntList;
            }
        }

        /// <summary>
        /// 按地点名称模糊搜索
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        internal List<CQTPoint> FindByName(string text)
        {
            List<CQTPoint> pnts = new List<CQTPoint>();
            string upperName = text.ToUpper();
            foreach (CQTPoint pnt in CQTPoints)
            {
                if (pnt.Name.ToUpper().IndexOf(upperName) != -1)
                {
                    pnts.Add(pnt);
                }
            }
            return pnts;
        }

    }
}
