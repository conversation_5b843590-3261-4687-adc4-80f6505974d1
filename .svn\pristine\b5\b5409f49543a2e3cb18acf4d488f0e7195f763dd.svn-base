﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 道路在一个时段中的多个文件的主方向测试信息
    /// </summary>
    public class RoadDirectionInfo_Period : RoadDirectionInfoBase
    {
        public RoadDirectionInfo_Period(string timeDes, StreetInjectInfo info)
            : base(timeDes, info)
        {
        }
        
        public List<EnumDirection> MainDirectionList { get; set; } = new List<EnumDirection>();

        public string MainDirectionDes
        {
            get
            {
                StringBuilder strb = new StringBuilder();
                foreach (EnumDirection direction in MainDirectionList)
                {
                    strb.Append(string.Format("{0}({1});", direction.ToString(), TestDirectionHelper.GetDirectionSign(direction)));
                }
                if (strb.Length > 0)
                {
                    strb.Remove(strb.Length - 1, 1);
                }
                return strb.ToString();
            }
        }
        
        public bool? IsSameDirectionWithFirst { get; set; }
        public string IsSameDirectionWithFirstDes { get; set; }

        public void AddData(RoadDirectionInfo_File info)
        {
            if (this.StreetInfo != null && info.StreetInfo != null)
            {
                this.StreetInfo.AddData(info.StreetInfo);
            }
            if (!MainDirectionList.Contains(info.MainDirection))
            {
                this.MainDirectionList.Add(info.MainDirection);
            }
        }
    }
    public class RoadDirectionCompareItem : RoadDirectionInfoBase
    {
        public RoadDirectionCompareItem(string timeDes, StreetInjectInfo info)
            : base(timeDes, info)
        {
        }
        public int SN { get; set; }

        private readonly Dictionary<string, RoadDirectionInfo_Period> difTimeRoadDirectionInfoDic = new Dictionary<string, RoadDirectionInfo_Period>();
        public List<RoadDirectionInfo_Period> DifTimeRoadDirectionInfoList
        {
            get { return new List<RoadDirectionInfo_Period>(difTimeRoadDirectionInfoDic.Values); }
        }
        public void AddData(RoadDirectionInfo_File info)
        {
            RoadDirectionInfo_Period directionInfo;
            if (!difTimeRoadDirectionInfoDic.TryGetValue(info.TimeDes, out directionInfo))
            {
                directionInfo = new RoadDirectionInfo_Period(info.TimeDes, info.StreetInfo);
                difTimeRoadDirectionInfoDic.Add(directionInfo.TimeDes, directionInfo);
            }
            directionInfo.AddData(info);
        }

        public void GetSumInfo(string firstTimeDes)
        {
            if (string.IsNullOrEmpty(firstTimeDes))
            {
                return;
            }

            RoadDirectionInfo_Period firstInfo = null;
            foreach (string strTimeDes in difTimeRoadDirectionInfoDic.Keys)
            {
                RoadDirectionInfo_Period info = difTimeRoadDirectionInfoDic[strTimeDes];
                if (strTimeDes == firstTimeDes)
                {
                    firstInfo = info;
                    continue;
                }
                if (firstInfo == null)
                {
                    continue;
                }

                string strDes = "";
                foreach (EnumDirection hostDirection in firstInfo.MainDirectionList)
                {
                    foreach (EnumDirection guestDirection in info.MainDirectionList)
                    {
                        if (hostDirection == guestDirection)
                        {
                            info.IsSameDirectionWithFirst = true;
                            strDes = "是";
                            break;
                        }
                        info.IsSameDirectionWithFirst = false;
                        strDes = "否";
                    }
                }
                info.IsSameDirectionWithFirstDes = string.Format("{0} ({1}-{2}对比)", strDes, firstInfo.TimeDes, info.TimeDes);
            }
        }
    }

    /// <summary>
    /// 道路在单个文件中各个方向的测试信息
    /// </summary>
    public class RoadDirectionInfo_File : RoadDirectionInfoBase
    {
        public RoadDirectionInfo_File(string timeDes, StreetInjectInfo info)
            : base(timeDes, info)
        {
        }
        
        public Dictionary<EnumDirection, double> Direction_DistanceDic { get; set; } = new Dictionary<EnumDirection, double>();

        private double roadTestLength = 0;
        public void AddDirectionInfo(EnumDirection direction, double length)
        {
            this.roadTestLength += length;
            if (Direction_DistanceDic.ContainsKey(direction))
            {
                Direction_DistanceDic[direction] += length;
            }
            else
            {
                Direction_DistanceDic.Add(direction, length);
            }
        }
        
        public EnumDirection MainDirection { get; set; } = new EnumDirection();
        public bool GetMainDirection()
        {
            if (this.roadTestLength > 0)
            {
                foreach (EnumDirection direction in this.Direction_DistanceDic.Keys)
                {
                    double directionLength = this.Direction_DistanceDic[direction];
                    double directionPer = directionLength / this.roadTestLength;
                    if (directionPer >= 0.6)
                    {
                        this.MainDirection = direction;
                        return true;
                    }
                }
            }
            return false;
        }
    }
    public class RoadDirectionInfoBase
    {
        public RoadDirectionInfoBase(string timeDes, StreetInjectInfo info)
        {
            this.TimeDes = timeDes;
            if (info != null)
            {
                this.StreetInfo = (StreetInjectInfo)info.Clone();
            }
        }

        /// <summary>
        /// 统计时间概述
        /// </summary>
        public string TimeDes { get; private set; }
        public StreetInjectInfo StreetInfo { get; set; }
        public string GridName
        {
            get { return this.StreetInfo == null ? "" : this.StreetInfo.AreaName; }
        }
        public string RoadLevel
        {
            get { return this.StreetInfo == null ? "" : this.StreetInfo.StreetTableName; }
        }
        public string RoadName
        {
            get { return this.StreetInfo == null ? "" : this.StreetInfo.StreetName; }
        }
        public double RoadLength
        {
            get { return this.StreetInfo == null ? 0 : Math.Round(this.StreetInfo.DistTotal, 2); }
        }
        public string RoadIdDes
        {
            get
            {
                return StreetInjectInfo.GetIntListDes(RoadIdList);
            }
        }

        public List<int> RoadIdList
        {
            get 
            {
                if (this.StreetInfo == null)
                {
                    return new List<int>();
                }
                else
                {
                    return this.StreetInfo.StreetIdList;
                }
            }
        }

        public MapWinGIS.Shape StreetCurv
        {
            get
            {
                return StreetInfo == null ? null : StreetInfo.streetCurv;
            }
        }
        public string Token
        {
            get
            {
                return GetToken(this);
            }
        }
        public static string GetToken(RoadDirectionInfoBase info)
        {
            return string.Format("{0}_{1}_{2}_{3}", info.GridName, info.RoadLevel, info.RoadName, info.RoadIdDes);
        }
    }

    /// <summary>
    /// 测试随机性（不一致道路个数/(不一致道路个数 + 一致道路个数)）
    /// </summary>
    public class TestRandomInfo
    {
        public TestRandomInfo(string firstTimeDes, string curTimeDes)
        {
            this.FirstTimeDes = firstTimeDes;
            this.NextTimedDes = curTimeDes;
        }
        public int SN { get; set; }
        public string FirstTimeDes { get; set; }
        public string NextTimedDes { get; set; }
        public int SameDirectionCount { get; set; }
        public int DifferentDirectionCount { get; set; }
        public int ToToalDirectionCount
        {
            get { return SameDirectionCount + DifferentDirectionCount; }
        }
        public double? TestRandomRate
        {
            get
            {
                if (ToToalDirectionCount > 0)
                {
                    return Math.Round((double)(100 * DifferentDirectionCount) / ToToalDirectionCount, 2);
                }
                return null;
            }
        }
        public string Des
        {
            get
            {
                return string.Format("({0}-{1}对比)", this.FirstTimeDes, this.NextTimedDes);
            }
        }
    }
}
