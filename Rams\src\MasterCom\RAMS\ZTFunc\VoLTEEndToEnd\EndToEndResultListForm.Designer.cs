﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class EndToEndResultListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.ListViewEndToEnd = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMoFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMessage = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMoPhone = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtPhone = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctmStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripExport = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewEndToEnd)).BeginInit();
            this.ctmStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // ListViewEndToEnd
            // 
            this.ListViewEndToEnd.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewEndToEnd.AllColumns.Add(this.olvColumnMoFileName);
            this.ListViewEndToEnd.AllColumns.Add(this.olvColumnMtFileName);
            this.ListViewEndToEnd.AllColumns.Add(this.olvColumnMessage);
            this.ListViewEndToEnd.AllColumns.Add(this.olvColumnDate);
            this.ListViewEndToEnd.AllColumns.Add(this.olvColumnTime);
            this.ListViewEndToEnd.AllColumns.Add(this.olvColumnMoPhone);
            this.ListViewEndToEnd.AllColumns.Add(this.olvColumnMtPhone);
            this.ListViewEndToEnd.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnMoFileName,
            this.olvColumnMtFileName,
            this.olvColumnMessage,
            this.olvColumnDate,
            this.olvColumnTime,
            this.olvColumnMoPhone,
            this.olvColumnMtPhone});
            this.ListViewEndToEnd.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewEndToEnd.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewEndToEnd.FullRowSelect = true;
            this.ListViewEndToEnd.GridLines = true;
            this.ListViewEndToEnd.HeaderWordWrap = true;
            this.ListViewEndToEnd.IsNeedShowOverlay = false;
            this.ListViewEndToEnd.Location = new System.Drawing.Point(0, 0);
            this.ListViewEndToEnd.Name = "ListViewEndToEnd";
            this.ListViewEndToEnd.OwnerDraw = true;
            this.ListViewEndToEnd.ShowGroups = false;
            this.ListViewEndToEnd.Size = new System.Drawing.Size(968, 572);
            this.ListViewEndToEnd.TabIndex = 10;
            this.ListViewEndToEnd.UseCompatibleStateImageBehavior = false;
            this.ListViewEndToEnd.View = System.Windows.Forms.View.Details;
            this.ListViewEndToEnd.VirtualMode = true;
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 40;
            // 
            // olvColumnMoFileName
            // 
            this.olvColumnMoFileName.HeaderFont = null;
            this.olvColumnMoFileName.Text = "主叫文件";
            this.olvColumnMoFileName.Width = 250;
            // 
            // olvColumnMtFileName
            // 
            this.olvColumnMtFileName.HeaderFont = null;
            this.olvColumnMtFileName.Text = "被叫文件";
            this.olvColumnMtFileName.Width = 250;
            // 
            // olvColumnMessage
            // 
            this.olvColumnMessage.HeaderFont = null;
            this.olvColumnMessage.Text = "异常信令组合";
            this.olvColumnMessage.Width = 200;
            // 
            // olvColumnDate
            // 
            this.olvColumnDate.HeaderFont = null;
            this.olvColumnDate.Text = "信令开始日期";
            this.olvColumnDate.Width = 80;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "信令开始时间";
            this.olvColumnTime.Width = 80;
            // 
            // olvColumnMoPhone
            // 
            this.olvColumnMoPhone.HeaderFont = null;
            this.olvColumnMoPhone.Text = "主叫手机号码";
            // 
            // olvColumnMtPhone
            // 
            this.olvColumnMtPhone.HeaderFont = null;
            this.olvColumnMtPhone.Text = "被叫电话号码";
            // 
            // ctmStrip
            // 
            this.ctmStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripReplay,
            this.ToolStripExport});
            this.ctmStrip.Name = "ctmStrip";
            this.ctmStrip.Size = new System.Drawing.Size(125, 48);
            // 
            // ToolStripReplay
            // 
            this.ToolStripReplay.Name = "ToolStripReplay";
            this.ToolStripReplay.Size = new System.Drawing.Size(124, 22);
            this.ToolStripReplay.Text = "回放文件";
            this.ToolStripReplay.Click += new System.EventHandler(this.ToolStripReplay_Click);
            // 
            // ToolStripExport
            // 
            this.ToolStripExport.Name = "ToolStripExport";
            this.ToolStripExport.Size = new System.Drawing.Size(124, 22);
            this.ToolStripExport.Text = "导出文件";
            this.ToolStripExport.Click += new System.EventHandler(this.ToolStripExport_Click);
            // 
            // EndToEndResultListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(968, 572);
            this.ContextMenuStrip = this.ctmStrip;
            this.Controls.Add(this.ListViewEndToEnd);
            this.Name = "EndToEndResultListForm";
            this.Text = "端对端问题分析结果";
            ((System.ComponentModel.ISupportInitialize)(this.ListViewEndToEnd)).EndInit();
            this.ctmStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView ListViewEndToEnd;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnMoFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnDate;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnMtFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnMessage;
        private BrightIdeasSoftware.OLVColumn olvColumnMoPhone;
        private BrightIdeasSoftware.OLVColumn olvColumnMtPhone;
        private System.Windows.Forms.ContextMenuStrip ctmStrip;
        private System.Windows.Forms.ToolStripMenuItem ToolStripReplay;
        private System.Windows.Forms.ToolStripMenuItem ToolStripExport;
    }
}