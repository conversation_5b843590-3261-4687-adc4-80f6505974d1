﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTScanLTEInterferenceSetForm : DevExpress.XtraEditors.XtraForm
    {
        public ZTScanLTEInterferenceSetForm()
        {
            InitializeComponent();
            cbxFreqBandType.Properties.Items.Clear();
            cbxFreqBandType.Properties.Items.Add("F段");
            cbxFreqBandType.Properties.Items.Add("A段");
            cbxFreqBandType.Properties.Items.Add("E段");
            cbxFreqBandType.Properties.Items.Add("D段");
            cbxFreqBandType.SelectedIndex = 0;
        }

        public void GetSettingFilterRet(out InterCondition interCondition)
        {
            interCondition = new InterCondition();

            interCondition.freqStart = ((int)spinEditBandStart.Value) * 1000;
            interCondition.freqEnd = ((int)spinEditBandEnd.Value) * 1000;

            interCondition.top1Rxlev = (int)numTop1Rxlev.Value;
            interCondition.topNCount = (int)numTopNCount.Value;
            interCondition.topNRxlev = (int)numTopNRxlev.Value;
            interCondition.allRxlev = (int)numAllRxlev.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            int freqBandStart = (int)spinEditBandStart.Value;
            int freqBandEnd = (int)spinEditBandEnd.Value;

            if (freqBandStart >= freqBandEnd)
            {
                XtraMessageBox.Show("频段设置错误，结束频点要大于开始频点，请重新设置。");
                return;
            }

            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void cbxFreqBandType_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch (cbxFreqBandType.SelectedIndex)
            {
                case 0:
                    spinEditBandStart.Value = 1880;
                    spinEditBandEnd.Value = 1920;
                    break;
                case 1:
                    spinEditBandStart.Value = 2010;
                    spinEditBandEnd.Value = 2025;
                    break;
                case 2:
                    spinEditBandStart.Value = 2300;
                    spinEditBandEnd.Value = 2400;
                    break;
                case 3:
                    spinEditBandStart.Value = 2570;
                    spinEditBandEnd.Value = 2620;
                    break;
            }
        }
    }
}