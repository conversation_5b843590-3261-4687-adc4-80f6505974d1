﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public partial class CustomReportForm : BaseForm
    {
        private CustomReportForm()
        {
            InitializeComponent();
            rngColorPnl.SetScoreColumnCaption("值范围");
            rngColorPnl.DescColumnsVisible = false;
            init();
        }

        private void init()
        {
            this.treeList.BeginUpdate();
            this.treeList.Nodes.Clear();
            foreach (TableTemplate tb in TableCfgManager.Instance.Tables)
            {
                TreeListNode rootNode = treeList.AppendNode(new object[] { tb.TypeName }, null);
                rootNode.Tag = tb;
                foreach (TableColumn col in tb.Columns)
                {
                    TreeListNode pNode = treeList.AppendNode(new object[] { col.Name }, rootNode);
                    pNode.Tag = col;
                }
            }
            this.treeList.EndUpdate();
            MasterCom.Util.DevControlManager.TreeListHelper.ThreeStateControl(treeList);
        }

        void treeList_AfterCheckNode(object sender, DevExpress.XtraTreeList.NodeEventArgs e)
        {
            List<TableColumn> cols = new List<TableColumn>();
            bool chk = e.Node.Checked;
            if (e.Node.Tag is TableTemplate)
            {
                foreach (TreeListNode node in e.Node.Nodes)
                {
                    cols.Add(node.Tag as TableColumn);
                }
            }
            else if (e.Node.Tag is TableColumn)
            {
                cols.Add(e.Node.Tag as TableColumn);
            }
            updateTemplateCol(cols, chk);
        }

        void updateTemplateCol(List<TableColumn> cols, bool isAdd)
        {
            foreach (TableColumn col in cols)
            {
                ReportColunm rptCol = getReportColunm(col);
                if (isAdd)
                {
                    if (rptCol == null)
                    {
                        curTemplate.Columns.Add(new ReportColunm(col));
                    }
                }
                else if (rptCol != null)
                {
                    curTemplate.Columns.Remove(rptCol);
                }
            }
            gridCtrlCol.RefreshDataSource();
        }

        private ReportColunm getReportColunm(TableColumn col)
        {
            return curTemplate.Columns.Find(delegate (ReportColunm x)
            { return x.TableCol == col; });
        }

        public CustomReportForm(ReportTemplate template)
            : this()
        {
            fillTemplateList(template);
        }

        private void fillTemplateList(ReportTemplate selRpt)
        {
            gridCtrlTmpl.DataSource = ReportTemplateMng.Instance.Templates;
            gridCtrlTmpl.RefreshDataSource();
            if (selRpt != null)
            {
                int idx = ReportTemplateMng.Instance.Templates.IndexOf(selRpt);
                if (idx != -1)
                {
                    gvTmpl.FocusedRowHandle = gvTmpl.GetRowHandle(idx);
                }
            }
        }

        private void visualizeCurTemplate()
        {
            treeList.AfterCheckNode -= treeList_AfterCheckNode;
            btnRemoveTemplate.Enabled = groupControl3.Enabled = curTemplate != null;
            gridCtrlCol.DataSource = null;
            if (curTemplate != null)
            {
                gridCtrlCol.DataSource = curTemplate.Columns;
                foreach (TreeListNode node in treeList.Nodes)
                {
                    TableTemplate tb = node.Tag as TableTemplate;
                    node.Checked = false;
                    foreach (TreeListNode subNode in node.Nodes)
                    {
                        subNode.Checked = false;
                    }

                    int chkNum = getChkNum(node, tb);
                    if (chkNum > 0)
                    {
                        if (chkNum == node.Nodes.Count)
                        {
                            node.Checked = true;
                        }
                        else
                        {
                            node.CheckState = CheckState.Indeterminate;
                        }
                    }

                }

                treeList.AfterCheckNode += treeList_AfterCheckNode;
            }
            gridCtrlCol.RefreshDataSource();
        }

        private int getChkNum(TreeListNode node, TableTemplate tb)
        {
            int chkNum = 0;
            foreach (ReportColunm col in curTemplate.Columns)
            {
                if (col.TableCol.Table == tb)
                {
                    foreach (TreeListNode subNode in node.Nodes)
                    {
                        if (subNode.Tag == col.TableCol)
                        {
                            subNode.Checked = true;
                            chkNum++;
                            break;
                        }
                    }
                }
            }

            return chkNum;
        }

        private void btnNewReport_Click(object sender, EventArgs e)
        {
            TextInputBox box = new TextInputBox("新建模板", "模板名称", "未命名模板");
            if (box.ShowDialog() == DialogResult.OK)
            {
                ReportTemplate template = new ReportTemplate(box.TextInput);
                ReportTemplateMng.Instance.Templates.Add(template);
                gridCtrlTmpl.RefreshDataSource();
                gvTmpl.FocusedRowHandle = ReportTemplateMng.Instance.Templates.IndexOf(template);
            }
        }
        ReportTemplate curTemplate;
        private void btnRemoveTemplate_Click(object sender, EventArgs e)
        {
            ReportTemplate t = gvTmpl.GetRow(gvTmpl.FocusedRowHandle) as ReportTemplate;
            if (t != null)
            {
                int idx = ReportTemplateMng.Instance.Templates.IndexOf(t);
                if (idx != -1
                    && MessageBox.Show(this, "确定删除该模板？", "确认", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    ReportTemplateMng.Instance.Templates.RemoveAt(idx);
                    gridCtrlTmpl.RefreshDataSource();
                    curTemplate = gvTmpl.GetRow(gvTmpl.FocusedRowHandle) as ReportTemplate;
                    visualizeCurTemplate();
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            ReportTemplateMng.Instance.Save();
        }

        private void gvTmpl_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            curTemplate = gvTmpl.GetRow(e.FocusedRowHandle) as ReportTemplate;
            visualizeCurTemplate();
        }

        ReportColunm curCol = null;
        private void gvCol_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            curCol = gvCol.GetRow(e.FocusedRowHandle) as ReportColunm;
            if (curCol == null)
            {
                grpCol.Enabled = false;
                return;
            }
            grpCol.Enabled = true;
            grpCell.Enabled = curCol.TableCol.Table != TableCfgManager.Instance.AreaTable;

            foreach (TreeListNode node in treeList.Nodes)
            {
                if (node.Tag == curCol.TableCol.Table)
                {
                    foreach (TreeListNode subNode in node.Nodes)
                    {
                        if (subNode.Tag == curCol.TableCol)
                        {
                            treeList.FocusedNode = subNode;
                            break;
                        }
                    }
                    break;
                }
            }

            btnUp.Enabled = e.FocusedRowHandle != 0;
            btnDown.Enabled = e.FocusedRowHandle != curTemplate.Columns.Count - 1;

            txtCaption.Text = curCol.Name;
            chkFrozen.Checked = curCol.IsFrozen;
            colorColBk.Color = curCol.ColHeaderBkColor;
            rBtnStaticBk.Checked = curCol.IsStaticColor;
            colorStatic.Color = curCol.CellStaticBkColor;
            rBtnDynamic.Checked = !curCol.IsStaticColor;

            numRngMin.ValueChanged -= numRngMin_ValueChanged;
            numRngMax.ValueChanged -= numRngMax_ValueChanged;
            numRngMin.Value = (decimal)curCol.ValueRangeMin;
            numRngMax.Value = (decimal)curCol.ValueRangeMax;
            List<DTParameterRangeColor> colors = new List<DTParameterRangeColor>();
            if (curCol.CellDynamicBkColorRanges != null)
            {
                foreach (DTParameterRangeColor rngColor in curCol.CellDynamicBkColorRanges)
                {
                    colors.Add((DTParameterRangeColor)rngColor.Clone());
                }
            }
            rngColorPnl.SetScoreColorRanges(colors
                , curCol.ValueRangeMin, curCol.ValueRangeMax);
            numRngMin.ValueChanged += numRngMin_ValueChanged;
            numRngMax.ValueChanged += numRngMax_ValueChanged;
        }

        void numRngMax_ValueChanged(object sender, EventArgs e)
        {
            rngColorPnl.UpdateRange((double)numRngMin.Value, (double)numRngMax.Value);
        }

        void numRngMin_ValueChanged(object sender, EventArgs e)
        {
            rngColorPnl.UpdateRange((double)numRngMin.Value, (double)numRngMax.Value);
        }

        private void gvCol_DoubleClick(object sender, EventArgs e)
        {
            //
        }

        private void rBtnStaticBk_CheckedChanged(object sender, EventArgs e)
        {
            rngColorPnl.Enabled = rBtnDynamic.Checked;
            colorStatic.Enabled = rBtnStaticBk.Checked;
        }


        private void btnApply_Click(object sender, EventArgs e)
        {
            if (curCol == null)
            {
                return;
            }
            curCol.Name = txtCaption.Text;
            curCol.IsFrozen = chkFrozen.Checked;
            curCol.ColHeaderBkColor = colorColBk.Color;
            curCol.IsStaticColor = rBtnStaticBk.Checked;
            curCol.CellStaticBkColor = colorStatic.Color;

            curCol.ValueRangeMin = (double)numRngMin.Value;
            curCol.ValueRangeMax = (double)numRngMax.Value;

            curCol.CellDynamicBkColorRanges = rngColorPnl.Ranges;
        }

        private void btnUp_Click(object sender, EventArgs e)
        {
            sortColumn(true);
        }

        private void btnDown_Click(object sender, EventArgs e)
        {
            sortColumn(false);
        }

        private void sortColumn(bool isMovePre)
        {
            if (curTemplate == null || curCol == null)
            {
                return;
            }
            int idx = curTemplate.Columns.IndexOf(curCol);
            int newIdx = isMovePre ? idx - 1 : idx + 1;
            curTemplate.Columns.RemoveAt(idx);
            curTemplate.Columns.Insert(newIdx, curCol);
            gridCtrlCol.RefreshDataSource();
            gvCol.FocusedRowHandle = newIdx;
        }

    }
}
