﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRHandoverNCellInfo
    {
        public NRHandoverNCellInfo(string fileName)
        {
            FileName = fileName;
            NcellList = new List<NRNCellInfo>();
            TestPonitList = new List<TestPoint>();
        }
        //public int SN { get; set; }
        public string FileName { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public DateTime DateTime { get; set; }
        public string Time { get; set; }
        public string CellType { get; set; }
        public string CellName { get; set; }
        public string Earfcn { get; set; }
        public string Pci { get; set; }
        public string TAC { get; set; }
        public string NCI { get; set; }
        public float? Rsrp { get; set; }
        public int NCellCount { get { return NcellList.Count; } }

        public List<NRNCellInfo> NcellList { get; set; }
        public Event Evt{ get; set; }

        public List<TestPoint> TestPonitList { get; set; }
    }

    public class NRNCellInfo
    {
        public string CellType { get; set; }
        public int SN { get; set; }
        public string CellName { get; set; }
        public int? Earfcn { get; set; }
        public int? Pci { get; set; }
        public int? TAC { get; set; }
        public long? NCI { get; set; }
        public float? Rsrp { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
    }
}
