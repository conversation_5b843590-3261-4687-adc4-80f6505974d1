using System;
using System.Collections.Generic;
using System.Text;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.Drawing;

namespace  MasterCom.Util.UiEx
{
    internal static class NativeMethods
    {
        #region Constants
        public const int LVM_FIRST = 0x1000;
        public const int LVM_SETIMAGELIST = LVM_FIRST + 3;
        public const int LVM_SCROLL = LVM_FIRST + 20;
        public const int LVM_GETHEADER = LVM_FIRST + 31;
        public const int LVM_GETCOUNTPERPAGE = LVM_FIRST + 40;
        public const int LVM_SETITEMSTATE = LVM_FIRST + 43;
        public const int LVM_SETEXTENDEDLISTVIEWSTYLE = LVM_FIRST + 54;
        public const int LVM_SETITEM = LVM_FIRST + 76;
        public const int LVM_GETTOOLTIPS = 0x1000 + 78;
        public const int LVM_SETTOOLTIPS = 0x1000 + 74;
        public const int LVM_GETCOLUMN = LVM_FIRST + 95;
        public const int LVM_SETCOLUMN = LVM_FIRST + 96;
        public const int LVM_SETSELECTEDCOLUMN = LVM_FIRST + 140;
        public const int LVM_INSERTGROUP = LVM_FIRST + 145;
        public const int LVM_SETGROUPINFO = LVM_FIRST + 147;
        public const int LVM_GETGROUPINFO = LVM_FIRST + 149;
        public const int LVM_GETGROUPSTATE = LVM_FIRST + 92;
        public const int LVM_SETGROUPMETRICS = LVM_FIRST + 155;
        public const int LVM_REMOVEALLGROUPS = LVM_FIRST + 160;

        public const int LVS_EX_SUBITEMIMAGES = 0x0002;

        public const int LVIF_TEXT = 0x0001;
        public const int LVIF_IMAGE = 0x0002;
        public const int LVIF_PARAM = 0x0004;
        public const int LVIF_STATE = 0x0008;
        public const int LVIF_INDENT = 0x0010;
        public const int LVIF_NORECOMPUTE = 0x0800;

        public const int LVCF_FMT = 0x0001;
        public const int LVCF_WIDTH = 0x0002;
        public const int LVCF_TEXT = 0x0004;
        public const int LVCF_SUBITEM = 0x0008;
        public const int LVCF_IMAGE = 0x0010;
        public const int LVCF_ORDER = 0x0020;
        public const int LVCFMT_LEFT = 0x0000;
        public const int LVCFMT_RIGHT = 0x0001;
        public const int LVCFMT_CENTER = 0x0002;
        public const int LVCFMT_JUSTIFYMASK = 0x0003;

        public const int LVCFMT_IMAGE = 0x0800;
        public const int LVCFMT_BITMAP_ON_RIGHT = 0x1000;
        public const int LVCFMT_COL_HAS_IMAGES = 0x8000;

        public const int HDM_FIRST = 0x1200;
        public const int HDM_HITTEST = HDM_FIRST + 6;
        public const int HDM_GETITEMRECT = HDM_FIRST + 7;
        public const int HDM_GETITEM = HDM_FIRST + 11;
        public const int HDM_SETITEM = HDM_FIRST + 12;

        public const int HDI_WIDTH = 0x0001;
        public const int HDI_TEXT = 0x0002;
        public const int HDI_FORMAT = 0x0004;
        public const int HDI_BITMAP = 0x0010;
        public const int HDI_IMAGE = 0x0020;

        public const int HDF_LEFT = 0x0000;
        public const int HDF_RIGHT = 0x0001;
        public const int HDF_CENTER = 0x0002;
        public const int HDF_JUSTIFYMASK = 0x0003;
        public const int HDF_RTLREADING = 0x0004;
        public const int HDF_STRING = 0x4000;
        public const int HDF_BITMAP = 0x2000;
        public const int HDF_BITMAP_ON_RIGHT = 0x1000;
        public const int HDF_IMAGE = 0x0800;
        public const int HDF_SORTUP = 0x0400;
        public const int HDF_SORTDOWN = 0x0200;

        public const int SB_HORZ = 0;
        public const int SB_VERT = 1;
        public const int SB_CTL = 2;
        public const int SB_BOTH = 3;

        public const int SIF_RANGE = 0x0001;
        public const int SIF_PAGE = 0x0002;
        public const int SIF_POS = 0x0004;
        public const int SIF_DISABLENOSCROLL = 0x0008;
        public const int SIF_TRACKPOS = 0x0010;
        public const int SIF_ALL = (SIF_RANGE | SIF_PAGE | SIF_POS | SIF_TRACKPOS);

        public const int ILD_NORMAL = 0x0;
        public const int ILD_TRANSPARENT = 0x1;
        public const int ILD_MASK = 0x10;
        public const int ILD_IMAGE = 0x20;
        public const int ILD_BLEND25 = 0x2;
        public const int ILD_BLEND50 = 0x4;

        const int SWP_NOSIZE = 1;
        const int SWP_NOMOVE = 2;
        const int SWP_NOZORDER = 4;
        const int SWP_NOREDRAW = 8;
        const int SWP_NOACTIVATE = 16;
        public const int SWP_FRAMECHANGED = 32;

        public const int SWP_zOrderOnly = SWP_NOSIZE | SWP_NOMOVE | SWP_NOREDRAW | SWP_NOACTIVATE;
        public const int SWP_sizeOnly = SWP_NOMOVE | SWP_NOREDRAW | SWP_NOZORDER | SWP_NOACTIVATE;
        public const int SWP_updateFrame = SWP_NOSIZE | SWP_NOMOVE | SWP_NOACTIVATE | SWP_NOZORDER | SWP_FRAMECHANGED;

        #endregion

        public static bool MakeTopMost(IWin32Window toBeMoved)
        {
            IntPtr HWND_TOPMOST = (IntPtr)(-1);
            return NativeMethods.SetWindowPos(toBeMoved.Handle, HWND_TOPMOST, 0, 0, 0, 0, SWP_zOrderOnly);
        }

        static public void ShowWithoutActivate(IWin32Window win)
        {
            const int SW_SHOWNA = 8;
            NativeMethods.ShowWindow(win.Handle, SW_SHOWNA);
        }
        #region Entry points

        // Various flavours of SendMessage: plain vanilla, and passing references to various structures
        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern IntPtr SendMessage(IntPtr hWnd, int msg, int wParam, int lParam);
        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        public static extern IntPtr SendMessage(IntPtr hWnd, int msg, IntPtr wParam, int lParam);
        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        public static extern IntPtr SendMessage(IntPtr hWnd, int msg, int wParam, IntPtr lParam);

        [DllImport("user32.dll", EntryPoint = "SendMessage", CharSet = CharSet.Auto)]
        public static extern IntPtr SendMessageString(IntPtr hWnd, int Msg, int wParam, string lParam);
        [DllImport("user32.dll", EntryPoint = "SendMessage", CharSet = CharSet.Auto)]
        public static extern IntPtr SendMessageIUnknown(IntPtr hWnd, int msg,
            [MarshalAs(UnmanagedType.IUnknown)] object wParam, int lParam);

        [DllImport("gdi32.dll")]
        public static extern bool DeleteObject(IntPtr objectHandle);

        [DllImport("user32.dll", CharSet = CharSet.Auto, ExactSpelling = true)]
        public static extern bool GetClientRect(IntPtr hWnd, ref Rectangle r);


        [DllImport("user32.dll", EntryPoint = "GetUpdateRect", CharSet = CharSet.Auto)]
        private static extern int GetUpdateRectInternal(IntPtr hWnd, ref Rectangle r, bool eraseBackground);

        [DllImport("comctl32.dll", CharSet = CharSet.Auto)]
        private static extern bool ImageList_Draw(IntPtr himl, int i, IntPtr hdcDst, int x, int y, int fStyle);

        [DllImport("user32.dll")]
        public static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter,
            int X, int Y, int cx, int cy, uint uFlags);

        [DllImport("user32.dll", EntryPoint = "GetWindowLong", CharSet = CharSet.Auto)]
        public static extern IntPtr GetWindowLong32(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll", EntryPoint = "GetWindowLongPtr", CharSet = CharSet.Auto)]
        public static extern IntPtr GetWindowLongPtr64(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll", EntryPoint = "SetWindowLong", CharSet = CharSet.Auto)]
        public static extern IntPtr SetWindowLongPtr32(IntPtr hWnd, int nIndex, int dwNewLong);

        [DllImport("user32.dll", EntryPoint = "SetWindowLongPtr", CharSet = CharSet.Auto)]
        public static extern IntPtr SetWindowLongPtr64(IntPtr hWnd, int nIndex, int dwNewLong);

        [DllImport("user32.dll")]
        public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll", EntryPoint = "ValidateRect", CharSet = CharSet.Auto)]
        private static extern IntPtr ValidatedRectInternal(IntPtr hWnd, ref Rectangle r);

        #endregion
    }
}
