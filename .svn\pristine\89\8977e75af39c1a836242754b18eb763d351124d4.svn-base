﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

namespace MasterCom.RAMS.Func
{
    public enum ScanGridAnaStatType
    {
        Coverage,
        Rxlev,
        Compare,
    }

    public class ScanGridAnaStater
    {
        private readonly ScanGridAnaResult anaResult;
        private readonly ScanGridAnaResult cmpResult;
        private ScanGridAnaCoverageStater covStater;
        private ScanGridAnaAppearStater appStater;
        private ScanGridAnaRxlevStater rxlevStater;
        private readonly ScanGridAnaColorRanger colorRanger;
        private ScanGridAnaGridBlockStater blockStater;

        public ScanGridAnaStater(ScanGridAnaResult anaResult, ScanGridAnaResult cmpResult)
        {
            this.anaResult = anaResult;
            this.cmpResult = cmpResult;
            colorRanger = ScanGridAnaColorRanger.Instance;
        }

        public void Stat()
        {
            covStater = new ScanGridAnaCoverageStater(anaResult, cmpResult, colorRanger.GetColorRanges(ScanGridAnaRangeType.Coverage));
            rxlevStater = new ScanGridAnaRxlevStater(anaResult, cmpResult, colorRanger.GetColorRanges(ScanGridAnaRangeType.Rxlev));
            appStater = new ScanGridAnaAppearStater(anaResult, cmpResult);
            blockStater = new ScanGridAnaGridBlockStater(anaResult);

            covStater.Stat();
            rxlevStater.Stat();
            appStater.Stat();
            blockStater.Stat();
        }

        public void Stat(ScanGridAnaRangeType rangeType)
        {
            if (rangeType == ScanGridAnaRangeType.Coverage)
            {
                covStater = new ScanGridAnaCoverageStater(anaResult, cmpResult, colorRanger.GetColorRanges(ScanGridAnaRangeType.Coverage));
                covStater.Stat();
            }
            else if (rangeType == ScanGridAnaRangeType.Compare)
            {
                appStater = new ScanGridAnaAppearStater(anaResult, cmpResult);
                appStater.Stat();
            }
            else if (rangeType == ScanGridAnaRangeType.Rxlev)
            {
                rxlevStater = new ScanGridAnaRxlevStater(anaResult, cmpResult, colorRanger.GetColorRanges(ScanGridAnaRangeType.Rxlev));
                rxlevStater.Stat();
            }
        }

        public List<DataTable> GetResult(ScanGridAnaStatType statType, ScanGridAnaGridType netType, int covType)
        {
            if (statType == ScanGridAnaStatType.Coverage && covStater != null)
            {
                return covStater.GetResult(netType, covType);
            }
            if (statType == ScanGridAnaStatType.Rxlev && rxlevStater != null)
            {
                return rxlevStater.GetResult(netType);
            }
            if (statType == ScanGridAnaStatType.Compare && cmpResult != null && appStater != null)
            {
                return appStater.GetResult(netType);
            }
            return new List<DataTable>();
        }

        public List<ScanGridAnaGridInfo[]> GetResult(ScanGridAnaGridType netType, ScanGridAnaRangeType rangeType)
        {
            return blockStater.GetResult(netType, rangeType);
        }

        public List<ScanGridAnaGridBlockLevelOne> GetShowResult(ScanGridAnaGridType netType, ScanGridAnaRangeType rangeType)
        {
            return blockStater.GetShowList(netType, rangeType);
        }
    }
}
