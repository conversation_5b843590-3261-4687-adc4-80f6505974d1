﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SetQueryFormQualRelateMultiCov : DataComparisonForm
    {
        public SetQueryFormQualRelateMultiCov(MainModel mModel, MapFormItemSelection itemSelection, QueryCondition condition)
            :base(mModel,itemSelection,condition)
        {
            InitializeComponent();
        }

        protected override void buttonQuery_Click(object sender, EventArgs e)
        {
            Func.conditionRecorder recorder = new Func.conditionRecorder();
            recorder.kind = Func.conditionRecorder.Kind.Common;
            recorder.dateTimePickerBeginTimeDTValue = dateTimePickerBeginTimeDT.Value;
            recorder.dateTimePickerBeginTimeScanValue = dateTimePickerBeginTimeScan.Value;
            recorder.dateTimePickerEndTimeDTValue = dateTimePickerEndTimeDT.Value;
            recorder.dateTimePickerEndTimeScanValue = dateTimePickerEndTimeScan.Value;
            recorder.fill(this.listViewProjectDT.Items, this.listViewProjectScan.Items, this.listViewServiceDT.Items, this.listViewServiceScan.Items);
            mainModel.ConditionRecorderCommon = recorder;

            if (GetPeriodProjServDT() && GetPeriodProjServScan())
                DialogResult = DialogResult.OK;
            else
                DialogResult = DialogResult.Cancel;
        }
    }
}
