﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;
using FileInfo = MasterCom.RAMS.Model.FileInfo;

namespace MasterCom.RAMS.ZTFunc
{
    public class NROptimizationcCusters : NROptimizationcCustersBase
    {
        public NROptimizationcCusters(MainModel mainModel)
        {
        }



        public override string Name
        {
            get
            {
                return "5G簇优化统计";
            }
        }

        protected override bool isValidCondition()
        {
            List<FileInfo> nrFiles = new List<FileInfo>();
            foreach (var file in Condition.FileInfos)
            {
                ServiceName name = ServiceTypeManager.getServiceNameFromTypeID(file.ServiceType);
                if (name == ServiceName.NR)
                {
                    nrFiles.Add(file);
                }
            }
            if (nrFiles.Count == 0)
            {
                MessageBox.Show("请选择5G文件进行簇优化统计");
                return false;
            }
            Condition.FileInfos = nrFiles;

            NRStationAcceptCondition cond = new NRStationAcceptCondition();
            cond.Init();
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                cond.SaveFolder = dlg.SelectedPath;
                this.SaveFolder = dlg.SelectedPath;
                this.custersName = nrFiles[0].Name.Split(new char[] { '_' })[0];
                NRPicture nRPicture = new NRPicture(mainModel, condition);
                nRPicture.CreatePicture(cond);
                this.btsInfoDic = NROptimizationcCustersBaseInfo.btsDic;
                return true;
            }
            return false;
        }
    }

    public class NRPicture : NROptimizationcCustersBaseInfo
    {
        NRStationAcceptCondition nRStationAcceptCondition = new NRStationAcceptCondition();
        public NRPicture(MainModel mainModel)
   : base(mainModel)
        {

        }

        public NRPicture(MainModel mainModel, QueryCondition queryCondition)
: base(mainModel)
        {
            condition = queryCondition;
        }

        protected override bool isValidCondition()
        {
            initManager(nRStationAcceptCondition);
            query();
            return true;
        }

        public void CreatePicture(NRStationAcceptCondition cond)
        {
            nRStationAcceptCondition = cond;
            isValidCondition();
        }

        protected override void initManager(StationAcceptConditionBase cond)
        {
            string workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/NROptimizationcCusters");
            string picPath = Path.Combine(workDir, "Pictures");
            List<StationAcceptBase> acceptorList = new List<StationAcceptBase>()
            {
                 new NRAcpCoverPic(picPath),
                 new NRAcpHandoverPic(picPath)
            };
        }

        class NRAcpCoverPic : NRStationAcceptCoverPic
        {
            public NRAcpCoverPic(string picPath)
                : base(picPath)
            {
                reSetMapView("NR_SS_RSRP", new Func(getRsrpRanges), "");
                reSetMapView("NR_SS_SINR", new Func(getSinrRanges), "");
            }

            protected List<RangeInfo> getRsrpRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                ranges.Add(new RangeInfo(false, false, -145, -110, Color.Red));
                ranges.Add(new RangeInfo(true, false, -110, -90, Color.Orange));
                ranges.Add(new RangeInfo(true, false, -90, -80, Color.Yellow));
                ranges.Add(new RangeInfo(true, false, -80, -70, Color.Green));
                ranges.Add(new RangeInfo(true, false, -70, -30, Color.Blue));
                return ranges;
            }

            protected List<RangeInfo> getSinrRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                ranges.Add(new RangeInfo(false, false, -20, 3, Color.Red));
                ranges.Add(new RangeInfo(true, false, 3, 10, Color.Orange));
                ranges.Add(new RangeInfo(true, false, 10, 16, Color.Yellow));
                ranges.Add(new RangeInfo(true, false, 16, 25, Color.Green));
                ranges.Add(new RangeInfo(true, false, 25, 50, Color.Blue));
                return ranges;
            }

            protected override bool isValidFile(FileInfo fileInfo)
            {
                return fileInfo.Name.Contains("DT上传") || fileInfo.Name.Contains("DT下载");
            }

            protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellServiceInfo cellTypeInfo, NRStationAcceptCondition condition)
            {
                MTGis.DbRect bounds = getCoverBounds(fileManager, cellTypeInfo.Cell.Longitude, cellTypeInfo.Cell.Latitude);
                string paramName;
                string path;
                string band = getBandWidth(fileInfo.Name);

                MapForm mf = initMap(bounds);

                string serviceType = "";
                if (condition.NRServiceType == NRServiceName.NSA)
                {
                    serviceType = "NSA_";
                }
                else if (condition.NRServiceType == NRServiceName.SA)
                {
                    serviceType = "SA_";
                }
                string cellName = serviceType + cellTypeInfo.Cell.Name;

                if (fileInfo.Name.Contains("DT下载") || string.IsNullOrEmpty(cellTypeInfo.NRRsrpPic.PicPath))
                {
                    paramName = "NR_SS_RSRP";
                    path = getCoverPicPath(bts.BtsName, cellName, paramName);
                    cellTypeInfo.NRRsrpPic.PicPath = fireMapAndTakePic(mf, paramName, path);

                    paramName = "NR_lte_RSRP";
                    path = getCoverPicPath(bts.BtsName, cellName, paramName);
                    cellTypeInfo.LteRsrpPic.PicPath = fireMapAndTakePic(mf, paramName, path);

                    paramName = "NR_SS_SINR";
                    path = getCoverPicPath(bts.BtsName, cellName, paramName);
                    cellTypeInfo.NRSinrPic.PicPath = fireMapAndTakePic(mf, paramName, path);

                    paramName = "NR_lte_SINR";
                    path = getCoverPicPath(bts.BtsName, cellName, paramName);
                    cellTypeInfo.LteSinrPic.PicPath = fireMapAndTakePic(mf, paramName, path);
                }

                if (fileInfo.Name.Contains("DT上传"))
                {
                    paramName = "NR_Throughput_MAC_UL_Mb";
                    reSetMapView(paramName, new Func(getULRanges), band);
                    path = getCoverPicPath(bts.BtsName, cellName, paramName);
                    cellTypeInfo.NRULPic.PicPath = fireMapAndTakePic(mf, paramName, path);
                }
                else
                {
                    paramName = "NR_Throughput_MAC_DL_Mb";
                    reSetMapView(paramName, new Func(getDLRanges), band);
                    path = getCoverPicPath(bts.BtsName, cellName, paramName);
                    cellTypeInfo.NRDLPic.PicPath = fireMapAndTakePic(mf, paramName, path);
                }
            }


            protected List<RangeInfo> getDLRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                switch (band)
                {
                    default:
                        ranges.Add(new RangeInfo(false, false, 0, 10, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 10, 100, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 100, 400, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 400, 700, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 700, 10000, Color.Blue));
                        break;
                }
                return ranges;
            }

            protected List<RangeInfo> getULRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                switch (band)
                {
                    default:
                        ranges.Add(new RangeInfo(false, false, 0, 10, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 10, 25, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 25, 75, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 75, 100, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 100, 10000, Color.Blue));
                        break;
                }
                return ranges;
            }
        }

        class NRAcpHandoverPic : StationAcceptHanOverPic
        {
            public NRAcpHandoverPic(string picPath)
                : base(picPath)
            {

            }

            private const string pciParamName = "NR_PCI";

            protected override bool isValidFile(FileInfo fileInfo)
            {
                return fileInfo.Name.Contains("切换");
            }

            protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
            {
                NRBtsInfo nrBts = bts as NRBtsInfo;
                NRStationAcceptCondition nrCondition = condition as NRStationAcceptCondition;
                reSetPCIMapView(nrBts.Bts, nrBts.CurFileBtsName, pciParamName);

                MTGis.DbRect bounds;
                if (nrBts.CurFileBtsName == nrBts.BtsName)
                {
                    bounds = getCoverBounds(fileManager, nrBts.Bts.Longitude, nrBts.Bts.Latitude);
                }
                else
                {
                    bounds = getCoverBounds(fileManager);
                }

                MapForm mf = initMap(bounds);
                MainModel.GetInstance().DrawDifferentServerColor = true;
                if (nrCondition.NRServiceType == NRServiceName.NSA)
                {
                    if (!nrBts.NSABtsInfo.FileBtsHandOverInfoDic.TryGetValue(nrBts.CurFileBtsName, out BtsHandoverInfo info))
                    {
                        info = new BtsHandoverInfo(nrBts.CurFileBtsName);
                        nrBts.NSABtsInfo.FileBtsHandOverInfoDic.Add(nrBts.CurFileBtsName, info);
                    }

                    string path = getCoverPicPath(nrBts.BtsName, "NSA_" + nrBts.CurFileBtsName, pciParamName);
                    info.PCIPicInfo.PicPath = fireMapAndTakePic(mf, pciParamName, path);
                }
                else if (nrCondition.NRServiceType == NRServiceName.SA)
                {
                    if (!nrBts.SABtsInfo.FileBtsHandOverInfoDic.TryGetValue(nrBts.CurFileBtsName, out BtsHandoverInfo info))
                    {
                        info = new BtsHandoverInfo(nrBts.CurFileBtsName);
                        nrBts.SABtsInfo.FileBtsHandOverInfoDic.Add(nrBts.CurFileBtsName, info);
                    }

                    string path = getCoverPicPath(nrBts.BtsName, "SA_" + nrBts.CurFileBtsName, pciParamName);
                    info.PCIPicInfo.PicPath = fireMapAndTakePic(mf, pciParamName, path);
                }

                MainModel.GetInstance().DrawDifferentServerColor = false;
            }

            protected override string fireMapAndTakePic(MapForm mf, string paramName, string filePath)
            {
                //要让小区显示在采样点之上
                var nrLayer = mf.GetNRCellLayer();
                mf.MakeSureCustomLayerVisible(nrLayer, true);

                return base.fireMapAndTakePic(mf, paramName, filePath);
            }

            protected override List<ICell> getValidCells(ISite bts, string btsNamePostfix)
            {
                List<ICell> cellList = new List<ICell>();
                if (bts is NRBTS)
                {
                    NRBTS nrBts = bts as NRBTS;
                    foreach (NRCell cell in nrBts.Cells)
                    {
                        if (cell.Name.Contains(btsNamePostfix))
                        {
                            cellList.Add(cell);
                        }
                    }
                }
                return cellList;
            }

            protected override List<int> getPCIList(List<ICell> cellList)
            {
                List<int> pciList = new List<int>();
                foreach (ICell cell in cellList)
                {
                    NRCell nrCell = cell as NRCell;
                    pciList.Add(nrCell.PCI);
                }
                return pciList;
            }

            protected override void setServerCellColorByPCI(List<ICell> cellList, List<RangeInfo> ranges)
            {
                foreach (ICell cell in cellList)
                {
                    NRCell nrCell = cell as NRCell;
                    foreach (RangeInfo range in ranges)
                    {
                        if (nrCell.PCI >= range.Min && nrCell.PCI < range.Max)
                        {
                            nrCell.ServerCellColor = range.RangeColor;
                            break;
                        }
                    }
                }
            }
        }

        protected override void query()
        {
            MainModel.FireDTDataChanged(MainModel.MainForm);
            base.query();
        }

        protected override List<string> queryColumns
        {
            get
            {
                return new List<string>()
                {
                     "isampleid",
                     "itime",
                     "ilongitude",
                     "ilatitude",
                     "NR_SSB_ARFCN",
                     "NR_PCI",
                     "NR_SS_RSRP",
                     "NR_SS_SINR",
                     "NR_Throughput_MAC_DL",
                     "NR_Throughput_MAC_UL",
                     "NR_APP_type",
                     "NR_APP_Status",

                     "NR_lte_TAC",
                     "NR_lte_ECI",
                     "NR_lte_EARFCN",
                     "NR_lte_PCI",
                     "NR_lte_RSRP",
                     "NR_lte_RSRQ",
                     "NR_lte_RSSI",
                     "NR_lte_SINR",
                };
            }
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            foreach (string col in queryColumns)
            {
                List<ColumnDefItem> items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                option.SampleColumns.AddRange(items);
            }

            option.EventInclude = true;
            option.MessageInclude = true;

            return option;
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            base.queryReplayInfo(clientProxy, package, fileInfo);
            if (judgeValidFile(fileInfo))
            {
                analyzeFile(fileInfo);
                MainModel.DTDataManager.Clear();
            }
        }
    }

    public class NROptimizationcCustersBaseInfo : DIYReplayFileQuery
    {
        public NROptimizationcCustersBaseInfo(MainModel mainModel)
 : base(mainModel)
        {
        }

        protected StationAcceptManagerBase manager;
        protected string errMsg;
        public static Dictionary<string, BtsInfoBase> btsDic { get; set; }

        /// <summary>
        /// 子类必须重写
        /// </summary>
        protected virtual void initManager(StationAcceptConditionBase cond)
        {
            manager.SetAcceptCond(cond);
        }

        /// <summary>
        /// 子类必须重写
        /// </summary>
        protected virtual List<string> queryColumns
        {
            get
            {
                return new List<string>()
                {
                     "isampleid",
                     "itime",
                     "ilongitude",
                     "ilatitude"
                };
            }
        }

        protected virtual bool judgeValidFile(FileInfo fileInfo)
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return false;
            }
            return true;
        }

        protected virtual void analyzeFile(FileInfo fileInfo)
        {
            manager.AnalyzeFile(fileInfo, MainModel.DTDataManager.FileDataManagers[0]);
        }

        protected override void doPostReplayAction()
        {
            afterAnalyzeInThread();
        }

        protected virtual void afterAnalyzeInThread()
        {
            btsDic = manager.getBtsInfo();
        }
    }
}
