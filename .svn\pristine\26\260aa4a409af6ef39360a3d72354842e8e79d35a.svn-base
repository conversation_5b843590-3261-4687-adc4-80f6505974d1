﻿using MapWinGIS;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    public static class WirelessNetTestQueryHelper
    {
        #region 查询地市在设置时间段内的有效文件
        public static List<FileInfo> GetFileList(WirelessNetTestCond anaCondition, int districtID, List<int> projects)
        {
            WaitBox.ProgressPercent += 10;
            MainModel model = MainModel.GetInstance();
            QueryCondition cond = new QueryCondition();
            cond.DistrictID = districtID;
            cond.DistrictIDs = new List<int>() { districtID };
            cond.Areas = new Dictionary<int, List<int>>();
            cond.Periods.Add(anaCondition.TestPeriod);
            cond.CarrierTypes.AddRange(anaCondition.Carrier);
            cond.Projects.AddRange(projects);

            List<ServiceType> serviceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            serviceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTETDDAndFDD));
            serviceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTETDDAndFDD));
            foreach (ServiceType item in serviceTypes)
            {
                cond.ServiceTypes.Add((int)item);
            }

            model.MainForm.SetQueryConditionBySettings(cond);
            DIYQueryFileInfo query = new DIYQueryFileInfo(model);
            query.SetQueryCondition(cond);
            query.IsShowFileInfoForm = false;
            query.Query();
            WaitBox.ProgressPercent += 10;
            WirelessNetTestConfig.Instance.WriteLog($"共查到文件[{model.FileInfos.Count}]个");

            return model.FileInfos;
        }

        //获取各级不同运营商对应的文件
        public static Dictionary<WirelessNetTestProjectType, Dictionary<int, Dictionary<FormulaType, List<FileInfo>>>> ClassifyFiles(WirelessNetTestCond anaCondition, List<FileInfo> fileInfos, string districtName)
        {
            var carrierFileInfoDic = anaCondition.InitProjectTypeDic<Dictionary<int, Dictionary<FormulaType, List<FileInfo>>>>();
            WirelessNetTestSceneCell.Instance.FileSceneDic = new Dictionary<int, WirelessNetTestScene>();
            WirelessNetTestSceneCell.Instance.FileSubSceneDic = new Dictionary<int, WirelessNetTestSubScene>();

            if (!WirelessNetTestSceneCell.Instance.DistrictDic.TryGetValue(districtName, out var districtScene))
            {
                WirelessNetTestConfig.Instance.WriteLog($"不存在[{districtName}]的场景信息", "Error");
            }

            foreach (var file in fileInfos)
            {
                var type = anaCondition.GetProjectType(file.ProjectID);
                bool isValid = getValidSceneFile(districtScene, file, type);
                if (isValid)
                {
                    addFile(anaCondition, carrierFileInfoDic, file, type);
                }
            }

            //输出分类后的日志
            foreach (var carrierFiles in carrierFileInfoDic)
            {
                foreach (var files in carrierFiles.Value)
                {
                    WirelessNetTestConfig.Instance.WriteLog($"[{carrierFiles.Key}][{files.Key}]文件[{files.Value.Count}]个");
                }
            }

            return carrierFileInfoDic;
        }

        private static void addFile(WirelessNetTestCond anaCondition, Dictionary<WirelessNetTestProjectType, Dictionary<int, Dictionary<FormulaType, List<FileInfo>>>> carrierFileInfoDic, FileInfo file, WirelessNetTestProjectType type)
        {
            if (carrierFileInfoDic.TryGetValue(type, out var carrierFiles))
            {
                if (!carrierFiles.TryGetValue(file.CarrierType, out var serviceFiles))
                {
                    serviceFiles = new Dictionary<FormulaType, List<FileInfo>>();
                    carrierFiles.Add(file.CarrierType, serviceFiles);
                }

                var serviceType = anaCondition.GetFileFormulaType(file.ServiceType);
                if (serviceType != FormulaType.全部)
                {
                    if (!serviceFiles.TryGetValue(serviceType, out var files))
                    {
                        files = new List<FileInfo>();
                        serviceFiles.Add(serviceType, files);
                    }
                    files.Add(file);
                }
            }
        }

        private static bool getValidSceneFile(WirelessNetTestDistrictInfo districtScene, FileInfo file, WirelessNetTestProjectType type)
        {
            bool isValid = false;
            //对于高铁,地铁,高速,机场文件,根据文件名判断是否存在对应场景,不存在则剔除文件
            switch (type)
            {
                case WirelessNetTestProjectType.高铁:
                case WirelessNetTestProjectType.地铁:
                case WirelessNetTestProjectType.高速:
                case WirelessNetTestProjectType.机场:
                    if (districtScene != null
                        && districtScene.SceneTypeDic.TryGetValue(type, out var sceneDic))
                    {
                        isValid = WirelessNetTestSceneCell.Instance.SetValidFileScene(file, sceneDic);
                    }
                    break;
                case WirelessNetTestProjectType.城区:
                case WirelessNetTestProjectType.区县:
                    isValid = true;
                    break;
            }

            return isValid;
        }
        #endregion

        #region 从数据库查询指标数据
        public static KpiData QueryKpiData(WirelessNetTestCond anaCondition, int districtID, WirelessNetTestProjectType type, int carrier
            , FormulaType service, List<FileInfo> files)
        {
            WirelessNetTestConfig.Instance.WriteLogWithWaitBox($"正在查询[{type}]的栅格数据...");
            var kpidata = new KpiData();
            var img = new List<string>();
            //在查询时只查询对应业务用到的指标,这样在统计时就可以直接合并结果
            foreach (var formula in WirelessNetTestReport.Instance.FormulaDic[service])
            {
                img.Add(formula.Formula);
            }
            if (img.Count == 0)
            {
                return kpidata;
            }
            if (service == FormulaType.数据)
            {
                //数据业务文件计算速率
                img.Add(WirelessNetTestReport.Instance.CarSpeedFormula);
            }

            List<int> services = anaCondition.FormulaTypeDic[service];
            List<int> projects = anaCondition.ProjectDic[type].ProjectList;

            QueryCondition cond = new QueryCondition();
            cond.DistrictID = districtID;
            cond.DistrictIDs = new List<int>() { districtID };
            cond.Areas = new Dictionary<int, List<int>>();
            cond.CarrierTypes.Add(carrier);
            cond.Projects.AddRange(projects);

            switch (type)
            {
                case WirelessNetTestProjectType.城区:
                    if (WirelessNetTestMap.Instance.CityGridDic.Count > 0)
                    {
                        kpidata = queryKpiByGrid(cond, districtID, anaCondition, services, files, img, WirelessNetTestMap.Instance.CityMergeShape);
                    }
                    break;
                case WirelessNetTestProjectType.区县:
                    if (WirelessNetTestMap.Instance.CountyDic.Count > 0)
                    {
                        kpidata = queryKpiByGrid(cond, districtID, anaCondition, services, files, img, WirelessNetTestMap.Instance.CountyMergeShape);
                    }
                    break;
                case WirelessNetTestProjectType.高铁:
                case WirelessNetTestProjectType.地铁:
                case WirelessNetTestProjectType.高速:
                case WirelessNetTestProjectType.机场:
                    kpidata = queryKpiByLog(cond, districtID, files, img);
                    break;
            }

            WirelessNetTestConfig.Instance.WriteLog($"查询到[{districtID}_{carrier}_{service}_{type}]数据指标[{kpidata.DataList.Count}]条;查询到事件指标[{kpidata.EventList.Count}]条;");
            return kpidata;
        }

        private static KpiData queryKpiByLog(QueryCondition cond, int districtID, List<FileInfo> files, List<string> img)
        {
            cond.FileInfos.AddRange(files);

            DiyQueryKpiByLog query = new DiyQueryKpiByLog(districtID, img);
            query.SetQueryCondition(cond);
            query.Query();
            var kpidata = new KpiData()
            {
                DataList = query.DataList,
                EventList = query.EventList
            };
            return kpidata;
        }

        private static KpiData queryKpiByGrid(QueryCondition cond, int districtID, WirelessNetTestCond anaCondition, List<int> services, List<FileInfo> files, List<string> img, Shape shape)
        {
            cond.Periods.Add(anaCondition.TestPeriod);
            cond.ServiceTypes.AddRange(services);
            cond.NameFilterType = FileFilterType.ByMark_ID;
            StringBuilder sb = new StringBuilder();
            foreach (FileInfo file in files)
            {
                sb.Append(file.ID);
                sb.Append(",");
            }
            cond.FileName = sb.ToString().TrimEnd(',');
            cond.Geometorys = new SearchGeometrys();
            cond.Geometorys.Region = shape;

            DiyQueryKpiByGrid query = new DiyQueryKpiByGrid(districtID, img);
            query.SetQueryCondition(cond);
            query.Query();

            var kpidata = new KpiData()
            {
                DataList = query.DataList,
                EventList = query.EventList
            };
            return kpidata;
        }

        public class KpiData
        {
            public List<KPIStatDataBase> DataList { get; set; } = new List<KPIStatDataBase>();
            public List<KPIStatDataBase> EventList { get; set; } = new List<KPIStatDataBase>();
        }
        #endregion
    }
}
