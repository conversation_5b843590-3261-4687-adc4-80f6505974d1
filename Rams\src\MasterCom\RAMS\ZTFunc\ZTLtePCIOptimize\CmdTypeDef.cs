﻿using System;
using System.Collections.Generic;
//using System.Linq;
using System.Text;
using System.IO;
using System.Net;
using System.Net.Sockets;

namespace MasterCom.RAMS.ZTFunc
{
    public static class TDCmdTypeDef
    {
        public const ushort Cell_Interfere = 0x0001;//查询小区当前干扰评估值
        public const ushort Freq_Arrange = 0x0002;//排频排扰计算
        public const ushort Freq_Eval = 0x0003;
        public const ushort Freq_RelaList = 0x0004;
        public const ushort Freq_Assess = 0x0005;
        public const ushort Rcv_Freq_ArrangePause = 0x8001;
        public const ushort Rcv_Freq_ArrangeProg = 0x8002;
        public const ushort Rcv_Freq_EvalResult = 0x8003;
        public const ushort Rcv_Freq_Freq_RelaListResult = 0x8004;
        public const ushort Rcv_Freq_ArrangeResult = 0x8012;
    }
    public static class LTECmdTypeDef
    {
        public const ushort Cell_Interfere = 0x0081;//查询小区当前干扰评估值
        public const ushort Freq_Arrange = 0x0002;//排频排扰计算
        public const ushort Freq_Eval = 0x0083;
        public const ushort Freq_RelaList = 0x0084;
        public const ushort Freq_Assess = 0x0085;
        public const ushort Rcv_Freq_ArrangePause = 0x8081;
        public const ushort Rcv_Freq_ArrangeProg = 0x8002;
        public const ushort Rcv_Freq_EvalResult = 0x8083;
        public const ushort Rcv_Freq_Freq_RelaListResult = 0x8084;
        public const ushort Rcv_Freq_ArrangeResult = 0x8012;
    }
    public static class GSMCmdTypeDef
    {
        public const ushort Cell_Interfere = 0x0011;//查询小区当前干扰评估值
        public const ushort Freq_Arrange = 0x0002;//排频排扰计算
        public const ushort Freq_Eval = 0x0013;
        public const ushort Freq_RelaList = 0x0014;
        public const ushort Freq_Assess = 0x0005;
        public const ushort Rcv_Freq_ArrangePause = 0x8001;
        public const ushort Rcv_Freq_ArrangeProg = 0x8002;
        public const ushort Rcv_Freq_EvalResult = 0x8003;
        public const ushort Rcv_Freq_Freq_RelaListResult = 0x8004;
        public const ushort Rcv_Freq_ArrangeResult = 0x8012;
    }
    public class InnerProcPackage
    {
        public ushort CmdType { get; set; }
        public bool EndFlag
        {
            get { return endFlag == (byte)0x00; }
            set
            {
                endFlag = value ? (byte)0x00 : (byte)0x01;
            }
        }
        public InnerProcContent Content
        {
            get { return content; }
            set { if (value != null) content = value; }
        }

        internal void Send(BinaryWriter writer)
        {
            writer.Write(header);
            writer.Write(IPAddress.HostToNetworkOrder((short)CmdType));
            writer.Write(IPAddress.HostToNetworkOrder((int)this.Length));//TOTAL_LEN
            writer.Write(endFlag);
            this.content.Write(writer);
        }

        internal void Recieve(BinaryReader reader)
        {
            reader.ReadByte();//0x9E readByte
            //Assert(readByte == Header1);
            reader.ReadByte();//0x62 readByte
            //assert(readByte == Header2);
            short typeX = (short)reader.ReadUInt16();
            CmdType = (ushort)IPAddress.NetworkToHostOrder(typeX);
            int length = IPAddress.NetworkToHostOrder(reader.ReadInt32());
            endFlag = reader.ReadByte();
            content.Read(reader, length);
        }

        private uint Length
        {
            get { return (content.Length); }
        }

        private const byte Header1 = 0x9E;

        private const byte Header2 = 0x62;

        private static readonly byte[] header = { Header1, Header2 };
        
        private byte endFlag = 0x00;//0x00 end  0x01 未完

        private InnerProcContent content = new InnerProcContent();
    }

    public class InnerProcContent : InnerContent
    {
        public override void Write(BinaryWriter writer)
        {
            if (paramsBufferSize > 0)
            {
                writer.Write(this.paramsBuffer, 0, this.paramsBufferSize);
            }
        }

        public override void Read(BinaryReader reader, int length)
        {
            if (length > 0)
            {
                this.paramsBuffer = reader.ReadBytes(length);
                this.paramsBufferSize = this.paramsBuffer.Length;
            }
            CurOffset = 0;
        }

        public override uint Length
        {
            get { return (uint)(paramsBufferSize); }
        }
    }

    public class InnerProcClientProxy
    {
        public InnerProcClientProxy()
        {
        }

        public InnerProcClientProxy(string ip, int port)
        {
            this.ip = ip;
            this.port = port;
        }

        public int SendTimeOut
        {
            get { return client.SendTimeout; }
            set { this.client.SendTimeout = value; }
        }

        public int ReciveTimeOut
        {
            get { return client.ReceiveTimeout; }
            set { this.client.ReceiveTimeout = value; }
        }
        public bool connect()
        {
            try
            {
                this.client.Connect(ip, port);
                this.client.ReceiveTimeout = 600000;//考虑到TD 智能排频的那段，延长接收的时间，大约为10分钟；原来为1分钟
                if (this.client.Connected)
                {
                    this.stream = client.GetStream();
                    this.writer = new BinaryWriter(stream);
                    this.reader = new BinaryReader(stream);
                    return true;
                }
            }
            catch (Exception e)
            {
                System.Console.WriteLine(e);
            }
            return false;
        }

        public void Close()
        {
            this.client.Close();
            if (this.stream != null)
            {
                this.stream.Close();
                this.stream = null;
            }
            if (this.reader != null)
            {
                this.reader.Close();
                this.reader = null;
            }
            if (this.writer != null)
            {
                this.writer.Close();
                this.writer = null;
            }
        }

        public void Send()
        {
            this.package.Send(writer);
            writer.Flush();
        }

        public void Recieve()
        {
            this.package.Recieve(reader);
        }

        public InnerProcPackage Package
        {
            get { return package; }
            set { if (value != null) this.package = value; }
        }

        private readonly string ip;

        private readonly int port;

        private readonly TcpClient client = new TcpClient();

        private NetworkStream stream;

        private BinaryWriter writer;

        private BinaryReader reader;

        private InnerProcPackage package = new InnerProcPackage();
    }
}
