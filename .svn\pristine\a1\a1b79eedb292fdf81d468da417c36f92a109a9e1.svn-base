﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public partial class NewColumnBox : TextInputBox
    {
        public NewColumnBox(string text, string labelText, string textInput)
            : base(text,labelText,textInput)
        {
            InitializeComponent();
            Text = text;
            LabelText = labelText;
            TextInput = textInput;
        }
        public int ColumnType
        {
            get { return radioGroup.SelectedIndex; }
        }
    }
}
