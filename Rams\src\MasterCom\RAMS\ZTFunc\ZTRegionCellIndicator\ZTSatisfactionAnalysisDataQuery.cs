﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSatisfactionAnalysisDataQuery : DIYSQLBase
    {
        public ZTSatisfactionAnalysisDataQuery(MainModel mainModel)
            : base(mainModel)
        {

        }
    
        public string City { private get; set; }
        public string Month { private get; set; }
        public string QuestionType { private get; set; }
        //gsm小区泰森多边形集合
        public Dictionary<Cell, List<Vertex[]>> GSMCellVertex { private get; set; }
        //td小区泰森多边形集合
        public Dictionary<TDCell, List<Vertex[]>> TDCellVertex { private get; set; }

        private string whereCondition;
        public string WhereCondition
        {
            set { whereCondition = value; }
            get { return whereCondition.Replace("网络覆盖", "网络覆盖和信号强度").Replace("网络质量", "网络整体质量").Replace("手机上网", "手机上网整体质量"); }
        }

        private readonly Dictionary<string, UserSatisfactionItem> userSatisfactionMap = new Dictionary<string, UserSatisfactionItem>();
        private readonly List<UserSatisfactionItem> userSatisfactionItems = new List<UserSatisfactionItem>();
        public List<UserSatisfactionItem> UserSatisfactionItems
        {
            get { return userSatisfactionItems; }
        }

        protected override string getSqlTextString()
        {
            DateTime monthdate;
            try
            {
                string year;
                string curmonth;
                string[] splits = Month.Split(new string[] { "年", "月", "-", "_" }, StringSplitOptions.RemoveEmptyEntries);
                if (splits.Length == 2)
                {
                    year = splits[0].Length == 2 ? "20" + splits[0] : splits[0];
                    curmonth = splits[1];
                }
                else
                {
                    year = splits[0].Substring(0, 4);
                    curmonth = splits[0].Substring(4, 2);

                }
                monthdate = new DateTime(Int32.Parse(year), Int32.Parse(curmonth), 1);
            }
            catch
            {
                monthdate = DateTime.Now;
            }
            return "if(not exists(select * from sysobjects where id= OBJECT_ID('Complain_Sys..wlmq_user_record_" + monthdate.ToString("yyMM") + "'))) select 电话,性别,终端类型,网络覆盖和信号强度 as 网络覆盖,网络整体质量 as 网络质量,手机上网整体质量 as 手机上网,语音通话,null,null,null,null,null,null,null,null,null from Complain_Sys..wlmq_user_satisfaction where Provider='中国移动' and (" + WhereCondition + ") and 地市='" + City + "' and ('" + QuestionType + "'='全部' or 问卷类型='" + QuestionType + "') and 月份='" + Month + "' else select a.电话,a.性别,a.终端类型,a.网络覆盖,a.网络质量,a.手机上网,a.语音通话,b.LAC,b.CELL_ID,b.GSM指标总得分,b.TD指标总得分,c.areaName,c.gridScene,c.ccount,c.GSM指标总得分,c.TD指标总得分 from (select 电话,性别,终端类型,网络覆盖和信号强度 as 网络覆盖,网络整体质量 as 网络质量,手机上网整体质量 as 手机上网,语音通话 from Complain_Sys..wlmq_user_satisfaction where Provider='中国移动' and (" + WhereCondition + ") and 地市='" + City + "' and ('" + QuestionType + "'='全部' or 问卷类型='" + QuestionType + "') and 月份='" + Month + "' ) a left join (select distinct a.手机号码,a.LAC,a.CELL_ID,b.GSM指标总得分,b.TD指标总得分 from Complain_Sys..wlmq_user_record_" + monthdate.ToString("yyMM") + " a left join (select distinct keyname,max(case when reportName='GSM区域评估报表' then allScore else 0 end )as GSM指标总得分,max(case when reportName='TD区域评估报表' then allScore else 0 end) as TD指标总得分 from  Complain_Sys..tb_QoE_cell_all_score where endTime=(select MAX(endTime) from Complain_Sys..tb_QoE_cell_all_score) group by keyname) b on b.keyName=(a.LAC+'-'+a.CELL_ID) ) b on a.电话=b.手机号码 left join (select distinct a.手机号码,b.areaName,b.LAC,b.CI,c.gridScene,e.ccount,d.GSM指标总得分,d.TD指标总得分 from Complain_Sys..wlmq_user_record_" + monthdate.ToString("yyMM") + " a join Complain_Sys..tb_QoE_cfg_area_cell b on a.LAC=b.LAC and a.CELL_ID=b.CI left join complaint_xj..tb_complainGrid c on b.areaName=c.gridName left join (select distinct keyname,max(case when reportName='GSM区域评估报表' then allScore else 0 end )as GSM指标总得分,max(case when reportName='TD区域评估报表' then allScore else 0 end) as TD指标总得分 from  Complain_Sys..tb_QoE_area_all_score where endTime=(select MAX(endTime) from Complain_Sys..tb_QoE_area_all_score) group by keyname) d on b.areaName=d.keyName left join (select gridName,COUNT(*) ccount from complaint_xj..tb_complain_item where acceptTM between '" + monthdate.ToString("yyyy-MM-dd") + "' and '" + monthdate.AddMonths(1).ToString("yyyy-MM-dd") + "' group by gridName) e on c.id=e.gridName) c on a.电话 =c.手机号码 and b.LAC=c.LAC and b.CELL_ID=c.CI order by a.电话";
        }

        protected override MasterCom.RAMS.Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vtypes = new E_VType[16];
            vtypes[0] = E_VType.E_String;
            vtypes[1] = E_VType.E_String;
            vtypes[2] = E_VType.E_String;
            vtypes[3] = E_VType.E_Int;
            vtypes[4] = E_VType.E_Int;
            vtypes[5] = E_VType.E_Int;
            vtypes[6] = E_VType.E_Int;
            vtypes[7] = E_VType.E_String;
            vtypes[8] = E_VType.E_String;
            vtypes[9] = E_VType.E_Float;
            vtypes[10] = E_VType.E_Float;
            vtypes[11] = E_VType.E_String;
            vtypes[12] = E_VType.E_String;
            vtypes[13] = E_VType.E_Int;
            vtypes[14] = E_VType.E_Float;
            vtypes[15] = E_VType.E_Float;
            return vtypes;
        }

        protected override void query()
        {
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
            fireResultForm();
        }

        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            WaitBox.Close();
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            userSatisfactionItems.Clear();
            userSatisfactionMap.Clear();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        private void fillData(Package package)
        {
            string userphone = package.Content.GetParamString();
            string usersex = package.Content.GetParamString();
            string terminaltype = package.Content.GetParamString();
            int networkcovert = package.Content.GetParamInt();
            int networkquality = package.Content.GetParamInt();
            int mobileinternet = package.Content.GetParamInt();
            int voicecall = package.Content.GetParamInt();
            string lac = package.Content.GetParamString();
            string ci = package.Content.GetParamString();
            double gsmcellscore = package.Content.GetParamFloat();
            double tdcellscore = package.Content.GetParamFloat();
            string gridname = package.Content.GetParamString();
            string gridschene = package.Content.GetParamString();
            int ccount = package.Content.GetParamInt();
            double gsmgridscore = package.Content.GetParamFloat();
            double tdgridscore = package.Content.GetParamFloat();
            UserSatisfactionItem userSatisfaction;
            if (!userSatisfactionMap.TryGetValue(userphone, out userSatisfaction))
            {
                userSatisfaction = new UserSatisfactionItem(userphone, usersex, terminaltype, networkcovert, networkquality, mobileinternet, voicecall);
                userSatisfactionMap[userphone] = userSatisfaction;
                userSatisfactionItems.Add(userSatisfaction);
            }
            if (!string.IsNullOrEmpty(lac) && !string.IsNullOrEmpty(ci))
            {
                string cellname = null;
                string network = null;
                List<Vertex[]> vertexs = new List<Vertex[]>();
                Cell cell = MainModel.CellManager.GetCurrentCell(Int32.Parse(lac), Int32.Parse(ci));
                if (cell == null)
                {
                    TDCell tdcell = MainModel.CellManager.GetCurrentTDCell(Int32.Parse(lac), Int32.Parse(ci));
                    if (tdcell != null)
                    {
                        cellname = tdcell.Name;
                        network = "TD";
                        vertexs = TDCellVertex[tdcell];
                    }
                }
                else
                {
                    cellname = cell.Name;
                    network = "GSM";
                    vertexs = GSMCellVertex[cell];
                }
                CellScore cellscore = new CellScore(cellname, lac, ci, network, gsmcellscore, tdcellscore);
                cellscore.Vertexs = vertexs;
                userSatisfaction.AddCellScore(cellscore);
            }
            if (!string.IsNullOrEmpty(gridname))
            {
                GridScore gridScore = new GridScore(gridname, gridschene, ccount, gsmgridscore, tdgridscore);
                userSatisfaction.AddGridScore(gridScore);
            }
        }

        public override string Name
        {
            get { return ""; }
        }

        protected void fireResultForm()
        {
            SatisfactionAnalysisReportForm frm = null;
            frm = MainModel.GetObjectFromBlackboard(typeof(SatisfactionAnalysisReportForm).FullName) as SatisfactionAnalysisReportForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new SatisfactionAnalysisReportForm(MainModel);
            }
            frm.FillData(userSatisfactionItems);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
            else
            {
                frm.BringToFront();
            }
        }
    }
}
