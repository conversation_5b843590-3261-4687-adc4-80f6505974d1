﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.PerformanceParam
{
    public class JoinHandoverAnalysis
    {
        public string LAC_CI
        {
            get
            {
                if (Cell != null)
                {
                    return Cell.Name + "(LAC:" + ILac.ToString() + ",CI:" + ICi.ToString()+")";
                }
                else
                {
                    return "LAC:" + ILac.ToString() + ",CI:" + ICi.ToString();
                }
            }
        }

       /// <summary>
        /// 轮次
       /// </summary>
        public int IMon { get; set; }
        /// <summary>
        /// 源LAC
        /// </summary>
        public int ILac { get; set; }
        /// <summary>
        /// 源CI
        /// </summary>
        public int ICi { get; set; }
        /// <summary>
        /// 目标LAC
        /// </summary>
        public int ITargetLac { get; set; }
        /// <summary>
        /// 目标CI
        /// </summary>
        public int ITargetCi { get; set; }
        /// <summary>
        /// 路测切换次数
        /// </summary>
        public int IHoNum { get; set; }
        /// <summary>
        /// 源小区切出占比
        /// </summary>
        public decimal FoutRate { get; set; }
        /// <summary>
        /// 目标小区切出占比
        /// </summary>
        public decimal FintRate { get; set; }
        /// <summary>
        /// 性能侧切换请求次数
        /// </summary>
        public int IReq { get; set; }
        /// <summary>
        /// 性能侧切换成功次数
        /// </summary>
        public int ISucc { get; set; }
        /// <summary>
        /// 性能侧切换成功率
        /// </summary>
        public decimal FSuccRate { get; set; }
        /// <summary>
        /// 问题级别
        /// </summary>
        public int ILevel { get; set; }
        /// <summary>
        /// 建议方案
        /// </summary>
        public string Strcomment { get; set; }
        /// <summary>
        /// 小区
        /// </summary>
        public Cell Cell { get; set; }
    }
}
