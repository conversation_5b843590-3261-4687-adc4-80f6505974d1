﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    public class DiyQueryKpiByGrid : QueryKPIStatBase
    {
        readonly int districtID;
        readonly List<string> img;
        public List<KPIStatDataBase> DataList { get; set; }
        public List<KPIStatDataBase> EventList { get; set; }

        public DiyQueryKpiByGrid(int districtID, List<string> img)
            : base(MainModel.GetInstance())
        {
            this.districtID = districtID;
            this.img = img;
        }

        public override string Name
        {
            get { return "按栅格查询指标"; }
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.grid;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                DataList = new List<KPIStatDataBase>();
                EventList = new List<KPIStatDataBase>();
                WaitBox.CanCancel = true;

                string statImgIDSet = this.getStatImgNeededTriadID();
                foreach (TimePeriod period in condition.Periods)
                {
                    queryPeriodInfo(period, clientProxy, statImgIDSet);
                }
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            return getTriadIDIgnoreServiceType(img);
        }

        /// <summary>
        /// 查询某时间段内的数据
        /// </summary>
        /// <param name="period">当该参数为null时，视为按轮查询</param>
        /// <param name="clientProxy"></param>
        /// <param name="package"></param>
        //protected override void queryPeriodInfo(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        //{
        //    isQueringEvent = false;
        //    preparePackageBasicContent(clientProxy.Package, period);
        //    preparePackageNeededInfo_StatImg(clientProxy.Package, reservedParams[0]);
        //    clientProxy.Send();
        //    recieveInfo_ImgGrid(clientProxy, null);

        //    if (evtIDSvrIDDic.Count > 0)
        //    {
        //        //event
        //        isQueringEvent = true;
        //        preparePackageBasicContent(clientProxy.Package, period, reservedParams);
        //        preparePackageNeededInfo_Event(clientProxy.Package);
        //        clientProxy.Send();
        //        recieveInfo_Event(clientProxy);
        //    }

        //    afterRecieveOnePeriodData();
        //}

        protected override void AddGeographicFilter(Package package)
        {
            if (isQueringEvent)
            {
                AddDIYEndOpFlag(package);
                this.AddDIYRegion_Sample(package);
            }
            else
            {
                this.AddDIYRegion_Intersect(package);
            }
        }

        //protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        //{
        //    Package package = clientProxy.Package;
        //    int counter = 0;
        //    int curPercent = 11;
        //    DTDataHeaderManager.GetInstance();
        //    List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
        //    List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
        //    while (true)
        //    {
        //        if (WaitBox.CancelRequest)
        //        {
        //            break;
        //        }
        //        clientProxy.Recieve();
        //        package.Content.PrepareGetParam();
        //        KPIStatDataBase singleStatData;
        //        if (isFileHeaderContentType(package.Content.Type))
        //        {
        //            recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
        //        }
        //        else if (isImgColDefContent(package, curImgColumnDef))
        //        {
        //            //
        //        }
        //        else if (isKPIDataContent(package, out singleStatData))
        //        {
        //            fillData(package, curImgColumnDef, singleStatData);
        //        }
        //        else
        //        {
        //            break;
        //        }
        //        setProgressPercent(ref counter, ref curPercent);
        //    }
        //}

        protected override void recieveAndHandleSpecificStatData(Package package
            , List<StatImgDefItem> curImgColumnDef
            , KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            GridUnitBase grid = new GridUnitBase(lng, lat);
            if (isValidStatImg(grid.CenterLng, grid.CenterLat))
            {
                fillStatData(package, curImgColumnDef, singleStatData);
                DataList.Add(singleStatData);
            }
        }

        //private void fillData(Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        //{
        //    double lng = package.Content.GetParamDouble();
        //    double lat = package.Content.GetParamDouble();
        //    if (isValidStatImg(lng, lat))
        //    {
        //        fillStatData(package, curImgColumnDef, singleStatData);
        //        //StatDataHubBase data = new StatDataHubBase();
        //        //data.AddStatData(singleStatData, false);
        //        DataList.Add(singleStatData);
        //    }
        //}

        protected bool isValidStatImg(double lng, double lat)
        {
            //GridUnitBase grid = new GridUnitBase(lng, lat);
            //condition.Geometorys.GeoOp.ContainsRectCenter(grid.Bounds)
            bool isValid = condition.Geometorys.GeoOp.Contains(lng, lat);
            return isValid;
        }

        //protected override void recieveInfo_Event(ClientProxy clientProxy, params object[] reservedParams)
        //{
        //    DTDataHeaderManager.GetInstance();
        //    List<ColumnDefItem> colDefSet = new List<ColumnDefItem>();
        //    int index = 0;
        //    Package package = clientProxy.Package;
        //    int progress = 0;
        //    while (true)
        //    {
        //        if (WaitBox.CancelRequest)
        //        {
        //            break;
        //        }
        //        clientProxy.Recieve();
        //        package.Content.PrepareGetParam();
        //        #region Read Stat Data
        //        if (isFileHeaderContentType(package.Content.Type))
        //        {
        //            recieveFileHeader(clientProxy.DbID, package.Content, colDefSet);
        //        }
        //        else if (isColDefContent(package, colDefSet))
        //        {
        //            //
        //        }
        //        else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT
        //            || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_GSM
        //            || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_TDSCDMA
        //            || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_WCDMA
        //            || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA
        //            || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA2000
        //            || package.Content.Type == ResponseType.AREASTAT_KPI_EVENT_LTE
        //            || package.Content.Type == ResponseType.AREASTAT_KPI_EVENT_LTE_FDD
        //            || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_NR)
        //        {
        //            NREventHelper.ReSetIntCI(colDefSet);
        //            fillData(colDefSet, package);
        //        }
        //        else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT_NR)
        //        {
        //            NREventHelper.SetLongCI(colDefSet);
        //            fillData(colDefSet, package);
        //        }
        //        else if (package.Content.Type == ResponseType.END ||
        //            package.Content.Type == ResponseType.RESTYPE_SEARCHERROR)
        //        {
        //            break;
        //        }
        //        else
        //        {
        //            System.Diagnostics.Debug.Assert(false, package.Content.Type.ToString());
        //            break;
        //        }

        //        #endregion

        //        setProgressPercent(ref index, ref progress);
        //    }
        //}

        //private void fillData(List<ColumnDefItem> colDefSet, Package package)
        //{
        //    Event evt = Event.Create(package.Content, colDefSet);
        //    if (evtIDSvrIDDic.ContainsKey(evt.ID) && isValidStatImg(evt.Longitude, evt.Latitude))
        //    {
        //        StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
        //        EventList.Add(eventData);
        //    }
        //}

        protected override void fillData(List<ColumnDefItem> colDefSet, Package package)
        {
            Event evt = Event.Create(package.Content, colDefSet);
            if (evtIDSvrIDDic.ContainsKey(evt.ID) && isValidStatImg(evt.Longitude, evt.Latitude))
            {
                StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
                EventList.Add(eventData);
            }
        }
    }
}
