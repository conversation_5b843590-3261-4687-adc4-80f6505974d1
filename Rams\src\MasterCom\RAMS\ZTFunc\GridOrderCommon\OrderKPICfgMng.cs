﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    public class OrderKPICfgMng
    {
        private static OrderKPICfgMng instance = null;
        public static OrderKPICfgMng Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new OrderKPICfgMng();
                }
                return instance;
            }
        }

        private OrderKPICfgMng()
        {
            queryFormDB();
        }

        public List<GridOrderKPICfg> KPICfgSet
        {
            get;
            set;
        }

        public GridOrderKPICfg GetKPICfgItem(int setTypeID, int cfgID)
        {
            string key = string.Format("{0}_{1}", setTypeID, cfgID);
            GridOrderKPICfg cfg = null;
            this.kpiCfgDic.TryGetValue(key, out cfg);
            return cfg;
        }

        public List<GridOrderKPICfg> GetKPICfgSetByTypeID(int tokenID)
        {
            List<GridOrderKPICfg> list = new List<GridOrderKPICfg>();
            foreach (GridOrderKPICfg item in kpiCfgDic.Values)
            {
                if (item.SetTypeID==tokenID)
                {
                    list.Add(item);
                }
            }
            return list;
        }

        private readonly Dictionary<string, GridOrderKPICfg> kpiCfgDic = new Dictionary<string, GridOrderKPICfg>();

        private void queryFormDB()
        {
            QueryGridOrderKPICfg query = new QueryGridOrderKPICfg();
            query.Query();
            KPICfgSet = query.KPICfgSet;
            foreach (GridOrderKPICfg item in KPICfgSet)
            {
                kpiCfgDic[item.Key] = item;
            }
        }

    }
}
