using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Frame
{
    public partial class ZTMainCellHandOverTooMuchConditionDlg : BaseDialog
    {
        public ZTMainCellHandOverTooMuchConditionDlg()
        {
            InitializeComponent();
        }

        private static ZTMainCellHandOverTooMuchConditionDlg dlg = new ZTMainCellHandOverTooMuchConditionDlg();
        public static ZTMainCellHandOverTooMuchConditionDlg GetDlg()
        {
            return dlg;
        }

        public int HandOverTimes
        {
            get { return int.Parse(spinEditTimes.Value.ToString()); }
        }

        public double Distance
        {
            get { return double.Parse(spinEditDistance.Value.ToString()); }
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}