﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class QueryFileStatusInfo
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.pag1 = new System.Windows.Forms.TabPage();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnProjectName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBeginTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEndTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnImportTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnImportStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEventStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnESStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.panel1 = new System.Windows.Forms.Panel();
            this.label7 = new System.Windows.Forms.Label();
            this.cbxAreaStatStatus = new System.Windows.Forms.ComboBox();
            this.label6 = new System.Windows.Forms.Label();
            this.cbxGridStatStatus = new System.Windows.Forms.ComboBox();
            this.label5 = new System.Windows.Forms.Label();
            this.cbxESStatus = new System.Windows.Forms.ComboBox();
            this.label4 = new System.Windows.Forms.Label();
            this.cbxEventDefineStatus = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.cbxImportStatus = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.datEnd = new System.Windows.Forms.DateTimePicker();
            this.label2 = new System.Windows.Forms.Label();
            this.butQuery = new System.Windows.Forms.Button();
            this.datStart = new System.Windows.Forms.DateTimePicker();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.lvNotStored = new System.Windows.Forms.ListView();
            this.columnHeaderFileName2 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader2 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader1 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader3 = new System.Windows.Forms.ColumnHeader();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.入库优先级ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miPriorityOne = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem6 = new System.Windows.Forms.ToolStripMenuItem();
            this.panel2 = new System.Windows.Forms.Panel();
            this.label9 = new System.Windows.Forms.Label();
            this.datImportEndTime = new System.Windows.Forms.DateTimePicker();
            this.btnRun = new System.Windows.Forms.Button();
            this.label8 = new System.Windows.Forms.Label();
            this.datImportStartTime = new System.Windows.Forms.DateTimePicker();
            this.contextMenuStrip2 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItemExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.tabControl1.SuspendLayout();
            this.pag1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.panel1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.contextMenuStrip2.SuspendLayout();
            this.SuspendLayout();
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.dataGridViewTextBoxColumn1.HeaderText = "文件名";
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.HeaderText = "项目名";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            // 
            // dataGridViewTextBoxColumn3
            // 
            this.dataGridViewTextBoxColumn3.HeaderText = "时间";
            this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            // 
            // dataGridViewTextBoxColumn4
            // 
            this.dataGridViewTextBoxColumn4.HeaderText = "入库状态";
            this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
            // 
            // dataGridViewTextBoxColumn5
            // 
            this.dataGridViewTextBoxColumn5.HeaderText = "事件合成状态";
            this.dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
            // 
            // dataGridViewTextBoxColumn6
            // 
            this.dataGridViewTextBoxColumn6.HeaderText = "智能预判状态";
            this.dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
            // 
            // dataGridViewTextBoxColumn7
            // 
            this.dataGridViewTextBoxColumn7.HeaderText = "统计状态";
            this.dataGridViewTextBoxColumn7.Name = "dataGridViewTextBoxColumn7";
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.pag1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(895, 495);
            this.tabControl1.TabIndex = 0;
            // 
            // pag1
            // 
            this.pag1.Controls.Add(this.gridControl);
            this.pag1.Controls.Add(this.panel1);
            this.pag1.Location = new System.Drawing.Point(4, 22);
            this.pag1.Name = "pag1";
            this.pag1.Padding = new System.Windows.Forms.Padding(3);
            this.pag1.Size = new System.Drawing.Size(887, 469);
            this.pag1.TabIndex = 0;
            this.pag1.Text = "已入库文件";
            this.pag1.UseVisualStyleBackColor = true;
            // 
            // gridControl
            // 
            this.gridControl.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.gridControl.ContextMenuStrip = this.contextMenuStrip2;
            this.gridControl.Location = new System.Drawing.Point(3, 86);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(881, 381);
            this.gridControl.TabIndex = 12;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnFileName,
            this.gridColumnProjectName,
            this.gridColumnBeginTime,
            this.gridColumnEndTime,
            this.gridColumnImportTime,
            this.gridColumnImportStatus,
            this.gridColumnEventStatus,
            this.gridColumnESStatus,
            this.gridColumnGridStatus,
            this.gridColumnAreaStatus});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名称";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 0;
            // 
            // gridColumnProjectName
            // 
            this.gridColumnProjectName.Caption = "项目名";
            this.gridColumnProjectName.FieldName = "ProjectName";
            this.gridColumnProjectName.Name = "gridColumnProjectName";
            this.gridColumnProjectName.Visible = true;
            this.gridColumnProjectName.VisibleIndex = 1;
            // 
            // gridColumnBeginTime
            // 
            this.gridColumnBeginTime.Caption = "开始时间";
            this.gridColumnBeginTime.FieldName = "IsTimeToString";
            this.gridColumnBeginTime.Name = "gridColumnBeginTime";
            this.gridColumnBeginTime.Visible = true;
            this.gridColumnBeginTime.VisibleIndex = 2;
            // 
            // gridColumnEndTime
            // 
            this.gridColumnEndTime.Caption = "结束时间";
            this.gridColumnEndTime.FieldName = "IeTimeToString";
            this.gridColumnEndTime.Name = "gridColumnEndTime";
            this.gridColumnEndTime.Visible = true;
            this.gridColumnEndTime.VisibleIndex = 3;
            // 
            // gridColumnImportTime
            // 
            this.gridColumnImportTime.Caption = "入库时间";
            this.gridColumnImportTime.FieldName = "IImportTimeToString";
            this.gridColumnImportTime.Name = "gridColumnImportTime";
            this.gridColumnImportTime.Visible = true;
            this.gridColumnImportTime.VisibleIndex = 4;
            // 
            // gridColumnImportStatus
            // 
            this.gridColumnImportStatus.Caption = "入库状态";
            this.gridColumnImportStatus.FieldName = "ImportStatus";
            this.gridColumnImportStatus.Name = "gridColumnImportStatus";
            this.gridColumnImportStatus.Visible = true;
            this.gridColumnImportStatus.VisibleIndex = 5;
            // 
            // gridColumnEventStatus
            // 
            this.gridColumnEventStatus.Caption = "事件合成状态";
            this.gridColumnEventStatus.FieldName = "EventDefineStatus";
            this.gridColumnEventStatus.Name = "gridColumnEventStatus";
            this.gridColumnEventStatus.Visible = true;
            this.gridColumnEventStatus.VisibleIndex = 6;
            // 
            // gridColumnESStatus
            // 
            this.gridColumnESStatus.Caption = "智能预判状态";
            this.gridColumnESStatus.FieldName = "ESStatus";
            this.gridColumnESStatus.Name = "gridColumnESStatus";
            this.gridColumnESStatus.Visible = true;
            this.gridColumnESStatus.VisibleIndex = 7;
            // 
            // gridColumnGridStatus
            // 
            this.gridColumnGridStatus.Caption = "栅格统计状态";
            this.gridColumnGridStatus.FieldName = "GridStatStatus";
            this.gridColumnGridStatus.Name = "gridColumnGridStatus";
            this.gridColumnGridStatus.Visible = true;
            this.gridColumnGridStatus.VisibleIndex = 8;
            // 
            // gridColumnAreaStatus
            // 
            this.gridColumnAreaStatus.Caption = "区域统计";
            this.gridColumnAreaStatus.FieldName = "AreaStatStatus";
            this.gridColumnAreaStatus.Name = "gridColumnAreaStatus";
            this.gridColumnAreaStatus.Visible = true;
            this.gridColumnAreaStatus.VisibleIndex = 9;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.label7);
            this.panel1.Controls.Add(this.cbxAreaStatStatus);
            this.panel1.Controls.Add(this.label6);
            this.panel1.Controls.Add(this.cbxGridStatStatus);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.cbxESStatus);
            this.panel1.Controls.Add(this.label4);
            this.panel1.Controls.Add(this.cbxEventDefineStatus);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Controls.Add(this.cbxImportStatus);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Controls.Add(this.datEnd);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Controls.Add(this.butQuery);
            this.panel1.Controls.Add(this.datStart);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(881, 77);
            this.panel1.TabIndex = 10;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(660, 46);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(89, 12);
            this.label7.TabIndex = 32;
            this.label7.Text = "区域统计状态：";
            // 
            // cbxAreaStatStatus
            // 
            this.cbxAreaStatStatus.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxAreaStatStatus.FormattingEnabled = true;
            this.cbxAreaStatStatus.Items.AddRange(new object[] {
            "全部",
            "成功",
            "异常"});
            this.cbxAreaStatStatus.Location = new System.Drawing.Point(755, 42);
            this.cbxAreaStatStatus.Name = "cbxAreaStatStatus";
            this.cbxAreaStatStatus.Size = new System.Drawing.Size(99, 20);
            this.cbxAreaStatStatus.TabIndex = 31;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(444, 46);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(89, 12);
            this.label6.TabIndex = 30;
            this.label6.Text = "栅格统计状态：";
            // 
            // cbxGridStatStatus
            // 
            this.cbxGridStatStatus.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxGridStatStatus.FormattingEnabled = true;
            this.cbxGridStatStatus.Items.AddRange(new object[] {
            "全部",
            "成功",
            "异常"});
            this.cbxGridStatStatus.Location = new System.Drawing.Point(539, 42);
            this.cbxGridStatStatus.Name = "cbxGridStatStatus";
            this.cbxGridStatStatus.Size = new System.Drawing.Size(99, 20);
            this.cbxGridStatStatus.TabIndex = 29;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(207, 46);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(89, 12);
            this.label5.TabIndex = 28;
            this.label5.Text = "智能预判状态：";
            // 
            // cbxESStatus
            // 
            this.cbxESStatus.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxESStatus.FormattingEnabled = true;
            this.cbxESStatus.Items.AddRange(new object[] {
            "全部",
            "成功"});
            this.cbxESStatus.Location = new System.Drawing.Point(302, 42);
            this.cbxESStatus.Name = "cbxESStatus";
            this.cbxESStatus.Size = new System.Drawing.Size(99, 20);
            this.cbxESStatus.TabIndex = 27;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(444, 17);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(89, 12);
            this.label4.TabIndex = 26;
            this.label4.Text = "事件合成状态：";
            // 
            // cbxEventDefineStatus
            // 
            this.cbxEventDefineStatus.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxEventDefineStatus.FormattingEnabled = true;
            this.cbxEventDefineStatus.Items.AddRange(new object[] {
            "全部",
            "成功",
            "异常",
            "正在处理"});
            this.cbxEventDefineStatus.Location = new System.Drawing.Point(539, 13);
            this.cbxEventDefineStatus.Name = "cbxEventDefineStatus";
            this.cbxEventDefineStatus.Size = new System.Drawing.Size(99, 20);
            this.cbxEventDefineStatus.TabIndex = 25;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(231, 17);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 24;
            this.label3.Text = "入库状态：";
            // 
            // cbxImportStatus
            // 
            this.cbxImportStatus.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxImportStatus.FormattingEnabled = true;
            this.cbxImportStatus.Items.AddRange(new object[] {
            "全部",
            "成功",
            "异常"});
            this.cbxImportStatus.Location = new System.Drawing.Point(302, 13);
            this.cbxImportStatus.Name = "cbxImportStatus";
            this.cbxImportStatus.Size = new System.Drawing.Size(99, 20);
            this.cbxImportStatus.TabIndex = 23;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(24, 46);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 22;
            this.label1.Text = "结束日期：";
            // 
            // datEnd
            // 
            this.datEnd.CustomFormat = "yyyy-MM-dd";
            this.datEnd.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.datEnd.Location = new System.Drawing.Point(95, 42);
            this.datEnd.Name = "datEnd";
            this.datEnd.Size = new System.Drawing.Size(88, 21);
            this.datEnd.TabIndex = 21;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(24, 17);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 20;
            this.label2.Text = "起始日期：";
            // 
            // butQuery
            // 
            this.butQuery.Location = new System.Drawing.Point(779, 12);
            this.butQuery.Name = "butQuery";
            this.butQuery.Size = new System.Drawing.Size(75, 23);
            this.butQuery.TabIndex = 18;
            this.butQuery.Text = "查询";
            this.butQuery.UseVisualStyleBackColor = true;
            this.butQuery.Click += new System.EventHandler(this.butQuery_Click);
            // 
            // datStart
            // 
            this.datStart.CustomFormat = "yyyy-MM-dd";
            this.datStart.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.datStart.Location = new System.Drawing.Point(95, 13);
            this.datStart.Name = "datStart";
            this.datStart.Size = new System.Drawing.Size(88, 21);
            this.datStart.TabIndex = 19;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.lvNotStored);
            this.tabPage2.Controls.Add(this.panel2);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(887, 469);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "未入库文件";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // lvNotStored
            // 
            this.lvNotStored.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderFileName2,
            this.columnHeader2,
            this.columnHeader1,
            this.columnHeader3});
            this.lvNotStored.ContextMenuStrip = this.contextMenuStrip1;
            this.lvNotStored.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvNotStored.FullRowSelect = true;
            this.lvNotStored.GridLines = true;
            this.lvNotStored.Location = new System.Drawing.Point(3, 55);
            this.lvNotStored.Name = "lvNotStored";
            this.lvNotStored.Size = new System.Drawing.Size(881, 411);
            this.lvNotStored.TabIndex = 1;
            this.lvNotStored.UseCompatibleStateImageBehavior = false;
            this.lvNotStored.View = System.Windows.Forms.View.Details;
            // 
            // columnHeaderFileName2
            // 
            this.columnHeaderFileName2.Text = "文件名";
            this.columnHeaderFileName2.Width = 300;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "项目名";
            this.columnHeader2.Width = 150;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "上传时间";
            this.columnHeader1.Width = 150;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "入库优先级";
            this.columnHeader3.Width = 80;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.入库优先级ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(173, 26);
            this.contextMenuStrip1.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip1_Opening);
            // 
            // 入库优先级ToolStripMenuItem
            // 
            this.入库优先级ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miPriorityOne,
            this.toolStripMenuItem3,
            this.toolStripMenuItem4,
            this.toolStripMenuItem5,
            this.toolStripMenuItem6});
            this.入库优先级ToolStripMenuItem.Name = "入库优先级ToolStripMenuItem";
            this.入库优先级ToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.入库优先级ToolStripMenuItem.Text = "设置入库优先级为";
            // 
            // miPriorityOne
            // 
            this.miPriorityOne.Name = "miPriorityOne";
            this.miPriorityOne.Size = new System.Drawing.Size(83, 22);
            this.miPriorityOne.Text = "1";
            this.miPriorityOne.Click += new System.EventHandler(this.miPriorityOne_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(83, 22);
            this.toolStripMenuItem3.Text = "2";
            this.toolStripMenuItem3.Click += new System.EventHandler(this.miPriorityOne_Click);
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(83, 22);
            this.toolStripMenuItem4.Text = "3";
            this.toolStripMenuItem4.Click += new System.EventHandler(this.miPriorityOne_Click);
            // 
            // toolStripMenuItem5
            // 
            this.toolStripMenuItem5.Name = "toolStripMenuItem5";
            this.toolStripMenuItem5.Size = new System.Drawing.Size(83, 22);
            this.toolStripMenuItem5.Text = "4";
            this.toolStripMenuItem5.Click += new System.EventHandler(this.miPriorityOne_Click);
            // 
            // toolStripMenuItem6
            // 
            this.toolStripMenuItem6.Name = "toolStripMenuItem6";
            this.toolStripMenuItem6.Size = new System.Drawing.Size(83, 22);
            this.toolStripMenuItem6.Text = "5";
            this.toolStripMenuItem6.Click += new System.EventHandler(this.miPriorityOne_Click);
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.label9);
            this.panel2.Controls.Add(this.datImportEndTime);
            this.panel2.Controls.Add(this.btnRun);
            this.panel2.Controls.Add(this.label8);
            this.panel2.Controls.Add(this.datImportStartTime);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel2.Location = new System.Drawing.Point(3, 3);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(881, 52);
            this.panel2.TabIndex = 0;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(254, 20);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(89, 12);
            this.label9.TabIndex = 25;
            this.label9.Text = "上传结束日期：";
            // 
            // datImportEndTime
            // 
            this.datImportEndTime.CustomFormat = "yyyy-MM-dd";
            this.datImportEndTime.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.datImportEndTime.Location = new System.Drawing.Point(349, 16);
            this.datImportEndTime.Name = "datImportEndTime";
            this.datImportEndTime.Size = new System.Drawing.Size(88, 21);
            this.datImportEndTime.TabIndex = 24;
            // 
            // btnRun
            // 
            this.btnRun.Location = new System.Drawing.Point(490, 15);
            this.btnRun.Name = "btnRun";
            this.btnRun.Size = new System.Drawing.Size(75, 23);
            this.btnRun.TabIndex = 23;
            this.btnRun.Text = "查询";
            this.btnRun.UseVisualStyleBackColor = true;
            this.btnRun.Click += new System.EventHandler(this.butQuery_Click);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(28, 20);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(89, 12);
            this.label8.TabIndex = 22;
            this.label8.Text = "上传起始日期：";
            // 
            // datImportStartTime
            // 
            this.datImportStartTime.CustomFormat = "yyyy-MM-dd";
            this.datImportStartTime.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.datImportStartTime.Location = new System.Drawing.Point(123, 16);
            this.datImportStartTime.Name = "datImportStartTime";
            this.datImportStartTime.Size = new System.Drawing.Size(88, 21);
            this.datImportStartTime.TabIndex = 21;
            // 
            // contextMenuStrip2
            // 
            this.contextMenuStrip2.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItemExport2Xls});
            this.contextMenuStrip2.Name = "contextMenuStrip2";
            this.contextMenuStrip2.Size = new System.Drawing.Size(151, 26);
            // 
            // toolStripMenuItemExport2Xls
            // 
            this.toolStripMenuItemExport2Xls.Name = "toolStripMenuItemExport2Xls";
            this.toolStripMenuItemExport2Xls.Size = new System.Drawing.Size(152, 22);
            this.toolStripMenuItemExport2Xls.Text = "导出到Excel...";
            this.toolStripMenuItemExport2Xls.Click += new System.EventHandler(this.toolStripMenuItemExport2Xls_Click);
            // 
            // QueryFileStatusInfo
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(895, 495);
            this.Controls.Add(this.tabControl1);
            this.Name = "QueryFileStatusInfo";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "文件状态查询";
            this.tabControl1.ResumeLayout(false);
            this.pag1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.tabPage2.ResumeLayout(false);
            this.contextMenuStrip1.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.contextMenuStrip2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn7;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage pag1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.ComboBox cbxAreaStatStatus;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.ComboBox cbxGridStatStatus;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.ComboBox cbxESStatus;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ComboBox cbxEventDefineStatus;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cbxImportStatus;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.DateTimePicker datEnd;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button butQuery;
        private System.Windows.Forms.DateTimePicker datStart;
        private System.Windows.Forms.ListView lvNotStored;
        private System.Windows.Forms.ColumnHeader columnHeaderFileName2;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.Button btnRun;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.DateTimePicker datImportStartTime;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.DateTimePicker datImportEndTime;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem 入库优先级ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem miPriorityOne;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem3;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem4;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem5;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem6;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnProjectName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBeginTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEndTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnImportTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnImportStatus;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEventStatus;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnESStatus;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridStatus;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaStatus;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip2;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemExport2Xls;
    }
}