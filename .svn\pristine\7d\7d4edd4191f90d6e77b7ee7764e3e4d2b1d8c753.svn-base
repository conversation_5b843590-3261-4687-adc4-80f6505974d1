﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc
{
    abstract class LteFddIndoorStationAccept
    {
        protected Dictionary<string, LteTestAcceptResult> btsResultDic = new Dictionary<string, LteTestAcceptResult>();

        protected string[,,] resultGrid = null; // 小区个数，行数，列数

        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        protected LTECell lteCell;

        protected string btsName = "";

        public void Init(LTECell lteCell, string btsName)
        {
            this.lteCell = lteCell;
            this.btsName = btsName;
        }

        public virtual void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
        }

        public virtual void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            FillResultToSheet(eBook, 2, sectorIDs);
        }

        public virtual bool IsValidFile(FileInfo fileInfo)
        {
            return false;
        }

        public virtual void Clear()
        {
            btsResultDic.Clear();
        }

        public virtual int GetSectorCount(string btsName)
        {
            return btsResultDic.ContainsKey(btsName) ? btsResultDic[btsName].SectorCount : 0;
        }

        public List<string> BtsNames
        {
            get
            {
                return new List<string>(btsResultDic.Keys);
            }
        }

        protected virtual void FillResultToSheet(Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int i = 0; i < sectorIDs.Count; ++i)
            {
                int sid = sectorIDs[i];
                for (int row = 0; row < resultGrid.GetLength(1); ++row)
                {
                    for (int col = 0; col < resultGrid.GetLength(2); ++col)
                    {
                        object value = result.GetValue(sid, row, col);
                        InsertExcelValue(eBook, sheetIndex, resultGrid[i, row, col], value);
                    }
                }
            }
        }

        protected void InsertExcelValue(Excel.Workbook eBook, int sheetIndex, string cell, object value)
        {
            if (value == null)
            {
                return;
            }
            if (value is double && (double)value == double.MinValue)
            {
                return;
            }

            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[sheetIndex];
            Excel.Range rng = eSheet.get_Range(cell, Type.Missing);
            rng.set_Value(Type.Missing, value);
        }

        protected void InsertExcelValue(Excel.Workbook eBook, int sheetIndex, string cell, object value, bool IsColorRed)
        {
            if (value == null)
            {
                return;
            }
            if (value is double && (double)value == double.MinValue)
            {
                return;
            }

            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[sheetIndex];
            Excel.Range rng = eSheet.get_Range(cell, Type.Missing);
            rng.set_Value(Type.Missing, value);
            if (IsColorRed)
            {
                rng.Font.ColorIndex = 3;
            }
        }

        /// <summary>
        /// 根据文件中的小区名获取文件对应的小区
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected LTECell getTargetCell(TestPoint tp)
        {
            List<LTECell> cells = StationAcceptCellHelper_XJ.Instance.GetLTECellList(tp);
            foreach (var cell in cells)
            {
                if (cell.Name.Contains(lteCell.Name))
                {
                    return cell;
                }
            }
            return null;
        }

        /// <summary>
        /// 获取与目标小区匹配的采样点
        /// </summary>
        /// <param name="targetCell"></param>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected bool getValidTestPoint(LTECell targetCell, TestPoint tp)
        {
            int? curPCI = (short?)tp["lte_PCI"];
            if (curPCI == null)
            {
                return false;
            }
            int? curEARFCN = (int?)tp["lte_EARFCN"];
            if (curEARFCN == null)
            {
                return false;
            }

            int targetCellPCI;
            int targetCellEARFCN;
            if (targetCell == null)
            {
                targetCell = getTargetCell(tp);
                if (targetCell == null)
                {
                    return false;
                }
            }
            targetCellPCI = targetCell.PCI;
            targetCellEARFCN = targetCell.EARFCN;

            if (curPCI == targetCellPCI && curEARFCN == targetCellEARFCN)
            {
                return true;
            }
            return false;
        }
    }

    class FddIndoorAcpFtpDownload : LteFddIndoorStationAccept
    {
        protected Dictionary<string, CellKpi> cellDic = null;

        public FddIndoorAcpFtpDownload()
        {
            cellDic = new Dictionary<string, CellKpi>();

            resultGrid = new string[3, 4, 1];
            int idx = 16;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 1, 0] = "p" + (++row).ToString();
                resultGrid[i, 2, 0] = "p" + (++row).ToString();
                resultGrid[i, 3, 0] = "p" + (++row).ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            CellKpi kpiCell = AnaFile(fileInfo, fileManager, pci);
            if (string.IsNullOrEmpty(pci))
            {
                log.Info(string.Format("文件{0}获取PCI失败", fileInfo.Name));
                return;
            }
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            kpiCell.CalcResult();

            SaveResult(fileInfo, kpiCell, 0, sectorID);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("点下载");
        }

        protected virtual CellKpi AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci)
        {
            CellKpi targetKpiCell = null;
            if (!cellDic.TryGetValue(pci, out targetKpiCell))
            {
                targetKpiCell = new CellKpi(pci);
                cellDic[pci] = targetKpiCell;
            }

            LTECell targetCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (getValidTestPoint(targetCell, tp))
                {
                    targetKpiCell.AddPoint(tp);
                }
            }
            return targetKpiCell;
        }

        protected virtual void SaveResult(FileInfo fileInfo, CellKpi kpiCell, int colIndex, int sectorID)
        {
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            SetValue(result, sectorID, 0, colIndex, Math.Round(kpiCell.CoverRate, 4));
            SetValue(result, sectorID, 1, colIndex, kpiCell.AvgRsrp);
            SetValue(result, sectorID, 2, colIndex, kpiCell.AvgSinr);
            SetValue(result, sectorID, 3, colIndex, kpiCell.AvgDLSpeed);
        }

        protected void SetValue(LteTestAcceptResult result, int sectorID, int rowIdx, int colIdx, object value)
        {
            // 直接覆盖原有的值
            result.SetValue(sectorID, rowIdx, colIdx, value, true);
        }

        protected class CellKpi
        {
            public CellKpi(string PCI)
            {
                this.PCI = PCI;
            }

            public string PCI
            {
                get;
                protected set;
            }

            public int PointCount
            {
                get;
                private set;
            }

            public double AvgRsrp
            {
                get;
                private set;
            }

            public double AvgSinr
            {
                get;
                private set;
            }

            public double AvgULSpeed
            {
                get;
                private set;
            }

            public double AvgDLSpeed
            {
                get;
                private set;
            }

            public double CoverRate
            {
                get;
                private set;
            }

            public void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? rsrp = getRsrpValue(tp);

                float? sinr = getSinrValue(tp);

                getCoverValue(rsrp, sinr);

                getSpeedValue(tp);
            }

            private float? getRsrpValue(TestPoint tp)
            {
                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                }
                return rsrp;
            }

            private float? getSinrValue(TestPoint tp)
            {
                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }
                return sinr;
            }

            private void getCoverValue(float? rsrp, float? sinr)
            {
                if (sinr != null && sinr >= 6 && rsrp != null && rsrp >= -105)
                {
                    ++cntRsrpAndSinr;
                }
            }

            private void getSpeedValue(TestPoint tp)
            {
                double? ulSpeed = (double?)tp["lte_PDCP_UL_Mb"];
                if (ulSpeed != null)
                {
                    ++cntULSpeed;
                    sumULSpeed += (double)ulSpeed;
                }

                double? dlSpeed = (double?)tp["lte_PDCP_DL_Mb"];
                if (dlSpeed != null)
                {
                    ++cntDLSpeed;
                    sumDLSpeed += (double)dlSpeed;
                }
            }

            public virtual void CalcResult()
            {
                AvgRsrp = getDivisionResult(sumRsrp, cntRsrp);
                AvgSinr = getDivisionResult(sumSinr, cntSinr);
                AvgULSpeed = getDivisionResult(sumULSpeed, cntULSpeed);
                AvgDLSpeed = getDivisionResult(sumDLSpeed, cntDLSpeed);
                CoverRate = getDivisionResult(cntRsrpAndSinr, PointCount, 4);
            }

            private double getDivisionResult(double value, int count, int places = 2)
            {
                if (count == 0)
                {
                    return double.MinValue;
                }
                double res = Math.Round(value / count, places);
                return res;
            }

            private double sumRsrp;
            private int cntRsrp;

            private double sumSinr;
            private int cntSinr;

            private double sumULSpeed;
            private int cntULSpeed;

            private double sumDLSpeed;
            private int cntDLSpeed;

            private int cntRsrpAndSinr;
        }
    }

    class FddIndoorAcpFtpUpload : FddIndoorAcpFtpDownload
    {
        public FddIndoorAcpFtpUpload()
        {
            cellDic = new Dictionary<string, CellKpi>();
            resultGrid = new string[3, 3, 1];
            int idx = 20;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 1, 0] = "p" + (++row).ToString();
                resultGrid[i, 2, 0] = "p" + (++row).ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("点上传");
        }

        protected override void SaveResult(FileInfo fileInfo, CellKpi kpiCell, int colIndex, int sectorID)
        {
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            SetValue(result, sectorID, 0, colIndex, kpiCell.AvgRsrp);
            SetValue(result, sectorID, 1, colIndex, kpiCell.AvgSinr);
            SetValue(result, sectorID, 2, colIndex, kpiCell.AvgULSpeed);
        }
    }

    class FddIndoorAcpHandover : FddIndoorAcpRrcRate
    {
        public FddIndoorAcpHandover()
        {
            evtRequList = new List<int> { 850, 898 };
            evtSuccList = new List<int> { 851, 899 };

            resultGrid = new string[3, 1, 2];
            int idx = 26;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("切换");
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            CellKpi kpiCell = AnaFile(fileInfo, fileManager, pci, sectorID);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, sectorID);
        }

        protected override CellKpi AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            CellKpi kpiCell = new CellKpi(PCI);
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++kpiCell.RequestCnt;
                }
                if (evtSuccList.Contains(evt.ID))
                {
                    ++kpiCell.SucceedCnt;
                }
            }
            return kpiCell;
        }
    }

    class FddIndoorAcpCsfbRate : FddIndoorAcpRrcRate
    {
        public FddIndoorAcpCsfbRate()
        {
            resultGrid = new string[3, 1, 2];
            int idx = 12;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != (int)MoMtFile.MoFlag)
            {
                return false;
            }
            return (fileInfo.Name.ToUpper().Contains("CSFB"));
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            AcpAutoCsfbRate.GetCsfbEventIdsByDevice(fileInfo.DeviceType, out evtRequList, out evtSuccList);

            CellKpi kpiCell = AnaFile(fileInfo, fileManager, pci, sectorID);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }

            SaveResult(fileInfo, kpiCell, sectorID);
        }
    }

    class FddIndoorAcpRrcRate : LteFddIndoorStationAccept
    {
        protected List<int> evtSuccList;
        protected List<int> evtRequList;

        public FddIndoorAcpRrcRate()
        {
            evtRequList = new List<int>() { 855 };
            evtSuccList = new List<int>() { 856 };

            resultGrid = new string[3, 1, 2];
            int idx = 7;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            CellKpi kpiCell = AnaFile(fileInfo, fileManager, pci, sectorID);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }

            SaveResult(fileInfo, kpiCell, sectorID);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("附着");
        }

        protected virtual CellKpi AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            CellKpi kpiCell = new CellKpi(PCI);
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++kpiCell.RequestCnt;
                }
                if (evtSuccList.Contains(evt.ID))
                {
                    ++kpiCell.SucceedCnt;
                }
            }
            return kpiCell;
        }

        protected virtual void SaveResult(FileInfo fileInfo, CellKpi kpiCell, int sectorID)
        {
            int rowIdx = 0;
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(sectorID, rowIdx, 0, kpiCell.RequestCnt, false);
            result.SetValue(sectorID, rowIdx, 1, kpiCell.SucceedCnt, false);
        }

        protected class CellKpi
        {
            public int RequestCnt
            {
                get;
                set;
            }

            public int SucceedCnt
            {
                get;
                set;
            }

            public string PCI
            {
                get;
                private set;
            }

            public CellKpi(string PCI)
            {
                this.PCI = PCI;
            }
        }
    }

    class FddIndoorAcpErabRate : FddIndoorAcpRrcRate
    {
        public FddIndoorAcpErabRate()
        {
            evtRequList = new List<int>() { 858 };
            evtSuccList = new List<int>() { 859 };

            resultGrid = new string[3, 1, 2];
            int idx = 8;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }
    }

    class FddIndoorAcpAccRate : FddIndoorAcpRrcRate
    {
        public FddIndoorAcpAccRate()
        {
            evtRequList = new List<int>() { 22 };
            evtSuccList = new List<int>() { 23 };

            resultGrid = new string[3, 1, 2];
            int idx = 9;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }
    }

    class FddIndoorAcp24ReselectRate : FddIndoorAcpRrcRate
    {
        public FddIndoorAcp24ReselectRate()
        {
            resultGrid = new string[3, 1, 2];
            int idx = 10;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("重选");
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            SwitchEventIDs(fileInfo.DeviceType);

            CellKpi kpiCell = AnaFile(fileInfo, fileManager, pci, sectorID);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }

            SaveResult(fileInfo, kpiCell, sectorID);
        }

        protected virtual void SwitchEventIDs(int deviceType)
        {
            if (deviceType == 21 || deviceType == 8) // 中兴
            {
                evtRequList = new List<int>() { 852 };
                evtSuccList = new List<int>() { 853 };
            }
            else
            {
                evtRequList = new List<int>() { 1306 };
                evtSuccList = new List<int>() { 1306 };
            }
        }
    }

    class FddIndoorAcpVolteVoiceMo : FddIndoorAcpRrcRate
    {
        public FddIndoorAcpVolteVoiceMo()
        {
            evtRequList = new List<int>() { 1070 };
            evtSuccList = new List<int>() { 1072 };

            resultGrid = new string[3, 1, 2];
            int idx = 13;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "P" + row.ToString();
                resultGrid[i, 0, 1] = "W" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != (int)MoMtFile.MoFlag)
            {
                return false;
            }
            return (fileInfo.Name.ToUpper().Contains("语音VOLTE"));
        }

        protected override void SaveResult(FileInfo fileInfo, CellKpi kpiCell, int sectorID)
        {
            int rowIdx = 0;
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(sectorID, rowIdx, 0, kpiCell.RequestCnt, false);
            result.SetValue(sectorID, rowIdx, 1, kpiCell.SucceedCnt, false);
        }
    }

    class FddIndoorAcpVolteVoiceMt : FddIndoorAcpRrcRate
    {
        public FddIndoorAcpVolteVoiceMt()
        {
            evtRequList = new List<int>() { 1071 };
            evtSuccList = new List<int>() { 1073 };

            resultGrid = new string[3, 1, 2];
            int idx = 14;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "P" + row.ToString();
                resultGrid[i, 0, 1] = "W" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != (int)MoMtFile.MtFlag)
            {
                return false;
            }
            return (fileInfo.Name.ToUpper().Contains("语音VOLTE"));
        }

        protected override void SaveResult(FileInfo fileInfo, CellKpi kpiCell, int sectorID)
        {
            int rowIdx = 0;
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(sectorID, rowIdx, 0, kpiCell.RequestCnt, false);
            result.SetValue(sectorID, rowIdx, 1, kpiCell.SucceedCnt, false);
        }
    }

    class FddIndoorAcpLeakOutLock : FddIndoorAcpFtpDownload
    {
        public FddIndoorAcpLeakOutLock()
        {
            resultGrid = new string[3, 1, 1];
            int idx = 24;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return (fileInfo.Name.Contains("锁频"));
        }

        protected override void SaveResult(FileInfo fileInfo, CellKpi kpiCell, int colIndex, int sectorID)
        {
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            CellKpiLeakOut kpi = kpiCell as CellKpiLeakOut;
            SetValue(result, sectorID, 0, colIndex, kpi.DivulgeRate);
        }

        protected override CellKpi AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci)
        {
            CellKpiLeakOut targetKpiCell = new CellKpiLeakOut(pci);

            LTECell targetCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (getValidTestPoint(targetCell, tp))
                {
                    float? rsrp = (float?)tp["lte_RSRP"];
                    if (rsrp != null && rsrp != -10000000)
                    {
                        ++targetKpiCell.TotalTpCount;
                        if (rsrp <= -115)
                        {
                            ++targetKpiCell.ValidTpCount;
                        }
                    }
                }
            }
            targetKpiCell.CalcResult();
            return targetKpiCell;
        }

        protected class CellKpiLeakOut : CellKpi
        {
            public CellKpiLeakOut(string PCI)
                : base(PCI)
            {
            }

            public int ValidTpCount = 0;
            public int TotalTpCount = 0;
            public double DivulgeRate
            {
                get;
                private set;
            }

            public override void CalcResult()
            {
                if (TotalTpCount == 0)
                {
                    DivulgeRate = 0;
                }
                else
                {
                    DivulgeRate = Math.Round((double)ValidTpCount / TotalTpCount, 4);
                }
            }
        }
    }

    class FddIndoorAcpLeakOutScan : FddIndoorAcpLeakOutLock
    {
        public FddIndoorAcpLeakOutScan()
        {
            resultGrid = new string[3, 1, 1];
            int idx = 24;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "AC" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("扫频");
        }

        protected override CellKpi AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci)
        {
            LTECell targetCell = GetLeakOutScanTestCell(fileManager);
            if (targetCell == null)
            {
                return null;
            }

            CellKpiLeakOut targetKpiCell = new CellKpiLeakOut(pci);
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                getLeakOutKpi(targetKpiCell, tp, targetCell.Token);
            }
            targetKpiCell.CalcResult();
            return targetKpiCell;
        }

        public LTECell GetLeakOutScanTestCell(DTFileDataManager fileManager)
        {
            //根据文件名中的频点PCI获取宏目标室分小区
            string[] strArray = fileManager.FileName.Split('_');
            if (strArray.Length == 6)
            {
                string cellName = strArray[2];
                int? pci = getValidData(strArray[3], "PCI");
                int? earfcn = getValidData(strArray[4], "频点");

                DateTime time = DateTime.Now;
                if (fileManager.TestPoints.Count > 0)
                {
                    time = fileManager.TestPoints[0].DateTime;
                }
                List<LTECell> cells = StationAcceptCellHelper_XJ.Instance.GetLTECellListByEarfcnPci(time, earfcn, pci);
                foreach (LTECell cell in cells)
                {
                    if (cell.Name.Contains(cellName))
                    {
                        return cell;
                    }
                }
            }
            return null;
        }

        private int? getValidData(string str, string type)
        {
            int? data = null;
            if (str.Contains(type))
            {
                string strData = str.Replace(type, "");
                int iData;
                if (int.TryParse(strData, out iData))
                {
                    data = iData;
                }
            }
            return data;
        }

        private void getLeakOutKpi(CellKpiLeakOut targetKpiCell, TestPoint tp, string token)
        {
            //获取宏站主控小区
            LTECell cell = StationAcceptCellHelper_XJ.Instance.GetLTECell(tp);
            if (cell == null)
            {
                return;
            }

            float? rsrp = (float?)tp["lte_RSRP"];
            if (rsrp != null && rsrp != -10000000)
            {
                ++targetKpiCell.TotalTpCount;
                if (cell.Type == LTEBTSType.Outdoor && cell.Token != token)
                {
                    bool hasValidTP = hasValidTestPoint(tp, token, rsrp);
                    if (hasValidTP)
                    {
                        ++targetKpiCell.ValidTpCount;
                    }
                }
            }
        }

        private bool hasValidTestPoint(TestPoint tp, string token, float? rsrp)
        {
            for (int i = 0; i < 10; i++)
            {
                //获取邻区为目标室分小区的采样点
                LTECell nCell = MultiStationAutoAcceptManager.GetLeakOutScanTpNCell(tp, i);
                if (nCell != null && nCell.Token == token)
                {
                    float? nRsrp = (float?)tp["lte_NCell_RSRP", i];
                    //宏站主控小区与目标室分小区场强在10db以内则为泄漏点
                    if (nRsrp != null && rsrp - nRsrp <= 10)
                    {
                        return false;
                    }
                }
            }
            return true;
        }
    }

    class FddIndoorAcpLeveling : LteFddIndoorStationAccept
    {
        public int FileCount { get { return fileList.Count; } }
        protected List<string> fileList = new List<string>();
        protected Dictionary<int, string[]> resultDic = new Dictionary<int, string[]>();
        public FddIndoorAcpLeveling()
        {
            resultGrid = new string[1, 1, 13] {
                {
                    { "a2", "b2", "c2", "d2", "e2", "f2", "g2", "h2", "i2", "j2", "k2", "l2", "m2" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            fileList.Add(fileInfo.Name);
            int count = fileList.Count;
            int index = count + 1;
            resultDic[count - 1] = new string[13] { "a" + index, "b" + index, "c" + index, "d" + index, "e" + index,
                "f" + index, "g" + index, "h" + index, "i" + index, "j" + index, "k" + index, "l" + index, "m" + index };

            AnaFile(fileInfo, fileManager, pci);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传") || fileInfo.Name.Contains("下载") || fileInfo.Name.Contains("切换");
        }

        private void AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI)
        {
            CellKpi targetKpiCell = new CellKpi(PCI);

            LTECell targetCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (getValidTestPoint(targetCell, tp))
                {
                    targetKpiCell.AddPoint(tp);
                }
            }
            foreach (Event evt in fileManager.Events)
            {
                targetKpiCell.AddEvent(evt);
            }

            targetKpiCell.CalcResult();

            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName,
                    resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            SetValue(result, fileInfo.Name, targetKpiCell, fileList.Count - 1);
        }

        private void SetValue(LteTestAcceptResult result, string fileName, CellKpi cellKPI, int sectorID)
        {
            if (fileName.Contains("下载"))
            {
                setDownloadValue(result, fileName, cellKPI, sectorID);
            }
            else if (fileName.Contains("上传"))
            {
                setUploadValue(result, fileName, cellKPI, sectorID);
            }
            else if (fileName.Contains("切换"))
            {
                setHandoverValue(result, fileName, cellKPI, sectorID);
            }
        }

        private void setDownloadValue(LteTestAcceptResult result, string fileName, CellKpi cellKPI, int sectorID)
        {
            result.SetValue(sectorID, 0, 0, fileName, true);
            result.SetValue(sectorID, 0, 2, cellKPI.AvgRsrp, true);
            result.SetValue(sectorID, 0, 3, cellKPI.AvgSinr, true);
            result.SetValue(sectorID, 0, 4, cellKPI.SinrMoreThan6Rate, true);
            result.SetValue(sectorID, 0, 5, cellKPI.SinrMoreThan9Rate, true);
            if (cellKPI.RsrpMoreThan105Rate < 0.95)
            {
                //小与95%特殊标识
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.RsrpMoreThan105Rate;
                result.SetValue(sectorID, 0, 6, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 6, cellKPI.RsrpMoreThan105Rate, true);
            }
            result.SetValue(sectorID, 0, 7, cellKPI.RsrpMoreThan95Rate, true);
            result.SetValue(sectorID, 0, 8, cellKPI.RsrpMoreThan85Rate, true);
            if ((cellKPI.AvgDLSpeed < 0.45 && fileName.Contains("双路")) || (cellKPI.AvgDLSpeed < 0.28 && fileName.Contains("单路")))
            {
                //双路下行吞吐量小与45Mbit/s,单路小与28Mbit/s特殊标识
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.AvgDLSpeed;
                result.SetValue(sectorID, 0, 10, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 10, cellKPI.AvgDLSpeed, true);
            }
            result.SetValue(sectorID, 0, 12, cellKPI.MaxDLSpeed, true);
        }

        private void setUploadValue(LteTestAcceptResult result, string fileName, CellKpi cellKPI, int sectorID)
        {
            result.SetValue(sectorID, 0, 0, fileName, true);
            result.SetValue(sectorID, 0, 2, cellKPI.AvgRsrp, true);
            result.SetValue(sectorID, 0, 3, cellKPI.AvgSinr, true);
            result.SetValue(sectorID, 0, 4, cellKPI.SinrMoreThan6Rate, true);
            result.SetValue(sectorID, 0, 5, cellKPI.SinrMoreThan9Rate, true);
            if (cellKPI.RsrpMoreThan105Rate < 0.95)
            {
                //小与95%特殊标识
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.RsrpMoreThan105Rate;
                result.SetValue(sectorID, 0, 6, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 6, cellKPI.RsrpMoreThan105Rate, true);
            }
            result.SetValue(sectorID, 0, 7, cellKPI.RsrpMoreThan95Rate, true);
            result.SetValue(sectorID, 0, 8, cellKPI.RsrpMoreThan85Rate, true);
            if (cellKPI.AvgULSpeed < 0.06)
            {
                //上行吞吐量小与6Mbit/s特殊标识
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.AvgULSpeed;
                result.SetValue(sectorID, 0, 9, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 9, cellKPI.AvgULSpeed, true);
            }
            if (cellKPI.MaxULSpeed < 0.09)
            {
                //上行吞吐峰值小与9Mbit/s特殊标识
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.MaxULSpeed;
                result.SetValue(sectorID, 0, 11, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 11, cellKPI.MaxULSpeed, true);
            }
        }

        private void setHandoverValue(LteTestAcceptResult result, string fileName, CellKpi cellKPI, int sectorID)
        {
            result.SetValue(sectorID, 0, 0, fileName, true);
            if (cellKPI.HandoverRate < 0.98)
            {
                //切换成功率小与98%特殊标识
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.HandoverRate;
                result.SetValue(sectorID, 0, 1, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 1, cellKPI.HandoverRate, true);
            }
            result.SetValue(sectorID, 0, 2, cellKPI.AvgRsrp, true);
            result.SetValue(sectorID, 0, 3, cellKPI.AvgSinr, true);
            result.SetValue(sectorID, 0, 4, cellKPI.SinrMoreThan6Rate, true);
            result.SetValue(sectorID, 0, 5, cellKPI.SinrMoreThan9Rate, true);
            if (cellKPI.RsrpMoreThan105Rate < 0.95)
            {
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.RsrpMoreThan105Rate;

                result.SetValue(sectorID, 0, 6, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 6, cellKPI.RsrpMoreThan105Rate, true);
            }
            result.SetValue(sectorID, 0, 7, cellKPI.RsrpMoreThan95Rate, true);
            result.SetValue(sectorID, 0, 8, cellKPI.RsrpMoreThan85Rate, true);
        }

        public void ClearData()
        {
            btsResultDic.Clear();
        }

        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }
            FillResultToSheet(eBook, 3, sectorIDs);
        }

        protected override void FillResultToSheet(Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int i = 0; i < resultDic.Count; ++i)
            {
                int sid = i;
                for (int row = 0; row < resultGrid.GetLength(1); ++row)
                {
                    for (int col = 0; col < resultGrid.GetLength(2); ++col)
                    {
                        object value = result.GetValue(sid, row, col);
                        if (value is Dictionary<bool, double>)
                        {
                            Dictionary<bool, double> dic = value as Dictionary<bool, double>;
                            InsertExcelValue(eBook, sheetIndex, resultDic[i][col], dic[true], true);
                        }
                        else
                        {
                            InsertExcelValue(eBook, sheetIndex, resultDic[i][col], value);
                        }
                    }
                }
            }
        }

        protected class CellKpi
        {
            public CellKpi(string PCI)
            {
                this.PCI = PCI;
            }

            private readonly List<int> evtSuccList = new List<int>() { 851, 899 };
            private readonly List<int> evtFailList = new List<int>() { 870, 1100 };

            private int cntEvent;
            private int cntFailed;
            private int cntSucc;

            private double sumRsrp;
            private int cntRsrp;
            private double sumSinr;
            private int cntSinr;
            private double sumULSpeed;
            private int cntULSpeed;
            private double sumDLSpeed;
            private int cntDLSpeed;

            private int cntRsrpAndSinr;
            private int cntRsrpMoreThan85;
            private int cntRsrpMoreThan95;
            private int cntRsrpMoreThan105;
            private int cntSinrMoreThan6;
            private int cntSinrMoreThan9;

            private double maxDLSpeed = 0;
            public double MaxDLSpeed
            {
                get { return maxDLSpeed; }
            }

            private double maxULSpeed = 0;
            public double MaxULSpeed
            {
                get { return maxULSpeed; }
            }

            public string PCI
            {
                get;
                private set;
            }

            public int PointCount
            {
                get;
                private set;
            }

            public double AvgRsrp
            {
                get;
                private set;
            }

            public double AvgSinr
            {
                get;
                private set;
            }

            public double AvgULSpeed
            {
                get;
                private set;
            }

            public double AvgDLSpeed
            {
                get;
                private set;
            }

            public double HandoverRate
            {
                get;
                private set;
            }

            public double SinrMoreThan6Rate
            {
                get;
                private set;
            }

            public double SinrMoreThan9Rate
            {
                get;
                private set;
            }

            public double RsrpMoreThan105Rate
            {
                get;
                private set;
            }

            public double RsrpMoreThan95Rate
            {
                get;
                private set;
            }

            public double RsrpMoreThan85Rate
            {
                get;
                private set;
            }

            public void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? rsrp = getRsrp(tp);
                float? sinr = getSinr(tp);
                getRsrpAndSinr(rsrp, sinr);
                getSpeedValue(tp);
            }

            private float? getRsrp(TestPoint tp)
            {
                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;

                    if (rsrp >= -85)
                    {
                        cntRsrpMoreThan85++;
                    }
                }
                return rsrp;
            }

            private float? getSinr(TestPoint tp)
            {
                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;

                    if (sinr >= 6)
                    {
                        cntSinrMoreThan6++;
                    }
                    if (sinr >= 9)
                    {
                        cntSinrMoreThan9++;
                    }
                }
                return sinr;
            }

            private void getRsrpAndSinr(float? rsrp, float? sinr)
            {
                if (rsrp != null && sinr != null)
                {
                    cntRsrpAndSinr++;
                    if (rsrp >= -105 && sinr >= 6)
                    {
                        cntRsrpMoreThan105++;
                    }
                    if (rsrp >= -95 && sinr >= 9)
                    {
                        cntRsrpMoreThan95++;
                    }
                }
            }

            private void getSpeedValue(TestPoint tp)
            {
                double? ulSpeed = (double?)tp["lte_PDCP_UL_Mb"];
                if (ulSpeed != null)
                {
                    ++cntULSpeed;
                    sumULSpeed += (double)ulSpeed;

                    if (ulSpeed > maxULSpeed)
                    {
                        maxULSpeed = (float)ulSpeed;
                    }
                }

                double? dlSpeed = (double?)tp["lte_PDCP_DL_Mb"];
                if (dlSpeed != null)
                {
                    ++cntDLSpeed;
                    sumDLSpeed += (double)dlSpeed;
                    if (dlSpeed > maxDLSpeed)
                    {
                        maxDLSpeed = (float)dlSpeed;
                    }
                }
            }

            public void AddEvent(Event evt)
            {
                cntEvent++;
                if (evtFailList.Contains(evt.ID))
                {
                    cntFailed++;
                }
                if (evtSuccList.Contains(evt.ID))
                {
                    cntSucc++;
                }
            }

            public void CalcResult()
            {
                AvgRsrp = getDivisionResult(sumRsrp, cntRsrp);
                AvgSinr = getDivisionResult(sumSinr, cntSinr);
                AvgULSpeed = getDivisionResult(sumULSpeed, cntULSpeed);
                AvgDLSpeed = getDivisionResult(sumDLSpeed, cntDLSpeed);
                HandoverRate = getDivisionResult(cntSucc, cntSucc + cntFailed, 4);
                SinrMoreThan6Rate = getDivisionResult(cntSinrMoreThan6, cntSinr, 4);
                SinrMoreThan9Rate = getDivisionResult(cntSinrMoreThan9, cntSinr, 4);
                RsrpMoreThan105Rate = getDivisionResult(cntRsrpMoreThan105, cntRsrpAndSinr, 4);
                RsrpMoreThan95Rate = getDivisionResult(cntRsrpMoreThan95, cntRsrpAndSinr, 4);
                RsrpMoreThan85Rate = getDivisionResult(cntRsrpMoreThan85, cntRsrp, 4);
            }

            private double getDivisionResult(double value, int count, int places = 2)
            {
                if (count == 0)
                {
                    return double.MinValue;
                }
                double res = Math.Round(value / count, places);
                return res;
            }
        }
    }

    class FddIndoorAcpCellName : LteFddIndoorStationAccept
    {
        public FddIndoorAcpCellName()
        {
            resultGrid = new string[3, 1, 1];
            int idx = 6;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "a" + row.ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            object value = result.GetValue(sectorID, 0, 0);
            if (value != null)
            {
                return;
            }

            result.SetValue(sectorID, 0, 0, pci, false);
        }

        protected override void FillResultToSheet(Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int i = 0; i < sectorIDs.Count; ++i)
            {
                int sid = sectorIDs[i];
                for (int row = 0; row < resultGrid.GetLength(1); ++row)
                {
                    for (int col = 0; col < resultGrid.GetLength(2); ++col)
                    {
                        object value = result.GetValue(sid, row, col);
                        string cellDes = string.Format("Cell-{0}(PCI:{1})", (i + 1), value.ToString());
                        InsertExcelValue(eBook, sheetIndex, resultGrid[i, row, col], cellDes);
                    }
                }
            }
        }
    }

    /// <summary>
    /// 查询其他平台推送的规划数据 一个站只执行一次
    /// </summary>
    class FddIndoorAcpCellPlanPara : LteFddIndoorStationAccept
    {
        public FddIndoorAcpCellPlanPara()
        {
            resultGrid = new string[3, 24, 1];
            int colIdx = 8;
            int step = 8;
            for (int i = 0; i < 3; i++)
            {
                #region 基站描述
                resultGrid[i, 0, 0] = "e3";
                resultGrid[i, 1, 0] = "z3"  ;
                resultGrid[i, 2, 0] = "e5"  ;
                resultGrid[i, 3, 0] = "z5"  ;
                resultGrid[i, 4, 0] = "e7"  ;
                resultGrid[i, 5, 0] = "z7"  ;
                resultGrid[i, 6, 0] = "e9"  ;
                resultGrid[i, 7, 0] = "z9"  ;
                resultGrid[i, 8, 0] = "e11";
                #endregion
                #region 基站参数
                int row = 14;
                for (int j = 9; j < 15; j++)
                {
                    resultGrid[i, j, 0] = "H" + (++row).ToString();
                }
                #endregion

                string col = ExcelHelper.GetColNameByIndex(colIdx + step * i);
                resultGrid[i, 15, 0] = col + 21;
                row = 22;
                for (int j = 16; j < 24; j++)
                {
                    resultGrid[i, j, 0] = col + (++row).ToString();
                }
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (lteCell == null)
            {
                return;
            }
            LTEBTS lteBts = lteCell.BelongBTS;
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            //从数据库读取规划信息
            DiyQueryFddPlanData query = new DiyQueryFddPlanData();
            query.SetCondition(lteBts.BTSID, btsName, lteBts.Type);
            query.Query();
            List<FddPlanBtsCellData> btsCellData = query.PlanBtsCellInfo;

            saveResult(fileInfo, btsCellData);
        }

        protected virtual void saveResult(FileInfo fileInfo, List<FddPlanBtsCellData> btsCellData)
        {
            LteTestAcceptResult result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
            btsResultDic.Add(btsName, result);
            
            int sectorID = 0;
            foreach (FddPlanBtsCellData cellData in btsCellData)
            {
                int row = 0;
                #region 基站描述
                result.SetValue(sectorID, row++, 0, cellData.BtsName, true);
                string date = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(fileInfo.BeginTime * 1000L).ToShortDateString();
                result.SetValue(sectorID, row++, 0, date, true);
                result.SetValue(sectorID, row++, 0, cellData.ENodeBID, true);
                result.SetValue(sectorID, row++, 0, cellData.CoverScenes, true);
                string address = btsName.Substring(0, btsName.IndexOf('-'));
                result.SetValue(sectorID, row++, 0, address, true);
                result.SetValue(sectorID, row++, 0, cellData.CoverArea, true);
                result.SetValue(sectorID, row++, 0, cellData.DeviceType, true);
                result.SetValue(sectorID, row++, 0, cellData.CoverRange, true);
                result.SetValue(sectorID, row++, 0, cellData.BtsType, true);
                #endregion
                #region 基站参数
                result.SetValue(sectorID, row++, 0, cellData.Longitude, true);
                result.SetValue(sectorID, row++, 0, cellData.Latitude, true);
                result.SetValue(sectorID, row++, 0, cellData.TAC, true);
                result.SetValue(sectorID, row++, 0, cellData.ENodeBID, true);
                result.SetValue(sectorID, row++, 0, cellData.Combiner, true);
                result.SetValue(sectorID, row++, 0, cellData.SingleDouble, true);
                #endregion
                #region 小区工程参数
                string cellDes = string.Format("Cell-{0}(PCI:{1})", (sectorID + 1), cellData.PCI);
                result.SetValue(sectorID, row++, 0, cellDes, true);
                result.SetValue(sectorID, row++, 0, cellData.CarrierInfo, true);
                result.SetValue(sectorID, row++, 0, cellData.CellID, true);
                result.SetValue(sectorID, row++, 0, cellData.PCI, true);
                result.SetValue(sectorID, row++, 0, cellData.FrequencyBand, true);
                result.SetValue(sectorID, row++, 0, cellData.Earfcn, true);
                result.SetValue(sectorID, row++, 0, cellData.CellBroadBand, true);
                result.SetValue(sectorID, row++, 0, cellData.RootSN, true);
                #endregion
                #region 小区网优参数
                result.SetValue(sectorID, row, 0, cellDes, true);
                #endregion
            }
        }

        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            FillResultToSheet(eBook, 1, sectorIDs);
        }
    }

    /// <summary>
    /// 查询其他平台推送的实测数据 一个站只执行一次
    /// </summary>
    class FddIndoorAcpCellActualPara : LteFddIndoorStationAccept
    {
        public FddIndoorAcpCellActualPara()
        {
            resultGrid = new string[3, 17, 1];
            int colIdx = 11;
            int step = 8;
            for (int i = 0; i < 3; i++)
            {
                #region 基站参数
                resultGrid[i, 0, 0] = "n17";
                resultGrid[i, 1, 0] = "n18";
                #endregion

                string col = ExcelHelper.GetColNameByIndex(colIdx + step * i);
                int row = 22;
                for (int j = 2; j < 9; j++)
                {
                    resultGrid[i, j, 0] = col + (++row).ToString();
                }
                row = 31;
                for (int j = 9; j < 17; j++)
                {
                    resultGrid[i, j, 0] = col + (++row).ToString();
                }
            }
        }
        
        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (lteCell == null)
            {
                return;
            }
            LTEBTS lteBts = lteCell.BelongBTS;
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            //从数据库读取规划信息
            DiyQueryFddActualData query = new DiyQueryFddActualData();
            query.SetCondition(lteBts.BTSID, btsName, lteBts.Type);
            query.Query();
            List<FddActualBtsCellData> btsCellData = query.ActualBtsCellInfo;

            saveResult(btsCellData);
        }

        protected virtual void saveResult(List<FddActualBtsCellData> btsCellData)
        {
            LteTestAcceptResult result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
            btsResultDic.Add(btsName, result);

            int sectorID = 0;
            foreach (FddActualBtsCellData cellData in btsCellData)
            {
                int row = 0;
                #region 基站参数
                result.SetValue(sectorID, row++, 0, cellData.TAC, true);
                result.SetValue(sectorID, row++, 0, cellData.ENodeBID, true);
                #endregion
                #region 小区工程参数
                result.SetValue(sectorID, row++, 0, cellData.CarrierInfo, true);
                result.SetValue(sectorID, row++, 0, cellData.CellID, true);
                result.SetValue(sectorID, row++, 0, cellData.PCI, true);
                result.SetValue(sectorID, row++, 0, cellData.FrequencyBand, true);
                result.SetValue(sectorID, row++, 0, cellData.Earfcn, true);
                result.SetValue(sectorID, row++, 0, cellData.CellBroadBand, true);
                result.SetValue(sectorID, row++, 0, cellData.RootSN, true);
                #endregion
                #region 小区网优参数
                result.SetValue(sectorID, row++, 0, cellData.RsPower, true);
                result.SetValue(sectorID, row++, 0, cellData.PA, true);
                result.SetValue(sectorID, row++, 0, cellData.PB, true);
                result.SetValue(sectorID, row++, 0, cellData.PDCCH, true);
                result.SetValue(sectorID, row++, 0, cellData.VOLTE, true);
                result.SetValue(sectorID, row++, 0, cellData.SRVCC, true);
                result.SetValue(sectorID, row++, 0, cellData.A2Threshold, true);
                result.SetValue(sectorID, row, 0, cellData.A4Threshold, true);
                #endregion
            }
        }

        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            FillResultToSheet(eBook, 1, sectorIDs);
        }
    }

    /// <summary>
    /// 查询App平台推送的天线下数据 一个站只执行一次
    /// </summary>
    class FddIndoorAcpCellAntenna : LteFddIndoorStationAccept
    {
        protected Dictionary<int, string[]> resultDic = new Dictionary<int, string[]>();
        public FddIndoorAcpCellAntenna()
        {
            resultGrid = new string[1, 1, 21] {
                {
                    { "a3", "b3", "c3", "d3", "e3", "f3", "g3", "h3", "i3", "j3", "k3", "l3", "m3", "n3", "o3", "p3", "q3", "r3", "s3", "t3", "u3" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (lteCell == null)
            {
                return;
            }
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            FddDatabaseSetting setting;
            if (!DiyQueryFddDBSetting.GetInstance().DatabaseSetting.TryGetValue("单验天线下", out setting))
            {
                return;
            }

            DiyQueryFddDBAntenna query = new DiyQueryFddDBAntenna(lteCell.ECI, setting);
            query.Query();
            Dictionary<string, FddIndoorDBAntennaResult> antennaResult = combineFloorAndType(query.DataList);

            InitResultGrid(antennaResult.Count);

            SaveResult(antennaResult);
        }

        private void InitResultGrid(int count)
        {
            for (int i = 1; i <= count; i++)
            {
                int index = i + 2;
                resultDic[i - 1] = new string[21] { "a" + index, "b" + index, "c" + index, "d" + index, "e" + index, "f" + index, "g" + index,
                "h" + index, "i" + index, "j" + index, "k" + index, "l" + index, "m" + index, "n" + index, "o" + index, "p" + index, "q" + index,
                "r" + index, "s" + index, "t" + index, "u" + index  };
            }
        }

        private Dictionary<string, FddIndoorDBAntennaResult> combineFloorAndType(List<FddIndoorDBAntenna> dataList)
        {
            //<楼层,天线数据>
            Dictionary<string, FddIndoorDBAntennaResult> floordata = new Dictionary<string, FddIndoorDBAntennaResult>();
            foreach (FddIndoorDBAntenna data in dataList)
            {
                FddIndoorDBAntennaResult antenna;
                if (!floordata.TryGetValue(data.Floor, out antenna))
                {
                    antenna = new FddIndoorDBAntennaResult();
                    floordata.Add(data.Floor, antenna);
                    antenna.Floor = data.Floor;
                }
                
                if (antenna.FloorData.ContainsKey(data.Type))
                {
                    antenna.FloorData[data.Type].Add(data);
                }
            }

            foreach (var item in floordata.Values)
            {
                item.CalculateData();
            }
            return floordata;
        }

        private void SaveResult(Dictionary<string, FddIndoorDBAntennaResult> antennaResult)
        {
            LteTestAcceptResult result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
            btsResultDic.Add(btsName, result);

            int sectorID = 0;
            int row = 0;
            foreach (FddIndoorDBAntennaResult data in antennaResult.Values)
            {
                int col = 0;
                result.SetValue(sectorID, row, col++, data.Floor, true);
                foreach (var avgData in data.AvgFloorData)
                {
                    result.SetValue(sectorID, row, col++, avgData.Rsrp, true);
                }
                sectorID++;
            }
        }

        protected override void FillResultToSheet(Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int sid = 0; sid < resultDic.Count; ++sid)
            {
                for (int col = 0; col < resultGrid.GetLength(2); ++col)
                {
                    object value = result.GetValue(sid, 0, col);
                    if (value is Dictionary<bool, double>)
                    {
                        Dictionary<bool, double> dic = value as Dictionary<bool, double>;
                        InsertExcelValue(eBook, sheetIndex, resultDic[sid][col], dic[true], true);
                    }
                    else
                    {
                        InsertExcelValue(eBook, sheetIndex, resultDic[sid][col], value);
                    }
                }
            }
        }

        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            FillResultToSheet(eBook, 6, sectorIDs);
        }
    }

    /// <summary>
    /// 查询App平台推送的平层测试数据 一个站只执行一次
    /// </summary>
    class FddIndoorAcpCellLeveling : LteFddIndoorStationAccept
    {
        public bool HasData { get; set; } = false;
        protected Dictionary<int, string[]> resultDic = new Dictionary<int, string[]>();
        public FddIndoorAcpCellLeveling()
        {
            resultGrid = new string[1, 1, 14] {
                {
                    { "a2", "b2", "c2", "d2", "e2", "f2", "g2", "h2", "i2", "j2", "k2", "l2", "m2", "n2" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (lteCell == null)
            {
                return;
            }
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            FddDatabaseSetting setting;
            if (!DiyQueryFddDBSetting.GetInstance().DatabaseSetting.TryGetValue("单验平层", out setting))
            {
                return;
            }
            
            DiyQueryFddDBLeveling query = new DiyQueryFddDBLeveling(lteCell.ECI, setting);
            query.Query();
            Dictionary<string, FddIndoorDBLevelingResult> levelingResult = combineFloorAndType(query.DataList);
            if (levelingResult.Count > 0)
            {
                HasData = true;
            }

            InitResultGrid(levelingResult.Count);

            SaveResult(levelingResult);
        }

        private void InitResultGrid(int count)
        {
            for (int i = 1; i <= count; i++)
            {
                int index = i + 1;
                resultDic[i - 1] = new string[14] { "a" + index, "b" + index, "c" + index, "d" + index, "e" + index, "f" + index, "g" + index,
                "h" + index, "i" + index, "j" + index, "k" + index, "l" + index, "m" + index, "n" + index  };
            }
        }

        private Dictionary<string, FddIndoorDBLevelingResult> combineFloorAndType(List<FddIndoorDBLeveling> dataList)
        {
            //<楼宇楼层,平层数据>
            Dictionary<string, FddIndoorDBLevelingResult> levelingResult = new Dictionary<string, FddIndoorDBLevelingResult>();
            foreach (FddIndoorDBLeveling data in dataList)
            {
                FddIndoorDBLevelingResult levelingData;
                if (!levelingResult.TryGetValue(data.Name, out levelingData))
                {
                    levelingData = new FddIndoorDBLevelingResult();
                    levelingResult.Add(data.Name, levelingData);
                }

                levelingData.LevelingDataList.Add(data);
            }

            foreach (var item in levelingResult.Values)
            {
                item.CalculateData();
            }
            return levelingResult;
        }

        private void SaveResult(Dictionary<string, FddIndoorDBLevelingResult> levelingResult)
        {
            LteTestAcceptResult result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
            btsResultDic.Add(btsName, result);

            int sectorID = 0;
            int row = 0;
            foreach (var data in levelingResult)
            {
                int col = 0;
                result.SetValue(sectorID, row, col++, data.Key, true);
                result.SetValue(sectorID, row, col++, data.Value.PCI, true);
                result.SetValue(sectorID, row, col++, data.Value.AvgRsrp, true);
                result.SetValue(sectorID, row, col++, data.Value.AvgSinr, true);
                result.SetValue(sectorID, row, col++, data.Value.SinrMoreThan6Rate, true);
                result.SetValue(sectorID, row, col++, data.Value.SinrMoreThan9Rate, true);
                result.SetValue(sectorID, row, col++, data.Value.RsrpMoreThan105Rate, true);
                result.SetValue(sectorID, row, col++, data.Value.RsrpMoreThan95Rate, true);
                result.SetValue(sectorID, row, col++, data.Value.RsrpMoreThan85Rate, true);
                result.SetValue(sectorID, row, col++, data.Value.AvgULSpeed, true);
                result.SetValue(sectorID, row, col++, data.Value.AvgDLSpeed, true);
                result.SetValue(sectorID, row, col++, data.Value.MaxULSpeed, true);
                result.SetValue(sectorID, row, col, data.Value.MaxDLSpeed, true);
                sectorID++;
            }
        }

        protected override void FillResultToSheet(Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int sid = 0; sid < resultDic.Count; ++sid)
            {
                for (int col = 0; col < resultGrid.GetLength(2); ++col)
                {
                    object value = result.GetValue(sid, 0, col);
                    if (value is Dictionary<bool, double>)
                    {
                        Dictionary<bool, double> dic = value as Dictionary<bool, double>;
                        InsertExcelValue(eBook, sheetIndex, resultDic[sid][col], dic[true], true);
                    }
                    else
                    {
                        InsertExcelValue(eBook, sheetIndex, resultDic[sid][col], value);
                    }
                }
            }
        }

        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            FillResultToSheet(eBook, 3, sectorIDs);
        }
    }

    /// <summary>
    /// 查询App平台推送的覆盖图 一个站只执行一次
    /// </summary>
    class FddIndoorAcpCellCoverePic : LteFddIndoorStationAccept
    {
        public FddIndoorAcpCellCoverePic()
        {
            resultGrid = new string[1, 3, 8] {
                {
                    { "a13", "j13", "s13", "AB13", "a14", "j14", "s14", "AB14"},
                    { "a35", "j35", "s35", "AB35", "a36", "j36", "s36", "AB36"},
                    { "a57", "j57", "s57", "AB57", "a58", "j58", "s58", "AB58"},
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (lteCell == null)
            {
                return;
            }
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            FddDatabaseSetting setting;
            if (!DiyQueryFddDBSetting.GetInstance().DatabaseSetting.TryGetValue("单验覆盖图", out setting))
            {
                return;
            }

            //读取配置文件,获取本地图片根目录
            if (ZTStationAcceptance_XJ.Singleton<ZTStationAcceptance_XJ
                .FddIndoorStationAcceptConfigHelper>.Instance.LoadConfig())
            {
                return;
            }

            //查询数据库中对应的图片名
            DiyQueryFddDBCoverPic query = new DiyQueryFddDBCoverPic(lteCell.ECI, setting);
            query.Query();
            string orderID = query.GetOrderID();
            string serverCoverPicPath = ZTStationAcceptance_XJ.Singleton<ZTStationAcceptance_XJ
                .FddIndoorStationAcceptConfigHelper>.Instance.ConfigInfo.ServerCoverPicPath;
            string localCoverPicPath = ZTStationAcceptance_XJ.Singleton<ZTStationAcceptance_XJ
                .FddIndoorStationAcceptConfigHelper>.Instance.ConfigInfo.LocalCoverPicPath;

            serverCoverPicPath = serverCoverPicPath + System.IO.Path.DirectorySeparatorChar + orderID;
            log.Info("serverCoverPicPath" + serverCoverPicPath);
            localCoverPicPath = localCoverPicPath + System.IO.Path.DirectorySeparatorChar + orderID;
            log.Info("localCoverPicPath" + localCoverPicPath);
            if (!System.IO.Directory.Exists(localCoverPicPath))
            {
                System.IO.Directory.CreateDirectory(localCoverPicPath);
            }

            //下载图片到本地
            List<string> picPathList = initPicPath(query.DataList, serverCoverPicPath, localCoverPicPath);
            DownloadStationAcceptPic downloadQuery = new DownloadStationAcceptPic(picPathList, localCoverPicPath);
            downloadQuery.Query();

            SaveResult(query.DataList);
        }

        private List<string> initPicPath(List<FddIndoorDBCoverPic> coverPicResult, string serverDirectoryName, string localDirectoryName)
        {
            List<string> picPathList = new List<string>();
            int maxCount = 3;
            foreach (var coverPicData in coverPicResult)
            {
                if (maxCount >= 0)
                {
                    maxCount--;
                }
                else
                {
                    break;
                }
                addRealPicPath(serverDirectoryName, coverPicData.RsrpPicPath, picPathList);
                addRealPicPath(serverDirectoryName, coverPicData.SinrPicPath, picPathList);
                addRealPicPath(serverDirectoryName, coverPicData.FtpULPicPath, picPathList);
                addRealPicPath(serverDirectoryName, coverPicData.FtpDLPicPath, picPathList);

                coverPicData.RsrpPicPath = getRealPicPath(localDirectoryName, coverPicData.RsrpPicPath);
                coverPicData.SinrPicPath = getRealPicPath(localDirectoryName, coverPicData.SinrPicPath);
                coverPicData.FtpULPicPath = getRealPicPath(localDirectoryName, coverPicData.FtpULPicPath);
                coverPicData.FtpDLPicPath = getRealPicPath(localDirectoryName, coverPicData.FtpDLPicPath);
            }

            return picPathList;
        }

        private void addRealPicPath(string directoryName, string picPath, List<string> picPathList)
        {
            string realPath = "";
            if (!string.IsNullOrEmpty(picPath))
            {
                realPath = directoryName + System.IO.Path.DirectorySeparatorChar + picPath;
                picPathList.Add(realPath);
            }
        }

        private string getRealPicPath(string directoryName, string picPath)
        {
            string realPath = "";
            if (!string.IsNullOrEmpty(picPath))
            {
                realPath = directoryName + System.IO.Path.DirectorySeparatorChar + picPath;
            }
            return realPath;
        }

        private void SaveResult(List<FddIndoorDBCoverPic> coverPicResult)
        {
            LteTestAcceptResult result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
            btsResultDic.Add(btsName, result);

            int sectorID = 0;
            int row = 0;
            foreach (var data in coverPicResult)
            {
                int col = 0;
                result.SetValue(sectorID, row, col++, data.RsrpName, true);
                result.SetValue(sectorID, row, col++, data.SinrName, true);
                result.SetValue(sectorID, row, col++, data.FtpDLName, true);
                result.SetValue(sectorID, row, col++, data.FtpULName, true);
                result.SetValue(sectorID, row, col++, getPicFilePath(data.RsrpPicPath), true);
                result.SetValue(sectorID, row, col++, getPicFilePath(data.SinrPicPath), true);
                result.SetValue(sectorID, row, col++, getPicFilePath(data.FtpDLPicPath), true);
                result.SetValue(sectorID, row, col, getPicFilePath(data.FtpULPicPath), true);
                row++;
            }
        }

        private string getPicFilePath(string picPath)
        {
            if (string.IsNullOrEmpty(picPath) || !System.IO.File.Exists(picPath))
            {
                return "";
            }
            return picPath;
        }

        protected override void FillResultToSheet(Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int row = 0; row < resultGrid.GetLength(1); ++row)
            {
                for (int col = 0; col < resultGrid.GetLength(2); ++col)
                {
                    object value = result.GetValue(0, row, col);

                    if (col < 4 || value == null || value.ToString() == "")
                    {
                        InsertExcelValue(eBook, sheetIndex, resultGrid[0, row, col], value);
                    }
                    else
                    {
                        AcpAutoCoverPicture.InsertExcelFDDPicture(eBook, resultGrid[0, row, col], value.ToString(), 4, 15.0, 9.0);
                    }
                }
            }
        }

        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            FillResultToSheet(eBook, 4, sectorIDs);
        }
    }

    class FddIndoorAcpLeakOutScanPic : LteFddIndoorStationAccept
    {
        public FddIndoorAcpLeakOutScanPic()
        {
            Init();
            resultGrid = new string[1, 1, 2] {
                    {
                        { "a79","a80" }
                    }
                };
        }

        protected void Init()
        {
            reSetRsrpMapView();
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("扫频");
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            AnaFile(fileInfo, fileManager, pci, sectorID);
        }


        protected virtual void AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            AcpAutoCoverPicture picAnaFunc = AcpAutoCoverPicture.Instance;
            MTGis.DbRect bounds = picAnaFunc.GetCoverBounds(fileManager);
            string picPath = picAnaFunc.FireMapAndTakePicByFunc("Scan", "RSRP", bounds, btsName, btsName);
            result.SetValue(sectorID, 0, 0, "泄漏测试（扫频模式）RSRP轨迹图", true);
            result.SetValue(sectorID, 0, 1, picPath, true);
        }

        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            FillResultToSheet(eBook, 4, sectorIDs);
        }

        protected override void FillResultToSheet(Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int row = 0; row < resultGrid.GetLength(1); ++row)
            {
                for (int col = 0; col < resultGrid.GetLength(2); ++col)
                {
                    object value = result.GetValue(0, row, col);

                    if (col < 1 || value == null || value.ToString() == "")
                    {
                        InsertExcelValue(eBook, sheetIndex, resultGrid[0, row, col], value);
                    }
                    else
                    {
                        AcpAutoCoverPicture.InsertExcelFDDPicture(eBook, resultGrid[0, row, col], value.ToString(), 4, 15.0, 9.0);
                    }
                }
            }
        }

        #region 重置图例
        protected virtual void reSetRsrpMapView()
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("lte_RSRP");
            if (msi != null)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                ranges.Add(new RangeInfo(false, false, -145, -110, Color.Red));
                ranges.Add(new RangeInfo(true, false, -110, -90, Color.Orange));
                ranges.Add(new RangeInfo(true, false, -90, -80, Color.Yellow));
                ranges.Add(new RangeInfo(true, false, -80, -70, Color.Green));
                ranges.Add(new RangeInfo(true, false, -70, -40, Color.Blue));

                msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                foreach (RangeInfo range in ranges)
                {
                    DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                    paramColor.MaxIncluded = range.InculdeMax;
                    paramColor.MinIncluded = range.InculdeMin;
                    msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                }
            }
        }

        protected class RangeInfo
        {
            public RangeInfo(bool inculdeMin, bool inculdeMax, float min, float max, Color rangeColor)
            {
                Min = min;
                InculdeMin = inculdeMin;
                Max = max;
                InculdeMax = inculdeMax;
                RangeColor = rangeColor;
            }

            public float Min
            {
                get;
                private set;
            }
            public bool InculdeMin
            {
                get;
                private set;
            }
            public float Max
            {
                get;
                private set;
            }
            public bool InculdeMax
            {
                get;
                private set;
            }

            public Color RangeColor
            {
                get;
                private set;
            }
        }
        #endregion

        public override void Clear()
        {
            foreach (LteTestAcceptResult result in btsResultDic.Values)
            {
                string folderPath = AcpAutoCoverPicture.Instance.GetBtsPicFolder(result.BtsName);
                if (System.IO.Directory.Exists(folderPath))
                {
                    System.IO.Directory.Delete(folderPath, true);
                }
            }
            btsResultDic.Clear();
        }
    }

    /// <summary>
    /// 查询性能数据 一个站只执行一次
    /// </summary>
    class FddIndoorAcpPerformance : LteFddIndoorStationAccept
    {
        public FddIndoorAcpPerformance()
        {
            resultGrid = new string[1, 3, 13] {
                {
                    { "a4","b4","c4","d4","e4","f4","g4","h4","i4","j4","k4","l4","m4" },
                    { "a5","b5","c5","d5","e5","f5","g5","h5","i5","j5","k5","l5","m5" },
                    { "a6","b6","c6","d6","e6","f6","g6","h6","i6","j6","k6","l6","m6" }
                }
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (lteCell == null)
            {
                return;
            }
            LTEBTS lteBts = lteCell.BelongBTS;
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            string[] fileName = fileInfo.Name.Split('_');
            //log文件测试日期
            DateTime testDate = new DateTime();
            if (fileName.Length > 1 && !DateTime.TryParseExact(fileName[1], "yyyyMMdd",
                new System.Globalization.CultureInfo("zh-CN", true), System.Globalization.DateTimeStyles.None, out testDate))
            {
                return;
            }

            Dictionary<LTECell, FddFusionBtsCellDataResult> cellResDic = new Dictionary<LTECell, FddFusionBtsCellDataResult>();
            List<LTECell> fddCells = getFddCells(lteBts);
            foreach (var cell in fddCells)
            {
                //同一个小区3天的数据取平均
                FddFusionBtsCellDataResult cellData = new FddFusionBtsCellDataResult();
                Dictionary<int, List<FddFusionBtsCellData>> fusionBtsCellInfo = new Dictionary<int, List<FddFusionBtsCellData>>();
                //从数据库读取实测信息,查询前2天和今天的推送数据
                for (int i = -2; i < 1; i++)
                {
                    string testDateStr = testDate.AddDays(i).ToString("yyyyMMdd");
                    DiyQueryFddFusionData query = new DiyQueryFddFusionData();
                    query.SetCondition(lteBts.BTSID, lteCell.CellID, testDateStr);
                    query.Query();
                    fusionBtsCellInfo.Add(i + 1, query.FusionBtsCellInfo);
                    if (query.FusionBtsCellInfo.Count > 0)
                    {
                        cellData.Add(query.FusionBtsCellInfo[0]);
                    }
                }
                cellResDic.Add(cell, cellData);
            }

            SaveResult(btsName, testDate, cellResDic);
        }

        protected List<LTECell> getFddCells(LTEBTS lteBts)
        {
            //由于共站,判断小区后缀是哪种类型,只验证相同类型的小区
            string type;
            if (lteCell.Name.Contains("-FDD900-"))
            {
                type = "-FDD900-";
            }
            else if (lteCell.Name.Contains("-FDD1800-"))
            {
                type = "-FDD1800-";
            }
            else
            {
                type = "-FDD-";
            }

            List<LTECell> fddCells = new List<LTECell>();
            foreach (var cell in lteBts.Cells)
            {
                if (cell.Name.Contains(type))
                {
                    fddCells.Add(cell);
                }
            }

            return fddCells;
        }

        private void SaveResult(string btsName, DateTime testDate, Dictionary<LTECell, FddFusionBtsCellDataResult> cellResDic)
        {
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            int count = 0;
            foreach (var res in cellResDic)
            {
                LTECell cell = res.Key;
                FddFusionBtsCellDataResult cellData = res.Value;
                result.SetValue(0, count, 0, testDate.AddDays(-2).ToShortDateString() + "-" + testDate.ToShortDateString(), true);
                result.SetValue(0, count, 1, testDate.ToShortDateString(), true);
                result.SetValue(0, count, 2, cell.CellID, true);
                if (cellData.DataList.Count > 0)
                {
                    result.SetValue(0, count, 3, cellData.AvgRrcConnectCount, true);
                    result.SetValue(0, count, 4, cellData.AvgRrcSuccessRate, true);
                    result.SetValue(0, count, 5, cellData.AvgERABConnectCount, true);
                    result.SetValue(0, count, 6, cellData.AvgERABSuccessRate, true);
                    result.SetValue(0, count, 7, cellData.AvgWirelessRate, true);
                    result.SetValue(0, count, 8, cellData.AvgWirelessDropRate, true);
                    result.SetValue(0, count, 9, cellData.AvgERABDropRate, true);
                    result.SetValue(0, count, 10, cellData.AvgHandOverSuccessRate, true);
                    result.SetValue(0, count, 11, cellData.AvgULPDCPThroughput, true);
                    result.SetValue(0, count, 12, cellData.AvgDLPDCPThroughput, true);
                }
                count++;
            }
        }

        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            FillResultToSheet(eBook, 5, sectorIDs);
        }

        protected override void FillResultToSheet(Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int row = 0; row < resultGrid.GetLength(1); ++row)
            {
                for (int col = 0; col < resultGrid.GetLength(2); ++col)
                {
                    object value = result.GetValue(0, row, col);
                    InsertExcelValue(eBook, sheetIndex, resultGrid[0, row, col], value);
                }
            }
        }
    }
    
    /// <summary>
    /// 查询告警数据 一个站只执行一次
    /// </summary>
    class FddIndoorAcpAlarm : FddIndoorAcpPerformance
    {
        protected Dictionary<int, string[]> resultDic = new Dictionary<int, string[]>();
        public FddIndoorAcpAlarm()
        {
            resultGrid = new string[1, 1, 3] {
                {
                    { "a39", "c39", "e39" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (lteCell == null)
            {
                return;
            }
            LTEBTS lteBts = lteCell.BelongBTS;

            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            FddDatabaseSetting setting;
            if (!DiyQueryFddDBSetting.GetInstance().DatabaseSetting.TryGetValue("单验告警", out setting))
            {
                return;
            }

            //查询当月告警信息
            string[] fileName = fileInfo.Name.Split('_');
            //log文件测试日期
            DateTime testMonth = new DateTime();
            if (fileName.Length > 1 && !DateTime.TryParseExact(fileName[1], "yyyyMMdd",
                new System.Globalization.CultureInfo("zh-CN", true), System.Globalization.DateTimeStyles.None, out testMonth))
            {
                return;
            }

            List<LTECell> fddCells = getFddCells(lteBts);

            Dictionary<LTECell, FddAlarmInfo> btsCellDataDic = new Dictionary<LTECell, FddAlarmInfo>();
            string testMonthStr = testMonth.ToString("yyMM");
            foreach (var cell in fddCells)
            {
                //从数据库读取告警信息
                DiyQueryFddDBAlarm query = new DiyQueryFddDBAlarm(cell, testMonthStr, setting);
                query.Query();
                if (query.DataList.Count > 0)
                {
                    btsCellDataDic.Add(cell, query.DataList[0]);
                }
            }

            InitResultGrid(btsCellDataDic.Count);

            SaveResult(btsName, btsCellDataDic);
        }

        private void SaveResult(string btsName, Dictionary<LTECell, FddAlarmInfo> btsCellDataDic)
        {
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            int count = 0;
            foreach (var cellData in btsCellDataDic.Values)
            {
                result.SetValue(count, 0, 0, cellData.Date, true);
                result.SetValue(count, 0, 1, cellData.CellName, true);
                result.SetValue(count, 0, 2, cellData.AlarmType, true);
                count++;
            }
        }

        private void InitResultGrid(int count)
        {
            for (int i = 0; i < count; i++)
            {
                int index = i + 39;
                resultDic[i] = new string[3] { "a" + index, "c" + index, "e" + index };
            }
        }

        protected override void FillResultToSheet(Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int sid = 0; sid < resultDic.Count; ++sid)
            {
                for (int col = 0; col < resultGrid.GetLength(2); ++col)
                {
                    object value = result.GetValue(sid, 0, col);
                    InsertExcelValue(eBook, sheetIndex, resultDic[sid][col], value);
                }
            }
        }
    }
}
