﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ChkMgrsLibTestSetForm : BaseDialog
    {
        public ChkMgrsLibTestSetForm()
        {
            InitializeComponent();
            SetText();

            this.btnOK.Click += BtnOK_Click;
            this.btnCancel.Click += BtnCancel_Click;
        }

        public double GetCondition()
        {
            return (double)numDistance.Value;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void SetText()
        {
            txtDesc.Text = "取文件中的每个采样点经纬度求得其军事栅格编号，再通过栅格编号反推栅格的左上右下经纬度。"
                + Environment.NewLine + Environment.NewLine
                + "计算采样点经纬度到栅格的左上右下经纬度的距离，小于设置的合法距离则认为该点通过测试。"
                + Environment.NewLine + Environment.NewLine
                + "50米栅格最大合法距离为70.7107米；100米栅格最大合法距离为141.4214。";
        }
    }
}
