﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class RsrpEarfcnForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnRegion = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRsrpRange = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTddEarfcnD = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTddEarfcnE = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTddEarfcnF = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTddPointCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTddEarfcn900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTddEarfcn1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFddPointCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPointCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTddPercentD = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTddPercentE = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTddPercentF = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTddPercent900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTddPercent1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnRegion);
            this.objectListView.AllColumns.Add(this.olvColumnRsrpRange);
            this.objectListView.AllColumns.Add(this.olvColumnTddEarfcnD);
            this.objectListView.AllColumns.Add(this.olvColumnTddEarfcnE);
            this.objectListView.AllColumns.Add(this.olvColumnTddEarfcnF);
            this.objectListView.AllColumns.Add(this.olvColumnTddPointCount);
            this.objectListView.AllColumns.Add(this.olvColumnTddEarfcn900);
            this.objectListView.AllColumns.Add(this.olvColumnTddEarfcn1800);
            this.objectListView.AllColumns.Add(this.olvColumnFddPointCount);
            this.objectListView.AllColumns.Add(this.olvColumnPointCount);
            this.objectListView.AllColumns.Add(this.olvColumnTddPercentD);
            this.objectListView.AllColumns.Add(this.olvColumnTddPercentE);
            this.objectListView.AllColumns.Add(this.olvColumnTddPercentF);
            this.objectListView.AllColumns.Add(this.olvColumnTddPercent900);
            this.objectListView.AllColumns.Add(this.olvColumnTddPercent1800);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnRegion,
            this.olvColumnRsrpRange,
            this.olvColumnTddEarfcnD,
            this.olvColumnTddEarfcnE,
            this.olvColumnTddEarfcnF,
            this.olvColumnTddPointCount,
            this.olvColumnTddEarfcn900,
            this.olvColumnTddEarfcn1800,
            this.olvColumnFddPointCount,
            this.olvColumnPointCount,
            this.olvColumnTddPercentD,
            this.olvColumnTddPercentE,
            this.olvColumnTddPercentF,
            this.olvColumnTddPercent900,
            this.olvColumnTddPercent1800});
            this.objectListView.ContextMenuStrip = this.contextMenuStrip;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.HeaderWordWrap = true;
            this.objectListView.Location = new System.Drawing.Point(0, 0);
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(1517, 640);
            this.objectListView.TabIndex = 3;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnRegion
            // 
            this.olvColumnRegion.HeaderFont = null;
            this.olvColumnRegion.Text = "网格";
            // 
            // olvColumnRsrpRange
            // 
            this.olvColumnRsrpRange.HeaderFont = null;
            this.olvColumnRsrpRange.HeaderForeColor = System.Drawing.Color.Black;
            this.olvColumnRsrpRange.Text = "RSRP区间";
            this.olvColumnRsrpRange.Width = 120;
            // 
            // olvColumnTddEarfcnD
            // 
            this.olvColumnTddEarfcnD.HeaderFont = null;
            this.olvColumnTddEarfcnD.Text = "D频采样点";
            this.olvColumnTddEarfcnD.Width = 120;
            // 
            // olvColumnTddEarfcnE
            // 
            this.olvColumnTddEarfcnE.HeaderFont = null;
            this.olvColumnTddEarfcnE.Text = "E频采样点";
            this.olvColumnTddEarfcnE.Width = 120;
            // 
            // olvColumnTddEarfcnF
            // 
            this.olvColumnTddEarfcnF.HeaderFont = null;
            this.olvColumnTddEarfcnF.Text = "F频采样点";
            this.olvColumnTddEarfcnF.Width = 120;
            // 
            // olvColumnTddPointCount
            // 
            this.olvColumnTddPointCount.HeaderFont = null;
            this.olvColumnTddPointCount.Text = "TDD采样点总数";
            this.olvColumnTddPointCount.Width = 120;
            // 
            // olvColumnTddEarfcn900
            // 
            this.olvColumnTddEarfcn900.HeaderFont = null;
            this.olvColumnTddEarfcn900.Text = "FDD-900采样点";
            this.olvColumnTddEarfcn900.Width = 130;
            // 
            // olvColumnTddEarfcn1800
            // 
            this.olvColumnTddEarfcn1800.HeaderFont = null;
            this.olvColumnTddEarfcn1800.Text = "FDD-1800采样点";
            this.olvColumnTddEarfcn1800.Width = 130;
            // 
            // olvColumnFddPointCount
            // 
            this.olvColumnFddPointCount.HeaderFont = null;
            this.olvColumnFddPointCount.Text = "FDD采样点总数";
            this.olvColumnFddPointCount.Width = 120;
            // 
            // olvColumnPointCount
            // 
            this.olvColumnPointCount.HeaderFont = null;
            this.olvColumnPointCount.Text = "采样点总数";
            this.olvColumnPointCount.Width = 120;
            // 
            // olvColumnTddPercentD
            // 
            this.olvColumnTddPercentD.HeaderFont = null;
            this.olvColumnTddPercentD.Text = "D频采样点占比";
            this.olvColumnTddPercentD.Width = 120;
            // 
            // olvColumnTddPercentE
            // 
            this.olvColumnTddPercentE.HeaderFont = null;
            this.olvColumnTddPercentE.Text = "E频采样点占比";
            this.olvColumnTddPercentE.Width = 120;
            // 
            // olvColumnTddPercentF
            // 
            this.olvColumnTddPercentF.HeaderFont = null;
            this.olvColumnTddPercentF.Text = "F频采样点占比";
            this.olvColumnTddPercentF.Width = 120;
            // 
            // olvColumnTddPercent900
            // 
            this.olvColumnTddPercent900.HeaderFont = null;
            this.olvColumnTddPercent900.Text = "FDD-900占比";
            this.olvColumnTddPercent900.Width = 130;
            // 
            // olvColumnTddPercent1800
            // 
            this.olvColumnTddPercent1800.HeaderFont = null;
            this.olvColumnTddPercent1800.Text = "FDD-1800占比";
            this.olvColumnTddPercent1800.Width = 130;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // ToolStripMenuItemExportExcel
            // 
            this.ToolStripMenuItemExportExcel.Enabled = false;
            this.ToolStripMenuItemExportExcel.Name = "ToolStripMenuItemExportExcel";
            this.ToolStripMenuItemExportExcel.Size = new System.Drawing.Size(129, 22);
            this.ToolStripMenuItemExportExcel.Text = "导出Excel";
            this.ToolStripMenuItemExportExcel.Click += new System.EventHandler(this.ToolStripMenuItemExportExcel_Click);
            // 
            // RsrpEarfcnForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1517, 640);
            this.Controls.Add(this.objectListView);
            this.Name = "RsrpEarfcnForm";
            this.Text = "各频段覆盖占比信息";
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnRegion;
        private BrightIdeasSoftware.OLVColumn olvColumnRsrpRange;
        private BrightIdeasSoftware.OLVColumn olvColumnTddEarfcnD;
        private BrightIdeasSoftware.OLVColumn olvColumnTddEarfcnE;
        private BrightIdeasSoftware.OLVColumn olvColumnTddEarfcnF;
        private BrightIdeasSoftware.OLVColumn olvColumnTddEarfcn900;
        private BrightIdeasSoftware.OLVColumn olvColumnTddEarfcn1800;
        private BrightIdeasSoftware.OLVColumn olvColumnTddPercentD;
        private BrightIdeasSoftware.OLVColumn olvColumnTddPercentE;
        private BrightIdeasSoftware.OLVColumn olvColumnTddPercentF;
        private BrightIdeasSoftware.OLVColumn olvColumnTddPointCount;
        private BrightIdeasSoftware.OLVColumn olvColumnTddPercent900;
        private BrightIdeasSoftware.OLVColumn olvColumnTddPercent1800;
        private BrightIdeasSoftware.OLVColumn olvColumnFddPointCount;
        private BrightIdeasSoftware.OLVColumn olvColumnPointCount;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExportExcel;
    }
}