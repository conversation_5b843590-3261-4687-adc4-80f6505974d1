<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">34_电信_FDD LTE语音业务统计报表</Item>
          <Item typeName="Int32" key="KeyCount">1</Item>
          <Item typeName="Int32" key="TimeShowType">0</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">文件名</Item>
              <Item typeName="String" key="Exp">{kFileName}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3000]+value9[3000]+evtIdCount[3335]+value9[3335]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[3007]+value9[3007]+evtIdCount[3347]+value9[3347]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[3000]+value9[3000]+evtIdCount[3335]+value9[3335])-(evtIdCount[3007]+value9[3007]+evtIdCount[3347]+value9[3347]))/(evtIdCount[3000]+value9[3000]+evtIdCount[3335]+value9[3335]) }%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3343]+value9[3343]+evtIdCount[3344]+value9[3344]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3343]+value9[3343]+evtIdCount[3344]+value9[3344])/((evtIdCount[3000]+value9[3000]+evtIdCount[3335]+value9[3335]+evtIdCount[3001]+value9[3001]+evtIdCount[3336]+value9[3336])-(evtIdCount[3007]+value9[3007]+evtIdCount[3347]+value9[3347]+evtIdCount[3008]+value9[3008]+evtIdCount[3348]+value9[3348]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">全称呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3343]+value9[3343]+evtIdCount[3344]+value9[3344])/((evtIdCount[3000]+value9[3000]+evtIdCount[3335]+value9[3335]+evtIdCount[3001]+value9[3001]+evtIdCount[3336]+value9[3336])-(evtIdCount[3007]+value9[3007]+evtIdCount[3347]+value9[3347]+evtIdCount[3008]+value9[3008]+evtIdCount[3348]+value9[3348]))))*(((evtIdCount[3000]+value9[3000]+evtIdCount[3335]+value9[3335])-(evtIdCount[3007]+value9[3007]+evtIdCount[3347]+value9[3347]))/(evtIdCount[3000]+value9[3000]+evtIdCount[3335]+value9[3335]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{(value1[3011]+value1[3352])/(1000.0*(evtIdCount[3011]+evtIdCount[3352]))}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">呼叫时延&gt;9s占比</Item>
              <Item typeName="String" key="Exp">{100*(value5[3011]+value5[3352])/(evtIdCount[3011]+evtIdCount[3352])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">MO CSFB返回FDD比例</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3083]+value9[3083])/(evtIdCount[3081]+value9[3081])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">MT CSFB返回FDD比例</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3084]+value9[3084])/(evtIdCount[3082]+value9[3082])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">MO CSFB返回FDD时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3083]/(1000.0*evtIdCount[3083])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">MT CSFB返回FDD时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3084]/(1000.0*evtIdCount[3084])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫CSFB回落到23G成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3075]+value9[3075])/(evtIdCount[3069]+value9[3069])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">被叫CSFB回落到23G成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3076]+value9[3076])/(evtIdCount[3070]+value9[3070])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫CSFB回落到23G平均时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3075]/(1000.0*evtIdCount[3075])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">被叫CSFB回落到23G平均时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3076]/(1000.0*evtIdCount[3076])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">MOS均值</Item>
              <Item typeName="String" key="Exp">{((Lf_6D2D09*Lf_6D2D08)+(Lf_612D6209*Lf_612D6208))/(Lf_6D2D08+Lf_612D6208) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">MOS&gt;2.8占比</Item>
              <Item typeName="String" key="Exp">{100*(Lf_6D2D04+Lf_6D2D05+Lf_6D2D06+Lf_6D2D07+Lf_6D2D0C+Lf_612D6204+Lf_612D6205+Lf_612D6206+Lf_612D6207+Lf_612D620C)/(Lf_6D2D08+Lf_612D6208) }%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3000]+value9[3000]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[3007]+value9[3007])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB接通率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3000]+value9[3000]-(evtIdCount[3007]+value9[3007]))/(evtIdCount[3000]+value9[3000])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016])/(evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB全程呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016])/(evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]))))*((evtIdCount[3000]+value9[3000]-(evtIdCount[3007]+value9[3007]))/(evtIdCount[3000]+value9[3000]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3011]/(1000.0*evtIdCount[3011])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CDMA试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3335]+value9[3335]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CDMA未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[3347]+value9[3347]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CDMA接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[3335]+value9[3335])-(evtIdCount[3347]+value9[3347]))/(evtIdCount[3335]+value9[3335]) }%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CDMA掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3343]+value9[3343]+evtIdCount[3344]+value9[3344]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CDMA掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3343]+value9[3343]+evtIdCount[3344]+value9[3344])/((evtIdCount[3335]+value9[3335]+evtIdCount[3336]+value9[3336])-(evtIdCount[3347]+value9[3347]+evtIdCount[3348]+value9[3348]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CDMA全程呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[3343]+value9[3343]+evtIdCount[3344]+value9[3344])/((evtIdCount[3335]+value9[3335]+evtIdCount[3336]+value9[3336])-(evtIdCount[3347]+value9[3347]+evtIdCount[3348]+value9[3348]))))*(((evtIdCount[3335]+value9[3335])-(evtIdCount[3347]+value9[3347]))/(evtIdCount[3335]+value9[3335]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CDMA呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3352]/(1000.0*evtIdCount[3352])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3075]+value9[3075]+evtIdCount[3076]+value9[3076])/(evtIdCount[3069]+value9[3069]+evtIdCount[3070]+value9[3070])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>