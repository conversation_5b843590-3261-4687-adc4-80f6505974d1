﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class CellLengthSettingDlg : BaseFormStyle
    {
        public CellLengthSettingDlg()
        {
            InitializeComponent();
#if LT
            labelTD.Visible = false;
            labelW.Visible = true;
            spinEditOtherCell.Value = (decimal)CD.CellLengthRadio_W;
#else
            spinEditOtherCell.Value = (decimal)CD.CellLengthRadio_TD;
#endif
            spinEditGSMCell.Value = (decimal)CD.CellLengthRadio;
        }

        private void spinEditGSMCell_EditValueChanged(object sender, EventArgs e)
        {
            if (checkEditAll.Checked)
            {
                spinEditOtherCell.Value = spinEditGSMCell.Value;
            }
        }

        private void spinEditOtherCell_EditValueChanged(object sender, EventArgs e)
        {
            if (checkEditAll.Checked)
            {
                spinEditGSMCell.Value = spinEditOtherCell.Value;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
#if LT
            CD.CellLengthRadio_W=(float)spinEditOtherCell.Value;
#else
            CD.CellLengthRadio_TD = (float)spinEditOtherCell.Value;
#endif
            CD.CellLengthRadio = (float)spinEditGSMCell.Value;
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
