﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTAntPropagationModel
    {
        /// <summary>
        /// 获取每个栅格的场强信息
        /// </summary>
        public void calcGridRsrp(Dictionary<LTECell, ZTLteAntSimulator.CellModelInfo> cellModelArray, ref Dictionary<GridLongLat, GridRsrpInfo> gridRsrpDic)
        {
            foreach (LTECell lteCell in cellModelArray.Keys)
            {
                try
                {
                    Dictionary<GridLongLat, double> cellGridRsrpDic = getMacroCellPropagationModel(lteCell, 0, cellModelArray[lteCell]);
                    foreach (GridLongLat gll in cellGridRsrpDic.Keys)
                    {
                        GridRsrpInfo gridRsrp;
                        if (!gridRsrpDic.TryGetValue(gll, out gridRsrp))
                        {
                            gridRsrp = new GridRsrpInfo();
                        }

                        if (!gridRsrp.cellRsrpDic.ContainsKey(lteCell))
                            gridRsrp.cellRsrpDic.Add(lteCell, cellGridRsrpDic[gll]);

                        gridRsrpDic[gll] = gridRsrp;
                    }
                }
                catch
                {
                    //continue
                }
            }
        }

        /// <summary>
        /// 计算小区传播模型
        /// </summary>
        /// <param name="iAreaType">地域类型,0为城市,1为农村</param>
        /// <param name="powerArray">天线权值</param>
        private Dictionary<GridLongLat, double> getMacroCellPropagationModel(LTECell lteCell, int iAreaType, ZTLteAntSimulator.CellModelInfo modelInfo)
        {
            int iRsrp = -100;//LTE评估-100dBm的覆盖范围
            AntPropModel propModel = new AntPropModel();
            LongLat cellLongLat = new LongLat();
            cellLongLat.fLongitude = (float)lteCell.Longitude;
            cellLongLat.fLatitude = (float)lteCell.Latitude;
            int iHeight = lteCell.Antennas[0].Altitude == 0 ? 20 : lteCell.Antennas[0].Altitude;
            int idir = lteCell.Antennas[0].Direction;
            int iFreq = 2000;// lteCell.EARFCN 需要中心频点转频率
            if (LTECell.GetBandTypeByEarfcn(lteCell.EARFCN) == LTEBandType.D)
                iFreq = 2600;
            else if (LTECell.GetBandTypeByEarfcn(lteCell.EARFCN) == LTEBandType.F)
                iFreq = 1900;

            for (int i = 0; i < 180; i++)
            {
                int tDir = (i - 90 + idir + 360) % 360;
                double dTargetDbi = 0;
                if (modelInfo.powerArray == null)
                    dTargetDbi = calcCommonAntDbi(iFreq, i);
                else
                    dTargetDbi = 15 + modelInfo.powerArray[i];//15dB为参考发射功率

                double dDistance = 0;//计算小区方向最远覆盖距离
                if (iAreaType == 1)//农村
                    dDistance = 1000 * Math.Pow(10, (0 - iRsrp + dTargetDbi - 69.55 - 26.16 * Math.Log10(iFreq) + 13.82 * Math.Log10(iHeight) + 4.78 * Math.Pow(Math.Log10(iFreq), 2) - 18.33 * Math.Log10(iFreq) + 40.94) / (44.9 - 6.55 * Math.Log10(iHeight)));
                else//城市
                    dDistance = 1000 * Math.Pow(10, (0 - iRsrp + dTargetDbi + 18 * Math.Log10(iHeight) - 21 * Math.Log10(iFreq) - 80) / (40 * (1 - 0.004 * iHeight)));//转换为米单位

                LongLat curLongLat = ZTAntFuncHelper.calcPointX(tDir, (float)dDistance, cellLongLat);

                int oDir = (i - 90 + 360) % 360;
                propModel.cellLongLatDic.Add(oDir, curLongLat);
                propModel.cellDistDic.Add(oDir, dDistance);
            }
            propModel.fillMaxRange();
            propModel.fillMaxCoverDist();
            Dictionary<GridLongLat, double> gridRsrpDic = propModel.getCoverGrid(lteCell, iAreaType, modelInfo);
            return gridRsrpDic;
        }

        /// <summary>
        /// 结合传播模型计算覆盖场强
        /// </summary>
        /// <param name="iAreaType">地域类型,0为城市,1为农村</param>
        /// <param name="powerArray">天线权值</param>
        public static double calcRSRPByPropagationModel(LTECell lteCell, int iAreaType, float fDistance, ZTLteAntSimulator.CellModelInfo modelInfo, int iAngle)
        {
            int iHeight = lteCell.Antennas[0].Altitude == 0 ? 20 : lteCell.Antennas[0].Altitude;
            int iFreq = 2000;// lteCell.EARFCN 需要中心频点转频率
            if (LTECell.GetBandTypeByEarfcn(lteCell.EARFCN) == LTEBandType.D)
                iFreq = 2600;
            else if (LTECell.GetBandTypeByEarfcn(lteCell.EARFCN) == LTEBandType.F)
                iFreq = 1900;

            double dTargetDbi = 0;
            if (modelInfo.powerArray == null)
                dTargetDbi = calcCommonAntDbi(iFreq, iAngle);
            else
                dTargetDbi = 15 + modelInfo.powerArray[iAngle];//15dB为参考发射功率

            double dVertPathLoss = calcVertPathLoss(LTECell.GetBandTypeByEarfcn(lteCell.EARFCN), ZTAntFuncHelper.calcVertAngle(lteCell, fDistance, modelInfo.dDownward));//垂直面衰减衰减值

            fDistance = fDistance / 1000;
            double dPathLoss = 0;
            if (iAreaType == 1)//农村
                dPathLoss = 69.55 + 26.16 * Math.Log10(iFreq) - 13.82 * Math.Log10(iHeight) + (44.9 - 6.55 * Math.Log10(iHeight)) * Math.Log10(fDistance) - 4.78 * Math.Pow(Math.Log10(iFreq), 2) + 18.33 * Math.Log10(iFreq) - 40.94;
            else
                dPathLoss = 40 * (1 - 0.004 * iHeight) * Math.Log10(fDistance) - 18 * Math.Log10(iHeight) + 21 * Math.Log10(iFreq) + 80;

            return dTargetDbi - dPathLoss - dVertPathLoss;
        }

        /// <summary>
        /// 计算垂直面的路损
        /// </summary>
        private static double calcVertPathLoss(LTEBandType bandType,double dVertAngle)
        {
            double dVertPathLoss = 0;
            if (bandType == LTEBandType.D)
            {
                if (dVertAngle <= 2.5d)//3dB功率角
                    dVertPathLoss = dVertAngle * 3d / 2.5d;
                else if(dVertAngle <= 7d)
                    dVertPathLoss = 3 + (dVertAngle - 2.5d) * 17d / 4.5;
                else if (dVertAngle <= 90d)
                    dVertPathLoss = 20 + (dVertAngle - 7d) * 15d / 83;
                else
                    dVertPathLoss = 35;
            }
            else if (bandType == LTEBandType.F)
            {
                if (dVertAngle <= 3.5d)//3dB功率角
                    dVertPathLoss = dVertAngle * 3d / 3.5d;
                else if (dVertAngle <= 10d)
                    dVertPathLoss = 3 + (dVertAngle - 3.5d) * 17d / 6.5d;
                else if (dVertAngle <= 90d)
                    dVertPathLoss = 20 + (dVertAngle - 10d) * 15d / 80d;
                else
                    dVertPathLoss = 35;
            }
            return dVertPathLoss;
        }

        /// <summary>
        /// 计算普通天线增益
        /// </summary>
        private static double calcCommonAntDbi(int iFreq,int iAngle)
        {
            double dTargetDbi = 0;
            double dOrgDbi = 16;
            if (iFreq >= 1710 && iFreq < 1885)
                dOrgDbi = 16.5;
            else if (iFreq >= 1885 && iFreq < 2010)
                dOrgDbi = 17;
            else if (iFreq >= 2010 && iFreq < 2575)
                dOrgDbi = 17;
            else if (iFreq >= 2575 && iFreq <= 2635)
                dOrgDbi = 17.5;

            iAngle = iAngle -90;
            double dBi = 12 * Math.Pow(iAngle / 65.0, 2) > 20 ? 20 : 12 * Math.Pow(iAngle / 65.0, 2);//普通天线增益，65为3dB功率角
            dTargetDbi = 15 + dOrgDbi - dBi;//15dB为参考发射功率

            return dTargetDbi;
        }

        public class AntPropModel
        {
            public Dictionary<int, LongLat> cellLongLatDic { get; set; } = new Dictionary<int, LongLat>();
            public Dictionary<int, double> cellDistDic { get; set; } = new Dictionary<int, double>();

            private GridLongLat maxRange;
            public GridLongLat MaxRange
            {
                get{return maxRange;}
            }
            public void fillMaxRange()
            {
                maxRange = null;
                foreach (int idir in cellLongLatDic.Keys)
                {
                    LongLat curll = cellLongLatDic[idir];
                    if (maxRange == null)
                        maxRange = GridLongLat.cvtLongLat(curll);
                    else
                        maxRange.updateValue(curll);
                }
            }

            private double maxCoverDist;
            public double MaxCoverDist
            {
                get { return maxCoverDist; }
            }
            public void fillMaxCoverDist()
            {
                maxCoverDist = 0;
                foreach (int idir in cellDistDic.Keys)
                {
                    maxCoverDist = cellDistDic[idir] > maxCoverDist ? cellDistDic[idir] : maxCoverDist;
                }
            }

            public Dictionary<GridLongLat, double> getCoverGrid(LTECell lteCell, int iAreaType, ZTLteAntSimulator.CellModelInfo modelInfo)
            {
                Dictionary<GridLongLat, double> gridRsrpDic = new Dictionary<GridLongLat, double>();
                double angleDiffRel = 0;
                double angleDiffAbs =0; 
                double distance =0;
                int iNum = 0;
                for (float i = maxRange.fltlongitude - 0.004f; i <= maxRange.fbrlongitude + 0.004f; i += 0.0004f)
                {
                    for (float j = maxRange.fbrlatitude - 0.0036f; j <= maxRange.fltlatitude + 0.0036f; j += 0.00036f)
                    {
                        iNum++;
                        try
                        {
                            ZTAntFuncHelper.CellCollection col = new ZTAntFuncHelper.CellCollection()
                            {
                                Cell = null,
                                TDCell = null,
                                LteCell = lteCell
                            };
                            ZTAntFuncHelper.calcSampleAngle(col, i, j, out angleDiffRel, out angleDiffAbs, out distance);

                            if (cellDistDic.ContainsKey((int)angleDiffRel) && cellDistDic[(int)angleDiffRel] > distance)
                            {
                                GridLongLat grid = GridLongLat.getGridByCenterLongLat(new LongLat(i, j));

                                double dRsrp = calcRSRPByPropagationModel(lteCell, iAreaType, (float)distance, modelInfo, ((int)angleDiffRel + 90) % 360);
                                if (dRsrp >= -140 && dRsrp <= -40 && !gridRsrpDic.ContainsKey(grid))
                                {
                                    gridRsrpDic.Add(grid, dRsrp);
                                }
                            }
                        }
                        catch
                        {
                            //continue
                        }
                    }
                }
                return gridRsrpDic;
            }
        }

        public class GridRsrpInfo
        {
            public Dictionary<LTECell, double> cellRsrpDic { get; set; } = new Dictionary<LTECell, double>();

            /// <summary>
            /// 获取最大电平
            /// </summary>
            public double getMaxRsrp()
            {
                double dRsrp = -140;
                foreach (LTECell lteCell in cellRsrpDic.Keys)
                {
                    if (cellRsrpDic[lteCell] > dRsrp)
                        dRsrp = cellRsrpDic[lteCell];
                }
                return dRsrp;
            }

            /// <summary>
            /// 获取平均电平
            /// </summary>
            public double getAvgRsrp()
            {
                double dSumRsrp = 0;
                foreach (LTECell lteCell in cellRsrpDic.Keys)
                {
                    dSumRsrp += cellRsrpDic[lteCell];
                }
                return dSumRsrp / cellRsrpDic.Count;
            }

            /// <summary>
            /// 计算栅格的SINR（仅模三小区）
            /// </summary>
            public double getMod3Sinr()
            {
                if (cellRsrpDic.Count == 1)
                    return 25;//sinr最大值
                double dSinr = 0;
                bool isMaxRsrp = true;

                double dMainPower = 0;
                double dOtherSumPower = 0;
                int iPCI = 0;
                List<KeyValuePair<LTECell, double>> cellList = new List<KeyValuePair<LTECell, double>>(cellRsrpDic);
                Dictionary<LTECell, double> newDic = new Dictionary<LTECell, double>();
                cellList.Sort(delegate(KeyValuePair<LTECell, double> s1, KeyValuePair<LTECell, double> s2)
                {
                    return s2.Value.CompareTo(s1.Value);
                });
                foreach (KeyValuePair<LTECell, double> dic in cellList)
                {
                    newDic.Add(dic.Key, dic.Value);
                }

                foreach (LTECell lteCell in newDic.Keys)
                {
                    double rsrp = cellRsrpDic[lteCell];
                    if (isMaxRsrp)
                    {
                        iPCI = lteCell.PCI;
                        dMainPower = Math.Pow(10, rsrp / 10);
                        isMaxRsrp = false;
                    }
                    else if (lteCell.PCI % 3 == iPCI % 3)
                    {
                        dOtherSumPower += Math.Pow(10, rsrp / 10);
                    }
                }
                dSinr = Math.Log10(dMainPower / dOtherSumPower) * 10;
                if (dSinr > 25)
                    return 25;  //sinr最大值
                if (dSinr < -50)
                    return -50; //sinr最小值
                return dSinr;
            }

            /// <summary>
            /// 计算栅格的SINR
            /// </summary>
            public double getSinr()
            {
                if (cellRsrpDic.Count == 1)
                    return 25;//sinr最大值
                double dSinr = 0;
                bool isMaxRsrp = true;

                double dMainPower = 0;
                double dOtherSumPower = 0;
                List<KeyValuePair<LTECell, double>> cellList = new List<KeyValuePair<LTECell, double>>(cellRsrpDic);
                Dictionary<LTECell, double> newDic = new Dictionary<LTECell, double>();
                cellList.Sort(delegate(KeyValuePair<LTECell, double> s1, KeyValuePair<LTECell, double> s2)
                {
                    return s2.Value.CompareTo(s1.Value);
                });
                foreach (KeyValuePair<LTECell, double> dic in cellList)
                {
                    newDic.Add(dic.Key, dic.Value);
                }
                foreach (LTECell lteCell in newDic.Keys)
                {
                    double rsrp = cellRsrpDic[lteCell];
                    if (isMaxRsrp)
                    {
                        dMainPower = Math.Pow(10, rsrp / 10);
                        isMaxRsrp = false;
                    }
                    else
                        dOtherSumPower += Math.Pow(10, rsrp / 10);
                }
                dSinr = Math.Log10(dMainPower / dOtherSumPower) * 10;
                if (dSinr > 25)
                    return 25;  //sinr最大值
                if (dSinr < -50)
                    return -50; //sinr最小值
                return dSinr;
            }

            public int getOverlapCoverage()
            {
                int iCov = 0;
                List<double> RSRPList = new List<double>();
                RSRPList.AddRange(cellRsrpDic.Values);
                RSRPList.Sort((x, y) => { return y.CompareTo(x); }); 
                if (RSRPList.Count > 0)
                {
                    for (int idx = 0; idx < RSRPList.Count; idx++)
                    {
                        if (RSRPList[0] - RSRPList[idx] <= 6)
                            iCov++;
                        else
                            break;
                    }
                }
                if (iCov >= 10)
                    return 10;
                return iCov;
            }
        }
    }
}
