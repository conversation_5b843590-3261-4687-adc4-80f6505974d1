﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class EventMessageColorSettingDlg : Form
    {
        public EventMessageColorSettingDlg(string text, Color curForeColor, Color curBackColor, bool fontBold)
        {
            InitializeComponent();
            this.pnForeColor.BackColor = curForeColor;
            this.pnBackColor.BackColor = curBackColor;
            this.chkFontBold.Checked = fontBold;
            this.chkFontBold.Hide();
            this.txtName.BackColor = curBackColor;
            this.txtName.ForeColor = curForeColor;
            this.txtName.Text = text;
            this.txtName.Font = this.GetTextBoxFont(fontBold);
            this.txtName.SelectionLength = 0;
            this.btnCancel.Focus();
        }

        public new Color ForeColor { get; set; }
        public new Color BackColor { get; set; }
        public bool FontBold { get; set; }

        private Font GetTextBoxFont(bool fontBold)
        {
            Font newFont = null;
            if (fontBold)
            {
                newFont = new Font(this.txtName.Font, FontStyle.Bold);
            }
            else
            {
                newFont = new Font(this.txtName.Font, FontStyle.Regular);
            }
            return newFont;
        }

        private void pnForeColor_Click(object sender, EventArgs e)
        {
            ColorDialog colorDlg = new ColorDialog();
            colorDlg.Color = this.pnForeColor.BackColor;
            colorDlg.ShowDialog();
            this.pnForeColor.BackColor = colorDlg.Color;
            this.txtName.ForeColor = colorDlg.Color;
        }

        private void pnBackColor_Click(object sender, EventArgs e)
        {
            ColorDialog colorDlg = new ColorDialog();
            colorDlg.Color = this.pnBackColor.BackColor;
            colorDlg.ShowDialog();
            this.pnBackColor.BackColor = colorDlg.Color;
            this.txtName.BackColor = colorDlg.Color;
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            this.pnForeColor.BackColor = Color.FromArgb((int)EventMessageColorConfig.DefaultForeColor);
            this.pnBackColor.BackColor = Color.FromArgb((int)EventMessageColorConfig.DefaultBackColor);
            this.txtName.ForeColor = this.pnForeColor.BackColor;
            this.txtName.BackColor = this.pnBackColor.BackColor;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.ForeColor = this.pnForeColor.BackColor;
            this.BackColor = this.pnBackColor.BackColor;
            this.FontBold = this.chkFontBold.Checked;
            this.DialogResult = DialogResult.OK;
        }

        private void chkFontBold_CheckedChange(object sender, EventArgs e)
        {
            this.txtName.Font = this.GetTextBoxFont(this.chkFontBold.Checked);
        }
    }
}
