﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public class CellParamDataBase
    {
        public CellParamDataBase()
        { 
        }
        public CellParamDataBase(string name)
        {
            this.Name = name;
        }
        public override string ToString()
        {
            return Name;
        }
        public string Name { get; set; } = string.Empty;
        public List<CellParamTable> Tables { get; set; } = new List<CellParamTable>();

        public bool ContainsTable(string name)
        {
            foreach (CellParamTable tb in Tables)
            {
                if (tb.Name.Equals(name))
                {
                    return true;
                }
            }
            return false;
        }

        public bool RemoveTable(string name)
        {
            for (int i = 0; i < Tables.Count; i++)
            {
                if (Tables[i].Name.Equals(name))
                {
                    Tables.RemoveAt(i);
                    return true;
                }
            }
            return false;
        }

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> cfgDic = new Dictionary<string, object>();
                cfgDic["Name"] = Name;

                List<object> tableCfgs = new List<object>();
                foreach (CellParamTable tb in Tables)
                {
                    tableCfgs.Add(tb.CfgParam);
                }
                cfgDic["Tables"] = tableCfgs;
                return cfgDic;
            }
            set
            {
                if (value==null)
                {
                    return;
                }
                Name = value["Name"].ToString();
                Tables.Clear();
                List<object> tableCfgs = value["Tables"] as List<object>;
                foreach (object tbCfg in tableCfgs)
                {
                    CellParamTable table = new CellParamTable(this);
                    table.CfgParam = tbCfg as Dictionary<string, object>;
                    Tables.Add(table);
                }
            }
        }



        internal bool TryGetColunmByFullName(string fullColName, out CellParamColumn col)
        {
            col = null;
            if (fullColName.IndexOf(Name) >=0)
            {
                foreach (CellParamTable tb in Tables)
                {
                    if (tb.TryGetColunmByFullName(fullColName,out col))
                    {
                        return true;
                    }
                }
                return false;
            }
            else
            {
                return false;
            }
        }
    }
}
