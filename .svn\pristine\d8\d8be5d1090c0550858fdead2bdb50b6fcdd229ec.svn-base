﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Data;
using System.Collections;
using System.Drawing;
using MasterCom.MTGis;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public  class ZTNearestSiteByExcelQuery : QueryBase
    {
        protected string xlsFilePathBase = null;
        protected string xlsFilePathOther = null;
        protected int distance = 50;
        protected Color colorBase;
        protected Color colorOther;
        private readonly List<SiteBaseInfo> siteInfo;
        private readonly List<SiteNearbyInfo> siteNearbyInfo;
        private List<string> sheetsName { get; set; }

        private List<SiteNearbyInfo> errorInfo;
        private readonly List<SiteBaseInfo> resultList;
      
        public ZTNearestSiteByExcelQuery(MainModel mainModel)
            : base(mainModel)
        {
            siteInfo = new List<SiteBaseInfo>();
            siteNearbyInfo = new List<SiteNearbyInfo>();
            errorInfo = new List<SiteNearbyInfo>();
            resultList=new List<SiteBaseInfo>();
        }

        public override string Name
        {
            get { return "通过Excel查找附近规划站"; }
        }
        public override string IconName
        {
            get { return ""; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19049, this.Name);
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        protected void clearData()
        {
            siteInfo.Clear();
            siteNearbyInfo.Clear();
            errorInfo.Clear();
            resultList.Clear();
 
        }
        protected virtual bool ReadXLSPath()
        {
            ZTNearestSiteByExcelSettingForm form = new ZTNearestSiteByExcelSettingForm();
            form.SetFilePathBase(xlsFilePathBase);
            form.SetFilePathOther(xlsFilePathOther);
            if (form.ShowDialog() != System.Windows.Forms.DialogResult.OK) return false;
            xlsFilePathBase = form.FilePathBase;  //规划站的路径
            xlsFilePathOther = form.FilePathOther;//其他站点的路径
            distance = form.Distance;             //设置的距离
            colorBase = form.GetBaseColor();      //规划站的颜色
            colorOther = form.GetOtherColor();    //其他站点的颜色
       
            return true;
        }
        private void readXLS()
        {
            try
            {
                //对第一个文件的处理
                if (!System.IO.File.Exists(xlsFilePathBase))
                {
                    System.Windows.Forms.MessageBox.Show("此xls文件不存在", "提示");
                    return;
                }
                readBaseSites();
                //对第二个文件的处理
                if (!System.IO.File.Exists(xlsFilePathOther))
                {
                    System.Windows.Forms.MessageBox.Show("此xls文件不存在", "提示");
                    return;
                }
                readOtherSites();
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitTextBox.Close();
            }
        }
        private void readBaseSites()
        {
            ExcelNPOIReader reader = new ExcelNPOIReader(xlsFilePathBase);
            ExcelNPOITable tb = reader.GetTable();
            if (tb.CellValues == null)
            {
                return;
            }
            for (int i = 0; i < tb.CellValues.Count; i++)
            {
                SiteBaseInfo newbase = new SiteBaseInfo();
                newbase.SN = i + 1;
                newbase.CellName = tb.CellValues[i].GetValue(0).ToString();
                string longitude = tb.CellValues[i].GetValue(5).ToString();
                newbase.Longitude = Convert.ToDouble(longitude);
                string latitude = tb.CellValues[i].GetValue(6).ToString();
                newbase.Latitude = Convert.ToDouble(latitude);
                siteInfo.Add(newbase);
            }
        }
        private void readOtherSites()
        {
            errorInfo = new List<SiteNearbyInfo>();
            ExcelNPOIReader readerOther = new ExcelNPOIReader(xlsFilePathOther);
            sheetsName = new List<string>();
            sheetsName = readerOther.GetSheets();
            for (int i = 0; i < sheetsName.Count; i++)
            {
                List<string> cloNames = new List<string>();
                cloNames.Add("物理站址编号");
                cloNames.Add("规划经度");
                cloNames.Add("规划纬度");
                cloNames.Add("D频段规划编号");
                cloNames.Add("F频段规划编号");
                ExcelNPOITable tbLocation = readerOther.GetTable(sheetsName[i], cloNames);
                bool isError = false;
                for (int j = 0; j < tbLocation.CellValues.Count; j++)
                {
                    SiteNearbyInfo nearbyInfobase = new SiteNearbyInfo();
                    nearbyInfobase.Type = sheetsName[i];
                    isError = setNearbyInfo(tbLocation, isError, j, nearbyInfobase);

                    //判断是否为错误的数据加入错误信息中
                    if (!isError)
                    {
                        siteNearbyInfo.Add(nearbyInfobase);
                    }
                    else
                    {
                        errorInfo.Add(nearbyInfobase);
                    }
                }
            }
        }

        private bool setNearbyInfo(ExcelNPOITable tbLocation, bool isError, int j, SiteNearbyInfo nearbyInfobase)
        {
            if (tbLocation.CellValues[j].GetValue(0) == null)
            {
                nearbyInfobase.Identifier = "   ";
            }
            else
            {
                nearbyInfobase.Identifier = tbLocation.CellValues[j].GetValue(0).ToString();
            }
            if (tbLocation.CellValues[j].GetValue(1).ToString() == "")
            {
                isError = true;
                nearbyInfobase.Longitude = 0.0;
            }
            else
            {
                string longitude = tbLocation.CellValues[j].GetValue(1).ToString();
                nearbyInfobase.Longitude = Convert.ToDouble(longitude);
            }
            if (tbLocation.CellValues[j].GetValue(2).ToString() == "")
            {
                isError = true;
                nearbyInfobase.Latitude = 0.0;
            }
            else
            {
                string latitude = tbLocation.CellValues[j].GetValue(2).ToString();
                nearbyInfobase.Latitude = Convert.ToDouble(latitude);
            }
            if (tbLocation.CellValues[j].GetValue(3).ToString() == "")
            {
                nearbyInfobase.DNumber = "  ";
            }
            else
            {
                nearbyInfobase.DNumber = tbLocation.CellValues[j].GetValue(3).ToString();
            }
            if (tbLocation.CellValues[j].GetValue(4).ToString() == "")
            {
                nearbyInfobase.FNumber = "  ";
            }
            else
            {
                nearbyInfobase.FNumber = tbLocation.CellValues[j].GetValue(4).ToString();
            }

            return isError;
        }

        protected override void query()
        {
            if (!ReadXLSPath()) return;
            clearData();
            WaitTextBox.Show("正在读取数据...", readXLS);
            WaitTextBox.Show("正在加载图层", showPlace);
            WaitBox.CanCancel = true;
            WaitBox.Show("正在匹配数据...", dealWithData);

            fireShowForm();
        }
        private void dealWithData()
        {
           //处理数据
            try
            {
                int loop = 0;
                foreach (SiteBaseInfo bi in siteInfo)
                {
                    int sn = 1;
                    foreach (SiteNearbyInfo ni in siteNearbyInfo)
                    {
                        SiteNearbyInfo newInfo = new SiteNearbyInfo();
                        newInfo.Type = ni.Type;
                        newInfo.Identifier = ni.Identifier;
                        newInfo.Longitude = ni.Longitude;
                        newInfo.Latitude = ni.Latitude;
                        newInfo.DNumber = ni.DNumber;
                        newInfo.FNumber = ni.FNumber;
                        double distanceBetweens = getDistance(bi, newInfo);
                        if (distanceBetweens<distance)
                        {
                            newInfo.SN = sn++;
                            newInfo.Distance = Math.Round(distanceBetweens,2);
                            siteInfo[loop].NearbyInfo.Add(newInfo);
                        }
                    }
                    siteInfo[loop].NearSiteCounts = siteInfo[loop].NearbyInfo.Count;
                    if(siteInfo[loop].NearSiteCounts!=0)
                    {
                        resultList.Add(siteInfo[loop]);
                    }
                    loop++;
                    WaitBox.ProgressPercent = (int)(loop * 100.0 / siteInfo.Count);
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                for (int i = 0; i < resultList.Count; i++)
                {
                    resultList[i].SN = i + 1;
                }
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitBox.Close();
            }
  
        }
      
        private double getDistance(SiteBaseInfo baseLocation, SiteNearbyInfo compareLocation)
        {
            double distanceBetween = MathFuncs.GetDistance(baseLocation.Longitude, baseLocation.Latitude,
                compareLocation.Longitude, compareLocation.Latitude);
            return distanceBetween;
          
        }
        private void fireShowForm()
        {
            if (errorInfo.Count != 0)
            {
                System.Windows.Forms.MessageBox.Show("第二个表中一共有"+errorInfo.Count.ToString()+"条表项没有经纬度信息");
            }
            if (siteInfo.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            ZTNearestSiteByExcelInfoForm frm = MainModel.CreateResultForm(typeof(ZTNearestSiteByExcelInfoForm)) as ZTNearestSiteByExcelInfoForm;
            frm.FillData(resultList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
        private void showPlace()//显示地点到图层上面
        {
            PlanningStationByExcels planningStation = PlanningStationByExcels.GetInstance();
            try
            {
                planningStation.Activate(mainModel);
                List<DbPoint> dbPointBase = new List<DbPoint>();//读取规划站经纬度
                foreach (SiteBaseInfo baseInfo in siteInfo)
                {
                    setLngLatList(baseInfo.Longitude.ToString(), baseInfo.Latitude.ToString(), dbPointBase);
                }
                planningStation.Add(xlsFilePathBase, colorBase,dbPointBase);//添加规划站的图层
                List<DbPoint> dbPointOther = new List<DbPoint>();
                foreach (SiteNearbyInfo nearInfo in siteNearbyInfo)//读取其他站点经纬度
                {
                    setLngLatList(nearInfo.Longitude.ToString(), nearInfo.Latitude.ToString(), dbPointOther);
                }
                planningStation.Add(xlsFilePathOther, colorOther,dbPointOther);//添加其他站点的图层
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitTextBox.Close();
            }
        }
        private void setLngLatList(string longitude, string latitude, List<DbPoint> dbPoint)
        {
            double x = 0, y = 0;
            if (!double.TryParse(longitude, out x)
                || !double.TryParse(latitude, out y))
            {
                dbPoint.Add(null);
            }
            else
            {
                dbPoint.Add(new DbPoint(x, y));
            }
        }
    }
    public class SiteBaseInfo
    {
        public SiteBaseInfo()
        {
            NearbyInfo = new List<SiteNearbyInfo>();
        }
        public int SN { get; set; }
        public string CellName{ get; set; }
        public double Longitude{ get;set; }
        public double Latitude{get;set; }
        public int NearSiteCounts { get; set; }

        public List<SiteNearbyInfo> NearbyInfo { get; set; }
      
     }
    public class SiteNearbyInfo
    {
        public int SN { get; set; }
        public string Type { get; set; }
        public string Identifier { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string DNumber { get; set; }
        public string FNumber { get; set; }
        public double Distance { get; set; }
    
    }
    public class PlanningStationByExcels
    {
        private readonly Dictionary<string, StationShowItem> fileStationDictionary = new Dictionary<string, StationShowItem>();
        private MainModel mainModelBase;
        private AxMapWinGIS.AxMap axMapBase;
        private static PlanningStationByExcels instance = null;
        public static PlanningStationByExcels GetInstance()
        {
            if (instance == null)
            {
                instance = new PlanningStationByExcels();
            }
            return instance;
        }
        public void Activate(MainModel mm)
        {
            if (mainModelBase == null)
            {
                mainModelBase = mm;
                axMapBase = mainModelBase.MainForm.GetMapForm().GetMapFormControl();
            }
        }
        public List<string> GetFileNames()
        {
            return new List<string>(fileStationDictionary.Keys);
        }
        /// <summary>
        /// 添加到图层显示
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="color">设置的颜色</param>
        /// <param name="dbPoint">点的集合</param>
        public void Add(string fileName, Color color, List<DbPoint> dbPoint)
        {
            StationShowItem item = new StationShowItem();
            item.StationInfo = new StationInfo();
            item.StationInfo.LngLatList = dbPoint;
            item.DrawStyle = new MapWinGIS.ShapeDrawingOptions();
            item.DrawStyle.Visible = true;
            item.DrawStyle.FillColor = (uint)ColorTranslator.ToOle(color);
            item.DrawStyle.PointSize = 7;
            item.DrawStyle.PointType = MapWinGIS.tkPointSymbolType.ptSymbolStandard;
            item.DrawStyle.PointShape = MapWinGIS.tkPointShapeType.ptShapeCross;
            item.DrawStyle.LineVisible = false;
            ThreadUtil.Invoke(axMapBase, addLayer, item);//委托
            if (fileStationDictionary.ContainsKey(fileName))
            {
                fileStationDictionary.Remove(fileName);
            }
            fileStationDictionary.Add(fileName, item);
        }
        private void addLayer(Object obj)
        {
            StationShowItem item = obj as StationShowItem;
            item.Shp = item.StationInfo.CreateDisplayShpfile();
            item.Shp.DefaultDrawingOptions = item.DrawStyle;
            item.layerHandle = axMapBase.AddLayer(item.Shp, true);
            mainModelBase.MainForm.GetMapForm().TempLayerHandle.Add(item.layerHandle);//添加为临时图层
        }
        public bool Refresh(string fileName)
        {
            axMapBase.Redraw();
            return true;
        }
        public bool Remove(string fileName)
        {
            if (!fileStationDictionary.ContainsKey(fileName))
            {
                return false;
            }
            StationShowItem item = fileStationDictionary[fileName];
            axMapBase.RemoveLayer(item.layerHandle);
            fileStationDictionary.Remove(fileName);
            return true;
        }
        public StationShowItem GetItem(string fileName)
        {
            if (fileStationDictionary.ContainsKey(fileName))
            {
                return fileStationDictionary[fileName];
            }
            return null;
        }
    }
}
