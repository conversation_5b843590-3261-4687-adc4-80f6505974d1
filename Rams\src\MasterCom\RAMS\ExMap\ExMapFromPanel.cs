﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using GMap.NET;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ExMap
{
    public partial class ExMapFromPanel : UserControl
    {

        public ENM_GMapOpStatus curOpMode { get; set; } = ENM_GMapOpStatus.E_SELECT;
        private MapForm mapForm = null;
        public MapForm MapForm
        {
            get { return mapForm; }
        }
        public ExMapFromPanel(MapForm mapform)
        {
            InitializeComponent();
            this.mapForm = mapform;
            MainMap.OnTileLoadStart += MainMap_OnTileLoadStart;
            MainMap.OnTileLoadComplete += MainMap_OnTileLoadComplete;
            MainMap.ParentExMapFormPanel = this;
            if(!this.DesignMode)
            {
                MainMap.Position = new PointLatLng(22.5267, 114.0507);
                
                MainMap.MapType =GMaps.IsOffLineMode? MapType.MT_Satellite:MapType.GoogleSatellite;
                MainMap.MinZoom = 1;
                MainMap.MaxZoom = 22;//最详细视图，不能超过22，否则会出现贴图错误
                MainMap.Zoom = 15;
                MainMap.DragButton = MouseButtons.Left;
            }
            GMaps.DoSetMapSrcUrl(this.mapForm.MainModel.MapURLsList);
            ApplyResvRegion(this.mapForm.MainModel.SearchGeometrys.SelectedResvRegions);
            ApplyStreet(this.mapForm.MainModel.SearchGeometrys.SelectedStreets);
        }

        void MainMap_OnTileLoadStart()
        {
            mapForm.TileLoaded = false;
        }

        void MainMap_OnTileLoadComplete(long ElapsedMilliseconds)
        {
            mapForm.TileLoaded = true;
        }
        public void SetMapType(MapTypeAdapter mtype)
        {
            MainMap.SetMapType(mtype);
        }
        public MapForm getMapFormInstance()
        {
            return this.mapForm;
        }

        public void FreshInvalidate()
        {
            MainMap.UpdateMap();
        }

        public void ApplyResvRegion(List<MasterCom.Util.ResvRegion> list)
        {
            MainMap.RsvRegionLayer.ApplyResvRegion(list);
            MainMap.ExMapScanMod3IndexLayer.ApplyResvRegion(list);
        }
        public void ApplyStreet(List<MapWinGIS.Shape> list)
        {
            MainMap.RsvStreetLayer.ApplyStreets(list);
        }

        /// <summary>
        /// 中心点
        /// </summary>
        /// <param name="point"></param>
        internal void GotoPositionPoint(DbPoint point)
        {
            MainMap.GotoView(point);
        }

        internal void GoToView(double jd, double wd, double zoom)
        {
            DbPoint pnt = new DbPoint(jd, wd);
            MainMap.GotoView(pnt);
            MainMap.Zoom = zoom;
        }

        internal void GotoPositionBounds(DbRect bounds)
        {
            MainMap.GotoView(bounds);
        }

        public DbRect MapBounds
        {
            get { return MainMap.CurBoundsAdaptered; }
        }

        /// <summary>
        /// 设置地图工具
        /// </summary>
        /// <param name="opStatus"></param>
        internal void SetCurrentToolStatus(ENM_GMapOpStatus opStatus)
        {
            MainMap.SetCurrentToolStatus(opStatus);
        }

        internal void ApplyAddRectTool(DbRect rect)
        {
            MainMap.ApplyAddRectTool(rect);
        }

        internal void ApplyMeasureToolData(List<DbPoint> points, bool measureStarted)
        {
            MainMap.ApplyMeasureToolData(points, measureStarted);
        }

        public void ApplyAddPolygonTool(List<DbPoint> points)
        {
            MainMap.ApplyAddPolygonTool(points);
        }

        internal void FireMapFormRegionChanged_Polygon(List<DbPoint> polygonPointsList)
        {
            mapForm.FireMapFormRegionChanged_Polygon(polygonPointsList);
        }

        internal void FireMapFormRegionChanged_Rectangle(DbRect rectRegion)
        {
            mapForm.FireMapFormRegionChanged_Rectangle(rectRegion);
        }

        internal DbRect GetMapCurrentViewBounds()
        {
            return MainMap.GetCurrentViewBounds();
        }

        internal void SetLayerVisible(string alias, bool visible)
        {
            MainMap.SetLayerVisible(alias, visible);
        }

        internal void ClearRegionLayer()
        {
            MainMap.ClearRegionLayer();
        }

        public MTExGMap getMainMap()
        {
            return this.MainMap; 
        }
    }
    
}
