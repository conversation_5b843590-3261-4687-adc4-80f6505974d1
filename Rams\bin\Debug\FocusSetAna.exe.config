﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
      <section name="FocusSetAna.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <userSettings>
    <FocusSetAna.Properties.Settings>
      <setting name="AreaTable13_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable14_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable15_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable16_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable17_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable18_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable19_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable20_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable21_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable22_1" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable13" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable14" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable15" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable16" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable17" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable18" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable19" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable20" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable21" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable22" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable13_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable14_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable15_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable16_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable17_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable18_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable19_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable20_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable21_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable22_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable1_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable2_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable3_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable4_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable5_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable6_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable7_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable8_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable9_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable10_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable11_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable12_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable13_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable14_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable15_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable16_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable17_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable18_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable19_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable20_3" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable22_3" serializeAs="String">
        <value />
      </setting>
      <setting name="ESValueIgnoreAt" serializeAs="String">
        <value>9</value>
      </setting>
      <setting name="AreaTable21_3" serializeAs="String">
        <value />
      </setting>
      <setting name="TestPlanGridGetByMapProjectList" serializeAs="String">
        <value>1,10,14</value>
      </setting>
      <setting name="CQTAreaID" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="NopServerPort" serializeAs="String">
        <value>7100</value>
      </setting>
      <setting name="InterSeconds" serializeAs="String">
        <value>7200</value>
      </setting>
      <setting name="StartHour" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="StopHour" serializeAs="String">
        <value>24</value>
      </setting>
      <setting name="TestPlanPath" serializeAs="String">
        <value>F:\workplace\Project\MasterCom\DtDrvSystem\FocusSetAna\bin\Debug\TestPlan</value>
      </setting>
      <setting name="NeedReadUserConfig" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="NeedFilterFileByTestPlan" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="BoundaryAreaTableId" serializeAs="String">
        <value>-2</value>
      </setting>
      <setting name="NopOrderConnectionString" serializeAs="String">
        <value>2h71eoBl/XL2LIvZPHBSZkHUfSNwRyi/gnAdU2o3Hn96iAOkidDBilToCwxx5UZlbuMHwvG/gAGIXNk7tjjbihmGL+Hytyktbbt4kf9KiI2Ewo03MSz16LpR0hO2pH9TEB0UdtjQzjA=</value>
      </setting>
      <setting name="AreaTable2_1" serializeAs="String">
        <value>2|D:\Application\集中优化\maps\GEOGRAPHIC\中卫市\县界_region.shp|NAME|obj</value>
      </setting>
      <setting name="AreaTable3_1" serializeAs="String">
        <value>2|D:\Application\集中优化\maps\GEOGRAPHIC\吴忠市\县界_region.shp|NAME|obj</value>
      </setting>
      <setting name="AreaTable4_1" serializeAs="String">
        <value>2|D:\Application\集中优化\maps\GEOGRAPHIC\石嘴山市\县界_region.shp|NAME|obj</value>
      </setting>
      <setting name="AreaTable5_1" serializeAs="String">
        <value>2|D:\Application\集中优化\maps\GEOGRAPHIC\固原市\县界_region.shp|NAME|obj</value>
      </setting>
      <setting name="AreaTable6_1" serializeAs="String">
        <value>2|D:\Application\集中优化\maps\GEOGRAPHIC\全区\县界_region.shp|NAME|obj</value>
      </setting>
      <setting name="AreaTable7_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable8_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable9_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable10_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable11_1" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable12_1" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable1" serializeAs="String">
        <value>10|D:\Application\集中优化\maps\GEOGRAPHIC\银川市\公路_polyline.shp|NAME|obj</value>
      </setting>
      <setting name="RoadTable2" serializeAs="String">
        <value>10|D:\Application\集中优化\maps\GEOGRAPHIC\中卫市\公路_polyline.shp|NAME|obj</value>
      </setting>
      <setting name="RoadTable3" serializeAs="String">
        <value>10|D:\Application\集中优化\maps\GEOGRAPHIC\吴忠市\公路_polyline.shp|NAME|obj</value>
      </setting>
      <setting name="RoadTable4" serializeAs="String">
        <value>10|D:\Application\集中优化\maps\GEOGRAPHIC\石嘴山市\公路_polyline.shp|NAME|obj</value>
      </setting>
      <setting name="RoadTable5" serializeAs="String">
        <value>10|D:\Application\集中优化\maps\GEOGRAPHIC\固原市\公路_polyline.shp|NAME|obj</value>
      </setting>
      <setting name="RoadTable6" serializeAs="String">
        <value>10|D:\Application\集中优化\maps\GEOGRAPHIC\全区\公路_polyline.shp|NAME|obj</value>
      </setting>
      <setting name="RoadTable7" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable8" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable9" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable10" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable11" serializeAs="String">
        <value />
      </setting>
      <setting name="RoadTable12" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable1_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable2_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable3_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable4_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable5_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable6_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable7_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable8_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable9_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable10_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable11_2" serializeAs="String">
        <value />
      </setting>
      <setting name="AreaTable12_2" serializeAs="String">
        <value />
      </setting>
      <setting name="GridRefAreaTableId" serializeAs="String">
        <value>2</value>
      </setting>
      <setting name="ValidMonthBeforeNow" serializeAs="String">
        <value>2</value>
      </setting>
      <setting name="DTASYSTEMConnectionString" serializeAs="String">
        <value>2h71eoBl/XL2LIvZPHBSZqRXnXpvE5D7+2VtNH3igvX2itST+EXoP9RFfqmrhMxm78o2TLs2uEM0336Fh000PCib9OXT4e3eTgrtlW8L4wp654zpyWtNLJRrPSVwv7y3</value>
      </setting>
      <setting name="DwRefAreaTableId" serializeAs="String">
        <value>2</value>
      </setting>
      <setting name="AreaTable1_1" serializeAs="String">
        <value>2|D:\Application\集中优化\maps\GEOGRAPHIC\银川市\县界_region.shp|NAME|obj</value>
      </setting>
      <setting name="InjRefAreaTableId" serializeAs="String">
        <value>2</value>
      </setting>
      <setting name="CheckStatus" serializeAs="String">
        <value>6</value>
      </setting>
    </FocusSetAna.Properties.Settings>
  </userSettings>
</configuration>