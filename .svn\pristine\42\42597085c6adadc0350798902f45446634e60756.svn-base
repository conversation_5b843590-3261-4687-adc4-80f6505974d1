﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class HandoverQueryCfg : DIYSQLBase
    {
        private string sql;
        private readonly List<string> result;

        public HandoverQueryCfg(MainModel mModel)
            : base(mModel) 
        { 
            sql = string.Empty;
            result = new List<string>();
        }

        protected override string getSqlTextString()
        {
            return sql;
        }

        public void SetSqlText(string sql)
        {
            this.sql = sql;
        }

        public List<string> GetResult()
        {
            return result;
        }

        protected override MasterCom.RAMS.Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] type = new E_VType[1];
            type[0] = E_VType.E_String;
            return type;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            result.Clear();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string strRtn = package.Content.GetParamString();
                    if (strRtn.Trim() != "" && !result.Contains(strRtn))
                    {
                        result.Add(strRtn.Trim());
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
    }
}
