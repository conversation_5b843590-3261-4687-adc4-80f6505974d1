﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class GSMWeakCovRoadListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(GSMWeakCovRoadListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportTab = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miDisplayAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewRoad = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCity = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDitance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSecond = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colMaxNCellMaxRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colMaxNCellMinRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colMaxNCellAvgRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSampleOpRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSamplePlanRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPcchC2IMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPcchC2IMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPcchC2IAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDpchC2IMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDpchC2IMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDpchC2IAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellInfos = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAreaName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGridName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAreaAgentName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnArea = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoad)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.miExportTab,
            this.toolStripMenuItem1,
            this.miDisplayAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(173, 76);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(172, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miExportTab
            // 
            this.miExportTab.Name = "miExportTab";
            this.miExportTab.Size = new System.Drawing.Size(172, 22);
            this.miExportTab.Text = "导出Shp";
            this.miExportTab.Click += new System.EventHandler(this.miExportTab_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(169, 6);
            // 
            // miDisplayAll
            // 
            this.miDisplayAll.Name = "miDisplayAll";
            this.miDisplayAll.Size = new System.Drawing.Size(172, 22);
            this.miDisplayAll.Text = "显示全部弱覆盖点";
            this.miDisplayAll.Click += new System.EventHandler(this.miDisplayAll_Click);
            // 
            // ListViewRoad
            // 
            this.ListViewRoad.AllColumns.Add(this.olvColumnSN);
            this.ListViewRoad.AllColumns.Add(this.olvColumnCity);
            this.ListViewRoad.AllColumns.Add(this.olvColumnFileName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnLongitudeMid);
            this.ListViewRoad.AllColumns.Add(this.olvColumnLatitudeMid);
            this.ListViewRoad.AllColumns.Add(this.olvColumnRoadName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnDitance);
            this.ListViewRoad.AllColumns.Add(this.olvColumnSecond);
            this.ListViewRoad.AllColumns.Add(this.olvColumnSample);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxRxlev);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinRxlev);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAvgRxlev);
            this.ListViewRoad.AllColumns.Add(this.colMaxNCellMaxRscp);
            this.ListViewRoad.AllColumns.Add(this.colMaxNCellMinRscp);
            this.ListViewRoad.AllColumns.Add(this.colMaxNCellAvgRscp);
            this.ListViewRoad.AllColumns.Add(this.olvColumnSampleOpRate);
            this.ListViewRoad.AllColumns.Add(this.olvColumnSamplePlanRate);
            this.ListViewRoad.AllColumns.Add(this.olvColumnPcchC2IMax);
            this.ListViewRoad.AllColumns.Add(this.olvColumnPcchC2IMin);
            this.ListViewRoad.AllColumns.Add(this.olvColumnPcchC2IAvg);
            this.ListViewRoad.AllColumns.Add(this.olvColumnDpchC2IMax);
            this.ListViewRoad.AllColumns.Add(this.olvColumnDpchC2IMin);
            this.ListViewRoad.AllColumns.Add(this.olvColumnDpchC2IAvg);
            this.ListViewRoad.AllColumns.Add(this.olvColumnCellInfos);
            this.ListViewRoad.AllColumns.Add(this.olvColumnArea);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAreaName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnGridName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAreaAgentName);
            this.ListViewRoad.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCity,
            this.olvColumnFileName,
            this.olvColumnLongitudeMid,
            this.olvColumnLatitudeMid,
            this.olvColumnRoadName,
            this.olvColumnDitance,
            this.olvColumnSecond,
            this.olvColumnSample,
            this.olvColumnMaxRxlev,
            this.olvColumnMinRxlev,
            this.olvColumnAvgRxlev,
            this.colMaxNCellMaxRscp,
            this.colMaxNCellMinRscp,
            this.colMaxNCellAvgRscp,
            this.olvColumnSampleOpRate,
            this.olvColumnSamplePlanRate,
            this.olvColumnPcchC2IMax,
            this.olvColumnPcchC2IMin,
            this.olvColumnPcchC2IAvg,
            this.olvColumnDpchC2IMax,
            this.olvColumnDpchC2IMin,
            this.olvColumnDpchC2IAvg,
            this.olvColumnCellInfos,
            this.olvColumnArea,
            this.olvColumnAreaName,
            this.olvColumnGridName,
            this.olvColumnAreaAgentName});
            this.ListViewRoad.ContextMenuStrip = this.ctxMenu;
            this.ListViewRoad.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewRoad.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewRoad.FullRowSelect = true;
            this.ListViewRoad.GridLines = true;
            this.ListViewRoad.HeaderWordWrap = true;
            this.ListViewRoad.IsNeedShowOverlay = false;
            this.ListViewRoad.Location = new System.Drawing.Point(0, 0);
            this.ListViewRoad.Name = "ListViewRoad";
            this.ListViewRoad.OwnerDraw = true;
            this.ListViewRoad.ShowGroups = false;
            this.ListViewRoad.Size = new System.Drawing.Size(1073, 502);
            this.ListViewRoad.TabIndex = 5;
            this.ListViewRoad.UseCompatibleStateImageBehavior = false;
            this.ListViewRoad.View = System.Windows.Forms.View.Details;
            this.ListViewRoad.VirtualMode = true;
            this.ListViewRoad.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 40;
            // 
            // olvColumnCity
            // 
            this.olvColumnCity.HeaderFont = null;
            this.olvColumnCity.Text = "地市";
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            this.olvColumnFileName.Width = 120;
            // 
            // olvColumnLongitudeMid
            // 
            this.olvColumnLongitudeMid.HeaderFont = null;
            this.olvColumnLongitudeMid.Text = "中心经度";
            this.olvColumnLongitudeMid.Width = 80;
            // 
            // olvColumnLatitudeMid
            // 
            this.olvColumnLatitudeMid.HeaderFont = null;
            this.olvColumnLatitudeMid.Text = "中心纬度";
            this.olvColumnLatitudeMid.Width = 80;
            // 
            // olvColumnRoadName
            // 
            this.olvColumnRoadName.HeaderFont = null;
            this.olvColumnRoadName.Text = "道路名称";
            this.olvColumnRoadName.Width = 200;
            // 
            // olvColumnDitance
            // 
            this.olvColumnDitance.HeaderFont = null;
            this.olvColumnDitance.Text = "持续距离(米)";
            this.olvColumnDitance.Width = 80;
            // 
            // olvColumnSecond
            // 
            this.olvColumnSecond.HeaderFont = null;
            this.olvColumnSecond.Text = "持续时间(秒)";
            this.olvColumnSecond.Width = 80;
            // 
            // olvColumnSample
            // 
            this.olvColumnSample.HeaderFont = null;
            this.olvColumnSample.Text = "采样点数";
            this.olvColumnSample.Width = 80;
            // 
            // olvColumnMaxRxlev
            // 
            this.olvColumnMaxRxlev.HeaderFont = null;
            this.olvColumnMaxRxlev.Text = "最大场强";
            this.olvColumnMaxRxlev.Width = 80;
            // 
            // olvColumnMinRxlev
            // 
            this.olvColumnMinRxlev.HeaderFont = null;
            this.olvColumnMinRxlev.Text = "最小场强";
            this.olvColumnMinRxlev.Width = 80;
            // 
            // olvColumnAvgRxlev
            // 
            this.olvColumnAvgRxlev.HeaderFont = null;
            this.olvColumnAvgRxlev.Text = "平均场强";
            this.olvColumnAvgRxlev.Width = 80;
            // 
            // colMaxNCellMaxRscp
            // 
            this.colMaxNCellMaxRscp.HeaderFont = null;
            this.colMaxNCellMaxRscp.Text = "最强TD邻区最大场强";
            // 
            // colMaxNCellMinRscp
            // 
            this.colMaxNCellMinRscp.HeaderFont = null;
            this.colMaxNCellMinRscp.Text = "最强TD邻区最小场强";
            // 
            // colMaxNCellAvgRscp
            // 
            this.colMaxNCellAvgRscp.HeaderFont = null;
            this.colMaxNCellAvgRscp.Text = "最强TD邻区平均场强";
            // 
            // olvColumnSampleOpRate
            // 
            this.olvColumnSampleOpRate.HeaderFont = null;
            this.olvColumnSampleOpRate.Text = "优化问题比例";
            this.olvColumnSampleOpRate.Width = 80;
            // 
            // olvColumnSamplePlanRate
            // 
            this.olvColumnSamplePlanRate.HeaderFont = null;
            this.olvColumnSamplePlanRate.Text = "规划问题比例";
            this.olvColumnSamplePlanRate.Width = 80;
            // 
            // olvColumnPcchC2IMax
            // 
            this.olvColumnPcchC2IMax.HeaderFont = null;
            this.olvColumnPcchC2IMax.Text = "最大PCCPCH_C/I";
            this.olvColumnPcchC2IMax.Width = 100;
            // 
            // olvColumnPcchC2IMin
            // 
            this.olvColumnPcchC2IMin.HeaderFont = null;
            this.olvColumnPcchC2IMin.Text = "最小PCCPCH_C/I";
            this.olvColumnPcchC2IMin.Width = 100;
            // 
            // olvColumnPcchC2IAvg
            // 
            this.olvColumnPcchC2IAvg.HeaderFont = null;
            this.olvColumnPcchC2IAvg.Text = "平均PCCPCH_C/I";
            this.olvColumnPcchC2IAvg.Width = 100;
            // 
            // olvColumnDpchC2IMax
            // 
            this.olvColumnDpchC2IMax.HeaderFont = null;
            this.olvColumnDpchC2IMax.Text = "最大DPCH_C/I";
            this.olvColumnDpchC2IMax.Width = 100;
            // 
            // olvColumnDpchC2IMin
            // 
            this.olvColumnDpchC2IMin.HeaderFont = null;
            this.olvColumnDpchC2IMin.Text = "最小DPCH_C/I";
            this.olvColumnDpchC2IMin.Width = 100;
            // 
            // olvColumnDpchC2IAvg
            // 
            this.olvColumnDpchC2IAvg.HeaderFont = null;
            this.olvColumnDpchC2IAvg.Text = "平均DPCH_C/I";
            this.olvColumnDpchC2IAvg.Width = 100;
            // 
            // olvColumnCellInfos
            // 
            this.olvColumnCellInfos.HeaderFont = null;
            this.olvColumnCellInfos.Text = "小区信息";
            // 
            // olvColumnAreaName
            // 
            this.olvColumnAreaName.HeaderFont = null;
            this.olvColumnAreaName.Text = "片区";
            // 
            // olvColumnGridName
            // 
            this.olvColumnGridName.HeaderFont = null;
            this.olvColumnGridName.Text = "网格";
            // 
            // olvColumnAreaAgentName
            // 
            this.olvColumnAreaAgentName.HeaderFont = null;
            this.olvColumnAreaAgentName.Text = "代维分区";
            // 
            // olvColumnArea
            // 
            this.olvColumnArea.HeaderFont = null;
            this.olvColumnArea.Text = "区域";
            // 
            // GSMWeakCovRoadListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1073, 502);
            this.Controls.Add(this.ListViewRoad);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "GSMWeakCovRoadListForm";
            this.Text = "弱覆盖路段";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoad)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miExportTab;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private BrightIdeasSoftware.TreeListView ListViewRoad;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnDitance;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnSample;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitudeMid;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitudeMid;
        private BrightIdeasSoftware.OLVColumn olvColumnPcchC2IMax;
        private BrightIdeasSoftware.OLVColumn olvColumnPcchC2IMin;
        private BrightIdeasSoftware.OLVColumn olvColumnPcchC2IAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnSecond;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnAreaName;
        private BrightIdeasSoftware.OLVColumn olvColumnGridName;
        private BrightIdeasSoftware.OLVColumn olvColumnAreaAgentName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellInfos;
        private BrightIdeasSoftware.OLVColumn olvColumnDpchC2IMax;
        private BrightIdeasSoftware.OLVColumn olvColumnDpchC2IMin;
        private BrightIdeasSoftware.OLVColumn olvColumnDpchC2IAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleOpRate;
        private BrightIdeasSoftware.OLVColumn olvColumnSamplePlanRate;
        private System.Windows.Forms.ToolStripMenuItem miDisplayAll;
        private BrightIdeasSoftware.OLVColumn colMaxNCellMaxRscp;
        private BrightIdeasSoftware.OLVColumn colMaxNCellMinRscp;
        private BrightIdeasSoftware.OLVColumn colMaxNCellAvgRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnCity;
        private BrightIdeasSoftware.OLVColumn olvColumnArea;

    }
}