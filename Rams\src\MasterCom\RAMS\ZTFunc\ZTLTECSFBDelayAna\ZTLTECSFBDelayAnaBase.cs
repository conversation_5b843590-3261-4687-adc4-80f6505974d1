﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTECSFBDelayAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public List<ZTLTECSFBDelayFileItem> resultList { get; set; } = new List<ZTLTECSFBDelayFileItem>();    //保存结果

        public ZTLTECSFBDelayAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = true;
        } 

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<ZTLTECSFBDelayFileItem>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                ZTLTECSFBDelayFileItem fileItem = new ZTLTECSFBDelayFileItem(fileMng.FileName, fileMng.MoMtFlag);

                List<DTData> dtDataList = getDTDatas(fileMng);

                dealDTDatas(fileItem, dtDataList);

                if (fileItem.csfbList.Count > 1)
                {
                    fileItem.SN = resultList.Count + 1;
                    resultList.Add(fileItem);
                }
            }
        }

        private List<DTData> getDTDatas(DTFileDataManager fileMng)
        {
            List<DTData> dtDataList = new List<DTData>();

            foreach (TestPoint tp in fileMng.TestPoints)
            {
                dtDataList.Add((DTData)tp);
            }

            foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
            {
                if (msg.ID == (int)EnumLteNBCheckMsg.RRCConnectionRelease || msg.ID == (int)EnumLteNBCheckMsg.GSMServiceRequest
                    || msg.ID == (int)EnumLteNBCheckMsg.PagingResponse || msg.ID == (int)EnumLteNBCheckMsg.GSMSetup
                    || msg.ID == (int)EnumLteNBCheckMsg.TDCMServiceRequest || msg.ID == (int)EnumLteNBCheckMsg.TDSetup)
                {
                    dtDataList.Add((DTData)msg);
                }
            }
            foreach (Event evt in fileMng.Events)
            {
                if (evt.ID == (int)EnumCsfbEvent.MOCSFBRequest || evt.ID == (int)EnumCsfbEvent.MOCSFBLTERelease
                    || evt.ID == (int)EnumCsfbEvent.MOCSFBProceeding || evt.ID == (int)EnumCsfbEvent.MTCSFBRequest
                    || evt.ID == (int)EnumCsfbEvent.MTCSFBLTERelease || evt.ID == (int)EnumCsfbEvent.MTCSFBProceeding
                    || evt.ID == (int)EnumCsfbEvent.TrackAreaUpdateAttempt || evt.ID == (int)EnumCsfbEvent.TrackAreaUpdateSuccess
                    || evt.ID == (int)EnumCsfbEvent.TrackAreaUpdateFail || evt.ID == (int)EnumCsfbEvent.LocationUpdateRequestLte_Gsm)
                {
                    dtDataList.Add((DTData)evt);
                }
            }

            dtDataList.Sort(comparer);
            return dtDataList;
        }

        private void dealDTDatas(ZTLTECSFBDelayFileItem fileItem, List<DTData> dtDataList)
        {
            TestPoint lastTp = null;
            Message lastMsg = null;
            ZTLTECSFBDelayAnaItem csfbItem = new ZTLTECSFBDelayAnaItem();

            for (int i = 0; i < dtDataList.Count; i++)
            {
                if (dtDataList[i] is TestPoint)
                {
                    lastTp = dtDataList[i] as TestPoint;                  //用最近的采样点经纬度来填充
                }
                else if (dtDataList[i] is Event)
                {
                    Event evt = dtDataList[i] as Event;
                    if (lastMsg != null && lastMsg.ID == (int)EnumLteNBCheckMsg.RRCConnectionRelease)
                    {
                        csfbItem.Latitude = evt.Latitude;
                        csfbItem.Longitude = evt.Longitude;
                    }
                    if ((evt.ID == (int)EnumCsfbEvent.MOCSFBRequest || evt.ID == (int)EnumCsfbEvent.MTCSFBRequest)
                        && csfbItem.IsGotBegin)
                    {
                        //前一个事件
                        addToResultList(csfbItem, fileItem);
                        csfbItem = new ZTLTECSFBDelayAnaItem();

                    }
                    doWithEvent(dtDataList[i], lastTp, ref csfbItem);
                }
                else if (dtDataList[i] is MasterCom.RAMS.Model.Message)
                {
                    lastMsg = dtDataList[i] as Message;
                    doWithMsg(dtDataList[i], lastTp, ref csfbItem);
                }
            }

            addToResultList(csfbItem, fileItem); //最后一个
        }

        private void doWithEvent(DTData dtData, TestPoint lastTp, ref ZTLTECSFBDelayAnaItem curItem)
        {
            Event evt = (Event)dtData;

            if (evt.ID == (int)EnumCsfbEvent.MOCSFBRequest || evt.ID == (int)EnumCsfbEvent.MTCSFBRequest)              //开始
            {
                curItem.AddBeginEvtInfo(lastTp, evt);
            }
            else if (evt.ID == (int)EnumCsfbEvent.MOCSFBProceeding || evt.ID == (int)EnumCsfbEvent.MTCSFBProceeding)      //结束
            {
                if (curItem.IsGotBegin && (long)curItem.EvtCsfbRequest["Value7"] == (long)evt["Value7"]) //已经开始，并且value7相等
                {
                    curItem.AddEndEvtInfo(lastTp,evt);
                }
            }
            else if (evt.ID == (int)EnumCsfbEvent.MOCSFBLTERelease || evt.ID == (int)EnumCsfbEvent.MTCSFBLTERelease)      //中间事件
            {
                if (curItem.IsGotBegin && (long)curItem.EvtCsfbRequest["Value7"] == (long)evt["Value7"]) //已经开始，并且value7相等
                {
                    curItem.AddMidEvtInfo(evt);
                }
            }
            else if (evt.ID == (int)EnumCsfbEvent.TrackAreaUpdateAttempt)      //TAC更新请求
            {
                setCurItem(curItem, evt, "TA");
            }
            else if (evt.ID == (int)EnumCsfbEvent.LocationUpdateRequestLte_Gsm)      //LAC更新请求
            {
                setCurItem(curItem, evt, "LAC");
            }
        }

        private static void setCurItem(ZTLTECSFBDelayAnaItem curItem, Event evt, string isTAUpdate)
        {
            if (curItem.IsGotBegin && curItem.EvtCsfbProceeding == null)  //有回落请求开始，尚未匹配回落结束事件
            {
                curItem.IsTAUpdate = isTAUpdate;
                curItem.Latitude = evt.Latitude;
                curItem.Longitude = evt.Longitude;
            }
        }

        private void doWithMsg(DTData dtData, TestPoint lastTp, ref ZTLTECSFBDelayAnaItem curItem)
        {
            MasterCom.RAMS.Model.Message msg = (MasterCom.RAMS.Model.Message)dtData;

            if (curItem.IsGotBegin) //已经有开始信息
            {
                curItem.msgList.Add(msg);
                if (msg.ID == (int)EnumLteNBCheckMsg.GSMSetup || msg.ID == (int)EnumLteNBCheckMsg.TDSetup)
                {
                    curItem.TpBeforeSetup = lastTp;
                }
            }
        }

        private void addToResultList(ZTLTECSFBDelayAnaItem csfbItem, ZTLTECSFBDelayFileItem fileItem)
        {
            //没有回落开始，不处理
            if (!csfbItem.IsGotBegin)
            {
                return;
            }

            csfbItem.SN = fileItem.csfbList.Count + 1;

            //开始填充回落前信息
            csfbItem.CellNameBegin = getLTECellNameByEvt(csfbItem.EvtCsfbRequest);

            if (csfbItem.msgList.Count > 0
                && csfbItem.msgList[0].ID == (int)EnumLteNBCheckMsg.RRCConnectionRelease)
            {
                csfbItem.MsgNameBegin = "RRCConnectionRelease";
                csfbItem.MsgTimeBegin = csfbItem.msgList[0].TimeStringWithMillisecond;
                getRRCConnReleaseInfo(ref csfbItem);
            }   
            
            //针对回落失败
            if (csfbItem.EvtCsfbProceeding == null)  //没有结束信息
            {
                csfbItem.CsfbResult = "失败";
                csfbItem.CsfbType = "";
                csfbItem.IsTAUpdate = "";   

                fileItem.csfbList.Add(csfbItem);
                return;
            }

            //回落成功
            csfbItem.CsfbResult = "成功";
            getCsfbCellInfo(ref csfbItem);

            //没有release信令
            if (csfbItem.EvtCsfbConnRelease == null)
            {
                csfbItem.MsgNameBegin = "无RRCConnectionRelease信令";
                fileItem.csfbList.Add(csfbItem);
                return;
            }

            csfbItem.CsfbDelayTime = (csfbItem.EvtCsfbProceeding.lTimeWithMillsecond - csfbItem.EvtCsfbConnRelease.lTimeWithMillsecond).ToString();

            if(csfbItem.msgList.Count >= 2)
            {
                if (csfbItem.msgList[1].ID == (int)EnumLteNBCheckMsg.GSMServiceRequest || csfbItem.msgList[1].ID == (int)EnumLteNBCheckMsg.TDCMServiceRequest)
                {
                    csfbItem.MsgNameEnd = "CMServiceRequest";
                }
                else if(csfbItem.msgList[1].ID == (int)EnumLteNBCheckMsg.PagingResponse)
                {
                    csfbItem.MsgNameEnd = "PagingResponse";
                }
                else if (csfbItem.msgList[1].ID == (int)EnumLteNBCheckMsg.GSMSetup || csfbItem.msgList[1].ID == (int)EnumLteNBCheckMsg.TDSetup)
                {
                    csfbItem.MsgNameEnd = "Setup";
                }

                csfbItem.MsgTimeEnd = csfbItem.msgList[1].TimeStringWithMillisecond;
            }
            
            fileItem.csfbList.Add(csfbItem);
        }

        private void getRRCConnReleaseInfo(ref ZTLTECSFBDelayAnaItem csfbItem)
        {
            csfbItem.ARFCNList = new List<uint>();

            MessageWithSource msg = ((MessageWithSource)csfbItem.msgList[0]);
            MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Source.Length, msg.ID);

            setReDirectCarrierInfo(csfbItem);

            setStartingARFCN(csfbItem);

            setBandIndicator(csfbItem);

            setFollowingARFCNs(csfbItem);

            setARFCNTotal(csfbItem);
        }

        private static void setReDirectCarrierInfo(ZTLTECSFBDelayAnaItem csfbItem)
        {
            uint redirectedCarrierInfo = 0;
            if (MessageDecodeHelper.GetSingleUInt("lte-rrc.redirectedCarrierInfo", ref redirectedCarrierInfo))
            {
                if ((int)redirectedCarrierInfo == (int)ECarrierInfo.eutra)
                {
                    csfbItem.ReDirectCarrierInfo = "eutra";
                }
                else if ((int)redirectedCarrierInfo == (int)ECarrierInfo.geran)
                {
                    csfbItem.ReDirectCarrierInfo = "geran";
                }
                else
                {
                    csfbItem.ReDirectCarrierInfo = redirectedCarrierInfo.ToString();
                }
            }
        }

        private static void setStartingARFCN(ZTLTECSFBDelayAnaItem csfbItem)
        {
            uint startingARFCN = 0;
            if (MessageDecodeHelper.GetSingleUInt("lte-rrc.startingARFCN", ref startingARFCN))
            {
                csfbItem.ARFCNList.Add(startingARFCN);
                csfbItem.StartingARFCN = startingARFCN.ToString();
            }
        }

        private void setBandIndicator(ZTLTECSFBDelayAnaItem csfbItem)
        {
            uint bandIndicator = 0;
            if (MessageDecodeHelper.GetSingleUInt("lte-rrc.bandIndicator", ref bandIndicator))
            {
                if (bandIndicator == 0)
                {
                    csfbItem.BandIndicator = "DSC1800";
                }
                else
                {
                    csfbItem.BandIndicator = bandIndicator.ToString();
                }
            }
        }

        private void setFollowingARFCNs(ZTLTECSFBDelayAnaItem csfbItem)
        {
            uint followingARFCNs = 0;
            if (MessageDecodeHelper.GetSingleUInt("lte-rrc.followingARFCNs", ref followingARFCNs))
            {
                if (followingARFCNs == 0)
                {
                    csfbItem.FollowingARFCNs = "explicitListOfARFCN";
                }
                else
                {
                    csfbItem.FollowingARFCNs = followingARFCNs.ToString();
                }
            }
        }

        private void setARFCNTotal(ZTLTECSFBDelayAnaItem csfbItem)
        {
            uint arfcnCount = 0;
            if (MessageDecodeHelper.GetSingleUInt("lte-rrc.explicitListOfARFCNs", ref arfcnCount) && arfcnCount > 0)
            {
                uint[] freqArray = new uint[arfcnCount];
                if (MessageDecodeHelper.GetMultiUInt("lte-rrc.ARFCN_ValueGERAN", ref freqArray, (int)arfcnCount))
                {
                    csfbItem.ARFCNList.AddRange(freqArray);
                    StringBuilder sb = new StringBuilder(csfbItem.ARFCNTotal);
                    for (int i = 0; i < freqArray.Length; i++)
                    {
                        sb.Append("|" + freqArray[i].ToString());
                    }
                    csfbItem.ARFCNTotal = sb.ToString();
                }
            }
        }

        protected string getLTECellNameByEvt(Event evt)
        {
            LTECell curCell = CellManager.GetInstance().GetLTECell(evt.DateTime, (int)evt["LAC"],(int)evt["CI"]);
            if(curCell != null)
            {
                return curCell.Name;
            }
            else
            {
                if ((int)evt["LAC"] == -1)
                {
                    return "";
                }
                return evt["LAC"].ToString() + "_" + evt["CI"].ToString();
            }
        }

        private void getCsfbCellInfo(ref ZTLTECSFBDelayAnaItem csfbItem)
        {
            if (csfbItem.TpEnd["lte_gsm_SC_BCCH"] != null)  //判断回落后的网络
            {
                csfbItem.CsfbType = "LTE-GSM";
                getGSMCellInfo(ref csfbItem);
            }
            else if (csfbItem.TpEnd["lte_td_SC_UARFCN"] != null)
            {
                csfbItem.CsfbType = "LTE-TD";
                csfbItem.CellNameEnd = getTDCellNameByEvt(csfbItem.EvtCsfbProceeding);
            }
        }

        private void getGSMCellInfo(ref ZTLTECSFBDelayAnaItem csfbItem)
        {
            Event evt = csfbItem.EvtCsfbProceeding;
            Cell curCell = CellManager.GetInstance().GetCell(evt.DateTime, (ushort)(int)evt["LAC"], (ushort)(int)evt["CI"]);
            if(curCell != null)
            {
                csfbItem.CellNameEnd = curCell.Name;
            }
            else
            {
                if ((int)evt["LAC"] == -1)
                {
                    csfbItem.CellNameEnd = "";
                }
                csfbItem.CellNameEnd = evt["LAC"].ToString() + "_" + evt["CI"].ToString();
            }

            TestPoint tp = csfbItem.TpBeforeSetup;
            if (tp != null)
            {
                csfbItem.CellEndBcch = (short?)tp["lte_gsm_SC_BCCH"];
                csfbItem.CellEndRxlev = (short?)tp["lte_gsm_DM_RxLevSub"];
                csfbItem.CellEndRxqual = (byte?)tp["lte_gsm_DM_RxQualSub"];
                if (csfbItem.CellEndRxlev != null)
                {
                    csfbItem.IsBackOnBestBcch = "是";

                    short? nRxlevMax = (short?)tp["lte_gsm_NC_RxLev", 0];
                    short? nArfcnMax = (short?)tp["lte_gsm_NC_BCCH", 0];
                    getNcellData(tp, ref nRxlevMax, ref nArfcnMax);

                    setCallEnd(csfbItem, nRxlevMax, nArfcnMax);
                }
            }

        }

        private static void getNcellData(TestPoint tp, ref short? nRxlevMax, ref short? nArfcnMax)
        {
            if (nRxlevMax != null)
            {
                for (int i = 1; i < 10; i++)
                {
                    short? nRxlevCur = (short?)tp["lte_gsm_NC_RxLev", i];
                    if (nRxlevCur == null)
                    {
                        break;
                    }
                    if (nRxlevCur > nRxlevMax)
                    {
                        nRxlevMax = nRxlevCur;
                        nArfcnMax = (short?)tp["lte_gsm_NC_BCCH", i];
                    }
                }
            }
        }

        private void setCallEnd(ZTLTECSFBDelayAnaItem csfbItem, short? nRxlevMax, short? nArfcnMax)
        {
            if (nRxlevMax != null && nRxlevMax > csfbItem.CellEndRxlev)
            {
                csfbItem.IsBackOnBestBcch = "否";
                csfbItem.CellEndRxlevMax = nRxlevMax;
                csfbItem.CellEndBcchBest = nArfcnMax;

                if (nArfcnMax != null)
                {
                    if (csfbItem.ARFCNList.Contains((uint)nArfcnMax))
                    {
                        csfbItem.HasSetBestBcch = "是";
                    }
                    else
                    {
                        csfbItem.HasSetBestBcch = "否";
                    }
                }
            }
            else
            {
                csfbItem.HasSetBestBcch = "是";
                csfbItem.CellEndRxlevMax = csfbItem.CellEndRxlev;
                csfbItem.CellEndBcchBest = csfbItem.CellEndBcch;
            }
        }

        private string getTDCellNameByEvt(Event evt)
        {
            TDCell curCell = CellManager.GetInstance().GetTDCell(evt.DateTime, (int)evt["LAC"],(int)evt["CI"]);
            if(curCell != null)
            {
                return curCell.Name;
            }
            else
            {
                if ((int)evt["LAC"] == -1)
                {
                    return "";
                }
                return evt["LAC"].ToString() + "_" + evt["CI"].ToString();
            }
        }

         /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTLTECSFBDelayAnaListForm).FullName);
            ZTLTECSFBDelayAnaListForm lteCsfbDelayAnaListForm = obj == null ? null : obj as ZTLTECSFBDelayAnaListForm;
            if (lteCsfbDelayAnaListForm == null || lteCsfbDelayAnaListForm.IsDisposed)
            {
                lteCsfbDelayAnaListForm = new ZTLTECSFBDelayAnaListForm(MainModel);
            }

            lteCsfbDelayAnaListForm.FillData(resultList);
            if (!lteCsfbDelayAnaListForm.Visible)
            {
                lteCsfbDelayAnaListForm.Show(MainModel.MainForm);
            }
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        protected Comparer comparer = new Comparer();
        protected class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class ZTLTECSFBDelayFileItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }
        public string FileType { get; set; }

        public List<ZTLTECSFBDelayAnaItem> csfbList { get; set; } = new List<ZTLTECSFBDelayAnaItem>();

        public ZTLTECSFBDelayFileItem(string fileName, int momtFlag)
        {
            FileName = fileName;

            if(momtFlag == (int)MoMtFile.MoFlag)
            {
                FileType = "主叫";
            }
            else if(momtFlag == (int)MoMtFile.MtFlag)
            {
                FileType = "被叫";
            }
        }
    }

    public class ZTLTECSFBDelayAnaItem
    {
        public int SN { get; set; }
        public string CsfbResult { get; set; }          //成功 or 失败
        public string CsfbType { get; set; }            //LTE-GSM or LTE-TD

        public Event EvtCsfbRequest { get; set; }       //csfb request 
        public Event EvtCsfbConnRelease { get; set; }   //csfb conn release
        public Event EvtCsfbProceeding { get; set; }    //csfb proceeding 

        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public string IsTAUpdate { get; set; }           //是否有TAC更新

        public TestPoint TpBegin { get; set; }
        public TestPoint TpEnd { get; set; }
        public TestPoint TpBeforeSetup { get; set; }

        public List<MasterCom.RAMS.Model.Message> msgList{ get; set; }

        public bool IsGotBegin { get; set; }

        public string CellNameBegin { get; set; }
        public string CellNameEnd { get; set; }
        public int? CellEndBcch { get; set; }
        public float? CellEndRxlev { get; set; }
        public float? CellEndRxqual { get; set; }
        public float? CellEndRxlevMax { get; set; }
        public int? CellEndBcchBest { get; set; }
        public string IsBackOnBestBcch { get; set; }
        public string HasSetBestBcch { get; set; }

        public string MsgNameBegin { get; set; }
        public string MsgNameEnd { get; set; }

        public string MsgTimeBegin { get; set; }
        public string MsgTimeEnd { get; set; }

        public string CsfbDelayTime { get; set; }

        //从RRC Connection Release中解析
        public string ReDirectCarrierInfo { get; set; }
        public string StartingARFCN { get; set; }
        public string BandIndicator { get; set; }
        public string FollowingARFCNs { get; set; }
        public string ARFCNTotal { get; set; }

        public List<uint> ARFCNList { get; set; }

        public ZTLTECSFBDelayAnaItem()
        {
            IsTAUpdate = "否";
            IsGotBegin = false;
            msgList = new List<Message>();
            ARFCNList = new List<uint>();
        }

        public void AddBeginEvtInfo(TestPoint tpBegin, Event evtCsfbRequest)
        {
            TpBegin = tpBegin;
            EvtCsfbRequest = evtCsfbRequest;
            IsGotBegin = true;
        }

        public void AddEndEvtInfo(TestPoint tpEnd, Event evtCsfbProceeding)
        {
            TpEnd = tpEnd;
            EvtCsfbProceeding = evtCsfbProceeding;
        }

        public void AddMidEvtInfo(Event evtCsfbConnRelease)
        {
            EvtCsfbConnRelease = evtCsfbConnRelease;
        }
    }

    enum EnumCsfbEvent
    {
        MOCSFBRequest = 877,
        MOCSFBLTERelease = 878,
        MOCSFBProceeding = 880,
        MOCSFBSuccess = 881,
        MOCSFBFailure = 882,

        MTCSFBRequest = 885,
        MTCSFBLTERelease = 886,
        MTCSFBProceeding = 888,
        MTCSFBSuccess = 889,
        MTCSFBFailure = 890,

        TrackAreaUpdateAttempt = 852,
        TrackAreaUpdateSuccess = 853,
        TrackAreaUpdateFail = 854,

        LocationUpdateRequestLte_Gsm = 1113,
        LocationUpdateSuccessLte_Gsm = 1114,
        LocationUpdateRequestGsm = 19,
        LocationUpdateAcceptGsm = 20,
    }
}
