﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTCellCheckInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition1 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition2 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition3 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode2 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition4 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition5 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition6 = new DevExpress.XtraGrid.StyleFormatCondition();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CQTCellCheckInfoForm));
            this.gridColumn_Percent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridViewFile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCoSampleNum_File = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAllSampleNum_File = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayFiles = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewPnt = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnCQTName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCoSampleNum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAllSampleNum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridViewCell = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAllSampleNum_Cell = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewFile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewPnt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).BeginInit();
            this.SuspendLayout();
            // 
            // gridColumn_Percent
            // 
            this.gridColumn_Percent.Caption = "吻合度(%)";
            this.gridColumn_Percent.FieldName = "Percent";
            this.gridColumn_Percent.Name = "gridColumn_Percent";
            this.gridColumn_Percent.Visible = true;
            this.gridColumn_Percent.VisibleIndex = 3;
            // 
            // gridViewFile
            // 
            this.gridViewFile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnFileName,
            this.gridColumnCoSampleNum_File,
            this.gridColumnAllSampleNum_File,
            this.gridColumn_Percent});
            this.gridViewFile.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None;
            styleFormatCondition1.Appearance.ForeColor = System.Drawing.Color.Red;
            styleFormatCondition1.Appearance.Options.UseForeColor = true;
            styleFormatCondition1.Column = this.gridColumn_Percent;
            styleFormatCondition1.Condition = DevExpress.XtraGrid.FormatConditionEnum.Less;
            styleFormatCondition1.Value1 = 80;
            styleFormatCondition2.Appearance.ForeColor = System.Drawing.Color.Yellow;
            styleFormatCondition2.Appearance.Options.UseForeColor = true;
            styleFormatCondition2.Column = this.gridColumn_Percent;
            styleFormatCondition2.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression;
            styleFormatCondition2.Expression = "80 <= [Percent] And [Percent] <= 90";
            styleFormatCondition2.Value1 = 80;
            styleFormatCondition2.Value2 = 90;
            styleFormatCondition3.Appearance.ForeColor = System.Drawing.Color.Green;
            styleFormatCondition3.Appearance.Options.UseForeColor = true;
            styleFormatCondition3.Column = this.gridColumn_Percent;
            styleFormatCondition3.Condition = DevExpress.XtraGrid.FormatConditionEnum.Greater;
            styleFormatCondition3.Value1 = 90;
            this.gridViewFile.FormatConditions.AddRange(new DevExpress.XtraGrid.StyleFormatCondition[] {
            styleFormatCondition1,
            styleFormatCondition2,
            styleFormatCondition3});
            this.gridViewFile.GridControl = this.gridControl1;
            this.gridViewFile.Name = "gridViewFile";
            this.gridViewFile.OptionsBehavior.Editable = false;
            this.gridViewFile.OptionsDetail.ShowDetailTabs = false;
            this.gridViewFile.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名称";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 0;
            // 
            // gridColumnCoSampleNum_File
            // 
            this.gridColumnCoSampleNum_File.Caption = "吻合采样点数";
            this.gridColumnCoSampleNum_File.FieldName = "CoSampleNum";
            this.gridColumnCoSampleNum_File.Name = "gridColumnCoSampleNum_File";
            this.gridColumnCoSampleNum_File.Visible = true;
            this.gridColumnCoSampleNum_File.VisibleIndex = 1;
            // 
            // gridColumnAllSampleNum_File
            // 
            this.gridColumnAllSampleNum_File.Caption = "总采样点数";
            this.gridColumnAllSampleNum_File.FieldName = "AllSampleNum";
            this.gridColumnAllSampleNum_File.Name = "gridColumnAllSampleNum_File";
            this.gridColumnAllSampleNum_File.Visible = true;
            this.gridColumnAllSampleNum_File.VisibleIndex = 2;
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.contextMenuStrip;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gridViewFile;
            gridLevelNode2.LevelTemplate = this.gridViewCell;
            gridLevelNode2.RelationName = "CqtCellInfoList";
            gridLevelNode1.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode2});
            gridLevelNode1.RelationName = "CqtFileInfoList";
            this.gridControl1.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gridViewPnt;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(692, 389);
            this.gridControl1.TabIndex = 0;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewPnt,
            this.gridViewCell,
            this.gridViewFile});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayFiles});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(134, 26);
            // 
            // miReplayFiles
            // 
            this.miReplayFiles.Name = "miReplayFiles";
            this.miReplayFiles.Size = new System.Drawing.Size(133, 22);
            this.miReplayFiles.Text = "回放文件...";
            this.miReplayFiles.Click += new System.EventHandler(this.miReplayFiles_Click);
            // 
            // gridViewPnt
            // 
            this.gridViewPnt.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnCQTName,
            this.gridColumnCoSampleNum,
            this.gridColumnAllSampleNum,
            this.gridColumnPercent});
            styleFormatCondition4.Appearance.ForeColor = System.Drawing.Color.Red;
            styleFormatCondition4.Appearance.Options.UseForeColor = true;
            styleFormatCondition4.Column = this.gridColumnPercent;
            styleFormatCondition4.Condition = DevExpress.XtraGrid.FormatConditionEnum.Less;
            styleFormatCondition4.Value1 = 80;
            styleFormatCondition5.Appearance.ForeColor = System.Drawing.Color.Yellow;
            styleFormatCondition5.Appearance.Options.UseForeColor = true;
            styleFormatCondition5.Column = this.gridColumnPercent;
            styleFormatCondition5.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression;
            styleFormatCondition5.Expression = "80 <= [Percent] And [Percent] <= 90";
            styleFormatCondition5.Value1 = 80;
            styleFormatCondition5.Value2 = 90;
            styleFormatCondition6.Appearance.ForeColor = System.Drawing.Color.Green;
            styleFormatCondition6.Appearance.Options.UseForeColor = true;
            styleFormatCondition6.Column = this.gridColumnPercent;
            styleFormatCondition6.Condition = DevExpress.XtraGrid.FormatConditionEnum.Greater;
            styleFormatCondition6.Value1 = 90;
            this.gridViewPnt.FormatConditions.AddRange(new DevExpress.XtraGrid.StyleFormatCondition[] {
            styleFormatCondition4,
            styleFormatCondition5,
            styleFormatCondition6});
            this.gridViewPnt.GridControl = this.gridControl1;
            this.gridViewPnt.Name = "gridViewPnt";
            this.gridViewPnt.OptionsBehavior.Editable = false;
            this.gridViewPnt.OptionsDetail.ShowDetailTabs = false;
            this.gridViewPnt.OptionsView.ShowGroupPanel = false;
            this.gridViewPnt.DoubleClick += new System.EventHandler(this.gridViewPnt_DoubleClick);
            // 
            // gridColumnCQTName
            // 
            this.gridColumnCQTName.Caption = "CQT地点名称";
            this.gridColumnCQTName.FieldName = "CqtName";
            this.gridColumnCQTName.Name = "gridColumnCQTName";
            this.gridColumnCQTName.Visible = true;
            this.gridColumnCQTName.VisibleIndex = 0;
            // 
            // gridColumnCoSampleNum
            // 
            this.gridColumnCoSampleNum.Caption = "吻合采样点数";
            this.gridColumnCoSampleNum.FieldName = "CoSampleNum";
            this.gridColumnCoSampleNum.Name = "gridColumnCoSampleNum";
            this.gridColumnCoSampleNum.Visible = true;
            this.gridColumnCoSampleNum.VisibleIndex = 1;
            // 
            // gridColumnAllSampleNum
            // 
            this.gridColumnAllSampleNum.Caption = "总采样点数";
            this.gridColumnAllSampleNum.FieldName = "AllSampleNum";
            this.gridColumnAllSampleNum.Name = "gridColumnAllSampleNum";
            this.gridColumnAllSampleNum.Visible = true;
            this.gridColumnAllSampleNum.VisibleIndex = 2;
            // 
            // gridColumnPercent
            // 
            this.gridColumnPercent.Caption = "吻合度(%)";
            this.gridColumnPercent.FieldName = "Percent";
            this.gridColumnPercent.Name = "gridColumnPercent";
            this.gridColumnPercent.Visible = true;
            this.gridColumnPercent.VisibleIndex = 3;
            // 
            // gridViewCell
            // 
            this.gridViewCell.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnCellName,
            this.gridColumnLAC,
            this.gridColumnCI,
            this.gridColumnAllSampleNum_Cell});
            this.gridViewCell.GridControl = this.gridControl1;
            this.gridViewCell.Name = "gridViewCell";
            this.gridViewCell.OptionsBehavior.Editable = false;
            this.gridViewCell.OptionsDetail.ShowDetailTabs = false;
            this.gridViewCell.OptionsView.ShowGroupPanel = false;
            this.gridViewCell.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.gridViewCell_CustomDrawCell);
            this.gridViewCell.DoubleClick += new System.EventHandler(this.gridViewCell_DoubleClick);
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "小区名称";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 0;
            // 
            // gridColumnLAC
            // 
            this.gridColumnLAC.Caption = "LAC";
            this.gridColumnLAC.FieldName = "LAC";
            this.gridColumnLAC.Name = "gridColumnLAC";
            this.gridColumnLAC.Visible = true;
            this.gridColumnLAC.VisibleIndex = 1;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "CI";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 2;
            // 
            // gridColumnAllSampleNum_Cell
            // 
            this.gridColumnAllSampleNum_Cell.Caption = "总采样点数";
            this.gridColumnAllSampleNum_Cell.FieldName = "AllSampleNum";
            this.gridColumnAllSampleNum_Cell.Name = "gridColumnAllSampleNum_Cell";
            this.gridColumnAllSampleNum_Cell.Visible = true;
            this.gridColumnAllSampleNum_Cell.VisibleIndex = 3;
            // 
            // CQTCellCheckInfoForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("CQTCellCheckInfoForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(692, 389);
            this.Controls.Add(this.gridControl1);
            this.Name = "CQTCellCheckInfoForm";
            this.Text = "CQT小区核查列表";
            ((System.ComponentModel.ISupportInitialize)(this.gridViewFile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewPnt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewPnt;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCQTName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCoSampleNum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAllSampleNum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPercent;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewCell;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAllSampleNum_Cell;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewFile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCoSampleNum_File;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAllSampleNum_File;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn_Percent;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miReplayFiles;
    }
}