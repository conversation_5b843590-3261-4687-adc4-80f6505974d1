﻿using EvtEngineLib;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_DIY_LOG_EVENT = 0x70; 	//REQUEST
        public const byte REQTYPE_DIY_LOG_SAMPLE = 0x71;	//REQUEST
        public const byte REQTYPE_DIY_LOG_MSG = 0x72; 	//REQUEST
        public const byte REQTYPE_DIY_LOG_MSG_AllRTP = 0x73; 	//REQUEST
    }
    public static partial class ResponseType
    {

        public const byte RESTYPE_COLUMN_SAMPLE_CDMA_V = 0x20;
        public const byte RESTYPE_DIY_SAMPLE_CDMA_V = 0x21;

        public const byte RESTYPE_COLUMN_SAMPLE_CDMA_D = 0x22;
        public const byte RESTYPE_DIY_SAMPLE_CDMA_D = 0x23;

        public const byte RESTYPE_COLUMN_SAMPLE_TDSCDMA_V = 0x24;
        public const byte RESTYPE_DIY_SAMPLE_TDSCDMA_V = 0x25;

        public const byte RESTYPE_COLUMN_SAMPLE_TDSCDMA_D = 0x26;
        public const byte RESTYPE_DIY_SAMPLE_TDSCDMA_D = 0x27;

        public const byte RESTYPE_COLUMN_SAMPLE_WCDMA_V = 0x28;
        public const byte RESTYPE_DIY_SAMPLE_WCDMA_V = 0x29;

        public const byte RESTYPE_COLUMN_SAMPLE_WCDMA_D = 0x2a;
        public const byte RESTYPE_DIY_SAMPLE_WCDMA_D = 0x2b;

        public const byte RESTYPE_COLUMN_SAMPLE_CDMA2000_D = 0x2c;
        public const byte RESTYPE_DIY_SAMPLE_CDMA2000_D = 0x2d;

        public const byte RESTYPE_COLUMN_SAMPLE_TDSCDMA_SCAN = 0x2e;
        public const byte RESTYPE_DIY_SAMPLE_TDSCDMA_SCAN = 0x2f;

        public const byte RESTYPE_COLUMN_SAMPLE_CDMA2000_V = 0x30;
        public const byte RESTYPE_DIY_SAMPLE_CDMA2000_V = 0x31;

        public const byte RESTYPE_COLUMN_SAMPLE_GSM_SCAN = 0x32;
        public const byte RESTYPE_DIY_SAMPLE_GSM_SCAN = 0x33;

        public const byte RESTYPE_COLUMN_SAMPLE_WCDMA_SCAN = 0x34;
        public const byte RESTYPE_DIY_SAMPLE_WCDMA_SCAN = 0x35;

        public const byte RESTYPE_COLUMN_SAMPLE_GSM_V = 0x36;
        public const byte RESTYPE_DIY_SAMPLE_GSM_V = 0x37;

        public const byte RESTYPE_COLUMN_SAMPLE_GSM_D = 0x38;
        public const byte RESTYPE_DIY_SAMPLE_GSM_D = 0x39;

        //events
        public const byte RESTYPE_COLUMN_L0G_EVENT = 0x20;
        public const byte RESTYPE_DIY_LOG_EVENT = 0x21;

        public const byte RESTYPE_DIY_LOG_EVENT_NR = 0x22;

        //message
        public const byte RESTYPE_COLUMN_L0G_MSG = 0x20;
        public const byte RESTYPE_DIY_LOG_MSG = 0x21;

        public const byte RESTYPE_COLUMN_SAMPLE_GSM_MTR = 0x3a;
        public const byte RESTYPE_DIY_SAMPLE_GSM_MTR = 0x3b;

        public const byte RESTYPE_COLUMN_SAMPLE_WLAN = 0x3e;
        public const byte RESTYPE_DIY_SAMPLE_WLAN = 0x3f;

        public const byte RESTYPE_COLUMN_SAMPLE_LTE = 0x40;
        public const byte RESTYPE_DIY_SAMPLE_LTE = 0x41;

        public const byte RESTYPE_COLUMN_SAMPLE_LTE_SCAN = 0x42;
        public const byte RESTYPE_DIY_SAMPLE_LTE_SCAN = 0x43;

        public const byte RESTYPE_COLUMN_SAMPLE_CW_SCAN = 0x44;
        public const byte RESTYPE_DIY_SAMPLE_CW_SCAN = 0x45;

        public const byte RESTYPE_COLUMN_SAMPLE_FREQSPECTURM_SCAN = 0x46;
        public const byte RESTYPE_DIY_SAMPLE_FREQSPECTURM_SCAN = 0x47;
        public const byte FILE_SAMPLE_LTE_UEP = 0x49;
        public const byte RESTYPE_DIY_SAMPLE_LTE_FDD = 0x4A;
        public const byte RESTYPE_DIY_SAMPLE_SIGNAL = 0x4B;

        public const byte RESTYPE_COLUMN_SAMPLE_NBIOT_SCAN = 0x4C;
        public const byte RESTYPE_DIY_SAMPLE_NBIOT_SCAN = 0x4D;

        public const byte RESTYPE_COLUMN_SAMPLE_NR = 0x4E;
        public const byte RESTYPE_DIY_SAMPLE_NR = 0x4F;

        public const byte RESTYPE_COLUMN_SAMPLE_NR_SCAN = 0x50;
        public const byte RESTYPE_DIY_SAMPLE_NR_SCAN = 0x51;
    }

    public delegate bool ReplayEventFilterDelegate(Event evt);

    public class DIYReplayFileQuery : QueryBase
    {
        public ReplayEventFilterDelegate ReplayEventFilter { get; set; }
        public DIYReplayFileQuery(MainModel mainModel)
            : base(mainModel)
        {
            IsMutCitys = false;
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected bool IsAddSampleToDTDataManager = true;//是否把采样点信息放到DTDataManager
        public void SetIsAddSampleToDTDataManager(bool value)
        {
            IsAddSampleToDTDataManager = value;
        }

        protected bool IsAddMessageToDTDataManager = true;//是否把层三信息放到DTDataManager
        public void SetIsAddMessageToDTDataManager(bool value)
        {
            IsAddMessageToDTDataManager = value;
        }

        protected bool isAutoLoadCQTPicture = false;//是否自动加载集团CQT图片
        public bool IsAutoLoadCQTPicture
        {
            get { return isAutoLoadCQTPicture; }
        }

        protected bool isAddAllOtherParameter = true;
        /// <summary>
        /// 回放时是否加载全部其他参数信息
        /// true 加载指标列字段包含的所有指标
        /// false 仅加载已添加的指标列
        /// </summary>
        public void SetIsAddAllOtherParameter(bool value)
        {
            isAddAllOtherParameter = value;
        }

        protected virtual void doWithDTData(TestPoint tp)
        {
        }
        protected virtual void doWithDTData(MasterCom.RAMS.Model.Message msg)
        {
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11029, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected virtual void fillContentNeeded_Sample(Package package)
        {
            if (replayContentOption == null)
            {
                throw (new Exception("没有选择的参数指标！"));
            }
            List<ColumnDefItem> colDefList = getNeededColumnDefList(replayContentOption);
            StringBuilder sbuilder = new StringBuilder();
            Dictionary<string, bool> tempDic = new Dictionary<string, bool>();
            for (int i = 0; i < colDefList.Count; i++)
            {
                ColumnDefItem sid = colDefList[i];
                string id = sid.GetTriIdStrIgnoreTable();
                if (tempDic.ContainsKey(id))
                {
                    continue;
                }
                tempDic[id] = true;
                sbuilder.Append(id);
                sbuilder.Append(",");
            }
            package.Content.AddParam(sbuilder.ToString().TrimEnd(','));
        }
        protected void fillContentNeeded_Event(Package package)
        {
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,1,43,");
            sbuilder.Append("0,2,43,");
            sbuilder.Append("0,3,43,");
            sbuilder.Append("0,4,43,");
            sbuilder.Append("0,5,43,");
            sbuilder.Append("0,6,43,");
            sbuilder.Append("0,7,43,");
            sbuilder.Append("0,8,43,");
            sbuilder.Append("0,9,43,");
            sbuilder.Append("0,10,43,");
            sbuilder.Append("0,11,43,");
            sbuilder.Append("0,12,43,");
            sbuilder.Append("0,13,43,");
            sbuilder.Append("0,14,43,");
            sbuilder.Append("0,15,43,");
            sbuilder.Append("0,16,43,");
            sbuilder.Append("0,17,43,");
            sbuilder.Append("0,18,43,");
            sbuilder.Append("0,19,43,");
            sbuilder.Append("0,20,43,");
            sbuilder.Append("0,21,43,");
            sbuilder.Append("0,22,43,");
            sbuilder.Append("0,23,43,");
            sbuilder.Append("0,24,43,");
            sbuilder.Append("0,25,43,");
            sbuilder.Append("0,26,43");
            package.Content.AddParam(sbuilder.ToString());
        }
        protected void fillContentNeeded_Message(Package package, bool needL3Hex)
        {
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,1,45,");
            sbuilder.Append("0,2,45,");
            sbuilder.Append("0,3,45,");
            sbuilder.Append("0,4,45,");
            sbuilder.Append("0,5,45,");
            sbuilder.Append("0,6,45,");
            sbuilder.Append("0,7,45,");
            if (needL3Hex)
            {
                sbuilder.Append("1,8,45,");
            }
            sbuilder.Append("2,9,45");
            package.Content.AddParam(sbuilder.ToString());
        }

        /// <summary>
        /// 获取唯一的查询指标列名
        /// </summary>
        /// <param name="replayOption"></param>
        /// <returns></returns>
        private Dictionary<string, string> getUniqueColumnDefDic(DIYReplayContentOption replayOption)
        {
            Dictionary<string, string> columnsDic = new Dictionary<string, string>();
            foreach (ColumnDefItem columnDef in replayOption.SampleColumns)
            {
                if (columnDef == null)
                {
                    continue;
                }
                //相同名称指标只保留一个,避免循环次数过多
                if (!columnsDic.ContainsKey(columnDef.showName))
                {
                    columnsDic[columnDef.showName] = columnDef.showName;
                }
            }
            return columnsDic;
        }

        private List<ColumnDefItem> getNeededColumnDefList(DIYReplayContentOption replayOption)
        {
            List<ColumnDefItem> retList = new List<ColumnDefItem>();
            Dictionary<string, ColumnDefItem> triIdTofixColumnsDic = new Dictionary<string, ColumnDefItem>();
            Dictionary<string, ColumnDefItem> tbIdAndImgIdToNonFixColumnsDic = new Dictionary<string, ColumnDefItem>();
            foreach (ColumnDefItem columnDef in replayOption.SampleColumns)
            {
                if (columnDef == null)
                {
                    continue;
                }
                if (columnDef.fix)
                {
                    triIdTofixColumnsDic[columnDef.GetTriIdStr()] = columnDef;
                }
                else
                {
                    string colDicKey = columnDef.tableName + "," + columnDef.imgID;
                    tbIdAndImgIdToNonFixColumnsDic[colDicKey] = columnDef;
                }
            }
            foreach (ColumnDefItem col in triIdTofixColumnsDic.Values)
            {
                retList.Add(col);
            }
            foreach (ColumnDefItem col in tbIdAndImgIdToNonFixColumnsDic.Values)
            {
                retList.Add(col);
            }
            return retList;
        }
        DIYReplayOptionDlg replayOptionDlg = null;
        protected DIYReplayContentOption replayContentOption = null;
        protected virtual DIYReplayContentOption getDIYReplayContent()
        {
            if (replayOptionDlg == null)
            {
                replayOptionDlg = new DIYReplayOptionDlg();
                replayOptionDlg.InitLoadInfo("", "隐藏"); //modified by yht dont show 隐藏
            }
            if (Condition.FileInfos.Count > 0)
            {
                int svtype = Condition.FileInfos[0].ServiceType;
                replayOptionDlg.FillCurrentServiceType(svtype);
            }
            if (replayOptionDlg.ShowDialog() != DialogResult.OK)
            {
                return null;
            }
            isAutoLoadCQTPicture = replayOptionDlg.IsAutoLoadCQTPicture;
            return replayOptionDlg.GetSelectedReplayOption();
        }

        protected void setColumns(DIYReplayContentOption option, string columnsName)
        {
            List<ColumnDefItem> columns = InterfaceManager.GetInstance().GetColumnDefByShowName(columnsName);
            if (columns != null && columns.Count > 0)
            {
                option.SampleColumns.AddRange(columns);
            }
        }

        protected override void query()//回放查询入口
        {
            replayContentOption = getDIYReplayContent();//获取查询指标（内容）
            if (replayContentOption == null)
            {
                return;
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, condition.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = "正在查询...";
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.IsDrawEventResult = false;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                MainModel.IsFileReplayByMTRMode = condition.isMTRMode;
                MainModel.IsFileReplayByMTRToLogMode = condition.isMTRToLogMode;
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                doPostReplayAction();
                if (replayContentOption.DefaultSerialThemeName != null)
                {
                    MainModel.FireSetDefaultMapSerialTheme(replayContentOption.DefaultSerialThemeName);
                }

            }
            finally
            {
                fireShowResult();
                MainModel.FireDTDataChanged(this);
                clientProxy.Close();
            }
        }

        protected virtual void fireShowResult()
        {

        }

        protected virtual void doPostReplayAction()
        {

        }

        //MTR关联回放使用
        protected int fileIdMTR = 0;
        protected DTDataHeader headerMTR = null;
        protected bool canGetHeaderMTR = false;
        protected int fileIndex = 0;
        protected int fileOffsetTimeMS = 0;
        public bool IsMutCitys { get; set; }

        protected virtual void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int index = 0;
                MainModel.SelectedFileInfo = Condition.FileInfos[0];
                foreach (MasterCom.RAMS.Model.FileInfo fileInfo in Condition.FileInfos)
                {
                    if (IsMutCitys)//支持同时回放多地市的文件
                    {
                        clientProxy.Close();
                        clientProxy = new ClientProxy();
                        if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName
                            , MainModel.User.Password, fileInfo.DistrictID) != ConnectResult.Success)
                        {
                            ErrorInfo = fileInfo.Name + " 回放失败，其余文件正在继续!!!";
                            continue;
                        }
                        package = clientProxy.Package;
                    }
                    setOriginTypeByFileType(fileInfo);
                    setMTRMode(index, fileInfo);
                    index++;
                    fileIndex++;
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "开始获取[" + fileInfo.Name + "]数据...";
                    queryReplayInfo(clientProxy, package, fileInfo);
                }
            }
            catch (DebugAssertException dbgE)
            {
                MessageBox.Show("Debug Assert 失败:" + dbgE.ToString());
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void setOriginTypeByFileType(FileInfo fileInfo)
        {
            if (fileInfo.FileTypeDescription.Contains("鼎利"))
            {//鼎利测试文件以左下角为坐标原点
                MainModel.CQTPlanImgOrigin = MainModel.OriginType.LeftBottom;
            }
            else if (fileInfo.FileTypeDescription.Contains("烽火"))
            {//烽火测试文件以左上角为坐标原点
                MainModel.CQTPlanImgOrigin = MainModel.OriginType.LeftTop;
            }
        }

        private void setMTRMode(int index, FileInfo fileInfo)
        {
            if (MainModel.IsFileReplayByMTRMode)
            {
                if (index == 1)
                {
                    fileIdMTR = fileInfo.ID;
                    canGetHeaderMTR = true;
                }
                if (index > 0)
                {
                    fileOffsetTimeMS = fileInfo.OffsetTimeMS;
                }
            }
            if (MainModel.IsFileReplayByMTRToLogMode && index == 0)
            {
                fileOffsetTimeMS = fileInfo.OffsetTimeMS;
            }
        }

        protected virtual void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)//选择好条件后查询回放信息
        {
            //sample
            prepareStatPackage_Sample_FileFilter(package, fileInfo);
            prepareStatPackage_Sample_SampleFilter(package);
            fillContentNeeded_Sample(package);
            clientProxy.Send();
            recieveInfo_Sample(clientProxy);

            //event
            if (replayContentOption.EventInclude)
            {
                prepareStatPackage_Event_FileFilter(package, fileInfo);
                prepareStatPackage_Event_EventFilter(package);
                fillContentNeeded_Event(package);
                clientProxy.Send();
                recieveInfo_Event(clientProxy);
            }
            //message
            if (replayContentOption.MessageInclude)
            {
                prepareStatPackage_Message_FileFilter(package, fileInfo);
                prepareStatPackage_Message_MessageFilter(package);
                fillContentNeeded_Message(package, replayContentOption.MessageL3HexCode);
                clientProxy.Send();
                recieveInfo_Message(clientProxy, replayContentOption.MessageL3HexCode);
            }
            //所有 RTP message
            if (replayContentOption.MessageIncludeAllRtp)
            {
                prepareStatPackage_AllRtpMessage_FileFilter(package, fileInfo);
                prepareStatPackage_Message_MessageFilter(package);
                fillContentNeeded_Message(package, replayContentOption.MessageL3HexCode);
                clientProxy.Send();
                recieveInfo_Message(clientProxy, replayContentOption.MessageL3HexCode);
            }

            MainModel.IsFusionInclude = replayContentOption.FusionInclude;
        }

        protected virtual void recieveInfo_Sample(ClientProxy clientProxy)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<ColumnDefItem> curSampleColumnDef = new List<ColumnDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;

            Dictionary<string, string> uniqueColumnDic = getUniqueColumnDefDic(replayContentOption);
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    dealHeaderInfo(clientProxy, headerManager, fileHeaderColumnDef, package);
                }
                else if (judgeSampleColumnDefType(package))
                {
                    curSampleColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curSampleColumnDef);
                }
                else if (judgeSampleResponseType(package))
                {
                    fillParams(headerManager, curSampleColumnDef, package, uniqueColumnDic);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

        private void dealHeaderInfo(ClientProxy clientProxy, DTDataHeaderManager headerManager,
            List<ColumnDefItem> fileHeaderColumnDef, Package package)
        {
            DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
            if (fileInfo != null)
            {
                DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                if (MainModel.IsFileReplayByMTRMode)
                {
                    if (canGetHeaderMTR)
                    {
                        headerMTR = fileInfo;
                        canGetHeaderMTR = false;
                    }
                    if (fileIndex > 1)
                    {
                        headerManager.AddDTDataHeader(headerMTR);
                    }
                }
            }
        }

        private bool judgeSampleColumnDefType(Package package)
        {
            return package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_GSM_V ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_GSM_D ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_CDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_CDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_TDSCDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_TDSCDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_WCDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_WCDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_CDMA2000_D ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_TDSCDMA_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_CDMA2000_V ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_GSM_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_WCDMA_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_GSM_MTR ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_WLAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_LTE ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_LTE_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_CW_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_FREQSPECTURM_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_NBIOT_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_NR ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_NR_SCAN ||
                   package.Content.Type == ResponseType.COLUMN_DEFINE;
        }

        private bool judgeSampleResponseType(Package package)
        {
            return package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_V ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_D ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_CDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_CDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_CDMA2000_D ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_CDMA2000_V ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_MTR ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_WLAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_LTE ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_LTE_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_CW_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_FREQSPECTURM_SCAN ||
                   package.Content.Type == ResponseType.FILE_SAMPLE_LTE_UEP ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_LTE_FDD ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_NBIOT_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_NR ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_NR_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_SIGNAL;
        }

        #region fillParams
        private void fillParams(DTDataHeaderManager headerManager, List<ColumnDefItem> curSampleColumnDef,
           Package package, Dictionary<string, string> uniqueColumnDic)
        {
            TestPoint tpPoint = getTestPoint(package);

            foreach (ColumnDefItem cdf in curSampleColumnDef)//回放指标名
            {
                switch (cdf.showName)
                {
                    case "ifileid":
                        fillIfileid(package, tpPoint);
                        break;
                    case "ilongitude":
                        fillIlongitude(package, tpPoint, cdf);
                        break;
                    case "ilatitude":
                        fillIlatitude(package, tpPoint, cdf);
                        break;
                    case "itime":
                        fillItime(package, tpPoint, cdf);
                        break;
                    case "isampleid":
                        fillIsampleid(package, tpPoint);
                        break;
                    case "bms":
                        fillBms(package, tpPoint, cdf);
                        break;
                    case "wtimems":
                        fillWtimems(package, tpPoint, cdf);
                        break;
                    default:
                        fillImageParams(package, uniqueColumnDic, tpPoint, cdf);
                        break;
                }
            }

            doAfterFillParams(headerManager, tpPoint);
        }

        private TestPoint getTestPoint(Package package)
        {
            TestPoint tpPoint = null;
            switch (package.Content.Type)
            {
                case ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_V:
                case ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_D:
                    tpPoint = new TDTestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_GSM_V:
                case ResponseType.RESTYPE_DIY_SAMPLE_GSM_D:
                    tpPoint = new TestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_V:
                case ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_D:
                    tpPoint = new WCDMATestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_CDMA_V:
                case ResponseType.RESTYPE_DIY_SAMPLE_CDMA_D:
                    tpPoint = new CDMATestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_CDMA2000_V:
                case ResponseType.RESTYPE_DIY_SAMPLE_CDMA2000_D:
                    tpPoint = new CDMATestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_GSM_SCAN:
                    tpPoint = new ScanTestPoint_G();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_GSM_MTR:
                    tpPoint = new GSMUplinkData();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_SCAN:
                    tpPoint = new ScanTestPoint_TD();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_SCAN:
                    tpPoint = new ScanTestPoint_W();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_WLAN:
                    tpPoint = new WLANTestPoint();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_LTE:
                    tpPoint = new LTETestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_LTE_SCAN:
                    tpPoint = new ScanTestPoint_LTE();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_CW_SCAN:
                    tpPoint = new ScanTestPoint_CW();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_FREQSPECTURM_SCAN:
                    tpPoint = new ScanTestPoint_FreqSpecturm();
                    break;
                case ResponseType.FILE_SAMPLE_LTE_UEP:
                    tpPoint = new LTEUepTestPoint();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_LTE_FDD:
                    tpPoint = new LTEFddTestPoint();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_SIGNAL:
                    tpPoint = new SignalTestPoint();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_NBIOT_SCAN:
                    tpPoint = new ScanTestPoint_NBIOT();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_NR:
                    tpPoint = new TestPoint_NR();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_NR_SCAN:
                    tpPoint = new ScanTestPoint_NR();
                    break;
            }

            return tpPoint;
        }

        private void fillIfileid(Package package, TestPoint tpPoint)
        {
            tpPoint.FileID = package.Content.GetParamInt();
            if (MainModel.IsFileReplayByMTRMode && package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_MTR)
            {
                tpPoint.FileID = fileIdMTR;
            }
        }

        private void fillIlongitude(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            Double curtmp = package.Content.GetParamDouble();
            if (curtmp.ToString() != cdf.nullValue)
            {
                tpPoint.Longitude = curtmp;
            }
        }

        private void fillIlatitude(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            Double curtmp = package.Content.GetParamDouble();
            if (curtmp.ToString() != cdf.nullValue)
            {
                tpPoint.Latitude = curtmp;
            }
        }

        private void fillItime(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            int curtmp = package.Content.GetParamInt();
            if (curtmp.ToString() != cdf.nullValue)
            {
                tpPoint.Time = curtmp;
            }
        }

        private void fillIsampleid(Package package, TestPoint tpPoint)
        {
            tpPoint.SN = package.Content.GetParamInt();
            if (MainModel.IsFileReplayByMTRMode && package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_MTR)
            {
                if (MainModel.DTDataManager.FileDataManagers.Count > 1)
                {
                    tpPoint.SN = MainModel.DTDataManager.FileDataManagers[1].TestPoints.Count + 1;
                }
                else
                {
                    tpPoint.SN = 1;
                }
            }
        }

        private void fillBms(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            if (cdf.vType == E_VType.E_Int)
            {
                tpPoint.MS = (byte)package.Content.GetParamInt();
            }
            else if (cdf.vType == E_VType.E_Short)
            {
                tpPoint.MS = (byte)package.Content.GetParamShort();
            }
            else if (cdf.vType == E_VType.E_Byte)
            {
                tpPoint.MS = package.Content.GetParamByte();
            }
            else
            {
                throw (new Exception("wtimems 类型配置异常！"));
            }
        }

        private void fillWtimems(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            if (cdf.vType == E_VType.E_Int)
            {
                tpPoint.Millisecond = (short)package.Content.GetParamInt();
            }
            else if (cdf.vType == E_VType.E_Short)
            {
                tpPoint.Millisecond = package.Content.GetParamShort();
            }
            else
            {
                throw (new Exception("wtimems 类型配置异常！"));
            }
            if (MainModel.IsFileReplayByMTRMode && package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_MTR)
            {
                tpPoint.Millisecond += (short)fileOffsetTimeMS;
            }
            if (MainModel.IsFileReplayByMTRToLogMode && package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_V)
            {
                tpPoint.Millisecond -= (short)fileOffsetTimeMS;
            }
        }

        #region fillImageParams
        private void fillImageParams(Package package, Dictionary<string, string> uniqueColumnDic,
            TestPoint tpPoint, ColumnDefItem cdf)
        {
            if (cdf.fix)
            {
                fillFixParam(package, tpPoint, cdf);
            }
            else// non fix
            {
                fillNonFixParam(package, uniqueColumnDic, tpPoint, cdf);
            }
        }

        private void fillFixParam(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            switch (cdf.vType)
            {
                case E_VType.E_Int:
                    {
                        int datav = package.Content.GetParamInt();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_Short:
                    {
                        short datav = package.Content.GetParamShort();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_UShort:
                    {
                        ushort datav = package.Content.GetParamUShort();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_Byte:
                    {
                        byte datav = package.Content.GetParamByte();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_Float:
                case E_VType.E_IntFloat:
                    {
                        int datav = package.Content.GetParamInt();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav / 1000.0f);
                    }
                    break;
                case E_VType.E_String:
                    {
                        string datav = package.Content.GetParamString();
                        if (datav != null && datav != "")
                        {
                            setValidTpParam(tpPoint, cdf, datav, datav);
                        }
                    }
                    break;
                case E_VType.E_UInt64:
                    {
                        UInt64 datav = package.Content.GetParamUInt64();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_Int64:
                    {
                        Int64 datav = package.Content.GetParamInt64();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                default:
                    package.Content.GetParamByte();
                    break;
            }
        }

        private void setValidTpParam<T>(TestPoint tpPoint, ColumnDefItem cdf, string datav, T defaultData)
        {
            if (datav != cdf.nullValue)
            {
                if (cdf.ShowArrayIndex == -1)
                {
                    tpPoint[cdf.showName] = defaultData;
                }
                else
                {
                    tpPoint[cdf.ShowArrayName, cdf.ShowArrayIndex] = defaultData;
                }
            }
        }

        private void fillNonFixParam(Package package, Dictionary<string, string> uniqueColumnDic,
            TestPoint tpPoint, ColumnDefItem cdf)
        {
            byte[] imgBytes = package.Content.GetParamBytes();
            if (cdf.vType != E_VType.E_ImgString)
            {
                fillImgStringParam(uniqueColumnDic, tpPoint, cdf, imgBytes);
            }
            else//string类型的image
            {
                fillStringParam(tpPoint, cdf, imgBytes);
            }
        }

        private void fillImgStringParam(Dictionary<string, string> uniqueColumnDic, TestPoint tpPoint,
            ColumnDefItem cdf, byte[] imgBytes)
        {
            List<ColumnDefItem> nonFixInColumns = InterfaceManager.GetInstance().GetOtherDefInSameColumn(cdf);
            foreach (ColumnDefItem nfColDef in nonFixInColumns)
            {
                if (!isAddAllOtherParameter && !uniqueColumnDic.ContainsKey(nfColDef.showName))
                {
                    //不查询所有其他参数时,如果参数名不是所选指标直接跳过,避免指标数据量过大导致内存增长过快
                    continue;
                }

                byte grpCount = imgBytes[0];
                byte grpAt = (byte)(nfColDef.paraID / 1000);
                if (grpAt > grpCount)
                {
                    continue;
                }
                int offsetx = initOffsetx(imgBytes, grpCount, grpAt);
                setImgStringParam(tpPoint, imgBytes, nfColDef, offsetx);
            }
        }

        private void setImgStringParam(TestPoint tpPoint, byte[] imgBytes, ColumnDefItem nfColDef, int offsetx)
        {
            byte ctOfNeib = imgBytes[offsetx++];
            byte szOfEachNeib = imgBytes[offsetx++];
            if (ctOfNeib > 0 && ctOfNeib <= nfColDef.maxArrCount)
            {
                for (int i = 0; i < ctOfNeib; i++)
                {
                    int startIdx = offsetx + i * szOfEachNeib + nfColDef.posFrom;
                    if (startIdx == imgBytes.Length)
                    {//一种可能情况：新加的参数，已入库的文件没有该参数。以新配置来解析image时，数组越界
                        break;
                    }
                    object arrObj = getArrObj(imgBytes, nfColDef, startIdx);
                    if (arrObj != null && arrObj.ToString() != nfColDef.nullValue)
                    {//有效值
                        tpPoint[nfColDef.showName, i] = arrObj;
                    }
                }
            }
        }

        private int initOffsetx(byte[] imgBytes, byte grpCount, byte grpAt)
        {
            int offsetx = 1;
            for (byte c = 0; c < grpCount && c < grpAt - 1; c++)
            {
                byte countOfArr = imgBytes[offsetx++];
                byte eachArrSize = imgBytes[offsetx++];
                offsetx += eachArrSize * countOfArr;
            }

            return offsetx;
        }

        private object getArrObj(byte[] imgBytes, ColumnDefItem nfColDef, int startIdx)
        {
            object arrObj = null;
            switch (nfColDef.vType)
            {
                case E_VType.E_Byte:
                    if (imgBytes.Length - startIdx >= 1)
                    {
                        arrObj = imgBytes[startIdx];
                    }
                    break;
                case E_VType.E_Int:
                    if (imgBytes.Length - startIdx >= 4)
                    {
                        int valueInt = BitConverter.ToInt32(imgBytes, startIdx);
                        arrObj = IPAddress.NetworkToHostOrder(valueInt);
                    }
                    break;
                case E_VType.E_Short:
                    if (imgBytes.Length - startIdx >= 2)
                    {
                        short valueShort = BitConverter.ToInt16(imgBytes, startIdx);
                        arrObj = IPAddress.NetworkToHostOrder(valueShort);
                    }
                    break;
                case E_VType.E_UShort:
                    if (imgBytes.Length - startIdx >= 2)
                    {
                        short valShort = BitConverter.ToInt16(imgBytes, startIdx);
                        arrObj = (ushort)IPAddress.NetworkToHostOrder(valShort);
                    }
                    break;
                case E_VType.E_Float:
                case E_VType.E_IntFloat:
                    if (imgBytes.Length - startIdx >= 4)
                    {
                        int valueFloatInt = BitConverter.ToInt32(imgBytes, startIdx);
                        if (valueFloatInt.ToString() != nfColDef.nullValue)
                        {
                            arrObj = IPAddress.NetworkToHostOrder(valueFloatInt) / 1000.0f;
                        }
                    }
                    break;
                case E_VType.E_String:
                    break;
                default:
                    break;
            }

            return arrObj;
        }

        private void fillStringParam(TestPoint tpPoint, ColumnDefItem cdf, byte[] imgBytes)
        {
            List<ColumnDefItem> nonFixInColumns = InterfaceManager.GetInstance().GetOtherDefInSameColumn(cdf);
            foreach (ColumnDefItem nfColDef in nonFixInColumns)
            {
                try
                {
                    byte grpCount = imgBytes[0];
                    if (grpCount == 0)
                    {
                        continue;
                    }
                    byte grpAt = (byte)(nfColDef.paraID / 1000);
                    if (grpAt > grpCount)
                    {
                        continue;
                    }
                    int offsetx = initOffsetx(imgBytes, grpCount, grpAt);
                    setStringParam(tpPoint, imgBytes, nfColDef, offsetx);
                }
                catch
                {
                    MessageBox.Show("解析数组出错！请核对配置或找入库人员处理！" + nfColDef.GetTriIdStr());
                }
            }
        }

        private void setStringParam(TestPoint tpPoint, byte[] imgBytes, ColumnDefItem nfColDef, int offsetx)
        {
            byte ctOfNeib = imgBytes[offsetx++];
            offsetx++;//byte countOfEachNeib  imgBytes[offsetx+]+
            byte szOfEachNeib;
            if (ctOfNeib > 0 && ctOfNeib <= nfColDef.maxArrCount)
            {
                for (int i = 0; i < ctOfNeib; i++)
                {
                    object arrObj = null;
                    szOfEachNeib = imgBytes[offsetx++];
                    if (szOfEachNeib == 0)
                    {
                        tpPoint[nfColDef.showName, i] = arrObj;
                        continue;
                    }
                    string sTmp = BitConverter.ToString(imgBytes, offsetx, szOfEachNeib);
                    offsetx += szOfEachNeib;
                    string[] sArr = sTmp.Split('-');
                    StringBuilder sb = new StringBuilder();
                    foreach (string s in sArr)
                    {
                        int numb = Convert.ToInt32(s, 16);
                        sb.Append((char)numb);
                    }
                    arrObj = sb.ToString();
                    tpPoint[nfColDef.showName, i] = arrObj;
                }
            }
        }
        #endregion
        #endregion

        private void doAfterFillParams(DTDataHeaderManager headerManager, TestPoint tpPoint)
        {
            DTDataHeader header = headerManager.GetHeaderByFileID(tpPoint.FileID);
            if (header != null && isValidTestPoint(tpPoint))
            {
                doForAttenuation(tpPoint);
                if (doForLTEScanWorkMode(tpPoint))
                {
                    tpPoint.ApplyHeader(header);
                    if (IsAddSampleToDTDataManager)
                    {
                        MainModel.DTDataManager.Add(tpPoint);
                    }
                    else
                    {
                        doWithDTData(tpPoint);
                    }
                    FireDoWithDTData(tpPoint);
                }
            }
        }

        protected virtual void recieveInfo_Event(ClientProxy clientProxy)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<ColumnDefItem> curSampleColumnDef = new List<ColumnDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    setEvtHeader(clientProxy, headerManager, fileHeaderColumnDef, package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_COLUMN_L0G_EVENT)
                {
                    curSampleColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curSampleColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_EVENT)
                {
                    NREventHelper.ReSetIntCI(curSampleColumnDef);
                    fillEvtData(headerManager, curSampleColumnDef, package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_EVENT_NR)
                {
                    NREventHelper.SetLongCI(curSampleColumnDef);
                    fillEvtData(headerManager, curSampleColumnDef, package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

        private void setEvtHeader(ClientProxy clientProxy, DTDataHeaderManager headerManager, List<ColumnDefItem> fileHeaderColumnDef, Package package)
        {
            DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
            if (fileInfo != null && MainModel.IsFileReplayByMTRMode)
            {
                if (canGetHeaderMTR)
                {
                    headerMTR = fileInfo;
                    canGetHeaderMTR = false;
                }
                if (fileIndex > 1)
                {
                    headerManager.AddDTDataHeader(headerMTR);
                }
            }
        }

        private void fillEvtData(DTDataHeaderManager headerManager, List<ColumnDefItem> curSampleColumnDef, Package package)
        {
            Event evt = Event.Create(package.Content, curSampleColumnDef);
            DTDataHeader header = headerManager.GetHeaderByFileID(evt.FileID);
            if (header != null)
            {
                evt.ApplyHeader(header);
                if (ReplayEventFilter == null || !ReplayEventFilter.Invoke(evt))
                {
                    bool isValid = isValidEvent(evt);
                    if (isValid)
                    {
                        MainModel.DTDataManager.Add(evt);
                        FireDoWithEventData(evt);
                    }
                }
            }
        }

        protected virtual void recieveInfo_Message(ClientProxy clientProxy, bool l3hexInclude)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<ColumnDefItem> curSampleColumnDef = new List<ColumnDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            int msgCount = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    dealHeaderInfo(clientProxy, headerManager, fileHeaderColumnDef, package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_COLUMN_L0G_MSG)
                {
                    curSampleColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curSampleColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_MSG)
                {
                    msgCount = fillMsgInfo(l3hexInclude, headerManager, curSampleColumnDef, package, msgCount);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

        private int fillMsgInfo(bool l3hexInclude, DTDataHeaderManager headerManager, List<ColumnDefItem> curSampleColumnDef, Package package, int msgCount)
        {
            Model.Message msg = getMsg(l3hexInclude);
            msg.Index = msgCount++;
            addMsfColumnData(curSampleColumnDef, package, msg);
            DTDataHeader header = headerManager.GetHeaderByFileID(msg.FileID);
            if (header != null)
            {
                msg.ApplyHeader(header);
                if (IsAddMessageToDTDataManager)
                {
                    MainModel.DTDataManager.Add(msg);
                }
                else
                {
                    doWithDTData(msg);
                }
            }

            return msgCount;
        }

        private Model.Message getMsg(bool l3hexInclude)
        {
            Model.Message msg;
            if (MainModel.IsFileReplayByMTRMode && fileIndex > 0)
            {
                if (l3hexInclude)
                {
                    msg = new MasterCom.RAMS.Model.GSMUplinkMessageWithSource();
                }
                else
                {
                    msg = new MasterCom.RAMS.Model.GSMUplinkMessage();
                }
            }
            else
            {
                if (l3hexInclude)
                {
                    msg = new MasterCom.RAMS.Model.MessageWithSource();
                }
                else
                {
                    msg = new MasterCom.RAMS.Model.Message();
                }
            }

            return msg;
        }

        private void addMsfColumnData(List<ColumnDefItem> curSampleColumnDef, Package package, Model.Message msg)
        {
            foreach (ColumnDefItem cdf in curSampleColumnDef)
            {
                if (cdf.showName == "SeqID")
                {
                    setMsgSN(package, msg);
                }
                else if (cdf.showName == "Time")
                {
                    setMsgTime(package, msg, cdf);
                }
                else if (cdf.showName == "TimeMS")
                {
                    setMsgTimeMS(package, msg);
                }
                else if (cdf.showName == "FileID")
                {
                    setMsgFileID(package, msg);
                }
                else if (cdf.showName == "bms")
                {
                    msg.MS = package.Content.GetParamByte();
                }
                else if (cdf.showName == "MsgID")
                {
                    msg.ID = package.Content.GetParamInt();
                }
                else if (cdf.showName == "Direction")
                {
                    msg.Direction = package.Content.GetParamByte();
                }
                else if (cdf.showName == "HexCode")
                {
                    ((MasterCom.RAMS.Model.MessageWithSource)msg).Source = package.Content.GetParamBytes();
                }
                else if (cdf.showName == "Block1Code")
                {
                    msg.HandsetTime = ConvertToHandsetTime(package.Content.GetParamBytes());
                }
            }
        }

        private void setMsgSN(Package package, Model.Message msg)
        {
            msg.SN = package.Content.GetParamInt();
            if (MainModel.IsFileReplayByMTRMode && fileIndex > 1)
            {
                if (MainModel.DTDataManager.FileDataManagers.Count > 1)
                {
                    msg.SN = MainModel.DTDataManager.FileDataManagers[1].Messages.Count + 1;
                }
                else
                {
                    msg.SN = 1;
                }
            }
        }

        private void setMsgTime(Package package, Model.Message msg, ColumnDefItem cdf)
        {
            int timeInt = package.Content.GetParamInt();
            if (timeInt.ToString() != cdf.nullValue)
            {
                msg.Time = timeInt;
            }
        }

        private void setMsgTimeMS(Package package, Model.Message msg)
        {
            msg.Millisecond = package.Content.GetParamShort();
            if (MainModel.IsFileReplayByMTRMode && fileIndex > 0)
            {
                msg.Millisecond += (short)fileOffsetTimeMS;
            }
            if (MainModel.IsFileReplayByMTRToLogMode && fileIndex > 0)
            {
                msg.Millisecond -= (short)fileOffsetTimeMS;
            }
        }

        private void setMsgFileID(Package package, Model.Message msg)
        {
            msg.FileID = package.Content.GetParamInt();
            if (MainModel.IsFileReplayByMTRMode && fileIndex > 1)
            {
                msg.FileID = fileIdMTR;
            }
        }

        public static DateTime ConvertToHandsetTime(byte[] block1code)
        {
            DateTime handTime = new DateTime();
            if (block1code != null && block1code.Length > 2)
            {
                OwnMsgDecode.StartDissect(block1code, 0x7fffffff);

                long value = 0;
                if (OwnMsgDecode.GetIntValue("eSam_Qualcomm_TimeStamp_64bits", ref value))
                {
                    handTime = JavaDate.GetDateTimeFromMilliseconds(value);
                }
            }
            return handTime;
        }

        protected virtual void prepareStatPackage_Sample_FileFilter(Package package, FileInfo fileInfo)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_LOG_SAMPLE;
            package.Content.PrepareAddParam();
            package.Content.AddParam(fileInfo.ID);
            package.Content.AddParam(fileInfo.LogTable);
        }
        protected virtual void prepareStatPackage_Event_FileFilter(Package package, FileInfo fileInfo)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_LOG_EVENT;
            package.Content.PrepareAddParam();
            package.Content.AddParam(fileInfo.ID);
            package.Content.AddParam(fileInfo.LogTable);
        }
        protected virtual void prepareStatPackage_Message_FileFilter(Package package, FileInfo fileInfo)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_LOG_MSG;
            package.Content.PrepareAddParam();
            package.Content.AddParam(fileInfo.ID);
            package.Content.AddParam(fileInfo.LogTable);
        }
        protected void prepareStatPackage_AllRtpMessage_FileFilter(Package package, FileInfo fileInfo)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_LOG_MSG_AllRTP;
            package.Content.PrepareAddParam();
            package.Content.AddParam(fileInfo.ID);
            package.Content.AddParam(fileInfo.LogTable);
        }
        protected virtual void prepareStatPackage_Sample_SampleFilter(Package package)
        {
            AddDIYEndOpFlag(package);//4.sample查询模式定义
        }
        protected virtual void prepareStatPackage_Event_EventFilter(Package package)
        {
            AddDIYEndOpFlag(package);//4.sample查询模式定义
        }
        protected virtual void prepareStatPackage_Message_MessageFilter(Package package)
        {
            AddDIYEndOpFlag(package);//4.sample查询模式定义
        }
        protected virtual bool isValidPoint(double ltX, double ltY, double brX, double brY)
        {
            return true;
        }
        protected virtual bool isValidPoint(double jd, double wd)
        {
            return true;
        }

        protected virtual bool isValidTestPoint(TestPoint tp)
        {
            return true;
        }

        protected virtual bool isValidEvent(Event e)
        {
            return true;
        }


        public override string Name
        {
            get { return "DIY回放"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
    }
}
