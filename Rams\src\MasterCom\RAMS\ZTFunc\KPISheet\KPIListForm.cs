﻿using MasterCom.RAMS.Frame;
using MasterCom.RAMS.ZTFunc.KPISheet;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraEditors.Controls;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class KPIListForm : ChildForm
    {
        public KPIListForm()
        {
            InitializeComponent();

            //init();
        }

        private void init()
        {
            foreach (AreaType item in Enum.GetValues(typeof(AreaType)))
            {
                cbxAreaType.Properties.Items.Add(item);
            }
            cbxAreaType.SelectedIndex = 0;
            foreach (TestTag testTag in TableCfgManager.Instance.TestTagSet)
            {
                chkCbxTestTag.Properties.Items.Add(testTag);
            }
            if (chkCbxTestTag.Properties.Items.Count > 0)
            {
                chkCbxTestTag.SetEditValue(TableCfgManager.Instance.TestTagSet[0]);
            }
            foreach (ReportTemplate template in ReportTemplateMng.Instance.Templates)
            {
                cbxTemplate.Properties.Items.Add(template);
            }
            if (cbxTemplate.Properties.Items.Count > 0)
            {
                cbxTemplate.SelectedIndex = 0;
            }
        }

        ReportTemplate curTemplate = null;
        private void btnQuery_Click(object sender, EventArgs e)
        {
            if (cbxAreaType.SelectedItem == null)
            {
                MessageBox.Show("请指定区域类型！");
                return;
            }
            AreaType at = (AreaType)cbxAreaType.SelectedItem;

            List<TestTag> testItems = new List<TestTag>();
            foreach (CheckedListBoxItem item in chkCbxTestTag.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    testItems.Add(item.Value as TestTag);
                }
            }
            if (testItems.Count == 0)
            {
                MessageBox.Show("请指定测试轮次！");
                return;
            }
            ReportTemplate template = cbxTemplate.SelectedItem as ReportTemplate;
            if (cbxTemplate.SelectedItem == null)
            {
                MessageBox.Show("请指定报表！");
                return;
            }
            curTemplate = template;
            bkWorker.RunWorkerAsync(new object[] { template, at, testItems });
        }

        private DataTable queryKPI(ReportTemplate template, AreaType at, List<TestTag> testItems)
        {
            QueryKPIByTemplate query = new QueryKPIByTemplate(template, at, testItems);
            query.Query();
            return query.DataTable;
        }

        private void refreshViewColCaption()
        {
            for (int i = 0; i < curTemplate.Columns.Count; i++)
            {
                ReportColunm rptCol = curTemplate.Columns[i];
                GridColumn grdCol = gridViewKPI.Columns[i];
                grdCol.Caption = grdCol.FieldName;
                if (rptCol.IsFrozen)
                {
                    grdCol.Fixed = FixedStyle.Left;
                }
                grdCol.Tag = rptCol;
            }
            //foreach (GridColumn col in gridViewKPI.Columns)
            //{
            //    col.Caption = col.FieldName;
            //    col.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            //    col.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            //}
        }

        private void gridViewKPI_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator && e.RowHandle >= 0)
            {
                e.Info.DisplayText = (e.RowHandle + 1).ToString();
            }
        }

        private void btnCustom_Click(object sender, EventArgs e)
        {
            CustomReportForm frm = new CustomReportForm(cbxTemplate.SelectedItem as ReportTemplate);
            frm.ShowDialog();
            cbxTemplate.Properties.Items.Clear();
            foreach (ReportTemplate template in ReportTemplateMng.Instance.Templates)
            {
                cbxTemplate.Properties.Items.Add(template);
            }
            if (cbxTemplate.Properties.Items.Count > 0)
            {
                cbxTemplate.SelectedIndex = 0;
            }
        }

        private void ExportToExcel_Click(object sender, EventArgs e)
        {
            DataTable tb = this.gridControlKPI.DataSource as DataTable;
            if (tb == null)
            {
                return;
            }
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            foreach (DataColumn col in tb.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            rows.Add(row);
            foreach (DataRow r in tb.Rows)
            {
                row = new NPOIRow();
                rows.Add(row);
                foreach (DataColumn col in tb.Columns)
                {
                    object val = r[col];
                    if (val is float)
                    {
                        float fVal = (float)val;
                        if (fVal == -255 || float.IsNaN(fVal))
                        {
                            row.AddCellValue(null);
                        }
                        else
                        {
                            row.AddCellValue(val);
                        }
                    }
                    else
                    {
                        row.AddCellValue(val);
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void gridViewKPI_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            ReportColunm rptCol = e.Column.Tag as ReportColunm;
            if (rptCol == null || e.CellValue == null)
            {
                return;
            }

            if (rptCol.IsStaticColor)
            {
                if (rptCol.CellStaticBkColor != Color.Transparent)
                {
                    e.Appearance.BackColor = rptCol.CellStaticBkColor;
                }
            }
            else if (rptCol.CellDynamicBkColorRanges != null)
            {
                float value;
                if (!float.TryParse(e.CellValue.ToString(), out value))
                {
                    return;
                }

                foreach (DTParameterRangeColor range in rptCol.CellDynamicBkColorRanges)
                {
                    if (range.Within(value))
                    {
                        e.Appearance.BackColor = range.Value;
                        break;
                    }
                }
            }

        }

        private void gridViewKPI_CustomDrawColumnHeader(object sender, DevExpress.XtraGrid.Views.Grid.ColumnHeaderCustomDrawEventArgs e)
        {
            //if (e.Column == null)
            //{
            //    return;
            //}
            //ReportColunm rptCol = e.Column.Tag as ReportColunm;
            //if (rptCol == null || rptCol.ColHeaderBkColor == Color.Transparent)
            //{
            //    return;
            //}
            //e.Graphics.FillRectangle(new SolidBrush(rptCol.ColHeaderBkColor),e.Bounds);
            //e.Handled = true;
        }

        private void bkWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            this.Enabled = false;
            if (e.Argument is object[])
            {
                object[] args = e.Argument as object[];
                e.Result = queryKPI((ReportTemplate)args[0], (AreaType)args[1], (List<TestTag>)args[2]);
            }
        }

        private void KPIListForm_Shown(object sender, EventArgs e)
        {
            bkWorker.RunWorkerAsync();
        }

        private void bkWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            this.Enabled = true;
            if (e.Result is DataTable)
            {
                gridViewKPI.Columns.Clear();//清空原有列名，否则重新绑定数据源列名是原来的列名
                gridControlKPI.DataSource = e.Result as DataTable;
                gridControlKPI.RefreshDataSource();
                gridViewKPI.PopulateColumns();
                refreshViewColCaption();
                gridViewKPI.BestFitColumns();
            }
            else
            {
                init();
            }
        }

    }
}
