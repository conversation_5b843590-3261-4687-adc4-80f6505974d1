﻿namespace MasterCom.RAMS.ZTFunc.JiLinFocusSet
{
    partial class GridOrderSetListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.lv = new BrightIdeasSoftware.TreeListView();
            this.colDistrictName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colOrderTypeName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colStatusDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colItemCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colAreaNames = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colRoadNames = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowAllOrder = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.btnQuery = new DevExpress.XtraEditors.SimpleButton();
            this.btnFilter = new DevExpress.XtraEditors.SimpleButton();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.dtTo = new System.Windows.Forms.DateTimePicker();
            this.dtFrom = new System.Windows.Forms.DateTimePicker();
            this.txtOrderID = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.lv)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // lv
            // 
            this.lv.Activation = System.Windows.Forms.ItemActivation.OneClick;
            this.lv.AllColumns.Add(this.colDistrictName);
            this.lv.AllColumns.Add(this.colOrderTypeName);
            this.lv.AllColumns.Add(this.colSN);
            this.lv.AllColumns.Add(this.colStatusDesc);
            this.lv.AllColumns.Add(this.colItemCount);
            this.lv.AllColumns.Add(this.colAreaNames);
            this.lv.AllColumns.Add(this.colRoadNames);
            this.lv.AllColumns.Add(this.colLng);
            this.lv.AllColumns.Add(this.colLat);
            this.lv.AllColumns.Add(this.colTAC);
            this.lv.AllColumns.Add(this.colECI);
            this.lv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colDistrictName,
            this.colOrderTypeName,
            this.colSN,
            this.colStatusDesc,
            this.colItemCount,
            this.colAreaNames,
            this.colRoadNames,
            this.colLng,
            this.colLat,
            this.colTAC,
            this.colECI});
            this.lv.ContextMenuStrip = this.ctxMenu;
            this.lv.Cursor = System.Windows.Forms.Cursors.Default;
            this.lv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lv.FullRowSelect = true;
            this.lv.GridLines = true;
            this.lv.HeaderWordWrap = true;
            this.lv.IsNeedShowOverlay = false;
            this.lv.Location = new System.Drawing.Point(0, 0);
            this.lv.Name = "lv";
            this.lv.OwnerDraw = true;
            this.lv.ShowGroups = false;
            this.lv.ShowItemToolTips = true;
            this.lv.Size = new System.Drawing.Size(973, 263);
            this.lv.TabIndex = 5;
            this.lv.UseCompatibleStateImageBehavior = false;
            this.lv.View = System.Windows.Forms.View.Details;
            this.lv.VirtualMode = true;
            // 
            // colDistrictName
            // 
            this.colDistrictName.HeaderFont = null;
            this.colDistrictName.Text = "地市";
            this.colDistrictName.Width = 43;
            // 
            // colOrderTypeName
            // 
            this.colOrderTypeName.HeaderFont = null;
            this.colOrderTypeName.Text = "工单类型";
            this.colOrderTypeName.Width = 124;
            // 
            // colSN
            // 
            this.colSN.HeaderFont = null;
            this.colSN.MinimumWidth = 60;
            this.colSN.Text = "工单ID";
            // 
            // colStatusDesc
            // 
            this.colStatusDesc.HeaderFont = null;
            this.colStatusDesc.MinimumWidth = 60;
            this.colStatusDesc.Text = "状态";
            // 
            // colItemCount
            // 
            this.colItemCount.HeaderFont = null;
            this.colItemCount.Text = "栅格个数";
            this.colItemCount.Width = 57;
            // 
            // colAreaNames
            // 
            this.colAreaNames.HeaderFont = null;
            this.colAreaNames.MinimumWidth = 100;
            this.colAreaNames.Text = "区域";
            this.colAreaNames.Width = 200;
            // 
            // colRoadNames
            // 
            this.colRoadNames.HeaderFont = null;
            this.colRoadNames.MinimumWidth = 100;
            this.colRoadNames.Text = "道路";
            this.colRoadNames.Width = 200;
            // 
            // colLng
            // 
            this.colLng.HeaderFont = null;
            this.colLng.MinimumWidth = 60;
            this.colLng.Text = "栅格左上经度";
            this.colLng.Width = 120;
            // 
            // colLat
            // 
            this.colLat.HeaderFont = null;
            this.colLat.MinimumWidth = 60;
            this.colLat.Text = "栅格左上纬度";
            this.colLat.Width = 120;
            // 
            // colTAC
            // 
            this.colTAC.HeaderFont = null;
            this.colTAC.Text = "TAC/EARFCN";
            // 
            // colECI
            // 
            this.colECI.HeaderFont = null;
            this.colECI.Text = "ECI/PCI";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowAllOrder,
            this.toolStripSeparator1,
            this.miExpandAll,
            this.miCollapseAll,
            this.toolStripSeparator2,
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(169, 104);
            // 
            // miShowAllOrder
            // 
            this.miShowAllOrder.Name = "miShowAllOrder";
            this.miShowAllOrder.Size = new System.Drawing.Size(168, 22);
            this.miShowAllOrder.Text = "GIS渲染所有工单";
            this.miShowAllOrder.Click += new System.EventHandler(this.miShowAllOrder_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(165, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(168, 22);
            this.miExpandAll.Text = "展开所有节点";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(168, 22);
            this.miCollapseAll.Text = "收缩所有节点";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(165, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(168, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.txtOrderID);
            this.splitContainerControl1.Panel1.Controls.Add(this.label4);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnQuery);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnFilter);
            this.splitContainerControl1.Panel1.Controls.Add(this.label2);
            this.splitContainerControl1.Panel1.Controls.Add(this.label3);
            this.splitContainerControl1.Panel1.Controls.Add(this.label1);
            this.splitContainerControl1.Panel1.Controls.Add(this.dtTo);
            this.splitContainerControl1.Panel1.Controls.Add(this.dtFrom);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.lv);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(973, 342);
            this.splitContainerControl1.SplitterPosition = 73;
            this.splitContainerControl1.TabIndex = 6;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // btnQuery
            // 
            this.btnQuery.Location = new System.Drawing.Point(550, 11);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(75, 23);
            this.btnQuery.TabIndex = 3;
            this.btnQuery.Text = "查询";
            this.btnQuery.ToolTip = "重新查询工单";
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // btnFilter
            // 
            this.btnFilter.Location = new System.Drawing.Point(454, 11);
            this.btnFilter.Name = "btnFilter";
            this.btnFilter.Size = new System.Drawing.Size(75, 23);
            this.btnFilter.TabIndex = 3;
            this.btnFilter.Text = "筛选";
            this.btnFilter.ToolTip = "在查询结果中进行筛选";
            this.btnFilter.Click += new System.EventHandler(this.btnFilter_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(24, 16);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(107, 14);
            this.label2.TabIndex = 1;
            this.label2.Text = "工单创建时间范围:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(288, 16);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(19, 14);
            this.label3.TabIndex = 1;
            this.label3.Text = "到";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(137, 16);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(19, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "从";
            // 
            // dtTo
            // 
            this.dtTo.Location = new System.Drawing.Point(313, 12);
            this.dtTo.Name = "dtTo";
            this.dtTo.Size = new System.Drawing.Size(120, 22);
            this.dtTo.TabIndex = 0;
            // 
            // dtFrom
            // 
            this.dtFrom.Location = new System.Drawing.Point(162, 12);
            this.dtFrom.Name = "dtFrom";
            this.dtFrom.Size = new System.Drawing.Size(120, 22);
            this.dtFrom.TabIndex = 0;
            // 
            // txtOrderID
            // 
            this.txtOrderID.Location = new System.Drawing.Point(162, 40);
            this.txtOrderID.Name = "txtOrderID";
            this.txtOrderID.Size = new System.Drawing.Size(271, 22);
            this.txtOrderID.TabIndex = 5;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(84, 43);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(47, 14);
            this.label4.TabIndex = 4;
            this.label4.Text = "工单ID:";
            // 
            // GridOrderSetListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(973, 342);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "GridOrderSetListForm";
            this.Text = "工单列表";
            ((System.ComponentModel.ISupportInitialize)(this.lv)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView lv;
        private BrightIdeasSoftware.OLVColumn colSN;
        private BrightIdeasSoftware.OLVColumn colItemCount;
        private BrightIdeasSoftware.OLVColumn colAreaNames;
        private BrightIdeasSoftware.OLVColumn colRoadNames;
        private BrightIdeasSoftware.OLVColumn colLng;
        private BrightIdeasSoftware.OLVColumn colLat;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SimpleButton btnQuery;
        private DevExpress.XtraEditors.SimpleButton btnFilter;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.DateTimePicker dtTo;
        private System.Windows.Forms.DateTimePicker dtFrom;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private BrightIdeasSoftware.OLVColumn colStatusDesc;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private BrightIdeasSoftware.OLVColumn colDistrictName;
        private BrightIdeasSoftware.OLVColumn colOrderTypeName;
        private BrightIdeasSoftware.OLVColumn colTAC;
        private BrightIdeasSoftware.OLVColumn colECI;
        private System.Windows.Forms.ToolStripMenuItem miShowAllOrder;
        private System.Windows.Forms.TextBox txtOrderID;
        private System.Windows.Forms.Label label4;
    }
}