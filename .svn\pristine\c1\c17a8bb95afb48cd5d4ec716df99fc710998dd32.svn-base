﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;
using System.Drawing;
namespace MasterCom.RAMS.CQT
{
    public class QueryCQTProblemTrackGSM : QueryBase
    {
        public QueryCQTProblemTrackGSM(MainModel mainModel, string netType)
        : base(mainModel)
        {
            bool isGD = true;
            if (CQTProblemSummaryAnalysGSM.cityLevel(mainModel.DistrictID) == "")
                isGD = false; 
            if (netType == "GSM")
            {
                if (isGD)
                    iareatype = 24;
                else
                    iareatype = -100;
            }
            else if (netType == "TD")
            {
                if (isGD)
                    iareatype = 25;
                else
                    iareatype = -200;
            }
            else
                iareatype = 28;
        }       
        protected override bool isValidCondition()
        {
            return true;
        }
        public override string Name
        {
            get { return "2G问题点跟踪"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21039, this.Name);
        }
        readonly int iareatype = -1;
        CQTProblemTrackGSMConditionForm problemTrackForm;
        public Dictionary<string, ConditionSetForm> conditionDic { get; set; }
        public List<string> stateNameList { get; set; }
        string selectSateName = "";
        List<TabProblemInfo> tabProblemInfoTem;
        public Dictionary<string, List<TabProblemInfo>> tabProblemInfoDic { get; set; }
        public static Dictionary<int, FileInfoItem> CQTFileDic { get; set; }
        protected override void query()
        {
            if(problemTrackForm == null)
                problemTrackForm = new CQTProblemTrackGSMConditionForm();
            if (problemTrackForm.ShowDialog() != DialogResult.OK)
                return;
            MainModel.TabProblemInfoList.Clear();
            conditionDic = new Dictionary<string,ConditionSetForm>();
            stateNameList = new List<string>();
            tabProblemInfoTem = new List<TabProblemInfo>();
            tabProblemInfoDic = new Dictionary<string, List<TabProblemInfo>>();
            CQTFileDic = new Dictionary<int, FileInfoItem>();
            stateNameList.AddRange(problemTrackForm.stateNameListTem);
            selectSateName = problemTrackForm.strSelectSateName;
            if (selectSateName == "所有状态")
            {
                foreach (string sName in stateNameList)
                {
                    if (sName == "所有状态")
                        continue;
                    if (problemTrackForm.conditionDic.ContainsKey(sName))
                        conditionDic.Add(sName, problemTrackForm.conditionDic[sName]);
                }
            }
            else
            {
                conditionDic.Add(selectSateName, problemTrackForm.conditionDic[selectSateName]);
            }
            if (conditionDic.Count == 0)
            {
                MessageBox.Show("当前状态数为0，请选设置状态！");
                return;
            }
            try
            {
                WaitBox.Show("准备获取CQT点...", seachProblemData);
            }
            finally
            {
                MainModel.MainForm.FireCQTProblemTrackQueried();
            }
        }
        /// <summary>
        /// 准备查询数据的条件
        /// </summary>
        private void seachProblemData()
        {
            WaitBox.ProgressPercent = 20;
            DateTime sDtime = condition.Periods[0].BeginTime;
            DateTime eDtime = condition.Periods[0].EndTime.AddSeconds(-1);
            StringBuilder project = new StringBuilder();
            for (int i = 0; i < condition.Projects.Count; i++)
            {
                if (i < condition.Projects.Count - 1)
                    project.Append(condition.Projects[i].ToString() + ",");
                else
                    project.Append(condition.Projects[i].ToString());
            }
            WaitBox.ProgressPercent = 30;
            if (MainModel.User.DBID == -1)
            {
                for (int chdistrid = 0; chdistrid < condition.DistrictIDs.Count; chdistrid++)
                {
                    MainModel.DistrictID = condition.DistrictIDs[chdistrid];
                    seachProblemPointData(MainModel.DistrictID, sDtime, eDtime, project.ToString());
                }
            }
            else
            {
                seachProblemPointData(MainModel.DistrictID, sDtime, eDtime, project.ToString());
            }
            WaitBox.ProgressPercent = 80;
            WaitBox.Close();
        }
        /// <summary>
        /// 真正查询问题点记录的过程
        /// </summary>
        private void seachProblemPointData(int cityId, DateTime sDtime, DateTime eDtime, string project)
        {
            string strCityName = DistrictManager.GetInstance().getDistrictName(cityId);
            WaitBox.Text = "正在获取 " + strCityName + " 市的CQT问题点信息...";
            DIYQueryProblemInfo diyProblemInfo = new DIYQueryProblemInfo(MainModel, iareatype, sDtime, eDtime, project);
            diyProblemInfo.Query();//查测试地点问题点各字段
            int iareatypeid = 0;
            if (diyProblemInfo.CQTPromblemDic.Count == 0)
                return;
            foreach (int iareaid in diyProblemInfo.CQTPromblemDic.Keys)
            {
                iareatypeid = diyProblemInfo.CQTPromblemDic[iareaid][0].IAreaType;
                if (iareatypeid > 0)
                {
                    break;
                }
            }
            MasterCom.RAMS.Stat.DIYQueryCQTPointInfo dqcpi = new MasterCom.RAMS.Stat.DIYQueryCQTPointInfo(MainModel, iareatypeid);
            dqcpi.Query();//查测试地点属性与地点名称
            WaitBox.ProgressPercent = WaitBox.ProgressPercent + 1;

            DIYQueryEventTiemInfo diyProblemEventTime = new DIYQueryEventTiemInfo(MainModel, iareatype, sDtime, eDtime, project);
            diyProblemEventTime.Query();//查询测试地点的测试事件记录
            DIYQueryEventLoLaInfo diyProblemLoLa = new DIYQueryEventLoLaInfo(MainModel, iareatype);
            diyProblemLoLa.Query();//查询问题点地点的经纬度
            List<string> logList = getLogFileNameList(sDtime, eDtime);
            foreach (string log in logList)
            {
                DIYQueryCQTFileInfo diyFileInfo = new DIYQueryCQTFileInfo(MainModel, log, iareatype, project, true, sDtime, eDtime);
                diyFileInfo.Query();//查询测试地点的文件列表
                foreach (int idk in diyFileInfo.CQTFileDic.Keys)
                {
                    if (!CQTFileDic.ContainsKey(idk))
                        CQTFileDic.Add(idk, diyFileInfo.CQTFileDic[idk]);
                }
            }
            List<TabProblemInfo> tabProblemList = new List<TabProblemInfo>();
            addTabProblemList(strCityName, diyProblemInfo, dqcpi, diyProblemEventTime, diyProblemLoLa, tabProblemList);
            tabProblemInfoTem.AddRange(tabProblemList);
            tabProblemInfoDic.Add(strCityName, tabProblemList);
            MainModel.TabProblemInfoList.AddRange(tabProblemList);
        }

        private void addTabProblemList(string strCityName, DIYQueryProblemInfo diyProblemInfo, DIYQueryCQTPointInfo dqcpi, DIYQueryEventTiemInfo diyProblemEventTime, DIYQueryEventLoLaInfo diyProblemLoLa, List<TabProblemInfo> tabProblemList)
        {
            foreach (int areaid in diyProblemEventTime.CQTPromblemDic.Keys)
            {
                TabProblemInfo tbInfo = new TabProblemInfo();
                if (!dqcpi.CQTPointDic.ContainsKey(areaid)
                    || !diyProblemLoLa.CQTPromblemLoLaDic.ContainsKey(areaid)
                    || !diyProblemInfo.CQTPromblemDic.ContainsKey(areaid))
                    continue;
                tbInfo.IAreaId = areaid;
                tbInfo.IAreaType = dqcpi.CQTPointDic[areaid].Iareatypeid;
                tbInfo.StrCityName = strCityName;
                tbInfo.StrTestName = dqcpi.CQTPointDic[areaid].Strareaname;
                tbInfo.StrTestComment = dqcpi.CQTPointDic[areaid].Strcomment;
                tbInfo.DLongitude = diyProblemLoLa.CQTPromblemLoLaDic[areaid].DLongitude;
                tbInfo.DLatitude = diyProblemLoLa.CQTPromblemLoLaDic[areaid].DLatitude;
                foreach (ProblemTime prItime in diyProblemEventTime.CQTPromblemDic[areaid])
                {
                    tbInfo.eventTimeInfo.Add(prItime);
                    if (prItime.Iresult == 0)
                        tbInfo.INormalNum += 1;
                }
                tbInfo.ITestNum = tbInfo.eventTimeInfo.Count;
                tbInfo.IAbNormalNum = tbInfo.ITestNum - tbInfo.INormalNum;
                tbInfo.ILastNormalNum = iLastNormal(tbInfo.eventTimeInfo);
                foreach (ProblemItem pr in diyProblemInfo.CQTPromblemDic[areaid])
                {
                    ProblemInfoTem prItem = new ProblemInfoTem();
                    setPrItem(pr, prItem);
                    tbInfo.problemInfo.Add(prItem);
                }
                tbInfo.StrSateName = strStateName(tbInfo);
                if (tbInfo.StrSateName.Equals(""))
                    continue;
                tbInfo.ITurn = MainModel.TabProblemInfoList.Count + tabProblemList.Count;
                tbInfo.ColorClose = conditionDic[tbInfo.StrSateName].ColorClose;
                tabProblemList.Add(tbInfo);
            }
        }

        private void setPrItem(ProblemItem pr, ProblemInfoTem prItem)
        {
            prItem.Dtime = pr.Dtime;
            prItem.StrCauseInfo = pr.StrCauseInfo;
            prItem.StrCauseType = pr.StrCauseType;
            prItem.StrLogInfo = pr.StrLogInfo;
            prItem.StrMainProblem1 = pr.StrMainProblem1;
            prItem.StrMainProblem2 = pr.StrMainProblem2;
            prItem.StrMainProblem3 = pr.StrMainProblem3;
            prItem.StrSecProblem1 = pr.StrSecProblem1;
            prItem.StrSecProblem2 = pr.StrSecProblem2;
            prItem.StrSecProblem3 = pr.StrSecProblem3;
            prItem.StrTestSite = pr.StrTestSite;
            prItem.probCellInfoList = getProbCellInfo(pr.StrCauseInfo);
            prItem.fileList = getFileNameList(pr);
            int mainProblemID = 0;
            if (prItem.StrMainProblem2 != "")
            {
                prItem.SMainType = prItem.StrMainProblem2;
                prItem.SPointPosition = getsub(prItem.StrTestSite, 2);
                mainProblemID = 0;
            }
            else if (prItem.StrMainProblem3 != "")
            {
                prItem.SMainType = prItem.StrMainProblem3;
                prItem.SPointPosition = getsub(prItem.StrTestSite, 3);
                mainProblemID = 1;
            }
            else if (prItem.StrMainProblem1 != "")
            {
                prItem.SMainType = prItem.StrMainProblem1;
                prItem.SPointPosition = getsub(prItem.StrTestSite, 1);
                mainProblemID = 2;
            }
            else if (prItem.StrSecProblem1 != "")
            {
                prItem.SMainType = prItem.StrSecProblem1;
                mainProblemID = 3;
            }
            setPrItemSSecondType(prItem, mainProblemID);
            StringBuilder strFile = new StringBuilder();
            StringBuilder strCell = new StringBuilder();
            int file = 0;
            int ilac = 0;
            int ici = 0;
            for (file = 0; file < prItem.fileList.Count; file++)
            {
                strFile.Append(prItem.fileList[file].ToString() + "；");
            }
            prItem.STestFilePosition = strFile.ToString();
            for (file = 0; file < prItem.probCellInfoList.Count; file++)
            {
                ilac = prItem.probCellInfoList[file].Ilac;
                ici = prItem.probCellInfoList[file].Ici;
                if (prItem.probCellInfoList[file].Strcellname == "")
                    strCell.Append("(" + ilac + "_" + ici + "),采样点共" + prItem.probCellInfoList[file].Isampleid + "个,场强为" + prItem.probCellInfoList[file].Irxlev + "dBm" + "；");
                else
                    strCell.Append(prItem.probCellInfoList[file].Strcellname + "(" + ilac + "_" + ici + "),采样点共" + prItem.probCellInfoList[file].Isampleid + "个,场强为" + prItem.probCellInfoList[file].Irxlev + "dBm" + "；");
            }
            prItem.SReasonAna = strCell.ToString();
        }

        private static void setPrItemSSecondType(ProblemInfoTem prItem, int mainProblemID)
        {
            if (mainProblemID == 0)
            {
                setProblem(prItem, prItem.StrMainProblem3, true);
                setProblem(prItem, prItem.StrMainProblem1, true);
                setProblem(prItem, prItem.StrSecProblem1, false);
            }
            else if (mainProblemID == 1)
            {
                setProblem(prItem, prItem.StrMainProblem1, true);
                setProblem(prItem, prItem.StrSecProblem1, false);
            }
            else if (mainProblemID == 2)
            {
                setProblem(prItem, prItem.StrSecProblem1, false);
            }
            prItem.SSecondType = "";
        }

        private static void setProblem(ProblemInfoTem prItem, string problem, bool split)
        {
            if (problem != "")
            {
                prItem.SSecondType += problem;
                if (split)
                {
                    prItem.SSecondType += ",";
                }
            }
        }

        /// <summary>
        /// 计算最后连续正常次数
        /// </summary>
        private int iLastNormal(List<ProblemTime> eventTimeInfo)
        {
            int ilastnormal = 0;
            int icount = eventTimeInfo.Count;
            for (int i = icount - 1; i >= 0; i--)
            {
                if (eventTimeInfo[i].Iresult == 0)
                    ilastnormal++;
                else
                    break;
            }
            return ilastnormal;
        }
        /// <summary>
        /// 判断所属状态
        /// </summary>
        private string strStateName(TabProblemInfo tbInfo)
        {
            string strstatename = "";
            foreach (string sName in conditionDic.Keys)
            {
                if (isRinght(tbInfo.ITestNum, conditionDic[sName].StrTestNumJudge, conditionDic[sName].ITestNum)
                    && isRinght(tbInfo.INormalNum, conditionDic[sName].StrNormalNumJudge, conditionDic[sName].INormalNum)
                    && isRinght(tbInfo.IAbNormalNum, conditionDic[sName].StrAbnormalNum, conditionDic[sName].IAbnormalNum)
                    && isRinght(tbInfo.ILastNormalNum, conditionDic[sName].StrLastNormalNum, conditionDic[sName].ILastNormalNum))
                {
                    strstatename = sName;
                    break;
                }
            }
            return strstatename;
        }
        /// <summary>
        /// 逐条判断是否满足条件
        /// </summary>
        /// <returns></returns>
        public bool isRinght(int iNow,string strJudge,int Judge)
        {
            bool isringht = false;
            switch (strJudge)
            {
                case ">=":
                    {
                        if (iNow >= Judge)
                            isringht = true; 
                    }
                    break;
                case ">":
                    {
                        if (iNow > Judge)
                            isringht = true; 
                    }
                    break;
                case "<=":
                    {
                        if (iNow <= Judge)
                            isringht = true;
                    }
                    break;
                case "<":
                    {
                        if (iNow < Judge)
                            isringht = true;
                    }
                    break;
                case "=":
                    {
                        if (iNow == Judge)
                            isringht = true;
                    }
                    break;
                default:
                    break;

            }
            return isringht;
        }
        /// <summary>
        /// 获取tb_log表名
        /// </summary>
        private List<string> getLogFileNameList(DateTime tmpDate, DateTime eDtime)
        {
            List<string> logList = new List<string>();
            while (tmpDate <= eDtime)
            {
                string strLogName = string.Format("tb_log_file_{0:yyyy}_{0:MM}", tmpDate);
                if (!logList.Contains(strLogName))
                {
                    logList.Add(strLogName);
                }
                tmpDate = tmpDate.AddDays(1);
            }
            return logList;
        }
        /// <summary>
        /// 问题点信息详情列表
        /// </summary>
        private List<ProbCellInfo> getProbCellInfo(string probInfo)
        {
            List<ProbCellInfo> pcInfo = new List<ProbCellInfo>();
            string[] probCell = probInfo.Split('|');
            foreach (string prob in probCell)
            {
                try
                {
                    string[] info = prob.Split(',');
                    ProbCellInfo cpi = new ProbCellInfo();
                    cpi.Strcellname = info[0].ToString();
                    cpi.Ilac = Convert.ToInt32(info[1]);
                    cpi.Ici = Convert.ToInt32(info[2]);
                    cpi.Isampleid = Convert.ToInt32(info[3]);
                    cpi.Irxlev = Convert.ToInt32(info[4]);
                    pcInfo.Add(cpi);
                }
                catch
                {
                    //continue
                }
            }
            return pcInfo;
        }
        /// <summary>
        /// 获取对应文件名列表
        /// </summary>
        private List<string> getFileNameList(ProblemItem pT)
        {
            List<String> fileNameList = new List<String>();

            string strFilePart = "";
            string[] filePart = pT.StrLogInfo.Split('|');
            if (pT.StrMainProblem2 != "" && filePart.Length > 1)
                strFilePart = filePart[1];
            else if (pT.StrMainProblem3 != "" && filePart.Length > 2)
                strFilePart = filePart[2];
            else
                strFilePart = filePart[0];
            string[] fileids = strFilePart.Split(',');
            foreach (string strfileid in fileids)
            {
                try
                {
                    int ifileid = Convert.ToInt32(strfileid);
                    if (QueryCQTProblemTrackGSM.CQTFileDic.ContainsKey(ifileid))
                        fileNameList.Add(QueryCQTProblemTrackGSM.CQTFileDic[ifileid].StrfileName);
                }
                catch
                {
                    //continue
                }
            }
            return fileNameList;
        }
        /// <summary>
        /// 截取字段
        /// </summary>
        private string getsub(string strlis, int num)
        {
            string[] newstr = strlis.Split('|');
            if (newstr.Length > 1)
            {
                if (num == 1)
                    return newstr[0];
                else if (num == 2)
                    return newstr[1];
                else if (num == 3)
                    return newstr[2];
                else
                    return "";
            }
            else
                return newstr[0];

        }
    }
    public class DIYQueryProblemInfo : DIYSQLBase
    {
        readonly int iAreaTypeId = 0;
        readonly string strProject;
        readonly DateTime sDate;
        readonly DateTime eDate;
        public DIYQueryProblemInfo(MainModel mm, int areaTypeId, DateTime sDate_l, DateTime eDate_l, string strPor)
            : base(mm)
        {
            mainModel = mm;
            this.iAreaTypeId = areaTypeId;
            this.sDate = sDate_l;
            this.eDate = eDate_l;
            this.strProject = strPor;
        }
        public override string Name
        {
            get { return "查询CQT地点属性"; }
        }
        protected override string getSqlTextString()
        {
            string strTable = " tb_func_cqt_problem_gsm ";
            string strIareatype = " and  iareatype in (" + iAreaTypeId + ")";
            if (iAreaTypeId != -100 && iAreaTypeId != -200)
            {
                if (iAreaTypeId == 25)
                    strTable = " tb_func_cqt_problem_td ";
                return "SELECT convert(varchar(20),dstime,20),icity,iareatype,iareaid,strMainProblem1,strSecProblem1,strMainProblem2,"
                            + "strSecProblem2,strMainProblem3,strSecProblem3,strTestSite,strLogInfo,strCauseInfo,strCauseType  from "
                            + strTable + " where  dstime >= '" + sDate + "' and detime <= '" + eDate
                            + "' and iprojecttype in (" + strProject + ") " + strIareatype;
            }
            else
            {
                if (iAreaTypeId == -200)
                    strTable = " tb_func_cqt_problem_td ";
                return "SELECT convert(varchar(20),dstime,20),icity,iareatype,iareaid,strMainProblem1,strSecProblem1,strMainProblem2,"
                            + "strSecProblem2,strMainProblem3,strSecProblem3,strTestSite,strLogInfo,strCauseInfo,strCauseType  from "
                            + strTable + " where  dstime >= '" + sDate + "' and detime <= '" + eDate
                            + "' and iprojecttype in (" + strProject + ") ";
            }
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[14];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_String;
            rType[9] = E_VType.E_String;
            rType[10] = E_VType.E_String;
            rType[11] = E_VType.E_String;
            rType[12] = E_VType.E_String;
            rType[13] = E_VType.E_String;
            return rType;
        }
        public Dictionary<int, List<ProblemItem>> CQTPromblemDic { get; set; } = new Dictionary<int, List<ProblemItem>>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            CQTPromblemDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ProblemItem cqtTem = new ProblemItem();
                    cqtTem.Dtime = DateTime.Parse(package.Content.GetParamString());
                    cqtTem.Icity = package.Content.GetParamInt();
                    cqtTem.IAreaType = package.Content.GetParamInt();
                    cqtTem.IAreaID = package.Content.GetParamInt();
                    cqtTem.StrMainProblem1 = package.Content.GetParamString();
                    cqtTem.StrSecProblem1 = package.Content.GetParamString();
                    cqtTem.StrMainProblem2 = package.Content.GetParamString();
                    cqtTem.StrSecProblem2 = package.Content.GetParamString();
                    cqtTem.StrMainProblem3 = package.Content.GetParamString();
                    cqtTem.StrSecProblem3 = package.Content.GetParamString();
                    cqtTem.StrTestSite = package.Content.GetParamString();
                    cqtTem.StrLogInfo = package.Content.GetParamString();
                    cqtTem.StrCauseInfo = package.Content.GetParamString();
                    cqtTem.StrCauseType = package.Content.GetParamString();
                    if (!CQTPromblemDic.ContainsKey(cqtTem.IAreaID))
                    {
                        List<ProblemItem> cqtTemList = new List<ProblemItem>();
                        cqtTemList.Add(cqtTem);
                        CQTPromblemDic.Add(cqtTem.IAreaID, cqtTemList);
                    }
                    else
                    {
                        CQTPromblemDic[cqtTem.IAreaID].Add(cqtTem);
                    }

                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }


    public class DIYQueryEventTiemInfo : DIYSQLBase
    {
        readonly int iAreaTypeId = 0;
        readonly string strProject;
        readonly DateTime sDate;
        readonly DateTime eDate;
        public DIYQueryEventTiemInfo(MainModel mm,  int areaTypeId,
            DateTime sDate_l, DateTime eDate_l, string strPor)
            : base(mm)
        {
            mainModel = mm;
            this.iAreaTypeId = areaTypeId;
            this.sDate = sDate_l;
            this.eDate = eDate_l;
            this.strProject = strPor;
        }
        public override string Name
        {
            get { return "查询问题点EventTime表信息"; }
        }
        protected override string getSqlTextString()
        {
            if (iAreaTypeId != -100 && iAreaTypeId != -200)
            {
                return "SELECT *  from tb_func_cqt_problem_time where iareatype in ("
                    + iAreaTypeId + ") and dstime >= '" + sDate + "' and detime <= '" + eDate
                    + "' and iprojecttype in (" + strProject + ")";
            }
            else
            {
                return "SELECT *  from tb_func_cqt_problem_time where dstime >= '" + sDate + "' and detime <= '" + eDate
                    + "' and iprojecttype in (" + strProject + ")";
            }
    }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[9];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_String;
            return rType;
        }
        public Dictionary<int, List<ProblemTime>> CQTPromblemDic { get; set; } = new Dictionary<int,List<ProblemTime>>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            CQTPromblemDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ProblemTime cqtTem = new ProblemTime();
                    cqtTem.IareaType = package.Content.GetParamInt();
                    cqtTem.IareaId = package.Content.GetParamInt();
                    cqtTem.DSTime = DateTime.Parse(package.Content.GetParamString());
                    cqtTem.DETime = DateTime.Parse(package.Content.GetParamString());
                    cqtTem.IProjectType = package.Content.GetParamInt();
                    cqtTem.StrNet = package.Content.GetParamString();
                    cqtTem.Iresult = package.Content.GetParamInt();
                    cqtTem.Strdesc = package.Content.GetParamString();
                    cqtTem.StrComnont = package.Content.GetParamString();
                    if (!CQTPromblemDic.ContainsKey(cqtTem.IareaId))
                    {
                        List<ProblemTime> prItimeList = new List<ProblemTime>();
                        prItimeList.Add(cqtTem);
                        CQTPromblemDic.Add(cqtTem.IareaId, prItimeList);
                    }
                    else
                    {
                        CQTPromblemDic[cqtTem.IareaId].Add(cqtTem);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
    public class DIYQueryEventLoLaInfo : DIYSQLBase
    {
        readonly int iAreaTypeId = 0;
        public DIYQueryEventLoLaInfo(MainModel mm,int areaTypeId)
            : base(mm)
        {
            mainModel = mm;
            this.iAreaTypeId = areaTypeId;
        }
        public override string Name
        {
            get { return "查询问题点经纬度表信息"; }
        }
        protected override string getSqlTextString()
        {
            if (iAreaTypeId != -100 && iAreaTypeId != -200)
            {
                return "SELECT iareatypeid,iareaid,itllongitude,itllatitude  from tb_cfg_static_point where iareatypeid in ("
                    + iAreaTypeId + ")";
            }
            else
            {
                return "SELECT iareatypeid,iareaid,itllongitude,itllatitude  from tb_cfg_static_point";
            }
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[4];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            return rType;
        }
        public Dictionary<int, ProblemLoLaInfo> CQTPromblemLoLaDic { get; set; } = new Dictionary<int, ProblemLoLaInfo>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            CQTPromblemLoLaDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ProblemLoLaInfo cqtTem = new ProblemLoLaInfo();
                    cqtTem.IAreaType = package.Content.GetParamInt();
                    cqtTem.IAreaId = package.Content.GetParamInt();
                    cqtTem.DLongitude = package.Content.GetParamInt() * 1.0 / 10000000;
                    cqtTem.DLatitude = package.Content.GetParamInt() * 1.0 / 10000000;
                    if (!CQTPromblemLoLaDic.ContainsKey(cqtTem.IAreaId))
                        CQTPromblemLoLaDic.Add(cqtTem.IAreaId, cqtTem);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
    public class ProblemTime
    {
        /// <summary>
        /// 问题点areaType
        /// </summary>
        public int IareaType { get; set; }
        /// <summary>
        /// 问题点areaId
        /// </summary>
        public int IareaId { get; set; }
        /// <summary>
        /// 问题点开始时间
        /// </summary>
        public DateTime DSTime { get; set; }
        /// <summary>
        /// 问题点结束时间
        /// </summary>
        public DateTime DETime { get; set; }
        /// <summary>
        /// 项目ID
        /// </summary>
        public int IProjectType { get; set; }
        /// <summary>
        /// 网络类型
        /// </summary>
        public string StrNet { get; set; }
        /// <summary>
        /// 状态结果
        /// </summary>
        public int Iresult { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Strdesc { get; set; }

        public string StrComnont { get; set; }
        public ProblemTime()
        {
            IareaType = 0;
            IareaId = 0;
            DSTime = DateTime.Now;
            DETime = DateTime.Now;
            IProjectType = -1;
            StrNet = "";
            Iresult = -100;
            Strdesc = "";
            StrComnont = "";
        }
    }
    public class TabProblemInfo
    {
        /// <summary>
        /// 唯一ID序号
        /// </summary>
        public int ITurn { get; set; }
        /// <summary>
        /// 地市
        /// </summary>
        public string StrCityName { get; set; }
        /// <summary>
        /// 地点areatype
        /// </summary>
        public int IAreaType { get; set; }
        /// <summary>
        /// 地点areaid
        /// </summary>
        public int IAreaId { get; set; }
        /// <summary>
        /// 测试地点
        /// </summary>
        public string StrTestName { get; set; }
        /// <summary>
        /// 测试场景
        /// </summary>
        public string StrTestComment { get; set; }
        /// <summary>
        /// 测试次数
        /// </summary>
        public int ITestNum { get; set; }
        /// <summary>
        /// 正常次数
        /// </summary>
        public int INormalNum { get; set; }
        /// <summary>
        /// 异常次数
        /// </summary>
        public int IAbNormalNum { get; set; }
        /// <summary>
        /// 最后连续正常次数
        /// </summary>
        public int ILastNormalNum { get; set; }
        /// <summary>
        /// 经度
        /// </summary>
        public double DLongitude { get; set; }
        /// <summary>
        /// 纬度
        /// </summary>
        public double DLatitude { get; set; }
        /// <summary>
        /// 状态名称
        /// </summary>
        public string StrSateName { get; set; }
        private readonly SolidBrush brushSet;
        /// <summary>
        /// 状态颜色
        /// </summary>
        public Color ColorClose
        {
            get { return brushSet.Color; }
            set { brushSet.Color = Color.FromArgb(150, value); }
        }
        public List<ProblemTime> eventTimeInfo { get; set; }
        public List<ProblemInfoTem> problemInfo { get; set; }
        public TabProblemInfo()
        {
            ITurn = 0;
            StrCityName = "";
            IAreaType = 0;
            IAreaId = 0;
            StrTestName = "";
            StrTestComment = "";
            ITestNum = 0;
            INormalNum = 0;
            IAbNormalNum = 0;
            ILastNormalNum = 0;
            DLongitude = 0;
            DLatitude = 0;
            StrSateName = "";
            eventTimeInfo = new List<ProblemTime>();
            problemInfo = new List<ProblemInfoTem>();
            brushSet = new SolidBrush(Color.FromArgb(150, 50, 102, 0));
        }
    }
    public class ProblemInfoTem
    {
        /// <summary>
        /// 测试时间
        /// </summary>
        public DateTime Dtime { get; set; }
        /// <summary>
        /// 主问题类型
        /// </summary>
        public string SMainType { get; set; }
        /// <summary>
        /// 次问题
        /// </summary>
        public string SSecondType { get; set; }
        /// <summary>
        /// 问题的位置
        /// </summary>
        public string SPointPosition { get; set; }
        /// <summary>
        /// 测试文件存放路径
        /// </summary>
        public string STestFilePosition { get; set; }
        /// <summary>
        /// 原因分析
        /// </summary>
        public string SReasonAna { get; set; }
        /// <summary>
        /// 主问题类别
        /// </summary>
        public string StrMainProblem1 { get; set; }
        /// <summary>
        /// 次问题类别
        /// </summary>
        public string StrSecProblem1 { get; set; }
        /// <summary>
        /// 不达标点问题类别
        /// </summary>
        public string StrMainProblem2 { get; set; }
        /// <summary>
        /// 备份字段
        /// </summary>
        public string StrSecProblem2 { get; set; }
        /// <summary>
        /// 劣点类别(语音)
        /// </summary>
        public string StrMainProblem3 { get; set; }
        /// <summary>
        /// 劣点类别(数据)
        /// </summary>
        public string StrSecProblem3 { get; set; }
        /// <summary>
        /// 问题点位置
        /// </summary>
        public string StrTestSite { get; set; }
        /// <summary>
        /// 原因类别
        /// </summary>
        public string StrCauseType { get; set; }
        /// <summary>
        /// 文件ID列表
        /// </summary>
        public string StrLogInfo { get; set; }
        /// <summary>
        /// strCauseInfo信息
        /// </summary>
        public string StrCauseInfo { get; set; }
        public List<ProbCellInfo> probCellInfoList { get; set; }
        public List<string> fileList { get; set; }
        public ProblemInfoTem()
        {
            Dtime = DateTime.Now;
            SMainType = "";
            SSecondType = "";
            SPointPosition = "";
            STestFilePosition = "";
            SReasonAna = "";
            StrMainProblem1 = "";
            StrSecProblem1 = "";
            StrMainProblem2 = "";
            StrSecProblem2 = "";
            StrMainProblem3 = "";
            StrSecProblem3 = "";
            StrTestSite = "";
            StrLogInfo = "";
            StrCauseInfo = "";
            StrCauseType = "";
            probCellInfoList = new List<ProbCellInfo>();
            fileList = new List<string>();
        }
    }
    public class ProblemLoLaInfo
    {
        /// <summary>
        /// 地点areatype
        /// </summary>
        public int IAreaType { get; set; }
        /// <summary>
        /// 地点areaid
        /// </summary>
        public int IAreaId { get; set; }
        /// <summary>
        /// 地点经度
        /// </summary>
        public double DLongitude { get; set; }
        /// <summary>
        /// 地点纬度
        /// </summary>
        public double DLatitude { get; set; }
        public ProblemLoLaInfo()
        {
            IAreaType = 0;
            IAreaId = 0;
            DLongitude = 0;
            DLatitude = 0;
        }
    }
}
