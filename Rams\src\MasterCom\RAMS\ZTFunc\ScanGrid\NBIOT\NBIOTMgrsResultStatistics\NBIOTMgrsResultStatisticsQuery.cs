﻿using System;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsResultStatisticsQuery : DIYSQLBase
    {
        public NbIotMgrsResultStatisticsQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        #region 基础数据重写
        public override string Name
        {
            get { return "NBIOT扫频栅格分析"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }
        #endregion

        private string tableName;
        private string fileID;
        private int TLLng;
        private int TLLat;
        private int BRLng;
        private int BRLat;
        private NbIotMgrsResultInfo result = new NbIotMgrsResultInfo();

        public void SetQueryCondition(string tableName, string fileID, double x1, double y1, double x2, double y2)
        {
            this.tableName = tableName;
            this.fileID = fileID;
            TLLng = (int)(x1 * 10000000);
            TLLat = (int)(y1 * 10000000);
            BRLng = (int)(x2 * 10000000);
            BRLat = (int)(y2 * 10000000);
        }

        #region 查询流程
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                string strsql = getSqlTextString();
                E_VType[] retArrDef = getSqlRetTypeArr();//获得枚举类型数组
                package.Command = Command.DIYSearch;//枚举类型：DIY接口
                package.SubCommand = SubCommand.Request;//枚举类型：请求
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }
                package.Content.PrepareAddParam();
                package.Content.AddParam(strsql);
                StringBuilder sb = new StringBuilder();
                if (retArrDef != null)
                {
                    for (int i = 0; i < retArrDef.Length; i++)
                    {
                        sb.Append((int)retArrDef[i]);
                        sb.Append(",");
                    }
                }
                package.Content.AddParam(sb.ToString().TrimEnd(','));
                clientProxy.Send();
                receiveRetData(clientProxy);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }

        protected override string getSqlTextString()
        {
            string strSQL = string.Format(@"EXEC [dbo].[PROC_NB扫频数据分析_军事栅格_指标_网络性能] 
'{0}','表名#{1}|文件id#{2}|左上经度#{3}|左上纬度#{4}|右下经度#{5}|右下纬度#{6}'",
               MainModel.DistrictID, tableName, fileID, TLLng, TLLat, BRLng, BRLat);
            return strSQL;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[9];
            rType[0] = E_VType.E_Float;
            rType[1] = E_VType.E_Float;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Float;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Float;
            rType[8] = E_VType.E_Float;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    result = NbIotMgrsResultInfo.Fill(package.Content);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }
        #endregion

        public NbIotMgrsResultInfo GetResult()
        {
            return result;
        }
    }
}
