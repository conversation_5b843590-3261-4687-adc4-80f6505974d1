﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTNRWeakCoverRoadQuery : ZTWeakCoverRoadQueryBaseModel<ZTNRWeakCoverRoadQuery>
    {
        protected override string name { get { return "弱覆盖路段_NR"; } }
        protected override int type { get { return 2; } }
        protected override int funcId { get { return 35000; } }
        protected override int subfuncId { get { return 35001; } }
        public override string themeName { get { return "NR:SS_RSRP"; } }

        public ZTNRWeakCoverRoadQuery()
        {
            init();
        }

        private void init()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, true);
            Columns.Add("NR_APP_type");
            Columns.Add("NR_APP_Speed");
            Columns.Add("NR_Throughput_MAC_DL");
            Columns.Add("NR_Throughput_MAC_UL");

            tpRSRP = "NR_SS_RSRP";
            tpSINR = "NR_SS_SINR";
            tpTac = "NR_TAC";
            tpSpeed = "NR_APP_Speed_Mb";
            tpAppType = "NR_APP_type";
            tpStr = "NR_NCell_RSRP";
            tpNCell_EARFCN = "NR_NCell_ARFCN";
            tpNCell_PCI = "NR_NCell_PCI";

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                weakCondition = weakCovRoadCond;
                setRoadCond();
                saveTestPoints = false;
                return true;
            }

            NRWeakCoverRoadSettingDlg dlg = new NRWeakCoverRoadSettingDlg(weakCovRoadCond);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                weakCovRoadCond = dlg.GetCondition();
                weakCondition = weakCovRoadCond;
                setRoadCond();
                return true;
            }

            return false;
        }

        public override void setRoadCond()
        {
            PercentRoadCondition roadCond = new PercentRoadCondition(weakCovRoadCond.MinWeakPointPercent / 100, OnOneRoadComplete);
            roadCond.IsCheckDuration = weakCovRoadCond.CheckMinDuration;
            roadCond.MinDuration = weakCovRoadCond.MinDuration;
            roadCond.IsCheckMinLength = weakCovRoadCond.CheckMinDistance;
            roadCond.MinLength = weakCovRoadCond.MinCoverRoadDistance;
            roadCond.IsCheckMaxLength = false;
            roadCond.MaxLength = weakCovRoadCond.MaxCoverRoadDistance;
            roadCond.IsCheckDistanceGap = true;
            roadCond.MaxDistanceGap = weakCovRoadCond.MaxTPDistance;

            this.roadBuilder = new PercentRoadBuilder(roadCond);
        }

        protected override ICell getNBCell(TestPoint testPoint, int index)
        {
            NRCell nrCell = testPoint.GetNBCell_NR(index);
            return nrCell;
        }

        protected override ICell getMainCell(TestPoint testPoint)
        {
            NRCell nrCell = testPoint.GetMainCell_NR();
            return nrCell;
        }

        protected override bool JudgeWeakTP(TestPoint tp)
        {
            float? rsrp = getRsrp(tp);
            bool isWeakRsrp = weakCovRoadCond.IsWeakRSRP(rsrp);
            float? sinr = getSinr(tp);
            bool isWeakSinr = weakCovRoadCond.IsWeakSINR(sinr);
            float? nbRsrp = getNbMaxRSRP(tp);
            bool isWeakNbMaxRsrp = weakCovRoadCond.IsWeakNbMaxRSRP(nbRsrp);
            if (weakCovRoadCond.CheckCondAnd)
            {
                //且
                bool isWeak = isWeakRsrp && (!weakCovRoadCond.CheckSINR || (weakCovRoadCond.CheckSINR && isWeakSinr))
                    && (!weakCovRoadCond.CheckNbMaxRSRP || (weakCovRoadCond.CheckNbMaxRSRP && isWeakNbMaxRsrp));
                return isWeak;
            }
            else
            {
                //或
                bool isWeak = (isWeakRsrp || (weakCovRoadCond.CheckSINR && isWeakSinr))
                    && (!weakCovRoadCond.CheckNbMaxRSRP || (weakCovRoadCond.CheckNbMaxRSRP && isWeakNbMaxRsrp));
                return isWeak;
            }
        }

        protected override WeakCoverRoadResultBase initResult()
        {
            return new WeakCoverRoadResultNR();
        }

        protected override List<ICell> getCellInThisRect()
        {
            List<NRCell> cells = MainModel.CellManager.GetCurrentNRCells();
            List<ICell> cellInLst = new List<ICell>();

            foreach (NRCell cell in cells)
            {
                bool isValid = false;
                if (cell.BelongBTS.Type == NRBTSType.Outdoor)
                {
                    isValid = true;
                }
                if (isValid && sampleRect.IsPointInThisRect(cell.Longitude, cell.Latitude))
                {
                    cellInLst.Add(cell);
                }
            }

            return cellInLst;
        }

        protected override double getDistance(TestPoint testPoint, ICell cell)
        {
            double dis = 0;
            if (cell is NRCell)
            {
                NRCell nrCell = cell as NRCell;
                dis = nrCell.GetDistance(testPoint.Longitude, testPoint.Latitude);
            }

            return dis;
        }

        protected override void setWeakCoverRoadResult(TestPoint testPoint, WeakCoverRoadResultBase weakCover, double dis, int inNbCellIndex, Dictionary<float, ICell> nbCellDic)
        {
            base.setWeakCoverRoadResult(testPoint, weakCover, dis, inNbCellIndex, nbCellDic);

            double? macDLSpeed = NRTpHelper.NrTpManager.GetMacDLMb(testPoint);
            double? macULSpeed = NRTpHelper.NrTpManager.GetMacULMb(testPoint);

            ((WeakCoverRoadResultNR)weakCover).AddMacSpeed(macDLSpeed, macULSpeed);
        }

        protected override int? getTac(TestPoint tp)
        {
            return (int?)NRTpHelper.NrTpManager.GetTAC(tp);
        }

        protected override void fireShowForm()
        {
            MainModel.FireSetDefaultMapSerialTheme(this.themeName);
            WeakCoverRoadNRForm frm = MainModel.GetInstance().CreateResultForm(typeof(WeakCoverRoadNRForm)) as WeakCoverRoadNRForm;
            frm.weakCondition = weakCovRoadCond;

            List<WeakCoverRoadResultNR> nrWeakCoverList = new List<WeakCoverRoadResultNR>();
            if (weakCoverList != null)
            {
                foreach (var item in weakCoverList)
                {
                    nrWeakCoverList.Add(item as WeakCoverRoadResultNR);
                }
            }

            frm.FillData(nrWeakCoverList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void getResultsAfterQuery()
        {
            //算法不明,先不进行问题判断
        }

        public WeakCoverRoadConditionNR weakCovRoadCond { get; set; } = new WeakCoverRoadConditionNR();
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>()
                {
                    ["BackgroundStat"] = BackgroundStat,
                    ["MaxRSRP"] = weakCovRoadCond.MaxRSRP,
                    ["MinCoverRoadDistance"] = weakCovRoadCond.MinCoverRoadDistance,
                    ["MaxTPDistance"] = weakCovRoadCond.MaxTPDistance,
                    ["CheckAndOr"] = weakCovRoadCond.CheckCondAnd,
                    ["CheckSINR"] = weakCovRoadCond.CheckSINR,
                    ["MaxSINR"] = weakCovRoadCond.MaxSINR,
                    ["CheckNbMaxRSRP"] = weakCovRoadCond.CheckNbMaxRSRP,
                    ["compareSymble"] = "<"
                };
                if (weakCovRoadCond.compareSymble == CompareSymble.GreaterThan)
                {
                    param["compareSymble"] = ">";
                }
                param["NbMaxRSRP"] = weakCovRoadCond.NbMaxRSRP;
                param["MaxSampleCellDistance"] = weakCovRoadCond.MaxSampleCellDistance;
                param["MaxSampleCellAngle"] = weakCovRoadCond.MaxSampleCellAngle;
                param["MinWeakPointPercent"] = weakCovRoadCond.MinWeakPointPercent;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                BackgroundStat = getValid(param, "BackgroundStat", BackgroundStat);
                weakCovRoadCond.MaxRSRP = getValid(param, "MaxRSRP", weakCovRoadCond.MaxRSRP);
                weakCovRoadCond.MinCoverRoadDistance = getValid(param, "MinCoverRoadDistance", weakCovRoadCond.MinCoverRoadDistance);
                weakCovRoadCond.MaxTPDistance = getValid(param, "MaxTPDistance", weakCovRoadCond.MaxTPDistance);
                weakCovRoadCond.CheckCondAnd = getValid(param, "CheckAndOr", weakCovRoadCond.CheckCondAnd);
                weakCovRoadCond.CheckSINR = getValid(param, "CheckSINR", weakCovRoadCond.CheckSINR);
                weakCovRoadCond.MaxSINR = getValid(param, "MaxSINR", weakCovRoadCond.MaxSINR);
                weakCovRoadCond.CheckNbMaxRSRP = getValid(param, "CheckNbMaxRSRP", weakCovRoadCond.CheckNbMaxRSRP);

                if (param.ContainsKey("compareSymble"))
                {
                    if (param["compareSymble"].ToString().Contains("<"))
                    {
                        weakCovRoadCond.compareSymble = CompareSymble.LessThan;
                    }
                    else if (param["compareSymble"].ToString().Contains(">"))
                    {
                        weakCovRoadCond.compareSymble = CompareSymble.GreaterThan;
                    }
                }

                weakCovRoadCond.NbMaxRSRP = getValid(param, "NbMaxRSRP", (int)weakCovRoadCond.NbMaxRSRP);
                weakCovRoadCond.MaxSampleCellDistance = getValid(param, "MaxSampleCellDistance", weakCovRoadCond.MaxSampleCellDistance);
                weakCovRoadCond.MaxSampleCellAngle = getValid(param, "MaxSampleCellAngle", weakCovRoadCond.MaxSampleCellAngle);
                weakCovRoadCond.MinWeakPointPercent = getValid(param, "MinWeakPointPercent", (int)weakCovRoadCond.MinWeakPointPercent);
            }
        }

        private int getValid(Dictionary<string, object> param, string name, int defaultData)
        {
            if (param.ContainsKey(name))
            {
                return int.Parse(param[name].ToString());
            }
            return defaultData;
        }

        private T getValid<T>(Dictionary<string, object> param, string name, T defaultData)
        {
            if (param.ContainsKey(name))
            {
                return (T)param[name];
            }
            return defaultData;
        }
    }

    public class WeakCoverRoadConditionNR : WeakCoverRoadConditionBase
    {
        public WeakCoverRoadConditionNR()
        {
            MaxRSRP = -88;
        }
        public bool CheckCondAnd { get; set; }
        public float MaxSINR { get; set; } = -3;
        public bool CheckSINR { get; set; }
        public float NbMaxRSRP { get; set; } = -95;
        public bool CheckNbMaxRSRP { get; set; }

        public bool CheckMinDistance { get; set; } = true;
        public bool CheckMinDuration { get; set; } = false;

        public double MaxCoverRoadDistance { get; set; } = 5000;//最大持续距离
        public double MinDuration { get; set; } = 10;//最小持续时长

        public CompareSymble compareSymble { get; set; } = CompareSymble.LessThan;

        public override bool MatchMinWeakCoverDistance(double distance)
        {
            if (CheckMinDistance)
            {
                return distance >= MinCoverRoadDistance;
            }
            else
            {
                return true;
            }
        }

        public override bool MatchMinWeakCoverDuration(double duration)
        {
            if (CheckMinDuration)
            {
                return duration >= MinDuration;
            }
            else
            {
                return true;
            }
        }

        public bool IsWeakRSRP(float? rxLev)
        {
            return rxLev <= MaxRSRP && rxLev >= -141;
        }

        public bool IsWeakSINR(float? sinr)
        {
            return sinr < MaxSINR && sinr >= -50;
        }

        public bool IsWeakNbMaxRSRP(float? nbRSRP)
        {
            if (compareSymble == CompareSymble.LessThan)
            {
                return nbRSRP < NbMaxRSRP && nbRSRP >= -141;
            }
            return nbRSRP > NbMaxRSRP && nbRSRP >= -141;
        }
    }

    public class WeakCoverRoadResultNR : WeakCoverRoadResultBase
    {
        public DataInfo LteRsrpData { get; set; } = new DataInfo();
        public DataInfo LteSinrData { get; set; } = new DataInfo();

        public SpeedInfo MacDownData { get; set; } = new SpeedInfo();
        public SpeedInfo MacUpData { get; set; } = new SpeedInfo();

        public List<string> ArfcnPciList { get; set; } = new List<string>();
        public string ArfcnPcis_plan { get; protected set; }

        public void AddMacSpeed(double? macDLSpeed, double? macULSpeed)
        {
            if (macDLSpeed != null)
            {
                MacDownData.ValidCnt++;
                MacDownData.Total += (double)macDLSpeed;
                if (macDLSpeed < 150)
                {
                    MacDownData.LowSpeedCnt++;
                }
            }

            if (macULSpeed != null)
            {
                MacUpData.ValidCnt++;
                MacUpData.Total += (double)macULSpeed;
                if (macULSpeed < 2)
                {
                    MacUpData.LowSpeedCnt++;
                }
            }
        }

        public override void AddOtherTPInfo(TestPoint testPoint)
        {
            float? lteRsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(testPoint, true);
            if (lteRsrp != null)
            {
                LteRsrpData.ValidCnt++;
                LteRsrpData.Total += (float)lteRsrp;
            }

            float? lteSinr = NRTpHelper.NrLteTpManager.GetSCellSinr(testPoint, true);
            if (lteSinr != null)
            {
                LteSinrData.ValidCnt++;
                LteSinrData.Total += (float)lteSinr;
            }
        }

        public override void AddProblemInfo(int? lac, ICell cell, List<string> lacciList, List<string> cellNames)
        {
            if (cell is NRCell)
            {
                NRCell nrCell = cell as NRCell;
                //string lacci = nrCell.TAC + "_" + nrCell.NCI;
                //if (!lacciList.Contains(lacci))
                //{
                //    lacciList.Add(lacci);
                //}

                string arfcnPci = nrCell.SSBARFCN + "_" + nrCell.PCI;
                if (!ArfcnPciList.Contains(arfcnPci))
                {
                    ArfcnPciList.Add(arfcnPci);
                }

                if (!cellNames.Contains(cell.Name))
                {
                    cellNames.Add(cell.Name);
                }
            }
        }

        public override void SetFinalData(int areaID, int areaTypeID)
        {
            base.SetFinalData(areaID, areaTypeID);

            if (TestPoints.Count > 0)
            {
                MacDownData.SetResult();
                MacUpData.SetResult();

                LteRsrpData.SetResult();
                LteSinrData.SetResult();
            }
        }

        public override void CalulateFinalRes()
        {
            ArfcnPcis_plan = setListTostr(ArfcnPciList);
            //LACCIs_plan = setListTostr(LacciList_plan);
            CellName_plan = setListTostr(CellNames_plan);
            //LACCIs_opt = setListTostr(LacciList_opt);
            //CellName_opt = setListTostr(CellNames_opt);

            TestPointRate_Plan = (float)Math.Round((float)100 * TestPointCount_Plan / (float)TestPoints.Count, 2);
            //TestPointRate_Opt = (float)Math.Round((float)100 * TestPointCount_Opt / (float)TestPoints.Count, 2);
        }
    }

    class ZTNRWeakCoverRoadQueryByFile : ZTNRWeakCoverRoadQuery
    {
        private static ZTNRWeakCoverRoadQueryByFile instance = null;
        public new static ZTNRWeakCoverRoadQueryByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTNRWeakCoverRoadQueryByFile();
            }
            return instance;
        }

        protected override string name { get { return "弱覆盖路段_NR(按文件)"; } }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isTPInRegion(TestPoint tp)
        {
            return true;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
