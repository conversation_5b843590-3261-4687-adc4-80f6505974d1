﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Net
{
    public class DIYReplayFileWithinPeriodQuery : DIYReplayFileQuery
    {
        public DIYReplayFileWithinPeriodQuery(MainModel mainModel)
            : base(mainModel)
        {
            NeedAskDlg = true;
        }

        public bool NeedAskDlg
        {
            get;
            set;
        }

        DIYReplayOptionDlg replayOptionDlg = null;
        protected override DIYReplayContentOption getDIYReplayContent()
        {
            if (replayOptionDlg == null)
            {
                replayOptionDlg = new DIYReplayOptionDlg();
                replayOptionDlg.InitLoadInfo("", "隐藏");
            }
            if (Condition.FileInfos.Count > 0)
            {
                int svtype = Condition.FileInfos[0].ServiceType;
                replayOptionDlg.FillCurrentServiceType(svtype);

                return getOption();
            }
            else
            {
                MessageBox.Show("没有可用的配置读取！");
                return null;
            }
        }

        private DIYReplayContentOption getOption()
        {
            if (NeedAskDlg)
            {
                if (replayOptionDlg.ShowDialog() == DialogResult.OK)
                {
                    return replayOptionDlg.GetSelectedReplayOption();
                }
                return null;
            }
            else
            {
                DIYReplayContentOption option = replayOptionDlg.GetCurDeepestOption();
                if (option == null)
                {
                    MessageBox.Show("没有可用的回放配置，请设置回放内容!");
                    if (replayOptionDlg.ShowDialog() == DialogResult.OK)
                    {
                        return replayOptionDlg.GetSelectedReplayOption();
                    }
                    return null;
                }
                else
                {
                    option.EventInclude = true;
                    option.MessageInclude = true;
                    option.MessageL3HexCode = true;
                    return option;
                }
            }
        }

        public override string Name
        {
            get { return "指定时间段文件回放"; }
        }

        public override string IconName
        {
            get { return "Images/replay.gif"; }
        }
        protected override void prepareStatPackage_Sample_SampleFilter(Package package)
        {
            prepareStatPackage(package);
        }
        protected override void prepareStatPackage_Event_EventFilter(Package package)
        {
            prepareStatPackage(package);
        }
        protected override void prepareStatPackage_Message_MessageFilter(Package package)
        {
            prepareStatPackage(package);
        }

        private void prepareStatPackage(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.iTimeSelect);
            QueryCondition cond = GetQueryCondition();
            TimePeriod tp = cond.Periods[0];
            package.Content.AddParam((int)(JavaDate.GetMilliseconds(tp.BeginTime) / 1000));
            package.Content.AddParam((int)(JavaDate.GetMilliseconds(tp.EndTime) / 1000));
            AddDIYEndOpFlag(package);
        }
    }

    public class ReplayFileInPeriodQueryByDIYContent : DIYReplayFileWithinPeriodQuery
    {
        public ReplayFileInPeriodQueryByDIYContent(MainModel mainModel)
            : base(mainModel)
        {
        }
        public DIYReplayContentOption DIYReplayContent { get; set; }
        protected override DIYReplayContentOption getDIYReplayContent()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return DIYReplayContent;
            }
            else
            {
                MessageBox.Show("没有可用的配置读取！");
                return null;
            }
        }
        protected override void query()//回放查询入口
        {
            replayContentOption = getDIYReplayContent();//获取查询指标（内容）
            if (replayContentOption == null)
            {
                return;
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, condition.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = "正在查询...";
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.IsDrawEventResult = false;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                MainModel.IsFileReplayByMTRMode = condition.isMTRMode;
                MainModel.IsFileReplayByMTRToLogMode = condition.isMTRToLogMode;
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                doPostReplayAction();
                if (replayContentOption.DefaultSerialThemeName != null)
                {
                    MainModel.FireSetDefaultMapSerialTheme(replayContentOption.DefaultSerialThemeName);
                }

            }
            finally
            {
                //fireShowResult();
                //MainModel.FireDTDataChanged(this);
                clientProxy.Close();
            }
        }
    }
}

