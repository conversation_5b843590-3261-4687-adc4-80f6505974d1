﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Collections.ObjectModel;
using System.Drawing.Drawing2D;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Stat;
using System.IO;
using System.ComponentModel;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyAntennaPara : DIYSQLBase
    {
        readonly string strCity;
        readonly AntTimeCfg timeCfg;
        public DiyAntennaPara(MainModel mainModel, AntTimeCfg timeCfg)
            : base(mainModel)
        {
            strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            this.timeCfg = timeCfg;

            antParaEciDic = new Dictionary<int, AntennaPara>();
            antParaEciSDic = new Dictionary<int, AntennaPara>();
            antParaCellNameDic = new Dictionary<string, AntennaPara>();
        }

        protected override string getSqlTextString()
        {
            DateTime dDate = DateTime.Now;
            if (timeCfg.DEtime >= Convert.ToDateTime("2015-06-01"))
                dDate = timeCfg.DEtime;

            string strSql = @"exec DTASYSTEM.dbo.sp_auto_antenna_para '" + strCity + "','" + dDate + "'";
#if DEBUG
            log.Info("查询SQL：" + strSql);
#endif
            return strSql;
        }

        public override string Name
        {
            get { return "DiyAntennaPara"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[31];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_String;
            rType[9] = E_VType.E_String;

            rType[10] = E_VType.E_Float;
            rType[11] = E_VType.E_Float;
            rType[12] = E_VType.E_Float;
            rType[13] = E_VType.E_Float;
            rType[14] = E_VType.E_Float;
            rType[15] = E_VType.E_Float;
            rType[16] = E_VType.E_Float;
            rType[17] = E_VType.E_Float;

            rType[18] = E_VType.E_Float;
            rType[19] = E_VType.E_Float;
            rType[20] = E_VType.E_Float;
            rType[21] = E_VType.E_Float;
            rType[22] = E_VType.E_Float;
            rType[23] = E_VType.E_Float;
            rType[24] = E_VType.E_Float;
            rType[25] = E_VType.E_Float;

            rType[26] = E_VType.E_String;
            rType[27] = E_VType.E_Float;
            rType[28] = E_VType.E_Float;
            rType[29] = E_VType.E_Float;
            rType[30] = E_VType.E_String;

            return rType;
        }

        public Dictionary<int, AntennaPara> antParaEciDic { get; set; }
        public Dictionary<int, AntennaPara> antParaEciSDic { get; set; }
        public Dictionary<string, AntennaPara> antParaCellNameDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            AntennaPara aPara = new AntennaPara();
            aPara.strcity = package.Content.GetParamString();
            aPara.strdevvender = package.Content.GetParamString();
            aPara.strcellname = package.Content.GetParamString();
            aPara.strcellnameen = package.Content.GetParamString();
            aPara.ienodebid = package.Content.GetParamInt();
            aPara.isectorid = package.Content.GetParamInt();
            aPara.strcgi = package.Content.GetParamString();
            aPara.strtype = package.Content.GetParamString();
            aPara.strbandtype = package.Content.GetParamString();
            aPara.strbeamwidth = package.Content.GetParamString();

            aPara.drangeport1 = package.Content.GetParamFloat();
            aPara.drangeport2 = package.Content.GetParamFloat();
            aPara.drangeport3 = package.Content.GetParamFloat();
            aPara.drangeport4 = package.Content.GetParamFloat();
            aPara.drangeport5 = package.Content.GetParamFloat();
            aPara.drangeport6 = package.Content.GetParamFloat();
            aPara.drangeport7 = package.Content.GetParamFloat();
            aPara.drangeport8 = package.Content.GetParamFloat();

            aPara.dphaseport1 = package.Content.GetParamFloat();
            aPara.dphaseport2 = package.Content.GetParamFloat();
            aPara.dphaseport3 = package.Content.GetParamFloat();
            aPara.dphaseport4 = package.Content.GetParamFloat();
            aPara.dphaseport5 = package.Content.GetParamFloat();
            aPara.dphaseport6 = package.Content.GetParamFloat();
            aPara.dphaseport7 = package.Content.GetParamFloat();
            aPara.dphaseport8 = package.Content.GetParamFloat();

            aPara.strWeightValue = package.Content.GetParamString();
            aPara.GMax = package.Content.GetParamFloat();
            aPara._3dbValue = package.Content.GetParamFloat();
            aPara._6dbValue = package.Content.GetParamFloat();
            aPara.isSmartAnt = package.Content.GetParamString();

            aPara.iStatus = 1;

            int iECI = aPara.ienodebid * 256 + aPara.isectorid;
            if (!antParaEciDic.ContainsKey(iECI))
            {
                antParaEciDic.Add(iECI, aPara);
            }

            int isectorSubid = aPara.isectorid % 10;
            int ieciS = aPara.ienodebid * 256 + isectorSubid;
            if (!antParaEciSDic.ContainsKey(ieciS))
            {
                antParaEciSDic.Add(ieciS, aPara);
            }

            if (!antParaCellNameDic.ContainsKey(aPara.strcellname))
            {
                antParaCellNameDic.Add(aPara.strcellname, aPara);
            }
        }
    }

    public class DiyCfgPara : DIYSQLBase
    {
        readonly string strCity;
        public DiyCfgPara(MainModel mainModel)
            : base(mainModel)
        {
            strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            antCfgParaDic = new Dictionary<int, AntCfgSub>();
            antCfgParaSDic = new Dictionary<int, AntCfgSub>();
        }

        protected override string getSqlTextString()
        {
            string strSql = @"exec DTASYSTEM.dbo.PROC_状态库_LTE天线信息 '" + strCity + "'";
#if DEBUG
            log.Info("查询SQL：" + strSql);
#endif
            return strSql;
        }

        public override string Name
        {
            get { return "DiyCfgPara"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[18];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Float;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Float;
            rType[8] = E_VType.E_Float;
            rType[9] = E_VType.E_String;
            rType[10] = E_VType.E_String;
            rType[11] = E_VType.E_String;
            rType[12] = E_VType.E_String;
            rType[13] = E_VType.E_Float;
            rType[14] = E_VType.E_Float;
            rType[15] = E_VType.E_String;
            rType[16] = E_VType.E_String;
            rType[17] = E_VType.E_String;
            return rType;
        }

        public Dictionary<int, AntCfgSub> antCfgParaDic { get; set; }
        public Dictionary<int, AntCfgSub> antCfgParaSDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            try
            {
                AntCfgSub cPara = new AntCfgSub();
                cPara.strCellName = package.Content.GetParamString().Replace("\r", "").Replace("\n", "");
                cPara.iTAC = package.Content.GetParamInt();
                cPara.iEnodebID = package.Content.GetParamInt();
                cPara.iSectorID = package.Content.GetParamInt();

                cPara.预置下倾角 = package.Content.GetParamFloat();
                cPara.机械下倾角 = package.Content.GetParamFloat();
                cPara.电调下倾角 = package.Content.GetParamFloat();
                cPara.挂高 = package.Content.GetParamFloat();
                cPara.方向角 = package.Content.GetParamFloat();

                cPara.strCellNameEn = package.Content.GetParamString().Replace("\r", "").Replace("\n", "");
                cPara.strVender = package.Content.GetParamString();
                cPara.strType = package.Content.GetParamString();
                cPara.strBtsType = package.Content.GetParamString();
                cPara.fLongitude = package.Content.GetParamFloat();
                cPara.fLatitude = package.Content.GetParamFloat();
                cPara.场景类型 = package.Content.GetParamString();
                cPara.strCity = package.Content.GetParamString();
                cPara.strRegion = package.Content.GetParamString();

                int iECI = cPara.iEnodebID * 256 + cPara.iSectorID;
                if (!antCfgParaDic.ContainsKey(iECI))
                {
                    antCfgParaDic.Add(iECI, cPara);
                }

                int isectorSubid = cPara.iSectorID % 10;
                int ieciS = cPara.iEnodebID * 256 + isectorSubid;
                if (!antCfgParaSDic.ContainsKey(ieciS))
                {
                    antCfgParaSDic.Add(ieciS, cPara);
                }
            }
            catch (Exception e)
            {
                log.Error(e.Message);
            }
        }
    }

    public class DiyCellPara : DIYSQLBase
    {
        readonly string strCity;
        readonly AntTimeCfg timeCfg;
        public DiyCellPara(MainModel mainModel, AntTimeCfg timeCfg)
            : base(mainModel)
        {
            strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            this.timeCfg = timeCfg;
            cellParaEciDic = new Dictionary<int, CellPara>();
            cellParaEciSDic = new Dictionary<int, CellPara>();
        }

        protected override string getSqlTextString()
        {
            DateTime dDate = DateTime.Now;
            if (timeCfg.DEtime >= Convert.ToDateTime("2015-06-01"))
                dDate = timeCfg.DEtime;

            string strSql = @"exec DTASYSTEM.dbo.sp_auto_cell_para '" + strCity + "','" + dDate + "'";
#if DEBUG
            log.Info("查询SQL：" + strSql);
#endif
            return strSql;
        }

        public override string Name
        {
            get { return "DiyCellPara"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[10];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Float;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Float;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Float;
            rType[8] = E_VType.E_Float;
            rType[9] = E_VType.E_Float;

            return rType;
        }

        public Dictionary<int, CellPara> cellParaEciDic { get; set; }
        public Dictionary<int, CellPara> cellParaEciSDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            CellPara aPara = new CellPara();
            aPara.strCgi = package.Content.GetParamString();

            aPara.F上行吞吐量 = package.Content.GetParamFloat();
            aPara.F下行吞吐量 = package.Content.GetParamFloat();
            aPara.F上行平均速率 = package.Content.GetParamFloat();
            aPara.F下行平均速率 = package.Content.GetParamFloat();
            aPara.F无线接通率 = package.Content.GetParamFloat();
            aPara.F无线掉线率 = package.Content.GetParamFloat();
            aPara.F切换成功率 = package.Content.GetParamFloat();
            aPara.fERAB建立成功率 = package.Content.GetParamFloat();
            aPara.fERAB掉线率 = package.Content.GetParamFloat();

            int ienodebid = 0;
            int isectorid = 0;
            string[] cgi = aPara.strCgi.Split('-');
            if (cgi.Length == 4)
            {
                if (int.TryParse(cgi[2], out ienodebid) && int.TryParse(cgi[3], out isectorid))
                {
                    int iECI = ienodebid * 256 + isectorid;
                    if (!cellParaEciDic.ContainsKey(iECI))
                    {
                        cellParaEciDic.Add(iECI, aPara);
                    }

                    int isectorSubid = isectorid % 10;
                    int ieciS = ienodebid * 256 + isectorSubid;
                    if (!cellParaEciSDic.ContainsKey(ieciS))
                    {
                        cellParaEciSDic.Add(ieciS, aPara);
                    }
                }
                else
                {
                    //do nothing
                }
            }
        }
    }

    public class DiyLTECellMR : DIYSQLBase
    {
        readonly AntTimeCfg timeCfg;
        public DiyLTECellMR(MainModel mainModel, AntTimeCfg timeCfg)
            : base(mainModel)
        {
            this.timeCfg = timeCfg;
            lteMREciDic = new Dictionary<int, LteMrItem>();
            lteMREciSDic = new Dictionary<int, LteMrItem>();
        }

        protected override string getSqlTextString()
        {
            DateTime dDate = DateTime.Now;
            if (timeCfg.DEtime >= Convert.ToDateTime("2015-06-01"))
                dDate = timeCfg.DEtime;

            string strSql = @"exec dbo.sp_auto_antenna_ltemr '" + dDate + "'";
#if DEBUG
            log.Info("查询SQL：" + strSql);
#endif
            return strSql;
        }

        public override string Name
        {
            get { return "DiyLTECellMR"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[6];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Float;

            return rType;
        }

        public Dictionary<int, LteMrItem> lteMREciDic { get; set; }
        public Dictionary<int, LteMrItem> lteMREciSDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    LteMrItem mrItem = new LteMrItem();
                    mrItem.iEnodebId = package.Content.GetParamInt();
                    mrItem.iSectorId = package.Content.GetParamInt();
                    mrItem.dAvgRsrp = package.Content.GetParamFloat();
                    mrItem.dAvgSinr = package.Content.GetParamFloat();
                    mrItem.dRate110Rsrp = package.Content.GetParamFloat();
                    mrItem.dRate95Rsrp = package.Content.GetParamFloat();

                    int iECI = mrItem.iEnodebId * 256 + mrItem.iSectorId;
                    if (!lteMREciDic.ContainsKey(iECI))
                    {
                        lteMREciDic.Add(iECI, mrItem);
                    }

                    int isectorSubid = mrItem.iSectorId % 10;
                    int ieciS = mrItem.iEnodebId * 256 + isectorSubid;
                    if (!lteMREciSDic.ContainsKey(ieciS))
                    {
                        lteMREciSDic.Add(ieciS, mrItem);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiyLTEMRData : DIYSQLBase
    {
        int iArrayNum = 2;
        readonly int iFunc = 0;
        readonly int iNum = 0;
        readonly AntTimeCfg timeCfg;
        public DiyLTEMRData(MainModel mainModel, int iFunc, int iNum, AntTimeCfg timeCfg)
            : base(mainModel)
        {
            this.iFunc = iFunc;
            this.iNum = iNum;
            this.timeCfg = timeCfg;
            lteMREciDic = new Dictionary<int, ZTAntMRBaseItem>();
            lteMREciSDic = new Dictionary<int, ZTAntMRBaseItem>();
        }

        protected override string getSqlTextString()
        {
            iArrayNum = iNum;
            DateTime dDate = DateTime.Now;
            if (timeCfg.DEtime >= Convert.ToDateTime("2015-06-01"))
                dDate = timeCfg.DEtime;

            string strSql = @"exec dbo.sp_auto_antenna_ltemr_sample_get '" + dDate + "'," + iFunc ;
#if DEBUG
            log.Info("查询SQL：" + strSql);
#endif
            return strSql;
        }

        public override string Name
        {
            get { return "DiyLTEMRData"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[iArrayNum+3];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            for (int i = 2; i < iArrayNum + 2; i++)//含总采样点数字段
            {
                rType[i] = E_VType.E_Float;
            }
            rType[iArrayNum + 2] = E_VType.E_Float;
            return rType;
        }

        public Dictionary<int, ZTAntMRBaseItem> lteMREciDic { get; set; }
        public Dictionary<int, ZTAntMRBaseItem> lteMREciSDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            ZTAntMRBaseItem mrItem = new ZTAntMRBaseItem();
            mrItem.iEnodebId = package.Content.GetParamInt();
            mrItem.iCellId = package.Content.GetParamInt();

            for (int i = 0; i < iArrayNum; i++)//含总采样点数字段
            {
                if (iFunc == 4)
                {
                    int j = (0 - i + 72) % 72;
                    mrItem.dataValue[j] = (int)package.Content.GetParamFloat();
                }
                else
                {
                    mrItem.dataValue[i] = (int)package.Content.GetParamFloat();
                }
            }
            mrItem.iSampleNum = (int)package.Content.GetParamFloat();

            if (!lteMREciDic.ContainsKey(mrItem.iECI))
            {
                lteMREciDic.Add(mrItem.iECI, mrItem);
            }

            if (!lteMREciSDic.ContainsKey(mrItem.iSECI))
            {
                lteMREciSDic.Add(mrItem.iSECI, mrItem);
            }
        }
    }

    public class DiyLTEMRCover : DIYSQLBase
    {
        readonly AntTimeCfg timeCfg;
        public DiyLTEMRCover(MainModel mainModel,AntTimeCfg timeCfg)
            : base(mainModel)
        {
            this.timeCfg = timeCfg;
            lteMREciDic = new Dictionary<int, LteCoverItem>();
            lteMREciSDic = new Dictionary<int, LteCoverItem>();
        }

        protected override string getSqlTextString()
        {
            DateTime dDate = DateTime.Now;
            if (timeCfg.DEtime >= Convert.ToDateTime("2015-06-01"))
                dDate = timeCfg.DEtime;

            string strSql = @"exec dbo.sp_auto_antenna_ltemr_ana '" + dDate + "'";
#if DEBUG
            log.Info("查询SQL：" + strSql);
#endif
            return strSql;
        }

        public override string Name
        {
            get { return "DiyLTEMRCover"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[8];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;

            return rType;
        }

        public Dictionary<int, LteCoverItem> lteMREciDic { get; set; }
        public Dictionary<int, LteCoverItem> lteMREciSDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    LteCoverItem mrItem = new LteCoverItem();
                    mrItem.strCgi = package.Content.GetParamString();
                    mrItem.strCellName = package.Content.GetParamString();
                    mrItem.IMRO总采样点数 = package.Content.GetParamInt();
                    mrItem.I重叠覆盖条件采样点数 = package.Content.GetParamInt();
                    mrItem.F重叠覆盖指数 = 100 * package.Content.GetParamFloat();
                    mrItem.I过覆盖影响小区数 = package.Content.GetParamInt();
                    mrItem.S高重叠覆盖小区 = package.Content.GetParamString();
                    mrItem.S过覆盖小区 = package.Content.GetParamString();

                    if (!lteMREciDic.ContainsKey(mrItem.iEci))
                    {
                        lteMREciDic.Add(mrItem.iEci, mrItem);
                    }

                    if (!lteMREciSDic.ContainsKey(mrItem.iSubEci))
                    {
                        lteMREciSDic.Add(mrItem.iSubEci, mrItem);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiyCellParaRank : DIYSQLBase
    {
        readonly string strCity;
        readonly AntTimeCfg timeCfg;
        public DiyCellParaRank(MainModel mainModel,AntTimeCfg timeCfg)
            : base(mainModel)
        {
            strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            this.timeCfg = timeCfg;
            cellParaRankEciDic = new Dictionary<int, CellParaRank>();
            cellParaRankEciSDic = new Dictionary<int, CellParaRank>();
        }

        protected override string getSqlTextString()
        {
            DateTime dDate = DateTime.Now;
            if (timeCfg.DEtime >= Convert.ToDateTime("2015-06-01"))
                dDate = timeCfg.DEtime;

            string strSql = @"exec DTASYSTEM.dbo.sp_auto_para_rank_get '" + strCity + "','" + dDate + "'";
#if DEBUG
            log.Info("查询SQL：" + strSql);
#endif
            return strSql;
        }

        public override string Name
        {
            get { return "DiyCellParaRank"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[8];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx] = E_VType.E_Float;

            return rType;
        }

        public Dictionary<int, CellParaRank> cellParaRankEciDic { get; set; }
        public Dictionary<int, CellParaRank> cellParaRankEciSDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            CellParaRank aPara = new CellParaRank();
            aPara.地市 = package.Content.GetParamString();
            aPara.小区英文名 = package.Content.GetParamString();
            aPara.小区中文名 = package.Content.GetParamString();
            aPara.CGI = package.Content.GetParamString();

            aPara.FRank1的下行传输TB数 = 1000 * package.Content.GetParamFloat();
            aPara.FRank2的下行传输TB数 = 1000 * package.Content.GetParamFloat();
            aPara.FRank1的下行传输TB数比例 = 100 * package.Content.GetParamFloat();
            aPara.FRank2的下行传输TB数比例 = 100 * package.Content.GetParamFloat();
            //aPara.TM1下行传输TB数占比 = package.Content.GetParamFloat();
            //aPara.TM2下行传输TB数占比 = package.Content.GetParamFloat();
            //aPara.TM3下行传输TB数占比 = package.Content.GetParamFloat();
            //aPara.TM4下行传输TB数占比 = package.Content.GetParamFloat();
            //aPara.TM5下行传输TB数占比 = package.Content.GetParamFloat();
            //aPara.TM6下行传输TB数占比 = package.Content.GetParamFloat();
            //aPara.TM7下行传输TB数占比 = package.Content.GetParamFloat();
            //aPara.TM8下行传输TB数占比 = package.Content.GetParamFloat();
            //aPara.备注 = package.Content.GetParamString();

            int ienodebid = 0;
            int isectorid = 0;
            string[] cgi = aPara.CGI.Split('-');
            if (cgi.Length == 4)
            {
                if (int.TryParse(cgi[2], out ienodebid) && int.TryParse(cgi[3], out isectorid))
                {
                    int iECI = ienodebid * 256 + isectorid;
                    if (!cellParaRankEciDic.ContainsKey(iECI))
                    {
                        cellParaRankEciDic.Add(iECI, aPara);
                    }

                    int isectorSubid = isectorid % 10;
                    int ieciS = ienodebid * 256 + isectorSubid;
                    if (!cellParaRankEciSDic.ContainsKey(ieciS))
                    {
                        cellParaRankEciSDic.Add(ieciS, aPara);
                    }
                }
                else
                {
                    //do nothing
                }
            }
        }
    }
    
    public class DiyAntTimeCfg : DIYSQLBase
    {
        public DiyAntTimeCfg(MainModel mainModel)
            : base(mainModel)
        {
            strNet = "LTE";
            timeCfgDic = new Dictionary<string, AntTimeCfg>();
        }

        protected override string getSqlTextString()
        {
            string strSql = @"exec sp_auto_antenna_timecfg '" + strNet + "'";
            log.Info("查询SQL：" + strSql);
            return strSql;
        }

        public override string Name
        {
            get { return "DiyAntTimeCfg"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[2];
            rType[idx++] = E_VType.E_Int;
            rType[idx] = E_VType.E_Int;
            return rType;
        }

        public string strNet { get; set; }
        public Dictionary<string, AntTimeCfg> timeCfgDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    AntTimeCfg timeCfg = new AntTimeCfg();
                    timeCfg.ISitme = package.Content.GetParamInt();
                    timeCfg.IEitme = package.Content.GetParamInt();
                    if (!timeCfgDic.ContainsKey(timeCfg.StrDate))
                    {
                        timeCfgDic.Add(timeCfg.StrDate, timeCfg);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiyCfgRamsPara : DIYSQLBase
    {
        readonly string strNet;
        public DiyCfgRamsPara(MainModel mainModel,string strNet)
            : base(mainModel)
        {
            this.strNet = strNet;
            antCfgRamsParaDic = new Dictionary<LaiKey, ZTAntGsmTdCellInfo>();
        }

        protected override string getSqlTextString()
        {
            string strSql = @"exec dbo.PROC_23G小区信息_查询_天线分析 '" + this.strNet + "'";
            return strSql;
        }

        public override string Name
        {
            get { return "DiyCfgRamsPara"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[14];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_String;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_Int;
            rType[11] = E_VType.E_Int;
            rType[12] = E_VType.E_Int;
            rType[13] = E_VType.E_Int;
            return rType;
        }

        public Dictionary<LaiKey, ZTAntGsmTdCellInfo> antCfgRamsParaDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    try
                    {
                        ZTAntGsmTdCellInfo cPara = new ZTAntGsmTdCellInfo();
                        cPara.基站BTS名称 = package.Content.GetParamString();
                        cPara.基站经度 = Convert.ToDouble(1.0 * package.Content.GetParamInt() / 10000000);
                        cPara.基站纬度 = Convert.ToDouble(1.0 * package.Content.GetParamInt() / 10000000);
                        cPara.基站室分类型 = package.Content.GetParamString();
                        cPara.小区名称 = package.Content.GetParamString();
                        cPara.小区LAC = Convert.ToInt32(package.Content.GetParamString());
                        cPara.小区CI = Convert.ToInt32(package.Content.GetParamString());
                        cPara.小区频点 = Convert.ToInt32(package.Content.GetParamString());
                        cPara.小区扰码 = Convert.ToInt32(package.Content.GetParamString());
                        cPara.天线经度 = Convert.ToDouble(1.0 * package.Content.GetParamInt() / 10000000);
                        cPara.天线纬度 = Convert.ToDouble(1.0 * package.Content.GetParamInt() / 10000000);
                        cPara.天线方向角 = package.Content.GetParamInt();
                        cPara.天线下倾角 = package.Content.GetParamInt();
                        cPara.天线挂高 = package.Content.GetParamInt();

                        LaiKey cellKey = new LaiKey(cPara.小区LAC, cPara.小区CI);
                        if (!antCfgRamsParaDic.ContainsKey(cellKey))
                        {
                            antCfgRamsParaDic.Add(cellKey, cPara);
                        }
                    }
                    catch(Exception e)
                    {
                        log.Error(e.Message);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiyAntProjectCfg : DIYSQLBase
    {
        public DiyAntProjectCfg(MainModel mainModel)
            : base(mainModel)
        {
            StrProjectCfg = new string[2];
        }
        string strNet = "";
        int istime = 0;
        int ietime = 0;
        protected override string getSqlTextString()
        {
            string strSql = @"select distinct strdt_scan,strprojectid from tb_probchk_gsmcell_angle_result "
                          + " where istime = " + istime + " and ietime = " + ietime;
            if (this.strNet.Equals("TD"))
            {
                strSql = strSql.Replace("_gsmcell_", "_tdcell_");
            }
            if (this.strNet.Equals("LTE"))
            {
                strSql = @"select distinct 'scan' as strdt_scan,strprojectid from tb_probchk_cell_angle_result "
                       + " where istime = " + istime + " and ietime = " + ietime;
                strSql += " union all select distinct 'dt' as strdt_scan,strprojectid from tb_probchk_dt_cell_angle_result "
                       + " where istime = " + istime + " and ietime = " + ietime; 
            }
            return strSql;
        }

        public override string Name
        {
            get { return "DiyAntProjectCfg"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[2];
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        public void SetCondition(int istime, int ietime, string strNet)
        {
            this.istime = istime;
            this.ietime = ietime;
            this.strNet = strNet;
        }
        public string[] StrProjectCfg { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            StrProjectCfg = new string[2] { "", "" };
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
            if (StrProjectCfg[0].Length > 0)
            {
                StrProjectCfg[0] = StrProjectCfg[0].Remove(StrProjectCfg[0].Length - 1);
            }
            if (StrProjectCfg[1].Length > 0)
            {
                StrProjectCfg[1] = StrProjectCfg[1].Remove(StrProjectCfg[1].Length - 1);
            }
        }

        private void fillData(Package package)
        {
            string dt_scan = package.Content.GetParamString();
            string projectID = package.Content.GetParamString();
            if (dt_scan.ToLower().Equals("scan"))
            {
                if (!StrProjectCfg[0].Contains(projectID))
                {
                    StrProjectCfg[0] += projectID + ",";
                }
            }
            else if (dt_scan.ToLower().Equals("dt"))
            {
                if (!StrProjectCfg[1].Contains(projectID))
                {
                    StrProjectCfg[1] += projectID + ",";
                }
            }
            else
            {
                //
            }
        }
    }

    public class DiyAntSimulatorAssess : DIYSQLBase
    {
        public DiyAntSimulatorAssess(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            StringBuilder strSql = new StringBuilder();
            foreach (AntennaPara ant in AntennaParaList)
            {
                strSql.Append(string.Format("EXEC proc_tb_auto_antenna_权值模拟评估值 '{0}','{1}','{2}','{3}',{4},{5},'{6}','{7}','{8}','{9}','{10}',{11},{12},{13},{14},{15},{16},{17},{18},{19},{20},{21},{22},{23},{24},{25},{26},{27},{28},{29},'{30}' \r\n",
                    ant.strcity,
                    ant.strdevvender,
                    ant.strcellname,
                    ant.strcellnameen,
                    ant.ienodebid,
                    ant.isectorid,
                    ant.strWeightValue,
                    ant.strcgi,
                    ant.strtype,
                    ant.strbandtype,
                    ant.strbeamwidth,
                    ant.drangeport1,
                    ant.drangeport2,
                    ant.drangeport3,
                    ant.drangeport4,
                    ant.drangeport5,
                    ant.drangeport6,
                    ant.drangeport7,
                    ant.drangeport8,
                    ant.dphaseport1,
                    ant.dphaseport2,
                    ant.dphaseport3,
                    ant.dphaseport4,
                    ant.dphaseport5,
                    ant.dphaseport6,
                    ant.dphaseport7,
                    ant.dphaseport8,
                    ant.GMax,
                    ant._3dbValue,
                    ant._6dbValue,
                    ant.isSmartAnt));
            }
            return strSql.ToString();
        }

        public override string Name
        {
            get { return "权值模拟评估值入库"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[1];
            rType[idx] = E_VType.E_String;
            return rType;
        }

        public void SetCondition(List<AntennaPara> AntennaParaList)
        {
           this.AntennaParaList.AddRange(AntennaParaList);
        }
        readonly List<AntennaPara> AntennaParaList = new List<AntennaPara>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {

            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiyAntGetSimulatorAssess : DIYSQLBase
    {
        public DiyAntGetSimulatorAssess(MainModel mainModel)
            : base(mainModel)
        {
            antParaEciDic = new Dictionary<int, AntennaPara>();
            antParaEciSDic = new Dictionary<int, AntennaPara>();
            antParaCellNameDic = new Dictionary<string, AntennaPara>();
        }
       
        protected override string getSqlTextString()
        {
            string strSql = @"EXEC sp_tb_auto_antenna_权值模拟评估值";
            return strSql;
        }

        public Dictionary<int, AntennaPara> antParaEciDic { get; set; }
        public Dictionary<int, AntennaPara> antParaEciSDic { get; set; }
        public Dictionary<string, AntennaPara> antParaCellNameDic { get; set; }
        public override string Name
        {
            get { return "查询天线权值模拟评估值"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[31];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_String;
            rType[9] = E_VType.E_String;

            rType[10] = E_VType.E_Float;
            rType[11] = E_VType.E_Float;
            rType[12] = E_VType.E_Float;
            rType[13] = E_VType.E_Float;
            rType[14] = E_VType.E_Float;
            rType[15] = E_VType.E_Float;
            rType[16] = E_VType.E_Float;
            rType[17] = E_VType.E_Float;

            rType[18] = E_VType.E_Float;
            rType[19] = E_VType.E_Float;
            rType[20] = E_VType.E_Float;
            rType[21] = E_VType.E_Float;
            rType[22] = E_VType.E_Float;
            rType[23] = E_VType.E_Float;
            rType[24] = E_VType.E_Float;
            rType[25] = E_VType.E_Float;

            rType[26] = E_VType.E_String;
            rType[27] = E_VType.E_Float;
            rType[28] = E_VType.E_Float;
            rType[29] = E_VType.E_Float;
            rType[30] = E_VType.E_String;

            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            AntennaPara aPara = new AntennaPara();
            aPara.strcity = package.Content.GetParamString();
            aPara.strdevvender = package.Content.GetParamString();
            aPara.strcellname = package.Content.GetParamString();
            aPara.strcellnameen = package.Content.GetParamString();
            aPara.ienodebid = package.Content.GetParamInt();
            aPara.isectorid = package.Content.GetParamInt();
            aPara.strcgi = package.Content.GetParamString();
            aPara.strtype = package.Content.GetParamString();
            aPara.strbandtype = package.Content.GetParamString();
            aPara.strbeamwidth = package.Content.GetParamString();

            aPara.drangeport1 = package.Content.GetParamFloat();
            aPara.drangeport2 = package.Content.GetParamFloat();
            aPara.drangeport3 = package.Content.GetParamFloat();
            aPara.drangeport4 = package.Content.GetParamFloat();
            aPara.drangeport5 = package.Content.GetParamFloat();
            aPara.drangeport6 = package.Content.GetParamFloat();
            aPara.drangeport7 = package.Content.GetParamFloat();
            aPara.drangeport8 = package.Content.GetParamFloat();

            aPara.dphaseport1 = package.Content.GetParamFloat();
            aPara.dphaseport2 = package.Content.GetParamFloat();
            aPara.dphaseport3 = package.Content.GetParamFloat();
            aPara.dphaseport4 = package.Content.GetParamFloat();
            aPara.dphaseport5 = package.Content.GetParamFloat();
            aPara.dphaseport6 = package.Content.GetParamFloat();
            aPara.dphaseport7 = package.Content.GetParamFloat();
            aPara.dphaseport8 = package.Content.GetParamFloat();

            aPara.strWeightValue = package.Content.GetParamString();
            aPara.GMax = package.Content.GetParamFloat();
            aPara._3dbValue = package.Content.GetParamFloat();
            aPara._6dbValue = package.Content.GetParamFloat();
            aPara.isSmartAnt = package.Content.GetParamString();

            aPara.iStatus = 1;

            int iECI = aPara.ienodebid * 256 + aPara.isectorid;
            if (!antParaEciDic.ContainsKey(iECI))
            {
                antParaEciDic.Add(iECI, aPara);
            }

            int isectorSubid = aPara.isectorid % 10;
            int ieciS = aPara.ienodebid * 256 + isectorSubid;
            if (!antParaEciSDic.ContainsKey(ieciS))
            {
                antParaEciSDic.Add(ieciS, aPara);
            }

            if (!antParaCellNameDic.ContainsKey(aPara.strcellname))
            {
                antParaCellNameDic.Add(aPara.strcellname, aPara);
            }
        }
    }
    public class AntTimeCfg
    {
        public int ISitme { get; set; }
        public int IEitme { get; set; }

        public DateTime DStime
        {
            get
            {
                return JavaDate.GetDateTimeFromMilliseconds(ISitme * 1000L);
            }
        }

        public DateTime DEtime
        {
            get
            {
                return JavaDate.GetDateTimeFromMilliseconds((IEitme - 1) * 1000L);
            }
        }

        public string StrDate
        {
            get
            {
                return string.Format("{0:yyyyMMdd} - {1:yyyyMMdd}", DStime, DEtime);
            }
        }
    }

    public class AntennaPara
    {
        public string strcity{ get; set; }
        public string strdevvender{ get; set; }
        public string strcellname{ get; set; }
        public string strcellnameen{ get; set; }
        public int ienodebid{ get; set; }
        public int isectorid{ get; set; }
        public string strcgi{ get; set; }
        public string strtype{ get; set; }
        public string strbandtype{ get; set; }
        public string strbeamwidth{ get; set; }
        public string strWeightValue{ get; set; }
        public float dDownward{ get; set; }

        public float drangeport1{ get; set; }
        public float drangeport2{ get; set; }
        public float drangeport3{ get; set; }
        public float drangeport4{ get; set; }
        public float drangeport5{ get; set; }
        public float drangeport6{ get; set; }
        public float drangeport7{ get; set; }
        public float drangeport8{ get; set; }

        public float dphaseport1{ get; set; }
        public float dphaseport2{ get; set; }
        public float dphaseport3{ get; set; }
        public float dphaseport4{ get; set; }
        public float dphaseport5{ get; set; }
        public float dphaseport6{ get; set; }
        public float dphaseport7{ get; set; }
        public float dphaseport8{ get; set; }

        public float GMax{ get; set; }
        public float _3dbValue{ get; set; }
        public float _6dbValue{ get; set; }

        public string isSmartAnt{ get; set; }//是否智能天线

        /// <summary>
        /// 权值匹配状态,1为匹配
        /// </summary>
        public int iStatus{ get; set; }

        public AntennaPara()
        {
            strcity = "";
            strdevvender = "";
            strcellname = "";
            strcellnameen = "";
            ienodebid = 0;
            isectorid = 0;
            strcgi = "";
            strtype = "";
            strbandtype = "";
            strbeamwidth = "";
            strWeightValue = "";

            dDownward = 9999;
            drangeport1 = 9999;
            drangeport2 = 9999;
            drangeport3 = 9999;
            drangeport4 = 9999;
            drangeport5 = 9999;
            drangeport6 = 9999;
            drangeport7 = 9999;
            drangeport8 = 9999;

            dphaseport1 = 9999;
            dphaseport2 = 9999;
            dphaseport3 = 9999;
            dphaseport4 = 9999;
            dphaseport5 = 9999;
            dphaseport6 = 9999;
            dphaseport7 = 9999;
            dphaseport8 = 9999;
            iStatus = 0;

            GMax = 0;
            _3dbValue = 0;
            _6dbValue = 0;

            isSmartAnt = "";
        }
    }

    /// <summary>
    /// 状态库工参信息
    /// </summary>
    public class AntCfgSub
    {
        public string strCity{ get; set; }
        public string strRegion{ get; set; }
        public string strCellName{ get; set; }
        public string strCellNameEn{ get; set; }
        public string strVender{ get; set; }
        public string strType{ get; set; }
        public string strBtsType{ get; set; }

        public int iTAC{ get; set; }
        public int iEnodebID{ get; set; }
        public int iSectorID{ get; set; }
        public float 预置下倾角{ get; set; }
        public float 机械下倾角{ get; set; }
        public float 电调下倾角{ get; set; }
        public float 挂高{ get; set; }
        public float 方向角{ get; set; }

        public float fLongitude{ get; set; }
        public float fLatitude{ get; set; }

        public string 场景类型{ get; set; }

        public AntCfgSub()
        {
            strCity = "";
            strRegion = "";
            strCellName = "";
            strCellNameEn = "";
            strVender = "";
            strType = "";
            strBtsType = "";

            iTAC = 0;
            iEnodebID = 0;
            iSectorID = 0;

            预置下倾角 = 0;
            机械下倾角 = 0;
            电调下倾角 = 0;
            挂高 = 0;
            方向角 = 0;

            fLongitude = 0;
            fLatitude = 0;
            场景类型 = "";
        }
    }

    public class CellPara
    {
        public string strCgi { get; set; }
        [Description("上行吞吐量")]
        public float F上行吞吐量 { get; set; }
        [Description("下行吞吐量")]
        public float F下行吞吐量 { get; set; }
        [Description("上行平均速率")]
        public float F上行平均速率 { get; set; }
        [Description("下行平均速率")]
        public float F下行平均速率 { get; set; }
        [Description("无线接通率")]
        public float F无线接通率 { get; set; }
        [Description("无线掉线率")]
        public float F无线掉线率 { get; set; }
        [Description("切换成功率")]
        public float F切换成功率 { get; set; }
        [Description("ERAB建立成功率")]
        public float fERAB建立成功率 { get; set; }
        [Description("ERAB掉线率")]
        public float fERAB掉线率 { get; set; }

        public CellPara()
        {
            strCgi = "";
            F上行吞吐量 = 0;
            F下行吞吐量 = 0;
            F上行平均速率 = 0;
            F下行平均速率 = 0;
            F无线接通率 = 0;
            F无线掉线率 = 0;
            F切换成功率 = 0;
            fERAB建立成功率 = 0;
            fERAB掉线率 = 0;
        }
    }

    public class CellParaRank
    {
        public string 地市 { get; set; }
        public string 小区英文名 { get; set; }
        public string 小区中文名 { get; set; }
        public string CGI { get; set; }
        [Description("Rank1的下行传输TB数")]
        public float FRank1的下行传输TB数 { get; set; }
        [Description("Rank2的下行传输TB数")]
        public float FRank2的下行传输TB数 { get; set; }
        [Description("Rank1的下行传输TB数比例")]
        public float FRank1的下行传输TB数比例 { get; set; }
        [Description("Rank2的下行传输TB数比例")]
        public float FRank2的下行传输TB数比例 { get; set; }
        //public float TM1下行传输TB数占比;
        //public float TM2下行传输TB数占比;
        //public float TM3下行传输TB数占比;
        //public float TM4下行传输TB数占比;
        //public float TM5下行传输TB数占比;
        //public float TM6下行传输TB数占比;
        //public float TM7下行传输TB数占比;
        //public float TM8下行传输TB数占比;
        //public string 备注;

        public CellParaRank()
        {
            地市 = "";
            小区英文名 = "";
            小区中文名 = "";
            CGI = "";
            FRank1的下行传输TB数 = 0;
            FRank2的下行传输TB数 = 0;
            FRank1的下行传输TB数比例 = 0;
            FRank2的下行传输TB数比例 = 0;
            //TM1下行传输TB数占比 = 0;
            //TM2下行传输TB数占比 = 0;
            //TM3下行传输TB数占比 = 0;
            //TM4下行传输TB数占比 = 0;
            //TM5下行传输TB数占比 = 0;
            //TM6下行传输TB数占比 = 0;
            //TM7下行传输TB数占比 = 0;
            //TM8下行传输TB数占比 = 0;
            //备注 = "";
        }
    }

    public class SCANR0R1Item
    {
        public string 地市 { get; set; }
        public string 小区CGI { get; set; }
        public int 小区TAC { get; set; }
        public int 小区ECI { get; set; }
        public string RSRP最大差值 { get; set; }
        public string RSRP最大差值角度 { get; set; }
        public string 主瓣RSRP最大差值 { get; set; }
        public string 主瓣RSRP最大差值角度 { get; set; }
        [Description("R0覆盖率")]
        public float FR0覆盖率 { get; set; }
        [Description("R1覆盖率")]
        public float FR1覆盖率 { get; set; }
        [Description("RSRP0均值")]
        public float FRSRP0均值 { get; set; }
        [Description("RSRP1均值")]
        public float FRSRP1均值 { get; set; }
        [Description("DRSRP")]
        public float FDRSRP { get; set; }
        [Description("RSRP稳定度")]
        public float FRSRP稳定度 { get; set; }
        public float SINR0均值 { get; set; }
        public float SINR1均值 { get; set; }
        [Description("DSINR")]
        public float FDSINR { get; set; }
        [Description("SINR稳定度")]
        public float FSINR稳定度 { get; set; }
        public string cell360gt9dB扫描角 { get; set; }
        [Description("360双流功率差>9dB扫描角")]
        public float Cell360gt9dB扫描角
        {
            get
            {
                float tmp = 0;
                float.TryParse(cell360gt9dB扫描角.Replace("%", ""), out tmp);
                return tmp;
            }
        }
        public string cell360gt5dB扫描角 { get; set; }
        [Description("360双流功率差>5dB扫描角")]
        public float Cell360gt5dB扫描角
        {
            get
            {
                float tmp = 0;
                float.TryParse(cell360gt5dB扫描角.Replace("%", ""), out tmp);
                return tmp;
            }
        }
        public string cell360gt3dB扫描角 { get; set; }
        [Description("360双流功率差>3dB扫描角")]
        public float Cell360gt3dB扫描角
        {
            get
            {
                float tmp = 0;
                float.TryParse(cell360gt3dB扫描角.Replace("%", ""), out tmp);
                return tmp;
            }
        }
        public string 主瓣双流功率差gt9dB扫描角 { get; set; }
        [Description("主瓣双流功率差>9dB扫描角")]
        public float F主瓣双流功率差gt9dB扫描角
        {
            get
            {
                float tmp = 0;
                float.TryParse(主瓣双流功率差gt9dB扫描角.Replace("%", ""), out tmp);
                return tmp;
            }
        }
        public string 主瓣双流功率差gt5dB扫描角 { get; set; }
        [Description("主瓣双流功率差>5dB扫描角")]
        public float F主瓣双流功率差gt5dB扫描角
        {
            get
            {
                float tmp = 0;
                float.TryParse(主瓣双流功率差gt5dB扫描角.Replace("%", ""), out tmp);
                return tmp;
            }
        }
        public string 主瓣双流功率差gt3dB扫描角 { get; set; }
        [Description("主瓣双流功率差>3dB扫描角")]
        public float F主瓣双流功率差gt3dB扫描角
        {
            get
            {
                float tmp = 0;
                float.TryParse(主瓣双流功率差gt3dB扫描角.Replace("%", ""), out tmp);
                return tmp;
            }
        }
        [Description("主瓣RSRP0均值")]
        public float F主瓣RSRP0均值 { get; set; }
        [Description("主瓣RSRP1均值")]
        public float F主瓣RSRP1均值 { get; set; }
        [Description("主瓣DRSRP")]
        public float F主瓣DRSRP { get; set; }

        public SCANR0R1Item()
        {
            地市 = "";
            小区CGI = "";
            小区TAC = 0;
            小区ECI = 0;
            RSRP最大差值 = "";
            RSRP最大差值角度 = "";
            主瓣RSRP最大差值 = "";
            主瓣RSRP最大差值角度 = "";
            FR0覆盖率 = 0;
            FR1覆盖率 = 0;
            FRSRP0均值 = 0;
            FRSRP1均值 = 0;
            FDRSRP = 0;
            FRSRP稳定度 = 0;
            SINR0均值 = 0;
            SINR1均值 = 0;
            FDSINR = 0;
            FSINR稳定度 = 0;

            F主瓣RSRP0均值 = -140;
            F主瓣RSRP1均值 = -140;
            F主瓣DRSRP = 0;
        }
    }

    /// <summary>
    /// 复数运算类
    /// </summary>
    public class CalcComplex
    {
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public CalcComplex()
            : this(0, 0)
        {
        }

        /// <summary>
        /// 只有实部的构造函数
        /// </summary>
        /// <param name="Real">实部</param>
        public CalcComplex(double Real) : this(Real, 0) { }

        /// <summary>
        /// 由实部和虚部构造
        /// </summary>
        /// <param name="Real">实部</param>
        /// <param name="image">虚部</param>
        public CalcComplex(double real, double virt)
        {
            this.Real = real;
            this.Virt = virt;
        }
        
        /// <summary>
        /// 复数的实部
        /// </summary>
        public double Real { get; set; }
        /// <summary>
        /// 复数的虚部
        /// </summary>
        public double Virt { get; set; }

        ///重载加法
        public static CalcComplex operator +(CalcComplex c1, CalcComplex c2)
        {
            return new CalcComplex(c1.Real + c2.Real, c1.Virt + c2.Virt);
        }

        ///重载减法
        public static CalcComplex operator -(CalcComplex c1, CalcComplex c2)
        {
            return new CalcComplex(c1.Real - c2.Real, c1.Virt - c2.Virt);
        }

        ///重载乘法
        public static CalcComplex operator *(CalcComplex c1, CalcComplex c2)
        {
            return new CalcComplex(c1.Real * c2.Real - c1.Virt * c2.Virt, c1.Virt * c2.Real + c1.Real * c2.Virt);
        }

        /// <summary>
        /// 计算其指数的复数
        /// </summary>
        public static CalcComplex Imexp(CalcComplex cl)
        {
            double dTmpReal = 0;
            double dTmpVirt = 0;
            double dTmp = Math.Pow(Math.E, cl.Real);
            dTmpReal = dTmp * Math.Cos(cl.Virt);
            dTmpVirt = dTmp * Math.Sin(cl.Virt);
            return new CalcComplex(dTmpReal, dTmpVirt);
        }

        /// <summary>
        /// 求复数的模
        /// </summary>
        /// <returns>模</returns>
        public static double CalcModul(CalcComplex cc)
        {
            return Math.Sqrt(cc.Real * cc.Real + cc.Virt * cc.Virt);
        }

        /// <summary>
        /// IMABS
        /// </summary>
        public static double CalcImabs(CalcComplex cc)
        {
            return Math.Sqrt(Math.Pow(cc.Real, 2) + Math.Pow(cc.Virt, 2));
        }
    }

    public class AntParaItem
    {
        public string strVendor{ get; set; }
        public string strBandType{ get; set; }
        public double d{ get; set; }//单元间距
        public double k{ get; set; }//2Pi/l，其中l为波长

        public double w1{ get; set; }
        public double w2{ get; set; }
        public double w3{ get; set; }
        public double w4{ get; set; }

        public double delta1{ get; set; }
        public double delta2{ get; set; }
        public double delta3{ get; set; }
        public double delta4{ get; set; }

        public AntParaItem()
        {
            strVendor = "";
            strBandType = "D";
            d = 0;
            k = 0;

            w1 = 1;//w5
            w2 = 1;//w6
            w3 = 1;//w7
            w4 = 1;//w8

            delta1 = 0;//delta5
            delta2 = 0;//delta6
            delta3 = 0;//delta7
            delta4 = 0;//delta8
        }

        public AntParaItem(string strBandType, double w1, double w2, double w3, double w4, string strVendor)
        {
            this.strVendor = strVendor;
            this.strBandType = strBandType;
            this.d = 0;
            this.k = 0;

            this.w1 = w1;//w5
            this.w2 = w2;//w6
            this.w3 = w3;//w7
            this.w4 = w4;//w8
        }

        public void Init(double delta1, double delta2, double delta3, double delta4)
        {
            this.delta1 = delta1;//delta5
            this.delta2 = delta2;//delta6
            this.delta3 = delta3;//delta7
            this.delta4 = delta4;//delta8
        }

        private double[] clacPowerArray()
        {
            double[] powerArray = new double[180];

            if (strBandType.IndexOf("D") >= 0)
            {
                d = 0.075;
                k = 2 * Math.PI / 0.12;
                double[] DipoleAF_D = getDipoleAFForDBand();

                for (int i = 0; i < 180; i++)
                {
                    double af = DipoleAF_D[i];
                    CalcComplex comp = calcAF1Value(i, af) + calcAF2Value(i, af) + calcAF3Value(i, af) + calcAF4Value(i, af);
                    double absValue = Math.Pow(CalcComplex.CalcImabs(comp), 2);
                    double logValue = 10 * Math.Log10(absValue);
                    powerArray[i] = logValue;
                }
            }
            else if (strBandType.IndexOf("F") >= 0)
            {
                d = 0.075;
                k = 2 * Math.PI / (300000000.0 / 1900000000);
                double[] DipoleAF_F = getDipoleAFForFBand();

                for (int i = 0; i < 180; i++)
                {
                    double af = DipoleAF_F[i];
                    CalcComplex comp = calcAF1Value(i, af) + calcAF2Value(i, af) + calcAF3Value(i, af) + calcAF4Value(i, af);
                    double absValue = Math.Pow(CalcComplex.CalcImabs(comp), 2);
                    double logValue = 10 * Math.Log10(absValue);
                    powerArray[i] = logValue;
                }
            }
            else
            {
                for (int i = 0; i < 180; i++)
                {
                    powerArray[i] = 0;
                }
            }

            return powerArray;
        }

        /// <summary>
        /// 判断是否特殊权值
        /// </summary>
        private bool isOtherPowerArray()
        {
            int i0Num = 0;
            int i180Num = 0;
            if (this.delta1 == 0)
                i0Num++;
            else if (this.delta1 == 180)
                i180Num++;

            if (this.delta2 == 0)
                i0Num++;
            else if (this.delta2 == 180)
                i180Num++;

            if (this.delta3 == 0)
                i0Num++;
            else if (this.delta3 == 180)
                i180Num++;

            if (this.delta4 == 0)
                i0Num++;
            else if (this.delta4 == 180)
                i180Num++;

            if (i0Num == 3 && i180Num == 1)
                return true;
            else
                return false;
        }

        public double[] getPowerArray()
        {
            double[] powerArray = new double[180];
            if (isOtherPowerArray())
            {
                if (strBandType.IndexOf("D") >= 0)
                {
                    return getOtherPowerArray();
                }
                else
                {
                    return getPowerArray(powerArray);
                }
            }
            else if ((strVendor == "华为" || strVendor == "爱立信") 
                      && this.w1 == 0 && this.w2 == 0 && this.w3 == 0 && this.w4 == 0)
            {
                delta1 = 0;
                delta2 = 0;
                delta3 = 0;
                delta4 = 180; 
                return getOtherPowerArray();
            }
            else
            {
                return clacPowerArray();
            }
        }

        private double[] getPowerArray(double[] powerArray)
        {
            if (this.w1 == 0 && this.w2 == 0 && this.w3 == 0 && this.w4 == 0)
            {
                for (int i = 0; i < 180; i++)
                {
                    powerArray[i] = 0;
                }
            }
            return powerArray;
        }

        /// <summary>
        /// 获取特殊权值
        /// </summary>
        private double[] getOtherPowerArray()
        {
            w1 = 1;
            w2 = 1;
            w3 = 0;
            w4 = 0;
            double[] powerArray1 = clacPowerArray();

            w1 = 0;
            w2 = 0;
            w3 = 1;
            w4 = 1;
            double[] powerArray2 = clacPowerArray();

            double[] powerArray = getSumPowerArray(powerArray1, powerArray2);

            return powerArray;
        }

        private CalcComplex calcAF1Value(int i, double af)
        {
            double tmpValue = -1.5 * k * d * Math.Cos(i * Math.PI / 180) + delta1 * Math.PI / 180;
            CalcComplex compTmp1 = new CalcComplex(0, tmpValue);
            CalcComplex compTmp2 = new CalcComplex(w1 * af, 0);
            return CalcComplex.Imexp(compTmp1) * compTmp2;
        }

        private CalcComplex calcAF2Value(int i, double af)
        {
            double tmpValue = -0.5 * k * d * Math.Cos(i * Math.PI / 180) + delta2 * Math.PI / 180;
            CalcComplex compTmp1 = new CalcComplex(0, tmpValue);
            CalcComplex compTmp2 = new CalcComplex(w2 * af, 0);
            return CalcComplex.Imexp(compTmp1) * compTmp2;
        }

        private CalcComplex calcAF3Value(int i, double af)
        {
            double tmpValue = 0.5 * k * d * Math.Cos(i * Math.PI / 180) + delta3 * Math.PI / 180;
            CalcComplex compTmp1 = new CalcComplex(0, tmpValue);
            CalcComplex compTmp2 = new CalcComplex(w3 * af, 0);
            return CalcComplex.Imexp(compTmp1) * compTmp2;
        }

        private CalcComplex calcAF4Value(int i, double af)
        {
            double tmpValue = 1.5 * k * d * Math.Cos(i * Math.PI / 180) + delta4 * Math.PI / 180;
            CalcComplex compTmp1 = new CalcComplex(0, tmpValue);
            CalcComplex compTmp2 = new CalcComplex(w4 * af, 0);
            return CalcComplex.Imexp(compTmp1) * compTmp2;
        }

        /// <summary>
        /// 取两数组中的大值
        /// </summary>
        public static double[] getMaxPowerArray(double[] power1Array, double[] power2Array)
        {
            double[] powerArray = new double[180];
            for (int i = 0; i < 180; i++)
            {
                double dTmp = power1Array[i] >= power2Array[i] ? power1Array[i] : power2Array[i];
                powerArray[i] = dTmp > -120 ? dTmp : -120;
            }
            return powerArray;
        }

        /// <summary>
        /// 取两数组中之和
        /// </summary>
        public static double[] getSumPowerArray(double[] power1Array, double[] power2Array)
        {
            double[] powerArray = new double[180];
            for (int i = 0; i < 180; i++)
            {
                double tmpPower = Math.Pow(10, power1Array[i] / 10) + Math.Pow(10, power2Array[i] / 10);
                powerArray[i] = Math.Log10(tmpPower) * 10;
            }
            return powerArray;
        }

        /// <summary>
        /// D频段天线
        /// </summary>
        private double[] getDipoleAFForDBand()
        {
            double[] DipoleAF_D = new double[181];
            DipoleAF_D[0] = 0.472829040019105d;
            DipoleAF_D[1] = 0.501330653855900d;
            DipoleAF_D[2] = 0.531202799355181d;
            DipoleAF_D[3] = 0.562486917621896d;
            DipoleAF_D[4] = 0.595224053881604d;
            DipoleAF_D[5] = 0.629454723722028d;
            DipoleAF_D[6] = 0.665218773286685d;
            DipoleAF_D[7] = 0.702555233632734d;
            DipoleAF_D[8] = 0.741502169504886d;
            DipoleAF_D[9] = 0.782096522817892d;
            DipoleAF_D[10] = 0.824373951181494d;
            DipoleAF_D[11] = 0.868368661843676d;
            DipoleAF_D[12] = 0.914113241470250d;
            DipoleAF_D[13] = 0.961638482221127d;
            DipoleAF_D[14] = 1.010973204625700d;
            DipoleAF_D[15] = 1.062144077801490d;
            DipoleAF_D[16] = 1.115175437600960d;
            DipoleAF_D[17] = 1.170089103311700d;
            DipoleAF_D[18] = 1.226904193573240d;
            DipoleAF_D[19] = 1.285636942211360d;
            DipoleAF_D[20] = 1.346300514725470d;
            DipoleAF_D[21] = 1.408904826197670d;
            DipoleAF_D[22] = 1.473456361422500d;
            DipoleAF_D[23] = 1.539957998084020d;
            DipoleAF_D[24] = 1.608408833831010d;
            DipoleAF_D[25] = 1.678804018122560d;
            DipoleAF_D[26] = 1.751134589733220d;
            DipoleAF_D[27] = 1.825387320820450d;
            DipoleAF_D[28] = 1.901544568466320d;
            DipoleAF_D[29] = 1.979584134609740d;
            DipoleAF_D[30] = 2.059479135285890d;
            DipoleAF_D[31] = 2.141197880084230d;
            DipoleAF_D[32] = 2.224703762726660d;
            DipoleAF_D[33] = 2.309955163652190d;
            DipoleAF_D[34] = 2.396905365473750d;
            DipoleAF_D[35] = 2.485502482147130d;
            DipoleAF_D[36] = 2.575689402660050d;
            DipoleAF_D[37] = 2.667403750012980d;
            DipoleAF_D[38] = 2.760577856220350d;
            DipoleAF_D[39] = 2.855138754013090d;
            DipoleAF_D[40] = 2.951008185870170d;
            DipoleAF_D[41] = 3.048102630948140d;
            DipoleAF_D[42] = 3.146333350414240d;
            DipoleAF_D[43] = 3.245606451619990d;
            DipoleAF_D[44] = 3.345822971479330d;
            DipoleAF_D[45] = 3.446878979337690d;
            DipoleAF_D[46] = 3.548665699537220d;
            DipoleAF_D[47] = 3.651069653797980d;
            DipoleAF_D[48] = 3.753972823446950d;
            DipoleAF_D[49] = 3.857252831435130d;
            DipoleAF_D[50] = 3.960783143989600d;
            DipoleAF_D[51] = 4.064433291652130d;
            DipoleAF_D[52] = 4.168069109358760d;
            DipoleAF_D[53] = 4.271552995117380d;
            DipoleAF_D[54] = 4.374744186742240d;
            DipoleAF_D[55] = 4.477499056006880d;
            DipoleAF_D[56] = 4.579671419480060d;
            DipoleAF_D[57] = 4.681112865213990d;
            DipoleAF_D[58] = 4.781673094361360d;
            DipoleAF_D[59] = 4.881200276706650d;
            DipoleAF_D[60] = 4.979541419010590d;
            DipoleAF_D[61] = 5.076542744983070d;
            DipoleAF_D[62] = 5.172050085621040d;
            DipoleAF_D[63] = 5.265909278574230d;
            DipoleAF_D[64] = 5.357966575133420d;
            DipoleAF_D[65] = 5.448069053373590d;
            DipoleAF_D[66] = 5.536065035929220d;
            DipoleAF_D[67] = 5.621804510830010d;
            DipoleAF_D[68] = 5.705139553784650d;
            DipoleAF_D[69] = 5.785924750266770d;
            DipoleAF_D[70] = 5.864017615732180d;
            DipoleAF_D[71] = 5.939279012279750d;
            DipoleAF_D[72] = 6.011573560060440d;
            DipoleAF_D[73] = 6.080770041739230d;
            DipoleAF_D[74] = 6.146741798325050d;
            DipoleAF_D[75] = 6.209367114701960d;
            DipoleAF_D[76] = 6.268529593222690d;
            DipoleAF_D[77] = 6.324118513762190d;
            DipoleAF_D[78] = 6.376029178674410d;
            DipoleAF_D[79] = 6.424163241149270d;
            DipoleAF_D[80] = 6.468429015529620d;
            DipoleAF_D[81] = 6.508741768218240d;
            DipoleAF_D[82] = 6.545023987883390d;
            DipoleAF_D[83] = 6.577205633757290d;
            DipoleAF_D[84] = 6.605224360914280d;
            DipoleAF_D[85] = 6.629025721514610d;
            DipoleAF_D[86] = 6.648563341104670d;
            DipoleAF_D[87] = 6.663799069174630d;
            DipoleAF_D[88] = 6.674703103289500d;
            DipoleAF_D[89] = 6.681254086227940d;
            DipoleAF_D[90] = 6.683439175686150d;
            DipoleAF_D[91] = 6.681254086227940d;
            DipoleAF_D[92] = 6.674703103289500d;
            DipoleAF_D[93] = 6.663799069174630d;
            DipoleAF_D[94] = 6.648563341104670d;
            DipoleAF_D[95] = 6.629025721514610d;
            DipoleAF_D[96] = 6.605224360914280d;
            DipoleAF_D[97] = 6.577205633757290d;
            DipoleAF_D[98] = 6.545023987883390d;
            DipoleAF_D[99] = 6.508741768218240d;
            DipoleAF_D[100] = 6.468429015529620d;
            DipoleAF_D[101] = 6.424163241149270d;
            DipoleAF_D[102] = 6.376029178674410d;
            DipoleAF_D[103] = 6.324118513762190d;
            DipoleAF_D[104] = 6.268529593222690d;
            DipoleAF_D[105] = 6.209367114701960d;
            DipoleAF_D[106] = 6.146741798325050d;
            DipoleAF_D[107] = 6.080770041739230d;
            DipoleAF_D[108] = 6.011573560060440d;
            DipoleAF_D[109] = 5.939279012279750d;
            DipoleAF_D[110] = 5.864017615732180d;
            DipoleAF_D[111] = 5.785924750266770d;
            DipoleAF_D[112] = 5.705139553784650d;
            DipoleAF_D[113] = 5.621804510830010d;
            DipoleAF_D[114] = 5.536065035929220d;
            DipoleAF_D[115] = 5.448069053373590d;
            DipoleAF_D[116] = 5.357966575133420d;
            DipoleAF_D[117] = 5.265909278574230d;
            DipoleAF_D[118] = 5.172050085621040d;
            DipoleAF_D[119] = 5.076542744983070d;
            DipoleAF_D[120] = 4.979541419010590d;
            DipoleAF_D[121] = 4.881200276706650d;
            DipoleAF_D[122] = 4.781673094361360d;
            DipoleAF_D[123] = 4.681112865213990d;
            DipoleAF_D[124] = 4.579671419480060d;
            DipoleAF_D[125] = 4.477499056006880d;
            DipoleAF_D[126] = 4.374744186742240d;
            DipoleAF_D[127] = 4.271552995117380d;
            DipoleAF_D[128] = 4.168069109358760d;
            DipoleAF_D[129] = 4.064433291652130d;
            DipoleAF_D[130] = 3.960783143989600d;
            DipoleAF_D[131] = 3.857252831435130d;
            DipoleAF_D[132] = 3.753972823446950d;
            DipoleAF_D[133] = 3.651069653797980d;
            DipoleAF_D[134] = 3.548665699537220d;
            DipoleAF_D[135] = 3.446878979337690d;
            DipoleAF_D[136] = 3.345822971479330d;
            DipoleAF_D[137] = 3.245606451619990d;
            DipoleAF_D[138] = 3.146333350414240d;
            DipoleAF_D[139] = 3.048102630948140d;
            DipoleAF_D[140] = 2.951008185870170d;
            DipoleAF_D[141] = 2.855138754013090d;
            DipoleAF_D[142] = 2.760577856220350d;
            DipoleAF_D[143] = 2.667403750012980d;
            DipoleAF_D[144] = 2.575689402660050d;
            DipoleAF_D[145] = 2.485502482147130d;
            DipoleAF_D[146] = 2.396905365473750d;
            DipoleAF_D[147] = 2.309955163652190d;
            DipoleAF_D[148] = 2.224703762726660d;
            DipoleAF_D[149] = 2.141197880084230d;
            DipoleAF_D[150] = 2.059479135285890d;
            DipoleAF_D[151] = 1.979584134609740d;
            DipoleAF_D[152] = 1.901544568466320d;
            DipoleAF_D[153] = 1.825387320820450d;
            DipoleAF_D[154] = 1.751134589733220d;
            DipoleAF_D[155] = 1.678804018122560d;
            DipoleAF_D[156] = 1.608408833831010d;
            DipoleAF_D[157] = 1.539957998084020d;
            DipoleAF_D[158] = 1.473456361422500d;
            DipoleAF_D[159] = 1.408904826197670d;
            DipoleAF_D[160] = 1.346300514725470d;
            DipoleAF_D[161] = 1.285636942211360d;
            DipoleAF_D[162] = 1.226904193573240d;
            DipoleAF_D[163] = 1.170089103311700d;
            DipoleAF_D[164] = 1.115175437600960d;
            DipoleAF_D[165] = 1.062144077801490d;
            DipoleAF_D[166] = 1.010973204625700d;
            DipoleAF_D[167] = 0.961638482221127d;
            DipoleAF_D[168] = 0.914113241470250d;
            DipoleAF_D[169] = 0.868368661843676d;
            DipoleAF_D[170] = 0.824373951181494d;
            DipoleAF_D[171] = 0.782096522817892d;
            DipoleAF_D[172] = 0.741502169504886d;
            DipoleAF_D[173] = 0.702555233632734d;
            DipoleAF_D[174] = 0.665218773286685d;
            DipoleAF_D[175] = 0.629454723722028d;
            DipoleAF_D[176] = 0.595224053881604d;
            DipoleAF_D[177] = 0.562486917621896d;
            DipoleAF_D[178] = 0.531202799355181d;
            DipoleAF_D[179] = 0.501330653855900d;
            DipoleAF_D[180] = 0.472829040019105d;

            return DipoleAF_D;
        }

        /// <summary>
        /// F频段天线
        /// </summary>
        private double[] getDipoleAFForFBand()
        {
            double[] DipoleAF_F = new double[181];
            DipoleAF_F[0] = 1.412537544622750d;
            DipoleAF_F[1] = 1.456328200295200d;
            DipoleAF_F[2] = 1.500964326170930d;
            DipoleAF_F[3] = 1.546440921421870d;
            DipoleAF_F[4] = 1.592751959860680d;
            DipoleAF_F[5] = 1.639890367222250d;
            DipoleAF_F[6] = 1.687847999574810d;
            DipoleAF_F[7] = 1.736615622939270d;
            DipoleAF_F[8] = 1.786182894194860d;
            DipoleAF_F[9] = 1.836538343348350d;
            DipoleAF_F[10] = 1.887669357243050d;
            DipoleAF_F[11] = 1.939562164782420d;
            DipoleAF_F[12] = 1.992201823741640d;
            DipoleAF_F[13] = 2.045572209238840d;
            DipoleAF_F[14] = 2.099656003935260d;
            DipoleAF_F[15] = 2.154434690031880d;
            DipoleAF_F[16] = 2.209888543126970d;
            DipoleAF_F[17] = 2.265996627996600d;
            DipoleAF_F[18] = 2.322736796357110d;
            DipoleAF_F[19] = 2.380085686664920d;
            DipoleAF_F[20] = 2.438018726006120d;
            DipoleAF_F[21] = 2.496510134124030d;
            DipoleAF_F[22] = 2.555532929629220d;
            DipoleAF_F[23] = 2.615058938432290d;
            DipoleAF_F[24] = 2.675058804435230d;
            DipoleAF_F[25] = 2.735502002512630d;
            DipoleAF_F[26] = 2.796356853809180d;
            DipoleAF_F[27] = 2.857590543374950d;
            DipoleAF_F[28] = 2.919169140154780d;
            DipoleAF_F[29] = 2.981057619342820d;
            DipoleAF_F[30] = 3.043219887107720d;
            DipoleAF_F[31] = 3.105618807688380d;
            DipoleAF_F[32] = 3.168216232854630d;
            DipoleAF_F[33] = 3.230973033721070d;
            DipoleAF_F[34] = 3.293849134896700d;
            DipoleAF_F[35] = 3.356803550946730d;
            DipoleAF_F[36] = 3.419794425137090d;
            DipoleAF_F[37] = 3.482779070426040d;
            DipoleAF_F[38] = 3.545714012661020d;
            DipoleAF_F[39] = 3.608555035933130d;
            DipoleAF_F[40] = 3.671257230035040d;
            DipoleAF_F[41] = 3.733775039962680d;
            DipoleAF_F[42] = 3.796062317394440d;
            DipoleAF_F[43] = 3.858072374076280d;
            DipoleAF_F[44] = 3.919758037034780d;
            DipoleAF_F[45] = 3.981071705534970d;
            DipoleAF_F[46] = 4.041965409693750d;
            DipoleAF_F[47] = 4.102390870654570d;
            DipoleAF_F[48] = 4.162299562223780d;
            DipoleAF_F[49] = 4.221642773863750d;
            DipoleAF_F[50] = 4.280371674933440d;
            DipoleAF_F[51] = 4.338437380061960d;
            DipoleAF_F[52] = 4.395791015536890d;
            DipoleAF_F[53] = 4.452383786584340d;
            DipoleAF_F[54] = 4.508167045414600d;
            DipoleAF_F[55] = 4.563092359903160d;
            DipoleAF_F[56] = 4.617111582773840d;
            DipoleAF_F[57] = 4.670176921148060d;
            DipoleAF_F[58] = 4.722241006321350d;
            DipoleAF_F[59] = 4.773256963626330d;
            DipoleAF_F[60] = 4.823178482239310d;
            DipoleAF_F[61] = 4.871959884786300d;
            DipoleAF_F[62] = 4.919556196603110d;
            DipoleAF_F[63] = 4.965923214503360d;
            DipoleAF_F[64] = 5.011017574908200d;
            DipoleAF_F[65] = 5.054796821191240d;
            DipoleAF_F[66] = 5.097219470093060d;
            DipoleAF_F[67] = 5.138245077060110d;
            DipoleAF_F[68] = 5.177834300364610d;
            DipoleAF_F[69] = 5.215948963863250d;
            DipoleAF_F[70] = 5.252552118254930d;
            DipoleAF_F[71] = 5.287608100700240d;
            DipoleAF_F[72] = 5.321082592667940d;
            DipoleAF_F[73] = 5.352942675877550d;
            DipoleAF_F[74] = 5.383156886210230d;
            DipoleAF_F[75] = 5.411695265464640d;
            DipoleAF_F[76] = 5.438529410838540d;
            DipoleAF_F[77] = 5.463632522021850d;
            DipoleAF_F[78] = 5.486979445791880d;
            DipoleAF_F[79] = 5.508546718006810d;
            DipoleAF_F[80] = 5.528312602899480d;
            DipoleAF_F[81] = 5.546257129579110d;
            DipoleAF_F[82] = 5.562362125655480d;
            DipoleAF_F[83] = 5.576611247906010d;
            DipoleAF_F[84] = 5.588990009913560d;
            DipoleAF_F[85] = 5.599485806609300d;
            DipoleAF_F[86] = 5.608087935662660d;
            DipoleAF_F[87] = 5.614787615667360d;
            DipoleAF_F[88] = 5.619578001080440d;
            DipoleAF_F[89] = 5.622454193878470d;
            DipoleAF_F[90] = 5.623413251903490d;
            DipoleAF_F[91] = 5.622454193878470d;
            DipoleAF_F[92] = 5.619578001080440d;
            DipoleAF_F[93] = 5.614787615667360d;
            DipoleAF_F[94] = 5.608087935662660d;
            DipoleAF_F[95] = 5.599485806609300d;
            DipoleAF_F[96] = 5.588990009913560d;
            DipoleAF_F[97] = 5.576611247906010d;
            DipoleAF_F[98] = 5.562362125655480d;
            DipoleAF_F[99] = 5.546257129579110d;
            DipoleAF_F[100] = 5.528312602899480d;
            DipoleAF_F[101] = 5.508546718006810d;
            DipoleAF_F[102] = 5.486979445791880d;
            DipoleAF_F[103] = 5.463632522021850d;
            DipoleAF_F[104] = 5.438529410838540d;
            DipoleAF_F[105] = 5.411695265464640d;
            DipoleAF_F[106] = 5.383156886210230d;
            DipoleAF_F[107] = 5.352942675877550d;
            DipoleAF_F[108] = 5.321082592667940d;
            DipoleAF_F[109] = 5.287608100700240d;
            DipoleAF_F[110] = 5.252552118254930d;
            DipoleAF_F[111] = 5.215948963863250d;
            DipoleAF_F[112] = 5.177834300364610d;
            DipoleAF_F[113] = 5.138245077060110d;
            DipoleAF_F[114] = 5.097219470093060d;
            DipoleAF_F[115] = 5.054796821191240d;
            DipoleAF_F[116] = 5.011017574908200d;
            DipoleAF_F[117] = 4.965923214503360d;
            DipoleAF_F[118] = 4.919556196603110d;
            DipoleAF_F[119] = 4.871959884786300d;
            DipoleAF_F[120] = 4.823178482239310d;
            DipoleAF_F[121] = 4.773256963626330d;
            DipoleAF_F[122] = 4.722241006321350d;
            DipoleAF_F[123] = 4.670176921148060d;
            DipoleAF_F[124] = 4.617111582773840d;
            DipoleAF_F[125] = 4.563092359903160d;
            DipoleAF_F[126] = 4.508167045414600d;
            DipoleAF_F[127] = 4.452383786584340d;
            DipoleAF_F[128] = 4.395791015536890d;
            DipoleAF_F[129] = 4.338437380061960d;
            DipoleAF_F[130] = 4.280371674933440d;
            DipoleAF_F[131] = 4.221642773863750d;
            DipoleAF_F[132] = 4.162299562223780d;
            DipoleAF_F[133] = 4.102390870654570d;
            DipoleAF_F[134] = 4.041965409693750d;
            DipoleAF_F[135] = 3.981071705534970d;
            DipoleAF_F[136] = 3.919758037034780d;
            DipoleAF_F[137] = 3.858072374076280d;
            DipoleAF_F[138] = 3.796062317394440d;
            DipoleAF_F[139] = 3.733775039962680d;
            DipoleAF_F[140] = 3.671257230035040d;
            DipoleAF_F[141] = 3.608555035933130d;
            DipoleAF_F[142] = 3.545714012661020d;
            DipoleAF_F[143] = 3.482779070426040d;
            DipoleAF_F[144] = 3.419794425137090d;
            DipoleAF_F[145] = 3.356803550946730d;
            DipoleAF_F[146] = 3.293849134896700d;
            DipoleAF_F[147] = 3.230973033721070d;
            DipoleAF_F[148] = 3.168216232854630d;
            DipoleAF_F[149] = 3.105618807688380d;
            DipoleAF_F[150] = 3.043219887107720d;
            DipoleAF_F[151] = 2.981057619342820d;
            DipoleAF_F[152] = 2.919169140154780d;
            DipoleAF_F[153] = 2.857590543374950d;
            DipoleAF_F[154] = 2.796356853809180d;
            DipoleAF_F[155] = 2.735502002512630d;
            DipoleAF_F[156] = 2.675058804435230d;
            DipoleAF_F[157] = 2.615058938432290d;
            DipoleAF_F[158] = 2.555532929629220d;
            DipoleAF_F[159] = 2.496510134124030d;
            DipoleAF_F[160] = 2.438018726006120d;
            DipoleAF_F[161] = 2.380085686664920d;
            DipoleAF_F[162] = 2.322736796357110d;
            DipoleAF_F[163] = 2.265996627996600d;
            DipoleAF_F[164] = 2.209888543126970d;
            DipoleAF_F[165] = 2.154434690031880d;
            DipoleAF_F[166] = 2.099656003935260d;
            DipoleAF_F[167] = 2.045572209238840d;
            DipoleAF_F[168] = 1.992201823741640d;
            DipoleAF_F[169] = 1.939562164782420d;
            DipoleAF_F[170] = 1.887669357243050d;
            DipoleAF_F[171] = 1.836538343348350d;
            DipoleAF_F[172] = 1.786182894194860d;
            DipoleAF_F[173] = 1.736615622939270d;
            DipoleAF_F[174] = 1.687847999574810d;
            DipoleAF_F[175] = 1.639890367222250d;
            DipoleAF_F[176] = 1.592751959860680d;
            DipoleAF_F[177] = 1.546440921421870d;
            DipoleAF_F[178] = 1.500964326170930d;
            DipoleAF_F[179] = 1.456328200295200d;
            DipoleAF_F[180] = 1.412537544622750d;
            return DipoleAF_F;
        }
    }

    public class LteMrItem
    {
        public int iEnodebId{ get; set; }
        public int iSectorId{ get; set; }
        /// <summary>
        /// RSRP均值
        /// </summary>
        public float dAvgRsrp{ get; set; }
        /// <summary>
        /// SINR均值
        /// </summary>
        public float dAvgSinr{ get; set; }
        /// <summary>
        /// 95覆盖率
        /// </summary>
        public float dRate95Rsrp{ get; set; }
        /// <summary>
        /// 110覆盖率
        /// </summary>
        public float dRate110Rsrp{ get; set; }

        public int ECI
        {
            get {
                return iEnodebId * 256 + iSectorId;
            }
        }

        public LteMrItem()
        {
            iEnodebId = 0;
            iSectorId = 0;
            dAvgRsrp = 0;
            dAvgSinr = 0;
            dRate95Rsrp = 0;
            dRate110Rsrp = 0;
        }
    }

    public class CellMrData
    {
        //MR DATA
        public LteMrItem lteMRItem{ get; set; }
        public ZTAntMRBaseItem lteMRPowerHeadRoomItem{ get; set; }
        public ZTAntMRBaseItem lteMRRsrpItem{ get; set; }
        public ZTAntMRBaseItem lteMRAoaItem{ get; set; }
        public ZTAntMRBaseItem lteMRSinrUlItem{ get; set; }
        public ZTAntMRBaseItem lteMRRttdAoaItem{ get; set; }
        public ZTAntMRBaseItem lteMRTaItem{ get; set; }
        public LteCoverItem lteMRCoverItem{ get; set; }

        public CellMrData()
        {
            lteMRItem = new LteMrItem();
            lteMRPowerHeadRoomItem = new ZTAntMRBaseItem();
            lteMRRsrpItem = new ZTAntMRBaseItem();
            lteMRAoaItem = new ZTAntMRBaseItem();
            lteMRSinrUlItem = new ZTAntMRBaseItem();
            lteMRRttdAoaItem = new ZTAntMRBaseItem();
            lteMRTaItem = new ZTAntMRBaseItem();
            lteMRCoverItem = new LteCoverItem();
        }

        public int[,] AnaRttdAoa
        {
            get
            {
                if (lteMRTaItem == null || lteMRAoaItem == null)
                    return new int[44, 72];

                int[,] rttdAoaItem = new int[44, 72];
                for (int i = 0; i < 44; i++)
                {
                    for (int j = 0; j < 72; j++)
                    {
                        if (lteMRTaItem.dataValue[i] > 0 && lteMRAoaItem.dataValue[j] > 0)
                        {
                            int iSecValue = 0; //二维数据
                            int m = i / 4;
                            int n = 12 - j / 6;//由于天线到达角为逆时针，一维数据读取时已经处理
                            int idx = m * 12 + n;
                            if (lteMRRttdAoaItem != null && lteMRRttdAoaItem.dataValue[idx - 1] > 0)
                                iSecValue = lteMRRttdAoaItem.dataValue[idx - 1];

                            rttdAoaItem[i, j] = lteMRTaItem.dataValue[i] + lteMRAoaItem.dataValue[j] + iSecValue;
                        }
                    }
                }
                return rttdAoaItem;
            }
        }
    }

    public class LteCoverItem
    {
        public string strCgi { get; set; }
        public string strCellName { get; set; }
        [Description("MRO总采样点数")]
        public int IMRO总采样点数 { get; set; }
        [Description("重叠覆盖条件采样点数")]
        public int I重叠覆盖条件采样点数 { get; set; }
        [Description("重叠覆盖指数")]
        public float F重叠覆盖指数 { get; set; }
        [Description("过覆盖影响小区数")]
        public int I过覆盖影响小区数 { get; set; }
        [Description("高重叠覆盖小区")]
        public string S高重叠覆盖小区 { get; set; }
        [Description("过覆盖小区")]
        public string S过覆盖小区 { get; set; }

        public int iEnodebId
        {
            get
            {
                int iTmpId = 0;
                string[] cgi = strCgi.Split('-');
                if (cgi.Length == 4)
                {
                    int.TryParse(cgi[2], out iTmpId);
                }
                else
                {
                    //DO NOTHING
                }
                return iTmpId;
            }
        }

        public int iSectorId
        {
            get
            {
                int iTmpId = 0;
                string[] cgi = strCgi.Split('-');
                if (cgi.Length == 4)
                {
                    int.TryParse(cgi[3], out iTmpId);
                }
                else
                {
                    //DO NOTHING
                }
                return iTmpId;
            }
        }

        public int iEci
        {
            get
            {
                return iEnodebId * 256 + iSectorId;
            }
        }

        public int iSubEci
        {
            get
            {
                int iTmpId = iSectorId % 10;
                return iEnodebId * 256 + iTmpId;
            }
        }

        public LteCoverItem()
        {
            strCgi = "";
            strCellName = "";
            IMRO总采样点数 = 0;
            I重叠覆盖条件采样点数 = 0;
            F重叠覆盖指数 = 0;
            I过覆盖影响小区数 = 0;
            S高重叠覆盖小区 = "否";
            S过覆盖小区 = "否";
        }

    }
}
