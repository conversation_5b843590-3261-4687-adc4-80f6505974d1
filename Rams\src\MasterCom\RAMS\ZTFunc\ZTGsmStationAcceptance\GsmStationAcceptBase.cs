﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Imaging;

using MasterCom.RAMS.Model;

using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class GsmStationAcceptBase
    {
        public virtual void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
        }

        #region 添加结果到Excel
        public virtual void FillResult(string btsName, Excel.Workbook eBook)
        {
            FillResultToSheet(btsName, eBook, 2);
        }

        protected void FillResultToSheet(string btsName, Excel.Workbook eBook, int sheetIndex)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            GsmStationAcceptResult result = btsResultDic[btsName];
            List<int> cellIDs = result.CellIDs;
            for (int i = 0; i < cellIDs.Count; ++i)
            {
                int cellId = cellIDs[i];
                int index = result.CellIDMap[cellId];
                for (int row = 0; row < resultGrid.GetLength(1); ++row)
                {
                    for (int col = 0; col < resultGrid.GetLength(2); ++col)
                    {
                        object value = result.GetValue(cellId, row, col);
                        InsertExcelValue(eBook, sheetIndex, resultGrid[index, row, col], value);
                    }
                }
            }
        }

        protected void InsertExcelValue(Excel.Workbook eBook, int sheetIndex, string cell, object value)
        {
            if (value == null)
            {
                return;
            }
            if (value is double && (double)value == double.MinValue)
            {
                return;
            }

            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[sheetIndex];
            Excel.Range rng = eSheet.get_Range(cell, Type.Missing);
            rng.set_Value(Type.Missing, value);
        }
        #endregion

        public virtual bool IsValidFile(FileInfo fileInfo)
        {
            return false;
        }

        public virtual void Clear()
        {
            btsResultDic.Clear();
        }

        public int GetCellCount(string btsName)
        {
            return btsResultDic.ContainsKey(btsName) ? btsResultDic[btsName].CellCount : 0;
        }

        public List<string> BtsNames
        {
            get
            {
                return new List<string>(btsResultDic.Keys);
            }
        }
      
        /// <summary>
        /// 结果集
        /// </summary>
        protected Dictionary<string, GsmStationAcceptResult> btsResultDic = new Dictionary<string, GsmStationAcceptResult>();

        /// <summary>
        /// 结果网格样式
        /// </summary>
        protected string[, ,] resultGrid = null; // 小区个数，行数，列数

        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public static int MaxDeviceID { get; set; } = 0;

        protected void SetCellDeviceIDMap(GsmStationAcceptResult result, string fileName, BTS bts)
        {
            if (bts == null)
            {
                result.CellIDMap = new Dictionary<int, int>();
                result.CellIDMap[0] = 0;
            }
            else
            {
                int deviceID = GetDeviceID(fileName);
                MaxDeviceID = Math.Max(MaxDeviceID, deviceID);

                result.CellIDMap = new Dictionary<int, int>();
                for (int i = 0; i < bts.Cells.Count; i++)
                {
                    for (int j = 1; j <= MaxDeviceID; j++)
                    {
                        int cellDeviceID;
                        int.TryParse(bts.Cells[i].ID.ToString() + j.ToString(), out cellDeviceID);

                        if (!result.CellIDMap.ContainsKey(cellDeviceID))
                        {
                            result.CellIDMap[cellDeviceID] = i * MaxDeviceID + j - 1;
                        }
                    }
                }
            }
        }

        protected int GetDeviceID(string fileName)
        {
            string device = "";
            int id = 0;
            int deviceIDStart = fileName.LastIndexOf('-') + 1;
            int deviceIDLength = fileName.LastIndexOf('_') - deviceIDStart;
            if (deviceIDLength > 0)
            {
                device = fileName.Substring(deviceIDStart, deviceIDLength);
            }
            int.TryParse(device, out id);
            return id;
        }

        protected int GetCellDeviceID(Cell cell, string fileName)
        {
            string device = "";
            int id = 0;
            int deviceIDStart = fileName.LastIndexOf('-') + 1;
            int deviceIDLength = fileName.LastIndexOf('_') - deviceIDStart;
            if (deviceIDLength > 0)
            {
                device = fileName.Substring(deviceIDStart, deviceIDLength);
            }
            int.TryParse(cell.ID.ToString() + device, out id);     
            return id;
        }

        protected virtual void SetCellIDMap(GsmStationAcceptResult result,BTS bts)
        {
            if (bts == null)
            {
                result.CellIDMap = new Dictionary<int, int>();
                result.CellIDMap[0] = 0;
            }
            else
            {
                result.CellIDMap = new Dictionary<int, int>();
                for (int i = 0; i < bts.Cells.Count; i++)
                {
                    if (!result.CellIDMap.ContainsKey(bts.Cells[i].ID))
                    {
                        result.CellIDMap[bts.Cells[i].ID] = i;
                    }
                }
            }
        }

        protected virtual int getResult(string fileName, Cell cell, out GsmStationAcceptResult result, bool isSetCellIDMap)
        {
            BTS gsmBTS = cell.BelongBTS;
            result = null;
            if (!btsResultDic.TryGetValue(gsmBTS.Name, out result))
            {
                result = new GsmStationAcceptResult(gsmBTS.ID, gsmBTS.Name, resultGrid.GetLength(1), resultGrid.GetLength(2));
                SetCellIDMap(result, isSetCellIDMap ? gsmBTS : null);
                btsResultDic.Add(gsmBTS.Name, result);
            }
            return cell.ID;
        }
    }

    /// <summary>
    /// 首页(站点验收记录单中的基础数据)
    /// </summary>
    class GsmAcpHomePage : GsmStationAcceptBase
    {
        public GsmAcpHomePage()
        {
            resultGrid = new string[1, 5, 2] {
                { 
                    { "e3", "y3" },
                    { "e5", "y5" },
                    { "e7", "y7" },
                    { "h13", "h14" },
                    { "h15", "h16" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            Cell cell = targetCell as Cell;
            GsmStationAcceptResult result;
            getResult(fileInfo.Name, cell, out result, false);

            BTS gsmBts = cell.BelongBTS;
            result.SetValue(cellID, 0, 0, gsmBts.Name);
            result.SetValue(cellID, 0, 1, DateTime.Now.ToString());
            result.SetValue(cellID, 1, 0, gsmBts.ID);
            result.SetValue(cellID, 1, 1, fileInfo.DistrictName);
            result.SetValue(cellID, 2, 0, "");
            result.SetValue(cellID, 2, 1, "");
            result.SetValue(cellID, 3, 0, gsmBts.Longitude);
            result.SetValue(cellID, 3, 1, gsmBts.Latitude);
            result.SetValue(cellID, 4, 0, gsmBts.Cells[0].LAC);
            result.SetValue(cellID, 4, 1, gsmBts.ID);
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            FillResultToSheet(btsName, eBook, 1);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return true;
        }

        private readonly int cellID = 0;
    }

    /// <summary>
    /// 首页部分的小区和对应天线信息
    /// </summary>
    class GsmAcpCellParameter : GsmStationAcceptBase
    {
        public GsmAcpCellParameter()
        {
            resultGrid = new string[3, 5, 1] {
                { 
                    { "h19" },
                    { "h20" },
                    { "h22" },
                    { "h23" },
                    { "h24" },
                },
                { 
                    { "o19" },
                    { "o20" },
                    { "o22" },
                    { "o23" },
                    { "o24" },
                },
                {  
                    { "w19" },
                    { "w20" },
                    { "w22" },
                    { "w23" },
                    { "w24" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            Cell cell = targetCell as Cell;
            GsmStationAcceptResult result;
            getResult(fileInfo.Name, cell, out result, true);
            foreach (Cell icell in cell.BelongBTS.Cells)
            {
                result.SetValue(icell.ID, 0, 0, icell.BCCH);
                result.SetValue(icell.ID, 1, 0, icell.BSIC);
                result.SetValue(icell.ID, 2, 0, icell.Altitude);
                result.SetValue(icell.ID, 3, 0, icell.Direction);
                result.SetValue(icell.ID, 4, 0, icell.Downword);//Downward拼写错误
            }
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            FillResultToSheet(btsName, eBook, 1);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return true;
        }
    }

    /// <summary>
    /// 语音业务(接通率,语音质量,掉话率)
    /// </summary>
    class GsmAcpVoice : GsmStationAcceptBase
    {
        /// <summary>
        /// 小区对应的主被叫信息(主被叫文件是分开分析的,所以需要一个全局变量进行累加)
        /// </summary>
        protected Dictionary<int, VoiceInfo> cellInfo = new Dictionary<int, VoiceInfo>();

        public GsmAcpVoice()
        {
            resultGrid = new string[3, 3, 1] {
                { 
                    { "y9" },
                    { "y10" },
                    { "y11" },
                },
                { 
                    { "y19" },
                    { "y20" },
                    { "y21" },
                },
                { 
                    { "y29" },
                    { "y30" },
                    { "y31" },
                },
            };
        }

        /// <summary>
        /// 由于主被叫文件cellID相同,用于保存合并主被叫文件数据
        /// </summary>
        /// <param name="cell"></param>
        /// <param name="fileInfo"></param>
        /// <param name="voiceInfo"></param>
        protected virtual void setCellInfo(Cell cell, FileInfo fileInfo, out VoiceInfo voiceInfo)
        {
            if (!cellInfo.TryGetValue(cell.ID, out voiceInfo))
            {
                voiceInfo = new VoiceInfo();
                cellInfo.Add(cell.ID, voiceInfo);
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            Cell cell = targetCell as Cell;
            VoiceInfo voiceInfo;
            setCellInfo(cell, fileInfo, out voiceInfo);

            #region 计算接通率,掉话率,语音质量
            foreach (Event evt in fileManager.Events)
            {
                if (evt.ID == 1)
                {
                    voiceInfo.MoCallAttemptTimes++;
                }
                else if (evt.ID == 4)
                {
                    voiceInfo.MoCallSuccessTimes++;
                }
                else if (evt.ID == 5)
                {
                    voiceInfo.MtCallSuccessTimes++;
                }
                else if (evt.ID == 6 || evt.ID == 7)
                {
                    voiceInfo.DropCallTimes++;
                }
            }
            //接通率
            string callSuccessRate = voiceInfo.MoCallAttemptTimes == 0 ? "" :
                Math.Round(voiceInfo.MoCallSuccessTimes * 100.0 / voiceInfo.MoCallAttemptTimes, 2).ToString() + "%";
            //掉话率
            string dropCallRate = voiceInfo.MoCallAttemptTimes + voiceInfo.MtCallSuccessTimes == 0 ? "" :
                Math.Round(voiceInfo.DropCallTimes * 100.0 / (voiceInfo.MoCallAttemptTimes + voiceInfo.MtCallSuccessTimes), 2).ToString() + "%";

            int[] rxqualSampleNum = new int[8];
            int validPoint = 0;
            foreach (var testPoint in fileManager.TestPoints)
            {
                int? rxQual = (int?)(byte?)testPoint["RxQualSub"];
                if (rxQual != null)
                {
                    rxqualSampleNum[(int)rxQual]++;
                    validPoint++;
                }
            }
            //语音质量
            string voiceQuality = validPoint == 0 ? "" : Math.Round(((rxqualSampleNum[0] + rxqualSampleNum[1]
                + rxqualSampleNum[2] + rxqualSampleNum[3] + rxqualSampleNum[4]) * 100.0 / validPoint), 2).ToString() + "%";
            #endregion

            GsmStationAcceptResult result;
            int id = getResult(fileInfo.Name, cell, out result, true);
            
            result.SetValue(id, 0, 0, dropCallRate);
            if (fileInfo.Momt == (int)MoMtFile.MoFlag)
            {
                result.SetValue(id, 1, 0, callSuccessRate);
                result.SetValue(id, 2, 0, voiceQuality);
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("语音");
        }

        public class VoiceInfo
        {
            //主叫呼叫建立尝试次数
            public int MoCallAttemptTimes = 0;
            //主叫呼叫建立成功次数(主叫接通次数)
            public int MoCallSuccessTimes = 0;
            //被叫呼叫建立成功次数(被叫接通次数)
            public int MtCallSuccessTimes = 0;
            //掉话次数
            public int DropCallTimes = 0;
        }
    }

    /// <summary>
    /// GPRS附着成功率
    /// </summary>
    class GsmAcpGPRSAttachRate : GsmStationAcceptBase
    {
        public GsmAcpGPRSAttachRate()
        {
            resultGrid = new string[3, 1, 2] {
                { 
                    { "y14", "ab14" },
                },
                {
                    { "y24", "ab24" },
                },
                {
                    { "y34", "ab34" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            //Attach次数
            int attachTimes = 0;
            //Attach成功次数
            int attachSuccessTimes = 0;
            foreach (Event evt in fileManager.Events)
            {
                if (evt.ID == 22)
                {
                    attachTimes++;
                }
                else if (evt.ID == 23)
                {
                    attachSuccessTimes++;
                }
            }

            //GPRS Attach成功率
            string attachSuccessRate = attachTimes < 5 ? "" :
                Math.Round(attachSuccessTimes * 100.0 / attachTimes, 2).ToString() + "%";

            Cell cell = targetCell as Cell;
            GsmStationAcceptResult result;
            int id = getResult(fileInfo.Name, cell, out result, true);
          
            result.SetValue(id, 0, 0, attachSuccessRate);
            result.SetValue(id, 0, 1, attachTimes);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("ATTACH");
        }
    }

    /// <summary>
    /// PING成功率
    /// </summary>
    class GsmAcpPingRate : GsmStationAcceptBase
    {
        public GsmAcpPingRate()
        {
            resultGrid = new string[3, 1, 2] {
                { 
                    { "y15", "ab15" },
                },
                {
                    { "y25", "ab25" },
                },
                {
                    { "y35", "ab35" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            //Ping成功次数
            int pingSuccessTimes = 0;
            //Ping失败次数
            int pingfailTimes = 0;
            foreach (Event evt in fileManager.Events)
            {
                if (evt.ID == 55)
                {
                    pingSuccessTimes++;
                }
                else if (evt.ID == 56)
                {
                    pingfailTimes++;
                }
            }

            //Ping成功率
            string pingSuccessRate = (pingSuccessTimes + pingfailTimes) == 0 ? "" :
                Math.Round(pingSuccessTimes * 100.0 / (pingSuccessTimes + pingfailTimes), 2).ToString() + "%";

            Cell cell = targetCell as Cell;
            GsmStationAcceptResult result;
            int id = getResult(fileInfo.Name, cell, out result, true);

            result.SetValue(id, 0, 0, pingSuccessRate);
            result.SetValue(id, 0, 1, (pingSuccessTimes + pingfailTimes));
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("PING");
        }
    }

    /// <summary>
    /// FTP下载
    /// </summary>
    class GsmAcpFtpDownload : GsmStationAcceptBase
    {
        public GsmAcpFtpDownload()
        {
            resultGrid = new string[3, 1, 1] {
                { 
                    { "y16" },
                },
                {
                    { "y26" },
                },
                {
                    { "y36" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            int speedPointCount = 0;
            int speedSum = 0;
            foreach (var tp in fileManager.TestPoints)
            {
                float? speed = (float?)tp["AppThroughputDL"];
                if (speed != null && speed > 0)
                {
                    speedPointCount++;
                    speedSum += (int)speed;
                }
            }

            //平均下载速率
            string avgSpeedRate = speedPointCount == 0 ? "" :
                Math.Round(speedSum / (speedPointCount * 1024.0), 2).ToString();

            Cell cell = targetCell as Cell;
            GsmStationAcceptResult result;
            int id = getResult(fileInfo.Name, cell, out result, true);

            result.SetValue(id, 0, 0, avgSpeedRate);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载");
        }
    }

    /// <summary>
    /// 覆盖效果图
    /// </summary>
    class GsmAcpCoverPicture : GsmStationAcceptBase
    {
        public readonly string picFolderPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata\\GsmStationAcceptance");

        public GsmAcpCoverPicture()
        {
            resultGrid = new string[3, 2, 1] {
                { 
                    { "a16" },
                    { "a60" },
                },
                  { 
                    { "a30" },
                    { "a74" },
                },
                  { 
                    { "a44" },
                    { "a88" },
                },
            };
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            GsmStationAcceptResult result = btsResultDic[btsName];
            foreach (var item in result.CellIDMap)
            {
                for (int row = 0; row < resultGrid.GetLength(1); ++row)
                {
                    string picPath = result.GetValue(item.Key, row, 0) as string;
                    if (picPath != null)
                    {
                        InsertExcelPicture(eBook, resultGrid[item.Value, row, 0], picPath);
                    }
                }
            }
        }

        public static void InsertExcelPicture(Excel.Workbook eBook, string startCell, string picPath)
        {
            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[3];
            Excel.Range rng = eSheet.get_Range(startCell, Type.Missing);
            //对应图片的长宽,厘米
            double width = eBook.Application.CentimetersToPoints(16.9);
            double height = eBook.Application.CentimetersToPoints(7.25);
            eSheet.Shapes.AddPicture(picPath,
                Microsoft.Office.Core.MsoTriState.msoFalse,
                Microsoft.Office.Core.MsoTriState.msoCTrue,
                (float)(double)rng.Left, (float)(double)rng.Top,
                (float)width, (float)height);
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            Cell cell = targetCell as Cell;
            GsmStationAcceptResult result;
            getResult(fileInfo.Name, cell, out result, true);
            AnaFile(result, fileManager, cell);
        }

        public override void Clear()
        {
            foreach (GsmStationAcceptResult result in btsResultDic.Values)
            {
                string folderPath = GetBtsPicFolder(result.BtsName);
                if (System.IO.Directory.Exists(folderPath))
                {
                    System.IO.Directory.Delete(folderPath, true);
                }
            }
            btsResultDic.Clear();
        }

        private void AnaFile(GsmStationAcceptResult result, DTFileDataManager fileManager, Cell cell)
        {
            int cellID = cell.ID;
            MasterCom.MTGis.DbRect bounds = GetCoverBounds(fileManager, cell);
            double nearestDistance;
            TestPoint nearestTp = GetNearestTp(fileManager.TestPoints, cell, out nearestDistance);
            string picPath = null;
            picPath = FireMapAndTakePic("RxLevSub", bounds, nearestTp, cell);
            result.SetValue(cellID, 0, 0, picPath);
            picPath = FireMapAndTakePic("RxQualSub", bounds, nearestTp, cell);
            result.SetValue(cellID, 1, 0, picPath);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("覆盖") && fileInfo.Momt == (int)MoMtFile.MoFlag;
        }

        public MasterCom.MTGis.DbRect GetCoverBounds(DTFileDataManager fileManager, Cell cell)
        {
            double lngMin = cell.Longitude;
            double lngMax = cell.Longitude;
            double latMin = cell.Latitude;
            double latMax = cell.Latitude;

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (tp.Longitude > 70 && tp.Latitude > 3
                    && GsmStationAcceptManager.GetTpSrcCell(tp) == cell)//tp.GetMainCell_GSM() == cell)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }
            }
            MasterCom.MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001
                , lngMax + 0.001, latMax + 0.001);

            return bounds;
        }

        public TestPoint GetNearestTp(List<TestPoint> testPoints, Cell cell, out double nearestDistance)
        {
            nearestDistance = double.MaxValue;
            TestPoint nearestTp = null;
            foreach (TestPoint tp in testPoints)
            {
                if (tp["RxLevSub"] != null && tp["RxQualSub"] != null)
                {
                    double curDistance = tp.Distance2(cell.Longitude, cell.Latitude);
                    if (curDistance < nearestDistance)
                    {
                        nearestDistance = curDistance;
                        nearestTp = tp;
                    }
                }
            }
            return nearestTp;
        }

        public string FireMapAndTakePic(string paramName, MTGis.DbRect bounds, TestPoint nearestTp, Cell srcCell)
        {
            MainModel mModel = MainModel.GetInstance();
            mModel.FireSetDefaultMapSerialTheme("GSM", paramName);
            mModel.DrawFlyLines = false;

            if (srcCell == null)
            {
                return "";
            }
            
            foreach (MapSerialInfo serialInfo in mModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
            {
                if (serialInfo.Name.Equals(paramName))
                {
                    mModel.MainForm.GetMapForm().GetDTLayer().CurFlyLinesSerialInfo = serialInfo;
                    break;
                }
            }
            if (paramName == "RxLevSub")
            {
                GsmStationAcceptManager.ReSetRxLevelMapView();
            }
            else
            {
                GsmStationAcceptManager.ReSetRxQualityMapView();
            }

            mModel.DrawLinesPntToCells = true;
            mModel.PntToCellsDic.Clear();
            List<LongLat> longlatList = new List<LongLat>();
            mModel.PntToCellsDic.Add(nearestTp, longlatList);
            longlatList.Add(new LongLat((float)srcCell.EndPointLongitude, (float)srcCell.EndPointLatitude));

            mModel.FireDTDataChanged(mModel.MainForm);
            mModel.MainForm.GetMapForm().GoToView(bounds);

            return takePicture(srcCell.BelongBTS.Name, srcCell.Name, paramName);
        }

        private string takePicture(string btsName, string cellName, string paramName)
        {
            string filePath = GetCoverPicPath(btsName, cellName, paramName);

            Bitmap bitMap = MainModel.GetInstance().MainForm.GetMapForm().DrawToBitmapDIY();
            bitMap.Save(filePath, ImageFormat.Png);
            bitMap.Dispose();
            return filePath;
        }

        public string GetCoverPicPath(string btsName, string cellName, string paramName)
        {
            string folderPath = GetBtsPicFolder(btsName);
            if (!System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.CreateDirectory(folderPath);
            }
            return System.IO.Path.Combine(folderPath, cellName + "_" + paramName + ".png");
        }

        public string GetBtsPicFolder(string btsName)
        {
            return System.IO.Path.Combine(picFolderPath, btsName.Trim());
        }
    }

    class GsmAcpWithInstationHandOver : GsmStationAcceptBase
    {
        public GsmAcpWithInstationHandOver()
        {
            resultGrid = new string[1, 1, 1] {
                { 
                    { "y5"},
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            Cell cell = targetCell as Cell;
            //切换成功次数
            int handoverSuccessTimes = 0;
            //切换次数
            int handoverTimes = 0;
            foreach (Event evt in fileManager.Events)
            {
                if (evt.ID == 16)
                {
                    handoverTimes++;
                }
                else if (evt.ID == 17)
                {
                    handoverSuccessTimes++;          
                }
            }

            //切换成功率
            string handoverSuccessRate = handoverTimes == 0 ? "" :
                Math.Round(handoverSuccessTimes * 100.0 / handoverTimes, 2).ToString() + "%";

         
            GsmStationAcceptResult result;
            getResult(fileInfo.Name, cell, out result, false);

            result.SetValue(0, 0, 0, handoverSuccessRate);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("站内切换") && fileInfo.Momt == (int)MoMtFile.MoFlag;
        }
    }

    class GsmAcpBetweenstationHandOver : GsmAcpWithInstationHandOver
    {
        public GsmAcpBetweenstationHandOver()
        {
            resultGrid = new string[1, 1, 1] {
                { 
                    { "y6"},
                },
            };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("站间切换") && fileInfo.Momt == (int)MoMtFile.MoFlag;
        }
    }


}
