using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    public partial class WCDMACellSetRoadInput : Form
    {
        private WCDMACellSetForm mForm = null;

        private Dictionary<string, MapWinGIS.Shape> dicRoad = new Dictionary<string, MapWinGIS.Shape>();

        public WCDMACellSetRoadInput(WCDMACellSetForm winMain)
        {
            InitializeComponent();
            this.mForm = winMain;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            string roadName = this.comboBoxColName.SelectedItem.ToString();

            if (dicRoad.ContainsKey(roadName))
            {
                MapWinGIS.Shape geometry = dicRoad[roadName];

                if (geometry != null)
                {
                    /*KillMapInfo
                    List<DbPoint> roadPointList = new List<DbPoint>();
                    MultiCurve mCurv = geometry as MultiCurve;
                    foreach (MapInfo.Geometry.Curve curv in mCurv)
                    {
                        MapInfo.Geometry.DPoint[] pts = curv.SamplePoints();

                        for (int i = 0; i < pts.Length - 1; i++)
                        {
                            MapInfo.Geometry.DPoint pt = pts[i];

                            roadPointList.Add(pt);
                        }
                    }

                    mForm.setRoadGeometry(roadPointList);
                    //*/
                }
            }
            
            this.Dispose();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Dispose();
        }

        private void btnOpenFile_Click(object sender, EventArgs e)
        {
            //
        }

        private void WCDMACellSetRoadInput_Load(object sender, EventArgs e)
        {
            //
        }

        /*public bool openRegionTable(string regionTableName, ref string nameColumnName, bool noShow, System.Windows.Forms.TreeView sender)
        {

            MIConnection connection = new MIConnection();
            connection.Open();
            try
            {
                MICommand command = connection.CreateCommand();
                command.CommandText = "Select * from " + table.Alias;
                MIDataReader dataReader = command.ExecuteReader();
                DataTable schema = dataReader.GetSchemaTable();
                List<string> nameColumnNames = new List<string>();
                foreach (DataRow dataRow in schema.Rows)
                {
                    if (dataRow["DataType"] == typeof(string))
                    {
                        nameColumnNames.Add((string)dataRow["ColumnName"]);
                    }
                }
                if (nameColumnName == null || !nameColumnNames.Contains(nameColumnName))
                {
                    if (nameColumnNames.Count > 1 && !noShow)
                    {
                        MapFormNameColumnNameChooser nameColumnNameChooser = new MapFormNameColumnNameChooser(nameColumnNames);
                        if (nameColumnNameChooser.ShowDialog(this) == DialogResult.OK)
                        {
                            nameColumnName = nameColumnNameChooser.NameColumnName;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    else if (nameColumnNames.Count == 1 || nameColumnNames.Count > 1 && noShow)
                    {
                        nameColumnName = nameColumnNames[0];
                    }
                    else
                    {
                        if (!noShow)
                        {
                            MessageBox.Show(this, "No name column in table, can not open!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                        return false;
                    }
                }
                foreach (Feature feature in table)
                {
                    string name = feature[nameColumnName] as string;
                    if (name != null && name.Trim().Length > 0)
                    {
                        Geometry geometry = feature["Obj"] as Geometry;
                        if (!(geometry is LegacyText))
                        {
                            TreeNode treeNodeRegion = new TreeNode(name, 2, 2);
                            treeNodeRegion.Tag = geometry;
                            treeNodeRegion.Checked = false;
                            treeNodeType.Nodes.Add(treeNodeRegion);
                        }
                    }
                }
                treeNodeType.ExpandAll();
                if (treeNodeType.Nodes.Count == 0)
                {
                    sender.Nodes.Remove(treeNodeType);
                }
                dataReader.Close();
                command.Cancel();
                command.Dispose();
            }
            catch
            {
                return false;
            }
            finally
            {

                connection.Close();
            }

            if (treeNodeType.Nodes.Count > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }*/
    }
}