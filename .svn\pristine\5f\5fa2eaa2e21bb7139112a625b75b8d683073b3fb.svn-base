﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public class DoubleKpiGroup
    {
        private double? kpiMax;
        public double? KpiMax
        {
            get
            {
                if (kpiMax == null)
                {
                    return null;
                }
                return Math.Round((double)kpiMax, 2);
            }
        }

        private double? kpiMin;
        public double? KpiMin
        {
            get
            {
                if (kpiMin == null)
                {
                    return null;
                }
                return Math.Round((double)kpiMin, 2);
            }
        }
        private double kpiSum { get; set; }
        public int PointCount { get; set; }
        public double? KpiAvgValue
        {
            get
            {
                if (PointCount > 0)
                {
                    return Math.Round(kpiSum / PointCount, 2);
                }
                return null;
            }
        }

        public string KpiAvgValueDes
        {
            get { return KpiAvgValue == null ? "" : KpiAvgValue.ToString(); }
        }
        public void AddSinglePointKpi(double kpi)
        {
            PointCount++;
            kpiSum += kpi;

            if (kpiMax == null)
            {
                kpiMax = kpi;
            }
            else
            {
                kpiMax = Math.Max((double)kpiMax, kpi);
            }

            if (kpiMin == null)
            {
                kpiMin = kpi;
            }
            else
            {
                kpiMin = Math.Min((double)kpiMin, kpi);
            }
        }
    }
}
