﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRReasonPnlMod3 : NRReasonPanelBase
    {
        public NRReasonPnlMod3()
        {
            InitializeComponent();
        }

        public override void AttachReason(NRReasonBase reason)
        {
            base.AttachReason(reason);
            numDiffMax.ValueChanged -= numDiffMax_ValueChanged;
            numDiffMax.Value = (decimal)((NRReasonMod3)reason).RSRPDiffMax;
            numDiffMax.ValueChanged += numDiffMax_ValueChanged;
        }


        void numDiffMax_ValueChanged(object sender, EventArgs e)
        {
            ((NRReasonMod3)reason).RSRPDiffMax = (float)numDiffMax.Value;
        }
    }
}
