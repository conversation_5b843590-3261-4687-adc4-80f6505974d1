﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Stat.Data;
using DBDataViewer;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.ZTFunc
{
    public class SiteDTAnaQuery : DIYStatQuery
    {
        private List<FileAreaStatInfo> retFileStatInfoList = null;
        public List<FileAreaStatInfo> ResultStatList
        {
            get { return retFileStatInfoList; }
        }

        public int DistrictId { get; set; }

        public SiteQueryCond SiteCond { get; set; }

        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.area;
        }

        public override string Name
        {
            get { return "查询区域KPI"; }
        }

        public SiteDTAnaQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override void query()
        {
            ClientProxy client = new ClientProxy();

            try
            {
                if (client.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictId) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败";
                    return;
                }
                retFileStatInfoList = new List<FileAreaStatInfo>();
                querySiteStat(client);
            }
            catch
            {
            	//continue
            }
        }

        private void querySiteStat(ClientProxy clientProxy)
        {
            Dictionary<string, FileAreaStatInfo> dicOfResult = new Dictionary<string, FileAreaStatInfo>();
            Package package = clientProxy.Package;
            foreach (int curType in SiteCond.SiteAreaIDDic.Keys)
            {
                SiteCond.CurType = curType;
                List<int> tmpList = new List<int>(SiteCond.SiteAreaIDDic[curType]);

                while (tmpList.Count > 0)
                {
                    SiteCond.CurIdLst.Clear();
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < tmpList.Count; i++)
                    {
                        int id = tmpList[i];
                        SiteCond.CurIdLst.Add(id);
                        sb.Append(id);
                        sb.Append(",");
                        if (sb.Length > 7000)
                        {
                            tmpList.RemoveRange(0, i + 1);
                            break;
                        }
                        else if (i == tmpList.Count - 1)
                        {
                            tmpList.Clear();
                        }
                    }
                    prepareStatLogKPI_ImgGrid_FileFilter(package);
                    prepareAreaStatFilter(package);
                    fillContentNeeded_ImgGrid(package);
                    clientProxy.Send();
                    recieveInfo_ImgGrid(clientProxy, dicOfResult);
                    prepareStatLogKPI_Event_FileFilter(package);
                    prepareAreaStatFilter(package);
                    fillContentNeededArea_Event(package);
                    clientProxy.Send();
                    recieveInfo_Event(clientProxy, dicOfResult);
                }
            }
            foreach (FileAreaStatInfo fsi in dicOfResult.Values)
            {
                retFileStatInfoList.Add(fsi);
            }
        }

        private void prepareStatLogKPI_ImgGrid_FileFilter(Package package)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREASTAT_KPI;
            package.Content.PrepareAddParam();
            AddDIYPeriod(package, SiteCond.GetTimePeriod());
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, SiteCond.CarrierLst);
            AddDIYFileFilter(package, condition);
            AddDIYStatStatus(package);

            AddDIYEndOpFlag(package);
        }

        private void prepareAreaStatFilter(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,25,1");
            package.Content.AddParam("" + SiteCond.CurType);
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,26,1");
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < SiteCond.CurIdLst.Count; i++)
            {
                int id = SiteCond.CurIdLst[i];
                sb.Append(id);
                if (i < SiteCond.CurIdLst.Count - 1)
                {
                    sb.Append(",");
                }
            }
            package.Content.AddParam(sb.ToString());
            AddDIYEndOpFlag(package);
        }

        private void fillContentNeeded_ImgGrid(Package package)
        {
            package.Content.AddParam("1,1,1089");
        }

        private void recieveInfo_ImgGrid(ClientProxy clientProxy, Dictionary<string, FileAreaStatInfo> dicOfResult)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader header = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (header != null)
                    {
                        headerManager.AddDTDataHeader(header);
                    }
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curImgColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurImgColumnDef(idpairs, curImgColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_GSM
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_GPRS)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataGSM_NewImg newImg = new DataGSM_NewImg();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            string oldKey = makeAreaFileKey(fileID, areaType, areaid);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(oldKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[oldKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }

                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_LTE_FDD_AMR)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataLTE_FDD newImg = new DataLTE_FDD();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            string oldKey = makeAreaFileKey(fileID, areaType, areaid);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(oldKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[oldKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }

                        }
                    }
                }
                else if (package.Content.Type == ResponseType.AREASTAT_KPI_LTE)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataLTE newImg = new DataLTE();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            string oldKey = makeAreaFileKey(fileID, areaType, areaid);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(oldKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[oldKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }

                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_AMR
              || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_PS
              || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_VP)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataTDSCDMA_NewImg newImg = new DataTDSCDMA_NewImg();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            string oldKey = makeAreaFileKey(fileID, areaType, areaid);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(oldKey, out fsi))
                            {
                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[oldKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }
                        }
                    }

                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_AMR
              || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_PS
              || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_VP
              || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_PSHS)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataWCDMA_AMR newImg = new DataWCDMA_AMR();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            string oldKey = makeAreaFileKey(fileID, areaType, areaid);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(oldKey, out fsi))
                            {
                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[oldKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }
                        }
                    }

                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA_V
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA_D)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataCDMA_Voice newImg = new DataCDMA_Voice();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            string oldKey = makeAreaFileKey(fileID, areaType, areaid);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(oldKey, out fsi))
                            {
                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[oldKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA2000_D)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataEVDO_Data newImg = new DataEVDO_Data();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            string oldKey = makeAreaFileKey(fileID, areaType, areaid);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(oldKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[oldKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_SEARCHERROR)
                {
                    log.Error("返回错误命令字: " + package.Content.Type + " 需终止查询！");
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgress(ref index, ref progress);
            }
        }

        private void prepareStatLogKPI_Event_FileFilter(Package package)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREASTAT_EVNET;
            package.Content.PrepareAddParam();
            AddDIYPeriod(package, condition.Periods[0]);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);
            AddDIYStatStatus(package);
            //AddDIYAreaTypeAndID(package, condition.Areas)  
            //
            AddDIYEndOpFlag(package);
        }

        protected void fillContentNeededArea_Event(Package package)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("0,1,47,");
            sb.Append("0,2,47,");
            sb.Append("0,3,47,");
            sb.Append("0,4,47,");
            sb.Append("0,5,47,");
            sb.Append("0,6,47,");
            sb.Append("0,7,47,");
            sb.Append("0,8,47,");
            sb.Append("0,9,47,");
            sb.Append("0,10,47,");
            sb.Append("0,11,47,");
            sb.Append("0,12,47,");
            sb.Append("0,13,47,");
            sb.Append("0,14,47,");
            sb.Append("0,15,47,");
            sb.Append("0,16,47");
            package.Content.AddParam(sb.ToString());
        }

        private void recieveInfo_Event(ClientProxy clientProxy, Dictionary<string, FileAreaStatInfo> dicOfResult)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> curDefColumnDef = new List<ColumnDefItem>();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();

            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader header = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (header != null)
                    {
                        headerManager.AddDTDataHeader(header);
                    }
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curDefColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curDefColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_GSM
                    || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_TDSCDMA
                    || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_WCDMA
                    || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA
                    || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA2000
                    || package.Content.Type == ResponseType.AREASTAT_KPI_EVENT_LTE
                    || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_NR)
                {
                    fillData(dicOfResult, headerManager, curDefColumnDef, package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_SEARCHERROR)
                {
                    log.Error("返回错误命令字: " + package.Content.Type + " 需终止查询！");
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgress(ref index, ref progress);
            }
        }

        private static void setProgress(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        private void fillData(Dictionary<string, FileAreaStatInfo> dicOfResult, DTDataHeaderManager headerManager, List<ColumnDefItem> curDefColumnDef, Package package)
        {
            Event data = Event.Create(package.Content, curDefColumnDef);
            int iareatype = data.AreaTypeID;
            int iareaid = data.AreaID;
            int ifileid = data.FileID;

            string oldKey = makeAreaFileKey(ifileid, iareatype, iareaid);
            DTDataHeader header = headerManager.GetHeaderByFileID(ifileid);
            if (header != null)
            {
                FileAreaStatInfo fsi = null;
                if (!dicOfResult.TryGetValue(oldKey, out fsi))
                {
                    fsi = new FileAreaStatInfo();
                    fsi.DistrictId = DistrictId;
                    fsi.areaType = data.AreaTypeID;
                    fsi.areaId = data.AreaID;
                    fsi.fileHeader = header;
                    fsi.kpiData = new DataUnitAreaKPIQuery();
                    fsi.kpiData.addStatData(data, false);
                    dicOfResult[oldKey] = fsi;
                }
                else
                {
                    fsi.kpiData.addStatData(data, false);
                }
            }
        }

        private string makeAreaFileKey(int fileID, int areaType, int areaid)
        {
            return areaType + ":" + areaid + ":" + fileID;
        }
    }
}
