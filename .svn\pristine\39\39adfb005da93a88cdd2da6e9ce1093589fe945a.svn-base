﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Func.ExportTestPoint
{
    public class ExportTemplate
    {
        public ExportTemplate()
        { }
        public ExportTemplate(string name)
        {
            this.Name = name;
        }

        public string Name
        {
            get;
            set;
        }

        public override string ToString()
        {
            return this.Name;
        }

        private List<ColumnOptions> columns = new List<ColumnOptions>();

        public List<ColumnOptions> Columns
        {
            get { return columns; }
        }

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["Name"] = this.Name;
                List<object> displayParams = new List<object>();
                foreach (ColumnOptions col in this.columns)
                {
                    displayParams.Add(col.CfgParam);
                }
                paramDic["Columns"] = displayParams;
                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.Name = value["Name"].ToString();
                columns = new List<ColumnOptions>();
                List<object> list = value["Columns"] as List<object>;
                foreach (object objParam in list)
                {
                    ColumnOptions col = new ColumnOptions();
                    col.CfgParam = objParam as Dictionary<string, object>;
                    //可能存在Default.dic更新后导致与ExportTestPoint.xml中的指标不一致,从而DisplayParam为空报错,故匹配不到不添加
                    if (col.DisplayParam != null)
                    {
                        columns.Add(col);
                    }
                }
            }
        }

        internal bool AddColumn(ColumnOptions col)
        {
            ColumnOptions existCol = columns.Find(
            delegate(ColumnOptions c) { return c.ParamKey == col.ParamKey; });
            if (existCol != null)
            {
                return false;
            }
            else
            {
                columns.Add(col);
                return true;
            }
        }

    }
}
