﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public class TestRound
    {
        public TestRound()
        {
        }
        public int SN { get; set; }
        public int Year { get; set; }

        /// <summary>
        /// 季度
        /// </summary>
        public int Quarter
        {
            get
            {
                if (Month >= 1 && Month <= 3)
                {
                    return 1;
                }
                else if (Month >= 4 && Month <= 6)
                {
                    return 2;
                }
                else if (Month >= 7 && Month <= 9)
                {
                    return 3;
                }
                else if (Month >= 10 && Month <= 12)
                {
                    return 4;
                }
                return 0;
            }
        }
        public int Month { get; set; }
        public TimePeriod Period
        {
            get
            {
                TimePeriod period = new TimePeriod(this.BeginTime, this.EndTime);
                period.showDayFormat = true;
                return period;
            }
        }
        public int BeginTimeInt { get; set; }
        public DateTime BeginTime { get; set; }
        public int EndTimeInt { get; set; }
        public DateTime EndTime { get; set; }

        public string StrDesc { get; set; }
        public void SetPeriod(int beginTimeInt, int endTimeInt)
        {
            this.BeginTimeInt = beginTimeInt;
            this.EndTimeInt = endTimeInt;
            this.BeginTime = JavaDate.GetDateTimeFromMilliseconds(beginTimeInt * 1000L);
            this.EndTime = JavaDate.GetDateTimeFromMilliseconds(endTimeInt * 1000L);
        }
        public override string ToString()
        {
            return Year + "年" + Quarter + "季度" + Month + "月份测试 （" + Period.ToString() + ")";
        }
    }
}
