﻿using MasterCom.RAMS.Chris.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTETMQueryBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        protected LTETMQueryBase()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);

            Columns = new List<string>();
            Columns.Add("lte_SINR");
            Columns.Add("lte_Transmission_Mode");
            Columns.Add("lte_APP_type");/*类型*/
            Columns.Add("lte_APP_Speed_Mb");/*速度*/
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22060, this.Name);
        }

        protected TMCondition tmCondition = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            TMSettingDlg dlg = new TMSettingDlg(tmCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                tmCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void fireShowForm()
        {
            if (tmSpeedDic.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            LTETMAnaForm frm = MainModel.CreateResultForm(typeof(LTETMAnaForm)) as LTETMAnaForm;
            frm.FillData(tmCondition, tmSpeedDic);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            tmSpeedDic = new Dictionary<Range, Dictionary<int, TMPointsSpeed>>();
        }

        Dictionary<Range, Dictionary<int, TMPointsSpeed>> tmSpeedDic = new Dictionary<Range, Dictionary<int, TMPointsSpeed>>();
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        float? sinr = GetSINR(tp);
                        int? tm = GetMode(tp);

                        if (sinr != null && tm != null && isValueTM((int)tm))
                        {
                            addRangePoint(tp, sinr, tm);
                        }
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private void addRangePoint(TestPoint tp, float? sinr, int? tm)
        {
            foreach (Range sinrRange in tmCondition.RangeValues.Values) /*遍历区间*/
            {
                if (sinrRange.Contains((float)sinr))
                {
                    Dictionary<int, TMPointsSpeed> tmPointDic;
                    if (!tmSpeedDic.TryGetValue(sinrRange, out tmPointDic))
                    {
                        tmPointDic = new Dictionary<int, TMPointsSpeed>();
                        tmSpeedDic.Add(sinrRange, tmPointDic);
                    }

                    TMPointsSpeed tmPoint;
                    if (!tmPointDic.TryGetValue((int)tm, out tmPoint))
                    {
                        tmPoint = new TMPointsSpeed();
                        tmPointDic.Add((int)tm, tmPoint);
                    }

                    short? type = GetType(tp);
                    object objSpeed = GetSpeed(tp);
                    tmPoint.AddTpData(type, objSpeed);
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            //
        }

        private bool isValueTM(int tm)
        {
            string strTM = "TM" + tm;
            foreach (string str in tmCondition.TMList)
            {
                if (str == strTM)
                {
                    return true;
                }
            }
            return false;
        }

        protected virtual float? GetSINR(TestPoint tp)
        {
            return (float?)tp["lte_SINR"];
        }
        protected virtual int? GetMode(TestPoint tp)
        {
            return (int?)(short?)tp["lte_Transmission_Mode"];
        }

        protected virtual short? GetType(TestPoint tp)
        {
            return (short?)tp["lte_APP_type"];
        }
        protected virtual object GetSpeed(TestPoint tp)
        {
            return tp["lte_APP_Speed_Mb"];
        }
    }

    public class TMPointsSet
    {
        public TMPointsSet(string typeName)
        {
            this.TypeName = typeName;
            PointSet = new List<PointsSet>();
        }
        public string TypeName { get; set; }
        public int SumCount { get; set; }
        public List<PointsSet> PointSet { get; set; }

        public void GetPSetPer()
        {
            foreach (PointsSet pSet in PointSet)
            {
                this.SumCount += pSet.Count;
            }
            foreach (PointsSet pSet in PointSet)
            {
                pSet.Percent = Math.Round((double)pSet.Count / this.SumCount, 4) * 100;
            }
        }
    }
    public class PointsSet
    {
        public PointsSet(string typeName, int count)
        {
            this.TypeName = typeName;
            this.Count = count;
        }
        public string TypeName { get; set; }
        public int Count { get; set; }
        public double Percent { get; set; }
        public double AvgDownSpeed { get; set; }
        public double AvgUploadSpeed { get; set; }
        public TMPointsSpeed PointsSpeed { get; set; }
        public void GetAverageSpeed()
        {
            if (PointsSpeed != null)
            {
                if (PointsSpeed.DownSpeed > 0)
                {
                    AvgDownSpeed = Math.Round((PointsSpeed.DownSpeed / PointsSpeed.DownSpeedCount), 2);
                }
                if (PointsSpeed.UploadSpeed > 0)
                {
                    AvgUploadSpeed = Math.Round((PointsSpeed.UploadSpeed / PointsSpeed.UploadSpeedCount), 2);
                }
            }
        }
    }
    public class TMPointsSpeed
    {
        public TMPointsSpeed()
        { }
        public int SamplePointCount { get; set; }
        public float DownSpeed { get; set; }
        public float UploadSpeed { get; set; }
        public int DownSpeedCount { get; set; }
        public int UploadSpeedCount { get; set; }
        public void AddTpData(short? type, object objSpeed)
        {
            this.SamplePointCount++;
            if (type != null && objSpeed != null)
            {
                float speed = float.Parse(objSpeed.ToString());
                if (type == (int)AppType.FTP_Download)
                {
                    this.DownSpeedCount++;
                    this.DownSpeed += speed;
                }
                else if (type == (int)AppType.FTP_Upload)
                {
                    this.UploadSpeedCount++;
                    this.UploadSpeed += speed;
                }
            }
        }
    }
    public class TMCondition
    {
        public TMCondition()
        {
            TMList = new List<string>();
            RangeValues = new RangeSet();
        }
        public List<string> TMList { get; set; }
        public RangeSet RangeValues { get; set; }
    }

    public class LTETMQueryBase_FDD : LTETMQueryBase
    {
        protected LTETMQueryBase_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);

            Columns = new List<string>();
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_Transmission_Mode");

            carrierID = CarrierType.ChinaUnicom;
        }


        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26033, this.Name);
        }

        protected override float? GetSINR(TestPoint tp)
        {
            return (float?)tp["lte_fdd_SINR"];
        }
        protected override int? GetMode(TestPoint tp)
        {
            return (int?)(short?)tp["lte_fdd_Transmission_Mode"];
        }
        protected override short? GetType(TestPoint tp)
        {
            return (short?)tp["lte_fdd_APP_type"];
        }
        protected override object GetSpeed(TestPoint tp)
        {
            return tp["lte_fdd_APP_Speed_Mb"];
        }
    }
}
