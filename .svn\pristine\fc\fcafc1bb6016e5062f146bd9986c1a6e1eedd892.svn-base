﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    static class CsvFieldFormatter
    {
        public static string Format(string orgField)
        {
            if (string.IsNullOrEmpty(orgField))
            {
                return string.Empty;
            }
            string ret = orgField.Replace("\"", "\"\"");//字段中的单个双引号，要变成2个双引号，其中一个相当于转义符。
            if (ret.Contains("\"") || ret.Contains(","))//字段中包含双引号或者逗号时，需用双引号把整个字段括起来。
            {
                ret = "\"" + ret + "\"";
            }
            return ret;
        }
    }
}
