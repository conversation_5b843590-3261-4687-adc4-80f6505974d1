﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class VoiceAnaByFreqBandHelperBase
    {
        protected List<int> MoCallRequestEvtList = new List<int>();
        protected List<int> MoCallEstablishedEvtList = new List<int>();
        protected List<int> MoDropCallEvtList = new List<int>();
        protected List<int> MtCallRequestEvtList = new List<int>();
        protected List<int> MtCallEstablishedEvtList = new List<int>();
        protected List<int> MtDropCallEvtList = new List<int>();

        protected int fileTotalTestPoint = 0;
        protected Dictionary<string, VoiceAnaByFreqBandResult> resDic;
        protected string mosParamName = "";

        /// <summary>
        /// 初始化结果类
        /// </summary>
        /// <param name="str">频段名</param>
        /// <returns></returns>
        protected abstract VoiceAnaByFreqBandResult initRes(string str);

        /// <summary>
        /// 初始化事件列表
        /// </summary>
        /// <param name="moMtFlag">主被叫</param>
        /// <returns></returns>
        protected abstract void intEvtList();

        protected abstract int getEarfcn(TestPoint tp);

        protected abstract float? getRsrp(TestPoint tp);

        protected abstract float? getSinr(TestPoint tp);

        public virtual void DealWithData(DTFileDataManager file)
        {
            intEvtList();

            fileTotalTestPoint = 0;
            //initFreqBand();

            List<DTData> dtDataList = file.DTDatas;
            //dtDataList.Sort(DTData.ComparerBySnAsc);

            //总计结果
            VoiceAnaByFreqBandResult resTotal = initRes("总体");

            //频段对应的结果集
            resDic = new Dictionary<string, VoiceAnaByFreqBandResult>
            {
                { "A", initRes("A") },
                { "D", initRes("D") },
                { "E", initRes("E") },
                { "F", initRes("F") },
                { "FDD900", initRes("FDD900") },
                { "FDD1800", initRes("FDD1800") },
                { "n28", initRes("n28") },
                { "n41", initRes("n41") },
                { "n77", initRes("n77") },
                { "n78", initRes("n78") },
                { "n79", initRes("n79") },
            };

            //采样点对应的频段(用于获取事件对应的频段)
            Dictionary<int, TPFreqBandTime> tpFreqBandDic = new Dictionary<int, TPFreqBandTime>();

            try
            {
                //分析采样点,记录每个有效采样点对应的频段
                for (int i = 0; i < dtDataList.Count; i++)
                {
                    if (dtDataList[i] is TestPoint)
                    {
                        TestPoint tp = dtDataList[i] as TestPoint;
                        dealTP(tp, i, resDic, tpFreqBandDic);
                    }
                }

                for (int i = 0; i < dtDataList.Count; i++)
                {
                    if (dtDataList[i] is Event)
                    {
                        dealEvt(dtDataList[i], i, resDic, tpFreqBandDic);
                    }
                }

                foreach (var res in resDic.Values)
                {
                    res.SetTotalSampleCount(fileTotalTestPoint);
                    res.Calculate();
                    resTotal.Add(res);
                }
                resTotal.SetTotalSampleCount(fileTotalTestPoint);
                resTotal.Calculate();
                resDic.Add("总体", resTotal);
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine 
                    + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        protected virtual void dealTP(TestPoint tp, int i, Dictionary<string, VoiceAnaByFreqBandResult> resDic
            , Dictionary<int, TPFreqBandTime> tpFreqBandDic)
        {
            int earfcn = getEarfcn(tp);
            string freqBand = LteCellDataHelper.FreqBand_BeiJing.GetFreqBandByEarfcn(earfcn);

            if (!string.IsNullOrEmpty(freqBand))
            {
                float? rsrp, sinr;
                bool isValid = judgeValidTP(tp, out rsrp, out sinr);
                if (isValid)
                {
                    VoiceAnaByFreqBandResult res;
                    if (!resDic.TryGetValue(freqBand, out res))
                    {
                        res = initRes(freqBand);
                        resDic.Add(freqBand, res);
                    }
                    res.AddTP(tp, (float)rsrp, (float)sinr, ref mosParamName);
                    tpFreqBandDic[i] = new TPFreqBandTime(freqBand, tp.DateTime);
                    fileTotalTestPoint++;
                }
            }
        }

        protected virtual bool judgeValidTP(TestPoint tp, out float? rsrp, out float? sinr)
        {
            //规则定为:频点,rsrp,sinr有值且在正常范围内记为有效点
            sinr = null;

            rsrp = getRsrp(tp);
            if (rsrp == null)
            {
                return false;
            }

            sinr = getSinr(tp);
            if (sinr == null)
            {
                return false;
            }
            return true;
        }

        protected virtual void dealEvt(DTData data, int i
            , Dictionary<string, VoiceAnaByFreqBandResult> resDic, Dictionary<int, TPFreqBandTime> tpFreqBandDic)
        {
            Event evt = data as Event;
            int evtFlag = -1;
            if (MoCallRequestEvtList.Contains(evt.ID))
            {
                evtFlag = 1;
            }
            else if (MoCallEstablishedEvtList.Contains(evt.ID))
            {
                evtFlag = 2;
            }
            else if (MoDropCallEvtList.Contains(evt.ID))
            {
                evtFlag = 3;
            }
            else if (MtCallRequestEvtList.Contains(evt.ID))
            {
                evtFlag = 4;
            }
            else if (MtCallEstablishedEvtList.Contains(evt.ID))
            {
                evtFlag = 5;
            }
            else if (MtDropCallEvtList.Contains(evt.ID))
            {
                evtFlag = 6;
            }

            addValidEvt(evt, i, resDic, tpFreqBandDic, evtFlag);
        }

        private void addValidEvt(Event evt, int i, Dictionary<string, VoiceAnaByFreqBandResult> resDic, Dictionary<int, TPFreqBandTime> tpFreqBandDic, int evtFlag)
        {
            if (evtFlag == -1)
            {
                return;
            }

            //分析事件,根据事件时间获取最近的有效采样点,取该采样点的频段作为事件频段
            string nearestFreqBand = getNearestFreqBand(evt, i, tpFreqBandDic);
            if (!string.IsNullOrEmpty(nearestFreqBand))
            {
                VoiceAnaByFreqBandResult res;
                if (resDic.TryGetValue(nearestFreqBand, out res))
                {
                    res.AddEvt(evt, evtFlag);
                }
            }
        }

        protected virtual string getNearestFreqBand(Event evt, int i, Dictionary<int, TPFreqBandTime> tpFreqBandDic)
        {
            string nearestFreqBand = "";
            for (int idx = 1; idx <= 20; idx++)
            {
                nearestFreqBand = getValidFreqBand(evt, i, idx, tpFreqBandDic);
                if (nearestFreqBand == null)
                {
                    return "";
                }
                if (nearestFreqBand != "")
                {
                    break;
                }
            }
            return nearestFreqBand;
        }

        /// <summary>
        /// 获取事件有效的频段
        /// </summary>
        /// <param name="evt">事件</param>
        /// <param name="i">事件index</param>
        /// <param name="idx">往前推的序号</param>
        /// <param name="tpFreqBandDic">采样点频段集合</param>
        /// <returns>null是超过时间限制(目前写死5秒),""是当前序号没有获取到采样点频段,不为空正常返回频段结果</returns>
        private string getValidFreqBand(Event evt, int i, int idx, Dictionary<int, TPFreqBandTime> tpFreqBandDic)
        {
            //int flag = 0;
            //只获取事件之前的采样点
            TPFreqBandTime tpFreqBandTime;
            if (tpFreqBandDic.TryGetValue(i - idx, out tpFreqBandTime))
            {
                TimeSpan sp = evt.DateTime.Subtract(tpFreqBandTime.Time);
                if (sp.TotalMilliseconds > 5000)
                {
                    return null;
                }
                else
                {
                    return tpFreqBandTime.FreqBand;
                }
            }
            return "";
        }

        protected class TPFreqBandTime
        {
            public TPFreqBandTime(string freqBand, DateTime time)
            {
                FreqBand = freqBand;
                Time = time;
            }

            public string FreqBand { get; set; }
            public DateTime Time { get; set; }
        }
    }
}
