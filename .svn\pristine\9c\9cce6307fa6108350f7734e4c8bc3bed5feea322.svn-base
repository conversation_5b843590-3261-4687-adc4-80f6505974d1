﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.Util;
using System.Windows.Forms;
using System.Runtime.Serialization;
using System.Collections;
using MasterCom.MControls;
using MasterCom.RAMS.Func.EventBlock;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Func;
using System.Drawing.Imaging;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEMgrsCoveLayer : LayerBase
    {
        public LTEMgrsCoveLayer()
            : base("区域内栅格")
        { }
         private Dictionary<string, LTEMgrsGridInfo> dicGisData = null;

         public void iniData(Dictionary<string, LTEMgrsGridInfo> dicGisData)
         {
             this.dicGisData = dicGisData;
         }
         public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
         {
             if (!IsVisible)
             {
                 return;
             }
             if (dicGisData != null && dicGisData.Count > 0)
             {
                 DrawRectangle(graphics);
             }
         }
         private void DrawRectangle(Graphics graphics)
         {
             foreach (string key in dicGisData.Keys)
             {
                 Color rectColor = dicGisData[key].GridColor;

                 DbPoint dbP = new DbPoint(dicGisData[key].TLLng, dicGisData[key].TLLat);
                 PointF pointLeft, pointRight;
                 gisAdapter.ToDisplay(dbP, out pointLeft);
                 dbP = new DbPoint(dicGisData[key].BRLng, dicGisData[key].BRLat);
                 gisAdapter.ToDisplay(dbP, out pointRight);
                 RectangleF rectF = new RectangleF(pointLeft.X, pointLeft.Y,
                         pointRight.X - pointLeft.X, pointRight.Y - pointLeft.Y);
                 //==================0<=Alpha值<=255，越小越透明======================
                 graphics.FillRectangle(new SolidBrush(Color.FromArgb(200, rectColor)), rectF);

             }
             graphics.ResetTransform();
         }

         internal int MakeShpFile(string fileName, bool fixColor, Color colorSelected, bool compareBTS)
         {
             try
             {
                 if (dicGisData == null)
                 {
                     MessageBox.Show("没有可导出数据");
                     return - 1;
                 }

                 Shapefile shpFile = new Shapefile();
                 bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYGON);
                 if (!result)
                 {
                     MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                     return -1;
                 }
                 int idIdx = 0;
                 int fiMGRTIndex = idIdx++;
                 int fiLongitude = idIdx++;
                 int fiLatitude = idIdx++;
                 int fiRSRP = idIdx++;
                 int fiColor = idIdx;
                 ShapeHelper.InsertNewField(shpFile, "MGRTIndex", FieldType.STRING_FIELD, 10, 30, ref fiMGRTIndex);
                 ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLongitude);
                 ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLatitude);
                 ShapeHelper.InsertNewField(shpFile, "RSRP", FieldType.DOUBLE_FIELD, 10, 30, ref fiRSRP);
                 ShapeHelper.InsertNewField(shpFile, "Color", FieldType.INTEGER_FIELD, 10, 30, ref fiColor);
                                
                 int numShp = 0;
                 foreach (LTEMgrsGridInfo data in dicGisData.Values)
                 {
                     numShp++;
                     shpFile.EditInsertShape(ShapeHelper.CreateRectShape(data.TLLng, data.TLLat, data.BRLng, data.BRLat), ref numShp);
                     shpFile.EditCellValue(fiMGRTIndex, numShp, data.MgrsString);
                     shpFile.EditCellValue(fiLongitude, numShp, data.CentLng);
                     shpFile.EditCellValue(fiLatitude, numShp, data.CentLat);
                     shpFile.EditCellValue(fiRSRP, numShp, data.CoverageCellList.Count > 0 ? (double)data.CoverageCellList[0].AvgRsrp : 0);
                     shpFile.EditCellValue(fiColor, numShp, ColorTranslator.ToOle(data.GridColor));
                 }
                 ShapeHelper.DeleteShpFile(fileName);
                 if (!shpFile.SaveAs(fileName, null))
                 {
                     MessageBox.Show("保存文件失败！" + shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                     shpFile.Close();
                     return -1;
                 }
                 shpFile.Close();
                 return 1;
             }
             catch(Exception ex)
             {
                 MessageBox.Show("保存文件失败！" + ex.Message);
                 return -1;
             }
         }


    }
}
