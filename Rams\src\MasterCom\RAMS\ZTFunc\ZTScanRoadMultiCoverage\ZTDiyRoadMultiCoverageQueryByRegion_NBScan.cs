﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTDiyRoadMultiCoverageQueryByRegion_NBScan : ZTDiyRoadMultiCoverageQueryByRegion_TDLTEScan
    {
        public ZTDiyRoadMultiCoverageQueryByRegion_NBScan(ServiceName serviceName, MainModel mainModel)
            : base(serviceName, mainModel)
        {
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33010, this.Name);
        }
    }
}
