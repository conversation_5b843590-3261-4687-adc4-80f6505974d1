namespace MasterCom.RAMS.Func
{
    partial class EventInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(EventInfoForm));
            this.toolStrip = new System.Windows.Forms.ToolStrip();
            this.btnBlackBlock = new System.Windows.Forms.ToolStripButton();
            this.btnInputEvents = new System.Windows.Forms.ToolStripButton();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowInterference = new System.Windows.Forms.ToolStripMenuItem();
            this.showESResult = new System.Windows.Forms.ToolStripMenuItem();
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridCtrl = new DevExpress.XtraGrid.GridControl();
            this.grid1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDateTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBCCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBSIC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTargetCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTargetCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTargetLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTargetCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTargetBCCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTargetBSIC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnStreetDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaNameInfo = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaPlace = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellBSIC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMS = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAgentArea = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnESType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnESAnalysis = new DevExpress.XtraGrid.Columns.GridColumn();
            this.toolStrip.SuspendLayout();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grid1View)).BeginInit();
            this.SuspendLayout();
            // 
            // toolStrip
            // 
            this.toolStrip.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.toolStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.btnBlackBlock,
            this.btnInputEvents});
            this.toolStrip.Location = new System.Drawing.Point(0, 0);
            this.toolStrip.Name = "toolStrip";
            this.toolStrip.Size = new System.Drawing.Size(1102, 25);
            this.toolStrip.TabIndex = 3;
            this.toolStrip.Text = "toolStrip1";
            // 
            // btnBlackBlock
            // 
            this.btnBlackBlock.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.btnBlackBlock.Image = ((System.Drawing.Image)(resources.GetObject("btnBlackBlock.Image")));
            this.btnBlackBlock.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnBlackBlock.Name = "btnBlackBlock";
            this.btnBlackBlock.Size = new System.Drawing.Size(108, 22);
            this.btnBlackBlock.Text = "关联道路问题黑点";
            this.btnBlackBlock.Click += new System.EventHandler(this.btnBlackBlock_Click);
            // 
            // btnInputEvents
            // 
            this.btnInputEvents.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.btnInputEvents.Image = ((System.Drawing.Image)(resources.GetObject("btnInputEvents.Image")));
            this.btnInputEvents.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnInputEvents.Name = "btnInputEvents";
            this.btnInputEvents.Size = new System.Drawing.Size(60, 22);
            this.btnInputEvents.Text = "导入事件";
            this.btnInputEvents.Visible = false;
            this.btnInputEvents.Click += new System.EventHandler(this.btnInputEvents_Click);
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowInterference,
            this.showESResult,
            this.miReplayEvent,
            this.toolStripMenuItem1,
            this.miExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(173, 98);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miShowInterference
            // 
            this.miShowInterference.CheckOnClick = true;
            this.miShowInterference.Name = "miShowInterference";
            this.miShowInterference.Size = new System.Drawing.Size(172, 22);
            this.miShowInterference.Text = "显示同邻频干扰";
            this.miShowInterference.Click += new System.EventHandler(this.miShowInterference_Click);
            // 
            // showESResult
            // 
            this.showESResult.Name = "showESResult";
            this.showESResult.Size = new System.Drawing.Size(172, 22);
            this.showESResult.Text = "显示智能预判信息";
            this.showESResult.Click += new System.EventHandler(this.showESResult_Click);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(172, 22);
            this.miReplayEvent.Text = "回放事件";
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(169, 6);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(172, 22);
            this.miExportExcel.Text = "导出Excel...";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // gridCtrl
            // 
            this.gridCtrl.ContextMenuStrip = this.contextMenuStrip;
            this.gridCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrl.Location = new System.Drawing.Point(0, 25);
            this.gridCtrl.MainView = this.grid1View;
            this.gridCtrl.Name = "gridCtrl";
            this.gridCtrl.Size = new System.Drawing.Size(1102, 397);
            this.gridCtrl.TabIndex = 5;
            this.gridCtrl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.grid1View});
            // 
            // grid1View
            // 
            this.grid1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnID,
            this.gridColumnName,
            this.gridColumnDateTime,
            this.gridColumnLongitude,
            this.gridColumnLatitude,
            this.gridColumnCellName,
            this.gridColumnCode,
            this.gridColumnLAC,
            this.gridColumnCI,
            this.gridColumnBCCH,
            this.gridColumnBSIC,
            this.gridColumnTargetCellName,
            this.gridColumnTargetCode,
            this.gridColumnTargetLAC,
            this.gridColumnTargetCI,
            this.gridColumnTargetBCCH,
            this.gridColumnTargetBSIC,
            this.gridColumnStreetDesc,
            this.gridColumnFileName,
            this.gridColumnAreaNameInfo,
            this.gridColumnGridName,
            this.gridColumnAreaPlace,
            this.gridColumnCellBSIC,
            this.gridColumnMS,
            this.gridColumnAgentArea,
            this.gridColumnESType,
            this.gridColumnESAnalysis});
            this.grid1View.GridControl = this.gridCtrl;
            this.grid1View.IndicatorWidth = 35;
            this.grid1View.Name = "grid1View";
            this.grid1View.OptionsBehavior.Editable = false;
            this.grid1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.grid1View.OptionsSelection.MultiSelect = true;
            this.grid1View.OptionsView.ColumnAutoWidth = false;
            this.grid1View.OptionsView.ShowGroupPanel = false;
            this.grid1View.OptionsView.ShowIndicator = false;
            this.grid1View.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.gridView1_CustomDrawCell);
            this.grid1View.DoubleClick += new System.EventHandler(this.gridView1_DoubleClick);
            // 
            // gridColumnID
            // 
            this.gridColumnID.Caption = "序号";
            this.gridColumnID.Name = "gridColumnID";
            this.gridColumnID.Visible = true;
            this.gridColumnID.VisibleIndex = 0;
            this.gridColumnID.Width = 50;
            // 
            // gridColumnName
            // 
            this.gridColumnName.Caption = "事件名称";
            this.gridColumnName.FieldName = "Name";
            this.gridColumnName.Name = "gridColumnName";
            this.gridColumnName.Visible = true;
            this.gridColumnName.VisibleIndex = 1;
            this.gridColumnName.Width = 120;
            // 
            // gridColumnDateTime
            // 
            this.gridColumnDateTime.Caption = "时间";
            this.gridColumnDateTime.DisplayFormat.FormatString = "yy-MM-dd HH:mm:ss.fff";
            this.gridColumnDateTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumnDateTime.FieldName = "DateTime";
            this.gridColumnDateTime.Name = "gridColumnDateTime";
            this.gridColumnDateTime.Visible = true;
            this.gridColumnDateTime.VisibleIndex = 2;
            this.gridColumnDateTime.Width = 164;
            // 
            // gridColumnLongitude
            // 
            this.gridColumnLongitude.Caption = "经度";
            this.gridColumnLongitude.FieldName = "Longitude";
            this.gridColumnLongitude.Name = "gridColumnLongitude";
            this.gridColumnLongitude.Visible = true;
            this.gridColumnLongitude.VisibleIndex = 3;
            // 
            // gridColumnLatitude
            // 
            this.gridColumnLatitude.Caption = "纬度";
            this.gridColumnLatitude.FieldName = "Latitude";
            this.gridColumnLatitude.Name = "gridColumnLatitude";
            this.gridColumnLatitude.Visible = true;
            this.gridColumnLatitude.VisibleIndex = 4;
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "小区名";
            this.gridColumnCellName.FieldName = "CellNameSrc";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 5;
            this.gridColumnCellName.Width = 100;
            // 
            // gridColumnCode
            // 
            this.gridColumnCode.Caption = "CellCode";
            this.gridColumnCode.FieldName = "CellCodeSrc";
            this.gridColumnCode.Name = "gridColumnCode";
            this.gridColumnCode.Visible = true;
            this.gridColumnCode.VisibleIndex = 6;
            this.gridColumnCode.Width = 100;
            // 
            // gridColumnLAC
            // 
            this.gridColumnLAC.Caption = "LAC\\TAC";
            this.gridColumnLAC.FieldName = "LAC";
            this.gridColumnLAC.Name = "gridColumnLAC";
            this.gridColumnLAC.Visible = true;
            this.gridColumnLAC.VisibleIndex = 7;
            this.gridColumnLAC.Width = 100;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "CI\\ECI";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 8;
            this.gridColumnCI.Width = 100;
            // 
            // gridColumnBCCH
            // 
            this.gridColumnBCCH.Caption = "BCCH\\EARFCN";
            this.gridColumnBCCH.FieldName = "CellBCCHSrc";
            this.gridColumnBCCH.Name = "gridColumnBCCH";
            this.gridColumnBCCH.Visible = true;
            this.gridColumnBCCH.VisibleIndex = 9;
            this.gridColumnBCCH.Width = 100;
            // 
            // gridColumnBSIC
            // 
            this.gridColumnBSIC.Caption = "BSIC\\PCI";
            this.gridColumnBSIC.FieldName = "CellBSCISrc";
            this.gridColumnBSIC.Name = "gridColumnBSIC";
            this.gridColumnBSIC.Visible = true;
            this.gridColumnBSIC.VisibleIndex = 10;
            this.gridColumnBSIC.Width = 100;
            // 
            // gridColumnTargetCellName
            // 
            this.gridColumnTargetCellName.Caption = "目标小区名";
            this.gridColumnTargetCellName.FieldName = "CellNameTarget";
            this.gridColumnTargetCellName.Name = "gridColumnTargetCellName";
            this.gridColumnTargetCellName.Visible = true;
            this.gridColumnTargetCellName.VisibleIndex = 11;
            this.gridColumnTargetCellName.Width = 100;
            // 
            // gridColumnTargetCode
            // 
            this.gridColumnTargetCode.Caption = "目标CellCode";
            this.gridColumnTargetCode.FieldName = "CellCodeTarget";
            this.gridColumnTargetCode.Name = "gridColumnTargetCode";
            this.gridColumnTargetCode.Visible = true;
            this.gridColumnTargetCode.VisibleIndex = 12;
            this.gridColumnTargetCode.Width = 100;
            // 
            // gridColumnTargetLAC
            // 
            this.gridColumnTargetLAC.Caption = "目标LAC\\TAC";
            this.gridColumnTargetLAC.FieldName = "TargetLAC";
            this.gridColumnTargetLAC.Name = "gridColumnTargetLAC";
            this.gridColumnTargetLAC.Visible = true;
            this.gridColumnTargetLAC.VisibleIndex = 13;
            this.gridColumnTargetLAC.Width = 100;
            // 
            // gridColumnTargetCI
            // 
            this.gridColumnTargetCI.Caption = "目标CI\\ECI";
            this.gridColumnTargetCI.FieldName = "TargetCI";
            this.gridColumnTargetCI.Name = "gridColumnTargetCI";
            this.gridColumnTargetCI.Visible = true;
            this.gridColumnTargetCI.VisibleIndex = 14;
            this.gridColumnTargetCI.Width = 120;
            // 
            // gridColumnTargetBCCH
            // 
            this.gridColumnTargetBCCH.Caption = "目标BCCH\\EARFCN";
            this.gridColumnTargetBCCH.FieldName = "CellBCCHTarget";
            this.gridColumnTargetBCCH.Name = "gridColumnTargetBCCH";
            this.gridColumnTargetBCCH.Visible = true;
            this.gridColumnTargetBCCH.VisibleIndex = 15;
            this.gridColumnTargetBCCH.Width = 120;
            // 
            // gridColumnTargetBSIC
            // 
            this.gridColumnTargetBSIC.Caption = "目标BSIC\\PCI";
            this.gridColumnTargetBSIC.FieldName = "CellBSICTarget";
            this.gridColumnTargetBSIC.Name = "gridColumnTargetBSIC";
            this.gridColumnTargetBSIC.Visible = true;
            this.gridColumnTargetBSIC.VisibleIndex = 16;
            this.gridColumnTargetBSIC.Width = 120;
            // 
            // gridColumnStreetDesc
            // 
            this.gridColumnStreetDesc.Caption = "街道名称";
            this.gridColumnStreetDesc.FieldName = "RoadPlaceDesc";
            this.gridColumnStreetDesc.Name = "gridColumnStreetDesc";
            this.gridColumnStreetDesc.Visible = true;
            this.gridColumnStreetDesc.VisibleIndex = 17;
            this.gridColumnStreetDesc.Width = 120;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 18;
            this.gridColumnFileName.Width = 120;
            // 
            // gridColumnAreaNameInfo
            // 
            this.gridColumnAreaNameInfo.Caption = "区域信息";
            this.gridColumnAreaNameInfo.FieldName = "StrAreaInfo";
            this.gridColumnAreaNameInfo.Name = "gridColumnAreaNameInfo";
            this.gridColumnAreaNameInfo.Visible = true;
            this.gridColumnAreaNameInfo.VisibleIndex = 19;
            // 
            // gridColumnGridName
            // 
            this.gridColumnGridName.Caption = "网格名";
            this.gridColumnGridName.FieldName = "GridDesc";
            this.gridColumnGridName.Name = "gridColumnGridName";
            this.gridColumnGridName.Visible = true;
            this.gridColumnGridName.VisibleIndex = 20;
            // 
            // gridColumnAreaPlace
            // 
            this.gridColumnAreaPlace.Caption = "片区";
            this.gridColumnAreaPlace.FieldName = "AreaPlaceDesc";
            this.gridColumnAreaPlace.Name = "gridColumnAreaPlace";
            this.gridColumnAreaPlace.Visible = true;
            this.gridColumnAreaPlace.VisibleIndex = 21;
            // 
            // gridColumnCellBSIC
            // 
            this.gridColumnCellBSIC.Caption = "PCI";
            this.gridColumnCellBSIC.FieldName = "CellBSICSrc";
            this.gridColumnCellBSIC.Name = "gridColumnCellBSIC";
            this.gridColumnCellBSIC.Visible = true;
            this.gridColumnCellBSIC.VisibleIndex = 22;
            // 
            // gridColumnMS
            // 
            this.gridColumnMS.Caption = "MS";
            this.gridColumnMS.FieldName = "MS";
            this.gridColumnMS.Name = "gridColumnMS";
            this.gridColumnMS.Visible = true;
            this.gridColumnMS.VisibleIndex = 23;
            // 
            // gridColumnAgentArea
            // 
            this.gridColumnAgentArea.Caption = "代维分区";
            this.gridColumnAgentArea.FieldName = "AgentAreaName";
            this.gridColumnAgentArea.Name = "gridColumnAgentArea";
            this.gridColumnAgentArea.Visible = true;
            this.gridColumnAgentArea.VisibleIndex = 24;
            // 
            // gridColumnESType
            // 
            this.gridColumnESType.Caption = "预判类型";
            this.gridColumnESType.FieldName = "EsResultType";
            this.gridColumnESType.Name = "gridColumnESType";
            this.gridColumnESType.Visible = true;
            this.gridColumnESType.VisibleIndex = 25;
            this.gridColumnESType.Width = 120;
            // 
            // gridColumnESAnalysis
            // 
            this.gridColumnESAnalysis.Caption = "预判原因分析";
            this.gridColumnESAnalysis.FieldName = "ESResultAnalysis";
            this.gridColumnESAnalysis.Name = "gridColumnESAnalysis";
            this.gridColumnESAnalysis.Visible = true;
            this.gridColumnESAnalysis.VisibleIndex = 26;
            this.gridColumnESAnalysis.Width = 120;
            // 
            // EventInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1102, 422);
            this.Controls.Add(this.gridCtrl);
            this.Controls.Add(this.toolStrip);
            this.Name = "EventInfoForm";
            this.Text = "事件信息";
            this.toolStrip.ResumeLayout(false);
            this.toolStrip.PerformLayout();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grid1View)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ToolStrip toolStrip;
        private System.Windows.Forms.ToolStripButton btnBlackBlock;
        private System.Windows.Forms.ToolStripButton btnInputEvents;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miShowInterference;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvent;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem showESResult;
        private DevExpress.XtraGrid.GridControl gridCtrl;
        private DevExpress.XtraGrid.Views.Grid.GridView grid1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDateTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCode;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBCCH;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBSIC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTargetCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTargetCode;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTargetLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTargetCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTargetBCCH;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTargetBSIC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnStreetDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaNameInfo;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaPlace;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellBSIC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMS;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAgentArea;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnESType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnESAnalysis;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnID;
    }
}