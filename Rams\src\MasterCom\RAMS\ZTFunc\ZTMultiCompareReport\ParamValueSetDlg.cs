﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ParamValueSetDlg : BaseForm
    {
        MsgParamSetting curParam;
        string paramNameDes;
        public ParamValueSetDlg(MsgParamSetting param)
        {
            InitializeComponent();
            this.curParam = param;
            paramNameDes = textBoxParamName.Text = param.ParamNameDes;
            textBoxParamValueDes.Text = param.ParamRightValueDes;
            textBoxParamValue.Text = param.ParamRightValue;
        }

        public MsgParamSetting GetParamSetting()
        {
            if (!string.IsNullOrEmpty(textBoxParamValueDes.Text) && !string.IsNullOrEmpty(textBoxParamValue.Text))
            {
                curParam.ParamRightValueDes = textBoxParamValueDes.Text;
                curParam.ParamRightValue = textBoxParamValue.Text;
            }
            return curParam;
        }

        private void textBoxParamValue_TextChanged(object sender, EventArgs e)
        {
            textBoxParamValueDes.Text = getMsgParamDesc(paramNameDes, textBoxParamValue.Text);
        }

        private string getMsgParamDesc(string strParaName, string strParamRightValue)
        {
            string[] strArray = strParamRightValue.Split('；');
            if (strArray.Length == 1)
            {
                strArray = strParamRightValue.Split(';');
            }
            string strVlaue = getParamDesc(strParaName, strArray[0]);

            if (strArray.Length > 1)
            {
                string strValueIndoor = getParamDesc(strParaName, strArray[1]);
                if (!string.IsNullOrEmpty(strValueIndoor))
                {
                    strVlaue += "(室分" + strValueIndoor + ")";
                }
            }
            return strVlaue;
        }

        private string getParamDesc(string strParaName, string strValue)
        {
            string str = "";
            StringBuilder strbSingleValue = new StringBuilder();
            StringBuilder strbDesc = new StringBuilder();
            foreach (char chr in strValue)
            {
                if (chr != ',' && chr != '，' && chr != '~' && chr != '～')
                {
                    strbSingleValue.Append(chr);
                }
                else
                {
                    if (MsgParamSetting.GetParamSingleValue(strParaName, strbSingleValue.ToString(), ref str))
                    {
                        strbDesc.Append(str + chr);
                    }
                    strbSingleValue = new StringBuilder();
                }
            }
            if (strbSingleValue.Length > 0 && MsgParamSetting.GetParamSingleValue(strParaName, strbSingleValue.ToString(), ref str))
            {
                strbDesc.Append(str);
            }
            return strbDesc.ToString();
        }
    }
}
