﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationFilesCountManager
    {
        protected StationFilesCountManager()
        { 
            //
        }

        public static bool AddBtsFileCountToDBAuto()
        {
            try
            {
                StationAcceptAutoSet funcSet = StationAcceptAna_HB.GetInstance().FuncSet;
                if (!funcSet.FileCountAutoIsCheck)
                {
                    return false;
                }
                bool hasFoundFile = false;
                string folderName = funcSet.FileCountAutoULFolder;
                if (!string.IsNullOrEmpty(folderName) && System.IO.Directory.Exists(folderName))
                {
                    System.IO.DirectoryInfo dinfo = new System.IO.DirectoryInfo(folderName);
                    foreach (System.IO.FileInfo file in dinfo.GetFiles("*文件数量*", System.IO.SearchOption.TopDirectoryOnly))
                    {
                        if (FilterHelper.Excel.Contains(file.Extension))
                        {
                            hasFoundFile = true;
                            addBtsFileCountToDB(file.FullName);
                        }
                    }
                }
                return hasFoundFile;
            }
            catch (Exception ex)
            {
                writeLog(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
                return false;
            }
        }

        private static void addBtsFileCountToDB(string fileName)
        {
            System.Data.DataSet dataSet;
            try
            {
                int reReadCount = 0;
                while (reReadCount < 6 && FileStatus.FileIsOpen(fileName) == 1)
                {
                    System.Threading.Thread.Sleep(1000);
                    reReadCount++;
                }
                dataSet = ExcelNPOIManager.ImportFromExcel(fileName);

                if (dataSet != null && dataSet.Tables != null)
                {
                    foreach (DataTable tb in dataSet.Tables)
                    {
                        if (tb == null || tb.Rows.Count <= 0)
                        {
                            continue;
                        }
                        addTableInfoToDB(fileName, tb);
                    }
                }

                System.IO.File.Delete(fileName);
            }
            catch (Exception ex)
            {
                writeLog(fileName + "-" + ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
            }
            //finally
            //{
            //    GC.Collect();
            //}
        }

        private static void addTableInfoToDB(string fileName, DataTable tb)
        {
            string strDistrictName = tb.TableName;
            int districtId = DistrictManager.GetInstance().GetDistrictID(strDistrictName);
            if (districtId > 0)
            {
                Dictionary<int, int> fileCountDic = new Dictionary<int, int>();
                foreach (System.Data.DataRow row in tb.Rows)
                {
                    object objValue = row["ENodeBID"];
                    if (objValue == null || string.IsNullOrEmpty(objValue.ToString().Trim()))
                    {
                        continue;
                    }
                    int enodeBid = Convert.ToInt32(objValue);

                    objValue = row["文件个数"];
                    if (objValue == null || string.IsNullOrEmpty(objValue.ToString().Trim()))
                    {
                        continue;
                    }
                    fileCountDic[enodeBid] = Convert.ToInt32(objValue);
                }
                StationFilesCountDBOperator stationFileOpera = new StationFilesCountDBOperator(fileCountDic, districtId);
                stationFileOpera.Query();
                writeLog(fileName + "-" + strDistrictName + "表 上传成功");
            }
        }

        protected static void writeLog(string strErr)
        {
            string path = BackgroundFuncManager.BackgroundLogSavePath;

            if (!File.Exists(path))
            {
                File.Create(path).Close();
            }
            using (StreamWriter sw = File.AppendText(path))
            {
                sw.Write(DateTime.Now.ToString() + "  [自动上传新站文件数量表]\r\n" + strErr + "\r\n");
                sw.Flush();
                sw.Close();
            }
        }
    }
}
