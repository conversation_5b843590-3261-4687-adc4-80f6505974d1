﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LTEWeakCoverRoadSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.numRSRP = new System.Windows.Forms.NumericUpDown();
            this.numMinDistance = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numMaxTPDistance = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.numSINRMax = new System.Windows.Forms.NumericUpDown();
            this.chkSINR = new System.Windows.Forms.CheckBox();
            this.chkNbRSRP = new System.Windows.Forms.CheckBox();
            this.numNbRSRP = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label12 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.numSampleCellAngle = new System.Windows.Forms.NumericUpDown();
            this.label10 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.numSampleCellDistance = new System.Windows.Forms.NumericUpDown();
            this.label13 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.cmbCompare = new System.Windows.Forms.ComboBox();
            this.cbxAssocAnalysis = new System.Windows.Forms.CheckBox();
            this.label15 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.numMinWeakPercent = new System.Windows.Forms.NumericUpDown();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.radAndOr = new DevExpress.XtraEditors.RadioGroup();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chkMinDuration = new System.Windows.Forms.CheckBox();
            this.chkMinDistance = new System.Windows.Forms.CheckBox();
            this.label18 = new System.Windows.Forms.Label();
            this.numMinDuration = new System.Windows.Forms.NumericUpDown();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSINRMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNbRSRP)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCellAngle)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCellDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinWeakPercent)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radAndOr.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDuration)).BeginInit();
            this.SuspendLayout();
            // 
            // numRSRP
            // 
            this.numRSRP.Location = new System.Drawing.Point(78, 20);
            this.numRSRP.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRSRP.Name = "numRSRP";
            this.numRSRP.Size = new System.Drawing.Size(80, 21);
            this.numRSRP.TabIndex = 0;
            this.numRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRSRP.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // numMinDistance
            // 
            this.numMinDistance.Location = new System.Drawing.Point(432, 20);
            this.numMinDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDistance.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMinDistance.Name = "numMinDistance";
            this.numMinDistance.Size = new System.Drawing.Size(80, 21);
            this.numMinDistance.TabIndex = 3;
            this.numMinDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(246, 61);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 55;
            this.label7.Text = "米";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(164, 24);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(23, 12);
            this.label3.TabIndex = 47;
            this.label3.Text = "dBm";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(53, 61);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 53;
            this.label6.Text = "相邻采样点距离≤";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(519, 25);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 48;
            this.label4.Text = "米";
            // 
            // numMaxTPDistance
            // 
            this.numMaxTPDistance.Location = new System.Drawing.Point(159, 56);
            this.numMaxTPDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxTPDistance.Name = "numMaxTPDistance";
            this.numMaxTPDistance.Size = new System.Drawing.Size(80, 21);
            this.numMaxTPDistance.TabIndex = 4;
            this.numMaxTPDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxTPDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(427, 341);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 5;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(521, 341);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "取消";
            // 
            // numSINRMax
            // 
            this.numSINRMax.Enabled = false;
            this.numSINRMax.Location = new System.Drawing.Point(256, 17);
            this.numSINRMax.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numSINRMax.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numSINRMax.Name = "numSINRMax";
            this.numSINRMax.Size = new System.Drawing.Size(80, 21);
            this.numSINRMax.TabIndex = 2;
            this.numSINRMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSINRMax.Value = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            // 
            // chkSINR
            // 
            this.chkSINR.AutoSize = true;
            this.chkSINR.Location = new System.Drawing.Point(193, 21);
            this.chkSINR.Name = "chkSINR";
            this.chkSINR.Size = new System.Drawing.Size(60, 16);
            this.chkSINR.TabIndex = 56;
            this.chkSINR.Text = "SINR＜";
            this.chkSINR.UseVisualStyleBackColor = true;
            this.chkSINR.CheckedChanged += new System.EventHandler(this.chkSINR_CheckedChanged);
            // 
            // chkNbRSRP
            // 
            this.chkNbRSRP.Location = new System.Drawing.Point(33, 60);
            this.chkNbRSRP.Name = "chkNbRSRP";
            this.chkNbRSRP.Size = new System.Drawing.Size(96, 16);
            this.chkNbRSRP.TabIndex = 56;
            this.chkNbRSRP.Text = "最强邻区RSRP";
            this.chkNbRSRP.UseVisualStyleBackColor = true;
            this.chkNbRSRP.CheckedChanged += new System.EventHandler(this.chkNbRSRP_CheckedChanged);
            // 
            // numNbRSRP
            // 
            this.numNbRSRP.Enabled = false;
            this.numNbRSRP.Location = new System.Drawing.Point(160, 56);
            this.numNbRSRP.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numNbRSRP.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numNbRSRP.Name = "numNbRSRP";
            this.numNbRSRP.Size = new System.Drawing.Size(80, 21);
            this.numNbRSRP.TabIndex = 1;
            this.numNbRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numNbRSRP.Value = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(246, 58);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(23, 12);
            this.label5.TabIndex = 47;
            this.label5.Text = "dBm";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(342, 22);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 47;
            this.label8.Text = "dB";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label12);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.numSampleCellAngle);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Controls.Add(this.numSampleCellDistance);
            this.groupBox1.Controls.Add(this.label13);
            this.groupBox1.Controls.Add(this.label14);
            this.groupBox1.Location = new System.Drawing.Point(31, 217);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(566, 105);
            this.groupBox1.TabIndex = 57;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "优化问题判断条件";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.ForeColor = System.Drawing.Color.Red;
            this.label12.Location = new System.Drawing.Point(246, 69);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(245, 12);
            this.label12.TabIndex = 29;
            this.label12.Text = "（周边没有符合条件且覆盖到此路段的小区）";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(31, 69);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(209, 12);
            this.label11.TabIndex = 28;
            this.label11.Text = "注：不符合上述条件，归类为规划问题";
            // 
            // numSampleCellAngle
            // 
            this.numSampleCellAngle.Location = new System.Drawing.Point(433, 25);
            this.numSampleCellAngle.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numSampleCellAngle.Name = "numSampleCellAngle";
            this.numSampleCellAngle.Size = new System.Drawing.Size(80, 21);
            this.numSampleCellAngle.TabIndex = 27;
            this.numSampleCellAngle.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSampleCellAngle.Value = new decimal(new int[] {
            60,
            0,
            0,
            0});
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(519, 30);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(17, 12);
            this.label10.TabIndex = 26;
            this.label10.Text = "度";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(308, 30);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(119, 12);
            this.label9.TabIndex = 25;
            this.label9.Text = "采样点与小区夹角 ≤";
            // 
            // numSampleCellDistance
            // 
            this.numSampleCellDistance.Location = new System.Drawing.Point(160, 25);
            this.numSampleCellDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numSampleCellDistance.Name = "numSampleCellDistance";
            this.numSampleCellDistance.Size = new System.Drawing.Size(80, 21);
            this.numSampleCellDistance.TabIndex = 24;
            this.numSampleCellDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSampleCellDistance.Value = new decimal(new int[] {
            300,
            0,
            0,
            0});
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(246, 29);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 23;
            this.label13.Text = "米";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(31, 31);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(119, 12);
            this.label14.TabIndex = 23;
            this.label14.Text = "采样点与小区距离 ≤";
            // 
            // cmbCompare
            // 
            this.cmbCompare.Enabled = false;
            this.cmbCompare.FormattingEnabled = true;
            this.cmbCompare.Items.AddRange(new object[] {
            "<",
            ">"});
            this.cmbCompare.Location = new System.Drawing.Point(125, 55);
            this.cmbCompare.Name = "cmbCompare";
            this.cmbCompare.Size = new System.Drawing.Size(32, 22);
            this.cmbCompare.TabIndex = 58;
            // 
            // cbxAssocAnalysis
            // 
            this.cbxAssocAnalysis.AutoSize = true;
            this.cbxAssocAnalysis.Location = new System.Drawing.Point(31, 341);
            this.cbxAssocAnalysis.Name = "cbxAssocAnalysis";
            this.cbxAssocAnalysis.Size = new System.Drawing.Size(72, 16);
            this.cbxAssocAnalysis.TabIndex = 59;
            this.cbxAssocAnalysis.Text = "关联分析";
            this.toolTip.SetToolTip(this.cbxAssocAnalysis, "需要预先导入小区关联信息");
            this.cbxAssocAnalysis.UseVisualStyleBackColor = true;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(248, 24);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(11, 12);
            this.label15.TabIndex = 62;
            this.label15.Text = "%";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(66, 25);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(89, 12);
            this.label16.TabIndex = 61;
            this.label16.Text = "弱覆盖点占比≥";
            // 
            // numMinWeakPercent
            // 
            this.numMinWeakPercent.Location = new System.Drawing.Point(159, 19);
            this.numMinWeakPercent.Name = "numMinWeakPercent";
            this.numMinWeakPercent.Size = new System.Drawing.Size(80, 21);
            this.numMinWeakPercent.TabIndex = 60;
            this.numMinWeakPercent.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinWeakPercent.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.radAndOr);
            this.groupBox2.Controls.Add(this.cmbCompare);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.numRSRP);
            this.groupBox2.Controls.Add(this.numSINRMax);
            this.groupBox2.Controls.Add(this.chkNbRSRP);
            this.groupBox2.Controls.Add(this.numNbRSRP);
            this.groupBox2.Controls.Add(this.chkSINR);
            this.groupBox2.Location = new System.Drawing.Point(31, 12);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(566, 91);
            this.groupBox2.TabIndex = 63;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "指标限定（RSRP、SINR且或关系可选,，但与邻区RSRP为且关系）";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(31, 26);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 80;
            this.label1.Text = "RSRP≤";
            // 
            // radAndOr
            // 
            this.radAndOr.EditValue = true;
            this.radAndOr.Enabled = false;
            this.radAndOr.Location = new System.Drawing.Point(391, 17);
            this.radAndOr.Name = "radAndOr";
            this.radAndOr.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "且关系"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "或关系")});
            this.radAndOr.Size = new System.Drawing.Size(100, 53);
            this.radAndOr.TabIndex = 60;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.chkMinDuration);
            this.groupBox3.Controls.Add(this.chkMinDistance);
            this.groupBox3.Controls.Add(this.label18);
            this.groupBox3.Controls.Add(this.numMinDuration);
            this.groupBox3.Controls.Add(this.numMaxTPDistance);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.label16);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.numMinWeakPercent);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.numMinDistance);
            this.groupBox3.Location = new System.Drawing.Point(31, 112);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(565, 96);
            this.groupBox3.TabIndex = 64;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "持续性";
            // 
            // chkMinDuration
            // 
            this.chkMinDuration.AutoSize = true;
            this.chkMinDuration.Location = new System.Drawing.Point(367, 61);
            this.chkMinDuration.Name = "chkMinDuration";
            this.chkMinDuration.Size = new System.Drawing.Size(60, 16);
            this.chkMinDuration.TabIndex = 67;
            this.chkMinDuration.Text = "时长≥";
            this.chkMinDuration.UseVisualStyleBackColor = true;
            this.chkMinDuration.CheckedChanged += new System.EventHandler(this.chkMinDuration_CheckedChanged);
            // 
            // chkMinDistance
            // 
            this.chkMinDistance.AutoSize = true;
            this.chkMinDistance.Checked = true;
            this.chkMinDistance.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkMinDistance.Location = new System.Drawing.Point(343, 24);
            this.chkMinDistance.Name = "chkMinDistance";
            this.chkMinDistance.Size = new System.Drawing.Size(84, 16);
            this.chkMinDistance.TabIndex = 66;
            this.chkMinDistance.Text = "持续距离≥";
            this.chkMinDistance.UseVisualStyleBackColor = true;
            this.chkMinDistance.CheckedChanged += new System.EventHandler(this.chkMinDistance_CheckedChanged);
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(519, 61);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(17, 12);
            this.label18.TabIndex = 65;
            this.label18.Text = "秒";
            // 
            // numMinDuration
            // 
            this.numMinDuration.Enabled = false;
            this.numMinDuration.Location = new System.Drawing.Point(432, 56);
            this.numMinDuration.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDuration.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMinDuration.Name = "numMinDuration";
            this.numMinDuration.Size = new System.Drawing.Size(80, 21);
            this.numMinDuration.TabIndex = 63;
            this.numMinDuration.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDuration.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // LTEWeakCoverRoadSettingDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(623, 379);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.cbxAssocAnalysis);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "LTEWeakCoverRoadSettingDlg";
            this.Text = "弱覆盖道路条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSINRMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNbRSRP)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCellAngle)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCellDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinWeakPercent)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radAndOr.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDuration)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.NumericUpDown numRSRP;
        private System.Windows.Forms.NumericUpDown numMinDistance;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numMaxTPDistance;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.NumericUpDown numSINRMax;
        private System.Windows.Forms.CheckBox chkSINR;
        private System.Windows.Forms.CheckBox chkNbRSRP;
        private System.Windows.Forms.NumericUpDown numNbRSRP;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown numSampleCellAngle;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown numSampleCellDistance;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.ComboBox cmbCompare;
        private System.Windows.Forms.CheckBox cbxAssocAnalysis;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.NumericUpDown numMinWeakPercent;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.NumericUpDown numMinDuration;
        private System.Windows.Forms.CheckBox chkMinDuration;
        private System.Windows.Forms.CheckBox chkMinDistance;
        private DevExpress.XtraEditors.RadioGroup radAndOr;
        private System.Windows.Forms.Label label1;
    }
}