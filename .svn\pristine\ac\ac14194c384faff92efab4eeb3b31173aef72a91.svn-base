﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class RoadQuaAnaInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gridViewGridCell = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColGridCellRxlev = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColGridCellRsrp = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColGridCellSinr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColGridCellLteCoverRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlGrid = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStripExport = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExportAllGrid = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExpand = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemCollapse = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewGrid = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColGridRxlev = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColGridRsrp = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColGridSinr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColGridLteCoverRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlSum = new DevExpress.XtraGrid.GridControl();
            this.gridViewSum = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlCountySum = new DevExpress.XtraGrid.GridControl();
            this.gridViewCountySum = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColProblemRoad24G = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColProblemRoad4G = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColProblemRoad2G = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlRoad = new DevExpress.XtraGrid.GridControl();
            this.gridViewRoad = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColRoadLabelRsrp = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColRoadLabelSinr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColRoadLabelLteCoverRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColRoadLabelRxlev = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColIsProblem = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.chkDrawRoadLabel = new DevExpress.XtraEditors.CheckEdit();
            this.cmbGridColorMode = new DevExpress.XtraEditors.ComboBoxEdit();
            this.btnEditColor = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.chkOnlyShowProblemRoad = new DevExpress.XtraEditors.CheckEdit();
            this.timeExitCellBegin = new DevExpress.XtraEditors.DateEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtRoadLabel = new DevExpress.XtraEditors.TextEdit();
            this.btsSearch = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.timeExitCellEnd = new DevExpress.XtraEditors.DateEdit();
            this.cmbMainLines = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.txtRoadMainDes = new System.Windows.Forms.RichTextBox();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGridCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGrid)).BeginInit();
            this.contextMenuStripExport.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCountySum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCountySum)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRoad)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRoad)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkDrawRoadLabel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbGridColorMode.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkOnlyShowProblemRoad.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellBegin.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellBegin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRoadLabel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellEnd.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellEnd.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbMainLines.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // gridViewGridCell
            // 
            this.gridViewGridCell.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn25,
            this.gridColumn34,
            this.gridColumn44,
            this.gridColumn35,
            this.gridColGridCellRxlev,
            this.gridColGridCellRsrp,
            this.gridColGridCellSinr,
            this.gridColGridCellLteCoverRate,
            this.gridColumn42,
            this.gridColumn18});
            this.gridViewGridCell.GridControl = this.gridControlGrid;
            this.gridViewGridCell.Name = "gridViewGridCell";
            this.gridViewGridCell.OptionsBehavior.Editable = false;
            this.gridViewGridCell.OptionsDetail.ShowDetailTabs = false;
            this.gridViewGridCell.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "小区名";
            this.gridColumn25.FieldName = "CellName";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 0;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "小区状态";
            this.gridColumn34.FieldName = "CellState";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 1;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "小区类型";
            this.gridColumn44.FieldName = "NetType";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 2;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "小区采样点数";
            this.gridColumn35.FieldName = "SampleNum_Total";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 3;
            // 
            // gridColGridCellRxlev
            // 
            this.gridColGridCellRxlev.Caption = "小区平均RxLev";
            this.gridColGridCellRxlev.FieldName = "RxlevAvg";
            this.gridColGridCellRxlev.Name = "gridColGridCellRxlev";
            this.gridColGridCellRxlev.Visible = true;
            this.gridColGridCellRxlev.VisibleIndex = 4;
            // 
            // gridColGridCellRsrp
            // 
            this.gridColGridCellRsrp.Caption = "小区平均RSRP";
            this.gridColGridCellRsrp.FieldName = "RsrpAvg";
            this.gridColGridCellRsrp.Name = "gridColGridCellRsrp";
            this.gridColGridCellRsrp.Visible = true;
            this.gridColGridCellRsrp.VisibleIndex = 5;
            // 
            // gridColGridCellSinr
            // 
            this.gridColGridCellSinr.Caption = "小区平均SINR";
            this.gridColGridCellSinr.FieldName = "SinrAvg";
            this.gridColGridCellSinr.Name = "gridColGridCellSinr";
            this.gridColGridCellSinr.Visible = true;
            this.gridColGridCellSinr.VisibleIndex = 6;
            // 
            // gridColGridCellLteCoverRate
            // 
            this.gridColGridCellLteCoverRate.Caption = "小区LTE综合覆盖率(%)";
            this.gridColGridCellLteCoverRate.FieldName = "LteCoverRate";
            this.gridColGridCellLteCoverRate.Name = "gridColGridCellLteCoverRate";
            this.gridColGridCellLteCoverRate.Visible = true;
            this.gridColGridCellLteCoverRate.VisibleIndex = 7;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "小区故障告警";
            this.gridColumn42.FieldName = "ExitInfoDes";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 8;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "备注";
            this.gridColumn18.FieldName = "StrDes";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 9;
            // 
            // gridControlGrid
            // 
            this.gridControlGrid.ContextMenuStrip = this.contextMenuStripExport;
            this.gridControlGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlGrid.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode1.LevelTemplate = this.gridViewGridCell;
            gridLevelNode1.RelationName = "RoadPartCellList";
            this.gridControlGrid.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControlGrid.Location = new System.Drawing.Point(0, 0);
            this.gridControlGrid.MainView = this.gridViewGrid;
            this.gridControlGrid.Name = "gridControlGrid";
            this.gridControlGrid.ShowOnlyPredefinedDetails = true;
            this.gridControlGrid.Size = new System.Drawing.Size(1001, 210);
            this.gridControlGrid.TabIndex = 5;
            this.gridControlGrid.UseEmbeddedNavigator = true;
            this.gridControlGrid.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewGrid,
            this.gridViewGridCell});
            this.gridControlGrid.DoubleClick += new System.EventHandler(this.gridControlGrid_DoubleClick);
            // 
            // contextMenuStripExport
            // 
            this.contextMenuStripExport.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport,
            this.ToolStripMenuItemExportAllGrid,
            this.ToolStripMenuItemExpand,
            this.ToolStripMenuItemCollapse});
            this.contextMenuStripExport.Name = "contextMenuStripNoFlow";
            this.contextMenuStripExport.Size = new System.Drawing.Size(209, 92);
            this.contextMenuStripExport.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStripExport_Opening);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(208, 22);
            this.ToolStripMenuItemExport.Text = "导出到xls...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // ToolStripMenuItemExportAllGrid
            // 
            this.ToolStripMenuItemExportAllGrid.Name = "ToolStripMenuItemExportAllGrid";
            this.ToolStripMenuItemExportAllGrid.Size = new System.Drawing.Size(208, 22);
            this.ToolStripMenuItemExportAllGrid.Text = "导出全部栅格信息到xls...";
            this.ToolStripMenuItemExportAllGrid.Click += new System.EventHandler(this.ToolStripMenuItemExportAllGrid_Click);
            // 
            // ToolStripMenuItemExpand
            // 
            this.ToolStripMenuItemExpand.Name = "ToolStripMenuItemExpand";
            this.ToolStripMenuItemExpand.Size = new System.Drawing.Size(208, 22);
            this.ToolStripMenuItemExpand.Text = "全部展开";
            this.ToolStripMenuItemExpand.Click += new System.EventHandler(this.ToolStripMenuItemExpand_Click);
            // 
            // ToolStripMenuItemCollapse
            // 
            this.ToolStripMenuItemCollapse.Name = "ToolStripMenuItemCollapse";
            this.ToolStripMenuItemCollapse.Size = new System.Drawing.Size(208, 22);
            this.ToolStripMenuItemCollapse.Text = "全部收起";
            this.ToolStripMenuItemCollapse.Click += new System.EventHandler(this.ToolStripMenuItemCollapse_Click);
            // 
            // gridViewGrid
            // 
            this.gridViewGrid.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn43,
            this.gridColumn14,
            this.gridColumn23,
            this.gridColumn30,
            this.gridColGridRxlev,
            this.gridColGridRsrp,
            this.gridColGridSinr,
            this.gridColGridLteCoverRate,
            this.gridColumn15,
            this.gridColumn16});
            this.gridViewGrid.GridControl = this.gridControlGrid;
            this.gridViewGrid.Name = "gridViewGrid";
            this.gridViewGrid.OptionsBehavior.Editable = false;
            this.gridViewGrid.OptionsDetail.ShowDetailTabs = false;
            this.gridViewGrid.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "路段栅格编号";
            this.gridColumn26.FieldName = "RoadPartAreaId";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 0;
            this.gridColumn26.Width = 64;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "所属道路";
            this.gridColumn27.FieldName = "RoadName";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 1;
            this.gridColumn27.Width = 88;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "所属路段";
            this.gridColumn43.FieldName = "RoadLabel";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 2;
            this.gridColumn43.Width = 112;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "是否为2G问题栅格";
            this.gridColumn14.FieldName = "IsAbnormalStr_2G";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 3;
            this.gridColumn14.Width = 85;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "是否为4G问题栅格";
            this.gridColumn23.FieldName = "IsAbnormalStr_4G";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 4;
            this.gridColumn23.Width = 85;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "总采样点数";
            this.gridColumn30.FieldName = "SampleNum_Total";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 5;
            this.gridColumn30.Width = 62;
            // 
            // gridColGridRxlev
            // 
            this.gridColGridRxlev.Caption = "平均RxLev";
            this.gridColGridRxlev.DisplayFormat.FormatString = "0.00";
            this.gridColGridRxlev.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColGridRxlev.FieldName = "RxlevAvg";
            this.gridColGridRxlev.Name = "gridColGridRxlev";
            this.gridColGridRxlev.Visible = true;
            this.gridColGridRxlev.VisibleIndex = 6;
            this.gridColGridRxlev.Width = 62;
            // 
            // gridColGridRsrp
            // 
            this.gridColGridRsrp.Caption = "平均RSRP";
            this.gridColGridRsrp.DisplayFormat.FormatString = "0.00";
            this.gridColGridRsrp.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColGridRsrp.FieldName = "RsrpAvg";
            this.gridColGridRsrp.Name = "gridColGridRsrp";
            this.gridColGridRsrp.Visible = true;
            this.gridColGridRsrp.VisibleIndex = 7;
            this.gridColGridRsrp.Width = 62;
            // 
            // gridColGridSinr
            // 
            this.gridColGridSinr.Caption = "平均SINR";
            this.gridColGridSinr.DisplayFormat.FormatString = "0.00";
            this.gridColGridSinr.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColGridSinr.FieldName = "SinrAvg";
            this.gridColGridSinr.Name = "gridColGridSinr";
            this.gridColGridSinr.Visible = true;
            this.gridColGridSinr.VisibleIndex = 8;
            this.gridColGridSinr.Width = 62;
            // 
            // gridColGridLteCoverRate
            // 
            this.gridColGridLteCoverRate.Caption = "LTE综合覆盖率(%)";
            this.gridColGridLteCoverRate.DisplayFormat.FormatString = "0.00";
            this.gridColGridLteCoverRate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColGridLteCoverRate.FieldName = "LteCoverRate";
            this.gridColGridLteCoverRate.Name = "gridColGridLteCoverRate";
            this.gridColGridLteCoverRate.Visible = true;
            this.gridColGridLteCoverRate.VisibleIndex = 9;
            this.gridColGridLteCoverRate.Width = 120;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "主覆盖小区数";
            this.gridColumn15.FieldName = "MainCellCount";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 10;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "退服小区数";
            this.gridColumn16.FieldName = "MainCellCount_Exit";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 11;
            this.gridColumn16.Width = 103;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(1008, 662);
            this.xtraTabControl1.TabIndex = 2;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.splitContainerControl1);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1001, 632);
            this.xtraTabPage1.Text = "汇总信息";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridControlSum);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.gridControlCountySum);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1001, 632);
            this.splitContainerControl1.SplitterPosition = 83;
            this.splitContainerControl1.TabIndex = 0;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridControlSum
            // 
            this.gridControlSum.ContextMenuStrip = this.contextMenuStripExport;
            this.gridControlSum.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlSum.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlSum.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlSum.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlSum.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlSum.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlSum.Location = new System.Drawing.Point(0, 0);
            this.gridControlSum.MainView = this.gridViewSum;
            this.gridControlSum.Name = "gridControlSum";
            this.gridControlSum.Size = new System.Drawing.Size(1001, 83);
            this.gridControlSum.TabIndex = 4;
            this.gridControlSum.UseEmbeddedNavigator = true;
            this.gridControlSum.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewSum});
            // 
            // gridViewSum
            // 
            this.gridViewSum.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22});
            this.gridViewSum.GridControl = this.gridControlSum;
            this.gridViewSum.Name = "gridViewSum";
            this.gridViewSum.OptionsBehavior.Editable = false;
            this.gridViewSum.OptionsSelection.MultiSelect = true;
            this.gridViewSum.OptionsView.ShowDetailButtons = false;
            this.gridViewSum.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "重要干道路数";
            this.gridColumn7.FieldName = "MainRoadCount";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 0;
            this.gridColumn7.Width = 100;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "总里程(公里)";
            this.gridColumn8.DisplayFormat.FormatString = "0.00";
            this.gridColumn8.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn8.FieldName = "LengthTotal_KM";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 1;
            this.gridColumn8.Width = 100;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "总路段数";
            this.gridColumn10.FieldName = "MainRoadLabelCount";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 2;
            this.gridColumn10.Width = 84;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "问题路段数";
            this.gridColumn11.FieldName = "ProblemRoadLabelCount";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 3;
            this.gridColumn11.Width = 100;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "问题里程数(公里)";
            this.gridColumn12.DisplayFormat.FormatString = "0.00";
            this.gridColumn12.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn12.FieldName = "Affectoi_KM";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 4;
            this.gridColumn12.Width = 124;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "问题里程数占比（%）";
            this.gridColumn19.FieldName = "ProblemLengthRate";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 5;
            this.gridColumn19.Width = 158;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "主覆盖小区数";
            this.gridColumn20.FieldName = "MainCellCount";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 6;
            this.gridColumn20.Width = 100;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "退服小区数";
            this.gridColumn21.FieldName = "ExitCellCount";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 7;
            this.gridColumn21.Width = 100;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "退服率（%）";
            this.gridColumn22.FieldName = "ExitCellPer";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 8;
            this.gridColumn22.Width = 100;
            // 
            // gridControlCountySum
            // 
            this.gridControlCountySum.ContextMenuStrip = this.contextMenuStripExport;
            this.gridControlCountySum.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCountySum.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlCountySum.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlCountySum.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlCountySum.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlCountySum.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlCountySum.Location = new System.Drawing.Point(0, 0);
            this.gridControlCountySum.MainView = this.gridViewCountySum;
            this.gridControlCountySum.Name = "gridControlCountySum";
            this.gridControlCountySum.Size = new System.Drawing.Size(1001, 543);
            this.gridControlCountySum.TabIndex = 4;
            this.gridControlCountySum.UseEmbeddedNavigator = true;
            this.gridControlCountySum.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewCountySum});
            // 
            // gridViewCountySum
            // 
            this.gridViewCountySum.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn13,
            this.gridColProblemRoad24G,
            this.gridColProblemRoad4G,
            this.gridColProblemRoad2G,
            this.gridColumn17});
            this.gridViewCountySum.GridControl = this.gridControlCountySum;
            this.gridViewCountySum.Name = "gridViewCountySum";
            this.gridViewCountySum.OptionsBehavior.Editable = false;
            this.gridViewCountySum.OptionsSelection.MultiSelect = true;
            this.gridViewCountySum.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "分区";
            this.gridColumn13.FieldName = "CountyName";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 0;
            this.gridColumn13.Width = 112;
            // 
            // gridColProblemRoad24G
            // 
            this.gridColProblemRoad24G.Caption = "2G+4G问题路段数";
            this.gridColProblemRoad24G.FieldName = "ProblemRoadCount_24G";
            this.gridColProblemRoad24G.Name = "gridColProblemRoad24G";
            this.gridColProblemRoad24G.Visible = true;
            this.gridColProblemRoad24G.VisibleIndex = 1;
            this.gridColProblemRoad24G.Width = 299;
            // 
            // gridColProblemRoad4G
            // 
            this.gridColProblemRoad4G.Caption = "4G问题路段数";
            this.gridColProblemRoad4G.FieldName = "ProblemRoadCount_4G";
            this.gridColProblemRoad4G.Name = "gridColProblemRoad4G";
            this.gridColProblemRoad4G.Visible = true;
            this.gridColProblemRoad4G.VisibleIndex = 2;
            this.gridColProblemRoad4G.Width = 318;
            // 
            // gridColProblemRoad2G
            // 
            this.gridColProblemRoad2G.Caption = "2G问题路段数";
            this.gridColProblemRoad2G.FieldName = "ProblemRoadCount_2G";
            this.gridColProblemRoad2G.Name = "gridColProblemRoad2G";
            this.gridColProblemRoad2G.Visible = true;
            this.gridColProblemRoad2G.VisibleIndex = 3;
            this.gridColProblemRoad2G.Width = 251;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "问题路段数总计";
            this.gridColumn17.FieldName = "ProblemRoadCount_Total";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 4;
            this.gridColumn17.Width = 209;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.splitContainerControl2);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1001, 632);
            this.xtraTabPage2.Text = "问题路段详情";
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.gridControlRoad);
            this.splitContainerControl2.Panel1.Controls.Add(this.panelControl2);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.gridControlGrid);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(1001, 632);
            this.splitContainerControl2.SplitterPosition = 416;
            this.splitContainerControl2.TabIndex = 0;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // gridControlRoad
            // 
            this.gridControlRoad.ContextMenuStrip = this.contextMenuStripExport;
            this.gridControlRoad.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRoad.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlRoad.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlRoad.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlRoad.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlRoad.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlRoad.Location = new System.Drawing.Point(0, 183);
            this.gridControlRoad.MainView = this.gridViewRoad;
            this.gridControlRoad.Name = "gridControlRoad";
            this.gridControlRoad.Size = new System.Drawing.Size(1001, 233);
            this.gridControlRoad.TabIndex = 5;
            this.gridControlRoad.UseEmbeddedNavigator = true;
            this.gridControlRoad.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewRoad});
            this.gridControlRoad.DoubleClick += new System.EventHandler(this.gridControlRoad_DoubleClick);
            // 
            // gridViewRoad
            // 
            this.gridViewRoad.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn3,
            this.gridColumn2,
            this.gridColumn41,
            this.gridColumn4,
            this.gridColRoadLabelRsrp,
            this.gridColRoadLabelSinr,
            this.gridColRoadLabelLteCoverRate,
            this.gridColRoadLabelRxlev,
            this.gridColumn9,
            this.gridColumn1,
            this.gridColumn6,
            this.gridColumn5,
            this.gridColIsProblem,
            this.gridColumn29,
            this.gridColumn24});
            this.gridViewRoad.GridControl = this.gridControlRoad;
            this.gridViewRoad.Name = "gridViewRoad";
            this.gridViewRoad.OptionsBehavior.Editable = false;
            this.gridViewRoad.OptionsView.ShowDetailButtons = false;
            this.gridViewRoad.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "路段标签";
            this.gridColumn3.FieldName = "RoadLabel";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 0;
            this.gridColumn3.Width = 100;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "具体道路";
            this.gridColumn2.FieldName = "RoadName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 60;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "分区";
            this.gridColumn41.FieldName = "CountyName";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 2;
            this.gridColumn41.Width = 47;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "路段长度(公里)";
            this.gridColumn4.DisplayFormat.FormatString = "0.00";
            this.gridColumn4.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn4.FieldName = "RoadLabelLength_KM";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 57;
            // 
            // gridColRoadLabelRsrp
            // 
            this.gridColRoadLabelRsrp.Caption = "平均RSRP";
            this.gridColRoadLabelRsrp.DisplayFormat.FormatString = "0.00";
            this.gridColRoadLabelRsrp.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColRoadLabelRsrp.FieldName = "RsrpAvg";
            this.gridColRoadLabelRsrp.Name = "gridColRoadLabelRsrp";
            this.gridColRoadLabelRsrp.Visible = true;
            this.gridColRoadLabelRsrp.VisibleIndex = 4;
            this.gridColRoadLabelRsrp.Width = 57;
            // 
            // gridColRoadLabelSinr
            // 
            this.gridColRoadLabelSinr.Caption = "平均SINR";
            this.gridColRoadLabelSinr.DisplayFormat.FormatString = "0.00";
            this.gridColRoadLabelSinr.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColRoadLabelSinr.FieldName = "SinrAvg";
            this.gridColRoadLabelSinr.Name = "gridColRoadLabelSinr";
            this.gridColRoadLabelSinr.Visible = true;
            this.gridColRoadLabelSinr.VisibleIndex = 5;
            this.gridColRoadLabelSinr.Width = 57;
            // 
            // gridColRoadLabelLteCoverRate
            // 
            this.gridColRoadLabelLteCoverRate.Caption = "LTE综合覆盖率(%)";
            this.gridColRoadLabelLteCoverRate.DisplayFormat.FormatString = "0.00";
            this.gridColRoadLabelLteCoverRate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColRoadLabelLteCoverRate.FieldName = "LteCoverRate";
            this.gridColRoadLabelLteCoverRate.Name = "gridColRoadLabelLteCoverRate";
            this.gridColRoadLabelLteCoverRate.Visible = true;
            this.gridColRoadLabelLteCoverRate.VisibleIndex = 6;
            this.gridColRoadLabelLteCoverRate.Width = 76;
            // 
            // gridColRoadLabelRxlev
            // 
            this.gridColRoadLabelRxlev.Caption = "平均RxLev";
            this.gridColRoadLabelRxlev.DisplayFormat.FormatString = "0.00";
            this.gridColRoadLabelRxlev.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColRoadLabelRxlev.FieldName = "RxlevAvg";
            this.gridColRoadLabelRxlev.Name = "gridColRoadLabelRxlev";
            this.gridColRoadLabelRxlev.Visible = true;
            this.gridColRoadLabelRxlev.VisibleIndex = 7;
            this.gridColRoadLabelRxlev.Width = 57;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "影响因子(公里)";
            this.gridColumn9.DisplayFormat.FormatString = "0.00";
            this.gridColumn9.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn9.FieldName = "Affectoi_KM";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            this.gridColumn9.Width = 70;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "异常概率(%)";
            this.gridColumn1.DisplayFormat.FormatString = "0.00";
            this.gridColumn1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn1.FieldName = "RoadAbnormalPer";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 9;
            this.gridColumn1.Width = 70;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "模拟异常概率(%)";
            this.gridColumn6.DisplayFormat.FormatString = "0.00";
            this.gridColumn6.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn6.FieldName = "WeakRxlevOrRsrpGridPer";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 10;
            this.gridColumn6.Width = 70;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "测试异常概率(%)";
            this.gridColumn5.DisplayFormat.FormatString = "0.00";
            this.gridColumn5.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn5.FieldName = "TestAbnormalPer";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 11;
            this.gridColumn5.Width = 70;
            // 
            // gridColIsProblem
            // 
            this.gridColIsProblem.Caption = "是否为问题路段";
            this.gridColIsProblem.FieldName = "IsProblemRoadStr";
            this.gridColIsProblem.Name = "gridColIsProblem";
            this.gridColIsProblem.Visible = true;
            this.gridColIsProblem.VisibleIndex = 12;
            this.gridColIsProblem.Width = 59;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "问题网络类型";
            this.gridColumn29.FieldName = "NetTypeStr";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 13;
            this.gridColumn29.Width = 48;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "问题原因";
            this.gridColumn24.FieldName = "ProblemReason";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 14;
            this.gridColumn24.Width = 82;
            // 
            // panelControl2
            // 
            this.panelControl2.Controls.Add(this.groupBox2);
            this.panelControl2.Controls.Add(this.groupBox1);
            this.panelControl2.Controls.Add(this.labelControl5);
            this.panelControl2.Controls.Add(this.txtRoadMainDes);
            this.panelControl2.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl2.Location = new System.Drawing.Point(0, 0);
            this.panelControl2.Name = "panelControl2";
            this.panelControl2.Size = new System.Drawing.Size(1001, 183);
            this.panelControl2.TabIndex = 6;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.labelControl11);
            this.groupBox2.Controls.Add(this.chkDrawRoadLabel);
            this.groupBox2.Controls.Add(this.cmbGridColorMode);
            this.groupBox2.Controls.Add(this.btnEditColor);
            this.groupBox2.Location = new System.Drawing.Point(10, 65);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(981, 52);
            this.groupBox2.TabIndex = 41;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "地图渲染";
            // 
            // labelControl11
            // 
            this.labelControl11.Location = new System.Drawing.Point(6, 21);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(52, 14);
            this.labelControl11.TabIndex = 35;
            this.labelControl11.Text = "渲染指标:";
            // 
            // chkDrawRoadLabel
            // 
            this.chkDrawRoadLabel.EditValue = true;
            this.chkDrawRoadLabel.Location = new System.Drawing.Point(292, 22);
            this.chkDrawRoadLabel.Name = "chkDrawRoadLabel";
            this.chkDrawRoadLabel.Properties.Caption = "显示道路名";
            this.chkDrawRoadLabel.Size = new System.Drawing.Size(94, 19);
            this.chkDrawRoadLabel.TabIndex = 40;
            // 
            // cmbGridColorMode
            // 
            this.cmbGridColorMode.Location = new System.Drawing.Point(73, 20);
            this.cmbGridColorMode.Name = "cmbGridColorMode";
            this.cmbGridColorMode.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbGridColorMode.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cmbGridColorMode.Size = new System.Drawing.Size(130, 21);
            this.cmbGridColorMode.TabIndex = 34;
            // 
            // btnEditColor
            // 
            this.btnEditColor.Location = new System.Drawing.Point(209, 16);
            this.btnEditColor.Name = "btnEditColor";
            this.btnEditColor.Size = new System.Drawing.Size(50, 27);
            this.btnEditColor.TabIndex = 36;
            this.btnEditColor.Text = "编辑";
            this.btnEditColor.Click += new System.EventHandler(this.btnEditColor_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.chkOnlyShowProblemRoad);
            this.groupBox1.Controls.Add(this.timeExitCellBegin);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Controls.Add(this.txtRoadLabel);
            this.groupBox1.Controls.Add(this.btsSearch);
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Controls.Add(this.labelControl3);
            this.groupBox1.Controls.Add(this.timeExitCellEnd);
            this.groupBox1.Controls.Add(this.cmbMainLines);
            this.groupBox1.Controls.Add(this.labelControl4);
            this.groupBox1.Location = new System.Drawing.Point(10, 8);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(983, 51);
            this.groupBox1.TabIndex = 39;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "搜索条件";
            // 
            // chkOnlyShowProblemRoad
            // 
            this.chkOnlyShowProblemRoad.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chkOnlyShowProblemRoad.EditValue = true;
            this.chkOnlyShowProblemRoad.Location = new System.Drawing.Point(809, 21);
            this.chkOnlyShowProblemRoad.Name = "chkOnlyShowProblemRoad";
            this.chkOnlyShowProblemRoad.Properties.Caption = "只显示问题路段";
            this.chkOnlyShowProblemRoad.Size = new System.Drawing.Size(110, 19);
            this.chkOnlyShowProblemRoad.TabIndex = 41;
            this.chkOnlyShowProblemRoad.CheckedChanged += new System.EventHandler(this.chkOnlyShowProblemRoad_CheckedChanged);
            // 
            // timeExitCellBegin
            // 
            this.timeExitCellBegin.EditValue = new System.DateTime(2018, 8, 1, 0, 0, 0, 0);
            this.timeExitCellBegin.Location = new System.Drawing.Point(90, 21);
            this.timeExitCellBegin.Name = "timeExitCellBegin";
            this.timeExitCellBegin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.timeExitCellBegin.Properties.DisplayFormat.FormatString = "G";
            this.timeExitCellBegin.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeExitCellBegin.Properties.EditFormat.FormatString = "G";
            this.timeExitCellBegin.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeExitCellBegin.Properties.Mask.EditMask = "G";
            this.timeExitCellBegin.Properties.VistaDisplayMode = DevExpress.Utils.DefaultBoolean.True;
            this.timeExitCellBegin.Properties.VistaEditTime = DevExpress.Utils.DefaultBoolean.True;
            this.timeExitCellBegin.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.timeExitCellBegin.Size = new System.Drawing.Size(125, 21);
            this.timeExitCellBegin.TabIndex = 25;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(598, 24);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 14);
            this.labelControl1.TabIndex = 19;
            this.labelControl1.Text = "路段标签：";
            // 
            // txtRoadLabel
            // 
            this.txtRoadLabel.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtRoadLabel.Location = new System.Drawing.Point(664, 21);
            this.txtRoadLabel.Name = "txtRoadLabel";
            this.txtRoadLabel.Size = new System.Drawing.Size(135, 21);
            this.txtRoadLabel.TabIndex = 20;
            // 
            // btsSearch
            // 
            this.btsSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btsSearch.Location = new System.Drawing.Point(927, 18);
            this.btsSearch.Name = "btsSearch";
            this.btsSearch.Size = new System.Drawing.Size(50, 25);
            this.btsSearch.TabIndex = 21;
            this.btsSearch.Text = "搜索";
            this.btsSearch.Click += new System.EventHandler(this.btsSearch_Click);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(6, 24);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(84, 14);
            this.labelControl2.TabIndex = 23;
            this.labelControl2.Text = "退服信息时间：";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(223, 24);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(12, 14);
            this.labelControl3.TabIndex = 24;
            this.labelControl3.Text = "至";
            // 
            // timeExitCellEnd
            // 
            this.timeExitCellEnd.EditValue = new System.DateTime(2018, 8, 1, 0, 0, 0, 0);
            this.timeExitCellEnd.Location = new System.Drawing.Point(241, 21);
            this.timeExitCellEnd.Name = "timeExitCellEnd";
            this.timeExitCellEnd.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.timeExitCellEnd.Properties.DisplayFormat.FormatString = "G";
            this.timeExitCellEnd.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeExitCellEnd.Properties.EditFormat.FormatString = "G";
            this.timeExitCellEnd.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeExitCellEnd.Properties.Mask.EditMask = "G";
            this.timeExitCellEnd.Properties.VistaDisplayMode = DevExpress.Utils.DefaultBoolean.True;
            this.timeExitCellEnd.Properties.VistaEditTime = DevExpress.Utils.DefaultBoolean.True;
            this.timeExitCellEnd.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.timeExitCellEnd.Size = new System.Drawing.Size(125, 21);
            this.timeExitCellEnd.TabIndex = 26;
            // 
            // cmbMainLines
            // 
            this.cmbMainLines.Location = new System.Drawing.Point(451, 21);
            this.cmbMainLines.Name = "cmbMainLines";
            this.cmbMainLines.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbMainLines.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cmbMainLines.Size = new System.Drawing.Size(130, 21);
            this.cmbMainLines.TabIndex = 27;
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(385, 24);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(60, 14);
            this.labelControl4.TabIndex = 28;
            this.labelControl4.Text = "路线名称：";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(10, 140);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(60, 14);
            this.labelControl5.TabIndex = 30;
            this.labelControl5.Text = "道路概况：";
            // 
            // txtRoadMainDes
            // 
            this.txtRoadMainDes.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtRoadMainDes.Location = new System.Drawing.Point(76, 125);
            this.txtRoadMainDes.Name = "txtRoadMainDes";
            this.txtRoadMainDes.Size = new System.Drawing.Size(915, 46);
            this.txtRoadMainDes.TabIndex = 29;
            this.txtRoadMainDes.Text = "";
            // 
            // RoadQuaAnaInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1008, 662);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "RoadQuaAnaInfoForm";
            this.Text = "道路质量分析结果信息";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.RoadQuaAnaInfoForm_FormClosed);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGridCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGrid)).EndInit();
            this.contextMenuStripExport.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCountySum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCountySum)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRoad)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRoad)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            this.panelControl2.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkDrawRoadLabel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbGridColorMode.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkOnlyShowProblemRoad.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellBegin.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellBegin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRoadLabel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellEnd.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellEnd.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbMainLines.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripExport;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridControlSum;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewSum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.GridControl gridControlCountySum;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewCountySum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColProblemRoad24G;
        private DevExpress.XtraGrid.Columns.GridColumn gridColProblemRoad4G;
        private DevExpress.XtraGrid.Columns.GridColumn gridColProblemRoad2G;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraGrid.GridControl gridControlRoad;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewRoad;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColRoadLabelRsrp;
        private DevExpress.XtraGrid.Columns.GridColumn gridColRoadLabelLteCoverRate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.GridControl gridControlGrid;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGrid;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColGridRsrp;
        private DevExpress.XtraGrid.Columns.GridColumn gridColGridSinr;
        private DevExpress.XtraGrid.Columns.GridColumn gridColGridLteCoverRate;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraEditors.ComboBoxEdit cmbMainLines;
        private DevExpress.XtraEditors.DateEdit timeExitCellEnd;
        private DevExpress.XtraEditors.DateEdit timeExitCellBegin;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SimpleButton btsSearch;
        private DevExpress.XtraEditors.TextEdit txtRoadLabel;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private System.Windows.Forms.RichTextBox txtRoadMainDes;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGridCell;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColGridCellRsrp;
        private DevExpress.XtraGrid.Columns.GridColumn gridColGridCellSinr;
        private DevExpress.XtraGrid.Columns.GridColumn gridColGridCellLteCoverRate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColRoadLabelRxlev;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColGridCellRxlev;
        private DevExpress.XtraGrid.Columns.GridColumn gridColGridRxlev;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraEditors.SimpleButton btnEditColor;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.ComboBoxEdit cmbGridColorMode;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.CheckEdit chkDrawRoadLabel;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColRoadLabelSinr;
        private DevExpress.XtraEditors.CheckEdit chkOnlyShowProblemRoad;
        private DevExpress.XtraGrid.Columns.GridColumn gridColIsProblem;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpand;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemCollapse;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExportAllGrid;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;


    }
}