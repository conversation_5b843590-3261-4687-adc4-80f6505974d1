﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NoSignalCellForm : MinCloseForm
    {
        public NoSignalCellForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        public void FillData(Dictionary<ICell, NoSignalCell> noSignalCellDic)
        {
            List<NoSignalCell> list = new List<NoSignalCell>(noSignalCellDic.Values); 
            Boolean isLTECell = false;
            foreach (KeyValuePair<ICell, NoSignalCell> cell in noSignalCellDic)
            {
                if (cell.Key is LTECell)
                {
                    isLTECell = true;
                } 
            }
            if (isLTECell)
            {
                gridColumnLAC.Caption = "TAC";
                gridColumnCI.Caption = "ECI";
                gridColumnBCCH.Caption = "EARFCN";
                gridColumnBSIC.Caption = "PCI";
                gridColumnCellID.Visible = true;
                gridColumnCellID.VisibleIndex = 1;
            }
            else
            {
                gridColumnLAC.Caption = "LAC";
                gridColumnCI.Caption = "CI";
                gridColumnBCCH.Caption = "BCCH";
                gridColumnBSIC.Caption = "BSIC";
                gridColumnCellID.Visible = false;
                gridColumnCellID.VisibleIndex = -1;
            }
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
        }


        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                NoSignalCell noSignalCell = gridView.GetRow(gridView.GetSelectedRows()[0]) as NoSignalCell;
                MainModel.ClearDTData();
                foreach (TestPoint tp in noSignalCell.TestPointList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
                if (noSignalCell.cell is LTECell)
                {
                    MainModel.ServerLTECells.Clear();
                    MainModel.ServerLTECells.Add(noSignalCell.cell as LTECell);
                }
                else if (noSignalCell.cell is Cell)
                {
                    MainModel.ServerCells.Clear();
                    MainModel.ServerCells.Add(noSignalCell.cell as Cell);
                }
                MainModel.MainForm.GetMapForm().GetCellLayer().Invalidate();
             
                if (noSignalCell.cell is LTECell)
                {
                    //LTEBTS bts = ((LTECell)noSignalCell.cell).LastBelongBTS;
                    //BTSAnalogCoverGroup group = MainModel.BTSToGroupDic[bts];
                    //MainModel.BTSAnalogCoverShowDic[group] = MainModel.BTSAnalogCoverGroupDic[group];
                    MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_PSS_RP");
                }
                else if (noSignalCell.cell is Cell)
                {
                    //BTS bts = ((Cell)noSignalCell.cell).LastBelongBTS;
                    //BTSAnalogCoverGroup group = MainModel.BTSToGroupDic[bts];
                    //MainModel.BTSAnalogCoverShowDic[group] = MainModel.BTSAnalogCoverGroupDic[group];
                }
            }
        }

       
    }
}
