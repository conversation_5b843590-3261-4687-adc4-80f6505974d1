﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FartherCoverNRSettingDlg : Form
    {
        private FartherCoverConditionNR cond = null;

        public FartherCoverNRSettingDlg(FartherCoverConditionNR condition)
        {
            InitializeComponent();
            this.cond = condition;
            if (this.cond != null)
            {
                fillCondition();
            }
        }

        private void fillCondition()
        {
            spinEditRSRP.Value = cond.RsrpThreshold;
            spinEditSampleNum.Value = cond.SampleNum;
            spinEditDistanceMin.Value = cond.DistanceMin;
            spinEditDistanceMax.Value = cond.DistanceMax;
        }

        public FartherCoverConditionNR GetCondition()
        {
            if (cond == null)
            {
                cond = new FartherCoverConditionNR();
            }
            cond.ResetValue((int)spinEditRSRP.Value, (int)spinEditSampleNum.Value, (int)spinEditDistanceMin.Value,
                (int)spinEditDistanceMax.Value);
            return cond;
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class FartherCoverConditionNR : FartherCoverCondition
    {
        public void ResetValue(int rsrp, int sampleNum, int distanceMin, int distanceMax)
        {
            this.RsrpThreshold = rsrp;
            this.SampleNum = sampleNum;
            this.DistanceMin = distanceMin;
            this.DistanceMax = distanceMax;
        }
    }
}
