﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTNearestSiteByExcelSettingForm : BaseDialog
    {
        public ZTNearestSiteByExcelSettingForm()
        {
            InitializeComponent();
        }
        public string FilePathBase
        {
            get { return textBoxFilePathBase.Text; }
        }
        public string FilePathOther
        {
            get { return textBoxFilePathOther.Text; }
        }
        public int Distance
        {
            get { return (int)spinEditDistance.Value; }
        }
   
        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
        public void SetFilePathBase(string filePathBase)
        {
            if (filePathBase != null)
            {
                textBoxFilePathBase.Text = filePathBase;
            }
        }
        public void SetFilePathOther(string filePathOther)
        {
            if (filePathOther != null)
            {
                textBoxFilePathOther.Text = filePathOther;
            }
        }

        private void simpleBtnSelectBase_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Multiselect = false;
            dlg.Filter = FilterHelper.Excel;
            if (dlg.ShowDialog() != DialogResult.OK) return;
            textBoxFilePathBase.Text = dlg.FileName;
        }

        private void simpleBtnSelectOther_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Multiselect = false;
            dlg.Filter = FilterHelper.Excel;
            if (dlg.ShowDialog() != DialogResult.OK) return;
            textBoxFilePathOther.Text = dlg.FileName;
        }

        private void simpleBtnSearch_Click(object sender, EventArgs e)
        {
            if (textBoxFilePathBase.Text.Trim().Equals("")||textBoxFilePathOther.Text.Trim().Equals(""))
                return;
            this.DialogResult = DialogResult.OK;
        }

        private void panelColorOther_Click(object sender, EventArgs e)
        {
            ColorDialog dlg = new ColorDialog();
            dlg.AllowFullOpen = false;
            dlg.FullOpen = false;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            panelColorOther.BackColor = dlg.Color;
            dlg.Dispose();
        }

        private void panelColorBase_Click(object sender, EventArgs e)
        {
            ColorDialog dlg = new ColorDialog();
            dlg.AllowFullOpen = false;
            dlg.FullOpen = false;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            panelColorBase.BackColor = dlg.Color;
            dlg.Dispose();
        }
        public Color GetBaseColor()
        {
            return panelColorBase.BackColor;
        }
        public Color GetOtherColor()
        {
            return panelColorOther.BackColor;
        }
    }
}
