﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;

using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsLayer : CustomDrawLayer
    {
        public static List<LteMgrsDrawItem> DrawList { get; set; }

        public static LteMgrsLegendGroup LegendGroup { get; set; }

        public static DbRect GetDrawBound(List<LteMgrsDrawItem> drawList)
        {
            if (drawList == null || drawList.Count == 0)
            {
                return null;
            }

            double topRightLng = drawList[0].TopRight.x;
            double topRightLat = drawList[0].TopRight.y;
            double bottomLeftLng = drawList[0].BottomLeft.x;
            double bottomLeftLat = drawList[0].BottomLeft.y;
            for (int i = 1; i < drawList.Count; ++i)
            {
                topRightLng = Math.Max(drawList[i].TopRight.x, topRightLng);
                topRightLat = Math.Max(drawList[i].TopRight.y, topRightLat);
                bottomLeftLng = Math.Min(drawList[i].BottomLeft.x, bottomLeftLng);
                bottomLeftLat = Math.Min(drawList[i].BottomLeft.y, bottomLeftLat);
            }
            return new DbRect(new DbPoint(bottomLeftLng, bottomLeftLat), new DbPoint(topRightLng, topRightLat));
        }

        public LteMgrsLayer(MapOperation oper, String name) : base(oper, name)
        {
            MainModel.MainForm.GetMapForm().ToolInfoClickEvent += ToolInfo_Clicked;
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || DrawList == null || DrawList.Count == 0)
            {
                return;
            }

            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            foreach (LteMgrsDrawItem item in DrawList)
            {
                if (!item.Rect.Within(dRect))
                {
                    continue;
                }

                Color color = item.FillColor;
                if (color == Color.Empty)
                {
                    continue;
                }
                DrawItem(item, color, graphics);
            }
        }

        private void DrawItem(LteMgrsDrawItem item, Color color, Graphics graphics)
        {
            PointF bottomLeft;
            this.Map.ToDisplay(item.BottomLeft, out bottomLeft);
            PointF topRight;
            this.Map.ToDisplay(item.TopRight, out topRight);
            Brush brush = new SolidBrush(color);
            graphics.FillRectangle(brush, bottomLeft.X, topRight.Y, topRight.X - bottomLeft.X, bottomLeft.Y - topRight.Y);
            brush.Dispose();
            if (SelGrid != null && SelGrid == item.GridCellInfo)
            {
                graphics.DrawRectangle(Pens.Red, bottomLeft.X, topRight.Y, topRight.X - bottomLeft.X, bottomLeft.Y - topRight.Y);

                foreach (LteMgrsCell cell in SelGrid.Cells)
                {
                    if (cell.Cell==null)
                    {
                        continue;
                    }
                    PointF cellPt;
                    this.Map.ToDisplay(new DbPoint(cell.Cell.EndPointLongitude, cell.Cell.EndPointLatitude), out cellPt);
                    graphics.DrawLine(Pens.Red, bottomLeft.X + (topRight.X - bottomLeft.X) / 2.0f
                        , bottomLeft.Y + (topRight.Y - bottomLeft.Y) / 2.0f, cellPt.X, cellPt.Y);
                }
            }
        }

        private void ToolInfo_Clicked(double lng, double lat, MapOperation2 mop2, ref List<string> titles, ref List<string> infos)
        {
            if (DrawList == null || DrawList.Count == 0)
            {
                return;
            }

            foreach (LteMgrsDrawItem item in DrawList)
            {
                if (item.Rect.IsPointInThisRect(lng, lat) && item.ToolInfoTitle != null && item.ToolInfoDetail != null)
                {
                    titles.Add(item.ToolInfoTitle);
                    infos.Add(item.ToolInfoDetail);
                }
            }
        }

        public static LteMgrsWeakRsrpGrid SelGrid { get; set; }
    }

    public class LteMgrsDrawItem
    {
        public LteMgrsDrawItem(DbPoint bottomLeft, DbPoint topRight)
        {
            BottomLeft = bottomLeft;
            TopRight = topRight;
            FillColor = Color.Empty;
            Rect = new DbRect(bottomLeft, topRight);
        }

        public DbPoint BottomLeft
        {
            get;
            private set;
        }

        public DbPoint TopRight
        {
            get;
            private set;
        }

        public DbRect Rect
        {
            get;
            private set;
        }

        public Color FillColor
        {
            get;
            set;
        }

        public string ToolInfoTitle
        {
            get;
            set;
        }

        public string ToolInfoDetail
        {
            get;
            set;
        }

        public LteMgrsWeakRsrpGrid GridCellInfo
        {
            get;
            set;
        }
    }

    public class LteMgrsLegendGroup : ILegend
    {
        public LteMgrsLegendGroup(string title)
        {
            Title = title;
        }

        public string Title
        {
            get;
            private set;
        }

        public bool Visible
        {
            get;
            set;
        }

        public void DrawOnListBox(System.Windows.Forms.ListBox listBox, System.Windows.Forms.DrawItemEventArgs e)
        {
            string text = Title;
            e.Graphics.DrawString(text, listBox.Font, Brushes.Black, e.Bounds.X, e.Bounds.Y);
        }

        public List<LteMgrsLegendItem> SubItems { get; set; } = new List<LteMgrsLegendItem>();
    }

    public class LteMgrsLegendItem : ILegend
    {
        public LteMgrsLegendItem(Color fillColor, string desc)
        {
            FillColor = fillColor;
            Desc = desc;
        }

        public Color FillColor
        {
            get;
            private set;
        }

        public string Desc
        {
            get;
            private set;
        }

        public bool Visible
        {
            get;
            set;
        }

        public void DrawOnListBox(System.Windows.Forms.ListBox listBox, System.Windows.Forms.DrawItemEventArgs e)
        {
            e.Graphics.FillRectangle(new SolidBrush(FillColor), e.Bounds.X + 25, e.Bounds.Y, 16, 16);
            e.Graphics.DrawString(Desc, listBox.Font, Brushes.Black, e.Bounds.X + 45, e.Bounds.Y);
        }
    }
}
