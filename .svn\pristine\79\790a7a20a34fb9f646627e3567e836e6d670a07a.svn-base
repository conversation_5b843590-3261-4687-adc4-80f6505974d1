﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;

using MasterCom.Util;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.Util.UiEx;
using GridDataHub = MasterCom.RAMS.ZTFunc.KpiGridRenderByRegion.GridDataHub;

using MapWinGIS;

namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    public class KpiRoadRenderLayer : LayerBase
    {
        public KpiRoadRenderLayer()
            : base("区域内道路")
        {
            //mainModel.MainForm.GetMapForm().ToolInfoClickEvent += InfoTool_Click;
        }

        public void SetMatrixAndColor(GridMatrix<GridDataHub> gridMatrix, List<KpiRoadRenderShpFile> shpFiles,
            GridColorModeItem colorMode)
        {
            this.gridMatrix = gridMatrix;
            this.colorMode = colorMode;
            this.shpFiles = shpFiles;

            CalcGridFormularValue();
            LoadRoadPoints();
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            Bitmap gridMap = DrawGridBitMap(clientRect, graphics);
            Bitmap roadMap = DrawRoadBitMap(clientRect, graphics);
            CopyColorFromGridToRoad(clientRect, gridMap, roadMap);
            graphics.DrawImage(roadMap, clientRect);
            gridMap.Dispose();
        }

        private Bitmap DrawGridBitMap(Rectangle clientRect, Graphics graphics)
        {
            Bitmap gridMap = new Bitmap(clientRect.Width, clientRect.Height, graphics);
            Graphics gridGrap = Graphics.FromImage(gridMap);
            gridGrap.CompositingMode = CompositingMode.SourceCopy;
            gridGrap.Clear(Color.FromArgb(0));

            SolidBrush solidBrush = new SolidBrush(Color.Empty);
            foreach (GridDataHub gdh in gridMatrix)
            {
                if (gdh.Tag == null)
                {
                    continue;
                }
                Color color = colorMode.GetColor((float)gdh.Tag);
                if (color == Color.Empty)
                {
                    continue;
                }

                DbPoint topLeft = new DbPoint(gdh.LTLng, gdh.LTLat);
                DbPoint bottomRight = new DbPoint(gdh.BRLng, gdh.BRLat);
                PointF pfTopLeft, pfBottomRight;
                gisAdapter.ToDisplay(topLeft, out pfTopLeft);
                gisAdapter.ToDisplay(bottomRight, out pfBottomRight);

                solidBrush.Color = color;
                gridGrap.FillRectangle(solidBrush, pfTopLeft.X, pfTopLeft.Y,
                    pfBottomRight.X - pfTopLeft.X, pfBottomRight.Y - pfTopLeft.Y);
            }

            gridGrap.Dispose();
            return gridMap;
        }

        private Bitmap DrawRoadBitMap(Rectangle clientRect, Graphics graphics)
        {
            Bitmap roadMap = new Bitmap(clientRect.Width, clientRect.Height, graphics);
            Graphics roadGrap = Graphics.FromImage(roadMap);
            roadGrap.CompositingMode = CompositingMode.SourceCopy;
            roadGrap.Clear(Color.FromArgb(0));

            Pen pen = new Pen(Color.Gainsboro, 3.0f);
            foreach (DbPoint[] road in roadList)
            {
                PointF[] points = null;
                gisAdapter.ToDisplay(road, out points);
                roadGrap.DrawLines(pen, points);
            }

            roadGrap.Dispose();
            return roadMap;
        }

        private void CopyColorFromGridToRoad(Rectangle clientRect, Bitmap gridMap, Bitmap roadMap)
        {
            BitmapData gridData = gridMap.LockBits(clientRect, ImageLockMode.ReadOnly, gridMap.PixelFormat);
            BitmapData roadData = roadMap.LockBits(clientRect, ImageLockMode.ReadWrite, roadMap.PixelFormat);

            int rowCnt = gridData.Height; // = roadData.Height
            int colCnt = gridData.Width; // = roadData.Width;
            unsafe
            {
                int* gridPtr = (int*)gridData.Scan0;
                int* roadPtr = (int*)roadData.Scan0;
                for (int row = 0; row < rowCnt; ++row)
                {
                    for (int col = 0; col < colCnt; ++col)
                    {
                        int idx = row * colCnt + col;
                        if (roadPtr[idx] == 0 || gridPtr[idx] == 0)
                        {
                            continue;
                        }
                        roadPtr[idx] = gridPtr[idx];
                    }
                }
            }

            gridMap.UnlockBits(gridData);
            roadMap.UnlockBits(roadData);
        }

        private void CalcGridFormularValue()
        {
            foreach (GridDataHub gdh in gridMatrix)
            {
                double value = GetGridValue(gdh);
                gdh.Tag = (float)value;
            }
        }

        private void LoadRoadPoints()
        {
            roadList = new List<DbPoint[]>();
            foreach (KpiRoadRenderShpFile shpFileItem in shpFiles)
            {
                Shapefile shpFile = new Shapefile();
                shpFile.Open(shpFileItem.FilePath, null);
                int numberShapes = shpFile.NumShapes;

                for (int i = 0; i < numberShapes; ++i)
                {
                    Shape shape = shpFile.get_Shape(i);
                    int numberParts = shape.NumParts;
                    for (int j = 0; j < numberParts; ++j)
                    {
                        List<DbPoint> points = ShapeHelper.GetPartShapePoints(shape, j);
                        roadList.Add(points.ToArray());
                    }
                }

                shpFile.Close();
            }
        }

        private double GetGridValue(GridDataHub gdh)
        {
            double value = gdh.DataHub.CalcFormula(Model.CarrierType.ChinaMobile, -1, this.colorMode.formula);
            if (double.IsNaN(value))
            {
                value = gdh.DataHub.CalcFormula(Model.CarrierType.ChinaMobile, 1, this.colorMode.formula);
                if (double.IsNaN(value))
                {
                    value = gdh.DataHub.CalcFormula(Model.CarrierType.ChinaMobile, 2, this.colorMode.formula);
                }
            }
            return value;
        }

        private GridMatrix<GridDataHub> gridMatrix;
        private GridColorModeItem colorMode;
        private List<KpiRoadRenderShpFile> shpFiles;
        private List<DbPoint[]> roadList;
    }
}
