﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.NOP
{
    class DealTimeStatAnalyzer
    {
        public List<DealTimeResult> TaskResults = null;

        public DealTimeStatAnalyzer()
        {
            TaskResults = new List<DealTimeResult>();
            init();
        }

        public List<DealTimeResult> GetTaskResults()
        {
            int[] counts = {0,0,0,0,0,0,0,0,0}; 
            DealTimeResult AvaResult = new DealTimeResult("平均统计");
            DealTimeResult TotalResult = new DealTimeResult("总计统计");
            foreach (DealTimeResult result in TaskResults)
            {
                for (int i = 0; i < 9; i++)
                {
                    if (result.Counts[i] > 0)
                    {
                        TotalResult.Counts[i] += result.Counts[i];
                        TotalResult.Times[i] += result.Times[i];

                        AvaResult.Counts[i] += result.Counts[i];
                        AvaResult.Times[i] += result.Times[i];

                        counts[i]++;
                        break;
                    }
                }
            }

            for (int i = 0; i < 9; i++)
            {
                if (counts[i] == 0)
                {
                    AvaResult.Times[i] = 0;
                    AvaResult.Counts[i] = 0;
                }
                else
                {
                    AvaResult.Times[i] = AvaResult.Times[i] / counts[i];
                    AvaResult.Counts[i] = AvaResult.Counts[i] / counts[i];
                }
            }
            TaskResults.Add(AvaResult);
            TaskResults.Add(TotalResult);
            return TaskResults;
        }

        public void ClearResult()
        {
            TaskResults = new List<DealTimeResult>();
        }

        public void Analyze(Dictionary<int, List<GroupStatModel>> models)
        {
            if (models == null)
            {
                return;
            }
            foreach (var item in models)
            {
                if (item.Value.Count < 1)
                {
                    continue;
                }

                dealGroupStatModel(item);
            }
        }

        private void dealGroupStatModel(KeyValuePair<int, List<GroupStatModel>> item)
        {
            DateTime beforTime = Convert.ToDateTime(item.Value[0].OrderTime);
            string beforGroupName = "";
            Dictionary<string, DealTimeResult> resultDic = new Dictionary<string, DealTimeResult>();

            foreach (GroupStatModel model in item.Value)
            {
                int groupIndex = getGroupIndex(model.GroupName, model.Description);
                if (groupIndex != -1 || validDescription(model.Description))
                {
                    addTaskResults(ref beforTime, ref beforGroupName, resultDic, model, groupIndex, false);
                }
            }
        }

        private void addTaskResults(ref DateTime beforTime, ref string beforGroupName, Dictionary<string, DealTimeResult> resultDic, GroupStatModel model, int groupIndex, bool judgeEqual)
        {
            DealTimeResult taskResult = new DealTimeResult(model.TaskID, model.District, model.TaskName);
            if (!resultDic.ContainsKey(model.GroupName))
            {
                resultDic[model.GroupName] = taskResult;
            }
            taskResult.GroupName = model.GroupName;
            taskResult.DealTime = model.OrderTime;
            taskResult.Status = getStatus(model.Description);

            DateTime time = Convert.ToDateTime(model.OrderTime);
            if (groupIndex >= 0)
            {
                TimeSpan span = (time - beforTime);
                taskResult.Counts[groupIndex]++;
                taskResult.Times[groupIndex] += Math.Round((double)(span.Days * 24) + (double)span.Hours + (double)span.Minutes / 60, 2);
            }

            if (resultDic.Count > 1)
            {
                TaskResults[TaskResults.Count - 1].NextGroupName = model.GroupName;
                taskResult.OrderTime = beforTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else if (resultDic.Count == 1 && judgeEqual)
            {
                taskResult.OrderTime = beforTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
            taskResult.LastGroupName = beforGroupName;

            beforTime = time;
            beforGroupName = model.GroupName;

            TaskResults.Add(taskResult);
        }

        public void Analyze(Dictionary<int, List<GroupStatModel>> models, DateTime beginTime, DateTime endTime)
        {
            if (models == null)
            {
                return;
            }
            foreach (var item in models)
            {
                if (item.Value.Count < 1)
                {
                    continue;
                }

                string nextGroupName = dealGroupStatModel(beginTime, endTime, item);
                if (!string.IsNullOrEmpty(nextGroupName))
                {
                    TaskResults[TaskResults.Count - 1].NextGroupName = nextGroupName;
                }
            }
        }

        private string dealGroupStatModel(DateTime beginTime, DateTime endTime, KeyValuePair<int, List<GroupStatModel>> item)
        {
            DateTime beforTime = Convert.ToDateTime(item.Value[0].OrderTime);
            string beforGroupName = "";
            string nextGroupName = "";
            Dictionary<string, DealTimeResult> resultDic = new Dictionary<string, DealTimeResult>();

            foreach (GroupStatModel model in item.Value)
            {
                DateTime oTime = Convert.ToDateTime(model.OrderTime);
                if (oTime < beginTime)
                {
                    beforTime = oTime;
                    beforGroupName = model.GroupName;
                }
                else
                {
                    if (oTime > endTime)
                    {
                        nextGroupName = model.GroupName;
                        TaskResults[TaskResults.Count - 1].NextGroupName = model.GroupName;
                        break;
                    }
                    int groupIndex = getGroupIndex(model.GroupName, model.Description);
                    if (groupIndex != -1 || validDescription(model.Description))
                    {
                        addTaskResults(ref beforTime, ref beforGroupName, resultDic, model, groupIndex, true);
                    }
                }
            }

            return nextGroupName;
        }

        private string getStatus(string str)
        {
            string status = "";

            if (str.Contains("为[预处理]") || str.Contains("创建了工单"))
            {
                return "预处理";
            }
            else if (str.Contains("为[已派单]"))
            {
                return "已派单";
            }
            else if (str.Contains("为[已接单]"))
            {
                return "已接单";
            }
            else if (str.Contains("为[已回单]"))
            {
                return "已回单";
            }
            else if (str.Contains("为[预关闭]"))
            {
                return "预关闭";
            }
            else if (str.Contains("为[工程督办]"))
            {
                return "工程督办";
            }
            else if (str.Contains("问题点没有关闭") || str.Contains("验证失败"))
            {
                return "验证失败";
            }
            else if (str.Contains("验证成功"))
            {
                return "验证成功";
            }
           
            return status;
        }
        private int getGroupIndex(string str,string status)
        {
            int index = -1;
            if (status.Contains("[工程督办]变为[已回单]"))
            {
                return 8;
            }

            if (groupDic.ContainsKey(str))
            {
                index = groupDic[str];
            }
            else if (validDistrict(str) >= 0)
            {
                index = 7;
            }
            return index;
        }

        private bool validDescription(string Description)
        {
            bool value = false;
            if (Description.Contains("创建了工单"))
            {
                value = true;
            }
            if (Description.Contains("问题点没有关闭"))
            {
                value = true;
            }
            if (Description.Contains("问题点已关闭"))
            {
                value = true;
            }
            return value;
        }
        private int validDistrict(string str)
        {
            int index = -1;
            if (districtDic.ContainsKey(str))
            {
                index = districtDic[str];
            }
            return index;
        }

        private void init()
        {
            initGroup();
            initDistrict();
        }

        Dictionary<string, int> groupDic;
        private void initGroup()
        {
            groupDic = new Dictionary<string, int>();
            groupDic.Add("T1", 0);
            groupDic.Add("测试数据管理", 0);
            groupDic.Add("T2_1", 1);
            groupDic.Add("片区优化组", 1);
            groupDic.Add("T2_2", 2);
            groupDic.Add("参数维护组", 2);
            groupDic.Add("T2_3", 3);
            groupDic.Add("室分维护组", 3);
            groupDic.Add("T2_4", 4);
            groupDic.Add("无线规划组", 4);
            groupDic.Add("T2_5", 5);
            groupDic.Add("干线优化组", 5);
            groupDic.Add("T2_6", 6);
            groupDic.Add("疑难问题处理组", 6);
        }

        Dictionary<string, int> districtDic;
        private void initDistrict()
        {
            districtDic = new Dictionary<string, int>();
            districtDic.Add("ezhou", 0);
            districtDic.Add("鄂州", 0);
            districtDic.Add("enshi", 1);
            districtDic.Add("恩施", 1);
            districtDic.Add("huangshi", 2);
            districtDic.Add("黄石", 2);
            districtDic.Add("huanggang", 3);
            districtDic.Add("黄冈", 3);
            districtDic.Add("jingmen", 4);
            districtDic.Add("荆门", 4);
            districtDic.Add("jingzhou", 5);
            districtDic.Add("荆州", 5);
            districtDic.Add("jianghan", 6);
            districtDic.Add("江汉", 6);
            districtDic.Add("tianmen", 7);
            districtDic.Add("天门", 7);
            districtDic.Add("qianjiang", 8);
            districtDic.Add("潜江", 8);
            districtDic.Add("shiyan", 9);
            districtDic.Add("十堰", 9);
            districtDic.Add("suizhou", 10);
            districtDic.Add("随州", 10);
            districtDic.Add("wuhan", 11);
            districtDic.Add("武汉", 11);
            districtDic.Add("xiangyang", 12);
            districtDic.Add("襄阳", 12);
            districtDic.Add("xianning", 13);
            districtDic.Add("咸宁", 13);
            districtDic.Add("xiaogan", 14);
            districtDic.Add("孝感", 14);
            districtDic.Add("yichang", 15);
            districtDic.Add("宜昌", 15);
        }
    }
}
