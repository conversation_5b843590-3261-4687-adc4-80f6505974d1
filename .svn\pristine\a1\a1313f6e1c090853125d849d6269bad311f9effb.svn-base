﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class MoBlockCallSettingForm : BaseDialog
    {
        public MoBlockCallSettingForm()
        {
            InitializeComponent();
        }

        public void GetCondition(out MoBlockCallCondition condition)
        {
            condition = new MoBlockCallCondition();
            condition.AfterTime = (int)numAfterTime.Value;
        }
    }
}
