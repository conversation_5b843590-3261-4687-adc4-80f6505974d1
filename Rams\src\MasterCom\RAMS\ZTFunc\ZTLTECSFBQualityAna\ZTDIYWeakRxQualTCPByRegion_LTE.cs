﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYWeakRxQualTCPByRegion_LTE : ZTDIYWeakRxQualTCPByRegion
    {
        public ZTDIYWeakRxQualTCPByRegion_LTE(MainModel mainModel)
            :base(mainModel)
        {
        }
        public override string Name
        {
            get { return "质差频点查询(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override byte? get_RxQualSub(TestPoint tp)
        {
            return (byte?)tp["lte_gsm_DM_RxQualSub"];
        }

        protected override WeakRxQualTCPDlg getConditionInstance()
        {
            WeakRxQualTCPDlg conditionDlg = new WeakRxQualTCPDlg();
            conditionDlg.InitForLTE();
            return conditionDlg;
        }
        protected override WeakRxQualFreq getFreqInstance(TestPoint tp)
        {
            return new WeakRxQualFreq_LTE(tp);
        }
        protected override byte? get_HP(TestPoint tp)
        {
            return null;
        }

        private bool isValidTestPoint_base(TestPoint tp)
        {
            try
            {
                return Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
            }
            catch
            {
                try
                {
                    return Condition.Geometorys.GeoOp2.CheckPointInRegion(tp.Longitude, tp.Latitude);//网络体检后台
                }
                catch
                {
                    //continue
                }
                return false;
            }

        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                if (!(tp is LTETestPointDetail))
                {
                    return false;
                }
                byte? rxQual = this.get_RxQualSub(tp);
                if (rxQual == null || rxQual < rxQualThreshold || rxQual > 7)
                {
                    return false;
                }
                return this.isValidTestPoint_base(tp);
            }
            catch
            {
                return false;
            }
        }
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_DR_List_of_TCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_DM_RxQualSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_DR_TCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_DM_RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 50; ++i)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "lte_gsm_NC_RxLev";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_gsm_NC_BCCH";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_gsm_NC_BSIC";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_gsm_DR_List_of_TCH";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"质差频点");
            tmpDic.Add("themeName", (object)"GSM RxQual");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
    }

    public class WeakRxQualFreq_LTE : WeakRxQualFreq
    {
        public WeakRxQualFreq_LTE(TestPoint tp)
            :base(tp)
        {
        }

        public override void GetResult(int c2iRxLevThreshold, int c2iThreshold)
        {
            for (int i = 0; i < 50; i++)
            {
                string str_c2i = "";
                short? c2i_RxLev = (short?)tp["lte_gsm_DM_RxLevSub", i];
                if (c2i_RxLev == null || c2i_RxLev < c2iRxLevThreshold)
                {
                    continue;
                } 
                FreqInfo freqInfo = new FreqInfo();
                freqInfo.C2I_ARFCN = str_c2i;
                freqInfo.C2I_RxLev = (float)c2i_RxLev;
                freqInfo.C2I = (float)0;
                freqInfoList.Add(freqInfo);
            }
        }

        protected override short? get_BCCH(TestPoint tp)
        {
            return (short?)tp["lte_gsm_SC_BCCH"];
        }
        protected override byte? get_BSIC(TestPoint tp)
        {
            return (byte?)tp["lte_gsm_SC_BSIC"];
        }
        protected override int? get_CI(TestPoint tp)
        {
            return (int?)tp["lte_gsm_SC_CI"];
        }
        protected override int? get_LAC(TestPoint tp)
        {
            return (int?)tp["lte_gsm_SC_LAC"];
        }
        protected override void get_N_Param(TestPoint tp, out short? n_RxLev, out short? n_BCCH, out byte? n_BSIC, int index)
        {
            n_RxLev = (short?)tp["lte_gsm_NC_RxLev", index];
            n_BCCH = (short?)tp["lte_gsm_NC_BCCH", index];
            n_BSIC = (byte?)tp["lte_gsm_NC_BSIC", index];
        }
        protected override short? get_RxLevSub(TestPoint tp)
        {
            return (short?)tp["lte_gsm_DM_RxLevSub"];
        }
        protected override int? get_TCH(TestPoint tp, int index)
        {
            return (int?)tp["lte_gsm_DR_List_of_TCH", index];
        }
        public override string C2I
        {
            get
            {
                return "-";
            }
        }
        public override string C2IARFCN
        {
            get
            {
                return "-";
            }
        }
        public override string C2IRxLev
        {
            get
            {
                return "-";
            }
        }
        public override string HP
        {
            get
            {
                return "-";
            }
        }
        public override string RxLev
        {
            get
            {
                return ((float)(short?)tp["lte_gsm_DM_RxLevSub"]).ToString();
            }
        }
        public override string RxQual
        {
            get { return ((byte)tp["lte_gsm_DM_RxQualSub"]).ToString(); }
        }
        public override string TCH
        {
            get
            {
                object obj = tp["lte_gsm_DR_TCH"];
                return (obj != null && (float)(short)obj != -255) ? obj.ToString() : "";
            }
        }

    }
}
