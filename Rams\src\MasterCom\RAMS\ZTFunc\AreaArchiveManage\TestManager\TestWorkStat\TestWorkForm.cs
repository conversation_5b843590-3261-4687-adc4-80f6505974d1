﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using DevExpress.XtraCharts;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class TestWorkForm : MinCloseForm
    {
        public TestWorkForm()
        {
            InitializeComponent();

            areaResultPanel.LogItemSrc = this.LogItemSrc;
            areaResultPanel.MakeChartPageVisible(true);
        }

        public void FillData(AnaDealerBase anaDealer, Dictionary<AreaBase, CAreaSummary> areaSummaryMap)
        {
            areaResultPanel.Init(anaDealer);
            areaResultPanel.FillData(areaSummaryMap);
        }
    }

    public class CtpCarBind
    {
        public TimePeriod Time { get; set; }
        public ECarrier Car { get; set; }

        public CtpCarBind(TimePeriod tp, Enum car)
        {
            this.Time = tp;
            this.Car = (ECarrier)car;
        }

        public override string ToString()
        {
            return string.Format("{0}，{1}", Car, Time);
        }
    }
}
