﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptAutoSet
    {
        public StationAcceptAutoSet()
        {
            AutoAnaDurationMinutes = 10;
            AutoWorkParamRecentDays = 2;
            OutDoorCallCheck = new CallKpiCheck();
            InDoorCallCheck = new CallKpiCheck();
        }

        public int DiyStatsRecentDays { get; set; } = 2;

        /// <summary>
        /// 工参选择从excel导入时的地址
        /// </summary>
        public string ExcelPath { get; set; }
        public bool IsAutoWorkParamFilePath { get; set; }
        public int AutoWorkParamRecentDays { get; set; }
        
        /// <summary>
        /// 文件名中ENodeBID进制： 0位10进制，1位16进制
        /// </summary>
        public int FileNameEnodType { get; set; }
        
        /// <summary>
        /// 报告输出目录
        /// </summary>
        public string FilePath { get; set; } = "C:\\Users\\<USER>\\Desktop";

        public int AutoAnaDurationMinutes { get; set; }
        public bool FileCountAutoIsCheck { get; set; }
        public string FileCountAutoULFolder { get; set; }
        public CallKpiCheck OutDoorCallCheck { get; set; }
        public CallKpiCheck InDoorCallCheck { get; set; }

    }
    public class CallKpiCheck
    {
        public CallKpiCheck()
        {
            IsCheckHandOver = true;
        }

        /// <summary>
        /// 室分站外泄受控比例
        /// </summary>
        public bool IsCheckHandOver { get; set; }
        public bool IsCheckLeakOutRatio { get; set; }
        public bool IsCheckOutDoorOtherSet { get; set; }
        public bool IsCheckInAndOutSrc { get; set; }
        public bool IsCheckCsfb { get; set; }

        /// <summary>
        /// VOLTE语音全程呼叫成功率
        /// </summary>
        public bool IsCheckVolteVoiceAllCall { get; set; }

        /// <summary>
        /// VoLTE语音业务接通率
        /// </summary>
        public bool IsCheckVolteVoiceMoCall { get; set; }

        /// <summary>
        /// Volte视频全程成功率
        /// </summary>
        public bool IsCheckVolteVideoAllCall { get; set; }
        /// <summary>
        /// VoLTE视频业务接通率
        /// </summary>
        public bool IsCheckVolteVideoMoCall { get; set; }
        public bool IsCheckSRVCCCall { get; set; }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["IsCheckHandOver"] = this.IsCheckHandOver;
                param["IsCheckCsfb"] = this.IsCheckCsfb;
                param["IsCheckVolteVoiceAllCall"] = this.IsCheckVolteVoiceAllCall;
                param["IsCheckVolteVoiceMoCall"] = this.IsCheckVolteVoiceMoCall;
                param["IsCheckVolteVideoAllCall"] = this.IsCheckVolteVideoAllCall;
                param["IsCheckVolteVideoMoCall"] = this.IsCheckVolteVideoMoCall;
                param["IsCheckSRVCCCall"] = this.IsCheckSRVCCCall;
                param["IsCheckLeakOutRatio"] = this.IsCheckLeakOutRatio;
                param["IsCheckOutDoorOtherSet"] = this.IsCheckOutDoorOtherSet;
                param["IsCheckInAndOutSrc"] = this.IsCheckInAndOutSrc;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                setParam(param);
            }
        }

        private void setParam(Dictionary<string, object> param)
        {
            this.IsCheckHandOver = getValidBool(param, "IsCheckHandOver");
            this.IsCheckCsfb = getValidBool(param, "IsCheckCsfb");
            this.IsCheckVolteVoiceAllCall = getValidBool(param, "IsCheckVolteVoiceAllCall");
            this.IsCheckVolteVoiceMoCall = getValidBool(param, "IsCheckVolteVoiceMoCall");
            this.IsCheckVolteVideoAllCall = getValidBool(param, "IsCheckVolteVideoAllCall");
            this.IsCheckVolteVideoMoCall = getValidBool(param, "IsCheckVolteVideoMoCall");
            this.IsCheckSRVCCCall = getValidBool(param, "IsCheckSRVCCCall");
            this.IsCheckLeakOutRatio = getValidBool(param, "IsCheckLeakOutRatio");
            this.IsCheckOutDoorOtherSet = getValidBool(param, "IsCheckOutDoorOtherSet");
            this.IsCheckInAndOutSrc = getValidBool(param, "IsCheckInAndOutSrc");
        }

        private bool getValidBool(Dictionary<string, object> param, string name, bool defaultValue = true)
        {
            if (param.ContainsKey(name) && param[name] != null)
            {
                return (bool)param[name];
            }
            return defaultValue;
        }
    }
}
