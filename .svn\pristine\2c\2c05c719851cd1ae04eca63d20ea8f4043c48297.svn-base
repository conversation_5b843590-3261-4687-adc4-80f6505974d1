﻿namespace MasterCom.ES.UI
{
    partial class FindResultForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.lstViewResult = new System.Windows.Forms.ListView();
            this.columnIdx = new System.Windows.Forms.ColumnHeader();
            this.columnExp = new System.Windows.Forms.ColumnHeader();
            this.columnFormular = new System.Windows.Forms.ColumnHeader();
            this.columnProc = new System.Windows.Forms.ColumnHeader();
            this.columnGroup = new System.Windows.Forms.ColumnHeader();
            this.SuspendLayout();
            // 
            // lstViewResult
            // 
            this.lstViewResult.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnIdx,
            this.columnExp,
            this.columnFormular,
            this.columnProc,
            this.columnGroup});
            this.lstViewResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lstViewResult.FullRowSelect = true;
            this.lstViewResult.Location = new System.Drawing.Point(0, 0);
            this.lstViewResult.MultiSelect = false;
            this.lstViewResult.Name = "lstViewResult";
            this.lstViewResult.Size = new System.Drawing.Size(802, 379);
            this.lstViewResult.TabIndex = 0;
            this.lstViewResult.UseCompatibleStateImageBehavior = false;
            this.lstViewResult.View = System.Windows.Forms.View.Details;
            this.lstViewResult.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lstViewResult_MouseDoubleClick);
            // 
            // columnIdx
            // 
            this.columnIdx.Text = "";
            this.columnIdx.Width = 34;
            // 
            // columnExp
            // 
            this.columnExp.Text = "节点名称";
            this.columnExp.Width = 146;
            // 
            // columnFormular
            // 
            this.columnFormular.Text = "表达式";
            this.columnFormular.Width = 238;
            // 
            // columnProc
            // 
            this.columnProc.Text = "所在流程名";
            this.columnProc.Width = 208;
            // 
            // columnGroup
            // 
            this.columnGroup.Text = "所在组";
            this.columnGroup.Width = 146;
            // 
            // FindResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(802, 379);
            this.Controls.Add(this.lstViewResult);
            this.Name = "FindResultForm";
            this.Text = "FindResultForm";
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ListView lstViewResult;
        private System.Windows.Forms.ColumnHeader columnIdx;
        private System.Windows.Forms.ColumnHeader columnExp;
        private System.Windows.Forms.ColumnHeader columnFormular;
        private System.Windows.Forms.ColumnHeader columnProc;
        private System.Windows.Forms.ColumnHeader columnGroup;
    }
}