﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTNRScanGoodRsrpPoorSinr
{
    public partial class NRScanGoodRsrpPoorSinrDlg : Form
    {
        public NRScanGoodRsrpPoorSinrDlg()
        {
            InitializeComponent();
        }

        public FuncCondition Condition
        {
            get
            {
                FuncCondition cond = new FuncCondition();
                cond.Rsrp = (float)numRsrp.Value;
                cond.Sinr = (float)numSinr.Value;
                cond.CheckBand = chkBand.Checked;
                cond.CoverBand = (int)numBand.Value;
                return cond;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                numRsrp.Value = (decimal)value.Rsrp;
                numSinr.Value = (decimal)value.Sinr;
                chkBand.Checked = value.CheckBand;
                numBand.Value = (decimal)value.CoverBand;
            }
        }
    }

    public class FuncCondition
    {
        public float Rsrp
        {
            get;
            set;
        }
        public float Sinr
        {
            get;
            set;
        }

        public bool CheckBand
        {
            get;
            set;
        }

        public float CoverBand
        {
            get;
            set;
        }

    }
}
