﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.Interface
{
    public enum GatherMethod
    {
        E_SUM =1,//
        E_MEAN = 2,
        E_MAX =3,
        E_MIN =4,
        E_MEDIAN = 5,
        E_Ignore =6
    }

    public enum StatTbToken
    {
        area,
        cell_grid,
        cell,
        grid,
        log
    }

    /// <summary>
    /// 统计返回结构配置 16个image
    /// </summary>
    public class StatImgDefItem
    {
        public StatImgDefItem()
        {
            itemNewCode = "";
        }

        /// <summary>
        /// imageID
        /// </summary>
        public int imgID{ get; set; }
        /// <summary>
        /// paraid
        /// </summary>
        public int paraID{ get; set; }
        /// <summary>
        /// 表id
        /// </summary>
        public int tableID{ get; set; }
        /// <summary>
        /// 业务类型说明
        /// </summary>
        public string serviceName{ get; set; }
        /// <summary>
        /// 表名称
        /// </summary>
        public string tableName{ get; set; }
        /// <summary>
        /// 统计项类型
        /// </summary>
        public string cateName{ get; set; }
        /// <summary>
        /// 表中相应字段名称
        /// </summary>
        public string tableColumnName{ get; set; }
        /// <summary>
        /// 统计项前缀
        /// </summary>
        public string catePrefix{ get; set; }
        /// <summary>
        /// 统计单元ID
        /// </summary>
        public string itemCode{ get; set; }
        /// <summary>
        /// 统计单元名称
        /// </summary>
        public string itemDesc{ get; set; }
        /// <summary>
        /// 统计方法
        /// </summary>
        public GatherMethod gatherMethod{ get; set; }
        /// <summary>
        /// 特殊标识描述
        /// </summary>
        public string bak{ get; set; }
        /// <summary>
        /// 新统计单元ID
        /// </summary>
        public string itemNewCode { get; set; }

        private void initTbToken()
        {
            foreach (string token in Enum.GetNames(typeof(StatTbToken)))
            {
                if (tableName.EndsWith(token))
                {
                    ShortTbName = tableName.Substring(0, tableName.Length - token.Length);
                    this.Token = (StatTbToken)(Enum.Parse(typeof(StatTbToken), token));
                    break;
                }
            }
        }

        public StatTbToken Token
        {
            get;
            private set;
        }

        public string ShortTbName
        {
            get;
            private set;
        }

        internal static StatImgDefItem FillFrom(MasterCom.RAMS.Net.Content c)
        {
            StatImgDefItem item = new StatImgDefItem();
            item.imgID = c.GetParamInt();
            item.paraID = c.GetParamInt();
            item.tableID = c.GetParamInt();
            item.serviceName = string.Intern(c.GetParamString());
            item.tableName = string.Intern(c.GetParamString());
            item.cateName = string.Intern(c.GetParamString());
            item.tableColumnName = string.Intern(c.GetParamString());
            item.catePrefix = string.Intern(c.GetParamString());
            item.itemCode = c.GetParamString();
            item.itemDesc = c.GetParamString();
            item.gatherMethod = (GatherMethod)c.GetParamInt();
            item.bak = c.GetParamString();
            item.initTbToken();
            return item;
        }

        internal static StatImgDefItem FillFrom(string[] str)
        {
            int idx = 0;
            StatImgDefItem item = new StatImgDefItem();
            item.imgID = Convert.ToInt32(str[idx++]);
            item.paraID = Convert.ToInt32(str[idx++]);
            item.tableID = Convert.ToInt32(str[idx++]);
            item.serviceName = string.Intern(str[idx++].Trim());
            item.tableName = string.Intern(str[idx++].ToLower().Trim());
            item.cateName = string.Intern(str[idx++].Trim());
            item.tableColumnName = string.Intern(str[idx++].ToLower().Trim());
            item.catePrefix = string.Intern(str[idx++].Trim());
            item.itemCode = str[idx++].Trim();
            item.itemDesc = str[idx++].Trim();
            item.gatherMethod = (GatherMethod)Convert.ToInt32(str[idx++]);
            item.bak = str[idx++].Trim();
            item.bak = item.bak.Equals("NULL") ? "" : item.bak;

            if (idx < str.Length)
                item.itemNewCode = str[idx].Trim();

            item.initTbToken();
            return item;
        }
        public string GetTriIdStr()
        {
            return imgID + "," + paraID + "," + tableID;
        }
    }
}
