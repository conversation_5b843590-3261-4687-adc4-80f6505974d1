﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class CellStater
    {
        public int SN { get; set; }
        public ICell Cell { get; set; }
        public string TAC { get; set; }
        public string ECI { get; set; }
        public string EARFCN { get; set; }
        public string PCI { get; set; }
        public string CellName { get; set; }
        public int TPCount { get; set; }
        public long TPTotalCount { get; set; }
        public double TPProportion { get; set; }

        public CellSetDataSub RsrpDataSub { get; private set; } = new CellSetDataSub(-10, -120);
        public CellSetDataSub SinrDataSub { get; private set; } = new CellSetDataSub(50, -50);
        public CellSetDataSub RsrqDataSub { get; private set; } = new CellSetDataSub(40, -40);
        public CellSetDataSub DistanceDataSub { get; private set; } = new CellSetDataSub();

        public string AreaPlaceDesc { get; private set; }
        public string GridDesc { get; private set; }

        public virtual void Calculate()
        {
            RsrpDataSub.Calculate();
            SinrDataSub.Calculate();
            RsrqDataSub.Calculate();
            DistanceDataSub.Calculate();

            TPProportion = Math.Round(1d * TPCount / TPTotalCount, 2);
            //setDesc();
        }

        //private void setDesc()
        //{
        //    AreaPlaceDesc = GISManager.GetInstance().GetAreaPlaceDesc(Cell.Longitude, Cell.Latitude);
        //    GridDesc = GISManager.GetInstance().GetGridDesc(Cell.Longitude, Cell.Latitude);
        //}
    }

    public class NRCellStater : CellStater
    {
        public override void Calculate()
        {
            if (Cell is NRCell)
            {
                NRCell nrCell = Cell as NRCell;
                CellName = nrCell.Name;
                TAC = nrCell.TAC.ToString();
                ECI = nrCell.NCI.ToString();
                EARFCN = nrCell.SSBARFCN.ToString();
                PCI = nrCell.PCI.ToString();
            }

            base.Calculate();
        }
    }
}
