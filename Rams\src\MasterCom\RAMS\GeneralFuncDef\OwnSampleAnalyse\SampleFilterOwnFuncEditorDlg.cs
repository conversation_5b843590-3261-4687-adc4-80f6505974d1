﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.OwnSampleAnalyse;
using MasterCom.RAMS.Model.Interface;
using System.Xml;

namespace MasterCom.RAMS.Func
{
    public partial class SampleFilterOwnFuncEditorDlg : Form
    {
        CryptionData cdata = new CryptionData();
        public SampleFilterOwnFuncEditorDlg()
        {
            InitializeComponent();
        }

        private void cbxColorOwnFunc_SelectedIndexChanged(object sender, EventArgs e)
        {
            CommonAnalyserCommander commander = cbxColorOwnFunc.SelectedItem as CommonAnalyserCommander;
            if(commander!=null)
            {
                tbxFuncName.Text = commander.funcName;
                tbxDesc.Text = commander.desc;
                tbxCodeInput.Text = cdata.DecryptionStringdata(commander.codeString);
                tbxNoteDesc.Text = commander.descriptionNote;
            }
            else
            {
                tbxFuncName.Text = "";
                tbxDesc.Text = "";
                tbxCodeInput.Text = "";
            }
        }

        private void btnApplyCommand_Click(object sender, EventArgs e)
        {
            CommonAnalyserCommander commander = new CommonAnalyserCommander();
            commander.funcName = tbxFuncName.Text.Trim();
            commander.desc = tbxDesc.Text.Trim();
            commander.descriptionNote = tbxNoteDesc.Text.Trim();
            commander.codeString = cdata.EncryptionStringData(tbxCodeInput.Text);
            commander._classReady = false;
            commander._hasError = false;
            string retStringErr =commander.initFuncClass_TestPoint();
            if(!commander._classReady)
            {
                MessageBox.Show("Check Error" + retStringErr);
                return;
            }
            foreach (CommonAnalyserCommander cmd in cbxColorOwnFunc.Items)
            {
                if(cmd.funcName==commander.funcName || cmd.desc == commander.desc)
                {
                    DialogResult res = MessageBox.Show(this, "该操作函数" + commander.funcName + "," + commander.desc + "存在同名,是否替换？", "保存", MessageBoxButtons.OKCancel);
                    if (res == DialogResult.OK)
                    {
                        cmd.funcName = commander.funcName;
                        cmd.desc = commander.desc;
                        cmd.codeString = commander.codeString;
                        cmd.descriptionNote = commander.descriptionNote;
                        cmd._classReady = false;
                        cmd._hasError = false;
                        cmd.clzzInst = null;
                        return;
                    }
                }
            }
            cbxColorOwnFunc.Items.Add(commander);
            cbxColorOwnFunc.SelectedItem = commander;
                
        }
        private void btnDeleteFunc_Click(object sender, EventArgs e)
        {
            CommonAnalyserCommander commander = cbxColorOwnFunc.SelectedItem as CommonAnalyserCommander;
            if (commander != null)
            {
                DialogResult res = MessageBox.Show(this, "删除该操作函数" + commander.desc + "？", "删除", MessageBoxButtons.OKCancel);
                if (res == DialogResult.OK)
                {
                    cbxColorOwnFunc.Items.Remove(commander);
                }
            }
        }

        internal void FillSetting(List<CommonAnalyserCommander> settings)
        {
            foreach (CommonAnalyserCommander commander in settings)
            {
                cbxColorOwnFunc.Items.Add(commander);
            }
        }

        internal List<CommonAnalyserCommander> GetSettingList()
        {
            List<CommonAnalyserCommander> list = new List<CommonAnalyserCommander>();
            foreach (CommonAnalyserCommander cmd in cbxColorOwnFunc.Items)
            {
                list.Add(cmd);
            }
            return list;
        }

        private void btnSaveToFile_Click(object sender, EventArgs e)
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement cfg = configFile.AddConfig("SampleAnaCommanderOptions");
                List<object> styles = new List<object>();
                foreach (CommonAnalyserCommander rpt in cbxColorOwnFunc.Items)
                {
                    styles.Add(rpt.Param);
                }
                configFile.AddItem(cfg, "options", styles);
                configFile.Save(string.Format(Application.StartupPath + "/config/samplefilter.xml"));
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存失败!" + ex.Message);
            }
        }
    }
}