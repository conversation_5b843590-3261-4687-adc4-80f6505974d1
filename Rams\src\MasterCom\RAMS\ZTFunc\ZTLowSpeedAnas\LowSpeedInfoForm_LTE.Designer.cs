﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedInfoForm_LTE
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportShp = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemShowAllPointMap = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemOnlyShowSelectedPoint = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnSN = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRoadDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDistance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDuration = new DevExpress.XtraGrid.Columns.GridColumn();
            this.TotalPointCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LowPointCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn小于2M采样点个数 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn小于2M采样点占比 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn小于512K采样点个数 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn小于512K采样点占比 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn大于等于1M采样点个数 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn大于等于1M采样点占比 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnHighSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLowSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDistance_0SPeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRate_Distance_0Speed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMeanSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_Rscp_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_Rscp_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_Rscp_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.N_RSRP_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.N_RSRP_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.N_RSRP_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_C2I_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_C2I_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_C2I_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDpch_Rscp_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDpch_Rscp_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDpch_Rscp_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTM3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRank2_Indicator = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPRb_Num_s = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLACCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnFreq = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(890, 416);
            this.gridControl.TabIndex = 3;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport,
            this.miExportShp,
            this.toolStripMenuItemShowAllPointMap,
            this.toolStripMenuItemOnlyShowSelectedPoint});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(161, 92);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(160, 22);
            this.ToolStripMenuItemExport.Text = "导出到Excel...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // miExportShp
            // 
            this.miExportShp.Name = "miExportShp";
            this.miExportShp.Size = new System.Drawing.Size(160, 22);
            this.miExportShp.Text = "导出shp图层...";
            this.miExportShp.Click += new System.EventHandler(this.miExportShp_Click);
            // 
            // toolStripMenuItemShowAllPointMap
            // 
            this.toolStripMenuItemShowAllPointMap.Name = "toolStripMenuItemShowAllPointMap";
            this.toolStripMenuItemShowAllPointMap.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItemShowAllPointMap.Text = "全图显示";
            this.toolStripMenuItemShowAllPointMap.Click += new System.EventHandler(this.toolStripMenuItemShowAllPointMap_Click);
            // 
            // toolStripMenuItemOnlyShowSelectedPoint
            // 
            this.toolStripMenuItemOnlyShowSelectedPoint.Name = "toolStripMenuItemOnlyShowSelectedPoint";
            this.toolStripMenuItemOnlyShowSelectedPoint.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItemOnlyShowSelectedPoint.Text = "只显示选中样点";
            this.toolStripMenuItemOnlyShowSelectedPoint.Click += new System.EventHandler(this.toolStripMenuItemOnlyShowSelectedPoint_Click);
            // 
            // gridView
            // 
            this.gridView.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridView.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView.ColumnPanelRowHeight = 50;
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnSN,
            this.gridColumn34,
            this.gridColumnGridName,
            this.gridColumnFileName,
            this.gridColumnTime,
            this.gridColumnLongitude,
            this.gridColumnLatitude,
            this.gridColumnRoadDesc,
            this.gridColumnNetType,
            this.gridColumn28,
            this.gridColumnDistance,
            this.gridColumnDuration,
            this.TotalPointCount,
            this.LowPointCount,
            this.gridColumn24,
            this.gridColumn小于2M采样点个数,
            this.gridColumn小于2M采样点占比,
            this.gridColumn小于512K采样点个数,
            this.gridColumn小于512K采样点占比,
            this.gridColumn大于等于1M采样点个数,
            this.gridColumn大于等于1M采样点占比,
            this.gridColumnHighSpeed,
            this.gridColumnLowSpeed,
            this.gridColumnDistance_0SPeed,
            this.gridColumnRate_Distance_0Speed,
            this.gridColumnMeanSpeed,
            this.gridColumn4,
            this.gridColumnPccpch_Rscp_Max,
            this.gridColumnPccpch_Rscp_Min,
            this.gridColumnPccpch_Rscp_Mean,
            this.N_RSRP_Max,
            this.N_RSRP_Min,
            this.N_RSRP_Mean,
            this.gridColumnPccpch_C2I_Max,
            this.gridColumnPccpch_C2I_Min,
            this.gridColumnPccpch_C2I_Mean,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn13,
            this.gridColumn12,
            this.gridColumn14,
            this.gridColumnDpch_Rscp_Max,
            this.gridColumnDpch_Rscp_Min,
            this.gridColumnDpch_Rscp_Mean,
            this.gridColumn15,
            this.gridColumnTM3,
            this.gridColumn16,
            this.gridColumnRank2_Indicator,
            this.gridColumn18,
            this.gridColumn17,
            this.gridColumnPRb_Num_s,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumnLACCI,
            this.gridColumnCellName,
            this.gridColumnFreq,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnSN
            // 
            this.gridColumnSN.Caption = "序号";
            this.gridColumnSN.FieldName = "SN";
            this.gridColumnSN.Name = "gridColumnSN";
            this.gridColumnSN.Visible = true;
            this.gridColumnSN.VisibleIndex = 0;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "地市";
            this.gridColumn34.FieldName = "CityName";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 1;
            // 
            // gridColumnGridName
            // 
            this.gridColumnGridName.Caption = "网格名称";
            this.gridColumnGridName.FieldName = "GridName";
            this.gridColumnGridName.Name = "gridColumnGridName";
            this.gridColumnGridName.Visible = true;
            this.gridColumnGridName.VisibleIndex = 2;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 3;
            this.gridColumnFileName.Width = 151;
            // 
            // gridColumnTime
            // 
            this.gridColumnTime.Caption = "开始时间";
            this.gridColumnTime.FieldName = "DateTimeString";
            this.gridColumnTime.Name = "gridColumnTime";
            this.gridColumnTime.Visible = true;
            this.gridColumnTime.VisibleIndex = 4;
            this.gridColumnTime.Width = 146;
            // 
            // gridColumnLongitude
            // 
            this.gridColumnLongitude.Caption = "中心经度";
            this.gridColumnLongitude.FieldName = "Longitude";
            this.gridColumnLongitude.Name = "gridColumnLongitude";
            this.gridColumnLongitude.Visible = true;
            this.gridColumnLongitude.VisibleIndex = 5;
            this.gridColumnLongitude.Width = 90;
            // 
            // gridColumnLatitude
            // 
            this.gridColumnLatitude.Caption = "中心纬度";
            this.gridColumnLatitude.FieldName = "Latitude";
            this.gridColumnLatitude.Name = "gridColumnLatitude";
            this.gridColumnLatitude.Visible = true;
            this.gridColumnLatitude.VisibleIndex = 6;
            this.gridColumnLatitude.Width = 90;
            // 
            // gridColumnRoadDesc
            // 
            this.gridColumnRoadDesc.Caption = "道路";
            this.gridColumnRoadDesc.FieldName = "RoadDesc";
            this.gridColumnRoadDesc.Name = "gridColumnRoadDesc";
            this.gridColumnRoadDesc.Visible = true;
            this.gridColumnRoadDesc.VisibleIndex = 7;
            this.gridColumnRoadDesc.Width = 115;
            // 
            // gridColumnNetType
            // 
            this.gridColumnNetType.Caption = "网络类型";
            this.gridColumnNetType.FieldName = "NetType";
            this.gridColumnNetType.Name = "gridColumnNetType";
            this.gridColumnNetType.Visible = true;
            this.gridColumnNetType.VisibleIndex = 8;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "业务类型";
            this.gridColumn28.FieldName = "AppType";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 9;
            // 
            // gridColumnDistance
            // 
            this.gridColumnDistance.Caption = "持续距离(米)";
            this.gridColumnDistance.FieldName = "Distance";
            this.gridColumnDistance.Name = "gridColumnDistance";
            this.gridColumnDistance.Visible = true;
            this.gridColumnDistance.VisibleIndex = 10;
            this.gridColumnDistance.Width = 73;
            // 
            // gridColumnDuration
            // 
            this.gridColumnDuration.Caption = "持续时间(秒)";
            this.gridColumnDuration.FieldName = "Duration";
            this.gridColumnDuration.Name = "gridColumnDuration";
            this.gridColumnDuration.Visible = true;
            this.gridColumnDuration.VisibleIndex = 11;
            this.gridColumnDuration.Width = 66;
            // 
            // TotalPointCount
            // 
            this.TotalPointCount.Caption = "采样点总数";
            this.TotalPointCount.FieldName = "TotalPointCount";
            this.TotalPointCount.Name = "TotalPointCount";
            this.TotalPointCount.Visible = true;
            this.TotalPointCount.VisibleIndex = 12;
            this.TotalPointCount.Width = 70;
            // 
            // LowPointCount
            // 
            this.LowPointCount.Caption = "低速率采样点个数";
            this.LowPointCount.FieldName = "LowPointCount";
            this.LowPointCount.Name = "LowPointCount";
            this.LowPointCount.Visible = true;
            this.LowPointCount.VisibleIndex = 13;
            this.LowPointCount.Width = 90;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "低速率采样点占比(%)";
            this.gridColumn24.FieldName = "LowPercent";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 14;
            this.gridColumn24.Width = 90;
            // 
            // gridColumn小于2M采样点个数
            // 
            this.gridColumn小于2M采样点个数.Caption = "小于2M采样点个数";
            this.gridColumn小于2M采样点个数.FieldName = "Under2MPointCount";
            this.gridColumn小于2M采样点个数.Name = "gridColumn小于2M采样点个数";
            this.gridColumn小于2M采样点个数.Visible = true;
            this.gridColumn小于2M采样点个数.VisibleIndex = 15;
            // 
            // gridColumn小于2M采样点占比
            // 
            this.gridColumn小于2M采样点占比.Caption = "小于2M采样点占比（%）";
            this.gridColumn小于2M采样点占比.FieldName = "Under2MPointPercent";
            this.gridColumn小于2M采样点占比.Name = "gridColumn小于2M采样点占比";
            this.gridColumn小于2M采样点占比.Visible = true;
            this.gridColumn小于2M采样点占比.VisibleIndex = 16;
            // 
            // gridColumn小于512K采样点个数
            // 
            this.gridColumn小于512K采样点个数.Caption = "小于512K采样点个数";
            this.gridColumn小于512K采样点个数.FieldName = "Under512KPointCount";
            this.gridColumn小于512K采样点个数.Name = "gridColumn小于512K采样点个数";
            this.gridColumn小于512K采样点个数.Visible = true;
            this.gridColumn小于512K采样点个数.VisibleIndex = 17;
            // 
            // gridColumn小于512K采样点占比
            // 
            this.gridColumn小于512K采样点占比.Caption = "小于512K采样点占比";
            this.gridColumn小于512K采样点占比.FieldName = "Under512KPointPercent";
            this.gridColumn小于512K采样点占比.Name = "gridColumn小于512K采样点占比";
            this.gridColumn小于512K采样点占比.Visible = true;
            this.gridColumn小于512K采样点占比.VisibleIndex = 18;
            // 
            // gridColumn大于等于1M采样点个数
            // 
            this.gridColumn大于等于1M采样点个数.Caption = "大于等于1M采样点个数";
            this.gridColumn大于等于1M采样点个数.FieldName = "Over1MKPointCount";
            this.gridColumn大于等于1M采样点个数.Name = "gridColumn大于等于1M采样点个数";
            this.gridColumn大于等于1M采样点个数.Visible = true;
            this.gridColumn大于等于1M采样点个数.VisibleIndex = 19;
            // 
            // gridColumn大于等于1M采样点占比
            // 
            this.gridColumn大于等于1M采样点占比.Caption = "大于等于1M采样点占比";
            this.gridColumn大于等于1M采样点占比.FieldName = "Over1MKPointPercent";
            this.gridColumn大于等于1M采样点占比.Name = "gridColumn大于等于1M采样点占比";
            this.gridColumn大于等于1M采样点占比.Visible = true;
            this.gridColumn大于等于1M采样点占比.VisibleIndex = 20;
            // 
            // gridColumnHighSpeed
            // 
            this.gridColumnHighSpeed.Caption = "最高速率(M)";
            this.gridColumnHighSpeed.FieldName = "HighSpeed";
            this.gridColumnHighSpeed.Name = "gridColumnHighSpeed";
            this.gridColumnHighSpeed.Visible = true;
            this.gridColumnHighSpeed.VisibleIndex = 21;
            this.gridColumnHighSpeed.Width = 69;
            // 
            // gridColumnLowSpeed
            // 
            this.gridColumnLowSpeed.Caption = "最低速率(M)";
            this.gridColumnLowSpeed.FieldName = "LowSpeed";
            this.gridColumnLowSpeed.Name = "gridColumnLowSpeed";
            this.gridColumnLowSpeed.Visible = true;
            this.gridColumnLowSpeed.VisibleIndex = 22;
            this.gridColumnLowSpeed.Width = 69;
            // 
            // gridColumnDistance_0SPeed
            // 
            this.gridColumnDistance_0SPeed.Caption = "0速率里程";
            this.gridColumnDistance_0SPeed.FieldName = "Distance_0Speed";
            this.gridColumnDistance_0SPeed.Name = "gridColumnDistance_0SPeed";
            this.gridColumnDistance_0SPeed.Visible = true;
            this.gridColumnDistance_0SPeed.VisibleIndex = 23;
            // 
            // gridColumnRate_Distance_0Speed
            // 
            this.gridColumnRate_Distance_0Speed.Caption = "0速率里程占比(%)";
            this.gridColumnRate_Distance_0Speed.FieldName = "Rate_Distance_0Speed";
            this.gridColumnRate_Distance_0Speed.Name = "gridColumnRate_Distance_0Speed";
            this.gridColumnRate_Distance_0Speed.Visible = true;
            this.gridColumnRate_Distance_0Speed.VisibleIndex = 24;
            this.gridColumnRate_Distance_0Speed.Width = 90;
            // 
            // gridColumnMeanSpeed
            // 
            this.gridColumnMeanSpeed.Caption = "平均速率(M)";
            this.gridColumnMeanSpeed.FieldName = "MeanSpeed";
            this.gridColumnMeanSpeed.Name = "gridColumnMeanSpeed";
            this.gridColumnMeanSpeed.Visible = true;
            this.gridColumnMeanSpeed.VisibleIndex = 25;
            this.gridColumnMeanSpeed.Width = 69;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "PDSCH_BLER平均值";
            this.gridColumn4.FieldName = "MeanPDSCHBLER";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 26;
            this.gridColumn4.Width = 70;
            // 
            // gridColumnPccpch_Rscp_Max
            // 
            this.gridColumnPccpch_Rscp_Max.Caption = "RSRP最大值";
            this.gridColumnPccpch_Rscp_Max.FieldName = "RSRP_Max";
            this.gridColumnPccpch_Rscp_Max.Name = "gridColumnPccpch_Rscp_Max";
            this.gridColumnPccpch_Rscp_Max.Visible = true;
            this.gridColumnPccpch_Rscp_Max.VisibleIndex = 27;
            this.gridColumnPccpch_Rscp_Max.Width = 90;
            // 
            // gridColumnPccpch_Rscp_Min
            // 
            this.gridColumnPccpch_Rscp_Min.Caption = "RSRP最小值";
            this.gridColumnPccpch_Rscp_Min.FieldName = "RSRP_Min";
            this.gridColumnPccpch_Rscp_Min.Name = "gridColumnPccpch_Rscp_Min";
            this.gridColumnPccpch_Rscp_Min.Visible = true;
            this.gridColumnPccpch_Rscp_Min.VisibleIndex = 28;
            this.gridColumnPccpch_Rscp_Min.Width = 90;
            // 
            // gridColumnPccpch_Rscp_Mean
            // 
            this.gridColumnPccpch_Rscp_Mean.Caption = "RSRP平均值";
            this.gridColumnPccpch_Rscp_Mean.FieldName = "RSRP_Mean";
            this.gridColumnPccpch_Rscp_Mean.Name = "gridColumnPccpch_Rscp_Mean";
            this.gridColumnPccpch_Rscp_Mean.Visible = true;
            this.gridColumnPccpch_Rscp_Mean.VisibleIndex = 29;
            this.gridColumnPccpch_Rscp_Mean.Width = 90;
            // 
            // N_RSRP_Max
            // 
            this.N_RSRP_Max.Caption = "邻区RSRP最大值";
            this.N_RSRP_Max.FieldName = "NRSRP_Max";
            this.N_RSRP_Max.Name = "N_RSRP_Max";
            this.N_RSRP_Max.Visible = true;
            this.N_RSRP_Max.VisibleIndex = 30;
            // 
            // N_RSRP_Min
            // 
            this.N_RSRP_Min.Caption = "邻区RSRP最小值";
            this.N_RSRP_Min.FieldName = "NRSRP_Min";
            this.N_RSRP_Min.Name = "N_RSRP_Min";
            this.N_RSRP_Min.Visible = true;
            this.N_RSRP_Min.VisibleIndex = 31;
            // 
            // N_RSRP_Mean
            // 
            this.N_RSRP_Mean.Caption = "邻区RSRP平均值";
            this.N_RSRP_Mean.FieldName = "NRSRP_Mean";
            this.N_RSRP_Mean.Name = "N_RSRP_Mean";
            this.N_RSRP_Mean.Visible = true;
            this.N_RSRP_Mean.VisibleIndex = 32;
            // 
            // gridColumnPccpch_C2I_Max
            // 
            this.gridColumnPccpch_C2I_Max.Caption = "SINR最大值";
            this.gridColumnPccpch_C2I_Max.FieldName = "SINR_Max";
            this.gridColumnPccpch_C2I_Max.Name = "gridColumnPccpch_C2I_Max";
            this.gridColumnPccpch_C2I_Max.Visible = true;
            this.gridColumnPccpch_C2I_Max.VisibleIndex = 33;
            this.gridColumnPccpch_C2I_Max.Width = 60;
            // 
            // gridColumnPccpch_C2I_Min
            // 
            this.gridColumnPccpch_C2I_Min.Caption = "SINR最小值";
            this.gridColumnPccpch_C2I_Min.FieldName = "SINR_Min";
            this.gridColumnPccpch_C2I_Min.Name = "gridColumnPccpch_C2I_Min";
            this.gridColumnPccpch_C2I_Min.Visible = true;
            this.gridColumnPccpch_C2I_Min.VisibleIndex = 34;
            this.gridColumnPccpch_C2I_Min.Width = 60;
            // 
            // gridColumnPccpch_C2I_Mean
            // 
            this.gridColumnPccpch_C2I_Mean.Caption = "SINR平均值";
            this.gridColumnPccpch_C2I_Mean.FieldName = "SINR_Mean";
            this.gridColumnPccpch_C2I_Mean.Name = "gridColumnPccpch_C2I_Mean";
            this.gridColumnPccpch_C2I_Mean.Visible = true;
            this.gridColumnPccpch_C2I_Mean.VisibleIndex = 35;
            this.gridColumnPccpch_C2I_Mean.Width = 60;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "连续SINR质差里程占比(%)";
            this.gridColumn1.FieldName = "RateWeakSinr";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 36;
            this.gridColumn1.Width = 90;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "重叠覆盖度≥3比例";
            this.gridColumn2.FieldName = "RateMultiCoverage";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 37;
            this.gridColumn2.Width = 90;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "重叠覆盖里程占比";
            this.gridColumn3.FieldName = "RateDisMulticoverage";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 38;
            this.gridColumn3.Width = 60;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "下行码字0MCS平均值";
            this.gridColumn5.FieldName = "Code0Mean";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 39;
            this.gridColumn5.Width = 90;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "下行码字1MCS平均值";
            this.gridColumn6.FieldName = "Code1Mean";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 40;
            this.gridColumn6.Width = 90;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "下行码字0最高频率MCS(%)";
            this.gridColumn7.FieldName = "Code0Max";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 41;
            this.gridColumn7.Width = 90;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "下行码字1最高频率MCS(%)";
            this.gridColumn8.FieldName = "Code1Max";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 42;
            this.gridColumn8.Width = 90;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "码字0CQI平均值";
            this.gridColumn9.FieldName = "CQI0Mean";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 43;
            this.gridColumn9.Width = 60;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "码字1CQI平均值";
            this.gridColumn10.FieldName = "CQI1Mean";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 44;
            this.gridColumn10.Width = 60;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "下行码字0 64QAM占比";
            this.gridColumn11.FieldName = "Code0_64QAMRate";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 45;
            this.gridColumn11.Width = 90;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "下行码字1 64QAM占比";
            this.gridColumn13.FieldName = "Code1_64QAMRate";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 46;
            this.gridColumn13.Width = 90;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "下行码字0 16QAM占比";
            this.gridColumn12.FieldName = "Code0_16QAMRate";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 47;
            this.gridColumn12.Width = 90;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "下行码字1 16QAM占比";
            this.gridColumn14.FieldName = "Code1_16QAMRate";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 48;
            this.gridColumn14.Width = 90;
            // 
            // gridColumnDpch_Rscp_Max
            // 
            this.gridColumnDpch_Rscp_Max.Caption = "Throughput_DL最大值";
            this.gridColumnDpch_Rscp_Max.FieldName = "Throughput_DL_Max";
            this.gridColumnDpch_Rscp_Max.Name = "gridColumnDpch_Rscp_Max";
            this.gridColumnDpch_Rscp_Max.Visible = true;
            this.gridColumnDpch_Rscp_Max.VisibleIndex = 49;
            this.gridColumnDpch_Rscp_Max.Width = 90;
            // 
            // gridColumnDpch_Rscp_Min
            // 
            this.gridColumnDpch_Rscp_Min.Caption = "Throughput_DL最小值";
            this.gridColumnDpch_Rscp_Min.FieldName = "Throughput_DL_Min";
            this.gridColumnDpch_Rscp_Min.Name = "gridColumnDpch_Rscp_Min";
            this.gridColumnDpch_Rscp_Min.Visible = true;
            this.gridColumnDpch_Rscp_Min.VisibleIndex = 50;
            this.gridColumnDpch_Rscp_Min.Width = 90;
            // 
            // gridColumnDpch_Rscp_Mean
            // 
            this.gridColumnDpch_Rscp_Mean.Caption = "Throughput_DL平均值";
            this.gridColumnDpch_Rscp_Mean.FieldName = "Throughput_DL_Mean";
            this.gridColumnDpch_Rscp_Mean.Name = "gridColumnDpch_Rscp_Mean";
            this.gridColumnDpch_Rscp_Mean.Visible = true;
            this.gridColumnDpch_Rscp_Mean.VisibleIndex = 51;
            this.gridColumnDpch_Rscp_Mean.Width = 90;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "Transmission_Mode";
            this.gridColumn15.DisplayFormat.FormatString = "F2";
            this.gridColumn15.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn15.FieldName = "Transmission_Mode";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 52;
            this.gridColumn15.Width = 90;
            // 
            // gridColumnTM3
            // 
            this.gridColumnTM3.Caption = "TM3比例（%）";
            this.gridColumnTM3.DisplayFormat.FormatString = "F2";
            this.gridColumnTM3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumnTM3.FieldName = "Transmission_Mode3";
            this.gridColumnTM3.Name = "gridColumnTM3";
            this.gridColumnTM3.Visible = true;
            this.gridColumnTM3.VisibleIndex = 53;
            this.gridColumnTM3.Width = 100;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "rank_indicator";
            this.gridColumn16.DisplayFormat.FormatString = "F2";
            this.gridColumn16.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn16.FieldName = "Rank_Indicator";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 54;
            this.gridColumn16.Width = 100;
            // 
            // gridColumnRank2_Indicator
            // 
            this.gridColumnRank2_Indicator.Caption = "双流时长占比（%）";
            this.gridColumnRank2_Indicator.DisplayFormat.FormatString = "F2";
            this.gridColumnRank2_Indicator.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumnRank2_Indicator.FieldName = "Rank2_Indicator";
            this.gridColumnRank2_Indicator.Name = "gridColumnRank2_Indicator";
            this.gridColumnRank2_Indicator.Visible = true;
            this.gridColumnRank2_Indicator.VisibleIndex = 55;
            this.gridColumnRank2_Indicator.Width = 120;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "误块率（%）";
            this.gridColumn18.DisplayFormat.FormatString = "F2";
            this.gridColumn18.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn18.FieldName = "PDSCH_BLER";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 56;
            this.gridColumn18.Width = 90;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "PDSCH_RB_Number";
            this.gridColumn17.DisplayFormat.FormatString = "F2";
            this.gridColumn17.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn17.FieldName = "PDSCH_RB_Number";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 57;
            this.gridColumn17.Width = 120;
            // 
            // gridColumnPRb_Num_s
            // 
            this.gridColumnPRb_Num_s.Caption = "PRB调度数";
            this.gridColumnPRb_Num_s.DisplayFormat.FormatString = "F2";
            this.gridColumnPRb_Num_s.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumnPRb_Num_s.FieldName = "PDSCH_PRb_Num_s";
            this.gridColumnPRb_Num_s.Name = "gridColumnPRb_Num_s";
            this.gridColumnPRb_Num_s.Visible = true;
            this.gridColumnPRb_Num_s.VisibleIndex = 58;
            this.gridColumnPRb_Num_s.Width = 90;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "PDCCH_DL_Grant_Count";
            this.gridColumn19.DisplayFormat.FormatString = "F2";
            this.gridColumn19.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn19.FieldName = "PDCCH_DL_Grant_Count";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 59;
            this.gridColumn19.Width = 120;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "Ratio_DL_Code0_HARQ_ACK";
            this.gridColumn20.DisplayFormat.FormatString = "F2";
            this.gridColumn20.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn20.FieldName = "Ratio_DL_Code0_HARQ_ACK";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 60;
            this.gridColumn20.Width = 140;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "Ratio_DL_Code0_HARQ_NACK";
            this.gridColumn21.DisplayFormat.FormatString = "F2";
            this.gridColumn21.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn21.FieldName = "Ratio_DL_Code0_HARQ_NACK";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 61;
            this.gridColumn21.Width = 140;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "Ratio_DL_Code1_HARQ_ACK";
            this.gridColumn22.DisplayFormat.FormatString = "F2";
            this.gridColumn22.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn22.FieldName = "Ratio_DL_Code1_HARQ_ACK";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 62;
            this.gridColumn22.Width = 140;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "Ratio_DL_Code1_HARQ_NACK";
            this.gridColumn23.DisplayFormat.FormatString = "F2";
            this.gridColumn23.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn23.FieldName = "Ratio_DL_Code1_HARQ_NACK";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 63;
            this.gridColumn23.Width = 140;
            // 
            // gridColumnLACCI
            // 
            this.gridColumnLACCI.Caption = "TAC-CellID";
            this.gridColumnLACCI.FieldName = "LACCIs";
            this.gridColumnLACCI.Name = "gridColumnLACCI";
            this.gridColumnLACCI.Visible = true;
            this.gridColumnLACCI.VisibleIndex = 64;
            this.gridColumnLACCI.Width = 177;
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "关联小区名称";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 65;
            this.gridColumnCellName.Width = 120;
            // 
            // gridColumnFreq
            // 
            this.gridColumnFreq.Caption = "频点";
            this.gridColumnFreq.FieldName = "BCCHs";
            this.gridColumnFreq.Name = "gridColumnFreq";
            this.gridColumnFreq.Visible = true;
            this.gridColumnFreq.VisibleIndex = 66;
            this.gridColumnFreq.Width = 160;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "片区名称";
            this.gridColumn25.FieldName = "AreaName";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 67;
            this.gridColumn25.Width = 90;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "代维区域";
            this.gridColumn27.FieldName = "AreaAgentName";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 68;
            this.gridColumn27.Width = 90;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "区域";
            this.gridColumn26.FieldName = "MotorWay";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 69;
            // 
            // LowSpeedInfoForm_LTE
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(890, 416);
            this.Controls.Add(this.gridControl);
            this.Name = "LowSpeedInfoForm_LTE";
            this.Text = "低速率里程分析";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDuration;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDistance;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnHighSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLowSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMeanSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLACCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFreq;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRoadDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_Rscp_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_Rscp_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_Rscp_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_C2I_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_C2I_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_C2I_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDpch_Rscp_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDpch_Rscp_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDpch_Rscp_Mean;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private System.Windows.Forms.ToolStripMenuItem miExportShp;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDistance_0SPeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRate_Distance_0Speed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTM3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRank2_Indicator;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPRb_Num_s;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn TotalPointCount;
        private DevExpress.XtraGrid.Columns.GridColumn LowPointCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetType;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemShowAllPointMap;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemOnlyShowSelectedPoint;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSN;
        private DevExpress.XtraGrid.Columns.GridColumn N_RSRP_Max;
        private DevExpress.XtraGrid.Columns.GridColumn N_RSRP_Min;
        private DevExpress.XtraGrid.Columns.GridColumn N_RSRP_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn小于2M采样点个数;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn小于2M采样点占比;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn小于512K采样点个数;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn小于512K采样点占比;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn大于等于1M采样点个数;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn大于等于1M采样点占比;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
    }
}