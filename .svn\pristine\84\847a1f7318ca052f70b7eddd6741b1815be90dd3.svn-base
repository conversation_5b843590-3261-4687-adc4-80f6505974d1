﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Stat
{
    class ProjectExtendDBOperator : DIYSQLBase
    {
        public ProjectExtendDBOperator(string excuteStr)
            : base(MainModel.GetInstance())
        {
            MainDB = true;
            this.excuteStr = excuteStr;
        }

        private readonly string excuteStr;

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }

                E_VType[] retArrDef = getSqlRetTypeArr();
                string strsql = getSqlTextString();
                string[] strArr = strsql.Split(';');
                int curIdx = 0;
                while (curIdx < strArr.Length)
                {
                    curIdx = addParam(package, retArrDef, strArr, curIdx);
                    clientProxy.Send();
                    System.Threading.Thread.Sleep(1000);
                }
                receiveRetData(clientProxy);
            }
            catch
            {
                //continue
            }
        }

        private int addParam(Package package, E_VType[] retArrDef, string[] strArr, int curIdx)
        {
            StringBuilder txt = new StringBuilder();
            for (; curIdx < strArr.Length; curIdx++)
            {
                txt.Append(strArr[curIdx] + ";");
                if (txt.Length > 6000)
                {
                    break;
                }
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(txt.ToString());

            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    if (i < retArrDef.Length - 1)
                    {
                        sb.Append(",");
                    }
                }
            }
            package.Content.AddParam(sb.ToString());
            return curIdx;
        }

        protected override string getSqlTextString()
        {
            return excuteStr;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        public override string Name
        {
            get { return "更新project_extend表（适用新增，删除）"; }
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {

        }
    }
}
