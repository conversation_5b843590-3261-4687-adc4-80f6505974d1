﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.NewBlackBlock;
using MasterCom.RAMS.NOP;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Frame
{
    /*
     * 
事件回放协议调用用例：rams:\ -Uadmin -P123456 -F-196453724 -T3 -A1421658507 -E907 -C5 -Z1421658557
rams:\     		//协议标记(固定)
-Uadmin  		//-U【路网通账号】
-P123456		    //-P【登录密码】 
-F-196453724	   	//-F【文件ID】
-T3 			//-T【操作Flag】(3：事件回放)
-A1421658507 	//-A【整型开始时间】
-E907       		//-E【事件ID】	
-C5			    //-C【地市ID】
-Z1421658557 	//-Z【整型结束时间】
     * 
     */
    public class StartArgInfo
    {
        public string username { get; set; } = "";
        public string password { get; set; } = "";
        public int districtID { get; set; } = -1; //地市ID
        public SAICmdType commandType { get; set; } = SAICmdType.Empty;

        private string strValue = string.Empty;

        public int projId { get; set; } //项目类型
        public int serviceType { get; set; } //业务类型
        public int fileId { get; set; } //文件ID,或问题黑点ID
        public List<int> eventIds { get; set; } = new List<int>(); //事件类型ID
        public int startTime { get; set; } //查询开始时间
        public int endTime { get; set; } //查询结束时间
        public int startTime2 { get; set; } //查询开始时间2
        public int endTime2 { get; set; } //查询结束时间2

        public int Lac { get; set; }
        public int Ci { get; set; }

        public int TaskId { get; set; }//工单ID
        public int TaskVersionId { get; set; }//工单版本号ID

        public int iLTLng { get; set; } //左上经度
        public int iLTLat { get; set; } //左上纬度
        public int iRBLng { get; set; } //右下经度
        public int iRBLat { get; set; } //右下纬度

        public double dCntLng { get; set; }//中心经度
        public double dCntLat { get; set; }//中心纬度
        public int iRadius { get; set; }//半径
        private string logTable = ""; //文件所在的log表名

        public string LogTable
        {
            get
            {
                if (!string.IsNullOrEmpty(logTable)) //如果logTable不是空字串就取logTable，否则从startTime中求logTable
                {
                    return logTable;
                }
                try
                {
                    DateTime dt = JavaDate.GetDateTimeFromMilliseconds(startTime * 1000L);
                    return "tb_log_file_" + dt.Year.ToString() + "_" + String.Format("{0:D2}", dt.Month);
                }
                catch
                {
                    return "";
                }
            }
            set { logTable = value; }
        }

        public string ReservedParam { get; set; }

        public static StartArgInfo Create(string[] argAry)
        {
            if (argAry.Length == 0)
            {
                return null;
            }
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < argAry.Length; i++)
            {
                sb.Append(argAry[i]);
                sb.Append(" ");
            }
            argAry = sb.ToString().Split(' ');

            StartArgInfo sai = new StartArgInfo();
            bool base64Encode = false;
            if (argAry[0].ToUpper().IndexOf("RAMS") != -1)
            {// RAMS://* 为URL传参调用客户端，参数值通过Base64加密，避免参数值出现空格出现解析参数异常。
                //形式为：rams:\* -T-(20) -F-（fileID） -N-（logTable） -C-（districtID）
                base64Encode = argAry[0].Contains("*");
            }
            if (base64Encode)
            {
                for (int i = 0; i < argAry.Length; i++)
                {
                    string arg = argAry[i];
                    arg = arg.Replace("–", "-");
                    sai.parseArgFromBase64(arg);
                }
            }
            else
            {//未采用Base64加密参数值，好处明文便于查看。
                for (int i = 0; i < argAry.Length; i++)
                {
                    string arg1 = argAry[i];
                    if (string.IsNullOrEmpty(arg1))
                    {
                        continue;
                    }
                    parseArgument(argAry, sai, i, arg1);
                }
            }
            sai.reflectoringAccounts();
            return sai;
        }

        private static void parseArgument(string[] argAry, StartArgInfo sai, int i, string arg1)
        {
            if (i + 1 < argAry.Length)
            {
                string arg2 = argAry[i + 1];
                if (!string.IsNullOrEmpty(arg2))
                {
                    if (arg2[0] != '-')
                    {//连接到上一参数
                        arg1 += arg2;
                    }
                    else
                    {//可解析
                        sai.parseArgument(arg2);
                    }
                    sai.parseArgument(arg1);
                }
            }
            else
            {
                sai.parseArgument(arg1);
            }
        }

        private void parseArgFromBase64(string argOne)
        {
            if (string.IsNullOrEmpty(argOne) || argOne[0] != '-')
            {
                return;
            }
            //-T-MQ==
            //-T-1
            StringBuilder sb = new StringBuilder();
            string token = string.Empty;
            int i = 1;
            for (; i < argOne.Length; i++)
            {
                char c = argOne[i];
                if (c == '-')
                {
                    break;
                }
                sb.Append(c);
            }
            token = sb.ToString();
            if (i + 1 >= argOne.Length)
            {
                return;
            }
            string base64 = argOne.Substring(i + 1);
            string value = Encoding.UTF8.GetString(Convert.FromBase64String(base64));
            bool isPraseSuccess;
            if (token == "A")
            {
                this.startTime = getValidData(value, out isPraseSuccess);
                if (!isPraseSuccess)
                {
                    DateTime dTime;
                    if (DateTime.TryParse(value, out dTime))
                    {
                        this.startTime = (int)(JavaDate.GetMilliseconds(dTime) / 1000);
                    }
                }
            }
            else if (token == "A2T")
            {
                this.startTime2 = getValidData(value, out isPraseSuccess);
                if (!isPraseSuccess)
                {
                    DateTime dTime;
                    if (DateTime.TryParse(value, out dTime))
                    {
                        this.startTime2 = (int)(JavaDate.GetMilliseconds(dTime) / 1000);
                    }
                }
            }
            else if (token == "B")
            {
                this.fileId = getValidData(value, out isPraseSuccess);
            }
            else if (token == "C")
            {
                this.districtID = getValidData(value, out isPraseSuccess);
                if (!isPraseSuccess && !string.IsNullOrEmpty(value))
                {
                        int iDistrictID = DistrictManager.GetInstance().GetDistrictID(value);
                        if (iDistrictID <= 0)
                        {
                            string strDistrictName = value.Contains("市") ? value.Replace("市", "") : value + "市";
                            iDistrictID = DistrictManager.GetInstance().GetDistrictID(strDistrictName);
                        }
                        if (iDistrictID > 0)
                        {
                            this.districtID = iDistrictID;
                        }
                }
            }
            else if (token == "CI")
            {
                this.Ci = getValidData(value, out isPraseSuccess);
            }
            else if (token == "D")
            {
                //
            }
            else if (token == "E")
            {
                string[] arrstr = value.Split(',');
                for (int j = 0; j < arrstr.Length; j++)
                {
                    int id;
                    if (int.TryParse(arrstr[j], out id))
                    {
                        this.eventIds.Add(id);
                    }
                }
            }
            else if (token == "F")
            {
                this.fileId = getValidData(value, out isPraseSuccess);
            }
            else if (token == "G")
            {
                strValue = value;
            }
            else if (token == "H")
            {
                //
            }
            else if (token == "I")
            {
                //
            }
            else if (token == "J")
            {
                //
            }
            else if (token == "K")
            {
                //
            }
            else if (token == "L")
            {
                //
            }
            else if (token == "LAC")
            {
                this.Lac = getValidData(value, out isPraseSuccess);
            }
            else if (token == "M")
            {
                //
            }
            else if (token == "N")
            {
                this.LogTable = value;
            }
            else if (token == "O")
            {
                //
            }
            else if (token == "P")
            {
                this.password = value;
            }
            else if (token == "P$")
            {
                this.password = DES.Decode(value);
            }
            else if (token == "Q")
            {
                //
            }
            else if (token == "R")
            {
                this.iRadius = getValidData(value, out isPraseSuccess);
            }
            else if (token == "S")
            {
                this.serviceType = getValidData(value, out isPraseSuccess);
            }
            else if (token == "T")
            {
                int tmp = 0;
                int.TryParse(value, out tmp);
                this.commandType = (SAICmdType)tmp;
            }
            else if (token == "TID")
            {
                this.TaskId = getValidData(value, out isPraseSuccess);
            }
            else if (token == "TVID")
            {
                this.TaskVersionId = getValidData(value, out isPraseSuccess);
            }
            else if (token == "U")
            {
                this.username = value;
            }
            else if (token == "V")
            {
                this.ReservedParam = value;
            }
            else if (token == "W")
            {
                //
            }
            else if (token == "X")
            {
                this.projId = getValidData(value, out isPraseSuccess);
            }
            else if (token == "X0")
            {
                int iCntLng;
                if (int.TryParse(value, out iCntLng))
                {
                    this.dCntLng = iCntLng / 10000000.0;
                }
                else
                {
                    this.dCntLng = getValidData(value);
                }
            }
            else if (token == "X1")
            {
                this.iLTLng = getValidData(value, out isPraseSuccess);
            }
            else if (token == "X2")
            {
                this.iRBLng = getValidData(value, out isPraseSuccess);
            }
            else if (token == "Y")
            {
                //
            }
            else if (token == "Y0")
            {
                int iCntLat;
                if (int.TryParse(value, out iCntLat))
                {
                    this.dCntLat = iCntLat / 10000000.0;
                }
                else
                {
                    this.dCntLat = getValidData(value);
                }
            }
            else if (token == "Y1")
            {
                this.iLTLat = getValidData(value, out isPraseSuccess);
            }
            else if (token == "Y2")
            {
                this.iRBLat = getValidData(value, out isPraseSuccess);
            }
            else if (token == "Z")
            {
                this.endTime = getValidData(value, out isPraseSuccess);
                if (!isPraseSuccess)
                {
                    DateTime dTime;
                    if (DateTime.TryParse(value, out dTime))
                    {
                        this.endTime = (int)(JavaDate.GetMilliseconds(dTime) / 1000);
                    }
                }
            }
            else if (token == "Z2T")
            {
                this.endTime2 = getValidData(value, out isPraseSuccess);
                if (!isPraseSuccess)
                {
                    DateTime dTime;
                    if (DateTime.TryParse(value, out dTime))
                    {
                        this.endTime2 = (int)(JavaDate.GetMilliseconds(dTime) / 1000);
                    }
                }
            }
        }

        private void parseArgument(string arg)
        {
            if (arg[0] != '-')
            {
                return;
            }
            StartArgInfo sai = this;
            bool isPraseSuccess;
            switch (arg[1])
            {
                case 'A':
                    dealAArgument(arg, sai);
                    break;
                case 'B':
                    sai.fileId = getValidData(arg.Substring(2), out isPraseSuccess);
                    break;
                case 'C':
                    dealCArgument(arg, sai);
                    break;
                case 'E':
                    dealEArgument(arg, sai);
                    break;
                case 'F':
                    sai.fileId = getValidData(arg.Substring(2), out isPraseSuccess);
                    break;
                case 'G':
                    break;
                case 'H':
                    break;
                case 'L':
                    if (arg.StartsWith("-LAC"))
                    {
                        sai.Lac = getValidData(arg.Substring(4), out isPraseSuccess);
                    }
                    break;
                case 'N':
                    sai.LogTable = arg.Substring(2);
                    break;
                case 'P':
                    dealPArgument(arg, sai);
                    break;
                case 'R':
                    break;
                case 'S':
                    sai.serviceType = getValidData(arg.Substring(2), out isPraseSuccess);
                    break;
                case 'T':
                    dealTArgument(arg, sai);
                    break;
                case 'U':
                    sai.username = arg.Substring(2);
                    break;
                case 'V':
                    sai.ReservedParam = arg.Substring(2);
                    break;
                case 'W':
                    break;
                case 'X':
                    sai.projId = getValidData(arg.Substring(2), out isPraseSuccess);
                    break;
                case 'Y':
                    break;
                case 'Z':
                    dealZArgument(arg, sai);
                    break;
            }
        }

        private void dealAArgument(string arg, StartArgInfo sai)
        {
            bool isPraseSuccess;
            if (arg.StartsWith("-A2T"))
            {
                sai.startTime2 = getValidData(arg.Substring(4), out isPraseSuccess);
                if (!isPraseSuccess)
                {
                    int dateLen = "yyyy-mm-dd".Length;
                    DateTime dTime;
                    DateTime.TryParse(string.Format("{0} {1}"
                        , arg.Substring(2, dateLen), arg.Substring(2 + dateLen)), out dTime);
                    sai.startTime2 = (int)(JavaDate.GetMilliseconds(dTime) / 1000);
                }
            }
            else
            {
                sai.startTime = getValidData(arg.Substring(2), out isPraseSuccess);
                if (!isPraseSuccess)
                {
                    int dateLen = "yyyy-mm-dd".Length;
                    DateTime dTime;
                    DateTime.TryParse(string.Format("{0} {1}"
                        , arg.Substring(2, dateLen), arg.Substring(2 + dateLen)), out dTime);
                    sai.startTime = (int)(JavaDate.GetMilliseconds(dTime) / 1000);
                }
            }
        }

        private void dealCArgument(string arg, StartArgInfo sai)
        {
            bool isPraseSuccess;
            if (arg.StartsWith("-CI"))
            {
                sai.Ci = getValidData(arg.Substring(3), out isPraseSuccess);
            }
            else
            {
                sai.districtID = getValidData(arg.Substring(2), out isPraseSuccess);
            }
        }

        private static void dealEArgument(string arg, StartArgInfo sai)
        {
            string evtIdstr = arg.Substring(2);
            string[] arrstr = evtIdstr.Split(',');
            for (int i = 0; i < arrstr.Length; i++)
            {
                int id;
                if (int.TryParse(arrstr[i], out id))
                {
                    sai.eventIds.Add(id);
                }
            }
        }

        private static void dealPArgument(string arg, StartArgInfo sai)
        {
            if (arg.StartsWith("-P$"))
            {
                sai.password = DES.Decode(arg.Substring(3));
            }
            else
            {
                sai.password = arg.Substring(2);
            }
        }

        private void dealTArgument(string arg, StartArgInfo sai)
        {
            bool isPraseSuccess;
            if (arg.StartsWith("-T1B"))
            {
                sai.startTime = getValidData(arg.Substring(4), out isPraseSuccess);
            }
            else if (arg.StartsWith("-T2B"))
            {
                sai.startTime2 = getValidData(arg.Substring(4), out isPraseSuccess);
            }
            else if (arg.StartsWith("-T1E"))
            {
                sai.endTime = getValidData(arg.Substring(4), out isPraseSuccess);
            }
            else if (arg.StartsWith("-T2E"))
            {
                sai.endTime2 = getValidData(arg.Substring(4), out isPraseSuccess);
            }
            else if (arg.StartsWith("-T"))
            {
                int tmp = 0;
                int.TryParse(arg.Substring(2), out tmp);
                sai.commandType = (SAICmdType)tmp;
            }
        }

        private void dealZArgument(string arg, StartArgInfo sai)
        {
            bool isPraseSuccess;
            sai.endTime = getValidData(arg.Substring(2), out isPraseSuccess);
            if (!isPraseSuccess)
            {
                int dateLen = "yyyy-mm-dd".Length;
                DateTime dTime;
                DateTime.TryParse(string.Format("{0} {1}"
                    , arg.Substring(2, dateLen), arg.Substring(2 + dateLen)), out dTime);
                sai.endTime = (int)(JavaDate.GetMilliseconds(dTime) / 1000);
            }
        }

        private double getValidData(string value)
        {
            double res;
            double.TryParse(value, out res);
            return res;
        }

        private int getValidData(string value, out bool praseSuccess)
        {
            int res;
            if (int.TryParse(value, out res))
            {
                praseSuccess = true;
            }
            else
            {
                praseSuccess = false;
            }
            return res;
        }

        /// <summary>
        /// 重构账号
        /// </summary>
        private void reflectoringAccounts()
        {
            //ShanxiJin
#if ShanxiJin
            //山西web地市账号-》路网通固定账号
            Dictionary<string, Accounts> idReflectorDic = new Dictionary<string, Accounts>();
            Accounts accounts = new Accounts("ty_zhangbo", "taiyuan");
            //太原
            idReflectorDic["machenye"] = accounts;
            idReflectorDic["likai"] = accounts;
            idReflectorDic["wuzhiqiang"] = accounts;
            idReflectorDic["maning"] = accounts;
            idReflectorDic["zhangxuying"] = accounts;
            idReflectorDic["haojunjie"] = accounts;
            idReflectorDic["tywangyan"] = accounts;
            idReflectorDic["yanzhiping"] = accounts;
            idReflectorDic["zhangweizheng"] = accounts;
            idReflectorDic["xuejian"] = accounts;
            idReflectorDic["xuefeng"] = accounts;
            idReflectorDic["sunxianzhao"] = accounts;
            idReflectorDic["zhangyongfei"] = accounts;
            idReflectorDic["wufeng"] = accounts;
            idReflectorDic["duanjunnan"] = accounts;
            idReflectorDic["fenghuiqiong"] = accounts;
            idReflectorDic["jizhe"] = accounts;
            idReflectorDic["jwyaoyong"] = accounts;
            idReflectorDic["lihaibin"] = accounts;
            idReflectorDic["songzirui"] = accounts;
            idReflectorDic["wuxiaokai"] = accounts;
            idReflectorDic["zhangbo"] = accounts;
            idReflectorDic["zhaoguohua"] = accounts;
            idReflectorDic["zhulin"] = accounts;
            //大同
            accounts = new Accounts("dt_huangshuai", "datong");
            idReflectorDic["zhaozhiyong"] = accounts;
            idReflectorDic["sunguodong"] = accounts;
            idReflectorDic["guozhiqing"] = accounts;
            idReflectorDic["liuxuanwang"] = accounts;
            idReflectorDic["lijianmin"] = accounts;
            idReflectorDic["chenrifei"] = accounts;
            idReflectorDic["chenjunjian"] = accounts;
            idReflectorDic["houjianmin"] = accounts;
            idReflectorDic["guoxingjun"] = accounts;
            idReflectorDic["diaohongwei"] = accounts;
            idReflectorDic["dtchenjie"] = accounts;
            idReflectorDic["huangshuai"] = accounts;
            idReflectorDic["niujianfeng"] = accounts;
            idReflectorDic["wuyuanhua"] = accounts;
            idReflectorDic["xichunhai"] = accounts;
            //阳泉
            accounts = new Accounts("yq_wangwendong", "yangquan");
            idReflectorDic["wangyafang"] = accounts;
            idReflectorDic["zhangjianen"] = accounts;
            idReflectorDic["songfeifan"] = accounts;
            idReflectorDic["baijiquan"] = accounts;
            idReflectorDic["dongshengqi"] = accounts;
            idReflectorDic["jiaopengfei"] = accounts;
            idReflectorDic["shihuajie"] = accounts;
            idReflectorDic["wangwendong"] = accounts;
            idReflectorDic["wangyepeng"] = accounts;
            idReflectorDic["wuxia"] = accounts;
            //长治
            accounts = new Accounts("cz_fengyaohui", "changzhi");
            idReflectorDic["yanxiaofang"] = accounts;
            idReflectorDic["fengyaohui"] = accounts;
            idReflectorDic["gaoyang"] = accounts;
            idReflectorDic["wangxi"] = accounts;
            idReflectorDic["xuebigang"] = accounts;
            idReflectorDic["wangshen"] = accounts;
            idReflectorDic["anzhenkun"] = accounts;
            idReflectorDic["zhangpenghao"] = accounts;
            idReflectorDic["lijinke"] = accounts;
            idReflectorDic["hanxiao"] = accounts;
            idReflectorDic["liziwu"] = accounts;
            idReflectorDic["gaotingting"] = accounts;
            idReflectorDic["hanfeng"] = accounts;
            idReflectorDic["niulin"] = accounts;
            idReflectorDic["sunwenhao"] = accounts;
            idReflectorDic["zhongjie"] = accounts;
            //晋城
            accounts = new Accounts("jc_tianliben", "jincheng");
            idReflectorDic["tianliben"] = accounts;
            idReflectorDic["huoluxia"] = accounts;
            idReflectorDic["fengshanshan"] = accounts;
            idReflectorDic["guanjunbo"] = accounts;
            idReflectorDic["litingting"] = accounts;
            idReflectorDic["lixuenan"] = accounts;
            idReflectorDic["niuboya"] = accounts;
            idReflectorDic["qincheng"] = accounts;
            idReflectorDic["qinxiangjun"] = accounts;
            idReflectorDic["shangxin"] = accounts;
            idReflectorDic["sunmin"] = accounts;
            idReflectorDic["zhangjun"] = accounts;
            //朔州
            accounts = new Accounts("sz_lihuadong", "shuozhou");
            idReflectorDic["guolijun"] = accounts;
            idReflectorDic["qiguohua"] = accounts;
            idReflectorDic["tangwendong"] = accounts;
            idReflectorDic["sunxiangzhen"] = accounts;
            idReflectorDic["yangchao"] = accounts;
            idReflectorDic["luzhanjun"] = accounts;
            idReflectorDic["guomeng"] = accounts;
            idReflectorDic["anyongdong"] = accounts;
            idReflectorDic["guohua"] = accounts;
            idReflectorDic["szzhanghaifeng"] = accounts;
            idReflectorDic["zhangyanhuang"] = accounts;
            idReflectorDic["zhaoyuan"] = accounts;
            //忻州
            accounts = new Accounts("xz_wangguopeng", "xinzhou");
            idReflectorDic["huangwei"] = accounts;
            idReflectorDic["wangjun"] = accounts;
            idReflectorDic["hanzhuying"] = accounts;
            idReflectorDic["xzhanbaojun"] = accounts;
            idReflectorDic["yanxiubin"] = accounts;
            idReflectorDic["jinkaichen"] = accounts;
            idReflectorDic["weiwentao"] = accounts;
            idReflectorDic["lixingzhong"] = accounts;
            idReflectorDic["wangqiusheng"] = accounts;
            idReflectorDic["xzjinxin"] = accounts;
            idReflectorDic["dingbin"] = accounts;
            idReflectorDic["gonghongli"] = accounts;
            idReflectorDic["qiaolongfeng"] = accounts;
            idReflectorDic["suxiaoxing"] = accounts;
            idReflectorDic["wangjia"] = accounts;
            idReflectorDic["xingyanting"] = accounts;
            idReflectorDic["yanfangjuan"] = accounts;
            idReflectorDic["yukai"] = accounts;
            //晋中
            accounts = new Accounts("jz_jinjiandong", "jinzhong");
            idReflectorDic["jzliujia"] = accounts;
            idReflectorDic["yangzhanlong"] = accounts;
            idReflectorDic["yangjinhong"] = accounts;
            idReflectorDic["zhangchengwei"] = accounts;
            idReflectorDic["gongjianling"] = accounts;
            idReflectorDic["danglirong"] = accounts;
            idReflectorDic["baiyuhong"] = accounts;
            idReflectorDic["cuiyongqiang"] = accounts;
            idReflectorDic["jinjiandong"] = accounts;
            idReflectorDic["lijuan"] = accounts;
            idReflectorDic["linweigong"] = accounts;
            idReflectorDic["masiwen"] = accounts;
            idReflectorDic["sunjian"] = accounts;
            idReflectorDic["wangyang"] = accounts;
            idReflectorDic["wuzhengrong"] = accounts;
            //吕梁
            accounts = new Accounts("ll_zhangdongdong", "lvliang");
            idReflectorDic["zhangdongdong"] = accounts;
            idReflectorDic["yanxiaolong"] = accounts;
            idReflectorDic["gaowenlou"] = accounts;
            idReflectorDic["lixiangqiang"] = accounts;
            idReflectorDic["liuguoqing"] = accounts;
            idReflectorDic["wangdianlong"] = accounts;
            idReflectorDic["wangxianjiao"] = accounts;
            idReflectorDic["bailifeng"] = accounts;
            idReflectorDic["baiyi"] = accounts;
            idReflectorDic["shihaiyan"] = accounts;
            idReflectorDic["zhaojing"] = accounts;
            //临汾
            accounts = new Accounts("lf_lvle", "linfen");
            idReflectorDic["wanghui"] = accounts;
            idReflectorDic["gaocong"] = accounts;
            idReflectorDic["haoxinghua"] = accounts;
            idReflectorDic["lvle"] = accounts;
            idReflectorDic["limin"] = accounts;
            idReflectorDic["yangzhimo"] = accounts;
            idReflectorDic["wangyun"] = accounts;
            idReflectorDic["zhangzhiwei"] = accounts;
            idReflectorDic["zhangshuangjie"] = accounts;
            idReflectorDic["zhanghuwei"] = accounts;
            idReflectorDic["zhaowenwei"] = accounts;
            idReflectorDic["guorui"] = accounts;
            idReflectorDic["hanyuehua"] = accounts;
            idReflectorDic["hejinqiang"] = accounts;
            idReflectorDic["qinxiaofeng"] = accounts;
            idReflectorDic["shantianmin"] = accounts;
            idReflectorDic["wangli"] = accounts;
            //运城
            accounts = new Accounts("yc_zhangbin", "yuncheng");
            idReflectorDic["fengxiaoye"] = accounts;
            idReflectorDic["wangxiaodong"] = accounts;
            idReflectorDic["ycliming"] = accounts;
            idReflectorDic["suwenqi"] = accounts;
            idReflectorDic["zhaoliyuncheng"] = accounts;
            idReflectorDic["zhangbin"] = accounts;
            idReflectorDic["wangzhuying"] = accounts;
            idReflectorDic["zhangyanhui"] = accounts;
            idReflectorDic["liuqing"] = accounts;
            idReflectorDic["chaipengke"] = accounts;
            idReflectorDic["jinqingze"] = accounts;
            idReflectorDic["lijunrong"] = accounts;
            idReflectorDic["liubinhua"] = accounts;
            idReflectorDic["liyunfeng"] = accounts;
            idReflectorDic["mayanni"] = accounts;
            idReflectorDic["wangchao"] = accounts;
            idReflectorDic["wufan"] = accounts;
            idReflectorDic["yangfan"] = accounts;
            
            //运城
            accounts = new Accounts("lihongri", "lihongri");
            idReflectorDic["liming"] = accounts;
            idReflectorDic["zhaihaiyun"] = accounts;
            idReflectorDic["liangxiujuan"] = accounts;
            idReflectorDic["yanghaijing"] = accounts;
            idReflectorDic["wangting"] = accounts;
            idReflectorDic["gaozhen"] = accounts;
            idReflectorDic["yaokun"] = accounts;
            idReflectorDic["guojuwei"] = accounts;
            idReflectorDic["guobao"] = accounts;
            idReflectorDic["liuwenji"] = accounts;
            idReflectorDic["yantao"] = accounts;
            idReflectorDic["changfeng"] = accounts;
            idReflectorDic["guopeng"] = accounts;
            idReflectorDic["xueshifeng"] = accounts;
            idReflectorDic["zhanghua"] = accounts;
            idReflectorDic["linwei"] = accounts;
            idReflectorDic["lijiangao"] = accounts;
            idReflectorDic["wangqi"] = accounts;

            if (idReflectorDic.TryGetValue(this.username, out accounts))
            {
                this.username = accounts.ID;
                this.password = accounts.Pw;
                MainModel.GetInstance().User.LoginName = accounts.ID;
                MainModel.GetInstance().User.Password = accounts.Pw;
            }
#endif
        }
#if ShanxiJin
        class Accounts
        {
            public Accounts(string id, string pw)
            {
                this.ID = id;
                this.Pw = pw;
            }
            public string ID { get; private set; }
            public string Pw { get; private set; }
        }
#endif
        public void Run()
        {
#if Ningxia
            MainModel.GetInstance().MainForm.changeDistrict(this.districtID);
#endif

            switch (commandType)
            {
                case SAICmdType.Empty:
#if Guangxi
                    if (districtID > 0)
                    {
                        GotoFromWithMap("Default");
                    }
#endif
                    break;
                case SAICmdType.ReplayFile:
                    replayFileByID();
                    break;
                case SAICmdType.ReplayFileByRect:
                    replayFileByRect();
                    break;
                case SAICmdType.ReplayFileByEvent:
                    replayFileByEvent();
                    break;
                case SAICmdType.ReplayFileByName:
                    replayFileByName();
                    break;
                case SAICmdType.RepalyFileByTask:
                    repalyFileByLowTask();
                    break;
                case SAICmdType.ReplayFileByIDMoMt:
                    replayFileMoMt();
                    break;
                case SAICmdType.QueryFileAll:
                    queryFileAll();
                    break;
                case SAICmdType.QueryBlackBlock:
                    queryBlackBlock();
                    break;
                case SAICmdType.OrderCompareReplay:
                    orderCompareReplay();
                    break;
                case SAICmdType.OrderESResultInfoQuery:
                    orderESResultQuery();
                    break;
                case SAICmdType.ReplayWeakcoverProblem:
                    break;
                case SAICmdType.ReplayFileByPointRadius:
                    replayFileByPointRadius();
                    break;
                case SAICmdType.SelectPage:
                    selectPage();
                    break;
                default:
                    break;
            }
        }

        private void selectPage()
        {//rams:\ -T30 -GVoLTE
            MainModel.GetInstance().MainForm.SelectRibbonPage(this.strValue);
        }

        /// <summary>
        /// 根据文件ID、地市ID和矩形区域回放文件
        /// </summary>
        private void replayFileByRect()
        {
            //rams:\* -T2 -F-文件ID -N-文件Log_file表名 -C-地市ID -S-业务类型 -X1-左上经度 -Y1-左上纬度 -X2-右下经度 -Y2-右下纬度
            FileInfo fileInfo = new FileInfo();
            fileInfo.ID = fileId;
            fileInfo.LogTable = logTable;
            fileInfo.DistrictID = districtID;
            fileInfo.ServiceType = serviceType;
            fileInfo.Name = "测试文件";
            MasterCom.RAMS.Model.Interface.FileReplayer.ReplayWhole(fileInfo);

            MasterCom.MTGis.DbRect rect = new MTGis.DbRect(this.iLTLng / 10000000.0
                , this.iLTLat / 10000000.0,
                this.iRBLng / 10000000.0, this.iRBLat / 10000000.0);
            MasterCom.MTGis.DbPoint point = rect.Center();
            MainModel.GetInstance().MainForm.GetMapForm().GoToView(point.x, point.y, 6000);
        }
        private void repalyFileByLowTask()
        {
            //rams:\* -T-6 -F-文件ID -A-文件开始时间 -Z-文件结束时间 -TVID-工单版本号ID -TID-工单ID
            //rams:\* -T-Ng== -F-MTk1NDQ1MTEzMw== -A-MjAxNy0xMi0xMyAxMjozMDoxOC4wMDA= -Z-MjAxNy0xMi0xMyAxMjo0Mzo0Mi4wMDA= -TVID-MTA= -TID-Nw==

            LowTaskInfoQuery lowTaskQuery = new LowTaskInfoQuery(TaskVersionId, TaskId);
            lowTaskQuery.Query();

#region 回放文件
            if (lowTaskQuery.LowTaskInfo != null)
            {
                districtID = DistrictManager.GetInstance().GetDistrictID(lowTaskQuery.LowTaskInfo.DistrictName);
            }

            if (districtID < 1)
            {
                districtID = 1;
            }
            QueryCondition condition = new QueryCondition();
            condition.DistrictID = districtID;
            condition.DistrictIDs.Clear();
            condition.DistrictIDs.Add(districtID);
            condition.FileName = fileId.ToString();
            condition.NameFilterType = FileFilterType.ByMark_ID;

            DateTime btime = JavaDate.GetDateTimeFromMilliseconds(startTime * 1000L);
            DateTime etime = JavaDate.GetDateTimeFromMilliseconds(endTime * 1000L);
            condition.Periods.Add(new TimePeriod(btime, etime));

            MainModel model = MainModel.GetInstance();
            model.MainForm.SetQueryConditionBySettings(condition);
            DIYQueryFileInfo query = new DIYQueryFileInfo(model);
            query.SetQueryCondition(condition);
            query.IsShowFileInfoForm = false;
            query.Query();

            if (model.FileInfos.Count > 0)
            {
                condition.FileInfos = model.FileInfos;
                DIYReplayFileQuery queryRplay = new DIYReplayFileQuery(model);
                queryRplay.SetQueryCondition(condition);
                queryRplay.Query();
            }
            else
            {
                MessageBox.Show("未查询到路测文件！");
            }
#endregion

#region 将规划图层绘制填充到地图上

            if (lowTaskQuery.LowTaskInfo != null)
            {
                List<DbGisId> dbGisIds = DbGisId.GetGisInfosByJsonData(lowTaskQuery.LowTaskInfo.PlanGisJsonData);
                if (dbGisIds != null && dbGisIds.Count > 0)
                {
                    List<MapWinGIS.Shape> selectedStreets = new List<MapWinGIS.Shape>();
                    List<ResvRegion> selectedResvRegions = new List<ResvRegion>();
                    getSelectedData(dbGisIds, selectedStreets, selectedResvRegions);

                    model.SearchGeometrys.SelectedStreets = selectedStreets;
                    model.SearchGeometrys.SelectedResvRegions = selectedResvRegions;
                    model.MainForm.GetMapForm().RefreshResvLayer();
                    if (selectedResvRegions.Count > 0)
                    {
                        model.MainForm.GetMapForm().searchGeometrysChanged();
                        model.MainForm.GetMapForm().GoToView(model.SearchGeometrys.RegionBounds);
                    }
                    else if (selectedStreets.Count > 0)
                    {
                        model.FireSearchGeometrysChanged(this);
                        model.MainForm.GetMapForm().GoToView(model.SearchGeometrys.Polygon.Bounds);
                    }

                }
                else
                {
                    MessageBox.Show("未查询到规划图层信息！");
                }
            }
            else
            {
                MessageBox.Show("未查询到工单信息！");
            }
#endregion
        }

        private void getSelectedData(List<DbGisId> dbGisIds, List<MapWinGIS.Shape> selectedStreets, List<ResvRegion> selectedResvRegions)
        {
            foreach (DbGisId dbGisId in dbGisIds)
            {
                TaskGisInfoQuery taskGisInfoQuery = new TaskGisInfoQuery(dbGisId);
                taskGisInfoQuery.Query();

                if (taskGisInfoQuery.DbPointList != null && taskGisInfoQuery.DbPointList.Count > 0)
                {
                    if (dbGisId.Type == -2)//路径
                    {
                        MapWinGIS.Shape shape = ShapeHelper.CreatePolylineShape(taskGisInfoQuery.DbPointList);
                        selectedStreets.Add(shape);
                    }
                    else if (dbGisId.Type == -1)//区域
                    {
                        MapWinGIS.Shape shape = ShapeHelper.CreatePolygonShape(taskGisInfoQuery.DbPointList);
                        selectedResvRegions.Add(new ResvRegion(shape, "规划区域" + dbGisId.Id));
                    }
                }
            }
        }

        private void orderESResultQuery()
        {//rams:\ -T21 -F（fileID） -N（问题点名称） -C地市ID（districtID） -S事件序号（eventSN） -A事件时间（问题点时间） -E（eventID）
         //base64加密形式：rams:\* -T-MjE= -F-LTEwNTM5MzA0MQ== -N-RlRQIERvd25sb2FkX+S9jumAn+eOh+i3r+autV8xMDBtMTBN -C-Mg== -S-NTk2Mzg1Nw== -A-MjAxNi8wNS8xOSAyMjowMToxNQ== -E-MTI0OQ==   
            ProcRoutineManager.Instance.Init();//初始化
            ProcRoutineManager2017.Instance.Init();//初始化
            string eventName = logTable;
            int eventSN = this.serviceType;
            DateTime date = JavaDate.GetDateTimeFromMilliseconds(startTime * 1000L);
            TaskEventItem taskItem = new TaskEventItem(eventName, this.districtID
                , this.fileId, this.eventIds[0], eventSN, date);
            ShowTaskESResult showResult = new ShowTaskESResult(taskItem, true);
            showResult.Query();
        }

        private void orderCompareReplay()
        {//rams:\ -T20 -F（fileID） -N（logTable） -C地市ID（districtID） -S（serviceType） -A（beginTime） -Z（endTime） -E（eventID） -G(OrderName)
            FileInfo fi = new FileInfo();
            fi.ID = fileId;
            fi.LogTable = logTable;
            fi.DistrictID = districtID;
            fi.ServiceType = serviceType;
            string taskName = this.strValue;
            TimePeriod period = new TimePeriod(
               JavaDate.GetDateTimeFromMilliseconds(this.startTime * 1000L)
               , JavaDate.GetDateTimeFromMilliseconds(this.endTime * 1000L));

            MasterCom.RAMS.NOP.TaskFileCompareReplay replay = new MasterCom.RAMS.NOP.TaskFileCompareReplay(fi, period, taskName);
            replay.Query();
            QueryCondition cond = replay.GetQueryCondition();

            if (cond.FileInfos.Count > 2)
            {
                foreach (FileInfo f in cond.FileInfos)
                {
                    if (f.ID != fi.ID)
                    {//多个验证文件时，只呈现主叫文件
                        if (f.Momt == (int)MoMtFile.MtFlag || f.Name.Contains("被叫"))
                        {
                            MainModel.GetInstance().VisibleOffsetManager.SetFileVisible(f.ID, false);
                        }
                        else
                        {
                            MainModel.GetInstance().VisibleOffsetManager.SetFileOffset(f.ID, 20, 20);
                        }
                    }
                }
            }
            else if (cond.FileInfos.Count == 2)
            {
                MainModel.GetInstance().VisibleOffsetManager.SetFileOffset(cond.FileInfos[1].ID, 20, 20);
            }
            else
            {
                return;
            }

            DataTable table = getTable(taskName);
            KPIForm form = MainModel.GetInstance().CreateResultForm(typeof(KPIForm)) as KPIForm;
            form.FillData(table);
            form.Visible = true;
            form.BringToFront();
            MainModel.GetInstance().MainForm.GetMapForm().updateMap();
        }

        private static DataTable getTable(string taskName)
        {
            DataTable table = null;
            if (taskName.Contains("CSFB"))
            {
                table = KPIStater.StatCSFB(MainModel.GetInstance().DTDataManager.FileDataManagers);
            }
            else if (taskName.Contains("SCAN"))
            {
                table = KPIStater.StatLTEScan(MainModel.GetInstance().DTDataManager.FileDataManagers);
            }
            else if (taskName.Contains("VOLTE"))
            {
                table = KPIStater.StatVoLTE(MainModel.GetInstance().DTDataManager.FileDataManagers);
            }
            else
            {
                table = KPIStater.StatLTEData(MainModel.GetInstance().DTDataManager.FileDataManagers);
            }

            return table;
        }

        //根据文件ID和LogTable回放文件
        private void replayFileByID()
        {//rams:\ -T1 -F文件ID -N文件Log_file表名 -C地市ID -S业务类型 -A开始时间（整型） -Z结束时间（整型）
            FileInfo fileInfo = new FileInfo();
            fileInfo.ID = fileId;
            fileInfo.LogTable = LogTable;
            fileInfo.DistrictID = districtID;
            fileInfo.ServiceType = serviceType;
            fileInfo.Name = "测试文件";
            //默认不限制时间
            DateTime begin = JavaDate.GetDateTimeFromMilliseconds(this.startTime * 1000L);
            int iend = this.endTime == 0 ? 0x7fffffff : this.endTime;
            DateTime end = JavaDate.GetDateTimeFromMilliseconds(iend * 1000L);
            TimePeriod period = new TimePeriod(begin, end);
            MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(
                   fileInfo, period);
        }

        private void replayFileByEvent()
        {//rams:\ -T3 -F文件ID -N文件Log_file表名 -C地市ID -S业务类型 -A开始时间（整型） -Z结束时间（整型）
         //rams:\* -T-Mg== -F-base64编码的文件ID -C-base64编码的地市ID -A-base64编码的整型开始时间 -Z-base64编码的整型开始时间
            QueryCondition condition = new QueryCondition();
            FileInfo fileInfo = new FileInfo();
            fileInfo.Name = "测试文件";
            fileInfo.ID = fileId;
            fileInfo.LogTable = LogTable;
            condition.DistrictID = districtID;
            condition.DistrictIDs.Add(districtID);
            condition.FileInfos.Add(fileInfo);
            DateTime dtBegin = JavaDate.GetDateTimeFromMilliseconds(startTime * 1000L);
            DateTime dtEnd = dtBegin.AddMinutes(1);
            if (endTime != 0)
            {
                dtEnd = JavaDate.GetDateTimeFromMilliseconds(endTime * 1000L);
            }
            if (ReservedParam != null && ReservedParam.StartsWith("A"))
            {//A60Z60
                int zIdx = ReservedParam.IndexOf("Z");
                int sec;
                if (int.TryParse(ReservedParam.Substring(zIdx + 1), out sec))
                {
                    dtEnd = dtBegin.AddSeconds(sec);
                }
                if (int.TryParse(ReservedParam.Substring(1, zIdx - 1), out sec))
                {
                    dtBegin = dtBegin.AddSeconds(-1 * sec);
                }
            }
            condition.Periods.Add(new TimePeriod(dtBegin, dtEnd));

            MasterCom.RAMS.Net.DIYReplayFileWithinPeriodQueryByCommand query =
                new MasterCom.RAMS.Net.DIYReplayFileWithinPeriodQueryByCommand(MainModel.GetInstance());
            if (ReservedParam == "1")
            {
                setReplayEventFilter(query);
            }
            query.SetQueryCondition(condition);
            query.Query();
        }

        private void setReplayEventFilter(DIYReplayFileWithinPeriodQueryByCommand query)
        {
            query.ReplayEventFilter = delegate (Event evt)
            {
                foreach (int id in eventIds)
                {
                    if (id == evt.ID
                            && (startTime2 == 0 || startTime2 == evt.Time)
                            && (Lac == 0 || Lac == (int)evt["LAC"])
                            && (Ci == 0 || Ci == (int)evt["CI"]))
                    {
                        evt.Selected = true;
                        MainModel.GetInstance().SelectedEvents.Add(evt);
                        return false;
                    }
                }
                return true;
            };
        }

        private void replayFileByName()
        {
            QueryCondition cond = new QueryCondition();
            cond.FileName = LogTable;
            cond.FileNameOrNum = 1;
            TimePeriod period = new TimePeriod(JavaDate.GetDateTimeFromMilliseconds(
                startTime * 1000L), DateTime.Now);
            cond.Periods.Add(period);
            cond.DistrictID = districtID;
            cond.DistrictIDs.Add(districtID);
            MainModel model = MainModel.GetInstance();
            DIYQueryFileInfo queryCheckFile = new DIYQueryFileInfo(model);
            queryCheckFile.IsShowFileInfoForm = false;
            queryCheckFile.SetQueryCondition(cond);
            queryCheckFile.Query();
            FileInfo checkFile = null;
            if (model.FileInfos.Count > 0)
            {
                checkFile = model.FileInfos[model.FileInfos.Count - 1];
                cond.FileInfos.Add(checkFile);
            }

            DIYReplayFileQuery query = new DIYReplayFileQuery(model);
            query.SetQueryCondition(cond);
            query.Query();
        }

        private void queryBlackBlock()
        {
            Dictionary<string, BlackBlockToken> tokenDic = QueryNewBlackBlock.GetTokenDic();
            BlackBlockToken token;
            if (!tokenDic.TryGetValue(LogTable, out token))
            {
                MessageBox.Show(string.Format("当前地市ID:{0}，不存在Token为{1}的黑点信息！"
                    , districtID, LogTable));
                return;
            }
            MainModel model = MainModel.GetInstance();
            int oldID = model.DistrictID;
            model.DistrictID = districtID;
            NewBlockQueryCond cond = new NewBlockQueryCond();
            for (int i = 0; i <= 5; i++)
            {//查询所有状态的黑点
                cond.statusMap[i] = true;
            }
            cond.dateValue = DateTime.Now;
            cond.blockid = fileId;
            cond.TokenName = token.Token;
            cond.Token = token;

            model.DrawEventDateLbl = true;
            model.EventDateLblTxtFormat = "yy年MM月dd日";
            QueryNewBlackBlock qryBb = new QueryNewBlackBlock(model);
            qryBb.FillCondition(false, cond);
            qryBb.Query();

            int fileID = -1;
            Event evt = null;
            foreach (BlackBlockItem bb in model.CurBlackBlockList)
            {
                if (fileID == -1 && bb.AbEvents.Count > 0)
                {
                    evt = bb.AbEvents[bb.AbEvents.Count - 1].ConvertToEvent();
                    fileID = evt.FileID;
                    DateTime dtBegin = JavaDate.GetDateTimeFromMilliseconds(evt.Time * 1000L).AddMinutes(-3);
                    DateTime dtEnd = dtBegin.AddMinutes(6);
                    FileReplayer.ReplayOnePart(evt, new TimePeriod(dtBegin, dtEnd));
                }
                foreach (BlockEventItem item in bb.AbEvents)
                {
                    if (item.file_id != fileID)
                    {
                        model.DTDataManager.Add(item.ConvertToEvent());
                    }
                }
            }
            model.FireDTDataChanged(this);
            if (evt != null)
            {
                model.MainForm.GetMapForm().GoToView(evt.Longitude, evt.Latitude, 4000);
            }

            model.DistrictID = oldID;

        }

        private void replayFileMoMt()
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = QueryType.Depth;
            FileInfo fileInfo = new FileInfo();
            fileInfo.Name = "测试文件";
            fileInfo.ProjectID = projId;
            fileInfo.ServiceType = serviceType;
            fileInfo.ID = fileId;
            fileInfo.LogTable = LogTable;
            condition.FileInfos.Add(fileInfo);
            int pre = 3;
            int next = 1;
            DateTime dtime = JavaDate.GetDateTimeFromMilliseconds(startTime * 1000L);
            condition.Periods.Add(new TimePeriod(dtime.AddMinutes(-pre), dtime.AddMinutes(next)));
            condition.isCompareMode = true;

            ReplayFileWithinCompareByCommand query = new ReplayFileWithinCompareByCommand(MainModel.GetInstance());
            query.SetQueryCondition(condition);
            query.Query();
        }

        private void queryFileAll()
        {
            QueryCondition condition = new QueryCondition();
            condition.Projects.Add(projId);
            condition.ServiceTypes.Add(serviceType);
            DateTime btime = JavaDate.GetDateTimeFromMilliseconds(startTime * 1000L);
            DateTime etime = JavaDate.GetDateTimeFromMilliseconds(endTime * 1000L);
            condition.Periods.Add(new TimePeriod(btime, etime));
            condition.CarrierTypes.Add(1);
            DIYQueryFileInfo query = new DIYQueryFileInfo(MainModel.GetInstance());
            query.SetQueryCondition(condition);
            query.Query();
        }

        private void replayFileByPointRadius()
        {
            //rams:\* -T-操作类型ID -C-地市ID -A-开始时间(整型或标准时间格式) -Z-结束时间 -X0-经度 -Y0-纬度 -R-半径(米)
            //rams:\* -T-MjY= -C-MQ== -A-MTUyNzgyMTMwNA== -Z-MTUyNzgyMTU5NA== -X0-MTIxNDAyMDEwMA== -Y0-MzEzMjg0MDE2 -R-MzAw
            //rams:\* -T-MjY= -C-5rW35Lic -A-MjAxOC82LzE5IDE1OjE0OjM0 -Z-MjAxOC83LzE5IDE1OjE0OjM0 -X0-MTAyLjMzMzM1NQ== -Y0-MzMuNDU2Nzg4 -R-MzAw

            if (dCntLng < 73.33 || dCntLng > 135.05 || dCntLat < 3.51 || dCntLat > 53.33 || districtID == 0)
            {
                MessageBox.Show("查询参数异常！");
                return;
            }

            MainModel model = MainModel.GetInstance();
            QueryCondition condition = new QueryCondition();
            condition.DistrictID = districtID;
            condition.DistrictIDs.Clear();
            condition.DistrictIDs.Add(districtID);

            DateTime btime = JavaDate.GetDateTimeFromMilliseconds(startTime * 1000L);
            DateTime etime = JavaDate.GetDateTimeFromMilliseconds(endTime * 1000L);
            condition.Periods.Add(new TimePeriod(btime, etime));

            double dRadius = iRadius / 100000.0;//米粗略换算成经纬度
            condition.Geometorys = new SearchGeometrys();
            condition.Geometorys.Region = MasterCom.MTGis.ShapeHelper.CreateCircleShape(dCntLng, dCntLat, dRadius);

            model.MainForm.SetQueryConditionBySettings(condition);
            model.SearchGeometrys.SelectedResvRegions = new List<ResvRegion>();
            model.SearchGeometrys.SelectedResvRegions.Add(condition.Geometorys.RegionInfo);
            model.MainForm.GetMapForm().RefreshResvLayer();
            model.MainForm.GetMapForm().searchGeometrysChanged();

            DIYQueryFileInfoByRegion query = new DIYQueryFileInfoByRegion(model);
            query.SetQueryCondition(condition);
            query.IsShowFileInfoForm = false;
            query.Query();

            if (model.FileInfos.Count > 0)
            {
                condition.FileInfos = model.FileInfos;
                DIYReplayFileQuery queryRplay = new DIYReplayFileQuery(model);
                queryRplay.SetQueryCondition(condition);
                queryRplay.Query();
            }
            else
            {
                MessageBox.Show("未查询到符合条件的路测文件！");
                model.MainForm.GetMapForm().GoToView(dCntLng, dCntLat, 5000);
            }
        }
    }

    public enum SAICmdType
    {
        Empty = 0,
        ReplayFile = 1,
        ReplayFileByRect = 2,
        ReplayFileByEvent = 3,
        QueryBlackBlock = 4,
        ReplayFileByName = 5,
        RepalyFileByTask = 6,
        ReplayFileByIDMoMt = 11,
        QueryFileAll = 12,

        OrderCompareReplay = 20,
        OrderESResultInfoQuery = 21,

        ReplayWeakcoverProblem = 25,     ////新疆MR数据分析GIS呈现
        ReplayFileByPointRadius = 26,   //青海以规划站点为中心，X米为半径查询回放文件

        SelectPage = 30,
    }

}
