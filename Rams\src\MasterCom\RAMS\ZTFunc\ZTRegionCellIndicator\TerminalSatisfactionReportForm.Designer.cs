﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TerminalSatisfactionReportForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TerminalSatisfactionReportForm));
            this.gpbCondition = new System.Windows.Forms.GroupBox();
            this.btnQuery = new System.Windows.Forms.Button();
            this.cbxQuestionType = new System.Windows.Forms.ComboBox();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxCity = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.cbxMonth = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.gpbResult = new System.Windows.Forms.GroupBox();
            this.gvResult = new System.Windows.Forms.DataGridView();
            this.gvcCity = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcMonth = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcTerminal = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcNetworkCover = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcNetworkQuality = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcMobileInternet = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvbVoiceCall = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cmsGrid = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.menuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.gpbCondition.SuspendLayout();
            this.gpbResult.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvResult)).BeginInit();
            this.cmsGrid.SuspendLayout();
            this.SuspendLayout();
            // 
            // gpbCondition
            // 
            this.gpbCondition.Controls.Add(this.btnQuery);
            this.gpbCondition.Controls.Add(this.cbxQuestionType);
            this.gpbCondition.Controls.Add(this.label2);
            this.gpbCondition.Controls.Add(this.cbxCity);
            this.gpbCondition.Controls.Add(this.label3);
            this.gpbCondition.Controls.Add(this.cbxMonth);
            this.gpbCondition.Controls.Add(this.label1);
            this.gpbCondition.Dock = System.Windows.Forms.DockStyle.Top;
            this.gpbCondition.Location = new System.Drawing.Point(0, 0);
            this.gpbCondition.Name = "gpbCondition";
            this.gpbCondition.Size = new System.Drawing.Size(1059, 56);
            this.gpbCondition.TabIndex = 0;
            this.gpbCondition.TabStop = false;
            this.gpbCondition.Text = "统计条件";
            // 
            // btnQuery
            // 
            this.btnQuery.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnQuery.Location = new System.Drawing.Point(958, 19);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(87, 27);
            this.btnQuery.TabIndex = 3;
            this.btnQuery.Text = "查询";
            this.btnQuery.UseVisualStyleBackColor = true;
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // cbxQuestionType
            // 
            this.cbxQuestionType.FormattingEnabled = true;
            this.cbxQuestionType.Items.AddRange(new object[] {
            "全部"});
            this.cbxQuestionType.Location = new System.Drawing.Point(580, 19);
            this.cbxQuestionType.Name = "cbxQuestionType";
            this.cbxQuestionType.Size = new System.Drawing.Size(168, 22);
            this.cbxQuestionType.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(497, 23);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(67, 14);
            this.label2.TabIndex = 1;
            this.label2.Text = "问卷类型：";
            // 
            // cbxCity
            // 
            this.cbxCity.FormattingEnabled = true;
            this.cbxCity.Items.AddRange(new object[] {
            "2013年10月"});
            this.cbxCity.Location = new System.Drawing.Point(62, 19);
            this.cbxCity.Name = "cbxCity";
            this.cbxCity.Size = new System.Drawing.Size(168, 22);
            this.cbxCity.TabIndex = 2;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(7, 23);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(43, 14);
            this.label3.TabIndex = 1;
            this.label3.Text = "地市：";
            // 
            // cbxMonth
            // 
            this.cbxMonth.FormattingEnabled = true;
            this.cbxMonth.Items.AddRange(new object[] {
            "2013年10月"});
            this.cbxMonth.Location = new System.Drawing.Point(305, 19);
            this.cbxMonth.Name = "cbxMonth";
            this.cbxMonth.Size = new System.Drawing.Size(168, 22);
            this.cbxMonth.TabIndex = 2;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(250, 23);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(43, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "月份：";
            // 
            // gpbResult
            // 
            this.gpbResult.Controls.Add(this.gvResult);
            this.gpbResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gpbResult.Location = new System.Drawing.Point(0, 56);
            this.gpbResult.Name = "gpbResult";
            this.gpbResult.Size = new System.Drawing.Size(1059, 441);
            this.gpbResult.TabIndex = 1;
            this.gpbResult.TabStop = false;
            this.gpbResult.Text = "统计结果";
            // 
            // gvResult
            // 
            this.gvResult.AllowUserToAddRows = false;
            this.gvResult.AllowUserToDeleteRows = false;
            this.gvResult.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.gvResult.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.gvcCity,
            this.gvcMonth,
            this.gvcTerminal,
            this.gvcNetworkCover,
            this.gvcNetworkQuality,
            this.gvcMobileInternet,
            this.gvbVoiceCall});
            this.gvResult.ContextMenuStrip = this.cmsGrid;
            this.gvResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gvResult.Location = new System.Drawing.Point(3, 18);
            this.gvResult.Name = "gvResult";
            this.gvResult.ReadOnly = true;
            this.gvResult.RowTemplate.Height = 23;
            this.gvResult.Size = new System.Drawing.Size(1053, 420);
            this.gvResult.TabIndex = 0;
            // 
            // gvcCity
            // 
            this.gvcCity.DataPropertyName = "City";
            this.gvcCity.FillWeight = 29.82234F;
            this.gvcCity.HeaderText = "地市";
            this.gvcCity.Name = "gvcCity";
            this.gvcCity.ReadOnly = true;
            this.gvcCity.Width = 120;
            // 
            // gvcMonth
            // 
            this.gvcMonth.DataPropertyName = "Month";
            this.gvcMonth.FillWeight = 29.82234F;
            this.gvcMonth.HeaderText = "月份";
            this.gvcMonth.Name = "gvcMonth";
            this.gvcMonth.ReadOnly = true;
            // 
            // gvcTerminal
            // 
            this.gvcTerminal.DataPropertyName = "TerminalType";
            this.gvcTerminal.FillWeight = 29.82234F;
            this.gvcTerminal.HeaderText = "终端类型";
            this.gvcTerminal.Name = "gvcTerminal";
            this.gvcTerminal.ReadOnly = true;
            this.gvcTerminal.Width = 120;
            // 
            // gvcNetworkCover
            // 
            this.gvcNetworkCover.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.gvcNetworkCover.DataPropertyName = "NetworkCover";
            this.gvcNetworkCover.HeaderText = "网络覆盖良好占比";
            this.gvcNetworkCover.Name = "gvcNetworkCover";
            this.gvcNetworkCover.ReadOnly = true;
            // 
            // gvcNetworkQuality
            // 
            this.gvcNetworkQuality.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.gvcNetworkQuality.DataPropertyName = "NetworkQuality";
            this.gvcNetworkQuality.HeaderText = "网络质量良好占比";
            this.gvcNetworkQuality.Name = "gvcNetworkQuality";
            this.gvcNetworkQuality.ReadOnly = true;
            // 
            // gvcMobileInternet
            // 
            this.gvcMobileInternet.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.gvcMobileInternet.DataPropertyName = "MobileInternet";
            this.gvcMobileInternet.HeaderText = "手机上网良好占比";
            this.gvcMobileInternet.Name = "gvcMobileInternet";
            this.gvcMobileInternet.ReadOnly = true;
            // 
            // gvbVoiceCall
            // 
            this.gvbVoiceCall.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.gvbVoiceCall.DataPropertyName = "VoiceCall";
            this.gvbVoiceCall.HeaderText = "语音通话良好占比";
            this.gvbVoiceCall.Name = "gvbVoiceCall";
            this.gvbVoiceCall.ReadOnly = true;
            // 
            // cmsGrid
            // 
            this.cmsGrid.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.menuItemExport});
            this.cmsGrid.Name = "cmsGrid";
            this.cmsGrid.Size = new System.Drawing.Size(153, 48);
            // 
            // menuItemExport
            // 
            this.menuItemExport.Image = global::MasterCom.RAMS.Properties.Resources.xls;
            this.menuItemExport.Name = "menuItemExport";
            this.menuItemExport.Size = new System.Drawing.Size(152, 22);
            this.menuItemExport.Text = "导出Excel";
            this.menuItemExport.Click += new System.EventHandler(this.menuItemExport_Click);
            // 
            // TerminalSatisfactionReportForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1059, 497);
            this.Controls.Add(this.gpbResult);
            this.Controls.Add(this.gpbCondition);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "TerminalSatisfactionReportForm";
            this.Text = "用户终端满意度统计";
            this.gpbCondition.ResumeLayout(false);
            this.gpbCondition.PerformLayout();
            this.gpbResult.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvResult)).EndInit();
            this.cmsGrid.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox gpbCondition;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ComboBox cbxMonth;
        private System.Windows.Forms.ComboBox cbxQuestionType;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnQuery;
        private System.Windows.Forms.GroupBox gpbResult;
        private System.Windows.Forms.DataGridView gvResult;
        private System.Windows.Forms.ComboBox cbxCity;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcCity;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcMonth;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcTerminal;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcNetworkCover;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcNetworkQuality;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcMobileInternet;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvbVoiceCall;
        private System.Windows.Forms.ContextMenuStrip cmsGrid;
        private System.Windows.Forms.ToolStripMenuItem menuItemExport;
    }
}