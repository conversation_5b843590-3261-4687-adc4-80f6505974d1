﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTGSMMemoryProblemInfo
    {
        public ZTGSMMemoryProblemInfo(TestPoint originalTestPoint)
        {
            this.FileName = originalTestPoint.FileName;
            this.testPointCount = 0;
            this.ProblemTestPointCount = 0;
            this.OtestPoint = originalTestPoint;
            this.MemoryProblemCellInfos = new List<MemoryProblemCellInfo>();
            this.Distance = 0;
        }

        public void addProblemTestPoint(TestPoint problemTestPoint, int num)
        {
            MemoryProblemCellInfo info = new MemoryProblemCellInfo(problemTestPoint, num);
            MemoryProblemCellInfos.Add(info);
            this.ProblemTestPointCount += 1;
        }       

        public TestPoint OtestPoint { get; set; }

        public string FileName { get; set; }

        public double Distance { get; set; }

        public int testPointCount { get; set; }

        public int ProblemTestPointCount { get; set; }

        public List<MemoryProblemCellInfo> MemoryProblemCellInfos { get; set; }


        public class MemoryProblemCellInfo
        {
            public MemoryProblemCellInfo(TestPoint problemTestPoint, int num)
            {
                this.ProblemTestPoint = problemTestPoint;
                this.BCNum = num;
            }

            public TestPoint ProblemTestPoint { get; set; }

            public int BCNum { get; set; }
        }
    }

    public class FileForGSMMemoryProblem
    {
        public string FileName { get; set; }

        public List<ZTGSMMemoryProblemInfo> ProblemInfos { get; set; }

        public FileForGSMMemoryProblem(List<ZTGSMMemoryProblemInfo> problemInfos)
        {
            this.FileName = problemInfos[0].FileName;
            this.ProblemInfos = problemInfos;
        }
    }
}


