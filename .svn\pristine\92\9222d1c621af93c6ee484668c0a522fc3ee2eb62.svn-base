﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SetThresholdForm : Form
    {
        public SetThresholdForm()
        {
            InitializeComponent();
        }

        public void GetSettingRxlev(out int unCoveredRexlev,out int weakCoveredRxlev)
        {
            unCoveredRexlev = (int)numUDUnRxlevThreshold.Value;
            weakCoveredRxlev = (int)numUDWkRxlevThreshold.Value;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void numUDWkRxlevThreshold_ValueChanged(object sender, EventArgs e)
        {
            if (numUDUnRxlevThreshold.Value>=numUDWkRxlevThreshold.Value)
            {
                numUDWkRxlevThreshold.Value = numUDUnRxlevThreshold.Value + 1;
                MessageBox.Show("设置信号的弱覆盖电平门限必须高于无覆盖电平门限！");
            }
        }

        private void numUDUnRxlevThreshold_ValueChanged(object sender, EventArgs e)
        {
            if (numUDUnRxlevThreshold.Value>=numUDWkRxlevThreshold.Value)
            {
                numUDUnRxlevThreshold.Value = numUDWkRxlevThreshold.Value - 1;
                MessageBox.Show("设置信号的无覆盖电平门限必须低于弱覆盖电平门限！");
            }
        }
    }

}
