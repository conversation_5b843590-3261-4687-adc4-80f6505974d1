﻿namespace MasterCom.RAMS.Func.PerformanceParam
{
    partial class PerformanceParamNewForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabControl = new System.Windows.Forms.TabControl();
            this.tabPageCellInfo = new System.Windows.Forms.TabPage();
            this.tabPageTrafficInfo = new System.Windows.Forms.TabPage();
            this.tabPageGPRSInfo = new System.Windows.Forms.TabPage();
            this.ctxCellInfo2Excel = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.Export2ExcelToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dgvTrafficInfo = new System.Windows.Forms.DataGridView();
            this.dgvGPRSInfo = new System.Windows.Forms.DataGridView();
            this.dgvCellInfo = new System.Windows.Forms.DataGridView();
            this.ctxTrafficInfo2Excel = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.ctxGPRSInfo2Excel = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.tabControl.SuspendLayout();
            this.tabPageCellInfo.SuspendLayout();
            this.tabPageTrafficInfo.SuspendLayout();
            this.tabPageGPRSInfo.SuspendLayout();
            this.ctxCellInfo2Excel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvTrafficInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvGPRSInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCellInfo)).BeginInit();
            this.ctxTrafficInfo2Excel.SuspendLayout();
            this.ctxGPRSInfo2Excel.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl
            // 
            this.tabControl.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl.Controls.Add(this.tabPageCellInfo);
            this.tabControl.Controls.Add(this.tabPageTrafficInfo);
            this.tabControl.Controls.Add(this.tabPageGPRSInfo);
            this.tabControl.Location = new System.Drawing.Point(2, 2);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new System.Drawing.Size(867, 479);
            this.tabControl.TabIndex = 0;
            // 
            // tabPageCellInfo
            // 
            this.tabPageCellInfo.Controls.Add(this.dgvCellInfo);
            this.tabPageCellInfo.Location = new System.Drawing.Point(4, 21);
            this.tabPageCellInfo.Name = "tabPageCellInfo";
            this.tabPageCellInfo.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageCellInfo.Size = new System.Drawing.Size(859, 454);
            this.tabPageCellInfo.TabIndex = 0;
            this.tabPageCellInfo.Text = "小区基本信息";
            this.tabPageCellInfo.UseVisualStyleBackColor = true;
            // 
            // tabPageTrafficInfo
            // 
            this.tabPageTrafficInfo.Controls.Add(this.dgvTrafficInfo);
            this.tabPageTrafficInfo.Location = new System.Drawing.Point(4, 21);
            this.tabPageTrafficInfo.Name = "tabPageTrafficInfo";
            this.tabPageTrafficInfo.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageTrafficInfo.Size = new System.Drawing.Size(859, 454);
            this.tabPageTrafficInfo.TabIndex = 1;
            this.tabPageTrafficInfo.Text = "话务性能数据";
            this.tabPageTrafficInfo.UseVisualStyleBackColor = true;
            // 
            // tabPageGPRSInfo
            // 
            this.tabPageGPRSInfo.Controls.Add(this.dgvGPRSInfo);
            this.tabPageGPRSInfo.Location = new System.Drawing.Point(4, 21);
            this.tabPageGPRSInfo.Name = "tabPageGPRSInfo";
            this.tabPageGPRSInfo.Size = new System.Drawing.Size(859, 454);
            this.tabPageGPRSInfo.TabIndex = 2;
            this.tabPageGPRSInfo.Text = "GPRS数据";
            this.tabPageGPRSInfo.UseVisualStyleBackColor = true;
            // 
            // ctxCellInfo2Excel
            // 
            this.ctxCellInfo2Excel.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.Export2ExcelToolStripMenuItem});
            this.ctxCellInfo2Excel.Name = "contextMenuStrip";
            this.ctxCellInfo2Excel.Size = new System.Drawing.Size(155, 26);
            // 
            // Export2ExcelToolStripMenuItem
            // 
            this.Export2ExcelToolStripMenuItem.Name = "Export2ExcelToolStripMenuItem";
            this.Export2ExcelToolStripMenuItem.Size = new System.Drawing.Size(154, 22);
            this.Export2ExcelToolStripMenuItem.Text = "导出到Excel...";
            this.Export2ExcelToolStripMenuItem.Click += new System.EventHandler(this.Export2ExcelToolStripMenuItem_Click);
            // 
            // dgvTrafficInfo
            // 
            this.dgvTrafficInfo.AllowUserToAddRows = false;
            this.dgvTrafficInfo.AllowUserToDeleteRows = false;
            this.dgvTrafficInfo.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dgvTrafficInfo.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvTrafficInfo.ContextMenuStrip = this.ctxTrafficInfo2Excel;
            this.dgvTrafficInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvTrafficInfo.Location = new System.Drawing.Point(3, 3);
            this.dgvTrafficInfo.Name = "dgvTrafficInfo";
            this.dgvTrafficInfo.ReadOnly = true;
            this.dgvTrafficInfo.RowTemplate.Height = 23;
            this.dgvTrafficInfo.Size = new System.Drawing.Size(853, 448);
            this.dgvTrafficInfo.TabIndex = 1;
            // 
            // dgvGPRSInfo
            // 
            this.dgvGPRSInfo.AllowUserToAddRows = false;
            this.dgvGPRSInfo.AllowUserToDeleteRows = false;
            this.dgvGPRSInfo.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dgvGPRSInfo.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvGPRSInfo.ContextMenuStrip = this.ctxGPRSInfo2Excel;
            this.dgvGPRSInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvGPRSInfo.Location = new System.Drawing.Point(0, 0);
            this.dgvGPRSInfo.Name = "dgvGPRSInfo";
            this.dgvGPRSInfo.ReadOnly = true;
            this.dgvGPRSInfo.RowTemplate.Height = 23;
            this.dgvGPRSInfo.Size = new System.Drawing.Size(859, 454);
            this.dgvGPRSInfo.TabIndex = 2;
            // 
            // dgvCellInfo
            // 
            this.dgvCellInfo.AllowUserToAddRows = false;
            this.dgvCellInfo.AllowUserToDeleteRows = false;
            this.dgvCellInfo.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dgvCellInfo.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvCellInfo.ContextMenuStrip = this.ctxCellInfo2Excel;
            this.dgvCellInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvCellInfo.Location = new System.Drawing.Point(3, 3);
            this.dgvCellInfo.Name = "dgvCellInfo";
            this.dgvCellInfo.ReadOnly = true;
            this.dgvCellInfo.RowTemplate.Height = 23;
            this.dgvCellInfo.Size = new System.Drawing.Size(853, 448);
            this.dgvCellInfo.TabIndex = 2;
            // 
            // ctxTrafficInfo2Excel
            // 
            this.ctxTrafficInfo2Excel.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem1});
            this.ctxTrafficInfo2Excel.Name = "contextMenuStrip";
            this.ctxTrafficInfo2Excel.Size = new System.Drawing.Size(155, 26);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(154, 22);
            this.toolStripMenuItem1.Text = "导出到Excel...";
            this.toolStripMenuItem1.Click += new System.EventHandler(this.toolStripMenuItem1_Click);
            // 
            // ctxGPRSInfo2Excel
            // 
            this.ctxGPRSInfo2Excel.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem2});
            this.ctxGPRSInfo2Excel.Name = "contextMenuStrip";
            this.ctxGPRSInfo2Excel.Size = new System.Drawing.Size(155, 48);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(154, 22);
            this.toolStripMenuItem2.Text = "导出到Excel...";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.toolStripMenuItem2_Click);
            // 
            // PerformanceParamNewForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(866, 477);
            this.Controls.Add(this.tabControl);
            this.Name = "PerformanceParamNewForm";
            this.Text = "性能参数";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.PerformanceParamNewForm_FormClosing);
            this.tabControl.ResumeLayout(false);
            this.tabPageCellInfo.ResumeLayout(false);
            this.tabPageTrafficInfo.ResumeLayout(false);
            this.tabPageGPRSInfo.ResumeLayout(false);
            this.ctxCellInfo2Excel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvTrafficInfo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvGPRSInfo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCellInfo)).EndInit();
            this.ctxTrafficInfo2Excel.ResumeLayout(false);
            this.ctxGPRSInfo2Excel.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl;
        private System.Windows.Forms.TabPage tabPageCellInfo;
        private System.Windows.Forms.TabPage tabPageTrafficInfo;
        private System.Windows.Forms.TabPage tabPageGPRSInfo;
        private System.Windows.Forms.ContextMenuStrip ctxCellInfo2Excel;
        private System.Windows.Forms.ToolStripMenuItem Export2ExcelToolStripMenuItem;
        private System.Windows.Forms.DataGridView dgvTrafficInfo;
        private System.Windows.Forms.DataGridView dgvGPRSInfo;
        private System.Windows.Forms.DataGridView dgvCellInfo;
        private System.Windows.Forms.ContextMenuStrip ctxTrafficInfo2Excel;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
        private System.Windows.Forms.ContextMenuStrip ctxGPRSInfo2Excel;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
    }
}