﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public static class ReplayFileManager
    {
        public static void ReplayFiles(List<FileInfo> files)
        {
            QueryCondition condition = new QueryCondition();
            foreach (FileInfo fileInfo in files)
            {
                condition.FileInfos.Add(fileInfo);
                if (fileInfo.DistrictID > 0)
                {
                    condition.DistrictID = fileInfo.DistrictID;
                }
                if (!condition.DistrictIDs.Contains(fileInfo.DistrictID))
                {
                    condition.DistrictIDs.Add(fileInfo.DistrictID);
                }
            }

            bool isMultiServieType = false;
            for (int i = 1; i < condition.FileInfos.Count; ++i)
            {
                if (condition.FileInfos[i].ServiceType != condition.FileInfos[i - 1].ServiceType)
                {
                    isMultiServieType = true;
                }
            }

            DIYReplayFileQuery query = new DIYReplayFileQuery(MainModel.GetInstance());
            if (isMultiServieType)
            {
#if ReplayMultiService || DEBUG
                query = new DIYReplayFileByMultiService(MainModel.GetInstance());
#endif
            }
            query.SetQueryCondition(condition);
            query.Query();
        }
    }
}
