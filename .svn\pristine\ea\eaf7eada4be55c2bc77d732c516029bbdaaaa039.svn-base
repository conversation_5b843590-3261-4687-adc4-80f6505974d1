﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public partial class StationAcceptReportForm : MinCloseForm
    {
        public StationAcceptReportForm()
        {
            InitializeComponent();
        }

        public void FillData(Dictionary<NetType, List<BtsAcceptRecordInfo_SX<string>>> btsRecordDic)
        {
            if (btsRecordDic.TryGetValue(NetType.LTE, out var lteRecord))
            {
                //tabPage4G.PageVisible = true;
                gc4G.DataSource = lteRecord;
                gc4G.RefreshDataSource();
            }

            if (btsRecordDic.TryGetValue(NetType.LTE, out var nrRecord))
            {
                //tabPage5G.PageVisible = true;
                gc5G.DataSource = nrRecord;
                gc5G.RefreshDataSource();
            }
        }
    }
}
