<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="mainToolBar.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnPackToZip.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAgxJREFUOE+lkvtL
        U2EYx+0PEbtpFwnBKPGKiJImGP0gYhIYs1E5GF5gIxkpA00JRSmMEF0ohMh+GaRWYlqabMVcNdS2QpaI
        VqiDIYhk397vA6fXhCjyhYdzeM/5fp7vczkAdeL2cwho7v/wWzT1zcN+Pwhr51uY2/y41PQaF+wzKKiZ
        QvaN58g0jyLd5KEUcQbg+84P/Cm2tncQjW3j68YWIqubCC3FcOJc478BAuGoZM6zvoRnakXEruEIjhc4
        /g5gZop9c+voGAyLbQIfeBZxLL9BA1jzXvuGbWamuKh+GmmVbswE19A59FEBbmoAG7YbsLtm2mZmiml9
        cvabNDwpz6YB7LYBoMXCumkJr7LOmnnHzBQ/9X2Bo2cOibm1GsBREbAQiYmw/8lnuCeWkVzcgnZlnw1j
        3HV/wuNXK6i/9x5Hc6wawDlTXHbLJ+LZUBQPRyKwdQdxutwl1h+NLXHh5Ht1ewBHsiwawCW57HyDAfWR
        dvl0uhZQ1eqX8aVc7EKLqrum651ATLf9OJx5XQM4KmY0xPzZ0hFAiQJnXB0WwME0E3IsL5B17ZlADqWb
        NYDrOepdlcysmTWWOrxqbceRWtaLk0VO1XW72D5Vckd2gMBfq8zdpmUG62NJvKM4+XyziDk24xmfWoGE
        s1c0gHPmbrPTpHNJKOCo2G1mZs20zcwUJ5yp1AB5+8/zEwgF5GMVDxh4AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnModeAddConn.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAALRJREFUOE+tk8EN
        gDAIRXUm747TnXQOB+gAdQUPLoJ+FIKVWk1sQlD8vAK1bfNydV0gkc7z2L5MO2RIHobEAHgLUxClhvpx
        IXhrEIeQNAYNbOdoRQzJE/EuO9++ETHk0oa3M8qVOGCqqQHynW+VPAGqyZhRDWDLtgP81EJxgKUKvCP0
        ToZjXgsAoFwYhj+tZXMB3Ou5ABMAjjI3F4CfQiAWptTsARr3PkgbNR9j9AGfbtkf4g1yrsqjvgnpigAA
        AABJRU5ErkJggg==
</value>
  </data>
  <data name="btnLarge.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAx9JREFUSEvFlH8s
        1HEYx7m1/FHW1D9txUx/1Go2f6Qf1izKkB+LScyqS7OsVhjWKJVChCxxyo/m7I7Oj6NjDjmdXOj85hgx
        wtU5W0yy1Wp7d59HylbtXLS+27PP94/vntf3/X4/z8fI6H8/qsFpsFJrP2LN/qW5YwJ54ilI5FPo7v2C
        1+MLGFN/opPBVgWKS+9Fas4YSqtmUFs7D7lcC8VLNZRKDbRa4J12EfhG8xeKLl5rREhMP1J4kxCI5lBc
        NouHuc+x0ToEQdECgo2OgkBMkUG2ZeYqcOJMK4LDBhGfokUufx4Fwve4GVcMjuVpcCP5EIkm0NH1lWxj
        ENWwASrs3erhdrKFADcS1EjnTSMrexIJiRUE8A/NQ37h0Hfb5tHXv4Dh4Q/oGpzTn0m1tBkHnGrh7vsK
        50NVSMtQIo/fhXxhGyKu88GxCoJHEA/i8jaUS/qp5C9GCaBon9UP4GVXE4BZdCG8HcbbPKgpZ0cw/T07
        9zjHws7nLp37vRPJMhb8s+YZ/YC0BxId4CkBwqM7YWxmC+MtDjDe6kTF2e5NQRNQ986x8IPFoSgCsFHW
        O7aP+VUE8OHKEXOnn7yukQ2hoVGFe/dzqSlryCyqrh6nqpdpKIfiOo1+gEwmwz6nErj6VSIythPCcjU6
        eheXagngzE0n33tUn2mS2MlqRQAm0dW/CEe8BAi4XIfEzG5I5W/BNjohOYsyOeyf+hOgg7PmzJ4VLxy/
        oJhUOHiV/IAUlA3gVqIAm6zcYGYTBknNCE0NK9ZcKFXrt2d5QEmpAtgeFcLeUwTvc1Vgm301WYHbaU1I
        5rUgk9+HR08GkVE0gqSCYSh7xqn0hrz8A6aEWXXwWBGp8eRWkqLAiHoERTUg8EojXM4KYbLrEgolSgJ0
        D0waBmGhM2tYLo6+FXAOkMDlVCWOcwXYbO4OjqkNjTEb2yXIqm9ZplJaUwexWAwjE0usM939C8RgJX/y
        liDrzX8Lae0aM8wuQyA5oiaUSjvBF7f8I4ju3trrGQ+vYN7aAJi6Jbs4G3bq7jA7Wkprx7C1AyyHUPC6
        i9KgvVjtx98AihixaI4E1swAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnSmall.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAudJREFUSEvFlF1I
        k1EcxnUXeRFdZJdlCF0UgRBRUiHCJNHSBKP0Qqw0AoUsnGJYDXIZZl9WkDpMM/NrOaebor7bCD9qukGp
        kalpvqnhrMZK7EOFetr/yPuSbrANB73wwHtxOL/zPP/nHD+///31D38BaXp2Hj47i6lvBuWaWeiMdgy+
        WcQ4v4CJqQXwM9/RPzK3NlBe0QBul/FQt9phMNlhNoMBrFYw2WzAKD8P/uOC96DUC104Lx/CreJpVKvm
        oG1ZAqf/je7uPwwkuCEIwbyCFNzhcPxkH9KzhpF/0wZl+U88bVwUIatBBPEqrvAYI2ISe5EuGxMBtaol
        J4DghmIjeQQpr2zGvkgOsQlmZOR8QOHdr3j05NeKiGhjciE4obgopm7zN/ezkOdVMgBFlHV5HDeKxqGs
        HYVaM4G2dh4cN8nU1DSFurplcdwn5oCK4La+2bllDoCWAWTyPkiCkxG4W4Y9cdcQckjhJGlSEVJyqmCx
        WKHrnHUPuHJVyQDHTnUygP/GUEg2xy5rS7yzHAfYGnYRdF8auM/uASWlFQiNbEB0cgsD3KsYRpthCC/M
        72AZ5EU9N/HQG3gWm77Hii6L3TNAY6MG0iPFCI9T4cQ5I64/GGAb0BNBl4oktIaG+/L1D3YnKJ73kzb3
        DmhIuZfysf9wHSISmkVIfcuEeFI6LTVGEG1e3zHj2eYE0Gq1SE0rwN6DNczJ0dOtOCvvgeJ+P4uspHqU
        NUtZP8L+C6vGHNFNMrltkbCAIOQkLKZCdENzodhSs404k/sM9JxEp9QgYEcGanUWBqAoPYbQwtJSJXMj
        ja9kkUUl6VgBohIfIjDI0awNu+C/SQrJtjQRMvB22juIEBsVgETuCKxQKOAXEAzJ+p2+gay2TiAGWRfk
        EkK19iouV4vbOzpcQspUPVC3v8JjTe/aIQaDwRniuOH0vMSnFa8dQM5WQrY7npgD7HkJicj0DWAFhA2e
        IKG+2/zf+VDTXM3rL/iT9sGGBKgWAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnParamSetting.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAFBJREFUOE9jYBgs
        4D/QIeRguPuBmhvg+MCBA2A2fhpsIU0MIMkrCBeAnAtxMsN/YgA0zLB5AWIAzCB8NEoYjLqAARwDFMYC
        SfEPS/aDICsCAPA4c095ru8dAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnL3ParamSetting.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAHdJREFUOE/NU0EO
        gDAI29N5Gj9TG63BBYpGD5KQsTCapnRj/DLMbFHZksZwFehtAMg97LjEE7UCQO8CMlPiMHBihkEC5EwU
        A4K7e82iYkA26J8ATzX4nkG2jdca0AfzFlINMlcpI93SAABQuUtlpmiUrm6/hnywAryZj78OfKYfAAAA
        AElFTkSuQmCC
</value>
  </data>
  <data name="btnContinueRun.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAATBJREFUOE+dkjFO
        w0AQRY3IAXwEH2GP4CPsEbakdJkyJSVlyhwhFBQ0CCLkEEKCExzJoCAcGZSAMYFAP+wMcuTdCY0tvWJm
        /39eS977/sxhv9Fwaj8o+NkUtXFQYDPNbthuVw53JNisXw1UW8Eku2Z7O4czCb6KpQEK/KaAaDFgZ3aW
        BOv8xUC1JXSTDklu0z47r+ZJ8PGWGcgjCcF5sJWMn0KWKTskKFYLA3nokwBRHUk3GT1eshz2SPC+TA38
        lhZ01V+5JUAEHngHLsRJn2VJkGtBFaFL1bKrHLjTZTuHMwlW2YOBaHrbN2M5inssU3Z2/kievjJeG8uj
        yRk8p7N/IUE6jwzwe7E8GJ+yMztLgvn90MBVLoTDE7a3cziTIJmFBr2rY7azM+VMgnh6UZtf4qw1p/k+
        q5YAAAAASUVORK5CYII=
</value>
  </data>
  <data name="miNextCursor.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAUlJREFUOE+lj7FO
        wlAUho9v0kfoyNjR8Y6Md3TsyNiRsaMjj9DFRENi0BggVKEgaEFiasBQReSKxLj99pbcunAmbvLlDOf/
        zp979LNdgw55280Kh0DfagmOxfyZ3RmHNus3cPiexFPcY/fao6/VAhz6gFeRmDx02Qytl6/g0AegGnBP
        BMbD2705+nyfwVDzJfyqxoWW9YQKgLQGWXYQ33eKrHFolb7AoGWVZsJvlDdrEakPJC5UJCCObTz220Ve
        e/SxSGDwPbET89adiDiTY4mk4aBx6kCUbITNeuHQMjtg8Cr6wK5VxV4uqkjLAoHvwClZGPVbRV57lM4m
        MHiugCsduGULUljZtJGc/8uDu+siaxyaJyNwyOzPQdXOm3udy705SqYROIRj5XLYumAzNB2H4NBy++aM
        3WuP4lETHN2wzu6MQ8PBFQ7hD3p3NSehDPc8AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnStepOver.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAWRJREFUOE+dkk1L
        AlEUhk+7oE2/wXULf4LQJndDLbJWbSohQjeVREaLoKIPImjRKhcFCUHTl4lYjYUFiTZGWok4hommmWUt
        avXmHSh0UCfmwsN77jnvORy4t+mzVEBU+qI76ZuEcI4kKUvU3EptuhaaNetI9Xy85bGwLmJsJYgdbwzx
        RBos91+oVHzG0NwV/MEEWKykvAEa8l7IoGfyDEy1QMV8GpxVAFM1am7ymkvJA5hqgQrZR3kAUzVqbvCS
        SYIzC2CqBcqXGw1mHkzVqLlBNhWTBzDVAj0lIzBa9hAWRbBYieo/SMZFdFn2seb0IRoJwTgqYnD6HEcn
        AbCaGhR/CGDA7oJ13g3ec4nVDT8MNhGdIz6ML3pw4BHAPPWg+8gFtvhT9E8cgrMJMNmv5Wc19Dmg55ah
        75gC89SDbm98qMTtPf5rbDfNwOHcrqor/SSGvKikd3gT3ZYl7Lr4qrzS93v/ASeE++gKiu7fAAAAAElF
        TkSuQmCC
</value>
  </data>
  <data name="bbtnStepInto.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAATVJREFUOE+l0k1L
        AkEYB/Cnb+RH2FNnL4JeaslLdOooBClRXvIgREi3oDoEHepQWRQpgb2s4ei2prHlmIqWbZZJ13/ugBK0
        mjEDv2Fe+A8PDzP21WmBZEanbUEGXesNRLar8Id1KNOsKwFFTcAzc4jF1St8vjeHoshmEQuxO+yflfBQ
        qqHdev4X8gdzuMyU8WHVf+m2xu7PQHaG3LMM8eQjWs0afGEOzxLHOauI/ShIDeoIrdxg58jAifYEd4hD
        CXAsb2VQKJp4e6kITpWI84PTPEKxNHzzWREWD6i7cHmjcLmjsBrloei1ztGjsTwU77oIjk+tYe841b9z
        qsDOUbM79UwG4piY28BFmvXPft47ralRucconCqwc1TlBmQQNxkG+esf2DkyixpkUMFIQQbd5pKQ8Q2D
        ePfmgxJrRwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnClearBrk.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAA0hJREFUSEu9lmtM
        U2cch032cd+N8m0JusUbaJ27ZeqMSrwOMQbcMhcmoNYRBc0EFBU1AgsiMkEbSzuZWEa9Ei8EmoEVrRdw
        VYrYUrVQsQWsWEBJnPp4eha68zITQ1Jt8iRvzvv+f8/5nXM+dMSI9/UbeOon1Aj3/qy/h1AjCPp7fbyJ
        LtvftNdW4zxl5Hb5EW7+aaD9ZuMbzw6dFwR9T7oZykOLGWfJQVwH9uHevwd71mYs69ScSEzAec3yv/ND
        5wVBb08nSjqbGmnVFNGlO0Df6Qp6TxzFqynEvmsrlpRkqlPXc3lvHtbyMhzmWmF2MEcQ+H0elLSZznM3
        bzd+g56n507Sf+Y47YeKsOfncr8wj7aCXFoy0/5tlJCAWa8T5gNZgqCnuwMljooy7u3YjE9q4K8oxVuq
        xZH/K13aIrFRxgZMSxZREh2D5ZhRyBAEj7vcKGkq1dG8Xo07JwvPvlxa83O4m7tTaNStK6Z1ewZXvltG
        2YyvKfl5nZAhCHzeNpRYpS/G/MNybOqVtGxMxrrlF+5tSws2emL4HY/04lvSU7kYtxTjZyqypn0lZAiC
        Rx4XSppNVRh/XEHV3G+4FD2fi6sTsSWvCjbq2JuNUxI2roqnZsFcjkZOIGOiSsgQBN2SIMCL3Wt4sTMJ
        X0sjdVotB+fNR6+KoC4umrrly4KNbNLja1i5gtqYhVR++Sn7P/mYoiS1nDGIIPC6HQT4J1sNm2J5npeC
        13WbS8fKsWeuxaOOQSfJqubMlBuZJaqjZlH5uQpt+EdsmRhJzWG9nDGIIHjgaiZAR6uVZ8WZkBbHc0k2
        ULiJV6lL8P+xB5OmWGo0D92UCAwR4zkybiy/jQknY/wk9Onp8rwSQeByWlHi12dDSjQkzeZRWUFwr85w
        GM0aNZlTppE2LpKC+J84p9MIs4M5gsBpv06QO1cZ2BoPSyfzMmoMfRti/9tTnnvLWhDcab5MAEdDDQPJ
        i2F6GP2JUfSs/ZbOqaN5kPq9vD8cBIHt1gUCuKVH8yriQ3xTR+HS5cjX2lRh1I/8QF4PB0FgvWEigK3W
        yOMvwnDPCMf2V4V8relsKbbYmfJ6OAiChuvnCTWC4IqlklAjCOrrjxNq3tf/iXfreQ1QLpXgFYEwmQAA
        AABJRU5ErkJggg==
</value>
  </data>
  <data name="btnFindText.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAjpJREFUOE+l019M
        UlEcB3BtbW3VY+uhZ31o1UOu2dbaarjhFuEF8l8tV2vEzFopZvpSF9iK9YeFqbPWJEWp9cdimC2jIoaV
        KYn5J4klZhQgwkXgcgmwfbv01MNVW53tvJxzfp9zvmf7ZWexgyQtKw9U5BROffMTXzyzG6MxZr2fotau
        WbX6U25unvawaLMpc27R0Wd1KFrajSOPng4kzEMfYR5347rJhjNaAyRSMlhyQn1w0eLy4xc3aHXGdy+s
        9p8BmoGXScIdZTAcoGEa/w6Frhf80voUv7gmhxMx24Z1t4x9Mc/cPOhUGpFkGoF4CpOhOCzTEXQNeVF5
        XoedklMqTqC53TTxpN/BFiURSy2ATi+AYtJwhRhY3fO46wjirP4Vtourw5xAjbIV/S4P3JEE5pgUqB9p
        eGNJjM3SePk5DMOgD6rbduwqloMTUFztmOp6/h52P41JioErnMAom//11yh6JoJos3kgb+5FvqBqmBMg
        NZ36hsbuxLPpKKwzMXZGYWafbnKG0Dnkg7p7DGW11yA+JKM5ARl5Y51Yqopcvm/FvQ9BPBylcIfNrXvr
        wyWTE0eUHeCVymFoknJHyKj7jl3gCyuU8aPKNpzTW9hbR1Db8hj765qwu/gkGhUEwk71MkilemuD5sFN
        XkndQL6gOlZQXo+yqisze/YW1vG2bcGb1k3LIxwZszNrQpFQQoiE/4z8dv8LIcmsFX8iPZo8nJYVLP6p
        SzUeQRA7CEI4WFQkEC/ZoX+7+Qs89nIz3EbwDwAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="ctxMenuNode.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>137, 17</value>
  </metadata>
  <metadata name="ctxMenuResv.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>257, 17</value>
  </metadata>
  <metadata name="ctxMenuRoutine.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>377, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>42</value>
  </metadata>
</root>