﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    class ReasonMultiCover : ReasonBase
    {
        public ReasonMultiCover()
        {
            this.Name = "重叠覆盖";
        }
        
        public int MultiNumMin { get; set; } = 4;
        public float RSRPDiffMax { get; set; } = 6;
        public float RSRSMin { get; set; } = -115;

        public override bool IsValid(Model.TestPoint tp, params object[] resvParams)
        {
            List<float> rsrpSet = new List<float>();
            object value = GetRSRP(tp);
            if (value != null)
            {
                rsrpSet.Add(float.Parse(value.ToString()));
            }
            for (int i = 0; i < 10; i++)
            {
                value = GetNRSRP(tp, i);
                if (value == null)
                {
                    continue;
                }
                rsrpSet.Add(float.Parse(value.ToString()));
            }
            if (rsrpSet.Count == 0)
            {
                return false;
            }
            rsrpSet.Sort();
            int cnt = rsrpSet.Count;
            float max = rsrpSet[cnt - 1];
            if (cnt < MultiNumMin)
            {//有效的RSRP小于最新重叠覆盖个数，返回false
                return false;
            }
            for (int i = 1; i <= MultiNumMin; i++)
            {
                float rsrp = rsrpSet[cnt - i];
                if (rsrp < RSRSMin || max - rsrp > RSRPDiffMax)
                {
                    return false;
                }
            }
            return true;
        }
        protected object GetRSRP(Model.TestPoint tp)
        {
            if (tp is Model.LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            else if (tp is Model.ScanTestPoint_NBIOT)
            {
                return tp["LTESCAN_TopN_CELL_Specific_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetNRSRP(Model.TestPoint tp, int index)
        {
            if (tp is Model.LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            else if (tp is Model.ScanTestPoint_NBIOT && index > 0)
            {
                return tp["LTESCAN_TopN_CELL_Specific_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                param["MultiNumMin"] = this.MultiNumMin;
                param["RSRPDiffMax"] = this.RSRPDiffMax;
                param["RSRSMin"] = this.RSRSMin;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("Enable"))
                {
                    this.Enable = (bool)param["Enable"];
                }
                if (param.ContainsKey("MultiNumMin"))
                {
                    this.MultiNumMin = (int)param["MultiNumMin"];
                }
                if (param.ContainsKey("RSRPDiffMax"))
                {
                    this.RSRPDiffMax = (float)param["RSRPDiffMax"];
                }
                if (param.ContainsKey("RSRSMin"))
                {
                    this.RSRSMin = (float)param["RSRSMin"];
                }
            }
        }
    }
}
