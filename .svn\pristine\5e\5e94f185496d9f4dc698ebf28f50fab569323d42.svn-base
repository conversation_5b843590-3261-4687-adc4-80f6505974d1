﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;
using CQTLibrary.PublicItem;
using CQTLibrary.CqtZTFunc;
using System.Reflection;
using System.Collections;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTPPHandOverAna : DIYStatQuery
    {
        public DIYQueryCQTPPHandOverAna(MainModel mainModel, string netWoker)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            this.netType = netWoker;
        }
        private readonly string netType;
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return ""; }
        }
        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21027, this.Name);//临时
        }
        
        #region 全局变量
        private readonly List<FileInfo> fileList = new List<FileInfo>();
        private readonly List<string> fileValueNameList = new List<string>();
        readonly Dictionary<string, List<FileInfo>> fileValueList = new Dictionary<string, List<FileInfo>>();
        Dictionary<int, List<Event>> condEventsDic = new Dictionary<int, List<Event>>();
        readonly List<HandOverItemCQT> cqtResultList = new List<HandOverItemCQT>();
        Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>> areaKeyStringHoSubInfo
            = new Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>>();
        #endregion
        /// <summary>
        /// 查询数据条件
        /// </summary>
        protected override void query()
        {
            fileList.Clear();
            fileValueNameList.Clear();
            fileValueList.Clear();
            cqtResultList.Clear();
            areaKeyStringHoSubInfo.Clear();
            // 查找全区域文件
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();
            fileList.AddRange(diyFileInfoData.FlieInfoData);
            //找出所有文件中包含的地点名称
            foreach (FileInfo fileIn in fileList)
            {
                string[] name = fileIn.Name.Split('_');
                if (name.Length < 3)
                    continue;
                if (!fileValueNameList.Contains(name[2]))
                {
                    fileValueNameList.Add(name[2]);
                }
            }
            //处理事件
            PrepareEvents();
            List<int> eventId = new List<int>();
            eventId.AddRange(condEventsDic.Keys);
            //每个地点所涉及的文件
            addFileValueList(eventId);
            //各测试地点事件
            if (netType.Equals("GSM"))
            {
                WaitBox.Show("开始分析各个测试地点的切换事件", cqtHandOverEventAnaGSM);
            }
            else if (netType.Equals("TD"))
            {
                WaitBox.Show("开始分析各个测试地点的切换事件", cqtHandOverEventAnaTD);
            }

            CQTPPSwitchXtraFormNew cqtShowForm = new CQTPPSwitchXtraFormNew(MainModel, netType);
            cqtShowForm.setData(cqtResultList, areaKeyStringHoSubInfo);
            cqtShowForm.Show();

        }

        private void addFileValueList(List<int> eventId)
        {
            foreach (string nameL in fileValueNameList)
            {
                List<FileInfo> subFileList = new List<FileInfo>();
                foreach (FileInfo fileIn in fileList)
                {
                    string[] name = fileIn.Name.Split('_');
                    if (name.Length < 3)
                        continue;
                    if (nameL.Equals(name[2]) && eventId.Contains(fileIn.ID))
                    {
                        subFileList.Add(fileIn);
                    }
                }
                fileValueList.Add(nameL, subFileList);
            }
        }

        /// <summary>
        /// 查询全区域切换事件
        /// </summary>
        private void PrepareEvents()
        {
            DIYEventByAllRegion queryEvent = new DIYEventByAllRegion(MainModel);
            queryEvent.SetIsAddEventToDTDataManager(false);
            queryEvent.SetSaveAsFileEventsDic(true);
            queryEvent.showEventChooser = false;
            queryEvent.IsQueryAllEvents = false;
            List<int> eventIds = new List<int>();
            if (netType.Equals("GSM"))
            {
                eventIds.Add(17);
            }
            else if (netType.Equals("TD"))
            {
                eventIds.Add(142);
                eventIds.Add(145);
                eventIds.Add(148);
                eventIds.Add(151);
            }
            condition.EventIDs = eventIds;
            queryEvent.SetQueryCondition(condition);
            queryEvent.Query();
            condEventsDic = queryEvent.fileEventsDic;
        }
        /// <summary>
        /// 按文件分析切换事件（TD）
        /// </summary>
        private void cqtHandOverEventAnaTD()
        {
            WaitBox.CanCancel = true;
            int idx = 1;
            foreach (string cpn in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                WaitBox.Text = "正在分析 " + idx++ + "/" + fileValueNameList.Count + " " + cpn + " 的切换事件...";
                WaitBox.ProgressPercent = 30;
                foreach (FileInfo fileinfo in fileValueList[cpn])
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    addCqtResultList(cpn, fileinfo);
                }
                WaitBox.ProgressPercent = 90;
            }
            areaKeyStringHoSubInfo = getHo2FreqList(cqtResultList, 20,1,1);
            WaitBox.Close();
        }

        private void addCqtResultList(string cpn, FileInfo fileinfo)
        {
            foreach (Event eve in condEventsDic[fileinfo.ID])
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                if (eve.ID % 3 != 1)
                    continue;
                HandOverItemCQT cqtResultTem = new HandOverItemCQT();
                cqtResultTem.Iareatype = eve.AreaTypeID;
                cqtResultTem.Iareaid = eve.AreaID;
                cqtResultTem.Ifileid = eve.FileID;
                cqtResultTem.Ilac = (int)eve["LAC"];
                cqtResultTem.Ici = (int)eve["CI"];
                cqtResultTem.Itime = (int)(JavaDate.GetMilliseconds(eve.DateTime) / 1000);
                cqtResultTem.Strcqtname = cpn;
                TDCell tdcell = CellManager.GetInstance().GetTDCell(eve.DateTime, (ushort)cqtResultTem.Ilac, (ushort)cqtResultTem.Ici);
                if (tdcell != null)
                    cqtResultTem.Strcellname = tdcell.Name;
                else
                    cqtResultTem.Strcellname = "";

                cqtResultList.Add(cqtResultTem);
            }
        }

        /// <summary>
        /// 按文件分析切换事件（GSM）
        /// </summary>
        private void cqtHandOverEventAnaGSM()
        {
            WaitBox.CanCancel = true;
            int idx = 1;
            foreach (string cpn in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                WaitBox.Text = "正在分析 " + idx++ + "/" + fileValueNameList.Count + " " + cpn + " 的切换事件...";
                WaitBox.ProgressPercent = 30;
                foreach (FileInfo fileinfo in fileValueList[cpn])
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    addGsmCqtResultList(cpn, fileinfo);
                }
                WaitBox.ProgressPercent = 90;
            }
            areaKeyStringHoSubInfo = getHo2FreqList(cqtResultList,20,1,1);
            WaitBox.Close();
        }

        private void addGsmCqtResultList(string cpn, FileInfo fileinfo)
        {
            foreach (Event eve in condEventsDic[fileinfo.ID])
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                if (eve.ID != 17)
                    continue;
                HandOverItemCQT cqtResultTem = new HandOverItemCQT();
                cqtResultTem.Iareatype = eve.AreaTypeID;
                cqtResultTem.Iareaid = eve.AreaID;
                cqtResultTem.Ifileid = eve.FileID;
                cqtResultTem.Ilac = (int)eve["LAC"];
                cqtResultTem.Ici = (int)eve["CI"];
                cqtResultTem.Itime = (int)(JavaDate.GetMilliseconds(eve.DateTime) / 1000);
                cqtResultTem.Strcqtname = cpn;
                Cell cell = CellManager.GetInstance().GetCell(eve.DateTime, (ushort)cqtResultTem.Ilac, (ushort)cqtResultTem.Ici);
                if (cell != null)
                    cqtResultTem.Strcellname = cell.Name;
                else
                    cqtResultTem.Strcellname = "";

                cqtResultList.Add(cqtResultTem);
            }
        }

        /// <summary>
        /// 获取乒乓切换序列
        /// </summary>
        public Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>> getHo2FreqList(List<HandOverItemCQT> hoList, int iSecond,int iTurn,int iTimes)
        {
            Dictionary<AreaKeyCQT, Dictionary<int, List<HandOverItemCQT>>> areaFileDic
                = new Dictionary<AreaKeyCQT, Dictionary<int, List<HandOverItemCQT>>>();
            foreach (HandOverItemCQT ho in hoList)
            {
                AreaKeyCQT aKey = new AreaKeyCQT();
                aKey.Iareatypeid = ho.Iareatype;
                aKey.Iareaid = ho.Iareaid;
                if (areaFileDic.ContainsKey(aKey))
                {
                    setAreaFileDic(areaFileDic, ho, aKey);
                }
                else
                {
                    Dictionary<int, List<HandOverItemCQT>> fileDic = new Dictionary<int, List<HandOverItemCQT>>();
                    List<HandOverItemCQT> tmpList = new List<HandOverItemCQT>();
                    tmpList.Add(ho);
                    fileDic.Add(ho.Ifileid, tmpList);
                    areaFileDic.Add(aKey, fileDic);
                }
            }
            Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>> handoverDic = getHandoverDic(iSecond, iTurn, iTimes, areaFileDic);
            return handoverDic;
        }

        private void setAreaFileDic(Dictionary<AreaKeyCQT, Dictionary<int, List<HandOverItemCQT>>> areaFileDic, HandOverItemCQT ho, AreaKeyCQT aKey)
        {
            Dictionary<int, List<HandOverItemCQT>> fileDic = areaFileDic[aKey];
            if (fileDic.ContainsKey(ho.Ifileid))
            {
                List<HandOverItemCQT> tmpList = fileDic[ho.Ifileid];
                tmpList.Add(ho);
            }
            else
            {
                List<HandOverItemCQT> tmpList = new List<HandOverItemCQT>();
                tmpList.Add(ho);
                fileDic[ho.Ifileid] = tmpList;
            }
        }

        private Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>> getHandoverDic(int iSecond, int iTurn, int iTimes, Dictionary<AreaKeyCQT, Dictionary<int, List<HandOverItemCQT>>> areaFileDic)
        {
            Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>> handoverDic = new Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>>();
            foreach (AreaKeyCQT aKey in areaFileDic.Keys)
            {
                Dictionary<int, List<HandOverItemCQT>> areaHoDic = areaFileDic[aKey];
                Dictionary<string, HoSubInfoCQT> hoSubDic = getHoSubDic(iSecond, areaHoDic);
                if (hoSubDic.Count < iTurn)
                    continue;
                Dictionary<string, HoSubInfoCQT> hoSubFinalDic = new Dictionary<string, HoSubInfoCQT>();
                foreach (string strChange in hoSubDic.Keys)
                {
                    if (hoSubDic[strChange].Itime >= iTimes)
                        hoSubFinalDic.Add(strChange, hoSubDic[strChange]);
                }
                handoverDic.Add(aKey, hoSubFinalDic);
            }

            return handoverDic;
        }

        private Dictionary<string, HoSubInfoCQT> getHoSubDic(int iSecond, Dictionary<int, List<HandOverItemCQT>> areaHoDic)
        {
            Dictionary<string, HoSubInfoCQT> hoSubDic = new Dictionary<string, HoSubInfoCQT>();
            foreach (int ifileid in areaHoDic.Keys)
            {
                List<HandOverItemCQT> hoListFile = areaHoDic[ifileid];
                HandOverItemCQT tmpHo = new HandOverItemCQT();
                ReverserCQT<HandOverItemCQT> reverser = new ReverserCQT<HandOverItemCQT>(tmpHo.GetType(), "Itime", ReverserInfoCQT.Direction.ASC);
                hoListFile.Sort(reverser);
                if (hoListFile.Count < 3)
                    continue;
                for (int i = 0; i < hoListFile.Count - 3; i++)
                {
                    addValidHoSubDic(iSecond, hoSubDic, hoListFile, i);
                }
            }

            return hoSubDic;
        }

        private void addValidHoSubDic(int iSecond, Dictionary<string, HoSubInfoCQT> hoSubDic, List<HandOverItemCQT> hoListFile, int i)
        {
            HandOverItemCQT ho1 = hoListFile[i];
            HandOverItemCQT ho2 = hoListFile[i + 1];
            HandOverItemCQT ho3 = hoListFile[i + 2];
            if (ho1.Itime + iSecond > ho2.Itime && ho1.Itime + iSecond > ho3.Itime && ho2.Itime + iSecond > ho3.Itime &&
                ho1.Ici != ho2.Ici && ho1.Ici == ho3.Ici && ho2.Ici != ho3.Ici)
            {
                string ciList = ho1.Ici.ToString() + "->" + ho2.Ici.ToString() + "->" + ho3.Ici.ToString();
                int isecond = ho3.Itime - ho1.Itime;
                if (hoSubDic.ContainsKey(ciList))
                {
                    HoSubInfoCQT hsi = hoSubDic[ciList];
                    hsi.Itime += 1;
                    hsi.Isecond += isecond;
                }
                else
                {
                    string ciListDetail = ho1.Strcellname + "[" + ho1.Ilac + "," + ho1.Ici + "] -> " +
                                          ho2.Strcellname + "[" + ho2.Ilac + "," + ho2.Ici + "] -> " +
                                          ho3.Strcellname + "[" + ho3.Ilac + "," + ho3.Ici + "]";

                    HoSubInfoCQT hsi = new HoSubInfoCQT();
                    hsi.Itime = 1;
                    hsi.Isecond = isecond;
                    hsi.StrHandOver = ciListDetail;
                    hsi.StrCqtName = ho1.Strcqtname;
                    hoSubDic.Add(ciList, hsi);
                }
            }
        }
    }
    public class AreaKeyCQT
    {
        public int Iareatypeid { get; set; }
        public int Iareaid { get; set; }

        public override bool Equals(object obj)
        {
            AreaKeyCQT other = obj as AreaKeyCQT;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.Iareatypeid.Equals(other.Iareatypeid) &&
                    this.Iareaid.Equals(other.Iareaid));
        }

        public override int GetHashCode()
        {
            return this.Iareaid.GetHashCode();
        }
    }
    public class HandOverItemCQT
    {
        public int Afterrelev { get; set; }
        public int Beforerelev { get; set; }
        public DateTime Dtime { get; set; }
        public string HandOverType { get; set; }
        public int Hotime { get; set; }
        public string Hotype { get; set; }
        public int Iareaid { get; set; }
        public int Iareatype { get; set; }
        public int Ici { get; set; }
        public int Ieventid { get; set; }
        public int Ifileid { get; set; }
        public int Ilac { get; set; }
        public int Itargetci { get; set; }
        public int Itargetlac { get; set; }
        public int Itime { get; set; }
        public string Strcellname { get; set; }
        public string Strcqtname { get; set; }
        public string Strfilename { get; set; }
        public string Strtargetcellname { get; set; }
        public int Wtimems { get; set; }
    }
    public class HoSubInfoCQT
    {
        public int ICellNum { get; set; }
        public double IResult { get; set; }
        public int Isecond { get; set; }
        public int Itime { get; set; }
        public string StrCqtName { get; set; }
        public string StrHandOver { get; set; }
        public string StrHandOverKey { get; set; }
    }
    public class ReverserCQT<T> : IComparer<T>
    {
        private readonly Type type = null;
        private readonly ReverserInfoCQT info;

        /**/
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="type">进行比较的类类型</param>
        /// <param name="name">进行比较对象的属性名称</param>
        /// <param name="direction">比较方向(升序/降序)</param>
        public ReverserCQT(Type type, string name, ReverserInfoCQT.Direction direction)
        {
            this.type = type;
            this.info.name = name;
            if (direction != ReverserInfoCQT.Direction.ASC)
                this.info.direction = direction;
        }

        /**/
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="className">进行比较的类名称</param>
        /// <param name="name">进行比较对象的属性名称</param>
        /// <param name="direction">比较方向(升序/降序)</param>
        public ReverserCQT(string className, string name, ReverserInfoCQT.Direction direction)
        {
            try
            {
                this.type = Type.GetType(className, true);
                this.info.name = name;
                this.info.direction = direction;
            }
            catch (Exception e)
            {
                throw (new Exception(e.Message));
            }
        }

        /**/
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="t">进行比较的类型的实例</param>
        /// <param name="name">进行比较对象的属性名称</param>
        /// <param name="direction">比较方向(升序/降序)</param>
        public ReverserCQT(T t, string name, ReverserInfoCQT.Direction direction)
        {
            this.type = t.GetType();
            this.info.name = name;
            this.info.direction = direction;
        }

        //必须！实现IComparer<T>的比较方法。
        int IComparer<T>.Compare(T x, T y)
        {
            object t1 = this.type.InvokeMember(this.info.name, BindingFlags.Public | BindingFlags.Instance | BindingFlags.GetProperty, null, x, null);
            object t2 = this.type.InvokeMember(this.info.name, BindingFlags.Public | BindingFlags.Instance | BindingFlags.GetProperty, null, y, null);
            if (this.info.direction != ReverserInfoCQT.Direction.ASC)
                Swap(ref t1, ref t2);
            return (new CaseInsensitiveComparer()).Compare(t1, t2);
        }

        //交换操作数
        private void Swap(ref object x, ref object y)
        {
            object temp = null;
            temp = x;
            x = y;
            y = temp;
        }
    }
    public struct ReverserInfoCQT
    {
        /**/
        /// <summary>
        /// 比较的方向，如下：
        /// ASC：升序
        /// DESC：降序
        /// </summary>
        public enum Direction
        {
            ASC = 0,
            DESC,
        };

        public enum Target
        {
            CUSTOMER = 0,
            FORM,
            FIELD,
            SERVER,
        };

        public string name { get; set; }
        public Direction direction { get; set; }
        public Target target { get; set; }
    }
}
