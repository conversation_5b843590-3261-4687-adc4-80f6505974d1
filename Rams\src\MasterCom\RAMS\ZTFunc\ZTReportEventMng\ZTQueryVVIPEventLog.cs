﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTReportEventMng;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTQueryVVIPEventLog:DIYSQLBase
    {
        public ZTQueryVVIPEventLog(MainModel mm)
            : base(mm)
        {
            MainDB = true;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18021, this.Name);
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override void query()
        {
            gsmLogs = null;
            tdLogs = null;
            if (!getCondition())
            {
                return;
            }
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitTextBox.Show("正在查询记录...", queryInThread, clientProxy);
                fireShowResultForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void fireShowResultForm()
        {
            VVipEventLogListForm frm = MainModel.GetObjectFromBlackboard(typeof(VVipEventLogListForm).FullName) as VVipEventLogListForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new VVipEventLogListForm();
            }
            frm.FillData(gsmLogs, tdLogs);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
            frm.BringToFront();
            gsmLogs = null;
            tdLogs = null;
        }

        protected override void queryInThread(object o)
        {
            try
            {
                int endType = netType;
                curNetType = netType;
                if (netType == 0)
                {
                    curNetType = 1;
                    endType = 2;
                }
                for (; curNetType <= endType; curNetType++)
                {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }
               
                    package.Content.PrepareAddParam();
                    string strsql = getSqlTextString();
                    E_VType[] retArrDef = getSqlRetTypeArr();
                    package.Content.AddParam(strsql);
                    StringBuilder sb = new StringBuilder();
                    if (retArrDef != null)
                    {
                        for (int i = 0; i < retArrDef.Length; i++)
                        {
                            sb.Append((int)retArrDef[i]);
                            if (i < retArrDef.Length - 1)
                            {
                                sb.Append(",");
                            }
                        }
                    }

                    package.Content.AddParam(sb.ToString());
                    clientProxy.Send();
                    receiveRetData(clientProxy);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitTextBox.Close();
            }
        }

        DateTime beginTime = DateTime.Now.Date.AddMonths(-1);
        DateTime endTime = DateTime.Now.Date;
        private int netType = 0;//1 for gsm ,2 for td
        private int curNetType = 0;//1 for gsm ,2 for td
        private bool getCondition()
        {
            VVipConditionDlg dlg = new VVipConditionDlg(beginTime, endTime, netType);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetSetting(out beginTime, out endTime, out netType);
            return true;
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder("select * from ");
            if (curNetType == 1)
            {
                sb.Append("tb_gsm_vvip_log");
            }
            else if (curNetType == 2)
            {
                sb.Append("tb_td_vvip_log");
            }
            sb.Append(" where [测试时间] between ");
            sb.Append(JavaDate.GetMilliseconds(beginTime.Date)/1000);
            sb.Append(" and ");
            sb.Append(JavaDate.GetMilliseconds(endTime.Date)/1000);
            sb.Append(" order by 事件编号");
            return sb.ToString();
        }

        protected override Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            int fieldCnt = curNetType == 1 ? 18 : 19;
            //数字列较少，且除了数据列外，其他列都可以用string来接收
            List<int> numericFieldIdx = new List<int>();
            numericFieldIdx.Add(0);//sn
            numericFieldIdx.Add(2);//date
            if (curNetType == 1)
            {
                numericFieldIdx.Add(7);//time
                numericFieldIdx.Add(9);//longitude
                numericFieldIdx.Add(10);//latitude
            }
            else
            {
                numericFieldIdx.Add(8);//time
                numericFieldIdx.Add(10);//longitude
                numericFieldIdx.Add(11);//latitude
            }
            E_VType[] arry = new E_VType[fieldCnt];
            for (int i = 0; i < fieldCnt; i++)
            {
                if (numericFieldIdx.Contains(i))
                {
                    arry[i] = E_VType.E_Int;
                }
                else
                {
                    arry[i] = E_VType.E_String;
                }
            }
            return arry;
        }

        private List<EventLogGSM> gsmLogs;
        private List<EventLogTD> tdLogs;
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            if (curNetType == 1)
            {
                gsmLogs = new List<EventLogGSM>();
            }
            else
            {
                tdLogs = new List<EventLogTD>();
            }
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            EventLog log = null;
            if (curNetType == 1)
            {
                log = new EventLogGSM();
                gsmLogs.Add((EventLogGSM)log);
            }
            else
            {
                log = new EventLogTD();
                tdLogs.Add((EventLogTD)log);
            }
            log.SN = package.Content.GetParamInt();
            log.BTSType = package.Content.GetParamString();
            int dateTicks = package.Content.GetParamInt();
            if (log is EventLogTD)
            {
                (log as EventLogTD).PhoneModelNumber = package.Content.GetParamString();
            }
            log.LogNumber = package.Content.GetParamString();
            log.CI = package.Content.GetParamString();
            log.MoMt = package.Content.GetParamString();
            log.EventName = package.Content.GetParamString();
            int dayTicks = package.Content.GetParamInt();
            log.Place = package.Content.GetParamString();
            log.Longitude = package.Content.GetParamDouble();
            log.Latitude = package.Content.GetParamDouble();
            log.Analytics = package.Content.GetParamString();
            log.Suggestion = package.Content.GetParamString();
            log.ErrorType = package.Content.GetParamString();
            log.OwnRegion = package.Content.GetParamString();
            log.IsCloseLoop = package.Content.GetParamString();
            log.HasTrafficLog = package.Content.GetParamString();
            log.PhoneNumber = package.Content.GetParamString();
            log.DateTime = JavaDate.GetDateTimeFromMilliseconds(dateTicks * 1000L).AddTicks(dayTicks * 1000L);
        }
    }

}
