﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLowSpeedCauseSummary
    {
        private readonly NRLowSpeedCauseBase cause = null;
        public NRLowSpeedCauseSummary(NRLowSpeedCauseBase cause)
        {
            this.cause = cause;
            if (cause.SubCauses != null)
            {
                SubCause = new List<NRLowSpeedCauseSummary>();
                foreach (NRLowSpeedCauseBase item in cause.SubCauses)
                {
                    SubCause.Add(new NRLowSpeedCauseSummary(item));
                }
            }
        }

        public string CauseName
        {
            get { return cause.Name; }
        }

        public int Count
        {
            get;
            private set;
        }

        public List<NRLowSpeedCauseSummary> SubCause
        {
            get;
            private set;
        }

        public void AddCause(NRLowSpeedCauseBase cause)
        {
            Count++;
            if (SubCause != null)
            {
                bool unknow = true;
                foreach (NRLowSpeedCauseSummary sub in SubCause)
                {
                    if (sub.cause == cause.Parent || sub.cause.GetType().FullName == cause.GetType().FullName)
                    {
                        sub.AddCause(cause);
                        unknow = false;
                        break;
                    }
                }
                if (unknow)
                {
                    NRLowSpeedCauseSummary unknowCause = new NRLowSpeedCauseSummary(cause);
                    unknowCause.AddCause(cause);
                    SubCause.Add(unknowCause);
                }
            }
        }
    }
}
