﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model;
using System.Drawing;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.CQT
{
    public class CQTPointKPIData
    {
        public CQTPointKPIData()
        { }
        protected Dictionary<int, List<FileInfo>> carreerFiles = new Dictionary<int, List<FileInfo>>();
        protected void addFile(FileInfo dataHeader)
        {
            if (carreerFiles.ContainsKey(dataHeader.CarrierType))
            {
                List<FileInfo> files = carreerFiles[dataHeader.CarrierType];
                for (int i = 0; i < files.Count; i++)
                {
                    if (dataHeader.ID == files[i].ID)
                    {
                        return;
                    }
                }
                files.Add(dataHeader);
            }
            else
            {
                List<FileInfo> files = new List<FileInfo>();
                files.Add(dataHeader);
                carreerFiles.Add(dataHeader.CarrierType, files);
            }
        }
        public List<FileInfo> GetFileInfo(int carreerID)
        {
            if (carreerFiles.ContainsKey(carreerID))
            {
                return carreerFiles[carreerID];
            }
            return new List<FileInfo>();
        }

        public CQTPoint CQTPoint { get; set; }
        public string Name { get; set; }

        protected StatDataHubBase cmDataHub = null;
        protected StatDataHubBase cuDataHub = null;
        protected StatDataHubBase ctDataHub = null;

        protected StatDataHubBase getDataHub(int carrierID)
        {
            StatDataHubBase dataHub = null;
            if (carrierID == 1)
            {
                if (cmDataHub == null)
                {
                    cmDataHub = new StatDataHubBase();
                }
                dataHub = cmDataHub;
            }
            else if (carrierID == 2)
            {
                if (cuDataHub == null)
                {
                    cuDataHub = new StatDataHubBase();
                }
                dataHub = cuDataHub;
            }
            else if (carrierID == 3)
            {
                if (ctDataHub == null)
                {
                    ctDataHub = new StatDataHubBase();
                }
                dataHub = ctDataHub;
            }
            return dataHub;
        }

        /// <summary>
        /// 当前报表各指标列计算结果
        /// </summary>
        public Dictionary<CQTKPIReportColumn, CQTKPIStatResult> ColumnKPIResultDic { get; set; } = new Dictionary<CQTKPIReportColumn, CQTKPIStatResult>();
        /// <summary>
        /// 当前报表汇各总列结果字典
        /// </summary>
        public Dictionary<CQTKPISummaryColumn, CQTKPIStatResult> SummaryResultDic { get; set; } = new Dictionary<CQTKPISummaryColumn, CQTKPIStatResult>();
        
        protected object[] makeRow()
        {
            object[] colValueArr = new object[1 + ColumnKPIResultDic.Count + SummaryResultDic.Count];//名字列+指标列+汇总列
            colValueArr[0] = Name;
            int idx = 1;
            foreach (CQTKPIStatResult result in ColumnKPIResultDic.Values)
            {
                if (double.IsNaN(result.KPIValue))
                {
                    colValueArr[idx] = "-";
                }
                else
                {
                    colValueArr[idx] = result.KPIValue;
                }
                idx++;
            }
            foreach (CQTKPIStatResult sResult in SummaryResultDic.Values )
            {
                if (double.IsNaN(sResult.Score))
                {
                    colValueArr[idx] = "-";
                }
                else
                {
                    colValueArr[idx] = sResult.Score;
                }
                idx++;
            }
            return colValueArr;
        }

        protected CQTKPIStatResult setValidResult(CQTKPIReportColumn rptCol, bool isNegate)
        {
            CQTKPIStatResult result;
            StatDataHubBase statData = getDataHub(rptCol.CarreerID);
            if (statData != null)
            {
                double dValue = statData.CalcValueByFormula(rptCol.Formula);
                bool isValid = judgeValid(dValue, isNegate);
                if (isValid)
                {
                    result = getResultInfo(rptCol, dValue);
                }
                else//非数字数字，无法按范围打分
                {
                    result = new CQTKPIStatResult();
                    result.Color = Color.Empty;
                    result.KPIValue = double.NaN;
                    result.Score = double.NaN;
                }
            }
            else//无数据
            {
                result = new CQTKPIStatResult();
                result.Color = Color.Empty;
                result.KPIValue = double.NaN;
                result.Score = double.NaN;
            }

            return result;
        }

        protected bool judgeValid(double dValue, bool isNegate)
        {
            bool isValid = double.IsNaN(dValue);
            if (isNegate)
            {
                return !isValid;
            }

            return isValid;
        }

        protected CQTKPIStatResult getResultInfo(CQTKPIReportColumn rptCol, double dValue)
        {
            CQTKPIStatResult result;
            double score;
            CQTKPIScoreColorRange range = rptCol.ScoreScheme.GetRangeByKPIValue(dValue, out score);
            if (range != null)//在打分规则内
            {
                result = new CQTKPIStatResult();
                result.Color = range.Color;
                result.KPIValue = dValue;
                result.Score = score;
            }
            else
            {
                result = new CQTKPIStatResult();
                result.Color = Color.Empty;
                result.KPIValue = dValue;
                result.Score = double.NaN;
            }

            return result;
        }

        protected void addSummaryResultDic(CQTKPIReport report, bool isRound)
        {
            foreach (CQTKPISummaryColumn smCol in report.SummaryColumns)
            {
                CQTKPIStatResult result = new CQTKPIStatResult();
                result.KPIValue = double.NaN;
                foreach (SummaryItem item in smCol.SummaryColumns)
                {
                    if (ColumnKPIResultDic.ContainsKey(item.Column))
                    {
                        double colScore = ColumnKPIResultDic[item.Column].Score;
                        if (!double.IsNaN(colScore))
                        {
                            result.Score += colScore * item.Weight;
                        }
                    }
                }
                if (isRound)
                {
                    result.Score = Math.Round(result.Score, 2);
                }
                result.Color = smCol.GetColorByScore(result.Score);
                SummaryResultDic.Add(smCol, result);
            }
        }
    }

    public class CQTMainPointKPI : CQTPointKPIData
    {
        public CQTMainPointKPI(CQTPoint point)
        {
            CQTPoint = point;
            Name = point.Name;
        }
        private readonly Dictionary<string, CQTSubPointKPI> subPointNameDataDic = new Dictionary<string, CQTSubPointKPI>();
        public Dictionary<string, CQTSubPointKPI> SubPointNameDataDic
        {
            get { return subPointNameDataDic; }
        }

        public void AddKPIStatData(FileInfo fileInfo, string subPointName, string floorName, KPIStatDataBase partialStatData)
        {
            StatDataHubBase dataHub = getDataHub(fileInfo.CarrierType);
            if (dataHub==null)
            {
                return;
            }
            addFile(fileInfo);
            dataHub.AddStatData(partialStatData, false);

            if (string.IsNullOrEmpty(subPointName))
            {
                return;
            }
            //子地点统计
            CQTSubPointKPI subPnt = null;
            if (!subPointNameDataDic.TryGetValue(subPointName, out subPnt))
            {
                subPnt = new CQTSubPointKPI(CQTPoint, subPointName);
                subPointNameDataDic.Add(subPointName, subPnt);
            }
            subPnt.AddKPIStatData(floorName, fileInfo, partialStatData);
        }
        
        public void MakeReportData(DevExpress.XtraTreeList.TreeList treeList, CQTKPIReport report)
        {
            ColumnKPIResultDic.Clear();
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIStatResult result = setValidResult(rptCol, true);
                ColumnKPIResultDic.Add(rptCol, result);
            }

            SummaryResultDic.Clear();
            addSummaryResultDic(report, true);

            object[] row = makeRow();
            DevExpress.XtraTreeList.Nodes.TreeListNode mainNode = treeList.AppendNode(row, null);
            mainNode.Tag = this;
            foreach (CQTSubPointKPI subPntData in subPointNameDataDic.Values)//子地点统计
            {
                subPntData.MakeReportData(treeList, mainNode, report);
            }
        }
     


        public void Stat(CQTKPIReport report)
        {
            ColumnKPIResultDic.Clear();
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIStatResult result = setValidResult(rptCol, false);
                ColumnKPIResultDic.Add(rptCol, result);
            }

            SummaryResultDic.Clear();
            addSummaryResultDic(report, false);

            foreach (CQTSubPointKPI subPntData in subPointNameDataDic.Values)//子地点统计
            {
                subPntData.Stat(report);
            }
        }
    }

    public class CQTSubPointKPI : CQTPointKPIData
    {
        public CQTSubPointKPI(CQTPoint point, string name)
        {
            CQTPoint = point;
            Name = name;
        }
        public class ComparerCQTSubPoint : IComparer<CQTSubPointKPI>
        {
            public int Compare(CQTSubPointKPI x, CQTSubPointKPI y)
            {
                return y.Name.CompareTo(x.Name);
            }
        }
        public Dictionary<int, List<CQTFileKPIData>> CarreerFileDataDic { get; set; } = new Dictionary<int, List<CQTFileKPIData>>();
        public List<CQTFileKPIData> cmFileData { get; set; }
        public List<CQTFileKPIData> cuFileData { get; set; }
        public List<CQTFileKPIData> ctFileData { get; set; }

        private readonly Dictionary<string, CQTSubPointKPI> subPointNameDataDic = new Dictionary<string, CQTSubPointKPI>();
        public Dictionary<string, CQTSubPointKPI> SubPointNameDataDic
        {
            get { return subPointNameDataDic; }
        }
        public bool HasChild
        {
            get { return subPointNameDataDic.Count > 0; }
        }

        private List<CQTFileKPIData> getFileDataSet(int carrierID)
        {
            if (carrierID == 1)
            {
                if (cmFileData==null)
                {
                    cmFileData = new List<CQTFileKPIData>();
                }
                return cmFileData;
            }
            else if (carrierID == 2)
            {
                if (cuFileData == null)
                {
                    cuFileData = new List<CQTFileKPIData>();
                }
                return cuFileData;
            }
            else if (carrierID == 3)
            {
                if (ctFileData == null)
                {
                    ctFileData = new List<CQTFileKPIData>();
                }
                return ctFileData;
            }
            return new List<CQTFileKPIData>();
        }

        public void AddKPIStatData(string floorName, FileInfo dataHeader, KPIStatDataBase partialStatData)
        {
            StatDataHubBase dataHub = getDataHub(dataHeader.CarrierType);
            if (dataHub==null)
            {
                return;
            }
            dataHub.AddStatData(partialStatData, false);
            addFile(dataHeader);

            if (string.IsNullOrEmpty(floorName))
            {
                //按文件保存数据
                List<CQTFileKPIData> fileDataList = getFileDataSet(dataHeader.CarrierType);
                if (fileDataList.Count == 0)
                {
                    return;
                }
                bool existFileData = false;
                foreach (CQTFileKPIData item in fileDataList)
                {
                    if (item.DataHeader.ID == dataHeader.ID)
                    {
                        item.AddKPIStatData(partialStatData);
                        existFileData = true;
                        break;
                    }
                }
                if (!existFileData)
                {
                    CQTFileKPIData fileData = new CQTFileKPIData(dataHeader);
                    fileData.AddKPIStatData(partialStatData);
                    fileDataList.Add(fileData);
                }
            }
            else
            {
                //子地点统计
                CQTSubPointKPI subPnt = null;
                if (!subPointNameDataDic.TryGetValue(floorName, out subPnt))
                {
                    subPnt = new CQTSubPointKPI(this.CQTPoint, floorName);
                    subPointNameDataDic.Add(floorName, subPnt);
                }
                subPnt.AddKPIStatData(null, dataHeader, partialStatData);
            }

        }


        public void MakeReportData(DevExpress.XtraTreeList.TreeList treeList, DevExpress.XtraTreeList.Nodes.TreeListNode parentNode, CQTKPIReport report)
        {
            ColumnKPIResultDic.Clear();
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIStatResult result = setValidResult(rptCol, true);
                ColumnKPIResultDic.Add(rptCol, result);
            }

            SummaryResultDic.Clear();
            addSummaryResultDic(report, true);

            object[] row = makeRow();
            DevExpress.XtraTreeList.Nodes.TreeListNode node = treeList.AppendNode(row, parentNode);
            node.Tag = this;
            if (HasChild)
            {
                foreach (CQTSubPointKPI subPntData in subPointNameDataDic.Values)//子地点统计
                {
                    subPntData.MakeReportData(treeList, node, report);
                }
            }
            else
            {
                List<CQTFileKPIData> fileDataList = null;
                if (CarreerFileDataDic.ContainsKey(report.CarreerID))
                {
                    fileDataList = CarreerFileDataDic[report.CarreerID];
                    foreach (CQTFileKPIData fileData in fileDataList)//各文件统计
                    {
                        fileData.MakeReportData(treeList, node, report);
                    }
                }
            }
            
        }

        public void Stat(CQTKPIReport report)
        {
            ColumnKPIResultDic.Clear();
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIStatResult result = setValidResult(rptCol, false);
                ColumnKPIResultDic.Add(rptCol, result);
            }

            SummaryResultDic.Clear();
            addSummaryResultDic(report, false);

            List<CQTFileKPIData> fileDataList = null;
            if (CarreerFileDataDic.ContainsKey(report.CarreerID))
            {
                fileDataList = CarreerFileDataDic[report.CarreerID];
                foreach (CQTFileKPIData fileData in fileDataList)//各文件统计
                {
                    fileData.Stat(report);
                }
            }
        }

    }

    public class CQTFileKPIData
    {
        public Dictionary<CQTKPIReportColumn, CQTKPIStatResult> ColumnKPIResultDic { get; set; } = new Dictionary<CQTKPIReportColumn, CQTKPIStatResult>();
        public Dictionary<CQTKPISummaryColumn, CQTKPIStatResult> SummaryResultDic { get; set; } = new Dictionary<CQTKPISummaryColumn, CQTKPIStatResult>();
        public FileInfo DataHeader { get; set; }
        public DataUnitAreaKPIQuery KPIData { get; set; } = new DataUnitAreaKPIQuery();
        public CQTFileKPIData(FileInfo dataHeader)
        {
            DataHeader = dataHeader;
        }
        public void AddKPIStatData(PartialData partialStatData)
        {
            KPIData.AddStatData(partialStatData);
        }

        public StatDataHubBase DataHub { get; set; } = new StatDataHubBase();
        public void AddKPIStatData(KPIStatDataBase partialStatData)
        {
            DataHub.AddStatData(partialStatData, false);
        }

        public void MakeReportData(DevExpress.XtraTreeList.TreeList treeList, DevExpress.XtraTreeList.Nodes.TreeListNode parentNode, CQTKPIReport report)
        {
            ColumnKPIResultDic.Clear();
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIStatResult result = null;
                string value = KPIData.CalcValueByFormula(rptCol.Formula);
                double dValue = 0;
                if (double.TryParse(value, out dValue))
                {
                    double score;
                    CQTKPIScoreColorRange range = rptCol.ScoreScheme.GetRangeByKPIValue(dValue, out score);
                    if (range != null)
                    {
                        result = new CQTKPIStatResult();
                        result.FileInfo = DataHeader;
                        result.Color = range.Color;
                        result.KPIValue = dValue;
                        result.Score = score;
                    }
                    else
                    {
                        result = new CQTKPIStatResult();
                        result.FileInfo = DataHeader;
                        result.Color = Color.Empty;
                        result.KPIValue = dValue;
                        result.Score = double.NaN;
                    }
                }
                else
                {
                    result = new CQTKPIStatResult();
                    result.FileInfo = DataHeader;
                    result.Color = Color.Empty;
                    result.KPIValue = double.NaN;
                    result.Score = double.NaN;
                }
                ColumnKPIResultDic.Add(rptCol, result);
            }

            SummaryResultDic.Clear();
            addSummaryResultDic(report, true);

            object[] row = makeRow();
            DevExpress.XtraTreeList.Nodes.TreeListNode node = treeList.AppendNode(row, parentNode);
            node.Tag = this;
        }

        public void Stat(CQTKPIReport report)
        {
            ColumnKPIResultDic.Clear();
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIStatResult result = setValidResult(rptCol);
                ColumnKPIResultDic.Add(rptCol, result);
            }

            addSummaryResultDic(report, false);
        }

        private CQTKPIStatResult setValidResult(CQTKPIReportColumn rptCol)
        {
            CQTKPIStatResult result = null;
            string value = KPIData.CalcValueByFormula(rptCol.Formula);
            double dValue = 0;
            if (double.TryParse(value, out dValue))
            {
                result = getResultInfo(rptCol, dValue);
            }
            else
            {
                result = new CQTKPIStatResult();
                result.FileInfo = DataHeader;
                result.Color = Color.Empty;
                result.KPIValue = double.NaN;
                result.Score = double.NaN;
            }

            return result;
        }

        private CQTKPIStatResult getResultInfo(CQTKPIReportColumn rptCol, double dValue)
        {
            CQTKPIStatResult result;
            double score;
            CQTKPIScoreColorRange range = rptCol.ScoreScheme.GetRangeByKPIValue(dValue, out score);
            if (range != null)
            {
                result = new CQTKPIStatResult();
                result.FileInfo = DataHeader;
                result.Color = range.Color;
                result.KPIValue = dValue;
                result.Score = score;
            }
            else
            {
                result = new CQTKPIStatResult();
                result.FileInfo = DataHeader;
                result.Color = Color.Empty;
                result.KPIValue = dValue;
                result.Score = double.NaN;
            }

            return result;
        }

        protected void addSummaryResultDic(CQTKPIReport report, bool isRound)
        {
            foreach (CQTKPISummaryColumn smCol in report.SummaryColumns)
            {
                CQTKPIStatResult result = new CQTKPIStatResult();
                result.KPIValue = double.NaN;
                foreach (SummaryItem item in smCol.SummaryColumns)
                {
                    if (ColumnKPIResultDic.ContainsKey(item.Column))
                    {
                        double colScore = ColumnKPIResultDic[item.Column].Score;
                        if (!double.IsNaN(colScore))
                        {
                            result.Score += colScore * item.Weight;
                        }
                    }
                }
                if (isRound)
                {
                    result.Score = Math.Round(result.Score, 2);
                }
                result.Color = smCol.GetColorByScore(result.Score);
                SummaryResultDic.Add(smCol, result);
            }
        }

        protected object[] makeRow()
        {
            object[] colValueArr = new object[1 + ColumnKPIResultDic.Count+SummaryResultDic.Count];//名字列+指标列+汇总列
            colValueArr[0] = DataHeader.Name;
            int idx = 1;
            foreach (CQTKPIStatResult result in ColumnKPIResultDic.Values)
            {
                colValueArr[idx] = result.KPIValue;
                idx++;
            }
            foreach (CQTKPIStatResult sR in SummaryResultDic.Values)
            {
                colValueArr[idx] = sR.Score;
                idx++;
            }
            return colValueArr;
        }

    }

    public class CQTKPIStatResult
    {
        public FileInfo FileInfo { get; set; }
        public double KPIValue { get; set; }
        public Color Color { get; set; }
        public double Score { get; set; }
    }

}
