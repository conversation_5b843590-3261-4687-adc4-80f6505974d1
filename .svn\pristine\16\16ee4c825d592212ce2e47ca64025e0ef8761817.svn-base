﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTWeakCoverConditionDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numMaxDistance = new System.Windows.Forms.NumericUpDown();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.numDistance = new System.Windows.Forms.NumericUpDown();
            this.numC_I = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.numPccpchRscpThreshold = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numGSMRxlevSub = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numC_I)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchRscpThreshold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGSMRxlevSub)).BeginInit();
            this.SuspendLayout();
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(217, 145);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 35;
            this.label7.Text = "米";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(21, 145);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 34;
            this.label6.Text = "相邻采样点距离≤";
            // 
            // numMaxDistance
            // 
            this.numMaxDistance.Location = new System.Drawing.Point(128, 141);
            this.numMaxDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxDistance.Name = "numMaxDistance";
            this.numMaxDistance.Size = new System.Drawing.Size(81, 21);
            this.numMaxDistance.TabIndex = 33;
            this.numMaxDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(158, 193);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(76, 23);
            this.btnCancel.TabIndex = 32;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(76, 193);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(76, 23);
            this.btnOK.TabIndex = 31;
            this.btnOK.Text = "确认";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(217, 113);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 30;
            this.label4.Text = "米";
            // 
            // numDistance
            // 
            this.numDistance.Location = new System.Drawing.Point(128, 109);
            this.numDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDistance.Name = "numDistance";
            this.numDistance.Size = new System.Drawing.Size(81, 21);
            this.numDistance.TabIndex = 28;
            this.numDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // numC_I
            // 
            this.numC_I.Location = new System.Drawing.Point(128, 75);
            this.numC_I.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numC_I.Name = "numC_I";
            this.numC_I.Size = new System.Drawing.Size(81, 21);
            this.numC_I.TabIndex = 27;
            this.numC_I.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numC_I.Value = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(57, 113);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 26;
            this.label2.Text = "持续距离≥";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(81, 79);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 25;
            this.label1.Text = "EcIo <";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(81, 49);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(41, 12);
            this.label5.TabIndex = 25;
            this.label5.Text = "RSCP <";
            // 
            // numPccpchRscpThreshold
            // 
            this.numPccpchRscpThreshold.Location = new System.Drawing.Point(128, 45);
            this.numPccpchRscpThreshold.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numPccpchRscpThreshold.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numPccpchRscpThreshold.Name = "numPccpchRscpThreshold";
            this.numPccpchRscpThreshold.Size = new System.Drawing.Size(81, 21);
            this.numPccpchRscpThreshold.TabIndex = 27;
            this.numPccpchRscpThreshold.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numPccpchRscpThreshold.Value = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(214, 49);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(23, 12);
            this.label8.TabIndex = 29;
            this.label8.Text = "dBm";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(39, 17);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(83, 12);
            this.label3.TabIndex = 25;
            this.label3.Text = "GSMRxLevSub <";
            // 
            // numGSMRxlevSub
            // 
            this.numGSMRxlevSub.Location = new System.Drawing.Point(128, 13);
            this.numGSMRxlevSub.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numGSMRxlevSub.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numGSMRxlevSub.Name = "numGSMRxlevSub";
            this.numGSMRxlevSub.Size = new System.Drawing.Size(81, 21);
            this.numGSMRxlevSub.TabIndex = 27;
            this.numGSMRxlevSub.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGSMRxlevSub.Value = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(214, 17);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(23, 12);
            this.label9.TabIndex = 29;
            this.label9.Text = "dBm";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(215, 79);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(17, 12);
            this.label10.TabIndex = 36;
            this.label10.Text = "dB";
            // 
            // ZTWeakCoverConditionDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(264, 239);
            this.Controls.Add(this.label10);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.numMaxDistance);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label9);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.numDistance);
            this.Controls.Add(this.numGSMRxlevSub);
            this.Controls.Add(this.numPccpchRscpThreshold);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.numC_I);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ZTWeakCoverConditionDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "WCDMA弱覆盖设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numC_I)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchRscpThreshold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGSMRxlevSub)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numMaxDistance;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numDistance;
        private System.Windows.Forms.NumericUpDown numC_I;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numPccpchRscpThreshold;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numGSMRxlevSub;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label10;
    }
}