<?xml version="1.0" encoding="UTF-8"?>
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<Configs>
	<Config name="StatParamCfg">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">基础信息</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">区域片区 kRegional</Item>
						<Item typeName="String" key="FName">kRegional</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">测试时间 kTimeValue</Item>
						<Item typeName="String" key="FName">kTimeValue</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">设备类型 kEqpId</Item>
						<Item typeName="String" key="FName">kEqpId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">文件类型 kFileTypeId</Item>
						<Item typeName="String" key="FName">kFileTypeId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">业务类型 kSvTypeId</Item>
						<Item typeName="String" key="FName">kSvTypeId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">运营商类型 kCarrierId</Item>
						<Item typeName="String" key="FName">kCarrierId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">项目类型 kProjId</Item>
						<Item typeName="String" key="FName">kProjId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">所属轮次 kRound</Item>
						<Item typeName="String" key="FName">kRound</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">语音GSM参数Idle模式</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">测试时长 viDuration</Item>
						<Item typeName="String" key="FName">viDuration</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">测试距离 viDistance</Item>
						<Item typeName="String" key="FName">viDistance</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">采样点总数 viSampleTotle</Item>
						<Item typeName="String" key="FName">viSampleTotle</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">信号强度</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-10,-45]的数目 viRxLev[0]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-46,-50]的数目 viRxLev[1]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-51,-55]的数目 viRxLev[2]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-56,-60]的数目 viRxLev[3]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-61,-65]的数目 viRxLev[4]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-66,-70]的数目 viRxLev[5]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">5</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-71,-75]的数目 viRxLev[6]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">6</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-76,-80]的数目 viRxLev[7]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">7</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-81db的数目 viRxLev[8]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">8</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-82db的数目 viRxLev[9]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">9</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-83db的数目 viRxLev[10]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">10</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-84db的数目 viRxLev[11]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">11</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-85db的数目 viRxLev[12]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">12</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-86db的数目 viRxLev[13]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">13</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-87db的数目 viRxLev[14]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">14</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-88db的数目 viRxLev[15]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">15</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-89db的数目 viRxLev[16]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">16</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-90db的数目 viRxLev[17]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">17</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-91db的数目 viRxLev[18]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">18</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-92db的数目 viRxLev[19]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">19</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-93db的数目 viRxLev[20]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">20</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-94db的数目 viRxLev[21]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">21</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-95db的数目 viRxLev[22]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">22</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-96db的数目 viRxLev[23]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">23</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-97db的数目 viRxLev[24]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">24</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-98db的数目 viRxLev[25]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">25</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-99,-120]的数目 viRxLev[26]</Item>
								<Item typeName="String" key="FName">viRxLev</Item>
								<Item typeName="Int32" key="FTag">26</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">语音GSM参数Dedicated模式</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">测试时长 vdDuration</Item>
						<Item typeName="String" key="FName">vdDuration</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">测试距离 vdDistance</Item>
						<Item typeName="String" key="FName">vdDistance</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">采样点总数 vdSampleTotle</Item>
						<Item typeName="String" key="FName">vdSampleTotle</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">信号强度</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-10,-45]的数目 vdRxLev[0]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-46,-50]的数目 vdRxLev[1]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-51,-55]的数目 vdRxLev[2]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-56,-60]的数目 vdRxLev[3]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-61,-65]的数目 vdRxLev[4]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-66,-70]的数目 vdRxLev[5]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">5</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-71,-75]的数目 vdRxLev[6]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">6</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-76,-80]的数目 vdRxLev[7]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">7</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-81db的数目 vdRxLev[8]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">8</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-82db的数目 vdRxLev[9]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">9</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-83db的数目 vdRxLev[10]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">10</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-84db的数目 vdRxLev[11]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">11</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-85db的数目 vdRxLev[12]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">12</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-86db的数目 vdRxLev[13]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">13</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-87db的数目 vdRxLev[14]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">14</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-88db的数目 vdRxLev[15]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">15</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-89db的数目 vdRxLev[16]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">16</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-90db的数目 vdRxLev[17]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">17</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-91db的数目 vdRxLev[18]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">18</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-92db的数目 vdRxLev[19]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">19</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-93db的数目 vdRxLev[20]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">20</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-94db的数目 vdRxLev[21]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">21</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-95db的数目 vdRxLev[22]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">22</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-96db的数目 vdRxLev[23]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">23</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-97db的数目 vdRxLev[24]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">24</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-98db的数目 vdRxLev[25]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">25</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-99,-120]的数目 vdRxLev[26]</Item>
								<Item typeName="String" key="FName">vdRxLev</Item>
								<Item typeName="Int32" key="FTag">26</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">信号质量</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">信号质量0数量 vdRxQual[0]</Item>
								<Item typeName="String" key="FName">vdRxQual</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">信号质量1数量 vdRxQual[1]</Item>
								<Item typeName="String" key="FName">vdRxQual</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">信号质量2数量 vdRxQual[2]</Item>
								<Item typeName="String" key="FName">vdRxQual</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">信号质量3数量 vdRxQual[3]</Item>
								<Item typeName="String" key="FName">vdRxQual</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">信号质量4数量 vdRxQual[4]</Item>
								<Item typeName="String" key="FName">vdRxQual</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">信号质量5数量 vdRxQual[5]</Item>
								<Item typeName="String" key="FName">vdRxQual</Item>
								<Item typeName="Int32" key="FTag">5</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">信号质量6数量 vdRxQual[6]</Item>
								<Item typeName="String" key="FName">vdRxQual</Item>
								<Item typeName="Int32" key="FTag">6</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">信号质量7数量 vdRxQual[7]</Item>
								<Item typeName="String" key="FName">vdRxQual</Item>
								<Item typeName="Int32" key="FTag">7</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">PESQ</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ[0~1]数量 vdPesq[0]</Item>
								<Item typeName="String" key="FName">vdPesq</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(1~2]数量 vdPesq[1]</Item>
								<Item typeName="String" key="FName">vdPesq</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(2~2.5]数量 vdPesq[2]</Item>
								<Item typeName="String" key="FName">vdPesq</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(2.5~2.8]数量 vdPesq[3]</Item>
								<Item typeName="String" key="FName">vdPesq</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(2.8~3]数量 vdPesq[4]</Item>
								<Item typeName="String" key="FName">vdPesq</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(3~3.3]数量 vdPesq[5]</Item>
								<Item typeName="String" key="FName">vdPesq</Item>
								<Item typeName="Int32" key="FTag">5</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(3.3~4]数量 vdPesq[6]</Item>
								<Item typeName="String" key="FName">vdPesq</Item>
								<Item typeName="Int32" key="FTag">6</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(4.5]数量 vdPesq[7]</Item>
								<Item typeName="String" key="FName">vdPesq</Item>
								<Item typeName="Int32" key="FTag">7</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">PESQ Value</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ[0~1]值之和 vdPesqValue[0]</Item>
								<Item typeName="String" key="FName">vdPesqValue</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(1~2]值之和 vdPesqValue[1]</Item>
								<Item typeName="String" key="FName">vdPesqValue</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(2~2.5]值之和 vdPesqValue[2]</Item>
								<Item typeName="String" key="FName">vdPesqValue</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(2.5~2.8]值之和 vdPesqValue[3]</Item>
								<Item typeName="String" key="FName">vdPesqValue</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(2.8~3]值之和 vdPesqValue[4]</Item>
								<Item typeName="String" key="FName">vdPesqValue</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(3~3.3]值之和 vdPesqValue[5]</Item>
								<Item typeName="String" key="FName">vdPesqValue</Item>
								<Item typeName="Int32" key="FTag">5</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(3.3~4]值之和 vdPesqValue[6]</Item>
								<Item typeName="String" key="FName">vdPesqValue</Item>
								<Item typeName="Int32" key="FTag">6</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ(4.5]值之和 vdPesqValue[7]</Item>
								<Item typeName="String" key="FName">vdPesqValue</Item>
								<Item typeName="Int32" key="FTag">7</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TA</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为0的数目 vdTA[0]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为1的数目 vdTA[1]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为2的数目 vdTA[2]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为3的数目 vdTA[3]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为4的数目 vdTA[4]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为5的数目 vdTA[5]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">5</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为6的数目 vdTA[6]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">6</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为7的数目 vdTA[7]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">7</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为8的数目 vdTA[8]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">8</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为9的数目 vdTA[9]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">9</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为10的数目 vdTA[10]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">10</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为11到15的采样点数目 vdTA[11]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">11</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为16到20的采样点数目 vdTA[12]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">12</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为21到30的采样点数目 vdTA[13]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">13</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为31到40的采样点数目 vdTA[14]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">14</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为41到64的采样点数目 vdTA[15]</Item>
								<Item typeName="String" key="FName">vdTA</Item>
								<Item typeName="Int32" key="FTag">15</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为11到15的采样点值之和 vdTAValue[11]</Item>
								<Item typeName="String" key="FName">vdTAValue</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为16到20的采样点值之和 vdTAValue[12]</Item>
								<Item typeName="String" key="FName">vdTAValue</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为21到30的采样点值之和 vdTAValue[13]</Item>
								<Item typeName="String" key="FName">vdTAValue</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为31到40的采样点值之和 vdTAValue[14]</Item>
								<Item typeName="String" key="FName">vdTAValue</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA值为41到64的采样点值之和 vdTAValue[15]</Item>
								<Item typeName="String" key="FName">vdTAValue</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">语音编码</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">编码个数 vdSpeechCodec_FR</Item>
								<Item typeName="String" key="FName">vdSpeechCodec_FR</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">编码个数 vdSpeechCodec_HR</Item>
								<Item typeName="String" key="FName">vdSpeechCodec_HR</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">编码个数 vdSpeechCodec_EFR</Item>
								<Item typeName="String" key="FName">vdSpeechCodec_EFR</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">编码个数 vdSpeechCodec_AMR</Item>
								<Item typeName="String" key="FName">vdSpeechCodec_AMR</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">编码时长 vdSpeechCodecTime_FR</Item>
								<Item typeName="String" key="FName">vdSpeechCodecTime_FR</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">编码时长 vdSpeechCodecTime_HR</Item>
								<Item typeName="String" key="FName">vdSpeechCodecTime_HR</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">编码时长 vdSpeechCodecTime_EFR</Item>
								<Item typeName="String" key="FName">vdSpeechCodecTime_EFR</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">编码时长 vdSpeechCodecTime_AMR</Item>
								<Item typeName="String" key="FName">vdSpeechCodecTime_AMR</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">C/I指标</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[-5,9)个数 vdCIWorst</Item>
								<Item typeName="String" key="FName">vdCIWorst</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[9,12)个数 vdCIWorst</Item>
								<Item typeName="String" key="FName">vdCIWorst</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[12,15)个数 vdCIWorst</Item>
								<Item typeName="String" key="FName">vdCIWorst</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[15,20)个数 vdCIWorst</Item>
								<Item typeName="String" key="FName">vdCIWorst</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[20,35]个数 vdCIWorst</Item>
								<Item typeName="String" key="FName">vdCIWorst</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[-5,9)值之和 vdCIWorstValue</Item>
								<Item typeName="String" key="FName">vdCIWorstValue</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[9,12)值之和 vdCIWorstValue</Item>
								<Item typeName="String" key="FName">vdCIWorstValue</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[12,15)值之和 vdCIWorstValue</Item>
								<Item typeName="String" key="FName">vdCIWorstValue</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[15,20)值之和 vdCIWorstValue</Item>
								<Item typeName="String" key="FName">vdCIWorstValue</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[20,35]值之和 vdCIWorstValue</Item>
								<Item typeName="String" key="FName">vdCIWorstValue</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[-5,9)个数 vdCIAvg</Item>
								<Item typeName="String" key="FName">vdCIAvg</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[9,12)个数 vdCIAvg</Item>
								<Item typeName="String" key="FName">vdCIAvg</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[12,15)个数 vdCIAvg</Item>
								<Item typeName="String" key="FName">vdCIAvg</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[15,20)个数 vdCIAvg</Item>
								<Item typeName="String" key="FName">vdCIAvg</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[20,35]个数 vdCIAvg</Item>
								<Item typeName="String" key="FName">vdCIAvg</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[-5,9)值之和 vdCIAvgValue</Item>
								<Item typeName="String" key="FName">vdCIAvgValue</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[9,12)值之和 vdCIAvgValue</Item>
								<Item typeName="String" key="FName">vdCIAvgValue</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[12,15)值之和 vdCIAvgValue</Item>
								<Item typeName="String" key="FName">vdCIAvgValue</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[15,20)值之和 vdCIAvgValue</Item>
								<Item typeName="String" key="FName">vdCIAvgValue</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I[20,35]值之和 vdCIAvgValue</Item>
								<Item typeName="String" key="FName">vdCIAvgValue</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">SQI指标</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SQI[-20~-10)个数 vdSQI</Item>
								<Item typeName="String" key="FName">vdSQI</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SQI[-10~-0)个数 vdSQI</Item>
								<Item typeName="String" key="FName">vdSQI</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SQI[0~10)个数 vdSQI</Item>
								<Item typeName="String" key="FName">vdSQI</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SQI[10~20)个数 vdSQI</Item>
								<Item typeName="String" key="FName">vdSQI</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SQI[20~30)个数 vdSQI</Item>
								<Item typeName="String" key="FName">vdSQI</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SQIValue[-20~-10)值之和 vdSQIValue</Item>
								<Item typeName="String" key="FName">vdSQIValue</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SQIValue[-10~-0)值之和 vdSQIValue</Item>
								<Item typeName="String" key="FName">vdSQIValue</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SQIValue[0~10)值之和 vdSQIValue</Item>
								<Item typeName="String" key="FName">vdSQIValue</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SQIValue[10~20)值之和 vdSQIValue</Item>
								<Item typeName="String" key="FName">vdSQIValue</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SQIValue[20~30)值之和 vdSQIValue</Item>
								<Item typeName="String" key="FName">vdSQIValue</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">GSM事件参数</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MO_Call_Attempt 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">0</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MT_Call_Attempt 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Call_Attempt_Retry 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">2</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MO_Call_Established 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">3</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MT_Call_Established 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">4</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MO_Drop_Call 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">5</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MT_Drop_Call 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">6</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MO_Call_Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">7</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MT_Call_Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">8</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MO_Block_Call 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">9</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MT_Block_Call 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">10</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MO_Call_End 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">11</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MT_Call_End 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">12</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MO_Call_Setup 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">13</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">cm service request时差</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">13</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">channelrequest时差</Item>
								<Item typeName="String" key="FName">value2</Item>
								<Item typeName="Int32" key="FTag">13</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MT_Call_Setup 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">14</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Paging Response时差</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">14</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">channelrequest时差</Item>
								<Item typeName="String" key="FName">value2</Item>
								<Item typeName="Int32" key="FTag">14</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Handover_Command 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">15</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Handover_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">16</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Handover_Failure 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">17</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Location_Area_Update_Request 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">18</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Location_Area_Update_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">19</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Location_Area_Update_Failure 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">20</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">GPRS_Attach_Request 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">21</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">GPRS_Attach_Accept 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">22</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">GPRS_Attach_Failure 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">23</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">GPRS Detach Request 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">24</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">GPRS Detach Accept 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">25</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Routing_Area_Update_Request 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">26</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Routing_Area_Update_Accept 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">27</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Routing_Area_Update_Reject 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">28</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Active_PDP_Context_Request 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">29</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Active_PDP_Context_Accept 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">30</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PDP激活时间</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">30</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Active_PDP_Context_Reject 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">31</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Request_PDP_Context_Activation 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">32</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Request_PDP_Context_Activation_Reject 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">33</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Deactive_PDP_Context_Request 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">34</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Deactive_PDP_Context_Accept 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">35</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Modify_PDP_Context_Request 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">36</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Modify_PDP_Context_Accept 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">37</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Modify_PDP_Context_Reject 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">38</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Cell_Reselection 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">39</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Weak Coverage 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">40</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MoMtDrop 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">41</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Weak Quality 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">42</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">GPRSDrop 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">43</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WAP_Logon_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">44</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP登陆时间</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">44</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WAP_Logon_Failure 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">45</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WAP_Page_Refresh_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">46</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP刷新时间</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">46</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WAP_Page_Refresh_Failure 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">47</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WAP_Download_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">48</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP下载时间</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">48</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP下载字节数</Item>
								<Item typeName="String" key="FName">value2</Item>
								<Item typeName="Int32" key="FTag">48</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WAP_Download_Failure 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">49</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WAP_Upload_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">50</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP上传时间</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">50</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP上传字节数</Item>
								<Item typeName="String" key="FName">value2</Item>
								<Item typeName="Int32" key="FTag">50</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WAP_Upload_Failure 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">51</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WAP_Kjava_Download_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">52</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP下载时间</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">52</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP下载字节数</Item>
								<Item typeName="String" key="FName">value2</Item>
								<Item typeName="Int32" key="FTag">53</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WAP_Kjava_Download_Failure 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">53</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Ping_success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">54</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ping时延</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">54</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Ping_fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">55</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">FTP_Download_Begin 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">56</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">FTP_Download_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">57</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP下载时间</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">57</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP下载字节数</Item>
								<Item typeName="String" key="FName">value2</Item>
								<Item typeName="Int32" key="FTag">57</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">FTP_Download_Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">58</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">FTP_Upload_Begin 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">59</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">FTP_Upload_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">60</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP上传时间</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">60</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP上传字节数</Item>
								<Item typeName="String" key="FName">value2</Item>
								<Item typeName="Int32" key="FTag">60</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">FTP_Upload_Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">61</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MMS_Send_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">62</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MMS_Send_Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">63</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MMS_Push 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">64</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PUSH时延</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">64</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MMS_Retrieve_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">65</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MMS_Retrieve_Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">66</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MMS_P2P_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">67</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MMS_P2P_Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">68</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">SMS_Send_Out 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">69</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">发送时延</Item>
								<Item typeName="String" key="FName">value1</Item>
								<Item typeName="Int32" key="FTag">69</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">SMS_Send_Timeout 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">70</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">SMS_Receieved 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">71</Item>
						<Item typeName="IList" key="children"/>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">TD-SCDMA AMR参数</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">T网占用时长 tdaDurationTD</Item>
						<Item typeName="String" key="FName">tdaDurationTD</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">G网占用时长 tdaDurationGSM</Item>
						<Item typeName="String" key="FName">tdaDurationGSM</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">T网测试距离 tdaDistanceTD</Item>
						<Item typeName="String" key="FName">tdaDistanceTD</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">G网测试距离 tdaDistanceGSM</Item>
						<Item typeName="String" key="FName">tdaDistanceGSM</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">BLER采样点数 tdaBler_SampleNum</Item>
						<Item typeName="String" key="FName">tdaBler_SampleNum</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">BLER总数 tdaBler_Total</Item>
						<Item typeName="String" key="FName">tdaBler_Total</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">BLER错误总数 tdaBler_Err</Item>
						<Item typeName="String" key="FName">tdaBler_Err</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">PCCPCH采样点数 tdaPCCPCH_RSCPCI_SampleNum</Item>
						<Item typeName="String" key="FName">tdaPCCPCH_RSCPCI_SampleNum</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">PCCPCH RSCP>-95 C/I>-3数量 tdaPCCPCH_RSCP_CI_95Sample</Item>
						<Item typeName="String" key="FName">tdaPCCPCH_RSCP_CI_95Sample</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">PCCPCH RSCP>-90 C/I>-3数量 tdaPCCPCH_RSCP_CI_90Sample</Item>
						<Item typeName="String" key="FName">tdaPCCPCH_RSCP_CI_90Sample</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">DPCH_C/I采样点数 tdaDPCH_C2I_SampleNum</Item>
						<Item typeName="String" key="FName">tdaDPCH_C2I_SampleNum</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">DPCH C/I>-3采样点数 tdaDPCH_C2I_F3_Sample</Item>
						<Item typeName="String" key="FName">tdaDPCH_C2I_F3_Sample</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">Rxlev采样点数 tdaRxlev_SampleNum</Item>
						<Item typeName="String" key="FName">tdaRxlev_SampleNum</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">RxLev[-10~-75] tdaRxlev_F75</Item>
						<Item typeName="String" key="FName">tdaRxlev_F75</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">RxLev(-75,-80]  tdaRxlev_F80</Item>
						<Item typeName="String" key="FName">tdaRxlev_F80</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">RxLev(-80,-85] tdaRxlev_F85</Item>
						<Item typeName="String" key="FName">tdaRxlev_F85</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">RxLev(-85,-90] tdaRxlev_F90</Item>
						<Item typeName="String" key="FName">tdaRxlev_F90</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">RxLev(-90,-94] tdaRxlev_F94</Item>
						<Item typeName="String" key="FName">tdaRxlev_F94</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">RxLev(-94,-100] tdaRxlev_F100</Item>
						<Item typeName="String" key="FName">tdaRxlev_F100</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MOS采样点数 tdaMOS_Sample</Item>
						<Item typeName="String" key="FName">tdaMOS_Sample</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">MOS值总量  tdaMOS_Total</Item>
						<Item typeName="String" key="FName">tdaMOS_Total</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">TD-SCDMA VP参数</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">T网占用时长 tdvDuration</Item>
						<Item typeName="String" key="FName">tdvDuration</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">T网测试距离 tdvDistance</Item>
						<Item typeName="String" key="FName">tdvDistance</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">BLER采样点数,R4协议 tdvBler_SampleNum</Item>
						<Item typeName="String" key="FName">tdvBler_SampleNum</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">BLER总数,R4协议 tdvBler_Total</Item>
						<Item typeName="String" key="FName">tdvBler_Total</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">BLER错误总数 tdvBler_Err</Item>
						<Item typeName="String" key="FName">tdvBler_Err</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
				</Item>
			</Item>
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">TDSCDMA事件参数</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MO_CallAttempt 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">100</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MO_Established 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">101</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MO_Setup 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">102</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">接入时长(rrcConnectionRequest到Alerting的时间毫秒)</Item>
								<Item typeName="String" key="FName">value3</Item>
								<Item typeName="Int32" key="FTag">102</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MO_Disconnect 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">103</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MO_CallFail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">104</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MO_Drop 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">105</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MO_CallAttempt 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">106</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MO_Established 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">107</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MO_Setup 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">108</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MO_Disconnect 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">109</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MO_CallFail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">110</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MO_Drop 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">111</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MT_CallAttempt 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">112</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MT_Established 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">113</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MT_Setup 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">114</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">接入时长(rrcConnectionRequest到Alerting的时间毫秒)</Item>
								<Item typeName="String" key="FName">value3</Item>
								<Item typeName="Int32" key="FTag">114</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MT_Disconnect 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">115</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MT_CallFail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">116</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_MT_Drop 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">117</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MT_CallAttempt 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">118</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MT_Established 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">119</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MT_Setup 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">120</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">接入时长(ChannelRequest到Alerting的时间毫秒)</Item>
								<Item typeName="String" key="FName">value3</Item>
								<Item typeName="Int32" key="FTag">120</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MT_Disconnect 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">121</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MT_CallFail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">122</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_MT_Drop 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">123</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_LocationUpdate_Request 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">124</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_LocationUpdate_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">125</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_LocationUpdate_Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">126</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_LocationUpdate_Request 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">127</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_LocationUpdate_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">128</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_LocationUpdate_Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">129</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_RAU_Request 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">130</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_RAU_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">131</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_RAU_Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">132</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_RAU_Request 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">133</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_RAU_Success 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">134</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GSM_RAU_Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">135</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_CellReselection_T2G 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">136</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选时延</Item>
								<Item typeName="String" key="FName">value5</Item>
								<Item typeName="Int32" key="FTag">136</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选前电平平均值</Item>
								<Item typeName="String" key="FName">value6</Item>
								<Item typeName="Int32" key="FTag">136</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选后电平平均值</Item>
								<Item typeName="String" key="FName">value7</Item>
								<Item typeName="Int32" key="FTag">136</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_CellReselection_Fail_T2G 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">137</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_CellReselection_G2T 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">138</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选时延</Item>
								<Item typeName="String" key="FName">value5</Item>
								<Item typeName="Int32" key="FTag">138</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选前电平平均值</Item>
								<Item typeName="String" key="FName">value6</Item>
								<Item typeName="Int32" key="FTag">138</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选后电平平均值</Item>
								<Item typeName="String" key="FName">value7</Item>
								<Item typeName="Int32" key="FTag">138</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_CellReselection_Fail_G2T 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">139</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverRequest_T2G 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">140</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverSuccess_T2G 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">141</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换时延</Item>
								<Item typeName="String" key="FName">value5</Item>
								<Item typeName="Int32" key="FTag">141</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换前电平平均值</Item>
								<Item typeName="String" key="FName">value6</Item>
								<Item typeName="Int32" key="FTag">141</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换后电平平均值</Item>
								<Item typeName="String" key="FName">value7</Item>
								<Item typeName="Int32" key="FTag">141</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverFail_T2G  数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">142</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverRequest_IntraT 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">143</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverSuccess_IntraT 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">144</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换时延</Item>
								<Item typeName="String" key="FName">value5</Item>
								<Item typeName="Int32" key="FTag">144</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换前电平平均值</Item>
								<Item typeName="String" key="FName">value6</Item>
								<Item typeName="Int32" key="FTag">144</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换后电平平均值</Item>
								<Item typeName="String" key="FName">value7</Item>
								<Item typeName="Int32" key="FTag">144</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverFail_IntraT 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">145</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverRequest_Baton 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">146</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverSuccess_Baton 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">147</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换时延</Item>
								<Item typeName="String" key="FName">value5</Item>
								<Item typeName="Int32" key="FTag">147</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换前电平平均值</Item>
								<Item typeName="String" key="FName">value6</Item>
								<Item typeName="Int32" key="FTag">147</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换后电平平均值</Item>
								<Item typeName="String" key="FName">value7</Item>
								<Item typeName="Int32" key="FTag">147</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverFail_Baton 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">148</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverRequest_IntraG 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">149</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverSuccess_IntraG 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">150</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换时延</Item>
								<Item typeName="String" key="FName">value5</Item>
								<Item typeName="Int32" key="FTag">150</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换前电平平均值</Item>
								<Item typeName="String" key="FName">value6</Item>
								<Item typeName="Int32" key="FTag">150</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">切换后电平平均值</Item>
								<Item typeName="String" key="FName">value7</Item>
								<Item typeName="Int32" key="FTag">150</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_HandoverFail_IntraG 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">151</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_R4_DataDrop 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">152</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_GPRSEDGE_DataDrop 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">153</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MO_CallAttempt 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">154</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MO_Cir_Setup 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">155</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MO_VP_Setup 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">156</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MO_Disonnect 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">157</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MO_Drop 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">158</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MT_CallAttempt 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">159</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MT_CirConnect  数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">160</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MT_VP_Connect  数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">161</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MT_Disonnect 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">162</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MT_Drop 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">163</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_PPP_Dial_Start 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">164</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_PPP_Dial_Success  数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">165</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_FTP_Download_Connect 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">166</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_FTP_Send_RETR 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">167</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_FTP_Download_First_Data 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">168</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_FTP_Download_Last_Data 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">169</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_FTP_Download_Disconnect 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">170</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_FTP_Download_Drop 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">171</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">RRC Connection Completed 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">172</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">RAB Setup 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">173</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">RAB Setup Completed 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">174</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">RAB Setup Fail 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">175</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_CellUPdate数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">176</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_CellUPdateConfirm 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">177</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_CellReselection_T2T 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">178</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选时延</Item>
								<Item typeName="String" key="FName">value5</Item>
								<Item typeName="Int32" key="FTag">178</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选前电平平均值</Item>
								<Item typeName="String" key="FName">value6</Item>
								<Item typeName="Int32" key="FTag">178</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选后电平平均值</Item>
								<Item typeName="String" key="FName">value7</Item>
								<Item typeName="Int32" key="FTag">178</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_CellReselection_Fail_T2T 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">179</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_CellReselection_G2G 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">180</Item>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选时延</Item>
								<Item typeName="String" key="FName">value5</Item>
								<Item typeName="Int32" key="FTag">180</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选前电平平均值</Item>
								<Item typeName="String" key="FName">value6</Item>
								<Item typeName="Int32" key="FTag">180</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">重选后电平平均值</Item>
								<Item typeName="String" key="FName">value7</Item>
								<Item typeName="Int32" key="FTag">180</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_CellReselection_Fail_G2G 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">181</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MO_Connect 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">182</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MO_Failed 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">183</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MT_Connect 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">184</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD_VP_MT_Failed 数量</Item>
						<Item typeName="String" key="FName">evtIdCount</Item>
						<Item typeName="Int32" key="FTag">185</Item>
						<Item typeName="IList" key="children"/>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
</Configs>
