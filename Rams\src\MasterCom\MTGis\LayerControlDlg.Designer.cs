﻿namespace MasterCom.MTGis
{
    partial class LayerControlDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(LayerControlDlg));
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.ColVisible = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.ColName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColLabel = new System.Windows.Forms.DataGridViewImageColumn();
            this.ColNote = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.tabMain = new System.Windows.Forms.TabControl();
            this.tpBase = new System.Windows.Forms.TabPage();
            this.tpCustom = new System.Windows.Forms.TabPage();
            this.imageList = new System.Windows.Forms.ImageList(this.components);
            this.splitMain = new System.Windows.Forms.SplitContainer();
            this.toolStripSetting = new System.Windows.Forms.ToolStrip();
            this.tsBtnNewSpace = new System.Windows.Forms.ToolStripButton();
            this.btnSaveIt = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.tsBtnAddLayer = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.tsBtnRemoveLayer = new System.Windows.Forms.ToolStripButton();
            this.miBtnUp = new System.Windows.Forms.ToolStripButton();
            this.miBtnDown = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.btnAddExcelPointLayer = new System.Windows.Forms.ToolStripButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnApply = new DevExpress.XtraEditors.SimpleButton();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.tabMain.SuspendLayout();
            this.splitMain.Panel1.SuspendLayout();
            this.splitMain.Panel2.SuspendLayout();
            this.splitMain.SuspendLayout();
            this.toolStripSetting.SuspendLayout();
            this.SuspendLayout();
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ColVisible,
            this.ColName,
            this.ColLabel,
            this.ColNote});
            this.dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView.Location = new System.Drawing.Point(0, 25);
            this.dataGridView.MultiSelect = false;
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.ReadOnly = true;
            this.dataGridView.RowHeadersVisible = false;
            this.dataGridView.RowTemplate.Height = 20;
            this.dataGridView.RowTemplate.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView.Size = new System.Drawing.Size(679, 188);
            this.dataGridView.TabIndex = 0;
            this.dataGridView.VirtualMode = true;
            this.dataGridView.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView_CellContentClick);
            this.dataGridView.CellValueNeeded += new System.Windows.Forms.DataGridViewCellValueEventHandler(this.dataGridView_CellValueNeeded);
            this.dataGridView.SelectionChanged += new System.EventHandler(this.dataGridView_SelectionChanged);
            // 
            // ColVisible
            // 
            this.ColVisible.HeaderText = "显示";
            this.ColVisible.Name = "ColVisible";
            this.ColVisible.ReadOnly = true;
            this.ColVisible.Width = 50;
            // 
            // ColName
            // 
            this.ColName.HeaderText = "名称";
            this.ColName.Name = "ColName";
            this.ColName.ReadOnly = true;
            this.ColName.Width = 150;
            // 
            // ColLabel
            // 
            this.ColLabel.HeaderText = "标注";
            this.ColLabel.Name = "ColLabel";
            this.ColLabel.ReadOnly = true;
            this.ColLabel.Width = 50;
            // 
            // ColNote
            // 
            this.ColNote.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.ColNote.HeaderText = "其他";
            this.ColNote.Name = "ColNote";
            this.ColNote.ReadOnly = true;
            // 
            // tabMain
            // 
            this.tabMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabMain.Controls.Add(this.tpBase);
            this.tabMain.Controls.Add(this.tpCustom);
            this.tabMain.Location = new System.Drawing.Point(0, 2);
            this.tabMain.Name = "tabMain";
            this.tabMain.SelectedIndex = 0;
            this.tabMain.Size = new System.Drawing.Size(679, 414);
            this.tabMain.TabIndex = 1;
            // 
            // tpBase
            // 
            this.tpBase.AutoScroll = true;
            this.tpBase.Location = new System.Drawing.Point(4, 23);
            this.tpBase.Name = "tpBase";
            this.tpBase.Padding = new System.Windows.Forms.Padding(3);
            this.tpBase.Size = new System.Drawing.Size(671, 387);
            this.tpBase.TabIndex = 0;
            this.tpBase.Text = "基础设置";
            this.tpBase.UseVisualStyleBackColor = true;
            // 
            // tpCustom
            // 
            this.tpCustom.AutoScroll = true;
            this.tpCustom.Location = new System.Drawing.Point(4, 23);
            this.tpCustom.Name = "tpCustom";
            this.tpCustom.Padding = new System.Windows.Forms.Padding(3);
            this.tpCustom.Size = new System.Drawing.Size(671, 387);
            this.tpCustom.TabIndex = 1;
            this.tpCustom.Text = "特殊设置";
            this.tpCustom.UseVisualStyleBackColor = true;
            // 
            // imageList
            // 
            this.imageList.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList.ImageStream")));
            this.imageList.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList.Images.SetKeyName(0, "label_show.gif");
            this.imageList.Images.SetKeyName(1, "label_no.gif");
            // 
            // splitMain
            // 
            this.splitMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitMain.Location = new System.Drawing.Point(0, 0);
            this.splitMain.Name = "splitMain";
            this.splitMain.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitMain.Panel1
            // 
            this.splitMain.Panel1.Controls.Add(this.dataGridView);
            this.splitMain.Panel1.Controls.Add(this.toolStripSetting);
            // 
            // splitMain.Panel2
            // 
            this.splitMain.Panel2.Controls.Add(this.btnOK);
            this.splitMain.Panel2.Controls.Add(this.btnApply);
            this.splitMain.Panel2.Controls.Add(this.tabMain);
            this.splitMain.Size = new System.Drawing.Size(679, 669);
            this.splitMain.SplitterDistance = 213;
            this.splitMain.TabIndex = 2;
            // 
            // toolStripSetting
            // 
            this.toolStripSetting.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsBtnNewSpace,
            this.btnSaveIt,
            this.toolStripSeparator1,
            this.tsBtnAddLayer,
            this.toolStripSeparator2,
            this.tsBtnRemoveLayer,
            this.miBtnUp,
            this.miBtnDown,
            this.toolStripSeparator3,
            this.btnAddExcelPointLayer});
            this.toolStripSetting.Location = new System.Drawing.Point(0, 0);
            this.toolStripSetting.Name = "toolStripSetting";
            this.toolStripSetting.Size = new System.Drawing.Size(679, 25);
            this.toolStripSetting.TabIndex = 2;
            this.toolStripSetting.Text = "toolStrip1";
            // 
            // tsBtnNewSpace
            // 
            this.tsBtnNewSpace.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnNewSpace.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnNewSpace.Image")));
            this.tsBtnNewSpace.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnNewSpace.Name = "tsBtnNewSpace";
            this.tsBtnNewSpace.Size = new System.Drawing.Size(23, 22);
            this.tsBtnNewSpace.Text = "新建地图空间";
            this.tsBtnNewSpace.Visible = false;
            // 
            // btnSaveIt
            // 
            this.btnSaveIt.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnSaveIt.Image = ((System.Drawing.Image)(resources.GetObject("btnSaveIt.Image")));
            this.btnSaveIt.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnSaveIt.Name = "btnSaveIt";
            this.btnSaveIt.Size = new System.Drawing.Size(23, 22);
            this.btnSaveIt.Text = "保存";
            this.btnSaveIt.Click += new System.EventHandler(this.btnSaveIt_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(6, 25);
            this.toolStripSeparator1.Visible = false;
            // 
            // tsBtnAddLayer
            // 
            this.tsBtnAddLayer.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnAddLayer.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnAddLayer.Image")));
            this.tsBtnAddLayer.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnAddLayer.Name = "tsBtnAddLayer";
            this.tsBtnAddLayer.Size = new System.Drawing.Size(23, 22);
            this.tsBtnAddLayer.Text = "添加图层";
            this.tsBtnAddLayer.Click += new System.EventHandler(this.tsBtnAddLayer_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(6, 25);
            // 
            // tsBtnRemoveLayer
            // 
            this.tsBtnRemoveLayer.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnRemoveLayer.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnRemoveLayer.Image")));
            this.tsBtnRemoveLayer.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnRemoveLayer.Name = "tsBtnRemoveLayer";
            this.tsBtnRemoveLayer.Size = new System.Drawing.Size(23, 22);
            this.tsBtnRemoveLayer.Text = "移除图层";
            this.tsBtnRemoveLayer.Click += new System.EventHandler(this.tsBtnRemoveLayer_Click);
            // 
            // miBtnUp
            // 
            this.miBtnUp.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.miBtnUp.Enabled = false;
            this.miBtnUp.Image = ((System.Drawing.Image)(resources.GetObject("miBtnUp.Image")));
            this.miBtnUp.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.miBtnUp.Name = "miBtnUp";
            this.miBtnUp.Size = new System.Drawing.Size(23, 22);
            this.miBtnUp.Text = "上一层";
            this.miBtnUp.Click += new System.EventHandler(this.miBtnUp_Click);
            // 
            // miBtnDown
            // 
            this.miBtnDown.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.miBtnDown.Image = ((System.Drawing.Image)(resources.GetObject("miBtnDown.Image")));
            this.miBtnDown.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.miBtnDown.Name = "miBtnDown";
            this.miBtnDown.Size = new System.Drawing.Size(23, 22);
            this.miBtnDown.Text = "下一层";
            this.miBtnDown.Click += new System.EventHandler(this.miBtnDown_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(6, 25);
            // 
            // btnAddExcelPointLayer
            // 
            this.btnAddExcelPointLayer.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnAddExcelPointLayer.Image = global::MasterCom.RAMS.Properties.Resources.xls;
            this.btnAddExcelPointLayer.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnAddExcelPointLayer.Name = "btnAddExcelPointLayer";
            this.btnAddExcelPointLayer.Size = new System.Drawing.Size(23, 22);
            this.btnAddExcelPointLayer.Text = "打开Excel表格生成地图上的点/制作专题图";
            this.btnAddExcelPointLayer.Click += new System.EventHandler(this.btnAddExcelPointLayer_Click);
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(511, 422);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnApply
            // 
            this.btnApply.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnApply.Location = new System.Drawing.Point(592, 422);
            this.btnApply.Name = "btnApply";
            this.btnApply.Size = new System.Drawing.Size(75, 23);
            this.btnApply.TabIndex = 2;
            this.btnApply.Text = "应用";
            this.btnApply.Click += new System.EventHandler(this.btnApply_Click);
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.HeaderText = "名称";
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            this.dataGridViewTextBoxColumn1.ReadOnly = true;
            this.dataGridViewTextBoxColumn1.Width = 150;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.HeaderText = "其他";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            this.dataGridViewTextBoxColumn2.ReadOnly = true;
            this.dataGridViewTextBoxColumn2.Width = 200;
            // 
            // LayerControlDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(679, 669);
            this.Controls.Add(this.splitMain);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.Sizable;
            this.Name = "LayerControlDlg";
            this.Text = "图层设置";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.LayerControlDlg_FormClosing);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.tabMain.ResumeLayout(false);
            this.splitMain.Panel1.ResumeLayout(false);
            this.splitMain.Panel1.PerformLayout();
            this.splitMain.Panel2.ResumeLayout(false);
            this.splitMain.ResumeLayout(false);
            this.toolStripSetting.ResumeLayout(false);
            this.toolStripSetting.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridView dataGridView;
        private System.Windows.Forms.TabControl tabMain;
        private System.Windows.Forms.TabPage tpBase;
        private System.Windows.Forms.TabPage tpCustom;
        private System.Windows.Forms.ImageList imageList;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.SplitContainer splitMain;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewCheckBoxColumn ColVisible;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColName;
        private System.Windows.Forms.DataGridViewImageColumn ColLabel;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColNote;
        private System.Windows.Forms.ToolStrip toolStripSetting;
        private System.Windows.Forms.ToolStripButton tsBtnNewSpace;
        private System.Windows.Forms.ToolStripButton btnSaveIt;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripButton tsBtnAddLayer;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripButton tsBtnRemoveLayer;
        private System.Windows.Forms.ToolStripButton miBtnUp;
        private System.Windows.Forms.ToolStripButton miBtnDown;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnApply;
        private System.Windows.Forms.ToolStripButton btnAddExcelPointLayer;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;

    }
}