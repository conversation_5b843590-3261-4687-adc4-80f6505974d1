﻿using System;
using System.Collections.Generic;
using System.Drawing;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using System.Data;
using MasterCom.MControls;
using System.Windows.Forms;
using MasterCom.RAMS.Grid;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.MTGis
{
    public class CustomThemeLayer : LayerBase
    {
        public CustomThemeLayer(string name)
            : base(name)
        {
            this.Fix = true;
        }
        /**
        public void GetSelectedInfo(MapOperation2 mop2, out List<string> titles, out List<string> infos)
        {
            
            titles = new List<string>();
            infos = new List<string>();
            foreach (StatusPointItem pt in PointList)
            {
                DbPoint dPoint = new DbPoint(pt.Longitude, pt.Latitude);
                PointF point;
                Map.ToDisplay(dPoint, out point);
                RectangleF rect;
                rect = new RectangleF(point.X - pt.Style.PointSize / 2, point.Y - pt.Style.PointSize / 2, pt.Style.PointSize, pt.Style.PointSize);

                DbRect dRect;
                Map.FromDisplay(rect, out dRect);
                if (!mop2.CheckCenterInDRect(dRect))
                {
                    continue;
                }

                if (pt.ShowAttributes.Count > 0)
                {
                    titles.Add(pt.ShowAttributes[0]);
                    StringBuilder sb = new StringBuilder();
                    foreach (string row in pt.ShowAttributes)
                    {
                        sb.AppendLine(row);
                    }
                    infos.Add(sb.ToString());
                }
            }
            
        }
        //*/
        public PointThemeSetting GetThemeSetting()
        {
            return this.themeSetting;
        }
        public void ApplyThemeSetting(PointThemeSetting setting)
        {
            this.themeSetting = setting;
            freshCountStatics();
            this.Invalidate();
        }
        private void freshCountStatics()
        {
            if (dataTable == null || themeSetting == null)
            {
                return;
            }
            themeSetting.countStaticsColorRangeDic.Clear();
            themeSetting.countStaticsVCPairDic.Clear();
            DataColumn dcolValue = null;
            foreach (DataColumn dcol in dataTable.Columns)
            {
                if (dcol.ColumnName == themeSetting.valueField)
                {
                    dcolValue = dcol;
                }
            }
            foreach (DataRow drow in dataTable.Rows)
            {
                double longi;
                double lati;
                ColorInfo clrInfo = new ColorInfo();
                if (double.TryParse(drow["经度"].ToString(), out longi)
                    && double.TryParse(drow["纬度"].ToString(), out lati) && dcolValue != null)
                {
                    object vValueObj = drow[dcolValue];
                    if (!doParseThemeStyle(vValueObj, themeSetting, longi, lati, clrInfo))
                    {
                        continue;
                    }
                    setThemeSettingCount(clrInfo.ClrRange, clrInfo.ClrPair);
                }
            }
        }

        private void setThemeSettingCount(ColorRange ClrRange, ValueColorPair vcPairRef)
        {
            if (ClrRange != null)
            {
                int cCount = 0;
                if (themeSetting.countStaticsColorRangeDic.TryGetValue(ClrRange, out cCount))
                {
                    themeSetting.countStaticsColorRangeDic[ClrRange] = cCount + 1;
                }
                else
                {
                    themeSetting.countStaticsColorRangeDic[ClrRange] = 1;
                }
            }
            if (vcPairRef != null)
            {
                int cCount = 0;
                if (themeSetting.countStaticsVCPairDic.TryGetValue(vcPairRef, out cCount))
                {
                    themeSetting.countStaticsVCPairDic[vcPairRef] = cCount + 1;
                }
                else
                {
                    themeSetting.countStaticsVCPairDic[vcPairRef] = 1;
                }
            }
        }

        private PointThemeSetting themeSetting = null;
        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (dataTable == null || themeSetting==null)
            {
                return;
            }
            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate(200, 200);
            DbRect dRect;
            this.gisAdapter.FromDisplay(inflatedRect, out dRect);
            DataColumn dcolName = null;
            DataColumn dcolValue = null;
            foreach(DataColumn dcol in dataTable.Columns)
            {
                if(themeSetting.drawLabel && dcol.ColumnName == themeSetting.nameField)
                {
                    dcolName = dcol;
                }
                if(dcol.ColumnName == themeSetting.valueField)
                {
                    dcolValue = dcol;
                }
            }
            Font labelFont = new Font("宋体", themeSetting.labelFontSize, FontStyle.Bold);
            Brush labelFontBrush = new SolidBrush(themeSetting.labelForeColor);
            SytelInfo styleInfo = new SytelInfo(labelFont, labelFontBrush);
            if (themeSetting.symbolStyle == 0 )
            {
                drawData(graphics, dRect, dcolName, dcolValue, styleInfo);
            }
        }

        private void drawData(Graphics graphics, DbRect dRect, DataColumn dcolName, DataColumn dcolValue, SytelInfo styleInfo)
        {
            foreach (DataRow drow in dataTable.Rows)
            {
                double longi;
                double lati;
                ColorInfo clrInfo = new ColorInfo();
                if (double.TryParse(drow["经度"].ToString(), out longi) && double.TryParse(drow["纬度"].ToString(), out lati))
                {
                    if (!dRect.IsPointInThisRect(longi, lati))
                    {
                        continue;
                    }
                    if (dcolValue != null)
                    {
                        object vValueObj = drow[dcolValue];
                        if (doParseThemeStyle(vValueObj, themeSetting, longi, lati, clrInfo))
                        {
                            //==============
                            DbPoint dPoint = new DbPoint(longi, lati);
                            PointF point;
                            int sbSize;
                            drawShape(graphics, clrInfo, dPoint, out point, out sbSize);

                            //=====
                            drawLabel(graphics, dcolName, styleInfo, drow, point, sbSize);
                        }
                        else
                        {
                            drawLabel(graphics, dcolName, styleInfo, drow, longi, lati, clrInfo);
                        }
                    }
                }
            }
        }

        private void drawShape(Graphics graphics, ColorInfo clrInfo, DbPoint dPoint, out PointF point, out int sbSize)
        {
            gisAdapter.ToDisplay(dPoint, out point);
            sbSize = themeSetting.symbolSize;
            if (clrInfo.Img == null)
            {
                if (Alpha != 255)
                {
                    clrInfo.Clr = Color.FromArgb(Alpha, clrInfo.Clr);
                }
                Brush brush = new SolidBrush(clrInfo.Clr);
                if (themeSetting.symbolType == 1)//方形
                {
                    Rectangle rectShape = new Rectangle((int)point.X - sbSize / 2, (int)point.Y - sbSize / 2, sbSize, sbSize);
                    graphics.FillRectangle(brush, rectShape);
                }
                else if (themeSetting.symbolType == 2)//三角形
                {
                    Point[] pts = new Point[3];
                    pts[0] = new Point((int)point.X, (int)(point.Y - sbSize / 2));
                    pts[1] = new Point((int)(point.X - sbSize / 2), (int)(point.Y + sbSize / 2));
                    pts[2] = new Point((int)(point.X + sbSize / 2), (int)(point.Y + sbSize / 2));
                    graphics.FillPolygon(brush, pts);
                }
                else//0 圆形
                {
                    Rectangle rectShape = new Rectangle((int)point.X - sbSize / 2, (int)point.Y - sbSize / 2, sbSize, sbSize);
                    graphics.FillEllipse(brush, rectShape);
                }
            }
            else
            {
                Rectangle rectShape = new Rectangle((int)point.X - sbSize / 2, (int)point.Y - sbSize / 2, sbSize, sbSize);
                graphics.DrawImage(clrInfo.Img, rectShape);
                //s
            }
        }

        private void drawLabel(Graphics graphics, DataColumn dcolName, SytelInfo styleInfo, DataRow drow, PointF point, int sbSize)
        {
            if (dcolName != null)
            {
                string vNameV = drow[dcolName].ToString();
                graphics.DrawString(vNameV, styleInfo.Font, styleInfo.FontBrush, point.X + sbSize / 2, point.Y - sbSize / 2);
            }
        }

        private void drawLabel(Graphics graphics, DataColumn dcolName, SytelInfo styleInfo, DataRow drow, double longi, double lati, ColorInfo clrInfo)
        {
            ///只点击显示标签的时候，也可以显示文字标签
            if (dcolName != null)
            {
                //==============显示打点
                DbPoint dPoint = new DbPoint(longi, lati);
                PointF point;
                gisAdapter.ToDisplay(dPoint, out point);
                int sbSize = themeSetting.symbolSize;

                clrInfo.Clr = themeSetting.pointBackColor;
                if (Alpha != 255)
                {
                    clrInfo.Clr = Color.FromArgb(Alpha, clrInfo.Clr);
                }
                Brush brush = new SolidBrush(clrInfo.Clr);
                Rectangle rectShape = new Rectangle((int)point.X - sbSize / 2, (int)point.Y - sbSize / 2, sbSize, sbSize);
                graphics.FillEllipse(brush, rectShape);
                //==============显示文字
                string vNameV = drow[dcolName].ToString();
                graphics.DrawString(vNameV, styleInfo.Font, styleInfo.FontBrush, point.X + sbSize / 2, point.Y - sbSize / 2);
            }
        }

        class SytelInfo
        {
            public SytelInfo(Font font, Brush fontBrush)
            {
                Font = font;
                FontBrush = fontBrush;
            }
            public Font Font { get; set; }
            public Brush FontBrush { get; set; }
        }

        class ColorInfo
        {
            public Color Clr { get; set; } = Color.Empty;
            public Image Img { get; set; } = null;
            public string ClrDesc { get; set; } = "";
            public ColorRange ClrRange { get; set; } = null;
            public ValueColorPair ClrPair { get; set; } = null;
        }

        private bool doParseThemeStyle(object vValueObj, PointThemeSetting themeSetting, double longi, double lati, ColorInfo clrInfo)
        {
            if (themeSetting.colorMethod == 1)
            {
                double vValueDb;
                if (!double.TryParse(vValueObj.ToString(), out vValueDb))
                {
                    return false;
                }
                getValidColorInfo(themeSetting.colorRangeList, clrInfo, vValueDb);
            }
            else if (themeSetting.colorMethod == 2)
            {
                string strV = vValueObj.ToString();
                ValueColorPair pair = null;
                if (themeSetting.dicColor.TryGetValue(strV, out pair))
                {
                    if (pair.image == null)
                    {
                        clrInfo.Clr = pair.color;
                    }
                    else
                    {
                        clrInfo.Img = pair.image;
                    }
                    clrInfo.ClrDesc = strV;
                    clrInfo.ClrPair = pair;
                }
            }
            else if (themeSetting.colorMethod == 3)
            {
                object[] args = new object[2];
                args[0] = longi;
                args[1] = lati;
                List<object> layerObjList = themeSetting.layerRefList;
                if (themeSetting.layerThemeOption != LayerBase.ThemeOption_NearestDistance)
                {
                    return true;
                }

                double rdb_NearestDistance = double.MaxValue;
                foreach (object layerObj in layerObjList)
                {
                    rdb_NearestDistance = getNearestDistance(themeSetting, args, rdb_NearestDistance, layerObj);
                }
                getValidColorInfo(themeSetting.colorRangeLayerOptionList, clrInfo, rdb_NearestDistance);
            }
            return true;
        }

        private double getNearestDistance(PointThemeSetting themeSetting, object[] args, double rdb_NearestDistance, object layerObj)
        {
            if (layerObj is LayerBase)
            {
                LayerBase layerBase = layerObj as LayerBase;
                if (layerBase != null)
                {
                    double rdb = layerBase.CalcThemeOption(themeSetting.layerThemeOption, args);
                    if (rdb < rdb_NearestDistance)
                    {
                        rdb_NearestDistance = rdb;
                    }
                }
            }
            else if (layerObj is CustomDrawLayer)
            {
                CustomDrawLayer custLayer = layerObj as CustomDrawLayer;
                if (custLayer != null)
                {
                    double rdb = custLayer.CalcThemeOption(themeSetting.layerThemeOption, args);
                    if (rdb < rdb_NearestDistance)
                    {
                        rdb_NearestDistance = rdb;
                    }
                }
            }

            return rdb_NearestDistance;
        }

        private void getValidColorInfo(List<ColorRange> colorRangeList, ColorInfo clrInfo, double value)
        {
            foreach (ColorRange cr in colorRangeList)
            {
                if (value >= cr.minValue && value < cr.maxValue)
                {
                    clrInfo.Clr = cr.color;
                    clrInfo.ClrDesc = cr.desInfo;
                    clrInfo.ClrRange = cr;
                    break;
                }
            }
        }

        private DataTable dataTable = null;
        internal void SetGridDataInfo(DataTable dt)
        {
            this.dataTable = dt;
        }
        internal DataTable GetGridDataInfo()
        {
            return this.dataTable;
        }

        internal int MakeShpFileOfPathByPoints(string fileName)
        {
            if (dataTable == null)
            {
                return -1;
            }

            try
            {
                MapWinGIS.Shapefile shpFile = new MapWinGIS.Shapefile();
                bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }
                List<DbPoint> pathList = new List<DbPoint>();
                foreach (DataRow drow in dataTable.Rows)
                {
                    double longi;
                    double lati;
                    if (double.TryParse(drow["经度"].ToString(), out longi) && double.TryParse(drow["纬度"].ToString(), out lati))
                    {
                        pathList.Add(new DbPoint(longi, lati));
                    }
                }
               shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYLINE);

                MapWinGIS.Shape spBase = new MapWinGIS.Shape();
                spBase.Create(MapWinGIS.ShpfileType.SHP_POLYLINE);

                int j = 0;
                for (int i = 0; i < pathList.Count; i++)
                {
                    MapWinGIS.Point pnt = new MapWinGIS.Point();
                    pnt.x = pathList[i].x;
                    pnt.y = pathList[i].y;
                    spBase.InsertPoint(pnt, ref j);
                }

                if (spBase.IsValidReason.Contains("Too few points in geometry component")) //存在只有两个点，而且两点的x，y相同的情况，导致无法连线图元无效，这里跳过不画
                {
                    shpFile.Close();
                    return -1;
                }
                int shpIdx = 0;
                shpFile.EditInsertShape(spBase, ref shpIdx);
                

                ShapeHelper.DeleteShpFile(fileName);
                if (!shpFile.SaveAs(fileName, null))
                {
                    MessageBox.Show("保存文件失败！" + shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    shpFile.Close();
                    return -1;
                }
                shpFile.Close();
                return 1;
            }
            catch
            {
                return -1;
            }
        }

        internal int MakeShpFile(string fileName)
        {
            if (dataTable == null || themeSetting == null)
            {
                return -1;
            }
            DataColumn dcolName = null;
            DataColumn dcolValue = null;
            foreach (DataColumn dcol in dataTable.Columns)
            {
                if (dcol.ColumnName == themeSetting.nameField)
                {
                    dcolName = dcol;
                }
                if (dcol.ColumnName == themeSetting.valueField)
                {
                    dcolValue = dcol;
                }
            }

            MapWinGIS.Shapefile shpFile = new MapWinGIS.Shapefile();
            bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POINT);
            if (!result)
            {
                MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                return -1;
            }
            shpFile.DefaultDrawingOptions.SetDefaultPointSymbol(MapWinGIS.tkDefaultPointSymbol.dpsCircle);

            int res = dealShapeFile(fileName, dcolName, dcolValue, shpFile);
            return res;
            //*/
        }

        private int dealShapeFile(string fileName, DataColumn dcolName, DataColumn dcolValue, MapWinGIS.Shapefile shpFile)
        {
            int idIdx = 0;
            int fLabel = idIdx++;
            int fValue = idIdx++;
            int fColorDesc = idIdx++;
            int fColorValue = idIdx++;
            int fLongId = idIdx++;
            int fLatId = idIdx;

            ShapeHelper.InsertNewField(shpFile, "LabelField", MapWinGIS.FieldType.STRING_FIELD, 10, 30, ref fLabel);
            ShapeHelper.InsertNewField(shpFile, "ValueField", MapWinGIS.FieldType.STRING_FIELD, 10, 30, ref fValue);
            ShapeHelper.InsertNewField(shpFile, "ColorDesc", MapWinGIS.FieldType.STRING_FIELD, 10, 30, ref fColorDesc);
            ShapeHelper.InsertNewField(shpFile, "ColorValue", MapWinGIS.FieldType.INTEGER_FIELD, 10, 30, ref fColorValue);
            ShapeHelper.InsertNewField(shpFile, "Longitude", MapWinGIS.FieldType.DOUBLE_FIELD, 10, 30, ref fLongId);
            ShapeHelper.InsertNewField(shpFile, "Latitude", MapWinGIS.FieldType.DOUBLE_FIELD, 10, 30, ref fLatId);

            try
            {
                int shpIdx = 0;
                foreach (DataRow drow in dataTable.Rows)
                {
                    double longi;
                    double lati;
                    ColorInfo clrInfo = new ColorInfo();
                    if (dcolValue != null
                        && double.TryParse(drow["经度"].ToString(), out longi) 
                        && double.TryParse(drow["纬度"].ToString(), out lati))
                    {
                        object vValueObj = drow[dcolValue];
                        if (doParseThemeStyle(vValueObj, themeSetting, longi, lati, clrInfo))
                        {
                            uint oleColor = (uint)ColorTranslator.ToOle(clrInfo.Clr);

                            string labelValue = getLabelValue(dcolName, drow);

                            MapWinGIS.Shape spBase = new MapWinGIS.Shape();
                            spBase.Create(MapWinGIS.ShpfileType.SHP_POINT);
                            MapWinGIS.Point pt = new MapWinGIS.Point();
                            pt.x = longi;
                            pt.y = lati;
                            int j = 0;
                            spBase.InsertPoint(pt, ref j);

                            shpFile.EditInsertShape(spBase, ref shpIdx);
                            shpFile.EditCellValue(fLabel, shpIdx, labelValue);
                            shpFile.EditCellValue(fValue, shpIdx, vValueObj != null ? vValueObj.ToString() : "");
                            shpFile.EditCellValue(fColorDesc, shpIdx, clrInfo.ClrDesc);
                            shpFile.EditCellValue(fColorValue, shpIdx, (int)oleColor);
                            shpFile.EditCellValue(fLongId, shpIdx, longi);
                            shpFile.EditCellValue(fLatId, shpIdx, lati);
                            shpIdx++;
                        }
                    }
                }
                try
                {
                    ShapeHelper.DeleteShpFile(fileName);
                }
                catch
                {
                    //continue
                }
                shpFile.SaveAs(fileName, null);
                shpFile.Close();
                return 1;
            }
            catch
            {
                return -1;
            }
        }

        private static string getLabelValue(DataColumn dcolName, DataRow drow)
        {
            string labelValue = "";
            if (dcolName != null)
            {
                labelValue = drow[dcolName].ToString();
            }

            return labelValue;
        }

        internal int ExportKmlByRegion(KMLExporter exporter, XmlElement parentElement, ResvRegion myRegion)
        {
            if (dataTable == null || themeSetting == null)
            {
                return -1;
            }
            DataColumn dcolName = null;
            DataColumn dcolValue = null;
            foreach (DataColumn dcol in dataTable.Columns)
            {
                if (dcol.ColumnName == themeSetting.nameField)
                {
                    dcolName = dcol;
                }
                if (dcol.ColumnName == themeSetting.valueField)
                {
                    dcolValue = dcol;
                }
            }

            XmlElement layerElement = exporter.CreateFolder(name, false);
            parentElement.AppendChild(layerElement);

            try
            {
                bool res = dealDTRow(exporter, myRegion, dcolName, dcolValue, layerElement);
                if (!res)
                {
                    return -1;
                }
                return 1;
            }
            catch
            {
                return -1;
            }
        }

        private bool dealDTRow(KMLExporter exporter, ResvRegion myRegion, DataColumn dcolName, DataColumn dcolValue, XmlElement layerElement)
        {
            foreach (DataRow drow in dataTable.Rows)
            {
                double longi;
                double lati;
                if (dcolValue != null
                    && double.TryParse(drow["经度"].ToString(), out longi) && double.TryParse(drow["纬度"].ToString(), out lati))
                {
                    try
                    {
                        string labelValue = "";
                        if (dcolName != null)
                        {
                            labelValue = drow[dcolName].ToString();
                        }

                        DbPoint myPoint = new DbPoint(longi, lati);
                        if (myRegion.GeoOp.CheckPointInRegion(myPoint.x, myPoint.y))
                        {
                            string destribeStr = "区域:" + myRegion.RegionName + "\r\n" + "描述:" + labelValue + "\r\n" + "经纬度:" + myPoint.x + "," + myPoint.y;
                            exporter.AddTestPoint(layerElement, Color.OrangeRed, myPoint, DateTime.Now, destribeStr, myRegion.RegionName + "自定义点");
                        }
                    }
                    catch
                    {
                        return false;
                    }
                }
            }
            return true;
        }
    }

    public class PointThemeSetting
    {
        public bool drawLabel { get; set; } = false;
        public string nameField { get; set; } = "";
        public string valueField { get; set; } = "";
        public int symbolSize { get; set; } = 16;
        /**0--"圆形", 
         * 1--"方形", 
         * 2---"三角形" 
         * 
         */
        public int symbolType { get; set; } = 0;
        /**0--"打点", 
         * 1--"栅格"
         * 
         */
        public int symbolStyle { get; set; } = 0;
        /**
         * 1:by ColorRange  2:by ColorDic 3: LayerRef
         * 
         */
        public int colorMethod { get; set; } = 1;
        public List<ColorRange> colorRangeList { get; set; } = new List<ColorRange>();
        public Dictionary<string, ValueColorPair> dicColor { get; set; } = new Dictionary<string, ValueColorPair>();
        public Color labelForeColor { get; set; } = Color.Black;
        public Color pointBackColor { get; set; } = Color.Green;
        public int labelFontSize { get; set; } = 12;
        //====
        public int layerThemeOption { get; set; }
        public List<object> layerRefList { get; set; }
        public List<ColorRange> colorRangeLayerOptionList { get; set; } = new List<ColorRange>();
        //====
        public Dictionary<ColorRange, int> countStaticsColorRangeDic { get; set; } = new Dictionary<ColorRange, int>();
        public Dictionary<ValueColorPair, int> countStaticsVCPairDic { get; set; } = new Dictionary<ValueColorPair, int>();

        //======
        public string LayerThemeOptionDesc
        {
            get
            {
                if(layerThemeOption == LayerBase.ThemeOption_NearestDistance)
                {
                    return "最近记录距离";
                }
                else
                {
                    return "";
                }
            }
        }
        public string ColorMethodDesc
        {
            get
            {
                if(colorMethod==1)
                {
                    return "按值域取值";
                }
                else if (colorMethod == 2)
                {
                    return "按字典取值";
                }
                else if (colorMethod == 3)
                {
                    return "关联图层取值";
                }
                else
                {
                    return "";
                }
            }
        }
    }

}
