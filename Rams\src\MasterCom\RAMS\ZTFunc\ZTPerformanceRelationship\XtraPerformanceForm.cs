﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTPerformRelated
{
    public partial class XtraPerformanceForm : DevExpress.XtraEditors.XtraForm
    {
        private XtraForm formOfPerDetailShow;
        private bool IsTDIn = false;
        public XtraPerformanceForm(MainModel Mainmodel,bool IsTDIn)
        {
            InitializeComponent();
            mainmodel = Mainmodel;
            layer= mainmodel.MainForm.GetMapForm().GetMapFormPerformanceRelationshipLayer();

            this.IsTDIn = IsTDIn;
            panelmengya.BackColor = layer.Colormengfa;
            paneloufa.BackColor = layer.Coloroufa;
            panelyinhuan.BackColor = layer.Coloryinhuan;
            panelpinfa.BackColor = layer.Colorpinfa;
            panelehua.BackColor = layer.Colorehua;

            this.Width = 700;
            splitContainer1.Panel2MinSize = 1;
            splitContainer1.SplitterDistance = this.Width;
            splitContainer1.IsSplitterFixed = true;
            button1.Visible = false;
            checkBoxCheckedBox = new List<string>();
            if (IsTDIn)
            {
                formOfPerDetailShow = new PerCellDetailShowTD();
            }
            else
            {
                formOfPerDetailShow = new PerCellDetailShowGSM();
            }
        }
        private MainModel mainmodel;
        MapFormPerformanceRelationshipLayer layer;

        public void setData(List<PerformanceItem> performList)
        {
            gridControl1.DataSource = performList;
        }

        //隐藏窗口至右下角
        private void XtraPerformanceForm_Deactivate(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Minimized)
            {
                this.Visible = false;
                mainmodel.AddQuickWindowItem(this.GetType().Name, this.Text, "images\\cellquery.gif");
            }
        }

        //双击定位小区
        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            string cellname = gridView1.GetFocusedRowCellValue("Cellname").ToString();
            if (cellname != null && cellname != "")
            {
                Cell cell = null;
                cell = mainmodel.CellManager.GetCellByName(cellname);
                if (cell != null)
                {
                    mainmodel.SelectedCell = cell;
                    mainmodel.FireSelectedCellChanged(this);
                }
            }
        }

        //问题点变更
        private void checkBox_CheckedChanged(object sender, EventArgs e)
        {
            List<PerformanceItem> tempItem = new List<PerformanceItem>();
            foreach (PerformanceItem item in mainmodel.PerformanceItemListAll)
            {
                addTempItem(tempItem, item, checkBox1);
                addTempItem(tempItem, item, checkBox2);
                addTempItem(tempItem, item, checkBox3);
                addTempItem(tempItem, item, checkBox4);
                addTempItem(tempItem, item, checkBox5);
            }

            mainmodel.PerformanceItemList = tempItem;
            mainmodel.FireDTDataChanged(this);  //更新GIS
            gridControl1.DataSource = tempItem; //更新窗体
        }

        private void addTempItem(List<PerformanceItem> tempItem, PerformanceItem item, CheckBox chb)
        {
            if (chb.Text == item.Type && chb.Checked)
            {
                tempItem.Add(item);
            }
        }

        private List<GSMPerDetailEleInf> deailInfGSM;
        private List<TDPerDetailEleInf> detailInfTD;
        private void GetDetailInf()
        {
            WaitBox.ProgressPercent = 25;
            PerDetailEleInfQuery detailQuery = new PerDetailEleInfQuery(mainmodel, IsTDIn);
            detailQuery.TimeStart = timeStart;
            detailQuery.TimeEnd = timeEnd;
            detailQuery.LAC = cellLac;
            detailQuery.CI = cellCi;
            detailQuery.Query();
            deailInfGSM = detailQuery.DetailInfGSM;
            detailInfTD = detailQuery.DetailInfTD;
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }
        //查看小区详细历史问题
        private int cellLac;
        private int cellCi;
        private DateTime timeStart;
        private DateTime timeEnd;
        private void ToolStripMenuItemDesc_Click(object sender, EventArgs e)
        {
            //SaveFileDialog saveFileDialog = new SaveFileDialog();
            //saveFileDialog.Title = "导出Excel";
            //saveFileDialog.Filter = "Excel文件(*.xls)|*.xls";
            //if (saveFileDialog.ShowDialog() == DialogResult.OK)
            //{
            //    gridControl1.ExportToExcelOld(saveFileDialog.FileName);
            //    DevExpress.XtraEditors.XtraMessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //}
            List<List<object>> exportList = GridViewTransfer.Transfer(this.gridControl1);
            ExcelNPOIManager.ExportToExcel(exportList);
        }

        //隐藏侧边栏
        private void button1_Click(object sender, EventArgs e)
        {
            this.Width = 700;
            splitContainer1.Panel2MinSize = 1;
            splitContainer1.SplitterDistance = this.Width;
            splitContainer1.IsSplitterFixed = true;
            button1.Visible = false;
        }
        
        // 萌发点
        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            ChangeCheckBoxChecked();
        }

        //偶发点
        private void checkBox2_CheckedChanged(object sender, EventArgs e)
        {
            ChangeCheckBoxChecked();
        }

        //隐患点
        private void checkBox3_CheckedChanged(object sender, EventArgs e)
        {
            ChangeCheckBoxChecked();
        }
        //频发点
        private void checkBox4_CheckedChanged(object sender, EventArgs e)
        {
            ChangeCheckBoxChecked();
        }
        //恶化点
        private void checkBox5_CheckedChanged(object sender, EventArgs e)
        {
            ChangeCheckBoxChecked();
        }
        private List<string> checkBoxCheckedBox;
        List<PerformanceItem> tempItemGridControlDataSource;
        private void ChangeCheckBoxChecked()
        {
            tempItemGridControlDataSource = new List<PerformanceItem>();
            IniGridControl1DataSource();
            mainmodel.PerformanceItemList = tempItemGridControlDataSource;
            mainmodel.FireDTDataChanged(this);  //更新GIS
            gridControl1.DataSource = tempItemGridControlDataSource; //更新窗体
        }

        private void IniGridControl1DataSource()
        {
            int indexOfL = 0;
            foreach (PerformanceItem item in mainmodel.PerformanceItemListAll)
            {
                WaitBox.ProgressPercent = (int)((((double)indexOfL++) * 100) / mainmodel.PerformanceItemListAll.Count);
                addTempItemGridControlDataSource(item, checkBox1);
                addTempItemGridControlDataSource(item, checkBox2);
                addTempItemGridControlDataSource(item, checkBox3);
                addTempItemGridControlDataSource(item, checkBox4);
                addTempItemGridControlDataSource(item, checkBox5);
            }
            WaitBox.Close();
        }

        private void addTempItemGridControlDataSource(PerformanceItem item,CheckBox chb)
        {
            if (chb.Text == item.Type && chb.Checked)
            {
                tempItemGridControlDataSource.Add(item);
            }
        }

        private void panelControl1_SizeChanged(object sender, EventArgs e)
        {
            if (formOfPerDetailShow != null)
            {
                formOfPerDetailShow.Size = panelControl1.Size;
            }
        }
        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            cellLac = int.Parse(gridView1.GetFocusedRowCellValue("Lac").ToString());
            cellCi = int.Parse(gridView1.GetFocusedRowCellValue("Ci").ToString());
            timeStart = DateTime.Parse(gridView1.GetFocusedRowCellValue("Stime").ToString());
            timeEnd = DateTime.Parse(gridView1.GetFocusedRowCellValue("Etime").ToString());

            WaitBox.Show(GetDetailInf);
            if (IsTDIn)
            {
                if (detailInfTD != null)
                {
                    formOfPerDetailShow = new PerCellDetailShowTD();
                    ((PerCellDetailShowTD)formOfPerDetailShow).DeailInfTD = detailInfTD;
                    ((PerCellDetailShowTD)formOfPerDetailShow).MainSelectedEleName = "CS域无线接通率";
                    formOfPerDetailShow.Location = new Point(this.Location.X + this.Width - 5, this.Location.Y);
                    formOfPerDetailShow.Show();
                }
            }
            else
            {
                if (deailInfGSM != null)
                {
                    formOfPerDetailShow = new PerCellDetailShowGSM();
                    ((PerCellDetailShowGSM)formOfPerDetailShow).DeailInfGSM = deailInfGSM;
                    ((PerCellDetailShowGSM)formOfPerDetailShow).MainSelectedEleName = "无线接通率";
                    formOfPerDetailShow.Location = new Point(this.Location.X + this.Width - 5, this.Location.Y);
                    formOfPerDetailShow.Show();
                }
            }
        }
    }
}