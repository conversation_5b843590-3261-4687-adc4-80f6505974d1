﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GridProblemDlg : BaseDialog
    {
        public GridProblemDlg()
        {
            InitializeComponent();
            cbxDataType.Properties.Items.Clear();
            cbxDataType.Properties.Items.Add("GSM语音");
            cbxDataType.Properties.Items.Add("TD语音");
            cbxDataType.SelectedIndex = 0;

            cbxStatus.Properties.Items.Clear();
            cbxStatus.Properties.Items.Add("全部");
            cbxStatus.Properties.Items.Add("已创建");
            cbxStatus.Properties.Items.Add("已关闭");
            cbxStatus.SelectedIndex = 0;
        }

        public void GetCondition(out int dataType, out int status)
        {
            dataType = cbxDataType.SelectedIndex;
            status = cbxStatus.SelectedIndex;
        }
    }
}
