﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class BtsAcceptFileInfo_TianJin
    {
        public BtsAcceptFileInfo_TianJin(FileInfo fileInfo, LTEBTS bts)
        {
            File = fileInfo;
            Bts = bts;
            AcceptKpiDic = new Dictionary<uint, object>();
        }

        public FileInfo File { get; set; }

        public int FileId
        {
            get
            {
                if (File != null)
                {
                    return File.ID;
                }
                return 0;
            }
        }

        public string FileName
        {
            get
            {
                if (File != null)
                {
                    return File.Name;
                }
                return "";
            }
        }

        public LTEBTS Bts { get; set; }

        public string BtsName
        {
            get
            {
                if (Bts != null)
                {
                    return Bts.Name;
                }
                return "";
            }
        }

        public int BtsId
        {
            get
            {
                if (Bts != null)
                {
                    return Bts.BTSID;
                }
                return 0;
            }
        }

        public Dictionary<uint, object> AcceptKpiDic { get; set; }
    }
}
