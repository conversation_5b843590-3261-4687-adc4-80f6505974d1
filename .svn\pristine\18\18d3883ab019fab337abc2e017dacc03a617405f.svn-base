﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYQueryScanAnalysis_LTE : DIYEventByRegion
    {
        public ZTDIYQueryScanAnalysis_LTE(MainModel mainModel)
            : base(mainModel)
        {
            isAddEventToDTDataManager = false;
        }
        public override string Name
        {
            get { return "驻留异常_LTE"; }
        }
        public override string IconName
        {
            get { return "Images/event/handover.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {

            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22072, "关联扫频分析_LTE");
        }
        #region 全局变量
        int iR0_RP = 0;
        double dTime = 0;
        int iCellCount = 0;
        bool isGSM = false;
        bool isTD = false;
        bool isGT = false;
        List<FileInfo> fileScanList { get; set; } = new List<FileInfo>();
        List<Event> reselectAnalysisEventList { get; set; } = new List<Event>();
        Dictionary<FileInfo, List<Event>> fileEventDic { get; set; } = new Dictionary<FileInfo, List<Event>>();
        Dictionary<Event, List<TestPoint>> evtTestPointDic { get; set; } = new Dictionary<Event, List<TestPoint>>();
        public List<ScanAnalysisLTEInfo> eventResultList { get; set; } = new List<ScanAnalysisLTEInfo>();
        public List<ScanAnalysisLTEInfo> cellResultList { get; set; } = new List<ScanAnalysisLTEInfo>();
        Dictionary<LTECell, ScanAnalysisLTEInfo> cellScanAna { get; set; } = new Dictionary<LTECell, ScanAnalysisLTEInfo>();
        public static List<TestPoint> ScanTestPointList { get; set; }
        public static bool isLTEScan { get; set; } = false;
        public Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic { get; set; }
        #endregion
        protected override void query()
        {
            if (!getConditionBeforeQuery() || !prepareAskWhatEvent())
            {
                return;
            }
            fileScanList.Clear();
            reselectAnalysisEventList.Clear();
            fileEventDic.Clear();
            evtTestPointDic.Clear();
            eventResultList.Clear();
            cellResultList.Clear();
            cellScanAna.Clear();
            isLTEScan = true;
            ScanTestPointList = new List<TestPoint>();
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            MainModel.ClearDTData();
            InitRegionMop2();
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = null;
            try
            {
                foreach (int DistrictID in condition.DistrictIDs)
                {
                    clientProxy = new ClientProxy();
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        return;
                    }

                    WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                    clientProxy.Close();
                }
                MainModel.FireDTDataChanged(this);
                reselectAnalysisEventList.Sort(ZTDIYQueryScanAnalysis_LTE.GetCompareByDTime());//将路测事件按时间排序
                getLTEScanFileList();///获取同车测试的文件
                fileScanList.Sort(FileInfo.GetCompareByBeginTimeAsc());//将扫频文件按时间排序
                changeEventToFileDic();//将事件按扫频文件的关联进行分类
                try
                {
                    WaitBox.CanCancel = true;
                    WaitBox.Show("正在获取扫频采样点信息...", analysisEventByScanFileAnyone);
                }
                catch
                {
                    WaitBox.Close();
                }
                fireShowFormAfterQuery();
                eventResultList.Clear();
                cellResultList.Clear();
            }
            finally
            {
                //WaitBox.Close()
            }

        }
        protected override bool prepareAskWhatEvent()
        {
            List<int> selectedEventIDs = new List<int>();
            if (isGSM)
                selectedEventIDs.Add(1124);
            if (isTD)
                selectedEventIDs.Add(1125);
            if (isGT)
                selectedEventIDs.Add(1126);
            Condition.EventIDs = selectedEventIDs;
            return true;
        }
        private ZTDIYQueryScanAnalysisSetForm rASetForm = null;
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (rASetForm == null)
            {
                rASetForm = new ZTDIYQueryScanAnalysisSetForm();
            }
            if (rASetForm.ShowDialog() == DialogResult.OK)
            {
                rASetForm.GetSetCond(ref iR0_RP, ref dTime, ref iCellCount
                    , ref isGSM, ref isTD, ref isGT);
                return true;
            }
            return false;
        }
        /// <summary>
        /// 接收满足ID的事件
        /// </summary>
        /// <param name="evt"></param>
        protected override void doWithDTData(Event evt)
        {
            reselectAnalysisEventList.Add(evt);
        }
        /// <summary>
        /// 获取同车测试的所有扫频文件
        /// </summary>
        private void getLTEScanFileList()
        {
            DIYQueryFileInfoByRegion queryFiles = new DIYQueryFileInfoByRegion(MainModel);
            queryFiles.IsShowFileInfoForm = false;
            QueryCondition qCondition = condition;
            qCondition.Projects.Clear();
            qCondition.Projects.Add(50);
            qCondition.ServiceTypes.Clear();
            qCondition.ServiceTypes.Add(35);
            queryFiles.SetQueryCondition(qCondition);
            queryFiles.Query();
            fileScanList.AddRange(MainModel.FileInfos);
        }
        /// <summary>
        /// 将事件按扫频文件的关联进行分类
        /// </summary>
        private void changeEventToFileDic()
        {
            foreach (FileInfo fi in fileScanList)
            {
                List<Event> eventList = new List<Event>();
                foreach (Event evt in reselectAnalysisEventList)
                {
                    if (evt.Time >= fi.BeginTime && evt.Time <= fi.EndTime
                        && !eventList.Contains(evt))
                        eventList.Add(evt);
                }
                if (!fileEventDic.ContainsKey(fi))
                    fileEventDic.Add(fi, eventList);
            }
        }
        /// <summary>
        /// 回放扫频文件获取采样点
        /// </summary>
        /// <param name="fileInfo"></param>
        private void replayFileInPeriod(FileInfo fileInfo)
        {
            DIYReplayFileWithNoWaitBox qb = new DIYReplayFileWithNoWaitBox(MainModel);
            qb.FilterByPeriod = true;
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.Add(fileInfo);
            condition.Periods.Add(GetTimePeriod(fileEventDic[fileInfo]));
            qb.SetQueryCondition(condition);
            qb.Query();
        }
        /// <summary>
        /// 以文件涉及事件的首尾时间段
        /// </summary>
        private TimePeriod GetTimePeriod(List<Event> events)
        {
            return new TimePeriod(events[0].DateTime.AddSeconds(-10), events[events.Count - 1].DateTime.AddSeconds(5));
        }
        /// <summary>
        /// 逐个事件关联扫频进行分析
        /// </summary>
        private void analysisEventByScanFileAnyone()
        {
            try
            {
                WaitBox.CanCancel = true;
                foreach (FileInfo fi in fileEventDic.Keys)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    if (fileEventDic[fi].Count == 0)
                        continue;
                    replayFileInPeriod(fi);
                    dealFileEvent(fi);
                }
                foreach (LTECell lteCell in cellScanAna.Keys)
                {
                    cellResultList.Add(cellScanAna[lteCell]);
                }
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void dealFileEvent(FileInfo fi)
        {
            foreach (DTFileDataManager dtFile in MainModel.DTDataManager.FileDataManagers)
            {
                dtFile.TestPoints.Sort(TestPointSortByMillsecondeTime.GetCompareByTime());
                foreach (Event evt in fileEventDic[fi])//按事件维度处理数据
                {
                    int iCurIndex = findTestPointIndex(dtFile.TestPoints, evt);
                    if (iCurIndex == -1)
                        continue;
                    ScanAnalysisLTEInfo saiEvt = getAnalysisItemInfo(dtFile, evt, iCurIndex, fi);//按事件处理数据
                    if (saiEvt != null)
                    {
                        saiEvt.IndexID = eventResultList.Count + 1;
                        eventResultList.Add(saiEvt);
                    }
                    //按小区维度处理数据
                    getAnalysisItemInfos(dtFile, iCurIndex);
                }
            }
        }

        class StatInfo
        {
            public int RORPMean { get; set; } = -1;
            public int RORPMax { get; set; } = -999;
            public int ROPRPMin { get; set; } = 999;
            public int CoverCount { get; set; } = 0;
            public long SumTime { get; set; } = 0;
            public long MaxTime { get; set; } = 0;
            public int TPCount { get; set; } = 0;
        }

        /// <summary>
        /// 按事件维度获取Item
        /// </summary>
        private ScanAnalysisLTEInfo getAnalysisItemInfo(DTFileDataManager dtFile, Event evt
            , int iCurIndex,FileInfo fi)
        {
            ScanAnalysisLTEInfo sai = new ScanAnalysisLTEInfo();
            sai.Evt = evt;
            sai.ReplayFile = fi;
            sai.StrGridType = "无网格类型";
            sai.StrGridName = "无网格名称";
            string gridTypeGrid = "";
            isContainPoint(evt.Longitude, evt.Latitude, ref gridTypeGrid);
            if (gridTypeGrid != "")
            {
                sai.StrGridType = gridTypeGrid.Split(',')[0];
                sai.StrGridName = gridTypeGrid.Split(',')[1];
            }

            StatInfo info = new StatInfo();
            Dictionary<LTECell, CellSampleInfo> cellInfo = evtList(dtFile.TestPoints, iCurIndex, info);
            if (cellInfo.Count == 0)
                return null;
            sai.cellInfoDic = cellInfo;
            sai.ItpCount = info.TPCount;
            sai.ICoverCellCount = cellInfo.Count;
            sai.IR0_RPMean = info.RORPMean;
            sai.IRO_RPMax = info.RORPMax;
            sai.IRO_RPMin = info.ROPRPMin;
            sai.FCoverRate = 100.0 * info.CoverCount / sai.ItpCount;
            sai.DCoverTime = Math.Round((double)info.SumTime / 1000L, 2);
            sai.DCoverTimeMax = Math.Round((double)info.MaxTime / 1000L, 2);
            if (info.RORPMean == -1 || info.RORPMax == -999 || info.ROPRPMin == 999
                || sai.ICoverCellCount < iCellCount || sai.DCoverTimeMax < dTime)
                return null;
            return sai;
        }
        /// <summary>
        ///匹配出最符合的采样点 
        /// </summary>
        private int findTestPointIndex(List<TestPoint> testPointList,Event e)
        {
            int index = -1;
            long lTime = 5 * 1000L;
            for (int i = 0; i < testPointList.Count;i++)
            {
                double dDistance = MathFuncs.GetDistance(testPointList[i].Longitude, testPointList[i].Latitude, e.Longitude, e.Latitude);
                if (dDistance > 50)
                    continue;
                if (Math.Abs(testPointList[i].lTimeWithMillsecond - e.lTimeWithMillsecond) < lTime)
                {
                    index = i;
                    lTime = Math.Abs(testPointList[i].lTimeWithMillsecond - e.lTimeWithMillsecond);
                }
            }
            return index;
        }
        /// <summary>
        /// 事件维度统计符合信号强度的小区数目及覆盖时长
        /// </summary>
        private Dictionary<LTECell, CellSampleInfo> evtList(List<TestPoint> testPointList, int iCurIndex, StatInfo info)
        {
            Dictionary<LTECell, CellSampleInfo> cellSampleDic = new Dictionary<LTECell, CellSampleInfo>();
            int iSum = 0;
            long lMaxSeries = 0;
            for (int i = iCurIndex; i >= 1; i--)
            {
                long lCellTime = 0;
                if ((testPointList[i - 1].lTimeWithMillsecond + 10 * 1000) < testPointList[iCurIndex].lTimeWithMillsecond)
                    break;
                float? fRO_RP = (float?)testPointList[i]["LTESCAN_TopN_CELL_Specific_RSRP"];
                if (fRO_RP == null || (int)fRO_RP < -140 || (int)fRO_RP > 25)
                    continue;
                iSum += (int)fRO_RP;
                if ((int)fRO_RP > info.RORPMax)
                    info.RORPMax = (int)fRO_RP;
                if ((int)fRO_RP < info.ROPRPMin)
                    info.ROPRPMin = (int)fRO_RP;
                info.TPCount++;
                if ((int)fRO_RP >= iR0_RP)
                {
                    lCellTime = testPointList[i].lTimeWithMillsecond - testPointList[i - 1].lTimeWithMillsecond;
                    info.CoverCount++;
                    info.SumTime += testPointList[i].lTimeWithMillsecond - testPointList[i - 1].lTimeWithMillsecond;
                    lMaxSeries += testPointList[i].lTimeWithMillsecond - testPointList[i - 1].lTimeWithMillsecond;
                    ScanTestPointList.Add(testPointList[i - 1]);
                }
                else
                {
                    setMaxTime(info, lMaxSeries);
                    lMaxSeries = 0;
                }
                addCellSampleDic(testPointList, cellSampleDic, i, lCellTime, fRO_RP);
            }
            setMaxTime(info, lMaxSeries);
            if (info.TPCount != 0)
                info.RORPMean = iSum / info.TPCount;
            return cellSampleDic;
        }

        private void setMaxTime(StatInfo info, long lMaxSeries)
        {
            if (lMaxSeries > info.MaxTime)
                info.MaxTime = lMaxSeries;
        }

        private void addCellSampleDic(List<TestPoint> testPointList, Dictionary<LTECell, CellSampleInfo> cellSampleDic, int i, long lCellTime, float? fRO_RP)
        {
            LTECell lteCell;
            for (int j = 0; j < 5; j++)
            {
                lteCell = testPointList[i].GetCell_LTEScan(j);
                if (lteCell != null)
                {
                    if (!cellSampleDic.ContainsKey(lteCell))
                    {
                        CellSampleInfo cellSampleInfo = new CellSampleInfo();
                        cellSampleInfo.cell = lteCell;
                        cellSampleInfo.LCoverTime = lCellTime;
                        cellSampleInfo.SampleList.Add((int)fRO_RP);
                        cellSampleDic.Add(lteCell, cellSampleInfo);
                    }
                    else
                    {
                        cellSampleDic[lteCell].SampleList.Add((int)fRO_RP);
                        cellSampleDic[lteCell].LCoverTime += lCellTime;
                    }
                }
            }
        }

        /// <summary>
        /// 按小区维度获取Items
        /// </summary>
        private void getAnalysisItemInfos(DTFileDataManager dtFile, int iCurIndex)
        {
            for (int i = iCurIndex; i >= 1; i--)
            {
                if ((dtFile.TestPoints[i - 1].lTimeWithMillsecond + 10 * 1000) < dtFile.TestPoints[iCurIndex].lTimeWithMillsecond)
                {
                    break;
                }
                float? fRO_RP = (float?)dtFile.TestPoints[i]["LTESCAN_TopN_CELL_Specific_RSRP"];
                if (fRO_RP == null || (int)fRO_RP < -140 || (int)fRO_RP > 25)
                {
                    continue;
                }
                LTECell lteCell;
                for (int j = 0; j < 5; j++)
                {
                    lteCell = dtFile.TestPoints[i].GetCell_LTEScan(j);
                    if (lteCell != null)
                    {
                        setCellScanAnaInfo(dtFile, i, fRO_RP, lteCell);
                    }
                }  
            }
        }

        private void setCellScanAnaInfo(DTFileDataManager dtFile, int i, float? fRO_RP, LTECell lteCell)
        {
            if (cellScanAna.ContainsKey(lteCell))
            {
                cellScanAna[lteCell].TestPointList.Add(dtFile.TestPoints[i]);
            }
            else
            {
                ScanAnalysisLTEInfo sai = new ScanAnalysisLTEInfo();
                sai.LteCell = lteCell;
                sai.StrCellName = lteCell.Name;
                sai.IEARFCN = lteCell.EARFCN;
                sai.IPCI = lteCell.PCI;
                sai.TestPointList.Add(dtFile.TestPoints[i]);
                cellScanAna.Add(lteCell, sai);
            }
            if (fRO_RP >= iR0_RP)
            {
                cellScanAna[lteCell].CoverTestPoint.Add(dtFile.TestPoints[i]);
                cellScanAna[lteCell].DCoverTime += Math.Round((double)(dtFile.TestPoints[i].lTimeWithMillsecond
                    - (dtFile.TestPoints[i - 1].lTimeWithMillsecond)) / 1000L, 2);
            }
            if ((int)fRO_RP > cellScanAna[lteCell].IRO_RPMax)
                cellScanAna[lteCell].IRO_RPMax = (int)fRO_RP;
            if ((int)fRO_RP < cellScanAna[lteCell].IRO_RPMin)
                cellScanAna[lteCell].IRO_RPMin = (int)fRO_RP;
            cellScanAna[lteCell].IRO_RPSum += (int)fRO_RP;
            if (cellScanAna[lteCell].TestPointList.Count > 0)
                cellScanAna[lteCell].IR0_RPMean = cellScanAna[lteCell].IRO_RPSum / cellScanAna[lteCell].TestPointList.Count;
            cellScanAna[lteCell].ItpCount = cellScanAna[lteCell].TestPointList.Count;
            cellScanAna[lteCell].FCoverRate = 100.0 * cellScanAna[lteCell].CoverTestPoint.Count / cellScanAna[lteCell].ItpCount;
        }

        /// <summary>
        /// 获取预存或圈选的区域
        /// </summary>
        protected void InitRegionMop2()
        {
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    if (!mutRegionMopDic.ContainsKey(strGridType))
                    {
                        addRegionMop(resvRegionsDic, strGridType);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private void addRegionMop(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
            foreach (ResvRegion region in resvRegionsDic[strGridType])
            {
                if (!regionMop.ContainsKey(region.RegionName))
                {
                    MapOperation2 mapOp2 = new MapOperation2();
                    mapOp2.FillPolygon(region.Shape);
                    regionMop.Add(region.RegionName, mapOp2);
                }
            }
            mutRegionMopDic.Add(strGridType, regionMop);
        }

        /// <summary>
        /// 定位所在网格(点)
        /// </summary>
        protected void isContainPoint(double x, double y, ref string gridTypeGrid)
        {
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (mutRegionMopDic[gridType][grid].CheckPointInRegion(x, y))
                    {
                        gridTypeGrid = gridType + "," + grid;
                        break;
                    }
                }
            }
        }
        /// <summary>
        /// 显示结果窗
        /// </summary>
        protected override void fireShowFormAfterQuery()
        {
            if (ZTDIYQueryScanAnalysis_LTE.ScanTestPointList.Count > 0)
            {
                FileInfo fileInfo = new FileInfo();
                TestPoint tp = ZTDIYQueryScanAnalysis_LTE.ScanTestPointList[0];
                fileInfo.Name = tp.FileName;
                fileInfo.ProjectID = tp.ProjectType;
                fileInfo.ID = tp.FileID;
                fileInfo.LogTable = tp.LogTable;
                fileInfo.ServiceType = tp.ServiceType;
                fileInfo.SampleTbName = tp.SampleTbName;
                QueryCondition condition = new QueryCondition();
                condition.FileInfos.Add(fileInfo);

                DateTime timeStart = tp.DateTime.AddMinutes(-3);
                DateTime timeEnd = tp.DateTime.AddMinutes(1);
                condition.Periods.Add(new TimePeriod(timeStart, timeEnd));
                try
                {
                    DIYQueryReplayFilePeriodScanLTE qb = new DIYQueryReplayFilePeriodScanLTE(MainModel);
                  
                    qb.SetQueryCondition(condition);
                    qb.Query();

                    MainModel.MainForm.GetMapForm().GoToView(tp.Longitude, tp.Latitude);
                }
                catch
                {
                    //continue
                }

                MainModel.DTDataManager.Clear();
                foreach (TestPoint tpp in ZTDIYQueryScanAnalysis_LTE.ScanTestPointList)
                {
                    MainModel.DTDataManager.Add(tpp);
                }
                MainModel.FireDTDataChanged(this);
                foreach (MasterCom.RAMS.Func.MapSerialInfo serial in MainModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
                {
                    serial.Visible = true;
                }
                MainModel.DrawFlyLines = true;
                MainModel.MainForm.GetMapForm().GetDTLayer().Invalidate();
            }
            MainModel.MainForm.FireSpecialSampleQueried();
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTDIYQueryScanAnalysisForm).FullName);
            ZTDIYQueryScanAnalysisForm lteForm = obj == null ? null : obj as ZTDIYQueryScanAnalysisForm;
            if (lteForm == null || lteForm.IsDisposed)
            {
                lteForm = new ZTDIYQueryScanAnalysisForm(MainModel);
            }
            lteForm.FillData(eventResultList, cellResultList);
            
            if (!lteForm.Visible)
            {
                lteForm.Show(MainModel.MainForm);
            }
        }

        public static IComparer<Event> GetCompareByDTime()
        {
            if (comparerByDTime == null)
            {
                comparerByDTime = new ComparerByDTime();
            }
            return comparerByDTime;
        }
        private static IComparer<Event> comparerByDTime;
        public class ComparerByDTime : IComparer<Event>
        {
            public int Compare(Event x, Event y)
            {
                return x.DateTime.CompareTo(y.DateTime);
            }
        }
    }
    public class TestPointSortByMillsecondeTime
    {
        //实现排序的接口
        public static IComparer<TestPoint> GetCompareByTime()
        {
            if (comparerByTime == null)
            {
                comparerByTime = new CompareBySampleTime();
            }
            return comparerByTime;
        }
        public class CompareBySampleTime : IComparer<TestPoint>
        {
            public int Compare(TestPoint x, TestPoint y)
            {
                return x.lTimeWithMillsecond.CompareTo(y.lTimeWithMillsecond);
            }
        }
        private static IComparer<TestPoint> comparerByTime;
    }

    public class ScanAnalysisLTEInfo
    {
        public Event Evt { get; set; }
        public int IndexID { get; set; }

        public string StrLAC
        {
            get 
            {
                if(Evt["LAC"] == null || int.Parse(Evt["LAC"].ToString()) < 1)
                    return "";
                return  Evt["LAC"].ToString();
                
            }
        }

        public string StrCI
        {
            get 
            {
                if (Evt["CI"] == null || int.Parse(Evt["CI"].ToString()) < 1)
                    return "";
                return Evt["CI"].ToString();
            }
        }
        public string StrGridType { get; set; }
        public string StrGridName { get; set; }
        public FileInfo ReplayFile { get; set; }
        public string StrEventName 
        {
            get { return  Evt.Name == null? "" : Evt.Name; }
        }
        public string StrEventRoadName
        {
            get { return Evt.RoadPlaceDesc == null? "" : Evt.RoadPlaceDesc; }
        }
        public double DEventLongitude
        {
            get { return Evt.Longitude == 0? 0 : Evt.Longitude; }
        }
        public double DEventLatitude
        {
            get { return Evt.Latitude == 0? 0 : Evt.Latitude; }
        }
        public string StrTime
        {
            get { return Evt.DateTimeStringWithMillisecond == null ?
                "" : String.Format("{0}.{1:d3}", Evt.DateTime.ToString("yyyy-MM-dd HH:mm:ss"), Evt.Millisecond);
            }
        }

        public Dictionary<LTECell, CellSampleInfo> cellInfoDic { get; set; }
        public int ItpCount { get; set; }
        public double FCoverRate { get; set; }
        public string StrCoverRate
        {
            get { return FCoverRate.ToString("0.00") + "%"; }
        }
        public double DCoverTime { get; set; }
        public string StrCoverTime
        {
            get
            {
                return DCoverTime.ToString("0.00");
            }
        }
        public double DCoverTimeMax { get; set; }
        public int ICoverCellCount { get; set; }
        public int IR0_RPMean { get; set; }
        public int IRO_RPMax { get; set; }
        public int IRO_RPMin { get; set; }
        //--------------------小区维度
        public LTECell LteCell { get; set; }
        public List<TestPoint> TestPointList { get; set; }
        public List<TestPoint> CoverTestPoint { get; set; }
        public int IRO_RPSum { get; set; }
        public string StrCellName { get; set; }
        public int IEARFCN { get; set; }
        public int IPCI { get; set; }
        public string StrCoverRoad { get; set; }
        public ScanAnalysisLTEInfo()
        {
            StrGridType = "";
            StrGridName = "";
            Evt = new Event();
            ReplayFile = new FileInfo();
            TestPointList = new List<TestPoint>();
            CoverTestPoint = new List<TestPoint>();
            FCoverRate = 0;
            DCoverTime = 0;
            DCoverTimeMax = 0;
            cellInfoDic = new Dictionary<LTECell, CellSampleInfo>();
            LteCell = new LTECell();
            ICoverCellCount = 0;
            ItpCount = 0;
            IRO_RPSum = 0;
            IR0_RPMean = 0;
            IRO_RPMax = -999;
            IRO_RPMin = 999;
            StrCellName = "";
            IEARFCN = 0;
            IPCI = 0;
            StrCoverRoad = "";
        }
    }

    public class CellSampleInfo
    {       
        private int iCellMaxRO_RP { get; set; }
        private int iCellMinRO_RP { get; set; }
        private int iCellMeanRO_RP { get; set; } = 0;
        private int iCellSumRO_RP { get; set; } = 0;
        public long LCoverTime { get; set; } = 0;

        public double DCellCoverTime
        {
            get
            {
                return Math.Round((double)LCoverTime / 1000L, 2);
            }
        }

        public int ICellMaxRO_RP
        {
            get 
            {
                foreach (int iRO_RP in SampleList)
                {
                    if (iRO_RP > iCellMaxRO_RP)
                    {
                        iCellMaxRO_RP = iRO_RP;
                    }
                }
                return iCellMaxRO_RP;
            }
        }
        public int ICellMinRO_RP
        {
            get
            {
                foreach (int iRO_RP in SampleList)
                {
                    if (iRO_RP < iCellMinRO_RP)
                    {
                        iCellMinRO_RP = iRO_RP;
                    }
                }
                return iCellMinRO_RP; 
            }
        }
        public int ICellMeanRO_RP
        {
            get 
            {
                if (SampleList.Count > 0)
                {
                    iCellSumRO_RP = 0;
                    foreach (int iRO_RP in SampleList)
                    {
                        iCellSumRO_RP += iRO_RP;
                    }
                    iCellMeanRO_RP = iCellSumRO_RP / ICellCountRO_RP;
                }
                return iCellMeanRO_RP; 
            }
        }
        public int ICellCountRO_RP
        {
            get { return SampleList.Count; }

        }
        public LTECell cell { get; set; }
        public List<int> SampleList { get; set; }
        public int ITAC
        {
            get
            {
                return cell.TAC;
            }
        }
        public int IECI
        {
            get
            {
                return cell.ECI;
            }
        }
        public string StrCellName
        {
            get 
            {
                return cell.Name;
            }
        }
        public CellSampleInfo()
        {
            iCellMaxRO_RP = -999;
            iCellMinRO_RP = 999;
            iCellMeanRO_RP = 0;
            iCellSumRO_RP = 0;
            LCoverTime = 0;
            cell = new LTECell();
            SampleList = new List<int>();
        }
    }

    public class DIYQueryReplayFilePeriodScanLTE : DIYReplayFileWithinPeriodQuery
    {
        public DIYQueryReplayFilePeriodScanLTE(MainModel mainModel)
            : base(mainModel)
        {
        }
        DIYReplayOptionDlg replayOptionDlg = null;
        protected override DIYReplayContentOption getDIYReplayContent()
        {
            if (replayOptionDlg == null)
            {
                replayOptionDlg = new DIYReplayOptionDlg();
                replayOptionDlg.InitLoadInfo("", "隐藏");
            }
            if (Condition.FileInfos.Count > 0)
            {
                int svtype = Condition.FileInfos[0].ServiceType;
                replayOptionDlg.FillCurrentServiceType(svtype);
                return replayOptionDlg.SelectLastOption;
            }
            else
            {
                throw (new Exception("没有可用的配置读取！"));
            }
        }
    }
}
