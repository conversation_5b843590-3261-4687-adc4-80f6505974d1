﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS
{
    public class GDQueryCPUnitDirectByGrid : QueryBase
    {
        public MapFormItemSelection ItemSelection { get; set; }

        private CPModeDlgLsatCondition curCPModeCondition;

        public GDQueryCPUnitDirectByGrid(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20007, this.Name);
        }

        public override string IconName
        {
            get { return "IconCP"; }
        }

        public override string Name
        {
            get { return "栅格指标对比"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            CPModeTimeProjSettingDlg dlg = new CPModeTimeProjSettingDlg(mainModel, ItemSelection, curCPModeCondition);
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }

            curCPModeCondition = dlg.CurCPModeCondition;

            GDCPUnitShowForm form = null;

            form = MainModel.GetObjectFromBlackboard(typeof(GDCPUnitShowForm).FullName) as GDCPUnitShowForm;
            if (form == null || form.IsDisposed)
            {
                form = new GDCPUnitShowForm(MainModel, ItemSelection, this.condition,
                    dlg.CurCPModeCondition.CurConditionHost, dlg.CurCPModeCondition.CurConditionGuest);
                form.StartAnalysisData(dlg.CurCPModeCondition.CurCompareTemplate, dlg.CurCPModeCondition.IsChkRoad);
            }
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
    }
}
