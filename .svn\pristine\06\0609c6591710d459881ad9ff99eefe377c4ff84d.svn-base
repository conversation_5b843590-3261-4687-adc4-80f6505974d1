﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRReasonPnlBackCover : NRReasonPanelBase
    {
        public NRReasonPnlBackCover()
        {
            InitializeComponent();
        }

        public override void AttachReason(NRReasonBase reason)
        {
            base.AttachReason(reason);
            numDirectionDif.ValueChanged -= numDirectionDif_ValueChanged;
            numDirectionDif.Value = (decimal)((NRReasonsBackCover)reason).DirectionDif;
            numDirectionDif.ValueChanged += numDirectionDif_ValueChanged;
            numCell2TpDis.ValueChanged -= numCell2TpDis_ValueChanged;
            numCell2TpDis.Value = (decimal)((NRReasonsBackCover)reason).Cell2TpDis;
            numCell2TpDis.ValueChanged += numCell2TpDis_ValueChanged;
        }

        void numDirectionDif_ValueChanged(object sender, EventArgs e)
        {
            ((NRReasonsBackCover)reason).DirectionDif = (double)numDirectionDif.Value;
        }
        void numCell2TpDis_ValueChanged(object sender, EventArgs e)
        {
            ((NRReasonsBackCover)reason).Cell2TpDis = (double)numCell2TpDis.Value;
        }
    }
}
