﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRCallEndDelayInfo
    {
        public NRCallEndDelayInfo(Event evt)
        {
            this.EvtCallEnd = evt;
        }
        public int SN { get; set; }
        public string FileName
        {
            get { return this.EvtCallEnd == null ? "" : this.EvtCallEnd.FileName; }
        }
        public float DelaySeconds { get; set; }

        public Event EvtCallEnd { get; set; }
        public int? TAC
        {
            get {

                if (this.EvtCallEnd != null)
                {
                    int? tac = (int?)this.EvtCallEnd["LAC"];

                    if (tac != null && tac > 0)
                    {
                        return tac;
                    }
                }

                if (TpTac > 0)
                {
                    return TpTac;
                }

                return null;
            }
        }
        public long? NCI
        {
            get {

                if (this.EvtCallEnd != null)
                {
                    long? ci = (long?)this.EvtCallEnd["CI"];

                    if (ci != null && ci > 0)
                    {
                        return ci;
                    }
                }

                if (TpNci > 0)
                {
                    return TpNci;
                }

                return null;
            }
        }
        public double Longitude
        {
            get { return this.EvtCallEnd == null ? 0 : this.EvtCallEnd.Longitude; }
        }
        public double Latitude
        {
            get { return this.EvtCallEnd == null ? 0 : this.EvtCallEnd.Latitude; }
        }
        public int TpTac;
        public long TpNci;
        
        public List<TestPoint> TestPointBefore { get; set; } = new List<TestPoint>();
        public float? RsrpAvgBefore { get; set; }
        public float? SinrAvgBefore { get; set; }
        public List<TestPoint> TestPointAfter { get; set; } = new List<TestPoint>();
        public float? RsrpAvgAfter { get; set; }
        public float? SinrAvgAfter { get; set; }

        public void StatBeforeAndAfterInfo()
        {
            float? rsrp;
            float? sinr;

            statInfo(TestPointAfter, out rsrp, out sinr);
            RsrpAvgAfter = rsrp;
            SinrAvgAfter = sinr;

            statInfo(TestPointBefore, out rsrp, out sinr);
            RsrpAvgBefore = rsrp;
            SinrAvgBefore = sinr;
        }

        private void statInfo(List<TestPoint> testPoint, out float? rsrpAvg, out float? sinrAvg)
        {
            int rsrpCount = 0;
            float rsrpSum = 0;
            int sinrCount = 0;
            float sinrSum = 0;

            foreach (TestPoint tp in testPoint)
            {
                float? rsrp = (float?)tp["NR_SS_RSRP"];
                if (rsrp != null)
                {
                    rsrpCount++;
                    rsrpSum += (float)rsrp;
                }

                float? sinr = (float?)tp["NR_SS_SINR"];
                if (sinr != null)
                {
                    sinrCount++;
                    sinrSum += (float)sinr;
                }

                int? tac = (int?)tp["NR_TAC"];
                if (tac != null && tac > 0)
                {
                    TpTac = (int)tac;
                }

                long? nci = (long?)tp["NR_NCI"];
                if (nci != null && nci > 0)
                {
                    TpNci = (long)nci;
                }
            }

            rsrpAvg = rsrpCount > 0 ? (float?)(Math.Round(rsrpSum / rsrpCount, 2)) : null;
            sinrAvg = sinrCount > 0 ? (float?)(Math.Round(sinrSum / sinrCount, 2)) : null;
        }
    }
}
