﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTSCellNCellInfo;

namespace MasterCom.RAMS.ZTFunc
{
    public class QuerySCellNCellSignalLevDiffByFiles : QuerySCellNCellSignalLevDiff
    {
        private static QuerySCellNCellSignalLevDiffByFiles instance = null;
        public static new QuerySCellNCellSignalLevDiffByFiles GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QuerySCellNCellSignalLevDiffByFiles();
                    }
                }
            }
            return instance;
        }

        protected QuerySCellNCellSignalLevDiffByFiles()
            : base()
        {
        }

        public override string Name
        {
            get
            {
                return "主服邻区场强信息查询(按文件)";
            }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }
}
