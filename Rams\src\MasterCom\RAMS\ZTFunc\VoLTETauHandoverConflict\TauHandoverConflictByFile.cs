﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class TauHandoverConflictByFile : TauHandoverConflictQuery
    {
        private static TauHandoverConflictByFile instance = null;
        public static new TauHandoverConflictByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new TauHandoverConflictByFile();
                    }
                }
            }
            return instance;
        }

        protected TauHandoverConflictByFile()
            : base()
        {

        }

        public override string Name
        {
            get
            {
                return "VoLTE_TAU与切换冲突分析(按文件)";
            }
        }

        public override Model.MainModel.NeedSearchType needSearchType()
        {
            return Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false; 
        }
    }

    public class TauHandoverConflictByFile_FDD : TauHandoverConflictQuery_FDD
    {
        private static TauHandoverConflictByFile_FDD instance = null;
        public static new TauHandoverConflictByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new TauHandoverConflictByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected TauHandoverConflictByFile_FDD()
            : base()
        {

        }
        public override string Name
        {
            get
            {
                return "VoLTE_FDD TAU与切换冲突分析(按文件)";
            }
        }

        public override Model.MainModel.NeedSearchType needSearchType()
        {
            return Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
