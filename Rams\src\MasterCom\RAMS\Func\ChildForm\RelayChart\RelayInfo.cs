﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.Func
{
    public class RelayInfo
    {
        public enum Direction
        {
            Unknown,
            Right,
            Left
        }

        public RelayInfo()
        {
        }

        public RelayInfo(RelayDecoInfo relayDeco, string upString, string downString)
        {
            RelayDeco = relayDeco;
            UpString = upString;
            DownString = downString;
        }

        public RelayDecoInfo RelayDeco { get; set; }

        public string UpString { get; set; }

        public string DownString { get; set; }

        public Rectangle drawPlaceRect { get; set; }
    }


    public class TerminalEntitySet
    {
        public string Name { get; set; }
        public Image ImgIcon { get; set; }
        public TerminalEntitySet(string name, string imgPathName)
        {
            this.Name = name;
            try
            {
                this.ImgIcon = Image.FromFile(imgPathName);
            }
            catch
            {
                //continue
            }
        }
    }

    public class RelayDecoInfo
    {
        /// <summary>
        /// stack分段， abis接口 -0, A接口 -1
        /// </summary>
        public int stackLayerIndex { get; set; }
        public RelayInfo.Direction direction { get; set; }
        public string shortName { get; set; } = "";
        public Color color { get; set; }
        public RelayDecoInfo(RelayInfo.Direction dir)
        {
            this.direction = dir;
        }
        public RelayDecoInfo(RelayInfo.Direction dir, string shortName)
        {
            this.direction = dir;
            this.shortName = shortName;
            this.color = Color.Empty;//defualt not used
        }
        public RelayDecoInfo(RelayInfo.Direction dir, string shortName, Color color)
        {
            this.direction = dir;
            this.shortName = shortName;
            this.color = color;
        }
    }

    public class AbisSignalInfo : RelayInfo
    {
        private static int a_downward_OPC { get; set; } = 0;
        public EventSignalData relativeESD { get; set; }
        private static Dictionary<string, RelayDecoInfo> sigMsgName2Direction { get; set; }
        private static int tempOPC { get; set; } = 0;
        private static string RightorLeft { get; set; } = "";
        public AbisSignalInfo(EventSignalData esd, int DecodeType, int number)
        {
            this.relativeESD = esd;
            string signalDescription = esd.MsgTypeInfo;
            string timeString = esd.Time.ToString("HH:mm:ss");
            string channelInfo = esd.ChannelDesc;
            if (channelInfo != "A" && channelInfo != "Um")
            {
                setChannelInfo(esd, DecodeType, number, signalDescription, timeString, channelInfo);
            }
            else if (channelInfo == "A")
            {
                setChannelInfoA(esd, signalDescription, timeString);
            }
            else//Um
            {
                setChannelInfoUm(esd, signalDescription, timeString);
            }

        }

        private void setChannelInfo(EventSignalData esd, int DecodeType, int number, string signalDescription, string timeString, string channelInfo)
        {
            if (DecodeType == 0)//GSM数据处理
            {
                #region ====GSM====
                if (sigMsgName2Direction == null)
                {
                    sigMsgName2Direction = initDirectionDic();
                }
                UpString = signalDescription;
                RelayDecoInfo relayDeco = getRelayDecoInfoGSM(signalDescription);
                if (relayDeco != null)
                {
                    relayDeco.stackLayerIndex = 1;///***
                }
                RelayDeco = relayDeco;
                DownString = timeString + " " + channelInfo;
                #endregion
            }
            else if (DecodeType == 1)//TD数据处理
            {
                #region =====TD=======
                UpString = signalDescription;
                RelayDecoInfo relayDeco = getRelayDecoInfoTD(esd, number);
                relayDeco.stackLayerIndex = 1;///***
                RelayDeco = relayDeco;
                if (channelInfo == "Unknown")
                    DownString = timeString;
                else
                    DownString = timeString + " " + channelInfo;
                #endregion
            }
        }

        private static RelayDecoInfo getRelayDecoInfoGSM(string signalDescription)
        {
            RelayDecoInfo relayDeco = null;
            string strToken = signalDescription.Trim();
            if (!sigMsgName2Direction.TryGetValue(signalDescription.Trim(), out relayDeco))
            {
                if (strToken.StartsWith("DATA REQuest"))
                {
                    relayDeco = new RelayDecoInfo(Direction.Left);
                }
                else if (strToken.StartsWith("DATA INDication"))
                {
                    relayDeco = new RelayDecoInfo(Direction.Right);
                }
                else if (strToken.StartsWith("ESTablish INDication"))
                {
                    relayDeco = new RelayDecoInfo(Direction.Right);
                }
                else
                {
                    relayDeco = new RelayDecoInfo(Direction.Left);
                }

                if (strToken.IndexOf("ERROR") != -1)
                {
                    relayDeco.color = Color.Red;
                }
            }

            return relayDeco;
        }

        private RelayDecoInfo getRelayDecoInfoTD(EventSignalData esd, int number)
        {
            RelayDecoInfo relayDeco = null;
            if (number == 0)
            {
                tempOPC = esd.OPC;
                relayDeco = new RelayDecoInfo(Direction.Right);
                RightorLeft = "R";
            }
            else
            {
                if (tempOPC == esd.OPC)
                    if (RightorLeft == "R")
                    {
                        relayDeco = new RelayDecoInfo(Direction.Right);
                    }
                    else
                    {
                        relayDeco = new RelayDecoInfo(Direction.Left);
                    }
                else
                    if (RightorLeft == "R")
                {
                    relayDeco = new RelayDecoInfo(Direction.Left);
                }
                else
                {
                    relayDeco = new RelayDecoInfo(Direction.Right);
                }
            }

            return relayDeco;
        }

        private void setChannelInfoA(EventSignalData esd, string signalDescription, string timeString)
        {
            RelayDecoInfo relayDeco = new RelayDecoInfo(Direction.Right, esd.MsgTypeInfo, Color.Blue);
            relayDeco.stackLayerIndex = 2;///***
            RelayDeco = relayDeco;
            UpString = signalDescription;
            DownString = timeString;
            if (esd.MsgID == 1)
            {
                AbisSignalInfo.a_downward_OPC = esd.DPC;
                relayDeco.direction = Direction.Right;
            }
            else
            {
                if (esd.DPC == AbisSignalInfo.a_downward_OPC)
                {
                    relayDeco.direction = Direction.Right;
                }
                else
                {
                    relayDeco.direction = Direction.Left;
                }
            }
        }

        private void setChannelInfoUm(EventSignalData esd, string signalDescription, string timeString)
        {
            RelayDecoInfo relayDeco = new RelayDecoInfo(Direction.Right, esd.MsgTypeInfo, Color.Blue);
            relayDeco.stackLayerIndex = 0;///***
            RelayDeco = relayDeco;
            UpString = signalDescription;
            DownString = timeString;
            if (esd.DTMsgDirection == 1)
            {
                relayDeco.direction = Direction.Left;
            }
            else
            {
                relayDeco.direction = Direction.Right;
            }
        }

        private static Dictionary<string, RelayDecoInfo> initDirectionDic()
        {
            Dictionary<string, RelayDecoInfo> dic = new Dictionary<string, RelayDecoInfo>();
            dic["PAGING CoMmanD"] = new RelayDecoInfo(Direction.Left, "", Color.Green);
            dic["BS POWER CONTROL"] = new RelayDecoInfo(Direction.Left, "", Color.Blue);
            dic["CHANnel ACTIVation ACKnowledge"] = new RelayDecoInfo(RelayInfo.Direction.Right, "", Color.Yellow);
            dic["CHANnel ACTIVation"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Yellow);
            dic["CHANnel ReQuireD"] = new RelayDecoInfo(RelayInfo.Direction.Right, "", Color.Yellow);
            dic["DATA INDication (DTAP) (CC) Alerting"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi(DTAP)(CC) Alerting", Color.Green);
            dic["DATA INDication (DTAP) (CC) Call Confirmed"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi (DTAP)(CC) Call Confirmd", Color.Green);
            dic["DATA INDication (DTAP) (CC) Connect Acknowledge"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi (DTAP)(CC) Connect Ack)", Color.Green);
            dic["DATA INDication (DTAP) (CC) Connect"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi (DTAP)(CC) Connect", Color.Green);
            dic["DATA INDication (DTAP) (CC) Disconnect"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi(DTAP)(CC) Disconnect", Color.Yellow);
            dic["DATA INDication (DTAP) (CC) Release Complete"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi(DTAP)(CC) Release Complete", Color.Chocolate);
            dic["DATA INDication (DTAP) (CC) Setup"] = new RelayDecoInfo(RelayInfo.Direction.Right, "", Color.Green);
            dic["DATA INDication (DTAP) (MM) Authentication Response"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi (DTAP) (MM) Auth Response", Color.Purple);
            dic["DATA INDication (DTAP) (MM) CM Service Request"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi(DTAP) (MM) CM Service Request", Color.Green);
            dic["DATA INDication (DTAP) (MM) Identity Response"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi(DTAP) (MM) Identity Response", Color.Purple);
            dic["DATA INDication (DTAP) (RR) Assignment Complete"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi(DTAP) (RR) Assign Complete", Color.Green);
            dic["DATA INDication (DTAP) (RR) Ciphering Mode Complete"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi (DTAP) (RR) Cipher Mode Complete", Color.Purple);
            dic["DATA INDication (DTAP) (RR) Classmark Change"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi (DTAP) (RR) Classmark Change", Color.Black);
            dic["DATA INDication (DTAP) (RR) GPRS Suspension Request"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi (DTAP) (RR) GPRS Susp Request", Color.Orange);
            dic["DATA INDication (DTAP) (SMS) CP-DATA (RP) RP-DATA (MS to Network)"] = new RelayDecoInfo(RelayInfo.Direction.Right, "DATA INDi(DTAP)(SMS)CP-DATA(RP)RP-DATA(MS to Net)", Color.Orange);
            dic["DATA REQuest (DTAP) (CC) Alerting"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Green);
            dic["DATA REQuest (DTAP) (CC) Connect Acknowledge"] = new RelayDecoInfo(RelayInfo.Direction.Left, "DATA REQuest (DTAP) (CC) Connect Ack", Color.Green);
            dic["DATA REQuest (DTAP) (CC) Connect"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Green);
            dic["DATA REQuest (DTAP) (CC) Disconnect"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Yellow);
            dic["DATA REQuest (DTAP) (CC) Release"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Yellow);
            dic["DATA REQuest (DTAP) (CC) Setup"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Green);
            dic["DATA REQuest (DTAP) (MM) Authentication Request"] = new RelayDecoInfo(RelayInfo.Direction.Left, "DATA REQuest (DTAP) (MM) Auth Request", Color.Purple);
            dic["DATA REQuest (DTAP) (MM) CM Service Accept"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Green);
            dic["DATA REQuest (DTAP) (MM) Identity Request"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Purple);
            dic["DATA REQuest (DTAP) (MM) Location Updating Accept"] = new RelayDecoInfo(RelayInfo.Direction.Left, "DATA REQuest (DTAP) (MM) Loc Updt Accept", Color.YellowGreen);
            dic["DATA REQuest (DTAP) (RR) Assignment Command"] = new RelayDecoInfo(RelayInfo.Direction.Left, "DATA REQuest (DTAP) (RR) Assign Command", Color.Green);
            dic["DATA REQuest (DTAP) (RR) Channel Release"] = new RelayDecoInfo(RelayInfo.Direction.Left, "DATA REQuest (DTAP) (RR) Channel Release", Color.Blue);
            dic["DATA REQuest (DTAP) (RR) Handover Command"] = new RelayDecoInfo(RelayInfo.Direction.Left, "DATA Req (DTAP) (RR) Handover Command", Color.YellowGreen);
            dic["DATA REQuest (DTAP) (SMS) CP-ACK"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Orange);
            dic["DEACTIVATE SACCH"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Blue);
            dic["ENCRyption CoMmanD (DTAP) (RR) Ciphering Mode Command"] = new RelayDecoInfo(RelayInfo.Direction.Left, "ENCRypt CMD (DTAP) (RR) Ciph Mode Cmd", Color.Purple);
            dic["ERROR INDication"] = new RelayDecoInfo(RelayInfo.Direction.Right, "", Color.Red);
            dic["ESTablish INDication (DTAP) (MM) CM Service Request"] = new RelayDecoInfo(RelayInfo.Direction.Right, "ESTablish INDi (DTAP) (MM) CM Service Request", Color.Green);
            dic["ESTablish INDication (DTAP) (MM) Location Updating Request"] = new RelayDecoInfo(RelayInfo.Direction.Right, "ESTablish INDi (DTAP) (MM) Loc Updt Request", Color.YellowGreen);
            dic["ESTablish INDication (DTAP) (RR) Paging Response"] = new RelayDecoInfo(RelayInfo.Direction.Right, "", Color.Brown);
            dic["ESTablish INDication"] = new RelayDecoInfo(RelayInfo.Direction.Right, "", Color.Blue);
            dic["IMMEDIATE ASSIGN COMMAND (CCCH) (RR) Immediate Assignment"] = new RelayDecoInfo(RelayInfo.Direction.Left, "IMMEDIATE ASSIGN CMD (CCCH)(RR) Immed Assign", Color.Blue);
            dic["MEASurement RESult (DTAP) (RR) Measurement Report"] = new RelayDecoInfo(RelayInfo.Direction.Right, "MEASure RESult (DTAP)(RR) Measure Report", Color.DarkCyan);
            dic["MEASurement RESult"] = new RelayDecoInfo(RelayInfo.Direction.Right, "", Color.DarkCyan);
            dic["MS POWER CONTROL"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Blue);
            dic["RELease INDication"] = new RelayDecoInfo(RelayInfo.Direction.Right, "", Color.Chocolate);
            dic["RF CHANnel RELease ACKnowledge"] = new RelayDecoInfo(RelayInfo.Direction.Right, "RF CHANnel RELease ACK", Color.Chocolate);
            dic["RF CHANnel RELease"] = new RelayDecoInfo(RelayInfo.Direction.Left, "", Color.Chocolate);
            dic["SACCH INFO MODIFY (DTAP) (RR) System Information Type 5ter"] = new RelayDecoInfo(RelayInfo.Direction.Left, "SACCH INFO MODI(DTAP)(RR) Sys Infor Type 5ter", Color.CadetBlue);
            dic["HANDOver DETection"] = new RelayDecoInfo(RelayInfo.Direction.Right, "", Color.DarkOliveGreen);
            dic["CONNection FAILure"] = new RelayDecoInfo(RelayInfo.Direction.Right, "", Color.Red);
            return dic;
        }
    }

    public class EventSignalData
    {

        public string MsgTypeInfo { get; set; }

        public string ChannelDesc { get; set; }

        public int OPC { get; set; }

        public int MsgID { get; set; }

        public int DPC { get; set; }

        public int DTMsgDirection { get; set; }

        public DateTime Time { get; set; }
    }
}
