﻿namespace MasterCom.RAMS.ZTFunc.FindNearestSiteByPoints
{
    partial class ConditionDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabCtrl = new DevExpress.XtraTab.XtraTabControl();
            this.pageXls = new DevExpress.XtraTab.XtraTabPage();
            this.gridCtrlXls = new DevExpress.XtraGrid.GridControl();
            this.gvXls = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.label1 = new System.Windows.Forms.Label();
            this.btnEditPath = new DevExpress.XtraEditors.ButtonEdit();
            this.pageGIS = new DevExpress.XtraTab.XtraTabPage();
            this.btnRemove = new DevExpress.XtraEditors.SimpleButton();
            this.btnEnd = new DevExpress.XtraEditors.SimpleButton();
            this.gridCtrlGIS = new DevExpress.XtraGrid.GridControl();
            this.gvGIS = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnBegin = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.chkIndoorSite = new DevExpress.XtraEditors.CheckEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numRadius = new System.Windows.Forms.NumericUpDown();
            this.rdbNearest = new System.Windows.Forms.RadioButton();
            this.rdbNear = new System.Windows.Forms.RadioButton();
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).BeginInit();
            this.tabCtrl.SuspendLayout();
            this.pageXls.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlXls)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvXls)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnEditPath.Properties)).BeginInit();
            this.pageGIS.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlGIS)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvGIS)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIndoorSite.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).BeginInit();
            this.SuspendLayout();
            // 
            // tabCtrl
            // 
            this.tabCtrl.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabCtrl.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.tabCtrl.Appearance.ForeColor = System.Drawing.SystemColors.ActiveCaption;
            this.tabCtrl.Appearance.Options.UseBackColor = true;
            this.tabCtrl.Appearance.Options.UseForeColor = true;
            this.tabCtrl.HeaderAutoFill = DevExpress.Utils.DefaultBoolean.True;
            this.tabCtrl.Location = new System.Drawing.Point(12, 12);
            this.tabCtrl.Name = "tabCtrl";
            this.tabCtrl.SelectedTabPage = this.pageXls;
            this.tabCtrl.Size = new System.Drawing.Size(725, 324);
            this.tabCtrl.TabIndex = 6;
            this.tabCtrl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pageXls,
            this.pageGIS});
            // 
            // pageXls
            // 
            this.pageXls.Controls.Add(this.gridCtrlXls);
            this.pageXls.Controls.Add(this.label1);
            this.pageXls.Controls.Add(this.btnEditPath);
            this.pageXls.Name = "pageXls";
            this.pageXls.Size = new System.Drawing.Size(718, 294);
            this.pageXls.Text = "从 Excel 读取";
            // 
            // gridCtrlXls
            // 
            this.gridCtrlXls.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridCtrlXls.Location = new System.Drawing.Point(3, 40);
            this.gridCtrlXls.MainView = this.gvXls;
            this.gridCtrlXls.Name = "gridCtrlXls";
            this.gridCtrlXls.Size = new System.Drawing.Size(712, 257);
            this.gridCtrlXls.TabIndex = 5;
            this.gridCtrlXls.UseEmbeddedNavigator = true;
            this.gridCtrlXls.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvXls});
            // 
            // gvXls
            // 
            this.gvXls.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3});
            this.gvXls.GridControl = this.gridCtrlXls;
            this.gvXls.Name = "gvXls";
            this.gvXls.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.True;
            this.gvXls.OptionsView.EnableAppearanceEvenRow = true;
            this.gvXls.OptionsView.ShowGroupPanel = false;
            this.gvXls.OptionsView.ShowIndicator = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "点名称";
            this.gridColumn1.FieldName = "Name";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "经度";
            this.gridColumn2.FieldName = "Longitude";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "纬度";
            this.gridColumn3.FieldName = "Latitude";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(7, 18);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 4;
            this.label1.Text = "点信息文件：";
            // 
            // btnEditPath
            // 
            this.btnEditPath.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEditPath.EditValue = "请选择文件";
            this.btnEditPath.Location = new System.Drawing.Point(90, 13);
            this.btnEditPath.Name = "btnEditPath";
            this.btnEditPath.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.btnEditPath.Properties.ReadOnly = true;
            this.btnEditPath.Properties.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.btnEditPath_Properties_ButtonClick);
            this.btnEditPath.Size = new System.Drawing.Size(616, 21);
            this.btnEditPath.TabIndex = 3;
            this.btnEditPath.DoubleClick += new System.EventHandler(this.btnEditPath_DoubleClick);
            // 
            // pageGIS
            // 
            this.pageGIS.Controls.Add(this.btnRemove);
            this.pageGIS.Controls.Add(this.btnEnd);
            this.pageGIS.Controls.Add(this.gridCtrlGIS);
            this.pageGIS.Controls.Add(this.btnBegin);
            this.pageGIS.Name = "pageGIS";
            this.pageGIS.Size = new System.Drawing.Size(718, 294);
            this.pageGIS.Text = "从地图选取";
            // 
            // btnRemove
            // 
            this.btnRemove.Location = new System.Drawing.Point(640, 11);
            this.btnRemove.Name = "btnRemove";
            this.btnRemove.Size = new System.Drawing.Size(75, 23);
            this.btnRemove.TabIndex = 7;
            this.btnRemove.Text = "删除点";
            this.btnRemove.Click += new System.EventHandler(this.btnRemove_Click);
            // 
            // btnEnd
            // 
            this.btnEnd.Enabled = false;
            this.btnEnd.Location = new System.Drawing.Point(559, 11);
            this.btnEnd.Name = "btnEnd";
            this.btnEnd.Size = new System.Drawing.Size(75, 23);
            this.btnEnd.TabIndex = 7;
            this.btnEnd.Text = "结束点选";
            this.btnEnd.Click += new System.EventHandler(this.btnEnd_Click);
            // 
            // gridCtrlGIS
            // 
            this.gridCtrlGIS.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridCtrlGIS.Location = new System.Drawing.Point(3, 40);
            this.gridCtrlGIS.MainView = this.gvGIS;
            this.gridCtrlGIS.Name = "gridCtrlGIS";
            this.gridCtrlGIS.Size = new System.Drawing.Size(712, 257);
            this.gridCtrlGIS.TabIndex = 6;
            this.gridCtrlGIS.UseEmbeddedNavigator = true;
            this.gridCtrlGIS.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvGIS});
            // 
            // gvGIS
            // 
            this.gvGIS.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6});
            this.gvGIS.GridControl = this.gridCtrlGIS;
            this.gvGIS.Name = "gvGIS";
            this.gvGIS.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.True;
            this.gvGIS.OptionsView.EnableAppearanceEvenRow = true;
            this.gvGIS.OptionsView.ShowGroupPanel = false;
            this.gvGIS.OptionsView.ShowIndicator = false;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "点名称";
            this.gridColumn4.FieldName = "Name";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 0;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "经度";
            this.gridColumn5.FieldName = "Longitude";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 1;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "纬度";
            this.gridColumn6.FieldName = "Latitude";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 2;
            // 
            // btnBegin
            // 
            this.btnBegin.Location = new System.Drawing.Point(478, 11);
            this.btnBegin.Name = "btnBegin";
            this.btnBegin.Size = new System.Drawing.Size(75, 23);
            this.btnBegin.TabIndex = 7;
            this.btnBegin.Text = "开始点选";
            this.btnBegin.Click += new System.EventHandler(this.btnBegin_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(581, 374);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 7;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(662, 374);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // chkIndoorSite
            // 
            this.chkIndoorSite.Location = new System.Drawing.Point(579, 342);
            this.chkIndoorSite.Name = "chkIndoorSite";
            this.chkIndoorSite.Properties.Caption = "包括室分站点";
            this.chkIndoorSite.Size = new System.Drawing.Size(101, 19);
            this.chkIndoorSite.TabIndex = 8;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(395, 350);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 9;
            this.label2.Text = "范围：";
            this.label2.Visible = false;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(513, 350);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 11;
            this.label3.Text = "米";
            this.label3.Visible = false;
            // 
            // numRadius
            // 
            this.numRadius.Location = new System.Drawing.Point(442, 345);
            this.numRadius.Maximum = new decimal(new int[] {
            987654321,
            0,
            0,
            0});
            this.numRadius.Name = "numRadius";
            this.numRadius.Size = new System.Drawing.Size(65, 21);
            this.numRadius.TabIndex = 14;
            this.numRadius.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRadius.Value = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numRadius.Visible = false;
            // 
            // rdbNearest
            // 
            this.rdbNearest.AutoSize = true;
            this.rdbNearest.Checked = true;
            this.rdbNearest.Location = new System.Drawing.Point(140, 350);
            this.rdbNearest.Name = "rdbNearest";
            this.rdbNearest.Size = new System.Drawing.Size(95, 16);
            this.rdbNearest.TabIndex = 15;
            this.rdbNearest.TabStop = true;
            this.rdbNearest.Text = "查找最近基站";
            this.rdbNearest.UseVisualStyleBackColor = true;
            this.rdbNearest.CheckedChanged += new System.EventHandler(this.rdbNearest_CheckedChanged);
            // 
            // rdbNear
            // 
            this.rdbNear.AutoSize = true;
            this.rdbNear.Location = new System.Drawing.Point(283, 350);
            this.rdbNear.Name = "rdbNear";
            this.rdbNear.Size = new System.Drawing.Size(95, 16);
            this.rdbNear.TabIndex = 16;
            this.rdbNear.Text = "查找附近基站";
            this.rdbNear.UseVisualStyleBackColor = true;
            this.rdbNear.CheckedChanged += new System.EventHandler(this.rdbNear_CheckedChanged);
            // 
            // ConditionDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(749, 409);
            this.Controls.Add(this.rdbNear);
            this.Controls.Add(this.rdbNearest);
            this.Controls.Add(this.numRadius);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.chkIndoorSite);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.tabCtrl);
            this.Name = "ConditionDlg";
            this.Text = "加载坐标点信息";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.ConditionDlg_FormClosing);
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).EndInit();
            this.tabCtrl.ResumeLayout(false);
            this.pageXls.ResumeLayout(false);
            this.pageXls.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlXls)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvXls)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnEditPath.Properties)).EndInit();
            this.pageGIS.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlGIS)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvGIS)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIndoorSite.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl tabCtrl;
        private DevExpress.XtraTab.XtraTabPage pageXls;
        private DevExpress.XtraTab.XtraTabPage pageGIS;
        private DevExpress.XtraGrid.GridControl gridCtrlXls;
        private DevExpress.XtraGrid.Views.Grid.GridView gvXls;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.ButtonEdit btnEditPath;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnRemove;
        private DevExpress.XtraEditors.SimpleButton btnEnd;
        private DevExpress.XtraGrid.GridControl gridCtrlGIS;
        private DevExpress.XtraGrid.Views.Grid.GridView gvGIS;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraEditors.SimpleButton btnBegin;
        private DevExpress.XtraEditors.CheckEdit chkIndoorSite;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numRadius;
        private System.Windows.Forms.RadioButton rdbNearest;
        private System.Windows.Forms.RadioButton rdbNear;

    }
}