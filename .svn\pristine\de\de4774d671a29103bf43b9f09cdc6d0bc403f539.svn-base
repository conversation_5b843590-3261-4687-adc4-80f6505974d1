﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.Net
{
    public class ZTNBCellMissByRegion_W2W : ZTNBCellMissByRegion
    {
        public ZTNBCellMissByRegion_W2W(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "W2W邻区配置核查"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14012, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            cellGridDic.Clear();
            CellManager.GetInstance().GetWNBCellInfo();
            return true;
        }
        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            formulaSet.Add(DataScan_WCDMA.RSCPMeanValueID);
            formulaSet.Add(DataScan_WCDMA.RSCPMaxID);
            formulaSet.Add(DataScan_WCDMA.RSCPSampleNumID);
            return getTriadIDIgnoreServiceType(formulaSet);
        }
        protected override void fireShowResult()
        {
            ZTNBCellMissForm frm = MainModel.CreateResultForm(typeof(ZTNBCellMissForm)) as ZTNBCellMissForm;
            frm.FillDatas(cellGridDic, InspectType.W2W);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void getGridCells()
        {
            foreach (GridDataUnit grid in CurScanGridUnitMatrix)
            {
                StatDataSCAN_WCDMA dataScan_WCDMA = grid.GetStatData(typeof(StatDataSCAN_WCDMA)) as StatDataSCAN_WCDMA;
                if (dataScan_WCDMA != null)
                {
                    GridItem gi = new GridItem(grid.LTLng, grid.LTLat);
                    foreach (int cellID in dataScan_WCDMA.CellInicatorDic.Keys)
                    {
                        setCellGridDic(dataScan_WCDMA, gi, cellID);
                    }
                }
            }
        }

        private void setCellGridDic(StatDataSCAN_WCDMA dataScan_WCDMA, GridItem gi, int cellID)
        {
            if (!cellGridDic.ContainsKey(gi))
            {
                Dictionary<string, Dictionary<int, GSMCellRxLev>> serviceCellsInfo = new Dictionary<string, Dictionary<int, GSMCellRxLev>>();
                serviceCellsInfo["W"] = new Dictionary<int, GSMCellRxLev>();
                cellGridDic[gi] = serviceCellsInfo;
            }
            if (!cellGridDic[gi].ContainsKey("W"))
            {
                cellGridDic[gi]["W"] = new Dictionary<int, GSMCellRxLev>();
            }
            if (!cellGridDic[gi]["W"].ContainsKey(cellID))
            {
                cellGridDic[gi]["W"][cellID] = new GSMCellRxLev();
            }
            cellGridDic[gi]["W"][cellID].cellID = cellID;
            cellGridDic[gi]["W"][cellID].rxlevAvg = dataScan_WCDMA[cellID, DataScan_WCDMA.RSCPMeanValueID];
            cellGridDic[gi]["W"][cellID].rxlevMax = dataScan_WCDMA[cellID, DataScan_WCDMA.RSCPMaxID];

            double num = dataScan_WCDMA[cellID, DataScan_WCDMA.RSCPSampleNumID];
            if (!double.IsNaN(num))
            {
                cellGridDic[gi]["W"][cellID].sampleNum = (int)num;
            }
        }
    }
}
