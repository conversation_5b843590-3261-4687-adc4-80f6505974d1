﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.Util;
using System.Xml;
using System.Windows.Forms;
using DevExpress.Utils;
using System.Collections;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    /// <summary>
    /// KPI报告
    /// </summary>
    public class CQTKPIReport
    {
        /// <summary>
        /// 报表文件后缀
        /// </summary>
        public static string ReportExtension { get; set; } = ".ckr";//CQT KPI Report
        public CQTKPIReport()
        {
        }
        public CQTKPIReport(string name)
        {
            Name = name;
        }
        public string Name { get; set; } = "未命名";
        public int CarreerID { get; set; } = 1;
        public List<CQTKPIReportColumn> Columns { get; set; } = new List<CQTKPIReportColumn>();
        public List<CQTKPISummaryColumn> SummaryColumns { get; set; } = new List<CQTKPISummaryColumn>();
        public string FileName { get; set; }
        public virtual void Save()
        {
            if (FileName == null)
            {
                SaveFileDialog dlg = new SaveFileDialog();
                dlg.InitialDirectory = CQTKPIReportCfgManager.FolderName;
                dlg.FileName = CQTKPIReportCfgManager.FolderName + Name + ReportExtension;
                if (dlg.ShowDialog()==DialogResult.OK)
                {
                    FileName = dlg.FileName;
                    Save(FileName);
                }
            }
            else
            {
                Save(FileName);
            }
        }

        public void Save(string filePath)
        {
            this.FileName = filePath;
            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement configMain = configFile.AddConfig("Main");
            configFile.AddItem(configMain, "Report", this, AddItem);
            configFile.Save(FileName);
        }
        
        public virtual bool Load(string fileName)
        {
            bool openSucc = true;
            this.FileName = fileName;
            Columns = new List<CQTKPIReportColumn>();
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(fileName);
                configFile.GetItemValue("Main", "Report", GetItemValue);
            }
            catch
            {
                openSucc = false;
            }
            return openSucc;
        }

        public virtual XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is CQTKPIReport)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Name", Name);
                configFile.AddItem(item, "CarreerID", CarreerID);
                configFile.AddItem(item, "Columns", Columns, AddItem);
                configFile.AddItem(item, "SummaryColumns", SummaryColumns, AddItem);
                return item;
            }
            else if (value is CQTKPIReportColumn)
            {
                return (value as CQTKPIReportColumn).AddItem(configFile, config, null, value);
            }
            else if (value is CQTKPISummaryColumn)
            {
                return (value as CQTKPISummaryColumn).AddItem(configFile, config, null, value);
            }
            return null;
        }

        public virtual object GetItemValue(XmlConfigFile configFile, XmlElement item, string itemName)
        {
            if (itemName == this.GetType().Name)
            {
                Name = configFile.GetItemValue(item, "Name") as string;
                CarreerID = (int)configFile.GetItemValue(item, "CarreerID");
                List<object> list = configFile.GetItemValue(item, "Columns", GetItemValue) as List<object>;
                foreach (object value in list)
                {
                    if (value != null && value is CQTKPIReportColumn)
                    {
                        Columns.Add((CQTKPIReportColumn)value);
                    }
                }
                list = configFile.GetItemValue(item, "SummaryColumns", GetItemValue) as List<object>;
                foreach (object value in list)
                {
                    if (value != null && value is CQTKPISummaryColumn)
                    {
                        CQTKPISummaryColumn scol = (CQTKPISummaryColumn)value;
                        scol.SetColumns(Columns);
                        SummaryColumns.Add(scol);
                    }
                }
                return this;
            }
            else if (itemName == typeof(CQTKPIReportColumn).Name)
            {
                CQTKPIReportColumn column = new CQTKPIReportColumn();
                column.GetItemValue(configFile, item, itemName);
                return column;
            }
            else if (itemName == typeof(CQTKPISummaryColumn).Name)
            {
                CQTKPISummaryColumn column = new CQTKPISummaryColumn();
                column.GetItemValue(configFile, item, itemName);
                return column;
            }
            return null;
        }

        public override string ToString()
        {
            return Name;
        }

        public static CQTKPIReport Open(string fileName)
        {
            CQTKPIReport report = new CQTKPIReport();
            report.FileName = fileName;
            if (!report.Load(fileName))
            {
                return null;
            }
            return report;
        }

    }

    public class CQTKPIReport_PK : CQTKPIReport
    {
        public static new string ReportExtension { get; set; } = ".ckrp";//CQT KPI Report PK
        public static new CQTKPIReport_PK Open(string fileName)
        {
            CQTKPIReport_PK report = new CQTKPIReport_PK();
            report.FileName = fileName;
            if (!report.Load(fileName))
            {
                return null;
            }
            return report;
        }
        public int CarreerID2 { get; set; } = 2;
        public List<CQTKPIReportColumn_PK> Columns_PK { get; set; } = new List<CQTKPIReportColumn_PK>();
        public CQTKPIReport_PK()
        {
        }
        public CQTKPIReport_PK(string name)
        {
            Name = name;
        }
        public override XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is CQTKPIReport_PK)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Name", Name);
                configFile.AddItem(item, "CarreerID", CarreerID);
                configFile.AddItem(item, "CarreerID2", CarreerID2);
                configFile.AddItem(item, "Columns", Columns_PK, AddItem);
                return item;
            }
            else if (value is CQTKPIReportColumn_PK)
            {
                return (value as CQTKPIReportColumn_PK).AddItem(configFile, config, null, value);
            }
            return null;
        }
        public override object GetItemValue(XmlConfigFile configFile, XmlElement item, string itemName)
        {
            if (itemName == this.GetType().Name)
            {
                Name = configFile.GetItemValue(item, "Name") as string;
                CarreerID = (int)configFile.GetItemValue(item, "CarreerID");
                CarreerID2 = (int)configFile.GetItemValue(item, "CarreerID2");
                List<object> list = configFile.GetItemValue(item, "Columns", GetItemValue) as List<object>;
                foreach (object value in list)
                {
                    if (value != null && value is CQTKPIReportColumn_PK)
                    {
                        Columns_PK.Add((CQTKPIReportColumn_PK)value);
                    }
                }
                return this;
            }
            else if (itemName == typeof(CQTKPIReportColumn_PK).Name)
            {
                CQTKPIReportColumn_PK column = new CQTKPIReportColumn_PK();
                column.GetItemValue(configFile, item, itemName);
                return column;
            }
            return null;
        }

        public override void Save()
        {
            if (FileName == null)
            {
                SaveFileDialog dlg = new SaveFileDialog();
                dlg.InitialDirectory = CQTKPIReportCfgManager.FolderName;
                dlg.FileName = CQTKPIReportCfgManager.FolderName + Name + ReportExtension;
                if (dlg.ShowDialog() == DialogResult.OK)
                {
                    FileName = dlg.FileName;
                    Save(FileName);
                }
            }
            else
            {
                Save(FileName);
            }
        }

    }

    /// <summary>
    /// KPI报告指标列
    /// </summary>
    public class CQTKPIReportColumn
    {
        public event EventHandler PropertyChanged;
        private void firePropertyChanged()
        {
            if (PropertyChanged != null)
            {
                PropertyChanged(this, EventArgs.Empty);
            }
        }
        public CQTKPIReportColumn()
        {
            id = Guid.NewGuid().ToString();
        }
        public CQTKPIReportColumn(string name, int carreerID, float kpiValueMin, float kpiValueMax, double scoreMin, double scoreMax)
        {
            Name = name;
            CarreerID = carreerID;
            KPIValueRangeMin = kpiValueMin;
            KPIValueRangeMax = kpiValueMax;
            ScoreRangeMin = scoreMin;
            ScoreRangeMax = scoreMax;
            ScoreScheme = new CQTKPIScoreScheme(kpiValueMin, kpiValueMax, scoreMin, scoreMax, ScoreOrderType.Positive);
            id = Guid.NewGuid().ToString();
        }
        public override string ToString()
        {
            return name;
        }
        protected string id;
        public string ID
        {
            get { return id; }
        }
        public int CarreerID { get; set; } = 1;
        private string name="未命名";
        public string Name
        {
            get { return name; }
            set
            {
                if (name != value)
                {
                    name = value;
                    firePropertyChanged();
                }
            }
        }
        public string Formula { get; set; } = "请定制公式！";
        protected float kpiValueMin = 0;
        public virtual float KPIValueRangeMin
        {
            get { return kpiValueMin; }
            set
            {
                if (kpiValueMin != value)
                {
                    kpiValueMin = value;
                    ScoreScheme.MinKPIValue = value;
                }
            }
        }
        protected float kpiValueMax = 100;
        public virtual float KPIValueRangeMax
        {
            get { return kpiValueMax; }
            set
            {
                if (kpiValueMax != value)
                {
                    kpiValueMax = value;
                    ScoreScheme.MaxKPIValue = value;
                }
            }
        }
        protected double scoreMin = 0;
        public virtual double ScoreRangeMin
        {
            get { return scoreMin; }
            set
            {
                if (scoreMin!=value)
                {
                    scoreMin = value;
                    ScoreScheme.MinScore = value;
                }
            }
        }
        protected  double scoreMax = 100;
        public virtual double ScoreRangeMax
        {
            get { return scoreMax; }
            set
            {
                if (scoreMax!=value)
                {
                    scoreMax = value;
                    ScoreScheme.MaxScore = value;
                }
            }
        }
        public CQTKPIScoreScheme ScoreScheme { get; set; } = new CQTKPIScoreScheme();

        public virtual XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is CQTKPIReportColumn)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Name", Name);
                configFile.AddItem(item, "CarreerID", CarreerID);
                configFile.AddItem(item, "Formula",Formula);
                configFile.AddItem(item, "KPIValueRangeMin", KPIValueRangeMin);
                configFile.AddItem(item, "KPIValueRangeMax", KPIValueRangeMax);
                configFile.AddItem(item, "ScoreRangeMin", ScoreRangeMin);
                configFile.AddItem(item, "ScoreRangeMax", ScoreRangeMax);
                configFile.AddItem(item, "ScoreScheme", ScoreScheme, AddItem);
                return item;
            }
            else if (value is CQTKPIScoreScheme)
            {
                return (value as CQTKPIScoreScheme).AddItem(configFile, config, name, value);
            }
            return null;
        }

        public virtual object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName == this.GetType().Name)
            {
                ScoreScheme = new CQTKPIScoreScheme();
                Name = configFile.GetItemValue(item, "Name") as string;
                Formula = configFile.GetItemValue(item, "Formula") as string;
                object value = null;
                value = configFile.GetItemValue(item, "CarreerID");
                if (value != null)
                {
                    CarreerID = (int)value;
                }
                value = configFile.GetItemValue(item, "KPIValueRangeMin");
                if (value != null)
                {
                    KPIValueRangeMin = (float)value;
                }
                value = configFile.GetItemValue(item, "KPIValueRangeMax");
                if (value != null)
                {
                    KPIValueRangeMax = (float)value;
                }
                value = configFile.GetItemValue(item, "ScoreRangeMin");
                if (value != null)
                {
                    ScoreRangeMin = (double)value;
                }
                value = configFile.GetItemValue(item, "ScoreRangeMax");
                if (value != null)
                {
                    ScoreRangeMax = (double)value;
                }
                ScoreScheme = configFile.GetItemValue(item, "ScoreScheme", GetItemValue) as CQTKPIScoreScheme;
                return this;
            }
            else if (typeName.Equals(typeof(CQTKPIScoreScheme).Name))
            {
                CQTKPIScoreScheme scheme = new CQTKPIScoreScheme();
                scheme.GetItemValue(configFile, item, typeName);
                return scheme;
            }
            return null;
        }
    }

    public class CQTKPIReportColumn_PK : CQTKPIReportColumn
    {
        public override double ScoreRangeMin
        {
            get
            {
                return scoreMin;
            }
            set
            {
               if (scoreMin!=value)
               {
                   scoreMin = value;
                   ScoreScheme_PK.MinScore = value;
               }
            }
        }
        public override double ScoreRangeMax
        {
            get
            {
                return scoreMax;
            }
            set
            {
               if (scoreMax!=value)
               {
                   scoreMax = value;
                   ScoreScheme_PK.MaxScore = value;
               }
            }
        }
        public override float KPIValueRangeMin
        {
            get
            {
                return kpiValueMin ;
            }
            set
            {
                if (kpiValueMin!=value)
                {
                    kpiValueMin = value;
                    ScoreScheme_PK.MinKPIValue = value;
                }
            }
        }
        public override float KPIValueRangeMax
        {
            get
            {
                return kpiValueMax;
            }
            set
            {
               if (kpiValueMax!=value)
               {
                   kpiValueMax = value;
                   ScoreScheme_PK.MaxKPIValue = value;
               }
            }
        }
        public int CarreerID2 { get; set; } = 2;
        public string Formula2 { get; set; } = "请定制公式！";
        private float kpiValueMin2 = 0;
        public float KPIValueRangeMin2
        {
            get
            {
                return kpiValueMin2;
            }
            set
            {
                if (kpiValueMin2 != value)
                {
                    kpiValueMin2 = value;
                    ScoreScheme_PK.MinKPIValue2 = value;
                }
            }
        }
        private float kpiValueMax2 = 100;
        public float KPIValueRangeMax2
        {
            get
            {
                return kpiValueMax2;
            }
            set
            {
                if (kpiValueMax2 != value)
                {
                    kpiValueMax2 = value;
                    ScoreScheme_PK.MaxKPIValue2 = value;
                }
            }
        }
        public override XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is CQTKPIReportColumn_PK)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Name", Name);
                configFile.AddItem(item, "CarreerID1", CarreerID);
                configFile.AddItem(item, "Formula1", Formula);
                configFile.AddItem(item, "KPIValueRangeMin1", KPIValueRangeMin);
                configFile.AddItem(item, "KPIValueRangeMax1", KPIValueRangeMax);
                configFile.AddItem(item, "CarreerID2", CarreerID2);
                configFile.AddItem(item, "Formula2", Formula2);
                configFile.AddItem(item, "KPIValueRangeMin2", KPIValueRangeMin2);
                configFile.AddItem(item, "KPIValueRangeMax2", KPIValueRangeMax2);
                configFile.AddItem(item, "ScoreRangeMin", ScoreRangeMin);
                configFile.AddItem(item, "ScoreRangeMax", ScoreRangeMax);
                configFile.AddItem(item, "ScoreScheme", ScoreScheme_PK, AddItem);
                return item;
            }
            else if (value is CQTKPIScoreScheme_PK)
            {
                return (value as CQTKPIScoreScheme_PK).AddItem(configFile, config, name, value);
            }
            return null;
        }

        public override object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName == this.GetType().Name)
            {
                ScoreScheme = new CQTKPIScoreScheme_PK();
                Name = configFile.GetItemValue(item, "Name") as string;
                Formula = configFile.GetItemValue(item, "Formula1") as string;
                Formula = configFile.GetItemValue(item, "Formula2") as string;

                CarreerID = getValidValue(configFile, item, "CarreerID1", CarreerID);
                CarreerID2 = getValidValue(configFile, item, "CarreerID2", CarreerID2);
                KPIValueRangeMin = getValidValue(configFile, item, "KPIValueRangeMin1", KPIValueRangeMin);
                KPIValueRangeMin2 = getValidValue(configFile, item, "KPIValueRangeMin2", KPIValueRangeMin2);
                KPIValueRangeMax2 = getValidValue(configFile, item, "KPIValueRangeMax2", KPIValueRangeMax2);
                ScoreRangeMin = getValidValue(configFile, item, "ScoreRangeMin", ScoreRangeMin);
                ScoreRangeMax = getValidValue(configFile, item, "ScoreRangeMax", ScoreRangeMax);

                scoreScheme_PK = configFile.GetItemValue(item, "ScoreScheme", GetItemValue) as CQTKPIScoreScheme_PK;
                return this;
            }
            else if (typeName.Equals(typeof(CQTKPIScoreScheme_PK).Name))
            {
                CQTKPIScoreScheme_PK scheme = new CQTKPIScoreScheme_PK();
                scheme.GetItemValue(configFile, item, typeName);
                return scheme;
            }
            return null;
        }

        private T getValidValue<T>(XmlConfigFile configFile, XmlElement item, string name, T defaultValue)
        {
            object value = configFile.GetItemValue(item, name);
            if (value != null)
            {
                return (T)value;
            }
            else
            {
                return defaultValue;
            }
        }

        private CQTKPIScoreScheme_PK scoreScheme_PK = new CQTKPIScoreScheme_PK();
        public CQTKPIScoreScheme_PK ScoreScheme_PK
        {
            get { return scoreScheme_PK; }
        }
    }

    public class CQTKPISummaryColumn
    {
        public CQTKPISummaryColumn()
        {
        }
        public CQTKPISummaryColumn(CQTKPIReport report, string colName)
        {
            Name = colName;
            SetColumns(report.Columns);
        }
        public void SetColumns(List<CQTKPIReportColumn> columns)
        {
            availableColumns = columns;
            double weight = Math.Round(1.0 / columns.Count, 5);
            foreach (CQTKPIReportColumn col in columns)
            {
                bool exist = false;
                foreach (SummaryItem item in summaryColumns)
                {
                    if (col.ID==item.ID)
                    {
                        item.Column = col;
                        exist = true;
                        break;
                    }
                }
                if (!exist)
                {
                    SummaryItem smryItem = new SummaryItem(col, weight);
                    summaryColumns.Add(smryItem);
                }
            }
        }
        public override string ToString()
        {
            return Name;
        }
        public string Name { get; set; } = "汇总列";
        private List<CQTKPIReportColumn> availableColumns = new List<CQTKPIReportColumn>();
        private readonly List<SummaryItem> summaryColumns = new List<SummaryItem>();
        public List<SummaryItem> SummaryColumns
        {
            get
            {
                setSummaryColumns();
                return summaryColumns;
            }
        }

        private void setSummaryColumns()
        {
            if (availableColumns.Count != summaryColumns.Count)//new 
            {
                addSummaryColumns();
            }
            List<int> needRemoveIdx = new List<int>();
            addNeedRemoveIdx(needRemoveIdx);
            foreach (int i in needRemoveIdx)
            {
                try
                {
                    summaryColumns.RemoveAt(i);
                }
                catch
                {
                    //continue
                }
            }
        }

        private void addSummaryColumns()
        {
            foreach (CQTKPIReportColumn col in availableColumns)
            {
                bool existCol = false;
                foreach (SummaryItem item in summaryColumns)
                {
                    if (col == item.Column)
                    {
                        existCol = true;
                        break;
                    }
                }
                if (!existCol)
                {
                    SummaryItem smryItem = new SummaryItem(col, Math.Round(1.0 / availableColumns.Count, 5));
                    summaryColumns.Add(smryItem);
                }
            }
        }

        private void addNeedRemoveIdx(List<int> needRemoveIdx)
        {
            for (int i = 0; i < summaryColumns.Count; i++)//remove
            {
                bool existCol = false;
                SummaryItem item = summaryColumns[i];
                foreach (CQTKPIReportColumn col in availableColumns)
                {
                    if (item.Column == col)
                    {
                        existCol = true;
                        break;
                    }
                }
                if (!existCol)
                {
                    needRemoveIdx.Add(i);
                }
            }
        }

        /// <summary>
        /// 评分着色
        /// </summary>
        public List<DTParameterRangeColor> ScoreRangeColors { get; set; } = new List<DTParameterRangeColor>();
        public Color GetColorByScore(double score)
        {
            Color color = Color.Empty;
            foreach (DTParameterRangeColor item in ScoreRangeColors)
            {
                if (item.Within((float)score))
                {
                    color = item.Value;
                    break;
                }
            }
            return color;
        }
        public virtual XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is CQTKPISummaryColumn)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Name", Name);
                configFile.AddItem(item, "Items", summaryColumns, AddItem);
                configFile.AddItem(item, "ScoreRangeColors", ScoreRangeColors, AddItem);
                return item;
            }
            else if (value is SummaryItem)
            {
                return (value as SummaryItem).AddItem(configFile, config, null, value);
            }
            else if (value is DTParameterRangeColor)
            {
                XmlElement item = configFile.AddItem(config, null, value.GetType());
                (value as DTParameterRangeColor).FillItem(configFile, item);
                return item;
            }
            return null;
        }

        public virtual object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName == this.GetType().Name)
            {
                Name = configFile.GetItemValue(item, "Name") as string;
                List<object> list = configFile.GetItemValue(item, "Items", GetItemValue) as List<object>;
                foreach (object o in list)
                {
                    if (o != null && o is SummaryItem)
                    {
                        summaryColumns.Add((SummaryItem)o);
                    }
                }
                list = configFile.GetItemValue(item, "ScoreRangeColors", GetItemValue) as List<object>;
                foreach (object o in list)
                {
                    if (o != null && o is DTParameterRangeColor)
                    {
                        ScoreRangeColors.Add((DTParameterRangeColor)o);
                    }
                }
                return this;
            }
            else if (typeName.Equals(typeof(SummaryItem).Name))
            {
                SummaryItem sItem = new SummaryItem();
                sItem.GetItemValue(configFile, item, typeName);
                return item;
            }
            else if (typeName.Equals(typeof(DTParameterRangeColor).Name))
            {
                DTParameterRangeColor range = new DTParameterRangeColor();
                range.FillItemValue(configFile, item);
                return range;
            }
            return null;
        }

    }

    public class SummaryItem
    {
        public SummaryItem() { }
        public CQTKPIReportColumn Column { get; set; }
        public double MinScore
        {
            get
            {
                return Column == null ? double.NaN : Column.ScoreRangeMin;
            }
        }
        public double MaxScore
        {
            get
            {
                return Column == null ? double.NaN : Column.ScoreRangeMax;
            }
        }
        public SummaryItem(CQTKPIReportColumn column, double weight)
        {
            this.Column = column;
            id = Column.ID;
            Name = Column.Name;
            this.Weight = weight;
        }
        private string id;
        public string ID
        {
            get { return id; }
        }
        public bool Included { get; set; }
        public string Name { get; set; }
        public double Weight { get; set; }

        public XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string Name, object value)
        {
            if (value is SummaryItem)
            {
                XmlElement item = configFile.AddItem(config, Name, value.GetType());
                configFile.AddTypeAttribute(item, typeof(CQTKPIScoreColorRange));
                configFile.AddItem(item, "Included", Included);
                configFile.AddItem(item, "ID", id);
                configFile.AddItem(item, "Name", Name);
                configFile.AddItem(item, "Weight", Weight);
                return item;
            }
            return null;
        }

        public object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName == "SummaryItem")
            {
                object value = configFile.GetItemValue(item, "Included");
                if (value != null)
                {
                    Included = (bool)value;
                }
                id = configFile.GetItemValue(item, "ID") as string;
                Name = configFile.GetItemValue(item, "Name") as string;
                value = configFile.GetItemValue(item, "Weight");
                if (value!=null)
                {
                    Weight = (double)value;
                }
                return this;
            }
            return null;
        }
    }


}
