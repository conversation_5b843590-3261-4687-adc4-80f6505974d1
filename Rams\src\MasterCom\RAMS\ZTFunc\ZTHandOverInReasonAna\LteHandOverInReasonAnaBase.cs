﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Chris.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteHandOverInReasonAnaBase : DIYAnalyseByCellBackgroundBaseByFile
    {
        protected List<int> hoSuccessEvtIdList = new List<int> { 851, 899 };
        List<HandoverReasonInfo> handOverReasonList = new List<HandoverReasonInfo>();
        public HandoverInReasonCondition HandoverInReasonCond { get; set; } = new HandoverInReasonCondition();
        protected static readonly object lockObj = new object();
        protected bool isVoLTE = false;
        protected LteHandOverInReasonAnaBase()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22094, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            return showFuncCondSetDlg();
        }

        protected virtual bool showFuncCondSetDlg()
        {
            HandoverInReasonConditiontDlg dlg = new HandoverInReasonConditiontDlg();
            dlg.SetCondition(HandoverInReasonCond);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                HandoverInReasonCond = dlg.GetCondition();
                return true;
            }
            return false;
        }
        protected override void getReadyBeforeQuery()
        {
            ServiceTypes.Clear();
            if (isVoLTE)
            {
                ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            }
            else
            {
                ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
                ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
                ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            }

            Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRQ");
            Columns.Add("lte_RSSI");
            Columns.Add("lte_SCell_LAC");
            Columns.Add("lte_SCell_CI");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
        }
        protected override void fireShowForm()
        {
            if (handOverReasonList.Count == 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("没有符合条件的信息！");
                return;
            }
            LteHandOverInReasonListForm frm = MainModel.CreateResultForm(typeof(LteHandOverInReasonListForm)) as LteHandOverInReasonListForm;
            frm.FillData(handOverReasonList, this.Name);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            handOverReasonList = new List<HandoverReasonInfo>();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
                {
                    List<DTData> dtDataList = fileMng.DTDatas;
                    dtDataList.Sort(comparer);
                    TestPoint lastTp = null;
                    int index = 0;
                    foreach (DTData data in dtDataList)
                    {
                        lastTp = dealDTData(dtDataList, lastTp, index, data);
                        index++;
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private TestPoint dealDTData(List<DTData> dtDataList, TestPoint lastTp, int index, DTData data)
        {
            if (data is TestPoint)
            {
                lastTp = data as TestPoint;
                if (!isValidTestPoint(lastTp))
                {
                    lastTp = null;
                }
            }
            else if (data is Event)
            {
                Event evt = data as Event;
                if (hoSuccessEvtIdList.Contains(evt.ID))
                {
                    TestPoint nextTp = getNextPoint(dtDataList, index);
                    HandoverReasonInfo info = new HandoverReasonInfo(evt);
                    info.AddTpInfo(lastTp, nextTp);

                    bool isUnReasonable;
                    info.ReasonDes = getReasonDes(info, out isUnReasonable);
                    if (isUnReasonable)
                    {
                        info.SN = handOverReasonList.Count + 1;
                        handOverReasonList.Add(info);
                    }
                }
            }

            return lastTp;
        }

        private TestPoint getNextPoint(List<DTData> dtDataList, int index)
        {
            TestPoint tp = null;
            for (int i = index; i < dtDataList.Count; i++)
            {
                if ((dtDataList[i].DateTime - dtDataList[index].DateTime).TotalSeconds > 10)
                {
                    break;
                }
                if (dtDataList[i] is TestPoint)
                {
                    TestPoint curTp = dtDataList[i] as TestPoint;
                    if (isValidTestPoint(curTp))
                    {
                        return curTp;
                    }
                }
            }
            return tp;
        }
        protected virtual string getReasonDes(HandoverReasonInfo info, out bool isUnReasonable)
        {
            isUnReasonable = false;

            StringBuilder strbDes = new StringBuilder();
            isUnReasonable = chkWeakSinr(info, isUnReasonable, strbDes);
            isUnReasonable = chkWeakRsrp(info, isUnReasonable, strbDes);
            isUnReasonable = chkWeakSinrRsrp(info, isUnReasonable, strbDes);
            if (strbDes.Length > 0)
            {
                strbDes = strbDes.Remove(strbDes.Length - 1, 1);
            }
            return strbDes.ToString();
        }

        private bool chkWeakSinr(HandoverReasonInfo info, bool isUnReasonable, StringBuilder strbDes)
        {
            if (HandoverInReasonCond.IsChkWeakSinr && info.SinrAfter != null && info.SinrAfter <= HandoverInReasonCond.SinrWeak1Gate)
            {
                isUnReasonable = true;
                if (info.RsrpBefore != null && info.RsrpBefore <= HandoverInReasonCond.RsrpBefore1Gate)
                {
                    strbDes.Append("切换前信号弱，且切换后质差，处理无线覆盖问题；");
                }
                if (info.SinrBefore != null && info.SinrBefore <= HandoverInReasonCond.SinrBefore1Gate)
                {
                    strbDes.Append("切换前、后质量差，处理质差问题；");
                }
                if (info.RsrpBefore != null && info.SinrBefore != null
                    && info.RsrpBefore > HandoverInReasonCond.RsrpBefore1Gate && info.SinrBefore > HandoverInReasonCond.SinrBefore1Gate)
                {
                    strbDes.Append("切换不合理；");
                }
            }

            return isUnReasonable;
        }

        private bool chkWeakRsrp(HandoverReasonInfo info, bool isUnReasonable, StringBuilder strbDes)
        {
            if (HandoverInReasonCond.IsChkWeakRsrp && info.RsrpAfter != null && info.RsrpAfter <= HandoverInReasonCond.RsrpWeak2Gate && info.RsrpBefore != null)
            {
                isUnReasonable = true;
                if (info.RsrpBefore <= HandoverInReasonCond.RsrpBefore2Gate)
                {
                    strbDes.Append("弱覆盖；");
                }
                else
                {
                    if (info.SinrBefore != null && info.SinrAfter != null && info.SinrBefore - info.SinrAfter > HandoverInReasonCond.SinrReduce2Gate)//切换后SINR恶化
                    {
                        strbDes.Append("切换不合理；");
                    }
                }
            }

            return isUnReasonable;
        }

        private bool chkWeakSinrRsrp(HandoverReasonInfo info, bool isUnReasonable, StringBuilder strbDes)
        {
            if (HandoverInReasonCond.IsChkWeakSinrRsrp && info.SinrAfter != null && info.RsrpAfter != null
                && info.SinrAfter > HandoverInReasonCond.SinrWeak3Gate && info.RsrpAfter > HandoverInReasonCond.RsrpWeak3Gate)
            {
                isUnReasonable = true;
                if (info.SinrBefore != null && info.SinrAfter != null)
                {
                    float sinrReduce = (float)info.SinrBefore - (float)info.SinrAfter;
                    if (sinrReduce > HandoverInReasonCond.SinrReduce3Gate)
                    {
                        strbDes.Append("切换不合理，或者目标小区无线质差；");
                    }
                    else
                    {
                        strbDes.Append("切换正常；");
                    }
                }
            }

            return isUnReasonable;
        }

        protected override void getResultsAfterQuery()
        {
            //
        }
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (condition.Geometorys != null && condition.Geometorys.Region != null 
                    && !condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
                {
                    return false;
                }
                return true;
            }
            catch
            {
                return false;
            }
        }


        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.切换; }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Simple; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["IsChkWeakSinr"] = this.HandoverInReasonCond.IsChkWeakSinr;
                param["SinrWeak1Gate"] = this.HandoverInReasonCond.SinrWeak1Gate;
                param["RsrpBefore1Gate"] = this.HandoverInReasonCond.RsrpBefore1Gate;
                param["SinrBefore1Gate"] = this.HandoverInReasonCond.SinrBefore1Gate;
                param["IsChkWeakRsrp"] = this.HandoverInReasonCond.IsChkWeakRsrp;
                param["RsrpWeak2Gate"] = this.HandoverInReasonCond.RsrpWeak2Gate;
                param["RsrpBefore2Gate"] = this.HandoverInReasonCond.RsrpBefore2Gate;
                param["SinrReduce2Gate"] = this.HandoverInReasonCond.SinrReduce2Gate;
                param["IsChkWeakSinrRsrp"] = this.HandoverInReasonCond.IsChkWeakSinrRsrp;
                param["RsrpWeak3Gate"] = this.HandoverInReasonCond.RsrpWeak3Gate;
                param["SinrWeak3Gate"] = this.HandoverInReasonCond.SinrWeak3Gate;
                param["SinrReduce3Gate"] = this.HandoverInReasonCond.SinrReduce3Gate;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("IsChkWeakSinr"))
                {
                    this.HandoverInReasonCond.IsChkWeakSinr = (bool)param["IsChkWeakSinr"];
                }
                if (param.ContainsKey("SinrWeak1Gate"))
                {
                    this.HandoverInReasonCond.SinrWeak1Gate = (float)param["SinrWeak1Gate"];
                }
                if (param.ContainsKey("RsrpBefore1Gate"))
                {
                    this.HandoverInReasonCond.RsrpBefore1Gate = (float)param["RsrpBefore1Gate"];
                }
                if (param.ContainsKey("SinrBefore1Gate"))
                {
                    this.HandoverInReasonCond.SinrBefore1Gate = (float)param["SinrBefore1Gate"];
                }
                if (param.ContainsKey("IsChkWeakRsrp"))
                {
                    this.HandoverInReasonCond.IsChkWeakRsrp = (bool)param["IsChkWeakRsrp"];
                }
                if (param.ContainsKey("RsrpWeak2Gate"))
                {
                    this.HandoverInReasonCond.RsrpWeak2Gate = (float)param["RsrpWeak2Gate"];
                }
                if (param.ContainsKey("RsrpBefore2Gate"))
                {
                    this.HandoverInReasonCond.RsrpBefore2Gate = (float)param["RsrpBefore2Gate"];
                }
                if (param.ContainsKey("SinrReduce2Gate"))
                {
                    this.HandoverInReasonCond.SinrReduce2Gate = (float)param["SinrReduce2Gate"];
                }
                if (param.ContainsKey("IsChkWeakSinrRsrp"))
                {
                    this.HandoverInReasonCond.IsChkWeakSinrRsrp = (bool)param["IsChkWeakSinrRsrp"];
                }
                if (param.ContainsKey("RsrpWeak3Gate"))
                {
                    this.HandoverInReasonCond.RsrpWeak3Gate = (float)param["RsrpWeak3Gate"];
                }
                if (param.ContainsKey("SinrWeak3Gate"))
                {
                    this.HandoverInReasonCond.SinrWeak3Gate = (float)param["SinrWeak3Gate"];
                }
                if (param.ContainsKey("SinrReduce3Gate"))
                {
                    this.HandoverInReasonCond.SinrReduce3Gate = (float)param["SinrReduce3Gate"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CommonSimpleProperties(this, showFuncCondSetDlg);
            }
        }
        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (HandoverReasonInfo item in handOverReasonList)
            {
                if (item.ReasonDes == "切换正常")
                {
                    continue;
                }
                BackgroundResult result = item.ConvertToBackgroundResult(curAnaFileInfo);
                result.SubFuncID = GetSubFuncID();
                result.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, bgResultList);
            handOverReasonList.Clear();
        }
        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                bgResult.StatType = BackgroundStatType.None;//导出专题结果时只需要导出附加信息，这里StatType设为None

                string time = bgResult.GetImageValueString();
                float rsrpBefore = bgResult.GetImageValueFloat();
                float sinrBefore = bgResult.GetImageValueFloat();
                float rsrpAfter = bgResult.GetImageValueFloat();
                float sinrAfter = bgResult.GetImageValueFloat();
                string reasonDes = bgResult.GetImageValueString();

                StringBuilder strb = new StringBuilder();
                strb.Append("时间：" + time + "\r\n");
                strb.Append("经度：" + bgResult.LongitudeMid + "\r\n");
                strb.Append("纬度：" + bgResult.LatitudeMid + "\r\n");
                strb.Append("切换前RSRP：" + rsrpBefore + "\r\n");
                strb.Append("切换前SINR：" + sinrBefore + "\r\n");
                strb.Append("切换后RSRP：" + rsrpAfter + "\r\n");
                strb.Append("切换后SINR：" + sinrAfter + "\r\n");
                strb.Append("分析原因：" + reasonDes + "\r\n");
                strb.Append("文件名：" + bgResult.FileName);
                bgResult.ImageDesc = strb.ToString();
            }
        }
        #endregion
    }

    public class LteHandOverInReasonAnaBase_FDD : LteHandOverInReasonAnaBase
    { 
        protected LteHandOverInReasonAnaBase_FDD()
            : base()
        {
            this.hoSuccessEvtIdList = new List<int> { 3156, 3159 };
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26041, this.Name);
        }
        protected override void getReadyBeforeQuery()
        {
            ServiceTypes.Clear();
            
            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
            
            Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_RSRQ");
            Columns.Add("lte_fdd_RSSI");
            Columns.Add("lte_fdd_SCell_LAC");
            Columns.Add("lte_fdd_SCell_CI");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
        }

    }

    public class VoLteHandOverInReasonAnaBase_FDD : LteHandOverInReasonAnaBase_FDD
    {
        protected VoLteHandOverInReasonAnaBase_FDD()
            : base()
        {

        }
        protected override void getReadyBeforeQuery()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);

            Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_RSRQ");
            Columns.Add("lte_fdd_RSSI");
            Columns.Add("lte_fdd_SCell_LAC");
            Columns.Add("lte_fdd_SCell_CI");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30029, this.Name);
        }
    }

    public class HandoverReasonInfo
    {
        public HandoverReasonInfo(Event evt)
        {
            CurEvent = evt;
            PointList = new List<TestPoint>();
        }
        public List<TestPoint> PointList { get; set; }
        public int SN { get; set; }
        public Event CurEvent { get; set; }
        public string FileName { get { return CurEvent.FileName; } }
        public string Time { get { return CurEvent.DateTimeStringWithMillisecond; } }
        public double Longitude { get { return CurEvent.Longitude; } }
        public double Latitude { get { return CurEvent.Latitude; } }

        public float? RsrpBefore { get; set; }
        public float? SinrBefore { get; set; }
        public float? RsrpAfter { get; set; }
        public float? SinrAfter { get; set; }

        public string ReasonDes { get; set; }

        public void AddTpInfo(TestPoint beforeTp, TestPoint afterTp)
        {
            if (beforeTp != null)
            {
                PointList.Add(beforeTp);
                RsrpBefore = GetRSRP(beforeTp);
                SinrBefore = GetSINR(beforeTp);
            }
            if (afterTp != null)
            {
                PointList.Add(afterTp);
                RsrpAfter = GetRSRP(afterTp);
                SinrAfter = GetSINR(afterTp);
            }
        }

        protected virtual float? GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRP"];
            }
            return (float?)tp["lte_RSRP"];
        }
        protected virtual float? GetSINR(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_SINR"];
            }
            return (float?)tp["lte_SINR"];
        }
        public BackgroundResult ConvertToBackgroundResult(FileInfo file)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.CellType = BackgroundCellType.LTE;
            if (file != null)
            {
                bgResult.FileID = file.ID;
                bgResult.FileName = file.Name;
            }
            if (PointList.Count > 0)
            {
                bgResult.ISTime = PointList[0].Time;
                bgResult.IETime = PointList[PointList.Count -1].Time;
            }

            bgResult.LongitudeMid = this.Longitude;
            bgResult.LatitudeMid = this.Latitude;

            bgResult.AddImageValue(Time);
            bgResult.AddImageValue(RsrpBefore == null ? float.NaN : (float)RsrpBefore);
            bgResult.AddImageValue(SinrBefore == null ? float.NaN : (float)SinrBefore);
            bgResult.AddImageValue(RsrpAfter == null ? float.NaN : (float)RsrpAfter);
            bgResult.AddImageValue(SinrAfter == null ? float.NaN : (float)SinrAfter);
            bgResult.AddImageValue(ReasonDes);

            return bgResult;
        }
    }
}
