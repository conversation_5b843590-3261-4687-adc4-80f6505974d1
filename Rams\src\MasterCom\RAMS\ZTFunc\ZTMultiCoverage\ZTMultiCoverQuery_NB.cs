﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTMultiCoverQuery_NB : ZTMultiCoverQuery_LTE
    {
        public ZTMultiCoverQuery_NB(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "重叠覆盖分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34004, this.Name);
        }
    }
}
