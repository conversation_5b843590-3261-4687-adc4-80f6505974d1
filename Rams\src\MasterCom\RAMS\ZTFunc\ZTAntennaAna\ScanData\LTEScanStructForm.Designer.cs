﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LTEScanStructForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            this.scanAntDataXTCtrl = new DevExpress.XtraTab.XtraTabControl();
            this.xtpGridStat = new DevExpress.XtraTab.XtraTabPage();
            this.dgViewGrid = new System.Windows.Forms.DataGridView();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn15 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn9 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn14 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn12 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn17 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column39 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column9 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column10 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column11 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column12 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column14 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column15 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column16 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column17 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column19 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column20 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column21 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column24 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column22 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowChart = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowGis = new System.Windows.Forms.ToolStripMenuItem();
            this.拆分导出CSVToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportWholeExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.xtpDetail = new DevExpress.XtraTab.XtraTabPage();
            this.dgViewSinr = new System.Windows.Forms.DataGridView();
            this.dgViewSample = new System.Windows.Forms.DataGridView();
            this.xtpCell = new DevExpress.XtraTab.XtraTabPage();
            this.dgViewCell = new System.Windows.Forms.DataGridView();
            this.Column32 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column31 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column30 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column38 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column37 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column36 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column35 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column34 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column33 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column29 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column28 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column27 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column26 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col7para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col6para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col5para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col4para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col3para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col2para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col1para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colaltitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col3ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col2ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col1ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col6db = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col3db = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colgmax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcovertype = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colbeamwidth = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcgi = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colvender = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellDistAvg = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSinrAvg = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSinrRate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colRsrpAvg = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSampleNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colFreq = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colBscName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colGridName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCell = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCityName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colDate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgSampleDistance = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgSinr = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgRsrp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIangle_dir = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIaltitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIangle_ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAnaType = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSampleTotal = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgSampleDistance = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSinr = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colMaxRsrp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgRsrp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSamplePect = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colRxlevSampleNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSection = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.coldMaxPcc0_30_150_180 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellChanel = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colBcch = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellname = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.cbbxSeries1 = new System.Windows.Forms.ComboBox();
            this.cbbxSeries2 = new System.Windows.Forms.ComboBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.panel1 = new System.Windows.Forms.Panel();
            this.dataGridViewAngle = new System.Windows.Forms.DataGridView();
            this.cluTarget = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cluCellName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.groupBox12 = new System.Windows.Forms.GroupBox();
            this.rtbDesc = new System.Windows.Forms.RichTextBox();
            this.groupBox15 = new System.Windows.Forms.GroupBox();
            this.groupBox13 = new System.Windows.Forms.GroupBox();
            this.xtpRoadStat = new DevExpress.XtraTab.XtraTabPage();
            this.dgViewRoad = new System.Windows.Forms.DataGridView();
            ((System.ComponentModel.ISupportInitialize)(this.scanAntDataXTCtrl)).BeginInit();
            this.scanAntDataXTCtrl.SuspendLayout();
            this.xtpGridStat.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgViewGrid)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.xtpDetail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgViewSinr)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgViewSample)).BeginInit();
            this.xtpCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgViewCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).BeginInit();
            this.xtpRoadStat.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgViewRoad)).BeginInit();
            this.SuspendLayout();
            // 
            // scanAntDataXTCtrl
            // 
            this.scanAntDataXTCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.scanAntDataXTCtrl.Location = new System.Drawing.Point(0, 0);
            this.scanAntDataXTCtrl.Name = "scanAntDataXTCtrl";
            this.scanAntDataXTCtrl.SelectedTabPage = this.xtpGridStat;
            this.scanAntDataXTCtrl.Size = new System.Drawing.Size(1184, 650);
            this.scanAntDataXTCtrl.TabIndex = 1;
            this.scanAntDataXTCtrl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtpGridStat,
            this.xtpDetail,
            this.xtpCell,
            this.xtpRoadStat});
            this.scanAntDataXTCtrl.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.scanAntDataXTCtrl_SelectedPageChanged);
            // 
            // xtpGridStat
            // 
            this.xtpGridStat.Controls.Add(this.dgViewGrid);
            this.xtpGridStat.Name = "xtpGridStat";
            this.xtpGridStat.Size = new System.Drawing.Size(1177, 620);
            this.xtpGridStat.Text = "网格指标统计";
            // 
            // dgViewGrid
            // 
            this.dgViewGrid.AllowUserToAddRows = false;
            this.dgViewGrid.AllowUserToDeleteRows = false;
            this.dgViewGrid.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dgViewGrid.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgViewGrid.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dataGridViewTextBoxColumn2,
            this.dataGridViewTextBoxColumn3,
            this.dataGridViewTextBoxColumn4,
            this.dataGridViewTextBoxColumn15,
            this.dataGridViewTextBoxColumn5,
            this.dataGridViewTextBoxColumn6,
            this.dataGridViewTextBoxColumn9,
            this.dataGridViewTextBoxColumn14,
            this.dataGridViewTextBoxColumn12,
            this.dataGridViewTextBoxColumn17,
            this.dataGridViewTextBoxColumn8,
            this.Column39,
            this.Column1,
            this.Column2,
            this.Column6,
            this.Column7,
            this.Column9,
            this.Column10,
            this.Column11,
            this.Column12,
            this.Column14,
            this.Column15,
            this.Column16,
            this.Column17,
            this.Column19,
            this.Column20,
            this.Column21,
            this.Column24,
            this.Column22});
            this.dgViewGrid.ContextMenuStrip = this.contextMenuStrip;
            this.dgViewGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgViewGrid.Location = new System.Drawing.Point(0, 0);
            this.dgViewGrid.Name = "dgViewGrid";
            this.dgViewGrid.RowTemplate.Height = 23;
            this.dgViewGrid.Size = new System.Drawing.Size(1177, 620);
            this.dgViewGrid.TabIndex = 3;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.HeaderText = "地市";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            this.dataGridViewTextBoxColumn2.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn3
            // 
            this.dataGridViewTextBoxColumn3.HeaderText = "小区名";
            this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            this.dataGridViewTextBoxColumn3.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn4
            // 
            this.dataGridViewTextBoxColumn4.HeaderText = "所在网格";
            this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
            this.dataGridViewTextBoxColumn4.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn15
            // 
            this.dataGridViewTextBoxColumn15.HeaderText = "主设备厂家";
            this.dataGridViewTextBoxColumn15.Name = "dataGridViewTextBoxColumn15";
            this.dataGridViewTextBoxColumn15.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn5
            // 
            this.dataGridViewTextBoxColumn5.HeaderText = "BSC名";
            this.dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
            this.dataGridViewTextBoxColumn5.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn6
            // 
            this.dataGridViewTextBoxColumn6.HeaderText = "频点标示";
            this.dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
            this.dataGridViewTextBoxColumn6.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn9
            // 
            this.dataGridViewTextBoxColumn9.HeaderText = "小区RSRP均值";
            this.dataGridViewTextBoxColumn9.Name = "dataGridViewTextBoxColumn9";
            this.dataGridViewTextBoxColumn9.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn14
            // 
            this.dataGridViewTextBoxColumn14.HeaderText = "小区SINR均值";
            this.dataGridViewTextBoxColumn14.Name = "dataGridViewTextBoxColumn14";
            this.dataGridViewTextBoxColumn14.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn12
            // 
            this.dataGridViewTextBoxColumn12.HeaderText = "小区平均通信距离";
            this.dataGridViewTextBoxColumn12.Name = "dataGridViewTextBoxColumn12";
            this.dataGridViewTextBoxColumn12.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn17
            // 
            this.dataGridViewTextBoxColumn17.HeaderText = "波束宽度";
            this.dataGridViewTextBoxColumn17.Name = "dataGridViewTextBoxColumn17";
            this.dataGridViewTextBoxColumn17.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn8
            // 
            this.dataGridViewTextBoxColumn8.HeaderText = "采样点总数";
            this.dataGridViewTextBoxColumn8.Name = "dataGridViewTextBoxColumn8";
            this.dataGridViewTextBoxColumn8.ReadOnly = true;
            // 
            // Column39
            // 
            this.Column39.HeaderText = "天线方位角";
            this.Column39.Name = "Column39";
            // 
            // Column1
            // 
            this.Column1.HeaderText = "±(0,60°)范围内采样点比例";
            this.Column1.Name = "Column1";
            // 
            // Column2
            // 
            this.Column2.HeaderText = "±(0,60°)范围内最强信号强度";
            this.Column2.Name = "Column2";
            // 
            // Column6
            // 
            this.Column6.HeaderText = "疑似旁瓣数量";
            this.Column6.Name = "Column6";
            // 
            // Column7
            // 
            this.Column7.HeaderText = "旁瓣1辐射方向";
            this.Column7.Name = "Column7";
            // 
            // Column9
            // 
            this.Column9.HeaderText = "旁瓣1最强信号强度";
            this.Column9.Name = "Column9";
            // 
            // Column10
            // 
            this.Column10.HeaderText = "旁瓣1平均信号强度";
            this.Column10.Name = "Column10";
            // 
            // Column11
            // 
            this.Column11.HeaderText = "旁瓣1采样点比例";
            this.Column11.Name = "Column11";
            // 
            // Column12
            // 
            this.Column12.HeaderText = "旁瓣2辐射方向";
            this.Column12.Name = "Column12";
            // 
            // Column14
            // 
            this.Column14.HeaderText = "旁瓣2最强信号强度";
            this.Column14.Name = "Column14";
            // 
            // Column15
            // 
            this.Column15.HeaderText = "旁瓣2平均信号强度";
            this.Column15.Name = "Column15";
            // 
            // Column16
            // 
            this.Column16.HeaderText = "旁瓣2采样点比例";
            this.Column16.Name = "Column16";
            // 
            // Column17
            // 
            this.Column17.HeaderText = "旁瓣3辐射方向";
            this.Column17.Name = "Column17";
            // 
            // Column19
            // 
            this.Column19.HeaderText = "旁瓣3最强信号强度";
            this.Column19.Name = "Column19";
            // 
            // Column20
            // 
            this.Column20.HeaderText = "旁瓣3平均信号强度";
            this.Column20.Name = "Column20";
            // 
            // Column21
            // 
            this.Column21.HeaderText = "旁瓣3采样点比例";
            this.Column21.Name = "Column21";
            // 
            // Column24
            // 
            this.Column24.HeaderText = "背瓣采样点比例";
            this.Column24.Name = "Column24";
            // 
            // Column22
            // 
            this.Column22.HeaderText = "前后比";
            this.Column22.Name = "Column22";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowChart,
            this.miShowGis,
            this.拆分导出CSVToolStripMenuItem,
            this.miExportWholeExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(149, 92);
            // 
            // miShowChart
            // 
            this.miShowChart.Name = "miShowChart";
            this.miShowChart.Size = new System.Drawing.Size(148, 22);
            this.miShowChart.Text = "二维四象分析";
            this.miShowChart.Click += new System.EventHandler(this.miShowChart_Click);
            // 
            // miShowGis
            // 
            this.miShowGis.Name = "miShowGis";
            this.miShowGis.Size = new System.Drawing.Size(148, 22);
            this.miShowGis.Text = "显示采样点";
            this.miShowGis.Click += new System.EventHandler(this.miShowGis_Click);
            // 
            // 拆分导出CSVToolStripMenuItem
            // 
            this.拆分导出CSVToolStripMenuItem.Name = "拆分导出CSVToolStripMenuItem";
            this.拆分导出CSVToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.拆分导出CSVToolStripMenuItem.Text = "拆分导出CSV";
            this.拆分导出CSVToolStripMenuItem.Click += new System.EventHandler(this.拆分导出CSVToolStripMenuItem_Click);
            // 
            // miExportWholeExcel
            // 
            this.miExportWholeExcel.Name = "miExportWholeExcel";
            this.miExportWholeExcel.Size = new System.Drawing.Size(148, 22);
            this.miExportWholeExcel.Text = "导出Excel";
            this.miExportWholeExcel.Click += new System.EventHandler(this.miExportWholeExcel_Click);
            // 
            // xtpDetail
            // 
            this.xtpDetail.Controls.Add(this.dgViewSinr);
            this.xtpDetail.Controls.Add(this.dgViewSample);
            this.xtpDetail.Name = "xtpDetail";
            this.xtpDetail.Size = new System.Drawing.Size(1177, 620);
            this.xtpDetail.Text = "二维四象优化分析";
            // 
            // dgViewSinr
            // 
            this.dgViewSinr.AllowUserToAddRows = false;
            this.dgViewSinr.AllowUserToDeleteRows = false;
            this.dgViewSinr.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgViewSinr.BackgroundColor = System.Drawing.Color.White;
            this.dgViewSinr.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgViewSinr.Location = new System.Drawing.Point(589, 0);
            this.dgViewSinr.Name = "dgViewSinr";
            this.dgViewSinr.RowTemplate.Height = 23;
            this.dgViewSinr.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect;
            this.dgViewSinr.Size = new System.Drawing.Size(588, 620);
            this.dgViewSinr.TabIndex = 1;
            // 
            // dgViewSample
            // 
            this.dgViewSample.AllowUserToAddRows = false;
            this.dgViewSample.AllowUserToDeleteRows = false;
            this.dgViewSample.BackgroundColor = System.Drawing.Color.White;
            this.dgViewSample.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgViewSample.ContextMenuStrip = this.contextMenuStrip;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgViewSample.DefaultCellStyle = dataGridViewCellStyle1;
            this.dgViewSample.Location = new System.Drawing.Point(0, 0);
            this.dgViewSample.Name = "dgViewSample";
            this.dgViewSample.RowTemplate.Height = 23;
            this.dgViewSample.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect;
            this.dgViewSample.Size = new System.Drawing.Size(588, 620);
            this.dgViewSample.TabIndex = 0;
            // 
            // xtpCell
            // 
            this.xtpCell.Controls.Add(this.dgViewCell);
            this.xtpCell.Name = "xtpCell";
            this.xtpCell.Size = new System.Drawing.Size(1177, 620);
            this.xtpCell.Text = "小区级数据统计";
            // 
            // dgViewCell
            // 
            this.dgViewCell.AllowUserToAddRows = false;
            this.dgViewCell.AllowUserToDeleteRows = false;
            this.dgViewCell.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgViewCell.BackgroundColor = System.Drawing.Color.White;
            this.dgViewCell.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgViewCell.ContextMenuStrip = this.contextMenuStrip;
            this.dgViewCell.Location = new System.Drawing.Point(3, 3);
            this.dgViewCell.Name = "dgViewCell";
            this.dgViewCell.RowTemplate.Height = 23;
            this.dgViewCell.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect;
            this.dgViewCell.Size = new System.Drawing.Size(1171, 614);
            this.dgViewCell.TabIndex = 2;
            // 
            // Column32
            // 
            this.Column32.HeaderText = "MR天线分析";
            this.Column32.Name = "Column32";
            // 
            // Column31
            // 
            this.Column31.HeaderText = "与天线方位角差值";
            this.Column31.Name = "Column31";
            // 
            // Column30
            // 
            this.Column30.HeaderText = "MR覆盖角";
            this.Column30.Name = "Column30";
            // 
            // Column38
            // 
            this.Column38.HeaderText = "过覆盖小区";
            this.Column38.Name = "Column38";
            // 
            // Column37
            // 
            this.Column37.HeaderText = "高重叠覆盖小区";
            this.Column37.Name = "Column37";
            // 
            // Column36
            // 
            this.Column36.HeaderText = "过覆盖影响小区数";
            this.Column36.Name = "Column36";
            // 
            // Column35
            // 
            this.Column35.HeaderText = "重叠覆盖指数";
            this.Column35.Name = "Column35";
            // 
            // Column34
            // 
            this.Column34.HeaderText = "重叠覆盖条件采样点数";
            this.Column34.Name = "Column34";
            // 
            // Column33
            // 
            this.Column33.HeaderText = "MRO总采样点数";
            this.Column33.Name = "Column33";
            // 
            // Column29
            // 
            this.Column29.HeaderText = "110覆盖率";
            this.Column29.Name = "Column29";
            // 
            // Column28
            // 
            this.Column28.HeaderText = "95覆盖率";
            this.Column28.Name = "Column28";
            // 
            // Column27
            // 
            this.Column27.HeaderText = "SINR均值";
            this.Column27.Name = "Column27";
            // 
            // Column26
            // 
            this.Column26.HeaderText = "RSRP均值";
            this.Column26.Name = "Column26";
            // 
            // col7para
            // 
            this.col7para.HeaderText = "ERAB掉线率(%)";
            this.col7para.Name = "col7para";
            this.col7para.ReadOnly = true;
            // 
            // col6para
            // 
            this.col6para.HeaderText = "ERAB建立成功率(%)";
            this.col6para.Name = "col6para";
            this.col6para.ReadOnly = true;
            // 
            // col5para
            // 
            this.col5para.HeaderText = "切换成功率(%)";
            this.col5para.Name = "col5para";
            this.col5para.ReadOnly = true;
            // 
            // col4para
            // 
            this.col4para.HeaderText = "无线掉线率(%)";
            this.col4para.Name = "col4para";
            this.col4para.ReadOnly = true;
            // 
            // col3para
            // 
            this.col3para.HeaderText = "无线接通率(%)";
            this.col3para.Name = "col3para";
            this.col3para.ReadOnly = true;
            // 
            // col2para
            // 
            this.col2para.HeaderText = "下行吞吐量(MB)";
            this.col2para.Name = "col2para";
            this.col2para.ReadOnly = true;
            // 
            // col1para
            // 
            this.col1para.HeaderText = "上行吞吐量(MB)";
            this.col1para.Name = "col1para";
            this.col1para.ReadOnly = true;
            // 
            // colaltitude
            // 
            this.colaltitude.HeaderText = "挂高";
            this.colaltitude.Name = "colaltitude";
            this.colaltitude.ReadOnly = true;
            // 
            // col3ob
            // 
            this.col3ob.HeaderText = "电调下倾角";
            this.col3ob.Name = "col3ob";
            this.col3ob.ReadOnly = true;
            // 
            // col2ob
            // 
            this.col2ob.HeaderText = "机械下倾角";
            this.col2ob.Name = "col2ob";
            this.col2ob.ReadOnly = true;
            // 
            // col1ob
            // 
            this.col1ob.HeaderText = "预置下倾角";
            this.col1ob.Name = "col1ob";
            this.col1ob.ReadOnly = true;
            // 
            // colphase8
            // 
            this.colphase8.HeaderText = "端口8相位";
            this.colphase8.Name = "colphase8";
            this.colphase8.ReadOnly = true;
            // 
            // colphase7
            // 
            this.colphase7.HeaderText = "端口7相位";
            this.colphase7.Name = "colphase7";
            this.colphase7.ReadOnly = true;
            // 
            // colphase6
            // 
            this.colphase6.HeaderText = "端口6相位";
            this.colphase6.Name = "colphase6";
            this.colphase6.ReadOnly = true;
            // 
            // colphase5
            // 
            this.colphase5.HeaderText = "端口5相位";
            this.colphase5.Name = "colphase5";
            this.colphase5.ReadOnly = true;
            // 
            // colphase4
            // 
            this.colphase4.HeaderText = "端口4相位";
            this.colphase4.Name = "colphase4";
            this.colphase4.ReadOnly = true;
            // 
            // colphase3
            // 
            this.colphase3.HeaderText = "端口3相位";
            this.colphase3.Name = "colphase3";
            this.colphase3.ReadOnly = true;
            // 
            // colphase2
            // 
            this.colphase2.HeaderText = "端口2相位";
            this.colphase2.Name = "colphase2";
            this.colphase2.ReadOnly = true;
            // 
            // colphase1
            // 
            this.colphase1.HeaderText = "端口1相位";
            this.colphase1.Name = "colphase1";
            this.colphase1.ReadOnly = true;
            // 
            // colrange8
            // 
            this.colrange8.HeaderText = "端口8幅度";
            this.colrange8.Name = "colrange8";
            this.colrange8.ReadOnly = true;
            // 
            // colrange7
            // 
            this.colrange7.HeaderText = "端口7幅度";
            this.colrange7.Name = "colrange7";
            this.colrange7.ReadOnly = true;
            // 
            // colrange6
            // 
            this.colrange6.HeaderText = "端口6幅度";
            this.colrange6.Name = "colrange6";
            this.colrange6.ReadOnly = true;
            // 
            // colrange5
            // 
            this.colrange5.HeaderText = "端口5幅度";
            this.colrange5.Name = "colrange5";
            this.colrange5.ReadOnly = true;
            // 
            // colrange4
            // 
            this.colrange4.HeaderText = "端口4幅度";
            this.colrange4.Name = "colrange4";
            this.colrange4.ReadOnly = true;
            // 
            // colrange3
            // 
            this.colrange3.HeaderText = "端口3幅度";
            this.colrange3.Name = "colrange3";
            this.colrange3.ReadOnly = true;
            // 
            // colrange2
            // 
            this.colrange2.HeaderText = "端口2幅度";
            this.colrange2.Name = "colrange2";
            this.colrange2.ReadOnly = true;
            // 
            // colrange1
            // 
            this.colrange1.HeaderText = "端口1幅度";
            this.colrange1.Name = "colrange1";
            this.colrange1.ReadOnly = true;
            // 
            // col6db
            // 
            this.col6db.HeaderText = "6dB功率角";
            this.col6db.Name = "col6db";
            this.col6db.ReadOnly = true;
            // 
            // col3db
            // 
            this.col3db.HeaderText = "3dB功率角";
            this.col3db.Name = "col3db";
            this.col3db.ReadOnly = true;
            // 
            // colgmax
            // 
            this.colgmax.HeaderText = "Gmax";
            this.colgmax.Name = "colgmax";
            this.colgmax.ReadOnly = true;
            // 
            // colcovertype
            // 
            this.colcovertype.HeaderText = "覆盖类型";
            this.colcovertype.Name = "colcovertype";
            this.colcovertype.ReadOnly = true;
            // 
            // colbeamwidth
            // 
            this.colbeamwidth.HeaderText = "波束宽度";
            this.colbeamwidth.Name = "colbeamwidth";
            this.colbeamwidth.ReadOnly = true;
            // 
            // colcgi
            // 
            this.colcgi.HeaderText = "CGI";
            this.colcgi.Name = "colcgi";
            this.colcgi.ReadOnly = true;
            // 
            // colvender
            // 
            this.colvender.HeaderText = "主设备厂家";
            this.colvender.Name = "colvender";
            this.colvender.ReadOnly = true;
            // 
            // colCellDistAvg
            // 
            this.colCellDistAvg.HeaderText = "小区平均通信距离";
            this.colCellDistAvg.Name = "colCellDistAvg";
            this.colCellDistAvg.ReadOnly = true;
            // 
            // colSinrAvg
            // 
            this.colSinrAvg.HeaderText = "小区C/I平均";
            this.colSinrAvg.Name = "colSinrAvg";
            this.colSinrAvg.ReadOnly = true;
            // 
            // colSinrRate
            // 
            this.colSinrRate.HeaderText = "小区SINR<=-3占比(%)";
            this.colSinrRate.Name = "colSinrRate";
            this.colSinrRate.ReadOnly = true;
            // 
            // colRsrpAvg
            // 
            this.colRsrpAvg.HeaderText = "小区RSRP均值";
            this.colRsrpAvg.Name = "colRsrpAvg";
            this.colRsrpAvg.ReadOnly = true;
            // 
            // colSampleNum
            // 
            this.colSampleNum.HeaderText = "采样点总数";
            this.colSampleNum.Name = "colSampleNum";
            this.colSampleNum.ReadOnly = true;
            // 
            // colFreq
            // 
            this.colFreq.HeaderText = "频点标示";
            this.colFreq.Name = "colFreq";
            this.colFreq.ReadOnly = true;
            // 
            // colBscName
            // 
            this.colBscName.HeaderText = "BSC名";
            this.colBscName.Name = "colBscName";
            this.colBscName.ReadOnly = true;
            // 
            // colGridName
            // 
            this.colGridName.HeaderText = "网格号";
            this.colGridName.Name = "colGridName";
            this.colGridName.ReadOnly = true;
            // 
            // colCell
            // 
            this.colCell.Frozen = true;
            this.colCell.HeaderText = "小区名";
            this.colCell.Name = "colCell";
            this.colCell.ReadOnly = true;
            // 
            // colCityName
            // 
            this.colCityName.Frozen = true;
            this.colCityName.HeaderText = "地市名称";
            this.colCityName.Name = "colCityName";
            this.colCityName.ReadOnly = true;
            // 
            // colDate
            // 
            this.colDate.Frozen = true;
            this.colDate.HeaderText = "时间";
            this.colDate.Name = "colDate";
            this.colDate.ReadOnly = true;
            // 
            // colCellAvgSampleDistance
            // 
            this.colCellAvgSampleDistance.HeaderText = "小区平均通信距离";
            this.colCellAvgSampleDistance.Name = "colCellAvgSampleDistance";
            this.colCellAvgSampleDistance.ReadOnly = true;
            // 
            // colCellAvgSinr
            // 
            this.colCellAvgSinr.HeaderText = "小区SINR平均";
            this.colCellAvgSinr.Name = "colCellAvgSinr";
            this.colCellAvgSinr.ReadOnly = true;
            // 
            // colCellAvgRsrp
            // 
            this.colCellAvgRsrp.HeaderText = "小区RSRP平均";
            this.colCellAvgRsrp.Name = "colCellAvgRsrp";
            this.colCellAvgRsrp.ReadOnly = true;
            // 
            // colIangle_dir
            // 
            this.colIangle_dir.HeaderText = "方向角";
            this.colIangle_dir.Name = "colIangle_dir";
            this.colIangle_dir.ReadOnly = true;
            // 
            // colIaltitude
            // 
            this.colIaltitude.HeaderText = "挂高";
            this.colIaltitude.Name = "colIaltitude";
            this.colIaltitude.ReadOnly = true;
            // 
            // colIangle_ob
            // 
            this.colIangle_ob.HeaderText = "下倾角";
            this.colIangle_ob.Name = "colIangle_ob";
            this.colIangle_ob.ReadOnly = true;
            // 
            // colAnaType
            // 
            this.colAnaType.HeaderText = "天线类型";
            this.colAnaType.Name = "colAnaType";
            this.colAnaType.ReadOnly = true;
            // 
            // colSampleTotal
            // 
            this.colSampleTotal.HeaderText = "总采样点数";
            this.colSampleTotal.Name = "colSampleTotal";
            this.colSampleTotal.ReadOnly = true;
            // 
            // colAvgSampleDistance
            // 
            this.colAvgSampleDistance.HeaderText = "区间平均通信距离";
            this.colAvgSampleDistance.Name = "colAvgSampleDistance";
            this.colAvgSampleDistance.ReadOnly = true;
            // 
            // colSinr
            // 
            this.colSinr.HeaderText = "区间SINR平均";
            this.colSinr.Name = "colSinr";
            this.colSinr.ReadOnly = true;
            // 
            // colMaxRsrp
            // 
            this.colMaxRsrp.HeaderText = "区间最大RSRP(dBm)";
            this.colMaxRsrp.Name = "colMaxRsrp";
            this.colMaxRsrp.ReadOnly = true;
            // 
            // colAvgRsrp
            // 
            this.colAvgRsrp.HeaderText = "区间平均RSRP(dBm)";
            this.colAvgRsrp.Name = "colAvgRsrp";
            this.colAvgRsrp.ReadOnly = true;
            // 
            // colSamplePect
            // 
            this.colSamplePect.HeaderText = "采样点占比";
            this.colSamplePect.Name = "colSamplePect";
            this.colSamplePect.ReadOnly = true;
            // 
            // colRxlevSampleNum
            // 
            this.colRxlevSampleNum.HeaderText = "区间采样点数";
            this.colRxlevSampleNum.Name = "colRxlevSampleNum";
            this.colRxlevSampleNum.ReadOnly = true;
            // 
            // colSection
            // 
            this.colSection.HeaderText = "方向角偏差区间";
            this.colSection.Name = "colSection";
            this.colSection.ReadOnly = true;
            // 
            // coldMaxPcc0_30_150_180
            // 
            this.coldMaxPcc0_30_150_180.HeaderText = "[0,30]区间与(150,180]区间最强RSRP的差值";
            this.coldMaxPcc0_30_150_180.Name = "coldMaxPcc0_30_150_180";
            this.coldMaxPcc0_30_150_180.ReadOnly = true;
            this.coldMaxPcc0_30_150_180.Width = 150;
            // 
            // cellChanel
            // 
            this.cellChanel.HeaderText = "频段";
            this.cellChanel.Name = "cellChanel";
            // 
            // colBcch
            // 
            this.colBcch.HeaderText = "频点";
            this.colBcch.Name = "colBcch";
            this.colBcch.ReadOnly = true;
            // 
            // colCellname
            // 
            this.colCellname.Frozen = true;
            this.colCellname.HeaderText = "小区名称";
            this.colCellname.Name = "colCellname";
            this.colCellname.ReadOnly = true;
            this.colCellname.Width = 250;
            // 
            // groupBox1
            // 
            this.groupBox1.Location = new System.Drawing.Point(10, 29);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1142, 50);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(67, 21);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 14);
            this.labelControl1.TabIndex = 0;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(299, 21);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 14);
            this.labelControl2.TabIndex = 1;
            // 
            // cbbxSeries1
            // 
            this.cbbxSeries1.FormattingEnabled = true;
            this.cbbxSeries1.Location = new System.Drawing.Point(133, 18);
            this.cbbxSeries1.Name = "cbbxSeries1";
            this.cbbxSeries1.Size = new System.Drawing.Size(116, 20);
            this.cbbxSeries1.TabIndex = 2;
            // 
            // cbbxSeries2
            // 
            this.cbbxSeries2.FormattingEnabled = true;
            this.cbbxSeries2.Location = new System.Drawing.Point(365, 18);
            this.cbbxSeries2.Name = "cbbxSeries2";
            this.cbbxSeries2.Size = new System.Drawing.Size(112, 20);
            this.cbbxSeries2.TabIndex = 3;
            // 
            // groupBox2
            // 
            this.groupBox2.Location = new System.Drawing.Point(10, 75);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(1142, 295);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            // 
            // panel1
            // 
            this.panel1.Location = new System.Drawing.Point(1092, 21);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(29, 268);
            this.panel1.TabIndex = 3;
            // 
            // dataGridViewAngle
            // 
            this.dataGridViewAngle.AllowUserToAddRows = false;
            this.dataGridViewAngle.AllowUserToDeleteRows = false;
            this.dataGridViewAngle.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewAngle.DefaultCellStyle = dataGridViewCellStyle2;
            this.dataGridViewAngle.Location = new System.Drawing.Point(12, 32);
            this.dataGridViewAngle.Name = "dataGridViewAngle";
            this.dataGridViewAngle.RowTemplate.Height = 23;
            this.dataGridViewAngle.Size = new System.Drawing.Size(1121, 181);
            this.dataGridViewAngle.TabIndex = 0;
            // 
            // cluTarget
            // 
            this.cluTarget.HeaderText = "指标项";
            this.cluTarget.Name = "cluTarget";
            // 
            // cluCellName
            // 
            this.cluCellName.HeaderText = "小区名称";
            this.cluCellName.Name = "cluCellName";
            this.cluCellName.Width = 150;
            // 
            // groupBox3
            // 
            this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox3.Location = new System.Drawing.Point(10, 26);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(583, 587);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            // 
            // groupBox4
            // 
            this.groupBox4.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox4.Location = new System.Drawing.Point(599, 304);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(571, 309);
            this.groupBox4.TabIndex = 1;
            this.groupBox4.TabStop = false;
            // 
            // groupBox6
            // 
            this.groupBox6.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox6.Location = new System.Drawing.Point(605, 26);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(565, 272);
            this.groupBox6.TabIndex = 2;
            this.groupBox6.TabStop = false;
            // 
            // groupBox9
            // 
            this.groupBox9.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox9.Location = new System.Drawing.Point(10, 26);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(576, 188);
            this.groupBox9.TabIndex = 0;
            this.groupBox9.TabStop = false;
            // 
            // groupBox7
            // 
            this.groupBox7.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox7.Location = new System.Drawing.Point(592, 26);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(576, 188);
            this.groupBox7.TabIndex = 2;
            this.groupBox7.TabStop = false;
            // 
            // groupBox10
            // 
            this.groupBox10.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox10.Location = new System.Drawing.Point(10, 220);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(576, 188);
            this.groupBox10.TabIndex = 3;
            this.groupBox10.TabStop = false;
            // 
            // groupBox11
            // 
            this.groupBox11.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox11.Location = new System.Drawing.Point(10, 414);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(576, 188);
            this.groupBox11.TabIndex = 1;
            this.groupBox11.TabStop = false;
            // 
            // groupBox8
            // 
            this.groupBox8.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox8.Location = new System.Drawing.Point(592, 220);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(576, 188);
            this.groupBox8.TabIndex = 4;
            this.groupBox8.TabStop = false;
            // 
            // groupBox12
            // 
            this.groupBox12.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox12.Location = new System.Drawing.Point(592, 414);
            this.groupBox12.Name = "groupBox12";
            this.groupBox12.Size = new System.Drawing.Size(576, 188);
            this.groupBox12.TabIndex = 4;
            this.groupBox12.TabStop = false;
            // 
            // rtbDesc
            // 
            this.rtbDesc.Location = new System.Drawing.Point(6, 21);
            this.rtbDesc.Name = "rtbDesc";
            this.rtbDesc.ReadOnly = true;
            this.rtbDesc.Size = new System.Drawing.Size(564, 161);
            this.rtbDesc.TabIndex = 0;
            this.rtbDesc.Text = "";
            // 
            // groupBox15
            // 
            this.groupBox15.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox15.Location = new System.Drawing.Point(10, 26);
            this.groupBox15.Name = "groupBox15";
            this.groupBox15.Size = new System.Drawing.Size(583, 587);
            this.groupBox15.TabIndex = 0;
            this.groupBox15.TabStop = false;
            // 
            // groupBox13
            // 
            this.groupBox13.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox13.Location = new System.Drawing.Point(599, 26);
            this.groupBox13.Name = "groupBox13";
            this.groupBox13.Size = new System.Drawing.Size(583, 587);
            this.groupBox13.TabIndex = 2;
            this.groupBox13.TabStop = false;
            // 
            // xtpRoadStat
            // 
            this.xtpRoadStat.Controls.Add(this.dgViewRoad);
            this.xtpRoadStat.Name = "xtpRoadStat";
            this.xtpRoadStat.Size = new System.Drawing.Size(1177, 620);
            this.xtpRoadStat.Text = "问题道路详情";
            // 
            // dgViewRoad
            // 
            this.dgViewRoad.AllowUserToAddRows = false;
            this.dgViewRoad.AllowUserToDeleteRows = false;
            this.dgViewRoad.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgViewRoad.BackgroundColor = System.Drawing.Color.White;
            this.dgViewRoad.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgViewRoad.ContextMenuStrip = this.contextMenuStrip;
            this.dgViewRoad.Location = new System.Drawing.Point(3, 3);
            this.dgViewRoad.Name = "dgViewRoad";
            this.dgViewRoad.RowTemplate.Height = 23;
            this.dgViewRoad.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect;
            this.dgViewRoad.Size = new System.Drawing.Size(1171, 614);
            this.dgViewRoad.TabIndex = 3;
            // 
            // LTEScanStructForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1184, 650);
            this.Controls.Add(this.scanAntDataXTCtrl);
            this.Name = "LTEScanStructForm";
            this.Tag = "";
            this.Text = "覆盖结构优化-二维四象分析";
            ((System.ComponentModel.ISupportInitialize)(this.scanAntDataXTCtrl)).EndInit();
            this.scanAntDataXTCtrl.ResumeLayout(false);
            this.xtpGridStat.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgViewGrid)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.xtpDetail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgViewSinr)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgViewSample)).EndInit();
            this.xtpCell.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgViewCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).EndInit();
            this.xtpRoadStat.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgViewRoad)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl scanAntDataXTCtrl;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miShowGis;
        private System.Windows.Forms.ToolStripMenuItem miExportWholeExcel;
        private System.Windows.Forms.ToolStripMenuItem 拆分导出CSVToolStripMenuItem;
        private DevExpress.XtraTab.XtraTabPage xtpGridStat;
        private System.Windows.Forms.DataGridView dgViewGrid;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn15;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn9;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn14;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn12;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn17;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn8;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column39;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column1;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column2;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column6;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column7;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column9;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column10;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column11;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column12;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column14;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column15;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column16;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column17;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column19;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column20;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column21;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column24;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column22;
        private DevExpress.XtraTab.XtraTabPage xtpDetail;
        private System.Windows.Forms.DataGridView dgViewSample;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column32;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column31;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column30;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column38;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column37;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column36;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column35;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column34;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column33;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column29;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column28;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column27;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column26;
        private System.Windows.Forms.DataGridViewTextBoxColumn col7para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col6para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col5para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col4para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col3para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col2para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col1para;
        private System.Windows.Forms.DataGridViewTextBoxColumn colaltitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn col3ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn col2ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn col1ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase8;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase7;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase6;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase5;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase4;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase1;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange8;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange7;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange6;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange5;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange4;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange1;
        private System.Windows.Forms.DataGridViewTextBoxColumn col6db;
        private System.Windows.Forms.DataGridViewTextBoxColumn col3db;
        private System.Windows.Forms.DataGridViewTextBoxColumn colgmax;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcovertype;
        private System.Windows.Forms.DataGridViewTextBoxColumn colbeamwidth;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcgi;
        private System.Windows.Forms.DataGridViewTextBoxColumn colvender;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellDistAvg;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSinrAvg;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSinrRate;
        private System.Windows.Forms.DataGridViewTextBoxColumn colRsrpAvg;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSampleNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn colFreq;
        private System.Windows.Forms.DataGridViewTextBoxColumn colBscName;
        private System.Windows.Forms.DataGridViewTextBoxColumn colGridName;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCell;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCityName;
        private System.Windows.Forms.DataGridViewTextBoxColumn colDate;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgSampleDistance;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgSinr;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgRsrp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIangle_dir;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIaltitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIangle_ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAnaType;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSampleTotal;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgSampleDistance;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSinr;
        private System.Windows.Forms.DataGridViewTextBoxColumn colMaxRsrp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgRsrp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSamplePect;
        private System.Windows.Forms.DataGridViewTextBoxColumn colRxlevSampleNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSection;
        private System.Windows.Forms.DataGridViewTextBoxColumn coldMaxPcc0_30_150_180;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellChanel;
        private System.Windows.Forms.DataGridViewTextBoxColumn colBcch;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellname;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private System.Windows.Forms.ComboBox cbbxSeries1;
        private System.Windows.Forms.ComboBox cbbxSeries2;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.DataGridView dataGridViewAngle;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluTarget;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluCellName;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.GroupBox groupBox11;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.GroupBox groupBox12;
        private System.Windows.Forms.RichTextBox rtbDesc;
        private System.Windows.Forms.GroupBox groupBox15;
        private System.Windows.Forms.GroupBox groupBox13;
        private System.Windows.Forms.ToolStripMenuItem miShowChart;
        private System.Windows.Forms.DataGridView dgViewSinr;
        private DevExpress.XtraTab.XtraTabPage xtpCell;
        private System.Windows.Forms.DataGridView dgViewCell;
        private DevExpress.XtraTab.XtraTabPage xtpRoadStat;
        private System.Windows.Forms.DataGridView dgViewRoad;
    }
}