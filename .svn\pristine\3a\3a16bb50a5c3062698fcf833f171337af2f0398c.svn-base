﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NR700MStationAcceptQuery : NRStationAcceptQuery
    {
        public NR700MStationAcceptQuery(MainModel mainModel)
       : base(mainModel)
        {
        }

        public override string Name
        {
            get
            {
                return "NR700M验收";
            }
        }

        NR700StationSettingDlgConfigModel_XJ curCondition = null;
        protected override bool isValidCondition()
        {
            List<FileInfo> nrFiles = new List<FileInfo>();
            foreach (var file in Condition.FileInfos)
            {
                ServiceName name = ServiceTypeManager.getServiceNameFromTypeID(file.ServiceType);
                if (name == ServiceName.NR)
                {
                    nrFiles.Add(file);
                }
            }
            if (nrFiles.Count == 0)
            {
                MessageBox.Show("请选择5G文件进行单验");
                return false;
            }
            Condition.FileInfos = nrFiles;

            curCondition = NR700StationSettingDlgConfig_XJ.Instance.LoadConfig();
            NR700StationSettingDlg_XJ dlg = new NR700StationSettingDlg_XJ();
            dlg.setCondition(curCondition);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }

            NR700MStationAcceptCondition cond = new NR700MStationAcceptCondition();
            cond.Init();
            FolderBrowserDialog Folderdlg = new FolderBrowserDialog();
            if (Folderdlg.ShowDialog() == DialogResult.OK)
            {
                cond.SaveFolder = Folderdlg.SelectedPath;
                initManager(cond);
                return true;
            }
            return false;
        }

        protected override void initManager(StationAcceptConditionBase cond)
        {
            manager = new NR700MStationAcceptManager();
            manager.SetAcceptCond(cond);
        }

        protected override List<string> queryColumns
        {
            get
            {
                return new List<string>()
                {
                     "isampleid",
                     "itime",
                     "ilongitude",
                     "ilatitude",
                     "NR_SSB_ARFCN",
                     "NR_PCI",
                     "NR_SS_RSRP",
                     "NR_SS_SINR",
                     "NR_Throughput_MAC_DL",
                     "NR_Throughput_MAC_UL",
                     "NR_APP_type",
                     "NR_APP_Status"
                };
            }
        }
    }

    public class NR700MStationAcceptCondition : NRStationAcceptCondition
    {
        public override void Init()
        {
            Standard = new NR700MThroughputStandard();
            Standard.Init();
        }
    }

    public class NR700MThroughputStandard : NRThroughputStandard
    {
        public override void Init()
        {
            ThroughputStandardList = new List<ThroughputStandard>()
            {
                new ThroughputStandard(NRServiceName.SA, 30, 4,
                  new ThroughputStandard.DataInfo( 200, 0, 80),
                  new ThroughputStandard.DataInfo(90, 0, 5)),
                new ThroughputStandard(NRServiceName.SA, 20, 4,
                  new ThroughputStandard.DataInfo( 135, 0, 52),
                  new ThroughputStandard.DataInfo(60, 0, 3.5)),
                new ThroughputStandard(NRServiceName.SA, 10, 4,
                  new ThroughputStandard.DataInfo( 67, 0, 26),
                  new ThroughputStandard.DataInfo(30, 0, 2)),
                new ThroughputStandard(NRServiceName.SA, 5, 4,
                  new ThroughputStandard.DataInfo( 33, 0, 13),
                  new ThroughputStandard.DataInfo(15, 0, 1)),
            };
        }
    }
}
