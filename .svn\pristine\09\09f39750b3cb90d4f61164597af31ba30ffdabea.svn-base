﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.Util;
using System.Windows.Forms;
using System.Runtime.Serialization;
using System.Collections;
using MasterCom.MControls;
using MasterCom.RAMS.Func.EventBlock;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Func.ProblemBlock;
namespace MasterCom.RAMS.Func
{
    [Serializable()]
    public class ProblemBlockEventOpLayer_GDI : CustomDrawLayer
    {
        public Pen selPen { get; set; } = new Pen(Color.Cyan, 3);
        private string name;
        public ProblemBlockEventOpLayer_GDI(MapOperation mp, string name)
            : base(mp, name)
        {
            this.name = name;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if(!IsVisible)
            {
                return;
            }
            updateRect.Inflate((int)(30 * 10000 / Map.Scale), (int)(30 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            double temp_long = Map.GetCenter().x;
            double temp_lati = Map.GetCenter().y;
            DbPoint ptDSel = new DbPoint(temp_long, temp_lati);
            PointF scrPointSel;
            Map.ToDisplay(ptDSel, out scrPointSel);
            //底层20米的精度跨度大小 0.0001951
            double llGap = (0.000195 / 20) * 100;
            DbPoint ptDSel2 = new DbPoint(temp_long + llGap, temp_lati);
            PointF scrPointSel2;
            Map.ToDisplay(ptDSel2, out scrPointSel2);
            int rGap = (int)((scrPointSel2.X - scrPointSel.X)/2)+1;
            if (name == "结构问题点事件图层")
                DrawItem(MainModel.CurProblemBlockList, dRect, rGap, graphics);
            else
                DrawItem(MainModel.CurProblemBlockEventList, dRect, rGap, graphics);
        }
        /// <summary>
        /// 结构问题点绘图
        /// </summary>
        private void DrawItem(List<ProblemBlockItem> problemBlockList, DbRect dRect, int rGap, Graphics graphics)
        {
            foreach (ProblemBlockItem block in problemBlockList)
            {
                List<ProblemBlockEventItem> evtList = block.AbEvents;
                Region blockReg = null;
                if (block.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
                {
                    foreach (ProblemBlockEventItem evt in evtList)
                    {
                        bool selected = false;
                        if (mainModel.MainForm.GetMapForm().CurSelProblemBlockEventID == evt.ieventId)
                        {
                            selected = true;
                        }
                        DbPoint dPoint = new DbPoint(evt.ilongitude, evt.ilatitude);
                        draw(rGap, graphics, evt.ieventId, selected, ref blockReg, dPoint);
                    }
                }
            }
        }
        
        private void DrawItem(List<EventInfoSecond> problemBlockEventList, DbRect dRect, int rGap, Graphics graphics)
        {
            foreach (EventInfoSecond block in problemBlockEventList)
            {
                if (ProblemBlockOpLayer_GDI.isOrder
                    && (block.strWorkOrder == null || block.strWorkOrder == ""))
                {
                    continue;
                }
                if (block.fLongitude >= dRect.x1 && block.fLongitude <= dRect.x2
                    && block.fLatitude >= dRect.y1 && block.fLatitude <= dRect.y2)
                {
                    bool selected = false;
                    if (mainModel.MainForm.GetMapForm().CurSelProblemBlockEventID == block.iid)
                    {
                        selected = true;
                    }
                    Region blockReg = null;
                    DbPoint dPoint = new DbPoint(block.fLongitude, block.fLatitude);
                    draw(rGap, graphics, block.iEventID, selected, ref blockReg, dPoint);
                }
            }
        }

        private void draw(int rGap, Graphics graphics, int evtID, bool selected, ref Region blockReg, DbPoint dPoint)
        {
            PointF point;
            this.Map.ToDisplay(dPoint, out point);
            float radius = rGap > 3 ? rGap : 3;
            RectangleF rectN = new RectangleF(point.X - radius, point.Y - radius, radius * 3 / 2, radius * 3 / 2);
            GraphicsPath gp = new GraphicsPath();
            gp.AddEllipse(rectN);
            if (blockReg == null)
            {
                blockReg = new Region(gp);
            }
            else
            {
                blockReg.Union(gp);
            }

            graphics.DrawImage(EventInfoManager.GetInstance()[evtID].Image, rectN);

            if (selected)
            {
                RectangleF rctf = blockReg.GetBounds(graphics);
                graphics.DrawRectangle(selPen, (int)rctf.Left, (int)rctf.Top, (int)rctf.Width, (int)rctf.Height);
            }
        }

        public static int OutputShapeFile(string filename)
        {
            try
            {
                if (MainModel.GetInstance().CurProblemBlockEventList.Count > 0)
                {
                    Shapefile shpFile = new Shapefile();
                    int idIdx = 0;
                    int fBlockId = idIdx++;
                    int strCity = idIdx++;
                    int strCounty = idIdx++;
                    int strTown = idIdx++;
                    int strGridType = idIdx++;
                    int strGridName = idIdx++;
                    int iLac = idIdx++;
                    int iCi = idIdx++;
                    int strProject = idIdx++;
                    int strFileName = idIdx++;
                    int dTime = idIdx++;
                    int strRoadType = idIdx++;
                    int strRoadName = idIdx++;                    
                    int fLongId = idIdx++;
                    int fLatId = idIdx++;
                    int StrIsRepeat = idIdx++;
                    int strEventType = idIdx++;
                    int iLast3MonthEventNum = idIdx++;
                    int iLast6MonthEventNum = idIdx++;
                    int StrIsTestNextMonth = idIdx++;
                    int iAccessTime = idIdx;

                    bool result = shpFile.CreateNew("", MapWinGIS.ShpfileType.SHP_POLYGON);
                    if (!result)
                    {
                        DevExpress.XtraEditors.XtraMessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                        return -1;
                    }

                    ShapeHelper.InsertNewField(shpFile, "BlockId", FieldType.INTEGER_FIELD, 7, 20, ref fBlockId);
                    ShapeHelper.InsertNewField(shpFile, "strCity", FieldType.STRING_FIELD, 7, 20, ref strCity);
                    ShapeHelper.InsertNewField(shpFile, "strCounty", FieldType.STRING_FIELD, 7, 20, ref strCounty);
                    ShapeHelper.InsertNewField(shpFile, "strTown", FieldType.STRING_FIELD, 7, 20, ref strTown);
                    ShapeHelper.InsertNewField(shpFile, "strGridType", FieldType.STRING_FIELD, 7, 20, ref strGridType);
                    ShapeHelper.InsertNewField(shpFile, "strGridName", FieldType.STRING_FIELD, 7, 20, ref strGridName);
                    ShapeHelper.InsertNewField(shpFile, "iLac", FieldType.INTEGER_FIELD, 7, 20, ref iLac);
                    ShapeHelper.InsertNewField(shpFile, "iCi", FieldType.INTEGER_FIELD, 7, 20, ref iCi);
                    ShapeHelper.InsertNewField(shpFile, "strProject", FieldType.STRING_FIELD, 7, 20, ref strProject);
                    ShapeHelper.InsertNewField(shpFile, "strFileName", FieldType.STRING_FIELD, 7, 20, ref strFileName);
                    ShapeHelper.InsertNewField(shpFile, "dTime", FieldType.STRING_FIELD, 7, 20, ref dTime);
                    ShapeHelper.InsertNewField(shpFile, "strRoadType", FieldType.STRING_FIELD, 7, 20, ref strRoadType);
                    ShapeHelper.InsertNewField(shpFile, "strRoadName", FieldType.STRING_FIELD, 7, 20, ref strRoadName);
                    ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 7, 20, ref fLongId);
                    ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 7, 20, ref fLatId);
                    ShapeHelper.InsertNewField(shpFile, "StrIlsRepeat", FieldType.STRING_FIELD, 7, 20, ref StrIsRepeat);
                    ShapeHelper.InsertNewField(shpFile, "strEventType", FieldType.STRING_FIELD, 7, 20, ref strEventType);
                    ShapeHelper.InsertNewField(shpFile, "iLast3MonthEventNum", FieldType.INTEGER_FIELD, 7, 20, ref iLast3MonthEventNum);
                    ShapeHelper.InsertNewField(shpFile, "iLast6MonthEventNum", FieldType.INTEGER_FIELD, 7, 20, ref iLast6MonthEventNum);
                    ShapeHelper.InsertNewField(shpFile, "StrIsTestNextMonth", FieldType.STRING_FIELD, 7, 20, ref StrIsTestNextMonth);
                    ShapeHelper.InsertNewField(shpFile, "iAccessTime", FieldType.INTEGER_FIELD, 7, 20, ref iAccessTime);
            
                    MainModel.GetInstance().MainForm.Cursor = Cursors.WaitCursor;
                    double radius = 0.00048775;//大约50米
                    int shpIdx = 0;
                    foreach (EventInfoSecond evtItem in MainModel.GetInstance().CurProblemBlockEventList)
                    {
                        if ((ProblemBlockOpLayer_GDI.isOrder
                            && (evtItem.strWorkOrder == null || evtItem.strWorkOrder == ""))
                            || (evtItem.fLongitude < 90 || evtItem.fLatitude > 45))
                        {
                            continue;
                        }
                        MapWinGIS.Shape evtShp = ShapeHelper.CreateCircleShape(evtItem.fLongitude, evtItem.fLatitude, radius);
                        
                        shpFile.EditInsertShape(evtShp, ref shpIdx);
                        shpFile.EditCellValue(fBlockId, shpIdx, evtItem.iid);
                        shpFile.EditCellValue(strCity, shpIdx, evtItem.strCity);
                        shpFile.EditCellValue(strCounty, shpIdx, evtItem.strCounty);
                        shpFile.EditCellValue(strTown, shpIdx, evtItem.strTown);
                        shpFile.EditCellValue(strGridType, shpIdx, evtItem.strGridType);
                        shpFile.EditCellValue(strGridName, shpIdx, evtItem.strGridName);
                        shpFile.EditCellValue(iLac, shpIdx, evtItem.iLac);
                        shpFile.EditCellValue(iCi, shpIdx, evtItem.iCi);
                        shpFile.EditCellValue(strProject, shpIdx, evtItem.strProject);
                        shpFile.EditCellValue(strFileName, shpIdx, evtItem.strFileName);
                        shpFile.EditCellValue(dTime, shpIdx, evtItem.dTime.ToString());
                        shpFile.EditCellValue(strRoadType, shpIdx, evtItem.strRoadType);
                        shpFile.EditCellValue(strRoadName, shpIdx, evtItem.strRoadName);
                        shpFile.EditCellValue(fLongId, shpIdx, evtItem.fLongitude);
                        shpFile.EditCellValue(fLatId, shpIdx, evtItem.fLatitude);
                        shpFile.EditCellValue(StrIsRepeat, shpIdx, evtItem.StrIsRepeat);
                        shpFile.EditCellValue(strEventType, shpIdx, evtItem.strEventType);
                        shpFile.EditCellValue(iLast3MonthEventNum, shpIdx, evtItem.iLast3MonthEventNum);
                        shpFile.EditCellValue(iLast6MonthEventNum, shpIdx, evtItem.iLast6MonthEventNum);
                        shpFile.EditCellValue(StrIsTestNextMonth, shpIdx, evtItem.StrIsTestNextMonth);
                        shpFile.EditCellValue(iAccessTime, shpIdx, evtItem.iAccessTime);
                        shpIdx++;
                    }
                    ShapeHelper.DeleteShpFile(filename);
                    shpFile.SaveAs(filename, null);
                    shpFile.Close();
                    return 1;
                }
                else
                {
                    return 0;
                }
            }
            catch
            {
                return -1;
            }
            finally
            {
                MainModel.GetInstance().MainForm.Cursor = Cursors.Default;
            }
        }
    }
}
